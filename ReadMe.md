一 新工程修改说明
1.module版本号替换
使用该模板创建的新工程的各module版本号会固定为“1.0.0-SNAPSHOT”，需要替换为变量：${revision}，包含：
a.父pom中的：<version>1.0.0-SNAPSHOT</version> 替换为 <version>${revision}</version>
b.5个子pom(xxx-api/pom.xml、  xxx-server/pom.xml、  xxx-application/pom.xml、  xxx-domain/pom.xml、 xxx-infrastructure/pom.xml)中的<version>1.0.0-SNAPSHOT</version> 替换为 <version>${revision}</version>。
2.数据库配置
application.yml中的zebra配置为自己数据库。

二 工程module说明:
1.xxx-api
thrift接口定义
2.xxx-server
对xxx-rpcapi和xxx-webapi所定义接口的实现,属于ddd中的用户接入层.
3.xxx-application
应用服务层
4.xxx-domain
领域服务层:把核心领域模型和业务逻辑，放到领域层，通过依赖倒置实现与外部的完全隔离，确保核心领域逻辑的独立演进、以及不被外部变化污染。
5.xxx-infrastructure
基础设施层