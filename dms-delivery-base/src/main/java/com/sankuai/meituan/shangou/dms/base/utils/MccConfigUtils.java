package com.sankuai.meituan.shangou.dms.base.utils;

import com.dianping.lion.client.Lion;
import com.google.common.base.Splitter;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-18
 */
@Slf4j
public class MccConfigUtils {

    private MccConfigUtils() {
    }

    /**
     * 查询青云智信的承运商code映射关系
     */
    public static DeliveryChannelEnum dapChannelMapping(String dapChannelCode) {
        if (StringUtils.isEmpty(dapChannelCode)) {
            return DeliveryChannelEnum.DAP_DELIVERY;
        }
        //格式 dapChannelCode=DeliveryChannelEnumCode|....
        String codeMappingStr = Lion.getConfigRepository().get("dap.channel.code.mapping", "");
        if (StringUtils.isEmpty(codeMappingStr)) {
            log.info("dapChannelMapping not set dapChannelCode:{}", dapChannelCode);
            return DeliveryChannelEnum.DAP_DELIVERY_UNKNOW;
        }
        try {
            List<String> mappingCodeList = Splitter.on("|").splitToList(codeMappingStr);
            for (String codeStr : mappingCodeList) {
                String[] code = codeStr.split("=");
                if (code[0].equals(dapChannelCode)) {
                    return DeliveryChannelEnum.valueOf(Integer.parseInt(code[1]));
                }
            }
        } catch (Exception e) {
            log.error("dapChannelMapping error codeMappingStr:{}，dapChannelCode:{}", codeMappingStr, dapChannelCode, e);
        }
        return DeliveryChannelEnum.DAP_DELIVERY_UNKNOW;
    }

    public static Boolean getDeliveryPushDegreeSwitch(){
        return Lion.getConfigRepository().getBooleanValue("delivery.push.degree.switch",false);
    }

}
