package com.sankuai.meituan.shangou.dms.base.model.value;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/8
 */
@Getter
@ToString
public class CoordinatePoint {
	/**
	 * 经度，标准格式
	 */
	private final String longitude;
	/**
	 * 纬度，标准格式
	 */
	private final String latitude;

	/**
	 * 时间戳
	 */
	private Long timestamp;

	@JsonCreator
	public CoordinatePoint(@JsonProperty("longitude") String longitude, @JsonProperty("latitude") String latitude) {
		Preconditions.checkArgument(StringUtils.isNotBlank(longitude), "longitude illegal");
		Preconditions.checkArgument(StringUtils.isNotBlank(latitude), "latitude illegal");

		this.longitude = longitude;
		this.latitude = latitude;
	}

	public CoordinatePoint(String longitude, String latitude, Long timestamp) {
		Preconditions.checkArgument(StringUtils.isNotBlank(longitude), "longitude illegal");
		Preconditions.checkArgument(StringUtils.isNotBlank(latitude), "latitude illegal");

		this.longitude = longitude;
		this.latitude = latitude;
		this.timestamp = timestamp;
	}
}
