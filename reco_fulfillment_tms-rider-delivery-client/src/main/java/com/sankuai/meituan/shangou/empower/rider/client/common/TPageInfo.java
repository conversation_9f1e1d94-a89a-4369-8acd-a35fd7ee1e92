package com.sankuai.meituan.shangou.empower.rider.client.common;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 分页信息.
 *
 * <AUTHOR>
 * @since 2021/6/14 11:47
 */
@TypeDoc(
        description = "分页信息.",
        authors = {
                "liyang176"
        }
)
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
public class TPageInfo {

    @FieldDoc(
            description = "页码",
            requiredness = Requiredness.REQUIRED
    )
    private Integer page = 1;

    @FieldDoc(
            description = "分页大小",
            requiredness = Requiredness.REQUIRED
    )
    private Integer pageSize = 20;

    @FieldDoc(
            description = "总条数",
            requiredness = Requiredness.REQUIRED
    )
    private Integer total = 0;

    @ThriftField(1)
    public Integer getPage() {
        return page;
    }

    @ThriftField
    public void setPage(Integer page) {
        this.page = page;
    }

    @ThriftField(2)
    public Integer getPageSize() {
        return pageSize;
    }

    @ThriftField
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @ThriftField(3)
    public Integer getTotal() {
        return total;
    }

    @ThriftField
    public void setTotal(Integer total) {
        this.total = total;
    }
}
