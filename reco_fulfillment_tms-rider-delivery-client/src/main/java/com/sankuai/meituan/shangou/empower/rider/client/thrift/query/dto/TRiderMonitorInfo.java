package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;

@Data
@ThriftStruct
public class TRiderMonitorInfo {
    @FieldDoc(
            description = "骑手账号信息"
    )
    @ThriftField(1)
    public TStaffRider staffRider;

    @FieldDoc(
            description = "骑手位置经纬度"
    )
    @ThriftField(2)
    public TLocationCoordinate location;

    @FieldDoc(
            description = "关联运单信息"
    )
    @ThriftField(3)
    public List<TRiderDeliveryOrderMonitorInfo> orderInfoList;

    @FieldDoc(
            description = "定位异常类型"
    )
    @ThriftField(4)
    public Integer locatingExceptionType;
}