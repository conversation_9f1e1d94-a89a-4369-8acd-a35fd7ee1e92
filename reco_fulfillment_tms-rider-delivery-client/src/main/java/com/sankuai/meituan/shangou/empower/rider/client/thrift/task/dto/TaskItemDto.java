package com.sankuai.meituan.shangou.empower.rider.client.thrift.task.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

/**
 * <AUTHOR>
 * @since 2021/09/29
 */
@TypeDoc(description = "任务列表项")
@ThriftStruct
public class TaskItemDto {
    @FieldDoc(description = "任务id")
    private Long taskId;

    @FieldDoc(description = "任务名称")
    private String taskName;

    @FieldDoc(description = "任务结果")
    private String taskResult;

    @FieldDoc(description = "任务类型")
    private Integer taskType;

    @FieldDoc(description = "任务总数量")
    private Integer totalCount;

    @FieldDoc(description = "任务成功数量")
    private Integer successCount;

    @FieldDoc(description = "任务失败数量")
    private Integer failedCount;

    @FieldDoc(description = "操作时间")
    private Long operateTime;

    @FieldDoc(description = "操作人名称")
    private String operatorName;

    @FieldDoc(description = "操作人id")
    private Long operatorId;

    @FieldDoc(description = "任务状态。参考枚举：TaskStatusEnum")
    private Integer taskStatus;

    @FieldDoc(description = "执行开始时间")
    private Long beginTime;

    @FieldDoc(description = "执行结束时间")
    private Long endTime;

    @FieldDoc(description = "任务创建时间")
    private Long createTime;

    // Getter && Setter

    @ThriftField(1)
    public Long getTaskId() {
        return taskId;
    }

    @ThriftField
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    @ThriftField(2)
    public String getTaskName() {
        return taskName;
    }

    @ThriftField
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    @ThriftField(3)
    public String getTaskResult() {
        return taskResult;
    }

    @ThriftField
    public void setTaskResult(String taskResult) {
        this.taskResult = taskResult;
    }

    @ThriftField(4)
    public Integer getTaskType() {
        return taskType;
    }

    @ThriftField
    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    @ThriftField(5)
    public Integer getTotalCount() {
        return totalCount;
    }

    @ThriftField
    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    @ThriftField(6)
    public Integer getSuccessCount() {
        return successCount;
    }

    @ThriftField
    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    @ThriftField(7)
    public Integer getFailedCount() {
        return failedCount;
    }

    @ThriftField
    public void setFailedCount(Integer failedCount) {
        this.failedCount = failedCount;
    }

    @ThriftField(8)
    public Long getOperateTime() {
        return operateTime;
    }

    @ThriftField
    public void setOperateTime(Long operateTime) {
        this.operateTime = operateTime;
    }

    @ThriftField(9)
    public String getOperatorName() {
        return operatorName;
    }

    @ThriftField
    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    @ThriftField(10)
    public Long getOperatorId() {
        return operatorId;
    }

    @ThriftField
    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    @ThriftField(11)
    public Integer getTaskStatus() {
        return taskStatus;
    }

    @ThriftField
    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    @ThriftField(12)
    public Long getBeginTime() {
        return beginTime;
    }

    @ThriftField
    public void setBeginTime(Long beginTime) {
        this.beginTime = beginTime;
    }

    @ThriftField(13)
    public Long getEndTime() {
        return endTime;
    }

    @ThriftField
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    @ThriftField(14)
    public Long getCreateTime() {
        return createTime;
    }

    @ThriftField
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }
}