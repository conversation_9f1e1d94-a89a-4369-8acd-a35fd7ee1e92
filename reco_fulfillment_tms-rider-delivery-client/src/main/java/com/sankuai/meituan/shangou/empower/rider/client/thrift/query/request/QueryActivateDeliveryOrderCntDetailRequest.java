package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/6 15:58
 **/

@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class QueryActivateDeliveryOrderCntDetailRequest {
    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "赋能门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long storeId;


    @FieldDoc(
            description = "骑手账号 ID",
            requiredness = Requiredness.OPTIONAL
    )
    private Long riderAccountId;


    @ThriftField(1)
    public Long getTenantId() {
        return this.tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(2)
    public Long getStoreId() {
        return this.storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(3)
    public Long getRiderAccountId() {
        return this.riderAccountId;
    }

    @ThriftField
    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    public String validate() {
        if (tenantId == null || tenantId <= 0L) {
            return "租户ID不合法";
        }
        if (storeId == null || storeId <= 0L) {
            return "赋能门店ID不合法";
        }
        if (riderAccountId == null || riderAccountId <= 0L) {
            return "骑手账号不合法";
        }

        return null;
    }
}
