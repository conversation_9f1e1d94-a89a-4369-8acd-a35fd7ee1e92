package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/28 11:44
 **/
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
public class BatchQueryDeliveryOrderResponse {
    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    private Status status = Status.SUCCESS;

    @FieldDoc(
            description = "运单信息列表",
            requiredness = Requiredness.REQUIRED
    )
    private List<TRiderDeliveryOrder> TRiderDeliveryOrders;

    @ThriftField(1)
    public Status getStatus() {
        return this.status;
    }

    @ThriftField
    public void setStatus(Status status) {
        this.status = status;
    }

    @ThriftField(2)
    public List<TRiderDeliveryOrder> getTRiderDeliveryOrders() {
        return this.TRiderDeliveryOrders;
    }

    @ThriftField
    public void setTRiderDeliveryOrders(List<TRiderDeliveryOrder> TRiderDeliveryOrders) {
        this.TRiderDeliveryOrders = TRiderDeliveryOrders;
    }

}
