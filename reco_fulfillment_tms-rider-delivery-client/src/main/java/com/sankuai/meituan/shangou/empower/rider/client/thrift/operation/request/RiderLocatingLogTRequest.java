package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

@ThriftStruct
@FieldDoc(
        description = "骑手定位日志"
)
@Data
public class RiderLocatingLogTRequest {
    @FieldDoc(
            description = "骑手账号id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long riderAccountId;

    @FieldDoc(
            description = "日志类型 0-正常日志 1-异常日志",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Integer logType;

    @FieldDoc(
            description = "异常类型",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public Integer exceptionType;

    @FieldDoc(
            description = "纬度",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    public String latitude;

    @FieldDoc(
            description = "经度",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    public String longitude;

    @FieldDoc(
            description = "手机系统",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(6)
    public String phoneOS;

    @FieldDoc(
            description = "如果是正常日志，是否用到该位置",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    public Boolean locationIsUsed;

    @FieldDoc(
            description = "设备id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(8)
    public String uuid;

    @FieldDoc(
            description = "手机厂商",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(9)
    public String manufacturer;


    @FieldDoc(
            description = "前端采集到此条日志的时间",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(10)
    public Long utime;

    @FieldDoc(
            description = "定位精度",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(11)
    public String accuracy;

    @FieldDoc(
            description = "角度",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(12)
    public String bearing;

    @FieldDoc(
            description = "速度",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(13)
    public String speed;


    public String validate() {

        if (riderAccountId == null || riderAccountId <= 0) {
            return "骑手账号id不合法";
        }

        if (logType == null) {
            return "日志类型不合法";
        }

        if (utime == null || utime <= 0) {
            return "时间戳不合法";
        }

        return null;
    }
}
