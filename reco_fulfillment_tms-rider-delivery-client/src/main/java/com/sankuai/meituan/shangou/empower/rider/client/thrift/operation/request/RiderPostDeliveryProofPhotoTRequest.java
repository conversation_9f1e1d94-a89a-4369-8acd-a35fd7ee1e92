package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/26 16:55
 **/
@TypeDoc(
        description = "骑手上传送达照片请求"
)
@ThriftStruct
public class RiderPostDeliveryProofPhotoTRequest {
    @FieldDoc(description = "运单id")
    private Long deliveryOrderId;

    @FieldDoc(description = "操作人账号id")
    private Long operatorAccountId;

    @FieldDoc(description = "送达照片url列表")
    private List<String> deliveryProofPhotoUrls;

    @FieldDoc(description = "骑手ip")
    private String riderIP;

    @FieldDoc(description = "租户id")
    private Long tenantId;

    @FieldDoc(description = "门店id")
    private Long storeId;

    @ThriftField(1)
    public Long getDeliveryOrderId() {
        return this.deliveryOrderId;
    }

    @ThriftField
    public void setDeliveryOrderId(Long deliveryOrderId) {
        this.deliveryOrderId = deliveryOrderId;
    }

    @ThriftField(2)
    public Long getOperatorAccountId() {
        return this.operatorAccountId;
    }

    @ThriftField
    public void setOperatorAccountId(Long operatorAccountId) {
        this.operatorAccountId = operatorAccountId;
    }

    @ThriftField(3)
    public List<String> getDeliveryProofPhotoUrls() {
        return this.deliveryProofPhotoUrls;
    }

    @ThriftField
    public void setDeliveryProofPhotoUrls(List<String> deliveryProofPhotoUrls) {
        this.deliveryProofPhotoUrls = deliveryProofPhotoUrls;
    }


    @ThriftField(4)
    public String getRiderIP() {
        return this.riderIP;
    }

    @ThriftField
    public void setRiderIP(String riderIP) {
        this.riderIP = riderIP;
    }


    @ThriftField(5)
    public Long getTenantId() {
        return tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(6)
    public Long getStoreId() {
        return storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String validate() {
        if (deliveryOrderId == null || deliveryOrderId <= 0L) {
            return "运单id不合法";
        }

        if (operatorAccountId == null || operatorAccountId <= 0L) {
            return "操作人id不合法";
        }

        if (CollectionUtils.isEmpty(deliveryProofPhotoUrls)) {
            return "送达照片链接不能为空";
        }

        if (StringUtils.isBlank(riderIP)) {
            return "骑手ip地址不能为空";
        }
        return null;
    }
}
