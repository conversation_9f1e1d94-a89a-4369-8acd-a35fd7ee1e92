package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ThriftStruct
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TLocationCoordinate {
    @FieldDoc(
            description = "定位经度"
    )
    @ThriftField(1)
    public String longitude;

    @FieldDoc(
            description = "定位纬度"
    )
    @ThriftField(2)
    public String latitude;
}
