package com.sankuai.meituan.shangou.empower.rider.client.thrift.task.resp;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.dto.TaskDetailDto;

/**
 * <AUTHOR>
 * @since 2021/09/29
 */
@TypeDoc(description = "任务信息查询响应")
@ThriftStruct
public class TaskDetailResp {

    @FieldDoc(description = "响应状态")
    private Status status;

    @FieldDoc(description = "任务信息")
    private TaskDetailDto item;

    public TaskDetailResp() {
    }

    @ThriftConstructor
    public TaskDetailResp(Status status, TaskDetailDto item) {
        this.status = status;
        this.item = item;
    }

    @ThriftField(1)
    public Status getStatus() {
        return status;
    }

    @ThriftField
    public void setStatus(Status status) {
        this.status = status;
    }

    @ThriftField(2)
    public TaskDetailDto getItem() {
        return item;
    }

    @ThriftField
    public void setItem(TaskDetailDto item) {
        this.item = item;
    }
}
