package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "骑手改派响应体"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class RiderChangeResponse {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status;

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Boolean isSameRider = false;

    @FieldDoc(
            description = "变更前骑手id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Long beforeRiderAccountId;

    @FieldDoc(
            description = "变更前骑手名称",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public String beforeRiderName;

    public RiderChangeResponse(Status status) {
        this.status = status;
    }
}
