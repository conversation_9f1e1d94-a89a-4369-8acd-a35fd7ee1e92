package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.zookeeper.data.Stat;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/6
 */

@TypeDoc(
        description = "门店配送监控信息响应"
)
@Data
@ThriftStruct
@NoArgsConstructor
public class StoreDeliveryMonitorResponse {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status = Status.SUCCESS;

    @FieldDoc(
            description = "骑手监控信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<TRiderMonitorInfo> riderMonitorInfoList;

    @FieldDoc(
            description = "运单监控信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public List<TRiderDeliveryOrderMonitorInfo> deliveryOrderMonitorInfoList;


    public StoreDeliveryMonitorResponse(Status status) {
        this.status = status;
    }

}
