package com.sankuai.meituan.shangou.empower.rider.client.thrift.task.req;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

/**
 * 异步任务详情查询请求参数
 *
 * @author: linjianyu
 * @since: 2019-07-30 Time: 11:34
 */
@TypeDoc(
        description = "异步任务详情查询请求",
        authors = {
                "linjianyu"
        }
)
@ThriftStruct
public class TaskDetailReq {
    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "操作员id",
            requiredness = Requiredness.REQUIRED
    )
    private Long operatorId;

    @FieldDoc(
            description = "任务id",
            requiredness = Requiredness.REQUIRED
    )
    private Long taskId;

    public TaskDetailReq() {
    }

    @ThriftConstructor
    public TaskDetailReq(Long tenantId, Long operatorId, Long taskId) {
        this.tenantId = tenantId;
        this.operatorId = operatorId;
        this.taskId = taskId;
    }

    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long getTenantId() {
        return tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(value = 2, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long getOperatorId() {
        return operatorId;
    }

    @ThriftField
    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    @ThriftField(value = 3, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long getTaskId() {
        return taskId;
    }

    @ThriftField
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }


    // builder
    // ---------------------------------------------------------------------------------

    public static Builder builder(){
        return new Builder();
    }

    public static final class Builder {
        private Long tenantId;
        private Long operatorId;
        private Long taskId;

        private Builder() {
        }

        public static Builder aTaskDetailReq() {
            return new Builder();
        }

        public Builder tenantId(Long tenantId) {
            this.tenantId = tenantId;
            return this;
        }

        public Builder operatorId(Long operatorId) {
            this.operatorId = operatorId;
            return this;
        }

        public Builder taskId(Long taskId) {
            this.taskId = taskId;
            return this;
        }

        public TaskDetailReq build() {
            TaskDetailReq taskDetailReq = new TaskDetailReq();
            taskDetailReq.setTenantId(tenantId);
            taskDetailReq.setOperatorId(operatorId);
            taskDetailReq.setTaskId(taskId);
            return taskDetailReq;
        }
    }
}
