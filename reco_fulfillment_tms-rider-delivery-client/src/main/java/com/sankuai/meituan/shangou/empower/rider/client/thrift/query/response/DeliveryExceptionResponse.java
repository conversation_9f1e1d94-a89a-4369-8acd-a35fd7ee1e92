package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ThriftStruct
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryExceptionResponse {
    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status = Status.SUCCESS;

    @FieldDoc(
            description = "骑手配送异常列表",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<TRiderDeliveryException> tRiderDeliveryExceptionList;

    public DeliveryExceptionResponse(Status status) {
        this.status = status;
    }

    public DeliveryExceptionResponse(List<TRiderDeliveryException> tRiderDeliveryExceptionList) {
        this.tRiderDeliveryExceptionList = tRiderDeliveryExceptionList;
    }
}
