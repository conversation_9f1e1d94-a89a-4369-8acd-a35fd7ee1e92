package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryProofPhotoInfo;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/4 20:21
 **/
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
public class QueryDeliveryProofPhotoResponse {
    private Status status = Status.SUCCESS;

    private List<TDeliveryProofPhotoInfo> tDeliveryProofPhotoInfoList;

    /**
     * 1-当面签收 2-放在指定地点并拍照 3-未选择，非自配骑手点已送达
     */
    private Integer signType;

    /**
     * 送达时是否是弱网环境
     */
    private Boolean isWeakNetwork;

    /**
     * 是否拣配分离
     */
    private Boolean isPickDeliverySplit = false;

    @ThriftField(1)
    public Status getStatus() {
        return this.status;
    }

    @ThriftField
    public void setStatus(Status status) {
        this.status = status;
    }

    @ThriftField(2)
    public List<TDeliveryProofPhotoInfo> getTDeliveryProofPhotoInfoList() {
        return this.tDeliveryProofPhotoInfoList;
    }

    @ThriftField
    public void setTDeliveryProofPhotoInfoList(List<TDeliveryProofPhotoInfo> tDeliveryProofPhotoInfoList) {
        this.tDeliveryProofPhotoInfoList = tDeliveryProofPhotoInfoList;
    }


    @ThriftField(3)
    public Integer getSignType() {
        return this.signType;
    }

    @ThriftField
    public void setSignType(Integer signType) {
        this.signType = signType;
    }

    @ThriftField(4)
    public Boolean getIsWeakNetwork() {
        return this.isWeakNetwork;
    }

    @ThriftField
    public void setIsWeakNetwork(Boolean isWeakNetwork) {
        this.isWeakNetwork = isWeakNetwork;
    }


    @ThriftField(5)
    public Boolean getIsPickDeliverySplit() {
        return this.isPickDeliverySplit;
    }

    @ThriftField
    public void setIsPickDeliverySplit(Boolean isPickDeliverySplit) {
        this.isPickDeliverySplit = isPickDeliverySplit;
    }

    public QueryDeliveryProofPhotoResponse(List<TDeliveryProofPhotoInfo> tDeliveryProofPhotoInfoList, Integer signType, Boolean isWeakNetwork) {
        this.setTDeliveryProofPhotoInfoList(tDeliveryProofPhotoInfoList);
        this.signType = signType;
        this.isWeakNetwork = isWeakNetwork;
    }

    @Override
    public String toString() {
        return "QueryDeliveryProofPhotoResponse{" +
                "status=" + status +
                ", tDeliveryProofPhotoInfoList=" + tDeliveryProofPhotoInfoList +
                ", signType=" + signType +
                ", isWeakNetwork=" + isWeakNetwork +
                ", isPickDeliverySplit=" + isPickDeliverySplit +
                '}';
    }
}
