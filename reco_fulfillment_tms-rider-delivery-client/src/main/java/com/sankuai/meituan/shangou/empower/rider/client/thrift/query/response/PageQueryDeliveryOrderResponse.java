package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import java.util.List;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.common.TPageInfo;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 针对自营配送骑手，查询运单的返回结果.
 *
 * <AUTHOR>
 * @since 2021/6/14 11:34
 */
@TypeDoc(
        description = "针对自营配送骑手，查询运单的返回结果.",
        authors = {
                "liyang176"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@ToString
public class PageQueryDeliveryOrderResponse {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    private Status status = Status.SUCCESS;

    @FieldDoc(
            description = "查询结果的分页信息",
            requiredness = Requiredness.REQUIRED
    )
    private TPageInfo pageInfo;

    @FieldDoc(
            description = "运单信息列表",
            requiredness = Requiredness.REQUIRED
    )
    private List<TRiderDeliveryOrder> TRiderDeliveryOrders;

    public PageQueryDeliveryOrderResponse(Status status) {
        this.status = status;
    }

    @ThriftField(1)
    public Status getStatus() {
        return status;
    }

    @ThriftField
    public void setStatus(Status status) {
        this.status = status;
    }

    @ThriftField(2)
    public TPageInfo getPageInfo() {
        return pageInfo;
    }

    @ThriftField
    public void setPageInfo(TPageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    @ThriftField(3)
    public List<TRiderDeliveryOrder> getTRiderDeliveryOrders() {
        return TRiderDeliveryOrders;
    }

    @ThriftField
    public void setTRiderDeliveryOrders(List<TRiderDeliveryOrder> TRiderDeliveryOrders) {
        this.TRiderDeliveryOrders = TRiderDeliveryOrders;
    }


}
