package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

@TypeDoc(
        description = "骑手维度统计数据",
        authors = {
                "yujing10"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@ToString
public class TDeliveryRiderStatistics {

    @FieldDoc(description = "骑手id")
    public Long riderAccountId;

    @FieldDoc(description = "配送中（骑手已取货）订单量")
    public Integer delivering;

    @FieldDoc(description = "配送中（骑手已取货）考核超时订单量")
    public Integer deliveringJudgeTimeout;

    @FieldDoc(description = "拣货中（骑手已接单）订单量")
    public Integer picking;

    @FieldDoc(description = "拣货中（骑手已接单）考核超时订单量")
    public Integer pickingJudgeTimeout;

    @FieldDoc(description = "拣货中（骑手已接单）超时订单量 V2版本")
    private Integer pickingJudgeTimeoutV2;

    @FieldDoc(description = "配送中（骑手已取货）超时订单量 V2版本")
    private Integer deliveringJudgeTimeoutV2;

    @ThriftField(1)
    public Long getRiderAccountId() {
        return riderAccountId;
    }

    @ThriftField
    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    @ThriftField(2)
    public Integer getDelivering() {
        return delivering;
    }

    @ThriftField
    public void setDelivering(Integer delivering) {
        this.delivering = delivering;
    }

    @ThriftField(3)
    public Integer getDeliveringJudgeTimeout() {
        return deliveringJudgeTimeout;
    }

    @ThriftField
    public void setDeliveringJudgeTimeout(Integer deliveringJudgeTimeout) {
        this.deliveringJudgeTimeout = deliveringJudgeTimeout;
    }


    @ThriftField(4)
    public Integer getPicking() {
        return this.picking;
    }

    @ThriftField
    public void setPicking(Integer picking) {
        this.picking = picking;
    }

    @ThriftField(5)
    public Integer getPickingJudgeTimeout() {
        return this.pickingJudgeTimeout;
    }

    @ThriftField
    public void setPickingJudgeTimeout(Integer pickingJudgeTimeout) {
        this.pickingJudgeTimeout = pickingJudgeTimeout;
    }

    @ThriftField(6)
    public Integer getPickingJudgeTimeoutV2() {
        return this.pickingJudgeTimeoutV2;
    }

    @ThriftField
    public void setPickingJudgeTimeoutV2(Integer pickingJudgeTimeoutV2) {
        this.pickingJudgeTimeoutV2 = pickingJudgeTimeoutV2;
    }

    @ThriftField(7)
    public Integer getDeliveringJudgeTimeoutV2() {
        return this.deliveringJudgeTimeoutV2;
    }

    @ThriftField
    public void setDeliveringJudgeTimeoutV2(Integer deliveringJudgeTimeoutV2) {
        this.deliveringJudgeTimeoutV2 = deliveringJudgeTimeoutV2;
    }

}
