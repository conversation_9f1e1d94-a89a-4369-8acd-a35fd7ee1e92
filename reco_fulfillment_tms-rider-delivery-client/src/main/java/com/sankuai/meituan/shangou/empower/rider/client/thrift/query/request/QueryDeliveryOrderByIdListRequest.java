package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/6/6 19:31
 **/
@ThriftStruct
public class QueryDeliveryOrderByIdListRequest {
    private Long tenantId;

    private List<Long> deliveryOrderIds;

    public boolean needReturnPricingRouteInfo = false;

    private Long storeId;


    public String validate() {
        if (Objects.isNull(tenantId)) {
            return "租户id不存在";
        }

        if(Objects.isNull(deliveryOrderIds)) {
            return "运单id不合法";
        }

        return null;
    }
    @ThriftField(1)
    public Long getTenantId() {
        return this.tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(2)
    public List<Long> getDeliveryOrderIds() {
        return this.deliveryOrderIds;
    }

    @ThriftField
    public void setDeliveryOrderIds(List<Long> deliveryOrderIds) {
        this.deliveryOrderIds = deliveryOrderIds;
    }


    @ThriftField(3)
    public boolean isNeedReturnPricingRouteInfo() {
        return this.needReturnPricingRouteInfo;
    }

    @ThriftField
    public void setNeedReturnPricingRouteInfo(boolean needReturnPricingRouteInfo) {
        this.needReturnPricingRouteInfo = needReturnPricingRouteInfo;
    }

    @ThriftField(4)
    public Long getStoreId() {
        return storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }
}
