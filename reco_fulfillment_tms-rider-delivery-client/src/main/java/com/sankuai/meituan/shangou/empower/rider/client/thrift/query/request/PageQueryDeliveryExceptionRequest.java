package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@ThriftStruct
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageQueryDeliveryExceptionRequest {

    @FieldDoc(
            description = "页码"
    )
    @ThriftField(1)
    public Integer pageNum;

    @FieldDoc(
            description = "分页大小"
    )
    @ThriftField(2)
    public Integer pageSize;

    @FieldDoc(
            description = "租户id"
    )
    @ThriftField(3)
    public Long tenantId;

    @FieldDoc(
            description = "门店id列表"
    )
    @ThriftField(4)
    public List<Long> storeIds;

    @FieldDoc(
            description = "骑手账号id"
    )
    @ThriftField(5)
    public Long riderAccountId;

    @FieldDoc(
            description = "渠道id列表",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(6)
    private List<Integer> orderBizTypeList;

    @FieldDoc(
            description = "渠道id列表",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    private Long channelOrderId;

    @FieldDoc(
            description = "异常类型",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(8)
    private List<Integer> exceptionTypeList;

    @FieldDoc(
            description = "订单支付时间-开始时间",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(9)
    private Long payTimeStartTimeStamp;

    @FieldDoc(
            description = "订单支付时间-结束日期",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(10)
    private Long payTimeEndTimeStamp;

    @FieldDoc(
            description = "上报时间-开始时间",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(11)
    private Long reportTimeStartTimeStamp;

    @FieldDoc(
            description = "上报时间-结束日期",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(12)
    private Long reportTimeEndTimeStamp;


    public String validate() {
        if (pageNum == null || pageNum <= 0) {
            return "pageNum不合法";
        }

        if (pageSize == null || pageSize <= 0) {
            return "pageSize不合法";
        }

        if (tenantId == null || tenantId <= 0L) {
            return "租户id不合法";
        }

        if (CollectionUtils.isEmpty(storeIds)) {
            return "门店id列表为空";
        }

        //为了避免查询范围过大，支付时间范围和上报时间范围必须要有一个有效
        if ((reportTimeEndTimeStamp == null || reportTimeStartTimeStamp == null)
                && (payTimeStartTimeStamp == null || payTimeEndTimeStamp == null)) {
            return "时间筛选范围不合法";
        }

        if (payTimeStartTimeStamp != null && payTimeEndTimeStamp != null
        && ((payTimeEndTimeStamp - payTimeStartTimeStamp) / 1000 / 60 / 60 / 24 / 31 > 6)) {
            return "支付时间筛选范围不能超过六个月";
        }

        if (reportTimeStartTimeStamp != null && reportTimeEndTimeStamp != null
                && ((reportTimeStartTimeStamp - reportTimeEndTimeStamp) / 1000 / 60 / 60 / 24 / 31 > 6)) {
            return "异常上报时间筛选范围不能超过六个月";
        }


        return null;
    }

}
