package com.sankuai.meituan.shangou.empower.rider.client.thrift.task;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.req.TaskDetailReq;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.req.TaskListReq;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.req.TaskSubmitReq;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.resp.TaskDetailResp;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.resp.TaskListResp;
import org.apache.thrift.TException;

/**
 *
 */
@InterfaceDoc(
        displayName = "配送服务通用异步任务操作服务",
        type = "octo.thrift.annotation",
        scenarios = "该服务用于执行异步任务创建/查询等接口（支持部分成功失败）",
        description = "该服务用于执行异步任务创建/查询等接口（支持部分成功失败）"
)
@ThriftService
public interface RiderDeliveryTaskThriftService {

    @MethodDoc(
            displayName = "01-异步任务提交",
            description = "异步任务提交操作（任务参数通过json支持泛化通用对象）",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            description = "任务提交参数",
                            type = TaskSubmitReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            exceptions = {
                    @ExceptionDoc(
                            type = TException.class,
                            description = "TException"
                    )
            },
            returnValueDescription = "任务提交操作结果"
    )
    @ThriftMethod
    Status submit(TaskSubmitReq req) throws TException;

    @MethodDoc(
            displayName = "02-任务列表查询",
            description = "按任务类型查询异步任务（支持分页查询）",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            description = "任务列表查询请求",
                            type = TaskSubmitReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            exceptions = {
                    @ExceptionDoc(
                            type = TException.class,
                            description = "TException"
                    )
            },
            returnValueDescription = "任务列表查询操作结果"
    )
    @ThriftMethod
    TaskListResp queryListPageable(TaskListReq req) throws TException;

    @MethodDoc(
            displayName = "03-任务详情查询",
            description = "按任务id查询异步任务详情",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            description = "任务详情查询请求",
                            type = TaskDetailReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            exceptions = {
                    @ExceptionDoc(
                            type = TException.class,
                            description = "TException"
                    )
            },
            returnValueDescription = "任务详情查询操作结果"
    )
    @ThriftMethod
    TaskDetailResp queryDetail(TaskDetailReq req) throws TException;
}
