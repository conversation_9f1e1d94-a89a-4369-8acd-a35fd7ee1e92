package com.sankuai.meituan.shangou.empower.rider.client.thrift.query;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.*;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.*;

import java.util.List;

/**
 * 自营骑手查询运单信息.
 *
 * <AUTHOR>
 * @since 2021/6/14 18:25
 */
@InterfaceDoc(
        displayName = "自营骑手查询运单信息",
        type = "octo.thrift.annotation",
        scenarios = "自营骑手查询运单信息",
        description = "自营骑手查询运单信息",
        authors = {
                "liyang176"
        }
)
@ThriftService
public interface RiderQueryThriftService {

        @MethodDoc(
                displayName = "针对自营配送骑手，按运单条件分页查询运单信息",
                description = "针对自营配送骑手，按运单条件分页查询运单信息",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "针对自营配送骑手，按运单条件分页查询运单信息的请求",
                                type = PageQueryDeliveryOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "骑手的运单结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        PageQueryDeliveryOrderResponse pageQueryDeliveryOrder(PageQueryDeliveryOrderRequest request);

        @MethodDoc(
                displayName = "针对自营配送骑手，按运单状态查询运单数量",
                description = "针对自营配送骑手，按运单状态查询运单数量",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "针对自营配送骑手，按运单状态查询运单数量",
                                type = QueryDeliveryQuantityRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "骑手的运单运单数量结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryDeliveryQuantityResponse queryDeliveryQuantity(QueryDeliveryQuantityRequest request);

        @MethodDoc(
                displayName = "针对自营配送骑手，分页查询已送达的运单",
                description = "针对自营配送骑手，分页查询已送达的运单",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "针对自营配送骑手，分页查询昨天到现在的已送达的运单, 结果按预计送达时间倒序",
                                type = PageQueryCompletedDeliveryOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "骑手已送达+已取消的运单",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        PageQueryDeliveryOrderResponse pageQueryCompletedDeliveryOrder(PageQueryCompletedDeliveryOrderRequest request);

        @MethodDoc(
                displayName = "针对自营配送骑手，分页查询已领取+已取货的运单",
                description = "针对自营配送骑手，分页查询已领取+已取货的运单",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "针对自营配送骑手，分页查询已领取+已取货的运单",
                                type = PageQueryInProgressDeliveryOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "骑手已领取+已取货的运单",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        PageQueryDeliveryOrderResponse pageQueryInProgressDeliveryOrder(PageQueryInProgressDeliveryOrderRequest request);

        @MethodDoc(
                displayName = "针对自营配送骑手，分页查询已领取+已取货的运单",
                description = "针对自营配送骑手，分页查询已领取+已取货的运单",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "针对自营配送骑手，分页查询已领取+已取货的运单",
                                type = PageQueryInProgressDeliveryOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "骑手已领取+已取货的运单",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        PageQueryDeliveryOrderResponse queryAllRiderDeliveryOrder(QueryAllDeliveryOrderRequest request);


        @MethodDoc(
                displayName = "针对自营配送骑手，查询门店配送监控信息",
                description = "针对自营配送骑手，查询门店配送监控信息",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "针对自营配送骑手，查询门店配送监控信息",
                                type = PageQueryInProgressDeliveryOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "骑手位置信息和状态信息 + 运单状态信息",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        StoreDeliveryMonitorResponse storeDeliveryMonitor(StoreDeliveryMonitorRequest request);

        @MethodDoc(
                displayName = "查询门店下骑手名单（具有门店权限的店员/店长/兼职骑手）",
                description = "查询门店下骑手名单（具有门店权限的店员/店长/兼职骑手）",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询门店下骑手名单（具有门店权限的店员/店长/兼职骑手）",
                                type = StoreRiderAccountListRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "查询门店下骑手名单（具有门店权限的店员/店长/兼职骑手）",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        StoreRiderAccountListResponse storeRiderAccountList(StoreRiderAccountListRequest request);

        @MethodDoc(
                displayName = "根据订单id查询骑手到刻位置 and 距离信息",
                description = "根据订单id查询骑手到刻位置 and 距离信息",
                parameters = {
                        @ParamDoc(
                                name = "tenantId",
                                description = "租户id",
                                type = Long.class,
                                requiredness = Requiredness.REQUIRED
                        ),
                        @ParamDoc(
                                name = "storeId",
                                description = "门店id",
                                type = Long.class,
                                requiredness = Requiredness.REQUIRED
                        ),
                        @ParamDoc(
                                name = "orderId",
                                description = "赋能系统订单id",
                                type = Long.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "骑手到刻位置/距离信息",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryRiderArrivalLocationResponse queryRiderArrivalLocationInfo(Long tenantId, Long storeId, Long orderId);

        @MethodDoc(
                displayName = "分页查询配送异常列表",
                description = "分页查询配送异常列表",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "分页查询配送异常列表",
                                type = PageQueryDeliveryExceptionRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "分页查询配送异常列表",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        PageQueryDeliveryExceptionListResponse pageQueryDeliveryExceptionList(PageQueryDeliveryExceptionRequest request);

        @MethodDoc(
                displayName = "根据渠道订单列表查配送异常",
                description = "根据渠道订单列表查配送异常",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "根据渠道订单列表查配送异常",
                                type = QueryDeliveryExceptionByChannelOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "根据渠道订单列表查配送异常",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        DeliveryExceptionResponse queryDeliveryExceptionByChannelOrder(QueryDeliveryExceptionByChannelOrderRequest request);


        @MethodDoc(
                displayName = "查询生效中的运单数量明细",
                description = "查询生效中的运单数量",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询生效中的运单数量明细",
                                type = QueryDeliveryExceptionByChannelOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "查询生效中的运单数量明细",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryActivateDeliveryOrderCntDetailResponse queryActivateDeliveryOrderCntDetail(QueryActivateDeliveryOrderCntDetailRequest request);

        @MethodDoc(
                displayName = "根据运单状态查询超时的运单数量",
                description = "根据运单状态查询超时的运单数量",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "根据运单状态查询超时的运单数量的请求",
                                type = QueryTimeoutDeliveryOrderNumRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "超时的运单数量",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryTimeoutDeliveryOrderNumResponse queryTimeoutDeliveryOrderNum(QueryTimeoutDeliveryOrderNumRequest request);

        @MethodDoc(
                displayName = "查看送达照片",
                description = "查看送达照片",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查看送达照片的请求",
                                type = QueryTimeoutDeliveryOrderNumRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "查看送达照片信息",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryDeliveryProofPhotoResponse queryDeliveryProofPhotosByOrderId(Long orderId);

        @MethodDoc(
                displayName = "批量查询门店列表+状态列表的运单(及*当前*骑手)",
                description = "批量查询门店列表+状态列表的运单(及*当前*骑手)",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "针对自营配送骑手，分页查询已领取+已取货的运单",
                                type = PageQueryInProgressDeliveryOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "骑手已领取+已取货的运单",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryDeliveryOrderWithCurrentRiderResponse queryDeliveryOrderByPoiAndStatusList(QueryDeliveryOrderByPoiAndStatusListRequest request);

        @MethodDoc(
                displayName = "根据中台订单列表批量查询运单",
                description = "根据中台订单列表批量查询运单",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "根据渠道订单列表批量查询运单",
                                type = QueryDeliveryOrderByOrderIdListRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "订单对应的当前生效的运单",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        BatchQueryDeliveryOrderResponse queryDeliveryOrderByOrderIdList(QueryDeliveryOrderByOrderIdListRequest request);


        @MethodDoc(
                displayName = "根据中台订单列表批量查询运单(每次最多查询100个订单)",
                description = "根据中台列表批量查询运单",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "根据渠道订单列表批量查询运单",
                                type = QueryDeliveryOrderByOrderIdListRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "订单对应的当前生效的运单",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        BatchQueryDeliveryOrderResponse queryDeliveryOrderByOrderIdListV2(List<Long> orderIds);

        @MethodDoc(
                displayName = "根据中台订单列表批量查询运单(每次最多查询100个订单)",
                description = "根据中台列表批量查询运单",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "根据渠道订单列表批量查询运单",
                                type = QueryDeliveryOrderByOrderIdListRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "订单对应的当前生效的运单",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        BatchQueryDeliveryOrderResponse queryAllDeliveryOrderByOrderIdList(List<Long> orderIds);


        @MethodDoc(
                displayName = "针对自营配送骑手，分页查询已送达运单",
                description = "针对自营配送骑手，分页查询已送达的运单",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "针对自营配送骑手，分页查询已送达的运单, 结果按实际送达时间倒序",
                                type = PageQueryCompletedDeliveryOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "骑手已送达运单",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        PageQueryDeliveryOrderResponse pageQueryCompletedDeliveryOrderByDuration(PageQueryCompletedDeliveryOrderByDurationRequest request);


        @MethodDoc(
                displayName = "根据订单id列表查询风控单",
                description = "根据订单id列表查询风控单",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "根据订单id列表查询风控单",
                                type = QueryDeliveryRiskControlOrderResponse.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "根据订单id列表查询风控单",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryDeliveryRiskControlOrderResponse queryRiskControlOrderByViewOrderIdList(QueryDeliveryRiskControlOrderRequest request);


        @MethodDoc(
                displayName = "查询已送达运单统计",
                description = "查询已送达运单统计",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询已送达运单统计",
                                type = QueryDeliveryRiskControlOrderResponse.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "查询已送达运单统计",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryCompletedStatisticResponse queryCompletedStatistic(QueryCompletedStatisticRequest request);



        @MethodDoc(
                displayName = "通过运单id查询运单信息",
                description = "通过运单id查询运单信息",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "通过运单id查询运单信息",
                                type = QueryDeliveryRiskControlOrderResponse.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "通过运单id查询运单信息",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        BatchQueryDeliveryOrderResponse queryDeliveryOrderByIds(QueryDeliveryOrderByIdListRequest request);


        @MethodDoc(
                displayName = "批量查询骑手+状态列表的运单",
                description = "批量查询骑手+状态列表的运单",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "针对自营配送骑手，分页查询已领取+已取货的运单",
                                type = PageQueryInProgressDeliveryOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "骑手已领取+已取货的运单",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        BatchQueryDeliveryOrderResponse queryDeliveryOrderByRiderAndStatusList(QueryDeliveryOrderByRiderAndStatusRequest request);

        @MethodDoc(
                displayName = "查询自配送预定单下发时间",
                description = "查询自配送预定单下发时间",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询预定单自配送下发时间",
                                type = PageQueryInProgressDeliveryOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "骑手已领取+已取货的运单",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        Long calcSdmsOrderPushDownTimestamp(Long merchantId, Long warehouseId, String tradeOrderNo, Integer orderBizType);
}
