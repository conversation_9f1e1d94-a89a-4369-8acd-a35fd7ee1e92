package com.sankuai.meituan.shangou.empower.rider.client.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 配送异步任务枚举
 * <p>注意：配送异步任务的code取值范围[190001,190100]。在E-API中是否是</p>
 *
 * <AUTHOR>
 * @since 2022/07/03
 */
public enum DeliveryTaskTypeEnum {
    EXPORT_RIDER_DELIVERY_EXCEPTION_DETAILS(190001, "导出骑手配送异常明细列表")
    ;

    private final int code;
    private final String name;

    DeliveryTaskTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    // static
    // ----------------------------------------------------------------------

    private static final Map<Integer, DeliveryTaskTypeEnum> codeMap;

    static {
        codeMap = new HashMap<>();
        for (DeliveryTaskTypeEnum value : values()) {
            codeMap.put(value.getCode(),value);
        }
    }

    public static DeliveryTaskTypeEnum ofCode(int code) {
        return codeMap.get(code);
    }
}