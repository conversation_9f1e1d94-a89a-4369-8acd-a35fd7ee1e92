package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

/**
 * <AUTHOR>
 * @since 2024/3/18 19:59
 **/
@ThriftStruct
public class TDeliveryRiskControlOrder {
    private Long tenantId;

    private Long storeId;

    private Long riderAccountId;

    private Integer orderBizType;

    private String viewOrderId;

    private Long dt;


    @ThriftField(1)
    public Long getTenantId() {
        return this.tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(2)
    public Long getStoreId() {
        return this.storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(3)
    public Long getRiderAccountId() {
        return this.riderAccountId;
    }

    @ThriftField
    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    @ThriftField(4)
    public Integer getOrderBizType() {
        return this.orderBizType;
    }

    @ThriftField
    public void setOrderBizType(Integer orderBizType) {
        this.orderBizType = orderBizType;
    }

    @ThriftField(5)
    public String getViewOrderId() {
        return this.viewOrderId;
    }

    @ThriftField
    public void setViewOrderId(String viewOrderId) {
        this.viewOrderId = viewOrderId;
    }

    @ThriftField(6)
    public Long getDt() {
        return this.dt;
    }

    @ThriftField
    public void setDt(Long dt) {
        this.dt = dt;
    }
}
