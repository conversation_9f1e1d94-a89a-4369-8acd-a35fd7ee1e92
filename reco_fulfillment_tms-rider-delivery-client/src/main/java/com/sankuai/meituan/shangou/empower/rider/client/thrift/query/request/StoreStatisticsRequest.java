package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@ThriftStruct
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreStatisticsRequest {

    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店列表",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<Long> storeIds;


    public void valid() {
        if (tenantId == null || tenantId <= 0) {
            throw new IllegalArgumentException("tenantId is illegal");
        }
        if (CollectionUtils.isEmpty(storeIds) || storeIds.size() > 100) {
            throw new IllegalArgumentException("storeIds is illegal");
        }
    }

}
