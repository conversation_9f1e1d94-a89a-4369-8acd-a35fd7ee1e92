package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryRiskControlOrder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/18 19:58
 **/
@ThriftStruct
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryDeliveryRiskControlOrderResponse {
    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    private Status status = Status.SUCCESS;

    @FieldDoc(
            description = "运单信息列表",
            requiredness = Requiredness.REQUIRED
    )
    private List<TDeliveryRiskControlOrder> deliveryRiskControlOrders;


    @ThriftField(1)
    public Status getStatus() {
        return this.status;
    }

    @ThriftField
    public void setStatus(Status status) {
        this.status = status;
    }

    @ThriftField(2)
    public List<TDeliveryRiskControlOrder> getDeliveryRiskControlOrders() {
        return this.deliveryRiskControlOrders;
    }

    @ThriftField
    public void setDeliveryRiskControlOrders(List<TDeliveryRiskControlOrder> deliveryRiskControlOrders) {
        this.deliveryRiskControlOrders = deliveryRiskControlOrders;
    }
}
