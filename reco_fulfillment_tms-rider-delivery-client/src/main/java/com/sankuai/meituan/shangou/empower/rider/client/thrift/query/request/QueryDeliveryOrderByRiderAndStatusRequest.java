package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@TypeDoc(
        description = "针对自营配送骑手，查询进行中的运单"
)
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
@ToString
@Data
public class QueryDeliveryOrderByRiderAndStatusRequest {

    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店ID列表",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "配送状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Integer deliveryStatus;

    @FieldDoc(
            description = "骑手账号id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Long riderAccountId;

    public String validate() {

        if (tenantId == null || tenantId < 1L) {
            return "租户ID不合法";
        }
        if (storeId == null) {
            return "门店ID不合法";
        }
        if (deliveryStatus == null) {
            return "配送状态ID不合法";
        }
        return null;
    }
}
