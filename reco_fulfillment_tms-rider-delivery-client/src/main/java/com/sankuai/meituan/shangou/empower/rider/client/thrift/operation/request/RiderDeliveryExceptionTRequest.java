package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/06/28
 */
@ThriftStruct
@Data
public class RiderDeliveryExceptionTRequest {
    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    private Long tenantId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    private Long storeId;

    @FieldDoc(
            description = "运单id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    private Long deliveryOrderId;

    @FieldDoc(
            description = "渠道订单id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    private Long channelOrderId;

    @FieldDoc(
            description = "订单业务类型",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    private Integer orderBizType;

    @FieldDoc(
            description = "订单支付时间",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    private Long payTime ;

    @FieldDoc(
            description = "订单日流水号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(7)
    private Integer daySeq ;


    @FieldDoc(
            description = "骑手账号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(8)
    private Long riderAccountId ;


    @FieldDoc(
            description = "骑手账号名",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(9)
    private String riderAccountName ;

    @FieldDoc(
            description = "异常一级类型",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(10)
    private Integer exceptionType;

    @FieldDoc(
            description = "异常二级类型",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(11)
    private Integer exceptionSubType;

    @FieldDoc(
            description = "照片URL列表",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(12)
    private List<String> picUrls;

    @FieldDoc(
            description = "备注",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(13)
    private String comment;

    @FieldDoc(
            description = "用户真实地址",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(14)
    private String userRealAddress;

    @FieldDoc(
            description = "修改后的地址",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(15)
    private String modifiedAddress;

    public String validate() {
        if (tenantId == null || tenantId <= 0L) {
            return "租户id不合法";
        }

        if (storeId == null || storeId <= 0L) {
            return "门店id不合法";
        }

        if (deliveryOrderId == null || deliveryOrderId <= 0L) {
            return "运单id不合法";
        }

        if (channelOrderId == null || channelOrderId <= 0L) {
            return "渠道id不合法";
        }

        if (orderBizType == null || orderBizType <= 0L) {
            return "订单业务类型不合法";
        }

        if (exceptionType == null || exceptionType <= 0L) {
            return "异常类型不合法";
        }

        if (exceptionSubType == null || exceptionSubType < 0L) {
            return "异常二级类型不合法";
        }

        if (riderAccountId == null || riderAccountId <= 0L) {
            return "骑手id不合法";
        }

        if (StringUtils.isBlank(riderAccountName)) {
            return "骑手账号名不合法";
        }

        if (daySeq == null || daySeq <= 0L) {
            return "订单日流水号不合法";
        }

        if (payTime == null || payTime<= 0L) {
            return "订单支付时间不合法";
        }

        return null;
    }
}
