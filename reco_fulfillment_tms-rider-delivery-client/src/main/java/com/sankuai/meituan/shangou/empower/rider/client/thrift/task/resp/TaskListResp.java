package com.sankuai.meituan.shangou.empower.rider.client.thrift.task.resp;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.rider.client.common.PageInfoResp;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.dto.TaskItemDto;

import java.util.List;

/**
 * 库存设置响应
 *
 * @author: lin<PERSON><PERSON>u <br>
 * @since: 2019-07-30 Time: 11:35
 */
@TypeDoc(description = "任务列表查询响应")
@ThriftStruct
public class TaskListResp {

    @FieldDoc(description = "响应状态")
    private Status status;

    @FieldDoc(description = "分页信息")
    private PageInfoResp page;

    @FieldDoc(description = "任务项列表")
    private List<TaskItemDto> items;

    public TaskListResp() {
    }

    @ThriftConstructor
    public TaskListResp(Status status, PageInfoResp page, List<TaskItemDto> items) {
        this.status = status;
        this.page = page;
        this.items = items;
    }

    @ThriftField(1)
    public Status getStatus() {
        return status;
    }

    @ThriftField
    public void setStatus(Status status) {
        this.status = status;
    }

    @ThriftField(2)
    public PageInfoResp getPage() {
        return page;
    }

    @ThriftField
    public void setPage(PageInfoResp page) {
        this.page = page;
    }

    @ThriftField(3)
    public List<TaskItemDto> getItems() {
        return items;
    }

    @ThriftField
    public void setItems(List<TaskItemDto> items) {
        this.items = items;
    }
}
