/*
 * Copyright (c) 2020 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.shangou.empower.rider.client.common;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.*;

/**
 * 分页请求参数
 * <br><br>
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <br>
 * Date: 2020-11-10 Time: 16:58
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Setter
@Getter
public class PageInfoResp {

    @FieldDoc(
            description = "目标页码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Integer pageNo;

    @FieldDoc(
            description = "每页数量",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Integer pageSize;

    @FieldDoc(
            description = "总条目数",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Long totalCount;

    @FieldDoc(
            description = "总页数",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Long totalPage;

    @FieldDoc(
            description = "是否还有下一页",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public Boolean hasMore;


    public PageInfoResp(Integer pageNo, Integer pageSize, Long totalCount) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.totalCount = totalCount;
        this.totalPage = (totalCount + pageSize - 1) / pageSize;
        this.hasMore = pageNo < totalPage;
    }
}
