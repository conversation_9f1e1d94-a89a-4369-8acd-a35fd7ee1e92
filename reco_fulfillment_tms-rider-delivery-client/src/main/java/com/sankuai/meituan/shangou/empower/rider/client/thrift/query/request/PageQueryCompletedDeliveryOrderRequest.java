package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

@TypeDoc(
        description = "针对自营配送骑手，分页查询已送达+已取消的运单"
)
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
@Getter
@Setter
@ToString
public class PageQueryCompletedDeliveryOrderRequest {

    @FieldDoc(
            description = "分页查询的页码",
            requiredness = Requiredness.REQUIRED

    )
    @ThriftField(1)
    public Integer page;

    @FieldDoc(
            description = "分页查询的页面大小",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Integer pageSize;

    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Long tenantId;

    @FieldDoc(
            description = "赋能门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Long storeId;

    @FieldDoc(
            description = "骑手账号 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public Long riderAccountId;

    public String validate() {
        if (page == null || page < 1 || pageSize == null || pageSize < 1 || pageSize > 100) {
            return "分页参数错误";
        }
        if (tenantId == null || tenantId < 1L) {
            return "租户ID不合法";
        }
        if (storeId == null || storeId < 1L) {
            return "赋能门店ID不合法";
        }
        if (riderAccountId == null || riderAccountId < 1L) {
            return "骑手id参数错误";
        }

        return null;
    }
}
