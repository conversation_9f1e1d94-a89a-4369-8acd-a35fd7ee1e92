package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 针对自营配送骑手，按运单条件分页查询运单信息的请求.
 *
 * <AUTHOR>
 * @since 2021/6/11 18:42
 */
@TypeDoc(
        description = "针对自营配送骑手，按运单条件分页查询运单信息的请求",
        authors = {
                "liyang176"
        }
)
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
@ToString
public class PageQueryDeliveryOrderRequest {

    @FieldDoc(
            description = "分页查询的页码",
            requiredness = Requiredness.REQUIRED
    )
    private Integer page;

    @FieldDoc(
            description = "分页查询的页面大小",
            requiredness = Requiredness.REQUIRED
    )
    private Integer pageSize;

    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "赋能门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "配送状态",
            requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryStatus;

    @FieldDoc(
            description = "骑手账号 ID",
            requiredness = Requiredness.OPTIONAL
    )
    private Long riderAccountId;

    @ThriftField(1)
    public Integer getPage() {
        return page;
    }

    @ThriftField
    public void setPage(Integer page) {
        this.page = page;
    }

    @ThriftField(2)
    public Integer getPageSize() {
        return pageSize;
    }

    @ThriftField
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @ThriftField(3)
    public Long getTenantId() {
        return tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(4)
    public Long getStoreId() {
        return storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(5)
    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    @ThriftField
    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    @ThriftField(6)
    public Long getRiderAccountId() {
        return riderAccountId;
    }

    @ThriftField
    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    public String validate() {
        if (page == null || page <= 0 || pageSize == null || pageSize <= 0) {
            return "分页参数错误";
        }
        if (tenantId == null || tenantId <= 0L) {
            return "租户ID不合法";
        }
        if (storeId == null || storeId <= 0L) {
            return "赋能门店ID不合法";
        }
        if (deliveryStatus == null) {
            return "运单状态不合法";
        }

        return null;
    }
}
