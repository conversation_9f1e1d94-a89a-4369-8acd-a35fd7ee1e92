package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.netty.util.internal.StringUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2022/3/7
 */
@TypeDoc(
        description = "骑手同步位置"
)
@ThriftStruct
@Data
public class RiderLocatingExceptionTRequest {
    @FieldDoc(
            description = "骑手账号id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long riderAccountId;

    @FieldDoc(
            description = "异常类型",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Integer exceptionType;

    @FieldDoc(
            description = "设备id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public String uuid;


    @FieldDoc(
            description = "手机操作系统",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    public String phoneOS;

    @FieldDoc(
            description = "手机厂商",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    public String manufacturer;


    @FieldDoc(
            description = "前端记录的时间戳",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    public Long utime;


    public String validate(){
        if (riderAccountId == null || riderAccountId <= 0L) {
            return "骑手id不合法";
        }

        if (exceptionType == null) {
            return "异常类型不合法";
        }

        if (StringUtils.isBlank(uuid)) {
            return "uuid不合法";
        }

        if (utime == null || utime <= 0L) {
            return "时间戳不合法";
        }

        return null;

    }
}
