package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "预订单立即发配送请求体"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class RiderImmediatelyDeliveryTRequest {
    @FieldDoc(
            deprecated = "租户id"
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            deprecated = "门店id"
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            deprecated = "订单id"
    )
    @ThriftField(3)
    public Long orderId;

    @FieldDoc(
            deprecated = "用户账号id"
    )
    @ThriftField(4)
    public Long operatorAccountId;

    public String validate() {
        if (tenantId == null || tenantId <= 0L) {
            return "租户id不合法";
        }

        if (storeId == null || storeId <= 0L) {
            return "门店id不合法";
        }

        if (orderId == null || orderId <= 0L) {
            return "订单id不合法";
        }

        return null;

    }
}
