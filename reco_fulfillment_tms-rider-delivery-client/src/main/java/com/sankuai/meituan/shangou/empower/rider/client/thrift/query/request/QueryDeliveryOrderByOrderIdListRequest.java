package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/28 11:40
 **/
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryDeliveryOrderByOrderIdListRequest {
    @FieldDoc(
            description = "租户id"
    )
    @ThriftField(1)
    @NotNull(message = "租户id不能为空")
    public Long tenantId;

    @FieldDoc(
            description = "门店id"
    )
    @ThriftField(2)
    @NotNull(message = "门店id不能为空")
    public Long storeId;

    @FieldDoc(
            description = "订单id列表"
    )
    @ThriftField(3)
    @NotEmpty
    @Size(min = 1, max = 200, message = "订单id数量必须在1-200之间")
    public List<Long> orderIds;

    @FieldDoc(
            description = "是否返回已取消的运单"
    )
    @ThriftField(4)
    public Boolean returnCanceledDeliveryOrder = false;
}
