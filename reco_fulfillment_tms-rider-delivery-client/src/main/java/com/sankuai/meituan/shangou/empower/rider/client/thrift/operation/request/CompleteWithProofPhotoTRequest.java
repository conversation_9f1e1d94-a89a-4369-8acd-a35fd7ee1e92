package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/22 17:12
 **/
@ThriftStruct
@Data
public class CompleteWithProofPhotoTRequest {
    @FieldDoc(
            description = "配送运单号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long deliveryOrderId;


    @FieldDoc(
            description = "操作人id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long operatorId;

    @FieldDoc(
            description = "操作人名称",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public String operatorName;

    @FieldDoc(
            description = "操作人号码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public String operatorPhone;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    private Long storeId;

    @FieldDoc(
            description = "签收方式",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(6)
    private Integer signType;


    @FieldDoc(
            description = "照片列表",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    private List<String> proofPhotoList;

    @FieldDoc(
            description = "是否是弱网环境",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(8)
    private Boolean isWeakNetWork;


    @FieldDoc(
            description = "骑手经度",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(9)
    private String longitude;

    @FieldDoc(
            description = "骑手纬度",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(10)
    private String latitude;

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(11)
    private Long tenantId;


    public String validate() {
        if (deliveryOrderId == null || deliveryOrderId <= 0) {
            return "运单号不合法";
        }
        if (operatorId == null || operatorId <= 0L) {
            return "操作人不合法";
        }
        if (StringUtils.isBlank(operatorPhone)) {
            return "操作人手机号不合法";
        }

        return null;
    }

}
