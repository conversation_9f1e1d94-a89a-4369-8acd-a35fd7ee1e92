package com.sankuai.meituan.shangou.empower.rider.client.common;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/22
 */
public enum ResponseCodeEnum {

	FAILED(-1),
	SUCCESS(0),
	SUCCESS_PARTITION(1),
	REQUEST_PARAMS_ERROR(100),
	THRIFT_SERVICE_ERROR(200),
	ORDER_SERVER_ERROR(201),
	CHANNEL_SERVER_FAIL(205),
	RHINO_ERROR(300),
	UNKNOWN_ERROR(500),
	NOT_OPENED_CHANNEL_STORES(601),
	ANALYZE_EXCEL_ERROR(602),
	SYSTEM_INTERNAL_ERROR(900),
	DUP_DATA(701),
	CHECK_BIZ_ERROR(702);

	private static final Map<Integer, ResponseCodeEnum> VALUE_ENUM_MAP = new HashMap<>();
	private final int value;

	static {
		for (ResponseCodeEnum each : values()) {
			VALUE_ENUM_MAP.put(each.getValue(), each);
		}
	}

	ResponseCodeEnum(int value) {
		this.value = value;
	}

	public static ResponseCodeEnum enumOf(int value){
		return VALUE_ENUM_MAP.get(value);
	}

	public int getValue() {
		return value;
	}
}
