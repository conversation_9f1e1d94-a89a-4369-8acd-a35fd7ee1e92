package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 查询运单数量请求.
 *
 * <AUTHOR>
 * @since 2021/6/16 16:56
 */
@TypeDoc(
        description = "针对自营配送骑手，查询运单数量请求",
        authors = {
                "liyang176"
        }
)
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
@ToString
public class QueryDeliveryQuantityRequest {

    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "赋能门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "配送状态列表",
            requiredness = Requiredness.REQUIRED
    )
    private List<Integer> deliveryStatusList;

    @FieldDoc(
            description = "骑手账号 ID",
            requiredness = Requiredness.OPTIONAL
    )
    private Long riderAccountId;

    @ThriftField(1)
    public Long getTenantId() {
        return tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(2)
    public Long getStoreId() {
        return storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(3)
    public List<Integer> getDeliveryStatusList() {
        return deliveryStatusList;
    }

    @ThriftField
    public void setDeliveryStatusList(List<Integer> deliveryStatusList) {
        this.deliveryStatusList = deliveryStatusList;
    }

    @ThriftField(4)
    public Long getRiderAccountId() {
        return riderAccountId;
    }

    @ThriftField
    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    public String validate() {
        if (tenantId == null || tenantId <= 0L) {
            return "租户ID不合法";
        }
        if (storeId == null || storeId <= 0L) {
            return "赋能门店ID不合法";
        }
        if (CollectionUtils.isEmpty(deliveryStatusList)) {
            return "运单状态列表不合法";
        }

        return null;
    }
}
