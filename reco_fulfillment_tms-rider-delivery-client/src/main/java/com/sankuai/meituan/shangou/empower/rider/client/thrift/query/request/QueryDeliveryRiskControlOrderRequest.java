package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/3/18 19:49
 **/
@ThriftStruct
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryDeliveryRiskControlOrderRequest {
    private List<ViewOrderKey> viewOrderKeys;

    @ThriftField(1)
    public List<ViewOrderKey> getViewOrderKeys() {
        return this.viewOrderKeys;
    }

    @ThriftField
    public void setViewOrderKeys(List<ViewOrderKey> viewOrderKeys) {
        this.viewOrderKeys = viewOrderKeys;
    }

    @ThriftStruct
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ViewOrderKey {
        private String viewOrderId;

        private Integer orderBizType;


        @ThriftField(1)
        public String getViewOrderId() {
            return this.viewOrderId;
        }

        @ThriftField
        public void setViewOrderId(String viewOrderId) {
            this.viewOrderId = viewOrderId;
        }

        @ThriftField(2)
        public Integer getOrderBizType() {
            return this.orderBizType;
        }

        @ThriftField
        public void setOrderBizType(Integer orderBizType) {
            this.orderBizType = orderBizType;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            ViewOrderKey that = (ViewOrderKey) o;
            return Objects.equals(viewOrderId, that.viewOrderId) && Objects.equals(orderBizType, that.orderBizType);
        }

        @Override
        public int hashCode() {
            return Objects.hash(viewOrderId, orderBizType);
        }

        public String validate() {
            if (StringUtils.isBlank(viewOrderId)) {
                return "viewOrderId不合法";
            }

            if (orderBizType == null || orderBizType <= 0) {
                return "orderBizType不合法";
            }

            return "";
        }
    }

    public String validate() {
        if (CollectionUtils.isNotEmpty(viewOrderKeys)) {
            for (ViewOrderKey viewOrderKey : viewOrderKeys) {
                String errMsg = viewOrderKey.validate();
                if (StringUtils.isNotBlank(errMsg)) {
                    return errMsg;
                }
            }
        }

        return "";
    }
}
