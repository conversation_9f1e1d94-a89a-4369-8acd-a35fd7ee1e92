package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2021/12/27
 */


@ThriftStruct
@Data
@TypeDoc(
        description = "骑手送达时刻位置上报请求"
)
public class RiderArrivalLocationTRequest {
    @FieldDoc(
            description = "骑手账号id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long riderAccountId;

    @FieldDoc(
            description = "运单号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long deliveryOrderId;

    @FieldDoc(
            description = "纬度",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    @Deprecated
    public String latitude;


    @FieldDoc(
            description = "经度",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    @Deprecated
    public String longitude;


    @FieldDoc(
            description = "定位信息",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    public LocationInfo locationInfo;

       @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(6)
    public Long tenantId;


    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    public Long storeId;

    @Data
    public static class LocationInfo {
        @FieldDoc(
                description = "经度", requiredness = Requiredness.REQUIRED
        )
        @ThriftField(1)
        public String longitude;

        @FieldDoc(
                description = "纬度", requiredness = Requiredness.REQUIRED
        )
        @ThriftField(2)
        public String latitude;

        @FieldDoc(
                description = "定位结果来源", requiredness = Requiredness.OPTIONAL
        )
        @ThriftField(3)
        public String provider;

        @FieldDoc(
                description = "定位结果精确度", requiredness = Requiredness.OPTIONAL
        )
        @ThriftField(4)
        public String accuracy;

        @FieldDoc(
                description = "方向信息", requiredness = Requiredness.OPTIONAL
        )
        @ThriftField(5)
        public String bearing;

        @FieldDoc(
                description = "速度信息", requiredness = Requiredness.OPTIONAL
        )
        @ThriftField(6)
        public String speed;

        @FieldDoc(
                description = "时间信息", requiredness = Requiredness.REQUIRED
        )
        @ThriftField(7)
        public String time;

        @FieldDoc(
                description = "操作系统", requiredness = Requiredness.OPTIONAL
        )
        @ThriftField(8)
        public String os;

        private String validate() {
            if (StringUtils.isBlank(longitude)) {
                return "经度不合法";
            }

            if (StringUtils.isBlank(latitude)) {
                return "纬度不合法";
            }

            return null;
        }
    }


    public String validate() {
        if (deliveryOrderId == null || deliveryOrderId <= 0L) {
            return "运单id不合法";
        }

        if (riderAccountId == null || riderAccountId <= 0L) {
            return "骑手id不合法";
        }

        if (locationInfo != null && StringUtils.isNotBlank(locationInfo.validate())) {
            return locationInfo.validate();
        }

        return null;
    }

}
