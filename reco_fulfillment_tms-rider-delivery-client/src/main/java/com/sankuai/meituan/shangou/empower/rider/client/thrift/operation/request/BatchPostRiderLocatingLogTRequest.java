package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 批量上报前端骑手定位日志请求
 * <AUTHOR>
 * @Date 2022/3/14
 */
@ThriftStruct
@Data
@TypeDoc(
        description = "批量上报骑手定位日志请求"
)
public class BatchPostRiderLocatingLogTRequest {

    @FieldDoc(
            description = "定位日志列表"
    )
    @ThriftField(1)
    public List<RiderLocatingLogTRequest> locatingLogList;

    public String validate() {
        if (CollectionUtils.isEmpty(locatingLogList)) {
            return "日志列表为空";
        }
        for (RiderLocatingLogTRequest logTRequest : locatingLogList) {
            if (logTRequest == null) {
                return "日志为空";
            }

            String checkErr = logTRequest.validate();
            if (checkErr != null) {
                return checkErr;
            }
        }
        return null;
    }
}
