package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryRiderStatistics;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@TypeDoc(
        description = "骑手维度统计数据返回",
        authors = {
                "yujing10"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@ToString
public class RiderStatisticsResponse {

        @FieldDoc(
                description = "执行状态",
                requiredness = Requiredness.REQUIRED
        )
        public Status status = Status.SUCCESS;

        @FieldDoc(
                description = "骑手维度统计数据",
                requiredness = Requiredness.REQUIRED
        )
        public List<TDeliveryRiderStatistics> riderStatisticsList;

        public RiderStatisticsResponse(List<TDeliveryRiderStatistics> riderStatistics) {
                this.riderStatisticsList = riderStatistics;
        }


        @ThriftField(1)
        public Status getStatus() {
                return status;
        }

        @ThriftField
        public void setStatus(Status status) {
                this.status = status;
        }

        @ThriftField(2)
        public List<TDeliveryRiderStatistics> getRiderStatisticsList() {
                return riderStatisticsList;
        }

        @ThriftField
        public void setRiderStatisticsList(List<TDeliveryRiderStatistics> riderStatisticsList) {
                this.riderStatisticsList = riderStatisticsList;
        }
}
