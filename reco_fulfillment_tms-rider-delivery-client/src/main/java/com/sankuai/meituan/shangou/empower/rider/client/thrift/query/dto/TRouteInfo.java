package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/30 20:09
 **/
@ThriftStruct
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TRouteInfo {
    private List<TLocationCoordinate> tLocationCoordinateList;

    private String routeId;


    @ThriftField(1)
    public List<TLocationCoordinate> getTLocationCoordinateList() {
        return this.tLocationCoordinateList;
    }

    @ThriftField
    public void setTLocationCoordinateList(List<TLocationCoordinate> tLocationCoordinateList) {
        this.tLocationCoordinateList = tLocationCoordinateList;
    }

    @ThriftField(2)
    public String getRouteId() {
        return this.routeId;
    }

    @ThriftField
    public void setRouteId(String routeId) {
        this.routeId = routeId;
    }
}
