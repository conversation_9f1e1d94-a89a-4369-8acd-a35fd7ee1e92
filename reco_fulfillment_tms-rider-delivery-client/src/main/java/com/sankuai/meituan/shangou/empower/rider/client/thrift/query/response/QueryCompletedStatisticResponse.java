package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sun.org.apache.xpath.internal.operations.Bool;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/3/19 15:26
 **/

@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
public class QueryCompletedStatisticResponse {
    private Status status;

    private Long completedTotalCnt;


    private Boolean riskControlDataIsReady;

    private Long riskControlCnt;

    private Long oneYuanOrderCnt;


    @ThriftField(1)
    public Status getStatus() {
        return this.status;
    }

    @ThriftField
    public void setStatus(Status status) {
        this.status = status;
    }

    @ThriftField(2)
    public Long getCompletedTotalCnt() {
        return this.completedTotalCnt;
    }

    @ThriftField
    public void setCompletedTotalCnt(Long completedTotalCnt) {
        this.completedTotalCnt = completedTotalCnt;
    }

    @ThriftField(3)
    public Boolean getRiskControlDataIsReady() {
        return this.riskControlDataIsReady;
    }

    @ThriftField
    public void setRiskControlDataIsReady(Boolean riskControlDataIsReady) {
        this.riskControlDataIsReady = riskControlDataIsReady;
    }

    @ThriftField(4)
    public Long getRiskControlCnt() {
        return this.riskControlCnt;
    }

    @ThriftField
    public void setRiskControlCnt(Long riskControlCnt) {
        this.riskControlCnt = riskControlCnt;
    }

    @ThriftField(5)
    public Long getOneYuanOrderCnt() {
        return this.oneYuanOrderCnt;
    }

    @ThriftField
    public void setOneYuanOrderCnt(Long oneYuanOrderCnt) {
        this.oneYuanOrderCnt = oneYuanOrderCnt;
    }
}
