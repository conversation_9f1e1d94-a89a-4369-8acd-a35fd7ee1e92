package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 收货人信息.
 *
 * <AUTHOR>
 * @since 2021/6/14 16:59
 */
@TypeDoc(
        description = "收货人信息",
        authors = {
                "liyang176"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class TReceiver {

    @FieldDoc(
            description = "收货人姓名"
    )
    private String receiverName;

    @FieldDoc(
            description = "收货人电话"
    )
    private String receiverPhone;

    @FieldDoc(
            description = "收货人隐私号"
    )
    private String receiverPrivacyPhone;

    @FieldDoc(
            description = "收货人详细地址描述"
    )
    private String addressDetail;

    @FieldDoc(
            description = "经纬度坐标类型，-1-未知，0-火星坐标系，1-百度坐标系"
    )
    private Integer coordinateType;

    @FieldDoc(
            description = "经度"
    )
    private String longitude;

    @FieldDoc(
            description = "纬度"
    )
    private String latitude;

    @ThriftField(1)
    public String getReceiverName() {
        return receiverName;
    }

    @ThriftField
    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    @ThriftField(2)
    public String getReceiverPhone() {
        return receiverPhone;
    }

    @ThriftField
    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    @ThriftField(3)
    public String getReceiverPrivacyPhone() {
        return receiverPrivacyPhone;
    }

    @ThriftField
    public void setReceiverPrivacyPhone(String receiverPrivacyPhone) {
        this.receiverPrivacyPhone = receiverPrivacyPhone;
    }

    @ThriftField(4)
    public String getAddressDetail() {
        return addressDetail;
    }

    @ThriftField
    public void setAddressDetail(String addressDetail) {
        this.addressDetail = addressDetail;
    }

    @ThriftField(5)
    public Integer getCoordinateType() {
        return coordinateType;
    }

    @ThriftField
    public void setCoordinateType(Integer coordinateType) {
        this.coordinateType = coordinateType;
    }

    @ThriftField(6)
    public String getLongitude() {
        return longitude;
    }

    @ThriftField
    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    @ThriftField(7)
    public String getLatitude() {
        return latitude;
    }

    @ThriftField
    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }
}
