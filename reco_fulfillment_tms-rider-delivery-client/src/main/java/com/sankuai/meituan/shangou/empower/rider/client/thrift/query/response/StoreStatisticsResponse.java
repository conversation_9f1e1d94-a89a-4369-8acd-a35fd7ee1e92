package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryStoreStatistics;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@TypeDoc(
        description = "门店维度统计数据",
        authors = {
                "yujing10"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@ToString
public class StoreStatisticsResponse {

        @FieldDoc(
                description = "执行状态",
                requiredness = Requiredness.REQUIRED
        )
        public Status status = Status.SUCCESS;

        @FieldDoc(
                description = "门店维度统计",
                requiredness = Requiredness.REQUIRED
        )
        public List<TDeliveryStoreStatistics> storeStatisticsList;


        public StoreStatisticsResponse(List<TDeliveryStoreStatistics> storeStatistics) {
                this.storeStatisticsList = storeStatistics;
        }
        @ThriftField(1)
        public Status getStatus() {
                return status;
        }

        @ThriftField
        public void setStatus(Status status) {
                this.status = status;
        }

        @ThriftField(2)
        public List<TDeliveryStoreStatistics> getStoreStatisticsList() {
                return storeStatisticsList;
        }

        @ThriftField
        public void setStoreStatisticsList(List<TDeliveryStoreStatistics> storeStatisticsList) {
                this.storeStatisticsList = storeStatisticsList;
        }
}
