package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 骑手运单信息.
 *
 * <AUTHOR>
 * @since 2021/6/14 18:07
 */
@TypeDoc(
        description = "骑手运单信息",
        authors = {
                "liyang176"
        }
)
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
public class TRiderDeliveryOrderWithCurrentRider {

    @FieldDoc(
            description = "运单 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long deliveryOrderId;

    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "赋能门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "赋能订单 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long orderId;

    @FieldDoc(
            description = "渠道订单 ID",
            requiredness = Requiredness.REQUIRED
    )
    private String channelOrderId;

    @FieldDoc(
            description = "订单业务类型Code，参照 com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum",
            requiredness = Requiredness.REQUIRED
    )
    private Integer orderBizTypeCode;

    @FieldDoc(
            description = "预计送达时间",
            requiredness = Requiredness.OPTIONAL
    )
    private Long estimatedDeliveryTime;

    @FieldDoc(
            description = "预计送达结束时间",
            requiredness = Requiredness.OPTIONAL
    )
    private Long estimatedDeliveryEndTime;

    @FieldDoc(
            description = "运单状态",
            requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryStatus;

    @FieldDoc(
            description = "收货人信息",
            requiredness = Requiredness.REQUIRED
    )
    private TReceiver receiver;

    @FieldDoc(
            description = "运单送达时间",
            requiredness = Requiredness.OPTIONAL
    )
    private Long deliveryDoneTime;

    @FieldDoc(
            description = "当前骑手",
            requiredness = Requiredness.OPTIONAL
    )
    private TStaffRider currentRider;


    @FieldDoc(
            description = "运单状态是否被锁定：0-未锁定；1-锁定",
            example = {}
    )
    public Integer statusLocked;


    @FieldDoc(
            description = "运单状态是否可以被锁定：0-不可以；1-可以",
            example = {}
    )
    public Integer canStatusBeLocked;

    @FieldDoc(
            description = "配送距离",
            example = {}
    )
    private Long distance;

    @FieldDoc(
            description = "是否可以上传送达照片",
            example = {}
    )
    private Boolean couldPostDeliveryProofPhoto;

    @ThriftField(1)
    public Long getDeliveryOrderId() {
        return deliveryOrderId;
    }

    @ThriftField
    public void setDeliveryOrderId(Long deliveryOrderId) {
        this.deliveryOrderId = deliveryOrderId;
    }

    @ThriftField(2)
    public Long getTenantId() {
        return tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(3)
    public Long getStoreId() {
        return storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(4)
    public Long getOrderId() {
        return orderId;
    }

    @ThriftField
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    @ThriftField(5)
    public String getChannelOrderId() {
        return channelOrderId;
    }

    @ThriftField
    public void setChannelOrderId(String channelOrderId) {
        this.channelOrderId = channelOrderId;
    }

    @ThriftField(6)
    public Integer getOrderBizTypeCode() {
        return orderBizTypeCode;
    }

    @ThriftField
    public void setOrderBizTypeCode(Integer orderBizTypeCode) {
        this.orderBizTypeCode = orderBizTypeCode;
    }

    @ThriftField(7)
    public Long getEstimatedDeliveryTime() {
        return estimatedDeliveryTime;
    }

    @ThriftField
    public void setEstimatedDeliveryTime(Long estimatedDeliveryTime) {
        this.estimatedDeliveryTime = estimatedDeliveryTime;
    }

    @ThriftField(8)
    public Long getEstimatedDeliveryEndTime() {
        return estimatedDeliveryEndTime;
    }

    @ThriftField
    public void setEstimatedDeliveryEndTime(Long estimatedDeliveryEndTime) {
        this.estimatedDeliveryEndTime = estimatedDeliveryEndTime;
    }

    @ThriftField(9)
    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    @ThriftField
    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    @ThriftField(10)
    public TReceiver getReceiver() {
        return receiver;
    }

    @ThriftField
    public void setReceiver(TReceiver receiver) {
        this.receiver = receiver;
    }

    @ThriftField
    public void setCanStatusBeLocked(Integer canStatusBeLocked) {
        this.canStatusBeLocked = canStatusBeLocked;
    }

    @ThriftField(11)
    public TStaffRider getCurrentRider() {
        return currentRider;
    }
    @ThriftField
    public void setCurrentRider(TStaffRider currentRider) {
        this.currentRider = currentRider;
    }

    @ThriftField(12)
    public Long getDeliveryDoneTime() {
        return deliveryDoneTime;
    }

    @ThriftField
    public void setDeliveryDoneTime(Long deliveryDoneTime) {
        this.deliveryDoneTime = deliveryDoneTime;
    }

    @ThriftField(13)
    public Integer getStatusLocked() {
        return statusLocked;
    }

    @ThriftField
    public void setStatusLocked(Integer statusLocked) {
        this.statusLocked = statusLocked;
    }

    @ThriftField(14)
    public Integer getCanStatusBeLocked() {
        return canStatusBeLocked;
    }

    @ThriftField
    public void setDistance(Long distance) {
        this.distance = distance;
    }

    @ThriftField(15)
    public Long getDistance(){
        return distance;
    }


    @ThriftField(16)
    public Boolean getCouldPostDeliveryProofPhoto() {
        return this.couldPostDeliveryProofPhoto;
    }

    @ThriftField
    public void setCouldPostDeliveryProofPhoto(Boolean couldPostDeliveryProofPhoto) {
        this.couldPostDeliveryProofPhoto = couldPostDeliveryProofPhoto;
    }
}
