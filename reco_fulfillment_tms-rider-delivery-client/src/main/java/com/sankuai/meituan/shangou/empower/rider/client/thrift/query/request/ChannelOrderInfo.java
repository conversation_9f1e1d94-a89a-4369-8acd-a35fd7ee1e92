package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/8/28 11:41
 **/
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ChannelOrderInfo {
    @FieldDoc(
            description = "渠道订单id列表"
    )
    @ThriftField(1)
    public String channelOrderId;

    @FieldDoc(
            description = "订单业务类型"
    )
    @ThriftField(2)
    public Integer orderBizType;
}
