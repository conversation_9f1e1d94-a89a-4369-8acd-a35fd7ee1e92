package com.sankuai.meituan.shangou.empower.rider.client.thrift.query;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.DurationsStatisticsRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.PageQueryDeliveryOrderRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.RiderStatisticsRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.StoreStatisticsRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.DurationsStatisticsResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.RiderStatisticsResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.StoreStatisticsResponse;

/**
 * 本质上，这个类的这些接口同样是查询接口，可以放到RiderQueryThriftService中，
 * 但考虑到这些接口主要是给统计看板提供，尽管当前走mysql从库，但后续可能走es等，因而将其单独定义成一个thrift类
 */
@InterfaceDoc(
        displayName = "运单统计数据查询接口",
        type = "octo.thrift.annotation",
        scenarios = "运单统计数据查询接口",
        description = "运单统计数据查询接口",
        authors = {
                "yujing10"
        }
)
@ThriftService
public interface RiderDeliveryStatisticThriftService {

    @MethodDoc(
            displayName = "查询骑手维度运单统计信息",
            description = "查询骑手维度运单统计信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询骑手维度运单统计信息",
                            type = PageQueryDeliveryOrderRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询骑手维度运单统计信息返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    RiderStatisticsResponse queryRiderStatistics(RiderStatisticsRequest request);

    @MethodDoc(
            displayName = "查询门店维度运单统计信息",
            description = "查询门店维度运单统计信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询门店维度运单统计信息",
                            type = PageQueryDeliveryOrderRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询门店维度运单统计信息返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    StoreStatisticsResponse queryStoreStatistics(StoreStatisticsRequest request);

    @MethodDoc(
            displayName = "查询指定时间段内的骑手配送统计信息",
            description = "查询指定时间段内的骑手配送统计信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询指定时间段内的骑手配送统计信息",
                            type = DurationsStatisticsRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "指定时间段内的骑手配送统计信息",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    DurationsStatisticsResponse queryDurationsStatistics(DurationsStatisticsRequest request);
}
