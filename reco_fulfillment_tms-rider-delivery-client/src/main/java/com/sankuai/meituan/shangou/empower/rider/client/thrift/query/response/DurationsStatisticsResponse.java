package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryRiderStatistics;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDurationDeliveryStatistic;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/8/24 17:07
 **/
@ThriftStruct
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DurationsStatisticsResponse {
    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status = Status.SUCCESS;

    @FieldDoc(
            description = "统计数据",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<TRiderDurationDeliveryStatistic> durationDeliveryStatistics;

    public DurationsStatisticsResponse(List<TRiderDurationDeliveryStatistic> durationDeliveryStatistics) {
        this.durationDeliveryStatistics = durationDeliveryStatistics;
    }
}
