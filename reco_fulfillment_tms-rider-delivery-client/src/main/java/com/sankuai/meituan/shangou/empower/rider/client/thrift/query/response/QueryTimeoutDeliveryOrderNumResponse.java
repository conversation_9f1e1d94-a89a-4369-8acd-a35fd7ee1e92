package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Map;

/**
 * 查询超时运单数量返回值.
 *
 * <AUTHOR>
 * @since 2022/12/8
 */
@TypeDoc(
        description = "针对自营配送骑手，查询超时运单数量返回值.",
        authors = {
                "yuanyu09"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@ToString
public class QueryTimeoutDeliveryOrderNumResponse {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    private Status status = Status.SUCCESS;

    @FieldDoc(
            description = "运单状态-数量 map",
            requiredness = Requiredness.REQUIRED
    )
    private Map<Integer, Integer> deliveryStatusCountMap;

    public QueryTimeoutDeliveryOrderNumResponse(Status status) {
        this.status = status;
    }

    public QueryTimeoutDeliveryOrderNumResponse(Map<Integer, Integer> deliveryStatusCountMap) {
        this.deliveryStatusCountMap = deliveryStatusCountMap;
    }

    @ThriftField(1)
    public Status getStatus() {
        return status;
    }

    @ThriftField
    public void setStatus(Status status) {
        this.status = status;
    }

    @ThriftField(2)
    public Map<Integer, Integer> getDeliveryStatusCountMap() {
        return deliveryStatusCountMap;
    }

    @ThriftField
    public void setDeliveryStatusCountMap(Map<Integer, Integer> deliveryStatusCountMap) {
        this.deliveryStatusCountMap = deliveryStatusCountMap;
    }
}
