package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 骑手指定时间段内的配送统计数据
 * <AUTHOR>
 * @since 2022/8/24 17:11
 **/
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@Data
public class TRiderDurationDeliveryStatistic {
    @ThriftField(1)
    @FieldDoc(description = "租户id")
    public Long tenantId;

    @ThriftField(2)
    @FieldDoc(description = "骑手id")
    public Long riderAccountId;

    @ThriftField(3)
    @FieldDoc(description = "已送达订单数")
    public Integer deliveredCount;

    @ThriftField(4)
    @FieldDoc(description = "配送超时订单数")
    public Integer timeoutCount;

    @ThriftField(5)
    @FieldDoc(description = "25分钟送达率")
    public String deliveredRateIn25min;

    @ThriftField(6)
    @FieldDoc(description = "送达前取消订单数")
    public Integer cancelBeforeDeliveredCount;

    @ThriftField(7)
    @FieldDoc(description = "提前点送达订单数")
    public Integer earlyClickDeliveryCount;

    @ThriftField(8)
    @FieldDoc(description = "平均履约时长")
    public String avgFulfillDuration;

    @ThriftField(9)
    @FieldDoc(description = "超时率")
    public String timeoutRate;

    @ThriftField(10)
    @FieldDoc(description = "统计时间范围-开始日期 格式：yyyyMMdd")
    public String beginDate;

    @ThriftField(11)
    @FieldDoc(description = "统计时间范围-结束日期 格式：yyyyMMdd")
    public String endDate;

    @ThriftField(12)
    @FieldDoc(description = "配送风控订单数")
    private Integer riskControlCount;

    @ThriftField(13)
    @FieldDoc(description = "配送超时订单数据")
    private Integer etaOvertimeOrdNumV2;

    @ThriftField(14)
    @FieldDoc(description = "配送超时订单率 百分比数")
    private String etaOvertimeRatioV2;
}
