package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ThriftStruct
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageReq {
    @FieldDoc(
            description = "pageNum",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Integer pageNum;

    @FieldDoc(
            description = "pageSize",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Integer pageSize;

    public void valid() {
        if (pageNum == null || pageNum < 1 || pageSize == null || pageSize < 1 || pageSize > 100) {
            throw new IllegalArgumentException("pageNum or pageSize is illegal");
        }
    }
}
