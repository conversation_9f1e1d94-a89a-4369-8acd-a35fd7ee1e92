package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.*;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.response.RiderChangeResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.response.RiderOperateTResponse;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/14
 */
@InterfaceDoc(
        displayName = "骑手配送操作服务",
        type = "octo.thrift.annotation",
        scenarios = "骑手配送操作服务，提供接单，取货，配送完成等能力",
        description = "骑手配送操作服务，提供接单，取货，配送完成等能力",
        authors = {
                "qianteng"
        }
)
@ThriftService
public interface RiderOperateThriftService {

    @MethodDoc(
            displayName = "骑手接单",
            description = "骑手接单",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手接单请求",
                            type = RiderOperateTRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "骑手接单请求操作结果",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    RiderOperateTResponse accept(RiderOperateTRequest request);

    @MethodDoc(
            displayName = "骑手取货",
            description = "骑手取货",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手取货请求",
                            type = RiderOperateTRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "骑手取货请求操作结果",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    RiderOperateTResponse takeAway(RiderOperateTRequest request);

    @MethodDoc(
            displayName = "骑手送达",
            description = "骑手送达",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手送达请求",
                            type = RiderOperateTRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "骑手送达请求操作结果",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    RiderOperateTResponse complete(RiderOperateTRequest request);


    @MethodDoc(
            displayName = "骑手送达并上传送达图片",
            description = "骑手送达并上传送达图片",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手送达并上传送达图片",
                            type = RiderOperateTRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "骑手送达并上传送达图片请求操作结果",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    RiderOperateTResponse completeWithProofPhoto(CompleteWithProofPhotoTRequest request);

    @MethodDoc(
            displayName = "针对自营配送骑手，店长定向转单改派骑手",
            description = "针对自营配送骑手，店长定向转单改派骑手",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "针对自营配送骑手，店长定向转单改派骑手",
                            type = RiderChangeRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "改派骑手操作结果",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    RiderChangeResponse riderChange(RiderChangeRequest request);

    @MethodDoc(
            displayName = "自营配送骑手，骑手位置同步",
            description = "自营配送骑手，骑手位置同步",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自营配送骑手，骑手位置同步",
                            type = RiderLocationRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "骑手位置同步操作结果",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    RiderOperateTResponse riderLocation(RiderLocationRequest request);

    @MethodDoc(
            displayName = "上报骑手送达时刻位置",
            description = "上报骑手送达时刻位置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自营配送骑手，上报骑手送达时刻位置",
                            type = RiderLocationRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "上报骑手送达时刻位置操作结果",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    RiderOperateTResponse riderArrivalLocation(RiderArrivalLocationTRequest request);

    @MethodDoc(
            displayName = "上报骑手定位异常原因",
            description = "上报骑手定位异常原因",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自营配送骑手，上报骑手定位异常原因",
                            type = RiderLocatingExceptionTRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "上报骑手定位异常原因操作结果",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    RiderOperateTResponse riderLocatingException(RiderLocatingExceptionTRequest request);

    @MethodDoc(
            displayName = "批量上报骑手定位前端日志",
            description = "批量上报骑手定位前端日志",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自营配送骑手，上报骑手定位异常原因",
                            type = RiderLocatingExceptionTRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "批量上报骑手定位前端日志",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    RiderOperateTResponse batchPostRiderLocatingLog(BatchPostRiderLocatingLogTRequest request);

    @MethodDoc(
            displayName = "预订单立即发配送",
            description = "预订单立即发配送",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自营配送骑手，预订单立即发配送",
                            type = RiderImmediatelyDeliveryTRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "预订单立即发配送",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    RiderOperateTResponse immediatelyDelivery(RiderImmediatelyDeliveryTRequest request);


    @MethodDoc(
            displayName = "锁定当前配送状态",
            description = "锁定当前配送状态",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自营配送骑手，锁定当前配送状态",
                            type = RiderOperateTRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "自营配送骑手，锁定当前配送状态",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    RiderOperateTResponse lockDeliveryStatus (RiderOperateTRequest request);

    @MethodDoc(
            displayName = "解锁当前配送状态",
            description = "解锁当前配送状态",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自营配送骑手，解锁当前配送状态",
                            type = RiderOperateTRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "自营配送骑手，解锁当前配送状态",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    RiderOperateTResponse unlockDeliveryStatus (RiderOperateTRequest request);

    @MethodDoc(
            displayName = "上报配送异常",
            description = "上报配送异常",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自营配送骑手，上报配送异常",
                            type = RiderDeliveryExceptionTRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "自营配送骑手，上报配送异常",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    RiderOperateTResponse reportDeliveryException (RiderDeliveryExceptionTRequest request);

    @MethodDoc(
            displayName = "上传送达照片",
            description = "上传送达照片",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自营配送骑手,上传送达照片",
                            type = RiderPostDeliveryProofPhotoTRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "自营配送骑手,上传送达照片",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    RiderOperateTResponse postDeliveryProofPhoto(RiderPostDeliveryProofPhotoTRequest request);


    @MethodDoc(
            displayName = "标记拣配分离",
            description = "标记拣配分离",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自营配送,标记拣配分离",
                            type = RiderPostDeliveryProofPhotoTRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "自营配送,标记拣配分离",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    RiderOperateTResponse markPickDeliverySplit(RiderOperateTRequest request);
}
