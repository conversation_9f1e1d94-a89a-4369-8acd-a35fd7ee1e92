package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/6/6 19:12
 **/
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
public class QueryPricingRouteInfoRequest {
    private Long tenantId;

    private List<Long> deliveryOrderIds;

    public String validate() {
        if (Objects.isNull(tenantId)) {
            return "租户id不合法";
        }

        if(Objects.isNull(deliveryOrderIds)) {
            return "运单id不合法";
        }

        return null;
    }

    @ThriftField(1)
    public Long getTenantId() {
        return this.tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }


    @ThriftField(2)
    public List<Long> getDeliveryOrderIds() {
        return this.deliveryOrderIds;
    }

    @ThriftField
    public void setDeliveryOrderIds(List<Long> deliveryOrderIds) {
        this.deliveryOrderIds = deliveryOrderIds;
    }
}
