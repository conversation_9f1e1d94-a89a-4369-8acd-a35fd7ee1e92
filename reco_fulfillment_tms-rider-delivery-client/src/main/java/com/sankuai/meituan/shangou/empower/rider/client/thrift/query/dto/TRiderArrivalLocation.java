package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 自营骑手到刻位置信息.
 *
 * <AUTHOR>
 * @since 2022/6/14
 */
@TypeDoc(
        description = "",
        authors = {
                "linxiaorui"
        }
)
@AllArgsConstructor
@NoArgsConstructor
@Data
@ThriftStruct
public class TRiderArrivalLocation {
    @FieldDoc(
            description = "骑手到刻位置",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    public TLocationCoordinate locationCoordinate;

    @FieldDoc(
            description = "骑手到刻位置与收货地址间的直线距离(单位:米),-1表示默认值",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public Long linearDistance;

    @FieldDoc(
            description = "骑手到刻位置与收货地址间的导航距离(单位:米),-1表示默认值",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public Long navigationDistance;
}
