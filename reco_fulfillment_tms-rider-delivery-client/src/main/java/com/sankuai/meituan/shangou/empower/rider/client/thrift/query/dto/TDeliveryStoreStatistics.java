package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

@TypeDoc(
        description = "门店维度统计数据",
        authors = {
                "yujing10"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@ToString
public class TDeliveryStoreStatistics {

    @FieldDoc(description = "门店id")
    public Long storeId;

    @FieldDoc(description = "待领取订单量")
    public Integer waitToAccept;

    @FieldDoc(description = "待领取超时订单量")
    private Integer waitToAcceptJudgeTimeout;

    @FieldDoc(description = "拣货中（骑手已接单）订单量")
    private Integer picking;

    @FieldDoc(description = "拣货中（骑手已接单）超时订单量")
    private Integer pickingJudgeTimeout;

    @FieldDoc(description = "配送中（骑手已取货）订单量")
    private Integer delivering;

    @FieldDoc(description = "配送中（骑手已取货）超时订单量")
    private Integer deliveringJudgeTimeout;

    @FieldDoc(description = "履约中订单数")
    private Integer fulfilling;

    @FieldDoc(description = "履约中骑手数")
    private Integer fulfillingRider;

    @FieldDoc(description = "拣货中（骑手已接单）超时订单量 V2版本")
    private Integer pickingJudgeTimeoutV2;

    @FieldDoc(description = "配送中（骑手已取货）超时订单量 V2版本")
    private Integer deliveringJudgeTimeoutV2;

    @FieldDoc(description = "待领取超时订单量 V2版本")
    private Integer waitToAcceptJudgeTimeoutV2;

    @ThriftField(1)
    public Long getStoreId() {
        return storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(2)
    public Integer getWaitToAccept() {
        return waitToAccept;
    }

    @ThriftField
    public void setWaitToAccept(Integer waitToAccept) {
        this.waitToAccept = waitToAccept;
    }

    @ThriftField(3)
    public Integer getWaitToAcceptJudgeTimeout() {
        return waitToAcceptJudgeTimeout;
    }

    @ThriftField
    public void setWaitToAcceptJudgeTimeout(Integer waitToAcceptJudgeTimeout) {
        this.waitToAcceptJudgeTimeout = waitToAcceptJudgeTimeout;
    }

    @ThriftField(4)
    public Integer getPicking() {
        return picking;
    }

    @ThriftField
    public void setPicking(Integer picking) {
        this.picking = picking;
    }

    @ThriftField(5)
    public Integer getPickingJudgeTimeout() {
        return pickingJudgeTimeout;
    }

    @ThriftField
    public void setPickingJudgeTimeout(Integer pickingJudgeTimeout) {
        this.pickingJudgeTimeout = pickingJudgeTimeout;
    }

    @ThriftField(6)
    public Integer getDelivering() {
        return delivering;
    }

    @ThriftField
    public void setDelivering(Integer delivering) {
        this.delivering = delivering;
    }

    @ThriftField(7)
    public Integer getDeliveringJudgeTimeout() {
        return deliveringJudgeTimeout;
    }

    @ThriftField
    public void setDeliveringJudgeTimeout(Integer deliveringJudgeTimeout) {
        this.deliveringJudgeTimeout = deliveringJudgeTimeout;
    }

    @ThriftField(8)
    public Integer getFulfilling() {
        return fulfilling;
    }

    @ThriftField
    public void setFulfilling(Integer fulfilling) {
        this.fulfilling = fulfilling;
    }

    @ThriftField(9)
    public Integer getFulfillingRider() {
        return fulfillingRider;
    }

    @ThriftField
    public void setFulfillingRider(Integer fulfillingRider) {
        this.fulfillingRider = fulfillingRider;
    }

    @ThriftField(10)
    public Integer getPickingJudgeTimeoutV2() {
        return pickingJudgeTimeoutV2;
    }

    @ThriftField
    public void setPickingJudgeTimeoutV2(Integer pickingJudgeTimeoutV2) {
        this.pickingJudgeTimeoutV2 = pickingJudgeTimeoutV2;
    }

    @ThriftField(11)
    public Integer getDeliveringJudgeTimeoutV2() {
        return deliveringJudgeTimeoutV2;
    }

    @ThriftField
    public void setDeliveringJudgeTimeoutV2(Integer deliveringJudgeTimeoutV2) {
        this.deliveringJudgeTimeoutV2 = deliveringJudgeTimeoutV2;
    }

    @ThriftField(12)
    public Integer getWaitToAcceptJudgeTimeoutV2() {
        return waitToAcceptJudgeTimeoutV2;
    }

    @ThriftField
    public void setWaitToAcceptJudgeTimeoutV2(Integer waitToAcceptJudgeTimeoutV2) {
        this.waitToAcceptJudgeTimeoutV2 = waitToAcceptJudgeTimeoutV2;
    }
}
