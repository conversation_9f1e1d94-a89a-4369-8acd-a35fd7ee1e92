package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TLocationCoordinate;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderArrivalLocation;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;


@TypeDoc(
        description = "骑手到刻位置信息（坐标和距离）",
        authors = {
                "linxiaorui"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class QueryRiderArrivalLocationResponse {
    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status = Status.SUCCESS;

    @FieldDoc(
            description = "骑手到刻位置信息(到刻经纬度、到刻距离)",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public TRiderArrivalLocation tRiderArrivalLocation;

    public QueryRiderArrivalLocationResponse(Status status) {
        this.status = status;
    }

    public QueryRiderArrivalLocationResponse(TRiderArrivalLocation tRiderArrivalLocation) {
        this.tRiderArrivalLocation = tRiderArrivalLocation;
    }
}
