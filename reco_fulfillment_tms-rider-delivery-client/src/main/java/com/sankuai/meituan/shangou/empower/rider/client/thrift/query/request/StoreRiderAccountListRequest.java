package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@FieldDoc(
        description = "查询门店下骑手名单（具有门店权限的店员/店长/兼职骑手）请求"
)
@ThriftStruct
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StoreRiderAccountListRequest {
    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "appId",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Integer appId;

    public String validate() {

        if (tenantId == null || tenantId < 1L) {
            return "租户ID不合法";
        }
        if (storeId == null || storeId < 1L) {
            return "门店ID不合法";
        }
        if (appId == null || appId < 1L) {
            return "appId不合法";
        }

        return null;
    }
}
