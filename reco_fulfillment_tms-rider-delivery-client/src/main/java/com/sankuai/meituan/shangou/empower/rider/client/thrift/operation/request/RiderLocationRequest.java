package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request;

import com.dianping.cat.util.StringUtils;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@TypeDoc(
        description = "骑手同步位置"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@SuppressWarnings("DuplicatedCode")
public class RiderLocationRequest {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "骑手id", requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Long riderAccountId;

    @FieldDoc(
            description = "骑手名字", requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public String riderName;

    @FieldDoc(
            description = "骑手电话", requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public String riderPhone;

    @FieldDoc(
            description = "经度", requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(6)
    public String longitude;

    @FieldDoc(
            description = "纬度", requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    public String latitude;

    @FieldDoc(
            description = "定位结果来源", requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(8)
    public String provider;

    @FieldDoc(
            description = "定位结果精确度", requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(9)
    public String accuracy;

    @FieldDoc(
            description = "方向信息", requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(10)
    public String bearing;

    @FieldDoc(
            description = "速度信息", requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(11)
    public String speed;

    @FieldDoc(
            description = "时间信息", requiredness = Requiredness.REQUIRED
    )
    @ThriftField(12)
    public String time;

    @FieldDoc(
            description = "操作系统", requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(13)
    public String os;

    @FieldDoc(
            description = "牵牛花app版本", requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(14)
    public String appVersion;

    @FieldDoc(
            description = "骑手设备id", requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(15)
    public String uuid;


    public String validate() {
        if (tenantId == null || tenantId <= 0L) {
            return "租户id不合法";
        }

        if (storeId == null || storeId <= 0L) {
            return "门店id不合法";
        }

        if (riderAccountId == null || riderAccountId <= 0L) {
            return "骑手账号id不合法";
        }
        if (StringUtils.isBlank(riderName)) {
            return "骑手名称不合法";
        }
        if (StringUtils.isBlank(riderName)) {
            return "骑手电话号不合法";
        }
        if (StringUtils.isBlank(latitude)) {
            return "纬度不能为空";
        }
        double latitudeData=Double.parseDouble(latitude);
        if(latitudeData>90 || latitudeData<-90){
            return "纬度("+latitude+")不在[-90,90]范围内";
        }
        if (StringUtils.isBlank(longitude)) {
            return "经度不能为空";
        }
        double longitudeData=Double.parseDouble(longitude);
        if(longitudeData>180 || longitudeData<-180){
            return "经度"+longitude+"不在[-180,180]范围内";
        }
        if (StringUtils.isBlank(time)) {
            return "定位时间不能为空";
        }
        try {
            LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            return "定位时间格式不合法, 要求格式:yyyy-MM-dd HH:mm:ss";
        }
        return null;
    }

}
