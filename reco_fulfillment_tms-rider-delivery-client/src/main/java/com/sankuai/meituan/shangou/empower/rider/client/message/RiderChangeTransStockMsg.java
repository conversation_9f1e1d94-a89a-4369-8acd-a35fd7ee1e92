package com.sankuai.meituan.shangou.empower.rider.client.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-02-12
 * @email <EMAIL>
 */
@Data
public class RiderChangeTransStockMsg {

    private long tenantId;

    private long storeId;

    private String channelOrderId;

    private int orderBizType;

    private long beforeRiderAccountId;

    private String beforeRiderName;

    private long afterRiderAccountId;

    private String afterRiderName;

    private TransStockMaterialInfo transStockMaterialInfo;

    private long operateTime;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TransStockMaterialInfo {

        private String materialSkuId;

        private String materialSkuName;

        private Integer operateCount;

        private String picUrl;

        private String specification;

    }

}
