package com.sankuai.meituan.shangou.empower.rider.client.thrift.task.dto;

/**
 * Task任务中的结果字符串对应的对象
 * <AUTHOR>
 * @since 2021/10/07
 */
public class TaskResultDto {
    /**
     * 结果描述
     * <p>这里是结果的提示语。成功失败时都有可能有/p>
     */
    private String description;

    /**
     * 更详细的结果信息。比如失败时，除了正常的提示外，这里保存了失败的具体原因
     */
    private String detailMessage;

    /**
     * 文件名
     * <p>无论成功还是失败，都有可能有下载文件S3地址</p>
     */
    private String fileName;

    public TaskResultDto() {
    }

    public TaskResultDto(String description, String detailMessage, String fileName) {
        this.description = description;
        this.detailMessage = detailMessage;
        this.fileName = fileName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDetailMessage() {
        return detailMessage;
    }

    public void setDetailMessage(String detailMessage) {
        this.detailMessage = detailMessage;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}
