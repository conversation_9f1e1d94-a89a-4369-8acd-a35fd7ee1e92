package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@TypeDoc(
        description = "针对自营配送骑手，查询进行中的运单"
)
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
@ToString
@Data
public class QueryDeliveryOrderByPoiAndStatusListRequest {

    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "赋能门店 ID列表，最多查5个门店",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<Long> storeIdList;

    @FieldDoc(
            description = "配送状态列表",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public List<Integer> deliveryStatusList;

    public String validate() {

        if (tenantId == null || tenantId < 1L) {
            return "租户ID不合法";
        }
        if (CollectionUtils.isEmpty(storeIdList) || storeIdList.size() > 5) {
            return "赋能门店ID不合法";
        }
        if (CollectionUtils.isEmpty(deliveryStatusList)) {
            return "配送状态列表";
        }

        return null;
    }
}
