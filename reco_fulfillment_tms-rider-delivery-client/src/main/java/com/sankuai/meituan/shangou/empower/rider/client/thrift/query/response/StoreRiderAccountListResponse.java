package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrderMonitorInfo;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderMonitorInfo;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TStaffRider;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@FieldDoc(
        description = "查询门店下骑手名单（具有门店权限的店员/店长/兼职骑手）响应"
)
@ThriftStruct
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StoreRiderAccountListResponse {
    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status = Status.SUCCESS;

    @FieldDoc(
            description = "门店骑手名单",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<TStaffRider> riderList;

    public StoreRiderAccountListResponse(Status status) {
        this.status = status;
    }
}
