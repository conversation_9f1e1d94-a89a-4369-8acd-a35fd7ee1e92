package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "骑手信息"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class TStaffRider {

    @FieldDoc(
            description = "姓名"
    )
    @ThriftField(1)
    public String name;

    @FieldDoc(
            description = "电话"
    )
    @ThriftField(2)
    public String phone;

    @FieldDoc(
            description = "账号"
    )
    @ThriftField(3)
    public Long accountId;

    @FieldDoc(
            description = "账号类型",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    public Integer accountType;

    @FieldDoc(
            description = "角色列表",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    public List<Long> roleIdList;

}
