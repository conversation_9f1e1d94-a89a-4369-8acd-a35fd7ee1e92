package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/5/4 20:22
 **/
@ThriftStruct
public class TDeliveryProofPhotoInfo {
    private String url;

    private Integer auditStatus;

    private Long picId;


    @ThriftField(1)
    public String getUrl() {
        return this.url;
    }

    @ThriftField
    public void setUrl(String url) {
        this.url = url;
    }

    @ThriftField(2)
    public Integer getAuditStatus() {
        return this.auditStatus;
    }

    @ThriftField
    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    @ThriftField(3)
    public Long getPicId() {
        return this.picId;
    }

    @ThriftField
    public void setPicId(Long picId) {
        this.picId = picId;
    }
}