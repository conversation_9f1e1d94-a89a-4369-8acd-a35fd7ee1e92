package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.facebook.swift.codec.internal.coercion.ToThrift;
import com.facebook.swift.service.ThriftMethod;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import groovy.transform.Field;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2021/12/6
 *
 */
@TypeDoc(
        description = "门店骑手监控大屏请求"
)
@Data
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
public class StoreDeliveryMonitorRequest {
    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;


    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "appId",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Integer appId;

    public String validate() {

        if (tenantId == null || tenantId < 1L) {
            return "租户ID不合法";
        }
        if (storeId == null || storeId < 1L) {
            return "门店ID不合法";
        }
        if (appId == null || appId < 1L) {
            return "appId不合法";
        }

        return null;
    }

}
