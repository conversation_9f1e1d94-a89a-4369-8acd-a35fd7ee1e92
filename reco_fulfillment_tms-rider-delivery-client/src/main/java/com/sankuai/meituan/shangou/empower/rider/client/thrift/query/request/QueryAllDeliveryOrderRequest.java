package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@TypeDoc(
        description = "针对自营配送骑手，查询进行中的运单"
)
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
@ToString
@Data
public class QueryAllDeliveryOrderRequest {

    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "赋能门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "骑手账号 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Long riderAccountId;

    @FieldDoc(
            description = "骑手账号 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Long lastUpdateTime;

}
