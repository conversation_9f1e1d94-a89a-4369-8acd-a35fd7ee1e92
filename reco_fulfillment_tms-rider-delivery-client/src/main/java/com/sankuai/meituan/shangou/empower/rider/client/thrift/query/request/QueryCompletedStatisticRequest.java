package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * <AUTHOR>
 * @since 2024/3/19 15:28
 **/
@ThriftStruct
public class QueryCompletedStatisticRequest {
    /**
     * 格式 yyyy-MM-dd
     */
    private String date;

    private Long tenantId;

    private Long storeId;

    private Long riderAccountId;


    @ThriftField(1)
    public String getDate() {
        return this.date;
    }

    @ThriftField
    public void setDate(String date) {
        this.date = date;
    }

    @ThriftField(2)
    public Long getTenantId() {
        return this.tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(3)
    public Long getStoreId() {
        return this.storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(4)
    public Long getRiderAccountId() {
        return this.riderAccountId;
    }

    @ThriftField
    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    public String validate() {
        if (tenantId == null) {
            return "租户id不合法";
        }

        if (storeId == null) {
            return "门店id不合法";
        }

        if (riderAccountId == null) {
            return "骑手id不合法";
        }

        if (StringUtils.isBlank(date)) {
            return "日期不合法";
        }

        try {
            LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (DateTimeParseException e) {
            return "日期格式不合法";
        }

        return "";
    }
}
