package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/6 17:25
 **/

@ThriftStruct
public class TPricingRouteInfo {
    private Long deliveryOrderId;
    private String routeId;
    private List<TLocationCoordinate> polyline;

    private TLocationCoordinate origin;

    private TLocationCoordinate destination;

    private Integer distance;

    private Integer duration;


    @ThriftField(1)
    public Long getDeliveryOrderId() {
        return this.deliveryOrderId;
    }

    @ThriftField
    public void setDeliveryOrderId(Long deliveryOrderId) {
        this.deliveryOrderId = deliveryOrderId;
    }

    @ThriftField(2)
    public String getRouteId() {
        return this.routeId;
    }

    @ThriftField
    public void setRouteId(String routeId) {
        this.routeId = routeId;
    }

    @ThriftField(3)
    public List<TLocationCoordinate> getPolyline() {
        return this.polyline;
    }

    @ThriftField
    public void setPolyline(List<TLocationCoordinate> polyline) {
        this.polyline = polyline;
    }

    @ThriftField(4)
    public TLocationCoordinate getOrigin() {
        return this.origin;
    }

    @ThriftField
    public void setOrigin(TLocationCoordinate origin) {
        this.origin = origin;
    }

    @ThriftField(5)
    public TLocationCoordinate getDestination() {
        return this.destination;
    }

    @ThriftField
    public void setDestination(TLocationCoordinate destination) {
        this.destination = destination;
    }

    @ThriftField(6)
    public Integer getDistance() {
        return this.distance;
    }

    @ThriftField
    public void setDistance(Integer distance) {
        this.distance = distance;
    }


    @ThriftField(7)
    public Integer getDuration() {
        return this.duration;
    }

    @ThriftField
    public void setDuration(Integer duration) {
        this.duration = duration;
    }
}
