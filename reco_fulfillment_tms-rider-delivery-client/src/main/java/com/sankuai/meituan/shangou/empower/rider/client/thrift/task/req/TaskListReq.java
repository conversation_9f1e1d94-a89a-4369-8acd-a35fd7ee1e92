package com.sankuai.meituan.shangou.empower.rider.client.thrift.task.req;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.util.List;

/**
 * 异步任务列表查询请求参数
 *
 * @author: linjianyu
 * @since: 2019-07-30 Time: 11:34
 */
@TypeDoc(
        description = "异步任务列表查询请求",
        authors = {
                "linjianyu"
        }
)
@ThriftStruct
public class TaskListReq {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "任务类型列表",
            requiredness = Requiredness.REQUIRED
    )
    private List<Integer> taskTypes;

    @FieldDoc(
            description = "页码",
            requiredness = Requiredness.REQUIRED
    )
    private Integer page;

    @FieldDoc(
            description = "每页数量",
            requiredness = Requiredness.REQUIRED
    )
    private Integer pageSize;

    @FieldDoc(
            description = "开始时间(更新时间)",
            requiredness = Requiredness.REQUIRED
    )
    private Long beginTime;

    @FieldDoc(
            description = "结束时间(更新时间)",
            requiredness = Requiredness.REQUIRED
    )
    private Long endTime;

    @FieldDoc(
            description = "操作员id",
            requiredness = Requiredness.REQUIRED
    )
    private Long operatorId;

    @FieldDoc(
            description = "任务名称",
            requiredness = Requiredness.OPTIONAL
    )
    private String taskName;

    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long getTenantId() {
        return tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(value = 2, requiredness = ThriftField.Requiredness.REQUIRED)
    public List<Integer> getTaskTypes() {
        return taskTypes;
    }

    @ThriftField
    public void setTaskTypes(List<Integer> taskTypes) {
        this.taskTypes = taskTypes;
    }

    @ThriftField(value = 3, requiredness = ThriftField.Requiredness.REQUIRED)
    public Integer getPage() {
        return page;
    }

    @ThriftField
    public void setPage(Integer page) {
        this.page = page;
    }

    @ThriftField(value = 4, requiredness = ThriftField.Requiredness.REQUIRED)
    public Integer getPageSize() {
        return pageSize;
    }

    @ThriftField
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @ThriftField(value = 5, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long getBeginTime() {
        return beginTime;
    }

    @ThriftField
    public void setBeginTime(Long beginTime) {
        this.beginTime = beginTime;
    }

    @ThriftField(value = 6, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long getEndTime() {
        return endTime;
    }

    @ThriftField
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    @ThriftField(value = 7, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long getOperatorId() {
        return operatorId;
    }

    @ThriftField
    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    @ThriftField(value = 8, requiredness = ThriftField.Requiredness.OPTIONAL)
    public String getTaskName() {
        return taskName;
    }

    @ThriftField
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    // builder
    // ----------------------------------------------------------------------

    public static Builder builder(){
        return new Builder();
    }

    public static final class Builder {
        private Long tenantId;
        private List<Integer> taskTypes;
        private Integer page;
        private Integer pageSize;
        private Long beginTime;
        private Long endTime;
        private Long operatorId;
        private String taskName;

        private Builder() {
        }

        public static Builder aTaskListReq() {
            return new Builder();
        }

        public Builder tenantId(Long tenantId) {
            this.tenantId = tenantId;
            return this;
        }

        public Builder taskTypes(List<Integer> taskTypes) {
            this.taskTypes = taskTypes;
            return this;
        }

        public Builder page(Integer page) {
            this.page = page;
            return this;
        }

        public Builder pageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public Builder beginTime(Long beginTime) {
            this.beginTime = beginTime;
            return this;
        }

        public Builder endTime(Long endTime) {
            this.endTime = endTime;
            return this;
        }

        public Builder operatorId(Long operatorId) {
            this.operatorId = operatorId;
            return this;
        }

        public Builder taskName(String taskName) {
            this.taskName = taskName;
            return this;
        }

        public TaskListReq build() {
            TaskListReq taskListReq = new TaskListReq();
            taskListReq.setTenantId(tenantId);
            taskListReq.setTaskTypes(taskTypes);
            taskListReq.setPage(page);
            taskListReq.setPageSize(pageSize);
            taskListReq.setBeginTime(beginTime);
            taskListReq.setEndTime(endTime);
            taskListReq.setOperatorId(operatorId);
            taskListReq.setTaskName(taskName);
            return taskListReq;
        }
    }
}
