package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@ThriftStruct
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryDeliveryExceptionByChannelOrderRequest {

    @FieldDoc(
            description = "租户id"
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店id"
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "渠道订单列表"
    )
    @ThriftField(3)
    public List<ChannelOrderInfo> channelOrderInfoList;


    @ThriftStruct
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChannelOrderInfo {
        @FieldDoc(
                description = "渠道订单id列表"
        )
        @ThriftField(1)
        public String channelOrderId;

        @FieldDoc(
                description = "订单业务类型"
        )
        @ThriftField(2)
        public Integer orderBizType;
    }

    public String validate() {
        if (tenantId == null || tenantId <= 0L) {
            return "租户id不合法";
        }

        if (storeId == null || storeId <= 0L) {
            return "门店id不合法";
        }

        if (CollectionUtils.isEmpty(channelOrderInfoList)) {
            return "渠道订单列表不合法";
        }

        return null;
    }
}
