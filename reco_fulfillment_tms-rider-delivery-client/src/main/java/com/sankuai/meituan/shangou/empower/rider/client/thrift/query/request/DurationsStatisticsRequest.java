package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/8/24 15:26
 **/
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DurationsStatisticsRequest {
    @ThriftField(1)
    @FieldDoc(description = "租户id")
    public Long tenantId;

    @ThriftField(2)
    @FieldDoc(description = "骑手账号id")
    public Long riderAccountId;

    @ThriftField(3)
    @FieldDoc(description = "时间范围")
    public List<TDuration> durationList;

    @ThriftStruct
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TDuration {
        @ThriftField(1)
        @FieldDoc(description = "时间范围-开始日期 格式:yyyyMMdd")
        public String beginDate;

        @ThriftField(2)
        @FieldDoc(description = "时间范围-结束日期 格式:yyyyMMdd")
        public String endDate;
    }

    public void valid() {
        if (tenantId == null || tenantId <= 0L) {
            throw new IllegalArgumentException( "租户id不合法");
        }

        if (riderAccountId == null || riderAccountId <= 0L) {
            throw new IllegalArgumentException("骑手账号id不合法");
        }

        if (CollectionUtils.isEmpty(durationList)) {
            throw new IllegalArgumentException("时间范围不合法");
        }

        if (durationList.size() > 10) {
            throw new IllegalArgumentException("duration数量不能超过10");
        }


        for (TDuration duration : durationList) {
            if (duration == null || duration.beginDate == null || duration.endDate == null) {
                throw new IllegalArgumentException("时间范围不合法");
            }

            LocalDate beginDate;
            LocalDate endDate;

            try {
                beginDate = LocalDate.parse(duration.beginDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
                endDate = LocalDate.parse(duration.endDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
            } catch (Exception e) {
                throw new IllegalArgumentException("日期解析失败,时间范围不合法");
            }

            if (Math.abs(endDate.toEpochDay() - beginDate.toEpochDay()) > 93) {
                throw new IllegalArgumentException("时间范围不能超过3个月");
            }
        }

    }
}
