package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request;

import com.dianping.cat.util.StringUtils;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.netty.util.internal.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
		description = "骑手改派请求体"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@SuppressWarnings("DuplicatedCode")
public class RiderChangeRequest {

	@FieldDoc(
			description = "租户id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Long tenantId;

	@FieldDoc(
			description = "门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public Long storeId;

	@FieldDoc(
			description = "赋能统一订单号",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public Long orderId;

	@FieldDoc(
			description = "操作人id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(4)
	public Long operatorId;

	@FieldDoc(
			description = "转单后的骑手账号id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(5)
	public Long riderAccountId;

	@FieldDoc(
			description = "转单后的骑手名称",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(6)

	public String riderName;
	@FieldDoc(
			description = "转单后的骑手手机号",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(7)
	public String riderPhone;

	@FieldDoc(
			description = "转单后骑手电话号码加密token",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(value = 8, requiredness = ThriftField.Requiredness.OPTIONAL)
	public String riderPhoneToken;


	public String validate() {
		if (tenantId == null || tenantId <= 0L) {
			return "租户id不合法";
		}

		if (storeId == null || storeId <= 0L) {
			return "门店id不合法";
		}

		if (orderId == null || orderId <= 0L) {
			return "赋能订单id不合法";
		}

		if (riderAccountId == null || riderAccountId <= 0L) {
			return "转单后的骑手账号id不合法";
		}
		if (StringUtils.isBlank(riderName)) {
			return "转单后的骑手账号名称不合法";
		}
		if (StringUtils.isBlank(riderPhone)) {
			return "转单后的骑手手机号不合法";
		}

		if (operatorId == null || operatorId <= 0L) {
			return "操作人id不合法";
		}

		return null;
	}
}
