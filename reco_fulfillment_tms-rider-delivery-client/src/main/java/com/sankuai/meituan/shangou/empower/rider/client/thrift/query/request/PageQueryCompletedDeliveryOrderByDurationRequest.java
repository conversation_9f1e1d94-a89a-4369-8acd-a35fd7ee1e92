package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/3/18 15:25
 **/
@ThriftStruct
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageQueryCompletedDeliveryOrderByDurationRequest {

    @FieldDoc(
            description = "分页查询的页码",
            requiredness = Requiredness.REQUIRED

    )
    private Integer page;

    @FieldDoc(
            description = "分页查询的页面大小",
            requiredness = Requiredness.REQUIRED
    )
    private Integer pageSize;

    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "赋能门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "赋能门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long riderAccountId;

    @FieldDoc(
            description = "赋能门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long startTime;

    @FieldDoc(
            description = "赋能门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long endTime;

    @ThriftField(1)
    public Integer getPage() {
        return this.page;
    }

    @ThriftField
    public void setPage(Integer page) {
        this.page = page;
    }

    @ThriftField(2)
    public Integer getPageSize() {
        return this.pageSize;
    }

    @ThriftField
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @ThriftField(3)
    public Long getTenantId() {
        return this.tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(4)
    public Long getStoreId() {
        return this.storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(5)
    public Long getRiderAccountId() {
        return this.riderAccountId;
    }

    @ThriftField
    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    @ThriftField(6)
    public Long getStartTime() {
        return this.startTime;
    }

    @ThriftField
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    @ThriftField(7)
    public Long getEndTime() {
        return this.endTime;
    }

    @ThriftField
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public String validate() {
        if (page == null || page < 1 || pageSize == null || pageSize < 1 || pageSize > 100) {
            return "分页参数错误";
        }
        if (tenantId == null || tenantId < 1L) {
            return "租户ID不合法";
        }
        if (storeId == null || storeId < 1L) {
            return "赋能门店ID不合法";
        }
        if (riderAccountId == null || riderAccountId < 1L) {
            return "骑手id参数错误";
        }
        if (startTime == null || endTime == null || endTime < startTime) {
            return "开始时间或结束时间不合法";
        }

        if ((endTime - startTime) / 1000 / 60 / 60 / 24 > 3) {
            return "最多只能查3天之内的数据";
        }

        return null;
    }
}
