package com.sankuai.meituan.shangou.empower.rider.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.HashMap;
import java.util.Map;

/**
 * 配送运单状态枚举
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
public enum RiderDeliveryStatusEnum {

	/**
	 * 等待分配骑手，配送平台已接单
	 */
	WAITING_TO_ASSIGN_RIDER(30, "等待分配骑手"),

	/**
	 * 骑手已接单
	 */
	RIDER_ASSIGNED(40, "骑手已接单"),

	/**
	 * 骑手已取货
	 */
	RIDER_TAKEN_GOODS(50, "骑手已取货"),

	/**
	 * 配送完成，骑手已送达
	 */
	DELIVERY_DONE(60, "骑手已送达"),

	/**
	 * 配送已取消
	 */
	DELIVERY_CANCELLED(130, "配送已取消");


	private static final Map<Integer, RiderDeliveryStatusEnum> CODE_TO_ENUM_MAP = new HashMap<>();
	private final int code;
	private final String desc;

	static {
		for (RiderDeliveryStatusEnum each : values()) {
			CODE_TO_ENUM_MAP.put(each.getCode(), each);
		}
	}

	RiderDeliveryStatusEnum(int code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	@JsonValue
	public int getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}

	@JsonCreator
	public static RiderDeliveryStatusEnum enumOf(int code) {
		return CODE_TO_ENUM_MAP.get(code);
	}

	/**
	 * 从source状态是否可以到达目标状态，目前逻辑是以下两条：
	 * 1、DELIVERY_DONE和DELIVERY_CANCELLED为终态，因而当source为这两个状态时返回false；
	 * 2、当source为非终态，则source需要大于target。后续如果有新增非终态需要注意满足source < target这一逻辑，或者更新该方法
	 * @param source
	 * @param target
	 * @return
	 */
	public static boolean canTargetBeReachedFromSource(RiderDeliveryStatusEnum source, RiderDeliveryStatusEnum target) {
		if (source.equals(RiderDeliveryStatusEnum.DELIVERY_DONE)
				|| source.equals(RiderDeliveryStatusEnum.DELIVERY_CANCELLED)) {
			return false;
		}
		return source.getCode() < target.getCode();
	}
}
