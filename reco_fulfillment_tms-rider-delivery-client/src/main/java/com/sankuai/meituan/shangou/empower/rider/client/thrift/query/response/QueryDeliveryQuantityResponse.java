package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import java.util.Map;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 查询运单数量返回值.
 *
 * <AUTHOR>
 * @since 2021/6/16 16:57
 */
@TypeDoc(
        description = "针对自营配送骑手，查询运单数量返回值.",
        authors = {
                "liyang176"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@ToString
public class QueryDeliveryQuantityResponse {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    private Status status = Status.SUCCESS;

    @FieldDoc(
            description = "运单状态-数量 map",
            requiredness = Requiredness.REQUIRED
    )
    private Map<Integer, Integer> deliveryStatusQuantityMap;

    public QueryDeliveryQuantityResponse(Status status) {
        this.status = status;
    }

    public QueryDeliveryQuantityResponse(Map<Integer, Integer> deliveryStatusQuantityMap) {
        this.deliveryStatusQuantityMap = deliveryStatusQuantityMap;
    }

    @ThriftField(1)
    public Status getStatus() {
        return status;
    }

    @ThriftField
    public void setStatus(Status status) {
        this.status = status;
    }

    @ThriftField(2)
    public Map<Integer, Integer> getDeliveryStatusQuantityMap() {
        return deliveryStatusQuantityMap;
    }

    @ThriftField
    public void setDeliveryStatusQuantityMap(Map<Integer, Integer> deliveryStatusQuantityMap) {
        this.deliveryStatusQuantityMap = deliveryStatusQuantityMap;
    }
}
