package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/6 14:46
 **/
@ThriftStruct
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class QueryActivateDeliveryOrderCntDetailResponse {
    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    private Status status = Status.SUCCESS;

    @FieldDoc(
            description = "新任务-仅配送运单数量",
            requiredness = Requiredness.REQUIRED
    )
    private Integer newJustDeliveryOrderCnt;

    @FieldDoc(
            description = "新任务-仅配送运单列表",
            requiredness = Requiredness.REQUIRED
    )
    private List<ChannelOrderKey> newJustDeliveryOrderKeyList;

    @FieldDoc(
            description = "新任务-拣配一体运单数量",
            requiredness = Requiredness.REQUIRED
    )
    private Integer newPickDeliveryOrderCnt;

    @FieldDoc(
            description = "新任务-拣配一体运单列表",
            requiredness = Requiredness.REQUIRED
    )
    private List<ChannelOrderKey> newPickDeliveryOrderKeyList;

    @FieldDoc(
            description = "待取货运单数量",
            requiredness = Requiredness.REQUIRED
    )
    private Integer waitTakeDeliveryOrderCount;

    @FieldDoc(
            description = "待取货运单数量列表",
            requiredness = Requiredness.REQUIRED
    )
    private List<ChannelOrderKey> waitTakeDeliveryOrderKeyList;

    @FieldDoc(
            description = "配送中运单数量",
            requiredness = Requiredness.REQUIRED
    )
    private Integer inDeliveryOrderCount;

    @FieldDoc(
            description = "配送中运单列表",
            requiredness = Requiredness.REQUIRED
    )
    private List<ChannelOrderKey> inDeliveryOrderKeyList;

    public QueryActivateDeliveryOrderCntDetailResponse(Status status) {
        this.status = status;
    }


    @ThriftField(1)
    public Status getStatus() {
        return this.status;
    }

    @ThriftField
    public void setStatus(Status status) {
        this.status = status;
    }

    @ThriftField(2)
    public Integer getNewJustDeliveryOrderCnt() {
        return this.newJustDeliveryOrderCnt;
    }

    @ThriftField
    public void setNewJustDeliveryOrderCnt(Integer newJustDeliveryOrderCnt) {
        this.newJustDeliveryOrderCnt = newJustDeliveryOrderCnt;
    }

    @ThriftField(3)
    public List<ChannelOrderKey> getNewJustDeliveryOrderKeyList() {
        return this.newJustDeliveryOrderKeyList;
    }

    @ThriftField
    public void setNewJustDeliveryOrderKeyList(List<ChannelOrderKey> newJustDeliveryOrderKeyList) {
        this.newJustDeliveryOrderKeyList = newJustDeliveryOrderKeyList;
    }

    @ThriftField(4)
    public Integer getNewPickDeliveryOrderCnt() {
        return this.newPickDeliveryOrderCnt;
    }

    @ThriftField
    public void setNewPickDeliveryOrderCnt(Integer newPickDeliveryOrderCnt) {
        this.newPickDeliveryOrderCnt = newPickDeliveryOrderCnt;
    }

    @ThriftField(5)
    public List<ChannelOrderKey> getNewPickDeliveryOrderKeyList() {
        return this.newPickDeliveryOrderKeyList;
    }

    @ThriftField
    public void setNewPickDeliveryOrderKeyList(List<ChannelOrderKey> newPickDeliveryOrderKeyList) {
        this.newPickDeliveryOrderKeyList = newPickDeliveryOrderKeyList;
    }

    @ThriftField(6)
    public Integer getWaitTakeDeliveryOrderCount() {
        return this.waitTakeDeliveryOrderCount;
    }

    @ThriftField
    public void setWaitTakeDeliveryOrderCount(Integer waitTakeDeliveryOrderCount) {
        this.waitTakeDeliveryOrderCount = waitTakeDeliveryOrderCount;
    }

    @ThriftField(7)
    public List<ChannelOrderKey> getWaitTakeDeliveryOrderKeyList() {
        return this.waitTakeDeliveryOrderKeyList;
    }

    @ThriftField
    public void setWaitTakeDeliveryOrderKeyList(List<ChannelOrderKey> waitTakeDeliveryOrderKeyList) {
        this.waitTakeDeliveryOrderKeyList = waitTakeDeliveryOrderKeyList;
    }

    @ThriftField(8)
    public Integer getInDeliveryOrderCount() {
        return this.inDeliveryOrderCount;
    }

    @ThriftField
    public void setInDeliveryOrderCount(Integer inDeliveryOrderCount) {
        this.inDeliveryOrderCount = inDeliveryOrderCount;
    }

    @ThriftField(9)
    public List<ChannelOrderKey> getInDeliveryOrderKeyList() {
        return this.inDeliveryOrderKeyList;
    }

    @ThriftField
    public void setInDeliveryOrderKeyList(List<ChannelOrderKey> inDeliveryOrderKeyList) {
        this.inDeliveryOrderKeyList = inDeliveryOrderKeyList;
    }

    @ThriftStruct
    @EqualsAndHashCode
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ChannelOrderKey {
        private String channelOrderId;

        private int orderBizType;
        @ThriftField(1)
        public String getChannelOrderId() {
            return this.channelOrderId;
        }

        @ThriftField
        public void setChannelOrderId(String channelOrderId) {
            this.channelOrderId = channelOrderId;
        }

        @ThriftField(2)
        public int getOrderBizType() {
            return this.orderBizType;
        }

        @ThriftField
        public void setOrderBizType(int orderBizType) {
            this.orderBizType = orderBizType;
        }
    }
}
