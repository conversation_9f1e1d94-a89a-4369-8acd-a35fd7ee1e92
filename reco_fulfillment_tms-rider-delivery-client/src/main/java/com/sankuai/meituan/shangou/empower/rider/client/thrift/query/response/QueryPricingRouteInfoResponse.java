package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryRiskControlOrder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TPricingRouteInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/6 17:24
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryPricingRouteInfoResponse {
    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    private Status status = Status.SUCCESS;

    @FieldDoc(
            description = "定价路线列表",
            requiredness = Requiredness.REQUIRED
    )
    private List<TPricingRouteInfo> pricingRouteInfoList;
}
