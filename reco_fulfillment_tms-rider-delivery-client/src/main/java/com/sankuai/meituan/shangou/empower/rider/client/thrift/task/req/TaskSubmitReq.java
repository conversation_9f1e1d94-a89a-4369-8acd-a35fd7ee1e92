package com.sankuai.meituan.shangou.empower.rider.client.thrift.task.req;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

/**
 * 异步任务提交请求参数
 *
 * @author: linjianyu <br>
 * @date: 2019-07-30 Time: 11:34
 */
@TypeDoc(
        description = "异步任务提交请求",
        authors = {
                "linjianyu"
        }
)
@ThriftStruct
public class TaskSubmitReq {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "任务类型",
            requiredness = Requiredness.REQUIRED
    )
    private Integer taskType;

    @FieldDoc(
            description = "操作员id",
            requiredness = Requiredness.REQUIRED
    )
    private Long operatorId;

    @FieldDoc(
            description = "操作员账户",
            requiredness = Requiredness.REQUIRED
    )
    private String operatorAccount;

    @FieldDoc(
            description = "任务参数",
            requiredness = Requiredness.OPTIONAL
    )
    private String parameter;

    public TaskSubmitReq() {
    }

    @ThriftConstructor
    public TaskSubmitReq(Long tenantId, Integer taskType, Long operatorId, String operatorAccount, String parameter) {
        this.tenantId = tenantId;
        this.taskType = taskType;
        this.operatorId = operatorId;
        this.operatorAccount = operatorAccount;
        this.parameter = parameter;
    }

    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long getTenantId() {
        return tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(value = 2, requiredness = ThriftField.Requiredness.REQUIRED)
    public Integer getTaskType() {
        return taskType;
    }

    @ThriftField
    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    @ThriftField(value = 3, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long getOperatorId() {
        return operatorId;
    }

    @ThriftField
    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    @ThriftField(value = 4, requiredness = ThriftField.Requiredness.REQUIRED)
    public String getOperatorAccount() {
        return operatorAccount;
    }

    @ThriftField
    public void setOperatorAccount(String operatorAccount) {
        this.operatorAccount = operatorAccount;
    }

    @ThriftField(value = 5, requiredness = ThriftField.Requiredness.OPTIONAL)
    public String getParameter() {
        return parameter;
    }

    @ThriftField
    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    // builder
    // --------------------------------------------------------------------------------

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private Long tenantId;
        private Integer taskType;
        private Long operatorId;
        private String operatorAccount;
        private String parameter;

        private Builder() {
        }

        public static Builder aTaskSubmitReq() {
            return new Builder();
        }

        public Builder tenantId(Long tenantId) {
            this.tenantId = tenantId;
            return this;
        }

        public Builder taskType(Integer taskType) {
            this.taskType = taskType;
            return this;
        }

        public Builder operatorId(Long operatorId) {
            this.operatorId = operatorId;
            return this;
        }

        public Builder operatorAccount(String operatorAccount) {
            this.operatorAccount = operatorAccount;
            return this;
        }

        public Builder parameter(String parameter) {
            this.parameter = parameter;
            return this;
        }

        public TaskSubmitReq build() {
            TaskSubmitReq taskSubmitReq = new TaskSubmitReq();
            taskSubmitReq.setTenantId(tenantId);
            taskSubmitReq.setTaskType(taskType);
            taskSubmitReq.setOperatorId(operatorId);
            taskSubmitReq.setOperatorAccount(operatorAccount);
            taskSubmitReq.setParameter(parameter);
            return taskSubmitReq;
        }
    }
}
