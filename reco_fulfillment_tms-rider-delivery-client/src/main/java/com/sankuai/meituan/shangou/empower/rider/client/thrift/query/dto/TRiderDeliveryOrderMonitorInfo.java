package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/12/9
 * Desc 骑手监控大屏-运单信息监控专用DTO
 */
@Data
@ThriftStruct
public class TRiderDeliveryOrderMonitorInfo {
    @FieldDoc(
            description = "运单 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long deliveryOrderId;

    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long tenantId;

    @FieldDoc(
            description = "赋能门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Long storeId;

    @FieldDoc(
            description = "赋能订单 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Long orderId;

    @FieldDoc(
            description = "渠道订单 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public String channelOrderId;

    @FieldDoc(
            description = "订单业务类型Code，参照 com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    public Integer orderBizTypeCode;

    @FieldDoc(
            description = "预计送达时间",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    public Long estimatedDeliveryTime;

    @FieldDoc(
            description = "预计送达结束时间",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(8)
    public Long estimatedDeliveryEndTime;

    @FieldDoc(
            description = "运单状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(9)
    public Integer deliveryStatus;

    @FieldDoc(
            description = "收货人信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(10)
    public TReceiver receiver;

    @FieldDoc(
            description = "骑手信息",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(11)
    public TStaffRider riderInfo;

    @FieldDoc(
            description = "创建时间",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(12)
    public Long createTime;

    @FieldDoc(
            description = "订单日流水号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(13)
    public Integer daySeq;

    @FieldDoc(
            description = "运单当前状态对应的超时时间点,比如 【等待分配骑手】状态对应【领取超时时间点】",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(14)
    public Long currentStatusTimeoutStamp;

    @FieldDoc(
            description = "整单超时时间戳",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(15)
    public Long entireOrderTimeoutStamp;

    @FieldDoc(
            description = "整单超时时间戳",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(16)
    public Integer deliveryPlatformCode;

    @FieldDoc(
            description = "整单超时时间戳",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(17)
    public String deliveryChannelName;

    @FieldDoc(
            description = "考核送达时间",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(18)
    public Long assessDeliveryTime;

    @FieldDoc(
            description = "是否拣配分离",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(19)
    public Boolean pickDeliverySplitTag;

}
