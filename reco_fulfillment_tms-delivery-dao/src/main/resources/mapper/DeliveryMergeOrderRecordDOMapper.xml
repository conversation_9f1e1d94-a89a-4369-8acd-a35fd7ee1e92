<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryMergeOrderRecordDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryMergeOrderRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="delivery_order_id" jdbcType="BIGINT" property="deliveryOrderId" />
    <result column="merge_id" jdbcType="BIGINT" property="mergeId" />
    <result column="active_status" jdbcType="BIGINT" property="activeStatus" />
    <result column="channel_order_id" jdbcType="VARCHAR" property="channelOrderId" />
    <result column="order_biz_type" jdbcType="INTEGER" property="orderBizType" />
    <result column="rider_account_id" jdbcType="BIGINT" property="riderAccountId" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, delivery_order_id, merge_id, active_status, channel_order_id, order_biz_type, 
    rider_account_id, ext_info, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryMergeOrderRecordDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from delivery_merge_order_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from delivery_merge_order_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from delivery_merge_order_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryMergeOrderRecordDOExample">
    delete from delivery_merge_order_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryMergeOrderRecordDO">
    insert into delivery_merge_order_record (id, delivery_order_id, merge_id, 
      active_status, channel_order_id, order_biz_type, 
      rider_account_id, ext_info, create_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{deliveryOrderId,jdbcType=BIGINT}, #{mergeId,jdbcType=BIGINT}, 
      #{activeStatus,jdbcType=BIGINT}, #{channelOrderId,jdbcType=VARCHAR}, #{orderBizType,jdbcType=INTEGER}, 
      #{riderAccountId,jdbcType=BIGINT}, #{extInfo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryMergeOrderRecordDO">
    insert into delivery_merge_order_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deliveryOrderId != null">
        delivery_order_id,
      </if>
      <if test="mergeId != null">
        merge_id,
      </if>
      <if test="activeStatus != null">
        active_status,
      </if>
      <if test="channelOrderId != null">
        channel_order_id,
      </if>
      <if test="orderBizType != null">
        order_biz_type,
      </if>
      <if test="riderAccountId != null">
        rider_account_id,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="deliveryOrderId != null">
        #{deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="mergeId != null">
        #{mergeId,jdbcType=BIGINT},
      </if>
      <if test="activeStatus != null">
        #{activeStatus,jdbcType=BIGINT},
      </if>
      <if test="channelOrderId != null">
        #{channelOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderBizType != null">
        #{orderBizType,jdbcType=INTEGER},
      </if>
      <if test="riderAccountId != null">
        #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryMergeOrderRecordDOExample" resultType="java.lang.Long">
    select count(*) from delivery_merge_order_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update delivery_merge_order_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.deliveryOrderId != null">
        delivery_order_id = #{record.deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.mergeId != null">
        merge_id = #{record.mergeId,jdbcType=BIGINT},
      </if>
      <if test="record.activeStatus != null">
        active_status = #{record.activeStatus,jdbcType=BIGINT},
      </if>
      <if test="record.channelOrderId != null">
        channel_order_id = #{record.channelOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderBizType != null">
        order_biz_type = #{record.orderBizType,jdbcType=INTEGER},
      </if>
      <if test="record.riderAccountId != null">
        rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update delivery_merge_order_record
    set id = #{record.id,jdbcType=BIGINT},
      delivery_order_id = #{record.deliveryOrderId,jdbcType=BIGINT},
      merge_id = #{record.mergeId,jdbcType=BIGINT},
      active_status = #{record.activeStatus,jdbcType=BIGINT},
      channel_order_id = #{record.channelOrderId,jdbcType=VARCHAR},
      order_biz_type = #{record.orderBizType,jdbcType=INTEGER},
      rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryMergeOrderRecordDO">
    update delivery_merge_order_record
    <set>
      <if test="deliveryOrderId != null">
        delivery_order_id = #{deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="mergeId != null">
        merge_id = #{mergeId,jdbcType=BIGINT},
      </if>
      <if test="activeStatus != null">
        active_status = #{activeStatus,jdbcType=BIGINT},
      </if>
      <if test="channelOrderId != null">
        channel_order_id = #{channelOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderBizType != null">
        order_biz_type = #{orderBizType,jdbcType=INTEGER},
      </if>
      <if test="riderAccountId != null">
        rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryMergeOrderRecordDO">
    update delivery_merge_order_record
    set delivery_order_id = #{deliveryOrderId,jdbcType=BIGINT},
      merge_id = #{mergeId,jdbcType=BIGINT},
      active_status = #{activeStatus,jdbcType=BIGINT},
      channel_order_id = #{channelOrderId,jdbcType=VARCHAR},
      order_biz_type = #{orderBizType,jdbcType=INTEGER},
      rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>