<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderLogDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="delivery_order_id" jdbcType="BIGINT" property="deliveryOrderId" />
    <result column="change_type" jdbcType="INTEGER" property="changeType" />
    <result column="change_info" jdbcType="CHAR" property="changeInfo" />
    <result column="change_time" jdbcType="TIMESTAMP" property="changeTime" />
    <result column="delivery_status" jdbcType="INTEGER" property="deliveryStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, delivery_order_id, change_type, change_info, change_time, delivery_status
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from delivery_order_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from delivery_order_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from delivery_order_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDOExample">
    delete from delivery_order_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDO">
    insert into delivery_order_log (id, delivery_order_id, change_type, 
      change_info, change_time, delivery_status
      )
    values (#{id,jdbcType=BIGINT}, #{deliveryOrderId,jdbcType=BIGINT}, #{changeType,jdbcType=INTEGER}, 
      #{changeInfo,jdbcType=CHAR}, #{changeTime,jdbcType=TIMESTAMP}, #{deliveryStatus,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDO">
    insert into delivery_order_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deliveryOrderId != null">
        delivery_order_id,
      </if>
      <if test="changeType != null">
        change_type,
      </if>
      <if test="changeInfo != null">
        change_info,
      </if>
      <if test="changeTime != null">
        change_time,
      </if>
      <if test="deliveryStatus != null">
        delivery_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="deliveryOrderId != null">
        #{deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="changeType != null">
        #{changeType,jdbcType=INTEGER},
      </if>
      <if test="changeInfo != null">
        #{changeInfo,jdbcType=CHAR},
      </if>
      <if test="changeTime != null">
        #{changeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDOExample" resultType="java.lang.Long">
    select count(*) from delivery_order_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update delivery_order_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.deliveryOrderId != null">
        delivery_order_id = #{record.deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.changeType != null">
        change_type = #{record.changeType,jdbcType=INTEGER},
      </if>
      <if test="record.changeInfo != null">
        change_info = #{record.changeInfo,jdbcType=CHAR},
      </if>
      <if test="record.changeTime != null">
        change_time = #{record.changeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deliveryStatus != null">
        delivery_status = #{record.deliveryStatus,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update delivery_order_log
    set id = #{record.id,jdbcType=BIGINT},
      delivery_order_id = #{record.deliveryOrderId,jdbcType=BIGINT},
      change_type = #{record.changeType,jdbcType=INTEGER},
      change_info = #{record.changeInfo,jdbcType=CHAR},
      change_time = #{record.changeTime,jdbcType=TIMESTAMP},
      delivery_status = #{record.deliveryStatus,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDO">
    update delivery_order_log
    <set>
      <if test="deliveryOrderId != null">
        delivery_order_id = #{deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="changeType != null">
        change_type = #{changeType,jdbcType=INTEGER},
      </if>
      <if test="changeInfo != null">
        change_info = #{changeInfo,jdbcType=CHAR},
      </if>
      <if test="changeTime != null">
        change_time = #{changeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryStatus != null">
        delivery_status = #{deliveryStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDO">
    update delivery_order_log
    set delivery_order_id = #{deliveryOrderId,jdbcType=BIGINT},
      change_type = #{changeType,jdbcType=INTEGER},
      change_info = #{changeInfo,jdbcType=CHAR},
      change_time = #{changeTime,jdbcType=TIMESTAMP},
      delivery_status = #{deliveryStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>