<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.RiderArrivalLocationDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderArrivalLocationDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="delivery_order_id" jdbcType="BIGINT" property="deliveryOrderId" />
    <result column="rider_account_id" jdbcType="BIGINT" property="riderAccountId" />
    <result column="longitude" jdbcType="VARCHAR" property="longitude" />
    <result column="latitude" jdbcType="VARCHAR" property="latitude" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="linear_distance_to_receiver" jdbcType="BIGINT" property="linearDistanceToReceiver" />
    <result column="navigation_distance_to_receiver" jdbcType="BIGINT" property="navigationDistanceToReceiver" />
    <result column="os" jdbcType="VARCHAR" property="os" />
    <result column="bearing" jdbcType="VARCHAR" property="bearing" />
    <result column="speed" jdbcType="VARCHAR" property="speed" />
    <result column="accuracy" jdbcType="VARCHAR" property="accuracy" />
    <result column="provider" jdbcType="VARCHAR" property="provider" />
    <result column="locate_time" jdbcType="VARCHAR" property="locateTime" />
    <result column="is_cache_location" jdbcType="INTEGER" property="isCacheLocation" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, delivery_order_id, rider_account_id, longitude, latitude, create_time, update_time, 
    linear_distance_to_receiver, navigation_distance_to_receiver, os, bearing, speed, 
    accuracy, provider, locate_time, is_cache_location
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderArrivalLocationDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from rider_arrival_location
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rider_arrival_location
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rider_arrival_location
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderArrivalLocationDOExample">
    delete from rider_arrival_location
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderArrivalLocationDO">
    insert into rider_arrival_location (id, delivery_order_id, rider_account_id, 
      longitude, latitude, create_time, 
      update_time, linear_distance_to_receiver, 
      navigation_distance_to_receiver, os, bearing, 
      speed, accuracy, provider, 
      locate_time, is_cache_location)
    values (#{id,jdbcType=BIGINT}, #{deliveryOrderId,jdbcType=BIGINT}, #{riderAccountId,jdbcType=BIGINT}, 
      #{longitude,jdbcType=VARCHAR}, #{latitude,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{linearDistanceToReceiver,jdbcType=BIGINT}, 
      #{navigationDistanceToReceiver,jdbcType=BIGINT}, #{os,jdbcType=VARCHAR}, #{bearing,jdbcType=VARCHAR}, 
      #{speed,jdbcType=VARCHAR}, #{accuracy,jdbcType=VARCHAR}, #{provider,jdbcType=VARCHAR}, 
      #{locateTime,jdbcType=VARCHAR}, #{isCacheLocation,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderArrivalLocationDO">
    insert into rider_arrival_location
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deliveryOrderId != null">
        delivery_order_id,
      </if>
      <if test="riderAccountId != null">
        rider_account_id,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="latitude != null">
        latitude,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="linearDistanceToReceiver != null">
        linear_distance_to_receiver,
      </if>
      <if test="navigationDistanceToReceiver != null">
        navigation_distance_to_receiver,
      </if>
      <if test="os != null">
        os,
      </if>
      <if test="bearing != null">
        bearing,
      </if>
      <if test="speed != null">
        speed,
      </if>
      <if test="accuracy != null">
        accuracy,
      </if>
      <if test="provider != null">
        provider,
      </if>
      <if test="locateTime != null">
        locate_time,
      </if>
      <if test="isCacheLocation != null">
        is_cache_location,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="deliveryOrderId != null">
        #{deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="riderAccountId != null">
        #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="linearDistanceToReceiver != null">
        #{linearDistanceToReceiver,jdbcType=BIGINT},
      </if>
      <if test="navigationDistanceToReceiver != null">
        #{navigationDistanceToReceiver,jdbcType=BIGINT},
      </if>
      <if test="os != null">
        #{os,jdbcType=VARCHAR},
      </if>
      <if test="bearing != null">
        #{bearing,jdbcType=VARCHAR},
      </if>
      <if test="speed != null">
        #{speed,jdbcType=VARCHAR},
      </if>
      <if test="accuracy != null">
        #{accuracy,jdbcType=VARCHAR},
      </if>
      <if test="provider != null">
        #{provider,jdbcType=VARCHAR},
      </if>
      <if test="locateTime != null">
        #{locateTime,jdbcType=VARCHAR},
      </if>
      <if test="isCacheLocation != null">
        #{isCacheLocation,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderArrivalLocationDOExample" resultType="java.lang.Long">
    select count(*) from rider_arrival_location
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rider_arrival_location
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.deliveryOrderId != null">
        delivery_order_id = #{record.deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.riderAccountId != null">
        rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.longitude != null">
        longitude = #{record.longitude,jdbcType=VARCHAR},
      </if>
      <if test="record.latitude != null">
        latitude = #{record.latitude,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.linearDistanceToReceiver != null">
        linear_distance_to_receiver = #{record.linearDistanceToReceiver,jdbcType=BIGINT},
      </if>
      <if test="record.navigationDistanceToReceiver != null">
        navigation_distance_to_receiver = #{record.navigationDistanceToReceiver,jdbcType=BIGINT},
      </if>
      <if test="record.os != null">
        os = #{record.os,jdbcType=VARCHAR},
      </if>
      <if test="record.bearing != null">
        bearing = #{record.bearing,jdbcType=VARCHAR},
      </if>
      <if test="record.speed != null">
        speed = #{record.speed,jdbcType=VARCHAR},
      </if>
      <if test="record.accuracy != null">
        accuracy = #{record.accuracy,jdbcType=VARCHAR},
      </if>
      <if test="record.provider != null">
        provider = #{record.provider,jdbcType=VARCHAR},
      </if>
      <if test="record.locateTime != null">
        locate_time = #{record.locateTime,jdbcType=VARCHAR},
      </if>
      <if test="record.isCacheLocation != null">
        is_cache_location = #{record.isCacheLocation,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rider_arrival_location
    set id = #{record.id,jdbcType=BIGINT},
      delivery_order_id = #{record.deliveryOrderId,jdbcType=BIGINT},
      rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      longitude = #{record.longitude,jdbcType=VARCHAR},
      latitude = #{record.latitude,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      linear_distance_to_receiver = #{record.linearDistanceToReceiver,jdbcType=BIGINT},
      navigation_distance_to_receiver = #{record.navigationDistanceToReceiver,jdbcType=BIGINT},
      os = #{record.os,jdbcType=VARCHAR},
      bearing = #{record.bearing,jdbcType=VARCHAR},
      speed = #{record.speed,jdbcType=VARCHAR},
      accuracy = #{record.accuracy,jdbcType=VARCHAR},
      provider = #{record.provider,jdbcType=VARCHAR},
      locate_time = #{record.locateTime,jdbcType=VARCHAR},
      is_cache_location = #{record.isCacheLocation,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderArrivalLocationDO">
    update rider_arrival_location
    <set>
      <if test="deliveryOrderId != null">
        delivery_order_id = #{deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="riderAccountId != null">
        rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        latitude = #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="linearDistanceToReceiver != null">
        linear_distance_to_receiver = #{linearDistanceToReceiver,jdbcType=BIGINT},
      </if>
      <if test="navigationDistanceToReceiver != null">
        navigation_distance_to_receiver = #{navigationDistanceToReceiver,jdbcType=BIGINT},
      </if>
      <if test="os != null">
        os = #{os,jdbcType=VARCHAR},
      </if>
      <if test="bearing != null">
        bearing = #{bearing,jdbcType=VARCHAR},
      </if>
      <if test="speed != null">
        speed = #{speed,jdbcType=VARCHAR},
      </if>
      <if test="accuracy != null">
        accuracy = #{accuracy,jdbcType=VARCHAR},
      </if>
      <if test="provider != null">
        provider = #{provider,jdbcType=VARCHAR},
      </if>
      <if test="locateTime != null">
        locate_time = #{locateTime,jdbcType=VARCHAR},
      </if>
      <if test="isCacheLocation != null">
        is_cache_location = #{isCacheLocation,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderArrivalLocationDO">
    update rider_arrival_location
    set delivery_order_id = #{deliveryOrderId,jdbcType=BIGINT},
      rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      longitude = #{longitude,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      linear_distance_to_receiver = #{linearDistanceToReceiver,jdbcType=BIGINT},
      navigation_distance_to_receiver = #{navigationDistanceToReceiver,jdbcType=BIGINT},
      os = #{os,jdbcType=VARCHAR},
      bearing = #{bearing,jdbcType=VARCHAR},
      speed = #{speed,jdbcType=VARCHAR},
      accuracy = #{accuracy,jdbcType=VARCHAR},
      provider = #{provider,jdbcType=VARCHAR},
      locate_time = #{locateTime,jdbcType=VARCHAR},
      is_cache_location = #{isCacheLocation,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>