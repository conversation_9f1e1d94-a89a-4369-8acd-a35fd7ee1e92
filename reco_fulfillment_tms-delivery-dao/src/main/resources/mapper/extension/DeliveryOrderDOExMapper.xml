<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.DeliveryOrderDOExMapper">
    <select id="countThirdDeliveryOrderGroupByStatus" parameterType = "java.util.Map" resultType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.CountGroupExDO">
        /*+zebra:oi=true*/select delivery_status as groupColumn, count(0) as groupCount
        from delivery_order
        where tenant_id = ${tenantId} and store_id = ${storeId} and delivery_channel != 2
        and delivery_status in
        <foreach collection="statusList" item="item" separator="," open="(" close=")">
                ${item}
        </foreach>
        and active_status = 0
        group by delivery_status;
    </select>
</mapper>

