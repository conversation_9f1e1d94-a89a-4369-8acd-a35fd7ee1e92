<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.StoreConfigOpLogDOExMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreConfigOpLogDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="store_config_id" jdbcType="BIGINT" property="storeConfigId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="op_type" jdbcType="TINYINT" property="opType"/>
        <result column="op_info" jdbcType="VARCHAR" property="opInfo"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="operator_name" jdbcType="VARCHAR" property="operatorName"/>
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime"/>
    </resultMap>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into store_config_op_log (store_config_id, tenant_id, store_id,
        op_type, op_info, operator_id, operator_name)
        values
        <foreach collection="recordList" item="record" index="index" separator=",">
            (
            #{record.storeConfigId,jdbcType=BIGINT},
            #{record.tenantId,jdbcType=BIGINT},
            #{record.storeId,jdbcType=BIGINT},
            #{record.opType,jdbcType=TINYINT},
            #{record.opInfo,jdbcType=VARCHAR},
            #{record.operatorId,jdbcType=BIGINT},
            #{record.operatorName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="queryByCondition" resultMap="BaseResultMap">
        SELECT
        id, store_config_id, tenant_id, store_id, op_type,
        op_info, operator_id, operator_name, ctime
        FROM
        store_config_op_log
        <where>
            <if test="storeConfigId != null">
                AND store_config_id = #{storeConfigId,jdbcType=BIGINT}
            </if>
            <if test="startTime != null">
                AND ctime >= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                AND ctime &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY ctime DESC
    </select>

</mapper>
