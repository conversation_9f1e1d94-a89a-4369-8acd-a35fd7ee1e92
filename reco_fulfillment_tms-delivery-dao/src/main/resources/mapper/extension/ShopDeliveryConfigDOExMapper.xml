<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.ShopDeliveryConfigDOExMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="delivery_channel_id" jdbcType="INTEGER" property="deliveryChannelId" />
    <result column="delivery_channel_poi_code" jdbcType="VARCHAR" property="deliveryChannelPoiCode" />
    <result column="delivery_channel_poi_name" jdbcType="VARCHAR" property="deliveryChannelPoiName" />
    <result column="ext_data" jdbcType="VARCHAR" property="extData" />
    <result column="enabled" jdbcType="TINYINT" property="enabled" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="TINYINT" property="isDel" />
    <result column="delivery_service_codes" jdbcType="VARCHAR" property="deliveryServiceCodes" />
    <result column="delivery_strategy" jdbcType="TINYINT" property="deliveryStrategy" />
    <result column="delivery_strategy_config" jdbcType="CHAR" property="deliveryStrategyConfig" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, shop_id, delivery_channel_id, delivery_channel_poi_code, delivery_channel_poi_name,
    ext_data, enabled, operator_id, operator_name, create_time, update_time, is_del,
    delivery_service_codes, delivery_strategy, delivery_strategy_config
  </sql>

  <select id="queryShopDeliveryConfigByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from shop_delivery_config
    where is_del = 0
    <if test="condition.tenantId != null">
      and tenant_id = #{condition.tenantId,jdbcType=BIGINT}
    </if>
    <if test="condition.shopId != null and condition.shopId != 0">
      and shop_id = #{condition.shopId,jdbcType=BIGINT}
    </if>
    <if test="condition.shopIdList != null and condition.shopIdList.size > 0">
      and shop_id in
      <foreach collection="condition.shopIdList" separator="," open="(" close=")" item="shopId">
        #{shopId}
      </foreach>
    </if>
    <if test="condition.deliveryChannelIdList != null and condition.deliveryChannelIdList.size > 0">
      and delivery_channel_id in
      <foreach collection="condition.deliveryChannelIdList" separator="," open="(" close=")" item="deliveryChannelId">
        #{deliveryChannelId}
      </foreach>
    </if>
    <if test="condition.enabled != null">
      and enabled = #{condition.enabled}
    </if>
  </select>

  <update id="batchUpdate">
    <foreach collection="recordList" item="shopDeliveryConfigDO" index="index" open="" close="" separator=";">
      update shop_delivery_config
      <set>
        <if test="shopDeliveryConfigDO.deliveryChannelPoiCode != null">
          delivery_channel_poi_code = #{shopDeliveryConfigDO.deliveryChannelPoiCode,jdbcType=VARCHAR},
        </if>
        <if test="shopDeliveryConfigDO.deliveryChannelPoiName != null">
          delivery_channel_poi_name = #{shopDeliveryConfigDO.deliveryChannelPoiName,jdbcType=VARCHAR},
        </if>
        <if test="shopDeliveryConfigDO.extData != null">
          ext_data = #{shopDeliveryConfigDO.extData,jdbcType=TINYINT},
        </if>
        <if test="shopDeliveryConfigDO.enabled != null">
          enabled = #{shopDeliveryConfigDO.enabled,jdbcType=VARCHAR},
        </if>
        <if test="shopDeliveryConfigDO.operatorId">
          operator_id = #{shopDeliveryConfigDO.operatorId,jdbcType=VARCHAR},
        </if>
        <if test="shopDeliveryConfigDO.operatorName">
          operator_name = #{shopDeliveryConfigDO.operatorName,jdbcType=VARCHAR},
        </if>
        <if test="shopDeliveryConfigDO.deliveryServiceCodes">
          delivery_service_codes = #{shopDeliveryConfigDO.deliveryServiceCodes, jdbcType=VARCHAR},
        </if>
        <if test="shopDeliveryConfigDO.isDel">
          is_del = #{shopDeliveryConfigDO.isDel,jdbcType=VARCHAR},
        </if>
        update_time = now()
      </set>
      where tenant_id = #{shopDeliveryConfigDO.tenantId}
      and shop_id = #{shopDeliveryConfigDO.shopId}
      and delivery_channel_id = #{shopDeliveryConfigDO.deliveryChannelId,jdbcType=INTEGER}
      and is_del = 0
    </foreach>
  </update>

  <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into shop_delivery_config
    (tenant_id, shop_id, delivery_channel_id, delivery_channel_poi_code, delivery_channel_poi_name, delivery_service_codes,
    ext_data, enabled, operator_id, operator_name, is_del)
    values
    <foreach collection="recordList" item="shopDeliveryConfigDO" index="index" separator=",">
      (#{shopDeliveryConfigDO.tenantId,jdbcType=BIGINT},
      #{shopDeliveryConfigDO.shopId,jdbcType=BIGINT},
      #{shopDeliveryConfigDO.deliveryChannelId,jdbcType=INTEGER},
      #{shopDeliveryConfigDO.deliveryChannelPoiCode,jdbcType=VARCHAR},
      #{shopDeliveryConfigDO.deliveryChannelPoiName,jdbcType=VARCHAR},
      #{shopDeliveryConfigDO.deliveryServiceCodes,jdbcType=VARCHAR},
      #{shopDeliveryConfigDO.extData,jdbcType=VARCHAR},
      #{shopDeliveryConfigDO.enabled,jdbcType=TINYINT},
      #{shopDeliveryConfigDO.operatorId,jdbcType=BIGINT},
      #{shopDeliveryConfigDO.operatorName,jdbcType=VARCHAR},
      0)
    </foreach>
  </insert>

  <update id="batchDelete">
      update shop_delivery_config
      set is_del = 1, operator_id = #{operatorId}, operator_name = #{operatorName}
      where tenant_id = #{tenantId}
      and shop_id = #{storeId}
      and is_del = 0
  </update>

  <select id="selectByExampleSlave" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDOExample" resultMap="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.ShopDeliveryConfigDOMapper.BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.ShopDeliveryConfigDOMapper.Base_Column_List" />
    from shop_delivery_config
    <if test="_parameter != null">
      <include refid="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.ShopDeliveryConfigDOMapper.Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
</mapper>
