<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.RiderDeliveryExceptionDOExMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
        <result column="store_id" jdbcType="BIGINT" property="storeId" />
        <result column="delivery_order_id" jdbcType="BIGINT" property="deliveryOrderId" />
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
        <result column="channel_order_id" jdbcType="VARCHAR" property="channelOrderId" />
        <result column="order_biz_type" jdbcType="INTEGER" property="orderBizType" />
        <result column="day_seq" jdbcType="INTEGER" property="daySeq" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="rider_name" jdbcType="VARCHAR" property="riderName" />
        <result column="rider_account_id" jdbcType="BIGINT" property="riderAccountId" />
        <result column="report_exception_type" jdbcType="INTEGER" property="reportExceptionType" />
        <result column="report_exception_sub_type" jdbcType="INTEGER" property="reportExceptionSubType" />
        <result column="report_exception_description" jdbcType="VARCHAR" property="reportExceptionDescription" />
    </resultMap>

    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <sql id="Base_Column_List">
        id, tenant_id, store_id, delivery_order_id, pay_time, channel_order_id, order_biz_type,
        day_seq, create_time, update_time, rider_name, rider_account_id, report_exception_type,
        report_exception_sub_type, report_exception_description
    </sql>

    <select id="selectDeliveryOrderIdByExampleDistinct" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDOExample" resultType="java.lang.Long">
        select
        distinct
        delivery_order_id
        from delivery_order_exception_report
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>

    <select id="selectCountByDistinctDeliveryOrderId" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDOExample" resultType="java.lang.Long">
        select
        count(distinct delivery_order_id)
        from delivery_order_exception_report
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>
</mapper>