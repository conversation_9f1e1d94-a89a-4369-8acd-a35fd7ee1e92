<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.DeliveryConfigDOExMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryConfigDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="delivery_channel_id" jdbcType="INTEGER" property="deliveryChannelId"/>
        <result column="app_key" jdbcType="VARCHAR" property="appKey"/>
        <result column="secret_key" jdbcType="VARCHAR" property="secretKey"/>
        <result column="ext_data" jdbcType="VARCHAR" property="extData"/>
        <result column="enabled" jdbcType="TINYINT" property="enabled"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="operator_name" jdbcType="VARCHAR" property="operatorName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_del" jdbcType="TINYINT" property="isDel"/>
    </resultMap>
    <update id="batchUpdate">
        <foreach collection="recordList" item="deliveryConfigDO" index="index" open="" close="" separator=";">
            update delivery_config
            <set>
                <if test="deliveryConfigDO.appKey != null">
                    app_key = #{deliveryConfigDO.appKey,jdbcType=VARCHAR},
                </if>
                <if test="deliveryConfigDO.secretKey != null">
                    secret_key = #{deliveryConfigDO.secretKey,jdbcType=VARCHAR},
                </if>
                <if test="deliveryConfigDO.enabled != null">
                    enabled = #{deliveryConfigDO.enabled,jdbcType=TINYINT},
                </if>
                <if test="deliveryConfigDO.extData != null">
                    ext_data = #{deliveryConfigDO.extData,jdbcType=VARCHAR},
                </if>
                <if test="deliveryConfigDO.operatorId != null">
                    operator_id = #{deliveryConfigDO.operatorId,jdbcType=BIGINT},
                </if>
                <if test="deliveryConfigDO.operatorName != null">
                    operator_name = #{deliveryConfigDO.operatorName,jdbcType=VARCHAR},
                </if>
                update_time = now()
            </set>
            where id = #{deliveryConfigDO.id}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into delivery_config
        (tenant_id, delivery_channel_id, app_key, secret_key, ext_data, enabled, operator_id, operator_name)
        values
        <foreach collection="recordList" item="deliveryConfigDO" index="index" separator=",">
            (#{deliveryConfigDO.tenantId,jdbcType=BIGINT},
            #{deliveryConfigDO.deliveryChannelId,jdbcType=TINYINT},
            #{deliveryConfigDO.appKey,jdbcType=VARCHAR},
            #{deliveryConfigDO.secretKey,jdbcType=VARCHAR},
            #{deliveryConfigDO.extData,jdbcType=VARCHAR},
            #{deliveryConfigDO.enabled,jdbcType=TINYINT},
            #{deliveryConfigDO.operatorId,jdbcType=BIGINT},
            #{deliveryConfigDO.operatorName,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="selectByExampleSlave" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryConfigDOExample" resultMap="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryConfigDOMapper.BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryConfigDOMapper.Base_Column_List" />
        from delivery_config
        <if test="_parameter != null">
            <include refid="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryConfigDOMapper.Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
</mapper>
