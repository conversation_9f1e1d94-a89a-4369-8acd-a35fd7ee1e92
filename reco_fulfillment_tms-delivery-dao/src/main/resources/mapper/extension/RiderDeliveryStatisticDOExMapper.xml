<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.RiderDeliveryStatisticDOExMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="rider_account_id" jdbcType="BIGINT" property="riderAccountId" />
    <result column="delivered" jdbcType="INTEGER" property="delivered" />
    <result column="cancel_before_delivered" jdbcType="INTEGER" property="cancelBeforeDelivered" />
    <result column="timeout" jdbcType="INTEGER" property="timeout" />
    <result column="delivered_exclude_booking" jdbcType="INTEGER" property="deliveredExcludeBooking" />
    <result column="delivered_in_25_mins" jdbcType="INTEGER" property="deliveredIn25Mins" />
    <result column="delivery_duration" jdbcType="BIGINT" property="deliveryDuration" />
    <result column="early_click" jdbcType="INTEGER" property="earlyClick" />
    <result column="dt" jdbcType="BIGINT" property="dt" />
  </resultMap>

<!--  只要能查到一条数据 就认为当天数据已到岗-->
  <select id="checkDataIsReady" resultType="boolean">
    select count(dt) > 0 from rider_delivery_statistics where dt = #{dt} limit 1;
  </select>

  <select id="queryStoreDeliveringOrderCount" parameterType="java.util.Map" resultType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.CountGroupExDO">
    select store_id as groupColumn, count(0) as groupCount from delivery_order where
    store_id in
    <foreach collection="storeIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
    and delivery_status in
    <foreach collection="status" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
    group by store_id;
  </select>

</mapper>