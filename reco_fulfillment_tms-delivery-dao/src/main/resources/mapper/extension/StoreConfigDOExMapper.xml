<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.StoreConfigDOExMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreConfigDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="address" jdbcType="CHAR" property="address"/>
        <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="delivery_strategy" jdbcType="TINYINT" property="deliveryStrategy"/>
        <result column="delivery_strategy_config" jdbcType="CHAR" property="deliveryStrategyConfig"/>
        <result column="delivery_launch_point" jdbcType="TINYINT" property="deliveryLaunchPoint"/>
        <result column="delivery_launch_delay_minutes" jdbcType="INTEGER" property="deliveryLaunchDelayMinutes"/>
        <result column="order_platform_delivery_config" jdbcType="CHAR" property="orderPlatformDeliveryConfig"/>
        <result column="enabled" jdbcType="TINYINT" property="enabled"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert ignore into store_config (tenant_id, store_id, address,
        contact_phone, delivery_strategy, delivery_strategy_config,
        delivery_launch_point, delivery_launch_delay_minutes,
        order_platform_delivery_config, enabled, is_auto_launch, delivery_launch_rule,
        open_aggr_platform, city_code, channel_type, last_platform_type)
        values
        <foreach collection="recordList" item="record" index="index" separator=",">
            (#{record.tenantId,jdbcType=BIGINT},
            #{record.storeId,jdbcType=BIGINT},
            #{record.address,jdbcType=CHAR},
            #{record.contactPhone,jdbcType=VARCHAR},
            #{record.deliveryStrategy,jdbcType=TINYINT},
            #{record.deliveryStrategyConfig,jdbcType=CHAR},
            #{record.deliveryLaunchPoint,jdbcType=TINYINT},
            #{record.deliveryLaunchDelayMinutes,jdbcType=INTEGER},
            #{record.orderPlatformDeliveryConfig,jdbcType=CHAR},
            #{record.enabled,jdbcType=TINYINT},
            #{record.is_auto_launch,jdbcType=TINYINT},
            #{record.delivery_launch_rule,jdbcType=TINYINT},
            #{record.open_aggr_platform,jdbcType=TINYINT},
            #{record.city_code,jdbcType=INTEGER},
            #{record.channel_type,jdbcType=INTEGER},
            #{record.last_platform_type,jdbcType=INTEGER})
        </foreach>
    </insert>

    <update id="batchUpdate">
        <foreach collection="recordList" item="record" separator=";">
            update store_config
            <set>
                delivery_launch_point = if(#{record.deliveryLaunchPoint} is null, delivery_launch_point, #{record.deliveryLaunchPoint})
            </set>
            <where>
                id = #{record.id}
                and
                channel_type = #{record.channelType}
            </where>
        </foreach>
    </update>

    <select id="selectByExampleSlave" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreConfigDOExample" resultMap="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.StoreConfigDOMapper.BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.StoreConfigDOMapper.Base_Column_List" />
        from store_config
        <if test="_parameter != null">
            <include refid="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.StoreConfigDOMapper.Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>

    <insert id="insertSelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreConfigDO" useGeneratedKeys="true" keyProperty="id">
        insert into store_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="storeId != null">
                store_id,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="contactPhone != null">
                contact_phone,
            </if>
            <if test="deliveryStrategy != null">
                delivery_strategy,
            </if>
            <if test="deliveryStrategyConfig != null">
                delivery_strategy_config,
            </if>
            <if test="deliveryLaunchPoint != null">
                delivery_launch_point,
            </if>
            <if test="deliveryLaunchDelayMinutes != null">
                delivery_launch_delay_minutes,
            </if>
            <if test="bookingOrderDeliveryLaunchPoint != null">
                booking_order_delivery_launch_point,
            </if>
            <if test="bookingOrderDeliveryLaunchMinutes != null">
                booking_order_delivery_launch_minutes,
            </if>
            <if test="orderPlatformDeliveryConfig != null">
                order_platform_delivery_config,
            </if>
            <if test="enabled != null">
                enabled,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isAutoLaunch != null">
                is_auto_launch,
            </if>
            <if test="deliveryLaunchRule != null">
                delivery_launch_rule,
            </if>
            <if test="openAggrPlatform != null">
                open_aggr_platform,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            <if test="channelType != null">
                channel_type,
            </if>
            <if test="lastPlatformType != null">
                last_platform_type,
            </if>
            <if test="isShowItemNumber != null">
                is_show_item_number,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="storeId != null">
                #{storeId,jdbcType=BIGINT},
            </if>
            <if test="address != null">
                #{address,jdbcType=CHAR},
            </if>
            <if test="contactPhone != null">
                #{contactPhone,jdbcType=VARCHAR},
            </if>
            <if test="deliveryStrategy != null">
                #{deliveryStrategy,jdbcType=TINYINT},
            </if>
            <if test="deliveryStrategyConfig != null">
                #{deliveryStrategyConfig,jdbcType=CHAR},
            </if>
            <if test="deliveryLaunchPoint != null">
                #{deliveryLaunchPoint,jdbcType=TINYINT},
            </if>
            <if test="deliveryLaunchDelayMinutes != null">
                #{deliveryLaunchDelayMinutes,jdbcType=INTEGER},
            </if>
            <if test="bookingOrderDeliveryLaunchPoint != null">
                #{bookingOrderDeliveryLaunchPoint,jdbcType=TINYINT},
            </if>
            <if test="bookingOrderDeliveryLaunchMinutes != null">
                #{bookingOrderDeliveryLaunchMinutes,jdbcType=INTEGER},
            </if>
            <if test="orderPlatformDeliveryConfig != null">
                #{orderPlatformDeliveryConfig,jdbcType=CHAR},
            </if>
            <if test="enabled != null">
                #{enabled,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isAutoLaunch != null">
                #{isAutoLaunch,jdbcType=TINYINT},
            </if>
            <if test="deliveryLaunchRule != null">
                #{deliveryLaunchRule,jdbcType=TINYINT},
            </if>
            <if test="openAggrPlatform != null">
                #{openAggrPlatform,jdbcType=TINYINT},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=INTEGER},
            </if>
            <if test="channelType != null">
                #{channelType,jdbcType=INTEGER},
            </if>
            <if test="lastPlatformType != null">
                #{lastPlatformType,jdbcType=INTEGER},
            </if>
            <if test="isShowItemNumber != null">
                #{isShowItemNumber,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

</mapper>
