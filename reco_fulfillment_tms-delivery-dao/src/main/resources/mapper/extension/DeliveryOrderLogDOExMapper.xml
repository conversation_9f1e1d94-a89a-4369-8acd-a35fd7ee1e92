<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.DeliveryOrderLogDOExMapper">
    <select id="selectByExampleSlave" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDOExample" resultMap="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderLogDOMapper.BaseResultMap">
        /*+zebra:oi=true*/select
        <if test="distinct">
            distinct
        </if>
        <include refid="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderLogDOMapper.Base_Column_List" />
        from delivery_order_log
        <if test="_parameter != null">
            <include refid="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderLogDOMapper.Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
</mapper>