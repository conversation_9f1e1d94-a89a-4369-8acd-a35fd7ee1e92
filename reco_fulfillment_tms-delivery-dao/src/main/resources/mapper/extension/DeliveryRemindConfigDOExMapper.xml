<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.DeliveryRemindConfigDOExMapper">

    <insert id="insertSelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRemindConfigDO" useGeneratedKeys="true" keyProperty="id">
        insert into delivery_remind_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="storeId != null">
                store_id,
            </if>
            <if test="recipients != null">
                recipients,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleteTime != null">
                delete_time,
            </if>
            <if test="enterpriseRecipients != null">
                enterprise_recipients,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="storeId != null">
                #{storeId,jdbcType=BIGINT},
            </if>
            <if test="recipients != null">
                #{recipients,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteTime != null">
                #{deleteTime,jdbcType=BIGINT},
            </if>
            <if test="enterpriseRecipients != null">
                #{enterpriseRecipients,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

</mapper>