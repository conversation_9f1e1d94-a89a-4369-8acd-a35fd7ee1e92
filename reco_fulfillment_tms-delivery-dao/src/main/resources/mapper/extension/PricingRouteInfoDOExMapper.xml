<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.PricingRouteInfoDOExMapper">

    <insert id="insertSelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.PricingRouteInfoDO" useGeneratedKeys="true" keyProperty="id">
        insert into pricing_route_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="storeId != null">
                store_id,
            </if>
            <if test="deliveryOrderId != null">
                delivery_order_id,
            </if>
            <if test="routeId != null">
                route_id,
            </if>
            <if test="origin != null">
                origin,
            </if>
            <if test="destination != null">
                destination,
            </if>
            <if test="distance != null">
                distance,
            </if>
            <if test="duration != null">
                duration,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="polyline != null">
                polyline,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="storeId != null">
                #{storeId,jdbcType=BIGINT},
            </if>
            <if test="deliveryOrderId != null">
                #{deliveryOrderId,jdbcType=BIGINT},
            </if>
            <if test="routeId != null">
                #{routeId,jdbcType=VARCHAR},
            </if>
            <if test="origin != null">
                #{origin,jdbcType=VARCHAR},
            </if>
            <if test="destination != null">
                #{destination,jdbcType=VARCHAR},
            </if>
            <if test="distance != null">
                #{distance,jdbcType=BIGINT},
            </if>
            <if test="duration != null">
                #{duration,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="polyline != null">
                #{polyline,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

</mapper>