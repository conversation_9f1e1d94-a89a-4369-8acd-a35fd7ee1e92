<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.DeliveryTaskFrameWorkMapper">
    <resultMap id="taskInfoBaseMap" type="com.sankuai.meituan.shangou.empower.task.dao.dbo.TaskInfoDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="task_type" jdbcType="INTEGER" property="taskType"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="task_status" jdbcType="INTEGER" property="taskStatus"/>
        <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="total_num" jdbcType="BIGINT" property="totalNum"/>
        <result column="success_num" jdbcType="BIGINT" property="successNum"/>
        <result column="failed_num" jdbcType="BIGINT" property="failedNum"/>
        <result column="execute_param" jdbcType="VARCHAR" property="executeParam"/>
        <result column="execute_result" jdbcType="VARCHAR" property="executeResult"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="operator_account" jdbcType="VARCHAR" property="operatorAccount"/>
        <result column="retry_times" jdbcType="VARCHAR" property="retryTimes"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="env" jdbcType="VARCHAR" property="env"/>
    </resultMap>

    <resultMap id="taskResultDetailBaseMap" type="com.sankuai.meituan.shangou.empower.task.dao.dbo.TaskResultDetailDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="data_type" jdbcType="VARCHAR" property="dataType"/>
        <result column="data_id" jdbcType="VARCHAR" property="dataId"/>
        <result column="ext_data" jdbcType="VARCHAR" property="extData"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="taskCountBaseMap" type="com.sankuai.meituan.shangou.empower.task.dao.dbo.TaskCountDO">
        <result column="unique_key" jdbcType="VARCHAR" property="uniqueKey"/>
        <result column="task_id_list" jdbcType="VARCHAR" property="taskIdList"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="env" jdbcType="VARCHAR" property="env"/>
    </resultMap>


    <sql id="task_info_col">
        id,tenant_id,task_type,task_name,task_status,begin_time,end_time,total_num,success_num,failed_num,
        execute_param,execute_result,operator_id,operator_account,retry_times,create_time,update_time,env
    </sql>

    <select id="queryRunningAndWaitingTaskByDuplicateKey" resultMap="taskInfoBaseMap">

        select
        <include refid="task_info_col"/>
        from task_info
        where create_time between #{createBeginTime} and #{createEndTime}
        and tenant_id=#{tenantId}
        and task_type=#{taskType}
        <if test="taskStatusList!=null and taskStatusList.size()>0">
            and task_status in
            <foreach collection="taskStatusList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="executeParam!=null">
            and execute_param=#{executeParam}
        </if>
        and operator_id=#{operatorId}
        <if test="env!=null">
            and env=#{env}
        </if>
        order by create_time
    </select>

</mapper>