<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.ShopDeliveryConfigDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="delivery_channel_id" jdbcType="INTEGER" property="deliveryChannelId" />
    <result column="delivery_channel_poi_code" jdbcType="VARCHAR" property="deliveryChannelPoiCode" />
    <result column="delivery_channel_poi_name" jdbcType="VARCHAR" property="deliveryChannelPoiName" />
    <result column="ext_data" jdbcType="VARCHAR" property="extData" />
    <result column="enabled" jdbcType="TINYINT" property="enabled" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="TINYINT" property="isDel" />
    <result column="delivery_service_codes" jdbcType="VARCHAR" property="deliveryServiceCodes" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, shop_id, delivery_channel_id, delivery_channel_poi_code, delivery_channel_poi_name, 
    ext_data, enabled, operator_id, operator_name, create_time, update_time, is_del, 
    delivery_service_codes
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from shop_delivery_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from shop_delivery_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from shop_delivery_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDOExample">
    delete from shop_delivery_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDO">
    insert into shop_delivery_config (id, tenant_id, shop_id, 
      delivery_channel_id, delivery_channel_poi_code, 
      delivery_channel_poi_name, ext_data, enabled, 
      operator_id, operator_name, create_time, 
      update_time, is_del, delivery_service_codes
      )
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, 
      #{deliveryChannelId,jdbcType=INTEGER}, #{deliveryChannelPoiCode,jdbcType=VARCHAR}, 
      #{deliveryChannelPoiName,jdbcType=VARCHAR}, #{extData,jdbcType=VARCHAR}, #{enabled,jdbcType=TINYINT}, 
      #{operatorId,jdbcType=BIGINT}, #{operatorName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=TINYINT}, #{deliveryServiceCodes,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDO">
    insert into shop_delivery_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="deliveryChannelId != null">
        delivery_channel_id,
      </if>
      <if test="deliveryChannelPoiCode != null">
        delivery_channel_poi_code,
      </if>
      <if test="deliveryChannelPoiName != null">
        delivery_channel_poi_name,
      </if>
      <if test="extData != null">
        ext_data,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
      <if test="deliveryServiceCodes != null">
        delivery_service_codes,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="deliveryChannelId != null">
        #{deliveryChannelId,jdbcType=INTEGER},
      </if>
      <if test="deliveryChannelPoiCode != null">
        #{deliveryChannelPoiCode,jdbcType=VARCHAR},
      </if>
      <if test="deliveryChannelPoiName != null">
        #{deliveryChannelPoiName,jdbcType=VARCHAR},
      </if>
      <if test="extData != null">
        #{extData,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=TINYINT},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=TINYINT},
      </if>
      <if test="deliveryServiceCodes != null">
        #{deliveryServiceCodes,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDOExample" resultType="java.lang.Long">
    select count(*) from shop_delivery_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update shop_delivery_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=BIGINT},
      </if>
      <if test="record.deliveryChannelId != null">
        delivery_channel_id = #{record.deliveryChannelId,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryChannelPoiCode != null">
        delivery_channel_poi_code = #{record.deliveryChannelPoiCode,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryChannelPoiName != null">
        delivery_channel_poi_name = #{record.deliveryChannelPoiName,jdbcType=VARCHAR},
      </if>
      <if test="record.extData != null">
        ext_data = #{record.extData,jdbcType=VARCHAR},
      </if>
      <if test="record.enabled != null">
        enabled = #{record.enabled,jdbcType=TINYINT},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=BIGINT},
      </if>
      <if test="record.operatorName != null">
        operator_name = #{record.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDel != null">
        is_del = #{record.isDel,jdbcType=TINYINT},
      </if>
      <if test="record.deliveryServiceCodes != null">
        delivery_service_codes = #{record.deliveryServiceCodes,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update shop_delivery_config
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      shop_id = #{record.shopId,jdbcType=BIGINT},
      delivery_channel_id = #{record.deliveryChannelId,jdbcType=INTEGER},
      delivery_channel_poi_code = #{record.deliveryChannelPoiCode,jdbcType=VARCHAR},
      delivery_channel_poi_name = #{record.deliveryChannelPoiName,jdbcType=VARCHAR},
      ext_data = #{record.extData,jdbcType=VARCHAR},
      enabled = #{record.enabled,jdbcType=TINYINT},
      operator_id = #{record.operatorId,jdbcType=BIGINT},
      operator_name = #{record.operatorName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_del = #{record.isDel,jdbcType=TINYINT},
      delivery_service_codes = #{record.deliveryServiceCodes,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDO">
    update shop_delivery_config
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="deliveryChannelId != null">
        delivery_channel_id = #{deliveryChannelId,jdbcType=INTEGER},
      </if>
      <if test="deliveryChannelPoiCode != null">
        delivery_channel_poi_code = #{deliveryChannelPoiCode,jdbcType=VARCHAR},
      </if>
      <if test="deliveryChannelPoiName != null">
        delivery_channel_poi_name = #{deliveryChannelPoiName,jdbcType=VARCHAR},
      </if>
      <if test="extData != null">
        ext_data = #{extData,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=TINYINT},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=TINYINT},
      </if>
      <if test="deliveryServiceCodes != null">
        delivery_service_codes = #{deliveryServiceCodes,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDO">
    update shop_delivery_config
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      shop_id = #{shopId,jdbcType=BIGINT},
      delivery_channel_id = #{deliveryChannelId,jdbcType=INTEGER},
      delivery_channel_poi_code = #{deliveryChannelPoiCode,jdbcType=VARCHAR},
      delivery_channel_poi_name = #{deliveryChannelPoiName,jdbcType=VARCHAR},
      ext_data = #{extData,jdbcType=VARCHAR},
      enabled = #{enabled,jdbcType=TINYINT},
      operator_id = #{operatorId,jdbcType=BIGINT},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=TINYINT},
      delivery_service_codes = #{deliveryServiceCodes,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>