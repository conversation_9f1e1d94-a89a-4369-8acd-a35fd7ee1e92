<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.RiderDeliveryStatisticDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="rider_account_id" jdbcType="BIGINT" property="riderAccountId" />
    <result column="delivered" jdbcType="INTEGER" property="delivered" />
    <result column="cancel_before_delivered" jdbcType="INTEGER" property="cancelBeforeDelivered" />
    <result column="timeout" jdbcType="INTEGER" property="timeout" />
    <result column="delivered_exclude_booking" jdbcType="INTEGER" property="deliveredExcludeBooking" />
    <result column="delivered_in_25_mins" jdbcType="INTEGER" property="deliveredIn25Mins" />
    <result column="delivery_duration" jdbcType="BIGINT" property="deliveryDuration" />
    <result column="early_click" jdbcType="INTEGER" property="earlyClick" />
    <result column="risk_control" jdbcType="INTEGER" property="riskControl" />
    <result column="dt" jdbcType="BIGINT" property="dt" />
    <result column="delivered_timeout" jdbcType="INTEGER" property="deliveredTimeout" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, store_id, rider_account_id, delivered, cancel_before_delivered, timeout, 
    delivered_exclude_booking, delivered_in_25_mins, delivery_duration, early_click, 
    risk_control, dt, delivered_timeout
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from rider_delivery_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rider_delivery_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rider_delivery_statistics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDOExample">
    delete from rider_delivery_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDO">
    insert into rider_delivery_statistics (id, tenant_id, store_id, 
      rider_account_id, delivered, cancel_before_delivered, 
      timeout, delivered_exclude_booking, delivered_in_25_mins, 
      delivery_duration, early_click, risk_control, 
      dt, delivered_timeout)
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      #{riderAccountId,jdbcType=BIGINT}, #{delivered,jdbcType=INTEGER}, #{cancelBeforeDelivered,jdbcType=INTEGER}, 
      #{timeout,jdbcType=INTEGER}, #{deliveredExcludeBooking,jdbcType=INTEGER}, #{deliveredIn25Mins,jdbcType=INTEGER}, 
      #{deliveryDuration,jdbcType=BIGINT}, #{earlyClick,jdbcType=INTEGER}, #{riskControl,jdbcType=INTEGER}, 
      #{dt,jdbcType=BIGINT}, #{deliveredTimeout,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDO">
    insert into rider_delivery_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="riderAccountId != null">
        rider_account_id,
      </if>
      <if test="delivered != null">
        delivered,
      </if>
      <if test="cancelBeforeDelivered != null">
        cancel_before_delivered,
      </if>
      <if test="timeout != null">
        timeout,
      </if>
      <if test="deliveredExcludeBooking != null">
        delivered_exclude_booking,
      </if>
      <if test="deliveredIn25Mins != null">
        delivered_in_25_mins,
      </if>
      <if test="deliveryDuration != null">
        delivery_duration,
      </if>
      <if test="earlyClick != null">
        early_click,
      </if>
      <if test="riskControl != null">
        risk_control,
      </if>
      <if test="dt != null">
        dt,
      </if>
      <if test="deliveredTimeout != null">
        delivered_timeout,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="riderAccountId != null">
        #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="delivered != null">
        #{delivered,jdbcType=INTEGER},
      </if>
      <if test="cancelBeforeDelivered != null">
        #{cancelBeforeDelivered,jdbcType=INTEGER},
      </if>
      <if test="timeout != null">
        #{timeout,jdbcType=INTEGER},
      </if>
      <if test="deliveredExcludeBooking != null">
        #{deliveredExcludeBooking,jdbcType=INTEGER},
      </if>
      <if test="deliveredIn25Mins != null">
        #{deliveredIn25Mins,jdbcType=INTEGER},
      </if>
      <if test="deliveryDuration != null">
        #{deliveryDuration,jdbcType=BIGINT},
      </if>
      <if test="earlyClick != null">
        #{earlyClick,jdbcType=INTEGER},
      </if>
      <if test="riskControl != null">
        #{riskControl,jdbcType=INTEGER},
      </if>
      <if test="dt != null">
        #{dt,jdbcType=BIGINT},
      </if>
      <if test="deliveredTimeout != null">
        #{deliveredTimeout,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDOExample" resultType="java.lang.Long">
    select count(*) from rider_delivery_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rider_delivery_statistics
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.riderAccountId != null">
        rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.delivered != null">
        delivered = #{record.delivered,jdbcType=INTEGER},
      </if>
      <if test="record.cancelBeforeDelivered != null">
        cancel_before_delivered = #{record.cancelBeforeDelivered,jdbcType=INTEGER},
      </if>
      <if test="record.timeout != null">
        timeout = #{record.timeout,jdbcType=INTEGER},
      </if>
      <if test="record.deliveredExcludeBooking != null">
        delivered_exclude_booking = #{record.deliveredExcludeBooking,jdbcType=INTEGER},
      </if>
      <if test="record.deliveredIn25Mins != null">
        delivered_in_25_mins = #{record.deliveredIn25Mins,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryDuration != null">
        delivery_duration = #{record.deliveryDuration,jdbcType=BIGINT},
      </if>
      <if test="record.earlyClick != null">
        early_click = #{record.earlyClick,jdbcType=INTEGER},
      </if>
      <if test="record.riskControl != null">
        risk_control = #{record.riskControl,jdbcType=INTEGER},
      </if>
      <if test="record.dt != null">
        dt = #{record.dt,jdbcType=BIGINT},
      </if>
      <if test="record.deliveredTimeout != null">
        delivered_timeout = #{record.deliveredTimeout,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rider_delivery_statistics
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      delivered = #{record.delivered,jdbcType=INTEGER},
      cancel_before_delivered = #{record.cancelBeforeDelivered,jdbcType=INTEGER},
      timeout = #{record.timeout,jdbcType=INTEGER},
      delivered_exclude_booking = #{record.deliveredExcludeBooking,jdbcType=INTEGER},
      delivered_in_25_mins = #{record.deliveredIn25Mins,jdbcType=INTEGER},
      delivery_duration = #{record.deliveryDuration,jdbcType=BIGINT},
      early_click = #{record.earlyClick,jdbcType=INTEGER},
      risk_control = #{record.riskControl,jdbcType=INTEGER},
      dt = #{record.dt,jdbcType=BIGINT},
      delivered_timeout = #{record.deliveredTimeout,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDO">
    update rider_delivery_statistics
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="riderAccountId != null">
        rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="delivered != null">
        delivered = #{delivered,jdbcType=INTEGER},
      </if>
      <if test="cancelBeforeDelivered != null">
        cancel_before_delivered = #{cancelBeforeDelivered,jdbcType=INTEGER},
      </if>
      <if test="timeout != null">
        timeout = #{timeout,jdbcType=INTEGER},
      </if>
      <if test="deliveredExcludeBooking != null">
        delivered_exclude_booking = #{deliveredExcludeBooking,jdbcType=INTEGER},
      </if>
      <if test="deliveredIn25Mins != null">
        delivered_in_25_mins = #{deliveredIn25Mins,jdbcType=INTEGER},
      </if>
      <if test="deliveryDuration != null">
        delivery_duration = #{deliveryDuration,jdbcType=BIGINT},
      </if>
      <if test="earlyClick != null">
        early_click = #{earlyClick,jdbcType=INTEGER},
      </if>
      <if test="riskControl != null">
        risk_control = #{riskControl,jdbcType=INTEGER},
      </if>
      <if test="dt != null">
        dt = #{dt,jdbcType=BIGINT},
      </if>
      <if test="deliveredTimeout != null">
        delivered_timeout = #{deliveredTimeout,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDO">
    update rider_delivery_statistics
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      delivered = #{delivered,jdbcType=INTEGER},
      cancel_before_delivered = #{cancelBeforeDelivered,jdbcType=INTEGER},
      timeout = #{timeout,jdbcType=INTEGER},
      delivered_exclude_booking = #{deliveredExcludeBooking,jdbcType=INTEGER},
      delivered_in_25_mins = #{deliveredIn25Mins,jdbcType=INTEGER},
      delivery_duration = #{deliveryDuration,jdbcType=BIGINT},
      early_click = #{earlyClick,jdbcType=INTEGER},
      risk_control = #{riskControl,jdbcType=INTEGER},
      dt = #{dt,jdbcType=BIGINT},
      delivered_timeout = #{deliveredTimeout,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>