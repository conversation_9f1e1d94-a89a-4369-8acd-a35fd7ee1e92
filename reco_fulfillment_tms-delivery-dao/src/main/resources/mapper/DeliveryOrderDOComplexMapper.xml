<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderDOComplexMapper">

    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <resultMap id="OrderIdResultMap"
               type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderIdDO">
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
    </resultMap>
    <resultMap id="IdResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryIdDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="active_status" jdbcType="BIGINT" property="activeStatus"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="delivery_exception_type" jdbcType="TINYINT" property="deliveryExceptionType"/>
        <result column="channel_order_id" jdbcType="VARCHAR" property="channelOrderId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>




    <sql id="Order_Id_Column">
        order_id
    </sql>
    <sql id="Id_Column">
        id
        ,active_status,order_id,delivery_exception_type,channel_order_id,create_time
    </sql>

    <select id="selectOrderIdList"
            parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDOExample"
            resultMap="OrderIdResultMap">
        /*+zebra:oi=true*/select
        <if test="distinct">
            distinct
        </if>
        <include refid="Order_Id_Column"/>
        from delivery_order
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectIdList"
            parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDOExample"
            resultMap="IdResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Id_Column"/>
        from delivery_order
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectByExampleSlave" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDOExample" resultMap="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderDOMapper.BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderDOMapper.Base_Column_List" />
        from delivery_order
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>

    <select id="selectByExampleWithStoreList" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDOExample" resultMap="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderDOMapper.BaseResultMap">
        /*+zebra:oi=true*/select
        <if test="distinct">
            distinct
        </if>
        <include refid="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderDOMapper.Base_Column_List" />
        from delivery_order
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>

    <select id="countByExampleSlave" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDOExample" resultType="java.lang.Long">
        select count(*) from delivery_order
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>

    <select id="countByExampleWithStoreListSlave" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDOExample" resultType="java.lang.Long">
        /*+zebra:oi=true*/select count(*) from delivery_order
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>

    <select id="countByExampleWithStoreList" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDOExample" resultType="java.lang.Long">
        /*+zebra:oi=true*/select count(*) from delivery_order
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>

    <insert id="insertSelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDO" useGeneratedKeys="true" keyProperty="id">
        insert into delivery_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="storeId != null">
                store_id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="fulfillmentOrderId != null">
                fulfillment_order_id,
            </if>
            <if test="platformSource != null">
                platform_source,
            </if>
            <if test="channelOrderId != null">
                channel_order_id,
            </if>
            <if test="orderBizType != null">
                order_biz_type,
            </if>
            <if test="daySeq != null">
                day_seq,
            </if>
            <if test="reserved != null">
                reserved,
            </if>
            <if test="orderSource != null">
                order_source,
            </if>
            <if test="receiverName != null">
                receiver_name,
            </if>
            <if test="receiverPhone != null">
                receiver_phone,
            </if>
            <if test="receiverPrivacyPhone != null">
                receiver_privacy_phone,
            </if>
            <if test="receiverAddress != null">
                receiver_address,
            </if>
            <if test="estimatedDeliveryTime != null">
                estimated_delivery_time,
            </if>
            <if test="estimatedDeliveryEndTime != null">
                estimated_delivery_end_time,
            </if>
            <if test="deliveryChannel != null">
                delivery_channel,
            </if>
            <if test="channelDeliveryId != null">
                channel_delivery_id,
            </if>
            <if test="channelServicePackageCode != null">
                channel_service_package_code,
            </if>
            <if test="deliveryStatus != null">
                delivery_status,
            </if>
            <if test="deliveryExceptionType != null">
                delivery_exception_type,
            </if>
            <if test="deliveryExceptionDescription != null">
                delivery_exception_description,
            </if>
            <if test="riderName != null">
                rider_name,
            </if>
            <if test="riderPhone != null">
                rider_phone,
            </if>
            <if test="riderPhoneToken != null">
                rider_phone_token,
            </if>
            <if test="riderAccountId != null">
                rider_account_id,
            </if>
            <if test="activeStatus != null">
                active_status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="lastEventTime != null">
                last_event_time,
            </if>
            <if test="deliveryDoneTime != null">
                delivery_done_time,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="deliveryFee != null">
                delivery_fee,
            </if>
            <if test="distance != null">
                distance,
            </if>
            <if test="cancelMark != null">
                cancel_mark,
            </if>
            <if test="deliveryExceptionCode != null">
                delivery_exception_code,
            </if>
            <if test="tipAmount != null">
                tip_amount,
            </if>
            <if test="deliveryCount != null">
                delivery_count,
            </if>
            <if test="platformFee != null">
                platform_fee,
            </if>
            <if test="currentStatusLock != null">
                current_status_lock,
            </if>
            <if test="totalLockCount != null">
                total_lock_count,
            </if>
            <if test="currentLockTime != null">
                current_lock_time,
            </if>
            <if test="currentUnlockTime != null">
                current_unlock_time,
            </if>
            <if test="extInfo != null">
                ext_info,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="storeId != null">
                #{storeId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="fulfillmentOrderId != null">
                #{fulfillmentOrderId,jdbcType=BIGINT},
            </if>
            <if test="platformSource != null">
                #{platformSource,jdbcType=INTEGER},
            </if>
            <if test="channelOrderId != null">
                #{channelOrderId,jdbcType=VARCHAR},
            </if>
            <if test="orderBizType != null">
                #{orderBizType,jdbcType=INTEGER},
            </if>
            <if test="daySeq != null">
                #{daySeq,jdbcType=INTEGER},
            </if>
            <if test="reserved != null">
                #{reserved,jdbcType=TINYINT},
            </if>
            <if test="orderSource != null">
                #{orderSource,jdbcType=TINYINT},
            </if>
            <if test="receiverName != null">
                #{receiverName,jdbcType=VARCHAR},
            </if>
            <if test="receiverPhone != null">
                #{receiverPhone,jdbcType=VARCHAR},
            </if>
            <if test="receiverPrivacyPhone != null">
                #{receiverPrivacyPhone,jdbcType=VARCHAR},
            </if>
            <if test="receiverAddress != null">
                #{receiverAddress,jdbcType=CHAR},
            </if>
            <if test="estimatedDeliveryTime != null">
                #{estimatedDeliveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="estimatedDeliveryEndTime != null">
                #{estimatedDeliveryEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deliveryChannel != null">
                #{deliveryChannel,jdbcType=INTEGER},
            </if>
            <if test="channelDeliveryId != null">
                #{channelDeliveryId,jdbcType=VARCHAR},
            </if>
            <if test="channelServicePackageCode != null">
                #{channelServicePackageCode,jdbcType=VARCHAR},
            </if>
            <if test="deliveryStatus != null">
                #{deliveryStatus,jdbcType=INTEGER},
            </if>
            <if test="deliveryExceptionType != null">
                #{deliveryExceptionType,jdbcType=TINYINT},
            </if>
            <if test="deliveryExceptionDescription != null">
                #{deliveryExceptionDescription,jdbcType=VARCHAR},
            </if>
            <if test="riderName != null">
                #{riderName,jdbcType=VARCHAR},
            </if>
            <if test="riderPhone != null">
                #{riderPhone,jdbcType=VARCHAR},
            </if>
            <if test="riderPhoneToken != null">
                #{riderPhoneToken,jdbcType=VARCHAR},
            </if>
            <if test="riderAccountId != null">
                #{riderAccountId,jdbcType=BIGINT},
            </if>
            <if test="activeStatus != null">
                #{activeStatus,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastEventTime != null">
                #{lastEventTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deliveryDoneTime != null">
                #{deliveryDoneTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="deliveryFee != null">
                #{deliveryFee,jdbcType=DECIMAL},
            </if>
            <if test="distance != null">
                #{distance,jdbcType=BIGINT},
            </if>
            <if test="cancelMark != null">
                #{cancelMark,jdbcType=TINYINT},
            </if>
            <if test="deliveryExceptionCode != null">
                #{deliveryExceptionCode,jdbcType=TINYINT},
            </if>
            <if test="tipAmount != null">
                #{tipAmount,jdbcType=DECIMAL},
            </if>
            <if test="deliveryCount != null">
                #{deliveryCount,jdbcType=INTEGER},
            </if>
            <if test="platformFee != null">
                #{platformFee,jdbcType=DECIMAL},
            </if>
            <if test="currentStatusLock != null">
                #{currentStatusLock,jdbcType=BIT},
            </if>
            <if test="totalLockCount != null">
                #{totalLockCount,jdbcType=INTEGER},
            </if>
            <if test="currentLockTime != null">
                #{currentLockTime,jdbcType=TIMESTAMP},
            </if>
            <if test="currentUnlockTime != null">
                #{currentUnlockTime,jdbcType=TIMESTAMP},
            </if>
            <if test="extInfo != null">
                #{extInfo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="queryStoreDeliveringOrderCount" parameterType="java.util.Map" resultType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.CountGroupExDO">
        /*+zebra:oi=true*/select store_id as groupColumn, count(0) as groupCount from delivery_order where tenant_id =#{tenantId} and
        store_id in
        <foreach collection="storeIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and delivery_status in
        <foreach collection="status" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by store_id;
    </select>
</mapper>