<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.RiderDeliveryExceptionDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="delivery_order_id" jdbcType="BIGINT" property="deliveryOrderId" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="channel_order_id" jdbcType="VARCHAR" property="channelOrderId" />
    <result column="order_biz_type" jdbcType="INTEGER" property="orderBizType" />
    <result column="day_seq" jdbcType="INTEGER" property="daySeq" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="rider_name" jdbcType="VARCHAR" property="riderName" />
    <result column="rider_account_id" jdbcType="BIGINT" property="riderAccountId" />
    <result column="report_exception_type" jdbcType="INTEGER" property="reportExceptionType" />
    <result column="report_exception_sub_type" jdbcType="INTEGER" property="reportExceptionSubType" />
    <result column="report_exception_description" jdbcType="VARCHAR" property="reportExceptionDescription" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, store_id, delivery_order_id, pay_time, channel_order_id, order_biz_type, 
    day_seq, create_time, update_time, rider_name, rider_account_id, report_exception_type, 
    report_exception_sub_type, report_exception_description
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from delivery_order_exception_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from delivery_order_exception_report
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from delivery_order_exception_report
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDOExample">
    delete from delivery_order_exception_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDO">
    insert into delivery_order_exception_report (id, tenant_id, store_id, 
      delivery_order_id, pay_time, channel_order_id, 
      order_biz_type, day_seq, create_time, 
      update_time, rider_name, rider_account_id, 
      report_exception_type, report_exception_sub_type, 
      report_exception_description)
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      #{deliveryOrderId,jdbcType=BIGINT}, #{payTime,jdbcType=TIMESTAMP}, #{channelOrderId,jdbcType=VARCHAR}, 
      #{orderBizType,jdbcType=INTEGER}, #{daySeq,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{riderName,jdbcType=VARCHAR}, #{riderAccountId,jdbcType=BIGINT}, 
      #{reportExceptionType,jdbcType=INTEGER}, #{reportExceptionSubType,jdbcType=INTEGER}, 
      #{reportExceptionDescription,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDO">
    insert into delivery_order_exception_report
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="deliveryOrderId != null">
        delivery_order_id,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="channelOrderId != null">
        channel_order_id,
      </if>
      <if test="orderBizType != null">
        order_biz_type,
      </if>
      <if test="daySeq != null">
        day_seq,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="riderName != null">
        rider_name,
      </if>
      <if test="riderAccountId != null">
        rider_account_id,
      </if>
      <if test="reportExceptionType != null">
        report_exception_type,
      </if>
      <if test="reportExceptionSubType != null">
        report_exception_sub_type,
      </if>
      <if test="reportExceptionDescription != null">
        report_exception_description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="deliveryOrderId != null">
        #{deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="channelOrderId != null">
        #{channelOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderBizType != null">
        #{orderBizType,jdbcType=INTEGER},
      </if>
      <if test="daySeq != null">
        #{daySeq,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="riderName != null">
        #{riderName,jdbcType=VARCHAR},
      </if>
      <if test="riderAccountId != null">
        #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="reportExceptionType != null">
        #{reportExceptionType,jdbcType=INTEGER},
      </if>
      <if test="reportExceptionSubType != null">
        #{reportExceptionSubType,jdbcType=INTEGER},
      </if>
      <if test="reportExceptionDescription != null">
        #{reportExceptionDescription,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDOExample" resultType="java.lang.Long">
    select count(*) from delivery_order_exception_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update delivery_order_exception_report
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.deliveryOrderId != null">
        delivery_order_id = #{record.deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.channelOrderId != null">
        channel_order_id = #{record.channelOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderBizType != null">
        order_biz_type = #{record.orderBizType,jdbcType=INTEGER},
      </if>
      <if test="record.daySeq != null">
        day_seq = #{record.daySeq,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.riderName != null">
        rider_name = #{record.riderName,jdbcType=VARCHAR},
      </if>
      <if test="record.riderAccountId != null">
        rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.reportExceptionType != null">
        report_exception_type = #{record.reportExceptionType,jdbcType=INTEGER},
      </if>
      <if test="record.reportExceptionSubType != null">
        report_exception_sub_type = #{record.reportExceptionSubType,jdbcType=INTEGER},
      </if>
      <if test="record.reportExceptionDescription != null">
        report_exception_description = #{record.reportExceptionDescription,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update delivery_order_exception_report
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      delivery_order_id = #{record.deliveryOrderId,jdbcType=BIGINT},
      pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      channel_order_id = #{record.channelOrderId,jdbcType=VARCHAR},
      order_biz_type = #{record.orderBizType,jdbcType=INTEGER},
      day_seq = #{record.daySeq,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      rider_name = #{record.riderName,jdbcType=VARCHAR},
      rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      report_exception_type = #{record.reportExceptionType,jdbcType=INTEGER},
      report_exception_sub_type = #{record.reportExceptionSubType,jdbcType=INTEGER},
      report_exception_description = #{record.reportExceptionDescription,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDO">
    update delivery_order_exception_report
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="deliveryOrderId != null">
        delivery_order_id = #{deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="channelOrderId != null">
        channel_order_id = #{channelOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderBizType != null">
        order_biz_type = #{orderBizType,jdbcType=INTEGER},
      </if>
      <if test="daySeq != null">
        day_seq = #{daySeq,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="riderName != null">
        rider_name = #{riderName,jdbcType=VARCHAR},
      </if>
      <if test="riderAccountId != null">
        rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="reportExceptionType != null">
        report_exception_type = #{reportExceptionType,jdbcType=INTEGER},
      </if>
      <if test="reportExceptionSubType != null">
        report_exception_sub_type = #{reportExceptionSubType,jdbcType=INTEGER},
      </if>
      <if test="reportExceptionDescription != null">
        report_exception_description = #{reportExceptionDescription,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDO">
    update delivery_order_exception_report
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      delivery_order_id = #{deliveryOrderId,jdbcType=BIGINT},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      channel_order_id = #{channelOrderId,jdbcType=VARCHAR},
      order_biz_type = #{orderBizType,jdbcType=INTEGER},
      day_seq = #{daySeq,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      rider_name = #{riderName,jdbcType=VARCHAR},
      rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      report_exception_type = #{reportExceptionType,jdbcType=INTEGER},
      report_exception_sub_type = #{reportExceptionSubType,jdbcType=INTEGER},
      report_exception_description = #{reportExceptionDescription,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>