<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryChannelDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryChannelDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="logistic_mark" jdbcType="VARCHAR" property="logisticMark" />
    <result column="delivery_platform_code" jdbcType="INTEGER" property="deliveryPlatformCode" />
    <result column="carrier_code" jdbcType="INTEGER" property="carrierCode" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="order_channel_code" jdbcType="INTEGER" property="orderChannelCode" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="utime" jdbcType="TIMESTAMP" property="utime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, logistic_mark, delivery_platform_code, carrier_code, carrier_name, order_channel_code, 
    ctime, utime
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryChannelDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from delivery_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from delivery_channel
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from delivery_channel
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryChannelDOExample">
    delete from delivery_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryChannelDO">
    insert into delivery_channel (id, logistic_mark, delivery_platform_code, 
      carrier_code, carrier_name, order_channel_code, 
      ctime, utime)
    values (#{id,jdbcType=BIGINT}, #{logisticMark,jdbcType=VARCHAR}, #{deliveryPlatformCode,jdbcType=INTEGER}, 
      #{carrierCode,jdbcType=INTEGER}, #{carrierName,jdbcType=VARCHAR}, #{orderChannelCode,jdbcType=INTEGER}, 
      #{ctime,jdbcType=TIMESTAMP}, #{utime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryChannelDO">
    insert into delivery_channel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="logisticMark != null">
        logistic_mark,
      </if>
      <if test="deliveryPlatformCode != null">
        delivery_platform_code,
      </if>
      <if test="carrierCode != null">
        carrier_code,
      </if>
      <if test="carrierName != null">
        carrier_name,
      </if>
      <if test="orderChannelCode != null">
        order_channel_code,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="utime != null">
        utime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="logisticMark != null">
        #{logisticMark,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPlatformCode != null">
        #{deliveryPlatformCode,jdbcType=INTEGER},
      </if>
      <if test="carrierCode != null">
        #{carrierCode,jdbcType=INTEGER},
      </if>
      <if test="carrierName != null">
        #{carrierName,jdbcType=VARCHAR},
      </if>
      <if test="orderChannelCode != null">
        #{orderChannelCode,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="utime != null">
        #{utime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryChannelDOExample" resultType="java.lang.Long">
    select count(*) from delivery_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update delivery_channel
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.logisticMark != null">
        logistic_mark = #{record.logisticMark,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryPlatformCode != null">
        delivery_platform_code = #{record.deliveryPlatformCode,jdbcType=INTEGER},
      </if>
      <if test="record.carrierCode != null">
        carrier_code = #{record.carrierCode,jdbcType=INTEGER},
      </if>
      <if test="record.carrierName != null">
        carrier_name = #{record.carrierName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderChannelCode != null">
        order_channel_code = #{record.orderChannelCode,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.utime != null">
        utime = #{record.utime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update delivery_channel
    set id = #{record.id,jdbcType=BIGINT},
      logistic_mark = #{record.logisticMark,jdbcType=VARCHAR},
      delivery_platform_code = #{record.deliveryPlatformCode,jdbcType=INTEGER},
      carrier_code = #{record.carrierCode,jdbcType=INTEGER},
      carrier_name = #{record.carrierName,jdbcType=VARCHAR},
      order_channel_code = #{record.orderChannelCode,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      utime = #{record.utime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryChannelDO">
    update delivery_channel
    <set>
      <if test="logisticMark != null">
        logistic_mark = #{logisticMark,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPlatformCode != null">
        delivery_platform_code = #{deliveryPlatformCode,jdbcType=INTEGER},
      </if>
      <if test="carrierCode != null">
        carrier_code = #{carrierCode,jdbcType=INTEGER},
      </if>
      <if test="carrierName != null">
        carrier_name = #{carrierName,jdbcType=VARCHAR},
      </if>
      <if test="orderChannelCode != null">
        order_channel_code = #{orderChannelCode,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="utime != null">
        utime = #{utime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryChannelDO">
    update delivery_channel
    set logistic_mark = #{logisticMark,jdbcType=VARCHAR},
      delivery_platform_code = #{deliveryPlatformCode,jdbcType=INTEGER},
      carrier_code = #{carrierCode,jdbcType=INTEGER},
      carrier_name = #{carrierName,jdbcType=VARCHAR},
      order_channel_code = #{orderChannelCode,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      utime = #{utime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>