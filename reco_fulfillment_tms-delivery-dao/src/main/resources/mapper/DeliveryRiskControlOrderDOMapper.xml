<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryRiskControlOrderDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="poi_id" jdbcType="BIGINT" property="poiId" />
    <result column="rider_account_id" jdbcType="BIGINT" property="riderAccountId" />
    <result column="order_biz_type" jdbcType="INTEGER" property="orderBizType" />
    <result column="order_id_view" jdbcType="VARCHAR" property="orderIdView" />
    <result column="dt" jdbcType="BIGINT" property="dt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, poi_id, rider_account_id, order_biz_type, order_id_view, dt
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from delivery_risk_control_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from delivery_risk_control_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from delivery_risk_control_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDOExample">
    delete from delivery_risk_control_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDO">
    insert into delivery_risk_control_order (id, tenant_id, poi_id, 
      rider_account_id, order_biz_type, order_id_view, 
      dt)
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, #{poiId,jdbcType=BIGINT}, 
      #{riderAccountId,jdbcType=BIGINT}, #{orderBizType,jdbcType=INTEGER}, #{orderIdView,jdbcType=VARCHAR}, 
      #{dt,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDO">
    insert into delivery_risk_control_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="poiId != null">
        poi_id,
      </if>
      <if test="riderAccountId != null">
        rider_account_id,
      </if>
      <if test="orderBizType != null">
        order_biz_type,
      </if>
      <if test="orderIdView != null">
        order_id_view,
      </if>
      <if test="dt != null">
        dt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="poiId != null">
        #{poiId,jdbcType=BIGINT},
      </if>
      <if test="riderAccountId != null">
        #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="orderBizType != null">
        #{orderBizType,jdbcType=INTEGER},
      </if>
      <if test="orderIdView != null">
        #{orderIdView,jdbcType=VARCHAR},
      </if>
      <if test="dt != null">
        #{dt,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDOExample" resultType="java.lang.Long">
    select count(*) from delivery_risk_control_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update delivery_risk_control_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.poiId != null">
        poi_id = #{record.poiId,jdbcType=BIGINT},
      </if>
      <if test="record.riderAccountId != null">
        rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.orderBizType != null">
        order_biz_type = #{record.orderBizType,jdbcType=INTEGER},
      </if>
      <if test="record.orderIdView != null">
        order_id_view = #{record.orderIdView,jdbcType=VARCHAR},
      </if>
      <if test="record.dt != null">
        dt = #{record.dt,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update delivery_risk_control_order
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      poi_id = #{record.poiId,jdbcType=BIGINT},
      rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      order_biz_type = #{record.orderBizType,jdbcType=INTEGER},
      order_id_view = #{record.orderIdView,jdbcType=VARCHAR},
      dt = #{record.dt,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDO">
    update delivery_risk_control_order
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="poiId != null">
        poi_id = #{poiId,jdbcType=BIGINT},
      </if>
      <if test="riderAccountId != null">
        rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="orderBizType != null">
        order_biz_type = #{orderBizType,jdbcType=INTEGER},
      </if>
      <if test="orderIdView != null">
        order_id_view = #{orderIdView,jdbcType=VARCHAR},
      </if>
      <if test="dt != null">
        dt = #{dt,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDO">
    update delivery_risk_control_order
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      poi_id = #{poiId,jdbcType=BIGINT},
      rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      order_biz_type = #{orderBizType,jdbcType=INTEGER},
      order_id_view = #{orderIdView,jdbcType=VARCHAR},
      dt = #{dt,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>