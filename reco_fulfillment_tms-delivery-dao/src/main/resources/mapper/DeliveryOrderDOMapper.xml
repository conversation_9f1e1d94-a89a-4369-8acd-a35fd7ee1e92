<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="fulfillment_order_id" jdbcType="BIGINT" property="fulfillmentOrderId" />
    <result column="platform_source" jdbcType="INTEGER" property="platformSource" />
    <result column="channel_order_id" jdbcType="VARCHAR" property="channelOrderId" />
    <result column="order_biz_type" jdbcType="INTEGER" property="orderBizType" />
    <result column="day_seq" jdbcType="INTEGER" property="daySeq" />
    <result column="reserved" jdbcType="TINYINT" property="reserved" />
    <result column="order_source" jdbcType="TINYINT" property="orderSource" />
    <result column="receiver_name" jdbcType="VARCHAR" property="receiverName" />
    <result column="receiver_phone" jdbcType="VARCHAR" property="receiverPhone" />
    <result column="receiver_privacy_phone" jdbcType="VARCHAR" property="receiverPrivacyPhone" />
    <result column="receiver_address" jdbcType="CHAR" property="receiverAddress" />
    <result column="estimated_delivery_time" jdbcType="TIMESTAMP" property="estimatedDeliveryTime" />
    <result column="estimated_delivery_end_time" jdbcType="TIMESTAMP" property="estimatedDeliveryEndTime" />
    <result column="delivery_channel" jdbcType="INTEGER" property="deliveryChannel" />
    <result column="channel_delivery_id" jdbcType="VARCHAR" property="channelDeliveryId" />
    <result column="channel_service_package_code" jdbcType="VARCHAR" property="channelServicePackageCode" />
    <result column="delivery_status" jdbcType="INTEGER" property="deliveryStatus" />
    <result column="delivery_exception_type" jdbcType="TINYINT" property="deliveryExceptionType" />
    <result column="delivery_exception_description" jdbcType="VARCHAR" property="deliveryExceptionDescription" />
    <result column="rider_name" jdbcType="VARCHAR" property="riderName" />
    <result column="rider_phone" jdbcType="VARCHAR" property="riderPhone" />
    <result column="rider_phone_token" jdbcType="VARCHAR" property="riderPhoneToken" />
    <result column="rider_account_id" jdbcType="BIGINT" property="riderAccountId" />
    <result column="active_status" jdbcType="BIGINT" property="activeStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="last_event_time" jdbcType="TIMESTAMP" property="lastEventTime" />
    <result column="delivery_done_time" jdbcType="TIMESTAMP" property="deliveryDoneTime" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
    <result column="distance" jdbcType="BIGINT" property="distance" />
    <result column="cancel_mark" jdbcType="TINYINT" property="cancelMark" />
    <result column="delivery_exception_code" jdbcType="TINYINT" property="deliveryExceptionCode" />
    <result column="tip_amount" jdbcType="DECIMAL" property="tipAmount" />
    <result column="delivery_count" jdbcType="INTEGER" property="deliveryCount" />
    <result column="platform_fee" jdbcType="DECIMAL" property="platformFee" />
    <result column="current_status_lock" jdbcType="BIT" property="currentStatusLock" />
    <result column="total_lock_count" jdbcType="INTEGER" property="totalLockCount" />
    <result column="current_lock_time" jdbcType="TIMESTAMP" property="currentLockTime" />
    <result column="current_unlock_time" jdbcType="TIMESTAMP" property="currentUnlockTime" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, store_id, order_id, fulfillment_order_id, platform_source, channel_order_id, 
    order_biz_type, day_seq, reserved, order_source, receiver_name, receiver_phone, receiver_privacy_phone, 
    receiver_address, estimated_delivery_time, estimated_delivery_end_time, delivery_channel, 
    channel_delivery_id, channel_service_package_code, delivery_status, delivery_exception_type, 
    delivery_exception_description, rider_name, rider_phone, rider_phone_token, rider_account_id, 
    active_status, create_time, update_time, last_event_time, delivery_done_time, version, 
    delivery_fee, distance, cancel_mark, delivery_exception_code, tip_amount, delivery_count, 
    platform_fee, current_status_lock, total_lock_count, current_lock_time, current_unlock_time, 
    ext_info
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from delivery_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from delivery_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from delivery_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDOExample">
    delete from delivery_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDO">
    insert into delivery_order (id, tenant_id, store_id, 
      order_id, fulfillment_order_id, platform_source, 
      channel_order_id, order_biz_type, day_seq, 
      reserved, order_source, receiver_name, 
      receiver_phone, receiver_privacy_phone, receiver_address, 
      estimated_delivery_time, estimated_delivery_end_time, 
      delivery_channel, channel_delivery_id, channel_service_package_code, 
      delivery_status, delivery_exception_type, 
      delivery_exception_description, rider_name, 
      rider_phone, rider_phone_token, rider_account_id, 
      active_status, create_time, update_time, 
      last_event_time, delivery_done_time, version, 
      delivery_fee, distance, cancel_mark, 
      delivery_exception_code, tip_amount, delivery_count, 
      platform_fee, current_status_lock, total_lock_count, 
      current_lock_time, current_unlock_time, 
      ext_info)
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      #{orderId,jdbcType=BIGINT}, #{fulfillmentOrderId,jdbcType=BIGINT}, #{platformSource,jdbcType=INTEGER}, 
      #{channelOrderId,jdbcType=VARCHAR}, #{orderBizType,jdbcType=INTEGER}, #{daySeq,jdbcType=INTEGER}, 
      #{reserved,jdbcType=TINYINT}, #{orderSource,jdbcType=TINYINT}, #{receiverName,jdbcType=VARCHAR}, 
      #{receiverPhone,jdbcType=VARCHAR}, #{receiverPrivacyPhone,jdbcType=VARCHAR}, #{receiverAddress,jdbcType=CHAR}, 
      #{estimatedDeliveryTime,jdbcType=TIMESTAMP}, #{estimatedDeliveryEndTime,jdbcType=TIMESTAMP}, 
      #{deliveryChannel,jdbcType=INTEGER}, #{channelDeliveryId,jdbcType=VARCHAR}, #{channelServicePackageCode,jdbcType=VARCHAR}, 
      #{deliveryStatus,jdbcType=INTEGER}, #{deliveryExceptionType,jdbcType=TINYINT}, 
      #{deliveryExceptionDescription,jdbcType=VARCHAR}, #{riderName,jdbcType=VARCHAR}, 
      #{riderPhone,jdbcType=VARCHAR}, #{riderPhoneToken,jdbcType=VARCHAR}, #{riderAccountId,jdbcType=BIGINT}, 
      #{activeStatus,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{lastEventTime,jdbcType=TIMESTAMP}, #{deliveryDoneTime,jdbcType=TIMESTAMP}, #{version,jdbcType=INTEGER}, 
      #{deliveryFee,jdbcType=DECIMAL}, #{distance,jdbcType=BIGINT}, #{cancelMark,jdbcType=TINYINT}, 
      #{deliveryExceptionCode,jdbcType=TINYINT}, #{tipAmount,jdbcType=DECIMAL}, #{deliveryCount,jdbcType=INTEGER}, 
      #{platformFee,jdbcType=DECIMAL}, #{currentStatusLock,jdbcType=BIT}, #{totalLockCount,jdbcType=INTEGER}, 
      #{currentLockTime,jdbcType=TIMESTAMP}, #{currentUnlockTime,jdbcType=TIMESTAMP}, 
      #{extInfo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDO">
    insert into delivery_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="fulfillmentOrderId != null">
        fulfillment_order_id,
      </if>
      <if test="platformSource != null">
        platform_source,
      </if>
      <if test="channelOrderId != null">
        channel_order_id,
      </if>
      <if test="orderBizType != null">
        order_biz_type,
      </if>
      <if test="daySeq != null">
        day_seq,
      </if>
      <if test="reserved != null">
        reserved,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="receiverName != null">
        receiver_name,
      </if>
      <if test="receiverPhone != null">
        receiver_phone,
      </if>
      <if test="receiverPrivacyPhone != null">
        receiver_privacy_phone,
      </if>
      <if test="receiverAddress != null">
        receiver_address,
      </if>
      <if test="estimatedDeliveryTime != null">
        estimated_delivery_time,
      </if>
      <if test="estimatedDeliveryEndTime != null">
        estimated_delivery_end_time,
      </if>
      <if test="deliveryChannel != null">
        delivery_channel,
      </if>
      <if test="channelDeliveryId != null">
        channel_delivery_id,
      </if>
      <if test="channelServicePackageCode != null">
        channel_service_package_code,
      </if>
      <if test="deliveryStatus != null">
        delivery_status,
      </if>
      <if test="deliveryExceptionType != null">
        delivery_exception_type,
      </if>
      <if test="deliveryExceptionDescription != null">
        delivery_exception_description,
      </if>
      <if test="riderName != null">
        rider_name,
      </if>
      <if test="riderPhone != null">
        rider_phone,
      </if>
      <if test="riderPhoneToken != null">
        rider_phone_token,
      </if>
      <if test="riderAccountId != null">
        rider_account_id,
      </if>
      <if test="activeStatus != null">
        active_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="lastEventTime != null">
        last_event_time,
      </if>
      <if test="deliveryDoneTime != null">
        delivery_done_time,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="deliveryFee != null">
        delivery_fee,
      </if>
      <if test="distance != null">
        distance,
      </if>
      <if test="cancelMark != null">
        cancel_mark,
      </if>
      <if test="deliveryExceptionCode != null">
        delivery_exception_code,
      </if>
      <if test="tipAmount != null">
        tip_amount,
      </if>
      <if test="deliveryCount != null">
        delivery_count,
      </if>
      <if test="platformFee != null">
        platform_fee,
      </if>
      <if test="currentStatusLock != null">
        current_status_lock,
      </if>
      <if test="totalLockCount != null">
        total_lock_count,
      </if>
      <if test="currentLockTime != null">
        current_lock_time,
      </if>
      <if test="currentUnlockTime != null">
        current_unlock_time,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="fulfillmentOrderId != null">
        #{fulfillmentOrderId,jdbcType=BIGINT},
      </if>
      <if test="platformSource != null">
        #{platformSource,jdbcType=INTEGER},
      </if>
      <if test="channelOrderId != null">
        #{channelOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderBizType != null">
        #{orderBizType,jdbcType=INTEGER},
      </if>
      <if test="daySeq != null">
        #{daySeq,jdbcType=INTEGER},
      </if>
      <if test="reserved != null">
        #{reserved,jdbcType=TINYINT},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="receiverName != null">
        #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="receiverPhone != null">
        #{receiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiverPrivacyPhone != null">
        #{receiverPrivacyPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiverAddress != null">
        #{receiverAddress,jdbcType=CHAR},
      </if>
      <if test="estimatedDeliveryTime != null">
        #{estimatedDeliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estimatedDeliveryEndTime != null">
        #{estimatedDeliveryEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryChannel != null">
        #{deliveryChannel,jdbcType=INTEGER},
      </if>
      <if test="channelDeliveryId != null">
        #{channelDeliveryId,jdbcType=VARCHAR},
      </if>
      <if test="channelServicePackageCode != null">
        #{channelServicePackageCode,jdbcType=VARCHAR},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryExceptionType != null">
        #{deliveryExceptionType,jdbcType=TINYINT},
      </if>
      <if test="deliveryExceptionDescription != null">
        #{deliveryExceptionDescription,jdbcType=VARCHAR},
      </if>
      <if test="riderName != null">
        #{riderName,jdbcType=VARCHAR},
      </if>
      <if test="riderPhone != null">
        #{riderPhone,jdbcType=VARCHAR},
      </if>
      <if test="riderPhoneToken != null">
        #{riderPhoneToken,jdbcType=VARCHAR},
      </if>
      <if test="riderAccountId != null">
        #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="activeStatus != null">
        #{activeStatus,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastEventTime != null">
        #{lastEventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryDoneTime != null">
        #{deliveryDoneTime,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="deliveryFee != null">
        #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="distance != null">
        #{distance,jdbcType=BIGINT},
      </if>
      <if test="cancelMark != null">
        #{cancelMark,jdbcType=TINYINT},
      </if>
      <if test="deliveryExceptionCode != null">
        #{deliveryExceptionCode,jdbcType=TINYINT},
      </if>
      <if test="tipAmount != null">
        #{tipAmount,jdbcType=DECIMAL},
      </if>
      <if test="deliveryCount != null">
        #{deliveryCount,jdbcType=INTEGER},
      </if>
      <if test="platformFee != null">
        #{platformFee,jdbcType=DECIMAL},
      </if>
      <if test="currentStatusLock != null">
        #{currentStatusLock,jdbcType=BIT},
      </if>
      <if test="totalLockCount != null">
        #{totalLockCount,jdbcType=INTEGER},
      </if>
      <if test="currentLockTime != null">
        #{currentLockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="currentUnlockTime != null">
        #{currentUnlockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDOExample" resultType="java.lang.Long">
    select count(*) from delivery_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="batchQueryOriginWaybillNo" resultMap="BaseResultMap">
    /*+zebra:oi=true*/ select order_id, delivery_status, ext_info from delivery_order
      where tenant_id = #{tenantId,jdbcType=BIGINT}
        and store_id in
        <foreach collection="storeIds" item="storeId" open="(" close=")" separator=",">
          #{storeId,jdbcType=BIGINT}
        </foreach>
        and order_id in
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
          #{orderId,jdbcType=BIGINT}
        </foreach>
        and delivery_status in
        <foreach collection="deliveryStatusList" item="deliveryStatus" open="(" close=")" separator=",">
          #{deliveryStatus,jdbcType=INTEGER}
        </foreach>
  </select>

  <update id="updateByExampleSelective" parameterType="map">
    update delivery_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.fulfillmentOrderId != null">
        fulfillment_order_id = #{record.fulfillmentOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.platformSource != null">
        platform_source = #{record.platformSource,jdbcType=INTEGER},
      </if>
      <if test="record.channelOrderId != null">
        channel_order_id = #{record.channelOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderBizType != null">
        order_biz_type = #{record.orderBizType,jdbcType=INTEGER},
      </if>
      <if test="record.daySeq != null">
        day_seq = #{record.daySeq,jdbcType=INTEGER},
      </if>
      <if test="record.reserved != null">
        reserved = #{record.reserved,jdbcType=TINYINT},
      </if>
      <if test="record.orderSource != null">
        order_source = #{record.orderSource,jdbcType=TINYINT},
      </if>
      <if test="record.receiverName != null">
        receiver_name = #{record.receiverName,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverPhone != null">
        receiver_phone = #{record.receiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverPrivacyPhone != null">
        receiver_privacy_phone = #{record.receiverPrivacyPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverAddress != null">
        receiver_address = #{record.receiverAddress,jdbcType=CHAR},
      </if>
      <if test="record.estimatedDeliveryTime != null">
        estimated_delivery_time = #{record.estimatedDeliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.estimatedDeliveryEndTime != null">
        estimated_delivery_end_time = #{record.estimatedDeliveryEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deliveryChannel != null">
        delivery_channel = #{record.deliveryChannel,jdbcType=INTEGER},
      </if>
      <if test="record.channelDeliveryId != null">
        channel_delivery_id = #{record.channelDeliveryId,jdbcType=VARCHAR},
      </if>
      <if test="record.channelServicePackageCode != null">
        channel_service_package_code = #{record.channelServicePackageCode,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryStatus != null">
        delivery_status = #{record.deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryExceptionType != null">
        delivery_exception_type = #{record.deliveryExceptionType,jdbcType=TINYINT},
      </if>
      <if test="record.deliveryExceptionDescription != null">
        delivery_exception_description = #{record.deliveryExceptionDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.riderName != null">
        rider_name = #{record.riderName,jdbcType=VARCHAR},
      </if>
      <if test="record.riderPhone != null">
        rider_phone = #{record.riderPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.riderPhoneToken != null">
        rider_phone_token = #{record.riderPhoneToken,jdbcType=VARCHAR},
      </if>
      <if test="record.riderAccountId != null">
        rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.activeStatus != null">
        active_status = #{record.activeStatus,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastEventTime != null">
        last_event_time = #{record.lastEventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deliveryDoneTime != null">
        delivery_done_time = #{record.deliveryDoneTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryFee != null">
        delivery_fee = #{record.deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="record.distance != null">
        distance = #{record.distance,jdbcType=BIGINT},
      </if>
      <if test="record.cancelMark != null">
        cancel_mark = #{record.cancelMark,jdbcType=TINYINT},
      </if>
      <if test="record.deliveryExceptionCode != null">
        delivery_exception_code = #{record.deliveryExceptionCode,jdbcType=TINYINT},
      </if>
      <if test="record.tipAmount != null">
        tip_amount = #{record.tipAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.deliveryCount != null">
        delivery_count = #{record.deliveryCount,jdbcType=INTEGER},
      </if>
      <if test="record.platformFee != null">
        platform_fee = #{record.platformFee,jdbcType=DECIMAL},
      </if>
      <if test="record.currentStatusLock != null">
        current_status_lock = #{record.currentStatusLock,jdbcType=BIT},
      </if>
      <if test="record.totalLockCount != null">
        total_lock_count = #{record.totalLockCount,jdbcType=INTEGER},
      </if>
      <if test="record.currentLockTime != null">
        current_lock_time = #{record.currentLockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.currentUnlockTime != null">
        current_unlock_time = #{record.currentUnlockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update delivery_order
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      fulfillment_order_id = #{record.fulfillmentOrderId,jdbcType=BIGINT},
      platform_source = #{record.platformSource,jdbcType=INTEGER},
      channel_order_id = #{record.channelOrderId,jdbcType=VARCHAR},
      order_biz_type = #{record.orderBizType,jdbcType=INTEGER},
      day_seq = #{record.daySeq,jdbcType=INTEGER},
      reserved = #{record.reserved,jdbcType=TINYINT},
      order_source = #{record.orderSource,jdbcType=TINYINT},
      receiver_name = #{record.receiverName,jdbcType=VARCHAR},
      receiver_phone = #{record.receiverPhone,jdbcType=VARCHAR},
      receiver_privacy_phone = #{record.receiverPrivacyPhone,jdbcType=VARCHAR},
      receiver_address = #{record.receiverAddress,jdbcType=CHAR},
      estimated_delivery_time = #{record.estimatedDeliveryTime,jdbcType=TIMESTAMP},
      estimated_delivery_end_time = #{record.estimatedDeliveryEndTime,jdbcType=TIMESTAMP},
      delivery_channel = #{record.deliveryChannel,jdbcType=INTEGER},
      channel_delivery_id = #{record.channelDeliveryId,jdbcType=VARCHAR},
      channel_service_package_code = #{record.channelServicePackageCode,jdbcType=VARCHAR},
      delivery_status = #{record.deliveryStatus,jdbcType=INTEGER},
      delivery_exception_type = #{record.deliveryExceptionType,jdbcType=TINYINT},
      delivery_exception_description = #{record.deliveryExceptionDescription,jdbcType=VARCHAR},
      rider_name = #{record.riderName,jdbcType=VARCHAR},
      rider_phone = #{record.riderPhone,jdbcType=VARCHAR},
      rider_phone_token = #{record.riderPhoneToken,jdbcType=VARCHAR},
      rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      active_status = #{record.activeStatus,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      last_event_time = #{record.lastEventTime,jdbcType=TIMESTAMP},
      delivery_done_time = #{record.deliveryDoneTime,jdbcType=TIMESTAMP},
      version = #{record.version,jdbcType=INTEGER},
      delivery_fee = #{record.deliveryFee,jdbcType=DECIMAL},
      distance = #{record.distance,jdbcType=BIGINT},
      cancel_mark = #{record.cancelMark,jdbcType=TINYINT},
      delivery_exception_code = #{record.deliveryExceptionCode,jdbcType=TINYINT},
      tip_amount = #{record.tipAmount,jdbcType=DECIMAL},
      delivery_count = #{record.deliveryCount,jdbcType=INTEGER},
      platform_fee = #{record.platformFee,jdbcType=DECIMAL},
      current_status_lock = #{record.currentStatusLock,jdbcType=BIT},
      total_lock_count = #{record.totalLockCount,jdbcType=INTEGER},
      current_lock_time = #{record.currentLockTime,jdbcType=TIMESTAMP},
      current_unlock_time = #{record.currentUnlockTime,jdbcType=TIMESTAMP},
      ext_info = #{record.extInfo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDO">
    update delivery_order
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="fulfillmentOrderId != null">
        fulfillment_order_id = #{fulfillmentOrderId,jdbcType=BIGINT},
      </if>
      <if test="platformSource != null">
        platform_source = #{platformSource,jdbcType=INTEGER},
      </if>
      <if test="channelOrderId != null">
        channel_order_id = #{channelOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderBizType != null">
        order_biz_type = #{orderBizType,jdbcType=INTEGER},
      </if>
      <if test="daySeq != null">
        day_seq = #{daySeq,jdbcType=INTEGER},
      </if>
      <if test="reserved != null">
        reserved = #{reserved,jdbcType=TINYINT},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="receiverName != null">
        receiver_name = #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="receiverPhone != null">
        receiver_phone = #{receiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiverPrivacyPhone != null">
        receiver_privacy_phone = #{receiverPrivacyPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiverAddress != null">
        receiver_address = #{receiverAddress,jdbcType=CHAR},
      </if>
      <if test="estimatedDeliveryTime != null">
        estimated_delivery_time = #{estimatedDeliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estimatedDeliveryEndTime != null">
        estimated_delivery_end_time = #{estimatedDeliveryEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryChannel != null">
        delivery_channel = #{deliveryChannel,jdbcType=INTEGER},
      </if>
      <if test="channelDeliveryId != null">
        channel_delivery_id = #{channelDeliveryId,jdbcType=VARCHAR},
      </if>
      <if test="channelServicePackageCode != null">
        channel_service_package_code = #{channelServicePackageCode,jdbcType=VARCHAR},
      </if>
      <if test="deliveryStatus != null">
        delivery_status = #{deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryExceptionType != null">
        delivery_exception_type = #{deliveryExceptionType,jdbcType=TINYINT},
      </if>
      <if test="deliveryExceptionDescription != null">
        delivery_exception_description = #{deliveryExceptionDescription,jdbcType=VARCHAR},
      </if>
      <if test="riderName != null">
        rider_name = #{riderName,jdbcType=VARCHAR},
      </if>
      <if test="riderPhone != null">
        rider_phone = #{riderPhone,jdbcType=VARCHAR},
      </if>
      <if test="riderPhoneToken != null">
        rider_phone_token = #{riderPhoneToken,jdbcType=VARCHAR},
      </if>
      <if test="riderAccountId != null">
        rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="activeStatus != null">
        active_status = #{activeStatus,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastEventTime != null">
        last_event_time = #{lastEventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryDoneTime != null">
        delivery_done_time = #{deliveryDoneTime,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="deliveryFee != null">
        delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="distance != null">
        distance = #{distance,jdbcType=BIGINT},
      </if>
      <if test="cancelMark != null">
        cancel_mark = #{cancelMark,jdbcType=TINYINT},
      </if>
      <if test="deliveryExceptionCode != null">
        delivery_exception_code = #{deliveryExceptionCode,jdbcType=TINYINT},
      </if>
      <if test="tipAmount != null">
        tip_amount = #{tipAmount,jdbcType=DECIMAL},
      </if>
      <if test="deliveryCount != null">
        delivery_count = #{deliveryCount,jdbcType=INTEGER},
      </if>
      <if test="platformFee != null">
        platform_fee = #{platformFee,jdbcType=DECIMAL},
      </if>
      <if test="currentStatusLock != null">
        current_status_lock = #{currentStatusLock,jdbcType=BIT},
      </if>
      <if test="totalLockCount != null">
        total_lock_count = #{totalLockCount,jdbcType=INTEGER},
      </if>
      <if test="currentLockTime != null">
        current_lock_time = #{currentLockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="currentUnlockTime != null">
        current_unlock_time = #{currentUnlockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDO">
    update delivery_order
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      fulfillment_order_id = #{fulfillmentOrderId,jdbcType=BIGINT},
      platform_source = #{platformSource,jdbcType=INTEGER},
      channel_order_id = #{channelOrderId,jdbcType=VARCHAR},
      order_biz_type = #{orderBizType,jdbcType=INTEGER},
      day_seq = #{daySeq,jdbcType=INTEGER},
      reserved = #{reserved,jdbcType=TINYINT},
      order_source = #{orderSource,jdbcType=TINYINT},
      receiver_name = #{receiverName,jdbcType=VARCHAR},
      receiver_phone = #{receiverPhone,jdbcType=VARCHAR},
      receiver_privacy_phone = #{receiverPrivacyPhone,jdbcType=VARCHAR},
      receiver_address = #{receiverAddress,jdbcType=CHAR},
      estimated_delivery_time = #{estimatedDeliveryTime,jdbcType=TIMESTAMP},
      estimated_delivery_end_time = #{estimatedDeliveryEndTime,jdbcType=TIMESTAMP},
      delivery_channel = #{deliveryChannel,jdbcType=INTEGER},
      channel_delivery_id = #{channelDeliveryId,jdbcType=VARCHAR},
      channel_service_package_code = #{channelServicePackageCode,jdbcType=VARCHAR},
      delivery_status = #{deliveryStatus,jdbcType=INTEGER},
      delivery_exception_type = #{deliveryExceptionType,jdbcType=TINYINT},
      delivery_exception_description = #{deliveryExceptionDescription,jdbcType=VARCHAR},
      rider_name = #{riderName,jdbcType=VARCHAR},
      rider_phone = #{riderPhone,jdbcType=VARCHAR},
      rider_phone_token = #{riderPhoneToken,jdbcType=VARCHAR},
      rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      active_status = #{activeStatus,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      last_event_time = #{lastEventTime,jdbcType=TIMESTAMP},
      delivery_done_time = #{deliveryDoneTime,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=INTEGER},
      delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      distance = #{distance,jdbcType=BIGINT},
      cancel_mark = #{cancelMark,jdbcType=TINYINT},
      delivery_exception_code = #{deliveryExceptionCode,jdbcType=TINYINT},
      tip_amount = #{tipAmount,jdbcType=DECIMAL},
      delivery_count = #{deliveryCount,jdbcType=INTEGER},
      platform_fee = #{platformFee,jdbcType=DECIMAL},
      current_status_lock = #{currentStatusLock,jdbcType=BIT},
      total_lock_count = #{totalLockCount,jdbcType=INTEGER},
      current_lock_time = #{currentLockTime,jdbcType=TIMESTAMP},
      current_unlock_time = #{currentUnlockTime,jdbcType=TIMESTAMP},
      ext_info = #{extInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>