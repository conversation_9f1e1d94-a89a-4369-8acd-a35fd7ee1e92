<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx.xsd">

<!--    <bean id="deliveryDataSource" class="com.dianping.zebra.group.jdbc.GroupDataSource" init-method="init"-->
<!--          destroy-method="close">-->
<!--        &lt;!&ndash; 必配。指定唯一确定数据库的key&ndash;&gt;-->
<!--        <property name="jdbcRef" value="${delivery.zebra.jdbcRef}"/>-->
<!--        &lt;!&ndash; 选配。指定底层使用的连接池类型，支持"c3p0","tomcat-jdbc","druid","hikaricp","dbcp2"和"dbcp"，推荐使用"tomcat-jdbc"，默认值为"c3p0" &ndash;&gt;-->
<!--        <property name="poolType" value="${delivery.zebra.poolType}"/>-->
<!--        &lt;!&ndash; 选配。指定连接池的最小连接数，默认值是5。 &ndash;&gt;-->
<!--        <property name="minPoolSize" value="${delivery.zebra.minPoolSize}"/>-->
<!--        &lt;!&ndash; 选配。指定连接池的最大连接数，默认值是20。 &ndash;&gt;-->
<!--        <property name="maxPoolSize" value="${delivery.zebra.maxPoolSize}"/>-->
<!--        &lt;!&ndash; 选配。指定连接池的初始化连接数，默认值是5。 &ndash;&gt;-->
<!--        <property name="initialPoolSize" value="${delivery.zebra.initialPoolSize}"/>-->
<!--        &lt;!&ndash; 选配。指定连接池的获取连接的超时时间，默认值是1000。 &ndash;&gt;-->
<!--        <property name="checkoutTimeout" value="${delivery.zebra.checkoutTimeout}"/>-->
<!--        &lt;!&ndash; jdbcdriver到高版本之后，需要强制指定useSSL &ndash;&gt;-->
<!--        <property name="extraJdbcUrlParams" value="useSSL=false"/>-->
<!--        &lt;!&ndash;以下配置全部可以选配&ndash;&gt;-->
<!--        <property name="maxIdleTime" value="${delivery.zebra.maxIdleTime}"/>-->
<!--        <property name="idleConnectionTestPeriod" value="${delivery.zebra.idleConnectionTestPeriod}"/>-->
<!--        <property name="acquireRetryAttempts" value="${delivery.zebra.acquireRetryAttempts}"/>-->
<!--        <property name="acquireRetryDelay" value="${delivery.zebra.acquireRetryDelay}"/>-->
<!--        <property name="maxStatements" value="${delivery.zebra.maxStatements}"/>-->
<!--        <property name="maxStatementsPerConnection" value="${delivery.zebra.maxStatementsPerConnection}"/>-->
<!--        <property name="numHelperThreads" value="${delivery.zebra.numHelperThreads}"/>-->
<!--        <property name="maxAdministrativeTaskTime" value="${delivery.zebra.maxAdministrativeTaskTime}"/>-->
<!--        <property name="preferredTestQuery" value="${delivery.zebra.preferredTestQuery}"/>-->
<!--        <property name="useLiteSet" value="true" />-->
<!--    </bean>-->

    <bean id="deliveryDataSource" class="com.dianping.zebra.shard.jdbc.ShardDataSource" init-method="init" destroy-method="close">
        <!-- 必配。指定唯一确定数据库的key-->
        <property name="useLiteSet" value="true" />
        <property name="ruleName" value="${delivery.zebra.qnhRuleName}"/>
        <!-- 选配。指定底层使用的连接池类型，支持"c3p0","tomcat-jdbc","druid","hikaricp","dbcp2"和"dbcp"，推荐使用"druid"或者"dbcp2"，版本2.10.3之后默认值为"druid"，之前版本默认值为"c3p0" -->
        <property name="poolType" value="druid"/>
        <!-- 选配。指定连接池的最小连接数，默认值是5。 -->
        <property name="minPoolSize" value="20"/>
        <!-- 选配。指定连接池的最大连接数，默认值是20。 -->
        <property name="maxPoolSize" value="100"/>
        <!-- 选配。指定连接池的初始化连接数，默认值是5。 -->
        <property name="initialPoolSize" value="10"/>
        <!-- 选配。指定连接池的获取连接的超时时间，默认值是1000。 -->
        <property name="checkoutTimeout" value="1000"/>
        <!--https://km.sankuai.com/page/58274218-->
        <property name="extraJdbcUrlParams" value="useSSL=false&amp;allowMultiQueries=true&amp;zeroDateTimeBehavior=convertToNull&amp;useAffectedRows=true"/>
        <property name="maxIdleTime" value="1800"/>
        <property name="idleConnectionTestPeriod" value="60"/>
        <property name="acquireRetryAttempts" value="3"/>
        <property name="acquireRetryDelay" value="300"/>
        <property name="maxStatements" value="0"/>
        <property name="maxStatementsPerConnection" value="100"/>
        <property name="numHelperThreads" value="6"/>
        <property name="maxAdministrativeTaskTime" value="5"/>
        <property name="preferredTestQuery" value="SELECT 1"/>
        <property name="concurrencyLevel" value="5" />
        <property name="lazyInit" value="false"/>
        <property name="parallelExecuteTimeOut" value="5000"/>
    </bean>

    <!--如果本来使用的就是mybatis，主要就是替换掉这个配置-->
    <bean class="com.dianping.zebra.dao.mybatis.ZebraMapperScannerConfigurer">
        <!--这里改成实际dao目录,如果有多个，可以用,;\t\n进行分割-->
        <property name="sqlSessionFactoryBeanName" value="deliverySqlSessionFactory"/>
        <property name="basePackage" value="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper"/>
    </bean>

    <bean id="routingDatasource" class="com.dianping.zebra.dao.datasource.ZebraRoutingDataSource">
        <property name="targetDataSources">
            <!--其中key属性将在后面的@ZebraRouting注解中使用到-->
            <map>
                <!-- 注意这里的key必须填写对应数据源的jdbcref -->
                <entry key="dataRef" value-ref="deliveryDataSource"/>
            </map>
        </property>

        <!--在未指定的情况下，默认走的数据源-->
        <property name="defaultTargetDataSource" value="dataRef"/>
        <property name="packageDataSourceKeyMap">
            <map>
                <entry key="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper" value="dataRef"/>
            </map>
        </property>
    </bean>



    <bean id="deliverySqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean" primary="true">
        <!--dataource-->
        <property name="dataSource" ref="routingDatasource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <!--Mapper files-->
        <property name="mapperLocations">
            <array>
                <value>classpath*:/mapper/*.xml</value>
                <value>classpath*:/mapper/extension/*.xml</value>
            </array>
        </property>
        <!--这里改成实际entity目录,如果有多个，可以用,;\t\n进行分割-->
        <property name="typeAliasesPackage" value="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model"/>
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <!--使用下面的方式配置参数，一行配置一个 -->
                        <value>
                            helperDialect=mysql
                            reasonable=false
                            supportMethodsArguments=true
                            params=count=countSql
                        </value>
                    </property>
                </bean>
                <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mybatis.ZebraForceMasterInterceptor"/>
            </array>
        </property>
    </bean>

    <bean id="mybatisTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager" primary="true">
        <property name="dataSource" ref="routingDatasource"/>
    </bean>

<!--    <tx:annotation-driven transaction-manager="mybatisTransactionManager"-->
<!--                          proxy-target-class="true"/>-->

    <bean id="transactionTemplate"
          class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="mybatisTransactionManager"/>
    </bean>
</beans>
