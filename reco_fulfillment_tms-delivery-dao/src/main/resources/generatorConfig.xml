<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="MybatisGenerator" targetRuntime="MyBatis3">
        <plugin type="com.sankuai.MySQLLimitPlugin"/>
        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"/>
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="true"/>
            <property name="javaFileEncoding" value="UTF-8"/>
        </commentGenerator>

        <!--load from properties-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="**********************************************"
                        userId="rds_delivery"
                        password="Rqg%^t#4&amp;LjyKh">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="useJSR310Types" value="true"/>
        </javaTypeResolver>

        <!--load targetProject from properties-->
        <javaModelGenerator targetPackage="com.sankuai.meituan.shangou.empower.tms.delivery.dao.model"
                            targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!--load targetProject from properties-->
        <sqlMapGenerator targetPackage="mapper"
                         targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!--load targetProject from properties-->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>


<!--        <table tableName="delivery_config" domainObjectName="DeliveryConfigDO">-->
<!--&lt;!&ndash;            <generatedKey column="id" sqlStatement="MySql" identity="true"/>&ndash;&gt;-->
<!--            <columnOverride column="enabled" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="is_del" javaType="java.lang.Integer"/>-->
<!--        </table>-->
<!--        <table tableName="delivery_remind_config" domainObjectName="DeliveryRemindConfigDO">-->
<!--&lt;!&ndash;            <generatedKey column="id" sqlStatement="MySql" identity="true"/>&ndash;&gt;-->
<!--        </table>-->

        <table tableName="store_config" domainObjectName="StoreConfigDO">
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
            <columnOverride column="delivery_strategy" javaType="java.lang.Integer"/>
            <columnOverride column="delivery_launch_point" javaType="java.lang.Integer"/>
            <columnOverride column="booking_order_delivery_launch_point" javaType="java.lang.Integer"/>
            <columnOverride column="enabled" javaType="java.lang.Integer"/>
            <columnOverride column="is_auto_launch" javaType="java.lang.Integer"/>
            <columnOverride column="delivery_launch_rule" javaType="java.lang.Integer"/>
            <columnOverride column="open_aggr_platform" javaType="java.lang.Integer"/>
            <columnOverride column="is_show_item_number" javaType="java.lang.Integer"/>
        </table>

        <table tableName="store_dimension_config" domainObjectName="StoreDimensionConfigDO">
            <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
            <columnOverride column="self_delivery_mode" javaType="java.lang.Integer"/>
            <columnOverride column="internal_navigation_mode" javaType="java.lang.Integer"/>
            <columnOverride column="completed_sort_mode" javaType="java.lang.Integer"/>
            <columnOverride column="enable" javaType="java.lang.Integer"/>
        </table>

<!--        <table tableName="shop_delivery_config" domainObjectName="ShopDeliveryConfigDO" >-->
<!--&lt;!&ndash;            <generatedKey column="id" sqlStatement="MySql" identity="true"/>&ndash;&gt;-->
<!--            <columnOverride column="is_del" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="enabled" javaType="java.lang.Integer"/>-->
<!--        </table>-->

<!--        <table tableName="rider_delivery_statistics" domainObjectName="RiderDeliveryStatisticDO" >-->
<!--            &lt;!&ndash;            <generatedKey column="id" sqlStatement="MySql" identity="true"/>&ndash;&gt;-->
<!--        </table>-->

<!--         <table tableName="delivery_order_route" domainObjectName="DeliveryOrderRoute" >-->
<!--&lt;!&ndash;            <generatedKey column="id" sqlStatement="MySql" identity="true"/>&ndash;&gt;-->
<!--        </table>-->
<!--         <table tableName="data_ready_record" domainObjectName="DataReadyRecord" >-->
<!--&lt;!&ndash;            <generatedKey column="id" sqlStatement="MySql" identity="true"/>&ndash;&gt;-->
<!--        </table>-->

<!--        <table tableName="delivery_risk_control_order" domainObjectName="DeliveryRiskControlOrderDO" >-->
<!--            &lt;!&ndash;            <generatedKey column="id" sqlStatement="MySql" identity="true"/>&ndash;&gt;-->
<!--        </table>-->

<!--  <table tableName="delivery_order" domainObjectName="DeliveryOrderDO">-->
<!--&lt;!&ndash;            <generatedKey column="id" sqlStatement="MySql" identity="true"/>&ndash;&gt;-->
<!--            <columnOverride column="delivery_exception_type" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="cancel_mark" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="current_status_lock" javaType="java.lang.Integer"/>-->

<!--            <columnOverride column="receiver_address_coordinate_type" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="valid" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="order_source" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="delivery_exception_code" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="reserved" javaType="java.lang.Integer"/>-->

<!--        </table>-->

<!--        <table tableName="delivery_order_exception_report" domainObjectName="RiderDeliveryExceptionDO">-->
<!--&lt;!&ndash;            <generatedKey column="id" sqlStatement="MySql" identity="true"/>&ndash;&gt;-->
<!--            <columnOverride column="order_biz_type" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="report_exception_type" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="report_exception_sub_type" javaType="java.lang.Integer"/>-->
<!--        </table>-->

<!--        <table tableName="rider_arrival_location" domainObjectName="RiderArrivalLocationDO">-->
<!--&lt;!&ndash;            <generatedKey column="id" sqlStatement="MySql" identity="true"/>&ndash;&gt;-->
<!--        </table>-->

<!--        <table tableName="delivery_order_log" domainObjectName="DeliveryOrderLogDO">-->
<!--&lt;!&ndash;            <generatedKey column="id" sqlStatement="MySql" identity="true"/>&ndash;&gt;-->
<!--            <columnOverride column="delivery_status" javaType="java.lang.Integer"/>-->
<!--        </table>-->

<!--        <table tableName="delivery_merge_order_record" domainObjectName="DeliveryMergeOrderRecordDO">-->
<!--&lt;!&ndash;            <generatedKey column="id" sqlStatement="MySql" identity="true"/>&ndash;&gt;-->
<!--            <columnOverride column="order_biz_type" javaType="java.lang.Integer"/>-->
<!--        </table>-->

<!--        <table tableName="delivery_channel" domainObjectName="DeliveryChannelDO">-->
<!--&lt;!&ndash;            <generatedKey column="id" sqlStatement="MySql" identity="true"/>&ndash;&gt;-->
<!--            <columnOverride column="logistic_mark" javaType="java.lang.String"/>-->
<!--            <columnOverride column="delivery_platform_code" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="carrier_code" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="carrier_name" javaType="java.lang.String"/>-->
<!--            <columnOverride column="order_channel_code" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="ctime" javaType="java.sql.Date"/>-->
<!--            <columnOverride column="utime" javaType="java.sql.Date"/>-->
<!--        </table>-->

<!--        <table tableName="pricing_route_info" domainObjectName="PricingRouteInfoDO">-->
<!--&lt;!&ndash;            <generatedKey column="id" sqlStatement="MySql" identity="true"/>&ndash;&gt;-->
<!--        </table>-->

    </context>
</generatorConfiguration>
