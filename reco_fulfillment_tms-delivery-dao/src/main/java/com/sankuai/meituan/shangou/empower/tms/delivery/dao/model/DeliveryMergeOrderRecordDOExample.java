package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class DeliveryMergeOrderRecordDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public DeliveryMergeOrderRecordDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdIsNull() {
            addCriterion("delivery_order_id is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdIsNotNull() {
            addCriterion("delivery_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdEqualTo(Long value) {
            addCriterion("delivery_order_id =", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdNotEqualTo(Long value) {
            addCriterion("delivery_order_id <>", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdGreaterThan(Long value) {
            addCriterion("delivery_order_id >", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("delivery_order_id >=", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdLessThan(Long value) {
            addCriterion("delivery_order_id <", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdLessThanOrEqualTo(Long value) {
            addCriterion("delivery_order_id <=", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdIn(List<Long> values) {
            addCriterion("delivery_order_id in", values, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdNotIn(List<Long> values) {
            addCriterion("delivery_order_id not in", values, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdBetween(Long value1, Long value2) {
            addCriterion("delivery_order_id between", value1, value2, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdNotBetween(Long value1, Long value2) {
            addCriterion("delivery_order_id not between", value1, value2, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andMergeIdIsNull() {
            addCriterion("merge_id is null");
            return (Criteria) this;
        }

        public Criteria andMergeIdIsNotNull() {
            addCriterion("merge_id is not null");
            return (Criteria) this;
        }

        public Criteria andMergeIdEqualTo(Long value) {
            addCriterion("merge_id =", value, "mergeId");
            return (Criteria) this;
        }

        public Criteria andMergeIdNotEqualTo(Long value) {
            addCriterion("merge_id <>", value, "mergeId");
            return (Criteria) this;
        }

        public Criteria andMergeIdGreaterThan(Long value) {
            addCriterion("merge_id >", value, "mergeId");
            return (Criteria) this;
        }

        public Criteria andMergeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("merge_id >=", value, "mergeId");
            return (Criteria) this;
        }

        public Criteria andMergeIdLessThan(Long value) {
            addCriterion("merge_id <", value, "mergeId");
            return (Criteria) this;
        }

        public Criteria andMergeIdLessThanOrEqualTo(Long value) {
            addCriterion("merge_id <=", value, "mergeId");
            return (Criteria) this;
        }

        public Criteria andMergeIdIn(List<Long> values) {
            addCriterion("merge_id in", values, "mergeId");
            return (Criteria) this;
        }

        public Criteria andMergeIdNotIn(List<Long> values) {
            addCriterion("merge_id not in", values, "mergeId");
            return (Criteria) this;
        }

        public Criteria andMergeIdBetween(Long value1, Long value2) {
            addCriterion("merge_id between", value1, value2, "mergeId");
            return (Criteria) this;
        }

        public Criteria andMergeIdNotBetween(Long value1, Long value2) {
            addCriterion("merge_id not between", value1, value2, "mergeId");
            return (Criteria) this;
        }

        public Criteria andActiveStatusIsNull() {
            addCriterion("active_status is null");
            return (Criteria) this;
        }

        public Criteria andActiveStatusIsNotNull() {
            addCriterion("active_status is not null");
            return (Criteria) this;
        }

        public Criteria andActiveStatusEqualTo(Long value) {
            addCriterion("active_status =", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusNotEqualTo(Long value) {
            addCriterion("active_status <>", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusGreaterThan(Long value) {
            addCriterion("active_status >", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusGreaterThanOrEqualTo(Long value) {
            addCriterion("active_status >=", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusLessThan(Long value) {
            addCriterion("active_status <", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusLessThanOrEqualTo(Long value) {
            addCriterion("active_status <=", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusIn(List<Long> values) {
            addCriterion("active_status in", values, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusNotIn(List<Long> values) {
            addCriterion("active_status not in", values, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusBetween(Long value1, Long value2) {
            addCriterion("active_status between", value1, value2, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusNotBetween(Long value1, Long value2) {
            addCriterion("active_status not between", value1, value2, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdIsNull() {
            addCriterion("channel_order_id is null");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdIsNotNull() {
            addCriterion("channel_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdEqualTo(String value) {
            addCriterion("channel_order_id =", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotEqualTo(String value) {
            addCriterion("channel_order_id <>", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdGreaterThan(String value) {
            addCriterion("channel_order_id >", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("channel_order_id >=", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdLessThan(String value) {
            addCriterion("channel_order_id <", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdLessThanOrEqualTo(String value) {
            addCriterion("channel_order_id <=", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdLike(String value) {
            addCriterion("channel_order_id like", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotLike(String value) {
            addCriterion("channel_order_id not like", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdIn(List<String> values) {
            addCriterion("channel_order_id in", values, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotIn(List<String> values) {
            addCriterion("channel_order_id not in", values, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdBetween(String value1, String value2) {
            addCriterion("channel_order_id between", value1, value2, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotBetween(String value1, String value2) {
            addCriterion("channel_order_id not between", value1, value2, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeIsNull() {
            addCriterion("order_biz_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeIsNotNull() {
            addCriterion("order_biz_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeEqualTo(Integer value) {
            addCriterion("order_biz_type =", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeNotEqualTo(Integer value) {
            addCriterion("order_biz_type <>", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeGreaterThan(Integer value) {
            addCriterion("order_biz_type >", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_biz_type >=", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeLessThan(Integer value) {
            addCriterion("order_biz_type <", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeLessThanOrEqualTo(Integer value) {
            addCriterion("order_biz_type <=", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeIn(List<Integer> values) {
            addCriterion("order_biz_type in", values, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeNotIn(List<Integer> values) {
            addCriterion("order_biz_type not in", values, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeBetween(Integer value1, Integer value2) {
            addCriterion("order_biz_type between", value1, value2, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("order_biz_type not between", value1, value2, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIsNull() {
            addCriterion("rider_account_id is null");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIsNotNull() {
            addCriterion("rider_account_id is not null");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdEqualTo(Long value) {
            addCriterion("rider_account_id =", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotEqualTo(Long value) {
            addCriterion("rider_account_id <>", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdGreaterThan(Long value) {
            addCriterion("rider_account_id >", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rider_account_id >=", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdLessThan(Long value) {
            addCriterion("rider_account_id <", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdLessThanOrEqualTo(Long value) {
            addCriterion("rider_account_id <=", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIn(List<Long> values) {
            addCriterion("rider_account_id in", values, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotIn(List<Long> values) {
            addCriterion("rider_account_id not in", values, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdBetween(Long value1, Long value2) {
            addCriterion("rider_account_id between", value1, value2, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotBetween(Long value1, Long value2) {
            addCriterion("rider_account_id not between", value1, value2, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}