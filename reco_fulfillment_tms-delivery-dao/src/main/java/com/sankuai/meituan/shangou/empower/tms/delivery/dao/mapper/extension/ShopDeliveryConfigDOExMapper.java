package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.request.GetShopDeliveryConfigCondition;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mybatis.SlaveQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShopDeliveryConfigDOExMapper {

	int batchUpdate(@Param("recordList") List<ShopDeliveryConfigDO> recordList);

	int batchInsert(@Param("recordList") List<ShopDeliveryConfigDO> recordList);

	int batchDelete(@Param("tenantId") long tenantId,
	                @Param("storeId") long storeId,
	                @Param("operatorId") long operatorId,
	                @Param("operatorName") String operatorName);

	List<ShopDeliveryConfigDO> queryShopDeliveryConfigByCondition(@Param("condition") GetShopDeliveryConfigCondition condition);

	@SlaveQuery
	List<ShopDeliveryConfigDO> selectByExampleSlave(ShopDeliveryConfigDOExample example);
}
