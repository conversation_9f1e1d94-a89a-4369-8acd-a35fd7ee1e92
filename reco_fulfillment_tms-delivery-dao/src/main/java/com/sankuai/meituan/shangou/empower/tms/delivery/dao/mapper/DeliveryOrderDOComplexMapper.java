package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mybatis.SlaveQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DeliveryOrderDOComplexMapper {

    // 只有orderId
    List<DeliveryOrderIdDO> selectOrderIdList(DeliveryOrderDOExample example);

    List<DeliveryIdDO> selectIdList(DeliveryOrderDOExample example);

    @SlaveQuery
    List<DeliveryOrderDO> selectByExampleSlave(DeliveryOrderDOExample example);

    @SlaveQuery
    long countByExampleSlave(DeliveryOrderDOExample example);

    List<DeliveryOrderDO> selectByExampleWithStoreList(DeliveryOrderDOExample example);

    @SlaveQuery
    long countByExampleWithStoreListSlave(DeliveryOrderDOExample example);

    long countByExampleWithStoreList(DeliveryOrderDOExample example);
    int insertSelective(DeliveryOrderDO record);

    List<CountGroupExDO> queryStoreDeliveringOrderCount(@Param("storeIds") List<Long> storeIds, @Param("status") List<Integer> status,@Param("tenantId")Long tenantId);

}
