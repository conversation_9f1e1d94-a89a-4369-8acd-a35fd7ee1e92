package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.config;

import com.sankuai.meituan.poros.client.PorosHighLevelClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class EsConfiguration {

    /**
     * es客户端
     */
    @Bean
    public RestHighLevelClient restHighLevelClient(
            @Value("${app.name}") String appKey,
            @Value("${eagle.clusterName}") String clusterName,
            @Value("${eagle.accessKey}") String accessKey) {
        return PorosHighLevelClientBuilder.builder()
                .appKey(appKey)
                .clusterName(clusterName)
                .accessKey(accessKey)
                .timeoutMillis(7_000)
                .build();
    }
}
