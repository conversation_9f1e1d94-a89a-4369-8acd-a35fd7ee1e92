package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.time.LocalDateTime;

public class RiderDeliveryExceptionDO {
    private Long id;

    private Long tenantId;

    private Long storeId;

    private Long deliveryOrderId;

    private LocalDateTime payTime;

    private String channelOrderId;

    private Integer orderBizType;

    private Integer daySeq;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String riderName;

    private Long riderAccountId;

    private Integer reportExceptionType;

    private Integer reportExceptionSubType;

    private String reportExceptionDescription;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Long getDeliveryOrderId() {
        return deliveryOrderId;
    }

    public void setDeliveryOrderId(Long deliveryOrderId) {
        this.deliveryOrderId = deliveryOrderId;
    }

    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }

    public String getChannelOrderId() {
        return channelOrderId;
    }

    public void setChannelOrderId(String channelOrderId) {
        this.channelOrderId = channelOrderId == null ? null : channelOrderId.trim();
    }

    public Integer getOrderBizType() {
        return orderBizType;
    }

    public void setOrderBizType(Integer orderBizType) {
        this.orderBizType = orderBizType;
    }

    public Integer getDaySeq() {
        return daySeq;
    }

    public void setDaySeq(Integer daySeq) {
        this.daySeq = daySeq;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getRiderName() {
        return riderName;
    }

    public void setRiderName(String riderName) {
        this.riderName = riderName == null ? null : riderName.trim();
    }

    public Long getRiderAccountId() {
        return riderAccountId;
    }

    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    public Integer getReportExceptionType() {
        return reportExceptionType;
    }

    public void setReportExceptionType(Integer reportExceptionType) {
        this.reportExceptionType = reportExceptionType;
    }

    public Integer getReportExceptionSubType() {
        return reportExceptionSubType;
    }

    public void setReportExceptionSubType(Integer reportExceptionSubType) {
        this.reportExceptionSubType = reportExceptionSubType;
    }

    public String getReportExceptionDescription() {
        return reportExceptionDescription;
    }

    public void setReportExceptionDescription(String reportExceptionDescription) {
        this.reportExceptionDescription = reportExceptionDescription == null ? null : reportExceptionDescription.trim();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RiderDeliveryExceptionDO other = (RiderDeliveryExceptionDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getDeliveryOrderId() == null ? other.getDeliveryOrderId() == null : this.getDeliveryOrderId().equals(other.getDeliveryOrderId()))
            && (this.getPayTime() == null ? other.getPayTime() == null : this.getPayTime().equals(other.getPayTime()))
            && (this.getChannelOrderId() == null ? other.getChannelOrderId() == null : this.getChannelOrderId().equals(other.getChannelOrderId()))
            && (this.getOrderBizType() == null ? other.getOrderBizType() == null : this.getOrderBizType().equals(other.getOrderBizType()))
            && (this.getDaySeq() == null ? other.getDaySeq() == null : this.getDaySeq().equals(other.getDaySeq()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getRiderName() == null ? other.getRiderName() == null : this.getRiderName().equals(other.getRiderName()))
            && (this.getRiderAccountId() == null ? other.getRiderAccountId() == null : this.getRiderAccountId().equals(other.getRiderAccountId()))
            && (this.getReportExceptionType() == null ? other.getReportExceptionType() == null : this.getReportExceptionType().equals(other.getReportExceptionType()))
            && (this.getReportExceptionSubType() == null ? other.getReportExceptionSubType() == null : this.getReportExceptionSubType().equals(other.getReportExceptionSubType()))
            && (this.getReportExceptionDescription() == null ? other.getReportExceptionDescription() == null : this.getReportExceptionDescription().equals(other.getReportExceptionDescription()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getDeliveryOrderId() == null) ? 0 : getDeliveryOrderId().hashCode());
        result = prime * result + ((getPayTime() == null) ? 0 : getPayTime().hashCode());
        result = prime * result + ((getChannelOrderId() == null) ? 0 : getChannelOrderId().hashCode());
        result = prime * result + ((getOrderBizType() == null) ? 0 : getOrderBizType().hashCode());
        result = prime * result + ((getDaySeq() == null) ? 0 : getDaySeq().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getRiderName() == null) ? 0 : getRiderName().hashCode());
        result = prime * result + ((getRiderAccountId() == null) ? 0 : getRiderAccountId().hashCode());
        result = prime * result + ((getReportExceptionType() == null) ? 0 : getReportExceptionType().hashCode());
        result = prime * result + ((getReportExceptionSubType() == null) ? 0 : getReportExceptionSubType().hashCode());
        result = prime * result + ((getReportExceptionDescription() == null) ? 0 : getReportExceptionDescription().hashCode());
        return result;
    }
}