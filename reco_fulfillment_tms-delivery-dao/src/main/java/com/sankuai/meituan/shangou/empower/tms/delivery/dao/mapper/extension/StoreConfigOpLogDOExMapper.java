package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreConfigOpLogDO;

public interface StoreConfigOpLogDOExMapper {

	int batchInsert(@Param("recordList") List<StoreConfigOpLogDO> recordList);

	List<StoreConfigOpLogDO> queryByCondition(@Param("storeConfigId") Long storeConfigId,
											  @Param("startTime") LocalDateTime startTime,
											  @Param("endTime") LocalDateTime endTime);

}
