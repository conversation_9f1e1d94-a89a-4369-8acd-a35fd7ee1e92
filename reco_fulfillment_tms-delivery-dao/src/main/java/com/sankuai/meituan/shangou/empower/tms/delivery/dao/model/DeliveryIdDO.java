package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.time.LocalDateTime;

public class DeliveryIdDO {

    public static final long DELIVERY_ORDER_ACTIVE = 0L;

    private Long id;

    private Long activeStatus;

    private Long orderId;

    private Integer deliveryExceptionType;

    private String channelOrderId;

    private LocalDateTime createTime;

    public String getChannelOrderId() {
        return channelOrderId;
    }

    public void setChannelOrderId(String channelOrderId) {
        this.channelOrderId = channelOrderId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }


    public Long getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(Long activeStatus) {
        this.activeStatus = activeStatus;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getDeliveryExceptionType() {
        return deliveryExceptionType;
    }

    public void setDeliveryExceptionType(Integer deliveryExceptionType) {
        this.deliveryExceptionType = deliveryExceptionType;
    }

    public boolean isActive() {
        return this.activeStatus == DELIVERY_ORDER_ACTIVE;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}