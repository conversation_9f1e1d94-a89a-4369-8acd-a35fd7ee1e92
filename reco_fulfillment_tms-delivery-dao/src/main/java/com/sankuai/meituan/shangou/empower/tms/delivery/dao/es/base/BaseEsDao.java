package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.base;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.constants.EsCommonConstants;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.exception.EsBaseException;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.exception.EsClientException;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.exception.EsVersionConflictException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.DocWriteRequest;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.engine.VersionConflictEngineException;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static org.elasticsearch.client.RequestOptions.DEFAULT;

/**
 * 访问es的dao，一般继承这个dao即可
 *
 * <AUTHOR>
 * @since 2022/3/31
 */
@Slf4j
public abstract class BaseEsDao<T extends BaseEsPo> extends BaseEsReadDao<T> {

    /**
     * 保存文档，文档不存在则创建，存在则更新
     */
    public void save(T doc) {
        LocalDateTime indexKey = doc.getIndexKey();
        String index = Objects.isNull(indexKey) ? getIndex() : getIndex(indexKey);
        IndexRequest request = getIndexRequest(index, DocWriteRequest.OpType.INDEX, doc.getRoutingValue());
        request.id(doc.getId());
        request.source(serialize(doc), XContentType.JSON);
        IndexResponse response;
        try {
            response = client.index(request, DEFAULT);
        } catch (VersionConflictEngineException e) {
            log.error("[{}]save doc version conflict: {}", index, e, e);
            throw new EsVersionConflictException(index, e);
        } catch (Exception e) {
            log.error("[{}]save doc error: {}", index, e, e);
            throw new EsClientException(index, e);
        }
    }

    /**
     * 批量保存文档，文档不存在则创建，存在则更新
     */
    public void batchSave(List<T> docList) {
        String index = getIndex();
        List<List<T>> allDocList = Lists.partition(docList, EsCommonConstants.ES_BULK_MAX);
        allDocList.forEach(bulkList -> {
            BulkRequest request = new BulkRequest(index);
            bulkList.forEach(doc -> {
                IndexRequest indexRequest = new IndexRequest(index);
                indexRequest.id(doc.getId());
                indexRequest.source(serialize(doc), XContentType.JSON);
                request.add(indexRequest);
            });
            BulkResponse response;
            try {
                response = client.bulk(request, DEFAULT);
            } catch (Exception e) {
                log.error("[{}]Index bulkList doc error: {}", index, e, e);
                throw new EsClientException(index, e);
            }
        });
    }


    /**
     * 更新文档，文档不存在抛出异常
     */
    public void update(T doc) {
        String index = getIndex();
        Objects.requireNonNull(doc.getId(), "Updating doc's id must not be null");
        UpdateRequest request = getUpdateRequest(index, doc.getId(), doc.getRoutingValue()).doc(serialize(doc), XContentType.JSON);
        UpdateResponse response;
        try {
            response = client.update(request, DEFAULT);
        } catch (Exception e) {
            log.error("[{}]Update doc error: {}", index, e, e);
            throw new EsClientException(index, e);
        }
    }

    /**
     * 批量更新文档，文档不存在抛出异常
     */
    public void batchUpdate(List<T> docList) {
        String index = getIndex();
        List<List<T>> allDocList = Lists.partition(docList, EsCommonConstants.ES_BULK_MAX);
        allDocList.forEach(bulkList -> {
            BulkRequest request = new BulkRequest(index);
            bulkList.forEach(doc -> {
                Objects.requireNonNull(doc.getId(), "Updating doc's id must not be null");
                UpdateRequest updateRequest = new UpdateRequest(index, doc.getId());
                updateRequest.doc(serialize(doc), XContentType.JSON);
                request.add(updateRequest);
            });
            BulkResponse response;
            try {
                response = client.bulk(request, DEFAULT);
            } catch (Exception e) {
                log.error("[{}]Update bulkList doc error: {}", index, e, e);
                throw new EsClientException(index, e);
            }
        });
    }

    /**
     * 根据路由值更新文档
     * 使用es提供的基于version的乐观锁机制保证线程安全,否则会抛VersionConflictEngineException
     *
     * @param doc 文档
     */
    public void updateWithSeqNoAndPrimaryTerm(T doc) {
        LocalDateTime indexKey = doc.getIndexKey();
        String index = getIndex(indexKey);
        Objects.requireNonNull(doc.getId(), "Updating doc's id must not be null");
        Objects.requireNonNull(doc.getSeqNo(), "Updating doc's seqNo must not be null");
        Objects.requireNonNull(doc.getPrimaryTerm(), "Updating doc's primaryTerm must not be null");

        String docJson = serialize(doc);
        UpdateRequest request = getUpdateRequest(index, doc.getId(), doc.getRoutingValue())
                .doc(docJson, XContentType.JSON)
                .setIfSeqNo(doc.getSeqNo())
                .setIfPrimaryTerm(doc.getPrimaryTerm());
        UpdateResponse response;
        try {
            response = client.update(request, DEFAULT);
        } catch (VersionConflictEngineException e) {
            log.error("[{}]update doc version conflict: {}", index, e, e);
            throw new EsVersionConflictException(index, e);
        } catch (Exception e) {
            log.error("[{}]update doc error: {}", index, e, e);
            throw new EsClientException(index, e);
        }
    }

    /**
     * 批量更新或插入文档，文档不存在创建，存在则更新
     * 效果跟{@link #save(BaseEsPo)}一样
     *
     * @see #save(BaseEsPo)
     */
    public void batchUpsert(List<T> docList) {
        LocalDateTime indexKey = docList.get(0).getIndexKey();
        String index = indexKey != null ? getIndex(indexKey) : getIndex();
        List<List<T>> allDocList = Lists.partition(docList, EsCommonConstants.ES_BULK_MAX);
        allDocList.forEach(bulkList -> {
            BulkRequest request = new BulkRequest(index);
            bulkList.forEach(doc -> {
                Objects.requireNonNull(doc.getId(), "Updating doc's id must not be null");
                UpdateRequest updateRequest = new UpdateRequest(index, doc.getId());
                String docJson = serialize(doc);
                updateRequest.doc(docJson, XContentType.JSON)
                        .upsert(docJson, XContentType.JSON);
                request.add(updateRequest);
            });
            BulkResponse response;
            try {
                response = client.bulk(request, DEFAULT);
            } catch (Exception e) {
                log.error("[{}]Update bulkList doc error: {}", index, e, e);
                throw new EsClientException(index, e);
            }
        });
    }

    /**
     * 删除文档，文档不存在则抛出异常
     */
    public void delete(String id) {
        String index = getIndex();
        DeleteRequest request = new DeleteRequest(index, id);
        DeleteResponse response;
        try {
            response = client.delete(request, DEFAULT);
        } catch (Exception e) {
            log.error("[{}]Delete error: {}", e, e);
            throw new EsClientException(index, e);
        }
        DocWriteResponse.Result result = response.getResult();
        if (result != DocWriteResponse.Result.DELETED) {
            throw new EsBaseException(index, "Delete doc failed");
        }
    }

    public void batchDelete(List<String> idList) {
        String index = getIndex();
        BulkRequest request = new BulkRequest(index);
        idList.forEach(id -> {
            DeleteRequest deleteRequest = new DeleteRequest(index, id);
            request.add(deleteRequest);
        });
        BulkResponse response;
        try {
            response = client.bulk(request, DEFAULT);
        } catch (Exception e) {
            log.error("[{}]Delete error: {}", e, e);
            throw new EsClientException(index, e);
        }
        for (BulkItemResponse bulkItemResponse : response) {
            switch (bulkItemResponse.getOpType()) {
                case DELETE:
                    DeleteResponse deleteResponse = bulkItemResponse.getResponse();
                    DocWriteResponse.Result result = deleteResponse.getResult();
                    if (result != DocWriteResponse.Result.DELETED) {
                        throw new EsBaseException(index, "Delete docList failed");
                    }
                    break;
                default:
                    continue;
            }
        }
    }

    private IndexRequest getIndexRequest(String index, DocWriteRequest.OpType opType, String routingValue) {
        if (StringUtils.isNotEmpty(routingValue)) {
            return new IndexRequest(index).opType(opType).routing(routingValue);
        } else {
            return new IndexRequest(index).opType(opType);
        }
    }

    private UpdateRequest getUpdateRequest(String index, String id, String routingValue) {
        if (StringUtils.isNotEmpty(routingValue)) {
            return new UpdateRequest(index, id).routing(routingValue);
        } else {
            return new UpdateRequest(index, id);
        }
    }

    protected String serialize(T doc) {
        Objects.requireNonNull(doc, "doc must not be null");
        return objectMapper.writeValueAsString(doc);
    }

}
