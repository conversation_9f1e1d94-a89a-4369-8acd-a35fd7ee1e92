package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class RiderArrivalLocationDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public RiderArrivalLocationDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdIsNull() {
            addCriterion("delivery_order_id is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdIsNotNull() {
            addCriterion("delivery_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdEqualTo(Long value) {
            addCriterion("delivery_order_id =", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdNotEqualTo(Long value) {
            addCriterion("delivery_order_id <>", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdGreaterThan(Long value) {
            addCriterion("delivery_order_id >", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("delivery_order_id >=", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdLessThan(Long value) {
            addCriterion("delivery_order_id <", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdLessThanOrEqualTo(Long value) {
            addCriterion("delivery_order_id <=", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdIn(List<Long> values) {
            addCriterion("delivery_order_id in", values, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdNotIn(List<Long> values) {
            addCriterion("delivery_order_id not in", values, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdBetween(Long value1, Long value2) {
            addCriterion("delivery_order_id between", value1, value2, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdNotBetween(Long value1, Long value2) {
            addCriterion("delivery_order_id not between", value1, value2, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIsNull() {
            addCriterion("rider_account_id is null");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIsNotNull() {
            addCriterion("rider_account_id is not null");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdEqualTo(Long value) {
            addCriterion("rider_account_id =", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotEqualTo(Long value) {
            addCriterion("rider_account_id <>", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdGreaterThan(Long value) {
            addCriterion("rider_account_id >", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rider_account_id >=", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdLessThan(Long value) {
            addCriterion("rider_account_id <", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdLessThanOrEqualTo(Long value) {
            addCriterion("rider_account_id <=", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIn(List<Long> values) {
            addCriterion("rider_account_id in", values, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotIn(List<Long> values) {
            addCriterion("rider_account_id not in", values, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdBetween(Long value1, Long value2) {
            addCriterion("rider_account_id between", value1, value2, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotBetween(Long value1, Long value2) {
            addCriterion("rider_account_id not between", value1, value2, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andLongitudeIsNull() {
            addCriterion("longitude is null");
            return (Criteria) this;
        }

        public Criteria andLongitudeIsNotNull() {
            addCriterion("longitude is not null");
            return (Criteria) this;
        }

        public Criteria andLongitudeEqualTo(String value) {
            addCriterion("longitude =", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotEqualTo(String value) {
            addCriterion("longitude <>", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeGreaterThan(String value) {
            addCriterion("longitude >", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeGreaterThanOrEqualTo(String value) {
            addCriterion("longitude >=", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeLessThan(String value) {
            addCriterion("longitude <", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeLessThanOrEqualTo(String value) {
            addCriterion("longitude <=", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeLike(String value) {
            addCriterion("longitude like", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotLike(String value) {
            addCriterion("longitude not like", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeIn(List<String> values) {
            addCriterion("longitude in", values, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotIn(List<String> values) {
            addCriterion("longitude not in", values, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeBetween(String value1, String value2) {
            addCriterion("longitude between", value1, value2, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotBetween(String value1, String value2) {
            addCriterion("longitude not between", value1, value2, "longitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeIsNull() {
            addCriterion("latitude is null");
            return (Criteria) this;
        }

        public Criteria andLatitudeIsNotNull() {
            addCriterion("latitude is not null");
            return (Criteria) this;
        }

        public Criteria andLatitudeEqualTo(String value) {
            addCriterion("latitude =", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotEqualTo(String value) {
            addCriterion("latitude <>", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeGreaterThan(String value) {
            addCriterion("latitude >", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeGreaterThanOrEqualTo(String value) {
            addCriterion("latitude >=", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeLessThan(String value) {
            addCriterion("latitude <", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeLessThanOrEqualTo(String value) {
            addCriterion("latitude <=", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeLike(String value) {
            addCriterion("latitude like", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotLike(String value) {
            addCriterion("latitude not like", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeIn(List<String> values) {
            addCriterion("latitude in", values, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotIn(List<String> values) {
            addCriterion("latitude not in", values, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeBetween(String value1, String value2) {
            addCriterion("latitude between", value1, value2, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotBetween(String value1, String value2) {
            addCriterion("latitude not between", value1, value2, "latitude");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andLinearDistanceToReceiverIsNull() {
            addCriterion("linear_distance_to_receiver is null");
            return (Criteria) this;
        }

        public Criteria andLinearDistanceToReceiverIsNotNull() {
            addCriterion("linear_distance_to_receiver is not null");
            return (Criteria) this;
        }

        public Criteria andLinearDistanceToReceiverEqualTo(Long value) {
            addCriterion("linear_distance_to_receiver =", value, "linearDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andLinearDistanceToReceiverNotEqualTo(Long value) {
            addCriterion("linear_distance_to_receiver <>", value, "linearDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andLinearDistanceToReceiverGreaterThan(Long value) {
            addCriterion("linear_distance_to_receiver >", value, "linearDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andLinearDistanceToReceiverGreaterThanOrEqualTo(Long value) {
            addCriterion("linear_distance_to_receiver >=", value, "linearDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andLinearDistanceToReceiverLessThan(Long value) {
            addCriterion("linear_distance_to_receiver <", value, "linearDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andLinearDistanceToReceiverLessThanOrEqualTo(Long value) {
            addCriterion("linear_distance_to_receiver <=", value, "linearDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andLinearDistanceToReceiverIn(List<Long> values) {
            addCriterion("linear_distance_to_receiver in", values, "linearDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andLinearDistanceToReceiverNotIn(List<Long> values) {
            addCriterion("linear_distance_to_receiver not in", values, "linearDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andLinearDistanceToReceiverBetween(Long value1, Long value2) {
            addCriterion("linear_distance_to_receiver between", value1, value2, "linearDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andLinearDistanceToReceiverNotBetween(Long value1, Long value2) {
            addCriterion("linear_distance_to_receiver not between", value1, value2, "linearDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andNavigationDistanceToReceiverIsNull() {
            addCriterion("navigation_distance_to_receiver is null");
            return (Criteria) this;
        }

        public Criteria andNavigationDistanceToReceiverIsNotNull() {
            addCriterion("navigation_distance_to_receiver is not null");
            return (Criteria) this;
        }

        public Criteria andNavigationDistanceToReceiverEqualTo(Long value) {
            addCriterion("navigation_distance_to_receiver =", value, "navigationDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andNavigationDistanceToReceiverNotEqualTo(Long value) {
            addCriterion("navigation_distance_to_receiver <>", value, "navigationDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andNavigationDistanceToReceiverGreaterThan(Long value) {
            addCriterion("navigation_distance_to_receiver >", value, "navigationDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andNavigationDistanceToReceiverGreaterThanOrEqualTo(Long value) {
            addCriterion("navigation_distance_to_receiver >=", value, "navigationDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andNavigationDistanceToReceiverLessThan(Long value) {
            addCriterion("navigation_distance_to_receiver <", value, "navigationDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andNavigationDistanceToReceiverLessThanOrEqualTo(Long value) {
            addCriterion("navigation_distance_to_receiver <=", value, "navigationDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andNavigationDistanceToReceiverIn(List<Long> values) {
            addCriterion("navigation_distance_to_receiver in", values, "navigationDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andNavigationDistanceToReceiverNotIn(List<Long> values) {
            addCriterion("navigation_distance_to_receiver not in", values, "navigationDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andNavigationDistanceToReceiverBetween(Long value1, Long value2) {
            addCriterion("navigation_distance_to_receiver between", value1, value2, "navigationDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andNavigationDistanceToReceiverNotBetween(Long value1, Long value2) {
            addCriterion("navigation_distance_to_receiver not between", value1, value2, "navigationDistanceToReceiver");
            return (Criteria) this;
        }

        public Criteria andOsIsNull() {
            addCriterion("os is null");
            return (Criteria) this;
        }

        public Criteria andOsIsNotNull() {
            addCriterion("os is not null");
            return (Criteria) this;
        }

        public Criteria andOsEqualTo(String value) {
            addCriterion("os =", value, "os");
            return (Criteria) this;
        }

        public Criteria andOsNotEqualTo(String value) {
            addCriterion("os <>", value, "os");
            return (Criteria) this;
        }

        public Criteria andOsGreaterThan(String value) {
            addCriterion("os >", value, "os");
            return (Criteria) this;
        }

        public Criteria andOsGreaterThanOrEqualTo(String value) {
            addCriterion("os >=", value, "os");
            return (Criteria) this;
        }

        public Criteria andOsLessThan(String value) {
            addCriterion("os <", value, "os");
            return (Criteria) this;
        }

        public Criteria andOsLessThanOrEqualTo(String value) {
            addCriterion("os <=", value, "os");
            return (Criteria) this;
        }

        public Criteria andOsLike(String value) {
            addCriterion("os like", value, "os");
            return (Criteria) this;
        }

        public Criteria andOsNotLike(String value) {
            addCriterion("os not like", value, "os");
            return (Criteria) this;
        }

        public Criteria andOsIn(List<String> values) {
            addCriterion("os in", values, "os");
            return (Criteria) this;
        }

        public Criteria andOsNotIn(List<String> values) {
            addCriterion("os not in", values, "os");
            return (Criteria) this;
        }

        public Criteria andOsBetween(String value1, String value2) {
            addCriterion("os between", value1, value2, "os");
            return (Criteria) this;
        }

        public Criteria andOsNotBetween(String value1, String value2) {
            addCriterion("os not between", value1, value2, "os");
            return (Criteria) this;
        }

        public Criteria andBearingIsNull() {
            addCriterion("bearing is null");
            return (Criteria) this;
        }

        public Criteria andBearingIsNotNull() {
            addCriterion("bearing is not null");
            return (Criteria) this;
        }

        public Criteria andBearingEqualTo(String value) {
            addCriterion("bearing =", value, "bearing");
            return (Criteria) this;
        }

        public Criteria andBearingNotEqualTo(String value) {
            addCriterion("bearing <>", value, "bearing");
            return (Criteria) this;
        }

        public Criteria andBearingGreaterThan(String value) {
            addCriterion("bearing >", value, "bearing");
            return (Criteria) this;
        }

        public Criteria andBearingGreaterThanOrEqualTo(String value) {
            addCriterion("bearing >=", value, "bearing");
            return (Criteria) this;
        }

        public Criteria andBearingLessThan(String value) {
            addCriterion("bearing <", value, "bearing");
            return (Criteria) this;
        }

        public Criteria andBearingLessThanOrEqualTo(String value) {
            addCriterion("bearing <=", value, "bearing");
            return (Criteria) this;
        }

        public Criteria andBearingLike(String value) {
            addCriterion("bearing like", value, "bearing");
            return (Criteria) this;
        }

        public Criteria andBearingNotLike(String value) {
            addCriterion("bearing not like", value, "bearing");
            return (Criteria) this;
        }

        public Criteria andBearingIn(List<String> values) {
            addCriterion("bearing in", values, "bearing");
            return (Criteria) this;
        }

        public Criteria andBearingNotIn(List<String> values) {
            addCriterion("bearing not in", values, "bearing");
            return (Criteria) this;
        }

        public Criteria andBearingBetween(String value1, String value2) {
            addCriterion("bearing between", value1, value2, "bearing");
            return (Criteria) this;
        }

        public Criteria andBearingNotBetween(String value1, String value2) {
            addCriterion("bearing not between", value1, value2, "bearing");
            return (Criteria) this;
        }

        public Criteria andSpeedIsNull() {
            addCriterion("speed is null");
            return (Criteria) this;
        }

        public Criteria andSpeedIsNotNull() {
            addCriterion("speed is not null");
            return (Criteria) this;
        }

        public Criteria andSpeedEqualTo(String value) {
            addCriterion("speed =", value, "speed");
            return (Criteria) this;
        }

        public Criteria andSpeedNotEqualTo(String value) {
            addCriterion("speed <>", value, "speed");
            return (Criteria) this;
        }

        public Criteria andSpeedGreaterThan(String value) {
            addCriterion("speed >", value, "speed");
            return (Criteria) this;
        }

        public Criteria andSpeedGreaterThanOrEqualTo(String value) {
            addCriterion("speed >=", value, "speed");
            return (Criteria) this;
        }

        public Criteria andSpeedLessThan(String value) {
            addCriterion("speed <", value, "speed");
            return (Criteria) this;
        }

        public Criteria andSpeedLessThanOrEqualTo(String value) {
            addCriterion("speed <=", value, "speed");
            return (Criteria) this;
        }

        public Criteria andSpeedLike(String value) {
            addCriterion("speed like", value, "speed");
            return (Criteria) this;
        }

        public Criteria andSpeedNotLike(String value) {
            addCriterion("speed not like", value, "speed");
            return (Criteria) this;
        }

        public Criteria andSpeedIn(List<String> values) {
            addCriterion("speed in", values, "speed");
            return (Criteria) this;
        }

        public Criteria andSpeedNotIn(List<String> values) {
            addCriterion("speed not in", values, "speed");
            return (Criteria) this;
        }

        public Criteria andSpeedBetween(String value1, String value2) {
            addCriterion("speed between", value1, value2, "speed");
            return (Criteria) this;
        }

        public Criteria andSpeedNotBetween(String value1, String value2) {
            addCriterion("speed not between", value1, value2, "speed");
            return (Criteria) this;
        }

        public Criteria andAccuracyIsNull() {
            addCriterion("accuracy is null");
            return (Criteria) this;
        }

        public Criteria andAccuracyIsNotNull() {
            addCriterion("accuracy is not null");
            return (Criteria) this;
        }

        public Criteria andAccuracyEqualTo(String value) {
            addCriterion("accuracy =", value, "accuracy");
            return (Criteria) this;
        }

        public Criteria andAccuracyNotEqualTo(String value) {
            addCriterion("accuracy <>", value, "accuracy");
            return (Criteria) this;
        }

        public Criteria andAccuracyGreaterThan(String value) {
            addCriterion("accuracy >", value, "accuracy");
            return (Criteria) this;
        }

        public Criteria andAccuracyGreaterThanOrEqualTo(String value) {
            addCriterion("accuracy >=", value, "accuracy");
            return (Criteria) this;
        }

        public Criteria andAccuracyLessThan(String value) {
            addCriterion("accuracy <", value, "accuracy");
            return (Criteria) this;
        }

        public Criteria andAccuracyLessThanOrEqualTo(String value) {
            addCriterion("accuracy <=", value, "accuracy");
            return (Criteria) this;
        }

        public Criteria andAccuracyLike(String value) {
            addCriterion("accuracy like", value, "accuracy");
            return (Criteria) this;
        }

        public Criteria andAccuracyNotLike(String value) {
            addCriterion("accuracy not like", value, "accuracy");
            return (Criteria) this;
        }

        public Criteria andAccuracyIn(List<String> values) {
            addCriterion("accuracy in", values, "accuracy");
            return (Criteria) this;
        }

        public Criteria andAccuracyNotIn(List<String> values) {
            addCriterion("accuracy not in", values, "accuracy");
            return (Criteria) this;
        }

        public Criteria andAccuracyBetween(String value1, String value2) {
            addCriterion("accuracy between", value1, value2, "accuracy");
            return (Criteria) this;
        }

        public Criteria andAccuracyNotBetween(String value1, String value2) {
            addCriterion("accuracy not between", value1, value2, "accuracy");
            return (Criteria) this;
        }

        public Criteria andProviderIsNull() {
            addCriterion("provider is null");
            return (Criteria) this;
        }

        public Criteria andProviderIsNotNull() {
            addCriterion("provider is not null");
            return (Criteria) this;
        }

        public Criteria andProviderEqualTo(String value) {
            addCriterion("provider =", value, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderNotEqualTo(String value) {
            addCriterion("provider <>", value, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderGreaterThan(String value) {
            addCriterion("provider >", value, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderGreaterThanOrEqualTo(String value) {
            addCriterion("provider >=", value, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderLessThan(String value) {
            addCriterion("provider <", value, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderLessThanOrEqualTo(String value) {
            addCriterion("provider <=", value, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderLike(String value) {
            addCriterion("provider like", value, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderNotLike(String value) {
            addCriterion("provider not like", value, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderIn(List<String> values) {
            addCriterion("provider in", values, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderNotIn(List<String> values) {
            addCriterion("provider not in", values, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderBetween(String value1, String value2) {
            addCriterion("provider between", value1, value2, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderNotBetween(String value1, String value2) {
            addCriterion("provider not between", value1, value2, "provider");
            return (Criteria) this;
        }

        public Criteria andLocateTimeIsNull() {
            addCriterion("locate_time is null");
            return (Criteria) this;
        }

        public Criteria andLocateTimeIsNotNull() {
            addCriterion("locate_time is not null");
            return (Criteria) this;
        }

        public Criteria andLocateTimeEqualTo(String value) {
            addCriterion("locate_time =", value, "locateTime");
            return (Criteria) this;
        }

        public Criteria andLocateTimeNotEqualTo(String value) {
            addCriterion("locate_time <>", value, "locateTime");
            return (Criteria) this;
        }

        public Criteria andLocateTimeGreaterThan(String value) {
            addCriterion("locate_time >", value, "locateTime");
            return (Criteria) this;
        }

        public Criteria andLocateTimeGreaterThanOrEqualTo(String value) {
            addCriterion("locate_time >=", value, "locateTime");
            return (Criteria) this;
        }

        public Criteria andLocateTimeLessThan(String value) {
            addCriterion("locate_time <", value, "locateTime");
            return (Criteria) this;
        }

        public Criteria andLocateTimeLessThanOrEqualTo(String value) {
            addCriterion("locate_time <=", value, "locateTime");
            return (Criteria) this;
        }

        public Criteria andLocateTimeLike(String value) {
            addCriterion("locate_time like", value, "locateTime");
            return (Criteria) this;
        }

        public Criteria andLocateTimeNotLike(String value) {
            addCriterion("locate_time not like", value, "locateTime");
            return (Criteria) this;
        }

        public Criteria andLocateTimeIn(List<String> values) {
            addCriterion("locate_time in", values, "locateTime");
            return (Criteria) this;
        }

        public Criteria andLocateTimeNotIn(List<String> values) {
            addCriterion("locate_time not in", values, "locateTime");
            return (Criteria) this;
        }

        public Criteria andLocateTimeBetween(String value1, String value2) {
            addCriterion("locate_time between", value1, value2, "locateTime");
            return (Criteria) this;
        }

        public Criteria andLocateTimeNotBetween(String value1, String value2) {
            addCriterion("locate_time not between", value1, value2, "locateTime");
            return (Criteria) this;
        }

        public Criteria andIsCacheLocationIsNull() {
            addCriterion("is_cache_location is null");
            return (Criteria) this;
        }

        public Criteria andIsCacheLocationIsNotNull() {
            addCriterion("is_cache_location is not null");
            return (Criteria) this;
        }

        public Criteria andIsCacheLocationEqualTo(Integer value) {
            addCriterion("is_cache_location =", value, "isCacheLocation");
            return (Criteria) this;
        }

        public Criteria andIsCacheLocationNotEqualTo(Integer value) {
            addCriterion("is_cache_location <>", value, "isCacheLocation");
            return (Criteria) this;
        }

        public Criteria andIsCacheLocationGreaterThan(Integer value) {
            addCriterion("is_cache_location >", value, "isCacheLocation");
            return (Criteria) this;
        }

        public Criteria andIsCacheLocationGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_cache_location >=", value, "isCacheLocation");
            return (Criteria) this;
        }

        public Criteria andIsCacheLocationLessThan(Integer value) {
            addCriterion("is_cache_location <", value, "isCacheLocation");
            return (Criteria) this;
        }

        public Criteria andIsCacheLocationLessThanOrEqualTo(Integer value) {
            addCriterion("is_cache_location <=", value, "isCacheLocation");
            return (Criteria) this;
        }

        public Criteria andIsCacheLocationIn(List<Integer> values) {
            addCriterion("is_cache_location in", values, "isCacheLocation");
            return (Criteria) this;
        }

        public Criteria andIsCacheLocationNotIn(List<Integer> values) {
            addCriterion("is_cache_location not in", values, "isCacheLocation");
            return (Criteria) this;
        }

        public Criteria andIsCacheLocationBetween(Integer value1, Integer value2) {
            addCriterion("is_cache_location between", value1, value2, "isCacheLocation");
            return (Criteria) this;
        }

        public Criteria andIsCacheLocationNotBetween(Integer value1, Integer value2) {
            addCriterion("is_cache_location not between", value1, value2, "isCacheLocation");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}