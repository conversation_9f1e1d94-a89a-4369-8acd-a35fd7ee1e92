package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.time.LocalDateTime;

public class StoreDimensionConfigDO {
    private Long id;

    private Long tenantId;

    private Long storeId;

    private Integer selfDeliveryMode;

    private Integer internalNavigationMode;

    private String assessTimeConfig;

    private String deliveryCompleteMode;

    private String riderTransRoles;

    private Integer completedSortMode;

    private String deliveryRemindConfig;

    private Integer enable;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Integer getSelfDeliveryMode() {
        return selfDeliveryMode;
    }

    public void setSelfDeliveryMode(Integer selfDeliveryMode) {
        this.selfDeliveryMode = selfDeliveryMode;
    }

    public Integer getInternalNavigationMode() {
        return internalNavigationMode;
    }

    public void setInternalNavigationMode(Integer internalNavigationMode) {
        this.internalNavigationMode = internalNavigationMode;
    }

    public String getAssessTimeConfig() {
        return assessTimeConfig;
    }

    public void setAssessTimeConfig(String assessTimeConfig) {
        this.assessTimeConfig = assessTimeConfig == null ? null : assessTimeConfig.trim();
    }

    public String getDeliveryCompleteMode() {
        return deliveryCompleteMode;
    }

    public void setDeliveryCompleteMode(String deliveryCompleteMode) {
        this.deliveryCompleteMode = deliveryCompleteMode == null ? null : deliveryCompleteMode.trim();
    }

    public String getRiderTransRoles() {
        return riderTransRoles;
    }

    public void setRiderTransRoles(String riderTransRoles) {
        this.riderTransRoles = riderTransRoles == null ? null : riderTransRoles.trim();
    }

    public Integer getCompletedSortMode() {
        return completedSortMode;
    }

    public void setCompletedSortMode(Integer completedSortMode) {
        this.completedSortMode = completedSortMode;
    }

    public String getDeliveryRemindConfig() {
        return deliveryRemindConfig;
    }

    public void setDeliveryRemindConfig(String deliveryRemindConfig) {
        this.deliveryRemindConfig = deliveryRemindConfig == null ? null : deliveryRemindConfig.trim();
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StoreDimensionConfigDO other = (StoreDimensionConfigDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getSelfDeliveryMode() == null ? other.getSelfDeliveryMode() == null : this.getSelfDeliveryMode().equals(other.getSelfDeliveryMode()))
            && (this.getInternalNavigationMode() == null ? other.getInternalNavigationMode() == null : this.getInternalNavigationMode().equals(other.getInternalNavigationMode()))
            && (this.getAssessTimeConfig() == null ? other.getAssessTimeConfig() == null : this.getAssessTimeConfig().equals(other.getAssessTimeConfig()))
            && (this.getDeliveryCompleteMode() == null ? other.getDeliveryCompleteMode() == null : this.getDeliveryCompleteMode().equals(other.getDeliveryCompleteMode()))
            && (this.getRiderTransRoles() == null ? other.getRiderTransRoles() == null : this.getRiderTransRoles().equals(other.getRiderTransRoles()))
            && (this.getCompletedSortMode() == null ? other.getCompletedSortMode() == null : this.getCompletedSortMode().equals(other.getCompletedSortMode()))
            && (this.getDeliveryRemindConfig() == null ? other.getDeliveryRemindConfig() == null : this.getDeliveryRemindConfig().equals(other.getDeliveryRemindConfig()))
            && (this.getEnable() == null ? other.getEnable() == null : this.getEnable().equals(other.getEnable()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getSelfDeliveryMode() == null) ? 0 : getSelfDeliveryMode().hashCode());
        result = prime * result + ((getInternalNavigationMode() == null) ? 0 : getInternalNavigationMode().hashCode());
        result = prime * result + ((getAssessTimeConfig() == null) ? 0 : getAssessTimeConfig().hashCode());
        result = prime * result + ((getDeliveryCompleteMode() == null) ? 0 : getDeliveryCompleteMode().hashCode());
        result = prime * result + ((getRiderTransRoles() == null) ? 0 : getRiderTransRoles().hashCode());
        result = prime * result + ((getCompletedSortMode() == null) ? 0 : getCompletedSortMode().hashCode());
        result = prime * result + ((getDeliveryRemindConfig() == null) ? 0 : getDeliveryRemindConfig().hashCode());
        result = prime * result + ((getEnable() == null) ? 0 : getEnable().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        return result;
    }
}