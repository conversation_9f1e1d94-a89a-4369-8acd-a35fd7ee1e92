package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.base;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 用于访问根据日期pattern的索引
 * pattern里面的非日期时间字符需要用单引号包围，例如："'order_info.'yyyyMM"
 *
 * <AUTHOR>
 * @since 2022/4/1
 */
public abstract class BaseEsDatePatternDao<T extends BaseEsPo> extends BaseEsDao<T> {

    private final DateTimeFormatter dateTimeFormatter;

    /**
     * 基于索引日期格式的dao
     *
     * @param indexPattern 索引格式
     */
    public BaseEsDatePatternDao(String indexPattern) {
        this.dateTimeFormatter = DateTimeFormatter.ofPattern(indexPattern);
    }

    @Override
    public String getIndex() {
        return dateTimeFormatter.format(LocalDateTime.now());
    }

    @Override
    public String getIndex(LocalDateTime dateTime) {
        return dateTimeFormatter.format(dateTime);
    }

}
