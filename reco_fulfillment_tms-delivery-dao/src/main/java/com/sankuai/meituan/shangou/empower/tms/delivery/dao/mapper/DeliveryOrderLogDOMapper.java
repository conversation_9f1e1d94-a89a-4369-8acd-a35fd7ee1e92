package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeliveryOrderLogDOMapper {
    long countByExample(DeliveryOrderLogDOExample example);

    int deleteByExample(DeliveryOrderLogDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DeliveryOrderLogDO record);

    int insertSelective(DeliveryOrderLogDO record);

    List<DeliveryOrderLogDO> selectByExample(DeliveryOrderLogDOExample example);

    DeliveryOrderLogDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DeliveryOrderLogDO record, @Param("example") DeliveryOrderLogDOExample example);

    int updateByExample(@Param("record") DeliveryOrderLogDO record, @Param("example") DeliveryOrderLogDOExample example);

    int updateByPrimaryKeySelective(DeliveryOrderLogDO record);

    int updateByPrimaryKey(DeliveryOrderLogDO record);
}