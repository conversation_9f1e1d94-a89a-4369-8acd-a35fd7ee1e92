package com.sankuai.meituan.shangou.empower.tms.delivery.dao.utils;

import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class MccDynamicConfigUtil {
    private MccDynamicConfigUtil() {
    }

    /**
     * 查询强制主库的配置
     *
     * @return
     */
    public static int getForceMasterConfig() {
        try {
            return ConfigUtilAdapter.getInt("zebra_force_master_config", ForceMasterEnum.ALL.getCode());
        } catch (Exception e) {
            log.error("error occurred in getForceMasterConfig!", e);
            return ForceMasterEnum.ALL.getCode();
        }
    }

    /**
     * 查询非强制主库方法列表
     *
     * @return
     */
    public static List<String> getNotForceMasterMapperMethodList() {
        String valueStr = ConfigUtilAdapter.getString("zebra_not_force_master_mapper_method_list", "");
        if (Strings.isNullOrEmpty(valueStr)) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return Splitter.on(",").trimResults().splitToList(valueStr);
    }

    public static String getReflectionsPathListStr(){
        return ConfigUtilAdapter.getString("reflections.path.list.str", "com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper");
    }

}

