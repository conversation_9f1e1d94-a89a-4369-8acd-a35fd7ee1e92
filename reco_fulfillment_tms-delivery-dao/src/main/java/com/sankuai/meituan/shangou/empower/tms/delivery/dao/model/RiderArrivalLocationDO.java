package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.time.LocalDateTime;

public class RiderArrivalLocationDO {
    private Long id;

    private Long deliveryOrderId;

    private Long riderAccountId;

    private String longitude;

    private String latitude;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Long linearDistanceToReceiver;

    private Long navigationDistanceToReceiver;

    private String os;

    private String bearing;

    private String speed;

    private String accuracy;

    private String provider;

    private String locateTime;

    private Integer isCacheLocation;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDeliveryOrderId() {
        return deliveryOrderId;
    }

    public void setDeliveryOrderId(Long deliveryOrderId) {
        this.deliveryOrderId = deliveryOrderId;
    }

    public Long getRiderAccountId() {
        return riderAccountId;
    }

    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude == null ? null : longitude.trim();
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude == null ? null : latitude.trim();
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Long getLinearDistanceToReceiver() {
        return linearDistanceToReceiver;
    }

    public void setLinearDistanceToReceiver(Long linearDistanceToReceiver) {
        this.linearDistanceToReceiver = linearDistanceToReceiver;
    }

    public Long getNavigationDistanceToReceiver() {
        return navigationDistanceToReceiver;
    }

    public void setNavigationDistanceToReceiver(Long navigationDistanceToReceiver) {
        this.navigationDistanceToReceiver = navigationDistanceToReceiver;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os == null ? null : os.trim();
    }

    public String getBearing() {
        return bearing;
    }

    public void setBearing(String bearing) {
        this.bearing = bearing == null ? null : bearing.trim();
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed == null ? null : speed.trim();
    }

    public String getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(String accuracy) {
        this.accuracy = accuracy == null ? null : accuracy.trim();
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider == null ? null : provider.trim();
    }

    public String getLocateTime() {
        return locateTime;
    }

    public void setLocateTime(String locateTime) {
        this.locateTime = locateTime == null ? null : locateTime.trim();
    }

    public Integer getIsCacheLocation() {
        return isCacheLocation;
    }

    public void setIsCacheLocation(Integer isCacheLocation) {
        this.isCacheLocation = isCacheLocation;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RiderArrivalLocationDO other = (RiderArrivalLocationDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getDeliveryOrderId() == null ? other.getDeliveryOrderId() == null : this.getDeliveryOrderId().equals(other.getDeliveryOrderId()))
            && (this.getRiderAccountId() == null ? other.getRiderAccountId() == null : this.getRiderAccountId().equals(other.getRiderAccountId()))
            && (this.getLongitude() == null ? other.getLongitude() == null : this.getLongitude().equals(other.getLongitude()))
            && (this.getLatitude() == null ? other.getLatitude() == null : this.getLatitude().equals(other.getLatitude()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getLinearDistanceToReceiver() == null ? other.getLinearDistanceToReceiver() == null : this.getLinearDistanceToReceiver().equals(other.getLinearDistanceToReceiver()))
            && (this.getNavigationDistanceToReceiver() == null ? other.getNavigationDistanceToReceiver() == null : this.getNavigationDistanceToReceiver().equals(other.getNavigationDistanceToReceiver()))
            && (this.getOs() == null ? other.getOs() == null : this.getOs().equals(other.getOs()))
            && (this.getBearing() == null ? other.getBearing() == null : this.getBearing().equals(other.getBearing()))
            && (this.getSpeed() == null ? other.getSpeed() == null : this.getSpeed().equals(other.getSpeed()))
            && (this.getAccuracy() == null ? other.getAccuracy() == null : this.getAccuracy().equals(other.getAccuracy()))
            && (this.getProvider() == null ? other.getProvider() == null : this.getProvider().equals(other.getProvider()))
            && (this.getLocateTime() == null ? other.getLocateTime() == null : this.getLocateTime().equals(other.getLocateTime()))
            && (this.getIsCacheLocation() == null ? other.getIsCacheLocation() == null : this.getIsCacheLocation().equals(other.getIsCacheLocation()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getDeliveryOrderId() == null) ? 0 : getDeliveryOrderId().hashCode());
        result = prime * result + ((getRiderAccountId() == null) ? 0 : getRiderAccountId().hashCode());
        result = prime * result + ((getLongitude() == null) ? 0 : getLongitude().hashCode());
        result = prime * result + ((getLatitude() == null) ? 0 : getLatitude().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getLinearDistanceToReceiver() == null) ? 0 : getLinearDistanceToReceiver().hashCode());
        result = prime * result + ((getNavigationDistanceToReceiver() == null) ? 0 : getNavigationDistanceToReceiver().hashCode());
        result = prime * result + ((getOs() == null) ? 0 : getOs().hashCode());
        result = prime * result + ((getBearing() == null) ? 0 : getBearing().hashCode());
        result = prime * result + ((getSpeed() == null) ? 0 : getSpeed().hashCode());
        result = prime * result + ((getAccuracy() == null) ? 0 : getAccuracy().hashCode());
        result = prime * result + ((getProvider() == null) ? 0 : getProvider().hashCode());
        result = prime * result + ((getLocateTime() == null) ? 0 : getLocateTime().hashCode());
        result = prime * result + ((getIsCacheLocation() == null) ? 0 : getIsCacheLocation().hashCode());
        return result;
    }
}