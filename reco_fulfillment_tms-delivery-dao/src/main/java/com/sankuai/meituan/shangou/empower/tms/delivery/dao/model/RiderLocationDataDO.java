package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import lombok.Data;

@Data
public class RiderLocationDataDO {
    /**
     * 骑手姓名
     */
    private String riderName;

    /**
     * 骑手手机号码
     */
    private String riderPhone;

    /**
     * 骑手账号id
     */
    private long riderAccountId;

    //经度
    private String longitude;

    //纬度
    private String latitude;

    //定位结果来源
    private String provider;

    //定位结果精确度
    private String accuracy;

    //方向信息
    private String bearing;

    //速度信息
    private String speed;

    //时间信息,前端传入
    private String time;

    //更新到缓存的时间
    private long utime;

    private String os;

    //牵牛花app版本
    private String appVersion;

    //骑手设备id
    private String uuid;
}

