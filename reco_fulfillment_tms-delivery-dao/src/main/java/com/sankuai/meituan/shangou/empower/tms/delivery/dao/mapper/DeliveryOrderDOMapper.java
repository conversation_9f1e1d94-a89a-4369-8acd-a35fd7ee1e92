package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDOExample;

import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeliveryOrderDOMapper {
    long countByExample(DeliveryOrderDOExample example);

    int deleteByExample(DeliveryOrderDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DeliveryOrderDO record);

    int insertSelective(DeliveryOrderDO record);

    List<DeliveryOrderDO> selectByExample(DeliveryOrderDOExample example);

    DeliveryOrderDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DeliveryOrderDO record, @Param("example") DeliveryOrderDOExample example);

    int updateByExample(@Param("record") DeliveryOrderDO record, @Param("example") DeliveryOrderDOExample example);

    int updateByPrimaryKeySelective(DeliveryOrderDO record);

    int updateByPrimaryKey(DeliveryOrderDO record);

    List<DeliveryOrderDO> batchQueryOriginWaybillNo(@Param("tenantId") Long tenantId, @Param("storeIds") Collection<Long> storeIds,
                                                    @Param("orderIds") List<Long> orderIds, @Param("deliveryStatusList")List<Integer> deliveryStatusList);
}