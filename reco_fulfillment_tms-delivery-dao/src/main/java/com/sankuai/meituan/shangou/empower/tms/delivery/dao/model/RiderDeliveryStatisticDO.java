package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

public class RiderDeliveryStatisticDO {
    private Long id;

    private Long tenantId;

    private Long storeId;

    private Long riderAccountId;

    private Integer delivered;

    private Integer cancelBeforeDelivered;

    private Integer timeout;

    private Integer deliveredExcludeBooking;

    private Integer deliveredIn25Mins;

    private Long deliveryDuration;

    private Integer earlyClick;

    private Integer riskControl;

    private Long dt;

    private Integer deliveredTimeout;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Long getRiderAccountId() {
        return riderAccountId;
    }

    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    public Integer getDelivered() {
        return delivered;
    }

    public void setDelivered(Integer delivered) {
        this.delivered = delivered;
    }

    public Integer getCancelBeforeDelivered() {
        return cancelBeforeDelivered;
    }

    public void setCancelBeforeDelivered(Integer cancelBeforeDelivered) {
        this.cancelBeforeDelivered = cancelBeforeDelivered;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public Integer getDeliveredExcludeBooking() {
        return deliveredExcludeBooking;
    }

    public void setDeliveredExcludeBooking(Integer deliveredExcludeBooking) {
        this.deliveredExcludeBooking = deliveredExcludeBooking;
    }

    public Integer getDeliveredIn25Mins() {
        return deliveredIn25Mins;
    }

    public void setDeliveredIn25Mins(Integer deliveredIn25Mins) {
        this.deliveredIn25Mins = deliveredIn25Mins;
    }

    public Long getDeliveryDuration() {
        return deliveryDuration;
    }

    public void setDeliveryDuration(Long deliveryDuration) {
        this.deliveryDuration = deliveryDuration;
    }

    public Integer getEarlyClick() {
        return earlyClick;
    }

    public void setEarlyClick(Integer earlyClick) {
        this.earlyClick = earlyClick;
    }

    public Integer getRiskControl() {
        return riskControl;
    }

    public void setRiskControl(Integer riskControl) {
        this.riskControl = riskControl;
    }

    public Long getDt() {
        return dt;
    }

    public void setDt(Long dt) {
        this.dt = dt;
    }

    public Integer getDeliveredTimeout() {
        return deliveredTimeout;
    }

    public void setDeliveredTimeout(Integer deliveredTimeout) {
        this.deliveredTimeout = deliveredTimeout;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RiderDeliveryStatisticDO other = (RiderDeliveryStatisticDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getRiderAccountId() == null ? other.getRiderAccountId() == null : this.getRiderAccountId().equals(other.getRiderAccountId()))
            && (this.getDelivered() == null ? other.getDelivered() == null : this.getDelivered().equals(other.getDelivered()))
            && (this.getCancelBeforeDelivered() == null ? other.getCancelBeforeDelivered() == null : this.getCancelBeforeDelivered().equals(other.getCancelBeforeDelivered()))
            && (this.getTimeout() == null ? other.getTimeout() == null : this.getTimeout().equals(other.getTimeout()))
            && (this.getDeliveredExcludeBooking() == null ? other.getDeliveredExcludeBooking() == null : this.getDeliveredExcludeBooking().equals(other.getDeliveredExcludeBooking()))
            && (this.getDeliveredIn25Mins() == null ? other.getDeliveredIn25Mins() == null : this.getDeliveredIn25Mins().equals(other.getDeliveredIn25Mins()))
            && (this.getDeliveryDuration() == null ? other.getDeliveryDuration() == null : this.getDeliveryDuration().equals(other.getDeliveryDuration()))
            && (this.getEarlyClick() == null ? other.getEarlyClick() == null : this.getEarlyClick().equals(other.getEarlyClick()))
            && (this.getRiskControl() == null ? other.getRiskControl() == null : this.getRiskControl().equals(other.getRiskControl()))
            && (this.getDt() == null ? other.getDt() == null : this.getDt().equals(other.getDt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getRiderAccountId() == null) ? 0 : getRiderAccountId().hashCode());
        result = prime * result + ((getDelivered() == null) ? 0 : getDelivered().hashCode());
        result = prime * result + ((getCancelBeforeDelivered() == null) ? 0 : getCancelBeforeDelivered().hashCode());
        result = prime * result + ((getTimeout() == null) ? 0 : getTimeout().hashCode());
        result = prime * result + ((getDeliveredExcludeBooking() == null) ? 0 : getDeliveredExcludeBooking().hashCode());
        result = prime * result + ((getDeliveredIn25Mins() == null) ? 0 : getDeliveredIn25Mins().hashCode());
        result = prime * result + ((getDeliveryDuration() == null) ? 0 : getDeliveryDuration().hashCode());
        result = prime * result + ((getEarlyClick() == null) ? 0 : getEarlyClick().hashCode());
        result = prime * result + ((getRiskControl() == null) ? 0 : getRiskControl().hashCode());
        result = prime * result + ((getDt() == null) ? 0 : getDt().hashCode());
        return result;
    }
}