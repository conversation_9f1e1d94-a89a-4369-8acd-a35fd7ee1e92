package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es;

import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.base.BaseEsAliasDao;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.base.SearchResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.constants.DeliveryOrderConstants;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.constants.TimeConstants;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.exception.EsBaseException;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.po.DeliveryOrderEsPo;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils.MccUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.constants.DeliveryOrderConstants.*;
import static org.elasticsearch.index.query.QueryBuilders.*;

/**
 * 运单索引的路由元数据是storeId，对应es里的增删改查方法都要带上这个routingKey
 * <AUTHOR>
 */
@Slf4j
@Repository
public class DeliveryOrderEsAliasDao extends BaseEsAliasDao<DeliveryOrderEsPo> {
    /**
     * 指定返回的字段
     */
    private static final String[] batchQueryOriginWaybillNoIncludeFields = new String[]{FIELD_ORDER_ID, FIELD_DELIVERY_ORDER_ID, FIELD_DELIVERY_STATUS, FIELD_ORIGIN_WAYBILL_NO, FIELD_DELIVERY_TRACE_LIST};

    @Override
    public String getIndex() {
        return DeliveryOrderConstants.INDEX_ALIAS;
    }

    public Long countExceptionDeliveryOrdersByStoreIds(List<Long> storeIdList) {
        if (CollectionUtils.isEmpty(storeIdList)) {
            log.warn("countExceptionDeliveryOrdersByStoreIds, storeIdList is empty");
            return NumberUtils.LONG_ZERO;
        }

        // 判断是否配送过时的时间点，若预计送达时间大于等于该时间点，则认为未超时
        String timeoutPointStr = getTimeOutPointFromNow();

        QueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .filter(termsQuery(FIELD_STORE_ID, storeIdList))
                .filter(rangeQuery(FIELD_ESTIMATED_DELIVERY_TIME).gte(timeoutPointStr))
                .mustNot(termQuery(FIELD_EXCEPTION_TYPE, NO_EXCEPTION_TYPE))
                .mustNot(termQuery(FIELD_ORDER_STATUS, ORDER_STATUS_CANCEL));
        return count(queryBuilder, storeIdList.stream().map(String::valueOf).toArray(String[]::new));
    }

    public Map<DeliveryExceptionSubTypeEnum, Long> countExceptionDeliveryOrdersSubTypeByStoreIds(List<Long> storeIdList) {
        Map<DeliveryExceptionSubTypeEnum, Long> result = Maps.newHashMap();

        // 判断是否配送过时的时间点，若预计送达时间大于等于该时间点，则认为未超时
        String timeoutPointStr = getTimeOutPointFromNow();

        QueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .filter(termsQuery(FIELD_STORE_ID, storeIdList))
                .filter(rangeQuery(FIELD_ESTIMATED_DELIVERY_TIME).gte(timeoutPointStr))
                .mustNot(termQuery(FIELD_EXCEPTION_TYPE, NO_EXCEPTION_TYPE))
                .mustNot(termQuery(FIELD_ORDER_STATUS, ORDER_STATUS_CANCEL));

        AggregationBuilder deliveryStatusAgg = AggregationBuilders.terms(DELIVERY_STATUS_AGG.getLeft()).field(DELIVERY_STATUS_AGG.getRight());
        AggregationBuilder exceptionCodeAgg = AggregationBuilders.terms(DELIVERY_EXCEPTION_CODE_AGG.getLeft()).field(DELIVERY_EXCEPTION_CODE_AGG.getRight()).subAggregation(deliveryStatusAgg);
        AggregationBuilder exceptionTypeAgg = AggregationBuilders.terms(DELIVERY_EXCEPTION_TYPE_AGG.getLeft()).field(DELIVERY_EXCEPTION_TYPE_AGG.getRight()).subAggregation(exceptionCodeAgg);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        searchSourceBuilder.aggregation(exceptionTypeAgg);
        // 仅需要返回聚合结果，不需要返回命中的doc
        searchSourceBuilder.size(NumberUtils.INTEGER_ZERO);
        Aggregations aggregations = searchAggregations(searchSourceBuilder, LocalDateTime.now(), storeIdList.stream().map(String::valueOf).toArray(String[]::new));

        Terms exceptionTypeTerms = aggregations.get(DELIVERY_EXCEPTION_TYPE_AGG.getLeft());
        for (Terms.Bucket exceptionTypeBucket : exceptionTypeTerms.getBuckets()) {
            Terms exceptionCodeTerms = exceptionTypeBucket.getAggregations().get(DELIVERY_EXCEPTION_CODE_AGG.getLeft());
            for (Terms.Bucket exceptionCodeBucket : exceptionCodeTerms.getBuckets()) {
                Terms deliveryStatusTerms = exceptionCodeBucket.getAggregations().get(DELIVERY_STATUS_AGG.getLeft());
                for (Terms.Bucket deliveryStatusBucket : deliveryStatusTerms.getBuckets()) {
                    long count = deliveryStatusBucket.getDocCount();
                    DeliveryExceptionSubTypeEnum subTypeEnum =
                            DeliveryExceptionSubTypeEnum.deliveryStatusCodeValueOfWithOutAll(
                                    exceptionTypeBucket.getKeyAsNumber().intValue(),
                                    exceptionCodeBucket.getKeyAsNumber().intValue(),
                                    deliveryStatusBucket.getKeyAsNumber().intValue());
                    if (Objects.nonNull(subTypeEnum)) {
                        result.put(subTypeEnum, result.getOrDefault(subTypeEnum, NumberUtils.LONG_ZERO) + count);
                    }
                }
            }
        }

        return result;
    }

    public Pair<List<DeliveryOrderEsPo>, Integer> searchExceptionDeliveryOrdersBySubTypeAndStoreIds(List<Integer> exceptionTypeList,
                                                                                                    List<Integer> exceptionCodeList,
                                                                                                    final List<Integer> deliveryStatusList,
                                                                                                    List<Long> storeIdList,
                                                                                                    Integer from, Integer size,List<String> viewOrderIdList) {
        if (CollectionUtils.isEmpty(storeIdList)) {
            log.warn("searchExceptionDeliveryOrdersBySubTypeAndStoreIds, storeIdList is empty");
            return Pair.of(Collections.emptyList(), NumberUtils.INTEGER_ZERO);
        }

        // 判断是否配送过时的时间点，若预计送达时间大于等于该时间点，则认为未超时
        String timeoutPointStr = getTimeOutPointFromNow();

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .filter(termsQuery(FIELD_STORE_ID, storeIdList))
                .filter(rangeQuery(FIELD_ESTIMATED_DELIVERY_TIME).gte(timeoutPointStr))
                .mustNot(termQuery(FIELD_EXCEPTION_TYPE, NO_EXCEPTION_TYPE))
                .mustNot(termQuery(FIELD_ORDER_STATUS, ORDER_STATUS_CANCEL));

        if (CollectionUtils.isNotEmpty(exceptionTypeList)) {
            queryBuilder.filter(termsQuery(FIELD_DELIVERY_EXCEPTION_TYPE, exceptionTypeList));
        }
        if (CollectionUtils.isNotEmpty(exceptionCodeList)) {
            queryBuilder.filter(termsQuery(FIELD_DELIVERY_EXCEPTION_CODE, exceptionCodeList));
        }
        if (CollectionUtils.isNotEmpty(deliveryStatusList)) {
            queryBuilder.filter(termsQuery(FIELD_DELIVERY_STATUS, deliveryStatusList));
        }

        if(CollectionUtils.isNotEmpty(viewOrderIdList)){
            queryBuilder.filter(termsQuery(FIELD_CHANNEL_ORDER_ID, viewOrderIdList));
        }

        SearchResult<DeliveryOrderEsPo> searchResult = search(queryBuilder, from, size, null);
        return Pair.of(searchResult.getData(), searchResult.getTotal());
    }

    /**
     * 包装BaseEsReadDao返回的searchResult
     * delivery_order.*索引的indexKey是create_time字段，这里设置indexKey
     * delivery_order.*索引的routingValue是storeId字段，这里设置routingValue
     */
    private void wrapSearchResult(SearchResult<DeliveryOrderEsPo> searchResult) {
        Objects.requireNonNull(searchResult, "wrapSearchResult, searchResult is null");
        searchResult.getData().forEach(hit -> {
            LocalDateTime createTime = hit.getCreateTime();
            if (Objects.isNull(createTime)) {
                throw new EsBaseException("", "wrapSearchResult, createTime is null");
            }
            hit.setIndexKey(hit.getCreateTime());

            Long storeId = hit.getStoreId();
            if (Objects.isNull(storeId)) {
                throw new EsBaseException("", "wrapSearchResult, storeId is null");
            }
            hit.setRoutingValue(String.valueOf(hit.getStoreId()));
        });
    }

    private String getTimeOutPointFromNow() {
        // 判断是否配送过时的时间点，若预计送达时间大于等于该时间点，则认为未超时
        Integer hours = MccUtils.getHours4JudgeDeliveryOrderTimeout();
        LocalDateTime timeoutPoint = LocalDateTime.now().minusHours(hours);

        return timeoutPoint.format(DateTimeFormatter.ofPattern(TimeConstants.FMT_YMD_HMS));
    }

    public List<DeliveryOrderEsPo> batchQueryOriginWaybillNo(List<Long> orderIds, Long tenantId, Set<Long> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            log.warn("batchQueryOriginWaybillNoByOrderIds, storeIds is empty");
            return new ArrayList<>();
        }
        QueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .filter(termQuery(FIELD_TENANT_ID, tenantId))
                .filter(termsQuery(FIELD_STORE_ID, storeIds))
                .filter(termsQuery(FIELD_ORDER_ID, orderIds));
        String[] routingValues = storeIds.stream().map(String::valueOf).toArray(String[]::new);
        int count = count(queryBuilder, routingValues).intValue();
        List<DeliveryOrderEsPo> resultList = new ArrayList<>(count);
        int from = 0;
        int size = MccUtils.queryOriginWaybillNoSourceESLimit();
        while (from < count) {
            List<DeliveryOrderEsPo> data = search(queryBuilder, batchQueryOriginWaybillNoIncludeFields, null, from, size, routingValues).getData();
            if (CollectionUtils.isNotEmpty(data)) {
                resultList.addAll(data);
            }
            from += size;
        }
        return resultList;
    }
}
