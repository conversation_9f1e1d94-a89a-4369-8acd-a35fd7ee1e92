package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryChannelDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryChannelDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeliveryChannelDOMapper {
    long countByExample(DeliveryChannelDOExample example);

    int deleteByExample(DeliveryChannelDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DeliveryChannelDO record);

    int insertSelective(DeliveryChannelDO record);

    List<DeliveryChannelDO> selectByExample(DeliveryChannelDOExample example);

    DeliveryChannelDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DeliveryChannelDO record, @Param("example") DeliveryChannelDOExample example);

    int updateByExample(@Param("record") DeliveryChannelDO record, @Param("example") DeliveryChannelDOExample example);

    int updateByPrimaryKeySelective(DeliveryChannelDO record);

    int updateByPrimaryKey(DeliveryChannelDO record);
}