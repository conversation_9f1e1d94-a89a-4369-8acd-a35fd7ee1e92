package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.json;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SequentialPollingStrategyConfigJson {

	private List<Integer> orderedDeliveryChannels;

	private Integer timeoutForShiftDeliveryChannelInMinutes;
}
