package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderRoute;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderRouteExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeliveryOrderRouteMapper {
    long countByExample(DeliveryOrderRouteExample example);

    int deleteByExample(DeliveryOrderRouteExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DeliveryOrderRoute record);

    int insertSelective(DeliveryOrderRoute record);

    List<DeliveryOrderRoute> selectByExample(DeliveryOrderRouteExample example);

    DeliveryOrderRoute selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DeliveryOrderRoute record, @Param("example") DeliveryOrderRouteExample example);

    int updateByExample(@Param("record") DeliveryOrderRoute record, @Param("example") DeliveryOrderRouteExample example);

    int updateByPrimaryKeySelective(DeliveryOrderRoute record);

    int updateByPrimaryKey(DeliveryOrderRoute record);
}