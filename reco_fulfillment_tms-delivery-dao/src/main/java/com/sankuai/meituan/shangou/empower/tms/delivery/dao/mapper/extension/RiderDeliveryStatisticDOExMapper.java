package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.CountGroupExDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/8/26 16:12
 **/
public interface RiderDeliveryStatisticDOExMapper {
    boolean checkDataIsReady(@Param("dt") long dt);

    List<CountGroupExDO> queryStoreDeliveringOrderCount(@Param("storeIds") List<Long> storeIds, @Param("status") List<Integer> status);
}
