package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;

public class DeliveryChannelDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public DeliveryChannelDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andLogisticMarkIsNull() {
            addCriterion("logistic_mark is null");
            return (Criteria) this;
        }

        public Criteria andLogisticMarkIsNotNull() {
            addCriterion("logistic_mark is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticMarkEqualTo(String value) {
            addCriterion("logistic_mark =", value, "logisticMark");
            return (Criteria) this;
        }

        public Criteria andLogisticMarkNotEqualTo(String value) {
            addCriterion("logistic_mark <>", value, "logisticMark");
            return (Criteria) this;
        }

        public Criteria andLogisticMarkGreaterThan(String value) {
            addCriterion("logistic_mark >", value, "logisticMark");
            return (Criteria) this;
        }

        public Criteria andLogisticMarkGreaterThanOrEqualTo(String value) {
            addCriterion("logistic_mark >=", value, "logisticMark");
            return (Criteria) this;
        }

        public Criteria andLogisticMarkLessThan(String value) {
            addCriterion("logistic_mark <", value, "logisticMark");
            return (Criteria) this;
        }

        public Criteria andLogisticMarkLessThanOrEqualTo(String value) {
            addCriterion("logistic_mark <=", value, "logisticMark");
            return (Criteria) this;
        }

        public Criteria andLogisticMarkLike(String value) {
            addCriterion("logistic_mark like", value, "logisticMark");
            return (Criteria) this;
        }

        public Criteria andLogisticMarkNotLike(String value) {
            addCriterion("logistic_mark not like", value, "logisticMark");
            return (Criteria) this;
        }

        public Criteria andLogisticMarkIn(List<String> values) {
            addCriterion("logistic_mark in", values, "logisticMark");
            return (Criteria) this;
        }

        public Criteria andLogisticMarkNotIn(List<String> values) {
            addCriterion("logistic_mark not in", values, "logisticMark");
            return (Criteria) this;
        }

        public Criteria andLogisticMarkBetween(String value1, String value2) {
            addCriterion("logistic_mark between", value1, value2, "logisticMark");
            return (Criteria) this;
        }

        public Criteria andLogisticMarkNotBetween(String value1, String value2) {
            addCriterion("logistic_mark not between", value1, value2, "logisticMark");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlatformCodeIsNull() {
            addCriterion("delivery_platform_code is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlatformCodeIsNotNull() {
            addCriterion("delivery_platform_code is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlatformCodeEqualTo(Integer value) {
            addCriterion("delivery_platform_code =", value, "deliveryPlatformCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlatformCodeNotEqualTo(Integer value) {
            addCriterion("delivery_platform_code <>", value, "deliveryPlatformCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlatformCodeGreaterThan(Integer value) {
            addCriterion("delivery_platform_code >", value, "deliveryPlatformCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlatformCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_platform_code >=", value, "deliveryPlatformCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlatformCodeLessThan(Integer value) {
            addCriterion("delivery_platform_code <", value, "deliveryPlatformCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlatformCodeLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_platform_code <=", value, "deliveryPlatformCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlatformCodeIn(List<Integer> values) {
            addCriterion("delivery_platform_code in", values, "deliveryPlatformCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlatformCodeNotIn(List<Integer> values) {
            addCriterion("delivery_platform_code not in", values, "deliveryPlatformCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlatformCodeBetween(Integer value1, Integer value2) {
            addCriterion("delivery_platform_code between", value1, value2, "deliveryPlatformCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlatformCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_platform_code not between", value1, value2, "deliveryPlatformCode");
            return (Criteria) this;
        }

        public Criteria andCarrierCodeIsNull() {
            addCriterion("carrier_code is null");
            return (Criteria) this;
        }

        public Criteria andCarrierCodeIsNotNull() {
            addCriterion("carrier_code is not null");
            return (Criteria) this;
        }

        public Criteria andCarrierCodeEqualTo(Integer value) {
            addCriterion("carrier_code =", value, "carrierCode");
            return (Criteria) this;
        }

        public Criteria andCarrierCodeNotEqualTo(Integer value) {
            addCriterion("carrier_code <>", value, "carrierCode");
            return (Criteria) this;
        }

        public Criteria andCarrierCodeGreaterThan(Integer value) {
            addCriterion("carrier_code >", value, "carrierCode");
            return (Criteria) this;
        }

        public Criteria andCarrierCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("carrier_code >=", value, "carrierCode");
            return (Criteria) this;
        }

        public Criteria andCarrierCodeLessThan(Integer value) {
            addCriterion("carrier_code <", value, "carrierCode");
            return (Criteria) this;
        }

        public Criteria andCarrierCodeLessThanOrEqualTo(Integer value) {
            addCriterion("carrier_code <=", value, "carrierCode");
            return (Criteria) this;
        }

        public Criteria andCarrierCodeIn(List<Integer> values) {
            addCriterion("carrier_code in", values, "carrierCode");
            return (Criteria) this;
        }

        public Criteria andCarrierCodeNotIn(List<Integer> values) {
            addCriterion("carrier_code not in", values, "carrierCode");
            return (Criteria) this;
        }

        public Criteria andCarrierCodeBetween(Integer value1, Integer value2) {
            addCriterion("carrier_code between", value1, value2, "carrierCode");
            return (Criteria) this;
        }

        public Criteria andCarrierCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("carrier_code not between", value1, value2, "carrierCode");
            return (Criteria) this;
        }

        public Criteria andCarrierNameIsNull() {
            addCriterion("carrier_name is null");
            return (Criteria) this;
        }

        public Criteria andCarrierNameIsNotNull() {
            addCriterion("carrier_name is not null");
            return (Criteria) this;
        }

        public Criteria andCarrierNameEqualTo(String value) {
            addCriterion("carrier_name =", value, "carrierName");
            return (Criteria) this;
        }

        public Criteria andCarrierNameNotEqualTo(String value) {
            addCriterion("carrier_name <>", value, "carrierName");
            return (Criteria) this;
        }

        public Criteria andCarrierNameGreaterThan(String value) {
            addCriterion("carrier_name >", value, "carrierName");
            return (Criteria) this;
        }

        public Criteria andCarrierNameGreaterThanOrEqualTo(String value) {
            addCriterion("carrier_name >=", value, "carrierName");
            return (Criteria) this;
        }

        public Criteria andCarrierNameLessThan(String value) {
            addCriterion("carrier_name <", value, "carrierName");
            return (Criteria) this;
        }

        public Criteria andCarrierNameLessThanOrEqualTo(String value) {
            addCriterion("carrier_name <=", value, "carrierName");
            return (Criteria) this;
        }

        public Criteria andCarrierNameLike(String value) {
            addCriterion("carrier_name like", value, "carrierName");
            return (Criteria) this;
        }

        public Criteria andCarrierNameNotLike(String value) {
            addCriterion("carrier_name not like", value, "carrierName");
            return (Criteria) this;
        }

        public Criteria andCarrierNameIn(List<String> values) {
            addCriterion("carrier_name in", values, "carrierName");
            return (Criteria) this;
        }

        public Criteria andCarrierNameNotIn(List<String> values) {
            addCriterion("carrier_name not in", values, "carrierName");
            return (Criteria) this;
        }

        public Criteria andCarrierNameBetween(String value1, String value2) {
            addCriterion("carrier_name between", value1, value2, "carrierName");
            return (Criteria) this;
        }

        public Criteria andCarrierNameNotBetween(String value1, String value2) {
            addCriterion("carrier_name not between", value1, value2, "carrierName");
            return (Criteria) this;
        }

        public Criteria andOrderChannelCodeIsNull() {
            addCriterion("order_channel_code is null");
            return (Criteria) this;
        }

        public Criteria andOrderChannelCodeIsNotNull() {
            addCriterion("order_channel_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrderChannelCodeEqualTo(Integer value) {
            addCriterion("order_channel_code =", value, "orderChannelCode");
            return (Criteria) this;
        }

        public Criteria andOrderChannelCodeNotEqualTo(Integer value) {
            addCriterion("order_channel_code <>", value, "orderChannelCode");
            return (Criteria) this;
        }

        public Criteria andOrderChannelCodeGreaterThan(Integer value) {
            addCriterion("order_channel_code >", value, "orderChannelCode");
            return (Criteria) this;
        }

        public Criteria andOrderChannelCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_channel_code >=", value, "orderChannelCode");
            return (Criteria) this;
        }

        public Criteria andOrderChannelCodeLessThan(Integer value) {
            addCriterion("order_channel_code <", value, "orderChannelCode");
            return (Criteria) this;
        }

        public Criteria andOrderChannelCodeLessThanOrEqualTo(Integer value) {
            addCriterion("order_channel_code <=", value, "orderChannelCode");
            return (Criteria) this;
        }

        public Criteria andOrderChannelCodeIn(List<Integer> values) {
            addCriterion("order_channel_code in", values, "orderChannelCode");
            return (Criteria) this;
        }

        public Criteria andOrderChannelCodeNotIn(List<Integer> values) {
            addCriterion("order_channel_code not in", values, "orderChannelCode");
            return (Criteria) this;
        }

        public Criteria andOrderChannelCodeBetween(Integer value1, Integer value2) {
            addCriterion("order_channel_code between", value1, value2, "orderChannelCode");
            return (Criteria) this;
        }

        public Criteria andOrderChannelCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("order_channel_code not between", value1, value2, "orderChannelCode");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Date value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Date value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Date value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Date value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Date value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Date> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Date> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Date value1, Date value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Date value1, Date value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andUtimeIsNull() {
            addCriterion("utime is null");
            return (Criteria) this;
        }

        public Criteria andUtimeIsNotNull() {
            addCriterion("utime is not null");
            return (Criteria) this;
        }

        public Criteria andUtimeEqualTo(Date value) {
            addCriterion("utime =", value, "utime");
            return (Criteria) this;
        }

        public Criteria andUtimeNotEqualTo(Date value) {
            addCriterion("utime <>", value, "utime");
            return (Criteria) this;
        }

        public Criteria andUtimeGreaterThan(Date value) {
            addCriterion("utime >", value, "utime");
            return (Criteria) this;
        }

        public Criteria andUtimeGreaterThanOrEqualTo(Date value) {
            addCriterion("utime >=", value, "utime");
            return (Criteria) this;
        }

        public Criteria andUtimeLessThan(Date value) {
            addCriterion("utime <", value, "utime");
            return (Criteria) this;
        }

        public Criteria andUtimeLessThanOrEqualTo(Date value) {
            addCriterion("utime <=", value, "utime");
            return (Criteria) this;
        }

        public Criteria andUtimeIn(List<Date> values) {
            addCriterion("utime in", values, "utime");
            return (Criteria) this;
        }

        public Criteria andUtimeNotIn(List<Date> values) {
            addCriterion("utime not in", values, "utime");
            return (Criteria) this;
        }

        public Criteria andUtimeBetween(Date value1, Date value2) {
            addCriterion("utime between", value1, value2, "utime");
            return (Criteria) this;
        }

        public Criteria andUtimeNotBetween(Date value1, Date value2) {
            addCriterion("utime not between", value1, value2, "utime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}