package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryMergeOrderRecordDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryMergeOrderRecordDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeliveryMergeOrderRecordDOMapper {
    long countByExample(DeliveryMergeOrderRecordDOExample example);

    int deleteByExample(DeliveryMergeOrderRecordDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DeliveryMergeOrderRecordDO record);

    int insertSelective(DeliveryMergeOrderRecordDO record);

    List<DeliveryMergeOrderRecordDO> selectByExample(DeliveryMergeOrderRecordDOExample example);

    DeliveryMergeOrderRecordDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DeliveryMergeOrderRecordDO record, @Param("example") DeliveryMergeOrderRecordDOExample example);

    int updateByExample(@Param("record") DeliveryMergeOrderRecordDO record, @Param("example") DeliveryMergeOrderRecordDOExample example);

    int updateByPrimaryKeySelective(DeliveryMergeOrderRecordDO record);

    int updateByPrimaryKey(DeliveryMergeOrderRecordDO record);
}