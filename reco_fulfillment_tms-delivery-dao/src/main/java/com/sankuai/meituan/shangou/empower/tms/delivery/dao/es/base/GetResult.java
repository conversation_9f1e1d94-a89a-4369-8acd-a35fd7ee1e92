package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.base;


import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * ES get 查询结果
 * <AUTHOR>
 */
@Getter
@ToString
@EqualsAndHashCode
public class GetResult<T extends BaseEsPo> {

    private static final GetResult<?> EMPTY = new GetResult<>(StringUtils.EMPTY, StringUtils.EMPTY, null);

    /**
     * 索引
     */
    private final String index;

    /**
     * doc id
     */
    private final String id;

    /**
     * 数据
     */
    private final T data;

    public GetResult(String index, String id, T data) {
        this.index = index;
        this.id = id;
        this.data = data;
    }

    @SuppressWarnings("unchecked")
    public static <T extends BaseEsPo> GetResult<T> empty() {
        return (GetResult<T>) EMPTY;
    }

    public boolean isEmpty() {
        return Objects.isNull(data);
    }

    public boolean isNotEmpty() {
        return !isEmpty();
    }

}
