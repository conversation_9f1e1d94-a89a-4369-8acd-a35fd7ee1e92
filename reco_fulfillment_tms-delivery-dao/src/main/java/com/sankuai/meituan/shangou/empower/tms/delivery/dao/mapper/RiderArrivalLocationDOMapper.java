package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderArrivalLocationDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderArrivalLocationDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RiderArrivalLocationDOMapper {
    long countByExample(RiderArrivalLocationDOExample example);

    int deleteByExample(RiderArrivalLocationDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RiderArrivalLocationDO record);

    int insertSelective(RiderArrivalLocationDO record);

    List<RiderArrivalLocationDO> selectByExample(RiderArrivalLocationDOExample example);

    RiderArrivalLocationDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RiderArrivalLocationDO record, @Param("example") RiderArrivalLocationDOExample example);

    int updateByExample(@Param("record") RiderArrivalLocationDO record, @Param("example") RiderArrivalLocationDOExample example);

    int updateByPrimaryKeySelective(RiderArrivalLocationDO record);

    int updateByPrimaryKey(RiderArrivalLocationDO record);
}