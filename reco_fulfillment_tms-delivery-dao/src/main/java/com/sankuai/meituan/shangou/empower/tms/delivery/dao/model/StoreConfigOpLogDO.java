package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import lombok.Data;

import java.time.LocalDateTime;


/**
 * @Description: 门店配置操作流水，对应store_config_op_log表
 * @Author: zhangjian155
 * @Date: 2022/10/11 15:20
 */
@Data
public class StoreConfigOpLogDO {

    private Long id;

    private Long storeConfigId;

    private Long tenantId;

    private Long storeId;

    private Integer opType;

    private String opInfo;

    private Long operatorId;

    private String operatorName;

    private LocalDateTime ctime;
}