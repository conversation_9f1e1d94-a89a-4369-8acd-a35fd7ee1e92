package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mybatis;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.utils.MccDynamicConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.reflections.Reflections;
import org.reflections.scanners.FieldAnnotationsScanner;
import org.reflections.scanners.MethodAnnotationsScanner;
import org.reflections.scanners.TypeAnnotationsScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SlaveQueryInitService implements BeanPostProcessor {

    private volatile boolean isInit = false;

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {

        if(!isInit){
            try {
                startSlaveQueryAnnotation();
            }catch (Exception e){
                log.error("startSlaveQueryAnnotation error",e);
            }finally {
                isInit = true;
            }
        }
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    private void startSlaveQueryAnnotation(){
        log.info("SlaveQuery---------start");
        long startTime=System.nanoTime();

        Reflections reflections=initReflections();

        if(reflections==null){
            log.info("SlaveQuery---------reflections is null");
            return;
        }

        Set<Method> methodSet=reflections.getMethodsAnnotatedWith(SlaveQuery.class);
        if(CollectionUtils.isEmpty(methodSet)){
            log.info("SlaveQuery---------method is null");
            return;
        }
        SlaveQueryCacheService.getMethodSet().addAll(methodSet.stream().map(new Function<Method, String>() {
            @Override
            public String apply(Method method) {
                return method.getDeclaringClass().getName()+"."+method.getName();
            }
        }).collect(Collectors.toList()));
        log.info("SlaveQuery---------end cost:{} ns",(System.nanoTime()-startTime));
    }

    private Reflections initReflections(){

        String path= MccDynamicConfigUtil.getReflectionsPathListStr();
        if(StringUtils.isEmpty(path)){
            log.info("未配置反射扫描路径，initReflections将返回null");
            return null;
        }

        String[] pathStrList= StringUtils.split(path,",");
        List<URL> pathList=new ArrayList<>();
        for (String str: pathStrList){
            try {
                Collection<URL> list= ClasspathHelper.forPackage(str);
                if(CollectionUtils.isNotEmpty(list)){
                    pathList.addAll(list);
                }
            } catch (Exception e) {
                log.error("SlaveQuery add URL error path:{}",str,e);
            }
        }
        if(CollectionUtils.isEmpty(pathList)){
            log.info("initReflections pathList is empty");
            return null;
        }

        return new Reflections(new ConfigurationBuilder()
                .setUrls(pathList)
                .addScanners(new MethodAnnotationsScanner()));

    }

}
