package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRemindConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRemindConfigDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeliveryRemindConfigDOMapper {
    long countByExample(DeliveryRemindConfigDOExample example);

    int deleteByExample(DeliveryRemindConfigDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DeliveryRemindConfigDO record);

    int insertSelective(DeliveryRemindConfigDO record);

    List<DeliveryRemindConfigDO> selectByExample(DeliveryRemindConfigDOExample example);

    DeliveryRemindConfigDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DeliveryRemindConfigDO record, @Param("example") DeliveryRemindConfigDOExample example);

    int updateByExample(@Param("record") DeliveryRemindConfigDO record, @Param("example") DeliveryRemindConfigDOExample example);

    int updateByPrimaryKeySelective(DeliveryRemindConfigDO record);

    int updateByPrimaryKey(DeliveryRemindConfigDO record);
}