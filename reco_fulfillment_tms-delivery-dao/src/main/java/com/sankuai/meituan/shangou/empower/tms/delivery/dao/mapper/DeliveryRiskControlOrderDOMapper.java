package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeliveryRiskControlOrderDOMapper {
    long countByExample(DeliveryRiskControlOrderDOExample example);

    int deleteByExample(DeliveryRiskControlOrderDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DeliveryRiskControlOrderDO record);

    int insertSelective(DeliveryRiskControlOrderDO record);

    List<DeliveryRiskControlOrderDO> selectByExample(DeliveryRiskControlOrderDOExample example);

    DeliveryRiskControlOrderD<PERSON> selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DeliveryRiskControlOrderDO record, @Param("example") DeliveryRiskControlOrderDOExample example);

    int updateByExample(@Param("record") DeliveryRiskControlOrderDO record, @Param("example") DeliveryRiskControlOrderDOExample example);

    int updateByPrimaryKeySelective(DeliveryRiskControlOrderDO record);

    int updateByPrimaryKey(DeliveryRiskControlOrderDO record);
}