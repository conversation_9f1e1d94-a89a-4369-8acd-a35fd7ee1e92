package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DataReadyRecord;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DataReadyRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DataReadyRecordMapper {
    long countByExample(DataReadyRecordExample example);

    int deleteByExample(DataReadyRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DataReadyRecord record);

    int insertSelective(DataReadyRecord record);

    List<DataReadyRecord> selectByExample(DataReadyRecordExample example);

    DataReadyRecord selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DataReadyRecord record, @Param("example") DataReadyRecordExample example);

    int updateByExample(@Param("record") DataReadyRecord record, @Param("example") DataReadyRecordExample example);

    int updateByPrimaryKeySelective(DataReadyRecord record);

    int updateByPrimaryKey(DataReadyRecord record);
}