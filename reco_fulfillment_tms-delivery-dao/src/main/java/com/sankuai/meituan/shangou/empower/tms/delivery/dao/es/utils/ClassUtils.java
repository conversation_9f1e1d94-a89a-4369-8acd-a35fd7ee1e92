package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils;

import javax.annotation.Nullable;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * ClassUtils
 *
 * <AUTHOR>
 * @since 2022/4/6
 */
public class ClassUtils {

    /**
     * 获取类定义的泛型
     *
     * @param clazz 类
     * @return 泛型，如果没有泛型返回null
     */
    @SuppressWarnings("unchecked")
    @Nullable
    public static <T> Class<T> getGenericType(Class<?> clazz) {
        // 参数化类型，即泛型
        Type genericSuperclass = clazz.getGenericSuperclass();
        if (!(genericSuperclass instanceof ParameterizedType)) {
            return null;
        }
        ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
        // 获取0号即第一个泛型参数
        return (Class<T>) parameterizedType.getActualTypeArguments()[0];
    }

}
