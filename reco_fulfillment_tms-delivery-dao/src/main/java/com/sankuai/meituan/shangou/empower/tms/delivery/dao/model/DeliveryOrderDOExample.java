package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class DeliveryOrderDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public DeliveryOrderDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(Long value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(Long value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(Long value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(Long value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(Long value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<Long> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<Long> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(Long value1, Long value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(Long value1, Long value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(Long value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(Long value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(Long value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(Long value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(Long value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<Long> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<Long> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(Long value1, Long value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(Long value1, Long value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andFulfillmentOrderIdIsNull() {
            addCriterion("fulfillment_order_id is null");
            return (Criteria) this;
        }

        public Criteria andFulfillmentOrderIdIsNotNull() {
            addCriterion("fulfillment_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andFulfillmentOrderIdEqualTo(Long value) {
            addCriterion("fulfillment_order_id =", value, "fulfillmentOrderId");
            return (Criteria) this;
        }

        public Criteria andFulfillmentOrderIdNotEqualTo(Long value) {
            addCriterion("fulfillment_order_id <>", value, "fulfillmentOrderId");
            return (Criteria) this;
        }

        public Criteria andFulfillmentOrderIdGreaterThan(Long value) {
            addCriterion("fulfillment_order_id >", value, "fulfillmentOrderId");
            return (Criteria) this;
        }

        public Criteria andFulfillmentOrderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("fulfillment_order_id >=", value, "fulfillmentOrderId");
            return (Criteria) this;
        }

        public Criteria andFulfillmentOrderIdLessThan(Long value) {
            addCriterion("fulfillment_order_id <", value, "fulfillmentOrderId");
            return (Criteria) this;
        }

        public Criteria andFulfillmentOrderIdLessThanOrEqualTo(Long value) {
            addCriterion("fulfillment_order_id <=", value, "fulfillmentOrderId");
            return (Criteria) this;
        }

        public Criteria andFulfillmentOrderIdIn(List<Long> values) {
            addCriterion("fulfillment_order_id in", values, "fulfillmentOrderId");
            return (Criteria) this;
        }

        public Criteria andFulfillmentOrderIdNotIn(List<Long> values) {
            addCriterion("fulfillment_order_id not in", values, "fulfillmentOrderId");
            return (Criteria) this;
        }

        public Criteria andFulfillmentOrderIdBetween(Long value1, Long value2) {
            addCriterion("fulfillment_order_id between", value1, value2, "fulfillmentOrderId");
            return (Criteria) this;
        }

        public Criteria andFulfillmentOrderIdNotBetween(Long value1, Long value2) {
            addCriterion("fulfillment_order_id not between", value1, value2, "fulfillmentOrderId");
            return (Criteria) this;
        }

        public Criteria andPlatformSourceIsNull() {
            addCriterion("platform_source is null");
            return (Criteria) this;
        }

        public Criteria andPlatformSourceIsNotNull() {
            addCriterion("platform_source is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformSourceEqualTo(Integer value) {
            addCriterion("platform_source =", value, "platformSource");
            return (Criteria) this;
        }

        public Criteria andPlatformSourceNotEqualTo(Integer value) {
            addCriterion("platform_source <>", value, "platformSource");
            return (Criteria) this;
        }

        public Criteria andPlatformSourceGreaterThan(Integer value) {
            addCriterion("platform_source >", value, "platformSource");
            return (Criteria) this;
        }

        public Criteria andPlatformSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("platform_source >=", value, "platformSource");
            return (Criteria) this;
        }

        public Criteria andPlatformSourceLessThan(Integer value) {
            addCriterion("platform_source <", value, "platformSource");
            return (Criteria) this;
        }

        public Criteria andPlatformSourceLessThanOrEqualTo(Integer value) {
            addCriterion("platform_source <=", value, "platformSource");
            return (Criteria) this;
        }

        public Criteria andPlatformSourceIn(List<Integer> values) {
            addCriterion("platform_source in", values, "platformSource");
            return (Criteria) this;
        }

        public Criteria andPlatformSourceNotIn(List<Integer> values) {
            addCriterion("platform_source not in", values, "platformSource");
            return (Criteria) this;
        }

        public Criteria andPlatformSourceBetween(Integer value1, Integer value2) {
            addCriterion("platform_source between", value1, value2, "platformSource");
            return (Criteria) this;
        }

        public Criteria andPlatformSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("platform_source not between", value1, value2, "platformSource");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdIsNull() {
            addCriterion("channel_order_id is null");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdIsNotNull() {
            addCriterion("channel_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdEqualTo(String value) {
            addCriterion("channel_order_id =", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotEqualTo(String value) {
            addCriterion("channel_order_id <>", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdGreaterThan(String value) {
            addCriterion("channel_order_id >", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("channel_order_id >=", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdLessThan(String value) {
            addCriterion("channel_order_id <", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdLessThanOrEqualTo(String value) {
            addCriterion("channel_order_id <=", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdLike(String value) {
            addCriterion("channel_order_id like", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotLike(String value) {
            addCriterion("channel_order_id not like", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdIn(List<String> values) {
            addCriterion("channel_order_id in", values, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotIn(List<String> values) {
            addCriterion("channel_order_id not in", values, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdBetween(String value1, String value2) {
            addCriterion("channel_order_id between", value1, value2, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotBetween(String value1, String value2) {
            addCriterion("channel_order_id not between", value1, value2, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeIsNull() {
            addCriterion("order_biz_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeIsNotNull() {
            addCriterion("order_biz_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeEqualTo(Integer value) {
            addCriterion("order_biz_type =", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeNotEqualTo(Integer value) {
            addCriterion("order_biz_type <>", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeGreaterThan(Integer value) {
            addCriterion("order_biz_type >", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_biz_type >=", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeLessThan(Integer value) {
            addCriterion("order_biz_type <", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeLessThanOrEqualTo(Integer value) {
            addCriterion("order_biz_type <=", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeIn(List<Integer> values) {
            addCriterion("order_biz_type in", values, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeNotIn(List<Integer> values) {
            addCriterion("order_biz_type not in", values, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeBetween(Integer value1, Integer value2) {
            addCriterion("order_biz_type between", value1, value2, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("order_biz_type not between", value1, value2, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andDaySeqIsNull() {
            addCriterion("day_seq is null");
            return (Criteria) this;
        }

        public Criteria andDaySeqIsNotNull() {
            addCriterion("day_seq is not null");
            return (Criteria) this;
        }

        public Criteria andDaySeqEqualTo(Integer value) {
            addCriterion("day_seq =", value, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqNotEqualTo(Integer value) {
            addCriterion("day_seq <>", value, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqGreaterThan(Integer value) {
            addCriterion("day_seq >", value, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("day_seq >=", value, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqLessThan(Integer value) {
            addCriterion("day_seq <", value, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqLessThanOrEqualTo(Integer value) {
            addCriterion("day_seq <=", value, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqIn(List<Integer> values) {
            addCriterion("day_seq in", values, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqNotIn(List<Integer> values) {
            addCriterion("day_seq not in", values, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqBetween(Integer value1, Integer value2) {
            addCriterion("day_seq between", value1, value2, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqNotBetween(Integer value1, Integer value2) {
            addCriterion("day_seq not between", value1, value2, "daySeq");
            return (Criteria) this;
        }

        public Criteria andReservedIsNull() {
            addCriterion("reserved is null");
            return (Criteria) this;
        }

        public Criteria andReservedIsNotNull() {
            addCriterion("reserved is not null");
            return (Criteria) this;
        }

        public Criteria andReservedEqualTo(Integer value) {
            addCriterion("reserved =", value, "reserved");
            return (Criteria) this;
        }

        public Criteria andReservedNotEqualTo(Integer value) {
            addCriterion("reserved <>", value, "reserved");
            return (Criteria) this;
        }

        public Criteria andReservedGreaterThan(Integer value) {
            addCriterion("reserved >", value, "reserved");
            return (Criteria) this;
        }

        public Criteria andReservedGreaterThanOrEqualTo(Integer value) {
            addCriterion("reserved >=", value, "reserved");
            return (Criteria) this;
        }

        public Criteria andReservedLessThan(Integer value) {
            addCriterion("reserved <", value, "reserved");
            return (Criteria) this;
        }

        public Criteria andReservedLessThanOrEqualTo(Integer value) {
            addCriterion("reserved <=", value, "reserved");
            return (Criteria) this;
        }

        public Criteria andReservedIn(List<Integer> values) {
            addCriterion("reserved in", values, "reserved");
            return (Criteria) this;
        }

        public Criteria andReservedNotIn(List<Integer> values) {
            addCriterion("reserved not in", values, "reserved");
            return (Criteria) this;
        }

        public Criteria andReservedBetween(Integer value1, Integer value2) {
            addCriterion("reserved between", value1, value2, "reserved");
            return (Criteria) this;
        }

        public Criteria andReservedNotBetween(Integer value1, Integer value2) {
            addCriterion("reserved not between", value1, value2, "reserved");
            return (Criteria) this;
        }

        public Criteria andOrderSourceIsNull() {
            addCriterion("order_source is null");
            return (Criteria) this;
        }

        public Criteria andOrderSourceIsNotNull() {
            addCriterion("order_source is not null");
            return (Criteria) this;
        }

        public Criteria andOrderSourceEqualTo(Integer value) {
            addCriterion("order_source =", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceNotEqualTo(Integer value) {
            addCriterion("order_source <>", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceGreaterThan(Integer value) {
            addCriterion("order_source >", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_source >=", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceLessThan(Integer value) {
            addCriterion("order_source <", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceLessThanOrEqualTo(Integer value) {
            addCriterion("order_source <=", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceIn(List<Integer> values) {
            addCriterion("order_source in", values, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceNotIn(List<Integer> values) {
            addCriterion("order_source not in", values, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceBetween(Integer value1, Integer value2) {
            addCriterion("order_source between", value1, value2, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("order_source not between", value1, value2, "orderSource");
            return (Criteria) this;
        }

        public Criteria andReceiverNameIsNull() {
            addCriterion("receiver_name is null");
            return (Criteria) this;
        }

        public Criteria andReceiverNameIsNotNull() {
            addCriterion("receiver_name is not null");
            return (Criteria) this;
        }

        public Criteria andReceiverNameEqualTo(String value) {
            addCriterion("receiver_name =", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameNotEqualTo(String value) {
            addCriterion("receiver_name <>", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameGreaterThan(String value) {
            addCriterion("receiver_name >", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameGreaterThanOrEqualTo(String value) {
            addCriterion("receiver_name >=", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameLessThan(String value) {
            addCriterion("receiver_name <", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameLessThanOrEqualTo(String value) {
            addCriterion("receiver_name <=", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameLike(String value) {
            addCriterion("receiver_name like", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameNotLike(String value) {
            addCriterion("receiver_name not like", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameIn(List<String> values) {
            addCriterion("receiver_name in", values, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameNotIn(List<String> values) {
            addCriterion("receiver_name not in", values, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameBetween(String value1, String value2) {
            addCriterion("receiver_name between", value1, value2, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameNotBetween(String value1, String value2) {
            addCriterion("receiver_name not between", value1, value2, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneIsNull() {
            addCriterion("receiver_phone is null");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneIsNotNull() {
            addCriterion("receiver_phone is not null");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneEqualTo(String value) {
            addCriterion("receiver_phone =", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneNotEqualTo(String value) {
            addCriterion("receiver_phone <>", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneGreaterThan(String value) {
            addCriterion("receiver_phone >", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("receiver_phone >=", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneLessThan(String value) {
            addCriterion("receiver_phone <", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneLessThanOrEqualTo(String value) {
            addCriterion("receiver_phone <=", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneLike(String value) {
            addCriterion("receiver_phone like", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneNotLike(String value) {
            addCriterion("receiver_phone not like", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneIn(List<String> values) {
            addCriterion("receiver_phone in", values, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneNotIn(List<String> values) {
            addCriterion("receiver_phone not in", values, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneBetween(String value1, String value2) {
            addCriterion("receiver_phone between", value1, value2, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneNotBetween(String value1, String value2) {
            addCriterion("receiver_phone not between", value1, value2, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPrivacyPhoneIsNull() {
            addCriterion("receiver_privacy_phone is null");
            return (Criteria) this;
        }

        public Criteria andReceiverPrivacyPhoneIsNotNull() {
            addCriterion("receiver_privacy_phone is not null");
            return (Criteria) this;
        }

        public Criteria andReceiverPrivacyPhoneEqualTo(String value) {
            addCriterion("receiver_privacy_phone =", value, "receiverPrivacyPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPrivacyPhoneNotEqualTo(String value) {
            addCriterion("receiver_privacy_phone <>", value, "receiverPrivacyPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPrivacyPhoneGreaterThan(String value) {
            addCriterion("receiver_privacy_phone >", value, "receiverPrivacyPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPrivacyPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("receiver_privacy_phone >=", value, "receiverPrivacyPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPrivacyPhoneLessThan(String value) {
            addCriterion("receiver_privacy_phone <", value, "receiverPrivacyPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPrivacyPhoneLessThanOrEqualTo(String value) {
            addCriterion("receiver_privacy_phone <=", value, "receiverPrivacyPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPrivacyPhoneLike(String value) {
            addCriterion("receiver_privacy_phone like", value, "receiverPrivacyPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPrivacyPhoneNotLike(String value) {
            addCriterion("receiver_privacy_phone not like", value, "receiverPrivacyPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPrivacyPhoneIn(List<String> values) {
            addCriterion("receiver_privacy_phone in", values, "receiverPrivacyPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPrivacyPhoneNotIn(List<String> values) {
            addCriterion("receiver_privacy_phone not in", values, "receiverPrivacyPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPrivacyPhoneBetween(String value1, String value2) {
            addCriterion("receiver_privacy_phone between", value1, value2, "receiverPrivacyPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPrivacyPhoneNotBetween(String value1, String value2) {
            addCriterion("receiver_privacy_phone not between", value1, value2, "receiverPrivacyPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverAddressIsNull() {
            addCriterion("receiver_address is null");
            return (Criteria) this;
        }

        public Criteria andReceiverAddressIsNotNull() {
            addCriterion("receiver_address is not null");
            return (Criteria) this;
        }

        public Criteria andReceiverAddressEqualTo(String value) {
            addCriterion("receiver_address =", value, "receiverAddress");
            return (Criteria) this;
        }

        public Criteria andReceiverAddressNotEqualTo(String value) {
            addCriterion("receiver_address <>", value, "receiverAddress");
            return (Criteria) this;
        }

        public Criteria andReceiverAddressGreaterThan(String value) {
            addCriterion("receiver_address >", value, "receiverAddress");
            return (Criteria) this;
        }

        public Criteria andReceiverAddressGreaterThanOrEqualTo(String value) {
            addCriterion("receiver_address >=", value, "receiverAddress");
            return (Criteria) this;
        }

        public Criteria andReceiverAddressLessThan(String value) {
            addCriterion("receiver_address <", value, "receiverAddress");
            return (Criteria) this;
        }

        public Criteria andReceiverAddressLessThanOrEqualTo(String value) {
            addCriterion("receiver_address <=", value, "receiverAddress");
            return (Criteria) this;
        }

        public Criteria andReceiverAddressLike(String value) {
            addCriterion("receiver_address like", value, "receiverAddress");
            return (Criteria) this;
        }

        public Criteria andReceiverAddressNotLike(String value) {
            addCriterion("receiver_address not like", value, "receiverAddress");
            return (Criteria) this;
        }

        public Criteria andReceiverAddressIn(List<String> values) {
            addCriterion("receiver_address in", values, "receiverAddress");
            return (Criteria) this;
        }

        public Criteria andReceiverAddressNotIn(List<String> values) {
            addCriterion("receiver_address not in", values, "receiverAddress");
            return (Criteria) this;
        }

        public Criteria andReceiverAddressBetween(String value1, String value2) {
            addCriterion("receiver_address between", value1, value2, "receiverAddress");
            return (Criteria) this;
        }

        public Criteria andReceiverAddressNotBetween(String value1, String value2) {
            addCriterion("receiver_address not between", value1, value2, "receiverAddress");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryTimeIsNull() {
            addCriterion("estimated_delivery_time is null");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryTimeIsNotNull() {
            addCriterion("estimated_delivery_time is not null");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryTimeEqualTo(LocalDateTime value) {
            addCriterion("estimated_delivery_time =", value, "estimatedDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryTimeNotEqualTo(LocalDateTime value) {
            addCriterion("estimated_delivery_time <>", value, "estimatedDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryTimeGreaterThan(LocalDateTime value) {
            addCriterion("estimated_delivery_time >", value, "estimatedDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("estimated_delivery_time >=", value, "estimatedDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryTimeLessThan(LocalDateTime value) {
            addCriterion("estimated_delivery_time <", value, "estimatedDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("estimated_delivery_time <=", value, "estimatedDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryTimeIn(List<LocalDateTime> values) {
            addCriterion("estimated_delivery_time in", values, "estimatedDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryTimeNotIn(List<LocalDateTime> values) {
            addCriterion("estimated_delivery_time not in", values, "estimatedDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("estimated_delivery_time between", value1, value2, "estimatedDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("estimated_delivery_time not between", value1, value2, "estimatedDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryEndTimeIsNull() {
            addCriterion("estimated_delivery_end_time is null");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryEndTimeIsNotNull() {
            addCriterion("estimated_delivery_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryEndTimeEqualTo(LocalDateTime value) {
            addCriterion("estimated_delivery_end_time =", value, "estimatedDeliveryEndTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryEndTimeNotEqualTo(LocalDateTime value) {
            addCriterion("estimated_delivery_end_time <>", value, "estimatedDeliveryEndTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryEndTimeGreaterThan(LocalDateTime value) {
            addCriterion("estimated_delivery_end_time >", value, "estimatedDeliveryEndTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryEndTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("estimated_delivery_end_time >=", value, "estimatedDeliveryEndTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryEndTimeLessThan(LocalDateTime value) {
            addCriterion("estimated_delivery_end_time <", value, "estimatedDeliveryEndTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryEndTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("estimated_delivery_end_time <=", value, "estimatedDeliveryEndTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryEndTimeIn(List<LocalDateTime> values) {
            addCriterion("estimated_delivery_end_time in", values, "estimatedDeliveryEndTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryEndTimeNotIn(List<LocalDateTime> values) {
            addCriterion("estimated_delivery_end_time not in", values, "estimatedDeliveryEndTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryEndTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("estimated_delivery_end_time between", value1, value2, "estimatedDeliveryEndTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedDeliveryEndTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("estimated_delivery_end_time not between", value1, value2, "estimatedDeliveryEndTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIsNull() {
            addCriterion("delivery_channel is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIsNotNull() {
            addCriterion("delivery_channel is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelEqualTo(Integer value) {
            addCriterion("delivery_channel =", value, "deliveryChannel");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelNotEqualTo(Integer value) {
            addCriterion("delivery_channel <>", value, "deliveryChannel");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelGreaterThan(Integer value) {
            addCriterion("delivery_channel >", value, "deliveryChannel");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_channel >=", value, "deliveryChannel");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelLessThan(Integer value) {
            addCriterion("delivery_channel <", value, "deliveryChannel");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_channel <=", value, "deliveryChannel");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIn(List<Integer> values) {
            addCriterion("delivery_channel in", values, "deliveryChannel");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelNotIn(List<Integer> values) {
            addCriterion("delivery_channel not in", values, "deliveryChannel");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelBetween(Integer value1, Integer value2) {
            addCriterion("delivery_channel between", value1, value2, "deliveryChannel");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_channel not between", value1, value2, "deliveryChannel");
            return (Criteria) this;
        }

        public Criteria andChannelDeliveryIdIsNull() {
            addCriterion("channel_delivery_id is null");
            return (Criteria) this;
        }

        public Criteria andChannelDeliveryIdIsNotNull() {
            addCriterion("channel_delivery_id is not null");
            return (Criteria) this;
        }

        public Criteria andChannelDeliveryIdEqualTo(String value) {
            addCriterion("channel_delivery_id =", value, "channelDeliveryId");
            return (Criteria) this;
        }

        public Criteria andChannelDeliveryIdNotEqualTo(String value) {
            addCriterion("channel_delivery_id <>", value, "channelDeliveryId");
            return (Criteria) this;
        }

        public Criteria andChannelDeliveryIdGreaterThan(String value) {
            addCriterion("channel_delivery_id >", value, "channelDeliveryId");
            return (Criteria) this;
        }

        public Criteria andChannelDeliveryIdGreaterThanOrEqualTo(String value) {
            addCriterion("channel_delivery_id >=", value, "channelDeliveryId");
            return (Criteria) this;
        }

        public Criteria andChannelDeliveryIdLessThan(String value) {
            addCriterion("channel_delivery_id <", value, "channelDeliveryId");
            return (Criteria) this;
        }

        public Criteria andChannelDeliveryIdLessThanOrEqualTo(String value) {
            addCriterion("channel_delivery_id <=", value, "channelDeliveryId");
            return (Criteria) this;
        }

        public Criteria andChannelDeliveryIdLike(String value) {
            addCriterion("channel_delivery_id like", value, "channelDeliveryId");
            return (Criteria) this;
        }

        public Criteria andChannelDeliveryIdNotLike(String value) {
            addCriterion("channel_delivery_id not like", value, "channelDeliveryId");
            return (Criteria) this;
        }

        public Criteria andChannelDeliveryIdIn(List<String> values) {
            addCriterion("channel_delivery_id in", values, "channelDeliveryId");
            return (Criteria) this;
        }

        public Criteria andChannelDeliveryIdNotIn(List<String> values) {
            addCriterion("channel_delivery_id not in", values, "channelDeliveryId");
            return (Criteria) this;
        }

        public Criteria andChannelDeliveryIdBetween(String value1, String value2) {
            addCriterion("channel_delivery_id between", value1, value2, "channelDeliveryId");
            return (Criteria) this;
        }

        public Criteria andChannelDeliveryIdNotBetween(String value1, String value2) {
            addCriterion("channel_delivery_id not between", value1, value2, "channelDeliveryId");
            return (Criteria) this;
        }

        public Criteria andChannelServicePackageCodeIsNull() {
            addCriterion("channel_service_package_code is null");
            return (Criteria) this;
        }

        public Criteria andChannelServicePackageCodeIsNotNull() {
            addCriterion("channel_service_package_code is not null");
            return (Criteria) this;
        }

        public Criteria andChannelServicePackageCodeEqualTo(String value) {
            addCriterion("channel_service_package_code =", value, "channelServicePackageCode");
            return (Criteria) this;
        }

        public Criteria andChannelServicePackageCodeNotEqualTo(String value) {
            addCriterion("channel_service_package_code <>", value, "channelServicePackageCode");
            return (Criteria) this;
        }

        public Criteria andChannelServicePackageCodeGreaterThan(String value) {
            addCriterion("channel_service_package_code >", value, "channelServicePackageCode");
            return (Criteria) this;
        }

        public Criteria andChannelServicePackageCodeGreaterThanOrEqualTo(String value) {
            addCriterion("channel_service_package_code >=", value, "channelServicePackageCode");
            return (Criteria) this;
        }

        public Criteria andChannelServicePackageCodeLessThan(String value) {
            addCriterion("channel_service_package_code <", value, "channelServicePackageCode");
            return (Criteria) this;
        }

        public Criteria andChannelServicePackageCodeLessThanOrEqualTo(String value) {
            addCriterion("channel_service_package_code <=", value, "channelServicePackageCode");
            return (Criteria) this;
        }

        public Criteria andChannelServicePackageCodeLike(String value) {
            addCriterion("channel_service_package_code like", value, "channelServicePackageCode");
            return (Criteria) this;
        }

        public Criteria andChannelServicePackageCodeNotLike(String value) {
            addCriterion("channel_service_package_code not like", value, "channelServicePackageCode");
            return (Criteria) this;
        }

        public Criteria andChannelServicePackageCodeIn(List<String> values) {
            addCriterion("channel_service_package_code in", values, "channelServicePackageCode");
            return (Criteria) this;
        }

        public Criteria andChannelServicePackageCodeNotIn(List<String> values) {
            addCriterion("channel_service_package_code not in", values, "channelServicePackageCode");
            return (Criteria) this;
        }

        public Criteria andChannelServicePackageCodeBetween(String value1, String value2) {
            addCriterion("channel_service_package_code between", value1, value2, "channelServicePackageCode");
            return (Criteria) this;
        }

        public Criteria andChannelServicePackageCodeNotBetween(String value1, String value2) {
            addCriterion("channel_service_package_code not between", value1, value2, "channelServicePackageCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIsNull() {
            addCriterion("delivery_status is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIsNotNull() {
            addCriterion("delivery_status is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusEqualTo(Integer value) {
            addCriterion("delivery_status =", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotEqualTo(Integer value) {
            addCriterion("delivery_status <>", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusGreaterThan(Integer value) {
            addCriterion("delivery_status >", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_status >=", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusLessThan(Integer value) {
            addCriterion("delivery_status <", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_status <=", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIn(List<Integer> values) {
            addCriterion("delivery_status in", values, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotIn(List<Integer> values) {
            addCriterion("delivery_status not in", values, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusBetween(Integer value1, Integer value2) {
            addCriterion("delivery_status between", value1, value2, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_status not between", value1, value2, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionTypeIsNull() {
            addCriterion("delivery_exception_type is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionTypeIsNotNull() {
            addCriterion("delivery_exception_type is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionTypeEqualTo(Integer value) {
            addCriterion("delivery_exception_type =", value, "deliveryExceptionType");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionTypeNotEqualTo(Integer value) {
            addCriterion("delivery_exception_type <>", value, "deliveryExceptionType");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionTypeGreaterThan(Integer value) {
            addCriterion("delivery_exception_type >", value, "deliveryExceptionType");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_exception_type >=", value, "deliveryExceptionType");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionTypeLessThan(Integer value) {
            addCriterion("delivery_exception_type <", value, "deliveryExceptionType");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionTypeLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_exception_type <=", value, "deliveryExceptionType");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionTypeIn(List<Integer> values) {
            addCriterion("delivery_exception_type in", values, "deliveryExceptionType");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionTypeNotIn(List<Integer> values) {
            addCriterion("delivery_exception_type not in", values, "deliveryExceptionType");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionTypeBetween(Integer value1, Integer value2) {
            addCriterion("delivery_exception_type between", value1, value2, "deliveryExceptionType");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_exception_type not between", value1, value2, "deliveryExceptionType");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionDescriptionIsNull() {
            addCriterion("delivery_exception_description is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionDescriptionIsNotNull() {
            addCriterion("delivery_exception_description is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionDescriptionEqualTo(String value) {
            addCriterion("delivery_exception_description =", value, "deliveryExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionDescriptionNotEqualTo(String value) {
            addCriterion("delivery_exception_description <>", value, "deliveryExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionDescriptionGreaterThan(String value) {
            addCriterion("delivery_exception_description >", value, "deliveryExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("delivery_exception_description >=", value, "deliveryExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionDescriptionLessThan(String value) {
            addCriterion("delivery_exception_description <", value, "deliveryExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionDescriptionLessThanOrEqualTo(String value) {
            addCriterion("delivery_exception_description <=", value, "deliveryExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionDescriptionLike(String value) {
            addCriterion("delivery_exception_description like", value, "deliveryExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionDescriptionNotLike(String value) {
            addCriterion("delivery_exception_description not like", value, "deliveryExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionDescriptionIn(List<String> values) {
            addCriterion("delivery_exception_description in", values, "deliveryExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionDescriptionNotIn(List<String> values) {
            addCriterion("delivery_exception_description not in", values, "deliveryExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionDescriptionBetween(String value1, String value2) {
            addCriterion("delivery_exception_description between", value1, value2, "deliveryExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionDescriptionNotBetween(String value1, String value2) {
            addCriterion("delivery_exception_description not between", value1, value2, "deliveryExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andRiderNameIsNull() {
            addCriterion("rider_name is null");
            return (Criteria) this;
        }

        public Criteria andRiderNameIsNotNull() {
            addCriterion("rider_name is not null");
            return (Criteria) this;
        }

        public Criteria andRiderNameEqualTo(String value) {
            addCriterion("rider_name =", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameNotEqualTo(String value) {
            addCriterion("rider_name <>", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameGreaterThan(String value) {
            addCriterion("rider_name >", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameGreaterThanOrEqualTo(String value) {
            addCriterion("rider_name >=", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameLessThan(String value) {
            addCriterion("rider_name <", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameLessThanOrEqualTo(String value) {
            addCriterion("rider_name <=", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameLike(String value) {
            addCriterion("rider_name like", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameNotLike(String value) {
            addCriterion("rider_name not like", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameIn(List<String> values) {
            addCriterion("rider_name in", values, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameNotIn(List<String> values) {
            addCriterion("rider_name not in", values, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameBetween(String value1, String value2) {
            addCriterion("rider_name between", value1, value2, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameNotBetween(String value1, String value2) {
            addCriterion("rider_name not between", value1, value2, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneIsNull() {
            addCriterion("rider_phone is null");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneIsNotNull() {
            addCriterion("rider_phone is not null");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneEqualTo(String value) {
            addCriterion("rider_phone =", value, "riderPhone");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneNotEqualTo(String value) {
            addCriterion("rider_phone <>", value, "riderPhone");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneGreaterThan(String value) {
            addCriterion("rider_phone >", value, "riderPhone");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("rider_phone >=", value, "riderPhone");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneLessThan(String value) {
            addCriterion("rider_phone <", value, "riderPhone");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneLessThanOrEqualTo(String value) {
            addCriterion("rider_phone <=", value, "riderPhone");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneLike(String value) {
            addCriterion("rider_phone like", value, "riderPhone");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneNotLike(String value) {
            addCriterion("rider_phone not like", value, "riderPhone");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneIn(List<String> values) {
            addCriterion("rider_phone in", values, "riderPhone");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneNotIn(List<String> values) {
            addCriterion("rider_phone not in", values, "riderPhone");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneBetween(String value1, String value2) {
            addCriterion("rider_phone between", value1, value2, "riderPhone");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneNotBetween(String value1, String value2) {
            addCriterion("rider_phone not between", value1, value2, "riderPhone");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneTokenIsNull() {
            addCriterion("rider_phone_token is null");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneTokenIsNotNull() {
            addCriterion("rider_phone_token is not null");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneTokenEqualTo(String value) {
            addCriterion("rider_phone_token =", value, "riderPhoneToken");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneTokenNotEqualTo(String value) {
            addCriterion("rider_phone_token <>", value, "riderPhoneToken");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneTokenGreaterThan(String value) {
            addCriterion("rider_phone_token >", value, "riderPhoneToken");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneTokenGreaterThanOrEqualTo(String value) {
            addCriterion("rider_phone_token >=", value, "riderPhoneToken");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneTokenLessThan(String value) {
            addCriterion("rider_phone_token <", value, "riderPhoneToken");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneTokenLessThanOrEqualTo(String value) {
            addCriterion("rider_phone_token <=", value, "riderPhoneToken");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneTokenLike(String value) {
            addCriterion("rider_phone_token like", value, "riderPhoneToken");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneTokenNotLike(String value) {
            addCriterion("rider_phone_token not like", value, "riderPhoneToken");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneTokenIn(List<String> values) {
            addCriterion("rider_phone_token in", values, "riderPhoneToken");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneTokenNotIn(List<String> values) {
            addCriterion("rider_phone_token not in", values, "riderPhoneToken");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneTokenBetween(String value1, String value2) {
            addCriterion("rider_phone_token between", value1, value2, "riderPhoneToken");
            return (Criteria) this;
        }

        public Criteria andRiderPhoneTokenNotBetween(String value1, String value2) {
            addCriterion("rider_phone_token not between", value1, value2, "riderPhoneToken");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIsNull() {
            addCriterion("rider_account_id is null");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIsNotNull() {
            addCriterion("rider_account_id is not null");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdEqualTo(Long value) {
            addCriterion("rider_account_id =", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotEqualTo(Long value) {
            addCriterion("rider_account_id <>", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdGreaterThan(Long value) {
            addCriterion("rider_account_id >", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rider_account_id >=", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdLessThan(Long value) {
            addCriterion("rider_account_id <", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdLessThanOrEqualTo(Long value) {
            addCriterion("rider_account_id <=", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIn(List<Long> values) {
            addCriterion("rider_account_id in", values, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotIn(List<Long> values) {
            addCriterion("rider_account_id not in", values, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdBetween(Long value1, Long value2) {
            addCriterion("rider_account_id between", value1, value2, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotBetween(Long value1, Long value2) {
            addCriterion("rider_account_id not between", value1, value2, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andActiveStatusIsNull() {
            addCriterion("active_status is null");
            return (Criteria) this;
        }

        public Criteria andActiveStatusIsNotNull() {
            addCriterion("active_status is not null");
            return (Criteria) this;
        }

        public Criteria andActiveStatusEqualTo(Long value) {
            addCriterion("active_status =", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusNotEqualTo(Long value) {
            addCriterion("active_status <>", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusGreaterThan(Long value) {
            addCriterion("active_status >", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusGreaterThanOrEqualTo(Long value) {
            addCriterion("active_status >=", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusLessThan(Long value) {
            addCriterion("active_status <", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusLessThanOrEqualTo(Long value) {
            addCriterion("active_status <=", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusIn(List<Long> values) {
            addCriterion("active_status in", values, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusNotIn(List<Long> values) {
            addCriterion("active_status not in", values, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusBetween(Long value1, Long value2) {
            addCriterion("active_status between", value1, value2, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusNotBetween(Long value1, Long value2) {
            addCriterion("active_status not between", value1, value2, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andLastEventTimeIsNull() {
            addCriterion("last_event_time is null");
            return (Criteria) this;
        }

        public Criteria andLastEventTimeIsNotNull() {
            addCriterion("last_event_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastEventTimeEqualTo(LocalDateTime value) {
            addCriterion("last_event_time =", value, "lastEventTime");
            return (Criteria) this;
        }

        public Criteria andLastEventTimeNotEqualTo(LocalDateTime value) {
            addCriterion("last_event_time <>", value, "lastEventTime");
            return (Criteria) this;
        }

        public Criteria andLastEventTimeGreaterThan(LocalDateTime value) {
            addCriterion("last_event_time >", value, "lastEventTime");
            return (Criteria) this;
        }

        public Criteria andLastEventTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_event_time >=", value, "lastEventTime");
            return (Criteria) this;
        }

        public Criteria andLastEventTimeLessThan(LocalDateTime value) {
            addCriterion("last_event_time <", value, "lastEventTime");
            return (Criteria) this;
        }

        public Criteria andLastEventTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_event_time <=", value, "lastEventTime");
            return (Criteria) this;
        }

        public Criteria andLastEventTimeIn(List<LocalDateTime> values) {
            addCriterion("last_event_time in", values, "lastEventTime");
            return (Criteria) this;
        }

        public Criteria andLastEventTimeNotIn(List<LocalDateTime> values) {
            addCriterion("last_event_time not in", values, "lastEventTime");
            return (Criteria) this;
        }

        public Criteria andLastEventTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_event_time between", value1, value2, "lastEventTime");
            return (Criteria) this;
        }

        public Criteria andLastEventTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_event_time not between", value1, value2, "lastEventTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryDoneTimeIsNull() {
            addCriterion("delivery_done_time is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDoneTimeIsNotNull() {
            addCriterion("delivery_done_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDoneTimeEqualTo(LocalDateTime value) {
            addCriterion("delivery_done_time =", value, "deliveryDoneTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryDoneTimeNotEqualTo(LocalDateTime value) {
            addCriterion("delivery_done_time <>", value, "deliveryDoneTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryDoneTimeGreaterThan(LocalDateTime value) {
            addCriterion("delivery_done_time >", value, "deliveryDoneTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryDoneTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("delivery_done_time >=", value, "deliveryDoneTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryDoneTimeLessThan(LocalDateTime value) {
            addCriterion("delivery_done_time <", value, "deliveryDoneTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryDoneTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("delivery_done_time <=", value, "deliveryDoneTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryDoneTimeIn(List<LocalDateTime> values) {
            addCriterion("delivery_done_time in", values, "deliveryDoneTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryDoneTimeNotIn(List<LocalDateTime> values) {
            addCriterion("delivery_done_time not in", values, "deliveryDoneTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryDoneTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("delivery_done_time between", value1, value2, "deliveryDoneTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryDoneTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("delivery_done_time not between", value1, value2, "deliveryDoneTime");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andDeliveryFeeIsNull() {
            addCriterion("delivery_fee is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryFeeIsNotNull() {
            addCriterion("delivery_fee is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryFeeEqualTo(BigDecimal value) {
            addCriterion("delivery_fee =", value, "deliveryFee");
            return (Criteria) this;
        }

        public Criteria andDeliveryFeeNotEqualTo(BigDecimal value) {
            addCriterion("delivery_fee <>", value, "deliveryFee");
            return (Criteria) this;
        }

        public Criteria andDeliveryFeeGreaterThan(BigDecimal value) {
            addCriterion("delivery_fee >", value, "deliveryFee");
            return (Criteria) this;
        }

        public Criteria andDeliveryFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("delivery_fee >=", value, "deliveryFee");
            return (Criteria) this;
        }

        public Criteria andDeliveryFeeLessThan(BigDecimal value) {
            addCriterion("delivery_fee <", value, "deliveryFee");
            return (Criteria) this;
        }

        public Criteria andDeliveryFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("delivery_fee <=", value, "deliveryFee");
            return (Criteria) this;
        }

        public Criteria andDeliveryFeeIn(List<BigDecimal> values) {
            addCriterion("delivery_fee in", values, "deliveryFee");
            return (Criteria) this;
        }

        public Criteria andDeliveryFeeNotIn(List<BigDecimal> values) {
            addCriterion("delivery_fee not in", values, "deliveryFee");
            return (Criteria) this;
        }

        public Criteria andDeliveryFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("delivery_fee between", value1, value2, "deliveryFee");
            return (Criteria) this;
        }

        public Criteria andDeliveryFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("delivery_fee not between", value1, value2, "deliveryFee");
            return (Criteria) this;
        }

        public Criteria andDistanceIsNull() {
            addCriterion("distance is null");
            return (Criteria) this;
        }

        public Criteria andDistanceIsNotNull() {
            addCriterion("distance is not null");
            return (Criteria) this;
        }

        public Criteria andDistanceEqualTo(Long value) {
            addCriterion("distance =", value, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceNotEqualTo(Long value) {
            addCriterion("distance <>", value, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceGreaterThan(Long value) {
            addCriterion("distance >", value, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceGreaterThanOrEqualTo(Long value) {
            addCriterion("distance >=", value, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceLessThan(Long value) {
            addCriterion("distance <", value, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceLessThanOrEqualTo(Long value) {
            addCriterion("distance <=", value, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceIn(List<Long> values) {
            addCriterion("distance in", values, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceNotIn(List<Long> values) {
            addCriterion("distance not in", values, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceBetween(Long value1, Long value2) {
            addCriterion("distance between", value1, value2, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceNotBetween(Long value1, Long value2) {
            addCriterion("distance not between", value1, value2, "distance");
            return (Criteria) this;
        }

        public Criteria andCancelMarkIsNull() {
            addCriterion("cancel_mark is null");
            return (Criteria) this;
        }

        public Criteria andCancelMarkIsNotNull() {
            addCriterion("cancel_mark is not null");
            return (Criteria) this;
        }

        public Criteria andCancelMarkEqualTo(Integer value) {
            addCriterion("cancel_mark =", value, "cancelMark");
            return (Criteria) this;
        }

        public Criteria andCancelMarkNotEqualTo(Integer value) {
            addCriterion("cancel_mark <>", value, "cancelMark");
            return (Criteria) this;
        }

        public Criteria andCancelMarkGreaterThan(Integer value) {
            addCriterion("cancel_mark >", value, "cancelMark");
            return (Criteria) this;
        }

        public Criteria andCancelMarkGreaterThanOrEqualTo(Integer value) {
            addCriterion("cancel_mark >=", value, "cancelMark");
            return (Criteria) this;
        }

        public Criteria andCancelMarkLessThan(Integer value) {
            addCriterion("cancel_mark <", value, "cancelMark");
            return (Criteria) this;
        }

        public Criteria andCancelMarkLessThanOrEqualTo(Integer value) {
            addCriterion("cancel_mark <=", value, "cancelMark");
            return (Criteria) this;
        }

        public Criteria andCancelMarkIn(List<Integer> values) {
            addCriterion("cancel_mark in", values, "cancelMark");
            return (Criteria) this;
        }

        public Criteria andCancelMarkNotIn(List<Integer> values) {
            addCriterion("cancel_mark not in", values, "cancelMark");
            return (Criteria) this;
        }

        public Criteria andCancelMarkBetween(Integer value1, Integer value2) {
            addCriterion("cancel_mark between", value1, value2, "cancelMark");
            return (Criteria) this;
        }

        public Criteria andCancelMarkNotBetween(Integer value1, Integer value2) {
            addCriterion("cancel_mark not between", value1, value2, "cancelMark");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionCodeIsNull() {
            addCriterion("delivery_exception_code is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionCodeIsNotNull() {
            addCriterion("delivery_exception_code is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionCodeEqualTo(Integer value) {
            addCriterion("delivery_exception_code =", value, "deliveryExceptionCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionCodeNotEqualTo(Integer value) {
            addCriterion("delivery_exception_code <>", value, "deliveryExceptionCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionCodeGreaterThan(Integer value) {
            addCriterion("delivery_exception_code >", value, "deliveryExceptionCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_exception_code >=", value, "deliveryExceptionCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionCodeLessThan(Integer value) {
            addCriterion("delivery_exception_code <", value, "deliveryExceptionCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionCodeLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_exception_code <=", value, "deliveryExceptionCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionCodeIn(List<Integer> values) {
            addCriterion("delivery_exception_code in", values, "deliveryExceptionCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionCodeNotIn(List<Integer> values) {
            addCriterion("delivery_exception_code not in", values, "deliveryExceptionCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionCodeBetween(Integer value1, Integer value2) {
            addCriterion("delivery_exception_code between", value1, value2, "deliveryExceptionCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryExceptionCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_exception_code not between", value1, value2, "deliveryExceptionCode");
            return (Criteria) this;
        }

        public Criteria andTipAmountIsNull() {
            addCriterion("tip_amount is null");
            return (Criteria) this;
        }

        public Criteria andTipAmountIsNotNull() {
            addCriterion("tip_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTipAmountEqualTo(BigDecimal value) {
            addCriterion("tip_amount =", value, "tipAmount");
            return (Criteria) this;
        }

        public Criteria andTipAmountNotEqualTo(BigDecimal value) {
            addCriterion("tip_amount <>", value, "tipAmount");
            return (Criteria) this;
        }

        public Criteria andTipAmountGreaterThan(BigDecimal value) {
            addCriterion("tip_amount >", value, "tipAmount");
            return (Criteria) this;
        }

        public Criteria andTipAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("tip_amount >=", value, "tipAmount");
            return (Criteria) this;
        }

        public Criteria andTipAmountLessThan(BigDecimal value) {
            addCriterion("tip_amount <", value, "tipAmount");
            return (Criteria) this;
        }

        public Criteria andTipAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("tip_amount <=", value, "tipAmount");
            return (Criteria) this;
        }

        public Criteria andTipAmountIn(List<BigDecimal> values) {
            addCriterion("tip_amount in", values, "tipAmount");
            return (Criteria) this;
        }

        public Criteria andTipAmountNotIn(List<BigDecimal> values) {
            addCriterion("tip_amount not in", values, "tipAmount");
            return (Criteria) this;
        }

        public Criteria andTipAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tip_amount between", value1, value2, "tipAmount");
            return (Criteria) this;
        }

        public Criteria andTipAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tip_amount not between", value1, value2, "tipAmount");
            return (Criteria) this;
        }

        public Criteria andDeliveryCountIsNull() {
            addCriterion("delivery_count is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryCountIsNotNull() {
            addCriterion("delivery_count is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryCountEqualTo(Integer value) {
            addCriterion("delivery_count =", value, "deliveryCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryCountNotEqualTo(Integer value) {
            addCriterion("delivery_count <>", value, "deliveryCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryCountGreaterThan(Integer value) {
            addCriterion("delivery_count >", value, "deliveryCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_count >=", value, "deliveryCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryCountLessThan(Integer value) {
            addCriterion("delivery_count <", value, "deliveryCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryCountLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_count <=", value, "deliveryCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryCountIn(List<Integer> values) {
            addCriterion("delivery_count in", values, "deliveryCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryCountNotIn(List<Integer> values) {
            addCriterion("delivery_count not in", values, "deliveryCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryCountBetween(Integer value1, Integer value2) {
            addCriterion("delivery_count between", value1, value2, "deliveryCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryCountNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_count not between", value1, value2, "deliveryCount");
            return (Criteria) this;
        }

        public Criteria andPlatformFeeIsNull() {
            addCriterion("platform_fee is null");
            return (Criteria) this;
        }

        public Criteria andPlatformFeeIsNotNull() {
            addCriterion("platform_fee is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformFeeEqualTo(BigDecimal value) {
            addCriterion("platform_fee =", value, "platformFee");
            return (Criteria) this;
        }

        public Criteria andPlatformFeeNotEqualTo(BigDecimal value) {
            addCriterion("platform_fee <>", value, "platformFee");
            return (Criteria) this;
        }

        public Criteria andPlatformFeeGreaterThan(BigDecimal value) {
            addCriterion("platform_fee >", value, "platformFee");
            return (Criteria) this;
        }

        public Criteria andPlatformFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("platform_fee >=", value, "platformFee");
            return (Criteria) this;
        }

        public Criteria andPlatformFeeLessThan(BigDecimal value) {
            addCriterion("platform_fee <", value, "platformFee");
            return (Criteria) this;
        }

        public Criteria andPlatformFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("platform_fee <=", value, "platformFee");
            return (Criteria) this;
        }

        public Criteria andPlatformFeeIn(List<BigDecimal> values) {
            addCriterion("platform_fee in", values, "platformFee");
            return (Criteria) this;
        }

        public Criteria andPlatformFeeNotIn(List<BigDecimal> values) {
            addCriterion("platform_fee not in", values, "platformFee");
            return (Criteria) this;
        }

        public Criteria andPlatformFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("platform_fee between", value1, value2, "platformFee");
            return (Criteria) this;
        }

        public Criteria andPlatformFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("platform_fee not between", value1, value2, "platformFee");
            return (Criteria) this;
        }

        public Criteria andCurrentStatusLockIsNull() {
            addCriterion("current_status_lock is null");
            return (Criteria) this;
        }

        public Criteria andCurrentStatusLockIsNotNull() {
            addCriterion("current_status_lock is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentStatusLockEqualTo(Integer value) {
            addCriterion("current_status_lock =", value, "currentStatusLock");
            return (Criteria) this;
        }

        public Criteria andCurrentStatusLockNotEqualTo(Integer value) {
            addCriterion("current_status_lock <>", value, "currentStatusLock");
            return (Criteria) this;
        }

        public Criteria andCurrentStatusLockGreaterThan(Integer value) {
            addCriterion("current_status_lock >", value, "currentStatusLock");
            return (Criteria) this;
        }

        public Criteria andCurrentStatusLockGreaterThanOrEqualTo(Integer value) {
            addCriterion("current_status_lock >=", value, "currentStatusLock");
            return (Criteria) this;
        }

        public Criteria andCurrentStatusLockLessThan(Integer value) {
            addCriterion("current_status_lock <", value, "currentStatusLock");
            return (Criteria) this;
        }

        public Criteria andCurrentStatusLockLessThanOrEqualTo(Integer value) {
            addCriterion("current_status_lock <=", value, "currentStatusLock");
            return (Criteria) this;
        }

        public Criteria andCurrentStatusLockIn(List<Integer> values) {
            addCriterion("current_status_lock in", values, "currentStatusLock");
            return (Criteria) this;
        }

        public Criteria andCurrentStatusLockNotIn(List<Integer> values) {
            addCriterion("current_status_lock not in", values, "currentStatusLock");
            return (Criteria) this;
        }

        public Criteria andCurrentStatusLockBetween(Integer value1, Integer value2) {
            addCriterion("current_status_lock between", value1, value2, "currentStatusLock");
            return (Criteria) this;
        }

        public Criteria andCurrentStatusLockNotBetween(Integer value1, Integer value2) {
            addCriterion("current_status_lock not between", value1, value2, "currentStatusLock");
            return (Criteria) this;
        }

        public Criteria andTotalLockCountIsNull() {
            addCriterion("total_lock_count is null");
            return (Criteria) this;
        }

        public Criteria andTotalLockCountIsNotNull() {
            addCriterion("total_lock_count is not null");
            return (Criteria) this;
        }

        public Criteria andTotalLockCountEqualTo(Integer value) {
            addCriterion("total_lock_count =", value, "totalLockCount");
            return (Criteria) this;
        }

        public Criteria andTotalLockCountNotEqualTo(Integer value) {
            addCriterion("total_lock_count <>", value, "totalLockCount");
            return (Criteria) this;
        }

        public Criteria andTotalLockCountGreaterThan(Integer value) {
            addCriterion("total_lock_count >", value, "totalLockCount");
            return (Criteria) this;
        }

        public Criteria andTotalLockCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_lock_count >=", value, "totalLockCount");
            return (Criteria) this;
        }

        public Criteria andTotalLockCountLessThan(Integer value) {
            addCriterion("total_lock_count <", value, "totalLockCount");
            return (Criteria) this;
        }

        public Criteria andTotalLockCountLessThanOrEqualTo(Integer value) {
            addCriterion("total_lock_count <=", value, "totalLockCount");
            return (Criteria) this;
        }

        public Criteria andTotalLockCountIn(List<Integer> values) {
            addCriterion("total_lock_count in", values, "totalLockCount");
            return (Criteria) this;
        }

        public Criteria andTotalLockCountNotIn(List<Integer> values) {
            addCriterion("total_lock_count not in", values, "totalLockCount");
            return (Criteria) this;
        }

        public Criteria andTotalLockCountBetween(Integer value1, Integer value2) {
            addCriterion("total_lock_count between", value1, value2, "totalLockCount");
            return (Criteria) this;
        }

        public Criteria andTotalLockCountNotBetween(Integer value1, Integer value2) {
            addCriterion("total_lock_count not between", value1, value2, "totalLockCount");
            return (Criteria) this;
        }

        public Criteria andCurrentLockTimeIsNull() {
            addCriterion("current_lock_time is null");
            return (Criteria) this;
        }

        public Criteria andCurrentLockTimeIsNotNull() {
            addCriterion("current_lock_time is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentLockTimeEqualTo(LocalDateTime value) {
            addCriterion("current_lock_time =", value, "currentLockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentLockTimeNotEqualTo(LocalDateTime value) {
            addCriterion("current_lock_time <>", value, "currentLockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentLockTimeGreaterThan(LocalDateTime value) {
            addCriterion("current_lock_time >", value, "currentLockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentLockTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("current_lock_time >=", value, "currentLockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentLockTimeLessThan(LocalDateTime value) {
            addCriterion("current_lock_time <", value, "currentLockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentLockTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("current_lock_time <=", value, "currentLockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentLockTimeIn(List<LocalDateTime> values) {
            addCriterion("current_lock_time in", values, "currentLockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentLockTimeNotIn(List<LocalDateTime> values) {
            addCriterion("current_lock_time not in", values, "currentLockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentLockTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("current_lock_time between", value1, value2, "currentLockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentLockTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("current_lock_time not between", value1, value2, "currentLockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentUnlockTimeIsNull() {
            addCriterion("current_unlock_time is null");
            return (Criteria) this;
        }

        public Criteria andCurrentUnlockTimeIsNotNull() {
            addCriterion("current_unlock_time is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentUnlockTimeEqualTo(LocalDateTime value) {
            addCriterion("current_unlock_time =", value, "currentUnlockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentUnlockTimeNotEqualTo(LocalDateTime value) {
            addCriterion("current_unlock_time <>", value, "currentUnlockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentUnlockTimeGreaterThan(LocalDateTime value) {
            addCriterion("current_unlock_time >", value, "currentUnlockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentUnlockTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("current_unlock_time >=", value, "currentUnlockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentUnlockTimeLessThan(LocalDateTime value) {
            addCriterion("current_unlock_time <", value, "currentUnlockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentUnlockTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("current_unlock_time <=", value, "currentUnlockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentUnlockTimeIn(List<LocalDateTime> values) {
            addCriterion("current_unlock_time in", values, "currentUnlockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentUnlockTimeNotIn(List<LocalDateTime> values) {
            addCriterion("current_unlock_time not in", values, "currentUnlockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentUnlockTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("current_unlock_time between", value1, value2, "currentUnlockTime");
            return (Criteria) this;
        }

        public Criteria andCurrentUnlockTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("current_unlock_time not between", value1, value2, "currentUnlockTime");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}