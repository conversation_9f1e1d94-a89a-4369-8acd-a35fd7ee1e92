package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryConfigDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeliveryConfigDOMapper {
    long countByExample(DeliveryConfigDOExample example);

    int deleteByExample(DeliveryConfigDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DeliveryConfigDO record);

    int insertSelective(DeliveryConfigDO record);

    List<DeliveryConfigDO> selectByExample(DeliveryConfigDOExample example);

    DeliveryConfigDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DeliveryConfigDO record, @Param("example") DeliveryConfigDOExample example);

    int updateByExample(@Param("record") DeliveryConfigDO record, @Param("example") DeliveryConfigDOExample example);

    int updateByPrimaryKeySelective(DeliveryConfigDO record);

    int updateByPrimaryKey(DeliveryConfigDO record);
}