package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class ShopDeliveryConfigDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public ShopDeliveryConfigDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(Long value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(Long value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(Long value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(Long value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(Long value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<Long> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<Long> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(Long value1, Long value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(Long value1, Long value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andShopIdIsNull() {
            addCriterion("shop_id is null");
            return (Criteria) this;
        }

        public Criteria andShopIdIsNotNull() {
            addCriterion("shop_id is not null");
            return (Criteria) this;
        }

        public Criteria andShopIdEqualTo(Long value) {
            addCriterion("shop_id =", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdNotEqualTo(Long value) {
            addCriterion("shop_id <>", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdGreaterThan(Long value) {
            addCriterion("shop_id >", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdGreaterThanOrEqualTo(Long value) {
            addCriterion("shop_id >=", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdLessThan(Long value) {
            addCriterion("shop_id <", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdLessThanOrEqualTo(Long value) {
            addCriterion("shop_id <=", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdIn(List<Long> values) {
            addCriterion("shop_id in", values, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdNotIn(List<Long> values) {
            addCriterion("shop_id not in", values, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdBetween(Long value1, Long value2) {
            addCriterion("shop_id between", value1, value2, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdNotBetween(Long value1, Long value2) {
            addCriterion("shop_id not between", value1, value2, "shopId");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIdIsNull() {
            addCriterion("delivery_channel_id is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIdIsNotNull() {
            addCriterion("delivery_channel_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIdEqualTo(Integer value) {
            addCriterion("delivery_channel_id =", value, "deliveryChannelId");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIdNotEqualTo(Integer value) {
            addCriterion("delivery_channel_id <>", value, "deliveryChannelId");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIdGreaterThan(Integer value) {
            addCriterion("delivery_channel_id >", value, "deliveryChannelId");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_channel_id >=", value, "deliveryChannelId");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIdLessThan(Integer value) {
            addCriterion("delivery_channel_id <", value, "deliveryChannelId");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIdLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_channel_id <=", value, "deliveryChannelId");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIdIn(List<Integer> values) {
            addCriterion("delivery_channel_id in", values, "deliveryChannelId");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIdNotIn(List<Integer> values) {
            addCriterion("delivery_channel_id not in", values, "deliveryChannelId");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIdBetween(Integer value1, Integer value2) {
            addCriterion("delivery_channel_id between", value1, value2, "deliveryChannelId");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelIdNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_channel_id not between", value1, value2, "deliveryChannelId");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiCodeIsNull() {
            addCriterion("delivery_channel_poi_code is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiCodeIsNotNull() {
            addCriterion("delivery_channel_poi_code is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiCodeEqualTo(String value) {
            addCriterion("delivery_channel_poi_code =", value, "deliveryChannelPoiCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiCodeNotEqualTo(String value) {
            addCriterion("delivery_channel_poi_code <>", value, "deliveryChannelPoiCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiCodeGreaterThan(String value) {
            addCriterion("delivery_channel_poi_code >", value, "deliveryChannelPoiCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiCodeGreaterThanOrEqualTo(String value) {
            addCriterion("delivery_channel_poi_code >=", value, "deliveryChannelPoiCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiCodeLessThan(String value) {
            addCriterion("delivery_channel_poi_code <", value, "deliveryChannelPoiCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiCodeLessThanOrEqualTo(String value) {
            addCriterion("delivery_channel_poi_code <=", value, "deliveryChannelPoiCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiCodeLike(String value) {
            addCriterion("delivery_channel_poi_code like", value, "deliveryChannelPoiCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiCodeNotLike(String value) {
            addCriterion("delivery_channel_poi_code not like", value, "deliveryChannelPoiCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiCodeIn(List<String> values) {
            addCriterion("delivery_channel_poi_code in", values, "deliveryChannelPoiCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiCodeNotIn(List<String> values) {
            addCriterion("delivery_channel_poi_code not in", values, "deliveryChannelPoiCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiCodeBetween(String value1, String value2) {
            addCriterion("delivery_channel_poi_code between", value1, value2, "deliveryChannelPoiCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiCodeNotBetween(String value1, String value2) {
            addCriterion("delivery_channel_poi_code not between", value1, value2, "deliveryChannelPoiCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiNameIsNull() {
            addCriterion("delivery_channel_poi_name is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiNameIsNotNull() {
            addCriterion("delivery_channel_poi_name is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiNameEqualTo(String value) {
            addCriterion("delivery_channel_poi_name =", value, "deliveryChannelPoiName");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiNameNotEqualTo(String value) {
            addCriterion("delivery_channel_poi_name <>", value, "deliveryChannelPoiName");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiNameGreaterThan(String value) {
            addCriterion("delivery_channel_poi_name >", value, "deliveryChannelPoiName");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiNameGreaterThanOrEqualTo(String value) {
            addCriterion("delivery_channel_poi_name >=", value, "deliveryChannelPoiName");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiNameLessThan(String value) {
            addCriterion("delivery_channel_poi_name <", value, "deliveryChannelPoiName");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiNameLessThanOrEqualTo(String value) {
            addCriterion("delivery_channel_poi_name <=", value, "deliveryChannelPoiName");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiNameLike(String value) {
            addCriterion("delivery_channel_poi_name like", value, "deliveryChannelPoiName");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiNameNotLike(String value) {
            addCriterion("delivery_channel_poi_name not like", value, "deliveryChannelPoiName");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiNameIn(List<String> values) {
            addCriterion("delivery_channel_poi_name in", values, "deliveryChannelPoiName");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiNameNotIn(List<String> values) {
            addCriterion("delivery_channel_poi_name not in", values, "deliveryChannelPoiName");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiNameBetween(String value1, String value2) {
            addCriterion("delivery_channel_poi_name between", value1, value2, "deliveryChannelPoiName");
            return (Criteria) this;
        }

        public Criteria andDeliveryChannelPoiNameNotBetween(String value1, String value2) {
            addCriterion("delivery_channel_poi_name not between", value1, value2, "deliveryChannelPoiName");
            return (Criteria) this;
        }

        public Criteria andExtDataIsNull() {
            addCriterion("ext_data is null");
            return (Criteria) this;
        }

        public Criteria andExtDataIsNotNull() {
            addCriterion("ext_data is not null");
            return (Criteria) this;
        }

        public Criteria andExtDataEqualTo(String value) {
            addCriterion("ext_data =", value, "extData");
            return (Criteria) this;
        }

        public Criteria andExtDataNotEqualTo(String value) {
            addCriterion("ext_data <>", value, "extData");
            return (Criteria) this;
        }

        public Criteria andExtDataGreaterThan(String value) {
            addCriterion("ext_data >", value, "extData");
            return (Criteria) this;
        }

        public Criteria andExtDataGreaterThanOrEqualTo(String value) {
            addCriterion("ext_data >=", value, "extData");
            return (Criteria) this;
        }

        public Criteria andExtDataLessThan(String value) {
            addCriterion("ext_data <", value, "extData");
            return (Criteria) this;
        }

        public Criteria andExtDataLessThanOrEqualTo(String value) {
            addCriterion("ext_data <=", value, "extData");
            return (Criteria) this;
        }

        public Criteria andExtDataLike(String value) {
            addCriterion("ext_data like", value, "extData");
            return (Criteria) this;
        }

        public Criteria andExtDataNotLike(String value) {
            addCriterion("ext_data not like", value, "extData");
            return (Criteria) this;
        }

        public Criteria andExtDataIn(List<String> values) {
            addCriterion("ext_data in", values, "extData");
            return (Criteria) this;
        }

        public Criteria andExtDataNotIn(List<String> values) {
            addCriterion("ext_data not in", values, "extData");
            return (Criteria) this;
        }

        public Criteria andExtDataBetween(String value1, String value2) {
            addCriterion("ext_data between", value1, value2, "extData");
            return (Criteria) this;
        }

        public Criteria andExtDataNotBetween(String value1, String value2) {
            addCriterion("ext_data not between", value1, value2, "extData");
            return (Criteria) this;
        }

        public Criteria andEnabledIsNull() {
            addCriterion("enabled is null");
            return (Criteria) this;
        }

        public Criteria andEnabledIsNotNull() {
            addCriterion("enabled is not null");
            return (Criteria) this;
        }

        public Criteria andEnabledEqualTo(Integer value) {
            addCriterion("enabled =", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotEqualTo(Integer value) {
            addCriterion("enabled <>", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledGreaterThan(Integer value) {
            addCriterion("enabled >", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledGreaterThanOrEqualTo(Integer value) {
            addCriterion("enabled >=", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledLessThan(Integer value) {
            addCriterion("enabled <", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledLessThanOrEqualTo(Integer value) {
            addCriterion("enabled <=", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledIn(List<Integer> values) {
            addCriterion("enabled in", values, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotIn(List<Integer> values) {
            addCriterion("enabled not in", values, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledBetween(Integer value1, Integer value2) {
            addCriterion("enabled between", value1, value2, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotBetween(Integer value1, Integer value2) {
            addCriterion("enabled not between", value1, value2, "enabled");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIsNull() {
            addCriterion("operator_id is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIsNotNull() {
            addCriterion("operator_id is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorIdEqualTo(Long value) {
            addCriterion("operator_id =", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotEqualTo(Long value) {
            addCriterion("operator_id <>", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdGreaterThan(Long value) {
            addCriterion("operator_id >", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdGreaterThanOrEqualTo(Long value) {
            addCriterion("operator_id >=", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLessThan(Long value) {
            addCriterion("operator_id <", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLessThanOrEqualTo(Long value) {
            addCriterion("operator_id <=", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIn(List<Long> values) {
            addCriterion("operator_id in", values, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotIn(List<Long> values) {
            addCriterion("operator_id not in", values, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdBetween(Long value1, Long value2) {
            addCriterion("operator_id between", value1, value2, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotBetween(Long value1, Long value2) {
            addCriterion("operator_id not between", value1, value2, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorNameIsNull() {
            addCriterion("operator_name is null");
            return (Criteria) this;
        }

        public Criteria andOperatorNameIsNotNull() {
            addCriterion("operator_name is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorNameEqualTo(String value) {
            addCriterion("operator_name =", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameNotEqualTo(String value) {
            addCriterion("operator_name <>", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameGreaterThan(String value) {
            addCriterion("operator_name >", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameGreaterThanOrEqualTo(String value) {
            addCriterion("operator_name >=", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameLessThan(String value) {
            addCriterion("operator_name <", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameLessThanOrEqualTo(String value) {
            addCriterion("operator_name <=", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameLike(String value) {
            addCriterion("operator_name like", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameNotLike(String value) {
            addCriterion("operator_name not like", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameIn(List<String> values) {
            addCriterion("operator_name in", values, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameNotIn(List<String> values) {
            addCriterion("operator_name not in", values, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameBetween(String value1, String value2) {
            addCriterion("operator_name between", value1, value2, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameNotBetween(String value1, String value2) {
            addCriterion("operator_name not between", value1, value2, "operatorName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIsDelIsNull() {
            addCriterion("is_del is null");
            return (Criteria) this;
        }

        public Criteria andIsDelIsNotNull() {
            addCriterion("is_del is not null");
            return (Criteria) this;
        }

        public Criteria andIsDelEqualTo(Integer value) {
            addCriterion("is_del =", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelNotEqualTo(Integer value) {
            addCriterion("is_del <>", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelGreaterThan(Integer value) {
            addCriterion("is_del >", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_del >=", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelLessThan(Integer value) {
            addCriterion("is_del <", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelLessThanOrEqualTo(Integer value) {
            addCriterion("is_del <=", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelIn(List<Integer> values) {
            addCriterion("is_del in", values, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelNotIn(List<Integer> values) {
            addCriterion("is_del not in", values, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelBetween(Integer value1, Integer value2) {
            addCriterion("is_del between", value1, value2, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelNotBetween(Integer value1, Integer value2) {
            addCriterion("is_del not between", value1, value2, "isDel");
            return (Criteria) this;
        }

        public Criteria andDeliveryServiceCodesIsNull() {
            addCriterion("delivery_service_codes is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryServiceCodesIsNotNull() {
            addCriterion("delivery_service_codes is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryServiceCodesEqualTo(String value) {
            addCriterion("delivery_service_codes =", value, "deliveryServiceCodes");
            return (Criteria) this;
        }

        public Criteria andDeliveryServiceCodesNotEqualTo(String value) {
            addCriterion("delivery_service_codes <>", value, "deliveryServiceCodes");
            return (Criteria) this;
        }

        public Criteria andDeliveryServiceCodesGreaterThan(String value) {
            addCriterion("delivery_service_codes >", value, "deliveryServiceCodes");
            return (Criteria) this;
        }

        public Criteria andDeliveryServiceCodesGreaterThanOrEqualTo(String value) {
            addCriterion("delivery_service_codes >=", value, "deliveryServiceCodes");
            return (Criteria) this;
        }

        public Criteria andDeliveryServiceCodesLessThan(String value) {
            addCriterion("delivery_service_codes <", value, "deliveryServiceCodes");
            return (Criteria) this;
        }

        public Criteria andDeliveryServiceCodesLessThanOrEqualTo(String value) {
            addCriterion("delivery_service_codes <=", value, "deliveryServiceCodes");
            return (Criteria) this;
        }

        public Criteria andDeliveryServiceCodesLike(String value) {
            addCriterion("delivery_service_codes like", value, "deliveryServiceCodes");
            return (Criteria) this;
        }

        public Criteria andDeliveryServiceCodesNotLike(String value) {
            addCriterion("delivery_service_codes not like", value, "deliveryServiceCodes");
            return (Criteria) this;
        }

        public Criteria andDeliveryServiceCodesIn(List<String> values) {
            addCriterion("delivery_service_codes in", values, "deliveryServiceCodes");
            return (Criteria) this;
        }

        public Criteria andDeliveryServiceCodesNotIn(List<String> values) {
            addCriterion("delivery_service_codes not in", values, "deliveryServiceCodes");
            return (Criteria) this;
        }

        public Criteria andDeliveryServiceCodesBetween(String value1, String value2) {
            addCriterion("delivery_service_codes between", value1, value2, "deliveryServiceCodes");
            return (Criteria) this;
        }

        public Criteria andDeliveryServiceCodesNotBetween(String value1, String value2) {
            addCriterion("delivery_service_codes not between", value1, value2, "deliveryServiceCodes");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}