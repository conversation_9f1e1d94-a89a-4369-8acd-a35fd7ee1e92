package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.exception;

/**
 * @see org.elasticsearch.index.engine.VersionConflictEngineException
 * <AUTHOR>
 */
public class EsVersionConflictException extends EsBaseException {

    public EsVersionConflictException(String index, String errMsg) {
        super(index, errMsg);
    }

    public EsVersionConflictException(String index, Throwable e) {
        super(index, e);
    }

    @Override
    public String toString() {
        return "EsVersionConflictException: [" + index + "]" + getLocalizedMessage();
    }

}
