package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

public class DeliveryRiskControlOrderDO {
    private Long id;

    private Long tenantId;

    private Long poiId;

    private Long riderAccountId;

    private Integer orderBizType;

    private String orderIdView;

    private Long dt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getPoiId() {
        return poiId;
    }

    public void setPoiId(Long poiId) {
        this.poiId = poiId;
    }

    public Long getRiderAccountId() {
        return riderAccountId;
    }

    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    public Integer getOrderBizType() {
        return orderBizType;
    }

    public void setOrderBizType(Integer orderBizType) {
        this.orderBizType = orderBizType;
    }

    public String getOrderIdView() {
        return orderIdView;
    }

    public void setOrderIdView(String orderIdView) {
        this.orderIdView = orderIdView == null ? null : orderIdView.trim();
    }

    public Long getDt() {
        return dt;
    }

    public void setDt(Long dt) {
        this.dt = dt;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DeliveryRiskControlOrderDO other = (DeliveryRiskControlOrderDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()))
            && (this.getPoiId() == null ? other.getPoiId() == null : this.getPoiId().equals(other.getPoiId()))
            && (this.getRiderAccountId() == null ? other.getRiderAccountId() == null : this.getRiderAccountId().equals(other.getRiderAccountId()))
            && (this.getOrderBizType() == null ? other.getOrderBizType() == null : this.getOrderBizType().equals(other.getOrderBizType()))
            && (this.getOrderIdView() == null ? other.getOrderIdView() == null : this.getOrderIdView().equals(other.getOrderIdView()))
            && (this.getDt() == null ? other.getDt() == null : this.getDt().equals(other.getDt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        result = prime * result + ((getPoiId() == null) ? 0 : getPoiId().hashCode());
        result = prime * result + ((getRiderAccountId() == null) ? 0 : getRiderAccountId().hashCode());
        result = prime * result + ((getOrderBizType() == null) ? 0 : getOrderBizType().hashCode());
        result = prime * result + ((getOrderIdView() == null) ? 0 : getOrderIdView().hashCode());
        result = prime * result + ((getDt() == null) ? 0 : getDt().hashCode());
        return result;
    }
}