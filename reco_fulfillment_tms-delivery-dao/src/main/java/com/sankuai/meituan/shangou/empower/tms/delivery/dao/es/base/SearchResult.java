package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.base;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * es搜索结果
 *
 * <AUTHOR>
 * @since 2022/3/31
 */
@Getter
@ToString
@EqualsAndHashCode
public class SearchResult<T extends BaseEsPo> {

    private static final SearchResult<?> EMPTY = new SearchResult<>(0, Collections.emptyList());

    /**
     * 结果总数
     */
    private final int total;

    /**
     * 一批搜索结果
     */
    @NotNull
    private final List<T> data;

    public SearchResult(int total, List<T> data) {
        this.total = total;
        if (total == 0) {
            this.data = Collections.emptyList();
        } else {
            this.data = Objects.requireNonNull(data, "Search result data must not be null while total not 0");
        }
    }

    @SuppressWarnings("unchecked")
    public static <T extends BaseEsPo> SearchResult<T> empty() {
        return (SearchResult<T>) EMPTY;
    }

    public boolean isEmpty() {
        return total == 0 || data.isEmpty();
    }

    public boolean isNotEmpty() {
        return !isEmpty();
    }

}
