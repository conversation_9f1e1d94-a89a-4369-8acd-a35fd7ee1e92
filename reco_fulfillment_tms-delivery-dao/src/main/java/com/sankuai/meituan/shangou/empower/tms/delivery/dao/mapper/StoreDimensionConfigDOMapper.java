package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreDimensionConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreDimensionConfigDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StoreDimensionConfigDOMapper {
    long countByExample(StoreDimensionConfigDOExample example);

    int deleteByExample(StoreDimensionConfigDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StoreDimensionConfigDO record);

    int insertSelective(StoreDimensionConfigDO record);

    List<StoreDimensionConfigDO> selectByExample(StoreDimensionConfigDOExample example);

    StoreDimensionConfigDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StoreDimensionConfigDO record, @Param("example") StoreDimensionConfigDOExample example);

    int updateByExample(@Param("record") StoreDimensionConfigDO record, @Param("example") StoreDimensionConfigDOExample example);

    int updateByPrimaryKeySelective(StoreDimensionConfigDO record);

    int updateByPrimaryKey(StoreDimensionConfigDO record);
}