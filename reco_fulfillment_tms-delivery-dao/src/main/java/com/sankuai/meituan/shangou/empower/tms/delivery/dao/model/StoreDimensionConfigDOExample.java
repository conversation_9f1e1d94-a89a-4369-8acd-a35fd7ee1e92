package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class StoreDimensionConfigDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StoreDimensionConfigDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(Long value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(Long value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(Long value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(Long value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(Long value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<Long> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<Long> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(Long value1, Long value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(Long value1, Long value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryModeIsNull() {
            addCriterion("self_delivery_mode is null");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryModeIsNotNull() {
            addCriterion("self_delivery_mode is not null");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryModeEqualTo(Integer value) {
            addCriterion("self_delivery_mode =", value, "selfDeliveryMode");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryModeNotEqualTo(Integer value) {
            addCriterion("self_delivery_mode <>", value, "selfDeliveryMode");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryModeGreaterThan(Integer value) {
            addCriterion("self_delivery_mode >", value, "selfDeliveryMode");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("self_delivery_mode >=", value, "selfDeliveryMode");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryModeLessThan(Integer value) {
            addCriterion("self_delivery_mode <", value, "selfDeliveryMode");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryModeLessThanOrEqualTo(Integer value) {
            addCriterion("self_delivery_mode <=", value, "selfDeliveryMode");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryModeIn(List<Integer> values) {
            addCriterion("self_delivery_mode in", values, "selfDeliveryMode");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryModeNotIn(List<Integer> values) {
            addCriterion("self_delivery_mode not in", values, "selfDeliveryMode");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryModeBetween(Integer value1, Integer value2) {
            addCriterion("self_delivery_mode between", value1, value2, "selfDeliveryMode");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryModeNotBetween(Integer value1, Integer value2) {
            addCriterion("self_delivery_mode not between", value1, value2, "selfDeliveryMode");
            return (Criteria) this;
        }

        public Criteria andInternalNavigationModeIsNull() {
            addCriterion("internal_navigation_mode is null");
            return (Criteria) this;
        }

        public Criteria andInternalNavigationModeIsNotNull() {
            addCriterion("internal_navigation_mode is not null");
            return (Criteria) this;
        }

        public Criteria andInternalNavigationModeEqualTo(Integer value) {
            addCriterion("internal_navigation_mode =", value, "internalNavigationMode");
            return (Criteria) this;
        }

        public Criteria andInternalNavigationModeNotEqualTo(Integer value) {
            addCriterion("internal_navigation_mode <>", value, "internalNavigationMode");
            return (Criteria) this;
        }

        public Criteria andInternalNavigationModeGreaterThan(Integer value) {
            addCriterion("internal_navigation_mode >", value, "internalNavigationMode");
            return (Criteria) this;
        }

        public Criteria andInternalNavigationModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("internal_navigation_mode >=", value, "internalNavigationMode");
            return (Criteria) this;
        }

        public Criteria andInternalNavigationModeLessThan(Integer value) {
            addCriterion("internal_navigation_mode <", value, "internalNavigationMode");
            return (Criteria) this;
        }

        public Criteria andInternalNavigationModeLessThanOrEqualTo(Integer value) {
            addCriterion("internal_navigation_mode <=", value, "internalNavigationMode");
            return (Criteria) this;
        }

        public Criteria andInternalNavigationModeIn(List<Integer> values) {
            addCriterion("internal_navigation_mode in", values, "internalNavigationMode");
            return (Criteria) this;
        }

        public Criteria andInternalNavigationModeNotIn(List<Integer> values) {
            addCriterion("internal_navigation_mode not in", values, "internalNavigationMode");
            return (Criteria) this;
        }

        public Criteria andInternalNavigationModeBetween(Integer value1, Integer value2) {
            addCriterion("internal_navigation_mode between", value1, value2, "internalNavigationMode");
            return (Criteria) this;
        }

        public Criteria andInternalNavigationModeNotBetween(Integer value1, Integer value2) {
            addCriterion("internal_navigation_mode not between", value1, value2, "internalNavigationMode");
            return (Criteria) this;
        }

        public Criteria andAssessTimeConfigIsNull() {
            addCriterion("assess_time_config is null");
            return (Criteria) this;
        }

        public Criteria andAssessTimeConfigIsNotNull() {
            addCriterion("assess_time_config is not null");
            return (Criteria) this;
        }

        public Criteria andAssessTimeConfigEqualTo(String value) {
            addCriterion("assess_time_config =", value, "assessTimeConfig");
            return (Criteria) this;
        }

        public Criteria andAssessTimeConfigNotEqualTo(String value) {
            addCriterion("assess_time_config <>", value, "assessTimeConfig");
            return (Criteria) this;
        }

        public Criteria andAssessTimeConfigGreaterThan(String value) {
            addCriterion("assess_time_config >", value, "assessTimeConfig");
            return (Criteria) this;
        }

        public Criteria andAssessTimeConfigGreaterThanOrEqualTo(String value) {
            addCriterion("assess_time_config >=", value, "assessTimeConfig");
            return (Criteria) this;
        }

        public Criteria andAssessTimeConfigLessThan(String value) {
            addCriterion("assess_time_config <", value, "assessTimeConfig");
            return (Criteria) this;
        }

        public Criteria andAssessTimeConfigLessThanOrEqualTo(String value) {
            addCriterion("assess_time_config <=", value, "assessTimeConfig");
            return (Criteria) this;
        }

        public Criteria andAssessTimeConfigLike(String value) {
            addCriterion("assess_time_config like", value, "assessTimeConfig");
            return (Criteria) this;
        }

        public Criteria andAssessTimeConfigNotLike(String value) {
            addCriterion("assess_time_config not like", value, "assessTimeConfig");
            return (Criteria) this;
        }

        public Criteria andAssessTimeConfigIn(List<String> values) {
            addCriterion("assess_time_config in", values, "assessTimeConfig");
            return (Criteria) this;
        }

        public Criteria andAssessTimeConfigNotIn(List<String> values) {
            addCriterion("assess_time_config not in", values, "assessTimeConfig");
            return (Criteria) this;
        }

        public Criteria andAssessTimeConfigBetween(String value1, String value2) {
            addCriterion("assess_time_config between", value1, value2, "assessTimeConfig");
            return (Criteria) this;
        }

        public Criteria andAssessTimeConfigNotBetween(String value1, String value2) {
            addCriterion("assess_time_config not between", value1, value2, "assessTimeConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryCompleteModeIsNull() {
            addCriterion("delivery_complete_mode is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryCompleteModeIsNotNull() {
            addCriterion("delivery_complete_mode is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryCompleteModeEqualTo(String value) {
            addCriterion("delivery_complete_mode =", value, "deliveryCompleteMode");
            return (Criteria) this;
        }

        public Criteria andDeliveryCompleteModeNotEqualTo(String value) {
            addCriterion("delivery_complete_mode <>", value, "deliveryCompleteMode");
            return (Criteria) this;
        }

        public Criteria andDeliveryCompleteModeGreaterThan(String value) {
            addCriterion("delivery_complete_mode >", value, "deliveryCompleteMode");
            return (Criteria) this;
        }

        public Criteria andDeliveryCompleteModeGreaterThanOrEqualTo(String value) {
            addCriterion("delivery_complete_mode >=", value, "deliveryCompleteMode");
            return (Criteria) this;
        }

        public Criteria andDeliveryCompleteModeLessThan(String value) {
            addCriterion("delivery_complete_mode <", value, "deliveryCompleteMode");
            return (Criteria) this;
        }

        public Criteria andDeliveryCompleteModeLessThanOrEqualTo(String value) {
            addCriterion("delivery_complete_mode <=", value, "deliveryCompleteMode");
            return (Criteria) this;
        }

        public Criteria andDeliveryCompleteModeLike(String value) {
            addCriterion("delivery_complete_mode like", value, "deliveryCompleteMode");
            return (Criteria) this;
        }

        public Criteria andDeliveryCompleteModeNotLike(String value) {
            addCriterion("delivery_complete_mode not like", value, "deliveryCompleteMode");
            return (Criteria) this;
        }

        public Criteria andDeliveryCompleteModeIn(List<String> values) {
            addCriterion("delivery_complete_mode in", values, "deliveryCompleteMode");
            return (Criteria) this;
        }

        public Criteria andDeliveryCompleteModeNotIn(List<String> values) {
            addCriterion("delivery_complete_mode not in", values, "deliveryCompleteMode");
            return (Criteria) this;
        }

        public Criteria andDeliveryCompleteModeBetween(String value1, String value2) {
            addCriterion("delivery_complete_mode between", value1, value2, "deliveryCompleteMode");
            return (Criteria) this;
        }

        public Criteria andDeliveryCompleteModeNotBetween(String value1, String value2) {
            addCriterion("delivery_complete_mode not between", value1, value2, "deliveryCompleteMode");
            return (Criteria) this;
        }

        public Criteria andRiderTransRolesIsNull() {
            addCriterion("rider_trans_roles is null");
            return (Criteria) this;
        }

        public Criteria andRiderTransRolesIsNotNull() {
            addCriterion("rider_trans_roles is not null");
            return (Criteria) this;
        }

        public Criteria andRiderTransRolesEqualTo(String value) {
            addCriterion("rider_trans_roles =", value, "riderTransRoles");
            return (Criteria) this;
        }

        public Criteria andRiderTransRolesNotEqualTo(String value) {
            addCriterion("rider_trans_roles <>", value, "riderTransRoles");
            return (Criteria) this;
        }

        public Criteria andRiderTransRolesGreaterThan(String value) {
            addCriterion("rider_trans_roles >", value, "riderTransRoles");
            return (Criteria) this;
        }

        public Criteria andRiderTransRolesGreaterThanOrEqualTo(String value) {
            addCriterion("rider_trans_roles >=", value, "riderTransRoles");
            return (Criteria) this;
        }

        public Criteria andRiderTransRolesLessThan(String value) {
            addCriterion("rider_trans_roles <", value, "riderTransRoles");
            return (Criteria) this;
        }

        public Criteria andRiderTransRolesLessThanOrEqualTo(String value) {
            addCriterion("rider_trans_roles <=", value, "riderTransRoles");
            return (Criteria) this;
        }

        public Criteria andRiderTransRolesLike(String value) {
            addCriterion("rider_trans_roles like", value, "riderTransRoles");
            return (Criteria) this;
        }

        public Criteria andRiderTransRolesNotLike(String value) {
            addCriterion("rider_trans_roles not like", value, "riderTransRoles");
            return (Criteria) this;
        }

        public Criteria andRiderTransRolesIn(List<String> values) {
            addCriterion("rider_trans_roles in", values, "riderTransRoles");
            return (Criteria) this;
        }

        public Criteria andRiderTransRolesNotIn(List<String> values) {
            addCriterion("rider_trans_roles not in", values, "riderTransRoles");
            return (Criteria) this;
        }

        public Criteria andRiderTransRolesBetween(String value1, String value2) {
            addCriterion("rider_trans_roles between", value1, value2, "riderTransRoles");
            return (Criteria) this;
        }

        public Criteria andRiderTransRolesNotBetween(String value1, String value2) {
            addCriterion("rider_trans_roles not between", value1, value2, "riderTransRoles");
            return (Criteria) this;
        }

        public Criteria andCompletedSortModeIsNull() {
            addCriterion("completed_sort_mode is null");
            return (Criteria) this;
        }

        public Criteria andCompletedSortModeIsNotNull() {
            addCriterion("completed_sort_mode is not null");
            return (Criteria) this;
        }

        public Criteria andCompletedSortModeEqualTo(Integer value) {
            addCriterion("completed_sort_mode =", value, "completedSortMode");
            return (Criteria) this;
        }

        public Criteria andCompletedSortModeNotEqualTo(Integer value) {
            addCriterion("completed_sort_mode <>", value, "completedSortMode");
            return (Criteria) this;
        }

        public Criteria andCompletedSortModeGreaterThan(Integer value) {
            addCriterion("completed_sort_mode >", value, "completedSortMode");
            return (Criteria) this;
        }

        public Criteria andCompletedSortModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("completed_sort_mode >=", value, "completedSortMode");
            return (Criteria) this;
        }

        public Criteria andCompletedSortModeLessThan(Integer value) {
            addCriterion("completed_sort_mode <", value, "completedSortMode");
            return (Criteria) this;
        }

        public Criteria andCompletedSortModeLessThanOrEqualTo(Integer value) {
            addCriterion("completed_sort_mode <=", value, "completedSortMode");
            return (Criteria) this;
        }

        public Criteria andCompletedSortModeIn(List<Integer> values) {
            addCriterion("completed_sort_mode in", values, "completedSortMode");
            return (Criteria) this;
        }

        public Criteria andCompletedSortModeNotIn(List<Integer> values) {
            addCriterion("completed_sort_mode not in", values, "completedSortMode");
            return (Criteria) this;
        }

        public Criteria andCompletedSortModeBetween(Integer value1, Integer value2) {
            addCriterion("completed_sort_mode between", value1, value2, "completedSortMode");
            return (Criteria) this;
        }

        public Criteria andCompletedSortModeNotBetween(Integer value1, Integer value2) {
            addCriterion("completed_sort_mode not between", value1, value2, "completedSortMode");
            return (Criteria) this;
        }

        public Criteria andDeliveryRemindConfigIsNull() {
            addCriterion("delivery_remind_config is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryRemindConfigIsNotNull() {
            addCriterion("delivery_remind_config is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryRemindConfigEqualTo(String value) {
            addCriterion("delivery_remind_config =", value, "deliveryRemindConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryRemindConfigNotEqualTo(String value) {
            addCriterion("delivery_remind_config <>", value, "deliveryRemindConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryRemindConfigGreaterThan(String value) {
            addCriterion("delivery_remind_config >", value, "deliveryRemindConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryRemindConfigGreaterThanOrEqualTo(String value) {
            addCriterion("delivery_remind_config >=", value, "deliveryRemindConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryRemindConfigLessThan(String value) {
            addCriterion("delivery_remind_config <", value, "deliveryRemindConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryRemindConfigLessThanOrEqualTo(String value) {
            addCriterion("delivery_remind_config <=", value, "deliveryRemindConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryRemindConfigLike(String value) {
            addCriterion("delivery_remind_config like", value, "deliveryRemindConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryRemindConfigNotLike(String value) {
            addCriterion("delivery_remind_config not like", value, "deliveryRemindConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryRemindConfigIn(List<String> values) {
            addCriterion("delivery_remind_config in", values, "deliveryRemindConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryRemindConfigNotIn(List<String> values) {
            addCriterion("delivery_remind_config not in", values, "deliveryRemindConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryRemindConfigBetween(String value1, String value2) {
            addCriterion("delivery_remind_config between", value1, value2, "deliveryRemindConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryRemindConfigNotBetween(String value1, String value2) {
            addCriterion("delivery_remind_config not between", value1, value2, "deliveryRemindConfig");
            return (Criteria) this;
        }

        public Criteria andEnableIsNull() {
            addCriterion("enable is null");
            return (Criteria) this;
        }

        public Criteria andEnableIsNotNull() {
            addCriterion("enable is not null");
            return (Criteria) this;
        }

        public Criteria andEnableEqualTo(Integer value) {
            addCriterion("enable =", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotEqualTo(Integer value) {
            addCriterion("enable <>", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThan(Integer value) {
            addCriterion("enable >", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThanOrEqualTo(Integer value) {
            addCriterion("enable >=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThan(Integer value) {
            addCriterion("enable <", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThanOrEqualTo(Integer value) {
            addCriterion("enable <=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableIn(List<Integer> values) {
            addCriterion("enable in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotIn(List<Integer> values) {
            addCriterion("enable not in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableBetween(Integer value1, Integer value2) {
            addCriterion("enable between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotBetween(Integer value1, Integer value2) {
            addCriterion("enable not between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(LocalDateTime value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(LocalDateTime value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(LocalDateTime value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(LocalDateTime value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<LocalDateTime> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<LocalDateTime> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(LocalDateTime value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(LocalDateTime value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(LocalDateTime value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(LocalDateTime value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<LocalDateTime> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<LocalDateTime> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}