package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.PricingRouteInfoDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.PricingRouteInfoDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PricingRouteInfoDOMapper {
    long countByExample(PricingRouteInfoDOExample example);

    int deleteByExample(PricingRouteInfoDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PricingRouteInfoDO record);

    int insertSelective(PricingRouteInfoDO record);

    List<PricingRouteInfoDO> selectByExampleWithBLOBs(PricingRouteInfoDOExample example);

    List<PricingRouteInfoDO> selectByExample(PricingRouteInfoDOExample example);

    PricingRouteInfoDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") PricingRouteInfoDO record, @Param("example") PricingRouteInfoDOExample example);

    int updateByExampleWithBLOBs(@Param("record") PricingRouteInfoDO record, @Param("example") PricingRouteInfoDOExample example);

    int updateByExample(@Param("record") PricingRouteInfoDO record, @Param("example") PricingRouteInfoDOExample example);

    int updateByPrimaryKeySelective(PricingRouteInfoDO record);

    int updateByPrimaryKeyWithBLOBs(PricingRouteInfoDO record);

    int updateByPrimaryKey(PricingRouteInfoDO record);
}