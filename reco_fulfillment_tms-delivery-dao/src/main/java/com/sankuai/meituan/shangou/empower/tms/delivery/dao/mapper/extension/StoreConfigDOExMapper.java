package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreConfigDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mybatis.SlaveQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StoreConfigDOExMapper {

	int batchInsert(@Param("recordList") List<StoreConfigDO> recordList);

	int batchUpdate(@Param("recordList") List<StoreConfigDO> recordList);

	@SlaveQuery
	List<StoreConfigDO> selectByExampleSlave(StoreConfigDOExample example);

	int insertSelective(StoreConfigDO record);
}
