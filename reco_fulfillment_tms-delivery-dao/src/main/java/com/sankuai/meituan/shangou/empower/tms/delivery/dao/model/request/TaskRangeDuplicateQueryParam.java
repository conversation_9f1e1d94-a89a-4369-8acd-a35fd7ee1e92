package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.request;

import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 重复任务查询参数
 *
 * <AUTHOR> on 2021-11-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskRangeDuplicateQueryParam {
    private long tenantId;
    private int taskType;
    private String executeParam;
    private List<Integer> taskStatusList;
    private long operatorId;
    private Date createBeginTime;
    private Date createEndTime;
    @Builder.Default
    private String env = ProcessInfoUtil.getEnvStr();

}
