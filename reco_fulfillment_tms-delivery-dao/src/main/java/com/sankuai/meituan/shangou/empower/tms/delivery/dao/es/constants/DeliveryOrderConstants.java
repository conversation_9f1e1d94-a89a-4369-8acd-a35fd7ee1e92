package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.constants;

import org.apache.commons.lang3.tuple.Pair;

/**
 * <AUTHOR>
 */
public interface DeliveryOrderConstants {

    String INDEX_ALIAS = "delivery_order";

    Integer NO_EXCEPTION_TYPE = 0;
    
    Integer ORDER_STATUS_CANCEL = 25;

    String MONTH_PATTERN_INDEX = "'delivery_order.'yyyyMM";

    String FIELD_ORDER_ID = "order_id";

    String FIELD_STORE_ID = "store_id";

    String FIELD_TENANT_ID = "tenant_id";

    String FIELD_EXCEPTION_TYPE = "delivery_exception_type";

    String FIELD_ESTIMATED_DELIVERY_TIME = "estimated_delivery_time";
    
    String FIELD_ORDER_STATUS = "order_status";

    String FIELD_DELIVERY_EXCEPTION_TYPE = "delivery_exception_type";

    String FIELD_DELIVERY_EXCEPTION_CODE = "delivery_exception_code";

    String FIELD_DELIVERY_STATUS = "delivery_status";

    String FIELD_EXT_DEAL_DEADLINE = "dealDeadline";


    String FIELD_CHANNEL_ORDER_ID = "channel_order_id";

    String FIELD_ORIGIN_WAYBILL_NO = "origin_waybill_no";

    String FIELD_DELIVERY_TRACE_LIST = "delivery_trace_list";

    String FIELD_DELIVERY_ORDER_ID = "delivery_order_id";

    String ORIGIN_WAYBILL_NO = "originWaybillNo";


    Pair<String, String> DELIVERY_EXCEPTION_TYPE_AGG = Pair.of("delivery_exception_type_agg", "delivery_exception_type");

    Pair<String, String> DELIVERY_EXCEPTION_CODE_AGG = Pair.of("delivery_exception_code_agg", "delivery_exception_code");

    Pair<String, String> DELIVERY_STATUS_AGG = Pair.of("delivery_status_agg", "delivery_status");


    /**
     * 隐私商品显示名称
     */
    String PRIVACY_GOODS_SHOW_NAME = "为保护隐私，商品已隐藏";

    /**
     * 隐私商品显示名称为***
     */
    String PRIVACY_GOODS_SHOW_STAR_NAME = "***";

}
