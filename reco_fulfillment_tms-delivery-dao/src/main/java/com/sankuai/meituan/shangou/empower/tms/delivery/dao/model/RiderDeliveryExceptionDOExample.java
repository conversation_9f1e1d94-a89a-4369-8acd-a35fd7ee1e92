package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class RiderDeliveryExceptionDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public RiderDeliveryExceptionDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(Long value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(Long value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(Long value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(Long value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(Long value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<Long> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<Long> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(Long value1, Long value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(Long value1, Long value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdIsNull() {
            addCriterion("delivery_order_id is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdIsNotNull() {
            addCriterion("delivery_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdEqualTo(Long value) {
            addCriterion("delivery_order_id =", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdNotEqualTo(Long value) {
            addCriterion("delivery_order_id <>", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdGreaterThan(Long value) {
            addCriterion("delivery_order_id >", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("delivery_order_id >=", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdLessThan(Long value) {
            addCriterion("delivery_order_id <", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdLessThanOrEqualTo(Long value) {
            addCriterion("delivery_order_id <=", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdIn(List<Long> values) {
            addCriterion("delivery_order_id in", values, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdNotIn(List<Long> values) {
            addCriterion("delivery_order_id not in", values, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdBetween(Long value1, Long value2) {
            addCriterion("delivery_order_id between", value1, value2, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdNotBetween(Long value1, Long value2) {
            addCriterion("delivery_order_id not between", value1, value2, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andPayTimeIsNull() {
            addCriterion("pay_time is null");
            return (Criteria) this;
        }

        public Criteria andPayTimeIsNotNull() {
            addCriterion("pay_time is not null");
            return (Criteria) this;
        }

        public Criteria andPayTimeEqualTo(LocalDateTime value) {
            addCriterion("pay_time =", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotEqualTo(LocalDateTime value) {
            addCriterion("pay_time <>", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeGreaterThan(LocalDateTime value) {
            addCriterion("pay_time >", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("pay_time >=", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeLessThan(LocalDateTime value) {
            addCriterion("pay_time <", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("pay_time <=", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeIn(List<LocalDateTime> values) {
            addCriterion("pay_time in", values, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotIn(List<LocalDateTime> values) {
            addCriterion("pay_time not in", values, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("pay_time between", value1, value2, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("pay_time not between", value1, value2, "payTime");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdIsNull() {
            addCriterion("channel_order_id is null");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdIsNotNull() {
            addCriterion("channel_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdEqualTo(String value) {
            addCriterion("channel_order_id =", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotEqualTo(String value) {
            addCriterion("channel_order_id <>", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdGreaterThan(String value) {
            addCriterion("channel_order_id >", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("channel_order_id >=", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdLessThan(String value) {
            addCriterion("channel_order_id <", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdLessThanOrEqualTo(String value) {
            addCriterion("channel_order_id <=", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdLike(String value) {
            addCriterion("channel_order_id like", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotLike(String value) {
            addCriterion("channel_order_id not like", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdIn(List<String> values) {
            addCriterion("channel_order_id in", values, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotIn(List<String> values) {
            addCriterion("channel_order_id not in", values, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdBetween(String value1, String value2) {
            addCriterion("channel_order_id between", value1, value2, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotBetween(String value1, String value2) {
            addCriterion("channel_order_id not between", value1, value2, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeIsNull() {
            addCriterion("order_biz_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeIsNotNull() {
            addCriterion("order_biz_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeEqualTo(Integer value) {
            addCriterion("order_biz_type =", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeNotEqualTo(Integer value) {
            addCriterion("order_biz_type <>", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeGreaterThan(Integer value) {
            addCriterion("order_biz_type >", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_biz_type >=", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeLessThan(Integer value) {
            addCriterion("order_biz_type <", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeLessThanOrEqualTo(Integer value) {
            addCriterion("order_biz_type <=", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeIn(List<Integer> values) {
            addCriterion("order_biz_type in", values, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeNotIn(List<Integer> values) {
            addCriterion("order_biz_type not in", values, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeBetween(Integer value1, Integer value2) {
            addCriterion("order_biz_type between", value1, value2, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("order_biz_type not between", value1, value2, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andDaySeqIsNull() {
            addCriterion("day_seq is null");
            return (Criteria) this;
        }

        public Criteria andDaySeqIsNotNull() {
            addCriterion("day_seq is not null");
            return (Criteria) this;
        }

        public Criteria andDaySeqEqualTo(Integer value) {
            addCriterion("day_seq =", value, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqNotEqualTo(Integer value) {
            addCriterion("day_seq <>", value, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqGreaterThan(Integer value) {
            addCriterion("day_seq >", value, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("day_seq >=", value, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqLessThan(Integer value) {
            addCriterion("day_seq <", value, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqLessThanOrEqualTo(Integer value) {
            addCriterion("day_seq <=", value, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqIn(List<Integer> values) {
            addCriterion("day_seq in", values, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqNotIn(List<Integer> values) {
            addCriterion("day_seq not in", values, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqBetween(Integer value1, Integer value2) {
            addCriterion("day_seq between", value1, value2, "daySeq");
            return (Criteria) this;
        }

        public Criteria andDaySeqNotBetween(Integer value1, Integer value2) {
            addCriterion("day_seq not between", value1, value2, "daySeq");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andRiderNameIsNull() {
            addCriterion("rider_name is null");
            return (Criteria) this;
        }

        public Criteria andRiderNameIsNotNull() {
            addCriterion("rider_name is not null");
            return (Criteria) this;
        }

        public Criteria andRiderNameEqualTo(String value) {
            addCriterion("rider_name =", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameNotEqualTo(String value) {
            addCriterion("rider_name <>", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameGreaterThan(String value) {
            addCriterion("rider_name >", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameGreaterThanOrEqualTo(String value) {
            addCriterion("rider_name >=", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameLessThan(String value) {
            addCriterion("rider_name <", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameLessThanOrEqualTo(String value) {
            addCriterion("rider_name <=", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameLike(String value) {
            addCriterion("rider_name like", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameNotLike(String value) {
            addCriterion("rider_name not like", value, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameIn(List<String> values) {
            addCriterion("rider_name in", values, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameNotIn(List<String> values) {
            addCriterion("rider_name not in", values, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameBetween(String value1, String value2) {
            addCriterion("rider_name between", value1, value2, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderNameNotBetween(String value1, String value2) {
            addCriterion("rider_name not between", value1, value2, "riderName");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIsNull() {
            addCriterion("rider_account_id is null");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIsNotNull() {
            addCriterion("rider_account_id is not null");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdEqualTo(Long value) {
            addCriterion("rider_account_id =", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotEqualTo(Long value) {
            addCriterion("rider_account_id <>", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdGreaterThan(Long value) {
            addCriterion("rider_account_id >", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rider_account_id >=", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdLessThan(Long value) {
            addCriterion("rider_account_id <", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdLessThanOrEqualTo(Long value) {
            addCriterion("rider_account_id <=", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIn(List<Long> values) {
            addCriterion("rider_account_id in", values, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotIn(List<Long> values) {
            addCriterion("rider_account_id not in", values, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdBetween(Long value1, Long value2) {
            addCriterion("rider_account_id between", value1, value2, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotBetween(Long value1, Long value2) {
            addCriterion("rider_account_id not between", value1, value2, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andReportExceptionTypeIsNull() {
            addCriterion("report_exception_type is null");
            return (Criteria) this;
        }

        public Criteria andReportExceptionTypeIsNotNull() {
            addCriterion("report_exception_type is not null");
            return (Criteria) this;
        }

        public Criteria andReportExceptionTypeEqualTo(Integer value) {
            addCriterion("report_exception_type =", value, "reportExceptionType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionTypeNotEqualTo(Integer value) {
            addCriterion("report_exception_type <>", value, "reportExceptionType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionTypeGreaterThan(Integer value) {
            addCriterion("report_exception_type >", value, "reportExceptionType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("report_exception_type >=", value, "reportExceptionType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionTypeLessThan(Integer value) {
            addCriterion("report_exception_type <", value, "reportExceptionType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionTypeLessThanOrEqualTo(Integer value) {
            addCriterion("report_exception_type <=", value, "reportExceptionType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionTypeIn(List<Integer> values) {
            addCriterion("report_exception_type in", values, "reportExceptionType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionTypeNotIn(List<Integer> values) {
            addCriterion("report_exception_type not in", values, "reportExceptionType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionTypeBetween(Integer value1, Integer value2) {
            addCriterion("report_exception_type between", value1, value2, "reportExceptionType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("report_exception_type not between", value1, value2, "reportExceptionType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionSubTypeIsNull() {
            addCriterion("report_exception_sub_type is null");
            return (Criteria) this;
        }

        public Criteria andReportExceptionSubTypeIsNotNull() {
            addCriterion("report_exception_sub_type is not null");
            return (Criteria) this;
        }

        public Criteria andReportExceptionSubTypeEqualTo(Integer value) {
            addCriterion("report_exception_sub_type =", value, "reportExceptionSubType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionSubTypeNotEqualTo(Integer value) {
            addCriterion("report_exception_sub_type <>", value, "reportExceptionSubType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionSubTypeGreaterThan(Integer value) {
            addCriterion("report_exception_sub_type >", value, "reportExceptionSubType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionSubTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("report_exception_sub_type >=", value, "reportExceptionSubType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionSubTypeLessThan(Integer value) {
            addCriterion("report_exception_sub_type <", value, "reportExceptionSubType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionSubTypeLessThanOrEqualTo(Integer value) {
            addCriterion("report_exception_sub_type <=", value, "reportExceptionSubType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionSubTypeIn(List<Integer> values) {
            addCriterion("report_exception_sub_type in", values, "reportExceptionSubType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionSubTypeNotIn(List<Integer> values) {
            addCriterion("report_exception_sub_type not in", values, "reportExceptionSubType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionSubTypeBetween(Integer value1, Integer value2) {
            addCriterion("report_exception_sub_type between", value1, value2, "reportExceptionSubType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionSubTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("report_exception_sub_type not between", value1, value2, "reportExceptionSubType");
            return (Criteria) this;
        }

        public Criteria andReportExceptionDescriptionIsNull() {
            addCriterion("report_exception_description is null");
            return (Criteria) this;
        }

        public Criteria andReportExceptionDescriptionIsNotNull() {
            addCriterion("report_exception_description is not null");
            return (Criteria) this;
        }

        public Criteria andReportExceptionDescriptionEqualTo(String value) {
            addCriterion("report_exception_description =", value, "reportExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andReportExceptionDescriptionNotEqualTo(String value) {
            addCriterion("report_exception_description <>", value, "reportExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andReportExceptionDescriptionGreaterThan(String value) {
            addCriterion("report_exception_description >", value, "reportExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andReportExceptionDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("report_exception_description >=", value, "reportExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andReportExceptionDescriptionLessThan(String value) {
            addCriterion("report_exception_description <", value, "reportExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andReportExceptionDescriptionLessThanOrEqualTo(String value) {
            addCriterion("report_exception_description <=", value, "reportExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andReportExceptionDescriptionLike(String value) {
            addCriterion("report_exception_description like", value, "reportExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andReportExceptionDescriptionNotLike(String value) {
            addCriterion("report_exception_description not like", value, "reportExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andReportExceptionDescriptionIn(List<String> values) {
            addCriterion("report_exception_description in", values, "reportExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andReportExceptionDescriptionNotIn(List<String> values) {
            addCriterion("report_exception_description not in", values, "reportExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andReportExceptionDescriptionBetween(String value1, String value2) {
            addCriterion("report_exception_description between", value1, value2, "reportExceptionDescription");
            return (Criteria) this;
        }

        public Criteria andReportExceptionDescriptionNotBetween(String value1, String value2) {
            addCriterion("report_exception_description not between", value1, value2, "reportExceptionDescription");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}