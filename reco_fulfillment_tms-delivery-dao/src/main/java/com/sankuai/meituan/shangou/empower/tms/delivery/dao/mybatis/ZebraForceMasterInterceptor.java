package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mybatis;

import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.google.common.base.Splitter;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.utils.ForceMasterEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.utils.MccDynamicConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.core.annotation.Order;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Properties;

@Slf4j
@Order(1)
@Intercepts({
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
public class ZebraForceMasterInterceptor implements Interceptor {
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        String id = mappedStatement.getId();
        if (slaveFlag(id)) {
//            log.info("mapperMethodName:{} 已查询从库!", mapperMethodName);
            return invocation.proceed();
        }
        String mapperMethodName = fetchClassMethodName(id);
        // 判断接口的强制主库配置
        boolean forceMaster = getForceMasterConfig(mapperMethodName);
        if (!forceMaster) {
//            log.debug("mapperMethodName:{} 默认查询主从库!", mapperMethodName);
            return invocation.proceed();
        }
        // 执行强制主库逻辑
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
//            log.info("mapperMethodName:{} 已强制查询主库!", mapperMethodName);
            return invocation.proceed();
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }


    private Boolean slaveFlag(String method) {
        try {
            return SlaveQueryCacheService.getMethodSet().contains(method);
        } catch (Exception e) {
            log.error("isSlaveQuery error", e);
            return false;
        }
    }

    private boolean getForceMasterConfig(String mapperMethod) {
        // 查询强制主库全局配置
        int config = MccDynamicConfigUtil.getForceMasterConfig();
        if (ForceMasterEnum.NONE.getCode() == config) {
            return false;
        } else if (ForceMasterEnum.ALL.getCode() == config) {
            return true;
        } else {
            // 判断mapper方法不在非强制主库列表中
            return !MccDynamicConfigUtil.getNotForceMasterMapperMethodList().contains(mapperMethod);
        }
    }

    private String fetchClassMethodName(String mappedStatementId) {
        if(StringUtils.isEmpty(mappedStatementId)){
            return StringUtils.EMPTY;
        }
        List<String> list = Splitter.on(".").splitToList(mappedStatementId);
        if (CollectionUtils.isEmpty(list) || list.size() <= 1) {
            return mappedStatementId;
        }
        return list.get(list.size() - 2) + "." + list.get(list.size() - 1);
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}

