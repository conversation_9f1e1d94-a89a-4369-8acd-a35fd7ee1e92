package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.sql.Date;

public class DeliveryChannelDO {
    private Long id;

    private String logisticMark;

    private Integer deliveryPlatformCode;

    private Integer carrierCode;

    private String carrierName;

    private Integer orderChannelCode;

    private Date ctime;

    private Date utime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLogisticMark() {
        return logisticMark;
    }

    public void setLogisticMark(String logisticMark) {
        this.logisticMark = logisticMark == null ? null : logisticMark.trim();
    }

    public Integer getDeliveryPlatformCode() {
        return deliveryPlatformCode;
    }

    public void setDeliveryPlatformCode(Integer deliveryPlatformCode) {
        this.deliveryPlatformCode = deliveryPlatformCode;
    }

    public Integer getCarrierCode() {
        return carrierCode;
    }

    public void setCarrierCode(Integer carrierCode) {
        this.carrierCode = carrierCode;
    }

    public String getCarrierName() {
        return carrierName;
    }

    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName == null ? null : carrierName.trim();
    }

    public Integer getOrderChannelCode() {
        return orderChannelCode;
    }

    public void setOrderChannelCode(Integer orderChannelCode) {
        this.orderChannelCode = orderChannelCode;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public Date getUtime() {
        return utime;
    }

    public void setUtime(Date utime) {
        this.utime = utime;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DeliveryChannelDO other = (DeliveryChannelDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getLogisticMark() == null ? other.getLogisticMark() == null : this.getLogisticMark().equals(other.getLogisticMark()))
            && (this.getDeliveryPlatformCode() == null ? other.getDeliveryPlatformCode() == null : this.getDeliveryPlatformCode().equals(other.getDeliveryPlatformCode()))
            && (this.getCarrierCode() == null ? other.getCarrierCode() == null : this.getCarrierCode().equals(other.getCarrierCode()))
            && (this.getCarrierName() == null ? other.getCarrierName() == null : this.getCarrierName().equals(other.getCarrierName()))
            && (this.getOrderChannelCode() == null ? other.getOrderChannelCode() == null : this.getOrderChannelCode().equals(other.getOrderChannelCode()))
            && (this.getCtime() == null ? other.getCtime() == null : this.getCtime().equals(other.getCtime()))
            && (this.getUtime() == null ? other.getUtime() == null : this.getUtime().equals(other.getUtime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getLogisticMark() == null) ? 0 : getLogisticMark().hashCode());
        result = prime * result + ((getDeliveryPlatformCode() == null) ? 0 : getDeliveryPlatformCode().hashCode());
        result = prime * result + ((getCarrierCode() == null) ? 0 : getCarrierCode().hashCode());
        result = prime * result + ((getCarrierName() == null) ? 0 : getCarrierName().hashCode());
        result = prime * result + ((getOrderChannelCode() == null) ? 0 : getOrderChannelCode().hashCode());
        result = prime * result + ((getCtime() == null) ? 0 : getCtime().hashCode());
        result = prime * result + ((getUtime() == null) ? 0 : getUtime().hashCode());
        return result;
    }
}