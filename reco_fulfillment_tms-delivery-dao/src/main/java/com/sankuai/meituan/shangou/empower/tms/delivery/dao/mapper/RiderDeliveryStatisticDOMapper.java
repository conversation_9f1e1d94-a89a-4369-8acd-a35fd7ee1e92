package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RiderDeliveryStatisticDOMapper {
    long countByExample(RiderDeliveryStatisticDOExample example);

    int deleteByExample(RiderDeliveryStatisticDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RiderDeliveryStatisticDO record);

    int insertSelective(RiderDeliveryStatisticDO record);

    List<RiderDeliveryStatisticDO> selectByExample(RiderDeliveryStatisticDOExample example);

    RiderDeliveryStatisticDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RiderDeliveryStatisticDO record, @Param("example") RiderDeliveryStatisticDOExample example);

    int updateByExample(@Param("record") RiderDeliveryStatisticDO record, @Param("example") RiderDeliveryStatisticDOExample example);

    int updateByPrimaryKeySelective(RiderDeliveryStatisticDO record);

    int updateByPrimaryKey(RiderDeliveryStatisticDO record);
}