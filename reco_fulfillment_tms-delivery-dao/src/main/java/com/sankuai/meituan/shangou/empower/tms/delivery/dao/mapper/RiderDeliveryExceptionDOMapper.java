package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RiderDeliveryExceptionDOMapper {
    long countByExample(RiderDeliveryExceptionDOExample example);

    int deleteByExample(RiderDeliveryExceptionDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RiderDeliveryExceptionDO record);

    int insertSelective(RiderDeliveryExceptionDO record);

    List<RiderDeliveryExceptionDO> selectByExample(RiderDeliveryExceptionDOExample example);

    RiderDeliveryExceptionDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RiderDeliveryExceptionDO record, @Param("example") RiderDeliveryExceptionDOExample example);

    int updateByExample(@Param("record") RiderDeliveryExceptionDO record, @Param("example") RiderDeliveryExceptionDOExample example);

    int updateByPrimaryKeySelective(RiderDeliveryExceptionDO record);

    int updateByPrimaryKey(RiderDeliveryExceptionDO record);
}