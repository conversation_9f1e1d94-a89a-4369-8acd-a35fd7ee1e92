package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * ObjectMapperWrapper
 *
 * <AUTHOR>
 * @since 2022/4/8
 */
@Slf4j
@Component
public class ObjectMapperWrapper {

    private final ObjectMapper objectMapper;

    public ObjectMapperWrapper(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    public <T> T readValue(String content, Class<T> valueType) {
        try {
            return objectMapper.readValue(content, valueType);
        } catch (IOException e) {
            log.error("readValue error: {}", e, e);
            throw new IllegalStateException(e);
        }
    }

    public <T> T readValue(String content, TypeReference<T> valueTypeRef) {
        try {
            return objectMapper.readValue(content, valueTypeRef);
        } catch (IOException e) {
            log.error("readValue error: {}", e, e);
            throw new IllegalStateException(e);
        }
    }

    public <T> String writeValueAsString(T doc) {
        try {
            return objectMapper.writeValueAsString(doc);
        } catch (JsonProcessingException e) {
            log.error("writeValueAsString error: {}", e, e);
            throw new IllegalStateException(e);
        }
    }

}
