package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class DeliveryOrderDO {
    private Long id;

    private Long tenantId;

    private Long storeId;

    private Long orderId;

    private Long fulfillmentOrderId;

    private Integer platformSource;

    private String channelOrderId;

    private Integer orderBizType;

    private Integer daySeq;

    private Integer reserved;

    private Integer orderSource;

    private String receiverName;

    private String receiverPhone;

    private String receiverPrivacyPhone;

    private String receiverAddress;

    private LocalDateTime estimatedDeliveryTime;

    private LocalDateTime estimatedDeliveryEndTime;

    private Integer deliveryChannel;

    private String channelDeliveryId;

    private String channelServicePackageCode;

    private Integer deliveryStatus;

    private Integer deliveryExceptionType;

    private String deliveryExceptionDescription;

    private String riderName;

    private String riderPhone;

    private String riderPhoneToken;

    private Long riderAccountId;

    private Long activeStatus;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private LocalDateTime lastEventTime;

    private LocalDateTime deliveryDoneTime;

    private Integer version;

    private BigDecimal deliveryFee;

    private Long distance;

    private Integer cancelMark;

    private Integer deliveryExceptionCode;

    private BigDecimal tipAmount;

    private Integer deliveryCount;

    private BigDecimal platformFee;

    private Integer currentStatusLock;

    private Integer totalLockCount;

    private LocalDateTime currentLockTime;

    private LocalDateTime currentUnlockTime;

    private String extInfo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getFulfillmentOrderId() {
        return fulfillmentOrderId;
    }

    public void setFulfillmentOrderId(Long fulfillmentOrderId) {
        this.fulfillmentOrderId = fulfillmentOrderId;
    }

    public Integer getPlatformSource() {
        return platformSource;
    }

    public void setPlatformSource(Integer platformSource) {
        this.platformSource = platformSource;
    }

    public String getChannelOrderId() {
        return channelOrderId;
    }

    public void setChannelOrderId(String channelOrderId) {
        this.channelOrderId = channelOrderId == null ? null : channelOrderId.trim();
    }

    public Integer getOrderBizType() {
        return orderBizType;
    }

    public void setOrderBizType(Integer orderBizType) {
        this.orderBizType = orderBizType;
    }

    public Integer getDaySeq() {
        return daySeq;
    }

    public void setDaySeq(Integer daySeq) {
        this.daySeq = daySeq;
    }

    public Integer getReserved() {
        return reserved;
    }

    public void setReserved(Integer reserved) {
        this.reserved = reserved;
    }

    public Integer getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(Integer orderSource) {
        this.orderSource = orderSource;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName == null ? null : receiverName.trim();
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone == null ? null : receiverPhone.trim();
    }

    public String getReceiverPrivacyPhone() {
        return receiverPrivacyPhone;
    }

    public void setReceiverPrivacyPhone(String receiverPrivacyPhone) {
        this.receiverPrivacyPhone = receiverPrivacyPhone == null ? null : receiverPrivacyPhone.trim();
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress == null ? null : receiverAddress.trim();
    }

    public LocalDateTime getEstimatedDeliveryTime() {
        return estimatedDeliveryTime;
    }

    public void setEstimatedDeliveryTime(LocalDateTime estimatedDeliveryTime) {
        this.estimatedDeliveryTime = estimatedDeliveryTime;
    }

    public LocalDateTime getEstimatedDeliveryEndTime() {
        return estimatedDeliveryEndTime;
    }

    public void setEstimatedDeliveryEndTime(LocalDateTime estimatedDeliveryEndTime) {
        this.estimatedDeliveryEndTime = estimatedDeliveryEndTime;
    }

    public Integer getDeliveryChannel() {
        return deliveryChannel;
    }

    public void setDeliveryChannel(Integer deliveryChannel) {
        this.deliveryChannel = deliveryChannel;
    }

    public String getChannelDeliveryId() {
        return channelDeliveryId;
    }

    public void setChannelDeliveryId(String channelDeliveryId) {
        this.channelDeliveryId = channelDeliveryId == null ? null : channelDeliveryId.trim();
    }

    public String getChannelServicePackageCode() {
        return channelServicePackageCode;
    }

    public void setChannelServicePackageCode(String channelServicePackageCode) {
        this.channelServicePackageCode = channelServicePackageCode == null ? null : channelServicePackageCode.trim();
    }

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public Integer getDeliveryExceptionType() {
        return deliveryExceptionType;
    }

    public void setDeliveryExceptionType(Integer deliveryExceptionType) {
        this.deliveryExceptionType = deliveryExceptionType;
    }

    public String getDeliveryExceptionDescription() {
        return deliveryExceptionDescription;
    }

    public void setDeliveryExceptionDescription(String deliveryExceptionDescription) {
        this.deliveryExceptionDescription = deliveryExceptionDescription == null ? null : deliveryExceptionDescription.trim();
    }

    public String getRiderName() {
        return riderName;
    }

    public void setRiderName(String riderName) {
        this.riderName = riderName == null ? null : riderName.trim();
    }

    public String getRiderPhone() {
        return riderPhone;
    }

    public void setRiderPhone(String riderPhone) {
        this.riderPhone = riderPhone == null ? null : riderPhone.trim();
    }

    public String getRiderPhoneToken() {
        return riderPhoneToken;
    }

    public void setRiderPhoneToken(String riderPhoneToken) {
        this.riderPhoneToken = riderPhoneToken == null ? null : riderPhoneToken.trim();
    }

    public Long getRiderAccountId() {
        return riderAccountId;
    }

    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    public Long getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(Long activeStatus) {
        this.activeStatus = activeStatus;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getLastEventTime() {
        return lastEventTime;
    }

    public void setLastEventTime(LocalDateTime lastEventTime) {
        this.lastEventTime = lastEventTime;
    }

    public LocalDateTime getDeliveryDoneTime() {
        return deliveryDoneTime;
    }

    public void setDeliveryDoneTime(LocalDateTime deliveryDoneTime) {
        this.deliveryDoneTime = deliveryDoneTime;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public BigDecimal getDeliveryFee() {
        return deliveryFee;
    }

    public void setDeliveryFee(BigDecimal deliveryFee) {
        this.deliveryFee = deliveryFee;
    }

    public Long getDistance() {
        return distance;
    }

    public void setDistance(Long distance) {
        this.distance = distance;
    }

    public Integer getCancelMark() {
        return cancelMark;
    }

    public void setCancelMark(Integer cancelMark) {
        this.cancelMark = cancelMark;
    }

    public Integer getDeliveryExceptionCode() {
        return deliveryExceptionCode;
    }

    public void setDeliveryExceptionCode(Integer deliveryExceptionCode) {
        this.deliveryExceptionCode = deliveryExceptionCode;
    }

    public BigDecimal getTipAmount() {
        return tipAmount;
    }

    public void setTipAmount(BigDecimal tipAmount) {
        this.tipAmount = tipAmount;
    }

    public Integer getDeliveryCount() {
        return deliveryCount;
    }

    public void setDeliveryCount(Integer deliveryCount) {
        this.deliveryCount = deliveryCount;
    }

    public BigDecimal getPlatformFee() {
        return platformFee;
    }

    public void setPlatformFee(BigDecimal platformFee) {
        this.platformFee = platformFee;
    }

    public Integer getCurrentStatusLock() {
        return currentStatusLock;
    }

    public void setCurrentStatusLock(Integer currentStatusLock) {
        this.currentStatusLock = currentStatusLock;
    }

    public Integer getTotalLockCount() {
        return totalLockCount;
    }

    public void setTotalLockCount(Integer totalLockCount) {
        this.totalLockCount = totalLockCount;
    }

    public LocalDateTime getCurrentLockTime() {
        return currentLockTime;
    }

    public void setCurrentLockTime(LocalDateTime currentLockTime) {
        this.currentLockTime = currentLockTime;
    }

    public LocalDateTime getCurrentUnlockTime() {
        return currentUnlockTime;
    }

    public void setCurrentUnlockTime(LocalDateTime currentUnlockTime) {
        this.currentUnlockTime = currentUnlockTime;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo == null ? null : extInfo.trim();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DeliveryOrderDO other = (DeliveryOrderDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getFulfillmentOrderId() == null ? other.getFulfillmentOrderId() == null : this.getFulfillmentOrderId().equals(other.getFulfillmentOrderId()))
            && (this.getPlatformSource() == null ? other.getPlatformSource() == null : this.getPlatformSource().equals(other.getPlatformSource()))
            && (this.getChannelOrderId() == null ? other.getChannelOrderId() == null : this.getChannelOrderId().equals(other.getChannelOrderId()))
            && (this.getOrderBizType() == null ? other.getOrderBizType() == null : this.getOrderBizType().equals(other.getOrderBizType()))
            && (this.getDaySeq() == null ? other.getDaySeq() == null : this.getDaySeq().equals(other.getDaySeq()))
            && (this.getReserved() == null ? other.getReserved() == null : this.getReserved().equals(other.getReserved()))
            && (this.getOrderSource() == null ? other.getOrderSource() == null : this.getOrderSource().equals(other.getOrderSource()))
            && (this.getReceiverName() == null ? other.getReceiverName() == null : this.getReceiverName().equals(other.getReceiverName()))
            && (this.getReceiverPhone() == null ? other.getReceiverPhone() == null : this.getReceiverPhone().equals(other.getReceiverPhone()))
            && (this.getReceiverPrivacyPhone() == null ? other.getReceiverPrivacyPhone() == null : this.getReceiverPrivacyPhone().equals(other.getReceiverPrivacyPhone()))
            && (this.getReceiverAddress() == null ? other.getReceiverAddress() == null : this.getReceiverAddress().equals(other.getReceiverAddress()))
            && (this.getEstimatedDeliveryTime() == null ? other.getEstimatedDeliveryTime() == null : this.getEstimatedDeliveryTime().equals(other.getEstimatedDeliveryTime()))
            && (this.getEstimatedDeliveryEndTime() == null ? other.getEstimatedDeliveryEndTime() == null : this.getEstimatedDeliveryEndTime().equals(other.getEstimatedDeliveryEndTime()))
            && (this.getDeliveryChannel() == null ? other.getDeliveryChannel() == null : this.getDeliveryChannel().equals(other.getDeliveryChannel()))
            && (this.getChannelDeliveryId() == null ? other.getChannelDeliveryId() == null : this.getChannelDeliveryId().equals(other.getChannelDeliveryId()))
            && (this.getChannelServicePackageCode() == null ? other.getChannelServicePackageCode() == null : this.getChannelServicePackageCode().equals(other.getChannelServicePackageCode()))
            && (this.getDeliveryStatus() == null ? other.getDeliveryStatus() == null : this.getDeliveryStatus().equals(other.getDeliveryStatus()))
            && (this.getDeliveryExceptionType() == null ? other.getDeliveryExceptionType() == null : this.getDeliveryExceptionType().equals(other.getDeliveryExceptionType()))
            && (this.getDeliveryExceptionDescription() == null ? other.getDeliveryExceptionDescription() == null : this.getDeliveryExceptionDescription().equals(other.getDeliveryExceptionDescription()))
            && (this.getRiderName() == null ? other.getRiderName() == null : this.getRiderName().equals(other.getRiderName()))
            && (this.getRiderPhone() == null ? other.getRiderPhone() == null : this.getRiderPhone().equals(other.getRiderPhone()))
            && (this.getRiderPhoneToken() == null ? other.getRiderPhoneToken() == null : this.getRiderPhoneToken().equals(other.getRiderPhoneToken()))
            && (this.getRiderAccountId() == null ? other.getRiderAccountId() == null : this.getRiderAccountId().equals(other.getRiderAccountId()))
            && (this.getActiveStatus() == null ? other.getActiveStatus() == null : this.getActiveStatus().equals(other.getActiveStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getLastEventTime() == null ? other.getLastEventTime() == null : this.getLastEventTime().equals(other.getLastEventTime()))
            && (this.getDeliveryDoneTime() == null ? other.getDeliveryDoneTime() == null : this.getDeliveryDoneTime().equals(other.getDeliveryDoneTime()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getDeliveryFee() == null ? other.getDeliveryFee() == null : this.getDeliveryFee().equals(other.getDeliveryFee()))
            && (this.getDistance() == null ? other.getDistance() == null : this.getDistance().equals(other.getDistance()))
            && (this.getCancelMark() == null ? other.getCancelMark() == null : this.getCancelMark().equals(other.getCancelMark()))
            && (this.getDeliveryExceptionCode() == null ? other.getDeliveryExceptionCode() == null : this.getDeliveryExceptionCode().equals(other.getDeliveryExceptionCode()))
            && (this.getTipAmount() == null ? other.getTipAmount() == null : this.getTipAmount().equals(other.getTipAmount()))
            && (this.getDeliveryCount() == null ? other.getDeliveryCount() == null : this.getDeliveryCount().equals(other.getDeliveryCount()))
            && (this.getPlatformFee() == null ? other.getPlatformFee() == null : this.getPlatformFee().equals(other.getPlatformFee()))
            && (this.getCurrentStatusLock() == null ? other.getCurrentStatusLock() == null : this.getCurrentStatusLock().equals(other.getCurrentStatusLock()))
            && (this.getTotalLockCount() == null ? other.getTotalLockCount() == null : this.getTotalLockCount().equals(other.getTotalLockCount()))
            && (this.getCurrentLockTime() == null ? other.getCurrentLockTime() == null : this.getCurrentLockTime().equals(other.getCurrentLockTime()))
            && (this.getCurrentUnlockTime() == null ? other.getCurrentUnlockTime() == null : this.getCurrentUnlockTime().equals(other.getCurrentUnlockTime()))
            && (this.getExtInfo() == null ? other.getExtInfo() == null : this.getExtInfo().equals(other.getExtInfo()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getFulfillmentOrderId() == null) ? 0 : getFulfillmentOrderId().hashCode());
        result = prime * result + ((getPlatformSource() == null) ? 0 : getPlatformSource().hashCode());
        result = prime * result + ((getChannelOrderId() == null) ? 0 : getChannelOrderId().hashCode());
        result = prime * result + ((getOrderBizType() == null) ? 0 : getOrderBizType().hashCode());
        result = prime * result + ((getDaySeq() == null) ? 0 : getDaySeq().hashCode());
        result = prime * result + ((getReserved() == null) ? 0 : getReserved().hashCode());
        result = prime * result + ((getOrderSource() == null) ? 0 : getOrderSource().hashCode());
        result = prime * result + ((getReceiverName() == null) ? 0 : getReceiverName().hashCode());
        result = prime * result + ((getReceiverPhone() == null) ? 0 : getReceiverPhone().hashCode());
        result = prime * result + ((getReceiverPrivacyPhone() == null) ? 0 : getReceiverPrivacyPhone().hashCode());
        result = prime * result + ((getReceiverAddress() == null) ? 0 : getReceiverAddress().hashCode());
        result = prime * result + ((getEstimatedDeliveryTime() == null) ? 0 : getEstimatedDeliveryTime().hashCode());
        result = prime * result + ((getEstimatedDeliveryEndTime() == null) ? 0 : getEstimatedDeliveryEndTime().hashCode());
        result = prime * result + ((getDeliveryChannel() == null) ? 0 : getDeliveryChannel().hashCode());
        result = prime * result + ((getChannelDeliveryId() == null) ? 0 : getChannelDeliveryId().hashCode());
        result = prime * result + ((getChannelServicePackageCode() == null) ? 0 : getChannelServicePackageCode().hashCode());
        result = prime * result + ((getDeliveryStatus() == null) ? 0 : getDeliveryStatus().hashCode());
        result = prime * result + ((getDeliveryExceptionType() == null) ? 0 : getDeliveryExceptionType().hashCode());
        result = prime * result + ((getDeliveryExceptionDescription() == null) ? 0 : getDeliveryExceptionDescription().hashCode());
        result = prime * result + ((getRiderName() == null) ? 0 : getRiderName().hashCode());
        result = prime * result + ((getRiderPhone() == null) ? 0 : getRiderPhone().hashCode());
        result = prime * result + ((getRiderPhoneToken() == null) ? 0 : getRiderPhoneToken().hashCode());
        result = prime * result + ((getRiderAccountId() == null) ? 0 : getRiderAccountId().hashCode());
        result = prime * result + ((getActiveStatus() == null) ? 0 : getActiveStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getLastEventTime() == null) ? 0 : getLastEventTime().hashCode());
        result = prime * result + ((getDeliveryDoneTime() == null) ? 0 : getDeliveryDoneTime().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getDeliveryFee() == null) ? 0 : getDeliveryFee().hashCode());
        result = prime * result + ((getDistance() == null) ? 0 : getDistance().hashCode());
        result = prime * result + ((getCancelMark() == null) ? 0 : getCancelMark().hashCode());
        result = prime * result + ((getDeliveryExceptionCode() == null) ? 0 : getDeliveryExceptionCode().hashCode());
        result = prime * result + ((getTipAmount() == null) ? 0 : getTipAmount().hashCode());
        result = prime * result + ((getDeliveryCount() == null) ? 0 : getDeliveryCount().hashCode());
        result = prime * result + ((getPlatformFee() == null) ? 0 : getPlatformFee().hashCode());
        result = prime * result + ((getCurrentStatusLock() == null) ? 0 : getCurrentStatusLock().hashCode());
        result = prime * result + ((getTotalLockCount() == null) ? 0 : getTotalLockCount().hashCode());
        result = prime * result + ((getCurrentLockTime() == null) ? 0 : getCurrentLockTime().hashCode());
        result = prime * result + ((getCurrentUnlockTime() == null) ? 0 : getCurrentUnlockTime().hashCode());
        result = prime * result + ((getExtInfo() == null) ? 0 : getExtInfo().hashCode());
        return result;
    }
}