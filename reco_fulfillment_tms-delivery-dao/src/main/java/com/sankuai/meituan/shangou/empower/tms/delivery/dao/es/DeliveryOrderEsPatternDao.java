package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.base.BaseEsDatePatternDao;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.base.GetResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.constants.DeliveryOrderConstants;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.exception.EsBaseException;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.po.DeliveryOrderEsPo;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils.MccUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * 运单索引的路由元数据是storeId，对应es里的增删改查方法都要带上这个routingKey
 * <AUTHOR>
 */
@Slf4j
@Repository
public class DeliveryOrderEsPatternDao extends BaseEsDatePatternDao<DeliveryOrderEsPo> {

    public DeliveryOrderEsPatternDao() {
        super(DeliveryOrderConstants.MONTH_PATTERN_INDEX);
    }

    /**
     * 根据赋能订单ID查询ES中的记录
     * 运单会出现跨月查询的情况，此时如果查询时间对应的索引没有查到，向前查一个月
     * 运单索引的doc_id即赋能订单ID，get查询效率比search高，因此这里没有使用索引别名查询，而是指定索引基于doc_id get查询
     */
    public DeliveryOrderEsPo getDeliveryOrderEsPoByOrderId(Long orderId, Long storeId) {
        LocalDateTime now = LocalDateTime.now();
        GetResult<DeliveryOrderEsPo> getResult = getV2(orderId, now, String.valueOf(storeId));
        if (getResult.isNotEmpty()) {
            wrapGetResult(getResult);
            return getResult.getData();
        }

        log.warn("getDeliveryOrderEsPoByOrderId, getResult is empty, orderId: {}", orderId);
        // 跨月查询会出现用当前时间查询结果为空，此时再向前多查一个月
        int crossMonthNumber = MccUtils.getDeliveryOrderCrossMonthNumber();
        for (int startMonthNumber = NumberUtils.INTEGER_ONE; startMonthNumber <= crossMonthNumber; ++startMonthNumber) {
            LocalDateTime startTime = now.minusMonths(startMonthNumber);
            GetResult<DeliveryOrderEsPo> result = getV2(orderId, startTime, String.valueOf(storeId));
            if (result.isNotEmpty()) {
                wrapGetResult(result);
                return result.getData();
            }
        }

        log.warn("getDeliveryOrderEsPoByOrderId, get result not found, orderId: {}", orderId);
        return null;
    }

    public void saveDeliveryOrderEsPo(DeliveryOrderEsPo deliveryOrderEsPo) {
        save(deliveryOrderEsPo);
    }

    public void updateDeliveryOrderEsPo(DeliveryOrderEsPo deliveryOrderEsPo) {
        updateWithSeqNoAndPrimaryTerm(deliveryOrderEsPo);
    }

    /**
     * 包装BaseEsReadDao返回的getResult
     * delivery_order.*索引的indexKey是create_time字段，这里设置indexKey
     * delivery_order.*索引的routingValue是storeId字段，这里设置routingValue
     */
    private void wrapGetResult(GetResult<DeliveryOrderEsPo> getResult) {
        Objects.requireNonNull(getResult, "wrapSearchResult, getResult is null");
        DeliveryOrderEsPo data = getResult.getData();
        if(Objects.isNull(data)) {
            log.warn("wrapGetResult, data is null");
            return;
        }

        LocalDateTime indexKey = getIndexKey(data);
        data.setIndexKey(indexKey);

        Long storeId = data.getStoreId();
        if (Objects.isNull(storeId)) {
            throw new EsBaseException("", "wrapGetResult, storeId is null");
        }
        data.setRoutingValue(String.valueOf(storeId));
    }

    /**
     * 获取索引记录对应的indexKey
     * 如果订单对应多个月份的运单，indexKey取最早创建的运单的create_time
     */
    private LocalDateTime getIndexKey(DeliveryOrderEsPo deliveryOrderEsPo) {
        String deliveryTraceListStr = deliveryOrderEsPo.getDeliveryTraceList();
        if (StringUtils.isEmpty(deliveryTraceListStr)) {
            log.warn("getIndexKey, deliveryTraceListStr is empty");
            LocalDateTime createTime = deliveryOrderEsPo.getCreateTime();
            if (Objects.isNull(createTime)) {
                throw new EsBaseException("", "getIndexKey, createTime is null");
            }
            return createTime;
        }

        List<DeliveryOrderEsPo.DeliveryOrderSubPo> deliveryOrderSubPoList = JsonUtil.fromJson
                (deliveryTraceListStr, new TypeReference<List<DeliveryOrderEsPo.DeliveryOrderSubPo>>(){});
        if (CollectionUtils.isEmpty(deliveryOrderSubPoList)) {
            log.warn("getIndexKey, deliveryOrderSubPoList is empty");
            LocalDateTime createTime = deliveryOrderEsPo.getCreateTime();
            if (Objects.isNull(createTime)) {
                throw new EsBaseException("", "getIndexKey, createTime is null");
            }
            return createTime;
        }

        // 取最早创建的运单的create_time
        deliveryOrderSubPoList.sort(Comparator.comparing(DeliveryOrderEsPo.DeliveryOrderSubPo::getCreateTime));
        return deliveryOrderSubPoList.get(0).getCreateTime();
    }

}
