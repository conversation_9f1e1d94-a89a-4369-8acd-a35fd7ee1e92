package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * ES基本模型
 *
 * <AUTHOR>
 * @since 2022/3/31
 */
@Data
public abstract class BaseEsPo {

    /**
     * 文档id，可以作为es查询结果的id，也可以用于设置插入es的id
     */
    @JsonIgnore
    private String id;

    /**
     * 作为排序条件的字段
     */
    @JsonIgnore
    private Object[] sortValues;

    /**
     * 文档版本，可以用于并发控制
     */
    @JsonIgnore
    private long version;

    /**
     * 索引路由键
     */
    @JsonIgnore
    private LocalDateTime indexKey;

    /**
     * 路由元数据
     */
    @JsonIgnore
    private String routingValue;

    /**
     * 文档版本号
     */
    @JsonIgnore
    private Long seqNo;

    /**
     * 文档版本号
     */
    @JsonIgnore
    private Long primaryTerm;

}
