package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.time.LocalDateTime;

public class DeliveryMergeOrderRecordDO {
    private Long id;

    private Long deliveryOrderId;

    private Long mergeId;

    private Long activeStatus;

    private String channelOrderId;

    private Integer orderBizType;

    private Long riderAccountId;

    private String extInfo;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDeliveryOrderId() {
        return deliveryOrderId;
    }

    public void setDeliveryOrderId(Long deliveryOrderId) {
        this.deliveryOrderId = deliveryOrderId;
    }

    public Long getMergeId() {
        return mergeId;
    }

    public void setMergeId(Long mergeId) {
        this.mergeId = mergeId;
    }

    public Long getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(Long activeStatus) {
        this.activeStatus = activeStatus;
    }

    public String getChannelOrderId() {
        return channelOrderId;
    }

    public void setChannelOrderId(String channelOrderId) {
        this.channelOrderId = channelOrderId == null ? null : channelOrderId.trim();
    }

    public Integer getOrderBizType() {
        return orderBizType;
    }

    public void setOrderBizType(Integer orderBizType) {
        this.orderBizType = orderBizType;
    }

    public Long getRiderAccountId() {
        return riderAccountId;
    }

    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo == null ? null : extInfo.trim();
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DeliveryMergeOrderRecordDO other = (DeliveryMergeOrderRecordDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getDeliveryOrderId() == null ? other.getDeliveryOrderId() == null : this.getDeliveryOrderId().equals(other.getDeliveryOrderId()))
            && (this.getMergeId() == null ? other.getMergeId() == null : this.getMergeId().equals(other.getMergeId()))
            && (this.getActiveStatus() == null ? other.getActiveStatus() == null : this.getActiveStatus().equals(other.getActiveStatus()))
            && (this.getChannelOrderId() == null ? other.getChannelOrderId() == null : this.getChannelOrderId().equals(other.getChannelOrderId()))
            && (this.getOrderBizType() == null ? other.getOrderBizType() == null : this.getOrderBizType().equals(other.getOrderBizType()))
            && (this.getRiderAccountId() == null ? other.getRiderAccountId() == null : this.getRiderAccountId().equals(other.getRiderAccountId()))
            && (this.getExtInfo() == null ? other.getExtInfo() == null : this.getExtInfo().equals(other.getExtInfo()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getDeliveryOrderId() == null) ? 0 : getDeliveryOrderId().hashCode());
        result = prime * result + ((getMergeId() == null) ? 0 : getMergeId().hashCode());
        result = prime * result + ((getActiveStatus() == null) ? 0 : getActiveStatus().hashCode());
        result = prime * result + ((getChannelOrderId() == null) ? 0 : getChannelOrderId().hashCode());
        result = prime * result + ((getOrderBizType() == null) ? 0 : getOrderBizType().hashCode());
        result = prime * result + ((getRiderAccountId() == null) ? 0 : getRiderAccountId().hashCode());
        result = prime * result + ((getExtInfo() == null) ? 0 : getExtInfo().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }
}