package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ShopDeliveryConfigDOMapper {
    long countByExample(ShopDeliveryConfigDOExample example);

    int deleteByExample(ShopDeliveryConfigDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ShopDeliveryConfigDO record);

    int insertSelective(ShopDeliveryConfigDO record);

    List<ShopDeliveryConfigDO> selectByExample(ShopDeliveryConfigDOExample example);

    ShopDeliveryConfigDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ShopDeliveryConfigDO record, @Param("example") ShopDeliveryConfigDOExample example);

    int updateByExample(@Param("record") ShopDeliveryConfigDO record, @Param("example") ShopDeliveryConfigDOExample example);

    int updateByPrimaryKeySelective(ShopDeliveryConfigDO record);

    int updateByPrimaryKey(ShopDeliveryConfigDO record);
}