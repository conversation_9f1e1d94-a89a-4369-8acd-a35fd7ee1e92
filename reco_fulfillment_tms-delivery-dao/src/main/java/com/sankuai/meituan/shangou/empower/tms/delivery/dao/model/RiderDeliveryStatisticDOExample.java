package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.util.ArrayList;
import java.util.List;

public class RiderDeliveryStatisticDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public RiderDeliveryStatisticDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(Long value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(Long value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(Long value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(Long value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(Long value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<Long> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<Long> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(Long value1, Long value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(Long value1, Long value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIsNull() {
            addCriterion("rider_account_id is null");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIsNotNull() {
            addCriterion("rider_account_id is not null");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdEqualTo(Long value) {
            addCriterion("rider_account_id =", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotEqualTo(Long value) {
            addCriterion("rider_account_id <>", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdGreaterThan(Long value) {
            addCriterion("rider_account_id >", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rider_account_id >=", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdLessThan(Long value) {
            addCriterion("rider_account_id <", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdLessThanOrEqualTo(Long value) {
            addCriterion("rider_account_id <=", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIn(List<Long> values) {
            addCriterion("rider_account_id in", values, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotIn(List<Long> values) {
            addCriterion("rider_account_id not in", values, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdBetween(Long value1, Long value2) {
            addCriterion("rider_account_id between", value1, value2, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotBetween(Long value1, Long value2) {
            addCriterion("rider_account_id not between", value1, value2, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andDeliveredIsNull() {
            addCriterion("delivered is null");
            return (Criteria) this;
        }

        public Criteria andDeliveredIsNotNull() {
            addCriterion("delivered is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveredEqualTo(Integer value) {
            addCriterion("delivered =", value, "delivered");
            return (Criteria) this;
        }

        public Criteria andDeliveredNotEqualTo(Integer value) {
            addCriterion("delivered <>", value, "delivered");
            return (Criteria) this;
        }

        public Criteria andDeliveredGreaterThan(Integer value) {
            addCriterion("delivered >", value, "delivered");
            return (Criteria) this;
        }

        public Criteria andDeliveredGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivered >=", value, "delivered");
            return (Criteria) this;
        }

        public Criteria andDeliveredLessThan(Integer value) {
            addCriterion("delivered <", value, "delivered");
            return (Criteria) this;
        }

        public Criteria andDeliveredLessThanOrEqualTo(Integer value) {
            addCriterion("delivered <=", value, "delivered");
            return (Criteria) this;
        }

        public Criteria andDeliveredIn(List<Integer> values) {
            addCriterion("delivered in", values, "delivered");
            return (Criteria) this;
        }

        public Criteria andDeliveredNotIn(List<Integer> values) {
            addCriterion("delivered not in", values, "delivered");
            return (Criteria) this;
        }

        public Criteria andDeliveredBetween(Integer value1, Integer value2) {
            addCriterion("delivered between", value1, value2, "delivered");
            return (Criteria) this;
        }

        public Criteria andDeliveredNotBetween(Integer value1, Integer value2) {
            addCriterion("delivered not between", value1, value2, "delivered");
            return (Criteria) this;
        }

        public Criteria andCancelBeforeDeliveredIsNull() {
            addCriterion("cancel_before_delivered is null");
            return (Criteria) this;
        }

        public Criteria andCancelBeforeDeliveredIsNotNull() {
            addCriterion("cancel_before_delivered is not null");
            return (Criteria) this;
        }

        public Criteria andCancelBeforeDeliveredEqualTo(Integer value) {
            addCriterion("cancel_before_delivered =", value, "cancelBeforeDelivered");
            return (Criteria) this;
        }

        public Criteria andCancelBeforeDeliveredNotEqualTo(Integer value) {
            addCriterion("cancel_before_delivered <>", value, "cancelBeforeDelivered");
            return (Criteria) this;
        }

        public Criteria andCancelBeforeDeliveredGreaterThan(Integer value) {
            addCriterion("cancel_before_delivered >", value, "cancelBeforeDelivered");
            return (Criteria) this;
        }

        public Criteria andCancelBeforeDeliveredGreaterThanOrEqualTo(Integer value) {
            addCriterion("cancel_before_delivered >=", value, "cancelBeforeDelivered");
            return (Criteria) this;
        }

        public Criteria andCancelBeforeDeliveredLessThan(Integer value) {
            addCriterion("cancel_before_delivered <", value, "cancelBeforeDelivered");
            return (Criteria) this;
        }

        public Criteria andCancelBeforeDeliveredLessThanOrEqualTo(Integer value) {
            addCriterion("cancel_before_delivered <=", value, "cancelBeforeDelivered");
            return (Criteria) this;
        }

        public Criteria andCancelBeforeDeliveredIn(List<Integer> values) {
            addCriterion("cancel_before_delivered in", values, "cancelBeforeDelivered");
            return (Criteria) this;
        }

        public Criteria andCancelBeforeDeliveredNotIn(List<Integer> values) {
            addCriterion("cancel_before_delivered not in", values, "cancelBeforeDelivered");
            return (Criteria) this;
        }

        public Criteria andCancelBeforeDeliveredBetween(Integer value1, Integer value2) {
            addCriterion("cancel_before_delivered between", value1, value2, "cancelBeforeDelivered");
            return (Criteria) this;
        }

        public Criteria andCancelBeforeDeliveredNotBetween(Integer value1, Integer value2) {
            addCriterion("cancel_before_delivered not between", value1, value2, "cancelBeforeDelivered");
            return (Criteria) this;
        }

        public Criteria andTimeoutIsNull() {
            addCriterion("timeout is null");
            return (Criteria) this;
        }

        public Criteria andTimeoutIsNotNull() {
            addCriterion("timeout is not null");
            return (Criteria) this;
        }

        public Criteria andTimeoutEqualTo(Integer value) {
            addCriterion("timeout =", value, "timeout");
            return (Criteria) this;
        }

        public Criteria andTimeoutNotEqualTo(Integer value) {
            addCriterion("timeout <>", value, "timeout");
            return (Criteria) this;
        }

        public Criteria andTimeoutGreaterThan(Integer value) {
            addCriterion("timeout >", value, "timeout");
            return (Criteria) this;
        }

        public Criteria andTimeoutGreaterThanOrEqualTo(Integer value) {
            addCriterion("timeout >=", value, "timeout");
            return (Criteria) this;
        }

        public Criteria andTimeoutLessThan(Integer value) {
            addCriterion("timeout <", value, "timeout");
            return (Criteria) this;
        }

        public Criteria andTimeoutLessThanOrEqualTo(Integer value) {
            addCriterion("timeout <=", value, "timeout");
            return (Criteria) this;
        }

        public Criteria andTimeoutIn(List<Integer> values) {
            addCriterion("timeout in", values, "timeout");
            return (Criteria) this;
        }

        public Criteria andTimeoutNotIn(List<Integer> values) {
            addCriterion("timeout not in", values, "timeout");
            return (Criteria) this;
        }

        public Criteria andTimeoutBetween(Integer value1, Integer value2) {
            addCriterion("timeout between", value1, value2, "timeout");
            return (Criteria) this;
        }

        public Criteria andTimeoutNotBetween(Integer value1, Integer value2) {
            addCriterion("timeout not between", value1, value2, "timeout");
            return (Criteria) this;
        }

        public Criteria andDeliveredExcludeBookingIsNull() {
            addCriterion("delivered_exclude_booking is null");
            return (Criteria) this;
        }

        public Criteria andDeliveredExcludeBookingIsNotNull() {
            addCriterion("delivered_exclude_booking is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveredExcludeBookingEqualTo(Integer value) {
            addCriterion("delivered_exclude_booking =", value, "deliveredExcludeBooking");
            return (Criteria) this;
        }

        public Criteria andDeliveredExcludeBookingNotEqualTo(Integer value) {
            addCriterion("delivered_exclude_booking <>", value, "deliveredExcludeBooking");
            return (Criteria) this;
        }

        public Criteria andDeliveredExcludeBookingGreaterThan(Integer value) {
            addCriterion("delivered_exclude_booking >", value, "deliveredExcludeBooking");
            return (Criteria) this;
        }

        public Criteria andDeliveredExcludeBookingGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivered_exclude_booking >=", value, "deliveredExcludeBooking");
            return (Criteria) this;
        }

        public Criteria andDeliveredExcludeBookingLessThan(Integer value) {
            addCriterion("delivered_exclude_booking <", value, "deliveredExcludeBooking");
            return (Criteria) this;
        }

        public Criteria andDeliveredExcludeBookingLessThanOrEqualTo(Integer value) {
            addCriterion("delivered_exclude_booking <=", value, "deliveredExcludeBooking");
            return (Criteria) this;
        }

        public Criteria andDeliveredExcludeBookingIn(List<Integer> values) {
            addCriterion("delivered_exclude_booking in", values, "deliveredExcludeBooking");
            return (Criteria) this;
        }

        public Criteria andDeliveredExcludeBookingNotIn(List<Integer> values) {
            addCriterion("delivered_exclude_booking not in", values, "deliveredExcludeBooking");
            return (Criteria) this;
        }

        public Criteria andDeliveredExcludeBookingBetween(Integer value1, Integer value2) {
            addCriterion("delivered_exclude_booking between", value1, value2, "deliveredExcludeBooking");
            return (Criteria) this;
        }

        public Criteria andDeliveredExcludeBookingNotBetween(Integer value1, Integer value2) {
            addCriterion("delivered_exclude_booking not between", value1, value2, "deliveredExcludeBooking");
            return (Criteria) this;
        }

        public Criteria andDeliveredIn25MinsIsNull() {
            addCriterion("delivered_in_25_mins is null");
            return (Criteria) this;
        }

        public Criteria andDeliveredIn25MinsIsNotNull() {
            addCriterion("delivered_in_25_mins is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveredIn25MinsEqualTo(Integer value) {
            addCriterion("delivered_in_25_mins =", value, "deliveredIn25Mins");
            return (Criteria) this;
        }

        public Criteria andDeliveredIn25MinsNotEqualTo(Integer value) {
            addCriterion("delivered_in_25_mins <>", value, "deliveredIn25Mins");
            return (Criteria) this;
        }

        public Criteria andDeliveredIn25MinsGreaterThan(Integer value) {
            addCriterion("delivered_in_25_mins >", value, "deliveredIn25Mins");
            return (Criteria) this;
        }

        public Criteria andDeliveredIn25MinsGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivered_in_25_mins >=", value, "deliveredIn25Mins");
            return (Criteria) this;
        }

        public Criteria andDeliveredIn25MinsLessThan(Integer value) {
            addCriterion("delivered_in_25_mins <", value, "deliveredIn25Mins");
            return (Criteria) this;
        }

        public Criteria andDeliveredIn25MinsLessThanOrEqualTo(Integer value) {
            addCriterion("delivered_in_25_mins <=", value, "deliveredIn25Mins");
            return (Criteria) this;
        }

        public Criteria andDeliveredIn25MinsIn(List<Integer> values) {
            addCriterion("delivered_in_25_mins in", values, "deliveredIn25Mins");
            return (Criteria) this;
        }

        public Criteria andDeliveredIn25MinsNotIn(List<Integer> values) {
            addCriterion("delivered_in_25_mins not in", values, "deliveredIn25Mins");
            return (Criteria) this;
        }

        public Criteria andDeliveredIn25MinsBetween(Integer value1, Integer value2) {
            addCriterion("delivered_in_25_mins between", value1, value2, "deliveredIn25Mins");
            return (Criteria) this;
        }

        public Criteria andDeliveredIn25MinsNotBetween(Integer value1, Integer value2) {
            addCriterion("delivered_in_25_mins not between", value1, value2, "deliveredIn25Mins");
            return (Criteria) this;
        }

        public Criteria andDeliveryDurationIsNull() {
            addCriterion("delivery_duration is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDurationIsNotNull() {
            addCriterion("delivery_duration is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDurationEqualTo(Long value) {
            addCriterion("delivery_duration =", value, "deliveryDuration");
            return (Criteria) this;
        }

        public Criteria andDeliveryDurationNotEqualTo(Long value) {
            addCriterion("delivery_duration <>", value, "deliveryDuration");
            return (Criteria) this;
        }

        public Criteria andDeliveryDurationGreaterThan(Long value) {
            addCriterion("delivery_duration >", value, "deliveryDuration");
            return (Criteria) this;
        }

        public Criteria andDeliveryDurationGreaterThanOrEqualTo(Long value) {
            addCriterion("delivery_duration >=", value, "deliveryDuration");
            return (Criteria) this;
        }

        public Criteria andDeliveryDurationLessThan(Long value) {
            addCriterion("delivery_duration <", value, "deliveryDuration");
            return (Criteria) this;
        }

        public Criteria andDeliveryDurationLessThanOrEqualTo(Long value) {
            addCriterion("delivery_duration <=", value, "deliveryDuration");
            return (Criteria) this;
        }

        public Criteria andDeliveryDurationIn(List<Long> values) {
            addCriterion("delivery_duration in", values, "deliveryDuration");
            return (Criteria) this;
        }

        public Criteria andDeliveryDurationNotIn(List<Long> values) {
            addCriterion("delivery_duration not in", values, "deliveryDuration");
            return (Criteria) this;
        }

        public Criteria andDeliveryDurationBetween(Long value1, Long value2) {
            addCriterion("delivery_duration between", value1, value2, "deliveryDuration");
            return (Criteria) this;
        }

        public Criteria andDeliveryDurationNotBetween(Long value1, Long value2) {
            addCriterion("delivery_duration not between", value1, value2, "deliveryDuration");
            return (Criteria) this;
        }

        public Criteria andEarlyClickIsNull() {
            addCriterion("early_click is null");
            return (Criteria) this;
        }

        public Criteria andEarlyClickIsNotNull() {
            addCriterion("early_click is not null");
            return (Criteria) this;
        }

        public Criteria andEarlyClickEqualTo(Integer value) {
            addCriterion("early_click =", value, "earlyClick");
            return (Criteria) this;
        }

        public Criteria andEarlyClickNotEqualTo(Integer value) {
            addCriterion("early_click <>", value, "earlyClick");
            return (Criteria) this;
        }

        public Criteria andEarlyClickGreaterThan(Integer value) {
            addCriterion("early_click >", value, "earlyClick");
            return (Criteria) this;
        }

        public Criteria andEarlyClickGreaterThanOrEqualTo(Integer value) {
            addCriterion("early_click >=", value, "earlyClick");
            return (Criteria) this;
        }

        public Criteria andEarlyClickLessThan(Integer value) {
            addCriterion("early_click <", value, "earlyClick");
            return (Criteria) this;
        }

        public Criteria andEarlyClickLessThanOrEqualTo(Integer value) {
            addCriterion("early_click <=", value, "earlyClick");
            return (Criteria) this;
        }

        public Criteria andEarlyClickIn(List<Integer> values) {
            addCriterion("early_click in", values, "earlyClick");
            return (Criteria) this;
        }

        public Criteria andEarlyClickNotIn(List<Integer> values) {
            addCriterion("early_click not in", values, "earlyClick");
            return (Criteria) this;
        }

        public Criteria andEarlyClickBetween(Integer value1, Integer value2) {
            addCriterion("early_click between", value1, value2, "earlyClick");
            return (Criteria) this;
        }

        public Criteria andEarlyClickNotBetween(Integer value1, Integer value2) {
            addCriterion("early_click not between", value1, value2, "earlyClick");
            return (Criteria) this;
        }

        public Criteria andRiskControlIsNull() {
            addCriterion("risk_control is null");
            return (Criteria) this;
        }

        public Criteria andRiskControlIsNotNull() {
            addCriterion("risk_control is not null");
            return (Criteria) this;
        }

        public Criteria andRiskControlEqualTo(Integer value) {
            addCriterion("risk_control =", value, "riskControl");
            return (Criteria) this;
        }

        public Criteria andRiskControlNotEqualTo(Integer value) {
            addCriterion("risk_control <>", value, "riskControl");
            return (Criteria) this;
        }

        public Criteria andRiskControlGreaterThan(Integer value) {
            addCriterion("risk_control >", value, "riskControl");
            return (Criteria) this;
        }

        public Criteria andRiskControlGreaterThanOrEqualTo(Integer value) {
            addCriterion("risk_control >=", value, "riskControl");
            return (Criteria) this;
        }

        public Criteria andRiskControlLessThan(Integer value) {
            addCriterion("risk_control <", value, "riskControl");
            return (Criteria) this;
        }

        public Criteria andRiskControlLessThanOrEqualTo(Integer value) {
            addCriterion("risk_control <=", value, "riskControl");
            return (Criteria) this;
        }

        public Criteria andRiskControlIn(List<Integer> values) {
            addCriterion("risk_control in", values, "riskControl");
            return (Criteria) this;
        }

        public Criteria andRiskControlNotIn(List<Integer> values) {
            addCriterion("risk_control not in", values, "riskControl");
            return (Criteria) this;
        }

        public Criteria andRiskControlBetween(Integer value1, Integer value2) {
            addCriterion("risk_control between", value1, value2, "riskControl");
            return (Criteria) this;
        }

        public Criteria andRiskControlNotBetween(Integer value1, Integer value2) {
            addCriterion("risk_control not between", value1, value2, "riskControl");
            return (Criteria) this;
        }

        public Criteria andDtIsNull() {
            addCriterion("dt is null");
            return (Criteria) this;
        }

        public Criteria andDtIsNotNull() {
            addCriterion("dt is not null");
            return (Criteria) this;
        }

        public Criteria andDtEqualTo(Long value) {
            addCriterion("dt =", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotEqualTo(Long value) {
            addCriterion("dt <>", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThan(Long value) {
            addCriterion("dt >", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThanOrEqualTo(Long value) {
            addCriterion("dt >=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThan(Long value) {
            addCriterion("dt <", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThanOrEqualTo(Long value) {
            addCriterion("dt <=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtIn(List<Long> values) {
            addCriterion("dt in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotIn(List<Long> values) {
            addCriterion("dt not in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtBetween(Long value1, Long value2) {
            addCriterion("dt between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotBetween(Long value1, Long value2) {
            addCriterion("dt not between", value1, value2, "dt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}