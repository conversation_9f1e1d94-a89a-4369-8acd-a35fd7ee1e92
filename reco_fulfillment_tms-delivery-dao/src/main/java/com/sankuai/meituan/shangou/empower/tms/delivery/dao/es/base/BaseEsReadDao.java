package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.base;


import com.dianping.cat.Cat;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.exception.EsBaseException;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.exception.EsClientException;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils.ClassUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils.Counter;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils.MccUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils.ObjectMapperWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.IdsQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static org.elasticsearch.client.RequestOptions.DEFAULT;

/**
 * es读dao
 *
 * <AUTHOR>
 * @since 2022/3/31
 */
@SuppressWarnings("unused")
@Slf4j
public abstract class BaseEsReadDao<T extends BaseEsPo> {

    protected static final TimeValue SEARCH_TIMEOUT = TimeValue.timeValueNanos(10);

    protected static final TimeValue SEARCH_SCROLL_KEEP_ALIVE = TimeValue.timeValueSeconds(30);

    protected static final TimeValue AGG_SEARCH_TIMEOUT = TimeValue.timeValueSeconds(10);

    /**
     * search size限制
     */
    protected static final int SEARCH_SIZE_LIMIT = 100;

    /**
     * search scroll size限制
     */
    protected static final int SEARCH_SCROLL_SIZE_LIMIT = 500;

    /**
     * ES查询from + size默认限制10000
     */
    protected static final int DEFAULT_SEARCH_LIMIT = 10000;

    @Autowired
    protected RestHighLevelClient client;

    @Autowired
    protected ObjectMapperWrapper objectMapper;

    /**
     * 持久化模型类
     */
    private final Class<T> poClass;

    public BaseEsReadDao() {
        this.poClass = ClassUtils.getGenericType(this.getClass());
    }

    /**
     * 获取ES索引名，也可以是索引别名
     *
     * @return 一般情况下，返回常量值即可，如果是要动态地（比如根据日期）写入到某个索引中，可以根据其pattern生成相应的索引名
     */
    public abstract String getIndex();

    /**
     * 根据日期获取ES索引名称
     *
     * @return 默认返回 getIndex()对应的索引名称，如果自定义需要重写该方法
     */
    public String getIndex(LocalDateTime dateTime){
        return getIndex();
    }

    /**
     * 搜索
     *
     * @param queryBuilder 查询条件
     * @param routingValues 路由元数据
     * @return 返回10个文档
     */
    public SearchResult<T> search(QueryBuilder queryBuilder, String[] routingValues) {
        return search(queryBuilder, 10, routingValues);
    }

    /**
     * 搜索
     *
     * @param queryBuilder 查询条件
     * @param size         文档个数
     * @param routingValues 路由元数据
     * @return 搜索结果
     */
    public SearchResult<T> search(QueryBuilder queryBuilder, int size, String[] routingValues) {
        return search(queryBuilder, 0, size, routingValues);
    }

    /**
     * 根据id获取文档，相比get方法支持了路由值
     *
     * @param id 文档id
     * @return 文档
     */
    public GetResult<T> getV2(Long id, LocalDateTime indexKey, String routingValue) {
        return getV2(String.valueOf(id), indexKey, routingValue);
    }

    public GetResult<T> getV2(String id, LocalDateTime indexKey, String routingValue) {
        String index = getIndex(indexKey);
        if (!isIndexExists(index)) {
            log.warn("getV2, index not exists, index: {}", index);
            return GetResult.empty();
        }

        GetRequest request = getGetRequest(index, id, routingValue);
        try {
            GetResponse response = client.get(request, DEFAULT);
            if (response.isExists()) {
                return buildGetRequest(response);
            }
            log.warn("getV2, response is not exist, index: {}", index);
            return GetResult.empty();
        } catch (Exception e) {
            log.error("[{}]getV2 doc error: {}", index, e, e);
            throw new EsClientException(index, e);
        }
    }

    /**
     * 指定index是否存在
     * @param: index 索引名称
     * @return true代表存在
    */
    public boolean isIndexExists(String index) {
        GetIndexRequest indexRequest = new GetIndexRequest(index);
        boolean isExists;

        try {
            isExists = client.indices().exists(indexRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("[{}]isIndexExists error: {}", index, e, e);
            throw new EsClientException(index, e);
        }

        return isExists;
    }

    /**
     * 根据id获取文档
     *
     * @param id 文档id
     * @return 文档
     */
    public Optional<T> get(String id) {
       return get(id, null);
    }

    public Optional<T> get(String id, LocalDateTime indexKey) {
        GetRequest request = new GetRequest(indexKey != null
                ? getIndex(indexKey)
                : getIndex(),
                Objects.requireNonNull(id, "id must not be null"));
        try {
            GetResponse response = client.get(request, DEFAULT);
            if (response.isExists()) {
                return Optional.of(deserialize(response.getSourceAsString()));
            }
            return Optional.empty();
        } catch (IOException e) {
            log.error("[{}]Get doc error: {}", getIndex(), e, e);
            throw new EsClientException(getIndex(), e);
        }
    }

    public Optional<T> get(long id) {
        return get(String.valueOf(id));
    }

    public List<T> getByIds2(List<Long> ids, String[] routingValues) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<String> docIds = ids.stream()
                .map(String::valueOf).collect(Collectors.toList());
        return getByIds(docIds, routingValues);
    }

    /**
     * 根据id获取文档
     *
     * @param ids 批量id，限制最多{@link #SEARCH_SIZE_LIMIT}个，防止慢查询
     * @param: routingValues 路由值，如果传null则不走路由
     * @return 批量文档
     */
    public List<T> getByIds(List<String> ids, String[] routingValues) {
        Assert.notEmpty(ids, "ids mut not be empty");
        if (ids.size() > SEARCH_SIZE_LIMIT) {
            throw new EsBaseException(getIndex(), "ids' size cannot > " + SEARCH_SIZE_LIMIT
                    + ", part ids before searching");
        }
        String[] stringIds = ids.toArray(new String[0]);
        IdsQueryBuilder queryBuilder = QueryBuilders.idsQuery().addIds(stringIds);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(queryBuilder);
        return search(searchSourceBuilder, routingValues).getData();
    }

    /**
     * 获取指定的文档元数据
     * @param id 文档ID
     * @param index 索引
     * @param includes 指定获取的元数据字段
     * @return 文档元数据
     */
    public GetResponse getSourceContext(String id, String index, String[] includes, String routingValue) {
        GetRequest request = getGetRequest(index, id, routingValue).fetchSourceContext(new FetchSourceContext(true, includes, null));
        try {
            return client.get(request, DEFAULT);
        } catch (IOException e) {
            log.error("[{}]getSourceContext error: {}", getIndex(), e, e);
            throw new EsClientException(getIndex(), e);
        }
    }

    public SearchResult<T> search(QueryBuilder queryBuilder, String[] includeFields, String[] excludeFields, int from, int size, String[] routingValues) {
        Assert.notNull(queryBuilder, "queryBuilder must not be null");
        if (from + size > DEFAULT_SEARCH_LIMIT) {
            throw new EsBaseException(getIndex(), "search from + size must <= " + DEFAULT_SEARCH_LIMIT
                    + ", use SearchScroll instead");
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(queryBuilder)
                .fetchSource(includeFields, excludeFields)
                .from(from)
                .size(size);
        return search(searchSourceBuilder, routingValues);
    }

    public SearchResult<T> search(QueryBuilder queryBuilder, int from, int size, String[] routingValues) {
        Assert.notNull(queryBuilder, "queryBuilder must not be null");
        if (from + size > DEFAULT_SEARCH_LIMIT) {
            throw new EsBaseException(getIndex(), "search from + size must <= " + DEFAULT_SEARCH_LIMIT
                    + ", use SearchScroll instead");
        }
        if (size > SEARCH_SIZE_LIMIT) {
            throw new EsBaseException(getIndex(), "search size must <= " + SEARCH_SIZE_LIMIT);
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(queryBuilder)
                .from(from)
                .size(size);
        return search(searchSourceBuilder, routingValues);
    }

    public SearchResult<T> search(SearchSourceBuilder search, String[] routingValues) {
        String index = getIndex();
        log.info("[{}]Search SearchSourceBuilder: {}", index, search);
        Assert.notNull(search, "searchSourceBuilder must not be null");
        search.timeout(SEARCH_TIMEOUT);
        SearchRequest searchRequest = getSearchRequest(index, search, routingValues);
        try {
            SearchResponse searchResponse = client.search(searchRequest, DEFAULT);
            return buildSearchResult(searchResponse);
        } catch (Exception e) {
            log.error("[{}]Eagle search error: {}", index, e, e);
            throw new EsClientException(index, e.toString());
        }
    }

    /**
     * 滚动搜索，数量默认100
     *
     * @param queryBuilder 查询条件
     * @param consumer     消费查询结果
     */
    public void searchScroll(QueryBuilder queryBuilder, Consumer<List<T>> consumer) {
        this.searchScroll(queryBuilder, 100, consumer);
    }

    /**
     * 滚动搜索
     *
     * @param queryBuilder 查询条件
     * @param size         一次查询的数量
     * @param consumer     消费查询结果
     */
    public void searchScroll(QueryBuilder queryBuilder, int size, Consumer<List<T>> consumer) {
        Objects.requireNonNull(consumer, "consumer must not be null");
        String index = getIndex();
        if (size > SEARCH_SCROLL_SIZE_LIMIT) {
            throw new EsBaseException(index, "search scroll size must <= " + SEARCH_SCROLL_SIZE_LIMIT);
        }

        Counter counter = new Counter();
        String scrollId = null;

        try {
            SearchResponse searchResponse = initialSearchScroll(queryBuilder, size);
            scrollId = searchResponse.getScrollId();
            SearchResult<T> result = buildSearchResult(searchResponse);
            if (result.isEmpty()) {
                return;
            }
            counter.count(result.getData().size());
            consumer.accept(result.getData());
            int count=0;
            int max= MccUtils.maxWhileNum();
            while (true) {
                result = searchScrollById(scrollId);
                if (result.isEmpty()) {
                    break;
                }
                if(count>=max){
                    Cat.logEvent("while.break.cat","BaseEsReadDao.searchScroll");
                    break;
                }
                consumer.accept(result.getData());
                counter.count(result.getData().size());
                count++;
            }
        } catch (IOException e) {
            log.error("[{}]Search scroll error: {}", index, e, e);
            throw new EsBaseException(index, e);
        } finally {
            clearScroll(scrollId);
            counter.finish();
            log.info("[{}]Search scroll finished: {}", index, counter);
        }
    }

    /**
     * 初始化Search Scroll
     */
    private SearchResponse initialSearchScroll(QueryBuilder queryBuilder, int size) throws IOException {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(Objects.requireNonNull(queryBuilder, "queryBuilder must not be null"))
                .timeout(SEARCH_TIMEOUT)
                .size(size);
        log.info("[{}]Search scroll SearchSourceBuilder: {}", getIndex(), searchSourceBuilder);
        SearchRequest searchRequest = new SearchRequest(getIndex())
                .source(searchSourceBuilder)
                .scroll(SEARCH_SCROLL_KEEP_ALIVE);
        return client.search(searchRequest, DEFAULT);
    }

    /**
     * 根据scrollId获取剩下的文档
     */
    private SearchResult<T> searchScrollById(String scrollId) throws IOException {
        SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId)
                .scroll(SEARCH_SCROLL_KEEP_ALIVE);
        SearchResponse response = client.scroll(scrollRequest, DEFAULT);
        return buildSearchResult(response);
    }

    private void clearScroll(String scrollId) {
        if (scrollId == null) {
            return;
        }
        ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
        clearScrollRequest.addScrollId(scrollId);
        try {
            client.clearScroll(clearScrollRequest, DEFAULT);
        } catch (Exception e) {
            log.error("[{}]Clear scroll error: {}", getIndex(), e, e);
        }
    }

    private SearchResult<T> buildSearchResult(SearchResponse searchResponse) {
        SearchHits searchHits = searchResponse.getHits();
        int totalHits = (int) searchHits.getTotalHits().value;
        SearchHit[] hits = searchHits.getHits();
        log.info("[{}]Total hits: {}, hits: {}", getIndex(), totalHits, hits.length);
        if (totalHits == 0 || hits.length == 0) {
            return SearchResult.empty();
        }

        List<T> data = Arrays.stream(hits)
                .map(hit -> {
                    T po = deserialize(hit.getSourceAsString());
                    po.setId(hit.getId());
                    po.setSortValues(hit.getSortValues());
                    po.setVersion(hit.getVersion());
                    po.setSeqNo(hit.getSeqNo());
                    po.setPrimaryTerm(hit.getPrimaryTerm());
                    return po;
                })
                .collect(Collectors.toList());
        return new SearchResult<>(totalHits, data);
    }

    private GetResult<T> buildGetRequest(GetResponse getResponse) {
        T data = deserialize(getResponse.getSourceAsString());
        data.setId(getResponse.getId());
        data.setSeqNo(getResponse.getSeqNo());
        data.setPrimaryTerm(getResponse.getPrimaryTerm());
        data.setVersion(getResponse.getVersion());

        return new GetResult<>(getResponse.getIndex(), getResponse.getId(), data);
    }

    /**
     * @description: 带路由值统计符合条件的文档数量
     * @param: queryBuilder 查询条件
     * @param: routingValues 路由值，如果传null则不走路由
     * @return 数量
    */
    public Long count(QueryBuilder queryBuilder, String[] routingValues) {
        String index = getIndex();
        CountRequest countRequest = getCountRequest(index, queryBuilder, routingValues);

        try {
            CountResponse countResponse = client.count(countRequest, DEFAULT);
            if (Objects.isNull(countResponse)) {
                log.error("[{}]Eagle countWithRouting countResponse is null", index);
                throw new EsClientException(index, "countWithRouting countResponse is null");
            }
            return countResponse.getCount();
        } catch (Exception e) {
            log.error("[{}]Eagle countWithRouting error: {}", index, e, e);
            throw new EsClientException(index, e.toString());
        }
    }

    public Aggregations searchAggregations(SearchSourceBuilder search, LocalDateTime indexKey, String[] routingValues) {
        String index = getIndex(indexKey);
        log.info("[{}]Search SearchSourceBuilder: {}", index, search);
        Assert.notNull(search, "searchSourceBuilder must not be null");
        search.timeout(AGG_SEARCH_TIMEOUT);
        SearchRequest searchRequest = getSearchRequest(index, search, routingValues);

        try {
            SearchResponse searchResponse = client.search(searchRequest, DEFAULT);
            return searchResponse.getAggregations();
        } catch (Exception e) {
            log.error("[{}]Eagle agg search error", index, e);
            throw new EsClientException(index, e.toString());
        }
    }

    public Map<String, List<Terms.Bucket>> aggSearch(SearchSourceBuilder search, LocalDateTime indexKey, String[] routingValues) {
        String index = getIndex(indexKey);
        log.info("[{}]Search SearchSourceBuilder: {}", index, search);
        Map<String, List<Terms.Bucket>> bucketsMap = new HashMap<>();
        Assert.notNull(search, "searchSourceBuilder must not be null");
        search.timeout(AGG_SEARCH_TIMEOUT);
        Aggregations aggregations = searchAggregations(search, indexKey, routingValues);
        SearchRequest searchRequest = getSearchRequest(index, search, routingValues);

        if (aggregations == null) {
            return bucketsMap;
        }
        List<Aggregation> allAgg = aggregations.asList();
        allAgg.forEach(agg -> {
            if (!(agg instanceof Terms)) {
                return;
            }
            Terms terms = (Terms) agg;
            List<? extends Terms.Bucket> buckets = terms.getBuckets();
            if (CollectionUtils.isEmpty(buckets)) {
                return;
            }
            bucketsMap.put(terms.getName(), new ArrayList<>(buckets));
        });
        return bucketsMap;
    }

    private SearchRequest getSearchRequest(String index, SearchSourceBuilder search, String[] routingValues) {
        if (ArrayUtils.isNotEmpty(routingValues)) {
            return new SearchRequest(index).routing(routingValues).source(search).indicesOptions(IndicesOptions.fromOptions(true, true, true, false));
        } else {
            return new SearchRequest(index).source(search).indicesOptions(IndicesOptions.fromOptions(true, true, true, false));
        }
    }

    private GetRequest getGetRequest(String index, String id, String routingValue) {
        if (StringUtils.isNotEmpty(routingValue)) {
            return new GetRequest(index, Objects.requireNonNull(id, "id must not be null")).routing(routingValue);
        } else {
            return new GetRequest(index, Objects.requireNonNull(id, "id must not be null"));
        }
    }

    private CountRequest getCountRequest(String index, QueryBuilder queryBuilder, String[] routingValues) {
        if (ArrayUtils.isNotEmpty(routingValues)) {
            return new CountRequest(index).query(queryBuilder).routing(routingValues);
        } else {
            return new CountRequest(index).query(queryBuilder);
        }
    }

    protected T deserialize(String source) {
        try {
            return objectMapper.readValue(source, poClass);
        } catch (Exception e) {
            log.error("deserialize json error, source: {}, msg: {}", source, e.getMessage(), e);
            throw new EsBaseException(getIndex(), e);
        }
    }

}
