package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.time.LocalDateTime;

public class ShopDeliveryConfigDO {
    private Long id;

    private Long tenantId;

    private Long shopId;

    private Integer deliveryChannelId;

    private String deliveryChannelPoiCode;

    private String deliveryChannelPoiName;

    private String extData;

    private Integer enabled;

    private Long operatorId;

    private String operatorName;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer isDel;

    private String deliveryServiceCodes;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getDeliveryChannelId() {
        return deliveryChannelId;
    }

    public void setDeliveryChannelId(Integer deliveryChannelId) {
        this.deliveryChannelId = deliveryChannelId;
    }

    public String getDeliveryChannelPoiCode() {
        return deliveryChannelPoiCode;
    }

    public void setDeliveryChannelPoiCode(String deliveryChannelPoiCode) {
        this.deliveryChannelPoiCode = deliveryChannelPoiCode == null ? null : deliveryChannelPoiCode.trim();
    }

    public String getDeliveryChannelPoiName() {
        return deliveryChannelPoiName;
    }

    public void setDeliveryChannelPoiName(String deliveryChannelPoiName) {
        this.deliveryChannelPoiName = deliveryChannelPoiName == null ? null : deliveryChannelPoiName.trim();
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData == null ? null : extData.trim();
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName == null ? null : operatorName.trim();
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public String getDeliveryServiceCodes() {
        return deliveryServiceCodes;
    }

    public void setDeliveryServiceCodes(String deliveryServiceCodes) {
        this.deliveryServiceCodes = deliveryServiceCodes == null ? null : deliveryServiceCodes.trim();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ShopDeliveryConfigDO other = (ShopDeliveryConfigDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()))
            && (this.getShopId() == null ? other.getShopId() == null : this.getShopId().equals(other.getShopId()))
            && (this.getDeliveryChannelId() == null ? other.getDeliveryChannelId() == null : this.getDeliveryChannelId().equals(other.getDeliveryChannelId()))
            && (this.getDeliveryChannelPoiCode() == null ? other.getDeliveryChannelPoiCode() == null : this.getDeliveryChannelPoiCode().equals(other.getDeliveryChannelPoiCode()))
            && (this.getDeliveryChannelPoiName() == null ? other.getDeliveryChannelPoiName() == null : this.getDeliveryChannelPoiName().equals(other.getDeliveryChannelPoiName()))
            && (this.getExtData() == null ? other.getExtData() == null : this.getExtData().equals(other.getExtData()))
            && (this.getEnabled() == null ? other.getEnabled() == null : this.getEnabled().equals(other.getEnabled()))
            && (this.getOperatorId() == null ? other.getOperatorId() == null : this.getOperatorId().equals(other.getOperatorId()))
            && (this.getOperatorName() == null ? other.getOperatorName() == null : this.getOperatorName().equals(other.getOperatorName()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getIsDel() == null ? other.getIsDel() == null : this.getIsDel().equals(other.getIsDel()))
            && (this.getDeliveryServiceCodes() == null ? other.getDeliveryServiceCodes() == null : this.getDeliveryServiceCodes().equals(other.getDeliveryServiceCodes()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        result = prime * result + ((getShopId() == null) ? 0 : getShopId().hashCode());
        result = prime * result + ((getDeliveryChannelId() == null) ? 0 : getDeliveryChannelId().hashCode());
        result = prime * result + ((getDeliveryChannelPoiCode() == null) ? 0 : getDeliveryChannelPoiCode().hashCode());
        result = prime * result + ((getDeliveryChannelPoiName() == null) ? 0 : getDeliveryChannelPoiName().hashCode());
        result = prime * result + ((getExtData() == null) ? 0 : getExtData().hashCode());
        result = prime * result + ((getEnabled() == null) ? 0 : getEnabled().hashCode());
        result = prime * result + ((getOperatorId() == null) ? 0 : getOperatorId().hashCode());
        result = prime * result + ((getOperatorName() == null) ? 0 : getOperatorName().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDel() == null) ? 0 : getIsDel().hashCode());
        result = prime * result + ((getDeliveryServiceCodes() == null) ? 0 : getDeliveryServiceCodes().hashCode());
        return result;
    }
}