package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils;

import javax.annotation.concurrent.ThreadSafe;
import java.time.Duration;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Counter
 *
 * <AUTHOR>
 * @since 2022/4/14
 */
@ThreadSafe
public class Counter {

    private final AtomicInteger count;

    private final AtomicInteger times;

    private final long startMill;

    private Duration duration;

    private volatile boolean finish;

    public Counter() {
        count = new AtomicInteger();
        times = new AtomicInteger();
        startMill = System.currentTimeMillis();
        finish = false;
    }

    public Counter count() {
        if (!finish) {
            count.incrementAndGet();
            times.incrementAndGet();
        }
        return this;
    }

    public Counter count(int num) {
        if (!finish) {
            count.addAndGet(num);
            times.incrementAndGet();
        }
        return this;
    }

    public void finish() {
        if (finish) {
            return;
        }
        finish = true;
        duration = Duration.ofMillis(System.currentTimeMillis() - startMill);
    }

    @Override
    public String toString() {
        return "count=" + count + ", times=" + times + ", duration=" + duration;
    }

}
