package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.CountGroupExDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/8/30 16:24
 **/
public interface DeliveryOrderDOExMapper {
    List<CountGroupExDO> countThirdDeliveryOrderGroupByStatus(@Param("tenantId") Long tenantId, @Param("storeId") Long storeId,
                                                        @Param("statusList") List<Integer> statusList);
}
