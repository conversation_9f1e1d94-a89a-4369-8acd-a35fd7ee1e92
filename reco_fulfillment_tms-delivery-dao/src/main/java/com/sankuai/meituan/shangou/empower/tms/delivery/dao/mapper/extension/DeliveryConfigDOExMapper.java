package com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryConfigDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mybatis.SlaveQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/10
 */
public interface DeliveryConfigDOExMapper {

	int batchUpdate(@Param("recordList") List<DeliveryConfigDO> recordList);

	int batchInsert(@Param("recordList")List<DeliveryConfigDO> recordList);

	@SlaveQuery
	List<DeliveryConfigDO> selectByExampleSlave(DeliveryConfigDOExample example);
}
