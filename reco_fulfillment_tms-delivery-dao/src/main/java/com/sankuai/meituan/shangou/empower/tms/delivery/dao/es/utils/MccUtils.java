package com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils;

import com.dianping.lion.client.Lion;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class MccUtils {

    /**
     * 判断运单超时的小时数，当超过预计送达时间x小时，判定为超时
     */
    public static Integer getHours4JudgeDeliveryOrderTimeout() {
        try {
            return ConfigUtilAdapter.getInt("hours_to_judge_timeout", 4);
        } catch (Exception e) {
            log.error("MCC[hours_to_judge_timeout] parse error", e);
            return 4;
        }
    }

    public static Integer getDeliveryOrderCrossMonthNumber() {
        try {
            return ConfigUtilAdapter.getInt("delivery_order_cross_month_number", 1);
        } catch (Exception e) {
            log.error("MCC[delivery_order_cross_month_number] parse error", e);
            return 4;
        }
    }

    public static boolean notConsumeOpenApiMessage() {
        return ConfigUtilAdapter.getBoolean("consume_open_api_message", false);
    }

    public static boolean getArrivalLocationSwitch() {
        return Lion.getConfigRepository().getBooleanValue("arrival.location.switch", false);
    }

    /**
     * DB批量查询原始运单号批量大小
     */
    public static int queryOriginWaybillNoSourceESLimit() {
        return Lion.getConfigRepository().getIntValue("query.originwaybillno.source.es.limit", 500);
    }

    public static int maxWhileNum(){
        return Lion.getConfigRepository().getIntValue("tms.max.while.num",1000);
    }

}
