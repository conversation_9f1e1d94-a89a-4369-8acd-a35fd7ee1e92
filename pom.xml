<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xframe-starter-parent</artifactId>
        <groupId>com.meituan.xframe</groupId>
        <!--XFrame产品版本：https://km.sankuai.com/custom/onecloud/page/133516477-->
        <version>2.6.7.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sankuai.shangou.logistics</groupId>
    <artifactId>sdms</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>sdms</name>

    <properties>
        <version.type>-SNAPSHOT</version.type>
        <revision>1.0.8</revision>
    </properties>

     <modules>
        <module>sdms-start</module>
        <module>sdms-infrastructure</module>
        <module>sdms-sdk</module>
        <module>sdms-application</module>
        <module>sdms-domain</module>
        <module>sdms-dao</module>
        <module>sdms-server</module>
    </modules>

    <dependencies>
        <!-- log start -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.26</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-web</artifactId>
            <version>2.17.1</version>
        </dependency>

        <!-- adapter log4j -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
            <version>1.7.12</version>
        </dependency>
        <!-- adapter commons-logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
            <version>1.7.12</version>
        </dependency>
        <!-- log end -->
        <!--异常框架-->
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-thrift-publisher</artifactId>
            <version>2.7.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>thrift-xframe-boot-starter</artifactId>
                    <groupId>com.meituan.xframe</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-exception-collector</artifactId>
            <version>2.6.1</version>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!--内部依赖-->
            <dependency>
                <groupId>com.sankuai.shangou.logistics</groupId>
                <artifactId>sdms-dao</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou.logistics</groupId>
                <artifactId>sdms-infrastructure</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou.logistics</groupId>
                <artifactId>sdms-domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou.logistics</groupId>
                <artifactId>sdms-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou.logistics</groupId>
                <artifactId>sdms-sdk</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou.logistics</groupId>
                <artifactId>sdms-server</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.28</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>shangou-thrift-augment</artifactId>
                <version>2.6.1</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile</groupId>
                <artifactId>mtthrift</artifactId>
                <version>2.5.9-annotation-extend</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.18</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
                <artifactId>reco_fulfillment_tms-rider-delivery-client</artifactId>
                <version>2.2.58</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.infra</groupId>
                <artifactId>osw-api</artifactId>
                <version>2.0.3</version>
            </dependency>

            <!-- 公共utils包 -->
            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>shangou-common-utils</artifactId>
                <version>2.8.14</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.rc.yoda</groupId>
                <artifactId>yoda-client</artifactId>
                <version>1.5.19</version>
            </dependency>

            <!-- 钱包产品提供的 签名和加密工具 SignAndEncUtil -->
            <dependency>
                <groupId>com.meituan.pay</groupId>
                <artifactId>mwalletSdk</artifactId>
                <version>1.0.9-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.bizmng</groupId>
                <artifactId>labor-api</artifactId>
                <version>1.0.12</version>
            </dependency>

            <dependency>
                <artifactId>reco_store_saas_tenant_client</artifactId>
                <groupId>com.meituan.shangou.saas</groupId>
                <version>3.3.65</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.sgxsupply.wxmall</groupId>
                <artifactId>bizmanagement-client</artifactId>
                <version>2.14.3-bdorder</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.waima</groupId>
                <artifactId>support-api</artifactId>
                <version>1.0.39</version>
            </dependency>

            <!-- 人脸图片解密相关 start -->
            <dependency>
                <groupId>com.meituan.service.inf</groupId>
                <artifactId>kms-pangolin-sdk</artifactId>
                <version>0.8.10</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.inf</groupId>
                <artifactId>idl-kms</artifactId>
                <version>1.4.4.1</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.service.inf</groupId>
                <artifactId>kms-java-client</artifactId>
                <version>0.12.12.2</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.inf</groupId>
                <artifactId>kms-tls-sdk</artifactId>
                <version>0.3.5.1</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>1.60</version>
            </dependency>

            <!-- 人脸图片解密相关 end -->

            <dependency>
                <groupId>com.sankuai.shangou.qnh.ofc</groupId>
                <artifactId>qnh_ofc_ofw-client</artifactId>
                <version>1.0.4</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>1.2.4.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.shangou</groupId>
                <artifactId>reco_store_saas_message_management_client</artifactId>
                <version>2.0.75</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.qnh.fulfill</groupId>
                <artifactId>pick-select-query-idl</artifactId>
                <version>1.0.38-drunk-horse-fusion-SNAPSHOT</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <!--<version>1.2.2</version>-->
                <configuration></configuration>
                <executions>
                    <!-- enable flattening -->
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <!-- ensure proper cleanup -->
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <compilerArgument>-parameters</compilerArgument>
                    <encoding>UTF-8</encoding>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.sankuai.inf</groupId>
                <artifactId>xmdlog-maven-plugin</artifactId>
                <version>1.1.6</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <phase>compile</phase>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.sankuai.inf</groupId>
                <artifactId>kms-maven-plugin</artifactId>
                <version>0.0.7</version>
                <executions>
                    <execution>
                        <id>run</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>check-kms-java-client-version</goal>
                            <goal>check-bc-artifactId</goal>
                            <goal>check-http-components-version</goal>
                            <goal>check-idl-kms-version</goal>
                            <goal>check-commons-codec-version</goal>
                            <goal>check-guava-version</goal>
                            <goal>check-kms-pangolin-sdk-version</goal>
                            <goal>check-kms-tls-sdk-version</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>staging</id>
            <properties>
                <version.type/>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <version.type/>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <version.type>-SNAPSHOT</version.type>
            </properties>
        </profile>
        <profile>
            <id>local</id>
            <properties>
                <version.type>-SNAPSHOT</version.type>
            </properties>
        </profile>
    </profiles>
</project>
