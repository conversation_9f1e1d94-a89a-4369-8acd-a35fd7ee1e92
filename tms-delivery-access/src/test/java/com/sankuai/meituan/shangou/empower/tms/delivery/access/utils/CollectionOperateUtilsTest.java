package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.CollectionOperateUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/12
 */
public class CollectionOperateUtilsTest {

    @Test
    public void filterValueList() {
        Map<String, String> stringStringMap = CollectionOperateUtils.filterValueList(buildMap(), list -> list.get(0));
        Assert.assertTrue(stringStringMap.size() == 2);
        Assert.assertTrue(stringStringMap.get("2").equals("1"));
        Assert.assertTrue(stringStringMap.get("3").equals("3"));
    }

    private Map<String, List<String>> buildMap() {

        Map<String, List<String>> map = Maps.newHashMap();
        map.put("1", Lists.newArrayList());
        map.put("2", Lists.newArrayList("1", "2", "3"));
        map.put("3", Lists.newArrayList("3", "2", "1"));
        return map;
    }
}
