package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/23
 */
public class RiderCoordinateUtilTest {

	@Test
	public void should_calculateZeroDistance() {
		CoordinatePoint sourcePoint = new CoordinatePoint("104.057651", "30.55985");
		CoordinatePoint targetPoint = new CoordinatePoint("104.057774", "30.559333");
		Long lineDistance = com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.CoordinateUtil.calLineDistance(sourcePoint, targetPoint);
		Assert.assertTrue(lineDistance != null);
		Assert.assertTrue(lineDistance < 100);
	}

	@Test
	public void should_calculateOneLatDistance() {
		CoordinatePoint sourcePoint = new CoordinatePoint("104", "30");
		CoordinatePoint targetPoint = new CoordinatePoint("104", "31");
		Long lineDistance = com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.CoordinateUtil.calLineDistance(sourcePoint, targetPoint);
		Assert.assertTrue(lineDistance != null);
		Assert.assertTrue(Math.abs(lineDistance - 111319) < 10);
	}

	@Test
	public void should_calculateOneLngDistance() {
		CoordinatePoint sourcePoint = new CoordinatePoint("105", "0");
		CoordinatePoint targetPoint = new CoordinatePoint("104", "0");
		Long lineDistance = com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.CoordinateUtil.calLineDistance(sourcePoint, targetPoint);
		Assert.assertTrue(lineDistance != null);
		Assert.assertTrue(Math.abs(lineDistance - 111319) < 10);
	}

	@Test
	public void should_calculateOneLngWhenLat60Distance() {
		CoordinatePoint sourcePoint = new CoordinatePoint("105", "60");
		CoordinatePoint targetPoint = new CoordinatePoint("104", "60");
		Long lineDistance = com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.CoordinateUtil.calLineDistance(sourcePoint, targetPoint);
		Assert.assertTrue(lineDistance != null);
		Assert.assertTrue(Math.abs(lineDistance - (111319/2)) < 10);
	}

	@Test
	public void should_translateToStandardStyle_success() {
		assertEquals("104.101463", CoordinateUtil.translateToStandardStyle("104.101463"));
		assertEquals("104.101463", CoordinateUtil.translateToStandardStyle("104101463"));
	}

	@Test
	public void should_translateFromBaiduToMars_success() {
		CoordinatePoint coordinatePoint = CoordinateUtil.translateFromBaiduToMars(new CoordinatePoint("104.072651", "30.573898"));
		assertEquals("104.066188", coordinatePoint.getLongitude());
		assertEquals("30.567841", coordinatePoint.getLatitude());
	}
}
