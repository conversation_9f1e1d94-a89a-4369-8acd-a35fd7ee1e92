package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MoneyUtil;
import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/20
 */
public class MoneyUtilTest {

    @Test
    public void fromCentToYuan() {
        double a = 0.1d;
        BigDecimal bigDecimal = MoneyUtil.fromYuanToCenter(BigDecimal.valueOf(a));
        double doubleValue = bigDecimal.doubleValue();
        Assert.assertNotNull(doubleValue);
    }
}
