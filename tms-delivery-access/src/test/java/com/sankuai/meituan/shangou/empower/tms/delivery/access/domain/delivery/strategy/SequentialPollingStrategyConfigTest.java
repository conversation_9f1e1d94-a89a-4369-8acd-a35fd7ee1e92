package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.delivery.strategy;

import com.google.common.collect.ImmutableList;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SequentialPollingStrategyConfig;
import org.junit.Test;

import java.util.ArrayList;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/23
 */
public class SequentialPollingStrategyConfigTest {

	@Test
	public void should_hasNextAvailableDeliveryChannel_return_false_with_no_available_channels() {
		assertFalse(
				new SequentialPollingStrategyConfig(new ArrayList<>(), 15)
						.hasNextAvailableDeliveryChannel(ThirdDeliveryChannelEnum.FENG_NIAO_DELIVERY)
		);
	}

	@Test
	public void should_hasNextAvailableDeliveryChannel_return_false_with_current_channel_not_configured() {
		assertFalse(
				new SequentialPollingStrategyConfig(ImmutableList.of(ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY), 15)
						.hasNextAvailableDeliveryChannel(ThirdDeliveryChannelEnum.FENG_NIAO_DELIVERY)
		);
	}

	@Test
	public void should_hasNextAvailableDeliveryChannel_return_false_with_current_channel_is_last() {
		assertFalse(
				new SequentialPollingStrategyConfig(ImmutableList.of(ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY, ThirdDeliveryChannelEnum.FENG_NIAO_DELIVERY), 15)
						.hasNextAvailableDeliveryChannel(ThirdDeliveryChannelEnum.FENG_NIAO_DELIVERY)
		);
	}

	@Test
	public void should_hasNextAvailableDeliveryChannel_return_true() {
		assertTrue(
				new SequentialPollingStrategyConfig(ImmutableList.of(ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY, ThirdDeliveryChannelEnum.FENG_NIAO_DELIVERY), 15)
						.hasNextAvailableDeliveryChannel(ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY)
		);
	}
}
