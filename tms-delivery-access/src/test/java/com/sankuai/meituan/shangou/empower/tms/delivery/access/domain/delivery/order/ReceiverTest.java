package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.delivery.order;

import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import org.junit.Test;
import org.mockito.Mockito;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/23
 */
public class ReceiverTest {

	@Test
	public void should_isDeliverable_success() {
		Address mockAddress = Mockito.mock(Address.class);
		when(mockAddress.isValid()).thenReturn(false);
		assertFalse(new Receiver("receiver_name", "receiver_phone", "receiver_privacy_phone", mockAddress).isDeliverable());

		when(mockAddress.isValid()).thenReturn(true);
		assertTrue(new Receiver("receiver_name", "receiver_phone", "receiver_privacy_phone", mockAddress).isDeliverable());
	}
}
