package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.delivery.store;

import com.google.common.collect.ImmutableList;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ChannelStoreConfig;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/23
 */
public class ChannelStoreConfigTest {

	@Test
	public void should_getOrderedServicePackageCodes_success() {
		ChannelStoreConfig channelStoreConfig = new ChannelStoreConfig(1L, 2L, ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY, "poi_code", "poi_name", ImmutableList.of("4013", "4002", "1"), true);
		List<String> orderedServicePackageCodes = channelStoreConfig.getOrderedServicePackageCodes();
		assertEquals(2, orderedServicePackageCodes.size());
		assertEquals("4002", orderedServicePackageCodes.get(0));
		assertEquals("4013", orderedServicePackageCodes.get(1));
	}
}
