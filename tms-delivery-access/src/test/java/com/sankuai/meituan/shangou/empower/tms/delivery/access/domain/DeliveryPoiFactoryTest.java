package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStrategyEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.BookingOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiFactory;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.aggr.AggrDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.maltfarm.MaltFarmDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.merchant.MerchantSelfDeliveryPoi;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum.FENG_NIAO_DELIVERY;
import static com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY;
import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/29
 */
@RunWith(MockitoJUnitRunner.class)
public class DeliveryPoiFactoryTest {

	private DeliveryPoiFactory deliveryPoiFactory = new DeliveryPoiFactory();

	@Mock
	private TenantSystemClient tenantSystemClient;

	@Before
	public void setUp() {
//		MockInjectUtils.injectField("tenantSystemClient", tenantSystemClient, deliveryPoiFactory);
//
//		TenantStoreInfo storeInfo = Mockito.mock(TenantStoreInfo.class);
//		when(storeInfo.getCityCode()).thenReturn(999);
//		when(storeInfo.getPhone()).thenReturn("13812345678");
//		when(tenantSystemClient.queryStoreDetailInfo(1L, 2L)).thenReturn(storeInfo);
	}

	@Test
	public void should_createDefaultDeliveryPoi_success() {
//		assertEquals("SelfBuiltDeliveryPoi(super=DeliveryPoi(id=null, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=SELF_BUILT_DELIVERY_PLATFORM, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=MERCHANT_ACCEPT, delayMinutes=0), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=BEFORE_DELIVERY, configMinutes=60)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY), channelTenantConfigMap={}, channelStoreConfigMap={}, orderPlatformDeliveryConfigMap={}, storeAddress=null, deliveryStrategy=SEQUENTIAL_POLLING, deliveryStrategyConfig=SequentialPollingStrategyConfig(super=DeliveryStrategyConfig(), orderedDeliveryChannels=[], timeoutForShiftDeliveryChannelInMinutes=15))", deliveryPoiFactory.createDefaultDeliveryPoi(DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM, 1L, 2L).toString());
//		assertEquals("AggrDeliveryPoi(super=DeliveryPoi(id=null, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=AGGREGATION_DELIVERY_PLATFORM, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=MERCHANT_ACCEPT, delayMinutes=0), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=BEFORE_DELIVERY, configMinutes=60)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY), autoLaunchStrategyId=1)", deliveryPoiFactory.createDefaultDeliveryPoi(DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM, 1L, 2L).toString());
//		assertEquals("MaltFarmDeliveryPoi(super=DeliveryPoi(id=null, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=MALT_FARM_DELIVERY_PLATFORM, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=MERCHANT_ACCEPT, delayMinutes=0), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=BEFORE_DELIVERY, configMinutes=60)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY))", deliveryPoiFactory.createDefaultDeliveryPoi(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM, 1L, 2L).toString());
//		assertEquals("MerchantSelfDeliveryPoi(super=DeliveryPoi(id=null, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=MERCHANT_SELF_DELIVERY, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=MERCHANT_ACCEPT, delayMinutes=0), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=BEFORE_DELIVERY, configMinutes=60)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY))", deliveryPoiFactory.createDefaultDeliveryPoi(DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY, 1L, 2L).toString());
	}

	@Test
	public void should_createNewPlatformPoiByExisting_success() {
//		SelfBuiltDeliveryPoi p1 = buildSelfBuiltDeliveryPoi();
//		assertEquals(p1, deliveryPoiFactory.createNewPlatformPoiByExisting(p1, DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM));
//		assertEquals("AggrDeliveryPoi(super=DeliveryPoi(id=12341, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=AGGREGATION_DELIVERY_PLATFORM, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, delayMinutes=1), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, configMinutes=2)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY), autoLaunchStrategyId=1)", deliveryPoiFactory.createNewPlatformPoiByExisting(p1, DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM).toString());
//		assertEquals("MaltFarmDeliveryPoi(super=DeliveryPoi(id=12341, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=MALT_FARM_DELIVERY_PLATFORM, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, delayMinutes=1), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, configMinutes=2)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY))", deliveryPoiFactory.createNewPlatformPoiByExisting(p1, DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM).toString());
//		assertEquals("MerchantSelfDeliveryPoi(super=DeliveryPoi(id=12341, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=MERCHANT_SELF_DELIVERY, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, delayMinutes=1), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, configMinutes=2)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY))", deliveryPoiFactory.createNewPlatformPoiByExisting(p1, DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY).toString());
//
//		AggrDeliveryPoi p2 = buildAggrDeliveryPoi();
//		assertEquals("SelfBuiltDeliveryPoi(super=DeliveryPoi(id=12342, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=SELF_BUILT_DELIVERY_PLATFORM, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, delayMinutes=3), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, configMinutes=4)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY), channelTenantConfigMap={}, channelStoreConfigMap={}, orderPlatformDeliveryConfigMap={}, storeAddress=null, deliveryStrategy=SEQUENTIAL_POLLING, deliveryStrategyConfig=SequentialPollingStrategyConfig(super=DeliveryStrategyConfig(), orderedDeliveryChannels=[], timeoutForShiftDeliveryChannelInMinutes=15))", deliveryPoiFactory.createNewPlatformPoiByExisting(p2, DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM).toString());
//		assertEquals(p2, deliveryPoiFactory.createNewPlatformPoiByExisting(p2, DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM));
//		assertEquals("MaltFarmDeliveryPoi(super=DeliveryPoi(id=12342, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=MALT_FARM_DELIVERY_PLATFORM, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, delayMinutes=3), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, configMinutes=4)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY))", deliveryPoiFactory.createNewPlatformPoiByExisting(p2, DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM).toString());
//		assertEquals("MerchantSelfDeliveryPoi(super=DeliveryPoi(id=12342, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=MERCHANT_SELF_DELIVERY, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, delayMinutes=3), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, configMinutes=4)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY))", deliveryPoiFactory.createNewPlatformPoiByExisting(p2, DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY).toString());
//
//		MaltFarmDeliveryPoi p3 = buildMaltFarmDeliveryPoi();
//		assertEquals("SelfBuiltDeliveryPoi(super=DeliveryPoi(id=12343, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=SELF_BUILT_DELIVERY_PLATFORM, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, delayMinutes=5), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, configMinutes=6)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY), channelTenantConfigMap={}, channelStoreConfigMap={}, orderPlatformDeliveryConfigMap={}, storeAddress=null, deliveryStrategy=SEQUENTIAL_POLLING, deliveryStrategyConfig=SequentialPollingStrategyConfig(super=DeliveryStrategyConfig(), orderedDeliveryChannels=[], timeoutForShiftDeliveryChannelInMinutes=15))", deliveryPoiFactory.createNewPlatformPoiByExisting(p3, DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM).toString());
//		assertEquals("AggrDeliveryPoi(super=DeliveryPoi(id=12343, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=AGGREGATION_DELIVERY_PLATFORM, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, delayMinutes=5), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, configMinutes=6)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY), autoLaunchStrategyId=1)", deliveryPoiFactory.createNewPlatformPoiByExisting(p3, DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM).toString());
//		assertEquals(p3, deliveryPoiFactory.createNewPlatformPoiByExisting(p3, DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM));
//		assertEquals("MerchantSelfDeliveryPoi(super=DeliveryPoi(id=12343, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=MERCHANT_SELF_DELIVERY, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, delayMinutes=5), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=PICK_DONE, configMinutes=6)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY))", deliveryPoiFactory.createNewPlatformPoiByExisting(p3, DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY).toString());
//
//		MerchantSelfDeliveryPoi p4 = buildMerchantSelfDeliveryPoi();
//		assertEquals("SelfBuiltDeliveryPoi(super=DeliveryPoi(id=12344, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=SELF_BUILT_DELIVERY_PLATFORM, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=MERCHANT_ACCEPT, delayMinutes=7), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=BEFORE_DELIVERY, configMinutes=8)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY), channelTenantConfigMap={}, channelStoreConfigMap={}, orderPlatformDeliveryConfigMap={}, storeAddress=null, deliveryStrategy=SEQUENTIAL_POLLING, deliveryStrategyConfig=SequentialPollingStrategyConfig(super=DeliveryStrategyConfig(), orderedDeliveryChannels=[], timeoutForShiftDeliveryChannelInMinutes=15))", deliveryPoiFactory.createNewPlatformPoiByExisting(p4, DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM).toString());
//		assertEquals("AggrDeliveryPoi(super=DeliveryPoi(id=12344, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=AGGREGATION_DELIVERY_PLATFORM, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=MERCHANT_ACCEPT, delayMinutes=7), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=BEFORE_DELIVERY, configMinutes=8)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY), autoLaunchStrategyId=1)", deliveryPoiFactory.createNewPlatformPoiByExisting(p4, DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM).toString());
//		assertEquals("MaltFarmDeliveryPoi(super=DeliveryPoi(id=12344, tenantId=1, storeId=2, cityCode=999, contactPhone=13812345678, deliveryPlatform=MALT_FARM_DELIVERY_PLATFORM, deliveryLaunchPoint=DeliveryLaunchPoint(immediateOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(launchPoint=MERCHANT_ACCEPT, delayMinutes=7), bookingOrderDeliveryLaunchPointConfig=DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(launchPoint=BEFORE_DELIVERY, configMinutes=8)), deliveryLaunchType=AUTO_LAUNCH_DELIVERY))", deliveryPoiFactory.createNewPlatformPoiByExisting(p4, DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM).toString());
//		assertEquals(p4, deliveryPoiFactory.createNewPlatformPoiByExisting(p4, DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY));
	}

	private SelfBuiltDeliveryPoi buildSelfBuiltDeliveryPoi() {
		return new SelfBuiltDeliveryPoi(12341L, 1L, 2L, null, null,
				new DeliveryLaunchPoint(
						new ImmediateOrderDeliveryLaunchPointConfig(ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE, 1),
						new BookingOrderDeliveryLaunchPointConfig(BookingOrderDeliveryLaunchPointEnum.PICK_DONE, 2)
				),
				new Address("mock address", CoordinateTypeEnum.MARS, new CoordinatePoint("101.123456", "31.123456")),
				DeliveryStrategyEnum.SEQUENTIAL_POLLING,
				new SequentialPollingStrategyConfig(Lists.newArrayList(HAI_KUI_DELIVERY, FENG_NIAO_DELIVERY), 15),
				ImmutableMap.of(
						HAI_KUI_DELIVERY, new ChannelTenantConfig(11L, HAI_KUI_DELIVERY, "haikui", "hk", true),
						FENG_NIAO_DELIVERY, new ChannelTenantConfig(22L, FENG_NIAO_DELIVERY, "fengniao", "fn", true)
				),
				ImmutableMap.of(
						HAI_KUI_DELIVERY, new ChannelStoreConfig(111L, 2L, HAI_KUI_DELIVERY, "hk_store_id", "hk_store_name", ImmutableList.of("123"), true),
						FENG_NIAO_DELIVERY, new ChannelStoreConfig(222L, 2L, FENG_NIAO_DELIVERY, "fn_store_id", "fn_store_name", ImmutableList.of("456"), true)
				),
				ImmutableMap.of(
						100, new OrderPlatformDeliveryConfig(2, null, null)
				),100,null
		);
	}

	private AggrDeliveryPoi buildAggrDeliveryPoi() {
		return new AggrDeliveryPoi(
				12342L, 1L, 2L, 999, "13812345678",
				new DeliveryLaunchPoint(
						new ImmediateOrderDeliveryLaunchPointConfig(ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE, 3),
						new BookingOrderDeliveryLaunchPointConfig(BookingOrderDeliveryLaunchPointEnum.PICK_DONE, 4)
				),
				DeliveryLaunchTypeEnum.MANUAL_LAUNCH_DELIVERY,
				1,100,null
		);
	}

	private MaltFarmDeliveryPoi buildMaltFarmDeliveryPoi() {
		return new MaltFarmDeliveryPoi(
				12343L, 1L, 2L, 999, "13812345678",
				new DeliveryLaunchPoint(
						new ImmediateOrderDeliveryLaunchPointConfig(ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE, 5),
						new BookingOrderDeliveryLaunchPointConfig(BookingOrderDeliveryLaunchPointEnum.PICK_DONE, 6)
				),
				null,100,null
		);
	}

	private MerchantSelfDeliveryPoi buildMerchantSelfDeliveryPoi() {
		return new MerchantSelfDeliveryPoi(
				12344L, 1L, 2L, 999, "13812345678",
				new DeliveryLaunchPoint(
						new ImmediateOrderDeliveryLaunchPointConfig(ImmediateOrderDeliveryLaunchPointEnum.MERCHANT_ACCEPT, 7),
						new BookingOrderDeliveryLaunchPointConfig(BookingOrderDeliveryLaunchPointEnum.BEFORE_DELIVERY, 8)
				),100,null
		);
	}
}
