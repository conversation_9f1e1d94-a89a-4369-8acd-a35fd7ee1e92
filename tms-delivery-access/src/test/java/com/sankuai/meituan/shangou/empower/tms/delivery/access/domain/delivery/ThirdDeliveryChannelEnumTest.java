package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.delivery;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.servicepackage.ServicePackage;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/15
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"javax.management.*"})
public class ThirdDeliveryChannelEnumTest {

	@Test
	@PrepareForTest(ConfigUtilAdapter.class)
	public void should_GetServicePackages_success_with_default() {
		PowerMockito.mockStatic(ConfigUtilAdapter.class);
		PowerMockito.when(ConfigUtilAdapter.getString("delivery_service_packages_HAI_KUI")).thenReturn(null);

		List<ServicePackage> servicePackages = ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY.getServicePackages();
		assertEquals(11, servicePackages.size());
		assertEquals("[{\"code\":\"4002\",\"name\":\"飞速达\",\"order\":1,\"testOnly\":false,\"expectedDeliveryMinutes\":45},{\"code\":\"4011\",\"name\":\"快速达\",\"order\":2,\"testOnly\":false,\"expectedDeliveryMinutes\":60},{\"code\":\"4012\",\"name\":\"及时达\",\"order\":3,\"testOnly\":false,\"expectedDeliveryMinutes\":120},{\"code\":\"4013\",\"name\":\"集中达\",\"order\":4,\"testOnly\":false,\"expectedDeliveryMinutes\":120},{\"code\":\"100000\",\"name\":\"光速达-40\",\"order\":5,\"testOnly\":false,\"expectedDeliveryMinutes\":40},{\"code\":\"100001\",\"name\":\"光速达-45\",\"order\":6,\"testOnly\":false,\"expectedDeliveryMinutes\":45},{\"code\":\"100002\",\"name\":\"光速达-50\",\"order\":7,\"testOnly\":false,\"expectedDeliveryMinutes\":50},{\"code\":\"100003\",\"name\":\"光速达-55\",\"order\":8,\"testOnly\":false,\"expectedDeliveryMinutes\":60},{\"code\":\"100004\",\"name\":\"快速达-7590\",\"order\":9,\"testOnly\":false,\"expectedDeliveryMinutes\":90},{\"code\":\"100005\",\"name\":\"快速达-6090\",\"order\":10,\"testOnly\":false,\"expectedDeliveryMinutes\":90},{\"code\":\"100006\",\"name\":\"及时达\",\"order\":11,\"testOnly\":false,\"expectedDeliveryMinutes\":120}]", JsonUtil.toJson(servicePackages));

		servicePackages = ThirdDeliveryChannelEnum.FENG_NIAO_DELIVERY.getServicePackages();
		assertEquals(4, servicePackages.size());
		assertEquals("[{\"code\":\"1\",\"name\":\"蜂鸟配送\",\"order\":1,\"testOnly\":false},{\"code\":\"2\",\"name\":\"蜂鸟优送\",\"order\":2,\"testOnly\":false},{\"code\":\"3\",\"name\":\"蜂鸟快送\",\"order\":3,\"testOnly\":false},{\"code\":\"911\",\"name\":\"联调专用\",\"order\":4,\"testOnly\":true}]", JsonUtil.toJson(servicePackages));
	}

	@Test
	@PrepareForTest(ConfigUtilAdapter.class)
	public void should_GetServicePackages_success_with_configured() {
		PowerMockito.mockStatic(ConfigUtilAdapter.class);
		PowerMockito.when(ConfigUtilAdapter.getString("delivery_service_packages_HAI_KUI")).thenReturn("[{\"code\":\"4002\",\"name\":\"飞速达\",\"order\":1,\"testOnly\":false,\"expectedDeliveryMinutes\":45},{\"code\":\"100006\",\"name\":\"及时达\",\"order\":11,\"testOnly\":false,\"expectedDeliveryMinutes\":120}]");

		List<ServicePackage> servicePackages = ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY.getServicePackages();
		assertEquals(2, servicePackages.size());
		assertEquals("[{\"code\":\"4002\",\"name\":\"飞速达\",\"order\":1,\"testOnly\":false,\"expectedDeliveryMinutes\":45},{\"code\":\"100006\",\"name\":\"及时达\",\"order\":11,\"testOnly\":false,\"expectedDeliveryMinutes\":120}]", JsonUtil.toJson(servicePackages));
	}
}
