package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;


import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/14
 */
public class MockInjectUtils {

    public static void injectField(String fieldName, Object injectField, Object toInjectObj) {
        try {
            Field field = toInjectObj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(toInjectObj, injectField);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
