package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/23
 */
public class CoordinateUtilTest {

	@Test
	public void should_translateToCoordinatePoint_success() {
		assertEquals("CoordinatePoint(longitude=104.101463, latitude=30.659969)", CoordinateUtil.translateToCoordinatePoint(104.101463, 30.659969).toString());
		assertEquals("CoordinatePoint(longitude=104.101463, latitude=30.659969)", CoordinateUtil.translateToCoordinatePoint(104101463, 30659969).toString());

		assertEquals("CoordinatePoint(longitude=104.101463, latitude=30.659969)", CoordinateUtil.translateToCoordinatePoint("104.101463", "30.659969").toString());
		assertEquals("CoordinatePoint(longitude=104.101463, latitude=30.659969)", CoordinateUtil.translateToCoordinatePoint("104101463", "30659969").toString());
	}

	@Test
	public void should_translateToStandardStyle_success() {
		assertEquals("104.101463", CoordinateUtil.translateToStandardStyle("104.101463"));
		assertEquals("104.101463", CoordinateUtil.translateToStandardStyle("104101463"));
	}

	@Test
	public void should_translateFromBaiduToMars_success() {
		CoordinatePoint coordinatePoint = CoordinateUtil.translateFromBaiduToMars(new CoordinatePoint("104.072651", "30.573898"));
		assertEquals("104.066188", coordinatePoint.getLongitude());
		assertEquals("30.567841", coordinatePoint.getLatitude());
	}
}
