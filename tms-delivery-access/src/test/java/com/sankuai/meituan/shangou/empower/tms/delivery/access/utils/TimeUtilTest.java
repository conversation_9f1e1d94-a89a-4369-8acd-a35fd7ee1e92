package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import org.junit.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/23
 */
public class TimeUtilTest {

	@Test
	public void should_getEpochTime_success() {
		assertEquals("1970-01-01T00:00:00", TimeUtil.getEpochTime().format(DateTimeFormatter.ISO_DATE_TIME));
	}

	@Test
	public void should_fromMilliSeconds_success() {
		assertEquals("1970-01-01T08:00:00", TimeUtil.fromMilliSeconds(0L).format(DateTimeFormatter.ISO_DATE_TIME));
		assertEquals("2008-05-12T14:28:00", TimeUtil.fromMilliSeconds(1210573680000L).format(DateTimeFormatter.ISO_DATE_TIME));
	}

	@Test
	public void should_fromSeconds_success() {
		assertEquals("1970-01-01T08:00:00", TimeUtil.fromSeconds(0L).format(DateTimeFormatter.ISO_DATE_TIME));
		assertEquals("2021-03-23T17:05:01", TimeUtil.fromSeconds(1616490301L).format(DateTimeFormatter.ISO_DATE_TIME));
	}

	@Test
	public void should_toMilliSeconds_success() {
		assertEquals(1210573680000L, TimeUtil.toMilliSeconds(LocalDateTime.of(2008, 5, 12, 14, 28, 0)).longValue());
	}
}
