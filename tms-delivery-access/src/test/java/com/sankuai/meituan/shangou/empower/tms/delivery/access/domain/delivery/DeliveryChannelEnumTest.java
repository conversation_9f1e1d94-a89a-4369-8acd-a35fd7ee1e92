package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.delivery;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import org.junit.Test;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/23
 */
public class DeliveryChannelEnumTest {

	@Test
	public void should_isThirdPartyDeliveryChannel_success() {
		assertFalse(DeliveryChannelEnum.AGGREGATION_DELIVERY.isSelfBuiltDelivery());
		assertFalse(DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.isSelfBuiltDelivery());
		assertFalse(DeliveryChannelEnum.MERCHANT_DELIVERY.isSelfBuiltDelivery());
		assertTrue(DeliveryChannelEnum.HAI_KUI_DELIVERY.isSelfBuiltDelivery());
		assertTrue(DeliveryChannelEnum.FENG_NIAO_DELIVERY.isSelfBuiltDelivery());
	}
}
