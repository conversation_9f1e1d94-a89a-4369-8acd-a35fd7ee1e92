package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.delivery;

import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import org.junit.Test;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/23
 */
public class AddressTest {

	@Test
	public void should_isValid_success() {
		assertFalse(new Address("mock address", null, new CoordinatePoint("104.101463", "30.659969")).isValid());
		assertFalse(new Address("mock address", CoordinateTypeEnum.UNKNOWN, new CoordinatePoint("104.101463", "30.659969")).isValid());
		assertFalse(new Address("mock address", CoordinateTypeEnum.MARS, null).isValid());

		assertTrue(new Address("mock address", CoordinateTypeEnum.MARS, new CoordinatePoint("104.101463", "30.659969")).isValid());
	}
}
