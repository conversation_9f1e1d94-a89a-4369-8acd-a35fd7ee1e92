package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.UrlUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.DapSignUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.MaltFarmSignUtils;

import java.text.MessageFormat;
import java.util.*;

public enum AggDeliveryPlatformEnum {
    /**
     * 麦芽田
     */
    MALT_FARM(2, "使用麦芽田配送", "麦芽田配送设置",
            "麦芽田配送", "查看配送详情>", "去处理异常>"),
    DAP_DELIVERY(4,"使用青云聚信平台","青云聚信平台设置","青云聚信平台","查看配送详情>", "去处理异常>"),

    ;

    AggDeliveryPlatformEnum(Integer code, String configTitle, String configRedirectTitle,
                            String deliveryTitle, String deliveryRedirectTitle, String deliveryExceptionRedirectTitle) {
        this(code, configTitle, configRedirectTitle, deliveryTitle, deliveryRedirectTitle, deliveryExceptionRedirectTitle, null);
    }

    AggDeliveryPlatformEnum(Integer code, String configTitle, String configRedirectTitle,
                            String deliveryTitle, String deliveryRedirectTitle, String deliveryExceptionRedirectTitle,
                            List<Integer> deliveryChannelCodes) {
        this.code = code;
        this.configTitle = configTitle;
        this.configRedirectTitle = configRedirectTitle;
        this.deliveryTitle = deliveryTitle;
        this.deliveryRedirectTitle = deliveryRedirectTitle;
        this.deliveryExceptionRedirectTitle = deliveryExceptionRedirectTitle;
        this.devlieryChannelCodes = Optional.ofNullable(deliveryChannelCodes).orElse(Collections.emptyList());
    }

    private Integer code;

    private String configTitle;

    private String configRedirectTitle;

    private String deliveryTitle;

    private String deliveryRedirectTitle;

    private String deliveryExceptionRedirectTitle;

    private List<Integer> devlieryChannelCodes;

    public Integer getCode() {
        return code;
    }

    public String getConfigTitle() {
        return configTitle;
    }

    public String getConfigRedirectTitle() {
        return configRedirectTitle;
    }

    public String getDeliveryTitle() {
        return deliveryTitle;
    }

    public String getDeliveryRedirectTitle() {
        return deliveryRedirectTitle;
    }

    public List<Integer> getDevlieryChannelCodes() {
        return devlieryChannelCodes;
    }

    public boolean checkChanel(Integer deliveryChannelCode){
        if(deliveryChannelCode == null){
            return false;
        }
        return devlieryChannelCodes.contains(deliveryChannelCode);
    }

    public static AggDeliveryPlatformEnum codeValueOf(int code){
        AggDeliveryPlatformEnum[] data=values();
        for (AggDeliveryPlatformEnum platformEnum : data){
            if(platformEnum.getCode().equals(code)){
                return platformEnum;
            }
        }
        return null;
    }

    public static AggDeliveryPlatformEnum channelCodeValueOf(int channelCode){
        AggDeliveryPlatformEnum[] data=values();
        for (AggDeliveryPlatformEnum platformEnum : data){
            if(platformEnum.getDevlieryChannelCodes().contains(channelCode)){
                return platformEnum;
            }
        }
        return null;
    }

    public DeliveryRedirectModule fillDeliveryRedirectModule(AggDeliveryPlatformAppConfig config, String orderId, Long shopId,
                                                               boolean isExceptional){
        String redirectUrl = config.getRedirectUrl();
        if(this==MALT_FARM){
            String url = MessageFormat.format(config.getRedirectUrl(), config.getAppKey(), orderId, String.valueOf(shopId));
            UrlUtils.UrlInfo urlInfo = getUrlInfo(config, url);
            redirectUrl=UrlUtils.buildUrl4UrlInfo(urlInfo);
        }

        DeliveryRedirectModule configVo = new DeliveryRedirectModule();
        configVo.setUrl(redirectUrl);
        if (isExceptional) {
            configVo.setUrlText(deliveryExceptionRedirectTitle);
        } else {
            configVo.setUrlText(deliveryRedirectTitle);
        }
        configVo.setTitle(deliveryTitle);
        return configVo;
    }

    public UrlUtils.UrlInfo getUrlInfo(AggDeliveryPlatformAppConfig config, String url) {
        UrlUtils.UrlInfo urlInfo = UrlUtils.buildUrlInfo4Url(url);
        Map<String, String> searchParams = urlInfo.getSearchParams();
        Map<String, Object> signMap = new HashMap<>(searchParams);
        String sign = MaltFarmSignUtils.generateSignature(signMap, config.getConfigUrl());
        searchParams.put("sign", sign);
        return urlInfo;
    }

    public DeliveryRedirectModule fillDeliveryRedirectModule(String redirectUrl, boolean isExceptional){

        DeliveryRedirectModule configVo = new DeliveryRedirectModule();
        configVo.setUrl(redirectUrl);
        if (isExceptional) {
            configVo.setUrlText(deliveryExceptionRedirectTitle);
        } else {
            configVo.setUrlText(deliveryRedirectTitle);
        }
        configVo.setTitle(deliveryTitle);
        return configVo;
    }

    public String createAggDeliverySign(Map<String, Object> params, String appSecret) {
        switch (this) {
            case MALT_FARM:
                return MaltFarmSignUtils.generateSignature(params, appSecret);
            case DAP_DELIVERY:
                return DapSignUtils.generateSign(params, appSecret);
            default:
                break;
        }
        return "";
    }

}
