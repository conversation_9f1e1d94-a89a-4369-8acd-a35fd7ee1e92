package com.sankuai.meituan.shangou.empower.tms.delivery.access.aviator.data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-07-13
 * @email <EMAIL>
 */
public enum ExpressionSourceDataEnum {

    ORDER_RESERVE_TYPE("${reserve_type}", "订单预定类型，0-非预定单 1-预定单"),
    ETA("${eta_duration}", "预计送达时长"),
    RESTAURANT_SCENE("${restaurant_scene}", "餐馆场景"),
    /**
     * 保真送
     */
    CONTAIN_SEAL_PRODUCT("${contain_seal_product}", "是否包含封签交付商品；0-否 1-是"),
    DELIVERY_DISTANCE("${delivery_distance}", "配送距离")
    ;

    private String dataIdentifier;

    private String desc;


    ExpressionSourceDataEnum(String dataIdentifier, String desc) {
        this.dataIdentifier = dataIdentifier;
        this.desc = desc;
    }

    public static ExpressionSourceDataEnum enumOf(String dataIdentifier) {
        for (ExpressionSourceDataEnum val : values()) {
            if (Objects.equals(val.dataIdentifier, dataIdentifier)) {
                return val;
            }
        }

        throw new IllegalArgumentException("错误的dataIdentifier");
    }

    public String getDataIdentifier() {
        return dataIdentifier;
    }

    public String getDesc() {
        return desc;
    }

}
