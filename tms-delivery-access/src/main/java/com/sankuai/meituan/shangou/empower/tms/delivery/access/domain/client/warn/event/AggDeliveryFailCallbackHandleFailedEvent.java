package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.DeliveryWarnEvent;

import java.util.Optional;

/**
 * 聚合运力异常处理
 *
 * <AUTHOR>
 * @since 2021/04/13
 */
public class AggDeliveryFailCallbackHandleFailedEvent extends DeliveryWarnEvent {

	private final String warnMessage;

	public AggDeliveryFailCallbackHandleFailedEvent(DeliveryPlatformEnum deliveryPlatform, OrderKey orderKey, String failReason) {
		this.warnMessage = String.format(
				"三方聚合运力平台异常回调失败\n[平台:%s]\n[门店id:%d]\n[赋能订单号:%d]\n[失败原因:%s]",
				Optional.ofNullable(deliveryPlatform).map(DeliveryPlatformEnum::getDesc).orElse(""),
				Optional.ofNullable(orderKey).map(OrderKey::getStoreId).orElse(-1L),
				Optional.ofNullable(orderKey).map(OrderKey::getOrderId).orElse(-1L),
				Optional.ofNullable(failReason).orElse("未知")
		);
	}

	public AggDeliveryFailCallbackHandleFailedEvent(OrderKey orderKey, String failReason) {
		this.warnMessage = String.format(
				"配送异常回调失败\n[门店id:%d]\n[赋能订单号:%d]\n[失败原因:%s]",
				Optional.ofNullable(orderKey).map(OrderKey::getStoreId).orElse(-1L),
				Optional.ofNullable(orderKey).map(OrderKey::getOrderId).orElse(-1L),
				Optional.ofNullable(failReason).orElse("未知")
		);
	}

	@Override
	public String getWarnMessage() {
		return warnMessage;
	}
}
