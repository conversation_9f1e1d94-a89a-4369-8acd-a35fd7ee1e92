package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel;

import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import lombok.Getter;
import lombok.ToString;

/**
 * 门店信息查询结果
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/14
 */
@Getter
@ToString
public class DeliveryStoreInfoQueryResult {

	/**
	 * 配送是否发起成功
	 */
	private final boolean success;
	/**
	 * 是否需要重试(网络超时/系统异常等场景需要重试)
	 */
	private final boolean needRetry;
	/**
	 * 门店地址
	 */
	private final Address address;
	/**
	 * 联系方式
	 */
	private final String contactPhone;

	public DeliveryStoreInfoQueryResult(boolean success, boolean needRetry, Address address, String contactPhone) {
		this.success = success;
		this.needRetry = needRetry;
		this.address = address;
		this.contactPhone = contactPhone;
	}
}
