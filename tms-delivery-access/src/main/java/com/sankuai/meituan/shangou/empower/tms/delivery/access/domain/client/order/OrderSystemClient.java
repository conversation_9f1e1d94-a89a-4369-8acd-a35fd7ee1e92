package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order;

import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 订单系统客户端
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/9
 */
public interface OrderSystemClient {

	/**
	 * 查询订单信息
	 */
	Result<OrderInfo> getOrderInfo(OrderKey orderKey, boolean needWeightInfo);

	Result<OrderInfo> getOrderInfo(Long tenantId, Long orderId, boolean needWeightInfo);

	Result<OrderInfo> getOrderInfo(long tenantId, long poiId, String channelOrderId, int channelId, boolean needWeightInfo);
	/**
	 * 查询订单信息
	 */
	Result<OrderInfo> getOrderInfo(Long orderId, boolean needWeightInfo);

	/**
	 * 订单转自配送
	 * 包括：
	 * 1.调用订单线上渠道，转自配送
	 * 2.同步修改订单配送状态为自配送
	 */
	Optional<Failure> turnToSelfDelivery(Long tenantId, Long orderId, Integer orderBizType, String channelOrderId);

	/**
	 * 尝试更新订单配送状态到自行配送
	 */
	void turnOrderDeliveryStatusToSelfDelivery(Long tenantId, Long orderId, Integer orderBizType,
											   Pair<DeliveryChannel, String> deliveryChannelWithId);

	/**
	 * 向订单系统同步配送变更
	 */
	void syncDeliveryChangeToOrderSystem(DeliveryOrder deliveryOrder);

	/**
	 * 向订单系统同步配送变更,带变更时间
	 */
	void syncDeliveryChangeToOrderSystem(DeliveryOrder deliveryOrder, LocalDateTime updateTime);


	/**
	 * 设置订单配送超时标记
	 */
	Optional<Failure> setOrderDeliveryTimeout(DeliveryOrder deliveryOrder);

	/**
	 * 向订单同步转单信息
	 */
	void syncDeliveryTransToOrderSystem(DeliveryOrder deliveryOrder,Integer lastDeliveryPlatform);

	/**
	 * 通过渠道订单号获取
	 * @param orderBizType
	 * @param viewOrderId
	 * @return
	 */
	Result<OrderInfo> queryByViewOrderId(int orderBizType, String viewOrderId, boolean needWeightInfo);

	/**
	 * 获取订单状态
	 * @param orderWitBizMap
	 * @return
	 */
	Map<String,Integer> queryOrderStatusMap(Map<String,Integer> orderWitBizMap);

	List<OrderInfo> batchQueryOrderByEs(Long tenantId,List<Long> orderIdList);

	void syncDeliveryRollbackStatus2OrderSystem(DeliveryOrder deliveryOrder);

	void syncDeliveryExceptionToOrderSystem(DeliveryOrder deliveryOrder);

	/**
	 * 替换商品名称为货号
	 * @param orderKey
	 * @param orderInfo
	 */
	void  replaceGoodsCode2SkuName(OrderKey orderKey, OrderInfo orderInfo);

	default Integer getDistributeStatus4DeliveryStatus(DeliveryStatusEnum deliveryStatusEnum) {
		if(deliveryStatusEnum == null){
			return DistributeStatusEnum.UN_KNOWN.getValue();
		}
		switch (deliveryStatusEnum) {
			case INIT:
				return DistributeStatusEnum.UN_KNOWN.getValue();
			case WAITING_TO_ASSIGN_RIDER:
				return DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER.getValue();
			case RIDER_ASSIGNED:
				return DistributeStatusEnum.RIDER_ASSIGNED.getValue();
			case RIDER_ARRIVED_SHOP:
				return DistributeStatusEnum.RIDER_REACH_SHOP.getValue();
			case RIDER_TAKEN_GOODS:
				return DistributeStatusEnum.RIDER_TAKE_GOODS.getValue();
			case DELIVERY_DONE:
				return DistributeStatusEnum.RIDER_DELIVERED.getValue();
			case DELIVERY_REJECTED:
				return DistributeStatusEnum.DISTRIBUTE_REJECTED.getValue();
			case DELIVERY_FAILED:
				return DistributeStatusEnum.DISTRIBUTE_FAILED.getValue();
			case DELIVERY_CANCELLED:
				return DistributeStatusEnum.DISTRIBUTE_CANCELED.getValue();
			default:
				return DistributeStatusEnum.UN_KNOWN.getValue();
		}
	}



}
