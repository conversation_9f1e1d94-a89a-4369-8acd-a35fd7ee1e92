package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * OrderPlatformDeliveryOperateCmd
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
@Getter
@ToString
@AllArgsConstructor
public abstract class BaseOrderPlatformDeliveryOperateCmd {

    private final Long tenantId;

    private final Long offlineStoreId;

    private final OrderInfo orderInfo;

    private final Integer channelId;

    private final Operator operator;

}
