package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import com.dianping.rhino.onelimiter.OneLimiter;

/**
 * <AUTHOR>
 * @date 2025-01-09
 * @email <EMAIL>
 *
 */
public class OneLimiterHolder {

    private OneLimiterHolder() {

    }

    // 初始化限流器，单例的，整个应用初始化一个即可
    // 默认rhino-key为rhino-one-limiter
    private static OneLimiter oneLimiter = com.dianping.rhino.Rhino.newOneLimiter();

    public static OneLimiter getOneLimiter() {
        return oneLimiter;
    }

}
