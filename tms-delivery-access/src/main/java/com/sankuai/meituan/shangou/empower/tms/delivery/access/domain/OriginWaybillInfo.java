package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.OriginWaybillDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-08-09
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode
public class OriginWaybillInfo {
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * tms运单id
     */
    private Long deliveryOrderId;
    /**
     * 运单状态
     */
    private Integer deliveryStatus;
    /**
     * 原始运单id
     */
    private String originWaybillNo;


    public OriginWaybillDto convertToOriginWaybillDto() {
        return new OriginWaybillDto(this.deliveryStatus, this.originWaybillNo);
    }


    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"orderId\":")
                .append(orderId);

        sb.append(",\"deliveryOrderId\":")
                .append(deliveryOrderId);

        sb.append(",\"deliveryStatus\":")
                .append(deliveryStatus);

        sb.append(",\"originWaybillNo\":\"")
                .append(originWaybillNo).append('\"');

        sb.append('}');
        return sb.toString();
    }
}
