package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.abn;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.warehouse.AbnOrderService;
import com.sankuai.shangou.logistics.warehouse.TradeShippingGrayService;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-02-19
 * @email <EMAIL>
 */
@Slf4j
@Service
public class TradeShippingOrderFacade {

    @Resource
    private TradeShippingGrayService tradeShippingGrayService;
    @Resource
    private AbnOrderService abnOrderService;

    @MethodLog(logRequest = false, logResponse = true)
    public boolean isNewPickGrayStore(long warehouseId) {
        try {
            return tradeShippingGrayService.isGrayStore(warehouseId);
        } catch (Exception e) {
            log.error("invoke tradeShippingGrayService.isGrayStore error", e);
            return false;
        }
    }

    @MethodLog(logRequest = false, logResponse = true)
    public Optional<AbnOrderDTO> queryAbnOrderByChannelOrderId(long warehouseId, int orderBizType, String channelOrderId) {
        try {
            TResult<List<AbnOrderDTO>> unprocessedResult = abnOrderService.getUnprocessed(warehouseId);
            return Optional.ofNullable(unprocessedResult.getData())
                    .orElse(Lists.newArrayList())
                    .stream()
                    .filter(abnOrderDTO -> Objects.equals(abnOrderDTO.getSourceOrderNo(), channelOrderId) && Objects.equals(abnOrderDTO.getSourceType(), orderBizType))
                    .findAny();
        } catch (Exception e) {
            log.error("invoke tradeShippingGrayService.isGrayStore error", e);
            return Optional.empty();
        }
    }
}
