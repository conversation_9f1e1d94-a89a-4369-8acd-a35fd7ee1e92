package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

/**
 * <AUTHOR>
 * 平台配送判断是否需要重新呼叫骑手的标识位
 */

public enum RecallDeliveryEnum {

    NO_NEED_RECALL(0, "无需呼叫"),
    NEED_RECALL(1, "需要呼叫")
    ;

    private final int code;
    private final String desc;

    RecallDeliveryEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }
}
