package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR>
 */

public enum TransDeliveryPlatformEnum {

    ORDER_PLATFORM_DELIVERY(0, "平台配送"),
    SELF_DELIVERY(1, "自配送")
    ;

    private final int code;
    private final String name;

    TransDeliveryPlatformEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }
    @JsonValue
    public int getCode() {
        return code;
    }
}
