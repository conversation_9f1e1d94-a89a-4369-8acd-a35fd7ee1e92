package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 配送变更回调请求
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/7
 */
@Getter
@ToString
@AllArgsConstructor
public class DeliveryChangeCallbackCmd {

	private final Long orderId;

	private final Integer deliveryChannel;
	private final String channelDeliveryId;
	private final String servicePackage;

	private final DeliveryEventEnum deliveryEvent;

	private final DeliveryExceptionInfo exceptionInfo;

	private final LocalDateTime changeTime;

	/**
	 * 骑手信息，可以为空，为空则将清空运单骑手信息
	 */
	private final Rider rider;

	/**
	 * 距离信息，可以为空，为空则不改动运单距离信息
	 */
	private final Long distance;

	/**
	 * 配送费用，可以为空，为空则不改动运单费用信息
	 */
	private final BigDecimal deliveryFee;

	/**
	 * 配送小费数值，元
	 */
	private final BigDecimal tipAmount;

	/**
	 * 平台来源
	 */
	private final Integer platformSource;

	private final String baseFee;
	private final String discountFee;
	private final String insuredFee;

	/**
	 * 配送平台方式
	 * @see com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum
	 */
	private final Integer deliveryPlatformCode;

	/**
	 * 配送取消原因code
	 * @see com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryCancelReasonEnum
	 */
	private final Integer deliveryCancelCode;

	/**
	 * 承运商原始运单id
	 */
	private String originWaybillNo;

}
