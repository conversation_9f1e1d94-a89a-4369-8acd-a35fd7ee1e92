package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 配送发起结果回调请求
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/7
 */
@Getter
@ToString
@AllArgsConstructor
public class DeliveryLaunchResultCallbackCmd {

	private final Long storeId;

	private final Long orderId;

	private final String channelDeliveryId;

	private final boolean launchSuccess;

	private final String launchFailReasonDesc;

	private final Integer deliveryChannel;

	private final String servicePackage;

	private final Long distance;

	private final BigDecimal deliveryFee;
}
