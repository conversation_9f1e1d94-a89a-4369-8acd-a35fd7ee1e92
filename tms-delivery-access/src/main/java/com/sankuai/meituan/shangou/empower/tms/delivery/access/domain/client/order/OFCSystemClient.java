package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;

import java.time.LocalDateTime;

public interface OFCSystemClient {

    void syncDeliveryChangeToSystem(DeliveryOrder deliveryOrder, LocalDateTime updateTime);

    void syncDeliveryChangeToSystem(Long tenantId, Long storeId,Long orderId,Long fulfillOrderId, LocalDateTime updateTime);

    boolean queryOrderIsContainSnOrSealProduct(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType);

    boolean queryOrderIsContainSealProductFromMaster(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType);
}
