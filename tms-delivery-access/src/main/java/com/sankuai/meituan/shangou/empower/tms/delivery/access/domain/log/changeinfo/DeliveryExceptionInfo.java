package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang.StringUtils;

/**
 * 运单异常详情
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/8
 */
@Getter
@ToString
public class DeliveryExceptionInfo extends DeliveryChangeInfo {

    /**
     * 异常类型
     */
    private final DeliveryExceptionTypeEnum exceptionType;

    /**
     * 异常描述
     */
    private final String exceptionDescription;

    private final Integer exceptionCode;

    public final static DeliveryExceptionInfo NO_EXCEPTION = new DeliveryExceptionInfo(
            DeliveryExceptionTypeEnum.NO_EXCEPTION,
            StringUtils.EMPTY,
            DeliveryExceptionCodeEnum.NO_EXCEPTION.getCode()
    );

    public DeliveryExceptionInfo(DeliveryExceptionTypeEnum exceptionType, String exceptionDescription) {
        this.exceptionType = exceptionType;
        this.exceptionDescription = exceptionDescription;
        this.exceptionCode = DeliveryExceptionCodeEnum.NO_EXCEPTION.getCode();
    }

    public DeliveryExceptionInfo(DeliveryExceptionTypeEnum exceptionType, DeliveryExceptionCodeEnum exceptionCode) {
        this(exceptionType, exceptionCode.getDesc(), exceptionCode.getCode());
    }

    public DeliveryExceptionInfo(DeliveryExceptionTypeEnum exceptionType, String exceptionDescription,
            Integer exceptionCode) {
        this.exceptionType = exceptionType;
        this.exceptionDescription = exceptionDescription;
        this.exceptionCode = exceptionCode;
    }

    public DeliveryExceptionInfo(String reason) {
        this.exceptionCode = DeliveryExceptionCodeEnum.NO_EXCEPTION.getCode();
        this.exceptionDescription = reason;
        this.exceptionType = DeliveryExceptionTypeEnum.LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION;
    }

}
