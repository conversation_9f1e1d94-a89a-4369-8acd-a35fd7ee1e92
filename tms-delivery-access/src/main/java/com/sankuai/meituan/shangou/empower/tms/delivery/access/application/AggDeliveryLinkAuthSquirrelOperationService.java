package com.sankuai.meituan.shangou.empower.tms.delivery.access.application;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.QueryAggLinkBaseRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.AggLinkAuthInfo;

import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-14
 */
public interface AggDeliveryLinkAuthSquirrelOperationService {

    boolean save(String code, QueryAggLinkBaseRequest baseRequest, String fulfillmentOrderId);

    Optional<AggLinkAuthInfo> get(String code);

    boolean remove(String code);
}
