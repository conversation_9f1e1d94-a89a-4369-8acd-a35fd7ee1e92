package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log;


import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 配送运单流水仓储服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/9
 */
public interface DeliveryOrderLogRepository {

	/**
	 * 保存配送运单流水记录
	 */
	void saveDeliveryChangeLog(Long deliveryId, Integer deliveryStatus, DeliveryEventEnum deliveryEvent, String changeInfoJson, LocalDateTime changeTime);

	/**
	 * 根据运单号查询运单流水信息
	 * attention:目前changeInfo因为是db中的string转出来的，而且当前没有使用的场景，当前没有塞changeInf的值
	 *
	 * @param deliveryIds
	 * @return
	 */
	Map<Long, DeliveryOrderLog> getLogsByDeliveryOrderIds(List<Long> deliveryIds, DeliveryEventEnum deliveryEvent);

	Map<Long, List<DeliveryOrderLog>> getLogsByDeliveryOrderIdsAndDeliveryEventCodes(List<Long> deliveryIds, List<Integer> deliveryEventCodes);

	Map<Long, List<DeliveryOrderLog>> getDeliveryOrderLogByDeliveryOrderIds(List<Long> deliveryIds);
}
