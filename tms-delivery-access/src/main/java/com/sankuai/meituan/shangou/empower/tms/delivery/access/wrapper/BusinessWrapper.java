package com.sankuai.meituan.shangou.empower.tms.delivery.access.wrapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 业态判断工具
 * <AUTHOR>
 */
@Slf4j
@Component
public class BusinessWrapper {

    @Resource
    private MedicineTenantWrapper medicineTenantWrapper;

    public boolean isDhAndMedicineTenant(Long tenantId) {
        try {
            if (MccConfigUtils.checkIsDHTenant(tenantId)) {
                return true;
            }
            if (medicineTenantWrapper.isUwmsMedicineTenant(tenantId)) {
                return true;
            }
        } catch (Exception e) {
            log.error("isDhAndMedicineTenant error", e);
        }
        return false;
    }

}
