package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryChangeInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 运单骑手改派详情
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/8
 */
@Getter
@ToString
@AllArgsConstructor
public class DeliveryRiderChangeInfo extends DeliveryChangeInfo {

	private final Rider oldRider;

	private final Rider newRider;
}
