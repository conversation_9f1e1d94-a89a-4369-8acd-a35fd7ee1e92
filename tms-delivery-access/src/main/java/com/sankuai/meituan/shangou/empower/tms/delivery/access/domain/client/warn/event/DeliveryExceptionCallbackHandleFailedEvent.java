package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.DeliveryExceptionCallbackCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.DeliveryWarnEvent;

import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/4/19
 */
public class DeliveryExceptionCallbackHandleFailedEvent extends DeliveryWarnEvent {

	private final String warnMessage;

	public DeliveryExceptionCallbackHandleFailedEvent(DeliveryExceptionCallbackCmd cmd, String failReason) {
		this.warnMessage = String.format(
				"配送异常回调消费失败\n[赋能订单号:%d]\n[消费失败原因:%s]",
				Optional.ofNullable(cmd).map(DeliveryExceptionCallbackCmd::getOrderId).orElse(-1L),
				failReason
		);
	}

	@Override
	public String getWarnMessage() {
		return warnMessage;
	}
}
