package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event;

import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.SpringContextUtils;
import lombok.AllArgsConstructor;

import java.util.Optional;

/**
 * 配送取消失败事件
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/6
 */
@AllArgsConstructor
public class DeliveryCancelFailedEvent extends DeliveryWarnEvent {

	private final DeliveryOrder deliveryOrder;

	private final String failReason;

	@Override
	public String getWarnMessage() {
		DeliveryChannelApplicationService deliveryChannelApplicationService = SpringContextUtils.getBean(DeliveryChannelApplicationService.class);
		return String.format(
				"配送取消失败\n[配送渠道:%s]\n[租户id:%d][门店id:%d]\n[运单id:%d]\n[赋能订单号:%d]\n[渠道:%s][渠道订单号:%s]\n[失败原因:%s]",
				Optional.ofNullable(deliveryOrder).map(DeliveryOrder::getDeliveryChannel).map(deliveryChannelCode -> deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryChannelCode).getCarrierName()).orElse("未知"),
				Optional.ofNullable(deliveryOrder).map(DeliveryOrder::getTenantId).orElse(-1L),
				Optional.ofNullable(deliveryOrder).map(DeliveryOrder::getStoreId).orElse(-1L),
				Optional.ofNullable(deliveryOrder).map(DeliveryOrder::getId).orElse(-1L),
				Optional.ofNullable(deliveryOrder).map(DeliveryOrder::getOrderKey).map(OrderKey::getOrderId).orElse(-1L),
				Optional.ofNullable(deliveryOrder).map(DeliveryOrder::getOrderBizType).map(DynamicOrderBizType::findOf).map(DynamicOrderBizType::getDesc).orElse(""),
				Optional.ofNullable(deliveryOrder).map(DeliveryOrder::getChannelOrderId).orElse(""),
				failReason
		);
	}
}
