package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
public class FourWheelPreviewCmd extends BaseOrderPlatformDeliveryOperateCmd{

    private final Integer previewType;

    public FourWheelPreviewCmd(Long tenantId, Long offlineStoreId, OrderInfo orderInfo, Integer channelId, Operator operator,Integer previewType) {
        super(tenantId, offlineStoreId, orderInfo, channelId, operator);
        this.previewType = previewType;
    }
}
