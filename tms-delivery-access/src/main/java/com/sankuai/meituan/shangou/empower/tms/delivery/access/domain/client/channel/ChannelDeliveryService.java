package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel;


import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ChannelStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;

/**
 * 渠道配送服务接口抽象
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/7
 */
public interface ChannelDeliveryService {

	/**
	 * 通过渠道接口进行发起配送前置校验，校验该配送渠道该服务包对该订单是否可以发起配送
	 *
	 * @param orderInfo          订单信息
	 * @param channelStore       渠道配送门店信息
	 * @param servicePackageCode 服务包码
	 * @return 校验结果
	 */
	DeliveryCheckResult checkDeliverable(OrderInfo orderInfo, ChannelStoreConfig channelStore, String servicePackageCode);

	/**
	 * 根据运单发起配送
	 *
	 * @param deliveryOrder 运单信息
	 * @param deliveryPoi
	 * @param orderInfo
	 * @return 配送发起结果
	 */
	DeliveryLaunchResult launchDelivery(DeliveryOrder deliveryOrder, SelfBuiltDeliveryPoi deliveryPoi, OrderInfo orderInfo);

}
