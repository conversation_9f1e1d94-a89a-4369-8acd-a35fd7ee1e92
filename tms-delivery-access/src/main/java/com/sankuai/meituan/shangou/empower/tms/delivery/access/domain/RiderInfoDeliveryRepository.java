package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;

import java.util.Optional;

/**
 * 新供给骑手信息相关接口
 *
 * <AUTHOR>
 * @date 2024/5/24
 */
public interface RiderInfoDeliveryRepository {

    /**
     * @description: 查询骑手信息
     * @param: orderId 赋能订单ID
     * @return 骑手信息
     */
    Optional<Rider> queryRiderInfo(Long orderId);

    /**
     * @description: 保存骑手信息
     * @param: orderId 赋能订单ID
     * @param: rider 骑手信息
     * @return true表示保存成功
    */
    boolean saveRiderInfo(Long orderId, Rider rider);

}
