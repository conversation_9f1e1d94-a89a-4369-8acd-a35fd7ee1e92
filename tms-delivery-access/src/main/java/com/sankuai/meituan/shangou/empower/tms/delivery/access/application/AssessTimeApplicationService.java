package com.sankuai.meituan.shangou.empower.tms.delivery.access.application;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.utils.AddressSceneConvertUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aviator.ExpressionCondition;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aviator.ExpressionNode;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aviator.data.ExpressionSourceDataEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aviator.excutable.ExecutableExpressionNode;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aviator.func.CaseThenFunction;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aviator.func.ExpressionCalcPeon;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aviator.func.SwitchGroupFunction;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aviator.interval.Interval;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aviator.interval.IntervalNumber;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aviator.interval.IntervalTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OFCSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.poi.PoiSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.LionConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.facade.RouteFacade;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalTime;
import java.util.*;

import static com.dianping.lion.client.Lion.getConfigRepository;

/**
 * <AUTHOR>
 * @date 2025-07-23
 * @email <EMAIL>
 */
@Slf4j
@Service
@Component
public class AssessTimeApplicationService {

    @Resource
    private OFCSystemClient ofcSystemClient;
    @Resource
    private PoiSystemClient poiSystemClient;
    @Resource
    private RouteFacade routeFacade;

    private static final int BOOKING_ORDER = 1;
    private static final String TRUE_CONDITION = "true";
    private static final Set<IntervalTypeEnum> ONE_VALUE_TYPE_SET = Sets.newHashSet(IntervalTypeEnum.EQUAL, IntervalTypeEnum.NOT_EQUAL);

    @MethodLog(logResponse = true)
    public Integer calcAssessDeliveryDuration(Long tenantId, long storeId, OrderInfo orderInfo, @Nullable Long deliveryDistance, String configJson) {
        if (StringUtils.isBlank(configJson)) {
            return -1;
        }
        // FIXME: 13.7.24 这里其实还差一步提取formula需要用到哪些source data，排期问题先不实现
        Map payload = new HashMap();
        payload.put(replaceSymbol(ExpressionSourceDataEnum.DELIVERY_DISTANCE.getDataIdentifier()), deliveryDistance);
        for (ExpressionSourceDataEnum value : ExpressionSourceDataEnum.values()) {
            switch (value) {
                case ETA: {
                    long etaDurationMill = TimeUtil.toMilliSeconds(orderInfo.getEstimatedDeliveryEndTime()) - TimeUtil.toMilliSeconds(orderInfo.getPayTime());
                    long etaDurationSeconds = etaDurationMill / 1000;
                    payload.put(replaceSymbol(value.getDataIdentifier()), etaDurationSeconds);
                    break;
                }
                case RESTAURANT_SCENE: {
                    payload.put(replaceSymbol(value.getDataIdentifier()), isRestaurantScene(orderInfo.getCategory()) ? 1 : 0);
                    break;
                }
                case ORDER_RESERVE_TYPE: {
                    payload.put(replaceSymbol(value.getDataIdentifier()), orderInfo.isBookingOrder() ? 1 : 0);
                    break;
                }
                case CONTAIN_SEAL_PRODUCT: {
                    boolean containSealProduct = ofcSystemClient.queryOrderIsContainSealProductFromMaster(tenantId, storeId, orderInfo.getChannelOrderId(), orderInfo.getOrderBizType());
                    payload.put(replaceSymbol(value.getDataIdentifier()), containSealProduct ? 1 : 0);
                    break;
                }
                default:
                    continue;
            }
        }
        log.info("payload = {}", payload);
        //表达式树，用作展示
        ExpressionNode rootExpressionTree = JsonUtil.fromJson(configJson, ExpressionNode.class);
        //生成执行树，用作后续编译为avitar语句。
        ExecutableExpressionNode executableExpressionTree = toExecutableExpressionTree(rootExpressionTree);
        log.info("executableExpressionTree = {}", executableExpressionTree);
        //生成表达式
        String formula = compile(executableExpressionTree);
        log.info("formula = {}", formula);
        //aviatar执行
        ExpressionCalcPeon expressionCalcPeon = new ExpressionCalcPeon();
        BigDecimal result = expressionCalcPeon.execute(replaceSymbol(formula), payload);
        if (result.compareTo(BigDecimal.ZERO) > 0) {
            return result.intValue();
        }

        return -1;
    }

    @MethodLog(logResponse = true)
    public Long calcPushDownTimestamp(Long tenantId, long storeId, OrderInfo orderInfo, String configJson, @Nullable Long deliveryDistance) {
        if (!orderInfo.isBookingOrder()) {
            return -1L;
        }
        if (Objects.isNull(deliveryDistance)) {
            Result<Double> distanceResult = routeFacade.queryRidePathDistance(
                    orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(),
                    orderInfo.getReceiver().getReceiverAddress().getCoordinatePoint()
            );
            if (distanceResult.isSuccess() && distanceResult.getInfo() > 0) {
                deliveryDistance = distanceResult.getInfo().longValue();
            }
        }
        if (Objects.isNull(deliveryDistance) || deliveryDistance <= 0) {
            return -1L;
        }

        Long eta = TimeUtil.toMilliSeconds(orderInfo.getEstimatedDeliveryEndTime());
        LocalTime earliestOpeningTime = getEarliestOpeningHours(tenantId, storeId, DynamicOrderBizType.findOf(orderInfo.getOrderBizType()).getChannelId());
        LocalTime etaTime = TimeUtils.fromMilliSeconds(eta).toLocalTime();
        Duration duration = Duration.between(earliestOpeningTime, etaTime);
        //eta和开业时间的差值小于0则肯定跨天了，不计入规则,即 duration>0&& eta <= 最早营业时间+40分钟,则提前40分钟下发
        if (duration.getSeconds() > 0 && etaTime.isBefore(earliestOpeningTime.plusMinutes(LionConfigUtils.getPushDownOpeningTimeMin()))) {
            return eta - LionConfigUtils.getPushDownOpeningTimeMin() * 60 * 1000L;
        } else {
            Integer calcDuration = calcAssessDeliveryDuration(tenantId, storeId, orderInfo, deliveryDistance, configJson);
            if (calcDuration < 0) {
                return -1L;
            }
            return eta - calcDuration * 1000L;
        }
    }

    private LocalTime getEarliestOpeningHours(Long merchantId, long warehouseId, int channelId) {
        ChannelPoiInfoDTO channelPoiInfoDTO = poiSystemClient.queryChannelPoiInfoDTO(merchantId, warehouseId, channelId);
        List<String> shippingTimes = channelPoiInfoDTO.getShippingTime();
        if (CollectionUtils.isEmpty(shippingTimes)) {
            throw new ThirdPartyException("没有营业时间！");
        }
        //这里逻辑是和产品业务定好了一个"参考时间"，从参考时间往后找最近的
        List<LocalTime> openTimes = Lists.newArrayList();
        for (String shippingTime : shippingTimes) {
            String[] split = shippingTime.split("-");
            openTimes.add(TimeUtil.fromFormat(split[0]));
        }
        String referenceOpenTimeStr = LionConfigUtils.getReferenceOpenTime();
        LocalTime referenceOpenTime = TimeUtil.fromFormat(referenceOpenTimeStr);

        LocalTime localTime = openTimes.get(0);
        long minDiffSeconds = Long.MAX_VALUE;
        for (LocalTime openTime : openTimes) {
            Duration duration = Duration.between(referenceOpenTime, openTime);
            long diffSeconds = duration.getSeconds();
            if (diffSeconds > 0 && diffSeconds < minDiffSeconds) {
                localTime = openTime;
            }
        }

        return localTime;
    }

    private String replaceSymbol(String source) {
        if (source.contains("{")) {
            source = source.replace("{", "__");
        }
        if (source.contains("}")) {
            source = source.replace("}", "__");
        }
        return source;
    }

    private Boolean isRestaurantScene(String category) {
        try {
            if (StringUtils.isBlank(category)) {
                return false;
            }
            String scene = AddressSceneConvertUtils.convertCategory2Scene(category);
            if (Objects.isNull(scene)) {
                return false;
            }
            return getDhRestaurantSceneList().contains(scene);
        } catch (Exception e) {
            log.error("isRestaurantScene error", e);
            return false;
        }
    }

    private static String compile(ExecutableExpressionNode tree) {
        if (tree == null) {
            throw new IllegalArgumentException("空树");
        }

        if (!tree.hasSubs()) {
            // 没有子节点
            return packageCaseThenFunction(tree.getCondition(), tree.getFormula());
        }

        // 1. 深度遍历到叶子节点 => caseThen
        List<String> pathList = new ArrayList<>();
        dfs(pathList, tree, new ArrayList<>());
        // 2. switchCase(所有caseThen)
        if (pathList.size() == 0) {
            throw new IllegalArgumentException("合法条件路径为空");
        }
        String compositeCaseThen = String.join(",", pathList);
        String switchGroupFormula = String.format("%s(%s)", SwitchGroupFunction.FUNCTION_NAME, compositeCaseThen);
        // 3. 如果表达式中有集合算子，如SWITCH_ACCUMULATE，转换表达式
        return switchGroupFormula;
    }

    private static String packageCaseThenFunction(String condition, String formula) {
        if (StringUtils.isBlank(condition)) {
            condition = "true";
        }
        if (StringUtils.isBlank(formula)) {
            throw new IllegalArgumentException("公式不能为空");
        }

        return String.format("%s(%s,%s)", CaseThenFunction.FUNCTION_NAME, condition, formula);
    }


    private static void dfs(List<String> pathList, ExecutableExpressionNode tree, List<String> conditionList) {
        if (tree == null) {
            return;
        }
        if (StringUtils.isNotBlank(tree.getCondition())) {
            conditionList.add(tree.getCondition());
        }

        if (!tree.hasSubs()) {
            // 叶子节点
            if (conditionList.size() > 0) {
                String compositeCondition = String.join(" && ", conditionList);
                String formula = tree.getFormula();
                pathList.add(packageCaseThenFunction(compositeCondition, formula));
            }
            return;
        }

        for (ExecutableExpressionNode node : tree.getSubs()) {
            List<String> newConditionList = new ArrayList<>(conditionList);
            dfs(pathList, node, newConditionList);
        }
    }

    private ExecutableExpressionNode toExecutableExpressionTree(ExpressionNode source) {

        if (Objects.isNull(source)) {
            throw new IllegalArgumentException("不正确的表达式树");
        }

        ExecutableExpressionNode newNode = new ExecutableExpressionNode();
        newNode.setCondition(buildCondition(source.getCondition()));
        if (CollectionUtils.isEmpty(source.getSubs())) {
            // leaf node
            newNode.setFormula(source.getFormula());
        }

        if (CollectionUtils.isNotEmpty(source.getSubs())) {
            List<ExecutableExpressionNode> newNodes = new ArrayList<>();
            for (ExpressionNode node : source.getSubs()) {
                newNodes.add(toExecutableExpressionTree(node));
            }
            newNode.setSubs(newNodes);
        }
        return newNode;
    }

    public static String buildCondition(ExpressionCondition expressionCondition) {
        if (Objects.isNull(expressionCondition)) {
            return TRUE_CONDITION;
        }

        Interval interval = expressionCondition.getInterval();
        List<IntervalNumber> values = interval.getValues();
        String conditionFormula = expressionCondition.getFormula();

        if (ONE_VALUE_TYPE_SET.contains(interval.getIntervalType())) {
            if (CollectionUtils.isEmpty(values) || values.size() != 1) {
                // 错误条件
                throw new SystemException(String.format("error condition: %s", JsonUtil.toJson(expressionCondition)));
            }
            IntervalNumber firstValue = values.get(0);
            switch (interval.getIntervalType()) {
                case EQUAL:
                    return String.format("%s == %s", conditionFormula, firstValue.getValue());
                case NOT_EQUAL:
                    return String.format("%s != %s", conditionFormula, firstValue.getValue());
                default:
                    // 暂不支持
                    throw new SystemException("don't support this interval: " + interval.getIntervalType());
            }
        }

        if (CollectionUtils.isEmpty(values) || values.size() != 2) {
            // 错误条件
            throw new SystemException(String.format("error condition: %s", JsonUtil.toJson(expressionCondition)));
        }
        IntervalNumber firstValue = values.get(0);
        IntervalNumber secondValue = values.get(1);

        switch (interval.getIntervalType()) {
            case EQUAL:
                return String.format("%s == %s", conditionFormula, firstValue.getValue());
            case NOT_EQUAL:
                return String.format("%s != %s", conditionFormula, firstValue.getValue());
            case LEFT_OPEN: // (,]
                if (secondValue.isPositiveInfinity() || firstValue.isPositiveInfinity()) {
                    // 错误条件
                    throw new SystemException(String.format("error condition: %s", JsonUtil.toJson(expressionCondition)));
                }
                if (firstValue.isNegativeInfinity()) {
                    return String.format("%s <= %s", conditionFormula, secondValue.getValue());
                }
                return String.format("%s < %s && %s <= %s", firstValue.getValue(), conditionFormula, conditionFormula,
                        secondValue.getValue());
            case RIGHT_OPEN: // [.)
                if (firstValue.isNegativeInfinity() || secondValue.isNegativeInfinity()) {
                    // 错误条件
                    throw new SystemException(String.format("error condition: %s", JsonUtil.toJson(expressionCondition)));
                }
                if (secondValue.isPositiveInfinity()) {
                    return String.format("%s >= %s", conditionFormula, firstValue.getValue());
                }
                return String.format("%s <= %s && %s < %s", firstValue.getValue(), conditionFormula, conditionFormula,
                        secondValue.getValue());

            case ALL_OPEN: // (,)
                if (firstValue.isPositiveInfinity() || secondValue.isNegativeInfinity()) {
                    // 错误条件
                    throw new SystemException(String.format("error condition: %s", JsonUtil.toJson(expressionCondition)));
                }
                if (firstValue.isNegativeInfinity() && secondValue.isPositiveInfinity()) {
                    return "true";
                }
                if (firstValue.isNegativeInfinity()) {
                    return String.format("%s < %s", conditionFormula, secondValue.getValue());
                }
                if (secondValue.isPositiveInfinity()) {
                    return String.format("%s > %s", conditionFormula, firstValue.getValue());
                }

                return String.format("%s < %s && %s < %s", firstValue.getValue(), conditionFormula, conditionFormula,
                        secondValue.getValue());
            case ALL_CLOSE: // [,]
                if (firstValue.isNegativeInfinity() || firstValue.isPositiveInfinity() || secondValue.isNegativeInfinity()
                        || secondValue.isPositiveInfinity()) {
                    // 错误条件
                    throw new SystemException(String.format("error condition: %s", JsonUtil.toJson(expressionCondition)));
                }
                return String.format("%s <= %s && %s <= %s", firstValue.getValue(), conditionFormula, conditionFormula,
                        secondValue.getValue());

            default:
                // 暂不支持
                throw new SystemException("don't support this interval: " + interval.getIntervalType());
        }
    }


    private static List<String> getDhRestaurantSceneList() {
        try {
            return getConfigRepository("com.sankuai.sgfulfillment.tms").getList("dh.restaurant.scene", String.class, Lists.newArrayList("餐馆"));
        } catch (Exception e) {
            log.error("isDhScenePoi error", e);
            return Lists.newArrayList();
        }
    }

}
