package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event;

import java.util.Optional;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;

/**
 * 三方聚合运力回调处理失败.
 *
 * <AUTHOR>
 * @since 2021/10/22 14:51
 */
public class AggDeliveryCallbackHandleFailedEvent extends DeliveryWarnEvent {

    private final String warnMessage;

    public AggDeliveryCallbackHandleFailedEvent(DeliveryPlatformEnum deliveryPlatform, OrderKey orderKey, String failReason) {
        this.warnMessage = String.format(
                "三方聚合运力平台回调处理失败\n[平台:%s]\n[门店id:%d]\n[赋能订单号:%d]\n[失败原因:%s]",
                Optional.ofNullable(deliveryPlatform).map(DeliveryPlatformEnum::getDesc).orElse(""),
                Optional.ofNullable(orderKey).map(OrderKey::getStoreId).orElse(-1L),
                Optional.ofNullable(orderKey).map(OrderKey::getOrderId).orElse(-1L),
                Optional.ofNullable(failReason).orElse("未知")
        );
    }

    @Override
    public String getWarnMessage() {
        return warnMessage;
    }
}
