package com.sankuai.meituan.shangou.empower.tms.delivery.access.application;


import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.dms.base.infrastructure.service.IDeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChannelRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 配送渠道应用层服务
 *
 * <AUTHOR>
 * @date 2023/4/8
 */
@Service
@Slf4j
public class DeliveryChannelApplicationService implements IDeliveryChannelApplicationService {

    @Autowired
    @Qualifier("mySQLDeliveryChannelRepository")
    private DeliveryChannelRepository mysqlDeliveryChannelRepository;

    @Autowired
    @Qualifier("squirrelDeliveryChannelRepository")
    private DeliveryChannelRepository squirrelDeliveryChannelRepository;

    @CatTransaction
    @Override
    public DeliveryChannel queryDeliveryChannelByCarrierCode(Integer carrierCode) {
        if (MccConfigUtils.isQueryDeliveryChannelDegrade()) {
            log.info("queryDeliveryChannelByCarrierCode degrade from DeliveryChannelEnum");
            return DeliveryChannel.translateFromDeliveryChannelEnum(carrierCode);
        }

        Optional<DeliveryChannel> squirrelOptional = squirrelDeliveryChannelRepository.getDeliveryChannelByCarrierCode(carrierCode);
        if (squirrelOptional.isPresent()) {
            return squirrelOptional.get();
        }

        if (MccConfigUtils.queryDeliveryChannelFromDBSwitch()) {
            Optional<DeliveryChannel> mysqlOptional = mysqlDeliveryChannelRepository.getDeliveryChannelByCarrierCode(carrierCode);
            if (mysqlOptional.isPresent()) {
                log.info("query delivery channel by carrier code from mysql");
                return mysqlOptional.get();
            }
        }

        log.info("query delivery channel by carrier code from DeliveryChannelEnum");
        return DeliveryChannel.translateFromDeliveryChannelEnum(carrierCode);
    }

    @CatTransaction
    public DeliveryChannel queryDeliveryChannelByLogisticMark(Integer deliveryPlatFormCode, Integer orderChannelCode, String logisticMark) {
        if (MccConfigUtils.isQueryDeliveryChannelDegrade()) {
            log.info("queryDeliveryChannelByLogisticMark degrade from DeliveryChannelEnum");
            return DeliveryChannel.translateFromDeliveryChannelEnum(deliveryPlatFormCode, orderChannelCode, logisticMark);
        }

        Optional<DeliveryChannel> squirrelOptional = squirrelDeliveryChannelRepository.getDeliveryChannelByLogisticMark(deliveryPlatFormCode, orderChannelCode, logisticMark);
        if (squirrelOptional.isPresent()) {
            return squirrelOptional.get();
        }

        if (MccConfigUtils.queryDeliveryChannelFromDBSwitch()) {
            Optional<DeliveryChannel> mysqlOptional = mysqlDeliveryChannelRepository.getDeliveryChannelByLogisticMark(deliveryPlatFormCode, orderChannelCode, logisticMark);
            if (mysqlOptional.isPresent()) {
                log.info("query delivery channel by logistic mark from mysql");
                return mysqlOptional.get();
            }
        }

        log.info("query delivery channel by logistic mark from DeliveryChannelEnum");
        return DeliveryChannel.translateFromDeliveryChannelEnum(deliveryPlatFormCode, orderChannelCode, logisticMark);
    }

    @CatTransaction
    @Override
    public List<DeliveryChannel> batchQueryDeliveryChannelByCarrierCodeSet(Set<Integer> carrierCodeSet) {
        if (MccConfigUtils.isQueryDeliveryChannelDegrade()) {
            log.info("batchQueryDeliveryChannelByCarrierCodeSet degrade from DeliveryChannelEnum");
            return DeliveryChannel.translateFromDeliveryChannelEnum(carrierCodeSet);
        }

        if (CollectionUtils.isEmpty(carrierCodeSet)) {
            log.warn("batchQueryDeliveryChannelByCarrierCodeList, carrierCodeSet is empty");
            return Collections.emptyList();
        }

        List<DeliveryChannel> deliveryChannelList = Lists.newArrayList();
        Map<Integer, Optional<DeliveryChannel>> squirrelMap = squirrelDeliveryChannelRepository.getDeliveryChannelMapByCarrierCodeSet(carrierCodeSet);
        deliveryChannelList.addAll(squirrelMap.values().stream().filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList()));
        Set<Integer> squirrelUnMissedCarrierCodeSet = squirrelMap.entrySet().stream().filter(entry -> !entry.getValue().isPresent()).map(Map.Entry::getKey).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(squirrelUnMissedCarrierCodeSet)) {
            return deliveryChannelList;
        }

        if (MccConfigUtils.queryDeliveryChannelFromDBSwitch()) {
            log.info("batchQueryDeliveryChannelByCarrierCodeSet, db switch is on");
            Map<Integer, Optional<DeliveryChannel>> mysqlMap = mysqlDeliveryChannelRepository.getDeliveryChannelMapByCarrierCodeSet(squirrelUnMissedCarrierCodeSet);
            deliveryChannelList.addAll(mysqlMap.values().stream().filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList()));
            Set<Integer> mysqlUnMissedCarrierCodeSet = mysqlMap.entrySet().stream().filter(entry -> !entry.getValue().isPresent()).map(Map.Entry::getKey).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(mysqlUnMissedCarrierCodeSet)) {
                log.info("mysqlUnMissedCarrierCodeSet is empty");
                return deliveryChannelList;
            }
            deliveryChannelList.addAll(DeliveryChannel.translateFromDeliveryChannelEnum(mysqlUnMissedCarrierCodeSet));
        } else {
            log.info("batchQueryDeliveryChannelByCarrierCodeSet, db switch is off");
            deliveryChannelList.addAll(DeliveryChannel.translateFromDeliveryChannelEnum(squirrelUnMissedCarrierCodeSet));
        }
        return deliveryChannelList;
    }
}
