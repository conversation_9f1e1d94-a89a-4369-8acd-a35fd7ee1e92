package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;

import javax.annotation.Nullable;
import java.util.Optional;

/**
 * 配送相关告警客户端接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/6
 */
public interface DeliveryWarnClient {

	void warn(String warnMessage);

	/**
	 * 发送骑手送达超时的大象推送通知
	 */
	Optional<Failure> pushDeliveryDoneTimeOut(OrderInfo orderInfo, DeliveryOrder deliveryOrder);

	/**
	 * 通知外部服务骑手送达超时消息
	 */
	Optional<Failure> notifyOrderEstimatedDeliveryTimeOut(OrderInfo orderInfo);

	void pushTurnToDapFail(OrderInfo orderInfo);

}
