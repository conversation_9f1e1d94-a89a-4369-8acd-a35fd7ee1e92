package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ChannelStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SequentialPollingStrategyConfig;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 发起配送策略领域服务
 * 提供多种配送发起策略，包括顺序轮询，多平台抢单等
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/4
 */
@Slf4j
@Service
public class LaunchDeliveryStrategyDomainService {

	@CatTransaction
	public DeliveryPlan getDeliveryPlan(SelfBuiltDeliveryPoi deliveryPoi) {
		log.info("LaunchDeliveryStrategyDomainService.getDeliveryPlan begin, deliveryStore={}", deliveryPoi);

		if (MapUtils.isEmpty(deliveryPoi.getChannelStoreConfigMap())) {
			log.warn("No available channel store, decide to give up delivery. deliveryStore={}", deliveryPoi);
			return new DeliveryPlan(DeliveryPlanType.GIVE_UP_DELIVERY, new ArrayList<>());
		}

		switch (deliveryPoi.getDeliveryStrategy()) {
			case SEQUENTIAL_POLLING:
				Preconditions.checkArgument(deliveryPoi.getDeliveryStrategyConfig() instanceof SequentialPollingStrategyConfig);
				SequentialPollingStrategyConfig deliveryStrategyConfig = (SequentialPollingStrategyConfig) deliveryPoi.getDeliveryStrategyConfig();

				List<ChannelStoreConfig> orderedChannelStoreList = deliveryStrategyConfig.getOrderedDeliveryChannels()
						.stream()
						.map(it -> deliveryPoi.getChannelStoreConfigMap().get(it))
						.filter(Objects::nonNull)
						.collect(Collectors.toList());

				DeliveryPlan deliveryPlan = new DeliveryPlan(DeliveryPlanType.SEQUENTIAL, orderedChannelStoreList);
				log.info("LaunchDeliveryStrategyDomainService.getDeliveryPlan success, deliveryStore={}, deliveryPlan={}", deliveryPoi, deliveryPlan);
				return deliveryPlan;

			case MULTI_PLATFORM_GRABBING:
			default:
				throw new IllegalStateException(String.format("Strategy[%s] is not supported for now.", deliveryPoi.getDeliveryStrategy()));
		}
	}
}
