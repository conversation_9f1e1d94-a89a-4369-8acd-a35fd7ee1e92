package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.auth;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/20
 */
public interface AuthClient {

	/**
	 * 校验操作人是否拥有门店权限
	 */
	Optional<Failure> checkStorePermission(Long tenantId, Long operatorId, List<Long> storeIds);
}
