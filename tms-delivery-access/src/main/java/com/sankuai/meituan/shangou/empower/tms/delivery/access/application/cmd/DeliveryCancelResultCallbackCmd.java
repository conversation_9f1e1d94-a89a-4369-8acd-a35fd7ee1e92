package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 配送取消结果回调请求
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/7
 */
@Getter
@ToString
@AllArgsConstructor
public class DeliveryCancelResultCallbackCmd {

	private final Long storeId;

	private final Long orderId;

	private final String channelDeliveryId;

	private final boolean cancelSuccess;

	private final String cancelFailReasonDesc;
}
