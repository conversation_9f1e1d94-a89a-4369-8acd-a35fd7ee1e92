package com.sankuai.meituan.shangou.empower.tms.delivery.access.application;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;

import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @date 2024-10-28
 */
public interface DeliveryMarkSquirrelOperationService {

    void saveDeliveryMark(DeliveryOrder deliveryOrder);

    void saveDeliveryMark(Integer orderBizType, String channelOrderId, Integer deliveryChannel);

    Optional<Integer> get(Integer orderBizType, String channelOrderId);

}
