package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;


import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/16
 */
public class CoordinateUtil {

	private static final double X_PI = Math.PI * 3000.0 / 180.0;

	public static CoordinatePoint translateToCoordinatePoint(double longitude, double latitude) {
		return new CoordinatePoint(translateToStandardStyle(String.valueOf(longitude)), translateToStandardStyle(String.valueOf(latitude)));
	}

	public static CoordinatePoint translateToCoordinatePoint(String longitude, String latitude) {
		return new CoordinatePoint(translateToStandardStyle(longitude), translateToStandardStyle(latitude));
	}

	public static String translateToStandardStyle(String value) {
		BigDecimal bigDecimal = new BigDecimal(value);
		if (bigDecimal.scale() == 0) {
			return bigDecimal.scaleByPowerOfTen(-6).toString();
		} else {
			return value;
		}
	}

	public static Integer translateToIntStyle(String standardValue) {
		BigDecimal bigDecimal = new BigDecimal(standardValue);
		if (bigDecimal.scale() == 0) {
			return bigDecimal.intValue();
		} else {
			return bigDecimal.scaleByPowerOfTen(6).intValue();
		}
	}

	/**
	 * 百度坐标系转成高德坐标系
	 */
	public static CoordinatePoint translateFromBaiduToMars(CoordinatePoint sourcePoint) {
		Preconditions.checkNotNull(sourcePoint, "sourcePoint is null");

		double x = Double.parseDouble(sourcePoint.getLongitude()) - 0.0065;
		double y = Double.parseDouble(sourcePoint.getLatitude()) - 0.006;
		double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * X_PI);
		double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI);

		double longitude = BigDecimal.valueOf(z * Math.cos(theta)).setScale(6, RoundingMode.HALF_UP).doubleValue();
		double latitude = BigDecimal.valueOf(z * Math.sin(theta)).setScale(6, RoundingMode.HALF_UP).doubleValue();
		return new CoordinatePoint(String.valueOf(longitude), String.valueOf(latitude));
	}
}
