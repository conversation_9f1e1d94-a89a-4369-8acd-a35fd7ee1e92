package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;
import java.util.Set;

import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.util.ConfigUtilAdapter;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * TMS 系统的 MCC 配置查询工具类.
 *
 * <AUTHOR>
 * @since 2021/3/8 17:49
 */
@Slf4j
@SuppressWarnings("deprecation")
public class TmsMccUtils {

    /**
     * 对于来自聚合运力平台支持的配送商，在 TMS 系统进行一次不支持的配送商的过滤.
     *
     * @return TMS 不支持的配送商
     */
    public static Set<Integer> getUnsupportedChannelsInTms() {
        try {
            String filteredChannels = ConfigUtilAdapter.getString("filter_delivery_channels_from_aggr_platform",
                    "[]");
            return JsonUtil.fromJson(filteredChannels, new TypeReference<Set<Integer>>() {
            });
        } catch (Exception e) {
            log.error("MCC[filter_delivery_channels_from_aggr_platform] parse error", e);
            return Collections.emptySet();
        }
    }

    /**
     * 判断运单超时的小时数，当超过预计送达时间x小时，判定为超时
     */
    public static Integer getHours4JudgeDeliveryOrderTimeout() {
        try {
            return ConfigUtilAdapter.getInt("hours_to_judge_timeout", 4);
        } catch (Exception e) {
            log.error("MCC[hours_to_judge_timeout] parse error", e);
            return 4;
        }
    }

    /**
     * 判断运单超时的小时数，当超过预计送达时间x小时，判定为超时
     */
    public static Integer getDHHours4JudgeDeliveryOrderTimeout() {
        try {
            return ConfigUtilAdapter.getInt("dh_hours_to_judge_timeout", 4);
        } catch (Exception e) {
            log.error("MCC[dh_hours_to_judge_timeout] parse error", e);
            return 4;
        }
    }

    /**
     * 是否是自提时也需要发配送的租户
     */
    public static boolean isLaunchStoreDeliveryTenant(long tenantId) {
        try {
            String tenantIds = Lion.getConfigRepository().get("delivery.launch.store.tenants", StringUtils.EMPTY);
            return Arrays.stream(tenantIds
                    .split(","))
                    .anyMatch(tenantIdStr -> Objects.equals(tenantId,Long.parseLong(tenantIdStr)));
        } catch (Exception e) {
            log.error("MCC[isLaunchStoreDeliveryTenant] parse error", e);
            return false;
        }

    }
}
