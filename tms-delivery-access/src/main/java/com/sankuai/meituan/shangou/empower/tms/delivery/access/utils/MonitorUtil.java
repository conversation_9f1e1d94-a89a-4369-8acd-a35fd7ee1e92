package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MonitorUtil {

    public static void logMetric(String metricName, boolean isFail) {
        try {
            Cat.logMetricForCount(metricName);
            if (isFail) {
                Cat.logMetricForCount(metricName + "_FAIL");
            } else {
                Cat.logMetricForCount(metricName + "_SUCCESS");
            }
        } catch (Exception e) {
            log.error("Log cat metric failed", e);
        }
    }
}
