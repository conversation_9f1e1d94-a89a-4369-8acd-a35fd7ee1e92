package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.DeliveryCancelResultCallbackCmd;

import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/8
 */
public class DeliveryCancelCallbackHandleFailedEvent extends DeliveryWarnEvent {

	private final String warnMessage;

	public DeliveryCancelCallbackHandleFailedEvent(DeliveryCancelResultCallbackCmd cmd, String failReason) {
		this.warnMessage = String.format(
				"配送取消回调消费失败\n[门店id:%d]\n[赋能订单号:%d]\n[是否取消成功:%s]\n[消费失败原因:%s]",
				Optional.ofNullable(cmd).map(DeliveryCancelResultCallbackCmd::getStoreId).orElse(-1L),
				Optional.ofNullable(cmd).map(DeliveryCancelResultCallbackCmd::getOrderId).orElse(-1L),
				Optional.ofNullable(cmd).map(DeliveryCancelResultCallbackCmd::isCancelSuccess).map(Object::toString).orElse(""),
				failReason
		);
	}

	@Override
	public String getWarnMessage() {
		return warnMessage;
	}
}
