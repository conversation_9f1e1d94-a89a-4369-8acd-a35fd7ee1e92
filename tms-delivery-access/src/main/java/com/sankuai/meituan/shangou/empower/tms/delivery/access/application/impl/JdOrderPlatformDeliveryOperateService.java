package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.impl;

import com.dianping.cat.Cat;
import com.google.common.collect.Sets;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.DeliveryOperationFailedException;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.platform.AuditExceptionReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.platform.ReportExceptionReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.platform.UrgeDispatchingReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.DeliveryOperationResultDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.OcmsResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.OrderPlatformDeliveryOperateThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.OrderPlatformDeliveryOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.AuditExceptionCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.CallDeliveryCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.ReportExceptionCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.RecallDeliveryEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.SpringContextUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * JdOrderPlatformDeliveryOperateService
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
@Slf4j
@Service
public class JdOrderPlatformDeliveryOperateService extends BaseOrderPlatformDeliveryOperateService implements OrderPlatformDeliveryOperateService {

    private static final String JDDJ_ORDER_PLATFORM_DELIVERY_OPERATE_CAT_TYPE = "JDDJOrderPlatformDeliveryOperate";

    private static final String RECALL_DELIVERY_FAILED = "recall.delivery.failed";

    private static final String RECALL_DELIVERY_SUCCEED = "recall.delivery.succeed";

    /**
     * 需要支持再次呼叫骑手的牵牛花运单终态
    */
    private static final Set<DeliveryStatusEnum> NEED_RECALL_DELIVERY_STATUS_SET = Sets.newHashSet(DeliveryStatusEnum.DELIVERY_FAILED);

    @Autowired
    private OrderPlatformDeliveryOperateThriftService orderPlatformDeliveryOperateThriftService;

    @Autowired
    private DeliveryOrderRepository deliveryOrderRepository;

    @Override
    public boolean reportException(ReportExceptionCmd cmd) throws DeliveryOperationFailedException {
        OrderInfo orderInfo = cmd.getOrderInfo();
        reportExceptionToJd(cmd, orderInfo);

        Long orderId = orderInfo.getOrderKey().getOrderId();
        DeliveryOrder deliveryOrder = null;
        if(MccConfigUtils.getDeliveryQueryTenantSwitch(cmd.getTenantId())){
            deliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderWithTenant(orderId, cmd.getTenantId(), cmd.getOfflineStoreId())
                    .orElseThrow(() -> new CommonRuntimeException("No such delivery order " + orderId));
        }else {
            deliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(orderId)
                    .orElseThrow(() -> new CommonRuntimeException("No such delivery order " + orderId));
        }

        cleanExceptionIfNotTimeout(deliveryOrder);
        deliveryOrderRepository.save(deliveryOrder);
        return true;
    }

    @Override
    public boolean auditException(AuditExceptionCmd cmd) throws DeliveryOperationFailedException {
        OrderInfo orderInfo = cmd.getOrderInfo();
        Long orderId = orderInfo.getOrderKey().getOrderId();
        DeliveryOrder deliveryOrder = null;
        if(MccConfigUtils.getDeliveryQueryTenantSwitch(cmd.getTenantId())){
            deliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderWithTenant(
                    orderId, cmd.getTenantId(), cmd.getOfflineStoreId()).orElseThrow(() -> new CommonRuntimeException("No such delivery order " + orderId));
        }else {
            deliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(
                    orderId).orElseThrow(() -> new CommonRuntimeException("No such delivery order " + orderId));
        }

        notifyAuditResult(cmd, orderInfo);

        if (Objects.equals(cmd.getIsAgree(), true)) {
            // 审核同意则催单
            ifAgreeProcess(cmd, orderInfo, deliveryOrder);
        }

        cleanExceptionIfNotTimeout(deliveryOrder);
        deliveryOrderRepository.save(deliveryOrder);
        return true;
    }

    @Override
    public boolean callDelivery(CallDeliveryCmd cmd) {
        if (!MccConfigUtils.getJddjRecallDeliverySwitch()) {
            log.info("jddj recall delivery switch is off, cmd is {}", cmd);
            return true;
        }

        OrderInfo orderInfo = cmd.getOrderInfo();
        if (Objects.isNull(orderInfo) || !Objects.equals(orderInfo.getOrderBizType(), DynamicOrderBizType.JING_DONG.getValue())) {
            throw new CommonRuntimeException("JdOrderPlatformDeliveryOperateService callDelivery failed, param error");
        }
        Long orderId = orderInfo.getOrderKey().getOrderId();

        List<DeliveryOrder> deliveryOrders = new ArrayList<>();
        if(MccConfigUtils.getDeliveryQueryTenantSwitch(cmd.getTenantId())){
            deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMasterWithTenant(orderId, cmd.getTenantId(), cmd.getOfflineStoreId());
        }else {
            deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMaster(orderId);
        }

        if (CollectionUtils.isEmpty(deliveryOrders)) {
            log.error("jddj recall delivery, no delivery order, orderId is {}", orderId);
            return false;
        }
        DeliveryOrder deliveryOrder = DeliveryOrder.filterActiveDeliveryOrder(deliveryOrders);
        if (!deliveryOrder.isActive() && !NEED_RECALL_DELIVERY_STATUS_SET.contains(deliveryOrder.getStatus())) {
            log.error("jddj recall delivery, deliveryOrder not support recall, orderId is {}", orderId);
            return false;
        }

        // 催单（重新呼叫骑手）
        boolean isSucceed = urgeDispatching(cmd.getTenantId(), cmd.getOfflineStoreId(), orderInfo.getChannelOrderId(),
                cmd.getChannelId(), cmd.getOperator());
        if (!isSucceed) {
            log.error("JdOrderPlatformDeliveryOperateService callDelivery failed, orderId is {}", orderId);
            Cat.logEvent(JDDJ_ORDER_PLATFORM_DELIVERY_OPERATE_CAT_TYPE, RECALL_DELIVERY_FAILED);
            throw new CommonRuntimeException("JdOrderPlatformDeliveryOperateService callDelivery failed");
        }

        Cat.logEvent(JDDJ_ORDER_PLATFORM_DELIVERY_OPERATE_CAT_TYPE, RECALL_DELIVERY_SUCCEED);
        // 催单成功，状态置为待骑手接单
        updateWaitToAssignRiderStatus(deliveryOrder);
        // 催单成功，清除配送异常
        cleanExceptionIfNotTimeout(deliveryOrder);
        deliveryOrderRepository.save(deliveryOrder);
        return true;
    }

    @Override
    public Integer ofOrderChannel() {
        return DynamicChannelType.JD2HOME.getChannelId();
    }

    /**
     * 上报异常给JDDJ
     */
    private void reportExceptionToJd(ReportExceptionCmd cmd, OrderInfo orderInfo) {
        ReportExceptionReq req = new ReportExceptionReq(cmd.getTenantId(), cmd.getOfflineStoreId(),
                orderInfo.getChannelOrderId(), cmd.getChannelId(), cmd.getPictureUrls());
        log.info("#reportException parameter: {}", req);

        try {
            OcmsResponse<DeliveryOperationResultDto> resp = orderPlatformDeliveryOperateThriftService.reportException(
                    req);
            log.info("#reportException response: {}", resp);
            if (!resp.isSuccess()) {
                throw new DeliveryOperationFailedException(resp.getMsg());
            }

        } catch (TException e) {
            log.error("#reportException error: {}", e.getMessage(), e);
            throw new CommonRuntimeException(e);
        }
    }

    private void ifAgreeProcess(AuditExceptionCmd cmd, OrderInfo orderInfo, DeliveryOrder deliveryOrder) {
        boolean isSucceed = urgeDispatching(cmd, orderInfo);
        if (isSucceed) {// 催单成功，状态置为待骑手接单
            flowWaitToAssignRiderStatus(deliveryOrder);
        }
    }

    /**
     * 催单
     */
    private boolean urgeDispatching(AuditExceptionCmd cmd, OrderInfo orderInfo) {
        UrgeDispatchingReq req = new UrgeDispatchingReq(cmd.getTenantId(), cmd.getOfflineStoreId(),
                orderInfo.getChannelOrderId(), cmd.getChannelId(),
                cmd.getOperator().getOperatorName());
        try {
            OcmsResponse<DeliveryOperationResultDto> resp = orderPlatformDeliveryOperateThriftService.urgeDispatching(
                    req);
            log.info("#urgeDispatching response: {}", resp);
            if (!resp.isSuccess()) {
                log.error("urge dispatching failed: {}", resp.getMsg());
            }
            return resp.isSuccess();
        } catch (TException e) {
            log.error("urge dispatching error: {}", e.getMessage(), e);
            throw new CommonRuntimeException(e);
        }
    }

    /**
     * 催单
     */
    private boolean urgeDispatching(Long tenantId, Long offlineStoreId, String channelOrderId, Integer channelId, Operator operator) {
        UrgeDispatchingReq req = new UrgeDispatchingReq(tenantId, offlineStoreId, channelOrderId, channelId, operator.getOperatorName());
        try {
            OcmsResponse<DeliveryOperationResultDto> resp = orderPlatformDeliveryOperateThriftService.urgeDispatching(req);
            log.info("urgeDispatching response: {}", resp);
            if (!resp.isSuccess()) {
                log.error("urge dispatching failed: {}", resp.getMsg());
            }
            return resp.isSuccess();
        } catch (TException e) {
            log.error("urge dispatching error: {}", e.getMessage(), e);
            throw new CommonRuntimeException(e);
        }
    }

    private void notifyAuditResult(AuditExceptionCmd cmd, OrderInfo orderInfo) {
        boolean isAgree = ObjectUtils.defaultIfNull(cmd.getIsAgree(), false);
        AuditExceptionReq req = new AuditExceptionReq(cmd.getTenantId(), cmd.getOfflineStoreId(),
                orderInfo.getChannelOrderId(), cmd.getChannelId(),
                cmd.getOperator().getOperatorName(), isAgree);
        try {
            OcmsResponse<DeliveryOperationResultDto> resp = orderPlatformDeliveryOperateThriftService.auditException(
                    req);
            log.info("#auditException response: {}", resp);
            if (!resp.isSuccess()) {
                throw new DeliveryOperationFailedException("操作失败");
            }
        } catch (TException e) {
            log.error("notify audit result error: {}", e.getMessage(), e);
            throw new CommonRuntimeException(e);
        }
    }

    /**
     * 将状态流转为待骑手接单
     */
    private void flowWaitToAssignRiderStatus(DeliveryOrder deliveryOrder) {
        LocalDateTime now = LocalDateTime.now();
        deliveryOrder.onRiderChange(new Rider("未知", "未知", ""), now);
        deliveryOrder.onStatusChange(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, now);
        //对订单同步运单变更
        OrderSystemClient orderSystemClient = SpringContextUtils.getBean(OrderSystemClient.class);
        orderSystemClient.syncDeliveryRollbackStatus2OrderSystem(deliveryOrder);
        log.info("Modify delivery order after agreeing report: {}", deliveryOrder);
    }

    /**
     * 再次呼叫后，将状态流转为待骑手接单，再次呼叫的标识位isRecallDelivery置为0
     */
    private void updateWaitToAssignRiderStatus(DeliveryOrder deliveryOrder) {
        LocalDateTime now = LocalDateTime.now();
        deliveryOrder.onRiderChange(new Rider("未知", "未知", ""), now);
        deliveryOrder.onStatusChange(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, now);
        // 「投递失败」状态的运单需要激活
        deliveryOrder.activate();
        // 再次呼叫的标识位isRecallDelivery置为0
        deliveryOrder.setIsRecallDelivery(RecallDeliveryEnum.NO_NEED_RECALL.getCode());

        //对订单同步运单变更
        OrderSystemClient orderSystemClient = SpringContextUtils.getBean(OrderSystemClient.class);
        orderSystemClient.syncDeliveryRollbackStatus2OrderSystem(deliveryOrder);
        log.info("recall delivery report: {}", deliveryOrder);
    }

}
