package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/6
 */
@Getter
@ToString
@AllArgsConstructor
public class DeliveryInfo {

	/**
	 * 三方配送渠道
	 */
	private final DeliveryChannelEnum deliveryChannel;

	/**
	 * 三方配送服务包
	 */
	private final String servicePackageCode;

	/**
	 * 渠道配送id
	 */
	private final String channelDeliveryId;

	/**
	 * 配送状态
	 */
	private final DeliveryStatusEnum deliveryStatus;

	/**
	 * 配送费，单位：元
	 */
	private final BigDecimal deliveryFee;

	/**
	 * 配送距离，单位：米
	 */
	private final Long distance;
}
