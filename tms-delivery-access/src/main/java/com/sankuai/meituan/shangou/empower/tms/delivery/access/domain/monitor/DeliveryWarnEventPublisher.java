package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor;

import com.google.common.eventbus.AsyncEventBus;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.DeliveryWarnEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/6
 */
@Slf4j
@Component
@SuppressWarnings("UnstableApiUsage")
public class DeliveryWarnEventPublisher {

	private static final int CORE_POOL_SIZE = 2;
	private static final int MAX_POOL_SIZE = 20;
	private static final int ALIVE_SECOND = 100;
	private static final int QUEUE_CAPACITY = 200;

	private AsyncEventBus asyncEventBus;

	@Resource
	private DeliveryWarnEventListener deliveryWarnEventListener;

	@PostConstruct
	public void init() {
		ThreadPoolExecutor executor = new ThreadPoolExecutor(CORE_POOL_SIZE,
				MAX_POOL_SIZE,
				ALIVE_SECOND, TimeUnit.SECONDS,
				new LinkedBlockingQueue<>(QUEUE_CAPACITY),
				new ThreadFactoryBuilder().setNameFormat("DeliveryWarnEventBus-pool-%d").build());
		asyncEventBus = new AsyncEventBus(new ExecutorServiceTraceWrapper(executor));
		this.asyncEventBus.register(deliveryWarnEventListener);
	}

	public void postEvent(DeliveryWarnEvent event) {
		try {
			log.info("post event {}", event);
			this.asyncEventBus.post(event);
		} catch (Exception e) {
			log.error("post event error, event:{}", event, e);
		}
	}
}
