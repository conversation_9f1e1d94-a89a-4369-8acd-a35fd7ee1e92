package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/12
 */
public class CollectionOperateUtils {

    /**
     * 将Map<K,List<V>>根据特定的优先原则，过滤出每一个K的第一优先Value
     * 如果某一个key的value是空，则这一个key会被忽略掉
     * @param oldMap
     * @param function
     * @param <K>
     * @param <V>
     * @return
     */
    public static <K,V> Map<K,V> filterValueList(Map<K, List<V>> oldMap, Function<List<V>, V> function) {
        if (MapUtils.isEmpty(oldMap)) {
            return Maps.newHashMap();
        }

        return oldMap.entrySet().stream().filter(entry -> CollectionUtils.isNotEmpty(entry.getValue())).collect(Collectors.toMap(Map.Entry::getKey, entry -> function.apply(entry.getValue())));

    }
}
