package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.google.common.collect.Iterables;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.ChannelDeliveryService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.DeliveryCheckResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.DeliveryLaunchResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.DeliveryLaunchFailedEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryWarnEventPublisher;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ChannelStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 发起配送领域服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
@Slf4j
@Service
public class LaunchThirdPartyDeliveryDomainService {

	@Resource
	private OrderSystemClient orderSystemClient;
	@Resource
	private LaunchDeliveryStrategyDomainService launchDeliveryStrategyDomainService;
	@Resource
	private ChannelDeliveryService channelDeliveryService;
	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private DeliveryWarnEventPublisher deliveryWarnEventPublisher;

	/**
	 * 发起配送领域服务
	 * 其中包括订单信息查询，基础校验，渠道配送前置校验和发起校验等流程处理
	 * 每一个配送渠道的服务包，都会记录运单
	 * 遇到需要重试的失败，会根据运单状态，从入口触发整体重试，重试会跳过已经成功/失败的运单
	 *
	 * @param orderInfo   订单key
	 * @param deliveryPoi 配送门店
	 * @return 配送发起结果
	 */
	@CatTransaction
	public Optional<Failure> launchDelivery(OrderInfo orderInfo, SelfBuiltDeliveryPoi deliveryPoi,
	                                        boolean acceptRetry, boolean tryFailedChannel, boolean tryNewChannel) {
		log.info("LaunchDeliveryDomainService.launchDelivery begin, orderInfo={}, deliveryStore={}", orderInfo, deliveryPoi);
		Preconditions.checkNotNull(orderInfo, "orderKey is null");
		Preconditions.checkNotNull(deliveryPoi, "deliveryStore is null");

		//订单当前状态是否可配送
		if (!orderInfo.canLaunchDelivery()) {
			log.warn("Order[{}] doesn't meed delivery condition, will ignore this delivery launching.", orderInfo);
			return Optional.of(new Failure(false, FailureCodeEnum.ORDER_STATUS_ERROR));
		}

		if (orderInfo.getReceiver() == null || !orderInfo.getReceiver().isDeliverable()) {
			log.warn("Receiver information[{}] invalid, will ignore this delivery launching.", orderInfo.getReceiver());
			Cat.logEvent("DELIVERY_LAUNCH_FAILED", "RECEIVER_ADDRESS_INVALID");
			return Optional.of(new Failure(false, FailureCodeEnum.INVALID_RECEIVER_ADDRESS));
		}

		DeliveryPlan deliveryPlan = launchDeliveryStrategyDomainService.getDeliveryPlan(deliveryPoi);
		switch (deliveryPlan.getDeliveryPlanType()) {
			case GIVE_UP_DELIVERY:
				log.warn("Delivery strategy is GIVE_UP_DELIVERY, will ignore this delivery launching.");
				return Optional.empty();

			case SEQUENTIAL:
				return launchDeliveryWithSequential(orderInfo, deliveryPoi, deliveryPlan, acceptRetry, tryFailedChannel, tryNewChannel);

			case MULTI_CHANNEL_GRABBING:
			default:
				throw new IllegalStateException(String.format("Not supported plan[%s] for now.", deliveryPlan.getDeliveryPlanType()));
		}
	}

	private Optional<Failure> launchDeliveryWithSequential(OrderInfo orderInfo, SelfBuiltDeliveryPoi deliveryPoi, DeliveryPlan deliveryPlan,
	                                                       boolean acceptRetry, boolean tryFailedChannel, boolean tryNewChannel) {
		List<DeliveryOrder> failedDeliveryOrders = new ArrayList<>();

		boolean needRetry = false;
		String finalTryFailReason = null;
		Map<ChannelAndServicePackage, DeliveryOrder> historyDeliveryOrderMap = queryHistoryDeliveryOrders(orderInfo.getOrderKey());

		//轮询配送渠道
		for (ChannelStoreConfig channelStore : deliveryPlan.getDeliveryChannelStoreList()) {

			List<String> orderedServicePackageCodes = channelStore.getOrderedServicePackageCodes();
			//配置问题，未配置服务包
			if (CollectionUtils.isEmpty(orderedServicePackageCodes)) {
				log.warn("No service package configured for channelStore[{}], will ignore this delivery launching.", channelStore);
				Cat.logEvent("DELIVERY_LAUNCH_FAILED", "NO_SERVICE_PACKAGE_CONFIGURED");
				continue;
			}
			//轮询服务包，进行配送前置校验并发起配送
			for (String servicePackageCode : orderedServicePackageCodes) {
				//优先判断历史运单是否存在
				DeliveryOrder deliveryOrder =
						historyDeliveryOrderMap.get(new ChannelAndServicePackage(channelStore.getDeliveryChannel().getChannel(), servicePackageCode));
				//运单已经存在
				if (deliveryOrder != null) {
					switch (deliveryOrder.getStatus()) {
						case INIT:
							//直接重试当前运单
							break;
						case DELIVERY_LAUNCHED:
						case WAITING_TO_ASSIGN_RIDER:
						case RIDER_ASSIGNED:
						case RIDER_ARRIVED_SHOP:
						case RIDER_TAKEN_GOODS:
						case DELIVERY_DONE:
							//跳过当前配送中运单
							if (tryNewChannel) {
								continue;
							} else {
								//已经发起配送成功，直接返回
								orderSystemClient.syncDeliveryChangeToOrderSystem(deliveryOrder);
								return Optional.empty();
							}

						case DELIVERY_REJECTED:
						case DELIVERY_FAILED:
						case DELIVERY_CANCELLED:
							//如果允许重试已经失败的渠道，重新创建新渠道配送
							if (tryFailedChannel) {
								deliveryOrder = new DeliveryOrder(orderInfo, channelStore.getDeliveryChannel().getCode(), servicePackageCode);
								deliveryOrderRepository.saveDeliveryOrder(deliveryOrder);
								break;
							} else {
								continue;
							}

						default:
							continue;
					}

				} else {
					//生成新运单
					deliveryOrder = new DeliveryOrder(orderInfo, channelStore.getDeliveryChannel().getCode(), servicePackageCode);
					deliveryOrderRepository.saveDeliveryOrder(deliveryOrder);
				}

				//进行前置校验
				DeliveryCheckResult checkResult = channelDeliveryService.checkDeliverable(orderInfo, channelStore, servicePackageCode);
				//前置检查通过，开始发起配送
				if (checkResult.isSuccess()) {
					//发起配送
					DeliveryLaunchResult deliveryLaunchResult = channelDeliveryService.launchDelivery(deliveryOrder, deliveryPoi, orderInfo);

					//发起配送成功，直接返回
					if (deliveryLaunchResult.isSuccess()) {
						deliveryOrder.onEvent(DeliveryEventEnum.LAUNCH_DELIVERY, LocalDateTime.now());
						deliveryOrder.setChannelDeliveryId(deliveryLaunchResult.getChannelDeliveryId());
						deliveryOrder.activate();
						if (!deliveryOrderRepository.saveDeliveryOrder(deliveryOrder)) {
							log.error("Save delivery order failed, need retry");
							return Optional.of(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
						}
						orderSystemClient.syncDeliveryChangeToOrderSystem(deliveryOrder);
						return Optional.empty();

					} else {
						failedDeliveryOrders.add(deliveryOrder);
						finalTryFailReason = deliveryLaunchResult.getRejectInfo().getExceptionDescription();
						//需要重试发起配送
						if (deliveryLaunchResult.isNeedRetry() && acceptRetry) {
							needRetry = true;
							log.warn("Delivery launch failed with retryable result, will retry soon. deliveryChannel={}, servicePackage={}",
									channelStore.getDeliveryChannel(), servicePackageCode);
							continue;
						} else {
							deliveryOrder.onEvent(DeliveryEventEnum.DELIVERY_REJECT, LocalDateTime.now());
							deliveryOrder.onExceptionWithChangeTime(deliveryLaunchResult.getRejectInfo(), LocalDateTime.now());
							Cat.logEvent("DELIVERY_LAUNCH_FAILED", "LAUNCH_[" + deliveryOrder.getDeliveryChannel() + "]_FAILED");
							deliveryWarnEventPublisher.postEvent(
									new DeliveryLaunchFailedEvent(deliveryOrder, deliveryLaunchResult.getRejectInfo().getExceptionDescription())
							);
							log.warn("Delivery launch failed，deliveryOrder={}", deliveryOrder);
						}
					}
				} else {
					failedDeliveryOrders.add(deliveryOrder);
					finalTryFailReason = checkResult.getFailInfo().getExceptionDescription();
					//需要重试前置检查
					if (checkResult.isNeedRetry() && acceptRetry) {
						needRetry = true;
						log.warn("Delivery check failed with retryable result, will retry soon. deliveryChannel={}, servicePackage={}",
								channelStore.getDeliveryChannel(), servicePackageCode);
						continue;
					} else {
						deliveryOrder.onEvent(DeliveryEventEnum.DELIVERY_REJECT, LocalDateTime.now());
						deliveryOrder.onExceptionWithChangeTime(checkResult.getFailInfo(), LocalDateTime.now());
						Cat.logEvent("DELIVERY_LAUNCH_FAILED", "CHECK_[" + deliveryOrder.getDeliveryChannel() + "]_FAILED");
						deliveryWarnEventPublisher.postEvent(
								new DeliveryLaunchFailedEvent(deliveryOrder, checkResult.getFailInfo().getExceptionDescription())
						);
						log.warn("Delivery check failed，deliveryOrder={}", deliveryOrder);
					}
				}

				if (!deliveryOrderRepository.saveDeliveryOrder(deliveryOrder)) {
					return Optional.of(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
				}
			}
		}

		if (!needRetry && CollectionUtils.isNotEmpty(failedDeliveryOrders) && !tryNewChannel) {
			orderSystemClient.syncDeliveryChangeToOrderSystem(Iterables.getLast(failedDeliveryOrders));
		}
		return Optional.of(new Failure(
				needRetry,
				FailureCodeEnum.LAUNCH_DELIVERY_FAILED,
				StringUtils.defaultString(finalTryFailReason, "无可用配送渠道")
		));
	}

	private Map<ChannelAndServicePackage, DeliveryOrder> queryHistoryDeliveryOrders(OrderKey orderKey) {
		List<DeliveryOrder> deliveryOrders = deliveryOrderRepository.getDeliveryOrders(orderKey);

		return Optional.ofNullable(deliveryOrders)
				.orElse(new ArrayList<>())
				.stream()
				.collect(Collectors.toMap(
						it -> new ChannelAndServicePackage(DeliveryChannelEnum.valueOf(it.getDeliveryChannel()), it.getChannelServicePackageCode()),
						it -> it,
						(o1, o2) -> o2)
				);
	}

	@Getter
	@ToString
	@EqualsAndHashCode
	@AllArgsConstructor
	private static class ChannelAndServicePackage {

		private final DeliveryChannelEnum deliveryChannel;

		private final String servicePackageCode;
	}
}
