package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;


import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TOrderIdentifier;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryIdDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PageRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PageResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.StaffRider;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 运单仓储服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
public interface DeliveryOrderRepository {

	/**
	 * 保存运单信息
	 */
	void save(DeliveryOrder deliveryOrder);

	/**
	 * 保存运单信息
	 */
	boolean saveDeliveryOrder(DeliveryOrder deliveryOrder);

	/**
	 * 更新运单激活状态
	 */
	void saveDeliveryOrderActiveStatus(DeliveryOrder deliveryOrder);

	/**
	 * 根据订单key查询所有该订单的非终态的运单信息
	 * 包括所有非终态的运单
	 */
	List<DeliveryOrder> getOpenDeliveryOrders(OrderKey orderKey);

	List<DeliveryOrder> getOpenDeliveryOrdersWithRoute(OrderKey orderKey);

	/**
	 * 通过订单key查询运单信息
	 */
	List<DeliveryOrder> getDeliveryOrders(OrderKey orderKey);

	List<DeliveryOrder> getDeliveryOrdersWithRoute(OrderKey orderKey);

	/**
	 * 通过订单key查询运单信息，强制走主库
	 */
	List<DeliveryOrder> getDeliveryOrdersForceMaster(Long orderId);

	List<DeliveryOrder> getDeliveryOrdersMaxWithOrderId(Long orderId);

	List<DeliveryOrder> getDeliveryOrdersAllWithOrderId(Long orderId);

	List<DeliveryOrder> getDeliveryOrdersAllWithOrderIdSlave(Long orderId);

	List<DeliveryOrder> getDeliveryOrdersForceMasterWithTenant(Long orderId,Long tenantId,Long storeId);

	List<DeliveryOrder> getDeliveryOrdersWithTenantOrderId(Long orderId,Long tenantId,Long storeId);

	@Deprecated
	List<DeliveryOrder> getDeliveryOrderSlave(Long orderId);

	List<DeliveryOrder> getDeliveryOrderSlave(Long orderId,Long tenantId,Long storeId);

	/**
	 * @return 最新的运单信息
	 */
	Optional<DeliveryOrder> getLastDeliveryOrderSlave(Long orderId,Long tenantId,Long storeId);

	/**
	 * 通过订单key查询激活的运单，强制走主库
	 */
	Optional<DeliveryOrder> getActiveDeliveryOrderForceMaster(Long orderId);

	Optional<DeliveryOrder> getActiveDeliveryOrderWithTenant(Long orderId,Long tenantId,Long storeId);

	/**
	 * 通过订单id查询激活的运单，或者当前最新一个运单，强制走主库
	 */
	Optional<DeliveryOrder> getCurrentDeliveryOrderForceMaster(Long orderId,Long tenantId,Long storeId);

	/**
	 * 通过订单id查询运单，强制走主库
	 */
	Optional<DeliveryOrder> getDeliveryOrderFromMaster(Long orderId,Long tenantId,Long storeId);

	/**
	 * 通过id查询运单信息
	 */
	DeliveryOrder getDeliveryOrder(Long id);

	DeliveryOrder getDeliveryOrderWithTenant(Long id,Long tenantId,Long storeId);

	/**
	 * 通过id查询运单信息
	 */
	@Deprecated
	Optional<DeliveryOrder> getDeliveryOrderForceMaster(Long id);

	/**
	 * 通过渠道+渠道运单id查询运单信息
	 */
	@Deprecated
	DeliveryOrder getDeliveryOrder(ThirdDeliveryChannelEnum deliveryChannel, String channelDeliveryId);

	/**
	 * 通过门店id+渠道+配送状态 查询运单列表
	 */
	PageResult<DeliveryOrder> pageQueryThirdDeliveryOrder(Long tenantId, Long storeId, List<Integer> statusList, Boolean filterException,
														  PageRequest pageRequest);


	/**
	 * 通过门店id+渠道+配送状态 查询运单数量
	 */
	Map<DeliveryStatusEnum, Integer> queryDeliveryOrderCount(Long tenantId, Long storeId, List<Integer> statusList);
	/**
	 * 通过门店id+渠道+配送状态 查询异常运单数量
	 */
	Integer queryThirdDeliveryOnExceptionOrderCount(Long tenantId, Long storeId, List<Integer> statusList);

	/**
	 * 通过订单keys批量查询运单信息
	 * @param orderKeys
	 * todo
	 * @return
	 */
	Map<OrderKey, List<DeliveryOrder>> getDeliveryOrdersByOrderKeys(List<OrderKey> orderKeys);

	/**
	 * 查询门店的未超时的运单（超时指当前时间超过预计送达时间x个小时）
	 */
	List<DeliveryOrder> getNotTimeoutDeliveryOrder(Long storeId,Long tenantId);

	List<DeliveryOrder> getDHNotTimeoutDeliveryOrder(Long storeId,Long tenantId);

	List<DeliveryOrder> getDeliveryOrdersByOrderIdList(List<Long> orderList);

	List<DeliveryOrder> getDeliveryOrdersByOrderIdListSlave(List<Long> orderList);

	List<DeliveryOrder> getDeliveryOrdersByOrderIdListWithTenant(List<Long> orderList,Long tenantId,Long storeId);


	@Deprecated
	List<Long> getNotTimeoutDeliveryOrderId(List<Long> storeIdList);


	@Deprecated
	List<DeliveryIdDO> getDeliveryIdList(List<Long> orderIdList);

	@Deprecated
	List<TOrderIdentifier> getDeliveryOrderList(List<Long> idList);

	Optional<DeliveryOrder> getActiveDeliveryOrderForceMaster(Long orderId,Long fulfillmentOrderId,Long tenantId,Long storeId);

	void updateRiderInfo(Long deliveryOrderId, StaffRider courier, Long tenantId, Long storeId);

	List<DeliveryOrderDO> batchQueryOriginWaybillNo(Long tenantId, Collection<Long> storeIds, List<Long> orderIds, List<Integer> statusCodes);

	/**
	 * 通过履约订单id查询配送单，强制走主库
	 * 只查询最新的一条数据，不一定是生效中的运单
	 */
	Optional<DeliveryOrder> getLatestDeliveryOrderForceMaster(Long orderId);
}
