package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderRoute;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;

import java.util.List;

public interface DeliveryOrderRouteRepository {

    void saveDeliveryOrderRoute(DeliveryOrder deliveryOrder);

    void saveRiderDeliveryOrderRoute(RiderDeliveryOrder deliveryOrder);

    DeliveryOrderRoute queryOrderRouteByFulfillOrderId(Long fulfillOrderId);

    List<DeliveryOrderRoute> queryOrderRouteByOrderId(Long orderId);

    List<DeliveryOrderRoute> queryOrderRouteByOrderIdList(List<Long> orderIdList);

    DeliveryOrderRoute queryOrderRouteByOrderIdWithMax(Long orderId);

    List<DeliveryOrderRoute> queryOrderRouteByOrderIdOrFulfill(Long orderId);

    List<DeliveryOrderRoute> queryOrderRouteByOrderIdOrFulfillSlave(Long orderId);

    List<DeliveryOrderRoute> queryOrderRouteByOrderIdListSlave(List<Long> orderIdList);

}
