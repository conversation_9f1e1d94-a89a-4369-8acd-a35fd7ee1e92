package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryAbility;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/12
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DeliveryAbility {

    /**
     * 是否开通聚合运力
     */
    private boolean hasAggrDeliveryChannel;

    /**
     * 是否开通麦芽田.
     */
    private boolean hasMaltfarmDeliveryChannel;

    /**
     * 是否开通 TMS 内部对接的第三方配送渠道
     */
    private boolean hasThirdPartDeliveryChannel;

    /**
     * 是否开通海葵配送
     */
    private boolean hasHaiKuiDelivery;

    private DeliveryLaunchTypeEnum deliveryLaunchType;

    /**
     * 是否开通青云聚信平台
     */
    private boolean hasDapDeliveryChannel;

    /**
     * 既没有对接老得三方运力，也没有对接聚合运力平台
     *
     * @return
     */
    public boolean hasNoAggrOrThirdDeliveryAbility() {
        return !hasAggrDeliveryChannel && !hasThirdPartDeliveryChannel;
    }

    public boolean canManualLaunchDelivery() {
        return hasAggrDeliveryChannel && DeliveryLaunchTypeEnum.MANUAL_LAUNCH_DELIVERY.equals(deliveryLaunchType);
    }

    public TDeliveryAbility convertToThriftDTO() {
        TDeliveryAbility tDeliveryAbility = new TDeliveryAbility();
        tDeliveryAbility.setHasAggrDeliveryChannel(this.isHasAggrDeliveryChannel());
        tDeliveryAbility.setHasThirdPartDeliveryChannel(this.isHasThirdPartDeliveryChannel());
        tDeliveryAbility.setDeliveryLaunchType(this.getDeliveryLaunchType().getCode());
        return tDeliveryAbility;
    }
}
