package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransferOperateInfo {

    private DeliveryPlatformEnum deliveryPlatformEnum;

    private Long operatorId;

    private String operatorName;
}

