package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;


import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
public class NewSupplyRiderLocationRepository {

    @Resource
    private DeliveryPlatformClient deliveryPlatformClient;

    @Resource(name = "squirrelRiderLocationAggDeliveryRepository")
    private SquirrelNewSupplyRiderLocationRepository squirrelRiderLocationAggDeliveryRepository;

    @Resource(name = "squirrelRiderLocationSelfMerchantDeliveryRepository")
    private SquirrelNewSupplyRiderLocationRepository squirrelRiderLocationSelfMerchantDeliveryRepository;

    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public Optional<CoordinatePoint> queryRiderLocationFromSquirrel(Long orderId, DeliveryChannel deliveryChannel) {
        if (Objects.isNull(deliveryChannel)) {
            log.error("queryRiderLocationFromSquirrel, deliveryChannel is null");
            return Optional.empty();
        }
        Integer deliveryPlatFormCode = deliveryChannel.getDeliveryPlatFormCode();
        if (Objects.isNull(deliveryPlatFormCode)) {
            log.error("queryRiderLocationFromSquirrel, deliveryPlatFormCode is null");
            return Optional.empty();
        }
        DeliveryPlatformEnum deliveryPlatformEnum = DeliveryPlatformEnum.enumOf(deliveryPlatFormCode);
        if (Objects.isNull(deliveryPlatformEnum)) {
            log.error("queryRiderLocationFromSquirrel, deliveryPlatformEnum is null");
            return Optional.empty();
        }

        Optional<CoordinatePoint> coordinatePointOptional = Optional.empty();
        switch (deliveryPlatformEnum) {
            case MALT_FARM_DELIVERY_PLATFORM:
            case DAP_DELIVERY_PLATFORM:
                coordinatePointOptional = squirrelRiderLocationAggDeliveryRepository.queryRiderLocation(orderId);
                break;
            case MERCHANT_SELF_DELIVERY:
                coordinatePointOptional = squirrelRiderLocationSelfMerchantDeliveryRepository.queryRiderLocation(orderId);
                break;
            default:
                log.warn("当前平台[{}]暂不支持查询骑手坐标", deliveryPlatformEnum);
        }
        return coordinatePointOptional;
    }

    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public Optional<CoordinatePoint> queryRiderLocationFromClient(DeliveryOrder deliveryOrder) {
        if (deliveryOrder.getStatus().needQueryRiderLocation()) {
            return deliveryPlatformClient.queryRiderLocation(deliveryOrder);
        } else {
            return Optional.empty();
        }
    }

}
