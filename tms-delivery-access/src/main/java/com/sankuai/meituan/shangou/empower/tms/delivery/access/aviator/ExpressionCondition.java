package com.sankuai.meituan.shangou.empower.tms.delivery.access.aviator;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.aviator.interval.Interval;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-07-09
 * @email <EMAIL>
 */
@Data
public class ExpressionCondition {

    private String name;

    private String identifier;

    private String formula;

    private Interval interval;

}
