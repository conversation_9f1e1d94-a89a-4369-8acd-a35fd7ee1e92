package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/8
 */
public class MoneyUtil {

	private static final BigDecimal ONE_HUNDRED = BigDecimal.valueOf(100);

	public static Optional<Double> fromCentToYuan(Integer cent) {
		return Optional.ofNullable(cent)
				.map(BigDecimal::valueOf)
				.map(it -> it.divide(ONE_HUNDRED, 2, RoundingMode.HALF_UP))
				.map(BigDecimal::doubleValue);
	}

	public static int compare(BigDecimal m1, BigDecimal m2) {
		return defaultZeroIfNull(m1).compareTo(defaultZeroIfNull(m2));
	}

	public static BigDecimal defaultZeroIfNull(BigDecimal value) {
		return Optional.ofNullable(value).orElse(BigDecimal.ZERO);
	}

	public static BigDecimal fromYuanToCenter(BigDecimal yuan) {

		return Optional.ofNullable(yuan)
				.map(it -> it.multiply(ONE_HUNDRED)).orElse(BigDecimal.ZERO);
	}

	public static Optional<BigDecimal> fromFenToYuan(Integer cent) {
		return Optional.ofNullable(cent)
				.map(BigDecimal::valueOf)
				.map(it -> it.divide(ONE_HUNDRED, 2, RoundingMode.HALF_UP));
	}

}
