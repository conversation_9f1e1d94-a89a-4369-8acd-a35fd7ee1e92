package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@ToString(callSuper = true)
public class FourWheelDispatchCmd extends BaseOrderPlatformDeliveryOperateCmd{

    @Setter
    private Integer deliveryType;

    @Setter
    private List<FourWheelCmdDto> fourWheelCmdDtoList;

    public FourWheelDispatchCmd(Long tenantId, Long offlineStoreId, OrderInfo orderInfo, Integer channelId, Operator operator) {
        super(tenantId, offlineStoreId, orderInfo, channelId, operator);
    }


}
