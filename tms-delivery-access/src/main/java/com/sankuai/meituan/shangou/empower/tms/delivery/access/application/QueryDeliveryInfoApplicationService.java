package com.sankuai.meituan.shangou.empower.tms.delivery.access.application;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListOrderIdRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListOrderIdResponse;
import com.meituan.shangou.saas.order.management.client.enums.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiSearchRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiSearchResponse;
import com.sankuai.inf.kms.pangolin.api.cat.Cat;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryRedirectModule;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.DeliveryOrderLog;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.DeliveryOrderLogRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.CollectionOperateUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.wrapper.AggDeliveryWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionCountRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.DeliveryOrderEsAliasDao;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.constants.DeliveryOrderConstants;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.po.DeliveryOrderEsPo;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryIdDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantChannelStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PageRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PageResult;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/8
 */
@Service
@Slf4j
public class QueryDeliveryInfoApplicationService {

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private DeliveryOrderLogRepository deliveryOrderLogRepository;

	@Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    @Resource
    private OrderSystemClient orderSystemClient;

    @Resource
    private DeliveryOrderEsAliasDao deliveryOrderEsAliasDao;

    @Autowired
    protected TenantSystemClient tenantSystemClient;

    @Resource
    private PoiThriftService poiThriftServiceClient;

    @Autowired
    private DeliveryChannelApplicationService deliveryChannelApplicationService;

    @Autowired
    private AggDeliveryConfigApplicationService aggDeliveryConfigApplicationService;

    @Autowired
    private AggDeliveryWrapper aggDeliveryWrapper;

    /**
     * 根据订单查询运单数据以及可以操作的发配送类型
     * 如果该订单没有运单，则生成的orderDeliveryDetail中只有order信息和可操作的发配送类型
     * 如果该订单有运单，则除了order信息和可操作的发配送类型之外，还有配送信息
     *
     * @param orderKeys
     * @param orderKeyDeliveryConditionInOrderMap
     * @return
     */
    public List<OrderDeliveryDetail> queryOrderDeliveryDetail(List<OrderKey> orderKeys, Map<OrderKey, DeliveryConditionInOrder> orderKeyDeliveryConditionInOrderMap) {

        if (CollectionUtils.isEmpty(orderKeys)) {
            return Lists.newArrayList();
        }
        //根据订单key查询运单list
        Map<OrderKey, List<DeliveryOrder>> deliveryOrdersByOrderKeys = deliveryOrderRepository.getDeliveryOrdersByOrderKeys(orderKeys);

        Map<OrderKey, List<OriginWaybillInfo>> originWaybillMap = buildOriginWaybillMap(deliveryOrdersByOrderKeys);

        //取订单上面的有效运单
        Map<OrderKey, DeliveryOrder> availableDeliveryOrders = CollectionOperateUtils.filterValueList(deliveryOrdersByOrderKeys, this::filterAvailableDeliveryOrder);

        //获取门店配送配置信息
        Map<Integer,DeliveryAbility> storeDeliveryConfigMap = getAggrDeliveryAbility(orderKeys);

        Map<Long, List<DeliveryOrderLog>> deliveryIdRelatedLogMap = deliveryOrderLogRepository.getDeliveryOrderLogByDeliveryOrderIds(availableDeliveryOrders.values().stream().map(DeliveryOrder::getId).collect(Collectors.toList()));

        return aggregateDeliverInfo(orderKeyDeliveryConditionInOrderMap, availableDeliveryOrders, deliveryIdRelatedLogMap, storeDeliveryConfigMap, originWaybillMap);
    }

    private Map<OrderKey, List<OriginWaybillInfo>> buildOriginWaybillMap(Map<OrderKey, List<DeliveryOrder>> deliveryOrdersByOrderKeys) {
        Map<OrderKey, List<OriginWaybillInfo>> originWaybillMap = new HashMap<>();
        deliveryOrdersByOrderKeys.forEach((key, deliveryOrders) -> {
            if (CollectionUtils.isEmpty(deliveryOrders)) {
                return;
            }
            List<OriginWaybillInfo> originWaybillInfoList = deliveryOrders.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getOriginWaybillNo()))
                    .map(item -> {
                        OriginWaybillInfo originWaybillInfo = new OriginWaybillInfo();
                        originWaybillInfo.setDeliveryOrderId(item.getId());
                        originWaybillInfo.setDeliveryStatus(item.getStatus().getCode());
                        originWaybillInfo.setOriginWaybillNo(item.getOriginWaybillNo());
                        return originWaybillInfo;
                    }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(originWaybillInfoList)) {
                return;
            }
            originWaybillMap.put(key, originWaybillInfoList);
        });
        return originWaybillMap;
    }

    /**
     * 根据订单查询运单数据以及可以操作的发配送类型
     * 如果该订单没有运单，则生成的orderDeliveryDetail中只有order信息和可操作的发配送类型
     * 如果该订单有运单，则除了order信息和可操作的发配送类型之外，还有配送信息
     *
     * @param orderKeys
     * @return
     */
    @MethodLog(logRequest = false, logResponse = true)
    public List<OrderDeliveryDetail> queryOrderDeliveryDetailByOrderIdList(List<QueryOrderDeliveryInfoKey> orderKeys) {

        if (CollectionUtils.isEmpty(orderKeys)) {
            return Lists.newArrayList();
        }
        //根据运单id查询运单list
        List<Long> orderIdList = orderKeys.stream().map(QueryOrderDeliveryInfoKey::getEmpowerOrderId).collect(Collectors.toList());
        List<DeliveryOrder> deliveryOrders = deliveryOrderRepository.getDeliveryOrdersByOrderIdList(orderIdList);

        Map<OrderKey, List<DeliveryOrder>> deliveryOrdersByOrderKeys = deliveryOrders.stream()
                .collect(Collectors.toMap(deliveryOrder -> new OrderKey(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getOrderId()), Lists::newArrayList, (newList, oldList) -> {
                    oldList.addAll(newList);
                    return oldList;
                }));

        //取订单上面的有效运单
        Map<OrderKey, DeliveryOrder> availableDeliveryOrders = CollectionOperateUtils.filterValueList(deliveryOrdersByOrderKeys, this::filterAvailableDeliveryOrder);

        Map<Long, DeliveryOrder> deliveryOrderMap = availableDeliveryOrders.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey().getOrderId(), Map.Entry::getValue));

        Map<OrderKey, DeliveryConditionInOrder> orderKeyDeliveryConditionInOrderMap = orderKeys.stream().
                collect(Collectors.toMap(o -> new OrderKey(o.tenantId, deliveryOrderMap.get(o.empowerOrderId).getStoreId(), o.empowerOrderId),
                        o -> DeliveryConditionInOrder.builder()
                                .isSelfDelivery(o.selfDeliveryMark)
                                .deliveryMethod(o.deliveryMethod)
                                .orderSource(o.orderSource)
                                .orderStatus(o.orderStatus).build(), (o, n) -> n));


        //获取门店配送配置信息
        Map<Integer,DeliveryAbility> storeDeliveryConfigMap = getAggrDeliveryAbility(new ArrayList<>(availableDeliveryOrders.keySet()));

        Map<Long, List<DeliveryOrderLog>> deliveryIdRelatedLogMap = deliveryOrderLogRepository.getDeliveryOrderLogByDeliveryOrderIds(availableDeliveryOrders.values().stream().map(DeliveryOrder::getId).collect(Collectors.toList()));
        // 非pc端 不展示三方运单号
        return aggregateDeliverInfo(orderKeyDeliveryConditionInOrderMap, availableDeliveryOrders, deliveryIdRelatedLogMap, storeDeliveryConfigMap, new HashMap<>());
    }

    public PageResult<OrderDeliveryDetail> pageQueryThirdDeliveryOrder(Long tenantId, Long storeId, List<Integer> statusList,  Boolean filterException, PageRequest pageRequest) {
        PageResult<DeliveryOrder> deliveryOrderPageResult = deliveryOrderRepository.pageQueryThirdDeliveryOrder(tenantId, storeId, statusList, filterException, pageRequest);

        List<DeliveryOrder> deliveryOrderList = deliveryOrderPageResult.getInfo();

        if (CollectionUtils.isEmpty(deliveryOrderList)) {
            return new PageResult<>(Collections.emptyList(), pageRequest, deliveryOrderPageResult.getTotal());
        }

        Map<Long/*deliveryOrderId*/, List<DeliveryOrderLog>> deliveryIdRelatedLogMap =
                deliveryOrderLogRepository.getDeliveryOrderLogByDeliveryOrderIds(Fun.map(deliveryOrderList, DeliveryOrder::getId));


        return new PageResult<>(aggregateDeliverInfo(deliveryOrderList, deliveryIdRelatedLogMap), pageRequest, deliveryOrderPageResult.getTotal());
    }

    public Map<DeliveryStatusEnum, Integer> queryThirdDeliveryOrderCount(Long tenantId, Long storeId) {
        //为了防止扫描的行数过多，不要查终态运单的数量
        List<Integer> statusList = Arrays.asList(DeliveryStatusEnum.INIT.getCode(),
                DeliveryStatusEnum.DELIVERY_LAUNCHED.getCode(),
                DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode(),
                DeliveryStatusEnum.RIDER_ASSIGNED.getCode(),
                DeliveryStatusEnum.RIDER_ARRIVED_SHOP.getCode(),
                DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode());

        return deliveryOrderRepository.queryDeliveryOrderCount(tenantId, storeId, statusList);
    }

    public Integer queryThirdDeliveryOnExceptionOrderCount(Long tenantId, Long storeId) {
        //为了防止扫描的行数过多，不要查终态运单的数量
        List<Integer> statusList = Arrays.asList(DeliveryStatusEnum.DELIVERY_LAUNCHED.getCode(), DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode(),
                DeliveryStatusEnum.RIDER_ASSIGNED.getCode(), DeliveryStatusEnum.RIDER_ARRIVED_SHOP.getCode(),
                DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode());

        return deliveryOrderRepository.queryThirdDeliveryOnExceptionOrderCount(tenantId, storeId, statusList);
    }


    private List<OrderDeliveryDetail> aggregateDeliverInfo(List<DeliveryOrder> deliveryOrderList, Map<Long, List<DeliveryOrderLog>> deliveryIdRelatedLogMap) {

        // 获取配送平台接单的配送运单log
        Map<Long, DeliveryOrderLog> waitToAssignRiderDeliveryOrderLogs = getWaitToAssignRiderDeliveryOrderLogs(deliveryIdRelatedLogMap);

        return deliveryOrderList.stream().map(deliveryOrder -> {

            DeliveryOrderLog deliveryOrderLog = null;
            DeliveryOrderLog deliveryOrderCurrentStatusLog = null;

            deliveryOrderLog = waitToAssignRiderDeliveryOrderLogs.get(deliveryOrder.getId());
            deliveryOrderCurrentStatusLog = getDeliveryOrderCurrentStatusLog(deliveryOrder, deliveryIdRelatedLogMap.get(deliveryOrder.getId()));

            //todo check 歪马现在先把配置写死
            DeliveryAbility deliveryAbility = new DeliveryAbility(true, false,
                    false, false, DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY , true);


            return OrderDeliveryDetail.buildFromDeliveryOrder(deliveryOrder, deliveryAbility, deliveryOrderLog, deliveryOrderCurrentStatusLog);
        }).collect(Collectors.toList());
    }

    private List<OrderDeliveryDetail> aggregateDeliverInfo(Map<OrderKey, DeliveryConditionInOrder> orderKeyDeliveryConditionInOrderMap,
                                                           Map<OrderKey, DeliveryOrder> availableDeliveryOrders,
                                                           Map<Long, List<DeliveryOrderLog>> deliveryIdRelatedLogMap,
                                                           Map<Integer,DeliveryAbility> deliveryAbilityMap,
                                                           Map<OrderKey, List<OriginWaybillInfo>> originWaybillMap) {

        // 获取配送平台接单的配送运单log
        Map<Long, DeliveryOrderLog> waitToAssignRiderDeliveryOrderLogs = getWaitToAssignRiderDeliveryOrderLogs(deliveryIdRelatedLogMap);

        return orderKeyDeliveryConditionInOrderMap.entrySet().stream().map(entry -> {
            OrderKey orderKey = entry.getKey();
            DeliveryConditionInOrder conditionInOrder = entry.getValue();
            DeliveryOrder deliveryOrder = availableDeliveryOrders.get(orderKey);

            DeliveryOrderLog deliveryOrderLog = null;
            DeliveryOrderLog deliveryOrderCurrentStatusLog = null;
            if (deliveryOrder != null) {
                deliveryOrderLog = waitToAssignRiderDeliveryOrderLogs.get(deliveryOrder.getId());
                deliveryOrderCurrentStatusLog = getDeliveryOrderCurrentStatusLog(deliveryOrder, deliveryIdRelatedLogMap.get(deliveryOrder.getId()));
            }
            DeliveryAbility deliveryAbility=new DeliveryAbility();
            if (deliveryOrder != null) {
                DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(
                        DynamicOrderBizType.findOf(deliveryOrder.getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
                int channelId = orderBizType.getChannelId();
                if (deliveryAbilityMap.containsKey(channelId)) {
                    deliveryAbility = deliveryAbilityMap.get(channelId);
                } else if (deliveryAbilityMap.containsKey(DynamicChannelType.MEITUAN.getChannelId())) {
                    deliveryAbility = deliveryAbilityMap.get(DynamicChannelType.MEITUAN.getChannelId());
                }
                if (deliveryAbility == null) {
                    deliveryAbility = new DeliveryAbility();
                }
            }
            List<OriginWaybillInfo> originWaybillInfoList = originWaybillMap.get(orderKey);
            return OrderDeliveryDetail.buildFromDeliveryOrder(deliveryOrder, orderKey, conditionInOrder, deliveryAbility, deliveryOrderLog,
                    deliveryOrderCurrentStatusLog, originWaybillInfoList);
        }).collect(Collectors.toList());
    }

    private DeliveryOrderLog getDeliveryOrderCurrentStatusLog(DeliveryOrder deliveryOrder, List<DeliveryOrderLog> deliveryOrderLogs) {
        if (CollectionUtils.isNotEmpty(deliveryOrderLogs)) {
            DeliveryStatusEnum deliveryStatusEnum = deliveryOrder.getStatus();
            for (DeliveryOrderLog log : deliveryOrderLogs) {
                if (log.getChangeEvent() == DeliveryEventEnum.getEventByStatus(deliveryStatusEnum)) {
                    return log;
                }
            }
        }
        return null;
    }

    private Map<Long, DeliveryOrderLog> getWaitToAssignRiderDeliveryOrderLogs(Map<Long, List<DeliveryOrderLog>> deliveryIdRelatedLogMap) {
        Map<Long, DeliveryOrderLog> waitToAssignRiderDeliveryOrderLogs = new HashMap<>();
        for (Map.Entry<Long, List<DeliveryOrderLog>> entry : deliveryIdRelatedLogMap.entrySet()) {
            for (DeliveryOrderLog log : entry.getValue()) {
                if (log.getChangeEvent() == DeliveryEventEnum.DELIVERY_PLATFORM_ACCEPT_DELIVERY_ORDER && !waitToAssignRiderDeliveryOrderLogs.containsKey(entry.getKey())) {
                    waitToAssignRiderDeliveryOrderLogs.put(entry.getKey(), log);
                }
            }
        }
        return waitToAssignRiderDeliveryOrderLogs;
    }

    /**
     * 获取配送门店能力配置，先获取是否对接聚合运力平台配置，如果对接，则去聚合运力配置；
     * 如果没有对接，会取之前老得三方配送配置（后续完全接入聚合运力平台，则不需要这一步）
     *
     * @param orderKeys
     */
    @VisibleForTesting
    public Map<Integer,DeliveryAbility> getAggrDeliveryAbility(List<OrderKey> orderKeys) {
        OrderKey orderKey = orderKeys.iterator().next();
        List<DeliveryPoi>  opDeliveryPoiList = deliveryPoiRepository.queryAllDeliveryPoi(orderKey.getTenantId(), orderKey.getStoreId());
        if(CollectionUtils.isEmpty(opDeliveryPoiList)){
            return Collections.emptyMap();
        }

        Map<Integer,DeliveryAbility> abilityMap=new HashMap<>();
        for (DeliveryPoi deliveryPoi : opDeliveryPoiList){
            DeliveryAbility deliveryAbility = new DeliveryAbility();
            deliveryAbility.setDeliveryLaunchType(deliveryPoi.getDeliveryLaunchType());
            deliveryAbility.setHasAggrDeliveryChannel(DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM == deliveryPoi.getDeliveryPlatform());
            deliveryAbility.setHasMaltfarmDeliveryChannel(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM == deliveryPoi.getDeliveryPlatform());
            deliveryAbility.setHasThirdPartDeliveryChannel(
                    DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM == deliveryPoi.getDeliveryPlatform()
                            && ((SelfBuiltDeliveryPoi) deliveryPoi).canLaunchDelivery()
            );

            deliveryAbility.setHasHaiKuiDelivery(
                    DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM == deliveryPoi.getDeliveryPlatform()
                            && ((SelfBuiltDeliveryPoi) deliveryPoi).canLaunchDelivery()
                            && ((SelfBuiltDeliveryPoi) deliveryPoi).getChannelStoreConfigMap().containsKey(ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY)
            );
            deliveryAbility.setHasDapDeliveryChannel(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM == deliveryPoi.getDeliveryPlatform());
            abilityMap.put(deliveryPoi.getChannelType(),deliveryAbility);
        }
        return abilityMap;
    }

    /**
     * 找出最近、有效的运单
     * 优先找出激活的运单，如果没有激活的，则按照id取最大的一个
     *
     * @param deliveryOrders
     * @return
     */
    @VisibleForTesting
    DeliveryOrder filterAvailableDeliveryOrder(List<DeliveryOrder> deliveryOrders) {

//        deliveryOrders.sort(Comparator.comparingLong(deliveryOrder -> {
//            if (deliveryOrder.isActive()) {
//                return Long.MIN_VALUE;
//            } else {
//                return -deliveryOrder.getId();
//            }
//        }));
        return DeliveryOrder.filterActiveDeliveryOrder(deliveryOrders);
    }

    public List<DeliveryOrder> queryExceptionDeliveryOrders(Long storeId,Long tenantId) {
        List<DeliveryOrder> notTimeoutDeliveryOrders = deliveryOrderRepository.getNotTimeoutDeliveryOrder(storeId,tenantId);
        Map<Long, List<DeliveryOrder>> orderId2DeliveryOrders = notTimeoutDeliveryOrders.stream()
                .collect(Collectors.groupingBy(deliveryOrder -> deliveryOrder.getOrderId()));
        // 取订单上面的有效运单
        Collection<DeliveryOrder> availableDeliveryOrders = CollectionOperateUtils
                .filterValueList(orderId2DeliveryOrders, this::filterAvailableDeliveryOrder)
                .values();
        // 过滤出异常的配送单
        List<DeliveryOrder> errorDeliveryOrder = availableDeliveryOrders.stream()
                .filter(deliveryOrder -> deliveryOrder.getExceptionType() != DeliveryExceptionTypeEnum.NO_EXCEPTION)
                .collect(Collectors.toList());
        return errorDeliveryOrder;
    }

    public List<OrderDeliveryDetail> queryExceptionOrCancelDeliveryOrders(Long storeId,Long tenantId) {
        List<DeliveryOrder> notTimeoutDeliveryOrders = deliveryOrderRepository.getDHNotTimeoutDeliveryOrder(storeId,tenantId);
        Map<Long, List<DeliveryOrder>> orderId2DeliveryOrders = notTimeoutDeliveryOrders.stream()
                .collect(Collectors.groupingBy(deliveryOrder -> deliveryOrder.getOrderId()));
        // 取订单上面的有效运单
        Collection<DeliveryOrder> availableDeliveryOrders = CollectionOperateUtils
                .filterValueList(orderId2DeliveryOrders, this::filterAvailableDeliveryOrder)
                .values();

        // 过滤出异常的配送单
        List<DeliveryOrder> filterDeliveryOrders = availableDeliveryOrders.stream()
                .filter(deliveryOrder -> (deliveryOrder.getExceptionType() != DeliveryExceptionTypeEnum.NO_EXCEPTION) || Objects.equals(deliveryOrder.getStatus(), DeliveryStatusEnum.DELIVERY_CANCELLED))
                //只要三方的
                .filter(deliveryOrder -> !Objects.equals(deliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterDeliveryOrders)) {
            return Lists.newArrayList();
        }

        Map<Long/*deliveryOrderId*/, List<DeliveryOrderLog>> deliveryIdRelatedLogMap =
                deliveryOrderLogRepository.getDeliveryOrderLogByDeliveryOrderIds(Fun.map(filterDeliveryOrders, DeliveryOrder::getId));


        return aggregateDeliverInfo(filterDeliveryOrders, deliveryIdRelatedLogMap);
    }


    @CatTransaction
    @Deprecated
    @MethodLog(logRequest = false, logResponse = true)
    public Pair<Integer, List<TOrderIdentifier>> queryExceptionDeliveryOrdersByStoreList(QueryDeliveryExceptionRequest request) {
        final List<Long> deliveryIdList = getPageIdList(request.getStoreIdList(), request.getViewOrderId(), request.getTenantId());

        final List<List<Long>> partition = Lists.partition(deliveryIdList, request.getSize());

        if (request.getPage() > partition.size()) {
            return Pair.of(deliveryIdList.size(), Collections.emptyList());
        }

        final List<Long> pageIdList = partition.get(request.getPage() - 1);

        if (CollectionUtils.isEmpty(pageIdList)) {
            return Pair.of(deliveryIdList.size(), Collections.emptyList());
        }

        return Pair.of(deliveryIdList.size(), deliveryOrderRepository.getDeliveryOrderList(pageIdList));
    }

    @CatTransaction
    @Deprecated
    @MethodLog(logRequest = false, logResponse = true)
    public Integer queryExceptionDeliveryOrdersCount(QueryDeliveryExceptionCountRequest request) {
        final List<Long> deliveryIdList = getPageIdList(request.getStoreIdList(), request.getViewOrderId(), request.getTenantId());

        return deliveryIdList.size();
    }

    @Deprecated
    private List<Long> getPageIdList(List<Long> storeIdList, String viewOrderId,Long tenantId) {
        final List<Long> notTimeoutDeliveryOrderId = getNotTimeoutDeliveryOrderIdList(storeIdList);
        final List<DeliveryIdDO> deliveryIdList = getDeliveryIdList(notTimeoutDeliveryOrderId);

        if (CollectionUtils.isEmpty(deliveryIdList)) {
            return Collections.emptyList();
        }

        Map<Long, List<DeliveryIdDO>> orderId2DeliveryOrders = deliveryIdList.stream()
                .collect(Collectors.groupingBy(deliveryOrder -> deliveryOrder.getOrderId()));
        // 取订单上面的有效运单
        Collection<DeliveryIdDO> availableDeliveryOrders = CollectionOperateUtils
                .filterValueList(orderId2DeliveryOrders, this::filterAvailableDeliveryId)
                .values();

        // 过滤出异常的配送单
        List<DeliveryIdDO> errorDeliveryOrder = availableDeliveryOrders.stream()
                .filter(deliveryOrder -> deliveryOrder.getDeliveryExceptionType() != DeliveryExceptionTypeEnum.NO_EXCEPTION.getCode())
                .collect(Collectors.toList());

        removeCancelOrderIdList(errorDeliveryOrder, tenantId);

        if (CollectionUtils.isEmpty(errorDeliveryOrder)) {
            return Lists.newArrayList();
        }

        if (StringUtils.isNotEmpty(viewOrderId)) {
            return errorDeliveryOrder.stream().filter(item -> item.getChannelOrderId().contains(viewOrderId)).map(item -> item.getId()).sorted().collect(Collectors.toList());
        } else {
            return errorDeliveryOrder.stream().map(item -> item.getId()).sorted().collect(Collectors.toList());
        }
    }

    @Deprecated
    private List<DeliveryIdDO> getDeliveryIdList(List<Long> orderIdList) {
        try {
            if (CollectionUtils.isEmpty(orderIdList)) {
                return Collections.emptyList();
            }
            final List<List<Long>> partition = Lists.partition(orderIdList, MccConfigUtils.getOrderIdSplitNum());
            List<DeliveryIdDO> result = new ArrayList<>();
            partition.forEach(item -> {
                final List<DeliveryIdDO> deliveryIdList = deliveryOrderRepository.getDeliveryIdList(item);
                if (CollectionUtils.isNotEmpty(deliveryIdList)) {
                    result.addAll(deliveryIdList);
                }
            });
            return result.stream().distinct().sorted(Comparator.comparing(DeliveryIdDO::getCreateTime)).collect(Collectors.toList());
        } catch (Exception e) {
            Cat.logEvent("DELIVERY_ORDER", "QUERY_ERROR");
            log.error("getDeliveryIdList error", e);
            return deliveryOrderRepository.getDeliveryIdList(orderIdList);
        }
    }

    @Deprecated
    private List<Long> getNotTimeoutDeliveryOrderIdList(List<Long> storeIdList) {
        try {
            if (CollectionUtils.isEmpty(storeIdList)) {
                return Collections.emptyList();
            }
            final List<List<Long>> partition = Lists.partition(storeIdList, MccConfigUtils.getStoreSplitNum());
            List<Long> result = new ArrayList<>();
            partition.forEach(item -> {
                final List<Long> notTimeoutDeliveryOrderId = deliveryOrderRepository.getNotTimeoutDeliveryOrderId(item);
                if (CollectionUtils.isNotEmpty(notTimeoutDeliveryOrderId)) {
                    result.addAll(notTimeoutDeliveryOrderId);
                }
            });
            return result.stream().distinct().collect(Collectors.toList());
        } catch (Exception e) {
            Cat.logEvent("DELIVERY_ORDER", "QUERY_ERROR");
            log.error("getNotTimeoutDeliveryOrderIdList error", e);
            return deliveryOrderRepository.getNotTimeoutDeliveryOrderId(storeIdList);
        }
    }

    private void removeCancelOrderIdList(List<DeliveryIdDO> errorDeliveryOrder, Long tenantId) {
        if (CollectionUtils.isEmpty(errorDeliveryOrder)) {
            return;
        }
        log.info("removeCancelOrderIdList");
        //分页查询订单服务
        final List<List<DeliveryIdDO>> partition = Lists.partition(errorDeliveryOrder, 100);
        final List<Long> cancelOrderList = new ArrayList<>();
        partition.stream().forEach(item -> {
            try {
                OCMSListOrderIdRequest request = new OCMSListOrderIdRequest();
                request.setTenantId(tenantId);
                request.setPage(1);
                request.setSize(100);
                request.setOrderStatusList(Lists.newArrayList(OrderStatusEnum.CANCELED.getValue()));
                final List<Long> collect = item.stream().map(DeliveryIdDO::getOrderId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                request.setOrderIdList(collect);
                final OCMSListOrderIdResponse ocmsListOrderIdResponse = ocmsQueryThriftService.queryOrderIdList(request);
                log.info("ocmsListOrderIdResponse:{}", ocmsListOrderIdResponse);
                if (ocmsListOrderIdResponse != null && ocmsListOrderIdResponse.getStatus() != null && ocmsListOrderIdResponse.getStatus().getCode() == StatusCodeEnum.SUCCESS.getCode() && CollectionUtils.isNotEmpty(ocmsListOrderIdResponse.getOrderIdList())) {
                    cancelOrderList.addAll(ocmsListOrderIdResponse.getOrderIdList());
                }
            } catch (Exception e) {
                log.error("queryOrderIdList error", e);
            }
        });
        log.info("cancel order list:{}", cancelOrderList);
        if (CollectionUtils.isNotEmpty(cancelOrderList)) {
            errorDeliveryOrder.removeIf(item -> cancelOrderList.contains(item.getOrderId()));
        }
    }

    /**
     * 找出最近、有效的运单
     * 优先找出激活的运单，如果没有激活的，则按照id取最大的一个
     *
     * @param deliveryIds
     * @return
     */
    @Deprecated
    private DeliveryIdDO filterAvailableDeliveryId(List<DeliveryIdDO> deliveryIds) {

        deliveryIds.sort(Comparator.comparingLong(deliveryOrder -> {
            if (deliveryOrder.isActive()) {
                return Long.MIN_VALUE;
            } else {
                return  -TimeUtil.toMilliSeconds(deliveryOrder.getCreateTime());
            }
        }));
        return deliveryIds.iterator().next();
    }

    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public Long queryExceptionDeliveryOrdersCountByStoreIds(List<Long> storeIdList) {
        return deliveryOrderEsAliasDao.countExceptionDeliveryOrdersByStoreIds(storeIdList);
    }

    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public Map<DeliveryExceptionSubTypeEnum, Integer> queryExceptionDeliveryOrdersCountBySubTypeAndStoreIds(List<DeliveryExceptionSubTypeEnum> deliveryExceptionSubTypeEnumList, List<Long> storeIdList) {
        Map<DeliveryExceptionSubTypeEnum, Long> countMap = deliveryOrderEsAliasDao.countExceptionDeliveryOrdersSubTypeByStoreIds(storeIdList);
        return countMap.entrySet().stream().filter(entry -> deliveryExceptionSubTypeEnumList.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().intValue()));
    }

    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public Pair<List<TOrderIdentifier>, Integer> pageQueryExceptionDeliveryOrdersBySubTypeAndStoreIds(DeliveryExceptionSubTypeEnum subTypeEnum,
                                                                                                      List<Long> storeIdList,
                                                                                                      Integer pageNo, Integer pageSize) {
        final List<Integer> exceptionTypeList = subTypeEnum.getExceptionTypeList();
        final List<Integer> exceptionCodeList = subTypeEnum.getExceptionCodeList();
        final List<Integer> deliveryStatusList = subTypeEnum.getDeliveryStatusList().stream().map(DeliveryStatusEnum::getCode).collect(Collectors.toList());

        Pair<List<DeliveryOrderEsPo>, Integer> searchResult = deliveryOrderEsAliasDao.searchExceptionDeliveryOrdersBySubTypeAndStoreIds(
                exceptionTypeList, exceptionCodeList, deliveryStatusList, storeIdList, (pageNo - 1) * pageSize, pageSize,null);
        return Pair.of(searchResult.getLeft().stream().map(DeliveryOrderEsPo::transfer2TOrderIdentifier).collect(Collectors.toList()), searchResult.getValue());
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public Optional<ShopDetail> queryShopDetailByTenantIdAndStoreId(Long storeId) {

        //获取租户id
        PoiSearchRequest request = new PoiSearchRequest();
        request.setFields(Sets.newHashSet("tenant_id"));
        request.setPoiIdList(Arrays.asList(storeId));
        PoiSearchResponse search = poiThriftServiceClient.search(request);
        if (search == null || CollectionUtils.isEmpty(search.getPoiList())) {
            log.info("queryShopDetailByTenantIdAndStoreId search tenantId is null. storeId={}", storeId);
            return Optional.empty();
        }
        Long tenantId = search.getPoiList().get(0).getTenantId();
        //获取门店信息
        Optional<TenantChannelStoreInfo> channelStore = Optional.empty();
        channelStore = tenantSystemClient.queryChannelStoreDetailInfoWithAnyChannel(tenantId, storeId,DynamicChannelType.MEITUAN.getChannelId());
        if (!channelStore.isPresent()) {
            log.warn("queryShopDetailByTenantIdAndStoreId mt error tenantId:{},storeId:{},result:{}",
                    tenantId, storeId, channelStore);
            return Optional.empty();
        }
        TenantInfo tenantInfo=tenantSystemClient.queryTenantInfo(tenantId);
        String categoryCode = aggDeliveryWrapper.getCategoryCode(tenantId);
        if (!StringUtils.isNoneBlank(channelStore.get().getLatitude(), channelStore.get().getLongitude())) {
            log.info("queryShopDetailByTenantIdAndStoreId store coordinate is null. storeId={}", storeId);
            return Optional.empty();
        }

        return Optional.of(ShopDetail
                .builder()
                .shopId(channelStore.get().getShopId())
                .shopAddress(channelStore.get().getAddress())
                .shopLatitude(Double.parseDouble(channelStore.get().getLatitude()))
                .shopLongitude(Double.parseDouble(channelStore.get().getLongitude()))
                .shopName(channelStore.get().getStoreName())
                .shopPhone(channelStore.get().getPhone())
                .tenantId(channelStore.get().getTenantId())
                .mapType(BigInteger.ONE.intValue())
                .category(BigInteger.ONE.intValue())
                .tenantName(tenantInfo==null ? "":tenantInfo.getTenantName())
                .categoryCode(categoryCode)
                .build());

    }


    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public List<DeliveryOrderInfo>  queryDeliveryOrderByOrderIdList(List<Long> orderIdList) {

        List<DeliveryOrderInfo> deliveryOrderInfos = new ArrayList<>();

        List<DeliveryOrder> deliveryOrdersByOrderIdList = deliveryOrderRepository.getDeliveryOrdersByOrderIdList(orderIdList);

        if (CollectionUtils.isEmpty(deliveryOrdersByOrderIdList)) {
            return deliveryOrderInfos;
        }
        Map<Long,DeliveryOrder> deliveryOrderMap = deliveryOrdersByOrderIdList.stream().collect(Collectors.toMap(DeliveryOrder::getOrderId,Function.identity(),(k,v)->{
            if(k.getId()>v.getId()){
                return k;
            }
            return v;
        }));

        Set<Integer> carrierList = deliveryOrderMap.values().stream().map(DeliveryOrder::getDeliveryChannel).collect(Collectors.toSet());

        List<DeliveryChannel> deliveryChannelList = deliveryChannelApplicationService.batchQueryDeliveryChannelByCarrierCodeSet(carrierList);

        buildDeliveryOrderInfo(new ArrayList<>(deliveryOrderMap.values()), deliveryChannelList, deliveryOrderInfos);

        return deliveryOrderInfos;
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public Optional<DeliveryOrder> queryDeliveryOrderByOrderId(Long orderId,Long tenantId,Long storeId) {
        List<DeliveryOrder> deliveryOrderList = new ArrayList<>();
        if(MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
            deliveryOrderList = deliveryOrderRepository.getDeliveryOrdersByOrderIdListWithTenant(Collections.singletonList(orderId),tenantId,storeId);
        }else {
            deliveryOrderList = deliveryOrderRepository.getDeliveryOrdersByOrderIdList(Collections.singletonList(orderId));
        }

        if (CollectionUtils.isEmpty(deliveryOrderList)) {
            return Optional.empty();
        }

        return Optional.ofNullable(filterAvailableDeliveryOrder(deliveryOrderList));
    }

    private void buildDeliveryOrderInfo(List<DeliveryOrder> deliveryOrdersByOrderIdList, List<DeliveryChannel> deliveryChannelList, List<DeliveryOrderInfo> deliveryOrderInfos) {
        if (CollectionUtils.isEmpty(deliveryChannelList)) {
            return;
        }

        Map<Integer, DeliveryChannel> deliveryChannelMapFromRpc = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, Function.identity()));

        deliveryOrdersByOrderIdList.forEach(deliveryOrder -> {

            DeliveryChannel deliveryChannelDto = deliveryChannelMapFromRpc.get(deliveryOrder.getDeliveryChannel());

            DeliveryOrderInfo deliveryOrderInfo = DeliveryOrderInfo.builder().deliveryChannel(deliveryOrder.getDeliveryChannel()).channelOrderId(deliveryOrder.getChannelOrderId())
                    .deliveryPlatformCode(Objects.nonNull(deliveryChannelDto) ? deliveryChannelDto.getDeliveryPlatFormCode() : NumberUtils.INTEGER_ZERO).orderBizType(deliveryOrder.getOrderBizType())
                    .orderId(deliveryOrder.getOrderId()).tenantId(deliveryOrder.getTenantId()).storeId(deliveryOrder.getStoreId()).status(deliveryOrder.getStatus().getCode()).deliveryOrderId(deliveryOrder.getDeliveryOrderId()).build();
            deliveryOrderInfos.add(deliveryOrderInfo);
        });
    }

    /**
     * @Source ES
     * @return 进行中的和配送完成的运单
     */
    @CatTransaction
    public List<OriginWaybillInfo> batchQueryOriginWaybillNoByES(List<Long> orderIds, Long tenantId, Set<Long> storeIds) {
        List<DeliveryOrderEsPo> deliveryOrderEsPos = deliveryOrderEsAliasDao.batchQueryOriginWaybillNo(orderIds, tenantId, storeIds);
        List<Integer> employDeliveryStatus = MccConfigUtils.getEmployDeliveryStatus();
        Set<OriginWaybillInfo> resultSet = new HashSet<>(deliveryOrderEsPos.size());
        deliveryOrderEsPos.forEach(po -> {
            // 最新运单
            if (StringUtils.isNotBlank(po.getOriginWaybillNo()) && employDeliveryStatus.contains(po.getDeliveryStatus())) {
                resultSet.add(new OriginWaybillInfo(po.getOrderId(), po.getDeliveryOrderId(), po.getDeliveryStatus(), po.getOriginWaybillNo()));
            }
            // 历史运单
            List<DeliveryOrderEsPo.DeliveryOrderSubPo> deliveryOrderSubPoList = JsonUtil.fromJson(po.getDeliveryTraceList(),
                    new TypeReference<List<DeliveryOrderEsPo.DeliveryOrderSubPo>>(){});
            if (CollectionUtils.isEmpty(deliveryOrderSubPoList)) {
               return;
            }
            for (DeliveryOrderEsPo.DeliveryOrderSubPo deliveryOrderSubPo : deliveryOrderSubPoList) {
                if (StringUtils.isBlank(deliveryOrderSubPo.getOriginWaybillNo())) {
                    continue;
                }
                if (!employDeliveryStatus.contains(deliveryOrderSubPo.getDeliveryStatus())) {
                    continue;
                }
                resultSet.add(new OriginWaybillInfo(po.getOrderId(), deliveryOrderSubPo.getDeliveryOrderId(), deliveryOrderSubPo.getDeliveryStatus(), deliveryOrderSubPo.getOriginWaybillNo()));
            }
        });
        return new ArrayList<>(resultSet);
    }

    /**
     * @Source DB
     * @return 进行中的和配送完成的运单
     */
    @CatTransaction
    public List<OriginWaybillInfo> batchQueryOriginWaybillNoByDB(List<Long> orderIds, Long tenantId, Set<Long> storeIds) {
        try {
            List<Integer> employDeliveryStatus = MccConfigUtils.getEmployDeliveryStatus();
            List<DeliveryOrderDO> records = deliveryOrderRepository.batchQueryOriginWaybillNo(tenantId, storeIds, orderIds, employDeliveryStatus);
            List<OriginWaybillInfo> list = new ArrayList<>(records.size());
            for (DeliveryOrderDO record : records) {
                HashMap<String, Object> map = JacksonUtils.fromJsonToMap(record.getExtInfo());
                Object originWaybillNo = map.get(DeliveryOrderConstants.ORIGIN_WAYBILL_NO);
                if (Objects.isNull(originWaybillNo)) {
                    continue;
                }
                OriginWaybillInfo originWaybillInfo = new OriginWaybillInfo();
                originWaybillInfo.setOrderId(record.getOrderId());
                originWaybillInfo.setDeliveryStatus(record.getDeliveryStatus());
                originWaybillInfo.setOriginWaybillNo(String.valueOf(originWaybillNo));
                list.add(originWaybillInfo);
            }
            return list;
        } catch (Exception e) {
            log.info("batchQueryOriginWaybillNoByDB req: tenantId={}; orderIds={}; storeIds={}", tenantId, orderIds, storeIds);
            log.error("batchQueryOriginWaybillNoByDB error: ", e);
            return new ArrayList<>();
        }
    }

    @MethodLog(logRequest = true, logResponse = true)
    public List<TActiveDeliveryDetail> queryActiveDeliveryInfoByOrderKeys(List<QueryOrderDeliveryInfoKey> orderKeys, boolean isNeedAggUrl) {

        List<OrderDeliveryDetail> deliveryDetails = queryOrderDeliveryDetailByOrderIdList(orderKeys);

        if (CollectionUtils.isEmpty(deliveryDetails)) {
            return new ArrayList<>();
        }

        // 查询承运商信息
        Set<Integer> carrierCodeSet = deliveryDetails.stream().map(OrderDeliveryDetail::getDeliveryChannel).filter(Objects::nonNull).collect(Collectors.toSet());
        List<DeliveryChannel> deliveryChannelList = deliveryChannelApplicationService.batchQueryDeliveryChannelByCarrierCodeSet(carrierCodeSet);
        Map<Integer, String> deliveryChannelNameMap = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, DeliveryChannel::getCarrierName));
        Map<Integer, Integer> deliveryPlatformCodeMap = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, DeliveryChannel::getDeliveryPlatFormCode, (v1, v2) -> v2));

        // 查询聚合配送url
        Map<Long, DeliveryRedirectModule> orderId2AggUrlMap = Maps.newHashMap();
        if (isNeedAggUrl) {
            orderId2AggUrlMap = buildOrderId2AggUrlMap(deliveryDetails, deliveryPlatformCodeMap, orderKeys);
        }

        return buildTDeliveryDetail(deliveryDetails, deliveryChannelNameMap, deliveryPlatformCodeMap, orderId2AggUrlMap);
    }

    private List<TActiveDeliveryDetail> buildTDeliveryDetail(List<OrderDeliveryDetail> deliveryDetails,
                                                       Map<Integer, String> deliveryChannelNameMap,
                                                       Map<Integer/*CarrierCode*/, Integer/*PlatformCode*/> carrierCodePlatformCodeMap,
                                                       Map<Long, DeliveryRedirectModule> orderId2AggUrlMap) {

        if (CollectionUtils.isEmpty(deliveryDetails) || MapUtils.isEmpty(deliveryChannelNameMap) || MapUtils.isEmpty(carrierCodePlatformCodeMap)) {
            return Collections.emptyList();
        }

        return deliveryDetails.stream().map(deliveryDetail -> convert(deliveryDetail, deliveryChannelNameMap, carrierCodePlatformCodeMap, orderId2AggUrlMap)).collect(Collectors.toList());
    }

    private TActiveDeliveryDetail convert(OrderDeliveryDetail domain, Map<Integer, String> deliveryChannelNameMap,
                                          Map<Integer/*CarrierCode*/, Integer/*PlatformCode*/> carrierCodePlatformCodeMap,
                                          Map<Long, DeliveryRedirectModule> orderId2AggUrlMap) {
        return TActiveDeliveryDetail.builder().deliveryFee(domain.getDeliveryFee() == null? null : domain.getDeliveryFee().doubleValue())
                .deliveryDistance(domain.getDistance())
                .bizOrderId(domain.getOrderKey().getOrderId())
                .deliveryEntity(getDeliveryEntityCode(domain.getDeliveryChannel(), carrierCodePlatformCodeMap))
                .deliveryException(domain.getExceptionDescription())
                .deliveryExceptionCode(domain.getExceptionCode())
                .deliveryExceptionType(Objects.nonNull(domain.getExceptionType()) ? domain.getExceptionType().getCode() :
                        DeliveryExceptionTypeEnum.NO_EXCEPTION.getCode())
                .deliveryChannelName(getDeliveryChannelName(domain, deliveryChannelNameMap))
                .startWaitAssignRiderTime(domain.getStartWaitingAssignRiderTime() == null ? null : TimeUtil.toMilliSeconds(domain.getStartWaitingAssignRiderTime()))
                .riderName(domain.getRiderInfo() == null ? null : domain.getRiderInfo().getRiderName())
                .status(domain.getStatus() == null ? null : domain.getStatus().getCode())
                .tLaunchDeliveryType(buildLaunchItem(domain))
                .riderPhone(domain.getRiderInfo() == null ? null : domain.getRiderInfo().getRiderPhone())
                .displayCancelStatus(domain.getDisplayCancelStatusEnum().getCode())
                .deliveryChannelCode(Optional.ofNullable(domain.getDeliveryChannel()).orElse(DeliveryChannelEnum.AGGREGATION_DELIVERY.getCode()))
                .deliveryStatusChangeTime(domain.getDeliveryStatusChangeTime() == null ? null : TimeUtil.toMilliSeconds(domain.getDeliveryStatusChangeTime()))
                .deliveryCount(domain.getDeliveryCount()==null ? 1:domain.getDeliveryCount())
                .createTime(domain.getCreateTime())
                .transType(domain.getTransType())
                .tipFee(domain.getTipFee() == null ? null:domain.getTipFee().doubleValue())
                .allowLatestAuditTime(domain.getDealDeadline())
                .fulfillOrderId(domain.getFulfillOrderId())
                .platformSource(domain.getPlatformSource())
                .orderBizType(domain.getOrderBizType())
                .channelOrderId(domain.getChannelOrderId())
                .platformCode(getPlatformCode(domain, carrierCodePlatformCodeMap))
                .platformDesc(getPlatformDesc(domain, carrierCodePlatformCodeMap))
                .signPosition(domain.getSignPosition())
                .TDeliveryRedirectModule(getDeliveryRedirectModule(domain, orderId2AggUrlMap))
                .build();
    }

    private Integer getDeliveryEntityCode(Integer deliveryChannel, Map<Integer, Integer> carrierCodePlatformCodeMap) {

        if (deliveryChannel == null) {
            return null;
        }
        if (deliveryChannel.equals(DeliveryChannelEnum.AGGREGATION_DELIVERY.getCode())) {
            return null;
        }
        Integer deliveryPlatFormCode = carrierCodePlatformCodeMap.get(deliveryChannel);
        if (deliveryChannel.equals(DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode()) ||
                Objects.equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode(), deliveryPlatFormCode)) {
            return DeliveryEntityEnum.PLATFORM.getValue();
        }
        if (deliveryChannel.equals(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())) {
            return DeliveryEntityEnum.DELIVER_BY_SELF.getValue();
        }
        return DeliveryEntityEnum.THIRD_PART.getValue();
    }

    private String getDeliveryChannelName(OrderDeliveryDetail domain, Map<Integer, String> deliveryChannelNameMap) {
        if (Objects.isNull(domain) || Objects.isNull(domain.getDeliveryChannel()) || MapUtils.isEmpty(deliveryChannelNameMap)) {
            return null;
        }

        return deliveryChannelNameMap.get(domain.getDeliveryChannel());
    }


    private Map<Long, DeliveryRedirectModule> buildOrderId2AggUrlMap(List<OrderDeliveryDetail> deliveryDetails,
                                                                    Map<Integer, Integer> deliveryPlatformCodeMap, List<QueryOrderDeliveryInfoKey> orderKeys) {
        Map<Long, DeliveryRedirectModule> orderId2AggUrlMap = Maps.newHashMap();
        if (MapUtils.isNotEmpty(deliveryPlatformCodeMap)) {
            Map<Long, Integer> orderId2DeliveryPlatformCodeMap = deliveryDetails.stream()
                    .collect(Collectors.toMap(deliveryDetail -> deliveryDetail.getOrderKey().getOrderId(),
                            deliveryDetail -> deliveryPlatformCodeMap.get(deliveryDetail.getDeliveryChannel())));
            List<DeliveryOrderRedirectModule> deliveryOrderRedirectModuleList = aggDeliveryConfigApplicationService.queryAggDeliveryConfig(deliveryDetails, orderId2DeliveryPlatformCodeMap, orderKeys.get(0).getStoreId());
            orderId2AggUrlMap = deliveryOrderRedirectModuleList.stream().collect(Collectors.toMap(DeliveryOrderRedirectModule::getOrderId, DeliveryOrderRedirectModule::getDeliveryRedirectModule));
        }
        return orderId2AggUrlMap;
    }


    private TLaunchDeliveryType buildLaunchItem(OrderDeliveryDetail domain) {

        TLaunchDeliveryType tLaunchDeliveryType = new TLaunchDeliveryType();
        tLaunchDeliveryType.setCanSelfDelivery(domain.isCanSelfDelivery());
        tLaunchDeliveryType.setCanRetryLaunch(domain.isCanRetryLaunch());
        tLaunchDeliveryType.setCanLaunchThirdPartWhenException(domain.isCanLaunchThirdPartWhenException());
        tLaunchDeliveryType.setCanManualLaunchThirdPart(domain.isCanManualLaunchThirdPart());
        tLaunchDeliveryType.setCanCancel(domain.isCanCancel());
        tLaunchDeliveryType.setCanRetryLaunchByMaltfarm(domain.isCanRetryLaunchByMaltfarm());
        tLaunchDeliveryType.setCanRetryLaunchByHaiKui(domain.isCanRetryLaunchByHaiKui());
        tLaunchDeliveryType.setCanRetryLaunchByDap(domain.isCanRetryLaunchByDap());
        return tLaunchDeliveryType;
    }

    private Integer getPlatformCode(OrderDeliveryDetail domain, Map<Integer/*CarrierCode*/, Integer/*PlatformCode*/> carrierCodePlatformCodeMap) {
        try {
            if (Objects.isNull(domain) || Objects.isNull(domain.getDeliveryChannel()) || MapUtils.isEmpty(carrierCodePlatformCodeMap)) {
                return null;
            }

            return carrierCodePlatformCodeMap.get(domain.getDeliveryChannel());
        } catch (Exception e) {
            log.error("getPlatformCode error", e);
            com.dianping.cat.Cat.logEvent("DH_ADAPT_DAP", "GET_PLATFORM_CODE_ERROR");
            return null;
        }
    }

    private String getPlatformDesc(OrderDeliveryDetail domain, Map<Integer/*CarrierCode*/, Integer/*PlatformCode*/> carrierCodePlatformCodeMap) {
        try {
            if (Objects.isNull(domain) || Objects.isNull(domain.getDeliveryChannel()) || MapUtils.isEmpty(carrierCodePlatformCodeMap)) {
                return null;
            }

            Integer platformCode = carrierCodePlatformCodeMap.get(domain.getDeliveryChannel());
            return Optional.ofNullable(DeliveryPlatformEnum.enumOf(platformCode)).map(DeliveryPlatformEnum::getDesc).orElse("");
        } catch (Exception e) {
            log.error("getPlatformDesc error", e);
            com.dianping.cat.Cat.logEvent("DH_ADAPT_DAP", "GET_PLATFORM_DESC_ERROR");
            return "";
        }
    }

    private TDeliveryRedirectModule getDeliveryRedirectModule(OrderDeliveryDetail domain, Map<Long, DeliveryRedirectModule> orderId2AggUrlMap) {
        if (Objects.isNull(domain) || Objects.isNull(domain.getOrderKey()) || MapUtils.isEmpty(orderId2AggUrlMap)) {
            return null;
        }
        DeliveryRedirectModule deliveryRedirectModule =  orderId2AggUrlMap.get(domain.getOrderKey().getOrderId());
        if (Objects.isNull(deliveryRedirectModule)) {
            return null;
        }
        return TDeliveryRedirectModule.builder()
                .title(deliveryRedirectModule.getTitle())
                .url(deliveryRedirectModule.getUrl())
                .urlText(deliveryRedirectModule.getUrlText())
                .build();
    }
}
