package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryDimensionPoi;

import java.util.Optional;

/**
 * 配送门店仓储服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/15
 */
public interface DeliveryDimensionPoiRepository {

	/**
	 * 查询配送门店信息(美团渠道)
	 */
	DeliveryDimensionPoi queryDeliveryDimensionPoi(Long tenantId, Long storeId);

	void insertDeliveryDimensionPoi(DeliveryDimensionPoi deliveryDimensionPoi);

	void updateDeliveryDimensionPoi(DeliveryDimensionPoi deliveryDimensionPoi);

}
