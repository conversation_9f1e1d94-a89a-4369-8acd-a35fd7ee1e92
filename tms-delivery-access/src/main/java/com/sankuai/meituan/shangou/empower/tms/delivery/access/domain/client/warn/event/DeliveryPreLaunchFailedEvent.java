package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event;

import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import lombok.AllArgsConstructor;

import java.util.Optional;

/**
 * 配送预发单失败事件
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/4
 */
@AllArgsConstructor
public class DeliveryPreLaunchFailedEvent extends DeliveryWarnEvent {

	private final String warnMessage;

	public DeliveryPreLaunchFailedEvent(OrderInfo orderInfo, String failReason) {
		this.warnMessage = String.format(
				"配送预发单失败\n[租户id:%d][门店id:%d]\n[赋能订单号:%d]\n[渠道:%s][渠道订单号:%s]\n[失败原因:%s]",
				Optional.ofNullable(orderInfo).map(OrderInfo::getOrderKey).map(OrderKey::getTenantId).orElse(-1L),
				Optional.ofNullable(orderInfo).map(OrderInfo::getOrderKey).map(OrderKey::getStoreId).orElse(-1L),
				Optional.ofNullable(orderInfo).map(OrderInfo::getOrderKey).map(OrderKey::getOrderId).orElse(-1L),
				Optional.ofNullable(orderInfo).map(OrderInfo::getOrderBizType).map(DynamicOrderBizType::findOf).map(DynamicOrderBizType::getDesc).orElse(""),
				Optional.ofNullable(orderInfo).map(OrderInfo::getChannelOrderId).orElse(""),
				failReason
		);
	}

	@Override
	public String getWarnMessage() {
		return warnMessage;
	}
}
