package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.TransferOperateInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;

/**
 * <AUTHOR>
 */
public interface TransferOrderSquirrelServiceClient {

    String getKey (Long tenantId, Long storeId, Long orderId);

    boolean set(String key, TransferOperateInfo transferOperate);
}
