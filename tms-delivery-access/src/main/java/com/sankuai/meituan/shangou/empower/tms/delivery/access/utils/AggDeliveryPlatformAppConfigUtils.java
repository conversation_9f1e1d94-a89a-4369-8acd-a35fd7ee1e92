package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.AggDeliveryPlatformAppConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021-04-08 16:32
 */
@Slf4j
public class AggDeliveryPlatformAppConfigUtils {

    public static AggDeliveryPlatformAppConfig getAggDeliveryPlatformAppConfig(Integer platformCode) {
        if (platformCode == null) {
            return null;
        }
        String configValue = MccConfigUtils.getAggDeliveryPlatformAppConfig();
        if (StringUtils.isEmpty(configValue)) {
            return null;
        }
        try {
            Map<String, AggDeliveryPlatformAppConfig> configMap = JsonUtil.fromJson(configValue, new TypeReference<Map<String,
                    AggDeliveryPlatformAppConfig>>() {
            });

            return configMap.get(platformCode.toString());
        }
        catch (Exception e) {
            log.info("search AggDeliveryPlatformAppConfig error, exception", e);
            return null;
        }
    }

    public static AggDeliveryPlatformAppConfig getAggAppNewConfig(Integer platformCode) {
        if (platformCode == null) {
            return null;
        }
        String configValue = MccConfigUtils.getAggDeliveryPlatformAppNewConfig();
        if (StringUtils.isBlank(configValue)) {
            return null;
        }
        try {
            Map<String, AggDeliveryPlatformAppConfig> configMap = JsonUtil.fromJson(configValue, new TypeReference<Map<String,
                    AggDeliveryPlatformAppConfig>>() {
            });

            if (configMap != null) {
                return configMap.get(platformCode.toString());
            }
        }
        catch (Exception e) {
            log.info("search AggDeliveryPlatformAppConfig error, exception", e);
        }
        return null;
    }
}
