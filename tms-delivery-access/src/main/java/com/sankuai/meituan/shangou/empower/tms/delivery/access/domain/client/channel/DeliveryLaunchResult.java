package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/7
 */
@Getter
@ToString
public class DeliveryLaunchResult {

	/**
	 * 配送是否发起成功
	 */
	private final boolean success;
	/**
	 * 是否需要重试(网络超时/系统异常等场景需要重试)
	 */
	private final boolean needRetry;
	/**
	 * 渠道配送id，仅当发起配送成功时有值
	 */
	private final String channelDeliveryId;
	/**
	 * 拒单信息，包括原因类型和描述
	 * 配送发起失败时有值
	 */
	private final DeliveryExceptionInfo rejectInfo;

	public DeliveryLaunchResult(String channelDeliveryId) {
		Preconditions.checkNotNull(channelDeliveryId, "channelDeliveryId is null");
		this.success = true;
		this.needRetry = false;
		this.channelDeliveryId = channelDeliveryId;
		this.rejectInfo = null;
	}

	public DeliveryLaunchResult(boolean needRetry, DeliveryExceptionInfo rejectInfo) {
		Preconditions.checkNotNull(rejectInfo, "rejectInfo is null");

		this.success = false;
		this.needRetry = needRetry;
		this.channelDeliveryId = null;
		this.rejectInfo = rejectInfo;
	}
}
