package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.DeliveryOrderLog;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;

import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import lombok.Data;
import lombok.Setter;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/8
 */
@Data
public class OrderDeliveryDetail {

    /**
     * 订单业务key
     */
    private OrderKey orderKey;

    /*以下字段在没有运单的情况下，可能是不存在的*/

    /**
     * 运单id
     */
    private Long deliveryOrderId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 赋能门店id
     */
    private Long storeId;

    /**
     * 渠道订单id
     */
    private String channelOrderId;

    /**
     * 收货人信息
     */
    private Receiver receiver;

    /**
     * 配送渠道
     */
    private Integer deliveryChannel;

    /**
     * 渠道运单id
     */
    private String channelDeliveryId;

    /**
     * 渠道服务包码
     */
    private String channelServicePackageCode;

    /**
     * 运单状态
     */
    private DeliveryStatusEnum status;

    /**
     * 运单当前异常类型
     */
    private DeliveryExceptionTypeEnum exceptionType;

    /**
     * 运单当前异常描述
     */
    private String exceptionDescription;

    /**
     * 运单当前的配送异常码，用于上游扩展异常描述
     */
    private Integer exceptionCode;

    /**
     * 运单最有一次事件发生事件
     * 默认值1970-01-01 00:00:00.000
     */
    private LocalDateTime startWaitingAssignRiderTime;

    /**
     * 骑手信息
     */
    private Rider riderInfo;

    /**
     * 运单生效状态
     * 0：该运单生效中，即是真正执行配送的运单
     * 非0毫秒时间戳：该运单未生效，即非最终配送运单
     */
    private Long activeStatus;

    /**
     * 配送费用，单位：元
     */
    private BigDecimal deliveryFee;

    /**
     * 配送距离
     */
    private Long distance;

    /**
     * 是否可以重发配送
     */
    private boolean canRetryLaunch;

    /**
     * 是否可以商家自行配送
     */
    private boolean canSelfDelivery;

    /**
     * 是否可以手动发三方配送
     */
    private boolean canManualLaunchThirdPart;

    /**
     * 是否可以平台异常转三方配送
     */
    private boolean canLaunchThirdPartWhenException;

    /**
     * 是否可以取消配送
     */
    private boolean canCancel;

    /**
     * 是否可以重发麦芽田配送
     */
    private boolean canRetryLaunchByMaltfarm;

    /**
     * 是否可以重发麦芽田配送
     */
    private boolean canRetryLaunchByHaiKui;

    /**
     * 展示的取消状态
     */
    private DisplayCancelStatusEnum displayCancelStatusEnum;

    /**
     * 运单状态变更时间
     */
    private LocalDateTime deliveryStatusChangeTime;

    /**
     * 配送次数
     */
    private Integer deliveryCount;

    /**
     * 是否可以重发青云聚信平台
     */
    private boolean canRetryLaunchByDap;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 转单类型
     */
    private Integer transType;

    /**
     * 小费
     */
    private BigDecimal tipFee;

    @Setter
    private String dealDeadline;

    @Setter
    private Integer orderBizType;

    @Setter
    public Long fulfillOrderId;

    @Setter
    public Integer platformSource;

    @Setter
    public String signPosition;

    @Setter
    public Long assessDeliveryTime;

    @Setter
    public Integer isFourWheelDelivery;

    @Setter
    private Integer isManual;

    /**
     * 原始运单信息
     */
    @Setter
    public List<OriginWaybillInfo> waybillList;


    private OrderDeliveryDetail(OrderKey orderKey) {
        this.orderKey = orderKey;
    }

    private OrderDeliveryDetail(Long deliveryOrderId, Long tenantId, Long storeId, OrderKey orderKey, String channelOrderId,
                                Receiver receiver, Integer deliveryChannel, String channelDeliveryId,
                                String channelServicePackageCode, DeliveryStatusEnum status, DeliveryExceptionTypeEnum exceptionType,
                                String exceptionDescription, Integer exceptionCode, LocalDateTime startWaitingAssignRiderTime,
                                Rider riderInfo, Long activeStatus, BigDecimal deliveryFee, Long distance) {
        this.deliveryOrderId = deliveryOrderId;
        this.tenantId = tenantId;
        this.storeId = storeId;
        this.orderKey = orderKey;
        this.channelOrderId = channelOrderId;
        this.receiver = receiver;
        this.deliveryChannel = deliveryChannel;
        this.channelDeliveryId = channelDeliveryId;
        this.channelServicePackageCode = channelServicePackageCode;
        this.status = status;
        this.exceptionType = exceptionType;
        this.exceptionDescription = exceptionDescription;
        this.exceptionCode = exceptionCode;
        this.startWaitingAssignRiderTime = startWaitingAssignRiderTime;
        this.riderInfo = riderInfo;
        this.activeStatus = activeStatus;
        this.deliveryFee = deliveryFee;
        this.distance = distance;
    }


    @VisibleForTesting
        //todo 分拆成四个方法，比较可读
    void fillOperateItem(DeliveryConditionInOrder conditionInOrder, DeliveryAbility deliveryAbility, DeliveryOrder deliveryOrder) {

        //如果订单状态、配送方式等不允许转自配送
        if (!conditionInOrder.orderAllowDelivery(deliveryOrder)) {
            return;
        }
        //如果门店是手动发单，并且没有运单，则展示发三方配送
        boolean manualThirdPart = (!this.hasDeliveryOrder() && conditionInOrder.canLaunchThirdPartDelivery() && deliveryAbility.canManualLaunchDelivery());
        //如果运单停留在初始状态，则展示发三方配送，给用户重试
        boolean initToThirdPart = this.hasDeliveryOrder() && DeliveryStatusEnum.INIT.equals(this.getStatus()) && deliveryAbility.isHasAggrDeliveryChannel();

        if (manualThirdPart || initToThirdPart) {
            this.setCanManualLaunchThirdPart(true);
            return;
        }

        // 若开通了麦芽田配送，且有异常，且是商家自配送，则设置麦芽田重发配送为 true
        if (deliveryAbility.isHasMaltfarmDeliveryChannel() && this.hasException() && conditionInOrder.selfDeliveryMark()) {
            if (this.getExceptionCode() == DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL.getCode()) {
                this.setCanRetryLaunchByMaltfarm(true);
            }
            return;
        }

        if(deliveryAbility.isHasDapDeliveryChannel() && this.hasException() && conditionInOrder.selfDeliveryMark()){
            if (this.getExceptionCode() == DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL.getCode()) {
                this.setCanRetryLaunchByMaltfarm(true);
            }
            return;
        }

        if (this.hasException()
                && deliveryAbility.isHasHaiKuiDelivery()
                && Objects.nonNull(deliveryOrder)
                && Objects.equals(deliveryOrder.getDeliveryChannel(),DeliveryChannelEnum.HAI_KUI_DELIVERY)) {
            this.setCanRetryLaunchByHaiKui(true);
        }

        //有配送异常,已经扭转过自配送标记，并且对接了聚合运力平台
        if (this.hasException() && deliveryAbility.isHasAggrDeliveryChannel()) {
            //如果订单还是标记了平台配送，则可以转三方配送
            if (conditionInOrder.canTurnThirdPartDelivery()) {
                this.setCanLaunchThirdPartWhenException(true);
            } else {
                this.setCanRetryLaunch(true);
            }
            return;
        }
        //如果有配送异常，并且门店没有对接任何三方运力渠道or聚合运力平台，则只能转商家自己送
        if (this.hasException() && deliveryAbility.hasNoAggrOrThirdDeliveryAbility()) {
            this.setCanSelfDelivery(true);
            return;
        }

        if (isCancelable(conditionInOrder, deliveryOrder, deliveryAbility)) {
            this.setCanCancel(true);
            return;
        }
        if (canRetryAfterCancel(conditionInOrder, deliveryOrder, deliveryAbility)) {
            this.setCanRetryLaunch(true);
            return;
        }
    }


    private boolean isCancelable(DeliveryConditionInOrder conditionInOrder, DeliveryOrder deliveryOrder, DeliveryAbility deliveryAbility) {
        if (Objects.isNull(deliveryOrder)) {
            return false;
        }
        boolean isAggregation = deliveryAbility.isHasAggrDeliveryChannel();
        boolean isInCanCancelStatus = MccConfigUtils.getCancelDeliveryStatusList().contains(deliveryOrder.getStatus().getCode());
        boolean noWaitAuditRefund = !conditionInOrder.isOrderWaitAudited();
        boolean notCanceling = deliveryOrder.getCancelMark().equals(CancelMarkEnum.DEFAULT.getValue());
        return isAggregation && isInCanCancelStatus && noWaitAuditRefund && notCanceling;
    }

    private boolean canRetryAfterCancel(DeliveryConditionInOrder conditionInOrder, DeliveryOrder deliveryOrder, DeliveryAbility deliveryAbility) {
        if (Objects.isNull(deliveryOrder)) {
            return false;
        }
        boolean isAggregation = deliveryAbility.isHasAggrDeliveryChannel();
        boolean isInCancelStatus = Objects.equals(DeliveryStatusEnum.DELIVERY_CANCELLED, deliveryOrder.getStatus());
        boolean orderNotInFinalStatus = !conditionInOrder.isOrderFinished();
        return isAggregation && isInCancelStatus && orderNotInFinalStatus;
    }

    private boolean hasException() {
        return this.hasDeliveryOrder() && (this.getExceptionType() != DeliveryExceptionTypeEnum.NO_EXCEPTION);
    }

    private boolean hasDeliveryOrder() {
        return this.deliveryOrderId != null;
    }

    public static OrderDeliveryDetail buildFromDeliveryOrder(DeliveryOrder deliveryOrder, OrderKey orderKey, DeliveryConditionInOrder conditionInOrder, DeliveryAbility deliveryAbility,
                                                             DeliveryOrderLog deliveryOrderLog, DeliveryOrderLog deliveryOrderCurrentStatusLog, List<OriginWaybillInfo> originWaybillInfoList) {
        Preconditions.checkNotNull(orderKey, "order不可为空");

        OrderDeliveryDetail orderDeliveryDetail = null;
        if (deliveryOrder != null) {
            orderDeliveryDetail = new OrderDeliveryDetail(deliveryOrder.getId(), deliveryOrder.getTenantId(), deliveryOrder.getStoreId(),
                    deliveryOrder.getOrderKey(), deliveryOrder.getChannelOrderId(), deliveryOrder.getReceiver(),
                    deliveryOrder.getDeliveryChannel(), deliveryOrder.getChannelDeliveryId(),
                    deliveryOrder.getChannelServicePackageCode(),
                    deliveryOrder.getStatus(),
                    deliveryOrder.getExceptionType(), deliveryOrder.getExceptionDescription(), deliveryOrder.getDeliveryExceptionCode(),
                    buildStartWaitAssginRiderTime(deliveryOrderLog), deliveryOrder.getRiderInfo(), deliveryOrder.getActiveStatus(),
                    deliveryOrder.getDeliveryFee(), deliveryOrder.getDistance());
            orderDeliveryDetail.setDeliveryStatusChangeTime(Objects.nonNull(deliveryOrderCurrentStatusLog) ? deliveryOrderCurrentStatusLog.getChangeTime() : null);
            orderDeliveryDetail.setDeliveryCount(deliveryOrder.getDeliveryCount());
            orderDeliveryDetail.setCreateTime(TimeUtil.toMilliSeconds(deliveryOrder.getCreateTime()));
            orderDeliveryDetail.setTransType(deliveryOrder.getTransType());
            orderDeliveryDetail.setTipFee(deliveryOrder.getTipAmount());
            orderDeliveryDetail.setDealDeadline(deliveryOrder.getDealDeadline());
            orderDeliveryDetail.setFulfillOrderId(deliveryOrder.getFulfillmentOrderId());
            orderDeliveryDetail.setPlatformSource(deliveryOrder.getPlatformSourceEnum().getCode());
            orderDeliveryDetail.setIsFourWheelDelivery(deliveryOrder.getIsFourWheelDelivery());
            orderDeliveryDetail.setIsManual(deliveryOrder.getIsManual());
        } else {
            orderDeliveryDetail = new OrderDeliveryDetail(orderKey);
        }

        orderDeliveryDetail.fillOperateItem(conditionInOrder, deliveryAbility, deliveryOrder);
        orderDeliveryDetail.setDisplayCancelStatusEnum(isCanceling(conditionInOrder, deliveryOrder, deliveryAbility) ? DisplayCancelStatusEnum.CANCELING : DisplayCancelStatusEnum.DEFAULT);
        orderDeliveryDetail.setWaybillList(originWaybillInfoList);
        return orderDeliveryDetail;
    }

    public static OrderDeliveryDetail buildFromDeliveryOrder(DeliveryOrder deliveryOrder, DeliveryAbility deliveryAbility, DeliveryOrderLog deliveryOrderLog, DeliveryOrderLog deliveryOrderCurrentStatusLog) {
        OrderDeliveryDetail orderDeliveryDetail = null;

        orderDeliveryDetail = new OrderDeliveryDetail(deliveryOrder.getId(), deliveryOrder.getTenantId(), deliveryOrder.getStoreId(),
                deliveryOrder.getOrderKey(), deliveryOrder.getChannelOrderId(), deliveryOrder.getReceiver(),
                deliveryOrder.getDeliveryChannel(), deliveryOrder.getChannelDeliveryId(),
                deliveryOrder.getChannelServicePackageCode(),
                deliveryOrder.getStatus(),
                deliveryOrder.getExceptionType(), deliveryOrder.getExceptionDescription(), deliveryOrder.getDeliveryExceptionCode(),
                buildStartWaitAssginRiderTime(deliveryOrderLog), deliveryOrder.getRiderInfo(), deliveryOrder.getActiveStatus(),
                deliveryOrder.getDeliveryFee(), deliveryOrder.getDistance());
        orderDeliveryDetail.setDeliveryStatusChangeTime(Objects.nonNull(deliveryOrderCurrentStatusLog) ? deliveryOrderCurrentStatusLog.getChangeTime() : null);
        orderDeliveryDetail.setDeliveryCount(deliveryOrder.getDeliveryCount());
        orderDeliveryDetail.setCreateTime(TimeUtil.toMilliSeconds(deliveryOrder.getCreateTime()));
        orderDeliveryDetail.setTransType(deliveryOrder.getTransType());
        orderDeliveryDetail.setTipFee(deliveryOrder.getTipAmount());
        orderDeliveryDetail.setDealDeadline(deliveryOrder.getDealDeadline());
        orderDeliveryDetail.setOrderBizType(deliveryOrder.getOrderBizType());
        orderDeliveryDetail.setSignPosition(deliveryOrder.getSignPosition());
        if (Objects.nonNull(deliveryOrder.getAssessDeliveryTime())) {
            orderDeliveryDetail.setAssessDeliveryTime(deliveryOrder.getAssessDeliveryTime());
        }

        //todo check 歪马用不到 先不设置
        //orderDeliveryDetail.fillOperateItem(conditionInOrder, deliveryAbility, deliveryOrder);
        orderDeliveryDetail.setDisplayCancelStatusEnum(isCanceling(null, deliveryOrder, deliveryAbility) ? DisplayCancelStatusEnum.CANCELING : DisplayCancelStatusEnum.DEFAULT);
        return orderDeliveryDetail;
    }

    private static LocalDateTime buildStartWaitAssginRiderTime(DeliveryOrderLog deliveryOrderLog) {

        if (deliveryOrderLog != null) {
            return deliveryOrderLog.getChangeTime();
        }
        return null;
    }

    private static boolean isCanceling(DeliveryConditionInOrder conditionInOrder, DeliveryOrder deliveryOrder, DeliveryAbility deliveryAbility) {
        if (Objects.isNull(deliveryOrder)) {
            return false;
        }
        boolean isAggregation = deliveryAbility.isHasAggrDeliveryChannel();
        boolean isInCanCancelStatus = MccConfigUtils.getCancelDeliveryStatusList().contains(deliveryOrder.getStatus().getCode());
        boolean isCanceling = deliveryOrder.getCancelMark().equals(CancelMarkEnum.CANCELING.getValue());
        return isAggregation && isInCanCancelStatus && isCanceling;
    }

}
