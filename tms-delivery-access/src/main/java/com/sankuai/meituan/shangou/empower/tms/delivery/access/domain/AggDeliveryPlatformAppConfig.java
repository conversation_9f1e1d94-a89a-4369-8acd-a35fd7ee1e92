package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.MaltFarmSignUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.UrlUtils;

import lombok.Data;

/**
 * 聚合平台秘钥信息
 *
 * <AUTHOR>
 * @since 2021/04/08 17:01
 */
@Data
public class AggDeliveryPlatformAppConfig {
    private String appKey;
    private String appSecret;
    private String configUrl;
    private String redirectUrl;
    private String version;

    public void fillRedirectUrl(Long orderId, Long storeId){
        String url = MessageFormat.format(redirectUrl, appKey, String.valueOf(orderId), String.valueOf(storeId));
        UrlUtils.UrlInfo urlInfo = UrlUtils.buildUrlInfo4Url(url);
        Map<String, String> searchParams = urlInfo.getSearchParams();
        Map<String, Object> signMap = new HashMap<>(searchParams);
        String sign = MaltFarmSignUtils.generateSignature(signMap, appSecret);
        searchParams.put("sign", sign);
        this.redirectUrl = UrlUtils.buildUrl4UrlInfo(urlInfo);
    }
}
