package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.google.common.base.Preconditions;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * 赋能订单业务key
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
@Getter
@ToString
@EqualsAndHashCode
public class OrderKey {

	/**
	 * 赋能租户ID
	 */
	private final Long tenantId;

	/**
	 * 赋能门店ID
	 */
	private final Long storeId;

	/**
	 * 赋能订单id
	 */
	private final Long orderId;

	public OrderKey(Long tenantId, Long storeId, Long orderId) {
		Preconditions.checkNotNull(tenantId, "tenantId is null");
		Preconditions.checkNotNull(storeId, "storeId is null");
		Preconditions.checkNotNull(orderId, "orderId is null");

		this.tenantId = tenantId;
		this.storeId = storeId;
		this.orderId = orderId;
	}
}
