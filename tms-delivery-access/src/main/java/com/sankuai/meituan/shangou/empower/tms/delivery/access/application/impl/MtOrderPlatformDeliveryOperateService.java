package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.impl;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.base.Splitter;
import com.google.common.collect.ArrayListMultimap;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.enums.DistributeTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.DeliveryOperationFailedException;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.platform.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.platform.response.CheckFourWheelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.platform.response.DispatchFourWheelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.platform.response.PreviewFourWheelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.platform.response.VehicleDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.DeliveryOperationResultDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.OcmsResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.OrderPlatformDeliveryOperateThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

/**
 * MtOrderPlatformDeliveryOperateServiceFacade
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
@Slf4j
@Rhino
public class MtOrderPlatformDeliveryOperateService extends BaseOrderPlatformDeliveryOperateService {

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;
    @Resource
    private OrderPlatformDeliveryOperateThriftService orderPlatformDeliveryOperateThriftService;


    @Override
    public boolean updateTip(UpdateTipCmd cmd) {
        operationBeforeCheck(cmd,
                orderInfo -> {
                    DistributeTypeEnum orderDistributeType = getOrderDistributeType(orderInfo.getOriginalDistributeType());
                    if (orderDistributeType != DistributeTypeEnum.ZONG_BAO && orderDistributeType != DistributeTypeEnum.KUAI_SONG) {
                        throw new DeliveryOperationFailedException("配送类型不符");
                    }
                    return orderInfo;
                },
                deliveryOrder -> {
                    OcmsResponse<DeliveryOperationResultDto> resp = null;
                    try {
                        resp = orderPlatformDeliveryOperateThriftService.updateTip(
                                new UpdateTipReq(cmd.getTenantId(), cmd.getOfflineStoreId(),
                                        cmd.getOrderInfo().getChannelOrderId(), cmd.getChannelId(), cmd.getTipAmount()));
                    } catch (TException e) {
                        throw new DeliveryOperationFailedException("调用加小费异常！");
                    }
                    log.info("#updateTip response: {}", resp);
                    if (resp.getCode() != 0) {
                        throw new DeliveryOperationFailedException(resp.getMsg());
                    }

                    if (deliveryOrder != null) {
                        // 调用记录小费
                        deliveryOrder.setTipAmount(new BigDecimal(cmd.getTipAmount()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                        deliveryOrderRepository.saveDeliveryOrder(deliveryOrder);
                    }
                    return deliveryOrder;
                });
        return true;
    }

    @Override
    public boolean cancelDelivery(CancelDeliveryCmd cmd) {
        if (!cancelDeliveryOperationCheck(cmd)) {
            return false;
        }

        //ocms_channel 接口取消配送
        try {
            OcmsResponse<DeliveryOperationResultDto> resp = orderPlatformDeliveryOperateThriftService.cancelPlatformDelivery(
                    new CancelDeliveryReq(cmd.getTenantId(), cmd.getOfflineStoreId(),
                            cmd.getOrderInfo().getChannelOrderId(), cmd.getChannelId()));
            log.info("#callDelivery response: {}", resp);
            if (!resp.isSuccess()) {
                throw new DeliveryOperationFailedException(resp.getMsg());
            }
            return true;
        } catch (TException e) {
            log.error("MtOrderPlatformDeliveryOperateService#callDelivery error! cmd: {}", cmd);
            throw new CommonRuntimeException("取消配送失败！");
        }
    }

    /**
     * 取消配送操作校验，按钮展示校验
     */
    public boolean cancelDeliveryOperationCheck(CancelDeliveryCmd cmd) {
        operationBeforeCheck(cmd,
                orderInfo -> {
                    DistributeTypeEnum orderDistributeType = getOrderDistributeType(orderInfo.getOriginalDistributeType());
                    if (orderDistributeType != DistributeTypeEnum.ZHUAN_SONG && orderDistributeType != DistributeTypeEnum.KUAI_SONG
                        && orderDistributeType != DistributeTypeEnum.COMBO && orderDistributeType != DistributeTypeEnum.ZONG_BAO) {
                        throw new DeliveryOperationFailedException("配送类型不符");
                    }
                    return orderInfo;
                },
                deliveryOrder -> {
                    if (deliveryOrder != null) {
                        //大等于骑手已取货状态
                        if (deliveryOrder.getStatus().getCode() >= DeliveryStatusEnum.RIDER_ASSIGNED.getCode()) {
                            throw new DeliveryOperationFailedException("运单状态不符");
                        }
                        //运单小于15分钟
                        if (LocalDateTime.now().minusMinutes(MccConfigUtils.getMtCancelDeliveryLimitTime()).isBefore(deliveryOrder.getCreateTime())) {
                            throw new DeliveryOperationFailedException("运单小于15分钟");
                        }
                    }
                    return deliveryOrder;
                });

        return true;
    }

    @CatTransaction
    @Degrade(rhinoKey = "mtOrderPlatformDeliveryOperateService.dispatchPreview", fallBackMethod = "dispatchPreviewFallBack", timeoutInMilliseconds = 3000)
    @Override
    public FourWheelPreviewInfo dispatchPreview(FourWheelPreviewCmd cmd) throws DeliveryOperationFailedException {
        try {
            PreviewFourWheelReq req = new PreviewFourWheelReq();
            req.setTenantId(cmd.getTenantId());
            req.setStoreId(cmd.getOfflineStoreId());
            req.setChannelOrderId(cmd.getOrderInfo().getChannelOrderId());
            req.setChannelId(cmd.getChannelId());
            req.setPreviewType(cmd.getPreviewType());

            OcmsResponse<PreviewFourWheelDTO> resp = orderPlatformDeliveryOperateThriftService.previewFourWheel(req);
            log.info("#dispatchPreview response: {}", resp);
            if (!resp.isSuccess()) {
                throw new DeliveryOperationFailedException(resp.getMsg());
            }
            PreviewFourWheelDTO wheelDTO = resp.getData();
            if (wheelDTO == null) {
                throw new DeliveryOperationFailedException("询价获取失败");
            }
            FourWheelPreviewInfo previewInfo = new FourWheelPreviewInfo();
            List<FourWheelInfo> fourWheelInfoList = new ArrayList<>();
            previewInfo.setFourWheelInfoList(fourWheelInfoList);
            if(CollectionUtils.isEmpty(wheelDTO.getPreviewList())){
                return previewInfo;
            }

            ArrayListMultimap<Integer,VehicleDetail> multimap=ArrayListMultimap.create();
            wheelDTO.getPreviewList().forEach(preview->{
                multimap.put(preview.getVehicleCode(),preview);
            });
            Map<String,FourWheelBaseInfo> baseMap = MccConfigUtils.getFourWheelBaseInfo();

            for (Integer code : multimap.keySet()){
                List<VehicleDetail> detailList = multimap.get(code);
                if(CollectionUtils.isEmpty(detailList)){
                    continue;
                }

                VehicleDetail vehicleDetail = null;
                Double totalFeeMin = null;
                Double totalFeeMax = null;
                for (VehicleDetail d : detailList){
                    if(d==null || d.getTotalFee() == null){
                        continue;
                    }
                    vehicleDetail = d;
                    if(totalFeeMin == null || totalFeeMin>d.getTotalFee()){
                        totalFeeMin = d.getTotalFee();
                    }
                    if(totalFeeMax == null || totalFeeMax<d.getTotalFee()){
                        totalFeeMax = d.getTotalFee();
                    }
                }
                if(vehicleDetail == null){
                    continue;
                }
                if(totalFeeMin ==null && totalFeeMax == null){
                    continue;
                }

                FourWheelBaseInfo baseInfo = getBase(baseMap,vehicleDetail.getVehicleCode());


                FourWheelInfo info = new FourWheelInfo();
                info.setVehicleCode(vehicleDetail.getVehicleCode());
                info.setVehicleName(baseInfo == null ? null:baseInfo.getName());
                info.setVehicleFeeRange(Objects.equals(totalFeeMin, totalFeeMax) ? totalFeeMin+"":totalFeeMin+"-"+totalFeeMax);
                info.setIsDelivery(vehicleDetail.getIsDelivery());
                info.setTotalFeeRangeMin(totalFeeMin+"");
                info.setTotalFeeRangeMax(totalFeeMax+"");
                if(StringUtils.isNotBlank(vehicleDetail.getVehicleFeeRange()) && vehicleDetail.getIsDelivery()){
                    //已发的汽车配送 totalFee为0，费用在range里
                    List<String> feeList = Splitter.on("-").splitToList(vehicleDetail.getVehicleFeeRange());
                    if(CollectionUtils.isNotEmpty(feeList)){
                        if(feeList.size() == 1){
                            info.setTotalFeeRangeMin(feeList.get(0));
                            info.setTotalFeeRangeMax(feeList.get(0));
                        }else {
                            info.setTotalFeeRangeMin(feeList.get(0));
                            info.setTotalFeeRangeMax(feeList.get(1));
                        }
                    }
                }
                info.setUrl(baseInfo == null ? null:baseInfo.getUrl());
                fourWheelInfoList.add(info);
            }
            return previewInfo;
        } catch (TException e) {
            log.error("MtOrderPlatformDeliveryOperateService#dispatchPreview error! cmd: {}", cmd);
            throw new CommonRuntimeException("四轮询价失败");
        }
    }

    public FourWheelPreviewInfo dispatchPreviewFallBack(FourWheelPreviewCmd cmd) throws DeliveryOperationFailedException{
        log.info("dispatchPreviewFallBack cmd:{}",cmd);
        return new FourWheelPreviewInfo();
    }

    private FourWheelBaseInfo getBase(Map<String,FourWheelBaseInfo> baseMap,Integer code){
        if(MapUtils.isEmpty(baseMap) || code == null){
            return null;
        }
        if(baseMap.containsKey(""+code)){
            return baseMap.get(""+code);
        }
        return baseMap.get("-1");
    }

    @CatTransaction
    @Degrade(rhinoKey = "mtOrderPlatformDeliveryOperateService.orderDispatch", fallBackMethod = "orderDispatchFallBack", timeoutInMilliseconds = 3000)
    @Override
    public boolean orderDispatch(FourWheelDispatchCmd cmd) throws DeliveryOperationFailedException {
        try {
            DispatchFourWheelReq req = new DispatchFourWheelReq();
            List<VehicleDetail> vehicleDetailList = new ArrayList<>();
            cmd.getFourWheelCmdDtoList().forEach(dto->{
                VehicleDetail detail = new VehicleDetail();
                detail.setVehicleCode(dto.getVehicleCode());
                detail.setVehicleName(dto.getVehicleName());
                detail.setVehicleFeeRange(dto.getVehicleFeeRange());
                vehicleDetailList.add(detail);
            });
            req.setTenantId(cmd.getTenantId());
            req.setStoreId(cmd.getOfflineStoreId());
            req.setChannelOrderId(cmd.getOrderInfo().getChannelOrderId());
            req.setChannelId(cmd.getChannelId());
            req.setDeliveryType(cmd.getDeliveryType());
            req.setVehicleDetailList(vehicleDetailList);
            OcmsResponse<DispatchFourWheelDTO> resp = orderPlatformDeliveryOperateThriftService.dispatchFourWheel(req);
            log.info("#orderDispatch response: {}", resp);
            if (!resp.isSuccess()) {
                throw new DeliveryOperationFailedException(resp.getMsg());
            }
            return true;
        } catch (TException e) {
            log.error("MtOrderPlatformDeliveryOperateService#dispatchFourWheel error! cmd: {}", cmd);
            throw new CommonRuntimeException("发送四轮失败");
        }
    }

    public boolean orderDispatchFallBack(FourWheelDispatchCmd cmd) throws DeliveryOperationFailedException {
        log.info("dispatchPreviewFallBack cmd:{}",cmd);
        return false;
    }

    @CatTransaction
    @Degrade(rhinoKey = "mtOrderPlatformDeliveryOperateService.fourWheelCheck", fallBackMethod = "fourWheelCheckFallBack", timeoutInMilliseconds = 3000)
    @Override
    public FourWheelCheckInfo fourWheelCheck(FourWheelCheckCmd cmd) throws DeliveryOperationFailedException {
        try {
            OcmsResponse<CheckFourWheelDTO> resp = orderPlatformDeliveryOperateThriftService.checkFourWheelDelivery(
                    new CheckFourWheelReq(cmd.getTenantId(), cmd.getOfflineStoreId(),
                            cmd.getViewOrderId(), cmd.getChannelId()));
            log.info("#fourWheelCheck response: {}", resp);
            if (!resp.isSuccess()) {
                throw new DeliveryOperationFailedException(resp.getMsg());
            }
            CheckFourWheelDTO wheelDTO = resp.getData();
            if(wheelDTO == null){
                throw new DeliveryOperationFailedException("获取信息为空");
            }
            FourWheelCheckInfo info = new FourWheelCheckInfo();
            info.setCanPushFourWheel(wheelDTO.getCanPushFourWheel());
            return info;
        } catch (TException e) {
            log.error("MtOrderPlatformDeliveryOperateService#fourWheelCheck error! cmd: {}", cmd);
            throw new CommonRuntimeException("获取四轮校验失败");
        }
    }

    public FourWheelCheckInfo fourWheelCheckFallBack(FourWheelCheckCmd cmd) throws DeliveryOperationFailedException {
        log.info("fourWheelCheckFallBack cmd:{}",cmd);
        return null;
    }

    private <R extends BaseOrderPlatformDeliveryOperateCmd> void operationBeforeCheck(R request,
                                                                                      UnaryOperator<OrderInfo> orderInfoFunction,
                                                                                      UnaryOperator<DeliveryOrder> deliveryOrderFunction) {

        OrderInfo orderInfo = request.getOrderInfo();

        DeliveryOrder deliveryOrder = null;
        List<DeliveryOrder> deliveryOrders =
                deliveryOrderRepository.getDeliveryOrders(new OrderKey(request.getTenantId(), request.getOfflineStoreId(), request.getOrderInfo().getOrderKey().getOrderId()));

        boolean orderPlatformDelivery = false;
        if (CollectionUtils.isNotEmpty(deliveryOrders)) {
            deliveryOrders = deliveryOrders.stream()
                    .sorted(Comparator.comparing(DeliveryOrder::getId, Comparator.reverseOrder()))
                    .collect(Collectors.toList());
            deliveryOrder = deliveryOrders.get(0);

            orderPlatformDelivery = deliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode();
        }

        if (!orderPlatformDelivery && !orderInfo.isSelfDelivery()) {
            throw new DeliveryOperationFailedException("非平台配送");
        }

        orderInfoFunction.apply(orderInfo);
        deliveryOrderFunction.apply(deliveryOrder);
    }


    private DistributeTypeEnum getOrderDistributeType(Integer originalDistributeType) {
        return Optional.ofNullable(originalDistributeType)
                .map(DistributeTypeEnum::enumOf)
                .orElse(DistributeTypeEnum.UN_KNOWN);
    }

    @Override
    public Integer ofOrderChannel() {
        return DynamicChannelType.MEITUAN.getChannelId();
    }

}
