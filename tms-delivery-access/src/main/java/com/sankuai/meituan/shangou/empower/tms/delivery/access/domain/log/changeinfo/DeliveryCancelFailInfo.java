package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 配送主动取消失败信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/8
 */
@Getter
@ToString
@AllArgsConstructor
public class DeliveryCancelFailInfo extends DeliveryChangeInfo {

	private final Integer cancelFailReasonCode;

	private final String cancelFailReasonDesc;
}
