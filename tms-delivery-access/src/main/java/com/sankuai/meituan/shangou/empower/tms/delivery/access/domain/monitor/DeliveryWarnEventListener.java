package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor;

import com.google.common.eventbus.Subscribe;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.DeliveryWarnEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.DeliveryWarnClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/6
 */
@Slf4j
@Component
@SuppressWarnings("UnstableApiUsage")
public class DeliveryWarnEventListener {

	@Resource
	private DeliveryWarnClient deliveryWarnClient;

	@Subscribe
	public void listenDeliveryWarnEvent(DeliveryWarnEvent deliveryWarnEvent) {
		try {
			deliveryWarnClient.warn(deliveryWarnEvent.getWarnMessage());
		} catch (Exception e) {
			log.error("配送告警事件消费失败", e);
		}
	}

}
