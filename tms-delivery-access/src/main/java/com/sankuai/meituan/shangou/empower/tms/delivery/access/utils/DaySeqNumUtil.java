package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024-03-01
 */
public final class DaySeqNumUtil {
    private DaySeqNumUtil() {
    }

    public static final String DAY_SEQ_NUM = "daySeqNum";

    /**
     * 返回订单流水号
     */
    public static String getDaySeqNum(Integer oldValue, String newValue) {
        if (MccConfigUtils.getDaySeqNumSwitch() && StringUtils.isNotEmpty(newValue)) {
            return newValue;
        }
        return oldValue == null ? null : String.valueOf(oldValue);
    }

    /**
     * 获取订单流水号
     * 歪马租户的操作
     */
    public static String getDaySeqNumWithoutDHTenant(Integer oldValue, String newValue, Long tenantId) {
        return getDaySeqNum(oldValue, newValue);
    }

    /**
     * 获取订单流水号
     * 歪马租户的操作
     */
    public static String getDaySeqNumWithoutDHTenant(Long oldValue, String newValue, Long tenantId) {
        return getDaySeqNumWithoutDHTenant(oldValue == null ? null : Math.toIntExact(oldValue), newValue, tenantId);
    }
}