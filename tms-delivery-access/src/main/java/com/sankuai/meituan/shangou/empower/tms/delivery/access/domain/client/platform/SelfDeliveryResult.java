package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform;


import lombok.Builder;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * 平台转自配送返回结果
 */
@Data
@ToString
@Builder
public class SelfDeliveryResult {

    private Integer status;

    private String msg;

    public static SelfDeliveryResult isSuccess() {
        return SelfDeliveryResult.builder().status(2001).msg("非平台配送转单返回成功").build();
    }

    public static SelfDeliveryResult inValidTrans() {
        return SelfDeliveryResult.builder().status(2002).msg("转单前后渠道一致无需转单").build();
    }
}
