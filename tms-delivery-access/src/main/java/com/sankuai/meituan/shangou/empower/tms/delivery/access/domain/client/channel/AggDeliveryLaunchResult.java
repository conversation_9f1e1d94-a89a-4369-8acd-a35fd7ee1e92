package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;

import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2021/04/07
 */
@Getter
@ToString
public class AggDeliveryLaunchResult {

	/**
	 * 配送是否发起成功
	 */
	private final boolean success;
	/**
	 * 是否需要重试(网络超时/系统异常等场景需要重试)
	 */
	private final boolean needRetry;
	/**
	 * 拒单信息，包括原因类型和描述
	 * 配送发起失败时有值
	 */
	private final DeliveryExceptionInfo rejectInfo;

	public AggDeliveryLaunchResult() {
		this.success = true;
		this.needRetry = false;
		this.rejectInfo = null;
	}

	public AggDeliveryLaunchResult(boolean needRetry, DeliveryExceptionInfo rejectInfo) {
		Preconditions.checkNotNull(rejectInfo, "rejectInfo is null");

		this.success = false;
		this.needRetry = needRetry;
		this.rejectInfo = rejectInfo;
	}
}
