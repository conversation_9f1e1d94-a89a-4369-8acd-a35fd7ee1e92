package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/10
 */
@Getter
@ToString
public class RiderLocationQueryResult {

	/**
	 * 配送是否发起成功
	 */
	private final boolean success;
	/**
	 * 是否需要重试(网络超时/系统异常等场景需要重试)
	 */
	private final boolean needRetry;
	/**
	 * 坐标系类型
	 */
	private final CoordinateTypeEnum coordinateType;
	/**
	 * 坐标点
	 */
	private final CoordinatePoint coordinatePoint;

	public RiderLocationQueryResult(boolean needRetry) {
		this.success = false;
		this.needRetry = needRetry;
		this.coordinateType = null;
		this.coordinatePoint = null;
	}

	public RiderLocationQueryResult(CoordinateTypeEnum coordinateType, CoordinatePoint coordinatePoint) {
		this.success = true;
		this.needRetry = false;
		this.coordinateType = coordinateType;
		this.coordinatePoint = coordinatePoint;
	}
}
