package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

public enum OrderDownTypeEnum {

	DATA_DOWN(1, "有数据降级"),
	NO_DOWN(0, "没有数据降级"),
	;

	private final int value;
	private final String desc;

	OrderDownTypeEnum(int value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public int getValue() {
		return this.value;
	}

	public String getDesc() {
		return this.desc;
	}
}
