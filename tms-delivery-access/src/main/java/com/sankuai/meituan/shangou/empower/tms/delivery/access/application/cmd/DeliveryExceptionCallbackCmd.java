package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 配送异常结果回调请求
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/4/19
 */
@Getter
@ToString
@AllArgsConstructor
public class DeliveryExceptionCallbackCmd {

	private final Long orderId;

	private final DeliveryExceptionTypeEnum exceptionType;

	/**
	 * @see DeliveryExceptionCodeEnum
	 */
	private final Integer code;

	private final String reason;

	private final LocalDateTime exceptionTime;

}
