package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MoneyUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;

import java.math.BigDecimal;

/**
 * 配送渠道预发信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/4
 */
@Getter
@ToString
public class DeliveryChannelPreLaunchInfo implements Comparable<DeliveryChannelPreLaunchInfo> {

	/**
	 * 配送渠道
	 */
	private final DeliveryChannelEnum deliveryChannel;

	/**
	 * 服务包编码
	 */
	private final String servicePackage;

	/**
	 * 是否可以发起配送
	 */
	private final boolean available;

	/**
	 * 失败原因(不可发起配送原因)
	 */
	private final String failReason;

	/**
	 * 预估配送费用, 单位：元
	 */
	private final BigDecimal estimatedDeliveryFee;

	/**
	 * 优惠金额, 单位：元
	 */
	private final BigDecimal discountAmount;

	public DeliveryChannelPreLaunchInfo(DeliveryChannelEnum deliveryChannel, String servicePackage, String failReason) {
		this.deliveryChannel = deliveryChannel;
		this.servicePackage = servicePackage;
		this.available = false;
		this.failReason = failReason;
		this.estimatedDeliveryFee = null;
		this.discountAmount = null;
	}

	public DeliveryChannelPreLaunchInfo(DeliveryChannelEnum deliveryChannel, String servicePackage, BigDecimal estimatedDeliveryFee) {
		this.deliveryChannel = deliveryChannel;
		this.servicePackage = servicePackage;
		this.available = true;
		this.failReason = StringUtils.EMPTY;
		this.estimatedDeliveryFee = estimatedDeliveryFee;
		this.discountAmount = null;
	}

	public DeliveryChannelPreLaunchInfo(DeliveryChannelEnum deliveryChannel, String servicePackage, BigDecimal estimatedDeliveryFee,
										BigDecimal discountAmount) {
		this.deliveryChannel = deliveryChannel;
		this.servicePackage = servicePackage;
		this.available = true;
		this.failReason = StringUtils.EMPTY;
		this.estimatedDeliveryFee = estimatedDeliveryFee;
		this.discountAmount = discountAmount;
	}

	@Override
	public int compareTo(@NonNull DeliveryChannelPreLaunchInfo o) {
		if (this.isAvailable() && !o.isAvailable()) {
			return -1;
		} else if (!this.isAvailable() && o.isAvailable()) {
			return 1;
		} else {
			return MoneyUtil.compare(this.getEstimatedDeliveryFee(), o.getEstimatedDeliveryFee());
		}
	}
}
