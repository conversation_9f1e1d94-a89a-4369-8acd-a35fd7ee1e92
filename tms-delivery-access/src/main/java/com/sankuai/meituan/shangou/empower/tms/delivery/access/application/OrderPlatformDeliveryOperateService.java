package com.sankuai.meituan.shangou.empower.tms.delivery.access.application;

import com.sankuai.meituan.shangou.empower.ocms.channel.component.ServingChannel;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.DeliveryOperationFailedException;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.FourWheelCheckInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.FourWheelPreviewInfo;

/**
 * OrderPlatformDeliveryOperateService
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
public interface OrderPlatformDeliveryOperateService extends ServingChannel {

    boolean updateTip(UpdateTipCmd cmd) throws DeliveryOperationFailedException;

    boolean cancelDelivery(CancelDeliveryCmd cmd) throws DeliveryOperationFailedException;

    boolean callDelivery(CallDeliveryCmd cmd) throws DeliveryOperationFailedException;

    boolean reportException(ReportExceptionCmd cmd) throws DeliveryOperationFailedException;

    boolean auditException(AuditExceptionCmd cmd) throws DeliveryOperationFailedException;

    FourWheelPreviewInfo dispatchPreview(FourWheelPreviewCmd cmd) throws DeliveryOperationFailedException;

    boolean orderDispatch(FourWheelDispatchCmd cmd) throws DeliveryOperationFailedException;

    FourWheelCheckInfo fourWheelCheck(FourWheelCheckCmd cmd) throws DeliveryOperationFailedException;

}
