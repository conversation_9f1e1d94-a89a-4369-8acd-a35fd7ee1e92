package com.sankuai.meituan.shangou.empower.tms.delivery.access.application;

import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.config.SdmsStoreConfigServiceClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.facade.RouteFacade;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-09-09
 * @email <EMAIL>
 */
@Slf4j
@Service
public class RiderDeliveryQueryAppService {

    @Resource
    private RouteFacade routeFacade;
    @Resource
    private SdmsStoreConfigServiceClient sdmsStoreConfigServiceClient;
    @Resource
    private OrderSystemClient orderSystemClient;

    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public Long calcSdmsOrderPushDownTimestamp(Long merchantId, Long warehouseId, String tradeOrderNo, Integer orderBizType) {

        Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(
                merchantId, warehouseId, tradeOrderNo, DynamicOrderBizType.findOf(orderBizType).getChannelId(), false
        );
        if (orderInfoResult.isFail()) {
            throw new ThirdPartyException("查询订单失败");
        }

        OrderInfo orderInfo = orderInfoResult.getInfo();
        Result<Double> distanceResult = routeFacade.queryRidePathDistance(
                merchantId, warehouseId,
                orderInfo.getReceiver().getReceiverAddress().getCoordinatePoint()
        );
        if (distanceResult != null && distanceResult.isSuccess() && Objects.nonNull(distanceResult.getInfo())) {
            Optional<Long> timestampOpt = sdmsStoreConfigServiceClient.calcPushDownTimestamp(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(), orderInfo.getChannelOrderId(), orderInfo.getOrderBizType(), distanceResult.getInfo().longValue());
            if (timestampOpt.isPresent()) {
                return timestampOpt.get();
            }
        }
        return -1L;
    }

}
