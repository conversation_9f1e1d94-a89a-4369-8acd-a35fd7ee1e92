package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

/**
 * 配送超时检查类型枚举
 */
public enum DeliveryTimeOutCheckTypeEnum {
    /**
     * 商家自配送检查 是否骑手已接单(包括后续骑手到店、取货、送达)
     */
    MERCHANT_SELF_DELIVERY_CHECK_RIDER_ASSIGNED(40),
    /**
     * 商家自配送检查 是否骑手已取货(包括后续骑手送达)
     */
    MERCHANT_SELF_DELIVERY_CHECK_RIDER_TAKEN_GOODS(50),

    /**
     *  检查是否骑手已送达
     */
    CHECK_DELIVERY_DONE(60),

    /**
     *  检查是否骑手已送达(对应B端的超时判定规则)
     */
    CHECK_DELIVERY_DONE_4_RIDER(70),

    /**
     * 检查是否骑手已送达(对应C端的超时判定规则)
     */
    CHECK_DELIVERY_DONE_4_CUS(80),
    /**
     *  商家自配送检查 检查配送是否即将超时
     */
    MERCHANT_SELF_DELIVERY_CHECK_DELIVERY_WILL_TIMEOUT(90),
    ;

    private final int type;

    DeliveryTimeOutCheckTypeEnum(int type) {
        this.type = type;
    }
}