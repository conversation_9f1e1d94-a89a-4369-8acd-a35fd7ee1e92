package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 平台配送更新信息命令
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/7
 */
@Getter
@ToString
public class PlatformDeliveryUpdateCmd {

	private final DeliveryOrder deliveryOrder;

	private final DeliveryStatusEnum status;

	private final Rider rider;

	private final DeliveryExceptionInfo exceptionInfo;

	private final LocalDateTime updateTime;

	@Setter
	private String failType;
	@Setter
	private String dealDeadline;

	/**
	 * 是否需要重新呼叫渠道平台配送，1代表需要
	 */
	private Integer isRecallDelivery;

	@Setter
	private Integer isFourWheelDelivery;

	@Setter
	private Integer isManual;

	@Setter
	private Integer performanceServiceFee;

	public PlatformDeliveryUpdateCmd(DeliveryOrder deliveryOrder,
	                                 DeliveryStatusEnum status,
	                                 Rider rider,
	                                 DeliveryExceptionInfo exceptionInfo,
	                                 LocalDateTime updateTime,
									 Integer isRecallDelivery,Integer isFourWheelDelivery,Integer isManual,Integer performanceServiceFee) {
		Preconditions.checkNotNull(deliveryOrder, "deliveryOrder is null");
		Preconditions.checkNotNull(status, "status is null");
		Preconditions.checkNotNull(updateTime, "updateTime is null");

		this.deliveryOrder = deliveryOrder;
		this.status = status;
		this.rider = rider;
		this.exceptionInfo = exceptionInfo;
		this.updateTime = updateTime;
		this.isRecallDelivery = isRecallDelivery;
		this.isFourWheelDelivery = isFourWheelDelivery;
		this.isManual = isManual;
		this.performanceServiceFee = performanceServiceFee;
	}
}
