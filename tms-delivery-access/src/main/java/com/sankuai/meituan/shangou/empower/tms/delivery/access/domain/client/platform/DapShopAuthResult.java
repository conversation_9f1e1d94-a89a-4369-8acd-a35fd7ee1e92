package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Builder
public class DapShopAuthResult {

    private Integer code;

    private String message;

    private Boolean isAuthed;

    public static DapShopAuthResult isSuccess() {
        return DapShopAuthResult.builder().isAuthed(true).build();
    }
}
