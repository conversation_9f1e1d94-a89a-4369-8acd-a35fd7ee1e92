package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import lombok.Getter;
import lombok.ToString;

/**
 * CancelDeliveryCmd
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
@Getter
@ToString(callSuper = true)
public class CancelDeliveryCmd extends BaseOrderPlatformDeliveryOperateCmd {

    public CancelDeliveryCmd(Long tenantId, Long offlineStoreId, OrderInfo orderInfo,
            Integer channelId, Operator operator) {
        super(tenantId, offlineStoreId, orderInfo, channelId, operator);
    }

}
