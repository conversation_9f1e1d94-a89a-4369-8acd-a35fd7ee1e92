package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ChannelStoreConfig;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * 配送计划，用于指导如何发起配送
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/4
 */
@Getter
@ToString
public class DeliveryPlan {

	/**
	 * 配送计划类型
	 */
	private final DeliveryPlanType deliveryPlanType;

	/**
	 * 多配送渠道顺序集合
	 */
	private final List<ChannelStoreConfig> deliveryChannelStoreList;

	public DeliveryPlan(DeliveryPlanType deliveryPlanType, List<ChannelStoreConfig> deliveryChannelStoreList) {
		Preconditions.checkNotNull(deliveryPlanType, "deliveryPlanType is null");
		Preconditions.checkNotNull(deliveryChannelStoreList, "deliveryChannelStoreList is null");

		this.deliveryPlanType = deliveryPlanType;
		this.deliveryChannelStoreList = deliveryChannelStoreList;
	}
}
