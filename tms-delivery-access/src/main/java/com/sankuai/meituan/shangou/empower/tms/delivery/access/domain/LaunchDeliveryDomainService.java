package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryMarkSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryChannelPreLaunchInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.push.PushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.DeliveryLaunchFailedEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.DeliveryPreLaunchFailedEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryWarnEventPublisher;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.AssessTimeAppender;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.PricingRouteInfoDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryDimensionPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PricingRouteInfoRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RouteInfoDTO;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.facade.RouteFacade;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * 发起配送领域服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/6
 */
@Slf4j
@Service
public class LaunchDeliveryDomainService {

	public static final String DELIVERY = "delivery";
	public static final String DELIVERY_ORDER = "delivery.order";
	@Resource
	private DeliveryWarnEventPublisher deliveryWarnEventPublisher;
	@Resource
	private DeliveryPlatformClient deliveryPlatformClient;
	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private PushClient pushClient;
	@Resource
	private RouteFacade routeFacade;
	@Resource
	private DeliveryTimeOutCheckService deliveryTimeOutCheckService;

	@Resource
	private DeliveryChannelApplicationService deliveryChannelApplicationService;

	@Resource
	private PricingRouteInfoRepository pricingRouteInfoRepository;

	@Resource
	private DeliveryNotifyService deliveryNotifyService;

	@Resource
	private AssessTimeAppender assessTimeAppender;
	@Resource
	private DeliveryDimensionPoiRepository deliveryDimensionPoiRepository;

	@Autowired
	private DeliveryMarkSquirrelOperationService deliveryMarkSquirrelOperationService;

	private static final Integer PRIVATE_DOMAIN_CHANNEL = 0;

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Result<List<DeliveryChannelPreLaunchInfo>> preLaunchDelivery(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
		Preconditions.checkNotNull(deliveryPoi, "deliveryPoi is null");
		Preconditions.checkNotNull(orderInfo, "orderInfo is null");

		//校验订单是否可以发起配送或转自配送
		if (!orderInfo.canLaunchDelivery() && !orderInfo.canTurnToSelfDelivery()) {
			log.warn("订单[{}]不满足发起配送条件，发三方配送失败", orderInfo);
			return new Result<>(new Failure(false, FailureCodeEnum.ORDER_STATUS_ERROR));
		}

		//校验订单收货人信息是否完整，是否可以发起配送
		if (orderInfo.getReceiver() == null || !orderInfo.getReceiver().isDeliverable()) {
			log.warn("订单[{}]收件人信息不完整，发三方配送失败", orderInfo);
			deliveryWarnEventPublisher.postEvent(new DeliveryPreLaunchFailedEvent(orderInfo, "收件人信息不完整"));
			return new Result<>(new Failure(false, FailureCodeEnum.INVALID_RECEIVER_ADDRESS));
		}

		//预发单
		return deliveryPlatformClient.preLaunch(deliveryPoi, orderInfo);
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> launchDelivery(DeliveryPoi deliveryPoi,
	                                        OrderInfo orderInfo,
	                                        DeliveryChannelEnum deliveryChannel,
	                                        String servicePackage,
	                                        BigDecimal estimatedDeliveryFee,
	                                        boolean acceptRetry,
											PlatformSourceEnum platformSourceEnum,Long fulfillOrderId) {
		Preconditions.checkNotNull(deliveryPoi, "deliveryPoi is null");
		Preconditions.checkNotNull(orderInfo, "orderInfo is null");
		Preconditions.checkNotNull(deliveryChannel, "deliveryChannel is null");
		Preconditions.checkNotNull(servicePackage, "servicePackage is null");

		//查当前是否有激活的运单，尝试幂等处理
		Optional<DeliveryOrder> activeDeliveryOrder = Optional.empty();
		if(MccConfigUtils.getDeliveryQueryTenantSwitch(deliveryPoi.getTenantId())){
			activeDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderWithTenant(orderInfo.getOrderKey().getOrderId(),deliveryPoi.getTenantId(),deliveryPoi.getStoreId());
		}else {
			activeDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(orderInfo.getOrderKey().getOrderId());
		}

		if (activeDeliveryOrder.isPresent()) {
			return handleExistingDeliveryOrder(deliveryPoi, orderInfo, activeDeliveryOrder.get(), acceptRetry);
		}


		try {
            //创建新运单并激活
			DeliveryOrder deliveryOrder = new DeliveryOrder(orderInfo, deliveryChannel.getCode(), servicePackage);
			//todo 需要等拣货融合后再改动
			if (!MccConfigUtils.checkIsDHTenant(orderInfo.getOrderKey().getTenantId())) {
				deliveryOrder.setPickDeliverySplitTag(true);
			}
			deliveryOrder.setDeliveryFee(estimatedDeliveryFee);
			if(platformSourceEnum!=null){
				deliveryOrder.setPlatformSourceEnum(platformSourceEnum);
				if(platformSourceEnum == PlatformSourceEnum.OFC){
					deliveryOrder.setStoreId(deliveryPoi.getStoreId());
					deliveryOrder.setOrderKey(new OrderKey(deliveryPoi.getTenantId(),deliveryPoi.getStoreId(),deliveryOrder.getOrderId()));
				}
			}
			if(fulfillOrderId !=null){
				deliveryOrder.setFulfillmentOrderId(fulfillOrderId);
			}

			//在创建运单时，为歪马的自配送填充距离
			RouteInfoDTO routeInfoDTO = null;
			if (isMerchantSelfDelivery(deliveryOrder)) {
				DeliveryDimensionPoi deliveryDimensionPoi = deliveryDimensionPoiRepository.queryDeliveryDimensionPoi(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId());

				Optional<RouteInfoDTO> routeInfoDTOOpt = routeFacade.queryRideRouteInfo4DrunkHorse(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getReceiver().getReceiverAddress().getCoordinatePoint());
				if (routeInfoDTOOpt.isPresent()) {
					if (DeliveryRiderMccConfigUtils.fusionSelfDegreeSwitchV2()) {
						routeInfoDTO = routeInfoDTOOpt.get();
						deliveryOrder.setDistance(Optional.ofNullable(routeInfoDTO.getDistance()).map(Double::longValue).orElse(0L));
						//填充考核时长
						assessTimeAppender.appendAssesDeliveryDuration(orderInfo, deliveryOrder, new Result<>(Optional.ofNullable(routeInfoDTO.getDistance()).orElse(0d)));
					} else {
						routeInfoDTO = routeInfoDTOOpt.get();
						deliveryOrder.setDistance(Optional.ofNullable(routeInfoDTO.getDistance()).map(Double::longValue).orElse(0L));
						//填充考核时长
						assessTimeAppender.appendAssesDeliveryDuration(deliveryDimensionPoi, orderInfo, deliveryOrder, new Result<>(Optional.ofNullable(routeInfoDTO.getDistance()).orElse(0d)));
					}
				}
			}

			deliveryOrder.activate();
			deliveryOrderRepository.save(deliveryOrder);
			deliveryMarkSquirrelOperationService.saveDeliveryMark(deliveryOrder);
			savePricingRouteInfo(deliveryOrder, routeInfoDTO);

			//自行配送
			Optional<Failure> result = doLaunchDelivery(deliveryPoi, orderInfo, deliveryOrder, acceptRetry);
			Cat.logEvent(DELIVERY, DELIVERY_ORDER);
			return result;
		} catch (Exception e) {
			Cat.logEvent(DELIVERY, DELIVERY_ORDER,"1","");
			throw e;
		}
	}

    @CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<LaunchFailure> manualLaunchDelivery(DeliveryPoi deliveryPoi, OrderInfo orderInfo, Long operatorId, String appId) {

		//查当前是否有激活的运单，尝试幂等处理
		Optional<DeliveryOrder> activeDeliveryOrder = Optional.empty();
		if(MccConfigUtils.getDeliveryQueryTenantSwitch(deliveryPoi.getTenantId())){
			activeDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderWithTenant(orderInfo.getOrderKey().getOrderId(),deliveryPoi.getTenantId(),deliveryPoi.getStoreId());
		}else {
			activeDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(orderInfo.getOrderKey().getOrderId());
		}

		DynamicOrderBizType orderBizType = DynamicOrderBizType.findOf(orderInfo.getOrderBizType());
		if (DynamicOrderBizType.DOU_YIN.equals(orderBizType)) {
			return handleDouYinManualLaunch(deliveryPoi, orderInfo, activeDeliveryOrder, operatorId, appId);
		} else if (orderBizType!=null && Objects.equals(orderBizType.getChannelStandard(), PRIVATE_DOMAIN_CHANNEL)) {
			// 查最近运单
			Optional<DeliveryOrder> deliveryOrderLatest = deliveryOrderRepository.getLatestDeliveryOrderForceMaster(orderInfo.getOrderKey().getOrderId());

			// 获取运单
			// 如果存在生效运单，则直接返回
			// 如果不存在生效运单，则取最近的一条运单(可能为取消状态)，且满足私域渠道且平台配送，则返回，否则返回空
			Optional<DeliveryOrder> deliveryOrder = Optional.empty();
			if (activeDeliveryOrder.isPresent()) {
				deliveryOrder = activeDeliveryOrder;
			} else if (deliveryOrderLatest.isPresent()) {
				DeliveryOrder latest = deliveryOrderLatest.get();
				DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(latest.getOrderBizType());
				DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(latest.getDeliveryChannel());

				if (channelType != null
						&& Objects.equals(channelType.getChannelStandard(), PRIVATE_DOMAIN_CHANNEL)
						&& deliveryChannel.getDeliveryPlatFormCode().equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode())) {
					log.info("manualLaunchDelivery 获取最近私域渠道配送运单, deliveryOrderLatest={}", latest);
					deliveryOrder = deliveryOrderLatest;
				}
			}
			return handleOtherManualLaunch(deliveryPoi, orderInfo, deliveryOrder, operatorId, appId);
		} else {
			return Optional.of(new LaunchFailure(false, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_FAILED));
		}
	}

	private Optional<LaunchFailure> handleDouYinManualLaunch(DeliveryPoi deliveryPoi, OrderInfo orderInfo, Optional<DeliveryOrder> activeDeliveryOrder, Long operatorId, String appId) {
		if (activeDeliveryOrder.isPresent()) {
			log.error("订单[{}]发起配送幂等成功，已有运单：{}", orderInfo.getOrderKey(), activeDeliveryOrder);
			return Optional.of(new LaunchFailure(false, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_DELIVERY_ORDER_ALREADY_EXISTS));
		}
		// 仅对于非牵牛花管理配送或者抖音平台配送放开
		DeliveryPlatformEnum deliveryPlatformEnum = deliveryPoi.getDeliveryPlatform();
		if (deliveryPlatformEnum != DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM  && deliveryPlatformEnum != DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM) {
			sendOrderTrack(orderInfo.getOrderKey().getTenantId(), orderInfo.getChannelOrderId(), orderInfo.getOrderBizType(), operatorId, false, appId);
			return Optional.of(new LaunchFailure(false, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_FAILED));
		}
		try {
			Integer dyDeliveryChannelId = MccConfigUtils.getDyPlatformDeliveryChannelCode();
			DeliveryOrder deliveryOrder = new DeliveryOrder(orderInfo, dyDeliveryChannelId);
			deliveryOrder.activate();
			deliveryOrderRepository.save(deliveryOrder);

			Optional<LaunchFailure> result = doManualLaunchDelivery(deliveryPoi, orderInfo, deliveryOrder);
			Cat.logEvent(DELIVERY, DELIVERY_ORDER);
			return result;
		} catch (Exception e) {
			Cat.logEvent(DELIVERY, DELIVERY_ORDER, "1", "");
			throw e;
		}
	}
	private Optional<LaunchFailure> handleOtherManualLaunch(DeliveryPoi deliveryPoi, OrderInfo orderInfo, Optional<DeliveryOrder> activeDeliveryOrder, Long operatorId, String appId) {
		try {
			if (deliveryPoi.getDeliveryPlatform() != DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM ) {
				sendOrderTrack(orderInfo.getOrderKey().getTenantId(), orderInfo.getChannelOrderId(), orderInfo.getOrderBizType(), operatorId, false, appId);
				return Optional.of(new LaunchFailure(false, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_FAILED));
			}
			if (activeDeliveryOrder.isPresent()) {
				DeliveryOrder deliveryOrder = activeDeliveryOrder.get();
				if (deliveryOrder.getDeliveryExceptionCode().equals(DeliveryExceptionCodeEnum.OPEN_API_DELIVERY_EXCEPTION.getCode())) {
					Optional<LaunchFailure> result = doManualLaunchDelivery(deliveryPoi, orderInfo, deliveryOrder);
					Cat.logEvent(DELIVERY, DELIVERY_ORDER);
					return result;
				}
			}
			return Optional.of(new LaunchFailure(false, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_DELIVERY_CHANNEL_NOT_SUPPORT));
		} catch (Exception e) {
			Cat.logEvent(DELIVERY, DELIVERY_ORDER, "1", "");
			throw e;
		}
	}

	private void sendOrderTrack(Long tenantId, String channelOrderId, Integer orderBizType, Long operatorId, boolean isSuccess, String appId) {
		OrderTrackEvent event = new OrderTrackEvent();
		event.setTrackSource(TrackSource.DELIVERY.getType());
		event.setTrackOpType(isSuccess ? TrackOpType.MANUAL_LAUNCH_DELIVERY_SUCCESS.getOpType() : TrackOpType.MANUAL_LAUNCH_DELIVERY_FAIL.getOpType());
		event.setAccountIdList(Collections.singletonList(operatorId));
		event.setOperateTime(System.currentTimeMillis());
		event.setTenantId(tenantId);
		event.setUnifyOrderId(channelOrderId);
		event.setOrderBizType(orderBizType);
		event.setAppId(appId);

		deliveryNotifyService.notifyDeliveryTrace(event);
	}


	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> launchDelivery(DeliveryPoi deliveryPoi,
	                                        OrderInfo orderInfo,
	                                        Supplier<DeliveryOrder> deliveryOrderCreateFunction,
	                                        boolean acceptRetry) {
		//查当前激活的运单
		Optional<DeliveryOrder> activeDeliveryOrder = Optional.empty();
		if(MccConfigUtils.getDeliveryQueryTenantSwitch(deliveryPoi.getTenantId())){
			activeDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderWithTenant(orderInfo.getOrderKey().getOrderId(),deliveryPoi.getTenantId(),deliveryPoi.getStoreId());
		}else {
			activeDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(orderInfo.getOrderKey().getOrderId());
		}

		if (activeDeliveryOrder.isPresent()) {
			return handleExistingDeliveryOrder(deliveryPoi, orderInfo, activeDeliveryOrder.get(), acceptRetry);
		}

		try {
           //先创建一个激活的运单，占位
			DeliveryOrder deliveryOrder = deliveryOrderCreateFunction.get();
			deliveryOrder.activate();
			deliveryOrderRepository.save(deliveryOrder);
			deliveryMarkSquirrelOperationService.saveDeliveryMark(deliveryOrder);

			//执行发起配送
			Optional<Failure> result = doLaunchDelivery(deliveryPoi, orderInfo, deliveryOrder, acceptRetry);
			Cat.logEvent(DELIVERY, DELIVERY_ORDER);
			return result;
		} catch (Exception e) {
			Cat.logEvent(DELIVERY, DELIVERY_ORDER,"1","");
			throw e;
		}
	}

	private Optional<Failure> handleExistingDeliveryOrder(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder activeDeliveryOrder, boolean acceptRetry) {
		if (activeDeliveryOrder.getStatus() == DeliveryStatusEnum.INIT) {
			//如果当前运单处于初始状态，可能前一个执行超时了或者并发了，补一下该运单的发起动作，并返回并发失败，等待重试，然后幂等
			return doLaunchDelivery(deliveryPoi, orderInfo, activeDeliveryOrder, acceptRetry);

		} else {
			//如果是其他状态，幂等成功
			log.warn("订单[{}]发起配送幂等成功，已有运单：{}", orderInfo.getOrderKey(), activeDeliveryOrder);
			return Optional.empty();
		}
	}

	/**
	 * 执行发起配送
	 */
	private Optional<Failure> doLaunchDelivery(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder, boolean acceptRetry) {
		TransferOrderMarkEnum transferOrderMarkEnum = TransferOrderMarkEnum.NORMAL_ORDER;
		if(orderInfo.getOrderTransInfo()!=null && orderInfo.getOrderTransInfo().getDispatchShopId()!=null){
			transferOrderMarkEnum = TransferOrderMarkEnum.TRANSFER_ORDER;
		}
		Optional<LaunchFailure> launchFailure = deliveryPlatformClient.launch(deliveryPoi, orderInfo, deliveryOrder,transferOrderMarkEnum.getCode());
		//监控配送超时
		deliveryTimeOutCheckService.triggerDeliveryTimeOutCheck(deliveryOrder);

		if (launchFailure.isPresent()) {
			String failReason = launchFailure.get().getFailureMessage();
			deliveryWarnEventPublisher.postEvent(new DeliveryLaunchFailedEvent(deliveryOrder, failReason));

			if (launchFailure.get().isNeedRetry() && acceptRetry) {
				//配送发起失败，需要重试
				return Optional.of(new Failure(true, FailureCodeEnum.LAUNCH_DELIVERY_FAILED, failReason));

			} else {
				//配送发起失败，不支持重试，直接推到终态
				deliveryOrder.onChange(DeliveryEventEnum.DELIVERY_REJECT, launchFailure.get().getExceptionInfo(), null, LocalDateTime.now());
				pushClient.pushDeliveryException(deliveryOrder);
				return Optional.of(new Failure(false, FailureCodeEnum.LAUNCH_DELIVERY_FAILED, failReason));
			}
		}

		return Optional.empty();
	}

	/**
	 * 发起手动配送
	 */
	private Optional<LaunchFailure> doManualLaunchDelivery(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder) {
		Optional<LaunchFailure> launchFailure = deliveryPlatformClient.manualLaunchDelivery(deliveryPoi, orderInfo);
		//监控配送超时
		deliveryTimeOutCheckService.triggerDeliveryTimeOutCheck(deliveryOrder);

		if (launchFailure.isPresent()) {
			String failReason = launchFailure.get().getFailureMessage();
			deliveryWarnEventPublisher.postEvent(new DeliveryLaunchFailedEvent(deliveryOrder, failReason));
			if (DynamicOrderBizType.DOU_YIN.getValue() == orderInfo.getOrderBizType()) {
				deliveryOrder.onChange(DeliveryEventEnum.DELIVERY_REJECT, launchFailure.get().getExceptionInfo(), null, LocalDateTime.now());
				pushClient.pushDeliveryException(deliveryOrder);
			}
			return launchFailure;
		}

		return Optional.empty();
	}

	private boolean isMerchantSelfDelivery(DeliveryOrder deliveryOrder) {
		DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
		if (Objects.isNull(deliveryChannelDto) || Objects.isNull(deliveryChannelDto.getDeliveryPlatFormCode())) {
			return false;
		}

		return DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode() == deliveryChannelDto.getDeliveryPlatFormCode();
	}

	private PricingRouteInfoDO buildPricingRouteInfoDO(DeliveryOrder deliveryOrder, RouteInfoDTO routeInfoDTO) {
		PricingRouteInfoDO pricingRouteInfoDO = new PricingRouteInfoDO();
		pricingRouteInfoDO.setDistance(routeInfoDTO.getDistance().longValue());
		pricingRouteInfoDO.setRouteId(routeInfoDTO.getRouteId());
		pricingRouteInfoDO.setPolyline(routeInfoDTO.getPolyline());
		pricingRouteInfoDO.setTenantId(deliveryOrder.getTenantId());
		pricingRouteInfoDO.setStoreId(deliveryOrder.getStoreId());
		pricingRouteInfoDO.setDeliveryOrderId(deliveryOrder.getId());
		pricingRouteInfoDO.setOrigin(Optional.ofNullable(routeInfoDTO.getOrigin())
				.map(origin -> routeInfoDTO.getOrigin().getLongitude() + "," + routeInfoDTO.getOrigin().getLatitude())
				.orElse(null));
		pricingRouteInfoDO.setDestination(Optional.ofNullable(routeInfoDTO.getDestination())
				.map(origin -> routeInfoDTO.getDestination().getLongitude() + "," + routeInfoDTO.getDestination().getLatitude())
				.orElse(null));
		pricingRouteInfoDO.setDuration(Optional.ofNullable(routeInfoDTO.getDuration()).map(Double::longValue).orElse(null));

		return pricingRouteInfoDO;
	}

	private void savePricingRouteInfo(DeliveryOrder deliveryOrder, RouteInfoDTO routeInfoDTO) {
		try {
			if ( Objects.nonNull(routeInfoDTO)
					&& MccConfigUtils.isQueryRouteGrayStore(deliveryOrder.getStoreId())) {
				pricingRouteInfoRepository.save(buildPricingRouteInfoDO(deliveryOrder, routeInfoDTO));
			}
		} catch (Exception e) {
			log.error("保存定价路线信息失败", e);
			Cat.logEvent("NAVIGATE", "PERSIST_ROUTE_INFO_ERROR");
		}
	}
}
