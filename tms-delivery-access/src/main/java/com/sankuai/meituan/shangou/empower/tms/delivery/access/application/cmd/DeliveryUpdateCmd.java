package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 更新配送信息命令
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/7
 */
@Getter
@ToString
public class DeliveryUpdateCmd {

	private final Long deliveryId;

	private final DeliveryEventEnum event;

	private final Rider rider;

	private final DeliveryExceptionInfo exceptionInfo;

	private final LocalDateTime updateTime;

	public DeliveryUpdateCmd(Long deliveryId,
	                         DeliveryEventEnum event,
	                         Rider rider,
	                         DeliveryExceptionInfo exceptionInfo,
	                         LocalDateTime updateTime) {
		Preconditions.checkNotNull(deliveryId, "deliveryId is null");
		Preconditions.checkNotNull(event, "event is null");
		Preconditions.checkNotNull(updateTime, "updateTime is null");

		this.deliveryId = deliveryId;
		this.event = event;
		this.rider = rider;
		this.exceptionInfo = exceptionInfo;
		this.updateTime = updateTime;
	}
}
