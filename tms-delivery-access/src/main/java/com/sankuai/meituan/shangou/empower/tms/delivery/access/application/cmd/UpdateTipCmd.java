package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import lombok.Getter;
import lombok.ToString;

/**
 * UpdateTipCmd
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
@Getter
@ToString(callSuper = true)
public class UpdateTipCmd extends BaseOrderPlatformDeliveryOperateCmd {

    /**
     * 小费金额
     */
    private final Integer tipAmount;

    public UpdateTipCmd(Long tenantId, Long offlineStoreId, OrderInfo orderInfo,
            Integer channelId, Operator operator, Integer tipAmount) {
        super(tenantId, offlineStoreId, orderInfo, channelId, operator);
        this.tipAmount = tipAmount;
    }

}
