package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.DeliveryChangeCallbackCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.DeliveryWarnEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;

import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/8
 */
public class DeliveryChangeCallbackHandleFailedEvent extends DeliveryWarnEvent {

	private final String warnMessage;

	public DeliveryChangeCallbackHandleFailedEvent(DeliveryChangeCallbackCmd cmd, String failReason) {
		this.warnMessage = String.format(
				"配送变更回调消费失败\n[赋能订单号:%d]\n[配送事件:%s]\n[消费失败原因:%s]",
				Optional.ofNullable(cmd).map(DeliveryChangeCallbackCmd::getOrderId).orElse(-1L),
				Optional.ofNullable(cmd).map(DeliveryChangeCallbackCmd::getDeliveryEvent).map(DeliveryEventEnum::name).orElse(""),
				failReason
		);
	}

	@Override
	public String getWarnMessage() {
		return warnMessage;
	}
}
