package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.sankuai.meituan.shangou.dms.base.model.DeliveryOrderBase;
import com.sankuai.meituan.shangou.dms.base.model.value.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OFCSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryChangeInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryPassiveFinishChangeInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryRiderChangeInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryMonitorDomainService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TmsMccUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.ChannelStoreQueryResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantChannelStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.SpringContextUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryExtInfo;
import lombok.*;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 运单领域模型
 * 维护了一次配送运单的基本信息，包括收货人信息，骑手信息，配送进度信息等
 * 提供包括更新，取消等运单基本能力
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
@Slf4j
@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
public class DeliveryOrder extends DeliveryOrderBase {
	public static final long DELIVERY_ORDER_ACTIVE = 0L;
	private static final int YOU_ZAN_CHANNEL_ID = 500;

	private static final String PAOTUI = "PAOTUI";




	/**
	 * 渠道运单id
	 */
	@Setter
	private String channelDeliveryId;

	/**
	 * 异常码
	 */
	private Integer deliveryExceptionCode;

	/**
	 * 渠道服务包码
	 */
	@Setter
	private String channelServicePackageCode;

	/**
	 * 运单当前异常类型
	 */
	private DeliveryExceptionTypeEnum exceptionType;

	/**
	 * 运单当前异常描述
	 */
	@Setter
	private String exceptionDescription;

	/**
	 * 骑手信息
	 */
	@Setter
	private Rider riderInfo;

	/**
	 * 运单生效状态
	 * 0：该运单生效中，即是真正执行配送的运单
	 * 非0毫秒时间戳：该运单未生效，即非最终配送运单
	 */
	private Long activeStatus;


	/**
	 * 配送费用，单位：元
	 */
	@Setter
	private BigDecimal deliveryFee;

	/**
	 * 更新时间
	 */
	private final LocalDateTime updateTime;

	/**
	 * 取消配送中的标识
	 */
	@Setter
	private Integer cancelMark;

	/**
	 * 配送小费 元
	 */
	@Setter
	private BigDecimal tipAmount;

	/**
	 * 第N次配送
	 */
	@Setter
	private Integer deliveryCount;

	/**
	 * 平台配送费（目前只记录美团跑腿） 元
	 */
	@Setter
	private BigDecimal platformFee;

	/**
	 * 	实际支付金额，单位分
	 */
	private Integer actualPayAmt;
	/**
	 * 是否企客转聚合配送
	 */
	private boolean businessToAggregationDelivery = false;

	/**
	 * 运单最近一次异常变更事件发生时间
	 */
	private LocalDateTime lastExceptionEventTime;

	@Setter
	private Integer transType;
	@Setter
	private String failType;
	@Setter
	private String dealDeadline;

	private Long userId;

	private List<RiderDeliveryExtInfo.DeliveryProofPhotoInfo> deliveryProofPhotoInfoList;

	private Boolean isAlreadyAudit = false;

	private Integer orderGoodsCount;

	private String signPosition;

	@Setter
	private Integer signType;

	private Boolean isWeakNetwork;

	private Integer proofPhotoCount;

	@Setter
	private String baseFee;
	@Setter
	private String discountFee;
	@Setter
	private String insuredFee;
	@Setter
	private Integer isQhnManagement;

	/**
	 * 配送取消原因
	 */
	@Setter
	private Integer deliveryCancelCode;

	@Setter
	private Boolean isOneYuanOrder;

	/**
	 * 是否需要重新呼叫渠道平台配送，1代表需要
	 */
	@Setter
	private Integer isRecallDelivery;

	/**
	 * 考核配送截止时间
	 */
	@Setter
	private Long assessDeliveryTime;

	/**
	 * 承运商原始运单id
	 */
	@Setter
	private String originWaybillNo;

	/**
	 * 拣配分离标记
	 */
	@Setter
	private Boolean pickDeliverySplitTag;

	/**
	 * 是否四轮配送
	 *
	 * 1-否
	 *
	 * 2-是
	 */
	@Setter
	private Integer isFourWheelDelivery;

	/**
	 * 是否手动发四轮
	 * 1-手动发四轮
	 *
	 * 2-自动发四轮
	 */
	@Setter
	private Integer isManual;

	public DeliveryOrder(OrderInfo orderInfo,
						 Integer deliveryChannel,
						 String channelServicePackageCode) {
		this.tenantId = orderInfo.getOrderKey().getTenantId();
		this.storeId = orderInfo.getOrderKey().getStoreId();

		this.customerOrderKey = new CustomerOrderKey(orderInfo.getOrderKey().getOrderId()
				,orderInfo.getChannelOrderId()
				,orderInfo.getOrderBizType()
				,orderInfo.getDaySeq().intValue()
				,orderInfo.isBookingOrder()
				,orderInfo.getOrderKey().getOrderId()
				,orderInfo.getOrderSource()
				,orderInfo.getDaySeqNum(),PlatformSourceEnum.OMS);

		this.timeline = new DeliveryTimeline(TimeUtil.getEpochTime()
				,orderInfo.getEstimatedDeliveryTime()
				,orderInfo.getEstimatedDeliveryEndTime(),TimeUtil.getEpochTime(),LocalDateTime.now());;


		this.receiver = getReceiver(orderInfo);
		this.setDeliveryChannel(deliveryChannel);
		this.channelServicePackageCode = channelServicePackageCode;
		this.status = DeliveryStatusEnum.INIT;
		this.activeStatus = System.currentTimeMillis();
		this.exceptionType = DeliveryExceptionTypeEnum.NO_EXCEPTION;
		this.exceptionDescription = StringUtils.EMPTY;
		this.deliveryFee = BigDecimal.ZERO;
		this.setDistance(0L);
		this.updateTime = LocalDateTime.now();
		this.cancelMark = 0;
		this.actualPayAmt = orderInfo.getActualPayAmt();
		this.userId = orderInfo.getUserId();
		this.orderGoodsCount = 0;
		if (CollectionUtils.isNotEmpty(orderInfo.getGoodsList())) {
			orderInfo.getGoodsList().forEach(goodsInfo -> this.orderGoodsCount += goodsInfo.getQuantity());
		}
		this.signPosition = orderInfo.getSignPosition();
		this.isOneYuanOrder = orderInfo.getIsOneYuanOrder();
	}

	public DeliveryOrder(OrderInfo orderInfo, Integer deliveryChannel) {
		this(orderInfo, deliveryChannel, "");
	}

	public static DeliveryOrder fromBusinessCustomer(OrderInfo orderInfo, Integer deliveryChannel,
			String channelServicePackageCode) {
		DeliveryOrder deliveryOrder = new DeliveryOrder(orderInfo, deliveryChannel, channelServicePackageCode);
		deliveryOrder.businessToAggregationDelivery = true;
		deliveryOrder.activate();
		return deliveryOrder;
	}

	public static DeliveryOrder fromPaoTuiCustomer(OrderInfo orderInfo, Integer deliveryChannel,
													 String channelServicePackageCode) {
		DeliveryOrder deliveryOrder = new DeliveryOrder(orderInfo, deliveryChannel, channelServicePackageCode);
		deliveryOrder.activate();
		return deliveryOrder;
	}

	private Receiver getReceiver(OrderInfo orderInfo) {
		try {
			if (orderInfo.isStoreDelivery() && TmsMccUtils.isLaunchStoreDeliveryTenant(orderInfo.getOrderKey().getTenantId())) {
				TenantSystemClient tenantSystemClient = SpringContextUtils.getBean(TenantSystemClient.class);
				ChannelStoreQueryResult channelStoreQueryResult = tenantSystemClient.queryChannelStoreDetailInfo(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId());
				Optional<TenantChannelStoreInfo> yzChannelStore = Optional
						.ofNullable(channelStoreQueryResult.getStoreInfos())
						.orElse(Lists.newArrayList())
						.stream()
						.filter(tenantChannelStoreInfo -> tenantChannelStoreInfo.getChannelId().equals(YOU_ZAN_CHANNEL_ID))
						.findFirst();
				if (!channelStoreQueryResult.isSuccess() || !yzChannelStore.isPresent()) {
					return orderInfo.getReceiver();
				}
				return new Receiver(
						orderInfo.getReceiver().getReceiverName(),
						orderInfo.getReceiver().getReceiverPhone(),
						orderInfo.getReceiver().getReceiverPrivacyPhone(),
						new Address(
								orderInfo.getReceiver().getReceiverAddress().getAddressDetail(),
								CoordinateTypeEnum.MARS,
								new CoordinatePoint(
										yzChannelStore.get().getLongitude(),
										yzChannelStore.get().getLatitude()
								)
						));
			}
		} catch (Exception e) {
			log.error("invoke getReceiver error", e);
		}

		return orderInfo.getReceiver();
	}

	public static DeliveryOrder createMerchantDeliveryOrder(OrderInfo orderInfo) {
		DeliveryOrder deliveryOrder = new DeliveryOrder(orderInfo, DeliveryChannelEnum.MERCHANT_DELIVERY.getCode(), StringUtils.EMPTY);
		deliveryOrder.activate();
		deliveryOrder.status = DeliveryStatusEnum.MERCHANT_DELIVERING;
		deliveryOrder.getTimeline().setLastEventTime(LocalDateTime.now());

		return deliveryOrder;
	}

	public static DeliveryOrder createMerchantDeliveryOrder(OrderInfo orderInfo, DeliveryStatusEnum status) {
		DeliveryOrder deliveryOrder = new DeliveryOrder(orderInfo, DeliveryChannelEnum.MERCHANT_DELIVERY.getCode(), StringUtils.EMPTY);
		deliveryOrder.activate();
		deliveryOrder.status = status;
		deliveryOrder.getTimeline().setLastEventTime(LocalDateTime.now());

		return deliveryOrder;
	}

	public Long getOrderId() {
		return this.getCustomerOrderKey().getOrderId();
	}

	public void activate() {
		this.activeStatus = DELIVERY_ORDER_ACTIVE;
	}

	public void deactivate() {
		this.activeStatus = System.currentTimeMillis();
	}

	public void activateSucCount(){
		if(deliveryCount!=null){
			this.activeStatus=deliveryCount.longValue();
		}
	}

	public boolean isActive() {
		return this.activeStatus == DELIVERY_ORDER_ACTIVE;
	}

	/**
	 * 取消运单
	 *
	 * @return 取消执行结果
	 */
	public Optional<Failure> cancel() {
		//已到终态，放弃取消
		if (this.status.isFinalStatus()) {
			log.warn("DeliveryOrder[{}] is in final status, will ignore this cancel command", this.id);
			return Optional.empty();
		}
        return doCancel();
	}

    /**
     * 取消订单流程不过滤终态订单
     *
     * @return 取消执行结果
     */
    public Optional<Failure> cancelWithoutFilter() {
        return doCancel();
    }

    /**
     * 取消运单具体流程
     *
     * @return 取消执行结果
     */
    public Optional<Failure> doCancel() {
        DeliveryChannelApplicationService deliveryChannelApplicationService = SpringContextUtils.getBean(DeliveryChannelApplicationService.class);
        DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(this.getDeliveryChannel());
        if (Objects.isNull(deliveryChannelDto) || Objects.isNull(deliveryChannelDto.getDeliveryPlatFormCode())) {
            log.error("deliveryOrder cancel error, deliveryPlatFormCode is null");
            return Optional.of(new Failure(false, FailureCodeEnum.CANCEL_DELIVERY_FAILED.getCode(), FailureCodeEnum.CANCEL_DELIVERY_FAILED.getMessage()));
        }
        DeliveryPlatformEnum deliveryPlatform = DeliveryPlatformEnum.enumOf(deliveryChannelDto.getDeliveryPlatFormCode());
        if (isTmsCancelCondition(deliveryPlatform)) {
            // 平台配送，直接取消
            onChange(DeliveryEventEnum.DELIVERY_CANCEL, DeliveryExceptionInfo.NO_EXCEPTION, getRiderInfo(), LocalDateTime.now());
            return Optional.empty();
        }

        return SpringContextUtils.getBean(DeliveryPlatformClient.class).cancelDelivery(this);
    }

    /**
	 * 转自配送后取消原有的运单
	 *
	 * @return 取消执行结果
	 */
	public Optional<Failure> cancelForTransOrder() {
		//已到终态，放弃取消
		if (this.status.isFinalStatus()) {
			log.warn("DeliveryOrder[{}] is in final status, will ignore this cancel command", this.id);
			return Optional.empty();
		}

		DeliveryChannelApplicationService deliveryChannelApplicationService = SpringContextUtils.getBean(DeliveryChannelApplicationService.class);
		DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(this.getDeliveryChannel());
		if (Objects.isNull(deliveryChannelDto) || Objects.isNull(deliveryChannelDto.getDeliveryPlatFormCode())) {
			log.error("deliveryOrder cancelForTransOrder error, deliveryPlatFormCode is null");
			return Optional.empty();
		}

		DeliveryPlatformEnum deliveryPlatform = DeliveryPlatformEnum.enumOf(deliveryChannelDto.getDeliveryPlatFormCode());
		if (deliveryPlatform == null || deliveryPlatform == DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM) {
			// 平台配送，直接取消
			onChange(DeliveryEventEnum.DELIVERY_CANCEL, DeliveryExceptionInfo.NO_EXCEPTION, getRiderInfo(), LocalDateTime.now());
			return Optional.empty();
		}

		return SpringContextUtils.getBean(DeliveryPlatformClient.class).cancelDeliveryForTransOrder(this);
	}

	/**
	 * 转自配送后取消原有的运单
	 *
	 * @return 取消执行结果
	 */
	public Optional<Failure> cancelDeliveryForOFC(boolean sendToOFC) {
		//已到终态，放弃取消
		LocalDateTime changeTime = LocalDateTime.now();
		if (this.status.isFinalStatus()) {
			log.warn("DeliveryOrder[{}] is in final status, will ignore this cancel command", this.id);
			if(sendToOFC){
				OFCSystemClient ofcSystemClient = SpringContextUtils.getBean(OFCSystemClient.class);
				ofcSystemClient.syncDeliveryChangeToSystem(this, changeTime);
			}
			return Optional.empty();
		}

		if(Arrays.asList(DeliveryStatusEnum.DELIVERY_LAUNCHED.getCode(),DeliveryStatusEnum.INIT.getCode()).contains(this.getStatus().getCode())){
			if(MccConfigUtils.transDeliveryCancelSwitch()){
				Optional<Failure> optional= SpringContextUtils.getBean(DeliveryPlatformClient.class).cancelDeliveryForOFC(this);
				if(optional.isPresent()){
					return optional;
				}
			}
			onChange(DeliveryEventEnum.DELIVERY_CANCEL, DeliveryExceptionInfo.NO_EXCEPTION, getRiderInfo(), LocalDateTime.now());
			return Optional.empty();
		}

		DeliveryChannelApplicationService deliveryChannelApplicationService = SpringContextUtils.getBean(DeliveryChannelApplicationService.class);
		DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(this.getDeliveryChannel());
		if (Objects.isNull(deliveryChannelDto) || Objects.isNull(deliveryChannelDto.getDeliveryPlatFormCode())) {
			log.error("deliveryOrder cancelForTransOrder error, deliveryPlatFormCode is null");
			if(sendToOFC){
				OFCSystemClient ofcSystemClient = SpringContextUtils.getBean(OFCSystemClient.class);
				ofcSystemClient.syncDeliveryChangeToSystem(this, changeTime);
			}
			return Optional.empty();
		}

		DeliveryPlatformEnum deliveryPlatform = DeliveryPlatformEnum.enumOf(deliveryChannelDto.getDeliveryPlatFormCode());
		if (deliveryPlatform == null) {
			// 未知配送，直接取消
			onChange(DeliveryEventEnum.DELIVERY_CANCEL, DeliveryExceptionInfo.NO_EXCEPTION, getRiderInfo(), LocalDateTime.now());
			return Optional.empty();
		}

		Optional<Failure> optional= SpringContextUtils.getBean(DeliveryPlatformClient.class).cancelDeliveryForOFC(this);
//		if(deliveryPlatform == DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM && !optional.isPresent()){
//			onChange(DeliveryEventEnum.DELIVERY_CANCEL, DeliveryExceptionInfo.NO_EXCEPTION, getRiderInfo(), LocalDateTime.now());
//		}
		return optional;
	}

	public void onChange(DeliveryEventEnum event, DeliveryExceptionInfo exceptionInfo, Rider rider, LocalDateTime changeTime) {
		//记录流水
		recordStatusChangeLog(event, changeTime);
		recordExceptionLog(exceptionInfo, changeTime);
		recordRiderChangeLog(rider, changeTime);

		if (needDoChange(changeTime)) {
			//更新运单
			doStatusChange(event.getTargetStatus(), changeTime);
			doExceptionChange(exceptionInfo, changeTime);
			doRiderChange(rider);

			//更新事件时间
			this.getTimeline().setLastEventTime(changeTime);

			//持久化
			SpringContextUtils.getBean(DeliveryOrderRepository.class).save(this);

			//对订单同步运单变更
			OrderSystemClient orderSystemClient = SpringContextUtils.getBean(OrderSystemClient.class);
			orderSystemClient.syncDeliveryChangeToOrderSystem(this, changeTime);

			//对OFC同步运单变更
			OFCSystemClient ofcSystemClient = SpringContextUtils.getBean(OFCSystemClient.class);
			ofcSystemClient.syncDeliveryChangeToSystem(this, changeTime);

			//触发监控
			DeliveryMonitorDomainService deliveryMonitorDomainService = SpringContextUtils.getBean(DeliveryMonitorDomainService.class);
			deliveryMonitorDomainService.triggerDeliveryMonitoring(this);
		}
	}

	/**
	 * 如果本次事件变更事件的秒级时间戳等于或晚于运单最后一次事件时间的秒级时间戳，需要消费变更
	 * 因为外部时间戳有些地方是毫秒时间戳，有些地方是秒级时间戳，所以不能直接判断变更时间是否晚于运单最后一次事件时间，统一用秒级判断
	 */
	public boolean needDoChange(LocalDateTime thisChangeTime) {
		Integer thisChangeTimestamp = TimeUtil.toSeconds(thisChangeTime).orElse(0);
		Integer lastEventTimestamp = TimeUtil.toSeconds(this.getTimeline().getLastEventTime()).orElse(0);

		return thisChangeTimestamp >= lastEventTimestamp;
	}

	private void recordStatusChangeLog(DeliveryEventEnum event, LocalDateTime changeTime) {
		if (event.getTargetStatus() != null) {
			DeliveryChangeInfo deliveryChangeInfo = null;

			if (event == DeliveryEventEnum.FINISH_BY_ORDER_DONE) {
				deliveryChangeInfo = new DeliveryPassiveFinishChangeInfo(true);
			}
			recordDeliveryChangeLog(this.id, event, deliveryChangeInfo, changeTime);
		}
	}

	private void recordExceptionLog(DeliveryExceptionInfo exceptionInfo, LocalDateTime changeTime) {
		if (exceptionInfo.getExceptionType() != DeliveryExceptionTypeEnum.NO_EXCEPTION) {
			recordDeliveryChangeLog(this.id, DeliveryEventEnum.DELIVERY_EXCEPTION, exceptionInfo, changeTime);
		}
	}

	private void recordRiderChangeLog(Rider rider, LocalDateTime changeTime) {
		if (!Objects.equals(this.riderInfo, rider)) {
			recordDeliveryChangeLog(this.id, DeliveryEventEnum.RIDER_CHANGE, new DeliveryRiderChangeInfo(this.riderInfo, rider), changeTime);
		}
	}

	/**
	 * 消费变更事件
	 */
	public void onEvent(DeliveryEventEnum event, LocalDateTime changeTime) {
		if (event.getTargetStatus() != null) {
			doStatusChange(event.getTargetStatus(), changeTime);
			this.getTimeline().setLastEventTime(changeTime);

			recordDeliveryChangeLog(this.id, event, null, changeTime);
		}
	}

	/**
	 * 消费状态变更，不走状态机
	 * 主要针对平台配送等配送进度不由我们控制等场景
	 */
	public void onStatusChange(DeliveryStatusEnum targetStatus, LocalDateTime changeTime) {
		doStatusChange(targetStatus, changeTime);
		this.getTimeline().setLastEventTime(changeTime);

		recordDeliveryChangeLog(this.id, DeliveryEventEnum.getEventByStatus(targetStatus), null, changeTime);
	}

	/**
	 * 消费状态变更，不走状态机,不发变更日志
	 * 主要针对平台配送等配送进度不由我们控制等场景
	 */
	public void onStatusChangeWithOutLog(DeliveryStatusEnum targetStatus, LocalDateTime changeTime) {
		doStatusChange(targetStatus, changeTime);
		this.getTimeline().setLastEventTime(changeTime);
	}

	private void doStatusChange(DeliveryStatusEnum targetStatus, LocalDateTime changeTime) {
		if (targetStatus == null) {
			return;
		}

		this.status = targetStatus;

		if (this.status == DeliveryStatusEnum.DELIVERY_DONE) {
			this.getTimeline().setDeliveryDoneTime(changeTime);

			//非正常终态，需要取消当前运单的激活状态，才能够发起其他渠道的配送
		} else if (this.status.isFinalStatus()) {
			this.deactivate();
		}
	}

	private void doExceptionChange(DeliveryExceptionInfo exceptionInfo) {
		this.exceptionType = exceptionInfo.getExceptionType();
		this.exceptionDescription = exceptionInfo.getExceptionDescription();
		this.deliveryExceptionCode = exceptionInfo.getExceptionCode();

		SpringContextUtils.getBean(OrderSystemClient.class).syncDeliveryExceptionToOrderSystem(this);
	}

	private void doExceptionChange(DeliveryExceptionInfo exceptionInfo, LocalDateTime changeTime) {
		doExceptionChange(exceptionInfo);
		// 异常事件时间更新
		if(exceptionInfo.getExceptionType() != null
				&& exceptionInfo.getExceptionType() != DeliveryExceptionTypeEnum.NO_EXCEPTION) {
			this.lastExceptionEventTime = changeTime;
		}
	}

	private void doRiderChange(Rider newRider) {
		if (!Objects.equals(this.riderInfo, newRider)) {
			this.riderInfo = newRider;
		}
	}

	public void onExceptionWithChangeTime(DeliveryExceptionInfo exceptionInfo, LocalDateTime changeTime) {
		onException(exceptionInfo, changeTime);
		if(changeTime != null){
			this.getTimeline().setLastEventTime(changeTime);
		}
	}
	/**
	 * 更新配送异常信息
	 *
	 * @param exceptionInfo 异常信息
	 * @param exceptionTime 异常发生时间点
	 */
	public void onException(DeliveryExceptionInfo exceptionInfo, LocalDateTime exceptionTime) {
		Preconditions.checkNotNull(exceptionInfo, "exceptionInfo is null");
		Preconditions.checkNotNull(exceptionTime, "exceptionTime is null");

		this.exceptionType = exceptionInfo.getExceptionType();
		this.deliveryExceptionCode = exceptionInfo.getExceptionCode();
		this.exceptionDescription = exceptionInfo.getExceptionDescription();

		if (this.exceptionType != DeliveryExceptionTypeEnum.NO_EXCEPTION) {
			// 更新异常事件处理时间
			this.lastExceptionEventTime = exceptionTime;
			recordDeliveryChangeLog(this.id, DeliveryEventEnum.DELIVERY_EXCEPTION, exceptionInfo, exceptionTime);
		}
		SpringContextUtils.getBean(OrderSystemClient.class).syncDeliveryExceptionToOrderSystem(this);
	}

	/**
	 * 更新骑手信息
	 *
	 * @param newRider   新骑手信息
	 * @param changeTime 变更时间点
	 * @return 可能的配送变更流水
	 */
	public void onRiderChange(Rider newRider, LocalDateTime changeTime) {
		Preconditions.checkNotNull(newRider, "newRider is null");
		Preconditions.checkNotNull(changeTime, "changeTime is null");

		if (!Objects.equals(this.riderInfo, newRider)) {
			DeliveryRiderChangeInfo changeInfo = new DeliveryRiderChangeInfo(this.riderInfo, newRider);
			recordDeliveryChangeLog(this.id, DeliveryEventEnum.RIDER_CHANGE, changeInfo, changeTime);

			this.riderInfo = newRider;
			this.getTimeline().setLastEventTime(changeTime);
		}
	}

	/**
	 * 当前配送运单是否可以转自配送
	 */
	public boolean canTurnToSelfDelivery() {
		return this.getDeliveryChannel() == DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode() && this.status.canTurnToSelfDelivery();
	}

	public void recordDeliveryChangeLog(Long deliveryId, DeliveryEventEnum deliveryEvent, DeliveryChangeInfo changeInfo, LocalDateTime changeTime) {
		if(deliveryEvent == null){
			return;
		}
		DeliveryChangeNotifyService notifyDomainService = SpringContextUtils.getBean(DeliveryChangeNotifyService.class);
		notifyDomainService.notifyDeliveryChangeLog(deliveryId, deliveryEvent, changeInfo, changeTime);
	}

	public void clearException() {
		doExceptionChange(new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.NO_EXCEPTION, StringUtils.EMPTY));
	}


	/**
	 * 获取坐标同步时间点，麦芽田从骑手接单开始同步坐标，其余渠道从骑手取货开始同步坐标
	 *
	 * @return
	 */
	public Optional<DeliveryStatusEnum> getSyncRiderPositionPoint(DeliveryChannel deliveryChannel) {
		if (Objects.isNull(deliveryChannel) || Objects.isNull(deliveryChannel.getDeliveryPlatFormCode())) {
			log.error("deliveryOrder getSyncRiderPositionPoint error, deliveryPlatFormCode is null");
			return Optional.empty();
		}

		if (deliveryChannel.getDeliveryPlatFormCode() == DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode()
				|| deliveryChannel.getDeliveryPlatFormCode() == DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode()
				|| deliveryChannel.getDeliveryPlatFormCode() == DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode()) {
			return Optional.of(DeliveryStatusEnum.RIDER_ASSIGNED);
		}
		return Optional.of(DeliveryStatusEnum.RIDER_TAKEN_GOODS);
	}

	/**
	 * 判断是否配送超时
	 */
	public boolean isDeliveryTimeout() {
		return LocalDateTime.now().isAfter(this.getTimeline().getEstimatedDeliveryEndTime());
	}

	/*
	 * 运单状态回退到待接单（转单中），本地运单重置为待接单状态
	 */
	public void reset2WaitAssignValue() {
		this.setDeliveryChannel(getInitDeliveryChannel());
		this.channelDeliveryId = StringUtils.EMPTY;
		this.channelServicePackageCode = StringUtils.EMPTY;
		this.status = DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER;
		this.riderInfo = Rider.buildEmptyRider();
		this.deliveryFee = BigDecimal.ZERO;
		this.setDistance(0L);
		this.tipAmount = BigDecimal.ZERO;
		this.platformFee = BigDecimal.ZERO;
	}

	private Integer getInitDeliveryChannel() {
		DeliveryChannelApplicationService deliveryChannelApplicationService = SpringContextUtils.getBean(DeliveryChannelApplicationService.class);
		DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(this.getDeliveryChannel());
		if (Objects.isNull(deliveryChannelDto) || Objects.isNull(deliveryChannelDto.getDeliveryPlatFormCode())) {
			return this.getDeliveryChannel();
		}

		DeliveryPlatformEnum deliveryPlatformEnum = DeliveryPlatformEnum.enumOf(deliveryChannelDto.getDeliveryPlatFormCode());

		switch (deliveryPlatformEnum) {
			case AGGREGATION_DELIVERY_PLATFORM:
				return DeliveryChannelEnum.AGGREGATION_DELIVERY.getCode();
			case MALT_FARM_DELIVERY_PLATFORM:
				return DeliveryChannelEnum.MALT_FARM.getCode();
			case DAP_DELIVERY_PLATFORM:
				return DeliveryChannelEnum.DAP_DELIVERY.getCode();
			case ORDER_CHANNEL_DELIVERY_PLATFORM:
				return DeliveryChannelEnum.ORDER_CHANNEL_DELIVERY.getCode();
			default:
				return this.getDeliveryChannel();
		}
	}

	public boolean isMaltFarmDeliveryOrder(DeliveryChannel deliveryChannel) {
		if (Objects.isNull(deliveryChannel) || Objects.isNull(deliveryChannel.getDeliveryPlatFormCode())) {
			return false;
		}

		return DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode() == deliveryChannel.getDeliveryPlatFormCode();
	}

	public boolean isPlatformDeliveryOrder(DeliveryChannel deliveryChannel) {
		if (Objects.isNull(deliveryChannel) || Objects.isNull(deliveryChannel.getDeliveryPlatFormCode())) {
			return false;
		}

		return deliveryChannel.getCarrierCode().equals(DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode());
	}

	public boolean isDapDeliveryOrder(DeliveryChannel deliveryChannel) {
		if (Objects.isNull(deliveryChannel) || Objects.isNull(deliveryChannel.getDeliveryPlatFormCode())) {
			return false;
		}

		return DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode() == deliveryChannel.getDeliveryPlatFormCode();
	}

	public boolean isOrderChannelDeliveryPlatform(DeliveryChannel deliveryChannel) {
		if (Objects.isNull(deliveryChannel) || Objects.isNull(deliveryChannel.getDeliveryPlatFormCode())) {
			return false;
		}

		return DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode() == deliveryChannel.getDeliveryPlatFormCode();
	}

	public boolean isOrderChannelDeliveryPlatFormDeliveryOrder(DeliveryChannel deliveryChannel) {
		if (Objects.isNull(deliveryChannel) || Objects.isNull(deliveryChannel.getDeliveryPlatFormCode())) {
			return false;
		}

		return DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode() == deliveryChannel.getDeliveryPlatFormCode();
	}

	public boolean isAggregationDeliveryDeliveryOrder(DeliveryChannel deliveryChannel) {
		if (Objects.isNull(deliveryChannel) || Objects.isNull(deliveryChannel.getDeliveryPlatFormCode())) {
			return false;
		}

		return DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM.getCode() == deliveryChannel.getDeliveryPlatFormCode();
	}

	public boolean isPaotuiDeliveryOrder() {
		if (Objects.isNull(this.getDeliveryChannel())) {
			log.error("isPaotuiDeliveryOrder, deliveryChannelCode is null");
			return false;
		}

		Map<String, List<Integer>> configMap = MccConfigUtils.querySpecialBusinessDeliveryChannelMap();
		if (MapUtils.isEmpty(configMap)) {
			log.error("isPaotuiDeliveryOrder, configMap is empty");
			return false;
		}

		List<Integer> paotuiDeliveryChannelList = configMap.get(PAOTUI);
		if (CollectionUtils.isEmpty(paotuiDeliveryChannelList)) {
			log.error("isPaotuiDeliveryOrder, paotuiDeliveryChannelList is empty");
			return false;
		}

		return paotuiDeliveryChannelList.contains(this.getDeliveryChannel());
	}

	public static boolean isPaotuiDeliveryOrder(Integer deliveryChannelCode) {
		if (Objects.isNull(deliveryChannelCode)) {
			log.error("isPaotuiDeliveryOrder, deliveryChannelCode is null");
			return false;
		}

		Map<String, List<Integer>> configMap = MccConfigUtils.querySpecialBusinessDeliveryChannelMap();
		if (MapUtils.isEmpty(configMap)) {
			log.error("isPaotuiDeliveryOrder, configMap is empty");
			return false;
		}

		List<Integer> paotuiDeliveryChannelList = configMap.get(PAOTUI);
		if (CollectionUtils.isEmpty(paotuiDeliveryChannelList)) {
			log.error("isPaotuiDeliveryOrder, paotuiDeliveryChannelList is empty");
			return false;
		}

		return paotuiDeliveryChannelList.contains(deliveryChannelCode);
	}

	public String getDeliveryOrderId(){
		PlatformSourceEnum platformSourceEnum = PlatformSourceEnum.OMS;
		if(this.getPlatformSourceEnum()!=null){
			platformSourceEnum = this.getPlatformSourceEnum();
		}
		String deliveryOrderId = String.valueOf(getOrderId());
		if(this.getFulfillmentOrderId()!=null && this.getFulfillmentOrderId()>0){
			deliveryOrderId = platformSourceEnum.toPrefixOrderId(String.valueOf(this.getFulfillmentOrderId()));
		}
		return deliveryOrderId;
	}

	/**
	 * 如果是tms侧直接将运单配送状态推进至「配送已取消」，无需调用平台取消配送接口的场景，返回true
	*/
	private boolean isTmsCancelCondition(DeliveryPlatformEnum deliveryPlatform) {
		// 订单侧的平台配送，例如美团、饿了么、京东平台配送
		if (Objects.isNull(deliveryPlatform)) {
			return true;
		}

		// tms侧的平台配送，当前仅对抖音渠道生效
		return DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.equals(deliveryPlatform)
				&& MccConfigUtils.isTmsCancelDeliveryChannel(this.getOrderBizType());
	}

	/**
	 * 找出最近、有效的运单
	 * 优先找出激活的运单，如果没有激活的，则按照id取最大的一个
	 */
	@Deprecated
	public static DeliveryOrder filterAvailableDeliveryOrder(List<DeliveryOrder> deliveryOrders) {
		deliveryOrders.sort(Comparator.comparingLong(deliveryOrder -> {
			if (deliveryOrder.isActive()) {
				return Long.MIN_VALUE;
			} else {
				return -deliveryOrder.getId();
			}
		}));
		return deliveryOrders.iterator().next();
	}

	/**
	 * 找出最近、有效的运单
	 * 优先找出激活的运单，如果没有激活的，则取create_time最大的一个
	 */
	public static DeliveryOrder filterActiveDeliveryOrder(List<DeliveryOrder> deliveryOrders) {
		deliveryOrders.sort(Comparator.comparingLong(deliveryOrder -> {
			if (deliveryOrder.isActive()) {
				return Long.MIN_VALUE;
			} else {
				return -TimeUtil.toMilliSeconds(deliveryOrder.getCreateTime());
			}
		}));
		return deliveryOrders.iterator().next();
	}

	public Integer getOrderBizType(){
		return this.getCustomerOrderKey().getOrderBizType();
	}

	public String getChannelOrderId(){
		return this.getCustomerOrderKey().getChannelOrderId();
	}

	public LocalDateTime getEstimatedDeliveryTime(){
		return this.getTimeline().getEstimatedDeliveryTime();
	}

	public LocalDateTime getEstimatedDeliveryEndTime(){
		return this.getTimeline().getEstimatedDeliveryEndTime();
	}

	public PlatformSourceEnum getPlatformSourceEnum(){
		return this.getCustomerOrderKey().getPlatformSourceEnum();
	}

	public Long getFulfillmentOrderId(){
		return this.getCustomerOrderKey().getFulfillOrderId();
	}

	public LocalDateTime getCreateTime(){
		return this.getTimeline().getCreateTime();
	}

	public OrderKey getOrderKey(){
		return new OrderKey(getTenantId(),getStoreId(),getOrderId());
	}

	public LocalDateTime getLastEventTime(){
		return this.getTimeline().getLastEventTime();
	}

	public LocalDateTime getDeliveryDoneTime(){
		return this.getTimeline().getDeliveryDoneTime();
	}

	public Integer getOrderSource(){
		return this.getCustomerOrderKey().getOrderSource();
	}

	public Integer getDaySeq(){
		return this.getCustomerOrderKey().getDaySeq();
	}

	public String getDaySeqNum(){
		return this.getCustomerOrderKey().getDaySeqNum();
	}

	public Boolean getReserved(){
		return this.getCustomerOrderKey().getReserved();
	}


	public void setReceiver(Receiver receiver){
		this.receiver = receiver;
	}

	public void setEstimatedDeliveryTime(LocalDateTime estimatedDeliveryTime){
		this.getTimeline().setEstimatedDeliveryTime(estimatedDeliveryTime);
	}

	public void setEstimatedDeliveryEndTime(LocalDateTime estimatedDeliveryEndTime){
		this.getTimeline().setEstimatedDeliveryEndTime(estimatedDeliveryEndTime);
	}

	public void setStoreId(Long storeId){
		this.storeId = storeId;
	}

	public void setTenantId(Long tenantId){
		this.tenantId = tenantId;
	}

	public void setOrderKey(OrderKey orderKey){
		this.tenantId = orderKey.getTenantId();
		this.storeId = orderKey.getStoreId();
		this.getCustomerOrderKey().setOrderId(orderKey.getOrderId());
	}

	public void setPlatformSourceEnum(PlatformSourceEnum platformSourceEnum){
		this.getCustomerOrderKey().setPlatformSourceEnum(platformSourceEnum);
	}

	public void setFulfillmentOrderId(Long fulfillmentOrderId){
		this.getCustomerOrderKey().setFulfillOrderId(fulfillmentOrderId);
	}

	public boolean isPickDeliverySplit() {
		if (Objects.nonNull(pickDeliverySplitTag)  && pickDeliverySplitTag) {
			return true;
		}
		return false;
	}
}
