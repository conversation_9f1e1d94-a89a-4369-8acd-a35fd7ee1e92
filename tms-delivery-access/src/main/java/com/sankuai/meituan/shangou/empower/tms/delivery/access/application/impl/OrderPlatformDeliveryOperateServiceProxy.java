package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.impl;

import com.sankuai.meituan.shangou.empower.ocms.channel.component.ServiceChannelProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.DeliveryOperationFailedException;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.OrderPlatformDeliveryOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.FourWheelCheckInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.FourWheelPreviewInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * OrderPlatformDeliveryOperateServiceFacade
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
@Slf4j
@Primary
@Service
public final class OrderPlatformDeliveryOperateServiceProxy extends ServiceChannelProxy<OrderPlatformDeliveryOperateService> implements OrderPlatformDeliveryOperateService {

    public OrderPlatformDeliveryOperateServiceProxy(List<OrderPlatformDeliveryOperateService> services) {
        super(services);
    }

    @Override
    public boolean updateTip(UpdateTipCmd cmd) {
        log.info("UpdateTipCmd: {}", cmd);
        return getService(cmd.getChannelId()).updateTip(cmd);
    }

    @Override
    public boolean cancelDelivery(CancelDeliveryCmd cmd) {
        log.info("CancelDeliveryCmd: {}", cmd);
        return getService(cmd.getChannelId()).cancelDelivery(cmd);
    }

    @Override
    public boolean callDelivery(CallDeliveryCmd cmd) {
        log.info("CallDeliveryCmd: {}", cmd);
        return getService(cmd.getChannelId()).callDelivery(cmd);
    }

    @Override
    public boolean reportException(ReportExceptionCmd cmd) {
        log.info("ReportExceptionCmd: {}", cmd);
        return getService(cmd.getChannelId()).reportException(cmd);
    }

    @Override
    public boolean auditException(AuditExceptionCmd cmd) {
        log.info("AuditExceptionCmd: {}", cmd);
        return getService(cmd.getChannelId()).auditException(cmd);
    }

    @Override
    public FourWheelPreviewInfo dispatchPreview(FourWheelPreviewCmd cmd) {
        log.info("dispatchPreview: {}", cmd);
        return getService(cmd.getChannelId()).dispatchPreview(cmd);
    }

    @Override
    public boolean orderDispatch(FourWheelDispatchCmd cmd) {
        log.info("orderDispatch: {}", cmd);
        return getService(cmd.getChannelId()).orderDispatch(cmd);
    }

    @Override
    public FourWheelCheckInfo fourWheelCheck(FourWheelCheckCmd cmd) throws DeliveryOperationFailedException {
        log.info("fourWheelCheck: {}", cmd);
        return getService(cmd.getChannelId()).fourWheelCheck(cmd);
    }

}
