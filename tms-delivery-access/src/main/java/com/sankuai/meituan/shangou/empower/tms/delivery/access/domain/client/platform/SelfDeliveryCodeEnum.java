package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform;

/**
 * <AUTHOR>
 */

public enum SelfDeliveryCodeEnum {

    SUCCESS(0, "成功"),
    AGG_SUCCESS(2001, "聚合配送转自配送成功"),
    INVALID_TRANS(2002, "转单前后渠道一致，无需转单");

    private final int code;
    private final String messageTemplate;
    SelfDeliveryCodeEnum(int code, String messageTemplate) {
        this.code = code;
        this.messageTemplate = messageTemplate;
    }
    public int getCode() {
        return code;
    }
}
