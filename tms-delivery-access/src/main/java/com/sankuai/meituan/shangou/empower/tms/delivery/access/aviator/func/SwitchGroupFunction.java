package com.sankuai.meituan.shangou.empower.tms.delivery.access.aviator.func;

import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorType;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/1/18
 * @email jianglilin02@meituan
 */
public class SwitchGroupFunction extends AbstractVariadicFunction {

    public static final String FUNCTION_NAME = "F_SWITCH_GROUP";

    @Override
    public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... args) {
        for (AviatorObject arg : args) {
            // aviator big decimal 对应 decimal
            if (arg != null && arg.getAviatorType().equals(AviatorType.Decimal)) {
                Number result = FunctionUtils.getNumberValue(arg, env);
                if (result != null) {
                    return new AviatorDecimal(result);
                }
            }
        }
        //NOTE：未命中任何条件时，不报错而是返回-1
        return new AviatorDecimal(-1);
    }

    @Override
    public String getName() {
        return FUNCTION_NAME;
    }
}
