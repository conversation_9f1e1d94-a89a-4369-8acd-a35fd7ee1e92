package com.sankuai.meituan.shangou.empower.tms.delivery.access.application;

import com.google.common.annotations.VisibleForTesting;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.DeliveryOrderLog;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.DeliveryOrderLogRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MoneyUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.exception.OuterQueryDeliveryOrderTypeNotSupportException;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.*;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 外部查询配送信息应用层
 * <AUTHOR>
 */
@Service
@Slf4j
public class OuterQueryDeliveryInfoApplicationService {

    @Resource
    private OrderSystemClient orderSystemClient;

    @Resource
    private DeliveryChannelApplicationService deliveryChannelApplicationService;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private DeliveryOrderLogRepository deliveryOrderLogRepository;

    @Resource
    private TenantSystemClient tenantSystemClient;

    @Resource
    private NewSupplyRiderLocationRepository riderLocationRepository;

    private static final List<Integer> DELIVERY_EVENT_CODES =
            Arrays.asList(DeliveryEventEnum.RIDER_ARRIVE_SHOP.getCode(), DeliveryEventEnum.RIDER_START_DELIVERY.getCode());

    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public OuterQueryRiderTrackImmutableInfoDto getDeliveryImmutableInfo(Long orderId) {
        Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(orderId, false);
        if (orderInfoResult.isFail() || Objects.isNull(orderInfoResult.getInfo())) {
            log.error("getDeliveryImmutableInfo, query orderInfo failed");
            return OuterQueryRiderTrackImmutableInfoDto.EMPTY;
        }
        OrderInfo orderInfo = orderInfoResult.getInfo();

        // 当前仅对自配送订单开放查询能力
        if (!orderInfo.isSelfDelivery()) {
            log.error("getDeliveryImmutableInfo, orderInfo is not selfDelivery, orderId is {}", orderId);
            throw new OuterQueryDeliveryOrderTypeNotSupportException(FailureCodeEnum.OUTER_QUERY_DELIVERY_ORDER_TYPE_NOT_SUPPORT);
        }

        Long tenantId = orderInfo.getOrderKey().getTenantId();
        Long storeId = orderInfo.getPoiId();
        TenantStoreInfo storeInfo = tenantSystemClient.queryStoreDetailInfo(tenantId, storeId);
        if (Objects.isNull(storeInfo)) {
            log.error("getDeliveryImmutableInfo, storeInfo is null");
            return OuterQueryRiderTrackImmutableInfoDto.EMPTY;
        }

        return buildRiderTrackImmutableInfo(storeInfo, orderInfo);
    }

    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public OuterQueryRiderTrackDynamicInfoDto getDeliveryDynamicInfo(Long orderId) {
        Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(orderId, false);
        if (orderInfoResult.isFail() || Objects.isNull(orderInfoResult.getInfo())) {
            log.error("getDeliveryDynamicInfo, query orderInfo failed");
            return OuterQueryRiderTrackDynamicInfoDto.EMPTY;
        }
        OrderInfo orderInfo = orderInfoResult.getInfo();

        // 当前仅对自配送订单开放查询能力
        if (!orderInfo.isSelfDelivery()) {
            log.error("getDeliveryDynamicInfo, orderInfo is not selfDelivery, orderId is {}", orderId);
            throw new OuterQueryDeliveryOrderTypeNotSupportException(FailureCodeEnum.OUTER_QUERY_DELIVERY_ORDER_TYPE_NOT_SUPPORT);
        }

        List<DeliveryOrder> deliveryOrderList = new ArrayList<>();
        if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
            deliveryOrderList = deliveryOrderRepository.getDeliveryOrdersByOrderIdListWithTenant(Collections.singletonList(orderId),orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
        }else {
            deliveryOrderList = deliveryOrderRepository.getDeliveryOrdersByOrderIdList(Collections.singletonList(orderId));
        }

        if (CollectionUtils.isEmpty(deliveryOrderList)) {
            log.info("getDeliveryDynamicInfo, deliveryOrderList is empty, orderId: {}", orderId);
            return OuterQueryRiderTrackDynamicInfoDto.EMPTY;
        }
        DeliveryOrder deliveryOrder = filterAvailableDeliveryOrder(deliveryOrderList);

        Integer deliveryChannelId = deliveryOrder.getDeliveryChannel();
        DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryChannelId);
        if (Objects.isNull(deliveryChannel)) {
            log.error("getDeliveryDynamicInfo, deliveryChannel is null, deliveryChannelId: {}", deliveryChannelId);
            return OuterQueryRiderTrackDynamicInfoDto.EMPTY;
        }

        // 查询骑手坐标
        Optional<CoordinatePoint> coordinatePointOptional = Optional.empty();
        boolean isQueryRiderLocationFromSquirrel = MccConfigUtils.getOuterQueryRiderLocation4DynamicInfo();
        if (isQueryRiderLocationFromSquirrel) {
            coordinatePointOptional = riderLocationRepository.queryRiderLocationFromSquirrel(orderId, deliveryChannel);
        } else {
            coordinatePointOptional = riderLocationRepository.queryRiderLocationFromClient(deliveryOrder);
        }
        return buildRiderTrackDynamicInfo(deliveryOrder, deliveryChannel, coordinatePointOptional);
    }

    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public OuterQueryDeliveryInfoDto queryDeliveryInfo(Long orderId) {
        Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(orderId, false);
        if (orderInfoResult.isFail() || Objects.isNull(orderInfoResult.getInfo())) {
            log.error("queryDeliveryInfo, query orderInfo failed");
            return OuterQueryDeliveryInfoDto.EMPTY;
        }
        OrderInfo orderInfo = orderInfoResult.getInfo();

        // 当前仅对自配送订单开放查询能力
        if (!orderInfo.isSelfDelivery()) {
            log.error("queryDeliveryInfo, orderInfo is not selfDelivery, orderId is {}", orderId);
            throw new OuterQueryDeliveryOrderTypeNotSupportException(FailureCodeEnum.OUTER_QUERY_DELIVERY_ORDER_TYPE_NOT_SUPPORT);
        }

        List<DeliveryOrder> deliveryOrderList = new ArrayList<>();
        if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
            deliveryOrderList = deliveryOrderRepository.getDeliveryOrdersByOrderIdListWithTenant(Collections.singletonList(orderId),orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
        }else {
            deliveryOrderList = deliveryOrderRepository.getDeliveryOrdersByOrderIdList(Collections.singletonList(orderId));
        }

        if (CollectionUtils.isEmpty(deliveryOrderList)) {
            log.info("queryDeliveryInfo, deliveryOrderList is empty, orderId: {}", orderId);
            return OuterQueryDeliveryInfoDto.EMPTY;
        }
        DeliveryOrder deliveryOrder = filterAvailableDeliveryOrder(deliveryOrderList);

        Integer deliveryChannelId = deliveryOrder.getDeliveryChannel();
        DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryChannelId);
        if (Objects.isNull(deliveryChannel)) {
            log.error("queryDeliveryInfo, deliveryChannel is null, deliveryChannelId: {}", deliveryChannelId);
            return OuterQueryDeliveryInfoDto.EMPTY;
        }

        // 查询骑手坐标
        Optional<CoordinatePoint> coordinatePointOptional = Optional.empty();
        boolean isQueryRiderLocationFromSquirrel = MccConfigUtils.getOuterQueryRiderLocation4DeliveryInfo();
        if (isQueryRiderLocationFromSquirrel) {
            coordinatePointOptional = riderLocationRepository.queryRiderLocationFromSquirrel(orderId, deliveryChannel);
        } else {
            coordinatePointOptional = riderLocationRepository.queryRiderLocationFromClient(deliveryOrder);
        }

        // 查询配送关键节点时间
        Map<Long, List<DeliveryOrderLog>> deliveryOrderLogMap = deliveryOrderLogRepository.getLogsByDeliveryOrderIdsAndDeliveryEventCodes(Collections.singletonList(deliveryOrder.getId()), DELIVERY_EVENT_CODES);

        return buildDeliveryInfo(orderId, deliveryOrder, deliveryChannel, coordinatePointOptional, deliveryOrderLogMap);
    }

    private OuterQueryRiderTrackImmutableInfoDto buildRiderTrackImmutableInfo(TenantStoreInfo storeInfo, OrderInfo orderInfo) {
        Receiver receiver = orderInfo.getReceiver();
        if (Objects.isNull(receiver)) {
            log.error("getDeliveryImmutableInfo, receiver is null");
            return OuterQueryRiderTrackImmutableInfoDto.EMPTY;
        }
        Address receiveAddress = receiver.getReceiverAddress();
        if (Objects.isNull(receiveAddress)) {
            log.error("getDeliveryImmutableInfo, receiveAddress is null");
            return OuterQueryRiderTrackImmutableInfoDto.EMPTY;
        }
        CoordinatePoint receiveCoordinatePoint = receiveAddress.getCoordinatePoint();
        if (Objects.isNull(receiveCoordinatePoint)) {
            log.error("getDeliveryImmutableInfo, receiveCoordinatePoint is null");
            return OuterQueryRiderTrackImmutableInfoDto.EMPTY;
        }

        OuterQueryRiderTrackImmutableInfoDto result = new OuterQueryRiderTrackImmutableInfoDto();
        StoreInfoDto storeInfoDto = new StoreInfoDto();
        storeInfoDto.setStoreCoordinate(CoordinateDto.builder().longitude(storeInfo.getLongitude()).latitude(storeInfo.getLatitude()).build());
        ReceiverInfoDto receiverInfoDto = new ReceiverInfoDto();
        receiverInfoDto.setReceiverCoordinate(CoordinateDto.builder().longitude(receiveCoordinatePoint.getLongitude()).latitude(receiveCoordinatePoint.getLatitude()).build());
        result.setStoreInfoDto(storeInfoDto);
        result.setReceiverInfoDto(receiverInfoDto);
        return result;
    }

    private OuterQueryRiderTrackDynamicInfoDto buildRiderTrackDynamicInfo(DeliveryOrder deliveryOrder, DeliveryChannel deliveryChannel,
                                                                          Optional<CoordinatePoint> coordinatePointOptional) {
        RiderInfoDto riderInfoDto = new RiderInfoDto();
        if (isShowRiderPosition(deliveryOrder)) {
            coordinatePointOptional.ifPresent(coordinatePoint -> riderInfoDto.setRiderCoordinate(CoordinateDto.builder()
                    .longitude(coordinatePoint.getLongitude())
                    .latitude(coordinatePoint.getLatitude())
                    .build()));
        }
        if (Objects.nonNull(deliveryOrder.getRiderInfo())) {
            riderInfoDto.setRiderName(deliveryOrder.getRiderInfo().getRiderName());
            riderInfoDto.setRiderPhone(deliveryOrder.getRiderInfo().getRiderPhone());
        }

        OuterQueryRiderTrackDynamicInfoDto result = new OuterQueryRiderTrackDynamicInfoDto();
        result.setDeliveryStatusCode(deliveryOrder.getStatus().getCode());
        result.setDeliveryChannelCode(deliveryChannel.getCarrierCode());
        result.setDeliveryChannelDesc(deliveryChannel.getCarrierName());
        if (Objects.nonNull(deliveryOrder.getLastEventTime())) {
            result.setLastEventTime(TimeUtil.toMilliSeconds(deliveryOrder.getLastEventTime()));
        }
        if (Objects.nonNull(deliveryOrder.getDeliveryDoneTime())) {
            result.setDeliveryDoneTime(TimeUtil.toMilliSeconds(deliveryOrder.getDeliveryDoneTime()));
        }
        result.setRiderInfoDto(riderInfoDto);

        return result;
    }

    private OuterQueryDeliveryInfoDto buildDeliveryInfo(Long orderId, DeliveryOrder deliveryOrder,
                                                        DeliveryChannel deliveryChannel,
                                                        Optional<CoordinatePoint> coordinatePointOptional,
                                                        Map<Long, List<DeliveryOrderLog>> deliveryOrderLogMap) {
        RiderInfoDto riderInfoDto = new RiderInfoDto();
        if (isShowRiderPosition(deliveryOrder)) {
            coordinatePointOptional.ifPresent(coordinatePoint -> riderInfoDto.setRiderCoordinate(CoordinateDto.builder()
                    .longitude(coordinatePoint.getLongitude())
                    .latitude(coordinatePoint.getLatitude())
                    .build()));
        }
        if (Objects.nonNull(deliveryOrder.getRiderInfo())) {
            riderInfoDto.setRiderName(deliveryOrder.getRiderInfo().getRiderName());
            riderInfoDto.setRiderPhone(deliveryOrder.getRiderInfo().getRiderPhone());
        }

        OuterQueryDeliveryInfoDto result = new OuterQueryDeliveryInfoDto();
        result.setOrderId(orderId);
        result.setDeliveryStatusCode(deliveryOrder.getStatus().getCode());
        result.setDeliveryChannelCode(deliveryChannel.getCarrierCode());
        result.setDeliveryChannelDesc(deliveryChannel.getCarrierName());
        if (Objects.nonNull(deliveryOrder.getDistance())) {
            result.setDeliveryDistance(deliveryOrder.getDistance());
        }
        if (Objects.nonNull(deliveryOrder.getDeliveryFee())) {
            result.setDeliveryFee(MoneyUtil.fromYuanToCenter(deliveryOrder.getDeliveryFee()).intValue());
        }
        result.setRiderInfoDto(riderInfoDto);

        // 组装配送关键节点的时间
        if (MapUtils.isNotEmpty(deliveryOrderLogMap)) {
            List<DeliveryOrderLog> deliveryOrderLogList = deliveryOrderLogMap.get(deliveryOrder.getId());
            if (CollectionUtils.isNotEmpty(deliveryOrderLogList)) {
                if (Objects.equals(DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode(), deliveryChannel.getDeliveryPlatFormCode())) {
                    // 商家自己送没有记骑手到店时间，骑手到店时间和骑手取货时间一致
                    deliveryOrderLogList.stream().filter(deliveryOrderLog -> filterDeliveryOpLog(deliveryOrderLog, DeliveryEventEnum.RIDER_START_DELIVERY))
                            .findAny().ifPresent(deliveryOrderLog -> result.setRiderArrivedStoreTime(TimeUtil.toMilliSeconds(deliveryOrderLog.getChangeTime())));
                } else {
                    deliveryOrderLogList.stream().filter(deliveryOrderLog -> filterDeliveryOpLog(deliveryOrderLog, DeliveryEventEnum.RIDER_ARRIVE_SHOP))
                            .findAny().ifPresent(deliveryOrderLog -> result.setRiderArrivedStoreTime(TimeUtil.toMilliSeconds(deliveryOrderLog.getChangeTime())));
                }
                deliveryOrderLogList.stream().filter(deliveryOrderLog -> filterDeliveryOpLog(deliveryOrderLog, DeliveryEventEnum.RIDER_START_DELIVERY))
                        .findAny().ifPresent(deliveryOrderLog -> result.setRiderTakeGoodsTime(TimeUtil.toMilliSeconds(deliveryOrderLog.getChangeTime())));
            }
        }

        // 组装配送取消原因
        if (Objects.equals(DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode(), deliveryChannel.getDeliveryPlatFormCode())) {
            // 对于商家自己送的配送取消原因用固定值填充
            if (Objects.equals(DeliveryStatusEnum.DELIVERY_CANCELLED, deliveryOrder.getStatus())) {
                result.setCancelReasonCode(DeliveryCancelReasonEnum.OTHER_CANCEL.getCancelCode());
                result.setCancelReasonDesc(DeliveryCancelReasonEnum.OTHER_CANCEL.getCancelReason());
            }
        } else {
            Integer deliveryCancelCode = deliveryOrder.getDeliveryCancelCode();
            DeliveryCancelReasonEnum deliveryCancelReasonEnum = DeliveryCancelReasonEnum.enumOf(deliveryCancelCode);
            if (Objects.nonNull(deliveryCancelCode) && Objects.nonNull(deliveryCancelReasonEnum)) {
                result.setCancelReasonCode(deliveryCancelCode);
                result.setCancelReasonDesc(deliveryCancelReasonEnum.getCancelReason());
            }
        }

        return result;
    }

    /**
     * 找出最近、有效的运单
     * 优先找出激活的运单，如果没有激活的，则按照id取最大的一个
     */
    @VisibleForTesting
    DeliveryOrder filterAvailableDeliveryOrder(List<DeliveryOrder> deliveryOrders) {

//        deliveryOrders.sort(Comparator.comparingLong(deliveryOrder -> {
//            if (deliveryOrder.isActive()) {
//                return Long.MIN_VALUE;
//            } else {
//                return -deliveryOrder.getId();
//            }
//        }));
        return DeliveryOrder.filterActiveDeliveryOrder(deliveryOrders);
    }

    private boolean filterDeliveryOpLog(DeliveryOrderLog deliveryOrderLog, DeliveryEventEnum deliveryEventEnum) {
        if (Objects.isNull(deliveryOrderLog) || Objects.isNull(deliveryEventEnum) || Objects.isNull(deliveryOrderLog.getChangeTime())) {
            return false;
        }

        return Objects.equals(deliveryOrderLog.getChangeEvent(), deliveryEventEnum);
    }

    private boolean isShowRiderPosition(DeliveryOrder deliveryOrder) {
        // 配送取消不展示骑手坐标
        DeliveryStatusEnum deliveryStatusEnum = deliveryOrder.getStatus();
        if (Objects.equals(deliveryStatusEnum, DeliveryStatusEnum.DELIVERY_CANCELLED)) {
            return false;
        }

        // 配送完成后8小时不展示骑手坐标
        if (Objects.equals(deliveryStatusEnum, DeliveryStatusEnum.DELIVERY_DONE)) {
            Integer noShowRiderPositionHours = MccConfigUtils.getOuterQueryDeliveryInfoNoShowRiderPositionHours();
            LocalDateTime deliveryDoneTime = deliveryOrder.getDeliveryDoneTime();
            LocalDateTime now = LocalDateTime.now();
            if (now.minusHours(noShowRiderPositionHours).isAfter(deliveryDoneTime)) {
                return false;
            }
        }

        return true;
    }

}
