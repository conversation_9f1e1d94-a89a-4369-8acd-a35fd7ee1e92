package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.meituan.shangou.saas.order.platform.enums.DistributeMethodEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TmsMccUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryConditionInOrder {

    private static final int selfDeliveryMark = 1;

    private static final int platformDeliveryMark = 0;

    /**
     * todo zhushi
     */
    private Integer orderStatus;

    private Integer orderSource;

    private Integer deliveryMethod;

    private Integer isSelfDelivery;



    /**
     * 订单已经结束
     *
     * @return
     */
    public boolean isOrderFinished() {
        return orderStatus == OrderStatusEnum.COMPLETED.getValue() || orderStatus == OrderStatusEnum.CANCELED.getValue();
    }

    /**
     * 订单正在被处理（退款、申诉）
     *
     * @return
     */
    public boolean isOrderWaitAudited() {
        return orderStatus == OrderStatusEnum.REFUND_APPLIED.getValue() || orderStatus == OrderStatusEnum.APPEAL_APPLIED.getValue();
    }

    /**
     * 订单已经被商家确认接单过
     * @return
     */
    public boolean isOrderConfirmed() {
        return orderStatus > OrderStatusEnum.MERCHANT_CONFIRMED.getValue();
    }

    public boolean selfDeliveryMark() {
        return Objects.equals(isSelfDelivery, selfDeliveryMark);
    }

    /**
     * 订单渠道平台配送标记
     * @return
     */
    public boolean isOrderPlatformDeliveryMark() {
        return Objects.equals(isSelfDelivery, platformDeliveryMark);
    }

    /**
     * 订单是否可以配送
     * @return
     */
    public boolean orderAllowDelivery(DeliveryOrder deliveryOrder) {

        //未接单或已完结，则不能发配送
        if (!isOrderConfirmed() || isOrderFinished() || isOrderWaitAudited()) {
            return false;
        }

        if (Objects.nonNull(deliveryOrder) && TmsMccUtils.isLaunchStoreDeliveryTenant(deliveryOrder.getTenantId())) {
            return true;
        }

        if (orderSource == OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue() || orderSource == OrderSourceEnum.GLORY.getValue()
                || orderSource == OrderSourceEnum.OTO_ONLINE.getValue()) {
            return isDeliveryToHome();
        }

        return false;

    }

   /**
     * 可以发起三方配送
     * @return
     */
    public boolean canLaunchThirdPartDelivery() {
        if (!isOrderConfirmed() || isOrderFinished() || isOrderWaitAudited()) {
            return false;
        }

        if (orderSource == OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue() || orderSource == OrderSourceEnum.GLORY.getValue()) {
            return selfDeliveryMark() && isDeliveryToHome();

        } else if (orderSource == OrderSourceEnum.OTO_ONLINE.getValue()) {
            return isDeliveryToHome();
        }

        return false;
    }

    /**
     * 可以转三方配送
     * @return
     */
    public boolean canTurnThirdPartDelivery() {
        if (!isOrderConfirmed() || isOrderFinished() || isOrderWaitAudited()) {
            return false;
        }

        if (orderSource == OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue() || orderSource == OrderSourceEnum.GLORY.getValue()) {
            return isOrderPlatformDeliveryMark() && isDeliveryToHome();

        } else if (orderSource == OrderSourceEnum.OTO_ONLINE.getValue()) {
            return isDeliveryToHome();
        }

        return false;
    }

    /**
     * 是否配送到家订单(非自提等)
     */
    private boolean isDeliveryToHome() {
        return deliveryMethod == DistributeMethodEnum.HOME_DELIVERY.getValue();
    }
}
