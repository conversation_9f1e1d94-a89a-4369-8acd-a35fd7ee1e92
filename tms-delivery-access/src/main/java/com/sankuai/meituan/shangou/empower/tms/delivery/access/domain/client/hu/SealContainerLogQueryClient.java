package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.hu;

import com.sankuai.shangou.logistics.hu.api.dto.SealContainerLogDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/6 15:01
 **/
public interface SealContainerLogQueryClient {
    List<SealContainerLogDTO> queryUsingContainerByOrderKey(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType);

    void signReturnTimeout(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType);

    void signOrderCompletedOrCanceled(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType);
}
