package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.core.Local;
import org.apache.calcite.common.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.*;
import java.time.chrono.ChronoZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/9
 */
@Slf4j
public class TimeUtil {

	public static final String FMT_YMD_HMS = "yyyy-MM-dd HH:mm:ss";

	public static final DateTimeFormatter DX_TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");

	public static LocalDateTime getEpochTime() {
		return Instant.EPOCH.atOffset(ZoneOffset.UTC).toLocalDateTime();
	}

	public static LocalDateTime fromMilliSeconds(Long milliseconds) {
		if (milliseconds == null) {
			return null;
		}
		return Instant.ofEpochMilli(milliseconds).atZone(ZoneId.systemDefault()).toLocalDateTime();
	}

	public static LocalDateTime fromSeconds(Long seconds) {
		if (seconds == null) {
			return null;
		}
		return Instant.ofEpochSecond(seconds).atZone(ZoneId.systemDefault()).toLocalDateTime();
	}

	public static Long toMilliSeconds(LocalDateTime time) {
		if (time == null) {
			return null;
		}

		return time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
	}

	public static Optional<Integer> toSeconds(LocalDateTime time) {
		return Optional.ofNullable(time)
				.map(it -> it.atZone(ZoneId.systemDefault()))
				.map(ChronoZonedDateTime::toEpochSecond)
				.map(Math::toIntExact);
	}

	public static Optional<LocalDateTime> toLocalDateTime(String dateTimeStr) {
		if (StringUtils.isEmpty(dateTimeStr)) {
			return Optional.empty();
		}

		try {
			 LocalDateTime localDateTime = LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern(FMT_YMD_HMS));
			 return Optional.of(localDateTime);
		} catch (Exception e) {
			log.error("toLocalDateTime error, dateTimeStr is {}", dateTimeStr);
			return Optional.empty();
		}
	}

	/**
	 * 计算给定天数对应的秒数，注意不要溢出
	*/
	public static int toSeconds(Integer days) {
		if (Objects.isNull(days)) {
			return NumberUtils.INTEGER_ZERO;
		}

		try {
			return (int) TimeUnit.DAYS.toSeconds(days);
		} catch (Exception e) {
			return NumberUtils.INTEGER_ZERO;
		}
	}

	public static Long fromMilliSeconds2Seconds(Long milliSeconds) {
		if (Objects.isNull(milliSeconds)) {
			return null;
		}
		return milliSeconds / 1000;
	}

	public static LocalTime fromFormat(String format) {
		return LocalTime.parse(format, DX_TIME_FORMATTER);
	}


}
