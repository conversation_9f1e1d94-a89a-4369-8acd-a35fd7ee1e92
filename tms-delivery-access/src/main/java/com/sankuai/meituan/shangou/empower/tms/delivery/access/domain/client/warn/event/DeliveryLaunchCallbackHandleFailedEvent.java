package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.DeliveryLaunchResultCallbackCmd;

import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/8
 */
public class DeliveryLaunchCallbackHandleFailedEvent extends DeliveryWarnEvent {

	private final String warnMessage;

	public DeliveryLaunchCallbackHandleFailedEvent(DeliveryLaunchResultCallbackCmd cmd, String failReason) {
		this.warnMessage = String.format(
				"配送发起回调消费失败\n[门店id:%d]\n[赋能订单号:%d]\n[是否发起成功:%s]\n[消费失败原因:%s]",
				Optional.ofNullable(cmd).map(DeliveryLaunchResultCallbackCmd::getStoreId).orElse(-1L),
				Optional.ofNullable(cmd).map(DeliveryLaunchResultCallbackCmd::getOrderId).orElse(-1L),
				Optional.ofNullable(cmd).map(DeliveryLaunchResultCallbackCmd::isLaunchSuccess).map(Object::toString).orElse(""),
				failReason
		);
	}

	@Override
	public String getWarnMessage() {
		return warnMessage;
	}
}
