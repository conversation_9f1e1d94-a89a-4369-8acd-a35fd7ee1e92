package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;

import java.util.Optional;

/**
 * 新供给骑手坐标缓存相关接口
 *
 * <AUTHOR>
 * @date 2023/4/7
 */
public interface SquirrelNewSupplyRiderLocationRepository {

    /**
     * @description: 查询骑手坐标信息
     * @param: orderId 赋能订单ID
     * @return 骑手坐标信息
     */
    Optional<CoordinatePoint> queryRiderLocation(Long orderId);

    /**
     * @description: 保存骑手坐标信息
     * @param: orderId 赋能订单ID
     * @param: coordinatePoint 骑手坐标信息
     * @return true表示保存成功
    */
    boolean saveRiderLocation(Long orderId, CoordinatePoint coordinatePoint);

}
