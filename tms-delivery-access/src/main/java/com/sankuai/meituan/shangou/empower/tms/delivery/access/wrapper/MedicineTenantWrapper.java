package com.sankuai.meituan.shangou.empower.tms.delivery.access.wrapper;

import com.alibaba.fastjson.JSON;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.meituan.linz.boot.util.Fun;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.exception.CommonErrException;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.UnmannedModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigListResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aop.LoadTestAop;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
@Rhino
public class MedicineTenantWrapper {

    @Autowired
    private ConfigThriftService configThriftService;

    /**
     * 10分钟过期缓存
     */
    private static final Cache<String, Object> TEN_MINUTES_LOCAL_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();
    private static final String UWMS_TENANT_ID_CACHE_KEY = "uwms_tenant_id_cache_key";


    /**
     * 判断是否是无人微仓租户
     */
    @LoadTestAop
    public Boolean isUwmsMedicineTenant(Long tenantId) {
        if (tenantId == null) {
            return false;
        }

        List<Long> uwmsTenantIds = queryUwmsTenantId();

        // 强制判断，一定存在，才过滤
        return uwmsTenantIds.contains(tenantId);
    }


    /**
     * 通过业务类型查询租户id信息
     *
     * @param bizMode 业务类型
     * @param content 业务类型中子类型列表，例如无人仓-医药 无人仓-商超
     */
    @Degrade(rhinoKey = "TenantFacade.queryTenantIdsByBizMode",
            fallBackMethod = "queryTenantIdsByBizModeFallback",
            timeoutInMilliseconds = 5000)
    public List<Long> queryTenantIdsByBizMode(ConfigItemEnum bizMode, List<String> content) {
        try {
            TenantConfigListResponse tenantInfoList = configThriftService.batchQueryTenantConfigsByContent(bizMode.getKey(), content);
            if (tenantInfoList == null) {
                log.error("TenantInfoFacade.queryTenantIdsByBizMode, 通过业务类型查询租户id信息 返回为空, bizMode: {}, content: {}", bizMode, content);
                throw new CommonErrException("通过业务类型查询租户id失败", ResultCode.RETRY_INNER_FAIL.getCode());
            }
            if (tenantInfoList.getStatus() == null || tenantInfoList.getStatus().getCode() != 0) {
                log.warn("TenantInfoFacade.queryTenantIdsByBizMode, 通过业务类型查询租户id信息 返回错误, bizMode: {}, content: {}, response: {}", bizMode, content, tenantInfoList);
                throw new CommonErrException("通过业务类型查询租户id返回错误", ResultCode.RETRY_INNER_FAIL.getCode());
            }
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(tenantInfoList.getConfigList())) {
                log.warn("TenantInfoFacade.queryTenantIdsByBizMode, 通过业务类型查询租户id信息 详情为空, bizMode: {}, content: {}, response: {}", bizMode, content, tenantInfoList);
                return Collections.emptyList();
            }
            List<Long> tenantIds = Fun.map(tenantInfoList.getConfigList(), ConfigDto::getTenantId);
            log.info("TenantInfoFacade.queryTenantIdsByBizMode, 通过业务类型查询租户id信息 查询结果, bizMode: {}, content: {}, tenantId: {}", bizMode, content, tenantIds);
            return tenantIds;
        } catch (Exception e) {
            log.error("TenantInfoFacade.queryTenantIdsByBizMode 方法执行出现异常, 降级处理", e);
            return Collections.emptyList();
        }
    }

    /**
     * 查询所有的无人微仓业务线下的租户id，不区分具体的无人微仓业务模式
     */
    @LoadTestAop
    public List<Long> queryUwmsTenantId() {
        try {
            Object uwmsTenantIds = TEN_MINUTES_LOCAL_CACHE.get(UWMS_TENANT_ID_CACHE_KEY, (Callable<List<Long>>) () -> {
                List<Long> uwmsTenantIdsFromRpc = new ArrayList<>();
                try {
                    uwmsTenantIdsFromRpc = queryTenantIdsByBizMode(
                            ConfigItemEnum.UNMANNED_MODE,
                            Arrays.stream(UnmannedModeEnum.values()).map(item -> {
                                Map<String, String> contentMap = new HashMap<>();
                                contentMap.put(UnmannedModeEnum.key, item.getMode());
                                return JSON.toJSONString(contentMap);
                            }).collect(Collectors.toList())
                    );

                    if (org.apache.commons.collections4.CollectionUtils.isEmpty(uwmsTenantIdsFromRpc)) {
                        return Collections.emptyList();
                    }
                } catch (Exception e) {
                    log.error("获取无人微仓租户id配置失败", e);
                    throw new CommonRuntimeException("获取无人微仓租户id配置失败", e);
                }
                return uwmsTenantIdsFromRpc;
            });
            return uwmsTenantIds instanceof List ? (List<Long>) uwmsTenantIds : Collections.emptyList();
        } catch (ExecutionException e) {
            log.error("获取无人微仓租户id配置失败", e);
            throw new CommonRuntimeException("获取无人微仓租户id配置失败");
        }
    }

    public List<Long> queryTenantIdsByBizModeFallback(ConfigItemEnum bizMode, List<String> content) {
        log.error("queryTenantIdsByBizModeFallback");
        throw new CommonErrException("通过业务模式获取租户id列表降级", ResultCode.RETRY_INNER_FAIL.getCode());
    }
}
