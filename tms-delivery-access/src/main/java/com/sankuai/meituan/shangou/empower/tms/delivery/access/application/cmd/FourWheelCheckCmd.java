package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
public class FourWheelCheckCmd extends BaseOrderPlatformDeliveryOperateCmd{

    private final String viewOrderId;

    public FourWheelCheckCmd(Long tenantId, Long offlineStoreId, OrderInfo orderInfo, Integer channelId, Operator operator,String viewOrderId) {
        super(tenantId, offlineStoreId, orderInfo, channelId, operator);
        this.viewOrderId = viewOrderId;
    }
}
