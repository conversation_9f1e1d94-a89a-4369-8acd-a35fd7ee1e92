package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.impl;

import com.sankuai.meituan.shangou.empower.ocms.channel.exception.DeliveryOperationFailedException;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.OrderPlatformDeliveryOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.FourWheelCheckInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.FourWheelPreviewInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * BaseOrderPlatformDeliveryOrderOperateService
 *
 * <AUTHOR>
 * @since 2023/4/6
 */
@Slf4j
public abstract class BaseOrderPlatformDeliveryOperateService implements OrderPlatformDeliveryOperateService {

    @Override
    public boolean updateTip(UpdateTipCmd cmd) throws DeliveryOperationFailedException {
        log.warn("Do nothing: {}", cmd);
        return false;
    }

    @Override
    public boolean cancelDelivery(CancelDeliveryCmd cmd) throws DeliveryOperationFailedException {
        log.warn("Do nothing: {}", cmd);
        return false;
    }

    @Override
    public boolean callDelivery(CallDeliveryCmd cmd) throws DeliveryOperationFailedException {
        log.warn("Do nothing: {}", cmd);
        return false;
    }

    @Override
    public boolean reportException(ReportExceptionCmd cmd) throws DeliveryOperationFailedException {
        log.warn("Do nothing: {}", cmd);
        return false;
    }

    @Override
    public boolean auditException(AuditExceptionCmd cmd) throws DeliveryOperationFailedException {
        log.warn("Do nothing: {}", cmd);
        return false;
    }

    /**
     * 如果不是配送异常则清空
     */
    protected void cleanExceptionIfNotTimeout(DeliveryOrder deliveryOrder) {
        if (deliveryOrder.isDeliveryTimeout()) {
            // 如果已经超时，根据状态判断异常类型
            log.info("Delivery order timeout: {}", deliveryOrder);
            DeliveryStatusEnum deliveryStatus = deliveryOrder.getStatus();
            DeliveryExceptionCodeEnum exceptionCode = DeliveryExceptionCodeEnum.resolveByStatus(deliveryStatus);
            if (exceptionCode != DeliveryExceptionCodeEnum.NO_EXCEPTION) {
                // 如果有异常则重置异常
                DeliveryExceptionInfo exceptionInfo = new DeliveryExceptionInfo(
                        DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER, exceptionCode);
                log.info("New exception: {}", exceptionInfo);
                deliveryOrder.onException(exceptionInfo, LocalDateTime.now());
                log.info("Delivery order timeout: {}", deliveryOrder);
                return;
            }
        }

        // 未超时或者判断无异常则清空异常
        deliveryOrder.clearException();
        log.info("Delivery order clean exception: {}", deliveryOrder);
    }

    @Override
    public FourWheelPreviewInfo dispatchPreview(FourWheelPreviewCmd cmd) throws DeliveryOperationFailedException {
        log.warn("Do nothing: {}", cmd);
        return null;
    }

    @Override
    public boolean orderDispatch(FourWheelDispatchCmd cmd) throws DeliveryOperationFailedException {
        log.warn("Do nothing: {}", cmd);
        return false;
    }

    @Override
    public FourWheelCheckInfo fourWheelCheck(FourWheelCheckCmd cmd) throws DeliveryOperationFailedException {
        log.warn("Do nothing: {}", cmd);
        return null;
    }
}
