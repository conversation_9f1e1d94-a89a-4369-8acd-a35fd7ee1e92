package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * ReportExceptionCmd
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
@Getter
@ToString(callSuper = true)
public class ReportExceptionCmd extends BaseOrderPlatformDeliveryOperateCmd {

    private final List<String> pictureUrls;

    public ReportExceptionCmd(Long tenantId, Long offlineStoreId, OrderInfo orderInfo,
            Integer channelId, Operator operator, List<String> pictureUrls) {
        super(tenantId, offlineStoreId, orderInfo, channelId, operator);
        this.pictureUrls = pictureUrls;
    }

}
