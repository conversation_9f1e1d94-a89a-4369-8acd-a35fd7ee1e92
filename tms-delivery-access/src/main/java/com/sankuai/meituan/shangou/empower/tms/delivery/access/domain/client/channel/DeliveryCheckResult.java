package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import lombok.Getter;
import lombok.ToString;

/**
 * 配送前置检查结果
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/7
 */
@Getter
@ToString
public class DeliveryCheckResult {
	/**
	 * 是否校验通过
	 */
	private final boolean success;
	/**
	 * 是否需要重试(网络超时/系统异常等场景需要重试)
	 */
	private final boolean needRetry;
	/**
	 * 拒单信息，包括原因类型和描述
	 * 配送校验失败时有值
	 */
	private final DeliveryExceptionInfo failInfo;

	public DeliveryCheckResult() {
		this.success = true;
		this.needRetry = false;
		this.failInfo = null;
	}

	public DeliveryCheckResult(boolean needRetry, DeliveryExceptionInfo failInfo) {
		Preconditions.checkNotNull(failInfo, "rejectInfo is null");

		this.success = false;
		this.needRetry = needRetry;
		this.failInfo = failInfo;
	}
}
