package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryChangeInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 运单历史
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/8
 */
@Getter
@ToString
public class DeliveryOrderLog {

	/**
	 * 运单变更id
	 */
	@Setter
	private Long id;

	/**
	 * 运单id
	 */
	private final Long deliveryId;

	/**
	 * 运单变更事件
	 */
	private final DeliveryEventEnum changeEvent;

	/**
	 * 运单变更补充信息
	 * 运单异常：异常类型+详细描述
	 * 骑手改派：改派前/后骑手信息
	 */
	private final DeliveryChangeInfo changeInfo;

	/**
	 * 运单变更发生事件
	 */
	private final LocalDateTime changeTime;

	public DeliveryOrderLog(Long deliveryId, DeliveryEventEnum changeEvent, DeliveryChangeInfo changeInfo, LocalDateTime changeTime) {
		Preconditions.checkNotNull(deliveryId, "deliveryId is null");
		Preconditions.checkNotNull(changeEvent, "changeEvent is null");
		Preconditions.checkNotNull(changeTime, "changeTime is null");

		this.deliveryId = deliveryId;
		this.changeEvent = changeEvent;
		this.changeInfo = changeInfo;
		this.changeTime = changeTime;
	}
}
