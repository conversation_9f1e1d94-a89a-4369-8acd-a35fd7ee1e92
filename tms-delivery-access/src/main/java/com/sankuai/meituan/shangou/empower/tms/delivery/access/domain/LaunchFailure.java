package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/4/20
 */
@Getter
@ToString
public class LaunchFailure extends Failure {

	private final DeliveryExceptionInfo exceptionInfo;

	public LaunchFailure(boolean needRetry, FailureCodeEnum failureCodeEnum, Object... params) {
		this(needRetry, DeliveryExceptionCodeEnum.NO_EXCEPTION, failureCodeEnum, params);
	}

	public LaunchFailure(boolean needRetry, DeliveryExceptionCodeEnum exceptionCode, FailureCodeEnum failureCodeEnum, Object... params) {
		super(needRetry, failureCodeEnum, params);
		this.exceptionInfo = new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION, getFailureMessage(), exceptionCode.getCode());
	}

	public LaunchFailure(Failure failure, DeliveryExceptionCodeEnum exceptionCode){
		super(failure.isNeedRetry(), failure.getFailureCode(), failure.getFailureMessage());
		this.exceptionInfo = new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION, getFailureMessage(), exceptionCode.getCode());
	}
}
