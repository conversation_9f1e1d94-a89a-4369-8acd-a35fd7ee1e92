package com.sankuai.meituan.shangou.empower.tms.delivery.access.application;

import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryNotifyService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.TransferOperateInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeliveryTransformApplicationService {

    @Resource
    private OrderSystemClient orderSystemClient;

    @Resource
    private DeliveryNotifyService deliveryNotifyService;


    public void notifyOrderTransfer(DeliveryOrder deliveryOrder, Integer lastDeliveryPlatform) {
        //转单信息同步到订单
        orderSystemClient.syncDeliveryTransToOrderSystem(deliveryOrder,lastDeliveryPlatform);
    }

    public void sendTransferTrack(DeliveryOrder deliveryOrder, TransferOperateInfo transferOperateInfo) {
        try {
            //记录转单日志
            OrderTrackEvent event = new OrderTrackEvent();
            event.setTrackSource(TrackSource.DELIVERY.getType());
            event.setTrackOpType(TrackOpType.TRANS_AGG_DELIVERY.getOpType());
            if (DeliveryChannelEnum.MERCHANT_DELIVERY.getCode() == deliveryOrder.getDeliveryChannel()) {
                event.setTrackOpType(TrackOpType.TRANS_SELF_DELIVERY.getOpType());
            }
            event.setTenantId(deliveryOrder.getTenantId());
            event.setUnifyOrderId(deliveryOrder.getChannelOrderId());
            event.setOrderBizType(deliveryOrder.getOrderBizType());
            event.setOperateTime(System.currentTimeMillis());
            event.setAccountIdList(Collections.singletonList(0L));
            if (transferOperateInfo != null && transferOperateInfo.getOperatorId() != null) {
                event.setAccountIdList(Collections.singletonList(transferOperateInfo.getOperatorId()));
            }
            deliveryNotifyService.notifyDeliveryTrace(event);
        } catch (Exception e) {
            log.info("记录转单日志失败", e);
        }
    }
}
