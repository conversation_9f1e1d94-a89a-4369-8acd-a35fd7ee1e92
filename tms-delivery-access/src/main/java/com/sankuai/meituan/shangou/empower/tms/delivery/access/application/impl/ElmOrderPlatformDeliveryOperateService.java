package com.sankuai.meituan.shangou.empower.tms.delivery.access.application.impl;

import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.DeliveryOperationFailedException;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.platform.CallDeliveryReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.DeliveryOperationResultDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.OcmsResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.OrderPlatformDeliveryOperateThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.OrderPlatformDeliveryOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.CallDeliveryCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

/**
 * ElmOrderPlatformDeliveryOperateService
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
@Slf4j
@Service
public class ElmOrderPlatformDeliveryOperateService extends BaseOrderPlatformDeliveryOperateService implements OrderPlatformDeliveryOperateService {

    @Autowired
    private OrderPlatformDeliveryOperateThriftService orderPlatformDeliveryOperateThriftService;

    @Autowired
    private DeliveryOrderRepository deliveryOrderRepository;

    @Override
    public boolean callDelivery(CallDeliveryCmd cmd) {

        OrderInfo orderInfo = cmd.getOrderInfo();
        if (orderInfo == null || !Objects.equals(orderInfo.getOrderBizType(), DynamicOrderBizType.ELE_ME.getValue())) {
            throw new CommonRuntimeException("param error");
        }

        try {
            OcmsResponse<DeliveryOperationResultDto> resp = orderPlatformDeliveryOperateThriftService.callDelivery
                    (new CallDeliveryReq(cmd.getTenantId(), cmd.getOfflineStoreId(),
                            orderInfo.getChannelOrderId(), cmd.getChannelId()));
            log.info("#callDelivery response: {}", resp);
            if (!resp.isSuccess()) {
                throw new DeliveryOperationFailedException(resp.getMsg());
            }
            Optional<DeliveryOrder> activeDeliveryOrderOpt = Optional.empty();
            if(MccConfigUtils.getDeliveryQueryTenantSwitch(cmd.getTenantId())){
                activeDeliveryOrderOpt = deliveryOrderRepository.getActiveDeliveryOrderWithTenant(
                        orderInfo.getOrderKey().getOrderId(),cmd.getTenantId(),cmd.getOfflineStoreId());
            }else {
                activeDeliveryOrderOpt = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(
                        orderInfo.getOrderKey().getOrderId());
            }

            activeDeliveryOrderOpt.ifPresent(deliveryOrder -> {
                cleanExceptionIfNotTimeout(deliveryOrder);
                deliveryOrderRepository.save(deliveryOrder);
            });
            return true;
        } catch (TException e) {
            log.error("#callDelivery error: {}", e.getMessage(), e);
            throw new CommonRuntimeException(e);
        }
    }

    @Override
    public Integer ofOrderChannel() {
        return DynamicChannelType.ELEM.getChannelId();
    }

}
