<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sankuai.shangou</groupId>
        <artifactId>logistics-dmp</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>dmp-startup</artifactId>

    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>dmp-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>xframe-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.log</groupId>
            <artifactId>scribe-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>dmp-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>dmp-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-exception-collector</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>


            <plugin>
                <groupId>com.sankuai.inf</groupId>
                <artifactId>xmdlog-maven-plugin</artifactId>
                <version>1.1.6</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <phase>compile</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
