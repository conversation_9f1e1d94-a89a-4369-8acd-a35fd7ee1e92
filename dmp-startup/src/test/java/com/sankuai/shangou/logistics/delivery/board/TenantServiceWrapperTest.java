package com.sankuai.shangou.logistics.delivery.board;

import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.shangou.logistics.delivery.TestStartApp;
import com.sankuai.shangou.logistics.delivery.board.load.wrapper.TenantServiceWrapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/7/9 22:47
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestStartApp.class)
@ActiveProfiles("test")
public class TenantServiceWrapperTest {
    @Resource
    private TenantServiceWrapper tenantServiceWrapper;

    @Test
    public void testPoiByIds() {
        Map<Long, PoiInfoDto> infoDtoMap = tenantServiceWrapper.queryPoiByIds(1001197L, Collections.singletonList(49867245L));
        System.out.println(infoDtoMap.get(49867245L).getPoiName());
    }
}
