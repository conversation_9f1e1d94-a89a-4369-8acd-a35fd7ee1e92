package com.sankuai.shangou.logistics.delivery.gray;

import com.sankuai.shangou.logistics.delivery.TestStartApp;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @since 2024/5/11 15:54
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestStartApp.class)
@ActiveProfiles("test")
public class GrayUtilsTest {

    @Test
    public void test() {
        GrayConfigUtils.judgeIsGrayStore(1000L, 110L, "hahah");
    }
}
