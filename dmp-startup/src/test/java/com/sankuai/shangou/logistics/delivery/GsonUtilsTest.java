package com.sankuai.shangou.logistics.delivery;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import org.junit.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2023/7/2 20:51
 **/

public class GsonUtilsTest {

    @Test
    public void map2JsonObject(){
        Map<String, Object> map = new HashMap<>();
        map.put("a","a obj");
        map.put("b","b obj");
        map.put("c","c obj");
        map.put("d","d obj");
        JsonObject jsonObject =new Gson().toJsonTree(map).getAsJsonObject();
        System.out.println(jsonObject);
    }
}
