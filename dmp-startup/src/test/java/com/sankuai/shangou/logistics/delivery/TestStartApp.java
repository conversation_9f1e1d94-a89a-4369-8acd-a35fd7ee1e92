package com.sankuai.shangou.logistics.delivery;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.elasticsearch.rest.RestClientAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Profile;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR>
 * @since 2023/7/5 15:56
 **/
@SpringBootApplication(exclude = {
        RestClientAutoConfiguration.class,
        RabbitAutoConfiguration.class,
        DataSourceAutoConfiguration.class
})
@Profile("test")
@ComponentScan(basePackages ="com.sankuai.shangou.logistics")
@PropertySource("classpath:META-INF/app.properties")
public class TestStartApp {
}