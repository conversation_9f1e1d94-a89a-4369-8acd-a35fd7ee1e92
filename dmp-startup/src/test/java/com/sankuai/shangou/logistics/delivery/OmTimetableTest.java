package com.sankuai.shangou.logistics.delivery;

import com.sankuai.shangou.logistics.delivery.indicator.service.OmTimetable;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;


public class OmTimetableTest {
    @Test
    public void threeSceneExamples() {
        //模拟三类客户端调用场景
        LocalTime startTime = LocalTime.of(8, 0);
        LocalTime openTime = LocalTime.of(10, 0);
        LocalTime closeTime = LocalTime.of(4, 0);
        OmTimetable omTimetable = OmTimetable.getInstance(startTime, openTime, closeTime);

        List<LocalDateTime> points;
        //用户选择日期组件的日期时间
        LocalDate paraDate = LocalDate.of(2023, 7, 13);
        LocalDate nowDay = omTimetable.getBizDay(null);
        if (paraDate.isBefore(nowDay)) {
            points = omTimetable.getHistoryPoints(paraDate, 15, 5);
            System.out.println("历史数据");
            points.forEach(p -> System.out.println(p.toString()));
        } else {
            points = omTimetable.getCurrentPoints(null, 15, 5);
            System.out.println("当日全量数据");
            points.forEach(p -> System.out.println(p.toString()));
        }

        System.out.println("当日增量数据");
        //浏览器上一次有数据的时间
        LocalTime lastPoint = LocalTime.of(10,55);
        points = omTimetable.getCurrentPoints(null, 10, 5);
        points.forEach(p -> System.out.println(p.toString()));
    }

    @Test
    public void getCurrentPoints() {
        LocalTime startTime = LocalTime.of(6, 0);
        LocalTime openTime = LocalTime.of(9, 29);
        LocalTime closeTime = LocalTime.of(2, 3);
        OmTimetable omTimetable = OmTimetable.getInstance(startTime, openTime, closeTime);
        //开业时间段内
        LocalTime clientTime = LocalTime.of(8, 44);
        List<LocalDateTime> points = omTimetable.getCurrentPoints(clientTime, 15, 5);
        points.forEach(p -> System.out.println(p.toString()));
    }

    @Test
    public void getHistoryPoints() {
        LocalTime startTime = LocalTime.of(6, 0);
        LocalTime openTime = LocalTime.of(9, 29);
        LocalTime closeTime = LocalTime.of(2, 3);
        OmTimetable omTimetable = OmTimetable.getInstance(startTime, openTime, closeTime);

        LocalDate clientDate = LocalDate.of(2023, 6, 24);
        List<LocalDateTime> historyPoints = omTimetable.getHistoryPoints(clientDate, 15, 5);
        Assert.assertEquals(omTimetable.getPointSize(15), historyPoints.size());

        List<LocalDateTime> currentPoints = omTimetable.getHistoryPoints(LocalDate.now(), 15, 5);
        Assert.assertEquals(0, currentPoints.size());

        List<LocalDateTime> futurePoints = omTimetable.getHistoryPoints(LocalDate.now().plusDays(1), 15, 5);
        Assert.assertEquals(0, futurePoints.size());

        List<LocalDateTime> futurePoints1 = omTimetable.getHistoryPoints(LocalDate.now().plusDays(1), 15, 5);
        Assert.assertEquals(0, futurePoints1.size());
    }
}
