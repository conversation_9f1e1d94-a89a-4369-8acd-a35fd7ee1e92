package com.sankuai.shangou.logistics.delivery.indicator;

import com.dianping.squirrel.client.impl.redis.api.RedisInnerCommands;
import com.google.common.collect.Lists;
import com.sankuai.shangou.logistics.delivery.TestStartApp;
import com.sankuai.shangou.logistics.delivery.indicator.repository.BaseIndicatorDataSquirrelRepository;
import com.sankuai.shangou.logistics.delivery.indicator.repository.BaseIndicatorEagleRepository;
import com.sankuai.shangou.logistics.delivery.indicator.service.IndicatorService;
import com.sankuai.shangou.logistics.delivery.indicator.service.wrapper.RiderQueryServiceWrapper;
import com.sankuai.shangou.logistics.delivery.indicator.service.wrapper.ScheduleShiftServiceWrapper;
import com.sankuai.shangou.logistics.delivery.indicator.service.wrapper.UserServiceWrapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Component;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023-07-05
 * @email <EMAIL>
 */
@RunWith(SpringRunner.class)
@ActiveProfiles("test")
public class IndicatorServiceTest {

    @Resource
    @InjectMocks
    private IndicatorService indicatorService;
    @Mock
    private BaseIndicatorDataSquirrelRepository baseIndicatorDataSquirrelRepository;
    @Mock
    private RiderQueryServiceWrapper riderQueryServiceWrapper;
    @Mock
    private ScheduleShiftServiceWrapper scheduleShiftServiceWrapper;
    @Mock
    private UserServiceWrapper userServiceWrapper;
    @Mock
    private BaseIndicatorEagleRepository baseIndicatorEagleRepository;

    @Before
    public void setUp() {
        when(baseIndicatorDataSquirrelRepository.scanBizTodayBaseIndicatorData(anyLong(), any())).thenReturn(
                Lists.newArrayList(
                        new RedisInnerCommands.FinalStoreKey(
                                "labor_attendance_tracking.49867245_2023-07-06_10042278_407_11:00_23:00_0_0"
                        ),
                        new RedisInnerCommands.FinalStoreKey(
                                "labor_attendance_tracking.49867245_2023-07-06_10038370_407_11:00_23:00_1_0"
                        )
                )
        );

        when(riderQueryServiceWrapper.queryDeliveryOrderByPoiAndStatusList(anyLong(), anyList(), anyList()))
                .thenReturn(Lists.newArrayList());

        when(scheduleShiftServiceWrapper.queryScheduleShiftListByIdList(anyList(), any())).thenReturn(Lists.newArrayList());

        doNothing().when(baseIndicatorEagleRepository).batchUpsert(any());
    }

    @Test
    public void syncIndicatorTest() {
        indicatorService.syncIndicator(1001197L, LocalDateTime.now(), LocalDateTime.now(), Lists.newArrayList(49867245L));
    }

}
