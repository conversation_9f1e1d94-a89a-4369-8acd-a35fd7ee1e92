package com.sankuai.shangou.logistics.delivery;

import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.monitoring.vo.QueryShopRiderAccountListResponse;
import com.sankuai.shangou.logistics.delivery.monitoring.vo.RiderAccountInfoVO;
import com.sankuai.shangou.logistics.delivery.monitoring.wrapper.RiderLimitAcceptOrderWrapper;
import com.sankuai.shangou.logistics.delivery.monitoring.wrapper.RiderManageClient;
import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class RiderLimitAcceptOrderInfoTest  {

    @InjectMocks
    private RiderManageClient riderManageClient;

    @Mock
    private RiderLimitAcceptOrderWrapper riderLimitAcceptOrderWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ConfigUtilAdapter.init();
    }

    /**
     * 测试当租户ID不是DH租户时，不进行任何处理
     */
    @Test
    public void testAppendLimitInfoResultWithNonDHTenant() {
        // arrange
        Long tenantId = 1L;
        QueryShopRiderAccountListResponse response = new QueryShopRiderAccountListResponse(null);

//        when(MccConfigUtils.isDhTenant(tenantId)).thenReturn(false);
        // act
        riderManageClient.appendLimitInfoResult(tenantId, response);

        // assert
        assertTrue("应当不处理非DH租户", response.getRiderAccountList() == null || response.getRiderAccountList().isEmpty());
    }

    /**
     * 测试当响应对象为空或骑手账号列表为空时，不进行任何处理
     */
    @Test
    public void testAppendLimitInfoResultWithEmptyResponseOrRiderAccountList() {
        // arrange
        Long tenantId = 1L;
        QueryShopRiderAccountListResponse response = new QueryShopRiderAccountListResponse(null);

        // act
        riderManageClient.appendLimitInfoResult(tenantId, response);

        // assert
        assertTrue("当响应对象为空或骑手账号列表为空时，不进行任何处理", response.getRiderAccountList() == null || response.getRiderAccountList().isEmpty());
    }

    /**
     * 测试正常情况下，能够正确处理骑手限制接单详情
     */
    @Test
    public void testAppendLimitInfoResultWithNormalCase() {
        // arrange
        Long tenantId = 1000395L;
        RiderAccountInfoVO riderAccountInfoVO = new RiderAccountInfoVO(1001L, "Test Rider", "**********", false, true, null);
        QueryShopRiderAccountListResponse response = new QueryShopRiderAccountListResponse(Arrays.asList(riderAccountInfoVO));

//        when(MccConfigUtils.isDhTenant(tenantId)).thenReturn(true);

        Map<Long, List<LimitItemDTO>> limitMap = new HashMap<>();
        limitMap.put(1001L, Arrays.asList(new LimitItemDTO()));
        TResult<Map<Long, List<LimitItemDTO>>> tResult = new TResult<>();
        tResult.setData(limitMap);
        tResult.setCode(0);

        when(riderLimitAcceptOrderWrapper.batchQueryRiderLimitAcceptOrderInfo(eq(tenantId), any(List.class),null)).thenReturn(limitMap);

        // act
        riderManageClient.appendLimitInfoResult(tenantId, response);

        // assert
        assertNotNull("骑手限制接单详情应当被正确处理", response.getRiderAccountList().get(0).getLimitItemList());
        assertFalse("骑手限制接单详情列表不应为空", response.getRiderAccountList().get(0).getLimitItemList().isEmpty());
    }

    /**
     * 测试当riderLimitAcceptOrderWrapper.batchQueryRiderLimitAcceptOrderInfo返回的结果为空时，不进行任何处理
     */
    @Test
    public void testAppendLimitInfoResultWithNullServiceResponse() {
        // arrange
        Long tenantId = 1000395L;
        RiderAccountInfoVO riderAccountInfoVO = new RiderAccountInfoVO(1001L, "Test Rider", "**********", false, true, null);
        QueryShopRiderAccountListResponse response = new QueryShopRiderAccountListResponse(Collections.singletonList(riderAccountInfoVO));

        when(riderLimitAcceptOrderWrapper.batchQueryRiderLimitAcceptOrderInfo(eq(tenantId), anyList(),null)).thenReturn(Collections.emptyMap());

        // act
        riderManageClient.appendLimitInfoResult(tenantId, response);

        // assert
        assertTrue("LimitAcceptOrderThriftService返回结果为空时，不应进行处理", CollectionUtils.isEmpty(response.getRiderAccountList().get(0).getLimitItemList()));
    }
}
