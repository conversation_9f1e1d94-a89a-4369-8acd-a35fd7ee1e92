package com.sankuai.shangou.logistics.delivery.indicator;

import com.sankuai.shangou.logistics.delivery.TestStartApp;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BizIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorDTO;
import com.sankuai.shangou.logistics.delivery.indicator.service.IndicatorReader;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/7/5 15:15
 **/

@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestStartApp.class)
@ActiveProfiles("test")
public class IndicatorReaderTest {
    @Resource
    private IndicatorReader indicatorReader;

    @Test
    public void testQueryIndicator() {
        List<LocalDateTime> bizTimeList = Arrays.asList(LocalDateTime.parse("2023-07-06 11:56:29.460", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")));
        Map<LocalDateTime, List<IndicatorDTO>> localDateTimeListMap = indicatorReader.readBizIndicator(49867245L, bizTimeList, Arrays.asList(BizIndicatorEnum.DELIVERED_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT));
        System.out.println(localDateTimeListMap);
    }
}
