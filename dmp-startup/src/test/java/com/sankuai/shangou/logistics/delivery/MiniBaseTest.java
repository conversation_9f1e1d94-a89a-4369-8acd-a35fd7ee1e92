package com.sankuai.shangou.logistics.delivery;

import org.junit.runner.RunWith;
import org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;

/**
 * <AUTHOR>
 * @date 2023-08-09
 * @email <EMAIL>
 */
@RunWith(SpringRunner.class)
// 基于 Junit4 需要添加 MockitoTestExecutionListener.class,DependencyInjectionTestExecutionListener.class 来实现 mock 和依赖注入
@TestExecutionListeners({MockitoTestExecutionListener.class, DependencyInjectionTestExecutionListener.class})
public class MiniBaseTest {
}
