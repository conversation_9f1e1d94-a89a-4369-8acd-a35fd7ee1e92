package com.sankuai.shangou.logistics.delivery;

import com.dianping.rhino.spring.RhinoConfiguration;
import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.elasticsearch.rest.RestClientAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Slf4j
@SpringBootApplication(exclude = {
        RestClientAutoConfiguration.class,
        RabbitAutoConfiguration.class,
        DataSourceAutoConfiguration.class,
        JdbcTemplateAutoConfiguration.class,
        DataSourceTransactionManagerAutoConfiguration.class,
        MybatisAutoConfiguration.class
})
@ComponentScan(basePackages = {"com.sankuai.shangou.logistics", "com.sankuai.shangou.commons.auth.login.web", "com.sankuai.shangou.commons.thrift", "com.sankuai.shangou.commons.utils.log", "com.sankuai.shangou.commons.auth.login.app"})
@ImportAutoConfiguration()
@RhinoConfiguration
@PropertySource("classpath:META-INF/app.properties")
@ImportResource({"classpath*:operation-thrift.xml", "classpath*:app-login-thrift-client.xml", "classpath*:applicationContext.xml"})
@SpringBootConfiguration
@EnableTransactionManagement
@EnableRetry
public class StartApp {
    public static void main(String[] args) {
        SpringApplication.run(StartApp.class, args);
        log.info("\n" +
                "███████╗████████╗ █████╗ ██████╗ ████████╗███████╗██████╗     ██╗\n" +
                "██╔════╝╚══██╔══╝██╔══██╗██╔══██╗╚══██╔══╝██╔════╝██╔══██╗    ██║\n" +
                "███████╗   ██║   ███████║██████╔╝   ██║   █████╗  ██║  ██║    ██║\n" +
                "╚════██║   ██║   ██╔══██║██╔══██╗   ██║   ██╔══╝  ██║  ██║    ╚═╝\n" +
                "███████║   ██║   ██║  ██║██║  ██║   ██║   ███████╗██████╔╝    ██╗\n" +
                "╚══════╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝   ╚══════╝╚═════╝     ╚═╝");
    }
}