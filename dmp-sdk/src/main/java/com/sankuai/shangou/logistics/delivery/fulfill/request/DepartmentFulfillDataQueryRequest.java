package com.sankuai.shangou.logistics.delivery.fulfill.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.netty.util.internal.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/4/10 15:18
 **/
@ThriftStruct
public class DepartmentFulfillDataQueryRequest {
    @FieldDoc(description = "门店id列表")
    private List<Long> storeIds;

    @FieldDoc(description = "租户id")
    private Long tenantId;

    @FieldDoc(description = "组织层级")
    private String departmentLevel;

    @FieldDoc(description = "页码")
    private Integer pageNo;

    @FieldDoc(description = "页大小")
    private Integer pageSize;


    public Optional<String> validate() {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Optional.of("未指定门店ID");
        }
        if (storeIds.size() > 1000) {
            return Optional.of("选择门店数量超出限制");
        }
        for (Long storeId : storeIds) {
            if (storeId == null || storeId <= 0) {
                return Optional.of("存在无效门店 ID");
            }
        }

        if (StringUtils.isBlank(departmentLevel)) {
            return Optional.of("组织层级为空");
        }

        if (getPageNo() == null || getPageNo() < 1) {
            return Optional.of("页码错误");
        }
        if (getPageSize() == null || getPageSize() < 1 || pageSize > 100) {
            return Optional.of("页大小错误");
        }

        return Optional.empty();
    }


    @ThriftField(1)
    public List<Long> getStoreIds() {
        return this.storeIds;
    }

    @ThriftField
    public void setStoreIds(List<Long> storeIds) {
        this.storeIds = storeIds;
    }

    @ThriftField(2)
    public Long getTenantId() {
        return this.tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(3)
    public String getDepartmentLevel() {
        return this.departmentLevel;
    }

    @ThriftField
    public void setDepartmentLevel(String departmentLevel) {
        this.departmentLevel = departmentLevel;
    }


    @ThriftField(4)
    public Integer getPageNo() {
        return this.pageNo;
    }

    @ThriftField
    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    @ThriftField(5)
    public Integer getPageSize() {
        return this.pageSize;
    }

    @ThriftField
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
