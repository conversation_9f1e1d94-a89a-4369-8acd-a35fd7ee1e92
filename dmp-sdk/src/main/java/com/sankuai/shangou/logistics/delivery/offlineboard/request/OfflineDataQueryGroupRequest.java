package com.sankuai.shangou.logistics.delivery.offlineboard.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023/2/15 15:32
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@TypeDoc(
        description = "员工维度数据查询请求"
)
public class OfflineDataQueryGroupRequest extends OfflineDataQueryBaseRequest {

    @FieldDoc(description = "聚合类型:2-二级维度 3-三级维度")
    private Integer groupColumnType;

    private String groupColumn;

    private String groupColumnName;
}
