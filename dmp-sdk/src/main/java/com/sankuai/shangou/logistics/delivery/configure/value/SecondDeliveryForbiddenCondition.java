package com.sankuai.shangou.logistics.delivery.configure.value;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-16
 * @email <EMAIL>
 */
@Data
public class SecondDeliveryForbiddenCondition {

    @FieldDoc(description = "订单标签")
    private List<Integer> orderTags;

    @FieldDoc(description = "订单实际支付金额")
    private String orderActualPayment;

}
