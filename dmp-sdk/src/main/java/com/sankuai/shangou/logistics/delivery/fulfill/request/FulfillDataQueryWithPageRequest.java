package com.sankuai.shangou.logistics.delivery.fulfill.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.assertj.core.util.Lists;

import java.io.Serializable;
import java.util.Optional;

@EqualsAndHashCode(callSuper = true)
@Data
public class FulfillDataQueryWithPageRequest extends FulfillDataQueryRequest implements Serializable {
    @FieldDoc(description = "页码")
    private Integer pageNo = 1;

    @FieldDoc(description = "分页大小,默认storeId长度,最大不能超过50")
    private Integer pageSize;
    @Override
    public Optional<String> validate() {
        if (pageSize == null) {
            pageSize = Optional.ofNullable(getStoreIds()).orElse(Lists.emptyList()).size();
        }
        if (pageSize > 50) {
            return Optional.of("单页最大不能超过50");
        }
        return super.validate();
    }
}
