package com.sankuai.shangou.logistics.delivery.fulfill.enums;

/**
 * <AUTHOR>
 * @Date 2022/7/27
 **/
public enum IndicatorTypeEnum {
    VALID_ORDER_COUNT(1, "有效订单量"),
    DELIVERER_ORDER_COUNT(2, "已送达订单量"),
    WORDED_EMPLOYEE_COUNT(3, "当日出勤人数"),
    NINETIETH_FULFILL_DURATION(4, "九分位履约时长"),
    AVG_FULFILL_DURATION(5, "整单履约时长"),
    SERIOUS_TIMEOUT_ORDER_COUNT(6, "严重超时订单量"),
    JUDGE_TIMEOUT_ORDER_COUNT(7, "考核超时订单量"),
    DELIVERY_RATE_IN_25_MIN(8, "25分钟送达率"),
    DELIVERY_RATE_IN_15_MIN(9, "15分钟送达率"),
    ETA_TIMEOUT_ORDER_COUNT(10, "ETA超时订单量"),
    ETA_BAD_TIMEOUT_ORDER_COUNT(11, "ETA严重超时订单量");

    private int value;
    private String desc;

    IndicatorTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static IndicatorTypeEnum enumOf(int value) {

        for (IndicatorTypeEnum typeEnum : values()) {
            if (value == typeEnum.getValue()) {
                return typeEnum;
            }
        }

        return null;
    }

}
