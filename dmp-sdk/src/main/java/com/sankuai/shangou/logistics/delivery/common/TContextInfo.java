package com.sankuai.shangou.logistics.delivery.common;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @email <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TContextInfo {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long poiId;

    @FieldDoc(
            description = "操作人员工id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Long employeeId;

    @FieldDoc(
            description = "操作人姓名",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public String employeeName;

    @FieldDoc(
            description = "账号id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public Long accountId;

    @FieldDoc(
            description = "账号名",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    public Long accountName;
}
