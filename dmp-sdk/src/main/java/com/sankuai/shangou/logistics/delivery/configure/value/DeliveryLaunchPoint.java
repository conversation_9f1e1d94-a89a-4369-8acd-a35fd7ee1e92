package com.sankuai.shangou.logistics.delivery.configure.value;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.BookingOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import lombok.*;

/**
 * 配送发起时间点
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/24
 */
@Getter
@ToString
@AllArgsConstructor
public class DeliveryLaunchPoint {

	/**
	 * 即时单-配送发起时间点配置
	 */
	@Setter
	private ImmediateOrderDeliveryLaunchPointConfig immediateOrderDeliveryLaunchPointConfig;

	/**
	 * 预约单-配送发起时间点配置
	 */
	@Setter
	private BookingOrderDeliveryLaunchPointConfig bookingOrderDeliveryLaunchPointConfig;

	public DeliveryLaunchPoint() {
		this.immediateOrderDeliveryLaunchPointConfig = new ImmediateOrderDeliveryLaunchPointConfig();
		this.bookingOrderDeliveryLaunchPointConfig = new BookingOrderDeliveryLaunchPointConfig();
	}

	/**
	 * 预订单-配送发起时间点配置
	 */
	@Data
	@ToString
	@AllArgsConstructor
	public static class BookingOrderDeliveryLaunchPointConfig {
		/**
		 * 触发配送时间点：预计送达前X分钟/拣货完成后X分钟
		 */
		private final BookingOrderDeliveryLaunchPointEnum launchPoint;

		/**
		 * 配送时间配置(分钟)，配合预约单配送触发类型，组成预约单的发起配送时间点，如：
		 * 预计送达前minutes分钟发起配送 or 拣货完成后minutes分钟发起配送
		 */
		private Integer configMinutes;

		/**
		 * 默认预计送达前60分钟发起配送
		 */
		public BookingOrderDeliveryLaunchPointConfig() {
			this.launchPoint = BookingOrderDeliveryLaunchPointEnum.BEFORE_DELIVERY;
			this.configMinutes = 60;
		}
	}

	/**
	 * 即时订单-配送发起时间点配置
	 */
	@Getter
	@Setter
	@ToString
	@AllArgsConstructor
	public static class ImmediateOrderDeliveryLaunchPointConfig {

		/**
		 * 触发配送时间点：商家接单/拣货完成
		 */
		private ImmediateOrderDeliveryLaunchPointEnum launchPoint;

		/**
		 * 配送发起延时分钟数
		 */
		private Integer delayMinutes;

		/**
		 * 默认商家接单后立即发起配送
		 */
		public ImmediateOrderDeliveryLaunchPointConfig() {
			this.launchPoint = ImmediateOrderDeliveryLaunchPointEnum.MERCHANT_ACCEPT;
			this.delayMinutes = 0;
		}
	}
}
