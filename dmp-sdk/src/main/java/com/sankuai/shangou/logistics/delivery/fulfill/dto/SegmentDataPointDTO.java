package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/7/27
 **/
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "分段数据点信息")
public class SegmentDataPointDTO implements Serializable {
    @FieldDoc(description = "时刻, 例如:22:15")
    private String moment;

    @FieldDoc(description = "数据值")
    private String value;


    @ThriftField(1)
    public String getMoment() {
        return this.moment;
    }

    @ThriftField
    public void setMoment(String moment) {
        this.moment = moment;
    }

    @ThriftField(2)
    public String getValue() {
        return this.value;
    }

    @ThriftField
    public void setValue(String value) {
        this.value = value;
    }
}
