package com.sankuai.shangou.logistics.delivery.configure.value;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-16
 * @email <EMAIL>
 */
@Data
public class BookingPushDownTimeConfig {

    @FieldDoc(description = "订单标签")
    private List<Integer> orderTags;

    @FieldDoc(description = "条件")
    private List<DeliveryConfigDetailVO.ConditionVO> condition;

    @FieldDoc(description = "公式")
    private String formula;

}
