package com.sankuai.shangou.logistics.delivery.configure;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 门店配置保存请求VO
 *
 * <AUTHOR>
 * @date 2025-07-15
 * @email <EMAIL>
 */
@Data
@AllArgsConstructor
public class DeliverySupportOrderTagResponse {

    //key:查询场景,1-转辅配方式 2-自动呼叫时间 3-剩余时长; value:订单tag列表,
    private Map<Integer, List<Integer>> sceneOrderTagMap;

}
