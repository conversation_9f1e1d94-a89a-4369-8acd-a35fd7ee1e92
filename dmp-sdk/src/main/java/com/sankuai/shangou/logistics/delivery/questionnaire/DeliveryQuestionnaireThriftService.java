package com.sankuai.shangou.logistics.delivery.questionnaire;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.questionnaire.dto.DeliveryQuestionnaireDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/15 15:55
 **/
@ThriftService
public interface DeliveryQuestionnaireThriftService {
    /**
     * 通过配送单id查询问卷信息
     * @param deliveryOrderIds 运单id
     * @return 问卷信息
     */
    @ThriftMethod
    TResult<List<DeliveryQuestionnaireDTO>> queryQuestionnaireByDeliveryOrderIds(@ThriftField(1) List<Long> deliveryOrderIds);
}
