package com.sankuai.shangou.logistics.delivery.fulfill.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.logistics.delivery.fulfill.enums.IndicatorTypeEnum;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2022/7/27
 **/
@ThriftStruct
@TypeDoc(description = "履约数据分时段趋势图查询请求")
public class SegmentationFulfillDataQueryRequest {

    @FieldDoc(description = "对比类型：1-日环比；2-周同比；3-月同比")
    private Integer compareType = 1;

    @FieldDoc(description = "门店id列表")
    private List<Long> storeIds;

    @FieldDoc(description = "租户id")
    private Long tenantId;

    @FieldDoc(description = "指标类型")
    private Integer indicatorType;


    public Optional<String> validate() {
        if (compareType == null) {
            return Optional.of("未指定对比类型");
        }
        if (CollectionUtils.isEmpty(storeIds)) {
            return Optional.of("未指定门店ID");
        }
        if (storeIds.size() > 1000) {
            return Optional.of("选择门店数量超出限制");
        }
        for (Long storeId : storeIds) {
            if (storeId == null || storeId <= 0) {
                return Optional.of("存在无效门店 ID");
            }
        }

        if (indicatorType == null || IndicatorTypeEnum.enumOf(indicatorType) == null) {
            return Optional.of("指标类型不合法");
        }
        return Optional.empty();
    }


    @ThriftField(1)
    public Integer getCompareType() {
        return this.compareType;
    }

    @ThriftField
    public void setCompareType(Integer compareType) {
        this.compareType = compareType;
    }

    @ThriftField(2)
    public List<Long> getStoreIds() {
        return this.storeIds;
    }

    @ThriftField
    public void setStoreIds(List<Long> storeIds) {
        this.storeIds = storeIds;
    }

    @ThriftField(3)
    public Long getTenantId() {
        return this.tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(4)
    public Integer getIndicatorType() {
        return this.indicatorType;
    }

    @ThriftField
    public void setIndicatorType(Integer indicatorType) {
        this.indicatorType = indicatorType;
    }
}
