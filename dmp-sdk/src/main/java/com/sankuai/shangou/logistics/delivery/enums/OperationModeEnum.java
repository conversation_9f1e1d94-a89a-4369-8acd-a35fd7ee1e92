package com.sankuai.shangou.logistics.delivery.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/4/19 17:09
 **/
public enum OperationModeEnum {
    DIRECT_SALE(1, "直营"),
    ALIGN(2, "加盟");
    private int type;

    private String desc;

    OperationModeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static OperationModeEnum enumOf(Integer type) {
        for (OperationModeEnum val : OperationModeEnum.values()) {
            if (Objects.equals(val.getType(), type)) {
                return val;
            }
        }

        throw new  IllegalArgumentException("经营模式不合法");
    }
}
