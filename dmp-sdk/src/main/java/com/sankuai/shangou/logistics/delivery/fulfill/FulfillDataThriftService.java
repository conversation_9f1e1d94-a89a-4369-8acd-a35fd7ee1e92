package com.sankuai.shangou.logistics.delivery.fulfill;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.response.TPageResult;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.fulfill.dto.*;
import com.sankuai.shangou.logistics.delivery.fulfill.request.DepartmentFulfillDataQueryRequest;
import com.sankuai.shangou.logistics.delivery.fulfill.request.PageFulfillDataQueryRequest;

import java.util.List;

@InterfaceDoc(
        displayName = "配送统计数据查询接口",
        type = "octo.thrift.annotation",
        scenarios = "配送统计数据查询接口",
        description = "配送统计数据查询接口",
        authors = {
                "xiaopengfei06"
        }
)
@ThriftService
public interface FulfillDataThriftService {
    @ThriftMethod
    @MethodDoc(
            displayName = "查询门店+承运商维度三方运力数据",
            description = "查询门店+承运商维度三方运力数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询自配门店配置",
                            type = FulfillDataPageQueryDTO.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "门店+承运商维度三方运力数据",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    TPageResult<FulfillCarrierStatisticsDTO> queryCarrierStatistic(FulfillDataPageQueryDTO request);

    @ThriftMethod
    @MethodDoc(
            displayName = "查询单门店骑手维度运力数据",
            description = "查询单门店骑手维度运力数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询单门店骑手维度运力数据",
                            type = FulfillDataPageQueryDTO.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询单门店骑手维度运力数据",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    TPageResult<FulfillRiderStatisticsDTO> queryRiderStatistic(PageFulfillDataQueryRequest request);

    @ThriftMethod
    @MethodDoc(
            displayName = "查询多门店门店维度运力数据",
            description = "查询多门店门店维度运力数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询多门店门店维度运力数据",
                            type = FulfillDataPageQueryDTO.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询多门店门店维度运力数据",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    TPageResult<FulfillStoreStatisticsDTO> queryStoreStatistic(PageFulfillDataQueryRequest request);


    @ThriftMethod
    @MethodDoc(
            displayName = "查询组织节点维度运力数据",
            description = "查询组织节点维度运力数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询组织节点维度运力数据",
                            type = FulfillDataPageQueryDTO.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询组织节点维度运力数据",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    TPageResult<DepartmentFulfillDataDTO> queryDepartmentLevelStatistic(DepartmentFulfillDataQueryRequest request);

    @ThriftMethod
    @MethodDoc(
            displayName = "查询异常单数据",
            description = "查询异常单数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询异常单数据",
                            type = FulfillDataPageQueryDTO.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询异常单数据",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    TPageResult<AbnormalOrderDTO> queryAbnormalOrderList(PageFulfillDataQueryRequest request);
}
