package com.sankuai.shangou.logistics.delivery.alert.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.logistics.delivery.alert.enums.DeliveryAlertCallbackEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 配送告警回调请求
 * @date 2025-05-15
 */
@Data
@ThriftStruct
public class DeliveryAlertCallbackRequest {

    /**
     * @see DeliveryAlertCallbackEnum
     */
    @FieldDoc(description = "回调类型")
    @ThriftField(1)
    private String type;

    @FieldDoc(description = "告警状态：[OK：代表告警恢复，PROBLEM：代表触发告警]")
    @ThriftField(2)
    private String status;

    @FieldDoc(description = "告警唯一表示")
    @ThriftField(3)
    private String eventId;


}
