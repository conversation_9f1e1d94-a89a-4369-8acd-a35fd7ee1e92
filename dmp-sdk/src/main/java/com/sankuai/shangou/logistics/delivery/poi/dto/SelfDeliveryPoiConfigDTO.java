package com.sankuai.shangou.logistics.delivery.poi.dto;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.logistics.delivery.common.TOperator;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @email <EMAIL>
 */
@Data
public class SelfDeliveryPoiConfigDTO {

    @FieldDoc(
            description = "id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long id;

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long tenantId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Long poiId;

    @FieldDoc(
            description = "是否支持转青云",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Integer enableTurnDelivery;

    @FieldDoc(
            description = "操作人信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public TOperator tOperator;

    @FieldDoc(
            description = "是否支持拣配分离",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    public Integer enablePickDeliverySplit;

}
