package com.sankuai.shangou.logistics.delivery.offlineboard;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.offlineboard.request.OfflineDataQueryBaseRequest;
import com.sankuai.shangou.logistics.delivery.offlineboard.request.OfflineDataQueryGroupRequest;
import com.sankuai.shangou.logistics.delivery.offlineboard.response.DynamicExportOfflineBoardDataResponse;

/**
 * <AUTHOR>
 * @date 2024-06-04
 * @email <EMAIL>
 */
@InterfaceDoc(
        displayName = "离线配送看板服务",
        type = "octo.thrift.annotation",
        scenarios = "离线配送看板服务",
        description = "离线配送看板服务",
        authors = {
                "jianglilin02"
        }
)
@ThriftService
public interface OfflineBoardThriftService {

    @MethodDoc(
            displayName = "导出离线数据-+1组织维度",
            description = "导出离线数据-+1组织维度",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "导出离线数据-+1组织维度",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "导出离线数据-+1组织维度返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<DynamicExportOfflineBoardDataResponse> exportOneOrgOfflineBoardData(OfflineDataQueryGroupRequest request);

    @MethodDoc(
            displayName = "导出离线数据-+2组织维度",
            description = "导出离线数据-+2组织维度",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "导出离线数据-+2组织维度",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "导出离线数据-+2组织维度返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<DynamicExportOfflineBoardDataResponse> exportTwoOrgOfflineBoardData(OfflineDataQueryGroupRequest request);

    @MethodDoc(
            displayName = "导出离线数据-门店维度",
            description = "导出离线数据-门店维度",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "导出离线数据-门店维度",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "导出离线数据-门店维度",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<DynamicExportOfflineBoardDataResponse> exportStoreOfflineBoardData(OfflineDataQueryBaseRequest request);


    @MethodDoc(
            displayName = "导出离线数据-骑手维度",
            description = "导出离线数据-骑手维度",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "导出离线数据-骑手维度",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "导出离线数据-骑手维度",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<DynamicExportOfflineBoardDataResponse> exportRiderOfflineBoardData(OfflineDataQueryBaseRequest request);

    @MethodDoc(
            displayName = "导出离线数据-异常维度",
            description = "导出离线数据-异常维度",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "导出离线数据-异常维度",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "导出离线数据-异常维度返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<DynamicExportOfflineBoardDataResponse> exportAbnormalOfflineBoardData(OfflineDataQueryBaseRequest request);

    @MethodDoc(
            displayName = "导出离线数据-三方承运商维度",
            description = "导出离线数据-三方承运商维度",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "导出离线数据-三方承运商维度",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "导出离线数据-三方承运商维度返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<DynamicExportOfflineBoardDataResponse> exportDeliveryChannelOfflineBoardData(OfflineDataQueryBaseRequest request);

}
