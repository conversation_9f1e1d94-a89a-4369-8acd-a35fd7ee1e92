package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@ThriftStruct
@TypeDoc(
        description = "运单渠道信息", authors = {"yujing10"}
)
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryOrderChannelInfo implements Serializable {

    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道日流水号"
    )
    private Integer daySeq;


    @ThriftField(1)
    public Integer getChannelId() {
        return this.channelId;
    }

    @ThriftField
    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    @ThriftField(2)
    public Integer getDaySeq() {
        return this.daySeq;
    }

    @ThriftField
    public void setDaySeq(Integer daySeq) {
        this.daySeq = daySeq;
    }
}
