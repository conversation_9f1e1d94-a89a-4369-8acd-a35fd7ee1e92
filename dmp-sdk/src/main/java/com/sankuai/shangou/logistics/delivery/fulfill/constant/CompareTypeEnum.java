package com.sankuai.shangou.logistics.delivery.fulfill.constant;

public enum CompareTypeEnum {
    COMPARE_WITH_YESTERDAY(1,"日同比"),
    COMPARE_WITH_LAST_WEEK_DAY(2,"周同比"),
    COMPARE_WITH_LAST_MONTH_DAY(3,"月同比");

    int value;
    String desc;

    CompareTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static CompareTypeEnum enumOf(int val) {
        for (CompareTypeEnum typeEnum : CompareTypeEnum.values()) {
            if (typeEnum.value == val) {
                return typeEnum;
            }
        }

        return null;
    }

}

