package com.sankuai.shangou.logistics.delivery.verify.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/15 12:01
 **/
@Data
@ThriftStruct
public class VerifyTaskViewAndExportDTO {
    @ThriftField(1)
    public String taskId;

    @ThriftField(2)
    public List<Integer> collectContentList;

    @ThriftField(3)
    public List<String> collectContentDescList;

    @ThriftField(4)
    public Integer collectMode;

    @ThriftField(5)
    public String collectModeDesc;

    @ThriftField(6)
    public Integer status;

    @ThriftField(7)
    public String statusDesc;

    @ThriftField(8)
    public Long pushTime;

    @ThriftField(9)
    public String pushTimeDesc;

    @ThriftField(10)
    public Long completeTime;

    @ThriftField(11)
    public String completeTimeDesc;

    @ThriftField(12)
    public Long expireTime;

    @ThriftField(13)
    public String expireTimeDesc;

    @ThriftField(14)
    public String riderAccountName;

    @ThriftField(15)
    public String riderName;

    @ThriftField(16)
    public String storeName;

    @ThriftField(17)
    public String channelOrderId;

    @ThriftField(18)
    public String callbackCode;

    @ThriftField(19)
    public String callbackMsg;

    @ThriftField(20)
    public Boolean faceIdentifyResult;

    @ThriftField(21)
    public String faceIdentifyResultDesc;

    @ThriftField(22)
    public Boolean helmetIdentifyResult;

    @ThriftField(23)
    public String helmetIdentifyResultDesc;

    @ThriftField(24)
    public Boolean dressingIdentifyResult;

    @ThriftField(25)
    public String dressingIdentifyResultDesc;

    @ThriftField(26)
    public String requestCode;

    @ThriftField(27)
    public PunishInfoDTO faceIdentifyPunishInfo;

    @ThriftField(28)
    public String faceIdentifyPunishInfoDesc;

    @ThriftField(29)
    public PunishInfoDTO helmetIdentifyPunishInfo;

    @ThriftField(30)
    public String helmetIdentifyPunishInfoDesc;

    @ThriftField(31)
    public PunishInfoDTO dressingIdentifyPunishInfo;

    @ThriftField(32)
    public String dressingIdentifyPunishInfoDesc;

    @ThriftField(33)
    public PunishInfoDTO taskExpirePunishInfo;

    @ThriftField(34)
    public String taskExpirePunishInfoDesc;

    @ThriftField(35)
    public String uuid;

    @ThriftField(36)
    public Boolean imageIsExpire;

    @ThriftField(37)
    public String riderNameWithAccountName;
}
