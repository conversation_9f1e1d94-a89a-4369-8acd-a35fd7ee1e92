package com.sankuai.shangou.logistics.delivery.gray.enums;

/**
 * <AUTHOR>
 * @since 2024/5/7 17:25
 **/
public enum GrayKeyEnum {

    SEAL_DELIVERY("seal-delivery"),

    SEAL_DELIVERY_V2("seal-delivery-v2"),

    PICK_DELIVERY_SPLIT("pick-delivery-split"),

    NAVIGATION_COMPLIANCE_LEVEL_SWITCH("navigation-compliance-level-switch"),

    LIMIT_ACCEPT_ORDER("limit-accept-order");
    private String grayKey;

    GrayKeyEnum(String grayKey) {
        this.grayKey = grayKey;
    }

    public String getGrayKey() {
        return grayKey;
    }
}
