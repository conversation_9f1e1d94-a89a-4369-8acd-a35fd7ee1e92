package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

@ThriftStruct
@TypeDoc(
        description = "履约配送数据看板查询响应", authors = {"yujing10"}
)
public class FulfillSummaryRealTimeResp implements Serializable {

    @FieldDoc(description = "待领取订单量")
    private Integer waitToAcceptOrder = 0;

    @FieldDoc(description = "待领取超时订单量")
    private Integer waitToAcceptJudgeTimeoutOrder = 0;

    @FieldDoc(description = "待领取eta超时订单量")
    private Integer waitToAcceptEtaTimeoutOrder = 0;

    @FieldDoc(description = "拣货中（骑手已接单）订单量")
    private Integer pickingOrder = 0;

    @FieldDoc(description = "拣货中（骑手已接单）超时订单量")
    private Integer pickingJudgeTimeoutOrder = 0;

    @FieldDoc(description = "拣货中（骑手已接单）eta超时订单量")
    private Integer pickingEtaTimeoutOrder = 0;

    @FieldDoc(description = "配送中（骑手已取货）订单量")
    private Integer deliveringOrder = 0;

    @FieldDoc(description = "配送中（骑手已取货）超时订单量")
    private Integer deliveringJudgeTimeoutOrder = 0;

    @FieldDoc(description = "配送中（骑手已取货）eta超时订单量")
    private Integer deliveringEtaTimeoutOrder = 0;

    @FieldDoc(description = "履约中人均负载（多门店）")
    private String avgRiderLoad;

    @FieldDoc(description = "超载的门店信息")
    private List<OverLoadStoreInfo> overLoadStoreInfos;

    @FieldDoc(description = "履约中骑手数量")
    private Integer deliveringRider = 0;

    @FieldDoc(description = "空闲中骑手数量")
    private Integer idleRider;


    @ThriftField(1)
    public Integer getWaitToAcceptOrder() {
        return this.waitToAcceptOrder;
    }

    @ThriftField
    public void setWaitToAcceptOrder(Integer waitToAcceptOrder) {
        this.waitToAcceptOrder = waitToAcceptOrder;
    }

    @ThriftField(2)
    public Integer getWaitToAcceptJudgeTimeoutOrder() {
        return this.waitToAcceptJudgeTimeoutOrder;
    }

    @ThriftField
    public void setWaitToAcceptJudgeTimeoutOrder(Integer waitToAcceptJudgeTimeoutOrder) {
        this.waitToAcceptJudgeTimeoutOrder = waitToAcceptJudgeTimeoutOrder;
    }

    @ThriftField(3)
    public Integer getPickingOrder() {
        return this.pickingOrder;
    }

    @ThriftField
    public void setPickingOrder(Integer pickingOrder) {
        this.pickingOrder = pickingOrder;
    }

    @ThriftField(4)
    public Integer getPickingJudgeTimeoutOrder() {
        return this.pickingJudgeTimeoutOrder;
    }

    @ThriftField
    public void setPickingJudgeTimeoutOrder(Integer pickingJudgeTimeoutOrder) {
        this.pickingJudgeTimeoutOrder = pickingJudgeTimeoutOrder;
    }

    @ThriftField(5)
    public Integer getDeliveringOrder() {
        return this.deliveringOrder;
    }

    @ThriftField
    public void setDeliveringOrder(Integer deliveringOrder) {
        this.deliveringOrder = deliveringOrder;
    }

    @ThriftField(6)
    public Integer getDeliveringJudgeTimeoutOrder() {
        return this.deliveringJudgeTimeoutOrder;
    }

    @ThriftField
    public void setDeliveringJudgeTimeoutOrder(Integer deliveringJudgeTimeoutOrder) {
        this.deliveringJudgeTimeoutOrder = deliveringJudgeTimeoutOrder;
    }

    @ThriftField(7)
    public String getAvgRiderLoad() {
        return this.avgRiderLoad;
    }

    @ThriftField
    public void setAvgRiderLoad(String avgRiderLoad) {
        this.avgRiderLoad = avgRiderLoad;
    }

    @ThriftField(8)
    public List<OverLoadStoreInfo> getOverLoadStoreInfos() {
        return this.overLoadStoreInfos;
    }

    @ThriftField
    public void setOverLoadStoreInfos(List<OverLoadStoreInfo> overLoadStoreInfos) {
        this.overLoadStoreInfos = overLoadStoreInfos;
    }

    @ThriftField(9)
    public Integer getDeliveringRider() {
        return this.deliveringRider;
    }

    @ThriftField
    public void setDeliveringRider(Integer deliveringRider) {
        this.deliveringRider = deliveringRider;
    }

    @ThriftField(10)
    public Integer getIdleRider() {
        return this.idleRider;
    }

    @ThriftField
    public void setIdleRider(Integer idleRider) {
        this.idleRider = idleRider;
    }

    @ThriftField(11)
    public Integer getWaitToAcceptEtaTimeoutOrder() {
        return waitToAcceptEtaTimeoutOrder;
    }

    @ThriftField
    public void setWaitToAcceptEtaTimeoutOrder(Integer waitToAcceptEtaTimeoutOrder) {
        this.waitToAcceptEtaTimeoutOrder = waitToAcceptEtaTimeoutOrder;
    }

    @ThriftField(12)
    public Integer getPickingEtaTimeoutOrder() {
        return pickingEtaTimeoutOrder;
    }

    @ThriftField
    public void setPickingEtaTimeoutOrder(Integer pickingEtaTimeoutOrder) {
        this.pickingEtaTimeoutOrder = pickingEtaTimeoutOrder;
    }

    @ThriftField(13)
    public Integer getDeliveringEtaTimeoutOrder() {
        return deliveringEtaTimeoutOrder;
    }

    @ThriftField
    public void setDeliveringEtaTimeoutOrder(Integer deliveringEtaTimeoutOrder) {
        this.deliveringEtaTimeoutOrder = deliveringEtaTimeoutOrder;
    }
}
