package com.sankuai.shangou.logistics.delivery.configure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 自送拣配作业模式枚举
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@Getter
@AllArgsConstructor
public enum SelfDeliveryModeEnum {

    /**
     * 拣配分离
     */
    SEPARATE(1, "拣配分离"),

    /**
     * 拣配一体
     */
    INTEGRATED(2, "拣配一体"),

    /**
     * 拣配一体，支持手动分离
     */
    INTEGRATED_WITH_MANUAL_SEPARATE(3, "拣配一体，支持手动分离");

    /**
     * 模式值
     */
    private final Integer mode;

    /**
     * 模式描述
     */
    private final String description;

    /**
     * 根据模式值获取枚举
     *
     * @param mode 模式值
     * @return 对应的枚举
     * @throws IllegalArgumentException 当模式值不存在时抛出异常
     */
    public static SelfDeliveryModeEnum getByMode(Integer mode) {
        if (mode == null) {
            return null;
        }
        for (SelfDeliveryModeEnum modeEnum : values()) {
            if (modeEnum.getMode().equals(mode)) {
                return modeEnum;
            }
        }
        throw new IllegalArgumentException("未知的自送拣配作业模式: " + mode);
    }
}

