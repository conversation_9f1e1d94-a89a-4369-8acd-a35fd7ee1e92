package com.sankuai.shangou.logistics.delivery.seal.request;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/27 17:03
 **/
@Data
public class QuerySealContainerListReq {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    private Long tenantId;

    @FieldDoc(
            description = "仓id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    private List<Long> warehouseIds;

    @FieldDoc(
            description = "容器编号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    private String sealContainerCode;

    @FieldDoc(
            description = "订单号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    private String tradeOrderNo;

    @FieldDoc(
            description = "分页大小",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    private int pageSize;

    @FieldDoc(
            description = "页码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    private int page;

}
