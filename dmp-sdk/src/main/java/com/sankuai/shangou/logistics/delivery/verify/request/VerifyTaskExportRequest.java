package com.sankuai.shangou.logistics.delivery.verify.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/15 12:02
 **/
@Data
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
public class VerifyTaskExportRequest {
    @ThriftField(1)
    public Long tenantId;

    @ThriftField(2)
    public List<Long> storeIds;

    @ThriftField(3)
    public String accountName;

    @ThriftField(4)
    public Long taskPushTimeBegin;

    @ThriftField(5)
    public Long taskPushTimeEnd;

    @ThriftField(6)
    public List<Integer> statusList;

    @ThriftField(7)
    public Integer page;

    @ThriftField(8)
    public Integer pageSize;

    /**
     * 人脸验证结果 0-否 1-是
     */
    @ThriftField(9)
    public Integer faceIdentifyResult;

    /**
     * 头盔验证结果 0-否 1-是
     */
    @ThriftField(10)
    public Integer helmetIdentifyResult;

    /**
     * 着装验证结果 0-否 1-是
     */
    @ThriftField(11)
    public Integer dressingIdentifyResult;

    /**
     * 任务id
     */
    @ThriftField(12)
    public Long taskId;

    /**
     * 任务id
     */
    @ThriftField(13)
    public Integer appId;

    /**
     * 任务id
     */
    @ThriftField(14)
    public Long accountId;
}
