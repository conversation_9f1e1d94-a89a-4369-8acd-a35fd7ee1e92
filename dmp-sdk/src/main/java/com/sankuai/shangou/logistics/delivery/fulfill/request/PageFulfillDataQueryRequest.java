package com.sankuai.shangou.logistics.delivery.fulfill.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/07/29 15:36
 */
@ThriftStruct
@TypeDoc(
        description = "履约配送数据看板查询请求", authors = {"huangcheng21"}
)
public class PageFulfillDataQueryRequest {
    @FieldDoc(description = "门店id")
    private Long storeId;

    @FieldDoc(description = "门店id列表")
    private List<Long> storeIds;

    @FieldDoc(description = "租户id")
    private Long tenantId;

    @FieldDoc(description = "页码")
    private Integer pageNo;

    @FieldDoc(description = "分页大小,默认为10")
    private Integer pageSize;

    @FieldDoc(description = "appId")
    private Integer appId;

    @FieldDoc(description = "经营类型列表")
    private List<Integer> operationModes;

    public Optional<String> validate() {

        if ((storeId == null || storeId < 1) && CollectionUtils.isEmpty(storeIds)) {
            return Optional.of("未指定门店ID");
        }

        if(tenantId == null || tenantId <= 0L) {
            return Optional.of("租户id不合法");
        }

        if (getPageNo() == null || getPageNo() < 1) {
            return Optional.of("页码错误");
        }
        if (getPageSize() == null || getPageSize() < 1 || pageSize > 100) {
            return Optional.of("页大小错误");
        }

        return Optional.empty();
    }


    @ThriftField(1)
    public Long getStoreId() {
        return this.storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(2)
    public List<Long> getStoreIds() {
        return this.storeIds;
    }

    @ThriftField
    public void setStoreIds(List<Long> storeIds) {
        this.storeIds = storeIds;
    }

    @ThriftField(3)
    public Long getTenantId() {
        return this.tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(4)
    public Integer getPageNo() {
        return this.pageNo;
    }

    @ThriftField
    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    @ThriftField(5)
    public Integer getPageSize() {
        return this.pageSize;
    }

    @ThriftField
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @ThriftField(6)
    public Integer getAppId() {
        return this.appId;
    }

    @ThriftField
    public void setAppId(Integer appId) {
        this.appId = appId;
    }

    @ThriftField(7)
    public void setOperationModes(List<Integer> operationModes) {
        this.operationModes = operationModes;
    }

    @ThriftField
    public List<Integer> getOperationModes() {
        return operationModes;
    }
}
