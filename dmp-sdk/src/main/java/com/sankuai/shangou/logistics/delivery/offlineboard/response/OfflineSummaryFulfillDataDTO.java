package com.sankuai.shangou.logistics.delivery.offlineboard.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/2/15 16:37
 **/

@Data
@TypeDoc(
        description = "总量数据-非实时履约数据指标VO", authors = {"linxiaorui"}
)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OfflineSummaryFulfillDataDTO implements Serializable {
    //总量数据
    @FieldDoc(description = "已送达订单量")
    private Integer deliveredOrderNum;

    @FieldDoc(description = "送达前取消单量")
    private Integer cancelOrderNum;

    @FieldDoc(description = "考核超时订单量")
    private Integer judgeTimeoutOrderNum;

    @FieldDoc(description = "提前点送达订单量")
    private Integer completeBeforeDeliveredOrderNum;

    @FieldDoc(description = "提前点送达率 保留2位小数 单位:%")
    private String completeBeforeDeliveredOrderPercent;

    @FieldDoc(description = "平均配送距离 单位:米")
    private Integer deliveryDistanceAvg;

    //履约质量
    @FieldDoc(description = "整单履约时长 单位:秒")
    private Integer fulfillDurationAvg;

    @FieldDoc(description = "九分位履约时长 单位:秒")
    private Integer ninetiethFulfillDuration;

    @FieldDoc(description = "平均出仓时长 单位:秒")
    private Integer outWarehouseDurationAvg;

    @FieldDoc(description = "25分钟送达率 保留2位小数 单位:%")
    private String deliveredRateIn25min;

    @FieldDoc(description = "15分钟送达率 保留2位小数 单位:%")
    private String deliveredRateIn15min;

    @FieldDoc(description = "45分钟送达率 保留2位小数 单位:%")
    private String deliveredRateIn45min;

    @FieldDoc(description = "配送差评量")
    private Integer badCommentOrderNum;

    @FieldDoc(description = "配送差评率 保留2位小数 单位:%")
    private String badCommentOrderPercent;

    @FieldDoc(description = "配送人效")
    private String deliveryLaborEfficiency;

    @FieldDoc(description = "平均拣货时长(单位：秒)")
    private String pickGoodsDurationAvg;

    @FieldDoc(description = "严重超时率")
    private String badOvertimeOrdPercentage;

    @FieldDoc(description = "eta超时订单率")
    private String etaOvertimeOrdPercentage;

    @FieldDoc(description = "全职人效")
    private String fullTimeEfficiency;

    @FieldDoc(description = "兼职人效")
    private String partTimeEfficiency;

    @FieldDoc(description = "兼职配送占比")
    private String partTimeArrivedOrdPercentage;

    //三方汇总数据
    @FieldDoc(description = "三方总发配送运单量")
    private String threeAllDelieryOrdNum;

    @FieldDoc(description = "三方已送达运单量")
    private String threeArrivedOrdNum;

    @FieldDoc(description = "三方已取消运单量")
    private String threeCancelOrdNum;

    @FieldDoc(description = "三方接单率")
    private String threeHave2PoiOrdPercentage;

    @FieldDoc(description = "三方取消率")
    private String threeCancelOrdPercentage;

    @FieldDoc(description = "三方单均配送成本")
    private String threeDeliverFeeAvg;

    @FieldDoc(description = "三方单均配送时长")
    private String threePeisongDurationAvg;

    @FieldDoc(description = "骑手接单后取消运单量")
    private String threeJiedanCancelOrdNum;

    @FieldDoc(description = "骑手取货后取消运单量")
    private String threeQuhuoCancelOrdNum;

    @FieldDoc(description = "运单状态有过「待到店」的运单量")
    private String threeHave2PoiOrdNum;

    @FieldDoc(description = "运单状态是已送达，配送费字段是deliver_fee")
    private String threeDeliverFee;

    @FieldDoc(description = "配送方式是三方配送，运单状态是已送达，小费金额大于0的累积金额")
    private String threeTipAmount;

    @FieldDoc(description = "有过「待到店」状态的运单单均接单时长，接单时长=状态是「待到店」状态的创建时间-「等待骑手接单」状态的创建时间(分钟)")
    private String threeJieDanDuration;

    @FieldDoc(description = "整单履约时长，分钟")
    private String threePerformanceDuration;

    @FieldDoc(description = "运单维度，配送方式是三方配送的运单，状态是「已送达」运单的运力侧履约时长，运力侧履约时长=「已送达」状态的创建时间-「等待骑手接单」状态的创建时间,整单运力侧履约时长分钟")
    private String threeYunliPerformanceDuration;

    @FieldDoc(description = "配送方式是三方配送的运单，状态是「已送达」运单的单均配送时长，配送时长=已送达」状态的创建时间-「待到店」状态创建时间(分钟)")
    private String threePeisongDuration;

    @FieldDoc(description = "排出预约单的已送达")
    private String threeArrivedOrdNoPreNum;

    @FieldDoc(description = "25分钟送达订单数")
    private String threeTwentyFiveMinArrOrdNum;

    @FieldDoc(description = "45分钟送达订单数")
    private String threeFortyFiveMinArrOrdNum;

    @FieldDoc(description = "25分钟送达率")
    private String threeTwentyFiveMinArrOrdPercentage;

    @FieldDoc(description = "45分钟送达率")
    private String threeFortyFiveMinArrOrdPercentage;

    @FieldDoc(description = "发单送达率")
    private String threeAllArrivedOrdPercentage;

    @FieldDoc(description = "接单后送达率")
    private String threeJiedanArrivedOrdPercentage;


    public static OfflineSummaryFulfillDataDTO createDefaultDataVO() {
        return OfflineSummaryFulfillDataDTO.builder()
                .deliveredOrderNum(0)
                .cancelOrderNum(0)
                .judgeTimeoutOrderNum(0)
                .completeBeforeDeliveredOrderNum(0)
                .completeBeforeDeliveredOrderPercent("0.00")
                .deliveryDistanceAvg(0)
                .fulfillDurationAvg(0)
                .outWarehouseDurationAvg(0)
                .deliveredRateIn25min("0.00")
                .deliveredRateIn15min("0.00")
                .deliveredRateIn45min("0.00")
                .badCommentOrderNum(0)
                .badCommentOrderPercent("0.00")
                .deliveryLaborEfficiency("0.00")
                .build();
    }

    public void appendThirdData(OfflineSummaryFulfillDataDTO thirdData) {
        threeAllDelieryOrdNum = thirdData.getThreeAllDelieryOrdNum();

        threeArrivedOrdNum =  thirdData.getThreeArrivedOrdNum();

        threeCancelOrdNum = thirdData.getThreeCancelOrdNum() ;

        threeHave2PoiOrdPercentage =  thirdData.getThreeHave2PoiOrdPercentage();

        threeCancelOrdPercentage =  thirdData.getThreeCancelOrdPercentage();

        threeDeliverFeeAvg =  thirdData.getThreeDeliverFeeAvg();

        threePeisongDurationAvg =  thirdData.getThreePeisongDurationAvg();

        threeJiedanCancelOrdNum =  thirdData.getThreeJiedanCancelOrdNum();

        threeQuhuoCancelOrdNum =  thirdData.getThreeQuhuoCancelOrdNum();

        threeHave2PoiOrdNum =  thirdData.getThreeHave2PoiOrdNum();

        threeDeliverFee =  thirdData.getThreeDeliverFee();

        threeTipAmount =  thirdData.getThreeTipAmount();

        threeJieDanDuration =  thirdData.getThreeJieDanDuration();

        threePerformanceDuration =  thirdData.getThreePerformanceDuration();

        threeYunliPerformanceDuration =  thirdData.getThreeYunliPerformanceDuration();

        threePeisongDuration =  thirdData.getThreePeisongDuration();

        threeArrivedOrdNoPreNum = thirdData.getThreeArrivedOrdNoPreNum() ;

        threeTwentyFiveMinArrOrdNum =  thirdData.getThreeTwentyFiveMinArrOrdNum();

        threeFortyFiveMinArrOrdNum =  thirdData.getThreeFortyFiveMinArrOrdNum();

        threeTwentyFiveMinArrOrdPercentage =  thirdData.getThreeTwentyFiveMinArrOrdPercentage();

        threeFortyFiveMinArrOrdPercentage =  thirdData.getThreeFortyFiveMinArrOrdPercentage();

        threeAllArrivedOrdPercentage = thirdData.getThreeAllArrivedOrdPercentage();

        threeJiedanArrivedOrdPercentage = thirdData.getThreeJiedanArrivedOrdPercentage();
    }
}
