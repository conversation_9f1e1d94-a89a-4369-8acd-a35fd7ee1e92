package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/08/02 17:38
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SgDepartmentData {

    @FieldDoc(
            description = "组织架构id"
    )
    private Long groupColumn;

    @FieldDoc(
            description = "门店数量"
    )
    private Integer poiCnt;

    @FieldDoc(
            description = "门店集合"
    )
    private String poiIds;

    @FieldDoc(
            description = "有效订单量"
    )
    private Integer finOrdNum;

    @FieldDoc(
            description = "已送达订单量"
    )
    private Integer arrivedOrdNum;

    @FieldDoc(
            description = "严重超时订单量"
    )
    private Integer badOvertimeOrdNum;

    @FieldDoc(
            description = "考核超时订单量"
    )
    private Integer checkOvertimeOrdNum;

    @FieldDoc(
            description = "当日出勤人数"
    )
    private Integer attendanceNum;

    @FieldDoc(
            description = "九分位履约时长，分钟"
    )
    private String ninePerformanceDuration;

    @FieldDoc(
            description = "整单履约时长，分钟"
    )
    private String performanceDuration;

    @FieldDoc(
            description = "已送达订单量，剔除预订单"
    )
    private Integer arrivedOrdNoPreNum;

    @FieldDoc(
            description = "15分钟送达订单数"
    )
    private Integer fifteenMinArrOrdNum;

    @FieldDoc(
            description = "25分钟送达订单数"
    )
    private Integer twentyFiveMinArrOrdNum;

    @FieldDoc(
            description = "eta超时订单数据"
    )
    private Integer etaOvertimeOrdNum;

    @FieldDoc(
            description = "ETA超时订单量"
    )
    private Long etaOvertimeOrdNumV2;

    @FieldDoc(
            description = "ETA严重超时订单量"
    )
    private Long etaBadOvertimeOrdNumV2;
}
