package com.sankuai.shangou.logistics.delivery.gray.config;

import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.meituan.xframe.boot.zebra.autoconfigure.ZebraAutoConfiguration;
import com.sankuai.shangou.logistics.delivery.gray.GrayManagementThriftService;
import com.sankuai.shangou.logistics.delivery.gray.utils.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

/**
 * <AUTHOR>
 * @since 2024/5/7 15:32
 **/

@Configuration
@Order(Ordered.HIGHEST_PRECEDENCE)
public class GrayConfigConfiguration {

    private static final Logger log = LoggerFactory.getLogger(GrayConfigConfiguration.class);

    @Bean
    @ConditionalOnMissingBean
    public static GrayManagementThriftService autoConfigGrayManagementThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.logistics.dmp");
        proxy.setServiceInterface(Class.forName("com.sankuai.shangou.logistics.delivery.gray.GrayManagementThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        log.info("注入autoConfigGrayManagementThriftService成功!");
        return (GrayManagementThriftService) proxy.getObject();
    }

    @Bean
    @ConditionalOnMissingBean
    public static SpringContextUtils grayConfigSpringContextUtils() {
        log.info("注入grayConfigSpringContextUtils成功!");
        return new SpringContextUtils();
    }

}
