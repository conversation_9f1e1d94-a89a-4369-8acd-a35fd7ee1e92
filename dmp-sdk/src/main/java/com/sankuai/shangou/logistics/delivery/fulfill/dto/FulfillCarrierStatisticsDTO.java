package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@ThriftStruct
@TypeDoc(description = "三方运力+门店维度数据")
@Data
public class FulfillCarrierStatisticsDTO {
    @FieldDoc(description = "门店编码")
    @ThriftField(1)
    private Long storeId;

    @FieldDoc(description = "门店名称")
    @ThriftField(2)
    private String storeName;

    @FieldDoc(description = "三级组织架构名称")
    @ThriftField(3)
    private String departmentThirdTierName;

    @FieldDoc(description = "二级组织架构名称")
    @ThriftField(4)
    private String departmentSecondTierName;

    @ThriftField(5)
    @FieldDoc(description = "承运商id")
    private Integer carrierCode;

    @ThriftField(6)
    @FieldDoc(description = "承运商名称")
    private String carrierName;

    @ThriftField(7)
    @FieldDoc(description = "总发配送运单")
    private Integer transferOrder;

    @ThriftField(8)
    @FieldDoc(description = "已送达订单量")
    private Integer arrivedOrder;

    @ThriftField(9)
    @FieldDoc(description = "骑手接单后取消订单量")
    private Integer canceledAfterAcceptingOrder;

    @ThriftField(10)
    @FieldDoc(description = "骑手取货后取消订单量")
    private Integer canceledAfterPickingOrder;

    @ThriftField(11)
    @FieldDoc(description = "接单后送达率，包含%")
    private String arrivedAfterAcceptingRatio;

    @ThriftField(12)
    @FieldDoc(description = "单均配送成本")
    private String avgDeliveringCost;

    @ThriftField(13)
    @FieldDoc(description = "配送小费总金额")
    private String tips;

    @ThriftField(14)
    @FieldDoc(description = "单均接单时长")
    private String avgAcceptDuration;

    @ThriftField(15)
    @FieldDoc(description = "整单履约时长")
    private String avgFulfillDuration;

    @ThriftField(16)
    @FieldDoc(description = "整单运力侧履约时长")
    private String avgCarrierFulfillDuration;

    @ThriftField(17)
    @FieldDoc(description = "单均配送时长")
    private String avgDeliveryDuration;
}
