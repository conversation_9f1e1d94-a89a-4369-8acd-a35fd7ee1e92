package com.sankuai.shangou.logistics.delivery.offlineboard.response;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.commons.thrift.publisher.response.PaginationList;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-04
 * @email <EMAIL>
 */
@Data
@TypeDoc(
        description = "离线配送数据-组织维度返回"
)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DynamicExportOfflineBoardDataResponse {

    @FieldDoc(description = "每一行的数据和分页情况")
    @ThriftField(1)
    private PaginationList<List<String>> resultPaginationList;

    @FieldDoc(description = "标题抬头")
    @ThriftField(2)
    private List<String> headList;

}
