package com.sankuai.shangou.logistics.delivery.questionnaire.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/15 15:57
 **/

@Data
@ThriftStruct
public class DeliveryQuestionnaireDTO {
    private Long id;
    private Long deliveryOrderId;
    private Long orderId;

    private Long riderAccountId;

    private String question;

    private String answer;

    private List<String> answerOptList;

    private Long storeId;

    private Integer cityId;

    @ThriftField(1)
    public Long getId() {
        return this.id;
    }

    @ThriftField
    public void setId(Long id) {
        this.id = id;
    }

    @ThriftField(2)
    public Long getDeliveryOrderId() {
        return this.deliveryOrderId;
    }

    @ThriftField
    public void setDeliveryOrderId(Long deliveryOrderId) {
        this.deliveryOrderId = deliveryOrderId;
    }

    @ThriftField(3)
    public Long getOrderId() {
        return this.orderId;
    }

    @ThriftField
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    @ThriftField(4)
    public Long getRiderAccountId() {
        return this.riderAccountId;
    }

    @ThriftField
    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    @ThriftField(5)
    public String getQuestion() {
        return this.question;
    }

    @ThriftField
    public void setQuestion(String question) {
        this.question = question;
    }

    @ThriftField(6)
    public String getAnswer() {
        return this.answer;
    }

    @ThriftField
    public void setAnswer(String answer) {
        this.answer = answer;
    }

    @ThriftField(7)
    public List<String> getAnswerOptList() {
        return this.answerOptList;
    }

    @ThriftField
    public void setAnswerOptList(List<String> answerOptList) {
        this.answerOptList = answerOptList;
    }

    @ThriftField(8)
    public Long getStoreId() {
        return this.storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(9)
    public Integer getCityId() {
        return this.cityId;
    }

    @ThriftField
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
}
