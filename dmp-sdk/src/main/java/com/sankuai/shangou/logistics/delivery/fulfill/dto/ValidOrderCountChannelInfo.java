package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/7/27
 **/
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "渠道有效订单量信息")
public class ValidOrderCountChannelInfo implements Serializable {
    @FieldDoc(description = "渠道id")
    private Integer channelId;

    @FieldDoc(description = "有效订单量")
    private Integer validOrderCount;


    @ThriftField(1)
    public Integer getChannelId() {
        return this.channelId;
    }

    @ThriftField
    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    @ThriftField(2)
    public Integer getValidOrderCount() {
        return this.validOrderCount;
    }

    @ThriftField
    public void setValidOrderCount(Integer validOrderCount) {
        this.validOrderCount = validOrderCount;
    }
}
