package com.sankuai.shangou.logistics.delivery.configure.value;

import com.sankuai.shangou.logistics.delivery.configure.enums.InternalNavigationModeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NavigationConfig {
    /**
     * 跳转导航模式
     * @see InternalNavigationModeEnum#getMode()
     */
    private Integer internalNavigationMode;
}
