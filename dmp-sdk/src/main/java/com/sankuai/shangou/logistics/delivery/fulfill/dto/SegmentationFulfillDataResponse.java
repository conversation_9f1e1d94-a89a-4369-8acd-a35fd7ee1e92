package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/27
 **/
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "履约数据分时段趋势图查询响应")
public class SegmentationFulfillDataResponse implements Serializable {
    /**
     * @see com.sankuai.shangou.logistics.delivery.fulfill.enums.IndicatorTypeEnum
     */
    @FieldDoc(description = "指标类型")
    private Integer indicatorType;

    @FieldDoc(description = "今日-分段统计数据点")
    private List<SegmentDataPointDTO> todayDataPoints;

    @FieldDoc(description = "参考值-分段统计数据点")
    private List<SegmentDataPointDTO> refDataPoints;


    @ThriftField(1)
    public Integer getIndicatorType() {
        return this.indicatorType;
    }

    @ThriftField
    public void setIndicatorType(Integer indicatorType) {
        this.indicatorType = indicatorType;
    }

    @ThriftField(2)
    public List<SegmentDataPointDTO> getTodayDataPoints() {
        return this.todayDataPoints;
    }

    @ThriftField
    public void setTodayDataPoints(List<SegmentDataPointDTO> todayDataPoints) {
        this.todayDataPoints = todayDataPoints;
    }

    @ThriftField(3)
    public List<SegmentDataPointDTO> getRefDataPoints() {
        return this.refDataPoints;
    }

    @ThriftField
    public void setRefDataPoints(List<SegmentDataPointDTO> refDataPoints) {
        this.refDataPoints = refDataPoints;
    }
}
