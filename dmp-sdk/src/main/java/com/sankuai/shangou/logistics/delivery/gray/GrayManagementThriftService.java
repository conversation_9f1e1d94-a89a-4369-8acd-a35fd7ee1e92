package com.sankuai.shangou.logistics.delivery.gray;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.annotation.GatewayApi;
import com.sankuai.shangou.commons.thrift.publisher.request.UserContext;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.fulfill.dto.FulfillDataPageQueryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/28 16:00
 **/
public interface GrayManagementThriftService {
    @MethodDoc(
            displayName = "判断门店是否命中grayKey的灰度门店",
            description = "判断门店是否命中grayKey的灰度门店",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "判断门店是否命中grayKey的灰度门店",
                            type = FulfillDataPageQueryDTO.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "判断门店是否命中grayKey的灰度门店",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    Boolean checkIsGrayStore(String grayKey, Long tenantId, Long storeId);

    @MethodDoc(
            displayName = "判断门店是否命中grayKey的灰度门店",
            description = "判断门店是否命中grayKey的灰度门店",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "判断门店是否命中grayKey的灰度门店",
                            type = FulfillDataPageQueryDTO.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "判断门店是否命中grayKey的灰度门店",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    @GatewayApi(apiGroup = "dmp", apiName = "query-gray-config", path = "/common/dmp/query-gray-config", description = "灰度配置查询接口")
    TResult<Boolean> checkIsGrayStoreV2(UserContext userContext, String grayKey, Long storeId);

    @MethodDoc(
            displayName = "批量判断门店是否命中grayKey的灰度,返回命中灰度的门店名单",
            description = "批量判断门店是否命中grayKey的灰度,返回命中灰度的门店名单",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量判断门店是否命中grayKey的灰度,返回命中灰度的门店名单",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "批量判断门店是否命中grayKey的灰度,返回命中灰度的门店名单",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    List<Long> batchJudgeIsGrayStore(String grayKey, Long tenantId, List<Long> storeIds);
}
