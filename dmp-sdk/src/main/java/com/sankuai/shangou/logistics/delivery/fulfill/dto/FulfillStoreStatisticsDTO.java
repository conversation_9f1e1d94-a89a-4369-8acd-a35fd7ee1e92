package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/07/27 17:46
 */

@ThriftStruct
@TypeDoc(
        description = "履约配送数据看板：门店维度统计数据", authors = {"huangcheng21"}
)
@NoArgsConstructor
@AllArgsConstructor
public class FulfillStoreStatisticsDTO implements Serializable {

    @FieldDoc(description = "门店id")
    private Long storeId;

    @FieldDoc(description = "门店名称")
    private String storeName;

    @FieldDoc(description = "有效订单量")
    private Integer validOrder;

    @FieldDoc(description = "待领取订单量")
    private Integer waitToAcceptOrder;

    @FieldDoc(description = "考核超时待领取订单量")
    private Integer waitToAcceptJudgeTimeoutOrder;

    @FieldDoc(description = "考核超时待领取订单量")
    private Long waitToAcceptEtaTimeoutOrder;

    @FieldDoc(description = "待拣货订单量")
    private Integer pickingOrder;

    @FieldDoc(description = "考核待拣货超时订单量")
    private Integer pickingJudgeTimeoutOder;

    @FieldDoc(description = "配送中订单量")
    private Integer deliveringOrder;

    @FieldDoc(description = "考核配送中超时订单量")
    private Integer deliveringJudgeTimeoutOrder;

    @FieldDoc(description = "已送达订单量")
    private Integer deliveredOrder;

    @FieldDoc(description = "履约中骑手数量")
    private Integer fulfillRiderNum;

    @FieldDoc(description = "空闲中骑手数量")
    private Integer idleRiderNum;

    @FieldDoc(description = "履约中人均负载量")
    private String avgRiderLoad;

    @FieldDoc(description = "出勤人数")
    private Integer workedEmployeeNum;

    @FieldDoc(description = "九分位履约时长")
    private String ninetiethFulfillDuration;

    @FieldDoc(description = "整单履约时长")
    private String avgFulfillDuration;

    @FieldDoc(description = "考核超时订单量")
    private Integer judgeTimeoutOrder;

    @FieldDoc(description = "严重超时订单量")
    private Integer seriousTimeoutOrder;

    @FieldDoc(description = "严重超时订单列表")
    private List<DeliveryOrderChannelInfo> seriousTimeoutOrderList;

    @FieldDoc(description = "考核超时订单列表")
    private List<DeliveryOrderChannelInfo> judgeTimeoutOrderList;

    @FieldDoc(description = "15分钟送达率")
    private String deliveredRateIn15Min;

    @FieldDoc(description = "25分钟送达率")
    private String deliveredRateIn25Min;

    @FieldDoc(description = "门店+1组织节点")
    private String upOneLevelDepartmentName;

    @FieldDoc(description = "门店+2组织节点")
    private String upTwoLevelDepartmentName;

    @FieldDoc(description = "eta超时订单率")
    private String etaTimeoutRate;

    @FieldDoc(description = "eta严重超时订单率")
    private String etaSeriouslyTimeoutRate;

    @FieldDoc(description = "15分钟送达率+%")
    private String deliveredRateIn15MinDesc;
    @FieldDoc(description = "25分钟送达率+%")
    private String deliveredRateIn25MinDesc;

    @FieldDoc(description = "eta超时订单率+%")
    private String etaTimeoutRateDesc;

    @FieldDoc(description = "eta严重超时订单率+%")
    private String etaSeriouslyTimeoutRateDesc;

    @FieldDoc(description = "ETA超时订单量")
    private Long etaOvertimeOrdNumV2;

    @FieldDoc(description = "ETA严重超时订单量 ")
    private Long etaBadOvertimeOrdNumV2;

    @FieldDoc(description = "拣货中ETA超时订单量")
    private Long pickingEtaOvertimeOrderV2;

    @FieldDoc(description = "配送中ETA超时订单量")
    private Long deliveringEtaOvertimeOrderV2;

    @FieldDoc(description = "ETA准时率")
    private String etaOntimeRatioV2;

    @FieldDoc(description = "ETA严重超时率")
    private String etaBadOvertimeRatioV2;

    @FieldDoc(description = "ETA准时率+%")
    private String etaOntimeRatioV2Desc;

    @FieldDoc(description = "ETA严重超时率+%")
    private String etaBadOvertimeRatioV2Desc;



    @ThriftField(1)
    public Long getStoreId() {
        return this.storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(2)
    public String getStoreName() {
        return this.storeName;
    }

    @ThriftField
    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    @ThriftField(3)
    public Integer getValidOrder() {
        return this.validOrder;
    }

    @ThriftField
    public void setValidOrder(Integer validOrder) {
        this.validOrder = validOrder;
    }

    @ThriftField(4)
    public Integer getWaitToAcceptOrder() {
        return this.waitToAcceptOrder;
    }

    @ThriftField
    public void setWaitToAcceptOrder(Integer waitToAcceptOrder) {
        this.waitToAcceptOrder = waitToAcceptOrder;
    }

    @ThriftField(5)
    public Integer getWaitToAcceptJudgeTimeoutOrder() {
        return this.waitToAcceptJudgeTimeoutOrder;
    }

    @ThriftField
    public void setWaitToAcceptJudgeTimeoutOrder(Integer waitToAcceptJudgeTimeoutOrder) {
        this.waitToAcceptJudgeTimeoutOrder = waitToAcceptJudgeTimeoutOrder;
    }

    @ThriftField(6)
    public Integer getPickingOrder() {
        return this.pickingOrder;
    }

    @ThriftField
    public void setPickingOrder(Integer pickingOrder) {
        this.pickingOrder = pickingOrder;
    }

    @ThriftField(7)
    public Integer getPickingJudgeTimeoutOder() {
        return this.pickingJudgeTimeoutOder;
    }

    @ThriftField
    public void setPickingJudgeTimeoutOder(Integer pickingJudgeTimeoutOder) {
        this.pickingJudgeTimeoutOder = pickingJudgeTimeoutOder;
    }

    @ThriftField(8)
    public Integer getDeliveringOrder() {
        return this.deliveringOrder;
    }

    @ThriftField
    public void setDeliveringOrder(Integer deliveringOrder) {
        this.deliveringOrder = deliveringOrder;
    }

    @ThriftField(9)
    public Integer getDeliveringJudgeTimeoutOrder() {
        return this.deliveringJudgeTimeoutOrder;
    }

    @ThriftField
    public void setDeliveringJudgeTimeoutOrder(Integer deliveringJudgeTimeoutOrder) {
        this.deliveringJudgeTimeoutOrder = deliveringJudgeTimeoutOrder;
    }

    @ThriftField(10)
    public Integer getDeliveredOrder() {
        return this.deliveredOrder;
    }

    @ThriftField
    public void setDeliveredOrder(Integer deliveredOrder) {
        this.deliveredOrder = deliveredOrder;
    }

    @ThriftField(11)
    public Integer getFulfillRiderNum() {
        return this.fulfillRiderNum;
    }

    @ThriftField
    public void setFulfillRiderNum(Integer fulfillRiderNum) {
        this.fulfillRiderNum = fulfillRiderNum;
    }

    @ThriftField(12)
    public Integer getIdleRiderNum() {
        return this.idleRiderNum;
    }

    @ThriftField
    public void setIdleRiderNum(Integer idleRiderNum) {
        this.idleRiderNum = idleRiderNum;
    }

    @ThriftField(13)
    public String getAvgRiderLoad() {
        return this.avgRiderLoad;
    }

    @ThriftField
    public void setAvgRiderLoad(String avgRiderLoad) {
        this.avgRiderLoad = avgRiderLoad;
    }

    @ThriftField(14)
    public Integer getWorkedEmployeeNum() {
        return this.workedEmployeeNum;
    }

    @ThriftField
    public void setWorkedEmployeeNum(Integer workedEmployeeNum) {
        this.workedEmployeeNum = workedEmployeeNum;
    }

    @ThriftField(15)
    public String getNinetiethFulfillDuration() {
        return this.ninetiethFulfillDuration;
    }

    @ThriftField
    public void setNinetiethFulfillDuration(String ninetiethFulfillDuration) {
        this.ninetiethFulfillDuration = ninetiethFulfillDuration;
    }

    @ThriftField(16)
    public String getAvgFulfillDuration() {
        return this.avgFulfillDuration;
    }

    @ThriftField
    public void setAvgFulfillDuration(String avgFulfillDuration) {
        this.avgFulfillDuration = avgFulfillDuration;
    }

    @ThriftField(17)
    public Integer getJudgeTimeoutOrder() {
        return this.judgeTimeoutOrder;
    }

    @ThriftField
    public void setJudgeTimeoutOrder(Integer judgeTimeoutOrder) {
        this.judgeTimeoutOrder = judgeTimeoutOrder;
    }

    @ThriftField(18)
    public Integer getSeriousTimeoutOrder() {
        return this.seriousTimeoutOrder;
    }

    @ThriftField
    public void setSeriousTimeoutOrder(Integer seriousTimeoutOrder) {
        this.seriousTimeoutOrder = seriousTimeoutOrder;
    }

    @ThriftField(19)
    public List<DeliveryOrderChannelInfo> getSeriousTimeoutOrderList() {
        return this.seriousTimeoutOrderList;
    }

    @ThriftField
    public void setSeriousTimeoutOrderList(List<DeliveryOrderChannelInfo> seriousTimeoutOrderList) {
        this.seriousTimeoutOrderList = seriousTimeoutOrderList;
    }

    @ThriftField(20)
    public List<DeliveryOrderChannelInfo> getJudgeTimeoutOrderList() {
        return this.judgeTimeoutOrderList;
    }

    @ThriftField
    public void setJudgeTimeoutOrderList(List<DeliveryOrderChannelInfo> judgeTimeoutOrderList) {
        this.judgeTimeoutOrderList = judgeTimeoutOrderList;
    }

    @ThriftField(21)
    public String getDeliveredRateIn15Min() {
        return this.deliveredRateIn15Min;
    }

    @ThriftField
    public void setDeliveredRateIn15Min(String deliveredRateIn15Min) {
        this.deliveredRateIn15Min = deliveredRateIn15Min;
    }

    @ThriftField(22)
    public String getDeliveredRateIn25Min() {
        return this.deliveredRateIn25Min;
    }

    @ThriftField
    public void setDeliveredRateIn25Min(String deliveredRateIn25Min) {
        this.deliveredRateIn25Min = deliveredRateIn25Min;
    }

    @ThriftField(23)
    public String getUpOneLevelDepartmentName() {
        return this.upOneLevelDepartmentName;
    }

    @ThriftField
    public void setUpOneLevelDepartmentName(String upOneLevelDepartmentName) {
        this.upOneLevelDepartmentName = upOneLevelDepartmentName;
    }

    @ThriftField(24)
    public String getUpTwoLevelDepartmentName() {
        return this.upTwoLevelDepartmentName;
    }

    @ThriftField
    public void setUpTwoLevelDepartmentName(String upTwoLevelDepartmentName) {
        this.upTwoLevelDepartmentName = upTwoLevelDepartmentName;
    }

    @ThriftField(25)
    public String getEtaTimeoutRate() {
        return this.etaTimeoutRate;
    }

    @ThriftField
    public void setEtaTimeoutRate(String etaTimeoutRate) {
        this.etaTimeoutRate = etaTimeoutRate;
    }

    @ThriftField(26)
    public String getEtaSeriouslyTimeoutRate() {
        return this.etaSeriouslyTimeoutRate;
    }

    @ThriftField
    public void setEtaSeriouslyTimeoutRate(String etaSeriouslyTimeoutRate) {
        this.etaSeriouslyTimeoutRate = etaSeriouslyTimeoutRate;
    }


    @ThriftField(27)
    public String getDeliveredRateIn15MinDesc() {
        return this.deliveredRateIn15MinDesc;
    }

    @ThriftField
    public void setDeliveredRateIn15MinDesc(String deliveredRateIn15MinDesc) {
        this.deliveredRateIn15MinDesc = deliveredRateIn15MinDesc;
    }

    @ThriftField(28)
    public String getDeliveredRateIn25MinDesc() {
        return this.deliveredRateIn25MinDesc;
    }

    @ThriftField
    public void setDeliveredRateIn25MinDesc(String deliveredRateIn25MinDesc) {
        this.deliveredRateIn25MinDesc = deliveredRateIn25MinDesc;
    }

    @ThriftField(29)
    public String getEtaTimeoutRateDesc() {
        return this.etaTimeoutRateDesc;
    }

    @ThriftField
    public void setEtaTimeoutRateDesc(String etaTimeoutRateDesc) {
        this.etaTimeoutRateDesc = etaTimeoutRateDesc;
    }

    @ThriftField(30)
    public String getEtaSeriouslyTimeoutRateDesc() {
        return this.etaSeriouslyTimeoutRateDesc;
    }

    @ThriftField
    public void setEtaSeriouslyTimeoutRateDesc(String etaSeriouslyTimeoutRateDesc) {
        this.etaSeriouslyTimeoutRateDesc = etaSeriouslyTimeoutRateDesc;
    }

    @ThriftField(31)
    public Long getEtaOvertimeOrdNumV2() {
        return etaOvertimeOrdNumV2;
    }

    @ThriftField
    public void setEtaOvertimeOrdNumV2(Long etaOvertimeOrdNumV2) {
        this.etaOvertimeOrdNumV2 = etaOvertimeOrdNumV2;
    }

    @ThriftField(32)
    public Long getEtaBadOvertimeOrdNumV2() {
        return etaBadOvertimeOrdNumV2;
    }

    @ThriftField
    public void setEtaBadOvertimeOrdNumV2(Long etaBadOvertimeOrdNumV2) {
        this.etaBadOvertimeOrdNumV2 = etaBadOvertimeOrdNumV2;
    }

    @ThriftField(33)
    public Long getPickingEtaOvertimeOrderV2() {
        return pickingEtaOvertimeOrderV2;
    }

    @ThriftField
    public void setPickingEtaOvertimeOrderV2(Long pickingEtaOvertimeOrderV2) {
        this.pickingEtaOvertimeOrderV2 = pickingEtaOvertimeOrderV2;
    }

    @ThriftField(34)
    public Long getDeliveringEtaOvertimeOrderV2() {
        return deliveringEtaOvertimeOrderV2;
    }

    public void setDeliveringEtaOvertimeOrderV2(Long deliveringEtaOvertimeOrderV2) {
        this.deliveringEtaOvertimeOrderV2 = deliveringEtaOvertimeOrderV2;
    }

    @ThriftField(35)
    public String getEtaOntimeRatioV2() {
        return etaOntimeRatioV2;
    }

    @ThriftField
    public void setEtaOntimeRatioV2(String etaOntimeRatioV2) {
        this.etaOntimeRatioV2 = etaOntimeRatioV2;
    }

    @ThriftField(36)
    public String getEtaBadOvertimeRatioV2() {
        return etaBadOvertimeRatioV2;
    }

    @ThriftField
    public void setEtaBadOvertimeRatioV2(String etaBadOvertimeRatioV2) {
        this.etaBadOvertimeRatioV2 = etaBadOvertimeRatioV2;
    }

    @ThriftField(35)
    public String getEtaOntimeRatioV2Desc() {
        return this.etaOntimeRatioV2Desc;
    }

    @ThriftField
    public void setEtaOntimeRatioV2Desc(String etaOntimeRatioV2Desc) {
        this.etaOntimeRatioV2Desc = etaOntimeRatioV2Desc;
    }

    @ThriftField(36)
    public String getEtaBadOvertimeRatioV2Desc() {
        return etaBadOvertimeRatioV2Desc;
    }

    @ThriftField
    public void setEtaBadOvertimeRatioV2Desc(String etaBadOvertimeRatioV2Desc) {
        this.etaBadOvertimeRatioV2Desc = etaBadOvertimeRatioV2Desc;
    }

    @ThriftField(37)
    public Long getWaitToAcceptEtaTimeoutOrder() {
        return waitToAcceptEtaTimeoutOrder;
    }

    @ThriftField
    public void setWaitToAcceptEtaTimeoutOrder(Long waitToAcceptEtaTimeoutOrder) {
        this.waitToAcceptEtaTimeoutOrder = waitToAcceptEtaTimeoutOrder;
    }
}
