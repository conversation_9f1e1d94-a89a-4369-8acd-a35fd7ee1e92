package com.sankuai.shangou.logistics.delivery.configure.value;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 确认送达操作配置
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@Data
public class DeliveryCompleteMode {

    /**
     * 距离提醒：0-不提示 1-提示
     */
    private Integer distanceReminder;

    /**
     * 配送完成配置
     */
    private DeliveryCompleteConfig deliveryCompleteConfig;

    /**
     * 配送完成配置
     */
    @Data
    public static class DeliveryCompleteConfig {

        /**
         * 经营类型：1-直营 2-加盟 3-赋能
         */
        private Integer operationMode;

        /**
         * 所有示例图片信息列表
         */
        private List<ExamplePicInfo> allExamplePicInfoList;

        /**
         * 特殊商品上传图片配置
         */
        private List<SpecialProductUploadPicConfig> specialProductUploadPicConfig;

        /**
         * 发送图片给顾客提示
         */
        private String sendPicToCustomerTips;

        /**
         * 未联系顾客提示
         */
        private String notContactCustomerTips;

        /**
         * 上传图片最大时长阈值
         */
        private Integer uploadImageDurationThreshold;

        /**
         * 是否显示未联系顾客提示（赋能无法使用）
         */
        private Boolean isShowNotContactCustomerTips;
    }

    /**
     * 示例图片信息
     */
    @Data
    public static class ExamplePicInfo {

        /**
         * 图片类型
         */
        private Integer type;

        /**
         * 图片名称
         */
        private String name;

        /**
         * 图片链接
         */
        private String picUrl;

        /**
         * 显示顺序
         */
        private Integer order;
    }

    /**
     * 特殊商品上传图片配置
     */
    @Data
    public static class SpecialProductUploadPicConfig {

        /**
         * 商品类型（如：封签/高价）
         */
        private String productType;

        /**
         * 图片类型列表
         */
        private List<Integer> picTypeList;

        /**
         * 是否强制上传图片
         */
        private Boolean isForceUploadPic;

        /**
         * 需要上传的照片张数
         */
        private Integer needUploadPicCount;
    }

    public static DeliveryCompleteMode makeDefault() {
        DeliveryCompleteMode deliveryCompleteMode = new DeliveryCompleteMode();

        // 距离提醒：0-不提示
        deliveryCompleteMode.setDistanceReminder(0);

        // 配送完成配置
        DeliveryCompleteConfig config = new DeliveryCompleteConfig();
        config.setAllExamplePicInfoList(new ArrayList<>());
        config.setSpecialProductUploadPicConfig(new ArrayList<>());
        config.setUploadImageDurationThreshold(7);
        config.setIsShowNotContactCustomerTips(false); // 是否展示未联系用户提示（赋能无法使用）

        deliveryCompleteMode.setDeliveryCompleteConfig(config);

        return deliveryCompleteMode;
    }
}

