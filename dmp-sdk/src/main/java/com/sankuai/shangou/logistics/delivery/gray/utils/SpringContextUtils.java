package com.sankuai.shangou.logistics.delivery.gray.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Spring上下文工具类，提供各种bean的获取
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/7
 */
public class SpringContextUtils implements ApplicationContextAware {

	private static ApplicationContext applicationContext;

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		SpringContextUtils.applicationContext = applicationContext;
	}

	public static <T> T getBean(Class<T> t) {
		return applicationContext.getBean(t);
	}

	public static <T> T getBean(Class<T> clazz, String beanName) {
		return applicationContext.getBean(beanName, clazz);
	}

	public static <T> Map<String, T> getBeanOfType(Class<T> clazz) {
		return applicationContext.getBeansOfType(clazz);
	}
}
