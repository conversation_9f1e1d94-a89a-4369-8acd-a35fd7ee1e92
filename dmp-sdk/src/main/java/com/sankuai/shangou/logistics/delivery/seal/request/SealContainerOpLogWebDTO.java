package com.sankuai.shangou.logistics.delivery.seal.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/6/27 17:30
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
public class SealContainerOpLogWebDTO {
    @FieldDoc(
            description = "仓id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    private Long warehouseId;

    @FieldDoc(
            description = "仓名称",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    private String warehouseName;

    @FieldDoc(
            description = "封签码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    private String sealContainerCode;

    @FieldDoc(
            description = "交易单",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    private String tradeOrderNo;

    @FieldDoc(
            description = "渠道id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    private Integer channelId;

    @FieldDoc(
            description = "状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    private Integer status;

    @FieldDoc(
            description = "状态文本",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(7)
    private String statusDesc;

    @FieldDoc(
            description = "出库人",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(8)
    private String stockOutOperatorName;

    @FieldDoc(
            description = "出库时间",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(9)
    private Long stockOutTime;

    @FieldDoc(
            description = "出库时间,格式化",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(10)
    private String stockOutTimeStr;

    @FieldDoc(
            description = "取货人",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(11)
    private String riderName;

    @FieldDoc(
            description = "取货时间",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(12)
    private Long riderStartDeliveryTime;

    @FieldDoc(
            description = "取货时间,格式化",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(13)
    private String riderStartDeliveryTimeStr;

    @FieldDoc(
            description = "归还人",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(14)
    private String giveBackOperatorName;

    @FieldDoc(
            description = "归还时间",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(15)
    private Long giveBackTime;

    @FieldDoc(
            description = "归还时间，格式化",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(16)
    private String giveBackTimeStr;

    @FieldDoc(
            description = "归还类型",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(17)
    private Integer giveBackType;

    @FieldDoc(
            description = "归还类型,文字描述",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(18)
    private String giveBackTypeDesc;
}
