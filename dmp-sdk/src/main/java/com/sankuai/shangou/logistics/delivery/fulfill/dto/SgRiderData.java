package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/08/02 20:00
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SgRiderData {

    @FieldDoc(
            description = "账号id"
    )
    private Long riderAccountId;

    @FieldDoc(
            description = "骑手名称"
    )
    private String riderName;

    @FieldDoc(
            description = "已送达订单量"
    )
    private Integer arrivedOrdNum;

    @FieldDoc(
            description = "严重超时订单量"
    )
    private Integer badOvertimeOrdNum;

    @FieldDoc(
            description = "考核超时订单量"
    )
    private Integer checkOvertimeOrdNum;

    @FieldDoc(
            description = "九分位履约时长，分钟"
    )

    private String ninePerformanceDuration;

    @FieldDoc(
            description = "整单履约时长，分钟"
    )

    private String performanceDuration;

    @FieldDoc(
            description = "已送达订单量，剔除预订单"
    )
    private Integer arrivedOrdNoPreNum;

    @FieldDoc(
            description = "15分钟送达订单数"
    )
    private Integer fifteenMinArrOrdNum;

    @FieldDoc(
            description = "25分钟送达订单数"
    )
    private Integer twentyFiveMinArrOrdNum;

    @FieldDoc(
            description = "严重超时订单渠道和流水号,示例：100:1551372563374981130,700:1551375661590073388"
    )
    private String badOrds;

    @FieldDoc(
            description = "考核超时订单渠道和流水号,示例：100:1551372563374981130,700:1551375661590073388"
    )
    private String checkOrds;

    @FieldDoc(
            description = "eta超时订单数据"
    )
    private Integer etaOvertimeOrdNum;

    @FieldDoc(
            description = "ETA超时订单量"
    )
    private Long etaOvertimeOrdNumV2;

    @FieldDoc(
            description = "ETA严重超时订单量"
    )
    private Long etaBadOvertimeOrdNumV2;

}
