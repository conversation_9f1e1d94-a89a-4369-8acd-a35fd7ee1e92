package com.sankuai.shangou.logistics.delivery.gray.utils;

import com.dianping.cat.Cat;
import com.sankuai.shangou.logistics.delivery.gray.GrayManagementThriftService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/7 15:49
 **/
public class GrayConfigUtils {

    private static final Logger log = LoggerFactory.getLogger(GrayConfigUtils.class);

    public static boolean judgeIsGrayStore(Long tenantId, Long storeId, String grayKey) {
        GrayManagementThriftService grayManagementThriftService =
                SpringContextUtils.getBean(GrayManagementThriftService.class);
        Boolean isGrayStore = grayManagementThriftService.checkIsGrayStore(grayKey, tenantId, storeId);
        log.info("tenantId: {}, storeId: {}, grayKey: {}, result: {}", tenantId, storeId, grayKey, isGrayStore);
        return isGrayStore;
    }

    public static boolean judgeIsGrayStore(Long tenantId, Long storeId, String grayKey, boolean defaultValue) {
        try {
            GrayManagementThriftService grayManagementThriftService =
                    SpringContextUtils.getBean(GrayManagementThriftService.class);
            Boolean isGrayStore = grayManagementThriftService.checkIsGrayStore(grayKey, tenantId, storeId);
            log.info("tenantId: {}, storeId: {}, grayKey: {}, result: {}",tenantId, storeId, grayKey, isGrayStore);
            return isGrayStore;
        } catch (Exception e) {
            log.error("查询灰度开关失败", e);
            log.info("tenantId: {}, storeId: {}, grayKey: {}, result: {}", tenantId, storeId, grayKey, defaultValue);
            return defaultValue;
        }
    }


    public static List<Long> batchJudgeIsGrayStore(Long tenantId, List<Long> storeIds, String grayKey) {
        GrayManagementThriftService grayManagementThriftService =
                SpringContextUtils.getBean(GrayManagementThriftService.class);
        List<Long> grayStoreIds = grayManagementThriftService.batchJudgeIsGrayStore(grayKey, tenantId, storeIds);
        log.info("tenantId: {}, storeIds: {}, grayKey: {}, result: {}", tenantId, storeIds, grayKey, grayStoreIds);
        return grayStoreIds;
    }

}
