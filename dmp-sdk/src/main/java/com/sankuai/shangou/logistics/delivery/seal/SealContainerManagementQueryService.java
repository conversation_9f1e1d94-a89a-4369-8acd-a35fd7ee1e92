package com.sankuai.shangou.logistics.delivery.seal;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.response.PaginationList;
import com.sankuai.shangou.logistics.delivery.seal.request.QuerySealContainerListReq;
import com.sankuai.shangou.logistics.delivery.seal.request.QuerySealContainerLogByConditionRequest;
import com.sankuai.shangou.logistics.delivery.seal.request.SealContainerOpLogWebDTO;

/**
 * <AUTHOR>
 * @date 2024-07-05
 * @email <EMAIL>
 */
@InterfaceDoc(
        displayName = "封签容具管理查询接口",
        type = "octo.thrift.annotation",
        scenarios = "封签容具管理查询接口",
        description = "封签容具管理查询接口",
        authors = {
                "jianglilin02"
        }
)
@ThriftService
public interface SealContainerManagementQueryService {

    @MethodDoc(
            displayName = "初始化自配门店配置",
            description = "初始化自配门店配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "初始化自配门店配置",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "初始化自配门店配置返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    PaginationList<SealContainerOpLogWebDTO> querySealContainerLogList(QuerySealContainerLogByConditionRequest request);

    @MethodDoc(
            displayName = "初始化自配门店配置",
            description = "初始化自配门店配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "初始化自配门店配置",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "初始化自配门店配置返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    PaginationList<SealContainerOpLogWebDTO> queryStoreUsingSealContainerList(QuerySealContainerListReq req);
}
