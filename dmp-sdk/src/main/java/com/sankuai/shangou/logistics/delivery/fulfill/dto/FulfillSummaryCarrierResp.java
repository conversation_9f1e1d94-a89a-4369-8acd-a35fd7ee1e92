package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;

@ThriftStruct
@TypeDoc(
        description = "履约配送数据看板查询第三方运力卡片数据响应", authors = {"xiaopengfei06"}
)
public class FulfillSummaryCarrierResp implements Serializable {

    @FieldDoc(description = "总发配送订单量")
    private Integer transferOrder = 0;

    @FieldDoc(description = "比对值-总发配送订单量")
    private Integer refTransferOrder = 0;

    @FieldDoc(description = "已经送达单量")
    private Integer arrivedOrder = 0;

    @FieldDoc(description = "比对值-已经送达单量")
    private Integer refArrivedOrder =  0;

    @FieldDoc(description = "已取消单量")
    private Integer canceledOrder = 0;

    @FieldDoc(description = "比对值-已取消单量")
    private Integer refCanceledOrder = 0;

    @FieldDoc(description = "骑手接单后取消运单量")
    private Integer canceledAfterAcceptingOrder = 0;

    @FieldDoc(description = "比对值-骑手接单后取消运单量")
    private Integer refCanceledAfterAcceptingOrder =  0;

    @FieldDoc(description = "骑手取货后取消运单量")
    private Integer canceledAfterPickingOrder = 0;

    @FieldDoc(description = "比对值-骑手取货后取消运单量")
    private Integer refCanceledAfterPickingOrder = 0;

    @FieldDoc(description = "接单率")
    private String acceptRatio;

    @FieldDoc(description = "比对值-接单率")
    private String refAcceptRatio;

    @FieldDoc(description = "发单后送达率")
    private String arriveAfterTransferRatio;

    @FieldDoc(description = "比对值-发单后送达率")
    private String refArriveAfterTransferRatio;

    @FieldDoc(description = "接单后送达率")
    private String arriveAfterAcceptingRatio;

    @FieldDoc(description = "比对值-接单后送达率")
    private String refArriveAfterAcceptingRatio;

    @FieldDoc(description = "单均配送成本")
    private String avgDeliveringCost;

    @FieldDoc(description = "比对值-单均配送成本")
    private String refAvgDeliveringCost;

    @FieldDoc(description = "配送小费总金额")
    private String tips;

    @FieldDoc(description = "比对值-配送小费总金额")
    private String refTips;

    @FieldDoc(description = "单均接单时长")
    private String avgAcceptDuration;

    @FieldDoc(description = "比对值-单均接单时长")
    private String refAvgAcceptDuration;

    @FieldDoc(description = "整单履约时长")
    private String avgFulfillDuration;

    @FieldDoc(description = "比对值-整单履约时长")
    private String refAvgFulfillDuration;

    @FieldDoc(description = "整单运力侧履约时长")
    private String avgCarrierFulfillDuration;

    @FieldDoc(description = "比对值-整单运力侧履约时长")
    private String refAvgCarrierFulfillDuration;

    @FieldDoc(description = "单均配送时长")
    private String avgDeliveryDuration;

    @FieldDoc(description = "比对值-单均配送时长")
    private String refAvgDeliveryDuration;

    @FieldDoc(description = "25分钟送达率")
    private String deliveredRateIn25min;

    @FieldDoc(description = "比对值-25分钟送达率")
    private String refDeliveredRateIn25min;

    @FieldDoc(description = "45分钟送达率")
    private String deliveredRateIn45min;

    @FieldDoc(description = "比对值-45分钟送达率")
    private String refDeliveredRateIn45min;

    @FieldDoc(description = "ETA准时率")
    private String etaOntimeRatioV2;

    @FieldDoc(description = "ETA严重超时率")
    private String etaBadOvertimeRatioV2;

    @FieldDoc(description = "对比ETA准时率")
    private String refEtaOntimeRatioV2;

    @FieldDoc(description = "对比ETA严重超时率")
    private String refEtaBadOvertimeRatioV2;


    @ThriftField(1)
    public Integer getTransferOrder() {
        return this.transferOrder;
    }

    @ThriftField
    public void setTransferOrder(Integer transferOrder) {
        this.transferOrder = transferOrder;
    }

    @ThriftField(2)
    public Integer getRefTransferOrder() {
        return this.refTransferOrder;
    }

    @ThriftField
    public void setRefTransferOrder(Integer refTransferOrder) {
        this.refTransferOrder = refTransferOrder;
    }

    @ThriftField(3)
    public Integer getArrivedOrder() {
        return this.arrivedOrder;
    }

    @ThriftField
    public void setArrivedOrder(Integer arrivedOrder) {
        this.arrivedOrder = arrivedOrder;
    }

    @ThriftField(4)
    public Integer getRefArrivedOrder() {
        return this.refArrivedOrder;
    }

    @ThriftField
    public void setRefArrivedOrder(Integer refArrivedOrder) {
        this.refArrivedOrder = refArrivedOrder;
    }

    @ThriftField(5)
    public Integer getCanceledOrder() {
        return this.canceledOrder;
    }

    @ThriftField
    public void setCanceledOrder(Integer canceledOrder) {
        this.canceledOrder = canceledOrder;
    }

    @ThriftField(6)
    public Integer getRefCanceledOrder() {
        return this.refCanceledOrder;
    }

    @ThriftField
    public void setRefCanceledOrder(Integer refCanceledOrder) {
        this.refCanceledOrder = refCanceledOrder;
    }

    @ThriftField(7)
    public Integer getCanceledAfterAcceptingOrder() {
        return this.canceledAfterAcceptingOrder;
    }

    @ThriftField
    public void setCanceledAfterAcceptingOrder(Integer canceledAfterAcceptingOrder) {
        this.canceledAfterAcceptingOrder = canceledAfterAcceptingOrder;
    }

    @ThriftField(8)
    public Integer getRefCanceledAfterAcceptingOrder() {
        return this.refCanceledAfterAcceptingOrder;
    }

    @ThriftField
    public void setRefCanceledAfterAcceptingOrder(Integer refCanceledAfterAcceptingOrder) {
        this.refCanceledAfterAcceptingOrder = refCanceledAfterAcceptingOrder;
    }

    @ThriftField(9)
    public Integer getCanceledAfterPickingOrder() {
        return this.canceledAfterPickingOrder;
    }

    @ThriftField
    public void setCanceledAfterPickingOrder(Integer canceledAfterPickingOrder) {
        this.canceledAfterPickingOrder = canceledAfterPickingOrder;
    }

    @ThriftField(10)
    public Integer getRefCanceledAfterPickingOrder() {
        return this.refCanceledAfterPickingOrder;
    }

    @ThriftField
    public void setRefCanceledAfterPickingOrder(Integer refCanceledAfterPickingOrder) {
        this.refCanceledAfterPickingOrder = refCanceledAfterPickingOrder;
    }

    @ThriftField(11)
    public String getAcceptRatio() {
        return this.acceptRatio;
    }

    @ThriftField
    public void setAcceptRatio(String acceptRatio) {
        this.acceptRatio = acceptRatio;
    }

    @ThriftField(12)
    public String getRefAcceptRatio() {
        return this.refAcceptRatio;
    }

    @ThriftField
    public void setRefAcceptRatio(String refAcceptRatio) {
        this.refAcceptRatio = refAcceptRatio;
    }

    @ThriftField(13)
    public String getArriveAfterTransferRatio() {
        return this.arriveAfterTransferRatio;
    }

    @ThriftField
    public void setArriveAfterTransferRatio(String arriveAfterTransferRatio) {
        this.arriveAfterTransferRatio = arriveAfterTransferRatio;
    }

    @ThriftField(14)
    public String getRefArriveAfterTransferRatio() {
        return this.refArriveAfterTransferRatio;
    }

    @ThriftField
    public void setRefArriveAfterTransferRatio(String refArriveAfterTransferRatio) {
        this.refArriveAfterTransferRatio = refArriveAfterTransferRatio;
    }

    @ThriftField(15)
    public String getArriveAfterAcceptingRatio() {
        return this.arriveAfterAcceptingRatio;
    }

    @ThriftField
    public void setArriveAfterAcceptingRatio(String arriveAfterAcceptingRatio) {
        this.arriveAfterAcceptingRatio = arriveAfterAcceptingRatio;
    }

    @ThriftField(16)
    public String getRefArriveAfterAcceptingRatio() {
        return this.refArriveAfterAcceptingRatio;
    }

    @ThriftField
    public void setRefArriveAfterAcceptingRatio(String refArriveAfterAcceptingRatio) {
        this.refArriveAfterAcceptingRatio = refArriveAfterAcceptingRatio;
    }

    @ThriftField(17)
    public String getAvgDeliveringCost() {
        return this.avgDeliveringCost;
    }

    @ThriftField
    public void setAvgDeliveringCost(String avgDeliveringCost) {
        this.avgDeliveringCost = avgDeliveringCost;
    }

    @ThriftField(18)
    public String getRefAvgDeliveringCost() {
        return this.refAvgDeliveringCost;
    }

    @ThriftField
    public void setRefAvgDeliveringCost(String refAvgDeliveringCost) {
        this.refAvgDeliveringCost = refAvgDeliveringCost;
    }

    @ThriftField(19)
    public String getTips() {
        return this.tips;
    }

    @ThriftField
    public void setTips(String tips) {
        this.tips = tips;
    }

    @ThriftField(20)
    public String getRefTips() {
        return this.refTips;
    }

    @ThriftField
    public void setRefTips(String refTips) {
        this.refTips = refTips;
    }

    @ThriftField(21)
    public String getAvgAcceptDuration() {
        return this.avgAcceptDuration;
    }

    @ThriftField
    public void setAvgAcceptDuration(String avgAcceptDuration) {
        this.avgAcceptDuration = avgAcceptDuration;
    }

    @ThriftField(22)
    public String getRefAvgAcceptDuration() {
        return this.refAvgAcceptDuration;
    }

    @ThriftField
    public void setRefAvgAcceptDuration(String refAvgAcceptDuration) {
        this.refAvgAcceptDuration = refAvgAcceptDuration;
    }

    @ThriftField(23)
    public String getAvgFulfillDuration() {
        return this.avgFulfillDuration;
    }

    @ThriftField
    public void setAvgFulfillDuration(String avgFulfillDuration) {
        this.avgFulfillDuration = avgFulfillDuration;
    }

    @ThriftField(24)
    public String getRefAvgFulfillDuration() {
        return this.refAvgFulfillDuration;
    }

    @ThriftField
    public void setRefAvgFulfillDuration(String refAvgFulfillDuration) {
        this.refAvgFulfillDuration = refAvgFulfillDuration;
    }

    @ThriftField(25)
    public String getAvgCarrierFulfillDuration() {
        return this.avgCarrierFulfillDuration;
    }

    @ThriftField
    public void setAvgCarrierFulfillDuration(String avgCarrierFulfillDuration) {
        this.avgCarrierFulfillDuration = avgCarrierFulfillDuration;
    }

    @ThriftField(26)
    public String getRefAvgCarrierFulfillDuration() {
        return this.refAvgCarrierFulfillDuration;
    }

    @ThriftField
    public void setRefAvgCarrierFulfillDuration(String refAvgCarrierFulfillDuration) {
        this.refAvgCarrierFulfillDuration = refAvgCarrierFulfillDuration;
    }

    @ThriftField(27)
    public String getAvgDeliveryDuration() {
        return this.avgDeliveryDuration;
    }

    @ThriftField
    public void setAvgDeliveryDuration(String avgDeliveryDuration) {
        this.avgDeliveryDuration = avgDeliveryDuration;
    }

    @ThriftField(28)
    public String getRefAvgDeliveryDuration() {
        return this.refAvgDeliveryDuration;
    }

    @ThriftField
    public void setRefAvgDeliveryDuration(String refAvgDeliveryDuration) {
        this.refAvgDeliveryDuration = refAvgDeliveryDuration;
    }

    @ThriftField(29)
    public String getDeliveredRateIn25min() {
        return this.deliveredRateIn25min;
    }

    @ThriftField
    public void setDeliveredRateIn25min(String deliveredRateIn25min) {
        this.deliveredRateIn25min = deliveredRateIn25min;
    }

    @ThriftField(30)
    public String getRefDeliveredRateIn25min() {
        return this.refDeliveredRateIn25min;
    }

    @ThriftField
    public void setRefDeliveredRateIn25min(String refDeliveredRateIn25min) {
        this.refDeliveredRateIn25min = refDeliveredRateIn25min;
    }

    @ThriftField(31)
    public String getDeliveredRateIn45min() {
        return this.deliveredRateIn45min;
    }

    @ThriftField
    public void setDeliveredRateIn45min(String deliveredRateIn45min) {
        this.deliveredRateIn45min = deliveredRateIn45min;
    }

    @ThriftField(32)
    public String getRefDeliveredRateIn45min() {
        return this.refDeliveredRateIn45min;
    }

    @ThriftField
    public void setRefDeliveredRateIn45min(String refDeliveredRateIn45min) {
        this.refDeliveredRateIn45min = refDeliveredRateIn45min;
    }

    @ThriftField(33)
    public String getEtaOntimeRatioV2() {
        return etaOntimeRatioV2;
    }

    @ThriftField
    public void setEtaOntimeRatioV2(String etaOntimeRatioV2) {
        this.etaOntimeRatioV2 = etaOntimeRatioV2;
    }

    @ThriftField(34)
    public String getEtaBadOvertimeRatioV2() {
        return etaBadOvertimeRatioV2;
    }

    @ThriftField
    public void setEtaBadOvertimeRatioV2(String etaBadOvertimeRatioV2) {
        this.etaBadOvertimeRatioV2 = etaBadOvertimeRatioV2;
    }

    @ThriftField(35)
    public String getRefEtaOntimeRatioV2() {
        return refEtaOntimeRatioV2;
    }

    @ThriftField
    public void setRefEtaOntimeRatioV2(String refEtaOntimeRatioV2) {
        this.refEtaOntimeRatioV2 = refEtaOntimeRatioV2;
    }

    @ThriftField(36)
    public String getRefEtaBadOvertimeRatioV2() {
        return refEtaBadOvertimeRatioV2;
    }

    @ThriftField
    public void setRefEtaBadOvertimeRatioV2(String refEtaBadOvertimeRatioV2) {
        this.refEtaBadOvertimeRatioV2 = refEtaBadOvertimeRatioV2;
    }
}
