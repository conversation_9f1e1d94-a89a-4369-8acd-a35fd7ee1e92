package com.sankuai.shangou.logistics.delivery.seal.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/27 17:03
 **/
@Data
@ThriftStruct
public class QuerySealContainerLogByConditionRequest {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    private long tenantId;

    @FieldDoc(
            description = "仓id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    private List<Long> warehouseIds;

    @FieldDoc(
            description = "容具码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    private String sealContainerCode;

    @FieldDoc(
            description = "归还时间开始",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    private Long giveBackTimestampStart;

    @FieldDoc(
            description = "归还时间截止",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    private Long giveBackTimestampEnd;

    @FieldDoc(
            description = "页面大小",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    private int pageSize;

    @FieldDoc(
            description = "页码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(7)
    private int page;
}
