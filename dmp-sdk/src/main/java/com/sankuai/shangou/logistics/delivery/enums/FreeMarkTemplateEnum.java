package com.sankuai.shangou.logistics.delivery.enums;

import java.util.Objects;

public enum FreeMarkTemplateEnum {

    ;

    private final Integer type;
    private final String desc;
    FreeMarkTemplateEnum(Integer type,String desc){
        this.type = type;
        this.desc = desc;
    }

    public final Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static FreeMarkTemplateEnum typeToEnum(Integer type){
        if(type == null){
            return null;
        }
        for (FreeMarkTemplateEnum templateEnum : values()){
            if(Objects.equals(type,templateEnum.getType())){
                return templateEnum;
            }
        }
        return null;
    }
}

