package com.sankuai.shangou.logistics.delivery.configure.value;

import lombok.Data;

/**
 * 配送提醒设置配置
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@Data
public class DeliveryRemindConfig {

    /**
     * 领取超时时间（分钟），0为未开启
     */
    private Integer receiveTimeOutMins;

    /**
     * 取货超时时间（分钟），0为未开启
     */
    private Integer takenTimeOutMins;

    /**
     * 即将超时时间（分钟），0为未开启
     */
    private Integer soonDeliveryTimeoutMinsBeforeEta;

    /**
     * 配送超时配置
     */
    private DeliveryTimeOutsBeforeEtaConfig deliveryTimeOutsBeforeEtaConfig;

    /**
     * 配送超时配置
     */
    @Data
    public static class DeliveryTimeOutsBeforeEtaConfig {

        /**
         * 立即单配送超时时间（分钟）
         */
        private Integer immediateBeforeEtaMins;

        /**
         * 预约单配送超时时间（分钟）
         */
        private Integer bookingBeforeEtaMins;
    }

    public static DeliveryRemindConfig makeDefault() {
        DeliveryRemindConfig config = new DeliveryRemindConfig();

        // 设置超时时间，-1表示未开启
        config.setReceiveTimeOutMins(-1);
        config.setTakenTimeOutMins(-1);
        config.setSoonDeliveryTimeoutMinsBeforeEta(-1);

        // 配送超时配置
        DeliveryTimeOutsBeforeEtaConfig timeOutsConfig = new DeliveryTimeOutsBeforeEtaConfig();
        timeOutsConfig.setImmediateBeforeEtaMins(-1);
        timeOutsConfig.setBookingBeforeEtaMins(-1);

        config.setDeliveryTimeOutsBeforeEtaConfig(timeOutsConfig);

        return config;
    }
}

