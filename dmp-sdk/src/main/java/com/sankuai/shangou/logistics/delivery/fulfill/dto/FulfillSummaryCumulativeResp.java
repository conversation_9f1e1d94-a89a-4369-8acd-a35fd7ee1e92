package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/27
 **/
@ThriftStruct
@TypeDoc(
    description = "履约配送数据看板-累计数据响应"
)
@NoArgsConstructor
@AllArgsConstructor
public class FulfillSummaryCumulativeResp implements Serializable {
    @FieldDoc(description = "有效订单量（美团渠道+微商城渠道）")
    private Integer totalValidOrder;

    @FieldDoc(description = "对比值-有效订单量（美团渠道+微商城渠道）")
    private Integer refTotalValidOrder;

    @FieldDoc(description = "各个渠道的订单量")
    private List<ValidOrderCountChannelInfo> channelValidOrderCountList;

    @FieldDoc(description = "已送达订单量")
    private Integer deliveredOrder;

    @FieldDoc(description = "对比值-已送达订单量")
    private Integer refDeliveredOrder;

    @FieldDoc(description = "严重超时订单量")
    private Integer seriousTimeoutOrder;

    @FieldDoc(description = "对比值-严重超时订单量")
    private Integer refSeriousTimeoutOrder;

    @FieldDoc(description = "考核超时订单量")
    private Integer judgeTimeoutOrder;

    @FieldDoc(description = "对比值-考核超时订单量")
    private Integer refJudgeTimeoutOrder;

    @FieldDoc(description = "当日出勤人数")
    private Integer workedEmployeeNum;

    @FieldDoc(description = "对比值-当日出勤人数")
    private Integer refWorkedEmployee;

    @FieldDoc(description = "九分位履约时长")
    private String ninetiethFulfillDuration;

    @FieldDoc(description = "对比值-九分位履约时长 单位：分钟")
    private String refNinetyFulfillDuration;

    @FieldDoc(description = "整单履约时长 单位：分钟")
    private String avgFulfillDuration;

    @FieldDoc(description = "对比值-整单履约时长 单位：分钟")
    private String refAvgFulfillDuration;

    @FieldDoc(description = "25分钟送达率")
    private String deliveredRateIn25min;

    @FieldDoc(description = "对比值-25分钟送达率")
    private String refDeliveredRateIn25min;

    @FieldDoc(description = "15分钟送达率")
    private String deliveredRateIn15min;

    @FieldDoc(description = "对比值-15分钟送达率")
    private String refDeliveredRateIn15min;

    @FieldDoc(description = "严重超标率")
    private String seriousTimeoutRatio;

    @FieldDoc(description = "对比值-严重超标率")
    private String refSeriousTimeoutRatio;

    @FieldDoc(description = "ETA超时率")
    private String etaTimeoutRatio;

    @FieldDoc(description = "对比值-ETA超时率")
    private String refEtaTimeoutRatio;

    @FieldDoc(description = "ETA严重超时订单量")
    private Long etaOvertimeOrdNumV2;

    @FieldDoc(description = "ETA严重超时订单量")
    private Long etaBadOvertimeOrdNumV2;

    @FieldDoc(description = "对比ETA超时订单量")
    private Long refEtaOvertimeOrdNumV2;

    @FieldDoc(description = "对比ETA严重超时订单量")
    private Long refEtaBadOvertimeOrdNumV2;

    @FieldDoc(description = "对比ETA超时订单率")
    private String refEtaOntimeRatioV2;

    @FieldDoc(description = "对比ETA严重超时订单率")
    private String refEtaBadOvertimeRatioV2;

    @FieldDoc(description = "ETA准时率")
    private String etaOntimeRatioV2;

    @FieldDoc(description = "ETA严重超时率")
    private String etaBadOvertimeRatioV2;


    @ThriftField(1)
    public Integer getTotalValidOrder() {
        return this.totalValidOrder;
    }

    @ThriftField
    public void setTotalValidOrder(Integer totalValidOrder) {
        this.totalValidOrder = totalValidOrder;
    }

    @ThriftField(2)
    public Integer getRefTotalValidOrder() {
        return this.refTotalValidOrder;
    }

    @ThriftField
    public void setRefTotalValidOrder(Integer refTotalValidOrder) {
        this.refTotalValidOrder = refTotalValidOrder;
    }

    @ThriftField(3)
    public List<ValidOrderCountChannelInfo> getChannelValidOrderCountList() {
        return this.channelValidOrderCountList;
    }

    @ThriftField
    public void setChannelValidOrderCountList(List<ValidOrderCountChannelInfo> channelValidOrderCountList) {
        this.channelValidOrderCountList = channelValidOrderCountList;
    }

    @ThriftField(4)
    public Integer getDeliveredOrder() {
        return this.deliveredOrder;
    }

    @ThriftField
    public void setDeliveredOrder(Integer deliveredOrder) {
        this.deliveredOrder = deliveredOrder;
    }

    @ThriftField(5)
    public Integer getRefDeliveredOrder() {
        return this.refDeliveredOrder;
    }

    @ThriftField
    public void setRefDeliveredOrder(Integer refDeliveredOrder) {
        this.refDeliveredOrder = refDeliveredOrder;
    }

    @ThriftField(6)
    public Integer getSeriousTimeoutOrder() {
        return this.seriousTimeoutOrder;
    }

    @ThriftField
    public void setSeriousTimeoutOrder(Integer seriousTimeoutOrder) {
        this.seriousTimeoutOrder = seriousTimeoutOrder;
    }

    @ThriftField(7)
    public Integer getRefSeriousTimeoutOrder() {
        return this.refSeriousTimeoutOrder;
    }

    @ThriftField
    public void setRefSeriousTimeoutOrder(Integer refSeriousTimeoutOrder) {
        this.refSeriousTimeoutOrder = refSeriousTimeoutOrder;
    }

    @ThriftField(8)
    public Integer getJudgeTimeoutOrder() {
        return this.judgeTimeoutOrder;
    }

    @ThriftField
    public void setJudgeTimeoutOrder(Integer judgeTimeoutOrder) {
        this.judgeTimeoutOrder = judgeTimeoutOrder;
    }

    @ThriftField(9)
    public Integer getRefJudgeTimeoutOrder() {
        return this.refJudgeTimeoutOrder;
    }

    @ThriftField
    public void setRefJudgeTimeoutOrder(Integer refJudgeTimeoutOrder) {
        this.refJudgeTimeoutOrder = refJudgeTimeoutOrder;
    }

    @ThriftField(10)
    public Integer getWorkedEmployeeNum() {
        return this.workedEmployeeNum;
    }

    @ThriftField
    public void setWorkedEmployeeNum(Integer workedEmployeeNum) {
        this.workedEmployeeNum = workedEmployeeNum;
    }

    @ThriftField(11)
    public Integer getRefWorkedEmployee() {
        return this.refWorkedEmployee;
    }

    @ThriftField
    public void setRefWorkedEmployee(Integer refWorkedEmployee) {
        this.refWorkedEmployee = refWorkedEmployee;
    }

    @ThriftField(12)
    public String getNinetiethFulfillDuration() {
        return this.ninetiethFulfillDuration;
    }

    @ThriftField
    public void setNinetiethFulfillDuration(String ninetiethFulfillDuration) {
        this.ninetiethFulfillDuration = ninetiethFulfillDuration;
    }

    @ThriftField(13)
    public String getRefNinetyFulfillDuration() {
        return this.refNinetyFulfillDuration;
    }

    @ThriftField
    public void setRefNinetyFulfillDuration(String refNinetyFulfillDuration) {
        this.refNinetyFulfillDuration = refNinetyFulfillDuration;
    }

    @ThriftField(14)
    public String getAvgFulfillDuration() {
        return this.avgFulfillDuration;
    }

    @ThriftField
    public void setAvgFulfillDuration(String avgFulfillDuration) {
        this.avgFulfillDuration = avgFulfillDuration;
    }

    @ThriftField(15)
    public String getRefAvgFulfillDuration() {
        return this.refAvgFulfillDuration;
    }

    @ThriftField
    public void setRefAvgFulfillDuration(String refAvgFulfillDuration) {
        this.refAvgFulfillDuration = refAvgFulfillDuration;
    }

    @ThriftField(16)
    public String getDeliveredRateIn25min() {
        return this.deliveredRateIn25min;
    }

    @ThriftField
    public void setDeliveredRateIn25min(String deliveredRateIn25min) {
        this.deliveredRateIn25min = deliveredRateIn25min;
    }

    @ThriftField(17)
    public String getRefDeliveredRateIn25min() {
        return this.refDeliveredRateIn25min;
    }

    @ThriftField
    public void setRefDeliveredRateIn25min(String refDeliveredRateIn25min) {
        this.refDeliveredRateIn25min = refDeliveredRateIn25min;
    }

    @ThriftField(18)
    public String getDeliveredRateIn15min() {
        return this.deliveredRateIn15min;
    }

    @ThriftField
    public void setDeliveredRateIn15min(String deliveredRateIn15min) {
        this.deliveredRateIn15min = deliveredRateIn15min;
    }

    @ThriftField(19)
    public String getRefDeliveredRateIn15min() {
        return this.refDeliveredRateIn15min;
    }

    @ThriftField
    public void setRefDeliveredRateIn15min(String refDeliveredRateIn15min) {
        this.refDeliveredRateIn15min = refDeliveredRateIn15min;
    }

    @ThriftField(20)
    public String getSeriousTimeoutRatio() {
        return this.seriousTimeoutRatio;
    }

    @ThriftField
    public void setSeriousTimeoutRatio(String seriousTimeoutRatio) {
        this.seriousTimeoutRatio = seriousTimeoutRatio;
    }

    @ThriftField(21)
    public String getRefSeriousTimeoutRatio() {
        return this.refSeriousTimeoutRatio;
    }

    @ThriftField
    public void setRefSeriousTimeoutRatio(String refSeriousTimeoutRatio) {
        this.refSeriousTimeoutRatio = refSeriousTimeoutRatio;
    }

    @ThriftField(22)
    public String getEtaTimeoutRatio() {
        return this.etaTimeoutRatio;
    }

    @ThriftField
    public void setEtaTimeoutRatio(String etaTimeoutRatio) {
        this.etaTimeoutRatio = etaTimeoutRatio;
    }

    @ThriftField(23)
    public String getRefEtaTimeoutRatio() {
        return this.refEtaTimeoutRatio;
    }

    @ThriftField
    public void setRefEtaTimeoutRatio(String refEtaTimeoutRatio) {
        this.refEtaTimeoutRatio = refEtaTimeoutRatio;
    }

    @ThriftField(24)
    public Long getEtaOvertimeOrdNumV2() {
        return etaOvertimeOrdNumV2;
    }

    @ThriftField
    public void setEtaOvertimeOrdNumV2(Long etaOvertimeOrdNumV2) {
        this.etaOvertimeOrdNumV2 = etaOvertimeOrdNumV2;
    }

    @ThriftField(25)
    public Long getEtaBadOvertimeOrdNumV2() {
        return etaBadOvertimeOrdNumV2;
    }

    @ThriftField
    public void setEtaBadOvertimeOrdNumV2(Long etaBadOvertimeOrdNumV2) {
        this.etaBadOvertimeOrdNumV2 = etaBadOvertimeOrdNumV2;
    }

    @ThriftField(26)
    public Long getRefEtaOvertimeOrdNumV2() {
        return refEtaOvertimeOrdNumV2;
    }

    @ThriftField
    public void setRefEtaOvertimeOrdNumV2(Long refEtaOvertimeOrdNumV2) {
        this.refEtaOvertimeOrdNumV2 = refEtaOvertimeOrdNumV2;
    }

    @ThriftField(27)
    public Long getRefEtaBadOvertimeOrdNumV2() {
        return refEtaBadOvertimeOrdNumV2;
    }

    @ThriftField
    public void setRefEtaBadOvertimeOrdNumV2(Long refEtaBadOvertimeOrdNumV2) {
        this.refEtaBadOvertimeOrdNumV2 = refEtaBadOvertimeOrdNumV2;

    }

    @ThriftField(28)
    public String getRefEtaOntimeRatioV2() {
        return refEtaOntimeRatioV2;
    }

    @ThriftField
    public void setRefEtaOntimeRatioV2(String refEtaOntimeRatioV2) {
        this.refEtaOntimeRatioV2 = refEtaOntimeRatioV2;
    }

    @ThriftField(29)
    public String getRefEtaBadOvertimeRatioV2() {
        return refEtaBadOvertimeRatioV2;
    }

    @ThriftField
    public void setRefEtaBadOvertimeRatioV2(String refEtaBadOvertimeRatioV2) {
        this.refEtaBadOvertimeRatioV2 = refEtaBadOvertimeRatioV2;
    }

    @ThriftField(30)
    public String getEtaOntimeRatioV2() {
        return etaOntimeRatioV2;
    }

    @ThriftField
    public void setEtaOntimeRatioV2(String etaOntimeRatioV2) {
        this.etaOntimeRatioV2 = etaOntimeRatioV2;
    }

    @ThriftField(31)
    public String getEtaBadOvertimeRatioV2() {
        return etaBadOvertimeRatioV2;
    }

    public void setEtaBadOvertimeRatioV2(String etaBadOvertimeRatioV2) {
        this.etaBadOvertimeRatioV2 = etaBadOvertimeRatioV2;
    }
}
