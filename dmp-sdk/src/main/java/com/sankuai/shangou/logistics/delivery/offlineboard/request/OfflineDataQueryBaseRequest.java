package com.sankuai.shangou.logistics.delivery.offlineboard.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/2/15 15:32
 **/

@AllArgsConstructor
@NoArgsConstructor
@Data
@TypeDoc(
        description = "员工维度数据查询请求"
)
public class OfflineDataQueryBaseRequest {

    @FieldDoc(description = "门店id列表")
    private List<Long> storeIds;

    @FieldDoc(description = "开始时间")
    private Long startDate;

    @FieldDoc(description = "结束时间")
    private Long endDate;

    @FieldDoc(description = "排序字段,前端页面排序")
    private String sortField;

    @FieldDoc(description = "排序类型 1-升序 2-降序， 前端页面排序")
    private Integer sortType = 1;

    @FieldDoc(description = "页码")
    private Integer pageNo = 1;

    @FieldDoc(description = "分页大小,默认为10")
    private Integer pageSize = 10;

    //前端不传
    private List<SortRule> sortRuleList;

    @FieldDoc(description = "经营类型列表")
    private List<Integer> operationModes;

    private String version;

    @FieldDoc(description = "异常类型")
    private List<String> abnormalTypes ;

    public Optional<String> validate() {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Optional.of("门店id列表不能为空");
        }

        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return Optional.of("查询开始时间或结束时间不能为空");
        }

        if ((endDate - startDate) / 1000 / 3600 / 24 > 93) {
            return Optional.of("查询时间范围不能超过三个月");
        }

        return Optional.empty();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SortRule {

        private String sortField;

        private Integer sortType;

    }
}
