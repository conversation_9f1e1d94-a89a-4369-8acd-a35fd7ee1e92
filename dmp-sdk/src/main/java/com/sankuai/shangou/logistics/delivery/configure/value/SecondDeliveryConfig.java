package com.sankuai.shangou.logistics.delivery.configure.value;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-16
 * @email <EMAIL>
 */
@Data
public class SecondDeliveryConfig {

    @FieldDoc(description = "二级平台编码")
    private List<Integer> secondPlatformCodeCode;

    @FieldDoc(description = "禁用条件")
    private SecondDeliveryForbiddenCondition forbiddenCondition;

}
