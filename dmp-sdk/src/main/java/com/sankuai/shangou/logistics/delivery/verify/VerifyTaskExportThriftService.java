package com.sankuai.shangou.logistics.delivery.verify;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.verify.dto.TPageDTO;
import com.sankuai.shangou.logistics.delivery.verify.dto.VerifyTaskViewAndExportDTO;
import com.sankuai.shangou.logistics.delivery.verify.request.VerifyTaskExportRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/15 12:04
 **/
@ThriftService
public interface VerifyTaskExportThriftService {
    @ThriftMethod
    TResult<TPageDTO<VerifyTaskViewAndExportDTO>> exportVerifyTaskList(VerifyTaskExportRequest request);
}
