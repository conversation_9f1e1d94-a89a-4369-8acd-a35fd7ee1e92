package com.sankuai.shangou.logistics.delivery.configure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 已送达列表排序模式枚举
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@Getter
@AllArgsConstructor
public enum CompletedSortModeEnum {

    /**
     * 实际送达时间排序
     */
    ACTUAL_DELIVERY_TIME(1, "实际送达时间"),

    /**
     * 预计送达时间排序
     */
    EXPECTED_DELIVERY_TIME(2, "预计送达时间");

    /**
     * 排序模式值
     */
    private final Integer mode;

    /**
     * 排序模式描述
     */
    private final String description;

    /**
     * 根据模式值获取枚举
     *
     * @param mode 模式值
     * @return 对应的枚举
     * @throws IllegalArgumentException 当模式值不存在时抛出异常
     */
    public static CompletedSortModeEnum getByMode(Integer mode) {
        if (mode == null) {
            return null;
        }
        for (CompletedSortModeEnum modeEnum : values()) {
            if (modeEnum.getMode().equals(mode)) {
                return modeEnum;
            }
        }
        throw new IllegalArgumentException("未知的已送达列表排序模式: " + mode);
    }
}

