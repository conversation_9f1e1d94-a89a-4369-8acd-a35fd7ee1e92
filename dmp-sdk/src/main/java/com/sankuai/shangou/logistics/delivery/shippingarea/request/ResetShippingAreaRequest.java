package com.sankuai.shangou.logistics.delivery.shippingarea.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/11 15:43
 **/
@ThriftStruct
public class ResetShippingAreaRequest {

    @FieldDoc(description = "租户id",
            requiredness = Requiredness.REQUIRED)
    private Long tenantId;

    @FieldDoc(description = "中台门店id",
            requiredness = Requiredness.REQUIRED)
    private Long storeId;

    @FieldDoc(description = "渠道门店配送范围dto",
            requiredness = Requiredness.REQUIRED)
    private List<ChannelShippingAreaParam> channelShippingAreaParams;


    @ThriftField(1)
    public Long getTenantId() {
        return this.tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(2)
    public Long getStoreId() {
        return this.storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(3)
    public List<ChannelShippingAreaParam> getChannelRegionDTOS() {
        return this.channelShippingAreaParams;
    }

    @ThriftField
    public void setChannelRegionDTOS(List<ChannelShippingAreaParam> channelShippingAreaParams) {
        this.channelShippingAreaParams = channelShippingAreaParams;
    }

    @ThriftStruct
    public static class ChannelShippingAreaParam {
        @FieldDoc(description = "渠道id",
                requiredness = Requiredness.REQUIRED)
        private Integer channelId;

        @FieldDoc(description = "配送范围,格式:经度,纬度,经度,纬度,经度,纬度...",
                requiredness = Requiredness.REQUIRED)
        private String deliveryRegion;

        @FieldDoc(description = "起送价 单位:元",
                requiredness = Requiredness.REQUIRED)
        private Double minPrice;

        @FieldDoc(description = "配送费 单位:元",
                requiredness = Requiredness.REQUIRED)
        private Double shippingFee;

        @FieldDoc(description = "范围标签列表，1:大范围（目前只支持'大范围'标签）")
        private List<Integer> shippingTagList;

        @ThriftField(1)
        public Integer getChannelId() {
            return this.channelId;
        }

        @ThriftField
        public void setChannelId(Integer channelId) {
            this.channelId = channelId;
        }

        @ThriftField(2)
        public String getDeliveryRegion() {
            return this.deliveryRegion;
        }

        @ThriftField
        public void setDeliveryRegion(String deliveryRegion) {
            this.deliveryRegion = deliveryRegion;
        }

        @ThriftField(3)
        public Double getMinPrice() {
            return this.minPrice;
        }

        @ThriftField
        public void setMinPrice(Double minPrice) {
            this.minPrice = minPrice;
        }

        @ThriftField(4)
        public Double getShippingFee() {
            return this.shippingFee;
        }

        @ThriftField
        public void setShippingFee(Double shippingFee) {
            this.shippingFee = shippingFee;
        }

        @ThriftField(5)
        public List<Integer> getShippingTagList() {
            return this.shippingTagList;
        }

        @ThriftField
        public void setShippingTagList(List<Integer> shippingTagList) {
            this.shippingTagList = shippingTagList;
        }

        public String validate() {
            if (channelId == null) {
                return "渠道id为空";
            }

            if (StringUtils.isBlank(deliveryRegion)) {
                return "配送范围不能为空";
            }

            if (minPrice == null) {
                return "起送价不能为空";
            }

            if (shippingFee == null) {
                return "配送费不能为空";
            }

            return null;
        }
    }

    public String validate() {
        if (tenantId == null) {
            return "租户id不合法";
        }

        if (storeId == null) {
            return "门店id不合法";
        }

        if (CollectionUtils.isEmpty(channelShippingAreaParams)) {
            return "渠道配送范围不能为空";
        }

        for (ChannelShippingAreaParam param : channelShippingAreaParams) {
            String errMsg = param.validate();
            if (StringUtils.isNotBlank(errMsg)) {
                return errMsg;
            }
        }

        return null;
    }

}
