package com.sankuai.shangou.logistics.delivery.offlineboard.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/2/15 11:54
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@TypeDoc(
        description = "非实时履约数据看板基础查询请求"
)
public class OfflineSummaryFulfillDataQueryRequest {

    @FieldDoc(description = "门店id列表")
    private List<Long> storeIds;

    @FieldDoc(description = "开始时间")
    private Long startDate;

    @FieldDoc(description = "结束时间")
    private Long endDate;

    public Optional<String> validate() {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Optional.of("门店id列表不能为空");
        }

        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return Optional.of("查询开始时间或结束时间不能为空");
        }

        if ((endDate - startDate) / 1000 / 3600 / 24 > 93) {
            return Optional.of("查询时间范围不能超过三个月");
        }

        return Optional.empty();
    }
}
