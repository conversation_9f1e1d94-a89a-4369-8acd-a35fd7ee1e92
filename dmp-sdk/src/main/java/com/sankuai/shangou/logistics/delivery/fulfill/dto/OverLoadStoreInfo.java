package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@ThriftStruct
@TypeDoc(
        description = "超载的门店信息", authors = {"yujing10"}
)
@AllArgsConstructor
@NoArgsConstructor
public class OverLoadStoreInfo implements Serializable {

    @FieldDoc(description = "门店id")
    private Long storeId;

    @FieldDoc(description = "超载的门店名称")
    private String storeName;

    @FieldDoc(description = "履约中人均负载（当前门店）")
    private String avgRiderLoad;


    @ThriftField(1)
    public Long getStoreId() {
        return this.storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(2)
    public String getStoreName() {
        return this.storeName;
    }

    @ThriftField
    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    @ThriftField(3)
    public String getAvgRiderLoad() {
        return this.avgRiderLoad;
    }

    @ThriftField
    public void setAvgRiderLoad(String avgRiderLoad) {
        this.avgRiderLoad = avgRiderLoad;
    }
}
