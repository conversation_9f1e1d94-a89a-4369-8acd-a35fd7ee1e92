package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.util.List;

@ThriftStruct
@TypeDoc(
        description = "履约配送数据看板：骑手维度统计数据", authors = {"yujing10"}
)
public class FulfillRiderStatisticsDTO {

    @FieldDoc(description = "骑手账号名")
    private String riderAccountName;

    @FieldDoc(description = "骑手名称")
    private String riderName;

    @FieldDoc(description = "配送中（骑手已取货）订单量")
    private Integer delivering;

    @FieldDoc(description = "已送达订单量")
    private Integer deliveredOrder;

    @FieldDoc(description = "配送中（骑手已取货）考核超时订单量")
    private Integer deliveringJudgeTimeoutOrder;

    @FieldDoc(description = "九分位履约时长")
    private String ninetiethFulfillDuration;

    @FieldDoc(description = "平均履约时长")
    private String avgFulfillDuration;

    @FieldDoc(description = "考核超时订单量")
    private Integer judgeTimeoutOrder;

    @FieldDoc(description = "考核超时订单列表")
    private List<DeliveryOrderChannelInfo> judgeTimeoutOrderList;

    @FieldDoc(description = "严重超时订单量")
    private Integer seriousTimeoutOrder;

    @FieldDoc(description = "严重超时订单列表")
    private List<DeliveryOrderChannelInfo> seriousTimeoutOrderList;

    @FieldDoc(description = "25分钟送达率")
    private String deliveredRateIn25Min;

    @FieldDoc(description = "15分钟送达率")
    private String deliveredRateIn15Min;

    @FieldDoc(description = "门店+1组织节点")
    private String upOneLevelDepartmentName;

    @FieldDoc(description = "门店+2组织节点")
    private String upTwoLevelDepartmentName;

    @FieldDoc(description = "岗位")
    private List<String> positionNameList;

    @FieldDoc(description = "在职状态")
    private Integer employeeStatus;

    @FieldDoc(description = "在职天数")
    private Integer onboardDays;

    @FieldDoc(description = "当月出勤天数")
    private Integer attendanceDaysOfCurMonth;

    @FieldDoc(description = "eta超时订单率")
    private String etaTimeoutRate;

    @FieldDoc(description = "eta严重超时订单率")
    private String etaSeriouslyTimeoutRate;

    @FieldDoc(description = "拣货中（骑手已接单）订单量")
    public Integer picking;

    @FieldDoc(description = "拣货中（骑手已接单）考核超时订单量")
    public Integer pickingJudgeTimeout;

    @FieldDoc(description = "25分钟送达率+%")
    private String deliveredRateIn25MinDesc;

    @FieldDoc(description = "15分钟送达率+%")
    private String deliveredRateIn15MinDesc;

    @FieldDoc(description = "eta严重超时订单率+%")
    private String etaSeriouslyTimeoutRateDesc;

    @FieldDoc(description = "eta超时订单率+%")
    private String etaTimeoutRateDesc;

    @FieldDoc(description = "在职状态描述")
    private String employeeStatusDesc;

    @FieldDoc(description = "门店名")
    private String storeName;

    @FieldDoc(description = "ETA超时订单量")
    private Long etaOvertimeOrdNumV2;

    @FieldDoc(description = "ETA严重超时订单量 ")
    private Long etaBadOvertimeOrdNumV2;

    @FieldDoc(description = "拣货中ETA超时订单量")
    private Long pickingEtaOvertimeOrderV2;

    @FieldDoc(description = "配送中ETA超时订单量")
    private Long deliveringEtaOvertimeOrderV2;

    @FieldDoc(description = "ETA准时率")
    private String etaOntimeRatioV2;

    @FieldDoc(description = "ETA严重超时率")
    private String etaBadOvertimeRatioV2;

    @FieldDoc(description = "ETA准时率+%")
    private String etaOntimeRatioV2Desc;

    @FieldDoc(description = "ETA严重超时率+%")
    private String etaBadOvertimeRatioV2Desc;

    @ThriftField(1)
    public String getRiderAccountName() {
        return this.riderAccountName;
    }

    @ThriftField
    public void setRiderAccountName(String riderAccountName) {
        this.riderAccountName = riderAccountName;
    }

    @ThriftField(2)
    public String getRiderName() {
        return this.riderName;
    }

    @ThriftField
    public void setRiderName(String riderName) {
        this.riderName = riderName;
    }

    @ThriftField(3)
    public Integer getDelivering() {
        return this.delivering;
    }

    @ThriftField
    public void setDelivering(Integer delivering) {
        this.delivering = delivering;
    }

    @ThriftField(4)
    public Integer getDeliveredOrder() {
        return this.deliveredOrder;
    }

    @ThriftField
    public void setDeliveredOrder(Integer deliveredOrder) {
        this.deliveredOrder = deliveredOrder;
    }

    @ThriftField(5)
    public Integer getDeliveringJudgeTimeoutOrder() {
        return this.deliveringJudgeTimeoutOrder;
    }

    @ThriftField
    public void setDeliveringJudgeTimeoutOrder(Integer deliveringJudgeTimeoutOrder) {
        this.deliveringJudgeTimeoutOrder = deliveringJudgeTimeoutOrder;
    }

    @ThriftField(6)
    public String getNinetiethFulfillDuration() {
        return this.ninetiethFulfillDuration;
    }

    @ThriftField
    public void setNinetiethFulfillDuration(String ninetiethFulfillDuration) {
        this.ninetiethFulfillDuration = ninetiethFulfillDuration;
    }

    @ThriftField(7)
    public String getAvgFulfillDuration() {
        return this.avgFulfillDuration;
    }

    @ThriftField
    public void setAvgFulfillDuration(String avgFulfillDuration) {
        this.avgFulfillDuration = avgFulfillDuration;
    }

    @ThriftField(8)
    public Integer getJudgeTimeoutOrder() {
        return this.judgeTimeoutOrder;
    }

    @ThriftField
    public void setJudgeTimeoutOrder(Integer judgeTimeoutOrder) {
        this.judgeTimeoutOrder = judgeTimeoutOrder;
    }

    @ThriftField(9)
    public List<DeliveryOrderChannelInfo> getJudgeTimeoutOrderList() {
        return this.judgeTimeoutOrderList;
    }

    @ThriftField
    public void setJudgeTimeoutOrderList(List<DeliveryOrderChannelInfo> judgeTimeoutOrderList) {
        this.judgeTimeoutOrderList = judgeTimeoutOrderList;
    }

    @ThriftField(10)
    public Integer getSeriousTimeoutOrder() {
        return this.seriousTimeoutOrder;
    }

    @ThriftField
    public void setSeriousTimeoutOrder(Integer seriousTimeoutOrder) {
        this.seriousTimeoutOrder = seriousTimeoutOrder;
    }

    @ThriftField(11)
    public List<DeliveryOrderChannelInfo> getSeriousTimeoutOrderList() {
        return this.seriousTimeoutOrderList;
    }

    @ThriftField
    public void setSeriousTimeoutOrderList(List<DeliveryOrderChannelInfo> seriousTimeoutOrderList) {
        this.seriousTimeoutOrderList = seriousTimeoutOrderList;
    }

    @ThriftField(12)
    public String getDeliveredRateIn25Min() {
        return this.deliveredRateIn25Min;
    }

    @ThriftField
    public void setDeliveredRateIn25Min(String deliveredRateIn25Min) {
        this.deliveredRateIn25Min = deliveredRateIn25Min;
    }

    @ThriftField(13)
    public String getDeliveredRateIn15Min() {
        return this.deliveredRateIn15Min;
    }

    @ThriftField
    public void setDeliveredRateIn15Min(String deliveredRateIn15Min) {
        this.deliveredRateIn15Min = deliveredRateIn15Min;
    }

    @ThriftField(14)
    public String getUpOneLevelDepartmentName() {
        return this.upOneLevelDepartmentName;
    }

    @ThriftField
    public void setUpOneLevelDepartmentName(String upOneLevelDepartmentName) {
        this.upOneLevelDepartmentName = upOneLevelDepartmentName;
    }

    @ThriftField(15)
    public String getUpTwoLevelDepartmentName() {
        return this.upTwoLevelDepartmentName;
    }

    @ThriftField
    public void setUpTwoLevelDepartmentName(String upTwoLevelDepartmentName) {
        this.upTwoLevelDepartmentName = upTwoLevelDepartmentName;
    }

    @ThriftField(16)
    public List<String> getPositionNameList() {
        return this.positionNameList;
    }

    @ThriftField
    public void setPositionNameList(List<String> positionNameList) {
        this.positionNameList = positionNameList;
    }

    @ThriftField(17)
    public Integer getEmployeeStatus() {
        return this.employeeStatus;
    }

    @ThriftField
    public void setEmployeeStatus(Integer employeeStatus) {
        this.employeeStatus = employeeStatus;
    }

    @ThriftField(18)
    public Integer getOnboardDays() {
        return this.onboardDays;
    }

    @ThriftField
    public void setOnboardDays(Integer onboardDays) {
        this.onboardDays = onboardDays;
    }

    @ThriftField(19)
    public Integer getAttendanceDaysOfCurMonth() {
        return this.attendanceDaysOfCurMonth;
    }

    @ThriftField
    public void setAttendanceDaysOfCurMonth(Integer attendanceDaysOfCurMonth) {
        this.attendanceDaysOfCurMonth = attendanceDaysOfCurMonth;
    }

    @ThriftField(20)
    public String getEtaTimeoutRate() {
        return this.etaTimeoutRate;
    }

    @ThriftField
    public void setEtaTimeoutRate(String etaTimeoutRate) {
        this.etaTimeoutRate = etaTimeoutRate;
    }

    @ThriftField(21)
    public String getEtaSeriouslyTimeoutRate() {
        return this.etaSeriouslyTimeoutRate;
    }

    @ThriftField
    public void setEtaSeriouslyTimeoutRate(String etaSeriouslyTimeoutRate) {
        this.etaSeriouslyTimeoutRate = etaSeriouslyTimeoutRate;
    }


    @ThriftField(22)
    public Integer getPicking() {
        return this.picking;
    }

    @ThriftField
    public void setPicking(Integer picking) {
        this.picking = picking;
    }

    @ThriftField(23)
    public Integer getPickingJudgeTimeout() {
        return this.pickingJudgeTimeout;
    }

    @ThriftField
    public void setPickingJudgeTimeout(Integer pickingJudgeTimeout) {
        this.pickingJudgeTimeout = pickingJudgeTimeout;
    }

    @ThriftField(24)
    public String getDeliveredRateIn25MinDesc() {
        return this.deliveredRateIn25MinDesc;
    }

    @ThriftField
    public void setDeliveredRateIn25MinDesc(String deliveredRateIn25MinDesc) {
        this.deliveredRateIn25MinDesc = deliveredRateIn25MinDesc;
    }

    @ThriftField(25)
    public String getDeliveredRateIn15MinDesc() {
        return this.deliveredRateIn15MinDesc;
    }

    @ThriftField
    public void setDeliveredRateIn15MinDesc(String deliveredRateIn15MinDesc) {
        this.deliveredRateIn15MinDesc = deliveredRateIn15MinDesc;
    }

    @ThriftField(26)
    public String getEtaSeriouslyTimeoutRateDesc() {
        return this.etaSeriouslyTimeoutRateDesc;
    }

    @ThriftField
    public void setEtaSeriouslyTimeoutRateDesc(String etaSeriouslyTimeoutRateDesc) {
        this.etaSeriouslyTimeoutRateDesc = etaSeriouslyTimeoutRateDesc;
    }

    @ThriftField(27)
    public String getEtaTimeoutRateDesc() {
        return this.etaTimeoutRateDesc;
    }

    @ThriftField
    public void setEtaTimeoutRateDesc(String etaTimeoutRateDesc) {
        this.etaTimeoutRateDesc = etaTimeoutRateDesc;
    }

    @ThriftField(28)
    public String getEmployeeStatusDesc() {
        return this.employeeStatusDesc;
    }

    @ThriftField
    public void setEmployeeStatusDesc(String employeeStatusDesc) {
        this.employeeStatusDesc = employeeStatusDesc;
    }


    @ThriftField(29)
    public String getStoreName() {
        return this.storeName;
    }

    @ThriftField
    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    @ThriftField(30)
    public Long getEtaOvertimeOrdNumV2() {
        return etaOvertimeOrdNumV2;
    }

    @ThriftField
    public void setEtaOvertimeOrdNumV2(Long etaOvertimeOrdNumV2) {
        this.etaOvertimeOrdNumV2 = etaOvertimeOrdNumV2;
    }

    @ThriftField(31)
    public Long getEtaBadOvertimeOrdNumV2() {
        return etaBadOvertimeOrdNumV2;
    }

    @ThriftField
    public void setEtaBadOvertimeOrdNumV2(Long etaBadOvertimeOrdNumV2) {
        this.etaBadOvertimeOrdNumV2 = etaBadOvertimeOrdNumV2;
    }

    @ThriftField(32)
    public Long getPickingEtaOvertimeOrderV2() {
        return pickingEtaOvertimeOrderV2;
    }

    @ThriftField
    public void setPickingEtaOvertimeOrderV2(Long pickingEtaOvertimeOrderV2) {
        this.pickingEtaOvertimeOrderV2 = pickingEtaOvertimeOrderV2;
    }

    @ThriftField(33)
    public Long getDeliveringEtaOvertimeOrderV2() {
        return deliveringEtaOvertimeOrderV2;
    }

    @ThriftField
    public void setDeliveringEtaOvertimeOrderV2(Long deliveringEtaOvertimeOrderV2) {
        this.deliveringEtaOvertimeOrderV2 = deliveringEtaOvertimeOrderV2;
    }

    @ThriftField(34)
    public String getEtaOntimeRatioV2() {
        return etaOntimeRatioV2;
    }

    @ThriftField
    public void setEtaOntimeRatioV2(String etaOntimeRatioV2) {
        this.etaOntimeRatioV2 = etaOntimeRatioV2;
    }

    @ThriftField(35)
    public String getEtaBadOvertimeRatioV2() {
        return etaBadOvertimeRatioV2;
    }

    @ThriftField
    public void setEtaBadOvertimeRatioV2(String etaBadOvertimeRatioV2) {
        this.etaBadOvertimeRatioV2 = etaBadOvertimeRatioV2;
    }

    @ThriftField(36)
    public String getEtaOntimeRatioV2Desc() {
        return this.etaOntimeRatioV2Desc;
    }

    @ThriftField
    public void setEtaOntimeRatioV2Desc(String etaOntimeRatioV2Desc) {
        this.etaOntimeRatioV2Desc = etaOntimeRatioV2Desc;
    }

    @ThriftField(37)
    public String getEtaBadOvertimeRatioV2Desc() {
        return etaBadOvertimeRatioV2Desc;
    }

    @ThriftField
    public void setEtaBadOvertimeRatioV2Desc(String etaBadOvertimeRatioV2Desc) {
        this.etaBadOvertimeRatioV2Desc = etaBadOvertimeRatioV2Desc;
    }
}
