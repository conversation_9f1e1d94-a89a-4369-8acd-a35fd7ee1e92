package com.sankuai.shangou.logistics.delivery.alert.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description
 * @date 2025-05-15
 */
@Getter
public enum DeliveryAlertCallbackEnum {
    DELIVERY_LAUNCH_FAILED("launchFail", "配送发单失败"),
    ;
    private final String type;
    private final String desc;

    DeliveryAlertCallbackEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static DeliveryAlertCallbackEnum ofType(String type) {
        return Arrays.stream(DeliveryAlertCallbackEnum.values())
                .filter(e -> e.getType().equals(type))
                .findFirst()
                .orElse(null);
    }


}
