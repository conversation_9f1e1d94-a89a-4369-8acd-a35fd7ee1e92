package com.sankuai.shangou.logistics.delivery.verify.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/15 18:02
 **/
@Data
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
public class TPageDTO<T> {
    @ThriftField(1)
    private List<T> list;

    @ThriftField(2)
    private int page;

    @ThriftField(3)
    private int pageSize;

    @ThriftField(4)
    private int total;

    @ThriftField(5)
    private boolean hasMore;

    public TPageDTO(List<T> list, int page, int pageSize, int total) {
        this.pageSize = pageSize;
        this.page = page;
        this.total = total;
        this.list = list;
        this.hasMore = total > page * pageSize;
    }
}
