package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.request.TPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Optional;

@ThriftStruct
@Data
public class FulfillDataPageQueryDTO  {

    @FieldDoc(description = "查询页码", requiredness = Requiredness.REQUIRED)
    @ThriftField(1)
    @Deprecated
    public Integer page;

    @FieldDoc(description = "分页大小", requiredness = Requiredness.REQUIRED)
    @ThriftField(2)
    public Integer pageSize;

    @FieldDoc(description = "对比类型：1-日环比；2-周同比；3-月同比")
    @ThriftField(3)
    private Integer compareType = 1;

    @ThriftField(4)
    @FieldDoc(description = "门店id列表")
    private List<Long> storeIds;

    @ThriftField(5)
    @FieldDoc(description = "租户id")
    private Long tenantId;

    @ThriftField(6)
    @FieldDoc(description = "查询页码")
    private Integer pageNo;

    public Optional<String> validate() {
        if (getPageNo() == null || getPageNo() <= 0) {
            return Optional.of("页码不合法");
        }

        if (pageSize == null || pageSize <= 0L) {
            return Optional.of("页大小不合法");
        }

        if (compareType == null) {
            return Optional.of("未指定对比类型");
        }
        if (CollectionUtils.isEmpty(storeIds)) {
            return Optional.of("未指定门店ID");
        }
        if (storeIds.size() > 1000) {
            return Optional.of("选择门店数量超出限制");
        }
        for (Long storeId : storeIds) {
            if (storeId == null || storeId <= 0) {
                return Optional.of("存在无效门店 ID");
            }
        }
        if (tenantId == null) {
            return Optional.of("租户id为空");
        }
        return Optional.empty();
    }

    /**
     * 兼容原来的page字段 全量后可以删除该逻辑
     */
    public Integer getPageNo() {
        if (this.pageNo == null) {
            return this.page;
        }

        return this.pageNo;
    }
}
