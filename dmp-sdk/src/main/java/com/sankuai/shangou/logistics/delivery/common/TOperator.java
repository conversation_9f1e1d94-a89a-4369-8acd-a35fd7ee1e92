package com.sankuai.shangou.logistics.delivery.common;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-08-30
 * @email <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TOperator {

    @FieldDoc(
            description = "操作人id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long operatorId;

    @FieldDoc(
            description = "操作人姓名",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String operatorName;

    @FieldDoc(
            description = "操作时间，unix毫秒时间戳",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Long operateTime;

}
