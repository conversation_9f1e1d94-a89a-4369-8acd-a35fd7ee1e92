package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "骑手信息")
@Data
public class StaffRider implements Serializable {

    @FieldDoc(
            description = "姓名"
    )
    private String name;

    @FieldDoc(
            description = "电话"
    )
    private String phone;

    @FieldDoc(
            description = "账号"
    )
    private Long accountId;
}
