package com.sankuai.shangou.logistics.delivery.fulfill.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 2021/12/9 16:08
 * @Description:
 */
@Data
@TypeDoc(
        description = "分页查询请求",
        authors = {
                "houjie11"
        }
)
@AllArgsConstructor
@NoArgsConstructor
public class PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final int DEFAULT_PAGESIZE = 10;
    public static final int DEFAULT_PAGE = 1;

    @FieldDoc(description = "页码")
    private Integer pageNo = DEFAULT_PAGE;

    @FieldDoc(description = "分页大小,默认为10")
    private Integer pageSize = DEFAULT_PAGESIZE;

    public Integer getPageNo() {
        if(pageNo == null || pageNo < 1){
            pageNo = DEFAULT_PAGE;
        }
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        if(pageSize == null || pageSize <= 0){
            pageSize = DEFAULT_PAGESIZE;
        }
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
