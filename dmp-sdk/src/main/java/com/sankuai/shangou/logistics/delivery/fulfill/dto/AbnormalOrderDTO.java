package com.sankuai.shangou.logistics.delivery.fulfill.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/9 21:19
 **/
@ThriftStruct
public class AbnormalOrderDTO {
    @FieldDoc(
            description = "账号id"
    )
    private Long riderAccountId;

    @FieldDoc(
            description = "账号名"
    )
    private String riderAccountName;

    @FieldDoc(
            description = "骑手名称"
    )
    private String riderName;

    @FieldDoc(
            description = "门店名"
    )
    private String poiName;

    @FieldDoc(
            description = "订单号"
    )
    private String viewOrderId;

    @FieldDoc(
            description = "是否是预订单"
    )
    private Boolean isBooking;

    @FieldDoc(
            description = "订单支付时间"
    )
    private String payTime;

    @FieldDoc(description = "门店+1组织节点")
    private String upOneLevelDepartmentName;

    @FieldDoc(description = "门店+2组织节点")
    private String upTwoLevelDepartmentName;

    @FieldDoc(description = "岗位")
    private List<String> positionNameList;

    @FieldDoc(description = "在职状态")
    private Integer employeeStatus;

    @FieldDoc(description = "在职天数")
    private Integer onboardDays;

    @FieldDoc(description = "当月出勤天数")
    private Integer attendanceDaysOfCurMonth;

    @FieldDoc(description = "是否严重超时")
    private Boolean isSeriouslyTimeout;

    @FieldDoc(description = "是否考核超时")
    private Boolean isJudgeTimeout;

    @FieldDoc(description = "在职状态描述")
    private String employeeStatusDesc;

    @FieldDoc(description = "是否严重超时描述")
    private String isSeriouslyTimeoutDesc;

    @FieldDoc(description = "是否考核超时描述")
    private String isJudgeTimeoutDesc;

    @FieldDoc(
            description = "订单类型描述"
    )
    private String orderTypeDesc;

    @FieldDoc(
            description = "是否ETA超时文字描述"
    )
    private String isEtaOvertimeDesc;

    @FieldDoc(
            description = "是否ETA严重超时文字描述"
    )
    private String isEtaBadOvertimeDesc;


    @ThriftField(1)
    public Long getRiderAccountId() {
        return this.riderAccountId;
    }

    @ThriftField
    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    @ThriftField(2)
    public String getRiderAccountName() {
        return this.riderAccountName;
    }

    @ThriftField
    public void setRiderAccountName(String riderAccountName) {
        this.riderAccountName = riderAccountName;
    }

    @ThriftField(3)
    public String getRiderName() {
        return this.riderName;
    }

    @ThriftField
    public void setRiderName(String riderName) {
        this.riderName = riderName;
    }

    @ThriftField(4)
    public String getPoiName() {
        return this.poiName;
    }

    @ThriftField
    public void setPoiName(String poiName) {
        this.poiName = poiName;
    }

    @ThriftField(5)
    public String getViewOrderId() {
        return this.viewOrderId;
    }

    @ThriftField
    public void setViewOrderId(String viewOrderId) {
        this.viewOrderId = viewOrderId;
    }

    @ThriftField(6)
    public Boolean getIsBooking() {
        return this.isBooking;
    }

    @ThriftField
    public void setIsBooking(Boolean isBooking) {
        this.isBooking = isBooking;
    }

    @ThriftField(7)
    public String getPayTime() {
        return this.payTime;
    }

    @ThriftField
    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    @ThriftField(8)
    public String getUpOneLevelDepartmentName() {
        return this.upOneLevelDepartmentName;
    }

    @ThriftField
    public void setUpOneLevelDepartmentName(String upOneLevelDepartmentName) {
        this.upOneLevelDepartmentName = upOneLevelDepartmentName;
    }

    @ThriftField(9)
    public String getUpTwoLevelDepartmentName() {
        return this.upTwoLevelDepartmentName;
    }

    @ThriftField
    public void setUpTwoLevelDepartmentName(String upTwoLevelDepartmentName) {
        this.upTwoLevelDepartmentName = upTwoLevelDepartmentName;
    }

    @ThriftField(10)
    public List<String> getPositionNameList() {
        return this.positionNameList;
    }

    @ThriftField
    public void setPositionNameList(List<String> positionNameList) {
        this.positionNameList = positionNameList;
    }

    @ThriftField(11)
    public Integer getEmployeeStatus() {
        return this.employeeStatus;
    }

    @ThriftField
    public void setEmployeeStatus(Integer employeeStatus) {
        this.employeeStatus = employeeStatus;
    }

    @ThriftField(12)
    public Integer getOnboardDays() {
        return this.onboardDays;
    }

    @ThriftField
    public void setOnboardDays(Integer onboardDays) {
        this.onboardDays = onboardDays;
    }

    @ThriftField(13)
    public Integer getAttendanceDaysOfCurMonth() {
        return this.attendanceDaysOfCurMonth;
    }

    @ThriftField
    public void setAttendanceDaysOfCurMonth(Integer attendanceDaysOfCurMonth) {
        this.attendanceDaysOfCurMonth = attendanceDaysOfCurMonth;
    }

    @ThriftField(14)
    public Boolean getIsSeriouslyTimeout() {
        return this.isSeriouslyTimeout;
    }

    @ThriftField
    public void setIsSeriouslyTimeout(Boolean isSeriouslyTimeout) {
        this.isSeriouslyTimeout = isSeriouslyTimeout;
    }

    @ThriftField(15)
    public Boolean getIsJudgeTimeout() {
        return this.isJudgeTimeout;
    }

    @ThriftField
    public void setIsJudgeTimeout(Boolean isJudgeTimeout) {
        this.isJudgeTimeout = isJudgeTimeout;
    }


    @ThriftField(16)
    public String getEmployeeStatusDesc() {
        return this.employeeStatusDesc;
    }

    @ThriftField
    public void setEmployeeStatusDesc(String employeeStatusDesc) {
        this.employeeStatusDesc = employeeStatusDesc;
    }

    @ThriftField(17)
    public String getIsSeriouslyTimeoutDesc() {
        return this.isSeriouslyTimeoutDesc;
    }

    @ThriftField
    public void setIsSeriouslyTimeoutDesc(String isSeriouslyTimeoutDesc) {
        this.isSeriouslyTimeoutDesc = isSeriouslyTimeoutDesc;
    }

    @ThriftField(18)
    public String getIsJudgeTimeoutDesc() {
        return this.isJudgeTimeoutDesc;
    }

    @ThriftField
    public void setIsJudgeTimeoutDesc(String isJudgeTimeoutDesc) {
        this.isJudgeTimeoutDesc = isJudgeTimeoutDesc;
    }

    @ThriftField(19)
    public String getOrderTypeDesc() {
        return this.orderTypeDesc;
    }

    @ThriftField
    public void setOrderTypeDesc(String orderTypeDesc) {
        this.orderTypeDesc = orderTypeDesc;
    }

    @ThriftField(20)
    public String getIsEtaOvertimeDesc() {
        return isEtaOvertimeDesc;
    }

    @ThriftField
    public void setIsEtaOvertimeDesc(String isEtaOvertimeDesc) {
        this.isEtaOvertimeDesc = isEtaOvertimeDesc;
    }

    @ThriftField(21)
    public String getIsEtaBadOvertimeDesc() {
        return isEtaBadOvertimeDesc;
    }

    @ThriftField
    public void setIsEtaBadOvertimeDesc(String isEtaBadOvertimeDesc) {
        this.isEtaBadOvertimeDesc = isEtaBadOvertimeDesc;
    }
}
