package com.sankuai.shangou.logistics.delivery.alert;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.sankuai.shangou.logistics.delivery.alert.request.DeliveryAlertCallbackRequest;

/**
 * <AUTHOR>
 * @description
 * @date 2025-05-15
 */
@InterfaceDoc(
        displayName = "配送告警回调",
        type = "octo.thrift.annotation",
        scenarios = "配送告警回调",
        description = "配送告警回调"
)
@ThriftService
public interface DeliveryAlertCallbackThriftService {

    @ThriftMethod
    Boolean callback(DeliveryAlertCallbackRequest request);
}
