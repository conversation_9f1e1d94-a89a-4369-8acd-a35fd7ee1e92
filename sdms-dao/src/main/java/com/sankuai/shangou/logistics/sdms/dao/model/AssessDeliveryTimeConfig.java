package com.sankuai.shangou.logistics.sdms.dao.model;

import java.time.LocalDateTime;

public class AssessDeliveryTimeConfig {
    private Long id;

    private Long belongStoreConfigId;

    private String configJson;

    private Integer calcUnit;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer isDeleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBelongStoreConfigId() {
        return belongStoreConfigId;
    }

    public void setBelongStoreConfigId(Long belongStoreConfigId) {
        this.belongStoreConfigId = belongStoreConfigId;
    }

    public String getConfigJson() {
        return configJson;
    }

    public void setConfigJson(String configJson) {
        this.configJson = configJson == null ? null : configJson.trim();
    }

    public Integer getCalcUnit() {
        return calcUnit;
    }

    public void setCalcUnit(Integer calcUnit) {
        this.calcUnit = calcUnit;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AssessDeliveryTimeConfig other = (AssessDeliveryTimeConfig) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBelongStoreConfigId() == null ? other.getBelongStoreConfigId() == null : this.getBelongStoreConfigId().equals(other.getBelongStoreConfigId()))
            && (this.getConfigJson() == null ? other.getConfigJson() == null : this.getConfigJson().equals(other.getConfigJson()))
            && (this.getCalcUnit() == null ? other.getCalcUnit() == null : this.getCalcUnit().equals(other.getCalcUnit()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getIsDeleted() == null ? other.getIsDeleted() == null : this.getIsDeleted().equals(other.getIsDeleted()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBelongStoreConfigId() == null) ? 0 : getBelongStoreConfigId().hashCode());
        result = prime * result + ((getConfigJson() == null) ? 0 : getConfigJson().hashCode());
        result = prime * result + ((getCalcUnit() == null) ? 0 : getCalcUnit().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDeleted() == null) ? 0 : getIsDeleted().hashCode());
        return result;
    }
}