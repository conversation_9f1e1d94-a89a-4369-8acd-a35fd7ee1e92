package com.sankuai.shangou.logistics.sdms.dao.mapper;

import com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskOpLogPO;
import com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskOpLogPOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface YodaIdentifyTaskOpLogPOMapper {
    long countByExample(YodaIdentifyTaskOpLogPOExample example);

    int deleteByExample(YodaIdentifyTaskOpLogPOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(YodaIdentifyTaskOpLogPO record);

    int insertSelective(YodaIdentifyTaskOpLogPO record);

    List<YodaIdentifyTaskOpLogPO> selectByExample(YodaIdentifyTaskOpLogPOExample example);

    YodaIdentifyTaskOpLogPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") YodaIdentifyTaskOpLogPO record, @Param("example") YodaIdentifyTaskOpLogPOExample example);

    int updateByExample(@Param("record") YodaIdentifyTaskOpLogPO record, @Param("example") YodaIdentifyTaskOpLogPOExample example);

    int updateByPrimaryKeySelective(YodaIdentifyTaskOpLogPO record);

    int updateByPrimaryKey(YodaIdentifyTaskOpLogPO record);

    int batchInsert(@Param("list") List<YodaIdentifyTaskOpLogPO> list);
}