package com.sankuai.shangou.logistics.sdms.dao.mapper;

import com.sankuai.shangou.logistics.sdms.dao.model.AssessDeliveryTimeConfig;
import com.sankuai.shangou.logistics.sdms.dao.model.AssessDeliveryTimeConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AssessDeliveryTimeConfigMapper {
    long countByExample(AssessDeliveryTimeConfigExample example);

    int deleteByExample(AssessDeliveryTimeConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(AssessDeliveryTimeConfig record);

    int insertSelective(AssessDeliveryTimeConfig record);

    List<AssessDeliveryTimeConfig> selectByExample(AssessDeliveryTimeConfigExample example);

    AssessDeliveryTimeConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") AssessDeliveryTimeConfig record, @Param("example") AssessDeliveryTimeConfigExample example);

    int updateByExample(@Param("record") AssessDeliveryTimeConfig record, @Param("example") AssessDeliveryTimeConfigExample example);

    int updateByPrimaryKeySelective(AssessDeliveryTimeConfig record);

    int updateByPrimaryKey(AssessDeliveryTimeConfig record);

    int batchInsert(@Param("list") List<AssessDeliveryTimeConfig> list);
}