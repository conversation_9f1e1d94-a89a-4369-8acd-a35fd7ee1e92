package com.sankuai.shangou.logistics.sdms.dao.mapper;

import com.sankuai.shangou.logistics.sdms.dao.model.SdmsStoreConfig;
import com.sankuai.shangou.logistics.sdms.dao.model.SdmsStoreConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SdmsStoreConfigMapper {
    long countByExample(SdmsStoreConfigExample example);

    int deleteByExample(SdmsStoreConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SdmsStoreConfig record);

    int insertSelective(SdmsStoreConfig record);

    List<SdmsStoreConfig> selectByExample(SdmsStoreConfigExample example);

    SdmsStoreConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SdmsStoreConfig record, @Param("example") SdmsStoreConfigExample example);

    int updateByExample(@Param("record") SdmsStoreConfig record, @Param("example") SdmsStoreConfigExample example);

    int updateByPrimaryKeySelective(SdmsStoreConfig record);

    int updateByPrimaryKey(SdmsStoreConfig record);

    int batchInsert(@Param("list") List<SdmsStoreConfig> list);
}