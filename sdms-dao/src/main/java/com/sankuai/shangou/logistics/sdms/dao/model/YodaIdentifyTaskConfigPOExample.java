package com.sankuai.shangou.logistics.sdms.dao.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class YodaIdentifyTaskConfigPOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public YodaIdentifyTaskConfigPOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(Long value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(Long value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(Long value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(Long value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(Long value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<Long> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<Long> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(Long value1, Long value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(Long value1, Long value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeIsNull() {
            addCriterion("store_operation_mode is null");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeIsNotNull() {
            addCriterion("store_operation_mode is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeEqualTo(Integer value) {
            addCriterion("store_operation_mode =", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeNotEqualTo(Integer value) {
            addCriterion("store_operation_mode <>", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeGreaterThan(Integer value) {
            addCriterion("store_operation_mode >", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("store_operation_mode >=", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeLessThan(Integer value) {
            addCriterion("store_operation_mode <", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeLessThanOrEqualTo(Integer value) {
            addCriterion("store_operation_mode <=", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeIn(List<Integer> values) {
            addCriterion("store_operation_mode in", values, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeNotIn(List<Integer> values) {
            addCriterion("store_operation_mode not in", values, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeBetween(Integer value1, Integer value2) {
            addCriterion("store_operation_mode between", value1, value2, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeNotBetween(Integer value1, Integer value2) {
            addCriterion("store_operation_mode not between", value1, value2, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andCityListIsNull() {
            addCriterion("city_list is null");
            return (Criteria) this;
        }

        public Criteria andCityListIsNotNull() {
            addCriterion("city_list is not null");
            return (Criteria) this;
        }

        public Criteria andCityListEqualTo(String value) {
            addCriterion("city_list =", value, "cityList");
            return (Criteria) this;
        }

        public Criteria andCityListNotEqualTo(String value) {
            addCriterion("city_list <>", value, "cityList");
            return (Criteria) this;
        }

        public Criteria andCityListGreaterThan(String value) {
            addCriterion("city_list >", value, "cityList");
            return (Criteria) this;
        }

        public Criteria andCityListGreaterThanOrEqualTo(String value) {
            addCriterion("city_list >=", value, "cityList");
            return (Criteria) this;
        }

        public Criteria andCityListLessThan(String value) {
            addCriterion("city_list <", value, "cityList");
            return (Criteria) this;
        }

        public Criteria andCityListLessThanOrEqualTo(String value) {
            addCriterion("city_list <=", value, "cityList");
            return (Criteria) this;
        }

        public Criteria andCityListLike(String value) {
            addCriterion("city_list like", value, "cityList");
            return (Criteria) this;
        }

        public Criteria andCityListNotLike(String value) {
            addCriterion("city_list not like", value, "cityList");
            return (Criteria) this;
        }

        public Criteria andCityListIn(List<String> values) {
            addCriterion("city_list in", values, "cityList");
            return (Criteria) this;
        }

        public Criteria andCityListNotIn(List<String> values) {
            addCriterion("city_list not in", values, "cityList");
            return (Criteria) this;
        }

        public Criteria andCityListBetween(String value1, String value2) {
            addCriterion("city_list between", value1, value2, "cityList");
            return (Criteria) this;
        }

        public Criteria andCityListNotBetween(String value1, String value2) {
            addCriterion("city_list not between", value1, value2, "cityList");
            return (Criteria) this;
        }

        public Criteria andStoreListIsNull() {
            addCriterion("store_list is null");
            return (Criteria) this;
        }

        public Criteria andStoreListIsNotNull() {
            addCriterion("store_list is not null");
            return (Criteria) this;
        }

        public Criteria andStoreListEqualTo(String value) {
            addCriterion("store_list =", value, "storeList");
            return (Criteria) this;
        }

        public Criteria andStoreListNotEqualTo(String value) {
            addCriterion("store_list <>", value, "storeList");
            return (Criteria) this;
        }

        public Criteria andStoreListGreaterThan(String value) {
            addCriterion("store_list >", value, "storeList");
            return (Criteria) this;
        }

        public Criteria andStoreListGreaterThanOrEqualTo(String value) {
            addCriterion("store_list >=", value, "storeList");
            return (Criteria) this;
        }

        public Criteria andStoreListLessThan(String value) {
            addCriterion("store_list <", value, "storeList");
            return (Criteria) this;
        }

        public Criteria andStoreListLessThanOrEqualTo(String value) {
            addCriterion("store_list <=", value, "storeList");
            return (Criteria) this;
        }

        public Criteria andStoreListLike(String value) {
            addCriterion("store_list like", value, "storeList");
            return (Criteria) this;
        }

        public Criteria andStoreListNotLike(String value) {
            addCriterion("store_list not like", value, "storeList");
            return (Criteria) this;
        }

        public Criteria andStoreListIn(List<String> values) {
            addCriterion("store_list in", values, "storeList");
            return (Criteria) this;
        }

        public Criteria andStoreListNotIn(List<String> values) {
            addCriterion("store_list not in", values, "storeList");
            return (Criteria) this;
        }

        public Criteria andStoreListBetween(String value1, String value2) {
            addCriterion("store_list between", value1, value2, "storeList");
            return (Criteria) this;
        }

        public Criteria andStoreListNotBetween(String value1, String value2) {
            addCriterion("store_list not between", value1, value2, "storeList");
            return (Criteria) this;
        }

        public Criteria andCollectModeIsNull() {
            addCriterion("collect_mode is null");
            return (Criteria) this;
        }

        public Criteria andCollectModeIsNotNull() {
            addCriterion("collect_mode is not null");
            return (Criteria) this;
        }

        public Criteria andCollectModeEqualTo(Integer value) {
            addCriterion("collect_mode =", value, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeNotEqualTo(Integer value) {
            addCriterion("collect_mode <>", value, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeGreaterThan(Integer value) {
            addCriterion("collect_mode >", value, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("collect_mode >=", value, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeLessThan(Integer value) {
            addCriterion("collect_mode <", value, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeLessThanOrEqualTo(Integer value) {
            addCriterion("collect_mode <=", value, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeIn(List<Integer> values) {
            addCriterion("collect_mode in", values, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeNotIn(List<Integer> values) {
            addCriterion("collect_mode not in", values, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeBetween(Integer value1, Integer value2) {
            addCriterion("collect_mode between", value1, value2, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeNotBetween(Integer value1, Integer value2) {
            addCriterion("collect_mode not between", value1, value2, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeListIsNull() {
            addCriterion("collect_content_type_list is null");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeListIsNotNull() {
            addCriterion("collect_content_type_list is not null");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeListEqualTo(String value) {
            addCriterion("collect_content_type_list =", value, "collectContentTypeList");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeListNotEqualTo(String value) {
            addCriterion("collect_content_type_list <>", value, "collectContentTypeList");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeListGreaterThan(String value) {
            addCriterion("collect_content_type_list >", value, "collectContentTypeList");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeListGreaterThanOrEqualTo(String value) {
            addCriterion("collect_content_type_list >=", value, "collectContentTypeList");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeListLessThan(String value) {
            addCriterion("collect_content_type_list <", value, "collectContentTypeList");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeListLessThanOrEqualTo(String value) {
            addCriterion("collect_content_type_list <=", value, "collectContentTypeList");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeListLike(String value) {
            addCriterion("collect_content_type_list like", value, "collectContentTypeList");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeListNotLike(String value) {
            addCriterion("collect_content_type_list not like", value, "collectContentTypeList");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeListIn(List<String> values) {
            addCriterion("collect_content_type_list in", values, "collectContentTypeList");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeListNotIn(List<String> values) {
            addCriterion("collect_content_type_list not in", values, "collectContentTypeList");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeListBetween(String value1, String value2) {
            addCriterion("collect_content_type_list between", value1, value2, "collectContentTypeList");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeListNotBetween(String value1, String value2) {
            addCriterion("collect_content_type_list not between", value1, value2, "collectContentTypeList");
            return (Criteria) this;
        }

        public Criteria andCollectPeriodIsNull() {
            addCriterion("collect_period is null");
            return (Criteria) this;
        }

        public Criteria andCollectPeriodIsNotNull() {
            addCriterion("collect_period is not null");
            return (Criteria) this;
        }

        public Criteria andCollectPeriodEqualTo(Integer value) {
            addCriterion("collect_period =", value, "collectPeriod");
            return (Criteria) this;
        }

        public Criteria andCollectPeriodNotEqualTo(Integer value) {
            addCriterion("collect_period <>", value, "collectPeriod");
            return (Criteria) this;
        }

        public Criteria andCollectPeriodGreaterThan(Integer value) {
            addCriterion("collect_period >", value, "collectPeriod");
            return (Criteria) this;
        }

        public Criteria andCollectPeriodGreaterThanOrEqualTo(Integer value) {
            addCriterion("collect_period >=", value, "collectPeriod");
            return (Criteria) this;
        }

        public Criteria andCollectPeriodLessThan(Integer value) {
            addCriterion("collect_period <", value, "collectPeriod");
            return (Criteria) this;
        }

        public Criteria andCollectPeriodLessThanOrEqualTo(Integer value) {
            addCriterion("collect_period <=", value, "collectPeriod");
            return (Criteria) this;
        }

        public Criteria andCollectPeriodIn(List<Integer> values) {
            addCriterion("collect_period in", values, "collectPeriod");
            return (Criteria) this;
        }

        public Criteria andCollectPeriodNotIn(List<Integer> values) {
            addCriterion("collect_period not in", values, "collectPeriod");
            return (Criteria) this;
        }

        public Criteria andCollectPeriodBetween(Integer value1, Integer value2) {
            addCriterion("collect_period between", value1, value2, "collectPeriod");
            return (Criteria) this;
        }

        public Criteria andCollectPeriodNotBetween(Integer value1, Integer value2) {
            addCriterion("collect_period not between", value1, value2, "collectPeriod");
            return (Criteria) this;
        }

        public Criteria andPeriodMaxCollectTimesIsNull() {
            addCriterion("period_max_collect_times is null");
            return (Criteria) this;
        }

        public Criteria andPeriodMaxCollectTimesIsNotNull() {
            addCriterion("period_max_collect_times is not null");
            return (Criteria) this;
        }

        public Criteria andPeriodMaxCollectTimesEqualTo(Integer value) {
            addCriterion("period_max_collect_times =", value, "periodMaxCollectTimes");
            return (Criteria) this;
        }

        public Criteria andPeriodMaxCollectTimesNotEqualTo(Integer value) {
            addCriterion("period_max_collect_times <>", value, "periodMaxCollectTimes");
            return (Criteria) this;
        }

        public Criteria andPeriodMaxCollectTimesGreaterThan(Integer value) {
            addCriterion("period_max_collect_times >", value, "periodMaxCollectTimes");
            return (Criteria) this;
        }

        public Criteria andPeriodMaxCollectTimesGreaterThanOrEqualTo(Integer value) {
            addCriterion("period_max_collect_times >=", value, "periodMaxCollectTimes");
            return (Criteria) this;
        }

        public Criteria andPeriodMaxCollectTimesLessThan(Integer value) {
            addCriterion("period_max_collect_times <", value, "periodMaxCollectTimes");
            return (Criteria) this;
        }

        public Criteria andPeriodMaxCollectTimesLessThanOrEqualTo(Integer value) {
            addCriterion("period_max_collect_times <=", value, "periodMaxCollectTimes");
            return (Criteria) this;
        }

        public Criteria andPeriodMaxCollectTimesIn(List<Integer> values) {
            addCriterion("period_max_collect_times in", values, "periodMaxCollectTimes");
            return (Criteria) this;
        }

        public Criteria andPeriodMaxCollectTimesNotIn(List<Integer> values) {
            addCriterion("period_max_collect_times not in", values, "periodMaxCollectTimes");
            return (Criteria) this;
        }

        public Criteria andPeriodMaxCollectTimesBetween(Integer value1, Integer value2) {
            addCriterion("period_max_collect_times between", value1, value2, "periodMaxCollectTimes");
            return (Criteria) this;
        }

        public Criteria andPeriodMaxCollectTimesNotBetween(Integer value1, Integer value2) {
            addCriterion("period_max_collect_times not between", value1, value2, "periodMaxCollectTimes");
            return (Criteria) this;
        }

        public Criteria andTaskValidPeriodIsNull() {
            addCriterion("task_valid_period is null");
            return (Criteria) this;
        }

        public Criteria andTaskValidPeriodIsNotNull() {
            addCriterion("task_valid_period is not null");
            return (Criteria) this;
        }

        public Criteria andTaskValidPeriodEqualTo(Integer value) {
            addCriterion("task_valid_period =", value, "taskValidPeriod");
            return (Criteria) this;
        }

        public Criteria andTaskValidPeriodNotEqualTo(Integer value) {
            addCriterion("task_valid_period <>", value, "taskValidPeriod");
            return (Criteria) this;
        }

        public Criteria andTaskValidPeriodGreaterThan(Integer value) {
            addCriterion("task_valid_period >", value, "taskValidPeriod");
            return (Criteria) this;
        }

        public Criteria andTaskValidPeriodGreaterThanOrEqualTo(Integer value) {
            addCriterion("task_valid_period >=", value, "taskValidPeriod");
            return (Criteria) this;
        }

        public Criteria andTaskValidPeriodLessThan(Integer value) {
            addCriterion("task_valid_period <", value, "taskValidPeriod");
            return (Criteria) this;
        }

        public Criteria andTaskValidPeriodLessThanOrEqualTo(Integer value) {
            addCriterion("task_valid_period <=", value, "taskValidPeriod");
            return (Criteria) this;
        }

        public Criteria andTaskValidPeriodIn(List<Integer> values) {
            addCriterion("task_valid_period in", values, "taskValidPeriod");
            return (Criteria) this;
        }

        public Criteria andTaskValidPeriodNotIn(List<Integer> values) {
            addCriterion("task_valid_period not in", values, "taskValidPeriod");
            return (Criteria) this;
        }

        public Criteria andTaskValidPeriodBetween(Integer value1, Integer value2) {
            addCriterion("task_valid_period between", value1, value2, "taskValidPeriod");
            return (Criteria) this;
        }

        public Criteria andTaskValidPeriodNotBetween(Integer value1, Integer value2) {
            addCriterion("task_valid_period not between", value1, value2, "taskValidPeriod");
            return (Criteria) this;
        }

        public Criteria andIntervalIsNull() {
            addCriterion("`interval` is null");
            return (Criteria) this;
        }

        public Criteria andIntervalIsNotNull() {
            addCriterion("`interval` is not null");
            return (Criteria) this;
        }

        public Criteria andIntervalEqualTo(Integer value) {
            addCriterion("`interval` =", value, "interval");
            return (Criteria) this;
        }

        public Criteria andIntervalNotEqualTo(Integer value) {
            addCriterion("`interval` <>", value, "interval");
            return (Criteria) this;
        }

        public Criteria andIntervalGreaterThan(Integer value) {
            addCriterion("`interval` >", value, "interval");
            return (Criteria) this;
        }

        public Criteria andIntervalGreaterThanOrEqualTo(Integer value) {
            addCriterion("`interval` >=", value, "interval");
            return (Criteria) this;
        }

        public Criteria andIntervalLessThan(Integer value) {
            addCriterion("`interval` <", value, "interval");
            return (Criteria) this;
        }

        public Criteria andIntervalLessThanOrEqualTo(Integer value) {
            addCriterion("`interval` <=", value, "interval");
            return (Criteria) this;
        }

        public Criteria andIntervalIn(List<Integer> values) {
            addCriterion("`interval` in", values, "interval");
            return (Criteria) this;
        }

        public Criteria andIntervalNotIn(List<Integer> values) {
            addCriterion("`interval` not in", values, "interval");
            return (Criteria) this;
        }

        public Criteria andIntervalBetween(Integer value1, Integer value2) {
            addCriterion("`interval` between", value1, value2, "interval");
            return (Criteria) this;
        }

        public Criteria andIntervalNotBetween(Integer value1, Integer value2) {
            addCriterion("`interval` not between", value1, value2, "interval");
            return (Criteria) this;
        }

        public Criteria andPeriodBeginTimeIsNull() {
            addCriterion("period_begin_time is null");
            return (Criteria) this;
        }

        public Criteria andPeriodBeginTimeIsNotNull() {
            addCriterion("period_begin_time is not null");
            return (Criteria) this;
        }

        public Criteria andPeriodBeginTimeEqualTo(String value) {
            addCriterion("period_begin_time =", value, "periodBeginTime");
            return (Criteria) this;
        }

        public Criteria andPeriodBeginTimeNotEqualTo(String value) {
            addCriterion("period_begin_time <>", value, "periodBeginTime");
            return (Criteria) this;
        }

        public Criteria andPeriodBeginTimeGreaterThan(String value) {
            addCriterion("period_begin_time >", value, "periodBeginTime");
            return (Criteria) this;
        }

        public Criteria andPeriodBeginTimeGreaterThanOrEqualTo(String value) {
            addCriterion("period_begin_time >=", value, "periodBeginTime");
            return (Criteria) this;
        }

        public Criteria andPeriodBeginTimeLessThan(String value) {
            addCriterion("period_begin_time <", value, "periodBeginTime");
            return (Criteria) this;
        }

        public Criteria andPeriodBeginTimeLessThanOrEqualTo(String value) {
            addCriterion("period_begin_time <=", value, "periodBeginTime");
            return (Criteria) this;
        }

        public Criteria andPeriodBeginTimeLike(String value) {
            addCriterion("period_begin_time like", value, "periodBeginTime");
            return (Criteria) this;
        }

        public Criteria andPeriodBeginTimeNotLike(String value) {
            addCriterion("period_begin_time not like", value, "periodBeginTime");
            return (Criteria) this;
        }

        public Criteria andPeriodBeginTimeIn(List<String> values) {
            addCriterion("period_begin_time in", values, "periodBeginTime");
            return (Criteria) this;
        }

        public Criteria andPeriodBeginTimeNotIn(List<String> values) {
            addCriterion("period_begin_time not in", values, "periodBeginTime");
            return (Criteria) this;
        }

        public Criteria andPeriodBeginTimeBetween(String value1, String value2) {
            addCriterion("period_begin_time between", value1, value2, "periodBeginTime");
            return (Criteria) this;
        }

        public Criteria andPeriodBeginTimeNotBetween(String value1, String value2) {
            addCriterion("period_begin_time not between", value1, value2, "periodBeginTime");
            return (Criteria) this;
        }

        public Criteria andPeriodEndTimeIsNull() {
            addCriterion("period_end_time is null");
            return (Criteria) this;
        }

        public Criteria andPeriodEndTimeIsNotNull() {
            addCriterion("period_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andPeriodEndTimeEqualTo(String value) {
            addCriterion("period_end_time =", value, "periodEndTime");
            return (Criteria) this;
        }

        public Criteria andPeriodEndTimeNotEqualTo(String value) {
            addCriterion("period_end_time <>", value, "periodEndTime");
            return (Criteria) this;
        }

        public Criteria andPeriodEndTimeGreaterThan(String value) {
            addCriterion("period_end_time >", value, "periodEndTime");
            return (Criteria) this;
        }

        public Criteria andPeriodEndTimeGreaterThanOrEqualTo(String value) {
            addCriterion("period_end_time >=", value, "periodEndTime");
            return (Criteria) this;
        }

        public Criteria andPeriodEndTimeLessThan(String value) {
            addCriterion("period_end_time <", value, "periodEndTime");
            return (Criteria) this;
        }

        public Criteria andPeriodEndTimeLessThanOrEqualTo(String value) {
            addCriterion("period_end_time <=", value, "periodEndTime");
            return (Criteria) this;
        }

        public Criteria andPeriodEndTimeLike(String value) {
            addCriterion("period_end_time like", value, "periodEndTime");
            return (Criteria) this;
        }

        public Criteria andPeriodEndTimeNotLike(String value) {
            addCriterion("period_end_time not like", value, "periodEndTime");
            return (Criteria) this;
        }

        public Criteria andPeriodEndTimeIn(List<String> values) {
            addCriterion("period_end_time in", values, "periodEndTime");
            return (Criteria) this;
        }

        public Criteria andPeriodEndTimeNotIn(List<String> values) {
            addCriterion("period_end_time not in", values, "periodEndTime");
            return (Criteria) this;
        }

        public Criteria andPeriodEndTimeBetween(String value1, String value2) {
            addCriterion("period_end_time between", value1, value2, "periodEndTime");
            return (Criteria) this;
        }

        public Criteria andPeriodEndTimeNotBetween(String value1, String value2) {
            addCriterion("period_end_time not between", value1, value2, "periodEndTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andPunishConfigIsNull() {
            addCriterion("punish_config is null");
            return (Criteria) this;
        }

        public Criteria andPunishConfigIsNotNull() {
            addCriterion("punish_config is not null");
            return (Criteria) this;
        }

        public Criteria andPunishConfigEqualTo(String value) {
            addCriterion("punish_config =", value, "punishConfig");
            return (Criteria) this;
        }

        public Criteria andPunishConfigNotEqualTo(String value) {
            addCriterion("punish_config <>", value, "punishConfig");
            return (Criteria) this;
        }

        public Criteria andPunishConfigGreaterThan(String value) {
            addCriterion("punish_config >", value, "punishConfig");
            return (Criteria) this;
        }

        public Criteria andPunishConfigGreaterThanOrEqualTo(String value) {
            addCriterion("punish_config >=", value, "punishConfig");
            return (Criteria) this;
        }

        public Criteria andPunishConfigLessThan(String value) {
            addCriterion("punish_config <", value, "punishConfig");
            return (Criteria) this;
        }

        public Criteria andPunishConfigLessThanOrEqualTo(String value) {
            addCriterion("punish_config <=", value, "punishConfig");
            return (Criteria) this;
        }

        public Criteria andPunishConfigLike(String value) {
            addCriterion("punish_config like", value, "punishConfig");
            return (Criteria) this;
        }

        public Criteria andPunishConfigNotLike(String value) {
            addCriterion("punish_config not like", value, "punishConfig");
            return (Criteria) this;
        }

        public Criteria andPunishConfigIn(List<String> values) {
            addCriterion("punish_config in", values, "punishConfig");
            return (Criteria) this;
        }

        public Criteria andPunishConfigNotIn(List<String> values) {
            addCriterion("punish_config not in", values, "punishConfig");
            return (Criteria) this;
        }

        public Criteria andPunishConfigBetween(String value1, String value2) {
            addCriterion("punish_config between", value1, value2, "punishConfig");
            return (Criteria) this;
        }

        public Criteria andPunishConfigNotBetween(String value1, String value2) {
            addCriterion("punish_config not between", value1, value2, "punishConfig");
            return (Criteria) this;
        }

        public Criteria andIsValidIsNull() {
            addCriterion("is_valid is null");
            return (Criteria) this;
        }

        public Criteria andIsValidIsNotNull() {
            addCriterion("is_valid is not null");
            return (Criteria) this;
        }

        public Criteria andIsValidEqualTo(Integer value) {
            addCriterion("is_valid =", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotEqualTo(Integer value) {
            addCriterion("is_valid <>", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidGreaterThan(Integer value) {
            addCriterion("is_valid >", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_valid >=", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidLessThan(Integer value) {
            addCriterion("is_valid <", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidLessThanOrEqualTo(Integer value) {
            addCriterion("is_valid <=", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidIn(List<Integer> values) {
            addCriterion("is_valid in", values, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotIn(List<Integer> values) {
            addCriterion("is_valid not in", values, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidBetween(Integer value1, Integer value2) {
            addCriterion("is_valid between", value1, value2, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotBetween(Integer value1, Integer value2) {
            addCriterion("is_valid not between", value1, value2, "isValid");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}