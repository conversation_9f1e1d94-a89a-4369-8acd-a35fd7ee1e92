package com.sankuai.shangou.logistics.sdms.dao.mapper;

import com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPO;
import com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface YodaIdentifyTaskPOMapper {
    long countByExample(YodaIdentifyTaskPOExample example);

    int deleteByExample(YodaIdentifyTaskPOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(YodaIdentifyTaskPO record);

    int insertSelective(YodaIdentifyTaskPO record);

    List<YodaIdentifyTaskPO> selectByExample(YodaIdentifyTaskPOExample example);

    YodaIdentifyTaskPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") YodaIdentifyTaskPO record, @Param("example") YodaIdentifyTaskPOExample example);

    int updateByExample(@Param("record") YodaIdentifyTaskPO record, @Param("example") YodaIdentifyTaskPOExample example);

    int updateByPrimaryKeySelective(YodaIdentifyTaskPO record);

    int updateByPrimaryKey(YodaIdentifyTaskPO record);

    int batchInsert(@Param("list") List<YodaIdentifyTaskPO> list);
}