package com.sankuai.shangou.logistics.sdms.dao.model;

import java.time.LocalDateTime;

public class SdmsStoreConfig {
    private Long id;

    private Long tenantId;

    private Integer storeOperationMode;

    private Long cityId;

    private Long storeId;

    private LocalDateTime validTimeStart;

    private LocalDateTime validTimeEnd;

    private Integer isDeleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getStoreOperationMode() {
        return storeOperationMode;
    }

    public void setStoreOperationMode(Integer storeOperationMode) {
        this.storeOperationMode = storeOperationMode;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public LocalDateTime getValidTimeStart() {
        return validTimeStart;
    }

    public void setValidTimeStart(LocalDateTime validTimeStart) {
        this.validTimeStart = validTimeStart;
    }

    public LocalDateTime getValidTimeEnd() {
        return validTimeEnd;
    }

    public void setValidTimeEnd(LocalDateTime validTimeEnd) {
        this.validTimeEnd = validTimeEnd;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SdmsStoreConfig other = (SdmsStoreConfig) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()))
            && (this.getStoreOperationMode() == null ? other.getStoreOperationMode() == null : this.getStoreOperationMode().equals(other.getStoreOperationMode()))
            && (this.getCityId() == null ? other.getCityId() == null : this.getCityId().equals(other.getCityId()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getValidTimeStart() == null ? other.getValidTimeStart() == null : this.getValidTimeStart().equals(other.getValidTimeStart()))
            && (this.getValidTimeEnd() == null ? other.getValidTimeEnd() == null : this.getValidTimeEnd().equals(other.getValidTimeEnd()))
            && (this.getIsDeleted() == null ? other.getIsDeleted() == null : this.getIsDeleted().equals(other.getIsDeleted()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        result = prime * result + ((getStoreOperationMode() == null) ? 0 : getStoreOperationMode().hashCode());
        result = prime * result + ((getCityId() == null) ? 0 : getCityId().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getValidTimeStart() == null) ? 0 : getValidTimeStart().hashCode());
        result = prime * result + ((getValidTimeEnd() == null) ? 0 : getValidTimeEnd().hashCode());
        result = prime * result + ((getIsDeleted() == null) ? 0 : getIsDeleted().hashCode());
        return result;
    }
}