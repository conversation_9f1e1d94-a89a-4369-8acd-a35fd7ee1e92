package com.sankuai.shangou.logistics.sdms.dao.mapper;

import com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyEndTaskQuery;
import com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/9 17:11
 **/
public interface YodaIdentifyTaskPOExMapper {
    List<YodaIdentifyTaskPO> getRiderLatestAssignTask(@Param("riderAccountId") Long riderAccountId);

    List<YodaIdentifyTaskPO> queryRiderEndTask(YodaIdentifyEndTaskQuery query);
}
