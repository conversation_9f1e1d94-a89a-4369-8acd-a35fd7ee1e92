package com.sankuai.shangou.logistics.sdms.dao.mapper;

import com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskConfigPO;
import com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskConfigPOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface YodaIdentifyTaskConfigPOMapper {
    long countByExample(YodaIdentifyTaskConfigPOExample example);

    int deleteByExample(YodaIdentifyTaskConfigPOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(YodaIdentifyTaskConfigPO record);

    int insertSelective(YodaIdentifyTaskConfigPO record);

    List<YodaIdentifyTaskConfigPO> selectByExample(YodaIdentifyTaskConfigPOExample example);

    YodaIdentifyTaskConfigPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") YodaIdentifyTaskConfigPO record, @Param("example") YodaIdentifyTaskConfigPOExample example);

    int updateByExample(@Param("record") YodaIdentifyTaskConfigPO record, @Param("example") YodaIdentifyTaskConfigPOExample example);

    int updateByPrimaryKeySelective(YodaIdentifyTaskConfigPO record);

    int updateByPrimaryKey(YodaIdentifyTaskConfigPO record);

    int batchInsert(@Param("list") List<YodaIdentifyTaskConfigPO> list);
}