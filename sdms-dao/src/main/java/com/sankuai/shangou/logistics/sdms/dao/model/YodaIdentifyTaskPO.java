package com.sankuai.shangou.logistics.sdms.dao.model;

import java.time.LocalDateTime;

public class YodaIdentifyTaskPO {
    private Long id;

    private Long deliveryOrderId;

    private String channelOrderId;

    private Integer orderBizType;

    private Long relRuleId;

    private String collectContentType;

    private Integer collectMode;

    private Long riderAccountId;

    private Long storeId;

    private Long tenantId;

    private Integer taskStatus;

    private LocalDateTime completeTime;

    private Integer faceIdentifyResult;

    private Integer helmetIdentifyResult;

    private Integer dressingIdentifyResult;

    private String extInfo;

    private LocalDateTime updateTime;

    private LocalDateTime createTime;

    private LocalDateTime pushTaskTime;

    private Integer resultCheckPass;

    private String callbackCode;

    private String callbackMsg;

    private LocalDateTime taskExpireTime;

    private String punishInfo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDeliveryOrderId() {
        return deliveryOrderId;
    }

    public void setDeliveryOrderId(Long deliveryOrderId) {
        this.deliveryOrderId = deliveryOrderId;
    }

    public String getChannelOrderId() {
        return channelOrderId;
    }

    public void setChannelOrderId(String channelOrderId) {
        this.channelOrderId = channelOrderId == null ? null : channelOrderId.trim();
    }

    public Integer getOrderBizType() {
        return orderBizType;
    }

    public void setOrderBizType(Integer orderBizType) {
        this.orderBizType = orderBizType;
    }

    public Long getRelRuleId() {
        return relRuleId;
    }

    public void setRelRuleId(Long relRuleId) {
        this.relRuleId = relRuleId;
    }

    public String getCollectContentType() {
        return collectContentType;
    }

    public void setCollectContentType(String collectContentType) {
        this.collectContentType = collectContentType == null ? null : collectContentType.trim();
    }

    public Integer getCollectMode() {
        return collectMode;
    }

    public void setCollectMode(Integer collectMode) {
        this.collectMode = collectMode;
    }

    public Long getRiderAccountId() {
        return riderAccountId;
    }

    public void setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public LocalDateTime getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(LocalDateTime completeTime) {
        this.completeTime = completeTime;
    }

    public Integer getFaceIdentifyResult() {
        return faceIdentifyResult;
    }

    public void setFaceIdentifyResult(Integer faceIdentifyResult) {
        this.faceIdentifyResult = faceIdentifyResult;
    }

    public Integer getHelmetIdentifyResult() {
        return helmetIdentifyResult;
    }

    public void setHelmetIdentifyResult(Integer helmetIdentifyResult) {
        this.helmetIdentifyResult = helmetIdentifyResult;
    }

    public Integer getDressingIdentifyResult() {
        return dressingIdentifyResult;
    }

    public void setDressingIdentifyResult(Integer dressingIdentifyResult) {
        this.dressingIdentifyResult = dressingIdentifyResult;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo == null ? null : extInfo.trim();
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getPushTaskTime() {
        return pushTaskTime;
    }

    public void setPushTaskTime(LocalDateTime pushTaskTime) {
        this.pushTaskTime = pushTaskTime;
    }

    public Integer getResultCheckPass() {
        return resultCheckPass;
    }

    public void setResultCheckPass(Integer resultCheckPass) {
        this.resultCheckPass = resultCheckPass;
    }

    public String getCallbackCode() {
        return callbackCode;
    }

    public void setCallbackCode(String callbackCode) {
        this.callbackCode = callbackCode == null ? null : callbackCode.trim();
    }

    public String getCallbackMsg() {
        return callbackMsg;
    }

    public void setCallbackMsg(String callbackMsg) {
        this.callbackMsg = callbackMsg == null ? null : callbackMsg.trim();
    }

    public LocalDateTime getTaskExpireTime() {
        return taskExpireTime;
    }

    public void setTaskExpireTime(LocalDateTime taskExpireTime) {
        this.taskExpireTime = taskExpireTime;
    }

    public String getPunishInfo() {
        return punishInfo;
    }

    public void setPunishInfo(String punishInfo) {
        this.punishInfo = punishInfo == null ? null : punishInfo.trim();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        YodaIdentifyTaskPO other = (YodaIdentifyTaskPO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getDeliveryOrderId() == null ? other.getDeliveryOrderId() == null : this.getDeliveryOrderId().equals(other.getDeliveryOrderId()))
            && (this.getChannelOrderId() == null ? other.getChannelOrderId() == null : this.getChannelOrderId().equals(other.getChannelOrderId()))
            && (this.getOrderBizType() == null ? other.getOrderBizType() == null : this.getOrderBizType().equals(other.getOrderBizType()))
            && (this.getRelRuleId() == null ? other.getRelRuleId() == null : this.getRelRuleId().equals(other.getRelRuleId()))
            && (this.getCollectContentType() == null ? other.getCollectContentType() == null : this.getCollectContentType().equals(other.getCollectContentType()))
            && (this.getCollectMode() == null ? other.getCollectMode() == null : this.getCollectMode().equals(other.getCollectMode()))
            && (this.getRiderAccountId() == null ? other.getRiderAccountId() == null : this.getRiderAccountId().equals(other.getRiderAccountId()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()))
            && (this.getTaskStatus() == null ? other.getTaskStatus() == null : this.getTaskStatus().equals(other.getTaskStatus()))
            && (this.getCompleteTime() == null ? other.getCompleteTime() == null : this.getCompleteTime().equals(other.getCompleteTime()))
            && (this.getFaceIdentifyResult() == null ? other.getFaceIdentifyResult() == null : this.getFaceIdentifyResult().equals(other.getFaceIdentifyResult()))
            && (this.getHelmetIdentifyResult() == null ? other.getHelmetIdentifyResult() == null : this.getHelmetIdentifyResult().equals(other.getHelmetIdentifyResult()))
            && (this.getDressingIdentifyResult() == null ? other.getDressingIdentifyResult() == null : this.getDressingIdentifyResult().equals(other.getDressingIdentifyResult()))
            && (this.getExtInfo() == null ? other.getExtInfo() == null : this.getExtInfo().equals(other.getExtInfo()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getPushTaskTime() == null ? other.getPushTaskTime() == null : this.getPushTaskTime().equals(other.getPushTaskTime()))
            && (this.getResultCheckPass() == null ? other.getResultCheckPass() == null : this.getResultCheckPass().equals(other.getResultCheckPass()))
            && (this.getCallbackCode() == null ? other.getCallbackCode() == null : this.getCallbackCode().equals(other.getCallbackCode()))
            && (this.getCallbackMsg() == null ? other.getCallbackMsg() == null : this.getCallbackMsg().equals(other.getCallbackMsg()))
            && (this.getTaskExpireTime() == null ? other.getTaskExpireTime() == null : this.getTaskExpireTime().equals(other.getTaskExpireTime()))
            && (this.getPunishInfo() == null ? other.getPunishInfo() == null : this.getPunishInfo().equals(other.getPunishInfo()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getDeliveryOrderId() == null) ? 0 : getDeliveryOrderId().hashCode());
        result = prime * result + ((getChannelOrderId() == null) ? 0 : getChannelOrderId().hashCode());
        result = prime * result + ((getOrderBizType() == null) ? 0 : getOrderBizType().hashCode());
        result = prime * result + ((getRelRuleId() == null) ? 0 : getRelRuleId().hashCode());
        result = prime * result + ((getCollectContentType() == null) ? 0 : getCollectContentType().hashCode());
        result = prime * result + ((getCollectMode() == null) ? 0 : getCollectMode().hashCode());
        result = prime * result + ((getRiderAccountId() == null) ? 0 : getRiderAccountId().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        result = prime * result + ((getTaskStatus() == null) ? 0 : getTaskStatus().hashCode());
        result = prime * result + ((getCompleteTime() == null) ? 0 : getCompleteTime().hashCode());
        result = prime * result + ((getFaceIdentifyResult() == null) ? 0 : getFaceIdentifyResult().hashCode());
        result = prime * result + ((getHelmetIdentifyResult() == null) ? 0 : getHelmetIdentifyResult().hashCode());
        result = prime * result + ((getDressingIdentifyResult() == null) ? 0 : getDressingIdentifyResult().hashCode());
        result = prime * result + ((getExtInfo() == null) ? 0 : getExtInfo().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getPushTaskTime() == null) ? 0 : getPushTaskTime().hashCode());
        result = prime * result + ((getResultCheckPass() == null) ? 0 : getResultCheckPass().hashCode());
        result = prime * result + ((getCallbackCode() == null) ? 0 : getCallbackCode().hashCode());
        result = prime * result + ((getCallbackMsg() == null) ? 0 : getCallbackMsg().hashCode());
        result = prime * result + ((getTaskExpireTime() == null) ? 0 : getTaskExpireTime().hashCode());
        result = prime * result + ((getPunishInfo() == null) ? 0 : getPunishInfo().hashCode());
        return result;
    }
}