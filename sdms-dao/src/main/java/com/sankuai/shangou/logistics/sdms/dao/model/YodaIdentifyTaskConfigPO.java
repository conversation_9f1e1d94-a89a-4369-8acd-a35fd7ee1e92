package com.sankuai.shangou.logistics.sdms.dao.model;

import java.time.LocalDateTime;

public class YodaIdentifyTaskConfigPO {
    private Long id;

    private Long tenantId;

    private Integer storeOperationMode;

    private String cityList;

    private String storeList;

    private Integer collectMode;

    private String collectContentTypeList;

    private Integer collectPeriod;

    private Integer periodMaxCollectTimes;

    private Integer taskValidPeriod;

    private Integer interval;

    private String periodBeginTime;

    private String periodEndTime;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String punishConfig;

    private Integer isValid;
    /**
     * 岗位列表
     */
    private String positionList;
    /**
     * 任务即将过期提醒时间（单位分钟）
     */
    private Integer taskNearExpiration;
    /**
     * 二级配置-不管控配置
     */
    private String noControl;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getStoreOperationMode() {
        return storeOperationMode;
    }

    public void setStoreOperationMode(Integer storeOperationMode) {
        this.storeOperationMode = storeOperationMode;
    }

    public String getCityList() {
        return cityList;
    }

    public void setCityList(String cityList) {
        this.cityList = cityList == null ? null : cityList.trim();
    }

    public String getStoreList() {
        return storeList;
    }

    public void setStoreList(String storeList) {
        this.storeList = storeList == null ? null : storeList.trim();
    }

    public Integer getCollectMode() {
        return collectMode;
    }

    public void setCollectMode(Integer collectMode) {
        this.collectMode = collectMode;
    }

    public String getCollectContentTypeList() {
        return collectContentTypeList;
    }

    public void setCollectContentTypeList(String collectContentTypeList) {
        this.collectContentTypeList = collectContentTypeList == null ? null : collectContentTypeList.trim();
    }

    public Integer getCollectPeriod() {
        return collectPeriod;
    }

    public void setCollectPeriod(Integer collectPeriod) {
        this.collectPeriod = collectPeriod;
    }

    public Integer getPeriodMaxCollectTimes() {
        return periodMaxCollectTimes;
    }

    public void setPeriodMaxCollectTimes(Integer periodMaxCollectTimes) {
        this.periodMaxCollectTimes = periodMaxCollectTimes;
    }

    public Integer getTaskValidPeriod() {
        return taskValidPeriod;
    }

    public void setTaskValidPeriod(Integer taskValidPeriod) {
        this.taskValidPeriod = taskValidPeriod;
    }

    public Integer getInterval() {
        return interval;
    }

    public void setInterval(Integer interval) {
        this.interval = interval;
    }

    public String getPeriodBeginTime() {
        return periodBeginTime;
    }

    public void setPeriodBeginTime(String periodBeginTime) {
        this.periodBeginTime = periodBeginTime == null ? null : periodBeginTime.trim();
    }

    public String getPeriodEndTime() {
        return periodEndTime;
    }

    public void setPeriodEndTime(String periodEndTime) {
        this.periodEndTime = periodEndTime == null ? null : periodEndTime.trim();
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getPunishConfig() {
        return punishConfig;
    }

    public void setPunishConfig(String punishConfig) {
        this.punishConfig = punishConfig == null ? null : punishConfig.trim();
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public String getPositionList() {
        return positionList;
    }

    public void setPositionList(String positionList) {
        this.positionList = positionList == null ? null : positionList.trim();
    }

    public Integer getTaskNearExpiration() {
        return taskNearExpiration;
    }

    public void setTaskNearExpiration(Integer taskNearExpiration) {
        this.taskNearExpiration = taskNearExpiration;
    }

    public String getNoControl() {
        return noControl;
    }

    public void setNoControl(String noControl) {
        this.noControl = noControl == null ? null : noControl.trim();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        YodaIdentifyTaskConfigPO other = (YodaIdentifyTaskConfigPO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()))
            && (this.getStoreOperationMode() == null ? other.getStoreOperationMode() == null : this.getStoreOperationMode().equals(other.getStoreOperationMode()))
            && (this.getCityList() == null ? other.getCityList() == null : this.getCityList().equals(other.getCityList()))
            && (this.getStoreList() == null ? other.getStoreList() == null : this.getStoreList().equals(other.getStoreList()))
            && (this.getCollectMode() == null ? other.getCollectMode() == null : this.getCollectMode().equals(other.getCollectMode()))
            && (this.getCollectContentTypeList() == null ? other.getCollectContentTypeList() == null : this.getCollectContentTypeList().equals(other.getCollectContentTypeList()))
            && (this.getCollectPeriod() == null ? other.getCollectPeriod() == null : this.getCollectPeriod().equals(other.getCollectPeriod()))
            && (this.getPeriodMaxCollectTimes() == null ? other.getPeriodMaxCollectTimes() == null : this.getPeriodMaxCollectTimes().equals(other.getPeriodMaxCollectTimes()))
            && (this.getTaskValidPeriod() == null ? other.getTaskValidPeriod() == null : this.getTaskValidPeriod().equals(other.getTaskValidPeriod()))
            && (this.getInterval() == null ? other.getInterval() == null : this.getInterval().equals(other.getInterval()))
            && (this.getPeriodBeginTime() == null ? other.getPeriodBeginTime() == null : this.getPeriodBeginTime().equals(other.getPeriodBeginTime()))
            && (this.getPeriodEndTime() == null ? other.getPeriodEndTime() == null : this.getPeriodEndTime().equals(other.getPeriodEndTime()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getPunishConfig() == null ? other.getPunishConfig() == null : this.getPunishConfig().equals(other.getPunishConfig()))
            && (this.getIsValid() == null ? other.getIsValid() == null : this.getIsValid().equals(other.getIsValid()))
            && (this.getPositionList() == null ? other.getPositionList() == null : this.getPositionList().equals(other.getPositionList()))
            && (this.getTaskNearExpiration() == null ? other.getTaskNearExpiration() == null : this.getTaskNearExpiration().equals(other.getTaskNearExpiration()))
            && (this.getNoControl() == null ? other.getNoControl() == null : this.getNoControl().equals(other.getNoControl()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        result = prime * result + ((getStoreOperationMode() == null) ? 0 : getStoreOperationMode().hashCode());
        result = prime * result + ((getCityList() == null) ? 0 : getCityList().hashCode());
        result = prime * result + ((getStoreList() == null) ? 0 : getStoreList().hashCode());
        result = prime * result + ((getCollectMode() == null) ? 0 : getCollectMode().hashCode());
        result = prime * result + ((getCollectContentTypeList() == null) ? 0 : getCollectContentTypeList().hashCode());
        result = prime * result + ((getCollectPeriod() == null) ? 0 : getCollectPeriod().hashCode());
        result = prime * result + ((getPeriodMaxCollectTimes() == null) ? 0 : getPeriodMaxCollectTimes().hashCode());
        result = prime * result + ((getTaskValidPeriod() == null) ? 0 : getTaskValidPeriod().hashCode());
        result = prime * result + ((getInterval() == null) ? 0 : getInterval().hashCode());
        result = prime * result + ((getPeriodBeginTime() == null) ? 0 : getPeriodBeginTime().hashCode());
        result = prime * result + ((getPeriodEndTime() == null) ? 0 : getPeriodEndTime().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getPunishConfig() == null) ? 0 : getPunishConfig().hashCode());
        result = prime * result + ((getIsValid() == null) ? 0 : getIsValid().hashCode());
        result = prime * result + ((getPositionList() == null) ? 0 : getPositionList().hashCode());
        result = prime * result + ((getTaskNearExpiration() == null) ? 0 : getTaskNearExpiration().hashCode());
        result = prime * result + ((getNoControl() == null) ? 0 : getNoControl().hashCode());
        return result;
    }
}