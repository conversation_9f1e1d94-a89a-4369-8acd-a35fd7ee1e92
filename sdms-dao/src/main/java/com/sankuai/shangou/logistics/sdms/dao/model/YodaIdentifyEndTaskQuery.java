package com.sankuai.shangou.logistics.sdms.dao.model;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2025-04-09
 */
public class YodaIdentifyEndTaskQuery {
    /**
     * 骑手账号id
     */
    private Long riderAccountId;

    /**
     * 查询任务下发开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 查询任务下发结束时间
     */
    private LocalDateTime endTime;

    /**
     * 任务状态
     */
    private Integer taskStatus;

    /**
     * 识别结果
     */
    private Integer verifyResult;

    public Long getRiderAccountId() {
        return riderAccountId;
    }

    public YodaIdentifyEndTaskQuery setRiderAccountId(Long riderAccountId) {
        this.riderAccountId = riderAccountId;
        return this;
    }

    public LocalDateTime getBeginTime() {
        return beginTime;
    }

    public YodaIdentifyEndTaskQuery setBeginTime(LocalDateTime beginTime) {
        this.beginTime = beginTime;
        return this;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public YodaIdentifyEndTaskQuery setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
        return this;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public YodaIdentifyEndTaskQuery setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
        return this;
    }

    public Integer getVerifyResult() {
        return verifyResult;
    }

    public YodaIdentifyEndTaskQuery setVerifyResult(Integer verifyResult) {
        this.verifyResult = verifyResult;
        return this;
    }
}
