package com.sankuai.shangou.logistics.sdms.dao.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class SdmsStoreConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public SdmsStoreConfigExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(Long value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(Long value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(Long value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(Long value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(Long value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<Long> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<Long> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(Long value1, Long value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(Long value1, Long value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeIsNull() {
            addCriterion("store_operation_mode is null");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeIsNotNull() {
            addCriterion("store_operation_mode is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeEqualTo(Integer value) {
            addCriterion("store_operation_mode =", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeNotEqualTo(Integer value) {
            addCriterion("store_operation_mode <>", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeGreaterThan(Integer value) {
            addCriterion("store_operation_mode >", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("store_operation_mode >=", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeLessThan(Integer value) {
            addCriterion("store_operation_mode <", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeLessThanOrEqualTo(Integer value) {
            addCriterion("store_operation_mode <=", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeIn(List<Integer> values) {
            addCriterion("store_operation_mode in", values, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeNotIn(List<Integer> values) {
            addCriterion("store_operation_mode not in", values, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeBetween(Integer value1, Integer value2) {
            addCriterion("store_operation_mode between", value1, value2, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeNotBetween(Integer value1, Integer value2) {
            addCriterion("store_operation_mode not between", value1, value2, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andCityIdIsNull() {
            addCriterion("city_id is null");
            return (Criteria) this;
        }

        public Criteria andCityIdIsNotNull() {
            addCriterion("city_id is not null");
            return (Criteria) this;
        }

        public Criteria andCityIdEqualTo(Long value) {
            addCriterion("city_id =", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotEqualTo(Long value) {
            addCriterion("city_id <>", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdGreaterThan(Long value) {
            addCriterion("city_id >", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdGreaterThanOrEqualTo(Long value) {
            addCriterion("city_id >=", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdLessThan(Long value) {
            addCriterion("city_id <", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdLessThanOrEqualTo(Long value) {
            addCriterion("city_id <=", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdIn(List<Long> values) {
            addCriterion("city_id in", values, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotIn(List<Long> values) {
            addCriterion("city_id not in", values, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdBetween(Long value1, Long value2) {
            addCriterion("city_id between", value1, value2, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotBetween(Long value1, Long value2) {
            addCriterion("city_id not between", value1, value2, "cityId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andValidTimeStartIsNull() {
            addCriterion("valid_time_start is null");
            return (Criteria) this;
        }

        public Criteria andValidTimeStartIsNotNull() {
            addCriterion("valid_time_start is not null");
            return (Criteria) this;
        }

        public Criteria andValidTimeStartEqualTo(LocalDateTime value) {
            addCriterion("valid_time_start =", value, "validTimeStart");
            return (Criteria) this;
        }

        public Criteria andValidTimeStartNotEqualTo(LocalDateTime value) {
            addCriterion("valid_time_start <>", value, "validTimeStart");
            return (Criteria) this;
        }

        public Criteria andValidTimeStartGreaterThan(LocalDateTime value) {
            addCriterion("valid_time_start >", value, "validTimeStart");
            return (Criteria) this;
        }

        public Criteria andValidTimeStartGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("valid_time_start >=", value, "validTimeStart");
            return (Criteria) this;
        }

        public Criteria andValidTimeStartLessThan(LocalDateTime value) {
            addCriterion("valid_time_start <", value, "validTimeStart");
            return (Criteria) this;
        }

        public Criteria andValidTimeStartLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("valid_time_start <=", value, "validTimeStart");
            return (Criteria) this;
        }

        public Criteria andValidTimeStartIn(List<LocalDateTime> values) {
            addCriterion("valid_time_start in", values, "validTimeStart");
            return (Criteria) this;
        }

        public Criteria andValidTimeStartNotIn(List<LocalDateTime> values) {
            addCriterion("valid_time_start not in", values, "validTimeStart");
            return (Criteria) this;
        }

        public Criteria andValidTimeStartBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("valid_time_start between", value1, value2, "validTimeStart");
            return (Criteria) this;
        }

        public Criteria andValidTimeStartNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("valid_time_start not between", value1, value2, "validTimeStart");
            return (Criteria) this;
        }

        public Criteria andValidTimeEndIsNull() {
            addCriterion("valid_time_end is null");
            return (Criteria) this;
        }

        public Criteria andValidTimeEndIsNotNull() {
            addCriterion("valid_time_end is not null");
            return (Criteria) this;
        }

        public Criteria andValidTimeEndEqualTo(LocalDateTime value) {
            addCriterion("valid_time_end =", value, "validTimeEnd");
            return (Criteria) this;
        }

        public Criteria andValidTimeEndNotEqualTo(LocalDateTime value) {
            addCriterion("valid_time_end <>", value, "validTimeEnd");
            return (Criteria) this;
        }

        public Criteria andValidTimeEndGreaterThan(LocalDateTime value) {
            addCriterion("valid_time_end >", value, "validTimeEnd");
            return (Criteria) this;
        }

        public Criteria andValidTimeEndGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("valid_time_end >=", value, "validTimeEnd");
            return (Criteria) this;
        }

        public Criteria andValidTimeEndLessThan(LocalDateTime value) {
            addCriterion("valid_time_end <", value, "validTimeEnd");
            return (Criteria) this;
        }

        public Criteria andValidTimeEndLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("valid_time_end <=", value, "validTimeEnd");
            return (Criteria) this;
        }

        public Criteria andValidTimeEndIn(List<LocalDateTime> values) {
            addCriterion("valid_time_end in", values, "validTimeEnd");
            return (Criteria) this;
        }

        public Criteria andValidTimeEndNotIn(List<LocalDateTime> values) {
            addCriterion("valid_time_end not in", values, "validTimeEnd");
            return (Criteria) this;
        }

        public Criteria andValidTimeEndBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("valid_time_end between", value1, value2, "validTimeEnd");
            return (Criteria) this;
        }

        public Criteria andValidTimeEndNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("valid_time_end not between", value1, value2, "validTimeEnd");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}