package com.sankuai.shangou.logistics.sdms.dao.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class AssessDeliveryTimeConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public AssessDeliveryTimeConfigExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBelongStoreConfigIdIsNull() {
            addCriterion("belong_store_config_id is null");
            return (Criteria) this;
        }

        public Criteria andBelongStoreConfigIdIsNotNull() {
            addCriterion("belong_store_config_id is not null");
            return (Criteria) this;
        }

        public Criteria andBelongStoreConfigIdEqualTo(Long value) {
            addCriterion("belong_store_config_id =", value, "belongStoreConfigId");
            return (Criteria) this;
        }

        public Criteria andBelongStoreConfigIdNotEqualTo(Long value) {
            addCriterion("belong_store_config_id <>", value, "belongStoreConfigId");
            return (Criteria) this;
        }

        public Criteria andBelongStoreConfigIdGreaterThan(Long value) {
            addCriterion("belong_store_config_id >", value, "belongStoreConfigId");
            return (Criteria) this;
        }

        public Criteria andBelongStoreConfigIdGreaterThanOrEqualTo(Long value) {
            addCriterion("belong_store_config_id >=", value, "belongStoreConfigId");
            return (Criteria) this;
        }

        public Criteria andBelongStoreConfigIdLessThan(Long value) {
            addCriterion("belong_store_config_id <", value, "belongStoreConfigId");
            return (Criteria) this;
        }

        public Criteria andBelongStoreConfigIdLessThanOrEqualTo(Long value) {
            addCriterion("belong_store_config_id <=", value, "belongStoreConfigId");
            return (Criteria) this;
        }

        public Criteria andBelongStoreConfigIdIn(List<Long> values) {
            addCriterion("belong_store_config_id in", values, "belongStoreConfigId");
            return (Criteria) this;
        }

        public Criteria andBelongStoreConfigIdNotIn(List<Long> values) {
            addCriterion("belong_store_config_id not in", values, "belongStoreConfigId");
            return (Criteria) this;
        }

        public Criteria andBelongStoreConfigIdBetween(Long value1, Long value2) {
            addCriterion("belong_store_config_id between", value1, value2, "belongStoreConfigId");
            return (Criteria) this;
        }

        public Criteria andBelongStoreConfigIdNotBetween(Long value1, Long value2) {
            addCriterion("belong_store_config_id not between", value1, value2, "belongStoreConfigId");
            return (Criteria) this;
        }

        public Criteria andConfigJsonIsNull() {
            addCriterion("config_json is null");
            return (Criteria) this;
        }

        public Criteria andConfigJsonIsNotNull() {
            addCriterion("config_json is not null");
            return (Criteria) this;
        }

        public Criteria andConfigJsonEqualTo(String value) {
            addCriterion("config_json =", value, "configJson");
            return (Criteria) this;
        }

        public Criteria andConfigJsonNotEqualTo(String value) {
            addCriterion("config_json <>", value, "configJson");
            return (Criteria) this;
        }

        public Criteria andConfigJsonGreaterThan(String value) {
            addCriterion("config_json >", value, "configJson");
            return (Criteria) this;
        }

        public Criteria andConfigJsonGreaterThanOrEqualTo(String value) {
            addCriterion("config_json >=", value, "configJson");
            return (Criteria) this;
        }

        public Criteria andConfigJsonLessThan(String value) {
            addCriterion("config_json <", value, "configJson");
            return (Criteria) this;
        }

        public Criteria andConfigJsonLessThanOrEqualTo(String value) {
            addCriterion("config_json <=", value, "configJson");
            return (Criteria) this;
        }

        public Criteria andConfigJsonLike(String value) {
            addCriterion("config_json like", value, "configJson");
            return (Criteria) this;
        }

        public Criteria andConfigJsonNotLike(String value) {
            addCriterion("config_json not like", value, "configJson");
            return (Criteria) this;
        }

        public Criteria andConfigJsonIn(List<String> values) {
            addCriterion("config_json in", values, "configJson");
            return (Criteria) this;
        }

        public Criteria andConfigJsonNotIn(List<String> values) {
            addCriterion("config_json not in", values, "configJson");
            return (Criteria) this;
        }

        public Criteria andConfigJsonBetween(String value1, String value2) {
            addCriterion("config_json between", value1, value2, "configJson");
            return (Criteria) this;
        }

        public Criteria andConfigJsonNotBetween(String value1, String value2) {
            addCriterion("config_json not between", value1, value2, "configJson");
            return (Criteria) this;
        }

        public Criteria andCalcUnitIsNull() {
            addCriterion("calc_unit is null");
            return (Criteria) this;
        }

        public Criteria andCalcUnitIsNotNull() {
            addCriterion("calc_unit is not null");
            return (Criteria) this;
        }

        public Criteria andCalcUnitEqualTo(Integer value) {
            addCriterion("calc_unit =", value, "calcUnit");
            return (Criteria) this;
        }

        public Criteria andCalcUnitNotEqualTo(Integer value) {
            addCriterion("calc_unit <>", value, "calcUnit");
            return (Criteria) this;
        }

        public Criteria andCalcUnitGreaterThan(Integer value) {
            addCriterion("calc_unit >", value, "calcUnit");
            return (Criteria) this;
        }

        public Criteria andCalcUnitGreaterThanOrEqualTo(Integer value) {
            addCriterion("calc_unit >=", value, "calcUnit");
            return (Criteria) this;
        }

        public Criteria andCalcUnitLessThan(Integer value) {
            addCriterion("calc_unit <", value, "calcUnit");
            return (Criteria) this;
        }

        public Criteria andCalcUnitLessThanOrEqualTo(Integer value) {
            addCriterion("calc_unit <=", value, "calcUnit");
            return (Criteria) this;
        }

        public Criteria andCalcUnitIn(List<Integer> values) {
            addCriterion("calc_unit in", values, "calcUnit");
            return (Criteria) this;
        }

        public Criteria andCalcUnitNotIn(List<Integer> values) {
            addCriterion("calc_unit not in", values, "calcUnit");
            return (Criteria) this;
        }

        public Criteria andCalcUnitBetween(Integer value1, Integer value2) {
            addCriterion("calc_unit between", value1, value2, "calcUnit");
            return (Criteria) this;
        }

        public Criteria andCalcUnitNotBetween(Integer value1, Integer value2) {
            addCriterion("calc_unit not between", value1, value2, "calcUnit");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}