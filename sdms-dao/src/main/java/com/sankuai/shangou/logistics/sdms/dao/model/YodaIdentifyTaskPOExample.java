package com.sankuai.shangou.logistics.sdms.dao.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class YodaIdentifyTaskPOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public YodaIdentifyTaskPOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdIsNull() {
            addCriterion("delivery_order_id is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdIsNotNull() {
            addCriterion("delivery_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdEqualTo(Long value) {
            addCriterion("delivery_order_id =", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdNotEqualTo(Long value) {
            addCriterion("delivery_order_id <>", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdGreaterThan(Long value) {
            addCriterion("delivery_order_id >", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("delivery_order_id >=", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdLessThan(Long value) {
            addCriterion("delivery_order_id <", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdLessThanOrEqualTo(Long value) {
            addCriterion("delivery_order_id <=", value, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdIn(List<Long> values) {
            addCriterion("delivery_order_id in", values, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdNotIn(List<Long> values) {
            addCriterion("delivery_order_id not in", values, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdBetween(Long value1, Long value2) {
            addCriterion("delivery_order_id between", value1, value2, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andDeliveryOrderIdNotBetween(Long value1, Long value2) {
            addCriterion("delivery_order_id not between", value1, value2, "deliveryOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdIsNull() {
            addCriterion("channel_order_id is null");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdIsNotNull() {
            addCriterion("channel_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdEqualTo(String value) {
            addCriterion("channel_order_id =", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotEqualTo(String value) {
            addCriterion("channel_order_id <>", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdGreaterThan(String value) {
            addCriterion("channel_order_id >", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("channel_order_id >=", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdLessThan(String value) {
            addCriterion("channel_order_id <", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdLessThanOrEqualTo(String value) {
            addCriterion("channel_order_id <=", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdLike(String value) {
            addCriterion("channel_order_id like", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotLike(String value) {
            addCriterion("channel_order_id not like", value, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdIn(List<String> values) {
            addCriterion("channel_order_id in", values, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotIn(List<String> values) {
            addCriterion("channel_order_id not in", values, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdBetween(String value1, String value2) {
            addCriterion("channel_order_id between", value1, value2, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andChannelOrderIdNotBetween(String value1, String value2) {
            addCriterion("channel_order_id not between", value1, value2, "channelOrderId");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeIsNull() {
            addCriterion("order_biz_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeIsNotNull() {
            addCriterion("order_biz_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeEqualTo(Integer value) {
            addCriterion("order_biz_type =", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeNotEqualTo(Integer value) {
            addCriterion("order_biz_type <>", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeGreaterThan(Integer value) {
            addCriterion("order_biz_type >", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_biz_type >=", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeLessThan(Integer value) {
            addCriterion("order_biz_type <", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeLessThanOrEqualTo(Integer value) {
            addCriterion("order_biz_type <=", value, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeIn(List<Integer> values) {
            addCriterion("order_biz_type in", values, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeNotIn(List<Integer> values) {
            addCriterion("order_biz_type not in", values, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeBetween(Integer value1, Integer value2) {
            addCriterion("order_biz_type between", value1, value2, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andOrderBizTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("order_biz_type not between", value1, value2, "orderBizType");
            return (Criteria) this;
        }

        public Criteria andRelRuleIdIsNull() {
            addCriterion("rel_rule_id is null");
            return (Criteria) this;
        }

        public Criteria andRelRuleIdIsNotNull() {
            addCriterion("rel_rule_id is not null");
            return (Criteria) this;
        }

        public Criteria andRelRuleIdEqualTo(Long value) {
            addCriterion("rel_rule_id =", value, "relRuleId");
            return (Criteria) this;
        }

        public Criteria andRelRuleIdNotEqualTo(Long value) {
            addCriterion("rel_rule_id <>", value, "relRuleId");
            return (Criteria) this;
        }

        public Criteria andRelRuleIdGreaterThan(Long value) {
            addCriterion("rel_rule_id >", value, "relRuleId");
            return (Criteria) this;
        }

        public Criteria andRelRuleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rel_rule_id >=", value, "relRuleId");
            return (Criteria) this;
        }

        public Criteria andRelRuleIdLessThan(Long value) {
            addCriterion("rel_rule_id <", value, "relRuleId");
            return (Criteria) this;
        }

        public Criteria andRelRuleIdLessThanOrEqualTo(Long value) {
            addCriterion("rel_rule_id <=", value, "relRuleId");
            return (Criteria) this;
        }

        public Criteria andRelRuleIdIn(List<Long> values) {
            addCriterion("rel_rule_id in", values, "relRuleId");
            return (Criteria) this;
        }

        public Criteria andRelRuleIdNotIn(List<Long> values) {
            addCriterion("rel_rule_id not in", values, "relRuleId");
            return (Criteria) this;
        }

        public Criteria andRelRuleIdBetween(Long value1, Long value2) {
            addCriterion("rel_rule_id between", value1, value2, "relRuleId");
            return (Criteria) this;
        }

        public Criteria andRelRuleIdNotBetween(Long value1, Long value2) {
            addCriterion("rel_rule_id not between", value1, value2, "relRuleId");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeIsNull() {
            addCriterion("collect_content_type is null");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeIsNotNull() {
            addCriterion("collect_content_type is not null");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeEqualTo(String value) {
            addCriterion("collect_content_type =", value, "collectContentType");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeNotEqualTo(String value) {
            addCriterion("collect_content_type <>", value, "collectContentType");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeGreaterThan(String value) {
            addCriterion("collect_content_type >", value, "collectContentType");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeGreaterThanOrEqualTo(String value) {
            addCriterion("collect_content_type >=", value, "collectContentType");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeLessThan(String value) {
            addCriterion("collect_content_type <", value, "collectContentType");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeLessThanOrEqualTo(String value) {
            addCriterion("collect_content_type <=", value, "collectContentType");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeLike(String value) {
            addCriterion("collect_content_type like", value, "collectContentType");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeNotLike(String value) {
            addCriterion("collect_content_type not like", value, "collectContentType");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeIn(List<String> values) {
            addCriterion("collect_content_type in", values, "collectContentType");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeNotIn(List<String> values) {
            addCriterion("collect_content_type not in", values, "collectContentType");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeBetween(String value1, String value2) {
            addCriterion("collect_content_type between", value1, value2, "collectContentType");
            return (Criteria) this;
        }

        public Criteria andCollectContentTypeNotBetween(String value1, String value2) {
            addCriterion("collect_content_type not between", value1, value2, "collectContentType");
            return (Criteria) this;
        }

        public Criteria andCollectModeIsNull() {
            addCriterion("collect_mode is null");
            return (Criteria) this;
        }

        public Criteria andCollectModeIsNotNull() {
            addCriterion("collect_mode is not null");
            return (Criteria) this;
        }

        public Criteria andCollectModeEqualTo(Integer value) {
            addCriterion("collect_mode =", value, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeNotEqualTo(Integer value) {
            addCriterion("collect_mode <>", value, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeGreaterThan(Integer value) {
            addCriterion("collect_mode >", value, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("collect_mode >=", value, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeLessThan(Integer value) {
            addCriterion("collect_mode <", value, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeLessThanOrEqualTo(Integer value) {
            addCriterion("collect_mode <=", value, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeIn(List<Integer> values) {
            addCriterion("collect_mode in", values, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeNotIn(List<Integer> values) {
            addCriterion("collect_mode not in", values, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeBetween(Integer value1, Integer value2) {
            addCriterion("collect_mode between", value1, value2, "collectMode");
            return (Criteria) this;
        }

        public Criteria andCollectModeNotBetween(Integer value1, Integer value2) {
            addCriterion("collect_mode not between", value1, value2, "collectMode");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIsNull() {
            addCriterion("rider_account_id is null");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIsNotNull() {
            addCriterion("rider_account_id is not null");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdEqualTo(Long value) {
            addCriterion("rider_account_id =", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotEqualTo(Long value) {
            addCriterion("rider_account_id <>", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdGreaterThan(Long value) {
            addCriterion("rider_account_id >", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rider_account_id >=", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdLessThan(Long value) {
            addCriterion("rider_account_id <", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdLessThanOrEqualTo(Long value) {
            addCriterion("rider_account_id <=", value, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdIn(List<Long> values) {
            addCriterion("rider_account_id in", values, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotIn(List<Long> values) {
            addCriterion("rider_account_id not in", values, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdBetween(Long value1, Long value2) {
            addCriterion("rider_account_id between", value1, value2, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andRiderAccountIdNotBetween(Long value1, Long value2) {
            addCriterion("rider_account_id not between", value1, value2, "riderAccountId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(Long value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(Long value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(Long value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(Long value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(Long value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<Long> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<Long> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(Long value1, Long value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(Long value1, Long value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIsNull() {
            addCriterion("task_status is null");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIsNotNull() {
            addCriterion("task_status is not null");
            return (Criteria) this;
        }

        public Criteria andTaskStatusEqualTo(Integer value) {
            addCriterion("task_status =", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotEqualTo(Integer value) {
            addCriterion("task_status <>", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusGreaterThan(Integer value) {
            addCriterion("task_status >", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("task_status >=", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLessThan(Integer value) {
            addCriterion("task_status <", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLessThanOrEqualTo(Integer value) {
            addCriterion("task_status <=", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIn(List<Integer> values) {
            addCriterion("task_status in", values, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotIn(List<Integer> values) {
            addCriterion("task_status not in", values, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusBetween(Integer value1, Integer value2) {
            addCriterion("task_status between", value1, value2, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("task_status not between", value1, value2, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeIsNull() {
            addCriterion("complete_time is null");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeIsNotNull() {
            addCriterion("complete_time is not null");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeEqualTo(LocalDateTime value) {
            addCriterion("complete_time =", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeNotEqualTo(LocalDateTime value) {
            addCriterion("complete_time <>", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeGreaterThan(LocalDateTime value) {
            addCriterion("complete_time >", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("complete_time >=", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeLessThan(LocalDateTime value) {
            addCriterion("complete_time <", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("complete_time <=", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeIn(List<LocalDateTime> values) {
            addCriterion("complete_time in", values, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeNotIn(List<LocalDateTime> values) {
            addCriterion("complete_time not in", values, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("complete_time between", value1, value2, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("complete_time not between", value1, value2, "completeTime");
            return (Criteria) this;
        }

        public Criteria andFaceIdentifyResultIsNull() {
            addCriterion("face_identify_result is null");
            return (Criteria) this;
        }

        public Criteria andFaceIdentifyResultIsNotNull() {
            addCriterion("face_identify_result is not null");
            return (Criteria) this;
        }

        public Criteria andFaceIdentifyResultEqualTo(Integer value) {
            addCriterion("face_identify_result =", value, "faceIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andFaceIdentifyResultNotEqualTo(Integer value) {
            addCriterion("face_identify_result <>", value, "faceIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andFaceIdentifyResultGreaterThan(Integer value) {
            addCriterion("face_identify_result >", value, "faceIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andFaceIdentifyResultGreaterThanOrEqualTo(Integer value) {
            addCriterion("face_identify_result >=", value, "faceIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andFaceIdentifyResultLessThan(Integer value) {
            addCriterion("face_identify_result <", value, "faceIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andFaceIdentifyResultLessThanOrEqualTo(Integer value) {
            addCriterion("face_identify_result <=", value, "faceIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andFaceIdentifyResultIn(List<Integer> values) {
            addCriterion("face_identify_result in", values, "faceIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andFaceIdentifyResultNotIn(List<Integer> values) {
            addCriterion("face_identify_result not in", values, "faceIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andFaceIdentifyResultBetween(Integer value1, Integer value2) {
            addCriterion("face_identify_result between", value1, value2, "faceIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andFaceIdentifyResultNotBetween(Integer value1, Integer value2) {
            addCriterion("face_identify_result not between", value1, value2, "faceIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andHelmetIdentifyResultIsNull() {
            addCriterion("helmet_identify_result is null");
            return (Criteria) this;
        }

        public Criteria andHelmetIdentifyResultIsNotNull() {
            addCriterion("helmet_identify_result is not null");
            return (Criteria) this;
        }

        public Criteria andHelmetIdentifyResultEqualTo(Integer value) {
            addCriterion("helmet_identify_result =", value, "helmetIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andHelmetIdentifyResultNotEqualTo(Integer value) {
            addCriterion("helmet_identify_result <>", value, "helmetIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andHelmetIdentifyResultGreaterThan(Integer value) {
            addCriterion("helmet_identify_result >", value, "helmetIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andHelmetIdentifyResultGreaterThanOrEqualTo(Integer value) {
            addCriterion("helmet_identify_result >=", value, "helmetIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andHelmetIdentifyResultLessThan(Integer value) {
            addCriterion("helmet_identify_result <", value, "helmetIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andHelmetIdentifyResultLessThanOrEqualTo(Integer value) {
            addCriterion("helmet_identify_result <=", value, "helmetIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andHelmetIdentifyResultIn(List<Integer> values) {
            addCriterion("helmet_identify_result in", values, "helmetIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andHelmetIdentifyResultNotIn(List<Integer> values) {
            addCriterion("helmet_identify_result not in", values, "helmetIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andHelmetIdentifyResultBetween(Integer value1, Integer value2) {
            addCriterion("helmet_identify_result between", value1, value2, "helmetIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andHelmetIdentifyResultNotBetween(Integer value1, Integer value2) {
            addCriterion("helmet_identify_result not between", value1, value2, "helmetIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andDressingIdentifyResultIsNull() {
            addCriterion("dressing_identify_result is null");
            return (Criteria) this;
        }

        public Criteria andDressingIdentifyResultIsNotNull() {
            addCriterion("dressing_identify_result is not null");
            return (Criteria) this;
        }

        public Criteria andDressingIdentifyResultEqualTo(Integer value) {
            addCriterion("dressing_identify_result =", value, "dressingIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andDressingIdentifyResultNotEqualTo(Integer value) {
            addCriterion("dressing_identify_result <>", value, "dressingIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andDressingIdentifyResultGreaterThan(Integer value) {
            addCriterion("dressing_identify_result >", value, "dressingIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andDressingIdentifyResultGreaterThanOrEqualTo(Integer value) {
            addCriterion("dressing_identify_result >=", value, "dressingIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andDressingIdentifyResultLessThan(Integer value) {
            addCriterion("dressing_identify_result <", value, "dressingIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andDressingIdentifyResultLessThanOrEqualTo(Integer value) {
            addCriterion("dressing_identify_result <=", value, "dressingIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andDressingIdentifyResultIn(List<Integer> values) {
            addCriterion("dressing_identify_result in", values, "dressingIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andDressingIdentifyResultNotIn(List<Integer> values) {
            addCriterion("dressing_identify_result not in", values, "dressingIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andDressingIdentifyResultBetween(Integer value1, Integer value2) {
            addCriterion("dressing_identify_result between", value1, value2, "dressingIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andDressingIdentifyResultNotBetween(Integer value1, Integer value2) {
            addCriterion("dressing_identify_result not between", value1, value2, "dressingIdentifyResult");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andPushTaskTimeIsNull() {
            addCriterion("push_task_time is null");
            return (Criteria) this;
        }

        public Criteria andPushTaskTimeIsNotNull() {
            addCriterion("push_task_time is not null");
            return (Criteria) this;
        }

        public Criteria andPushTaskTimeEqualTo(LocalDateTime value) {
            addCriterion("push_task_time =", value, "pushTaskTime");
            return (Criteria) this;
        }

        public Criteria andPushTaskTimeNotEqualTo(LocalDateTime value) {
            addCriterion("push_task_time <>", value, "pushTaskTime");
            return (Criteria) this;
        }

        public Criteria andPushTaskTimeGreaterThan(LocalDateTime value) {
            addCriterion("push_task_time >", value, "pushTaskTime");
            return (Criteria) this;
        }

        public Criteria andPushTaskTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("push_task_time >=", value, "pushTaskTime");
            return (Criteria) this;
        }

        public Criteria andPushTaskTimeLessThan(LocalDateTime value) {
            addCriterion("push_task_time <", value, "pushTaskTime");
            return (Criteria) this;
        }

        public Criteria andPushTaskTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("push_task_time <=", value, "pushTaskTime");
            return (Criteria) this;
        }

        public Criteria andPushTaskTimeIn(List<LocalDateTime> values) {
            addCriterion("push_task_time in", values, "pushTaskTime");
            return (Criteria) this;
        }

        public Criteria andPushTaskTimeNotIn(List<LocalDateTime> values) {
            addCriterion("push_task_time not in", values, "pushTaskTime");
            return (Criteria) this;
        }

        public Criteria andPushTaskTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("push_task_time between", value1, value2, "pushTaskTime");
            return (Criteria) this;
        }

        public Criteria andPushTaskTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("push_task_time not between", value1, value2, "pushTaskTime");
            return (Criteria) this;
        }

        public Criteria andResultCheckPassIsNull() {
            addCriterion("result_check_pass is null");
            return (Criteria) this;
        }

        public Criteria andResultCheckPassIsNotNull() {
            addCriterion("result_check_pass is not null");
            return (Criteria) this;
        }

        public Criteria andResultCheckPassEqualTo(Integer value) {
            addCriterion("result_check_pass =", value, "resultCheckPass");
            return (Criteria) this;
        }

        public Criteria andResultCheckPassNotEqualTo(Integer value) {
            addCriterion("result_check_pass <>", value, "resultCheckPass");
            return (Criteria) this;
        }

        public Criteria andResultCheckPassGreaterThan(Integer value) {
            addCriterion("result_check_pass >", value, "resultCheckPass");
            return (Criteria) this;
        }

        public Criteria andResultCheckPassGreaterThanOrEqualTo(Integer value) {
            addCriterion("result_check_pass >=", value, "resultCheckPass");
            return (Criteria) this;
        }

        public Criteria andResultCheckPassLessThan(Integer value) {
            addCriterion("result_check_pass <", value, "resultCheckPass");
            return (Criteria) this;
        }

        public Criteria andResultCheckPassLessThanOrEqualTo(Integer value) {
            addCriterion("result_check_pass <=", value, "resultCheckPass");
            return (Criteria) this;
        }

        public Criteria andResultCheckPassIn(List<Integer> values) {
            addCriterion("result_check_pass in", values, "resultCheckPass");
            return (Criteria) this;
        }

        public Criteria andResultCheckPassNotIn(List<Integer> values) {
            addCriterion("result_check_pass not in", values, "resultCheckPass");
            return (Criteria) this;
        }

        public Criteria andResultCheckPassBetween(Integer value1, Integer value2) {
            addCriterion("result_check_pass between", value1, value2, "resultCheckPass");
            return (Criteria) this;
        }

        public Criteria andResultCheckPassNotBetween(Integer value1, Integer value2) {
            addCriterion("result_check_pass not between", value1, value2, "resultCheckPass");
            return (Criteria) this;
        }

        public Criteria andCallbackCodeIsNull() {
            addCriterion("callback_code is null");
            return (Criteria) this;
        }

        public Criteria andCallbackCodeIsNotNull() {
            addCriterion("callback_code is not null");
            return (Criteria) this;
        }

        public Criteria andCallbackCodeEqualTo(String value) {
            addCriterion("callback_code =", value, "callbackCode");
            return (Criteria) this;
        }

        public Criteria andCallbackCodeNotEqualTo(String value) {
            addCriterion("callback_code <>", value, "callbackCode");
            return (Criteria) this;
        }

        public Criteria andCallbackCodeGreaterThan(String value) {
            addCriterion("callback_code >", value, "callbackCode");
            return (Criteria) this;
        }

        public Criteria andCallbackCodeGreaterThanOrEqualTo(String value) {
            addCriterion("callback_code >=", value, "callbackCode");
            return (Criteria) this;
        }

        public Criteria andCallbackCodeLessThan(String value) {
            addCriterion("callback_code <", value, "callbackCode");
            return (Criteria) this;
        }

        public Criteria andCallbackCodeLessThanOrEqualTo(String value) {
            addCriterion("callback_code <=", value, "callbackCode");
            return (Criteria) this;
        }

        public Criteria andCallbackCodeLike(String value) {
            addCriterion("callback_code like", value, "callbackCode");
            return (Criteria) this;
        }

        public Criteria andCallbackCodeNotLike(String value) {
            addCriterion("callback_code not like", value, "callbackCode");
            return (Criteria) this;
        }

        public Criteria andCallbackCodeIn(List<String> values) {
            addCriterion("callback_code in", values, "callbackCode");
            return (Criteria) this;
        }

        public Criteria andCallbackCodeNotIn(List<String> values) {
            addCriterion("callback_code not in", values, "callbackCode");
            return (Criteria) this;
        }

        public Criteria andCallbackCodeBetween(String value1, String value2) {
            addCriterion("callback_code between", value1, value2, "callbackCode");
            return (Criteria) this;
        }

        public Criteria andCallbackCodeNotBetween(String value1, String value2) {
            addCriterion("callback_code not between", value1, value2, "callbackCode");
            return (Criteria) this;
        }

        public Criteria andCallbackMsgIsNull() {
            addCriterion("callback_msg is null");
            return (Criteria) this;
        }

        public Criteria andCallbackMsgIsNotNull() {
            addCriterion("callback_msg is not null");
            return (Criteria) this;
        }

        public Criteria andCallbackMsgEqualTo(String value) {
            addCriterion("callback_msg =", value, "callbackMsg");
            return (Criteria) this;
        }

        public Criteria andCallbackMsgNotEqualTo(String value) {
            addCriterion("callback_msg <>", value, "callbackMsg");
            return (Criteria) this;
        }

        public Criteria andCallbackMsgGreaterThan(String value) {
            addCriterion("callback_msg >", value, "callbackMsg");
            return (Criteria) this;
        }

        public Criteria andCallbackMsgGreaterThanOrEqualTo(String value) {
            addCriterion("callback_msg >=", value, "callbackMsg");
            return (Criteria) this;
        }

        public Criteria andCallbackMsgLessThan(String value) {
            addCriterion("callback_msg <", value, "callbackMsg");
            return (Criteria) this;
        }

        public Criteria andCallbackMsgLessThanOrEqualTo(String value) {
            addCriterion("callback_msg <=", value, "callbackMsg");
            return (Criteria) this;
        }

        public Criteria andCallbackMsgLike(String value) {
            addCriterion("callback_msg like", value, "callbackMsg");
            return (Criteria) this;
        }

        public Criteria andCallbackMsgNotLike(String value) {
            addCriterion("callback_msg not like", value, "callbackMsg");
            return (Criteria) this;
        }

        public Criteria andCallbackMsgIn(List<String> values) {
            addCriterion("callback_msg in", values, "callbackMsg");
            return (Criteria) this;
        }

        public Criteria andCallbackMsgNotIn(List<String> values) {
            addCriterion("callback_msg not in", values, "callbackMsg");
            return (Criteria) this;
        }

        public Criteria andCallbackMsgBetween(String value1, String value2) {
            addCriterion("callback_msg between", value1, value2, "callbackMsg");
            return (Criteria) this;
        }

        public Criteria andCallbackMsgNotBetween(String value1, String value2) {
            addCriterion("callback_msg not between", value1, value2, "callbackMsg");
            return (Criteria) this;
        }

        public Criteria andTaskExpireTimeIsNull() {
            addCriterion("task_expire_time is null");
            return (Criteria) this;
        }

        public Criteria andTaskExpireTimeIsNotNull() {
            addCriterion("task_expire_time is not null");
            return (Criteria) this;
        }

        public Criteria andTaskExpireTimeEqualTo(LocalDateTime value) {
            addCriterion("task_expire_time =", value, "taskExpireTime");
            return (Criteria) this;
        }

        public Criteria andTaskExpireTimeNotEqualTo(LocalDateTime value) {
            addCriterion("task_expire_time <>", value, "taskExpireTime");
            return (Criteria) this;
        }

        public Criteria andTaskExpireTimeGreaterThan(LocalDateTime value) {
            addCriterion("task_expire_time >", value, "taskExpireTime");
            return (Criteria) this;
        }

        public Criteria andTaskExpireTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("task_expire_time >=", value, "taskExpireTime");
            return (Criteria) this;
        }

        public Criteria andTaskExpireTimeLessThan(LocalDateTime value) {
            addCriterion("task_expire_time <", value, "taskExpireTime");
            return (Criteria) this;
        }

        public Criteria andTaskExpireTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("task_expire_time <=", value, "taskExpireTime");
            return (Criteria) this;
        }

        public Criteria andTaskExpireTimeIn(List<LocalDateTime> values) {
            addCriterion("task_expire_time in", values, "taskExpireTime");
            return (Criteria) this;
        }

        public Criteria andTaskExpireTimeNotIn(List<LocalDateTime> values) {
            addCriterion("task_expire_time not in", values, "taskExpireTime");
            return (Criteria) this;
        }

        public Criteria andTaskExpireTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("task_expire_time between", value1, value2, "taskExpireTime");
            return (Criteria) this;
        }

        public Criteria andTaskExpireTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("task_expire_time not between", value1, value2, "taskExpireTime");
            return (Criteria) this;
        }

        public Criteria andPunishInfoIsNull() {
            addCriterion("punish_info is null");
            return (Criteria) this;
        }

        public Criteria andPunishInfoIsNotNull() {
            addCriterion("punish_info is not null");
            return (Criteria) this;
        }

        public Criteria andPunishInfoEqualTo(String value) {
            addCriterion("punish_info =", value, "punishInfo");
            return (Criteria) this;
        }

        public Criteria andPunishInfoNotEqualTo(String value) {
            addCriterion("punish_info <>", value, "punishInfo");
            return (Criteria) this;
        }

        public Criteria andPunishInfoGreaterThan(String value) {
            addCriterion("punish_info >", value, "punishInfo");
            return (Criteria) this;
        }

        public Criteria andPunishInfoGreaterThanOrEqualTo(String value) {
            addCriterion("punish_info >=", value, "punishInfo");
            return (Criteria) this;
        }

        public Criteria andPunishInfoLessThan(String value) {
            addCriterion("punish_info <", value, "punishInfo");
            return (Criteria) this;
        }

        public Criteria andPunishInfoLessThanOrEqualTo(String value) {
            addCriterion("punish_info <=", value, "punishInfo");
            return (Criteria) this;
        }

        public Criteria andPunishInfoLike(String value) {
            addCriterion("punish_info like", value, "punishInfo");
            return (Criteria) this;
        }

        public Criteria andPunishInfoNotLike(String value) {
            addCriterion("punish_info not like", value, "punishInfo");
            return (Criteria) this;
        }

        public Criteria andPunishInfoIn(List<String> values) {
            addCriterion("punish_info in", values, "punishInfo");
            return (Criteria) this;
        }

        public Criteria andPunishInfoNotIn(List<String> values) {
            addCriterion("punish_info not in", values, "punishInfo");
            return (Criteria) this;
        }

        public Criteria andPunishInfoBetween(String value1, String value2) {
            addCriterion("punish_info between", value1, value2, "punishInfo");
            return (Criteria) this;
        }

        public Criteria andPunishInfoNotBetween(String value1, String value2) {
            addCriterion("punish_info not between", value1, value2, "punishInfo");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}