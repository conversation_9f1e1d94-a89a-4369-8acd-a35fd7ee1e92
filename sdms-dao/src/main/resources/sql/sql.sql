-- MySQL dump 10.13  Distrib 5.7.34, for osx10.16 (x86_64)
--
-- Host: ************    Database: dh_labor_test
-- ------------------------------------------------------
-- Server version	5.7.26-30102-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup
--

SET @@GLOBAL.GTID_PURGED='131057b5-8790-11ea-9c75-0a580a247f5e:1-21484556,
a33453f5-baec-11e8-8b6d-00220e71d53f:1-7106436';

--
-- Table structure for table `app_poi_employee_wmsj_dely_detail_dd`
--

DROP TABLE IF EXISTS `app_poi_employee_wmsj_dely_detail_dd`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `app_poi_employee_wmsj_dely_detail_dd` (
                                                        `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
                                                        `poi_id` bigint(20) NOT NULL COMMENT '蔬果门店id',
                                                        `employee_id` bigint(20) NOT NULL COMMENT '员工id,即骑手id',
                                                        `account_name` varchar(100) NOT NULL COMMENT '账户名称',
                                                        `employee_name` varchar(100) NOT NULL COMMENT '员工姓名',
                                                        `order_id_view` varchar(30) NOT NULL COMMENT '订单展示id',
                                                        `delivery_id` bigint(20) DEFAULT NULL COMMENT '配送运单id',
                                                        `delivery_interval` decimal(20,2) DEFAULT NULL COMMENT '单均履约时长（整单）,统计周期内，不含一元购>的实时单的平均送达时间-支付时间',
                                                        `on_way_interval` decimal(20,2) DEFAULT NULL COMMENT '在途时长，统计周期内，不含一元购的实时单的送达时间-骑手取货时间',
                                                        `store_out_interval` decimal(20,2) DEFAULT NULL COMMENT '出库时长，统计周期内，不含一元购的实时单的骑手取货时间-支付时间',
                                                        `dt` varchar(100) NOT NULL COMMENT '分区字段,YYYYMMDD格式',
                                                        `dt_code` bigint(20) DEFAULT NULL COMMENT '分区字段,unixtime格式',
                                                        `origin_gmv_amt` decimal(20,2) DEFAULT NULL COMMENT '原价GMV',
                                                        `actual_gmv_amt` decimal(20,2) DEFAULT NULL COMMENT '实付GMV',
                                                        `account_id` bigint(20) NOT NULL COMMENT '百川账号id',
                                                        UNIQUE KEY `unique_tenant_poi_employee_order` (`tenant_id`,`poi_id`,`employee_id`,`order_id_view`),
                                                        KEY `idx_poi_employee_dt` (`poi_id`,`employee_id`,`dt_code`),
                                                        KEY `idx_dt` (`dt_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='歪马送酒门店员工运单明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `app_poi_employee_wmsj_order_detail_dd`
--

DROP TABLE IF EXISTS `app_poi_employee_wmsj_order_detail_dd`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `app_poi_employee_wmsj_order_detail_dd` (
                                                         `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
                                                         `poi_id` bigint(20) NOT NULL COMMENT '蔬果门店id',
                                                         `employee_id` bigint(20) NOT NULL COMMENT '员工id,即骑手id',
                                                         `account_name` varchar(100) NOT NULL COMMENT '账户名称',
                                                         `employee_name` varchar(100) NOT NULL COMMENT '员工姓名',
                                                         `order_biz_type` bigint(20) DEFAULT NULL COMMENT '业务类型ID',
                                                         `order_id` bigint(20) DEFAULT NULL COMMENT '订单id',
                                                         `order_id_view` varchar(30) NOT NULL COMMENT '订单展示id',
                                                         `dt` varchar(100) NOT NULL COMMENT '分区字段,YYYYMMDD格式',
                                                         `dt_code` bigint(20) DEFAULT NULL COMMENT '分区字段,unixtime格式',
                                                         `account_id` bigint(20) NOT NULL COMMENT '百川账号id',
                                                         `unit_product_price` decimal(20,2) DEFAULT NULL COMMENT '订单单瓶价格',
                                                         `origin_gmv_amt` decimal(20,2) DEFAULT NULL COMMENT '原价GMV',
                                                         `actual_gmv_amt` decimal(20,2) DEFAULT NULL COMMENT '实付GMV',
                                                         `ord_sale_bottle_cnt` bigint(20) DEFAULT NULL COMMENT '订单售卖瓶数',
                                                         `is_first_extone_risk_flag` bigint(20) DEFAULT NULL COMMENT '是否是首单',
                                                         UNIQUE KEY `unique_tenant_poi_employee_order` (`tenant_id`,`poi_id`,`employee_id`,`order_id_view`),
                                                         KEY `idx_poi_employee_dt` (`poi_id`,`employee_id`,`dt_code`),
                                                         KEY `idx_dt` (`dt_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='歪马送酒门店员工运单明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `app_poi_employee_wmsj_order_time_dd`
--

DROP TABLE IF EXISTS `app_poi_employee_wmsj_order_time_dd`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `app_poi_employee_wmsj_order_time_dd` (
                                                       `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
                                                       `poi_id` bigint(20) NOT NULL COMMENT '蔬果门店id',
                                                       `employee_id` bigint(20) NOT NULL COMMENT '员工id,即骑手id',
                                                       `account_id` bigint(20) NOT NULL COMMENT '百川账号id,即骑手id',
                                                       `account_name` varchar(100) NOT NULL COMMENT '账户名称',
                                                       `employee_name` varchar(100) NOT NULL COMMENT '员工姓名',
                                                       `order_biz_type` bigint(20) DEFAULT NULL COMMENT '业务类型ID:业务类型ID:101-美团外卖,501-有赞平台',
                                                       `order_id` bigint(20) DEFAULT NULL COMMENT '订单id',
                                                       `order_id_view` varchar(64) NOT NULL COMMENT '订单展示id',
                                                       `rider_arrived_time` bigint(20) NOT NULL COMMENT '订单配送完成时间，unix_timestamp格式',
                                                       `dt` varchar(100) NOT NULL COMMENT '分区字段,YYYYMMDD格式',
                                                       `dt_code` bigint(20) DEFAULT NULL COMMENT '分区字段,unixtime格式',
                                                       `ord_sale_bottle_cnt` varchar(45) DEFAULT NULL COMMENT '订单售卖瓶数',
                                                       UNIQUE KEY `unique_tenant_poi_employee_order` (`tenant_id`,`poi_id`,`employee_id`,`order_id_view`),
                                                       KEY `idx_poi_employee_dt` (`poi_id`,`employee_id`,`dt_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='歪马送酒门店员工订单时间明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `app_poi_employee_wmsj_statistics_sd`
--

DROP TABLE IF EXISTS `app_poi_employee_wmsj_statistics_sd`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `app_poi_employee_wmsj_statistics_sd` (
                                                       `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
                                                       `poi_id` bigint(20) NOT NULL COMMENT '蔬果门店id',
                                                       `employee_id` bigint(20) NOT NULL COMMENT '员工id,即骑手id',
                                                       `account_name` varchar(100) NOT NULL COMMENT '账户名称',
                                                       `employee_name` varchar(100) NOT NULL COMMENT '员工姓名',
                                                       `first_usr_ord_num` bigint(20) DEFAULT NULL COMMENT '统计周期内>，该账号拉取的、未取消的新客下单单量总和（不含一元购订单）',
                                                       `first_usr_extrisk_ord_num` bigint(20) DEFAULT NULL COMMENT '统计周期内，该账号拉取 >的、未取消的新客下单单量总和（不含一元购订单、不含风控单）',
                                                       `second_usr_extrisk_ord_num` bigint(20) DEFAULT NULL COMMENT '统计周期内，该账号拉取的、未取消的二单单量的总和（不含一元购、不含风控单）',
                                                       `actual_gmv_extont_amt` decimal(20,2) DEFAULT NULL COMMENT '订单实付金额，统计周期内 >，未取消订单的用户实付金额 （不含一元购）',
                                                       `actual_gmv_amt` decimal(20,2) DEFAULT NULL COMMENT '订单实付金额，统计周期内，未取消订单的用户实付金额',
                                                       `one_price_ord_num` bigint(20) DEFAULT NULL COMMENT '一元购单量',
                                                       `risk_ord_num` bigint(20) DEFAULT NULL COMMENT '风控单量',
                                                       `valid_focus_num` bigint(20) DEFAULT NULL COMMENT '有效关注量',
                                                       `cancel_focus_num` bigint(20) DEFAULT NULL COMMENT '取消关注量',
                                                       `delivery_ord_num` bigint(20) DEFAULT NULL COMMENT '配送单量,统计周期内，运单状态>为已送达的实时订单总数（不含一元购）',
                                                       `fifteen_arrive_order_num` bigint(20) DEFAULT NULL COMMENT '15分钟履约送达订单',
                                                       `twenty_five_arrive_order_num` bigint(20) DEFAULT NULL COMMENT '25分钟履约送达订单',
                                                       `overtime_ord_num` bigint(20) DEFAULT NULL COMMENT '超时订单数',
                                                       `valid_reply_cnt` bigint(20) DEFAULT NULL COMMENT '有效>会话总数',
                                                       `user_reply_cnt` bigint(20) DEFAULT NULL COMMENT '用户主动咨询会话总数',
                                                       `dt` varchar(100) NOT NULL COMMENT '分区字段,YYYYMMDD格式',
                                                       `dt_code` bigint(20) DEFAULT NULL COMMENT '分区字段,unixtime格式',
                                                       `account_id` bigint(20) NOT NULL COMMENT '百川账号id',
                                                       `second_usr_ord_num` bigint(20) DEFAULT NULL COMMENT '统计周期内，该账号拉取的、未取消的二单单量的总和（不含一元购）',
                                                       `first_usr_extrisk_ord_num_new` bigint(20) DEFAULT NULL COMMENT '统计周期内，该账号拉取的、未取消的新客下单单量总和（不含一元购订单、不含风控单）',
                                                       `wechat_vaild_friend_num` bigint(20) DEFAULT NULL COMMENT '统计周期内，该账号 赋能企微有效好友量',
                                                       `wechat_cancle_friend_num` bigint(20) DEFAULT NULL COMMENT '统计周期内，该账号 赋能企微取消好友量',
                                                       `valid_pro_ord_num` bigint(20) DEFAULT NULL COMMENT '统计周期内，该账号 赋能有效商品激励订单 不包含黑名单商品的订单',
                                                       `promote_pro_ord_num` bigint(20) DEFAULT NULL COMMENT '商品推广单量\n\n',
                                                       UNIQUE KEY `unique_tenant_poi_employee_dt` (`tenant_id`,`poi_id`,`employee_id`,`dt`),
                                                       KEY `idx_poi_employee_dt` (`poi_id`,`employee_id`,`dt_code`),
                                                       KEY `idx_dt` (`dt_code`),
                                                       KEY `idx_app_poi_employee_wmsj_statistics_sd_employee_id_dt_code` (`employee_id`,`dt_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='歪马送酒门店员工算薪统计';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `approval_activity_turn_anyone`
--

DROP TABLE IF EXISTS `approval_activity_turn_anyone`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `approval_activity_turn_anyone` (
                                                 `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                                 `base_info_id` bigint(20) NOT NULL COMMENT '审批基础信息id',
                                                 `approval_candidates` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '审批候选人列表',
                                                 `activity_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '流程类型，如一审、二审、终审等',
                                                 `activity_status` int(11) NOT NULL DEFAULT '1' COMMENT '流程状态 1待审批 2通过 4驳回 8撤回',
                                                 `activity_status_text` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '流程状态说明',
                                                 `activity_status_json` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '流程状态扩展说明',
                                                 `received_time` datetime NOT NULL COMMENT '审批到达时间',
                                                 `finish_time` datetime DEFAULT NULL COMMENT '审批完成时间',
                                                 `finish_user_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审批人',
                                                 `next_activity_id` bigint(20) DEFAULT NULL COMMENT '下一步审批流id',
                                                 `create_time` datetime NOT NULL,
                                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                 PRIMARY KEY (`id`),
                                                 KEY `idx_info_id` (`base_info_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审批流程_流转或签';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `approval_base_info`
--

DROP TABLE IF EXISTS `approval_base_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `approval_base_info` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                      `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
                                      `poi_id` bigint(20) DEFAULT NULL COMMENT '门店id',
                                      `employee_id` bigint(20) NOT NULL COMMENT '提交者id',
                                      `employee_name` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '提交者姓名',
                                      `relation_event_id` bigint(20) NOT NULL COMMENT '关联事件id，如考勤统计id',
                                      `event_domain` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '事件领域，如用工管理',
                                      `event_scene` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '事件场景类型，如考勤打卡异常申诉',
                                      `apply_text_content` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '申诉描述文本',
                                      `apply_json_content` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '申诉描述补充信息',
                                      `approval_channel_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '审批流类型，如"self_turn_anyone""kuaida"',
                                      `current_activity_id` bigint(20) DEFAULT NULL COMMENT '当前流程id',
                                      `current_approval_candidates` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '当前审批候选人列表',
                                      `approval_status` int(11) NOT NULL DEFAULT '1' COMMENT '审批状态 1待审批 2通过 4驳回 8已撤回',
                                      `approval_status_text` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审批状态说明，如描述驳回理由',
                                      `approval_finish_time` datetime DEFAULT NULL COMMENT '审批完结时间',
                                      `create_time` datetime NOT NULL,
                                      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                      PRIMARY KEY (`id`),
                                      KEY `idx_tenant` (`tenant_id`),
                                      KEY `idx_event_all` (`event_domain`,`event_scene`,`relation_event_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审批基础信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `attendance_checkin_record`
--

DROP TABLE IF EXISTS `attendance_checkin_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `attendance_checkin_record` (
                                             `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                             `employee_id` bigint(20) NOT NULL COMMENT '员工id',
                                             `longitude` decimal(9,6) DEFAULT NULL COMMENT '精度',
                                             `latitude` decimal(9,6) DEFAULT NULL COMMENT '纬度',
                                             `ip_address` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'ip地址',
                                             `checkin_day` date NOT NULL COMMENT '打卡日期',
                                             `checkin_type` int(11) NOT NULL DEFAULT '1' COMMENT '打卡类型 1GPS，2Wi-Fi_MAC，4Wi-Fi_iP，8考勤机，16外勤打卡',
                                             `checkin_json_content` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '打卡补充信息（如外勤地址、拍照上传、打卡说明等）',
                                             `checkin_time` datetime NOT NULL COMMENT '打卡时间',
                                             `create_time` datetime NOT NULL COMMENT '落库时间',
                                             PRIMARY KEY (`id`),
                                             KEY `idx_empid_day` (`employee_id`,`checkin_day`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考勤记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `attendance_result_statistics`
--

DROP TABLE IF EXISTS `attendance_result_statistics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `attendance_result_statistics` (
                                                `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                                `employee_id` bigint(20) NOT NULL COMMENT '员工id',
                                                `employee_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '员工姓名',
                                                `start_work_checkin_record` bigint(20) NOT NULL DEFAULT '0' COMMENT '上班打卡记录id',
                                                `start_work_checkin_time` datetime DEFAULT NULL COMMENT '上班打卡时间，合并日期时间存储',
                                                `end_work_checkin_record` bigint(20) NOT NULL DEFAULT '0' COMMENT '下班打卡记录id',
                                                `end_work_checkin_time` datetime DEFAULT NULL COMMENT '下班打卡时间，合并日期时间存储',
                                                `stat_day` date NOT NULL COMMENT '统计考勤日期',
                                                `work_shift_id` bigint(20) NOT NULL COMMENT '对应排班记录id',
                                                `work_shift_snapshot` varchar(1024) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '排班信息快照',
                                                `start_work_attendance_status` int(11) NOT NULL COMMENT '上班考勤状态 1正常，2异常，4审批中，8审批通过，16审批驳回',
                                                `end_work_attendance_status` int(11) NOT NULL COMMENT '下班考勤状态 1正常，2异常，4审批中，8审批通过，16审批驳回',
                                                `start_work_attendance_exceptions` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上班考勤异常原因列表，逗号分割',
                                                `end_work_attendance_exceptions` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '下班考勤异常原因列表，逗号分割',
                                                `start_work_appeal_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '上班考勤申诉id',
                                                `end_work_appeal_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '下班考勤申诉id',
                                                `create_time` datetime NOT NULL,
                                                `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                PRIMARY KEY (`id`),
                                                UNIQUE KEY `uidx_empid_day_shift` (`employee_id`,`stat_day`,`work_shift_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考勤统计';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `base_indicator`
--

DROP TABLE IF EXISTS `base_indicator`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `base_indicator` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                  `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '基础指标名称',
                                  `source` tinyint(4) NOT NULL DEFAULT '0' COMMENT '来源，1-数仓 2-人工 3-组合',
                                  `identifier` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '标识符',
                                  `number_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '数字类型，1-百分数 2-小数 3-整数',
                                  `formula` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公式',
                                  `calculate_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '计算类型，0-无需计算 1-平均 2-求和 3-九分位 4-明细',
                                  `source_table` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '数据源表',
                                  `source_col` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '数据源列',
                                  `decimal_places` tinyint(4) NOT NULL DEFAULT '0' COMMENT '保留小数位数',
                                  `interval` json NOT NULL COMMENT '取值区间',
                                  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                                  PRIMARY KEY (`id`),
                                  UNIQUE KEY `identifier` (`identifier`) COMMENT '唯一标识符',
                                  UNIQUE KEY `name` (`name`),
                                  KEY `idx_source` (`source`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='基础指标表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `calculate_indicator_group`
--

DROP TABLE IF EXISTS `calculate_indicator_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `calculate_indicator_group` (
                                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                             `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '计算指标组名称',
                                             `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                             `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                             `is_deleted` bigint(20) NOT NULL DEFAULT '0' COMMENT '删除标识， 0-未删，非0的时间戳为删除状态',
                                             PRIMARY KEY (`id`),
                                             UNIQUE KEY `name_is_deleted` (`name`,`is_deleted`),
                                             KEY `idx_is_deleted_update_time` (`is_deleted`,`update_time`)
) ENGINE=InnoDB AUTO_INCREMENT=847 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='计算指标组表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `calculate_indicator_item`
--

DROP TABLE IF EXISTS `calculate_indicator_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `calculate_indicator_item` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                            `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '计算指标名称',
                                            `define_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '定义id',
                                            `group_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '计算指标组id',
                                            `expression_tree` json NOT NULL DEFAULT 'null' COMMENT '表达式树',
                                            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                            `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                                            PRIMARY KEY (`id`),
                                            KEY `idx_group_id_is_deleted` (`group_id`,`is_deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=1385 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='计算指标明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `indicator_category`
--

DROP TABLE IF EXISTS `indicator_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `indicator_category` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                      `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
                                      `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标类别表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `indicator_condition_define`
--

DROP TABLE IF EXISTS `indicator_condition_define`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `indicator_condition_define` (
                                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                              `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '考核条件名称',
                                              `identifier` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标识符',
                                              `formula` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公式',
                                              `interval_value` json NOT NULL,
                                              `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                              `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                                              `number_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '数字类型，1-百分数 2-小数 3-整数',
                                              PRIMARY KEY (`id`),
                                              UNIQUE KEY `identifier` (`identifier`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考核条件表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `indicator_define`
--

DROP TABLE IF EXISTS `indicator_define`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `indicator_define` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                    `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '指标名称',
                                    `identifier` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标识符',
                                    `formula` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公式',
                                    `type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '类型，1-计算指标 2-人工薪资方案项 3-系统薪资方案项',
                                    `collect_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '聚合类型，0-无，1-阶梯，2-累加',
                                    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `description` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '指标描述',
                                    `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                                    `condition_define_identifiers` json NOT NULL DEFAULT 'null' COMMENT '考核指标关联标识列表',
                                    `category_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '类别id',
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `identifier` (`identifier`) COMMENT '唯一标识符',
                                    UNIQUE KEY `name` (`name`) COMMENT '唯一指标名称',
                                    KEY `idx_type` (`type`)
) ENGINE=InnoDB AUTO_INCREMENT=70 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='基础指标表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `indicator_variable_define`
--

DROP TABLE IF EXISTS `indicator_variable_define`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `indicator_variable_define` (
                                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                             `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '变量名称',
                                             `identifier` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标识符',
                                             `interval_value` json NOT NULL,
                                             `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                             `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                             `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                                             `number_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '数字类型，1-百分数 2-小数 3-整数',
                                             PRIMARY KEY (`id`),
                                             UNIQUE KEY `identifier` (`identifier`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标变量表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `labor_candidate`
--

DROP TABLE IF EXISTS `labor_candidate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `labor_candidate` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                   `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
                                   `name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '姓名',
                                   `phone_token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号token',
                                   `id_card_token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '身份证token',
                                   `work_type_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '工作类型id',
                                   `audit_status` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '审核状态，0-审核中，10-通过，99-不通过',
                                   `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
                                   PRIMARY KEY (`id`),
                                   UNIQUE KEY `unique_phone_idx` (`tenant_id`,`phone_token`,`is_deleted`) COMMENT '电话token唯一索引',
                                   UNIQUE KEY `unique_id_idx` (`tenant_id`,`id_card_token`,`is_deleted`) COMMENT '身份证token唯一索引',
                                   KEY `name_idx` (`tenant_id`,`name`) USING BTREE COMMENT '姓名索引',
                                   KEY `time_idx` (`tenant_id`,`create_time`) USING BTREE COMMENT '创建时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COMMENT='用工候选人表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `labor_info`
--

DROP TABLE IF EXISTS `labor_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `labor_info` (
                              `id` bigint(20) NOT NULL AUTO_INCREMENT,
                              `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
                              `employee_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '员工id',
                              `hire_service_provider_id` bigint(20) DEFAULT NULL COMMENT '招聘服务商id',
                              `rely_service_provider_id` bigint(20) DEFAULT NULL COMMENT '挂靠服务商id',
                              `hire_channel` tinyint(4) DEFAULT NULL COMMENT '招聘渠道，1-服务商推荐、2-内推、3-门店自招',
                              `job_nature` tinyint(4) DEFAULT NULL COMMENT '岗位性质，1 - 兼职，2-全职',
                              `resign_time` timestamp NULL DEFAULT NULL COMMENT '离职时间',
                              `id_card_token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '身份证token',
                              `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                              PRIMARY KEY (`id`),
                              UNIQUE KEY `idx_id_card_token` (`id_card_token`,`is_deleted`) COMMENT '唯一身份证',
                              UNIQUE KEY `idx_employee_id` (`employee_id`,`is_deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COMMENT='劳动力信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `labor_service_provider`
--

DROP TABLE IF EXISTS `labor_service_provider`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `labor_service_provider` (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                          `identifier` varchar(32) NOT NULL COMMENT '服务商标识',
                                          `type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '类型，1-招聘服务商 2-挂靠服务商, 3-招聘/挂靠服务商',
                                          `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '服务商名称',
                                          `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                          `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                                          PRIMARY KEY (`id`),
                                          UNIQUE KEY `idx_identifier` (`identifier`,`is_deleted`) COMMENT '唯一标识符'
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='用工服务商信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `labor_work_type`
--

DROP TABLE IF EXISTS `labor_work_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `labor_work_type` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                   `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
                                   `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工作类型名称',
                                   `desc` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '描述',
                                   `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='工作类型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `operate_job`
--

DROP TABLE IF EXISTS `operate_job`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `operate_job` (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                               `type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '类型，1-账号导入 2-人工维护信息模版 3-人工维护模版信息导入 4-算薪结果生成 5-算薪结果导入',
                               `task_no` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '任务号',
                               `job_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '任务状态，1-进行中 10-完成 99-失败',
                               `active_status` bigint(20) NOT NULL DEFAULT '0' COMMENT '激活状态，一个taskNo只能有一个激活任务。0-激活中，非0的时间戳为非激活状态',
                               `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                               `operator_account_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '操作人accountId',
                               `operator_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作人姓名',
                               PRIMARY KEY (`id`),
                               UNIQUE KEY `task_no_active` (`task_no`,`active_status`) COMMENT '唯一激活任务'
) ENGINE=InnoDB AUTO_INCREMENT=4434 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作任务表，用于控制并发操作，记录异步操作状态';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `salary_category`
--

DROP TABLE IF EXISTS `salary_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `salary_category` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                   `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
                                   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='薪资类别';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `salary_plan_group`
--

DROP TABLE IF EXISTS `salary_plan_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `salary_plan_group` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                     `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '薪资方案名称',
                                     `calculate_group_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '关联的计算指标组id',
                                     `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     `is_deleted` bigint(20) NOT NULL DEFAULT '0' COMMENT '删除标识， 0-未删，非0的时间戳为删除状态',
                                     PRIMARY KEY (`id`),
                                     UNIQUE KEY `name_is_deleted` (`name`,`is_deleted`),
                                     KEY `idx_calculate_group_id_is_deleted_update_time` (`calculate_group_id`,`is_deleted`,`update_time`),
                                     KEY `idx_is_deleted_update_time` (`update_time`,`is_deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=539 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='薪资计划组表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `salary_plan_item`
--

DROP TABLE IF EXISTS `salary_plan_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `salary_plan_item` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                    `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '薪资项名称',
                                    `define_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '定义id',
                                    `group_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '计算指标组id',
                                    `expression_tree` json NOT NULL DEFAULT 'null' COMMENT '表达式树',
                                    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                                    PRIMARY KEY (`id`),
                                    KEY `idx_group_id_is_deleted` (`group_id`,`is_deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=1335 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='薪资计划明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `salary_result_item`
--

DROP TABLE IF EXISTS `salary_result_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `salary_result_item` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                      `task_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '算薪任务id',
                                      `task_employee_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '算薪人员id',
                                      `result_detail` json DEFAULT NULL COMMENT '算薪明细',
                                      `total_amout` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '总计，单位：元',
                                      `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_task_id_is_deleted` (`task_id`,`is_deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=1988 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='算薪任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `salary_task`
--

DROP TABLE IF EXISTS `salary_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `salary_task` (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                               `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '算薪任务名称',
                               `salary_plan_group_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '薪资组id',
                               `start_time` datetime(3) NOT NULL DEFAULT '1970-01-01 00:00:00.000' COMMENT '开始算薪时间',
                               `end_time` datetime(3) NOT NULL DEFAULT '1970-01-01 00:00:00.000' COMMENT '结束算薪时间',
                               `operator_account_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '操作人accountId',
                               `operator_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作人姓名',
                               `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '任务状态，1-进行中 10-审核中 20-审核通过 90-审核驳回 99-失效',
                               `last_operate_time` datetime(3) NOT NULL DEFAULT '1970-01-01 00:00:00.000' COMMENT '最后操作时间',
                               `account_associate_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '账号关联方式',
                               `role_info` json DEFAULT NULL COMMENT '角色信息，json格式。只有当按角色导入时才有用',
                               `dx_apply_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '大象审核单id',
                               `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                               `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '版本控制',
                               PRIMARY KEY (`id`),
                               UNIQUE KEY `idx_name_is_deleted` (`name`,`is_deleted`),
                               KEY `idx_is_deleted_last_operate_time` (`is_deleted`,`last_operate_time`),
                               KEY `idx_status_is_deleted_last_operate_time` (`status`,`is_deleted`,`last_operate_time`),
                               KEY `idx_salary_plan_group_id_is_deleted_last_operate_time` (`salary_plan_group_id`,`is_deleted`,`last_operate_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1271 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='算薪任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `salary_task_employee`
--

DROP TABLE IF EXISTS `salary_task_employee`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `salary_task_employee` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `salary_task_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '算薪任务id',
                                        `account_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '账号id',
                                        `emplyee_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '员工id',
                                        `account_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '账号名称',
                                        `emplyee_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '员工名称',
                                        `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删>除',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_is_deleted_update_time` (`update_time`,`is_deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=10841 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='算薪任务员工表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `salary_task_store`
--

DROP TABLE IF EXISTS `salary_task_store`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `salary_task_store` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                     `salary_task_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '算薪任务id',
                                     `city_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '城市id',
                                     `city_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '城市名称',
                                     `store_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '门店id',
                                     `store_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '门店名称',
                                     `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删 1-已删除',
                                     PRIMARY KEY (`id`),
                                     KEY `idx_salary_task_store_salary_task_id_store_id` (`salary_task_id`,`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10893 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='算薪任务门店表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `schedule_detail`
--

DROP TABLE IF EXISTS `schedule_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `schedule_detail` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                   `rule_id` bigint(20) NOT NULL,
                                   `employee_id` bigint(20) NOT NULL,
                                   `employee_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                   `schedule_date` date NOT NULL COMMENT '排班日期',
                                   `setting_mode` int(11) NOT NULL DEFAULT '1' COMMENT '排班设置方式，1管理员排班 2员工自选',
                                   `shift_id` bigint(20) NOT NULL COMMENT '班次id',
                                   `shift_description` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '班次描述',
                                   `create_time` datetime NOT NULL,
                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                   `is_deleted` int(11) NOT NULL DEFAULT '0',
                                   PRIMARY KEY (`id`),
                                   UNIQUE KEY `uidx_emp_schedule_shift` (`employee_id`,`schedule_date`,`shift_id`),
                                   KEY `idx_rule_id` (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排班细则';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `schedule_rule`
--

DROP TABLE IF EXISTS `schedule_rule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `schedule_rule` (
                                 `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                 `tenant_id` bigint(11) NOT NULL COMMENT '租户Id',
                                 `poi_id` bigint(11) NOT NULL COMMENT '门店id',
                                 `name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '规则名称',
                                 `rule_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'turn_shift' COMMENT '规则类型，如固定班制、排班制、自由班制',
                                 `operator_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作者ID（百川账号）',
                                 `operator_name` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作者名字',
                                 `create_time` datetime NOT NULL,
                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                 `is_deleted` int(11) NOT NULL DEFAULT '0',
                                 `is_delete` int(11) NOT NULL DEFAULT '0' COMMENT '是否删除,1=yes',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排班规则';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `schedule_rule_employee`
--

DROP TABLE IF EXISTS `schedule_rule_employee`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `schedule_rule_employee` (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                          `rule_id` bigint(20) NOT NULL,
                                          `employee_id` bigint(20) NOT NULL COMMENT '员工id',
                                          `employee_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '员工名字',
                                          `is_delete` int(11) NOT NULL DEFAULT '0',
                                          `create_time` datetime NOT NULL,
                                          `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                          `is_deleted` int(11) NOT NULL DEFAULT '0',
                                          PRIMARY KEY (`id`),
                                          KEY `idx_rule_id` (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排班规则关联员工';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `schedule_rule_location`
--

DROP TABLE IF EXISTS `schedule_rule_location`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `schedule_rule_location` (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                          `rule_id` bigint(20) NOT NULL,
                                          `poi_id` bigint(20) DEFAULT NULL COMMENT '门店id',
                                          `location_description` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '位置描述，如门店名',
                                          `longitude` decimal(9,6) NOT NULL COMMENT '经度',
                                          `latitude` decimal(9,6) NOT NULL COMMENT '维度',
                                          `allow_checkin_range` int(11) NOT NULL COMMENT '允许打卡范围，米',
                                          `create_time` datetime NOT NULL,
                                          `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                          `is_deleted` int(11) NOT NULL DEFAULT '0',
                                          PRIMARY KEY (`id`),
                                          KEY `idx_rule_id` (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排班规则位置信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `schedule_shift`
--

DROP TABLE IF EXISTS `schedule_shift`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `schedule_shift` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                  `rule_id` bigint(20) NOT NULL COMMENT '考勤规则id',
                                  `name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
                                  `start_work_time` time NOT NULL COMMENT '上班时间',
                                  `end_work_time` time NOT NULL COMMENT '下班时间',
                                  `start_work_checkin_duration_begin` time NOT NULL COMMENT '上班打卡时间段开始时间',
                                  `start_work_checkin_duration_end` time NOT NULL COMMENT '上班打卡时间段结束时间',
                                  `end_work_checkin_duration_begin` time NOT NULL COMMENT '下班打卡时间段开始时间',
                                  `end_work_checkin_duration_end` time NOT NULL COMMENT '下班打卡时间段结束时间',
                                  `is_delete` int(11) NOT NULL DEFAULT '0',
                                  `color_code` varchar(8) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '颜色RGB16进制标识码，前端展示用',
                                  `operator_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                  `operator_name` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                  `create_time` datetime NOT NULL,
                                  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                  `is_deleted` int(11) NOT NULL DEFAULT '0',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排班班次';
/*!40101 SET character_set_client = @saved_cs_client */;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2022-10-14 15:06:27
