<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.sdms.dao.mapper.AssessDeliveryTimeConfigMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.sdms.dao.model.AssessDeliveryTimeConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="belong_store_config_id" jdbcType="BIGINT" property="belongStoreConfigId" />
    <result column="config_json" jdbcType="CHAR" property="configJson" />
    <result column="calc_unit" jdbcType="INTEGER" property="calcUnit" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, belong_store_config_id, config_json, calc_unit, create_time, update_time, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.AssessDeliveryTimeConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from assess_delivery_time_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from assess_delivery_time_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from assess_delivery_time_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.AssessDeliveryTimeConfigExample">
    delete from assess_delivery_time_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.AssessDeliveryTimeConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into assess_delivery_time_config (belong_store_config_id, config_json, calc_unit, 
      create_time, update_time, is_deleted
      )
    values (#{belongStoreConfigId,jdbcType=BIGINT}, #{configJson,jdbcType=CHAR}, #{calcUnit,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.AssessDeliveryTimeConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into assess_delivery_time_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="belongStoreConfigId != null">
        belong_store_config_id,
      </if>
      <if test="configJson != null">
        config_json,
      </if>
      <if test="calcUnit != null">
        calc_unit,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="belongStoreConfigId != null">
        #{belongStoreConfigId,jdbcType=BIGINT},
      </if>
      <if test="configJson != null">
        #{configJson,jdbcType=CHAR},
      </if>
      <if test="calcUnit != null">
        #{calcUnit,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.AssessDeliveryTimeConfigExample" resultType="java.lang.Long">
    select count(*) from assess_delivery_time_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update assess_delivery_time_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.belongStoreConfigId != null">
        belong_store_config_id = #{record.belongStoreConfigId,jdbcType=BIGINT},
      </if>
      <if test="record.configJson != null">
        config_json = #{record.configJson,jdbcType=CHAR},
      </if>
      <if test="record.calcUnit != null">
        calc_unit = #{record.calcUnit,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update assess_delivery_time_config
    set id = #{record.id,jdbcType=BIGINT},
      belong_store_config_id = #{record.belongStoreConfigId,jdbcType=BIGINT},
      config_json = #{record.configJson,jdbcType=CHAR},
      calc_unit = #{record.calcUnit,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.AssessDeliveryTimeConfig">
    update assess_delivery_time_config
    <set>
      <if test="belongStoreConfigId != null">
        belong_store_config_id = #{belongStoreConfigId,jdbcType=BIGINT},
      </if>
      <if test="configJson != null">
        config_json = #{configJson,jdbcType=CHAR},
      </if>
      <if test="calcUnit != null">
        calc_unit = #{calcUnit,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.AssessDeliveryTimeConfig">
    update assess_delivery_time_config
    set belong_store_config_id = #{belongStoreConfigId,jdbcType=BIGINT},
      config_json = #{configJson,jdbcType=CHAR},
      calc_unit = #{calcUnit,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into assess_delivery_time_config
    (belong_store_config_id, config_json, calc_unit, create_time, update_time, is_deleted
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.belongStoreConfigId,jdbcType=BIGINT}, #{item.configJson,jdbcType=CHAR}, #{item.calcUnit,jdbcType=INTEGER}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=TINYINT}
        )
    </foreach>
  </insert>
</mapper>