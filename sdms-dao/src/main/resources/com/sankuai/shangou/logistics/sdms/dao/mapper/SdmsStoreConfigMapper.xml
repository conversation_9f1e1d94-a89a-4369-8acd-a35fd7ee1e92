<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.sdms.dao.mapper.SdmsStoreConfigMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.sdms.dao.model.SdmsStoreConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_operation_mode" jdbcType="INTEGER" property="storeOperationMode" />
    <result column="city_id" jdbcType="BIGINT" property="cityId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="valid_time_start" jdbcType="TIMESTAMP" property="validTimeStart" />
    <result column="valid_time_end" jdbcType="TIMESTAMP" property="validTimeEnd" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, store_operation_mode, city_id, store_id, valid_time_start, valid_time_end, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.SdmsStoreConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sdms_store_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sdms_store_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sdms_store_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.SdmsStoreConfigExample">
    delete from sdms_store_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.SdmsStoreConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sdms_store_config (tenant_id, store_operation_mode, city_id, 
      store_id, valid_time_start, valid_time_end, 
      is_deleted)
    values (#{tenantId,jdbcType=BIGINT}, #{storeOperationMode,jdbcType=INTEGER}, #{cityId,jdbcType=BIGINT}, 
      #{storeId,jdbcType=BIGINT}, #{validTimeStart,jdbcType=TIMESTAMP}, #{validTimeEnd,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.SdmsStoreConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sdms_store_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeOperationMode != null">
        store_operation_mode,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="validTimeStart != null">
        valid_time_start,
      </if>
      <if test="validTimeEnd != null">
        valid_time_end,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeOperationMode != null">
        #{storeOperationMode,jdbcType=INTEGER},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="validTimeStart != null">
        #{validTimeStart,jdbcType=TIMESTAMP},
      </if>
      <if test="validTimeEnd != null">
        #{validTimeEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.SdmsStoreConfigExample" resultType="java.lang.Long">
    select count(*) from sdms_store_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sdms_store_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeOperationMode != null">
        store_operation_mode = #{record.storeOperationMode,jdbcType=INTEGER},
      </if>
      <if test="record.cityId != null">
        city_id = #{record.cityId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.validTimeStart != null">
        valid_time_start = #{record.validTimeStart,jdbcType=TIMESTAMP},
      </if>
      <if test="record.validTimeEnd != null">
        valid_time_end = #{record.validTimeEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sdms_store_config
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      store_operation_mode = #{record.storeOperationMode,jdbcType=INTEGER},
      city_id = #{record.cityId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      valid_time_start = #{record.validTimeStart,jdbcType=TIMESTAMP},
      valid_time_end = #{record.validTimeEnd,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.SdmsStoreConfig">
    update sdms_store_config
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeOperationMode != null">
        store_operation_mode = #{storeOperationMode,jdbcType=INTEGER},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="validTimeStart != null">
        valid_time_start = #{validTimeStart,jdbcType=TIMESTAMP},
      </if>
      <if test="validTimeEnd != null">
        valid_time_end = #{validTimeEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.SdmsStoreConfig">
    update sdms_store_config
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      store_operation_mode = #{storeOperationMode,jdbcType=INTEGER},
      city_id = #{cityId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      valid_time_start = #{validTimeStart,jdbcType=TIMESTAMP},
      valid_time_end = #{validTimeEnd,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into sdms_store_config
    (tenant_id, store_operation_mode, city_id, store_id, valid_time_start, valid_time_end, 
      is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.tenantId,jdbcType=BIGINT}, #{item.storeOperationMode,jdbcType=INTEGER}, #{item.cityId,jdbcType=BIGINT}, 
        #{item.storeId,jdbcType=BIGINT}, #{item.validTimeStart,jdbcType=TIMESTAMP}, #{item.validTimeEnd,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=TINYINT})
    </foreach>
  </insert>
</mapper>