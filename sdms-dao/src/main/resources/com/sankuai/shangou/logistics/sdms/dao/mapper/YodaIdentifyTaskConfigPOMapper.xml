<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.sdms.dao.mapper.YodaIdentifyTaskConfigPOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskConfigPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_operation_mode" jdbcType="INTEGER" property="storeOperationMode" />
    <result column="city_list" jdbcType="CHAR" property="cityList" />
    <result column="store_list" jdbcType="CHAR" property="storeList" />
    <result column="collect_mode" jdbcType="INTEGER" property="collectMode" />
    <result column="collect_content_type_list" jdbcType="CHAR" property="collectContentTypeList" />
    <result column="collect_period" jdbcType="INTEGER" property="collectPeriod" />
    <result column="period_max_collect_times" jdbcType="INTEGER" property="periodMaxCollectTimes" />
    <result column="task_valid_period" jdbcType="INTEGER" property="taskValidPeriod" />
    <result column="interval" jdbcType="INTEGER" property="interval" />
    <result column="period_begin_time" jdbcType="VARCHAR" property="periodBeginTime" />
    <result column="period_end_time" jdbcType="VARCHAR" property="periodEndTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="punish_config" jdbcType="CHAR" property="punishConfig" />
    <result column="is_valid" jdbcType="INTEGER" property="isValid" />
    <result column="position_list" jdbcType="CHAR" property="positionList" />
    <result column="task_near_expiration" jdbcType="INTEGER" property="taskNearExpiration" />
    <result column="no_control" jdbcType="CHAR" property="noControl" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, store_operation_mode, city_list, store_list, collect_mode, collect_content_type_list, 
    collect_period, period_max_collect_times, task_valid_period, `interval`, period_begin_time, 
    period_end_time, create_time, update_time, punish_config, is_valid, position_list, task_near_expiration, no_control
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskConfigPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from yoda_identify_task_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from yoda_identify_task_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from yoda_identify_task_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskConfigPOExample">
    delete from yoda_identify_task_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskConfigPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into yoda_identify_task_config (tenant_id, store_operation_mode, city_list, 
      store_list, collect_mode, collect_content_type_list, 
      collect_period, period_max_collect_times, task_valid_period, 
      `interval`, period_begin_time, period_end_time, 
      create_time, update_time, punish_config, 
      is_valid, position_list, task_near_expiration, no_control)
    values (#{tenantId,jdbcType=BIGINT}, #{storeOperationMode,jdbcType=INTEGER}, #{cityList,jdbcType=CHAR}, 
      #{storeList,jdbcType=CHAR}, #{collectMode,jdbcType=INTEGER}, #{collectContentTypeList,jdbcType=CHAR}, 
      #{collectPeriod,jdbcType=INTEGER}, #{periodMaxCollectTimes,jdbcType=INTEGER}, #{taskValidPeriod,jdbcType=INTEGER}, 
      #{interval,jdbcType=INTEGER}, #{periodBeginTime,jdbcType=VARCHAR}, #{periodEndTime,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{punishConfig,jdbcType=CHAR}, 
      #{isValid,jdbcType=INTEGER}, #{positionList,jdbcType=CHAR}, #{taskNearExpiration,jdbcType=INTEGER}, #{noControl,jdbcType=CHAR} )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskConfigPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into yoda_identify_task_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeOperationMode != null">
        store_operation_mode,
      </if>
      <if test="cityList != null">
        city_list,
      </if>
      <if test="storeList != null">
        store_list,
      </if>
      <if test="collectMode != null">
        collect_mode,
      </if>
      <if test="collectContentTypeList != null">
        collect_content_type_list,
      </if>
      <if test="collectPeriod != null">
        collect_period,
      </if>
      <if test="periodMaxCollectTimes != null">
        period_max_collect_times,
      </if>
      <if test="taskValidPeriod != null">
        task_valid_period,
      </if>
      <if test="interval != null">
        `interval`,
      </if>
      <if test="periodBeginTime != null">
        period_begin_time,
      </if>
      <if test="periodEndTime != null">
        period_end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="punishConfig != null">
        punish_config,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="positionList != null">
        position_list,
      </if>
      <if test="taskNearExpiration != null">
        task_near_expiration,
      </if>
      <if test="noControl != null">
        no_control,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeOperationMode != null">
        #{storeOperationMode,jdbcType=INTEGER},
      </if>
      <if test="cityList != null">
        #{cityList,jdbcType=CHAR},
      </if>
      <if test="storeList != null">
        #{storeList,jdbcType=CHAR},
      </if>
      <if test="collectMode != null">
        #{collectMode,jdbcType=INTEGER},
      </if>
      <if test="collectContentTypeList != null">
        #{collectContentTypeList,jdbcType=CHAR},
      </if>
      <if test="collectPeriod != null">
        #{collectPeriod,jdbcType=INTEGER},
      </if>
      <if test="periodMaxCollectTimes != null">
        #{periodMaxCollectTimes,jdbcType=INTEGER},
      </if>
      <if test="taskValidPeriod != null">
        #{taskValidPeriod,jdbcType=INTEGER},
      </if>
      <if test="interval != null">
        #{interval,jdbcType=INTEGER},
      </if>
      <if test="periodBeginTime != null">
        #{periodBeginTime,jdbcType=VARCHAR},
      </if>
      <if test="periodEndTime != null">
        #{periodEndTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="punishConfig != null">
        #{punishConfig,jdbcType=CHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskConfigPOExample" resultType="java.lang.Long">
    select count(*) from yoda_identify_task_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update yoda_identify_task_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeOperationMode != null">
        store_operation_mode = #{record.storeOperationMode,jdbcType=INTEGER},
      </if>
      <if test="record.cityList != null">
        city_list = #{record.cityList,jdbcType=CHAR},
      </if>
      <if test="record.storeList != null">
        store_list = #{record.storeList,jdbcType=CHAR},
      </if>
      <if test="record.collectMode != null">
        collect_mode = #{record.collectMode,jdbcType=INTEGER},
      </if>
      <if test="record.collectContentTypeList != null">
        collect_content_type_list = #{record.collectContentTypeList,jdbcType=CHAR},
      </if>
      <if test="record.collectPeriod != null">
        collect_period = #{record.collectPeriod,jdbcType=INTEGER},
      </if>
      <if test="record.periodMaxCollectTimes != null">
        period_max_collect_times = #{record.periodMaxCollectTimes,jdbcType=INTEGER},
      </if>
      <if test="record.taskValidPeriod != null">
        task_valid_period = #{record.taskValidPeriod,jdbcType=INTEGER},
      </if>
      <if test="record.interval != null">
        `interval` = #{record.interval,jdbcType=INTEGER},
      </if>
      <if test="record.periodBeginTime != null">
        period_begin_time = #{record.periodBeginTime,jdbcType=VARCHAR},
      </if>
      <if test="record.periodEndTime != null">
        period_end_time = #{record.periodEndTime,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.punishConfig != null">
        punish_config = #{record.punishConfig,jdbcType=CHAR},
      </if>
      <if test="record.isValid != null">
        is_valid = #{record.isValid,jdbcType=INTEGER},
      </if>
      <if test="record.positionList != null">
        position_list = #{record.positionList,jdbcType=CHAR},
      </if>
      <if test="record.taskNearExpiration != null">
        task_near_expiration = #{record.taskNearExpiration,jdbcType=INTEGER},
      </if>
      <if test="record.noControl != null">
        no_control = #{record.noControl,jdbcType=CHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update yoda_identify_task_config
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      store_operation_mode = #{record.storeOperationMode,jdbcType=INTEGER},
      city_list = #{record.cityList,jdbcType=CHAR},
      store_list = #{record.storeList,jdbcType=CHAR},
      collect_mode = #{record.collectMode,jdbcType=INTEGER},
      collect_content_type_list = #{record.collectContentTypeList,jdbcType=CHAR},
      collect_period = #{record.collectPeriod,jdbcType=INTEGER},
      period_max_collect_times = #{record.periodMaxCollectTimes,jdbcType=INTEGER},
      task_valid_period = #{record.taskValidPeriod,jdbcType=INTEGER},
      `interval` = #{record.interval,jdbcType=INTEGER},
      period_begin_time = #{record.periodBeginTime,jdbcType=VARCHAR},
      period_end_time = #{record.periodEndTime,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      punish_config = #{record.punishConfig,jdbcType=CHAR},
      is_valid = #{record.isValid,jdbcType=INTEGER},
      position_list = #{record.positionList,jdbcType=CHAR},
      task_near_expiration = #{record.taskNearExpiration,jdbcType=INTEGER},
      no_control = #{record.noControl,jdbcType=CHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskConfigPO">
    update yoda_identify_task_config
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeOperationMode != null">
        store_operation_mode = #{storeOperationMode,jdbcType=INTEGER},
      </if>
      <if test="cityList != null">
        city_list = #{cityList,jdbcType=CHAR},
      </if>
      <if test="storeList != null">
        store_list = #{storeList,jdbcType=CHAR},
      </if>
      <if test="collectMode != null">
        collect_mode = #{collectMode,jdbcType=INTEGER},
      </if>
      <if test="collectContentTypeList != null">
        collect_content_type_list = #{collectContentTypeList,jdbcType=CHAR},
      </if>
      <if test="collectPeriod != null">
        collect_period = #{collectPeriod,jdbcType=INTEGER},
      </if>
      <if test="periodMaxCollectTimes != null">
        period_max_collect_times = #{periodMaxCollectTimes,jdbcType=INTEGER},
      </if>
      <if test="taskValidPeriod != null">
        task_valid_period = #{taskValidPeriod,jdbcType=INTEGER},
      </if>
      <if test="interval != null">
        `interval` = #{interval,jdbcType=INTEGER},
      </if>
      <if test="periodBeginTime != null">
        period_begin_time = #{periodBeginTime,jdbcType=VARCHAR},
      </if>
      <if test="periodEndTime != null">
        period_end_time = #{periodEndTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="punishConfig != null">
        punish_config = #{punishConfig,jdbcType=CHAR},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=INTEGER},
      </if>
      <if test="positionList != null">
        position_list = #{positionList,jdbcType=CHAR},
      </if>
      <if test="taskNearExpiration != null">
        task_near_expiration = #{taskNearExpiration,jdbcType=INTEGER},
      </if>
      <if test="noControl != null">
        no_control = #{noControl,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskConfigPO">
    update yoda_identify_task_config
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      store_operation_mode = #{storeOperationMode,jdbcType=INTEGER},
      city_list = #{cityList,jdbcType=CHAR},
      store_list = #{storeList,jdbcType=CHAR},
      collect_mode = #{collectMode,jdbcType=INTEGER},
      collect_content_type_list = #{collectContentTypeList,jdbcType=CHAR},
      collect_period = #{collectPeriod,jdbcType=INTEGER},
      period_max_collect_times = #{periodMaxCollectTimes,jdbcType=INTEGER},
      task_valid_period = #{taskValidPeriod,jdbcType=INTEGER},
      `interval` = #{interval,jdbcType=INTEGER},
      period_begin_time = #{periodBeginTime,jdbcType=VARCHAR},
      period_end_time = #{periodEndTime,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      punish_config = #{punishConfig,jdbcType=CHAR},
      is_valid = #{isValid,jdbcType=INTEGER},
      position_list = #{positionList,jdbcType=CHAR},
      task_near_expiration = #{taskNearExpiration,jdbcType=INTEGER},
      no_control = #{noControl,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into yoda_identify_task_config
    (tenant_id, store_operation_mode, city_list, store_list, collect_mode, collect_content_type_list, 
      collect_period, period_max_collect_times, task_valid_period, `interval`, period_begin_time, 
      period_end_time, create_time, update_time, punish_config, is_valid, position_list, task_near_expiration, no_control)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.tenantId,jdbcType=BIGINT}, #{item.storeOperationMode,jdbcType=INTEGER}, #{item.cityList,jdbcType=CHAR}, 
        #{item.storeList,jdbcType=CHAR}, #{item.collectMode,jdbcType=INTEGER}, #{item.collectContentTypeList,jdbcType=CHAR}, 
        #{item.collectPeriod,jdbcType=INTEGER}, #{item.periodMaxCollectTimes,jdbcType=INTEGER}, 
        #{item.taskValidPeriod,jdbcType=INTEGER}, #{item.interval,jdbcType=INTEGER}, #{item.periodBeginTime,jdbcType=VARCHAR}, 
        #{item.periodEndTime,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.punishConfig,jdbcType=CHAR}, #{item.isValid,jdbcType=INTEGER},
        #{item.positionList,jdbcType=CHAR}, #{item.taskNearExpiration,jdbcType=INTEGER}, #{item.noControl,jdbcType=CHAR}
        )
    </foreach>
  </insert>
</mapper>