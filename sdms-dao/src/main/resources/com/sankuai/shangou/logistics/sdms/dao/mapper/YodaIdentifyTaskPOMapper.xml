<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.sdms.dao.mapper.YodaIdentifyTaskPOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="delivery_order_id" jdbcType="BIGINT" property="deliveryOrderId" />
    <result column="channel_order_id" jdbcType="VARCHAR" property="channelOrderId" />
    <result column="order_biz_type" jdbcType="INTEGER" property="orderBizType" />
    <result column="rel_rule_id" jdbcType="BIGINT" property="relRuleId" />
    <result column="collect_content_type" jdbcType="VARCHAR" property="collectContentType" />
    <result column="collect_mode" jdbcType="INTEGER" property="collectMode" />
    <result column="rider_account_id" jdbcType="BIGINT" property="riderAccountId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="task_status" jdbcType="INTEGER" property="taskStatus" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="face_identify_result" jdbcType="INTEGER" property="faceIdentifyResult" />
    <result column="helmet_identify_result" jdbcType="INTEGER" property="helmetIdentifyResult" />
    <result column="dressing_identify_result" jdbcType="INTEGER" property="dressingIdentifyResult" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="push_task_time" jdbcType="TIMESTAMP" property="pushTaskTime" />
    <result column="result_check_pass" jdbcType="INTEGER" property="resultCheckPass" />
    <result column="callback_code" jdbcType="VARCHAR" property="callbackCode" />
    <result column="callback_msg" jdbcType="VARCHAR" property="callbackMsg" />
    <result column="task_expire_time" jdbcType="TIMESTAMP" property="taskExpireTime" />
    <result column="punish_info" jdbcType="CHAR" property="punishInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, delivery_order_id, channel_order_id, order_biz_type, rel_rule_id, collect_content_type, 
    collect_mode, rider_account_id, store_id, tenant_id, task_status, complete_time, 
    face_identify_result, helmet_identify_result, dressing_identify_result, ext_info, 
    update_time, create_time, push_task_time, result_check_pass, callback_code, callback_msg, 
    task_expire_time, punish_info
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from yoda_identify_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from yoda_identify_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from yoda_identify_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPOExample">
    delete from yoda_identify_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into yoda_identify_task (delivery_order_id, channel_order_id, order_biz_type, 
      rel_rule_id, collect_content_type, collect_mode, 
      rider_account_id, store_id, tenant_id, 
      task_status, complete_time, face_identify_result, 
      helmet_identify_result, dressing_identify_result, 
      ext_info, update_time, create_time, 
      push_task_time, result_check_pass, callback_code, 
      callback_msg, task_expire_time, punish_info
      )
    values (#{deliveryOrderId,jdbcType=BIGINT}, #{channelOrderId,jdbcType=VARCHAR}, #{orderBizType,jdbcType=INTEGER}, 
      #{relRuleId,jdbcType=BIGINT}, #{collectContentType,jdbcType=VARCHAR}, #{collectMode,jdbcType=INTEGER}, 
      #{riderAccountId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, 
      #{taskStatus,jdbcType=INTEGER}, #{completeTime,jdbcType=TIMESTAMP}, #{faceIdentifyResult,jdbcType=INTEGER}, 
      #{helmetIdentifyResult,jdbcType=INTEGER}, #{dressingIdentifyResult,jdbcType=INTEGER}, 
      #{extInfo,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{pushTaskTime,jdbcType=TIMESTAMP}, #{resultCheckPass,jdbcType=INTEGER}, #{callbackCode,jdbcType=VARCHAR}, 
      #{callbackMsg,jdbcType=VARCHAR}, #{taskExpireTime,jdbcType=TIMESTAMP}, #{punishInfo,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into yoda_identify_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deliveryOrderId != null">
        delivery_order_id,
      </if>
      <if test="channelOrderId != null">
        channel_order_id,
      </if>
      <if test="orderBizType != null">
        order_biz_type,
      </if>
      <if test="relRuleId != null">
        rel_rule_id,
      </if>
      <if test="collectContentType != null">
        collect_content_type,
      </if>
      <if test="collectMode != null">
        collect_mode,
      </if>
      <if test="riderAccountId != null">
        rider_account_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="faceIdentifyResult != null">
        face_identify_result,
      </if>
      <if test="helmetIdentifyResult != null">
        helmet_identify_result,
      </if>
      <if test="dressingIdentifyResult != null">
        dressing_identify_result,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="pushTaskTime != null">
        push_task_time,
      </if>
      <if test="resultCheckPass != null">
        result_check_pass,
      </if>
      <if test="callbackCode != null">
        callback_code,
      </if>
      <if test="callbackMsg != null">
        callback_msg,
      </if>
      <if test="taskExpireTime != null">
        task_expire_time,
      </if>
      <if test="punishInfo != null">
        punish_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deliveryOrderId != null">
        #{deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="channelOrderId != null">
        #{channelOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderBizType != null">
        #{orderBizType,jdbcType=INTEGER},
      </if>
      <if test="relRuleId != null">
        #{relRuleId,jdbcType=BIGINT},
      </if>
      <if test="collectContentType != null">
        #{collectContentType,jdbcType=VARCHAR},
      </if>
      <if test="collectMode != null">
        #{collectMode,jdbcType=INTEGER},
      </if>
      <if test="riderAccountId != null">
        #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="faceIdentifyResult != null">
        #{faceIdentifyResult,jdbcType=INTEGER},
      </if>
      <if test="helmetIdentifyResult != null">
        #{helmetIdentifyResult,jdbcType=INTEGER},
      </if>
      <if test="dressingIdentifyResult != null">
        #{dressingIdentifyResult,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pushTaskTime != null">
        #{pushTaskTime,jdbcType=TIMESTAMP},
      </if>
      <if test="resultCheckPass != null">
        #{resultCheckPass,jdbcType=INTEGER},
      </if>
      <if test="callbackCode != null">
        #{callbackCode,jdbcType=VARCHAR},
      </if>
      <if test="callbackMsg != null">
        #{callbackMsg,jdbcType=VARCHAR},
      </if>
      <if test="taskExpireTime != null">
        #{taskExpireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="punishInfo != null">
        #{punishInfo,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPOExample" resultType="java.lang.Long">
    select count(*) from yoda_identify_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update yoda_identify_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.deliveryOrderId != null">
        delivery_order_id = #{record.deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.channelOrderId != null">
        channel_order_id = #{record.channelOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderBizType != null">
        order_biz_type = #{record.orderBizType,jdbcType=INTEGER},
      </if>
      <if test="record.relRuleId != null">
        rel_rule_id = #{record.relRuleId,jdbcType=BIGINT},
      </if>
      <if test="record.collectContentType != null">
        collect_content_type = #{record.collectContentType,jdbcType=VARCHAR},
      </if>
      <if test="record.collectMode != null">
        collect_mode = #{record.collectMode,jdbcType=INTEGER},
      </if>
      <if test="record.riderAccountId != null">
        rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=INTEGER},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.faceIdentifyResult != null">
        face_identify_result = #{record.faceIdentifyResult,jdbcType=INTEGER},
      </if>
      <if test="record.helmetIdentifyResult != null">
        helmet_identify_result = #{record.helmetIdentifyResult,jdbcType=INTEGER},
      </if>
      <if test="record.dressingIdentifyResult != null">
        dressing_identify_result = #{record.dressingIdentifyResult,jdbcType=INTEGER},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pushTaskTime != null">
        push_task_time = #{record.pushTaskTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.resultCheckPass != null">
        result_check_pass = #{record.resultCheckPass,jdbcType=INTEGER},
      </if>
      <if test="record.callbackCode != null">
        callback_code = #{record.callbackCode,jdbcType=VARCHAR},
      </if>
      <if test="record.callbackMsg != null">
        callback_msg = #{record.callbackMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.taskExpireTime != null">
        task_expire_time = #{record.taskExpireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.punishInfo != null">
        punish_info = #{record.punishInfo,jdbcType=CHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update yoda_identify_task
    set id = #{record.id,jdbcType=BIGINT},
      delivery_order_id = #{record.deliveryOrderId,jdbcType=BIGINT},
      channel_order_id = #{record.channelOrderId,jdbcType=VARCHAR},
      order_biz_type = #{record.orderBizType,jdbcType=INTEGER},
      rel_rule_id = #{record.relRuleId,jdbcType=BIGINT},
      collect_content_type = #{record.collectContentType,jdbcType=VARCHAR},
      collect_mode = #{record.collectMode,jdbcType=INTEGER},
      rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      task_status = #{record.taskStatus,jdbcType=INTEGER},
      complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      face_identify_result = #{record.faceIdentifyResult,jdbcType=INTEGER},
      helmet_identify_result = #{record.helmetIdentifyResult,jdbcType=INTEGER},
      dressing_identify_result = #{record.dressingIdentifyResult,jdbcType=INTEGER},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      push_task_time = #{record.pushTaskTime,jdbcType=TIMESTAMP},
      result_check_pass = #{record.resultCheckPass,jdbcType=INTEGER},
      callback_code = #{record.callbackCode,jdbcType=VARCHAR},
      callback_msg = #{record.callbackMsg,jdbcType=VARCHAR},
      task_expire_time = #{record.taskExpireTime,jdbcType=TIMESTAMP},
      punish_info = #{record.punishInfo,jdbcType=CHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPO">
    update yoda_identify_task
    <set>
      <if test="deliveryOrderId != null">
        delivery_order_id = #{deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="channelOrderId != null">
        channel_order_id = #{channelOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderBizType != null">
        order_biz_type = #{orderBizType,jdbcType=INTEGER},
      </if>
      <if test="relRuleId != null">
        rel_rule_id = #{relRuleId,jdbcType=BIGINT},
      </if>
      <if test="collectContentType != null">
        collect_content_type = #{collectContentType,jdbcType=VARCHAR},
      </if>
      <if test="collectMode != null">
        collect_mode = #{collectMode,jdbcType=INTEGER},
      </if>
      <if test="riderAccountId != null">
        rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="faceIdentifyResult != null">
        face_identify_result = #{faceIdentifyResult,jdbcType=INTEGER},
      </if>
      <if test="helmetIdentifyResult != null">
        helmet_identify_result = #{helmetIdentifyResult,jdbcType=INTEGER},
      </if>
      <if test="dressingIdentifyResult != null">
        dressing_identify_result = #{dressingIdentifyResult,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pushTaskTime != null">
        push_task_time = #{pushTaskTime,jdbcType=TIMESTAMP},
      </if>
      <if test="resultCheckPass != null">
        result_check_pass = #{resultCheckPass,jdbcType=INTEGER},
      </if>
      <if test="callbackCode != null">
        callback_code = #{callbackCode,jdbcType=VARCHAR},
      </if>
      <if test="callbackMsg != null">
        callback_msg = #{callbackMsg,jdbcType=VARCHAR},
      </if>
      <if test="taskExpireTime != null">
        task_expire_time = #{taskExpireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="punishInfo != null">
        punish_info = #{punishInfo,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPO">
    update yoda_identify_task
    set delivery_order_id = #{deliveryOrderId,jdbcType=BIGINT},
      channel_order_id = #{channelOrderId,jdbcType=VARCHAR},
      order_biz_type = #{orderBizType,jdbcType=INTEGER},
      rel_rule_id = #{relRuleId,jdbcType=BIGINT},
      collect_content_type = #{collectContentType,jdbcType=VARCHAR},
      collect_mode = #{collectMode,jdbcType=INTEGER},
      rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      task_status = #{taskStatus,jdbcType=INTEGER},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      face_identify_result = #{faceIdentifyResult,jdbcType=INTEGER},
      helmet_identify_result = #{helmetIdentifyResult,jdbcType=INTEGER},
      dressing_identify_result = #{dressingIdentifyResult,jdbcType=INTEGER},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      push_task_time = #{pushTaskTime,jdbcType=TIMESTAMP},
      result_check_pass = #{resultCheckPass,jdbcType=INTEGER},
      callback_code = #{callbackCode,jdbcType=VARCHAR},
      callback_msg = #{callbackMsg,jdbcType=VARCHAR},
      task_expire_time = #{taskExpireTime,jdbcType=TIMESTAMP},
      punish_info = #{punishInfo,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into yoda_identify_task
    (delivery_order_id, channel_order_id, order_biz_type, rel_rule_id, collect_content_type, 
      collect_mode, rider_account_id, store_id, tenant_id, task_status, complete_time, 
      face_identify_result, helmet_identify_result, dressing_identify_result, ext_info, 
      update_time, create_time, push_task_time, result_check_pass, callback_code, callback_msg, 
      task_expire_time, punish_info)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.deliveryOrderId,jdbcType=BIGINT}, #{item.channelOrderId,jdbcType=VARCHAR}, 
        #{item.orderBizType,jdbcType=INTEGER}, #{item.relRuleId,jdbcType=BIGINT}, #{item.collectContentType,jdbcType=VARCHAR}, 
        #{item.collectMode,jdbcType=INTEGER}, #{item.riderAccountId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, 
        #{item.tenantId,jdbcType=BIGINT}, #{item.taskStatus,jdbcType=INTEGER}, #{item.completeTime,jdbcType=TIMESTAMP}, 
        #{item.faceIdentifyResult,jdbcType=INTEGER}, #{item.helmetIdentifyResult,jdbcType=INTEGER}, 
        #{item.dressingIdentifyResult,jdbcType=INTEGER}, #{item.extInfo,jdbcType=VARCHAR}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.pushTaskTime,jdbcType=TIMESTAMP}, 
        #{item.resultCheckPass,jdbcType=INTEGER}, #{item.callbackCode,jdbcType=VARCHAR}, 
        #{item.callbackMsg,jdbcType=VARCHAR}, #{item.taskExpireTime,jdbcType=TIMESTAMP}, 
        #{item.punishInfo,jdbcType=CHAR})
    </foreach>
  </insert>
</mapper>