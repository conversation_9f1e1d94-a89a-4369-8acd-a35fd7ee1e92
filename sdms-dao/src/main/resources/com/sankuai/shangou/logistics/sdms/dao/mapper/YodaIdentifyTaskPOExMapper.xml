<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.sdms.dao.mapper.YodaIdentifyTaskPOExMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="delivery_order_id" jdbcType="BIGINT" property="deliveryOrderId" />
        <result column="channel_order_id" jdbcType="VARCHAR" property="channelOrderId" />
        <result column="order_biz_type" jdbcType="VARCHAR" property="orderBizType" />
        <result column="rel_rule_id" jdbcType="BIGINT" property="relRuleId" />
        <result column="collect_content_type" jdbcType="VARCHAR" property="collectContentType" />
        <result column="collect_mode" jdbcType="VARCHAR" property="collectMode" />
        <result column="rider_account_id" jdbcType="BIGINT" property="riderAccountId" />
        <result column="store_id" jdbcType="BIGINT" property="storeId" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
        <result column="task_status" jdbcType="INTEGER" property="taskStatus" />
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
        <result column="face_identify_result" jdbcType="VARCHAR" property="faceIdentifyResult" />
        <result column="helmet_identify_result" jdbcType="VARCHAR" property="helmetIdentifyResult" />
        <result column="dressing_identify_result" jdbcType="VARCHAR" property="dressingIdentifyResult" />
        <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="push_task_time" jdbcType="TIMESTAMP" property="pushTaskTime" />
        <result column="result_check_pass" jdbcType="INTEGER" property="resultCheckPass" />
        <result column="callback_code" jdbcType="VARCHAR" property="callbackCode" />
        <result column="callback_msg" jdbcType="VARCHAR" property="callbackMsg" />
        <result column="task_expire_time" jdbcType="TIMESTAMP" property="taskExpireTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, delivery_order_id, channel_order_id, order_biz_type, rel_rule_id, collect_content_type,
        collect_mode, rider_account_id, store_id, tenant_id, task_status, complete_time,
        face_identify_result, helmet_identify_result, dressing_identify_result, ext_info,
        update_time, create_time, push_task_time, result_check_pass, callback_code, callback_msg,
        task_expire_time
    </sql>

    <select id="getRiderLatestAssignTask" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from yoda_identify_task
        where rider_account_id = ${riderAccountId}
        order by create_time desc
        limit 1
    </select>

    <select id="queryRiderEndTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from yoda_identify_task
        where rider_account_id = #{riderAccountId}
        and push_task_time between #{beginTime} and #{endTime}
        <choose>
            <when test="taskStatus != null">
                and task_status = #{taskStatus}
            </when>
            <otherwise>
                and task_status in (100, 80)
            </otherwise>
        </choose>
        <if test="verifyResult != null and verifyResult == 2">
            and ( face_identify_result = 0 or helmet_identify_result = 0 or dressing_identify_result = 0 )
        </if>
        order by push_task_time desc
    </select>
</mapper>