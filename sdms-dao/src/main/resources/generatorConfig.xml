<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="MybatisGenerator" targetRuntime="MyBatis3">
        <property name="autoDelimitKeywords" value="true"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"/>
        <plugin type="com.meituan.xframe.mybatis.generator.plugins.BatchInsertPlugin"/>
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="true"/>
            <property name="javaFileEncoding" value="UTF-8"/>
        </commentGenerator>

        <!--替换成自己数据库的jdbcRef -->
        <zebra jdbcRef="dhorderfulfillment_sdms_test"/>

        <javaTypeResolver>
            <property name="useJSR310Types" value="true"/>
        </javaTypeResolver>

        <!--load targetProject from properties-->
        <javaModelGenerator targetPackage="com.sankuai.shangou.logistics.sdms.dao.model" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- sql语句相关的xml或者注解的生成包路径 -->
        <sqlMapGenerator targetPackage="com.sankuai.shangou.logistics.sdms.dao.mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true" />
        </sqlMapGenerator>

        <!--load targetProject from properties-->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.shangou.logistics.sdms.dao.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!-- 示例 -->
<!--        <table tableName="self_delivery_poi_config" domainObjectName="SelfDeliveryPoiConfigPO">-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            <columnOverride column="enable_turn_delivery" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="dap_sync_status" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="is_deleted" javaType="java.lang.Integer"/>-->
<!--        </table>-->
        <table tableName="yoda_identify_task" domainObjectName="YodaIdentifyTaskPO">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

<!--        <table tableName="yoda_identify_task_config" domainObjectName="YodaIdentifyTaskConfigPO">-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="sdms_store_config" domainObjectName="SdmsStoreConfig">-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            <columnOverride column="is_deleted" javaType="java.lang.Integer"/>-->
<!--        </table>-->
<!--        <table tableName="assess_delivery_time_config" domainObjectName="AssessDeliveryTimeConfig">-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            <columnOverride column="is_deleted" javaType="java.lang.Integer"/>-->
<!--        </table>-->
    </context>
</generatorConfiguration>