package com.sankuai.meituan.shangou.empower.tms.delivery.rider.application;

import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.cmd.MergeOrderOperateCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryMergeOrderRecordRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.factory.DeliveryMergeOrderRecordFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 与运单并单操作相关的入口
 */
@Service
@Slf4j
public class DeliveryMergeOrderRecordApplicationService {

    @Resource
    private RiderDeliveryMergeOrderRecordRepository mergeOrderRecordRepository;

    @Resource
    private DeliveryMergeOrderRecordFactory mergeOrderRecordFactory;

    /**
     * 将运单列表中的运单添加到并单id中
     *
     * @param addList
     */
    public void addMergeOrderRecords(List<MergeOrderOperateCmd> addList) {
        if (CollectionUtils.isNotEmpty(addList)) {
            mergeOrderRecordRepository.batchUpsert(mergeOrderRecordFactory.batchCreate(addList));
        }
    }

    public void removeMergeOrderRecords(List<MergeOrderOperateCmd> removeList) {
        if (CollectionUtils.isNotEmpty(removeList)) {
            mergeOrderRecordRepository.batchRemove(ParamExchanger.transfer(removeList));
        }
    }

    public void transferMergeOrderRecords(List<MergeOrderOperateCmd> addList, List<MergeOrderOperateCmd> removeList) {
        if (CollectionUtils.isNotEmpty(addList)) {
            mergeOrderRecordRepository.batchUpsertAndRemove(
                    mergeOrderRecordFactory.batchCreate(addList), ParamExchanger.transfer(removeList));
            return;
        }
        if (CollectionUtils.isNotEmpty(removeList)) {
            mergeOrderRecordRepository.batchRemove(ParamExchanger.transfer(removeList));
        }
    }


    private static class ParamExchanger {

        private static List<Pair<Long, Long>> transfer(List<MergeOrderOperateCmd> cmdList) {
            List<Pair<Long, Long>> pairList = new ArrayList<>();
            for (MergeOrderOperateCmd cmd : cmdList) {
                for (RiderDeliveryOrder order : cmd.getRiderDeliveryOrders()) {
                    pairList.add(Pair.of(cmd.getMergeId(), order.getId()));
                }
            }
            return pairList;
        }
    }
}
