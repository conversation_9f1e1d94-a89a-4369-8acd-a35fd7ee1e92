package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/6
 * @Desc 门店账号信息
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountInfo {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 电话
     */
    private String phone;

    /**
     * 角色id
     */
    private List<Long> roleIdList;

    /**
     * 账号类型
     */
    private Integer accountType;

    /**
     * 是否有效，0-停用，1-有效。默认为创建0
     */
    private int valid;
}
