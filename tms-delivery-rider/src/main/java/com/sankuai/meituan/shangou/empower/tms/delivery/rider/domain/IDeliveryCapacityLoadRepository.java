package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.DeliveryCapacityLoadRecordSequenceEnum;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/1/11 15:45
 **/
public interface IDeliveryCapacityLoadRepository {
    Map<Long, Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO>> getStoreDeliveryCapacityRecord(List<String> storeIds);

    void setStoreDeliveryCapacityRecord(Map<Long, Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO>> deliveringCapacityLoadRecords);
}
