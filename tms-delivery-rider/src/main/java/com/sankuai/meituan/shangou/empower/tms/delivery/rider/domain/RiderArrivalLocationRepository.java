package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderArrivalLocationTRequest;

public interface RiderArrivalLocationRepository {
    /**
     * 更新或插入骑手送达时刻位置
     * @param riderArrivalLocation 骑手送达时刻经纬度
     * @return 数据库表更新行数
     */
    void save(RiderArrivalLocation riderArrivalLocation);

    /**
     * 通过运单id查询骑手到达时刻位置
     * @param deliveryOrderId 运单id
     * @return
     */
    RiderArrivalLocation getByDeliveryOrderId(Long deliveryOrderId);
}

