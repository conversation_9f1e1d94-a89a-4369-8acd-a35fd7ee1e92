package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.config;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.sdms.sdk.config.SdmsStoreConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-07-13
 * @email <EMAIL>
 */
@Slf4j
@Rhino
public class SdmsStoreConfigServiceClient {

    @Resource
    private SdmsStoreConfigService sdmsStoreConfigService;

    @Deprecated
    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 100L, multiplier = 1.5))
    @MethodLog(logRequest = true, logResponse = true)
    public Optional<Integer> calcAssessDeliveryDuration(Long merchantId, Long warehouseId, String tradeOrderNo, Integer orderBizType, Long deliveryDistance) {
        TResult<Integer> result = sdmsStoreConfigService.calcAssessDeliveryDuration(merchantId, warehouseId, tradeOrderNo, orderBizType, deliveryDistance);
        log.info("invoke  sdmsStoreConfigService.calcAssessDeliveryDuration, resp = {}", result);
        //小于等于0也认为计算失败了
        if (!result.isSuccess() || Objects.isNull(result.getData()) || result.getData() <= 0) {
            return Optional.empty();
        }
        return Optional.ofNullable(result.getData());
    }

    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 100L, multiplier = 1.5))
    @MethodLog(logRequest = true, logResponse = true)
    public Optional<Integer> calcAssessDeliveryDurationWithSeconds(Long merchantId, Long warehouseId, String tradeOrderNo, Integer orderBizType, Long deliveryDistance) {
        TResult<Integer> result = sdmsStoreConfigService.calcAssessDeliveryDurationBySeconds(merchantId, warehouseId, tradeOrderNo, orderBizType, deliveryDistance);
        log.info("invoke  sdmsStoreConfigService.calcAssessDeliveryDurationBySeconds, resp = {}", result);
        //小于等于0也认为计算失败了
        if (!result.isSuccess() || Objects.isNull(result.getData()) || result.getData() <= 0) {
            return Optional.empty();
        }
        return Optional.ofNullable(result.getData());
    }

    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 100L, multiplier = 1.5))
    @MethodLog(logRequest = true, logResponse = true)
    public Optional<Long> calcPushDownTimestamp(Long merchantId, long warehouseId, String tradeOrderNo, int orderBizType, long deliveryDistance) {
        TResult<Long> result = sdmsStoreConfigService.calcPushDownTimestamp(merchantId, warehouseId, tradeOrderNo, orderBizType, deliveryDistance);
        log.info("invoke sdmsStoreConfigService.calcPushDownTimestamp, resp = {}", result);
        //小于等于0也认为计算失败了
        if (!result.isSuccess() || Objects.isNull(result.getData()) || result.getData() <= 0) {
            return Optional.empty();
        }
        return Optional.ofNullable(result.getData());
    }
}
