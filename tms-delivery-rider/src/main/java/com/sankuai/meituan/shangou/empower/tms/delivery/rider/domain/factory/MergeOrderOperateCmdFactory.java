package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.factory;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.SpringContextUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.cmd.MergeOrderOperateCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.strategy.MergeOrderOperationCreateStrategy;

import org.springframework.stereotype.Component;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2022/9/7 23:49
 **/
@Component
public class MergeOrderOperateCmdFactory {

    public List<MergeOrderOperateCmd> createMergeOrderCmdByStrategy(RiderDeliveryOrder deliveryOrder,
                                                    Class<? extends MergeOrderOperationCreateStrategy> strategy) {
        MergeOrderOperationCreateStrategy strategyBean = SpringContextUtils.getBean(strategy);
        return strategyBean.buildMergeOrderOperateCmd(deliveryOrder);
    }

}
