package com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils;

import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2023/5/4 20:01
 **/
public class UUIDUtil {
    public static long uuidToLong(UUID uuid) {
        long mostSignificantBits = uuid.getMostSignificantBits();
        long leastSignificantBits = uuid.getLeastSignificantBits();
        long high = mostSignificantBits ^ leastSignificantBits;
        long low = leastSignificantBits ^ (mostSignificantBits & 0xFFFFL);
        return Math.abs(high ^ low);
    }
}
