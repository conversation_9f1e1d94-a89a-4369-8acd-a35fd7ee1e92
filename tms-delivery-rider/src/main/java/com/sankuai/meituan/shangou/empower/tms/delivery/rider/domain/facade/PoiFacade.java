package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.facade;

import com.meituan.shangou.saas.tenant.thrift.PoiApprovalManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiApprovalScenesTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiApprovalFullEmployeeChainRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiApprovalFullEmployeeChainResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiInfoResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;

import com.sankuai.drunkhorsemgmt.labor.types.Failure;
import com.sankuai.drunkhorsemgmt.labor.types.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;

@Component
@Slf4j
public class PoiFacade {
    @Resource
    private PoiThriftService poiThriftService;

    @Resource
    private PoiManageThriftService poiManageThriftService;

    @Resource
    private PoiApprovalManageThriftService poiApprovalManageThriftService;

    private static final int SUCCESS_CODE = 0;


    public List<PoiInfoDto> queryPoiListByTenantId(Long tenantId) {
        PoiListResponse poiListResponse = poiThriftService.queryTenantPoiList(tenantId);

        if (FailureCodeEnum.SUCCESS.getCode() != poiListResponse.getStatus().getCode()) {
            log.error("query tenant poi by id failed: {}", poiListResponse.getStatus().getMessage());
            throw new CommonRuntimeException(FailureCodeEnum.QUERY_POI_FAILED, poiListResponse.getStatus().getMessage());
        }
        return poiListResponse.getPoiList();
    }

    @NotNull
    public PoiInfoDto queryPoiManagerInfo(long poiId) {
        PoiInfoResponse poiInfoResponse = poiManageThriftService.queryPoiInfo(poiId);
        if (FailureCodeEnum.SUCCESS.getCode() != poiInfoResponse.getStatus().getCode()) {
            log.error("query tenant poi by id failed: {}", poiInfoResponse.getStatus().getMessage());
            throw new CommonRuntimeException(FailureCodeEnum.QUERY_POI_FAILED, poiInfoResponse.getStatus().getMessage());
        }
        if (poiInfoResponse.getPoiInfo() == null) {
            throw new CommonRuntimeException("No such POI: " + poiId);
        }

        return poiInfoResponse.getPoiInfo();
    }

    public Map<Long, PoiInfoDto> queryPoiByIds(Long tenantId, Set<Long> poiIds) {
        PoiMapResponse poiListResponse = poiThriftService.queryTenantPoiInfoMapByPoiIds(new ArrayList<>(poiIds), tenantId);
        if (FailureCodeEnum.SUCCESS.getCode() != poiListResponse.getStatus().getCode()) {
            log.error("query poi by ids failed: {}", poiListResponse.getStatus().getMessage());
            throw new CommonRuntimeException(FailureCodeEnum.QUERY_POI_FAILED, poiListResponse.getStatus().getMessage());
        }
        return poiListResponse.getPoiInfoMap();
    }


    public Result<List<List<String>>> queryApprovalFullEmployeeChain(Long tenantId, Long poiId, PoiApprovalScenesTypeEnum poiApprovalScenesTypeEnum) {
        if (poiId == null) {
            new Result<>(new Failure(com.sankuai.drunkhorsemgmt.labor.constants.FailureCodeEnum.PARAM_ERROR));
        }

        PoiApprovalFullEmployeeChainRequest request = new PoiApprovalFullEmployeeChainRequest();
        request.setTenantId(tenantId);
        request.setPoiId(poiId);
        request.setPoiApprovalScenesType(poiApprovalScenesTypeEnum.getType());
        PoiApprovalFullEmployeeChainResponse resp =
                poiApprovalManageThriftService.queryApprovalFullEmployeeChain(request);
        log.info("invoke poiApprovalManageThriftService.queryApprovalFullEmployeeChain, poiId={}, response={}",
                poiId, resp);
        if (!Objects.equals(resp.getStatus().getCode(), SUCCESS_CODE)) {
            return new Result<>(new Failure(com.sankuai.drunkhorsemgmt.labor.constants.FailureCodeEnum.SYSTEM_ERROR));
        }

        if (Objects.isNull(resp.getEmployeeChain())) {
            return new Result<>(new Failure(com.sankuai.drunkhorsemgmt.labor.constants.FailureCodeEnum.NONE_ERROR));
        }

        return new Result<>(resp.getEmployeeChain());
    }
}
