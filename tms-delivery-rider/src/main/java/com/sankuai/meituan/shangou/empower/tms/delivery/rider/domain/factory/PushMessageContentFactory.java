package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.factory;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantChannelStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.DeliveryCapacityLoadRecordDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.DeliveryBusyLevelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.DeliveryCapacityLoadRecordSequenceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.PushMessageTypeEnum;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.DeliveryCapacityLoadRecordSequenceEnum.THE_FIRST_BEFORE_LATEST_RECORD;

/**
 * <AUTHOR>
 * @since 2023/1/16 16:31
 **/
public class PushMessageContentFactory {

    private static final int MEITUAN_CHANNEL_ID = 100;
    private static final int WEI_XIN_CHANNEL_ID = 700;

    public static String buildPushContent(PushMessageTypeEnum pushMessageTypeEnum,
                                           DeliveryBusyLevelEnum deliveryBusyLevelEnum,
                                           List<TenantChannelStoreInfo> tenantChannelStoreInfos,
                                           Integer storeDeliveringOrderCount,
                                           Integer onDutyEmployeeCount,
                                           Float storeDeliveryCapacityLoad,
                                           Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO> capacityRecordMap) {
        if (CollectionUtils.isEmpty(tenantChannelStoreInfos)) {
            return "";
        }
        String cityName = tenantChannelStoreInfos.get(0).getCityName();
        String poiName = tenantChannelStoreInfos.get(0).getStoreName();
        Map<Integer, String> channelStoreMap = tenantChannelStoreInfos.stream()
                .collect(Collectors.toMap(TenantChannelStoreInfo::getChannelId, TenantChannelStoreInfo::getChannelPoiId));

        StringBuilder alarmStringBuilder = new StringBuilder()
                .append("❗【爆单告警】\n")
                .append("\u0020\u0020\u0020\u0020【门店】")
                .append(cityName)
                .append(poiName)
                .append("\n")
                .append("\u0020\u0020\u0020\u0020【渠道门店ID】外卖\u0020")
                .append(channelStoreMap.getOrDefault(MEITUAN_CHANNEL_ID, ""))
                .append("\u0020微商城\u0020")
                .append(channelStoreMap.getOrDefault(WEI_XIN_CHANNEL_ID, ""))
                .append("\n")
                .append("\u0020\u0020\u0020\u0020【当前爆单等级】")
                .append(deliveryBusyLevelEnum.getDesc())
                .append("\n")
                .append("\u0020\u0020\u0020\u0020【负载详情】运单数\u0020")
                .append(storeDeliveringOrderCount)
                .append("\u0020在岗骑手数\u0020")
                .append(onDutyEmployeeCount)
                .append("\u0020人均负载\u0020")
                .append(Objects.equals(onDutyEmployeeCount, 0) ? "--" : String.format("%2.1f", storeDeliveryCapacityLoad))
                .append("\n");

        StringBuilder alarmMitigateStringBuilder = new StringBuilder()
                .append("✅【负载压力缓解】\n")
                .append("\u0020\u0020\u0020\u0020【门店】")
                .append(cityName)
                .append(poiName)
                .append("\n")
                .append("\u0020\u0020\u0020\u0020【渠道门店ID】外卖\u0020")
                .append(channelStoreMap.getOrDefault(MEITUAN_CHANNEL_ID, ""))
                .append("\u0020微商城\u0020")
                .append(channelStoreMap.getOrDefault(WEI_XIN_CHANNEL_ID, ""))
                .append("\n")
                .append("\u0020\u0020\u0020\u0020【当前爆单等级】")
                .append(deliveryBusyLevelEnum.getDesc())
                .append("\n")
                .append("\u0020\u0020\u0020\u0020【负载详情】运单数\u0020")
                .append(storeDeliveringOrderCount)
                .append("\u0020在岗骑手数\u0020")
                .append(onDutyEmployeeCount)
                .append("\u0020人均负载\u0020")
                .append(Objects.equals(onDutyEmployeeCount, 0) ? "--" : String.format("%2.1f", storeDeliveryCapacityLoad))
                .append("\n")
                .append("\u0020\u0020\u0020\u0020【一小时前爆单等级】")
                .append(Optional.ofNullable(capacityRecordMap.get(THE_FIRST_BEFORE_LATEST_RECORD)).map(DeliveryCapacityLoadRecordDO::getDeliveryBusyLevel).map(DeliveryBusyLevelEnum::getDesc).orElse(""))
                .append("\n");

        switch (pushMessageTypeEnum) {
            case PUSH_FIRST_ALARM_MESSAGE:
                return alarmStringBuilder.append("\u0020\u0020\u0020\u0020\u0020请及时评估、取消C端营销活动").toString();
            case PUSH_SECOND_ALARM_MESSAGE:
                return alarmStringBuilder.append("\u0020\u0020\u0020\u0020\u0020请及时评估、增加门店配送费").toString();
            case PUSH_THIRD_ALARM_MESSAGE:
                return alarmStringBuilder.append("\u0020\u0020\u0020\u0020\u0020请及时评估、缩减门店配送范围").toString();
            case PUSH_SERIOUS_ALARM_MESSAGE:
                return alarmStringBuilder.append("\u0020\u0020\u0020\u0020\u0020请及时评估、置休门店").toString();
            case PUSH_ALARM_MITIGATE_MESSAGE:
                return alarmMitigateStringBuilder.append("\u0020\u0020\u0020\u0020\u0020请及时恢复门店相应配置！").toString();
            default:
                return "";
        }
    }
}
