package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.PageQueryDeliveryExceptionRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.TimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiderDeliveryExceptionPageQueryConditions {
    private PageRequest pageRequest;

    private Long tenantId;

    private List<Long> storeIds;

    private Long riderAccountId;

    private List<Integer> orderBizTypeList;

    private Long channelOrderId;

    private List<Integer> exceptionTypeList ;

    private Long payTimeStartTimeStamp;

    private Long payTimeEndTimeStamp;

    private Long reportTimeStartTimeStamp;

    private Long reportTimeEndTimeStamp;

    public RiderDeliveryExceptionPageQueryConditions(PageQueryDeliveryExceptionRequest request) {

        this.pageRequest = new PageRequest(Optional.ofNullable(request.getPageNum()).orElse(1),
                Optional.ofNullable(request.getPageSize()).orElse(20));
        this.tenantId = request.getTenantId();
        this.storeIds = request.getStoreIds();
        this.channelOrderId = request.getChannelOrderId();
        this.orderBizTypeList = request.getOrderBizTypeList();
        this.payTimeStartTimeStamp = request.getPayTimeStartTimeStamp();
        this.payTimeEndTimeStamp = request.getPayTimeEndTimeStamp();
        this.reportTimeStartTimeStamp = request.getReportTimeStartTimeStamp();
        this.reportTimeEndTimeStamp = request.getReportTimeEndTimeStamp();
        this.riderAccountId = request.getRiderAccountId();
        this.exceptionTypeList = request.getExceptionTypeList();
    }
}
