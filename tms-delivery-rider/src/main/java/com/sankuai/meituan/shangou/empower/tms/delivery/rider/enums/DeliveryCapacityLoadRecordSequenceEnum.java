package com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/1/11 16:58
 **/
public enum DeliveryCapacityLoadRecordSequenceEnum {
    LATEST_RECORD("t", "最新点"),
    THE_FIRST_BEFORE_LATEST_RECORD("t-1", "前一个点"),
    THE_SECOND_BEFORE_LATEST_RECORD("t-2", "前两个点");

    private final String value;
    private final String desc;

    DeliveryCapacityLoadRecordSequenceEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return this.value;
    }

    public String getDesc() {
        return this.desc;
    }

    public static DeliveryCapacityLoadRecordSequenceEnum enumOf(String value) {
        for (DeliveryCapacityLoadRecordSequenceEnum each : values()) {
            if (Objects.equals(each.getValue(),value)) {
                return each;
            }
        }

        throw new  IllegalArgumentException("无对应枚举类型");
    }
}
