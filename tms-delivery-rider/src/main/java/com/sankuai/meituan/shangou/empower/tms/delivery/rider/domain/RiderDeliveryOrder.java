package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.dianping.cat.Cat;
import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.shangou.dms.base.model.DeliveryOrderBase;
import com.sankuai.meituan.shangou.dms.base.model.value.CustomerOrderKey;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryTimeline;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.SpringContextUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.log.RiderDeliveryOrderLogClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.RiderDeliveryOrderSyncOutClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.AuditStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.ChangeRiderEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Nullable;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.*;
import static com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.UUIDUtil.uuidToLong;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/9
 * 商家自配送物流单
 */
@Slf4j
@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class RiderDeliveryOrder extends DeliveryOrderBase {

	/**
	 * 转单前的骑手
	 */
	@Setter
	private StaffRider riderInfo;

	/**
	 * 转单前的骑手
	 */
	@Setter
	private StaffRider changeFromRider;

	/**
	 * 当前状态是否被锁定：0-未锁定；1-锁定
	 */
	private Integer currentStatusLocked;

	/**
	 * 状态锁定次数
	 */
	private Integer totalLockCount;

	/**
	 * 当前锁定时间
	 */
	private LocalDateTime currentLockTime;

	/**
	 * 当前解锁时间
	 */
	private LocalDateTime currentUnlockTime;


	/**
	 * 扩展信息
	 */
	private RiderDeliveryExtInfo riderDeliveryExtInfo;


	/**
	 * 是否为三方异常单，兼容三方运单
	 * @Deprecated 融合二期去掉
	 */
	private Boolean isThirdException;


	public RiderDeliveryOrder(Long id, Long tenantId, Long storeId, DeliveryChannelEnum deliveryChannelEnum, Integer deliveryChannel, CustomerOrderKey customerOrderKey, DeliveryStatusEnum status, DeliveryTimeline timeline, Receiver receiver, Long distance, Integer version, StaffRider riderInfo, StaffRider changeFromRider, Integer currentStatusLocked, Integer totalLockCount, LocalDateTime currentLockTime, LocalDateTime currentUnlockTime, RiderDeliveryExtInfo riderDeliveryExtInfo, Boolean isThirdException) {
		super(id, tenantId, storeId, deliveryChannelEnum, deliveryChannel, customerOrderKey, status, timeline, receiver, distance, version);
		this.riderInfo = riderInfo;
		this.changeFromRider = changeFromRider;
		this.currentStatusLocked = currentStatusLocked;
		this.totalLockCount = totalLockCount;
		this.currentLockTime = currentLockTime;
		this.currentUnlockTime = currentUnlockTime;
		this.riderDeliveryExtInfo = riderDeliveryExtInfo;
		this.isThirdException = isThirdException;
	}

	/**
	 * 检查实付金额是否大于某个值
	 * @return
	 */
	public boolean checkPayAmtBiggerThan(Integer actualPayAmt) {
		return this.getRiderDeliveryExtInfo() != null && this.riderDeliveryExtInfo.getActualPayAmt() != null && this.riderDeliveryExtInfo.getActualPayAmt() > actualPayAmt;
	}

	/**
	 * 判断运单是否考核超时（超时分为考核超时和严重超时，其中严重超时是考核超时的子集），本接口检查当前运单是否考核超时
	 * 考核超时：
	 * 		预订单：当前时间>预计送达时间+5分钟
	 * 		实时单：当前时间>支付时间+25分钟
	 *
	 * @return
	 */
	public boolean checkDeliveryFulfillTimeout() {
		if (this.getCustomerOrderKey() != null && this.getCustomerOrderKey().getReserved() != null && this.getCustomerOrderKey().getReserved()) {
			return this.getTimeline().getEstimatedDeliveryEndTime() != null
					&& this.getTimeline().getEstimatedDeliveryEndTime().plusMinutes(5).isBefore(LocalDateTime.now());
		}
		//实时单以运单创建时间
		return this.getTimeline() != null && this.getTimeline().getCreateTime().plusMinutes(25).isBefore(LocalDateTime.now());
	}

	/**
	 * (新版)判断运单是否考核超时（超时分为考核超时和严重超时，其中严重超时是考核超时的子集），本接口检查当前运单是否考核超时
	 * 考核超时：
	 *      预订单：当前时间>预计送达时间
	 *      实时单：当前时间>预计送达时间
	 *
	 * @return
	 */
	public boolean checkDeliveryFulfillTimeoutV2() {
		if (riderDeliveryExtInfo != null && riderDeliveryExtInfo.getIsOneYuanOrder()) {
			return false;
		}

		return LocalDateTime.now().isAfter(this.getTimeline().getEstimatedDeliveryEndTime());
	}

	/**
	 * 目标状态是否可以从当前状态到达
	 * @param targetStatus
	 * @return
	 */
	public boolean canStatusBeReachedFromCurrent(DeliveryStatusEnum targetStatus) {
		// DELIVERY_DONE and DELIVERY_CANCELLED are final statuses
		if (this.getStatus().isFinalStatus()) {
			return false;
		}
		// Otherwise, the target status code must be greater than the current status code
		return targetStatus.getCode() > this.getStatus().getCode();
	}

	/**
	 * 检查运单状态是否已被锁定：为1表示锁定
	 * @return
	 */
	public boolean checkOrderStatusLocked() {
        //只有骑手已接单和骑手已取货两种状态下，才关心currentStatusLocked字段，其他状态目前不支持锁定
        if (DeliveryStatusEnum.RIDER_TAKEN_GOODS.equals(getStatus())
                || DeliveryStatusEnum.RIDER_ASSIGNED.equals(getStatus())) {
            return this.currentStatusLocked == 1;
        }
        return false;
	}

	/**
	 * 当前运单状态是否可以被锁定
	 * @return
	 */
	public boolean canLockStatus() {
		//只有骑手已接单和骑手已取货两种状态下，才关心可不可以锁定，其他状态目前不支持锁定
		if (DeliveryStatusEnum.RIDER_TAKEN_GOODS.equals(getStatus())
				|| DeliveryStatusEnum.RIDER_ASSIGNED.equals(getStatus())) {
			//不可以重复锁
			if (this.checkOrderStatusLocked()) {
				return false;
			}
			//目前运单整个生命周期只能锁定一次
			if (totalLockCount > 0) {
				return false;
			}
			return this.hasRiderReportedCannotConnectCustomer();
		}

		return false;
	}

	//只有骑手已经上报过联系不上顾客异常后才可以锁定状态
	public boolean hasRiderReportedCannotConnectCustomer() {

		List<RiderDeliveryException> exceptionList = SpringContextUtils.getBean(RiderDeliveryExceptionRepository.class)
				.queryDeliveryExceptionByChannelOrderId(this.getTenantId(), this.getStoreId(),
						this.getCustomerOrderKey().getChannelOrderId(), this.getCustomerOrderKey().getOrderBizType());
		if (CollectionUtils.isNotEmpty(exceptionList)) {
			//任意一条异常上报是联系不上顾客即可锁定
			for (RiderDeliveryException exception : exceptionList) {
				if (RiderDeliveryExceptionEnum.cannotConnectCustomer(exception.getExceptionType())) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 锁定运单状态，这里没判定当前状态是否可以锁定，需要调用方先调用canStatusLock方法判断
	 */
	private void lockStatus() {
		this.currentStatusLocked = 1;
		this.totalLockCount += 1;
		this.currentLockTime = LocalDateTime.now();
		this.currentUnlockTime = null;
	}

	/**
	 * 解锁运单状态，这里没判定当前状态是否可以解锁，需要调用方先调用checkOrderStatusLocked方法判断
	 */
	private void unlockStatus() {
		this.currentStatusLocked = 0;
		this.currentUnlockTime = LocalDateTime.now();
	}

	public Optional<Failure> lockDeliveryStatus(StaffRider rider) {

		if (this.checkOrderStatusLocked()) {
			log.info("骑手重复操作锁定，幂等处理，deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.empty();
		}
		if (!this.canLockStatus()) {
			log.info("当前状态不支持该操作，deliveryOrderId={}, rider={}, deliveryStatus={}, currentStatusLocked={}, totalLockCount={}",
					this.getId(), rider, this.getStatus(), this.currentStatusLocked, this.totalLockCount);
			return Optional.of(new Failure(false, FailureCodeEnum.ORDER_STATUS_ERROR));
		}
		if (!isSameRider(rider)) {
			log.error("当前用户没有该运单操作权限， deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.of(new Failure(false, FailureCodeEnum.RIDER_OPERATE_CONFLICT));
		}
		this.lockStatus();
		persist();

		recordDeliveryChangeLog(this.getId(), this.getStatus(), DeliveryEventEnum.DELIVERY_STATUS_LOCK, null, LocalDateTime.now());
		syncOut(DeliveryAsyncOutTypeEnum.LOCK_DELIVERY_STATUS, rider);
		return Optional.empty();
	}

	public Optional<Failure> unlockDeliveryStatus(StaffRider rider) {
		if (!this.checkOrderStatusLocked()) {
			log.info("骑手重复操作解锁，幂等处理，deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.empty();
		}
		if (!isSameRider(rider)) {
			log.error("当前用户没有该运单操作权限， deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.of(new Failure(false, FailureCodeEnum.RIDER_OPERATE_CONFLICT));
		}
		this.unlockStatus();
		persist();

		recordDeliveryChangeLog(this.getId(), this.getStatus(), DeliveryEventEnum.DELIVERY_STATUS_UNLOCK, null, LocalDateTime.now());
		syncOut(DeliveryAsyncOutTypeEnum.UNLOCK_DELIVERY_STATUS, rider);
		return Optional.empty();
	}


	public Optional<Failure> accept(StaffRider rider, RiderLocationDetail riderLocationDetail) {
		if (this.getStatus() == DeliveryStatusEnum.RIDER_ASSIGNED && isSameRider(rider)) {
			log.info("骑手重复操作，幂等处理， deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.empty();
		}

		if (this.getRiderInfo() != null && !isSameRider(rider)) {
			log.error("已被其他骑手操作， deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.of(new Failure(false, FailureCodeEnum.RIDER_OPERATE_CONFLICT));
		}

		//当前状态是否被锁定
		if (this.checkOrderStatusLocked()) {
			log.error("当前运单状态被锁定， deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_STATUS_LOCKED));
		}

		if (this.getStatus() == DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER) {
			LocalDateTime now = LocalDateTime.now();
			this.status = DeliveryStatusEnum.RIDER_ASSIGNED;
			this.riderInfo = rider;
			this.getTimeline().setLastEventTime(now);
			persist();

			recordDeliveryChangeLog(this.getId(), DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryEventEnum.RIDER_ASSIGN, null, now);
			recordDeliveryChangeLog(this.getId(), DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryEventEnum.RIDER_CHANGE, new RiderDeliveryOrderRiderChangeInfo(null, this.getRiderInfo()), now);
			syncOut(now,riderLocationDetail);
			if (DeliveryRiderMccConfigUtils.checkIsDHTenant(this.getTenantId()) || (!DeliveryRiderMccConfigUtils.fusionSelfDegreeSwitch())) {
				syncOutRiderChange(rider, null, ChangeRiderEventEnum.ACCEPT);
			}
			notifySyncRiderPosition(this.getId(), DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY,this.getTenantId(),this.getStoreId());
			return Optional.empty();

		} else {
			log.error("运单状态异常， deliveryOrderId={}, status={}", this.getId(), this.getStatus());
			return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_STATUS_ERR, this.getStatus().getDesc()));
		}
	}

	public Optional<Failure> takeAway(StaffRider rider, RiderLocationDetail riderLocationDetail) {
		if (this.getStatus() == DeliveryStatusEnum.RIDER_TAKEN_GOODS && isSameRider(rider)) {
			log.info("骑手重复操作，幂等处理， deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.empty();
		}

		if (!isSameRider(rider)) {
			log.error("已被其他骑手操作， deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.of(new Failure(false, FailureCodeEnum.RIDER_OPERATE_CONFLICT));
		}

		//当前状态是否被锁定
		if (this.checkOrderStatusLocked()) {
			log.error("当前运单状态被锁定， deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_STATUS_LOCKED));
		}

		if (this.getStatus().getCode() < DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode()) {
			LocalDateTime now = LocalDateTime.now();
			this.status = DeliveryStatusEnum.RIDER_TAKEN_GOODS;
			this.getTimeline().setLastEventTime(now);
			persist();

			recordDeliveryChangeLog(this.getId(), DeliveryStatusEnum.RIDER_TAKEN_GOODS, DeliveryEventEnum.RIDER_START_DELIVERY, null, now);
			syncOut(now,riderLocationDetail);
			syncOut(DeliveryAsyncOutTypeEnum.TAKE_AWAY, rider);
			return Optional.empty();

		} else {
			log.error("运单状态异常， deliveryOrderId={}, status={}", this.getId(), this.getStatus());
			return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_STATUS_ERR, this.getStatus().getDesc()));
		}
	}

	public Optional<Failure> complete(StaffRider rider, RiderLocationDetail riderLocationDetail, Boolean isWeakNetWork) {
		if (this.getStatus() == DeliveryStatusEnum.DELIVERY_DONE && isSameRider(rider)) {
			log.info("骑手重复操作，幂等处理， deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.empty();
		}

		if (!isSameRider(rider)) {
			log.error("已被其他骑手操作， deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.of(new Failure(false, FailureCodeEnum.RIDER_OPERATE_CONFLICT));
		}

		//当前状态是否被锁定
		if (this.checkOrderStatusLocked()) {
			log.error("当前运单状态被锁定， deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_STATUS_LOCKED));
		}

		if (this.getStatus().getCode() < DeliveryStatusEnum.DELIVERY_DONE.getCode()) {
			LocalDateTime now = LocalDateTime.now();
			this.status = DeliveryStatusEnum.DELIVERY_DONE;
			this.getTimeline().setDeliveryDoneTime(now);
			this.getTimeline().setLastEventTime(now);
			persist();

			recordDeliveryChangeLog(this.getId(), DeliveryStatusEnum.DELIVERY_DONE, DeliveryEventEnum.RIDER_FINISH_DELIVERY, null, now);
			syncOut(now,riderLocationDetail);
			//再同步一次消息 用来统计并单记录
			syncOut(DeliveryAsyncOutTypeEnum.DELIVER_COMPLETE, rider);
			reportDeliveryDurationLogEvent();
			return Optional.empty();

		} else {
			log.error("运单状态异常， deliveryOrderId={}, status={}", this.getId(), this.getStatus());
			return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_STATUS_ERR, this.getStatus().getDesc()));
		}
	}

	public Optional<Failure> completeWithProofPhoto(StaffRider rider, RiderLocationDetail riderLocationDetail,
													Boolean isWeakNetWork, List<String> proofPhotoUrls,
													Integer deliverySignType) {
		if (this.getStatus() == DeliveryStatusEnum.DELIVERY_DONE && isSameRider(rider)) {
			log.info("骑手重复操作，幂等处理， deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.of(new Failure(false, FailureCodeEnum.ALREADY_DELIVERY_DONE));
		}

		if (!isSameRider(rider)) {
			log.error("已被其他骑手操作， deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.of(new Failure(false, FailureCodeEnum.RIDER_OPERATE_CONFLICT));
		}

		//当前状态是否被锁定
		if (this.checkOrderStatusLocked()) {
			log.error("当前运单状态被锁定， deliveryOrderId={}, rider={}", this.getId(), rider);
			return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_STATUS_LOCKED));
		}

		if (this.getStatus().getCode() < DeliveryStatusEnum.DELIVERY_DONE.getCode()) {
			LocalDateTime now = LocalDateTime.now();
			this.status = DeliveryStatusEnum.DELIVERY_DONE;
			this.getTimeline().setDeliveryDoneTime(now);
			this.getTimeline().setLastEventTime(now);
			List<RiderDeliveryExtInfo.DeliveryProofPhotoInfo> deliveryProofPhotoInfos = proofPhotoUrls.stream()
					.map(url -> new RiderDeliveryExtInfo.DeliveryProofPhotoInfo(url, uuidToLong(UUID.randomUUID()), AuditStatusEnum.WAITING_TO_AUDIT, null))
					.collect(Collectors.toList());

			if (this.getRiderDeliveryExtInfo() != null) {
				this.getRiderDeliveryExtInfo().setDeliveryProofPhotoInfoList(deliveryProofPhotoInfos);
				this.getRiderDeliveryExtInfo().setSignType(deliverySignType);
				this.getRiderDeliveryExtInfo().setIsWeakNetwork(isWeakNetWork);
				this.getRiderDeliveryExtInfo().setProofPhotoCount(deliveryProofPhotoInfos.size());
			} else {
				this.riderDeliveryExtInfo = new RiderDeliveryExtInfo(null, false, deliveryProofPhotoInfos, false, null, null, deliverySignType, isWeakNetWork, null, null, deliveryProofPhotoInfos.size(), null, null, false, null, null);
			}

			persist();

			recordDeliveryChangeLog(this.getId(), DeliveryStatusEnum.DELIVERY_DONE, DeliveryEventEnum.RIDER_FINISH_DELIVERY, null, now);
			syncOut(now,riderLocationDetail);
			//再同步一次消息 用来统计并单记录
			syncOut(DeliveryAsyncOutTypeEnum.DELIVER_COMPLETE, rider);
			reportDeliveryDurationLogEvent();
			return Optional.empty();

		} else {
			log.error("运单状态异常， deliveryOrderId={}, status={}", this.getId(), this.getStatus());
			return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_STATUS_ERR, this.getStatus().getDesc()));
		}
	}

	public Optional<Failure> saveDeliveryProofPhotoUrls(List<String> photoUrls, Long riderAccountId) {
		if(CollectionUtils.isEmpty(photoUrls) || riderAccountId == null) {
			return Optional.empty();
		}

		//校验当前是否满足上传条件
		if (!couldPostDeliveryProofPhoto()) {
			return Optional.of(new Failure(false, DELIVERY_ORDER_STATUS_ERROR));
		}

		//校验骑手
		if (Objects.nonNull(this.getRiderInfo()) && !Objects.equals(this.getRiderInfo().getRiderAccountId(), riderAccountId)) {
			return Optional.of(new Failure(false, DELIVER_RIDER_ERROR));
		}

		List<RiderDeliveryExtInfo.DeliveryProofPhotoInfo> deliveryProofPhotoInfos = photoUrls.stream()
				.map(url -> new RiderDeliveryExtInfo.DeliveryProofPhotoInfo(url, uuidToLong(UUID.randomUUID()), AuditStatusEnum.WAITING_TO_AUDIT, null))
				.collect(Collectors.toList());

		if (this.getRiderDeliveryExtInfo() != null) {
			this.getRiderDeliveryExtInfo().setDeliveryProofPhotoInfoList(deliveryProofPhotoInfos);
		} else {
			this.riderDeliveryExtInfo = new RiderDeliveryExtInfo(null, false, deliveryProofPhotoInfos, false, null, null, null, null, null, null, 0, null, null, false, null, null);
		}

		persist();

		return Optional.empty();
	}

	private boolean isSameRider(StaffRider rider) {
		if (rider == null || this.getRiderInfo() == null) {
			return false;
		}
		return rider.getRiderAccountId().equals(this.getRiderInfo().getRiderAccountId());
	}

	private void persist() {
		SpringContextUtils.getBean(RiderDeliveryOrderRepository.class).save(this);
	}

	private void recordDeliveryChangeLog(Long deliveryId, DeliveryStatusEnum status, DeliveryEventEnum deliveryEvent,
										 RiderDeliveryOrderChangeInfo changeInfo, LocalDateTime changeTime) {
		RiderDeliveryOrderLogClient logClient = SpringContextUtils.getBean(RiderDeliveryOrderLogClient.class);
		logClient.logDeliveryChange(deliveryId, status, deliveryEvent, changeInfo, changeTime);
	}

	@Deprecated
	private void syncOut(LocalDateTime changeTime,RiderLocationDetail riderLocationDetail) {
		SpringContextUtils.getBean(RiderDeliveryOrderSyncOutClient.class).syncToOrderSystem(this, changeTime,riderLocationDetail);
	}

	private void syncOut(DeliveryAsyncOutTypeEnum typeEnum, StaffRider rider) {

		switch (typeEnum) {
			case TAKE_AWAY:
				DeliveryChangeSyncOutMessage<DeliveryChangeSyncOutMessage.TakeAwayBody> takeAwayMessage =
						new DeliveryChangeSyncOutMessage<>(typeEnum.getValue(),
								new DeliveryChangeSyncOutMessage.Head(this.getTenantId(),
										this.getStoreId(), this.getId(), this.getCustomerOrderKey().getOrderBizType(), this.getCustomerOrderKey().getOrderId(),
										this.getCustomerOrderKey().getChannelOrderId(), this.getStatus().getCode()),
								new DeliveryChangeSyncOutMessage.TakeAwayBody(rider.getRiderAccountId()));

				SpringContextUtils.getBean(RiderDeliveryOrderSyncOutClient.class).asyncOut(takeAwayMessage);
				return;

			default:
				DeliveryChangeSyncOutMessage<DeliveryChangeSyncOutMessage.DeliveryCompleteBody> message =
						new DeliveryChangeSyncOutMessage<>(typeEnum.getValue(),
								new DeliveryChangeSyncOutMessage.Head(this.getTenantId(),
										this.getStoreId(), this.getId(), this.getCustomerOrderKey().getOrderBizType(), this.getCustomerOrderKey().getOrderId(),
										this.getCustomerOrderKey().getChannelOrderId(), this.getStatus().getCode()),
								new DeliveryChangeSyncOutMessage.DeliveryCompleteBody(rider.getRiderAccountId(), rider.getRiderName()));

				SpringContextUtils.getBean(RiderDeliveryOrderSyncOutClient.class).asyncOut(message);
		}
	}

	public void syncOutRiderChange(StaffRider newRider, @Nullable StaffRider oldRider, ChangeRiderEventEnum changeRiderEventEnum) {
		try {
			DeliveryChangeSyncOutMessage<DeliveryChangeSyncOutMessage.DeliveryRiderChangeBody> message =
					new DeliveryChangeSyncOutMessage<>(DeliveryAsyncOutTypeEnum.DELIVERY_ACCEPT_OR_RIDER_CHANGE.getValue(),
							new DeliveryChangeSyncOutMessage.Head(this.getTenantId(),
									this.getStoreId(), this.getId(), this.getCustomerOrderKey().getOrderBizType(), this.getCustomerOrderKey().getOrderId(),
									this.getCustomerOrderKey().getChannelOrderId(), this.getStatus().getCode()),
							new DeliveryChangeSyncOutMessage.DeliveryRiderChangeBody(
									newRider.getRiderAccountId(),
									com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.TimeUtil.toMilliSeconds(LocalDateTime.now()),
									this.status.getCode(),
									changeRiderEventEnum.getCode(),
									Optional.ofNullable(oldRider).map(StaffRider::getRiderAccountId).orElse(null)
							)
					);

			SpringContextUtils.getBean(RiderDeliveryOrderSyncOutClient.class).asyncOut(message);
		} catch (Exception e) {
			log.error("syncOutAcceptAndRiderChange error");
		}
	}

	public boolean couldPostDeliveryProofPhoto() {
		if (!DeliveryRiderMccConfigUtils.getDeliveryProofPhotoSwitch()) {
			return false;
		}

		if (!DeliveryRiderMccConfigUtils.checkIsDHTenant(this.getTenantId())) {
			return false;
		}

		if (this.getCurrentStatusLocked() == 1) {
			return false;
		}

		if (this.getRiderDeliveryExtInfo() != null && CollectionUtils.isNotEmpty(this.getRiderDeliveryExtInfo().getDeliveryProofPhotoInfoList())) {
			return false;
		}


		return (this.getStatus() == DeliveryStatusEnum.RIDER_TAKEN_GOODS || this.getStatus() == DeliveryStatusEnum.DELIVERY_DONE);
	}

	private void notifySyncRiderPosition(Long deliveryId, DeliveryPlatformEnum platformEnum,Long tenantId,Long storeId){
		SpringContextUtils.getBean(RiderDeliveryOrderSyncOutClient.class).notifySyncRiderPosition(deliveryId,platformEnum,tenantId,storeId);
	}

	/**
	 * 埋点上报配送时长
	 */
	private void reportDeliveryDurationLogEvent() {
		if (this.getCustomerOrderKey() == null || this.getCustomerOrderKey().getReserved() == null || this.getCustomerOrderKey().getReserved()) {
			return;
		}
		long deliveryDuration = Duration.between(this.getTimeline().getCreateTime(), this.getTimeline().getDeliveryDoneTime()).getSeconds();
		List<Integer> metricList = DeliveryRiderMccConfigUtils.getInTimeOrderDeliveryDurationMonitorMetric();
		if (CollectionUtils.isNotEmpty(metricList) && metricList.size() >= 2) {
			Integer metric1 = metricList.get(0);
			Integer metric2 = metricList.get(1);
			if (deliveryDuration < metric1 * TimeUtil.MINUTE_SECONDS) {
				Cat.newCompletedTransactionWithDuration("DH.delivery.duration", "0-" + metric1 + "minutes", deliveryDuration);
			} else if (deliveryDuration < metric2 * TimeUtil.MINUTE_SECONDS) {
				Cat.newCompletedTransactionWithDuration("DH.delivery.duration",metric1 + "-" + metric2 + "minutes", deliveryDuration);
			} else {
				Cat.newCompletedTransactionWithDuration("DH.delivery.duration","more.than." + metric2 + "minutes", deliveryDuration);
			}
		}
	}

	/**
	 * 改派骑手
	 * @param rider 新骑手
	 * @param now 当前时间
	 * @return 操作结果
	 */
	public Result<Void> riderChange(StaffRider rider, LocalDateTime now) {
		// 判断状态
		if (this.getStatus() != DeliveryStatusEnum.RIDER_ASSIGNED && this.getStatus() != DeliveryStatusEnum.RIDER_TAKEN_GOODS) {
			return new Result<>(new Failure(false, FailureCodeEnum.DELIVERY_STATUS_ERR, this.getStatus().getDesc()));
		}

		if (isSameRider(rider)) {
			return new Result<>(new Failure(false, FailureCodeEnum.RIDER_CHANGE_SAME));
		}
		StaffRider oldRider = this.getRiderInfo();
		if (Objects.nonNull(oldRider)) {
			if (Objects.isNull(this.riderDeliveryExtInfo)) {
				this.riderDeliveryExtInfo = new RiderDeliveryExtInfo();
			}
			this.getRiderDeliveryExtInfo().setLastFromRiderAccountId(oldRider.getRiderAccountId());
			this.getRiderDeliveryExtInfo().setLastFromRiderName(oldRider.getRiderName());
		}

		this.riderInfo = rider;
		this.getTimeline().setLastEventTime(now);

		return new Result<>(null);
	}



	/**
	 * 运单取消
	 * @return
	 */
	public void cancel() {
		if (this.getStatus() == DeliveryStatusEnum.DELIVERY_CANCELLED) {
			log.info("already change, deliveryOrderId={}", this.getId());
			return;
		}
		LocalDateTime now = LocalDateTime.now();
		this.status = DeliveryStatusEnum.DELIVERY_CANCELLED;
		this.getTimeline().setLastEventTime(now);
		persist();
		recordDeliveryChangeLog(this.getId(), DeliveryStatusEnum.DELIVERY_CANCELLED, DeliveryEventEnum.CANCEL_BY_CRANE_TASK, null, now);
	}


	public Optional<Failure> updateDeliveryProofPhotoAuditStatus(List<Long> violationPicIdList, LocalDateTime auditTime) {
		if (this.getRiderDeliveryExtInfo() == null || CollectionUtils.isEmpty(this.getRiderDeliveryExtInfo().getDeliveryProofPhotoInfoList())) {
			return Optional.of(new Failure(false, SYSTEM_ERROR.getCode(), "运单扩展信息为空或送达照片列表为空"));
		}

		if(Objects.equals(this.getRiderDeliveryExtInfo().getIsAlreadyAudit(), true)) {
			log.warn("送达图片已审核, 无需处理");
			return Optional.of(new Failure(false, ORDER_NOT_SUPPORT_ERROR.getCode(), "送达图片已审核, 无需处理"));
		}

		this.getRiderDeliveryExtInfo().getDeliveryProofPhotoInfoList().forEach(
				deliveryProofPhotoInfo -> {
					if (violationPicIdList.contains(deliveryProofPhotoInfo.getPicId())) {
						deliveryProofPhotoInfo.setAuditStatusEnum(AuditStatusEnum.NOT_PASS_AUDIT);
					} else {
						deliveryProofPhotoInfo.setAuditStatusEnum(AuditStatusEnum.PASS_AUDIT);
					}
					deliveryProofPhotoInfo.setAuditTime(auditTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
				}
		);

		this.getRiderDeliveryExtInfo().setIsAlreadyAudit(true);
		return Optional.empty();
	}

	public boolean checkIsFinalStatus() {
		return Objects.equals(this.getStatus(), DeliveryStatusEnum.DELIVERY_DONE) ||
				Objects.equals(this.getStatus(), DeliveryStatusEnum.DELIVERY_CANCELLED);

	}


	public boolean isPickDeliverySplit() {
		if (Objects.nonNull(riderDeliveryExtInfo) && Objects.nonNull(riderDeliveryExtInfo.getPickDeliverySplitTag()) && riderDeliveryExtInfo.getPickDeliverySplitTag()) {
			return true;
		}
		return false;
	}
}
