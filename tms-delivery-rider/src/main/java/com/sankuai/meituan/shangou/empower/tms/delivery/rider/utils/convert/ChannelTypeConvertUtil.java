package com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.convert;

import com.meituan.shangou.saas.common.enums.ChannelTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;

@Deprecated
public class ChannelTypeConvertUtil {

    public static ChannelTypeEnum orderBizToChannelType(OrderBizTypeEnum bizTypeEnum){
        if (bizTypeEnum == null) {
            return ChannelTypeEnum.MEITUAN;
        }

        switch (bizTypeEnum) {
            case ELE_ME:
                return ChannelTypeEnum.ELEM;
            case JING_DONG:
                return ChannelTypeEnum.JD2HOME;
            case YOU_ZAN:
            case YOU_ZAN_MIDDLE:
                return ChannelTypeEnum.YOU_ZAN;
            case QUAN_QIU_WA:
                return ChannelTypeEnum.QUAN_QIU_WA;
            case SELF_PLATFORM:
                return ChannelTypeEnum.SELF_CHANNEL;
            default:
                return ChannelTypeEnum.MEITUAN;

        }
    }

}
