package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.auth;

import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.AccountInfo;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;

import java.util.List;

public interface RiderAuthClient {
    /**
     * 查询门店下所有效账号信息
     */
    List<AccountInfo> queryAccountListByPoiAndAuth(Long tenantId, Long storeId,Integer appId);

    /**
     * 查询门店下骑手的信息
     */
    List<AccountInfo> queryRiderByPoiAndRoleIds(Long tenantId, Long storeId);

    List<Long> queryRiderAccountRoleIds(Long accountId);

    AccountInfo queryRiderByAccountId(Long tenantId, Long accountId);
}
