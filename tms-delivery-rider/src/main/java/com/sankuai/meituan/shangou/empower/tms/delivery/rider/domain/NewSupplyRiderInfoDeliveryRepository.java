package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;

import java.util.Optional;

/**
 * 新供给骑手信息相关接口
 * 为了不破坏Model依赖关系，将tms-delivery-access Model里的接口和实现类在Rider Model里拷贝一份
 *
 * <AUTHOR>
 * @date 2024/5/24
 */
public interface NewSupplyRiderInfoDeliveryRepository {

    /**
     * @description: 查询骑手信息
     * @param: orderId 赋能订单ID
     * @return 骑手信息
     */
    Optional<Rider> queryRiderInfo(Long orderId);

    /**
     * @description: 保存骑手信息
     * @param: orderId 赋能订单ID
     * @param: rider 骑手信息
     * @return true表示保存成功
    */
    boolean saveRiderInfo(Long orderId, Rider rider);

}
