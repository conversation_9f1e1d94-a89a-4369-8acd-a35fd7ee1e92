package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.facade;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.ChannelStoreQueryResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantChannelStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;

import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.Coordination;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RouteInfoDTO;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.map.MapClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.map.MapRouteRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.CoordinateUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.RetryTemplateUtil;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/28
 * @email jianglilin02@meituan
 */
@Slf4j
@Service
public class RouteFacade {

    @Resource(name = "mapClientImpl")
    private MapClient mapClientImpl;

    @Resource(name = "mapCenterClientImpl")
    private MapClient mapCenterClientImpl;
    @Resource
    private TenantSystemClient tenantSystemClient;

    private static final int MEITUAN_CHANNEL_ID = 100;
    private static final int WEI_XIN_CHANNEL_ID = 700;

    //TODO:tangjianpei 合并下地图方法
    @MethodLog(logRequest = false, logResponse = true)
    public Result<Double> queryRidePathDistance(long tenantId, long storeId, CoordinatePoint destinationPoint) {
        try {
            TenantChannelStoreInfo tenantChannelStoreInfo = selectChannelStore(tenantId, storeId);
            if (Objects.isNull(tenantChannelStoreInfo)) {
                log.error("selectChannelStore return null, tenantId = {}, storeId = {}, destinationPoint = {}", tenantId, storeId ,destinationPoint);
                return new Result<>(new Failure(false, FailureCodeEnum.TENANT_SHOP_NOT_CONFIG_DELIVERY_CHANNEL_SHOP));
            }

            MapRouteRequest request = new MapRouteRequest();
            request.setOriginLongitude(tenantChannelStoreInfo.getLongitude());
            request.setOriginLatitude(tenantChannelStoreInfo.getLatitude());
            request.setDestinationLongitude(destinationPoint.getLongitude());
            request.setDestinationLatitude(destinationPoint.getLatitude());
            request.setType(DeliveryRiderMccConfigUtils.getDHTenantIdList().contains(tenantId+"") ? 1 : 2);
            request.setStoreId(storeId);
            request.setTenantId(tenantId);
            if(DeliveryRiderMccConfigUtils.getDHTenantIdList().contains(tenantId+"")) {
                try {
                    Double distanceFromOpenMap = RetryTemplateUtil.simpleWithFixedRetry(3, 100).execute(
                            new RetryCallback<Double, Exception>() {
                                @Override
                                public Double doWithRetry(RetryContext retryContext) {
                                    return mapCenterClientImpl.queryRideRidingDistance4DrunkHorse(request);
                                }
                            });

                    return new Result<>(distanceFromOpenMap);
                } catch (Exception e) {
                    log.error("get distance from open map fail", e);
                    Cat.logEvent("MAP_CENTER_CLIENT", "GET_RIDING_ROUTE_FAIL");
                    return new Result<>(calBrokenLineDistance(request));
                }
            } else {
                return mapCenterClientImpl.queryRidePathDistance(request);
            }

        } catch (Exception e) { //不影响整体流程
            log.error("queryRidePathDistance error", e);
            return new Result<>(new Failure(false, FailureCodeEnum.SYSTEM_ERROR));
        }
    }


    @MethodLog(logRequest = false, logResponse = true)
    public Optional<RouteInfoDTO> queryRideRouteInfo4DrunkHorse(long tenantId, long storeId, CoordinatePoint destinationPoint) {
        try {
            TenantChannelStoreInfo tenantChannelStoreInfo = selectChannelStore(tenantId, storeId);
            if (Objects.isNull(tenantChannelStoreInfo)) {
                log.error("selectChannelStore return null, tenantId = {}, storeId = {}, destinationPoint = {}", tenantId, storeId, destinationPoint);
                return Optional.empty();
            }

            MapRouteRequest request = new MapRouteRequest();
            request.setOriginLongitude(tenantChannelStoreInfo.getLongitude());
            request.setOriginLatitude(tenantChannelStoreInfo.getLatitude());
            request.setDestinationLongitude(destinationPoint.getLongitude());
            request.setDestinationLatitude(destinationPoint.getLatitude());
            request.setType(DeliveryRiderMccConfigUtils.getDHTenantIdList().contains(tenantId+"") ? 1 : 2);
            request.setStoreId(storeId);
            request.setTenantId(tenantId);
            try {
                RouteInfoDTO routeInfoDTO = RetryTemplateUtil.simpleWithFixedRetry(3, 100).execute(
                        new RetryCallback<RouteInfoDTO, Exception>() {
                            @Override
                            public RouteInfoDTO doWithRetry(RetryContext retryContext) {
                                return mapCenterClientImpl.queryRideRidingInfo4DrunkHorse(request);
                            }
                        });

                return Optional.of(routeInfoDTO);
            } catch (Exception e) {
                log.error("get distance from open map fail", e);
                Cat.logEvent("MAP_CENTER_CLIENT", "GET_RIDING_ROUTE_FAIL");
                Double brokenLineDistance = calBrokenLineDistance(request);
                RouteInfoDTO brokenLineRouteInfoDTO = new RouteInfoDTO(brokenLineDistance, null, null,
                        new Coordination(request.getOriginLongitude(), request.getOriginLatitude()),
                        new Coordination(request.getDestinationLongitude(), request.getDestinationLatitude()), calNavigationDuration(brokenLineDistance));
                return Optional.of(brokenLineRouteInfoDTO);
            }

        } catch (Exception e) { //不影响整体流程
            log.error("queryRidePathDistance error", e);
            Cat.logEvent("MAP_CENTER_CLIENT", "GET_RIDING_ROUTE_FAIL");
            return Optional.empty();
        }
    }

    public Double calBrokenLineDistance(MapRouteRequest req) {
        CoordinatePoint sourcePoint = new CoordinatePoint(req.getOriginLongitude(), req.getOriginLatitude());
        CoordinatePoint targetPoint = new CoordinatePoint(req.getDestinationLongitude(), req.getDestinationLatitude());
        Long lineDistance = CoordinateUtil.calLineDistance(sourcePoint, targetPoint);

        return lineDistance.doubleValue() * DeliveryRiderMccConfigUtils.getBrokenLineCoefficient();
    }

    /**
     * 计算两个高德坐标点之间的导航距离 单位:米
     */
    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public Result<Double> calNavigationDistance(CoordinatePoint originPoint, CoordinatePoint destinationPoint) {
        if (originPoint == null || destinationPoint == null) {
            return new Result<>(new Failure(false, FailureCodeEnum.INVALID_PARAM));
        }

        try {
            MapRouteRequest request = new MapRouteRequest();
            request.setOriginLongitude(originPoint.getLongitude());
            request.setOriginLatitude(originPoint.getLatitude());
            request.setDestinationLongitude(destinationPoint.getLongitude());
            request.setDestinationLatitude(destinationPoint.getLatitude());
            request.setType(1);
            if (DeliveryRiderMccConfigUtils.isMigrateMapPlatformGrayStore(0L)) {
               return mapCenterClientImpl.queryRidePathDistance(request);
            } else {
                return mapClientImpl.queryRidePathDistance(request);
            }

        } catch (Exception e) { //不影响整体流程
            log.error("calNavigationDistance error", e);
            return new Result<>(new Failure(false, FailureCodeEnum.SYSTEM_ERROR));
        }

    }

    /**
     * 按照业务逻辑，优先取美团渠道经纬度，若美团渠道为空，则取微商城渠道经纬度
     * 注：这里引用不到ChannelType
     */
    private TenantChannelStoreInfo selectChannelStore(long tenantId, long storeId) {

        if(DeliveryRiderMccConfigUtils.getDHTenantIdList().contains(tenantId+"")){

            ChannelStoreQueryResult channelStoreQueryResult = tenantSystemClient.queryChannelStoreDetailInfo(tenantId, storeId);
            if (!channelStoreQueryResult.isSuccess()) {
                log.error("invoke tenantSystemClient.queryChannelStoreDetailInfo error, result = {}", channelStoreQueryResult);
                throw new BizException("invoke tenantSystemClient.queryChannelStoreDetailInfo error");
            }

            Map<Integer, TenantChannelStoreInfo> channelIdStoreInfoMap = Optional
                    .ofNullable(channelStoreQueryResult.getStoreInfos())
                    .orElse(Lists.newArrayList())
                    .stream()
                    .collect(
                            Collectors.toMap(
                                    TenantChannelStoreInfo::getChannelId,
                                    Function.identity()
                            )
                    );
            return Optional
                    .ofNullable(channelIdStoreInfoMap.get(MEITUAN_CHANNEL_ID))
                    .orElse(channelIdStoreInfoMap.get(WEI_XIN_CHANNEL_ID));
        }

        Optional<TenantChannelStoreInfo> storeInfoOptional = tenantSystemClient.queryChannelStoreDetailInfoWithAnyChannel(tenantId,storeId,null);
        if(storeInfoOptional.isPresent()){
            return storeInfoOptional.get();
        }
        return null;
    }





    /**
     * 计算定价路线时间
     * distance 距离 单位：米
     */
    public static Double calNavigationDuration(Double distance) {
        if (distance == null) {
            return 0d;
        }

        //电动车速度=17km/h = 4.72m/s
        double electricBicycleSpeed = 4.72;
        return distance / electricBicycleSpeed;
    }
}
