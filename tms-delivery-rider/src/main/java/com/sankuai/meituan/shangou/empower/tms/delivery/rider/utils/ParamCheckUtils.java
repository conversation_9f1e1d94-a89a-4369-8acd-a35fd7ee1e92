package com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils;

import com.dianping.cat.util.StringUtils;

public class ParamCheckUtils {

    public ParamCheckUtils() {
    }

    public static void emptyCheck(String param, String errorMsg) {
        if (StringUtils.isEmpty(param)) {
            throw new IllegalArgumentException(errorMsg);
        }
    }

    public static void nullCheck(Object param, String errorMsg) {
        if (param == null) {
            throw new IllegalArgumentException(errorMsg);
        }
    }

    public static void pageNoCheck(int pageNo) {
        if (pageNo < 1) {
            throw new IllegalArgumentException("pageNo must >= 1");
        }
    }

    public static void pageSizeCheck(int pageSize) {
        if (pageSize < 1) {
            throw new IllegalArgumentException("pageSize must >= 1");
        }
    }
}
