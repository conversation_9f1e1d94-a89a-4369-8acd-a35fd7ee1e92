package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.DeliveryBusyLevelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.PushMessageTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023/1/11 15:48
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DeliveryCapacityLoadRecordDO {
    private Long storeId;

    private DeliveryBusyLevelEnum deliveryBusyLevel;

    private PushMessageTypeEnum messageType;

    private Integer onDutyEmployeeCount;

    private Integer deliveringOrderCount;

    private LocalDateTime recordTime;

    private PushMessageTypeEnum latestPushMessageRecordType;
}
