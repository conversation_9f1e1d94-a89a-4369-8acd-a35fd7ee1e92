package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.strategy;


import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.cmd.MergeOrderOperateCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.leaf.LeafClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2022/9/7 17:01
 **/
@Slf4j
@Component
public class MergeOrderRecordAddStrategy implements MergeOrderOperationCreateStrategy {
    @Resource
    private RiderDeliveryOrderRepository riderDeliveryOrderRepository;

    @Resource
    private RiderDeliveryMergeOrderRecordRepository riderDeliveryMergeOrderRecordRepository;

    @Resource
    private LeafClient leafClient;

    public List<MergeOrderOperateCmd> buildMergeOrderOperateCmd(RiderDeliveryOrder riderDeliveryOrder) {
        //获取骑手配送中的运单Map
        Map<Long, RiderDeliveryOrder> deliveringOrderMap = riderDeliveryOrderRepository.queryRiderDeliveringOrderMap(
                riderDeliveryOrder.getStoreId(),
                riderDeliveryOrder.getRiderInfo().getRiderAccountId(),riderDeliveryOrder.getTenantId());

        //没有同时配送的运单 ==> 无需插入并单记录
        if (deliveringOrderMap.isEmpty()) {
            return Collections.emptyList();
        }

        //需要合并的运单组合:配送中运单+该运单
        HashMap<Long, RiderDeliveryOrder> allDeliveryOrderMap = new HashMap<>(deliveringOrderMap);
        allDeliveryOrderMap.put(riderDeliveryOrder.getId(), riderDeliveryOrder);

        //获取并单id与运单id的关联关系
        Map<Long, Set<Long>> mergeRecordMap = riderDeliveryMergeOrderRecordRepository.queryRiderMergeOrderRecordMap(
                riderDeliveryOrder.getRiderInfo().getRiderAccountId(),
                allDeliveryOrderMap.keySet()
        );

        //判断并单表中是否已经包含需要合并的运单组合
        boolean isMergedDeliveryOrderContainCurrentDeliveryOrder = mergeRecordMap.values().stream()
                .anyMatch(deliveryIdList -> deliveryIdList.containsAll(allDeliveryOrderMap.keySet()));

        //包含，则说明该运单组合已经插入过了，无需再次插入
        if (isMergedDeliveryOrderContainCurrentDeliveryOrder) {
            return Collections.emptyList();
        }

        //不包含，生成并单id 插入该并单记录
        Long mergeId = leafClient.nextSnowflakeId();

        MergeOrderOperateCmd orderOperateCmd = new MergeOrderOperateCmd(mergeId, riderDeliveryOrder.getTenantId(),
                riderDeliveryOrder.getRiderInfo(),
                new ArrayList<>(allDeliveryOrderMap.values()));

        return Collections.singletonList(orderOperateCmd);
    }

}
