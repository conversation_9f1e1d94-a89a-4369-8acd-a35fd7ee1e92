package com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.cmd;

import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.StaffRider;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@AllArgsConstructor
@Getter
public class MergeOrderOperateCmd {

    private Long mergeId;

    private Long tenantId;

    private StaffRider courier;

    private List<RiderDeliveryOrder> riderDeliveryOrders;
}
