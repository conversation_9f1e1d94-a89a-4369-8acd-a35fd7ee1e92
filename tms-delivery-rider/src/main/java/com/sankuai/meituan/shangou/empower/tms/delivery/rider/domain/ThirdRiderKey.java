package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-09-13
 * @email <EMAIL>
 */
@Data
@NoArgsConstructor
public class ThirdRiderKey {

    private String name;

    private String phone;

    public ThirdRiderKey(String name, String phone) {
        this.name = name;
        this.phone = phone;
    }

    public String toKey() {
        return name + phone;
    }
}
