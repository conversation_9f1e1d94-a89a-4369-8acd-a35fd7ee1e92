package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.RiderLocatingExceptionEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 骑手定位异常
 */
@Data
@NoArgsConstructor
public class RiderLocatingExceptionDetail {
    /**
     *  骑手账号
     */
    private Long riderAccount;

    /**
     * 骑手设备id
     */
    private String uuid;

    /**
     * 定位异常枚举值
     */
    private RiderLocatingExceptionEnum riderLocatingExceptionEnum;

    /**
     * 前端上报时间
     */
    private Long utime;

    /**
     * 手机操作系统
     */
    private String phoneOS;

    /**
     * 手机厂商
     */
    private String manufacturer;
}
