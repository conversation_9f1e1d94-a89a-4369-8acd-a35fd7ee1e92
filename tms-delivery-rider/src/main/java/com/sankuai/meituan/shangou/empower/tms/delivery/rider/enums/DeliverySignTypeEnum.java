package com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums;

/**
 * <AUTHOR>
 * @since 2023/12/19 16:49
 **/
public enum DeliverySignTypeEnum {
    FACE_TO_FACE_SIGN(1, "当面签收"),
    PLACE_IN_ASSIGNED_POINT(2, "放在指定地点"),

    OTHER(3, "非自配骑手点送达"),
    ;
    private int type;

    private String desc;

    DeliverySignTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static DeliverySignTypeEnum enumOf(int type) {
        for (DeliverySignTypeEnum val : DeliverySignTypeEnum.values()) {
            if (val.getType() == type) {
                return val;
            }
        }

        throw new IllegalArgumentException("不识别的签收方式");
    }
    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
