package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.AuditStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/27 11:50
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiderDeliveryExtInfo {
    private Integer actualPayAmt;

    private Boolean businessToAggregationDelivery;

    private List<DeliveryProofPhotoInfo> deliveryProofPhotoInfoList;

    private Boolean isAlreadyAudit;

    private Long userId;

    private Integer orderGoodsCount;

    private Integer signType;

    private Boolean isWeakNetwork;

    private Integer transType;

    private String signPosition;

    private Integer proofPhotoCount;

    private Boolean isOneYuanOrder;

    /**
     * 考核配送时长
     */
    private Long assessDeliveryTime;

    /**
     * 是否拣配分离
     */
    private Boolean pickDeliverySplitTag;

    /**
     * 转单人
     */
    private String lastFromRiderName;
    private Long lastFromRiderAccountId;

    @AllArgsConstructor
    @Data
    public static class DeliveryProofPhotoInfo {
        private String photoUrl;

        private Long picId;

        private AuditStatusEnum auditStatusEnum;

        private String auditTime;
    }
}
