package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.PageQueryDeliveryExceptionRequest;

import javax.annotation.Resource;
import java.util.List;

public interface RiderDeliveryExceptionRepository {
    void save(RiderDeliveryException riderDeliveryException);

    PageResult<RiderDeliveryException> pageQueryRiderDeliveryException(RiderDeliveryExceptionPageQueryConditions conditions);

    List<RiderDeliveryException> queryDeliveryExceptionByChannelOrderIds(Long tenantId, Long storeId, List<String> channelOrderIds);

    List<RiderDeliveryException> queryDeliveryExceptionByChannelOrderId(Long tenantId, Long storeId, String channelOrderId, Integer orderBizTye);

    long countDeliveryException(RiderDeliveryExceptionPageQueryConditions conditions);
}
