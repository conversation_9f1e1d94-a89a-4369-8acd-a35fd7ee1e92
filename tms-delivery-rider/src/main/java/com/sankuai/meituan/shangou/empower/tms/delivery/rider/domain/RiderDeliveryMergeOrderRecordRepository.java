package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface RiderDeliveryMergeOrderRecordRepository {

    boolean upsert(RiderDeliveryMergeOrderRecord mergeOrder);

    void batchUpsert(List<RiderDeliveryMergeOrderRecord> mergeOrders);

    boolean remove(Long mergeId, Long deliveryOrderId);

    void batchRemove(List<Pair<Long, Long>> mergeIdAndOrderIdPairList);

    void batchUpsertAndRemove(List<RiderDeliveryMergeOrderRecord> mergeOrders, List<Pair<Long, Long>> mergeIdAndOrderIdPairList);

    List<RiderDeliveryMergeOrderRecord> queryByAccountIdAndDeliveryIds(Long accountId, Set<Long> deliveryIds);

    List<RiderDeliveryMergeOrderRecord> queryByMergeId(Long mergeId);

    List<RiderDeliveryMergeOrderRecord> queryByMergeIds(List<Long> mergeIds);

    Map<Long,Set<Long>> queryRiderMergeOrderRecordMap(Long accountId, Set<Long> deliveryIds);
}
