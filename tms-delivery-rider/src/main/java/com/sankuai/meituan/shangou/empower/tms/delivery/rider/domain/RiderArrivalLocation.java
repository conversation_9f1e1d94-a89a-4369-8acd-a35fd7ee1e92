package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderArrivalLocationTRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils.MccUtils;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.SpringContextUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.facade.RouteFacade;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.CoordinateUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021/12/27
 */

@Data
@Builder
@AllArgsConstructor
public class RiderArrivalLocation {
    /**
     * 运单id
     */
    private final Long deliveryOrderId;

    /**
     * 骑手账号id
     */
    private final Long riderAccountId;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 与收货地址之间的直线距离 单位:米
     */
    private Long lineDistance;

    /**
     * 与收货地址之间的导航距离 单位:米
     */
    private Long navigationDistance;

    @FieldDoc(
            description = "定位结果来源", requiredness = Requiredness.OPTIONAL
    )
    public String provider;

    @FieldDoc(
            description = "定位结果精确度", requiredness = Requiredness.OPTIONAL
    )
    public String accuracy;

    @FieldDoc(
            description = "方向信息", requiredness = Requiredness.OPTIONAL
    )

    public String bearing;

    @FieldDoc(
            description = "速度信息", requiredness = Requiredness.OPTIONAL
    )
    public String speed;

    @FieldDoc(
            description = "时间信息", requiredness = Requiredness.REQUIRED
    )
    public String time;

    @FieldDoc(
            description = "操作系统", requiredness = Requiredness.OPTIONAL
    )
    public String os;

    @FieldDoc(
            description = "位置是否来自后端缓存", requiredness = Requiredness.OPTIONAL
    )
    public Boolean isCacheLocation;

    public RiderArrivalLocation(Long deliveryOrderId, Long riderAccountId) {
        this.deliveryOrderId = deliveryOrderId;
        this.riderAccountId = riderAccountId;
    }

    /**
     * 检查经纬度信息是否完整
     * @return
     */
    public Boolean missingLocationInfo() {
        return StringUtils.isBlank(latitude) || StringUtils.isBlank(longitude);
    }

    /**
     * 填充到刻位置
     * @param request
     * @return
     */
    public void fillRiderCoordinatePoint(RiderArrivalLocationTRequest request) {
        this.longitude = request.getLongitude();
        this.latitude = request.getLatitude();

        if(MccUtils.getArrivalLocationSwitch()) {
            if (Objects.nonNull(request.getLocationInfo()) && StringUtils.isNotBlank(request.getLocationInfo().getLatitude()) && StringUtils.isNotBlank(request.getLocationInfo().getLongitude())) {
                RiderArrivalLocationTRequest.LocationInfo locationInfo = request.getLocationInfo();
                this.longitude = locationInfo.getLongitude();
                this.latitude = locationInfo.getLatitude();
                this.accuracy = locationInfo.getAccuracy();
                this.bearing = locationInfo.getBearing();
                this.provider = locationInfo.getProvider();
                this.speed = locationInfo.getSpeed();
                this.os = locationInfo.getOs();
                this.time = locationInfo.getTime();
                this.isCacheLocation = false;
            } else {
                RiderLocationDetail riderLocation = SpringContextUtils.getBean(RiderLocationRepository.class).getStaffRiderLocation(request.getRiderAccountId());
                if (riderLocation != null) {
                    this.longitude = riderLocation.getLongitude();
                    this.latitude = riderLocation.getLatitude();
                    this.accuracy = riderLocation.getAccuracy();
                    this.bearing = riderLocation.getBearing();
                    this.provider = riderLocation.getProvider();
                    this.speed = riderLocation.getSpeed();
                    this.os = riderLocation.getOs();
                    this.time = riderLocation.getTime();
                    this.isCacheLocation = true;
                }
            }

            return;
        }

        // 如果前端sdk没获取到位置信息,填充最近一次记录到的骑手位置
        if (StringUtils.isBlank(request.getLongitude()) || StringUtils.isBlank(request.getLatitude())) {
            RiderLocationDetail riderLocation = SpringContextUtils.getBean(RiderLocationRepository.class).getStaffRiderLocation(request.getRiderAccountId());

            if (riderLocation != null){
                this.longitude = riderLocation.getLongitude();
                this.latitude = riderLocation.getLatitude();
            }
        }
    }

    /**
     * 计算导航距离 单位:米
     * @param receiverCoordinatePoint
     * @return
     */
    public void calNavigationDistanceToReceiver(CoordinatePoint receiverCoordinatePoint) {
        if (receiverCoordinatePoint == null || StringUtils.isBlank(this.latitude) || StringUtils.isBlank(this.longitude)) {
            return;
        }

        CoordinatePoint riderCoordinatePoint = new CoordinatePoint(this.getLongitude(),this.getLatitude());

        Result<Double> navigationDistanceResult = SpringContextUtils.getBean(RouteFacade.class)
                .calNavigationDistance(riderCoordinatePoint, receiverCoordinatePoint);
        if (navigationDistanceResult.isSuccess() && navigationDistanceResult.getInfo() != null) {
            this.navigationDistance =  navigationDistanceResult.getInfo().longValue();
        }
    }

    /**
     * 计算直线距离 单位:米
     * @return
     */
    public void calLineDistanceToReceiver(CoordinatePoint receiverCoordinatePoint) {
        if (receiverCoordinatePoint == null || StringUtils.isBlank(this.latitude) || StringUtils.isBlank(this.longitude)) {
            return;
        }

        CoordinatePoint riderCoordinatePoint = new CoordinatePoint(this.getLongitude(),this.getLatitude());

        this.lineDistance = CoordinateUtil.calLineDistance(riderCoordinatePoint,receiverCoordinatePoint);

    }

}
