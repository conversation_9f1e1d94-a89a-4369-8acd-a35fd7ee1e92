package com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.chrono.ChronoZonedDateTime;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/12/8
 * @Desc  从com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil复制过来的时间工具类
 */
@Slf4j
public class TimeUtil {

	public static LocalDateTime getEpochTime() {
		return Instant.EPOCH.atOffset(ZoneOffset.UTC).toLocalDateTime();
	}

	public static LocalDateTime fromMilliSeconds(Long milliseconds) {
		if (milliseconds == null) {
			return null;
		}
		return Instant.ofEpochMilli(milliseconds).atZone(ZoneId.systemDefault()).toLocalDateTime();
	}

	public static LocalDateTime fromSeconds(Long seconds) {
		if (seconds == null) {
			return null;
		}
		return Instant.ofEpochSecond(seconds).atZone(ZoneId.systemDefault()).toLocalDateTime();
	}

	public static Long toMilliSeconds(LocalDateTime time) {
		if (time == null) {
			return null;
		}

		return time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
	}

	public static Optional<Integer> toSeconds(LocalDateTime time) {
		return Optional.ofNullable(time)
				.map(it -> it.atZone(ZoneId.systemDefault()))
				.map(ChronoZonedDateTime::toEpochSecond)
				.map(Math::toIntExact);
	}

	public static Long toMilliSeconds(String dateTimeStr) {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			return simpleDateFormat.parse(dateTimeStr).getTime();
		} catch (ParseException e) {
			log.error("时间字符串解析失败, dateTimeStr: {}",dateTimeStr);
			return null;
		}
	}
}
