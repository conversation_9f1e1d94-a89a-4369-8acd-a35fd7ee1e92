package com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-07
 * @email <EMAIL>
 */
public enum PositionPushConfigEnum {

    DELIVERY_DONE_TIMEOUT("配送超时发大象", "done.timeout.positions", Lists.newArrayList("主管", "城市经理", "店长", "副店长"), Lists.newArrayList("加盟-店长", "加盟-副店长")),
    DELIVERY_PAUSE("配送暂停发push", "delivery.pause.positions", Lists.newArrayList("店长", "副店长"), Lists.newArrayList("加盟-店长", "加盟-副店长")),
    ORDER_OVERLOAD("爆单提醒发大象", "order.overload.positions", Lists.newArrayList("主管", "城市经理"), Lists.newArrayList()),
    ;

    private String desc;
    private String nameKey;
    private List<String> defaultDirectNameList;
    private List<String> defaultFranchiseeDirectNameList;

    PositionPushConfigEnum(String desc, String nameKey, List<String> defaultDirectNameList, List<String> franchiseeDirectNameList) {
        this.desc = desc;
        this.nameKey = nameKey;
        this.defaultDirectNameList = defaultDirectNameList;
        this.defaultFranchiseeDirectNameList = franchiseeDirectNameList;
    }

    public String getNameKey() {
        return nameKey;
    }

    public List<String> getDefaultDirectNameList() {
        return defaultDirectNameList;
    }

    public List<String> getDefaultFranchiseeDirectNameList() {
        return defaultFranchiseeDirectNameList;
    }
}
