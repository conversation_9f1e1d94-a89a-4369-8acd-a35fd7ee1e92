package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationDetail;
import org.apache.thrift.TException;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/12
 */
public interface RiderDeliveryOrderSyncOutClient {

	void syncToOrderSystem(RiderDeliveryOrder deliveryOrder, LocalDateTime changeTime, RiderLocationDetail riderLocationDetail);

	/**
	 * 异步通过kafka将运单变更同步出去
	 */
	void asyncOut(DeliveryChangeSyncOutMessage message);

	void notifySyncRiderPosition(Long deliveryId, DeliveryPlatformEnum platformEnum,Long tenantId,Long storeId);

	void updateDeliveryProofPhoto(RiderDeliveryOrder deliveryOrder, CoordinatePoint coordinatePoint) throws TException;

	/**
	 * 同步渠道骑手信息变更
	 */
	void syncRiderInfoChange(RiderDeliveryOrder deliveryOrder, Rider rider, String channelOrderId);
}
