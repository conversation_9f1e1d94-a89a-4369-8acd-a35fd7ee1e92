package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.map;

import lombok.AllArgsConstructor;
import lombok.Data;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 * @date 2021/12/27
 * @email jianglilin02@meituan
 */
@Data
public class MapRouteRequest {

    /**
     * 起点经度
     */
    private String originLongitude;
    /**
     * 起点纬度
     */
    private String originLatitude;

    /**
     * 终点经度
     */
    private String destinationLongitude;
    /**
     * 终点纬度
     */
    private String destinationLatitude;

    /**
     * 1歪马 2新供给
     */
    private Integer type = 1;

    @Nullable
    private Long storeId;

    private Long tenantId;

}
