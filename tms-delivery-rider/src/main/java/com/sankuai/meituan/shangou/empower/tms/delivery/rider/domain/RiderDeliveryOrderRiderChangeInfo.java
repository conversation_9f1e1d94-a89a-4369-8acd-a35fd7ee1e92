package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import lombok.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/12
 */
@Getter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class RiderDeliveryOrderRiderChangeInfo extends RiderDeliveryOrderChangeInfo {

	private StaffRider oldRider;

	private StaffRider newRider;

	private DeliveryStatusEnum oldDeliveryStatus;

	public RiderDeliveryOrderRiderChangeInfo(StaffRider oldRider, StaffRider newRider) {
		this.oldRider = oldRider;
		this.newRider = newRider;
	}
}
