package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.log;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrderChangeInfo;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/12
 */
public interface RiderDeliveryOrderLogClient {

	/**
	 * 记录运单变更流水(发送MQ, 消费者异步写入)
	 */
	void logDeliveryChange(Long deliveryId, DeliveryStatusEnum status, DeliveryEventEnum deliveryEvent, RiderDeliveryOrderChangeInfo changeInfo, LocalDateTime changeTime);

	/**
	 * 记录运单变更流水(同步写入数据库)
	 */
	void logDeliveryChangeSyncly(Long deliveryId, DeliveryStatusEnum status, DeliveryEventEnum deliveryEvent, RiderDeliveryOrderChangeInfo changeInfo, LocalDateTime changeTime);
}
