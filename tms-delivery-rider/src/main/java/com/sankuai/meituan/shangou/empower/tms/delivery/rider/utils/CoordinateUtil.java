package com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils;


import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2021/12/8
 * @Desc 从com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.CoordinateUtil复制过来的坐标工具类
 */
public class CoordinateUtil {

	private static final double X_PI = Math.PI * 3000.0 / 180.0;
	private final static double EARTH_RADIUS = 6378137.0; //地球半径 单位:米


	public static CoordinatePoint translateToCoordinatePoint(double longitude, double latitude) {
		return new CoordinatePoint(translateToStandardStyle(String.valueOf(longitude)), translateToStandardStyle(String.valueOf(latitude)));
	}

	public static CoordinatePoint translateToCoordinatePoint(String longitude, String latitude) {
		return new CoordinatePoint(translateToStandardStyle(longitude), translateToStandardStyle(latitude));
	}

	public static String translateToStandardStyle(String value) {
		BigDecimal bigDecimal = new BigDecimal(value);
		if (bigDecimal.scale() == 0) {
			return bigDecimal.scaleByPowerOfTen(-6).toString();
		} else {
			return value;
		}
	}

	public static Integer translateToIntStyle(String standardValue) {
		BigDecimal bigDecimal = new BigDecimal(standardValue);
		if (bigDecimal.scale() == 0) {
			return bigDecimal.intValue();
		} else {
			return bigDecimal.scaleByPowerOfTen(6).intValue();
		}
	}

	/**
	 * 百度坐标系转成高德坐标系
	 */
	public static CoordinatePoint translateFromBaiduToMars(CoordinatePoint sourcePoint) {
		Preconditions.checkNotNull(sourcePoint, "sourcePoint is null");

		double x = Double.parseDouble(sourcePoint.getLongitude()) - 0.0065;
		double y = Double.parseDouble(sourcePoint.getLatitude()) - 0.006;
		double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * X_PI);
		double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI);

		double longitude = BigDecimal.valueOf(z * Math.cos(theta)).setScale(6, RoundingMode.HALF_UP).doubleValue();
		double latitude = BigDecimal.valueOf(z * Math.sin(theta)).setScale(6, RoundingMode.HALF_UP).doubleValue();
		return new CoordinatePoint(String.valueOf(longitude), String.valueOf(latitude));
	}

	/**
	 * 计算高德坐标系下两个点之间的直线距离 单位：米
	 */
	public static Long calLineDistance(CoordinatePoint sourcePoint, CoordinatePoint targetPoint) {
		if (sourcePoint == null || targetPoint == null) {
			return null;
		}
		double lat1 = Double.parseDouble(sourcePoint.getLatitude());
		double lat2 = Double.parseDouble(targetPoint.getLatitude());
		double lng1 = Double.parseDouble(sourcePoint.getLongitude());
		double lng2 = Double.parseDouble(targetPoint.getLongitude());

		double radLat1 = rad(lat1);
		double radLat2 = rad(lat2);
		double a = radLat1 - radLat2;
		double b = rad(lng1) - rad(lng2);
		double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
				Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
		s = s * EARTH_RADIUS;
		return new BigDecimal(s).setScale(0,RoundingMode.HALF_UP).longValue();

	}

	private static double rad(double d) {
		return d * Math.PI / 180.0;
	}

}
