package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.Map;

@Getter
@AllArgsConstructor
public class RiderDeliveryMergeOrderRecord {

    private Long id;

    private Long deliveryOrderId;

    private Long mergeId;

    private Long activeStatus;

    private String channelOrderId;

    private Integer orderBizType;

    private Long riderAccountId;

    private Map<String, Object> extInfoMap;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
