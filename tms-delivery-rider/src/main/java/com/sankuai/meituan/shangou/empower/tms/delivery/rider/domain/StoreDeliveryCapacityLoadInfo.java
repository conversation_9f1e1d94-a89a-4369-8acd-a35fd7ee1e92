package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.DeliveryBusyLevelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.DeliveryCapacityLoadRecordSequenceEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/1/16 20:44
 **/
@NoArgsConstructor
@Data
public class StoreDeliveryCapacityLoadInfo {

    private Map<Long/*storeId*/, Integer> storeDeliveringOrderCountMap;

    private Map<Long/*storeId*/, Integer> onDutyEmployeeCount;

    private Map<Long/*storeId*/, Float> storeDeliveryCapacityLoadMap;

    private Map<Long/*storeId*/, DeliveryBusyLevelEnum> storeBusyLevelMap;

    Map<Long/*storeId*/, Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO>> capacityRecordMap;

    public StoreDeliveryCapacityLoadInfo(@NotNull Map<Long/*storeId*/, Integer> storeDeliveringOrderCountMap,
                                         @NotNull Map<Long/*storeId*/, Integer> onDutyEmployeeCount,
                                         @NotNull Map<Long/*storeId*/, Float> storeDeliveryCapacityLoadMap,
                                         @NotNull Map<Long/*storeId*/, DeliveryBusyLevelEnum> storeBusyLevelMap,
                                         @NotNull Map<Long/*storeId*/, Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO>> capacityRecordMap) {
        this.storeDeliveringOrderCountMap = storeDeliveringOrderCountMap;
        this.onDutyEmployeeCount = onDutyEmployeeCount;
        this.storeDeliveryCapacityLoadMap = storeDeliveryCapacityLoadMap;
        this.storeBusyLevelMap = storeBusyLevelMap;
        this.capacityRecordMap = capacityRecordMap;
    }
}
