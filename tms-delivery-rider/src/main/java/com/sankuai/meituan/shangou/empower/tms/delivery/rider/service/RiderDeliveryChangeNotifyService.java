package com.sankuai.meituan.shangou.empower.tms.delivery.rider.service;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;

/**
 * <AUTHOR>
 * @date 2025-01-09
 * @email <EMAIL>
 */
public interface RiderDeliveryChangeNotifyService {

    /**
     * 及时骑手位置同步
     */
    void notifySyncDrunkHorseRiderPositionImmediately(Long deliveryId, DeliveryPlatformEnum platformEnum, Long tenantId, Long storeId, boolean feDirectSyncRiderPosition);


}
