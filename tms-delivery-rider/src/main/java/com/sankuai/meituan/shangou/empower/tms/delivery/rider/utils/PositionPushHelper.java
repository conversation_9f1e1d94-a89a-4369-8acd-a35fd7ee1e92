package com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.PositionPushConfigEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-11-06
 * @email <EMAIL>
 */
@Slf4j
public class PositionPushHelper {

    private PositionPushHelper() {

    }

    private static final String FRANCHISEE_LION_KEY = ".franchisee";

    private static final int FRANCHISEE_OPERATION_MODE = 2;

    //寄居到DMP去，以后这里可以直接删了
    private static final String LION_APP_KEY = "com.sankuai.shangou.logistics.dmp";

    public static List<Long> getPositionIds(PoiInfoDto poiInfoDto, PositionPushConfigEnum positionPushConfigEnum) {
        if (Objects.isNull(poiInfoDto)) {
            return Lists.newArrayList();
        }
        boolean isFranchisee = isFranchisee(poiInfoDto);

        if (!isFranchisee) {
            return getPositionIdsByName(Lion.getConfigRepository(LION_APP_KEY).getList(positionPushConfigEnum.getNameKey(), String.class, positionPushConfigEnum.getDefaultDirectNameList()));
        } else {
            return getPositionIdsByName(Lion.getConfigRepository(LION_APP_KEY).getList(positionPushConfigEnum.getNameKey() + FRANCHISEE_LION_KEY, String.class, positionPushConfigEnum.getDefaultFranchiseeDirectNameList()));
        }
    }





    /**
     * 根据岗位中文名查岗位id列表
     * @param positionNames 岗位中文名，建议配到lion上
     * @return 岗位id列表
     */
    private static List<Long> getPositionIdsByName(List<String> positionNames) {
        if (CollectionUtils.isEmpty(positionNames)) {
            return Lists.newArrayList();
        }
        Map<String, List<Long>> dhPositionMap = getDHPositionMap();
        if (MapUtils.isEmpty(dhPositionMap)) {
            return Lists.newArrayList();
        }

        return positionNames.stream()
                .map(dhPositionMap::get)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    private static Map<String, List<Long>> getDHPositionMap() {
        try {
            String positionMapJson = Lion.getConfigRepository(LION_APP_KEY).get("position.push.map", StringUtils.EMPTY);
            return JSON.parseObject(positionMapJson, new TypeReference<Map<String, List<Long>>>(){});
        } catch (Exception e) {
            log.error("getDHPositionMap error", e);
            return Maps.newHashMap();
        }
    }



    private static boolean isFranchisee(PoiInfoDto poiInfoDto) {
        return Objects.nonNull(poiInfoDto.getPoiExtendContentDto())
                && Objects.nonNull(poiInfoDto.getPoiExtendContentDto().getOperationMode())
                && Objects.equals(poiInfoDto.getPoiExtendContentDto().getOperationMode(), FRANCHISEE_OPERATION_MODE);
    }

    public static Boolean getDHByPositionSwitch(long tenantId, long storeId) {
        if (!DeliveryRiderMccConfigUtils.checkIsDHTenant(tenantId)) {
            return false;
        }
        try {
            List<Long> stores = Lion.getConfigRepository(LION_APP_KEY).getList("position.push.stores", Long.class, Lists.newArrayList());
            //全量逻辑
            if (stores.size() == 1 && stores.get(0).equals(-1L)) {
                return true;
            }
            return stores.contains(storeId);
        } catch (Exception e) {
            log.error("getDHByPositionSwitch error", e);
            return false;
        }
    }

}
