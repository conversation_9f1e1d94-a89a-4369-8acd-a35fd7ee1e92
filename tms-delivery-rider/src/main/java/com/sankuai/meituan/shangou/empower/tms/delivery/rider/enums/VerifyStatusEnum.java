package com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums;

/**
 * <AUTHOR>
 * @since 2023/5/5 16:52
 **/
public enum VerifyStatusEnum {
    NOT_HIT_ANY_VIOLATION_LABEL(2 , "未命中任何违规标签"),

    HIT_VIOLATION_LABEL(3, "命中或疑似命中了违规标签");

    private int code;

    private String desc;

    VerifyStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static VerifyStatusEnum enumOf(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("code is null");
        }

        for (VerifyStatusEnum val : values()) {
            if (val.getCode() == code) {
                return val;
            }
        }

        throw new IllegalArgumentException("无对应枚举");
    }
}
