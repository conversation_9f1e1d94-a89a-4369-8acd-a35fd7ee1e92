package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.PricingRouteInfoDO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/6 17:06
 **/
public interface PricingRouteInfoRepository {
    /**
     * 通过运单id查询定价路线信息
     * @param tenantId
     * @param deliveryOrderIds
     * @return
     */
    List<PricingRouteInfoDO> queryByDeliveryOrderIds(Long tenantId, List<Long> deliveryOrderIds);

    /**
     * 保存定价路线信息
     * @param pricingRouteInfoDO
     * @return
     */
    void save(PricingRouteInfoDO pricingRouteInfoDO);
}
