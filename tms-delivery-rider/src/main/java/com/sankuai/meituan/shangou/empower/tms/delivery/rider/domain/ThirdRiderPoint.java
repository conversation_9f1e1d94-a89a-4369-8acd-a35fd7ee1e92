package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-09-12
 * @email <EMAIL>
 */
@Data
@NoArgsConstructor
public class ThirdRiderPoint {

    private ThirdRiderKey thirdRiderKey;

    private CoordinatePoint point;

    private Long time;

    public ThirdRiderPoint(ThirdRiderKey thirdRiderKey, CoordinatePoint point, Long time) {
        this.thirdRiderKey = thirdRiderKey;
        this.point = point;
        this.time = time;
    }



}
