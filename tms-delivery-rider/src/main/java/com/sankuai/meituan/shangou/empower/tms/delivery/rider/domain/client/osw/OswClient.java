package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.osw;

import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/6/6 14:54
 **/
public interface OswClient {
    Optional<WarehouseDTO> queryWarehouseById(Long tenantId, Long storeId);
}
