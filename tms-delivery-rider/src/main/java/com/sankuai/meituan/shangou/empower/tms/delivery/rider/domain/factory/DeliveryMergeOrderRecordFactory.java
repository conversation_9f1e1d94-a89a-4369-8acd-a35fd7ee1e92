package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.factory;

import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.cmd.MergeOrderOperateCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryMergeOrderRecord;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.auth.RiderAuthClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class DeliveryMergeOrderRecordFactory {

    @Resource
    private RiderAuthClient riderAuthClient;

    public List<RiderDeliveryMergeOrderRecord> batchCreate(List<MergeOrderOperateCmd> cmdList) {
        return Optional.ofNullable(cmdList).orElse(Collections.emptyList())
                .stream().map(this::create).flatMap(Collection::stream).collect(Collectors.toList());
    }

    public List<RiderDeliveryMergeOrderRecord> create(MergeOrderOperateCmd cmd) {
        List<Long> roleIds = riderAuthClient.queryRiderAccountRoleIds(cmd.getCourier().getRiderAccountId());
        return cmd.getRiderDeliveryOrders().stream().map(order -> new RiderDeliveryMergeOrderRecord(
                null,
                order.getId(),
                cmd.getMergeId(),
                0L,
                order.getCustomerOrderKey().getChannelOrderId(),
                order.getCustomerOrderKey().getOrderBizType(),
                cmd.getCourier().getRiderAccountId(),
                Collections.singletonMap("roleIdList", roleIds),
                LocalDateTime.now(),
                LocalDateTime.now()
        )).collect(Collectors.toList());
    }
}
