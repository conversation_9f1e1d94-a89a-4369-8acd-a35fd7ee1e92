package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.push;

import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PushInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;

import java.util.List;
import java.util.Map;

/**
 * 骑手相关 push
 *
 * <AUTHOR>
 * @since 2021/8/2 17:08
 */
public interface RiderPushClient {

    /**
     * 推送给被转单骑手，有新的转单任务.
     *
     * @param riderDeliveryOrder   运单
     * @param targetRiderAccountId 被转单骑手账号 ID
     */
    void pushSelfRiderNewTransferOrder(RiderDeliveryOrder riderDeliveryOrder, Long targetRiderAccountId);

    /**
     * 推送给前端,开始获取转单后骑手的位置
     *
     * @param deliveryOrder 运单
     * @param targetRiderAccountId 被转单骑手账号 ID
     */
    void pushFrontEndToGetRiderLocation(RiderDeliveryOrder deliveryOrder,Long targetRiderAccountId);


    /**
     * 推送给转单的被转出骑手，配送单被转出.
     *
     * @param riderDeliveryOrder   运单
     * @param targetRiderAccountId 转出骑手账号 ID
     */
    void pushSelfRiderTransferOutOrder(RiderDeliveryOrder riderDeliveryOrder, Long targetRiderAccountId);

    PushInfo queryPushInfoByPositions(long tenantId, long storeId, List<Long> positionIds);
}
