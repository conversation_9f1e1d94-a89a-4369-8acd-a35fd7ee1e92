package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/9
 */
public interface RiderDeliveryOrderRepository {

	void save(RiderDeliveryOrder deliveryOrder);

	/**
	 * 通过订单key查询激活的运单，强制走主库
	 */
	Optional<RiderDeliveryOrder> getActiveDeliveryOrderForceMaster(Long tenantId, Long storeId, Long orderId);

	Optional<RiderDeliveryOrder> getDeliveryOrderForceMaster(Long id);

	Optional<RiderDeliveryOrder> getDeliveryOrderForceMasterWithTenantId(Long id,Long tenantId, Long storeId);

	PageResult<RiderDeliveryOrder> pageQueryRiderDeliveryOrdersTenant(PageRequest pageRequest, Long storeId, DeliveryStatusEnum status,Long tenantId);

	PageResult<RiderDeliveryOrder> pageQueryRiderDeliveryOrders(PageRequest pageRequest, Long storeId, DeliveryStatusEnum status, Long riderAccountId,Long tenantId);

	PageResult<RiderDeliveryOrder> pageQueryCompletedDeliveryOrders(PageRequest pageRequest, Long storeId, Long riderAccountId, LocalDateTime startETA,Long tenantId);

	PageResult<RiderDeliveryOrder> pageQueryCompletedDeliveryOrders(PageRequest pageRequest, Long storeId, Long riderAccountId, LocalDateTime deliveryDoneTimeBegin, LocalDateTime deliveryDoneTimeEnd,Long tenantId);

	long countDeliveryOrderTenant(Long storeId, DeliveryStatusEnum status,Long tenantId);

	long countDeliveryOrder(Long storeId, DeliveryStatusEnum status, Long riderAccountId,Long tenantId);

	long countTimeoutDeliveryOrderTenant(Long storeId, DeliveryStatusEnum status,Long tenantId);

	long countTimeoutDeliveryOrder(Long storeId, DeliveryStatusEnum status, Long riderAccountId,Long tenantId);

	PageResult<RiderDeliveryOrder> pageQueryRiderDeliveryOrdersWithTenantId(PageRequest pageRequest, Long storeId, List<DeliveryStatusEnum> statusList, Long riderAccountId,Long activeStatus,Long tenantId);

	List<RiderDeliveryOrder> querySelfRiderNewAndEndDeliveryOrders(Long tenantId, Long storeId, List<DeliveryStatusEnum> statusList, Long riderAccountId);

	List<RiderDeliveryOrder> querySelfRiderInProcessDeliveryOrders(Long tenantId, Long storeId, List<DeliveryStatusEnum> statusList, Long riderAccountId);

	List<RiderDeliveryOrder> queryThirdRiderInProcessDeliveryOrders(Long tenantId, Long storeId, List<DeliveryStatusEnum> statusList, LocalDateTime timeoutPoint);


	long countDeliveryOrderWithTenantId(Long storeId, List<DeliveryStatusEnum> statusList, Long riderAccountId,Long activeStatus,Long tenantId);

	@Deprecated
	Optional<RiderDeliveryOrder> getCurrentDeliveryOrderForceMaster(Long orderId);

	Optional<RiderDeliveryOrder> getCurrentDeliveryOrderForceMasterWithRoute(Long orderId);

	Optional<RiderDeliveryOrder> getCurrentDeliveryOrderForceMasterWithTenant(Long orderId,Long tenantId,Long storeId);

	List<RiderDeliveryOrder> batchQueryDeliveryOrder(Long storeId, List<DeliveryStatusEnum> statusList, Long lastQueryResultMaxId, Integer pageSize,Long tenantId);

	long countRiderDeliveryOrder(Long tenantId, List<DeliveryStatusEnum> statusEnumList, LocalDateTime start,
								LocalDateTime end, List<Long> storeIds, List<Long> riderAccountIds);

	long countRiderCompletedDeliveryOrder(Long tenantId, Long storeId, Long riderAccountId,
										  LocalDateTime deliveryDoneTimeBegin, LocalDateTime deliveryDoneTimeEnd);

	long countRiderCompletedAndOneYuanOrder(Long tenantId, Long storeId, Long riderAccountId,
										  LocalDateTime deliveryDoneTimeBegin, LocalDateTime deliveryDoneTimeEnd);

	List<RiderDeliveryOrder> queryRiderDeliveryOrderByPage(PageRequest pageRequest, Long tenantId,
														   List<DeliveryStatusEnum> statusEnumList,
														   LocalDateTime start, LocalDateTime end, List<Long> storeIds,
														   List<Long> riderAccountIds);

	Map<Long,RiderDeliveryOrder> queryRiderDeliveringOrderMap(Long storeId, Long riderAccountId,Long tenantId);

	List<RiderDeliveryOrder> queryDeliveryOrdersByPoiAndStatusList(Long tenantId, List<Long> storeIdList, List<Integer> statusList);

	List<RiderDeliveryOrder> batchQueryDeliveryOrder(Long tenantId, Long storeId, List<Long> orderIds);

	public List<RiderDeliveryOrder> batchQueryDeliveryOrderContainUnactivateOrder(Long tenantId, Long storeId, List<Long> orderIds);

	@Deprecated
	List<RiderDeliveryOrder> batchQueryDeliveryOrder(List<Long> orderIds);

	List<RiderDeliveryOrder> batchQueryDeliveryOrderByRoute(List<Long> orderIds, boolean filterSelfDelivery);

	List<RiderDeliveryOrder> batchQueryDeliveryOrderWithTenant(Long storeId, DeliveryStatusEnum status,Long tenantId);

	List<RiderDeliveryOrder> batchQueryDeliveryOrderByIds(List<Long> deliveryOrderIds,Long tenantId,Long storeId);


	List<RiderDeliveryOrder> batchQueryDeliveryOrder(Long storeId, DeliveryStatusEnum status, Long riderAccountId,Long tenantId);

	List<RiderDeliveryOrder> batchQueryDeliveryOrder(Long storeId, List<DeliveryStatusEnum> status, Long riderAccountId,Long tenantId);


	List<DeliveryOrderDO> batchQueryThirdDeliveryOrder(Long storeId, List<DeliveryStatusEnum> statusList, Long lastQueryResultMaxId, Integer pageSize,Long tenantId);

	Map<Long, Integer> queryStoreDeliveringOrderCount(List<Long> storeIds, List<DeliveryStatusEnum> statusList,Long tenantId);
}
