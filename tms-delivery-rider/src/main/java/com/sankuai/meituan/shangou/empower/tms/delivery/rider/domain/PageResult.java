package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/9
 */
@Getter
@ToString(callSuper = true)
public class PageResult<T> extends Result<List<T>> {

	private int page;
	private int pageSize;
	private long total;

	public PageResult(List<T> list, PageRequest pageRequest, long total) {
		super(list);
		this.page = pageRequest.getPage();
		this.pageSize = pageRequest.getPageSize();
		this.total = total;
	}

	public PageResult(Failure failure) {
		super(failure);
	}
}
