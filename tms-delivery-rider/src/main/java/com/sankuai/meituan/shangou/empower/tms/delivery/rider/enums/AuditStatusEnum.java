package com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums;

import com.meituan.kafka.utils.NOT_READY;

/**
 * <AUTHOR>
 * @since 2023/4/27 11:36
 **/
public enum AuditStatusEnum {
    WAITING_TO_AUDIT(1, "待审核"),
    PASS_AUDIT(2, "审核通过"),
    NOT_PASS_AUDIT(3,"审核不通过");

    private int code;
    private String desc;

    AuditStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
