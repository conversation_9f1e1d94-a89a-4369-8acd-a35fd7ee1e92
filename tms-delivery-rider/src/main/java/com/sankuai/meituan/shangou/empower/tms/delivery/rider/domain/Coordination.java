package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/3/20 10:29
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class Coordination {
    private String longitude;

    private String latitude;

    public Boolean checkIsValid() {
        return StringUtils.isNotBlank(longitude) && StringUtils.isNotBlank(latitude);
    }
}
