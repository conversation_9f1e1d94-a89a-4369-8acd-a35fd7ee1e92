package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/18 18:00
 **/
public interface DeliveryRiskControlOrderRepository {

    /**
     * 通过订单号和biztype查询
     * @param viewOrderIdList
     * @return
     */
    List<DeliveryRiskControlOrderDO> queryByViewOrderIdList(List<String> viewOrderIdList);


    /**
     * 通过骑手id和日期查风控单
     * @param viewOrderIdList
     * @return
     */
    Long countByDtAndAccount(Long dt, Long riderAccountId);
}
