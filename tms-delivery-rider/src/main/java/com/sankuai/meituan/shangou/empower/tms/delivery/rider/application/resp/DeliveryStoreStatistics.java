package com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "门店维度统计数据",
        authors = {
                "yujing10"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DeliveryStoreStatistics {

    @FieldDoc(description = "门店id")
    public Long storeId;

    @FieldDoc(description = "待领取订单量")
    public Integer waitToAccept;

    @FieldDoc(description = "待领取超时订单量")
    private Integer waitToAcceptJudgeTimeout;

    @FieldDoc(description = "待领取超时订单量, 只按eta算，不加时")
    private Integer waitToAcceptJudgeTimeoutV2;

    @FieldDoc(description = "拣货中（骑手已接单）订单量")
    private Integer picking;

    @FieldDoc(description = "拣货中（骑手已接单）超时订单量")
    private Integer pickingJudgeTimeout;

    @FieldDoc(description = "拣货中（骑手已接单）超时订单量, 只按eta算，不加时")
    private Integer pickingJudgeTimeoutV2;

    @FieldDoc(description = "配送中（骑手已取货）订单量")
    private Integer delivering;

    @FieldDoc(description = "配送中（骑手已取货）超时订单量")
    private Integer deliveringJudgeTimeout;

    @FieldDoc(description = "配送中（骑手已取货）超时订单量, 只按eta算，不加时")
    private Integer deliveringJudgeTimeoutV2;

    @FieldDoc(description = "履约中订单数")
    private Integer fulfilling;

    @FieldDoc(description = "履约中骑手数")
    private Integer fulfillingRider;

}
