package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-07
 * @email <EMAIL>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PushInfo {

    //美团员工mis体系
    private List<String> meituanMisIdList;
    //外部mis体系
    private List<String> externalMisIdList;

    private List<Long> accountIdList;

    public static PushInfo empty() {
        return new PushInfo(Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList());
    }

}
