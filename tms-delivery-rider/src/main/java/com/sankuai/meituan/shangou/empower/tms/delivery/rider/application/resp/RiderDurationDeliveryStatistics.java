package com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/8/24 18:23
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RiderDurationDeliveryStatistics {
    @FieldDoc(description = "租户id")
    private Long tenantId;

    @FieldDoc(description = "骑手id")
    private Long riderAccountId;

    @FieldDoc(description = "已送达订单数")
    private Integer deliveredCount;

    @FieldDoc(description = "配送超时订单数")
    private Integer timeoutCount;

    @FieldDoc(description = "25分钟送达率")
    private String deliveredRateIn25min;

    @FieldDoc(description = "送达前取消订单数")
    private Integer cancelBeforeDeliveredCount;

    @FieldDoc(description = "提前点送达订单数")
    private Integer earlyClickDeliveryCount;

    @FieldDoc(description = "配送风控订单数")
    private Integer riskControlCount;

    @FieldDoc(description = "平均履约时长")
    private Double avgFulfillDuration;

    @FieldDoc(description = "超时率")
    private String timeoutRate;

    @FieldDoc(description = "统计时间范围-开始日期 格式：yyyyMMdd")
    private String beginDate;

    @FieldDoc(description = "统计时间范围-结束日期 格式：yyyyMMdd")
    private String endDate;

    @FieldDoc(description = "配送超时订单数")
    private Integer etaOvertimeOrdNumV2;

    @FieldDoc(description = "配送超时订单率")
    private String etaOvertimeRatioV2;


    public static RiderDurationDeliveryStatistics newEmptyDeliveryStatistics(Long tenantId, Long riderAccountId,
                                                                          String beginDate, String endDate) {
        RiderDurationDeliveryStatistics emptyDeliveryStatistics = new RiderDurationDeliveryStatistics();
        emptyDeliveryStatistics.setRiderAccountId(riderAccountId);
        emptyDeliveryStatistics.setTenantId(tenantId);
        emptyDeliveryStatistics.setBeginDate(beginDate);
        emptyDeliveryStatistics.setEndDate(endDate);

        return emptyDeliveryStatistics;
    }
}
