package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.service;

import com.dianping.cat.Cat;
import com.dianping.cat.log.CatLogger;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiApprovalScenesTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.drunkhorsemgmt.labor.exception.SystemException;
import com.sankuai.drunkhorsemgmt.labor.types.Result;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.utils.MccDynamicConfigUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.labor.AttendanceSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantChannelStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.LionConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.RiderDeliveryStatisticsApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.push.RiderPushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.facade.PoiFacade;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.factory.PushMessageContentFactory;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.DeliveryBusyLevelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.DeliveryCapacityLoadRecordSequenceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.PositionPushConfigEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.PushMessageTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.PositionPushHelper;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.TimeUtil;
import com.sankuai.shangou.commons.message.pusher.channelPusher.XmMessagePusher;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.common.StringEscapeUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.DeliveryCapacityLoadRecordSequenceEnum.*;

/**
 * <AUTHOR>
 * @since 2023/1/17 00:28
 **/
@Service
@Slf4j
public class DeliveryCapacityLoadMonitorService {

    @Resource
    private RiderDeliveryStatisticsRepository riderDeliveryStatisticsRepository;

    @Resource
    private RiderDeliveryOrderRepository riderDeliveryOrderRepository;

    @Resource
    private IDeliveryCapacityLoadRepository deliveryCapacityLoadRepository;

    @Resource
    private TenantSystemClient tenantSystemClient;

    @Resource
    private AttendanceSystemClient attendanceSystemClient;

    @Resource
    private PoiFacade poiFacade;

    @Resource(name = "orderOverloadMessagePusher")
    private XmMessagePusher orderOverloadMessagePusher;
    @Resource
    private RiderPushClient riderPushClient;
    @Resource
    private PoiThriftService poiThriftService;

    private static final Logger deliveryCapacityLoadLog = LoggerFactory.getLogger("logger_drunk.horse.delivery.capacity.load.log");

    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000L, multiplier = 1.5))
    public StoreDeliveryCapacityLoadInfo calStoreDeliveryCapacityLoadInfo(Long tenantId, List<Long> poiIdList) {
        //查配送单
        Map<Long, Integer> storeDeliveringOrderCountMap = new HashMap<>();
        if(DeliveryRiderMccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
            storeDeliveringOrderCountMap = riderDeliveryOrderRepository.queryStoreDeliveringOrderCount(poiIdList,
                    Arrays.asList(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, DeliveryStatusEnum.RIDER_ASSIGNED,
                            DeliveryStatusEnum.RIDER_TAKEN_GOODS),tenantId);
        }else {
            storeDeliveringOrderCountMap = riderDeliveryStatisticsRepository.queryStoreDeliveringOrderCount(poiIdList,
                    Arrays.asList(RiderDeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, RiderDeliveryStatusEnum.RIDER_ASSIGNED,
                            RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS));
        }


        //查在岗人数
        Map<Long/*storeId*/, Integer> onDutyEmployeeCount = attendanceSystemClient.queryStoreOnDutyEmployeeCount(tenantId, poiIdList);

        //计算门店人均负载
        Map<Long/*storeId*/, Float> storeDeliveryCapacityLoadMap = calLoadPerRider(storeDeliveringOrderCountMap, onDutyEmployeeCount);

        //计算每个门店的爆单程度
        Map<Long/*storeId*/, DeliveryBusyLevelEnum> storeBusyLevelMap = calMultiStoreBusyLevel(poiIdList, storeDeliveryCapacityLoadMap);

        //查询前三个点的负载情况
        Map<Long/*storeId*/, Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO>> capacityRecordMap =
                deliveryCapacityLoadRepository.getStoreDeliveryCapacityRecord(Fun.map(poiIdList, Object::toString));

        return new StoreDeliveryCapacityLoadInfo(storeDeliveringOrderCountMap, onDutyEmployeeCount, storeDeliveryCapacityLoadMap, storeBusyLevelMap, capacityRecordMap);

    }

    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000L, multiplier = 1.5))
    public Map<Long/*storeId*/, PushMessageTypeEnum> pushXmMessage(Long tenantId, List<Long> poiList, StoreDeliveryCapacityLoadInfo storeDeliveryCapacityLoadInfo) {

        //计算需要推送的门店和对应的消息类型
        Map<Long/*storeId*/, PushMessageTypeEnum> pushMessageTypeMap = calNeedPushMessageStoreMap(poiList, storeDeliveryCapacityLoadInfo.getCapacityRecordMap(), storeDeliveryCapacityLoadInfo.getStoreBusyLevelMap());

        //查看每个门店的主管和城市经理
        Map<Long/*storeId*/, List<String>> storeManagerMap = getStoreManagerMap(tenantId, new ArrayList<>(pushMessageTypeMap.keySet()));

        //查看每个门店的渠道门店信息
        Map<Long/*storeId*/, List<TenantChannelStoreInfo>> storeChannelInfoMap =
                tenantSystemClient.queryChannelStoreDetailInfoList(tenantId, new ArrayList<>(pushMessageTypeMap.keySet()));

        //push大象消息
        pushXmMessage(storeManagerMap, pushMessageTypeMap, storeChannelInfoMap, storeDeliveryCapacityLoadInfo);

        return pushMessageTypeMap;
    }

    private  Map<Long, List<String>> getStoreManagerMap(Long tenantId, List<Long> pushStoreIdList) {
        if (CollectionUtils.isEmpty(pushStoreIdList)) {
            return Collections.emptyMap();
        }

        List<Long> finalPushStoreIdList = pushStoreIdList;

        //查询门店信息
        PoiMapResponse poiMapResponse = poiThriftService.queryTenantPoiInfoMapByPoiIds(pushStoreIdList, tenantId);
        if (0 != poiMapResponse.getStatus().getCode() || MapUtils.isEmpty(poiMapResponse.getPoiInfoMap())) {
            log.error("Query poiInfo failed. poiIds = {}", pushStoreIdList);
            throw new SystemException("查询门店失败");
        }

        Map<Long, PoiInfoDto> poiInfoMap = poiMapResponse.getPoiInfoMap();
        List<Long> byPositionStoreIds = pushStoreIdList.stream()
                //灰度,全量后去掉该条即可
                .filter(storeId -> PositionPushHelper.getDHByPositionSwitch(tenantId, storeId))
                //直营门店
                .filter(poiInfoMap::containsKey)
                .filter(storeId -> Objects.isNull(poiInfoMap.get(storeId).getPoiExtendContentDto()) || Objects.isNull(poiInfoMap.get(storeId).getPoiExtendContentDto().getOperationMode()) ||  poiInfoMap.get(storeId).getPoiExtendContentDto().getOperationMode() != 2)
                .collect(Collectors.toList());


        Map<Long, List<String>> storeIdAndMisMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(byPositionStoreIds)) {
            try {

                Map<Long, List<String>> storeIdPositionManagerMap = byPositionStoreIds.stream()
                        .collect(Collectors.toMap(
                                Function.identity(),
                                storeId -> {
                                    PushInfo pushInfo = riderPushClient.queryPushInfoByPositions(tenantId, storeId, PositionPushHelper.getPositionIds(poiMapResponse.getPoiInfoMap().get(storeId), PositionPushConfigEnum.ORDER_OVERLOAD));
                                    return pushInfo.getMeituanMisIdList();
                                },
                                (older, newer) -> newer
                        ));
                storeIdAndMisMap.putAll(storeIdPositionManagerMap);
                //这个必须放在这里，如果上面报错，这里就不会remove掉，可以走到下面的老逻辑
                finalPushStoreIdList.removeAll(byPositionStoreIds);
            } catch (Exception e) {
                log.error("position push gary logic throw exp", e);
            }
        }

        if (CollectionUtils.isNotEmpty(finalPushStoreIdList)) {
            Map<Long, Result<List<List<String>>>> resultMap = finalPushStoreIdList.stream()
                    .collect(Collectors.toMap(Function.identity(),
                            poi -> poiFacade.queryApprovalFullEmployeeChain(tenantId, poi, PoiApprovalScenesTypeEnum.COMMON), (k1, k2) -> k2));
            Map<Long, List<String>> tenantSystemManagerMap = resultMap.entrySet().stream()
                    .filter(entry -> Objects.isNull(entry.getValue().getFailure()))
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                        List<List<String>> managerList = entry.getValue().getInfo();
                        ArrayList<String> receiverEmployeeIds = new ArrayList<>();
                        //第一级是主管
                        if (managerList.size() >= 1 && CollectionUtils.isNotEmpty(managerList.get(0))) {
                            receiverEmployeeIds.addAll(managerList.get(0));
                        }

                        //第二级是城市经理
                        if (managerList.size() >= 2 && CollectionUtils.isNotEmpty(managerList.get(1))) {
                            receiverEmployeeIds.addAll(managerList.get(1));
                        }

                        return receiverEmployeeIds;
                    }, (k1, k2) -> k2));
            storeIdAndMisMap.putAll(tenantSystemManagerMap);
        }

        return storeIdAndMisMap;
    }


    @Deprecated
    private void pushXmMessage(Map<Long, List<String>> storeManagerMap,
                               Map<Long, PushMessageTypeEnum> messageTypeMap,
                               Map<Long/*storeId*/, List<TenantChannelStoreInfo>> storeChannelInfoMap,
                               StoreDeliveryCapacityLoadInfo storeDeliveryCapacityLoadInfo) {
        for (Map.Entry<Long, List<String>> entry : storeManagerMap.entrySet()) {
            String pushContent = buildPushContent(entry.getKey(), messageTypeMap, storeChannelInfoMap, storeDeliveryCapacityLoadInfo);
            ArrayList<String> receiverMisIds = new ArrayList<>();

            //加入主管、城市经理
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                receiverMisIds.addAll(entry.getValue());
            }

            //加入值班人
            if (CollectionUtils.isNotEmpty(DeliveryRiderMccConfigUtils.getDutyPersonMisIdList())) {
                receiverMisIds.addAll(DeliveryRiderMccConfigUtils.getDutyPersonMisIdList());
            }

            if (CollectionUtils.isEmpty(receiverMisIds) || StringUtils.isBlank(pushContent)) {
                log.warn("接收人或者推送消息内容为空，放弃推送");
                return;
            }

            orderOverloadMessagePusher.pushMessageByMisIds(receiverMisIds.stream().distinct().collect(Collectors.toList()), pushContent);

            reportLog2Hive(entry.getKey(), messageTypeMap, storeChannelInfoMap, storeDeliveryCapacityLoadInfo, pushContent);
        }
    }

    private Map<Long, PushMessageTypeEnum> calNeedPushMessageStoreMap(List<Long> poiList, Map<Long, Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO>> capacityRecordMap,
                                                                            Map<Long, DeliveryBusyLevelEnum> storeBusyLevelMap) {
        Map<Long, PushMessageTypeEnum> messageTypeMap = poiList.stream().collect(Collectors.toMap(
                Function.identity(),
                poiId -> {
                    Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO> recordMap = capacityRecordMap.getOrDefault(poiId, Collections.emptyMap());
                    DeliveryBusyLevelEnum nowBusyLevel = storeBusyLevelMap.get(poiId);
                    DeliveryCapacityLoadRecordDO tRecord = recordMap.get(LATEST_RECORD);
                    DeliveryCapacityLoadRecordDO t_1Record = recordMap.get(THE_FIRST_BEFORE_LATEST_RECORD);
                    //先判断是否推爆单缓解消息
                    if (judgeWhetherPushMitigateMessage(nowBusyLevel, tRecord, t_1Record)) {
                        return PushMessageTypeEnum.PUSH_ALARM_MITIGATE_MESSAGE;
                    }

                    //再判断是否推爆单消息
                    if (judgeWhetherPushBusyMessage(nowBusyLevel, tRecord)) {
                        return PushMessageTypeEnum.map2PushMessageTypeEnum(nowBusyLevel);
                    }

                    return PushMessageTypeEnum.DO_NOT_PUSH_MESSAGE;
                }
        ));

        return messageTypeMap.entrySet().stream()
                .filter(entry -> !Objects.equals(entry.getValue(), PushMessageTypeEnum.DO_NOT_PUSH_MESSAGE))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (k1, k2) -> k2));
    }

    /**
     * 判断是否推爆单缓解消息
     * 判断条件:
     * 1.前两个点的采样值(t,t-1)不为空
     * 2.若当前运力负载（now）、t点运力负载均小于t-1的爆单等级对应的运力负载，则推送负载压力缓解消息
     */

    private boolean judgeWhetherPushMitigateMessage(DeliveryBusyLevelEnum nowBusyLevel, DeliveryCapacityLoadRecordDO tRecord,
                                                           DeliveryCapacityLoadRecordDO t_1Record) {
        if (Objects.nonNull(tRecord) && Objects.nonNull(t_1Record)) {
            if (nowBusyLevel.getCode() < t_1Record.getDeliveryBusyLevel().getCode() &&
                    tRecord.getDeliveryBusyLevel().getCode() < t_1Record.getDeliveryBusyLevel().getCode()) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断是否推送爆单消息
     * 判断条件:
     * 1.当前运力负载触发告警标准
     * 2.上次推送为高于该级别的告警，则本次不重复推送
     */
    private boolean judgeWhetherPushBusyMessage(DeliveryBusyLevelEnum nowBusyLevel, DeliveryCapacityLoadRecordDO tRecord) {
        if (nowBusyLevel == DeliveryBusyLevelEnum.NOT_BUSY) {
            return false;
        }

        if (Objects.isNull(tRecord)) {
            return true;
        }

        //当前点的爆单程度大于上个点的爆单程度 且当前要推的消息了类型与上最近一次要推的消息类型不相等
        if (nowBusyLevel.getCode() > tRecord.getDeliveryBusyLevel().getCode()
                && !Objects.equals(PushMessageTypeEnum.map2PushMessageTypeEnum(nowBusyLevel), tRecord.getLatestPushMessageRecordType())) {
            return true;
        }

        return false;
    }


    private String buildPushContent(Long storeId, Map<Long, PushMessageTypeEnum> messageTypeMap,
                                          Map<Long/*storeId*/, List<TenantChannelStoreInfo>> storeChannelInfoMap,
                                          StoreDeliveryCapacityLoadInfo storeDeliveryCapacityLoadInfo) {
        PushMessageTypeEnum pushMessageTypeEnum = messageTypeMap.get(storeId);
        List<TenantChannelStoreInfo> tenantChannelStoreInfos = storeChannelInfoMap.get(storeId);
        DeliveryBusyLevelEnum deliveryBusyLevelEnum = storeDeliveryCapacityLoadInfo.getStoreBusyLevelMap().get(storeId);
        Integer storeDeliveringOrderCount = storeDeliveryCapacityLoadInfo.getStoreDeliveringOrderCountMap().getOrDefault(storeId, 0);
        Integer onDutyEmployeeCount = storeDeliveryCapacityLoadInfo.getOnDutyEmployeeCount().getOrDefault(storeId, 0);
        Float storeDeliveryCapacityLoad = storeDeliveryCapacityLoadInfo.getStoreDeliveryCapacityLoadMap().getOrDefault(storeId, 0f);
        Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO> storeCapacityRecordMap = storeDeliveryCapacityLoadInfo.getCapacityRecordMap().getOrDefault(storeId, Collections.emptyMap());

        return PushMessageContentFactory.buildPushContent(pushMessageTypeEnum, deliveryBusyLevelEnum, tenantChannelStoreInfos,
                storeDeliveringOrderCount, onDutyEmployeeCount, storeDeliveryCapacityLoad, storeCapacityRecordMap);

    }


    private Map<Long, Float> calLoadPerRider(Map<Long, Integer> storeNewOrderCount, Map<Long, Integer> storeOnDutyEmployeeCount) {
        return storeNewOrderCount.entrySet().stream().map(
                entry -> {
                    Integer newOrderCount = entry.getValue();
                    Integer onDutyEmployeeCount = storeOnDutyEmployeeCount.getOrDefault(entry.getKey(), 0);
                    return Pair.of(entry.getKey(), Float.valueOf(divide(newOrderCount.toString(), onDutyEmployeeCount.toString())));
                }
        ).collect(Collectors.toMap(Pair::getLeft, Pair::getRight));
    }

    private Map<Long, DeliveryBusyLevelEnum> calMultiStoreBusyLevel(List<Long> poiSubList, Map<Long, Float> perRiderLoad) {

        return poiSubList.stream().collect(Collectors.toMap(Function.identity(), poiId -> {
            Float load = perRiderLoad.get(poiId);
            if (Objects.isNull(load)) {
                return DeliveryBusyLevelEnum.NOT_BUSY;
            }

            return DeliveryBusyLevelEnum.map2DeliveryBusyLevelEnum(load.intValue());
        }));
    }


    private String divide(String partition, String total) {
        BigDecimal partitionBigDecimal = new BigDecimal(partition);
        BigDecimal totalBigDecimal = new BigDecimal(total);
        if (totalBigDecimal.equals(BigDecimal.ZERO)) {
            return BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP).toString();
        }
        return partitionBigDecimal.divide(totalBigDecimal, 4, RoundingMode.HALF_UP)
                .toString();
    }


    public Map<Long, Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO>> buildDeliveringCapacityLoadRecordMap(List<Long> storeIds,
                                                                                                                              StoreDeliveryCapacityLoadInfo storeDeliveryCapacityLoadInfo,
                                                                                                                              Map<Long, PushMessageTypeEnum> pushMessageTypeMap) {
        Map<Long, Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO>> oldCapacityRecordMap = storeDeliveryCapacityLoadInfo.getCapacityRecordMap();
        Map<Long, DeliveryBusyLevelEnum> storeBusyLevelMap = storeDeliveryCapacityLoadInfo.getStoreBusyLevelMap();
        Map<Long, Integer> onDutyEmployeeCount = storeDeliveryCapacityLoadInfo.getOnDutyEmployeeCount();
        Map<Long, Integer> storeDeliveringOrderCountMap = storeDeliveryCapacityLoadInfo.getStoreDeliveringOrderCountMap();
        HashMap<Long, Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO>> newCapacityRecordMap = new HashMap<>();
        for (Long storeId : storeIds) {
            Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO> storeOldCapacityLoadMap = oldCapacityRecordMap.getOrDefault(storeId, Collections.emptyMap());
            HashMap<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO> storeNewCapacityLoadMap = new HashMap<>();
            if (storeOldCapacityLoadMap.containsKey(THE_FIRST_BEFORE_LATEST_RECORD)) {
                storeNewCapacityLoadMap.put(THE_SECOND_BEFORE_LATEST_RECORD, storeOldCapacityLoadMap.get(THE_FIRST_BEFORE_LATEST_RECORD));
            }

            if (storeOldCapacityLoadMap.containsKey(LATEST_RECORD)) {
                storeNewCapacityLoadMap.put(THE_FIRST_BEFORE_LATEST_RECORD, storeOldCapacityLoadMap.get(LATEST_RECORD));
            }

            storeNewCapacityLoadMap.put(LATEST_RECORD, new DeliveryCapacityLoadRecordDO(storeId, storeBusyLevelMap.get(storeId),
                    pushMessageTypeMap.getOrDefault(storeId, PushMessageTypeEnum.DO_NOT_PUSH_MESSAGE),
                    onDutyEmployeeCount.getOrDefault(storeId, 0),
                    storeDeliveringOrderCountMap.getOrDefault(storeId, 0),
                    LocalDateTime.now(),
                    pushMessageTypeMap.containsKey(storeId) ? pushMessageTypeMap.get(storeId) : Optional.ofNullable(storeOldCapacityLoadMap.get(LATEST_RECORD))
                            .map(DeliveryCapacityLoadRecordDO::getLatestPushMessageRecordType)
                            .orElse(PushMessageTypeEnum.DO_NOT_PUSH_MESSAGE)));

            newCapacityRecordMap.put(storeId, storeNewCapacityLoadMap);
        }

        return newCapacityRecordMap;

    }

    private void reportLog2Hive(Long storeId, Map<Long, PushMessageTypeEnum> messageTypeMap,
                                Map<Long/*storeId*/, List<TenantChannelStoreInfo>> storeChannelInfoMap,
                                StoreDeliveryCapacityLoadInfo storeDeliveryCapacityLoadInfo,
                                String pushContent) {
        PushMessageTypeEnum pushMessageTypeEnum = messageTypeMap.get(storeId);
        List<TenantChannelStoreInfo> tenantChannelStoreInfos = storeChannelInfoMap.get(storeId);
        Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO> storeCapacityRecordMap =
                storeDeliveryCapacityLoadInfo.getCapacityRecordMap().getOrDefault(storeId, Collections.emptyMap());

        if (tenantChannelStoreInfos.size() == 0) {
            return;
        }

        try {
            deliveryCapacityLoadLog.info(XMDLogFormat.build()
                    .putTag("store_id", storeId + "")
                    .putTag("city_name", tenantChannelStoreInfos.get(0).getCityName())
                    .putTag("store_name", tenantChannelStoreInfos.get(0).getStoreName())
                    .putTag("push_time", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .putTag("message_type", pushMessageTypeEnum.getCode()+"")
                    .putTag("message_type_desc", pushMessageTypeEnum.getDesc())
                    .putTag("latest_message_type", Objects.equals(messageTypeMap.get(storeId), PushMessageTypeEnum.PUSH_ALARM_MITIGATE_MESSAGE) ?
                            Optional.ofNullable(storeCapacityRecordMap.get(THE_FIRST_BEFORE_LATEST_RECORD)).map(DeliveryCapacityLoadRecordDO::getDeliveryBusyLevel).map(DeliveryBusyLevelEnum::getDesc).orElse("") : "")
                    .putTag("latest_push_time", Objects.equals(messageTypeMap.get(storeId), PushMessageTypeEnum.PUSH_ALARM_MITIGATE_MESSAGE) ?
                            Optional.ofNullable(storeCapacityRecordMap.get(THE_FIRST_BEFORE_LATEST_RECORD)).map(DeliveryCapacityLoadRecordDO::getRecordTime).map(localDateTime -> localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).orElse("") : "")
                    .putTag("message_content", pushContent)
                    .toString());
        } catch (Exception e) {
            log.error("deliveryCapacityLoadLog reportLog2Hive error", e);
            Cat.logEvent("deliveryCapacityLoadLog.reportLog2Hive", "error");
        }

    }

}
