package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp.RiderDurationDeliveryStatistics;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/8/24 18:35
 **/
public interface RiderDeliveryStatisticsRepository {
    List<RiderDeliveryStatisticDO> queryByAccountIdAndDuration(Long tenantId, Long riderAccountId,
                                                                             Long beginDate, Long endDate);

    Boolean checkDataIsReadyByDt(@NotNull Long dt);

    Map<Long, Integer> queryStoreDeliveringOrderCount(List<Long> storeIds, List<RiderDeliveryStatusEnum> statusList);
}
