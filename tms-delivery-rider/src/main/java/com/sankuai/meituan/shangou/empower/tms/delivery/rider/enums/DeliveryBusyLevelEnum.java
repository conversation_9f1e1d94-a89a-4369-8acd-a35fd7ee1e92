package com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums;

import com.meituan.linz.boot.util.Assert;

/**
 * <AUTHOR>
 * @since 2023/1/11 15:50
 **/
public enum DeliveryBusyLevelEnum {

    NOT_BUSY(0, "不爆单"),
    //进行中运单是在岗骑手3倍
    FIRST_LEVEL_BUSY(3, "一级爆单"),
    //进行中运单是在岗骑手4倍
    SECOND_LEVEL_BUSY(4, "二级爆单"),
    //进行中运单是在岗骑手5倍
    THIRD_LEVEL_BUSY(5, "三级爆单"),
    //进行中运单是在岗骑手6倍
    SERIOUS_BUSY(6, "严重爆单"),
    ;

    private int loadVal;
    private String desc;

    DeliveryBusyLevelEnum(int code, String desc) {
        this.loadVal = code;
        this.desc = desc;
    }

    public int getCode() {
        return loadVal;
    }

    public String getDesc() {
        return desc;
    }

    public static DeliveryBusyLevelEnum enumOf(int value) {
        for (DeliveryBusyLevelEnum each : values()) {
            if (each.getCode() == value) {
                return each;
            }
        }

        throw new  IllegalArgumentException("无对应枚举类型");
    }

    public static DeliveryBusyLevelEnum map2DeliveryBusyLevelEnum(int DeliveryCapacityLoad) {
        if (DeliveryCapacityLoad < 0) {
            throw new  IllegalArgumentException("运力负载不能小于0");
        }

        if (DeliveryCapacityLoad < FIRST_LEVEL_BUSY.loadVal) {
            return NOT_BUSY;
        }

        for (DeliveryBusyLevelEnum each : values()) {
            if (each.getCode() == DeliveryCapacityLoad) {
                return each;
            }
        }

        if (DeliveryCapacityLoad > SERIOUS_BUSY.loadVal) {
            return SERIOUS_BUSY;
        }

        throw new  IllegalArgumentException("无对应枚举类型");
    }

    public static DeliveryBusyLevelEnum map2DeliveryBusyLevelEnum(PushMessageTypeEnum pushMessageTypeEnum) {
        if (pushMessageTypeEnum == null) {
            return null;
        }

        switch (pushMessageTypeEnum) {
            case PUSH_FIRST_ALARM_MESSAGE:
                return FIRST_LEVEL_BUSY;
            case PUSH_SECOND_ALARM_MESSAGE:
                return SECOND_LEVEL_BUSY;
            case PUSH_THIRD_ALARM_MESSAGE:
                return THIRD_LEVEL_BUSY;
            case PUSH_SERIOUS_ALARM_MESSAGE:
                return SERIOUS_BUSY;
            default:
                return null;
        }
    }
}
