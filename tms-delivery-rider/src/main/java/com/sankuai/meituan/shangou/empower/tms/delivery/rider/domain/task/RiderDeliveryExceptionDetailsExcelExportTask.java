package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.task;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.style.column.AbstractHeadColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.shangou.empower.rider.client.enums.DeliveryTaskTypeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.PageQueryDeliveryExceptionRequest;
import com.sankuai.meituan.shangou.empower.task.constant.enums.TaskResultCodeEnum;
import com.sankuai.meituan.shangou.empower.task.vo.TaskResultVo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.RiderDeliveryExceptionEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PageResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryException;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryExceptionPageQueryConditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryExceptionRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.facade.PoiFacade;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.AbstractExcelDownloadTask;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.dto.TaskReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.component.TaskExcelComponent;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RiderDeliveryExceptionDetailsExcelExportTask extends AbstractExcelDownloadTask<PageQueryDeliveryExceptionRequest> {
    private static List<List<String>> excelHeaders;
    private static AbstractHeadColumnWidthStyleStrategy excelColumnWidthStyleStrategy;
    private static final String EMPTY_STRING = "";

    static {
        String[] headers = {
                "门店名称", "渠道", "订单号", "订单支付时间", "上报骑手", "异常上报类型", "异常上报时间", "异常上报详情", "修改后的地址", "顾客真实地址",
                "导航距离", "备注"
        };
        excelHeaders = Arrays.stream(headers).map(Collections::singletonList).collect(Collectors.toList());
        // 各列的宽度，integer 从0开始
        excelColumnWidthStyleStrategy = new AbstractHeadColumnWidthStyleStrategy() {
            @Override
            protected Integer columnWidth(Head head, Integer integer) {
                switch (integer) {
                    case 0:
                    case 4:
                    case 1:
                    case 5:
                    case 7:
                    case 8:
                    default:
                        return 23;
                }
            }
        };
    }

    private final RiderDeliveryExceptionRepository riderDeliveryExceptionRepository;
    private final PoiFacade poiFacade;

    @Autowired
    public RiderDeliveryExceptionDetailsExcelExportTask(RiderDeliveryExceptionRepository riderDeliveryExceptionRepository, PoiFacade poiFacade) {
        this.riderDeliveryExceptionRepository = riderDeliveryExceptionRepository;
        this.poiFacade = poiFacade;
    }

    @Override
    protected TaskResultVo checkParam(TaskReq taskReq, PageQueryDeliveryExceptionRequest param) {
        return TaskResultVo.resultOf(TaskResultCodeEnum.SUCCESS);
    }

    @Override
    public TaskExcelComponent.Sheet[] getSheets(long tenantId, long operatorId,
                                                   PageQueryDeliveryExceptionRequest request) {
        log.info("start export rider delivery exception details {}", request);


        TaskExcelComponent.Sheet sheet = TaskExcelComponent.Sheet.builder()
                .sheetName("已上报异常导出")
                .head(excelHeaders)
                .columnWidthStyleStrategy(excelColumnWidthStyleStrategy)
                .rowHeightStyleStrategy(new SimpleRowHeightStyleStrategy((short) 25, null))
                .callbackUntilDataEmpty(true)
                .dataSupplier(() -> {
                    try {
                        RiderDeliveryExceptionPageQueryConditions conditions = new RiderDeliveryExceptionPageQueryConditions(request);

                        PageResult<RiderDeliveryException> exceptionPageResult =
                                riderDeliveryExceptionRepository.pageQueryRiderDeliveryException(conditions);

                        request.setPageNum(request.getPageNum() + 1);

                        return buildContent(tenantId, exceptionPageResult.getInfo());
                    } catch (Exception e) {
                        log.error("导出配送异常失败！rpcReq: {}, error: {}", JsonUtil.toJson(request), e.getMessage(), e);
                        throw new CommonRuntimeException("导出配送异常失败！", e);
                    }
                })
                .build();

        log.info("finish export rider delivery exception details {}", request);
        return new TaskExcelComponent.Sheet[]{sheet};
    }

    private List<List> buildContent(Long tenantId, List<RiderDeliveryException> pageList) {
        if (CollectionUtils.isEmpty(pageList)) {
            return Collections.emptyList();
        }
        Set<Long> poiIds = pageList.stream().map(RiderDeliveryException::getStoreId).collect(Collectors.toSet());
        Map<Long, PoiInfoDto> poiInfo = poiFacade.queryPoiByIds(tenantId, poiIds);

        return pageList.stream().sorted((o1, o2) -> {
            //排序规则 按支付时间倒序排列，若支付时间相同，按上报异常上报时间正序排
            if (o1.getPayTime().compareTo(o2.getPayTime()) == 0) {
                return o1.getCreateTime().compareTo(o2.getCreateTime());
            }

            return -(o1.getPayTime().compareTo(o2.getPayTime()));
        }).map(item -> {
            List<Object> line = new ArrayList<>(excelHeaders.size());
            RiderDeliveryExceptionEnum deliveryExceptionEnum = RiderDeliveryExceptionEnum
                    .enumOf(item.getExceptionType(), item.getExceptionSubType());

            if (deliveryExceptionEnum == null) {
                log.error("导出配送异常失败,异常类型错误！deliveryException:{}", JsonUtil.toJson(item));
                throw new CommonRuntimeException("导出配送异常失败,异常类型错误!");
            }
            //门店名
            line.add(0, Optional.ofNullable(poiInfo.get(item.getStoreId())).map(PoiInfoDto::getPoiName).orElse(EMPTY_STRING));

            //渠道
            line.add(1, DynamicOrderBizType.findOf(item.getOrderBizType()).getDesc());

            //渠道订单号
            line.add(2, item.getChannelOrderId().toString());

            //订单支付时间
            line.add(3, item.getPayTime().format(DateTimeFormatter.ofPattern(TimeUtil.SECOND_FORMAT)));

            //上报骑手
            line.add(4, item.getRiderAccountName());

            //异常上报类型
            line.add(5, deliveryExceptionEnum.getExceptionDesc());

            //异常上报时间
            line.add(6, item.getCreateTime().format(DateTimeFormatter.ofPattern(TimeUtil.SECOND_FORMAT)));

            //上报异常的二级分类，如车胎没气、顾客电话为空号；若为顾客修改地址等，则为空
            if (item.getExceptionType() != RiderDeliveryExceptionEnum.ADDRESS_IS_WRONG_500.getExceptionType() &&
                    item.getExceptionType() != RiderDeliveryExceptionEnum.CUSTOMER_CHANGE_ADDRESS_500.getExceptionType()) {
                line.add(7, deliveryExceptionEnum.getExceptionSubDesc());
            } else {
                line.add(7, EMPTY_STRING);
            }

            //修改后的地址
            if (item.getExceptionType() == RiderDeliveryExceptionEnum.CUSTOMER_CHANGE_ADDRESS_500.getExceptionType()) {
                line.add(8, Optional.ofNullable(item.getExceptionDescription())
                        .map(RiderDeliveryException.ExceptionDescription::getModifiedAddress)
                        .orElse(EMPTY_STRING));
            } else {
                line.add(8, EMPTY_STRING);
            }


            //用户真实地址
            if (item.getExceptionType() == RiderDeliveryExceptionEnum.ADDRESS_IS_WRONG_500.getExceptionType()) {
                line.add(9, Optional.ofNullable(item.getExceptionDescription())
                        .map(RiderDeliveryException.ExceptionDescription::getUserRealAddress)
                        .orElse(EMPTY_STRING));
            } else {
                line.add(9, EMPTY_STRING);
            }

            //导航距离:顾客真实地址与系统不符和顾客修改地址时填写
            if (item.getExceptionType() == RiderDeliveryExceptionEnum.ADDRESS_IS_WRONG_500.getExceptionType() ||
                    item.getExceptionType() == RiderDeliveryExceptionEnum.CUSTOMER_CHANGE_ADDRESS_500.getExceptionType()) {
                line.add(10, deliveryExceptionEnum.getExceptionSubDesc());
            } else {
                line.add(10, EMPTY_STRING);
            }

            //备注
            if (item.getExceptionDescription() != null && StringUtils.isNotBlank(item.getExceptionDescription().getComment())) {
                line.add(11, item.getExceptionDescription().getComment());
            } else {
                line.add(11, EMPTY_STRING);
            }

            return line;
        }).collect(Collectors.toList());
    }

    @Override
    public Integer getTaskType() {
        return DeliveryTaskTypeEnum.EXPORT_RIDER_DELIVERY_EXCEPTION_DETAILS.getCode();
    }
}
