package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.creditaudit;

import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryExtInfo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/4 19:19
 **/
public interface CreditAuditClient {
    void sendCreditAudit(List<Pair<Long/*picId*/, String/*url*/>> picInfos, Long deliveryOrderId, String userIP, Long riderAccountId,Long tenantId,Long storeId);

    void sendCreditAuditAutomaticPassMessage(Long deliveryOrderId,Long tenantId,Long storeId);
}
