package com.sankuai.meituan.shangou.empower.tms.delivery.rider.application;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.client.openapi.response.dto.OrderDetailDTO;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListViewIdConditionResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.enums.SortByEnum;
import com.meituan.shangou.saas.order.management.client.enums.SortFieldEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.platform.common.ResultCodeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.dms.base.infrastructure.service.IDeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryProofPhotoInfo;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.PageQueryDeliveryExceptionRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryCompletedStatisticRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryDeliveryExceptionByChannelOrderRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryDeliveryRiskControlOrderRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils.MccUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.PricingRouteInfoDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.LionConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.auth.RiderAuthClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.ParamCheckUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.convert.RiderQueryResponseConvertUtils.*;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.*;

/**
 * 自营骑手查询应用服务.
 *
 * <AUTHOR>
 * @since 2021/6/16 17:14
 */
@Service
@Slf4j
public class RiderQueryApplicationService {

	private static final Integer DEFAULT_BATCH_SIZE = 200;

	@Resource
	private RiderDeliveryOrderRepository riderDeliveryOrderRepository;
	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;
	@Resource
	private RiderDeliveryOrderLogRepository riderDeliveryOrderLogRepository;
	@Resource
	private RiderAuthClient riderAuthClient;
	@Resource
	private RiderLocationRepository riderLocationRepository;
	@Resource
	private RiderLocatingExceptionRepository riderLocatingExceptionRepository;
	@Resource
	private RiderDeliveryExceptionRepository riderDeliveryExceptionRepository;
	@Resource
	private RiderArrivalLocationRepository riderArrivalLocationRepository;

	@Resource
	private DeliveryRiskControlOrderRepository deliveryRiskControlOrderRepository;

	@Resource
	private DataReadyRecordRepository dataReadyRecordRepository;

	@Resource
	private PricingRouteInfoRepository pricingRouteInfoRepository;

	@Resource
	private OCMSQueryThriftService ocmsQueryThriftService;
	@Autowired
	private IDeliveryChannelApplicationService deliveryChannelApplicationService;

	private static List<Integer> IN_PROCESS_STATUS = Lists.newArrayList(DeliveryStatusEnum.INIT.getCode(),
			DeliveryStatusEnum.DELIVERY_LAUNCHED.getCode(),
			DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode(),
			DeliveryStatusEnum.RIDER_ASSIGNED.getCode(),
			DeliveryStatusEnum.RIDER_ARRIVED_SHOP.getCode(),
			DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode());


	@CatTransaction
	@MethodLog(logResponse = true)
	public PageResult<RiderDeliveryOrder> queryDeliveryOrders(Long tenantId,
	                                                          Long storeId,
	                                                          DeliveryStatusEnum status,
	                                                          PageRequest pageRequest) {
		Preconditions.checkNotNull(tenantId, "tenantId is null");
		Preconditions.checkNotNull(storeId, "storeId is null");
		Preconditions.checkNotNull(status, "status is null");
		Preconditions.checkNotNull(pageRequest, "pageRequest is null");

		return riderDeliveryOrderRepository.pageQueryRiderDeliveryOrdersTenant(pageRequest, storeId, status,tenantId);
	}

	@CatTransaction
	@MethodLog(logResponse = true)
	public PageResult<RiderDeliveryOrder> queryDeliveryOrders(Long tenantId,
	                                                          Long storeId,
	                                                          DeliveryStatusEnum status,
	                                                          Long riderAccountId,
	                                                          PageRequest pageRequest) {
		Preconditions.checkNotNull(tenantId, "tenantId is null");
		Preconditions.checkNotNull(storeId, "storeId is null");
		Preconditions.checkNotNull(status, "status is null");
		Preconditions.checkNotNull(riderAccountId, "riderAccountId is null");
		Preconditions.checkNotNull(pageRequest, "pageRequest is null");

		PageResult<RiderDeliveryOrder> pageResult = riderDeliveryOrderRepository.pageQueryRiderDeliveryOrders(pageRequest, storeId, status, riderAccountId,tenantId);

		return queryAndFillChangeFromRider(status, pageResult);
	}

	@CatTransaction
	@MethodLog(logResponse = true)
	public List<RiderDeliveryOrder> batchQueryDeliveryOrders(Long tenantId,
															  Long storeId,
															  DeliveryStatusEnum status) {
		Preconditions.checkNotNull(tenantId, "tenantId is null");
		Preconditions.checkNotNull(storeId, "storeId is null");
		Preconditions.checkNotNull(status, "status is null");

		return riderDeliveryOrderRepository.batchQueryDeliveryOrderWithTenant(storeId, status,tenantId);
	}

	@CatTransaction
	@MethodLog(logResponse = true)
	public List<RiderDeliveryOrder> batchQueryDeliveryOrders(Long tenantId,
															  Long storeId,
															  DeliveryStatusEnum status,
															  Long riderAccountId) {
		Preconditions.checkNotNull(tenantId, "tenantId is null");
		Preconditions.checkNotNull(storeId, "storeId is null");
		Preconditions.checkNotNull(status, "status is null");
		Preconditions.checkNotNull(riderAccountId, "riderAccountId is null");

		List<RiderDeliveryOrder> pageResult = riderDeliveryOrderRepository.batchQueryDeliveryOrder(storeId, status, riderAccountId,tenantId);

		return queryAndFillChangeFromRider(status, pageResult);
	}

	private List<RiderDeliveryOrder> queryAndFillChangeFromRider(DeliveryStatusEnum status, List<RiderDeliveryOrder> list) {
		try {
			if (status != DeliveryStatusEnum.RIDER_ASSIGNED && status != DeliveryStatusEnum.RIDER_TAKEN_GOODS) {
				return list;
			}
			if (CollectionUtils.isEmpty(list)) {
				return list;
			}

			List<Long> deliveryOrderIds = list.stream().map(RiderDeliveryOrder::getId).collect(Collectors.toList());
			Map<Long, List<RiderDeliveryOrderLog<RiderDeliveryOrderRiderChangeInfo>>> riderChangeLogsMap = riderDeliveryOrderLogRepository.getRiderChangeLogs(deliveryOrderIds);
			for (RiderDeliveryOrder riderDeliveryOrder : list) {
				List<RiderDeliveryOrderLog<RiderDeliveryOrderRiderChangeInfo>> riderChangeLogs = riderChangeLogsMap.get(riderDeliveryOrder.getId());
				fillChangeFromRider(riderDeliveryOrder, riderChangeLogs);
			}

			return list;
		} catch (Exception e) {
			log.error("queryAndFillChangeFromRider ex: {}", e.getMessage(), e);
			return list;
		}
	}

	private PageResult<RiderDeliveryOrder> queryAndFillChangeFromRider(DeliveryStatusEnum status, PageResult<RiderDeliveryOrder> pageResult) {
	    try {
			if (status != DeliveryStatusEnum.RIDER_ASSIGNED && status != DeliveryStatusEnum.RIDER_TAKEN_GOODS) {
				return pageResult;
			}
			if (CollectionUtils.isEmpty(pageResult.getInfo())) {
				return pageResult;
			}

			List<Long> deliveryOrderIds = pageResult.getInfo().stream().map(RiderDeliveryOrder::getId).collect(Collectors.toList());
			Map<Long, List<RiderDeliveryOrderLog<RiderDeliveryOrderRiderChangeInfo>>> riderChangeLogsMap = riderDeliveryOrderLogRepository.getRiderChangeLogs(deliveryOrderIds);
			for (RiderDeliveryOrder riderDeliveryOrder : pageResult.getInfo()) {
				List<RiderDeliveryOrderLog<RiderDeliveryOrderRiderChangeInfo>> riderChangeLogs = riderChangeLogsMap.get(riderDeliveryOrder.getId());
				fillChangeFromRider(riderDeliveryOrder, riderChangeLogs);
			}

			return pageResult;
		} catch (Exception e) {
	    	log.error("queryAndFillChangeFromRider ex: {}", e.getMessage(), e);
	    	return pageResult;
		}
	}

	private void fillChangeFromRider(RiderDeliveryOrder riderDeliveryOrder, List<RiderDeliveryOrderLog<RiderDeliveryOrderRiderChangeInfo>> riderChangeLogs) {
		if (CollectionUtils.isEmpty(riderChangeLogs)) {
			return;
		}

		// 过滤掉分配骑手的记录
		riderChangeLogs = riderChangeLogs.stream().filter(log -> log.getChangeInfo().getOldRider() != null).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(riderChangeLogs)) {
			return;
		}
		// 按changeTime倒序排列
		riderChangeLogs.sort((o1, o2) -> o2.getChangeTime().compareTo(o1.getChangeTime()));

		// 最近一条转单流水记录的新骑手为本骑手
		if (Objects.equals(riderChangeLogs.get(0).getChangeInfo().getNewRider().getRiderAccountId(), riderDeliveryOrder.getRiderInfo().getRiderAccountId())) {
			// 当前物流单的状态和转单前的物流单状态一致的时候
			if (riderChangeLogs.get(0).getChangeInfo().getOldDeliveryStatus() == riderDeliveryOrder.getStatus()) {
				riderDeliveryOrder.setChangeFromRider(riderChangeLogs.get(0).getChangeInfo().getOldRider());
			}
		}
	}

	@CatTransaction
	@MethodLog(logResponse = true)
	public PageResult<RiderDeliveryOrder> queryCompletedDeliveryOrders(Long tenantId, Long storeId, Long riderAccountId, PageRequest pageRequest) {

		return riderDeliveryOrderRepository.pageQueryCompletedDeliveryOrders(
				pageRequest, storeId, riderAccountId, LocalDate.now().minusDays(1).atStartOfDay(),tenantId
		);
	}


	public PageResult<RiderDeliveryOrder> queryCompletedDeliveryOrders(Long storeId, Long riderAccountId,
																	   PageRequest pageRequest,
																	   LocalDateTime deliveryDoneTimeBegin,
																	   LocalDateTime deliveryDoneTimeEnd,Long tenantId) {
		//分页查已送达列表
		return riderDeliveryOrderRepository.pageQueryCompletedDeliveryOrders(
				pageRequest, storeId, riderAccountId, deliveryDoneTimeBegin, deliveryDoneTimeEnd,tenantId);
	}


	public List<DeliveryRiskControlOrderDO> queryDeliveryRiskControlOrder(QueryDeliveryRiskControlOrderRequest request) {
		if(CollectionUtils.isEmpty(request.getViewOrderKeys())) {
			return Collections.emptyList();
		}

		List<String> viewOrderList = request.getViewOrderKeys().stream()
				.map(QueryDeliveryRiskControlOrderRequest.ViewOrderKey::getViewOrderId)
				.collect(Collectors.toList());
		List<DeliveryRiskControlOrderDO> deliveryRiskControlOrderDOS = deliveryRiskControlOrderRepository.queryByViewOrderIdList(viewOrderList);

		//再用orderBizType过滤一遍
		return deliveryRiskControlOrderDOS.stream()
				.filter(riskControlOrder -> request.getViewOrderKeys().contains(new QueryDeliveryRiskControlOrderRequest.ViewOrderKey(riskControlOrder.getOrderIdView(), riskControlOrder.getOrderBizType())))
				.collect(Collectors.toList());

	}
	public PageResult<RiderDeliveryOrder> queryInProgressDeliveryOrders(Long tenantId, Long storeId, Long riderAccountId, PageRequest pageRequest){
		PageResult<RiderDeliveryOrder> pageResult = riderDeliveryOrderRepository.pageQueryRiderDeliveryOrdersWithTenantId(pageRequest, storeId, Arrays.asList(DeliveryStatusEnum.RIDER_ASSIGNED,DeliveryStatusEnum.RIDER_TAKEN_GOODS), riderAccountId,0L,tenantId);
		return pageResult;
	}

	public List<RiderDeliveryOrder> queryAllRiderDeliveryOrder(Long tenantId, Long storeId, Long riderAccountId){
		List<RiderDeliveryOrder> resultList = Lists.newArrayList();
		List<RiderDeliveryOrder> selfRiderDeliveryOrders = riderDeliveryOrderRepository.querySelfRiderNewAndEndDeliveryOrders(
				tenantId, storeId,
				Lists.newArrayList(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER),
				riderAccountId);
		resultList.addAll(Optional.ofNullable(selfRiderDeliveryOrders).orElse(Lists.newArrayList()));

		List<RiderDeliveryOrder> selfInProcessRiderDeliveryOrders = riderDeliveryOrderRepository.querySelfRiderInProcessDeliveryOrders(
				tenantId, storeId,
				Arrays.asList(DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_TAKEN_GOODS),
				riderAccountId);
		resultList.addAll(Optional.ofNullable(selfInProcessRiderDeliveryOrders).orElse(Lists.newArrayList()));

		//TEMP: 三方配送还没融合，赋能的三方配送不在这里
		if (LionConfigUtils.checkIsDHTenant(tenantId) && DeliveryRiderMccConfigUtils.fusionSelfFeatureSwitchV2()) {
			appendThirdDeliveryOrders(tenantId, storeId, resultList);
		}
		return resultList;
	}

	private void appendThirdDeliveryOrders(Long tenantId, Long storeId, List<RiderDeliveryOrder> resultList) {
		List<RiderDeliveryOrder> thirdInProcessRiderDeliveryOrders = riderDeliveryOrderRepository.queryThirdRiderInProcessDeliveryOrders(
				tenantId, storeId,
				Arrays.asList(DeliveryStatusEnum.INIT,
						DeliveryStatusEnum.DELIVERY_LAUNCHED,
						DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER,
						DeliveryStatusEnum.RIDER_ASSIGNED,
						DeliveryStatusEnum.RIDER_ARRIVED_SHOP,
						DeliveryStatusEnum.RIDER_TAKEN_GOODS),
				null
		);

		//三方异常的可能处于各个状态下
		Integer hours = LionConfigUtils.getDHHours4JudgeDeliveryOrderTimeoutForAllThird();
		// 判断是否配送过时的时间点，若预计送达时间大于等于该时间点，则认为未超时.只要未超时的
		LocalDateTime timeoutPoint = LocalDateTime.now().minusHours(hours);
		List<RiderDeliveryOrder> exceptionRiderDeliveryOrder = riderDeliveryOrderRepository.queryThirdRiderInProcessDeliveryOrders(
				tenantId, storeId,
				Arrays.asList(DeliveryStatusEnum.DELIVERY_REJECTED,
						DeliveryStatusEnum.DELIVERY_FAILED,
						DeliveryStatusEnum.DELIVERY_CANCELLED),
				timeoutPoint
		);
		thirdInProcessRiderDeliveryOrders.addAll(
				Optional.ofNullable(exceptionRiderDeliveryOrder).orElse(Lists.newArrayList())
						.stream()
						.filter(RiderDeliveryOrder::getIsThirdException)
						.collect(Collectors.toList())
		);

		//要过滤掉订单状态取消 或者 完成的
		List<RiderDeliveryOrder> finalThirdRiderDeliveryOrders = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(thirdInProcessRiderDeliveryOrders)) {

			Map<Boolean, List<RiderDeliveryOrder>> isExceptionGroupMap = thirdInProcessRiderDeliveryOrders.stream().collect(Collectors.groupingBy(RiderDeliveryOrder::getIsThirdException));

			//没异常的只要进行中的
			List<RiderDeliveryOrder> noExceptionThirdDeliveryOrder = isExceptionGroupMap.getOrDefault(false, Lists.newArrayList())
					.stream()
					.filter(riderDeliveryOrder -> IN_PROCESS_STATUS.contains(riderDeliveryOrder.getStatus().getCode()))
					.collect(Collectors.toList());
			finalThirdRiderDeliveryOrders.addAll(noExceptionThirdDeliveryOrder);

			//异常的要过滤
			List<RiderDeliveryOrder> exceptionDeliveryOrderList = isExceptionGroupMap.getOrDefault(true, Lists.newArrayList());
			if (CollectionUtils.isNotEmpty(exceptionDeliveryOrderList)) {
				List<OCMSOrderVO> ocmsOrderVOS = queryTradeOrderInfoListByPartition(
						tenantId,
						exceptionDeliveryOrderList.stream().map(riderDeliveryOrder -> new ViewIdCondition(riderDeliveryOrder.getCustomerOrderKey().getOrderBizType(), riderDeliveryOrder.getCustomerOrderKey().getChannelOrderId())).collect(Collectors.toList())
				);
				Map<String, OCMSOrderVO> channelOrderIdVOMap = ocmsOrderVOS
						.stream()
						.collect(Collectors.toMap(
								OCMSOrderVO::getViewOrderId,
								Function.identity(),
								(older, newer) -> newer
						));
				exceptionDeliveryOrderList = exceptionDeliveryOrderList
						.stream()
						.filter(riderDeliveryOrder -> channelOrderIdVOMap.containsKey(riderDeliveryOrder.getCustomerOrderKey().getChannelOrderId()))
						//取消即终态
						.filter(riderDeliveryOrder -> !Objects.equals(channelOrderIdVOMap.get(riderDeliveryOrder.getCustomerOrderKey().getChannelOrderId()).getOrderStatus(), OrderStatusEnum.CANCELED.getValue()))
						//运单是取消，但送达了也不展示了
						.filter(riderDeliveryOrder -> !(Objects.equals(riderDeliveryOrder.getStatus().getCode(), DeliveryStatusEnum.DELIVERY_CANCELLED.getCode()) && Objects.equals(channelOrderIdVOMap.get(riderDeliveryOrder.getCustomerOrderKey().getChannelOrderId()).getOrderStatus(), OrderStatusEnum.COMPLETED.getValue())))
						.collect(Collectors.toList());
			}
			finalThirdRiderDeliveryOrders.addAll(exceptionDeliveryOrderList);
		}

		resultList.addAll(finalThirdRiderDeliveryOrders);
	}

	@CatTransaction
	@MethodLog(logResponse = true)
	public long countDeliveryOrder(Long tenantId, Long storeId, DeliveryStatusEnum status) {
		Preconditions.checkNotNull(tenantId, "tenantId is null");
		Preconditions.checkNotNull(storeId, "storeId is null");
		Preconditions.checkNotNull(status, "status is null");


		return riderDeliveryOrderRepository.countDeliveryOrderTenant(storeId, status,tenantId);
	}

	@CatTransaction
	@MethodLog(logResponse = true)
	public long countDeliveryOrder(Long tenantId, Long storeId, DeliveryStatusEnum status, Long riderAccountId) {
		Preconditions.checkNotNull(tenantId, "tenantId is null");
		Preconditions.checkNotNull(storeId, "storeId is null");
		Preconditions.checkNotNull(status, "status is null");
		Preconditions.checkNotNull(riderAccountId, "riderAccountId is null");

		return riderDeliveryOrderRepository.countDeliveryOrder(storeId, status, riderAccountId,tenantId);
	}

	@CatTransaction
	@MethodLog(logResponse = true)
	public long countTimeoutDeliveryOrder(Long tenantId, Long storeId, DeliveryStatusEnum status) {
		Preconditions.checkNotNull(tenantId, "tenantId is null");
		Preconditions.checkNotNull(storeId, "storeId is null");
		Preconditions.checkNotNull(status, "status is null");

		return riderDeliveryOrderRepository.countTimeoutDeliveryOrderTenant(storeId, status,tenantId);
	}

	@CatTransaction
	@MethodLog(logResponse = true)
	public long countTimeoutDeliveryOrder(Long tenantId, Long storeId, DeliveryStatusEnum status, Long riderAccountId) {
		Preconditions.checkNotNull(tenantId, "tenantId is null");
		Preconditions.checkNotNull(storeId, "storeId is null");
		Preconditions.checkNotNull(status, "status is null");
		Preconditions.checkNotNull(riderAccountId, "riderAccountId is null");


		return riderDeliveryOrderRepository.countTimeoutDeliveryOrder(storeId, status, riderAccountId,tenantId);
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public StoreDeliveryMonitorResponse queryDeliveryMonitoringInfo(Long tenantId, Long storeId, Integer appId) {
		ParamCheckUtils.nullCheck(tenantId, "tenantId is null");
		ParamCheckUtils.nullCheck(storeId, "storeId is null");
		ParamCheckUtils.nullCheck(appId, "appId is null");

		//查询该门店下所有骑手账号
		List<AccountInfo> riderAccountList = getStoreRiderAccountList(tenantId, storeId, appId);

		//全量查询骑手位置
		List<Long> riderAccountIdList = riderAccountList.stream().map(AccountInfo::getAccountId).collect(Collectors.toList());
		Map<Long, RiderLocationDetail> riderLocationDetailMap = riderLocationRepository.getStaffRiderLocations(riderAccountIdList);

		//查询没有位置的骑手定位异常原因
		Map<Long, Optional<RiderLocatingExceptionDetail>> riderLocatingExceptionMap = riderLocatingExceptionRepository.getRiderLocatingExceptions(riderAccountIdList
				.stream()
				.filter(accountId -> !riderLocationDetailMap.containsKey(accountId))
				.collect(Collectors.toList()));

		//按照状态查询出所有活跃运单
		List<RiderDeliveryOrder> riderDeliveryOrders = fullQueryActiveRiderDeliveryOrders(storeId,tenantId);

		//查三方骑手
		List<DeliveryOrderDO> thirdDeliveryOrderDOS = Lists.newArrayList();
		Map<ThirdRiderKey, ThirdRiderPoint> thirdRiderLocationMap = Maps.newHashMap();
		//查三方骑手运单与位置
		//NOTE：此处Lion用作上线期间的兼容，完成后可以删除
		if (Lion.getConfigRepository().getBooleanValue("enable.monitor.third", false)) {
			thirdDeliveryOrderDOS = fullQueryThirdActiveRiderDeliveryOrders(storeId,tenantId);
			List<String> thirdRiderKeyList = riderDeliveryOrders
					.stream()
					.filter(riderDeliveryOrder -> !Objects.equals(riderDeliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode()))
					.filter(riderDeliveryOrder -> Objects.nonNull(riderDeliveryOrder.getRiderInfo()) && StringUtils.isNotBlank(riderDeliveryOrder.getRiderInfo().getRiderName()) && StringUtils.isNotBlank(riderDeliveryOrder.getRiderInfo().getRiderPhone()))
					.map(riderDeliveryOrder -> new ThirdRiderKey(riderDeliveryOrder.getRiderInfo().getRiderName(), riderDeliveryOrder.getRiderInfo().getRiderPhone()).toKey())
					.collect(Collectors.toList());
			thirdRiderLocationMap = riderLocationRepository.getThirdRiderLocations(thirdRiderKeyList);
		}
		Set<Integer> carrierCodeSet = riderDeliveryOrders.stream().map(RiderDeliveryOrder::getDeliveryChannel).collect(Collectors.toSet());
		carrierCodeSet.addAll(thirdDeliveryOrderDOS.stream().map(DeliveryOrderDO::getDeliveryChannel).collect(Collectors.toSet()));
		List<DeliveryChannel> deliveryChannelList = deliveryChannelApplicationService.batchQueryDeliveryChannelByCarrierCodeSet(carrierCodeSet);
		Map<Integer, DeliveryChannel> deliveryChannelMap = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, Function.identity()));

		//组装结果
		return buildStoreDeliveryMonitorResponse(riderAccountList, riderLocationDetailMap, riderDeliveryOrders, riderLocatingExceptionMap, thirdRiderLocationMap, thirdDeliveryOrderDOS,deliveryChannelMap);

	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public StoreRiderAccountListResponse storeRiderAccountList(Long tenantId, Long storeId, Integer appId) {
		ParamCheckUtils.nullCheck(tenantId, "tenantId is null");
		ParamCheckUtils.nullCheck(storeId, "storeId is null");
		ParamCheckUtils.nullCheck(appId, "appId is null");

		List<AccountInfo> riderInfoList = riderAuthClient.queryRiderByPoiAndRoleIds(tenantId, storeId);

		return buildStoreRiderAccountListResponse(riderInfoList);
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public PageQueryDeliveryExceptionListResponse pageQueryDeliveryExceptionList(PageQueryDeliveryExceptionRequest request) {
		RiderDeliveryExceptionPageQueryConditions conditions = new RiderDeliveryExceptionPageQueryConditions(request);

		PageResult<RiderDeliveryException> riderDeliveryExceptionPageResult = riderDeliveryExceptionRepository.pageQueryRiderDeliveryException(conditions);

		return buildPageQueryDeliveryExceptionListResponse(riderDeliveryExceptionPageResult);
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public DeliveryExceptionResponse queryDeliveryExceptionByChannelOrder(QueryDeliveryExceptionByChannelOrderRequest request) {

		//获取渠道订单Id列表
		List<String> channelOrderIds = request.getChannelOrderInfoList().stream()
				.map(QueryDeliveryExceptionByChannelOrderRequest.ChannelOrderInfo::getChannelOrderId)
				.collect(Collectors.toList());

		//Map化 channelOrderId ==> OrderBiz
		Map<Long, Integer> channelOrderIdMap = request.getChannelOrderInfoList().stream()
				.collect(Collectors.toMap(
						channelOrderInfo -> Long.valueOf(channelOrderInfo.getChannelOrderId()),
						QueryDeliveryExceptionByChannelOrderRequest.ChannelOrderInfo::getOrderBizType,
						(k1, k2) -> k1));

		//获取异常列表
		List<RiderDeliveryException> riderDeliveryExceptions =
				riderDeliveryExceptionRepository.queryDeliveryExceptionByChannelOrderIds(request.getTenantId(),
						request.getStoreId(),channelOrderIds);

		//过滤orderBizType不符合的结果(防止不同渠道的channelOrderId相同)
		List<RiderDeliveryException> deliveryExceptions = riderDeliveryExceptions.stream().filter(riderDeliveryException -> channelOrderIdMap.containsKey(riderDeliveryException.getChannelOrderId()) &&
				channelOrderIdMap.get(riderDeliveryException.getChannelOrderId()).equals(riderDeliveryException.getOrderBizType()))
				.collect(Collectors.toList());

		return buildQueryDeliveryExceptionByChannelOrderIdResponse(deliveryExceptions);

	}



	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public QueryRiderArrivalLocationResponse queryRiderArrivalLocationInfo(Long tenantId, Long storeId, Long orderId) {
		ParamCheckUtils.nullCheck(tenantId,"tenantId is null");
		ParamCheckUtils.nullCheck(storeId,"storeId is null");
		ParamCheckUtils.nullCheck(orderId,"orderId is null");

		Optional<RiderDeliveryOrder> deliveryOrderOpt = riderDeliveryOrderRepository.getActiveDeliveryOrderForceMaster(tenantId, storeId, orderId);
		if (!deliveryOrderOpt.isPresent()) {
			return new QueryRiderArrivalLocationResponse(new Status(DELIVERY_ORDER_NOT_EXIST.getCode(),DELIVERY_ORDER_NOT_EXIST.getMessage()));
		}

		RiderArrivalLocation riderArrivalLocation = riderArrivalLocationRepository.getByDeliveryOrderId(deliveryOrderOpt.get().getId());

		return buildRiderArrivalLocationResponse(riderArrivalLocation);
	}

	public List<RiderDeliveryOrder> queryDeliveryOrderByOrderIdList(Long tenantId, Long storeId, List<Long> orderIds) {
		return riderDeliveryOrderRepository.batchQueryDeliveryOrder(tenantId, storeId, orderIds);
	}

	public List<RiderDeliveryOrder> batchQueryDeliveryOrderContainsUnActive(Long tenantId, Long storeId, List<Long> orderIds) {
		return riderDeliveryOrderRepository.batchQueryDeliveryOrderContainUnactivateOrder(tenantId, storeId, orderIds);
	}

	public List<RiderDeliveryOrder> queryDeliveryOrderByOrderIdList(List<Long> orderIds) {
		if(DeliveryRiderMccConfigUtils.getDeliveryQueryTenantSwitch()){
			return riderDeliveryOrderRepository.batchQueryDeliveryOrderByRoute(orderIds, true);
		}
		return riderDeliveryOrderRepository.batchQueryDeliveryOrder(orderIds);
	}

	public List<RiderDeliveryOrder> queryAllDeliveryOrderByOrderIdList(List<Long> orderIds) {
		return riderDeliveryOrderRepository.batchQueryDeliveryOrderByRoute(orderIds, false);
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public QueryDeliveryProofPhotoResponse queryDeliveryProofPhotoInfos(Long orderId) {
		ParamCheckUtils.nullCheck(orderId,"orderId is null");

		Optional<RiderDeliveryOrder> deliveryOrderOpt = Optional.empty();
		if(DeliveryRiderMccConfigUtils.getDeliveryQueryTenantSwitch()){
			deliveryOrderOpt = riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMasterWithRoute(orderId);
		}else {
			deliveryOrderOpt = riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMaster(orderId);
		}



		if (!deliveryOrderOpt.isPresent()) {
			return new QueryDeliveryProofPhotoResponse(new Status(FailureCodeEnum.DELIVERY_ORDER_NOT_EXIST.getCode(),
					FailureCodeEnum.DELIVERY_ORDER_NOT_EXIST.getMessage()),
					Collections.emptyList(), null, null, null);
		}

		RiderDeliveryOrder deliveryOrder = deliveryOrderOpt.get();

		if (deliveryOrder.getRiderDeliveryExtInfo() == null) {
			return new QueryDeliveryProofPhotoResponse(Collections.emptyList(), null, null);
		}

		if (deliveryOrder.getRiderDeliveryExtInfo().getDeliveryProofPhotoInfoList() == null) {
			return new QueryDeliveryProofPhotoResponse(Status.SUCCESS, Collections.emptyList(),
					deliveryOrder.getRiderDeliveryExtInfo().getSignType(),
					deliveryOrder.getRiderDeliveryExtInfo().getIsWeakNetwork(),
					Objects.equals(deliveryOrder.getRiderDeliveryExtInfo().getPickDeliverySplitTag(), true));
		}

		List<TDeliveryProofPhotoInfo> tDeliveryProofPhotoInfos = deliveryOrder.getRiderDeliveryExtInfo().getDeliveryProofPhotoInfoList()
				.stream().map(info -> {
					TDeliveryProofPhotoInfo tDeliveryProofPhotoInfo = new TDeliveryProofPhotoInfo();
					tDeliveryProofPhotoInfo.setAuditStatus(info.getAuditStatusEnum().getCode());
					tDeliveryProofPhotoInfo.setPicId(info.getPicId());
					tDeliveryProofPhotoInfo.setUrl(info.getPhotoUrl());
					return tDeliveryProofPhotoInfo;
				}).collect(Collectors.toList());

		QueryDeliveryProofPhotoResponse response = new QueryDeliveryProofPhotoResponse();
		response.setTDeliveryProofPhotoInfoList(tDeliveryProofPhotoInfos);
		response.setSignType(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getSignType).orElse(null));
		response.setIsWeakNetwork(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getIsWeakNetwork).orElse(null));
		response.setIsPickDeliverySplit(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getPickDeliverySplitTag).orElse(false));
		return response;
	}

	public List<PricingRouteInfoDO> queryPriceRouteInfo(Long tenantId, List<Long> deliveryIds) {
		if (CollectionUtils.isEmpty(deliveryIds)) {
			return Collections.emptyList();
		}
		return pricingRouteInfoRepository.queryByDeliveryOrderIds(tenantId, deliveryIds);
	}

	public List<RiderDeliveryOrder> queryByIdList(List<Long> deliveryIds,Long tenantId,Long storeId) {
		if (CollectionUtils.isEmpty(deliveryIds)) {
			return Collections.emptyList();
		}
		return riderDeliveryOrderRepository.batchQueryDeliveryOrderByIds(deliveryIds,tenantId,storeId);
	}

	public QueryCompletedStatisticResponse queryCompletedStatistic(QueryCompletedStatisticRequest request) {
		LocalDate date = LocalDate.parse(request.getDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));

		//查已送达运单数
		long completedOrderCnt = riderDeliveryOrderRepository.countRiderCompletedDeliveryOrder(request.getTenantId(),
				request.getStoreId(),
				request.getRiderAccountId(),
				LocalDateTime.of(date, LocalTime.MIN),
				LocalDateTime.of(date, LocalTime.MAX));


		//查一元单数
		long oneYuanOrderCnt = riderDeliveryOrderRepository.countRiderCompletedAndOneYuanOrder(request.getTenantId(),
				request.getStoreId(),
				request.getRiderAccountId(),
				LocalDateTime.of(date, LocalTime.MIN),
				LocalDateTime.of(date, LocalTime.MAX));


		//查风控单数
		long dt = Long.parseLong(date.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
		boolean isReady = dataReadyRecordRepository.checkIsReady("delivery_risk_control_order", dt);
		Long riskControlCnt = null;
		if (isReady) {
			riskControlCnt = deliveryRiskControlOrderRepository.countByDtAndAccount(dt, request.getRiderAccountId());
		}

		return new QueryCompletedStatisticResponse(Status.SUCCESS, completedOrderCnt, isReady, riskControlCnt, oneYuanOrderCnt);

	}

	/**
	 * 获取该门店下的所有骑手账号
	 * @param tenantId 租户id
	 * @param storeId 门店id
	 * @param appId appId
	 * @return List<AccountInfo>
	 */
	private List<AccountInfo> getStoreRiderAccountList(Long tenantId, Long storeId, Integer appId) {
		ParamCheckUtils.nullCheck(tenantId, "tenantId is null");
		ParamCheckUtils.nullCheck(storeId, "storeId is null");
		ParamCheckUtils.nullCheck(appId, "appId is null");

		try {
			return riderAuthClient.queryRiderByPoiAndRoleIds(tenantId, storeId);
		} catch (Exception e) {
			log.error("getStoreRiderAccountList method error, tenantId: {}, storeId: {}, appId: {}",
					storeId, storeId, appId, e);
			return Collections.emptyList();
		}

	}

	/**
	 * 查询出该门店下所有活跃状态运单
	 * 自配（等待分配骑手、骑手已接单、骑手已取货）
	 * 三方（）
	 * @param storeId 门店id
	 * @return 该门店下所有活跃状态运单列表
	 */
	private List<RiderDeliveryOrder> fullQueryActiveRiderDeliveryOrders(Long storeId,Long tenantId) {
		List<RiderDeliveryOrder> batchQueryResult;
		List<RiderDeliveryOrder> result = new ArrayList<>();

		Long lastQueryResultMaxId = 0L;
		List<DeliveryStatusEnum> statusEnumList = Arrays.asList(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER,
																	 DeliveryStatusEnum.RIDER_ASSIGNED,
																	 DeliveryStatusEnum.RIDER_TAKEN_GOODS);

		int batchSize = DEFAULT_BATCH_SIZE;
		int maxCount = 0;
		int maxNum = MccUtils.maxWhileNum();
		do {
			batchQueryResult = riderDeliveryOrderRepository.batchQueryDeliveryOrder(storeId, statusEnumList, lastQueryResultMaxId, batchSize,tenantId);
			result.addAll(batchQueryResult);
			if (CollectionUtils.isNotEmpty(batchQueryResult)) {
				lastQueryResultMaxId = batchQueryResult.get(batchQueryResult.size() - 1).getId();
			}
			if(maxCount++>maxNum){
				Cat.logEvent("while.break.cat","RiderQueryApplicationService.fullQueryActiveRiderDeliveryOrders");
				break;
			}
		} while (batchQueryResult.size() == batchSize);

		return result;
	}

	//最终决定三方与自配分开查：本来这里就会分批次去查，强行用or去关联会导致SQL变得较为复杂，不如干脆直接分开查。大数据量的情况下不会赠加多少次查询
	private List<DeliveryOrderDO> fullQueryThirdActiveRiderDeliveryOrders(Long storeId,Long tenantId) {
		List<DeliveryOrderDO> batchQueryResult;
		List<DeliveryOrderDO> result = new ArrayList<>();

		//最终决定三方与自配分开查：本来这里就会分批次去查，强行用or去关联会导致SQL变得较为复杂，不如干脆直接分开查。大数据量的情况下不会赠加多少次查询
		Long newLastQueryResultMaxId = 0L;
		List<DeliveryStatusEnum> thirdStatusEnumList = Arrays.asList(DeliveryStatusEnum.INIT, DeliveryStatusEnum.DELIVERY_LAUNCHED,
				DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER,
				DeliveryStatusEnum.RIDER_ASSIGNED,
				DeliveryStatusEnum.RIDER_ARRIVED_SHOP,
				DeliveryStatusEnum.RIDER_TAKEN_GOODS);
		int batchSize = DEFAULT_BATCH_SIZE;
		int maxCount = 0;
		int maxNum = MccUtils.maxWhileNum();
		do {
			batchQueryResult = riderDeliveryOrderRepository.batchQueryThirdDeliveryOrder(storeId, thirdStatusEnumList, newLastQueryResultMaxId, batchSize,tenantId);
			result.addAll(batchQueryResult);
			if (CollectionUtils.isNotEmpty(batchQueryResult)) {
				newLastQueryResultMaxId = batchQueryResult.get(batchQueryResult.size() - 1).getId();
			}
			if(maxCount++>maxNum){
				Cat.logEvent("while.break.cat","RiderQueryApplicationService.fullQueryThirdActiveRiderDeliveryOrders");
				break;
			}
		} while (batchQueryResult.size() == batchSize);


		return result;
	}

	/**
	 * 校验门店不是自营配送门店
	 *
	 * @param tenantId 租户 ID
	 * @param storeId  门店 ID
	 */
	private boolean nonSelfDeliveryPoi(long tenantId, long storeId) {
		Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(tenantId, storeId);
		return !opDeliveryPoi.isPresent() || DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY != opDeliveryPoi.get().getDeliveryPlatform();
	}

	@CatTransaction
	@MethodLog(logResponse = true)
	public List<RiderDeliveryOrder> queryDeliveryByTimeIntervalAndStatus(Long tenantId,
																		 List<DeliveryStatusEnum> statusEnumList,
																		 LocalDateTime start, LocalDateTime end,List<Long> storeIdList) {
		long total = riderDeliveryOrderRepository.countRiderDeliveryOrder(tenantId, statusEnumList, start, end, storeIdList, null);
		log.info("queryDeliveryByTimeIntervalAndStatus takes {} deliverOrder Record", total);
		if (total == 0) {
			return new ArrayList<>();
		}
		int page = 1;
		int pageSize = 50;
		PageRequest pageRequest = new PageRequest(page, pageSize);
		List<RiderDeliveryOrder> orderList = new ArrayList<>();
		long maxPage = total % pageSize == 0 ? total / pageSize : total / pageSize + 1;
		for (; page <= maxPage; page++) {
			orderList.addAll(riderDeliveryOrderRepository.queryRiderDeliveryOrderByPage(pageRequest,
					tenantId, statusEnumList, start, end, storeIdList, null));
			pageRequest.setPage(page);
		}
		return orderList;
	}

	/**
	 * 注意：
	 * 该方法没有返回历史的rider！如果后续要修改返回rider,请联系@jianglilin02 或 @linxiaorui
	 */
	@CatTransaction
	@MethodLog(logResponse = true)
	public List<RiderDeliveryOrder> queryDeliveryOrdersByPoiAndStatusList(Long tenantId, List<Long> storeIdList, List<Integer> statusList) {

		return riderDeliveryOrderRepository.queryDeliveryOrdersByPoiAndStatusList(tenantId, storeIdList, statusList);
	}

	public List<OCMSOrderVO> queryTradeOrderInfoListByPartition(long tenantId, List<ViewIdCondition> viewIdConditionList) {
		try {
			if (CollectionUtils.isEmpty(viewIdConditionList)) {
				return Lists.newArrayList();
			}
			List<OCMSOrderVO> ocmsOrderList = Lists.newArrayList();
			List<List<ViewIdCondition>> partition = Lists.partition(viewIdConditionList, LionConfigUtils.getPartitionSize());
			for (List<ViewIdCondition> partitionTradeOrderKeys : partition) {
				OCMSListViewIdConditionRequest viewIdConditionRequest = buildOCMSListViewIdConditionRequestByRiderOrders(tenantId, partitionTradeOrderKeys);
				log.info("RiderDeliveryServiceWrapper call OcmsQueryThriftService.queryOrderByViewIdCondition request:{}",
						viewIdConditionRequest);
				OCMSListViewIdConditionResponse viewIdConditionResponse =
						ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
				log.info("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition response:{}",
						viewIdConditionResponse);
				if (viewIdConditionResponse.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode()) {
					log.warn("获取订单详情失败, viewOrderId:{}",
							viewIdConditionRequest.getViewIdConditionList().stream().map(ViewIdCondition::getViewOrderId).collect(Collectors.toList()));
					throw new CommonRuntimeException(viewIdConditionResponse.getStatus().getMessage());
				}
				if (CollectionUtils.isNotEmpty(viewIdConditionResponse.getOcmsOrderList())) {
					ocmsOrderList.addAll(viewIdConditionResponse.getOcmsOrderList());
				}
			}

			return ocmsOrderList;
		} catch (Exception e) {
			log.error("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition error", e);
			throw new CommonRuntimeException("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition error", e);
		}
	}

	/**
	 * 根据骑手运单，构造查询订单信息的请求.
	 *
	 * @param viewIdConditionList 查询运单条件
	 */
	private OCMSListViewIdConditionRequest buildOCMSListViewIdConditionRequestByRiderOrders(long tenantId, List<ViewIdCondition> viewIdConditionList) {
		OCMSListViewIdConditionRequest request = new OCMSListViewIdConditionRequest();
		request.setTenantId(tenantId);
		request.setViewIdConditionList(viewIdConditionList);
		request.setSortField(SortFieldEnum.ORDER_CREATE_TIME);
		request.setSort(SortByEnum.DESC);
		return request;
	}
}
