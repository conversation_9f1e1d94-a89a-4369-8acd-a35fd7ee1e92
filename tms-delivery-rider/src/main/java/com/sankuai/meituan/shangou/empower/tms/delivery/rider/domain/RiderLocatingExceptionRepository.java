package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface RiderLocatingExceptionRepository {
    /**
     * 把骑手定位异常写入缓存
     */
    void saveRiderLocatingException(RiderLocatingExceptionDetail riderLocatingException);

    /**
     * 批量获取骑手定位异常
     */
    Map<Long, Optional<RiderLocatingExceptionDetail>> getRiderLocatingExceptions(List<Long> riderAccountIdList);

    /**
     * 获取单个骑手定位异常
     */
    Optional<RiderLocatingExceptionDetail> getRiderLocatingException(Long riderAccountId);

    /**
     * 清除单个骑手的异常
     */
    void deleteRiderLocatingException(Long riderAccountId);
}
