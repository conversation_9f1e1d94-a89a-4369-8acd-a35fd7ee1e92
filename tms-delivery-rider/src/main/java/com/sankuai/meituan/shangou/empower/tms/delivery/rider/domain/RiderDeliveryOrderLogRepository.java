package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;

import java.util.List;
import java.util.Map;

public interface RiderDeliveryOrderLogRepository {
    /**
     * 获取骑手改派流水记录
     */
    Map<Long, List<RiderDeliveryOrderLog<RiderDeliveryOrderRiderChangeInfo>>> getRiderChangeLogs(List<Long> deliveryOrderIds);
}
