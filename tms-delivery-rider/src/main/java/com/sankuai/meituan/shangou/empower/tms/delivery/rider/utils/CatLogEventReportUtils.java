package com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * 埋点统计工具
 */
@Slf4j
public class CatLogEventReportUtils {
    /**
     * 埋点上报: 自营骑手位置同步
     * @param pointOpt
     */
    public static void reportSelfDeliveryRiderLocationSyncLogEvent(Long tenantId, Integer deliveryChannelId, Optional<CoordinatePoint> pointOpt){
        try {
            //过滤非歪马
            if (!DeliveryRiderMccConfigUtils.checkIsDHTenant(tenantId)) {
                return;
            }

            //过滤非自配运单
            if (!Objects.equals(deliveryChannelId, DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())) {
                return;
            }

            if (pointOpt.isPresent()) {
                Cat.logEvent("DH.rider.location","sync.to.customer.terminal", Event.SUCCESS,"");
            } else {
                Cat.logEvent("DH.rider.location","sync.to.customer.terminal", "FAIL","");
            }

            if (pointOpt.isPresent() && Objects.nonNull(pointOpt.get().getTimestamp()) && TimeUtil.toMilliSeconds(LocalDateTime.now()) - pointOpt.get().getTimestamp() < DeliveryRiderMccConfigUtils.getRealTimePositionDuration() * 1000)  {
                Cat.logEvent("DH.rider.location","sync.to.customer.terminal.with.realtime.position", Event.SUCCESS,"");
            } else {
                Cat.logEvent("DH.rider.location","sync.to.customer.terminal.with.realtime.position", "FAIL","");
            }
        } catch (Exception e) {
            log.error("埋点失败", e);
        }
    }

    /**
     * 埋点上报: 从redis缓存中写入或查询自营骑手位置
     * @param name
     * @param success
     */
    public static void reportSelfDeliveryRiderLocationSaveOrGetLogEvent(String name,  boolean success) {
        if (success) {
            Cat.logEvent("DH.rider.location", name, Event.SUCCESS, "");
        } else {
            Cat.logEvent("DH.rider.location", name, "FAIL", "");
        }
    }
}
