package com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/12/8
 * @Desc tms-delivery-rider模块用到的Mcc配置
 */

@Slf4j
public class DeliveryRiderMccConfigUtils {
    /**
     * 获取定义为骑手的角色id列表
     * @return 骑手的accountId列表
     */
    public static List<Long> getRiderRoleIdList(){
        try {
            String riderRoleIdListStr = Lion.getConfigRepository().get("rider.role.id.list", StringUtils.EMPTY);
            if (StringUtils.isBlank(riderRoleIdListStr)) {
                return Collections.emptyList();
            }
            return Arrays.stream(riderRoleIdListStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getRiderRoleIdList error", e);
            return Collections.emptyList();
        }
    }

    /**
     * 歪马骑手接单超时时长配置，单位：分钟。从配送任务下发后开始计时
     */
    public static int getDHRiderAssignTimeoutMinutes() {
        return Lion.getConfigRepository().getIntValue("drunk.horse.rider.assigned.timeout.minutes", 1);
    }

    /**
     * 歪马骑手取货超时时长配置，单位：分钟。从配送任务下发后开始计时
     */
    public static int getDHRiderTakenGoodsTimeoutMinutes() {
        return Lion.getConfigRepository().getIntValue("drunk.horse.rider.taken.goods.timeout.minutes", 4);
    }


    /**
     * toB: 实时订单配送超时时长配置，单位：分钟。从超过预计送达时间开始计时
     */
    public static int getInTimeOrderDeliveryTimeOutDuration2B() {
        return Lion.getConfigRepository().getIntValue("delivery.timeout.duration-in-minutes.inTime.toB", 0);
    }


    /**
     * toB: 预约订单配送超时时长配置，单位：分钟。从超过预计送达时间开始计时
     */
    public static int getBookingOrderDeliveryTimeOutDuration2B() {
        return Lion.getConfigRepository().getIntValue("delivery.timeout.duration-in-minutes.booking.toB", 5);
    }

    /**
     * 实时单配送时长监控指标: x分钟内送达
     */
    public static List<Integer> getInTimeOrderDeliveryDurationMonitorMetric() {
        return Lion.getConfigRepository().getList("delivery.monitor.metric.duration-in-minutes.inTime", Integer.class, Lists.newArrayList(15, 25));
    }

    /**
     * 降级开关:是否计算骑手到刻位置与收货地址间导航距离
     */
    public static Boolean getCalNavigationDistanceSwitch() {
        return Lion.getConfigRepository().getBooleanValue("DH.calculate.arrival-time.navigation.distance", true);
    }

    /**
     * 降级开关:批量上报骑手定位日志
     */
    public static Boolean getBatchPostRiderLocatingLogSwitch() {
        return Lion.getConfigRepository().getBooleanValue("batch.post.rider.locating.log.switch", true);
    }

    /**
     * 异常上报最大次数
     */
    public static Integer getExceptionReportMaxTimes() {
        return Lion.getConfigRepository().getIntValue("delivery.exception.report.max.times", 3);
    }

    /**
     * 推送爆单推送消息时，获取值班人列表
     */
    public static List<String> getDutyPersonMisIdList() {
        return Lion.getConfigRepository().getList("order.overload.duty.person.misIds", String.class,
                Arrays.asList("zhanyu03", "lilin13"));
    }

    /**
     * 歪马送酒租户id
     */
    @SuppressWarnings({"UnstableApiUsage"})
    public static List<String> getDHTenantIdList(){
        String tenantIdStr = Lion.getConfigRepository().get("drunk.horse.tenant.id.list", "1000395");
        if(org.apache.commons.lang.StringUtils.isBlank(tenantIdStr)){
            return Collections.emptyList();
        }
        return Splitter.on(",").splitToList(tenantIdStr);
    }

    /**
     * 是否是歪马租户
     */
    public static Boolean checkIsDHTenant(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            return false;
        }
        List<String> dhTenantIdList = getDHTenantIdList();
        return dhTenantIdList.contains(tenantId.toString());
    }

    /**
     * 自配送融合降级开关 false 未降级 true 降级
     * @return
     */
    public static Boolean fusionSelfDegreeSwitch(){
        return Lion.getConfigRepository().getBooleanValue("fusion.self.degree.switch", false);
    }

    /**
     * 自配送融合v2降级开关 false 未降级 true 降级
     * @return
     */
    public static Boolean fusionSelfDegreeSwitchV2(){
        return Lion.getConfigRepository().getBooleanValue("fusion.self.degree.switch.v2", false);
    }

    /**
     * 融合依赖前端开关
     * @return
     */
    public static Boolean fusionSelfFeatureSwitchV2(){
        return Lion.getConfigRepository().getBooleanValue("fusion.self.feature.switch.v2", true);
    }


    /**
     * 自配送push权限查询次数
     * @return
     */
    public static int getSelfDeliveryQueryAuthCount(){
        return Lion.getConfigRepository().getIntValue("self.query.auth.count", 5);
    }

    public static Boolean getFillRiderLocationSwitch() {
        return Lion.getConfigRepository().getBooleanValue("fill.rider.location.switch", true);
    }

    /**
     * 送达图片合规检查自动通过时间 单位:毫秒
     */
    public static Integer getCreditAuditAutoPassDuration() {
        return Lion.getConfigRepository().getIntValue("credit.audit.auto.pass.duration", 18 * 1000);
    }

    /**
     * 送达拍照短信模版-跳转链接
     */
    public static String getDeliveryPhotoSmsTemplatePageLink() {
        return Lion.getConfigRepository().get("delivery.photo.sms.template.page.link", "dpurl.cn/xw5u63Oz");
    }


    /**
     * 送达拍照微信模版-跳转链接
     */
    public static String getDeliveryPhotoWxTemplatePageLink() {
        return Lion.getConfigRepository().get("delivery.photo.wx.template.page.link", "/market/pages/order-detail/index?orderViewId={0}");
    }

    /**
     * 获取超时发券开关
     */
    public static Boolean getIssueCouponsSwitch() {
        return Lion.getConfigRepository().getBooleanValue("delivery.timeout.issue.coupons.switch", true);
    }


    /**
     * 获取骑手送达拍照开关
     */
    public static Boolean getDeliveryProofPhotoSwitch() {
        return Lion.getConfigRepository().getBooleanValue("rider.delivery.proof.photo.switch", true);
    }

    /**
     * 是否是送达拍照新流程灰度门店
     */
    public static Boolean isNewDeliveryCompleteGrayStore(Long storeId) {
        if (storeId == null) {
            return false;
        }
        List<Long> grayStoreList = Lion.getConfigRepository().getList("delivery.photo.configurable.gray.store", Long.class);

        if (CollectionUtils.isEmpty(grayStoreList)) {
            return false;
        }

        if (grayStoreList.size() == 1 && grayStoreList.get(0).equals(-1L)) {
            return true;
        }

        return grayStoreList.contains(storeId);
    }

    /**
     * 地图平台迁移开关
     */
    public static Boolean isMigrateMapPlatformGrayStore(Long storeId) {
        if(storeId == null) {
            return false;
        }

        String grayStoreIdStr = Lion.getConfigRepository().get("migrate.map.platform.switch", "");
        if (StringUtils.isBlank(grayStoreIdStr)) {
            return false;
        }

        if ("-1".equals(grayStoreIdStr)) {
            return true;
        }

        return Arrays.asList(grayStoreIdStr.split(",")).contains(String.valueOf(storeId));
    }

    public static double getBrokenLineCoefficient() {
        return Lion.getConfigRepository().getDoubleValue("broken.line.coefficient", 1.45);
    }


    /**
     * 单位：秒
     * @return
     */
    public static Integer getRealTimePositionDuration() {
        return Lion.getConfigRepository().getIntValue("real.time.position.duration",30);
    }


    public static Boolean getLocateLogPostSwitch() {
        return Lion.getConfigRepository().getBooleanValue("locate.post.switch",true);
    }

    public static Integer getNotFreshThreshold() {
        return Lion.getConfigRepository().getIntValue("not.fresh.threshold",60 * 1000);
    }

    /**
     * 新供给处理自配送骑手信息变更功能开关
     */
    public static boolean getProcessSelfDeliveryRiderChangeSwitch(){
        return Lion.getConfigRepository().getBooleanValue("process.self.delivery.rider.change.switch", true);
    }

    public static Boolean getDeliveryQueryTenantSwitch(Long tenantId){
        if(tenantId == null || tenantId<=0){
            log.info("getPickSelectQueryJoinSwitch tenantId:{}",tenantId);
            return false;
        }
        return getDeliveryQueryTenantSwitch();
    }

    public static Boolean getDeliveryQueryTenantSwitch(){
        return Lion.getConfigRepository().getBooleanValue("delivery.query.tenant.switch", true);
    }

    public static boolean isFillRiderLocationGrayStore(Long storeId) {
        if(storeId == null) {
            return false;
        }

        List<Long> grayStoreIds = Lion.getConfigRepository().getList("fill.rider.location.gray.store.list", Long.class);
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(grayStoreIds.get(0), -1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }
}
