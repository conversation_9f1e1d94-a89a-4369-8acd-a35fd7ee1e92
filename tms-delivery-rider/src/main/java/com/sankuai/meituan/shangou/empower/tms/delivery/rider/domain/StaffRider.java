package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/12
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StaffRider extends Rider {

    public static final long DEFAULT_ACCOUNT_ID = 0;

    /**
     * 骑手账号id，默认0
     */
    private Long riderAccountId;

    public StaffRider(String riderName, String riderPhone, String riderPhoneToken, Long riderAccountId) {
        super(riderName, riderPhone, riderPhoneToken);
        this.riderAccountId = riderAccountId;
    }
}
