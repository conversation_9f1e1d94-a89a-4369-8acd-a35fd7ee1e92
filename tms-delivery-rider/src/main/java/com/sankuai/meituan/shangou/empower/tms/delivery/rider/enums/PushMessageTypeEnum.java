package com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums;

/**
 * <AUTHOR>
 * @since 2023/1/11 15:56
 **/
public enum PushMessageTypeEnum {
    DO_NOT_PUSH_MESSAGE(0, "不推送消息"),
    PUSH_FIRST_ALARM_MESSAGE(1, "推送一级爆单告警消息"),
    PUSH_SECOND_ALARM_MESSAGE(2, "推送二级爆单告警消息"),
    PUSH_THIRD_ALARM_MESSAGE(3, "推送三级爆单告警消息"),
    PUSH_SERIOUS_ALARM_MESSAGE(4, "推送严重爆单告警消息"),

    PUSH_ALARM_MITIGATE_MESSAGE(10, "推送告警缓解消息");



    private int code;
    private String desc;

    PushMessageTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PushMessageTypeEnum enumOf(int value) {
        for (PushMessageTypeEnum each : values()) {
            if (each.getCode() == value) {
                return each;
            }
        }
        return null;
    }

    public static PushMessageTypeEnum map2PushMessageTypeEnum(DeliveryBusyLevelEnum deliveryBusyLevelEnum) {
        switch (deliveryBusyLevelEnum) {
            case FIRST_LEVEL_BUSY:
                return PUSH_FIRST_ALARM_MESSAGE;
            case SECOND_LEVEL_BUSY:
                return PUSH_SECOND_ALARM_MESSAGE;
            case THIRD_LEVEL_BUSY:
                return PUSH_THIRD_ALARM_MESSAGE;
            case SERIOUS_BUSY:
                return PUSH_SERIOUS_ALARM_MESSAGE;
            default:
                throw new IllegalArgumentException("爆单等级不合法");

        }
    }
}
