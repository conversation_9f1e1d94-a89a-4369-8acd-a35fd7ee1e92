package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.push;

import java.util.Map;

/**用户消息推送
 * <AUTHOR>
 * @since 2023/5/6 17:17
 **/
public interface ClientPushClient {
    /**
     * 给微商城用户推送微信模版消息
     * @param userId
     * @param tenantId
     * @param templateId
     * @param dataContent
     * @param pageLink
     */
    void sendTemplateMessage(Long userId, Long tenantId, String templateId, Map<String, String> dataContent, String pageLink);

    /**
     * 给微商城用户推送短信消息
     * @param userId
     * @param tenantId
     * @param templateId
     * @param dataContent
     */
    void sendSms(Long userId, Long tenantId, String templateId, Map<String, String> dataContent);
}
