package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.strategy;

import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.cmd.MergeOrderOperateCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/9/7 23:54
 **/
public interface MergeOrderOperationCreateStrategy {
    List<MergeOrderOperateCmd> buildMergeOrderOperateCmd(RiderDeliveryOrder riderDeliveryOrder);
}
