package com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "骑手维度统计数据",
        authors = {
                "yujing10"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DeliveryRiderStatistics {

    @FieldDoc(description = "骑手id")
    public Long riderAccountId;

    @FieldDoc(description = "配送中（骑手已取货）订单量")
    public Integer delivering;

    @FieldDoc(description = "配送中（骑手已取货）考核超时订单量")
    public Integer deliveringJudgeTimeout;

    @FieldDoc(description = "拣货中（骑手已接单）订单量")
    public Integer picking;

    @FieldDoc(description = "拣货中（骑手已接单）考核超时订单量")
    public Integer pickingJudgeTimeout;

    @FieldDoc(description = "拣货中（骑手已接单）超时订单量 V2版本")
    private Integer pickingJudgeTimeoutV2;

    @FieldDoc(description = "配送中（骑手已取货）超时订单量 V2版本")
    private Integer deliveringJudgeTimeoutV2;
}
