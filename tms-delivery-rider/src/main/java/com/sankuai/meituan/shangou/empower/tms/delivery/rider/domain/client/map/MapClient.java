package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.map;

import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RouteInfoDTO;

/**
 * <AUTHOR>
 * @date 2021/12/27
 * @email jianglilin02@meituan
 */
public interface MapClient {
    /**
     * 查询骑行路径距离
     * @param req 查询骑行路径距离请求
     * @return 距离（单位：米）
     */
    Result<Double> queryRidePathDistance(MapRouteRequest req);

	/**
	 * 根据详细地址查询坐标
	 *
	 * @param address 详细地址
	 * @return 坐标点
	 */
	Result<CoordinatePoint> queryCoordinatesByDetailAddress(String address);


	/**
	 * 查询骑行路径距离
	 *
	 * @param
 	 * @return 坐标点
	 */
	Double queryRideRidingDistance4DrunkHorse(MapRouteRequest req);

	/**
	 * 查询骑行路径距离
	 *
	 * @param
	 * @return 坐标点
	 */
	RouteInfoDTO queryRideRidingInfo4DrunkHorse(MapRouteRequest req);
}
