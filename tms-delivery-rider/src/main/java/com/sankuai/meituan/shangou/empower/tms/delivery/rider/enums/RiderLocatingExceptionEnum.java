package com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums;

import org.apache.commons.lang.enums.Enum;

import javax.swing.*;

public enum RiderLocatingExceptionEnum{

    QUERY_IN_PROGRESS_ORDER_FAIL(1, "调用后端查询进行中运单接口失败"),
    TURN_OFF_BAI_CHUAN_LOCATING_SERVICE(2,"用户在牵牛花-隐私中关闭了定位服务"),
    TURN_OFF_SYSTEM_LOCATING_PERMISSION(3, "隐私SDK鉴权失败/用户未开启系统定位权限"),
    SDK_GET_LOCATION_FAIL(4, "定位SDK获取定位失败"),
    POST_RIDER_LOCATION_FAIL(5, "前端向后端上报位置接口失败"),
    RIDER_KILL_APPLICATION(6,"骑手关闭应用"),
    RIDER_SWITCH_STORE(7, "骑手切换门店"),
    RIDER_LOGGED_OUT(8, "骑手退出登录"),
    UNKNOWN_EXCEPTION(9, "未知异常"),
    RIDER_FINISH_DELIVERY_MORE_THAN_45_MINUTES(10, "骑手完成配送后45分钟停止定位");

    private int value;
    private String desc;

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    RiderLocatingExceptionEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static RiderLocatingExceptionEnum enumOf(int value) {
        for (RiderLocatingExceptionEnum each : values()) {
            if (each.getValue() == value) {
                return each;
            }
        }
        return null;
    }
}
