package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.wrapper;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.exception.CommonErrException;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderFinanceModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderFinanceQueryRequest;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderFinanceQueryResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderFinanceThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.configuration.TMSConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Rhino
@Slf4j
public class OrderThriftServiceWrapper {

    @Resource
    private BizOrderFinanceThriftService bizOrderFinanceThriftService;

    /**
     * 查询财务订单商品总金额
     */
    @Degrade(rhinoKey = "OrderThriftServiceWrapper.queryOrderFinance",
            fallBackMethod = "queryOrderFinanceFallback",
            timeoutInMilliseconds = 5000)
    public Integer queryOrderFinance(Long tenantId, Long shopId, Long orderId) {
        BizOrderFinanceQueryRequest request = new BizOrderFinanceQueryRequest();
        request.setTenantId(tenantId);
        request.setShopId(shopId);
        request.setOrderId(orderId);
        try {
            log.info("queryOrderFinance request={}", request);
            BizOrderFinanceQueryResponse response = bizOrderFinanceThriftService.queryOrderFinance(request);
            log.info("queryOrderFinance response={}", response);
            if (Objects.isNull(response) || !Objects.equals(StatusCodeEnum.SUCCESS.toStatus(), response.getStatus())) {
                Cat.logEvent(TMSConstant.QUERY_ORDER_FINANCE,"fail");
                return null;
            }
            List<BizOrderFinanceModel> bizOrderFinanceModelList = response.getBizOrderFinanceModelList();
            if (CollectionUtils.isEmpty(bizOrderFinanceModelList)) {
                Cat.logEvent(TMSConstant.QUERY_ORDER_FINANCE,"empty");
                return null;
            }
            BizOrderFinanceModel bizOrderFinanceModel = bizOrderFinanceModelList.get(0);
            return bizOrderFinanceModel.getItemSaleAmt();
        } catch (Exception e) {
            log.error("queryOrderFinance failed: ", e);
            Cat.logEvent(TMSConstant.QUERY_ORDER_FINANCE,"error");
        }
        return null;
    }

    public Integer queryOrderFinanceFallback(Long tenantId, Long shopId, Long orderId) {
        log.error("queryOrderFinanceFallback");
        throw new CommonErrException("查询财务订单商品总金额降级", ResultCode.RETRY_INNER_FAIL.getCode());
    }
}
