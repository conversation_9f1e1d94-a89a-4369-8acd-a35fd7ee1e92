package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.rider.repository;

import com.meituan.linz.boot.util.Assert;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.RiderDeliveryStatisticDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.RiderDeliveryStatisticDOExMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.CountGroupExDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp.RiderDurationDeliveryStatistics;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryStatisticsRepository;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/8/24 18:39
 **/
@Repository
public class MySQLRiderDeliveryStatisticsRepository implements RiderDeliveryStatisticsRepository {
    @Resource
    private RiderDeliveryStatisticDOMapper riderDeliveryStatisticDOMapper;

    @Resource
    private RiderDeliveryStatisticDOExMapper riderDeliveryStatisticDOExMapper;

    @Override
    public List<RiderDeliveryStatisticDO> queryByAccountIdAndDuration(Long tenantId, Long riderAccountId,
                                                                      Long beginDate, Long endDate) {
        RiderDeliveryStatisticDOExample example = new RiderDeliveryStatisticDOExample();
        example.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andRiderAccountIdEqualTo(riderAccountId)
                .andDtBetween(beginDate, endDate);
        return riderDeliveryStatisticDOMapper.selectByExample(example);
    }

    @Override
    public Boolean checkDataIsReadyByDt(Long dt) {
        return riderDeliveryStatisticDOExMapper.checkDataIsReady(dt);
    }

    @Override
    public Map<Long, Integer> queryStoreDeliveringOrderCount(List<Long> storeIds, List<RiderDeliveryStatusEnum> statusList) {

        if (CollectionUtils.isEmpty(storeIds) || CollectionUtils.isEmpty(statusList)) {
            return Collections.emptyMap();
        }

        List<CountGroupExDO> countGroupExDOS = riderDeliveryStatisticDOExMapper.queryStoreDeliveringOrderCount(storeIds, Fun.map(statusList, RiderDeliveryStatusEnum::getCode));
        return countGroupExDOS.stream().collect(Collectors.toMap(CountGroupExDO::getGroupColumn, CountGroupExDO::getGroupCount, (k1,k2) -> k2));
    }
}
