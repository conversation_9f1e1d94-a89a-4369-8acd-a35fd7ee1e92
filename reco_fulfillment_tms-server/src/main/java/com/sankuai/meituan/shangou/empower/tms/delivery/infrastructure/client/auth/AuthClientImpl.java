package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.auth;

import com.meituan.shangou.saas.tenant.thrift.UserThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.dto.EmployeeInfoV2Dto;
import com.meituan.shangou.saas.tenant.thrift.dto.user.RoleDto;
import com.meituan.shangou.saas.tenant.thrift.dto.user.UserDto;
import com.meituan.shangou.saas.tenant.thrift.dto.user.response.UserInfoResponse;
import com.meituan.shangou.sac.dto.model.SacRoleDTO;
import com.meituan.shangou.sac.dto.request.search.QuerySacAccountRoleRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.*;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.QueryAccountListByPoiAndAuthRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.QueryAccountListByPoiAndRoleIdsRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryDimensionPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.auth.AuthClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.wrapper.SacAccountSearchThriftServiceWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryDimensionPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.AccountInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.auth.RiderAuthClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.OPERATOR_SHOP_PERMISSION_ERROR;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/20
 */
@Slf4j
@Service
public class AuthClientImpl implements AuthClient, RiderAuthClient {

	private static final int SUCCESS = 0;

	private static final int DEFAULT_PAGE_SIZE = 200;

	//最大查询页数 防止循环分页查询次数过多
	private static final int MAX_PAGE_NUM = 5;

	@Resource
	private AuthThriftService.Iface authThriftServiceClient;

	@Resource
	private SacAccountSearchThriftServiceWrapper sacAccountSearchThriftServiceWrapper;

	@Resource
	private UserThriftService userThriftService;

	@Resource
	private DeliveryDimensionPoiRepository deliveryDimensionPoiRepository;

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> checkStorePermission(Long tenantId, Long operatorId, List<Long> storeIds) {
		if (CollectionUtils.isEmpty(storeIds)) {
			return Optional.empty();
		}

		try {
			QueryPermissionGroupRequest request = new QueryPermissionGroupRequest(operatorId, 0, tenantId, PermissionGroupTypeEnum.POI.getValue());
			log.info("AuthThriftService.queryPermissionGroupByTokenAndPermissionType request:{}", request);
			QueryPermissionGroupResponse response = authThriftServiceClient.queryPermissionGroupByTokenAndPermissionType(request);
			log.info("AuthThriftService.queryPermissionGroupByTokenAndPermissionType  response:{}", response);
			if (response.getResult().getCode() == SUCCESS) {
				Set<Long> noAuthStoreIds = new HashSet<>(storeIds);
				noAuthStoreIds.removeAll(
						Optional.ofNullable(response.getPermissionGroupCodeList())
								.orElse(new ArrayList<>())
								.stream()
								.map(PermissionGroupVo::getCode)
								.filter(StringUtils::isNumeric)
								.map(Long::valueOf)
								.collect(Collectors.toSet())
				);

				if (CollectionUtils.isNotEmpty(noAuthStoreIds)) {
					return Optional.of(new Failure(false, OPERATOR_SHOP_PERMISSION_ERROR, StringUtils.join(noAuthStoreIds, ",")));
				} else {
					return Optional.empty();
				}

			} else {
				return Optional.of(new Failure(false, OTHER_SYSTEM_CALL_FAILED, response.getResult().getMsg()));
			}

		} catch (Exception e) {
			log.error("AuthThriftService.queryPermissionGroupByTokenAndPermissionType error", e);
			return Optional.of(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<AccountInfo> queryAccountListByPoiAndAuth(Long tenantId, Long storeId, Integer appId) {

		HashMap<Integer, List<String>> dataAuthMap = new HashMap<>();
		dataAuthMap.put(PermissionGroupTypeEnum.POI.getValue(), Collections.singletonList(storeId.toString()));

		QueryAccountListByPoiAndAuthRequest request = new QueryAccountListByPoiAndAuthRequest();
		request.setAppId(appId);
		request.setTenantId(tenantId);
		request.setDataAuthMap(dataAuthMap);
		request.setNeedRoleInfo(true);
		request.setValid(1);
		request.setPageSize(DEFAULT_PAGE_SIZE);

		int pageNum = 1;
		Set<AccountInfo> accountInfos = new HashSet<>();
		QueryAccountInfoListResponse response;
		do {
			request.setPageNum(pageNum);
			try {
				response = authThriftServiceClient.queryAccountListByPoiAndAuth(request);
				log.info("AuthThriftService.queryAccountListByPoiAndAuth request: {}, response: {}", request, response);
			} catch (Exception e) {
				log.error("AuthThriftService.queryAccountListByPoiAndAuth error, req: {}", request, e);
				throw new RuntimeException(e);
			}

			if (response == null || response.getResult() == null || response.getResult().getCode() != SUCCESS || response.getPageInfo() == null) {
				log.error("AuthThriftService.queryAccountListByPoiAndAuth remote call fail, req: {}, resp: {}", request, response);
				throw new RuntimeException("AuthThriftService.queryAccountListByPoiAndAuth fail");
			}

			if (CollectionUtils.isNotEmpty(response.getAccountInfoList())) {
				accountInfos.addAll(response.getAccountInfoList().stream()
						.filter(Objects::nonNull)
						.map(this::transform2AccountInfo)
						.collect(Collectors.toList()));
			}

			pageNum++;
		} while (response.getPageInfo().getPageNumber() < response.getPageInfo().getPages() && pageNum <= MAX_PAGE_NUM);

		return new ArrayList<>(accountInfos);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<AccountInfo> queryRiderByPoiAndRoleIds(Long tenantId, Long storeId) {
		DeliveryDimensionPoi deliveryDimensionPoi = deliveryDimensionPoiRepository.queryDeliveryDimensionPoi(tenantId, storeId);
		QueryAccountListByPoiAndRoleIdsRequest request = new QueryAccountListByPoiAndRoleIdsRequest(tenantId,
				storeId,
				CollectionUtils.isNotEmpty(deliveryDimensionPoi.getRiderTransRoles())? deliveryDimensionPoi.getRiderTransRoles() : DeliveryRiderMccConfigUtils.getRiderRoleIdList());
		QueryRiderInfoListResponse response;
		try {
			response = authThriftServiceClient.queryRiderByPoiAndRoleIds(request);
		} catch (Exception e) {
			log.error("AuthClientImpl.queryRiderByPoiAndRoleIds error, request:{}", request, e);
			throw new RuntimeException(e);
		}

		if (response == null || response.getResult() == null ||
				response.getResult().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
			log.error("AuthClientImpl.queryRiderByPoiAndRoleIds error, req:{}, resp:{}", request, response);
			throw new RuntimeException("RiderQueryApplicationService.queryRiderByPoiAndRoleIds fail");
		}

		return Optional.ofNullable(response.getRiderAccountInfoList())
				.orElse(Collections.emptyList()).stream()
				.map(riderAccountInfoVo -> {
							AccountInfo accountInfo = new AccountInfo();
							accountInfo.setAccountId(riderAccountInfoVo.getId());
							accountInfo.setPhone(riderAccountInfoVo.getMobile());
							accountInfo.setUserName(riderAccountInfoVo.getEmpName());
							accountInfo.setTenantId(request.getTenantId());
							accountInfo.setAccountType(riderAccountInfoVo.getAccountType());
							accountInfo.setRoleIdList(riderAccountInfoVo.getRoleIds());
							return accountInfo;
						}
				).collect(Collectors.toList());
	}

	public List<Long> queryRiderAccountRoleIds(Long accountId) {
		QuerySacAccountRoleRequest request = new QuerySacAccountRoleRequest();
		request.setAccountId(accountId);
		List<SacRoleDTO> dtoList = sacAccountSearchThriftServiceWrapper.queryAccountRoleInfo(request);
		return Optional.ofNullable(dtoList).orElse(Collections.emptyList()).stream().filter(dto -> dto.getValid() == 1)
				.map(SacRoleDTO::getSacRoleId).collect(Collectors.toList());
	}

	@Override
	public AccountInfo queryRiderByAccountId(Long tenantId, Long accountId) {
		UserInfoResponse response = userThriftService.queryTenantUserByAccountId(tenantId, accountId);
		if (response == null || !response.getStatus().isSuccess()) {
			log.error("AuthClientImpl.queryRiderByAccountId error, accountId:{}, resp:{}", accountId, response);
			throw new RuntimeException("RiderQueryApplicationService.queryRiderByAccountId fail");
		}
		return transform2AccountInfo(response.getUserInfo());
	}

	public QueryAccountInfoListResponse getQueryAccountInfoListResponse(long tenantId, Map<Long, List<EmployeeInfoV2Dto>> positionAndEmployeeInfoMap) throws TException {
		QueryAccountDetailByTenantIdAndStaffIdsRequest request = new QueryAccountDetailByTenantIdAndStaffIdsRequest();
		request.setTenantId(tenantId);
		request.setStaffIds(positionAndEmployeeInfoMap.values().stream().flatMap(List::stream).map(EmployeeInfoV2Dto::getEmployeeId).collect(Collectors.toList()));

		QueryAccountInfoListResponse accountResponse = authThriftServiceClient.queryAccountDetailByTenantIdAndStaffIds(request);
		if (!Objects.equals(accountResponse.getResult().getCode(), SUCCESS)) {
			throw new CommonRuntimeException("authThriftService.queryAccountDetailByTenantIdAndStaffIds fail");
		}
		return accountResponse;
	}

	private AccountInfo transform2AccountInfo(AccountInfoVo accountInfoVo) {
		if (accountInfoVo == null) {
			return null;
		}

		AccountInfo accountInfo = new AccountInfo();

		accountInfo.setAccountId(accountInfoVo.getAccountId());
		accountInfo.setRoleIdList(Collections.emptyList());
		if (CollectionUtils.isNotEmpty(accountInfoVo.getRoleList())) {
			accountInfo.setRoleIdList(accountInfoVo.getRoleList().stream()
					.map(RoleInfoVo::getId)
					.collect(Collectors.toList()));
		}
		accountInfo.setPhone(accountInfoVo.getMobile());
		accountInfo.setUserName(accountInfoVo.getUsername());
		accountInfo.setValid(accountInfoVo.getValid());

		return accountInfo;
	}

	private AccountInfo transform2AccountInfo(UserDto userDto) {
		if (userDto == null) {
			return null;
		}

		AccountInfo accountInfo = new AccountInfo();

		accountInfo.setAccountId(userDto.getAccountId());
		accountInfo.setRoleIdList(Collections.emptyList());
		if (CollectionUtils.isNotEmpty(userDto.getRoleList())) {
			accountInfo.setRoleIdList(userDto.getRoleList().stream()
					.map(RoleDto::getRoleId)
					.collect(Collectors.toList()));
		}
		accountInfo.setPhone(userDto.getEmpPhone());
		accountInfo.setUserName(userDto.getEmpName());
		accountInfo.setValid(userDto.getUserStatus());

		return accountInfo;
	}
}
