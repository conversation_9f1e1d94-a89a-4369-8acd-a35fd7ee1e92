package com.sankuai.meituan.shangou.empower.tms.delivery.enums;

/**
 * <AUTHOR>
 * @since 2024/11/21 20:00
 **/
public enum SharkPushSendSceneEnum {
    RIDER_ACCEPT_ORDER("riderAcceptOrder"),
    TRANS_ORDER_TO("tansOrderTo"),
    TRANS_ORDER_FORM("transOrderFrom"),
    DELIVERY_COMPLETE("deliveryComplete"),
    DELIVERY_CANCEL("deliveryCancel"),
    TRANS_TO_THIRD_PART_DELIVERY("transToThirdPartDelivery"),
    TRANS_TO_SELF_DELIVERY("transToSelfDelivery"),
    PICK_DELIVERY_SPLIT("pickDeliverySplit"),
    TAKE_AWAY("takeAway"),
    DELIVERY_DONE_BY_ORDER_FINISH("deliveryDoneByOrderFinish"),
    THIRD_PART_DELIVERY_ORDER_CHANGE("thirdPartDeliveryOrderChange"),
    PICK_DONE_IN_PICK_DELIVERY_SPLIT("pickDoneInPickDeliverySplit"),
    TRANSFER_OUT_FACAI("tansferOutFacai"),
    TRANSFER_IN_FACAI("tansferInFacai"),

    ;

    private String scene;

    SharkPushSendSceneEnum(String scene) {
        this.scene = scene;
    }

    public String getScene() {
        return scene;
    }
}
