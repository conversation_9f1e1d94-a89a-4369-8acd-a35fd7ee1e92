package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.crane;

import com.cip.crane.client.ITaskHandler;
import com.cip.crane.netty.protocol.ScheduleTask;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.PoiBaseInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.RiderQueryApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/07/11 10:37
 */
@Slf4j
@Component
public class RiderDeliveryOrderStatusCompensateCraneService implements ITaskHandler {

    /**
     * 最长距今时间
     * 表示查询的起点时间距离现在的时间差，目前是每天凌晨3点和15点各执行一次，15点扫描的时间范围是前一天21点30分到当天的10点，
     * 凌晨3点扫描的范围是前一天的9点30分到前一天的22点，中间有0.5个小时的重叠，目的是为了避免因为执行时间的误差，导致无法覆盖24小时
     *
     * 17.5*60 = 1050 minutes
     */
    final static Integer durationStartTime = 1050;
    /**
     * 最短距今是时间
     * 表示查询的终点时间距离现在的时间差，5*60 = 300 minutes
     */
    final static Integer durationEndTime = 300;

    final static List<DeliveryStatusEnum> nonFinalState = Lists.list(
            DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER,
            DeliveryStatusEnum.RIDER_ASSIGNED,
            DeliveryStatusEnum.RIDER_TAKEN_GOODS);
    @Resource
    RiderQueryApplicationService riderQueryApplicationService;
    @Resource
    OrderSystemClient orderSystemClient;
    @Resource
    RiderDeliveryOrderRepository riderDeliveryOrderRepository;

    @Resource
    private TenantSystemClient tenantSystemClient;

    /**
     * crane定时任务起点
     * 有两种情况：
     * * 若输入只有一个参数，则该参数应该表示为租户ID，此时表示对一段时间内的未处理到的订单做补偿处理
     * * 若输入有两个参数，则第一个参数表示为租户ID，第二个参数表示为订单号，此时为临时对某一特定订单进行处理
     *
     * @param scheduleTask 任务入参为一个字符串
     * @throws Exception 抛出异常
     */
    @Override
    public void handleTask(ScheduleTask scheduleTask) throws Exception {
        log.info("RiderDeliveryOrderStatusCompensateCraneService started, params:{}", scheduleTask.getTaskItems().get(0));
        List<Long> wrongOrderIds = null;
        String[] params = scheduleTask.getTaskItems().get(0).split(",");
        if (params.length == 1) {
            wrongOrderIds = doCrane(Long.valueOf(params[0]), LocalDateTime.now());
        } else if (params.length == 3) {
            if (!handleOrder(Long.valueOf(params[0]),Long.valueOf(params[1]), Long.valueOf(params[2]))) {
                wrongOrderIds = Collections.singletonList(Long.valueOf(params[2]));
            }
        } else {
            throw new RuntimeException("handleTask params error");
        }

        if (wrongOrderIds != null && !wrongOrderIds.isEmpty()) {
            log.error("delivery orders handle error, order id list: {}", wrongOrderIds);
            throw new RuntimeException(String.format("[%s] delivery orders handle error.", wrongOrderIds.size()));
        }
    }

    public List<Long> doCrane(Long tenantId, LocalDateTime processTime) {

        List<PoiBaseInfo> poiBaseInfoList = tenantSystemClient.queryTenantStoreList(tenantId);
        List<Long> storeIdList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(poiBaseInfoList)){
            storeIdList = poiBaseInfoList.stream().map(PoiBaseInfo::getPoiId).distinct().collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(storeIdList)){
            log.error("orderSystemClient.getOrderInfo store is empty ");
            return Collections.emptyList();
        }

        List<List<Long>> partList = com.google.common.collect.Lists.partition(storeIdList,200);

        List<RiderDeliveryOrder> deliveryOrderList = new ArrayList<>();
        for (List<Long> list : partList){
            List<RiderDeliveryOrder> listDelivery = riderQueryApplicationService.
                    queryDeliveryByTimeIntervalAndStatus(tenantId, nonFinalState, processTime.minusMinutes(durationStartTime),
                            processTime.minusMinutes(durationEndTime),list);
            if(CollectionUtils.isNotEmpty(listDelivery)){
                deliveryOrderList.addAll(listDelivery);
            }
        }



        List<Long> wrongDeliveryOrderIds = new ArrayList<>();
        for (RiderDeliveryOrder deliveryOrder : deliveryOrderList) {
            Result<OrderInfo> orderInfo = orderSystemClient.getOrderInfo(deliveryOrder.getCustomerOrderKey().getOrderId(), false);
            if (orderInfo.isFail() || orderInfo.getInfo() == null) {
                log.error("orderSystemClient.getOrderInfo error.");
                wrongDeliveryOrderIds.add(deliveryOrder.getCustomerOrderKey().getOrderId());
                continue;
            }
            if(!doCompensate(deliveryOrder, orderInfo.getInfo())){
                wrongDeliveryOrderIds.add(deliveryOrder.getCustomerOrderKey().getOrderId());
            }
        }
        return wrongDeliveryOrderIds;
    }

    public boolean handleOrder(Long tenantId,Long storeId, Long orderId) {
        Optional<RiderDeliveryOrder> riderDeliveryOrderOptional = Optional.empty();
        if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
            riderDeliveryOrderOptional = riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMasterWithTenant(orderId,tenantId,storeId);
        }else {
            riderDeliveryOrderOptional = riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMaster(orderId);
        }

        if (!riderDeliveryOrderOptional.isPresent()) {
            log.error("delivery order not exist. orderId: {}", orderId);
            return false;
        }
        RiderDeliveryOrder deliveryOrder = riderDeliveryOrderOptional.get();
        if (!tenantId.equals(deliveryOrder.getTenantId())) {
            log.error("given tenant id not match delivery order's tenant id. given tenant id: {}, delivery order's tenant id: {}",
                    tenantId, deliveryOrder.getTenantId());
            return false;
        }

        Result<OrderInfo> orderInfo = orderSystemClient.getOrderInfo(orderId, false);
        if (orderInfo.isFail() || orderInfo.getInfo() == null) {
            log.error("orderSystemClient.getOrderInfo error");
            return false;
        }
        return doCompensate(deliveryOrder, orderInfo.getInfo());
    }

    private boolean doCompensate(RiderDeliveryOrder deliveryOrder, OrderInfo order) {
        if (!order.orderCancelled()) {
            log.info("订单无需处理, delivery order id: {}", deliveryOrder.getId());
            return true;
        }
        try{
            deliveryOrder.cancel();
        }catch (Exception e){
            log.error("delivery order compensate error，deliver order id: {}", deliveryOrder.getId());
            return false;
        }
        return true;
    }
}
