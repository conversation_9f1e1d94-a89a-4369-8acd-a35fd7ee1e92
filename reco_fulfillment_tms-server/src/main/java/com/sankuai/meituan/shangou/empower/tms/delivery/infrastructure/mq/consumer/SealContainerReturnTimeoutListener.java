package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.hu.SealContainerLogQueryClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.push.PushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.SealContainerReturnTimeoutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.push.RiderPushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.shangou.logistics.hu.api.dto.SealContainerLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * <AUTHOR>
 * @since 2024/8/6 15:59
 **/
@Service
@Slf4j
public class SealContainerReturnTimeoutListener extends AbstractDeadLetterConsumer {

    @Resource
    private SealContainerLogQueryClient sealContainerLogQueryClient;

    @Resource
    private RiderPushClient riderPushClient;

    @Resource
    private PushClient pushClient;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.SEAL_CONTAINER_RETURN_TIMEOUT_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
        log.info("开始消费容具归还超时消息: mafkaMessage:{}", mafkaMessage);

        SealContainerReturnTimeoutMessage message = translateMessage(mafkaMessage);
        if (message == null) {
            return CONSUME_SUCCESS;
        }

        //过滤非歪马租户
        if (!MccConfigUtils.checkIsDHTenant(message.getTenantId())) {
            return CONSUME_SUCCESS;
        }

        if (!MccConfigUtils.isSealContainerReturnTimeoutNotifyGrayStore(message.getStoreId())) {
            return CONSUME_SUCCESS;
        }

        List<SealContainerLogDTO> sealContainerLogDTOS = sealContainerLogQueryClient.queryUsingContainerByOrderKey(message.getTenantId(), message.getStoreId(), message.getChannelOrderId(), message.getOrderBizType());
        if (CollectionUtils.isEmpty(sealContainerLogDTOS)) {
            return CONSUME_SUCCESS;
        }

        //标记容具归还超时
        sealContainerLogQueryClient.signReturnTimeout(message.getTenantId(), message.getStoreId(), message.getChannelOrderId(), message.getOrderBizType());

        //发送超时提醒
        pushClient.pushSealContainerReturnTimeout(message.getTenantId(), message.getStoreId(), message.getRiderAccountId());

        return CONSUME_SUCCESS;
    }

    public SealContainerReturnTimeoutMessage translateMessage(MafkaMessage message) {
        try {
            SealContainerReturnTimeoutMessage outMessage = super.translateMessage(message, SealContainerReturnTimeoutMessage.class);
            Bssert.throwIfNull(outMessage, "message is null");
            Bssert.throwIfNull(outMessage.getTenantId(), "tenantId is null");
            Bssert.throwIfNull(outMessage.getStoreId(), "storeId is null");
            Bssert.throwIfNull(outMessage.getChannelOrderId(), "channelOrderId is null");
            Bssert.throwIfNull(outMessage.getOrderBizType(), "orderBizType is null");
            Bssert.throwIfNull(outMessage.getRiderAccountId(), "riderAccountId is null");
            return outMessage;
        } catch (Exception e) {
            log.error("解析容具归还超时消息失败:{}", message);
            Cat.logEvent("SEAL_CONTAINER_RETURN_TIMEOUT", "MESSAGE_WRONG");
            return null;
        }
    }
}
