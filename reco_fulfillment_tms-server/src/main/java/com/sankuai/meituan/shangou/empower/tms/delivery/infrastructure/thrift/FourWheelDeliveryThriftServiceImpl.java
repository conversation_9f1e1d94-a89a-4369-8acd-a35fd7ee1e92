package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.meituan.reco.pickselect.common.domain.orderTrack.OperationScene;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DistributeTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.DeliveryOperationFailedException;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.OrderPlatformDeliveryOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.FourWheelCmdDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.FourWheelDispatchCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.FourWheelPreviewCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.impl.OrderPlatformDeliveryOperateServiceProxy;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.dto.response.TmsResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.FourWheelDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.FourWheelDeliveryDispatchRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.FourWheelDeliveryPreviewRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.FourWheelDeliveryDispatchResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.FourWheelDeliveryPreviewDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.FourWheelDeliveryPreviewResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.PlatformDeliveryCheckSquirrelService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class FourWheelDeliveryThriftServiceImpl implements FourWheelDeliveryThriftService {

    private String vehicleDescFormat = "%s(预估%s元)";

    @Autowired
    private OrderSystemClient orderSystemClient;

    @Autowired
    private OrderPlatformDeliveryOperateService orderPlatformDeliveryOperateService;

    @Autowired
    private PlatformDeliveryCheckSquirrelService platformDeliveryCheckSquirrelService;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Autowired
    private MafkaMessageProducer<OrderTrackEvent> orderTrackLogProducer;


    @Override
    public TmsResponse<FourWheelDeliveryPreviewResponse> dispatchPreview(FourWheelDeliveryPreviewRequest request) {


        try{
            Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(request.getTenantId(),request.getOrderId(),false);
            if(orderInfoResult == null || orderInfoResult.getInfo() == null){
                return TmsResponse.fail(ResponseCodeEnum.FAILED,"获取订单失败，请稍后重试");
            }

            OrderInfo orderInfo = orderInfoResult.getInfo();

            if(!MccConfigUtils.getFourWheelDistributeCodeList().contains(""+orderInfo.getOriginalDistributeType())){
                return TmsResponse.fail(ResponseCodeEnum.DELIVERY_METHOD_NOT_MATCH,"配送方式不匹配");
            }

            if(Objects.equals(orderInfo.getOrderStatus(), OrderStatusEnum.CANCELED.getValue())
                    || Objects.equals(orderInfo.getOrderStatus(), OrderStatusEnum.CLOSED.getValue())
                    || Objects.equals(orderInfo.getOrderStatus(), OrderStatusEnum.COMPLETED.getValue())){
                return TmsResponse.fail(ResponseCodeEnum.FAILED,"订单状态不匹配，请刷新后重试");
            }

            Optional<FourWheelCheckInfo> fourWheelCheckInfoOptional = platformDeliveryCheckSquirrelService.get(orderInfo.getOrderKey().getOrderId()+"", FourWheelCheckInfo.class);
            if(!fourWheelCheckInfoOptional.isPresent()){
                return TmsResponse.fail(ResponseCodeEnum.ORDER_NOT_MATCH_FOUR_WHEEL,"订单不满足汽车配送条件");
            }

            Optional<DeliveryOrder> deliveryOrderOptional = deliveryOrderRepository.getLastDeliveryOrderSlave(orderInfo.getOrderKey().getOrderId(),orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
            if(deliveryOrderOptional.isPresent()){
                DeliveryOrder deliveryOrder = deliveryOrderOptional.get();
                if(deliveryOrder.getStatus().riderHasAccept()){
                    return TmsResponse.fail(ResponseCodeEnum.DELIVERY_RIDER_HAS_ACCEPT,"已有骑手接单");
                }

                if(deliveryOrder.getStatus().isFinalStatus()){
                    return TmsResponse.fail(ResponseCodeEnum.ORDER_NOT_MATCH_FOUR_WHEEL,"配送已终态，请刷新后重试");
                }
                if(Objects.equals(deliveryOrder.getIsFourWheelDelivery(),2) && Objects.equals(request.getPreviewType(),1)){
                    return TmsResponse.fail(ResponseCodeEnum.FAILED,"已经发起汽车配送，请刷新后重试");
                }
            }


            Operator operator = new Operator(request.getAccountId(), request.getAccountName());
            DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
            FourWheelPreviewCmd cmd = new FourWheelPreviewCmd(orderInfo.getOrderKey().getTenantId(),orderInfo.getPoiId(),orderInfo,channelType.getChannelId(),operator, request.previewType);
            FourWheelPreviewInfo previewInfo = orderPlatformDeliveryOperateService.dispatchPreview(cmd);
            FourWheelDeliveryPreviewResponse previewResponse = new FourWheelDeliveryPreviewResponse();
            if(previewInfo == null || CollectionUtils.isEmpty(previewInfo.getFourWheelInfoList())){
                return TmsResponse.succeed(previewResponse);
            }
            List<FourWheelDeliveryPreviewDto> previewList = new ArrayList<>();
            previewInfo.getFourWheelInfoList().forEach(w->{
                FourWheelDeliveryPreviewDto dto = new FourWheelDeliveryPreviewDto();
                dto.setVehicleCode(w.getVehicleCode());
                dto.setVehicleName(w.getVehicleName());
                dto.setVehicleFeeRange(w.getVehicleFeeRange());
                dto.setIsDelivery(w.getIsDelivery());
                dto.setTotalFeeRangeMin(w.getTotalFeeRangeMin());
                dto.setTotalFeeRangeMax(w.getTotalFeeRangeMax());
                dto.setUrl(w.getUrl());
                previewList.add(dto);
            });
            previewResponse.setPreviewList(previewList);
            return TmsResponse.succeed(previewResponse);
        }catch (DeliveryOperationFailedException fe){
            log.error("dispatchPreview DeliveryOperationFailedException",fe);
            return TmsResponse.fail(ResponseCodeEnum.FOUR_WHEEL_PREVIEW_FAIL,fe.getMessage());
        }catch (Exception e){
            log.error("dispatchPreview error",e);
            return TmsResponse.fail(ResponseCodeEnum.FAILED,"系统异常，请稍后重试");
        }
    }

    @Override
    public TmsResponse<FourWheelDeliveryDispatchResponse> orderDispatch(FourWheelDeliveryDispatchRequest request) {

        if(CollectionUtils.isEmpty(request.getVehicleDetailList())){
            return TmsResponse.fail(ResponseCodeEnum.FAILED,"参数错误");
        }
        try {
            long operateTime = System.currentTimeMillis();
            Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(request.getTenantId(),request.getOrderId(),false);
            if(orderInfoResult == null || orderInfoResult.getInfo() == null){
                return TmsResponse.fail(ResponseCodeEnum.FAILED,"获取订单失败，请稍后重试");
            }

            OrderInfo orderInfo = orderInfoResult.getInfo();
            if(!MccConfigUtils.getFourWheelDistributeCodeList().contains(""+orderInfo.getOriginalDistributeType())){
                return TmsResponse.fail(ResponseCodeEnum.DELIVERY_METHOD_NOT_MATCH,"配送方式不匹配");
            }

            if(Objects.equals(orderInfo.getOrderStatus(), OrderStatusEnum.CANCELED.getValue())
                    || Objects.equals(orderInfo.getOrderStatus(), OrderStatusEnum.CLOSED.getValue())
                    || Objects.equals(orderInfo.getOrderStatus(), OrderStatusEnum.COMPLETED.getValue())){
                return TmsResponse.fail(ResponseCodeEnum.FAILED,"订单状态不匹配，请刷新后重试");
            }

            Optional<FourWheelCheckInfo> fourWheelCheckInfoOptional = platformDeliveryCheckSquirrelService.get(orderInfo.getOrderKey().getOrderId()+"", FourWheelCheckInfo.class);
            if(!fourWheelCheckInfoOptional.isPresent()){
                return TmsResponse.fail(ResponseCodeEnum.ORDER_NOT_MATCH_FOUR_WHEEL,"订单不满足汽车配送条件");
            }

            Optional<DeliveryOrder> deliveryOrderOptional = deliveryOrderRepository.getLastDeliveryOrderSlave(orderInfo.getOrderKey().getOrderId(),orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
            if(deliveryOrderOptional.isPresent()){
                DeliveryOrder deliveryOrder = deliveryOrderOptional.get();
                if(deliveryOrder.getStatus().riderHasAccept()){
                    return TmsResponse.fail(ResponseCodeEnum.DELIVERY_RIDER_HAS_ACCEPT,"已有骑手接单");
                }

                if(deliveryOrder.getStatus().isFinalStatus()){
                    return TmsResponse.fail(ResponseCodeEnum.ORDER_NOT_MATCH_FOUR_WHEEL,"配送已终态，请刷新后重试");
                }

                if(Objects.equals(deliveryOrder.getIsFourWheelDelivery(),2) && Objects.equals(request.getDeliveryType(),1)){
                    return TmsResponse.fail(ResponseCodeEnum.FAILED,"已经发起汽车配送，请刷新后重试");
                }
            }

            Operator operator = new Operator(request.getAccountId(), request.getAccountName());
            DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
            FourWheelDispatchCmd fourWheelDispatchCmd = new FourWheelDispatchCmd(orderInfo.getOrderKey().getTenantId(),orderInfo.getPoiId(),orderInfo,channelType.getChannelId(),operator);
            List<FourWheelCmdDto> fourWheelCmdDtoList = new ArrayList<>();
            List<String> vehicleDescList = new ArrayList<>();
            request.getVehicleDetailList().forEach(d->{
                FourWheelCmdDto dto = new FourWheelCmdDto();
                dto.setVehicleCode(d.getVehicleCode());
                dto.setVehicleName(d.getVehicleName());
                vehicleDescList.add(String.format(vehicleDescFormat,d.getVehicleName(),d.getVehicleFeeRange()));
                dto.setVehicleFeeRange(d.getVehicleFeeRange());
                fourWheelCmdDtoList.add(dto);
            });
            fourWheelDispatchCmd.setFourWheelCmdDtoList(fourWheelCmdDtoList);
            fourWheelDispatchCmd.setDeliveryType(request.getDeliveryType());
            orderPlatformDeliveryOperateService.orderDispatch(fourWheelDispatchCmd);

            Map<String,Object> extMap = new HashMap<>();
            extMap.put("vehicleNameListStr", Joiner.on(",").join(vehicleDescList));

            TrackOpType trackOpType = TrackOpType.DISPATCH_FOUR_WHEEL;
            if(request.getDeliveryType() == 2){
                trackOpType = TrackOpType.ADD_DISPATCH_FOUR_WHEEL;
            }
            OrderTrackEvent event = new OrderTrackEvent(TrackSource.DELIVERY, trackOpType, request.getAccountId(),
                    OperationScene.DELIVERY_EXCEPTION,orderInfo.getOrderKey().getTenantId() , orderInfo.getChannelOrderId(),
                    orderInfo.getOrderBizType(), extMap, request.getAppId());
            event.setOperateTime(operateTime);
            orderTrackLogProducer.sendMessage(event);

        }catch (DeliveryOperationFailedException fe){
            log.error("orderDispatch DeliveryOperationFailedException",fe);
            return TmsResponse.fail(ResponseCodeEnum.FOUR_WHEEL_DISPATCH_FAIL,fe.getMessage());
        }catch (Exception e){
            log.error("orderDispatch error",e);
            return TmsResponse.fail(ResponseCodeEnum.FAILED,"系统异常，请稍后重试");
        }
        return TmsResponse.succeed(null);
    }
}
