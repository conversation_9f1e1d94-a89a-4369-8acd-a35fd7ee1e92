package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryOperationApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryProcessApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRouteRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryMonitorDomainService;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderRouteMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderRoute;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.PickSelectRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.OrderStatusDoubleCheckService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryCancelCommandMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryLaunchCommandMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryTimeOutCheckMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.ClearDeliveryExceptionMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryUnifiedCallbackMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.List;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

@Slf4j
@Component
@Order()
public class OrderStatusCheckListener extends AbstractLaunchDeliveryListener {

    @Resource
    private OrderSystemClient orderSystemClient;
    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;
    @Resource
    private DeliveryProcessApplicationService deliveryProcessApplicationService;
    @Resource
    private MafkaDelayMessageProducer<DeliveryLaunchCommandMessage> deliveryDelayLaunchMessageProducer;
    @Resource
    private DeliveryOperationApplicationService deliveryOperationApplicationService;
    @Resource
    private MafkaDelayMessageProducer<DeliveryTimeOutCheckMessage> deliveryTimeOutCheckProducer;
    @Resource
    private DeliveryMonitorDomainService deliveryMonitorDomainService;
    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;
    @Resource
    private DeliveryRemindConfigRepository deliveryRemindConfigRepository;

    @Resource
    private TenantRemoteService tenantRemoteService;

    @Resource
    private PickSelectRemoteService pickSelectRemoteService;

    @Resource
    private OrderStatusChangeMessageListener orderStatusChangeMessageListener;

    @Resource
    private DeliveryOrderRouteRepository deliveryOrderRouteRepository;


    @Resource
    private OrderStatusDoubleCheckService orderStatusDoubleCheckService;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.DELIVERY_ORDER_STATUS_DELAY_CHECK_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
        log.info("开始消费赋能订单状态校验消息：{}", mafkaMessage);
        OrderStatusChangeMessageListener.MessageDTO message = translateMessage(mafkaMessage);
        if (message == null) {
            return CONSUME_SUCCESS;
        }

        if(orderStatusDoubleCheckService.check(message.getOrderId()+"",message.getStatus())){
            log.info("orderId :{} ，status:{} 已被幂等",message.getOrderId(),message.getStatus());
            return CONSUME_SUCCESS;
        }

        ConsumeStatus consumeStatus = orderStatusChangeMessageListener.handle(message,mafkaMessage);
        if(consumeStatus == CONSUME_SUCCESS){
            orderStatusDoubleCheckService.setCheck(message.getOrderId()+"",message.getStatus());
        }
        return consumeStatus;
    }

    private OrderStatusChangeMessageListener.MessageDTO translateMessage(MafkaMessage mafkaMessage) {
        try {
            OrderStatusChangeMessageListener.MessageDTO message = translateMessage(mafkaMessage, OrderStatusChangeMessageListener.MessageDTO.class);

            Preconditions.checkNotNull(message, "empty mafkaMessage");
            Preconditions.checkNotNull(message.getTenantId(), "tenantId is null");
            Preconditions.checkNotNull(message.getShopId(), "shopId is null");
            Preconditions.checkNotNull(message.getOrderId(), "orderId is null");
            Preconditions.checkNotNull(message.getStatus(), "status is null");
            Preconditions.checkNotNull(message.getOrderSource(), "orderSource is null");
            Preconditions.checkNotNull(message.getSourceStatus(), "sourceStatus is null");
            return message;
        } catch (Exception e) {
            log.error("解析订单状态变更消息失败:{}", mafkaMessage, e);
            return null;
        }
    }

    @Override
    protected OrderSystemClient getOrderSystemClient() {
        return orderSystemClient;
    }

    @Override
    protected DeliveryProcessApplicationService getDeliveryProcessApplicationService() {
        return deliveryProcessApplicationService;
    }

    @Override
    protected MafkaDelayMessageProducer<DeliveryLaunchCommandMessage> getDeliveryDelayLaunchMessageProducer() {
        return deliveryDelayLaunchMessageProducer;
    }

    @Override
    protected DeliveryOperationApplicationService getDeliveryOperationApplicationService() {
        return deliveryOperationApplicationService;
    }

    @Override
    protected MafkaDelayMessageProducer<DeliveryTimeOutCheckMessage> getDeliveryTimeOutCheckProducer() {
        return deliveryTimeOutCheckProducer;
    }

    @Override
    protected DeliveryMonitorDomainService getDeliveryMonitorDomainService() {
        return deliveryMonitorDomainService;
    }

    @Override
    protected DeliveryPoiRepository getDeliveryPoiRepository() {
        return deliveryPoiRepository;
    }

    @Override
    protected DeliveryOrderRepository getDeliveryOrderRepository() {
        return deliveryOrderRepository;
    }

    @Override
    protected DeliveryRemindConfigRepository getDeliveryRemindConfigRepository() {
        return deliveryRemindConfigRepository;
    }

    @Override
    protected PickSelectRemoteService getPickSelectRemoteService() {
        return pickSelectRemoteService;
    }

    @Override
    protected  TenantRemoteService getTenantRemoteService(){
        return tenantRemoteService;
    }

}
