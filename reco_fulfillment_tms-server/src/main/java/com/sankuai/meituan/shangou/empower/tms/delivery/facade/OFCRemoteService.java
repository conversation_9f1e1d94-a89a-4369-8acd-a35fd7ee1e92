package com.sankuai.meituan.shangou.empower.tms.delivery.facade;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryBaseException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.qnh.ofc.ofw.client.thrift.common.base.OfcStatus;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.BatchFulfillmentOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.ChannelOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.OrderInfoByOrderIdReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.response.order.FulfillmentOrderDetailResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.response.order.FulfillmentOrderResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService;
import com.sankuai.shangou.qnh.ofc.ebase.consts.PickDeliveryWorkModeEnum;
import com.sankuai.shangou.qnh.ofc.ebase.request.QueryFulfillConfigRequest;
import com.sankuai.shangou.qnh.ofc.ebase.response.QueryFulfillConfigResponse;
import com.sankuai.shangou.qnh.ofc.ebase.service.FulfillmentStoreConfigThriftService;
import com.sankuai.shangou.qnh.ofc.ebase.consts.OrderTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Rhino
@Slf4j
public class OFCRemoteService {

    @Resource
    private FulfillmentOrderSearchThriftService fulfillmentOrderSearchThriftService;

    @Autowired
    private FulfillmentStoreConfigThriftService fulfillmentStoreConfigThriftService;

    @MethodLog(logRequest = false, logResponse = true)
    @Degrade(
            rhinoKey = "OfcRemoteService.getOfcOrder",
            fallBackMethod = "getOfcOrderFallback", timeoutInMilliseconds = 1000
    )
    public List<FulfillmentOrderDTO> getOfcOrder(Long tenantId,Long offlineStoreId,Long orderId){
        OrderInfoByOrderIdReq req = new OrderInfoByOrderIdReq();
        req.setTenantId(tenantId);
        req.setWarehouseId(offlineStoreId);
        req.setOrderId(orderId);
        try {
            FulfillmentOrderResponse response = fulfillmentOrderSearchThriftService.searchFulfillmentOrderByOrderId(req);
            if(response == null || response.getStatus() == OfcStatus.FAIL){
                throw new DeliveryBaseException("查询ofc订单失败");
            }
            return response.getFulfillmentOrderList();
        }catch (Exception e){
            log.error("getOfcOrder error",e);
            throw new DeliveryBaseException("查询ofc订单失败");
        }
    }

    public List<FulfillmentOrderDTO> getOfcOrderFallback(Long tenantId,Long offlineStoreId,Long orderId){
        throw new DeliveryBaseException("查询ofc订单降级");
    }

    public List<FulfillmentOrderDetailDTO> searchFulfillmentOrderListByBatchFulfillmentOrderId(Long tenantId,Long storeId,List<ChannelOrderIdKeyReq> channelOrderIdKeyReqList){

        if(CollectionUtils.isEmpty(channelOrderIdKeyReqList)){
            return Collections.emptyList();
        }

        List<FulfillmentOrderDetailDTO> fulfillmentOrderDetailDTOList = Lists.newArrayList();
        //OFC最大查询100
        List<List<ChannelOrderIdKeyReq>> partitionChannelOrderIdKey = Lists.partition(channelOrderIdKeyReqList, 100);
        for (List<ChannelOrderIdKeyReq> orderIdKeyReqs : partitionChannelOrderIdKey) {

            try {
                BatchFulfillmentOrderIdKeyReq req = new BatchFulfillmentOrderIdKeyReq();
                req.setTenantId(tenantId);
                req.setWarehouseId(storeId);
                req.setChannelOrderIdKeyList(orderIdKeyReqs);
                FulfillmentOrderDetailResponse response = fulfillmentOrderSearchThriftService.searchFulfillmentOrderListByBatchFulfillmentOrderId(req);
                if (!Objects.equals(response.getStatus().getCode(), OfcStatus.SUCCESS.getCode())) {
                    log.info("fulfillmentOrderSearchThriftService.searchFulfillmentOrderListByBatchFulfillmentOrderId, req = {}, response = {}", req, response);
                    continue;
                }
                fulfillmentOrderDetailDTOList.addAll(Optional.ofNullable(response.getFulfillmentOrderList()).orElse(Lists.newArrayList()));
            }catch (Exception e){
                log.error("searchFulfillmentOrderListByBatchFulfillmentOrderId error",e);
            }
        }
        return fulfillmentOrderDetailDTOList;
    }

    public int queryPickDeliveryWorkMode(Long tenantId,Long warehouseId){

        try {
            QueryFulfillConfigRequest request = new QueryFulfillConfigRequest(tenantId,warehouseId, OrderTypeEnum.SALE_TYPE.getCode());
            QueryFulfillConfigResponse response = fulfillmentStoreConfigThriftService.queryFulfillConfig(request);
            if(response!=null && Objects.equals(response.getCode(),OfcStatus.SUCCESS.getCode())){
                if(response.getFulfillConfig()!=null){
                    return  response.getFulfillConfig().getPickDeliveryWorkMode();
                }
            }
            return PickDeliveryWorkModeEnum.PICK_DELIVERY_SPLIT.getCode();
        }catch (Exception e){
            log.error("queryPickDeliveryWorkMode error",e);
        }
        return PickDeliveryWorkModeEnum.PICK_DELIVERY_SPLIT.getCode();
    }
}
