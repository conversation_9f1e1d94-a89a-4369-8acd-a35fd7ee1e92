package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.creditaudit;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.CreditAuditAutomaticPassMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.CreditAuditPicMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.CreditAuditBizData;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.creditaudit.CreditAuditClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/27 15:31
 **/
@Service
@Slf4j
public class CreditAuditClientImpl implements CreditAuditClient {
    @Resource
    private MafkaMessageProducer<CreditAuditPicMessage> creditAuditPicMessageProducer;

    @Resource
    private MafkaDelayMessageProducer<CreditAuditAutomaticPassMessage> automaticPassMessageDelayProducer;

    public static final String TYPE_ID = "21097";

    private static final int RIDER_DATA_SOURCE = 5;

    private static final int E_PASSPORT_SOURCE_ID = 12;

    @Override
    public void sendCreditAudit(List<Pair<Long/*picId*/, String/*url*/>> picInfos, Long deliveryOrderId, String userIP, Long riderAccountId,Long tenantId,Long storeId) {
        if (CollectionUtils.isEmpty(picInfos)) {
            log.warn("送审图片信息为空");
            return;
        }

        creditAuditPicMessageProducer.sendMessage(buildCreditPicMessage(buildPicKey(picInfos), deliveryOrderId, riderAccountId, userIP,tenantId,storeId));
    }

    @Override
    public void sendCreditAuditAutomaticPassMessage(Long deliveryOrderId,Long tenantId,Long storeId) {
        automaticPassMessageDelayProducer.sendDelayMessageInMillis(new CreditAuditAutomaticPassMessage(deliveryOrderId,tenantId,storeId),
                DeliveryRiderMccConfigUtils.getCreditAuditAutoPassDuration());
    }

    private CreditAuditPicMessage buildCreditPicMessage(List<CreditAuditPicMessage.PicKey> picKeys, Long deliveryOrderId,
                                                        long userId, String userIP,Long tenantId,Long storeId) {
        CreditAuditPicMessage message = new CreditAuditPicMessage();
        message.setType(TYPE_ID);
        message.setDatetime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        message.setUserIP(userIP);
        message.setPicInfo(JsonUtil.fromJson(JsonUtil.toJson(picKeys), new TypeReference<List<Map<String, Object>>>() {
        }));
        message.setBizData(JsonUtil.toJson(new CreditAuditBizData(deliveryOrderId,tenantId,storeId)));
        message.setTransid(deliveryOrderId.toString());
        message.setDataSource(RIDER_DATA_SOURCE);
        message.setUserId(userId);
        message.setSource(E_PASSPORT_SOURCE_ID);

        return message;
    }

    private List<CreditAuditPicMessage.PicKey> buildPicKey(List<Pair<Long, String>> picInfos) {
        if (CollectionUtils.isEmpty(picInfos)) {
            return Collections.emptyList();
        }

        return picInfos.stream().map(picInfo -> {
            CreditAuditPicMessage.PicKey picKey = new CreditAuditPicMessage.PicKey();
            picKey.setPicUrl(picInfo.getRight());
            picKey.setPicId(picInfo.getLeft());
            return picKey;
        }).collect(Collectors.toList());

    }

}
