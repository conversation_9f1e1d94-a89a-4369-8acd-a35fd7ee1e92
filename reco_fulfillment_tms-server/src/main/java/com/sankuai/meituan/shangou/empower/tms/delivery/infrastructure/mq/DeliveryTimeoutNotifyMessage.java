package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 配送超时消息，外部监听配送超时消息然后运营人员申请给用户发券
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryTimeoutNotifyMessage {

	/**
	 * 赋能租户ID
	 */
	private Long tenantId;

	/**
	 * 赋能门店ID
	 */
	private Long storeId;

	/**
	 * 赋能订单id
	 */
	private Long orderId;

	/**
	 * 渠道订单id
	 */
	private String channelOrderId;

	/**
	 * 订单渠道
	 *
	 * @see com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType
	 */
	private Integer orderBizType;

	/**
	 * 配送超时时刻时间戳
	 */
	private Long deliveryTimeoutStamp;
}
