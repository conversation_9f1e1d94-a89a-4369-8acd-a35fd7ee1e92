package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/9/21 20:56
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DelayNotifyDeliveryStatusMessage {
    private Long deliveryOrderId;

    private DeliveryStatusEnum changeDeliveryStatus;

    private String riderAccountName;

    private Long tenantId;

    private Long storeId;
}
