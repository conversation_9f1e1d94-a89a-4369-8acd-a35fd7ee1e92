package com.sankuai.meituan.shangou.empower.tms.delivery.facade.config;

import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.RetryTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Import;
import com.sankuai.shangou.infra.cc.sdk.config.CcConfigSdk;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 配置中心 SDK
 */
@Slf4j
@Import(CcConfigSdk.class) // 导入配置中心SDK，会自动按需注册 Thrift 接口服务
@Component
public class CcConfigSdkFacade {

    @Resource
    private CcConfigSdk ccConfigSdk;

    // 配置 Bean 必须为 pulic class，有无参构造器，建议仅包括配置字段，相关判定
    public <ConfigBean> ConfigBean queryTenantConfigAsBean(Long tenantId, Class<ConfigBean> configBeanClass){

        try {
            return RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getCcTenantConfigRetryCount(),MccConfigUtils.getCcTenantConfigRetryTime())
                    .execute(new RetryCallback<ConfigBean, Exception>() {
                        @Override
                        public ConfigBean doWithRetry(RetryContext retryContext) throws Exception {
                            return ccConfigSdk.queryTenantConfigAsBean(tenantId, configBeanClass);
                        }
                    });
        } catch (Exception e) {
            log.error("queryTenantConfigAsBean error:", e);
        }
        return null;
    }





}
