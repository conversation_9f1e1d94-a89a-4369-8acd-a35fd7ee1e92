package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.TransferOrderMarkEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.OFCRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.ProxyDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.HuSyncDeliveryOrderMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

@Slf4j
@Component
public class DeliveryHuSyncConsumerListener extends AbstractDeadLetterConsumer{

    @Autowired
    private OrderSystemClient orderSystemClient;

    @Autowired
    private ProxyDeliveryPlatformClient deliveryPlatformClient;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    @Resource
    private OFCRemoteService ofcRemoteService;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.SG_EMPOWER_DELIVERY_HU_SYNC_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("开始消费手动发单:{}",message);
        HuSyncDeliveryOrderMessage huSyncDeliveryOrderMessage = translateMessage(message);
        Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(huSyncDeliveryOrderMessage.getOrderId(),true);
        if(orderInfoResult ==null || orderInfoResult.getInfo()==null){
            log.error("手动发配送查询不到订单详情:{}",message);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        OrderInfo orderInfo=orderInfoResult.getInfo();

        DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
                orderInfo.getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
        Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(orderInfo.getOrderKey().getTenantId(),
                orderInfo.getWarehouseId(), orderBizType.getChannelId());

        if (!opDeliveryPoi.isPresent()) {
            log.error("手动发配送查询不到门店详情:{}",message);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        List<DeliveryOrder> deliveryOrderList = deliveryOrderRepository.getDeliveryOrdersMaxWithOrderId(orderInfo.getOrderKey().getOrderId());
        DeliveryOrder deliveryOrder;
        if(CollectionUtils.isNotEmpty(deliveryOrderList)){
            deliveryOrder=deliveryOrderList.get(0);
            if(!deliveryOrder.getStatus().isFinalStatus()){
                log.error("手动发配送配送单据不是终态:{}",message);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        }
        DeliveryChannelEnum channelEnum = DeliveryChannelEnum.MALT_FARM;
        if(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM == opDeliveryPoi.get().getDeliveryPlatform()){
            channelEnum = DeliveryChannelEnum.DAP_DELIVERY;
        }else if(DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY == opDeliveryPoi.get().getDeliveryPlatform()){
            channelEnum = DeliveryChannelEnum.MERCHANT_DELIVERY;
        }

        // 发起新的配送单
        deliveryOrder = DeliveryOrder.fromBusinessCustomer(orderInfo, channelEnum.getCode(), StringUtils.EMPTY);
        log.info("Launch a new deliveryOrder: {}", deliveryOrder);
        if(orderInfo.getOrderTransInfo()!=null && orderInfo.getOrderTransInfo().getDispatchShopId()!=null){
            deliveryOrder.setStoreId(orderInfo.getWarehouseId());
            deliveryOrder.setOrderKey(new OrderKey(orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId(),orderInfo.getOrderKey().getOrderId()));
            List<FulfillmentOrderDTO> fulfillmentOrderDTOList = new ArrayList<>();
            if(com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.getOfcRemoteSwitch()){
                fulfillmentOrderDTOList = ofcRemoteService.getOfcOrder(deliveryOrder.getTenantId(),deliveryOrder.getStoreId(),orderInfo.getOrderKey().getOrderId());
            }
            if(CollectionUtils.isNotEmpty(fulfillmentOrderDTOList)){
                FulfillmentOrderDTO dto = fulfillmentOrderDTOList.get(0);
                if(AbstractLaunchDeliveryListener.FULFILL_ORDER_STATUS_INIT.contains(dto.getStatus())){
                    return CONSUME_SUCCESS;
                }
                if(!AbstractLaunchDeliveryListener.FULFILL_ORDER_STATUS_CANCEL.contains(dto.getStatus())){
                    deliveryOrder.setPlatformSourceEnum(PlatformSourceEnum.OFC);
                    deliveryOrder.setFulfillmentOrderId(dto.getFulfillmentOrderId());
                }
            }
        }
        deliveryOrderRepository.saveDeliveryOrder(deliveryOrder);

        TransferOrderMarkEnum transferOrderMarkEnum = TransferOrderMarkEnum.NORMAL_ORDER;
        if(orderInfo.getOrderTransInfo()!=null && orderInfo.getOrderTransInfo().getDispatchShopId()!=null){
            transferOrderMarkEnum = TransferOrderMarkEnum.TRANSFER_ORDER;
        }


        Optional<LaunchFailure> failureOptional=deliveryPlatformClient.launch(opDeliveryPoi.get(),orderInfo,deliveryOrder,transferOrderMarkEnum.getCode());
        if(failureOptional.isPresent()){
            log.error("手动发配送配送失败:{}，failureOptional:{}",message,failureOptional);
            return ConsumeStatus.CONSUME_FAILURE;
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private HuSyncDeliveryOrderMessage translateMessage(MafkaMessage mafkaMessage) {
        try {
            HuSyncDeliveryOrderMessage message = translateMessage(mafkaMessage, HuSyncDeliveryOrderMessage.class);
            Preconditions.checkNotNull(message, "empty message");
            Preconditions.checkNotNull(message.getOrderId(), "orderId is null");
            return message;

        } catch (Exception e) {
            log.error("DeliveryHuSyncConsumerListener:{}", mafkaMessage, e);
            return null;
        }
    }

    protected <T> T translateMessage(MafkaMessage mafkaMessage, Class<T> clazz) {
        return Optional.ofNullable(mafkaMessage)
                .map(MafkaMessage::getBody)
                .map(Object::toString)
                .map(it -> JsonUtil.fromJson(it, clazz))
                .orElse(null);
    }
}
