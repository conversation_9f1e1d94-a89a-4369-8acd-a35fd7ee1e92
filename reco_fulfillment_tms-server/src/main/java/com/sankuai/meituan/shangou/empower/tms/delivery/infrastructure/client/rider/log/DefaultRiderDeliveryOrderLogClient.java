package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.rider.log;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.DeliveryOrderLogRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryChangeLogMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrderChangeInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.log.RiderDeliveryOrderLogClient;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/12
 */
@Slf4j
@Service
public class DefaultRiderDeliveryOrderLogClient implements RiderDeliveryOrderLogClient {

	@Resource
	private MafkaMessageProducer<DeliveryChangeLogMessage> deliveryChangeLogMessageProducer;
	@Resource
	private DeliveryOrderLogRepository deliveryOrderLogRepository;

	@Override
	@CatTransaction
	@MethodLog(logRequest = true)
	public void logDeliveryChange(Long deliveryId, DeliveryStatusEnum status, DeliveryEventEnum deliveryEvent,
								  RiderDeliveryOrderChangeInfo changeInfo, LocalDateTime changeTime) {
		deliveryChangeLogMessageProducer.sendMessage(
				new DeliveryChangeLogMessage(deliveryId, deliveryEvent.getCode(), JsonUtil.toJson(changeInfo),
						TimeUtil.toMilliSeconds(changeTime), status.getCode()), deliveryId);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = true)
	public void logDeliveryChangeSyncly(Long deliveryId, DeliveryStatusEnum status, DeliveryEventEnum deliveryEvent, RiderDeliveryOrderChangeInfo changeInfo, LocalDateTime changeTime) {
		deliveryOrderLogRepository.saveDeliveryChangeLog(deliveryId, status.getCode(), deliveryEvent, JsonUtil.toJson(changeInfo), changeTime);
	}
}
