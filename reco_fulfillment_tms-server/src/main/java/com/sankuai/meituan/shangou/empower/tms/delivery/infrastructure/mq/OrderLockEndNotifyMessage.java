package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * 锁单结束变更
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderLockEndNotifyMessage {
    /**
     * 赋能订单号
     */
    private Long orderId;
    /**
     * 渠道类型
     */
    private Integer orderBizType;
    /**
     * 锁单状态  0-未锁单/可发配送 1-已锁单/不可发配送
     */
    private Integer lockOrderStatus;
    /**
     * 渠道id
     */
    private String channelOrderId;


    /**
     * 是否锁单2.0
     */
    private Boolean isLockOrderV2;
}
