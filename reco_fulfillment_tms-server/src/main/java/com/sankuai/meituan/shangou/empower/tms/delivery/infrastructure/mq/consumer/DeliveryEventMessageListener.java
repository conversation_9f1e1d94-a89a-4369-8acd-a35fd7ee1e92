package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryCallbackTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryEventMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryChangeCallbackInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryUnifiedCallbackMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/14
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class DeliveryEventMessageListener extends AbstractDeadLetterConsumer {

	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private MafkaMessageProducer<DeliveryUnifiedCallbackMessage> deliveryUnifiedCallbackMessageProducer;

	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.DELIVERY_EVENT;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {

		if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
			log.error("开始消费配送事件消息 已废弃: {}", mafkaMessage);
			return CONSUME_SUCCESS;
		}

		log.info("开始消费配送事件消息: {}", mafkaMessage);
		DeliveryEventMessage message = translateMessage(mafkaMessage);
		if (message == null) {
			return CONSUME_SUCCESS;
		}

		try {
			Optional<DeliveryOrder> opDeliveryOrder = deliveryOrderRepository.getDeliveryOrderForceMaster(message.getDeliveryId());
			if (!opDeliveryOrder.isPresent()) {
				log.error("对应运单[{}]不存在，将放弃处理", message.getDeliveryId());
				return CONSUME_SUCCESS;
			}
			DeliveryOrder deliveryOrder = opDeliveryOrder.get();

			deliveryUnifiedCallbackMessageProducer.sendMessage(
					new DeliveryUnifiedCallbackMessage(
							DeliveryCallbackTypeEnum.CHANGE_CALLBACK,
							new DeliveryChangeCallbackInfo(
									deliveryOrder.getOrderId(),
									deliveryOrder.getDeliveryChannel(),
									deliveryOrder.getChannelDeliveryId(),
									deliveryOrder.getChannelServicePackageCode(),
									DeliveryEventEnum.valueOf(message.getEvent()),
									new DeliveryExceptionInfo(
											DeliveryExceptionTypeEnum.valueOf(message.getExceptionType()), message.getExceptionDescription()
									),
									TimeUtil.fromMilliSeconds(message.getEventTime()),
									message.getRider(),
									null,
									null
							)
					),
					deliveryOrder.getOrderId()
			);

			return CONSUME_SUCCESS;

		} catch (Exception e) {
			log.error("消费配送事件消息失败，将会进行重试消费", e);
			return CONSUME_FAILURE;
		}
	}

	private DeliveryEventMessage translateMessage(MafkaMessage mafkaMessage) {
		try {
			DeliveryEventMessage message = translateMessage(mafkaMessage, DeliveryEventMessage.class);
			Preconditions.checkNotNull(message, "empty mafkaMessage");
			Preconditions.checkNotNull(message.getDeliveryId(), "deliveryId is null");
			Preconditions.checkNotNull(message.getEvent(), "event is null");
			Preconditions.checkNotNull(message.getEventTime(), "eventTime is null");
			return message;

		} catch (Exception e) {
			log.error("解析配送事件消息失败:{}", mafkaMessage, e);
			Cat.logEvent("DELIVERY_EVENT_CONSUME_FAILED", "MESSAGE_WRONG");
			return null;
		}
	}
}
