package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderDeliveryStatisticThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.DurationsStatisticsRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.RiderStatisticsRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.StoreStatisticsRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.DurationsStatisticsResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.RiderStatisticsResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.StoreStatisticsResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mapstruct.RiderDeliveryStatisticConverter;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.RiderDeliveryStatisticsApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp.DeliveryRiderStatistics;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp.DeliveryStoreStatistics;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp.RiderDurationDeliveryStatistics;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.StatisticDuration;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RiderDeliveryStatisticThriftServiceImpl implements RiderDeliveryStatisticThriftService {
    
    @Resource
    private RiderDeliveryStatisticsApplicationService statisticsApplicationService;

    @Resource
    private RiderDeliveryStatisticConverter converter;
    
    @MethodLog(logRequest = false, logResponse = true)
    @Override
    public RiderStatisticsResponse queryRiderStatistics(RiderStatisticsRequest request) {
        request.valid();
        List<DeliveryRiderStatistics> riderStatistics = statisticsApplicationService.queryRiderStatistics(
                request.getTenantId(), request.getStoreIds(), request.getRiderAccountIds());
        return new RiderStatisticsResponse(converter.toTDeliveryRiderStatisticsList(riderStatistics));
    }
    
    @MethodLog(logRequest = false, logResponse = true)
    @Override
    public StoreStatisticsResponse queryStoreStatistics(StoreStatisticsRequest request) {
        request.valid();
        List<DeliveryStoreStatistics> storeStatistics = statisticsApplicationService.queryStoreStatistics(
                request.getTenantId(), request.getStoreIds());
        return new StoreStatisticsResponse(converter.toTDeliveryStoreStatisticsList(storeStatistics));
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public DurationsStatisticsResponse queryDurationsStatistics(DurationsStatisticsRequest request) {
        request.valid();
        List<RiderDurationDeliveryStatistics> durationStatistics = statisticsApplicationService.queryDurationsStatistics(request.getTenantId(),
                request.getRiderAccountId(), transform(request.getDurationList()));

        return new DurationsStatisticsResponse(converter.toTRiderDurationDeliveryStatisticList(durationStatistics));


    }

    private List<StatisticDuration> transform(List<DurationsStatisticsRequest.TDuration> durations) {
        if (CollectionUtils.isEmpty(durations)) {
            return Collections.emptyList();
        }
        return durations.stream().map(duration -> {
            StatisticDuration statisticDuration = new StatisticDuration();
            statisticDuration.setBeginDate(LocalDate.parse(duration.beginDate, DateTimeFormatter.ofPattern("yyyyMMdd")));
            statisticDuration.setEndDate(LocalDate.parse(duration.endDate, DateTimeFormatter.ofPattern("yyyyMMdd")));

            return statisticDuration;
        }).collect(Collectors.toList());
    }
}
