package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.service;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.RiderInfoChangeRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderDockingThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.model.RiderInfoChangeBo;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 将歪马和新供给同步渠道公共逻辑收口在这里
 * <AUTHOR>
 */
@Slf4j
@Service
@Rhino
public class OcmsChannelService {

	@Resource
	private ChannelOrderDockingThriftService.Iface channelOrderDockingThriftServiceClient;

	private static final int SUCCESS = 0;

	@CatTransaction
	@Degrade(rhinoKey = "OcmsChannelService-syncRiderInfoChange", fallBackMethod = "syncRiderInfoChangeFallback", timeoutInMilliseconds = 2000)
	public void syncRiderInfoChange(RiderInfoChangeBo riderInfoChangeBo) {
		try {
			DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(riderInfoChangeBo.getOrderBizType());
			if (Objects.isNull(channelType)) {
				log.error("OcmsChannelService syncRiderInfoChange failed, channelType is null");
				return;
			}

			RiderInfoChangeRequest request = new RiderInfoChangeRequest();
			request.setTenantId(riderInfoChangeBo.getTenantId());
			request.setShopId(riderInfoChangeBo.getStoreId());
			request.setOrderId(riderInfoChangeBo.getChannelOrderId());
			request.setChannelId(channelType.getChannelId());
			request.setDeliveryChannelId(riderInfoChangeBo.getDeliveryChannelId());
			request.setDeliveryOrderId(riderInfoChangeBo.getDeliveryOrderId());
			request.setRiderName(riderInfoChangeBo.getRiderName());
			request.setRiderPhone(riderInfoChangeBo.getRiderPhone());

			log.info("OcmsChannelService syncRiderInfoChange, request is {}", request);
			ResultStatus response = channelOrderDockingThriftServiceClient.riderInfoChange(request);
			if (Objects.nonNull(response) && response.getCode() != SUCCESS) {
				log.error("OcmsChannelService syncRiderInfoChange failed, response is {}", response);
			}
		} catch (Exception e) {
			log.error("OcmsChannelService syncRiderInfoChange failed", e);
		}
	}

	public void syncRiderInfoChangeFallback(RiderInfoChangeBo riderInfoChangeBo) {
		log.warn("OcmsChannelService syncRiderInfoChangeFallback, channelOrderId is {}", riderInfoChangeBo.getChannelOrderId());
	}
}
