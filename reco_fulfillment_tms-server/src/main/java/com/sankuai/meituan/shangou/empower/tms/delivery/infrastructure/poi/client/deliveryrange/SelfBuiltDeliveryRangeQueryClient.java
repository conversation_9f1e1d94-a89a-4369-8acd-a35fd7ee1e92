package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.poi.client.deliveryrange;

import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DeliveryResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery.QueryDeliveryRangeRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery.QueryDeliveryRangeResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery.RangeInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.deliveryrange.DeliveryRangeQueryClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.deliveryrange.DeliveryRangeQueryResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.DeliveryRange;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.CoordinateUtil;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.DeliveryResultCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/9
 */
@Slf4j
@Component
public class SelfBuiltDeliveryRangeQueryClient implements DeliveryRangeQueryClient {

	@Resource
	private ChannelDeliveryThriftService.Iface channelDeliveryThriftServiceClient;

	@Override
	@CatTransaction
	public DeliveryRangeQueryResult queryDeliveryRange(SelfBuiltDeliveryPoi deliveryPoi, ThirdDeliveryChannelEnum deliveryChannel) {
		try {
			QueryDeliveryRangeRequest request = new QueryDeliveryRangeRequest(
					deliveryPoi.getTenantId(),
					deliveryPoi.getStoreId(),
					deliveryChannel.getCode(),
					deliveryPoi.getChannelStoreConfigMap().get(deliveryChannel).getOrderedServicePackageCodes().get(0)
			);
			log.info("ChannelDeliveryThriftService.queryDeliveryRange begin, request={}", request);
			QueryDeliveryRangeResponse response = channelDeliveryThriftServiceClient.queryDeliveryRange(request);
			log.info("ChannelDeliveryThriftService.queryDeliveryRange finish, response={}", response);
			if (response == null || response.getResultStatus() == null) {
				return new DeliveryRangeQueryResult(false);
			}

			if (response.getResultStatus().getCode() == SUCCESS.getCode()) {
				List<DeliveryRange> rangeList = Optional.ofNullable(response.getRangeInfos())
						.orElse(new ArrayList<>())
						.stream()
						.map(this::translateDeliveryRange)
						.filter(Objects::nonNull)
						.collect(Collectors.toList());
				if (CollectionUtils.isEmpty(rangeList)) {
					log.error("查询到的配送范围为空，视作查询失败处理");
					return new DeliveryRangeQueryResult(false);
				}
				return new DeliveryRangeQueryResult(rangeList);

			} else {
				DeliveryResultCodeEnum resultCode = Optional.ofNullable(DeliveryResultCodeEnum.enumOf(response.getResultStatus().getCode()))
						.orElse(DeliveryResultCodeEnum.SYSTEM_ERROR);
				return new DeliveryRangeQueryResult(resultCode.isCanRetry());
			}

		} catch (Exception e) {
			log.error("查询渠道配送范围失败", e);
			return new DeliveryRangeQueryResult(true);
		}
	}

	private DeliveryRange translateDeliveryRange(RangeInfo rangeInfo) {
		if (CollectionUtils.isEmpty(rangeInfo.getRanges())) {
			return null;
		}

		return new DeliveryRange(
				rangeInfo.getRangeId(),
				CoordinateTypeEnum.MARS,
				rangeInfo.getRanges()
						.stream()
						.map(it -> CoordinateUtil.translateToCoordinatePoint(it.getLongitude(), it.getLatitude()))
						.collect(Collectors.toList())
		);
	}
}
