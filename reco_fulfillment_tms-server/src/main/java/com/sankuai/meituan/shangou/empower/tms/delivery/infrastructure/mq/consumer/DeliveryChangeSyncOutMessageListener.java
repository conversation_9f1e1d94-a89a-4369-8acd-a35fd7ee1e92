package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.OcmsChannelClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.push.PushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryAsyncOutTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.DeliveryMergeOrderRecordApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.cmd.MergeOrderOperateCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.factory.MergeOrderOperateCmdFactory;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.strategy.MergeOrderOperationCreateStrategy;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.strategy.MergeOrderRecordAddStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.*;

import static com.meituan.mafka.client.consumer.ConsumeStatus.*;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryAsyncOutTypeEnum.*;


/**
 * 监听本服务发送的运单变更消息，目前是过滤出骑手锁定/解锁运单状态消息，并同步到ocms-channel，后者继续通过开放平台同步给TSP-履约服务
 */
@Slf4j
@SuppressWarnings("rawtypes")
@Component
public class DeliveryChangeSyncOutMessageListener extends AbstractDeadLetterConsumer {

    @Resource
    private OcmsChannelClient ocmsChannelClient;

    @Resource
    private PushClient pushClient;

    @Resource
    private MergeOrderOperateCmdFactory mergeOrderOperateCmdFactory;

    @Resource
    private RiderDeliveryOrderRepository riderDeliveryOrderRepository;

    @Resource
    private DeliveryMergeOrderRecordApplicationService deliveryMergeOrderRecordApplicationService;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.DELIVERY_CHANGE_ASYNC_OUT;
    }

    private static Set<DeliveryAsyncOutTypeEnum> asyncOutTypeEnumSet = Sets.newHashSet(LOCK_DELIVERY_STATUS,
            UNLOCK_DELIVERY_STATUS, REPORT_EXCEPTION);


    @Override
    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("DeliveryChangeSyncOutMessageListener start consume: {}", message);
        DeliveryChangeSyncOutMessage syncOutMessage = this.translateMessage(message);

        if(syncOutMessage == null) {
            log.warn("解析得到的消息为null");
            return CONSUME_SUCCESS;
        }

        if (asyncOutTypeEnumSet.contains(DeliveryAsyncOutTypeEnum.getInstance(syncOutMessage.getChangeType()))) {
            //同步ocms-channel
            ocmsChannelClient.syncDeliveryChange(syncOutMessage);
        }

        if (LOCK_DELIVERY_STATUS.getValue() == syncOutMessage.getChangeType()) {
            //语音推送通知店长/副店长
            pushClient.pushMerchantSelfManagerDeliveryPauseWarning(syncOutMessage);
        }

        if (DELIVER_COMPLETE.getValue() == syncOutMessage.getChangeType()) {
            //记录是否并单
            recordMergeOrder(syncOutMessage);
        }
        return CONSUME_SUCCESS;
    }

    public DeliveryChangeSyncOutMessage translateMessage(MafkaMessage message) {
        DeliveryChangeSyncOutMessage outMessage = super.translateMessage(message,
                new TypeReference<DeliveryChangeSyncOutMessage>(){});
        try {
            outMessage.validCheck();
            return outMessage;
        } catch (Exception e) {
            log.error("解析运单变更同步消息失败:{}", message);
            Cat.logEvent("DELIVERY_CHANGE_SYNC_OUT_CONSUME_FAILED", "MESSAGE_WRONG");
            return null;
        }
    }

    private void recordMergeOrder(DeliveryChangeSyncOutMessage syncOutMessage) {
        Optional<RiderDeliveryOrder> deliveryOrder = Optional.empty();

        if(MccConfigUtils.getDeliveryQueryTenantSwitch(syncOutMessage.getHead().getTenantId())){
            deliveryOrder = riderDeliveryOrderRepository.getDeliveryOrderForceMasterWithTenantId(
                    syncOutMessage.getHead().getDeliveryId(),syncOutMessage.getHead().getTenantId(),syncOutMessage.getHead().getShopId());
        }else {
            deliveryOrder = riderDeliveryOrderRepository.getDeliveryOrderForceMaster(
                    syncOutMessage.getHead().getDeliveryId());
        }


        if (!deliveryOrder.isPresent()) {
            log.warn("未查询到对应运单,deliveryId:{}", syncOutMessage.getHead().getDeliveryId());
            return;
        }
        List<MergeOrderOperateCmd> mergeOrderCmdList = mergeOrderOperateCmdFactory.createMergeOrderCmdByStrategy(
                deliveryOrder.get(),
                MergeOrderRecordAddStrategy.class);
        deliveryMergeOrderRecordApplicationService.addMergeOrderRecords(mergeOrderCmdList);
    }
}
