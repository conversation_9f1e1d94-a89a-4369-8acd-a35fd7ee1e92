package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import java.util.Collections;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.DeliveryRemindConfigThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.dto.TDeliveryRemindConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.request.QueryStoreRemindConfigRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.request.SaveStoreRemindConfigRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.response.DeliveryRemindConfigResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.response.RemindConfigCommonResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.remind.DeliveryRemindConfigOpeAppService;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfigRepository;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;

import lombok.extern.slf4j.Slf4j;

/**
 * 配送提醒配置 thrift 服务.
 *
 * <AUTHOR>
 * @since 2021/10/8 16:33
 */
@Service
@Slf4j
public class DeliveryRemindConfigThriftServiceImpl implements DeliveryRemindConfigThriftService {

    @Resource
    private DeliveryRemindConfigRepository deliveryRemindConfigRepository;
    @Resource
    private DeliveryRemindConfigOpeAppService deliveryRemindConfigOpeAppService;

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public DeliveryRemindConfigResponse queryStoreDeliveryRemindConfig(QueryStoreRemindConfigRequest request) {
        String check = request.validate();
        if (check != null) {
            return new DeliveryRemindConfigResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), check));
        }
        try {
            Optional<DeliveryRemindConfig> configOptional = deliveryRemindConfigRepository.queryDeliveryRemindConfig(request.getTenantId(),
                    request.getStoreId());
            TDeliveryRemindConfig tDeliveryRemindConfig = new TDeliveryRemindConfig(request.getTenantId(), request.getStoreId(),
                    Collections.emptyList(), Collections.emptyList());
            if (configOptional.isPresent()) {
                tDeliveryRemindConfig = convert(configOptional.get());
            }
            return new DeliveryRemindConfigResponse(tDeliveryRemindConfig);
        } catch (Exception e) {
            log.error("DeliveryRemindConfigThriftServiceImpl#queryStoreDeliveryRemindConfig error. request:{}", request, e);
            return new DeliveryRemindConfigResponse(new Status(FailureCodeEnum.SYSTEM_ERROR.getCode(),
                    FailureCodeEnum.SYSTEM_ERROR.getMessage()));
        }
    }

    private TDeliveryRemindConfig convert(DeliveryRemindConfig remindConfig) {
        Preconditions.checkNotNull(remindConfig, "remindConfig cannot be null when converted");
        return new TDeliveryRemindConfig(remindConfig.getTenantId(), remindConfig.getStoreId(), remindConfig.getRecipients(), remindConfig.getDhRecipients());
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public RemindConfigCommonResponse saveStoreDeliveryRemindConfig(SaveStoreRemindConfigRequest request) {
        String check = request.validate();
        if (check != null) {
            return new RemindConfigCommonResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), check));
        }
        try {
            deliveryRemindConfigOpeAppService.saveStoreDeliveryRemindConfig(
                    request.getTenantId(), request.getStoreId(),
                    request.getRecipients(), request.getDhRecipients());
            return new RemindConfigCommonResponse(Status.SUCCESS);
        } catch (BizException e) {
            log.warn("DeliveryRemindConfigThriftServiceImpl#queryStoreDeliveryRemindConfig fail. request:{}", request, e);
            return new RemindConfigCommonResponse(new Status(FailureCodeEnum.SYSTEM_ERROR.getCode(), e.getMessage()));
        } catch (Exception e) {
            log.error("DeliveryRemindConfigThriftServiceImpl#queryStoreDeliveryRemindConfig error. request:{}", request, e);
            return new RemindConfigCommonResponse(new Status(FailureCodeEnum.SYSTEM_ERROR.getCode(),
                    FailureCodeEnum.SYSTEM_ERROR.getMessage()));
        }
    }
}
