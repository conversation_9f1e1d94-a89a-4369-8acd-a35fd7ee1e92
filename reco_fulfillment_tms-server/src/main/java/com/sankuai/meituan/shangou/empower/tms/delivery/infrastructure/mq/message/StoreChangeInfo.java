package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StoreChangeInfo {
    /**
     * 消息类型（1-删除门店，2-新增渠道门店关联，3-更换渠道门店关联, 4-删除渠道门店关联）
     */
    private int infoType;

    /**
     * 租户ID
     */
    private long tenantId;

    /**
     * 门店ID
     */
    private long storeId;
    /**
     * 渠道ID
     * 该字段只在infoType为2、3、4场景下有值
     */
    private long channelId;
    /**
     * 线上渠道门店编码(三方)
     * 该字段只在infoType为2、3、4场景下有值
     */
    private String channelOnLinePoiCode;
    /**
     * 更换前线上渠道门店编码(三方)
     * 该字段只在infoType为3场景下有值
     */
    private String originalChannelOnLinePoiCode;

    public static StoreChangeInfo of(int infoType, long tenantId, long storeId) {
        return StoreChangeInfo.builder().infoType(infoType).tenantId(tenantId).storeId(storeId).build();
    }
}
