package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryDimensionPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.StoreDimensionConfigDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreDimensionConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreDimensionConfigDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.*;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/28
 */
@Slf4j
@Repository
public class MySQLDeliveryDimensionPoiRepository implements DeliveryDimensionPoiRepository {

	private static final int ENABLED = 1;
	private static final int NON_DELETED = 0;

	@Resource
	private StoreDimensionConfigDOMapper storeDimensionConfigDOMapper;

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public DeliveryDimensionPoi queryDeliveryDimensionPoi(Long tenantId, Long storeId) {
		try {
			StoreDimensionConfigDOExample example = new StoreDimensionConfigDOExample();
			example.createCriteria()
					.andTenantIdEqualTo(tenantId)
					.andStoreIdEqualTo(storeId)
					.andEnableEqualTo(ENABLED);

			List<StoreDimensionConfigDO> records = storeDimensionConfigDOMapper.selectByExample(example);

			if (CollectionUtils.isNotEmpty(records)) {
				StoreDimensionConfigDO record = records.get(0);
				DeliveryDimensionPoi deliveryDimensionPoi = convertToDeliveryDimensionPoi(record);
				return deliveryDimensionPoi;
			}

			return null;
		} catch (Exception e) {
			log.error("查询配送维度门店配置失败, tenantId: {}, storeId: {}", tenantId, storeId, e);
			return null;
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false)
	public void insertDeliveryDimensionPoi(DeliveryDimensionPoi deliveryDimensionPoi) {
		try {
			if (deliveryDimensionPoi == null) {
				log.warn("插入配送维度门店配置失败，deliveryDimensionPoi为空");
				return;
			}

			StoreDimensionConfigDO record = convertToStoreDimensionConfigDO(deliveryDimensionPoi);
			LocalDateTime now = LocalDateTime.now();
			record.setEnable(ENABLED);
			record.setCreatedAt(now);
			record.setUpdatedAt(now);

			storeDimensionConfigDOMapper.insertSelective(record);
			log.info("插入配送维度门店配置成功, tenantId: {}, storeId: {}",
					deliveryDimensionPoi.getTenantId(), deliveryDimensionPoi.getStoreId());
		} catch (Exception e) {
			log.error("插入配送维度门店配置失败, tenantId: {}, storeId: {}",
					deliveryDimensionPoi.getTenantId(), deliveryDimensionPoi.getStoreId(), e);
			throw new RuntimeException("插入配送维度门店配置失败", e);
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = true)
	public void updateDeliveryDimensionPoi(DeliveryDimensionPoi deliveryDimensionPoi) {
		try {
			if (deliveryDimensionPoi == null) {
				log.warn("更新配送维度门店配置失败，deliveryDimensionPoi为空");
				return;
			}

			StoreDimensionConfigDO record = convertToStoreDimensionConfigDO(deliveryDimensionPoi);
			record.setUpdatedAt(LocalDateTime.now());

			int updateCount = storeDimensionConfigDOMapper.updateByPrimaryKeySelective(record);
			if (updateCount > 0) {
				log.info("更新配送维度门店配置成功, tenantId: {}, storeId: {}",
						deliveryDimensionPoi.getTenantId(), deliveryDimensionPoi.getStoreId());
			} else {
				log.warn("更新配送维度门店配置失败，未找到匹配记录, tenantId: {}, storeId: {}",
						deliveryDimensionPoi.getTenantId(), deliveryDimensionPoi.getStoreId());
			}
		} catch (Exception e) {
			log.error("更新配送维度门店配置失败, tenantId: {}, storeId: {}",
					deliveryDimensionPoi.getTenantId(), deliveryDimensionPoi.getStoreId(), e);
			throw new RuntimeException("更新配送维度门店配置失败", e);
		}
	}

	/**
	 * 将StoreDimensionConfigDO转换为DeliveryDimensionPoi
	 */
	private DeliveryDimensionPoi convertToDeliveryDimensionPoi(StoreDimensionConfigDO record) {
		if (record == null) {
			return null;
		}

		try {
			// 转换枚举类型
			SelfDeliveryModeEnum selfDeliveryMode = SelfDeliveryModeEnum.getByMode(record.getSelfDeliveryMode());
			InternalNavigationModeEnum internalNavigationMode = InternalNavigationModeEnum.getByMode(record.getInternalNavigationMode());
			CompletedSortModeEnum completedSortMode = CompletedSortModeEnum.getByMode(record.getCompletedSortMode());

			// 转换JSON字段
			DeliveryCompleteMode deliveryCompleteMode = parseJsonField(record.getDeliveryCompleteMode(), DeliveryCompleteMode.class);
			DeliveryRemindConfig deliveryRemindConfig = parseJsonField(record.getDeliveryRemindConfig(), DeliveryRemindConfig.class);

			// 转换角色ID列表
			List<Long> riderTransRoles = parseJsonField(record.getRiderTransRoles(), new TypeReference<List<Long>>() {});

			return new DeliveryDimensionPoi(
					record.getId(),
					record.getTenantId(),
					record.getStoreId(),
					selfDeliveryMode,
					internalNavigationMode,
					record.getAssessTimeConfig(),
					deliveryCompleteMode,
					riderTransRoles,
					completedSortMode,
					deliveryRemindConfig,
					record.getCreatedAt(),
					record.getUpdatedAt()
			);
		} catch (Exception e) {
			log.error("转换StoreDimensionConfigDO到DeliveryDimensionPoi失败, id: {}", record.getId(), e);
			throw new RuntimeException("转换StoreDimensionConfigDO到DeliveryDimensionPoi失败", e);
		}
	}

	/**
	 * 将DeliveryDimensionPoi转换为StoreDimensionConfigDO
	 */
	private StoreDimensionConfigDO convertToStoreDimensionConfigDO(DeliveryDimensionPoi deliveryDimensionPoi) {
		if (deliveryDimensionPoi == null) {
			return null;
		}

		try {
			StoreDimensionConfigDO record = new StoreDimensionConfigDO();

			// 设置基本字段
			record.setId(deliveryDimensionPoi.getId());
			record.setTenantId(deliveryDimensionPoi.getTenantId());
			record.setStoreId(deliveryDimensionPoi.getStoreId());

			// 转换枚举字段
			record.setSelfDeliveryMode(deliveryDimensionPoi.getSelfDeliveryMode() != null ?
					deliveryDimensionPoi.getSelfDeliveryMode().getMode() : null);
			record.setInternalNavigationMode(deliveryDimensionPoi.getInternalNavigationMode() != null ?
					deliveryDimensionPoi.getInternalNavigationMode().getMode() : null);
			record.setCompletedSortMode(deliveryDimensionPoi.getCompletedSortMode() != null ?
					deliveryDimensionPoi.getCompletedSortMode().getMode() : null);

			// 转换JSON字段
			record.setAssessTimeConfig(toJsonString(deliveryDimensionPoi.getAssessTimeConfig()));
			record.setDeliveryCompleteMode(toJsonString(deliveryDimensionPoi.getDeliveryCompleteMode()));
			record.setDeliveryRemindConfig(toJsonString(deliveryDimensionPoi.getDeliveryRemindConfig()));
			record.setRiderTransRoles(toJsonString(deliveryDimensionPoi.getRiderTransRoles()));

			// 设置时间字段
			record.setCreatedAt(deliveryDimensionPoi.getCreatedAt());
			record.setUpdatedAt(deliveryDimensionPoi.getUpdatedAt());

			return record;
		} catch (Exception e) {
			log.error("转换DeliveryDimensionPoi到StoreDimensionConfigDO失败, tenantId: {}, storeId: {}",
					deliveryDimensionPoi.getTenantId(), deliveryDimensionPoi.getStoreId(), e);
			throw new RuntimeException("转换DeliveryDimensionPoi到StoreDimensionConfigDO失败", e);
		}
	}

	/**
	 * 解析JSON字段为指定类型
	 */
	private <T> T parseJsonField(String json, Class<T> clazz) {
		if (StringUtils.isBlank(json)) {
			return null;
		}
		try {
			return JSON.parseObject(json, clazz);
		} catch (Exception e) {
			log.warn("解析JSON字段失败, json: {}, class: {}", json, clazz.getSimpleName(), e);
			return null;
		}
	}

	/**
	 * 解析JSON字段为指定类型（支持泛型）
	 */
	private <T> T parseJsonField(String json, TypeReference<T> typeReference) {
		if (StringUtils.isBlank(json)) {
			return null;
		}
		try {
			return JSON.parseObject(json, typeReference);
		} catch (Exception e) {
			log.warn("解析JSON字段失败, json: {}, typeReference: {}", json, typeReference.getType(), e);
			return null;
		}
	}

	/**
	 * 将对象转换为JSON字符串
	 */
	private String toJsonString(Object obj) {
		if (obj == null) {
			return null;
		}
		try {
			return JSON.toJSONString(obj);
		} catch (Exception e) {
			log.warn("对象转换为JSON字符串失败, obj: {}", obj, e);
			return null;
		}
	}
}
