package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.labor;

import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.sankuai.drunkhorsemgmt.labor.thrift.AttendanceThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.QueryPoiOnDutyEmployeeCountRequest;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.QueryPoiOnDutyEmployeeCountResp;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.labor.AttendanceSystemClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/1/10 16:57
 **/
@Service
@Slf4j
public class AttendanceSystemClientImpl implements AttendanceSystemClient {
    @Resource
    private AttendanceThriftService attendanceThriftService;


    @Override
    public Map<Long, Integer> queryStoreOnDutyEmployeeCount(Long tenantId, List<Long> storeIds) {
        QueryPoiOnDutyEmployeeCountRequest request = new QueryPoiOnDutyEmployeeCountRequest();
        request.setStoreIds(storeIds);
        request.setTenantId(tenantId);
        QueryPoiOnDutyEmployeeCountResp resp = null;
        try {
            resp = attendanceThriftService.queryPoiOnDutyEmployeeCount(request);
            log.info("invoke attendanceThriftService.queryPoiOnDutyEmployeeCount, request:{}, resp:{}", request, resp);
        } catch (Exception e) {
            log.error("invoke attendanceThriftService.queryPoiOnDutyEmployeeCount error, request:{}", request, e);
            throw new RuntimeException("查询门店在岗员工人数失败");
        }

        if (Objects.isNull(resp) || Objects.isNull(resp.getStatus())) {
            log.error("invoke attendanceThriftService.queryPoiOnDutyEmployeeCount error, request:{}, resp:{}", request, resp);
            throw new RuntimeException("查询门店在岗员工人数失败");
        }

        if (resp.getStatus().getCode() != ResultCode.SUCCESS.code) {
            log.warn("invoke attendanceThriftService.queryPoiOnDutyEmployeeCount fail, request:{}, resp:{}", request, resp);
            throw new RuntimeException("查询门店在岗员工人数失败");
        }

        return Objects.isNull(resp.getPoiOnDutyEmployeeCount()) ? Collections.emptyMap() : resp.getPoiOnDutyEmployeeCount();
    }
}
