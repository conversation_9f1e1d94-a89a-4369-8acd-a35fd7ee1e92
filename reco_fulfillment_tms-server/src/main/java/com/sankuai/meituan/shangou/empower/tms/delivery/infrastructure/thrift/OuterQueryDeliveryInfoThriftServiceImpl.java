package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.OuterQueryDeliveryInfoApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.exception.OuterQueryDeliveryOrderTypeNotSupportException;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.OuterQueryDeliveryInfoThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.OuterQueryDeliveryInfoDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.OuterQueryRiderTrackDynamicInfoDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.OuterQueryRiderTrackImmutableInfoDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.OuterDeliveryInfoQueryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.OuterRiderTrackQueryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.OuterQueryDeliveryInfoResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.OuterQueryRiderTrackDynamicInfoResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.OuterQueryRiderTrackImmutableInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.SYSTEM_ERROR;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OuterQueryDeliveryInfoThriftServiceImpl implements OuterQueryDeliveryInfoThriftService {

    @Resource
    private OuterQueryDeliveryInfoApplicationService outerQueryDeliveryInfoApplicationService;

    @Override
    public OuterQueryRiderTrackImmutableInfoResponse getDeliveryImmutableInfo(OuterRiderTrackQueryRequest outerRiderTrackQueryRequest) {
        Long orderId = outerRiderTrackQueryRequest.getOrderId();
        try {
            OuterQueryRiderTrackImmutableInfoDto data = outerQueryDeliveryInfoApplicationService.getDeliveryImmutableInfo(orderId);
            return OuterQueryRiderTrackImmutableInfoResponse.builder().status(Status.SUCCESS).data(data).build();
        } catch (OuterQueryDeliveryOrderTypeNotSupportException e) {
            log.error("OuterQueryDeliveryInfoThriftServiceImpl getDeliveryImmutableInfo OuterQueryDeliveryOrderTypeNotSupportException", e);
            return OuterQueryRiderTrackImmutableInfoResponse.builder()
                    .status(new Status(FailureCodeEnum.OUTER_QUERY_DELIVERY_ORDER_TYPE_NOT_SUPPORT.getCode(),
                            FailureCodeEnum.OUTER_QUERY_DELIVERY_ORDER_TYPE_NOT_SUPPORT.getMessage()))
                    .data(OuterQueryRiderTrackImmutableInfoDto.EMPTY).build();
        } catch (Exception e) {
            log.error("OuterQueryDeliveryInfoThriftServiceImpl getDeliveryImmutableInfo failed", e);
            return OuterQueryRiderTrackImmutableInfoResponse.builder()
                    .status(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()))
                    .data(OuterQueryRiderTrackImmutableInfoDto.EMPTY).build();
        }
    }

    @Override
    public OuterQueryRiderTrackDynamicInfoResponse getDeliveryDynamicInfo(OuterRiderTrackQueryRequest outerRiderTrackQueryRequest) {
        Long orderId = outerRiderTrackQueryRequest.getOrderId();
        try {
            OuterQueryRiderTrackDynamicInfoDto data = outerQueryDeliveryInfoApplicationService.getDeliveryDynamicInfo(orderId);
            return OuterQueryRiderTrackDynamicInfoResponse.builder().status(Status.SUCCESS).data(data).build();
        } catch (OuterQueryDeliveryOrderTypeNotSupportException e) {
            log.error("OuterQueryDeliveryInfoThriftServiceImpl getDeliveryDynamicInfo OuterQueryDeliveryOrderTypeNotSupportException", e);
            return OuterQueryRiderTrackDynamicInfoResponse.builder()
                    .status(new Status(FailureCodeEnum.OUTER_QUERY_DELIVERY_ORDER_TYPE_NOT_SUPPORT.getCode(),
                            FailureCodeEnum.OUTER_QUERY_DELIVERY_ORDER_TYPE_NOT_SUPPORT.getMessage()))
                    .data(OuterQueryRiderTrackDynamicInfoDto.EMPTY).build();
        } catch (Exception e) {
            log.error("OuterQueryDeliveryInfoThriftServiceImpl getDeliveryDynamicInfo failed", e);
            return OuterQueryRiderTrackDynamicInfoResponse.builder()
                    .status(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()))
                    .data(OuterQueryRiderTrackDynamicInfoDto.EMPTY).build();
        }
    }

    @Override
    public OuterQueryDeliveryInfoResponse queryDeliveryInfo(OuterDeliveryInfoQueryRequest outerDeliveryInfoQueryRequest) {
        Long orderId = outerDeliveryInfoQueryRequest.getOrderId();
        try {
            OuterQueryDeliveryInfoDto data = outerQueryDeliveryInfoApplicationService.queryDeliveryInfo(orderId);
            return OuterQueryDeliveryInfoResponse.builder().status(Status.SUCCESS).data(data).build();
        } catch (OuterQueryDeliveryOrderTypeNotSupportException e) {
            return OuterQueryDeliveryInfoResponse.builder()
                    .status(new Status(FailureCodeEnum.OUTER_QUERY_DELIVERY_ORDER_TYPE_NOT_SUPPORT.getCode(),
                            FailureCodeEnum.OUTER_QUERY_DELIVERY_ORDER_TYPE_NOT_SUPPORT.getMessage()))
                    .data(OuterQueryDeliveryInfoDto.EMPTY).build();
        } catch (Exception e) {
            log.error("OuterQueryDeliveryInfoThriftServiceImpl queryDeliveryInfo failed", e);
            return OuterQueryDeliveryInfoResponse.builder()
                    .status(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()))
                    .data(OuterQueryDeliveryInfoDto.EMPTY).build();
        }
    }
}
