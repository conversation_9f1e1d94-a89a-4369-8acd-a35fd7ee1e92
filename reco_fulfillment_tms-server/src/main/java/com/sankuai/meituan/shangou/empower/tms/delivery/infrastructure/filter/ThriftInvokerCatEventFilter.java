package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.filter;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.dorado.common.RpcRole;
import com.meituan.dorado.rpc.handler.filter.Filter;
import com.meituan.dorado.rpc.handler.filter.FilterHandler;
import com.meituan.dorado.rpc.meta.RpcInvocation;
import com.meituan.dorado.rpc.meta.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * Create by yujing10 on 2020/2/20.
 */
@Slf4j
public class ThriftInvokerCatEventFilter implements Filter {
    private static final String TENANT_ID = "tenantId";


    @Override
    public RpcResult filter(RpcInvocation invocation, FilterHandler nextHandler) throws Throwable {
        StringBuilder builder = new StringBuilder();
        builder.append(ThriftFilterAndInterceptorUtil.resolveBasicParamValue(invocation.getArguments(), TENANT_ID));
        RpcResult result;
        try {
            long startTime = System.currentTimeMillis();
            result = nextHandler.handle(invocation);
            long endTime = System.currentTimeMillis();
            long SLOW_RPC_COST_VALVE = 100L;
            if (endTime - startTime > SLOW_RPC_COST_VALVE) {//大于100ms则统计为慢rpc调用
                Transaction transaction = Cat.newTransaction(builder.append(rpcLongCatEvent()).toString(), builder.append(invocation.getServiceInterface().getSimpleName()).append(".").append(invocation.getMethod().getName()).toString());
                transaction.setStatus("0");//调用成功
                transaction.complete();
            }
            return result;
        } catch (Throwable throwable) {
            Transaction transaction = Cat.newTransaction(builder.append(rpcExceptionCatEvent()).toString(), builder.append(invocation.getServiceInterface().getSimpleName()).append(".").append(invocation.getMethod().getName()).toString());
            transaction.setStatus(throwable.getMessage());//调用出错
            transaction.complete();
            throw throwable;
        }
    }

    @Override
    public int getPriority() {
        return 0;
    }

    @Override
    public RpcRole getRole() {
        return RpcRole.INVOKER;
    }

    public String rpcLongCatEvent() {
        return "_Rpc_Long_Call_Event_";
    }

    public String rpcExceptionCatEvent() {
        return "_Rpc_Exception_Event_";
    }
}
