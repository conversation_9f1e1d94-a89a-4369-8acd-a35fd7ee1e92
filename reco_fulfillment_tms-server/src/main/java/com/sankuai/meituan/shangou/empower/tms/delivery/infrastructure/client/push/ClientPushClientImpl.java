package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.push;

import com.dianping.rhino.annotation.Retry;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.push.ClientPushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.push.SendSmsRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.push.SendTemplateMessageRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.push.SendSmsResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.push.SendTemplateMessageResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.thrift.DrunkHorsePushThriftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/5/6 17:17
 **/
@Component
@Slf4j
public class ClientPushClientImpl implements ClientPushClient {

    @Resource
    private DrunkHorsePushThriftService drunkHorsePushThriftService;


    @Retryable(value = CommonRuntimeException.class, maxAttempts = 3, backoff = @Backoff(delay = 1000L, multiplier = 1.5))
    public void sendTemplateMessage(Long userId, Long tenantId, String templateId, Map<String, String> dataContent, String pageLink) {
        SendTemplateMessageRequest request = new SendTemplateMessageRequest();
        request.setTemplateId(templateId);
        request.setPage(pageLink);
        request.setUserId(userId);
        request.setTenantId(tenantId);
        request.setDataContent(dataContent);
        SendTemplateMessageResponse response = null;
        try {
            log.info("invoke pushThriftService.sendTemplateMessage start, request:{}", request);
            response = drunkHorsePushThriftService.sendTemplateMessage(request);
            log.info("invoke pushThriftService.sendTemplateMessage end, response:{}", response);
        } catch (Exception e) {
            log.error("invoke pushThriftService.sendTemplateMessage error", e);
            throw new CommonRuntimeException("推送微信消息失败");
        }

        if (response == null || response.getCode() == null ) {
            log.error("invoke pushThriftService.sendTemplateMessage error, response:{}", response);
            throw new CommonRuntimeException("推送微信消息失败");
        }

        if(response.getCode() != ResultCode.SUCCESS.getCode()) {
            log.warn("invoke pushThriftService.sendTemplateMessage fail, response:{}", response);
            throw new BizException("推送微信消息失败");
        }

    }


    @Retryable(value = CommonRuntimeException.class, maxAttempts = 3, backoff = @Backoff(delay = 1000L, multiplier = 1.5))
    public void sendSms(Long userId, Long tenantId, String templateId, Map<String, String> dataContent) {
        SendSmsRequest request = new SendSmsRequest();
        request.setTemplateId(templateId);
        request.setUserId(userId);
        request.setTenantId(tenantId);
        request.setDataContent(dataContent);
        SendSmsResponse response = null;
        try {
            log.info("invoke pushThriftService.sendSms start, request:{}", request);
            response = drunkHorsePushThriftService.sendSms(request);
            log.info("invoke pushThriftService.sendSms end, response:{}", response);
        } catch (Exception e) {
            log.error("invoke pushThriftService.sendSms error");
            throw new CommonRuntimeException("推送短信消息失败");
        }

        if (response == null || response.getCode() == null ) {
            log.error("invoke pushThriftService.sendSms error");
            throw new CommonRuntimeException("推送短信消息失败");
        }

        if(response.getCode() != ResultCode.SUCCESS.getCode()) {
            log.warn("invoke pushThriftService.sendSms fail, response:{}", response);
            throw new BizException("推送短信消息失败");
        }

    }
}
