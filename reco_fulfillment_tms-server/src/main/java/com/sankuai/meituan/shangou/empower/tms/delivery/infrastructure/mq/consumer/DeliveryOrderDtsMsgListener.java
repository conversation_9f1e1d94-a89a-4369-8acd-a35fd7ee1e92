package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MoneyUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.DeliveryOrderEsPatternDao;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.constants.DeliveryOrderConstants;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.po.DeliveryOrderEsPo;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils.ObjectMapperWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryOrderEsCompensateCraneTaskException;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DeliveryOrderDtsMsgBody;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DtsMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder.DELIVERY_ORDER_ACTIVE;
import static com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.constants.DeliveryOrderConstants.FIELD_EXT_DEAL_DEADLINE;

/**
 * 消费delivery_order表的dts消息
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeliveryOrderDtsMsgListener extends AbstractEsDtsMsgListener<DeliveryOrderDtsMsgBody> {

    @Resource
    private DeliveryChannelApplicationService deliveryChannelApplicationService;

    @Resource
    private DeliveryOrderEsPatternDao deliveryOrderEsPatternDao;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private OrderSystemClient orderSystemClient;

    @Resource
    protected ObjectMapperWrapper objectMapper;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.DTS_DELIVERY_ORDER_CONSUMER;
    }

    @Override
    protected DtsMsg<DeliveryOrderDtsMsgBody> deserialize(Object msgBody) {
        return objectMapper.readValue(msgBody.toString(), new TypeReference<DtsMsg<DeliveryOrderDtsMsgBody>>(){});
    }

    @Override
    protected void checkMessageParameter(DeliveryOrderDtsMsgBody msgBody) {
        Objects.requireNonNull(msgBody.getOrderId(), "buildDeliveryOrderDoc, order id is null");
        Objects.requireNonNull(msgBody.getActiveStatus(), "buildDeliveryOrderDoc, active status is null");
        Objects.requireNonNull(msgBody.getCreateTime(), "buildDeliveryOrderDoc, createTime is null");
        Objects.requireNonNull(msgBody.getUpdateTime(), "buildDeliveryOrderDoc, updateTime is null");
        Objects.requireNonNull(msgBody.getStoreId(), "buildDeliveryOrderDoc, storeId is null");
        Objects.requireNonNull(msgBody.getDeliveryChannel(), "buildDeliveryOrderDoc, deliveryChannel is null");
    }

    @Override
    protected boolean preCheckBusiness(DeliveryOrderDtsMsgBody msgBody) {
        return true;
    }

    @Override
    protected void onInsert(DeliveryOrderDtsMsgBody afterInsert) {
        aggAndUpsertDeliveryOrderEsPo(afterInsert);
    }

    @Override
    protected void onUpdate(DeliveryOrderDtsMsgBody afterUpdate, Map<String, Object> updatedField2OldValueMap) {
        aggAndUpsertDeliveryOrderEsPo(afterUpdate);
    }

    private void aggAndUpsertDeliveryOrderEsPo(DeliveryOrderDtsMsgBody msgBody) {
        if (isDeliveryOrderCompleted(msgBody)) {
            processCompletedDeliveryOrder(msgBody);
            return;
        }

        DeliveryOrderEsPo currentEsPo = deliveryOrderEsPatternDao.getDeliveryOrderEsPoByOrderId(msgBody.getOrderId(), msgBody.getStoreId());
        DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(msgBody.getDeliveryChannel());

        if (Objects.isNull(currentEsPo)) {
            DeliveryOrderEsPo deliveryOrderEsPo = buildCreateDeliveryOrderDoc(msgBody, deliveryChannel);
            deliveryOrderEsPatternDao.saveDeliveryOrderEsPo(deliveryOrderEsPo);
        } else {
            if (msgBody.getActiveStatus().equals(DELIVERY_ORDER_ACTIVE)) {
                fillCurrentEsPoWhenMsgActiveStatus(msgBody, currentEsPo, deliveryChannel);
            } else {
                fillCurrentEsPoWhenMsgNoActiveStatus(msgBody, currentEsPo, deliveryChannel);
            }
            deliveryOrderEsPatternDao.updateWithSeqNoAndPrimaryTerm(currentEsPo);
        }
    }

    private void fillCurrentEsPoWhenMsgActiveStatus(DeliveryOrderDtsMsgBody msgBody, DeliveryOrderEsPo currentEsPo, DeliveryChannel deliveryChannel) {
        if (currentEsPo.getActiveStatus().equals(DELIVERY_ORDER_ACTIVE)) {
            if (msgBody.getUpdateTime().isAfter(currentEsPo.getUpdateTime())) {
                fillDeliveryOrderDoc4Available(currentEsPo, msgBody, deliveryChannel);
            }
        } else {
            fillDeliveryOrderDoc4Available(currentEsPo, msgBody, deliveryChannel);
        }

        fillDeliveryOrderDoc4Trace(currentEsPo, msgBody, deliveryChannel);
    }

    private void fillCurrentEsPoWhenMsgNoActiveStatus(DeliveryOrderDtsMsgBody msgBody, DeliveryOrderEsPo currentEsPo, DeliveryChannel deliveryChannel) {

        // 优先找出激活的运单，如果没有激活的，则按照id取最大的一个
        if (currentEsPo.getActiveStatus().equals(DELIVERY_ORDER_ACTIVE)) {
            if (msgBody.getUpdateTime().isAfter(currentEsPo.getUpdateTime())) {
                fillDeliveryOrderDoc4Available(currentEsPo, msgBody, deliveryChannel);
            }
        } else {
            if (msgBody.getId() > currentEsPo.getDeliveryOrderId()) {
                fillDeliveryOrderDoc4Available(currentEsPo, msgBody, deliveryChannel);
            }
        }

        fillDeliveryOrderDoc4Trace(currentEsPo, msgBody, deliveryChannel);
    }

    private DeliveryOrderEsPo buildCreateDeliveryOrderDoc(DeliveryOrderDtsMsgBody msgBody, DeliveryChannel deliveryChannel) {
        DeliveryOrderEsPo deliveryOrderEsPo = new DeliveryOrderEsPo();
        fillDeliveryOrderDoc4Base(deliveryOrderEsPo, msgBody);
        fillDeliveryOrderDoc4Available(deliveryOrderEsPo, msgBody, deliveryChannel);
        fillDeliveryOrderDoc4Trace(deliveryOrderEsPo, msgBody, deliveryChannel);
        return deliveryOrderEsPo;
    }

    private void fillDeliveryOrderDoc4Base(DeliveryOrderEsPo deliveryOrderEsPo, DeliveryOrderDtsMsgBody msgBody) {
        deliveryOrderEsPo.setId(String.valueOf(msgBody.getOrderId()));
        deliveryOrderEsPo.setIndexKey(msgBody.getCreateTime());
        deliveryOrderEsPo.setRoutingValue(String.valueOf(msgBody.getStoreId()));
    }
    private void fillDeliveryOrderDoc4Available(DeliveryOrderEsPo deliveryOrderEsPo, DeliveryOrderDtsMsgBody msgBody, DeliveryChannel deliveryChannel) {
        JsonNode extInfoNode = JsonUtil.toJsonNode(msgBody.getExtInfo());
        deliveryOrderEsPo.setDeliveryOrderId(msgBody.getId());
        deliveryOrderEsPo.setTenantId(msgBody.getTenantId());
        deliveryOrderEsPo.setStoreId(msgBody.getStoreId());
        deliveryOrderEsPo.setOrderId(msgBody.getOrderId());
        deliveryOrderEsPo.setChannelOrderId(msgBody.getChannelOrderId());
        deliveryOrderEsPo.setOrderBizType(msgBody.getOrderBizType());
        deliveryOrderEsPo.setDaySeq(msgBody.getDaySeq());
        deliveryOrderEsPo.setReserved(msgBody.getReserved());
        deliveryOrderEsPo.setOrderSource(msgBody.getOrderSource());
        deliveryOrderEsPo.setReceiverName(msgBody.getReceiverName());
        deliveryOrderEsPo.setReceiverPhone(msgBody.getReceiverPhone());
        deliveryOrderEsPo.setReceiverPrivacyPhone(msgBody.getReceiverPrivacyPhone());
        deliveryOrderEsPo.setReceiverAddress(msgBody.getReceiverAddress());
        deliveryOrderEsPo.setEstimatedDeliveryTime(msgBody.getEstimatedDeliveryTime());
        deliveryOrderEsPo.setEstimatedDeliveryEndTime(msgBody.getEstimatedDeliveryEndTime());
        deliveryOrderEsPo.setDeliveryChannelCode(msgBody.getDeliveryChannel());
        deliveryOrderEsPo.setDeliveryChannelName(deliveryChannel.getCarrierName());
        deliveryOrderEsPo.setDeliveryPlatformCode(deliveryChannel.getDeliveryPlatFormCode());
        deliveryOrderEsPo.setChannelDeliveryId(msgBody.getChannelDeliveryId());
        deliveryOrderEsPo.setDeliveryStatus(msgBody.getDeliveryStatus());
        deliveryOrderEsPo.setDeliveryExceptionType(msgBody.getDeliveryExceptionType());
        deliveryOrderEsPo.setDeliveryExceptionDescription(msgBody.getDeliveryExceptionDescription());
        deliveryOrderEsPo.setRiderName(msgBody.getRiderName());
        deliveryOrderEsPo.setRiderPhone(msgBody.getRiderPhone());
        deliveryOrderEsPo.setActiveStatus(msgBody.getActiveStatus());
        deliveryOrderEsPo.setCreateTime(msgBody.getCreateTime());
        deliveryOrderEsPo.setUpdateTime(msgBody.getUpdateTime());
        deliveryOrderEsPo.setLastEventTime(msgBody.getLastEventTime());
        deliveryOrderEsPo.setDeliveryDoneTime(msgBody.getDeliveryDoneTime());
        deliveryOrderEsPo.setDeliveryFee(MoneyUtil.fromYuanToCenter(msgBody.getDeliveryFee()).intValue());
        deliveryOrderEsPo.setDistance(msgBody.getDistance());
        deliveryOrderEsPo.setCancelMark(msgBody.getCancelMark());
        deliveryOrderEsPo.setDeliveryExceptionCode(msgBody.getDeliveryExceptionCode());
        deliveryOrderEsPo.setTipAmount(MoneyUtil.fromYuanToCenter(msgBody.getTipAmount()).intValue());
        deliveryOrderEsPo.setDeliveryCount(msgBody.getDeliveryCount());
        deliveryOrderEsPo.setPlatformFee(MoneyUtil.fromYuanToCenter(msgBody.getPlatformFee()).intValue());
        deliveryOrderEsPo.setAllowLatestAuditTime(getAllowLatestAuditTime(extInfoNode));
        deliveryOrderEsPo.setOriginWaybillNo(getOriginWaybillNo(extInfoNode));
    }

    private void fillDeliveryOrderDoc4Trace(DeliveryOrderEsPo deliveryOrderEsPo, DeliveryOrderDtsMsgBody msgBody, DeliveryChannel deliveryChannel) {
        List<DeliveryOrderEsPo.DeliveryOrderSubPo> deliveryOrderSubPoList = JsonUtil.fromJson(deliveryOrderEsPo.getDeliveryTraceList(),
                new TypeReference<List<DeliveryOrderEsPo.DeliveryOrderSubPo>>() {});

        if (CollectionUtils.isEmpty(deliveryOrderSubPoList)) {
            // 索引里没有deliveryOrderEsPo，save的时候需新建一个deliveryOrderSubPo
            DeliveryOrderEsPo.DeliveryOrderSubPo deliveryOrderSubPo = buildDeliveryOrderSubPo(msgBody, deliveryChannel);
            List<DeliveryOrderEsPo.DeliveryOrderSubPo> deliveryOrderSubPos = Collections.singletonList(deliveryOrderSubPo);
            deliveryOrderEsPo.setDeliveryTraceList(JsonUtil.toJson(deliveryOrderSubPos));
            return;
        }

        Map<Long, DeliveryOrderEsPo.DeliveryOrderSubPo> deliveryOrderSubPoMap = deliveryOrderSubPoList.stream().collect(Collectors.toMap(DeliveryOrderEsPo.DeliveryOrderSubPo::getDeliveryOrderId, Function.identity()));
        if (deliveryOrderSubPoMap.containsKey(msgBody.getId())) {
            DeliveryOrderEsPo.DeliveryOrderSubPo existDeliveryOrderSubPo = deliveryOrderSubPoMap.get(msgBody.getId());
            if (msgBody.getUpdateTime().isAfter(existDeliveryOrderSubPo.getUpdateTime())) {
                fillDeliveryOrderSubPoWithMsgBody(existDeliveryOrderSubPo, msgBody, deliveryChannel);
                deliveryOrderSubPoMap.put(msgBody.getId(), existDeliveryOrderSubPo);
                deliveryOrderSubPoList = Lists.newArrayList(deliveryOrderSubPoMap.values());
            }
        } else {
            DeliveryOrderEsPo.DeliveryOrderSubPo deliveryOrderSubPo = buildDeliveryOrderSubPo(msgBody, deliveryChannel);
            deliveryOrderSubPoList.add(deliveryOrderSubPo);
        }

        deliveryOrderSubPoList.sort(Comparator.comparing(DeliveryOrderEsPo.DeliveryOrderSubPo::getUpdateTime).reversed());
        deliveryOrderEsPo.setDeliveryTraceList(JsonUtil.toJson(deliveryOrderSubPoList));
    }

    private DeliveryOrderEsPo.DeliveryOrderSubPo buildDeliveryOrderSubPo(DeliveryOrderDtsMsgBody msgBody, DeliveryChannel deliveryChannel) {
        DeliveryOrderEsPo.DeliveryOrderSubPo deliveryOrderSubPo = new DeliveryOrderEsPo.DeliveryOrderSubPo();
        fillDeliveryOrderSubPoWithMsgBody(deliveryOrderSubPo, msgBody, deliveryChannel);
        return deliveryOrderSubPo;
    }

    private void fillDeliveryOrderSubPoWithMsgBody(DeliveryOrderEsPo.DeliveryOrderSubPo deliveryOrderSubPo, DeliveryOrderDtsMsgBody msgBody, DeliveryChannel deliveryChannel) {
        JsonNode extInfoNode = JsonUtil.toJsonNode(msgBody.getExtInfo());
        deliveryOrderSubPo.setDeliveryOrderId(msgBody.getId());
        deliveryOrderSubPo.setDeliveryChannelCode(msgBody.getDeliveryChannel());
        deliveryOrderSubPo.setDeliveryChannelName(deliveryChannel.getCarrierName());
        deliveryOrderSubPo.setDeliveryPlatformCode(deliveryChannel.getDeliveryPlatFormCode());
        deliveryOrderSubPo.setChannelDeliveryId(msgBody.getChannelDeliveryId());
        deliveryOrderSubPo.setDeliveryStatus(msgBody.getDeliveryStatus());
        deliveryOrderSubPo.setDeliveryExceptionType(msgBody.getDeliveryExceptionType());
        deliveryOrderSubPo.setDeliveryExceptionDescription(msgBody.getDeliveryExceptionDescription());
        deliveryOrderSubPo.setRiderName(msgBody.getRiderName());
        deliveryOrderSubPo.setRiderPhone(msgBody.getRiderPhone());
        deliveryOrderSubPo.setActiveStatus(msgBody.getActiveStatus());
        deliveryOrderSubPo.setCreateTime(msgBody.getCreateTime());
        deliveryOrderSubPo.setUpdateTime(msgBody.getUpdateTime());
        deliveryOrderSubPo.setLastEventTime(msgBody.getLastEventTime());
        deliveryOrderSubPo.setDeliveryDoneTime(msgBody.getDeliveryDoneTime());
        deliveryOrderSubPo.setDeliveryFee(MoneyUtil.fromYuanToCenter(msgBody.getDeliveryFee()).intValue());
        deliveryOrderSubPo.setDistance(msgBody.getDistance());
        deliveryOrderSubPo.setCancelMark(msgBody.getCancelMark());
        deliveryOrderSubPo.setDeliveryExceptionCode(msgBody.getDeliveryExceptionCode());
        deliveryOrderSubPo.setTipAmount(MoneyUtil.fromYuanToCenter(msgBody.getTipAmount()).intValue());
        deliveryOrderSubPo.setDeliveryCount(msgBody.getDeliveryCount());
        deliveryOrderSubPo.setPlatformFee(MoneyUtil.fromYuanToCenter(msgBody.getPlatformFee()).intValue());
        deliveryOrderSubPo.setAllowLatestAuditTime(getAllowLatestAuditTime(extInfoNode));
        deliveryOrderSubPo.setOriginWaybillNo(getOriginWaybillNo(extInfoNode));
    }

    private String getAllowLatestAuditTime(JsonNode extInfoNode) {
        if (Objects.isNull(extInfoNode)) {
            return StringUtils.EMPTY;
        }
        return Optional.of(extInfoNode)
                .map(it -> it.get(FIELD_EXT_DEAL_DEADLINE))
                .map(JsonNode::toString)
                .orElse(StringUtils.EMPTY);
    }

    private String getOriginWaybillNo(JsonNode extInfoNode) {
        if (Objects.isNull(extInfoNode)) {
            return null;
        }
        return Optional.of(extInfoNode)
                .map(it -> it.get(DeliveryOrderConstants.ORIGIN_WAYBILL_NO))
                .map(JsonNode::asText)
                .orElse(StringUtils.EMPTY);
    }

    /**
     * 这里运单完成的语义是骑手已送达
    */
    private boolean isDeliveryOrderCompleted(DeliveryOrderDtsMsgBody msgBody) {
        return msgBody.getDeliveryStatus() == DeliveryStatusEnum.DELIVERY_DONE.getCode();
    }

    private void processCompletedDeliveryOrder(DeliveryOrderDtsMsgBody msgBody) {
        Long orderId = msgBody.getOrderId();

        // 查询运单主库获取运单列表
        List<DeliveryOrder> deliveryOrders = new ArrayList<>();

        if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
            deliveryOrders = deliveryOrderRepository.getDeliveryOrdersWithTenantOrderId(orderId, msgBody.getTenantId(), msgBody.getStoreId());
        }else {
            deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMaster(orderId);
        }

        if (CollectionUtils.isEmpty(deliveryOrders)) {
            log.warn("compensateSingleOrder, deliveryOrders is empty");
            return;
        }

        // 查询承运商信息
        Set<Integer> carrierCodeSet = deliveryOrders.stream().map(DeliveryOrder::getDeliveryChannel).collect(Collectors.toSet());
        List<DeliveryChannel> deliveryChannelList = deliveryChannelApplicationService.batchQueryDeliveryChannelByCarrierCodeSet(carrierCodeSet);
        Map<Integer, DeliveryChannel> deliveryChannelMap = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, Function.identity()));

        // 获取当前DB里的有效运单
        DeliveryOrder availableDeliveryOrder = filterAvailableDeliveryOrder(deliveryOrders);

        // 查询订单状态
        Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(orderId, false);
        if (orderInfoResult.isFail() || Objects.isNull(orderInfoResult.getInfo())) {
            throw new DeliveryOrderEsCompensateCraneTaskException("compensateSingleOrder, query orderInfo failed");
        }
        Integer orderStatus = orderInfoResult.getInfo().getOrderStatus();

        // 聚合并写入ES，如果存在当前索引则更新
        aggAndUpdateCompletedDeliveryOrder(deliveryOrders, availableDeliveryOrder, deliveryChannelMap, orderStatus);
    }

    private void aggAndUpdateCompletedDeliveryOrder(List<DeliveryOrder> deliveryOrders, DeliveryOrder availableDeliveryOrder, Map<Integer, DeliveryChannel> deliveryChannelMap, Integer orderStatus) {
        DeliveryOrderEsPo deliveryOrderEsPo = new DeliveryOrderEsPo();
        DeliveryOrder earliestCreateTimeDeliveryOrder = getEarliestCreateTimeDeliveryOrder(deliveryOrders);

        fillDeliveryOrderDoc4Base(deliveryOrderEsPo, earliestCreateTimeDeliveryOrder);
        fillDeliveryOrderDoc4Available(deliveryOrderEsPo, availableDeliveryOrder, deliveryChannelMap, orderStatus);
        fillDeliveryOrderDoc4Trace(deliveryOrderEsPo, deliveryOrders, deliveryChannelMap);

        deliveryOrderEsPatternDao.save(deliveryOrderEsPo);
    }

    /**
     * 找出最近、有效的运单
     * 优先找出激活的运单，如果没有激活的，则按照id取最大的一个
     */
    private DeliveryOrder filterAvailableDeliveryOrder(List<DeliveryOrder> deliveryOrders) {

//        deliveryOrders.sort(Comparator.comparingLong(deliveryOrder -> {
//            if (deliveryOrder.isActive()) {
//                return Long.MIN_VALUE;
//            } else {
//                return -deliveryOrder.getId();
//            }
//        }));
        return DeliveryOrder.filterActiveDeliveryOrder(deliveryOrders);
    }

    private void fillDeliveryOrderDoc4Base(DeliveryOrderEsPo deliveryOrderEsPo, DeliveryOrder deliveryOrder) {
        deliveryOrderEsPo.setId(String.valueOf(deliveryOrder.getOrderId()));
        deliveryOrderEsPo.setIndexKey(deliveryOrder.getCreateTime());
        deliveryOrderEsPo.setRoutingValue(String.valueOf(deliveryOrder.getStoreId()));
    }

    private DeliveryOrder getEarliestCreateTimeDeliveryOrder(List<DeliveryOrder> deliveryOrders) {
        return deliveryOrders.stream().min(Comparator.comparing(DeliveryOrder::getCreateTime)).orElseThrow(() -> new DeliveryOrderEsCompensateCraneTaskException("getEarliestCreateTimeDeliveryOrder, deliveryOrder is null"));
    }

    private void fillDeliveryOrderDoc4Available(DeliveryOrderEsPo deliveryOrderEsPo, DeliveryOrder deliveryOrder,
                                                Map<Integer, DeliveryChannel> deliveryChannelMap, Integer orderStatus) {
        deliveryOrderEsPo.setDeliveryOrderId(deliveryOrder.getId());
        deliveryOrderEsPo.setTenantId(deliveryOrder.getTenantId());
        deliveryOrderEsPo.setStoreId(deliveryOrder.getStoreId());
        deliveryOrderEsPo.setOrderId(deliveryOrder.getOrderId());
        deliveryOrderEsPo.setChannelOrderId(deliveryOrder.getChannelOrderId());
        deliveryOrderEsPo.setOrderBizType(deliveryOrder.getOrderBizType());
        deliveryOrderEsPo.setDaySeq(deliveryOrder.getDaySeq());
        deliveryOrderEsPo.setReserved(deliveryOrder.getReserved() ? NumberUtils.BYTE_ONE : NumberUtils.BYTE_ZERO);
        deliveryOrderEsPo.setOrderSource(deliveryOrder.getOrderSource());
        deliveryOrderEsPo.setReceiverName(Objects.nonNull(deliveryOrder.getReceiver()) ? deliveryOrder.getReceiver().getReceiverName() : StringUtils.EMPTY);
        deliveryOrderEsPo.setReceiverPhone(Objects.nonNull(deliveryOrder.getReceiver()) ? deliveryOrder.getReceiver().getReceiverPhone() : StringUtils.EMPTY);
        deliveryOrderEsPo.setReceiverPrivacyPhone(Objects.nonNull(deliveryOrder.getReceiver()) ? deliveryOrder.getReceiver().getReceiverPrivacyPhone() : StringUtils.EMPTY);
        deliveryOrderEsPo.setReceiverAddress(Objects.nonNull(deliveryOrder.getReceiver()) && Objects.nonNull(deliveryOrder.getReceiver().getReceiverAddress()) ? deliveryOrder.getReceiver().getReceiverAddress().getAddressDetail() : StringUtils.EMPTY);
        deliveryOrderEsPo.setEstimatedDeliveryTime(deliveryOrder.getEstimatedDeliveryTime());
        deliveryOrderEsPo.setEstimatedDeliveryEndTime(deliveryOrder.getEstimatedDeliveryEndTime());
        deliveryOrderEsPo.setDeliveryChannelCode(deliveryOrder.getDeliveryChannel());
        deliveryOrderEsPo.setDeliveryChannelName(Objects.nonNull(deliveryChannelMap.get(deliveryOrder.getDeliveryChannel())) ? deliveryChannelMap.get(deliveryOrder.getDeliveryChannel()).getCarrierName() : StringUtils.EMPTY);
        deliveryOrderEsPo.setDeliveryPlatformCode(Objects.nonNull(deliveryChannelMap.get(deliveryOrder.getDeliveryChannel())) ? deliveryChannelMap.get(deliveryOrder.getDeliveryChannel()).getDeliveryPlatFormCode() : NumberUtils.INTEGER_ZERO);
        deliveryOrderEsPo.setChannelDeliveryId(deliveryOrder.getChannelDeliveryId());
        deliveryOrderEsPo.setDeliveryStatus(deliveryOrder.getStatus().getCode());
        deliveryOrderEsPo.setDeliveryExceptionType(deliveryOrder.getExceptionType().getCode());
        deliveryOrderEsPo.setDeliveryExceptionDescription(deliveryOrder.getExceptionDescription());
        deliveryOrderEsPo.setRiderName(Objects.nonNull(deliveryOrder.getRiderInfo()) ? deliveryOrder.getRiderInfo().getRiderName() : StringUtils.EMPTY);
        deliveryOrderEsPo.setRiderPhone(Objects.nonNull(deliveryOrder.getRiderInfo()) ? deliveryOrder.getRiderInfo().getRiderPhone() : StringUtils.EMPTY);
        deliveryOrderEsPo.setActiveStatus(deliveryOrder.getActiveStatus());
        deliveryOrderEsPo.setCreateTime(deliveryOrder.getCreateTime());
        deliveryOrderEsPo.setUpdateTime(deliveryOrder.getUpdateTime());
        deliveryOrderEsPo.setLastEventTime(deliveryOrder.getLastEventTime());
        deliveryOrderEsPo.setDeliveryDoneTime(deliveryOrder.getDeliveryDoneTime());
        deliveryOrderEsPo.setDeliveryFee(MoneyUtil.fromYuanToCenter(deliveryOrder.getDeliveryFee()).intValue());
        deliveryOrderEsPo.setDistance(deliveryOrder.getDistance());
        deliveryOrderEsPo.setCancelMark(deliveryOrder.getCancelMark());
        deliveryOrderEsPo.setDeliveryExceptionCode(deliveryOrder.getDeliveryExceptionCode());
        deliveryOrderEsPo.setTipAmount(MoneyUtil.fromYuanToCenter(deliveryOrder.getTipAmount()).intValue());
        deliveryOrderEsPo.setDeliveryCount(deliveryOrder.getDeliveryCount());
        deliveryOrderEsPo.setPlatformFee(MoneyUtil.fromYuanToCenter(deliveryOrder.getPlatformFee()).intValue());
        deliveryOrderEsPo.setOrderStatus(orderStatus);
        deliveryOrderEsPo.setAllowLatestAuditTime(deliveryOrder.getDealDeadline());
        deliveryOrderEsPo.setOriginWaybillNo(deliveryOrder.getOriginWaybillNo());
    }

    private void fillDeliveryOrderDoc4Trace(DeliveryOrderEsPo deliveryOrderEsPo, List<DeliveryOrder> deliveryOrders, Map<Integer, DeliveryChannel> deliveryChannelMap) {
        List<DeliveryOrderEsPo.DeliveryOrderSubPo> deliveryOrderSubPoList = deliveryOrders.stream()
                .map(deliveryOrder -> buildDeliveryOrderSubPo(deliveryOrder, deliveryChannelMap)).collect(Collectors.toList());
        deliveryOrderEsPo.setDeliveryTraceList(JsonUtil.toJson(deliveryOrderSubPoList));
    }

    private DeliveryOrderEsPo.DeliveryOrderSubPo buildDeliveryOrderSubPo(DeliveryOrder deliveryOrder, Map<Integer, DeliveryChannel> deliveryChannelMap) {
        DeliveryOrderEsPo.DeliveryOrderSubPo deliveryOrderSubPo = new DeliveryOrderEsPo.DeliveryOrderSubPo();

        deliveryOrderSubPo.setDeliveryOrderId(deliveryOrder.getId());
        deliveryOrderSubPo.setDeliveryChannelCode(deliveryOrder.getDeliveryChannel());
        deliveryOrderSubPo.setDeliveryChannelName(Objects.nonNull(deliveryChannelMap.get(deliveryOrder.getDeliveryChannel())) ? deliveryChannelMap.get(deliveryOrder.getDeliveryChannel()).getCarrierName() : StringUtils.EMPTY);
        deliveryOrderSubPo.setDeliveryPlatformCode(Objects.nonNull(deliveryChannelMap.get(deliveryOrder.getDeliveryChannel())) ? deliveryChannelMap.get(deliveryOrder.getDeliveryChannel()).getDeliveryPlatFormCode() : NumberUtils.INTEGER_ZERO);
        deliveryOrderSubPo.setChannelDeliveryId(deliveryOrder.getChannelDeliveryId());
        deliveryOrderSubPo.setDeliveryStatus(deliveryOrder.getStatus().getCode());
        deliveryOrderSubPo.setDeliveryExceptionType(deliveryOrder.getExceptionType().getCode());
        deliveryOrderSubPo.setDeliveryExceptionDescription(deliveryOrder.getExceptionDescription());
        deliveryOrderSubPo.setRiderName(Objects.nonNull(deliveryOrder.getRiderInfo()) ? deliveryOrder.getRiderInfo().getRiderName() : StringUtils.EMPTY);
        deliveryOrderSubPo.setRiderPhone(Objects.nonNull(deliveryOrder.getRiderInfo()) ? deliveryOrder.getRiderInfo().getRiderPhone() : StringUtils.EMPTY);
        deliveryOrderSubPo.setActiveStatus(deliveryOrder.getActiveStatus());
        deliveryOrderSubPo.setCreateTime(deliveryOrder.getCreateTime());
        deliveryOrderSubPo.setUpdateTime(deliveryOrder.getUpdateTime());
        deliveryOrderSubPo.setLastEventTime(deliveryOrder.getLastEventTime());
        deliveryOrderSubPo.setDeliveryDoneTime(deliveryOrder.getDeliveryDoneTime());
        deliveryOrderSubPo.setDeliveryFee(MoneyUtil.fromYuanToCenter(deliveryOrder.getDeliveryFee()).intValue());
        deliveryOrderSubPo.setDistance(deliveryOrder.getDistance());
        deliveryOrderSubPo.setCancelMark(deliveryOrder.getCancelMark());
        deliveryOrderSubPo.setDeliveryExceptionCode(deliveryOrder.getDeliveryExceptionCode());
        deliveryOrderSubPo.setTipAmount(MoneyUtil.fromYuanToCenter(deliveryOrder.getTipAmount()).intValue());
        deliveryOrderSubPo.setDeliveryCount(deliveryOrder.getDeliveryCount());
        deliveryOrderSubPo.setPlatformFee(MoneyUtil.fromYuanToCenter(deliveryOrder.getPlatformFee()).intValue());
        deliveryOrderSubPo.setAllowLatestAuditTime(deliveryOrder.getDealDeadline());
        deliveryOrderSubPo.setOriginWaybillNo(deliveryOrder.getOriginWaybillNo());

        return deliveryOrderSubPo;
    }

}
