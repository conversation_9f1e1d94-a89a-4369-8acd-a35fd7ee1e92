package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.RiderInfoDeliveryRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Repository
public class SquirrelRiderInfoDeliveryRepository implements RiderInfoDeliveryRepository {

    @Resource
    private CommonSquirrelRiderInfoDeliveryRepository riderInfoDeliveryRepository;

    @Override
    public Optional<Rider> queryRiderInfo(Long orderId) {
        return riderInfoDeliveryRepository.queryRiderInfo(orderId);
    }

    @Override
    public boolean saveRiderInfo(Long orderId, Rider rider) {
        return riderInfoDeliveryRepository.saveRiderInfo(orderId, rider);
    }

}
