package com.sankuai.meituan.shangou.empower.tms.delivery.enums;

import com.meituan.shangou.saas.order.platform.enums.ChangedFieldEnum;

import java.util.Objects;
import java.util.Optional;

/**
 * 订单修改字段枚举
 * <AUTHOR>
 */

public enum OrderChangeFieldEnum {

    ESTIMATED_DELIVERY_TIME(1, "预计送达时间"),

    COMMENT(2, "订单备注"),

    RECEIVER_ADDRESS(3, "收件人地址"),

    ADDRESS_CHANGE_FEE(4, "正单地址变更费"),

    REFUND_ADDRESS_CHANGE_FEE_NOTES(5, "退单地址变更费"),
    ;

    private final int value;

    private final String desc;

    OrderChangeFieldEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static Optional<OrderChangeFieldEnum> map2OrderChangeFieldEnum(ChangedFieldEnum changedFieldEnum) {
        if (Objects.isNull(changedFieldEnum)) {
            return Optional.empty();
        }

        switch (changedFieldEnum) {
            case DELIVERY_TIME:
            case DELIVERY_END_TIME:
                return Optional.of(ESTIMATED_DELIVERY_TIME);
            case COMMENT:
                return Optional.of(COMMENT);
            case DELIVERY_ADDRESS:
                return Optional.of(RECEIVER_ADDRESS);
            case ADDRESS_CHANGE_FEE:
                return Optional.of(ADDRESS_CHANGE_FEE);
            case ADDRESS_CHANGE_FEE_NOTES:
                return Optional.of(REFUND_ADDRESS_CHANGE_FEE_NOTES);
            default:
                return Optional.empty();
        }
    }

    public int getValue() {
        return value;
    }

}
