package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.utils.ChannelTypeConvertUtils;
import com.sankuai.meituan.shangou.empower.rider.client.message.RiderChangeTransStockMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.SharkPushOperateTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.SharkPushSendSceneEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.saas.message.request.push.FusionPushSendRequest;
import com.sankuai.meituan.shangou.saas.message.service.PushThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-02-12
 * @email <EMAIL>
 */
@Slf4j
@Component
public class RiderChangeTransMaterialStockConsumer extends AbstractDeadLetterConsumer {

    @Resource
    private PushThriftService pushThriftService;
    @Resource
    private OrderSystemClient orderSystemClient;

    private static final String QNH_APP_ID = "5";

    private static final long RIDER_CHANGE_TRANS_PUSH_EVENT_CONFIG_ID = 1127L;

    private static final List<SharkPushSendSceneEnum> SHARK_PUSH_SEND_SCENE_ENUM_LIST = Lists.newArrayList(SharkPushSendSceneEnum.TRANSFER_IN_FACAI, SharkPushSendSceneEnum.TRANSFER_OUT_FACAI);

    @Override
    protected ConsumeStatus consume(MafkaMessage message) {

        log.info("RiderChangeTransMaterialStockConsumer.consume, message = {}", message);
        RiderChangeTransStockMsg riderChangeTransStockMsg = translateMessage(message, RiderChangeTransStockMsg.class);
        if (Objects.isNull(riderChangeTransStockMsg)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(riderChangeTransStockMsg.getTenantId(), riderChangeTransStockMsg.getStoreId(), riderChangeTransStockMsg.getChannelOrderId(), ChannelTypeConvertUtils.convert(riderChangeTransStockMsg.getOrderBizType()), false);
        if (orderInfoResult.isFail()) {
            throw new SystemException("查询订单失败");
        }
        OrderInfo orderInfo = orderInfoResult.getInfo();

        //透传json
        JSONObject jsonObject = buildPassThroughParam(riderChangeTransStockMsg, orderInfo);

        Map<String, String> msgPropertyMap = Maps.newHashMap();

        for (SharkPushSendSceneEnum sharkPushSendSceneEnum : SHARK_PUSH_SEND_SCENE_ENUM_LIST) {
            jsonObject.put("scene", sharkPushSendSceneEnum.getScene()); //SharkPushSendSceneEnum
            msgPropertyMap.put("passThrough", jsonObject.toJSONString());
            switch (sharkPushSendSceneEnum) {
                case TRANSFER_IN_FACAI:
                    sendFusionPush(Lists.newArrayList(riderChangeTransStockMsg.getAfterRiderAccountId()), riderChangeTransStockMsg, msgPropertyMap);
                    break;
                case TRANSFER_OUT_FACAI:
                    sendFusionPush(Lists.newArrayList(riderChangeTransStockMsg.getBeforeRiderAccountId()), riderChangeTransStockMsg, msgPropertyMap);
                    break;
                default:
                    break;
            }
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private JSONObject buildPassThroughParam(RiderChangeTransStockMsg riderChangeTransStockMsg, OrderInfo orderInfo) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("serverTS", System.currentTimeMillis());
        jsonObject.put("operation", "dhOrderUpdate");
        jsonObject.put("storeId", riderChangeTransStockMsg.getStoreId());
        jsonObject.put("tenantId", riderChangeTransStockMsg.getTenantId());
        jsonObject.put("channelOrderId", riderChangeTransStockMsg.getChannelOrderId());
        jsonObject.put("channelId", ChannelTypeConvertUtils.convert(riderChangeTransStockMsg.getOrderBizType()));
        jsonObject.put("type", SharkPushOperateTypeEnum.UPDATE.getType());

        jsonObject.put("channelName", DynamicOrderBizType.findOf(riderChangeTransStockMsg.getOrderBizType()).getDesc());
        jsonObject.put("serialNo", orderInfo.getDaySeq());
        jsonObject.put("beforeRiderName", riderChangeTransStockMsg.getBeforeRiderName());
        jsonObject.put("afterRiderName", riderChangeTransStockMsg.getAfterRiderName());
        RiderChangeTransStockMsg.TransStockMaterialInfo transStockMaterialInfo = riderChangeTransStockMsg.getTransStockMaterialInfo();
        JSONObject materialInfo = buildMaterialInfo(transStockMaterialInfo);

        jsonObject.put("externalProductList", Lists.newArrayList(materialInfo));
        jsonObject.put("transTime", riderChangeTransStockMsg.getOperateTime());
        return jsonObject;
    }

    private JSONObject buildMaterialInfo(RiderChangeTransStockMsg.TransStockMaterialInfo transStockMaterialInfo) {
        JSONObject materialInfo = new JSONObject();
        materialInfo.put("productName", transStockMaterialInfo.getMaterialSkuName());
        materialInfo.put("count", transStockMaterialInfo.getOperateCount());
        materialInfo.put("picUrl", transStockMaterialInfo.getPicUrl());
        materialInfo.put("specification", transStockMaterialInfo.getSpecification());
        return materialInfo;
    }

    private void sendFusionPush(List<Long> accountIds,RiderChangeTransStockMsg riderChangeTransStockMsg, Map<String, String> msgPropertyMap) {
        try {
            FusionPushSendRequest fusionPushSendRequest = new FusionPushSendRequest();
            fusionPushSendRequest.setTenantId(riderChangeTransStockMsg.getTenantId());
            fusionPushSendRequest.setPoiId(riderChangeTransStockMsg.getStoreId());
            fusionPushSendRequest.setAppCode(QNH_APP_ID);
            fusionPushSendRequest.setAccountIds(accountIds);
            fusionPushSendRequest.setEventConfigId(RIDER_CHANGE_TRANS_PUSH_EVENT_CONFIG_ID);
            fusionPushSendRequest.setMsgPropertyMap(msgPropertyMap);
            pushThriftService.sendFusionPush(fusionPushSendRequest);
        } catch (TException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.RIDER_CHANGE_TRANS_MATERIAL_STOCK_CONSUMER;
    }
}
