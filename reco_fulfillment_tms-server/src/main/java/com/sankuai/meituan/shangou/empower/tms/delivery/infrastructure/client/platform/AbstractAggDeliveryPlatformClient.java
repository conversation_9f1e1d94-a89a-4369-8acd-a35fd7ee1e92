package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.ChangedFieldEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.push.PushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.AggDeliveryLaunchHandleFailedEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryWarnEventPublisher;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.wrapper.AggDeliveryWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.constants.DeliveryOrderConstants;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.OrderChangeFieldEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryPlatformException;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryChangeCallbackInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryUnifiedCallbackMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.wrapper.OrderThriftServiceWrapper;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.ChannelStoreQueryResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantChannelStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.LionConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.map.MapClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.ConvertUtil;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.DeliveryResultCodeEnum.*;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum.SYNC_CANCEL_FAIL;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum.LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.PLATFORM_CREATE_SHOP_EXCEPTION;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.TENANT_POI_DELIVERY_POI_LATITUDE_LONGITUDE_EMPTY;

@Slf4j
public abstract class AbstractAggDeliveryPlatformClient extends AbstractDeliveryPlatformClient {


    public static final String DELIVERY = "delivery";
    public static final String DELIVERY_ORDER = "delivery.order";
    public static final String DELIVERY_LAUNCH = "delivery.launch";
    public static final String AGG_DELIVERY_ORDER_CHANGE_NOTIFY = "agg.delivery.order.change.notify";
    @Autowired
    protected TenantSystemClient tenantSystemClient;
    @Autowired
    protected DeliveryWarnEventPublisher warnEventPublisher;
    @Autowired
    protected DeliveryOrderRepository deliveryOrderRepository;
    @Autowired
    protected PushClient pushClient;
    @Autowired
    protected MafkaMessageProducer<DeliveryUnifiedCallbackMessage> deliveryUnifiedCallbackMessageProducer;
    @Qualifier("mapClientImpl")
    @Autowired
    protected MapClient mapClient;
    @Autowired
    protected DeliveryPoiRepository deliveryPoiRepository;

    @Autowired
    private DeliveryChannelApplicationService deliveryChannelApplicationService;
    @Autowired
    private TenantRemoteService tenantRemoteService;
    @Autowired
    private OrderThriftServiceWrapper orderThriftServiceWrapper;
    @Autowired
    protected AggDeliveryWrapper aggDeliveryWrapper;

    protected abstract int getCancelReasonCode();

    protected abstract IChannelAggDeliveryPlatform getChannelAggDeliveryThriftService();

    protected abstract Optional<Failure> doCancelDelivery(DeliveryOrder deliveryOrder);

    protected abstract Optional<Failure> doCancelDeliveryForTransOrder(DeliveryOrder deliveryOrder);

    @Override
    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public Optional<LaunchFailure> launch(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder,Integer transferOrderMark) {
        Optional<LaunchFailure> launchFailure = doLaunch(deliveryPoi, orderInfo,deliveryOrder,transferOrderMark);

        //发起成功，推进到发起成功状态并记录流水
        if (!launchFailure.isPresent()) {
            DeliveryChangeCallbackInfo callbackInfo = new DeliveryChangeCallbackInfo(
                    deliveryOrder.getOrderId(),
                    deliveryOrder.getDeliveryChannel(),
                    deliveryOrder.getChannelDeliveryId(),
                    deliveryOrder.getChannelServicePackageCode(),
                    DeliveryEventEnum.LAUNCH_DELIVERY,
                    DeliveryExceptionInfo.NO_EXCEPTION,
                    LocalDateTime.now(),
                    null,
                    null,
                    null
            );
            callbackInfo.setPlatformSource(deliveryOrder.getPlatformSourceEnum()!=null ? deliveryOrder.getPlatformSourceEnum().getCode():PlatformSourceEnum.OMS.getCode());
            DeliveryUnifiedCallbackMessage message = new DeliveryUnifiedCallbackMessage(
                    DeliveryCallbackTypeEnum.CHANGE_CALLBACK,callbackInfo
                    );
            deliveryUnifiedCallbackMessageProducer.sendMessage(
                    message,
                    deliveryOrder.getOrderId()
            );
        }

        return launchFailure;
    }

    private Optional<LaunchFailure> doLaunch(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder,Integer transferOrderMark) {
        try {

            //隐私商品处理
            privacyGoodsHandle(deliveryPoi, orderInfo);

            Result<CreateAggDeliveryRequest> requestBuildResult;
            if (LionConfigUtils.isGloryStore(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId())) {
                requestBuildResult = buildCreateAggDeliveryRequestForGloryOrder(deliveryPoi, orderInfo,deliveryOrder);
            } else {
                requestBuildResult = buildCreateAggDeliveryRequestForEmpowerOrder(orderInfo,deliveryOrder,transferOrderMark);
            }

            if (requestBuildResult.isFail()) {
                Cat.logEvent(DELIVERY, DELIVERY_LAUNCH,"1","");
                return Optional.of(new LaunchFailure(requestBuildResult.getFailure(), SYNC_CREATE_FAIL));
            }
            CreateAggDeliveryRequest request = requestBuildResult.getInfo();
            if (MccConfigUtils.getPaotuiLockSwitch()
                    && MccConfigUtils.isPaotuiLockOrderV2GrayTenant(orderInfo.getOrderKey().getTenantId())
                    && MccConfigUtils.isPaotuiLockAggDeliveryPlatform(getDeliveryPlatform().getCode())) {
                boolean orderLockLabel = false;
                if(orderInfo.getOrderLockLabel()!=null){
                    orderLockLabel = orderInfo.getOrderLockLabel();
                }
                boolean isLocked = false;
                if(orderInfo.getIsLocked()!=null){
                    isLocked = orderInfo.getIsLocked();
                }
                int deliveryAvailable = orderLockLabel && isLocked ? DeliveryAvailableEnum.NO.getValue() : DeliveryAvailableEnum.YES.getValue();
                log.info("跑腿锁单2.0订单锁定: orderId:{}, deliveryAvailable:{}", deliveryOrder.getOrderId(), deliveryAvailable);
                request.setDeliveryAvailable(deliveryAvailable);
                request.setPaotuiDelivery(orderLockLabel ? 1:0);
            }

            log.info("ChannelAggDeliveryThriftService.createAggDelivery begin, request={}", request);
            CreateAggDeliveryResponse response = getChannelAggDeliveryThriftService().createDelivery(request);
            log.info("ChannelAggDeliveryThriftService.createAggDelivery finish, response={}", response);
            if (response == null || response.getCode() == null) {
                warnEventPublisher.postEvent(new AggDeliveryLaunchHandleFailedEvent(getDeliveryPlatform(), orderInfo.getOrderKey(),
                        "发起运单失败"));
                Cat.logEvent("LAUNCH_CANCEL_" + getDeliveryPlatform().getCode(), "CALL_CHANNEL_ERROR");
                Cat.logEvent(DELIVERY, DELIVERY_LAUNCH,"1","");
                return Optional.of(new LaunchFailure(true, SYNC_CREATE_FAIL, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "调用渠道聚合运力网关接口失败"));
            }

            if (response.getCode() == SUCCESS.getCode()) {
                Cat.logEvent(DELIVERY, DELIVERY_LAUNCH);
                return Optional.empty();
            }

            if(response.getCode() == MALT_FALLBACK_ERROR.getCode() || response.getCode() == DAP_FALLBACK_ERROR.getCode()){
                Cat.logEvent("LAUNCH_CANCEL_" + getDeliveryPlatform().getCode(), "RESPONSE_ERROR");
                warnEventPublisher.postEvent(new AggDeliveryLaunchHandleFailedEvent(getDeliveryPlatform(), orderInfo.getOrderKey(), response.getMsg()));
                Cat.logEvent(DELIVERY, DELIVERY_LAUNCH,"1","");
                return Optional.of(new LaunchFailure(false, SYNC_CREATE_FAIL, FailureCodeEnum.LAUNCH_DELIVERY_FAILED, response.getMsg()));
            }

            Cat.logEvent("LAUNCH_CANCEL_" + getDeliveryPlatform().getCode(), "RESPONSE_ERROR");
            warnEventPublisher.postEvent(new AggDeliveryLaunchHandleFailedEvent(getDeliveryPlatform(), orderInfo.getOrderKey(), response.getMsg()));
            Cat.logEvent(DELIVERY, DELIVERY_LAUNCH,"1","");
            return Optional.of(new LaunchFailure(true, SYNC_CREATE_FAIL, FailureCodeEnum.LAUNCH_DELIVERY_FAILED, response.getMsg()));
        } catch (Exception e) {
            log.error("发起配送失败", e);
            Cat.logEvent(DELIVERY, DELIVERY_LAUNCH,"1","");
            Cat.logEvent("LAUNCH_CANCEL_" + getDeliveryPlatform().getCode(), "UNKNOWN_ERROR");

            warnEventPublisher.postEvent(new AggDeliveryLaunchHandleFailedEvent(getDeliveryPlatform(), orderInfo.getOrderKey(), "发起运单失败"));
            return Optional.of(new LaunchFailure(true, SYNC_CREATE_FAIL, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "调用渠道网关接口失败"));
        }
    }

    /**
     * 隐私商品处理
     */
    private static void privacyGoodsHandle(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
        //是否开启聚合配送 发配送时 隐私商品的处理
        if (!MccConfigUtils.checkStoreOpenAggPrivacyDelivery(deliveryPoi.getStoreId())) {
            return;
        }

        //当前只处理麦芽田/青云
        if (!Objects.equals(deliveryPoi.getDeliveryPlatform(), DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM)
                && !Objects.equals(deliveryPoi.getDeliveryPlatform(), DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM)) {
            return;
        }

        //是否隐私商品
        if (!Objects.equals(orderInfo.getPrivacyGoods(), true)) {
            return;
        }

        //计数器
        AtomicInteger goodsCount = new AtomicInteger(1);
        //将第一行商品的名称替换为【为保护隐私，商品已隐藏】，其余商品名称、商品规格替换为【***】展示。
        Optional.ofNullable(orderInfo.getGoodsList())
                .orElse(Collections.emptyList())
                .stream()
                .filter(goodsInfo -> !goodsInfo.isHasReplaceGoodsCode())
                .forEach(goodsInfo -> {
                    if (Objects.equals(goodsCount.getAndIncrement(), 1)) {
                        //第一行商品 【为保护隐私，商品已隐藏】
                        goodsInfo.setName(DeliveryOrderConstants.PRIVACY_GOODS_SHOW_NAME);
                    } else {
                        //其他行商品 【***】
                        goodsInfo.setName(DeliveryOrderConstants.PRIVACY_GOODS_SHOW_STAR_NAME);
                    }
                });
    }

    private Result<CreateAggDeliveryRequest> buildCreateAggDeliveryRequestForGloryOrder(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder) {
        TenantStoreInfo tenantStoreInfo = tenantSystemClient.queryStoreDetailInfo(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId());
        if (tenantStoreInfo == null) {
            Cat.logEvent("LAUNCH_CANCEL_" + getDeliveryPlatform().getCode(), "STORE_ERROR");
            return new Result<>(new Failure(true, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "查询门店信息失败"));
        }

        Result<Address> getStoreAddressResult = getStoreAddress(deliveryPoi, tenantStoreInfo);
        if (getStoreAddressResult.isFail()) {
            return new Result<>(getStoreAddressResult.getFailure());
        }

        CreateAggDeliveryRequest request = buildBaseAggCreateDeliveryRequest(orderInfo);
        request.setShopName(tenantStoreInfo.getStoreName());
        request.setCityCode(String.valueOf(tenantStoreInfo.getCityCode()));
        request.setDistrictCode(String.valueOf(tenantStoreInfo.getAreaCode()));
        // 设置请求的发送者电话，根据租户和门店信息获取门店的电话号码。
        request.setSenderPhone(getPhoneByTenantStoreInfo(orderInfo.getOrderKey().getTenantId(), tenantStoreInfo));
        if (LionConfigUtils.needSendStoreCoordinateAddressWhenLaunching()) {
            request.setSenderLatitude(getStoreAddressResult.getInfo().getCoordinatePoint().getLatitude());
            request.setSenderLongitude(getStoreAddressResult.getInfo().getCoordinatePoint().getLongitude());
        } else {
            request.setSenderLatitude(StringUtils.EMPTY);
            request.setSenderLongitude(StringUtils.EMPTY);
        }
        request.setSenderAddress(tenantStoreInfo.getAddress());
        request.setDeliveryOrderId(deliveryOrder.getDeliveryOrderId());
        return new Result<>(request);
    }

    private Result<CreateAggDeliveryRequest> buildCreateAggDeliveryRequestForEmpowerOrder(OrderInfo orderInfo, DeliveryOrder deliveryOrder,Integer transferOrderMark) {
        DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
        Optional<TenantChannelStoreInfo> channelStoreInfo = Optional.empty();
        channelStoreInfo = tenantSystemClient.queryChannelStoreDetailInfoWithAnyChannel(deliveryOrder.getOrderKey().getTenantId(), deliveryOrder.getOrderKey().getStoreId(),channelType.getChannelId());



        if (channelType == null || !channelStoreInfo.isPresent() || !orderInfo.isSelfDelivery()) {
            Cat.logEvent("LAUNCH_CANCEL_" + getDeliveryPlatform().getCode(), "CHANNEL_STORE_ERROR");
            return new Result<>(new Failure(false, FailureCodeEnum.LAUNCH_DELIVERY_FAILED, "订单不是自配送"));
        }


        CreateAggDeliveryRequest request = buildBaseAggCreateDeliveryRequest(orderInfo);
        if(deliveryOrder.getPlatformSourceEnum() == PlatformSourceEnum.OFC){
            request.setShopId(deliveryOrder.getOrderKey().getStoreId());
        }
        request.setShopName(channelStoreInfo.get().getStoreName());
        request.setCityCode(String.valueOf(channelStoreInfo.get().getCityCode()));
        request.setDistrictCode(String.valueOf(channelStoreInfo.get().getAreaCode()));
        // 设置请求的发送者电话，根据租户和门店信息获取门店的电话号码
        request.setSenderPhone(getPhoneByTenantChannelStoreInfo(orderInfo.getOrderKey().getTenantId(), channelStoreInfo.get()));
        request.setSenderLatitude(channelStoreInfo.get().getLatitude());
        request.setSenderLongitude(channelStoreInfo.get().getLongitude());
        request.setSenderAddress(channelStoreInfo.get().getAddress());
        request.setDeliveryOrderId(deliveryOrder.getDeliveryOrderId());
        request.setTransferOrderMark(transferOrderMark);
        String channelPoiId = tenantSystemClient.queryChannelPoiIdByPoiId(orderInfo.getOrderKey().getTenantId(), orderInfo.getPoiId(), channelType.getChannelId());
        request.setChannelStoreId(channelPoiId);
        return new Result<>(request);
    }

    /**
     * 获取门店电话 如果是无人仓的租户&门店电话不为空 用门店电话作为联系电话
     *
     * @param tenantId        租户ID
     * @param tenantStoreInfo 获取信息对象
     * @return
     */
    private String getPhoneByTenantStoreInfo(Long tenantId, TenantStoreInfo tenantStoreInfo) {
        String phone = tenantStoreInfo.getPhone();
        // (开启全量|无人仓的租户)&门店电话不为空 用门店电话作为联系电话
        if ((MccConfigUtils.openServicePhoneSwitch()
                || tenantRemoteService.isMedicineUnmannedWarehouse(tenantId))
            && StringUtils.isNotBlank(tenantStoreInfo.getServicePhone())) {
            // 经确认 麦芽田支持中划线-或者下划线_的固定电话号码，所以这里不做特殊处理
            phone = tenantStoreInfo.getServicePhone();
        }
        return phone;
    }

    /**
     * 获取门店电话 如果是无人仓的租户&门店电话不为空 用门店电话作为联系电话
     *
     * @param tenantId         租户ID
     * @param channelStoreInfo 获取信息对象
     * @return
     */
    private String getPhoneByTenantChannelStoreInfo(Long tenantId, TenantChannelStoreInfo channelStoreInfo){
        String phone = channelStoreInfo.getPhone();
        // 如果是无人仓的租户&门店电话不为空 用门店电话作为联系电话
        if ((MccConfigUtils.openServicePhoneSwitch()
                || tenantRemoteService.isMedicineUnmannedWarehouse(tenantId))
            && StringUtils.isNotBlank(channelStoreInfo.getServicePhone())) {
            // 经确认 麦芽田支持中划线-或者下划线_的固定电话号码，所以这里不做特殊处理
            phone = channelStoreInfo.getServicePhone();
        }
        return phone;
    }

    @Override
    @CatTransaction
    // @MethodLog(logRequest = false, logResponse = true)
    public Optional<Failure> cancelDelivery(DeliveryOrder deliveryOrder) {
        log.info("cancelDelivery deliveryOrderPrimaryId:{}", deliveryOrder.getId());
        Optional<Failure> failure = doCancelDelivery(deliveryOrder);

        if (failure.isPresent()) {
            deliveryOrder.onExceptionWithChangeTime(
                    new DeliveryExceptionInfo(LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION, failure.get().getFailureMessage(), SYNC_CANCEL_FAIL.getCode()),
                    LocalDateTime.now()
            );
            deliveryOrderRepository.save(deliveryOrder);
            pushClient.pushDeliveryException(deliveryOrder);
        }

        return failure;
    }

    @Override
    @CatTransaction
    //  @MethodLog(logRequest = false, logResponse = true)
    public Optional<Failure> cancelDeliveryForTransOrder(DeliveryOrder deliveryOrder) {
        log.info("cancelDeliveryForTransOrder deliveryOrderPrimaryId:{}", deliveryOrder.getId());
        Optional<Failure> failure = doCancelDeliveryForTransOrder(deliveryOrder);

        if (failure.isPresent()) {
            deliveryOrder.onExceptionWithChangeTime(
                    new DeliveryExceptionInfo(LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION, failure.get().getFailureMessage(), SYNC_CANCEL_FAIL.getCode()),
                    LocalDateTime.now()
            );
            deliveryOrderRepository.save(deliveryOrder);
            pushClient.pushDeliveryException(deliveryOrder);
        }

        return failure;
    }

    @Override
    @CatTransaction
    // @MethodLog(logRequest = false, logResponse = true)
    public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryChannel deliveryChannelDto, DeliveryPoi deliveryPoi) {
        try {
            QueryAggDeliveryRiderInfoRequest thriftRequest = new QueryAggDeliveryRiderInfoRequest(
                    deliveryOrder.getOrderId(), getDeliveryPlatform().getCode(), getAppInfo(),deliveryOrder.getDeliveryOrderId()
            );

            log.info("ChannelAggDeliveryThriftService.queryRiderLocation begin, request={}", thriftRequest);
            QueryAggDeliveryRiderInfoResponse response = getChannelAggDeliveryThriftService().queryRiderLocation(thriftRequest);
            log.info("ChannelAggDeliveryThriftService.queryRiderLocation finish, result={}", response);
            if (response == null || !Objects.equals(response.getCode(), BigInteger.ZERO.intValue())) {
                return Optional.empty();
            }
            // 校验骑手所属配送渠道与当前运单的配送渠道一致
            String channelMark = response.getLogistic();
            if (StringUtils.isNotBlank(channelMark)) {
                DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByLogisticMark(deliveryChannelDto.getDeliveryPlatFormCode(), deliveryChannelDto.getOrderChannelCode(), channelMark);
                // 判断查询到的渠道，需要跟当前运单的渠道一致
                if (Objects.nonNull(deliveryChannel) && !Objects.equals(deliveryChannel.getCarrierCode(), deliveryOrder.getDeliveryChannel())) {
                    log.info("deliveryChannelDto: {}", deliveryChannelDto);
                    log.warn("delivery order channel is:{}, but rider channel:[{}] is different",
                            deliveryOrder.getDeliveryChannel(), deliveryChannel.getCarrierCode());
                    return Optional.empty();
                }
            }
            return Optional.of(CoordinateUtil.translateToCoordinatePoint(response.getLongitude(), response.getLatitude()));
        } catch (Exception e) {
            log.error("查询骑手位置失败", e);
            return Optional.empty();
        }
    }

    @Override
    @CatTransaction
    // @MethodLog(logRequest = false, logResponse = true)
    public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryPoi deliveryPoi) {
        try {
            QueryAggDeliveryRiderInfoRequest thriftRequest = new QueryAggDeliveryRiderInfoRequest(
                    deliveryOrder.getOrderId(), getDeliveryPlatform().getCode(), getAppInfo(),deliveryOrder.getDeliveryOrderId()
            );

            log.info("ChannelAggDeliveryThriftService.queryRiderLocation begin, request={}", thriftRequest);
            QueryAggDeliveryRiderInfoResponse response = getChannelAggDeliveryThriftService().queryRiderLocation(thriftRequest);
            log.info("ChannelAggDeliveryThriftService.queryRiderLocation finish, result={}", response);
            if (response == null || !Objects.equals(response.getCode(), BigInteger.ZERO.intValue())) {
                return Optional.empty();
            }
            // 校验骑手所属配送渠道与当前运单的配送渠道一致
            String channelMark = response.getLogistic();
            if (StringUtils.isNotBlank(channelMark)) {
                DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByLogisticMark(getDeliveryPlatform().getCode(), NumberUtils.INTEGER_ZERO, channelMark);
                // 判断查询到的渠道，需要跟当前运单的渠道一致
                if (Objects.nonNull(deliveryChannel) && !Objects.equals(deliveryChannel.getCarrierCode(), deliveryOrder.getDeliveryChannel())) {
                    log.warn("delivery order channel is:{}, but rider channel:[{}] is different",
                            deliveryOrder.getDeliveryChannel(), deliveryChannel.getCarrierCode());
                    return Optional.empty();
                }
            }
            return Optional.of(CoordinateUtil.translateToCoordinatePoint(response.getLongitude(), response.getLatitude()));
        } catch (Exception e) {
            log.error("查询骑手位置失败", e);
            return Optional.empty();
        }
    }

    @Override
    @CatTransaction
    // @MethodLog(logRequest = false, logResponse = true)
    public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder) {
        try {
            QueryAggDeliveryRiderInfoRequest thriftRequest = new QueryAggDeliveryRiderInfoRequest(
                    deliveryOrder.getOrderId(), getDeliveryPlatform().getCode(), getAppInfo(),deliveryOrder.getDeliveryOrderId()
            );

            log.info("ChannelAggDeliveryThriftService.queryRiderLocation begin, request={}", thriftRequest);
            QueryAggDeliveryRiderInfoResponse response = getChannelAggDeliveryThriftService().queryRiderLocation(thriftRequest);
            log.info("ChannelAggDeliveryThriftService.queryRiderLocation finish, result={}", response);
            if (response == null || !Objects.equals(response.getCode(), BigInteger.ZERO.intValue())) {
                return Optional.empty();
            }

            return Optional.of(CoordinateUtil.translateToCoordinatePoint(response.getLongitude(), response.getLatitude()));
        } catch (Exception e) {
            log.error("查询骑手位置失败", e);
            return Optional.empty();
        }
    }

    @Override
    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    @Degrade(rhinoKey = "MaltFarmPlatformClient-createShop", fallBackMethod = "createShopFallback", timeoutInMilliseconds = 2000)
    public Optional<Long> createShop(DeliveryPoi deliveryPoi) {
        Optional<CreateAggDeliveryShopRequest> request;
        if (LionConfigUtils.isGloryStore(deliveryPoi.getTenantId(), deliveryPoi.getStoreId())) {
            request = buildDeliveryStoreCreateRequestForGloryStore(deliveryPoi);
        } else {
            request = buildDeliveryStoreCreateRequestForEmpowerStore(deliveryPoi);
        }

        if (!request.isPresent()) {
            return Optional.empty();
        }
        //最终判断经纬度为空则抛异常
        if (Objects.isNull(request.get().getShopLatitude()) || Objects.isNull(request.get().getShopLongitude())) {
            throw new DeliveryPlatformException(TENANT_POI_DELIVERY_POI_LATITUDE_LONGITUDE_EMPTY);
        }

        try {
            CreateAggDeliveryShopResponse response = getChannelAggDeliveryThriftService().createAggDeliveryShop(request.get());
            if (response == null || response.getCode() == null || response.getCode() != BigInteger.ZERO.intValue()) {
                log.error("createShop error request:{},deliveryPlatform:{},response:{}", request.get(), deliveryPoi.getDeliveryPlatform(), response);
                Cat.logEvent("CREATE_DELIVERY_SHOP_FAIL", "CREATE_FAILED");
                throw new DeliveryPlatformException(PLATFORM_CREATE_SHOP_EXCEPTION);
            }
            if(response.getAggDeliveryShopId()==null){
                return Optional.empty();
            }
            return Optional.of(response.getAggDeliveryShopId());
        } catch (Exception e) {
            log.error("createShop error request:{},deliveryPlatform:{}", request.get(), deliveryPoi.getDeliveryPlatform(), e);
            throw new DeliveryPlatformException(PLATFORM_CREATE_SHOP_EXCEPTION);
        }
    }

    private Optional<CreateAggDeliveryShopRequest> buildDeliveryStoreCreateRequestForEmpowerStore(DeliveryPoi deliveryPoi) {

        //获取门店信息
        Optional<TenantChannelStoreInfo> channelStore = Optional.empty();
        channelStore = tenantSystemClient.queryChannelStoreDetailInfoWithAnyChannel(deliveryPoi.getTenantId(), deliveryPoi.getStoreId(),DynamicChannelType.MEITUAN.getChannelId());


        if (!channelStore.isPresent()) {
            log.warn("queryChannelStoreDetailInfo mt error tenantId:{},storeId:{},deliveryPlatform:{} ,result:{}",
                    deliveryPoi.getTenantId(), deliveryPoi.getStoreId(), deliveryPoi.getDeliveryPlatform(), channelStore);
            return Optional.empty();
        }

        TenantInfo tenantInfo=tenantSystemClient.queryTenantInfo(deliveryPoi.getTenantId());
        String categoryCode = aggDeliveryWrapper.getCategoryCode(deliveryPoi.getTenantId());
        return Optional.of(CreateAggDeliveryShopRequest
                .builder()
                .shopId(channelStore.get().getShopId())
                .aggDeliveryPlatformId(deliveryPoi.getDeliveryPlatform().getCode())
                .cityCode(channelStore.get().getCityCode() + "")
                .districtCode(channelStore.get().getAreaCode() + "")
                .shopAddress(channelStore.get().getAddress())
                .shopLatitude(Double.parseDouble(channelStore.get().getLatitude()))
                .shopLongitude(Double.parseDouble(channelStore.get().getLongitude()))
                .shopName(channelStore.get().getStoreName())
                // 设置请求的发送者电话，根据租户和门店信息获取门店的电话号码
                .shopPhone(getPhoneByTenantChannelStoreInfo(deliveryPoi.getTenantId(), channelStore.get()))
                .tenantId(channelStore.get().getTenantId())
                .mapType(BigInteger.ONE.intValue())
                .category(BigInteger.ONE.intValue())
                .tenantName(tenantInfo==null ? "":tenantInfo.getTenantName())
                .appInfo(getAppInfo())
                .categoryCode(categoryCode)
                .build());
    }

    private Optional<CreateAggDeliveryShopRequest> buildDeliveryStoreCreateRequestForGloryStore(DeliveryPoi deliveryPoi) {
        TenantStoreInfo tenantStoreInfo = tenantSystemClient.queryStoreDetailInfo(deliveryPoi.getTenantId(), deliveryPoi.getStoreId());
        if (tenantStoreInfo == null) {
            log.warn("查询门店信息失败, tenantId:{}, storeId:{}", deliveryPoi.getTenantId(), deliveryPoi.getStoreId());
            Cat.logEvent("CREATE_DELIVERY_SHOP_FAIL", "QUERY_TENANT_STORE_FAILED");
            return Optional.empty();
        }


        Result<Address> queryStoreAddressResult = getStoreAddress(deliveryPoi, tenantStoreInfo);
        if (queryStoreAddressResult.isFail()) {
            return Optional.empty();
        }

        TenantInfo tenantInfo=tenantSystemClient.queryTenantInfo(deliveryPoi.getTenantId());
        String categoryCode = aggDeliveryWrapper.getCategoryCode(deliveryPoi.getTenantId());

        return Optional.of(CreateAggDeliveryShopRequest
                .builder()
                .shopId(deliveryPoi.getStoreId())
                .aggDeliveryPlatformId(deliveryPoi.getDeliveryPlatform().getCode())
                .cityCode(tenantStoreInfo.getCityCode() + "")
                .districtCode(tenantStoreInfo.getAreaCode() + "")
                .shopAddress(tenantStoreInfo.getAddress())
                .shopLatitude(Double.parseDouble(queryStoreAddressResult.getInfo().getCoordinatePoint().getLatitude()))
                .shopLongitude(Double.parseDouble(queryStoreAddressResult.getInfo().getCoordinatePoint().getLongitude()))
                .shopName(tenantStoreInfo.getStoreName())
                // 设置请求的发送者电话，根据租户和门店信息获取门店的电话号码
                .shopPhone(getPhoneByTenantStoreInfo(deliveryPoi.getTenantId(), tenantStoreInfo))
                .tenantId(deliveryPoi.getTenantId())
                .mapType(BigInteger.ONE.intValue())
                .category(BigInteger.ONE.intValue())
                .tenantName(tenantInfo==null ? "":tenantInfo.getTenantName())
                .appInfo(getAppInfo())
                .categoryCode(categoryCode)
                .build());
    }

    public Optional<Long> createShopFallback(DeliveryPoi deliveryPoi) {
        log.error("createShopFallback : deliveryPoi:{}", deliveryPoi);
        throw new DeliveryPlatformException(PLATFORM_CREATE_SHOP_EXCEPTION);
    }

    private CreateAggDeliveryRequest buildBaseAggCreateDeliveryRequest(OrderInfo orderInfo) {
        // 预计送达时间以订单期望截止时间为准
        LocalDateTime estimatedDeliveryTime = Optional.ofNullable(orderInfo.getEstimatedDeliveryEndTime()).orElse(orderInfo.getEstimatedDeliveryTime());
        String addressDetail = orderInfo.getReceiver().getReceiverAddress().getAddressDetail();
        CreateAggDeliveryRequest request = CreateAggDeliveryRequest.builder()
                .appInfo(getAppInfo())
                .tenantId(orderInfo.getOrderKey().getTenantId())
                .shopId(orderInfo.getOrderKey().getStoreId())
                .qnhStoreId(orderInfo.getPoiId())
                .aggDeliveryPlatformId(getDeliveryPlatform().getCode())
                .orderId(orderInfo.getOrderKey().getOrderId())
                .orderBizType(orderInfo.getOrderBizType())
                .orderViewId(orderInfo.getChannelOrderId())
                .shopSequence(DaySeqNumUtil.getDaySeqNumWithoutDHTenant(orderInfo.getDaySeq(), orderInfo.getDaySeqNum(),
                        orderInfo.getOrderKey().getTenantId()))
                .orderType(orderInfo.isBookingOrder() ? 1 : 0)
                .expectDeliveryTime((long) TimeUtil.toSeconds(Optional.ofNullable(estimatedDeliveryTime)
                        .orElse(LocalDateTime.now())).orElse(0))
                .expectPickupTime((long) TimeUtil.toSeconds(LocalDateTime.now()).orElse(0))
                .receiverLongitude(orderInfo.getReceiver().getReceiverAddress().getCoordinatePoint().getLongitude())
                .receiverLatitude(orderInfo.getReceiver().getReceiverAddress().getCoordinatePoint().getLatitude())
                .receiverAddress(addressDetail)
                .receiverAddressDetail(addressDetail)
                .receiverName(orderInfo.getReceiver().getReceiverName())
                .receiverPhone(orderInfo.getReceiver().getReceiverPhone())
                .coordinateType(orderInfo.getReceiver().getReceiverAddress().getCoordinateType().getCode())
                .goods(orderInfo.getGoodsList()
                        .stream()
                        .map(it -> AggDeliveryGoodDTO
                                .builder()
                                .name(ConvertUtil.replaceSpecialStr(it.getName()))
                                .count(it.getQuantity())
                                .price(it.getSinglePrice())
                                .unit(it.getSellUnit())
                                .weight(it.getSingleGoodsWeight())
                                .total(Optional.ofNullable(it.getSinglePrice()).orElse(0) * it.getQuantity())
                                .build())
                        .collect(Collectors.toList())
                )
                .goodsValue(orderInfo.getOriginalTotalAmount())
                .saleTotalAmount(getOrderFinance(orderInfo))
                .goodsWeight(orderInfo.getGoodsTotalWeight())
                .goodsCategory(1)
                .discountAmount(orderInfo.getDiscountAmt() == null ? 0 : orderInfo.getDiscountAmt())
                .actualPayAmount(orderInfo.getActualPayAmt() == null ? 0 : orderInfo.getActualPayAmt())
                .orderCreateTime((long) TimeUtil.toSeconds(Optional.ofNullable(orderInfo.getCreateTime())
                        .orElse(LocalDateTime.now())).orElse(0))
                .remark(ConvertUtil.replaceSpecialStr(orderInfo.getComments()))
                .build();
        if (Objects.equals(orderInfo.getOrderBizType(), DynamicOrderBizType.MEITUAN_DRUNK_HOURSE.getValue())) {
            //订单侧其他渠道的的隐私号和正常号赋值反了，微商城渠道的隐私号才是真正的隐私号
            //UPDATE：微商城的隐私号，如果在C端勾选了隐私号则为隐私号，不勾选中间会带****号（美团未勾选传的是真实号码）
            String receiverPrivacyPhone = orderInfo.getReceiver().getReceiverPrivacyPhone();
            if (StringUtils.isNotBlank(receiverPrivacyPhone) && !receiverPrivacyPhone.contains("*")) {
                request.setReceiverPhone(receiverPrivacyPhone);
            }
        }

        return request;
    }

    private Integer getOrderFinance(OrderInfo orderInfo) {
        OrderKey orderKey = orderInfo.getOrderKey();
        if (!MccConfigUtils.aggDeliveryPriceTenantWhitelist(orderKey.getTenantId())) {
            return null;
        }
        Integer orderFinance = orderThriftServiceWrapper.queryOrderFinance(orderKey.getTenantId(), orderInfo.getPoiId(), orderKey.getOrderId());
        return orderFinance != null ? orderFinance : orderInfo.getOriginalTotalAmount();
    }

    protected AggDeliveryPlatformAppDTO getAppInfo() {
        return Optional.ofNullable(AggDeliveryPlatformAppConfigUtils.getAggDeliveryPlatformAppConfig(getDeliveryPlatform().getCode()))
                .map(it -> new AggDeliveryPlatformAppDTO(it.getAppKey(), it.getAppSecret()))
                .orElse(null);
    }

    protected abstract Result<Address> getStoreAddress(DeliveryPoi deliveryPoi, TenantStoreInfo tenantStoreInfo);

    @Override
    @CatTransaction
    public Optional<Failure> syncOrderInfoChange(DeliveryOrder deliveryOrder, OrderInfo orderInfo, List<Integer> changedFields) {
        try {
            SyncOrderInfoChangeRequest request = buildSyncOrderInfoChangeRequest(orderInfo, changedFields);
            log.info("ChannelAggDeliveryThriftService.syncOrderInfoChange begin, request={}", request);
            SyncOrderInfoChangeResponse response = getChannelAggDeliveryThriftService().syncOrderInfoChange(request);
            log.info("ChannelAggDeliveryThriftService.syncOrderInfoChange finish, response={}", response);
            if (response == null || response.getCode() == null) {
                Cat.logEvent(AGG_DELIVERY_ORDER_CHANGE_NOTIFY + getDeliveryPlatform().getCode(), "ORDER_CHANGE_NOTIFY_AGG_PLATFORM_FAIL");
                return Optional.of(new Failure(FailureCodeEnum.ORDER_CHANGE_NOTIFY_AGG_PLATFORM_FAILED));
            }

            if (response.getCode() == SUCCESS.getCode()) {
                Cat.logEvent(AGG_DELIVERY_ORDER_CHANGE_NOTIFY + getDeliveryPlatform().getCode(), "ORDER_CHANGE_NOTIFY_AGG_PLATFORM_SUCCESS");
                return Optional.empty();
            } else {
                Cat.logEvent(AGG_DELIVERY_ORDER_CHANGE_NOTIFY + getDeliveryPlatform().getCode(), "ORDER_CHANGE_NOTIFY_AGG_PLATFORM_FAIL");
                return Optional.of(new Failure(FailureCodeEnum.ORDER_CHANGE_NOTIFY_AGG_PLATFORM_FAILED));
            }
        } catch (Exception e) {
            log.error("syncOrderInfoChange failed", e);
            Cat.logEvent(AGG_DELIVERY_ORDER_CHANGE_NOTIFY + getDeliveryPlatform().getCode(), "ORDER_CHANGE_NOTIFY_AGG_PLATFORM_FAIL");
            return Optional.of(new Failure(FailureCodeEnum.ORDER_CHANGE_NOTIFY_AGG_PLATFORM_FAILED));
        }
    }

    private SyncOrderInfoChangeRequest buildSyncOrderInfoChangeRequest(OrderInfo orderInfo, List<Integer> changedFields) {
        SyncOrderInfoChangeRequest request = new SyncOrderInfoChangeRequest();

        request.setAppInfo(getAppInfo());
        request.setOrderId(orderInfo.getOrderKey().getOrderId());
        request.setComments(orderInfo.getComments());
        request.setAddressChangeFee(orderInfo.getAddressChangeFee());
        request.setRefundAddressChangeFee(orderInfo.getRefundAddressChangeFee());
        LocalDateTime estimatedDeliveryTime = Optional.ofNullable(orderInfo.getEstimatedDeliveryEndTime()).orElse(orderInfo.getEstimatedDeliveryTime());
        request.setEstimatedDeliveryTime(TimeUtil.toMilliSeconds(estimatedDeliveryTime));
        request.setAggDeliveryPlatformId(getDeliveryPlatform().getCode());
        request.setChangedFields(transformChangedFields(changedFields));

        Receiver receiver = orderInfo.getReceiver();
        if (Objects.nonNull(receiver)) {
            request.setReceiverName(receiver.getReceiverName());
            request.setReceiverPhone(receiver.getReceiverPhone());
            Address receiverAddress = receiver.getReceiverAddress();
            if (Objects.nonNull(receiverAddress)) {
                request.setReceiverAddress(receiverAddress.getAddressDetail());
                CoordinatePoint receiverCoordinatePoint = receiverAddress.getCoordinatePoint();
                if (Objects.nonNull(receiverCoordinatePoint)) {
                    request.setReceiverLongitude(receiverCoordinatePoint.getLongitude());
                    request.setReceiverLatitude(receiverCoordinatePoint.getLatitude());
                }
            }

        }

        return request;
    }

    /**
     * 将订单侧的变更字段转换为履约侧的变更字段
    */
    private Set<Integer> transformChangedFields(List<Integer> changedFields) {
        if (CollectionUtils.isEmpty(changedFields)) {
            return Collections.emptySet();
        }
        return changedFields.stream().map(value -> {
            ChangedFieldEnum changedFieldEnum = ChangedFieldEnum.enumOf(value);
            return OrderChangeFieldEnum.map2OrderChangeFieldEnum(changedFieldEnum);
        }).filter(Optional::isPresent).map(optional -> optional.get().getValue()).collect(Collectors.toSet());
    }

}
