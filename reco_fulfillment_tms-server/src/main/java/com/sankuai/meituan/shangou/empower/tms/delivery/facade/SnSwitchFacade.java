package com.sankuai.meituan.shangou.empower.tms.delivery.facade;

import com.dianping.cat.Cat;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/9/25 16:09
 **/
@Service
@Slf4j
public class SnSwitchFacade {
    @Autowired
    private PoiThriftService poiThriftService;


    private Integer getPoiCityId(Long tenantId, Long offlineStoreId) {
        try {
            log.info("invoke poiThriftService.queryTenantPoiInfoMapByPoiIds start, storeId:{}", offlineStoreId);
            PoiMapResponse response = poiThriftService.queryTenantPoiInfoMapByPoiIds(Collections.singletonList(offlineStoreId), tenantId);
            log.info("invoke poiThriftService.queryTenantPoiInfoMapByPoiIds end, storeId:{}, response:{}", offlineStoreId, response);

            if (response == null || response.getStatus() == null) {
                throw new RuntimeException("调用租户服务系统失败");
            }

            if (!Objects.equals(response.getStatus().getCode(), ResultCodeEnum.SUCCESS.getCode())) {
                throw new RuntimeException("调用租户服务业务失败");
            }

            PoiInfoDto poiInfoDto = response.getPoiInfoMap().get(offlineStoreId);

            return poiInfoDto.getDistrict().getCityId();
        } catch (Exception e) {
            log.error("调用租户服务查询门店所属的城市失败", e);
            Cat.logEvent("DH_ADAPT_SN", "QUERY_POI_CITY_ID_FAIL");
            return null;
        }

    }
}
