package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryLaunchCmdMessage {

	private Long tenantId;

	private Long shopId;

	private Integer orderSource;

	private Integer orderBizType;

	private Long orderId;

	private String viewOrderId;

	private Long fulfillOrderId;

	private Integer platformSource;

	public DeliveryLaunchCmdMessage(OrderInfo orderInfo) {
		this.tenantId = orderInfo.getOrderKey().getTenantId();
		this.shopId = orderInfo.getOrderKey().getStoreId();
		this.orderId = orderInfo.getOrderKey().getOrderId();
		this.orderSource = orderInfo.getOrderSource();
		this.orderBizType = orderInfo.getOrderBizType();
		this.viewOrderId = orderInfo.getChannelOrderId();
		this.platformSource = PlatformSourceEnum.OMS.getCode();
	}
}
