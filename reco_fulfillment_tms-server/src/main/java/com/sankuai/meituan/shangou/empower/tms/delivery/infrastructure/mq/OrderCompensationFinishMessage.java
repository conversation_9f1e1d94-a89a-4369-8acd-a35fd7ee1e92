package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderCompensationFinishMessage {
    /**
     * 租户
     */
    private Long tenantId;
    /**
     * 门店id
     */
    private Long shopId;
    /**
     * 赋能订单id
     */
    private Long orderId;
    /**
     * 渠道订单id
     */
    private String viewOrderId;
    /**
     * 订单类型
     */
    private String degradeModules;
    /**
     * 渠道类型
     */
    private Integer orderBizType;
    /**
     * 补单状态
     */
    private Integer compensateStatus;

    /**
     * 仓id
     * @return
     */
    private Long warehouseId;

    private String notifyType;

    public Long getPoiId(){
        return warehouseId == null ? shopId : warehouseId;
    }

}
