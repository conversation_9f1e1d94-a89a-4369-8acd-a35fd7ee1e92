package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.DeliveryOperationFailedException;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.OrderPlatformDeliveryOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.FourWheelCheckCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.FourWheelCheckInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.PlatformDeliveryCheckSquirrelService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.PlatformDeliveryCheckMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PlatformDeliveryCheckMessageListener extends AbstractDeadLetterConsumer{

    @Autowired
    private OrderPlatformDeliveryOperateService orderPlatformDeliveryOperateService;

    @Autowired
    private PlatformDeliveryCheckSquirrelService platformDeliveryCheckSquirrelService;


    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.PLATFORM_DELIVERY_CHECK_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("开始消费平台配送检查消息 msg:{}",message);
        PlatformDeliveryCheckMessage checkMessage = translateMessage(message);
        if(checkMessage == null){
            log.info("消息为空");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(checkMessage.getOrderBizType());

        try {
            FourWheelCheckInfo checkInfo = orderPlatformDeliveryOperateService.fourWheelCheck(new FourWheelCheckCmd(checkMessage.getTenantId(), checkMessage.getStoreId(), null,channelType.getChannelId(),null, checkMessage.getViewOrderId()));
            if(checkInfo == null){
                log.info("checkInfo为空");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            platformDeliveryCheckSquirrelService.set(checkMessage.getOrderId()+"",checkInfo);

        }catch (DeliveryOperationFailedException fe){
            log.error("check business error",fe);
        } catch (Exception e){
            log.error("check error",e);
            return ConsumeStatus.CONSUME_FAILURE;
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private PlatformDeliveryCheckMessage translateMessage(MafkaMessage mafkaMessage) {
        try {
            return translateMessage(mafkaMessage, PlatformDeliveryCheckMessage.class);
        } catch (Exception e) {
            log.error("解析消息失败:{}", mafkaMessage, e);
            return null;
        }
    }
}
