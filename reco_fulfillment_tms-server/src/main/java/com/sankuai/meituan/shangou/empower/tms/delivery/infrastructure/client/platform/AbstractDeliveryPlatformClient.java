package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform;

import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.DeliveryTenantPoiSyncRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.LinkTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.SiteTypeEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/12
 */
public abstract class AbstractDeliveryPlatformClient implements DeliveryPlatformClient {

	public abstract DeliveryPlatformEnum getDeliveryPlatform();

	@Override
	public Result<List<DeliveryChannelPreLaunchInfo>> preLaunch(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
		throw new UnsupportedOperationException("暂不支持预发单操作");
	}

	@Override
	public Optional<LaunchFailure> launch(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder,Integer transferOrderMark) {
		throw new UnsupportedOperationException("暂不支持发起配送操作");
	}

	@Override
	public Optional<Failure> cancelDelivery(DeliveryOrder deliveryOrder) {
		throw new UnsupportedOperationException("暂不支持取消配送操作");
	}

	@Override
	public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryPoi deliveryPoi) {
		throw new UnsupportedOperationException("暂不支持查询骑手位置操作");
	}

	@Override
	public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryChannel deliveryChannelDto, DeliveryPoi deliveryPoi) {
		throw new UnsupportedOperationException("暂不支持查询骑手位置操作");
	}

	@Override
	public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder) {
		throw new UnsupportedOperationException("暂不支持查询骑手位置操作");
	}

	@Override
	public List<LaunchRuleInfo> queryAvailableDeliveryRules(DeliveryPlatformEnum deliveryPlatform) {
		throw new UnsupportedOperationException("暂不支持查询可用策略操作");
	}

	@Override
	public Optional<Long> createShop(DeliveryPoi deliveryPoi) {
		throw new UnsupportedOperationException("暂不支持创建门店操作");
	}

	@Override
	public Optional<Failure> autoSend(DeliveryOrder deliveryOrder) {
		throw new UnsupportedOperationException("暂不支持自动发渠道操作");
	}

	@Override
	public Optional<Failure> closeShop(DeliveryPoi deliveryPoi) {
		throw new UnsupportedOperationException("暂不支持关闭门店操作");
	}

	@Override
	public Optional<Map<String, DeliveryPlatformUrlInfo>> queryLinkInfo(DeliveryPoi deliveryPoi, List<String> markIdList, LinkTypeEnum typeEnum) {
		throw new UnsupportedOperationException("暂不支持获取URL操作");
	}

	@Override
	public Optional<Map<String, DeliveryPlatformUrlInfo>> queryLinkInfoOfToken(DeliveryPoi deliveryPoi, List<String> markIdList, LinkTypeEnum typeEnum, String token, SiteTypeEnum siteType) {
		throw new UnsupportedOperationException("暂不支持获取URL操作");
	}

	@Override
	public Optional<Failure> cancelDeliveryForTransOrder(DeliveryOrder deliveryOrder) {
		throw new UnsupportedOperationException("暂不支持取消配送操作");
	}

	@Override
	public Optional<Failure> deliveryFastAuth(DeliveryPoi deliveryPoi, List<Long> wmStoreIdList, List<ChannelStoreRelation> relationList) {
		throw new UnsupportedOperationException("暂不支持快捷授权操作");
	}

	@Override
	public Optional<Failure> cancelDeliveryForOFC(DeliveryOrder deliveryOrder) {
		throw new UnsupportedOperationException("暂不支持取消配送操作");
	}

	@Override
	public Result<DapShopAuthResult> deliveryShopAuth(DeliveryPlatformEnum deliveryPlatform, Long storeId) {
		throw new UnsupportedOperationException("暂不支持查询门店授权信息操作");
	}

	@Override
	public Optional<Failure> syncTenantPoi(DeliveryTenantPoiSyncRequest request) {
		throw new UnsupportedOperationException("暂不支持同步渠道门店操作");
	}

	@Override
	public Optional<LaunchFailure> manualLaunchDelivery(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
		throw new UnsupportedOperationException("暂不支持手动呼叫配送操作");
	}

	@Override
	public Optional<Failure> syncPaoTuiDeliveryStatusAfterLockOrder(DeliveryOrder deliveryOrder, String packageId) {
		throw new UnsupportedOperationException("暂不支持同步跑腿配送状态操作");
	}

	@Override
	public Optional<Failure> syncOrderInfoChange(DeliveryOrder deliveryOrder, OrderInfo orderInfo, List<Integer> changedFields) {
		throw new UnsupportedOperationException("暂不支持同步订单信息变更操作");
	}

	@Override
	public Optional<Failure> notifyDeliveryPlatformLockStatusChange(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder) {
		throw new UnsupportedOperationException("暂不支持同步跑腿锁单2.0锁单状态变更操作");
	}
}
