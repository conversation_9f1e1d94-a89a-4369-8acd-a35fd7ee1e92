package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.infra.osw.api.org.TEmployeeService;
import com.sankuai.shangou.infra.osw.api.org.dto.request.AccountIdsRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import com.sankuai.shangou.infra.osw.api.poi.TPoiService;
import com.sankuai.shangou.infra.osw.api.poi.dto.request.QueryOperatePoiRequest;
import com.sankuai.shangou.infra.osw.api.poi.dto.response.BusinessPoiDTO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

@Rhino
@Slf4j
public class OSWServiceWrapper {

    @Resource
    private TEmployeeService tEmployeeService;

    @Degrade(rhinoKey = "OSWServiceWrapper.queryEmpByAccountIds", fallBackMethod = "queryEmpByAccountIdsFallBack", timeoutInMilliseconds = 2000)
    public List<EmployeeDTO> queryEmpByAccountIds(Long tenantId, List<Long> accountIds) {
        AccountIdsRequest request = new AccountIdsRequest();
        request.setAccountIds(accountIds);
        request.setTenantId(tenantId);
        TResult<List<EmployeeDTO>> result;
        try {
            log.info("start invoke tEmployeeService.queryEmpByAccountIds, request: {}", request);
            result = tEmployeeService.queryEmpByAccountIds(request);
            log.info("end invoke tEmployeeService.queryEmpByAccountIds, response: {}", result);
        } catch (Exception e) {
            log.info("invoke tEmployeeService.queryEmpByAccountIds error", e);
            throw new ThirdPartyException(e);
        }

        if (!result.isSuccess()) {
            throw new BizException("查询店仓基础信息失败:" + result.getMsg());
        }

        return result.getData();
    }

    public List<EmployeeDTO> queryEmpByAccountIdsFallBack(Long tenantId, List<Long> accountIds){
        log.info("queryEmpByAccountIdsFallBack, accountIds: {},tenantId:{}", accountIds,tenantId);
        return null;
    }

}
