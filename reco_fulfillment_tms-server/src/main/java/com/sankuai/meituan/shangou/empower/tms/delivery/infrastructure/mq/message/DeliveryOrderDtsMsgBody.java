package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * <AUTHOR>
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonIgnoreProperties
public class DeliveryOrderDtsMsgBody implements Serializable {

    private static final long serialVersionUID = 7408150117576455421L;

    /**
     * 运单主键ID
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 赋能订单id
     */
    private Long orderId;

    /**
     * 渠道订单id
     */
    private String channelOrderId;

    /**
     * 渠道类型
     */
    private Integer orderBizType;

    /**
     * 订单日流水号
     */
    private Integer daySeq;

    /**
     * 是否预订单
     * -1:未知;0:非预约单;1:预约单
     */
    private Byte reserved;

    /**
     * 订单来源
     * 1:人工pos 安卓;2:人工pos win;3:h5;10:o2o;20:自助收银;30:中台线上O2O订单
     */
    private Integer orderSource;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人手机号码
     */
    private String receiverPhone;

    /**
     * 收货人隐私号
     */
    private String receiverPrivacyPhone;

    /**
     * 收货人地址
     */
    private String receiverAddress;

    /**
     * 预计送达时间
     */
    @JsonDeserialize(using = TimeStampDeserializer.class)
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 预计送达结束时间
     */
    @JsonDeserialize(using = TimeStampDeserializer.class)
    private LocalDateTime estimatedDeliveryEndTime;

    /**
     * 承运商code
     */
    private Integer deliveryChannel;

    /**
     * 渠道运单ID
     */
    private String channelDeliveryId;

    /**
     * 运单状态
     * 0:初始化, 1:配送发起成功, 30:等待分配骑手, 40:骑手已接单, 45:骑手已到店, 50:骑手已取货, 60:骑手已送达, 110:配送拒单, 120:配送失败, 130:运单已取消
     */
    private Integer deliveryStatus;

    /**
     * 配送异常类型
     * 0:无异常, 1:发起配送失败, 2:商户/门店原因导致发单失败, 3:配送服务能力原因导致异常, 4:用户原因导致异常, 5:运力/骑手原因导致异常, 6:系统原因导致异常, 7:其他未知原因导致异常
     */
    private Integer deliveryExceptionType;

    /**
     * 配送异常详情描述
     */
    private String deliveryExceptionDescription;

    /**
     * 骑手姓名
     */
    private String riderName;

    /**
     * 骑手手机号码
     */
    private String riderPhone;

    /**
     * 运单生效状态
     * 0:该运单生效中，即是真正执行配送的运单, 时间戳(>0):该运单未生效，即非最终配送运单
     */
    private Long activeStatus;

    /**
     * 运单创建时间
     */
    @JsonDeserialize(using = TimeStampDeserializer.class)
    private LocalDateTime createTime;

    /**
     * 运单最后更新时间
     */
    @JsonDeserialize(using = TimeStampDeserializer.class)
    private LocalDateTime updateTime;

    /**
     * 运单最后一次事件发生时间
     */
    @JsonDeserialize(using = TimeStampDeserializer.class)
    private LocalDateTime lastEventTime;

    /**
     * 配送完成时间
     */
    @JsonDeserialize(using = TimeStampDeserializer.class)
    private LocalDateTime deliveryDoneTime;

    /**
     * 配送费用，单位：元
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal deliveryFee;

    /**
     * 配送距离，单位：米
     */
    private Long distance;

    /**
     * 是否取消中的标识符，0否1是
     */
    private Integer cancelMark;

    /**
     * 异常配送码
     */
    private Integer deliveryExceptionCode;

    /**
     * 配送小费，单位：元
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal tipAmount;

    /**
     * 配送成功次数
     */
    private Integer deliveryCount;

    /**
     * 平台配送费，单位：元
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal platformFee;

    /**
     * ext字段
     */
    private String extInfo;

    public static class TimeStampDeserializer extends JsonDeserializer<LocalDateTime> {

        @Override
        public LocalDateTime deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
            return Instant.ofEpochMilli(Long.parseLong(jsonParser.getText())).atZone(ZoneId.systemDefault()).toLocalDateTime();
        }
    }

}
