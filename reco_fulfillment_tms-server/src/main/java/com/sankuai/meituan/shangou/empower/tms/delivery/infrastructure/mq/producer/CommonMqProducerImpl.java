package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.FutureCallback;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.meituan.reco.pickselect.common.mq.producer.MqProducer;
import com.meituan.reco.pickselect.common.mq.producer.ProducerConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Collections;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
@Service
public class CommonMqProducerImpl implements MqProducer, DisposableBean {

    private static final Map<ProducerConfig, IProducerProcessor<Object, String>> producerCache = new ConcurrentHashMap<>();
    private static final Map<ProducerConfig, IProducerProcessor<Object, String>> delayProducerCache = new ConcurrentHashMap<>();

    @SuppressWarnings("UnstableApiUsage")
    private static final Interner<String> POOL = Interners.newWeakInterner();
    @SuppressWarnings("UnstableApiUsage")
    private static final Interner<String> DELAY_POOL = Interners.newWeakInterner();

    private IProducerProcessor<Object, String> getProducer(ProducerConfig producerConfig) throws Exception {
        if (producerCache.containsKey(producerConfig)) {
            return producerCache.get(producerConfig);
        } else {
            synchronized (POOL.intern(producerConfig.toString())) {
                if (!producerCache.containsKey(producerConfig)) {
                    Properties properties = new Properties();
                    properties.setProperty(ConsumerConstants.MafkaBGNamespace, producerConfig.getBgName());
                    properties.setProperty(ConsumerConstants.MafkaClientAppkey, producerConfig.getAppKey());
                    IProducerProcessor<Object, String> producerProcessor = buildProducer(producerConfig, properties);
                    log.info("build producer success . topic:{}", producerConfig);
                    producerCache.put(producerConfig, producerProcessor);
                }
            }
            return producerCache.get(producerConfig);
        }
    }

    private IProducerProcessor<Object, String> buildProducer(ProducerConfig producerConfig, Properties properties) throws Exception {
        return MafkaClient.buildProduceFactory(properties, producerConfig.getTopic(), false);
    }

    private IProducerProcessor<Object, String> getDelayProducer(ProducerConfig producerConfig) throws Exception {
        if (delayProducerCache.containsKey(producerConfig)) {
            return delayProducerCache.get(producerConfig);
        } else {
            synchronized (DELAY_POOL.intern(producerConfig.toString())) {
                if (!delayProducerCache.containsKey(producerConfig)) {
                    Properties properties = new Properties();
                    properties.setProperty(ConsumerConstants.MafkaBGNamespace, producerConfig.getBgName());
                    properties.setProperty(ConsumerConstants.MafkaClientAppkey, producerConfig.getAppKey());
                    IProducerProcessor<Object, String> producerProcessor = buildDelayProducer(producerConfig, properties);
                    log.info("build delay producer success, topic:{}", producerConfig);
                    delayProducerCache.put(producerConfig, producerProcessor);
                }
            }
            return delayProducerCache.get(producerConfig);
        }
    }

    private IProducerProcessor<Object, String> buildDelayProducer(ProducerConfig producerConfig, Properties properties) throws Exception {
        return MafkaClient.buildDelayProduceFactory(properties, producerConfig.getTopic(), false);
    }

    @Override
    public Map<ProducerConfig, IProducerProcessor<Object, String>> getAllProducers() {
        return Collections.unmodifiableMap(producerCache);
    }

    @Override
    public String publish(ProducerConfig producerConfig, String msg, int delaySec) throws IOException {

        ProducerResult producerResult;
        try {
            producerResult = getDelayProducer(producerConfig).sendDelayMessage(msg, delaySec * 1000);

        } catch (Exception e) {
            throw new IOException("publish msg exception", e);
        }
        if (!ProducerStatus.SEND_OK.equals(producerResult.getProducerStatus())) {
            throw new IOException("publish msg fail.");
        }
        return producerResult.getMessageID();
    }

    @Override
    public String publish(ProducerConfig producerConfig, String msg) throws IOException {
        ProducerResult producerResult;
        try {
            producerResult = getProducer(producerConfig).sendMessage(msg);

        } catch (Exception e) {
            throw new IOException("publish msg exception", e);
        }
        if (!ProducerStatus.SEND_OK.equals(producerResult.getProducerStatus())) {
            throw new IOException("publish msg fail.");
        }
        return producerResult.getMessageID();
    }

    @Override
    public String publishWithPartition(ProducerConfig producerConfig, String msg, Object partKey) throws IOException {
        ProducerResult producerResult;
        try {
            producerResult = getProducer(producerConfig).sendMessage(msg, partKey);

        } catch (Exception e) {
            throw new IOException("publish partKey msg exception", e);
        }
        if (!ProducerStatus.SEND_OK.equals(producerResult.getProducerStatus())) {
            throw new IOException("publish partKey msg fail.");
        }
        return producerResult.getMessageID();
    }

    @Override
    public String asyncPublish(ProducerConfig producerConfig, String msg, FutureCallback callback) throws IOException {
        ProducerResult result;
        try {
            result = getProducer(producerConfig).sendAsyncMessage(msg, callback);

        } catch (Exception e) {
            throw new IOException("async publish msg exception.", e);
        }
        if (!ProducerStatus.SEND_OK.equals(result.getProducerStatus())) {
            throw new IOException("async publish msg fail.");
        }
        return result.getMessageID();
    }

    @Override
    public String asyncPublishWithPartition(ProducerConfig config, Object partKey, String msg, FutureCallback callback) throws IOException {
        ProducerResult result;
        try {
            result = getProducer(config).sendAsyncMessage(msg, partKey,callback);

        } catch (Exception e) {
            throw new IOException("async publish Partition msg exception.", e);
        }
        if (!ProducerStatus.SEND_OK.equals(result.getProducerStatus())) {
            throw new IOException("async publish Partition msg fail.");
        }
        return result.getMessageID();
    }

    @Override
    public void publishWithRetry(ProducerConfig producerConfig, String messageToSend, int delayTime, int retryTimes) throws IOException {

        IOException mqException = null;
        while (retryTimes-- > 0) {
            try {
                if (delayTime <= 0) {
                    publish(producerConfig, messageToSend);
                } else {
                    publish(producerConfig, messageToSend, delayTime);
                }
                return;
            } catch (Exception e) {
                mqException = new IOException("重试发送[" + producerConfig.getTopic() + "]消息异常", e);
            }
        }
        if (mqException != null) {
            log.error("发送消息失败，topic:{}, msg:{}", producerConfig.getTopic(), messageToSend);
            throw mqException;
        }
    }

    @Override
    public void destroy() {
        for (Map.Entry<ProducerConfig, IProducerProcessor<Object, String>> entry : producerCache.entrySet()) {
            try {
                entry.getValue().close();
            } catch (Exception e) {
                log.error("close producer fail. topic:{}", entry.getKey(), e);
            }
        }
        for (Map.Entry<ProducerConfig, IProducerProcessor<Object, String>> entry : delayProducerCache.entrySet()) {
            try {
                entry.getValue().close();
            } catch (Exception e) {
                log.error("close delayProducer fail. topic:{}", entry.getKey(), e);
            }
        }
    }
}
