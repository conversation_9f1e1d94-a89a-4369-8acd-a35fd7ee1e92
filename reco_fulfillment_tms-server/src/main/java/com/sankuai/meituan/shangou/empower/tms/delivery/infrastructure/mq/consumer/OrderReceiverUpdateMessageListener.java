package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryTransformApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.TransferMtFamousTavernOrderSquirrelOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.TransferOrderSquirrelOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.DapDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.MaltFarmDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.MerchantSelfDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.OrderReceiverUpdateBaseMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.OrderReceiverUpdateMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.service.DiscreetDeliveryService;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.facade.RouteFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Predicate;

import static com.meituan.mafka.client.consumer.ConsumeStatus.*;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * 拷贝自DeliveryOrderTransMessageListener
 * <AUTHOR>
 * @date 2024-12-13
 */
@Slf4j
@Component
public class OrderReceiverUpdateMessageListener extends AbstractDeadLetterConsumer{


    @Resource
    private OrderSystemClient orderSystemClient;

    @Resource
    private MaltFarmDeliveryPlatformClient maltFarmDeliveryPlatformClient;

    @Resource
    private DapDeliveryPlatformClient dapDeliveryPlatformClient;

    @Resource
    private MerchantSelfDeliveryPlatformClient merchantSelfDeliveryPlatformClient;

    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private RouteFacade routeFacade;


    @Resource
    private TransferOrderSquirrelOperateService transferOrderSquirrelOperateService;

    @Resource
    private DeliveryTransformApplicationService deliveryTransformApplicationService;

    @Resource
    private DeliveryChannelApplicationService deliveryChannelApplicationService;

    @Resource
    private DiscreetDeliveryService discreetDeliveryService;

    @Resource
    private TransferMtFamousTavernOrderSquirrelOperateService transferMtFamousTavernOrderSquirrelOperateService;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return  MQConsumerEnum.ORDER_RECEIVER_UPDATE;
    }


    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("开始消费美团名酒馆订单重新获取电话后发三方配送消息: {}", message);
        OrderReceiverUpdateBaseMessage baseMessage = translateMessage(message);
        if (baseMessage == null) {
            return CONSUME_SUCCESS;
        }
        OrderReceiverUpdateMessage msg = new OrderReceiverUpdateMessage(baseMessage);
        Result<OrderInfo> orderQueryResult = orderSystemClient.getOrderInfo(new OrderKey(msg.getTenantId(), msg.getStoreId(), msg.getOrderId()), false);
        if (orderQueryResult.isFail()) {
            log.info("订单查询失败,{}",message);
            return CONSUME_FAILURE;
        }
        OrderInfo orderInfo = orderQueryResult.getInfo();

        // 名酒馆
        processMtFamousTavern(orderInfo, msg);


        // 如果订单已到达终态，不再进行后续逻辑
        if (OrderStatusEnum.CANCELED.getValue() == orderInfo.getOrderStatus() || OrderStatusEnum.COMPLETED.getValue() == orderInfo.getOrderStatus()) {
            log.info("订单[{}]已经取消或已经完成，无需进行后续转自送/聚合配送", msg.getOrderId());
            return CONSUME_SUCCESS;
        }


        //读取缓存中的配送平台
        String key = transferOrderSquirrelOperateService.getKey(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(), orderInfo.getOrderKey().getOrderId());
        TransferOperateInfo transferOperate = new TransferOperateInfo();
        Optional<TransferOperateInfo> transferOperateInfo = transferOrderSquirrelOperateService.get(key, TransferOperateInfo.class);
        if (!transferOperateInfo.isPresent()) {
            log.error("查询转单配送平台为空，放弃转单");
            return CONSUME_FAILURE;
        } else {
            transferOperate = transferOperateInfo.get();
        }
        if (Objects.isNull(transferOperate.getDeliveryPlatformEnum())) {
            log.error("查询转单配送平台为空，放弃转单{}", transferOperate);
            return CONSUME_FAILURE;
        }
        Integer lastDeliveryPlatform = TransDeliveryPlatformEnum.SELF_DELIVERY.getCode();
        //查当前所有运单
        List<DeliveryOrder> deliveryOrders = getDeliveryOrders(orderInfo);

        DeliveryOrder currentDeliveryOrder = null;
        if(CollectionUtils.isNotEmpty(deliveryOrders)) {
            currentDeliveryOrder = deliveryOrders.stream().filter(new Predicate<DeliveryOrder>() {
                @Override
                public boolean test(DeliveryOrder deliveryOrder) {
                    return deliveryOrder.isActive();
                }
            }).findFirst().orElse(deliveryOrders.stream().sorted(new Comparator<DeliveryOrder>() {
                @Override
                public int compare(DeliveryOrder o1, DeliveryOrder o2) {
                    return o1.getCreateTime().isAfter(o2.getCreateTime()) ? -1:1;
                }
            }).findFirst().get());
            DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(currentDeliveryOrder.getDeliveryChannel());
            if (DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode() == currentDeliveryOrder.getDeliveryChannel()
                    || DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode() == deliveryChannel.getDeliveryPlatFormCode()) {
                lastDeliveryPlatform = TransDeliveryPlatformEnum.ORDER_PLATFORM_DELIVERY.getCode();
            }

            //歪马 && 当前运单是三方 && 当前运单状态为0
            boolean noNeedToCancel = noNeedToCancel(currentDeliveryOrder);
            //取消当前运单
            if(!noNeedToCancel) {
                Optional<Failure> cancelFailure = currentDeliveryOrder.cancelForTransOrder();
                if (cancelFailure.isPresent()) {
                    log.warn("订单[{}]转自配送后，取消当前平台配送运单失败，失败原因：{}", orderInfo.getOrderKey(), cancelFailure.get());
                    return CONSUME_FAILURE;
                }
            }
            if(currentDeliveryOrder.isActive() && (currentDeliveryOrder.getStatus() == DeliveryStatusEnum.INIT || currentDeliveryOrder.getStatus() == DeliveryStatusEnum.DELIVERY_LAUNCHED)){
                currentDeliveryOrder.onChange(DeliveryEventEnum.DELIVERY_CANCEL, DeliveryExceptionInfo.NO_EXCEPTION, currentDeliveryOrder.getRiderInfo(), LocalDateTime.now());
            }
        } else if (isProcessLastDeliveryPlatform4OrderPlatformDelivery(orderInfo, msg)) {
            lastDeliveryPlatform = TransDeliveryPlatformEnum.ORDER_PLATFORM_DELIVERY.getCode();
        }

        //常规转单
        int transType = DeliveryOrderTransEnum.NORMAL_TRANS.getTransType();
        transType = getTransType(deliveryOrders, transType);

        DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
                orderInfo.getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
        Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(orderInfo.getOrderKey().getTenantId(),
                orderInfo.getWarehouseId(), orderBizType.getChannelId());
        if(!opDeliveryPoi.isPresent()){
            log.info("门店查询失败,{}",message);
            return CONSUME_FAILURE;
        }
        //业务线为不是医药无人仓
        discreetDeliveryService.replaceNotMedicineUnmannedGoodsCode2SkuName(orderInfo.getOrderKey(), orderInfo, orderSystemClient, orderInfo.getWarehouseId());

        TransferOrderMarkEnum transferOrderMarkEnum = TransferOrderMarkEnum.NORMAL_ORDER;
        if(orderInfo.getOrderTransInfo()!=null && orderInfo.getOrderTransInfo().getDispatchShopId()!=null){
            transferOrderMarkEnum = TransferOrderMarkEnum.TRANSFER_ORDER;
        }

        orderInfo.setSelfDelivery(true);
        DeliveryOrder deliveryOrder=null;
        switch (transferOperate.getDeliveryPlatformEnum()){
            case DAP_DELIVERY_PLATFORM:
                deliveryOrder = transToDapDelivery(msg, orderInfo, transType, currentDeliveryOrder, opDeliveryPoi.get(), transferOrderMarkEnum);
                if (Objects.isNull(deliveryOrder)) {
                    Cat.logEvent("TURN_LAUNCH_DAP_FAIL", "FAIL");
                    return RECONSUME_LATER;
                }
                break;
            case MALT_FARM_DELIVERY_PLATFORM:
                deliveryOrder = transToMaltFarmDelivery(msg, orderInfo, transType, currentDeliveryOrder, opDeliveryPoi.get(), transferOrderMarkEnum);
                break;
            case MERCHANT_SELF_DELIVERY:
                deliveryOrder = transToMerchantSelfDelivery(msg, orderInfo, transType, currentDeliveryOrder, opDeliveryPoi.get(), transferOrderMarkEnum);
                break;
            default:
                break;
        }
        deliveryTransformApplicationService.notifyOrderTransfer(deliveryOrder, lastDeliveryPlatform);
        deliveryTransformApplicationService.sendTransferTrack(deliveryOrder, transferOperate);
        return CONSUME_SUCCESS;
    }

    private int getTransType(List<DeliveryOrder> deliveryOrders, int transType) {
        if(CollectionUtils.isNotEmpty(deliveryOrders)){
            Optional<DeliveryOrder> optionalDelivery = deliveryOrders.stream().filter(new Predicate<DeliveryOrder>() {
                @Override
                public boolean test(DeliveryOrder deliveryOrder) {
                    return deliveryOrder.getExceptionType() != DeliveryExceptionTypeEnum.NO_EXCEPTION;
                }
            }).findAny();
            if(optionalDelivery.isPresent()){
                //异常转单
                transType = DeliveryOrderTransEnum.DELIVERY_EXCEPTION_TRANS.getTransType();
            }
        }
        return transType;
    }

    private DeliveryOrder transToDapDelivery(OrderReceiverUpdateMessage msg, OrderInfo orderInfo, int transType, DeliveryOrder currentDeliveryOrder, DeliveryPoi deliveryPoi, TransferOrderMarkEnum transferOrderMarkEnum) {
        DeliveryOrder deliveryOrder;
        log.info("名酒馆转单后平台为青云 orderId: {}", msg.getOrderId());
        deliveryOrder = DeliveryOrder.fromBusinessCustomer(orderInfo, DeliveryChannelEnum.DAP_DELIVERY.getCode(), StringUtils.EMPTY);
        deliveryOrder.setTransType(transType);
        if(currentDeliveryOrder !=null && currentDeliveryOrder.getPlatformSourceEnum() == PlatformSourceEnum.OFC){
            deliveryOrder.setPlatformSourceEnum(PlatformSourceEnum.OFC);
            deliveryOrder.setStoreId(currentDeliveryOrder.getStoreId());
            deliveryOrder.setFulfillmentOrderId(currentDeliveryOrder.getFulfillmentOrderId());
            deliveryOrder.setOrderKey(new OrderKey(currentDeliveryOrder.getTenantId(), currentDeliveryOrder.getStoreId(), currentDeliveryOrder.getOrderId()));
        }
        deliveryOrder.activate();
        deliveryOrderRepository.save(deliveryOrder);

        Optional<LaunchFailure> launchFailure = dapDeliveryPlatformClient.launch(deliveryPoi, orderInfo, deliveryOrder, transferOrderMarkEnum.getCode());
        if (launchFailure.isPresent()) {
            return null;
        }
        return deliveryOrder;
    }

    private DeliveryOrder transToMaltFarmDelivery(OrderReceiverUpdateMessage msg, OrderInfo orderInfo, int transType, DeliveryOrder currentDeliveryOrder, DeliveryPoi deliveryPoi, TransferOrderMarkEnum transferOrderMarkEnum) {
        DeliveryOrder deliveryOrder;
        log.info("名酒馆转单后平台为麦芽田 orderId: {}", msg.getOrderId());
        deliveryOrder = DeliveryOrder.fromBusinessCustomer(orderInfo, DeliveryChannelEnum.MALT_FARM.getCode(), StringUtils.EMPTY);
        deliveryOrder.setTransType(transType);
        if(currentDeliveryOrder !=null && currentDeliveryOrder.getPlatformSourceEnum() == PlatformSourceEnum.OFC){
            deliveryOrder.setPlatformSourceEnum(PlatformSourceEnum.OFC);
            deliveryOrder.setStoreId(currentDeliveryOrder.getStoreId());
            deliveryOrder.setFulfillmentOrderId(currentDeliveryOrder.getFulfillmentOrderId());
            deliveryOrder.setOrderKey(new OrderKey(currentDeliveryOrder.getTenantId(), currentDeliveryOrder.getStoreId(), currentDeliveryOrder.getOrderId()));
        }
        deliveryOrder.activate();
        deliveryOrderRepository.save(deliveryOrder);
        maltFarmDeliveryPlatformClient.launch(deliveryPoi, orderInfo,deliveryOrder, transferOrderMarkEnum.getCode());
        return deliveryOrder;
    }

    private DeliveryOrder transToMerchantSelfDelivery(OrderReceiverUpdateMessage msg, OrderInfo orderInfo, int transType, DeliveryOrder currentDeliveryOrder, DeliveryPoi deliveryPoi, TransferOrderMarkEnum transferOrderMarkEnum) {
        DeliveryOrder deliveryOrder;
        log.info("名酒馆转单后平台为商家自配送 orderId: {}", msg.getOrderId());
        deliveryOrder = DeliveryOrder.fromBusinessCustomer(orderInfo, DeliveryChannelEnum.MERCHANT_DELIVERY.getCode(), StringUtils.EMPTY);
        deliveryOrder.setTransType(transType);
        if(currentDeliveryOrder !=null && currentDeliveryOrder.getPlatformSourceEnum() == PlatformSourceEnum.OFC){
            deliveryOrder.setPlatformSourceEnum(PlatformSourceEnum.OFC);
            deliveryOrder.setStoreId(currentDeliveryOrder.getStoreId());
            deliveryOrder.setFulfillmentOrderId(currentDeliveryOrder.getFulfillmentOrderId());
            deliveryOrder.setOrderKey(new OrderKey(currentDeliveryOrder.getTenantId(), currentDeliveryOrder.getStoreId(), currentDeliveryOrder.getOrderId()));
        }
        deliveryOrder.activate();
        Result<Double> distanceResult = routeFacade.queryRidePathDistance(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getReceiver().getReceiverAddress().getCoordinatePoint());
        if (distanceResult.isSuccess() && Objects.nonNull(distanceResult.getInfo())) {
            deliveryOrder.setDistance(distanceResult.getInfo().longValue());
        }
        deliveryOrderRepository.save(deliveryOrder);
        merchantSelfDeliveryPlatformClient.launch(deliveryPoi, orderInfo,deliveryOrder, transferOrderMarkEnum.getCode());
        return deliveryOrder;
    }

    private List<DeliveryOrder> getDeliveryOrders(OrderInfo orderInfo) {
        List<DeliveryOrder> deliveryOrders;
        if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
            deliveryOrders = deliveryOrderRepository.getDeliveryOrdersMaxWithOrderId(orderInfo.getOrderKey().getOrderId());
        }else {
            deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMaster(orderInfo.getOrderKey().getOrderId());
        }
        return deliveryOrders;
    }

    private void processMtFamousTavern(OrderInfo orderInfo, OrderReceiverUpdateMessage msg) {
        if(Boolean.TRUE.equals(orderInfo.getIsMtFamousTavern())) {
            // 使用MQ消息体中的收货人联系方式
            if ( Objects.nonNull(orderInfo.getReceiver()) && !msg.getUserPhone().equals(orderInfo.getReceiver().getReceiverPhone())) {
                orderInfo.getReceiver().setReceiverPhone(msg.getUserPhone());
            }

            // 查询缓存中的信息并赋值到msg中
            String famousTavernOrderKey = transferMtFamousTavernOrderSquirrelOperateService.getKey(msg.getTenantId(), msg.getStoreId(), msg.getOrderId());
            Optional<MtFamousTavernTransferCacheInfo> cacheInfoOp = transferMtFamousTavernOrderSquirrelOperateService.get(famousTavernOrderKey, MtFamousTavernTransferCacheInfo.class);
            if (cacheInfoOp.isPresent()) {
                MtFamousTavernTransferCacheInfo cacheInfo = cacheInfoOp.get();
                msg.setDeliveryPlatformCode(cacheInfo.getDeliveryPlatformCode());
            }
        }
    }

    //歪马 && 当前运单是三方 && 当前运单状态为0
    private static boolean noNeedToCancel(DeliveryOrder currentDeliveryOrder) {
        try {
            boolean isThirdDelivery = !Objects.equals(currentDeliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode());
            boolean isInitStatus = Objects.equals(currentDeliveryOrder.getStatus(), DeliveryStatusEnum.INIT);
            return isThirdDelivery && isInitStatus;
        } catch (Exception e) {
            log.error("noNeedToCancel error", e);
            return false;
        }
    }

    private OrderReceiverUpdateBaseMessage translateMessage(MafkaMessage mafkaMessage) {
        try {
            OrderReceiverUpdateBaseMessage message = translateMessage(mafkaMessage, OrderReceiverUpdateBaseMessage.class);
            Preconditions.checkNotNull(message, "empty mafkaMessage");
            return message;

        } catch (Exception e) {
            log.error("解析消息失败:{}", mafkaMessage, e);
            return null;
        }
    }


    /**
     * 是否为平台配送转自送/聚合配送，单独处理lastDeliveryPlatform值，返回true代表需要处理
     */
    private boolean isProcessLastDeliveryPlatform4OrderPlatformDelivery(OrderInfo orderInfo, OrderReceiverUpdateMessage msg) {

        DynamicOrderBizType orderBizTypeEnum = DynamicOrderBizType.findOf(orderInfo.getOrderBizType());
        if (Objects.isNull(orderBizTypeEnum)) {
            return false;
        }

        if (!DynamicOrderBizType.DOU_YIN.equals(orderBizTypeEnum) && !DynamicOrderBizType.YOU_ZAN_MIDDLE.equals(orderBizTypeEnum)) {
            return false;
        }

        if (Objects.isNull(msg.getDeliveryPlatformCode())) {
            return false;
        }

        return Objects.equals(msg.getDeliveryPlatformCode(), DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM.getCode())
                || Objects.equals(msg.getDeliveryPlatformCode(), DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode());
    }
}
