package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.time.LocalDateTime;

/**
 * 配送进度异步更新指令消息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryEventMessage {

	/**
	 * 运单id
	 */
	private Long deliveryId;
	/**
	 * 配送状态
	 */
	private Integer event;
	/**
	 * 骑手信息
	 */
	private Rider rider;
	/**
	 * 配送异常信息
	 */
	private Integer exceptionType;

	/**
	 * 异常描述
	 */
	private String exceptionDescription;
	/**
	 * 事件发生时间
	 */
	private Long eventTime;

	public DeliveryEventMessage(Long deliveryId, DeliveryEventEnum event) {
		Preconditions.checkNotNull(deliveryId, "deliveryId is null");
		Preconditions.checkNotNull(event, "event is null");

		this.deliveryId = deliveryId;
		this.event = event.getCode();
		this.rider = null;
		this.exceptionType = DeliveryExceptionTypeEnum.NO_EXCEPTION.getCode();
		this.exceptionDescription = StringUtils.EMPTY;
		this.eventTime = System.currentTimeMillis();
	}

	public DeliveryEventMessage(Long deliveryId, DeliveryEventEnum event, Rider rider, DeliveryExceptionInfo exceptionInfo, LocalDateTime eventTime) {
		Preconditions.checkNotNull(deliveryId, "deliveryId is null");
		Preconditions.checkNotNull(event, "event is null");
		Preconditions.checkNotNull(exceptionInfo, "exceptionInfo is null");
		Preconditions.checkNotNull(eventTime, "eventTime is null");

		this.deliveryId = deliveryId;
		this.event = event.getCode();
		this.rider = rider;
		this.exceptionType = exceptionInfo.getExceptionType().getCode();
		this.exceptionDescription = exceptionInfo.getExceptionDescription();
		this.eventTime = TimeUtil.toMilliSeconds(eventTime);
	}
}
