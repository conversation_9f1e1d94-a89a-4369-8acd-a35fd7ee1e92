package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.leaf;

import com.sankuai.inf.leaf.thrift.IDGen;
import com.sankuai.inf.leaf.thrift.Result;
import com.sankuai.inf.leaf.thrift.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.RetryTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public abstract class AbstractLeafService {
    @Resource(name = "deliveryIdGen")
    private  IDGen.Iface deliveryIdGen;
    private List<Long> batchGetLeafId(String leafKey, int batchNum){
        RetryTemplate retryTemplate = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getDeliveryLeafIdRetryCount(), MccConfigUtils.getDeliveryLeafIdRetryTime());
        List<Long> idList = new ArrayList<>();
        try {
            retryTemplate.execute(new RetryCallback<Void, Exception>() {
                @Override
                public Void doWithRetry(RetryContext retryContext) throws Exception {
                    List<Result> resultList = deliveryIdGen.getDefaultBatch(leafKey, batchNum - idList.size());
                    if (CollectionUtils.isEmpty(resultList)) {
                        throw new RuntimeException("get leaf result error");
                    }
                    resultList.forEach(w -> {
                        if (w.getStatus() != null && Status.SUCCESS.getValue() == w.getStatus().getValue() && w.getId()>0L) {
                            idList.add(w.getId());
                        }
                    });
                    if (idList.size() < batchNum) {
                        throw new RuntimeException("leaf should get again :" + idList.size());
                    }
                    return null;
                }
            });
        } catch (Exception e) {
            log.error("leaf id生成错误",e);
            throw new RuntimeException("leaf id生成错误");
        }
        return idList;
    }
    protected abstract String getLeafKey();
    public List<Long> batchGetLeafId(int batchNum) {
        if(batchNum<=100){
            return batchGetLeafId(getLeafKey(),batchNum);
        }
        List<Long> leafIdList = new ArrayList<>();
        int count = batchNum/100;
        int leftNum = batchNum%100;
        for (int i=0;i<count;i++){
            leafIdList.addAll(batchGetLeafId(getLeafKey(),100));
        }
        if(leftNum>0){
            leafIdList.addAll(batchGetLeafId(getLeafKey(),leftNum));
        }
        return leafIdList;
    }
}
