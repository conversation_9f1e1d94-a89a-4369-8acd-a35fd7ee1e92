package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache;

import com.dianping.squirrel.client.StoreClient;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.RetryTemplateUtil;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/2/26 14:52
 **/
@Slf4j
public abstract class DrunkHorseBusinessSquirrelOperateService {
    @Resource(name = "redisSgDrunkHorseBusiness")
    protected RedisStoreClient redisClient;

    protected RedisStoreClient getRedisClient(){
        return redisClient;
    }

    public abstract String getCategoryName();

    @MethodLog(logRequest = false, logResponse = true)
    public <T> boolean set(String key,T data) {
        if(StringUtils.isEmpty(key) || data==null || StringUtils.isEmpty(getCategoryName())){
            log.error("SquirrelOperateService.set param error . key:{},data:{},categoryName:{}",key,data,getCategoryName());
            return false;
        }
        try {

            Boolean result= RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(),MccConfigUtils.getSquirrelOperateRetryPeriod()).execute(new RetryCallback<Boolean, Exception>() {
                @Override
                public Boolean doWithRetry(RetryContext retryContext) throws Exception {
                    StoreKey storeKey = new StoreKey(getCategoryName(), key);
                    return getRedisClient().set(storeKey, JsonUtil.toJson(data));
                }
            });
            if(result!=null){
                return result;
            }
        }catch (Exception e){
            log.error("SquirrelOperateService.set error . key:{},data:{},categoryName:{}",key,data,getCategoryName(),e);
        }
        return false;
    }

    @MethodLog(logRequest = false, logResponse = true)
    public <T> Optional<T> get(String key, Class<T> clazz){
        if(StringUtils.isEmpty(key)){
            log.info("SquirrelOperateService.get key is empty ,key:{}",key);
            return Optional.empty();
        }

        try {
            StoreKey storeKey = new StoreKey(getCategoryName(), key);
            String result=RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(),MccConfigUtils.getSquirrelOperateRetryPeriod()).execute(new RetryCallback<String, Exception>() {
                @Override
                public String doWithRetry(RetryContext retryContext) throws Exception {
                    return getRedisClient().get(storeKey);
                }
            });
            if(StringUtils.isEmpty(result)){
                return Optional.empty();
            }
            T valObj=JsonUtil.fromJson(result,clazz);
            if(valObj==null){
                return Optional.empty();
            }
            return Optional.of(valObj);
        }catch (Exception e){
            log.info("SquirrelOperateService.get error ,key:{}",key,e);
        }
        return Optional.empty();
    }


    @MethodLog(logRequest = false, logResponse = true)
    public <T> Map<StoreKey, Optional<T>> multiGet(List<String> keys, Class<T> clazz) {
        if (CollectionUtils.isEmpty(keys)) {
            log.info("SquirrelOperateService.multiGet keys is empty ,keys:{}", keys);
            return Collections.emptyMap();
        }

        try {
            List<StoreKey> storeKeys = keys.stream().map(item -> new StoreKey(getCategoryName(), item)).collect(Collectors.toList());
            Map<StoreKey, String> keyStringMap = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(), MccConfigUtils.getSquirrelOperateRetryPeriod()).execute(new RetryCallback<Map<StoreKey, String>, Exception>() {
                @Override
                public Map<StoreKey, String> doWithRetry(RetryContext retryContext) {
                    return getRedisClient().multiGet(storeKeys);
                }
            });

            if (MapUtils.isEmpty(keyStringMap)) {
                return Collections.emptyMap();
            }

            return keyStringMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                    entry -> {

                        if (StringUtils.isBlank(entry.getValue())) {
                            return Optional.empty();
                        }

                        T valObj = JsonUtil.fromJson(entry.getValue(), clazz);

                        return Optional.ofNullable(valObj);
                    }, (k1, k2) -> k1));

        } catch (Exception e) {
            log.error("SquirrelOperateService.multiGet error ,keys:{}", keys, e);
            return Collections.emptyMap();
        }
    }

}

