package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryCallbackTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 配送进度异步更新指令消息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryUnifiedCallbackMessage {

	private Integer type;

	private String info;

	public DeliveryUnifiedCallbackMessage(DeliveryCallbackTypeEnum type, DeliveryUnifiedCallbackInfo info) {
		Preconditions.checkNotNull(type, "type is null");
		Preconditions.checkNotNull(info, "info is null");

		this.type = type.getCode();
		this.info = JsonUtil.toJson(info);
	}
}
