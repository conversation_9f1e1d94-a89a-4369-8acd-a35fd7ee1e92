package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.channel;

import com.dianping.cat.Cat;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DeliveryResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.ChannelDeliveryService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.DeliveryCheckResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.DeliveryLaunchResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.CoordinateUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ChannelStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.servicepackage.HaiKuiServicePackage;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.CommentUtils;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.DeliveryResultCodeEnum.SUCCESS;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum.LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/15
 */
@Slf4j
@Service
public class ChannelDeliveryServiceImpl implements ChannelDeliveryService {

	//配送类型--立即
	public static final int DELIVERY_ORDER_TYPE_INSTANT = 0;
	//配送类型--预约
	public static final int DELIVERY_ORDER_TYPE_SCHEDULE = 1;

	@Resource
	private ChannelDeliveryThriftService.Iface channelDeliveryThriftServiceClient;
	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;

	@Override
	@CatTransaction
	public DeliveryCheckResult checkDeliverable(OrderInfo orderInfo, ChannelStoreConfig channelStore, String servicePackageCode) {
		if(orderInfo.isBookingOrder() && channelStore.getDeliveryChannel().needSkipDeliveryCheckStep()){
			log.info("Skip check step by config");
			return new DeliveryCheckResult();
		}

		try {
			CheckDeliverableRequest request = new CheckDeliverableRequest()
					.setTenantId(orderInfo.getOrderKey().getTenantId())
					.setShopId(orderInfo.getOrderKey().getStoreId())
					.setDeliveryChannelId(channelStore.getDeliveryChannel().getCode())
					.setDeliveryServiceCode(servicePackageCode)
					.setReceiverAddress(orderInfo.getReceiver().getReceiverAddress().getAddressDetail())
					.setReceiverLongitude(Double.parseDouble(orderInfo.getReceiver().getReceiverAddress().getCoordinatePoint().getLongitude()))
					.setReceiverLatitude(Double.parseDouble(orderInfo.getReceiver().getReceiverAddress().getCoordinatePoint().getLatitude()))
					.setCoordinateType(orderInfo.getReceiver().getReceiverAddress().getCoordinateType().getCode())
					.setExpectDeliveryTime(TimeUtil.toMilliSeconds(orderInfo.getEstimatedDeliveryTime()));
			log.info("ChannelDeliveryThriftService.checkDeliverable begin, request={}", request);
			ResultStatus result = channelDeliveryThriftServiceClient.checkDeliverable(request);
			log.info("ChannelDeliveryThriftService.checkDeliverable finish, result={}", result);
			if (result == null) {
				return new DeliveryCheckResult(
						false,
						new DeliveryExceptionInfo(LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION, "调用渠道网关接口失败")
				);
			}

			if (result.getCode() == SUCCESS.getCode()) {
				return new DeliveryCheckResult();

			} else {
				DeliveryResultCodeEnum resultCode = Optional.ofNullable(DeliveryResultCodeEnum.enumOf(result.getCode()))
						.orElse(DeliveryResultCodeEnum.SYSTEM_ERROR);
				return new DeliveryCheckResult(
						resultCode.isCanRetry(),
						new DeliveryExceptionInfo(
								translateLaunchException(resultCode),
								translateLaunchExceptionMessage(resultCode, result.getMsg())
						)
				);
			}

		} catch (Exception e) {
			log.error("调用渠道网管检查是否可配送失败", e);
			return new DeliveryCheckResult(
					true,
					new DeliveryExceptionInfo(LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION, "调用渠道网关接口失败")
			);
		}
	}

	@Override
	@CatTransaction
	public DeliveryLaunchResult launchDelivery(DeliveryOrder deliveryOrder, SelfBuiltDeliveryPoi deliveryPoi, OrderInfo orderInfo) {
		try {
			CreateDeliveryRequest request = buildCreateDeliveryRequest(deliveryOrder, deliveryPoi, orderInfo);
			log.info("ChannelDeliveryThriftService.createDelivery begin, request={}", request);
			CreateDeliveryResponse response = channelDeliveryThriftServiceClient.createDelivery(request);
			log.info("ChannelDeliveryThriftService.createDelivery finish, response={}", response);
			if (response == null || response.getStatus() == null) {
				Cat.logEvent("LAUNCH_DELIVERY_" + deliveryOrder.getDeliveryChannel(), "FAIL_BY_NULL");
				return new DeliveryLaunchResult(
						false,
						new DeliveryExceptionInfo(LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION, "调用渠道网关接口失败")
				);
			}

			if (response.getStatus().getCode() == SUCCESS.getCode()) {
				Cat.logEvent("LAUNCH_DELIVERY_" + deliveryOrder.getDeliveryChannel(), "SUCCESS");
				return new DeliveryLaunchResult(response.getChannelDeliveryId());

			} else {
				Cat.logEvent("LAUNCH_DELIVERY_" + deliveryOrder.getDeliveryChannel(),
						"FAIL_BY_CODE_" + response.getStatus().getCode());
				DeliveryResultCodeEnum resultCode = Optional.ofNullable(DeliveryResultCodeEnum.enumOf(response.getStatus().getCode()))
						.orElse(DeliveryResultCodeEnum.SYSTEM_ERROR);
				return new DeliveryLaunchResult(
						resultCode.isCanRetry(),
						new DeliveryExceptionInfo(
								translateLaunchException(resultCode),
								translateLaunchExceptionMessage(resultCode, response.getStatus().getMsg())
						)
				);
			}

		} catch (QueryStoreAddressFailedException e) {
			Cat.logEvent("LAUNCH_DELIVERY_" + deliveryOrder.getDeliveryChannel(), "FAIL_BY_ADDRESS_QUERY");
			log.error("发起配送失败：查询门店地址失败", e);
			return new DeliveryLaunchResult(true, new DeliveryExceptionInfo(LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION, "通过配送渠道查询门店地址失败"));

		} catch (Exception e) {
			Cat.logEvent("LAUNCH_DELIVERY_" + deliveryOrder.getDeliveryChannel(), "ERROR");
			log.error("发起配送失败", e);
			return new DeliveryLaunchResult(false, new DeliveryExceptionInfo(LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION, "调用渠道网关接口失败"));
		}
	}

	private DeliveryExceptionTypeEnum translateLaunchException(DeliveryResultCodeEnum resultCode) {
		if (resultCode == null) {
			return LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION;
		}

		switch (resultCode) {
			case SUCCESS:
				return DeliveryExceptionTypeEnum.NO_EXCEPTION;

			case ORDER_POI_ERROR:
			case STORE_ERROR:
			case ORDER_STATUS_ERROR:
			case ORDER_TIME_ERROR:
			case ORDER_ERROR:
			case RIDER_ERROR:
				return DeliveryExceptionTypeEnum.LAUNCH_DELIVERY_FAILED_BY_MERCHANT;

			default:
				return LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION;
		}
	}

	private String translateLaunchExceptionMessage(DeliveryResultCodeEnum resultCode, String originalMessage) {
		if (resultCode == DeliveryResultCodeEnum.LIMITED_ERROR) {
			return FailureCodeEnum.INTERFACE_LIMITED.getMessage();
		}

		return originalMessage;
	}

	private CreateDeliveryRequest buildCreateDeliveryRequest(DeliveryOrder deliveryOrder, SelfBuiltDeliveryPoi deliveryPoi, OrderInfo orderInfo) {
		return new CreateDeliveryRequest()
				.setTenantId(deliveryOrder.getTenantId())
				.setShopId(deliveryOrder.getStoreId())
				.setDeliveryChannelId(deliveryOrder.getDeliveryChannel())
				.setDeliveryId(String.valueOf(getDeliveryId(deliveryOrder)))
				.setOrderId(getDeliveryOrderId(deliveryOrder))
				.setOrderBizType(deliveryOrder.getOrderBizType())
				.setChannelOrderId(deliveryOrder.getChannelOrderId())
				.setDeliveryServiceCode(deliveryOrder.getChannelServicePackageCode())
				.setReceiverName(deliveryOrder.getReceiver().getReceiverName())
				.setReceiverAddress(deliveryOrder.getReceiver().getReceiverAddress().getAddressDetail())
				.setReceiverPhone(deliveryOrder.getReceiver().getReceiverPhone())
				.setReceiverLongitude(Double.parseDouble(deliveryOrder.getReceiver().getReceiverAddress().getCoordinatePoint().getLongitude()))
				.setReceiverLatitude(Double.parseDouble(deliveryOrder.getReceiver().getReceiverAddress().getCoordinatePoint().getLatitude()))
				.setCoordinateType(deliveryOrder.getReceiver().getReceiverAddress().getCoordinateType().getCode())
				.setGoods(orderInfo.getGoodsList()
						.stream()
						.map(it -> new GoodsDTO(it.getName(), it.getQuantity(), it.getSinglePrice(), it.getSellUnit(), it.getSingleGoodsWeight()))
						.collect(Collectors.toList())
				)
				.setGoodsValue(orderInfo.getOriginalTotalAmount())
				.setGoodsWeight(orderInfo.getGoodsTotalWeight())
				.setExpectDeliveryTime(TimeUtil.toMilliSeconds(orderInfo.getEstimatedDeliveryTime()))
				.setOrderType(decideOrderType(deliveryOrder))
				.setShopSequence(orderInfo.getShopSequence())
				.setNote(CommentUtils.filterDefaultComment(orderInfo.getComments()))
				.setCashOnDelivery(0)
				.setCashOnPickup(0)
				.setInvoiceTitle(orderInfo.getInvoiceTitle())
				.setTransportInfo(buildTransportInfo(deliveryPoi, deliveryOrder.getDeliveryChannel()));
	}

	private int decideOrderType(DeliveryOrder deliveryOrder) {
		if (deliveryOrder.getEstimatedDeliveryTime().isBefore(LocalDateTime.now())) {
			return DELIVERY_ORDER_TYPE_INSTANT;
		}
		if (Objects.isNull(deliveryOrder.getDeliveryChannel())) {
			log.warn("decideOrderType, deliveryChannel is null");
			return DELIVERY_ORDER_TYPE_INSTANT;
		}

		DeliveryChannelEnum deliveryChannelEnum = DeliveryChannelEnum.valueOf(deliveryOrder.getDeliveryChannel());
		if (Objects.isNull(deliveryChannelEnum)) {
			log.warn("decideOrderType, deliveryChannelEnum is null");
			return DELIVERY_ORDER_TYPE_INSTANT;
		}

		switch (deliveryChannelEnum) {
			case HAI_KUI_DELIVERY:
				return HaiKuiServicePackage.getServicePackageByCode(deliveryOrder.getChannelServicePackageCode())
						.map(HaiKuiServicePackage::getExpectedDeliveryMinutes)
						.filter(it -> ChronoUnit.MINUTES.between(LocalDateTime.now(), deliveryOrder.getEstimatedDeliveryTime()) > it + 3)
						.map(it -> DELIVERY_ORDER_TYPE_SCHEDULE)
						.orElse(DELIVERY_ORDER_TYPE_INSTANT);


			case FENG_NIAO_DELIVERY:
				if (ChronoUnit.MINUTES.between(LocalDateTime.now(), deliveryOrder.getEstimatedDeliveryTime()) < 60) {
					return DELIVERY_ORDER_TYPE_INSTANT;
				} else {
					return DELIVERY_ORDER_TYPE_SCHEDULE;
				}

			default:
				return DELIVERY_ORDER_TYPE_INSTANT;
		}
	}

	private TransportInfo buildTransportInfo(SelfBuiltDeliveryPoi deliveryPoi, Integer deliveryChannel) {
		if (deliveryChannel != DeliveryChannelEnum.FENG_NIAO_DELIVERY.getCode()) {
			return null;
		}

		if (deliveryPoi.getStoreAddress() == null) {
			//需要查询门店地址和电话等信息
			try {
				QueryDeliveryStoreRequest request = new QueryDeliveryStoreRequest(deliveryPoi.getTenantId(), deliveryPoi.getStoreId(), deliveryChannel);
				log.info("ChannelDeliveryThriftService.queryChannelStore begin, request={}", request);
				QueryDeliveryStoreResponse response = channelDeliveryThriftServiceClient.queryChannelStore(request);
				log.info("ChannelDeliveryThriftService.queryChannelStore finish, response={}", response);
				if (response == null || response.getResultStatus() == null || response.getResultStatus().getCode() != SUCCESS.getCode()) {
					log.error("查询门店地址信息失败, response={}", response);
					throw new QueryStoreAddressFailedException("查询门店地址信息失败");
				}

				deliveryPoi.setStoreAddress(new Address(
						response.getShopAddress(),
						CoordinateTypeEnum.valueOf(response.getCoordinateType()),
						CoordinateUtil.translateToCoordinatePoint(response.getLongitude(), response.getLatitude())
				));
				deliveryPoi.setContactPhone(response.getContactPhone());
				deliveryPoiRepository.saveDeliveryPoi(deliveryPoi);

			} catch (Exception e) {
				log.error("查询门店地址信息失败", e);
				throw new QueryStoreAddressFailedException("查询门店地址信息失败", e);
			}
		}

		ChannelStoreConfig channelStoreConfig = deliveryPoi.getChannelStoreConfigMap().get(ThirdDeliveryChannelEnum.valueOf(deliveryChannel));
		return new TransportInfo(
				channelStoreConfig.getDeliveryChannelStoreName(),
				deliveryPoi.getStoreAddress().getAddressDetail(),
				Double.parseDouble(deliveryPoi.getStoreAddress().getCoordinatePoint().getLongitude()),
				Double.parseDouble(deliveryPoi.getStoreAddress().getCoordinatePoint().getLatitude()),
				deliveryPoi.getStoreAddress().getCoordinateType().getCode(),
				deliveryPoi.getContactPhone()
		);
	}

	private Long getDeliveryId(DeliveryOrder deliveryOrder) {
		if (deliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.HAI_KUI_DELIVERY.getCode()) {
			return deliveryOrder.getOrderId();
		}
		return deliveryOrder.getId();
	}

	public String getDeliveryOrderId(DeliveryOrder deliveryOrder) {
		if (Objects.isNull(deliveryOrder.getDeliveryChannel())) {
			log.warn("getDeliveryOrderId, deliveryChannel is null");
			return String.valueOf(deliveryOrder.getOrderId());
		}

		DeliveryChannelEnum deliveryChannelEnum = DeliveryChannelEnum.valueOf(deliveryOrder.getDeliveryChannel());
		if (Objects.isNull(deliveryChannelEnum)) {
			log.warn("getDeliveryOrderId, deliveryChannelEnum is null");
			return String.valueOf(deliveryOrder.getOrderId());
		}

		switch (deliveryChannelEnum) {
			case HAI_KUI_DELIVERY:
				return deliveryOrder.getChannelOrderId();

			case FENG_NIAO_DELIVERY:
				return deliveryOrder.getOrderId() + "#" + deliveryOrder.getId();

			default:
				return String.valueOf(deliveryOrder.getOrderId());
		}
	}

	private static class QueryStoreAddressFailedException extends RuntimeException {
		public QueryStoreAddressFailedException(String message) {
			super(message);
		}

		public QueryStoreAddressFailedException(String message, Throwable cause) {
			super(message, cause);
		}
	}
}
