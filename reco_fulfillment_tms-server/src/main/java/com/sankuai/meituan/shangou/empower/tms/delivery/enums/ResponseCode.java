package com.sankuai.meituan.shangou.empower.tms.delivery.enums;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ResponseCodeEnum;

public enum ResponseCode {

    FAILED(ResponseCodeEnum.FAILED.getValue(), "调用失败"),
    SUCCESS(ResponseCodeEnum.SUCCESS.getValue(), "操作成功"),
    SUCCESS_PARTITION(ResponseCodeEnum.SUCCESS_PARTITION.getValue(), "部分成功"),
    REQUEST_PARAMS_ERROR(ResponseCodeEnum.REQUEST_PARAMS_ERROR.getValue(), "请求参数非法"),
    THRIFT_SERVICE_ERROR(ResponseCodeEnum.THRIFT_SERVICE_ERROR.getValue(), "远程服务调用失败"),
    ORDER_SERVER_ERROR(ResponseCodeEnum.ORDER_SERVER_ERROR.getValue(), "不好意思出错了，请稍候再试"),
    CHANNEL_SERVER_FAIL(ResponseCodeEnum.CHANNEL_SERVER_FAIL.getValue(), "调用渠道服务失败"),
    RHINO_ERROR(ResponseCodeEnum.RHINO_ERROR.getValue(), "调用失败,接口降级"),
    UNKNOWN_ERROR(ResponseCodeEnum.UNKNOWN_ERROR.getValue(), "未知错误"),
    ANALYZE_EXCEL_ERROR(ResponseCodeEnum.ANALYZE_EXCEL_ERROR.getValue(),"解析excel异常"),
    DUP_DATA(ResponseCodeEnum.DUP_DATA.getValue(),"重复数据"),
    CHECK_BIZ_ERROR(ResponseCodeEnum.CHECK_BIZ_ERROR.getValue(),"业务校验失败");

    private int code;

    private String msg;

    ResponseCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Status generateStatus() {
        Status status = new Status();
        status.setCode(this.code);
        status.setMsg(this.msg);
        return status;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
