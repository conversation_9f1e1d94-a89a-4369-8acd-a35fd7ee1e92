package com.sankuai.meituan.shangou.empower.tms.delivery.utils;

import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/20
 */
@Deprecated
public class ChannelTypeTranslateUtil {

	public static ChannelType translateFromOrderBizType(Integer orderBizType) {
		if (orderBizType == null) {
			return null;
		}

		DynamicOrderBizType orderBizTypeEnum = DynamicOrderBizType.findOf(orderBizType);
		if (orderBizTypeEnum == null) {
			return null;
		}

		if (DynamicOrderBizType.MEITUAN_WAIMAI.equals(orderBizTypeEnum)) {
			return ChannelType.MEITUAN;
		}
		if (DynamicOrderBizType.ELE_ME.equals(orderBizTypeEnum)) {
			return ChannelType.ELEM;
		}
		if (DynamicOrderBizType.JING_DONG.equals(orderBizTypeEnum)) {
			return ChannelType.JD2HOME;
		}
		if (DynamicOrderBizType.YOU_ZAN_MIDDLE.equals(orderBizTypeEnum)) {
			return ChannelType.YOU_ZAN;
		}
		if (DynamicOrderBizType.MEITUAN_DRUNK_HOURSE.equals(orderBizTypeEnum)) {
			return ChannelType.MT_DRUNK_HORSE;
		}
		if (DynamicOrderBizType.QUAN_QIU_WA.equals(orderBizTypeEnum)) {
			return ChannelType.QUAN_QIU_WA;
		}
		if (DynamicOrderBizType.SELF_PLATFORM.equals(orderBizTypeEnum)) {
			return ChannelType.SELF_CHANNEL;
		}
		return null;
	}
}
