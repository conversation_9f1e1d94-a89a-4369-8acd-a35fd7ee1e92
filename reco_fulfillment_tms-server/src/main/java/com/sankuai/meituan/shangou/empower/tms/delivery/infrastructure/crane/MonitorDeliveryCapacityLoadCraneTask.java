package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.crane;

import com.cip.crane.client.ITaskHandler;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.cip.crane.netty.protocol.ScheduleTask;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.RiderDeliveryStatisticsApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/1/15 18:01
 **/
@Slf4j
@Component
public class MonitorDeliveryCapacityLoadCraneTask implements ITaskHandler{

    @Resource
    private RiderDeliveryStatisticsApplicationService riderDeliveryStatisticsApplicationService;

    @Override
    public void handleTask(ScheduleTask scheduleTask) {
        Long tenantId;
        try {
            tenantId = Long.parseLong(scheduleTask.getTaskItems().get(0));
        } catch (Exception e) {
            throw new IllegalArgumentException("租户id不合法");
        }

        log.info("start execute MonitorDeliveringCapacityLoadCraneTask, tenantId:{}" , tenantId);
        Set<Long> pushStoreSet = riderDeliveryStatisticsApplicationService.pushOrderOverloadMessage(tenantId);
        log.info("end execute MonitorDeliveringCapacityLoadCraneTask, tenantId:{}, pushStoreSet:{}" , tenantId, pushStoreSet);
    }
}
