package com.sankuai.meituan.shangou.empower.tms.delivery.model.deliveryconfig;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/3/4
 * desc: 发起配送配置Model
 */
@Data
public class LaunchDeliveryConfigModel {

    //配送发起点为商家接单 默认为发起配送点
    public Integer launchOnMerchantConfirm = 1;

    //配送发起点为商家接单的发配送延迟时间
    public Integer launchOnMerchantConfirmDelayTime = 0;

    //配送发起点为拣货完成
    public Integer launchOnPickupComplete = 0;

    //配送发起点为拣货完成的发配送延迟时间
    public Integer launchOnPickupCompleteDelayTime = 0;

    public LaunchDeliveryConfigModel() {
    }

    @JsonCreator
    public LaunchDeliveryConfigModel(@JsonProperty("launchOnMerchantConfirm") Integer launchOnMerchantConfirm,
                                     @JsonProperty("launchOnMerchantConfirmDelayTime") Integer launchOnMerchantConfirmDelayTime,
                                     @JsonProperty("launchOnPickupComplete") Integer launchOnPickupComplete,
                                     @JsonProperty("launchOnPickupCompleteDelayTime") Integer launchOnPickupCompleteDelayTime) {
        this.launchOnMerchantConfirm = launchOnMerchantConfirm;
        this.launchOnMerchantConfirmDelayTime = launchOnMerchantConfirmDelayTime;
        this.launchOnPickupComplete = launchOnPickupComplete;
        this.launchOnPickupCompleteDelayTime = launchOnPickupCompleteDelayTime;
    }
}
