package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer;

/**
 * 消息发送失败异常
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/4
 */
public class MessageSendFailedException extends RuntimeException {

	public MessageSendFailedException() {
	}

	public MessageSendFailedException(String message) {
		super(message);
	}

	public MessageSendFailedException(String message, Throwable cause) {
		super(message, cause);
	}

	public MessageSendFailedException(Throwable cause) {
		super(cause);
	}

	public MessageSendFailedException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}
}
