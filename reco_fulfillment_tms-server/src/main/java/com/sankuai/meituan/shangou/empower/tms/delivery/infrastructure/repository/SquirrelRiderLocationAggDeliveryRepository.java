package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.SquirrelNewSupplyRiderLocationRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.RiderLocationAggDeliverySquirrelOperationService;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 新供给聚合配送骑手坐标缓存
 * <AUTHOR>
 */
@Repository
@Slf4j
@Rhino
public class SquirrelRiderLocationAggDeliveryRepository implements SquirrelNewSupplyRiderLocationRepository {

    @Resource
    private RiderLocationAggDeliverySquirrelOperationService squirrelOperationService;

    @Override
    @Degrade(rhinoKey = "SquirrelRiderLocationAggDeliveryRepository-queryRiderLocation", fallBackMethod = "queryRiderLocationFallBack", timeoutInMilliseconds = 3000)
    public Optional<CoordinatePoint> queryRiderLocation(Long orderId) {
        return squirrelOperationService.get(String.valueOf(orderId), CoordinatePoint.class);
    }

    @Override
    @Degrade(rhinoKey = "SquirrelRiderLocationAggDeliveryRepository-saveRiderLocation", fallBackMethod = "saveRiderLocationFallBack", timeoutInMilliseconds = 3000)
    public boolean saveRiderLocation(Long orderId, CoordinatePoint coordinatePoint) {
        Integer expireDays = MccConfigUtils.getAggDeliveryRiderLocationExpireDays();
        String key = String.valueOf(orderId);
        return squirrelOperationService.set(key, coordinatePoint, TimeUtil.toSeconds(expireDays));
    }

    public Optional<CoordinatePoint> queryRiderLocationFallBack(Long orderId) {
        log.warn("SquirrelRiderLocationAggDeliveryRepository queryRiderLocationFallBack degrade");
        return Optional.empty();
    }

    boolean saveRiderLocationFallBack(Long orderId, CoordinatePoint coordinatePoint) {
        log.warn("SquirrelRiderLocationAggDeliveryRepository saveRiderLocationFallBack degrade");
        return false;
    }

}
