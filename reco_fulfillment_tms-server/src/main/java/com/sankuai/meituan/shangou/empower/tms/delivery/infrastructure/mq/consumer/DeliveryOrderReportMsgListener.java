package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;


import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils.ObjectMapperWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DeliveryOrderDtsMsgBody;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DeliveryStatusMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DtsMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.FulfillMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 消费delivery_order表的dts消息，构建订单履约看板信息
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeliveryOrderReportMsgListener extends AbstractEsDtsMsgListener<DeliveryOrderDtsMsgBody> {

    @Resource
    private MafkaMessageProducer<FulfillMsg> fulfillMsgProducer;

    @Resource
    protected ObjectMapperWrapper objectMapper;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.DELIVERY_ORDER_REPORT_CONSUMER;
    }

    @Override
    protected DtsMsg<DeliveryOrderDtsMsgBody> deserialize(Object msgBody) {
        return objectMapper.readValue(msgBody.toString(), new TypeReference<DtsMsg<DeliveryOrderDtsMsgBody>>() {
        });
    }

    @Override
    protected void checkMessageParameter(DeliveryOrderDtsMsgBody msgBody) {
        Objects.requireNonNull(msgBody.getOrderId(), "buildDeliveryOrderDoc, order id is null");
        Objects.requireNonNull(msgBody.getActiveStatus(), "buildDeliveryOrderDoc, active status is null");
        Objects.requireNonNull(msgBody.getCreateTime(), "buildDeliveryOrderDoc, createTime is null");
        Objects.requireNonNull(msgBody.getUpdateTime(), "buildDeliveryOrderDoc, updateTime is null");
        Objects.requireNonNull(msgBody.getStoreId(), "buildDeliveryOrderDoc, storeId is null");
        Objects.requireNonNull(msgBody.getDeliveryChannel(), "buildDeliveryOrderDoc, deliveryChannel is null");
    }

    @Override
    protected void onInsert(DeliveryOrderDtsMsgBody afterInsert) {
        aggAndUpsertDeliveryOrderEsPo(afterInsert);
    }

    @Override
    protected void onUpdate(DeliveryOrderDtsMsgBody afterUpdate, Map<String, Object> updatedField2OldValueMap) {
        aggAndUpsertDeliveryOrderEsPo(afterUpdate);
    }

    private void aggAndUpsertDeliveryOrderEsPo(DeliveryOrderDtsMsgBody msgBody) {
        FulfillMsg fulfillMsg = new FulfillMsg();
        fulfillMsg.setServiceType(2);
        fulfillMsg.setOrderId(msgBody.getOrderId());
        fulfillMsg.setTenantId(msgBody.getTenantId());
        DeliveryStatusMsg deliveryStatusMsg = new DeliveryStatusMsg();
        deliveryStatusMsg.setDeliveryExceptionCode(msgBody.getDeliveryExceptionCode());
        deliveryStatusMsg.setDeliveryStatus(msgBody.getDeliveryStatus());

        fulfillMsg.setDeliveryStatusMsg(deliveryStatusMsg);

        //发送exception——type 去订单履约看板
        fulfillMsgProducer.sendMessage(fulfillMsg, msgBody.getOrderId());
    }
}
