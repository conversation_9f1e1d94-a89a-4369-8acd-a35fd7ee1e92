package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 歪马运单状态检查消息体.
 *
 * <AUTHOR>
 * @since 2021/11/23 21:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DHDeliveryStatusCheckMessage {

    /**
     * 租户 ID
     */
    private Long tenantId;

    /**
     * 门店 ID
     */
    private Long storeId;

    /**
     * 赋能订单 ID
     */
    private Long orderId;

    /**
     * 期望的运单状态，参考 com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum
     */
    private Integer expectedStatus;

}
