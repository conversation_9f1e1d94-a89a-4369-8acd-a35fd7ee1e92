package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import java.util.*;

import javax.annotation.Resource;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.DeliveryRemindConfigDOExMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryRemindConfigDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRemindConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRemindConfigDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfigRepository;

/**
 * 配送提醒配置仓库的 MySQL 实现.
 *
 * <AUTHOR>
 * @since 2021/10/8 17:24
 */
@Slf4j
@Repository
public class MySQLDeliveryRemindConfigRepository implements DeliveryRemindConfigRepository {

    private static final Long DELETE_TIME_VALID = 0L;

    /**
     * 配送超时提醒依赖的企业id
     */
    @Resource
    private Long dhCid;
    @Resource
    private Long meituanCid;

    @Resource
    private DeliveryRemindConfigDOMapper deliveryRemindConfigDOMapper;

    @Resource
    private DeliveryRemindConfigDOExMapper deliveryRemindConfigDOExMapper;

    /**
     * 查询门店的配送提醒配置.
     *
     * @param tenantId 租户 ID
     * @param storeId  门店 ID
     * @return 配送提醒配置
     */
    @Override
    public Optional<DeliveryRemindConfig> queryDeliveryRemindConfig(long tenantId, long storeId) {
        DeliveryRemindConfigDOExample example = new DeliveryRemindConfigDOExample();
        example.createCriteria().andTenantIdEqualTo(tenantId)
                .andStoreIdEqualTo(storeId)
                .andDeleteTimeEqualTo(DELETE_TIME_VALID);
        List<DeliveryRemindConfigDO> deliveryRemindConfigDos = deliveryRemindConfigDOMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(deliveryRemindConfigDos)) {
            return Optional.empty();
        }
        DeliveryRemindConfigDO configDo = deliveryRemindConfigDos.get(0);
        return translate(configDo);
    }

    private Optional<DeliveryRemindConfig> translate(DeliveryRemindConfigDO configDo) {
        if (configDo == null) {
            return Optional.empty();
        }
        // configDo.getEnterpriseRecipients() 为空时map为null
        Map<String, List<String>> enterpriseRecipientsMap = JsonUtil.fromJson(
                configDo.getEnterpriseRecipients(), new TypeReference<Map<String, List<String>>>() {});

        DeliveryRemindConfig deliveryRemindConfig = new DeliveryRemindConfig(
                configDo.getId(),
                configDo.getTenantId(),
                configDo.getStoreId(),
                Optional.ofNullable(enterpriseRecipientsMap).map(map -> {
                   return map.getOrDefault(meituanCid.toString(), Collections.emptyList());
                }).orElse(Collections.emptyList()),
                Objects.equals(DELETE_TIME_VALID, configDo.getDeleteTime()),
                configDo.getCreateTime(),
                Optional.ofNullable(enterpriseRecipientsMap).map(map -> {
                    return map.getOrDefault(dhCid.toString(), Collections.emptyList());
                }).orElse(Collections.emptyList())
        );

        return Optional.of(deliveryRemindConfig);
    }

    /**
     * 保存门店的配送提醒配置.
     *
     * @param remindConfig 配送提醒配置
     */
    @Override
    public void saveDeliveryRemindConfig(DeliveryRemindConfig remindConfig) {
        Preconditions.checkNotNull(remindConfig, "deliveryRemindConfig is null");
        DeliveryRemindConfigDO remindConfigDo = translate(remindConfig);
        if (Objects.nonNull(remindConfig.getId())) {
            deliveryRemindConfigDOMapper.updateByPrimaryKeySelective(remindConfigDo);
        } else {
            deliveryRemindConfigDOExMapper.insertSelective(remindConfigDo);
            remindConfig.setId(remindConfigDo.getId());
        }
    }

    private DeliveryRemindConfigDO translate(DeliveryRemindConfig remindConfig) {
        DeliveryRemindConfigDO remindConfigDo = new DeliveryRemindConfigDO();
        remindConfigDo.setId(remindConfig.getId());
        remindConfigDo.setTenantId(remindConfig.getTenantId());
        remindConfigDo.setStoreId(remindConfig.getStoreId());
        remindConfigDo.setCreateTime(remindConfig.getCreateTime());
        remindConfigDo.setDeleteTime(remindConfig.getIsValid() ? DELETE_TIME_VALID : System.currentTimeMillis());

        Map<String, List<String>> enterpriseRecipientsMap = new HashMap<>(2);
        // 增加美团企业的mis，参考 https://km.sankuai.com/page/1301871363
        enterpriseRecipientsMap.put(meituanCid.toString(), remindConfig.getRecipients());

        // 增加歪马企业的mis
        enterpriseRecipientsMap.put(dhCid.toString(), remindConfig.getDhRecipients());

        String enterpriseRecipientsString = JsonUtil.toJson(enterpriseRecipientsMap);
        log.debug("enterpriseRecipientsString: {}", enterpriseRecipientsString);
        remindConfigDo.setEnterpriseRecipients(enterpriseRecipientsString);

        return remindConfigDo;
    }
}
