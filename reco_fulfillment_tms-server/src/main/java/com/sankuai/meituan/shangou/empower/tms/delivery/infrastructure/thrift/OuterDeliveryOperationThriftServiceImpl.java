package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryOperationApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.ConvertUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.OuterDeliveryOperationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.OuterAggregationLaunchRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.OuterDeliveryLaunchRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.DeliveryConfigUpdateTimeRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryLaunchResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryConfigUpdateTimeResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryConfigUpdateTimeDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.StoreConfigDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreConfigDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.TransferOrderMarkEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.ProxyDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum.DELIVERY_PLATFORM_ACCEPT_DELIVERY_ORDER;

@Slf4j
@Service
public class OuterDeliveryOperationThriftServiceImpl implements OuterDeliveryOperationThriftService {

    @Autowired
    private OrderSystemClient orderSystemClient;

    @Autowired
    private ProxyDeliveryPlatformClient deliveryPlatformClient;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    @Resource
    private DeliveryOperationApplicationService deliveryOperationApplicationService;

    @Resource
    private LaunchDeliveryDomainService launchDeliveryDomainService;

    @Resource
    private StoreConfigDOMapper storeConfigDOMapper;

    @Resource
    private DeliveryTimeOutCheckService deliveryTimeOutCheckService;

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public DeliveryLaunchResponse launchDelivery(OuterDeliveryLaunchRequest request) {
        if(request==null || StringUtils.isEmpty(request.getChannelOrderId())){
            return new DeliveryLaunchResponse(new Status(ResponseCodeEnum.REQUEST_PARAMS_ERROR.getValue(),"参数错误"));
        }
        Result<OrderInfo> orderInfoResult = orderSystemClient.queryByViewOrderId(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(),request.getChannelOrderId(),false);
        OrderInfo orderInfo=orderInfoResult.getInfo();
        if(orderInfo==null){
            return new DeliveryLaunchResponse(new Status(ResponseCodeEnum.ORDER_SERVER_ERROR.getValue(),"获取订单信息错误"));
        }


        Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId());
        if (!opDeliveryPoi.isPresent() || (DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM != opDeliveryPoi.get().getDeliveryPlatform() && DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM != opDeliveryPoi.get().getDeliveryPlatform())) {
            log.error("DeliveryStore[{}] is illegal, will give up launchDelivery", orderInfo.getOrderKey().getStoreId());
            return new DeliveryLaunchResponse(new Status(ResponseCodeEnum.FAILED.getValue(),"未开通麦芽田配送"));
        }

        Optional<DeliveryOrder> optionalDeliveryOrder = Optional.empty();
        if(MccConfigUtils.getDeliveryQueryTenantSwitch(opDeliveryPoi.get().getTenantId())){
            optionalDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderWithTenant(orderInfo.getOrderKey().getOrderId(),opDeliveryPoi.get().getTenantId(),opDeliveryPoi.get().getStoreId());
        }else {
            optionalDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(orderInfo.getOrderKey().getOrderId());
        }

        DeliveryOrder deliveryOrder;
        if(optionalDeliveryOrder.isPresent()){
            deliveryOrder=optionalDeliveryOrder.get();
            if(DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode() == deliveryOrder.getDeliveryChannel()){
                // 将原来的配送单取消
                deliveryOrder.onStatusChange(DeliveryStatusEnum.DELIVERY_CANCELLED, LocalDateTime.now());
                deliveryOrderRepository.saveDeliveryOrder(deliveryOrder);
            }
        }
        orderInfo.setSelfDelivery(true);
        DeliveryChannelEnum channelEnum = DeliveryChannelEnum.MALT_FARM;
        if(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM == opDeliveryPoi.get().getDeliveryPlatform()){
            channelEnum = DeliveryChannelEnum.DAP_DELIVERY;
        }

        // 发起新的配送单
        deliveryOrder = DeliveryOrder.fromBusinessCustomer(orderInfo, channelEnum.getCode(), StringUtils.EMPTY);
        log.info("Launch a new deliveryOrder: {}", deliveryOrder);
        deliveryOrderRepository.saveDeliveryOrder(deliveryOrder);


        Optional<LaunchFailure> failureOptional=deliveryPlatformClient.launch(opDeliveryPoi.get(),orderInfo,deliveryOrder, TransferOrderMarkEnum.NORMAL_ORDER.getCode());
        //监控配送超时
        deliveryTimeOutCheckService.triggerDeliveryTimeOutCheck(deliveryOrder);
        if(failureOptional.isPresent()){
            return new DeliveryLaunchResponse(new Status(ResponseCodeEnum.FAILED.getValue(),failureOptional.get().getFailureMessage()));
        }
        return new DeliveryLaunchResponse(Status.SUCCESS);
    }


    /**
     * 暂时是迁移订单使用
     *
     * @param request
     * @return
     */
    @Override
    public DeliveryLaunchResponse launchAggregationDelivery(OuterAggregationLaunchRequest request) {
        DeliveryLaunchResponse deliveryLaunchResponse = new DeliveryLaunchResponse();
        deliveryLaunchResponse.setStatus(Status.SUCCESS);
        try {
            OrderKey orderKey = new OrderKey(request.getTenantId(), request.getShopId(), request.getOrderId());
            // 查询订单信息，并获知订单是否为预约单
            Result<OrderInfo> orderInfoQueryResult = orderSystemClient.getOrderInfo(orderKey, false);
            if (orderInfoQueryResult.isFail()) {
                log.warn("查询订单[{}]失败，等待重试. failure:{}", orderKey, orderInfoQueryResult.getFailure());
                Status status = new Status(ResponseCodeEnum.FAILED.getValue(), "查询订单失败");
                deliveryLaunchResponse.setStatus(status);
                return deliveryLaunchResponse;
            }
            OrderInfo orderInfo = orderInfoQueryResult.getInfo();

            Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(orderKey.getTenantId(), orderKey.getStoreId(), ConvertUtil.orderBizToChannelType(OrderBizTypeEnum.enumOf(orderInfo.getOrderBizType())).getValue());

            if (!opDeliveryPoi.isPresent()) {
                log.warn("门店[{}]未开通任何配送平台, 将会放弃发起配送.", orderKey.getStoreId());
                return deliveryLaunchResponse;
            }
            DeliveryPoi deliveryPoi = opDeliveryPoi.get();

            // 判断是否非自动发起
            if (deliveryPoi.getDeliveryLaunchType() != DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY) {
                log.info("门店[{}]配置为非自动发起，因此放弃自动发起配送", orderKey.getStoreId());
                return deliveryLaunchResponse;
            }

            //判断是否已经在配送中
            if (orderInfo.isInDelivery()) {
                log.info("订单[{}]已经在配送中，因此放弃自动发起配送", orderKey.getOrderId());
                return deliveryLaunchResponse;
            }

            deliveryOperationApplicationService.launchDelivery(deliveryPoi, orderInfo, false, PlatformSourceEnum.OMS,null);

            return deliveryLaunchResponse;
        } catch (Exception e) {
            Status status = new Status(ResponseCodeEnum.FAILED.getValue(), "发配送报错");
            deliveryLaunchResponse.setStatus(status);
            return deliveryLaunchResponse;
        }

    }


    /**
     * 暂时是迁移订单使用，补自配送运单
     *
     * @param request
     * @return
     */
    @Override
    public DeliveryLaunchResponse launchMerchantSelfDelivery(OuterAggregationLaunchRequest request) {
        DeliveryLaunchResponse deliveryLaunchResponse = new DeliveryLaunchResponse();
        deliveryLaunchResponse.setStatus(Status.SUCCESS);
        try {
            OrderKey orderKey = new OrderKey(request.getTenantId(), request.getShopId(), request.getOrderId());
            // 查询订单信息，并获知订单是否为预约单
            Result<OrderInfo> orderInfoQueryResult = orderSystemClient.getOrderInfo(orderKey, false);
            if (orderInfoQueryResult.isFail()) {
                log.warn("查询订单[{}]失败，等待重试. failure:{}", orderKey, orderInfoQueryResult.getFailure());
                Status status = new Status(ResponseCodeEnum.FAILED.getValue(), "查询订单失败");
                deliveryLaunchResponse.setStatus(status);
                return deliveryLaunchResponse;
            }
            OrderInfo orderInfo = orderInfoQueryResult.getInfo();

            Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(orderKey.getTenantId(), orderKey.getStoreId(), ConvertUtil.orderBizToChannelType(OrderBizTypeEnum.enumOf(orderInfo.getOrderBizType())).getValue());

            if (!opDeliveryPoi.isPresent()) {
                log.warn("门店[{}]未开通任何配送平台, 将会放弃发起配送.", orderKey.getStoreId());
                return deliveryLaunchResponse;
            }
            DeliveryPoi deliveryPoi = opDeliveryPoi.get();

            // 判断是否非自动发起
            if (deliveryPoi.getDeliveryLaunchType() != DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY) {
                log.info("门店[{}]配置为非自动发起，因此放弃自动发起配送", orderKey.getStoreId());
                return deliveryLaunchResponse;
            }

            if (orderInfo.isNewSupplyStoreDelivery() || orderInfo.isFinished()) {
                log.info("订单[{}]已到终态，将放弃创建运单", orderInfo.getOrderKey());
                return deliveryLaunchResponse;
            }

            //查当前是否有激活的运单，尝试幂等处理
            Optional<DeliveryOrder> activeDeliveryOrder = Optional.empty();
            if(MccConfigUtils.getDeliveryQueryTenantSwitch(deliveryPoi.getTenantId())){
                activeDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderWithTenant(orderInfo.getOrderKey().getOrderId(),deliveryPoi.getTenantId(),deliveryPoi.getStoreId());
            }else {
                activeDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(orderInfo.getOrderKey().getOrderId());
            }
            if (activeDeliveryOrder.isPresent()) {
                return deliveryLaunchResponse;
            }

            //创建新运单并激活
            DeliveryOrder deliveryOrder = new DeliveryOrder(orderInfo, DeliveryChannelEnum.MERCHANT_DELIVERY.getCode(), StringUtils.EMPTY);
            deliveryOrder.activate();
            final LocalDateTime now = LocalDateTime.now();
            deliveryOrder.onStatusChangeWithOutLog(DELIVERY_PLATFORM_ACCEPT_DELIVERY_ORDER.getTargetStatus(), now);

            deliveryOrderRepository.save(deliveryOrder);

            //保存成功后在发日志
            deliveryOrder.recordDeliveryChangeLog(deliveryOrder.getId(), DeliveryEventEnum.getEventByStatus(DELIVERY_PLATFORM_ACCEPT_DELIVERY_ORDER.getTargetStatus()), null, now);
            return deliveryLaunchResponse;
        } catch (Exception e) {
            Status status = new Status(ResponseCodeEnum.FAILED.getValue(), "发配送报错");
            deliveryLaunchResponse.setStatus(status);
            return deliveryLaunchResponse;
        }

    }

    @Override
    public DeliveryLaunchResponse launchMerchantSelfDeliveryDemo(OuterAggregationLaunchRequest request) {
        DeliveryLaunchResponse deliveryLaunchResponse = new DeliveryLaunchResponse();
        deliveryLaunchResponse.setStatus(Status.SUCCESS);
        try {
            OrderKey orderKey = new OrderKey(request.getTenantId(), request.getShopId(), request.getOrderId());
            // 查询订单信息，并获知订单是否为预约单
            Result<OrderInfo> orderInfoQueryResult = orderSystemClient.getOrderInfo(orderKey, false);
            if (orderInfoQueryResult.isFail()) {
                log.warn("查询订单[{}]失败，等待重试. failure:{}", orderKey, orderInfoQueryResult.getFailure());
                Status status = new Status(ResponseCodeEnum.FAILED.getValue(), "查询订单失败");
                deliveryLaunchResponse.setStatus(status);
                return deliveryLaunchResponse;
            }
            OrderInfo orderInfo = orderInfoQueryResult.getInfo();

            Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(
                    orderKey.getTenantId(), orderKey.getStoreId(),
                    ConvertUtil.orderBizToChannelType(OrderBizTypeEnum.enumOf(orderInfo.getOrderBizType())).getValue());

            if (!opDeliveryPoi.isPresent()) {
                log.warn("门店[{}]未开通任何配送平台, 将会放弃发起配送.", orderKey.getStoreId());
                return deliveryLaunchResponse;
            }
            DeliveryPoi deliveryPoi = opDeliveryPoi.get();

            // 判断是否非自动发起
            if (deliveryPoi.getDeliveryLaunchType() != DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY) {
                log.info("门店[{}]配置为非自动发起，因此放弃自动发起配送", orderKey.getStoreId());
                return deliveryLaunchResponse;
            }

            // 删除订单状态判断
//            if (orderInfo.isNewSupplyStoreDelivery() || orderInfo.isFinished()) {
//                log.info("订单[{}]已到终态，将放弃创建运单", orderInfo.getOrderKey());
//                return deliveryLaunchResponse;
//            }

            // 查当前是否有激活的运单，尝试幂等处理
            Optional<DeliveryOrder> activeDeliveryOrder = Optional.empty();
            if(MccConfigUtils.getDeliveryQueryTenantSwitch(deliveryPoi.getTenantId())){
                activeDeliveryOrder = deliveryOrderRepository
                        .getActiveDeliveryOrderForceMaster(orderInfo.getOrderKey().getOrderId());
            }else {
                activeDeliveryOrder = deliveryOrderRepository
                        .getActiveDeliveryOrderWithTenant(orderInfo.getOrderKey().getOrderId(),deliveryPoi.getTenantId(),deliveryPoi.getStoreId());
            }

            if (activeDeliveryOrder.isPresent()) {
                return deliveryLaunchResponse;
            }

            // 创建新运单并激活
            DeliveryOrder deliveryOrder = new DeliveryOrder(orderInfo, DeliveryChannelEnum.MERCHANT_DELIVERY.getCode(),
                    StringUtils.EMPTY);
            deliveryOrder.activate();
            final LocalDateTime now = LocalDateTime.now();

            deliveryOrder.onStatusChangeWithOutLog(DELIVERY_PLATFORM_ACCEPT_DELIVERY_ORDER.getTargetStatus(),
                    now);
            deliveryOrderRepository.save(deliveryOrder);
            //保存成功后在发日志
            deliveryOrder.recordDeliveryChangeLog(deliveryOrder.getId(), DeliveryEventEnum.getEventByStatus(DELIVERY_PLATFORM_ACCEPT_DELIVERY_ORDER.getTargetStatus()), null, now);

            return deliveryLaunchResponse;
        } catch (Exception e) {
            Status status = new Status(ResponseCodeEnum.FAILED.getValue(), "发配送报错");
            deliveryLaunchResponse.setStatus(status);
            return deliveryLaunchResponse;
        }
    }

    @Override
    public DeliveryConfigUpdateTimeResponse queryDeliveryConfigUpdateTime(DeliveryConfigUpdateTimeRequest updateTimeRequest) {
        DeliveryConfigUpdateTimeResponse updateTimeConfigResponse = new DeliveryConfigUpdateTimeResponse();
        updateTimeConfigResponse.setStatus(Status.SUCCESS);
        try {
            StoreConfigDOExample example = new StoreConfigDOExample();
            example.createCriteria()
                    .andTenantIdEqualTo(updateTimeRequest.getTenantId())
                    .andStoreIdEqualTo(updateTimeRequest.getStoreId());
            List<StoreConfigDO> records = storeConfigDOMapper.selectByExample(example);

            DeliveryConfigUpdateTimeDto updateTimeResponse = new DeliveryConfigUpdateTimeDto();
            updateTimeConfigResponse.setData(updateTimeResponse);
            if (CollectionUtils.isNotEmpty(records)) {
                final Optional<Long> max = records.stream()
                        .map(item -> item.getUpdateTime().atZone(ZoneId.systemDefault()).toEpochSecond())
                        .max(Comparator.comparingLong(Long::valueOf));
                //返回秒纬度时间
                updateTimeResponse.setUpdateTime(max.orElse(null));
            }

            return updateTimeConfigResponse;

        } catch (Exception e) {
            Status status = new Status(ResponseCodeEnum.FAILED.getValue(), "计算更新时间报错");
            updateTimeConfigResponse.setStatus(status);
            return updateTimeConfigResponse;
        }
    }
}
