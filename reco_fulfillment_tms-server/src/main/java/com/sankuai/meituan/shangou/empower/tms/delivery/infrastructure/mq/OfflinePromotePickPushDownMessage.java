package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-08-04
 * @email <EMAIL>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OfflinePromotePickPushDownMessage {

    /**
     * 赋能租户ID
     */
    private Long tenantId;

    /**
     * 赋能门店ID
     */
    private Long storeId;

    /**
     * 赋能订单id
     */
    private Long orderId;


    /**
     * 更新时间，毫秒时间戳
     */
    private Long updateTime;

    private int times = 0;
}
