package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message;

import com.sankuai.qnh.ofc.ofw.common.consts.FulfillmentOrderMessageTypeEnum;
import lombok.Data;

@Data
public class FulfillmentOrderTransferMessage {
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 订单仓id
     */
    private Long warehouseId;
    /**
     * 履约仓id
     */
    private Long fulfillmentWarehouseId;
    /**
     * 预留字段，后续锁定库存拆分到ofc后、下游根据当前字段过滤
     */
    private Integer handleStock;

    /**
     * 转单编号
     */
    private Integer fulfillOrderSeq;

    /**
     * 履约仓类型
     */
    private Integer fulfillmentWarehouseType;

    /**
     * 消息触发时间
     */
    private Long timeStamp;

    /**
     * 订单对应的牵牛花门店id
     */
    private Long orderShopId;

    /**
     * 仓门店id
     */
    private Long warehousePoiId;

    /**
     * 履约消息类型
     *
     * @see FulfillmentOrderMessageTypeEnum
     */
    private Integer fulfillmentOrderType;
}
