package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;


import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DeliveryStatusMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.FulfillMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * 数据补偿消息消费，用于补偿数据，消息消费延迟2S
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeliveryOrderReportCompensateMsgListener extends AbstractDeadLetterConsumer {


    @Resource
    private MafkaMessageProducer<FulfillMsg> fulfillMsgProducer;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;


    private void sendMsg(DeliveryOrder deliveryOrder) {
        FulfillMsg fulfillMsg = new FulfillMsg();
        fulfillMsg.setServiceType(2);
        fulfillMsg.setOrderId(deliveryOrder.getOrderId());
        fulfillMsg.setTenantId(deliveryOrder.getTenantId());
        DeliveryStatusMsg deliveryStatusMsg = new DeliveryStatusMsg();
        deliveryStatusMsg.setDeliveryExceptionCode(deliveryOrder.getDeliveryExceptionCode());
        deliveryStatusMsg.setDeliveryStatus(deliveryOrder.getStatus().getCode());

        fulfillMsg.setDeliveryStatusMsg(deliveryStatusMsg);

        //发送exception——type 去订单履约看板
        fulfillMsgProducer.sendMessage(fulfillMsg, deliveryOrder.getOrderId());
    }

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.DELIVERY_ORDER_REPORT_COMPENSATE_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage message) {
        //先查询
        FulfillMsg fulfillMsg = translateMessage(message);
        if (message == null || fulfillMsg.getServiceType() == null) {
            return CONSUME_SUCCESS;
        }

        if (fulfillMsg.getServiceType() == 4) {
            final Optional<DeliveryOrder> activeDeliveryOrderForceMaster = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(fulfillMsg.getOrderId());
            activeDeliveryOrderForceMaster.ifPresent(this::sendMsg);
        }

        return CONSUME_SUCCESS;
    }

    public FulfillMsg translateMessage(MafkaMessage message) {
        try {
            FulfillMsg outMessage = super.translateMessage(message,
                    new TypeReference<FulfillMsg>() {
                    });
            return outMessage;
        } catch (Exception e) {
            log.error("解析补偿信息错误:{}", message);
            return null;
        }
    }
}
