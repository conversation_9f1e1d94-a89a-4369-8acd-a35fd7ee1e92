package com.sankuai.meituan.shangou.empower.tms.delivery.enums;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CancelTypeEnum;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/4/14
 * @email jianglilin02@meituan
 */

public enum FarmCancelTypeEnum {

    MERCHANT_CANCEL(1, "商户取消"),
    DELIVERY_CHANNEL_CANCEL(2, "配送商取消"),
    SYSTEM_CANCEL(4, "系统取消");
    private int code;

    private String msg;

    FarmCancelTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static CancelTypeEnum mapToCancelType(int value) {
        FarmCancelTypeEnum farmCancelTypeEnum = enumOf(value);
        if (Objects.isNull(farmCancelTypeEnum)) {
            return CancelTypeEnum.UNKNOWN_CANCEL;
        }
        switch (farmCancelTypeEnum) {
            case MERCHANT_CANCEL:
                return CancelTypeEnum.ACTIVE_CANCEL;
            case DELIVERY_CHANNEL_CANCEL:
                return CancelTypeEnum.PASSIVE_CANCEL;
            case SYSTEM_CANCEL:
            default:
                return CancelTypeEnum.UNKNOWN_CANCEL;
        }
    }


    public static FarmCancelTypeEnum enumOf(int value) {
        for (FarmCancelTypeEnum each : values()) {
            if (each.getCode() == value) {
                return each;
            }
        }

        return null;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
