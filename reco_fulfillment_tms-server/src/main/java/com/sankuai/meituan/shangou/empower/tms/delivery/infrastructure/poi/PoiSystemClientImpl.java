package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.poi;

import com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiDetailInfoResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.poi.PoiSystemClient;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-07-23
 * @email <EMAIL>
 */
@Slf4j
@Service
public class PoiSystemClientImpl implements PoiSystemClient {

    @Resource
    private ChannelPoiManageThriftService channelPoiManageThriftService;

    @Override
    public ChannelPoiInfoDTO queryChannelPoiInfoDTO(long tenantId, long warehouseId, int channelId) {
        try {
            PoiDetailInfoResponse response = channelPoiManageThriftService.queryPoiDetailInfoByPoiId(tenantId, warehouseId);
            if (!response.getStatus().isSuccess() || Objects.isNull(response.getPoiDetailInfoDTO()) || CollectionUtils.isEmpty(response.getPoiDetailInfoDTO().getChannelPoiInfoList())) {
                log.error("channelPoiManageThriftService.queryPoiDetailInfoByPoiId return error, response = {}", response);
                throw new ThirdPartyException("查询渠道门店信息信息错误");
            }
            List<ChannelPoiInfoDTO> channelPoiInfoDTOS = IListUtils.nullSafeFilterElement(
                    response.getPoiDetailInfoDTO().getChannelPoiInfoList(),
                    channelPoiInfoDTO -> Objects.equals(channelPoiInfoDTO.getChannelId(), channelId)
            );
            return CollectionUtils.isNotEmpty(channelPoiInfoDTOS) ? channelPoiInfoDTOS.get(0) : response.getPoiDetailInfoDTO().getChannelPoiInfoList().get(0);
        } catch (Exception e) {
            log.error("invoke channelPoiManageThriftService.queryPoiDetailInfoByPoiId error", e);
            throw new ThirdPartyException("查询渠道门店信息信息错误", e);
        }
    }

}
