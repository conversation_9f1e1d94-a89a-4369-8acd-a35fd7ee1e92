package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OFCSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.OFCDeliveryOperationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.OFCDeliveryCancelRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryCancelResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.ProxyDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.SpringContextUtils;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Optional;

@Slf4j
@Service
public class OFCDeliveryOperationThriftServiceImpl implements OFCDeliveryOperationThriftService {

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    @Autowired
    private ProxyDeliveryPlatformClient deliveryPlatformClient;

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    public DeliveryCancelResponse cancelDelivery(OFCDeliveryCancelRequest request) {
        if(request==null || request.getOrderId() == null){
            return new DeliveryCancelResponse(new Status(ResponseCodeEnum.REQUEST_PARAMS_ERROR.getValue(),"参数错误"));
        }
        Optional<DeliveryOrder> optionalDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(request.getOrderId(),request.getFulfillmentOrderId(),request.getTenantId(),request.getWarehouseId());
        if(!optionalDeliveryOrder.isPresent()){
            log.info("未查到运单:{}",request.getOrderId());
            OFCSystemClient ofcSystemClient = SpringContextUtils.getBean(OFCSystemClient.class);
            ofcSystemClient.syncDeliveryChangeToSystem(request.getTenantId(),request.getWarehouseId(),request.getOrderId(),request.getFulfillmentOrderId(), LocalDateTime.now());
            return new DeliveryCancelResponse(Status.SUCCESS);
        }
        DeliveryOrder deliveryOrder = optionalDeliveryOrder.get();
        Optional<Failure> failure = deliveryOrder.cancelDeliveryForOFC(true);
        if(failure.isPresent()){
            return new DeliveryCancelResponse(new Status(ResponseCodeEnum.FAILED.getValue(),"取消失败"));
        }
        return new DeliveryCancelResponse(Status.SUCCESS);
    }
}
