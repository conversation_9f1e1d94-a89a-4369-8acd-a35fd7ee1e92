package com.sankuai.meituan.shangou.empower.tms.delivery.exception;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/10/12
 * desc: 配送基础异常
 */
@Data
public class DeliveryBaseException extends RuntimeException {

    private String msg;

    private Integer code;

    public DeliveryBaseException(DeliveryError error) {
        super(error.getMsg());
        this.code = error.getCode();
        this.msg = error.getMsg();
    }

    public DeliveryBaseException(Integer code, String msg) {
        super(msg);

        this.code = code;
        this.msg = msg;
    }

    public DeliveryBaseException(String msg) {
        super(msg);

        this.code = -1;
        this.msg = msg;
    }
}
