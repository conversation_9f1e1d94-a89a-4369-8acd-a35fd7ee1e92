package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryTimeOutCheckTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryTimeOutCheckMessage {

	private DeliveryTimeOutCheckTypeEnum deliveryTimeOutCheckTypeEnum;

	private Long tenantId;

	private Long storeId;

	private Long orderId;

	public DeliveryTimeOutCheckMessage(OrderKey orderKey, DeliveryTimeOutCheckTypeEnum deliveryTimeOutCheckTypeEnum) {
		this.tenantId = orderKey.getTenantId();
		this.storeId = orderKey.getStoreId();
		this.orderId = orderKey.getOrderId();
		this.deliveryTimeOutCheckTypeEnum = deliveryTimeOutCheckTypeEnum;
	}
}
