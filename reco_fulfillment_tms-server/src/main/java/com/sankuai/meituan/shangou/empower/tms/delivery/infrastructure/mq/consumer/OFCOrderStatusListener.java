package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryOperationApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryProcessApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryMonitorDomainService;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.PickSelectRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.OrderStatusDoubleCheckService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryLaunchCommandMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryTimeOutCheckMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.OFCOrderStatusMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.wrapper.MedicineTenantWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfigRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.qnh.ofc.ofw.common.mq.message.FulfillmentOrderDownstreamMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.Objects;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

@Slf4j
@Component
@Order()
public class OFCOrderStatusListener extends AbstractLaunchDeliveryListener{

    @Resource
    private OrderSystemClient orderSystemClient;
    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;
    @Resource
    private DeliveryProcessApplicationService deliveryProcessApplicationService;
    @Resource
    private MafkaDelayMessageProducer<DeliveryLaunchCommandMessage> deliveryDelayLaunchMessageProducer;
    @Resource
    private DeliveryOperationApplicationService deliveryOperationApplicationService;
    @Resource
    private MafkaDelayMessageProducer<DeliveryTimeOutCheckMessage> deliveryTimeOutCheckProducer;
    @Resource
    private DeliveryMonitorDomainService deliveryMonitorDomainService;
    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;
    @Resource
    private DeliveryRemindConfigRepository deliveryRemindConfigRepository;

    @Resource
    private TenantRemoteService tenantRemoteService;

    @Resource
    private PickSelectRemoteService pickSelectRemoteService;

    @Resource
    private OrderStatusChangeMessageListener orderStatusChangeMessageListener;


    @Resource
    private OrderStatusDoubleCheckService orderStatusDoubleCheckService;

    @Resource
    private MedicineTenantWrapper medicineTenantWrapper;

    @Resource
    private OFCOfwFulfillmentDownListener ofcOfwFulfillmentDownListener;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.DELIVERY_OFC_STATUS_CHANGE_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
        log.info("开始消费赋能OFC状态消息：{}", mafkaMessage);
        OFCOrderStatusMsg message = translateMessage(mafkaMessage);
        if (message == null) {
            return CONSUME_SUCCESS;
        }
        if(!MccConfigUtils.checkUseOFCTenantIdList(message.getTenantId())){
            return CONSUME_SUCCESS;
        }

        if (MccConfigUtils.orderStatusConsumeFilterDrunkHorseSwitch()
                && com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.checkIsDHTenant(message.getTenantId())) {
            //log.info("过滤歪马订单:{}", message.getOrderId());
            return CONSUME_SUCCESS;
        }
        //隔离医药租户
        if (medicineTenantWrapper.isUwmsMedicineTenant(message.getTenantId())) {
            return CONSUME_SUCCESS;
        }

        if(Objects.equals(message.getOrderStatus(), OrderStatusEnum.MERCHANT_CONFIRMED.getValue())){
            if(message.getTransferMessage()!=null){
                FulfillmentOrderDownstreamMessage fulfillmentOrderDownstreamMessage = convert(message);
                ConsumeStatus consumeStatus = ofcOfwFulfillmentDownListener.doHandle(fulfillmentOrderDownstreamMessage);
                if(consumeStatus == CONSUME_SUCCESS){
                    orderStatusDoubleCheckService.setCheck(message.getUnifyOrderId(),OrderStatusEnum.MERCHANT_CONFIRMED.getValue());
                }
                return consumeStatus;
            }
        }

        OrderStatusChangeMessageListener.MessageDTO messageDTO = convertToDTO(message);

        ConsumeStatus consumeStatus = orderStatusChangeMessageListener.handle(messageDTO,mafkaMessage);
        if(consumeStatus == CONSUME_SUCCESS){
            orderStatusDoubleCheckService.setCheck(message.getOrderId()+"",messageDTO.getStatus());
        }
        return consumeStatus;
    }

    private OrderStatusChangeMessageListener.MessageDTO convertToDTO(OFCOrderStatusMsg message){
        OrderStatusChangeMessageListener.MessageDTO dto = new OrderStatusChangeMessageListener.MessageDTO();
        dto.setTenantId(message.getTenantId());
        if (Objects.equals(message.getOrderShopId(),message.getWarehouseId())){
            dto.setShopId(message.getOrderShopId());
        }else {
            dto.setShopId(message.getOrderShopId());
            dto.setWarehouseId(message.getWarehouseId());
        }
        dto.setOrderId(Long.parseLong(message.getUnifyOrderId()));
        dto.setOrderSource(message.getSourceType());
        dto.setStatus(message.getOrderStatus());
        dto.setSourceStatus(message.getSourceStatus());
        dto.setCompensate(message.getCompensate());
        dto.setOnlineDistributeStatus(message.getDistributeStatus());
        dto.setDelayPush(message.isDelayPush());
        return dto;
    }

    private OFCOrderStatusMsg translateMessage(MafkaMessage mafkaMessage) {
        try {
            OFCOrderStatusMsg message = translateMessage(mafkaMessage, OFCOrderStatusMsg.class);

            Preconditions.checkNotNull(message, "empty mafkaMessage");
            Preconditions.checkNotNull(message.getTenantId(), "tenantId is null");
            Preconditions.checkNotNull(message.getOrderShopId(), "shopId is null");
            Preconditions.checkNotNull(message.getUnifyOrderId(), "orderId is null");
            Preconditions.checkNotNull(message.getOrderStatus(), "status is null");
            Preconditions.checkNotNull(message.getOrderSource(), "orderSource is null");
            Preconditions.checkNotNull(message.getSourceStatus(), "sourceStatus is null");
            return message;
        } catch (Exception e) {
            log.error("解析订单状态变更消息失败:{}", mafkaMessage, e);
            return null;
        }
    }

    @Override
    protected OrderSystemClient getOrderSystemClient() {
        return orderSystemClient;
    }

    @Override
    protected DeliveryProcessApplicationService getDeliveryProcessApplicationService() {
        return deliveryProcessApplicationService;
    }

    @Override
    protected MafkaDelayMessageProducer<DeliveryLaunchCommandMessage> getDeliveryDelayLaunchMessageProducer() {
        return deliveryDelayLaunchMessageProducer;
    }

    @Override
    protected DeliveryOperationApplicationService getDeliveryOperationApplicationService() {
        return deliveryOperationApplicationService;
    }

    @Override
    protected MafkaDelayMessageProducer<DeliveryTimeOutCheckMessage> getDeliveryTimeOutCheckProducer() {
        return deliveryTimeOutCheckProducer;
    }

    @Override
    protected DeliveryMonitorDomainService getDeliveryMonitorDomainService() {
        return deliveryMonitorDomainService;
    }

    @Override
    protected DeliveryPoiRepository getDeliveryPoiRepository() {
        return deliveryPoiRepository;
    }

    @Override
    protected DeliveryOrderRepository getDeliveryOrderRepository() {
        return deliveryOrderRepository;
    }

    @Override
    protected DeliveryRemindConfigRepository getDeliveryRemindConfigRepository() {
        return deliveryRemindConfigRepository;
    }

    @Override
    protected PickSelectRemoteService getPickSelectRemoteService() {
        return pickSelectRemoteService;
    }

    @Override
    protected  TenantRemoteService getTenantRemoteService(){
        return tenantRemoteService;
    }

    private FulfillmentOrderDownstreamMessage convert(OFCOrderStatusMsg msg){
        FulfillmentOrderDownstreamMessage downstreamMessage = new FulfillmentOrderDownstreamMessage();
        downstreamMessage.setWarehouseId(msg.getWarehouseId());
        downstreamMessage.setWarehousePoiId(msg.getOrderShopId());
        downstreamMessage.setFulfillmentWarehouseId(msg.getFulfillmentWarehouseId());
        downstreamMessage.setOrderId(msg.getOrderId());
        downstreamMessage.setOrderSource(msg.getOrderSource());
        downstreamMessage.setChannelOrderId(msg.getChannelOrderId());
        downstreamMessage.setFulfillmentOrderId(msg.getFulfillmentOrderId());
        downstreamMessage.setFulfillmentOrderType(msg.getTransferMessage().getFulfillmentOrderType());
        downstreamMessage.setFulfillmentWarehouseType(msg.getTransferMessage().getFulfillmentWarehouseType());
        downstreamMessage.setFulfillOrderSeq(msg.getTransferMessage().getFulfillOrderSeq());
        downstreamMessage.setOrderShopId(msg.getOrderShopId());
        downstreamMessage.setTenantId(msg.getTenantId());
        downstreamMessage.setUnifyOrderId(msg.getUnifyOrderId());
        return downstreamMessage;
    }

}
