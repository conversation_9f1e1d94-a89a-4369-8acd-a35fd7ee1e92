package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform;


import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DeliveryResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapChannelAggDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.ChannelStoreRelation;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DapShopAuthResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformUrlInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.AggDeliveryCancelHandleFailedEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryPlatformException;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.dap.DapDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.RetryTemplateUtil;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM;

@Slf4j
@Rhino
public class DapDeliveryPlatformClient extends AbstractAggDeliveryPlatformClient {

    // 取消原因code
    public static final int CANCEL_REASON_CODE = 99;
    public static final String TENANT_POI_SYNC = "Tenant.poi.sync";
    public static final String DAP = "Dap";

    @Resource
    protected DapChannelAggDeliveryThriftService dapChannelAggDeliveryThriftService;

    protected IChannelAggDeliveryPlatform iChannelAggDeliveryPlatform = new IChannelAggDeliveryPlatform() {
        @Override
        public CreateAggDeliveryResponse createDelivery(CreateAggDeliveryRequest request) throws TException {
            return dapChannelAggDeliveryThriftService.createDelivery(request);
        }

        @Override
        public QueryAggDeliveryRiderInfoResponse queryRiderLocation(QueryAggDeliveryRiderInfoRequest request) throws TException {
            return dapChannelAggDeliveryThriftService.queryRiderLocation(request);
        }

        @Override
        public CreateAggDeliveryShopResponse createAggDeliveryShop(CreateAggDeliveryShopRequest request) throws TException {
            return dapChannelAggDeliveryThriftService.createAggDeliveryShop(request);
        }

        @Override
        public SyncOrderInfoChangeResponse syncOrderInfoChange(SyncOrderInfoChangeRequest request) throws TException {
            return dapChannelAggDeliveryThriftService.syncOrderInfoChange(request);
        }
    };

    @Override
    protected int getCancelReasonCode() {
        return CANCEL_REASON_CODE;
    }

    @Override
    protected IChannelAggDeliveryPlatform getChannelAggDeliveryThriftService() {
        return iChannelAggDeliveryPlatform;
    }

    @Override
    public DeliveryPlatformEnum getDeliveryPlatform() {
        return DAP_DELIVERY_PLATFORM;
    }

    @Override
    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public Optional<Failure> closeShop(DeliveryPoi deliveryPoi) {
        if(deliveryPoi==null){
            return Optional.of(new Failure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "查询门店信息失败"));
        }
        CloseAggDeliveryShopRequest closeShopRequest=new CloseAggDeliveryShopRequest();
        closeShopRequest.setShopId(deliveryPoi.getStoreId());
        closeShopRequest.setAggDeliveryPlatformId(getDeliveryPlatform().getCode());

        try {
            CloseAggDeliveryShopResponse response = dapChannelAggDeliveryThriftService.closeAggDeliveryShop(closeShopRequest);
            if(response==null || response.getCode()!=FailureCodeEnum.SUCCESS.getCode()){
                throw new DeliveryPlatformException(FailureCodeEnum.PLATFORM_CLOSE_SHOP_EXCEPTION);
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("平台关店失败 deliveryPoi:{}",deliveryPoi,e);
            throw new DeliveryPlatformException(FailureCodeEnum.PLATFORM_CLOSE_SHOP_EXCEPTION);
        }
    }

    @Override
    @CatTransaction
    // @MethodLog(logRequest = false, logResponse = true)
    @Degrade(rhinoKey = "DapDeliveryPlatformClient.queryLinkInfo", fallBackMethod = "queryLinkInfoFallback", timeoutInMilliseconds = 1000)
    public Optional<Map<String, DeliveryPlatformUrlInfo>> queryLinkInfo(DeliveryPoi deliveryPoi, List<String> markIdList, LinkTypeEnum typeEnum) {
        if(deliveryPoi==null || CollectionUtils.isEmpty(markIdList) || typeEnum==null){
            return Optional.empty();
        }
        Map<String, DeliveryPlatformUrlInfo> deliveryPlatformUrlInfoMap = batchQueryDapLinkInfo(deliveryPoi.getStoreId(), markIdList, typeEnum);
        return Optional.ofNullable(deliveryPlatformUrlInfoMap);
    }

    @Override
    @CatTransaction
    // @MethodLog(logRequest = false, logResponse = true)
    @Degrade(rhinoKey = "DapDeliveryPlatformClient.queryLinkInfoOfToken", fallBackMethod = "queryLinkInfoOfTokenFallback", timeoutInMilliseconds = 1000)
    public Optional<Map<String, DeliveryPlatformUrlInfo>> queryLinkInfoOfToken(DeliveryPoi deliveryPoi, List<String> markIdList, LinkTypeEnum typeEnum, String token, SiteTypeEnum siteType) {
        if(!MccConfigUtils.dapConfigLinkNoAuthSwitch()){
            return Optional.empty();
        }
        if(deliveryPoi==null || CollectionUtils.isEmpty(markIdList) || typeEnum==null){
            return Optional.empty();
        }
        Map<String, DeliveryPlatformUrlInfo> deliveryPlatformUrlInfoMap = batchQueryDapLinkInfoOfToken(deliveryPoi.getStoreId(), markIdList, typeEnum, token, siteType);
        return Optional.ofNullable(deliveryPlatformUrlInfoMap);
    }

    @CatTransaction
    // @MethodLog(logRequest = false, logResponse = true)
    @Degrade(rhinoKey = "DapDeliveryPlatformClient.batchQueryDapLinkInfo", fallBackMethod = "batchQueryDapLinkInfoFallback", timeoutInMilliseconds = 1000)
    public Map<String, DeliveryPlatformUrlInfo> batchQueryDapLinkInfo(Long storeId, List<String> markIdList, LinkTypeEnum typeEnum) {
        return batchQueryDapLinkInfoOfToken(storeId, markIdList, typeEnum, null, null);
    }

    @CatTransaction
    // @MethodLog(logRequest = false, logResponse = true)
    @Degrade(rhinoKey = "DapDeliveryPlatformClient.batchQueryDapLinkInfoOfToken", fallBackMethod = "batchQueryDapLinkInfoOfTokenFallback", timeoutInMilliseconds = 1000)
    public Map<String, DeliveryPlatformUrlInfo> batchQueryDapLinkInfoOfToken(Long storeId, List<String> markIdList, LinkTypeEnum typeEnum, String token, SiteTypeEnum siteType) {
        if (!MccConfigUtils.dapLinkNoAuthSwitch()) {
            return Maps.newHashMap();
        }
        if (Objects.isNull(storeId) || CollectionUtils.isEmpty(markIdList) || Objects.isNull(typeEnum)) {
            return Maps.newHashMap();
        }

        Map<String, DeliveryPlatformUrlInfo> result = Maps.newHashMap();

        List<List<String>> partList = Lists.partition(markIdList, MccConfigUtils.dapUrlMarkIdPartNum());
        for (List<String> part : partList){
            GetAggDeliveryLinkRequest request=new GetAggDeliveryLinkRequest();
            request.setShopId(storeId);
            request.setIds(Joiner.on(",").join(part));
            request.setAggDeliveryPlatformId(getDeliveryPlatform().getCode());
            request.setType(typeEnum.getType());
            request.setToken(token);
            if (Objects.nonNull(siteType)) {
                request.setDeviceType(siteType.getCode());
            }

            RetryTemplate retryTemplate = RetryTemplateUtil.simpleWithFixedRetry(3, 100);
            try {
                GetAggDeliveryLinkResponse response = retryTemplate.execute((RetryCallback<GetAggDeliveryLinkResponse, Exception>) retryContext -> {
                            GetAggDeliveryLinkResponse res = dapChannelAggDeliveryThriftService.getAggDeliveryLink(request);
                            if (res == null || res.getCode() != FailureCodeEnum.SUCCESS.getCode()) {
                                if (res != null && res.getCode() == DeliveryResultCodeEnum.DAP_FALLBACK_ERROR.getCode()) {
                                    return res;
                                }
                                throw new DeliveryPlatformException(FailureCodeEnum.QUERY_PLATFORM_LINK_EXCEPTION);
                            }
                            return res;
                        });
                if (response == null || response.getCode() != FailureCodeEnum.SUCCESS.getCode() || CollectionUtils.isEmpty(response.getLinkDetailList())) {
                    continue;
                }
                for (AggDeliveryLinkDetailDTO linkDetailDTO : response.getLinkDetailList()) {
                    result.put(linkDetailDTO.getId(), DeliveryPlatformUrlInfo.builder()
                            .linkTitle(linkDetailDTO.getTitle())
                            .linkUrl(linkDetailDTO.getUrl())
                            .markId(linkDetailDTO.getId())
                            .build());
                }
            } catch (Exception e) {
                log.error("queryLinkInfo error storeId:{},markIdList:{},typeEnum:{}", storeId, markIdList, typeEnum, e);
            }
        }

        return result;
    }

    public Map<String, DeliveryPlatformUrlInfo> batchQueryDapLinkInfoFallback(Long storeId, List<String> markIdList, LinkTypeEnum typeEnum) {
        log.info("batchQueryDapLinkInfoFallback markIdList:{}",markIdList);
        return Maps.newHashMap();
    }

    public Map<String, DeliveryPlatformUrlInfo> batchQueryDapLinkInfoOfTokenFallback(Long storeId, List<String> markIdList, LinkTypeEnum typeEnum, String token, SiteTypeEnum siteType) {
        log.info("batchQueryDapLinkInfoOfTokenFallback markIdList:{}",markIdList);
        return Maps.newHashMap();
    }

    public Optional<Map<String, DeliveryPlatformUrlInfo>> queryLinkInfoFallback(DeliveryPoi deliveryPoi, List<String> markIdList, LinkTypeEnum typeEnum) {
        log.info("queryLinkInfo fall back markIdList:{}",markIdList);
        return Optional.empty();
    }

    public Optional<Map<String, DeliveryPlatformUrlInfo>> queryLinkInfoOfTokenFallback(DeliveryPoi deliveryPoi, List<String> markIdList, LinkTypeEnum typeEnum, String token, SiteTypeEnum siteType) {
        log.info("queryLinkInfoOfToken fall back markIdList:{}", markIdList);
        return Optional.empty();
    }

    @Override
    @CatTransaction
    // @MethodLog(logRequest = false, logResponse = true)
    protected Optional<Failure> doCancelDelivery(DeliveryOrder deliveryOrder) {
        log.info("DapDeliveryPlatformClient.doCancelDelivery deliveryOrderPrimaryId:{}", deliveryOrder.getId());
        try {
            CancelAggDeliveryRequest cancelAggDeliveryRequest = new CancelAggDeliveryRequest(
                    deliveryOrder.getOrderId(), getDeliveryPlatform().getCode(), "订单取消", getCancelReasonCode(), getAppInfo(),deliveryOrder.getDeliveryOrderId()
            );

            log.info("DapAggDeliveryThriftService.cancelAggDelivery begin, request={}", cancelAggDeliveryRequest);
            CancelAggDeliveryResponse response = dapChannelAggDeliveryThriftService.cancelDelivery(cancelAggDeliveryRequest);
            log.info("DapAggDeliveryThriftService.cancelAggDelivery finish, result={}", response);
            if (response == null || response.getCode() != BigInteger.ZERO.intValue()) {
                warnEventPublisher.postEvent(new AggDeliveryCancelHandleFailedEvent(getDeliveryPlatform(),
                        deliveryOrder.getOrderKey(), Optional.ofNullable(response).map(CancelAggDeliveryResponse::getMsg).orElse("取消运单失败")));
                if(response != null && response.getCode() == DeliveryResultCodeEnum.DAP_FALLBACK_ERROR.getCode()){
                    return Optional.of(new Failure(false, FailureCodeEnum.CANCEL_DELIVERY_FAILED.getCode(),
                            Optional.ofNullable(response).map(CancelAggDeliveryResponse::getMsg).orElse("调用聚合运力平台取消配送异常")));
                }
                return Optional.of(new Failure(true, FailureCodeEnum.CANCEL_DELIVERY_FAILED.getCode(),
                        Optional.ofNullable(response).map(CancelAggDeliveryResponse::getMsg).orElse("调用聚合运力平台取消配送异常")));
            }
        } catch (Exception e) {
            warnEventPublisher.postEvent(new AggDeliveryCancelHandleFailedEvent(getDeliveryPlatform(),
                    deliveryOrder.getOrderKey(), "取消运单失败"));
            log.info("ChannelDeliveryThriftService.cancelAggDelivery exception", e);
            return Optional.of(new Failure(true, FailureCodeEnum.CANCEL_DELIVERY_FAILED.getCode(),
                    "调用聚合运力平台取消配送异常"));
        }
        return Optional.empty();
    }

    @Override
    protected Optional<Failure> doCancelDeliveryForTransOrder(DeliveryOrder deliveryOrder) {
        try {
            CancelDeliveryForTransOrderRequest cancelAggDeliveryRequest = new CancelDeliveryForTransOrderRequest(
                    deliveryOrder.getOrderId(), getDeliveryPlatform().getCode(), "运单取消", getCancelReasonCode(),
            getAppInfo(),deliveryOrder.getDeliveryOrderId());

            log.info("DapAggDeliveryThriftService.cancelDeliveryForTransOrder begin, request={}", cancelAggDeliveryRequest);
            CancelAggDeliveryResponse response = dapChannelAggDeliveryThriftService.cancelDeliveryForTransOrder(cancelAggDeliveryRequest);
            log.info("DapAggDeliveryThriftService.cancelDeliveryForTransOrder finish, result={}", response);
            if (response == null || response.getCode() != BigInteger.ZERO.intValue()) {
                warnEventPublisher.postEvent(new AggDeliveryCancelHandleFailedEvent(getDeliveryPlatform(),
                        deliveryOrder.getOrderKey(), Optional.ofNullable(response).map(CancelAggDeliveryResponse::getMsg).orElse("取消运单失败")));
                if(response != null && response.getCode() == DeliveryResultCodeEnum.DAP_FALLBACK_ERROR.getCode()){
                    return Optional.of(new Failure(false, FailureCodeEnum.CANCEL_DELIVERY_FAILED.getCode(),
                            Optional.ofNullable(response).map(CancelAggDeliveryResponse::getMsg).orElse("调用聚合运力平台取消配送异常")));
                }
                return Optional.of(new Failure(true, FailureCodeEnum.CANCEL_DELIVERY_FAILED.getCode(),
                        Optional.ofNullable(response).map(CancelAggDeliveryResponse::getMsg).orElse("调用聚合运力平台取消配送异常")));
            }
        } catch (Exception e) {
            warnEventPublisher.postEvent(new AggDeliveryCancelHandleFailedEvent(getDeliveryPlatform(),
                    deliveryOrder.getOrderKey(), "取消运单失败"));
            log.info("ChannelDeliveryThriftService.cancelAggDelivery exception", e);
            return Optional.of(new Failure(true, FailureCodeEnum.CANCEL_DELIVERY_FAILED.getCode(),
                    "调用聚合运力平台取消配送异常"));
        }
        return Optional.empty();
    }

    @Override
    @CatTransaction
    protected Result<Address> getStoreAddress(DeliveryPoi deliveryPoi, TenantStoreInfo tenantStoreInfo) {
        DapDeliveryPoi dapDeliveryPoi = (DapDeliveryPoi) deliveryPoi;
        Address storeAddress = dapDeliveryPoi.getStoreAddress();
        if (storeAddress == null) {
            Result<CoordinatePoint> coordinateQueryResult = mapClient.queryCoordinatesByDetailAddress(tenantStoreInfo.getAddress());
            if (coordinateQueryResult.isFail()) {
                log.warn("查询门店坐标失败, tenantId:{}, storeId:{}, address:{}, failure:{}",
                        deliveryPoi.getTenantId(), deliveryPoi.getStoreId(), tenantStoreInfo.getAddress(), coordinateQueryResult.getFailure()
                );
                Cat.logEvent("CREATE_DELIVERY_SHOP_FAIL", "GLORY_STORE_COORDINATES_QUERY_FAILED");
                return new Result<>(new Failure(true, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "查询门店经纬度失败"));
            }

            storeAddress = new Address(tenantStoreInfo.getAddress(), CoordinateTypeEnum.MARS, coordinateQueryResult.getInfo());
            dapDeliveryPoi.setStoreAddress(storeAddress);
            deliveryPoiRepository.saveDeliveryPoi(deliveryPoi);
        }

        return new Result<>(storeAddress);
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public Optional<Failure> deliveryFastAuth(DeliveryPoi deliveryPoi, List<Long> wmStoreIdList, List<ChannelStoreRelation> relationList) {

        DeliveryFastAuthRequest request = new DeliveryFastAuthRequest();
        request.setStoreId(deliveryPoi.getStoreId());
        request.setWmStoreIdList(wmStoreIdList);
        request.setAggDeliveryPlatformId(getDeliveryPlatform().getCode());
        if(CollectionUtils.isNotEmpty(relationList)){
            request.setRelationList(relationList.stream().map(item->new com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.ChannelStoreRelation(item.getStoreId(),item.getChannelStoreId())).collect(Collectors.toList()));
        }

        try{
            DeliveryFastAuthResponse response = dapChannelAggDeliveryThriftService.deliveryFastAuth(request);
            if (response == null || response.getCode() == null) {
                return Optional.of(new Failure(true, FailureCodeEnum.FAST_AUTH_EXCEPTION));
            }
            if (response.getCode() == DeliveryResultCodeEnum.DAP_FALLBACK_ERROR.getCode()) {
                return Optional.of(new Failure(false, FailureCodeEnum.FAST_AUTH_EXCEPTION));
            } else if (response.getCode() != DeliveryResultCodeEnum.SUCCESS.getCode()) {
                return Optional.of(new Failure(true, FailureCodeEnum.FAST_AUTH_EXCEPTION));
            }
            return Optional.empty();
        }catch (Exception e){
            log.error("deliveryFastAuth error",e);
        }
        return Optional.of(new Failure(true, FailureCodeEnum.FAST_AUTH_EXCEPTION));
    }

    @Override
    public Optional<Failure> cancelDeliveryForOFC(DeliveryOrder deliveryOrder) {
        return super.cancelDeliveryForTransOrder(deliveryOrder);
    }

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public Result<DapShopAuthResult> deliveryShopAuth(DeliveryPlatformEnum deliveryPlatform, Long storeId) {

        DeliveryShopAuthRequest request = new DeliveryShopAuthRequest();
        request.setStoreId(storeId);
        request.setAggDeliveryPlatformId(getDeliveryPlatform().getCode());
        RetryTemplate retryTemplate=RetryTemplateUtil.simpleWithFixedRetry(3,100);

        try{
            DeliveryShopAuthResponse response = retryTemplate.execute(new RetryCallback<DeliveryShopAuthResponse, Exception>() {
                @Override
                public DeliveryShopAuthResponse doWithRetry(RetryContext retryContext) throws Exception {
                    DeliveryShopAuthResponse response = dapChannelAggDeliveryThriftService.deliveryShopAuth(request);
                    if(response ==null || response.getCode()!=FailureCodeEnum.SUCCESS.getCode()){
                        if(response != null && response.getCode() == DeliveryResultCodeEnum.DAP_FALLBACK_ERROR.getCode()){
                            return response;
                        }
                        throw new DeliveryPlatformException(FailureCodeEnum.QUERY_SHOP_AUTH_EXCEPTION);
                    }
                    return response;
                }
            });

            if (response == null || response.getCode() == null || response.getCode() != DeliveryResultCodeEnum.SUCCESS.getCode()) {
                return new Result<>(DapShopAuthResult.builder().code(FailureCodeEnum.QUERY_SHOP_AUTH_EXCEPTION.getCode()).message("查询门店授权信息失败").build());
            }
            return new Result<>(DapShopAuthResult.builder().code(response.getCode()).isAuthed(response.getIsAuthed()).build());
        }catch (Exception e){
            log.error("deliveryShopAuth error",e);
        }
        return new Result<>(DapShopAuthResult.builder().code(FailureCodeEnum.QUERY_SHOP_AUTH_EXCEPTION.getCode()).message("查询门店授权信息失败").build());
    }

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public Optional<Failure> syncTenantPoi(DeliveryTenantPoiSyncRequest request) {
        try {
            RetryTemplate retryTemplate= RetryTemplateUtil.simpleWithFixedRetry(2,100);
            DeliveryTenantPoiSyncResponse response = retryTemplate.execute(new RetryCallback<DeliveryTenantPoiSyncResponse, Throwable>() {
                @Override
                public DeliveryTenantPoiSyncResponse doWithRetry(RetryContext retryContext) throws Throwable {
                    return dapChannelAggDeliveryThriftService.syncTenantPoi(request);
                }
            });

            if (response == null || response.getCode() == null) {
                Cat.logEvent(TENANT_POI_SYNC, DAP, "1", "");
                return Optional.of(new Failure(false, FailureCodeEnum.SYNC_TENANT_POI_FAILED));
            }
            if (response.getCode() == DeliveryResultCodeEnum.DAP_FALLBACK_ERROR.getCode()) {
                Cat.logEvent(TENANT_POI_SYNC, DAP, "1", "");
                return Optional.of(new Failure(false, FailureCodeEnum.SYNC_TENANT_POI_FAILED));
            } else if (response.getCode() != DeliveryResultCodeEnum.SUCCESS.getCode()) {
                Cat.logEvent(TENANT_POI_SYNC, DAP, "1", "");
                return Optional.of(new Failure(false, FailureCodeEnum.SYNC_TENANT_POI_FAILED));
            }
            Cat.logEvent(TENANT_POI_SYNC, DAP);
        } catch (Throwable e) {
            log.error("syncTenantPoi error", e);
            Cat.logEvent(TENANT_POI_SYNC, DAP, "1", "");
            return Optional.of(new Failure(false, FailureCodeEnum.SYNC_TENANT_POI_FAILED));
        }

        return Optional.empty();
    }

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public Optional<Failure> syncPaoTuiDeliveryStatusAfterLockOrder(DeliveryOrder deliveryOrder, String channelServicePackageId) {
        try {
            //10：等待分配骑手
            SyncPaoTuiDeliveryStatusAfterLockOrderRequest request = new SyncPaoTuiDeliveryStatusAfterLockOrderRequest(
                    deliveryOrder.getOrderId(), channelServicePackageId, getDeliveryPlatform().getCode(), 10);
            RetryTemplate retryTemplate= RetryTemplateUtil.simpleWithFixedRetry(2,100);
            SyncPaoTuiDeliveryStatusAfterLockOrderResponse response = retryTemplate.execute(new RetryCallback<SyncPaoTuiDeliveryStatusAfterLockOrderResponse, Throwable>() {
                @Override
                public SyncPaoTuiDeliveryStatusAfterLockOrderResponse doWithRetry(RetryContext retryContext) throws Throwable {
                    SyncPaoTuiDeliveryStatusAfterLockOrderResponse response = dapChannelAggDeliveryThriftService.syncPaoTuiDeliveryStatusAfterLockOrder(request);
                    if(response ==null || response.getCode()!=FailureCodeEnum.SUCCESS.getCode()){
                        if(response != null && response.getCode() == DeliveryResultCodeEnum.DAP_FALLBACK_ERROR.getCode()){
                            return response;
                        }
                        throw new DeliveryPlatformException(FailureCodeEnum.SYNC_PAO_TUI_DELIVERY_STATUS_FAILED);
                    }
                    return response;
                }
            });

            if (response == null || response.getCode() == null || response.getCode() != DeliveryResultCodeEnum.SUCCESS.getCode()) {
                return Optional.of(new Failure(false, FailureCodeEnum.SYNC_PAO_TUI_DELIVERY_STATUS_FAILED));
            }
            return Optional.empty();
        } catch (Throwable e) {
            log.error("syncPaoTuiDeliveryStatusAfterLockOrder error", e);
            return Optional.of(new Failure(false, FailureCodeEnum.SYNC_PAO_TUI_DELIVERY_STATUS_FAILED));
        }
    }

    @Override
    public Optional<Failure> notifyDeliveryPlatformLockStatusChange(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder) {
        log.error("青云暂不支持同步跑腿锁单2.0锁单状态变更操作 orderId: {}", deliveryOrder.getOrderId());
        return Optional.empty();
    }
}
