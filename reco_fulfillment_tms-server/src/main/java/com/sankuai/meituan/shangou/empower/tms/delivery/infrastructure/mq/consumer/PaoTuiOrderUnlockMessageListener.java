package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryOperationApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryAvailableEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.OrderUnLockIdempotentOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.OrderLockEndNotifyMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.*;

@Service
@Slf4j
public class PaoTuiOrderUnlockMessageListener extends AbstractDeadLetterConsumer{

    @Resource
    private OrderSystemClient orderSystemClient;
    @Resource
    private TenantRemoteService tenantRemoteService;
    @Resource
    private DeliveryOperationApplicationService deliveryOperationApplicationService;

    @Resource
    private OrderUnLockIdempotentOperateService orderUnLockIdempotentOperateService;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.PAO_TUI_ORDER_UNLOCK_CONSUMER;
    }
    @Override
    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("开始消费锁单结束发配送消息: {}", message);
        OrderLockEndNotifyMessage msg = translateMessage(message);
        if (msg == null) {
            return CONSUME_SUCCESS;
        }
        if(MccConfigUtils.unlockOrderIdempotentSwitch()){
            if(orderUnLockIdempotentOperateService.check(msg.getChannelOrderId())){
                log.info("锁单已经幂等: {}", msg.getChannelOrderId());
                return CONSUME_SUCCESS;
            }
        }

        try {
            Result<OrderInfo> orderInfoResult = orderSystemClient.queryByViewOrderId(OrderBizTypeEnum.MEITUAN_WAIMAI.getValue(),msg.getChannelOrderId(),false);
            if (orderInfoResult.isFail()) {
                log.info("订单查询失败,{}",message);
                return CONSUME_FAILURE;
            }
            OrderInfo orderInfo = orderInfoResult.getInfo();
            if(orderInfo==null){
                return CONSUME_FAILURE;
            }


            DeliveryAvailableEnum deliveryAvailableEnum = DeliveryAvailableEnum.findOf(msg.getLockOrderStatus());
            boolean lockStatus = deliveryAvailableEnum == DeliveryAvailableEnum.NO;
            log.info("覆盖锁单标识,from {} to {}", orderInfo.getIsLocked(), lockStatus);
            orderInfo.setIsLocked(lockStatus);
            orderInfo.setOrderLockLabel(lockStatus);

            Optional<Failure> failure = deliveryOperationApplicationService.paoTuiLockStatusNotify(orderInfo);
            if (failure.isPresent()) {
                return CONSUME_FAILURE;
            }
            orderUnLockIdempotentOperateService.setCheck(msg.getChannelOrderId());
            return CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("处理消息失败", e);
            return CONSUME_FAILURE;
        }
    }
    private OrderLockEndNotifyMessage translateMessage(MafkaMessage mafkaMessage) {
        try {
            OrderLockEndNotifyMessage message = translateMessage(mafkaMessage, OrderLockEndNotifyMessage.class);
            Preconditions.checkNotNull(message, "empty mafkaMessage");
            Preconditions.checkNotNull(message.getChannelOrderId(), "channelOrderId is null");
            return message;

        } catch (Exception e) {
            log.error("解析消息失败:{}", mafkaMessage, e);
            return null;
        }
    }
}
