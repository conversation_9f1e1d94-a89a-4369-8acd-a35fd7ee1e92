package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.lion.client.Lion;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DistributeMethodEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.DyDeliveryCallbackMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.QueryDeliveryInfoApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.DeliveryCallbackThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request.FarmDeliveryChangeNotifyReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.response.FarmDeliveryChangeNotifyResp;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils.MccUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.DyOrderChannelDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.FarmCancelTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.OpenApiDistributeStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.ClearDeliveryExceptionMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryExceptionCallbackInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryUnifiedCallbackMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * 私域渠道非牵牛花管理平台配送状态变更消费
 */
@Slf4j
@Component
public class OpenApiDeliveryChangeMessageListener extends AbstractDeadLetterConsumer{
    /**
     * 私域渠道平台配送承运商信息，这里写死
     */
    private static final String OPENAPI_LOGISTIC_MARK = "openapi";

    /**
     * 私域渠道平台配送渠道运单ID，这里写死
     */
    private static final String OPENAPI_LOGISTIC_NO = "0";

    @Resource
    private OrderSystemClient orderSystemClient;

    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private DeliveryTimeOutCheckService deliveryTimeOutCheckService;

    @Resource
    private DeliveryCallbackThriftService deliveryCallbackThriftService;
    @Resource
    private MafkaDelayMessageProducer<ClearDeliveryExceptionMsg> clearDeliveryExceptionProducer;

    @Resource
    private MafkaMessageProducer<DeliveryUnifiedCallbackMessage> deliveryUnifiedCallbackMessageProducer;

    @Resource
    private QueryDeliveryInfoApplicationService queryDeliveryInfoApplicationService;
    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.OPEN_API_DELIVERY_ORDER_CHANGE_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("开始消费私域渠道平台配送状态变更消息: {}", message);
        OpenApiDeliveryStatusUpdateMessage msg = translateMessage(message);
        if (msg == null) {
            return CONSUME_SUCCESS;
        }
        if (MccUtils.notConsumeOpenApiMessage()) {
            log.info("不消费私域渠道配送变更消息");
            return CONSUME_SUCCESS;
        }

        try {
            String viewOrderId = msg.getViewOrderId();
            if (StringUtils.isBlank(viewOrderId)) {
                log.error("消费私域渠道配送状态变更消息失败, viewOrderId is null");
                return CONSUME_SUCCESS;
            }
            Result<OrderInfo> orderInfoResult = orderSystemClient.queryByViewOrderId(msg.getOrderBizType(), viewOrderId,false);
            OrderInfo orderInfo = orderInfoResult.getInfo();
            if (orderInfo == null) {
                return CONSUME_FAILURE;
            }
            if (orderInfo.getDeliveryMethod() != null && !orderInfo.getDeliveryMethod().equals(DistributeMethodEnum.HOME_DELIVERY.getValue())) {
                log.error("私域渠道订单非配送单，放弃消费");
                return CONSUME_SUCCESS;
            }
            DynamicOrderBizType dynamicOrderBizType = DynamicOrderBizType.findOf(msg.getOrderBizType());
            if (dynamicOrderBizType == null || dynamicOrderBizType.getChannelId() == null || dynamicOrderBizType.getChannelStandard() == null) {
                log.error("订单来源[{}]未知, 将会放弃消费消费私域渠道配送变更消息失败，dynamicOrderBizType{}", dynamicOrderBizType);
                return CONSUME_FAILURE;
            }
            Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(), dynamicOrderBizType.getChannelId());
            if (!opDeliveryPoi.isPresent()) {
                return CONSUME_SUCCESS;
            }
            DeliveryPlatformEnum deliveryPlatformEnum = opDeliveryPoi.get().getDeliveryPlatform();
            if (deliveryPlatformEnum != DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM) {
                return CONSUME_SUCCESS;
            }
            DeliveryOrder deliveryOrder;
            Optional<DeliveryOrder> deliveryOrderOptional = queryDeliveryInfoApplicationService.queryDeliveryOrderByOrderId(orderInfo.getOrderKey().getOrderId(),opDeliveryPoi.get().getTenantId(),opDeliveryPoi.get().getStoreId());
            if (!deliveryOrderOptional.isPresent()) {
                deliveryOrder = new DeliveryOrder(orderInfo, DeliveryChannelEnum.ORDER_CHANNEL_DELIVERY.getCode());
                deliveryOrder.activate();
                deliveryOrderRepository.saveDeliveryOrder(deliveryOrder);
                // 保存运单、发送配送超时检查
                deliveryTimeOutCheckService.triggerDeliveryTimeOutCheck(deliveryOrder);
                // 发送异常清除检查
                triggerExceptionClearMonitor(Optional.ofNullable(deliveryOrder.getEstimatedDeliveryTime())
                        .orElse(deliveryOrder.getEstimatedDeliveryEndTime()), orderInfo.getOrderKey());

            } else {
                deliveryOrder = deliveryOrderOptional.get();
            }
            OpenApiDistributeStatusEnum distributeStatusEnum = OpenApiDistributeStatusEnum.enumOf(msg.getDistributeStatus());
            if (distributeStatusEnum != null) {
                switch (distributeStatusEnum) {
                    case WAIT_FOR_ASSIGN_RIDER:
                        if (Objects.equals(deliveryOrder.getDeliveryExceptionCode(), DeliveryExceptionCodeEnum.OPEN_API_DELIVERY_EXCEPTION.getCode())) {
                            deliveryTimeOutCheckService.triggerDeliveryTimeOutCheck(deliveryOrder);
                            triggerExceptionClearMonitor(Optional.ofNullable(deliveryOrder.getEstimatedDeliveryTime())
                                    .orElse(deliveryOrder.getEstimatedDeliveryEndTime()), orderInfo.getOrderKey());
                        }
                    case RIDER_ASSIGNED:
                    case RIDER_TAKE_GOODS:
                    case SENDING:
                    case RIDER_DELIVERED:
                    case CANCEL:
                        FarmDeliveryChangeNotifyReq req = buildFarmDeliveryChangeNotifyReq(msg, deliveryOrder, dynamicOrderBizType, distributeStatusEnum);
                        log.info("OpenApiDeliveryChangeMessageListener invoke farmNotifyDeliveryChange request = {}", req);
                        FarmDeliveryChangeNotifyResp farmDeliveryChangeNotifyResp = deliveryCallbackThriftService.farmNotifyDeliveryChange(req);
                        log.info("OpenApiDeliveryChangeMessageListener invoke farmNotifyDeliveryChange response = {}", farmDeliveryChangeNotifyResp);
                        break;
                    case DISTRIBUTE_EXCEPTION:
                        sendDeliveryExceptionMessage(msg, orderInfo.getOrderKey().getOrderId());
                        break;
                    default:
                        break;
                }
            }
            return CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("消费私域渠道订单配送状态变更消息失败，将会进行重试", e);
            return CONSUME_FAILURE;
        }
    }

    private OpenApiDeliveryStatusUpdateMessage translateMessage(MafkaMessage mafkaMessage) {
        try {
            OpenApiDeliveryStatusUpdateMessage message = translateMessageWithBodyHolder(mafkaMessage, OpenApiDeliveryStatusUpdateMessage.class);
            Preconditions.checkNotNull(message, "empty mafkaMessage");
            Preconditions.checkNotNull(message.getTenantId(), "tenantId is null");
            Preconditions.checkNotNull(message.getViewOrderId(), "orderId is null");
            Preconditions.checkNotNull(message.getDistributeStatus(), "distributeStatus is null");
            return message;

        } catch (Exception e) {
            log.error("解析私域渠道订单配送状态变更消息失败:{}", mafkaMessage, e);
            return null;
        }
    }

    private void sendDeliveryExceptionMessage(OpenApiDeliveryStatusUpdateMessage msg, Long orderId) {
        deliveryUnifiedCallbackMessageProducer.sendMessage(
                new DeliveryUnifiedCallbackMessage(
                        DeliveryCallbackTypeEnum.EXCEPTION_CALLBACK,
                        new DeliveryExceptionCallbackInfo(
                                orderId,
                                new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_SYSTEM,
                                        StringUtils.isNotBlank(msg.getExtJson()) ? msg.getExtJson() : DeliveryExceptionCodeEnum.OPEN_API_DELIVERY_EXCEPTION.getDesc(),
                                        DeliveryExceptionCodeEnum.OPEN_API_DELIVERY_EXCEPTION.getCode()),
                                LocalDateTime.now())
                ),
                orderId);
    }

    private FarmDeliveryChangeNotifyReq buildFarmDeliveryChangeNotifyReq(OpenApiDeliveryStatusUpdateMessage msg, DeliveryOrder deliveryOrder, DynamicOrderBizType dynamicOrderBizType, OpenApiDistributeStatusEnum distributeStatusEnum) {
        FarmDeliveryChangeNotifyReq req = new FarmDeliveryChangeNotifyReq();

        req.setOriginId(String.valueOf(deliveryOrder.getOrderId()));
        req.setPlatformCode(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode());
        req.setOrderChannelType(dynamicOrderBizType.getChannelId());
        req.setLogistic(OPENAPI_LOGISTIC_MARK);
        req.setLogisticNo(OPENAPI_LOGISTIC_NO);

        if (StringUtils.isNotEmpty(msg.getDistributeStatus())) {
            req.setStatus(Integer.valueOf(msg.getDistributeStatus()));
        }
        if (msg.getUpdateTime() != null) {
            req.setTimestamp(msg.getUpdateTime()/1000);
        }
        if (StringUtils.isNotEmpty(msg.getRiderName())) {
            req.setRiderName(msg.getRiderName());
        }
        if (StringUtils.isNotEmpty(msg.getRiderPhone())) {
            req.setRiderPhone(msg.getRiderPhone());
        }
        if (distributeStatusEnum == OpenApiDistributeStatusEnum.CANCEL &&
                MccConfigUtils.getPrivateChannelId(dynamicOrderBizType.getChannelId())) {
            req.setCancelReasonCode(FarmCancelTypeEnum.SYSTEM_CANCEL.getCode());
        }

        return req;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OpenApiDeliveryStatusUpdateMessage {
        /**
         * 订单业务类型
         */
        private Integer orderBizType;
        /**
         * 租户id
         */
        private Long tenantId;
        /**
         * 订单id
         */
        private String viewOrderId;
        /**
         * 配送状态
         */
        private String distributeStatus;
        /**
         * 变更时间
         */
        private Long updateTime;

        /**
         * 骑手姓名
         */
        private String riderName;

        /**
         * 骑手电话
         */
        private String riderPhone;

        /**
         * 额外信息
         */
        private String extJson;
    }

    private void triggerExceptionClearMonitor(LocalDateTime estimatedDeliveryTime, OrderKey orderKey) {
        if (Lion.getConfigRepository().getBooleanValue("delivery.clear.exception.fallback", false)) {
            return;
        }
        if(estimatedDeliveryTime == null){
            return;
        }
        long delayMillis = com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils
                .getDeliveryExceptionClearDelayMillis(estimatedDeliveryTime);
        clearDeliveryExceptionProducer.sendDelayMessageInMillis(new ClearDeliveryExceptionMsg(orderKey, TimeUtil.toMilliSeconds(estimatedDeliveryTime)), Math.max(delayMillis, 5000));
    }
}
