package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryChangeInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.DeliveryOrderLog;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.DeliveryOrderLogRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderLogDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.DeliveryOrderLogDOExMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDOExample;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/9
 */
@Slf4j
@Repository
public class MySQLDeliveryOrderLogRepository implements DeliveryOrderLogRepository {

	@Resource
	private DeliveryOrderLogDOMapper deliveryOrderLogDOMapper;

	@Resource
	private DeliveryOrderLogDOExMapper deliveryOrderLogDOExMapper;

	@Override
	public void saveDeliveryChangeLog(Long deliveryId, Integer deliveryStatus, DeliveryEventEnum deliveryEvent, String changeInfoJson, LocalDateTime changeTime) {
		if (isRecordAlreadyExist(deliveryId, deliveryEvent, changeTime)) {
			return;
		}

		try {
			deliveryOrderLogDOMapper.insertSelective(translate(deliveryId, deliveryStatus, deliveryEvent, changeInfoJson, changeTime));
		} catch (DuplicateKeyException e) {
			log.debug("Delivery change log record already existed", e);
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Map<Long, DeliveryOrderLog> getLogsByDeliveryOrderIds(List<Long> deliveryIds, DeliveryEventEnum deliveryEvent) {

		if (CollectionUtils.isEmpty(deliveryIds) || deliveryEvent == null) {
			return Maps.newHashMap();
		}
		DeliveryOrderLogDOExample example = new DeliveryOrderLogDOExample();
		example.createCriteria().andDeliveryOrderIdIn(deliveryIds).andChangeTypeEqualTo(deliveryEvent.getCode());
		List<DeliveryOrderLogDO> deliveryOrderLogDOS = deliveryOrderLogDOMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(deliveryOrderLogDOS)) {
			return Maps.newHashMap();
		}
		return deliveryOrderLogDOS.stream().collect(Collectors.toMap(DeliveryOrderLogDO::getDeliveryOrderId, this::buildDoToDomain, (newLog, oldLog) -> newLog));
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Map<Long, List<DeliveryOrderLog>> getLogsByDeliveryOrderIdsAndDeliveryEventCodes(List<Long> deliveryIds, List<Integer> deliveryEventCodes) {
		if (CollectionUtils.isEmpty(deliveryIds) || CollectionUtils.isEmpty(deliveryEventCodes)) {
			return Maps.newHashMap();
		}

		DeliveryOrderLogDOExample example = new DeliveryOrderLogDOExample();
		example.createCriteria().andDeliveryOrderIdIn(deliveryIds).andChangeTypeIn(deliveryEventCodes);
		List<DeliveryOrderLogDO> deliveryOrderLogDOList = deliveryOrderLogDOMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(deliveryOrderLogDOList)) {
			return Maps.newHashMap();
		}

		return deliveryOrderLogDOList.stream().collect(Collectors.groupingBy(DeliveryOrderLogDO::getDeliveryOrderId,
				Collectors.mapping(this::buildDoToDomain, Collectors.toList())));
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Map<Long, List<DeliveryOrderLog>> getDeliveryOrderLogByDeliveryOrderIds(List<Long> deliveryIds) {
		if (CollectionUtils.isEmpty(deliveryIds)) {
			return Maps.newHashMap();
		}
		DeliveryOrderLogDOExample example = new DeliveryOrderLogDOExample();
		example.createCriteria().andDeliveryOrderIdIn(deliveryIds);
		List<DeliveryOrderLogDO> deliveryOrderLogDOS = deliveryOrderLogDOExMapper.selectByExampleSlave(example);
		if (CollectionUtils.isEmpty(deliveryOrderLogDOS)) {
			return Maps.newHashMap();
		}

		List<DeliveryOrderLog> deliveryOrderLogs = deliveryOrderLogDOS.stream().map(this::buildDoToDomain).collect(Collectors.toList());

		return deliveryOrderLogs.stream().collect(Collectors.groupingBy(DeliveryOrderLog::getDeliveryId));
	}

	private DeliveryOrderLog buildDoToDomain(DeliveryOrderLogDO log) {
		return new DeliveryOrderLog(log.getDeliveryOrderId(), DeliveryEventEnum.valueOf(log.getChangeType()), new DeliveryChangeInfo(), log.getChangeTime());
	}

	private boolean isRecordAlreadyExist(Long deliveryId, DeliveryEventEnum deliveryEvent, LocalDateTime changeTime) {
		try {
			ZebraForceMasterHelper.forceMasterInLocalContext();

			DeliveryOrderLogDOExample example = new DeliveryOrderLogDOExample();
			example.createCriteria()
					.andDeliveryOrderIdEqualTo(deliveryId)
					.andChangeTypeEqualTo(deliveryEvent.getCode())
					.andChangeTimeEqualTo(changeTime);
			List<DeliveryOrderLogDO> records = deliveryOrderLogDOMapper.selectByExample(example);
			return Optional.ofNullable(records).orElse(new ArrayList<>()).size() > 0;

		} finally {
			ZebraForceMasterHelper.clearLocalContext();
		}
	}

	private DeliveryOrderLogDO translate(Long deliveryId, Integer deliveryStatus, DeliveryEventEnum deliveryEvent, String changeInfoJson, LocalDateTime changeTime) {
		DeliveryOrderLogDO record = new DeliveryOrderLogDO();
		record.setDeliveryStatus(deliveryStatus);
		record.setDeliveryOrderId(deliveryId);
		record.setChangeType(deliveryEvent.getCode());
		record.setChangeInfo(changeInfoJson);
		record.setChangeTime(changeTime);
		return record;
	}


}
