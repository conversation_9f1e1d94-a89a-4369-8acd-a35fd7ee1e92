package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryOperationApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.push.PushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.DeliveryLaunchFailedEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryWarnEventPublisher;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.configuration.TMSConstant;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.TransferOrderMarkEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.PickSelectRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.ProxyDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryLaunchCommandMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.service.DiscreetDeliveryService;
import com.sankuai.meituan.shangou.empower.tms.delivery.model.config.PickSelectStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.OrderCheckUtil;
import com.sankuai.qnh.ofc.ofw.common.mq.message.FulfillmentOrderDownstreamMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

@Slf4j
@Service
public class OFCOfwFulfillmentDownListener extends AbstractDeadLetterConsumer{

    @Autowired
    private OrderSystemClient orderSystemClient;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    @Autowired
    private ProxyDeliveryPlatformClient deliveryPlatformClient;

    @Resource
    private PickSelectRemoteService pickSelectRemoteService;

    @Resource
    private MafkaDelayMessageProducer<DeliveryLaunchCommandMessage> deliveryDelayLaunchMessageProducer;

    @Resource
    private TenantRemoteService tenantRemoteService;
    @Resource
    private DiscreetDeliveryService discreetDeliveryService;

    @Resource
    private DeliveryWarnEventPublisher deliveryWarnEventPublisher;

    @Resource
    private PushClient pushClient;

    @Resource
    private DeliveryTimeOutCheckService deliveryTimeOutCheckService;

    @Resource
    private DeliveryOperationApplicationService deliveryOperationApplicationService;


    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.OFC_OFW_FULFILLMENT_DOWN_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("开始消费 订单转单消息 msg:{}",message);
        FulfillmentOrderDownstreamMessage msg = translateMessage(message);
        return doHandle(msg);
    }

    private FulfillmentOrderDownstreamMessage translateMessage(MafkaMessage mafkaMessage) {
        try {
            FulfillmentOrderDownstreamMessage message = translateMessage(mafkaMessage, FulfillmentOrderDownstreamMessage.class);
            Preconditions.checkNotNull(message, "empty mafkaMessage");
            Preconditions.checkNotNull(message.getTenantId(), "发起配送消息tenantId为空");
            Preconditions.checkNotNull(message.getWarehouseId(), "发起配送消息shopId为空");
            Preconditions.checkNotNull(message.getUnifyOrderId(), "发起配送消息unifyOrderId为空");
            Preconditions.checkNotNull(message.getFulfillmentOrderId(), "发起配送消息履约orderId为空");
            return message;

        } catch (Exception e) {
            log.error("解析线下推广自提订单下发消息失败:{}", mafkaMessage, e);
            return null;
        }
    }

    private long calculateBookingOrderDelayMillis4NewSupply(Integer minutes,
                                                            LocalDateTime estimatedDeliveryTime) {
        if(minutes == null || minutes<=0){
            return 0L;
        }
        // 预计送达前 minutes 分钟发起配送
        LocalDateTime launchDeliveryTime = estimatedDeliveryTime.minusMinutes(minutes.longValue());
        Duration duration = Duration.between(LocalDateTime.now(), launchDeliveryTime);
        long delayMillis = duration.toMillis();
        return delayMillis;
    }

    public ConsumeStatus doHandle(FulfillmentOrderDownstreamMessage msg){
        if(msg == null){
            log.info("数据不完整");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(Long.parseLong(msg.getUnifyOrderId()),false);
        OrderInfo orderInfo=orderInfoResult.getInfo();
        if(orderInfo==null){
            log.info("查询订单失败");
            return ConsumeStatus.RECONSUME_LATER;
        }

        DynamicOrderBizType dynamicOrderBizType = ObjectUtils.defaultIfNull(
                DynamicOrderBizType.findOf(orderInfo.getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
        Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(msg.getTenantId(), msg.getFulfillmentWarehouseId(),dynamicOrderBizType.getChannelId());
        if (!opDeliveryPoi.isPresent()) {
            log.info("DeliveryStore[{}] is illegal, will give up launchDelivery", orderInfo.getOrderKey().getStoreId());
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        boolean isMedicineUW = tenantRemoteService.isMedicineUnmannedWarehouse(orderInfo.getOrderKey().getTenantId());

        int result = checkOrder(orderInfo,isMedicineUW,opDeliveryPoi.get());
        if(result == 0){
            return ConsumeStatus.CONSUME_SUCCESS;
        }else if(result == 1){
            return CONSUME_FAILURE;
        }

        Optional<DeliveryOrder> optionalDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(orderInfo.getOrderKey().getOrderId(),msg.getFulfillmentOrderId(),opDeliveryPoi.get().getTenantId(),opDeliveryPoi.get().getStoreId());
        if(optionalDeliveryOrder.isPresent() && optionalDeliveryOrder.get().getStatus() != DeliveryStatusEnum.INIT){
            log.info("已经存在激活运单：{}", orderInfo.getOrderKey().getStoreId());
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        if(opDeliveryPoi.get().getDeliveryPlatform() == DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM){
            log.info("无需发起配送 orderId:{}",msg.getOrderId());
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        if(opDeliveryPoi.get().getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint() == ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE){
            log.info("发单节点为拣货完成，无需发起配送 orderId:{}",msg.getOrderId());
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        DeliveryChannelEnum channelEnum = DeliveryChannelEnum.MALT_FARM;
        if(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM == opDeliveryPoi.get().getDeliveryPlatform()){
            channelEnum = DeliveryChannelEnum.DAP_DELIVERY;
        }else if(DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY == opDeliveryPoi.get().getDeliveryPlatform()){
            channelEnum = DeliveryChannelEnum.MERCHANT_DELIVERY;
        }

        if(channelEnum == DeliveryChannelEnum.MERCHANT_DELIVERY && orderInfo.isBookingOrder()){
            LocalDateTime estimatedDeliveryTime = orderInfo.getEstimatedDeliveryTime();
            if(orderInfo.getEstimatedDeliveryEndTime()!=null){
                estimatedDeliveryTime = orderInfo.getEstimatedDeliveryEndTime();
            }
            PickSelectStoreConfig pickSelectStoreConfig = pickSelectRemoteService.queryFulfillStoreConfig(
                    opDeliveryPoi.get().getTenantId(), opDeliveryPoi.get().getStoreId(), orderInfo.getOrderBizType());
            Integer pushTime=pickSelectStoreConfig ==null || pickSelectStoreConfig.getBookingPushDownTime()==null ? 40:pickSelectStoreConfig.getBookingPushDownTime();
            long delayMillis = calculateBookingOrderDelayMillis4NewSupply(pushTime,estimatedDeliveryTime);
            if (delayMillis >= TMSConstant.MAFKA_DELAY_MESSAGE_MILLIS_MIN) {

                DeliveryLaunchCommandMessage launchCommandMessage = new DeliveryLaunchCommandMessage();
                launchCommandMessage.setFulfillOrderId(msg.getFulfillmentOrderId());
                launchCommandMessage.setTenantId(msg.getTenantId());
                launchCommandMessage.setShopId(msg.getFulfillmentWarehouseId());
                launchCommandMessage.setOrderId(orderInfo.getOrderKey().getOrderId());
                launchCommandMessage.setOrderSource(orderInfo.getOrderSource());
                launchCommandMessage.setOrderBizType(orderInfo.getOrderBizType());
                launchCommandMessage.setViewOrderId(orderInfo.getChannelOrderId());
                launchCommandMessage.setPlatformSource(PlatformSourceEnum.OFC.getCode());
                // 发起毫秒级别延时消息
                deliveryDelayLaunchMessageProducer.sendDelayMessageInMillis(launchCommandMessage, delayMillis);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        }
        // 替换货号
        try{
            if(isMedicineUW){
                discreetDeliveryService.replaceMedicineGoodsCode2SkuName(orderInfo.getOrderKey(), orderInfo, orderSystemClient,
                        msg.getFulfillmentWarehouseId());
            }else {
                discreetDeliveryService.replaceNotMedicineUnmannedGoodsCode2SkuName(orderInfo.getOrderKey(), orderInfo, orderSystemClient
                        , msg.getFulfillmentWarehouseId());
            }
        }catch (Exception e){
            log.error("替换货号失败",e);
        }

        // 发起新的配送单
        DeliveryOrder deliveryOrder = null;
        if(!optionalDeliveryOrder.isPresent()){
            deliveryOrder = DeliveryOrder.fromBusinessCustomer(orderInfo, channelEnum.getCode(), StringUtils.EMPTY);
            deliveryOrder.setPlatformSourceEnum(PlatformSourceEnum.OFC);
            deliveryOrder.setFulfillmentOrderId(msg.getFulfillmentOrderId());
            deliveryOrder.setTenantId(msg.getTenantId());
            deliveryOrder.setStoreId(msg.getFulfillmentWarehouseId());
            deliveryOrder.setOrderKey(new OrderKey(msg.getTenantId(), msg.getFulfillmentWarehouseId(), orderInfo.getOrderKey().getOrderId()));
            deliveryOrderRepository.saveDeliveryOrder(deliveryOrder);
        }else {
            deliveryOrder = optionalDeliveryOrder.get();
        }

        Optional<LaunchFailure> failureOptional=deliveryPlatformClient.launch(opDeliveryPoi.get(),orderInfo,deliveryOrder, TransferOrderMarkEnum.TRANSFER_ORDER.getCode());
        if(failureOptional.isPresent()){

            String failReason = failureOptional.get().getFailureMessage();
            deliveryWarnEventPublisher.postEvent(new DeliveryLaunchFailedEvent(deliveryOrder, failReason));
            deliveryOrder.onChange(DeliveryEventEnum.DELIVERY_REJECT, failureOptional.get().getExceptionInfo(), null, LocalDateTime.now());

            if (failureOptional.get().isNeedRetry()) {
                //配送发起失败，需要重试
                return ConsumeStatus.RECONSUME_LATER;
            } else {
                //配送发起失败，不支持重试，直接推到终态
                pushClient.pushDeliveryException(deliveryOrder);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        }
        if(deliveryOrder.getStatus() == DeliveryStatusEnum.INIT){
            deliveryOrder.onStatusChangeWithOutLog(DeliveryStatusEnum.DELIVERY_LAUNCHED,LocalDateTime.now());
            deliveryOrderRepository.saveDeliveryOrder(deliveryOrder);
        }
        //监控配送超时
        deliveryTimeOutCheckService.triggerDeliveryTimeOutCheck(deliveryOrder);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private int checkOrder(OrderInfo orderInfo, boolean isMedicineUW, DeliveryPoi deliveryPoi){
        if(MccConfigUtils.transAllDegreeSwitch()){
            return -1;
        }

        if(OrderCheckUtil.isDownOrder(orderInfo,isMedicineUW,deliveryPoi)){
            log.info("订单{}降级，暂不发起配送",orderInfo.getOrderKey().getOrderId());
            return 0;
        }

        if (!OrderCheckUtil.isLockOrderV2(orderInfo, deliveryPoi)){
            // 不是锁单2.0 走1.0流程
            if (OrderCheckUtil.isLockedOrder(orderInfo, isMedicineUW)) {
                if (orderInfo.getIsLocked() != null && orderInfo.getIsLocked()) {
                    log.info("当前订单处于锁单状态，暂不发起配送。租户：{}，门店：{}",orderInfo.getOrderKey().getTenantId(),orderInfo.getOrderKey().getStoreId());
                    Cat.logEvent(AbstractLaunchDeliveryListener.LOCK, AbstractLaunchDeliveryListener.LOCK_ORDER);
                    return 0;
                }
                if (orderInfo.getIsLocked() != null && !orderInfo.getIsLocked()) {
                    Optional<Failure> failure = deliveryOperationApplicationService.paoTuiLockStatusNotify(orderInfo);
                    if (failure.isPresent()) {
                        return 1;
                    }
                    return 0;
                }
            }
        }
        return -1;
    }

}
