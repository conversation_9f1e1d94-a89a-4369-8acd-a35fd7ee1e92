package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.poi.client.relation;

import com.dianping.cat.Cat;
import com.google.common.collect.Sets;
import com.sankuai.meituan.banma.thrift.delivery3pl.open.exception.BmOpen3PLException;
import com.sankuai.meituan.banma.thrift.delivery3pl.open.platform.iface.BmOpen3PLPoiThriftIface;
import com.sankuai.meituan.banma.thrift.delivery3pl.open.platform.vo.Bm3PLChannel;
import com.sankuai.meituan.banma.thrift.delivery3pl.open.platform.vo.Open3PLCompanyInfo;
import com.sankuai.meituan.banma.thrift.delivery3pl.open.platform.vo.OpenBmConfig3PLPoiBindingParam;
import com.sankuai.meituan.banma.thrift.delivery3pl.open.platform.vo.OpenBmPoi3PLCompanyRelSyncParam;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.SyncTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryBaseException;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.relation.DeliveryChannelInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.relation.DeliveryPoiRelationClient;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import deps.redis.clients.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/9
 */
@Slf4j
@Component
public class AggregationDeliveryPoiRelationClient implements DeliveryPoiRelationClient {

	/**
	 * 绑定配送商门店时，需要同时传入配送商商户号的配送商
	 */
	private static final Set<Integer> SYNC_WITH_THIRD_COMPANY_SOURCE = Sets.newHashSet(
			DeliveryChannelEnum.AGGREGATE_DELIVERY_DADA.getCode());

	@Resource
	private BmOpen3PLPoiThriftIface.Iface openBm3PLPoiThriftClient;

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public void syncStoreChannelInfo(DeliveryChannelInfo channelInfo) {
		// 仅达达配送使用这个老接口去绑定配送商门店，其余配送商使用三方运力平台提供的新的门店绑定接口
		if (!SYNC_WITH_THIRD_COMPANY_SOURCE.contains(channelInfo.getDeliveryChannelId())) {
			syncStoreChannelInfoV2(channelInfo);
			return;
		}
		try {
			OpenBmPoi3PLCompanyRelSyncParam param = new OpenBmPoi3PLCompanyRelSyncParam();
			param.setChannel(Bm3PLChannel.SHANGOU_FUNENG.getValue());
			param.setPoiId(String.valueOf(channelInfo.getStoreId()));
			param.setSyncType(channelInfo.getSyncType());
			param.setThirdCompanyCode(channelInfo.getDeliveryChannelId());
			param.setThirdCompanyPoiId(channelInfo.getDeliveryChannelPoiId());
			param.setThirdCompanySourceId(channelInfo.getDeliveryChannelMerchantId());

			openBm3PLPoiThriftClient.syncPoi3PLCompanyRel(param);

			catLogEvent4ChannelPoiMng(channelInfo, true);
		} catch (BmOpen3PLException e) {
			catLogEvent4ChannelPoiMng(channelInfo, false);
			log.error("syncStoreChannelInfo known_error", e);
			throw new DeliveryBaseException(e.getMsg());
		} catch (Exception e) {
			catLogEvent4ChannelPoiMng(channelInfo, false);
			log.error("syncStoreChannelInfo error", e);
			throw new DeliveryBaseException("系统异常，请稍后重试");
		}
	}

	private void syncStoreChannelInfoV2(DeliveryChannelInfo channelInfo) {
		try {
			OpenBmConfig3PLPoiBindingParam param = new OpenBmConfig3PLPoiBindingParam();
			param.setChannel(Bm3PLChannel.SHANGOU_FUNENG.getValue());
			// 由于在赋能侧暂无商户号概念来管理配送门店，所以此处商户号暂时传入门店 ID
			param.setSourceId(String.valueOf(channelInfo.getStoreId()));
			param.setPoiId(String.valueOf(channelInfo.getStoreId()));
			param.setThirdCompanyCode(channelInfo.getDeliveryChannelId());
			param.setThirdCompanyPoiId(channelInfo.getDeliveryChannelPoiId());
			param.setThirdCompanyPoiConfig(channelInfo.getDeliveryChannelPoiExt());
			param.setType(channelInfo.getSyncType());
			log.info("BmOpen3PLPoiThriftIface.syncConfig3PLPoiBinding param:{}", param);
			openBm3PLPoiThriftClient.syncConfig3PLPoiBinding(param);

			catLogEvent4ChannelPoiMng(channelInfo, true);
		} catch (BmOpen3PLException e) {
			catLogEvent4ChannelPoiMng(channelInfo, false);
			log.error("syncStoreChannelInfoV2 known_error", e);
			throw new DeliveryBaseException(e.getMsg());
		} catch (Exception e) {
			catLogEvent4ChannelPoiMng(channelInfo, false);
			log.error("syncStoreChannelInfoV2 error", e);
			throw new DeliveryBaseException("系统异常，请稍后重试");
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public void unbindDeliveryCompanyPoi(Long storeId, Integer deliveryChannelId, String deliveryChannelPoiId) {
		DeliveryChannelInfo deliveryChannelInfo = new DeliveryChannelInfo();
		deliveryChannelInfo.setDeliveryChannelId(deliveryChannelId);
		deliveryChannelInfo.setDeliveryChannelPoiId(deliveryChannelPoiId);
		deliveryChannelInfo.setStoreId(storeId);
		deliveryChannelInfo.setSyncType(SyncTypeEnum.DELETE.getValue());
		syncStoreChannelInfoV2(deliveryChannelInfo);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryChannelInfo> queryStoreChannelInfo(Long storeId) {
		try {
			List<Open3PLCompanyInfo> result = openBm3PLPoiThriftClient
					.getOpen3PLCompanyInfoListByPoiId(Bm3PLChannel.SHANGOU_FUNENG.getValue(), String.valueOf(storeId));
			if (CollectionUtils.isNotEmpty(result)) {
				return result.stream()
						.map(it -> DeliveryChannelInfo
								.builder()
								.storeId(storeId)
								.deliveryChannelPoiId(it.getThirdCompanyPoiId())
								.deliveryChannelPoiExt(it.getThirdCompanyPoiConfig())
								.deliveryChannelName(it.getName())
								.deliveryChannelMerchantId(it.getThirdCompanySourceId())
								.openFlag(it.getOpenFlag())
								.deliveryChannelId(it.getCode())
								.build()
						)
						.collect(Collectors.toList());
			}
		} catch (BmOpen3PLException e) {
			log.error("queryStoreChannelInfo known_error", e);
			throw new DeliveryBaseException(e.getMessage());
		} catch (Exception e) {
			log.error("queryStoreChannelInfo error", e);
			throw new DeliveryBaseException(FailureCodeEnum.SYSTEM_ERROR.getMessage());

		}
		return Lists.newArrayList();
	}

	private void catLogEvent4ChannelPoiMng(DeliveryChannelInfo channelInfo, boolean isSuccess) {
		Integer channelId = channelInfo.getDeliveryChannelId();
		String successName = "SUCCESS_" + channelId;
		String failName = "FAIL_" + channelId;

		Optional.ofNullable(SyncTypeEnum.findByValue(channelInfo.getSyncType()))
				.ifPresent(syncType ->
						Cat.logEvent("CHANNEL_POI_" + syncType.name(), isSuccess ? successName : failName)
				);
	}
}
