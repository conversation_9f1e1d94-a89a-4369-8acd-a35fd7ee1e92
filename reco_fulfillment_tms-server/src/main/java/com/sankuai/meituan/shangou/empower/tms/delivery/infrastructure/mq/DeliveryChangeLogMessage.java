package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryChangeLogMessage {

	/**
	 * 运单id
	 */
	private Long deliveryId;

	/**
	 * 运单变更事件
	 */
	private Integer changeEvent;

	/**
	 * 运单变更补充信息
	 * 运单异常：异常类型+详细描述
	 * 骑手改派：改派前/后骑手信息
	 */
	private String changeInfoJson;

	/**
	 * 运单变更发生事件
	 */
	private Long changeTime;

	/**
	 * 运单当前状态
	 */
	private Integer deliveryOrderStatus;

	public DeliveryChangeLogMessage(Long deliveryId, Integer changeEvent, String changeInfoJson, LocalDateTime changeTime) {
		this.deliveryId = deliveryId;
		this.changeEvent = changeEvent;
		this.changeInfoJson = changeInfoJson;
		this.changeTime = TimeUtil.toMilliSeconds(changeTime);
	}
}
