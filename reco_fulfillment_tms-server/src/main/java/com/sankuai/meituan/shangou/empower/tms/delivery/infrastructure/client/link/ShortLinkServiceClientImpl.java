package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.link;

import com.dianping.mobileossapi.dto.operate.ShortUrlRequestThrift;
import com.dianping.mobileossapi.dto.operate.ShortUrlResultThrift;
import com.dianping.mobileossapi.service.operate.OperateServiceThrift;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.link.ShortLinkServiceClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-05-13
 * @email <EMAIL>
 */
@Slf4j
@Service
public class ShortLinkServiceClientImpl implements ShortLinkServiceClient {

    @Resource
    private OperateServiceThrift shortLinkOperateServiceThrift;
    @Value("${short-link.biz-type}")
    private Integer bizType;

    private static final int SHORT_LINK_SUCCESS_CODE = 1;

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @Retryable(value = CommonRuntimeException.class, maxAttempts = 3, backoff = @Backoff(delay = 1000L, multiplier = 1.5))
    public String createShortLink(String url) {
        ShortUrlRequestThrift request = new ShortUrlRequestThrift();
        request.setOriginUrl(url);
        request.setBizType(bizType);
        request.setAppName("com.sankuai.sgfulfillment.tms");
        request.setExpireDays(7);

        ShortUrlResultThrift shortUrlResultThrift = shortLinkOperateServiceThrift.genShortLinkWithParam(request);
        log.info("invoke shortLinkOperateServiceThrift.genShortLinkWithParam, request = {}, response = {}",request, shortUrlResultThrift);
        if (!Objects.equals(shortUrlResultThrift.getStatus(), SHORT_LINK_SUCCESS_CODE) || StringUtils.isBlank(shortUrlResultThrift.getShortUrl())) {
            throw new CommonRuntimeException("生成短链失败");
        }
        return shortUrlResultThrift.getShortUrl();
    }

}
