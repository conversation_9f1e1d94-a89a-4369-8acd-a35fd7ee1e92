package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.tenant.mq.PoiNotifyMessage;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.DeliveryTenantPoiSyncRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.ChannelStoreRelation;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.wrapper.AggDeliveryWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ConfigModuleEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.BookingOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.DeliveryConfigApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.SaveConfigCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantChannelStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.AggDeliveryPlatformInfo;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.maltfarm.MaltFarmDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.LionConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.map.MapClient;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigThriftService;
import com.sankuai.shangou.logistics.delivery.poi.dto.TVoid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.shiro.util.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.meituan.mafka.client.consumer.ConsumeStatus.*;

/**
 * 监听中台新建门店的消息.
 *
 * <AUTHOR>
 * @since 2021/9/28 17:50
 */
@Slf4j
@Component
public class StoreChangeListener extends AbstractDeadLetterConsumer {

    @Resource
    private DeliveryConfigApplicationService deliveryConfigApplicationService;
    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;
    @Resource(name = "mapClientImpl")
    private MapClient mapClient;

    @Resource
    private TenantSystemClient tenantSystemClient;
    @Resource
    private DeliveryPlatformClient deliveryPlatformClient;

    @Resource
    private SelfDeliveryPoiConfigThriftService selfDeliveryPoiConfigThriftService;

    @Resource
    private DeliveryConfigurationThriftService deliveryConfigurationThriftService;
    @Autowired
    private AggDeliveryWrapper aggDeliveryWrapper;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.NEW_STORE_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("开始消费中台门店变更消息：{}", message);
        PoiNotifyMessage poiNotifyMessage = translateMessage(message);
        if (poiNotifyMessage == null) {
            return CONSUME_SUCCESS;
        }

        switch (poiNotifyMessage.getNotifyType()) {
            case POI_CREATE:
                return tryCreateStore(poiNotifyMessage);

            case POI_MODIFY:
                try {
                    // 同步门店信息变更
                    if (MccConfigUtils.getTenantPoiChangeSyncSwitch()) {
                        syncTenantPoiChange(poiNotifyMessage);
                    }
                } catch (Exception e) {
                    log.error("门店变更同步同步青云失败，msg={}", poiNotifyMessage, e);
                }
                return tryUpdateStore(
                        poiNotifyMessage.getPoiInfoDto().getTenantId(),
                        poiNotifyMessage.getPoiInfoDto().getPoiId(),
                        poiNotifyMessage.getPoiInfoDto().getPoiAddress()
                );
            case STORE_BIND_SHAREABLE_WAREHOUSE:
                return tryShareableWareHouseBind(poiNotifyMessage);
            default:
                return CONSUME_SUCCESS;
        }
    }

    /**
     * 同步租户变更信息
     * 
     * @param poiNotifyMessage
     * @return
     */
    private ConsumeStatus syncTenantPoiChange(PoiNotifyMessage poiNotifyMessage) {
        Long tenantId = Objects.nonNull(poiNotifyMessage.getTenantInfoDto()) ? poiNotifyMessage.getTenantInfoDto().getTenantId() : null;
        Assert.notNull(tenantId,"租户id为空，无法同步");
        Long poiId = Objects.nonNull(poiNotifyMessage.getPoiInfoDto()) ? poiNotifyMessage.getPoiInfoDto().getPoiId() : null;
        Assert.notNull(poiId, "门店id为空，无法同步");

        // 拦截非歪马的中台门店，判断未开通青云不同步，返回消费成功
        List<DeliveryPoi> deliveryPoiList = deliveryPoiRepository.queryAllDeliveryPoi(tenantId, poiId);
        List<Integer> platformList = Optional.ofNullable(deliveryPoiList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(DeliveryPoi::getDeliveryPlatform)
                .filter(Objects::nonNull)
                .map(DeliveryPlatformEnum::getCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        platformList.addAll(Optional.ofNullable(deliveryPoiList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(DeliveryPoi::getTurnSecondDeliveryPlatformCondition)
                .filter(Objects::nonNull)
                .map(SecondDeliveryConfig::getSecondPlatformCodeCode)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList()));

        if (!platformList.contains(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode())) {
            log.info("拦截非歪马的中台门店，未开通青云配送平台不同步租户门店变更，同步消息{}", poiNotifyMessage);
            return CONSUME_SUCCESS;
        }

        DeliveryTenantPoiSyncRequest request = translateTenantPoiSyncMsg(poiNotifyMessage);

        // 同步青云配送平台
        request.setPlatformCode(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode());
        String categoryCode = aggDeliveryWrapper.getCategoryCode(tenantId);
        request.setCategoryCode(categoryCode);
        Optional<Failure> failure = deliveryPlatformClient.syncTenantPoi(request);
        if (failure.isPresent()) {
            log.error("syncTenantPoiChange error, msg:{}, failure:{}", JSON.toJSONString(poiNotifyMessage), failure.get());
            return CONSUME_FAILURE;
        }

        return CONSUME_SUCCESS;
    }

    private DeliveryTenantPoiSyncRequest translateTenantPoiSyncMsg(PoiNotifyMessage poiNotifyMessage) {
        Assert.notNull(poiNotifyMessage,"租户门店消息为空");
        PoiInfoDto poiInfoDto = poiNotifyMessage.getPoiInfoDto();
        Assert.notNull(poiInfoDto,"无门店数据");
        return DeliveryTenantPoiSyncRequest.builder()
                .channelShopId(String.valueOf(poiInfoDto.getPoiId()))
                .channelShopName(poiInfoDto.getPoiName())
                .channelShopAddress(poiInfoDto.getPoiAddress())
                .longitude(poiInfoDto.getLongitude())
                .latitude(poiInfoDto.getLatitude())
                .cityId(Objects.nonNull(poiInfoDto.getDistrict()) ? poiInfoDto.getDistrict().getCityId() : null)
                .channelShopStatus(poiInfoDto.getPoiStatus())
                .contact(poiInfoDto.getMobile()).build();
    }

    public ConsumeStatus tryShareableWareHouseBind(PoiNotifyMessage poiNotifyMessage) {
        if (!MccConfigUtils.getTenantPoiFastAuthSwitch()) {
            log.info("绑定信息开关关闭");
            return CONSUME_SUCCESS;
        }
        try {
            Long tenantId = poiNotifyMessage.getPoiInfoDto().getTenantId();
            Long storeId = poiNotifyMessage.getPoiInfoDto().getPoiId();
            Long warehouseId = poiNotifyMessage.getStoreBindingShareableWarehouse().getAfterBindingShareableWarehouse()
                    .getPoiId();
            List<DeliveryPoi> deliveryPoiList = deliveryPoiRepository.queryAllDeliveryPoi(tenantId, warehouseId);
            if (CollectionUtils.isEmpty(deliveryPoiList)) {
                log.info("无配送门店");
                return CONSUME_SUCCESS;
            }
            Optional<DeliveryPoi> optionalDeliveryPoi = deliveryPoiList.stream().filter(
                    deliveryPoi -> deliveryPoi.getDeliveryPlatform() == DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM)
                    .findAny();
            if (!optionalDeliveryPoi.isPresent()) {
                log.info("无开通青云门店");
                return CONSUME_SUCCESS;
            }
            DeliveryPoi deliveryPoi = optionalDeliveryPoi.get();
            Map<Long, List<Long>> relationMap = tenantSystemClient.batchQueryRelationMapByPoiIds(tenantId,
                    Arrays.asList(warehouseId), true);
            Set<Long> poiSet = new HashSet<>();
            poiSet.add(storeId);
            if (MapUtils.isNotEmpty(relationMap) && CollectionUtils.isNotEmpty(relationMap.get(warehouseId))) {
                poiSet.addAll(relationMap.get(warehouseId));
            }
            Map<Long, List<TenantChannelStoreInfo>> channelStoreMap = tenantSystemClient
                    .queryChannelStoreDetailInfoList(tenantId, new ArrayList<>(poiSet));
            Set<Long> wmStoreIdSet = new HashSet<>();
            List<ChannelStoreRelation> relationList = new ArrayList<>();
            if (MapUtils.isNotEmpty(channelStoreMap)) {
                for (Long poiId : channelStoreMap.keySet()) {
                    List<TenantChannelStoreInfo> channelStoreInfoList = channelStoreMap.get(poiId);
                    if (CollectionUtils.isEmpty(channelStoreInfoList)) {
                        relationList.add(new ChannelStoreRelation(poiId,0L));
                        continue;
                    }
                    Optional<TenantChannelStoreInfo> mtStoreInfo = channelStoreInfoList.stream()
                            .filter(tenantChannelStoreInfo -> tenantChannelStoreInfo.getChannelId()
                                    .equals(ChannelType.MEITUAN.getValue()))
                            .findFirst();
                    mtStoreInfo.ifPresent(tenantChannelStoreInfo -> wmStoreIdSet
                            .add(NumberUtils.toLong(tenantChannelStoreInfo.getChannelPoiId())));
                    if(mtStoreInfo.isPresent()){
                        String channelPoiId = mtStoreInfo.get().getChannelInnerPoiId();
                        if(StringUtils.isEmpty(channelPoiId)){
                            channelPoiId = mtStoreInfo.get().getChannelPoiId();
                        }
                        relationList.add(new ChannelStoreRelation(poiId,NumberUtils.toLong(channelPoiId)));
                    }else {
                        relationList.add(new ChannelStoreRelation(poiId,0L));
                    }

                }
            }
            Optional<Failure> failure = deliveryPlatformClient.deliveryFastAuth(deliveryPoi,
                    new ArrayList<>(wmStoreIdSet),relationList);
            if (failure.isPresent() && failure.get().isNeedRetry()) {
                return RECONSUME_LATER;
            }
            return CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("tryShareableWareHouseBind error", e);
            return CONSUME_FAILURE;
        }
    }

    private ConsumeStatus tryCreateStore(PoiNotifyMessage poiNotifyMessage) {
        if (!isNeedHandled(poiNotifyMessage)) {
            return CONSUME_SUCCESS;
        }
        Long tenantId = poiNotifyMessage.getPoiInfoDto().getTenantId();
        Long poiId = poiNotifyMessage.getPoiInfoDto().getPoiId();
        log.info("开始处理歪马新建门店消息. tenantId:{}, poiId:{}", tenantId, poiId);
        try {
            SaveConfigCmd cmd = buildWaimaNewStoreConfig(tenantId, poiId);
            DeliveryPoi deliveryPoi = deliveryConfigApplicationService.buildDeliveryPoiConfig(cmd);
            deliveryConfigApplicationService.saveStoreConfiguration(cmd, deliveryPoi);
            tryInitSelfDeliveryConfig(tenantId, poiId);
            return CONSUME_SUCCESS;
        } catch (Exception e) {
            log.warn("新建歪马门店消息处理失败. poiNotifyMessage:{}", poiNotifyMessage, e);
            return CONSUME_FAILURE;
        }
    }

    //初始化自营门店失败暂时不阻塞流程
    private void tryInitSelfDeliveryConfig(Long tenantId, Long poiId) {
        try {
            TResult<TVoid> result = selfDeliveryPoiConfigThriftService.initSelfDeliveryConfig(tenantId, poiId);

            if (result == null || !result.isSuccess()) {
                log.warn("初始化自营配送配置失败, resp:{}", result);
                Cat.logEvent("DH_ADAPT_DAP", "INIT_SELF_DELIVERY_CONFIG_FAIL");
            }
        } catch (Exception e) {
            log.warn("初始化自营配送配置失败", e);
            Cat.logEvent("DH_ADAPT_DAP", "INIT_SELF_DELIVERY_CONFIG_ERROR");
        }

    }

    private ConsumeStatus tryUpdateStore(Long tenantId, Long storeId, String newAddress) {
        //目前仅牵牛花门店需要同步更新门店地址
        if (LionConfigUtils.isGloryStore(tenantId, storeId)) {
            //查门店信息
            List<DeliveryPoi> deliveryPoiList = deliveryPoiRepository.queryAllDeliveryPoi(tenantId, storeId);
            if (CollectionUtils.isEmpty(deliveryPoiList)) {
                log.info("未初始化配送门店，将放弃更新门店地址");
                return CONSUME_SUCCESS;
            }

            List<MaltFarmDeliveryPoi> updatePoiList=new ArrayList<>();

            for (DeliveryPoi deliveryPoi : deliveryPoiList){

                //如果不是麦芽田配送，不管
                if (!(deliveryPoi instanceof MaltFarmDeliveryPoi)) {
                    log.info("不是麦芽田配送，将放弃更新门店地址");
                    continue;
                }
                MaltFarmDeliveryPoi maltFarmDeliveryPoi = (MaltFarmDeliveryPoi) deliveryPoi;

                if (maltFarmDeliveryPoi.getStoreAddress() != null && StringUtils.equals(maltFarmDeliveryPoi.getStoreAddress().getAddressDetail(), newAddress)) {
                    log.info("地址未变更，不触发更新");
                    continue;
                }

                updatePoiList.add(maltFarmDeliveryPoi);
            }
            if(CollectionUtils.isEmpty(updatePoiList)){
                return CONSUME_SUCCESS;
            }

            //查询经纬度, 更新门店地址
            Result<CoordinatePoint> coordinateQueryResult = mapClient.queryCoordinatesByDetailAddress(newAddress);
            if (coordinateQueryResult.isFail()) {
                log.warn("查询门店坐标失败, tenantId:{}, storeId:{}, address:{}, failure:{}",
                        tenantId, storeId, newAddress, coordinateQueryResult.getFailure()
                );
                return coordinateQueryResult.getFailure().isNeedRetry() ? CONSUME_FAILURE : CONSUME_SUCCESS;
            }

            for (MaltFarmDeliveryPoi maltFarmDeliveryPoi : updatePoiList){
                //持久化
                maltFarmDeliveryPoi.setStoreAddress(new Address(newAddress, CoordinateTypeEnum.MARS, coordinateQueryResult.getInfo()));
                deliveryPoiRepository.saveDeliveryPoi(maltFarmDeliveryPoi);
            }
        }
        return CONSUME_SUCCESS;
    }

    /**
     * 构造保存歪马门店初始化配置的请求.
     *
     * @param tenantId 租户 ID
     * @param poiId    门店 ID
     * @return
     */
    private SaveConfigCmd buildWaimaNewStoreConfig(Long tenantId, Long poiId) {
        SaveConfigCmd cmd = SaveConfigCmd.builder()
                .tenantId(tenantId)
                .storeId(poiId)
                .module(ConfigModuleEnum.PLATFORM.getValue())
                .deliveryLaunchType(DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY)
                .channelInfo(null)
                .deliveryPlatformInfo(new AggDeliveryPlatformInfo(
                        DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode(), 1, DynamicChannelType.MEITUAN.getChannelId())
                )
                .immediateOrderDeliveryLaunchPointConfig(
                        new DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(
                                ImmediateOrderDeliveryLaunchPointEnum.ORDER_PAID, 0)
                )
                .bookingOrderDeliveryLaunchPointConfig(
                        new DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(
                                BookingOrderDeliveryLaunchPointEnum.BEFORE_DELIVERY, 40)
                )
                .autoLaunchStrategyId(null)
                .build();
        return cmd;
    }

    /**
     * 将 mafka 消息对象转换为门店通知消息对象.
     *
     * @param message mafka 消息
     * @return 门店通知
     */
    private PoiNotifyMessage translateMessage(MafkaMessage message) {
        try {
            PoiNotifyMessage poiNotify = translateMessageWithBodyHolder(message, PoiNotifyMessage.class);
            Preconditions.checkNotNull(poiNotify, "新建门店通知消息不能为空");
            Preconditions.checkNotNull(poiNotify.getNotifyType(), "门店通知类型不能为空");
            Preconditions.checkNotNull(poiNotify.getTenantInfoDto(), "门店通知中的租户信息不能为空");
            Preconditions.checkNotNull(poiNotify.getPoiInfoDto(), "门店通知中的门店信息不能为空");
            return poiNotify;
        } catch (Exception e) {
            log.error("解析门店创建消息失败. message:{}", message, e);
            return null;
        }
    }

    /**
     * 判断消息是否需要处理.该消息监听器目前仅处理歪马门店的创建消息.
     *
     * @param poiNotifyMessage 门店通知
     * @return 是否需要处理
     */
    private boolean isNeedHandled(PoiNotifyMessage poiNotifyMessage) {
        PoiInfoDto poi = poiNotifyMessage.getPoiInfoDto();
        Long waiMaTenantId = MccConfigUtils.getWaiMaTenantId();
        return waiMaTenantId.equals(poi.getTenantId());
    }

}
