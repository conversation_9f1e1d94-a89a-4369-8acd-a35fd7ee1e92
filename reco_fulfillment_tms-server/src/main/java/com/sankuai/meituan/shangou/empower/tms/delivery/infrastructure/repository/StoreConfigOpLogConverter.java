package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreConfigOpLogDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.StoreConfigOpLog;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/10/11 20:49
 */
@Mapper(componentModel = "spring")
public abstract class StoreConfigOpLogConverter {
    public abstract StoreConfigOpLog convert2StoreConfigOpLog(StoreConfigOpLogDO storeConfigOpLogDO);

    @InheritInverseConfiguration
    public abstract StoreConfigOpLogDO convert2StoreConfigOpLogDo(StoreConfigOpLog storeConfigOpLog);
}
