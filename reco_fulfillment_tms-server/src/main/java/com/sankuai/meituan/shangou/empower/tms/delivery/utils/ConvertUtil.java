package com.sankuai.meituan.shangou.empower.tms.delivery.utils;

import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderDeliveryExceptionTRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderLocatingExceptionTRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderLocationRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderLocatingExceptionDataDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderLocationDataDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryException;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocatingExceptionDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.RiderLocatingExceptionEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class ConvertUtil {

    public static RiderLocationDataDO convertToRiderLocation(RiderLocationDetail locationDetail){
        RiderLocationDataDO locationData=new RiderLocationDataDO();
        locationData.setRiderAccountId(locationDetail.getRiderAccountId());
        locationData.setRiderName(locationDetail.getRiderName());
        locationData.setRiderPhone(locationDetail.getRiderPhone());
        locationData.setAccuracy(locationDetail.getAccuracy());
        locationData.setBearing(locationDetail.getBearing());
        locationData.setLatitude(locationDetail.getLatitude());
        locationData.setLongitude(locationDetail.getLongitude());
        locationData.setProvider(locationDetail.getProvider());
        locationData.setSpeed(locationDetail.getSpeed());
        locationData.setTime(locationDetail.getTime());
        locationData.setUtime(System.currentTimeMillis());
        locationData.setOs(locationDetail.getOs());
        locationData.setAppVersion(locationDetail.getAppVersion());
        locationData.setUuid(locationDetail.getUuid());
        return locationData;
    }

    public static RiderLocationDetail convertToLocationDetail(RiderLocationDataDO locationData){
        RiderLocationDetail locationDetail=new RiderLocationDetail();
        locationDetail.setAccuracy(locationData.getAccuracy());
        locationDetail.setBearing(locationData.getBearing());
        locationDetail.setLatitude(locationData.getLatitude());
        locationDetail.setLongitude(locationData.getLongitude());
        locationDetail.setProvider(locationData.getProvider());
        locationDetail.setSpeed(locationData.getSpeed());
        locationDetail.setTime(locationData.getTime());
        locationDetail.setRiderAccountId(locationData.getRiderAccountId());
        locationDetail.setRiderName(locationData.getRiderName());
        locationDetail.setRiderPhone(locationData.getRiderPhone());
        locationDetail.setOs(locationData.getOs());
        return locationDetail;
    }

    public static RiderLocationDetail convertToLocationDetail(RiderLocationRequest request){
        RiderLocationDetail locationDetail=new RiderLocationDetail();
        locationDetail.setAccuracy(request.getAccuracy());
        locationDetail.setBearing(request.getBearing());
        locationDetail.setLatitude(request.getLatitude());
        locationDetail.setLongitude(request.getLongitude());
        locationDetail.setProvider(request.getProvider());
        locationDetail.setSpeed(request.getSpeed());
        locationDetail.setTime(request.getTime());
        locationDetail.setRiderAccountId(request.getRiderAccountId());
        locationDetail.setRiderName(request.getRiderName());
        locationDetail.setRiderPhone(request.getRiderPhone());
        locationDetail.setOs(request.getOs());
        locationDetail.setAppVersion(request.getAppVersion());
        locationDetail.setUuid(request.getUuid());
        return locationDetail;
    }

    public static RiderLocatingExceptionDataDO convertToLocatingExceptionDO(RiderLocatingExceptionDetail locatingExceptionDetail) {
        if (locatingExceptionDetail == null) {
            return null;
        }

        RiderLocatingExceptionDataDO exceptionDataDO = new RiderLocatingExceptionDataDO();
        exceptionDataDO.setRiderAccountId(locatingExceptionDetail.getRiderAccount());
        exceptionDataDO.setExceptionType(locatingExceptionDetail.getRiderLocatingExceptionEnum().getValue());
        exceptionDataDO.setPhoneOS(locatingExceptionDetail.getPhoneOS());
        exceptionDataDO.setPhoneManufacturers(locatingExceptionDetail.getManufacturer());
        exceptionDataDO.setUtime(locatingExceptionDetail.getUtime());
        exceptionDataDO.setTime(System.currentTimeMillis());
        exceptionDataDO.setUuid(locatingExceptionDetail.getUuid());

        return exceptionDataDO;

    }


    public static RiderLocatingExceptionDetail convertToExceptionDetail(RiderLocatingExceptionDataDO locatingExceptionDataDO) {
        if (locatingExceptionDataDO == null) {
            return null;
        }

        RiderLocatingExceptionDetail exceptionDetail = new RiderLocatingExceptionDetail();
        exceptionDetail.setPhoneOS(locatingExceptionDataDO.getPhoneOS());
        exceptionDetail.setManufacturer(locatingExceptionDataDO.getPhoneManufacturers());
        exceptionDetail.setRiderAccount(locatingExceptionDataDO.getRiderAccountId());
        exceptionDetail.setRiderLocatingExceptionEnum(RiderLocatingExceptionEnum.enumOf(locatingExceptionDataDO.getExceptionType()));
        exceptionDetail.setUtime(locatingExceptionDataDO.getUtime());
        exceptionDetail.setUuid(locatingExceptionDataDO.getUuid());

        return exceptionDetail;
    }

    public static RiderLocatingExceptionDetail convertToExceptionDetail(RiderLocatingExceptionTRequest request) {
        if (request == null) {
            return null;
        }

        RiderLocatingExceptionDetail exceptionDetail = new RiderLocatingExceptionDetail();
        exceptionDetail.setRiderAccount(request.getRiderAccountId());
        exceptionDetail.setUuid(request.getUuid());
        exceptionDetail.setRiderLocatingExceptionEnum(RiderLocatingExceptionEnum.enumOf(request.getExceptionType()));
        exceptionDetail.setUtime(request.getUtime());
        exceptionDetail.setPhoneOS(request.getPhoneOS());
        exceptionDetail.setManufacturer(request.getManufacturer());

        return exceptionDetail;
    }

    public static RiderDeliveryException convertToRiderDeliveryException(RiderDeliveryExceptionTRequest request) {
        if (request == null) {
            return null;
        }

        RiderDeliveryException riderDeliveryException = new RiderDeliveryException();
        riderDeliveryException.setTenantId(request.getTenantId());
        riderDeliveryException.setStoreId(request.getStoreId());
        riderDeliveryException.setChannelOrderId(request.getChannelOrderId());
        riderDeliveryException.setOrderBizType(request.getOrderBizType());
        riderDeliveryException.setDaySeq(request.getDaySeq());
        riderDeliveryException.setDeliveryOrderId(request.getDeliveryOrderId());
        riderDeliveryException.setExceptionType(request.getExceptionType());
        riderDeliveryException.setExceptionSubType(request.getExceptionSubType());
        riderDeliveryException.setExceptionDescription(new RiderDeliveryException.ExceptionDescription(request.getPicUrls(), request.getUserRealAddress(), request.getModifiedAddress(), request.getComment()));
        riderDeliveryException.setPayTime(TimeUtil.fromMilliSeconds(request.getPayTime()));
        riderDeliveryException.setRiderAccountId(request.getRiderAccountId());
        riderDeliveryException.setRiderAccountName(request.getRiderAccountName());

        return riderDeliveryException;
    }

    public static Long toLong(Object number){
        if(number == null){
            return null;
        }
        try {
            return Long.parseLong(number.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public static String replaceSpecialStr(String str){
        if(StringUtils.isEmpty(str)){
            return str;
        }
        if(!MccConfigUtils.getReplaceSwitch()){
            return str;
        }
        try {
            return str.replaceAll("\\p{C}", "");
        }catch (Exception e){
            log.error("replaceSpecialStr err",e);
        }
        return str;
    }
}
