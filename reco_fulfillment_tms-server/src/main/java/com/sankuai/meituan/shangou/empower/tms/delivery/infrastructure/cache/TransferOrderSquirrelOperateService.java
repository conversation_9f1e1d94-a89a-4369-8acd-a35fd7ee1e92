package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache;

import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
public class TransferOrderSquirrelOperateService extends SquirrelOperateService{

    @Autowired
    @Qualifier("redisSgNewSupplyOfc")
    protected RedisStoreClient redisNewSupplyClient;

    private static final String CATEGORY_NAME = "transfer_order";

    @Override
    protected RedisStoreClient getRedisClient() {
        return redisNewSupplyClient;
    }
    @Override
    public String getCategoryName() {
        return CATEGORY_NAME;
    }

    public String getKey (Long tenantId, Long storeId, Long orderId) {

        return String.valueOf(tenantId)+"_"+String.valueOf(storeId)+"_"+String.valueOf(orderId);

    }
}
