package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.PricingRouteInfoDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.PricingRouteInfoDOExMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.PricingRouteInfoDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.PricingRouteInfoDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PricingRouteInfoRepository;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/6 17:15
 **/
@Repository
public class PricingRouteInfoRepositoryImpl implements PricingRouteInfoRepository {
    @Resource
    private PricingRouteInfoDOMapper pricingRouteInfoDOMapper;

    @Resource
    private PricingRouteInfoDOExMapper pricingRouteInfoDOExMapper;

    @Override
    public List<PricingRouteInfoDO> queryByDeliveryOrderIds(Long tenantId, List<Long> deliveryOrderIds) {
        PricingRouteInfoDOExample example = new PricingRouteInfoDOExample();
        example.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDeliveryOrderIdIn(deliveryOrderIds);
        return pricingRouteInfoDOMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public void save(PricingRouteInfoDO pricingRouteInfoDO) {
        if (pricingRouteInfoDO.getId() == null) {
            pricingRouteInfoDOExMapper.insertSelective(pricingRouteInfoDO);
        } else {
            pricingRouteInfoDOMapper.updateByPrimaryKeySelective(pricingRouteInfoDO);
        }

    }
}
