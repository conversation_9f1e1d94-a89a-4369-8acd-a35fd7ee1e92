package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.NewSupplyDeliveryTimeOutCheckMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryExceptionCallbackInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryUnifiedCallbackMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.wrapper.MedicineTenantWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

@Slf4j
@Component
public class NewSupplyDeliveryTimeOutCheckMessageListener extends AbstractDeadLetterConsumer {
    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private OrderSystemClient orderSystemClient;

    @Resource
    private MafkaMessageProducer<DeliveryUnifiedCallbackMessage> deliveryUnifiedCallbackMessageProducer;

    @Resource
    private DeliveryChannelApplicationService deliveryChannelApplicationService;

    @Resource
    private MedicineTenantWrapper medicineTenantWrapper;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.NEW_SUPPLY_DELIVERY_TIMEOUT_CHECK;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
        if (!MccConfigUtils.getEstimatedTimeOutCheckSwitch()) {
            return CONSUME_SUCCESS;
        }
        log.info("消费新供给门店配送超时检查消息: {}", mafkaMessage);
        NewSupplyDeliveryTimeOutCheckMessage message = translateMessage(mafkaMessage);
        if (message == null) {
            return CONSUME_SUCCESS;
        }
        try {
            Optional<DeliveryOrder> activeDeliveryOrder = Optional.empty();
            if(MccConfigUtils.getDeliveryQueryTenantSwitch(message.getTenantId())){
                activeDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderWithTenant(message.getOrderId(),message.getTenantId(),message.getStoreId());
            }else {
                activeDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(message.getOrderId());
            }
            if (!activeDeliveryOrder.isPresent()) {
                return CONSUME_SUCCESS;
            }
            DeliveryOrder deliveryOrder = activeDeliveryOrder.get();
            LocalDateTime estimatedDeliveryTime = Optional.ofNullable(deliveryOrder.getEstimatedDeliveryEndTime())
                    .orElse(deliveryOrder.getEstimatedDeliveryTime());
            if (estimatedDeliveryTime == null) {
                return CONSUME_SUCCESS;
            }
            if (isCompareEstimatedDeliveryTime(message)) {
                LocalDateTime estimatedDeliveryTimeFromMsg = TimeUtil.fromMilliSeconds(message.getEstimatedDeliveryTime());
                // 下单后支持修改预计送达时间，这里比较消息体和运单快照里的预计送达时间，确保仅处理最新的预计送达时间对应的消息
                if (!Objects.equals(estimatedDeliveryTimeFromMsg, Optional.ofNullable(deliveryOrder.getEstimatedDeliveryEndTime())
                        .orElse(deliveryOrder.getEstimatedDeliveryTime()))) {
                    log.info("NewSupplyDeliveryTimeOutCheckMessageListener estimatedDeliveryTime not equal, orderId is {}", message.getOrderId());
                    return CONSUME_SUCCESS;
                }
            }
            // 即将达到异常失效时间不进行异常处理
            if(exceptionExpire(estimatedDeliveryTime)){
                return CONSUME_SUCCESS;
            }
            //异常上报和异常审核优先级高
            if(deliveryOrder.getDeliveryExceptionCode()!=null
                    && (deliveryOrder.getDeliveryExceptionCode() == DeliveryExceptionCodeEnum.RIDER_REPORT_FAIL.getCode()
                    || deliveryOrder.getDeliveryExceptionCode() == DeliveryExceptionCodeEnum.RIDER_TAKE_FAIL_AUDITING.getCode() )){
                return CONSUME_SUCCESS;
            }
            DeliveryStatusEnum deliveryStatus = deliveryOrder.getStatus();
            // 防止时差
            Duration between = Duration.between(estimatedDeliveryTime, LocalDateTime.now().plusSeconds(2));
            // 超过预计送达时间
            if (between.getSeconds() > 0) {
                Integer deliveryChannelCode = deliveryOrder.getDeliveryChannel();
                if (deliveryChannelCode == null || deliveryStatus == null) {
                    return CONSUME_SUCCESS;
                }
                DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryChannelCode);
                // 平台配送&聚合配送
                boolean deliveryPlatCheck = Objects.equals(deliveryChannelCode, DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode())
                        || Objects.equals(deliveryChannelCode, DeliveryChannelEnum.HAI_KUI_DELIVERY.getCode())
                        || deliveryOrder.isMaltFarmDeliveryOrder(deliveryChannel)
                        || deliveryOrder.isDapDeliveryOrder(deliveryChannel)
                        || deliveryOrder.isOrderChannelDeliveryPlatform(deliveryChannel);
                // 平台配送
                if (!deliveryPlatCheck) {
                    return CONSUME_SUCCESS;
                }

                switch (deliveryStatus) {
                    case DELIVERY_LAUNCHED:
                    case WAITING_TO_ASSIGN_RIDER:
                    case RIDER_ASSIGNED:
                    case RIDER_ARRIVED_SHOP:
                    case RIDER_TAKEN_GOODS:
                        Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(deliveryOrder.getOrderId(), false);
                        if (orderInfoResult.isFail()
                                || orderInfoResult.getInfo() == null
                                || orderInfoResult.getInfo().isFinished()) {
                            return CONSUME_SUCCESS;
                        }
                        OrderInfo orderInfo = orderInfoResult.getInfo();
                        LocalDateTime estimatedTime = Optional.ofNullable(orderInfo.getEstimatedDeliveryEndTime())
                                .orElse(orderInfo.getEstimatedDeliveryTime());
                        LocalDateTime createTime = orderInfo.getCreateTime();
                        Duration duration = Duration.between(Optional.ofNullable(createTime).orElse(LocalDateTime.now()), estimatedTime);
                        long seconds = duration.getSeconds();
                        if (seconds > 0 && seconds > MccConfigUtils.getEstimatedTimeOutMinimumSeconds()) {
                            Long orderId = deliveryOrder.getOrderId();
                            if(deliveryOrder.getPlatformSourceEnum()!=null && deliveryOrder.getPlatformSourceEnum() == PlatformSourceEnum.OFC){
                                orderId = deliveryOrder.getFulfillmentOrderId();
                            }
                            sendEstimatedDeliveryTimeOutMessage(orderId);
                        }
                        break;
                    default:
                        break;
                }
            }
            return CONSUME_SUCCESS;

        } catch (Exception e) {
            log.error("消费新供给门店配送超时检查消息失败，将会进行重试消费", e);
            return CONSUME_FAILURE;
        }
    }


    private void sendEstimatedDeliveryTimeOutMessage(Long orderId) {
        deliveryUnifiedCallbackMessageProducer.sendMessage(
                new DeliveryUnifiedCallbackMessage(
                        DeliveryCallbackTypeEnum.EXCEPTION_CALLBACK,
                        new DeliveryExceptionCallbackInfo(
                                orderId,
                                new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER,
                                        DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getDesc(),
                                        DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode()),
                                LocalDateTime.now())
                ),
                orderId);
    }

    private NewSupplyDeliveryTimeOutCheckMessage translateMessage(MafkaMessage mafkaMessage) {
        try {
            NewSupplyDeliveryTimeOutCheckMessage message = translateMessage(mafkaMessage, NewSupplyDeliveryTimeOutCheckMessage.class);
            Preconditions.checkNotNull(message, "empty mafkaMessage");
            Preconditions.checkNotNull(message.getOrderId(), "order id is null");
            return message;
        } catch (Exception e) {
            log.error("解析新供给配送超时检查消息失败:{}", mafkaMessage, e);
            return null;
        }
    }

    private boolean exceptionExpire(LocalDateTime estimatedDeliveryTime){
        // 当前时间已经超过预计送达时间或者快接近异常清除时间
        long deliveryExceptionClearDelayMillis = com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.getDeliveryExceptionClearDelayMillis(estimatedDeliveryTime);
        return deliveryExceptionClearDelayMillis <= 5000;
    }

    /**
     * 返回true代表需要比较预计送达时间
     */
    private boolean isCompareEstimatedDeliveryTime(NewSupplyDeliveryTimeOutCheckMessage message) {
        Long tenantId = message.getTenantId();
        if (medicineTenantWrapper.isUwmsMedicineTenant(tenantId)) {
            return false;
        }

        if (Objects.isNull(message.getEstimatedDeliveryTime())) {
            return false;
        }

        Long storeId = message.getStoreId();
        if (Objects.isNull(storeId)) {
            return false;
        }

        // 门店白名单校验
        return MccConfigUtils.isOrderInfoChangeStoreIdWhiteList(storeId);
    }
}