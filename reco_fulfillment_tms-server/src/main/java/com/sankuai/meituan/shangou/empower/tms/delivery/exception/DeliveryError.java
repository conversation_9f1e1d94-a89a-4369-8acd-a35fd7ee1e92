package com.sankuai.meituan.shangou.empower.tms.delivery.exception;

import lombok.Data;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @date 2019/10/29
 * desc: 配送错误
 */
@Data
public class DeliveryError {

    public static final DeliveryError SUCCESS = new DeliveryError(0, "成功");
    public static final DeliveryError CREATE_DELIVERY_ERROR = new DeliveryError(10000, "接入配送失败，请重试");
    public static final DeliveryError UNKNOWN_DELIVERY_CHANNEL = new DeliveryError(10001, "无法解析配送渠道");
    public static final DeliveryError CANCEL_ORDER_FAIL = new DeliveryError(10002, "无法进行配送后取消订单失败");
    public static final DeliveryError QUERY_ORDER_FAIL = new DeliveryError(10003, "无法查询订单信息");

    public static final DeliveryError OPERATOR_SHOP_PERMISSION_ERROR = new DeliveryError(10004, "操作员无门店{0}权限");
    public static final DeliveryError DELIVERY_REJECTED = new DeliveryError(10005, "配送拒单:{0}");
    public static final DeliveryError DELIVERY_NEED_RETRY = new DeliveryError(10006, "配送需要重试");


    /********** 配送配置类型错误 **********/
    public static final DeliveryError PART_SHOP_DELIVERY_CONFIG_ALREADY_EXIST = new DeliveryError(801, "部分门店配送配置已存在");

    public static final DeliveryError DELIVERY_CONFIG_NOT_EXIST = new DeliveryError(11001, "配送配置不存在");
    public static final DeliveryError DELETE_SHOP_DELIVERY_CONFIGS_FAIL = new DeliveryError(11002, "删除门店配送配置失败");
    public static final DeliveryError MODIFY_SHOP_DELIVERY_FAIL = new DeliveryError(11003, "配送配置不存在");
    public static final DeliveryError BATCH_IMPORT_SHOP_DELIVERY_CONFIG_ERROR = new DeliveryError(11004, "批量导入门店配送配置失败");
    public static final DeliveryError BATCH_IMPORT_CONFIG_VALIDATE_ERROR = new DeliveryError(11005, "导入文件校验失败");
    public static final DeliveryError NEW_DELIVERY_CONFIG_PARAM_MISS_ERROR = new DeliveryError(11006, "新增配送配置参数缺失");
    public static final DeliveryError NEW_SHOP_DELIVERY_CONFIG_PARAM_MISS_ERROR = new DeliveryError(11007, "新增门店配送配置参数缺失");
    public static final DeliveryError LAUNCH_DELIVERY_CONFIG_INVALID_ERROR = new DeliveryError(11008, "发起配送配置无效");
    public static final DeliveryError IMPORT_SHOP_DELIVERY_CONFIG_DUPLICATE_ERROR = new DeliveryError(11009, "门店{0}数据重复，请检查后重新上传提交。");
    public static final DeliveryError APP_KEY_CONFIG_ALREADY_ERROR = new DeliveryError(11010, "AppKey:{0}被占用，不可使用。");
    public static final DeliveryError IMPORT_EXCEL_ROW_LIMIT_ERROR = new DeliveryError(11011, "导入Excel文档记录数超过{0}条限制");
    public static final DeliveryError CANNOT_MODIFY_SHOP_DELIVERY_NOT_EXIST = new DeliveryError(11012, "待编辑的门店{0}的{1}配送渠道不存在");
    public static final DeliveryError TENANT_NOT_CONFIG_DELIVERY_CHANNEL = new DeliveryError(11014, "租户没有启用的配送渠道");
    public static final DeliveryError TENANT_SHOP_NOT_CONFIG_DELIVERY_CHANNEL_SHOP = new DeliveryError(11015, "租户门店未配置配送渠道门店");
    public static final DeliveryError SYNC_DELIVERY_RANGE_ERROR = new DeliveryError(11016, "同步配送范围失败");
    public static final DeliveryError CANNOT_GET_DELIVERY_RANGE_FROM_DELIVERY_CHANNEL = new DeliveryError(11017, "无法获取配送渠道配送范围");
    public static final DeliveryError UNKNOWN_CHANNEL = new DeliveryError(11018, "未知渠道编码:{0}");


    private int code;

    private String msg;

    private String msgTemplate;

    public DeliveryError(int code, String msgTemplate) {
        this.code = code;
        this.msgTemplate = msgTemplate;
        this.msg = msgTemplate;
    }

    public DeliveryError messageFormat(Object ... params) {
        String message = MessageFormat.format(this.getMsgTemplate(), params);
        this.setMsg(message);
        return this;
    }
}
