package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.CreditAuditNotifyMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.RiderOperateApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.CreditAuditBizData;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.CreditAuditResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.VerifyStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;


/**
 * <AUTHOR>
 * @since 2023/5/5 16:12
 **/
@Component
@Slf4j
public class CreditAuditNotifyListener extends AbstractDeadLetterConsumer{

    @Resource
    private RiderOperateApplicationService riderOperateApplicationService;

    public static final Integer TYPE_ID = 21097;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.CREDIT_AUDIT_BATCH_NOTIFY_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
        try {
            CreditAuditNotifyMessage message = translateMessage(mafkaMessage);
            if(!Objects.equals(message.getType(), TYPE_ID)) {
                return CONSUME_SUCCESS;
            }
            Optional<Failure> failure;
            log.info("开始消费保时洁图片审核结果消息：{}", mafkaMessage);
            failure = riderOperateApplicationService.processCreditAuditResult(buildCreditAuditResult(message));

            //业务异常不重试
            if (failure.isPresent()) {
                log.error("消费保时洁图片审核结果消息失败, 失败原因:{} ", failure.get());
                return CONSUME_SUCCESS;
            }
        } catch (Exception e) {
            Cat.logEvent("DELIVERY_PROOF_PHOTO", "CONSUME_CREDIT_AUDIT_RESULT_MESSAGE_FAIL");
            log.error("消费保时洁图片审核结果消息失败", e);
            return CONSUME_FAILURE;
        }


        return CONSUME_SUCCESS;
    }

    private CreditAuditNotifyMessage translateMessage(MafkaMessage mafkaMessage) {
        CreditAuditNotifyMessage message = translateMessage(mafkaMessage, CreditAuditNotifyMessage.class);
        Preconditions.checkNotNull(message, "empty mafkaMessage");

        return message;
    }

    private CreditAuditResult buildCreditAuditResult(CreditAuditNotifyMessage message) {
        Preconditions.checkNotNull(message, "message is null");
        Preconditions.checkNotNull(message.getPair().get("bizData"), "bizData is null");
        Preconditions.checkNotNull(message.getPair().get("verifyStatus"), "verifyStatus is null");
        Preconditions.checkNotNull(message.getDatetime(), "datetime is null");
        Preconditions.checkNotNull(message.getAuditFreq(), "auditFreq is null");
        Preconditions.checkNotNull(message.getPair(), "pair is null");

        CreditAuditResult creditAuditResult = new CreditAuditResult();
        creditAuditResult.setDatetime(LocalDateTime.parse(message.getDatetime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        CreditAuditBizData bizData = JsonUtil.fromJson((String) message.getPair().get("bizData"), CreditAuditBizData.class);
        if (Objects.isNull(bizData) || Objects.isNull(bizData.getDeliveryOrderId())) {
            throw new CommonRuntimeException("审核结果缺失运单id");
        }
        Long deliveryOrderId = bizData.getDeliveryOrderId();
        creditAuditResult.setDeliveryOrderId(deliveryOrderId);
        creditAuditResult.setTenantId(bizData.getTenantId());
        creditAuditResult.setStoreId(bizData.getStoreId());
        if (Objects.equals(message.getPair().get("verifyStatus"), VerifyStatusEnum.HIT_VIOLATION_LABEL.getCode())) {
            List<Map<String,Object>> statusNameList = (List<Map<String,Object>>) message.getPair().getOrDefault("statusName", Collections.emptyList());
            List<Long> violationPicIdList = statusNameList.stream().filter(map -> map.containsKey("picId")).map(map ->Long.valueOf(map.get("picId").toString())).collect(Collectors.toList());
            creditAuditResult.setViolationPicIdList(violationPicIdList);
        } else {
            creditAuditResult.setViolationPicIdList(Collections.emptyList());
        }

        return creditAuditResult;
    }
}
