package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryCallbackApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryCallbackTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * 渠道配送回调监听
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/14
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class DeliveryUnifiedCallbackMessageListener extends AbstractDeadLetterConsumer {

	@Resource
	private DeliveryCallbackApplicationService deliveryCallbackApplicationService;

	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.DELIVERY_UNIFIED_CALLBACK_CONSUMER;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
		log.info("开始消费配送统一回调消息: {}", mafkaMessage);
		DeliveryUnifiedCallbackMessage message = translateMessage(mafkaMessage);
		if (message == null) {
			return CONSUME_SUCCESS;
		}

		try {
			switch (DeliveryCallbackTypeEnum.enumOf(message.getType())) {
				case LAUNCH_CALLBACK:
					getCallbackInfo(message, DeliveryLaunchCallbackInfo.class)
							.ifPresent(it -> deliveryCallbackApplicationService.deliveryLaunchCallback(it.toCmd()));
					break;

				case CANCEL_CALLBACK:
					getCallbackInfo(message, DeliveryCancelCallbackInfo.class)
							.ifPresent(it -> deliveryCallbackApplicationService.deliveryCancelCallback(it.toCmd()));
					break;

				case CHANGE_CALLBACK:
					return getCallbackInfo(message, DeliveryChangeCallbackInfo.class)
							.flatMap(it -> deliveryCallbackApplicationService.deliveryChangeCallback(it.toCmd()))
							.map(it -> CONSUME_FAILURE)
							.orElse(CONSUME_SUCCESS);

				case EXCEPTION_CALLBACK:
					getCallbackInfo(message, DeliveryExceptionCallbackInfo.class)
							.ifPresent(it -> deliveryCallbackApplicationService.deliveryExceptionCallback(it.toCmd()));
					break;

				default:
					throw new IllegalStateException("Unexpected value: " + DeliveryCallbackTypeEnum.enumOf(message.getType()));
			}

			return CONSUME_SUCCESS;

		} catch (Exception e) {
			log.error("消费配送统一回调消息失败，将会进行重试消费", e);
			return CONSUME_FAILURE;
		}
	}

	private DeliveryUnifiedCallbackMessage translateMessage(MafkaMessage mafkaMessage) {
		try {
			DeliveryUnifiedCallbackMessage message = translateMessage(mafkaMessage, DeliveryUnifiedCallbackMessage.class);
			Preconditions.checkNotNull(message, "empty mafkaMessage");
			Preconditions.checkNotNull(message.getType(), "callbackType is invalid");
			Preconditions.checkNotNull(message.getInfo(), "callbackInfo is null");
			return message;

		} catch (Exception e) {
			log.error("解析配送统一回调消息失败:{}", mafkaMessage, e);
			Cat.logEvent("DELIVERY_UNIFIED_CALLBACK_CONSUME_FAILED", "MESSAGE_WRONG");
			return null;
		}
	}

	private <T extends DeliveryUnifiedCallbackInfo> Optional<T> getCallbackInfo(DeliveryUnifiedCallbackMessage message, Class<T> expectClass) {
		try {
			return Optional.ofNullable(JsonUtil.fromJson(message.getInfo(), expectClass));
		} catch (Exception e) {
			log.error("解析配送统一回调消息中的回调信息失败, message:{}", message, e);
			return Optional.empty();
		}
	}
}
