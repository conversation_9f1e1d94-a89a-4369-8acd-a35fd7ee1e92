package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.osw;

import com.dianping.rhino.annotation.Degrade;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.osw.OswClient;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.infra.osw.api.poi.store.dto.request.WarehouseIdsRequest;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.TWarehouseService;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/6/6 14:59
 **/
@Service
@Slf4j
public class OswClientImpl implements OswClient {

    @Resource
    private TWarehouseService tWarehouseService;

    @Override
    @Degrade(rhinoKey = "OswClient.queryWarehouseById", fallBackMethod = "queryWarehouseByIdFallback", timeoutInMilliseconds = 2000)
    public Optional<WarehouseDTO> queryWarehouseById(@NotNull Long tenantId, @NotNull Long storeId) {
        WarehouseIdsRequest request = new WarehouseIdsRequest();
        request.setWarehouseIds(Collections.singletonList(storeId));
        request.setTenantId(tenantId);
        TResult<List<WarehouseDTO>> result;
        try {
            log.info("invoke tWarehouseService.batchQueryWarehouseById start, request: {}", request);
            result = tWarehouseService.batchQueryWarehouseById(request);
            log.info("invoke tWarehouseService.batchQueryWarehouseById end, response: {}", JsonUtil.toJson(result));
        } catch (Exception e) {
            log.error("invoke tWarehouseService.batchQueryWarehouseById error", e);
            throw new ThirdPartyException("查询仓信息失败");
        }

        if (!result.isSuccess()) {
            throw new BizException("查询仓信息失败");
        }

        if (CollectionUtils.isEmpty(result.getData())) {
            return Optional.empty();
        }

        return Optional.of(result.getData().get(0));
    }

    public Optional<WarehouseDTO> queryWarehouseByIdFallback(@NotNull Long tenantId, @NotNull Long storeId) {
        log.error("OswClient.queryWarehouseById 发生降级");
        return Optional.empty();
    }
}
