package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.rider.repository;

import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderArrivalLocationTRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.RiderArrivalLocationDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderArrivalLocationDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderArrivalLocationDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderArrivalLocation;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderArrivalLocationRepository;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Repository
public class MySQLRiderArrivalLocationRepository implements RiderArrivalLocationRepository {

    @Resource
    private RiderArrivalLocationDOMapper riderArrivalLocationDOMapper;

    @Override
    public void save(RiderArrivalLocation riderArrivalLocation) {
        if (riderArrivalLocation == null) {
            return;
        }

        riderArrivalLocationDOMapper.insertSelective(transform2RiderArrivalLocationDO(riderArrivalLocation));

    }

    @Override
    public RiderArrivalLocation getByDeliveryOrderId(Long deliveryOrderId) {
        RiderArrivalLocationDOExample example = new RiderArrivalLocationDOExample();
        example.createCriteria()
                .andDeliveryOrderIdEqualTo(deliveryOrderId);
        List<RiderArrivalLocationDO> locationDOList = riderArrivalLocationDOMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(locationDOList)) {
            return null;
        }

        return transform2RiderArrivalLocation(locationDOList.get(0));
    }

    private RiderArrivalLocationDO transform2RiderArrivalLocationDO(RiderArrivalLocation riderArrivalLocation) {
        if (riderArrivalLocation == null) {
            return null;
        }

        RiderArrivalLocationDO record = new RiderArrivalLocationDO();
        record.setDeliveryOrderId(riderArrivalLocation.getDeliveryOrderId());
        record.setLatitude(riderArrivalLocation.getLatitude());
        record.setLongitude(riderArrivalLocation.getLongitude());
        record.setRiderAccountId(riderArrivalLocation.getRiderAccountId());
        record.setLinearDistanceToReceiver(riderArrivalLocation.getLineDistance());
        record.setNavigationDistanceToReceiver(riderArrivalLocation.getNavigationDistance());
        record.setOs(riderArrivalLocation.getOs());
        record.setAccuracy(riderArrivalLocation.getAccuracy());
        record.setProvider(riderArrivalLocation.getProvider());
        record.setBearing(riderArrivalLocation.getBearing());
        record.setSpeed(riderArrivalLocation.getSpeed());
        record.setLocateTime(riderArrivalLocation.getTime());
        if (riderArrivalLocation.getIsCacheLocation() != null) {
            record.setIsCacheLocation(riderArrivalLocation.getIsCacheLocation() ? 1 : 0);
        }


        return record;
    }

    private RiderArrivalLocation transform2RiderArrivalLocation(RiderArrivalLocationDO riderArrivalLocationDO) {
        if (riderArrivalLocationDO == null) {
            return null;
        }

        RiderArrivalLocation riderArrivalLocation = new RiderArrivalLocation(riderArrivalLocationDO.getDeliveryOrderId(),riderArrivalLocationDO.getRiderAccountId());
        riderArrivalLocation.setLatitude(riderArrivalLocationDO.getLatitude());
        riderArrivalLocation.setLongitude(riderArrivalLocationDO.getLongitude());
        if (riderArrivalLocationDO.getLinearDistanceToReceiver() >= 0) {
            riderArrivalLocation.setLineDistance(riderArrivalLocationDO.getLinearDistanceToReceiver());
        }

        if (riderArrivalLocationDO.getNavigationDistanceToReceiver() >= 0) {
            riderArrivalLocation.setNavigationDistance(riderArrivalLocationDO.getNavigationDistanceToReceiver());
        }

        riderArrivalLocation.setSpeed(riderArrivalLocationDO.getSpeed());
        riderArrivalLocation.setAccuracy(riderArrivalLocationDO.getAccuracy());
        riderArrivalLocation.setBearing(riderArrivalLocationDO.getBearing());
        riderArrivalLocation.setProvider(riderArrivalLocationDO.getProvider());
        riderArrivalLocation.setOs(riderArrivalLocationDO.getOs());
        riderArrivalLocation.setTime(riderArrivalLocationDO.getLocateTime());
        riderArrivalLocation.setIsCacheLocation(Objects.equals(riderArrivalLocationDO.getIsCacheLocation(), 1));

        return riderArrivalLocation;
    }
}
