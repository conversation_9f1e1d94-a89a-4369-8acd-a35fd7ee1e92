package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.rider.repository;

import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryMergeOrderRecordDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryMergeOrderRecordDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryMergeOrderRecordDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository.MySQLOperateFailedException;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryMergeOrderRecord;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryMergeOrderRecordRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class MySQLRiderDeliveryMergeOrderRecordRepository implements RiderDeliveryMergeOrderRecordRepository {

    private final static long VALID_ACTIVE_STATUS = 0L;

    private final static int VALID_OPERATE_COUNT = 1;

    @Resource
    private DeliveryMergeOrderRecordDOMapper mergeOrderRecordDOMapper;

    @MethodLog(logRequest = false, logResponse = true)
    @Override
    public boolean upsert(RiderDeliveryMergeOrderRecord mergeOrder) {
        DeliveryMergeOrderRecordDO recordDO = ParamExchanger.transfer(mergeOrder);
        if (recordDO.getId() == null) {
            return mergeOrderRecordDOMapper.insert(recordDO) == VALID_OPERATE_COUNT;
        } else {
            return mergeOrderRecordDOMapper.updateByPrimaryKeySelective(recordDO) == VALID_OPERATE_COUNT;
        }
    }

    @MethodLog(logRequest = false, logResponse = true)
    @Transactional
    @Override
    public void batchUpsert(List<RiderDeliveryMergeOrderRecord> mergeOrders) {
        if (CollectionUtils.isNotEmpty(mergeOrders)) {
            for (RiderDeliveryMergeOrderRecord mergeOrder : mergeOrders) {
                if (!upsert(mergeOrder)) {
                    throw new MySQLOperateFailedException("并单变更失败");
                }
            }
        }
    }

    @MethodLog(logRequest = false, logResponse = true)
    @Override
    public boolean remove(Long mergeId, Long deliveryOrderId) {
        DeliveryMergeOrderRecordDO recordDO = new DeliveryMergeOrderRecordDO();
        recordDO.setMergeId(mergeId);
        recordDO.setDeliveryOrderId(deliveryOrderId);
        recordDO.setActiveStatus(System.currentTimeMillis());
        DeliveryMergeOrderRecordDOExample example = new DeliveryMergeOrderRecordDOExample();
        example.createCriteria().andMergeIdEqualTo(mergeId)
                .andDeliveryOrderIdEqualTo(deliveryOrderId)
                .andActiveStatusEqualTo(VALID_ACTIVE_STATUS);
        return mergeOrderRecordDOMapper.updateByExampleSelective(recordDO, example) == VALID_OPERATE_COUNT;
    }

    @Transactional
    @MethodLog(logRequest = false, logResponse = true)
    @Override
    public void batchRemove(List<Pair<Long, Long>> mergeIdAndOrderIdPairList) {
        if (CollectionUtils.isNotEmpty(mergeIdAndOrderIdPairList)) {
            for (Pair<Long, Long> pair : mergeIdAndOrderIdPairList) {
                if (!remove(pair.getLeft(), pair.getRight())) {
                    throw new MySQLOperateFailedException("并单删除失败");
                }
            }
        }
    }

    @Transactional
    @MethodLog(logRequest = false, logResponse = true)
    @Override
    public void batchUpsertAndRemove(List<RiderDeliveryMergeOrderRecord> mergeOrders, List<Pair<Long, Long>> mergeIdAndOrderIdPairList) {
        batchUpsert(mergeOrders);
        batchRemove(mergeIdAndOrderIdPairList);
    }

    @Override
    public List<RiderDeliveryMergeOrderRecord> queryByAccountIdAndDeliveryIds(Long accountId, Set<Long> deliveryIds) {
        DeliveryMergeOrderRecordDOExample example = new DeliveryMergeOrderRecordDOExample();
        example.createCriteria().andDeliveryOrderIdIn(new ArrayList<>(deliveryIds))
                .andRiderAccountIdEqualTo(accountId)
                .andActiveStatusEqualTo(VALID_ACTIVE_STATUS);
        return ParamExchanger.transferToDomain(mergeOrderRecordDOMapper.selectByExample(example));
    }

    @Override
    public List<RiderDeliveryMergeOrderRecord> queryByMergeId(Long mergeId) {
        DeliveryMergeOrderRecordDOExample example = new DeliveryMergeOrderRecordDOExample();
        example.createCriteria().andMergeIdEqualTo(mergeId).andActiveStatusEqualTo(VALID_ACTIVE_STATUS);
        return ParamExchanger.transferToDomain(mergeOrderRecordDOMapper.selectByExample(example));
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public List<RiderDeliveryMergeOrderRecord> queryByMergeIds(List<Long> mergeIds) {
        if (CollectionUtils.isEmpty(mergeIds)) {
            return Collections.emptyList();
        }
        DeliveryMergeOrderRecordDOExample example = new DeliveryMergeOrderRecordDOExample();
        example.createCriteria().andMergeIdIn(mergeIds).andActiveStatusEqualTo(VALID_ACTIVE_STATUS);
        return ParamExchanger.transferToDomain(mergeOrderRecordDOMapper.selectByExample(example));
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public Map<Long, Set<Long>> queryRiderMergeOrderRecordMap(Long accountId, Set<Long> deliveryIds) {
        List<RiderDeliveryMergeOrderRecord> records = queryByAccountIdAndDeliveryIds(accountId, deliveryIds);
        Set<Long> mergeIds = records.stream().map(RiderDeliveryMergeOrderRecord::getMergeId).collect(Collectors.toSet());
        List<RiderDeliveryMergeOrderRecord> mergeOrderRecords = queryByMergeIds(new ArrayList<>(mergeIds));
        HashMap<Long, Set<Long>> mergeMap = new HashMap<>();
        for (RiderDeliveryMergeOrderRecord record : mergeOrderRecords) {
            if (!mergeMap.containsKey(record.getMergeId())) {
                mergeMap.put(record.getMergeId(), new HashSet<>());
            }

            mergeMap.get(record.getMergeId()).add(record.getDeliveryOrderId());
        }

        return mergeMap;
    }


    private static class ParamExchanger {
        private static List<RiderDeliveryMergeOrderRecord> transferToDomain(List<DeliveryMergeOrderRecordDO> recordDOList) {
            return Optional.ofNullable(recordDOList).orElse(Collections.emptyList()).stream().map(record ->
                    new RiderDeliveryMergeOrderRecord(
                            record.getId(),
                            record.getDeliveryOrderId(),
                            record.getMergeId(),
                            record.getActiveStatus(),
                            record.getChannelOrderId(),
                            record.getOrderBizType(),
                            record.getRiderAccountId(),
                            JacksonUtils.fromJsonToMap(record.getExtInfo()),
                            record.getCreateTime(),
                            record.getUpdateTime()
                    )).collect(Collectors.toList());
        }

        private static DeliveryMergeOrderRecordDO transfer(RiderDeliveryMergeOrderRecord mergeOrder) {
            DeliveryMergeOrderRecordDO recordDO = new DeliveryMergeOrderRecordDO();
            recordDO.setId(mergeOrder.getId());
            recordDO.setDeliveryOrderId(mergeOrder.getDeliveryOrderId());
            recordDO.setMergeId(mergeOrder.getMergeId());
            recordDO.setActiveStatus(mergeOrder.getActiveStatus());
            recordDO.setChannelOrderId(mergeOrder.getChannelOrderId());
            recordDO.setOrderBizType(mergeOrder.getOrderBizType());
            recordDO.setRiderAccountId(mergeOrder.getRiderAccountId());
            recordDO.setExtInfo(JacksonUtils.toJson(mergeOrder.getExtInfoMap()));
            recordDO.setCreateTime(mergeOrder.getCreateTime());
            recordDO.setUpdateTime(mergeOrder.getUpdateTime());
            return recordDO;
        }
    }
}
