package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.RiderInfoDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 自配送订单配送状态对外推送msg
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SelfDeliveryChangeOuterNotifyMessage {

	/**
	 * 租户ID
	 */
	private Long tenantId;

	/**
	 * 赋能订单号
	 */
	private Long orderId;

	/**
	 * 配送状态code
	 */
	private Integer deliveryStatusCode;

	/**
	 * 承运商code
	 */
	private Integer deliveryChannelCode;

	/**
	 * 承运商名称
	 */
	private String deliveryChannelDesc;

	/**
	 * 订单配送距离（单位米）
	 */
	private Long deliveryDistance;

	/**
	 * 订单配送费（单位分）
	 */
	private Integer deliveryFee;

	/**
	 * 骑手信息
	 */
	private RiderInfoDto riderInfoDto;

	/**
	 * 最新一次状态变更时间（毫秒）
	 */
	private Long lastEventTime;

	/**
	 * 骑手实际到店时间戳（毫秒）
	 */
	private Long riderArrivedStoreTime;

	/**
	 * 骑手实际取货时间戳（毫秒）
	 */
	private Long riderTakeGoodsTime;

	/**
	 * 配送取消原因code
	 */
	private Integer cancelReasonCode;

	/**
	 * 配送取消原因描述
	 */
	private String cancelReasonDesc;

	private String channel_sheetno;

}
