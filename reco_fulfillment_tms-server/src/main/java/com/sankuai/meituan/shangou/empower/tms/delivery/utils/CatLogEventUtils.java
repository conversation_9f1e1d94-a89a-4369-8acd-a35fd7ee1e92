package com.sankuai.meituan.shangou.empower.tms.delivery.utils;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 新供给cat打点上报工具类
 * <AUTHOR>
 */
@Slf4j
public class CatLogEventUtils {

    public static void reportAggDeliveryRiderLocationSyncLogEvent(DeliveryPlatformEnum deliveryPlatformEnum, boolean getLocationSuccess) {
        if (Objects.isNull(deliveryPlatformEnum)) {
            log.warn("reportAggDeliveryRiderLocationSyncLogEvent, deliveryPlatformEnum is null");
            return;
        }

        switch (deliveryPlatformEnum) {
            case MALT_FARM_DELIVERY_PLATFORM:
                Cat.logEvent("aggDelivery.sync.rider.location","malt.farm.delivery", getLocationSuccess ? Event.SUCCESS: "FAIL","");
                break;
            case DAP_DELIVERY_PLATFORM:
                Cat.logEvent("aggDelivery.sync.rider.location","dap.delivery", getLocationSuccess ? Event.SUCCESS: "FAIL","");
                break;
            default:
                log.warn("reportAggDeliveryRiderLocationSyncLogEvent, unknown deliveryPlatformEnum");
        }
    }
}
