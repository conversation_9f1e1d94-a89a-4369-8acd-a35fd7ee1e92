package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * 配送转单消息变更
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryTransNotifyMessage {
    /**
     * 订单业务类型
     */
    private Integer orderBizType;
    /**
     * 仓Id
     */
    private Long warehouseId;
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 渠道订单号
     */
    private String viewOrderId;
    /**
     * 赋能订单号
     */
    private Long orderId;
    /**
     * 转单之前的配送平台
     * @see
     *package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.TranDeliveryPlatformEnum
     *
     */
    private Integer lastDeliveryPlatform;
    /**
     * 当前的配送平台
     * @see
     * package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.TranDeliveryPlatformEnum
     */
    private Integer deliveryPlatform;
    /**
     * 当前渠道类型
     */
    private Integer deliveryChannel;
    /**
     * 转单时间，时间戳
     */
    private Long time;

}
