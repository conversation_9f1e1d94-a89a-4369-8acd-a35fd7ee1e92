package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.aop;

import com.meituan.mtrace.Tracer;
import com.meituan.reco.pickselect.common.utils.AopUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.model.config.TenantBizModeConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.StoreOpeningOnlineChannelsQueryResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantChannelStoreInfo;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum.MERCHANT_ACCEPT;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class LoadTestAspect {
    @Around("@annotation(com.sankuai.meituan.shangou.empower.tms.delivery.access.aop.LoadTestAop)")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        //关闭压测数据拦截
        if (!MccConfigUtils.loadTestAopOpenFlag()) {
            return joinPoint.proceed();
        }

        //正常流量
        if (!Tracer.isTest()) {
            return joinPoint.proceed();
        }

        //压测流量
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        final String methodName = AopUtils.getMethodName(joinPoint);

        if("isUwmsMedicineTenant".equalsIgnoreCase(methodName)){
            log.info("Method {} is about not to be called，tracer test flag {}", methodName, Tracer.isTest());
            return false;
        }

        if ("queryUwmsTenantId".equalsIgnoreCase(methodName)) {
            log.info("Method {} is about not to be called，tracer test flag {}", methodName, Tracer.isTest());
            return new ArrayList<>();
        }

        if("queryDeliveryLaunchPointConfig".equalsIgnoreCase(methodName)){
            log.info("Method {} is about not to be called，tracer test flag {}", methodName, Tracer.isTest());
            return new DeliveryLaunchPoint(
                    new DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(MERCHANT_ACCEPT, 0),
                    new DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig()
            );
        }

        if("queryTenantBizModeConfig".equalsIgnoreCase(methodName)){
            log.info("Method {} is about not to be called，tracer test flag {}", methodName, Tracer.isTest());
            final TenantBizModeConfig tenantBizModeConfig = new TenantBizModeConfig();
            tenantBizModeConfig.setBizMode("convenience_store");
            return tenantBizModeConfig;
        }

        if("isMedicineUnmannedWarehouse".equalsIgnoreCase(methodName)){
            log.info("Method {} is about not to be called，tracer test flag {}", methodName, Tracer.isTest());
            return false;
        }

        if("getTenantChannelType".equalsIgnoreCase(methodName)){
            log.info("Method {} is about not to be called，tracer test flag {}", methodName, Tracer.isTest());
            List<Integer> result = new ArrayList<>();
            result.add(100);
            return result;
        }

        if("queryChannelStoreDetailInfoWithAnyChannel".equalsIgnoreCase(methodName)){
            log.info("Method {} is about not to be called，tracer test flag {}", methodName, Tracer.isTest());
            TenantChannelStoreInfo tenantChannelStoreInfo= TenantChannelStoreInfo.builder().address("中山公园音乐堂")
                    .areaCode(510107).cityCode(510100).latitude("39.910831")
                    .longitude("116.395615").phone("13013131313").shopId(49864215L).storeName("麦芽田平台配送测试门店-有自动化用例不准动")
                    .tenantId(1000078L).build();
            return Optional.of(tenantChannelStoreInfo);
        }

        if("queryChannelPoiIdByPoiId".equalsIgnoreCase(methodName)){
            return "10906181";
        }

        if("queryStoreOpeningOnlineChannels".equalsIgnoreCase(methodName)){
            log.info("Method {} is about not to be called，tracer test flag {}", methodName, Tracer.isTest());
            List<Integer> result = new ArrayList<>();
            result.add(100);
            return new StoreOpeningOnlineChannelsQueryResult(result);
        }

        if("queryDiscreetDelivery".equalsIgnoreCase(methodName)){
            log.info("Method {} is about not to be called，tracer test flag {}", methodName, Tracer.isTest());
            return false;
        }

        if (Double.class.isAssignableFrom(method.getReturnType())) {
            log.info("Method {} is about not to be called，tracer test flag {}", methodName, Tracer.isTest());
            return 0;
        } else {
            log.info("Method {} is about not to be called，tracer test flag {}", methodName, Tracer.isTest());
            return null;
        }
    }

}
