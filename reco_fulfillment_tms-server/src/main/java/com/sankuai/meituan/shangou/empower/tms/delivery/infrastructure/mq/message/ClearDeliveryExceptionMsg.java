package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/4/16
 * @email jianglilin02@meituan
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClearDeliveryExceptionMsg {
    private OrderKey orderKey;

    private Long estimatedDeliveryTime;
}
