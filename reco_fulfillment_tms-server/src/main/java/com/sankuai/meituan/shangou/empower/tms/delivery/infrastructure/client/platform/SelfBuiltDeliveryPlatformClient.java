package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform;

import com.dianping.cat.Cat;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DeliveryResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery.CancelDeliveryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery.LocationInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery.QueryRiderLocationRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.LaunchFailure;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.CoordinateUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.SpringContextUtils;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.DeliveryResultCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/13
 */
@Slf4j
@Service
public class SelfBuiltDeliveryPlatformClient extends AbstractDeliveryPlatformClient {

	@Resource
	private ChannelDeliveryThriftService.Iface channelDeliveryThriftServiceClient;

	@Override
	public DeliveryPlatformEnum getDeliveryPlatform() {
		return DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM;
	}

	@Override
	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> cancelDelivery(DeliveryOrder deliveryOrder) {
		log.info("SelfBuiltDeliveryPlatformClient.cancelDelivery deliveryOrderPrimaryId:{}", deliveryOrder.getId());
		try {
			CancelDeliveryRequest request = new CancelDeliveryRequest()
					.setTenantId(deliveryOrder.getTenantId())
					.setShopId(deliveryOrder.getStoreId())
					.setDeliveryChannelId(deliveryOrder.getDeliveryChannel())
					.setDeliveryId(getDeliveryId(deliveryOrder))
					.setChannelDeliveryId(deliveryOrder.getChannelDeliveryId())
					.setCancelReasonType(101)
					.setCancelReasonDesc("订单取消");
			log.info("ChannelDeliveryThriftService.cancelDelivery begin, request={}", request);
			ResultStatus result = channelDeliveryThriftServiceClient.cancelDelivery(request);
			log.info("ChannelDeliveryThriftService.cancelDelivery finish, result={}", result);
			if (result == null) {
				Cat.logEvent("CANCEL_DELIVERY_" + deliveryOrder.getDeliveryChannel(), "FAIL_BY_NULL");
				return Optional.of(new Failure(false, FailureCodeEnum.SYSTEM_ERROR));
			}

			if (result.getCode() == SUCCESS.getCode()) {
				Cat.logEvent("CANCEL_DELIVERY_" + deliveryOrder.getDeliveryChannel(), "SUCCESS");
				return Optional.empty();

			} else {
				Cat.logEvent("CANCEL_DELIVERY_" + deliveryOrder.getDeliveryChannel(),
						"FAIL_BY_CODE_" + result.getCode());
				DeliveryResultCodeEnum resultCode = Optional.ofNullable(DeliveryResultCodeEnum.enumOf(result.getCode()))
						.orElse(DeliveryResultCodeEnum.SYSTEM_ERROR);
				return Optional.of(new Failure(resultCode.isCanRetry(), FailureCodeEnum.CANCEL_DELIVERY_FAILED.getCode(), result.getMsg()));
			}

		} catch (Exception e) {
			Cat.logEvent("CANCEL_DELIVERY_" + deliveryOrder.getDeliveryChannel(), "ERROR");
			log.error("取消配送失败", e);
			return Optional.of(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
		}
	}

	@Override
	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryPoi deliveryPoi) {
		try {
			QueryRiderLocationRequest request = new QueryRiderLocationRequest()
					.setTenantId(deliveryOrder.getTenantId())
					.setShopId(deliveryOrder.getStoreId())
					.setDeliveryChannelId(deliveryOrder.getDeliveryChannel())
					.setDeliveryId(getDeliveryId(deliveryOrder))
					.setChannelDeliveryId(deliveryOrder.getChannelDeliveryId());
			log.info("ChannelDeliveryThriftService.queryRiderLocation begin, request={}", request);
			LocationInfo result = channelDeliveryThriftServiceClient.queryRiderLocation(request);
			log.info("ChannelDeliveryThriftService.queryRiderLocation finish, result={}", result);

			if (result != null && result.getStatus() != null && result.getStatus().getCode() == SUCCESS.getCode()) {
				return Optional.of(CoordinateUtil.translateToCoordinatePoint(result.getLongitude(), result.getLatitude()));
			}

			return Optional.empty();

		} catch (Exception e) {
			log.error("查询骑手位置失败", e);
			return Optional.empty();
		}
	}

	@Override
	public Optional<LaunchFailure> manualLaunchDelivery(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
		// 非牵牛花管理配送，当前deliveryPlatForm为0，也支持手动呼叫平台配送
		return SpringContextUtils.getBean(OrderChannelDeliveryPlatformClient.class).manualLaunchDelivery(deliveryPoi, orderInfo);
	}

	private Long getDeliveryId(DeliveryOrder deliveryOrder) {
		if (deliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.HAI_KUI_DELIVERY.getCode()) {
			return deliveryOrder.getOrderId();
		}
		return deliveryOrder.getId();
	}
}
