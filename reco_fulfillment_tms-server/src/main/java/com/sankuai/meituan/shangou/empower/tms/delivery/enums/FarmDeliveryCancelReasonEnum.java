package com.sankuai.meituan.shangou.empower.tms.delivery.enums;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryCancelReasonEnum;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 麦芽田取消配送原因枚举
 * <AUTHOR>
 */

public enum FarmDeliveryCancelReasonEnum {

    NO_CANCEL(0, "", ""),

    MERCHANT_CANCEL(1, "商户取消", ""),

    CARRIER_CANCEL(2, "配送商取消", ""),

    CUSTOMER_CANCEL(3, "用户取消", ""),

    TIME_OUT_CANCEL(4, "超时取消", ""),

    OTHER_PLATFORM_CANCEL(5, "其他平台取消（商户主动取消）", ""),

    SYSTEM_CANCEL(9, "系统取消", ""),

    CUSTOMER_NO_ANSWER_PHONE_CANCEL(10, "用户不接电话", ""),

    CUSTOMER_REFUND_CANCEL(11, "用户退单", ""),

    CUSTOMER_ADDRESS_ERROR_CANCEL(12, "用户地址错误", ""),

    DELIVERY_BEYOND_RANGE_CANCEL(13, "配送超出范围", ""),

    OTHER_CARRIER_ACCEPT_CANCEL(14, "已被平台其他配送接单", ""),

    ORDER_INFO_ERROR_CANCEL(15, "订单信息错误", ""),

    NO_RIDER_ACCEPT_CANCEL(16, "没有骑手接单", ""),

    ORDER_CANCEL(17, "订单取消", ""),

    OTHER_CANCEL(99, "其他原因", ""),
    ;

    private final int cancelCode;

    private final String cancelReason;

    private final String cancelDesc;

    private static final Map<Integer, FarmDeliveryCancelReasonEnum> CODE_TO_ENUM_MAP = new HashMap<>();

    static {
        for (FarmDeliveryCancelReasonEnum each : values()) {
            CODE_TO_ENUM_MAP.put(each.getCancelCode(), each);
        }
    }

    public int getCancelCode() {
        return this.cancelCode;
    }

    FarmDeliveryCancelReasonEnum(int cancelCode, String cancelReason, String cancelDesc) {
        this.cancelCode = cancelCode;
        this.cancelReason = cancelReason;
        this.cancelDesc = cancelDesc;
    }

    public static Optional<DeliveryCancelReasonEnum> map2QnhDeliveryCancelReasonEnum(Integer cancelCode) {
        if (Objects.isNull(cancelCode)) {
            return Optional.empty();
        }

        FarmDeliveryCancelReasonEnum farmDeliveryCancelReasonEnum = CODE_TO_ENUM_MAP.get(cancelCode);
        if (Objects.isNull(farmDeliveryCancelReasonEnum)) {
            return Optional.empty();
        }

        switch (farmDeliveryCancelReasonEnum) {
            case NO_CANCEL:
                return Optional.of(DeliveryCancelReasonEnum.NO_CANCEL);
            case MERCHANT_CANCEL:
            case OTHER_PLATFORM_CANCEL:
                return Optional.of(DeliveryCancelReasonEnum.MERCHANT_CANCEL);
            case CARRIER_CANCEL:
            case DELIVERY_BEYOND_RANGE_CANCEL:
            case NO_RIDER_ACCEPT_CANCEL:
            case OTHER_CARRIER_ACCEPT_CANCEL:
                return Optional.of(DeliveryCancelReasonEnum.CARRIER_CANCEL);
            case CUSTOMER_CANCEL:
            case CUSTOMER_NO_ANSWER_PHONE_CANCEL:
            case CUSTOMER_REFUND_CANCEL:
            case CUSTOMER_ADDRESS_ERROR_CANCEL:
                return Optional.of(DeliveryCancelReasonEnum.CUSTOMER_CANCEL);
            case TIME_OUT_CANCEL:
            case SYSTEM_CANCEL:
            case ORDER_INFO_ERROR_CANCEL:
            case ORDER_CANCEL:
            case OTHER_CANCEL:
                return Optional.of(DeliveryCancelReasonEnum.OTHER_CANCEL);
            default:
                return Optional.empty();
        }
    }
}
