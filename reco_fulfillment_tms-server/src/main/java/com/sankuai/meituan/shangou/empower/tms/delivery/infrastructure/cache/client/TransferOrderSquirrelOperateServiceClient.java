package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.client;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.TransferOperateInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.TransferOrderSquirrelServiceClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.TransferOrderSquirrelOperateService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class TransferOrderSquirrelOperateServiceClient implements TransferOrderSquirrelServiceClient {

    @Resource
    private TransferOrderSquirrelOperateService transferOrderSquirrelOperateService;

    @Override
    public String getKey(Long tenantId, Long storeId, Long orderId) {
        return transferOrderSquirrelOperateService.getKey(tenantId, storeId, orderId);
    }

    @Override
    public boolean set(String key, TransferOperateInfo transferOperate) {
        return transferOrderSquirrelOperateService.set(key, transferOperate);
    }
}
