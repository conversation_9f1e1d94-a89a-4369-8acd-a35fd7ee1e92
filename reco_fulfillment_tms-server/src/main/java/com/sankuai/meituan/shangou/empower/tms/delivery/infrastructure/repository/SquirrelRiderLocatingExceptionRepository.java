package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.dianping.squirrel.client.StoreKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderLocatingExceptionDataDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.RiderLocatingExceptionSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocatingExceptionDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocatingExceptionRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.ConvertUtil;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


@Slf4j
@Service
public class SquirrelRiderLocatingExceptionRepository implements RiderLocatingExceptionRepository {

    @Resource
    private RiderLocatingExceptionSquirrelOperationService squirrelOperationService;

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public void saveRiderLocatingException(RiderLocatingExceptionDetail riderLocatingException) {
        if (riderLocatingException == null) {
            return;
        }

        squirrelOperationService.set(riderLocatingException.getRiderAccount()+"", ConvertUtil.convertToLocatingExceptionDO(riderLocatingException));
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public Map<Long, Optional<RiderLocatingExceptionDetail>> getRiderLocatingExceptions(List<Long> riderAccountIdList) {
        if (CollectionUtils.isEmpty(riderAccountIdList)) {
            return Collections.emptyMap();
        }

        List<String> riderAccountStringList = riderAccountIdList.stream().map(Object::toString).collect(Collectors.toList());
        Map<StoreKey, Optional<RiderLocatingExceptionDataDO>> exceptionMap = squirrelOperationService.multiGet(riderAccountStringList, RiderLocatingExceptionDataDO.class);

        if (exceptionMap.isEmpty()) {
            return Collections.emptyMap();
        }

        HashMap<Long, Optional<RiderLocatingExceptionDetail>> result = new HashMap<>();
        exceptionMap.forEach(((storeKey, exceptionDataDOOpt) -> {
            Long riderAccountId = Long.valueOf(String.valueOf(storeKey.getParams()[0]));
            result.put(riderAccountId, exceptionDataDOOpt.map(ConvertUtil::convertToExceptionDetail));

        }));

        return result;

    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public Optional<RiderLocatingExceptionDetail> getRiderLocatingException(Long riderAccountId) {
        if (riderAccountId == null) {
            return Optional.empty();
        }

        Optional<RiderLocatingExceptionDataDO> dataOptional = squirrelOperationService.get(riderAccountId + "", RiderLocatingExceptionDataDO.class);
        return dataOptional.map(ConvertUtil::convertToExceptionDetail);

    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public void deleteRiderLocatingException(Long riderAccountId) {
        if (riderAccountId == null) {
            return;
        }
        squirrelOperationService.delete(riderAccountId+"");
    }
}
