package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.wrapper;

import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.sac.dto.model.SacRoleDTO;
import com.meituan.shangou.sac.dto.request.search.QuerySacAccountRoleRequest;
import com.meituan.shangou.sac.dto.response.search.QuerySacAccountRoleResponse;
import com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SacAccountSearchThriftServiceWrapper {

    @Resource
    private SacAccountSearchThriftService sacAccountSearchThriftService;


    @MethodLog(logRequest = false, logResponse = true)
    public List<SacRoleDTO> queryAccountRoleInfo(QuerySacAccountRoleRequest request) {
        try {
            QuerySacAccountRoleResponse resp = sacAccountSearchThriftService.querySacAccountRole(request);
            if (resp == null || resp.getSacStatus() == null || ResultCode.SUCCESS.getCode() != resp.getSacStatus().getCode()) {
                throw new CommonRuntimeException("查询权限异常, response is null");
            }
            return resp.getSacRoleDTOS();
        } catch (Exception e) {
            throw new CommonRuntimeException(e);
        }

    }
}
