package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.reco.pickselect.common.mq.consumer.ConsumerConfig;
import com.meituan.reco.pickselect.common.mq.consumer.ConsumerRetryException;
import com.meituan.reco.pickselect.common.mq.consumer.RetryConfig;
import com.meituan.reco.pickselect.common.mq.producer.ProducerConfig;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.TxdDeliveryCallbackMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.QueryDeliveryInfoApplicationService;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.DeliveryCallbackThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request.FarmDeliveryChangeNotifyReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.response.FarmDeliveryChangeNotifyResp;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.TxdOrderChannelDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.CommonLogicException;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaTopicEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;


/**
 * 淘鲜达平台配送状态变更消费者
 * 淘鲜达平台配送没有提供消息订阅，当前是基于订单信息构造的运单DeliveryOrder
 * <AUTHOR>
 */
@Slf4j
@Component
public class TxdDeliveryChangeMessageListener extends AbstractMqListener {

	/**
	 * 淘鲜达平台配送承运商信息，渠道没有回传牵牛花自己维护的
	 */
	private static final String TXD_LOGISTIC_MARK = "txd";

	/**
	 * 淘鲜达平台配送渠道运单ID，渠道没有回传牵牛花自己维护的
	 */
	private static final String TXD_LOGISTIC_NO = "0";

	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;

	@Resource
	private OrderSystemClient orderSystemClient;

	@Resource
	private QueryDeliveryInfoApplicationService queryDeliveryInfoApplicationService;

	@Resource
	private DeliveryChannelApplicationService deliveryChannelApplicationService;

	@Resource
	private DeliveryCallbackThriftService deliveryCallbackThriftService;

	@Override
	protected ConsumerConfig config() {
		return MafkaConsumerEnum.TXD_DELIVERY_ORDER_CHANGE_CONSUMER;
	}

	@Override
	protected RetryConfig retryConfig() {
		return new RetryConfig() {
			@Override
			public ProducerConfig producerConfig() {
				return MafkaTopicEnum.TXD_DELIVERY_ORDER_CHANGE_TOPIC;
			}

			@Override
			public int maxRetry() {
				return MccConfigUtils.getTxdPlatformDeliveryMqMaxRetryTimes();
			}

			@Override
			public int delayInSeconds() {
				return MccConfigUtils.getTxdPlatformDeliveryMqDelaySeconds();
			}
		};
	}

	@Override
	protected void consume(MafkaMessage mafkaMessage) throws ConsumerRetryException {
		try {
			if (!MccConfigUtils.getTxdDeliveryChangeMessageListenerSwitch()) {
				log.info("TxdDeliveryChangeMessageListener switch is off");
				return;
			}

			log.info("TxdDeliveryChangeMessageListener consume: {}", mafkaMessage);
			TxdDeliveryCallbackMsg msg = translateMessage(mafkaMessage);
			if (Objects.isNull(msg) || !msg.isValid()) {
				log.error("TxdDeliveryChangeMessageListener, msg is invalid");
				throw new CommonLogicException("TxdDeliveryChangeMessageListener, msg is invalid");
			}

			Result<OrderInfo> orderInfoResult = orderSystemClient.queryByViewOrderId(DynamicOrderBizType.TAO_XIAN_DA.getValue(), msg.getChannelOrderId(),false);
			if (orderInfoResult.isFail()) {
				// 查订单失败需要重试
				log.error("TxdDeliveryChangeMessageListener, orderInfoResult is fail, orderInfoResult is {}", orderInfoResult);
				throw new CommonRuntimeException("TxdDeliveryChangeMessageListener, orderInfoResult is fail");
			}

			DeliveryOrder deliveryOrder;
			OrderInfo orderInfo = orderInfoResult.getInfo();
			Long orderId = orderInfo.getOrderKey().getOrderId();
			if (isFilterTxdDeliveryChangeMessage(orderInfo)) {
				log.info("TxdDeliveryChangeMessageListener, order is filter, orderId is {}", orderId);
				return;
			}

			Optional<DeliveryOrder> deliveryOrderOptional = queryDeliveryInfoApplicationService.queryDeliveryOrderByOrderId(orderId,orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
			if (deliveryOrderOptional.isPresent()) {
				deliveryOrder = deliveryOrderOptional.get();
				// 只有当淘鲜达渠道订单在商家端发的是平台配送才会走后续逻辑
				if (!isOrderChannelDeliveryPlatFormDeliveryOrder(deliveryOrder)) {
					log.info("TxdDeliveryChangeMessageListener, deliveryOrder is not platform delivery, orderId is {}", orderId);
					return;
				}
			} else {
				deliveryOrder = getInitActiveDeliveryOrder(orderInfo, msg);
				deliveryOrderRepository.save(deliveryOrder);
			}

			FarmDeliveryChangeNotifyReq req = buildFarmDeliveryChangeNotifyReq(msg, orderInfo);
			log.info("TxdDeliveryChangeMessageListener invoke farmNotifyDeliveryChange request = {}", req);
			FarmDeliveryChangeNotifyResp farmDeliveryChangeNotifyResp = deliveryCallbackThriftService.farmNotifyDeliveryChange(req);
			log.info("TxdDeliveryChangeMessageListener invoke farmNotifyDeliveryChange response = {}", farmDeliveryChangeNotifyResp);
		} catch (CommonLogicException e) {
			log.error("TxdDeliveryChangeMessageListener CommonLogicException, message={}", mafkaMessage, e);
		} catch (Exception e) {
			log.error("TxdDeliveryChangeMessageListener Exception, message={}", mafkaMessage, e);
			throw new ConsumerRetryException(e);
		}
	}

	private TxdDeliveryCallbackMsg translateMessage(MafkaMessage mafkaMessage) {
		try {
			return Optional.ofNullable(mafkaMessage)
					.map(MafkaMessage::getBody)
					.map(Object::toString)
					.map(it -> JsonUtil.fromJson(it, TxdDeliveryCallbackMsg.class))
					.orElse(null);
		} catch (Exception e) {
			log.error("TxdDeliveryChangeMessageListener translateMessage error", e);
			Cat.logEvent("TXD_DELIVERY_ORDER_CHANGE_CONSUMER_FAILED", "MESSAGE_WRONG");
			return null;
		}
	}

	/**
	 * 屏蔽淘鲜达自配送和自提单的处理，返回true代表过滤不处理
	*/
	private boolean isFilterTxdDeliveryChangeMessage(OrderInfo orderInfo) {
		// 订单终态不处理
		if (orderInfo.isFinished()) {
			return true;
		}

		// 自提单不处理
		if (orderInfo.isStoreDelivery()) {
			return true;
		}

		// 自配送单不处理
		Integer orderSource = orderInfo.getOrderSource();
		if (orderSource == OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue() || orderSource == OrderSourceEnum.GLORY.getValue()) {
			return orderInfo.isSelfDelivery() && orderInfo.isDeliveryToHome();
		} else if (orderSource == OrderSourceEnum.OTO_ONLINE.getValue()) {
			return orderInfo.isDeliveryToHome();
		}

		return false;
	}

	private FarmDeliveryChangeNotifyReq buildFarmDeliveryChangeNotifyReq(TxdDeliveryCallbackMsg msg, OrderInfo orderInfo) {
		FarmDeliveryChangeNotifyReq req = new FarmDeliveryChangeNotifyReq();

		req.setOriginId(String.valueOf(orderInfo.getOrderKey().getOrderId()));
		req.setPlatformCode(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode());
		req.setOrderChannelType(DynamicChannelType.TAO_XIAN_DA.getChannelId());
		req.setLogistic(TXD_LOGISTIC_MARK);
		req.setLogisticNo(TXD_LOGISTIC_NO);
		req.setTimestamp(TimeUtil.fromMilliSeconds2Seconds(msg.getUpdateTime()));

		TxdOrderChannelDeliveryStatusEnum txdDeliveryStatusEnum = TxdOrderChannelDeliveryStatusEnum.enumOf(msg.getChannelOrderStatus());
		if (Objects.nonNull(txdDeliveryStatusEnum)) {
			req.setStatus(txdDeliveryStatusEnum.getCode());
		}

		if (StringUtils.isNotEmpty(msg.getRiderName()) && StringUtils.isNotEmpty(msg.getRiderPhone())) {
			req.setRiderName(msg.getRiderName());
			req.setRiderPhone(msg.getRiderPhone());
		}

		return req;
	}

	private boolean isOrderChannelDeliveryPlatFormDeliveryOrder(DeliveryOrder deliveryOrder) {
		DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
		if (Objects.isNull(deliveryChannelDto) || Objects.isNull(deliveryChannelDto.getDeliveryPlatFormCode())) {
			return false;
		}

		// 淘鲜达渠道在牵牛花门店配送配置里没有「平台配送」，淘鲜达发平台配送只能通过商家端发
		return DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode() == deliveryChannelDto.getDeliveryPlatFormCode();
	}

	private DeliveryOrder getInitActiveDeliveryOrder(OrderInfo orderInfo, TxdDeliveryCallbackMsg msg) {
		Integer deliveryChannel = MccConfigUtils.getTxdPlatformDeliveryChannelCode();
		DeliveryOrder deliveryOrder = new DeliveryOrder(orderInfo, deliveryChannel);
		deliveryOrder.activate();
		deliveryOrder.setDeliveryCount(NumberUtils.INTEGER_ONE);

		LocalDateTime updateTime = TimeUtil.fromMilliSeconds(msg.getUpdateTime());
		String channelOrderStatus = msg.getChannelOrderStatus();
		Optional<DeliveryStatusEnum> deliveryStatusEnumOptional = TxdOrderChannelDeliveryStatusEnum.mapToQnhDeliveryStatus(channelOrderStatus);
		deliveryStatusEnumOptional.ifPresent(deliveryStatusEnum -> deliveryOrder.onStatusChangeWithOutLog(deliveryStatusEnum, updateTime));

		if (StringUtils.isNotEmpty(msg.getRiderName()) && StringUtils.isNotEmpty(msg.getRiderPhone())) {
			deliveryOrder.setRiderInfo(new Rider(msg.getRiderName(), msg.getRiderPhone(), StringUtils.EMPTY));
		}

		return deliveryOrder;
	}

}
