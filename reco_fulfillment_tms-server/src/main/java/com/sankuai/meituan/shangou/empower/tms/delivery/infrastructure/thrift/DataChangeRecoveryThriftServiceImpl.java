package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import javax.annotation.Resource;

import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository.StoreConfigMergeConverter;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.StoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.StoreConfigOpInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.StoreConfigOpLog;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.StoreConfigOpTypeEnum;
import com.sankuai.meituan.waimaidata.disaster.utils.DataChangeRecoveryThriftService;
import com.sankuai.meituan.waimaidata.disaster.utils.dto.RepairParam;
import com.sankuai.meituan.waimaidata.disaster.utils.dto.RepairResult;
import com.sankuai.meituan.waimaidata.disaster.utils.dto.RepairStatus;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
@Slf4j
@Service
public class DataChangeRecoveryThriftServiceImpl implements DataChangeRecoveryThriftService {

    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    @Resource
    private StoreConfigMergeConverter storeConfigMergeConverter;

    @Override
    public RepairResult repairData(RepairParam repairParam) {
        RepairResult repairResult = new RepairResult();
        repairResult.setStatus(RepairStatus.REPAIR_FAILURE);
        repairResult.setMessage("修复数据为空，修复失败");
        try {
            log.info("repairData: {}", JsonUtil.toJson(repairParam));
            if (repairParam == null || repairParam.getDataInfo() == null) {
                return repairResult;
            }
            StoreConfig storeConfig = JSON.parseObject(repairParam.getDataInfo().getDataMapJson(), StoreConfig.class);
            if (storeConfig == null || storeConfig.getId() == null || storeConfig.getUpdateTime() == null) {
                return repairResult;
            }
            StoreConfig diffStoreConfig = JSON.parseObject(repairParam.getDataInfo().getDiffMapJson(), StoreConfig.class);
            storeConfigMergeConverter.mergeStoreConfigFromDiff(diffStoreConfig, storeConfig);
            // 清除updateTime，使用当前时间
            storeConfig.setUpdateTime(null);
            int updateCount = deliveryPoiRepository.updateStoreConfigByPrimaryKeySelective(storeConfig);
            // 更新数据不为1，修复失败
            if (updateCount != 1) {
                repairResult.setMessage("更新数据失败，修复失败");
                return repairResult;
            }
            // 插入操作日志
            deliveryPoiRepository.batchSaveStoreConfigOpLog(Lists.newArrayList(transfer2OpLog(storeConfig)));
            repairResult.setStatus(RepairStatus.REPAIR_SUCCESS);
            repairResult.setMessage("修复成功");
            return repairResult;
        }
        catch (Exception e) {
            log.error("repairData error, repairParam:{}", JsonUtil.toJson(repairParam), e);
        }
        repairResult.setMessage("修复失败");
        return repairResult;
    }

    private StoreConfigOpLog transfer2OpLog(StoreConfig storeConfig) {
        StoreConfigOpInfo opInfo = StoreConfigOpInfo.builder()
                .channelType(storeConfig.getChannelType())
                .deliveryPoint(storeConfig.getDeliveryLaunchPoint())
                .deliveryPlatform(storeConfig.getOpenAggrPlatform())
                .deliveryLaunchDelayMinutes(storeConfig.getDeliveryLaunchDelayMinutes())
                .bookingDeliveryLaunchDelayMinutes(storeConfig.getBookingOrderDeliveryLaunchMinutes())
                .build();
        return StoreConfigOpLog.builder()
                .storeConfigId(storeConfig.getId())
                .tenantId(storeConfig.getTenantId())
                .storeId(storeConfig.getStoreId())
                .opType(StoreConfigOpTypeEnum.MODIFY.getValue())
                .opInfo(JSON.toJSONString(opInfo))
                // 容灾平台使用-2作为operatorId
                .operatorId((long) (-2))
                .operatorName("容灾平台")
                .build();
    }
}
