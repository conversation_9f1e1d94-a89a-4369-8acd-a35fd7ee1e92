package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.TransDeliveryTypeOperationTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.SyncRiderPositionMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChangeNotifyService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryChangeInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/9
 */
@Service
@SuppressWarnings({"SpellCheckingInspection"})
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class MafkaDeliveryChangeNotifyServiceImpl implements DeliveryChangeNotifyService {

	@Resource
	private MafkaMessageProducer<DeliveryChangeLogMessage> deliveryChangeLogMessageProducer;
	@Resource
	private MafkaDelayMessageProducer<RiderAssignTimeOutCheckMessageHolder> riderAssignTimeOutCheckMessageProducer;
	@Resource
	private MafkaDelayMessageProducer<SyncRiderPositionMessage> riderPositionSyncProducer;
	@Resource
	private MafkaMessageProducer<SyncRiderPositionMessage> riderPositionImmediatelySyncProducer;
	@Resource
	private MafkaMessageProducer<DeliveryCancelCommandMessage> deliveryCancelMessageProducer;
	@Resource
	private MafkaDelayMessageProducer<NewSupplyDeliveryTimeOutCheckMessage> deliveryTimeOutMessageProducer;

	@Resource
	private MafkaMessageProducer<DrunkHorseTransDeliveryTypeMessage> drunkHorseTransDeliveryTypeMessageProducer;

	private static final Integer DEFAULT_SYNC_RIDER_LOCATION_PERIOD = 2 * 60 * 1000;

	@Override
	public void notifyDeliveryChangeLog(Long deliveryId, DeliveryEventEnum deliveryEvent, DeliveryChangeInfo changeInfo, LocalDateTime changeTime) {
		deliveryChangeLogMessageProducer.sendMessage(
				new DeliveryChangeLogMessage(deliveryId, deliveryEvent.getCode(), JsonUtil.toJson(changeInfo), changeTime),
				deliveryId
		);
	}

	@Override
	public void notifyRiderAssignTimeOutCheckTriggered(DeliveryOrder deliveryOrder, int checkMinutes) {
		riderAssignTimeOutCheckMessageProducer.sendDelayMessage(new RiderAssignTimeOutCheckMessageHolder(new RiderAssignTimeOutCheckMessage(deliveryOrder)), checkMinutes);
	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public void notifySyncRiderPosition(Long deliveryId, DeliveryPlatformEnum platformEnum,Long tenantId,Long storeId) {
		if(platformEnum==DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY){
			riderPositionSyncProducer.sendDelayMessageInMillis(new SyncRiderPositionMessage(deliveryId,tenantId,storeId), MccConfigUtils.getRiderLocationSyncTime());
			return;
		}
		riderPositionSyncProducer.sendDelayMessage(new SyncRiderPositionMessage(deliveryId,tenantId,storeId), ConfigUtilAdapter.getInt("rider_sync_internal", 2));
	}

	@Override
	public void notifySyncDrunkHorseRiderPosition(Long deliveryId, DeliveryPlatformEnum platformEnum,Long tenantId,Long storeId, boolean feDirectSyncRiderPosition) {
		riderPositionSyncProducer.sendDelayMessageInMillis(new SyncRiderPositionMessage(deliveryId,tenantId,storeId, feDirectSyncRiderPosition), MccConfigUtils.getDrunkHorseRiderLocationSyncTime());
	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public void notifySyncRiderPosition(Long deliveryId, DeliveryPlatformEnum platformEnum, Integer orderBizType,Long tenantId,Long storeId) {
		Integer period = getSelfDeliverySyncRiderLocationPeriod(orderBizType, platformEnum);
		riderPositionSyncProducer.sendDelayMessageInMillis(new SyncRiderPositionMessage(deliveryId,tenantId,storeId), period);
	}

	@Override
	public void notifyDeliveryCancel(Long tenantId, Long storeId , Long deliveryId) {
		deliveryCancelMessageProducer.sendMessage(new DeliveryCancelCommandMessage(tenantId, storeId, deliveryId));
	}

	@Override
	public void notifyDeliveryTimeOutCheckTriggered(Long orderId, Long tenantId, Long storeId, LocalDateTime estimatedDeliveryTime, Long delayMillis) {
		NewSupplyDeliveryTimeOutCheckMessage message = new NewSupplyDeliveryTimeOutCheckMessage();
		message.setOrderId(orderId);
		message.setTenantId(tenantId);
		message.setStoreId(storeId);
		message.setEstimatedDeliveryTime(TimeUtil.toMilliSeconds(estimatedDeliveryTime));
		deliveryTimeOutMessageProducer.sendDelayMessageInMillis(message, delayMillis);
	}

	@Override
	public void notifyDrunkHorseTransDeliveryType(String viewOrderId, Integer orderBizType, TransDeliveryTypeOperationTypeEnum transType,
												  Long newRiderAccountId, String newRiderName) {
		drunkHorseTransDeliveryTypeMessageProducer.sendMessage(new DrunkHorseTransDeliveryTypeMessage(viewOrderId, orderBizType, transType.getValue(), newRiderAccountId, newRiderName));
	}

	/**
	 * 获取新供给自配送同步骑手坐标频率，单位毫秒
	*/
	private Integer getSelfDeliverySyncRiderLocationPeriod(Integer orderBizType, DeliveryPlatformEnum platformEnum) {
		try {
			String periodMapping = MccConfigUtils.getSelfDeliverySyncRiderLocationPeriodMapping();
			if (StringUtils.isEmpty(periodMapping)) {
				return getDefaultSyncRiderLocationPeriod(platformEnum);
			}

			Map<Integer, Map<Integer, Integer>> periodMap = JsonUtil.fromJson(periodMapping, new TypeReference<Map<Integer, Map<Integer, Integer>>>() {});
			if (MapUtils.isEmpty(periodMap)) {
				return getDefaultSyncRiderLocationPeriod(platformEnum);
			}
			Map<Integer, Integer> innerMapping = periodMap.get(orderBizType);
			if (MapUtils.isEmpty(innerMapping)) {
				return getDefaultSyncRiderLocationPeriod(platformEnum);
			}
			Integer period = innerMapping.get(platformEnum.getCode());
			if (Objects.isNull(period)) {
				return getDefaultSyncRiderLocationPeriod(platformEnum);
			}
			return period;
		} catch (Exception e) {
			log.error("MafkaDeliveryChangeNotifyServiceImpl getSelfDeliverySyncRiderLocationPeriod error");
			return getDefaultSyncRiderLocationPeriod(platformEnum);
		}
	}

	/**
	 * 获取默认的新供给自配送同步骑手坐标频率，单位毫秒
	 */
	private Integer getDefaultSyncRiderLocationPeriod(DeliveryPlatformEnum platformEnum) {
		switch (platformEnum) {
			case MALT_FARM_DELIVERY_PLATFORM:
			case DAP_DELIVERY_PLATFORM:
				return ConfigUtilAdapter.getInt("rider_sync_internal", 2) * 60 * 1000;
			case MERCHANT_SELF_DELIVERY:
				return MccConfigUtils.getRiderLocationSyncTime();
			default:
				return DEFAULT_SYNC_RIDER_LOCATION_PERIOD;
		}
	}

	@Override
	public void notifySyncDrunkHorseRiderPositionImmediately(Long deliveryId, DeliveryPlatformEnum platformEnum, Long tenantId, Long storeId, boolean feDirectSyncRiderPosition) {
		riderPositionImmediatelySyncProducer.sendMessage(new SyncRiderPositionMessage(deliveryId,tenantId,storeId, feDirectSyncRiderPosition));

	}
}
