package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache;

import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class DeliveryChannelSquirrelOperationService extends SquirrelOperateService {

    @Autowired
    @Qualifier("redisSgNewSupplyOfc")
    protected RedisStoreClient redisNewSupplyClient;

    private static final String CATEGORY_NAME = "delivery_channel";

    @Override
    protected RedisStoreClient getRedisClient() {
        return redisNewSupplyClient;
    }

    @Override
    public String getCategoryName() {
        return CATEGORY_NAME;
    }
}
