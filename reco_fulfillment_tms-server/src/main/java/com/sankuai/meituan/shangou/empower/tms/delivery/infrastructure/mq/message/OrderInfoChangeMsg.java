package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message;

import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.order.platform.enums.ChangedFieldEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 订单信息修改消息
 * <AUTHOR>
 */
@Data
public class OrderInfoChangeMsg {

    private Long bornTimestamp;

    private Long delayInMs;

    private String body;

    public Body parseBody() {
        Body obj = JacksonUtils.fromJson(body, Body.class);
        return Objects.requireNonNull(obj, "Message body must not be null");
    }

    @Data
    public static class Body {

        private Integer orderBizType;

        private Long orderId;

        private String viewOrderId;

        private Long shopId;

        private Long warehouseId;

        private Long tenantId;

        private Integer distributeStatus;

        private Integer orderSource;

        private Long updateTime;

        private Integer isStatusChange;

        private Integer isRiderInfoChange;

        private Integer isUserInfoChange;

        /**
         * 修改的字段
         *
         * @see com.meituan.shangou.saas.order.platform.enums.ChangedFieldEnum
         */
        private List<Integer> changedFields;

        public boolean hasFieldChanged(ChangedFieldEnum field) {
            Objects.requireNonNull(field, "ChangedFieldEnum must not be null");
            return CollectionUtils.isNotEmpty(changedFields) && changedFields.contains(field.getValue());
        }

        public Long getPoiId() {
            return warehouseId != null ? warehouseId : shopId;
        }
    }

}
