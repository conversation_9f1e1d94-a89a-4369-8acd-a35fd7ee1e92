package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.wrapper;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.sdms.sdk.limit.LimitAcceptOrderThriftService;
import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Rhino
@Slf4j
public class LimitAcceptServiceWrapper {
    @Resource
    private LimitAcceptOrderThriftService limitAcceptOrderThriftService;

    @Degrade(rhinoKey = "LimitAcceptServiceWrapper.queryLimitItemByAccountId", fallBackMethod = "queryLimitItemByAccountIdFallback", timeoutInMilliseconds = 5000)
    public List<LimitItemDTO> queryLimitItemByAccountId(Long tenantId, Long accountId, Long storeId) {
        try {
            log.info("start invoke limitAcceptOrderThriftService.queryLimitItemListByAccountId,tenantId: {},  accountId: {} ,storeId:{}", tenantId, accountId, storeId);
            TResult<List<LimitItemDTO>> result = limitAcceptOrderThriftService.queryLimitItemListByAccountIdNew(tenantId, accountId, storeId);
            log.info("end invoke limitAcceptOrderThriftService.queryLimitItemListByAccountId, result: {}", result);

            if (!result.isSuccess()) {
                throw new BizException("查询限制接单项失败:" + result.getMsg());
            }

            return result.getData();
        } catch (Exception e) {
            Cat.logEvent("LIMIT_ACCEPT", "QUERY_LIMIT_ITEM_ERROR");
            log.error("查询限制接单项失败", e);
            return Collections.emptyList();
        }
    }

    @Degrade(rhinoKey = "LimitAcceptServiceWrapper.queryLimitItemByAccountIdList", fallBackMethod = "queryLimitItemByAccountIdListFallback", timeoutInMilliseconds = 5000)
    public Map<Long, List<LimitItemDTO>> queryLimitItemByAccountIdList(Long tenantId, List<Long> accountIdList, Long storeId) {
        try {
            log.info("start invoke limitAcceptOrderThriftService.queryLimitItemListByAccountId,tenantId: {},  accountIdList: {},storeId:{}", tenantId, accountIdList, storeId);
            TResult<Map<Long, List<LimitItemDTO>>> tResult = limitAcceptOrderThriftService.queryLimitItemListByAccountIdListNew(tenantId, accountIdList, storeId);
            log.info("end invoke limitAcceptOrderThriftService.queryLimitItemListByAccountId, result: {}", tResult);

            if (!tResult.isSuccess()) {
                throw new BizException("查询限制接单项失败:" + tResult.getMsg());
            }

            return tResult.getData();
        } catch (Exception e) {
            Cat.logEvent("LIMIT_ACCEPT", "QUERY_LIMIT_ITEM_ERROR");
            log.error("查询限制接单项失败", e);
            return Collections.emptyMap();
        }
    }

    public Map<Long, List<LimitItemDTO>> queryLimitItemByAccountIdListFallback(Long tenantId, List<Long> accountIdList, Long storeId) {
        log.error("LimitAcceptServiceWrapper.queryLimitItemByAccountIdList 发生降级");
        return Collections.emptyMap();
    }


    public List<LimitItemDTO> queryLimitItemByAccountIdFallback(Long tenantId, Long accountId, Long storeId) {
        log.error("LimitAcceptServiceWrapper.queryLimitItemByAccountId 发生降级");
        return Collections.emptyList();
    }
}
