package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer;

import com.meituan.reco.pickselect.common.mq.producer.ProducerConfig;

import java.util.HashMap;
import java.util.Map;


public enum MafkaTopicEnum implements ProducerConfig {

    /**
     * 用于配置死信的ProducerConfig，实际无用
     */
    DEFAULT_CONFIG("", "waimai", "com.sankuai.waimai.sc.pickselectservice"),

    /**
     * 抖音平台配送状态变更
     */
    DY_DELIVERY_ORDER_CHANGE_TOPIC("shangou_empower_dy_delivery_order_change_topic", "waimai", "com.sankuai.shangou.empower.ocmschannel"),

    /**
     * 配送状态同步给订单侧的topic
     */
    DELIVERY_CHANGE_NOTIFY_TMS_TOPIC("shangou_empower_delivery_change_topic", "waimai", "com.sankuai.sgfulfillment.tms"),

    /**
     * 淘鲜达平台配送状态变更
     */
    TXD_DELIVERY_ORDER_CHANGE_TOPIC("shangou_empower_txd_delivery_order_change_topic", "waimai", "com.sankuai.shangou.empower.ocmschannel"),

    /**
     * tms监听订单信息修改topic
     */
    ORDER_INFO_CHANGE_TOPIC("shangou_empower_delivery_update_topic", "waimai", "com.sankuai.shangou.empower.orderbiz"),

     ;

    private final String topic;
    private final String bgName;
    private final String producerAppKey;

    MafkaTopicEnum(String topic, String bgName, String producerAppKey) {
        this.topic = topic;
        this.bgName = bgName;
        this.producerAppKey = producerAppKey;
    }

    MafkaTopicEnum(String topic) {
        this(topic, "waimai", "com.sankuai.waimai.sc.pickselectservice");
    }

    @Override
    public String getBgName() {
        return bgName;
    }

    public String getProducerAppKey() {
        return producerAppKey;
    }

    @Override
    public String getTopic() {
        return topic;
    }

    @Override
    public String getAppKey() {
        return producerAppKey;
    }

    private static final Map<String, MafkaTopicEnum> TOPIC_MAP = new HashMap<>();

    static {
        for (MafkaTopicEnum mqTopicEnum : MafkaTopicEnum.values()) {
            TOPIC_MAP.put(mqTopicEnum.getTopic(), mqTopicEnum);
        }
    }

    public static MafkaTopicEnum fromTopic(String topicText) {

        return TOPIC_MAP.get(topicText);
    }
}
