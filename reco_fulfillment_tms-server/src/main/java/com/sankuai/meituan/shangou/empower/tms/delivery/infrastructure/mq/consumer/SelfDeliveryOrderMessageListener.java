package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.SelfDeliveryOrderContent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryMarkSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;


/**
 * <AUTHOR>
 * @date 2024-09-12
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class SelfDeliveryOrderMessageListener extends AbstractDeadLetterConsumer {
    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;
    @Autowired
    private OrderSystemClient orderSystemClient;
    @Autowired
    private DeliveryMarkSquirrelOperationService deliveryMarkSquirrelOperationService;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.SELF_DELIVERY_ORDER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("消费自配送订单状态消息：{}", message);
        try {
            SelfDeliveryOrderContent content = translateMessage(message, SelfDeliveryOrderContent.class);
            if (!check(content)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            DeliveryStatusEnum deliveryStatus = DeliveryStatusEnum.valueOf(content.getStatus());
            if (Objects.isNull(deliveryStatus)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            int orderBizType = DynamicOrderBizType.channelId2OrderBizType(content.getChannelType()).getValue();
            String viewOrderId = content.getOrderId();
            // 查询配送标记信息
            Optional<Integer> deliveryChannel = deliveryMarkSquirrelOperationService.get(orderBizType, viewOrderId);
            // 过滤非三方自配送订单
            final int otherSelfDeliveryCode = DeliveryChannelEnum.OTHER_SELF_DELIVERY_MNG.getCode();
            if (deliveryChannel.isPresent() && !Objects.equals(otherSelfDeliveryCode, deliveryChannel.get())) {
                log.info("消费自配送订单状态消息 牵牛花配送信息已存在 deliveryChannel={}", deliveryChannel.get());
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 查询订单信息
            Result<OrderInfo> orderInfoResult = orderSystemClient.queryByViewOrderId(orderBizType, viewOrderId, false);
            OrderInfo orderInfo = orderInfoResult.getInfo();
            if (orderInfo == null) {
                log.error("消费自配送订单状态消息，orderInfo is null");
                return ConsumeStatus.CONSUME_FAILURE;
            }
            if (Objects.nonNull(orderInfo.getOrderTransInfo()) && Objects.nonNull(orderInfo.getOrderTransInfo().getDispatchShopId())) {
                log.error("转单订单不处理，viewOrderId:{}", viewOrderId);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            Long orderId = orderInfo.getOrderKey().getOrderId();
            Long tenantId = orderInfo.getOrderKey().getTenantId();
            Long storeId = orderInfo.getWarehouseId();
            // 查询配送单信息
            Optional<DeliveryOrder> deliveryOrderOptional = deliveryOrderRepository.getLastDeliveryOrderSlave(orderId, tenantId, storeId);
            if (deliveryOrderOptional.isPresent()) {
                DeliveryOrder deliveryOrder = deliveryOrderOptional.get();
                if (!Objects.equals(otherSelfDeliveryCode, deliveryOrder.getDeliveryChannel())) {
                    deliveryMarkSquirrelOperationService.saveDeliveryMark(orderBizType, viewOrderId, deliveryOrder.getDeliveryChannel());
                    log.info("消费自配送订单状态消息，订单已存在配送信息");
                } else {
                    DeliveryEventEnum deliveryEvent = DeliveryEventEnum.getEventByStatus(deliveryStatus);
                    LocalDateTime changeTime = TimeUtil.fromMilliSeconds(content.getOperateTime());
                    if (StringUtils.isNoneBlank(content.getRiderName(),content.getRiderPhone())) {
                        deliveryOrder.setRiderInfo(new Rider(content.getRiderName(), content.getRiderPhone(), StringUtils.EMPTY));
                    }
                    deliveryOrder.onChange(deliveryEvent, buildDeliveryException(deliveryStatus), deliveryOrder.getRiderInfo(), changeTime);
                }
            } else {
                initDeliverOrder(content, orderInfo, deliveryStatus);
            }
            return  ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("消费三方自配送订单状态消息异常", e);
            return ConsumeStatus.CONSUME_FAILURE;
        }
    }

    private boolean check(SelfDeliveryOrderContent content) {
        if (Objects.isNull(content)) {
            log.error("消费自配送订单状态消息过滤：msg is null");
            return false;
        }

        if (StringUtils.isBlank(content.getOrderId())
                || Objects.isNull(content.getChannelType())
                || Objects.isNull(content.getTenantId())
                || Objects.isNull(content.getStatus())) {
            log.error("消费自配送订单状态消息过滤：msg param is error");
            return false;
        }
        DynamicOrderBizType dynamicOrderBizType = DynamicOrderBizType.channelId2OrderBizType(content.getChannelType());
        if (Objects.isNull(dynamicOrderBizType)) {
            log.info("消费自配送订单状态消息过滤：无效的渠道类型 不处理。channelType: {}", content.getChannelType());
            return false;
        }

        if (Objects.isNull(content.getOperateTime())) {
            content.setOperateTime(System.currentTimeMillis());
        }

        Long tenantId = content.getTenantId();
        if (!MccConfigUtils.getSelfDeliveryOrderConsumeSwitch(tenantId)) {
            log.info("消费自配送订单状态消息过滤：非白名单租户 不处理。tenantId: {}", tenantId);
            return false;
        }
        return true;
    }

    /**
     * 无运单时，运单初始化
     */
    private void initDeliverOrder(SelfDeliveryOrderContent content, OrderInfo orderInfo, DeliveryStatusEnum deliveryStatus) {
        DeliveryEventEnum deliveryEvent = DeliveryEventEnum.getEventByStatus(deliveryStatus);
        LocalDateTime changeTime = TimeUtil.fromMilliSeconds(content.getOperateTime());
        DeliveryOrder deliveryOrder = new DeliveryOrder(orderInfo, DeliveryChannelEnum.OTHER_SELF_DELIVERY_MNG.getCode());
        deliveryOrder.setPlatformSourceEnum(PlatformSourceEnum.OMS);
        deliveryOrder.setOrderKey(new OrderKey(orderInfo.getOrderKey().getTenantId(), orderInfo.getWarehouseId(), orderInfo.getOrderKey().getOrderId()));
        deliveryOrder.setStoreId(orderInfo.getWarehouseId());
        deliveryOrder.activate();
        deliveryOrder.setDeliveryCount(1);
        deliveryOrder.setPickDeliverySplitTag(true);
        if (StringUtils.isNoneBlank(content.getRiderName(),content.getRiderPhone())) {
            deliveryOrder.setRiderInfo(new Rider(content.getRiderName(), content.getRiderPhone(), StringUtils.EMPTY));
        }
        deliveryOrder.onStatusChangeWithOutLog(deliveryStatus, changeTime);
        deliveryOrderRepository.save(deliveryOrder);
        deliveryOrder.onChange(deliveryEvent, buildDeliveryException(deliveryStatus), deliveryOrder.getRiderInfo(), changeTime);
    }

    private DeliveryExceptionInfo buildDeliveryException(DeliveryStatusEnum deliveryStatus) {
        if (deliveryStatus.isExceptionStatus()) {
            return new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.UNKNOWN, StringUtils.EMPTY);
        } else {
            return DeliveryExceptionInfo.NO_EXCEPTION;
        }
    }
}
