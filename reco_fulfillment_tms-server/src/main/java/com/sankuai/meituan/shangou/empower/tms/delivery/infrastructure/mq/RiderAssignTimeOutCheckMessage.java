package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiderAssignTimeOutCheckMessage {

	/**
	 * 租户ID
	 */
	private Long tenantId;

	/**
	 * 门店ID
	 */
	private Long shopId;

	/**
	 * 内部订单ID
	 */
	private Long orderId;

	/**
	 * 业务类型
	 */
	private Integer orderBizType;

	/**
	 * 业务订单号
	 */
	private String viewOrderId;

	/**
	 * 运单id
	 *
	 * @since 1.4
	 */
	private Long deliveryId;

	public RiderAssignTimeOutCheckMessage(DeliveryOrder deliveryOrder) {
		this.tenantId = deliveryOrder.getTenantId();
		this.shopId = deliveryOrder.getStoreId();
		this.orderId = deliveryOrder.getOrderKey().getOrderId();
		this.orderBizType = deliveryOrder.getOrderBizType();
		this.viewOrderId = deliveryOrder.getChannelOrderId();
		this.deliveryId = deliveryOrder.getId();
	}
}
