package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.reco.pickselect.common.mq.consumer.ConsumerConfig;
import com.meituan.reco.pickselect.common.mq.consumer.ConsumerRetryException;
import com.meituan.reco.pickselect.common.mq.consumer.RetryConfig;
import com.meituan.reco.pickselect.common.mq.producer.ProducerConfig;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.DyDeliveryCallbackMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.QueryDeliveryInfoApplicationService;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryOrderSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.DeliveryCallbackThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request.FarmDeliveryChangeNotifyReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.response.FarmDeliveryChangeNotifyResp;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.DyOrderChannelDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.CommonLogicException;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaTopicEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

import static com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.TimeUtil.toMilliSeconds;


/**
 * 抖音平台配送状态变更消费者
 * <AUTHOR>
 */
@Slf4j
@Component
public class DyDeliveryChangeMessageListener extends AbstractMqListener {

	/**
	 * 抖音平台配送承运商信息，由于抖音平台配送状态回传没有给牵牛花承运商信息，这里写死
	 */
	private static final String DOUYIN_LOGISTIC_MARK = "douyin";

	/**
	 * 抖音平台配送渠道运单ID，由于抖音平台配送状态回传没有给牵牛花渠道运单ID，这里写死
	*/
	private static final String DOUYIN_LOGISTIC_NO = "0";

	@Resource
	private QueryDeliveryInfoApplicationService queryDeliveryInfoApplicationService;

	@Resource
	private DeliveryCallbackThriftService deliveryCallbackThriftService;

	@Resource
	DeliveryChannelApplicationService deliveryChannelApplicationService;

	@Resource
	private OrderSystemClient orderSystemClient;

	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;

	@Override
	protected ConsumerConfig config() {
		return MafkaConsumerEnum.DY_DELIVERY_ORDER_CHANGE_CONSUMER;
	}

	@Override
	protected RetryConfig retryConfig() {
		return new RetryConfig() {
			@Override
			public ProducerConfig producerConfig() {
				return MafkaTopicEnum.DY_DELIVERY_ORDER_CHANGE_TOPIC;
			}

			@Override
			public int maxRetry() {
				return MccConfigUtils.getDyPlatformDeliveryMqMaxRetryTimes();
			}

			@Override
			public int delayInSeconds() {
				return MccConfigUtils.getDyPlatformDeliveryMqDelaySeconds();
			}
		};
	}

	@Override
	protected void consume(MafkaMessage mafkaMessage) throws ConsumerRetryException {
		try {
			if (!MccConfigUtils.getDyDeliveryChangeMessageListenerSwitch()) {
				log.info("抖音配送状态变更消息监听开关关闭，不处理消息");
				return;
			}

			log.info("开始消费抖音平台配送状态变更事件消息: {}", mafkaMessage);
			DyDeliveryCallbackMsg msg = translateMessage(mafkaMessage);
			if (Objects.isNull(msg)) {
				log.error("DyDeliveryChangeMessageListener, msg is null");
				throw new CommonLogicException("DyDeliveryChangeMessageListener, msg is null");
			}

			String channelOrderId = msg.getShopOrderId();
			if (StringUtils.isEmpty(channelOrderId)) {
				log.error("DyDeliveryChangeMessageListener, channelOrderId is empty");
				throw new CommonLogicException("DyDeliveryChangeMessageListener, channelOrderId is empty");
			}

			Result<OrderInfo> orderInfoResult = orderSystemClient.queryByViewOrderId(DynamicOrderBizType.DOU_YIN.getValue(), channelOrderId,false);
			if (orderInfoResult.isFail()) {
				// 由于抖音渠道推消息会有概率乱序，配送状态回传会在生单消息之前推过来，此时查询订单查不到，这里临时方案是重试再查订单
				log.error("DyDeliveryChangeMessageListener, orderInfoResult is fail, orderInfoResult is {}", orderInfoResult);
				throw new CommonRuntimeException("DyDeliveryChangeMessageListener, orderInfoResult is fail");
			}

			DeliveryOrder deliveryOrder;
			OrderInfo orderInfo = orderInfoResult.getInfo();
			Long orderId = orderInfo.getOrderKey().getOrderId();
			Optional<DeliveryOrder> deliveryOrderOptional = queryDeliveryInfoApplicationService.queryDeliveryOrderByOrderId(orderId,orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
			if (deliveryOrderOptional.isPresent()) {
				deliveryOrder = deliveryOrderOptional.get();
				// 只有当抖音渠道订单发的是牵牛花平台配送才会走后续逻辑
				if (!isOrderChannelDeliveryPlatFormDeliveryOrder(deliveryOrder)) {
					log.info("DyDeliveryChangeMessageListener, deliveryOrder is not platform delivery");
					throw new CommonLogicException("DyDeliveryChangeMessageListener, deliveryOrder is not platform delivery");
				}
			} else {
				// 非牵牛花管理配送的运单（商家端发的），DB里初始化一个运单，用于记录后续运单配送状态变更
				deliveryOrder = getInitActiveDeliveryOrder(orderInfo, msg);
				deliveryOrderRepository.save(deliveryOrder);
			}

			FarmDeliveryChangeNotifyReq req = buildFarmDeliveryChangeNotifyReq(msg, deliveryOrder);
			log.info("DyDeliveryChangeMessageListener invoke farmNotifyDeliveryChange request = {}", req);
			FarmDeliveryChangeNotifyResp farmDeliveryChangeNotifyResp = deliveryCallbackThriftService.farmNotifyDeliveryChange(req);
			log.info("DyDeliveryChangeMessageListener invoke farmNotifyDeliveryChange response = {}", farmDeliveryChangeNotifyResp);
		} catch (CommonLogicException e) {
			log.error("消费抖音运单变更消息失败, message={}", mafkaMessage, e);
		} catch (Exception e) {
			log.error("消费抖音运单变更消息失败，将会进行重试消费,  message={}", mafkaMessage, e);
			throw new ConsumerRetryException(e);
		}
	}

	private DyDeliveryCallbackMsg translateMessage(MafkaMessage mafkaMessage) {
		try {
			return Optional.ofNullable(mafkaMessage)
					.map(MafkaMessage::getBody)
					.map(Object::toString)
					.map(it -> JsonUtil.fromJson(it, DyDeliveryCallbackMsg.class))
					.orElse(null);
		} catch (Exception e) {
			log.error("DyDeliveryChangeMessageListener translateMessage error", e);
			Cat.logEvent("DY_DELIVERY_ORDER_CHANGE_CONSUMER_FAILED", "MESSAGE_WRONG");
			return null;
		}
	}

	private FarmDeliveryChangeNotifyReq buildFarmDeliveryChangeNotifyReq(DyDeliveryCallbackMsg msg, DeliveryOrder deliveryOrder) {
		FarmDeliveryChangeNotifyReq req = new FarmDeliveryChangeNotifyReq();

		req.setOriginId(String.valueOf(deliveryOrder.getOrderId()));
		req.setPlatformCode(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode());
		req.setOrderChannelType(DynamicChannelType.DOU_YIN.getChannelId());
		req.setLogistic(DOUYIN_LOGISTIC_MARK);
		req.setLogisticNo(DOUYIN_LOGISTIC_NO);

		if (Objects.nonNull(msg.getStatus())) {
			req.setStatus(msg.getStatus().intValue());
		}
		if (Objects.nonNull(msg.getOperateTime())) {
			Long milliSeconds = toMilliSeconds(msg.getOperateTime());
			if (Objects.nonNull(milliSeconds)) {
				req.setTimestamp(milliSeconds/1000);
			}
		}
		if (StringUtils.isNotEmpty(msg.getRiderName())) {
			req.setRiderName(msg.getRiderName());
		}
		if (StringUtils.isNotEmpty(msg.getRiderPhone())) {
			req.setRiderPhone(msg.getRiderPhone());
		}
		if (Objects.nonNull(msg.getCancelCode())) {
			req.setCancelReasonCode(msg.getCancelCode().intValue());
		}

		return req;
	}

	private boolean isOrderChannelDeliveryPlatFormDeliveryOrder(DeliveryOrder deliveryOrder) {
		DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
		if (Objects.isNull(deliveryChannelDto) || Objects.isNull(deliveryChannelDto.getDeliveryPlatFormCode())) {
			return false;
		}

		return DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode() == deliveryChannelDto.getDeliveryPlatFormCode();
	}

	private DeliveryOrder getInitActiveDeliveryOrder(OrderInfo orderInfo, DyDeliveryCallbackMsg msg) {
		Integer deliveryChannel = MccConfigUtils.getDyPlatformDeliveryChannelCode();
		DeliveryOrder deliveryOrder = new DeliveryOrder(orderInfo, deliveryChannel);
		deliveryOrder.activate();
		deliveryOrder.setDeliveryCount(1);
		deliveryOrder.setIsQhnManagement(DeliveryOrderSourceEnum.NOT_QNH_MANAGEMENT.getCode());

		Optional<LocalDateTime> operateTimeOptional = TimeUtil.toLocalDateTime(msg.getOperateTime());
		if (Objects.nonNull(msg.getStatus()) && operateTimeOptional.isPresent()) {
			Optional<DeliveryStatusEnum> deliveryStatusEnumOptional = DyOrderChannelDeliveryStatusEnum.mapToDeliveryStatus(msg.getStatus().intValue());
			deliveryStatusEnumOptional.ifPresent(deliveryStatusEnum -> deliveryOrder.onStatusChangeWithOutLog(deliveryStatusEnum, operateTimeOptional.get()));
		}

		if (StringUtils.isNotEmpty(msg.getRiderName()) && StringUtils.isNotEmpty(msg.getRiderPhone())) {
			deliveryOrder.setRiderInfo(new Rider(msg.getRiderName(), msg.getRiderPhone(), StringUtils.EMPTY));
		}

		return deliveryOrder;
	}

}
