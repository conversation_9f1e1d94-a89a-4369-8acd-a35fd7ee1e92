package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryOperationApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryProcessApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryLaunchCommandMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * <AUTHOR>
 * @date 2020/3/11
 * desc: 发起配送消息Listener
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class DeliveryLaunchCommandMessageListener extends AbstractDeadLetterConsumer {

	@Resource
	private DeliveryProcessApplicationService deliveryProcessApplicationService;
	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;
	@Resource
	private DeliveryOperationApplicationService deliveryOperationApplicationService;

	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.DELIVERY_LAUNCH_COMMAND;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
		log.info("开始消费发起配送消息：{}", mafkaMessage);
		DeliveryLaunchCommandMessage message = translateMessage(mafkaMessage);
		if (message == null) {
			return CONSUME_SUCCESS;
		}

		try {
			Optional<DeliveryPoi> opDeliveryPoi = Optional.empty();
			DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
					message.getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
			opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(message.getTenantId(), message.getShopId(),
					orderBizType.getChannelId());
			if (!opDeliveryPoi.isPresent()) {
				log.warn("门店[{}]未开通自配送，将放弃发起配送", message.getShopId());
				return CONSUME_SUCCESS;
			}
			DeliveryPoi deliveryPoi = opDeliveryPoi.get();

			PlatformSourceEnum platformSourceEnum = PlatformSourceEnum.OMS;
			if(message.getPlatformSource()!=null){
				platformSourceEnum = PlatformSourceEnum.platformCodeToEnum(message.getPlatformSource());
			}

			Optional<Failure> launchFailure;
			switch (deliveryPoi.getDeliveryPlatform()) {
				case SELF_BUILT_DELIVERY_PLATFORM:
					SelfBuiltDeliveryPoi selfBuiltDeliveryPoi = (SelfBuiltDeliveryPoi) deliveryPoi;
					if (!selfBuiltDeliveryPoi.canLaunchDelivery()) {
						log.warn("门店[{}]在自建平台没有可用的配送渠道，将放弃发起配送", selfBuiltDeliveryPoi.getStoreId());
						return CONSUME_SUCCESS;
					}

					launchFailure = deliveryProcessApplicationService.launchDelivery(
							new OrderKey(message.getTenantId(), message.getShopId(), message.getOrderId()),
							selfBuiltDeliveryPoi,
							!isLastRetry(mafkaMessage),
							false,
							false
					);
					break;

				case MERCHANT_SELF_DELIVERY:
				case AGGREGATION_DELIVERY_PLATFORM:
				case MALT_FARM_DELIVERY_PLATFORM:
				case DAP_DELIVERY_PLATFORM:
					launchFailure = deliveryOperationApplicationService.launchDelivery(
							deliveryPoi, new OrderKey(message.getTenantId(), message.getShopId(), message.getOrderId()), !isLastRetry(mafkaMessage),platformSourceEnum,message.getFulfillOrderId()
					);
					break;

				default:
					log.warn("不支持的配送平台[{}]，将放弃发起配送", deliveryPoi.getDeliveryPlatform().getCode());
					return CONSUME_SUCCESS;
			}

			if (launchFailure.isPresent() && launchFailure.get().isNeedRetry()) {
				log.info("发起配送消息处理失败,需要进行重试, failure={}", launchFailure.get());
				return CONSUME_FAILURE;
			}

			return CONSUME_SUCCESS;

		} catch (Exception e) {
			log.warn("发起配送消息处理异常", e);
			return CONSUME_FAILURE;
		}
	}

	private DeliveryLaunchCommandMessage translateMessage(MafkaMessage mafkaMessage) {
		try {
			DeliveryLaunchCommandMessage message = translateMessage(mafkaMessage, DeliveryLaunchCommandMessage.class);
			Preconditions.checkNotNull(message, "empty mafkaMessage");
			Preconditions.checkNotNull(message.getTenantId(), "发起配送消息tenantId为空");
			Preconditions.checkNotNull(message.getShopId(), "发起配送消息shopId为空");
			Preconditions.checkNotNull(message.getOrderId(), "发起配送消息orderId为空");
			return message;

		} catch (Exception e) {
			log.error("解析发起配送消息失败:{}", mafkaMessage, e);
			Cat.logEvent("DELIVERY_LAUNCH_FAILED", "DELIVERY_LAUNCH_COMMAND_MESSAGE_WRONG");
			return null;
		}
	}
}
