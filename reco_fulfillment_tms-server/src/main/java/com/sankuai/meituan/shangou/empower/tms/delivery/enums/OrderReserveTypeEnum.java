package com.sankuai.meituan.shangou.empower.tms.delivery.enums;

/**
 * 订单的预约类型枚举.
 *
 * <AUTHOR>
 * @since 2021/12/14 10:40
 */
public enum OrderReserveTypeEnum {

    /**
     * 订单的预约类型未知
     */
    INVALID_TYPE(-1, "未知"),

    /**
     * 订单为实时单
     */
    IMMEDIATE_ORDER(0, "实时单"),

    /**
     * 订单为预约单
     */
    RESERVED_ORDER(1, "预约单"),
    ;

    /**
     * 枚举编码
     */
    private final int code;

    /**
     * 枚举描述
     */
    private final String msg;

    OrderReserveTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static OrderReserveTypeEnum enumOf(int value) {
        for (OrderReserveTypeEnum each : values()) {
            if (each.getCode() == value) {
                return each;
            }
        }
        return null;
    }

    /**
     * 是否为预约单转换为改枚举.
     *
     * @param isReserved 是预约单
     * @return 预约类型枚举
     */
    public static OrderReserveTypeEnum convert(Boolean isReserved) {
        if (isReserved == null) {
            return INVALID_TYPE;
        }
        if (isReserved) {
            return RESERVED_ORDER;
        } else {
            return IMMEDIATE_ORDER;
        }
    }

    /**
     * 将预约类型枚举转换为：是否为预约单
     *
     * @param value
     * @return
     */
    public static Boolean convert(int value) {
        OrderReserveTypeEnum orderReserveType = enumOf(value);
        if (orderReserveType == null) {
            return null;
        }
        switch (orderReserveType) {
            case IMMEDIATE_ORDER:
                return false;
            case RESERVED_ORDER:
                return true;
            default:
                return null;
        }
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
