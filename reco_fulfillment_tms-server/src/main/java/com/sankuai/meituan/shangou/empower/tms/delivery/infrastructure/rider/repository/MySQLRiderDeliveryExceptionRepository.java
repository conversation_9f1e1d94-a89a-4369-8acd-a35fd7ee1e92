package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.rider.repository;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.linz.boot.util.TimeUtils;
import com.sankuai.meituan.common.util.CollectionUtil;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.PageQueryDeliveryExceptionRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.RiderDeliveryExceptionDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.RiderDeliveryExceptionDOExMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryExceptionDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository.MySQLOperateFailedException;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class MySQLRiderDeliveryExceptionRepository implements RiderDeliveryExceptionRepository {

    @Resource
    private RiderDeliveryExceptionDOMapper riderDeliveryExceptionDOMapper;

    @Resource
    private RiderDeliveryExceptionDOExMapper riderDeliveryExceptionDOExMapper;

    @Override
    public void save(RiderDeliveryException riderDeliveryException) {
        if (riderDeliveryException == null) {
            return;
        }

        int rows = riderDeliveryExceptionDOMapper.insertSelective(transform2RiderDeliveryExceptionDO(riderDeliveryException));
        if (rows != 1) {
            throw new MySQLOperateFailedException("骑手配送异常保存失败");
        }
    }

    @Override
    public PageResult<RiderDeliveryException> pageQueryRiderDeliveryException(RiderDeliveryExceptionPageQueryConditions conditions) {
        long total = countDeliveryException(conditions);

        RiderDeliveryExceptionDOExample example = new RiderDeliveryExceptionDOExample();
        RiderDeliveryExceptionDOExample.Criteria criteria = example.createCriteria();

        if (conditions.getTenantId() == null || CollectionUtils.isEmpty(conditions.getStoreIds())) {
            return new PageResult<>(Collections.emptyList(), conditions.getPageRequest(), 0);
        }
        criteria.andTenantIdEqualTo(conditions.getTenantId())
                .andStoreIdIn(conditions.getStoreIds());

        if (CollectionUtils.isNotEmpty(conditions.getOrderBizTypeList())) {
            criteria.andOrderBizTypeIn(conditions.getOrderBizTypeList());
        }

        if (conditions.getChannelOrderId() != null) {
            criteria.andChannelOrderIdEqualTo(conditions.getChannelOrderId().toString());
        }

        if (conditions.getRiderAccountId() != null) {
            criteria.andRiderAccountIdEqualTo(conditions.getRiderAccountId());
        }

        if (conditions.getReportTimeStartTimeStamp() != null && conditions.getReportTimeEndTimeStamp() != null) {
            criteria.andCreateTimeBetween(TimeUtil.fromMilliSeconds(conditions.getReportTimeStartTimeStamp()),
                    TimeUtil.fromMilliSeconds(conditions.getReportTimeEndTimeStamp()));
        }

        if (conditions.getPayTimeStartTimeStamp() != null && conditions.getPayTimeEndTimeStamp() != null) {
            criteria.andPayTimeBetween(TimeUtil.fromMilliSeconds(conditions.getPayTimeStartTimeStamp()),
                    TimeUtil.fromMilliSeconds(conditions.getPayTimeEndTimeStamp()));
        }

        if (CollectionUtils.isNotEmpty(conditions.getExceptionTypeList())) {
            criteria.andReportExceptionTypeIn(conditions.getExceptionTypeList());
        }

        example.setOrderByClause("pay_time DESC");
        example.setLimit(conditions.getPageRequest().getPageSize());
        example.setOffset((conditions.getPageRequest().getPage() - 1) * conditions.getPageRequest().getPageSize());

        //先查满足条件的运单id
        List<Long> deliveryOrderIdList = riderDeliveryExceptionDOExMapper.selectDeliveryOrderIdByExampleDistinct(example);

        if (CollectionUtils.isEmpty(deliveryOrderIdList)) {
            return new PageResult<>(Collections.emptyList(), conditions.getPageRequest(), 0);
        }

        //根据运单id筛选出符合条件的异常记录
        RiderDeliveryExceptionDOExample example2 = new RiderDeliveryExceptionDOExample();
        example2.createCriteria().andTenantIdEqualTo(conditions.getTenantId())
                .andStoreIdIn(conditions.getStoreIds())
                .andDeliveryOrderIdIn(deliveryOrderIdList);

        if (CollectionUtils.isNotEmpty(conditions.getExceptionTypeList())) {
            example2.getOredCriteria().get(0).andReportExceptionTypeIn(conditions.getExceptionTypeList());
        }

        if (conditions.getReportTimeStartTimeStamp() != null && conditions.getReportTimeEndTimeStamp() != null) {
            example2.getOredCriteria().get(0).andCreateTimeBetween(TimeUtil.fromMilliSeconds(conditions.getReportTimeStartTimeStamp()),
                    TimeUtil.fromMilliSeconds(conditions.getReportTimeEndTimeStamp()));
        }

        //再查满足条件的渠道订单关联的配送异常
        List<RiderDeliveryException> riderDeliveryExceptions = riderDeliveryExceptionDOMapper.selectByExample(example2)
                .stream().map(this::transform2RiderDeliveryException).collect(Collectors.toList());

        return new PageResult<>(riderDeliveryExceptions, conditions.getPageRequest(), total);
    }


    @Override
    public long countDeliveryException(RiderDeliveryExceptionPageQueryConditions conditions) {
        RiderDeliveryExceptionDOExample example = new RiderDeliveryExceptionDOExample();
        RiderDeliveryExceptionDOExample.Criteria criteria = example.createCriteria();

        if (conditions.getTenantId() == null || CollectionUtils.isEmpty(conditions.getStoreIds())) {
            return 0;
        }
        criteria.andTenantIdEqualTo(conditions.getTenantId())
                .andStoreIdIn(conditions.getStoreIds());

        if (CollectionUtils.isNotEmpty(conditions.getOrderBizTypeList())) {
            criteria.andOrderBizTypeIn(conditions.getOrderBizTypeList());
        }

        if (conditions.getChannelOrderId() != null) {
            criteria.andChannelOrderIdEqualTo(conditions.getChannelOrderId().toString());
        }

        if (conditions.getRiderAccountId() != null) {
            criteria.andRiderAccountIdEqualTo(conditions.getRiderAccountId());
        }

        if (conditions.getReportTimeStartTimeStamp() != null && conditions.getReportTimeEndTimeStamp() != null) {
            criteria.andCreateTimeBetween(TimeUtil.fromMilliSeconds(conditions.getReportTimeStartTimeStamp()),
                    TimeUtil.fromMilliSeconds(conditions.getReportTimeEndTimeStamp()));
        }

        if (conditions.getPayTimeStartTimeStamp() != null && conditions.getPayTimeEndTimeStamp() != null) {
            criteria.andPayTimeBetween(TimeUtil.fromMilliSeconds(conditions.getPayTimeStartTimeStamp()),
                    TimeUtil.fromMilliSeconds(conditions.getPayTimeEndTimeStamp()));
        }

        if (CollectionUtils.isNotEmpty(conditions.getExceptionTypeList())) {
            criteria.andReportExceptionTypeIn(conditions.getExceptionTypeList());
        }

        return riderDeliveryExceptionDOExMapper.selectCountByDistinctDeliveryOrderId(example);
    }

    @Override
    public List<RiderDeliveryException> queryDeliveryExceptionByChannelOrderIds(Long tenantId, Long storeId,
                                                                                List<String> channelOrderId) {
        RiderDeliveryExceptionDOExample example = new RiderDeliveryExceptionDOExample();
        example.createCriteria().andTenantIdEqualTo(tenantId)
                .andStoreIdEqualTo(storeId)
                .andChannelOrderIdIn(channelOrderId);

        List<RiderDeliveryExceptionDO> riderDeliveryExceptionDOS = riderDeliveryExceptionDOMapper.selectByExample(example);
        return  riderDeliveryExceptionDOS.stream()
                .map(this::transform2RiderDeliveryException)
                .collect(Collectors.toList());
    }

    @Override
    public List<RiderDeliveryException> queryDeliveryExceptionByChannelOrderId(Long tenantId, Long storeId,
                                                                                String channelOrderId, Integer orderBizType) {
        RiderDeliveryExceptionDOExample example = new RiderDeliveryExceptionDOExample();
        example.createCriteria().andTenantIdEqualTo(tenantId)
                .andStoreIdEqualTo(storeId)
                .andChannelOrderIdEqualTo(channelOrderId)
                .andOrderBizTypeEqualTo(orderBizType);

        List<RiderDeliveryExceptionDO> riderDeliveryExceptionDOS = riderDeliveryExceptionDOMapper.selectByExample(example);
        return  riderDeliveryExceptionDOS.stream()
                .map(this::transform2RiderDeliveryException)
                .collect(Collectors.toList());
    }

    private RiderDeliveryExceptionDO transform2RiderDeliveryExceptionDO(RiderDeliveryException riderDeliveryException) {
        RiderDeliveryExceptionDO deliveryExceptionDO = new RiderDeliveryExceptionDO();
        deliveryExceptionDO.setChannelOrderId(riderDeliveryException.getChannelOrderId().toString());
        deliveryExceptionDO.setDeliveryOrderId(riderDeliveryException.getDeliveryOrderId());
        deliveryExceptionDO.setDaySeq(riderDeliveryException.getDaySeq());
        deliveryExceptionDO.setPayTime(riderDeliveryException.getPayTime());
        deliveryExceptionDO.setTenantId(riderDeliveryException.getTenantId());
        deliveryExceptionDO.setStoreId(riderDeliveryException.getStoreId());
        deliveryExceptionDO.setRiderName(riderDeliveryException.getRiderAccountName());
        deliveryExceptionDO.setReportExceptionType(riderDeliveryException.getExceptionType());
        deliveryExceptionDO.setReportExceptionSubType(riderDeliveryException.getExceptionSubType());
        deliveryExceptionDO.setOrderBizType(riderDeliveryException.getOrderBizType());
        deliveryExceptionDO.setReportExceptionDescription(JsonUtil.toJson(riderDeliveryException.getExceptionDescription()));
        deliveryExceptionDO.setRiderAccountId(riderDeliveryException.getRiderAccountId());

        return deliveryExceptionDO;
    }

    private RiderDeliveryException transform2RiderDeliveryException(RiderDeliveryExceptionDO riderDeliveryExceptionDO) {
        RiderDeliveryException deliveryException = new RiderDeliveryException();
        deliveryException.setChannelOrderId(Long.valueOf(riderDeliveryExceptionDO.getChannelOrderId()));
        deliveryException.setDeliveryOrderId(riderDeliveryExceptionDO.getDeliveryOrderId());
        deliveryException.setDaySeq(riderDeliveryExceptionDO.getDaySeq());
        deliveryException.setPayTime(riderDeliveryExceptionDO.getPayTime());
        deliveryException.setTenantId(riderDeliveryExceptionDO.getTenantId());
        deliveryException.setStoreId(riderDeliveryExceptionDO.getStoreId());
        deliveryException.setRiderAccountName(riderDeliveryExceptionDO.getRiderName());
        deliveryException.setExceptionType(riderDeliveryExceptionDO.getReportExceptionType());
        deliveryException.setExceptionSubType(riderDeliveryExceptionDO.getReportExceptionSubType());
        deliveryException.setOrderBizType(riderDeliveryExceptionDO.getOrderBizType());
        deliveryException.setRiderAccountId(riderDeliveryExceptionDO.getRiderAccountId());
        deliveryException.setExceptionDescription(JsonUtil.fromJson(riderDeliveryExceptionDO.getReportExceptionDescription(),
                new TypeReference<RiderDeliveryException.ExceptionDescription>() {}));
        deliveryException.setCreateTime(riderDeliveryExceptionDO.getCreateTime());

        return deliveryException;
    }
}
