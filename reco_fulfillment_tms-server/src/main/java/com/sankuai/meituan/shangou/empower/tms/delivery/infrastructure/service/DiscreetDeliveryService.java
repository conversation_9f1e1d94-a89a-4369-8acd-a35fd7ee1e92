package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.service;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.DiscreetDeliveryRemoteService;

import lombok.extern.slf4j.Slf4j;

/**
 * 隐私发货服务收口
 *
 * <AUTHOR>
 * @since 2024/9/10
 */
@Slf4j
@Service
public class DiscreetDeliveryService {
    @Resource
    private DiscreetDeliveryRemoteService discreetDeliveryRemoteService;

    /**
     * 替换货号，目前只有医药调用
     *
     * @param orderKey
     * @param orderInfo
     */
    public void replaceMedicineGoodsCode2SkuName(OrderKey orderKey, OrderInfo orderInfo, OrderSystemClient orderSystemClient, Long warehouseId) {
        Boolean discreetDelivery = discreetDeliveryRemoteService.queryDiscreetDelivery(orderInfo.getOrderKey().getTenantId(),
                warehouseId);
        // 如果隐私发货为null，医药默认替换货号
        // 如果隐私发货为true，则替换货号
        if (discreetDelivery == null || Boolean.TRUE.equals(discreetDelivery)) {
            orderSystemClient.replaceGoodsCode2SkuName(orderKey, orderInfo);
        }
        else {
            log.info("replaceMedicineGoodsCode2SkuName 隐私发货为false或null，不替换货号, tenantId:{}, warehouseId:{}",
                    orderInfo.getOrderKey().getTenantId(),
                    warehouseId);
        }

    }

    /**
     * 非医药业务线判断是否替换货号
     *
     * @param orderKey
     * @param orderInfo
     */
    public void replaceNotMedicineUnmannedGoodsCode2SkuName(OrderKey orderKey, OrderInfo orderInfo, OrderSystemClient orderSystemClient,
                                                            Long warehouseId) {

        Boolean discreetDelivery = discreetDeliveryRemoteService.queryDiscreetDelivery(orderInfo.getOrderKey().getTenantId(),
                warehouseId);
        // 如果隐私发货为null，非医药默认为false
        // 如果隐私发货为true，则替换货号
        if (Boolean.TRUE.equals(discreetDelivery)) {
            log.info("replaceNotMedicineUnmannedGoodsCode2SkuName 隐私发货为true，替换货号, tenantId:{}, warehouseId:{}",
                    orderInfo.getOrderKey().getTenantId(),
                    warehouseId);
            orderSystemClient.replaceGoodsCode2SkuName(orderKey, orderInfo);
        }
    }
}
