package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class DeliveryTransSelfMsg {

    /**
     * 租户 ID
     */
    private Long tenantId;

    /**
     * 门店 ID
     */
    private Long storeId;

    /**
     * 赋能订单 ID
     */
    private Long orderId;

    //歪马转自营配送需要指定骑手 start
    /**
     * 骑手账号id
     */
    private Long riderAccountId;

    /**
     * 骑手姓名
     */
    private String riderName;

    /**
     * 骑手电话
     */
    private String riderPhone;

    //歪马转自营配送需要指定骑手 end

    /**
     * 转单前的配送方式
     */
    private Integer deliveryPlatformCode;

}
