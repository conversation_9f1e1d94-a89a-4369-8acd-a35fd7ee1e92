package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryIsShowItemNumberEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStrategyEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.BookingOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.ShopDeliveryConfigDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.StoreConfigDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.DeliveryConfigDOExMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.ShopDeliveryConfigDOExMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.StoreConfigDOExMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.StoreConfigOpLogDOExMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryConfigDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.ShopDeliveryConfigDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreConfigDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreConfigOpLogDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.wrapper.TenantChannelThriftServiceWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.StoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.StoreConfigOpLog;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.aggr.AggrDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.dap.DapDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.maltfarm.MaltFarmDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.merchant.MerchantSelfDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.orderchannel.OrderChannelDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ChannelStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ChannelTenantConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/28
 */
@Slf4j
@Repository
public class MySQLDeliveryPoiRepository implements DeliveryPoiRepository {

	private static final int ENABLED = 1;
	private static final int NON_DELETED = 0;

	@Resource
	private StoreConfigDOMapper storeConfigDOMapper;

	@Resource
	private DeliveryConfigDOExMapper deliveryConfigDOExMapper;

	@Resource
	private ShopDeliveryConfigDOMapper shopDeliveryConfigDOMapper;

	@Resource
	private ShopDeliveryConfigDOExMapper shopDeliveryConfigDOExMapper;

	@Resource(name = "tenantRemoteServiceForDelivery")
	private TenantRemoteService tenantRemoteService;
	@Resource
	private TenantSystemClient tenantSystemClient;

	@Resource
	private StoreConfigDOExMapper storeConfigDOExMapper;

	@Resource
	private StoreConfigOpLogDOExMapper storeConfigOpLogDOExMapper;

	@Resource
	private StoreConfigOpLogConverter storeConfigOpLogConverter;

	@Resource
	private StoreConfigConverter storeConfigConverter;


	@Resource
	private TenantChannelThriftServiceWrapper tenantChannelThriftServiceWrapper;

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<DeliveryPoi> queryDeliveryPoi(Long tenantId, Long storeId) {
		StoreConfigDOExample example = new StoreConfigDOExample();
		example.createCriteria()
				.andTenantIdEqualTo(tenantId)
				.andStoreIdEqualTo(storeId)
				.andChannelTypeEqualTo(DynamicChannelType.MEITUAN.getChannelId())
				.andEnabledEqualTo(ENABLED);
		List<StoreConfigDO> records = storeConfigDOExMapper.selectByExampleSlave(example);

		if (CollectionUtils.isNotEmpty(records)) {
			return translate(records.get(0), true);

		} else {
			//默认兜底自建平台配送门店
			return tryMakeUpMissingSelfBuiltDeliveryPoi(tenantId, storeId,DynamicChannelType.MEITUAN.getChannelId());
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<DeliveryPoi> queryDeliveryPoiWithChannelType(Long tenantId, Long storeId, Integer channelType) {
		StoreConfigDOExample example = new StoreConfigDOExample();
		example.createCriteria()
				.andTenantIdEqualTo(tenantId)
				.andStoreIdEqualTo(storeId)
				.andChannelTypeEqualTo(channelType)
				.andEnabledEqualTo(ENABLED);
		List<StoreConfigDO> records = storeConfigDOExMapper.selectByExampleSlave(example);

		if (CollectionUtils.isNotEmpty(records)) {
			return translate(records.get(0), true);

		} else {
			//默认兜底自建平台配送门店
			if(channelType.equals(DynamicChannelType.MEITUAN.getChannelId())){
				return tryMakeUpMissingSelfBuiltDeliveryPoi(tenantId, storeId,channelType);
			}else {
				Optional<DeliveryPoi> mtDeliveryPoiOpt=queryDeliveryPoiWithChannelType(tenantId, storeId,DynamicChannelType.MEITUAN.getChannelId());
				if(!mtDeliveryPoiOpt.isPresent()){
					return Optional.empty();
				}
				DeliveryPoi deliveryPoi=mtDeliveryPoiToOther(tenantId,storeId,mtDeliveryPoiOpt.get(),channelType);
				saveDeliveryPoi(deliveryPoi);
				return Optional.of(deliveryPoi);
			}

		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<DeliveryPoi> queryDeliveryPoiWithDefault(Long tenantId, Long storeId, Integer channelType) {

		List<DeliveryPoi> deliveryPoiList=queryAllDeliveryPoi(tenantId,storeId);
		if(CollectionUtils.isEmpty(deliveryPoiList)){
			return Optional.empty();
		}
		DeliveryPoi mtDeliveryPoi=null;
		for (DeliveryPoi deliveryPoi : deliveryPoiList){
			if(deliveryPoi.getChannelType().equals(channelType)){
				return Optional.of(deliveryPoi);
			}else if(deliveryPoi.getChannelType().equals(DynamicChannelType.MEITUAN.getChannelId())){
				mtDeliveryPoi=deliveryPoi;
			}
		}
		if(mtDeliveryPoi==null){
			return tryMakeUpMissingSelfBuiltDeliveryPoi(tenantId, storeId,channelType);
		}
		DeliveryPoi deliveryPoi=mtDeliveryPoiToOther(tenantId,storeId,mtDeliveryPoi,channelType);
		saveDeliveryPoi(deliveryPoi);
		return Optional.of(deliveryPoi);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryPoi> queryAllDeliveryPoi(Long tenantId, Long storeId) {
		List<Integer> tenantChannelTypeList = tenantChannelThriftServiceWrapper.getTenantChannelType(String.valueOf(tenantId));
		StoreConfigDOExample example = new StoreConfigDOExample();
		example.createCriteria()
				.andTenantIdEqualTo(tenantId)
				.andStoreIdEqualTo(storeId)
				.andEnabledEqualTo(ENABLED);
		List<StoreConfigDO> records = storeConfigDOExMapper.selectByExampleSlave(example);
		List<DeliveryPoi> deliveryPoiList=new ArrayList<>();
		if(CollectionUtils.isEmpty(records)){
			for (Integer channelType : tenantChannelTypeList){
				if(channelType != DynamicChannelType.MEITUAN.getChannelId()){
					continue;
				}
				Optional<DeliveryPoi> optional=tryMakeUpMissingSelfBuiltDeliveryPoi(tenantId, storeId,channelType);
				optional.ifPresent(deliveryPoiList::add);
			}

		}else {
			List<Integer> channelTypeList=new ArrayList<>();
			DeliveryPoi mtDeliveryPoi=null;
			for (StoreConfigDO configDO : records){
				Optional<DeliveryPoi> optional=translate(configDO, true);
				if(optional.isPresent()){
					DeliveryPoi deliveryPoi=optional.get();
					channelTypeList.add(deliveryPoi.getChannelType());
					deliveryPoiList.add(deliveryPoi);
					if(deliveryPoi.getChannelType().equals(DynamicChannelType.MEITUAN.getChannelId())){
						mtDeliveryPoi=deliveryPoi;
					}
				}
			}
			for (Integer channelType : tenantChannelTypeList){
				if(channelTypeList.contains(channelType)){
					continue;
				}
				if(channelType != DynamicChannelType.MEITUAN.getChannelId()){
					continue;
				}
				Optional<DeliveryPoi> optional;
				if(mtDeliveryPoi==null){
					optional=tryMakeUpMissingSelfBuiltDeliveryPoi(tenantId, storeId,channelType);
				}else {
					DeliveryPoi deliveryPoi=mtDeliveryPoiToOther(tenantId,storeId,mtDeliveryPoi,channelType);
					if(com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.elemStoreConfigSwitch()){
						saveDeliveryPoi(deliveryPoi);
					}
					optional=Optional.of(deliveryPoi);
				}
				optional.ifPresent(deliveryPoiList::add);
			}
		}
		return deliveryPoiList;
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryPoi> queryAggDeliveryPoi(Long tenantId, Long storeId) {
		List<Integer> tenantChannelTypeList = tenantChannelThriftServiceWrapper.getTenantChannelType(String.valueOf(tenantId));
		log.info("查询聚合配送设置, tenantChannelTypeList: {}, tenantId: {}, storeId: {}", tenantChannelTypeList, tenantId, storeId);
		if (CollectionUtils.isEmpty(tenantChannelTypeList)) {
			log.info("查询租户渠道为空");
			return Collections.emptyList();
		}

		List<DeliveryPoi> deliveryPoiList = queryAllDeliveryPoi(tenantId, storeId);
		log.info("查询聚合配送设置，deliveryPoiList channelType: {}, tenantId: {}, storeId: {}",
				Lists.transform(deliveryPoiList, DeliveryPoi::getChannelType), tenantId, storeId);
		if (CollectionUtils.isEmpty(deliveryPoiList)) {
			log.info("查询门店渠道配置为空");
			return Collections.emptyList();
		}

		// 用M端租户渠道过滤store_config表的记录
		List<DeliveryPoi> filteredPoiList = deliveryPoiList.stream()
				.filter(deliveryPoi -> tenantChannelTypeList.contains(deliveryPoi.getChannelType()))
				.collect(Collectors.toList());
		log.info("用M端租户渠道过滤后的渠道配置: {}", filteredPoiList);
		return filteredPoiList;
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<Integer> queryChannelOrder() {
		return com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.queryChannelOrder();
	}

	@Override
	public List<DeliveryPoi> batchQueryDeliveryPoi(Long tenantId, List<Long> storeIdList) {
		List<Integer> tenantChannelTypeList = tenantChannelThriftServiceWrapper.getTenantChannelType(String.valueOf(tenantId));
		if (CollectionUtils.isEmpty(tenantChannelTypeList)) {
			log.warn("查询租户渠道为空");
			return Collections.emptyList();
		}

		StoreConfigDOExample example = new StoreConfigDOExample();
		example.createCriteria()
				.andTenantIdEqualTo(tenantId)
				.andStoreIdIn(storeIdList)
				.andEnabledEqualTo(ENABLED);
		List<StoreConfigDO> records = storeConfigDOExMapper.selectByExampleSlave(example);

		// 只返回当前租户开通的渠道对应的配送配置
		return records.stream().filter(deliveryPoi -> tenantChannelTypeList.contains(deliveryPoi.getChannelType()))
				.map(record -> translate(record, false)).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
	}

	@Override
	public List<DeliveryPoi> batchQueryDeliveryPoiList(Long tenantId, List<Long> storeIdList) {
		StoreConfigDOExample example = new StoreConfigDOExample();
		example.createCriteria()
				.andTenantIdEqualTo(tenantId)
				.andStoreIdIn(storeIdList)
				.andEnabledEqualTo(ENABLED);
		List<StoreConfigDO> records = storeConfigDOExMapper.selectByExampleSlave(example);

		return records.stream().map(record -> translate(record, false)).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
	}

	private DeliveryPoi mtDeliveryPoiToOther(Long tenantId, Long storeId, DeliveryPoi mtDeliveryPoi, Integer channelType){

		if(mtDeliveryPoi==null || mtDeliveryPoi.getDeliveryPlatform()!=DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM){
			DeliveryLaunchPoint deliveryLaunchPoint=new DeliveryLaunchPoint();
			if(channelType == DynamicChannelType.JD2HOME.getChannelId()){
				deliveryLaunchPoint.setImmediateOrderDeliveryLaunchPointConfig(new ImmediateOrderDeliveryLaunchPointConfig(ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE,0));
			}
			SelfBuiltDeliveryPoi deliveryPoi = new SelfBuiltDeliveryPoi(
					tenantId,
					storeId,
					null,
					null,
					deliveryLaunchPoint,
					new HashMap<>(),
					new HashMap<>(),
					channelType
			);
			return deliveryPoi;
		}
		MaltFarmDeliveryPoi maltFarmDeliveryPoi=new MaltFarmDeliveryPoi(mtDeliveryPoi);
		maltFarmDeliveryPoi.setStoreAddress(((MaltFarmDeliveryPoi)mtDeliveryPoi).getStoreAddress());
		maltFarmDeliveryPoi.setChannelType(channelType);
		maltFarmDeliveryPoi.setLastDeliveryPlatform(mtDeliveryPoi.getLastDeliveryPlatform());
		if(maltFarmDeliveryPoi.getDeliveryLaunchPoint() !=null && channelType == DynamicChannelType.JD2HOME.getChannelId()){
			maltFarmDeliveryPoi.getDeliveryLaunchPoint().setImmediateOrderDeliveryLaunchPointConfig(new ImmediateOrderDeliveryLaunchPointConfig(ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE,0));
		}else {
			maltFarmDeliveryPoi.setDeliveryLaunchPoint(new DeliveryLaunchPoint());
		}
		return maltFarmDeliveryPoi;
	}

	@Override
	public Optional<SelfBuiltDeliveryPoi> querySelfBuiltDeliveryPoi(ThirdDeliveryChannelEnum deliveryChannel, String channelStoreCode) {
		ShopDeliveryConfigDOExample example = new ShopDeliveryConfigDOExample();
		example.createCriteria()
				.andDeliveryChannelIdEqualTo(deliveryChannel.getCode())
				.andDeliveryChannelPoiCodeEqualTo(channelStoreCode)
				.andEnabledEqualTo(ENABLED)
				.andIsDelEqualTo(0);

		List<ShopDeliveryConfigDO> records = shopDeliveryConfigDOMapper.selectByExample(example);

		if (CollectionUtils.isNotEmpty(records)) {
			return queryDeliveryPoi(records.get(0).getTenantId(), records.get(0).getShopId())
					.filter(it -> it.getDeliveryPlatform() == DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM)
					.filter(it -> it.getChannelType() == DynamicChannelType.MEITUAN.getChannelId())
					.map(it -> (SelfBuiltDeliveryPoi) it);
		}
		return Optional.empty();
	}

	@Override
	public void saveDeliveryPoi(DeliveryPoi deliveryPoi) {
		Preconditions.checkNotNull(deliveryPoi, "deliveryPoi is null");

		StoreConfigDO storeConfigDO = translate(deliveryPoi);
		if (Objects.nonNull(deliveryPoi.getId())) {
			storeConfigDOMapper.updateByPrimaryKeySelective(storeConfigDO);
		} else {
			storeConfigDOExMapper.insertSelective(storeConfigDO);
			deliveryPoi.setId(storeConfigDO.getId());
		}
	}

	@Override
	public void batchSaveDeliveryPoi(List<DeliveryPoi> deliveryPoiList) {
		if (CollectionUtils.isEmpty(deliveryPoiList)) {
			log.info("更新门店配送列表为空");
			return;
		}
		deliveryPoiList.forEach(it-> {
			StoreConfigDO storeConfigDO = translate(it);
			if(Objects.nonNull(it.getId())) {
				storeConfigDOMapper.updateByPrimaryKeySelective(storeConfigDO);
			} else {
				storeConfigDOExMapper.insertSelective(storeConfigDO);
				it.setId(storeConfigDO.getId());
			}
		});
	}

	@Override
	public void batchUpdateDeliveryPoi(List<DeliveryPoi> deliveryPoiList) {
		if (CollectionUtils.isEmpty(deliveryPoiList)) {
			log.info("更新门店配送列表为空");
			return;
		}
		List<StoreConfigDO> storeConfigDOList = deliveryPoiList.stream().map(this::translate).collect(Collectors.toList());
		storeConfigDOExMapper.batchUpdate(storeConfigDOList);
	}

	@Override
	public void batchSaveStoreConfigOpLog(List<StoreConfigOpLog> storeConfigOpLogList) {
		if (CollectionUtils.isEmpty(storeConfigOpLogList)) {
			return;
		}
		List<StoreConfigOpLogDO> storeConfigOpLogDOList =
				storeConfigOpLogList.stream().map(config -> storeConfigOpLogConverter.convert2StoreConfigOpLogDo(config)).collect(Collectors.toList());
		storeConfigOpLogDOExMapper.batchInsert(storeConfigOpLogDOList);
	}

	@Override
	public List<StoreConfigOpLog> queryStoreConfigOpLogByCondition(Long storeConfigId, LocalDateTime startTime, LocalDateTime endTime) {
		if (Objects.isNull(storeConfigId)) {
			return Lists.newArrayList();
		}
		List<StoreConfigOpLogDO> doList = storeConfigOpLogDOExMapper.queryByCondition(storeConfigId, startTime, endTime);
		if (CollectionUtils.isEmpty(doList)) {
			return Lists.newArrayList();
		}
		return doList.stream().map(config -> storeConfigOpLogConverter.convert2StoreConfigOpLog(config)).collect(Collectors.toList());
	}

	@Override
	public int updateStoreConfigByPrimaryKeySelective(StoreConfig storeConfig) {
		if (storeConfig == null || storeConfig.getId() == null) {
			return 0;
		}
		return storeConfigDOMapper.updateByPrimaryKeySelective(storeConfigConverter.convert2StoreConfigOpLogDo(storeConfig));
	}

	private StoreConfigDO translate(DeliveryPoi deliveryPoi) {
		StoreConfigDO record = new StoreConfigDO();
		record.setId(deliveryPoi.getId());
		record.setTenantId(deliveryPoi.getTenantId());
		record.setStoreId(deliveryPoi.getStoreId());
		record.setContactPhone(deliveryPoi.getContactPhone());
		record.setDeliveryLaunchPoint(deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint().getCode());
		record.setDeliveryLaunchDelayMinutes(deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getDelayMinutes());
		record.setBookingOrderDeliveryLaunchPoint(deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getLaunchPoint().getCode());
		record.setBookingOrderDeliveryLaunchMinutes(deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getConfigMinutes());
		record.setEnabled(ENABLED);
		record.setIsAutoLaunch(deliveryPoi.getDeliveryLaunchType().getCode());
		record.setOpenAggrPlatform(deliveryPoi.getDeliveryPlatform().getCode());
		record.setCityCode(deliveryPoi.getCityCode());
		record.setChannelType(deliveryPoi.getChannelType());

		if (deliveryPoi instanceof SelfBuiltDeliveryPoi) {
			SelfBuiltDeliveryPoi selfBuiltDeliveryPoi = (SelfBuiltDeliveryPoi) deliveryPoi;
			record.setAddress(JsonTranslator.toJson(selfBuiltDeliveryPoi.getStoreAddress()));
			record.setDeliveryStrategy(selfBuiltDeliveryPoi.getDeliveryStrategy().getCode());
			record.setDeliveryStrategyConfig(JsonTranslator.toJson(selfBuiltDeliveryPoi.getDeliveryStrategyConfig()));
			record.setOrderPlatformDeliveryConfig(JsonTranslator.toJson(selfBuiltDeliveryPoi.getOrderPlatformDeliveryConfigMap()));
		}

		if (deliveryPoi instanceof AggrDeliveryPoi) {
			record.setDeliveryLaunchRule(((AggrDeliveryPoi) deliveryPoi).getAutoLaunchStrategyId());
		}

		if (deliveryPoi instanceof MaltFarmDeliveryPoi) {
			record.setAddress(JsonTranslator.toJson(((MaltFarmDeliveryPoi) deliveryPoi).getStoreAddress()));
		}

		if(deliveryPoi.getLastDeliveryPlatform()!=null){
			record.setLastPlatformType(deliveryPoi.getLastDeliveryPlatform().getCode());
		}

		return record;
	}

	/**
	 * isQueryTenantAndStoreConfig参数在translateSelfBuiltDeliveryPoi方法里生效
	 * 如果isQueryTenantAndStoreConfig为true,构建SelfBuiltDeliveryPoi时,查tenantConfigMap和storeConfigMap会查DB
	 * 因为translate方法会在外部被循环调用,有些查询场景不需要tenantConfigMap和storeConfigMap,此时isQueryTenantAndStoreConfig可以传false
	 */
	private Optional<DeliveryPoi> translate(StoreConfigDO record, Boolean isQueryTenantAndStoreConfig) {
		if (record == null) {
			return Optional.empty();
		}

		DeliveryPlatformEnum deliveryPlatform = DeliveryPlatformEnum.enumOf(record.getOpenAggrPlatform());
		if (deliveryPlatform == null) {
			log.error("Unknown deliveryPlatform[{}] from store[{}], will treat as null", record.getOpenAggrPlatform(), record.getStoreId());
			return Optional.empty();
		}

		switch (deliveryPlatform) {
			case AGGREGATION_DELIVERY_PLATFORM:
				return translateAggrDeliveryPoi(record);

			case MALT_FARM_DELIVERY_PLATFORM:
				return translateMaltFarmDeliveryPoi(record);

			case MERCHANT_SELF_DELIVERY:
				return translateMerchantSelfDeliveryPoi(record);

			case SELF_BUILT_DELIVERY_PLATFORM:
				return translateSelfBuiltDeliveryPoi(record, isQueryTenantAndStoreConfig);
			case DAP_DELIVERY_PLATFORM:
				return translateDapDeliveryPoi(record);
			case ORDER_CHANNEL_DELIVERY_PLATFORM:
				return translateOrderChannelDeliveryPoi(record);
			default:
				log.error("Unsupported DeliveryPlatform[{}] from store[{}], will treat as null", deliveryPlatform.getCode(), record.getStoreId());
				return Optional.empty();
		}
	}

	private Optional<DeliveryPoi> tryMakeUpMissingSelfBuiltDeliveryPoi(Long tenantId, Long storeId,Integer channelType) {
		Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> tenantConfigMap = querySelfBuiltTenantConfig(tenantId);
		if (MapUtils.isEmpty(tenantConfigMap)) {
			tenantConfigMap=new HashMap<>();
		}

		Map<ThirdDeliveryChannelEnum, ChannelStoreConfig> storeConfigMap = querySelfBuiltStoreConfig(tenantId, storeId, tenantConfigMap.keySet());
		if (MapUtils.isEmpty(storeConfigMap)) {
			storeConfigMap=new HashMap<>();
		}

		//查询租户侧遗留配置信息
		DeliveryLaunchPoint deliveryLaunchPoint = tenantRemoteService.queryDeliveryLaunchPointConfig(tenantId, storeId);
		//查询租户侧门店信息
		TenantStoreInfo storeInfo = tenantSystemClient.queryStoreDetailInfo(tenantId, storeId);

		if(channelType == DynamicChannelType.JD2HOME.getChannelId()){
			deliveryLaunchPoint.setImmediateOrderDeliveryLaunchPointConfig(new ImmediateOrderDeliveryLaunchPointConfig(ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE,0));
		}

		//补建一个配送门店，并保存
		SelfBuiltDeliveryPoi deliveryPoi = new SelfBuiltDeliveryPoi(
				tenantId,
				storeId,
				Optional.ofNullable(storeInfo).map(TenantStoreInfo::getCityCode).orElse(null),
				Optional.ofNullable(storeInfo).map(TenantStoreInfo::getPhone).orElse(null),
				deliveryLaunchPoint,
				tenantConfigMap,
				storeConfigMap,
				channelType
		);
		saveDeliveryPoi(deliveryPoi);

		return Optional.of(deliveryPoi);
	}

	/**
	 * 查询租户开通的渠道信息
	 */
	private Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> querySelfBuiltTenantConfig(Long tenantId) {
		if (MccConfigUtils.closeQueryDeliveryConfigSwitch()) {
			Cat.logEvent("DELIVERY_CONFIG", "querySelfBuiltTenantConfig");
			return new HashMap<>();
		}
		DeliveryConfigDOExample tenantConfigQueryExample = new DeliveryConfigDOExample();
		tenantConfigQueryExample.createCriteria()
				.andTenantIdEqualTo(tenantId)
				.andIsDelEqualTo(NON_DELETED)
				.andEnabledEqualTo(ENABLED);

		List<DeliveryConfigDO> records = deliveryConfigDOExMapper.selectByExampleSlave(tenantConfigQueryExample);

		return Optional.ofNullable(records)
				.orElse(new ArrayList<>())
				.stream()
				.filter(it -> Objects.nonNull(ThirdDeliveryChannelEnum.valueOf(it.getDeliveryChannelId())))
				.map(it -> new ChannelTenantConfig(
						it.getId(),
						ThirdDeliveryChannelEnum.valueOf(it.getDeliveryChannelId()),
						it.getAppKey(),
						it.getSecretKey(),
						it.getEnabled() == ENABLED)
				)
				.collect(Collectors.toMap(ChannelTenantConfig::getDeliveryChannel, it -> it, (o1, o2) -> o2));
	}

	/**
	 * 查询门店绑定记录
	 */
	private Map<ThirdDeliveryChannelEnum, ChannelStoreConfig> querySelfBuiltStoreConfig(Long tenantId,
	                                                                                    Long storeId,
	                                                                                    Set<ThirdDeliveryChannelEnum> tenantChannelSet) {
		if(CollectionUtils.isEmpty(tenantChannelSet)){
			return new HashMap<>();
		}

		ShopDeliveryConfigDOExample storeConfigQueryExample = new ShopDeliveryConfigDOExample();
		storeConfigQueryExample.createCriteria()
				.andTenantIdEqualTo(tenantId)
				.andShopIdEqualTo(storeId)
				.andDeliveryChannelIdIn(tenantChannelSet.stream().map(ThirdDeliveryChannelEnum::getCode).distinct().collect(Collectors.toList()))
				.andIsDelEqualTo(NON_DELETED)
				.andEnabledEqualTo(ENABLED);

		List<ShopDeliveryConfigDO> records = shopDeliveryConfigDOExMapper.selectByExampleSlave(storeConfigQueryExample);

		return Optional.ofNullable(records)
				.orElse(new ArrayList<>())
				.stream()
				.filter(it -> Objects.nonNull(ThirdDeliveryChannelEnum.valueOf(it.getDeliveryChannelId())))
				.map(it -> new ChannelStoreConfig(
						it.getId(),
						it.getShopId(),
						ThirdDeliveryChannelEnum.valueOf(it.getDeliveryChannelId()),
						it.getDeliveryChannelPoiCode(),
						it.getDeliveryChannelPoiName(),
						JsonUtil.fromJson(it.getDeliveryServiceCodes(), new TypeReference<List<String>>() {
						}),
						it.getEnabled() == ENABLED
				))
				.collect(Collectors.toMap(ChannelStoreConfig::getDeliveryChannel, it -> it, (o1, o2) -> o2));
	}

	private DeliveryLaunchPoint translateDeliveryLaunchPoint(StoreConfigDO record) {
		return new DeliveryLaunchPoint(
				new ImmediateOrderDeliveryLaunchPointConfig(
						ImmediateOrderDeliveryLaunchPointEnum.enumOf(record.getDeliveryLaunchPoint()),
						record.getDeliveryLaunchDelayMinutes()
				),
				new BookingOrderDeliveryLaunchPointConfig(
						BookingOrderDeliveryLaunchPointEnum.enumOf(record.getBookingOrderDeliveryLaunchPoint()),
						record.getBookingOrderDeliveryLaunchMinutes()
				)
		);
	}

	private Optional<DeliveryPoi> translateAggrDeliveryPoi(StoreConfigDO record) {
		return Optional.of(new AggrDeliveryPoi(
				record.getId(),
				record.getTenantId(),
				record.getStoreId(),
				record.getCityCode(),
				record.getContactPhone(),
				translateDeliveryLaunchPoint(record),
				DeliveryLaunchTypeEnum.enumOf(record.getIsAutoLaunch()),
				record.getDeliveryLaunchRule(),
				record.getChannelType(),
				DeliveryPlatformEnum.enumOf(record.getLastPlatformType()),
				DeliveryIsShowItemNumberEnum.enumOf(record.getIsShowItemNumber())

		));
	}

	private Optional<DeliveryPoi> translateMerchantSelfDeliveryPoi(StoreConfigDO record) {
		return Optional.of(new MerchantSelfDeliveryPoi(
				record.getId(),
				record.getTenantId(),
				record.getStoreId(),
				record.getCityCode(),
				record.getContactPhone(),
				translateDeliveryLaunchPoint(record),
				record.getChannelType(),
				DeliveryPlatformEnum.enumOf(record.getLastPlatformType()),
				DeliveryIsShowItemNumberEnum.enumOf(record.getIsShowItemNumber())
		));
	}

	private Optional<DeliveryPoi> translateMaltFarmDeliveryPoi(StoreConfigDO record) {
		return Optional.of(new MaltFarmDeliveryPoi(
				record.getId(),
				record.getTenantId(),
				record.getStoreId(),
				record.getCityCode(),
				record.getContactPhone(),
				translateDeliveryLaunchPoint(record),
				JsonTranslator.fromAddressJson(record.getAddress()),
				record.getChannelType(),
				DeliveryPlatformEnum.enumOf(record.getLastPlatformType()),
				DeliveryIsShowItemNumberEnum.enumOf(record.getIsShowItemNumber())
		));
	}

	private Optional<DeliveryPoi> translateDapDeliveryPoi(StoreConfigDO record) {
		return Optional.of(new DapDeliveryPoi(
				record.getId(),
				record.getTenantId(),
				record.getStoreId(),
				record.getCityCode(),
				record.getContactPhone(),
				translateDeliveryLaunchPoint(record),
				JsonTranslator.fromAddressJson(record.getAddress()),
				record.getChannelType(),
				DeliveryPlatformEnum.enumOf(record.getLastPlatformType()),
				DeliveryIsShowItemNumberEnum.enumOf(record.getIsShowItemNumber())
		));
	}

	/**
	 * 如果isQueryTenantAndStoreConfig为true,构建SelfBuiltDeliveryPoi时,查tenantConfigMap和storeConfigMap会查DB
	 * 因为translate方法会在外部被循环调用,有些场景不需要tenantConfigMap和storeConfigMap,此时isQueryTenantAndStoreConfig可以传false
	 */
	private Optional<DeliveryPoi> translateSelfBuiltDeliveryPoi(StoreConfigDO record, Boolean isQueryTenantAndStoreConfig) {
		Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> tenantConfigMap = isQueryTenantAndStoreConfig ? querySelfBuiltTenantConfig(record.getTenantId()) : Maps.newHashMap();

		Map<ThirdDeliveryChannelEnum, ChannelStoreConfig> storeConfigMap = isQueryTenantAndStoreConfig ? querySelfBuiltStoreConfig(
				record.getTenantId(), record.getStoreId(), Optional.ofNullable(tenantConfigMap).map(Map::keySet).orElse(new HashSet<>())) : Maps.newHashMap();

		DeliveryStrategyEnum deliveryStrategy = DeliveryStrategyEnum.valueOf(record.getDeliveryStrategy());
		SelfBuiltDeliveryPoi deliveryPoi = new SelfBuiltDeliveryPoi(
				record.getId(),
				record.getTenantId(),
				record.getStoreId(),
				record.getCityCode(),
				record.getContactPhone(),
				translateDeliveryLaunchPoint(record),
				JsonTranslator.fromAddressJson(record.getAddress()),
				deliveryStrategy,
				JsonTranslator.fromDeliveryStrategyConfigJson(record.getDeliveryStrategyConfig(), deliveryStrategy),
				tenantConfigMap,
				storeConfigMap,
				JsonTranslator.fromOrderPlatformDeliveryConfigJson(record.getOrderPlatformDeliveryConfig()),
				record.getChannelType(),
				DeliveryPlatformEnum.enumOf(record.getLastPlatformType()),
				DeliveryIsShowItemNumberEnum.enumOf(record.getIsShowItemNumber())
		);

		if (deliveryPoi.selfCorrect()) {
			saveDeliveryPoi(deliveryPoi);
		}

		return Optional.of(deliveryPoi);
	}

	private Optional<DeliveryPoi> translateOrderChannelDeliveryPoi(StoreConfigDO record) {
		return Optional.of(new OrderChannelDeliveryPoi(
				record.getId(),
				record.getTenantId(),
				record.getStoreId(),
				record.getCityCode(),
				record.getContactPhone(),
				translateDeliveryLaunchPoint(record),
				JsonTranslator.fromAddressJson(record.getAddress()),
				record.getChannelType(),
				DeliveryPlatformEnum.enumOf(record.getLastPlatformType()),
				DeliveryIsShowItemNumberEnum.enumOf(record.getIsShowItemNumber())
		));
	}
}
