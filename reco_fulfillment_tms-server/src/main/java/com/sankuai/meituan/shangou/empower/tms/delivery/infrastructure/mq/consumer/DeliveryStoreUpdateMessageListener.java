package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DeliveryStoreOptionEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.DeliveryStoreChangeContent;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.DeliveryRangeSyncCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.DeliveryStoreApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.StoreOpeningOnlineChannelsQueryResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.OrderPlatformDeliveryConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * 渠道配送门店变更监听
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/14
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class DeliveryStoreUpdateMessageListener extends AbstractDeadLetterConsumer {

	@Resource
	private DeliveryStoreApplicationService deliveryStoreApplicationService;
	@Resource
	private TenantSystemClient tenantSystemClient;
	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;

	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.DELIVERY_STORE_UPDATE;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
		log.info("开始消费配送门店变更消息: {}", mafkaMessage);
		DeliveryStoreChangeContent message = translateMessage(mafkaMessage);
		if (message == null) {
			return CONSUME_SUCCESS;
		}

		try {
			if (message.getOptionType() == DeliveryStoreOptionEnum.DELIVERY_RANGE_CHANGE.getCode()) {
				Optional<SelfBuiltDeliveryPoi> opDeliveryPoi = deliveryPoiRepository.querySelfBuiltDeliveryPoi(
						ThirdDeliveryChannelEnum.valueOf(message.getDeliveryChannelType()), message.getChannelStoreCode()
				);

				if (!opDeliveryPoi.isPresent()) {
					log.warn("配送门店不可用, message={}", message);
					return CONSUME_SUCCESS;
				}
				SelfBuiltDeliveryPoi deliveryPoi = opDeliveryPoi.get();

				StoreOpeningOnlineChannelsQueryResult onlineChannelsQueryResult
						= tenantSystemClient.queryStoreOpeningOnlineChannels(deliveryPoi.getTenantId(), deliveryPoi.getStoreId());
				if (!onlineChannelsQueryResult.isSuccess()) {
					log.error("消费配送门店变更消息失败:查询门店开通的线上渠道失败，将会进行重试");
					return CONSUME_FAILURE;
				}

				Map<Integer, OrderPlatformDeliveryConfig> orderPlatformDeliveryConfigMap = new HashMap<>();
				for (Integer channelId : onlineChannelsQueryResult.getChannelIds()) {
					orderPlatformDeliveryConfigMap.put(channelId, null);
				}

				Map<Integer, Failure> failureMap = deliveryStoreApplicationService.syncDeliveryRange(
						new DeliveryRangeSyncCmd(deliveryPoi, orderPlatformDeliveryConfigMap)
				);

				if (MapUtils.isNotEmpty(failureMap)) {
					log.error("消费配送门店变更消息失败，将会进行重试消费");
					return CONSUME_FAILURE;
				}
			}

			return CONSUME_SUCCESS;

		} catch (Exception e) {
			log.error("消费配送门店变更消息失败，将会进行重试消费", e);
			return CONSUME_FAILURE;
		}
	}

	private DeliveryStoreChangeContent translateMessage(MafkaMessage mafkaMessage) {
		try {
			DeliveryStoreChangeContent message = translateMessage(mafkaMessage, DeliveryStoreChangeContent.class);
			Preconditions.checkNotNull(message, "empty mafkaMessage");
			Preconditions.checkNotNull(ThirdDeliveryChannelEnum.valueOf(message.getDeliveryChannelType()), "channelType is invalid");
			Preconditions.checkNotNull(message.getChannelStoreCode(), "channelStoreCode is null");
			Preconditions.checkNotNull(DeliveryStoreOptionEnum.enumOf(message.getOptionType()), "opType is invalid");
			return message;

		} catch (Exception e) {
			log.error("解析配送门店变更消息失败:{}", mafkaMessage, e);
			Cat.logEvent("DELIVERY_STORE_UPDATE_CONSUME_FAILED", "MESSAGE_WRONG");
			return null;
		}
	}
}
