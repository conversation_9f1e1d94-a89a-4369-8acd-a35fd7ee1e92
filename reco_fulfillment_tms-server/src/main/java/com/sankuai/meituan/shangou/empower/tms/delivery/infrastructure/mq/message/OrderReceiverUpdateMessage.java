package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-12-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderReceiverUpdateMessage extends OrderReceiverUpdateBaseMessage {

    /**
     * 转单前的配送方式
     */
    private Integer deliveryPlatformCode;


    public OrderReceiverUpdateMessage(OrderReceiverUpdateBaseMessage baseMessage) {
        super(baseMessage.getTenantId(), baseMessage.getStoreId(), baseMessage.getOrderId(), baseMessage.getUserPhone());
    }
}
