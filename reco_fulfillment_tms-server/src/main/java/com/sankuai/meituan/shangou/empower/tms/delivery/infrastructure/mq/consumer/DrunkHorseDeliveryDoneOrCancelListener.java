package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.hu.SealContainerLogQueryClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryAsyncOutTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.SealContainerReturnTimeoutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DHDeliveryStatusCheckMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.logistics.hu.api.dto.SealContainerLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * <AUTHOR>
 * @since 2024/8/6 14:40
 **/
@Service
@Slf4j
public class DrunkHorseDeliveryDoneOrCancelListener extends AbstractDeadLetterConsumer {

    @Resource
    private OrderSystemClient orderSystemClient;


    @Resource
    private SealContainerLogQueryClient sealContainerLogQueryClient;

    @Resource
    private MafkaDelayMessageProducer<SealContainerReturnTimeoutMessage> sealContainerReturnTimeoutMessageDelayProducer;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.DRUNK_HORSE_DELIVERY_DONE_OR_CANCEL_CONSUMER;
    }


    @Override
    protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
        log.info("开始消费配送完成或配送取消消息: mafkaMessage:{}", mafkaMessage);

        DeliveryChangeSyncOutMessage message = translateMessage(mafkaMessage);
        if (message == null) {
            return CONSUME_SUCCESS;
        }

        //过滤非歪马租户
        if (!MccConfigUtils.checkIsDHTenant(message.getHead().getTenantId())) {
            return CONSUME_SUCCESS;
        }

        if (!MccConfigUtils.isSealContainerReturnTimeoutNotifyGrayStore(message.getHead().getShopId())) {
            return CONSUME_SUCCESS;
        }

        //只消费送达和取消消息
        if (!Objects.equals(message.getChangeType(), DeliveryAsyncOutTypeEnum.DELIVER_COMPLETE.getValue())
                && !Objects.equals(message.getChangeType(), DeliveryAsyncOutTypeEnum.DELIVERY_CANCEL.getValue())) {
            return CONSUME_SUCCESS;
        }

        //如果是配送取消消息 需要判断订单是否取消, 原因是转配送方式也会取消运单 必须是订单取消才行
        if (Objects.equals(message.getChangeType(), DeliveryAsyncOutTypeEnum.DELIVERY_CANCEL.getValue())) {
            Result<OrderInfo> orderInfo = orderSystemClient.getOrderInfo(message.getHead().getOrderId(), false);
            if (!orderInfo.isSuccess()) {
                throw new BizException("查询订单信息失败");
            }

            if (!Objects.equals(orderInfo.getInfo().getOrderStatus(), OrderStatusEnum.CANCELED.getValue())) {
                return CONSUME_SUCCESS;
            }
        }

        //查询本单是否有使用容具
        List<SealContainerLogDTO> usingSealContainerList = sealContainerLogQueryClient.queryUsingContainerByOrderKey(message.getHead().getTenantId(), message.getHead().getShopId(),
                message.getHead().getChannelOrderId(), message.getHead().getOrderBizType());
        if (CollectionUtils.isEmpty(usingSealContainerList)) {
            return CONSUME_SUCCESS;
        }

        try {
            if (MccConfigUtils.isSignOrderCompletedOrCanceledGrayStore(message.getHead().getShopId())) {
                //标记容具对应订单已完成或已取消
                sealContainerLogQueryClient.signOrderCompletedOrCanceled(message.getHead().getTenantId(), message.getHead().getShopId(), message.getHead().getChannelOrderId(), message.getHead().getOrderBizType());
            }
        } catch (Exception e) {
            log.error("标记容具对应订单已完成或已取消失败, tenantId:{} storeId:{} channelOrderId:{} orderBizType:{}",
                    message.getHead().getTenantId(), message.getHead().getShopId(), message.getHead().getChannelOrderId(), message.getHead().getOrderBizType(), e);
            Cat.logEvent("SIGN-ORDER-COMPLETED-OR-CANCELED", "SIGN-ORDER-COMPLETED-OR-CANCELED-FAIL");
        }

        //发送延时消息
        SealContainerReturnTimeoutMessage returnTimeoutMessage = new SealContainerReturnTimeoutMessage();
        returnTimeoutMessage.setChannelOrderId(message.getHead().getChannelOrderId());
        returnTimeoutMessage.setOrderBizType(message.getHead().getOrderBizType());
        returnTimeoutMessage.setTenantId(message.getHead().getTenantId());
        returnTimeoutMessage.setStoreId(message.getHead().getShopId());
        returnTimeoutMessage.setRiderAccountId(usingSealContainerList.get(0).getOperatorId());
        sealContainerReturnTimeoutMessageDelayProducer.sendDelayMessage(returnTimeoutMessage, MccConfigUtils.sealContainerReturnTimeOutConfig());

        return CONSUME_SUCCESS;
    }

    public DeliveryChangeSyncOutMessage translateMessage(MafkaMessage message) {
        try {
            DeliveryChangeSyncOutMessage outMessage = super.translateMessage(message, new TypeReference<DeliveryChangeSyncOutMessage>(){});
            outMessage.validCheck();
            return outMessage;
        } catch (Exception e) {
            log.error("解析运单变更同步消息失败:{}", message);
            Cat.logEvent("DELIVERY_CHANGE_SYNC_OUT_CONSUME_FAILED", "MESSAGE_WRONG");
            return null;
        }
    }


}
