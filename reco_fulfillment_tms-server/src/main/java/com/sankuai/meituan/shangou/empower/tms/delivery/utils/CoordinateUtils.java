package com.sankuai.meituan.shangou.empower.tms.delivery.utils;

import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 坐标工具类
 * @date 2024-12-02
 */
public class CoordinateUtils {
    private static final double EARTH_RADIUS = 6378137.0; // 地球半径，单位：米

    /**
     * @param longitude 经度
     * @param latitude  纬度
     * @param distance  距离(米)
     * @param angle     角度(度)
     * @return 新的经纬度[newLon, newLat]
     */
    public static double[] calculateNewCoordinates(double longitude, double latitude, double distance, double angle) {
        if (Objects.equals(distance, NumberUtils.DOUBLE_ZERO)) {
            return new double[]{longitude, latitude};
        }
        double radLat = rad(latitude);
        double radLon = rad(longitude);
        double radDist = distance / EARTH_RADIUS;
        double radAngle = rad(angle); // 角度转换为弧度

        double newLat = Math.asin(Math.sin(radLat) * Math.cos(radDist) +
                Math.cos(radLat) * Math.sin(radDist) * Math.cos(radAngle));
        double newLon = radLon + Math.atan2(Math.sin(radAngle) * Math.sin(radDist) * Math.cos(radLat),
                Math.cos(radDist) - Math.sin(radLat) * Math.sin(newLat));

        newLat = newLat * 180.0 / Math.PI;
        newLon = newLon * 180.0 / Math.PI;

        return new double[]{roundingHalfUp(newLon, 6), roundingHalfUp(newLat, 6)};
    }

    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }

    public static double roundingHalfUp(double value, int scale) {
        BigDecimal bigDecimal =  BigDecimal.valueOf(value);
        bigDecimal = bigDecimal.setScale(scale, RoundingMode.HALF_UP);
        return bigDecimal.doubleValue();
    }
}
