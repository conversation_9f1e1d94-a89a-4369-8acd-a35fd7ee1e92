package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.DeliveryCancelFailedEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryCancelFailInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryCancelCommandMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryWarnEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;
import static com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo.NO_EXCEPTION;

/**
 * <AUTHOR>
 * @date 2020/9/16
 * desc: 取消配送消息Listener
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class DeliveryCancelCommandMessageListener extends AbstractDeadLetterConsumer {

	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private DeliveryWarnEventPublisher deliveryWarnEventPublisher;
	@Resource
	private DeliveryChangeNotifyService deliveryChangeNotifyService;

	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.DELIVERY_CANCEL_COMMAND;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
		log.info("开始消费取消配送消息：{}", mafkaMessage);
		DeliveryCancelCommandMessage message = translateMessage(mafkaMessage);
		if (message == null) {
			return CONSUME_SUCCESS;
		}

		try {
			DeliveryOrder deliveryOrder = null;
			if(MccConfigUtils.getDeliveryQueryTenantSwitch(message.getTenantId())){
				deliveryOrder = deliveryOrderRepository.getDeliveryOrderWithTenant(message.getDeliveryId(),message.getTenantId(),message.getStoreId());
			}else {
				deliveryOrder = deliveryOrderRepository.getDeliveryOrder(message.getDeliveryId());
			}

			if (deliveryOrder == null) {
				log.error("取消配送消息处理失败：运单查询失败, deliveryId={}", message.getDeliveryId());
				Cat.logEvent("DELIVERY_CANCEL_FAILED", "DELIVERY_ORDER_NOT_EXIST");
				return CONSUME_SUCCESS;
			}

			Optional<Failure> failure;
			if (MccConfigUtils.getDeliveryCancelFilterSwitch()) {
				// 为true时，对终态进行过滤，默认为false
				failure = deliveryOrder.cancel();
			} else {
				failure = deliveryOrder.cancelWithoutFilter();
			}

			if (failure.isPresent()) {
				deliveryChangeNotifyService.notifyDeliveryChangeLog(
						deliveryOrder.getId(), DeliveryEventEnum.DELIVERY_CANCEL_FAIL,
						new DeliveryCancelFailInfo(failure.get().getFailureCode(), failure.get().getFailureMessage()),
						LocalDateTime.now()
				);
				deliveryWarnEventPublisher.postEvent(new DeliveryCancelFailedEvent(deliveryOrder, failure.get().getFailureMessage()));

				if (failure.get().isNeedRetry() && !isLastRetry(mafkaMessage)) {
					log.warn("取消配送消息处理失败，将会进行重试, deliveryId={}, failure={}", deliveryOrder.getId(), failure.get());
					return CONSUME_FAILURE;
				} else {
					log.error("取消配送消息处理失败，将放弃取消, deliveryId={}, failure={}", deliveryOrder.getId(), failure.get());
					return CONSUME_SUCCESS;
				}

			} else {
				//成功取消了且停留在待发配送,歪马需要同步取消运单
				//NOTE：因为新供给靠订单来过滤的列表，停留在代发配送的运单会因为订单状态被过滤掉。
				//歪马列表靠的是运单，但上面的deliveryOrder.cancel并不会去取消聚合平台的运单，而是靠聚合平台回调来取消运单
				//而青云在待发配送的状态下去取消运单时不会去回调消息。因此在这里同步持久化
				if (MccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId()) &&
						(Objects.equals(deliveryOrder.getStatus(), DeliveryStatusEnum.DELIVERY_LAUNCHED) || Objects.equals(deliveryOrder.getStatus(), DeliveryStatusEnum.INIT))) {
					deliveryOrder.onChange(DeliveryEventEnum.DELIVERY_CANCEL, NO_EXCEPTION, deliveryOrder.getRiderInfo(), LocalDateTime.now());
				}
			}

			return CONSUME_SUCCESS;
		} catch (Exception e) {
			log.warn("取消配送消息处理异常", e);
			return CONSUME_FAILURE;
		}
	}

	private DeliveryCancelCommandMessage translateMessage(MafkaMessage mafkaMessage) {
		try {
			DeliveryCancelCommandMessage message = translateMessage(mafkaMessage, DeliveryCancelCommandMessage.class);
			Preconditions.checkNotNull(message, "empty mafkaMessage");
			Preconditions.checkNotNull(message.getTenantId(), "tenantId is null");
			Preconditions.checkNotNull(message.getStoreId(), "storeId is null");
			Preconditions.checkNotNull(message.getDeliveryId(), "deliveryId is null");
			return message;

		} catch (Exception e) {
			log.error("解析取消配送消息失败:{}", mafkaMessage, e);
			Cat.logEvent("DELIVERY_CANCEL_FAILED", "MESSAGE_WRONG");
			return null;
		}
	}
}
