package com.sankuai.meituan.shangou.empower.tms.delivery.configuration;

import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.Data;

@Data
public class TMSConstant {

    /**
     * Mafka 延时消息支持的最小毫秒数
     */
    public static final Long MAFKA_DELAY_MESSAGE_MILLIS_MIN = 5000L;

    /**
     * Mafka 延时消息支持的最大毫秒数
     */
    public static final Long MAFKA_DELAY_MESSAGE_MILLIS_MAX = 30 * 24 * 60 * 60 * 1000L;

    public static final String CHAR_SET = "UTF-8";

    public static final String QUERY_ORDER_FINANCE = "queryOrderFinance";

}
