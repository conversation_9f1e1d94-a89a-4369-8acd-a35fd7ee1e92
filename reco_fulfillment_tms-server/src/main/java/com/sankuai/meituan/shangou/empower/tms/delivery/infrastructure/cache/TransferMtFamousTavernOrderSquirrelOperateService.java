package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache;

import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;


@Service
public class TransferMtFamousTavernOrderSquirrelOperateService extends SquirrelOperateService{

    @Autowired
    @Qualifier("redisSgNewSupplyOfc")
    protected RedisStoreClient redisNewSupplyClient;

    private static final String CATEGORY_NAME = "famous_tavern_transfer_order";
    private static final String KEY_PREFIX = "mt_famous_tavern_transfer_order_";

    @Override
    protected RedisStoreClient getRedisClient() {
        return redisNewSupplyClient;
    }
    @Override
    public String getCategoryName() {
        return CATEGORY_NAME;
    }

    public String getKey (Long tenantId, Long storeId, Long orderId) {

        return KEY_PREFIX + tenantId + "_"+ storeId+"_" + orderId;

    }
}
