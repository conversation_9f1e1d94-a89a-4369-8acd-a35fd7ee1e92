package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.DeliveryLaunchResultCallbackCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import lombok.*;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DeliveryLaunchCallbackInfo extends DeliveryUnifiedCallbackInfo {

	/**
	 * 赋能门店id
	 */
	private Long storeId;

	/**
	 * 赋能订单id
	 */
	private Long orderId;

	/**
	 * 是否发起成功
	 */
	private boolean launchSuccess;

	/**
	 * 配送商code
	 */
	private Integer deliveryChannelCode;

	/**
	 * 配送商具体服务包
	 */
	private String servicePackage;

	/**
	 * 渠道配送id
	 */
	private String channelDeliveryId;

	/**
	 * 配送距离(发起成功才有)
	 */
	private Long distance;

	/**
	 * 配送费用(发起成功才有)
	 */
	private Double deliveryFee;

	/**
	 * 发起失败原因(发起失败才有)
	 */
	private String launchFailReason;

	public DeliveryLaunchResultCallbackCmd toCmd(){
		return new DeliveryLaunchResultCallbackCmd(
				storeId, orderId, channelDeliveryId, launchSuccess, launchFailReason, deliveryChannelCode,
				servicePackage, distance, Optional.ofNullable(deliveryFee).map(BigDecimal::valueOf).orElse(null)
		);
	}
}
