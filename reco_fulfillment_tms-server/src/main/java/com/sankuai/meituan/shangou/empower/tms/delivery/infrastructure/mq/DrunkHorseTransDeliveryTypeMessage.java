package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/9/4 16:05
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DrunkHorseTransDeliveryTypeMessage {
    private String viewOrderId;

    private Integer orderBizType;

    /**
     * 1-转自送 2-转三方配送
     */
    private Integer transType;

    /**
     * 转配送后的骑手id，只有在1-转自送场景下有值
     */
    private Long newRiderAccountId;

    /**
     * 转配送后的骑手名字，只有在1-转自送场景下有值
     */
    private String newRiderName;
}
