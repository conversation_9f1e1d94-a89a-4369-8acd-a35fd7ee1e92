package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderLocatingExceptionDataDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.OrderLastRiderLocationSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.OrderLastRiderLocationDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocatingExceptionDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.ConvertUtil;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/2/26 15:01
 **/
@Service
@Slf4j
public class SquirrelOrderLastRiderLocationRepository {
    @Resource
    private OrderLastRiderLocationSquirrelOperationService orderLastRiderLocationSquirrelOperationService;

    @MethodLog(logRequest = false, logResponse = true)
    public void save(OrderLastRiderLocationDetail orderLastRiderLocationDetail) {
        if (orderLastRiderLocationDetail == null) {
            return;
        }

        orderLastRiderLocationSquirrelOperationService.set(orderLastRiderLocationDetail.getOrderId()+"", orderLastRiderLocationDetail);
    }


    @MethodLog(logRequest = false, logResponse = true)
    public Optional<OrderLastRiderLocationDetail> get(Long orderId) {
        if (orderId == null) {
            return Optional.empty();
        }

       return orderLastRiderLocationSquirrelOperationService.get(orderId + "", OrderLastRiderLocationDetail.class);
    }
}
