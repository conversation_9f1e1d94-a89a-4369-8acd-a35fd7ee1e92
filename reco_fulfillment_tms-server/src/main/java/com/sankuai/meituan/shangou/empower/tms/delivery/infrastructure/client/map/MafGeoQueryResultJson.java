package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.map;

import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/1/4
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MafGeoQueryResultJson {

	private static final String SPLITTER = ",";
	private static final int EXPECT_SPLIT_LENGTH = 2;
	private static final int LNG_INDEX = 0;
	private static final int LAT_INDEX = 1;

	/**
	 * 可信度从0-100，100为最可信，
	 * 0认为输入不是地址
	 * 该值>=70时，解析结果较为准确，
	 * <70时，会存各类不可靠因素
	 * 可信度为-1时，表示该可信度无效
	 */
	private Integer reliability;

	/**
	 * 格式为"lng,lat"。示例："116.480881,39.989410"
	 */
	private String location;

	public Optional<CoordinatePoint> getCoordinatePoint() {
		return Optional.ofNullable(location)
				.filter(StringUtils::isNotBlank)
				.map(it -> it.split(SPLITTER))
				.filter(it -> it.length == EXPECT_SPLIT_LENGTH)
				.map(it -> new CoordinatePoint(it[LNG_INDEX], it[LAT_INDEX]));
	}
}
