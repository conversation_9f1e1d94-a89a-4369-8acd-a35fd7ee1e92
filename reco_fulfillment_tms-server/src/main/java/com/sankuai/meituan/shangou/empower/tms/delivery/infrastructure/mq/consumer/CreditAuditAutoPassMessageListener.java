package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.CreditAuditAutomaticPassMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.CreditAuditNotifyMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.RiderOperateApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.CreditAuditResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * <AUTHOR>
 * @since 2023/5/8 17:41
 **/
@Component
@Slf4j
public class CreditAuditAutoPassMessageListener extends AbstractDeadLetterConsumer  {
    @Resource
    private RiderOperateApplicationService riderOperateApplicationService;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.CREDIT_AUDIT_AUTOMATIC_PASS_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
        try {
            log.info("开始消费图片审核自动通过消息：{}", mafkaMessage);
            CreditAuditAutomaticPassMessage message = translateMessage(mafkaMessage, CreditAuditAutomaticPassMessage.class);
            Preconditions.checkNotNull(message, "empty mafkaMessage");
            Preconditions.checkNotNull(message.getDeliveryOrderId(), "deliveryOrderId is null");

            Optional<Failure> failure;
            failure = riderOperateApplicationService.processCreditAuditResult(buildCreditAuditResult(message));

            //业务异常不重试
            if (failure.isPresent()) {
                log.error("消费消费图片审核自动通过消息失败, 失败原因:{} ", failure.get());
                return CONSUME_SUCCESS;
            }
        } catch (Exception e) {
            Cat.logEvent("DELIVERY_PROOF_PHOTO", "CONSUME_CREDIT_AUTO_PASS_MESSAGE_FAIL");
            log.error("消费消费图片审核自动通过消息失败", e);
            return CONSUME_FAILURE;
        }


        return CONSUME_SUCCESS;
    }

    private CreditAuditResult buildCreditAuditResult(CreditAuditAutomaticPassMessage message) {
        CreditAuditResult creditAuditResult = new CreditAuditResult();
        creditAuditResult.setDatetime(LocalDateTime.now());
        creditAuditResult.setViolationPicIdList(Collections.emptyList());
        creditAuditResult.setDeliveryOrderId(message.getDeliveryOrderId());
        creditAuditResult.setIsAutoPass(true);
        creditAuditResult.setTenantId(message.getTenantId());
        creditAuditResult.setStoreId(message.getStoreId());
        return creditAuditResult;
    }
}
