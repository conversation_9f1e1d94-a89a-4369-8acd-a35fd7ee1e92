package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.meituan.reco.pickselect.common.domain.orderTrack.OperationScene;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.OrderPlatformDeliveryOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.AuditExceptionCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.CallDeliveryCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.CancelDeliveryCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.ReportExceptionCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.UpdateTipCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.dto.response.TmsResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.OrderPlatformDeliveryOperateThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.AuditExceptionReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.CallDeliveryReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.CancelDeliveryReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.ReportExceptionReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.UpdateTipReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryOperationResultDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * OrderPlatformDeliveryOperateThriftServiceImpl
 *
 * <AUTHOR>
 * @since 2023/3/1
 */
@Slf4j
@Service
public class OrderPlatformDeliveryOperateThriftServiceImpl implements OrderPlatformDeliveryOperateThriftService {

    @Autowired
    private OrderPlatformDeliveryOperateService orderPlatformDeliveryOperateService;

    /**
     * @see com.sankuai.meituan.shangou.empower.tms.delivery.configuration.MessageProducerConfiguration#orderTrackLogProducer()
     */
    @Autowired
    private MafkaMessageProducer<OrderTrackEvent> orderTrackLogProducer;

    @Autowired
    private OrderSystemClient orderSystemClient;

    @MethodLog(logRequest = true)
    @Override
    public TmsResponse<DeliveryOperationResultDto> updateTip(UpdateTipReq req) throws TException {
        Operator operator = new Operator(req.getOperatorAccountId(), req.getOperatorName());
        try {
            OrderInfo order = getOrder(req.getEmpowerOrderId());
            boolean executed = orderPlatformDeliveryOperateService.updateTip(new UpdateTipCmd(req.getTenantId(), order.getPoiId(),
                    order, req.getChannelId(), operator, req.getTipAmount()));
            if (!executed) {
                return TmsResponse.succeed(null);
            }

            Map<String, Object> extMap = new HashMap<>();
            extMap.put("operatorName", req.getOperatorName());
            extMap.put("tipAmount", req.getTipAmount());

            sendOrderTrackEvent(req.getTenantId(), order.getChannelOrderId(), order.getOrderBizType(),
                    req.getOperatorAccountId(), req.getRequestAppId(), TrackOpType.DELIVERY_RAISE_TIP, extMap);
            return TmsResponse.succeed(null);
        } catch (Exception e) {
            log.error("#updateTip error: {}", e.getMessage(), e);
            return TmsResponse.fail(e);
        }
    }

    @MethodLog(logRequest = true)
    @Override
    public TmsResponse<DeliveryOperationResultDto> cancelDelivery(CancelDeliveryReq req) throws TException {
        Operator operator = new Operator(req.getOperatorAccountId(), req.getOperatorName());
        try {
            OrderInfo order = getOrder(req.getEmpowerOrderId());
            boolean executed = orderPlatformDeliveryOperateService.cancelDelivery(new CancelDeliveryCmd(req.getTenantId(),
                    order.getPoiId(), order, req.getChannelId(), operator));
            if (!executed) {
                return TmsResponse.succeed(null);
            }

            sendOrderTrackEvent(req.getTenantId(), order.getChannelOrderId(), order.getOrderBizType(),
                    req.getOperatorAccountId(), req.getRequestAppId(),
                    TrackOpType.DELIVERY_CANCEL, null);
            return TmsResponse.succeed(null);
        } catch (Exception e) {
            log.error("#cancelDelivery error: {}", e.getMessage(), e);
            return TmsResponse.fail(e);
        }
    }

    @MethodLog(logRequest = true)
    @Override
    public TmsResponse<DeliveryOperationResultDto> callDelivery(CallDeliveryReq req) throws TException {
        Operator operator = new Operator(req.getOperatorAccountId(), req.getOperatorName());
        try {
            OrderInfo order = getOrder(req.getEmpowerOrderId());
            boolean executed = orderPlatformDeliveryOperateService.callDelivery(new CallDeliveryCmd(
                    req.getTenantId(), order.getPoiId(), order, req.getChannelId(), operator));
            if (!executed) {
                return TmsResponse.succeed(null);
            }

            sendOrderTrackEvent(req.getTenantId(), order.getChannelOrderId(), order.getOrderBizType(),
                    req.getOperatorAccountId(), req.getRequestAppId(), TrackOpType.DELIVERY_CALL, null);
            return TmsResponse.succeed(null);
        } catch (Exception e) {
            log.error("#callDelivery error: {}", e.getMessage(), e);
            return TmsResponse.fail(e);
        }
    }

    @MethodLog(logRequest = true)
    @Override
    public TmsResponse<DeliveryOperationResultDto> reportException(ReportExceptionReq req) throws TException {
        Operator operator = new Operator(req.getOperatorAccountId(), req.getOperatorName());
        try {
            OrderInfo order = getOrder(req.getEmpowerOrderId());
            List<String> pictureUrls = ObjectUtils.defaultIfNull(req.getPictureUrls(), Collections.emptyList());
            boolean executed = orderPlatformDeliveryOperateService.reportException(new ReportExceptionCmd(req.getTenantId(),
                    order.getPoiId(), order, req.getChannelId(), operator, pictureUrls));
            if (!executed) {
                return TmsResponse.succeed(null);
            }

            Map<String, Object> ext = new HashMap<>();
            ext.put("operatorName", req.getOperatorName());
            ext.put("pictureUrls", pictureUrls);

            sendOrderTrackEvent(req.getTenantId(), order.getChannelOrderId(), order.getOrderBizType(),
                    req.getOperatorAccountId(), req.getRequestAppId(), TrackOpType.DELIVERY_EXCEPTION_UPLOAD_MERCHANT, ext);
            return TmsResponse.succeed(null);
        } catch (Exception e) {
            log.error("#reportException error: {}", e.getMessage(), e);
            return TmsResponse.fail(e);
        }
    }

    @MethodLog(logRequest = true)
    @Override
    public TmsResponse<DeliveryOperationResultDto> auditException(AuditExceptionReq req) throws TException {
        Operator operator = new Operator(req.getOperatorAccountId(), req.getOperatorName());
        try {
            OrderInfo order = getOrder(req.getEmpowerOrderId());
            boolean executed = orderPlatformDeliveryOperateService.auditException(new AuditExceptionCmd(req.getTenantId(),
                    order.getPoiId(), order, req.getChannelId(), operator, req.getIsAgree()));
            if (!executed) {
                return TmsResponse.succeed(null);
            }

            Map<String, Object> ext = new HashMap<>();
            ext.put("operatorName", req.getOperatorName());
            TrackOpType trackOpType = req.getIsAgree() ? TrackOpType.DELIVERY_EXCEPTION_AUDIT_AGREE : TrackOpType.DELIVERY_EXCEPTION_AUDIT_DISAGREE;
            sendOrderTrackEvent(req.getTenantId(), order.getChannelOrderId(), order.getOrderBizType(),
                    req.getOperatorAccountId(), req.getRequestAppId(), trackOpType, ext);
            return TmsResponse.succeed(null);
        } catch (Exception e) {
            log.error("#auditException error: {}", e.getMessage(), e);
            return TmsResponse.fail(e);
        }
    }

    private OrderInfo getOrder(long empowerOrderId) {
        Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(empowerOrderId, false);
        if (orderInfoResult.isFail()) {
            log.error("#getOrderInfo error: {}, orderId: {}", orderInfoResult.getFailure(), empowerOrderId);
            throw new CommonRuntimeException("Fetch orderInfoResult error: " + empowerOrderId);
        }
        OrderInfo orderInfo = orderInfoResult.getInfo();
        return Objects.requireNonNull(orderInfo, "#getOrderInfo returns null, id: " + empowerOrderId);
    }

    private void sendOrderTrackEvent(long tenantId, String channelOrderId, int orderBizType,
            long operatorAccountId, String requestAppId, TrackOpType trackOpType, Map<String, Object> ext) {
        OrderTrackEvent event = new OrderTrackEvent(TrackSource.DELIVERY, trackOpType, operatorAccountId,
                OperationScene.DELIVERY_EXCEPTION, tenantId, channelOrderId,
                orderBizType, ext, requestAppId);
        orderTrackLogProducer.sendMessage(event);
    }

}
