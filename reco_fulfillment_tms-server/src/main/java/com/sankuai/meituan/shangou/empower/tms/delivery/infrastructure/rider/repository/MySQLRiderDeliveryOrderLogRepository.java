package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.rider.repository;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderLogDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderLogDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.*;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Repository
public class MySQLRiderDeliveryOrderLogRepository implements RiderDeliveryOrderLogRepository {

	@Resource
	private DeliveryOrderLogDOMapper deliveryOrderLogDOMapper;

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Map<Long, List<RiderDeliveryOrderLog<RiderDeliveryOrderRiderChangeInfo>>> getRiderChangeLogs(List<Long> deliveryOrderIds) {
		if (CollectionUtils.isEmpty(deliveryOrderIds)) {
			return Collections.emptyMap();
		}

		DeliveryOrderLogDOExample example = new DeliveryOrderLogDOExample();
		example.createCriteria()
				.andDeliveryOrderIdIn(deliveryOrderIds)
				.andChangeTypeEqualTo(DeliveryEventEnum.RIDER_CHANGE.getCode());
		List<DeliveryOrderLogDO> deliveryOrderLogDOS = deliveryOrderLogDOMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(deliveryOrderLogDOS)) {
			return Collections.emptyMap();
		}

		Map<Long, List<RiderDeliveryOrderLog<RiderDeliveryOrderRiderChangeInfo>>> result = Maps.newHashMap();
		for (DeliveryOrderLogDO deliveryOrderLogDO : deliveryOrderLogDOS) {
			result.computeIfAbsent(deliveryOrderLogDO.getDeliveryOrderId(), deliveryOrderId -> Lists.newArrayList());
			result.get(deliveryOrderLogDO.getDeliveryOrderId()).add(translate(deliveryOrderLogDO));
		}
		return result;
	}

	private RiderDeliveryOrderLog<RiderDeliveryOrderRiderChangeInfo> translate(DeliveryOrderLogDO deliveryOrderLogDO) {
		RiderDeliveryOrderRiderChangeInfo changeInfo = JsonUtil.fromJson(deliveryOrderLogDO.getChangeInfo(), RiderDeliveryOrderRiderChangeInfo.class);
		return new RiderDeliveryOrderLog<>(
				deliveryOrderLogDO.getId(), DeliveryEventEnum.valueOf(deliveryOrderLogDO.getChangeType()), changeInfo, deliveryOrderLogDO.getChangeTime());
	}



}
