package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryMonitorDomainService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryTimeOutCheckMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * 配送超时消息消费者
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/21
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class DeliveryTimeOutCheckMessageListener extends AbstractDeadLetterConsumer {

	@Resource
	private OrderSystemClient orderSystemClient;
	@Resource
	private DeliveryMonitorDomainService deliveryMonitorDomainService;

	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.DELIVERY_TIMEOUT_CHECK_CONSUMER;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
		log.info("开始消费配送超时检查消息：{}", mafkaMessage);
		DeliveryTimeOutCheckMessage message = translateMessage(mafkaMessage);
		if (message == null) {
			return CONSUME_SUCCESS;
		}

		try {
			//查询订单
			OrderKey orderKey = new OrderKey(message.getTenantId(), message.getStoreId(), message.getOrderId());
			Result<OrderInfo> orderInfoQueryResult = orderSystemClient.getOrderInfo(orderKey, false);
			if (orderInfoQueryResult.isFail()) {
				log.warn("查询订单失败[{}]", orderKey);
				return CONSUME_FAILURE;
			}

			//触发超值监控检查
			Optional<Failure> failure = deliveryMonitorDomainService.triggerDeliveryTimeOutMonitoring(orderInfoQueryResult.getInfo(), message.getDeliveryTimeOutCheckTypeEnum());
			if (failure.isPresent()) {
				return CONSUME_FAILURE;
			}

		} catch (Exception e) {
			log.error("Consume DeliveryTimeOutCheckMessage[{}] failed, need retry", message, e);
			return CONSUME_FAILURE;
		}

		return CONSUME_SUCCESS;
	}


	private DeliveryTimeOutCheckMessage translateMessage(MafkaMessage mafkaMessage) {
		try {
			DeliveryTimeOutCheckMessage message = translateMessage(mafkaMessage, DeliveryTimeOutCheckMessage.class);
			Preconditions.checkNotNull(message, "empty mafkaMessage");
			Preconditions.checkNotNull(message.getTenantId(), "tenantId is null");
			Preconditions.checkNotNull(message.getStoreId(), "storeId is null");
			Preconditions.checkNotNull(message.getOrderId(), "orderId is null");

			return message;
		} catch (Exception e) {
			log.error("解析配送超时检查消息失败:{}", mafkaMessage, e);
			return null;
		}
	}
}
