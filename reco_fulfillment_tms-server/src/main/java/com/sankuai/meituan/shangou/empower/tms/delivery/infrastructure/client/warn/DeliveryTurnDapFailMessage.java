package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.warn;

import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import lombok.AllArgsConstructor;

import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/21
 */
@AllArgsConstructor
public class DeliveryTurnDapFailMessage {

	private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

	private final OrderInfo orderInfo;

	@Override
	public String toString() {
		return "【严重】转三方发青云失败\n" +
				"【门店】" + orderInfo.getOrderKey().getStoreId() + "\n" +
				"【失败订单号】" + orderInfo.getChannelOrderId() + "\n";

	}

}
