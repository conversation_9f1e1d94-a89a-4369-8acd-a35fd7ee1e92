package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.dianping.squirrel.client.StoreKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.DeliveryCapacityLoadSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.DeliveryCapacityLoadRecordDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.IDeliveryCapacityLoadRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.DeliveryCapacityLoadRecordSequenceEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/1/11 16:33
 **/
@Service
public class SquirrelIDeliveryCapacityLoadRepository implements IDeliveryCapacityLoadRepository {
    @Resource
    private DeliveryCapacityLoadSquirrelOperationService deliveryCapacityLoadSquirrelOperationService;

    @Override
    public Map<Long, Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO>> getStoreDeliveryCapacityRecord(List<String> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Collections.emptyMap();
        }
        List<String> storeKeys = storeIds.stream().map(this::buildStoreKey).flatMap(Collection::stream).collect(Collectors.toList());

        Map<StoreKey, Optional<DeliveryCapacityLoadRecordDO>> storeKeyOptionalMap = deliveryCapacityLoadSquirrelOperationService.multiGet(storeKeys, DeliveryCapacityLoadRecordDO.class);

        Map<Long, Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO>> storeDeliveryCapacityLoadMap = new HashMap<>();

        for (Map.Entry<StoreKey, Optional<DeliveryCapacityLoadRecordDO>> entry: storeKeyOptionalMap.entrySet()) {
            //解析门店id和序列号
            Object[] params = entry.getKey().getParams();
            String keyStr = (String) params[0];
            Long storeId = Long.parseLong(keyStr.split("_")[0]);
            DeliveryCapacityLoadRecordSequenceEnum recordSequenceEnum = DeliveryCapacityLoadRecordSequenceEnum.enumOf(keyStr.split("_")[1]);

            if (!storeDeliveryCapacityLoadMap.containsKey(storeId)) {
                storeDeliveryCapacityLoadMap.put(storeId, new HashMap<>());
            }

            if (entry.getValue().isPresent()) {
                storeDeliveryCapacityLoadMap.get(storeId).put(recordSequenceEnum, entry.getValue().get());
            }
        }

        return storeDeliveryCapacityLoadMap;
    }

    @Override
    public void setStoreDeliveryCapacityRecord(Map<Long, Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO>> deliveringCapacityLoadRecords) {
        HashMap<String, DeliveryCapacityLoadRecordDO> storeKeyRecordDOMap = new HashMap<>();
        for (Map.Entry<Long, Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO>> entry : deliveringCapacityLoadRecords.entrySet()) {
            Long storeId = entry.getKey();
            Map<DeliveryCapacityLoadRecordSequenceEnum, DeliveryCapacityLoadRecordDO> singleStoreRecordDOMap = entry.getValue();
            Map<String, DeliveryCapacityLoadRecordDO> storeKeyMap = singleStoreRecordDOMap.entrySet().stream()
                    .collect(Collectors.toMap(entry2 -> new StringBuilder().append(storeId).append("_").append(entry2.getKey().getValue()).toString(), Map.Entry::getValue, (k1, k2) -> k2));
            storeKeyRecordDOMap.putAll(storeKeyMap);
        }

        deliveryCapacityLoadSquirrelOperationService.multiSet(storeKeyRecordDOMap);
    }


    private List<String> buildStoreKey(String poiId) {
        ArrayList<String> storeKeys = new ArrayList<>();
        storeKeys.add(new StringBuilder().append(poiId).append("_").append(DeliveryCapacityLoadRecordSequenceEnum.LATEST_RECORD.getValue()).toString());
        storeKeys.add(new StringBuilder().append(poiId).append("_").append(DeliveryCapacityLoadRecordSequenceEnum.THE_FIRST_BEFORE_LATEST_RECORD.getValue()).toString());
        storeKeys.add(new StringBuilder().append(poiId).append("_").append(DeliveryCapacityLoadRecordSequenceEnum.THE_SECOND_BEFORE_LATEST_RECORD.getValue()).toString());

        return storeKeys;
    }
}
