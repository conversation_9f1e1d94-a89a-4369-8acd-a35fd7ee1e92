package com.sankuai.meituan.shangou.empower.tms.delivery.enums;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */

@Slf4j
public enum OpenApiDistributeStatusEnum {

    CANCEL(0, "配送取消"),

    WAIT_FOR_ASSIGN_RIDER(1, "骑手待接单"),

    RIDER_ASSIGNED(2, "骑手已接单"),

    RIDER_TAKE_GOODS(3, "骑手已取货"),

    SENDING(4, "配送中"),

    RIDER_DELIVERED(5, "骑手配送完成"),

    DISTRIBUTE_EXCEPTION(6, "配送异常");
    @Getter
    private int value;

    private String desc;


    OpenApiDistributeStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static OpenApiDistributeStatusEnum enumOf(int value) {
        for (OpenApiDistributeStatusEnum openApiDistributeStatusEnum : OpenApiDistributeStatusEnum.values()) {
            if (openApiDistributeStatusEnum.value == value) {
                return openApiDistributeStatusEnum;
            }
        }
        return null;
    }

    public static OpenApiDistributeStatusEnum enumOf(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }

        Integer iValue = Integer.parseInt(value);

        return enumOf(iValue);
    }

    public static Optional<DeliveryStatusEnum> mapToDeliveryStatus(int value) {
        String openApiDeliveryStatusMappingStr = MccConfigUtils.queryOpenApiOrderChannelDeliveryStatusMapping();
        if (StringUtils.isEmpty(openApiDeliveryStatusMappingStr)) {
            log.error("openApiDeliveryStatusEnum mapToDeliveryStatus error, openApiDeliveryStatusMappingStr is empty");
            return Optional.empty();
        }

        Map<String, Integer> sourceMap;
        try {
            sourceMap = JSON.parseObject(openApiDeliveryStatusMappingStr, new TypeReference<Map<String, Integer>>() {}.getType());
        } catch (Exception e) {
            log.error("mapToDeliveryStatus error, parse openApiDeliveryStatusMappingStr error, openApiDeliveryStatusMappingStr: {}", openApiDeliveryStatusMappingStr);
            return Optional.empty();
        }

        Integer qnhDeliveryStatusCode = sourceMap.get(String.valueOf(value));
        if (Objects.isNull(qnhDeliveryStatusCode)) {
            log.error("mapToDeliveryStatus error, qnhDeliveryStatusCode is null");
            return Optional.empty();
        }

        return Optional.ofNullable(DeliveryStatusEnum.valueOf(qnhDeliveryStatusCode));
    }
}
