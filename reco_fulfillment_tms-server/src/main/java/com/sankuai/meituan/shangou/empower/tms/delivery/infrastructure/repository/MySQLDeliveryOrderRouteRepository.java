package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRouteRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderRouteMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.DeliveryOrderRouteExMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderRoute;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderRouteExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Slf4j
@Repository
@Order(Ordered.HIGHEST_PRECEDENCE)
public class MySQLDeliveryOrderRouteRepository implements DeliveryOrderRouteRepository {

    @Resource
    private DeliveryOrderRouteMapper deliveryOrderRouteMapper;

    @Resource
    private DeliveryOrderRouteExMapper deliveryOrderRouteExMapper;


    @Override
    public void saveDeliveryOrderRoute(DeliveryOrder deliveryOrder) {

        if(deliveryOrder == null){
            return;
        }
        try {
            DeliveryOrderRouteExample example = new DeliveryOrderRouteExample();
            example.createCriteria().andOrderIdEqualTo(deliveryOrder.getOrderId()).andFulfillmentOrderIdEqualTo(deliveryOrder.getFulfillmentOrderId());
            List<DeliveryOrderRoute> deliveryOrderRouteList = deliveryOrderRouteMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(deliveryOrderRouteList)){
                return;
            }
            DeliveryOrderRoute route = buildDeliveryOrder(deliveryOrder);
            deliveryOrderRouteMapper.insertSelective(route);
        }catch (Exception e){
            log.error("MySQLDeliveryOrderRouteRepository save error",e);
        }
    }
    @Override
    public void saveRiderDeliveryOrderRoute(RiderDeliveryOrder riderDeliveryOrder) {
        if(riderDeliveryOrder == null){
            return;
        }
        try {
            DeliveryOrderRouteExample example = new DeliveryOrderRouteExample();
            example.createCriteria().andOrderIdEqualTo(riderDeliveryOrder.getCustomerOrderKey().getOrderId());
            List<DeliveryOrderRoute> deliveryOrderRouteList = deliveryOrderRouteMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(deliveryOrderRouteList)){
                return;
            }
            DeliveryOrderRoute route = buildRiderDeliveryOrder(riderDeliveryOrder);
            deliveryOrderRouteMapper.insertSelective(route);
        }catch (Exception e){
            log.error("MySQLDeliveryOrderRouteRepository save error",e);
        }
    }

    @Override
    public DeliveryOrderRoute queryOrderRouteByFulfillOrderId(Long fulfillOrderId) {
        if(fulfillOrderId == null){
            return null;
        }
        try {
            DeliveryOrderRouteExample example = new DeliveryOrderRouteExample();
            example.createCriteria().andFulfillmentOrderIdEqualTo(fulfillOrderId);
            List<DeliveryOrderRoute> deliveryOrderRouteList = deliveryOrderRouteMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(deliveryOrderRouteList)){
                return null;
            }
            return deliveryOrderRouteList.get(0);
        }catch (Exception e){
            log.error("queryOrderRoute error",e);
        }
        return null;
    }

    @Override
    public List<DeliveryOrderRoute> queryOrderRouteByOrderId(Long orderId) {
        if(orderId == null){
            return Collections.emptyList();
        }
        try {
            DeliveryOrderRouteExample example = new DeliveryOrderRouteExample();
            example.createCriteria().andOrderIdEqualTo(orderId);
            example.or().andFulfillmentOrderIdEqualTo(orderId);
            List<DeliveryOrderRoute> deliveryOrderRouteList = deliveryOrderRouteMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(deliveryOrderRouteList)){
                return Collections.emptyList();
            }
            return deliveryOrderRouteList;
        }catch (Exception e){
            log.error("queryOrderRoute error",e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<DeliveryOrderRoute> queryOrderRouteByOrderIdList(List<Long> orderIdList) {
        if(CollectionUtils.isEmpty(orderIdList)){
            return Collections.emptyList();
        }
        try {
            DeliveryOrderRouteExample example = new DeliveryOrderRouteExample();
            example.createCriteria().andOrderIdIn(orderIdList);
            List<DeliveryOrderRoute> deliveryOrderRouteList = deliveryOrderRouteMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(deliveryOrderRouteList)){
                return Collections.emptyList();
            }
            return deliveryOrderRouteList;
        }catch (Exception e){
            log.error("queryOrderRouteByOrderIdList error",e);
        }
        return Collections.emptyList();
    }

    @Override
    public DeliveryOrderRoute queryOrderRouteByOrderIdWithMax(Long orderId) {
        List<DeliveryOrderRoute> routeList = queryOrderRouteByOrderId(orderId);
        if(CollectionUtils.isEmpty(routeList)){
            return null;
        }
        Optional<DeliveryOrderRoute> routeOptional = routeList.stream().max(Comparator.comparing(DeliveryOrderRoute::getCreateTime));
        return routeOptional.orElse(null);
    }

    @Override
    public List<DeliveryOrderRoute> queryOrderRouteByOrderIdOrFulfill(Long orderId){
        if(orderId == null){
            return Collections.emptyList();
        }
        try {
            DeliveryOrderRouteExample example = new DeliveryOrderRouteExample();
            example.createCriteria().andOrderIdEqualTo(orderId);
            example.or().andFulfillmentOrderIdEqualTo(orderId);
            List<DeliveryOrderRoute> deliveryOrderRouteList = deliveryOrderRouteMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(deliveryOrderRouteList)){
                return Collections.emptyList();
            }
            return deliveryOrderRouteList;
        }catch (Exception e){
            log.error("queryOrderRoute error",e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<DeliveryOrderRoute> queryOrderRouteByOrderIdOrFulfillSlave(Long orderId){
        if(orderId == null){
            return Collections.emptyList();
        }
        try {
            DeliveryOrderRouteExample example = new DeliveryOrderRouteExample();
            example.createCriteria().andOrderIdEqualTo(orderId);
            example.or().andFulfillmentOrderIdEqualTo(orderId);
            List<DeliveryOrderRoute> deliveryOrderRouteList = deliveryOrderRouteExMapper.selectByExampleSlave(example);
            if(CollectionUtils.isEmpty(deliveryOrderRouteList)){
                return Collections.emptyList();
            }
            return deliveryOrderRouteList;
        }catch (Exception e){
            log.error("queryOrderRoute error",e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<DeliveryOrderRoute> queryOrderRouteByOrderIdListSlave(List<Long> orderIdList) {
        if(CollectionUtils.isEmpty(orderIdList)){
            return Collections.emptyList();
        }
        try {
            DeliveryOrderRouteExample example = new DeliveryOrderRouteExample();
            example.createCriteria().andOrderIdIn(orderIdList);
            List<DeliveryOrderRoute> deliveryOrderRouteList = deliveryOrderRouteExMapper.selectByExampleSlave(example);
            if(CollectionUtils.isEmpty(deliveryOrderRouteList)){
                return Collections.emptyList();
            }
            return deliveryOrderRouteList;
        }catch (Exception e){
            log.error("queryOrderRouteByOrderIdList error",e);
        }
        return Collections.emptyList();
    }

    private DeliveryOrderRoute buildDeliveryOrder(DeliveryOrder deliveryOrder){
        DeliveryOrderRoute route = new DeliveryOrderRoute();
        route.setOrderId(deliveryOrder.getOrderId());
        route.setFulfillmentOrderId(deliveryOrder.getFulfillmentOrderId());
        route.setTenantId(deliveryOrder.getTenantId());
        route.setOfflineStoreId(deliveryOrder.getStoreId());
        return route;
    }

    private DeliveryOrderRoute buildRiderDeliveryOrder(RiderDeliveryOrder riderDeliveryOrder){
        DeliveryOrderRoute route = new DeliveryOrderRoute();
        route.setOrderId(riderDeliveryOrder.getCustomerOrderKey().getOrderId());
        route.setFulfillmentOrderId(riderDeliveryOrder.getCustomerOrderKey().getOrderId());
        route.setTenantId(riderDeliveryOrder.getTenantId());
        route.setOfflineStoreId(riderDeliveryOrder.getStoreId());
        return route;
    }
}
