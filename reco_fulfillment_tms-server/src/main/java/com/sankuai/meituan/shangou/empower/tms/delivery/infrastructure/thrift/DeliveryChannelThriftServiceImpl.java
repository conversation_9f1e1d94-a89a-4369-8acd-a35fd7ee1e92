package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.DeliveryChannelThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.request.DeliveryChannelBatchQueryByCarrierCodeRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.request.DeliveryChannelQueryByCarrierCodeRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.request.DeliveryChannelQueryByLogisticMarkRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.response.DeliveryChannelBatchQueryResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.response.DeliveryChannelQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeliveryChannelThriftServiceImpl implements DeliveryChannelThriftService {

    @Autowired
    private DeliveryChannelApplicationService deliveryChannelApplicationService;

    @Override
    public DeliveryChannelQueryResponse queryDeliveryChannelByCarrierCode(DeliveryChannelQueryByCarrierCodeRequest request) {
        try {
            request.validate();
            Integer carrierCode = request.getCarrierCode();
            DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(carrierCode);
            return buildSuccessResponse(deliveryChannel);
        } catch (Exception e) {
            log.error("DeliveryChannelThriftServiceImpl queryDeliveryChannelByCarrierCode error", e);
            return buildFailedResponse();
        }
    }

    @Override
    public DeliveryChannelQueryResponse queryDeliveryChannelByLogisticMark(DeliveryChannelQueryByLogisticMarkRequest request) {
        try {
            request.validate();
            Integer deliveryPlatFormCode = request.getDeliveryPlatFormCode();
            Integer orderChannelCode = request.getOrderChannelCode();
            String logisticMark = request.getLogisticMark();

            DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByLogisticMark(deliveryPlatFormCode, orderChannelCode, logisticMark);
            return buildSuccessResponse(deliveryChannel);
        } catch (Exception e) {
            log.error("DeliveryChannelThriftServiceImpl queryDeliveryChannelByLogisticMark error", e);
            return buildFailedResponse();
        }
    }

    @Override
    public DeliveryChannelBatchQueryResponse batchQueryDeliveryChannelByCarrierCodeList(DeliveryChannelBatchQueryByCarrierCodeRequest request) {
        try {
            request.validate();
            Set<Integer> carrierCodeSet = request.getCarrierCodeSet();
            if (CollectionUtils.isEmpty(carrierCodeSet)) {
                return buildSuccessResponse(Collections.emptyList());
            }

            List<DeliveryChannel> deliveryChannelList = deliveryChannelApplicationService.batchQueryDeliveryChannelByCarrierCodeSet(carrierCodeSet);
            return buildSuccessResponse(CollectionUtils.isNotEmpty(deliveryChannelList) ? deliveryChannelList : Collections.emptyList());
        } catch (Exception e) {
            log.error("DeliveryChannelThriftServiceImpl batchQueryDeliveryChannelByCarrierCodeList error", e);
            return buildBatchQueryFailedResponse();
        }
    }

    private DeliveryChannelQueryResponse buildSuccessResponse(DeliveryChannel deliveryChannel) {
        DeliveryChannelQueryResponse response = new DeliveryChannelQueryResponse();
        response.setStatus(Status.SUCCESS);
        response.setDeliveryChannelDto(translate(deliveryChannel));
        return response;
    }

    private DeliveryChannelBatchQueryResponse buildSuccessResponse(List<DeliveryChannel> deliveryChannelList) {
        DeliveryChannelBatchQueryResponse response = new DeliveryChannelBatchQueryResponse();
        response.setStatus(Status.SUCCESS);
        List<DeliveryChannelDto> deliveryChannelDtoList = deliveryChannelList.stream().map(this::translate).collect(Collectors.toList());
        response.setDeliveryChannelDtoList(deliveryChannelDtoList);
        return response;
    }

    private DeliveryChannelQueryResponse buildFailedResponse() {
        DeliveryChannelQueryResponse response = new DeliveryChannelQueryResponse();
        response.setStatus(new Status(ResponseCodeEnum.FAILED.getValue(), "查询配送渠道信息失败"));
        response.setDeliveryChannelDto(null);
        return response;
    }

    private DeliveryChannelQueryResponse buildFailedResponse(String msg) {
        DeliveryChannelQueryResponse response = new DeliveryChannelQueryResponse();
        response.setStatus(new Status(ResponseCodeEnum.FAILED.getValue(), msg));
        response.setDeliveryChannelDto(null);
        return response;
    }

    private DeliveryChannelBatchQueryResponse buildBatchQueryFailedResponse() {
        DeliveryChannelBatchQueryResponse response = new DeliveryChannelBatchQueryResponse();
        response.setStatus(new Status(ResponseCodeEnum.FAILED.getValue(), "批量查询配送渠道信息失败"));
        response.setDeliveryChannelDtoList(null);
        return response;
    }

    private DeliveryChannelDto translate(DeliveryChannel deliveryChannel) {
        DeliveryChannelDto dto = new DeliveryChannelDto();
        dto.setLogisticMark(deliveryChannel.getLogisticMark());
        dto.setDeliveryPlatFormCode(deliveryChannel.getDeliveryPlatFormCode());
        dto.setCarrierCode(deliveryChannel.getCarrierCode());
        dto.setCarrierName(deliveryChannel.getCarrierName());
        dto.setOrderChannelCode(deliveryChannel.getOrderChannelCode());
        return dto;
    }
}
