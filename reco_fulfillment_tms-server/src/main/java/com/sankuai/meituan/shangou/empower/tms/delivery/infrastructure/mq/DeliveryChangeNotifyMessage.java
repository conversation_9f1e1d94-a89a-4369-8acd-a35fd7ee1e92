package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.DeliveryCancelTypeEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.SpringContextUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationDetail;
import lombok.*;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryChangeNotifyMessage {
	/**
	 * 配送变更消息类型
	 * <p>
	 * UNKNOWN(0, "未知")
	 * DELIVERY_STATUS(1, "配送状态")
	 * RIDER_LOCATION(5, "骑手位置")
	 */
	private final Integer deliveryChangeType = 1;
	/**
	 * 租户id
	 */
	private Long tenantId;
	/**
	 * 门店id
	 */
	private Long shopId;
	/**
	 * 订单业务类型
	 * @see com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType
	 */
	private Integer orderBizType;
	/**
	 * 闪购赋能内部订单号
	 */
	private Long orderId;
	/**
	 * 渠道订单号
	 */
	private String viewOrderId;
	/**
	 * 订单来源
	 *
	 * @see OrderSourceEnum
	 */
	private Integer orderSource;
	/**
	 * 配送状态
	 */
	private Integer deliveryStatus;
	/**
	 * 骑手姓名
	 */
	private String riderName;
	/**
	 * 骑手电话
	 */
	private String riderPhone;
	/**
	 * 骑手位置经度
	 */
	private String riderCurrentLongitude;
	/**
	 * 骑手位置纬度
	 */
	private String riderCurrentLatitude;
	/**
	 * 骑手位置坐标类型
	 */
	private Integer coordinateType;
	/**
	 * 配送渠道ID
	 */
	private Integer deliveryChannelId;
	/**
	 * 平台配送类型id
	 */
	private Integer platformDistributeTypeId;
	/**
	 * 渠道运单号
	 */
	private String channelDeliveryId;
	/**
	 * 配送取消原因类型
	 */
	public Integer cancelReasonType = 0;
	/**
	 * 运单取消原因
	 */
	public String cancelReasonDescription = StringUtils.EMPTY;
	/**
	 * 配送拒单原因类型
	 */
	public Integer rejectReasonType = 0;
	/**
	 * 配送拒单原因
	 */
	public String rejectReasonDescription = StringUtils.EMPTY;
	/**
	 * 更新时间
	 */
	private Long updateTime;

	/**
	 * 闪购内部运单id
	 */
	private Long deliveryOrderId;

	/**
	 * 配送费用，单位：元
	 */
	private BigDecimal deliveryFee;

	/**
	 * 配送距离
	 */
	private Long distance;

	/**
	 * 预计送达时间
	 * 格式：时间戳
	 */
	private Long estimatedDeliveryEndTime;

	private Integer deliveryCount;

	/**
	 * 小费，单位：元
	 */
	private BigDecimal tipAmount;

	/**
	 * 配送平台
	 */
	private Integer deliveryPlatformCode;

	/**
	 * 配送取消原因code
	 */
	private Integer cancelReasonCode;

	/**
	 * 骑手账号id
	 */
	private Long riderAccountId;

	public DeliveryChangeNotifyMessage(DeliveryOrder deliveryOrder, CoordinatePoint riderLocation, LocalDateTime updateTime, Integer deliveryPlatformCode) {
		this.tenantId = deliveryOrder.getTenantId();
		this.shopId = deliveryOrder.getStoreId();
		this.viewOrderId = deliveryOrder.getChannelOrderId();
		this.orderId = deliveryOrder.getOrderKey().getOrderId();
		this.orderSource = deliveryOrder.getOrderSource();
		this.orderBizType = deliveryOrder.getOrderBizType();
		this.channelDeliveryId = deliveryOrder.getChannelDeliveryId();
		deliveryChannelHandle(deliveryOrder.getDeliveryChannel());
		this.deliveryOrderId = deliveryOrder.getId();
		this.deliveryPlatformCode = deliveryPlatformCode;
		if (deliveryOrder.getRiderInfo() != null) {
			this.riderName = deliveryOrder.getRiderInfo().getRiderName();
			this.riderPhone = deliveryOrder.getRiderInfo().getRiderPhone();
			if (riderLocation != null) {
				this.riderCurrentLongitude = riderLocation.getLongitude();
				this.riderCurrentLatitude = riderLocation.getLatitude();
				this.coordinateType = CoordinateTypeEnum.MARS.getCode();
			}
		}
		//状态映射，兼容原老逻辑，待迁移配送查询后可直接删除
		switch (deliveryOrder.getStatus()) {
			case DELIVERY_FAILED:
				if (deliveryOrder.getExceptionType() == DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER ||
						deliveryOrder.getExceptionType() == DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_SYSTEM ||
						deliveryOrder.getExceptionType() == DeliveryExceptionTypeEnum.UNKNOWN) {
					this.deliveryStatus = DeliveryStatusEnum.DELIVERY_CANCELLED.getCode();
					this.cancelReasonType = DeliveryCancelTypeEnum.SYSTEM_CANCEL.getValue();
				} else {
					this.deliveryStatus = DeliveryStatusEnum.DELIVERY_FAILED.getCode();
					this.cancelReasonType = DeliveryCancelTypeEnum.SYSTEM_CANCEL_FOR_OTHERS.getValue();
				}
				this.cancelReasonDescription = deliveryOrder.getExceptionDescription();
				break;
			case DELIVERY_CANCELLED:
				this.deliveryStatus = DeliveryStatusEnum.DELIVERY_CANCELLED.getCode();
				this.cancelReasonDescription = deliveryOrder.getExceptionDescription();
				if (deliveryOrder.getExceptionType() != DeliveryExceptionTypeEnum.NO_EXCEPTION) {
					this.cancelReasonType = DeliveryCancelTypeEnum.SYSTEM_CANCEL.getValue();
				} else {
					this.cancelReasonType = DeliveryCancelTypeEnum.CLIENT_CANCEL.getValue();
				}
				break;
			case DELIVERY_REJECTED:
				this.deliveryStatus = DeliveryStatusEnum.DELIVERY_REJECTED.getCode();
				this.rejectReasonDescription = deliveryOrder.getExceptionDescription();
				break;
			default:
				this.deliveryStatus = deliveryOrder.getStatus().getCode();
		}
		this.updateTime = TimeUtil.toMilliSeconds(updateTime);
		this.distance = deliveryOrder.getDistance();
		this.estimatedDeliveryEndTime = TimeUtil.toMilliSeconds(deliveryOrder.getEstimatedDeliveryEndTime());
		this.deliveryFee = deliveryOrder.getDeliveryFee();
		this.deliveryCount = deliveryOrder.getDeliveryCount();
		this.tipAmount = deliveryOrder.getTipAmount();
		this.cancelReasonCode = deliveryOrder.getDeliveryCancelCode();
	}

	public DeliveryChangeNotifyMessage(RiderDeliveryOrder deliveryOrder, LocalDateTime changeTime, RiderLocationDetail riderLocationDetail) {
		this.tenantId = deliveryOrder.getTenantId();
		this.shopId = deliveryOrder.getStoreId();
		this.viewOrderId = deliveryOrder.getCustomerOrderKey().getChannelOrderId();
		this.orderId = deliveryOrder.getCustomerOrderKey().getOrderId();
		this.orderSource = deliveryOrder.getCustomerOrderKey().getOrderSource();
		this.orderBizType = deliveryOrder.getCustomerOrderKey().getOrderBizType();
		this.channelDeliveryId = String.valueOf(deliveryOrder.getCustomerOrderKey().getOrderId());
		this.deliveryChannelId = DeliveryChannelEnum.MERCHANT_DELIVERY.getCode();
		this.deliveryOrderId = deliveryOrder.getId();
		this.deliveryPlatformCode = DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode();
		if (deliveryOrder.getRiderInfo() != null) {
			this.riderAccountId = deliveryOrder.getRiderInfo().getRiderAccountId();
			this.riderName = deliveryOrder.getRiderInfo().getRiderName();
			this.riderPhone = deliveryOrder.getRiderInfo().getRiderPhone();
		}
		this.deliveryStatus = deliveryOrder.getStatus().getCode();
		this.updateTime = TimeUtil.toMilliSeconds(changeTime);
		if(riderLocationDetail!=null){
			this.riderCurrentLatitude=riderLocationDetail.getLatitude();
			this.riderCurrentLongitude=riderLocationDetail.getLongitude();
		}
	}

	private void deliveryChannelHandle(Integer deliveryChannel) {
		if (deliveryChannel == null) {
			this.deliveryChannelId = deliveryChannel;
			return;
		}
		DeliveryChannelApplicationService deliveryChannelApplicationService = SpringContextUtils.getBean(DeliveryChannelApplicationService.class);
		DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryChannel);
		if (Objects.equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode(), deliveryChannelDto.getDeliveryPlatFormCode())) {
			this.deliveryChannelId = null;
			Map<String, Integer> typeMappingMap = MccConfigUtils.orderChannelDistributeTypeMappingMap();
			String orderChannelCode = String.valueOf(deliveryChannelDto.getOrderChannelCode());
			if (typeMappingMap.containsKey(orderChannelCode)) {
				this.platformDistributeTypeId = typeMappingMap.get(orderChannelCode);
				return;
			}
        }
		this.deliveryChannelId = deliveryChannel;
	}


}
