package com.sankuai.meituan.shangou.empower.tms.delivery.enums;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;

import java.util.Objects;
import java.util.Optional;

public enum DapDeliveryStatusEnum {

    CREATE(0,"创建配送订单"),

    DELIVERY_FAILED(9, "配送失败"),
    WAIT_TO_RECEIVE(10, "已发配送"),
    WAIT_TO_TAKE(20, "骑手已接单"),
    RIDER_ARRIVED_SHOP(25, "已到店"),
    MERCHANT_DELIVERING(30, "已取货"),
    DELIVERY_DONE(50, "已送达"),
    DELIVERY_CANCELLED(99, "已取消"),
    ;
    private int code;

    private String msg;

    DapDeliveryStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static DapDeliveryStatusEnum enumOf(int value) {
        for (DapDeliveryStatusEnum each : values()) {
            if (each.getCode() == value) {
                return each;
            }
        }

        return null;
    }



    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static Optional<DeliveryStatusEnum> mapToDeliveryStatus(int value) {
        DapDeliveryStatusEnum dapDeliveryStatusEnum = enumOf(value);
        if (Objects.isNull(dapDeliveryStatusEnum)) {
            return Optional.empty();
        }
        switch (dapDeliveryStatusEnum) {
            case CREATE:
                return Optional.of(DeliveryStatusEnum.DELIVERY_LAUNCHED);
            case WAIT_TO_RECEIVE:
                return Optional.of(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER);
            case WAIT_TO_TAKE:
                return Optional.of(DeliveryStatusEnum.RIDER_ASSIGNED);
            case RIDER_ARRIVED_SHOP:
                return Optional.of(DeliveryStatusEnum.RIDER_ARRIVED_SHOP);
            case MERCHANT_DELIVERING:
                return Optional.of(DeliveryStatusEnum.RIDER_TAKEN_GOODS);
            case DELIVERY_DONE:
                return Optional.of(DeliveryStatusEnum.DELIVERY_DONE);
            case DELIVERY_CANCELLED:
                return Optional.of(DeliveryStatusEnum.DELIVERY_CANCELLED);
            case DELIVERY_FAILED:
                return Optional.of(DeliveryStatusEnum.DELIVERY_FAILED);
            default:
                return Optional.empty();
        }
    }

}
