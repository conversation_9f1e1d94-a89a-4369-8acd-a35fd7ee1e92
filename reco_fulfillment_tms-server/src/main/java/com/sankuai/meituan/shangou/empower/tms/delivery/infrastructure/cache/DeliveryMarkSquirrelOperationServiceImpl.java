package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache;

import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryMarkSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-09-12
 */
@Slf4j
@Service
public class DeliveryMarkSquirrelOperationServiceImpl extends SquirrelOperateService implements DeliveryMarkSquirrelOperationService {

    private static final String CATEGORY_NAME = "delivery_mark";

    @Autowired
    @Qualifier("redisSgNewSupplyOfc")
    protected RedisStoreClient redisNewSupplyClient;

    @Override
    protected RedisStoreClient getRedisClient() {
        return redisNewSupplyClient;
    }

    @Override
    public String getCategoryName() {
        return CATEGORY_NAME;
    }

    @Override
    public void saveDeliveryMark(DeliveryOrder deliveryOrder) {
        saveDeliveryMark(deliveryOrder.getOrderBizType(), deliveryOrder.getChannelOrderId(), deliveryOrder.getDeliveryChannel());
    }

    @Override
    public void saveDeliveryMark(Integer orderBizType, String channelOrderId, Integer deliveryChannel) {
        try {
            super.set(getKey(orderBizType, channelOrderId), deliveryChannel);
        } catch (Exception e) {
            log.warn("delivery_mark save error; channelOrderId={} \n", channelOrderId, e);
        }
    }

    @Override
    public Optional<Integer> get(Integer orderBizType, String channelOrderId) {
        return super.get(getKey(orderBizType, channelOrderId), Integer.class);
    }

    private String getKey(Integer orderBizType, String channelOrderId) {
        return String.format("%s_%s", orderBizType, channelOrderId);
    }

}
