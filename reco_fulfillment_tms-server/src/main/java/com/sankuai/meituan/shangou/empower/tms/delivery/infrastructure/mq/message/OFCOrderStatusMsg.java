package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message;

import com.sankuai.qnh.ofc.ofw.common.consts.FulfillmentOrderMessageTypeEnum;
import lombok.Data;

@Data
public class OFCOrderStatusMsg {

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 订单仓id
     */
    private Long warehouseId;

    /**
     * 消息触发时间
     */
    private Long timeStamp;

    /**
     * 履约消息内容
     */
    private String messageContent;

    /**
     * 履约仓id
     */
    private Long fulfillmentWarehouseId;
    /**
     * 计划单id
     */
    private Long orderId;
    /**
     * 履约单id
     */
    private Long fulfillmentOrderId;
    /**
     * 渠道订单id
     */
    private String channelOrderId;
    /**
     * 赋能订单id
     */
    private String unifyOrderId;
    /**
     * 订单对应的牵牛花门店id
     */
    private Long orderShopId;

    /**
     * 订单OMS的orderBizType
     */
    private Integer orderSource;

    /**
     * 对应OMS的orderSource
     */
    private Integer sourceType;

    /**
     * 补单标识
     */
    private Boolean compensate;

    /**
     * 配送状态
     */
    private Integer distributeStatus;
    /**
     * 补单情况实时获取的、线上最新的配送状态
     */
    private Integer onlineDistributeStatus;
    /**
     * 标记qnh侧jddj/elm订单延迟下发
     */
    private boolean delayPush;

    //配送状态
    private int deliveryStatus;

    private Integer orderOTOCategory;

    /**
     * 订单标识
     */
    private String orderLabel;

    /**
     * 转单消息
     */
    private FulfillmentOrderTransferMessage transferMessage;

    /**
     * 新字段履行消息类型
     *
     * @see FulfillmentOrderMessageTypeEnum
     */
    private Integer messageType;
    /**
     * oms订单状态
     */
    private Integer orderStatus;

    private Integer sourceStatus;

}
