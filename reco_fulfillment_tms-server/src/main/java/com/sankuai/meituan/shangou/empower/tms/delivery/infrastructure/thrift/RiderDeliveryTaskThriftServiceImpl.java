package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.enums.DeliveryTaskTypeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.RiderDeliveryTaskThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.req.TaskDetailReq;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.req.TaskListReq;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.req.TaskSubmitReq;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.resp.TaskDetailResp;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.resp.TaskListResp;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.application.DeliveryTaskAppService;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.dto.TaskReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.ExcelTaskConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.sankuai.meituan.shangou.empower.tms.delivery.task.common.ThriftResponseHelper.buildFailedStatusAndPrintLog;

/**
 * <AUTHOR>
 * @since 7/5/21 Time: 7:10 PM
 */
@Slf4j
@Component
public class RiderDeliveryTaskThriftServiceImpl implements RiderDeliveryTaskThriftService {

    @Autowired
    private DeliveryTaskAppService deliveryTaskAppService;

    @Autowired
    private ExcelTaskConfig excelTaskConfig;

    @Override
    public Status submit(TaskSubmitReq req) {
        try {
            validate(req);
            TaskReq<String> taskReq = ofTaskSubmitReq(req);
            return deliveryTaskAppService.submit(taskReq);
        } catch (Exception e) {
            return buildFailedStatusAndPrintLog("AsyncTaskThriftService.submit", JsonUtil.toJson(req), e);
        }
    }

    @Override
    public TaskListResp queryListPageable(TaskListReq req) {
        try {
            return deliveryTaskAppService.queryListPageable(req);
        } catch (Exception e) {
            Status status = buildFailedStatusAndPrintLog("AsyncTaskThriftService.queryListPageable", JsonUtil.toJson(req), e);
            return new TaskListResp(status, null, null);
        }
    }

    @Override
    public TaskDetailResp queryDetail(TaskDetailReq req) {
        try {
            return deliveryTaskAppService.queryDetail(req);
        } catch (Exception e) {
            Status status = buildFailedStatusAndPrintLog("AsyncTaskThriftService.queryDetail", JsonUtil.toJson(req), e);
            return new TaskDetailResp(status, null);
        }
    }

    private TaskReq<String> ofTaskSubmitReq(TaskSubmitReq req) {
        TaskReq<String> taskReq = new TaskReq<>();

        DeliveryTaskTypeEnum taskType = DeliveryTaskTypeEnum.ofCode(req.getTaskType());
        if (taskType != null) {
            taskReq.setTypeCode(taskType.getCode());
            taskReq.setTypeName(taskType.getName());
        } else {
            // excelTaskConfig是为了test用例的兼容
            Preconditions.checkNotNull(excelTaskConfig.getByTypeCode(req.getTaskType()), "无法识别的任务号：" + req.getTaskType());
            taskReq.setTypeCode(req.getTaskType());
            taskReq.setTypeName("测试任务" + req.getTaskType());
        }

        taskReq.setTenantId(req.getTenantId());
        taskReq.setOperatorId(req.getOperatorId());
        taskReq.setOperatorAccount(req.getOperatorAccount());
        taskReq.setParam(req.getParameter());

        return taskReq;
    }

    private void validate(TaskSubmitReq req) {
        Preconditions.checkNotNull(req, "入参为空");

        Preconditions.checkNotNull(req.getTenantId(), "租户id不能为空");
        Preconditions.checkNotNull(req.getTaskType(), "任务类型不能为空");
        Preconditions.checkNotNull(req.getOperatorId(), "操作员id不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getOperatorAccount()), "操作员账号不能为空");
    }
}
