package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/4/27 15:39
 * desc 保时洁官方规定的接入消息结构体 参考文档:https://km.sankuai.com/page/237744136
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreditAuditPicMessage {
    /**
     * 业务方id
     */
    private String type;

    /**
     * 内容在平台上产生的时间
     */
    private String datetime;

    /**
     * 内容生产者发布内容时，内容生产者的ip
     */
    private String userIP;

    /**
     * 需要审核的图片基本信息 单条送审消息不可超过10张图片
     */
    private List<Map<String,Object>> picInfo;

    /**
     * 每条送审内容事件的唯一标识，全局唯一，业务方负责维护
     */
    private String transid;

    /**
     * 内容的生产（编写、上传）来源方
     */
    private int dataSource;

    /**
     * 生成内容的角色ID
     */
    private long userId;

    /**
     * userId 的体系
     */
    private int source;

    /**
     * 商户ID
     */
    private long shopId;

    /**
     * shopId 对应的平台体系
     */
    private int shopSource;

    /**
     * 商户的后台一级类目
     */
    private int cat0id;

    /**
     * 商户的后台二级类目
     */
    private int cat1id;

    /**
     * （生产或展示内容的）城市ID
     */
    private int cityId;

    /**
     * cityId 对应的平台
     */
    private int citySource;

    /**
     * 点评的移动设备id
     */
    private String dpid;

    /**
     * 美团的移动设备id
     */
    private String uuid;

    /**
     * 移动设备指纹 device fingerprint
     */
    private String cx;

    /**
     * 业务方透传数据，内容审核侧不关心
     */
    private String bizData;

    /**
     * Json格式，业务定制化策略所需的字段，以kv形式存放在该字段中
     */
    private String extend;


    @Data
    public static class PicKey {
        private long picId;

        private String picUrl;

        private String picTitle;
    }
}
