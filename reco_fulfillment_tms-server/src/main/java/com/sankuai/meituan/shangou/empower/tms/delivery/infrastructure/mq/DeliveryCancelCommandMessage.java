package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/4
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryCancelCommandMessage {

	private Long tenantId;

	private Long storeId;

	private Long deliveryId;

	public DeliveryCancelCommandMessage(DeliveryOrder deliveryOrder) {
		Preconditions.checkNotNull(deliveryOrder, "deliveryOrder is null");

		this.tenantId = deliveryOrder.getTenantId();
		this.storeId = deliveryOrder.getStoreId();
		this.deliveryId = deliveryOrder.getId();
	}
}
