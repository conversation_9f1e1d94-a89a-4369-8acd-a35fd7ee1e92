package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.DeliveryCancelResultCallbackCmd;
import lombok.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DeliveryCancelCallbackInfo extends DeliveryUnifiedCallbackInfo {

	private Long storeId;

	private Long orderId;

	private String channelDeliveryId;

	private boolean cancelSuccess;

	private String cancelFailReason;

	public DeliveryCancelResultCallbackCmd toCmd(){
		return new DeliveryCancelResultCallbackCmd(storeId, orderId, channelDeliveryId, cancelSuccess, cancelFailReason);
	}
}
