package com.sankuai.meituan.shangou.empower.tms.delivery.utils;

import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.banma.deliverywaybill.order.ldp.client.enums.DeliveryStrategyEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.AggDeliveryPlatformAppConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.LaunchRuleInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.AggDeliveryPlatformAppConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TmsMccUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TAggDeliveryPlatformConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TLaunchRule;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TOuterDeliveryConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryBaseException;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.wrapper.TenantChannelThriftServiceWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.relation.DeliveryChannelInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.aggr.AggrDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.SpringContextUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by liuyonggao on 2021/1/7.
 */
@Slf4j
public class StoreConfigTranslate {

	public static void fillConfigAttr(DeliveryPoi deliveryPoi, TStoreConfig tStoreConfig) {
		try {
			tStoreConfig.setAutoLaunchDelayMinutes(deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getDelayMinutes());
			tStoreConfig.setAutoLaunchPoint(deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint().getCode());
			tStoreConfig.setBookingOrderAutoLaunchMinutes(deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getConfigMinutes());
			tStoreConfig.setBookingOrderAutoLaunchPoint(deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getLaunchPoint().getCode());
			tStoreConfig.setContactPhone(deliveryPoi.getContactPhone());
			tStoreConfig.setLaunchPattern(deliveryPoi.getDeliveryLaunchType().getCode());
			tStoreConfig.setLaunchRule((
					deliveryPoi instanceof AggrDeliveryPoi) ?
					((AggrDeliveryPoi) deliveryPoi).getAutoLaunchStrategyId() : AggrDeliveryPoi.LOWEST_PRICE_FIRST
			);
		}catch (Exception e){
			log.error("fillConfigAttr error deliveryPoi:{}",deliveryPoi,e);
		}
		tStoreConfig.setStoreId(deliveryPoi.getStoreId());
		tStoreConfig.setTenantId(deliveryPoi.getTenantId());
	}
	public static List<TAggDeliveryPlatformConfig> fillAggPlatformConfig(List<DeliveryPoi> deliveryPoiList, List<Integer> platformCodes, Long tenantId) {
		TenantChannelThriftServiceWrapper tenantChannelThriftServiceWrapper =
				Optional.of(SpringContextUtils.getBean(TenantChannelThriftServiceWrapper.class)).orElseThrow(() -> new CommonRuntimeException("tenantChannelThriftServiceWrapper is null"));
		final List<Integer> channelTypeList = tenantChannelThriftServiceWrapper.getTenantChannelType(String.valueOf(tenantId));
		if(CollectionUtils.isEmpty(platformCodes)){
			return Collections.emptyList();
		}
		Map<Integer,DeliveryPlatformEnum> deliveryPlatformEnumMap=deliveryPoiList.stream().collect(Collectors.toMap(DeliveryPoi::getChannelType,DeliveryPoi::getDeliveryPlatform,(k1, k2) -> k1));
		List<TAggDeliveryPlatformConfig>  configList=new ArrayList<>();
		for (Integer channelType : channelTypeList){
			configList.addAll(fillAggPlatformConfig(deliveryPlatformEnumMap.get(channelType),channelType,platformCodes));
		}
		return configList;
	}

	public static List<TAggDeliveryPlatformConfig> fillLastAggPlatformConfig(List<DeliveryPoi> deliveryPoiList, Long tenantId) {
		Map<Integer,DeliveryPlatformEnum> deliveryPlatformEnumMap=new HashMap<>();
		if(CollectionUtils.isNotEmpty(deliveryPoiList)){
			for (DeliveryPoi deliveryPoi : deliveryPoiList){
				deliveryPlatformEnumMap.put(deliveryPoi.getChannelType(),deliveryPoi.getLastDeliveryPlatform()==null ? DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM:deliveryPoi.getLastDeliveryPlatform());
			}
		}

		TenantChannelThriftServiceWrapper tenantChannelThriftServiceWrapper =
				Optional.of(SpringContextUtils.getBean(TenantChannelThriftServiceWrapper.class)).orElseThrow(() -> new CommonRuntimeException("tenantChannelThriftServiceWrapper is null"));
		final List<Integer> channelTypeList = tenantChannelThriftServiceWrapper.getTenantChannelType(String.valueOf(tenantId));
		List<TAggDeliveryPlatformConfig>  configList=new ArrayList<>();
		for (Integer channelType : channelTypeList){
			DeliveryPlatformEnum platformEnum=deliveryPlatformEnumMap.get(channelType);
			if(platformEnum==null){
				platformEnum = DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM;
			}
			AggDeliveryPlatformAppConfig appConfig =
					Optional.ofNullable(AggDeliveryPlatformAppConfigUtils.getAggDeliveryPlatformAppConfig(platformEnum.getCode()))
							.orElse(new AggDeliveryPlatformAppConfig());

			configList.add(new TAggDeliveryPlatformConfig(
					platformEnum.getCode(),
					BigInteger.ZERO.intValue(),
					appConfig.getConfigUrl(),
					appConfig.getRedirectUrl(),
					appConfig.getAppKey(),
					appConfig.getAppSecret(),
					channelType
			));
		}
		return configList;
	}

	public static List<TAggDeliveryPlatformConfig> fillAggPlatformConfig(DeliveryPlatformEnum deliveryPlatform,Integer channelType,List<Integer> platformCodes){
		return platformCodes.stream()
				.map(DeliveryPlatformEnum::enumOf)
				.filter(Objects::nonNull)
				.map(it -> {
					AggDeliveryPlatformAppConfig appConfig =
							Optional.ofNullable(AggDeliveryPlatformAppConfigUtils.getAggDeliveryPlatformAppConfig(it.getCode()))
									.orElse(new AggDeliveryPlatformAppConfig());

					return new TAggDeliveryPlatformConfig(
							it.getCode(),
							it == deliveryPlatform ? BigInteger.ONE.intValue() : BigInteger.ZERO.intValue(),
							appConfig.getConfigUrl(),
							appConfig.getRedirectUrl(),
							appConfig.getAppKey(),
							appConfig.getAppSecret(),
							channelType
					);
				})
				.collect(Collectors.toList());
	}

	public static void fillAggPlatformConfig(DeliveryPlatformEnum deliveryPlatform, TStoreConfig tStoreConfig, List<Integer> platformCodes) {
		if (CollectionUtils.isEmpty(platformCodes)) {
			return;
		}

		tStoreConfig.setAggPlatformConfigs(
				platformCodes.stream()
						.map(DeliveryPlatformEnum::enumOf)
						.filter(Objects::nonNull)
						.map(it -> {
							AggDeliveryPlatformAppConfig appConfig =
									Optional.ofNullable(AggDeliveryPlatformAppConfigUtils.getAggDeliveryPlatformAppConfig(it.getCode()))
											.orElse(new AggDeliveryPlatformAppConfig());

							return new TAggDeliveryPlatformConfig(
									it.getCode(),
									it == deliveryPlatform ? BigInteger.ONE.intValue() : BigInteger.ZERO.intValue(),
									appConfig.getConfigUrl(),
									appConfig.getRedirectUrl(),
									appConfig.getAppKey(),
									appConfig.getAppSecret(),
									DynamicChannelType.MEITUAN.getChannelId()
							);
						})
						.collect(Collectors.toList())
		);
	}

	public static List<TOuterDeliveryConfig> filterSupportChannel(List<DeliveryChannelInfo> channelInfos) {
		if (CollectionUtils.isEmpty(channelInfos)) {
			return Lists.emptyList();
		}
		// TMS 支持的聚合运力平台上的配送商
		Set<Integer> unsupportedChannels = TmsMccUtils.getUnsupportedChannelsInTms();
		return channelInfos.stream()
				.filter(it -> !unsupportedChannels.contains(it.getDeliveryChannelId()))
				.map(it -> {
					TOuterDeliveryConfig outer = new TOuterDeliveryConfig();
					outer.setDeliveryChannelId(it.getDeliveryChannelId());
					outer.setDeliveryChannelMerchantId(it.getDeliveryChannelMerchantId());
					outer.setDeliveryChannelName(it.getDeliveryChannelName());
					outer.setDeliveryChannelPoiId(it.getDeliveryChannelPoiId());
					outer.setDeliveryChannelPoiExt(it.getDeliveryChannelPoiExt());
					outer.setOpenFlag(it.getOpenFlag());
					outer.setOperateType(it.getSyncType());
					outer.setStoreId(it.getStoreId());
					return outer;
				})
				.collect(Collectors.toList());
	}

	/**
	 * 为 TStoreConfig 填充可用的发单规则
	 */
	public static List<TLaunchRule> translateAvailableRule(TStoreConfig tStoreConfig, List<LaunchRuleInfo> availableRules) {
		if (CollectionUtils.isEmpty(availableRules)) {
			Integer launchRule = tStoreConfig.getLaunchRule();
			DeliveryStrategyEnum deliveryStrategyEnum = DeliveryStrategyEnum.enumOf(launchRule);
			if (deliveryStrategyEnum == null) {
				throw new DeliveryBaseException("查询可用发单规则失败，请联系管理员。");
			}
			return Lists.newArrayList(new TLaunchRule(deliveryStrategyEnum.getCode(), deliveryStrategyEnum.getName()));
		}
		return availableRules.stream().map(rule -> new TLaunchRule(rule.getCode(), rule.getName())).collect(Collectors.toList());
	}
}
