package com.sankuai.meituan.shangou.empower.tms.delivery.exception;

import lombok.Data;


@Data
public class CommonLogicException extends RuntimeException {

    private String msg;

    private Integer code;

    public CommonLogicException(DeliveryError error) {
        super(error.getMsg());
        this.code = error.getCode();
        this.msg = error.getMsg();
    }

    public CommonLogicException(Integer code, String msg) {
        super(msg);

        this.code = code;
        this.msg = msg;
    }

    public CommonLogicException(String msg) {
        super(msg);

        this.code = -1;
        this.msg = msg;
    }
}
