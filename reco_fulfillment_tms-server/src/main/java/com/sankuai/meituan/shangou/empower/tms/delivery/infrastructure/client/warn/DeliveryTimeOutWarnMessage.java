package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.warn;

import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.DaySeqNumUtil;
import lombok.AllArgsConstructor;

import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/21
 */
@AllArgsConstructor
public class DeliveryTimeOutWarnMessage {

	private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

	private final DeliveryOrder deliveryOrder;
	private final PoiInfoDto poiInfo;
	private final OrderInfo orderInfo;

	@Override
	public String toString() {
		String daySeq = DaySeqNumUtil.getDaySeqNumWithoutDHTenant(orderInfo.getDaySeq(), orderInfo.getDaySeqNum(), orderInfo.getOrderKey().getTenantId());
		return "配送超时提醒\n" +
				"【门店】" + poiInfo.getPoiName() + "\n" +
				"【超时订单号】" + orderInfo.getChannelOrderId() + "\n" +
				"【流水号】" + Optional.ofNullable(DynamicOrderBizType.findOf(orderInfo.getOrderBizType())).map(DynamicOrderBizType::getDesc).orElse("") + "#" + daySeq + "\n" +
				"【用户姓名】" + orderInfo.getReceiver().getReceiverName() + "\n" +
				"【骑手姓名】" + Optional.ofNullable(deliveryOrder).map(DeliveryOrder::getRiderInfo).map(Rider::getRiderName).orElse("") + "\n" +
				"【骑手电话】" + Optional.ofNullable(deliveryOrder).map(DeliveryOrder::getRiderInfo).map(Rider::getRiderPhone).orElse("") + "\n" +
				"【当前配送状态】" + Optional.ofNullable(deliveryOrder).map(DeliveryOrder::getStatus).map(DeliveryStatusEnum::getDesc).orElse("暂无配送状态") + "\n" +
				"【订单类型】" + (orderInfo.isBookingOrder() ? "预订单" : "实时单") + "\n" +
				"【订单支付时间】：" + Optional.ofNullable(orderInfo.getPayTime()).map(it -> it.format(DATE_TIME_FORMATTER)).orElse("") + "\n" +
				"【订单预计送达时间】：" + getETADescription();
	}

	private String getETADescription() {
		if (orderInfo.getEstimatedDeliveryTime().compareTo(orderInfo.getEstimatedDeliveryEndTime()) == 0) {
			return orderInfo.getEstimatedDeliveryTime().format(DATE_TIME_FORMATTER);
		} else {
			return String.format(
					"%s~%s",
					orderInfo.getEstimatedDeliveryTime().format(DATE_TIME_FORMATTER),
					orderInfo.getEstimatedDeliveryEndTime().format(DATE_TIME_FORMATTER)
			);
		}
	}
}
