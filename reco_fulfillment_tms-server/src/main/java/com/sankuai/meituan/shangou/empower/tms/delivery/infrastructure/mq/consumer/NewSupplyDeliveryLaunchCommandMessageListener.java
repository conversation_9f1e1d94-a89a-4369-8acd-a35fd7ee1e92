package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryOperationApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryProcessApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryNotifyService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryLaunchCmdMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;


/**
 * 新供给发起配送消息Listener
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class NewSupplyDeliveryLaunchCommandMessageListener extends AbstractDeadLetterConsumer {

	private static final Long SYSTEM_OPERATOR_ID = 0L;

	@Resource
	private DeliveryProcessApplicationService deliveryProcessApplicationService;

	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;

	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;

	@Resource
	private DeliveryOperationApplicationService deliveryOperationApplicationService;

	@Resource
	private DeliveryNotifyService deliveryNotifyService;

	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.NEW_SUPPLY_DELIVERY_LAUNCH_COMMAND;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
		if (!MccConfigUtils.getNewSupplyDeliveryLaunchCommandMessageListenerSwitch()) {
			log.info("新供给发起配送消息开关关闭，不处理消息");
			return CONSUME_SUCCESS;
		}

		log.info("开始消费新供给发起配送消息：{}", mafkaMessage);
		DeliveryLaunchCmdMessage message = translateMessage(mafkaMessage);
		if (message == null) {
			return CONSUME_SUCCESS;
		}

		try {
			DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
					message.getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
			Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(message.getTenantId(),
					message.getShopId(), orderBizType.getChannelId());

			if (!opDeliveryPoi.isPresent()) {
				log.warn("门店[{}]未开通自配送，将放弃发起配送", message.getShopId());
				return CONSUME_SUCCESS;
			}
			DeliveryPoi deliveryPoi = opDeliveryPoi.get();

			PlatformSourceEnum platformSourceEnum = PlatformSourceEnum.OMS;
			if (message.getPlatformSource() != null) {
				platformSourceEnum = PlatformSourceEnum.platformCodeToEnum(message.getPlatformSource());
			}

			// 当前仅用于抖音平台配送
			if (!DynamicOrderBizType.DOU_YIN.equals(orderBizType) || !DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.equals( deliveryPoi.getDeliveryPlatform())) {
				log.info("当前延迟发配送仅用于抖音平台配送, orderId: {}, orderBizType is {}, deliveryPoi is {}", message.getOrderId(), orderBizType, deliveryPoi);
				return CONSUME_SUCCESS;
			}

			// 可能存在先发延迟平台配送，在平台配送发之前转了自送，此时需要做幂等处理
			Optional<DeliveryOrder> activeDeliveryOrder = Optional.empty();
			if(MccConfigUtils.getDeliveryQueryTenantSwitch(deliveryPoi.getTenantId())){
				activeDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderWithTenant(message.getOrderId(),deliveryPoi.getTenantId(),deliveryPoi.getStoreId());
			}else {
				activeDeliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(message.getOrderId());
			}

			if (activeDeliveryOrder.isPresent()) {
				log.info("NewSupplyDeliveryLaunchCommandMessageListener, activeDeliveryOrder is present, orderId: {}, viewOrderId: {}", message.getOrderId(), message.getViewOrderId());
				return CONSUME_SUCCESS;
			}

			Optional<Failure> launchFailure;
			switch (deliveryPoi.getDeliveryPlatform()) {
				case MERCHANT_SELF_DELIVERY:
				case AGGREGATION_DELIVERY_PLATFORM:
				case MALT_FARM_DELIVERY_PLATFORM:
				case DAP_DELIVERY_PLATFORM:
				case ORDER_CHANNEL_DELIVERY_PLATFORM:
					launchFailure = deliveryOperationApplicationService.launchDelivery(deliveryPoi,
							new OrderKey(message.getTenantId(), message.getShopId(), message.getOrderId()),
							!isLastRetry(mafkaMessage),platformSourceEnum,message.getFulfillOrderId());
					break;
				default:
					log.warn("不支持的配送平台[{}]，将放弃发起配送", deliveryPoi.getDeliveryPlatform().getCode());
					return CONSUME_SUCCESS;
			}

			if (isDyPlatformDelivery(deliveryPoi, message)) {
				sendOrderTrack(deliveryPoi.getTenantId(), message.getViewOrderId(), message.getOrderBizType(), SYSTEM_OPERATOR_ID, !launchFailure.isPresent());
			}

			if (launchFailure.isPresent() && launchFailure.get().isNeedRetry()) {
				log.info("发起配送消息处理失败,需要进行重试, failure={}", launchFailure.get());
				return CONSUME_FAILURE;
			}

			return CONSUME_SUCCESS;
		} catch (Exception e) {
			log.warn("发起配送消息处理异常", e);
			return CONSUME_FAILURE;
		}
	}

	private DeliveryLaunchCmdMessage translateMessage(MafkaMessage mafkaMessage) {
		try {
			DeliveryLaunchCmdMessage message = translateMessage(mafkaMessage, DeliveryLaunchCmdMessage.class);
			Preconditions.checkNotNull(message, "empty mafkaMessage");
			Preconditions.checkNotNull(message.getTenantId(), "发起配送消息tenantId为空");
			Preconditions.checkNotNull(message.getShopId(), "发起配送消息shopId为空");
			Preconditions.checkNotNull(message.getOrderId(), "发起配送消息orderId为空");
			return message;

		} catch (Exception e) {
			log.error("解析发起配送消息失败:{}", mafkaMessage, e);
			Cat.logEvent("DELIVERY_LAUNCH_FAILED", "DELIVERY_LAUNCH_COMMAND_MESSAGE_WRONG");
			return null;
		}
	}

	/**
	 * 判断是否是新供给的抖音平台配送
	 */
	private boolean isDyPlatformDelivery(DeliveryPoi deliveryPoi, DeliveryLaunchCmdMessage message) {
		return  DynamicOrderBizType.DOU_YIN.equals(DynamicOrderBizType.findOf(message.getOrderBizType()))
				&& DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.equals(deliveryPoi.getDeliveryPlatform());
	}

	private void sendOrderTrack(Long tenantId, String channelOrderId, Integer orderBizType, Long operatorId, boolean isSuccess) {
		OrderTrackEvent event = new OrderTrackEvent();
		event.setTrackSource(TrackSource.DELIVERY.getType());
		event.setTrackOpType(isSuccess ? TrackOpType.AUTO_LAUNCH_DELIVERY_SUCCESS.getOpType() : TrackOpType.AUTO_LAUNCH_DELIVERY_FAIL.getOpType());
		event.setAccountIdList(Collections.singletonList(operatorId));
		event.setOperateTime(System.currentTimeMillis());
		event.setTenantId(tenantId);
		event.setUnifyOrderId(channelOrderId);
		event.setOrderBizType(orderBizType);

		deliveryNotifyService.notifyDeliveryTrace(event);
	}
}
