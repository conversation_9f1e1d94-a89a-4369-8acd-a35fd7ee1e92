package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.crane;

import com.cip.crane.client.ITaskHandler;
import com.cip.crane.netty.protocol.ScheduleTask;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChannelRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryChannelCraneTaskException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 配送渠道信息定时同步任务，将DB里的数据刷新至缓存delivery_channel中，周期6小时一次
 * <AUTHOR>
 */
@Component
@Slf4j
public class DeliveryChannelRefreshCraneTask implements ITaskHandler  {

    @Autowired
    @Qualifier("mySQLDeliveryChannelRepository")
    private DeliveryChannelRepository mysqlDeliveryChannelRepository;

    @Autowired
    @Qualifier("squirrelDeliveryChannelRepository")
    private DeliveryChannelRepository squirrelDeliveryChannelRepository;

    private static final long START_ID = NumberUtils.LONG_ONE;

    @Override
    public void handleTask(ScheduleTask scheduleTask) throws DeliveryChannelCraneTaskException {
        log.info("DeliveryChannelRefreshCraneTask start");
        try {
            int batchSize = MccConfigUtils.getBatchSize4QueryAllDeliveryChannelFromDB();
            Long maxId = mysqlDeliveryChannelRepository.getMaxId();

            for (long startId = START_ID; startId <= maxId; startId += batchSize) {
                long endId = startId + batchSize - 1;
                // 从DB分批次查询数据并更新到缓存，避免大的list导致fullGc
                List<DeliveryChannel> deliveryChannelList = mysqlDeliveryChannelRepository.getBatchDeliveryChannelList(startId, endId);
                if (CollectionUtils.isEmpty(deliveryChannelList)) {
                    log.error("handleTask failed, get delivery channel list from mysql is empty");
                    throw new DeliveryChannelCraneTaskException("DeliveryChannelRefreshCraneTask handleTask failed, deliveryChannelList is empty");
                }

                Boolean result = squirrelDeliveryChannelRepository.updateAllDeliveryChannelList(deliveryChannelList);
                if (!result) {
                    throw new DeliveryChannelCraneTaskException("DeliveryChannelRefreshCraneTask handleTask failed, updateAllDeliveryChannelList error");
                }
            }
        } catch (Exception e) {
            throw new DeliveryChannelCraneTaskException("DeliveryChannelRefreshCraneTask handleTask failed");
        }
        log.info("DeliveryChannelRefreshCraneTask end");
    }
}
