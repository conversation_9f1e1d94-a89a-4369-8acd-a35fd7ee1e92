package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mapstruct;

import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryRiderStatistics;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryStoreStatistics;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDurationDeliveryStatistic;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp.DeliveryRiderStatistics;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp.DeliveryStoreStatistics;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp.RiderDurationDeliveryStatistics;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RiderDeliveryStatisticConverter {

    List<TDeliveryRiderStatistics> toTDeliveryRiderStatisticsList(List<DeliveryRiderStatistics> statisticsList);

    TDeliveryRiderStatistics toTDeliveryRiderStatistics(DeliveryRiderStatistics statistics);

    List<TDeliveryStoreStatistics> toTDeliveryStoreStatisticsList(List<DeliveryStoreStatistics> storeStatisticsList);

    TDeliveryStoreStatistics toTDeliveryStoreStatistics(DeliveryStoreStatistics storeStatistics);

    List<TRiderDurationDeliveryStatistic> toTRiderDurationDeliveryStatisticList(List<RiderDurationDeliveryStatistics> durationStatisticsList);

    TRiderDurationDeliveryStatistic toTRiderDurationDeliveryStatistic(RiderDurationDeliveryStatistics durationStatistics);
}
