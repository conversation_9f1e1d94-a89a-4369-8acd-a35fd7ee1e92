package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform;

import com.dianping.cat.Cat;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.orderChannelDelivery.request.CancelOrderChannelDeliveryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.orderChannelDelivery.request.CreateOrderChannelDeliveryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.orderChannelDelivery.request.ManualLaunchDeliveryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.orderChannelDelivery.response.CancelOrderChannelDeliveryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.orderChannelDelivery.response.CreateOrderChannelDeliveryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.orderChannelDelivery.response.ManualLaunchDeliveryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.OrderChannelDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.LaunchFailure;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryUnifiedCallbackMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.RetryTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.DeliveryResultCodeEnum.SUCCESS;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/12/8 17:53
 */
@Slf4j
@Component
public class OrderChannelDeliveryPlatformClient extends AbstractDeliveryPlatformClient {

    @Autowired
    private MafkaMessageProducer<DeliveryUnifiedCallbackMessage> deliveryUnifiedCallbackMessageProducer;

    @Autowired
    private OrderChannelDeliveryThriftService orderChannelDeliveryThriftService;

    @Autowired
    private TenantSystemClient tenantSystemClient;
    
    @Autowired
    private DeliveryOrderRepository deliveryOrderRepository;

    @Override
    public DeliveryPlatformEnum getDeliveryPlatform() {
        return DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM;
    }

    @Override
    public Optional<LaunchFailure> launch(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder,Integer transferOrderMark) {
        CreateOrderChannelDeliveryRequest request = buildCreateDeliveryRequest(deliveryPoi, orderInfo,deliveryOrder);
        log.info("OrderChannelDeliveryPlatformClient create delivery request is {}", request);

        CreateOrderChannelDeliveryResponse response;
        try {
            response = orderChannelDeliveryThriftService.createDelivery(request);
            log.info("OrderChannelDeliveryPlatformClient create delivery response is {}", response);
        } catch (TException e) {
            log.error("OrderChannelDeliveryPlatformClient create delivery TException", e);
            Cat.logEvent("LAUNCH_CANCEL_" + getDeliveryPlatform().getCode(), "CALL_CHANNEL_ERROR");
            return Optional.of(new LaunchFailure(true, DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "调用订单渠道运力网关发配送接口失败"));
        }

        // 处理抖音平台配送，且商家端开启了自动呼叫的特殊场景
        process4DyPlatformDelivery(response, orderInfo, deliveryOrder);

        if (Objects.isNull(response) || Objects.isNull(response.getCode())) {
            log.error("response code is null");
            Cat.logEvent("LAUNCH_CANCEL_" + getDeliveryPlatform().getCode(), "CALL_CHANNEL_ERROR");
            return Optional.of(new LaunchFailure(true, DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "调用订单渠道运力网关发配送接口失败"));
        }
        if (response.getCode() != SUCCESS.getCode()) {
            // 对抖音商家端开启了自动呼叫的场景不处理成配送拒单
            if (FailureCodeEnum.DOU_YIN_DELIVERY_MERCHANT_PLATFORM_AUTO_CALL.getCode() == response.getCode()) {
                log.info("douyin auto call is on, orderId: {}", orderInfo.getOrderKey().getOrderId());
                return Optional.empty();
            }

            Cat.logEvent("LAUNCH_CANCEL_" + getDeliveryPlatform().getCode(), "CALL_CHANNEL_ERROR");
            return Optional.of(new LaunchFailure(true, DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "调用订单渠道运力网关发配送接口失败"));
        }
        return Optional.empty();
    }

    @Override
    public Optional<Failure> cancelDelivery(DeliveryOrder deliveryOrder) {
        /*
         * 有赞渠道的平台配送，取消配送时存在这样的问题：订单侧取消订单调用有赞接口后，有赞侧订单状态变为取消，
         * 之后调用有赞取消三方配送接口会调用失败，原因是此时订单已取消。
         * 因此对于有赞渠道的平台配送，取消配送时不再调用有赞侧的取消配送接口，而是将运单状态推到已取消状态
         */
        List<Integer> orderBizTypeCodeList = MccConfigUtils.queryOrderBizType4OrderChannelDelivery();
        if (CollectionUtils.isEmpty(orderBizTypeCodeList)) {
            log.error("cancelDelivery, orderBizTypeCodeCodeList is empty");
            return Optional.empty();
        }

        if (orderBizTypeCodeList.contains(deliveryOrder.getOrderBizType())) {
            log.info("cancelDelivery, orderBizType is in orderBizTypeCodeList, orderBizType is {}", deliveryOrder.getOrderBizType());
            deliveryOrder.onChange(DeliveryEventEnum.DELIVERY_CANCEL, DeliveryExceptionInfo.NO_EXCEPTION, deliveryOrder.getRiderInfo(), LocalDateTime.now());
            return Optional.empty();
        }

        // 调用渠道三方运力平台接口取消配送
        return cancelOrderChannelDelivery(deliveryOrder);
    }

    @Override
    public Optional<LaunchFailure> manualLaunchDelivery(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
        ManualLaunchDeliveryRequest request = buildManualLaunchDeliveryRequest(deliveryPoi, orderInfo);
        log.info("OrderChannelDeliveryPlatformClient manualLaunchDelivery request is {}", request);

        ManualLaunchDeliveryResponse response;
        try {
            response = orderChannelDeliveryThriftService.manualLaunchDelivery(request);
            log.info("OrderChannelDeliveryPlatformClient manualLaunchDelivery response is {}", response);
        } catch (TException e) {
            log.error("OrderChannelDeliveryPlatformClient manualLaunchDelivery TException", e);
            Cat.logEvent("LAUNCH_CANCEL_" + getDeliveryPlatform().getCode(), "CALL_CHANNEL_ERROR");
            return Optional.of(new LaunchFailure(true, DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_FAILED));
        }

        if (Objects.isNull(response) || Objects.isNull(response.getCode())) {
            Cat.logEvent("LAUNCH_CANCEL_" + getDeliveryPlatform().getCode(), "CALL_CHANNEL_ERROR");
            return Optional.of(new LaunchFailure(true, DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_FAILED));
        }

        if (response.getCode() != SUCCESS.getCode()) {
            Cat.logEvent("LAUNCH_CANCEL_" + getDeliveryPlatform().getCode(), "CALL_CHANNEL_ERROR");
            if (response.getCode() == FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_DOU_YIN_FAILED.getCode()) {
                return Optional.of(new LaunchFailure(true, DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_DOU_YIN_FAILED, response.getMsg()));
            } else if (response.getCode() == FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_OPEN_API_FAILED.getCode()) {
                return Optional.of(new LaunchFailure(true, DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_OPEN_API_FAILED, response.getMsg()));
            } else {
                return Optional.of(new LaunchFailure(true, DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_FAILED));
            }
        }
        return Optional.empty();
    }

    private CreateOrderChannelDeliveryRequest buildCreateDeliveryRequest(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder) {
        CreateOrderChannelDeliveryRequest request = new CreateOrderChannelDeliveryRequest();
        request.setTenantId(deliveryPoi.getTenantId());
        request.setStoreId(deliveryPoi.getStoreId());
        request.setOrderId(orderInfo.getOrderKey().getOrderId());
        request.setChannelType(DynamicOrderBizType.orderBizTypeValue2ChannelId(orderInfo.getOrderBizType()));
        request.setChannelOrderId(orderInfo.getChannelOrderId());
        request.setGoodsWeight(orderInfo.getGoodsTotalWeight());
        request.setTipAmount(Objects.isNull(deliveryOrder.getTipAmount()) ? NumberUtils.INTEGER_ZERO :
                deliveryOrder.getTipAmount().multiply(BigDecimal.TEN).multiply(BigDecimal.TEN).intValue());

        DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
        if (Objects.nonNull(channelType)) {
            String channelPoiId = tenantSystemClient.queryChannelPoiIdByPoiId(orderInfo.getOrderKey().getTenantId(), orderInfo.getPoiId(), channelType.getChannelId());
            request.setChannelStoreId(channelPoiId);
        }

        return request;
    }

    private ManualLaunchDeliveryRequest buildManualLaunchDeliveryRequest(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
        ManualLaunchDeliveryRequest request = new ManualLaunchDeliveryRequest();
        request.setTenantId(deliveryPoi.getTenantId());
        request.setStoreId(deliveryPoi.getStoreId());
        request.setOrderId(orderInfo.getOrderKey().getOrderId());
        request.setChannelType(DynamicOrderBizType.orderBizTypeValue2ChannelId(orderInfo.getOrderBizType()));
        request.setChannelOrderId(orderInfo.getChannelOrderId());

        DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
        if (Objects.nonNull(channelType)) {
            String channelPoiId = tenantSystemClient.queryChannelPoiIdByPoiId(orderInfo.getOrderKey().getTenantId(), orderInfo.getPoiId(), channelType.getChannelId());
            request.setChannelStoreId(channelPoiId);
        }

        return request;
    }

    private Optional<Failure> cancelOrderChannelDelivery(DeliveryOrder deliveryOrder) {
        CancelOrderChannelDeliveryRequest request = buildCancelDeliveryRequest(deliveryOrder);
        log.info("OrderChannelDeliveryPlatformClient cancel delivery request is {}", request);

        CancelOrderChannelDeliveryResponse response;
        try {
            response = orderChannelDeliveryThriftService.cancelDelivery(request);
            log.info("OrderChannelDeliveryPlatformClient cancel delivery response is {}", response);
        } catch (TException e) {
            log.error("OrderChannelDeliveryPlatformClient cancel delivery TException", e);
            Cat.logEvent("LAUNCH_CANCEL_" + getDeliveryPlatform().getCode(), "CALL_CHANNEL_ERROR");
            return Optional.of(new LaunchFailure(true, DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "调用订单渠道运力网关取消配送接口失败"));
        }

        if (Objects.isNull(response) || Objects.isNull(response.getCode()) || response.getCode() != SUCCESS.getCode()) {
            Cat.logEvent("LAUNCH_CANCEL_" + getDeliveryPlatform().getCode(), "CALL_CHANNEL_ERROR");
            return Optional.of(new LaunchFailure(true, DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "调用订单渠道运力网关取消配送接口失败"));
        }
        return Optional.empty();
    }

    private CancelOrderChannelDeliveryRequest buildCancelDeliveryRequest(DeliveryOrder deliveryOrder) {
        CancelOrderChannelDeliveryRequest request = new CancelOrderChannelDeliveryRequest();
        request.setTenantId(deliveryOrder.getTenantId());
        request.setStoreId(deliveryOrder.getStoreId());
        request.setChannelOrderId(deliveryOrder.getChannelOrderId());
        request.setChannelDeliveryId(deliveryOrder.getChannelDeliveryId());
        if (Objects.nonNull(deliveryOrder.getDeliveryChannel())) {
            request.setDeliveryChannel(deliveryOrder.getDeliveryChannel());
        }
        DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(deliveryOrder.getOrderBizType());
        request.setChannelType(channelType.getChannelId());
        return request;
    }

    private void process4DyPlatformDelivery(CreateOrderChannelDeliveryResponse response, OrderInfo orderInfo, DeliveryOrder deliveryOrder) {
        DynamicOrderBizType orderBizTypeEnum = DynamicOrderBizType.findOf(orderInfo.getOrderBizType());
        if (Objects.isNull(orderBizTypeEnum)) {
            return;
        }
        if (!DynamicOrderBizType.DOU_YIN.equals(orderBizTypeEnum)) {
            return;
        }

        // 仅处理抖音商家端开启了自动呼叫的场景
        if (Objects.isNull(response) || FailureCodeEnum.DOU_YIN_DELIVERY_MERCHANT_PLATFORM_AUTO_CALL.getCode() != response.getCode()) {
            return;
        }

        // 更新运单标记位为非牵牛花管理配送运单
        log.info("process4DyPlatformDelivery, orderId is {}", orderInfo.getOrderKey().getOrderId());
        try {
            RetryTemplate retryTemplate = RetryTemplateUtil.simpleWithFixedRetry(3,100);
            retryTemplate.execute((RetryCallback<Void, Throwable>) retryContext -> {
                deliveryOrder.setIsQhnManagement(DeliveryOrderSourceEnum.NOT_QNH_MANAGEMENT.getCode());
                deliveryOrderRepository.save(deliveryOrder);
                return null;
            });
        } catch (Throwable e) {
            log.error("process4DyPlatformDelivery retry error", e);
        }
    }
}
