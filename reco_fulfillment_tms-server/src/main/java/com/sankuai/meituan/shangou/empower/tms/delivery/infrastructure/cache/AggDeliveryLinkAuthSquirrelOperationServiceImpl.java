package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache;

import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.AggDeliveryLinkAuthSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.QueryAggLinkBaseRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.QueryAggStoreSettingsLinkRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.AggLinkAuthInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-10
 */
@Service
@Slf4j
public class AggDeliveryLinkAuthSquirrelOperationServiceImpl extends SquirrelOperateService implements AggDeliveryLinkAuthSquirrelOperationService {
    @Autowired
    @Qualifier("redisSgNewSupplyOfc")
    protected RedisStoreClient redisNewSupplyClient;

    private static final String CATEGORY_NAME = "agg_link_auth";

    @Override
    protected RedisStoreClient getRedisClient() {
        return redisNewSupplyClient;
    }

    @Override
    public String getCategoryName() {
        return CATEGORY_NAME;
    }

    @Override
    public boolean save(String code, QueryAggLinkBaseRequest baseRequest, String fulfillmentOrderId) {
        AggLinkAuthInfo aggLinkAuthInfo = new AggLinkAuthInfo();
        if (baseRequest instanceof QueryAggStoreSettingsLinkRequest) {
            QueryAggStoreSettingsLinkRequest request = (QueryAggStoreSettingsLinkRequest) baseRequest;
            aggLinkAuthInfo.setEToken(request.getEToken());
        }
        aggLinkAuthInfo.setDeliveryOrderId(fulfillmentOrderId);
        aggLinkAuthInfo.setOperatorId(baseRequest.getOperatorAccount());
        aggLinkAuthInfo.setOperatorName(baseRequest.getOperatorName());
        aggLinkAuthInfo.setPoiId(baseRequest.getPoiId());
        return set(code, aggLinkAuthInfo);
    }

    @Override
    public Optional<AggLinkAuthInfo> get(String code) {
        return get(code, AggLinkAuthInfo.class);
    }

    @Override
    public boolean remove(String code) {
        return delete(code);
    }
}
