package com.sankuai.meituan.shangou.empower.tms.delivery.enums;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;


/**
 * 有赞平台配送，配送状态与百川统一配送状态映射
 * <AUTHOR>
 * @see "https://doc.youzanyun.com/detail/MSG/357"
 */
@Slf4j
public enum YzOrderChannelDeliveryStatusEnum {

    WAIT_TO_RECEIVE(1, "待接单"),
    WAIT_TO_TAKE(2, "待取货"),
    MERCHANT_DELIVERING(3, "配送中"),
    DELIVERY_DONE(4, "已完成"),
    DELIVERY_CANCELLED(5, "已取消"),
    DELIVERY_EXPIRE(7, "已过期-十分钟骑手未接单"),

    CANCEL_EXCEPTION(6, "取消异常"),
    CUSTOMER_EXCEPTION_CANCEL(8, "客户方异常终止-配送方无责任的异常终止，比如客人联系不上，无需退款"),
    DELIVERY_EXCEPTION_CANCEL(9, "配送方异常终止-配送方有责任的异常终止，比如骑士丢件，需要退款"),
    SELF_TRACE_INITIAL(30, "自己追溯初始态"),
    THIRD_PART_EXCEPTION_CODE(90, "第三方返回异常码"),
    SERVER_EXCEPTION(1000, "服务器异常"),
    THIRD_PART_ORDER_FAILED(-2, "调用第三方下单失败"),
    THIRD_PART_EXCEPTION(10, "第三方反馈异常")
    ;
    private int code;

    private String msg;

    YzOrderChannelDeliveryStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static YzOrderChannelDeliveryStatusEnum enumOf(int value) {
        for (YzOrderChannelDeliveryStatusEnum each : values()) {
            if (each.getCode() == value) {
                return each;
            }
        }

        return null;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static Optional<DeliveryStatusEnum> mapToDeliveryStatus(int value) {
        String yzDeliveryStatusMappingStr = MccConfigUtils.queryYzOrderChannelDeliveryStatusMapping();
        if (StringUtils.isEmpty(yzDeliveryStatusMappingStr)) {
            log.error("YzOrderChannelDeliveryStatusEnum mapToDeliveryStatus error, yzDeliveryStatusMappingStr is empty");
            return Optional.empty();
        }

        Map<String, Integer> sourceMap;
        try {
            sourceMap = JSON.parseObject(yzDeliveryStatusMappingStr, new TypeReference<Map<String, Integer>>() {}.getType());
        } catch (Exception e) {
            log.error("mapToDeliveryStatus error, parse yzDeliveryStatusMappingStr error, yzDeliveryStatusMappingStr: {}", yzDeliveryStatusMappingStr);
            return Optional.empty();
        }

        YzOrderChannelDeliveryStatusEnum yzOrderChannelDeliveryStatusEnum = enumOf(value);
        if (Objects.isNull(yzOrderChannelDeliveryStatusEnum)) {
            log.error("mapToDeliveryStatus error, yzOrderChannelDeliveryStatusEnum is null");
            return Optional.empty();
        }

        Integer deliveryStatusCode = sourceMap.get(yzOrderChannelDeliveryStatusEnum.name());
        if (Objects.isNull(deliveryStatusCode)) {
            log.error("mapToDeliveryStatus error, deliveryStatusCode is null");
            return Optional.empty();
        }

        return Optional.ofNullable(DeliveryStatusEnum.valueOf(deliveryStatusCode));
    }
}
