package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.map;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.base.Joiner;
import com.meituan.reco.pickselect.common.exception.FallbackException;
import com.sankuai.map.open.platform.api.MapOpenApiService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aop.LoadTestAop;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.Coordination;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RouteInfoDTO;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.map.MapClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.map.MapRouteRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.wmarch.map.thriftClient.route.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@Rhino
public class MapCenterClientImpl implements MapClient {

    @Resource(name = "mapOpenApiService")
    private MapOpenApiService.Iface mapOpenApiService;

    @Resource(name = "mapOpenApiService4DrunkHorse")
    private MapOpenApiService.Iface mapOpenApiService4DrunkHorse;

    /**
     * NORMAL：常规路线
     * COMPLIANT_LEVEL_2：二级合规
     * COMPLIANT_LEVEL_3：三级合规
     */
    private static final String ROUTE_STRATEGY = "COMPLIANT_LEVEL_2";
    private static final String SPLITTER = ",";
    private static final int MAP_SUCCESS_CODE = 200;

    private static final String POLYLINE = "polyline";

    private static final String ATTRIBUTE = "attribute";

    @Override
    @Degrade(rhinoKey = "MapCenterClientImpl.queryRidePathDistance", fallBackMethod = "queryRidePathDistanceFallback", timeoutInMilliseconds = 1000)
    @LoadTestAop
    public Result<Double> queryRidePathDistance(MapRouteRequest req) {
        try {
            RouteRequest request = new RouteRequest();
            request.setKey((req.getType() == null || req.getType() == 1) ? MccConfigUtils.getDrunkHorseMapReqKey():MccConfigUtils.getNewSupplyMapReqKey());
            Origin origin = new Origin();
            origin.setLocation(concatLatitudeAndLongitude(req.getOriginLongitude(), req.getOriginLatitude()));
            request.setOrigin(origin);
            Destination destination = new Destination();
            destination.setLocation(concatLatitudeAndLongitude(req.getDestinationLongitude(), req.getDestinationLatitude()));
            request.setDestination(destination);
            request.setStrategy(ROUTE_STRATEGY);
            RouteResponse routeResult = mapOpenApiService.riding(request);
            log.info("invoke mapOpenApiService.riding, req = {}, resp = {}", request, routeResult);
            if (routeResult.getStatus() != MAP_SUCCESS_CODE) {
                throw new BizException(StringUtils.defaultString(routeResult.getMsg()));
            }
            if(CollectionUtils.isNotEmpty(routeResult.getRoute())){
                return new Result<>(routeResult.getRoute().get(0).getDistance());
            }
            return new Result<>(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
        } catch (Exception e) {
            log.error("invoke mapOpenApiService.riding error, req = {}" , req ,e);
            return new Result<>(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
        }
    }


    @Degrade(rhinoKey = "MapCenterClientImpl.queryRideRidingDistance", fallBackMethod = "queryRideRidingDistanceFallback", timeoutInMilliseconds = 1000)
    @Override
    @LoadTestAop
    public Double queryRideRidingDistance4DrunkHorse(MapRouteRequest req) {

            RouteRequest request = new RouteRequest();
            request.setKey(MccConfigUtils.getDrunkHorseMapReqKey());
            Origin origin = new Origin();
            origin.setLocation(concatLatitudeAndLongitude(req.getOriginLongitude(), req.getOriginLatitude()));
            request.setOrigin(origin);
            Destination destination = new Destination();
            destination.setLocation(concatLatitudeAndLongitude(req.getDestinationLongitude(), req.getDestinationLatitude()));
            request.setDestination(destination);
            //multipath 对应高质量/高性能。multipath=1高性能；multipath=3高质量
            request.setMultipath(3);

            if (GrayConfigUtils.judgeIsGrayStore(req.getTenantId(), req.getStoreId(), GrayKeyEnum.NAVIGATION_COMPLIANCE_LEVEL_SWITCH.getGrayKey(), false)) {
                request.setStrategy(MccConfigUtils.getNewComplianceStrategy());
            } else {
                request.setStrategy(MccConfigUtils.getOldRouteStrategy());
            }

            log.info("invoke mapOpenApiService.riding start, req = {}", request);
            RouteResponse routeResult = null;
            try {
                routeResult = mapOpenApiService4DrunkHorse.riding(request);
            } catch (Exception e) {
                throw new RuntimeException();
            }

            log.info("invoke mapOpenApiService.riding end, req = {}, resp = {}", request, routeResult);
            if (routeResult == null) {
                throw new RuntimeException("response is null");
            }

            if (routeResult.getStatus() != MAP_SUCCESS_CODE) {
                throw new BizException(StringUtils.defaultString(routeResult.getMsg()));
            }

            if (CollectionUtils.isEmpty(routeResult.getRoute())) {
                throw new RuntimeException("routeResult.getRoute is empty");
            }

            return routeResult.getRoute().get(0).getDistance();
    }

    @Override
    @LoadTestAop
    public RouteInfoDTO queryRideRidingInfo4DrunkHorse(MapRouteRequest req) {
        RouteRequest request = new RouteRequest();
        request.setKey(MccConfigUtils.getDrunkHorseMapReqKey());
        Origin origin = new Origin();
        origin.setLocation(concatLatitudeAndLongitude(req.getOriginLongitude(), req.getOriginLatitude()));
        request.setOrigin(origin);
        Destination destination = new Destination();
        destination.setLocation(concatLatitudeAndLongitude(req.getDestinationLongitude(), req.getDestinationLatitude()));
        request.setDestination(destination);
        //multipath 对应高质量/高性能。multipath=1高性能；multipath=3高质量
        request.setMultipath(3);
        if (GrayConfigUtils.judgeIsGrayStore(req.getTenantId(), req.getStoreId(), GrayKeyEnum.NAVIGATION_COMPLIANCE_LEVEL_SWITCH.getGrayKey(), false)) {
            request.setStrategy(MccConfigUtils.getNewComplianceStrategy());
        } else {
            request.setStrategy(MccConfigUtils.getOldRouteStrategy());
        }

        request.setShow_fields(POLYLINE + "|" + ATTRIBUTE);
        log.info("invoke mapOpenApiService.riding start, req = {}", request);
        RouteResponse routeResult = null;
        try {
            routeResult = mapOpenApiService4DrunkHorse.riding(request);
        } catch (Exception e) {
            throw new RuntimeException();
        }

        log.info("invoke mapOpenApiService.riding end, req = {}, resp = {}", request, routeResult);
        if (routeResult == null) {
            throw new RuntimeException("response is null");
        }

        if (routeResult.getStatus() != MAP_SUCCESS_CODE) {
            throw new BizException(StringUtils.defaultString(routeResult.getMsg()));
        }

        if (CollectionUtils.isEmpty(routeResult.getRoute())) {
            throw new RuntimeException("routeResult.getRoute is empty");
        }

        return transRouteInfo(routeResult.getRoute().get(0), req);
    }

    private RouteInfoDTO transRouteInfo(RouteInfo routeInfo, MapRouteRequest req) {
        if (Objects.isNull(routeInfo)) {
            throw new SystemException("路线信息为空");
        }

        if (Objects.isNull(routeInfo.getRoute_id())) {
            log.warn("routeId为空");
            Cat.logEvent("NAVIGATE", "ROUTE_IS_NULL");
        }

        if (StringUtils.isBlank(routeInfo.getPolyline())) {
            log.warn("路线点为空");
            Cat.logEvent("NAVIGATE", "POLYLINE_IS_NULL");
            return new RouteInfoDTO(routeInfo.getDistance(), routeInfo.getRoute_id(), null,
                    new Coordination(req.getOriginLongitude(), req.getOriginLatitude()),
                    new Coordination(req.getDestinationLongitude(), req.getDestinationLatitude()),
                    calNavigationDuration(routeInfo.getDistance()));
        } else {
            //解析坐标点 参考https://km.sankuai.com/collabpage/1116117420
            List<Double> coors = Arrays.stream(routeInfo.getPolyline().split(","))
                    .map(Double::new).collect(Collectors.toList());
            for (int i = 2; i < coors.size() ; i++) {
                coors.set(i, coors.get(i - 2) + coors.get(i) / 1000000);
            }
            return new RouteInfoDTO(routeInfo.getDistance(), routeInfo.getRoute_id(),
                    Joiner.on(",").join(coors),
                    new Coordination(req.getOriginLongitude(), req.getOriginLatitude()),
                    new Coordination(req.getDestinationLongitude(), req.getDestinationLatitude()),
                    calNavigationDuration(routeInfo.getDistance()));
        }

    }

    private Result<Double> queryRidePathDistanceFallback(MapRouteRequest req, Throwable e) {
        log.error("queryRidePathDistanceFallback throw ex", e);
        throw new FallbackException("queryRidePathDistance fallback");
    }

    private Result<Double> queryRideRidingDistanceFallback(MapRouteRequest req, Throwable e) {
        log.error("queryRideRidingDistanceFallback throw ex", e);
        throw new FallbackException("queryRideRidingDistance fallback");
    }


    @Override
    public Result<CoordinatePoint> queryCoordinatesByDetailAddress(String address) {
        return null;
    }

    /**
     * 按地图接口要求拼接地址参数
     * - 经纬度之间以逗号","分隔，如117.500244,40.417801
     * - 经纬度小数点不超过6位
     * @param longitude 经度
     * @param latitude 纬度
     * @return 拼接后的地址
     */
    private String concatLatitudeAndLongitude(String longitude, String latitude) {
        return trimToSix(longitude) + SPLITTER + trimToSix(latitude);
    }

    private String trimToSix(String coordinatePoint) {
        BigDecimal pointBigDecimal = new BigDecimal(coordinatePoint);
        if (pointBigDecimal.scale() > 6) {
            pointBigDecimal = pointBigDecimal.setScale(6, RoundingMode.DOWN);
        }
        return pointBigDecimal.toPlainString();
    }




    /**
     * 计算定价路线时间
     * distance 距离 单位：米
     */
    public static Double calNavigationDuration(Double distance) {
        //电动车速度=17km/h = 4.72m/s
        double electricBicycleSpeed = 4.72;
        return distance / electricBicycleSpeed;
    }
}
