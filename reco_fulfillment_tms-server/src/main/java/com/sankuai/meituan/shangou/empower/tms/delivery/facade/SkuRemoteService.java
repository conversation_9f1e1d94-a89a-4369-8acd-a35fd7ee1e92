package com.sankuai.meituan.shangou.empower.tms.delivery.facade;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryBaseException;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.QuerySkuInfoByIdsCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.ExtendSkuParts;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.SkuInfoWithGroup;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.SkuInfoWithGroupResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerProductThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/12/30
 * desc: 商品服务远程服务
 */
@Service
@Slf4j
public class SkuRemoteService {

    @Resource
    private EmpowerProductThriftService.Iface empowerProductThriftServiceClient;

    public List<SkuInfoWithGroup> findStoreSkuInfoBySkuIds(long tenantId, long storeId, Set<String> skuIds, boolean hasInvalid) {
        log.info("SkuBizServiceImpl.findStoreSkuInfoBySkuIds; tenantId:{} storeId:{} skuIds:{}", tenantId, storeId, skuIds);
        List<SkuInfoWithGroup> skuInfoWithGroupList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(skuIds)) {
            return skuInfoWithGroupList;
        }
        //商品查询服务，数量限制为50条，所以分批调用再组合
        //TODO 查询门店商品，商品服务老接口支持灰度，看时间是否替换
        skuInfoWithGroupList = Lists.partition(new ArrayList<>(skuIds), 50).parallelStream().map(skuIdList ->
                queryStoreSkuInfoBySkuIds(tenantId, storeId, new HashSet<>(skuIdList), hasInvalid)
        ).flatMap(item -> item.stream()).distinct().collect(Collectors.toList());
        return skuInfoWithGroupList;
    }

    private List<SkuInfoWithGroup> queryStoreSkuInfoBySkuIds(long tenantId, long storeId, Set<String> skuCodes, boolean hasInvalid) {
        QuerySkuInfoByIdsCommand querySkuInfoByIdsCommand = new QuerySkuInfoByIdsCommand();
        querySkuInfoByIdsCommand.setTenantId(tenantId);
        if (storeId > 0) {
            querySkuInfoByIdsCommand.setStoreId(storeId);
        }
        querySkuInfoByIdsCommand.setSkuIds(skuCodes);
        querySkuInfoByIdsCommand.setExtendParts(new ExtendSkuParts()
                .setStoreSku(true)
                .setBrandInfo(true)
                .setSortInfo(true)
                .setStatus(true)
                .setUnit(true)
                .setOnlineUnit(true)
                .setSize(true)
                .setPriceTag(true)
                .setChannelSkuList(true));
        querySkuInfoByIdsCommand.setHasInvalid(hasInvalid);

        try {
            log.info("ProductRpcServiceImpl.queryStoreSkuInfoBySkuIds;invoke empowerProductThriftService.querySkuInfoByIds request:{}", querySkuInfoByIdsCommand);
            SkuInfoWithGroupResult skuInfoWithGroupResult = empowerProductThriftServiceClient.querySkuInfoByIds(querySkuInfoByIdsCommand);
            log.info("ProductRpcServiceImpl.queryStoreSkuInfoBySkuIds;invoke empowerProductThriftService.querySkuInfoByIds response:{}", skuInfoWithGroupResult);

            List<SkuInfoWithGroup> skuInfoWithGroups = buildSkuInfoWithGroupListResult(skuInfoWithGroupResult);

            if (CollectionUtils.isNotEmpty(skuInfoWithGroups)) {
                return skuInfoWithGroups.stream().filter(Objects::nonNull).collect(Collectors.toList());
            }
            return Lists.newArrayList();
        } catch (TException e) {
            log.error("ProductRpcServiceImpl.queryStoreSkuInfoBySkuIds;invoke empowerProductThriftService.querySkuInfoByIds e:{}", e);
            throw new DeliveryBaseException("获取商品信息失败");
        }
    }

    private List<SkuInfoWithGroup> buildSkuInfoWithGroupListResult(SkuInfoWithGroupResult result) {

        // 如果商品服务返回接口为空或失败抛业务异常，上层业务按需求处理
        if (result == null || result.getStatus() == null) {
            throw new DeliveryBaseException(-1, "查询门店商品失败, 请稍后重试！");
        }
        if (result.getStatus().getCode() != 0) {
            throw new DeliveryBaseException(-1, result.getStatus().getMsg());
        }

        return result.getSkuInfoWithGroup();
    }

}
