package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.NewSupplyRiderInfoDeliveryRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Repository
public class NewSupplySquirrelRiderInfoDeliveryRepository implements NewSupplyRiderInfoDeliveryRepository {

    @Resource
    private CommonSquirrelRiderInfoDeliveryRepository riderInfoDeliveryRepository;

    @Override
    public Optional<Rider> queryRiderInfo(Long orderId) {
        return riderInfoDeliveryRepository.queryRiderInfo(orderId);
    }

    @Override
    public boolean saveRiderInfo(Long orderId, Rider rider) {
        return riderInfoDeliveryRepository.saveRiderInfo(orderId, rider);
    }

}
