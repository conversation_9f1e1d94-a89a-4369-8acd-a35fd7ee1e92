package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.RiderInfoSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 新供给聚合配送骑手信息缓存，使用时注意：
 * 1、当前针对的是牵牛花自配送识别骑手信息变更的场景
 * 2、骑手信息不是实时的，是自配送状态回传时更新的缓存
 * 3、回传信息里如果没有骑手信息不更新缓存，例如骑手转单已接单->待接单，缓存里存储的还是已接单的骑手信息
 * <AUTHOR>
 */
@Repository
@Slf4j
@Rhino
public class CommonSquirrelRiderInfoDeliveryRepository {

    @Resource
    private RiderInfoSquirrelOperationService riderInfoSquirrelOperationService;

    @MethodLog(logRequest = false, logResponse = true)
    @Degrade(rhinoKey = "CommonSquirrelRiderInfoDeliveryRepository-queryRiderInfo", fallBackMethod = "queryRiderInfoFallBack", timeoutInMilliseconds = 3000)
    public Optional<Rider> queryRiderInfo(Long orderId) {
        String key = String.valueOf(orderId);
        return riderInfoSquirrelOperationService.get(key, Rider.class);
    }

    @MethodLog(logRequest = true, logResponse = false)
    @Degrade(rhinoKey = "CommonSquirrelRiderInfoDeliveryRepository-saveRiderInfo", fallBackMethod = "saveRiderInfoFallBack", timeoutInMilliseconds = 3000)
    public boolean saveRiderInfo(Long orderId, Rider rider) {
        String key = String.valueOf(orderId);
        return riderInfoSquirrelOperationService.set(key, rider, TimeUtil.toSeconds(MccConfigUtils.getDeliveryRiderInfoExpireDays()));
    }

    public Optional<Rider> queryRiderInfoFallBack(Long orderId) {
        log.warn("CommonSquirrelRiderInfoDeliveryRepository queryRiderInfoFallBack degrade");
        return Optional.empty();
    }

    public boolean saveRiderInfoFallBack(Long orderId, Rider rider) {
        log.warn("CommonSquirrelRiderInfoDeliveryRepository saveRiderInfoFallBack degrade");
        return false;
    }

}
