package com.sankuai.meituan.shangou.empower.tms.delivery.enums;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;


/**
 * 淘鲜达平台配送，配送状态与百川统一配送状态映射
 * <AUTHOR>
 */
@Slf4j
public enum TxdOrderChannelDeliveryStatusEnum {

    SHIPPING(1, "SHIPPING", "开始配送"),
    SUCCESS(2, "SUCCESS", "配送完成"),
    ;

    /**
     * 非渠道返回的code，仅用作标识
    */
    private final int code;

    /**
     * 渠道返回的订单状态
     */
    private final String status;

    private final String msg;

    TxdOrderChannelDeliveryStatusEnum(int code, String status, String msg) {
        this.code = code;
        this.status = status;
        this.msg = msg;
    }

    public static TxdOrderChannelDeliveryStatusEnum enumOf(String status) {
        for (TxdOrderChannelDeliveryStatusEnum each : values()) {
            if (Objects.equals(each.getStatus(), status)) {
                return each;
            }
        }
        return null;
    }

    public static TxdOrderChannelDeliveryStatusEnum enumOf(int code) {
        for (TxdOrderChannelDeliveryStatusEnum each : values()) {
            if (each.getCode() == code) {
                return each;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }
    public String getStatus() {
        return status;
    }
    public String getMsg() {
        return msg;
    }

    public static Optional<DeliveryStatusEnum> mapToQnhDeliveryStatus(String status) {
        if (StringUtils.isEmpty(status)) {
            log.error("TxdOrderChannelDeliveryStatusEnum mapToDeliveryStatus error, status is empty");
            return Optional.empty();
        }

        TxdOrderChannelDeliveryStatusEnum txdDeliveryStatusEnum = enumOf(status);
        if (Objects.isNull(txdDeliveryStatusEnum)) {
            log.error("TxdOrderChannelDeliveryStatusEnum mapToDeliveryStatus error, txdDeliveryStatusEnum is empty, status is {}", status);
            return Optional.empty();
        }

        return txdDeliveryStatusEnum.map2QnhDeliveryStatus();
    }

    public static Optional<DeliveryStatusEnum> mapToQnhDeliveryStatus(int statusCode) {
        TxdOrderChannelDeliveryStatusEnum txdDeliveryStatusEnum = enumOf(statusCode);
        if (Objects.isNull(txdDeliveryStatusEnum)) {
            log.error("TxdOrderChannelDeliveryStatusEnum mapToDeliveryStatus error, txdDeliveryStatusEnum is empty, statusCode is {}", statusCode);
            return Optional.empty();
        }

        return txdDeliveryStatusEnum.map2QnhDeliveryStatus();
    }

    private Optional<DeliveryStatusEnum> map2QnhDeliveryStatus() {
        switch (this) {
            case SHIPPING:
                return Optional.of(DeliveryStatusEnum.RIDER_TAKEN_GOODS);
            case SUCCESS:
                return Optional.of(DeliveryStatusEnum.DELIVERY_DONE);
            default:
                return Optional.empty();
        }
    }

}
