package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryOperationApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryProcessApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryMonitorDomainService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.BookingOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.PickSelectRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryLaunchCommandMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryTimeOutCheckMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.OrderCompensationFinishMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

@Service
@Slf4j
public class OrderCompensationFinishMessageListener extends AbstractLaunchDeliveryListener{

    @Resource
    private OrderSystemClient orderSystemClient;
    @Resource
    private DeliveryProcessApplicationService deliveryProcessApplicationService;

    @Resource
    private DeliveryOperationApplicationService deliveryOperationApplicationService;

    @Resource
    private MafkaDelayMessageProducer<DeliveryTimeOutCheckMessage> deliveryTimeOutCheckProducer;

    @Resource
    private DeliveryRemindConfigRepository deliveryRemindConfigRepository;

    @Resource
    private TenantRemoteService tenantRemoteService;

    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    @Resource
    private DeliveryMonitorDomainService deliveryMonitorDomainService;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private PickSelectRemoteService pickSelectRemoteService;

    @Resource
    private MafkaDelayMessageProducer<DeliveryLaunchCommandMessage> deliveryDelayLaunchMessageProducer;


    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.ORDER_COMPENSATION_CONSUMER;
    }
    @Override
    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("开始订单降级补偿完成消息: {}", message);
        OrderCompensationFinishMessage msg = translateMessage(message);
        if (msg == null) {
            return CONSUME_SUCCESS;
        }
        if (StringUtils.isEmpty(msg.getNotifyType()) || !msg.getNotifyType().equals(NotifyTypeEnums.COMPENSATE_DEMOTION.getValue())) {
            return CONSUME_SUCCESS;
        }
        if (msg.getCompensateStatus() == null || msg.getCompensateStatus() != DegradeCompensateStatus.PROCESSED.getCode()) {
            return CONSUME_SUCCESS;
        }
        if (StringUtils.isEmpty(msg.getDegradeModules())) {
            return CONSUME_SUCCESS;
        }
        List<Integer> needDownType = MccConfigUtils.getNeedDownType();
        if (CollectionUtils.isEmpty(needDownType)) {
            return CONSUME_SUCCESS;
        }
        try {
            List<Integer> degradeModuleList = Arrays.stream(msg.getDegradeModules().split(",")).map(Integer::valueOf).collect(Collectors.toList());
            List<Integer> downType = needDownType.stream().filter(degradeModuleList::contains).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(downType)) {
                return CONSUME_SUCCESS;
            }
            Result<OrderInfo> orderInfoResult = getOrderSystemClient().getOrderInfo(new OrderKey(msg.getTenantId(), msg.getPoiId(), msg.getOrderId()), false);
            if (orderInfoResult.isFail()) {
                log.info("订单查询失败,{}",message);
                return CONSUME_SUCCESS;
            }
            OrderInfo orderInfo = orderInfoResult.getInfo();
            if(orderInfo==null){
                return CONSUME_SUCCESS;
            }
            if (orderInfo.getOrderStatus().equals(OrderStatusEnum.CANCELED.getValue()) || orderInfo.getOrderStatus().equals(OrderStatusEnum.COMPLETED.getValue())) {
                return CONSUME_SUCCESS;
            }
            ImmediateOrderDeliveryLaunchPointEnum immediateOrderDeliveryLaunchPointEnum = ImmediateOrderDeliveryLaunchPointEnum.MERCHANT_ACCEPT;
            BookingOrderDeliveryLaunchPointEnum beforeDelivery = BookingOrderDeliveryLaunchPointEnum.BEFORE_DELIVERY;
            Integer orderStatus = orderInfo.getOrderStatus();
            if (orderStatus.equals(OrderStatusEnum.PICKING.getValue()) && orderInfo.getPickStatus() == com.meituan.shangou.saas.order.platform.enums.DeliveryStatusEnum.PICKED.getValue()) {
                immediateOrderDeliveryLaunchPointEnum = ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE;
                beforeDelivery = BookingOrderDeliveryLaunchPointEnum.PICK_DONE;
            }
            return tryLaunchDelivery(
                    message,
                    orderInfo.getOrderKey(),
                    immediateOrderDeliveryLaunchPointEnum,
                    beforeDelivery
            );
        } catch (Exception e) {
            log.error("处理消息失败", e);
            return CONSUME_FAILURE;
        }
    }
    private OrderCompensationFinishMessage translateMessage(MafkaMessage mafkaMessage) {
        try {
            OrderCompensationFinishMessage message = translateMessageWithBodyHolder(mafkaMessage, OrderCompensationFinishMessage.class);
            Preconditions.checkNotNull(message, "empty mafkaMessage");
            return message;

        } catch (Exception e) {
            log.error("解析消息失败:{}", mafkaMessage, e);
            return null;
        }
    }

    @Override
    protected OrderSystemClient getOrderSystemClient() {
        return orderSystemClient;
    }

    @Override
    protected DeliveryProcessApplicationService getDeliveryProcessApplicationService() {
        return deliveryProcessApplicationService;
    }

    @Override
    protected MafkaDelayMessageProducer<DeliveryLaunchCommandMessage> getDeliveryDelayLaunchMessageProducer() {
        return deliveryDelayLaunchMessageProducer;
    }

    @Override
    protected DeliveryOperationApplicationService getDeliveryOperationApplicationService() {
        return deliveryOperationApplicationService;
    }

    @Override
    protected MafkaDelayMessageProducer<DeliveryTimeOutCheckMessage> getDeliveryTimeOutCheckProducer() {
        return deliveryTimeOutCheckProducer;
    }

    @Override
    protected DeliveryMonitorDomainService getDeliveryMonitorDomainService() {
        return deliveryMonitorDomainService;
    }

    @Override
    protected DeliveryPoiRepository getDeliveryPoiRepository() {
        return deliveryPoiRepository;
    }

    @Override
    protected DeliveryOrderRepository getDeliveryOrderRepository() {
        return deliveryOrderRepository;
    }
    @Override
    protected TenantRemoteService getTenantRemoteService(){
        return tenantRemoteService;
    }

    @Override
    protected DeliveryRemindConfigRepository getDeliveryRemindConfigRepository() {
        return deliveryRemindConfigRepository;
    }

    @Override
    protected PickSelectRemoteService getPickSelectRemoteService() {
        return pickSelectRemoteService;
    }
}
