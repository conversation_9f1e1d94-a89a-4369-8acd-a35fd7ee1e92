package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.DevlieryNotifyContent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryCallbackTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryChangeCallbackInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryUnifiedCallbackMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * 渠道配送回调监听
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/14
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class OCMSChannelDeliveryNotifyMessageListener extends AbstractDeadLetterConsumer {

	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private MafkaMessageProducer<DeliveryUnifiedCallbackMessage> deliveryUnifiedCallbackMessageProducer;

	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.DELIVERY_CALLBACK;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {

		if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
			log.error("开始消费配送回调消息 已废弃: {} ", mafkaMessage);
			return CONSUME_SUCCESS;
		}

		log.info("开始消费配送回调消息: {}", mafkaMessage);
		DevlieryNotifyContent message = translateMessage(mafkaMessage);
		if (message == null) {
			return CONSUME_SUCCESS;
		}

		try {
			DeliveryOrder deliveryOrder = deliveryOrderRepository.getDeliveryOrder(
					ThirdDeliveryChannelEnum.valueOf(message.getChannelCode()),
					message.getChannelDeliveryId()
			);
			if (deliveryOrder == null) {
				log.error("消费配送回调消息失败, 运单不存在, mafkaMessage:{}", mafkaMessage);
				Cat.logEvent("DELIVERY_CALLBACK_CONSUME_FAILED", "DELIVERY_ORDER_NOT_EXIST");
				return CONSUME_SUCCESS;
			}

			DeliveryEventEnum event = DeliveryEventEnum.getEventByStatus(DeliveryStatusEnum.valueOf(message.getStatus()));
			if (event != null) {
				deliveryUnifiedCallbackMessageProducer.sendMessage(
						new DeliveryUnifiedCallbackMessage(
								DeliveryCallbackTypeEnum.CHANGE_CALLBACK,
								new DeliveryChangeCallbackInfo(
										deliveryOrder.getOrderId(),
										deliveryOrder.getDeliveryChannel(),
										deliveryOrder.getChannelDeliveryId(),
										deliveryOrder.getChannelServicePackageCode(),
										event,
										new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.valueOf(message.getExceptionType()), message.getExceptionDesc()),
										message.getTimestamp() != 0 ? TimeUtil.fromMilliSeconds(message.getTimestamp()) : LocalDateTime.now(),
										parseRiderInfo(message),
										null,
										null
								)
						),
						deliveryOrder.getOrderId()
				);
			}

			return CONSUME_SUCCESS;

		} catch (Exception e) {
			log.error("消费配送回调消息失败，将会进行重试消费", e);
			return CONSUME_FAILURE;
		}
	}

	private Rider parseRiderInfo(DevlieryNotifyContent message) {
		return StringUtils.isNotEmpty(message.getRiderName()) && StringUtils.isNotEmpty(message.getRiderPhone()) ?
				new Rider(message.getRiderName(), message.getRiderPhone(), null) : null;
	}

	private DevlieryNotifyContent translateMessage(MafkaMessage mafkaMessage) {
		try {
			DevlieryNotifyContent message = translateMessage(mafkaMessage, DevlieryNotifyContent.class);
			Preconditions.checkNotNull(message, "empty mafkaMessage");
			Preconditions.checkNotNull(ThirdDeliveryChannelEnum.valueOf(message.getChannelCode()), "channelCode is invalid");
			Preconditions.checkNotNull(message.getChannelDeliveryId(), "channelDeliveryId is null");
			Preconditions.checkNotNull(DeliveryStatusEnum.valueOf(message.getStatus()), "status is invalid");
			return message;

		} catch (Exception e) {
			log.error("解析配送回调消息失败:{}", mafkaMessage, e);
			Cat.logEvent("DELIVERY_CALLBACK_CONSUME_FAILED", "MESSAGE_WRONG");
			return null;
		}
	}
}
