package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryOperationApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.shangou.logistics.warehouse.enums.CompensateType;
import com.sankuai.shangou.logistics.warehouse.message.CompensateFulfillmentOrderMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * <AUTHOR>
 * @date 2024-09-05
 * @email <EMAIL>
 */
@Slf4j
@Component
public class CompensateFulfillmentOrderConsumer extends AbstractDeadLetterConsumer {

    @Resource
    private DeliveryOperationApplicationService deliveryOperationApplicationService;
    @Resource
    private OrderSystemClient orderSystemClient;
    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.COMPENSATE_FULFILLMENT_ORDER_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage message) {

        log.info("CompensateFulfillmentOrderConsumer.consume, message = {}", message);
        CompensateFulfillmentOrderMessage compensateFulfillmentOrderMessage = translateMessage(message, CompensateFulfillmentOrderMessage.class);
        if (Objects.isNull(compensateFulfillmentOrderMessage)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        CompensateType compensateType = CompensateType.valueOfCode(compensateFulfillmentOrderMessage.getCompensateForPickType());
        switch (compensateType) {
            case COMPENSATE_FOR_NEW_DELIVERY: {
                Optional<DeliveryPoi> deliveryPoiOptional = deliveryPoiRepository.queryDeliveryPoi(compensateFulfillmentOrderMessage.getMerchantId(), compensateFulfillmentOrderMessage.getWarehouseId());
                if (!deliveryPoiOptional.isPresent()) {
                    log.warn("门店[{}]未开通任何配送平台, 将会放弃发起配送.", compensateFulfillmentOrderMessage.getWarehouseId());
                    return CONSUME_SUCCESS;
                }
                Result<OrderInfo> orderInfoQueryResult = orderSystemClient.getOrderInfo(
                        compensateFulfillmentOrderMessage.getMerchantId(), compensateFulfillmentOrderMessage.getWarehouseId(),
                        compensateFulfillmentOrderMessage.getTradeOrderNo(), DynamicOrderBizType.findOf(compensateFulfillmentOrderMessage.getTradeOrderBizType()).getChannelId(), false
                );
                if (orderInfoQueryResult.isFail()) {
                    log.warn("查询订单失败，等待重试");
                    return CONSUME_FAILURE;
                }

                deliveryOperationApplicationService.launchDelivery(deliveryPoiOptional.get(), orderInfoQueryResult.getInfo(), false, PlatformSourceEnum.OMS, null);
                break;
            }
            default:
                break;
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
