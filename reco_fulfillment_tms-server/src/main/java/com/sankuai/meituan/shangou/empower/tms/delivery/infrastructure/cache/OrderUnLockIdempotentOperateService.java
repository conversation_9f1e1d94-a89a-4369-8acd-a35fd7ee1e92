package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache;

import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class OrderUnLockIdempotentOperateService extends OFCSquirrelOperateService{

    private static final String CATEGORY_NAME = "order_unlock_idempotent";

    @Override
    public String getCategoryName() {
        return CATEGORY_NAME;
    }

    public boolean check(String orderId){
        Optional<String> val = get(orderId,String.class);
        return val.isPresent();
    }

    public void setCheck(String orderId){
        set(orderId,"1");
    }
}
