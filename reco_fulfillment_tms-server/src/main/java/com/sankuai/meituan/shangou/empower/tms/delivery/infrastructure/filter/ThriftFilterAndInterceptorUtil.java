package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.filter;

import com.alibaba.fastjson.JSON;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * Create by yujing10 on 2020/2/20.
 */
@Slf4j
public class ThriftFilterAndInterceptorUtil {


    /**
     * 解析thrift接口中的基础类型（String、Integer等）的参数值
     * @param args
     * @param paramName
     * @return
     */
    public static String resolveBasicParamValue(Object[] args, String paramName) {
        String value = "-1";
        try {
            if (args != null && args.length > 0) {
                for (Object arg : args) {
                    if (arg != null) {
                        value = resolveBasicParamValue(arg, paramName);
                        if (StringUtils.isNotBlank(value)) {
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            //解析异常打日志
            log.error("resolveBasicParamValue error, msg:{}", e.getMessage(), e);
        }

        return value;
    }

    /**
     * 解析thrift接口中的基础类型（String、Integer等）的参数值
     * @param arg
     * @param paramName
     * @return
     */
    public static String resolveBasicParamValue(Object arg, String paramName) {
        String jsonStr = JSON.toJSONString(arg);//将参数先转成JSON格式的字符串
        //log.info("resolveBasicParamValue arg is {}", arg);
        String regex = "\"" + paramName + "\":";
        int index = jsonStr.indexOf(regex);
        if (index < 0) {//参数不存在
            return "";
        }
        char tmpChar = jsonStr.charAt(index + regex.length());
        if (tmpChar == '[' || tmpChar == '{') {//paramName不是基本类型的参数
            return "";
        }
        if (tmpChar == '"') {//字符串类型
            String subString = jsonStr.substring(index + regex.length() + 1);
            return subString.substring(0, subString.indexOf('"'));
        } else {//数字类型，则该参数值的结尾可能是, ] }三种中的一种
            String subString = jsonStr.substring(index + regex.length());
            int commaIndex = subString.indexOf(',');
            commaIndex = commaIndex < 0 ? Integer.MAX_VALUE : commaIndex;
            int bracketIndex = subString.indexOf(']');
            bracketIndex = bracketIndex < 0 ? Integer.MAX_VALUE : bracketIndex;
            int braceIndex = subString.indexOf('}');
            braceIndex = braceIndex < 0 ? Integer.MAX_VALUE : braceIndex;
            return subString.substring(0, Math.min(Math.min(commaIndex, bracketIndex), braceIndex));
        }
    }
}
