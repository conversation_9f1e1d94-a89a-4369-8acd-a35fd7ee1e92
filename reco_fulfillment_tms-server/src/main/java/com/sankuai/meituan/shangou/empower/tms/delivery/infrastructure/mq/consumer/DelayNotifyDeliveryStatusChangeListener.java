package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.google.common.base.Objects;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DelayNotifyDeliveryStatusMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/9/21 20:51
 **/
@Slf4j
@Component
public class DelayNotifyDeliveryStatusChangeListener extends AbstractDeadLetterConsumer {

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private OrderSystemClient orderSystemClient;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.DELAY_NOTIFY_DELIVERY_STATUS_CONSUMER;
    }


    @Override
    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("开始消费延迟配送状态变更消息: {}", message);
        DelayNotifyDeliveryStatusMessage msg = translateMessage(message);
        if (msg == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        DeliveryOrder deliveryOrder = null;
        if(MccConfigUtils.getDeliveryQueryTenantSwitch(msg.getTenantId())){
            deliveryOrder = deliveryOrderRepository.getDeliveryOrderWithTenant(msg.getDeliveryOrderId(),msg.getTenantId(),msg.getStoreId());
        }else {
            deliveryOrder = deliveryOrderRepository.getDeliveryOrder(msg.getDeliveryOrderId());
        }

        if (deliveryOrder == null || !Objects.equal(deliveryOrder.getStatus(), msg.getChangeDeliveryStatus())
            || (deliveryOrder.getRiderInfo() != null && !Objects.equal(msg.getRiderAccountName(), deliveryOrder.getRiderInfo().getRiderName()))) {
            log.info("运单状态已发生变化，不同步配送状态, message:{}, deliveryOrder:{}", msg, deliveryOrder);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        orderSystemClient.syncDeliveryChangeToOrderSystem(deliveryOrder);

        return ConsumeStatus.CONSUME_SUCCESS;

    }

    private DelayNotifyDeliveryStatusMessage translateMessage(MafkaMessage mafkaMessage) {
        try {
            DelayNotifyDeliveryStatusMessage message = translateMessage(mafkaMessage, DelayNotifyDeliveryStatusMessage.class);
            Preconditions.checkNotNull(message, "empty mafkaMessage");
            Preconditions.checkNotNull(message.getDeliveryOrderId(), "deliveryOrderId is null");
            Preconditions.checkNotNull(message.getChangeDeliveryStatus(), "status is null");
            return message;

        } catch (Exception e) {
            log.error("解析消息失败:{}", mafkaMessage, e);
            return null;
        }
    }
}
