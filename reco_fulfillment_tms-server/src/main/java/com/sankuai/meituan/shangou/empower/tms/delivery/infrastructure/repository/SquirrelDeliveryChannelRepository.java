package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.dianping.squirrel.client.StoreKey;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChannelRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.DeliveryChannelSquirrelOperationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@Rhino
public class SquirrelDeliveryChannelRepository implements DeliveryChannelRepository {

    private static final String KEY_DELIMITER = "_";

    @Autowired
    private DeliveryChannelSquirrelOperationService deliveryChannelSquirrelOperationService;

    @Override
    @Degrade(rhinoKey = "SquirrelDeliveryChannelRepository-getDeliveryChannelByLogisticMark", fallBackMethod = "getDeliveryChannelByLogisticMarkFallBack", timeoutInMilliseconds = 3000)
    public Optional<DeliveryChannel> getDeliveryChannelByLogisticMark(Integer deliveryPlatFormCode,
                                                                      Integer orderChannelCode, String logisticMark) {
        String deliveryChannelKey = getLogisticMarkSquirrelKey(deliveryPlatFormCode, orderChannelCode, logisticMark);
        return deliveryChannelSquirrelOperationService.get(deliveryChannelKey, DeliveryChannel.class);
    }

    @Override
    @Degrade(rhinoKey = "SquirrelDeliveryChannelRepository-getDeliveryChannelByCarrierCode", fallBackMethod = "getDeliveryChannelByCarrierCodeFallBack", timeoutInMilliseconds = 3000)
    public Optional<DeliveryChannel> getDeliveryChannelByCarrierCode(Integer carrierCode) {
        String deliveryChannelKey = String.valueOf(carrierCode);
        return deliveryChannelSquirrelOperationService.get(deliveryChannelKey, DeliveryChannel.class);
    }

    @Override
    @Degrade(rhinoKey = "SquirrelDeliveryChannelRepository-getDeliveryChannelMapByCarrierCodeSet", fallBackMethod = "getDeliveryChannelMapByCarrierCodeSetFallBack", timeoutInMilliseconds = 3000)
    public Map<Integer, Optional<DeliveryChannel>> getDeliveryChannelMapByCarrierCodeSet(Set<Integer> carrierCodeSet) {
        if (CollectionUtils.isEmpty(carrierCodeSet)) {
            return Maps.newHashMap();
        }

        try {
            List<String> carrierCodeStrList = carrierCodeSet.stream().map(String::valueOf).collect(Collectors.toList());
            Map<StoreKey, Optional<DeliveryChannel>> deliveryChannelMap = deliveryChannelSquirrelOperationService.multiGetWithMiss(carrierCodeStrList, DeliveryChannel.class);
            return deliveryChannelMap.entrySet().stream().collect(Collectors.toMap(entry -> Integer.valueOf(String.valueOf(entry.getKey().getParams()[0])), Map.Entry::getValue));
        } catch (Exception e) {
            log.error("SquirrelDeliveryChannelRepository getDeliveryChannelListByCarrierCodeSet error", e);
            return Maps.newHashMap();
        }
    }

    @Override
    @Degrade(rhinoKey = "SquirrelDeliveryChannelRepository-updateAllDeliveryChannelList", fallBackMethod = "updateAllDeliveryChannelListFallBack", timeoutInMilliseconds = 3000)
    public Boolean updateAllDeliveryChannelList(List<DeliveryChannel> allDeliveryChannelList) {
        Map<String, DeliveryChannel> deliveryChannelMap = Maps.newHashMap();
        allDeliveryChannelList.forEach(deliveryChannel -> {
            deliveryChannelMap.put(String.valueOf(deliveryChannel.getCarrierCode()), deliveryChannel);
            deliveryChannelMap.put(getLogisticMarkSquirrelKey(deliveryChannel.getDeliveryPlatFormCode(),
                    deliveryChannel.getOrderChannelCode(), deliveryChannel.getLogisticMark()), deliveryChannel);
        });

        return deliveryChannelSquirrelOperationService.multiSet(deliveryChannelMap);
    }

    private String getLogisticMarkSquirrelKey(Integer deliveryPlatFormCode, Integer orderChannelCode, String logisticMark) {
        return StringUtils.join(new String[]{String.valueOf(deliveryPlatFormCode),
                String.valueOf(Objects.isNull(orderChannelCode) ? NumberUtils.INTEGER_ZERO : orderChannelCode),
                logisticMark}, KEY_DELIMITER);
    }

    public Optional<DeliveryChannel> getDeliveryChannelByLogisticMarkFallBack(Integer deliveryPlatFormCode, Integer orderChannelCode, String logisticMark) {
        log.warn("get deliveryChannel by logisticMark degrade");
        return Optional.ofNullable(DeliveryChannel.translateFromDeliveryChannelEnum(deliveryPlatFormCode, orderChannelCode, logisticMark));
    }

    public Optional<DeliveryChannel> getDeliveryChannelByCarrierCodeFallBack(Integer carrierCode) {
        log.warn("get deliveryChannel by carrierCode degrade");
        return Optional.ofNullable(DeliveryChannel.translateFromDeliveryChannelEnum(carrierCode));
    }

    public Map<Integer, Optional<DeliveryChannel>> getDeliveryChannelMapByCarrierCodeSetFallBack(Set<Integer> carrierCodeSet) {
        log.warn("get deliveryChannel by carrierCode set degrade");
        List<DeliveryChannel> deliveryChannelList = DeliveryChannel.translateFromDeliveryChannelEnum(carrierCodeSet);
        return deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, Optional::ofNullable));
    }

    public Boolean updateAllDeliveryChannelListFallBack(List<DeliveryChannel> allDeliveryChannelList) {
        log.warn("update all deliveryChannel list degrade");
        return true;
    }
}
