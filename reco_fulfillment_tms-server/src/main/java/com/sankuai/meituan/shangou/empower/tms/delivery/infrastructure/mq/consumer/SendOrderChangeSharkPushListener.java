package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.utils.ChannelTypeConvertUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.auth.AuthClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryAsyncOutTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.SharkPushSendSceneEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.SharkPushOperateTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.push.BasePushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.push.PushClientImpl;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import javafx.beans.binding.ObjectExpression;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/21 11:57
 **/
@Component
@Slf4j
@SuppressWarnings("rawtypes")
public class SendOrderChangeSharkPushListener extends AbstractDeadLetterConsumer {

    @Resource
    private BasePushClient basePushClient;

    @Resource
    private PushClientImpl pushClient;


    private final int ACCEPT_DELIVERY_ORDER = 1;
    private final int TRANS_DELIVERY_ORDER = 2;

    // 牵牛花 push appId
    private final Integer SHU_GUO_PAI_PUSH_APP_ID = 5;

    //自营配送骑手新任务-拣配任务 tab 权限码
    private final String SELF_RIDER_NEW_PICK_DELIVERY_TASK_TAB_AUTH_CODE = "NEW_TASK_PICK_DELIVERY";
    //自营配送骑手工作台新任务 tab 权限码
    private final String WORK_BENCH_NEW_TASK_TAB_AUTH_CODE = "DELIVERY_SELF_TASK_MENU";

    //三方配送 tab 权限码
    private final String THIRD_DELIVERY_TAB_AUTH_CODE = "THIRD_ORDER_QUERYABLE";

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.SEND_DELIVERY_ORDER_CHANGE_SHARK_PUSH_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage messageStr) {
        log.info("开始消费运单变更消息, message: {}",  messageStr);
        DeliveryChangeSyncOutMessage message = translateMessage(messageStr);
        if (message == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        if(Objects.isNull(message.getHead()) ||
                !com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.isOrderInteractionOptimizeGrayStore(message.getHead().getShopId())) {
            log.info("非灰度门店,不消费");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        if (Objects.isNull(message.getChangeType()) || Objects.isNull(DeliveryAsyncOutTypeEnum.getInstance(message.getChangeType()))) {
            log.error("消息类型不合法,放弃消费");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        DeliveryAsyncOutTypeEnum asyncOutTypeEnum = DeliveryAsyncOutTypeEnum.getInstance(message.getChangeType());

        if (asyncOutTypeEnum == null) {
            log.error("消息类型不识别,放弃消费");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        switch (asyncOutTypeEnum) {
            case DELIVERY_ACCEPT_OR_RIDER_CHANGE:
                handleDeliveryAcceptOrRiderChange(message);
                break;
//            case DELIVER_COMPLETE:
//                handleDeliveryComplete(message);
//                break;
            case TRANS_TO_THIRD_PART_DELIVERY:
                handleTransToThirdPartDelivery(message);
                break;
            case TRANS_TO_SELF_DELIVERY:
                handleTransToSelfDelivery(message);
                break;
            case DELIVERY_CANCEL:
                handleDeliveryCancel(message);
                break;
            case PICK_DELIVERY_SPLIT:
                handlePickDeliverySplit(message);
                break;
            case TAKE_AWAY:
                handleTakeAway(message);
                break;
            case DELIVERY_DONE_BY_ORDER_FINISH:
                handleDeliveryDoneByOrderFinish(message);
                break;
            case THIRD_PART_DELIVERY_ORDER_CHANGE:
                handleThirdPartDeliveryOrderChange(message);
                break;
            case PICK_DONE_IN_PICK_DELIVERY_SPLIT:
                handlePickDoneInPickDeliverySplit(message);
                break;
            default:
                break;
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private DeliveryChangeSyncOutMessage translateMessage(MafkaMessage message) {
        try {
            return super.translateMessage(message, new TypeReference<DeliveryChangeSyncOutMessage>(){});
        } catch (Exception e) {
            log.error("解析运单变更同步消息失败:{}", message);
            Cat.logEvent("DELIVERY_CHANGE_SYNC_OUT_CONSUME_FAILED", "MESSAGE_WRONG");
            return null;
        }
    }

    private void handleDeliveryAcceptOrRiderChange(DeliveryChangeSyncOutMessage message) {
        DeliveryChangeSyncOutMessage.Head head = message.getHead();

        DeliveryChangeSyncOutMessage.DeliveryRiderChangeBody body =
                JSON.parseObject(message.getBody(), DeliveryChangeSyncOutMessage.DeliveryRiderChangeBody.class);

        //骑手接单
        if (Objects.equals(body.getEventType(), ACCEPT_DELIVERY_ORDER)) {
            //给接单骑手发push
            String content = buildDeliveryOrderChangePushContent(head.getTenantId(),
                    head.getShopId(),
                    SharkPushSendSceneEnum.RIDER_ACCEPT_ORDER,
                    head.getChannelOrderId(),
                    head.getOrderBizType(),
                    SharkPushOperateTypeEnum.UPDATE);
            //fixme：似乎这里没用，tenantId赋成了accountId，先留着吧。
            basePushClient.unifiedSendSharkPush(body.getRiderAccountId(), Collections.singletonList(body.getRiderAccountId()),
                    SHU_GUO_PAI_PUSH_APP_ID.toString(), content);


            //给其他骑手发push
            List<Long> hasNewTaskAuthRiderList = new ArrayList<>();
            if (DeliveryRiderMccConfigUtils.fusionSelfDegreeSwitch()) {
                hasNewTaskAuthRiderList = pushClient.queryAccountListByPoiAndAuth(head.getTenantId(), head.getShopId(),
                        SELF_RIDER_NEW_PICK_DELIVERY_TASK_TAB_AUTH_CODE, SHU_GUO_PAI_PUSH_APP_ID);
            } else {
                //给其他有“订单-新任务”骑手发push && 给其他有“工作台-新任务”骑手发push
                hasNewTaskAuthRiderList = pushClient.queryAccountListByPoiAndAuthList(head.getTenantId(), head.getShopId(),
                        Lists.newArrayList(SELF_RIDER_NEW_PICK_DELIVERY_TASK_TAB_AUTH_CODE, WORK_BENCH_NEW_TASK_TAB_AUTH_CODE), SHU_GUO_PAI_PUSH_APP_ID);
            }



            List<Long> otherRiderAccountId = hasNewTaskAuthRiderList.stream()
                    .filter(accountId -> !Objects.equals(body.getRiderAccountId(), accountId))
                    .distinct()
                    .collect(Collectors.toList());
            String contentToOtherRider = buildDeliveryOrderChangePushContent(head.getTenantId(),
                    head.getShopId(),
                    SharkPushSendSceneEnum.RIDER_ACCEPT_ORDER,
                    head.getChannelOrderId(),
                    head.getOrderBizType(),
                    SharkPushOperateTypeEnum.DELETE);
            basePushClient.unifiedSendSharkPush(head.getTenantId(), otherRiderAccountId,
                    SHU_GUO_PAI_PUSH_APP_ID.toString(), contentToOtherRider);
        }

        if (Objects.equals(body.getEventType(), TRANS_DELIVERY_ORDER)) {
            //给转出骑手发push
            basePushClient.unifiedSendSharkPush(head.getTenantId(), Collections.singletonList(body.getOldRiderAccountId()),
                    SHU_GUO_PAI_PUSH_APP_ID.toString(),
                    buildDeliveryOrderChangePushContent(head.getTenantId(), head.getShopId(),
                            SharkPushSendSceneEnum.TRANS_ORDER_FORM,
                            head.getChannelOrderId(),
                            head.getOrderBizType(), SharkPushOperateTypeEnum.DELETE));

            //给转入骑手发push
            basePushClient.unifiedSendSharkPush(head.getTenantId(), Collections.singletonList(body.getRiderAccountId()),
                    SHU_GUO_PAI_PUSH_APP_ID.toString(),
                    buildDeliveryOrderChangePushContent(head.getTenantId(), head.getShopId(),
                            SharkPushSendSceneEnum.TRANS_ORDER_TO,
                            head.getChannelOrderId(),
                            head.getOrderBizType(), SharkPushOperateTypeEnum.ADD));
        }
    }

    private void handleDeliveryComplete(DeliveryChangeSyncOutMessage message) {
        DeliveryChangeSyncOutMessage.Head head = message.getHead();
        DeliveryChangeSyncOutMessage.DeliveryCompleteBody body =
                JSON.parseObject(message.getBody(), DeliveryChangeSyncOutMessage.DeliveryCompleteBody.class);


        String content = buildDeliveryOrderChangePushContent(head.getTenantId(),
                head.getShopId(),
                SharkPushSendSceneEnum.DELIVERY_COMPLETE,
                head.getChannelOrderId(),
                head.getOrderBizType(),
                SharkPushOperateTypeEnum.DELETE);

        basePushClient.unifiedSendSharkPush(head.getTenantId(), Collections.singletonList(body.getRiderAccountId()),
                SHU_GUO_PAI_PUSH_APP_ID.toString(), content);

    }

    private void handleDeliveryCancel(DeliveryChangeSyncOutMessage message) {
        DeliveryChangeSyncOutMessage.Head head = message.getHead();
        DeliveryChangeSyncOutMessage.DeliveryCancelBody body =
                JSON.parseObject(message.getBody(), DeliveryChangeSyncOutMessage.DeliveryCancelBody.class);


        String content = buildDeliveryOrderChangePushContent(head.getTenantId(),
                head.getShopId(),
                SharkPushSendSceneEnum.DELIVERY_CANCEL,
                head.getChannelOrderId(),
                head.getOrderBizType(),
                SharkPushOperateTypeEnum.DELETE);

        List<Long> sendAccountIdList ;
        //配送单被领取就发给当前骑手 未被领取就发给所有有拣配权限的骑手
        if (Objects.nonNull(body.getCurrentRiderAccountId())) {
            sendAccountIdList = Collections.singletonList(body.getCurrentRiderAccountId());
        } else {
            if (DeliveryRiderMccConfigUtils.fusionSelfDegreeSwitch()) {
                sendAccountIdList = pushClient.queryAccountListByPoiAndAuth(head.getTenantId(), head.getShopId(),
                        SELF_RIDER_NEW_PICK_DELIVERY_TASK_TAB_AUTH_CODE, SHU_GUO_PAI_PUSH_APP_ID);
            } else {
                sendAccountIdList = pushClient.queryAccountListByPoiAndAuthList(head.getTenantId(), head.getShopId(),
                        Lists.newArrayList(SELF_RIDER_NEW_PICK_DELIVERY_TASK_TAB_AUTH_CODE, WORK_BENCH_NEW_TASK_TAB_AUTH_CODE), SHU_GUO_PAI_PUSH_APP_ID);
            }

        }

        basePushClient.unifiedSendSharkPush(head.getTenantId(), sendAccountIdList, SHU_GUO_PAI_PUSH_APP_ID.toString(), content);

    }

    private void handleTransToThirdPartDelivery(DeliveryChangeSyncOutMessage message) {
        DeliveryChangeSyncOutMessage.Head head = message.getHead();
        DeliveryChangeSyncOutMessage.TransToThirdPartDeliveryBody body =
                JSON.parseObject(message.getBody(), DeliveryChangeSyncOutMessage.TransToThirdPartDeliveryBody.class);


        String content = buildDeliveryOrderChangePushContent(head.getTenantId(),
                head.getShopId(),
                SharkPushSendSceneEnum.TRANS_TO_THIRD_PART_DELIVERY,
                head.getChannelOrderId(),
                head.getOrderBizType(),
                SharkPushOperateTypeEnum.DELETE);

        //订单被领取了就给领取的人发 没被领取就给所有人发
        List<Long> sendAccountIdList;
        if (Objects.nonNull(body.getOldAccountId())) {
            sendAccountIdList = Collections.singletonList(body.getOldAccountId());

        } else {
            sendAccountIdList = pushClient.queryAccountListByPoiAndAuth(head.getTenantId(), head.getShopId(),
                    SELF_RIDER_NEW_PICK_DELIVERY_TASK_TAB_AUTH_CODE, SHU_GUO_PAI_PUSH_APP_ID);
        }
        basePushClient.unifiedSendSharkPush(head.getTenantId(), sendAccountIdList, SHU_GUO_PAI_PUSH_APP_ID.toString(), content);

    }

    private void handleTransToSelfDelivery(DeliveryChangeSyncOutMessage message) {
        DeliveryChangeSyncOutMessage.Head head = message.getHead();
        DeliveryChangeSyncOutMessage.TransToSelfDeliveryBody body =
                JSON.parseObject(message.getBody(), DeliveryChangeSyncOutMessage.TransToSelfDeliveryBody.class);


        String content = buildDeliveryOrderChangePushContent(head.getTenantId(),
                head.getShopId(),
                SharkPushSendSceneEnum.TRANS_TO_SELF_DELIVERY,
                head.getChannelOrderId(),
                head.getOrderBizType(),
                SharkPushOperateTypeEnum.ADD);

        basePushClient.unifiedSendSharkPush(head.getTenantId(), Collections.singletonList(body.getTargetAccountId()),
                SHU_GUO_PAI_PUSH_APP_ID.toString(), content);
    }

    private void handlePickDeliverySplit(DeliveryChangeSyncOutMessage message) {
        DeliveryChangeSyncOutMessage.Head head = message.getHead();

        String content = buildDeliveryOrderChangePushContent(head.getTenantId(),
                head.getShopId(),
                SharkPushSendSceneEnum.PICK_DELIVERY_SPLIT,
                head.getChannelOrderId(),
                head.getOrderBizType(),
                SharkPushOperateTypeEnum.UPDATE);

        List<Long> riderHasPickDeliveryTaskAuth = pushClient.queryAccountListByPoiAndAuth(head.getTenantId(), head.getShopId(),
                SELF_RIDER_NEW_PICK_DELIVERY_TASK_TAB_AUTH_CODE, SHU_GUO_PAI_PUSH_APP_ID);

        basePushClient.unifiedSendSharkPush(head.getTenantId(), riderHasPickDeliveryTaskAuth,
                SHU_GUO_PAI_PUSH_APP_ID.toString(), content);
    }

    private void handleTakeAway(DeliveryChangeSyncOutMessage message) {
        DeliveryChangeSyncOutMessage.Head head = message.getHead();
        DeliveryChangeSyncOutMessage.TakeAwayBody body =
                JSON.parseObject(message.getBody(), DeliveryChangeSyncOutMessage.TakeAwayBody.class);

        String content = buildDeliveryOrderChangePushContent(head.getTenantId(),
                head.getShopId(),
                SharkPushSendSceneEnum.TAKE_AWAY,
                head.getChannelOrderId(),
                head.getOrderBizType(),
                SharkPushOperateTypeEnum.UPDATE);

        basePushClient.unifiedSendSharkPush(head.getTenantId(), Collections.singletonList(body.getRiderAccountId()),
                SHU_GUO_PAI_PUSH_APP_ID.toString(), content);
    }

    private void handleDeliveryDoneByOrderFinish(DeliveryChangeSyncOutMessage message) {
        DeliveryChangeSyncOutMessage.Head head = message.getHead();
        DeliveryChangeSyncOutMessage.DeliveryDoneByOrderFinishBody body =
                JSON.parseObject(message.getBody(), DeliveryChangeSyncOutMessage.DeliveryDoneByOrderFinishBody.class);

        String content = buildDeliveryOrderChangePushContent(head.getTenantId(),
                head.getShopId(),
                SharkPushSendSceneEnum.DELIVERY_DONE_BY_ORDER_FINISH,
                head.getChannelOrderId(),
                head.getOrderBizType(),
                SharkPushOperateTypeEnum.DELETE);

        if (Objects.nonNull(body.getRiderAccountId())) {
            basePushClient.unifiedSendSharkPush(head.getTenantId(), Collections.singletonList(body.getRiderAccountId()),
                    SHU_GUO_PAI_PUSH_APP_ID.toString(), content);
        }
    }

    private void handleThirdPartDeliveryOrderChange(DeliveryChangeSyncOutMessage message) {
        DeliveryChangeSyncOutMessage.Head head = message.getHead();
        String content = buildDeliveryOrderChangePushContent(head.getTenantId(),
                head.getShopId(),
                SharkPushSendSceneEnum.THIRD_PART_DELIVERY_ORDER_CHANGE,
                head.getChannelOrderId(),
                head.getOrderBizType(),
                SharkPushOperateTypeEnum.UPDATE);

        List<Long> sendAccountIdList = pushClient.queryAccountListByPoiAndAuth(head.getTenantId(), head.getShopId(),
                THIRD_DELIVERY_TAB_AUTH_CODE, SHU_GUO_PAI_PUSH_APP_ID);

        if (CollectionUtils.isNotEmpty(sendAccountIdList)) {
            basePushClient.unifiedSendSharkPush(head.getTenantId(), sendAccountIdList,
                    SHU_GUO_PAI_PUSH_APP_ID.toString(), content);
        }
    }

    private void handlePickDoneInPickDeliverySplit(DeliveryChangeSyncOutMessage message) {
        DeliveryChangeSyncOutMessage.Head head = message.getHead();
        DeliveryChangeSyncOutMessage.PickDeliverySplitPickDoneBody body =
                JSON.parseObject(message.getBody(), DeliveryChangeSyncOutMessage.PickDeliverySplitPickDoneBody.class);

        String content = buildDeliveryOrderChangePushContent(head.getTenantId(),
                head.getShopId(),
                SharkPushSendSceneEnum.PICK_DONE_IN_PICK_DELIVERY_SPLIT,
                head.getChannelOrderId(),
                head.getOrderBizType(),
                SharkPushOperateTypeEnum.UPDATE);


        if (Objects.nonNull(body.getRiderAccountId())) {
            basePushClient.unifiedSendSharkPush(head.getTenantId(), Collections.singletonList(body.getRiderAccountId()),
                    SHU_GUO_PAI_PUSH_APP_ID.toString(), content);
        }
    }

    private String buildDeliveryOrderChangePushContent(Long tenantId, Long storeId, SharkPushSendSceneEnum sceneEnum,
                                                       String channelOrderId, Integer orderBizType,
                                                       SharkPushOperateTypeEnum typeEnum) {
        ObjectNode contentJson = JsonUtil.generateObjectNode();
        contentJson.put("serverTS", System.currentTimeMillis());
        contentJson.put("operation", "dhOrderUpdate");
        contentJson.put("storeId", storeId);
        contentJson.put("tenantId", tenantId);
        contentJson.put("channelOrderId", channelOrderId);
        contentJson.put("channelId", ChannelTypeConvertUtils.convert(orderBizType));
        contentJson.put("type", typeEnum.getType());
        contentJson.put("scene", sceneEnum.getScene());


        return contentJson.toString();
    }
}
