package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.poi.client.deliveryrange;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.BatchUpdatePoiShippingRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.Coordinate;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.PoiShippingInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPoiShippingThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.deliveryrange.OnlineStoreDeliveryRangeUpdateResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.deliveryrange.OrderPlatformDeliveryRangeUpdateClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.DeliveryRange;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.OrderPlatformDeliveryConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/9
 */
@Slf4j
@Component
public class DefaultOrderPlatformDeliveryRangeUpdateClient implements OrderPlatformDeliveryRangeUpdateClient {

	private static final int SUCCESS = 0;

	@Resource
	private ChannelPoiShippingThriftService.Iface channelPoiShippingThriftServiceClient;

	@Override
	@CatTransaction
	public OnlineStoreDeliveryRangeUpdateResult updateDeliveryRange(SelfBuiltDeliveryPoi deliveryPoi, Integer channelId,
	                                                                Map<ThirdDeliveryChannelEnum, List<DeliveryRange>> rangeMap) {
		try {
			OrderPlatformDeliveryConfig orderPlatformDeliveryConfig = deliveryPoi.getOrderPlatformDeliveryConfigMap().get(channelId);

			List<PoiShippingInfo> shippingInfoList = new ArrayList<>();
			rangeMap.forEach((channel, rangeList) ->
					rangeList.forEach(range ->
							shippingInfoList.add(
									new PoiShippingInfo(
											Integer.parseInt(String.valueOf(channel.getCode()) + range.getRangeId()),
											orderPlatformDeliveryConfig.getMinOrderPrice(),
											translateCoordinatePoints(range)
									)
							)
					)
			);

			BatchUpdatePoiShippingRequest request = new BatchUpdatePoiShippingRequest()
					.setTenantId(deliveryPoi.getTenantId())
					.setShopId(deliveryPoi.getStoreId())
					.setChannelId(channelId)
					.setPoiShippingInfo(shippingInfoList);
			log.info("ChannelPoiShippingThriftService.batchUpdatePoiShipping begin, request={}", request);
			ResultStatus result = channelPoiShippingThriftServiceClient.batchUpdatePoiShipping(request);
			log.info("ChannelPoiShippingThriftService.batchUpdatePoiShipping finish, response={}", result);
			if (result == null) {
				log.error("向订单平台[{}]同步配送范围异常， result=null", channelId);
				return new OnlineStoreDeliveryRangeUpdateResult(true, FailureCodeEnum.SYSTEM_ERROR.getMessage());
			}

			if (result.getCode() == SUCCESS) {
				return new OnlineStoreDeliveryRangeUpdateResult();
			} else {
				log.error("向订单平台[{}]同步配送范围失败, result={}", channelId, result);
				return new OnlineStoreDeliveryRangeUpdateResult(false, result.getMsg());
			}

		} catch (Exception e) {
			log.error("向订单平台[{}]同步配送范围失败", channelId, e);
			return new OnlineStoreDeliveryRangeUpdateResult(true, FailureCodeEnum.SYSTEM_ERROR.getMessage());
		}
	}

	private List<Coordinate> translateCoordinatePoints(DeliveryRange coordinatePoints) {
		return Optional.ofNullable(coordinatePoints.getCoordinatePoints())
				.orElse(new ArrayList<>())
				.stream()
				.map(it -> new Coordinate(Double.parseDouble(it.getLongitude()), Double.parseDouble(it.getLatitude())))
				.collect(Collectors.toList());
	}
}
