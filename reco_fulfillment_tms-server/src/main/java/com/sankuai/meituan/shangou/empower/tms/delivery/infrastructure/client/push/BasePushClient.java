package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.push;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aop.LoadTestAop;
import org.springframework.stereotype.Service;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.saas.message.request.push.FusionPushSendRequest;
import com.sankuai.meituan.shangou.saas.message.request.push.SharkPushSendRequest;
import com.sankuai.meituan.shangou.saas.message.response.MessageCommonResponse;
import com.sankuai.meituan.shangou.saas.message.service.PushThriftService;

import lombok.extern.slf4j.Slf4j;

/**
 * 基础 push 服务.
 *
 * <AUTHOR>
 * @since 2021/8/2 17:16
 */
@Slf4j
@Service
public class BasePushClient {

    @Resource
    private PushThriftService pushThriftServiceClient;

    /**
     * 统一发送融合 push.
     *
     * @param tenantId       租户 ID
     * @param storeId        门店 ID
     * @param accountIdList  需要通知的账号 ID 列表
     * @param appCode        应用 Code
     * @param pushConfigId   push ID
     * @param msgPropertyMap 消息属性值 Map
     */
    @LoadTestAop
    public void unifiedSendFusionPush(Long tenantId, Long storeId, List<Long> accountIdList, String appCode, Long pushConfigId,
                                      Map<String, String> msgPropertyMap) {
        FusionPushSendRequest request = new FusionPushSendRequest();
        request.setTenantId(tenantId);
        request.setPoiId(storeId);
        request.setAppCode(appCode);
        request.setEventConfigId(pushConfigId);
        request.setMsgPropertyMap(msgPropertyMap);

        // accountIdList数量可能超过消息中心的限制，这里需要进行分批处理
        // 通过Lion配置，方便动态调整和测试使用
        int batchSize = Lion.getConfigRepository().getIntValue("push.onetimes.account.count", 100);
        int accountIdCount = accountIdList.size();
        if (accountIdCount <= batchSize) {
            // 直接全量发送
            request.setAccountIds(accountIdList);
            doUnifiedSendFusionPush(storeId, pushConfigId, request);
        } else {
            // 分批发送
            int batchTimes = (accountIdCount - 1) / batchSize + 1;
            for (int p = 0; p < batchTimes; p++) {
                List<Long> accountIds = subAccountIds(accountIdList, batchSize, p);
                request.setAccountIds(accountIds);
                doUnifiedSendFusionPush(storeId, pushConfigId, request);
            }
        }
    }

    private void doUnifiedSendFusionPush(Long storeId, Long pushConfigId, FusionPushSendRequest request) {
        try {
            log.info("BasePushClient call PushThriftService.sendFusionPush, request:{}", JsonUtil.toJson(request));
            MessageCommonResponse messageCommonResponse = pushThriftServiceClient.sendFusionPush(request);
            log.info("BasePushClient call PushThriftService.sendFusionPush, response:{}", JsonUtil.toJson(messageCommonResponse));
        } catch (Exception e) {
            Cat.logEvent("FusionPush", pushConfigId.toString(), "fail", storeId.toString());
            log.error("BasePushClient call PushThriftService.sendFusionPush error", e);
        }
    }


    /**
     * 统一发送 sharkPush.
     *
     * @param tenantId      租户 ID
     * @param accountIdList 需要通知的账号 ID 列表
     * @param appCode       应用 Code
     * @param content       sharkPush 内容
     */
    @LoadTestAop
    public void unifiedSendSharkPush(Long tenantId, List<Long> accountIdList, String appCode, String content) {
        SharkPushSendRequest sharkPushSendRequest = new SharkPushSendRequest();
        sharkPushSendRequest.setTenantId(tenantId);
        sharkPushSendRequest.setAppCode(appCode);
        sharkPushSendRequest.setContent(content);

        // accountIdList数量可能超过消息中心的限制，这里需要进行分批处理
        int batchSize = Lion.getConfigRepository().getIntValue("push.onetimes.account.count", 100);
        int accountIdCount = accountIdList.size();
        if (accountIdCount <= batchSize) {
            // 直接全量发送
            sharkPushSendRequest.setAccountIds(accountIdList);
            doUnifiedSendSharkPush(sharkPushSendRequest);
        } else {
            // 分批发送
            int batchTimes = (accountIdCount - 1) / batchSize + 1;
            for (int p = 0; p < batchTimes; p++) {
                List<Long> accountIds = subAccountIds(accountIdList, batchSize, p);
                sharkPushSendRequest.setAccountIds(accountIds);
                doUnifiedSendSharkPush(sharkPushSendRequest);
            }
        }
    }

    private void doUnifiedSendSharkPush(SharkPushSendRequest sharkPushSendRequest) {
        try {
            log.info("PushClientImpl call PushThriftService.sendSharkPush, request:{}", JsonUtil.toJson(sharkPushSendRequest));
            MessageCommonResponse messageCommonResponse = pushThriftServiceClient.sendSharkPush(sharkPushSendRequest);
            log.info("PushClientImpl call PushThriftService.sendSharkPush, response:{}", JsonUtil.toJson(messageCommonResponse));
        } catch (Exception e) {
            log.error("PushClientImpl call PushThriftService.sendSharkPush error", e);
        }
    }

    /**
     * 切分分批发送的用户账号列表
     *
     * @param accountIdList 用户账号全量列表
     * @param batchSize     分批批次大小
     * @param batchTimes    分批批次数（分页数）
     * @return
     */
    private List<Long> subAccountIds(List<Long> accountIdList, int batchSize, int batchTimes) {
        List<Long> accountIds = new ArrayList<>();
        int accountIdCount = accountIdList.size();
        int fromIndex = batchTimes * batchSize;
        int endIndex = Math.min((fromIndex + batchSize), accountIdCount);
        accountIdList.subList(fromIndex, endIndex).stream().forEach(accountIds::add);
        return accountIds;
    }
}
