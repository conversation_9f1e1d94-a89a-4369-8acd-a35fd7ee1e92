package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.DeliveryExceptionCallbackCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/13
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DeliveryExceptionCallbackInfo extends DeliveryUnifiedCallbackInfo {

	/**
	 * 赋能订单id
	 */
	private Long orderId;

	/**
	 * 异常类型
	 */
	private Integer exceptionType;

	/**
	 * 异常code
	 */
	private Integer exceptionCode;

	/**
	 * 异常描述
	 */
	private String exceptionDescription;

	/**
	 * 异常发生时间
	 */
	private Long exceptionTimeInMillis;

	public DeliveryExceptionCallbackInfo(Long orderId, DeliveryExceptionInfo exceptionInfo, LocalDateTime exceptionTime) {
		Preconditions.checkNotNull(orderId);
		Preconditions.checkNotNull(exceptionInfo);
		Preconditions.checkNotNull(exceptionTime);

		this.orderId = orderId;
		this.exceptionType = exceptionInfo.getExceptionType().getCode();
		this.exceptionCode = exceptionInfo.getExceptionCode();
		this.exceptionDescription = exceptionInfo.getExceptionDescription();
		this.exceptionTimeInMillis = TimeUtil.toMilliSeconds(exceptionTime);
	}

	public DeliveryExceptionCallbackCmd toCmd() {
		return new DeliveryExceptionCallbackCmd(
				orderId, DeliveryExceptionTypeEnum.valueOf(exceptionType), exceptionCode, exceptionDescription,
				TimeUtil.fromMilliSeconds(exceptionTimeInMillis)
		);
	}
}
