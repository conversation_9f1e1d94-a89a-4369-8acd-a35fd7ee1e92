package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发起同步骑手位置消息体
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SyncRiderPositionMessage {

	/**
	 * 运单Id
	 */
	private Long deliveryId;

	private Long tenantId;

	private Long storeId;

	//是否根据前端上报位置直接同步骑手位置
	private boolean feDirectSyncRiderPosition = false;

	public SyncRiderPositionMessage(Long deliveryId, Long tenantId, Long storeId) {
		this.deliveryId = deliveryId;
		this.tenantId = tenantId;
		this.storeId = storeId;
	}


}
