package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.meituan.reco.pickselect.common.domain.orderTrack.OperationScene;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryNotifyService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.push.PushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.AggDeliveryCallbackHandleFailedEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.AggDeliveryFailCallbackHandleFailedEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryWarnEventPublisher;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.DeliveryCallbackThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.response.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.TransferOrderIdempotentSquirrelOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryChangeCallbackInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryExceptionCallbackInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryUnifiedCallbackMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_SYSTEM;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum.LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.SYSTEM_ERROR;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/7
 */
@Slf4j
@Service
public class DeliveryCallbackThriftServiceImpl implements DeliveryCallbackThriftService {

	@Resource
	private OrderSystemClient orderSystemClient;
	@Resource
	private DeliveryWarnEventPublisher warnEventPublisher;
	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private MafkaMessageProducer<DeliveryUnifiedCallbackMessage> deliveryUnifiedCallbackMessageProducer;
	@Resource
	private DeliveryNotifyService deliveryNotifyService;

	@Resource
	private DeliveryChannelApplicationService deliveryChannelApplicationService;

	@Autowired
	private PushClient pushClient;

	@Resource
	private TransferOrderIdempotentSquirrelOperateService transferOrderIdempotentSquirrelOperateService;

	private static final Integer PRIVATE_DOMAIN_CHANNEL = 0;

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public AggDeliveryFailCallbackResponse notifyAggDeliveryFailResult(AggDeliveryFailReasonNotifyRequest request) {

		AggDeliveryFailCallbackResponse response = new AggDeliveryFailCallbackResponse(Status.SUCCESS);
		Integer platformCode = request.getPlatformCode();
		try {
			long zero = BigDecimal.ZERO.longValue();
			PlatformSourceEnum platformSourceEnum = PlatformSourceEnum.checkOrderIdSource(request.getOriginId());
			String originId = PlatformSourceEnum.getOrderIdWithoutPrefix(request.getOriginId());
			long orderId = NumberUtils.toLong(originId, zero);
			if (orderId <= zero) {
				response.setStatus(new Status(1, "订单id错误"));
				if(platformCode == null){
					warnEventPublisher.postEvent(new AggDeliveryFailCallbackHandleFailedEvent(
							new OrderKey(-1L, -1L, orderId), "订单id错误"));
				}else{
					warnEventPublisher.postEvent(new AggDeliveryFailCallbackHandleFailedEvent(DeliveryPlatformEnum.enumOf(platformCode),
							new OrderKey(-1L, -1L, orderId), "订单id错误"));
				}
				return response;
			}
			Optional<DeliveryOrder> deliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(orderId);
			if(!deliveryOrder.isPresent()){
				response.setStatus(new Status(1, "运单错误"));
				return response;
			}
			Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(deliveryOrder.get().getOrderId(), false);
			if (orderInfoResult.isFail()) {
				response.setStatus(new Status(1, orderInfoResult.getFailure().getFailureMessage()));
				return response;
			}
			OrderInfo order = orderInfoResult.getInfo();

			if (Objects.isNull(request.getTimestamp())) {
				// 不是自配送
				log.error("参数错误 ,orderId->{}", orderId);
				response.setStatus(new Status(1, "参数错误"));
				return response;
			}

			if (!order.isFinished()) {
				deliveryUnifiedCallbackMessageProducer.sendMessage(
						new DeliveryUnifiedCallbackMessage(
								DeliveryCallbackTypeEnum.EXCEPTION_CALLBACK,
								new DeliveryExceptionCallbackInfo(
										orderId,
										new DeliveryExceptionInfo(
												Objects.equals(request.getCode(), DeliveryExceptionCodeEnum.SYSTEM_EXCEPTION_DELIVERY_FAIL.getCode())
												? LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION
														: DELIVERY_EXCEPTION_BY_RIDER, request.getReason(), request.getCode()),
										TimeUtil.fromSeconds(request.getTimestamp())
								)
						),
						orderId
				);
			}

			response.setStatus(Status.SUCCESS);
		} catch (Exception e) {
			log.error("DeliveryCallbackThriftService#notifyAggDeliveryFailResult error. request:{}", request, e);
			warnEventPublisher.postEvent(new AggDeliveryFailCallbackHandleFailedEvent(DeliveryPlatformEnum.enumOf(platformCode),
					new OrderKey(-1L, -1L, NumberUtils.toLong(request.getOriginId(), -1L)), "配送异常消费失败"));
			response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
		return response;
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public FarmDeliveryChangeNotifyResp farmNotifyDeliveryChange(FarmDeliveryChangeNotifyReq req) {
		PlatformSourceEnum platformSourceEnum = PlatformSourceEnum.checkOrderIdSource(req.getOriginId());
		String originId = PlatformSourceEnum.getOrderIdWithoutPrefix(req.getOriginId());
		try {
			Optional<DeliveryStatusEnum> deliveryStatus =Optional.empty();
			Optional<DeliveryCancelReasonEnum> deliveryCancelReason = Optional.empty();
			String logisticNo = null;
			Integer deliveryChannelCode = null;
			Integer deliveryPlatformCode = null;
			if(req.getPlatformCode()==DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode()){
				if (!Lion.getConfigRepository().getBooleanValue("farm.delivery.call.back.status", false)) {
					return new FarmDeliveryChangeNotifyResp(Status.SUCCESS);
				}

				deliveryStatus = FarmDeliveryStatusEnum.mapToDeliveryStatus(req.getStatus());
				if (!deliveryStatus.isPresent()) {
					warnEventPublisher.postEvent(new AggDeliveryCallbackHandleFailedEvent(DeliveryPlatformEnum.enumOf(req.getPlatformCode()),
							new OrderKey(-1L, -1L, NumberUtils.toLong(originId, -1L)), "未知配送状态"));
					return new FarmDeliveryChangeNotifyResp(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), "未知配送状态"));
				}
				logisticNo = req.getLogisticNo();
				//这个时候麦芽田还没发单，不能确认下来。不更新我们的logisticNo
				if (req.getStatus() <= FarmDeliveryStatusEnum.WAIT_TO_RECEIVE.getCode()) {
					logisticNo = StringUtils.EMPTY;
				}

				//取消后，配送费更新为取消费用
				if(req.getStatus() == FarmDeliveryStatusEnum.DELIVERY_CANCELLED.getCode()) {
					if (StringUtils.isNotEmpty(req.getCancelAmount())) {
						req.setAmount(req.getCancelAmount());
					} else {
						req.setAmount("0");
					}
					req.setTipAmount("0");
				}

				DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByLogisticMark(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode(), DeliveryChannel.DEFAULT_ORDER_CHANNEL_CODE, req.getLogistic());
				deliveryChannelCode = deliveryChannel.getCarrierCode();
				deliveryPlatformCode = deliveryChannel.getDeliveryPlatFormCode();
				// 若是麦芽田转自配送消息，则将配送状态置为 DeliveryStatusEnum.MERCHANT_DELIVERING（商家自行配送中）,原状态为麦芽田同步的配送已完成
				if (deliveryChannelCode == DeliveryChannelEnum.FARM_DELIVERY_MERCHANT.getCode()) {
					Cat.logEvent("MALT_TURN_TO_SELF", "COUNT");
					deliveryStatus = Optional.of(DeliveryStatusEnum.MERCHANT_DELIVERING);
				}

				deliveryCancelReason = FarmDeliveryCancelReasonEnum.map2QnhDeliveryCancelReasonEnum(req.getCancelReasonCode());
			}else if(req.getPlatformCode()==DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode()){
				if (!Lion.getConfigRepository().getBooleanValue("dap.delivery.call.back.status", true)) {
					return new FarmDeliveryChangeNotifyResp(Status.SUCCESS);
				}
				deliveryStatus = DapDeliveryStatusEnum.mapToDeliveryStatus(req.getStatus());
				if (!deliveryStatus.isPresent()) {
					warnEventPublisher.postEvent(new AggDeliveryCallbackHandleFailedEvent(DeliveryPlatformEnum.enumOf(req.getPlatformCode()),
							new OrderKey(-1L, -1L, NumberUtils.toLong(originId, -1L)), "未知配送状态"));
					return new FarmDeliveryChangeNotifyResp(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), "未知配送状态"));
				}
				logisticNo = req.getLogisticNo();
				if (req.getStatus() <= DapDeliveryStatusEnum.WAIT_TO_RECEIVE.getCode()) {
					logisticNo = StringUtils.EMPTY;
				}

				//取消后，配送费更新为取消费用
				if(req.getStatus() == DapDeliveryStatusEnum.DELIVERY_CANCELLED.getCode()) {
					if (StringUtils.isNotEmpty(req.getCancelAmount())) {
						req.setAmount(req.getCancelAmount());
					} else {
						req.setAmount("0");
					}
					req.setTipAmount("0");
				}
				DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByLogisticMark(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode(), DeliveryChannel.DEFAULT_ORDER_CHANNEL_CODE, req.getLogistic());
				deliveryChannelCode = deliveryChannel.getCarrierCode();
				deliveryPlatformCode = deliveryChannel.getDeliveryPlatFormCode();

				deliveryCancelReason = DapDeliveryCancelReasonEnum.map2QnhDeliveryCancelReasonEnum(req.getCancelReasonCode());
			} else if (req.getPlatformCode()==DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode()) {
				if (!Lion.getConfigRepository().getBooleanValue("orderChannel.delivery.call.back.status", true)) {
					return new FarmDeliveryChangeNotifyResp(Status.SUCCESS);
				}

				deliveryStatus = getDeliveryStatusEnum4OrderChannelDeliveryPlatform(req);
				if (!deliveryStatus.isPresent()) {
					log.error("平台配送，deliveryStatus为空");
					return new FarmDeliveryChangeNotifyResp(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), "deliveryStatus为空"));
				}

				logisticNo = req.getLogisticNo();
				if (isUpdateLogisticNo4OrderChannelDeliveryPlatform(req)) {
					logisticNo = StringUtils.EMPTY;
				}

				int channelCode = Optional.ofNullable(DynamicChannelType.findOf(req.getOrderChannelType()))
						.map(DynamicChannelType::getChannelId).orElse(0);
				DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByLogisticMark(
						DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode(), channelCode, req.getLogistic());
				deliveryChannelCode = deliveryChannel.getCarrierCode();
				deliveryPlatformCode = deliveryChannel.getDeliveryPlatFormCode();
			}

			deliveryUnifiedCallbackMessageProducer.sendMessage(
					new DeliveryUnifiedCallbackMessage(
							DeliveryCallbackTypeEnum.CHANGE_CALLBACK,
							new DeliveryChangeCallbackInfo(
									Long.valueOf(originId),
									deliveryChannelCode,
									logisticNo,
									StringUtils.EMPTY,
									DeliveryEventEnum.getEventByStatus(deliveryStatus.get()),
									buildExceptionInfo(req, deliveryStatus.get()),
									TimeUtil.fromSeconds(req.getTimestamp()),
									translateRider(req),
									Optional.ofNullable(req.getDistance()).map(Long::parseLong).orElse(null),
									Optional.ofNullable(req.getAmount()).map(Double::parseDouble).orElse(null),
									Optional.ofNullable(req.getTipAmount()).map(Double::parseDouble).orElse(null),
									platformSourceEnum.getCode(),
									req.getBaseFee(),
									req.getDiscountFee(),
									req.getInsuredFee(),
									deliveryPlatformCode,
									deliveryCancelReason.orElse(null),
									req.getOriginWaybillNo()
							)
					),
					Long.valueOf(originId)
			);
			return new FarmDeliveryChangeNotifyResp(Status.SUCCESS);
		} catch (Exception e) {
			log.error("DeliveryCallbackThriftService#farmNotifyDeliveryChange error, request:{}", req, e);
			warnEventPublisher.postEvent(new AggDeliveryCallbackHandleFailedEvent(DeliveryPlatformEnum.enumOf(req.getPlatformCode()),
					new OrderKey(-1L, -1L, NumberUtils.toLong(originId, -1L)), "未知"));
			return new FarmDeliveryChangeNotifyResp(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}

	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public SelfDeliveryCallbackResp selfDeliveryCallback(SelfDeliveryCallbackReq req) {
		SelfDeliveryCallbackResp resp=new SelfDeliveryCallbackResp(Status.SUCCESS);
		if(req==null || StringUtils.isEmpty(req.getChannelOrderId())){
			resp.setStatus(new Status(ResponseCodeEnum.REQUEST_PARAMS_ERROR.getValue(), ResponseCodeEnum.REQUEST_PARAMS_ERROR.name()));
			return resp;
		}
		Result<OrderInfo> orderInfoResult = orderSystemClient.queryByViewOrderId(req.getOrderBizType(),req.getChannelOrderId(),false);
		OrderInfo orderInfo=orderInfoResult.getInfo();
		if(orderInfo==null){
			resp.setStatus(new Status(ResponseCodeEnum.REQUEST_PARAMS_ERROR.getValue(), ResponseCodeEnum.REQUEST_PARAMS_ERROR.name()));
			return resp;
		}

		// 转单
		if (MccConfigUtils.transferOrderIdempotentSwitch()) {
			boolean setnx = transferOrderIdempotentSquirrelOperateService.setnx(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(), orderInfo.getOrderKey().getOrderId());
			if (setnx) {
				//发起三方配送
				try {
					deliveryNotifyService.notifyDeliveryTransMsg(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(), orderInfo.getOrderKey().getOrderId());
				} catch (Exception e) {
					transferOrderIdempotentSquirrelOperateService.delete(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(), orderInfo.getOrderKey().getOrderId());
					throw e;
				}
			} else {
				log.info("转自配送回调幂等, orderId:{}", orderInfo.getOrderKey().getOrderId());
			}
		}else {
			deliveryNotifyService.notifyDeliveryTransMsg(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(), orderInfo.getOrderKey().getOrderId());
		}

		return resp;
	}

	@Override
	public ElmSwitchSelfDeliveryCallbackResp elmSwitchSelfDeliveryCallback(ElmSwitchSelfDeliveryCallbackReq req) {
		ElmSwitchSelfDeliveryCallbackResp resp=new ElmSwitchSelfDeliveryCallbackResp(Status.SUCCESS);
		if(req==null ||  StringUtils.isEmpty(req.getChannelOrderId()) || (req.getCanSelfDelivery() == null && req.getCanSecondManualCall() == null)){
			resp.setStatus(new Status(ResponseCodeEnum.REQUEST_PARAMS_ERROR.getValue(), ResponseCodeEnum.REQUEST_PARAMS_ERROR.name()));
			return resp;
		}
		Result<OrderInfo> orderInfoResult = orderSystemClient.queryByViewOrderId(DynamicOrderBizType.ELE_ME.getValue(),req.getChannelOrderId(),false);

		OrderInfo orderInfo=orderInfoResult.getInfo();
		if(orderInfo==null){
			resp.setStatus(new Status(ResponseCodeEnum.REQUEST_PARAMS_ERROR.getValue(), ResponseCodeEnum.REQUEST_PARAMS_ERROR.name()));
			return resp;
		}

		Optional<DeliveryOrder> deliveryOrder = deliveryOrderRepository.getDeliveryOrderFromMaster(orderInfo.getOrderKey().getOrderId(),orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());

		if (!deliveryOrder.isPresent()) {
			log.error("饿了么切换自配送回调处理失败，未查询到对应运单, orderInfo={}", orderInfo);
			resp.setStatus(new Status(ResponseCodeEnum.UNKNOWN_ERROR.getValue(), ResponseCodeEnum.UNKNOWN_ERROR.name()));
			return resp;
		}
		DeliveryExceptionInfo exceptionInfo = null;
		if(req.getCanSelfDelivery() != null && req.getCanSelfDelivery()
				&& req.getCanSecondManualCall() != null && req.getCanSecondManualCall()){
			exceptionInfo = new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER, null, DeliveryExceptionCodeEnum.RECALL_SELF_RIDER_FAIL.getCode());
		}else if(req.getCanSelfDelivery() != null && req.getCanSelfDelivery()){
			exceptionInfo = new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER, null, DeliveryExceptionCodeEnum.SELF_DELIVERY.getCode());
		}else if(req.getCanSecondManualCall() != null && req.getCanSecondManualCall()){
			exceptionInfo = new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER, null, DeliveryExceptionCodeEnum.RECALL_RIDER_FAIL.getCode());
		}

		if(exceptionInfo == null){
			return resp;
		}

		deliveryOrder.get().onException(exceptionInfo, LocalDateTime.now());
		deliveryOrderRepository.save(deliveryOrder.get());
		sendOrderTrack(deliveryOrder.get().getTenantId(),deliveryOrder.get().getChannelOrderId(),deliveryOrder.get().getOrderBizType(),exceptionInfo);

		pushClient.pushDeliveryException(deliveryOrder.get());
		return resp;
	}

	private void sendOrderTrack(Long tenantId,String channelOrderId,Integer orderBizType, DeliveryExceptionInfo deliveryExceptionInfo){
		TrackOpType trackOpType =null;
		if(deliveryExceptionInfo.getExceptionCode() == DeliveryExceptionCodeEnum.RECALL_SELF_RIDER_FAIL.getCode()
		|| deliveryExceptionInfo.getExceptionCode() == DeliveryExceptionCodeEnum.SELF_DELIVERY.getCode()
		|| deliveryExceptionInfo.getExceptionCode() == DeliveryExceptionCodeEnum.RECALL_RIDER_FAIL.getCode()){
			trackOpType = TrackOpType.DELIVERY_EXCEPTION_UPLOAD_RIDER;

		}
		if(trackOpType == null){
			return;
		}
		Map<String,Object> extMap=new HashMap<>();
		OrderTrackEvent event = new OrderTrackEvent(TrackSource.DELIVERY, trackOpType, 0L,
				OperationScene.DELIVERY_EXCEPTION, tenantId, channelOrderId,
				orderBizType, extMap, null);
		deliveryNotifyService.notifyDeliveryTrace(event);
	}

	private Rider translateRider(FarmDeliveryChangeNotifyReq req) {
		return StringUtils.isNotEmpty(req.getRiderName()) && StringUtils.isNotEmpty(req.getRiderPhone()) ?
				new Rider(req.getRiderName(), req.getRiderPhone(), req.getRiderPhoneToken()) : null;
	}

	private DeliveryExceptionInfo buildExceptionInfo(FarmDeliveryChangeNotifyReq req, DeliveryStatusEnum deliveryStatus) {
		// 优先根据异常原因code判断、未命中异常、再根据配送状态判断
		DeliveryExceptionInfo deliveryExceptionInfo = getDeliveryExceptionInfo4ReasonCode(req);
		if(deliveryExceptionInfo != null){
			return deliveryExceptionInfo;
		}
		return getDeliveryExceptionInfo4Status(req, deliveryStatus);
	}

	private DeliveryExceptionInfo getDeliveryExceptionInfo4ReasonCode(FarmDeliveryChangeNotifyReq req) {
		DeliveryExceptionCodeEnum deliveryExceptionCodeEnum = DeliveryExceptionCodeEnum.codeStrOf(req.getFailReasonCode());
		switch (deliveryExceptionCodeEnum){
			case DELIVERY_STATUS_ROLLBACK:
				// 配送状态回退
				return new DeliveryExceptionInfo(DELIVERY_EXCEPTION_BY_RIDER,
						req.getFailReasonMessage(),
						DeliveryExceptionCodeEnum.DELIVERY_STATUS_ROLLBACK.getCode());
			default:
				return null;
		}
	}

	private DeliveryExceptionInfo getDeliveryExceptionInfo4Status(FarmDeliveryChangeNotifyReq req, DeliveryStatusEnum deliveryStatus) {
		switch (deliveryStatus) {
			case DELIVERY_REJECTED:
			case DELIVERY_FAILED:
				return new DeliveryExceptionInfo(DELIVERY_EXCEPTION_BY_SYSTEM, req.getFailReasonMessage());

			case DELIVERY_CANCELLED:
				return Optional.ofNullable(req.getCancelReasonCode())
						.map(FarmCancelTypeEnum::enumOf)
						.map(it -> {
							switch (it){
								case DELIVERY_CHANNEL_CANCEL:
									return new DeliveryExceptionInfo(DELIVERY_EXCEPTION_BY_SYSTEM, req.getFailReasonMessage());

								case SYSTEM_CANCEL:
									return new DeliveryExceptionInfo(DELIVERY_EXCEPTION_BY_SYSTEM, "其它原因取消");

								default:
									return null;
							}
						})
						.orElse(DeliveryExceptionInfo.NO_EXCEPTION);

			default:
				return new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.NO_EXCEPTION, StringUtils.EMPTY);
		}
	}

	private boolean isUpdateLogisticNo4OrderChannelDeliveryPlatform(FarmDeliveryChangeNotifyReq req) {
		Integer orderChannel = req.getOrderChannelType();
		if (Objects.isNull(orderChannel)) {
			log.error("isUpdateLogisticNo4OrderChannelDeliveryPlatform error, orderChannel is null");
			return false;
		}

		if (DynamicChannelType.YOU_ZAN.getChannelId() == orderChannel) {
			if (req.getStatus() < YzOrderChannelDeliveryStatusEnum.WAIT_TO_RECEIVE.getCode() &&
					req.getStatus() != YzOrderChannelDeliveryStatusEnum.THIRD_PART_ORDER_FAILED.getCode()) {
				log.warn("有赞订单平台配送，这个时候还没发单，不能确认下来，不更新logisticNo");
				return true;
			}
		}
		return false;
	}

	/**
	 * 获取平台配送具体订单渠道的配送状态与百川统一配送状态的映射
	*/
	private Optional<DeliveryStatusEnum> getDeliveryStatusEnum4OrderChannelDeliveryPlatform(FarmDeliveryChangeNotifyReq req) {
		int orderChannel = req.getOrderChannelType();
		DynamicOrderBizType dynamicOrderBizType = DynamicOrderBizType.channelId2OrderBizType(orderChannel);

		if (DynamicChannelType.YOU_ZAN.getChannelId() == orderChannel) {
			return YzOrderChannelDeliveryStatusEnum.mapToDeliveryStatus(req.getStatus());
		} else if (DynamicChannelType.DOU_YIN.getChannelId() == orderChannel) {
			return DyOrderChannelDeliveryStatusEnum.mapToDeliveryStatus(req.getStatus());
		} else if (dynamicOrderBizType != null && Objects.equals(dynamicOrderBizType.getChannelStandard(), PRIVATE_DOMAIN_CHANNEL)) {
			return OpenApiDistributeStatusEnum.mapToDeliveryStatus(req.getStatus());
		} else if (Objects.equals(dynamicOrderBizType, DynamicOrderBizType.TAO_XIAN_DA)) {
			return TxdOrderChannelDeliveryStatusEnum.mapToQnhDeliveryStatus(req.getStatus());
		} else {
			return Optional.empty();
		}
	}
}
