package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.StoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.StoreConfigOpLog;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.LionConfigUtils;
import com.sankuai.meituan.waimaidata.disaster.utils.DataReportCompleteDataThriftService;
import com.sankuai.meituan.waimaidata.disaster.utils.dto.BaseFieldCoverData;
import com.sankuai.meituan.waimaidata.disaster.utils.dto.CompleteDataItem;
import com.sankuai.meituan.waimaidata.disaster.utils.dto.DisasterCompleteDataRequestParam;

import lombok.extern.slf4j.Slf4j;

/**
 * 容灾服务反查特征数据
 *
 * <AUTHOR>
 * @since 2024/11/11
 */
@Slf4j
@Service
public class DataReportCompleteDataThriftServiceImpl implements DataReportCompleteDataThriftService {

    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    @Override
    public List<CompleteDataItem> batchCompleteData(List<DisasterCompleteDataRequestParam> requestParamList) {
        try {
            log.info("requestParamList: {}", JsonUtil.toJson(requestParamList));
            if (CollectionUtils.isEmpty(requestParamList)) {
                return new ArrayList<>();
            }

            return requestParamList.stream()
                    .filter(requestParam -> requestParam != null
                            && requestParam.getDataInfo() != null
                            && StringUtils.isNotBlank(requestParam.getDataInfo().getDataMapJson()))
                    .map(requestParam -> {
                        StoreConfig storeConfig = JSON.parseObject(requestParam.getDataInfo().getDataMapJson(), StoreConfig.class);
                        if (storeConfig == null || storeConfig.getId() == null || storeConfig.getUpdateTime() == null) {
                            return null;
                        }

                        List<StoreConfigOpLog> opLogList = deliveryPoiRepository.queryStoreConfigOpLogByCondition(
                                storeConfig.getId(),
                                getAdjustedTime(storeConfig.getUpdateTime(), false, LionConfigUtils.getStoreOpLogQueryTimeRange()),
                                getAdjustedTime(storeConfig.getUpdateTime(), true, LionConfigUtils.getStoreOpLogQueryTimeRange())
                        );

                        StoreConfigOpLog opLog = CollectionUtils.isEmpty(opLogList) ? null : opLogList.get(0);
                        return createCompleteDataItem(requestParam, storeConfig, opLog);
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        catch (Exception e) {
            log.error("batchCompleteData error, requestParamList:{}", JsonUtil.toJson(requestParamList), e);
        }
        return new ArrayList<>();
    }

    private CompleteDataItem createCompleteDataItem(DisasterCompleteDataRequestParam requestParam, StoreConfig storeConfig, StoreConfigOpLog opLog) {
        CompleteDataItem completeDataItem = new CompleteDataItem();
        completeDataItem.setESId(requestParam.getESId());

        String channelType = storeConfig.getChannelType().toString();
        Map<String, String> featureMap = new HashMap<>();
        featureMap.put(channelType, channelType);
        completeDataItem.setFeatureMap(featureMap);

        BaseFieldCoverData baseFieldCoverData = new BaseFieldCoverData();
        baseFieldCoverData.setDataInfo(requestParam.getDataInfo());
        baseFieldCoverData.setReasonCode("update");
        baseFieldCoverData.setOpSource("com.sankuai.sgfulfillment.tms");
        baseFieldCoverData.setDeleteTime(toEpochSecond(storeConfig.getUpdateTime()));
//        baseFieldCoverData.setTraceId(Tracer.id());

        // 如果opLog为空，可能是上游系统没有记录操作日志，则使用默认值
        if (opLog == null) {
            // 除容灾平台外的其他平台使用-1作为opUid，容灾平台使用-2
            baseFieldCoverData.setOpUid(-1);
            baseFieldCoverData.setOpName("SYSTEM");
            // 0 未知类型1 系统发起，如定时任务等2 非系统发起
            baseFieldCoverData.setSystemRole(1);
            // 容灾系统识别对应角色（开发、运营等）
            // 0、未知类型，业务方自身无法判断操作人类型1、 mt员工，上报opuid为美团员工UID2、其他类型，如商家账号，三方ID等
            baseFieldCoverData.setOpUidType(0);
        }
        else {
            baseFieldCoverData.setOpUid(opLog.getOperatorId() == null ? null : opLog.getOperatorId().intValue());
            baseFieldCoverData.setOpName(opLog.getOperatorName());
            // 0 未知类型1 系统发起，如定时任务等2 非系统发起
            baseFieldCoverData.setSystemRole(2);
            // 容灾系统识别对应角色（开发、运营等）
            // 0、未知类型，业务方自身无法判断操作人类型1、 mt员工，上报opuid为美团员工UID2、其他类型，如商家账号，三方ID等
            baseFieldCoverData.setOpUidType(2);
        }

        completeDataItem.setCoverData(baseFieldCoverData);
        return completeDataItem;
    }

    /**
     * 返回指定时间之前或之后指定的的时间，单位秒
     *
     * @param baseTime 基准时间
     * @param isAfter  如果为true，则返回之后指定的时间；如果为false，则返回之前的时间
     * @return 调整后的时间
     */
    public static LocalDateTime getAdjustedTime(LocalDateTime baseTime, boolean isAfter, int seconds) {
        if (isAfter) {
            // 返回之后3秒的时间
            return baseTime.plusSeconds(seconds);
        }
        else {
            // 返回之前3秒的时间
            return baseTime.minusSeconds(seconds);
        }
    }

    /**
     * 将LocalDateTime转换为秒级时间戳
     *
     * @param localDateTime 需要转换的LocalDateTime对象
     * @return 秒级时间戳
     */
    public static long toEpochSecond(LocalDateTime localDateTime) {
        return localDateTime.toEpochSecond(ZoneOffset.systemDefault().getRules().getOffset(localDateTime));
    }
}
