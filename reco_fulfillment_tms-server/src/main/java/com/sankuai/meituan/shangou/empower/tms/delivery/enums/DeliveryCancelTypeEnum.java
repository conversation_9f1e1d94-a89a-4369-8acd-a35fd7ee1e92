package com.sankuai.meituan.shangou.empower.tms.delivery.enums;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/22
 */
public enum DeliveryCancelTypeEnum {

	UNCANCEL(0, "未取消"),
	UNKNOWN(-1, "未知原因取消"),
	CLIENT_CANCEL(1, "客户主动取消"),
	CLIENT_CANCEL_FOR_SYSTEM(2, "由于配送方原因客户取消"),
	CLIENT_CANCEL_FOR_OTHERS(3, "由于其他原因客户取消"),
	SYSTEM_CANCEL_FOR_CLIENT(4, "由于客户客户原因系统取消"),
	SYSTEM_CANCEL(5, "配送系统原因取消"),
	SYSTEM_CANCEL_FOR_OTHERS(6, "由于其他原因系统取消"),
	;

	private final int value;
	private final String desc;

	DeliveryCancelTypeEnum(int value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public int getValue() {
		return this.value;
	}

	public String getDesc() {
		return this.desc;
	}

	public static DeliveryCancelTypeEnum enumOf(int value) {
		for (DeliveryCancelTypeEnum each : values()) {
			if (each.getValue() == value) {
				return each;
			}
		}

		return null;
	}
}
