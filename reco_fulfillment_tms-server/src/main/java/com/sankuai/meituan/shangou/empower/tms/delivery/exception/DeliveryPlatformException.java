package com.sankuai.meituan.shangou.empower.tms.delivery.exception;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/02/11
 * desc: 配送平台异常
 */
@Data
public class DeliveryPlatformException extends RuntimeException {

    private String msg;

    private Integer code;

    public DeliveryPlatformException(FailureCodeEnum failureCodeEnum) {
        super(failureCodeEnum.getMessage());

        this.code = failureCodeEnum.getCode();
        this.msg = failureCodeEnum.getMessage();
    }

}
