package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/8 17:49
 **/
@Data
public class RiderLocationSyncToChannelMessage {
    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 订单渠道id
     */
    private Integer channelId;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 运单id
     */
    private String deliveryOrderId;

    /**
     * 是否只同步骑手位置,不同步运单状态(只有同步到微商城渠道才会看这个字段)
     */
    private Boolean onlySyncRiderPosition;

    /**
     * 订单id
     */
    private String viewOrderId;

    /**
     * 骑手位置-经度
     */
    private Double longitude;

    /**
     * 骑手位置-纬度
     */
    private Double latitude;

    /**
     * 骑手姓名
     */
    private String RiderName;

    /**
     * 骑手电话
     */
    private String RiderPhone;

    /**
     * 配送渠道
     */
    private Integer deliveryChannelId;

    /**
     * 运单状态
     */
    private Integer status;
}
