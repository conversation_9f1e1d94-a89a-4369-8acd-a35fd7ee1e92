package com.sankuai.meituan.shangou.empower.tms.delivery.enums;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryCancelReasonEnum;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 青云取消配送原因枚举
 * <AUTHOR>
 */

public enum DapDeliveryCancelReasonEnum {

    NO_CANCEL(0, "", ""),

    MERCHANT_CANCEL(1, "商户取消", "用于商家在渠道/聚合平台发起的取消"),

    CUSTOMER_CANCEL(2, "用户取消", "用于用户在渠道或更上一层的取消"),

    SYSTEM_CANCEL(3, "系统取消", "用于因为各系统原因发起的取消"),

    RIDER_CANCEL(4, "骑手取消", "用于配送商的骑手发起的取消"),

    OTHER_CANCEL(99, "其他", "这种情况下要加文字说明"),
    ;

    private final int cancelCode;

    private final String cancelReason;

    private final String cancelDesc;

    private static final Map<Integer, DapDeliveryCancelReasonEnum> CODE_TO_ENUM_MAP = new HashMap<>();

    static {
        for (DapDeliveryCancelReasonEnum each : values()) {
            CODE_TO_ENUM_MAP.put(each.getCancelCode(), each);
        }
    }

    DapDeliveryCancelReasonEnum(int cancelCode, String cancelReason, String cancelDesc) {
        this.cancelCode = cancelCode;
        this.cancelReason = cancelReason;
        this.cancelDesc = cancelDesc;
    }

    public int getCancelCode() {
        return this.cancelCode;
    }

    public static Optional<DeliveryCancelReasonEnum> map2QnhDeliveryCancelReasonEnum(Integer cancelCode) {
        if (Objects.isNull(cancelCode)) {
            return Optional.empty();
        }

        DapDeliveryCancelReasonEnum dapDeliveryCancelReasonEnum = CODE_TO_ENUM_MAP.get(cancelCode);
        if (Objects.isNull(dapDeliveryCancelReasonEnum)) {
            return Optional.empty();
        }

        switch (dapDeliveryCancelReasonEnum) {
            case NO_CANCEL:
                return Optional.of(DeliveryCancelReasonEnum.NO_CANCEL);
            case MERCHANT_CANCEL:
                return Optional.of(DeliveryCancelReasonEnum.MERCHANT_CANCEL);
            case RIDER_CANCEL:
                return Optional.of(DeliveryCancelReasonEnum.CARRIER_CANCEL);
            case CUSTOMER_CANCEL:
                return Optional.of(DeliveryCancelReasonEnum.CUSTOMER_CANCEL);
            case SYSTEM_CANCEL:
            case OTHER_CANCEL:
                return Optional.of(DeliveryCancelReasonEnum.OTHER_CANCEL);
            default:
                return Optional.empty();
        }
    }
}
