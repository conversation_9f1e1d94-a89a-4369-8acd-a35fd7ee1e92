package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.dianping.cat.Cat;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ChannelTenantConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.DeliveryTenant;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.DeliveryTenantRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryConfigDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.DeliveryConfigDOExMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryConfigDOExample;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 配送租户仓储服务MySQL实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/10
 */
@Repository
public class MySQLDeliveryTenantRepository implements DeliveryTenantRepository {

	private static final int ENABLED = 1;
	private static final int DISABLED = 0;
	private static final int NON_DELETED = 0;

	@Resource
	private DeliveryConfigDOMapper deliveryConfigDOMapper;
	@Resource
	private DeliveryConfigDOExMapper deliveryConfigDOExMapper;

	@Override
	public DeliveryTenant getValidDeliveryTenant(Long tenantId) {
		Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> channelTenantMap = this.getChannelTenants(tenantId, true);
		if (MapUtils.isEmpty(channelTenantMap)) {
			return null;
		}

		return new DeliveryTenant(tenantId, channelTenantMap);
	}

	@Override
	public Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> getChannelTenants(Long tenantId, boolean onlyEnabled) {
		if (MccConfigUtils.closeQueryDeliveryConfigSwitch()) {
			Cat.logEvent("DELIVERY_CONFIG", "getChannelTenants");
			return new HashMap<>();
		}
		DeliveryConfigDOExample example = new DeliveryConfigDOExample();
		DeliveryConfigDOExample.Criteria criteria = example.createCriteria()
				.andTenantIdEqualTo(tenantId)
				.andIsDelEqualTo(NON_DELETED);
		if (onlyEnabled) {
			criteria.andEnabledEqualTo(ENABLED);
		}

		return translate(deliveryConfigDOMapper.selectByExample(example));
	}

	@Override
	public ChannelTenantConfig getDeliveryChannelTenant(Long tenantId, ThirdDeliveryChannelEnum deliveryChannel, boolean onlyEnabled) {
		if (MccConfigUtils.closeQueryDeliveryConfigSwitch()) {
			Cat.logEvent("DELIVERY_CONFIG", "getDeliveryChannelTenant");
			return null;
		}
		DeliveryConfigDOExample example = new DeliveryConfigDOExample();
		DeliveryConfigDOExample.Criteria criteria = example.createCriteria()
				.andTenantIdEqualTo(tenantId)
				.andDeliveryChannelIdEqualTo(deliveryChannel.getCode())
				.andIsDelEqualTo(NON_DELETED);
		if (onlyEnabled) {
			criteria.andEnabledEqualTo(ENABLED);
		}

		List<DeliveryConfigDO> records = deliveryConfigDOMapper.selectByExample(example);

		return translate(records).get(deliveryChannel);
	}

	@Override
	public Long getTenantIdByChannelAndAppKey(ThirdDeliveryChannelEnum deliveryChannel, String appKey) {
		if (MccConfigUtils.closeQueryDeliveryConfigSwitch()) {
			Cat.logEvent("DELIVERY_CONFIG", "getTenantIdByChannelAndAppKey");
			return null;
		}
		DeliveryConfigDOExample example = new DeliveryConfigDOExample();
		example.createCriteria()
				.andDeliveryChannelIdEqualTo(deliveryChannel.getCode())
				.andAppKeyEqualTo(appKey);

		List<DeliveryConfigDO> records = deliveryConfigDOMapper.selectByExample(example);
		return Optional.ofNullable(records)
				.filter(CollectionUtils::isNotEmpty)
				.map(it -> it.get(0))
				.map(DeliveryConfigDO::getTenantId)
				.orElse(null);
	}

	@Override
	public void saveChannelTenants(Long tenantId, List<ChannelTenantConfig> channelTenantList, Operator operator) {
		List<DeliveryConfigDO> needInsertRecords = translateNewRecords(tenantId, channelTenantList, operator);
		if (CollectionUtils.isNotEmpty(needInsertRecords)) {
			deliveryConfigDOExMapper.batchInsert(needInsertRecords);
		}

		List<DeliveryConfigDO> needUpdateRecords = translateExistingRecords(tenantId, channelTenantList, operator);
		if (CollectionUtils.isNotEmpty(needUpdateRecords)) {
			deliveryConfigDOExMapper.batchUpdate(needUpdateRecords);
		}
	}

	private Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> translate(List<DeliveryConfigDO> records) {
		if (CollectionUtils.isEmpty(records)) {
			return new HashMap<>();
		}

		return records.stream()
				.filter(it -> Objects.nonNull(ThirdDeliveryChannelEnum.valueOf(it.getDeliveryChannelId())))
				.map(it -> new ChannelTenantConfig(
						it.getId(),
						ThirdDeliveryChannelEnum.valueOf(it.getDeliveryChannelId()),
						it.getAppKey(),
						it.getSecretKey(),
						it.getEnabled() == ENABLED)
				)
				.collect(Collectors.toMap(ChannelTenantConfig::getDeliveryChannel, it -> it, (o1, o2) -> o2));
	}

	private List<DeliveryConfigDO> translateNewRecords(Long tenantId, List<ChannelTenantConfig> channelTenantList, Operator operator) {
		if (CollectionUtils.isEmpty(channelTenantList)) {
			return new ArrayList<>();
		}

		return channelTenantList.stream()
				.filter(it -> it.getId() == null)
				.map(it -> translate(tenantId, it, operator, it.isEnabled()))
				.collect(Collectors.toList());
	}

	private List<DeliveryConfigDO> translateExistingRecords(Long tenantId, List<ChannelTenantConfig> channelTenantList, Operator operator) {
		if (CollectionUtils.isEmpty(channelTenantList)) {
			return new ArrayList<>();
		}

		return channelTenantList.stream()
				.filter(it -> it.getId() != null)
				.map(it -> translate(tenantId, it, operator, it.isEnabled()))
				.collect(Collectors.toList());
	}

	private DeliveryConfigDO translate(Long tenantId, ChannelTenantConfig channelTenant, Operator operator, boolean enable) {
		DeliveryConfigDO record = new DeliveryConfigDO();
		record.setId(channelTenant.getId());
		record.setTenantId(tenantId);
		record.setDeliveryChannelId(channelTenant.getDeliveryChannel().getCode());
		record.setAppKey(channelTenant.getAppKey());
		record.setSecretKey(channelTenant.getSecretKey());
		record.setExtData("");
		record.setEnabled(enable ? ENABLED : DISABLED);
		record.setOperatorId(operator.getOperatorId());
		record.setOperatorName(operator.getOperatorName());
		record.setIsDel(NON_DELETED);

		return record;
	}
}
