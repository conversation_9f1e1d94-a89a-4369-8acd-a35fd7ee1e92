package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils.ObjectMapperWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DtsMsg;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Map;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * 通用DTS消息消费者
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractDtsMsgListener<T> extends AbstractDeadLetterConsumer {

    @Resource
    protected ObjectMapperWrapper objectMapper;

    @Override
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("Received msg: {}", message);
        DtsMsg<T> dtsMsg = deserialize(message.getBody());
        T msgBody = dtsMsg.getData();
        log.info("Msg body: {}, diff: {}", msgBody, dtsMsg.getDiffMapJson());

        try {
            switch (dtsMsg.getType()) {
                case "insert":
                    onInsert(msgBody);
                    break;
                case "update":
                    Map<String, Object> diffMap = objectMapper.readValue(dtsMsg.getDiffMapJson(), Map.class);
                    onUpdate(msgBody, diffMap);
                    break;
                case "delete":
                    onDelete(msgBody);
                    break;
                default:
                    log.error("Unknown message type: {}", dtsMsg);
            }
            return CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("Consuming DTS message error, message: {}, error: {}", message, e.getMessage(), e);
            return CONSUME_FAILURE;
        }
    }

    /**
     * 反序列化
     *
     * @param msgBody 消息体，通常是String
     * @return 对象
     */
    protected abstract DtsMsg<T> deserialize(Object msgBody);

    /**
     * 处理插入事件
     *
     * @param afterInsert 插入之后的行记录
     */
    protected void onInsert(T afterInsert) {
        log.info("do nothing on insert");
    }

    /**
     * 处理更新事件
     *
     * @param afterUpdate              更新后的行记录
     * @param updatedField2OldValueMap 更新过的列及其旧值
     */
    protected void onUpdate(T afterUpdate, Map<String, Object> updatedField2OldValueMap) {
        log.info("do nothing on update");
    }

    /**
     * 处理删除事件
     *
     * @param beforeDelete 删除之前的行记录
     */
    protected void onDelete(T beforeDelete) {
        log.info("do nothing on delete");
    }

}
