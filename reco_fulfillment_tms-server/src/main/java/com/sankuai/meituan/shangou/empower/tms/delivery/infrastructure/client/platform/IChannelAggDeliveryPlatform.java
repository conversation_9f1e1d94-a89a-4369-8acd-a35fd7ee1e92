package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform;

import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.*;
import org.apache.thrift.TException;

public interface IChannelAggDeliveryPlatform {

    CreateAggDeliveryResponse createDelivery(CreateAggDeliveryRequest request) throws TException;

    QueryAggDeliveryRiderInfoResponse queryRiderLocation(QueryAggDeliveryRiderInfoRequest request) throws TException;

    CreateAggDeliveryShopResponse createAggDeliveryShop(CreateAggDeliveryShopRequest request) throws TException;

    SyncOrderInfoChangeResponse syncOrderInfoChange(SyncOrderInfoChangeRequest request) throws TException;



}
