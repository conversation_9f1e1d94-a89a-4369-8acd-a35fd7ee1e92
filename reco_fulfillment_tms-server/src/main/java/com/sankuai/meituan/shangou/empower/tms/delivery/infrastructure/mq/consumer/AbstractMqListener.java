package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.gson.JsonObject;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.*;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.reco.pickselect.common.mq.consumer.*;
import com.meituan.reco.pickselect.common.mq.producer.MqProducer;
import com.meituan.reco.pickselect.common.mq.producer.CommonMqProducerKeepAlive;
import com.meituan.reco.pickselect.common.mq.producer.ProducerConfig;
import com.meituan.reco.pickselect.common.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Properties;

/**
 * 抽象的consumer，实现逻辑如下
 *
 * (1) 使用config()获得consumer配置
 * (2) 使用retryConfig()获得retry配置
 * (3) 如果retryConfig()为null(默认)或者maxRetry <= 0，消息处理失败会调用onFail接口，由应用层处理。与现在的处理保持一致
 * (4) 如果retryConfig()返回不为null，并且maxRetry大于0，会默认走retry逻辑，将消息延迟发送到mafka重试；
 *     如果重试超过最大次数，或者发送失败，会调用onFail接口
 * */
@Slf4j
public abstract class AbstractMqListener implements InitializingBean, DisposableBean {

    @Resource
    protected MqProducer mqProducer;

    protected IConsumerProcessor consumerProcessor;

    protected RetryConfig innerRetryConfig;

    protected ConsumerConfig consumerConfig;

    @Override
    public void destroy() {
        try {
            consumerProcessor.close();
        } catch (Exception e) {
            log.error("close consumer failed", e);
        }
    }

    @Override
    public final void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        initConfig(properties);

        doStartConsume(properties);
    }

    /**
     * 初始化属性，包括bgName，appKey，subscribeGroup，心跳间隔
     * */
    protected void initConfig(Properties properties) {
        checkConfig(config());

        consumerConfig = config();
        innerRetryConfig = retryConfig();

        properties.setProperty(ConsumerConstants.MafkaBGNamespace, consumerConfig.getBgName());
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, consumerConfig.getAppKey());
        properties.setProperty(ConsumerConstants.SubscribeGroup, consumerConfig.getConsumerGroupName());
        properties.setProperty(ConsumerConstants.CLIENT_HEARTBEAT_GAP_SECOND, "1");

        ReconsumeLaterConfig reconsumeLaterConfig = reconsumeLaterConfig();
        properties.setProperty(ConsumerConstants.CONSUME_MAX_RETRY_COUNT, String.valueOf(reconsumeLaterConfig.maxRetryCount()));
        properties.setProperty(ConsumerConstants.CONSUME_RETRY_MIN_BACKOFF_MS, String.valueOf(reconsumeLaterConfig.minBackOffMs()));
        properties.setProperty(ConsumerConstants.CONSUME_RETRY_MAX_BACKOFF_MS, String.valueOf(reconsumeLaterConfig.maxBackOffMs()));
    }

    /**
     * 启动消息消费，默认启动单个消息消费，子类可重写，实现批量消费
     * */
    protected void doStartConsume(Properties properties) throws Exception {
        startConsume(properties);
    }

    private void checkConfig(ConsumerConfig config) {
        if (config == null) {
            throw new MqConsumerConfigException("mq consumer config is null");
        }

        if(StringUtils.isEmpty(config.getBgName())) {
            throw new MqConsumerConfigException("BG name is empty");
        }

        if(StringUtils.isEmpty(config.getAppKey())) {
            throw new MqConsumerConfigException("app key is empty");
        }

        if(StringUtils.isEmpty(config.getConsumerGroupName())) {
            throw new MqConsumerConfigException("group name is empty");
        }

        if(StringUtils.isEmpty(config.getTopic())) {
            throw new MqConsumerConfigException("topic is empty");
        }
    }

    /**
     * 使用延迟队列重试
     *
     */
    RetryHandler retryHandlerByDelayQueue = new RetryHandler() {
        @Override
        public boolean doRetry(String message, int delay) {
            ProducerConfig producerConfig = innerRetryConfig.producerConfig();
            try {
                mqProducer.publish(producerConfig, message, delay);
            } catch (IOException e) {
                Cat.logEvent("MQ_CONSUMER_RETRY_SEND", config().getTopic(), "ERROR",  "topic "+config().getTopic()+" cannot publish for retry and abandon，msg="+message);
                log.error("topic {} cannot publish for retry and abandon，msg={}", config().getTopic(), message, e);
                return false;
            }
            return true;
        }
    };

    protected void startConsume(Properties properties) throws Exception {
        consumerProcessor = MafkaClient.buildCommonConsumerFactory(properties, config().getTopic());

        consumerProcessor.recvMessageWithParallel(String.class, new AbstractMessageListener() {
            @Override
            public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
                Transaction t = Cat.newTransaction("MQ_CONSUMER", config().getTopic());
                try {
                    filterKeepAliveMessage(mafkaMessage);
                    t.setStatus(Transaction.SUCCESS);
                } catch (ConsumerRetryException e) {
                    log.error("caught retry exception during process, msg: {}", mafkaMessage.getBody().toString(), e);
                    t.setStatus(e);
                    //重试失败的消息
                    if (!processRetriableFail(mafkaMessage, retryHandlerByDelayQueue)) {
                        return ConsumeStatus.RECONSUME_LATER;
                    }
                } catch (Exception e) {
                    log.error("caught exception during process, msg: {}", mafkaMessage.getBody().toString(), e);
                    t.setStatus(e);
                } finally {
                    t.complete();
                }
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        });
    }

    final boolean isKeepAliveMessage(MafkaMessage mafkaMessage) {
        return CommonMqProducerKeepAlive.KEEP_ALIVE_MSG.equals(mafkaMessage.getBody().toString());
    }

    void filterKeepAliveMessage(MafkaMessage mafkaMessage) throws ConsumerRetryException {
        if (mafkaMessage != null && mafkaMessage.getBody() != null) {
            if (isKeepAliveMessage(mafkaMessage)) {
                log.info("receive keep alive message and ignore");
            } else {
                consume(mafkaMessage);
            }
        }
    }

    public static interface RetryHandler{
        /**
         * 消息重试
         *
         * @param message：重试消息
         * @param delay: 延迟时间，单位秒
         *
         * @return 是否重试成功
         */
        boolean doRetry(String message, int delay);

    }

    public static interface RetryMafkaHandler{
        /**
         * 消息重试
         *
         * @param message：重试消息
         * @param delay: 延迟时间，单位秒
         *
         * @return 是否重试成功
         */
        boolean doRetry(MafkaMessage message, int delay);

    }

    protected boolean processMafkaRetriableFail(MafkaMessage mafkaMessage, RetryMafkaHandler retryHandler) {
        if((innerRetryConfig == null) || (innerRetryConfig.maxRetry() <= 0)) {
            log.info("common retry route is disabled for innerRetryConfig is null or innerRetryConfig.maxRetry <= 0");
            return callOnFailSilently(mafkaMessage);
        } else {
            int maxRetry = innerRetryConfig.maxRetry();
            int delay = innerRetryConfig.delayInSeconds();
            ProducerConfig producerConfig = innerRetryConfig.producerConfig();

            if(producerConfig == null) {
                log.error("producerConfig of innerRetryConfig is null, call onFail, messge = {}", mafkaMessage);
                return callOnFailSilently(mafkaMessage);
            }

            String messageBody = mafkaMessage.getBody().toString();
            JsonObject jsonObject = GsonUtils.fromJson(messageBody, JsonObject.class);
            int retryTimes = getRetryTimes(jsonObject);

            //需要使用延迟策略的地方，只需要将delay值从默认的5改为-1
            if(delay <= 0) {
                //将峰值请求打散到10s时间段  如果是重试请求，则往后延迟更久
                delay = 5 * (retryTimes + 1) + RandomUtils.nextInt(0,10);
                log.warn("delayInSeconds of innerRetryConfig <= 0, set it to {} seconds", delay);

            }

            if (retryTimes >= maxRetry) {
                Cat.logEvent("MQ_CONSUMER_MAX_RETRY", config().getTopic(), "ERROR", "topic "+config().getTopic()+" has retry for "+maxRetry+" times and abandon, msg: "+ messageBody);
                log.error("topic {} has retry for {} times and abandon，msg={}", config().getTopic(), maxRetry, mafkaMessage);
                return callOnFailSilently(mafkaMessage);
            } else {
                putRetryTimes(jsonObject, retryTimes);
                mafkaMessage.setBody(GsonUtils.toJson(jsonObject));
                return retryHandler.doRetry(mafkaMessage, delay);
            }
        }
    }

    /**
     * 使用retryHandler重试mafkaMessage，如果不可重试（没有配置RetryConfig）、重试次数<= 0或者达到最大重试次数，则调用onFail处理。
     * 如果onFail失败，会让Mafka重试
     *
     * */
    protected boolean processRetriableFail(MafkaMessage mafkaMessage, RetryHandler retryHandler) {

        if((innerRetryConfig == null) || (innerRetryConfig.maxRetry() <= 0)) {
            log.info("common retry route is disabled for innerRetryConfig is null or innerRetryConfig.maxRetry <= 0");
            return callOnFailSilently(mafkaMessage);
        } else {
            int maxRetry = innerRetryConfig.maxRetry();
            int delay = innerRetryConfig.delayInSeconds();
            ProducerConfig producerConfig = innerRetryConfig.producerConfig();

            if(producerConfig == null) {
                log.error("producerConfig of innerRetryConfig is null, call onFail, messge = {}", mafkaMessage);
                return callOnFailSilently(mafkaMessage);
            }

            String messageBody = mafkaMessage.getBody().toString();
            JsonObject jsonObject = GsonUtils.fromJson(messageBody, JsonObject.class);
            int retryTimes = getRetryTimes(jsonObject);

            //需要使用延迟策略的地方，只需要将delay值从默认的5改为-1
            if(delay <= 0) {
                //将峰值请求打散到10s时间段  如果是重试请求，则往后延迟更久
                delay = 5 * (retryTimes + 1) + RandomUtils.nextInt(0,10);
                log.warn("delayInSeconds of innerRetryConfig <= 0, set it to {} seconds", delay);

            }

            if (retryTimes >= maxRetry) {
                Cat.logEvent("MQ_CONSUMER_MAX_RETRY", config().getTopic(), "ERROR", "topic "+config().getTopic()+" has retry for "+maxRetry+" times and abandon, msg: "+ messageBody);
                log.error("topic {} has retry for {} times and abandon，msg={}", config().getTopic(), maxRetry, mafkaMessage);
                return callOnFailSilently(mafkaMessage);
            } else {
                putRetryTimes(jsonObject, retryTimes);
                return retryHandler.doRetry(GsonUtils.toJson(jsonObject), delay);
            }
        }
    }

    private int getRetryTimes(JsonObject jsonObject) {
        Integer retryTimes = jsonObject.get("retryTimes") == null ? null : jsonObject.get("retryTimes").getAsInt();
        return retryTimes == null ? 0 : retryTimes + 1;
    }

    private void putRetryTimes(JsonObject jsonObject, int retryTimes) {
        jsonObject.addProperty("retryTimes", retryTimes);
    }

    protected boolean callOnFailSilently(MafkaMessage mafkaMessage) {
        try {
            onFail(mafkaMessage);
            return true;
        } catch(Exception e) {
            log.error("call on fail silently exception", e);
            return false;
        }
    }

    protected abstract ConsumerConfig config();

    protected RetryConfig retryConfig() {
        return null;
    }

    protected ReconsumeLaterConfig reconsumeLaterConfig() {
        return new ReconsumeLaterConfig() {
            @Override
            public int maxRetryCount() {
                return 5;
            }

            @Override
            public int minBackOffMs() {
                return 100;
            }

            @Override
            public int maxBackOffMs() {
                return 1000;
            }
        };
    }

    /**
     * 消息消费。当跑出ConsumerRetryException时，会调用{@link #onFail(MafkaMessage)}处理
     * */
    protected abstract void consume(MafkaMessage message) throws ConsumerRetryException;

    protected void onFail(MafkaMessage mafkaMessage) {

    }
}
