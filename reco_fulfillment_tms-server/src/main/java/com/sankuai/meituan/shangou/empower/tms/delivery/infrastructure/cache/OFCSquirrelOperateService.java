package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache;

import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Slf4j
public abstract class OFCSquirrelOperateService extends SquirrelOperateService {

    @Resource(name = "redisSgNewSupplyOfc")
    protected RedisStoreClient redisNewSupplyClient;

    @Override
    protected RedisStoreClient getRedisClient() {
        return redisNewSupplyClient;
    }

}
