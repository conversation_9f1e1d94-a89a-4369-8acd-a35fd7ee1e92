package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache;

import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class OrderStatusDoubleCheckService extends OFCSquirrelOperateService{

    private static final String CATEGORY_NAME = "order_status_double_check";

    @Override
    public String getCategoryName() {
        return CATEGORY_NAME;
    }

    public boolean check(String orderId,Integer status){
        Optional<String> val = get(buildKey(orderId, status),String.class);
        return val.isPresent();
    }

    public void setCheck(String orderId,Integer status){
        set(buildKey(orderId, status),"1");
    }

    private String buildKey(String viewOrderId,Integer status){
        return "delivery_"+viewOrderId+"_"+status;
    }
}
