package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.tenant.thrift.ChannelManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.ChannelDetailDto;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.request.ChannelBatchRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.response.ChannelDetailListResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.Channel;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelThriftResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aop.LoadTestAop;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.wrapper.BusinessWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.sgfnqnh.poi.api.client.thrift.TenantThriftService;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.tenant.ChannelInfoDTO;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.tenant.response.TenantChannelMapResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/10/21 17:27
 */
@Component
@Slf4j
@Rhino
public class TenantChannelThriftServiceWrapper {

    @Autowired
    private ChannelThriftService.Iface channelThriftService;

    @Autowired
    private ChannelManageThriftService channelManageThriftService;

    @Autowired
    @Qualifier("qnhTenantThriftService")
    private TenantThriftService tenantThriftService;

    @Resource
    private TenantRemoteService tenantRemoteService;

    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    @Degrade(rhinoKey = "TenantChannelThriftServiceWrapper.getTenantChannelType", fallBackMethod = "getTenantChannelTypeFallback", timeoutInMilliseconds = 2000)
    @LoadTestAop
    public List<Integer> getTenantChannelType(String tenantId) {
        boolean isMedicineUW = tenantRemoteService.isMedicineUnmannedWarehouse(Long.parseLong(tenantId));

        if (MccConfigUtils.getQueryTenantChannelSwitch() && !isMedicineUW) {
            return queryTenantChannelFromCache(Long.valueOf(tenantId));
        }

        // 如果是牵牛花租户，从mcc配置里获取租户渠道，否则调用租户接口查询租户渠道
        List<Integer> qnhChannelTypeList = MccConfigUtils.getChannelType(tenantId);
        if (CollectionUtils.isNotEmpty(qnhChannelTypeList)) {
            log.info("租户是牵牛花租户，qnhChannelTypeList: {}, tenantId: {}", qnhChannelTypeList, tenantId);
            return qnhChannelTypeList;
        } else {
            List<Integer> channelTypeList = getChannelTypeFromTenantService(tenantId);
            return channelTypeList;
        }
    }

    /**
     * 批量查询渠道配置
     * @param channelIds channel id array
     * @return k-渠道Id,v-渠道配置
     */
    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public Map<Integer, ChannelDetailDto> batchQueryChannelConfig(List<Integer> channelIds){
        ChannelBatchRequest request = new ChannelBatchRequest();
        request.setChannelIds(channelIds);
        request.setIncludeConfig(true);
        request.setIncludeTenant(false);
        Map<Integer, ChannelDetailDto> channelDetailDtoMap = new HashMap<>();
        try {
            ChannelDetailListResponse channelDetailListResponse = channelManageThriftService.batchQueryChannelDetails(request);
            if (Objects.isNull(channelDetailListResponse)) {
                return new HashMap<>();
            }
            if (CollectionUtils.isEmpty(channelDetailListResponse.getChannelList())) {
                return new HashMap<>();
            }
            channelDetailDtoMap = channelDetailListResponse.getChannelList().stream()
                    .collect(Collectors.groupingBy(
                            ChannelDetailDto::getChannelId,
                            Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));

        } catch (Exception e) {
            log.error("ChannelThriftService.getTenantChannels", e);
            throw new CommonRuntimeException(e);
        }
        return channelDetailDtoMap;
    }

    /**
     * @description: 调用租户服务接口查询租户渠道
     * @param: tenantId 租户ID
     * @return 租户渠道
    */
    private List<Integer> getChannelTypeFromTenantService(String tenantId) {


        // 调用租户服务接口查询租户开通的渠道
        List<Integer> channelTypeList;
        try {
            ChannelThriftResponse response = channelThriftService.getTenantChannels(Long.parseLong(tenantId));
            if (CollectionUtils.isEmpty(response.getChannelList())) {
                log.info("查询租户渠道信息，根据租户ID查询门店开通的渠道列表为空, tenantId: {}", tenantId);
                return Arrays.asList(DynamicChannelType.MEITUAN.getChannelId(), DynamicChannelType.ELEM.getChannelId());
            }
            channelTypeList = response.getChannelList().stream().map(Channel::getId).collect(Collectors.toList());
        } catch (TException e) {
            log.error("ChannelThriftService.getTenantChannels, tenantId: {}", tenantId, e);
            throw new CommonRuntimeException(e);
        }
        return channelTypeList;
    }

    /**
     * 查询租户开通渠道，基础底层走缓存
    */
    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    @Degrade(rhinoKey = "TenantChannelThriftServiceWrapper.queryTenantChannel", fallBackMethod = "queryTenantChannelFromCacheFallback", timeoutInMilliseconds = 2000)
    public List<Integer> queryTenantChannelFromCache(Long tenantId) {
        try {
            TenantChannelMapResponse response = tenantThriftService.batchGetTenantChannels(Collections.singletonList(tenantId));
            if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
                log.error("TenantChannelThriftServiceWrapper queryTenantChannel error, response is null");
                return Collections.emptyList();
            }
            if (FailureCodeEnum.SUCCESS.getCode() != response.getStatus().getCode()) {
                log.error("TenantChannelThriftServiceWrapper queryTenantChannel failed, response code is not success");
                return Collections.emptyList();
            }
            Map<Long, List<ChannelInfoDTO>> channelMap = response.getData();
            if (MapUtils.isEmpty(channelMap)) {
                log.info("TenantChannelThriftServiceWrapper queryTenantChannel, channelMap is empty");
                return Collections.emptyList();
            }
            List<ChannelInfoDTO> channelInfoDTOList = channelMap.get(tenantId);
            if (CollectionUtils.isEmpty(channelInfoDTOList)) {
                log.info("TenantChannelThriftServiceWrapper queryTenantChannel, channelInfoDTOList is empty");
                return Collections.emptyList();
            }

            return channelInfoDTOList.stream().map(ChannelInfoDTO::getChannelId).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("TenantChannelThriftServiceWrapper queryTenantChannel error", e);
            return Collections.emptyList();
        }
    }

    public List<Integer> getTenantChannelTypeFallback(String tenantId) {
        log.error("TenantChannelThriftServiceWrapper getTenantChannelTypeFallback, tenantId is {}", tenantId);
        return Collections.emptyList();
    }

    public List<Integer> queryTenantChannelFromCacheFallback(Long tenantId) {
        log.error("TenantChannelThriftServiceWrapper queryTenantChannelFallback, tenantId is {}", tenantId);
        return Collections.emptyList();
    }

}
