package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.StoreConfig;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, componentModel = "spring")
public abstract class StoreConfigMergeConverter {
    /**
     * helper method to merge storeConfig from diffStoreConfig
     * @param diffStoreConfig diff store config
     * @param storeConfig store config to merge into
     */
    public abstract void mergeStoreConfigFromDiff(StoreConfig diffStoreConfig, @MappingTarget StoreConfig storeConfig);
}
