package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Setter
@Getter
@ToString
public class FulfillMsg {

    //数据补偿
    private FulfillCompensateMsg fulfillCompensateMsg;
    /**
     * 打印状态值
     */
    private DeliveryStatusMsg deliveryStatusMsg;

    //orderId
    private Long orderId;
    /**
     * 服务方，
     * 1：打印
     * 2：TMS
     * 3：拣货
     */
    private Integer serviceType;

    private Long tenantId;
}
