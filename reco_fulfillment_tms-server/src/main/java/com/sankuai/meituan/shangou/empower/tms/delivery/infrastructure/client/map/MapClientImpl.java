package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.map;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.reco.pickselect.common.exception.FallbackException;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RouteInfoDTO;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.map.MapClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.map.MapRouteRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.wmarch.map.client.MafClient;
import com.sankuai.wmarch.map.thrift.server.GeoRequest;
import com.sankuai.wmarch.map.thrift.server.RouteRequest;
import com.sankuai.wmarch.map.thrift.server.ThriftCommonResponse;
import com.sankuai.wmarch.map.vo.result.RouteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/12/27
 * @email jianglilin02@meituan
 */
@Slf4j
@Service
@Rhino
public class MapClientImpl implements MapClient {


    @Resource(name="mafClient")
    private MafClient mafClient;
    /**
     * 求路模式,传骑行
     */
    private static final String ROUTE_MODE = "RIDING";
    /**
     * 返回结果详略,需要返回所有结果
     */
    private static final String ROUTE_EXTENSIONS = "ALL";
    /**
     * 选择路线策略，最快
     */
    private static final String ROUTE_STRATEGY = "FASTEST ";
    private static final String SPLITTER = ",";
    private static final int MAP_SUCCESS_CODE = 200;

    @Override
    @Degrade(rhinoKey = "MapClient.queryRidePathDistance", fallBackMethod = "queryRidePathDistanceFallback", timeoutInMilliseconds = 1000)
    @Deprecated
    public Result<Double> queryRidePathDistance(MapRouteRequest req) {
        try {
            RouteRequest request = new RouteRequest();
            request.setKey((req.getType() == null || req.getType() == 1) ? MccConfigUtils.getMapReqKey():MccConfigUtils.getNewSupplyMapReqKey());
            request.setOrigin(concatLatitudeAndLongitude(req.getOriginLongitude(), req.getOriginLatitude()));
            request.setDestination(concatLatitudeAndLongitude(req.getDestinationLongitude(), req.getDestinationLatitude()));
            request.setMode(ROUTE_MODE);
            request.setExtensions(ROUTE_EXTENSIONS);
            request.setStrategy(ROUTE_STRATEGY);
            RouteResult routeResult = mafClient.route(request);
            log.info("invoke mafClient.route, req = {}, resp = {}", request, routeResult);
            if (routeResult.getStatus() != MAP_SUCCESS_CODE) {
                throw new BizException(StringUtils.defaultString(routeResult.getMsg()));
            }
            return new Result<>(routeResult.getResult().getDistance());
        } catch (Exception e) {
            log.error("invoke mafClient.route error, req = {}" , req ,e);
            return new Result<>(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
        }
    }

    @Override
    @Degrade(rhinoKey = "MapClient.queryCoordinatesByDetailAddress", fallBackMethod = "queryCoordinatesByDetailAddressFallback", timeoutInMilliseconds = 2000)
    public Result<CoordinatePoint> queryCoordinatesByDetailAddress(String address) {
        if (StringUtils.isBlank(address)) {
            return new Result<>(new Failure(false, FailureCodeEnum.INVALID_PARAM));
        }

        try {
            GeoRequest request = new GeoRequest(MccConfigUtils.getMapGeoQueryKey(), address);
            ThriftCommonResponse response = mafClient.getClient().geo(request);
            log.info("invoke mafClient.geo, req = {}, resp = {}", request, response);
            if(response.getStatus() != MAP_SUCCESS_CODE){
                return new Result<>(new Failure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "查询门店经纬度失败：" + response.getMsg()));
            }

            return extractCoordinatePoint(response);
        } catch (TException e) {
            log.error("invoke mafClient.geo error, address = {}" , address ,e);
            return new Result<>(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
        }
    }

    @Override
    public Double queryRideRidingDistance4DrunkHorse(MapRouteRequest req) {
        throw new RuntimeException("未实现此方法");
    }

    @Override
    public RouteInfoDTO queryRideRidingInfo4DrunkHorse(MapRouteRequest req) {
        throw new SystemException("未实现此方法");
    }

    private Result<CoordinatePoint> extractCoordinatePoint(ThriftCommonResponse response) {
        return Optional.ofNullable(response.getResult())
                .filter(StringUtils::isNotBlank)
                .map(it -> JsonUtil.fromJson(it, new TypeReference<List<MafGeoQueryResultJson>>() {
                }))
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(it -> it.stream().max(Comparator.comparing(MafGeoQueryResultJson::getReliability)))
                .flatMap(MafGeoQueryResultJson::getCoordinatePoint)
                .map(Result::new)
                .orElseGet(() -> new Result<>(new Failure(false, FailureCodeEnum.STORE_ADDRESS_INVALID)));
    }

    /**
     * 按地图接口要求拼接地址参数
     * - 经纬度之间以逗号","分隔，如117.500244,40.417801
     * - 经纬度小数点不超过6位
     * @param longitude 经度
     * @param latitude 纬度
     * @return 拼接后的地址
     */
    private String concatLatitudeAndLongitude(String longitude, String latitude) {
        return trimToSix(longitude) + SPLITTER + trimToSix(latitude);
    }

    private String trimToSix(String coordinatePoint) {
        BigDecimal pointBigDecimal = new BigDecimal(coordinatePoint);
        if (pointBigDecimal.scale() > 6) {
            pointBigDecimal = pointBigDecimal.setScale(6, RoundingMode.DOWN);
        }
        return pointBigDecimal.toPlainString();
    }

    private Result<Double> queryRidePathDistanceFallback(MapRouteRequest req, Throwable e) {
        log.error("queryRidePathDistanceFallback throw ex", e);
        throw new FallbackException("queryRidePathDistance fallback");
    }

    private Result<CoordinatePoint> queryCoordinatesByDetailAddressFallback(String address) {
        throw new FallbackException("queryCoordinatesByDetailAddress 熔断降级");
    }
}
