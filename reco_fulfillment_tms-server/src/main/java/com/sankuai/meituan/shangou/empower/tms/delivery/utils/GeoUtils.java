package com.sankuai.meituan.shangou.empower.tms.delivery.utils;

import com.meituan.linz.boot.util.Assert;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/9 15:10
 **/
public class GeoUtils {
    private static final GeometryFactory geometryFactory = new GeometryFactory();

    /**
     * 创建几何坐标
     *
     * @param coordinate 地理点位
     * @return 几何坐标
     */
    public static Point createPoint(Coordinate coordinate) {
        Assert.throwIfNull(coordinate, "地理点位不得为空");
        return geometryFactory.createPoint(coordinate);
    }

    /**
     * 创建平面几何区域
     *
     * @param pointList 顶点列表
     * @return 几何区域
     */
    public static Polygon createPolygon(List<Coordinate> pointList) {
        checkLinearRing(pointList);
        Coordinate start = pointList.get(0);
        Coordinate end = pointList.get(pointList.size() - 1);
        List<Coordinate> tempCoordinates = new ArrayList<>(pointList);
        if (!start.equals2D(end)) {
            tempCoordinates.add(new Coordinate(start));
        }
        Coordinate[] coordinates = tempCoordinates.toArray(new Coordinate[0]);
        return geometryFactory.createPolygon(coordinates);
    }

    /**
     * 根据经纬度创建几何坐标
     * @param originLgt 原始经度 103792856
     * @param originLat 原始纬度 239897908
     * @return
     */
    public static Coordinate createCoordinate(Long originLat, Long originLgt) {
        return new Coordinate(convert2StandardFormat(originLat), convert2StandardFormat(originLgt));
    }

    /**
     * 根据经纬度创建几何坐标
     * @param lgt 标准经度 103.792856
     * @param lat 标准纬度 23.9897908
     * @return
     */
    public static Coordinate createCoordinate(Double lat, Double lgt) {
        return new Coordinate(lat, lgt);
    }

    private static Double convert2StandardFormat(Long origin) {
        Assert.throwIfNull(origin, "经/纬度不能为null");
        BigDecimal DIVIDER = BigDecimal.valueOf(1000000);
        BigDecimal originBigDecimal = new BigDecimal(origin);

        return originBigDecimal.divide(DIVIDER, 6, RoundingMode.HALF_UP)
                .doubleValue();
    }

    public static void checkLinearRing(List<Coordinate> pointList) {
        Assert.throwIfEmpty(pointList, "顶点列表不得为空");
        Coordinate start = pointList.get(0);
        if (start.equals(pointList.get(pointList.size() - 1))) {
            // 首尾同一个点时
            Assert.throwIfTrue(pointList.size() < 4, "首尾点相同时, 至少4个顶点才能组成区域 input:{}", pointList);
        } else {
            Assert.throwIfTrue(pointList.size() < 3, "至少3个顶点才能组成区域 input:{}", pointList);
        }
    }
}
