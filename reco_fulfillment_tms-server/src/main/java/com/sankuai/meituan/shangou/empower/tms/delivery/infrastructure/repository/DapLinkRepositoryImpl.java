package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.dianping.squirrel.client.StoreKey;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DapLinkRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformUrlInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.LinkTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.DapLinkSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.DapDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.model.DapLinkInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.google.common.collect.Sets.newHashSet;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class DapLinkRepositoryImpl implements DapLinkRepository {

    @Resource
    private DapLinkSquirrelOperationService dapLinkSquirrelOperationService;

    @Resource
    private DapDeliveryPlatformClient dapDeliveryPlatformClient;

    @Override
    public Map<String, String> batchQueryDapLinkUrl(Long storeId, List<String> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Maps.newHashMap();
        }

        Map<String, String> cacheUrlMap = queryDapLinkUrlFromCache(orderIds);
        if (MapUtils.isEmpty(cacheUrlMap)) {
            Map<String, String> clientUrlMap = queryDapLinkUrlFromClient(storeId, orderIds);
            saveDapLinkUrlToCache(clientUrlMap);
            return clientUrlMap;
        }

        // 如果待查询的orderIds都在缓存中，直接返回缓存里的数据
        Set<String> missOrderIds = Sets.difference(newHashSet(orderIds), newHashSet(cacheUrlMap.keySet()));

        if (CollectionUtils.isEmpty(missOrderIds)) {
            return cacheUrlMap;
        }

        // 缓存没命中的orderIds，调青云的http接口批量查询返回，并更新到缓存
        Map<String, String> clientUrlMap = queryDapLinkUrlFromClient(storeId, new ArrayList<>(missOrderIds));
        saveDapLinkUrlToCache(clientUrlMap);

        Map<String, String> result = Maps.newHashMap();
        result.putAll(cacheUrlMap);
        result.putAll(clientUrlMap);
        return result;
    }

    private Map<String, String> queryDapLinkUrlFromCache(List<String> orderIds) {
        try {
            Map<StoreKey, Optional<DapLinkInfo>> urlMap = dapLinkSquirrelOperationService.multiGet(orderIds, DapLinkInfo.class);
            if (MapUtils.isEmpty(urlMap)) {
                return Maps.newHashMap();
            }

            return urlMap.values().stream().filter(Optional::isPresent).map(Optional::get)
                    .collect(Collectors.toMap(DapLinkInfo::getOrderId, DapLinkInfo::getUrl));
        } catch (Exception e) {
            log.error("DapLinkRepository queryDapLinkUrlFromCache error", e);
            return Maps.newHashMap();
        }
    }

    private Map<String, String> queryDapLinkUrlFromClient(Long storeId, List<String> orderIds) {
        try {
            Map<String, DeliveryPlatformUrlInfo> dapUrlMap = dapDeliveryPlatformClient.batchQueryDapLinkInfo(storeId, orderIds, LinkTypeEnum.ORDER_LINK_TYPE);
            if (MapUtils.isEmpty(dapUrlMap)) {
                return Maps.newHashMap();
            }

            return dapUrlMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getLinkUrl()));
        } catch (Exception e) {
            log.error("DapLinkRepository queryDapLinkUrlFromClient error", e);
            return Maps.newHashMap();
        }
    }

    private void saveDapLinkUrlToCache(Map<String, String> clientUrlMap) {
        if (MapUtils.isEmpty(clientUrlMap)) {
            return;
        }
        try {
            Map<String, DapLinkInfo> dapLinkInfoMap = clientUrlMap.entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> new DapLinkInfo(entry.getKey(), entry.getValue())));
            int expireInSeconds = MccConfigUtils.getDapLinkSquirrelExpireMinutes() * 60;
            dapLinkSquirrelOperationService.multiSet(dapLinkInfoMap, expireInSeconds);
        } catch (Exception e) {
            log.error("DapLinkRepository saveDapLinkUrlFromCache error", e);
        }
    }

}
