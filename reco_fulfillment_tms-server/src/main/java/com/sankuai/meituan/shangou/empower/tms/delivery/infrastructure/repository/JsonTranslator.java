package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.json.AddressJson;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.json.OrderPlatformDeliveryConfigJson;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.json.SequentialPollingStrategyConfigJson;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.OrderPlatformDeliveryConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.DeliveryStrategyConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStrategyEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SequentialPollingStrategyConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/20
 */
public class JsonTranslator {

	public static String toJson(Address address) {
		if (address == null) {
			return null;
		}

		return JsonUtil.toJson(
				new AddressJson(
						address.getAddressDetail(),
						address.getCoordinateType().getCode(),
						Optional.ofNullable(address.getCoordinatePoint()).map(CoordinatePoint::getLongitude).orElse(null),
						Optional.ofNullable(address.getCoordinatePoint()).map(CoordinatePoint::getLatitude).orElse(null)
				)
		);
	}

	public static Address fromAddressJson(String json) {
		AddressJson dto = JsonUtil.fromJson(json, AddressJson.class);
		if (dto == null) {
			return null;

		} else {
			return new Address(
					dto.getAddressDetail(),
					CoordinateTypeEnum.valueOf(dto.getCoordinateType()),
					StringUtils.isNotBlank(dto.getLongitude()) && StringUtils.isNotBlank(dto.getLatitude()) ?
							new CoordinatePoint(dto.getLongitude(), dto.getLatitude()) : null
			);
		}
	}

	public static String toJson(DeliveryStrategyConfig deliveryStrategyConfig) {
		if (deliveryStrategyConfig == null) {
			return null;
		}

		if (deliveryStrategyConfig instanceof SequentialPollingStrategyConfig) {
			SequentialPollingStrategyConfig config = (SequentialPollingStrategyConfig) deliveryStrategyConfig;
			return JsonUtil.toJson(
					new SequentialPollingStrategyConfigJson(
							Optional.ofNullable(config.getOrderedDeliveryChannels())
									.orElse(new ArrayList<>())
									.stream()
									.map(ThirdDeliveryChannelEnum::getCode)
									.collect(Collectors.toList()),
							config.getTimeoutForShiftDeliveryChannelInMinutes()
					)
			);

		} else {
			return null;
		}
	}

	public static DeliveryStrategyConfig fromDeliveryStrategyConfigJson(String json, DeliveryStrategyEnum deliveryStrategy) {
		if (deliveryStrategy == DeliveryStrategyEnum.SEQUENTIAL_POLLING) {
			SequentialPollingStrategyConfigJson dto = JsonUtil.fromJson(json, SequentialPollingStrategyConfigJson.class);
			if (dto == null) {
				return null;
			} else {
				return new SequentialPollingStrategyConfig(
						Optional.ofNullable(dto.getOrderedDeliveryChannels())
								.orElse(new ArrayList<>())
								.stream()
								.map(ThirdDeliveryChannelEnum::valueOf)
								.collect(Collectors.toList()),
						dto.getTimeoutForShiftDeliveryChannelInMinutes()
				);
			}

		} else {
			return new DeliveryStrategyConfig();
		}
	}

	public static String toJson(Map<Integer, OrderPlatformDeliveryConfig> configMap) {
		if (MapUtils.isEmpty(configMap)) {
			return null;
		}

		return JsonUtil.toJson(
				configMap.entrySet()
						.stream()
						.collect(Collectors.toMap(
								Map.Entry::getKey,
								it -> new OrderPlatformDeliveryConfigJson(
										it.getValue().getMinOrderPrice(),
										it.getValue().getDeliveryFee(),
										it.getValue().getDeliveryMinutes()
								)
						))
		);
	}

	public static Map<Integer, OrderPlatformDeliveryConfig> fromOrderPlatformDeliveryConfigJson(String json) {
		Map<Integer, OrderPlatformDeliveryConfigJson> dtoMap = JsonUtil.fromJson(
				json,
				new TypeReference<Map<Integer, OrderPlatformDeliveryConfigJson>>() {
				}
		);

		if (dtoMap == null) {
			return new HashMap<>();
		}

		return dtoMap.entrySet()
				.stream()
				.collect(Collectors.toMap(
						Map.Entry::getKey,
						it -> new OrderPlatformDeliveryConfig(
								it.getValue().getMinOrderPrice(),
								it.getValue().getDeliveryFee(),
								it.getValue().getDeliveryMinutes()
						)
				));
	}
}
