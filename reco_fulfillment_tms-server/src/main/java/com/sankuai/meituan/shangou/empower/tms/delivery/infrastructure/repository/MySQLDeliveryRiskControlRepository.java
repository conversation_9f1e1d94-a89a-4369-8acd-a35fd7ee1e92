package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryRiskControlOrderDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.DeliveryRiskControlOrderRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/18 19:06
 **/
@Repository
public class MySQLDeliveryRiskControlRepository implements DeliveryRiskControlOrderRepository {

    @Resource
    private DeliveryRiskControlOrderDOMapper deliveryRiskControlOrderDOMapper;

    @Override
    public List<DeliveryRiskControlOrderDO> queryByViewOrderIdList(List<String> viewOrderIdList) {
        if (CollectionUtils.isEmpty(viewOrderIdList)) {
            return Collections.emptyList();
        }

        List<DeliveryRiskControlOrderDO> riskControlOrderList = new ArrayList<>();

        List<List<String>> lists = Lists.partition(viewOrderIdList, 50);
        for (List<String> subList : lists) {
            DeliveryRiskControlOrderDOExample example = new DeliveryRiskControlOrderDOExample();
            example.createCriteria()
                    .andOrderIdViewIn(subList);
            riskControlOrderList.addAll(deliveryRiskControlOrderDOMapper.selectByExample(example));
        }

        return riskControlOrderList;
    }

    @Override
    public Long countByDtAndAccount(Long dt, Long riderAccountId) {
        DeliveryRiskControlOrderDOExample example = new DeliveryRiskControlOrderDOExample();
        example.createCriteria()
                .andDtEqualTo(dt)
                .andRiderAccountIdEqualTo(riderAccountId);

        return deliveryRiskControlOrderDOMapper.countByExample(example);
    }
}
