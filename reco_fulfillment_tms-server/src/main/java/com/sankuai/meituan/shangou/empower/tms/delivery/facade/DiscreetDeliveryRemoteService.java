package com.sankuai.meituan.shangou.empower.tms.delivery.facade;

import java.util.Objects;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.aop.LoadTestAop;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dianping.rhino.annotation.Rhino;
import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.shangou.qnh.ofc.ebase.consts.OfcEbaseResultCodeEnum;
import com.sankuai.shangou.qnh.ofc.ebase.request.DiscreetDeliveryConfigQueryRequest;
import com.sankuai.shangou.qnh.ofc.ebase.response.DiscreetDeliveryConfigQueryResponse;
import com.sankuai.shangou.qnh.ofc.ebase.service.FulfillmentStoreConfigThriftService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@Rhino
public class DiscreetDeliveryRemoteService {

    @Autowired
    private FulfillmentStoreConfigThriftService fulfillmentStoreConfigThriftService;

    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    @LoadTestAop
    public Boolean queryDiscreetDelivery(Long tenantId, Long warehouseId) {
        try {
            DiscreetDeliveryConfigQueryRequest request = new DiscreetDeliveryConfigQueryRequest();
            request.setTenantId(tenantId);
            request.setWarehouseId(warehouseId);

            DiscreetDeliveryConfigQueryResponse response = fulfillmentStoreConfigThriftService.queryDiscreetDeliveryConfig(request);
            log.info("queryDiscreetDelivery end, tenantId:{}, warehouseId:{}, response:{}", tenantId, warehouseId, response);
            if (Objects.nonNull(response) &&
                    Objects.nonNull(response.getCode()) &&
                    response.getCode().equals(OfcEbaseResultCodeEnum.SUCCESS.getCode()) &&
                    Objects.nonNull(response.getData()) &&
                    Objects.nonNull(response.getData().getDiscreetDelivery())) {
                return response.getData().getDiscreetDelivery();
            }
        }
        catch (Exception e) {
            log.error("queryDiscreetDelivery error, tenantId:{}, warehouseId:{}", tenantId, warehouseId, e);
        }
        return null;
    }
}
