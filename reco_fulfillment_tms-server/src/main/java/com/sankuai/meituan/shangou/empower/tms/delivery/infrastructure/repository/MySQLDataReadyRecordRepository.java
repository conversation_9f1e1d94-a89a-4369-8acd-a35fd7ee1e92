package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DataReadyRecordMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DataReadyRecord;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DataReadyRecordExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.DataReadyRecordRepository;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/19 16:11
 **/
@Repository
public class MySQLDataReadyRecordRepository implements DataReadyRecordRepository {
    @Resource
    private DataReadyRecordMapper dataReadyRecordMapper;

    @Override
    public boolean checkIsReady(String tableName, Long dt) {
        DataReadyRecordExample example = new DataReadyRecordExample();
        example.createCriteria()
                .andTableNameEqualTo(tableName)
                .andDtEqualTo(dt);
        List<DataReadyRecord> dataReadyRecords =
                dataReadyRecordMapper.selectByExample(example);


        return CollectionUtils.isNotEmpty(dataReadyRecords);
    }
}
