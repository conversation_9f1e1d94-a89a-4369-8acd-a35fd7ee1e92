package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;


import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryOperationApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryProcessApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryMonitorDomainService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.BookingOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.PickSelectRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryLaunchCommandMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryTimeOutCheckMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DHDeliveryStatusCheckMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.wrapper.MedicineTenantWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfigRepository;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * <AUTHOR>
 * @date 2020/3/12
 * desc: 订单拣货完成消息Listener
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class OrderPickFinishMessageListener extends AbstractLaunchDeliveryListener {

	@Resource
	private OrderSystemClient orderSystemClient;
	@Resource
	private DeliveryProcessApplicationService deliveryProcessApplicationService;
	@Resource
	private MafkaDelayMessageProducer<DeliveryLaunchCommandMessage> deliveryDelayLaunchMessageProducer;
	@Resource
	MafkaDelayMessageProducer<DHDeliveryStatusCheckMessage> dhDeliveryStatusCheckMessageProducer;
	@Resource
	private DeliveryOperationApplicationService deliveryOperationApplicationService;
	@Resource
	private MafkaDelayMessageProducer<DeliveryTimeOutCheckMessage> deliveryTimeOutCheckProducer;
	@Resource
	private DeliveryMonitorDomainService deliveryMonitorDomainService;
	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;
	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private DeliveryRemindConfigRepository deliveryRemindConfigRepository;

	@Resource
	private PickSelectRemoteService pickSelectRemoteService;

	@Resource
	private TenantRemoteService tenantRemoteService;

	@Resource
	private MedicineTenantWrapper medicineTenantWrapper;

	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.PICK_FINISH;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
		log.info("开始消费拣货完成消息：{}", mafkaMessage);
		MessageDTO message = translateMessage(mafkaMessage);
		if (message == null) {
			return CONSUME_SUCCESS;
		}
		//隔离医药租户
		if (medicineTenantWrapper.isUwmsMedicineTenant(message.getTenantId())) {
			return CONSUME_SUCCESS;
		}

		// 若收到歪马自营骑手的拣货完成消息，则发送延时消息去检查配送状态是否流转到了已取货
		if (MccConfigUtils.getDHTenantIdList().contains(message.getTenantId().toString())) {
			DHDeliveryStatusCheckMessage dhDeliveryStatusCheckMessage = new DHDeliveryStatusCheckMessage(message.getTenantId(),
					message.getShopId(), message.getOrderId(), RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode());
			dhDeliveryStatusCheckMessageProducer.sendDelayMessageInMillis(dhDeliveryStatusCheckMessage ,
					DhDeliveryStatusCheckMessageListener.CHECK_DELAY_MILLIS);
		}
    	Long shopId = message.getShopId();
		if(message.getWarehouseId() != null){
			shopId = message.getWarehouseId();
		}
		try {
			return tryLaunchDelivery(
					mafkaMessage,
					new OrderKey(message.getTenantId(), shopId, message.getOrderId()),
					ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE,
					BookingOrderDeliveryLaunchPointEnum.PICK_DONE
			);
		} catch (Exception e) {
			//后续会依赖死信进行重试
			log.warn("消费拣货完成消息失败", e);
			return CONSUME_FAILURE;
		}
	}

	@Override
	protected OrderSystemClient getOrderSystemClient() {
		return orderSystemClient;
	}

	@Override
	protected DeliveryProcessApplicationService getDeliveryProcessApplicationService() {
		return deliveryProcessApplicationService;
	}

	@Override
	protected MafkaDelayMessageProducer<DeliveryLaunchCommandMessage> getDeliveryDelayLaunchMessageProducer() {
		return deliveryDelayLaunchMessageProducer;
	}

	@Override
	protected DeliveryOperationApplicationService getDeliveryOperationApplicationService() {
		return deliveryOperationApplicationService;
	}

	@Override
	protected MafkaDelayMessageProducer<DeliveryTimeOutCheckMessage> getDeliveryTimeOutCheckProducer() {
		return deliveryTimeOutCheckProducer;
	}

	@Override
	protected DeliveryMonitorDomainService getDeliveryMonitorDomainService() {
		return deliveryMonitorDomainService;
	}

	@Override
	protected DeliveryPoiRepository getDeliveryPoiRepository() {
		return deliveryPoiRepository;
	}

	@Override
	protected DeliveryOrderRepository getDeliveryOrderRepository() {
		return deliveryOrderRepository;
	}
	@Override
	protected TenantRemoteService getTenantRemoteService(){
		return tenantRemoteService;
	}

	@Override
	protected DeliveryRemindConfigRepository getDeliveryRemindConfigRepository() {
		return deliveryRemindConfigRepository;
	}

	@Override
	protected PickSelectRemoteService getPickSelectRemoteService() {
		return pickSelectRemoteService;
	}


	private MessageDTO translateMessage(MafkaMessage mafkaMessage) {
		try {
			MessageDTO message = translateMessageWithBodyHolder(mafkaMessage, MessageDTO.class);

			Preconditions.checkNotNull(message, "empty mafkaMessage");
			Preconditions.checkNotNull(message.getTenantId(), "tenantId is null");
			Preconditions.checkNotNull(message.getShopId(), "shopId is null");
			Preconditions.checkNotNull(message.getOrderId(), "orderId is null");

			return message;
		} catch (Exception e) {
			log.error("解析拣货完成消息失败:{}", mafkaMessage, e);
			return null;
		}
	}

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class MessageDTO {
		private Long tenantId;
		private Long shopId;
		private Long orderId;
		private Long warehouseId;
	}
}
