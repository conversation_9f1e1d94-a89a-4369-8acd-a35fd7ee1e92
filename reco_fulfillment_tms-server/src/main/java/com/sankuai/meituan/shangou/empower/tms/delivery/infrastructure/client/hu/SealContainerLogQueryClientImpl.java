package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.hu;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.hu.SealContainerLogQueryClient;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.logistics.hu.api.dto.Result;
import com.sankuai.shangou.logistics.hu.api.dto.SealContainerLogDTO;
import com.sankuai.shangou.logistics.hu.api.service.SealContainerLogService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/6 15:12
 **/

@Rhino
@Slf4j
public class SealContainerLogQueryClientImpl implements SealContainerLogQueryClient {
    @Resource
    private SealContainerLogService sealContainerLogService;

    @Override
    @Degrade(rhinoKey = "SealContainerLogQueryClientImpl.queryUsingContainerByOrderKey", fallBackMethod = "queryUsingContainerByOrderKeyFallback", timeoutInMilliseconds = 2000)
    public List<SealContainerLogDTO> queryUsingContainerByOrderKey(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType) {
        log.info("start invoke sealContainerLogService.queryLatestSealContainerOperateLog, storeId: {}, viewOrderId: {}", storeId, channelOrderId);
        Result<List<SealContainerLogDTO>> result = sealContainerLogService.queryLatestSealContainerOperateLog(Collections.singletonList(storeId), tenantId, Collections.emptyList());
        log.info("end invoke sealContainerLogService.queryLatestSealContainerOperateLog, result: {}", result);
        if (result.getCode() != 0) {
            throw new BizException("查询容具使用记录失败");
        }

        return result.getModule().stream()
                .filter(sealContainerLog -> Objects.equals(sealContainerLog.getTradeOrderNo(), channelOrderId)
                        && Objects.equals(sealContainerLog.getTradeOrderBizType(), orderBizType))
                .collect(Collectors.toList());
    }
    @Override
    @Degrade(rhinoKey = "SealContainerLogQueryClientImpl.signReturnTimeout", fallBackMethod = "signReturnTimeoutFallback", timeoutInMilliseconds = 2000)
    public void signReturnTimeout(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType) {
        log.info("start invoke sealContainerLogService.signReturnTimeout, storeId: {}, viewOrderId: {}", storeId, channelOrderId);
        Result<Void> result = sealContainerLogService.signReturnTimeout(storeId, tenantId, channelOrderId, orderBizType);
        log.info("end invoke sealContainerLogService.signReturnTimeout, result: {}", result);
        if (result.getCode() != 0) {
            throw new BizException("标记容具归还超时失败");
        }
    }


    @Override
    @Degrade(rhinoKey = "SealContainerLogQueryClientImpl.signOrderCompletedOrCanceled", fallBackMethod = "signOrderCompletedOrCanceledFallback", timeoutInMilliseconds = 2000)
    public void signOrderCompletedOrCanceled(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType) {
        log.info("start invoke sealContainerLogService.signOrderCompletedOrCanceled, storeId: {}, viewOrderId: {}", storeId, channelOrderId);
        Result<Void> result = sealContainerLogService.signOrderCompletedOrCanceled(storeId, tenantId, channelOrderId, orderBizType);
        log.info("end invoke sealContainerLogService.signOrderCompletedOrCanceled, result: {}", result);
        if (result.getCode() != 0) {
            throw new BizException("标记容具对应订单已完成或已取消失败");
        }
    }
    public void signReturnTimeoutFallback(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType) {
        log.error("SealContainerLogQueryClientImpl.signReturnTimeout 发生降级");
    }
    public void signOrderCompletedOrCanceledFallback(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType) {
        log.error("SealContainerLogQueryClientImpl.signOrderCompletedOrCanceled 发生降级");
    }

    public List<SealContainerLogDTO> queryUsingContainerByOrderKeyFallback(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType) {
        log.error("SealContainerLogQueryClientImpl.queryUsingContainerByOrderKey 发生降级");
        return Collections.emptyList();
    }
}
