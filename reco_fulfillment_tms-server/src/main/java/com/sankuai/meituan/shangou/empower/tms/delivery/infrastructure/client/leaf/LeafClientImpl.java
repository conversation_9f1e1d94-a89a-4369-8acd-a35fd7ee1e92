package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.leaf;

import com.sankuai.inf.leaf.thrift.IDGen;
import com.sankuai.inf.leaf.thrift.Result;
import com.sankuai.inf.leaf.thrift.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.leaf.LeafClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022/9/8 18:47
 **/
@Service
@Slf4j
public class LeafClientImpl implements LeafClient {

    @Resource(name = "idGen")
    private IDGen.Iface idGen;

    private static final int INVOKE_RETRY_TIME = 3;

    @Resource
    private String leafKey;


    public long nextSnowflakeId() {
        int retryTime = 0;
        Result result = null;
        while (++retryTime <= INVOKE_RETRY_TIME) {
            try {
               result = idGen.getSnowFlake(leafKey);
            } catch (TException e) {
                if (e.getMessage() != null && e.getMessage().contains("timeout")) {
                    continue;
                }
                //非超时异常，或因超时重试达到最大重试次数
                if ((e.getMessage() != null && !e.getMessage().contains("timeout")) || retryTime >= INVOKE_RETRY_TIME) {
                    throw new CommonRuntimeException(e);
                }
            } catch (Exception e) {
                throw new CommonRuntimeException(e);
            }

            if (Objects.nonNull(result) && Status.SUCCESS.equals(result.getStatus())) {
                return result.getId();
            } else {
                throw new CommonRuntimeException("leaf id生成异常");
            }
        }

        throw new CommonRuntimeException("leaf id生成错误");
    }

}
