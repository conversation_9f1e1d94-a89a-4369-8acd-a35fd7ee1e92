package com.sankuai.meituan.shangou.empower.tms;

import com.dianping.rhino.spring.RhinoConfiguration;
import com.meituan.shangou.saas.order.platform.common.types.EnableDynamicChannelType;
import com.sankuai.wmarch.map.spring.MafSpringConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.ImportResource;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@EnableTransactionManagement
@ImportResource("classpath:/applicationContext.xml")
@Import(MafSpringConfiguration.class)
@RhinoConfiguration
@EnableRetry
@EnableDynamicChannelType
@DependsOn("springContextUtils")
public class StartApp {
    private static final Logger log = LoggerFactory.getLogger(StartApp.class);

    public static void main(String[] args) {
        SpringApplication.run(StartApp.class, args);
        log.info("服务启动成功！");
    }
}
