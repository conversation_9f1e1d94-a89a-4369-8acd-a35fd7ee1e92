package com.sankuai.meituan.shangou.empower.tms.delivery.enums;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;


/**
 * 抖音平台配送，配送状态与百川统一配送状态映射
 * <AUTHOR>
 * @see "https://bytedance.larkoffice.com/docx/PYytd0kxvo5jEqxsIgoc9AKhnmg"
 */
@Slf4j
public enum DyOrderChannelDeliveryStatusEnum {

    WAIT_TO_RECEIVE(101, "呼叫运力"),
    WAIT_TO_TAKE(102, "骑手已接单"),
    MERCHANT_DELIVERING(103, "骑手已到店"),
    DELIVERY_DONE(104, "骑手已取货"),
    DELIVERY_CANCELLED(200, "已送达"),
    DELIVERY_EXPIRE(300, "配送已取消"),
    ;

    private final int code;

    private final String msg;

    DyOrderChannelDeliveryStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static DyOrderChannelDeliveryStatusEnum enumOf(int value) {
        for (DyOrderChannelDeliveryStatusEnum each : values()) {
            if (each.getCode() == value) {
                return each;
            }
        }

        return null;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static Optional<DeliveryStatusEnum> mapToDeliveryStatus(int value) {
        String dyDeliveryStatusMappingStr = MccConfigUtils.queryDyOrderChannelDeliveryStatusMapping();
        if (StringUtils.isEmpty(dyDeliveryStatusMappingStr)) {
            log.error("DyOrderChannelDeliveryStatusEnum mapToDeliveryStatus error, dyDeliveryStatusMappingStr is empty");
            return Optional.empty();
        }

        Map<String, Integer> sourceMap;
        try {
            sourceMap = JSON.parseObject(dyDeliveryStatusMappingStr, new TypeReference<Map<String, Integer>>() {}.getType());
        } catch (Exception e) {
            log.error("mapToDeliveryStatus error, parse dyDeliveryStatusMappingStr error, dyDeliveryStatusMappingStr: {}", dyDeliveryStatusMappingStr);
            return Optional.empty();
        }

        Integer qnhDeliveryStatusCode = sourceMap.get(String.valueOf(value));
        if (Objects.isNull(qnhDeliveryStatusCode)) {
            log.error("mapToDeliveryStatus error, qnhDeliveryStatusCode is null");
            return Optional.empty();
        }

        return Optional.ofNullable(DeliveryStatusEnum.valueOf(qnhDeliveryStatusCode));
    }
}
