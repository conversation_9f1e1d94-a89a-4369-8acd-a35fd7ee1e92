package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryNotifyService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DeliveryTransSelfMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class DeliveryNotifyServiceImpl implements DeliveryNotifyService {

    @Resource
    private MafkaMessageProducer<DeliveryTransSelfMsg> deliveryOrderTansMessageProducer;

    @Autowired
    private MafkaMessageProducer<OrderTrackEvent> orderTrackLogProducer;
    @Override
    public void notifyDeliveryTransMsg(Long tenantId, Long storeId, Long orderId) {
        DeliveryTransSelfMsg msg = new DeliveryTransSelfMsg();
        msg.setOrderId(orderId);
        msg.setTenantId(tenantId);
        msg.setStoreId(storeId);
        deliveryOrderTansMessageProducer.sendMessage(msg);
    }

    @Override
    public void notifyDeliveryTransMsg(Long tenantId, Long storeId, Long orderId, Long riderAccountId, String riderName, String riderPhone) {
        DeliveryTransSelfMsg msg = new DeliveryTransSelfMsg();
        msg.setOrderId(orderId);
        msg.setTenantId(tenantId);
        msg.setStoreId(storeId);
        msg.setRiderAccountId(riderAccountId);
        msg.setRiderName(riderName);
        msg.setRiderPhone(riderPhone);
        deliveryOrderTansMessageProducer.sendMessage(msg);
    }

    @Override
    public void notifyDeliveryTrace(OrderTrackEvent event) {
        orderTrackLogProducer.sendMessage(event);
    }

    @Override
    public void notifyDeliveryTransMsg(Long tenantId, Long storeId, Long orderId, Integer deliveryPlatformCode) {
        DeliveryTransSelfMsg msg = new DeliveryTransSelfMsg();
        msg.setOrderId(orderId);
        msg.setTenantId(tenantId);
        msg.setStoreId(storeId);
        msg.setDeliveryPlatformCode(deliveryPlatformCode);
        deliveryOrderTansMessageProducer.sendMessage(msg);
    }

    @Override
    public void notifyDeliveryTransMsg(Long tenantId, Long storeId, Long orderId, Integer deliveryPlatformCode, Long riderAccountId, String riderName, String riderPhone) {
        DeliveryTransSelfMsg msg = new DeliveryTransSelfMsg();
        msg.setOrderId(orderId);
        msg.setTenantId(tenantId);
        msg.setStoreId(storeId);
        msg.setRiderAccountId(riderAccountId);
        msg.setRiderName(riderName);
        msg.setRiderPhone(riderPhone);
        msg.setDeliveryPlatformCode(deliveryPlatformCode);
        deliveryOrderTansMessageProducer.sendMessage(msg);
    }
}
