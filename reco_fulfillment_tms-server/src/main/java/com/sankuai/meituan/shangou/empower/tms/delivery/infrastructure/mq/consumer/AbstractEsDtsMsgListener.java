package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.exception.EsClientException;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.exception.EsVersionConflictException;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DtsMsg;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * 通用监听DTS消息用以异构ES的消费者
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractEsDtsMsgListener<T> extends AbstractDtsMsgListener<T> {

    @Override
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected ConsumeStatus consume(MafkaMessage message) {
        DtsMsg<T> dtsMsg = deserialize(message.getBody());
        T msgBody = dtsMsg.getData();

        try {
            checkMessageParameter(msgBody);
            boolean isContinueConsume = preCheckBusiness(msgBody);
            if (!isContinueConsume) {
                return CONSUME_SUCCESS;
            }

            switch (dtsMsg.getType()) {
                case "insert":
                    onInsert(msgBody);
                    break;
                case "update":
                    Map<String, Object> diffMap = objectMapper.readValue(dtsMsg.getDiffMapJson(), Map.class);
                    onUpdate(msgBody, diffMap);
                    break;
                case "delete":
                    super.onDelete(msgBody);
                    break;
                default:
                    log.error("Unknown message type: {}", dtsMsg);
            }
            return CONSUME_SUCCESS;
        } catch (EsClientException | EsVersionConflictException e) {
            log.error("Consuming DTS message error and need retry, message: {}, error: {}", message, e.getMessage(), e);
            return CONSUME_FAILURE;
        } catch (Exception e) {
            log.error("Consuming DTS message error and need no retry, message: {}, error: {}", message, e.getMessage(), e);
            return CONSUME_SUCCESS;
        }
    }

    /**
     * @description: 校验mq参数，如果不通过，不会重试
     * @param: msgBody 消息体
    */
    protected void checkMessageParameter(T msgBody) {}

    /**
     * @description: 业务前置检查，如果返回false，则不继续消费，也不会重试
     * @param: msgBody 消息体
     * @return boolean
    */
    protected boolean preCheckBusiness(T msgBody) {return true;}
}
