package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.warn;

import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aop.LoadTestAop;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.PositionPushConfigEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryTimeoutNotifyMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.DeliveryWarnClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfigRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PushInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.push.RiderPushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.PositionPushHelper;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import com.sankuai.xm.pubapi.thrift.PusherInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;

/**
 * 配送告警客户端实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/6
 */
@Slf4j
@Component
public class DeliveryWarnClientImpl implements DeliveryWarnClient {

	private static final int SUCCESS = 0;

	private static final String PUSH_MSG_TO_MT_RECIPIENTS = "push_msg_to_mtRecipients";
	private static final String PUSH_MSG_TO_DH_RECIPIENTS = "push_msg_to_dhRecipients";
	private static final String PUSH_MSG_TO_OTHER_RECIPIENTS = "push_msg_to_other_Recipients";

	@Resource
	private PushMessageServiceI.Iface pushMessageThriftServiceClient;
	@Resource
	private PoiThriftService poiThriftServiceClient;
	@Resource
	private MafkaMessageProducer<DeliveryTimeoutNotifyMessage> deliveryTimeOutNotifyProducer;
	@Resource
	private DeliveryRemindConfigRepository deliveryRemindConfigRepository;
	@Resource
	private String xmKey;
	@Resource
	private String xmToken;
	@Resource
	private Long xmPubId;

	// “歪马履约监控” 公众号相关信息，参见https://km.sankuai.com/page/1301871363
	@Resource
	private String dhOaKey;
	@Resource
	private String dhOaToken;
	@Resource
	private Long dhOaPubId;

	// 配送超时提醒依赖的企业id
	@Resource
	private Long dhCid;
	@Resource
	private Long meituanCid;
	@Resource
	private RiderPushClient riderPushClient;

	@Override
	@CatTransaction
	@LoadTestAop
	public void warn(String warnMessage) {
		List<String> misIds = getMisIds();
		if (CollectionUtils.isEmpty(misIds)) {
			log.warn("No configured receivers, will ignore warn message.");
			return;
		}

		try {
			PusherInfo pusherInfo = new PusherInfo().setAppkey(xmKey).setToken(xmToken).setFromUid(xmPubId);
			log.info("PushMessageService.pushTextMessage start, misIds:{}, msg:{}", misIds, warnMessage);
			String resp = pushMessageThriftServiceClient.pushTextMessage(System.currentTimeMillis(), warnMessage, misIds, pusherInfo);
			log.info("PushMessageService.pushTextMessage finish, resp:{}", resp);
		} catch (Exception e) {
			log.error("PushMessageService.pushTextMessage error, msg:{}, misIds:{}", warnMessage, misIds, e);
		}
	}

	private List<String> getMisIds() {
		try {
			String misIdsStr = ConfigUtilAdapter.getString("delivery_warn_misIds", "[]");
			return JsonUtil.fromJson(misIdsStr, new TypeReference<List<String>>() {
			});
		} catch (Exception e) {
			log.error("MCC[delivery_warn_misIds] parse error", e);
			return new ArrayList<>();
		}
	}

	/**
	 * 配送超时提醒发起大象IM推送
	 */
	@Deprecated
	@CatTransaction
	@LoadTestAop
	public Optional<Failure> pushDeliveryDoneTimeOutMessage(String messageStr, DeliveryRemindConfig deliveryRemindConfig) {
		try {
			PusherInfo pusherInfo = new PusherInfo().setAppkey(dhOaKey).setToken(dhOaToken).setFromUid(dhOaPubId);
			log.info("PushMessageService.pushDeliveryDoneTimeOutMessage start, deliveryRemindConfig:{}, msg:{}",
					deliveryRemindConfig, messageStr);

			Map<String, String> bodyJsonMap = new HashMap<String, String>(1) {{put("text",messageStr);}};
			String bodyJson = JsonUtil.toJson(bodyJsonMap);

			if (CollectionUtils.isNotEmpty(deliveryRemindConfig.getRecipients())) {
				String resp = pushMessageThriftServiceClient.pushMessageCrossEnterpriseWithCid(
						System.currentTimeMillis(), "text", bodyJson,
						deliveryRemindConfig.getRecipients(), pusherInfo, meituanCid);
				reportPushSuccessCount(resp, meituanCid);
				log.info("PushMessageService.pushMessageCrossEnterpriseWithCid finish, cid:{}, resp:{}", meituanCid, resp);
			}

			if (CollectionUtils.isNotEmpty(deliveryRemindConfig.getDhRecipients())) {
				// receivers的用户部分不存在也是可以发送成功的
				String resp = pushMessageThriftServiceClient.pushMessageCrossEnterpriseWithCid(
						System.currentTimeMillis(), "text", bodyJson,
						deliveryRemindConfig.getDhRecipients(), pusherInfo, dhCid);
				reportPushSuccessCount(resp, dhCid);
				log.info("PushMessageService.pushMessageCrossEnterpriseWithCid finish, cid:{}, resp:{}", dhCid, resp);
			}

			return Optional.empty();
		} catch (Exception e) {
			log.error("PushMessageService.pushTextMessage error", e);
			return Optional.of(new Failure(true, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "发送大象推送失败"));
		}
	}

	@CatTransaction
	@LoadTestAop
	public Optional<Failure> pushDeliveryDoneTimeOutMessageByPosition(long tenantId, long storeId, String messageStr, PoiInfoDto poiInfoDto) {
		if (Objects.isNull(poiInfoDto)) {
			return Optional.of(new Failure(FailureCodeEnum.QUERY_POI_FAILED));
		}
		try {
			PusherInfo pusherInfo = new PusherInfo().setAppkey(dhOaKey).setToken(dhOaToken).setFromUid(dhOaPubId);
			log.info("PushMessageService.pushDeliveryDoneTimeOutMessage start,  msg:{}", messageStr);

			Map<String, String> bodyJsonMap = new HashMap<String, String>(1) {{put("text",messageStr);}};
			String bodyJson = JsonUtil.toJson(bodyJsonMap);

			PushInfo pushInfo = riderPushClient.queryPushInfoByPositions(tenantId, storeId, PositionPushHelper.getPositionIds(poiInfoDto, PositionPushConfigEnum.DELIVERY_DONE_TIMEOUT));
			log.info("queryPushInfoByPositions result = {}", pushInfo);
			if (CollectionUtils.isNotEmpty(pushInfo.getMeituanMisIdList())) {
				String resp = pushMessageThriftServiceClient.pushMessageCrossEnterpriseWithCid(
						System.currentTimeMillis(), "text", bodyJson,
						pushInfo.getMeituanMisIdList(), pusherInfo, meituanCid);
				reportPushSuccessCount(resp, meituanCid);
				log.info("PushMessageService.pushMessageCrossEnterpriseWithCid finish, cid:{}, resp:{}", meituanCid, resp);
			}

			if (CollectionUtils.isNotEmpty(pushInfo.getExternalMisIdList())) {
				// receivers的用户部分不存在也是可以发送成功的
				String resp = pushMessageThriftServiceClient.pushMessageCrossEnterpriseWithCid(
						System.currentTimeMillis(), "text", bodyJson,
						pushInfo.getExternalMisIdList(), pusherInfo, dhCid);
				reportPushSuccessCount(resp, dhCid);
				log.info("PushMessageService.pushMessageCrossEnterpriseWithCid finish, cid:{}, resp:{}", dhCid, resp);
			}

			return Optional.empty();
		} catch (Exception e) {
			log.error("PushMessageService.pushTextMessage error", e);
			return Optional.of(new Failure(true, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "发送大象推送失败"));
		}
	}

	@Override
	@CatTransaction
	@LoadTestAop
	public Optional<Failure> pushDeliveryDoneTimeOut(OrderInfo orderInfo, @Nullable DeliveryOrder deliveryOrder) {

		//查询门店信息
		PoiMapResponse poiMapResponse = poiThriftServiceClient.queryTenantPoiInfoMapByPoiIds(
				Lists.newArrayList(orderInfo.getOrderKey().getStoreId()), orderInfo.getOrderKey().getTenantId()
		);
		if (SUCCESS != poiMapResponse.getStatus().getCode() || !poiMapResponse.getPoiInfoMap().containsKey(orderInfo.getOrderKey().getStoreId())) {
			log.warn("Query poi[{}] info failed. ", orderInfo.getOrderKey().getStoreId());
			return Optional.of(new Failure(true, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "门店查询失败"));
		}

		DeliveryTimeOutWarnMessage message = new DeliveryTimeOutWarnMessage(
				deliveryOrder, poiMapResponse.getOne(orderInfo.getOrderKey().getStoreId()), orderInfo
		);

		//按岗位推送
		if (MccConfigUtils.checkIsDHTenant(orderInfo.getOrderKey().getTenantId()) && PositionPushHelper.getDHByPositionSwitch(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId())) {
			return pushDeliveryDoneTimeOutMessageByPosition(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(), message.toString(), poiMapResponse.getOne(orderInfo.getOrderKey().getStoreId()));
		} else {//按固定id推送，以后会废弃
			// 判断是否该门店是否需要推送
			Optional<DeliveryRemindConfig> configOptional = deliveryRemindConfigRepository.queryDeliveryRemindConfig(
					orderInfo.getOrderKey().getTenantId(),
					orderInfo.getOrderKey().getStoreId());
			if (!configOptional.isPresent()) {
				log.info("configOptional is not exists, tenantId:{}, storeId:{}",
						orderInfo.getOrderKey().getTenantId(),
						orderInfo.getOrderKey().getStoreId());
				return Optional.empty();
			}
			DeliveryRemindConfig deliveryRemindConfig = configOptional.get();
			log.info("deliveryRemindConfig:{} ", deliveryRemindConfig);

			if (CollectionUtils.isEmpty(deliveryRemindConfig.getRecipients()) &&
					CollectionUtils.isEmpty(deliveryRemindConfig.getDhRecipients())) {
				log.info("No configured misIds for store[{}], will give up sending timeout warn push.", orderInfo.getOrderKey().getStoreId());
				return Optional.empty();
			}
			// 发起大象IM推送
			return pushDeliveryDoneTimeOutMessage(message.toString(), deliveryRemindConfig);
		}
	}

	@Override
	@CatTransaction
	@LoadTestAop
	public Optional<Failure> notifyOrderEstimatedDeliveryTimeOut(OrderInfo orderInfo) {
		if (!DeliveryRiderMccConfigUtils.getIssueCouponsSwitch()) {
			log.warn("超时发券开关已关闭,将不会发券");
			return Optional.empty();
		}

		if (!DeliveryRiderMccConfigUtils.checkIsDHTenant(orderInfo.getOrderKey().getTenantId())) {
			log.warn("不是歪马租户, 不触发超时发券流程");
			return Optional.empty();
		}

		long deliveryTimeoutStamp;
		if (orderInfo.isBookingOrder()) {
			deliveryTimeoutStamp = TimeUtil.toMilliSeconds(orderInfo.getEstimatedDeliveryEndTime()
					.plusMinutes(MccConfigUtils.getBookingOrderDeliveryTimeOutDuration2C()));
		} else {
			deliveryTimeoutStamp = TimeUtil.toMilliSeconds(orderInfo.getEstimatedDeliveryEndTime()
					.plusMinutes(MccConfigUtils.getInTimeOrderDeliveryTimeOutDuration2C()));
		}

		OrderKey orderKey = orderInfo.getOrderKey();
		DeliveryTimeoutNotifyMessage notifyMessage = new DeliveryTimeoutNotifyMessage(
				orderKey.getTenantId(), orderKey.getStoreId(), orderKey.getOrderId(), orderInfo.getChannelOrderId(), orderInfo.getOrderBizType(), deliveryTimeoutStamp
		);
		try {
			deliveryTimeOutNotifyProducer.sendMessage(notifyMessage);
		} catch (Exception e) {
			log.error("deliveryTimeOutNotifyProducer.sendMessage error msg = {}", notifyMessage, e);
			return Optional.of(new Failure(true, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "通知外部服务配送超时失败"));
		}

		return Optional.empty();
	}

	@Override
	@LoadTestAop
	public void pushTurnToDapFail(OrderInfo orderInfo) {
		try {
			PusherInfo pusherInfo = new PusherInfo().setAppkey(dhOaKey).setToken(dhOaToken).setFromUid(dhOaPubId);
			log.info("PushMessageService.pushTurnToDapFail start, orderId = {}", orderInfo.getOrderKey().getOrderId());

			Map<String, String> bodyJsonMap = new HashMap<String, String>(1) {{put("text", new DeliveryTurnDapFailMessage(orderInfo).toString());}};

			String bodyJson = JsonUtil.toJson(bodyJsonMap);
			String resp = pushMessageThriftServiceClient.pushMessageCrossEnterpriseWithCid(
					System.currentTimeMillis(), "text", bodyJson,
					MccConfigUtils.getTurnToDapPushMis(), pusherInfo, meituanCid);
			reportPushSuccessCount(resp, meituanCid);
			log.info("PushMessageService.pushMessageCrossEnterpriseWithCid finish, cid:{}, resp:{}", meituanCid, resp);

		} catch (Exception e) {
			log.error("pushTurnToDapFail throws exp", e);
		}

	}

		/**
		 * 埋点统计推送超时消息的次数
		 * @param resp
		 */
		private void reportPushSuccessCount(String resp,Long cid) {
			String name;
			if (cid.equals(meituanCid)) {
				name = PUSH_MSG_TO_MT_RECIPIENTS;
			} else if (cid.equals(dhCid)) {
				name = PUSH_MSG_TO_DH_RECIPIENTS;
			} else {
				name = PUSH_MSG_TO_OTHER_RECIPIENTS;
			}

			try {
				PushMessageResponse response = JsonUtil.fromJson(resp, new TypeReference<PushMessageResponse>() {});
				if (response != null && response.getRescode() != null && response.getRescode() == ResultCodeEnum.SUCCESS.getValue()
						&& response.getData() != null && response.getData().getSuccessUids() != null) {
					Cat.logMetricForCount(name, response.getData().getSuccessUids().size());
				}
			} catch (Exception e) {
				log.warn("解析PushMessageResponse失败,response: {}", resp, e);
			}
		}

}
