package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiderAssignTimeOutCheckMessageHolder {

	private String body;

	private Long bornTimestamp;

	private int delayInMs;

	public RiderAssignTimeOutCheckMessageHolder(RiderAssignTimeOutCheckMessage message) {
		this.body = JsonUtil.toJson(message);
		this.bornTimestamp = System.currentTimeMillis();
		this.delayInMs = ConfigUtilAdapter.getInt("delivery_exception_timeout_seconds", 15 * 60) * 1000;
	}
}
