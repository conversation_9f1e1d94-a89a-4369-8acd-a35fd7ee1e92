package com.sankuai.meituan.shangou.empower.tms.delivery.model;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class RiderInfoChangeBo {

    /**
     * 租户ID
    */
    private Long tenantId;

    /**
     * 牵牛花中台门店ID
     */
    private Long storeId;

    /**
     * 渠道订单ID
     */
    private String channelOrderId;

    /**
     * 渠道bizType
     */
    private Integer orderBizType;

    /**
     * 牵牛花承运商ID
     */
    private Integer deliveryChannelId;

    /**
     * deliveryOrder主键ID
     */
    private Long deliveryOrderId;

    /**
     * 骑手姓名
     */
    private String riderName;

    /**
     * 骑手电话
     */
    private String riderPhone;

}
