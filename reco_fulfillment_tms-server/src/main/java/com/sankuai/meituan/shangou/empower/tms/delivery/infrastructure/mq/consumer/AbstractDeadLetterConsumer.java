package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;


import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.MetricHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.*;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.filter.ThriftFilterAndInterceptorUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.filter.ThriftInvokerCatEventFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.meituan.mafka.client.consumer.ConsumeStatus.*;

/**
 * <AUTHOR>
 * @date 2020/9/4
 */
@Slf4j
@SuppressWarnings({"rawtypes", "unchecked"})
public abstract class AbstractDeadLetterConsumer implements InitializingBean, DisposableBean {
	public static final String DEFAULT_MQ_BG_NAME = "waimai";

	public static final String CREDIT_AUDIT_BG_NAME = "common";

	private static final String TENANT_ID = "tenantId";

	private static final String RETRY_TIMES_KEY = "_retry_times";
	private static final String DEFAULT_CLIENT_HEARTBEAT_GAP_SECOND = "1";
	private static final int DEFAULT_RETRY_DELAY_SECONDS_MIN_VALUE = 5;
	private static final int DEFAULT_RETRY_DELAY_SECONDS_MAX_VALUE = 30;

	private MQConsumerEnum consumerEnum;
	private IConsumerProcessor consumerProcessor;

	protected abstract MQConsumerEnum consumerConfig();

	protected abstract ConsumeStatus consume(MafkaMessage message);

	@Override
	public void afterPropertiesSet() throws Exception {
		init();
		startConsume();
	}

	@Override
	public void destroy() throws Exception {
		if (this.consumerProcessor != null) {
			this.consumerProcessor.close();
		}
	}

	private void init() throws Exception {
		this.consumerEnum = consumerConfig();

		Properties properties = new Properties();
		properties.setProperty(ConsumerConstants.MafkaClientAppkey, consumerEnum.getAppkey());
		properties.setProperty(ConsumerConstants.CLIENT_HEARTBEAT_GAP_SECOND, DEFAULT_CLIENT_HEARTBEAT_GAP_SECOND);
		properties.setProperty(ConsumerConstants.SubscribeGroup, consumerEnum.getConsumerGroup());
		properties.setProperty(ConsumerConstants.MafkaBGNamespace, consumerEnum.getNameSpace());
		this.consumerProcessor = MafkaClient.buildCommonConsumerFactory(properties, consumerEnum.getTopic(),true);
	}

	private void startConsume() {
		consumerProcessor.recvMessageWithParallel(String.class, new AbstractMessageListener() {

			@Override
			public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext messagetContext) {
				if (Objects.isNull(message) || Objects.isNull(message.getBody())) {
					log.warn("消费到消息为空，不处理! {}", message);
					return CONSUME_SUCCESS;
				}
				boolean consumeException = false;
				ConsumeStatus consumeStatus = CONSUME_SUCCESS;
				try {
					consumeStatus = consume(message);
				} catch (Exception e) {
					log.error("encounter exception while process message <{}>", message, e);
					MetricHelper.build().name(String.format("mq.%s.err", consumerEnum.getTopic())).count();
					consumeException = true;
				}

				if (consumeException || CONSUME_FAILURE == consumeStatus) {
					if (needRetry(message)) {
						try {
							if (ProducerStatus.SEND_OK.equals(retry(message, calcDelayMilliseconds()))) {
								return CONSUME_SUCCESS;
							} else {
								return CONSUME_FAILURE;
							}
						} catch (Exception ex) {
							log.error("加入死信队列异常, 原消费组将进行重试", ex);
							if (HostEnv.TEST.equals(ProcessInfoUtil.getHostEnv())) {
								log.warn("test env discard dead letter retry failed msg {}", message);
								return CONSUME_SUCCESS;
							}
							return CONSUME_FAILURE;
						}
					} else {
						//消费失败，且不再重试则需要上报cat
						reportToCat(message, "消费失败，且重试过期");
						log.error("消息消费失败，且重试过期, 将放弃重试:{}", message);
						MetricHelper.build().name(String.format("mq.%s.retry.timeout", consumerEnum.getTopic())).count(1);
						return CONSUME_SUCCESS;
					}
				}

				return consumeStatus;
			}
		});
	}

	protected <T> T translateMessage(MafkaMessage mafkaMessage, Class<T> clazz) {
		return Optional.ofNullable(mafkaMessage)
				.map(MafkaMessage::getBody)
				.map(Object::toString)
				.map(it -> JsonUtil.fromJson(it, clazz))
				.orElse(null);
	}

	protected <T> T translateMessage(MafkaMessage mafkaMessage, TypeReference<T> typeRef) {
		return Optional.ofNullable(mafkaMessage)
				.map(MafkaMessage::getBody)
				.map(Object::toString)
				.map(it -> JsonUtil.fromJson(it, typeRef))
				.orElse(null);
	}

	protected <T> T translateMessageWithBodyHolder(MafkaMessage mafkaMessage, Class<T> clazz) {
		return Optional.ofNullable(mafkaMessage)
				.map(MafkaMessage::getBody)
				.map(Object::toString)
				.map(JsonUtil::toJsonNode)
				.map(it -> it.get("body"))
				.map(JsonNode::asText)
				.map(it -> JsonUtil.fromJson(it, clazz))
				.orElse(null);
	}

	protected boolean isLastRetry(MafkaMessage message) {
		try {
			JsonNode jsonNode = JsonUtil.toJsonNode(message.getBody().toString());
			if (jsonNode == null) {
				return true;
			}

			return getRetryTimes(jsonNode) >= consumerEnum.getMaxRetryTimes();
		} catch (Exception e) {
			log.error("Get retry times from message failed", e);
			return true;
		}
	}

	private boolean needRetry(MafkaMessage message) {
		try {
			JsonNode jsonNode = JsonUtil.toJsonNode(message.getBody().toString());
			if (jsonNode == null) {
				log.error("Parse message failed, will give up retry, message={}", message);
				return false;
			}

			int currentRetryTimes = getRetryTimes(jsonNode);
			if (currentRetryTimes < consumerEnum.getMaxRetryTimes()) {
				putRetryTimes(jsonNode, currentRetryTimes + 1);
				message.setBody(JsonUtil.toJson(jsonNode));
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			log.error("Get or put retry times from message json failed, will give up retry, message={}", message, e);
			return false;
		}
	}

	private String extractParam(MafkaMessage message, String param) {
		try {
			JsonNode jsonNode = JsonUtil.toJsonNode(message.getBody().toString());
			return Optional.ofNullable(jsonNode)
					.map(it -> it.get(param))
					.map(JsonNode::toString)
					.orElse("-1");
		} catch (Exception e) {
			log.error("Get param from message json failed, message={}", message, e);
			return "-1";
		}
	}

	private int getRetryTimes(JsonNode jsonNode) {
		return Optional.ofNullable(jsonNode)
				.map(it -> it.get(RETRY_TIMES_KEY))
				.map(JsonNode::asInt)
				.orElse(0);
	}

	private void putRetryTimes(JsonNode objectNode, int retryTimes) {
		((ObjectNode) objectNode).put(RETRY_TIMES_KEY, retryTimes);
	}


	private long calcDelayMilliseconds() {
		int delaySeconds = RandomUtils.nextInt(DEFAULT_RETRY_DELAY_SECONDS_MIN_VALUE, DEFAULT_RETRY_DELAY_SECONDS_MAX_VALUE);
		return TimeUnit.SECONDS.toMillis(delaySeconds);
	}

	/**
	 * 分租户上报消费异常
	 * @param message
	 * @param status
	 */
	private void reportToCat(MafkaMessage message, String status) {
		StringBuilder transactionKeyBuilder = new StringBuilder(extractParam(message, TENANT_ID))
				.append("_MESSAGE_CONSUMER_FAIL_");
		Transaction transaction = Cat.newTransaction(transactionKeyBuilder.toString(),
				transactionKeyBuilder.append(consumerConfig().getTopic()).toString());
		transaction.setStatus(status);
		transaction.complete();
	}
}
