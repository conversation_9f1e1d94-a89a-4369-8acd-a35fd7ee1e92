package com.sankuai.meituan.shangou.empower.tms.delivery.facade;

import com.alibaba.fastjson.JSONObject;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.Status;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aop.LoadTestAop;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryBaseException;
import com.sankuai.meituan.shangou.empower.tms.delivery.model.config.TenantBizModeConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.model.deliveryconfig.LaunchDeliveryConfigModel;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum.MERCHANT_ACCEPT;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE;

/**
 * <AUTHOR>
 * @date 2020/3/2
 * desc: 租户远程服务
 */
@Service("tenantRemoteServiceForDelivery")
@Slf4j
public class TenantRemoteService {
    private static final int TENANT_SHOP_CONFIG_LAUNCH_DELIVERY_CONFIG = 16;
    private static final int TENANT_SHOP_CONFIG_BIZ_MODE_CONFIG_ID = 32;

    @Resource
    private ConfigThriftService configThriftServiceClient;


    @LoadTestAop
    public DeliveryLaunchPoint queryDeliveryLaunchPointConfig(Long tenantId, Long storeId) {
        LaunchDeliveryConfigModel configModel = queryLaunchDeliveryConfig(tenantId, storeId);
        if (configModel.getLaunchOnPickupComplete() == 1) {
            return new DeliveryLaunchPoint(
                    new ImmediateOrderDeliveryLaunchPointConfig(PICK_DONE, configModel.getLaunchOnPickupCompleteDelayTime()),
                    new BookingOrderDeliveryLaunchPointConfig()
            );
        } else {
            return new DeliveryLaunchPoint(
                    new ImmediateOrderDeliveryLaunchPointConfig(MERCHANT_ACCEPT, configModel.getLaunchOnMerchantConfirmDelayTime()),
                    new BookingOrderDeliveryLaunchPointConfig()
            );
        }
    }

    public LaunchDeliveryConfigModel queryLaunchDeliveryConfig(Long tenantId, Long shopId) {
        //默认发起配送配置
        LaunchDeliveryConfigModel launchDeliveryConfigModel = new LaunchDeliveryConfigModel();
        ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
        configQueryRequest.setTenantId(tenantId);
        configQueryRequest.setSubjectId(shopId);
        configQueryRequest.setConfigId(TENANT_SHOP_CONFIG_LAUNCH_DELIVERY_CONFIG);
        try {
            // log.info("TenantRemoteService.queryLaunchDeliveryConfig  调用configThriftService.queryTenantConfig() request:{}", configQueryRequest);
            TenantConfigResponse tenantConfigResponse = configThriftServiceClient.queryTenantConfig(configQueryRequest);
            // log.info("TenantRemoteService.queryLaunchDeliveryConfig  调用configThriftService.queryTenantConfig() response:{}", tenantConfigResponse);
            if (Objects.nonNull(tenantConfigResponse) && 0 == tenantConfigResponse.getStatus().getCode() && Objects.nonNull(tenantConfigResponse.getConfig())) {
                String configContent = tenantConfigResponse.getConfig().getConfigContent();
                launchDeliveryConfigModel = JsonUtil.fromJson(configContent, LaunchDeliveryConfigModel.class);
            }
        } catch (Exception e) {
            log.warn("TenantRemoteService.queryLaunchDeliveryConfig  获取门店发起配送配置错误", e);
        }

        return launchDeliveryConfigModel;
    }

    /**
     * 查询租户业务模式
     * waima 表示歪马业务
     *
     * @param tenantId
     * @return
     */
    @LoadTestAop
    public TenantBizModeConfig queryTenantBizModeConfig(Long tenantId) {
        if (tenantId == null) {
            log.error("queryTenantBizModeConfig: tenantId is null");
            return null;
        }
        return queryTenantConfig(tenantId, tenantId, TENANT_SHOP_CONFIG_BIZ_MODE_CONFIG_ID, TenantBizModeConfig.class);
    }


    public <T> T queryTenantConfig(Long tenantId, Long shopId, Integer configId, Class<T> clazz) {
        ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
        configQueryRequest.setTenantId(tenantId);
        configQueryRequest.setSubjectId(shopId);
        configQueryRequest.setConfigId(configId);
        try {
            TenantConfigResponse tenantConfigResponse = configThriftServiceClient.queryTenantConfig(configQueryRequest);
            // log.info("TenantRemoteService.queryTenantConfig ,tenantId:{},shopId:{},configId:{},tenantConfigResponse:{}", tenantId, shopId, configId, tenantConfigResponse);
            if (Objects.nonNull(tenantConfigResponse) && 0 == tenantConfigResponse.getStatus().getCode() && Objects.nonNull(tenantConfigResponse.getConfig())) {
                String configContent = tenantConfigResponse.getConfig().getConfigContent();
                return JsonUtil.fromJson(configContent, clazz);
            }
        } catch (Exception e) {
            log.error("TenantRemoteService.queryTenantConfig  获取租户门店配置错误,tenantId:{},shopId:{},configId:{}", tenantId, shopId, configId, e);
            throw new DeliveryBaseException("获取租户门店配置失败");
        }
        return null;
    }


    /**
     * 是否医药无人仓
     * @param tenantId
     * @return
     */
    @LoadTestAop
    public Boolean isMedicineUnmannedWarehouse(Long tenantId) {
        try {
            TenantBizModeConfig tenantBizModeConfig = queryTenantBizModeConfig(tenantId);
            if(Objects.isNull(tenantBizModeConfig)){
                return false;
            }
            if(!TenantBusinessModeEnum.MEDICINE_UNMANNED_WAREHOUSE.getKey().equals(tenantBizModeConfig.getBizMode())){
                return false;
            }
            return true;
        }catch (Exception e){
            log.error("isMedicineUnmannedWarehouse error tenantId:{}",tenantId,e);
        }
        return false;
    }


}
