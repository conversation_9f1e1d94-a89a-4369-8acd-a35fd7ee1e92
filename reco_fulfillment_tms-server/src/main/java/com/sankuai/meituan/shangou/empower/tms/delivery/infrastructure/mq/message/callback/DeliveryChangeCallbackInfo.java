package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.DeliveryChangeCallbackCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryCancelReasonEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.StaffRider;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/13
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DeliveryChangeCallbackInfo extends DeliveryUnifiedCallbackInfo {

	private Long orderId;

	private Integer deliveryChannel;
	private String channelDeliveryId;
	private String servicePackage;

	private Integer deliveryEvent;

	private Integer exceptionType;
	private String exceptionDescription;
	private Integer exceptionCode;

	private String riderName;
	private String riderPhone;
	private String riderPhoneToken;
	private Long riderAccountId;

	private Long distance;
	private Double deliveryFee;

	private Long changeTimeInMillis;

	//配送小费 元
	private Double tipAmount;

	private Integer platformSource;

	private String baseFee;
	private String discountFee;
	private String insuredFee;

	private Integer deliveryPlatformCode;

	/**
	 * 配送取消原因描述
	 * @see com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryCancelReasonEnum
	 */
	private Integer deliveryCancelCode;
	/**
	 * 承运商原始运单id
	 */
	private String originWaybillNo;

	/**
	 * @param rider       骑手信息，可以为空，为空则将清空运单骑手信息
	 * @param distance    距离信息，可以为空，为空则不改动运单距离信息
	 * @param deliveryFee 配送费用，可以为空，为空则不改动运单费用信息
	 */
	public DeliveryChangeCallbackInfo(Long orderId,
									  Integer deliveryChannel, String channelDeliveryId, String servicePackage,
									  DeliveryEventEnum deliveryEvent,
									  DeliveryExceptionInfo exceptionInfo,
									  LocalDateTime changeTime,
									  Rider rider, Long distance, Double deliveryFee){
		this(orderId,
				deliveryChannel, channelDeliveryId, servicePackage,
				deliveryEvent,
				exceptionInfo,
				changeTime,
				rider, distance, deliveryFee,null,0,null,null,null, 0, null, null);
	}
	public DeliveryChangeCallbackInfo(Long orderId,
	                                  Integer deliveryChannel, String channelDeliveryId, String servicePackage,
	                                  DeliveryEventEnum deliveryEvent,
	                                  DeliveryExceptionInfo exceptionInfo,
	                                  LocalDateTime changeTime,
	                                  Rider rider, Long distance, Double deliveryFee,Double tipAmount,Integer platformSource,String baseFee,String discountFee,
									  String insuredFee, Integer deliveryPlatformCode, DeliveryCancelReasonEnum deliveryCancelReasonEnum, String originWaybillNo) {
		Preconditions.checkNotNull(orderId);
		Preconditions.checkNotNull(deliveryChannel);
		Preconditions.checkNotNull(deliveryEvent);
		Preconditions.checkNotNull(exceptionInfo);
		Preconditions.checkNotNull(changeTime);

		this.orderId = orderId;
		this.deliveryChannel = deliveryChannel;
		this.channelDeliveryId = channelDeliveryId;
		this.servicePackage = servicePackage;
		this.deliveryEvent = deliveryEvent.getCode();
		this.exceptionType = exceptionInfo.getExceptionType().getCode();
		this.exceptionDescription = exceptionInfo.getExceptionDescription();
		this.exceptionCode = exceptionInfo.getExceptionCode();
		this.changeTimeInMillis = TimeUtil.toMilliSeconds(changeTime);
		Optional.ofNullable(rider).ifPresent(it -> {
			this.riderName = it.getRiderName();
			this.riderPhone = it.getRiderPhone();
			this.riderPhoneToken = it.getRiderPhoneToken();
			if (it instanceof StaffRider) {
				this.riderAccountId = ((StaffRider) it).getRiderAccountId();
			}
		});
		this.distance = distance;
		this.deliveryFee = deliveryFee;
		this.tipAmount = tipAmount;
		this.platformSource = platformSource;
		this.baseFee = baseFee;
		this.discountFee = discountFee;
		this.insuredFee = insuredFee;
		this.deliveryPlatformCode = deliveryPlatformCode;
		Optional.ofNullable(deliveryCancelReasonEnum).ifPresent(it -> this.deliveryCancelCode = it.getCancelCode());
		this.originWaybillNo = originWaybillNo;
	}

	public DeliveryChangeCallbackCmd toCmd() {
		return new DeliveryChangeCallbackCmd(
				orderId,
				deliveryChannel, channelDeliveryId, servicePackage,
				DeliveryEventEnum.valueOf(deliveryEvent),
				new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.valueOf(exceptionType), exceptionDescription, exceptionCode),
				TimeUtil.fromMilliSeconds(changeTimeInMillis),
				getRider(),
				distance,
				Optional.ofNullable(deliveryFee).map(BigDecimal::valueOf).orElse(null),
				Optional.ofNullable(tipAmount).map(BigDecimal::valueOf).orElse(null),
				platformSource,
				baseFee,
				discountFee,
				insuredFee,
				deliveryPlatformCode,
				deliveryCancelCode,
				originWaybillNo
		);
	}

	private Rider getRider() {
		if (riderAccountId != null) {
			return new StaffRider(riderName, riderPhone, riderPhoneToken, riderAccountId);
		}

		if (StringUtils.isNotEmpty(riderName) && StringUtils.isNotEmpty(riderPhone)) {
			return new Rider(riderName, riderPhone, riderPhoneToken);
		}

		return null;
	}
}
