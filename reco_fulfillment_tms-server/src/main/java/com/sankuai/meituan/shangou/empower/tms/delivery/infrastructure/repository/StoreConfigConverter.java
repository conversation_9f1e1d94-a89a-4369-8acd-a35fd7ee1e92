package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.StoreConfigDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.StoreConfig;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/10/11 20:49
 */
@Mapper(componentModel = "spring")
public abstract class StoreConfigConverter {
    public abstract StoreConfig convert2StoreConfigOpLog(StoreConfigDO storeConfigDO);

    @InheritInverseConfiguration
    public abstract StoreConfigDO convert2StoreConfigOpLogDo(StoreConfig storeConfig);
}
