package com.sankuai.meituan.shangou.empower.tms.delivery.enums;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/4/14
 * @email jianglilin02@meituan
 */
public enum FarmDeliveryStatusEnum {

    WAIT_TO_RECEIVE(10, "待接单"),
    WAIT_TO_TAKE(20, "待取货"),
    RIDER_ARRIVED_SHOP(30, "已到店"),
    MERCHANT_DELIVERING(40, "配送中"),
    DELIVERY_DONE(50, "已完成"),
    DELIVERY_CANCELLED(60, "已取消"),
    DELIVERY_FAILED(70, "配送失败"),
    ;
    private int code;

    private String msg;

    FarmDeliveryStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static Optional<DeliveryStatusEnum> mapToDeliveryStatus(int value) {
        FarmDeliveryStatusEnum farmDeliveryStatusEnum = enumOf(value);
        if (Objects.isNull(farmDeliveryStatusEnum)) {
            return Optional.empty();
        }
        switch (farmDeliveryStatusEnum) {
            case WAIT_TO_RECEIVE:
                return Optional.of(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER);
            case WAIT_TO_TAKE:
                return Optional.of(DeliveryStatusEnum.RIDER_ASSIGNED);
            case RIDER_ARRIVED_SHOP:
                return Optional.of(DeliveryStatusEnum.RIDER_ARRIVED_SHOP);
            case MERCHANT_DELIVERING:
                return Optional.of(DeliveryStatusEnum.RIDER_TAKEN_GOODS);
            case DELIVERY_DONE:
                return Optional.of(DeliveryStatusEnum.DELIVERY_DONE);
            case DELIVERY_CANCELLED:
                return Optional.of(DeliveryStatusEnum.DELIVERY_CANCELLED);
            case DELIVERY_FAILED:
                return Optional.of(DeliveryStatusEnum.DELIVERY_FAILED);
            default:
                return Optional.empty();
        }
    }


    public static FarmDeliveryStatusEnum enumOf(int value) {
        for (FarmDeliveryStatusEnum each : values()) {
            if (each.getCode() == value) {
                return each;
            }
        }

        return null;
    }



    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
