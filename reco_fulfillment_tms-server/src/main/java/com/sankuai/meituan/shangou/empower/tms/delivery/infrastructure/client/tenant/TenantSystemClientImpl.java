package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.tenant;

import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.saas.tenant.thrift.PoiRelationThriftService;
import com.meituan.shangou.saas.tenant.thrift.TenantThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiRelationTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiDetailInfosResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiRelationQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiRelationSimpleMapResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.TenantInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.response.TenantBaseResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.response.TenantInfoResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aop.LoadTestAop;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiDetailInfoResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.ChannelStoreQueryResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.PoiBaseInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.StoreOpeningOnlineChannelsQueryResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantChannelStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 租户系统客户端实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/10
 */
@Slf4j
@Service
public class TenantSystemClientImpl implements TenantSystemClient {
	private static final int SUCCESS = 0;

	@Resource
	private ChannelPoiManageThriftService channelPoiManageThriftServiceClient;
	@Resource
	private PoiThriftService poiThriftServiceClient;

	@Resource
	private TenantThriftService tenantThriftService;

    @Resource
    private PoiRelationThriftService poiRelationThriftService;

	@Override
	@CatTransaction
	@LoadTestAop
	public StoreOpeningOnlineChannelsQueryResult queryStoreOpeningOnlineChannels(Long tenantId, Long storeId) {
		try {
			log.info("ChannelPoiManageThriftService.queryPoiDetailInfoByPoiId begin, tenantId={}, storeId={}", tenantId, storeId);
			PoiDetailInfoResponse response = channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiId(tenantId, storeId);
			log.info("ChannelPoiManageThriftService.queryPoiDetailInfoByPoiId begin, response={}", response);
			if (response.getStatus().getCode() == SUCCESS) {
				return new StoreOpeningOnlineChannelsQueryResult(
						Optional.ofNullable(response.getPoiDetailInfoDTO().getChannelPoiInfoList())
								.orElse(new ArrayList<>())
								.stream()
								.map(ChannelPoiInfoDTO::getChannelId)
								.filter(Objects::nonNull)
								.collect(Collectors.toList())
				);
			} else {
				return new StoreOpeningOnlineChannelsQueryResult(
						new Failure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, response.getStatus().getMessage())
				);
			}
		} catch (Exception e) {
			return new StoreOpeningOnlineChannelsQueryResult(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
		}
	}

	@Override
	@CatTransaction
	public TenantStoreInfo queryStoreDetailInfo(Long tenantId, Long storeId) {
		try {
			PoiMapResponse response = poiThriftServiceClient.queryTenantPoiInfoMapByPoiIds(Lists.newArrayList(storeId), tenantId);
			if (response.getStatus().getCode().equals(StatusCodeEnum.SUCCESS.getCode())){
				PoiInfoDto poi = response.getOne(storeId);
				if (Objects.nonNull(poi)) {
					return TenantStoreInfo.builder()
							.cityCode(poi.getDistrict().getCityId())
							.areaCode(poi.getDistrict().getAreaId())
							.phone(poi.getMobile())
							.storeName(poi.getPoiName())
							.address(poi.getPoiAddress())
                            .entityType(poi.getEntityType()).shippingMode(poi.getShippingMode())
							.longitude(Objects.nonNull(poi.getLongitude()) ? poi.getLongitude().toString() : StringUtils.EMPTY)
							.latitude(Objects.nonNull(poi.getLatitude()) ? poi.getLatitude().toString() : StringUtils.EMPTY)
							.servicePhone(poi.getServicePhone())
							.build();
				}
			}
		} catch (Exception e) {
			log.error("queryStoreDetailInfo error", e);
		}

		return null;
	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	@LoadTestAop
	public Optional<TenantChannelStoreInfo> queryChannelStoreDetailInfoWithAnyChannel(Long tenantId, Long storeId,Integer channelType) {
		PoiDetailInfoResponse response = channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiId(tenantId, storeId);
		log.info("channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiId reponse = {}", response);
		if (response.getStatus().getCode() != SUCCESS || response.getPoiDetailInfoDTO() == null) {
			return Optional.empty();
		}
		PoiDetailInfoDTO poiDetailInfo = response.getPoiDetailInfoDTO();
		if(poiDetailInfo==null){
			return Optional.empty();
		}

		Double longitude = poiDetailInfo.getLongitude();
		Double latitude = poiDetailInfo.getLatitude();
		if(longitude==null || latitude==null){
			if(CollectionUtils.isNotEmpty(poiDetailInfo.getChannelPoiInfoList())){
				Optional<ChannelPoiInfoDTO> poiInfoDTOOptional = poiDetailInfo.getChannelPoiInfoList()
						.stream()
						.filter(store -> store.getChannelId() == (channelType == null
								? DynamicChannelType.MEITUAN.getChannelId()
								: channelType))
						.findAny();
				if(!poiInfoDTOOptional.isPresent()){
					poiInfoDTOOptional=poiDetailInfo.getChannelPoiInfoList().stream().findAny();
				}
				if(poiInfoDTOOptional.isPresent()){
					longitude = poiInfoDTOOptional.get().getLongitude();
					latitude = poiInfoDTOOptional.get().getLatitude();
				}
			}
		}

		String lat="";
		String lon="";
		if(latitude!=null){
			lat=String.valueOf(latitude);
		}
		if(longitude!=null){
			lon=String.valueOf(longitude);
		}


		return Optional.of(TenantChannelStoreInfo.builder()
				.cityCode(poiDetailInfo.getDistrict().getCityId())
				.areaCode(poiDetailInfo.getDistrict().getAreaId())
				.phone(poiDetailInfo.getPoiPhoneNum())
				.storeName(poiDetailInfo.getPoiName())
				.latitude(lat)
				.longitude(lon)
				.address(poiDetailInfo.getPoiAddress())
				.tenantId(tenantId)
				.shopId(storeId)
				.servicePhone(poiDetailInfo.getServicePhone())
				.build());

	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public ChannelStoreQueryResult queryChannelStoreDetailInfo(Long tenantId, Long storeId) {
		PoiDetailInfoResponse response = channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiId(tenantId, storeId);
		log.info("channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiId reponse = {}", response);
		if (response.getStatus().getCode() != SUCCESS || response.getPoiDetailInfoDTO() == null) {
			return new ChannelStoreQueryResult(new Failure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED,
					response.getStatus().getMessage()));
		}
		PoiDetailInfoDTO poiDetailInfo = response.getPoiDetailInfoDTO();
		return new ChannelStoreQueryResult(Optional.ofNullable(response.getPoiDetailInfoDTO().getChannelPoiInfoList())
				.orElse(Collections.emptyList())
				.stream()
				.map(poiInfo -> TenantChannelStoreInfo.builder()
						.cityCode(poiDetailInfo.getDistrict().getCityId())
                        .areaCode(poiDetailInfo.getDistrict().getAreaId())
						.phone(poiDetailInfo.getPoiPhoneNum())
						.storeName(poiDetailInfo.getPoiName())
						.latitude(String.valueOf(poiInfo.getLatitude()))
						.longitude(String.valueOf(poiInfo.getLongitude()))
						.channelId(poiInfo.getChannelId())
						.address(poiDetailInfo.getPoiAddress())
						.tenantId(tenantId)
						.shopId(storeId)
                        .channelPoiId(poiInfo.getChannelPoiId())
						.channelInnerPoiId(poiInfo.getChannelInnerPoiId())
						.servicePhone(poiDetailInfo.getServicePhone())
						.build()).collect(Collectors.toList()));
	}


	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public Map<Long, List<TenantChannelStoreInfo>> queryChannelStoreDetailInfoList(Long tenantId, List<Long> storeIds) {
		if (CollectionUtils.isEmpty(storeIds)) {
			return Collections.emptyMap();
		}
		PoiDetailInfosResponse response = channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiIds(tenantId, storeIds);
		log.info("channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiIds response = {}", response);
		if (response.getStatus().getCode() != SUCCESS || response.getPoiDetailInfoDTOs() == null) {
			log.error("invoke channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiIds fail, tenantId:{}, resp:{}", tenantId, response);
			throw new RuntimeException("查询渠道门店信息失败");
		}
		HashMap<Long, List<TenantChannelStoreInfo>> poiChannelInfoMap = new HashMap<>();
		for (PoiDetailInfoDTO poiDetailInfoDTO : response.getPoiDetailInfoDTOs()) {

			List<TenantChannelStoreInfo> poiChannelInfo = Optional.ofNullable(poiDetailInfoDTO.getChannelPoiInfoList())
					.orElse(Collections.emptyList())
					.stream()
					.map(poiInfo -> TenantChannelStoreInfo.builder()
							.cityCode(poiDetailInfoDTO.getDistrict().getCityId())
							.cityName(poiDetailInfoDTO.getDistrict().getCityName())
							.areaCode(poiDetailInfoDTO.getDistrict().getAreaId())
							.phone(poiDetailInfoDTO.getPoiPhoneNum())
							.storeName(poiDetailInfoDTO.getPoiName())
							.latitude(String.valueOf(poiInfo.getLatitude()))
							.longitude(String.valueOf(poiInfo.getLongitude()))
							.channelId(poiInfo.getChannelId())
							.channelPoiId(poiInfo.getChannelPoiId())
							.channelInnerPoiId(poiInfo.getChannelInnerPoiId())
							.address(poiDetailInfoDTO.getPoiAddress())
							.tenantId(tenantId)
							.shopId(poiDetailInfoDTO.getPoiId())
							.build()).collect(Collectors.toList());

			poiChannelInfoMap.put(poiDetailInfoDTO.getPoiId(), poiChannelInfo);
		}

		return poiChannelInfoMap;

	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public TenantInfo queryTenantInfo(Long tenantId){
		try {
			TenantInfoResponse response=tenantThriftService.queryTenantInfoByTenantId(tenantId);
			TenantInfoDto tenantInfoDto=response.getTenantInfo();
			if(tenantInfoDto==null){
				return null;
			}
			return TenantInfo.builder().tenantId(tenantId)
					.tenantName(tenantInfoDto.getTenantName())
					.build();
		}catch (Exception e){
			log.error("query tenant info error tenantId:{}",tenantId,e);
		}
		return null;
	}

	@MethodLog(logRequest = false, logResponse = true)
	@Override
	public List<PoiBaseInfo> queryTenantStoreList(Long tenantId) {
		PoiListResponse response = poiThriftServiceClient.queryTenantPoiList(tenantId);
		if (response == null || response.getStatus() == null) {
			log.error("invoke poiThriftServiceClient.queryPoiListByTenantIds error, tenantId:{}", tenantId);
			throw new RuntimeException("查询租户门店列表失败");

		}

		if (response.getStatus().getCode() != ResultCode.SUCCESS.code) {
			log.error("invoke poiThriftServiceClient.queryPoiListByTenantIds fail, tenantId:{}, resp:{}", tenantId, response);
			throw new RuntimeException("查询租户门店列表失败");
		}

		return response.getPoiList().stream().map(poiBaseInfoDto -> {
					return PoiBaseInfo.builder()
							.tenantId(poiBaseInfoDto.getTenantId())
							.poiId(poiBaseInfoDto.getPoiId())
							.poiName(poiBaseInfoDto.getPoiName())
							.build();
				}).collect(Collectors.toList());
	}

    @MethodLog(logRequest = false, logResponse = true)
    @Override
    public Map<Long, List<Long>> batchQueryRelationMapByPoiIds(Long tenantId, List<Long> poiIdList,
            boolean reverseRelation) {
        if (CollectionUtils.isEmpty(poiIdList)) {
            return Collections.emptyMap();
        }
        PoiRelationQueryRequest relationQueryRequest = new PoiRelationQueryRequest();
        relationQueryRequest.setTenantId(tenantId);
        relationQueryRequest.setPoiIdList(poiIdList);
        relationQueryRequest.setRelationType(PoiRelationTypeEnum.STORE_SHAREABLE_WAREHOUSE_RELATION.code());
        relationQueryRequest.setReverseRelation(reverseRelation);
        try {
            PoiRelationSimpleMapResponse response = poiRelationThriftService
                    .batchQueryRelationMapByPoiIds(relationQueryRequest);
            if (response == null || MapUtils.isEmpty(response.getPoiRelationMap())) {
                return Collections.emptyMap();
            }
            return new HashMap<>(response.getPoiRelationMap());
        } catch (Exception e) {
            log.error("batchQueryRelationMapByPoiIds error", e);
        }
        return Collections.emptyMap();
    }


	@Override
	@LoadTestAop
	public String queryChannelPoiIdByPoiId(Long tenantId, Long poiId, Integer channelType) {
		try {
			PoiDetailInfoResponse response = channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiId(tenantId, poiId);
			log.info("channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiId response = {}", response);
			if (response.getStatus().getCode() != SUCCESS || response.getPoiDetailInfoDTO() == null) {
				return null;
			}
			List<ChannelPoiInfoDTO> channelPoiInfoList = response.getPoiDetailInfoDTO().getChannelPoiInfoList();
			if (CollectionUtils.isEmpty(channelPoiInfoList)) {
				return null;
			}
			return channelPoiInfoList.stream()
					.filter(store -> store.getChannelId() == (channelType == null
							? DynamicChannelType.MEITUAN.getChannelId() : channelType))
					.findAny()
					.map(channelPoiInfoDTO -> {
						if (Objects.equals(DynamicChannelType.MEITUAN.getChannelId(), channelPoiInfoDTO.getChannelId())) {
							return channelPoiInfoDTO.getChannelInnerPoiId();
						} else {
							return channelPoiInfoDTO.getChannelPoiId();
						}
					})
					.orElseGet(() -> channelPoiInfoList.get(0).getChannelPoiId());
		} catch (Exception e) {
			log.error("TenantSystemClient.queryChannelPoiIdByPoiId error", e);
			return null;
		}
	}

	@MethodLog(logRequest = true, logResponse = true)
	@Override
	public List<String> queryTenantCategory(Long tenantId) {
		List<String> list = new ArrayList<>();
		try {
			TenantBaseResponse response = tenantThriftService.getTenantBase(tenantId);
			if (response == null || response.getStatus().getCode() != SUCCESS || response.getTenantBaseDto() == null) {
				return list;
			}
			List<String> categories = response.getTenantBaseDto().getCategories();
			if (CollectionUtils.isEmpty(categories)) {
				return list;
			}
			categories.stream().filter(StringUtils::isNotBlank).forEach(item -> list.add(item.trim()));
		} catch (Exception e) {
			log.info("TenantSystemClient.queryTenantCategory error: ", e);
		}
		return list;
	}
}
