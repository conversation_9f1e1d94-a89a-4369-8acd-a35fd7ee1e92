package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.DeliveryOrderLogRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryChangeLogMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/14
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class DeliveryChangeLogMessageListener extends AbstractDeadLetterConsumer {

	@Resource
	private DeliveryOrderLogRepository deliveryOrderLogRepository;

	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.DELIVERY_LOG_COMMAND;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
		log.info("开始消费配送流水消息: {}", mafkaMessage);
		DeliveryChangeLogMessage message = translateMessage(mafkaMessage);
		if (message == null) {
			return CONSUME_SUCCESS;
		}

		try {
			deliveryOrderLogRepository.saveDeliveryChangeLog(
					message.getDeliveryId(),
					message.getDeliveryOrderStatus(),
					DeliveryEventEnum.valueOf(message.getChangeEvent()),
					message.getChangeInfoJson(),
					TimeUtil.fromMilliSeconds(message.getChangeTime())
			);
			return CONSUME_SUCCESS;

		} catch (Exception e) {
			log.error("消费配送流水消息失败，将会进行重试消费", e);
			return CONSUME_FAILURE;
		}
	}

	private DeliveryChangeLogMessage translateMessage(MafkaMessage mafkaMessage) {
		try {
			DeliveryChangeLogMessage message = translateMessage(mafkaMessage, DeliveryChangeLogMessage.class);
			Preconditions.checkNotNull(message, "empty mafkaMessage");
			Preconditions.checkNotNull(message.getDeliveryId(), "deliveryId is null");
			Preconditions.checkNotNull(DeliveryEventEnum.valueOf(message.getChangeEvent()), "changeEvent is invalid");
			Preconditions.checkNotNull(message.getChangeTime(), "changeTime is null");
			return message;

		} catch (Exception e) {
			log.error("解析配送流水消息失败:{}", mafkaMessage, e);
			Cat.logEvent("DELIVERY_CHANGE_LOG_CONSUME_FAILED", "MESSAGE_WRONG");
			return null;
		}
	}
}
