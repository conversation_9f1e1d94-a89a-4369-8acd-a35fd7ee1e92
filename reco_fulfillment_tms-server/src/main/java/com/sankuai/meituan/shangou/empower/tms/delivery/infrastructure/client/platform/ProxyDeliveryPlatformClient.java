package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform;

import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.DeliveryTenantPoiSyncRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.LinkTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.SiteTypeEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/12
 */
@Slf4j
@Service
@Primary
public class ProxyDeliveryPlatformClient implements DeliveryPlatformClient {

	private final Map<DeliveryPlatformEnum, DeliveryPlatformClient> deliveryPlatformClientMap = new HashMap<>();
	@Resource
	private List<AbstractDeliveryPlatformClient> deliveryPlatformClients;

	@Resource
	private DeliveryChannelApplicationService deliveryChannelApplicationService;

	@PostConstruct
	public void init() {
		Optional.ofNullable(deliveryPlatformClients)
				.orElse(new ArrayList<>())
				.stream()
				.filter(it -> Objects.nonNull(it.getDeliveryPlatform()))
				.forEach(it -> deliveryPlatformClientMap.put(it.getDeliveryPlatform(), it));
	}

	@Override
	public Result<List<DeliveryChannelPreLaunchInfo>> preLaunch(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
		return getDeliveryPlatformClient(deliveryPoi).preLaunch(deliveryPoi, orderInfo);
	}

	@Override
	public Optional<LaunchFailure> launch(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder,Integer transferOrderMark) {
		return getDeliveryPlatformClient(deliveryOrder).launch(deliveryPoi, orderInfo, deliveryOrder,transferOrderMark);
	}

	@Override
	public Optional<Failure> cancelDelivery(DeliveryOrder deliveryOrder) {
		return getDeliveryPlatformClient(deliveryOrder).cancelDelivery(deliveryOrder);
	}

	@Override
	public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryPoi deliveryPoi) {
		return getDeliveryPlatformClient(deliveryOrder).queryRiderLocation(deliveryOrder, deliveryPoi);
	}

	@Override
	public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryChannel deliveryChannelDto, DeliveryPoi deliveryPoi) {
		return getDeliveryPlatformClient(deliveryOrder).queryRiderLocation(deliveryOrder, deliveryChannelDto, deliveryPoi);
	}

	@Override
	public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder) {
		return getDeliveryPlatformClient(deliveryOrder).queryRiderLocation(deliveryOrder);
	}

	@Override
	public List<LaunchRuleInfo> queryAvailableDeliveryRules(DeliveryPlatformEnum deliveryPlatform) {
		return getDeliveryPlatformClient(deliveryPlatform).queryAvailableDeliveryRules(deliveryPlatform);
	}

	@Override
	public Optional<Long> createShop(DeliveryPoi deliveryPoi) {
		return getDeliveryPlatformClient(deliveryPoi.getDeliveryPlatform()).createShop(deliveryPoi);
	}

	@Override
	public Optional<Failure> autoSend(DeliveryOrder deliveryOrder) {
		return getDeliveryPlatformClient(deliveryOrder).autoSend(deliveryOrder);
	}

	@Override
	public Optional<Failure> closeShop(DeliveryPoi deliveryPoi) {
		return getDeliveryPlatformClient(deliveryPoi.getDeliveryPlatform()).closeShop(deliveryPoi);
	}

	@Override
	public Optional<Map<String, DeliveryPlatformUrlInfo>> queryLinkInfo(DeliveryPoi deliveryPoi, List<String> markIdList, LinkTypeEnum typeEnum) {
		return getDeliveryPlatformClient(deliveryPoi.getDeliveryPlatform()).queryLinkInfo(deliveryPoi,markIdList,typeEnum);
	}

	@Override
	public Optional<Map<String, DeliveryPlatformUrlInfo>> queryLinkInfoOfToken(DeliveryPoi deliveryPoi, List<String> markIdList, LinkTypeEnum typeEnum, String token, SiteTypeEnum siteType) {
		return getDeliveryPlatformClient(deliveryPoi.getDeliveryPlatform()).queryLinkInfoOfToken(deliveryPoi, markIdList, typeEnum, token, siteType);
	}

	@Override
	public Optional<Failure> cancelDeliveryForTransOrder(DeliveryOrder deliveryOrder) {
		return getDeliveryPlatformClient(deliveryOrder).cancelDeliveryForTransOrder(deliveryOrder);
	}

	@Override
	public Optional<Failure> cancelDeliveryForOFC(DeliveryOrder deliveryOrder) {
		return getDeliveryPlatformClient(deliveryOrder).cancelDeliveryForOFC(deliveryOrder);
	}

	@Override
	public Optional<Failure> deliveryFastAuth(DeliveryPoi deliveryPoi, List<Long> wmStoreIdList, List<ChannelStoreRelation> relationList) {
		return getDeliveryPlatformClient(deliveryPoi.getDeliveryPlatform()).deliveryFastAuth(deliveryPoi,wmStoreIdList,relationList);
	}

	@Override
	public Result<DapShopAuthResult> deliveryShopAuth(DeliveryPlatformEnum deliveryPlatform, Long storeId) {
		return getDeliveryPlatformClient(deliveryPlatform).deliveryShopAuth(deliveryPlatform, storeId);
	}

	private DeliveryPlatformClient getDeliveryPlatformClient(DeliveryOrder deliveryOrder) {
		DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
		return getDeliveryPlatformClient(DeliveryPlatformEnum.enumOf(deliveryChannel.getDeliveryPlatFormCode()));
	}

	private DeliveryPlatformClient getDeliveryPlatformClient(DeliveryPoi deliveryPoi) {
		return getDeliveryPlatformClient(deliveryPoi.getDeliveryPlatform());
	}

	private DeliveryPlatformClient getDeliveryPlatformClient(DeliveryPlatformEnum deliveryPlatform) {
		return Optional.ofNullable(deliveryPlatformClientMap.get(deliveryPlatform))
				.orElseThrow(() -> new UnsupportedOperationException(String.format("Unsupported platform[%s]", deliveryPlatform)));
	}
	@Override
	public Optional<Failure> syncTenantPoi(DeliveryTenantPoiSyncRequest request) {
		DeliveryPlatformClient deliveryPlatformClient = getDeliveryPlatformClient(DeliveryPlatformEnum.enumOf(request.getPlatformCode()));
		return deliveryPlatformClient.syncTenantPoi(request);
	}

	@Override
	public Optional<LaunchFailure> manualLaunchDelivery(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
		return getDeliveryPlatformClient(deliveryPoi).manualLaunchDelivery(deliveryPoi, orderInfo);
	}

	@Override
	public Optional<Failure> syncPaoTuiDeliveryStatusAfterLockOrder(DeliveryOrder deliveryOrder, String channelServicePackageId) {
		return getDeliveryPlatformClient(deliveryOrder).syncPaoTuiDeliveryStatusAfterLockOrder(deliveryOrder, channelServicePackageId);
	}

	@Override
	public Optional<Failure> syncOrderInfoChange(DeliveryOrder deliveryOrder, OrderInfo orderInfo, List<Integer> changedFields) {
		return getDeliveryPlatformClient(deliveryOrder).syncOrderInfoChange(deliveryOrder, orderInfo, changedFields);
	}

	@Override
	public Optional<Failure> notifyDeliveryPlatformLockStatusChange(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder) {
		return getDeliveryPlatformClient(deliveryOrder).notifyDeliveryPlatformLockStatusChange(deliveryPoi, orderInfo, deliveryOrder);
	}
}
