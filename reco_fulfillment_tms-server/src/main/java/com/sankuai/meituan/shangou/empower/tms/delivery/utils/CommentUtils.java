package com.sankuai.meituan.shangou.empower.tms.delivery.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/22
 * desc: 备注工具类
 */
@Slf4j
public class CommentUtils {

    public static final String COMMENT_FILTER_REPLACE = "comment_filter_replace";

    public static String filterDefaultComment(String comment) {
        log.info("filterDefaultComment-入参：comment={}", comment);
        try {
            if (StringUtils.isEmpty(comment)) {
                return StringUtils.EMPTY;
            }

            String regEx = ConfigUtilAdapter.getString(COMMENT_FILTER_REPLACE, StringUtils.EMPTY);

            List<String> regexList = JsonUtil.fromJson(regEx, new TypeReference<List<String>>() {
            });

            if (CollectionUtils.isEmpty(regexList)) {
                return comment;
            }

            for (String reg : regexList) {
                comment = comment.replaceAll(reg, StringUtils.EMPTY);
            }
        } catch (Exception e) {
            log.warn("过滤默认备注出错", e);
        }

        log.info("filterDefaultComment-出参：comment={}", comment);
        return comment;
    }

}
