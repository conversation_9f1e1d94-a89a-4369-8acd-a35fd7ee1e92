package com.sankuai.meituan.shangou.empower.tms.delivery.exception;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DeliveryOrderEsCompensateCraneTaskException extends RuntimeException {

    private String msg;

    private Integer code;

    public DeliveryOrderEsCompensateCraneTaskException(String msg) {
        super(msg);
        this.code = FailureCodeEnum.SYSTEM_ERROR.getCode();
        this.msg = msg;
    }
}
