<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="classpath:shangou-message-pusher.xml"/>

    <!--大象公众号(TMS预警通知)配置信息-->
    <bean id="xmKey" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="xm.key"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="xmToken" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="xm.token"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="xmPubId" class="com.meituan.service.inf.kms.value.KMSLongValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="xm.pubId"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="dhOaKey" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="dh.oa.key"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="dhOaToken" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="dh.oa.token"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="dhOaPubId" class="com.meituan.service.inf.kms.value.KMSLongValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="dh.oa.pubId"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="dhCid" class="com.meituan.service.inf.kms.value.KMSLongValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="dh.cid"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="meituanCid" class="com.meituan.service.inf.kms.value.KMSLongValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="meituan.cid"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="leafKey" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="leaf.key"/>
        <property name="retryCount" value="3"/>
    </bean>

    <bean id="orderOverloadXmKey" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="order.overload.xm.key"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="orderOverloadXmToken" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="order.overload.xm.token"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="orderOverloadPubId" class="com.meituan.service.inf.kms.value.KMSLongValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="order.overload.xm.pubId"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="orderOverloadMessagePusher" class="com.sankuai.shangou.commons.message.pusher.channelPusher.XmMessagePusher" autowire="byName">
        <property name="xmOaKey" ref="orderOverloadXmKey"/>
        <property name="xmOaToken" ref="orderOverloadXmToken"/>
        <property name="xmOaPubId" ref="orderOverloadPubId"/>
    </bean>
</beans>
