<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <!-- 配送查询线程池配置 -->
    <bean name="queryDeliveryServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator" factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.QueryDeliveryInfoThriftService"/>
        <constructor-arg name="threadPoolName" value="query-delivery-thrift-thread-pool"/> <!-- value格式：${接口名} -->
        <constructor-arg name="min" value="30"/>
        <constructor-arg name="max" value="80"/>
        <constructor-arg name="workQueueSize" value="150"/> <!-- 默认queueSize为-1，修改此参数可能会增加服务端响应时间 -->
    </bean>

    <!-- 批量查询线程池配置 -->
    <bean name="batchQueryDeliveryServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator" factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="thrift.com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.QueryDeliveryInfoThriftService"/>
        <constructor-arg name="threadPoolName" value="batch-query-delivery-thrift-thread-pool"/> <!-- value格式：${接口名} -->
        <constructor-arg name="min" value="4"/>
        <constructor-arg name="max" value="32"/>
        <constructor-arg name="workQueueSize" value="100"/> <!-- 默认queueSize为-1，修改此参数可能会增加服务端响应时间 -->
    </bean>
    <!-- 聚合相关接口查询线程池配置 -->
    <bean name="aggRiderQueryDeliveryServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator" factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="agg_rider_query_executor"/>
        <constructor-arg name="threadPoolName" value="agg-rider-query-delivery-thrift-thread-pool"/> <!-- value格式：${接口名} -->
        <constructor-arg name="min" value="64"/>
        <constructor-arg name="max" value="512"/>
        <constructor-arg name="workQueueSize" value="-1"/>
    </bean>

    <!-- 配送配置动态线程池 -->
    <bean name="deliveryConfigServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator" factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService"/> <!-- value格式：thrift.${接口名} -->
        <constructor-arg name="threadPoolName" value="delivery-configuration-thrift-thread-pool"/> <!-- value格式：${接口名} -->
        <constructor-arg name="min" value="50"/>
        <constructor-arg name="max" value="100"/>
        <constructor-arg name="workQueueSize" value="100"/> <!-- 默认queueSize为-1，修改此参数可能会增加服务端响应时间 -->
    </bean>
    <!-- 外部查询配送配置动态线程池 -->
    <bean name="outerQueryDeliveryServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator" factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.OuterQueryDeliveryInfoThriftService"/> <!-- value格式：thrift.${接口名} -->
        <constructor-arg name="threadPoolName" value="outer-query-delivery-thrift-thread-pool"/> <!-- value格式：${接口名} -->
        <constructor-arg name="min" value="50"/>
        <constructor-arg name="max" value="100"/>
        <constructor-arg name="workQueueSize" value="100"/> <!-- 默认queueSize为-1，修改此参数可能会增加服务端响应时间 -->
    </bean>

    <!-- 服务鉴权 -->
    <bean id="kmsAuthDataSource" class="com.meituan.service.inf.kms.client.KmsAuthDataSource">
        <property name="appkey" value="${app.name}"/>
    </bean>
    <!-- 连接粒度鉴权 -->
    <bean id="defaultAuthHandler" class="com.meituan.service.mobile.mtthrift.auth.DefaultAuthHandler">
        <property name="authDataSource" ref="kmsAuthDataSource"/>
    </bean>

    <!-- 配送渠道动态线程池 -->
    <bean name="deliveryChannelExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator" factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.DeliveryChannelThriftService"/> <!-- value格式：thrift.${接口名} -->
        <constructor-arg name="threadPoolName" value="delivery-channel-thrift-thread-pool"/> <!-- value格式：${接口名} -->
        <constructor-arg name="min" value="50"/>
        <constructor-arg name="max" value="200"/>
        <constructor-arg name="workQueueSize" value="100"/> <!-- 默认queueSize为-1，修改此参数可能会增加服务端响应时间 -->
    </bean>

    <bean name="deliveryOperationServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator" factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService"/> <!-- value格式：thrift.${接口名} -->
        <constructor-arg name="threadPoolName" value="delivery-operation-thrift-thread-pool"/> <!-- value格式：${接口名} -->
        <constructor-arg name="min" value="50"/>
        <constructor-arg name="max" value="100"/>
        <constructor-arg name="workQueueSize" value="100"/> <!-- 默认queueSize为-1，修改此参数可能会增加服务端响应时间 -->
    </bean>

    <bean name="deliveryCallbackThriftServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator" factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.DeliveryCallbackThriftService"/> <!-- value格式：thrift.${接口名} -->
        <constructor-arg name="threadPoolName" value="delivery-callback-thrift-thread-pool"/> <!-- value格式：${接口名} -->
        <constructor-arg name="min" value="50"/>
        <constructor-arg name="max" value="100"/>
        <constructor-arg name="workQueueSize" value="100"/> <!-- 默认queueSize为-1，修改此参数可能会增加服务端响应时间 -->
    </bean>

    <bean id="deliveryOperationThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.DeliveryOperationThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="deliveryOperationServiceExecutor"/>
    </bean>

    <bean id="outerDeliveryOperationThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.OuterDeliveryOperationThriftServiceImpl" />
        </property>
    </bean>

    <bean id="deliveryConfigurationThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.DeliveryConfigurationThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="deliveryConfigServiceExecutor"/>
    </bean>

    <bean id="deliveryRemindConfigThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.DeliveryRemindConfigThriftServiceImpl"/>
        </property>
    </bean>

    <bean id="queryDeliveryInfoThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.QueryDeliveryInfoThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="queryDeliveryServiceExecutor"/>
        <property name="methodExecutor">
            <map>
                <entry key="batchQueryOriginWaybillNo" value-ref="batchQueryDeliveryServiceExecutor"/> <!-- method粒度的线程池，优先级更高 -->
            </map>
        </property>
    </bean>

    <bean id="outerQueryDeliveryInfoThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.OuterQueryDeliveryInfoThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="outerQueryDeliveryServiceExecutor"/>
    </bean>

    <bean id="deliveryCallbackThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.DeliveryCallbackThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="deliveryCallbackThriftServiceExecutor"/>
    </bean>

    <bean id="deliveryChannelThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.DeliveryChannelThriftServiceImpl"/>
        </property>
        <property name="serviceExecutor" ref="deliveryChannelExecutor"/>
    </bean>

    <bean id="cosUnifiedDeliveryCallbackThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.CosUnifiedDeliveryCallbackThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="deliveryCallbackThriftServiceExecutor"/>
    </bean>

    <bean id="riderOperateThriftServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.RiderOperateThriftService"/>
        <constructor-arg name="threadPoolName" value="rider-operate-thrift-service-thread-pool"/>
        <constructor-arg name="min" value="30"/>
        <constructor-arg name="max" value="256"/>
        <constructor-arg name="workQueueSize" value="10"/>
    </bean>

    <bean id="riderLocationExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="rider-location-method-pool"/>
        <constructor-arg name="threadPoolName" value="rider-location-thrift-service-thread-pool"/>
        <constructor-arg name="min" value="30"/>
        <constructor-arg name="max" value="256"/>
        <constructor-arg name="workQueueSize" value="10"/>
    </bean>

    <bean id="disasterRecoveryThriftServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.waimaidata.disaster.utils.DataReportCompleteDataThriftService"/>
        <constructor-arg name="threadPoolName"
                         value="disaster-recovery-thrift-service-thread-pool"/>
        <constructor-arg name="min" value="30"/>
        <constructor-arg name="max" value="256"/>
        <constructor-arg name="workQueueSize" value="10"/>
    </bean>

    <bean id="riderOperateThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.RiderOperateThriftServiceImpl"/>
        </property>
        <property name="serviceExecutor" ref="riderOperateThriftServiceExecutor"/>
        <property name="methodExecutor">
            <map>
                <entry key="riderLocation" value-ref="riderLocationExecutor"/> <!-- method粒度的线程池，优先级更高 -->
            </map>
        </property>
    </bean>

    <bean id="riderQueryThriftServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.RiderQueryThriftService"/>
        <constructor-arg name="threadPoolName" value="rider-query-thrift-service-thread-pool"/>
        <constructor-arg name="min" value="30"/>
        <constructor-arg name="max" value="256"/>
        <constructor-arg name="workQueueSize" value="10"/>
    </bean>

    <bean id="riderQueryThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.RiderQueryThriftServiceImpl"/>
        </property>
        <property name="serviceExecutor" ref="riderQueryThriftServiceExecutor"/>
        <property name="methodExecutor">
            <map>
                <entry key="queryAllRiderDeliveryOrder" value-ref="aggRiderQueryDeliveryServiceExecutor"/> <!-- method粒度的线程池，优先级更高 -->
                <entry key="queryAllDeliveryOrderByOrderIdList" value-ref="aggRiderQueryDeliveryServiceExecutor"/> <!-- method粒度的线程池，优先级更高 -->
            </map>
        </property>
    </bean>

    <bean id="riderDeliveryStatisticThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.RiderDeliveryStatisticThriftServiceImpl"/>
        </property>
    </bean>

    <bean id="riderDeliveryTaskThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.RiderDeliveryTaskThriftServiceImpl"/>
        </property>
    </bean>

    <bean id="orderPlatformDeliveryOperateThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.OrderPlatformDeliveryOperateThriftServiceImpl"/>
        </property>
    </bean>

    <bean id="oFCDeliveryOperationThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.OFCDeliveryOperationThriftServiceImpl" />
        </property>
    </bean>

    <bean id="fourWheelDeliveryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.FourWheelDeliveryThriftServiceImpl" />
        </property>
    </bean>

    <bean id="openAggDeliveryThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.OpenAggDeliveryThriftServiceImpl"/>
        </property>
    </bean>

    <bean id="dataReportCompleteDataThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.DataReportCompleteDataThriftServiceImpl"/>
        </property>
        <property name="serviceExecutor" ref="disasterRecoveryThriftServiceExecutor"/>
    </bean>

    <bean id="dataChangeRecoveryThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift.DataChangeRecoveryThriftServiceImpl"/>
        </property>
        <property name="serviceExecutor" ref="disasterRecoveryThriftServiceExecutor"/>
    </bean>

    <bean id="deliveryServicePublisher" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="port" value="8411"/>
        <property name="appKey" value="${app.name}"/>
        <property name="serviceProcessorMap">
            <map>
                <entry key="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService"
                       value-ref="deliveryOperationThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService"
                       value-ref="deliveryConfigurationThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.DeliveryRemindConfigThriftService"
                       value-ref="deliveryRemindConfigThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.QueryDeliveryInfoThriftService"
                       value-ref="queryDeliveryInfoThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.DeliveryCallbackThriftService"
                       value-ref="deliveryCallbackThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.DeliveryChannelThriftService"
                       value-ref="deliveryChannelThriftServiceBean"/>
                <entry key="com.sankuai.banma.deliverywaybill.order.callback.client.callback.DeliveryCallbackThriftService"
                       value-ref="cosUnifiedDeliveryCallbackThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.RiderOperateThriftService"
                       value-ref="riderOperateThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService"
                       value-ref="riderQueryThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.rider.client.thrift.task.RiderDeliveryTaskThriftService"
                       value-ref="riderDeliveryTaskThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderDeliveryStatisticThriftService"
                       value-ref="riderDeliveryStatisticThriftService"/>
                <entry key="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.OrderPlatformDeliveryOperateThriftService"
                       value-ref="orderPlatformDeliveryOperateThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.OpenAggDeliveryThriftService"
                       value-ref="openAggDeliveryThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.OFCDeliveryOperationThriftService"
                       value-ref="oFCDeliveryOperationThriftService"/>
                <entry key="com.sankuai.meituan.waimaidata.disaster.utils.DataChangeRecoveryThriftService"
                       value-ref="dataChangeRecoveryThriftServiceBean"/>
                <entry key="com.sankuai.meituan.waimaidata.disaster.utils.DataReportCompleteDataThriftService"
                       value-ref="dataReportCompleteDataThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.FourWheelDeliveryThriftService"
                       value-ref="fourWheelDeliveryThriftService"/>
            </map>
        </property>
        <property name="filters">
            <list>
                <ref bean="thriftProviderCatEventFilter"/>
            </list>
        </property>
        <property name="authHandler" ref="defaultAuthHandler"/>
    </bean>

    <bean id="outerDeliveryServicePublisher" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="port" value="8412"/>
        <property name="appKey" value="${app.name}"/>
        <property name="serviceProcessorMap">
            <map>
                <entry key="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.OuterDeliveryOperationThriftService"
                       value-ref="outerDeliveryOperationThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.OuterQueryDeliveryInfoThriftService"
                       value-ref="outerQueryDeliveryInfoThriftServiceBean"/>
            </map>
        </property>
        <property name="filters">
            <list>
                <ref bean="thriftProviderCatEventFilter"/>
            </list>
        </property>
        <property name="authHandler" ref="defaultAuthHandler"/>
    </bean>

</beans>
