---
#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-local.xml

# Zebra属性配置
delivery:
  zebra:
    jdbcRef: shangousaasorder_test_saas_delivery_test # jdbcRef配置, 通过profile隔离
    qnhRuleName: qnh_delivery_test
    poolType: druid
    minPoolSize: 5
    maxPoolSize: 30
    initialPoolSize: 5
    checkoutTimeout: 1000
    maxIdleTime: 1800
    idleConnectionTestPeriod: 60
    acquireRetryAttempts: 3
    acquireRetryDelay: 300
    maxStatements: 0
    maxStatementsPerConnection: 100
    numHelperThreads: 6
    maxAdministrativeTaskTime: 5
    preferredTestQuery: SELECT 1
squirrel:
  redisSgCommon:
    clusterName: redis-sg-common_qa
  redisSgNewSupply:
    clusterName: redis-sg-newsupply-ofc_qa
  redisSgDrunkHorseBusiness:
    clusterName: redis-sg-drunkhorse-business_qa

s3:
  access-key: 553a9470c28941048f816eee09aa1198
  secret-key: be73fdbe8bc54815b93d89031cb1e645
  url: http://msstest.vip.sankuai.com
  bucket: xsupply-product

# Mafka属性配置
#mafka:
#   producer: # 生产者配置
#     namespace: # 必填，所属业务
#     topic: # 必填，消息主题
#   consumer: # 消费者配置
#     namespace: # 必填，所属业务
#     topic: # 必填，消息主题
#     group: # 必填，消费组名称
#     listener: # 必填，监听器类全路径
#     className: # 必填，发送消息类型，用于解码

#歪马店长/副店长角色id列表，多个值用,分割
dh:
  storeManager:
    roleIds: 93909
  clientPush:
    wxMessageTemplateId: 1-qF8-plJne44F7Y1zAM-u65PCj5n5zx-4tUqNs7oeI
    smsMessageTemplateId: 1654777862819160102


---

spring:
  profiles: dev

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-offline.xml
delivery:
  zebra:
    jdbcRef: shangousaasorder_saas_delivery_dev
    qnhRuleName: qnh_delivery_test
squirrel:
  redisSgCommon:
    clusterName: redis-sg-common_dev
  redisSgNewSupply:
    clusterName: redis-sg-newsupply-ofc_dev
  clusterName: redis-sg-common_dev

eagle:
  clusterName: shangou_tms_default
  accessKey: AC896E76E21FB43108FDF13ADB300C85

dh:
  clientPush:
    wxMessageTemplateId: 1-qF8-plJne44F7Y1zAM-u65PCj5n5zx-4tUqNs7oeI
    smsMessageTemplateId: 1654777494898765844

---

spring:
  profiles: test

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-offline.xml
delivery:
  zebra:
    jdbcRef: shangousaasorder_test_saas_delivery_test
    qnhRuleName: qnh_delivery_test
squirrel:
  redisSgCommon:
    clusterName: redis-sg-common_qa
  redisSgNewSupply:
    clusterName: redis-sg-newsupply-ofc_qa
  redisSgDrunkHorseBusiness:
    clusterName: redis-sg-drunkhorse-business_qa
  clusterName: redis-sg-common_qa

eagle:
  clusterName: shangou_tms_default
  accessKey: AC896E76E21FB43108FDF13ADB300C85

dh:
  clientPush:
    wxMessageTemplateId: 1-qF8-plJne44F7Y1zAM-u65PCj5n5zx-4tUqNs7oeI
    smsMessageTemplateId: 1654777494898765844

short-link:
  biz-type: 1164

---

spring:
  profiles: staging

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-online.xml
delivery:
  zebra:
    jdbcRef: shangousaasorder_saas_delivery_product
    qnhRuleName: tms_delivery_product
squirrel:
  redisSgCommon:
    clusterName: redis-sg-common_stage
  redisSgNewSupply:
    clusterName: redis-sg-newsupply-ofc_stage
  redisSgDrunkHorseBusiness:
    clusterName: redis-sg-drunkhorse-business_stage

eagle:
  clusterName: shangou_eaglenode-es-tms_default
  accessKey: AC896E76E21FB43108FDF13ADB300C85

#歪马店长/副店长角色id列表
dh:
  storeManager:
    roleIds: 42123,38464

s3:
  access-key: 689e9e56b7de41f9951b8e42e2a5ba07
  secret-key: b3b6bc33f5714d339917ae4095b84246
  url: https://s3plus.vip.sankuai.com
  bucket: xsupply-product


short-link:
  biz-type: 764
---

spring:
  profiles: prod

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-online.xml
delivery:
  zebra:
    jdbcRef: shangousaasorder_saas_delivery_product
    qnhRuleName: tms_delivery_product
squirrel:
  redisSgCommon:
    clusterName: redis-sg-common_product
  redisSgNewSupply:
    clusterName: redis-sg-newsupply-ofc_product
  redisSgDrunkHorseBusiness:
    clusterName: redis-sg-drunkhorse-business_product

eagle:
  clusterName: shangou_eaglenode-es-tms_default
  accessKey: AC896E76E21FB43108FDF13ADB300C85

#歪马店长/副店长角色id列表
dh:
  storeManager:
    roleIds: 28084,38308
  clientPush:
    wxMessageTemplateId: 1-qF8-plJne44F7Y1zAM-u65PCj5n5zx-4tUqNs7oeI
    smsMessageTemplateId: 1654777862819160102

s3:
  access-key: 689e9e56b7de41f9951b8e42e2a5ba07
  secret-key: b3b6bc33f5714d339917ae4095b84246
  url: https://s3plus.vip.sankuai.com
  bucket: xsupply-product

short-link:
  biz-type: 764