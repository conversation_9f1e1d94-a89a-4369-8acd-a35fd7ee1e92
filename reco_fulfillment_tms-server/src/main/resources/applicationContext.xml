<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.sankuai.meituan.shangou.saas.common,com.sankuai.qnh.ofc"/>

    <import resource="classpath:applicationContext-xm-config.xml"/>
    <import resource="delivery-zebra-datasource.xml"/>
    <import resource="classpath:applicationContext-thrift-client.xml"/>
    <import resource="classpath:applicationContext-thrift-server.xml"/>
    <import resource="classpath*:squirrel-config.xml"/>
    <import resource="classpath*:filter-config.xml"/>
    <import resource="classpath*:leaf-config.xml"/>

    <bean id="crane.ScheduleManager"
          class="com.cip.crane.client.ScheduleManager" init-method="init" destroy-method="destroy" lazy-init="false">
        <!--可以为空 非空范围在8410（含）~8430（含） 默认为8410，用户可选配置-->
        <property name="taskAcceptorPort" value="8419"/>
        <!--此端口一般不会变，该端口对应调度端的通信端口，用户可选配置-->
        <property name="taskCallBackPort" value="8383"/>
        <!--initedJobs会保存用户注册的所有任务，用户必须配置-->
        <property name="initedJobs">
            <map>
                <entry key="com.sankuai.sgfulfillment.tms.RiderDeliveryOrderStatusCompensateCraneService"
                       value-ref="riderDeliveryOrderStatusCompensateCraneService"/>
                <entry key="monitor-delivery-capacity-load-task"
                       value-ref="monitorDeliveryCapacityLoadCraneTask"/>
                <entry key="delivery-channel-refresh-crane-task"
                       value-ref="deliveryChannelRefreshCraneTask"/>
                <entry key="delivery-order-es-compensate-crane-task"
                       value-ref="deliveryOrderEsCompensateCraneTask"/>
            </map>
        </property>
    </bean>
</beans>
