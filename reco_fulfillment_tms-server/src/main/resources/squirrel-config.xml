<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">


    <bean id="redisSgCommonProduct" class="com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory">
        <!-- 集群名称,必填 -->
        <property name="clusterName" value="${squirrel.redisSgCommon.clusterName}"/>
        <!--读取超时时间,缓存业务建议改成100，存储业务建议改成1000，默认值为1000。选填-->
        <property name="readTimeout" value="1000"/>
        <!--路由策略,默认值是master-only表示只从主节点读取。slave-only表示只读从节点,master-slave表示主从都可以读。选填-->
        <property name="routerType" value="master-slave"/>
        <!--连接redis节点的连接池配置，选填-->
        <property name="poolMaxIdle" value="30"/>
        <property name="poolMaxTotal" value="50"/>
        <property name="poolWaitMillis" value="500"/>

        <!--异步操作线程池配置，选填-->
        <property name="asyncCoreSize" value="16"/>
        <property name="asyncMaxSize" value="64"/>
        <property name="asyncQueueSize" value="1000"/>
    </bean>

    <bean id="redisSgNewSupplyOfc" class="com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory">
        <!-- 集群名称,必填 -->
        <property name="clusterName" value="${squirrel.redisSgNewSupply.clusterName}"/>
        <!--读取超时时间,缓存业务建议改成100，存储业务建议改成1000，默认值为1000。选填-->
        <property name="readTimeout" value="1000"/>
        <!--路由策略,默认值是master-only表示只从主节点读取。slave-only表示只读从节点,master-slave表示主从都可以读。选填-->
        <property name="routerType" value="master-slave"/>
        <!--连接redis节点的连接池配置，选填-->
        <property name="poolMaxIdle" value="30"/>
        <property name="poolMaxTotal" value="50"/>
        <property name="poolWaitMillis" value="500"/>

        <!--异步操作线程池配置，选填-->
        <property name="asyncCoreSize" value="16"/>
        <property name="asyncMaxSize" value="64"/>
        <property name="asyncQueueSize" value="1000"/>
    </bean>

    <bean id="redisSgDrunkHorseBusiness" class="com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory">
        <!-- 集群名称,必填 -->
        <property name="clusterName" value="${squirrel.redisSgDrunkHorseBusiness.clusterName}"/>
        <!--读取超时时间,缓存业务建议改成100，存储业务建议改成1000，默认值为1000。选填-->
        <property name="readTimeout" value="1000"/>
        <!--路由策略,默认值是master-only表示只从主节点读取。slave-only表示只读从节点,master-slave表示主从都可以读。选填-->
        <property name="routerType" value="master-slave"/>
        <!--连接redis节点的连接池配置，选填-->
        <property name="poolMaxIdle" value="30"/>
        <property name="poolMaxTotal" value="50"/>
        <property name="poolWaitMillis" value="500"/>

        <!--异步操作线程池配置，选填-->
        <property name="asyncCoreSize" value="16"/>
        <property name="asyncMaxSize" value="64"/>
        <property name="asyncQueueSize" value="1000"/>
    </bean>
</beans>