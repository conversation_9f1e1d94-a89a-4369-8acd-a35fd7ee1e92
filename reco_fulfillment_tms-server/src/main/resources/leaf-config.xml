<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="idGen" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.inf.leaf.thrift.IDGen"/> <!--leaf的 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 本地appkey，请自行配置 -->
        <property name="remoteAppkey" value="com.sankuai.leaf.service.common"/>  <!-- leaf Appkey  -->
        <property name="timeout" value="500"/> <!--超时时间为500ms-->
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>


    <bean id="deliveryIdGen" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.inf.leaf.thrift.IDGen"/> <!--leaf的 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 本地appkey，请自行配置 -->
        <property name="remoteAppkey" value="com.sankuai.leaf.common.default"/>  <!-- leaf Appkey  -->
        <property name="timeout" value="500"/> <!--超时时间为500ms-->
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>
</beans>