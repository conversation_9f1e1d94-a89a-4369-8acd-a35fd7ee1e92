<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

<!--    <bean id="xmThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">-->
<!--        <property name="maxActive"-->
<!--                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('xm_thrift_client_pool_max-active', '100')}"/>-->
<!--        <property name="maxIdle"-->
<!--                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('xm_thrift_client_pool_max-idle', '20')}"/>-->
<!--        <property name="minIdle"-->
<!--                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('xm_thrift_client_pool_min-idle', '1')}"/>-->
<!--        <property name="maxWait"-->
<!--                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('xm_thrift_client_pool_max-wait', '1000')}"/>-->
<!--        <property name="testOnBorrow" value="true"/>-->
<!--        <property name="testOnReturn" value="false"/>-->
<!--        <property name="testWhileIdle" value="false"/>-->
<!--    </bean>-->

<!--    <bean id="pushMessageThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
<!--        <property name="mtThriftPoolConfig" ref="xmThriftPoolConfig"/>-->
<!--        <property name="serviceInterface" value="com.sankuai.xm.pubapi.thrift.PushMessageServiceI"/>-->
<!--        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('xm_thrift_timeout','10000')}"/>-->
<!--        <property name="serverDynamicWeight" value="true"/>-->
<!--        <property name="appKey" value="${app.name}"/>-->
<!--        <property name="remoteAppkey" value="com.sankuai.xm.pubapi"/>-->
<!--        <property name="remoteServerPort" value="8820"/>-->
<!--        <property name="nettyIO" value="true"/>-->
<!--        <property name="filters">-->
<!--            <list>-->
<!--                <ref bean="thriftInvokerCatEventFilter"/>-->
<!--            </list>-->
<!--        </property>-->
<!--    </bean>-->

</beans>
