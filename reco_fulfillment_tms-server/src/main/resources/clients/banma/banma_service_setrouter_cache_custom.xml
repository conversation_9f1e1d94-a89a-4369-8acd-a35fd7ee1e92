<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <!--custom point: remove duplicate import-->
    <!--<import resource="classpath:banmaConfig/setrouter/banma_service_setrouter_config.xml" />-->
    <!--custom point: remove duplicate import-->

    <!--可自定义policy实现set路由降级逻辑，默认使用com.sankuai.meituan.banma.setrouter.thrift.clientcache.policy.BmSetCacheClientRouterDegradePolicy-->
    <!--<bean id="bmSetRouterDegradePolicy" class="com.sankuai.meituan.banma.setrouter.thrift.core.policy.BmSetRouterDegradePolicy" />-->

    <!--BmSetCacheClientRouterDegradePolicy 本地内存降级使用配置-->
    <bean id="syncCacheConfig" class="com.sankuai.meituan.banma.setrouter.thrift.clientcache.constants.BmSetRouterClientCacheConfig" >
        <property name="enableOrg" value="true"/> <!--组织本地缓存,预计占用2mb -->
        <property name="enableArea" value="true"/> <!--区域本地缓存,预计占用2mb -->
        <property name="enableRider" value="false"/> <!--骑手本地缓存 默认不开启，数据量较大。截止2018-9-17 需要占用600+mb-->
        <property name="enablePoi" value="false"/>  <!--商家本地缓存 默认不开启，数据量较大。截止2018-9-17 需要占用400+mb-->
        <property name="initOrgAsyncUseAsync" value="false"/> <!--是否异步初始化组织数据 -->
        <property name="initAreaAsyncUseAsync" value="false"/> <!--是否异步初始化区域数据 -->
        <property name="initRiderUseAsync" value="true"/> <!--是否异步初始化骑手数据-->
        <property name="initPoiAsyncUseAsync" value="true"/>  <!--是否异步初始化商家数据-->
    </bean>

    <!--BmSetCacheClientRouterDegradePolicy 依赖路由Set服务Iface-->
    <bean id="bmSetRouterClientCacheThriftIface" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="bmSetRouterThriftPoolConfig"/>
        <property name="serviceInterface"  value="com.sankuai.meituan.banma.setrouter.thrift.clientcache.iface.BmSetRouterClientCacheThriftIface"/>
        <property name="timeout" value="3000"/><!-- thrift rpc 超时时间（毫秒） -->
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="appKey"/>
        <!--<property name="remoteServerPort" value="9091"/>-->
        <property name="filterByServiceName" value="true" />
        <property name="remoteAppkey" value="com.sankuai.banma.paas.setrouter"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="bmSetRouterOrgMappingThriftIface" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="bmSetRouterThriftPoolConfig"/>
        <property name="serviceInterface"  value="com.sankuai.meituan.banma.setrouter.thrift.mapping.iface.BmSetRouterOrgMappingThriftIface"/>
        <property name="timeout" value="3000"/><!-- thrift rpc 超时时间（毫秒） -->
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="appKey"/>
        <!--<property name="remoteServerPort" value="9106"/>-->
        <property name="filterByServiceName" value="true" />
        <property name="remoteAppkey" value="com.sankuai.deliveryrouter.setrouter.admin"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>


    <bean id="bmSetRouterAreaMappingThriftIface" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="bmSetRouterThriftPoolConfig"/>
        <property name="serviceInterface"  value="com.sankuai.meituan.banma.setrouter.thrift.mapping.iface.BmSetRouterAreaMappingThriftIface"/>
        <property name="timeout" value="3000"/><!-- thrift rpc 超时时间（毫秒） -->
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="appKey"/>
        <!--<property name="remoteServerPort" value="9107"/>-->
        <property name="filterByServiceName" value="true" />
        <property name="remoteAppkey" value="com.sankuai.deliveryrouter.setrouter.admin"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="bmSetRouterMappingAdaptor" class="com.sankuai.meituan.banma.setrouter.thrift.clientcache.adaptor.BmSetRouterMappingAdaptor"></bean>

    <bean id="bmSetRouterClientCacheService" class="com.sankuai.meituan.banma.setrouter.thrift.clientcache.service.BmSetRouterClientCacheService" destroy-method="destroy" >
        <property name="syncCacheConfig" ref="syncCacheConfig"/>
        <property name="clientVersion" value="1" />
    </bean>

    <bean id="bmSetRouterCacheClientDegradePolicy" class="com.sankuai.meituan.banma.setrouter.thrift.clientcache.policy.BmSetCacheClientRouterDegradePolicy"/>

    <bean id="bmSetRouterClosePlugin" class="com.sankuai.meituan.banma.setrouter.thrift.clientcache.service.close.BmSetRouterClosePlugin" >
        <property name="clientVersion" value="1" />
    </bean>

    <bean id="bmSetRouterReportClientDataAdaptor" class="com.sankuai.meituan.banma.setrouter.thrift.common.adaptor.BmSetRouterReportClientDataAdaptor">
        <property name="bmSetRouterReportClientDataThriftIface" ref="bmSetRouterReportClientDataThriftIface" />
    </bean>
</beans>
