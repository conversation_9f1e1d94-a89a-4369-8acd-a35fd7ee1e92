<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
		 http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd">

	<context:annotation-config/>
	<context:component-scan base-package="com.sankuai.meituan.banma.common.set" />

    <!-- 路由set核心Iface和service 提供查询SetName和SetKey基本能力 -->
    <import resource="classpath:banmaConfig/setrouter/banma_service_setrouter_core.xml" />
    <!-- 路由set服务本地缓存 -->
    <!--custom point: replace import file-->
    <import resource="classpath:clients/banma/banma_service_setrouter_cache_custom.xml" />
    <!--custom point: replace import file-->

    <bean id="bmSetRouteService" class="com.sankuai.meituan.banma.common.set.service.BmSetRouteService" />
</beans>
