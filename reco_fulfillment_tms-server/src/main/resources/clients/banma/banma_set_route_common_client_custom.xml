<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--custom point: remove duplicate import-->
    <!--<import resource="classpath:banma_common_client.xml"/>-->
    <!--custom point: remove duplicate import-->

    <!--custom point: replace import file-->
    <import resource="classpath:clients/banma/banma_set_route_core_custom.xml"/>
    <!--custom point: replace import file-->

    <bean id="bmAreaSetRouteProxy" class="com.sankuai.meituan.banma.common.client.set.proxy.BmAreaSetRouteProxy"/>
    <bean id="bmCitySetRouteProxy" class="com.sankuai.meituan.banma.common.client.set.proxy.BmCitySetRouteProxy"/>
    <bean id="bmCityWithZeroSetRouteProxy" class="com.sankuai.meituan.banma.common.client.set.proxy.BmCityWithZeroSetRouteProxy"/>
    <bean id="bmDefaultSetRouteProxy" class="com.sankuai.meituan.banma.common.client.set.proxy.BmDefaultSetRouteProxy"/>
    <bean id="bmLeafIdSetRouteProxy" class="com.sankuai.meituan.banma.common.client.set.proxy.BmLeafIdSetRouteProxy"/>
    <bean id="bmOrgSetRouteProxy" class="com.sankuai.meituan.banma.common.client.set.proxy.BmOrgSetRouteProxy"/>
    <bean id="bmPackageIdSetRouteProxy" class="com.sankuai.meituan.banma.common.client.set.proxy.BmPackageIdSetRouteProxy"/>
    <bean id="bmPoiSetRouteProxy" class="com.sankuai.meituan.banma.common.client.set.proxy.BmPoiSetRouteProxy"/>
    <bean id="bmRiderSetRouteProxy" class="com.sankuai.meituan.banma.common.client.set.proxy.BmRiderSetRouteProxy"/>
</beans>
