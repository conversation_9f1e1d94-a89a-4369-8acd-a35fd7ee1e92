<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd">

    <!--custom point: remove duplicate import-->
    <!--<import resource="classpath:banma_common_client.xml"/>-->
    <!--custom point: remove duplicate import-->

    <!--custom point: remove duplicate import-->
    <!--<import resource="classpath:clients/banma/banma_set_route_common_client_custom.xml"/>-->
    <!--custom point: remove duplicate import-->

    <context:component-scan base-package="com.sankuai.meituan.logisticorder.platform.client"/>

    <!--接口路由方法的配置 不同服务不能重复-->
    <bean id="getUlapOpenRouteConfigFromXml"
          class="com.sankuai.meituan.banma.common.client.set.config.GetRouteConfigFromXml" init-method="init">
        <property name="xmlFilePath" value="routeConfig/ulap_method_route_strategy_config.xml"/>
    </bean>

    <!--根据路由配置实现路由  不同服务不能重复-->
    <bean id="bmUlapOpenSetRoutePolicy" class="com.sankuai.meituan.banma.common.client.set.policy.BmSetRoutePolicy">
        <property name="getRouteConfig" ref="getUlapOpenRouteConfigFromXml"/>
    </bean>

    <bean id="ulapThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="200"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <!--custom point: no need-->
    <!-- <bean id="logisticOrderThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="ulapThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.logisticorder.platform.client.service.LogisticOrderThriftService"/>
        <property name="timeout" value="500"/>
        <property name="appKey" ref="appKey"/>
        <property name="remoteAppkey" value="com.sankuai.deliverywaybill.ulap"/>
        <property name="filterByServiceName" value="true"/>
        <property name="userDefinedCellPolicy" ref="bmUlapOpenSetRoutePolicy"/>
    </bean>-->
    <!--custom point: no need-->

    <bean id="logisticsOrderSelfDeliveryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="ulapThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.logisticorder.platform.client.service.LogisticsOrderAggregationThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" ref="appKey"/>
        <property name="remoteAppkey" value="com.sankuai.deliverywaybill.ulap"/>
        <property name="filterByServiceName" value="true"/>
        <!--指定路由策略-->
        <property name="userDefinedCellPolicy" ref="bmUlapOpenSetRoutePolicy"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

</beans>
