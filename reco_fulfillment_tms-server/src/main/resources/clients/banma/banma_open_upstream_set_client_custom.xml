<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
		 http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd">

    <!--custom point: remove duplicate import-->
    <!--<import resource="classpath:banma_common_client.xml" />-->
    <!--custom point: remove duplicate import-->

	<import resource="classpath:clients/banma/banma_set_route_common_client_custom.xml" />

	<context:component-scan base-package="com.sankuai.meituan.banma.thrift.upstream.open" />

    <!--接口路由方法的配置 不同服务不能重复-->
    <bean id = "getUpsOpenRouteConfigFromXml" class="com.sankuai.meituan.banma.common.client.set.config.GetRouteConfigFromXml" init-method="init">
        <property name="xmlFilePath" value="routeConfig/ups_open_method_route_strategy_config.xml"/>
    </bean>

    <!--根据路由配置实现路由  不同服务不能重复-->
    <bean id = "bmUpsOpenSetRoutePolicy" class="com.sankuai.meituan.banma.common.client.set.policy.BmSetRoutePolicy">
        <property name="getRouteConfig" ref="getUpsOpenRouteConfigFromXml"/>
    </bean>

    <bean id="bmUpstreamThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="200"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
	</bean>

    <!--custom point: no need-->
    <!--<bean id="openUpstreamPkgThriftIface" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="bmUpstreamThriftPoolConfig"/>
        <property name="serviceInterface"  value="com.sankuai.meituan.banma.thrift.upstream.open.iface.OpenUpstreamPkgThriftIface"/>
        <property name="timeout" value="500"/>&lt;!&ndash; thrift rpc 超时时间（毫秒） &ndash;&gt;
        <property name="appKey" ref="appKey"/>
        <property name="remoteServerPort" value="9280"/>
        <property name="remoteAppkey" value="com.sankuai.banma.package.upstream"/>
        &lt;!&ndash;指定路由策略&ndash;&gt;
        <property name="userDefinedCellPolicy" ref="bmUpsOpenSetRoutePolicy"/>
    </bean>-->
    <!--custom point: no need-->

    <!--custom point: no need-->
    <!--<bean id="openUpstreamZBPkgThriftIface" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="bmUpstreamThriftPoolConfig"/>
        <property name="serviceInterface"  value="com.sankuai.meituan.banma.thrift.upstream.open.iface.OpenUpstreamZBPkgThriftIface"/>
        <property name="timeout" value="500"/>&lt;!&ndash; thrift rpc 超时时间（毫秒） &ndash;&gt;
        <property name="appKey" ref="appKey"/>
        <property name="remoteServerPort" value="9281"/>
        <property name="remoteAppkey" value="com.sankuai.banma.package.upstream"/>
        &lt;!&ndash;指定路由策略&ndash;&gt;
        <property name="userDefinedCellPolicy" ref="bmUpsOpenSetRoutePolicy"/>
    </bean>-->
    <!--custom point: no need-->

    <!--custom point: rename-->
    <bean id="openUpstreamSelfDeliveryOrderThriftClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="bmUpstreamThriftPoolConfig"/>
        <property name="serviceInterface"  value="com.sankuai.meituan.banma.thrift.upstream.open.iface.OpenUpstreamSelfDeliveryOrderThriftIface"/>
        <property name="timeout" value="10000"/><!-- thrift rpc 超时时间（毫秒） -->
        <property name="appKey" ref="appKey"/>
        <property name="remoteServerPort" value="9288"/>
        <property name="remoteAppkey" value="com.sankuai.banma.package.upstream"/>
        <!--指定路由策略-->
        <property name="userDefinedCellPolicy" ref="bmUpsOpenSetRoutePolicy"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>
    <!--custom point: rename-->


</beans>
