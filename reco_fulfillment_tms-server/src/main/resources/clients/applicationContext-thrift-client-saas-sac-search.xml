<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <bean id="saassacsearchThriftPoolConfig"
          class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_client_pool_max-active', '100')}"/>
        <property name="maxIdle"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_client_pool_max-idle', '20')}"/>
        <property name="minIdle"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_client_pool_min-idle', '1')}"/>
        <property name="maxWait"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_client_pool_max-wait', '1000')}"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
        <!--Netty IO默认的连接数为1，即一个client使用一个网络连接与服务端进行通信，当一个连接满足不了需求时，可以在MTthriftPoolConfig中设置Netty连接数大小。-->
        <property name="normalSize"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_client_netty_normalSize', '3')}"/>
        <property name="initialSize"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_client_netty_initialSize', '1')}"/>
    </bean>

    <bean id="sacAccountSearchThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="saassacsearchThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_timeout','1500')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

</beans>
