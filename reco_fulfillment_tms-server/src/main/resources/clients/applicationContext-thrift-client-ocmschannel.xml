<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <bean id="ocmschannelThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ocmschannel_thrift_client_pool_max-active', '100')}"/>
        <property name="maxIdle"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ocmschannel_thrift_client_pool_max-idle', '20')}"/>
        <property name="minIdle"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ocmschannel_thrift_client_pool_min-idle', '1')}"/>
        <property name="maxWait"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ocmschannel_thrift_client_pool_max-wait', '1000')}"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="dapOcmschannelThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('dap_ocmschannel_thrift_client_pool_max-active', '100')}"/>
        <property name="maxIdle"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('dap_ocmschannel_thrift_client_pool_max-idle', '20')}"/>
        <property name="minIdle"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('dap_ocmschannel_thrift_client_pool_min-idle', '1')}"/>
        <property name="maxWait"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('dap_ocmschannel_thrift_client_pool_max-wait', '1000')}"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="maltOcmschannelThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('malt_ocmschannel_thrift_client_pool_max-active', '100')}"/>
        <property name="maxIdle"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('malt_ocmschannel_thrift_client_pool_max-idle', '20')}"/>
        <property name="minIdle"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('malt_ocmschannel_thrift_client_pool_min-idle', '1')}"/>
        <property name="maxWait"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('malt_ocmschannel_thrift_client_pool_max-wait', '1000')}"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="channelDeliveryThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="ocmschannelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelDeliveryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ocmschannel_thrift_timeout','10000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="channelPoiShippingThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="ocmschannelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPoiShippingThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ocmschannel_thrift_timeout','10000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <!-- 渠道订单对接服务 -->
    <bean id="channelOrderDockingThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="ocmschannelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderDockingThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_order_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <!-- 牵牛花平台订单对接服务 -->
    <bean id="qnhOrderDockingThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="ocmschannelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.QnhOrderDockingThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <!-- 三方聚合运力服务 -->
    <bean id="channelAggDeliveryThriftServiceClient"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="ocmschannelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelAggDeliveryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="maltChannelAggDeliveryThriftServiceClient"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="maltOcmschannelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MaltChannelAggDeliveryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('malt_channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="dapChannelAggDeliveryThriftServiceClient"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="dapOcmschannelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapChannelAggDeliveryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('dap_channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="farmPaoTuiInnerThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="ocmschannelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.FarmPaoTuiInnerThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_paotui_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8091"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <!-- 平台配送服务 -->
    <bean id="orderChannelDeliveryThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="ocmschannelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.OrderChannelDeliveryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>


    <bean id="orderPlatformDeliveryOperateThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="ocmschannelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.OrderPlatformDeliveryOperateThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="channelDeliverySyncThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="ocmschannelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelDeliverySyncThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <!-- 渠道配送对接服务 -->
    <bean id="channelDeliveryDockingThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="ocmschannelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelDeliveryDockingThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

</beans>
