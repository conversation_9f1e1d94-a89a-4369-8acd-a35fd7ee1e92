<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <bean id="tenantThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tenant_thrift_client_pool_max-active', '100')}"/>
        <property name="maxIdle"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tenant_thrift_client_pool_max-idle', '20')}"/>
        <property name="minIdle"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tenant_thrift_client_pool_min-idle', '1')}"/>
        <property name="maxWait"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tenant_thrift_client_pool_max-wait', '1000')}"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="channelPoiManageThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tenantThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tenant_thrift_timeout','10000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8868"/>
        <property name="nettyIO" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="poiRelationThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tenantThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.PoiRelationThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8890"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO -->
    </bean>

    <bean id="poiThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tenantThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.PoiThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tenant_thrift_timeout','10000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8849"/>
        <property name="nettyIO" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="configThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tenantThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.ConfigThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tenant_thrift_timeout','10000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8846"/>
        <property name="nettyIO" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="poiManageThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tenantThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.PoiManageThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tenant_thrift_timeout','10000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8856"/>
        <property name="nettyIO" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="tenantThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tenantThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.TenantThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8852"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="poiApprovalManageThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tenantThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.PoiApprovalManageThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8893"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="departmentV2ThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tenantThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.DepartmentV2ThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8892"/>
    </bean>

    <bean id="qnhTenantThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tenantThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.sgfnqnh.poi.api.client.thrift.TenantThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfnqnh.poi.api"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

</beans>
