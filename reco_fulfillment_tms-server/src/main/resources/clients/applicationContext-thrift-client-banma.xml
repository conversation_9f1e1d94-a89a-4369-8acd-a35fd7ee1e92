<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="classpath:clients/banma/banma_open_upstream_set_client_custom.xml"/>
    <import resource="classpath:clients/banma/unified_logistics_accept_platform_set_client_custom.xml"/>
    <import resource="classpath:ldp-set-client.xml"/>

    <bean id="banMaThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="1000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="openBm3PLPoiThriftClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="banMaThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.banma.thrift.delivery3pl.open.platform.iface.BmOpen3PLPoiThriftIface"/>
        <property name="timeout" value="10000"/><!-- thrift rpc 超时时间（毫秒） -->
        <property name="appKey" ref="appKey"/>
        <property name="remoteServerPort" value="9303"/>
        <property name="remoteAppkey" value="com.sankuai.delivery3pl.3pl"/>
        <property name="nettyIO" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="openBmLbsCoordinateThriftClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="banMaThriftPoolConfig"/>
        <property name="serviceInterface"  value="com.sankuai.meituan.banma.thrift.lbs.open.iface.OpenBmLbsCoordinateIface"/>
        <property name="timeout" value="10000"/><!-- thrift rpc 超时时间（毫秒） -->
        <property name="appKey" ref="appKey"/>
        <property name="filterByServiceName" value="true"/>
        <property name="remoteAppkey" value="com.sankuai.deliverylbs.coordinate"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

</beans>
