<?xml version="1.0" encoding="UTF-8"?>
<configuration status="debug">
    <appenders>
        <!--异步磁盘appender，默认按天&按512M文件大小切分日志，默认最多保留30个日志文件，默认为noblocking写日志模式-->
        <XMDFile name="requestLog" fileName="request.log" rolloverMax="5"></XMDFile>

        <!--ERROR日志、WARN日志单独输出到一个文件-->
        <XMDFile name="errorLog" fileName="error.log">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>
        <XMDFile name="warnLog" fileName="warn.log">
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>

        <!--日志远程上报-->
        <Scribe name="ScribeAppender">
            <!-- 在指定日志名方面，scribeCategory 和 appkey 两者至少存在一种，且 scribeCategory 高于 appkey。-->
            <!-- <Property name="scribeCategory">data_update_test_lc</Property> -->
            <LcLayout/>
        </Scribe>
        <Async name="ScribeAsyncAppender" blocking="false">
            <AppenderRef ref="ScribeAppender"/>
        </Async>

        <AsyncScribe name="AsyncScribeAppender_drunk.horse.rider.locating.log" blocking="false">
            <Property name="scribeCategory">drunk.horse.rider.locating.log</Property>
            <LcLayout/>
        </AsyncScribe>

        <AsyncScribe name="AsyncScribeAppender_drunk.horse.rider.location.sync.log" blocking="false">
            <Property name="scribeCategory">drunk.horse.rider.location.sync.log</Property>
            <LcLayout/>
        </AsyncScribe>

        <AsyncScribe name="AsyncScribeAppender_drunk.horse.delivery.capacity.load.log" blocking="false">
            <Property name="scribeCategory">drunk.horse.delivery.capacity.load.log</Property>
            <!-- 如果要开启丢失率检测，请放开下面代码注释 -->
            <!-- <Property name="checkLoss">true</Property> -->
            <LcLayout/>
        </AsyncScribe>

        <AsyncScribe name="AsyncScribeAppender_rider.post.location.log" blocking="false">
            <Property name="scribeCategory">rider.post.location.log</Property>
            <LcLayout/>
        </AsyncScribe>

        <!-- 集成CAT -->
        <CatAppender name="CatAppender" />

    </appenders>

    <loggers>
        <logger name="com.meituan" level="info"/>

        <logger name="org.springframework" level="info"/>

        <!--配置在root logger中 -->
        <logger name="logger_drunk.horse.rider.locating.log" level="info" additivity="false">
            <appender-ref ref="AsyncScribeAppender_drunk.horse.rider.locating.log"/>
        </logger>

        <!--想要上报到org.drunk.horse.rider.location.sync.log这个topic，代码中必须使用 private static final Logger scribelogger = LoggerFactory.getLogger("logger_drunk.horse.rider.location.sync.log"); -->
        <logger name="logger_drunk.horse.rider.location.sync.log" level="info" additivity="false">
            <appender-ref ref="AsyncScribeAppender_drunk.horse.rider.location.sync.log"/>
        </logger>

        <logger name="logger_drunk.horse.delivery.capacity.load.log" level="info" additivity="false">
            <appender-ref ref="AsyncScribeAppender_drunk.horse.delivery.capacity.load.log"/>
        </logger>

        <logger name="logger_rider.post.location.log" level="info" additivity="false">
            <appender-ref ref="AsyncScribeAppender_rider.post.location.log"/>
        </logger>

        <root level="info">
            <appender-ref ref="requestLog"/>
            <appender-ref ref="warnLog"/>
            <appender-ref ref="errorLog"/>
            <appender-ref ref="ScribeAsyncAppender"/>
            <appender-ref ref="CatAppender" />
        </root>
    </loggers>
</configuration>