<?xml version="1.0" encoding="UTF-8" ?>
<dataset>

    <store_config id="1" tenant_id="1000" store_id="1000" contact_phone="123"
                  delivery_strategy="1"
                  delivery_strategy_config="{&quot;orderedDeliveryChannels&quot;: [900], &quot;timeoutForShiftDeliveryChannelInMinutes&quot;: 15}"
                  delivery_launch_point="1" delivery_launch_delay_minutes="0"
                  booking_order_delivery_launch_point="1" booking_order_delivery_launch_minutes="60"
                  order_platform_delivery_config='{"100": {"minOrderPrice": 2000}}'
                  is_auto_launch="1" delivery_launch_rule="1" open_aggr_platform="0" city_code="0" enabled="1"/>

    <store_config id="2" tenant_id="1000" store_id="1001" contact_phone="13812345678"
                  delivery_strategy="1"
                  delivery_launch_point="1" delivery_launch_delay_minutes="0"
                  booking_order_delivery_launch_point="1" booking_order_delivery_launch_minutes="60"
                  is_auto_launch="1" delivery_launch_rule="1" open_aggr_platform="1" city_code="510100" enabled="1"/>

    <store_config id="3" tenant_id="1000" store_id="1002" contact_phone="13812345678"
                  delivery_strategy="1"
                  delivery_launch_point="1" delivery_launch_delay_minutes="0"
                  booking_order_delivery_launch_point="1" booking_order_delivery_launch_minutes="60"
                  is_auto_launch="1" delivery_launch_rule="1" open_aggr_platform="2" city_code="510100" enabled="1"/>

</dataset>
