<?xml version="1.0" encoding="UTF-8" ?>
<dataset>

    <store_config tenant_id="1000" store_id="1000" contact_phone="13812345678"
                  delivery_strategy="1" address='{"latitude": "31.123456", "longitude": "101.123456", "addressDetail": "mock address", "coordinateType": 0}'
                  delivery_strategy_config='{"orderedDeliveryChannels": [800], "timeoutForShiftDeliveryChannelInMinutes": 20}'
                  delivery_launch_point="2" delivery_launch_delay_minutes="10"
                  booking_order_delivery_launch_point="2" booking_order_delivery_launch_minutes="20"
                  order_platform_delivery_config='{"100": {"deliveryFee": 2, "minOrderPrice": 1, "deliveryMinutes": 3}}'
                  is_auto_launch="1" delivery_launch_rule="1" open_aggr_platform="0" city_code="510100" enabled="1"/>

    <store_config tenant_id="1000" store_id="1001" contact_phone="13812345678"
                  delivery_strategy="1"
                  delivery_launch_point="2" delivery_launch_delay_minutes="50"
                  booking_order_delivery_launch_point="2" booking_order_delivery_launch_minutes="60"
                  is_auto_launch="1" delivery_launch_rule="1" open_aggr_platform="2" city_code="510100" enabled="1"/>

    <store_config tenant_id="1000" store_id="1002" contact_phone="13812345678"
                  delivery_strategy="1"
                  delivery_launch_point="2" delivery_launch_delay_minutes="30"
                  booking_order_delivery_launch_point="2" booking_order_delivery_launch_minutes="40"
                  is_auto_launch="2" delivery_launch_rule="2" open_aggr_platform="1" city_code="510100" enabled="1"/>

    <store_config tenant_id="1000" store_id="1003" contact_phone="13812345678"
                  delivery_strategy="1"
                  delivery_launch_point="2" delivery_launch_delay_minutes="70"
                  booking_order_delivery_launch_point="2" booking_order_delivery_launch_minutes="80"
                  is_auto_launch="1" delivery_launch_rule="1" open_aggr_platform="3" city_code="510100" enabled="1"/>

</dataset>
