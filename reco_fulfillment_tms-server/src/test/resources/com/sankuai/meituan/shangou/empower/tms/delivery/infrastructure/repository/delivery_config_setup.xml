<?xml version="1.0" encoding="UTF-8" ?>
<dataset>
    <delivery_config id="1" tenant_id="1000001" delivery_channel_id="900"
                     app_key="TEST_APP_KEY_1000001_900" secret_key="TEST_SECRET_KEY_1000001_900"
                     enabled="1" operator_id="1" operator_name="测试操作员1"
                     create_time="2020-02-27 00:00:00" update_time="2020-02-27 00:00:00" is_del="0" />

    <delivery_config id="2" tenant_id="1000001" delivery_channel_id="800"
                     app_key="TEST_APP_KEY_1000001_800" secret_key="TEST_SECRET_KEY_1000001_800"
                     enabled="1" operator_id="1" operator_name="测试操作员1"
                     create_time="2020-02-27 00:00:00" update_time="2020-02-27 00:00:00" is_del="0" />

    <delivery_config id="3" tenant_id="1000009" delivery_channel_id="900"
                     app_key="TEST_APP_KEY_1000009_900" secret_key="TEST_SECRET_KEY_1000009_900"
                     enabled="0" operator_id="1" operator_name="测试操作员1"
                     create_time="2020-02-27 00:00:00" update_time="2020-02-27 00:00:00" is_del="0" />

    <delivery_config id="4" tenant_id="1000099" delivery_channel_id="900"
                     app_key="TEST_APP_KEY_1000099_900" secret_key="TEST_SECRET_KEY_1000099_900"
                     enabled="0" operator_id="1" operator_name="测试操作员1"
                     create_time="2020-02-27 00:00:00" update_time="2020-02-27 00:00:00" is_del="0" />

    <delivery_config id="5" tenant_id="1000099" delivery_channel_id="800"
                     app_key="TEST_APP_KEY_1000099_800" secret_key="TEST_SECRET_KEY_1000099_800"
                     enabled="1" operator_id="1" operator_name="测试操作员1"
                     create_time="2020-02-27 00:00:00" update_time="2020-02-27 00:00:00" is_del="1" />

</dataset>
