<?xml version="1.0" encoding="UTF-8" ?>
<dataset>

    <!--1000: 有完整的自建运力的记录，查询成功-->
    <store_config tenant_id="1000" store_id="1000" contact_phone="13812345678"
                  delivery_strategy="1"
                  delivery_strategy_config="{&quot;orderedDeliveryChannels&quot;: [900], &quot;timeoutForShiftDeliveryChannelInMinutes&quot;: 15}"
                  delivery_launch_point="1" delivery_launch_delay_minutes="0"
                  booking_order_delivery_launch_point="1" booking_order_delivery_launch_minutes="60"
                  order_platform_delivery_config='{"100": {"minOrderPrice": 2000}}'
                  is_auto_launch="1" delivery_launch_rule="1" open_aggr_platform="0" city_code="510100" enabled="1"/>

    <delivery_config tenant_id="1000" delivery_channel_id="900"
                     app_key="1000_900_key" secret_key="1000_900_sec"
                     enabled="1" operator_id="1" operator_name="测试操作员1" is_del="0"/>

    <shop_delivery_config tenant_id="1000" shop_id="1000" delivery_channel_id="900" delivery_channel_poi_code="1000_900"
                          delivery_channel_poi_name="1000_900测试门店" delivery_service_codes="[&quot;4011&quot;,&quot;4012&quot;]"
                          enabled="1" operator_id="1" operator_name="测试操作员1" is_del="0"/>


    <!--1001: 有聚合运力的记录，查询成功-->
    <store_config tenant_id="1001" store_id="1001" contact_phone="13812345678"
                  delivery_strategy="1"
                  delivery_launch_point="2" delivery_launch_delay_minutes="10"
                  booking_order_delivery_launch_point="2" booking_order_delivery_launch_minutes="40"
                  is_auto_launch="2" delivery_launch_rule="1" open_aggr_platform="1" city_code="510100" enabled="1"/>


    <!--1002: 有麦芽田的记录，查询成功-->
    <store_config tenant_id="1002" store_id="1002" contact_phone="13812345678"
                  delivery_strategy="1"
                  delivery_launch_point="1" delivery_launch_delay_minutes="0"
                  booking_order_delivery_launch_point="1" booking_order_delivery_launch_minutes="60"
                  is_auto_launch="1" delivery_launch_rule="1" open_aggr_platform="2" city_code="510100" enabled="1"/>


    <!--1003: 有商家自配送的记录，查询成功-->
    <store_config tenant_id="1003" store_id="1003" contact_phone="13812345678"
                  delivery_strategy="1"
                  delivery_launch_point="1" delivery_launch_delay_minutes="0"
                  booking_order_delivery_launch_point="1" booking_order_delivery_launch_minutes="40"
                  is_auto_launch="1" delivery_launch_rule="1" open_aggr_platform="3" city_code="510100" enabled="1"/>


    <!--1010: 有自建运力记录，有可用的租户和门店配置，门店设置里面没有排序，触发selfCorrect，查询成功-->
    <store_config tenant_id="1010" store_id="1010" contact_phone="13812345678"
                  delivery_strategy="1"
                  delivery_strategy_config="{&quot;orderedDeliveryChannels&quot;: [900], &quot;timeoutForShiftDeliveryChannelInMinutes&quot;: 15}"
                  delivery_launch_point="1" delivery_launch_delay_minutes="0"
                  booking_order_delivery_launch_point="1" booking_order_delivery_launch_minutes="60"
                  order_platform_delivery_config='{"100": {"minOrderPrice": 2000}}'
                  is_auto_launch="1" delivery_launch_rule="1" open_aggr_platform="0" city_code="510100" enabled="1"/>

    <delivery_config tenant_id="1010" delivery_channel_id="900"
                     app_key="1010_900_key" secret_key="1010_900_sec"
                     enabled="1" operator_id="1" operator_name="测试操作员1" is_del="0"/>

    <delivery_config tenant_id="1010" delivery_channel_id="800"
                     app_key="1010_800_key" secret_key="1010_800_sec"
                     enabled="1" operator_id="1" operator_name="测试操作员1" is_del="0"/>

    <shop_delivery_config tenant_id="1010" shop_id="1010" delivery_channel_id="900" delivery_channel_poi_code="1010_900"
                          delivery_channel_poi_name="1010_900测试门店" delivery_service_codes="[&quot;4011&quot;,&quot;4012&quot;]"
                          enabled="1" operator_id="1" operator_name="测试操作员1" is_del="0"/>

    <shop_delivery_config tenant_id="1010" shop_id="1010" delivery_channel_id="800" delivery_channel_poi_code="1010_800"
                          delivery_channel_poi_name="1010_800测试门店" delivery_service_codes="[&quot;1&quot;]"
                          enabled="1" operator_id="1" operator_name="测试操作员1" is_del="0"/>


    <!--1011: 有自建运力的记录，但是没有可用的租户和门店配置，查询失败-->
    <store_config tenant_id="1011" store_id="1011" contact_phone="13812345678"
                  delivery_strategy="1"
                  delivery_strategy_config="{&quot;orderedDeliveryChannels&quot;: [900], &quot;timeoutForShiftDeliveryChannelInMinutes&quot;: 15}"
                  delivery_launch_point="1" delivery_launch_delay_minutes="0"
                  booking_order_delivery_launch_point="1" booking_order_delivery_launch_minutes="60"
                  order_platform_delivery_config='{"100": {"minOrderPrice": 2000}}'
                  is_auto_launch="1" delivery_launch_rule="1" open_aggr_platform="0" city_code="510100" enabled="1"/>


    <!--1012: 有自建运力的记录，有可用的租户配置，但是没有可用的门店记录，查询失败-->
    <store_config tenant_id="1012" store_id="1012" contact_phone="13812345678"
                  delivery_strategy="1"
                  delivery_strategy_config="{&quot;orderedDeliveryChannels&quot;: [900], &quot;timeoutForShiftDeliveryChannelInMinutes&quot;: 15}"
                  delivery_launch_point="1" delivery_launch_delay_minutes="0"
                  booking_order_delivery_launch_point="1" booking_order_delivery_launch_minutes="60"
                  order_platform_delivery_config='{"100": {"minOrderPrice": 2000}}'
                  is_auto_launch="1" delivery_launch_rule="1" open_aggr_platform="0" city_code="510100" enabled="1"/>

    <delivery_config tenant_id="1012" delivery_channel_id="900"
                     app_key="1012_900_key" secret_key="1012_900_sec"
                     enabled="1" operator_id="1" operator_name="测试操作员1" is_del="0"/>


    <!--1013: 没有自建运力的记录，有可用的租户配置，没有可用的门店配置，查询失败-->
    <delivery_config tenant_id="1013" delivery_channel_id="900"
                     app_key="1013_900_key" secret_key="1013_900_sec"
                     enabled="1" operator_id="1" operator_name="测试操作员1" is_del="0"/>


    <!--1014: 没有自建运力的记录，有可用的租户和门店配置，补创建对应自建运力记录，查询成功-->
    <delivery_config tenant_id="1014" delivery_channel_id="900"
                     app_key="1014_900_key" secret_key="1014_900_sec"
                     enabled="1" operator_id="1" operator_name="测试操作员1" is_del="0"/>

    <shop_delivery_config tenant_id="1014" shop_id="1014" delivery_channel_id="900" delivery_channel_poi_code="1014_900"
                          delivery_channel_poi_name="1014_900测试门店" delivery_service_codes="[&quot;4011&quot;,&quot;4012&quot;]"
                          enabled="1" operator_id="1" operator_name="测试操作员1" is_del="0"/>

</dataset>
