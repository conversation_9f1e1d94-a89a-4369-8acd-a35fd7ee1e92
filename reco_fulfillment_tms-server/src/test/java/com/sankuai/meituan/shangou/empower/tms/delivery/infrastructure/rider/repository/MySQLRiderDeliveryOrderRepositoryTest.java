package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.rider.repository;

import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import com.github.springtestdbunit.assertion.DatabaseAssertionMode;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.DbTestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PageRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PageResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.util.Assert;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Slf4j
public class MySQLRiderDeliveryOrderRepositoryTest extends DbTestBase {

    @Resource
    private RiderDeliveryOrderRepository riderDeliveryOrderRepository;


    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void testCountDeliveryOrder_assigned() {
        long storeId = 4988512L;
        List<DeliveryStatusEnum> statusEnumList = Arrays.asList(DeliveryStatusEnum.RIDER_ASSIGNED);
        long accountId = 46675L;
        long count = riderDeliveryOrderRepository.countDeliveryOrderWithTenantId(storeId, statusEnumList, accountId, 0L,0L);
        log.info("-----------------------------------------");
        log.info("testCountDeliveryOrder_assigned count={}", count);
        log.info("-----------------------------------------");
        Assert.isTrue(count == 1);
    }

    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void testCountDeliveryOrder_take() {
        long storeId = 4988512L;
        List<DeliveryStatusEnum> statusEnumList = Arrays.asList(DeliveryStatusEnum.RIDER_TAKEN_GOODS);
        long accountId = 46675L;
        long count = riderDeliveryOrderRepository.countDeliveryOrderWithTenantId(storeId, statusEnumList, accountId, 0L,0L);
        log.info("-----------------------------------------");
        log.info("testCountDeliveryOrder_take count={}", count);
        log.info("-----------------------------------------");
        Assert.isTrue(count == 1);
    }

    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void testCountDeliveryOrder_assigned_and_take() {
        long storeId = 4988512L;
        List<DeliveryStatusEnum> statusEnumList = Arrays.asList(DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_TAKEN_GOODS);
        long accountId = 46675L;
        long count = riderDeliveryOrderRepository.countDeliveryOrderWithTenantId(storeId, statusEnumList, accountId, 0L,0L);
        log.info("-----------------------------------------");
        log.info("testCountDeliveryOrder_assigned_and_take count={}", count);
        log.info("-----------------------------------------");
        Assert.isTrue(count == 2);
    }

    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void testCountDeliveryOrder_status_null() {
        long storeId = 4988512L;
        List<DeliveryStatusEnum> statusEnumList = null;
        long accountId = 46675L;
        long count = riderDeliveryOrderRepository.countDeliveryOrderWithTenantId(storeId, statusEnumList, accountId, 0L,0L);
        log.info("-----------------------------------------");
        log.info("testCountDeliveryOrder_status_null count={}", count);
        log.info("-----------------------------------------");
        Assert.isTrue(count == 0);
    }

    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void testPageQueryRiderDeliveryOrders_take() {
        long storeId = 4988512L;
        List<DeliveryStatusEnum> statusEnumList = Arrays.asList(DeliveryStatusEnum.RIDER_TAKEN_GOODS);
        long accountId = 46675L;
        PageRequest pageRequest = new PageRequest(1, 10);
        PageResult<RiderDeliveryOrder> result = riderDeliveryOrderRepository.pageQueryRiderDeliveryOrdersWithTenantId(pageRequest, storeId, statusEnumList, accountId, 0L,0L);
        log.info("-----------------------------------------");
        log.info("testPageQueryRiderDeliveryOrders_take result={}", result);
        log.info("-----------------------------------------");
        Assert.isTrue(result.getTotal() == 1);
        Assert.notEmpty(result.getInfo());
        Assert.isTrue(result.getInfo().size() == 1);
        Assert.isTrue(result.getInfo().get(0).getId() == 4);
    }

    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void testPageQueryRiderDeliveryOrders_assigned() {
        long storeId = 4988512L;
        List<DeliveryStatusEnum> statusEnumList = Arrays.asList(DeliveryStatusEnum.RIDER_ASSIGNED);
        long accountId = 46675L;
        PageRequest pageRequest = new PageRequest(1, 10);
        PageResult<RiderDeliveryOrder> result = riderDeliveryOrderRepository.pageQueryRiderDeliveryOrdersWithTenantId(pageRequest, storeId, statusEnumList, accountId, 0L,0L);
        log.info("-----------------------------------------");
        log.info("testPageQueryRiderDeliveryOrders_assigned result={}", result);
        log.info("-----------------------------------------");
        Assert.isTrue(result.getTotal() == 1);
        Assert.notEmpty(result.getInfo());
        Assert.isTrue(result.getInfo().size() == 1);
        Assert.isTrue(result.getInfo().get(0).getId() == 1);
    }

    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void testPageQueryRiderDeliveryOrders_assigned_and_take() {
        long storeId = 4988512L;
        List<DeliveryStatusEnum> statusEnumList = Arrays.asList(DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_TAKEN_GOODS);
        long accountId = 46675L;
        PageRequest pageRequest = new PageRequest(1, 10);
        PageResult<RiderDeliveryOrder> result = riderDeliveryOrderRepository.pageQueryRiderDeliveryOrdersWithTenantId(pageRequest, storeId, statusEnumList, accountId, 0L,0L);
        log.info("-----------------------------------------");
        log.info("testPageQueryRiderDeliveryOrders_assigned_and_take result={}", result);
        log.info("-----------------------------------------");
        Assert.isTrue(result.getTotal() == 2);
        Assert.notEmpty(result.getInfo());
        Assert.isTrue(result.getInfo().size() == 2);
        Assert.isTrue(result.getInfo().get(0).getId().longValue() != result.getInfo().get(1).getId().longValue());
        Assert.isTrue(result.getInfo().get(0).getId() == 1 || result.getInfo().get(1).getId() == 1);
        Assert.isTrue(result.getInfo().get(0).getId() == 4 || result.getInfo().get(1).getId() == 4);
    }

    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void testPageQueryRiderDeliveryOrders_status_null() {
        long storeId = 4988512L;
        List<DeliveryStatusEnum> statusEnumList = null;
        long accountId = 46675L;
        PageRequest pageRequest = new PageRequest(1, 10);
        PageResult<RiderDeliveryOrder> result = riderDeliveryOrderRepository.pageQueryRiderDeliveryOrdersWithTenantId(pageRequest, storeId, statusEnumList, accountId, 0L,0L);
        log.info("-----------------------------------------");
        log.info("testPageQueryRiderDeliveryOrders_status_null result={}", result);
        log.info("-----------------------------------------");
        Assert.isTrue(result.getTotal() == 0);
        Assert.isTrue(CollectionUtils.isEmpty(result.getInfo()));
    }

    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void countNotActiveDeliveryOrder() {
        long countDeliveryOrder = riderDeliveryOrderRepository.countDeliveryOrderWithTenantId(8000001L, Lists.newArrayList(DeliveryStatusEnum.DELIVERY_CANCELLED), 111L, 0L,0L);
        Assert.isTrue(countDeliveryOrder == 0L);
    }

    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void countNoneDeliveryOrder() {
        long countDeliveryOrder = riderDeliveryOrderRepository.countDeliveryOrderWithTenantId(8000001L, Lists.newArrayList(), 111L, 0L,0L);
        Assert.isTrue(countDeliveryOrder == 0L);
    }

    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void countDeliveryOrder() {
        long countDeliveryOrder = riderDeliveryOrderRepository.countDeliveryOrderWithTenantId(8000001L, Lists.newArrayList(DeliveryStatusEnum.RIDER_ASSIGNED), 111L, 0L,0L);
        Assert.isTrue(countDeliveryOrder == 1L);
    }

    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void queryActiveRiderOrder() {
        List<RiderDeliveryOrder> result = riderDeliveryOrderRepository.batchQueryDeliveryOrder(
                8000001L,
                Arrays.asList(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_TAKEN_GOODS),
                0L, 200,1L);
        log.info("-----------------------------------------");
        log.info("testPageQueryRiderDeliveryOrders_query_active_rider_order result={}", result);
        log.info("-----------------------------------------");
        Assert.isTrue(!result.isEmpty());
    }


    @Test
    @Transactional
    @DatabaseSetup("./dh_rider_delivery_order_setup.xml")
    @ExpectedDatabase(value = "./rider_delivery_order_status_lock_expect.xml", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void lockDeliveryStatus() {
        Optional<RiderDeliveryOrder> order = riderDeliveryOrderRepository.getActiveDeliveryOrderForceMaster(1000395L, 1023770L, 3333L);
        RiderDeliveryOrder riderDeliveryOrder = order.get();
        riderDeliveryOrder.lockDeliveryStatus(riderDeliveryOrder.getRiderInfo());

        order = riderDeliveryOrderRepository.getActiveDeliveryOrderForceMaster(1000395L, 1023770L, 3333L);
        //这里锁定时间字段是动态实时生成的，所以这里不便于放到rider_delivery_order_status_lock_expect.xml中去比较
        org.junit.Assert.assertNotNull(order.get().getCurrentLockTime());
        org.junit.Assert.assertNull(order.get().getCurrentUnlockTime());
        System.out.println(JacksonUtils.toJson(order.get()));
    }

    @Test
    @Transactional
    @DatabaseSetup("./dh_rider_delivery_order_setup.xml")
    @ExpectedDatabase(value = "./rider_delivery_order_status_unlock_expect.xml", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void unlockDeliveryStatus() {
        Optional<RiderDeliveryOrder> order = riderDeliveryOrderRepository.getActiveDeliveryOrderForceMaster(1000395L, 1023770L, 4444L);
        RiderDeliveryOrder riderDeliveryOrder = order.get();
        riderDeliveryOrder.unlockDeliveryStatus(riderDeliveryOrder.getRiderInfo());

        order = riderDeliveryOrderRepository.getActiveDeliveryOrderForceMaster(1000395L, 1023770L, 4444L);
        //这里锁定时间字段是动态实时生成的，所以这里不便于放到rider_delivery_order_status_lock_expect.xml中去比较
        org.junit.Assert.assertNotNull(order.get().getCurrentUnlockTime());
        System.out.println(JacksonUtils.toJson(order.get()));
    }

    @Test
    @Transactional
    @DatabaseSetup("./dh_rider_delivery_order_setup.xml")
    @ExpectedDatabase(value = "./dh_rider_delivery_order_setup.xml", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void countRiderDeliveryOrder() {
        List<DeliveryStatusEnum> fulfilStatusList = Lists.list(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER,
                DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_TAKEN_GOODS);

        long count  = riderDeliveryOrderRepository.countRiderDeliveryOrder(1000395L,
                fulfilStatusList, LocalDateTime.now().minusYears(100), LocalDateTime.now().plusYears(100), Collections.singletonList(1023770L), null);
        Assert.isTrue(count == 2);
        System.out.println("result:" + JacksonUtils.toJson(count));
    }

    @Test
    @Transactional
    @DatabaseSetup("./dh_rider_delivery_order_setup.xml")
    @ExpectedDatabase(value = "./dh_rider_delivery_order_setup.xml", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void queryRiderDeliveryOrder() {
        List<DeliveryStatusEnum> fulfilStatusList = Lists.list(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER,
                DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_TAKEN_GOODS);

        List<RiderDeliveryOrder> orderList  = riderDeliveryOrderRepository.queryRiderDeliveryOrderByPage(new PageRequest(2, 1), 1000395L,
                fulfilStatusList, LocalDateTime.now().minusYears(100), LocalDateTime.now().plusYears(100), Collections.singletonList(1023770L), null);
        Assert.isTrue(orderList.size() == 1);
    }

    @Test
    @Transactional
    @DatabaseSetup("./dh_delivery_order_add_ext_info.xml")
    @ExpectedDatabase(value = "./dh_delivery_order_add_ext_info.xml", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void query() {
        Optional<RiderDeliveryOrder> order = riderDeliveryOrderRepository.getActiveDeliveryOrderForceMaster(1000395L, 1023770L, 4444L);
        System.out.println("result:" + JacksonUtils.toJson(order.get()));
    }

}

