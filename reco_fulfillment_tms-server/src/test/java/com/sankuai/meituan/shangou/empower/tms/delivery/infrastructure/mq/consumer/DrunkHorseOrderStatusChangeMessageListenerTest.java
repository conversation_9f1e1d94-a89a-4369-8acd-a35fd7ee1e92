package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.StartAppTest;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderExtInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.order.OrderSystemClientImpl;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository.MySQLDeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository.StoreConfigOpLogConverter;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023-08-02
 * @email <EMAIL>
 */
@SpringBootTest
@Import({DrunkHorseOrderStatusChangeMessageListener.class, MySQLDeliveryPoiRepository.class})
public class DrunkHorseOrderStatusChangeMessageListenerTest {

    @Resource
    private DrunkHorseOrderStatusChangeMessageListener listener;

    @MockBean
    private OrderSystemClientImpl orderSystemClient;

    @Before
    public void setUp() {
        when(orderSystemClient.getOrderInfo(any(OrderKey.class), anyBoolean())).thenReturn(
                new Result<>(
                        new OrderInfo(
                                new OrderKey(1001197L, 49867245L, 1L), "1", 1L,"1-01",
                                1, 1, 1, false, 1, null, null, null,false,
                                1,1 ,null, "", "", 1, null,
                                false, "","",1, null, null, null, 1L,
                                1, "", null, null, 1L, null, Lists.newArrayList(),null,null,
                                null,null,null,null,null,null,null,null,null,null,
                                null,null,null,null,null,null,null)
                )
        );
    }

    @Test
    public void consumeTest() {
        OrderStatusChangeMessageListener.MessageDTO message = new OrderStatusChangeMessageListener.MessageDTO();
        message.setTenantId(1001197L);
        message.setShopId(1L);
        message.setOrderId(1L);
        message.setOrderSource(10);
        message.setStatus(OrderStatusEnum.SUBMIT.getValue());
        message.setSourceStatus(1);
        message.setWarehouseId(1L);
        message.setCompensate(false);
        message.setOnlineDistributeStatus(30);
        message.setDelayPush(false);

        Map<String, Object> map = new HashMap<>();
        map.put("body", JSON.toJSONString(message));
        MafkaMessage mafkaMessage = new MafkaMessage(
                "", 1, 1, null, JSON.toJSONString(map)
        );
        listener.consume(mafkaMessage);
    }


}
