package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.meituan.linz.boot.util.TimeUtils;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.reco.pickselect.common.utils.TimeFormatUtils;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.PageQueryDeliveryExceptionRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryDeliveryExceptionByChannelOrderRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryTimeoutDeliveryOrderNumRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.StoreDeliveryMonitorRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.*;
import com.sankuai.meituan.shangou.empower.tms.StartApp;
import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.TimeUtil;
import org.junit.Test;
import org.junit.platform.commons.util.CollectionUtils;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {StartApp.class})
@ActiveProfiles({"test"})
public class RiderQueryThriftServiceImplTest {
    @Resource
    public RiderQueryThriftService riderQueryThriftService;

    @Test
    public void storeDeliveryMonitor() {
        StoreDeliveryMonitorRequest request = new StoreDeliveryMonitorRequest();
        request.setAppId(3);
        request.setStoreId(49867245L);
        request.setTenantId(1001197L);
        StoreDeliveryMonitorResponse response = riderQueryThriftService.storeDeliveryMonitor(request);
        System.out.println(JacksonUtils.toJson(response));
    }

    @Test
    public void pageQueryDeliveryExceptionList() {
        PageQueryDeliveryExceptionRequest request = new PageQueryDeliveryExceptionRequest();
        request.setPageNum(1);
        request.setPageSize(20);
        request.setTenantId(1001197L);
        request.setStoreIds(Collections.singletonList(49867245L));
        request.setPayTimeStartTimeStamp(TimeUtil.toMilliSeconds(LocalDateTime.now().minusMonths(3)));
        request.setPayTimeEndTimeStamp(TimeUtil.toMilliSeconds(LocalDateTime.now()));
        request.setOrderBizTypeList(Collections.singletonList(101));
        request.setExceptionTypeList(Collections.singletonList(1));
        PageQueryDeliveryExceptionListResponse response = riderQueryThriftService.pageQueryDeliveryExceptionList(request);
        System.out.println(response);
    }

    @Test
    public void pageQueryDeliveryExceptionList2() {
        PageQueryDeliveryExceptionRequest request = new PageQueryDeliveryExceptionRequest();
        request.setPageNum(1);
        request.setPageSize(20);
        request.setTenantId(1001197L);
        request.setStoreIds(Arrays.asList(49867236L,49867260L,49869339L,49869158L,49867245L,49868326L,49867488L,49867628L,49867284L));
        request.setPayTimeStartTimeStamp(1651334400000L);
        request.setPayTimeEndTimeStamp(1657295999999L);
        request.setReportTimeStartTimeStamp(1656604800000L);
        request.setReportTimeEndTimeStamp(1657295999999L);
        request.setExceptionTypeList(Collections.singletonList(1));
        PageQueryDeliveryExceptionListResponse response = riderQueryThriftService.pageQueryDeliveryExceptionList(request);
        System.out.println(response);
    }

    @Test
    public void queryDeliveryExceptionByChannelOrder() {
        QueryDeliveryExceptionByChannelOrderRequest request = new QueryDeliveryExceptionByChannelOrderRequest();
        request.setStoreId(49867245L);
        request.setChannelOrderInfoList(Collections.singletonList(new QueryDeliveryExceptionByChannelOrderRequest.ChannelOrderInfo("" + 276268152280292569L, 103)));
        request.setTenantId(1001197L);
        DeliveryExceptionResponse response = riderQueryThriftService.queryDeliveryExceptionByChannelOrder(request);

        System.out.println(JsonUtil.toJson(response));
    }



    @Test
    public void queryRiderArrivalLocationInfo() {
        QueryRiderArrivalLocationResponse response = riderQueryThriftService.queryRiderArrivalLocationInfo(1001197L, 49868326L, 1534102992955908109L);
        System.out.println(JacksonUtils.toJson(response));
    }

    @Test
    public void queryDeliveryTimeoutNum() {
        QueryTimeoutDeliveryOrderNumRequest request = new QueryTimeoutDeliveryOrderNumRequest();
        request.setTenantId(1000078l);
        request.setStoreId(49864215l);
        request.setRiderAccountId(47850l);
        List<Integer> deliveryStatusList = new ArrayList<>();
        deliveryStatusList.add(30);
        deliveryStatusList.add(40);
        deliveryStatusList.add(50);
        request.setDeliveryStatusList(deliveryStatusList);
        QueryTimeoutDeliveryOrderNumResponse queryTimeoutDeliveryOrderNumResponse = riderQueryThriftService.queryTimeoutDeliveryOrderNum(request);
        int count  = queryTimeoutDeliveryOrderNumResponse.getDeliveryStatusCountMap().values().stream().mapToInt(Integer::intValue).sum();
        System.out.println(count);
    }
}
