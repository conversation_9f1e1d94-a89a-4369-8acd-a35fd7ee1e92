package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;


import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import com.github.springtestdbunit.assertion.DatabaseAssertionMode;
import com.sankuai.meituan.shangou.empower.tms.DbTestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ChannelTenantConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.DeliveryTenant;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.DeliveryTenantRepository;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/17
 */
public class MySQLDeliveryTenantRepositoryTest extends DbTestBase {

	@Resource
	private DeliveryTenantRepository deliveryTenantRepository;

	@Test
	@Transactional
	@DatabaseSetup("./delivery_config_setup.xml")
	public void should_getValidDeliveryTenant_success() {
		DeliveryTenant deliveryTenant = deliveryTenantRepository.getValidDeliveryTenant(1000001L);
		assertNotNull(deliveryTenant);
		assertEquals(2, deliveryTenant.getChannelTenantConfigMap().size());
		assertEquals("TEST_APP_KEY_1000001_900", deliveryTenant.getChannelTenantConfigMap().get(ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY).getAppKey());
		assertEquals("TEST_SECRET_KEY_1000001_900", deliveryTenant.getChannelTenantConfigMap().get(ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY).getSecretKey());
		assertEquals("TEST_APP_KEY_1000001_800", deliveryTenant.getChannelTenantConfigMap().get(ThirdDeliveryChannelEnum.FENG_NIAO_DELIVERY).getAppKey());
		assertEquals("TEST_SECRET_KEY_1000001_800", deliveryTenant.getChannelTenantConfigMap().get(ThirdDeliveryChannelEnum.FENG_NIAO_DELIVERY).getSecretKey());
	}

	@Test
	public void should_getValidDeliveryTenant_fail_with_no_valid_channel_config() {
		assertNull(deliveryTenantRepository.getValidDeliveryTenant(1000099L));
		assertNull(deliveryTenantRepository.getValidDeliveryTenant(10000999999L));
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_config_setup.xml")
	public void should_getTenantIdByChannelAndAppKey_success() {
		Long tenantId = deliveryTenantRepository.getTenantIdByChannelAndAppKey(ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY, "TEST_APP_KEY_1000001_900");
		assertEquals(1000001L, tenantId.longValue());
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_config_setup.xml")
	public void should_getTenantIdByChannelAndAppKey_fail_with_no_record_match() {
		assertNull(deliveryTenantRepository.getTenantIdByChannelAndAppKey(ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY, "mock_app_key"));
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_config_setup.xml")
	public void should_getChannelTenants_success() {
		Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> map = deliveryTenantRepository.getChannelTenants(1000001L, true);
		assertEquals(2, map.size());
		assertNotNull(map.get(ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY));
		assertNotNull(map.get(ThirdDeliveryChannelEnum.FENG_NIAO_DELIVERY));
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_config_setup.xml")
	public void should_getChannelTenants_success_with_disabled_records() {
		Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> map = deliveryTenantRepository.getChannelTenants(1000099L, false);
		assertEquals(1, map.size());
		assertFalse(map.get(ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY).isEnabled());
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_config_setup.xml")
	public void should_getDeliveryChannelTenant_success() {
		ChannelTenantConfig channelTenant = deliveryTenantRepository.getDeliveryChannelTenant(1000001L, ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY, true);
		assertNotNull(channelTenant);
		assertEquals("TEST_APP_KEY_1000001_900", channelTenant.getAppKey());
		assertEquals("TEST_SECRET_KEY_1000001_900", channelTenant.getSecretKey());
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_config_setup.xml")
	public void should_getDeliveryChannelTenant_success_with_disabled_records() {
		ChannelTenantConfig channelTenant = deliveryTenantRepository.getDeliveryChannelTenant(1000099L, ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY, false);
		assertNotNull(channelTenant);
		assertEquals("TEST_APP_KEY_1000099_900", channelTenant.getAppKey());
		assertEquals("TEST_SECRET_KEY_1000099_900", channelTenant.getSecretKey());
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_config_save_channel_tenants_setup.xml")
	@ExpectedDatabase(value = "./delivery_config_save_channel_tenants_expect.xml", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
	public void should_saveChannelTenants_success() {
		List<ChannelTenantConfig> channelTenantList = new ArrayList<>();
		channelTenantList.add(new ChannelTenantConfig(3L, ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY, "MOCK_KEY_1", "MOCK_SECRET_1", true));
		channelTenantList.add(new ChannelTenantConfig(null, ThirdDeliveryChannelEnum.FENG_NIAO_DELIVERY, "MOCK_KEY_2", "MOCK_SECRET_2", true));
		deliveryTenantRepository.saveChannelTenants(1000009L, channelTenantList, new Operator(14L, "hedong07"));
	}
}
