package com.sankuai.meituan.shangou.empower.tms;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Log4j2DiscardPolicyTest {

    private static final Logger logger = LoggerFactory.getLogger("request.log");

    public static void main(String[] args) throws InterruptedException {
        for (int i = 0; i < 150; i++) {
            new Thread(new LogPrinter("p" + i)).start();
        }
        Thread.sleep(5000);
    }

    static class LogPrinter implements Runnable {
        private String name;

        public LogPrinter(String name) {
            this.name = name;
        }

        public void run() {
            int max = 100;
            for (int i = 0; i < max; i++) {
                logger.info("info - name: {}, index: {}", name, padding(i, max));
                logger.error("error - name: {}, index: {}", name, padding(i, max));
            }
        }

        private StringBuilder padding(int val, int max) {
            StringBuilder sb = new StringBuilder();
            while ((max = max / 10) > val && max > 1) {
                sb.append("0");
            }
            return sb.append(val);
        }
    }
}
