package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryCallbackTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import org.junit.Test;

import java.time.LocalDateTime;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/15
 */
public class DeliveryUnifiedCallbackMessageTest {

	@Test
	public void should_serialize_and_deserialize_DeliveryLaunchCallbackInfo_success() {
		DeliveryUnifiedCallbackMessage message = JsonUtil.fromJson(
				JsonUtil.toJson(
						new DeliveryUnifiedCallbackMessage(
								DeliveryCallbackTypeEnum.LAUNCH_CALLBACK,
								new DeliveryLaunchCallbackInfo(
										123L,
										456L,
										true,
										10002,
										"2107",
										"channelDeliveryId",
										1234L,
										3.25D,
										"fail reason"
								)
						)
				),
				DeliveryUnifiedCallbackMessage.class
		);

		assertNotNull(message);
		assertEquals(DeliveryCallbackTypeEnum.LAUNCH_CALLBACK.getCode(), message.getType().intValue());
		assertEquals("{\"storeId\":123,\"orderId\":456,\"launchSuccess\":true,\"deliveryChannelCode\":10002,\"servicePackage\":\"2107\",\"channelDeliveryId\":\"channelDeliveryId\",\"distance\":1234,\"deliveryFee\":3.25,\"launchFailReason\":\"fail reason\"}", JsonUtil.toJson(JsonUtil.fromJson(message.getInfo(), DeliveryLaunchCallbackInfo.class)));
	}

	@Test
	public void should_serialize_and_deserialize_DeliveryCancelCallbackInfo_success() {
		DeliveryUnifiedCallbackMessage message = JsonUtil.fromJson(
				JsonUtil.toJson(
						new DeliveryUnifiedCallbackMessage(
								DeliveryCallbackTypeEnum.CANCEL_CALLBACK,
								new DeliveryCancelCallbackInfo(
										123L,
										456L,
										"channelDeliveryId",
										true,
										"fail reason"
								)
						)
				),
				DeliveryUnifiedCallbackMessage.class
		);

		assertNotNull(message);
		assertEquals(DeliveryCallbackTypeEnum.CANCEL_CALLBACK.getCode(), message.getType().intValue());
		assertEquals("{\"storeId\":123,\"orderId\":456,\"channelDeliveryId\":\"channelDeliveryId\",\"cancelSuccess\":true,\"cancelFailReason\":\"fail reason\"}", JsonUtil.toJson(JsonUtil.fromJson(message.getInfo(), DeliveryCancelCallbackInfo.class)));
	}

	@Test
	public void should_serialize_and_deserialize_DeliveryChangeCallbackInfo_success() {
		DeliveryUnifiedCallbackMessage message = JsonUtil.fromJson(
				JsonUtil.toJson(
						new DeliveryUnifiedCallbackMessage(
								DeliveryCallbackTypeEnum.CHANGE_CALLBACK,
								new DeliveryChangeCallbackInfo(
										456L,
										DeliveryChannelEnum.AGGREGATE_DELIVERY_DADA.getCode(),
										"channelDeliveryId",
										"servicePackage",
										DeliveryEventEnum.RIDER_START_DELIVERY,
										new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER, "exception", 1),
										LocalDateTime.of(2021,1,1, 0, 0, 0, 0),
										new Rider("riderName", "riderPhone", "riderPhoneToken"),
										1234L,
										3.25D
								)
						)
				),
				DeliveryUnifiedCallbackMessage.class
		);

		assertNotNull(message);
		assertEquals(DeliveryCallbackTypeEnum.CHANGE_CALLBACK.getCode(), message.getType().intValue());
		assertEquals("{\"orderId\":456,\"deliveryChannel\":10002,\"channelDeliveryId\":\"channelDeliveryId\",\"servicePackage\":\"servicePackage\",\"deliveryEvent\":40,\"exceptionType\":5,\"exceptionDescription\":\"exception\",\"exceptionCode\":1,\"riderName\":\"riderName\",\"riderPhone\":\"riderPhone\",\"distance\":1234,\"deliveryFee\":3.25,\"changeTimeInMillis\":1609430400000}", JsonUtil.toJson(JsonUtil.fromJson(message.getInfo(), DeliveryChangeCallbackInfo.class)));
	}

	@Test
	public void should_serialize_and_deserialize_DeliveryExceptionCallbackInfo_success() {
		DeliveryUnifiedCallbackMessage message = JsonUtil.fromJson(
				JsonUtil.toJson(
						new DeliveryUnifiedCallbackMessage(
								DeliveryCallbackTypeEnum.EXCEPTION_CALLBACK,
								new DeliveryExceptionCallbackInfo(
										456L,
										new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER, "exception", 1),
										LocalDateTime.of(2021,1,1, 0, 0, 0, 0)
								)
						)
				),
				DeliveryUnifiedCallbackMessage.class
		);

		assertNotNull(message);
		assertEquals(DeliveryCallbackTypeEnum.EXCEPTION_CALLBACK.getCode(), message.getType().intValue());
		assertEquals("{\"orderId\":456,\"exceptionType\":5,\"exceptionCode\":1,\"exceptionDescription\":\"exception\",\"exceptionTimeInMillis\":1609430400000}", JsonUtil.toJson(JsonUtil.fromJson(message.getInfo(), DeliveryExceptionCallbackInfo.class)));
	}
}
