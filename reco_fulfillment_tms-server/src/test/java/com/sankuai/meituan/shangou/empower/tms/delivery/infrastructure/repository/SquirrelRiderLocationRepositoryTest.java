package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationDetail;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@ActiveProfiles("test")
@RunWith(SpringRunner.class)
//@SpringBootTest(classes={StartAppTest.class})
@Slf4j
public class SquirrelRiderLocationRepositoryTest {

    @Resource
    SquirrelRiderLocationRepository riderLocationRepository;

    @Test
    public void getRiderLocations() {
        List<Long> riderAccounts = Arrays.asList(62835L,63703L);
        Map<Long, RiderLocationDetail> result = riderLocationRepository.getStaffRiderLocations(riderAccounts);
        log.info("-----------------------------------------");
        log.info("testSquirrelRiderLocationRepository_get_rider_locations result={}", JsonUtil.toJson(result));
        log.info("-----------------------------------------");

        System.out.println(JsonUtil.toJson(result));
    }

    @Test
    public void getRiderLocation() {
        Long riderAccount = 62835L;
        RiderLocationDetail result = riderLocationRepository.getStaffRiderLocation(riderAccount);
        log.info("-----------------------------------------");
        log.info("testSquirrelRiderLocationRepository_get_rider_locations result={}", JsonUtil.toJson(result));
        log.info("-----------------------------------------");

        System.out.println(JsonUtil.toJson(result));
    }


}
