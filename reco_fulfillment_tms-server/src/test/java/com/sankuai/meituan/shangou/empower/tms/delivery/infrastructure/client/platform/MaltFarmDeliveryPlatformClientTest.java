package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform;

import com.google.common.collect.ImmutableList;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.CreateAggDeliveryShopResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelAggDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryBaseException;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryPlatformException;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.ChannelStoreQueryResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantChannelStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import org.apache.thrift.TException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.context.ActiveProfiles;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/2/9
 */
@RunWith(MockitoJUnitRunner.class)
@ActiveProfiles("dev")
public class MaltFarmDeliveryPlatformClientTest {

    @InjectMocks
    private MaltFarmDeliveryPlatformClient maltFarmDeliveryPlatformClient;

    @Mock
    private TenantSystemClient tenantSystemClient;

    @Mock
    private ChannelAggDeliveryThriftService channelAggDeliveryThriftService;


    @Test(expected = DeliveryPlatformException.class)
    public void testCreateShopShouldThrowException() throws TException {
        DeliveryPoi deliveryPoi = mock(DeliveryPoi.class);
        when(deliveryPoi.getDeliveryPlatform()).thenReturn(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM);
        when(tenantSystemClient.queryChannelStoreDetailInfo(any(), any())).thenReturn(prepareChannelStoreQueryResult());
        when(channelAggDeliveryThriftService.createAggDeliveryShop(any())).thenThrow(new DeliveryBaseException("CREATE_DELIVERY_SHOP_FAIL"));
        maltFarmDeliveryPlatformClient.createShop(deliveryPoi);

    }

    @Test(expected = DeliveryPlatformException.class)
    public void testCreateShopShouldThrowExceptionWhenDirectResponseError() throws TException {
        DeliveryPoi deliveryPoi = mock(DeliveryPoi.class);
        when(deliveryPoi.getDeliveryPlatform()).thenReturn(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM);
        when(tenantSystemClient.queryChannelStoreDetailInfo(any(), any())).thenReturn(prepareChannelStoreQueryResult());
        CreateAggDeliveryShopResponse createAggDeliveryShopResponse = new CreateAggDeliveryShopResponse();
        createAggDeliveryShopResponse.setCode(1);
        when(channelAggDeliveryThriftService.createAggDeliveryShop(any())).thenThrow(new DeliveryBaseException("CREATE_DELIVERY_SHOP_FAIL"));
        maltFarmDeliveryPlatformClient.createShop(deliveryPoi);

    }

    private ChannelStoreQueryResult prepareChannelStoreQueryResult() {
        TenantChannelStoreInfo tenantChannelStoreInfo = TenantChannelStoreInfo.builder().shopId(2L)
                .cityCode(2).areaCode(2).address("testAddress").latitude("22").longitude("22")
                .storeName("storeName").phone("phone").
                tenantId(2L).channelId(DynamicChannelType.MEITUAN.getChannelId()).build();
        ChannelStoreQueryResult channelStoreQueryResult = new ChannelStoreQueryResult(ImmutableList.of(tenantChannelStoreInfo));
        return channelStoreQueryResult;
    }
}
