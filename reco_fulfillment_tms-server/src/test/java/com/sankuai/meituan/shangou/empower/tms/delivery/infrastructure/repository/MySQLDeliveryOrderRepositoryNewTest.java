package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrderRepository;
import org.junit.Test;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/5/11 20:47
 **/
public class MySQLDeliveryOrderRepositoryNewTest extends TestBase {
    @Resource
    private MySQLDeliveryOrderRepository mySQLDeliveryOrderRepository;

    @Resource
    protected RiderDeliveryOrderRepository riderDeliveryOrderRepository;

    @Test
    public void test1() {
        OrderKey orderKey = new OrderKey(1001197L, 49867245L, 1656629602413473842L);
        List<DeliveryOrder> openDeliveryOrders =
                mySQLDeliveryOrderRepository.getOpenDeliveryOrders(orderKey);
        System.out.println(JsonUtil.toJson(openDeliveryOrders));

        mySQLDeliveryOrderRepository.saveDeliveryOrder(openDeliveryOrders.get(0));
    }

    @Test
    public void test2() {
        Optional<RiderDeliveryOrder> deliveryOrderForceMaster =
                riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMaster(1656641619069268060L);
        System.out.println(JsonUtil.toJson(deliveryOrderForceMaster.get()));
    }
}
