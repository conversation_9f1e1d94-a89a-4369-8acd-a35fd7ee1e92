package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.rider.repository;

import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.DbTestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryStatisticsRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.util.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/8/25 22:50
 **/
@Slf4j
public class MySQLRiderStatisticRepositoryTest extends DbTestBase {
    @Resource
    private RiderDeliveryStatisticsRepository riderDeliveryStatisticsRepository;

    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_statistics_setup.xml")
    public void testQueryByAccountIdAndDuration() {
        long tenant = 1000395;
        long riderAccountId = 1234L;
        Long begin = 20220825L;
        Long end = 20220827L;
        List<RiderDeliveryStatisticDO> riderDeliveryStatisticDOS =
                riderDeliveryStatisticsRepository.queryByAccountIdAndDuration(tenant, riderAccountId, begin, end);
        log.info("-----------------------------------------");
        log.info("testQueryByAccountIdAndDuration_assigned riderDeliveryStatisticDOS={}", JacksonUtils.toJson(riderDeliveryStatisticDOS));
        log.info("-----------------------------------------");
        Assert.isTrue(riderDeliveryStatisticDOS.size() == 2);
    }


    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_statistics_setup.xml")
    public void testCheckDataIsReady() {
        Long dt = 20220826L;
        Boolean dataIsReady = riderDeliveryStatisticsRepository.checkDataIsReadyByDt(dt);
        Assert.isTrue(dataIsReady);
    }
}
