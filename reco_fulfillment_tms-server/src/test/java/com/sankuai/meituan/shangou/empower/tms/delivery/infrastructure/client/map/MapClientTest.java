package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.map;

import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.map.MapClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.map.MapRouteRequest;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/12/28
 * @email jianglilin02@meituan
 */
public class MapClientTest extends TestBase {

    @Resource(name = "mapClientImpl")
    private MapClient mapClient;

    @Test
    public void testRoute() {
        MapRouteRequest request = new MapRouteRequest();
        request.setOriginLongitude("104.058271");
        request.setOriginLatitude("30.560014");
        request.setDestinationLongitude("104.045408");
        request.setDestinationLatitude("30.545702");
        Result<Double> doubleResult = mapClient.queryRidePathDistance(request);
        Assert.assertNotNull(doubleResult.getInfo());
    }

    @Test
    @Ignore
    public void testGeo() {
        System.out.println(mapClient.queryCoordinatesByDetailAddress("北京市海淀区花园路5号5栋一层144"));
        System.out.println(mapClient.queryCoordinatesByDetailAddress("天津市南开区二马路龙凤市场内45号"));
        System.out.println(mapClient.queryCoordinatesByDetailAddress("北京市丰台区嘉业大厦2期1号楼101室（嘉业大厦内部底商）小仓生活"));
        System.out.println(mapClient.queryCoordinatesByDetailAddress("北京市东城区珠市口东大街4号2层2-11室"));
    }
}
