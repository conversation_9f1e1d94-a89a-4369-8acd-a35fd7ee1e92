package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.amazonaws.util.json.Jackson;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class DeliveryChangeSyncOutMessageListenerTest {

    @Test
    void translateMessage() {
        DeliveryChangeSyncOutMessage syncOutMessage = new DeliveryChangeSyncOutMessage(2,
                new DeliveryChangeSyncOutMessage.Head(1L, 1L, 1L, 1, 1L,
                        "1", 30),
                new DeliveryChangeSyncOutMessage.DeliveryStatusLockUnlockBody());

        MafkaMessage mafkaMessage = new MafkaMessage(null, 1, 1, null, Jackson.toJsonString(syncOutMessage));

        DeliveryChangeSyncOutMessageListener listener = new DeliveryChangeSyncOutMessageListener();
        DeliveryChangeSyncOutMessage message = listener.translateMessage(mafkaMessage);
        System.out.println(Jackson.toJsonString(message));
    }
}