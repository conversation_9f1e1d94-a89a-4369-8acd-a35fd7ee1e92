package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import com.github.springtestdbunit.assertion.DatabaseAssertionMode;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.tms.DbTestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/20
 */
public class MySQLDeliveryOrderRepositoryTest extends DbTestBase {

	private static final LocalDateTime DELIVERY_TIME = LocalDateTime.of(2020, 12, 12, 12, 12, 12);

	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;

	@Test
	@Transactional
	@DatabaseSetup("./delivery_order_setup.xml")
	@ExpectedDatabase(value = "./delivery_order_save_new_record_expect.xml", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
	public void should_saveDeliveryOrder_with_new_record() {
		deliveryOrderRepository.saveDeliveryOrder(buildDeliveryOrder(null));
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_order_setup.xml")
	@ExpectedDatabase(value = "./delivery_order_add_ext_info.xml", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
	public void add_ext_info() {
		deliveryOrderRepository.saveDeliveryOrder(buildDeliveryOrder(null));
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_order_setup.xml")
	@ExpectedDatabase(value = "./delivery_order_save_existing_record_expect.xml", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
	public void should_saveDeliveryOrder_with_existing_record() {
		deliveryOrderRepository.saveDeliveryOrder(buildDeliveryOrder(1L));
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_order_setup.xml")
	public void should_getOpenDeliveryOrders_success() {
		List<DeliveryOrder> openDeliveryOrders = deliveryOrderRepository.getOpenDeliveryOrders(new OrderKey(1000001L, 8000001L, 1111L));
		assertEquals(1, openDeliveryOrders.size());
		assertEquals("{\"id\":2,\"tenantId\":1000001,\"storeId\":8000001,\"orderKey\":{\"tenantId\":1000001,\"storeId\":8000001,\"orderId\":1111},\"channelOrderId\":\"channel_order_id\",\"orderSource\":30,\"orderBizType\":101,\"receiver\":{\"receiverName\":\"receiver_name\",\"receiverPhone\":\"receiver_phone\",\"receiverPrivacyPhone\":\"receiver_privacy_phone\",\"receiverAddress\":{\"addressDetail\":\"addressDetail\",\"coordinateType\":0,\"coordinatePoint\":{\"longitude\":\"111.2345\",\"latitude\":\"60.4567\"}},\"deliverable\":true},\"estimatedDeliveryTime\":\"2020-01-01T00:00:00\",\"estimatedDeliveryEndTime\":\"2020-01-01T00:00:00\",\"deliveryChannel\":900,\"channelDeliveryId\":\"hk_1\",\"channelServicePackageCode\":\"4002\",\"status\":1,\"exceptionType\":0,\"exceptionDescription\":\"无异常\",\"lastEventTime\":\"1970-01-01T00:00:00\",\"activeStatus\":0,\"deliveryDoneTime\":\"1970-01-01T00:00:00\",\"version\":0,\"active\":true,\"orderId\":1111}", JsonUtil.toJson(openDeliveryOrders.get(0)));
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_order_setup.xml")
	public void should_getDeliveryOrder_by_order_key_success() {
		List<DeliveryOrder> allDeliveryOrders = deliveryOrderRepository.getDeliveryOrders(new OrderKey(1000001L, 8000001L, 1111L));
		assertEquals(2, allDeliveryOrders.size());
		assertEquals("{\"id\":2,\"tenantId\":1000001,\"storeId\":8000001,\"orderKey\":{\"tenantId\":1000001,\"storeId\":8000001,\"orderId\":1111},\"channelOrderId\":\"channel_order_id\",\"orderSource\":30,\"orderBizType\":101,\"receiver\":{\"receiverName\":\"receiver_name\",\"receiverPhone\":\"receiver_phone\",\"receiverPrivacyPhone\":\"receiver_privacy_phone\",\"receiverAddress\":{\"addressDetail\":\"addressDetail\",\"coordinateType\":0,\"coordinatePoint\":{\"longitude\":\"111.2345\",\"latitude\":\"60.4567\"}},\"deliverable\":true},\"estimatedDeliveryTime\":\"2020-01-01T00:00:00\",\"estimatedDeliveryEndTime\":\"2020-01-01T00:00:00\",\"deliveryChannel\":900,\"channelDeliveryId\":\"hk_1\",\"channelServicePackageCode\":\"4002\",\"status\":1,\"exceptionType\":0,\"exceptionDescription\":\"无异常\",\"lastEventTime\":\"1970-01-01T00:00:00\",\"activeStatus\":0,\"deliveryDoneTime\":\"1970-01-01T00:00:00\",\"version\":0,\"active\":true,\"orderId\":1111}", JsonUtil.toJson(allDeliveryOrders.get(0)));
		assertEquals("{\"id\":1,\"tenantId\":1000001,\"storeId\":8000001,\"orderKey\":{\"tenantId\":1000001,\"storeId\":8000001,\"orderId\":1111},\"channelOrderId\":\"channel_order_id\",\"orderSource\":30,\"orderBizType\":101,\"receiver\":{\"receiverName\":\"receiver_name\",\"receiverPhone\":\"receiver_phone\",\"receiverPrivacyPhone\":\"receiver_privacy_phone\",\"receiverAddress\":{\"addressDetail\":\"addressDetail\",\"coordinateType\":0,\"coordinatePoint\":{\"longitude\":\"111.2345\",\"latitude\":\"60.4567\"}},\"deliverable\":true},\"estimatedDeliveryTime\":\"2020-01-01T00:00:00\",\"estimatedDeliveryEndTime\":\"2020-01-01T00:00:00\",\"deliveryChannel\":800,\"channelDeliveryId\":\"fn_1\",\"channelServicePackageCode\":\"1\",\"status\":110,\"exceptionType\":1,\"exceptionDescription\":\"系统异常\",\"lastEventTime\":\"2020-01-01T00:00:00\",\"activeStatus\":123456,\"deliveryDoneTime\":\"1970-01-01T00:00:00\",\"version\":0,\"active\":false,\"orderId\":1111}", JsonUtil.toJson(allDeliveryOrders.get(1)));
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_order_setup.xml")
	public void should_getDeliveryOrder_by_id_success() {
		DeliveryOrder deliveryOrder = deliveryOrderRepository.getDeliveryOrder(1L);
		assertEquals("{\"id\":1,\"tenantId\":1000001,\"storeId\":8000001,\"orderKey\":{\"tenantId\":1000001,\"storeId\":8000001,\"orderId\":1111},\"channelOrderId\":\"channel_order_id\",\"orderSource\":30,\"orderBizType\":101,\"receiver\":{\"receiverName\":\"receiver_name\",\"receiverPhone\":\"receiver_phone\",\"receiverPrivacyPhone\":\"receiver_privacy_phone\",\"receiverAddress\":{\"addressDetail\":\"addressDetail\",\"coordinateType\":0,\"coordinatePoint\":{\"longitude\":\"111.2345\",\"latitude\":\"60.4567\"}},\"deliverable\":true},\"estimatedDeliveryTime\":\"2020-01-01T00:00:00\",\"estimatedDeliveryEndTime\":\"2020-01-01T00:00:00\",\"deliveryChannel\":800,\"channelDeliveryId\":\"fn_1\",\"channelServicePackageCode\":\"1\",\"status\":110,\"exceptionType\":1,\"exceptionDescription\":\"系统异常\",\"lastEventTime\":\"2020-01-01T00:00:00\",\"activeStatus\":123456,\"deliveryDoneTime\":\"1970-01-01T00:00:00\",\"version\":0,\"active\":false,\"orderId\":1111}", JsonUtil.toJson(deliveryOrder));
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_order_setup.xml")
	public void should_getDeliveryOrder_by_deliveryChannel_and_channelDeliveryId() {
		DeliveryOrder deliveryOrder = deliveryOrderRepository.getDeliveryOrder(ThirdDeliveryChannelEnum.FENG_NIAO_DELIVERY, "fn_1");
		assertEquals("{\"id\":1,\"tenantId\":1000001,\"storeId\":8000001,\"orderKey\":{\"tenantId\":1000001,\"storeId\":8000001,\"orderId\":1111},\"channelOrderId\":\"channel_order_id\",\"orderSource\":30,\"orderBizType\":101,\"receiver\":{\"receiverName\":\"receiver_name\",\"receiverPhone\":\"receiver_phone\",\"receiverPrivacyPhone\":\"receiver_privacy_phone\",\"receiverAddress\":{\"addressDetail\":\"addressDetail\",\"coordinateType\":0,\"coordinatePoint\":{\"longitude\":\"111.2345\",\"latitude\":\"60.4567\"}},\"deliverable\":true},\"estimatedDeliveryTime\":\"2020-01-01T00:00:00\",\"estimatedDeliveryEndTime\":\"2020-01-01T00:00:00\",\"deliveryChannel\":800,\"channelDeliveryId\":\"fn_1\",\"channelServicePackageCode\":\"1\",\"status\":110,\"exceptionType\":1,\"exceptionDescription\":\"系统异常\",\"lastEventTime\":\"2020-01-01T00:00:00\",\"activeStatus\":123456,\"deliveryDoneTime\":\"1970-01-01T00:00:00\",\"version\":0,\"active\":false,\"orderId\":1111}", JsonUtil.toJson(deliveryOrder));
	}


	@Test
	@Transactional
	@DatabaseSetup("./delivery_order_setup.xml")
	public void should_getDeliveryOrder_by_OrderIds() {
		List<OrderKey> orderKeys = buildOrderKeys();
		Map<OrderKey, List<DeliveryOrder>> deliveryOrderMap = deliveryOrderRepository.getDeliveryOrdersByOrderKeys(orderKeys);

		List<DeliveryOrder> deliveryOrder1s = deliveryOrderMap.get(orderKeys.get(0));
		List<DeliveryOrder> deliveryOrder2s = deliveryOrderMap.get(orderKeys.get(1));
		assertEquals(2, deliveryOrder1s.size());
		assertEquals(1, deliveryOrder2s.size());
		deliveryOrder1s.sort(Comparator.comparing(DeliveryOrder::getCreateTime));
		assertEquals(Long.valueOf(1L), deliveryOrder1s.get(0).getId());
		assertEquals(Long.valueOf(2L), deliveryOrder1s.get(1).getId());
		assertEquals(Long.valueOf(3L), deliveryOrder2s.get(0).getId());

	}

	private List<OrderKey> buildOrderKeys() {
		return Lists.newArrayList(new OrderKey(1000001L, 8000001L, 1111L), new OrderKey(1000001L, 8000001L, 2222L));
	}

	private DeliveryOrder buildDeliveryOrder(Long id) {
		return new DeliveryOrder(null,2,"12345");
	}
}
