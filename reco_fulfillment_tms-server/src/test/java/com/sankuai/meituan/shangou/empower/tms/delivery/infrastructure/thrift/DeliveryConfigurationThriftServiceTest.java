package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.StoreConfigSaveRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.ConfigCommonResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TAggDeliveryPlatformConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryBaseException;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.DeliveryConfigApplicationService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.context.ActiveProfiles;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.SYSTEM_ERROR;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/2/10
 */
@RunWith(MockitoJUnitRunner.class)
@ActiveProfiles("dev")
public class DeliveryConfigurationThriftServiceTest {

    @InjectMocks
    private DeliveryConfigurationThriftServiceImpl deliveryConfigurationThriftService;

    @Mock
    private DeliveryConfigApplicationService deliveryConfigApplicationService;

    @Mock
    private DeliveryPlatformClient deliveryPlatformClient;

    /**
     * 开启麦芽田配送,麦芽田创店成功
     */
    @Test
    public void testSaveStoreConfiguration_whenMaltCreateShopSuccess() {
        deliveryConfigurationThriftService.saveStoreConfiguration(prepareStoreConfigSaveRequest());
        Mockito.verify(deliveryConfigApplicationService, Mockito.times(1)).buildDeliveryPoiConfig(any());
        Mockito.verify(deliveryPlatformClient, Mockito.times(1)).createShop(any());
        Mockito.verify(deliveryConfigApplicationService, Mockito.times(1)).saveStoreConfiguration(any(), any());

    }

    /**
     * 开启麦芽田配送,麦芽田创店失败
     */
    @Test
    public void testSaveStoreConfiguration_whenMaltCreateShopError() {
        when(deliveryPlatformClient.createShop(any())).thenThrow(new DeliveryBaseException("Create shop error"));
        ConfigCommonResponse configCommonResponse = deliveryConfigurationThriftService.saveStoreConfiguration(prepareStoreConfigSaveRequest());
        Mockito.verify(deliveryConfigApplicationService, Mockito.times(1)).buildDeliveryPoiConfig(any());
        Mockito.verify(deliveryConfigApplicationService, Mockito.times(0)).saveStoreConfiguration(any(), any());
        Assert.assertEquals(SYSTEM_ERROR.getCode(), configCommonResponse.getStatus().getCode());
    }

    /**
     * 关闭麦芽田配送
     */
    @Test
    public void testSaveStoreConfiguration_whenCloseMaltPlateForm() {
        StoreConfigSaveRequest storeConfigSaveRequest = prepareStoreConfigSaveRequest();
        storeConfigSaveRequest.getTAggDeliveryPlatformConfig().setOpenFlag(0);
        deliveryConfigurationThriftService.saveStoreConfiguration(storeConfigSaveRequest);
        Mockito.verify(deliveryConfigApplicationService, Mockito.times(1)).buildDeliveryPoiConfig(any());
        Mockito.verify(deliveryPlatformClient, Mockito.times(0)).createShop(any());
        Mockito.verify(deliveryConfigApplicationService, Mockito.times(1)).saveStoreConfiguration(any(), any());
    }


    private StoreConfigSaveRequest prepareStoreConfigSaveRequest() {
        return new StoreConfigSaveRequest(
                1000L, 1007L, 3,
                new TStoreConfig(null, null, null, 2, 10, 2, null, null, null, null, null, null, null),
                null,
                new TAggDeliveryPlatformConfig(2, 1, null, null, null, null, 1),null,null);

    }


}
