package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.auth;

import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.auth.AuthClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.AccountInfo;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class AuthClientImplTest extends TestBase {
    @Resource
    private AuthClientImpl authClient;
    @Test
    void queryAccountListByPoiAndAuth() {
        List<AccountInfo> accountInfos = authClient.queryAccountListByPoiAndAuth(1001197L,49867245L,3);
        Assert.assertNotNull(accountInfos);
    }
}