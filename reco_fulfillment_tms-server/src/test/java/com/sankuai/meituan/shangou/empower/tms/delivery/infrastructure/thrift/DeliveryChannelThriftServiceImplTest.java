package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request.AggDeliveryFailReasonNotifyRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request.DeliveryCancelResultNotifyRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request.FarmDeliveryChangeNotifyReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.response.AggDeliveryFailCallbackResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.response.DeliveryCancelResultNotifyResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.response.FarmDeliveryChangeNotifyResp;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Assert;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;


import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class DeliveryCallbackThriftServiceImplTest extends TestBase {

    @Autowired
    private DeliveryCallbackThriftServiceImpl deliveryCallbackThriftService;

    @Test
    public void farmNotifyDeliveryChange() {
        FarmDeliveryChangeNotifyReq req = new FarmDeliveryChangeNotifyReq();
        req.setOriginId("1386220570558865422");
        req.setOriginLogisticNo("21324534678");
        req.setTimestamp(System.currentTimeMillis()/1000);
        req.setLogistic("mtps");
        req.setLogisticNo("2");
        req.setStatus(30);
        req.setStatusDesc("");
        req.setRiderName("吗哈哈");
        req.setRiderPhone("1234567");
        req.setContent("");
        req.setAmount("1");
        req.setDistance("1");
        req.setSign("1234tyu78");
        req.setPlatformCode(2);
        FarmDeliveryChangeNotifyResp resp = deliveryCallbackThriftService.farmNotifyDeliveryChange(req);

        assertEquals(resp.getStatus(), Status.SUCCESS);
    }

    @Test
    public void farmNotifyDeliveryException() {
        AggDeliveryFailReasonNotifyRequest request = JSON.parseObject("{\"code\":3,\"originId\":\"1385417897077121056\",\"platformCode\":2,\"reason\":\"配送超时\",\"sign\":\"4F13DD9065784889A5EA6CA282396D57\",\"timestamp\":1619144866}", AggDeliveryFailReasonNotifyRequest.class);
        AggDeliveryFailCallbackResponse response = deliveryCallbackThriftService.notifyAggDeliveryFailResult(request);
        assertEquals(response.getStatus(), Status.SUCCESS);
    }

    @Test
    public void farmNotifyDeliveryChangeTest() {
        FarmDeliveryChangeNotifyReq req = new FarmDeliveryChangeNotifyReq();
        req.setOriginId("1498557069404414005");
        req.setOriginLogisticNo("");
        req.setTimestamp(System.currentTimeMillis()/1000);
        req.setLogistic("");
        req.setLogisticNo("596688102446082117");
        req.setStatus(20);
        req.setStatusDesc("已接单");
        req.setRiderName("胡旭东");
        req.setRiderPhone("18291966773");
        req.setAmount("10.3");
        req.setDistance("100");
        req.setContent("");
        req.setSign("D723113B7775175EECA68E9C99A5BA5D");
        req.setPlatformCode(2);
        // 已接单
        FarmDeliveryChangeNotifyResp resp = deliveryCallbackThriftService.farmNotifyDeliveryChange(req);
        assertEquals(resp.getStatus(), Status.SUCCESS);
    }


}
