package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryOrderRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryDeliveryOrderResponse;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.Request;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/5/18
 */
class QueryDeliveryOrderThriftServiceImplTest extends TestBase {

	@Resource
	QueryDeliveryInfoThriftServiceImpl queryDeliveryOrderThriftService;

	@Test
	void testQueryDeliveryOrderByOrderId() {
		QueryDeliveryOrderRequest req = new QueryDeliveryOrderRequest();
		req.setOrderId(1351439928528994387L);
		QueryDeliveryOrderResponse resp = queryDeliveryOrderThriftService.queryDeliveryOrderByOrderId(req);
		Assert.assertNotNull(resp);
		Assert.assertEquals(resp.status.code, 0);
		Assert.assertEquals(resp.status.msg, "");
		System.out.print(resp.TDeliveryOrders.iterator().next());
	}

	@Test
	void testQueryDeliveryOrderByOrderIdWithWrongId() {
		QueryDeliveryOrderRequest req1 = new QueryDeliveryOrderRequest();
		req1.setOrderId(null);
		QueryDeliveryOrderRequest req2 = new QueryDeliveryOrderRequest();
		req2.setOrderId(0L);
		QueryDeliveryOrderRequest req3 = new QueryDeliveryOrderRequest();
		req3.setOrderId(-1L);
		QueryDeliveryOrderRequest req4 = null;
		QueryDeliveryOrderResponse resp1 = queryDeliveryOrderThriftService.queryDeliveryOrderByOrderId(req1);
		QueryDeliveryOrderResponse resp2 = queryDeliveryOrderThriftService.queryDeliveryOrderByOrderId(req2);
		QueryDeliveryOrderResponse resp3 = queryDeliveryOrderThriftService.queryDeliveryOrderByOrderId(req3);
		QueryDeliveryOrderResponse resp4 = queryDeliveryOrderThriftService.queryDeliveryOrderByOrderId(req4);
		Assert.assertEquals(resp1.status.code, 200);
		Assert.assertEquals(resp1.status.msg, "赋能订单id不合法");
	}
}