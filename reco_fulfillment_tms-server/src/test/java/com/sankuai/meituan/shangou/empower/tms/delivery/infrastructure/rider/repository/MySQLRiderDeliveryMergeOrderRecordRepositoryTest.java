package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.rider.repository;

import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.shangou.empower.tms.DbTestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryMergeOrderRecord;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.*;

class MySQLRiderDeliveryMergeOrderRecordRepositoryTest extends DbTestBase {

    @Resource
    private MySQLRiderDeliveryMergeOrderRecordRepository mergeOrderRecordRepository;

    @Transactional
    @Test
    void upsert_insert() {
        RiderDeliveryMergeOrderRecord record = this.buildRecord(222L, 1L);

        boolean bool = mergeOrderRecordRepository.upsert(record);
        Assert.assertTrue(bool);
        //System.out.println(JacksonUtils.toJson(mergeOrderRecordRepository.queryByMergeId(222L).get(0)));
    }

    @Transactional
    @Test
    void batchUpsert() {
        try {
            mergeOrderRecordRepository.batchUpsert(Arrays.asList(this.buildRecord(222L, 1L),
                    this.buildRecord(222L, 2L)));
        } catch (Exception e) {
            Assert.assertTrue(Boolean.FALSE);
        }
    }

    @Transactional
    @DatabaseSetup("./dh_delivery_merge_order_setup.xml")
    @Test
    void remove() {
        mergeOrderRecordRepository.remove(222L, 1L);
        Assert.assertEquals(mergeOrderRecordRepository.queryByMergeId(222L).size(), 1);
    }


    @Test
    void batchUpsertAndRemove() {
    }

    @Transactional
    @DatabaseSetup("./dh_delivery_merge_order_setup.xml")
    @Test
    void queryByAccountIdAndDeliveryIds() {
        List<RiderDeliveryMergeOrderRecord> list = mergeOrderRecordRepository.queryByAccountIdAndDeliveryIds(2345L, Sets.newHashSet(1L, 2L));
        Assert.assertEquals(list.size(), 2);
    }

    @Transactional
    @DatabaseSetup("./dh_delivery_merge_order_setup.xml")
    @Test
    void queryRiderMergeOrderRecordMap() {
        Map<Long, Set<Long>> map = mergeOrderRecordRepository.queryRiderMergeOrderRecordMap(2345L, Sets.newHashSet(1L));
        Assert.assertEquals(map.size(), 1);
        Assert.assertEquals(2, map.get(222L).size());
    }


    private RiderDeliveryMergeOrderRecord buildRecord(Long mergeId, Long deliveryId) {
        return new RiderDeliveryMergeOrderRecord(
                null,
                deliveryId,
                mergeId,
                0L,
                "34rtfdf",
                1,
                2345L,
                Collections.emptyMap(),
                LocalDateTime.now(),
                LocalDateTime.now()
        );
    }
}