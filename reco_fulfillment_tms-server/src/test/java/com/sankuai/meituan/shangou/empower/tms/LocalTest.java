package com.sankuai.meituan.shangou.empower.tms;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.filter.ThriftFilterAndInterceptorUtil;
import com.sankuai.meituan.shangou.saas.message.dto.push.PushIdempotentDTO;
import com.sankuai.meituan.shangou.saas.message.request.push.SharkPushSendRequest;

import java.util.Optional;


public class LocalTest {

    public static void main(String[] args) {

        String str = "{\"body\":\"{\\\"orderBizType\\\":101,\\\"orderOTOCategory\\\":1,\\\"viewOrderId\\\":\\\"84788313128056092\\\",\\\"shopId\\\":1002845,\\\"tenantId\\\":1000061,\\\"status\\\":1,\\\"deliveryStatus\\\":5,\\\"orderSource\\\":30,\\\"distributeStatus\\\":-1,\\\"sourceStatus\\\":1,\\\"orderId\\\":1568035456115851320,\\\"updateTime\\\":1662683760119,\\\"compensate\\\":false,\\\"delayPush\\\":false}\",\"bornTimestamp\":1662683760119,\"delayInMs\":0}";

        MafkaMessage mafkaMessage = JSON.parseObject(str, MafkaMessage.class);

        System.out.println(extractParam(mafkaMessage, "tenantId"));
    }

    public static String extractParam(MafkaMessage message, String param) {
        try {
            JsonNode jsonNode = JsonUtil.toJsonNode(message.getBody().toString());
            return Optional.ofNullable(jsonNode)
                    .map(it -> it.get(param))
                    .map(JsonNode::toString)
                    .orElse("-1");
        } catch (Exception e) {
            return "-1";
        }
    }
}
