package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.DLaunchWithOutChannelReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryLaunchResponse;
import org.apache.shiro.util.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import javax.validation.constraints.AssertTrue;

public class DeliveryOperationThriftServiceImplTest extends TestBase {

    @Resource
    private DeliveryOperationThriftServiceImpl deliveryOperationThriftService;

    @Test
    public void testLaunchWithOutChannelDelivery() {
        DLaunchWithOutChannelReq req = new DLaunchWithOutChannelReq();
        req.setOperatorId(111l);
        req.setOrderId(1384403708829245474l);
        req.setStoreId(49864215l);
        req.setTenantId(1000078l);
        DeliveryLaunchResponse response = deliveryOperationThriftService.launchWithOutChannelDelivery(req);
        System.out.println(response);
        Assert.isTrue(response != null);


    }
}