package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.leaf;

import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.leaf.LeafClient;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2022/9/9 23:04
 **/
public class LeafClientTest extends TestBase {
    @Resource
    private LeafClient leafClient;

    @Test
    public void test_create_nextSnowflakeId() {
        long snowflakeId = leafClient.nextSnowflakeId();
        Assert.assertTrue(snowflakeId > 1568256129614483532L);
    }


}
