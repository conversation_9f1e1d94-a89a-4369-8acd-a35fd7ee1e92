package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.QueryOrderDeliveryInfoKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionOrderBySubTypeRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionOrderRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryInfoRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.DeliveryExceptionOrderSubTypeCountResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.DeliveryExceptionOrdersBySubTypeResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryDeliveryInfoResponse;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/13
 */
public class QueryDeliveryInfoThriftServiceImplTest extends TestBase {

    @Resource
    private QueryDeliveryInfoThriftServiceImpl queryDeliveryInfoThriftService;

    @Test
    public void queryDeliveryInfoByOrderKeys() {
        QueryOrderDeliveryInfoKey key1 = new QueryOrderDeliveryInfoKey();
        key1.empowerOrderId = 1485883832962916373L;
        //key1.empowerOrderId = 1349601731578691594L;
        key1.selfDeliveryMark = 1;
        key1.deliveryMethod = 1;
        key1.storeId = 4987269L;
        key1.orderSource = 30;
        key1.tenantId = 1000094L;
        key1.orderStatus = 13;
        QueryOrderDeliveryInfoKey key2 = new QueryOrderDeliveryInfoKey();
        key2.empowerOrderId = 1349601731578691594L;
        key2.selfDeliveryMark = 1;
        key2.deliveryMethod = 1;
        key2.storeId = 4986523L;
        key2.orderSource = 30;
        key2.tenantId = 1000078L;
        key2.orderStatus = 13;

        QueryDeliveryInfoRequest req = new QueryDeliveryInfoRequest(Lists.newArrayList(key1));

        QueryDeliveryInfoResponse response = queryDeliveryInfoThriftService.queryDeliveryInfoByOrderKeys(req);
        Assert.assertNotNull(response);
    }

    @Test
    public void queryDeliveryExceptionOrdersBySubType() {
        QueryDeliveryExceptionOrderBySubTypeRequest request = new QueryDeliveryExceptionOrderBySubTypeRequest();
        request.setSubType(10);
        request.setEmpowerStoreId(49868025L);
        DeliveryExceptionOrdersBySubTypeResponse response = queryDeliveryInfoThriftService.queryDeliveryExceptionOrdersBySubType(request);
        Assert.assertEquals(response.getStatus().getCode(), Status.SUCCESS.getCode());
        Assert.assertTrue(response.getOrders().size() >= 0);
    }

    @Test
    public void queryDeliveryExceptionOrderSubTypeCount() {
        QueryDeliveryExceptionOrderRequest request = new QueryDeliveryExceptionOrderRequest();
        request.setEmpowerStoreId(49868025L);
        DeliveryExceptionOrderSubTypeCountResponse response = queryDeliveryInfoThriftService.queryDeliveryExceptionOrderSubTypeCount(request);
        Assert.assertEquals(response.getStatus().getCode(), Status.SUCCESS.getCode());
        Assert.assertTrue(response.getAllSubTypeCount() == response.getNoRiderAcceptCount() + response.getNoArrivalStoreCount() + response.getNoRiderTakeGoodsCount() + response.getDeliveryTimeoutCount());
    }
}