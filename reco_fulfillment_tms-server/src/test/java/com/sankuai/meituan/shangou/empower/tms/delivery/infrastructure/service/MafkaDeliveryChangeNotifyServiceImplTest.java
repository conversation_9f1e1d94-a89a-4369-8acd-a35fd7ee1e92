package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.service;

import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

class MafkaDeliveryChangeNotifyServiceImplTest extends TestBase {

    @Autowired
    private MafkaDeliveryChangeNotifyServiceImpl mafkaDeliveryChangeNotifyService;

    @Test
    void saveStoreConfiguration() {
        mafkaDeliveryChangeNotifyService.notifySyncRiderPosition(547l, null,0L,0L);
    }

}