package com.sankuai.meituan.shangou.empower.tms.delivery.service.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.DeliveryStoreChangeContent;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.DevlieryNotifyContent;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.warn.PushMessageResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer.OrderPickFinishMessageListener;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer.OrderPlatformDeliveryUpdateMessageListener;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer.OrderStatusChangeMessageListener;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import org.junit.Test;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/18
 */
public class JsonUtilTest {
	private static final String RETRY_TIMES_KEY = "_retry_times";
	private static final LocalDateTime TIME =  LocalDateTime.of(2008, 5, 12, 14,28, 0, 0);

	@Test
	public void should_get_and_put_fields_success() {
		JsonNode jsonNode = JsonUtil.toJsonNode("{\"a\":1,\"b\":2}");
		assertNotNull(jsonNode);
		Integer retryTimes = Optional.of(jsonNode).map(it -> it.get(RETRY_TIMES_KEY)).map(JsonNode::asInt).orElse(0);
		assertEquals(0, retryTimes.intValue());

		((ObjectNode) jsonNode).put(RETRY_TIMES_KEY, 1);
		retryTimes = Optional.of(jsonNode).map(it -> it.get(RETRY_TIMES_KEY)).map(JsonNode::asInt).orElse(0);
		assertEquals(1, retryTimes.intValue());
		assertEquals("{\"a\":1,\"b\":2,\"_retry_times\":1}", JsonUtil.toJson(jsonNode));
	}

	@Test
	public void should_serialize_message_success() {
		assertEquals(
				"{\"deliveryChangeType\":1,\"tenantId\":1,\"shopId\":2,\"orderBizType\":101,\"orderId\":3,\"viewOrderId\":\"view_order_id\",\"orderSource\":30,\"deliveryStatus\":1,\"riderName\":\"riderName\",\"riderPhone\":\"riderPhone\",\"riderCurrentLongitude\":\"12345\",\"riderCurrentLatitude\":\"6789\",\"coordinateType\":1,\"deliveryChannelId\":800,\"channelDeliveryId\":\"channel_delivery_id\",\"cancelReasonType\":1,\"cancelReasonDescription\":\"cancel_reason_desc\",\"rejectReasonType\":1,\"rejectReasonDescription\":\"reject_reson_desc\",\"updateTime\":1000,\"deliveryOrderId\":2333}",
				JsonUtil.toJson(new DeliveryChangeNotifyMessage(1L, 2L, 101, 3L, "view_order_id", 30, 1, "riderName", "riderPhone", "12345", "6789", 1, 800,10, "channel_delivery_id" ,1, "cancel_reason_desc", 1, "reject_reson_desc", 1000L, 2333L,
						null,null,null, null,null,1,null,null))
		);

		assertEquals(
				"{\"deliveryId\":1,\"event\":50,\"rider\":{\"riderName\":\"riderName\",\"riderPhone\":\"riderPhone\"},\"exceptionType\":6,\"exceptionDescription\":\"exception_desc\",\"eventTime\":1210573680000}",
				JsonUtil.toJson(new DeliveryEventMessage(1L, DeliveryEventEnum.RIDER_FINISH_DELIVERY, new Rider("riderName", "riderPhone"
						, "riderPhoneToken"), new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_SYSTEM,
						"exception_desc"), TIME))
		);

		assertEquals(
				"{\"tenantId\":1,\"storeId\":2,\"deliveryId\":3}",
				JsonUtil.toJson(new DeliveryCancelCommandMessage(1L, 2L, 3L))
		);

		assertEquals(
				"{\"deliveryId\":1,\"changeEvent\":2,\"changeInfoJson\":\"{\\\"a\\\":1,\\\"b\\\":2}\",\"changeTime\":1210573680000}",
				JsonUtil.toJson(new DeliveryChangeLogMessage(1L, 2, "{\"a\":1,\"b\":2}", TIME))
		);

		assertEquals(
				"{\"tenantId\":1,\"shopId\":2,\"orderSource\":3,\"orderBizType\":4,\"orderId\":5,\"viewOrderId\":\"view-order-id\"}",
				JsonUtil.toJson(new DeliveryLaunchCommandMessage(1L, 2L, 3, 4, 5L, "view-order-id",null,null))
		);

		assertEquals(
				"{\"tenantId\":1,\"shopId\":2,\"orderId\":3,\"orderBizType\":4,\"viewOrderId\":\"view-order-id\",\"deliveryId\":5}",
				JsonUtil.toJson(new RiderAssignTimeOutCheckMessage(1L, 2L, 3L,4, "view-order-id", 5L))
		);
	}

	@Test
	public void should_deserialize_message_success() {
		assertEquals(
				"{\"deliveryId\":1,\"changeEvent\":2,\"changeInfoJson\":\"{\\\"a\\\":1,\\\"b\\\":2}\",\"changeTime\":1210573680000}",
				JsonUtil.toJson(JsonUtil.fromJson("{\"deliveryId\":1,\"changeEvent\":2,\"changeInfoJson\":\"{\\\"a\\\":1,\\\"b\\\":2}\",\"changeTime\":1210573680000}", DeliveryChangeLogMessage.class))
		);

		assertEquals(
				"{\"channelCode\":1,\"channelDeliveryId\":\"channel_delivery_id\",\"status\":2,\"riderName\":\"rider_name\",\"riderPhone\":\"riderPhone\",\"timestamp\":3,\"exceptionType\":4,\"exceptionDesc\":\"exception_desc\"}",
				JsonUtil.toJson(JsonUtil.fromJson("{\"channelCode\":1,\"channelDeliveryId\":\"channel_delivery_id\",\"status\":2,\"riderName\":\"rider_name\",\"riderPhone\":\"riderPhone\",\"timestamp\":3,\"exceptionType\":4,\"exceptionDesc\":\"exception_desc\"}", DevlieryNotifyContent.class))
		);

		assertEquals(
				"{\"tenantId\":1,\"shopId\":2,\"orderSource\":3,\"orderBizType\":4,\"orderId\":5,\"viewOrderId\":\"view-order-id\"}",
				JsonUtil.toJson(JsonUtil.fromJson("{\"tenantId\":1,\"shopId\":2,\"orderSource\":3,\"orderBizType\":4,\"orderId\":5,\"viewOrderId\":\"view-order-id\"}", DeliveryLaunchCommandMessage.class))
		);

		assertEquals(
				"{\"channelStoreCode\":\"channel_store_code\",\"deliveryChannelType\":1,\"optionType\":2}",
				JsonUtil.toJson(JsonUtil.fromJson("{\"channelStoreCode\":\"channel_store_code\",\"deliveryChannelType\":1,\"optionType\":2}", DeliveryStoreChangeContent.class))
		);

		assertEquals(
				"{\"tenantId\":1,\"shopId\":2,\"orderId\":3}",
				JsonUtil.toJson(JsonUtil.fromJson("{\"tenantId\":1,\"shopId\":2,\"orderId\":3}", OrderPickFinishMessageListener.MessageDTO.class))
		);

		assertEquals(
				"{\"tenantId\":1,\"shopId\":2,\"orderId\":3,\"distributeStatus\":4}",
				JsonUtil.toJson(JsonUtil.fromJson("{\"tenantId\":1,\"shopId\":2,\"orderId\":3,\"distributeStatus\":4}", OrderPlatformDeliveryUpdateMessageListener.OrderPlatformDeliveryUpdateMessage.class))
		);

		assertEquals(
				"{\"tenantId\":1,\"shopId\":2,\"orderId\":3,\"orderSource\":4,\"status\":5,\"sourceStatus\":6,\"onlineOrder\":false}",
				JsonUtil.toJson(JsonUtil.fromJson("{\"tenantId\":1,\"shopId\":2,\"orderId\":3,\"orderSource\":4,\"status\":5,\"sourceStatus\":6}", OrderStatusChangeMessageListener.MessageDTO.class))
		);

		assertEquals(
				"{\"tenantId\":1,\"shopId\":2,\"orderId\":3,\"orderBizType\":4,\"viewOrderId\":\"view-order-id\",\"deliveryId\":5}",
				JsonUtil.toJson(JsonUtil.fromJson("{\"tenantId\":1,\"shopId\":2,\"orderId\":3,\"orderBizType\":4,\"viewOrderId\":\"view-order-id\",\"deliveryId\":5}", RiderAssignTimeOutCheckMessage.class))
		);

		assertEquals(
				"{\"tenantId\":1,\"storeId\":2,\"deliveryId\":3}",
				JsonUtil.toJson(JsonUtil.fromJson("{\"tenantId\":1,\"storeId\":2,\"deliveryId\":3}", DeliveryCancelCommandMessage.class))
		);

		assertEquals(
				"{\"deliveryId\":1,\"event\":50,\"rider\":{\"riderName\":\"riderName\",\"riderPhone\":\"riderPhone\"},\"exceptionType\":6,\"exceptionDescription\":\"exception_desc\",\"eventTime\":1210573680000}",
				JsonUtil.toJson(JsonUtil.fromJson("{\"deliveryId\":1,\"event\":50,\"rider\":{\"riderName\":\"riderName\",\"riderPhone\":\"riderPhone\"},\"exceptionType\":6,\"exceptionDescription\":\"exception_desc\",\"eventTime\":1210573680000}", DeliveryEventMessage.class))
		);

	}

	@Test
	public void check_manually_construct_json() {
		ObjectNode contentJson = JsonUtil.generateObjectNode();
		contentJson.put("operation", "combo");
		contentJson.put("combo", "true");
		contentJson.put("serverTS", 1624473593921L);
		ArrayNode actions = contentJson.putArray("actions");
		ObjectNode redDotAction = JsonUtil.generateObjectNode();
		redDotAction.put("operation", "redDot");
		redDotAction.put("code", "SELF_RIDER_WAIT_TO_GET");
		actions.add(redDotAction);
		String sharkContent = contentJson.toString();
		String target = "{\"operation\":\"combo\",\"combo\":\"true\",\"serverTS\":1624473593921,\"actions\":[{\"operation\":\"redDot\"," +
				"\"code\":\"SELF_RIDER_WAIT_TO_GET\"}]}";
		assertEquals(target, sharkContent);
	}

	@Test
	public void check_parse_from_json(){
		String json = "{\"rescode\":0,\"data\":{\"mids\":[1216947505669885952,1216947505669885952,1216947505669885952],\"total\":3,\"failUids\":[],\"successUids\":[1081468276,2204584850,1080845902],\"failReason\":{}}}";
		PushMessageResponse response = JsonUtil.fromJson(json, new TypeReference<PushMessageResponse>() {});
		assertNotNull(response);
	}



}
