package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryChannelPreLaunchInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/12
 */
public class AggregationDeliveryPlatformClientTest {

	@Test
	public void should_sortDeliveryChannelPreLaunchInfo_success() {
		List<DeliveryChannelPreLaunchInfo> list = new ArrayList<>();
		list.add(new DeliveryChannelPreLaunchInfo(DeliveryChannelEnum.AGGREGATE_DELIVERY_MEITUAN_TUAN, "1", BigDecimal.valueOf(9.9)));
		list.add(new DeliveryChannelPreLaunchInfo(DeliveryChannelEnum.HAI_KUI_DELIVERY, "2", "范围超区"));
		list.add(new DeliveryChannelPreLaunchInfo(DeliveryChannelEnum.AGGREGATE_DELIVERY_DADA, "3", BigDecimal.valueOf(3.5)));
		list.add(new DeliveryChannelPreLaunchInfo(DeliveryChannelEnum.AGGREGATE_DELIVERY_FENGNIAO, "4", BigDecimal.valueOf(7)));
		list.add(new DeliveryChannelPreLaunchInfo(DeliveryChannelEnum.FENG_NIAO_DELIVERY, "5", "范围超区"));

		List<DeliveryChannelPreLaunchInfo> sortedList = list.stream().sorted().collect(Collectors.toList());

		assertEquals(5, sortedList.size());
		assertEquals(DeliveryChannelEnum.AGGREGATE_DELIVERY_DADA, sortedList.get(0).getDeliveryChannel());
		assertEquals(DeliveryChannelEnum.AGGREGATE_DELIVERY_FENGNIAO, sortedList.get(1).getDeliveryChannel());
		assertEquals(DeliveryChannelEnum.AGGREGATE_DELIVERY_MEITUAN_TUAN, sortedList.get(2).getDeliveryChannel());
		assertEquals(DeliveryChannelEnum.HAI_KUI_DELIVERY, sortedList.get(3).getDeliveryChannel());
		assertEquals(DeliveryChannelEnum.FENG_NIAO_DELIVERY, sortedList.get(4).getDeliveryChannel());
	}
}
