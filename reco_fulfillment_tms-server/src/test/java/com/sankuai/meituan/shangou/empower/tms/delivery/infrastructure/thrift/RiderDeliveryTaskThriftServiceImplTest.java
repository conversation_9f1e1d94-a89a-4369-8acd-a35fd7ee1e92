package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;


import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.enums.DeliveryTaskTypeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.PageQueryDeliveryExceptionRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.RiderDeliveryTaskThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.req.TaskListReq;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.req.TaskSubmitReq;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.resp.TaskListResp;
import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.TimeUtil;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;

public class RiderDeliveryTaskThriftServiceImplTest extends TestBase {

    @Autowired
    private RiderDeliveryTaskThriftService riderDeliveryTaskThriftService;


    @Test
    public void submit() throws TException {
        PageQueryDeliveryExceptionRequest pageRequest = new PageQueryDeliveryExceptionRequest();
        pageRequest.setPageNum(1);
        pageRequest.setPageSize(50);
        pageRequest.setStoreIds(Collections.singletonList(49867245L));
        pageRequest.setTenantId(1001197L);
        pageRequest.setPayTimeStartTimeStamp(TimeUtil.toMilliSeconds(LocalDateTime.now().plusDays(-10)));
        pageRequest.setPayTimeEndTimeStamp(TimeUtil.toMilliSeconds(LocalDateTime.now()));

        TaskSubmitReq request = new TaskSubmitReq();
        request.setOperatorAccount("林晓蕊");
        request.setOperatorId(10038370L);
        request.setParameter(JsonUtil.toJson(pageRequest));
        request.setTaskType(DeliveryTaskTypeEnum.EXPORT_RIDER_DELIVERY_EXCEPTION_DETAILS.getCode());
        request.setTenantId(1001197L);

        Status status = riderDeliveryTaskThriftService.submit(request);
        Assert.assertEquals(Status.SUCCESS.getCode(), status.getCode());

    }

    @Test
    public void queryListPageable() throws TException {
        TaskListReq request = new TaskListReq();
        request.setTaskTypes(Arrays.asList(DeliveryTaskTypeEnum.EXPORT_RIDER_DELIVERY_EXCEPTION_DETAILS.getCode()));
        request.setOperatorId(10038370L);
        request.setPage(1);
        request.setPageSize(10);
        request.setTaskName(DeliveryTaskTypeEnum.EXPORT_RIDER_DELIVERY_EXCEPTION_DETAILS.getName());
        request.setTenantId(1001197L);
        request.setBeginTime(1656259200000L);
        request.setEndTime(1656950399999L);

        TaskListResp taskListResp = riderDeliveryTaskThriftService.queryListPageable(request);
        System.out.println(JsonUtil.toJson(taskListResp));
    }

}
