package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import com.github.springtestdbunit.assertion.DatabaseAssertionMode;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.DbTestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.DeliveryOrderLog;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.DeliveryOrderLogRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryRiderChangeInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/20
 */
public class MySQLDeliveryOrderLogRepositoryTest extends DbTestBase {

	private LocalDateTime changeTime = LocalDateTime.of(2008, 5, 12, 14, 28, 0, 0);

	@Resource
	private DeliveryOrderLogRepository deliveryOrderLogRepository;

	@Test
	@Transactional
	@DatabaseSetup("./delivery_order_log_setup.xml")
	@ExpectedDatabase(value = "./delivery_order_log_save_expect.xml", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
	public void should_saveDeliveryChangeLog_success() {
		deliveryOrderLogRepository.saveDeliveryChangeLog(2L,
				RiderDeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode(), DeliveryEventEnum.LAUNCH_DELIVERY,
				null, changeTime);
		deliveryOrderLogRepository.saveDeliveryChangeLog(2L, RiderDeliveryStatusEnum.RIDER_ASSIGNED.getCode(),
				DeliveryEventEnum.DELIVERY_EXCEPTION,
				JsonUtil.toJson(new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION, "系统异常")), changeTime);
		deliveryOrderLogRepository.saveDeliveryChangeLog(2L, RiderDeliveryStatusEnum.RIDER_ASSIGNED.getCode(),
				DeliveryEventEnum.RIDER_CHANGE, JsonUtil.toJson(new DeliveryRiderChangeInfo(new Rider("old_rider_name", "old_rider_phone"
						, "old_rider_phone_token"), new Rider("new_rider_name", "new_rider_phone", "new_rider_phone_token"))), changeTime);
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_order_log_setup.xml")
	public void should_queryDeliveryChangeLog_success() {
		Map<Long, DeliveryOrderLog> logsByDeliveryOrderIds = deliveryOrderLogRepository.getLogsByDeliveryOrderIds(Lists.newArrayList(1L, 2L, 3L), DeliveryEventEnum.DELIVERY_PLATFORM_ACCEPT_DELIVERY_ORDER);

		Assert.assertTrue(logsByDeliveryOrderIds.size() == 1);
		Assert.assertTrue(logsByDeliveryOrderIds.get(1L) == null);
		Assert.assertTrue(logsByDeliveryOrderIds.get(2L).getChangeInfo() != null);
		Assert.assertTrue(logsByDeliveryOrderIds.get(2L).getDeliveryId() == 2L);
	}
}
