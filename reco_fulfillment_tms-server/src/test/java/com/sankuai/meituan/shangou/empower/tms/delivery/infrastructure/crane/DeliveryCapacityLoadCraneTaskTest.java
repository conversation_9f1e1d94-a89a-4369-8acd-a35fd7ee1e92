package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.crane;

import com.cip.crane.netty.protocol.ScheduleTask;
import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.shangou.commons.message.pusher.channelPusher.XmMessagePusher;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;

/**
 * <AUTHOR>
 * @since 2023/1/15 18:55
 **/
@Slf4j
public class DeliveryCapacityLoadCraneTaskTest extends TestBase {
    @Resource
    private MonitorDeliveryCapacityLoadCraneTask monitorDeliveringCapacityLoadCraneTask;

    @Resource
    private XmMessagePusher xmMessagePusher;

    @Test
    public void executeTaskTest() {
        ScheduleTask scheduleTask = new ScheduleTask();
        ArrayList<String> param = new ArrayList<>();
        param.add("1001197");
        scheduleTask.setTaskItems(param);
        monitorDeliveringCapacityLoadCraneTask.handleTask(scheduleTask);
    }

    @Test
    public void test() {
        String content = new StringBuilder().append("⚠️【爆单告警】\n")
                .append("\u0020\u0020\u0020\u0020【门店】深圳市歪马送酒（前海店）\n")
                .append("\u0020\u0020\u0020\u0020【渠道门店ID】\u0020外卖\u002013279807\u0020微商城\u002013279950\n")
                .toString();
        xmMessagePusher.pushMessageByMisIds(Collections.singletonList("linxiaorui"), content);
    }
}
