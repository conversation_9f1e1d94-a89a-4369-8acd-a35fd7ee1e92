package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.meituan.linz.boot.util.TimeUtils;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.*;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.response.RiderOperateTResponse;
import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.TimeUtil;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/16
 */
class RiderOperateThriftServiceImplTest extends TestBase {

    @Resource
    private RiderOperateThriftServiceImpl riderOperateThriftService;

    @Test
    void acceptOperateNotValid() {
        long deliveryOrderId = 24L;
        String operateName = "qianteng";
        String operatePhone = "***********";
        RiderOperateTResponse accept = riderOperateThriftService.accept(new RiderOperateTRequest(deliveryOrderId, 0L, operateName, operatePhone, null, null, null, null));
        Assert.assertNotNull(accept);
        Assert.assertTrue(accept.getStatus().code == 200);
    }

    @Test
    void acceptOperateStoreNotValid() {
        long deliveryOrderId = 24L;
        String operateName = "qianteng";
        String operatePhone = "***********";
        Long operateId = 11L;
        RiderOperateTResponse accept = riderOperateThriftService.accept(new RiderOperateTRequest(deliveryOrderId, operateId, operateName, operatePhone, null, null, null, null));
        Assert.assertNotNull(accept);
        Assert.assertTrue(accept.getStatus().code == 11043);
    }

    @Test
    void acceptOperateNormal() {
        long deliveryOrderId = 48L;
        String operateName = "qianteng";
        String operatePhone = "***********";
        Long operateId = 11L;
        RiderOperateTResponse accept = riderOperateThriftService.accept(new RiderOperateTRequest(deliveryOrderId, operateId, operateName, operatePhone, null, null, null, null));
        Assert.assertNotNull(accept);
        Assert.assertTrue(accept.getStatus().code == 0);
    }

    @Test
    void acceptOperateIdpoment() {
        long deliveryOrderId = 48L;
        String operateName = "qianteng";
        String operatePhone = "***********";
        Long operateId = 11L;
        RiderOperateTResponse accept = riderOperateThriftService.accept(new RiderOperateTRequest(deliveryOrderId, operateId, operateName, operatePhone, null, null, null, null));
        Assert.assertNotNull(accept);
        Assert.assertTrue(accept.getStatus().code == 0);
    }

    @Test
    void acceptOtherAlreadyOperate() {
        long deliveryOrderId = 48L;
        String operateName = "qianteng";
        String operatePhone = "13444444445";
        Long operateId = 11L;
        RiderOperateTResponse accept = riderOperateThriftService.accept(new RiderOperateTRequest(deliveryOrderId, operateId, operateName, operatePhone, null, null, null, null));
        Assert.assertNotNull(accept);
        Assert.assertTrue(accept.getStatus().code == 11040);
    }

    @Test
    void takeAwayNormal() {
        long deliveryOrderId = 48L;
        String operateName = "qianteng";
        String operatePhone = "***********";
        Long operateId = 11L;
        RiderOperateTResponse res = riderOperateThriftService.takeAway(new RiderOperateTRequest(deliveryOrderId, operateId, operateName, operatePhone, null,null,null,null));
        Assert.assertNotNull(res);
        Assert.assertTrue(res.getStatus().code == 0);
    }


    @Test
    void takeAwayNormalConcurrent() {
        takeAwayRunnable(11L);
        takeAwayRunnable(1000000);
    }

    private void takeAwayRunnable(long accountId) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                long deliveryOrderId = 48L;
                String operateName = "qianteng";
                String operatePhone = "***********";
                RiderOperateTResponse res = riderOperateThriftService.takeAway(new RiderOperateTRequest(deliveryOrderId, accountId, operateName, operatePhone, null, null, null,null));
                try {
                    Thread.sleep(1000L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                Assert.assertTrue(res == null);
            }
        }).start();
    }

    @Test
    public void riderArrivalLocation() {
        RiderArrivalLocationTRequest request = new RiderArrivalLocationTRequest();
        request.setLatitude("30.***************");
        request.setLongitude("104.**************");
        request.setRiderAccountId(367237L);
        request.setDeliveryOrderId(13047L);
        RiderOperateTResponse response = riderOperateThriftService.riderArrivalLocation(request);
        Assert.assertEquals(Status.SUCCESS,response.getStatus());
    }

    @Test
    public void  riderLocatingException() {
        RiderLocatingExceptionTRequest request = new RiderLocatingExceptionTRequest();
        request.setRiderAccountId(123456L);
        request.setExceptionType(1);
        request.setPhoneOS("ios");
        request.setManufacturer("oppo");
        request.setUtime(System.currentTimeMillis());
        request.setUuid("**********************************");
        RiderOperateTResponse response = riderOperateThriftService.riderLocatingException(request);
        Assert.assertEquals(Status.SUCCESS,response.getStatus());
    }

    @Test
    public void  riderLocation() {
        RiderLocationRequest request = new RiderLocationRequest();
        request.setRiderAccountId(123456L);
        request.setTenantId(1000395L);
        request.setStoreId(7346736L);
        request.setLongitude("104.**************");
        request.setLatitude("30.***************");
        request.setRiderName("hahaha");
        request.setRiderPhone("**********");
        request.setTime(LocalDateTime.now().toString());
        RiderOperateTResponse response = riderOperateThriftService.riderLocation(request);
        Assert.assertEquals(Status.SUCCESS.code,response.getStatus().code);
    }

    @Test
    public void  batchPostRiderLocatingLog() {
        BatchPostRiderLocatingLogTRequest request = new BatchPostRiderLocatingLogTRequest();
        RiderLocatingLogTRequest logTRequest1 = new RiderLocatingLogTRequest();
        logTRequest1.setRiderAccountId(123456L);
        logTRequest1.setLongitude("104.**************");
        logTRequest1.setLatitude("30.***************");
        logTRequest1.setLogType(0);
        logTRequest1.setManufacturer("apple");
        logTRequest1.setUtime(System.currentTimeMillis());
        logTRequest1.setUuid("****************************");
        logTRequest1.setPhoneOS("ios");

        RiderLocatingLogTRequest logTRequest2 = new RiderLocatingLogTRequest();
        logTRequest2.setRiderAccountId(123456L);
        logTRequest2.setExceptionType(2);
        logTRequest2.setLogType(1);
        logTRequest2.setLocationIsUsed(true);
        logTRequest2.setManufacturer("vivo");
        logTRequest2.setUtime(System.currentTimeMillis());
        logTRequest2.setUuid("****************************");
        logTRequest2.setPhoneOS("android");


        request.setLocatingLogList(new ArrayList<>());
        request.getLocatingLogList().add(logTRequest1);
        request.getLocatingLogList().add(logTRequest2);
        RiderOperateTResponse response = riderOperateThriftService.batchPostRiderLocatingLog(request);

        Assert.assertEquals(Status.SUCCESS.code,response.getStatus().code);
    }

    @Test
    public void reportException() {
        RiderDeliveryExceptionTRequest request = new RiderDeliveryExceptionTRequest();
        request.setOrderBizType(103);
        request.setComment("这是备注哈哈哈哈");
        request.setTenantId(1001197L);
        request.setStoreId(49867245L);
        request.setExceptionType(2);
        request.setExceptionSubType(2);
        request.setChannelOrderId(275301313209136172L);
        request.setDeliveryOrderId(19587L);
        request.setDaySeq(20);
        request.setRiderAccountId(62835L);
        request.setRiderAccountName("林晓蕊");
        request.setPayTime(TimeUtil.toMilliSeconds(LocalDateTime.of(2022,7,4,16,28,0)));
        RiderOperateTResponse tResponse = riderOperateThriftService.reportDeliveryException(request);
        Assert.assertEquals(tResponse.getStatus(),Status.SUCCESS);
    }

}