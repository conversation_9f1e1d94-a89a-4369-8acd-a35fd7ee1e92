package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.TOperator;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.request.QueryStoreRemindConfigRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.request.SaveStoreRemindConfigRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.response.DeliveryRemindConfigResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.response.RemindConfigCommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Slf4j
@ActiveProfiles("test")
public class DeliveryRemindConfigThriftServiceImplTest extends TestBase {
    @Resource
    DeliveryRemindConfigThriftServiceImpl deliveryRemindConfigThriftService;

    @Test
    public void queryStoreDeliveryRemindConfig_success() {
        QueryStoreRemindConfigRequest request = new QueryStoreRemindConfigRequest(1001197L, 49867245L);
        DeliveryRemindConfigResponse deliveryRemindConfigResponse =
                deliveryRemindConfigThriftService.queryStoreDeliveryRemindConfig(request);

        log.info("deliveryRemindConfigResponse: {}", deliveryRemindConfigResponse);

    }
    
    @Test
    public void saveStoreDeliveryRemindConfig_success() {
        TOperator tOperator = new TOperator(12L, "test12", 12L);


        SaveStoreRemindConfigRequest request = new SaveStoreRemindConfigRequest(1001197L, 49867245L, null, tOperator, null);
        RemindConfigCommonResponse response = deliveryRemindConfigThriftService.saveStoreDeliveryRemindConfig(request);

        log.info("RemindConfigCommonResponse: {}", response);
        Assert.assertNotEquals(Status.SUCCESS, response.getStatus());
        Assert.assertEquals(FailureCodeEnum.INVALID_PARAM.getCode(), response.getStatus().getCode());

        // 消息提醒人员为空，也应该正常返回
        request.setRecipients(Collections.emptyList());
        request.setDhRecipients(Collections.emptyList());
        response = deliveryRemindConfigThriftService.saveStoreDeliveryRemindConfig(request);

        log.info("RemindConfigCommonResponse: {}", response);
        Assert.assertEquals(Status.SUCCESS, response.getStatus());
        Assert.assertEquals(FailureCodeEnum.SUCCESS.getCode(), response.getStatus().getCode());

        QueryStoreRemindConfigRequest queryStoreRemindConfigRequest = new QueryStoreRemindConfigRequest(1001197L, 49867245L);
        DeliveryRemindConfigResponse deliveryRemindConfigResponse =
                deliveryRemindConfigThriftService.queryStoreDeliveryRemindConfig(queryStoreRemindConfigRequest);

        log.info("deliveryRemindConfigResponse: {}", deliveryRemindConfigResponse);
        Assert.assertEquals(0L, deliveryRemindConfigResponse.getConfig().getRecipients().size());
        Assert.assertEquals(0L, deliveryRemindConfigResponse.getConfig().getRecipients().size());


        List<String> recipients = new ArrayList<String>(Arrays.asList("muxiaobo123", "muxiaobo", "linxiaorui", "xuninghan"));
        request.setRecipients(recipients);


        // receivers的用户超过人数限制
        List<String> dfRecipients = new ArrayList<String>(Arrays.asList("muxiaobo123", "muxiaobo", "linxiaorui", "<EMAIL>", "testdx_001"));
        for (int i = 0; i < 20; ++i) {
            dfRecipients.add("test_" + i);
        }
        request.setDhRecipients(dfRecipients);

        response = deliveryRemindConfigThriftService.saveStoreDeliveryRemindConfig(request);
        log.info("RemindConfigCommonResponse: {}", response);
        Assert.assertNotEquals(Status.SUCCESS, response.getStatus());

        // receivers的用户部分不存在也是可以发送成功的
        dfRecipients = new ArrayList<String>(Arrays.asList("muxiaobo123", "muxiaobo", "linxiaorui", "<EMAIL>", "testdx_001"));
        request.setDhRecipients(dfRecipients);

        response = deliveryRemindConfigThriftService.saveStoreDeliveryRemindConfig(request);
        log.info("RemindConfigCommonResponse: {}", response);
        Assert.assertEquals(Status.SUCCESS, response.getStatus());

        // 打印当时的数据
        queryStoreDeliveryRemindConfig_success();


    }
}
