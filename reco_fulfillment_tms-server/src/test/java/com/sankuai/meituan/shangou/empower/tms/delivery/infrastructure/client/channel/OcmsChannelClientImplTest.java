package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.channel;

import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.OcmsChannelClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import org.junit.Test;

import javax.annotation.Resource;

public class OcmsChannelClientImplTest extends TestBase {

    @Resource
    private OcmsChannelClient ocmsChannelClient;
    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Test
    public void syncRiderPosition() throws Exception{
        CoordinatePoint point = new CoordinatePoint("1","1");
        DeliveryOrder deliveryOrder = deliveryOrderRepository.getDeliveryOrder(794l);

        for (int i = 1; i < 10; i++) {
            ocmsChannelClient.syncRiderLocation(deliveryOrder, point);
            System.out.println("同步" + i + "次");

            Thread.sleep(30000);
            point = new CoordinatePoint(String.valueOf(i*10),String.valueOf(i*8));
        }

    }

}
