<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="commonThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="5000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="openAggDeliveryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.OpenAggDeliveryThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="selfDeliveryPoiConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.dmp"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>


    <bean id="abnOrderService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.AbnOrderService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tEmployeeInfoService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.bizmng.labor.api.employee.TEmployeeInfoService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.bizmng.labor"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="yodaService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.rc.yoda.thrift.service.YodaService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.rc.yoda.yoda"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="riderQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tWarehouseService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value=" com.sankuai.shangou.infra.osw.api.poi.warehouse.TWarehouseService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.infra.osw"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="bizOrderThriftServiceBiz" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.shangou.saas.o2o.service.BizOrderThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="fulfillmentOrderSearchThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ofc_ofw_order_search_thrift_timeout','10000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.ofc.ofw"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="pullNewThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.sgxsupply.wxmall.bizmanagement.client.thrift.PullNewThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgxsupply.wxmall.bizmanagement"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>


    <bean id="yodaDataRemoteAppkey" class="java.lang.String">
        <constructor-arg value="com.sankuai.rc.yoda.data"/> <!-- 目标 Server Appkey  -->
    </bean>

    <bean id="yodaDataLocalAppkey" class="java.lang.String">
        <constructor-arg value="${app.name}"/> <!-- 填写自身服务的AppKey标识 -->
    </bean>

    <bean id="yodaDataThriftTimeout" class="java.lang.String">
        <constructor-arg value="5000"/>  <!-- 设置接口超时时间（ms） -->
    </bean>


    <bean id="channelPoiManageThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <import resource="classpath:yoda-data-client.xml"></import>

    <bean id="tDxMsgService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.waima.support.api.service.msg.TDxMsgService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.waima.support"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="tPunishService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.waima.support.api.service.punish.TPunishService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.waima.support"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="tEmployeeService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.infra.osw.api.org.TEmployeeService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.infra.osw"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>


    <bean id="smileMsgRobotKey" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="smileMsgRobotKey"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="smileMsgRobotToken" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="smileMsgRobotToken"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="smileMsgRobotPubId" class="com.meituan.service.inf.kms.value.KMSLongValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="smileMsgRobotPubId"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="authThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="dhCid" class="com.meituan.service.inf.kms.value.KMSLongValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="dhCid"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="meituanCid" class="com.meituan.service.inf.kms.value.KMSLongValue">
        <property name="appKey" value="${app.name}"/>
        <property name="name" value="meituanCid"/>
        <property name="retryCount" value="5"/>
    </bean>

    <bean id="pushMessageThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.xm.pubapi.thrift.PushMessageServiceI"/>
        <property name="timeout" value="3000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.xm.pubapi"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="pushThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.saas.message.service.PushThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saasmessage_thrift_timeout','5000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasmessage"/>
        <property name="remoteServerPort" value="8414"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tOrgService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.infra.osw.api.org.TOrgService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.infra.osw"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="outboundOrderLackLockedThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.query.thrift.lackLocked.OutboundOrderLackLockedThriftService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.fulfill.pickquery"/>
        <property name="timeout" value="5000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>


</beans>
