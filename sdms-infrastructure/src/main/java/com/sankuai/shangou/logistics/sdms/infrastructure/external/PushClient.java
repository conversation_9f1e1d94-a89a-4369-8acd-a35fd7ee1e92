package com.sankuai.shangou.logistics.sdms.infrastructure.external;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.saas.message.request.push.FusionPushSendRequest;
import com.sankuai.meituan.shangou.saas.message.service.PushThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 推送客户端
 * @date 2025-03-21
 */
@Rhino
@Service
@Slf4j
public class PushClient {
    @Resource
    private PushThriftService pushThriftService;

    private static final String APP_CODE = "5";
    private static final Long SMILE_TASK = 1161L;
    private static final Long NEAR_EXPIRATION_SMILE_TASK = 1162L;


    /**
     * 发送微笑行动任务下发push
     */
    @Degrade(rhinoKey = "PushClient.pushSmileTask", fallBackMethod = "pushSmileTaskFallback", timeoutInMilliseconds = 1000)
    public void pushSmileTask(Long tenantId, Long accountId, Long storeId) {
        baseSmilePush(tenantId, accountId, storeId, SMILE_TASK);
    }

    /**
     * 发送即将过期微笑行动任务下发push
     */
    @Degrade(rhinoKey = "PushClient.pushNearExpirationSmileTask", fallBackMethod = "pushNearExpirationSmileTaskFallback", timeoutInMilliseconds = 1000)
    public void pushSmileNearExpirationTask(Long tenantId, Long accountId, Long storeId) {
        baseSmilePush(tenantId, accountId, storeId, NEAR_EXPIRATION_SMILE_TASK);
    }

    private void baseSmilePush(Long tenantId, Long accountId, Long storeId, Long eventConfigId) {
        try {
            FusionPushSendRequest request = new FusionPushSendRequest();
            request.setAppCode(APP_CODE);
            request.setEventConfigId(eventConfigId);
            request.setTenantId(tenantId);
            request.setPoiId(storeId);
            request.setAccountIds(Lists.newArrayList(accountId));
            request.setMsgPropertyMap(ImmutableMap.of(
                    "tenantId", String.valueOf(tenantId),
                    "storeId", String.valueOf(storeId)
            ));
            pushThriftService.sendFusionPush(request);
            log.info("发送push成功");
        } catch (TException e) {
            Cat.logEvent("PUSH_SMILE_TASK", "fail");
            log.error("发送push失败", e);
        }
    }

    public void pushSmileTaskFallback(Long tenantId, Long accountId, Long storeId) {
        log.warn("PushClient.pushSmileTask 降级");
    }

    public void pushNearExpirationSmileTaskFallback(Long tenantId, Long accountId, Long storeId) {
        log.warn("PushClient.pushNearExpirationSmileTask 降级");
    }
}
