package com.sankuai.shangou.logistics.sdms.infrastructure.external;

import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiDetailInfoResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.infra.osw.api.poi.store.dto.request.WarehouseIdsRequest;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.TWarehouseService;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import com.sankuai.shangou.logistics.sdms.domain.utils.IListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-07-09
 * @email <EMAIL>
 */
@Slf4j
@Rhino
public class WarehouseQueryWrapper {

    @Resource
    private TWarehouseService tWarehouseService;
    @Resource
    private ChannelPoiManageThriftService channelPoiManageThriftService;

    public WarehouseDTO queryWarehouse(long merchantId, long warehouseId) {
        try {
            WarehouseIdsRequest request = new WarehouseIdsRequest();
            request.setTenantId(merchantId);
            request.setWarehouseIds(Lists.newArrayList(warehouseId));
            TResult<List<WarehouseDTO>> result = tWarehouseService.batchQueryWarehouseById(request);
            if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
                throw new ThirdPartyException("查询仓库信息错误");
            }
            return result.getData().get(0);
        } catch (Exception e) {
            log.error("invoke tWarehouseService.queryWarehouse error", e);
            throw new ThirdPartyException("查询仓库信息错误");
        }
    }

    public ChannelPoiInfoDTO queryChannelPoiInfoDTO(long tenantId, long warehouseId, int channelId) {
        try {
            PoiDetailInfoResponse response = channelPoiManageThriftService.queryPoiDetailInfoByPoiId(tenantId, warehouseId);
            if (!response.getStatus().isSuccess() || Objects.isNull(response.getPoiDetailInfoDTO()) || CollectionUtils.isEmpty(response.getPoiDetailInfoDTO().getChannelPoiInfoList())) {
                log.error("channelPoiManageThriftService.queryPoiDetailInfoByPoiId return error, response = {}", response);
                throw new ThirdPartyException("查询渠道门店信息信息错误");
            }
            List<ChannelPoiInfoDTO> channelPoiInfoDTOS = IListUtils.nullSafeFilterElement(
                    response.getPoiDetailInfoDTO().getChannelPoiInfoList(),
                    channelPoiInfoDTO -> Objects.equals(channelPoiInfoDTO.getChannelId(), channelId)
            );
            return CollectionUtils.isNotEmpty(channelPoiInfoDTOS) ? channelPoiInfoDTOS.get(0) : response.getPoiDetailInfoDTO().getChannelPoiInfoList().get(0);
        } catch (Exception e) {
            log.error("invoke channelPoiManageThriftService.queryPoiDetailInfoByPoiId error", e);
            throw new ThirdPartyException("查询渠道门店信息信息错误", e);
        }
    }

}
