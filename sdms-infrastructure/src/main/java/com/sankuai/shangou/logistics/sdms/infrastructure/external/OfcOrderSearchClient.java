package com.sankuai.shangou.logistics.sdms.infrastructure.external;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.ChannelOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.FulfillmentOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.response.order.FulfillmentOrderDetailResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.logistics.sdms.infrastructure.utils.MccUtils;
import com.sankuai.shangou.logistics.sdms.infrastructure.utils.RetryTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @date 2025-03-12
 */
@Rhino
@Service
@Slf4j
public class OfcOrderSearchClient {

    @Resource
    private FulfillmentOrderSearchThriftService fulfillmentOrderSearchThriftService;

    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "OfcOrderSearchClient.queryOrderIsContainSealProduct", fallBackMethod = "queryOrderIsContainSealProductFallback", timeoutInMilliseconds = 3000)
    public boolean queryOrderIsContainSealProduct(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType) {
        try {
            FulfillmentOrderIdKeyReq request = new FulfillmentOrderIdKeyReq();
            request.setTenantId(tenantId);
            request.setWarehouseId(storeId);
            request.setChannelOrderIdKey(new ChannelOrderIdKeyReq(orderBizType, channelOrderId));
            request.setMaster(true);
            // 重试
            RetryTemplate retryTemplate = RetryTemplateUtil.simpleWithFixedRetry(MccUtils.queryIsContainSealProductRetryCount(), MccUtils.queryIsContainSealProductRetryPeriod());
            Optional<FulfillmentOrderDetailDTO> fulfillmentOrderDetailOpt = retryTemplate.execute(
                    (RetryCallback<Optional<FulfillmentOrderDetailDTO>, Exception>) retryContext -> {
                        FulfillmentOrderDetailResponse response = fulfillmentOrderSearchThriftService.searchFulfillmentOrderByFulfillmentOrderId(request);
                        if (response == null || response.getStatus() == null) {
                            throw new BizException("查询履约单失败");
                        }

                        if (response.getStatus().getCode() != 0) {
                            throw new BizException("查询履约单业务失败");
                        }

                        if (CollectionUtils.isEmpty(response.getFulfillmentOrderList())) {
                            throw new BizException("未查询到履约单");
                        }
                        FulfillmentOrderDetailDTO fulfillmentOrderDetailDTO = response.getFulfillmentOrderList().get(0);
                        return Optional.ofNullable(fulfillmentOrderDetailDTO);
                    }
            );

            if (!fulfillmentOrderDetailOpt.isPresent()) {
                throw new BizException("未查询到履约单");
            }

            return Objects.equals(fulfillmentOrderDetailOpt.get().getIsContainSealProduct(), true);
        } catch (Exception e) {
            log.error("查询订单是否包含封签商品失败", e);
            return false;
        }
    }

    public boolean queryOrderIsContainSealProductFallback(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType) {
        log.error("OfcOrderSearchClient.queryOrderIsContainSealProduct 发生降级");
        return false;
    }


}
