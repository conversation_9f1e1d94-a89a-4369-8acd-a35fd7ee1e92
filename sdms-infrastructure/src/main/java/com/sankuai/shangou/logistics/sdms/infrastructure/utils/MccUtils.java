package com.sankuai.shangou.logistics.sdms.infrastructure.utils;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/7/14 19:54
 **/
@Slf4j
public class MccUtils {

    public static String getSignAndEncUtilPublicKey() {
        return Lion.getConfigRepository().get("sign.and.enc.util.public.key", "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDlse+5QxAw8wXqc/I+hJzqkXUSW6KU3BjGncQFKfu4HTs3+RBJmjphZuaGz4CtgyFYXbtXJLQeXP4VzdDsa4xW+RJAYONr2Z5HYQVQ0QJ5beJSEF2qiIkQwC1ysdbgdAMVarGO2lli3szsCjsWQa9DhubDz48kVXzD9Uw3dvM0AwIDAQAB");
    }

    public static List<Integer> getFilterOrderPullNewEventTypes () {
        return Lion.getConfigRepository().getList("order.pull.new.event.types", Integer.class, Arrays.asList(5,7,20,26,27,46,54,55));
    }

    public static List<String> getInterceptAssignIdentifyCallbackCodes () {
        return Lion.getConfigRepository().getList("intercept.assign.identify.callback.codes", String.class, Arrays.asList("121065", "121061"));
    }

    public static Map<String, String> getYodaBisinsessErrCodeMap () {
        return Lion.getConfigRepository().getMap("yoda.business.err.code.map", String.class, new HashMap<>());
    }

    public static List<Long> getAssignVerifyTaskAccountBlackList() {
        return Lion.getConfigRepository().getList("assign.verify.task.account.black.list", Long.class, Collections.emptyList());
    }

    public static boolean getYodaForcedInterceptSwitch() {
        return Lion.getConfigRepository().getBooleanValue("yoda.force.intercept.switch", true);
    }

    public static boolean isVerifyPunishGrayStore(long storeId) {
        try {
            List<Long> grayStores = Lion.getConfigRepository().getList("verify.punish.gray.store", Long.class, Lists.newArrayList());
            if (CollectionUtils.isEmpty(grayStores)) {
                return false;
            }
            if (grayStores.size() == 1 && Objects.equals(grayStores.get(0), -1L)) {
                return true;
            }
            return grayStores.contains(storeId);

        } catch (Exception e) {
            log.error("isVerifyPunishGrayStore error", e);
            return false;
        }
    }

    /**
     * 查询是否包含封签商品重试次数
     */
    public static int queryIsContainSealProductRetryCount() {
        return Lion.getConfigRepository().getIntValue("query.contain.seal.product.retry.count", 3);
    }

    /**
     * 查询是否包含封签商品重试周期(毫秒)
     */
    public static int queryIsContainSealProductRetryPeriod() {
        return Lion.getConfigRepository().getIntValue("query.contain.seal.product.retry.period", 100);
    }

    public static boolean isSmilePushGrayStore(long storeId) {
        try {
            List<Long> grayStores = Lion.getConfigRepository().getList("smile.push.gray.store", Long.class, Lists.newArrayList());
            if (CollectionUtils.isEmpty(grayStores)) {
                return false;
            }
            if (grayStores.size() == 1 && Objects.equals(grayStores.get(0), -1L)) {
                return true;
            }
            return grayStores.contains(storeId);
        } catch (Exception e) {
            log.error("isSmileMessagePushSwitch error", e);
            return false;
        }
    }

    public static boolean getPickSelectLackLockedSwitch() {
        return Lion.getConfigRepository().getBooleanValue("pick.select.lack.locked.switch", true);
    }
}
