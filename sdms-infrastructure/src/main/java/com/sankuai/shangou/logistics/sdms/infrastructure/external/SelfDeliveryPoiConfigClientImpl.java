package com.sankuai.shangou.logistics.sdms.infrastructure.external;

import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.thrift.publisher.utils.CheckUtils;
import com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigThriftService;
import com.sankuai.shangou.logistics.delivery.poi.dto.SelfDeliveryPoiConfigDTO;
import com.sankuai.shangou.logistics.sdms.domain.entity.config.SelfDeliveryPoiConfig;
import com.sankuai.shangou.logistics.sdms.domain.external.SelfDeliveryPoiConfigClient;
import com.sankuai.shangou.logistics.sdms.domain.utils.IntBooleanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023-09-04
 * @email <EMAIL>
 */
@Slf4j
@Service
public class SelfDeliveryPoiConfigClientImpl implements SelfDeliveryPoiConfigClient {

    @Resource
    private SelfDeliveryPoiConfigThriftService selfDeliveryPoiConfigThriftService;

    @Override
    public SelfDeliveryPoiConfig querySelfDeliveryConfig(long tenantId, long poiId) {
        TResult<SelfDeliveryPoiConfigDTO> result = selfDeliveryPoiConfigThriftService.querySelfDeliveryConfig(tenantId, poiId);
        CheckUtils.assertTrue(result.isSuccess(), "查询配置错误");
        CheckUtils.checkNotNull(result.getData(), "未查询到配送门店配置");
        return convertFromDTO(result.getData());
    }

    private SelfDeliveryPoiConfig convertFromDTO(SelfDeliveryPoiConfigDTO dto) {
        return new SelfDeliveryPoiConfig(
                dto.getId(), dto.getTenantId(), dto.getPoiId(), IntBooleanUtils.toBool(dto.getEnableTurnDelivery())
        );
    }
}
