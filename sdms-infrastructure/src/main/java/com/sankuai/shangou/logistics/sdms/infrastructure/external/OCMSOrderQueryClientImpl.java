package com.sankuai.shangou.logistics.sdms.infrastructure.external;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryRequest;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderQueryResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.logistics.sdms.domain.entity.order.OrderTagInfo;
import com.sankuai.shangou.logistics.sdms.domain.external.OCMSOrderQueryClient;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/7/9 17:46
 **/
@Service
@Slf4j
public class OCMSOrderQueryClientImpl implements OCMSOrderQueryClient {

    @Resource
    private BizOrderThriftService bizOrderThriftServiceBiz;

    @Override
    public OrderTagInfo checkOrderTag(Long tenantId, Long storeId, String viewOrderId, Integer orderBizType) {
        BizOrderQueryRequest request = new BizOrderQueryRequest();
        request.setOrderBizType(orderBizType);
        request.setViewOrderId(viewOrderId);
        request.setTenantId(tenantId);
        request.setShopId(storeId);
        BizOrderQueryResponse response;
        try {
            log.info("start invoke bizOrderThriftServiceBiz.query, request: {}", request);
            response = bizOrderThriftServiceBiz.query(request);
            log.info("end invoke bizOrderThriftServiceBiz.query, response: {}", response);
        } catch (Exception e) {
            log.error("查询订单详情失败", e);
            throw new ThirdPartyException("查询订单详情失败");
        }

        if (response.getStatus().getCode() != 0) {
            throw new BizException(response.getStatus().getMessage());
        }

        if (response.getBizOrderModel() == null) {
            throw new BizException("未查询到订单信息");
        }

        Boolean isOneYuanOrder = parseOneYuanOrderTag(response.getBizOrderModel());
        Boolean isOfflinePromoteOrder = parsePromoteInfoTag(response.getBizOrderModel());

        return new OrderTagInfo(isOneYuanOrder, isOfflinePromoteOrder);
    }

    public Boolean parseOneYuanOrderTag(BizOrderModel order) {
        try {
            JSONObject jsonObject = JSON.parseObject(order.getNewExtData(), JSONObject.class);
            if (Objects.nonNull(jsonObject) && Objects.nonNull(jsonObject.getJSONArray("tagModels"))) {
                for (Object tagModel : jsonObject.getJSONArray("tagModels")) {
                    Map<String, Object> tagModelMap = (Map<String, Object>) tagModel;
                    if (Objects.equals(tagModelMap.get("type"), 102)) {
                        return true;
                    }
                }
            }

            return false;

        } catch (Exception e) {
            Cat.logEvent("PARSE_ONE_YUAN_ORDER", "ERROR");
            log.error("解析一元单错误", e);
            return null;
        }
    }

    public Boolean parsePromoteInfoTag(BizOrderModel order) {
        try {
            String commonInfo = order.getCommonInfo();
            if (StringUtils.isBlank(commonInfo)) {
                return false;
            }

            OrderExtInfo orderExtInfo = JSON.parseObject(order.getCommonInfo(), OrderExtInfo.class);
            if (orderExtInfo != null && Objects.equals(orderExtInfo.getPullNewSelfPickGoodsOrder(), true) && orderExtInfo.getPullNewAccountId() != null) {
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("getOrderExtInfo throws exception",e);
            return false;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderExtInfo {

        /**
         * 推广员账号id
         */
        @Nullable
        private Long pullNewAccountId;

        /**
         * 是否地推自提商品单
         */
        @Nullable
        private Boolean pullNewSelfPickGoodsOrder;

    }
}
