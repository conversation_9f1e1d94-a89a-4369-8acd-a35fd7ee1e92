package com.sankuai.shangou.logistics.sdms.infrastructure.external;

import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.pullnew.QueryOrderEventRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.pullnew.QueryOrderEventResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.thrift.PullNewThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.logistics.sdms.domain.external.PullNewThriftServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/7/16 10:53
 **/
@Service
@Slf4j
public class PullNewThriftServiceClientImpl implements PullNewThriftServiceClient {
    @Resource
    private PullNewThriftService pullNewThriftService;

    @Override
    public Boolean checkOrderIsSpecialType(String viewOrderId, List<Integer> eventTypeList) {
        QueryOrderEventRequest request = new QueryOrderEventRequest();
        request.setEventTypeList(eventTypeList);
        request.setOrderViewIdList(Collections.singletonList(viewOrderId));
        log.info("start invoke pullNewThriftService.queryOrderEvent, request: {}", request);
        QueryOrderEventResponse response = pullNewThriftService.queryOrderEvent(request);
        log.info("end invoke pullNewThriftService.queryOrderEvent, response: {}", response);
        if (!Objects.equals(response.getStatus().getCode(),0)) {
            throw new BizException("查询订单推广信息失败," + response.getStatus().getMsg());
        }

        return MapUtils.isNotEmpty(response.getOrderEventMap())
                && response.getOrderEventMap().containsKey(viewOrderId)
                && CollectionUtils.isNotEmpty(response.getOrderEventMap().get(viewOrderId));
    }
}
