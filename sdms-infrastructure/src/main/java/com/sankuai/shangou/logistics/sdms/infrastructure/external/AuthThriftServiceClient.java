package com.sankuai.shangou.logistics.sdms.infrastructure.external;

import com.alibaba.fastjson.JSON;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountDetailByTenantIdAndStaffIdsRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountInfoListResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/9/24 11:00
 **/
@Rhino
@Slf4j
public class AuthThriftServiceClient {

    @Resource
    private AuthThriftService.Iface authThriftService;

    @Degrade(rhinoKey = "AuthThriftServiceClient.queryAccountDetailByTenantIdAndStaffIds", fallBackMethod = "queryAccountDetailByTenantIdAndStaffIdsFallback", timeoutInMilliseconds = 2000)
    public List<AccountInfoVo> queryAccountDetailByTenantIdAndStaffIds(long tenantId, List<Long> employeeIds) {
        QueryAccountDetailByTenantIdAndStaffIdsRequest request = new QueryAccountDetailByTenantIdAndStaffIdsRequest();
        request.setTenantId(tenantId);
        request.setStaffIds(employeeIds);

        QueryAccountInfoListResponse accountResponse;
        try {
            log.info("start invoke authThriftServiceClient.queryAccountDetailByTenantIdAndStaffIds, request: {}", JSON.toJSONString(request));
            accountResponse = authThriftService.queryAccountDetailByTenantIdAndStaffIds(request);
            log.info("start invoke authThriftServiceClient.queryAccountDetailByTenantIdAndStaffIds, response: {}", JSON.toJSONString(accountResponse));
        } catch (Exception e) {
            log.error("查询账号信息失败", e);
            throw new ThirdPartyException("查询账号信息失败");
        }

        if (!Objects.equals(accountResponse.getResult().getCode(), 0)) {
            throw new BizException("查询账号信息失败:" + accountResponse.getResult().getMsg());
        }
        return accountResponse.getAccountInfoList();
    }


    public List<AccountInfoVo> queryAccountDetailByTenantIdAndStaffIdsFallback(long tenantId, List<Long> employeeIds) {
        log.error("AuthThriftServiceClient.queryAccountDetailByTenantIdAndStaffIds 发生降级");
        return Collections.emptyList();
    }
}
