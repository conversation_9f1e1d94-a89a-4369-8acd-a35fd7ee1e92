package com.sankuai.shangou.logistics.sdms.infrastructure.external;

import com.sankuai.shangou.bizmng.labor.api.employee.TEmployeeInfoService;
import com.sankuai.shangou.bizmng.labor.api.employee.dto.LaborDTO;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.sdms.domain.external.LaborClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/7/14 21:07
 **/
@Service
@Slf4j
public class LaborClientImpl implements LaborClient {
    @Resource
    private TEmployeeInfoService tEmployeeInfoService;

    @Override
    public String queryEmpIdCardByEmpId(Long tenantId, Long employeeId) {
        log.info("start invoke tEmployeeInfoService.queryEmpIdCardByEmpId, tenantId: {}, employeeId: {}", tenantId, employeeId);
        TResult<Map<Long, String>> tResult = tEmployeeInfoService.queryEmpIdCardByEmpId(tenantId,
                Collections.singletonList(employeeId));
        log.info("end invoke tEmployeeInfoService.queryEmpIdCardByEmpId, tResult: {}", tResult);

        if (!tResult.isSuccess()) {
            throw new BizException(tResult.getMsg());
        }

        if (MapUtils.isEmpty(tResult.getData()) || !tResult.getData().containsKey(employeeId)) {
            throw new BizException("未查到员工身份证号");
        }
        return tResult.getData().get(employeeId);
    }

    /**
     * 员工入职时间查询(转岗、二次入职时间会更新)
     */
    @Override
    public Long queryOnboardTimeByEmpId(Long tenantId, Long empId) {
        TResult<List<LaborDTO>> listTResult;
        try {
             listTResult = tEmployeeInfoService.queryLaborInfo(tenantId, Collections.singletonList(empId));
            log.info("查询员工信息, tenantId:{}, empId:{}, result:{}", tenantId, empId, listTResult);
        } catch (Exception e) {
            log.error("查询员工信息异常, tenantId:{}, empId:{}", tenantId, empId, e);
            throw new BizException("查询员工信息异常");
        }

        if (listTResult == null || !listTResult.isSuccess()) {
            log.error("查询员工信息失败, tenantId:{}, empId:{}, result:{}", tenantId, empId, listTResult);
            throw new BizException("查询员工信息失败");
        }
        if (CollectionUtils.isEmpty(listTResult.getData())) {
            return null;
        }
        return listTResult.getData().get(0).getOnboardTime();
    }
}
