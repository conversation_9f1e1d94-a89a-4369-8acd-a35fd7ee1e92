package com.sankuai.shangou.logistics.sdms.infrastructure.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sankuai.shangou.logistics.sdms.dao.mapper.YodaIdentifyTaskConfigPOMapper;
import com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskConfigPO;
import com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskConfigPOExample;
import com.sankuai.shangou.logistics.sdms.domain.entity.poi.PoiBaseInfo;
import com.sankuai.shangou.logistics.sdms.domain.entity.yoda.VerifyNoControlInfo;
import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.CollectContentTypeEnum;
import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.PunishTypeEnum;
import com.sankuai.shangou.logistics.sdms.domain.entity.yoda.YodaIdentityTaskConfig;
import com.sankuai.shangou.logistics.sdms.domain.repository.YodaIdentityTaskConfigRepository;
import com.sankuai.shangou.logistics.sdms.infrastructure.external.OswClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/9 15:47
 **/
@Service
@Slf4j
public class YodaIdentityTaskConfigRepositoryImpl implements YodaIdentityTaskConfigRepository {
    @Resource
    private YodaIdentifyTaskConfigPOMapper yodaIdentifyTaskConfigPOMapper;

    @Resource
    private OswClient oswClient;

    @Override
    public Optional<YodaIdentityTaskConfig> getHitConfig(Long tenantId, Long storeId) {
        PoiBaseInfo poiBaseInfo = oswClient.queryWarehouseInfo(tenantId, storeId);

        YodaIdentifyTaskConfigPOExample example = new YodaIdentifyTaskConfigPOExample();
        example.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andStoreOperationModeEqualTo(poiBaseInfo.getStoreOperationMode())
                .andIsValidEqualTo(1);
        List<YodaIdentifyTaskConfigPO> configPOS = yodaIdentifyTaskConfigPOMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(configPOS)) {
            return Optional.empty();
        }

        List<YodaIdentityTaskConfig> taskConfigs = configPOS.stream().map(this::transform).collect(Collectors.toList());


        List<YodaIdentityTaskConfig> hitStoreIdConfigs = taskConfigs.stream().filter(taskConfig -> {
                    return taskConfig.getStoreIds().contains(poiBaseInfo.getPoiId())
                            || (taskConfig.getStoreIds().size() == 1 && Objects.equals(taskConfig.getStoreIds().get(0), -1L));
                }).sorted(new Comparator<YodaIdentityTaskConfig>() {
                    @Override
                    public int compare(YodaIdentityTaskConfig o1, YodaIdentityTaskConfig o2) {
                        return -o1.getCreateTime().compareTo(o2.getCreateTime());
                    }
                })
                .collect(Collectors.toList());

        //优先取命中门店维度的配置 && 取最新的配置
        if (CollectionUtils.isNotEmpty(hitStoreIdConfigs)) {
            return Optional.of(hitStoreIdConfigs.get(0));
        }

        List<YodaIdentityTaskConfig> hitCityIdConfigs = taskConfigs.stream().filter(taskConfig -> {
                    return taskConfig.getCityIds().contains(poiBaseInfo.getCityId())
                            || (taskConfig.getCityIds().size() == 1 && Objects.equals(taskConfig.getCityIds().get(0), -1));
                }).sorted(new Comparator<YodaIdentityTaskConfig>() {
                    @Override
                    public int compare(YodaIdentityTaskConfig o1, YodaIdentityTaskConfig o2) {
                        return -o1.getCreateTime().compareTo(o2.getCreateTime());
                    }
                })
                .collect(Collectors.toList());

        //如果没命中门店维度配置, 取命中城市维度的配置 && 取最新的配置
        if (CollectionUtils.isNotEmpty(hitCityIdConfigs)) {
            return Optional.of(hitCityIdConfigs.get(0));
        }

        return Optional.empty();
    }

    @Override
    public Optional<YodaIdentityTaskConfig> selectByPrimaryKey(Long id) {
        YodaIdentifyTaskConfigPO configPO = yodaIdentifyTaskConfigPOMapper.selectByPrimaryKey(id);
        if (Objects.isNull(configPO)) {
            return Optional.empty();
        }
        return Optional.of(transform(configPO));
    }

    @Override
    public List<YodaIdentityTaskConfig> selectByPrimaryKeys(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        YodaIdentifyTaskConfigPOExample example = new YodaIdentifyTaskConfigPOExample();
        example.createCriteria().andIdIn(new ArrayList<>(ids));
        List<YodaIdentifyTaskConfigPO> list = yodaIdentifyTaskConfigPOMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::transform).collect(Collectors.toList());
    }

    private YodaIdentityTaskConfig transform(YodaIdentifyTaskConfigPO po) {
        YodaIdentityTaskConfig taskConfig = new YodaIdentityTaskConfig();
        taskConfig.setId(po.getId());
        taskConfig.setTenantId(po.getTenantId());
        taskConfig.setStoreOperationMode(po.getStoreOperationMode());

        if (StringUtils.isNotBlank(po.getCityList())) {
            taskConfig.setCityIds(JSON.parseArray(po.getCityList(), Integer.class));
        } else {
            taskConfig.setCityIds(Collections.emptyList());
        }

        if (StringUtils.isNotBlank(po.getStoreList())) {
            taskConfig.setStoreIds(JSON.parseArray(po.getStoreList(), Long.class));
        } else {
            taskConfig.setStoreIds(Collections.emptyList());
        }

        if (StringUtils.isNotBlank(po.getCollectContentTypeList())) {
            taskConfig.setCollectContentTypeList(JSON.parseArray(po.getCollectContentTypeList(), Integer.class));
        }
        taskConfig.setCollectMode(po.getCollectMode());
        taskConfig.setCollectInterval(po.getInterval());
        taskConfig.setTaskValidPeriod(po.getTaskValidPeriod());
        taskConfig.setPeriodBeginTime(LocalTime.parse(po.getPeriodBeginTime(), DateTimeFormatter.ofPattern("HH:mm")));
        taskConfig.setPeriodEndTime(LocalTime.parse(po.getPeriodEndTime(), DateTimeFormatter.ofPattern("HH:mm")));
        taskConfig.setMaxCollectTime(po.getPeriodMaxCollectTimes());
        taskConfig.setCollectPeriod(po.getCollectPeriod());
        taskConfig.setCreateTime(po.getCreateTime());
        taskConfig.setUpdateTime(po.getUpdateTime());
        setPunishConfigMap(po, taskConfig);
        if (StringUtils.isNotBlank(po.getPositionList())) {
            taskConfig.setPositionList(JSON.parseArray(po.getPositionList(), String.class));
        }
        taskConfig.setTaskNearExpiration(po.getTaskNearExpiration() == null ? 0 : po.getTaskNearExpiration());
        setNoControlMap(po, taskConfig);
        return taskConfig;
    }

    private void setNoControlMap(YodaIdentifyTaskConfigPO po, YodaIdentityTaskConfig taskConfig) {
        try {
            if (StringUtils.isBlank(po.getNoControl())) {
                return;
            }
            List<VerifyNoControlInfo> verifyNoControlInfos = JSON.parseArray(po.getNoControl(), VerifyNoControlInfo.class);
            if (CollectionUtils.isEmpty(verifyNoControlInfos)) {
                return;
            }
            taskConfig.setNoControl(verifyNoControlInfos);
        } catch (Exception e) {
            log.error("try setNoControlMap error", e);
        }
    }

    private void setPunishConfigMap(YodaIdentifyTaskConfigPO po, YodaIdentityTaskConfig taskConfig) {
        if (StringUtils.isBlank(po.getPunishConfig())) {
            return;
        }

        try {
            Map<String/*collectType*/, Integer/*punishType*/> collectTypePunishMap = JSON.parseObject(po.getPunishConfig(), new TypeReference<Map<String, Integer>>() {
            });
            if (MapUtils.isEmpty(collectTypePunishMap)) {
                return;
            }
            taskConfig.setPunishConfigMap(
                    collectTypePunishMap.entrySet().stream()
                            .collect(Collectors.toMap(
                                    entry -> CollectContentTypeEnum.valueOfCode(Integer.parseInt(entry.getKey())),
                                    entry -> PunishTypeEnum.valueOfCode(entry.getValue())
                            ))
            );

        } catch (Exception e) {
            log.error("try setPunishConfigMap error", e);
        }
    }

}

