package com.sankuai.shangou.logistics.sdms.infrastructure.external;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Rhino;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.dto.EmployeeInfoV2Dto;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.response.EmployeeInfoInDepartmentQueryV2Response;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountInfoListResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeInOrgDTO;
import com.sankuai.shangou.logistics.sdms.domain.entity.push.PushInfo;

import com.sankuai.shangou.waima.support.api.service.msg.TDxMsgService;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import com.sankuai.xm.pubapi.thrift.PusherInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/24 19:07
 **/
@Service
@Rhino
@Slf4j
public class DxMsgServiceClient {
    @Resource
    private Long dhCid;

    @Resource
    private Long meituanCid;

    @Resource
    private PushMessageServiceI.Iface pushMessageThriftServiceClient;

    @Resource
    private AuthThriftServiceClient authThriftServiceClient;

    @Resource
    private OswClient oswClient;

    private static final int ACCOUNT_VALID = 1;

    public void sendCrossEnterpriseDxMsgByPosition(Long tenantId, String key, String token, Long pubId,
                                                   String msgContent, List<Long> positionIds, Long storeId,
                                                   List<String> whiteMidIds) {

        try {
            PusherInfo pusherInfo = new PusherInfo().setAppkey(key).setToken(token).setFromUid(pubId);
            log.info("PushMessageService.pushDeliveryDoneTimeOutMessage start,  msg:{}", msgContent);

            Map<String, String> bodyJsonMap = new HashMap<String, String>(1) {{put("text",msgContent);}};
            String bodyJson = JsonUtil.toJson(bodyJsonMap);

            PushInfo pushInfo = queryPushInfoByPositions(tenantId, storeId, positionIds);
            log.info("queryPushInfoByPositions result = {}", pushInfo);

            Set<String> meituanMisIdSet = new HashSet<>();
            meituanMisIdSet.addAll(pushInfo.getMeituanMisIdList());
            meituanMisIdSet.addAll(whiteMidIds);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(pushInfo.getMeituanMisIdList())) {
                String resp = pushMessageThriftServiceClient.pushMessageCrossEnterpriseWithCid(
                        System.currentTimeMillis(), "text", bodyJson,
                        new ArrayList<>(meituanMisIdSet), pusherInfo, meituanCid);
                log.info("PushMessageService.pushMessageCrossEnterpriseWithCid finish, cid:{}, resp:{}", meituanCid, resp);
            }

            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(pushInfo.getExternalMisIdList())) {
                // receivers的用户部分不存在也是可以发送成功的
                String resp = pushMessageThriftServiceClient.pushMessageCrossEnterpriseWithCid(
                        System.currentTimeMillis(), "text", bodyJson,
                        pushInfo.getExternalMisIdList(), pusherInfo, dhCid);
                log.info("PushMessageService.pushMessageCrossEnterpriseWithCid finish, cid:{}, resp:{}", dhCid, resp);
            }
        } catch (Exception e) {
            log.error("推送大象消息失败", e);
            throw new ThirdPartyException("推送大象消息失败");
        }
    }


    public PushInfo queryPushInfoByPositions(long tenantId, long storeId, List<Long> positionIds) {
        try {
            List<EmployeeInOrgDTO> employeeInOrgDTOS = oswClient.queryEmpByWarehouse(tenantId, storeId, positionIds);

            if (CollectionUtils.isEmpty(employeeInOrgDTOS)) {
                return PushInfo.empty();
            }
            List<AccountInfoVo> accountInfoVos = authThriftServiceClient.queryAccountDetailByTenantIdAndStaffIds(tenantId, employeeInOrgDTOS.stream().map(EmployeeDTO::getEmpId).collect(Collectors.toList()));
            List<AccountInfoVo> validAccountInfoList =
                    Optional.ofNullable(accountInfoVos)
                            .orElse(Lists.newArrayList())
                            .stream()
                            .filter(accountInfoVo -> Objects.equals(accountInfoVo.getValid(), ACCOUNT_VALID))
                            .collect(Collectors.toList());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(validAccountInfoList)) {
                return PushInfo.empty();
            }

            //整体逻辑，有mis取mis，没有mis取accountName
            //对于美团员工和加盟来说，其大象账号是accountVO的美团体系misId
            List<String> meituanMisIdList = validAccountInfoList.stream().filter(accountVO -> StringUtils.isNotBlank(accountVO.getMisId())).map(AccountInfoVo::getMisId).distinct().collect(Collectors.toList());
            //对于直营来说，其大象账号是用accountName来开通的外部mis
            List<String> externalMisIdList = validAccountInfoList.stream().filter(accountVO -> StringUtils.isBlank(accountVO.getMisId())).map(AccountInfoVo::getAccountName).distinct().collect(Collectors.toList());

            List<Long> accountList = validAccountInfoList.stream().map(AccountInfoVo::getAccountId).distinct().collect(Collectors.toList());
            return new PushInfo(meituanMisIdList, externalMisIdList, accountList);

        } catch (Exception e) {
            log.error("queryPushInfoByPositions error", e);
            throw new ThirdPartyException("查询push信息失败");
        }
    }
}
