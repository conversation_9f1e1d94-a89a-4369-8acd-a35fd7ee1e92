package com.sankuai.shangou.logistics.sdms.infrastructure.external;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryByOrderIdRequest;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryByViewOrderIdRequest;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryRequest;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderQueryResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-07-13
 * @email <EMAIL>
 */
@Slf4j
@Rhino
public class TradeOrderClientImpl {

    @Resource
    private BizOrderThriftService bizOrderThriftServiceClient;

    @CatTransaction
    public BizOrderModel queryTradeOrder(Long merchantId, long warehouseId, String tradeOrderNo, int orderBizType) {
        try {
            BizOrderQueryRequest request = new BizOrderQueryRequest();
            request.setOrderBizType(orderBizType);
            request.setViewOrderId(tradeOrderNo);
            request.setTenantId(merchantId);
            request.setShopId(warehouseId);
            request.setFromMaster(true);
            request.setContainsPromotion(false);
            request.setContainsComposeSku(false);
            request.setIsQueryOrderFinance(false);
            log.info("BizOrderThriftService.query begin, request={}", request);
            //先查从库
            BizOrderQueryResponse response = bizOrderThriftServiceClient.query(request);
            if (Objects.isNull(response.getBizOrderModel())) {
                throw new ThirdPartyException("未查询到订单");
            }

            return response.getBizOrderModel();
        } catch (Exception e) {
            log.error("Call BizOrderThriftService.query failed", e);
            throw new ThirdPartyException("查询订单失败", e);
        }
    }

}
