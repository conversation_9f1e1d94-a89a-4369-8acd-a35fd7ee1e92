package com.sankuai.shangou.logistics.sdms.infrastructure.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.shangou.saas.order.platform.enums.TaskStatusEnum;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaProducer;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.logistics.sdms.dao.mapper.YodaIdentifyTaskPOMapper;
import com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPO;
import com.sankuai.shangou.logistics.sdms.domain.mq.message.VerifyFailMessage;
import com.sankuai.shangou.logistics.sdms.domain.mq.message.VerifyTaskExpireMessage;
import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.VerifyTaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/7/15 17:06
 **/
@Slf4j
@Component
@MafkaConsumer(namespace = "com.sankuai.mafka.castle.daojiacommon",
        topic = "verify_task_expire_delay_message",
        group = "verify_task_expire_delay_message_consumer",
        deadLetter = true)
public class VerifyTaskExpireMessageConsumer implements IMessageListener {
    @Resource
    private YodaIdentifyTaskPOMapper yodaIdentifyTaskPOMapper;
    @MafkaProducer(namespace = "com.sankuai.mafka.castle.daojiacommon", topic = "verify_task_fail_message")
    private IProducerProcessor<Object, String> yodaVerifyFailMessageProducer;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
        String body = (String) mafkaMessage.getBody();
        log.info("开始消费采集任务过期消息: {}", body);

        VerifyTaskExpireMessage message = transform(body);
        YodaIdentifyTaskPO yodaIdentifyTaskPO = yodaIdentifyTaskPOMapper.selectByPrimaryKey(message.getTaskId());
        if (yodaIdentifyTaskPO == null) {
            log.error("采集任务不存在");
            throw new BizException("采集任务不存在");
        }

        if (isFinalStatus(yodaIdentifyTaskPO)) {
            log.info("采集任务已结束");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        yodaIdentifyTaskPO.setTaskStatus(VerifyTaskStatusEnum.EXPIRE.getCode());
        yodaIdentifyTaskPO.setUpdateTime(LocalDateTime.now());
        int updateRows = yodaIdentifyTaskPOMapper.updateByPrimaryKeySelective(yodaIdentifyTaskPO);
        log.info("updateRows: {}", updateRows);

        //发MQ通知验证失败结果
        sendVerifyExpireMessage(yodaIdentifyTaskPO);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private boolean isFinalStatus(YodaIdentifyTaskPO yodaIdentifyTaskPO) {
        return Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.EXPIRE.getCode())
                || Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.COMPLETED.getCode())
                || Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.CANCEL.getCode());
    }

    private VerifyTaskExpireMessage transform(String body) {
        VerifyTaskExpireMessage message = JSON.parseObject(body, VerifyTaskExpireMessage.class);
        Bssert.throwIfNull(message, "消息为空");
        Bssert.throwIfNull(message.getTaskId(), "任务id为空");
        return message;
    }

    private void sendVerifyExpireMessage(YodaIdentifyTaskPO yodaIdentifyTaskPO) {
        try {
            VerifyFailMessage verifyFailMessage = new VerifyFailMessage();
            verifyFailMessage.setYodaIdentifyTaskId(yodaIdentifyTaskPO.getId());
            verifyFailMessage.setVerifyExpire(true);
            yodaVerifyFailMessageProducer.sendMessage(JSON.toJSONString(verifyFailMessage));
        } catch (Exception e) {
            log.error("发送验证失败结果失败", e);
        }
    }
}
