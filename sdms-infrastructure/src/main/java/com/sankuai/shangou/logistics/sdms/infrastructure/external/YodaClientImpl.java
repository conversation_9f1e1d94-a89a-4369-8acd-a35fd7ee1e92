package com.sankuai.shangou.logistics.sdms.infrastructure.external;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.meituan.pay.mwallet.util.SignAndEncUtil;
import com.meituan.rc.yoda.thrift.api.yodaData.YodaDataClient;
import com.meituan.rc.yoda.thrift.data.FaceVerifyData;
import com.meituan.rc.yoda.thrift.data.FaceVerifyDataResult;
import com.meituan.rc.yoda.thrift.data.ProcessResult;
import com.meituan.rc.yoda.thrift.service.YodaService;
import com.meituan.rc.yoda.thrift.util.FaceDecryptUtil;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.logistics.sdms.domain.constants.CatEventEnum;
import com.sankuai.shangou.logistics.sdms.domain.constants.VerifyTypeEnum;
import com.sankuai.shangou.logistics.sdms.domain.entity.yoda.YodaVerifyResult;
import com.sankuai.shangou.logistics.sdms.domain.external.YodaClient;
import com.sankuai.shangou.logistics.sdms.infrastructure.utils.MccUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/7/12 11:33
 **/
@Slf4j
@Service
public class YodaClientImpl implements YodaClient {

    @Resource
    private YodaService.Iface yodaService;

    @Resource(name = "yodaDataClient")
    private YodaDataClient yodaDataClient;

    private final String ACTION = "drunkhorserandomcheck";

    private final String samePersonVerifyAction = "dhrandomcheckverify";

    private final String RISK_LEVEL = "108";

    /**
     * 业务方：591-歪马送酒
     */
    private final int PARTNER = 591;

    /**
     * 2-美团商户
     */
    private int MEITUAN = 2;

    /**
     * 274-牵牛花
     */
    private final int QNH_APP = 274;

    /**
     * 4-优先使用视觉身份人脸数据源
     */
    private final int FACE_COMPARE_TYPE = 4;

    /**
     * 证件号类型，0-身份证
     *
     */
    private final int ID_CARD_CERTIFICATE_TYPE = 0;

    private static final int DEFAULT_TIMEOUT_MILLIS = 300;

    @Override
    public String authorize(String userAgent, String uuid, String ip,
                            String version, Long userAccountId, String userPhoneNum,
                            String userName, String userIdNum, List<VerifyTypeEnum> verifyTypeEnums,
                            int platform) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("riskLevel", RISK_LEVEL);
        params.put("uuid", uuid);
        params.put("ip", ip);
        params.put("ua", userAgent);
        params.put("partner", PARTNER);
        params.put("platform", platform);
        params.put("version", version);
        params.put("app", QNH_APP);
        params.put("accountSystem", MEITUAN);
        params.put("user", userAccountId);
        params.put("mobile", userPhoneNum);
        params.put("showRealName", true);
        params.put("identityIdEncrypt", encipher(userIdNum));
        params.put("name", userName);
        params.put("certificateType", ID_CARD_CERTIFICATE_TYPE);
        params.put("faceCompareType", FACE_COMPARE_TYPE);
        params.put("beLiveType", transBeLiveType(verifyTypeEnums));

        ProcessResult result;
        try {
            log.info("invoke yodaService.authorize start, param: {}", params);
            result = yodaService.authorize(ACTION, JSON.toJSONString(params));
            log.info("end yodaService.authorize start, result: {}", result);
        } catch (Exception e) {
            log.error("获取yoda权限码失败", e);
            throw new ThirdPartyException("获取yoda权限码失败");
        }

        if (result.getStatus() == 1
                && MapUtils.isNotEmpty(result.getData())
                && result.getData().containsKey("request_code")) {
            return result.getData().get("request_code");
        }

        throw new BizException("获取yoda权限码失败");
    }


    @Override
    public Boolean result(String requestCode, String responseCode, Long riderAccountId) {
        try {
            ProcessResult result;
            Map<String, Object> params = Maps.newHashMap();
            params.put("request_code", requestCode);
            params.put("response_code", responseCode);

            log.info("start invoke yodaService.result, params: {}", params);
            result = yodaService.result(ACTION, JSON.toJSONString(params));
            log.info("end invoke yodaService.result, result: {}", result);

            if (result.getStatus() == 1) {
                return Objects.equals(result.getData().get("user"), riderAccountId.toString());
            }

            throw new BizException("验证yoda识别结果失败");
        } catch (Exception e) {
            log.error("验证yoda识别结果失败", e);
            Cat.logEvent(CatEventEnum.VERIFY_RESULT_CHECK_NOT_PASS.getType(), CatEventEnum.VERIFY_RESULT_CHECK_ERROR.getName());
            return null;
        }
    }

    @Override
    public YodaVerifyResult queryFaceVerifyData(String requestCode) {
        FaceVerifyDataResult result;
        try {
            log.info("start invoke yodaDataClient.queryFaceVerifyData, requestCode: {}", requestCode);
            result = yodaDataClient.queryFaceVerifyData(requestCode);
            log.info("end invoke yodaDataClient.queryFaceVerifyData, result: {}", result);
        } catch (Exception e) {
            log.error("查询yoda验证结果失败", e);
            throw new ThirdPartyException("查询yoda验证结果失败");
        }

        //如果没查到验证记录 不报错
        if (result.getStatus() != 1 && Objects.equals(result.getError().get("code"), "4")) {
            log.info("没有查到验证记录");
            return new YodaVerifyResult(null,null,null,null);
        }

        if (result.getStatus() != 1) {
            throw new BizException("查询yoda验证结果失败");
        }
        FaceVerifyData faceVerifyData = result.getFaceVerifyData();
        JSONObject jsonObject = JSON.parseObject(faceVerifyData.getTags());

        Boolean helmetVerifyResult = Optional.ofNullable(jsonObject)
                .map(val -> val.getJSONObject("helmet"))
                .map(val -> val.getBoolean("flag"))
                .orElse(null);
        Boolean outfitVerifyResult = Optional.ofNullable(jsonObject)
                .map(val -> val.getJSONObject("outfit"))
                .map(val -> val.getBoolean("flag"))
                .orElse(null);
        Boolean compareVerifyResult = Optional.ofNullable(jsonObject)
                .map(val -> val.getJSONObject("compare"))
                .map(val -> val.getBoolean("flag"))
                .orElse(null);

        return new YodaVerifyResult(helmetVerifyResult, outfitVerifyResult, compareVerifyResult, faceVerifyData.getFaceImageUrl());
    }

    @Override
    public Pair<Boolean, String> verifySamePerson(String userAgent, String uuid, String ip,
                                 Long userAccountId, String userName, String userIdNum, String faceUrl) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("id", 132);
        params.put("uuid", uuid);
        params.put("ip", ip);
        params.put("ua", userAgent);
        params.put("partner", PARTNER);
        params.put("accountSystem", MEITUAN);
        params.put("user", userAccountId);
        params.put("identityIdEncrypt", encipher(userIdNum));
        params.put("name", userName);
        params.put("certificateType", ID_CARD_CERTIFICATE_TYPE);
        params.put("faceUrl", faceUrl);
        params.put("encImage", true);
        params.put("credibilityLevel", 3);
        params.put("faceCompareType", FACE_COMPARE_TYPE);
        ProcessResult result;
        try {
            log.info("start invoke yodaService.verify, request: {}", params);
            result = yodaService.verify(samePersonVerifyAction, JSON.toJSONString(params));
            log.info("end invoke yodaService.verify, result: {}", result);
        } catch (Exception e) {
            log.error("同人比对失败", e);
            throw new ThirdPartyException("同人比对失败");
        }

        if (result.getStatus() != 1) {
            return Pair.of(false, Optional.ofNullable(result.getError()).orElse(Collections.emptyMap()).getOrDefault("request_code", ""));
        }

        return Pair.of(true, "");

    }

    @Override
    public String queryFaceImageBase64(String requestCode) {
        FaceVerifyDataResult result;
        try {
            log.info("start invoke yodaDataClient.queryFaceVerifyData, requestCode: {}", requestCode);
            result = yodaDataClient.queryFaceVerifyData(requestCode);
            log.info("end invoke yodaDataClient.queryFaceVerifyData, result: {}", result);
        } catch (Exception e) {
            log.error("查询yoda验证结果失败", e);
            throw new ThirdPartyException("查询yoda验证结果失败");
        }

        if (result.getStatus() != 1 && Objects.equals(result.getError().get("code"), "4")) {
            log.info("没有查到验证记录");
            throw new BizException("没有查到验证记录");
        }

        FaceVerifyData faceVerifyData = result.getFaceVerifyData();
        if (faceVerifyData == null || StringUtils.isBlank(faceVerifyData.getFaceImageUrl())) {
            throw new SystemException("人脸验证图片为空");
        }

        try {
            return FaceDecryptUtil.getFaceImageBase64(faceVerifyData.getFaceImageUrl(), faceVerifyData.getEncryptionVersion(), DEFAULT_TIMEOUT_MILLIS);
        } catch (Exception e) {
            log.error("解密人脸图片失败", e);
            throw new SystemException("解密人脸图片失败");
        }
    }

    /**
     * 后端活体检测类型：
     * 1-二次活体检测
     * 2-遮挡识别
     * 3-口罩检测
     * 4-头盔检测
     * 6-着装检测
     * @param verifyTypeEnums
     * @return
     */
    private String transBeLiveType(List<VerifyTypeEnum> verifyTypeEnums) {
        HashSet<Integer> beLiveTypeSet = new HashSet<>();

        if (verifyTypeEnums.contains(VerifyTypeEnum.Helmet)) {
            beLiveTypeSet.add(1);
            beLiveTypeSet.add(2);
            beLiveTypeSet.add(4);
        }

        if (verifyTypeEnums.contains(VerifyTypeEnum.DRESSING)) {
            beLiveTypeSet.add(1);
            beLiveTypeSet.add(2);
            beLiveTypeSet.add(6);
        }

        return Joiner.on(",").join(beLiveTypeSet);
    }


    private static String encipher(String idNum) {
        try {
            return SignAndEncUtil.encrypt(idNum, MccUtils.getSignAndEncUtilPublicKey(), "UTF-8");
        } catch (Exception e) {
            log.error("加密身份证号失败", e);
            throw new BizException("加密身份证号失败");
        }
    }
}
