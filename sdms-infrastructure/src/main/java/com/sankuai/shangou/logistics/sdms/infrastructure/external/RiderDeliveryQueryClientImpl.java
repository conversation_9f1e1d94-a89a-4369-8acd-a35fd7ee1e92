package com.sankuai.shangou.logistics.sdms.infrastructure.external;

import com.dianping.rhino.annotation.Degrade;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TStaffRider;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.PageQueryInProgressDeliveryOrderRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.PageQueryDeliveryOrderResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.logistics.sdms.domain.entity.rider.RiderDeliveryOrderInfo;
import com.sankuai.shangou.logistics.sdms.domain.external.RiderDeliveryQueryClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/9 10:55
 **/
@Service
@Slf4j
public class RiderDeliveryQueryClientImpl implements RiderDeliveryQueryClient {
    @Resource
    private RiderQueryThriftService riderQueryThriftService;

    @Override
    @Degrade(rhinoKey = "RiderDeliveryQueryClient.queryRiderInProgressOrder", fallBackMethod = "queryRiderInProgressOrderFallback", timeoutInMilliseconds = 2000)
    public List<RiderDeliveryOrderInfo> queryRiderInProgressOrder(Long tenantId, Long storeId, Long accountId) {
        PageQueryInProgressDeliveryOrderRequest request = new PageQueryInProgressDeliveryOrderRequest(1, 1, tenantId, storeId, accountId);

        PageQueryDeliveryOrderResponse response;
        try {
            log.info("start invoke riderQueryThriftService.pageQueryInProgressDeliveryOrder, request: {}", request);
            response = riderQueryThriftService.pageQueryInProgressDeliveryOrder(request);
            log.info("end invoke riderQueryThriftService.pageQueryInProgressDeliveryOrder, response: {}", response);
        } catch (Exception e) {
            log.error("查询骑手进行中的运单失败", e);
            throw new ThirdPartyException("查询骑手进行中的运单失败");
        }

        if (response.getStatus().getCode() != 0) {
            throw new BizException(response.getStatus().getMsg());
        }

        return Optional.ofNullable(response.getTRiderDeliveryOrders())
                .orElse(Collections.emptyList())
                .stream()
                .map(this::transform)
                .collect(Collectors.toList());
    }

    private RiderDeliveryOrderInfo transform(TRiderDeliveryOrder tRiderDeliveryOrder) {
        return new RiderDeliveryOrderInfo(
                tRiderDeliveryOrder.getTenantId(),
                tRiderDeliveryOrder.getStoreId(),
                Optional.ofNullable(tRiderDeliveryOrder.getCurrentRider()).map(TStaffRider::getAccountId).orElse(null),
                tRiderDeliveryOrder.getDeliveryOrderId(),
                tRiderDeliveryOrder.getDeliveryStatus(),
                tRiderDeliveryOrder.getOrderId(),
                tRiderDeliveryOrder.getChannelOrderId(),
                tRiderDeliveryOrder.getOrderBizTypeCode()
                );
    }
}
