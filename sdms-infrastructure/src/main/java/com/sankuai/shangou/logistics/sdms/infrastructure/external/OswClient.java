package com.sankuai.shangou.logistics.sdms.infrastructure.external;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.infra.osw.api.org.TEmployeeService;
import com.sankuai.shangou.infra.osw.api.org.TOrgService;
import com.sankuai.shangou.infra.osw.api.org.dto.request.AccountIdsRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.request.BatchQueryDepByEmpRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.request.OrgIdRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.request.QueryEmpByWarehouseRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmpDepRelDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeInOrgDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.OrgDTO;
import com.sankuai.shangou.infra.osw.api.poi.store.dto.request.WarehouseIdsRequest;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.TWarehouseService;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import com.sankuai.shangou.logistics.sdms.domain.entity.poi.PoiBaseInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Rhino
@Slf4j
public class OswClient {
    private static final Integer QUERY_PARENT_DEPARTMENT_PAGE_SIZE = 100;
    @Resource
    private TWarehouseService tWarehouseService;

    @Resource
    private TEmployeeService tEmployeeService;

    @Resource
    private TOrgService tOrgService;

    @Degrade(rhinoKey = "OswClient.queryWarehouseInfo", fallBackMethod = "queryWarehouseInfoFallback", timeoutInMilliseconds = 2000)
    public PoiBaseInfo queryWarehouseInfo(Long tenantId, Long warehouseId) {
        WarehouseIdsRequest request = new WarehouseIdsRequest();
        request.setTenantId(tenantId);
        request.setWarehouseIds(Collections.singletonList(warehouseId));

        TResult<List<WarehouseDTO>> tResult;
        try {
            log.info("start invoke tWarehouseService.batchQueryWarehouseById, request: {}", request);
            tResult = tWarehouseService.batchQueryWarehouseById(request);
            log.info("end invoke tWarehouseService.batchQueryWarehouseById, tResult: {}", tResult);
        } catch (Exception e) {
            log.error("查询门店信息失败", e);
            throw new ThirdPartyException("查询门店信息失败");
        }

        if (!tResult.isSuccess()) {
            throw new BizException(tResult.getMsg());
        }

        if (CollectionUtils.isEmpty(tResult.getData())) {
            throw new BizException("未查询到门店信息");
        }

        return transfer(tResult.getData().get(0));
    }

    @Degrade(rhinoKey = "OswClient.queryEmpByWarehouse", fallBackMethod = "queryEmpByWarehouseFallback", timeoutInMilliseconds = 2000)
    public List<EmployeeInOrgDTO> queryEmpByWarehouse(Long tenantId, Long warehouseId, List<Long> positionIds) {
        QueryEmpByWarehouseRequest request = new QueryEmpByWarehouseRequest();
        request.setTenantId(tenantId);
        request.setWarehouseId(warehouseId);
        request.setPositionIds(positionIds);
        request.setScope(1);
        request.setFindInParent(true);
        TResult<List<EmployeeInOrgDTO>> listTResult ;
        try {
            listTResult = tEmployeeService.queryEmpByWarehouse(request);
        } catch (Exception e) {
            log.error("按岗位查询员工失败", e);
            throw new ThirdPartyException("按岗位查询员工失败");
        }

        if (!listTResult.isSuccess()) {
            throw new BizException("按岗位查询员工失败");
        }

        return listTResult.getData();

    }

    @Degrade(rhinoKey = "OswClient.queryEmpByAccountId", fallBackMethod = "queryEmpByAccountIdFallback", timeoutInMilliseconds = 2000)
    public Optional<EmployeeDTO> queryEmpByAccountId(Long tenantId, Long accountId) {
        AccountIdsRequest request =  new AccountIdsRequest();
        request.setTenantId(tenantId);
        request.setAccountIds(Collections.singletonList(accountId));
        TResult<List<EmployeeDTO>> listTResult ;
        try {
            log.info("start invoke tEmployeeService.queryEmpByAccountIds, request: {}", request);
            listTResult = tEmployeeService.queryEmpByAccountIds(request);
            log.info("end invoke tEmployeeService.queryEmpByAccountIds, listTResult: {}", listTResult);
        } catch (Exception e) {
            log.error("按账号id查询员工失败", e);
            throw new ThirdPartyException("按账号id查询员工失败");
        }
        if (listTResult == null) {
            throw new BizException("按账号id查询员工失败");
        }

        if (!listTResult.isSuccess()) {
            throw new BizException("按账号id查询员工失败");
        }

        if (CollectionUtils.isEmpty(listTResult.getData())) {
            return Optional.empty();
        }

        return Optional.of(listTResult.getData().get(0));

    }

    /**
     * @param tenantId
     * @param empIds
     * @return 批量查询员工与部门的关系
     */
    @Degrade(rhinoKey = "OswClient.batchQueryBelongDepByEmp",
            fallBackMethod = "batchQueryBelongDepByEmpFallback",
            timeoutInMilliseconds = 2000)
    public List<EmpDepRelDTO> batchQueryBelongDepByEmp(Long tenantId, List<Long> empIds) {
        BatchQueryDepByEmpRequest request = new BatchQueryDepByEmpRequest();
        request.setTenantId(tenantId);
        request.setEmpIds(empIds);
        TResult<List<EmpDepRelDTO>> listTResult;
        try {
            listTResult = tEmployeeService.batchQueryBelongDepByEmp(request);
            log.info("tEmployeeService.batchQueryBelongDepByEmp, result: {}", JsonUtil.toJson(listTResult));
        } catch (TException e) {
            log.error("查询员工所属组织失败", e);
            throw new ThirdPartyException("查询员工所属组织异常");
        }
        if (listTResult == null || !listTResult.isSuccess()) {
            log.error("查询员工所属组织失败, tenantId:{}, empIds:{}, result:{}", tenantId, empIds, listTResult);
            throw new BizException("查询员工所属组织失败");
        }
        return listTResult.getData();
    }

    @Degrade(rhinoKey = "OswClient.getParentDeptById",
            fallBackMethod = "getParentDeptByIdFallback",
            timeoutInMilliseconds = 2000)
    public OrgDTO getParentDeptById(Long tenantId, Long depId) {
        // 0代表根节点
        if (depId == null || depId == 0) {
            return null;
        }
        OrgIdRequest request = new OrgIdRequest();
        request.setTenantId(tenantId);
        request.setOrgId(depId);
        TResult<OrgDTO> parentDeptR = tOrgService.getParentById(request);
        log.info("tOrgService.getParentById, result: {}", JsonUtil.toJson(parentDeptR));
        if (parentDeptR == null || !parentDeptR.isSuccess()) {
            log.error("查询部门父节点失败, tenantId:{}, depId:{}, result:{}", tenantId, depId, parentDeptR);
            throw new BizException("查询部门父节点失败");
        }
        return parentDeptR.getData();
    }

    public Optional<EmployeeDTO> queryEmpByAccountIdFallback(Long tenantId, Long accountId) {
        log.error("swClient.queryEmpByAccountId 发生降级");
        return Optional.empty();
    }

    public List<EmployeeInOrgDTO> queryEmpByWarehouseFallback(Long tenantId, Long warehouseId, List<Long> positionIds) {
        log.error("OswClient.queryEmpByWarehouse 发生降级");
        return Collections.emptyList();
    }

    public PoiBaseInfo queryWarehouseInfoFallback(Long tenantId, Long warehouseId) {
        log.error("OswClient.tWarehouseService 发生降级");
        throw new BizException("店仓接口降级");
    }

    public List<EmpDepRelDTO> batchQueryBelongDepByEmpFallback(Long tenantId, List<Long> accountIds) {
        log.error("OswClient.batchQueryBelongDepByEmp 发生降级");
        return Collections.emptyList();
    }

    public OrgDTO getParentDeptByIdFallback(Long tenantId, Long depId) {
        log.error("OswClient.getParentDeptById 发生降级");
        return null;
    }

    private PoiBaseInfo transfer(WarehouseDTO warehouseDTO) {
        return new PoiBaseInfo(warehouseDTO.getTenantId(),
                warehouseDTO.getId(),
                warehouseDTO.getName(),
                warehouseDTO.getRegion().getCityId(),
                warehouseDTO.getOperationMode());
    }
}