package com.sankuai.shangou.logistics.sdms.infrastructure.external;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.OpenAggDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.request.OpenTurnToAggregationDeliveryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.request.QueryAggStoreConfigUrlRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.response.QueryAggStoreConfigUrlResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.TurnToAggregationDeliveryResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.logistics.sdms.domain.constants.CatEventEnum;
import com.sankuai.shangou.logistics.sdms.domain.entity.common.Operator;
import com.sankuai.shangou.logistics.sdms.domain.external.OpenAggDeliveryClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-08-29
 * @email <EMAIL>
 */
@Slf4j
@Service
public class OpenAggDeliveryClientImpl implements OpenAggDeliveryClient {

    @Resource
    private OpenAggDeliveryThriftService openAggDeliveryThriftService;

    @Override
    public void turnToAggDelivery(long tenantId, long poiId, String channelOrderId, int channelId, Operator operator) {
        OpenTurnToAggregationDeliveryRequest request = new OpenTurnToAggregationDeliveryRequest();
        request.setTenantId(tenantId);
        request.setPoiId(poiId);
        request.setChannelOrderId(channelOrderId);
        request.setChannelId(channelId);
        request.setOperatorId(operator.getOperatorId());
        request.setOperatorName(operator.getOperatorName());
        request.setDeliveryPlatformId(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode());
        log.info("openAggDeliveryThriftService.turnToAggregationDelivery invoked! deliveryOrderId = {}", channelOrderId);
        TurnToAggregationDeliveryResponse response = openAggDeliveryThriftService.turnToAggregationDelivery(request);
        log.info("openAggDeliveryThriftService.turnToAggregationDelivery response = {}", JSON.toJSONString(response));
        if (!Objects.equals(response.getStatus().getCode(), Status.SUCCESS.getCode())) {
            Cat.logEvent(CatEventEnum.DAP_TURN_DELIVERY_ERROR.getType(), CatEventEnum.DAP_TURN_DELIVERY_ERROR.getName());
            throw new BizException(StringUtils.defaultString(response.getStatus().getMsg(), "转配送方式失败，请重试"));
        }
    }
}
