<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sankuai.shangou.logistics</groupId>
        <artifactId>sdms</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sdms-infrastructure</artifactId>
    <version>${revision}</version>

    <dependencies>
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>dmp-sdk</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>sdms-sdk</artifactId>
        </dependency>


        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>sdms-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>sdms-dao</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_tenant_client</artifactId>
            <version>3.0.12</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower</groupId>
            <artifactId>reco_store_saas_auth_client</artifactId>
            <version>1.5.14</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-delivery-client</artifactId>
            <version>2.2.38</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>oio-api</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-rider-delivery-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.infra</groupId>
            <artifactId>osw-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>mafka-xframe-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.rc.yoda</groupId>
            <artifactId>yoda-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.pay</groupId>
            <artifactId>mwalletSdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.bizmng</groupId>
            <artifactId>labor-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.sgxsupply.wxmall</groupId>
            <artifactId>bizmanagement-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.waima</groupId>
            <artifactId>support-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_biz_client</artifactId>
            <version>2.1.149</version>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mss-java-sdk-s3</artifactId>
                    <groupId>com.amazonaws</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--租户-->
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_tenant_client</artifactId>
            <version>3.2.6</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>xm-pub-api-client</artifactId>
            <version>1.5.7</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-pangolin-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>idl-kms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-java-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-tls-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.qnh.ofc</groupId>
            <artifactId>qnh_ofc_ofw-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou</groupId>
            <artifactId>reco_store_saas_message_management_client</artifactId>
        </dependency>
    </dependencies>

    <name>sdms-infrastructure</name>


</project>
