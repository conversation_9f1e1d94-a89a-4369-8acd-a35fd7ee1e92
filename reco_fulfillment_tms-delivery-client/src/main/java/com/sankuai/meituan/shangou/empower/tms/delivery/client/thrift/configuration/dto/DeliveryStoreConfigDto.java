package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DeliveryStoreConfigDto {

    @FieldDoc(
            description = "配送平台code",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    public Integer platformCode;

    @FieldDoc(
            description = "当前开通状态标识 1-已开通 0-未开通",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public Integer openFlag;

    @FieldDoc(
            description = "渠道名称"
    )
    @ThriftField(3)
    private Integer channelType;

    @FieldDoc(
            description = "配送发单节点"
    )
    @ThriftField(4)
    private Integer deliveryLaunchPoint;

    @FieldDoc(
            description = "立即单自动呼叫骑手延迟时间"
    )
    @ThriftField(5)
    private Integer deliveryLaunchDelayMinutes;

    @FieldDoc(
            description = "预约单自动呼叫骑手延迟时间"
    )
    @ThriftField(6)
    private Integer bookingOrderDeliveryLaunchMinutes;
}
