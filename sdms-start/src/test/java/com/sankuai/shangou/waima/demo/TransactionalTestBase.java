//package com.sankuai.shangou.logistics.sdms;
//
//import org.junit.Before;
//import org.junit.Ignore;
//import org.junit.Rule;
//import org.junit.rules.ExpectedException;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.jdbc.core.JdbcTemplate;
//import org.springframework.test.annotation.Rollback;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//
///**
// * 集成测试基类
// * @Author: gantianxing
// * @Date: 2019-09-24 16-44
// **/
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = ApplicationTestConfiguration.class)
//@Transactional
//@Rollback
//@Ignore
//public abstract class TransactionalTestBase {
//    protected static final String loginEmployeeId = "1200";
//    @Resource
//    protected JdbcTemplate jdbcTemplate;
//
//    @Rule
//    public ExpectedException thrown = ExpectedException.none();
//
//    @Before
//    public void startUp() {
//    }
//}
