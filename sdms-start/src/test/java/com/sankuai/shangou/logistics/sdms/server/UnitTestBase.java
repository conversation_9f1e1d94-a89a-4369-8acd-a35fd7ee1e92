package com.sankuai.shangou.logistics.sdms.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.sankuai.shangou.logistics.sdms.infrastructure.utils.MccUtils;
import com.sankuai.shangou.logistics.sdms.server.StartApp;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * 单元测试基类
 * @Author: gantianxing
 * @Date: 2019-09-24 16-44
 **/
@RunWith(SpringRunner.class)
@ActiveProfiles("test")
@SpringBootTest(classes = StartApp.class)
public class UnitTestBase {
    @Test
    public void test() {
        Map<String, String> map = Lion.getConfigRepository().getMap("yoda.business.err.code.map", String.class, new HashMap<>());
        System.out.println(map);
    }

}
