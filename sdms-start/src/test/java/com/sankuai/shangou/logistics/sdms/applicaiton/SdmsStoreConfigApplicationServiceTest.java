package com.sankuai.shangou.logistics.sdms.applicaiton;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderDeliveryModel;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.RegionDTO;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import com.sankuai.shangou.logistics.sdms.BaseIntegrationTest;
import com.sankuai.shangou.logistics.sdms.application.config.SdmsStoreConfigApplicationService;
import com.sankuai.shangou.logistics.sdms.dao.mapper.AssessDeliveryTimeConfigMapper;
import com.sankuai.shangou.logistics.sdms.dao.mapper.SdmsStoreConfigMapper;
import com.sankuai.shangou.logistics.sdms.dao.model.AssessDeliveryTimeConfig;
import com.sankuai.shangou.logistics.sdms.dao.model.SdmsStoreConfig;
import com.sankuai.shangou.logistics.sdms.domain.utils.TimeUtils;
import com.sankuai.shangou.logistics.sdms.infrastructure.external.TradeOrderClientImpl;
import com.sankuai.shangou.logistics.sdms.infrastructure.external.WarehouseQueryWrapper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024-07-13
 * @email <EMAIL>
 */
public class SdmsStoreConfigApplicationServiceTest extends BaseIntegrationTest {

    @Resource
    private SdmsStoreConfigApplicationService sdmsStoreConfigApplicationService;
    @MockBean
    private SdmsStoreConfigMapper sdmsStoreConfigMapper;
    @MockBean
    private WarehouseQueryWrapper warehouseQueryWrapper;
    @MockBean
    private AssessDeliveryTimeConfigMapper assessDeliveryTimeConfigMapper;
    @MockBean
    private TradeOrderClientImpl tradeOrderClient;

    @Before
    public void setUp() {
        WarehouseDTO warehouseDTO = new WarehouseDTO();
        warehouseDTO.setTenantId(1000395L);
        warehouseDTO.setOperationMode(1);
        RegionDTO regionDTO = new RegionDTO();
        regionDTO.setCityId(1);
        warehouseDTO.setRegion(regionDTO);
        when(warehouseQueryWrapper.queryWarehouse(anyLong(), anyLong())).thenReturn(warehouseDTO);

        SdmsStoreConfig sdmsStoreConfig = new SdmsStoreConfig();
        sdmsStoreConfig.setId(1L);
        sdmsStoreConfig.setTenantId(1L);
        sdmsStoreConfig.setStoreOperationMode(1);
        when(sdmsStoreConfigMapper.selectByExample(any())).thenReturn(Lists.newArrayList(sdmsStoreConfig));


        AssessDeliveryTimeConfig assessDeliveryTimeConfig = new AssessDeliveryTimeConfig();
        assessDeliveryTimeConfig.setId(1L);
        assessDeliveryTimeConfig.setBelongStoreConfigId(1L);
        assessDeliveryTimeConfig.setConfigJson(CONFIG_JSON);
        when(assessDeliveryTimeConfigMapper.selectByExample(any())).thenReturn(Lists.newArrayList(assessDeliveryTimeConfig));

        BizOrderModel bizOrderModel = new BizOrderModel();
        bizOrderModel.setIsBooking(0);
        BizOrderDeliveryModel deliveryModel = new BizOrderDeliveryModel();
        LocalDateTime now = LocalDateTime.now();
        deliveryModel.setArrivalTime(TimeUtils.toMilliSeconds(now));
        bizOrderModel.setDeliveryModel(deliveryModel);
        bizOrderModel.setPayTime(TimeUtils.toMilliSeconds(now.minusMinutes(50)));
        deliveryModel.setCategory("餐馆");
        when(tradeOrderClient.queryTradeOrder(anyLong(),anyLong(), any(),anyInt())).thenReturn(bizOrderModel);

    }

    @Test
    public void queryAssessDeliveryDurationTest_happyPath1() {
        Integer result = sdmsStoreConfigApplicationService.calcAssessDeliveryDuration(
                1L, 1L, "1", 101, 10, null, null
        );
        Assert.assertEquals(10, (int) result);
    }

    @Test
    public void queryAssessDeliveryDurationTest_happyPath2() {
        Integer result = sdmsStoreConfigApplicationService.calcAssessDeliveryDuration(
                1L, 1L, "1", 101, 2000, null, null
        );
        Assert.assertEquals(20, (int) result);
    }

    @Test
    public void queryAssessDeliveryDurationTest_happyPath3() {
        Integer result = sdmsStoreConfigApplicationService.calcAssessDeliveryDuration(
                1L, 1L, "1", 101, 4000, null, null
        );
        Assert.assertEquals(40, (int) result);
    }

    @Test
    public void queryAssessDeliveryDurationTest_badPath() {
        Integer result = sdmsStoreConfigApplicationService.calcAssessDeliveryDuration(
                1L, 1L, "1", 101, -1, null, null
        );
        Assert.assertEquals(-1, (int) result);
    }

    public static void main(String[] args) {
        System.out.println(CONFIG_JSON);
    }

    private static final String CONFIG_JSON = "{\n" +
            "    \"subs\":[\n" +
            "        {\n" +
            "            \"condition\":{\n" +
            "                \"name\":\"订单预约类型\",\n" +
            "                \"interval\":{\n" +
            "                    \"values\":[\n" +
            "                        \"0\"\n" +
            "                    ],\n" +
            "                    \"intervalType\":6\n" +
            "                },\n" +
            "                \"identifier\":\"@{reserve_type}\",\n" +
            "                \"formula\":\"${reserve_type}\"\n" +
            "            },\n" +
            "            \"subs\":[\n" +
            "                {\n" +
            "                    \"condition\":{\n" +
            "                        \"name\":\"餐馆场景\",\n" +
            "                        \"defineId\":9,\n" +
            "                        \"interval\":{\n" +
            "                            \"values\":[\n" +
            "                                \"0\"\n" +
            "                            ],\n" +
            "                            \"intervalType\":6\n" +
            "                        },\n" +
            "                        \"identifier\":\"@{restaurant_scene}\",\n" +
            "                        \"formula\":\"${restaurant_scene}\"\n" +
            "                    },\n" +
            "                    \"subs\":[\n" +
            "                        {\n" +
            "                            \"condition\":{\n" +
            "                                \"name\":\"配送距离\",\n" +
            "                                \"defineId\":9,\n" +
            "                                \"interval\":{\n" +
            "                                    \"values\":[\n" +
            "                                        \"0\",\n" +
            "                                        \"1000\"\n" +
            "                                    ],\n" +
            "                                    \"intervalType\":2\n" +
            "                                },\n" +
            "                                \"identifier\":\"@{delivery_distance}\",\n" +
            "                                \"formula\":\"${delivery_distance}\"\n" +
            "                            },\n" +
            "                            \"formula\":\"10\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"condition\":{\n" +
            "                                \"name\":\"配送距离\",\n" +
            "                                \"defineId\":9,\n" +
            "                                \"interval\":{\n" +
            "                                    \"values\":[\n" +
            "                                        \"1000\",\n" +
            "                                        \"3000\"\n" +
            "                                    ],\n" +
            "                                    \"intervalType\":2\n" +
            "                                },\n" +
            "                                \"identifier\":\"@{delivery_distance}\",\n" +
            "                                \"formula\":\"${delivery_distance}\"\n" +
            "                            },\n" +
            "                            \"formula\":\"20\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"condition\":{\n" +
            "                                \"name\":\"配送距离\",\n" +
            "                                \"defineId\":9,\n" +
            "                                \"interval\":{\n" +
            "                                    \"values\":[\n" +
            "                                        \"3000\",\n" +
            "                                        \"infinity\"\n" +
            "                                    ],\n" +
            "                                    \"intervalType\":2\n" +
            "                                },\n" +
            "                                \"identifier\":\"@{delivery_distance}\",\n" +
            "                                \"formula\":\"${delivery_distance}\"\n" +
            "                            },\n" +
            "                            \"formula\":\"${eta_duration} - 10\"\n" +
            "                        }\n" +
            "                    ]\n" +
            "                }\n" +
            "            ]\n" +
            "        }\n" +
            "    ]\n" +
            "}";
}
