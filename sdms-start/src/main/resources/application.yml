# Zebra属性配置
zebra:
  jdbcRef: dhorderfulfillment_sdms_test # jdbcRef配置, 通过profile隔离
  default-transaction-manager: true
  poolType: druid
  minPoolSize: 5
  maxPoolSize: 30
  initialPoolSize: 5
  checkoutTimeout: 1000
  maxIdleTime: 1800
  idleConnectionTestPeriod: 60
  sqlSessionFactory:
    typeAliasesPackage: com.sankuai.shangou.logistics.sdms.dao.model; # 替换成自己的路径
    plugins:
      - className: com.github.pagehelper.PageInterceptor
        properties:
          helperDialect: mysql # 数据库方言配置，默认为mysql。当使用其他数据库时请对应修改，例如：PostgreSQL数据库配置为postgresql、DB2数据配置db2等。
  zebraMapperScannerConfigurer:
    basePackage: com.sankuai.shangou.logistics.sdms.dao.mapper   # 替换成自己的路径

app:
  name: com.sankuai.shangou.logistics.sdms

spring:
  main:
    allow-bean-definition-overriding: true



#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-online.xml

squirrel:
  dhBusinessClusterName: redis-sg-drunkhorse-business_qa
---

spring:
  profiles: local

zebra:
  jdbcRef: dhorderfulfillment_sdms_test #替换为自己dev环境的jdbcRef

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-local.xml
  level:
    com.sankuai.shangou.logistics.sdms.dao.mapper: DEBUG # 打印mybatis的sql日志


---
spring:
  profiles: dev

zebra:
  jdbcRef: dhorderfulfillment_sdms_test #替换为自己dev环境的jdbcRef

logging:
  config: classpath:log4j2/log4j2-offline.xml
  level:
    com.sankuai.shangou.logistics.sdms.dao.mapper: DEBUG # 打印mybatis的sql日志
---

spring:
  profiles: test

zebra:
  jdbcRef: dhorderfulfillment_sdms_test #替换为自己dev环境的jdbcRef
logging:
  config: classpath:log4j2/log4j2-offline.xml
  level:
    com.sankuai.shangou.logistics.sdms.dao.mapper: DEBUG # 打印mybatis的sql日志

---
spring:
  profiles: beta

zebra:
  jdbcRef: dhorderfulfillment_sdms_test #替换为自己dev环境的jdbcRef

logging:
  config: classpath:log4j2/log4j2-offline.xml
  level:
    com.sankuai.shangou.logistics.sdms.dao.mapper: DEBUG # 打印mybatis的sql日志

---

spring:
  profiles: staging


zebra:
  jdbcRef: dhorderfulfillment_sdms_product #替换为自己dev环境的jdbcRef

squirrel:
  dhBusinessClusterName: redis-sg-drunkhorse-business_stage

---

spring:
  profiles: prod

zebra:
  jdbcRef: dhorderfulfillment_sdms_product #替换为自己dev环境的jdbcRef

squirrel:
  dhBusinessClusterName: redis-sg-drunkhorse-business_product



