<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <context:annotation-config/>

    <bean id="propertyConfigurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <!-- https://wiki.sankuai.com/pages/viewpage.action?pageId=1252888869 -->
        <property name="ignoreUnresolvablePlaceholders" value="true"/>
        <property name="locations">
            <list>
                <!--用来存放和profile相关的配置信息-->
                <value>classpath:META-INF/app.properties</value>
            </list>
        </property>
    </bean>

    <bean id="kmsAuthDataSource" class="com.meituan.service.inf.kms.client.KmsAuthDataSource">
        <property name="appkey" value="${app.name}"/>
    </bean>

    <bean id="defaultAuthHandler" class="com.meituan.service.mobile.mtthrift.auth.DefaultAuthHandler">
        <property name="authDataSource" ref="kmsAuthDataSource" />
    </bean>

    <bean id="redisClient" class="com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory">
        <!-- 集群名称,必填 -->
        <property name="clusterName" value="${squirrel.dhBusinessClusterName}"/>
        <!--读取超时时间,缓存业务建议改成100，存储业务建议改成1000，默认值为1000。选填-->
        <property name="readTimeout" value="1000"/>
        <!--路由策略,默认值是master-only表示只从主节点读取。slave-only表示只读从节点,master-slave表示主从都可以读。选填-->
        <property name="routerType" value="master-only"/>
        <!--连接redis节点的连接池配置，选填-->
        <property name="poolMaxIdle" value="30"/>
        <property name="poolMaxTotal" value="50"/>
        <property name="poolWaitMillis" value="500"/>

        <!--异步操作线程池配置，选填-->
        <property name="asyncCoreSize" value="16"/>
        <property name="asyncMaxSize" value="64"/>
        <property name="asyncQueueSize" value="1000"/>
    </bean>

    <import resource="infrastructure-thrift-client.xml"/>
</beans>