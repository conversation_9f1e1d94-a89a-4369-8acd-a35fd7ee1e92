<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sankuai.shangou.logistics</groupId>
        <artifactId>sdms</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sdms-start</artifactId>

    <dependencies>
        <!--xframe依赖-->
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-pangolin-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.log</groupId>
            <artifactId>scribe-log4j2</artifactId>
            <exclusions>
        <exclusion>
          <artifactId>slf4j-log4j12</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xmd-log4j2</artifactId>
          <groupId>com.meituan.inf</groupId>
        </exclusion>
      </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.servicecatalog</groupId>
            <artifactId>api-annotations</artifactId>
            <version>1.0.7</version>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.7.0</version>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.7.0</version>
        </dependency>
        <!--目前 scribe-log4j2 依赖 log4j2 版本 -->
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>web-xframe-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>zebra-xframe-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>threadpool-xframe-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>sdms-sdk</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>sdms-domain</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>sdms-infrastructure</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>sdms-application</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>sdms-server</artifactId>
            <version>${revision}</version>
        </dependency>


        <!-- ServiceCatalog api doc -->
        

        <!--swagger依赖-->
        
        

        <!--business-->
        

    </dependencies>

    <name>sdms-start</name>

    <version>1.0.0-SNAPSHOT</version>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
    </build>

</project>
