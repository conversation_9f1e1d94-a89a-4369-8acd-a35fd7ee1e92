<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx.xsd">

    <bean id="taskDataSource" class="com.dianping.zebra.group.jdbc.GroupDataSource" init-method="init"
          destroy-method="close">
        <!-- 必配。指定唯一确定数据库的key-->
        <property name="jdbcRef" value="${delivery.zebra.jdbcRef}"/>
        <!-- 只走写库，如需使用请升级到2.8.3版本以上 -->
        <property name="routerType" value="master-only" />
        <!-- 选配。指定底层使用的连接池类型，支持"c3p0","tomcat-jdbc","druid","hikaricp","dbcp2"和"dbcp"，推荐使用"tomcat-jdbc"，默认值为"c3p0" -->
        <property name="poolType" value="${delivery.zebra.poolType}"/>
        <!-- 选配。指定连接池的最小连接数，默认值是5。 -->
        <property name="minPoolSize" value="${delivery.zebra.minPoolSize}"/>
        <!-- 选配。指定连接池的最大连接数，默认值是20。 -->
        <property name="maxPoolSize" value="${delivery.zebra.maxPoolSize}"/>
        <!-- 选配。指定连接池的初始化连接数，默认值是5。 -->
        <property name="initialPoolSize" value="${delivery.zebra.initialPoolSize}"/>
        <!-- 选配。指定连接池的获取连接的超时时间，默认值是1000。 -->
        <property name="checkoutTimeout" value="${delivery.zebra.checkoutTimeout}"/>
        <!-- jdbcdriver到高版本之后，需要强制指定useSSL -->
        <property name="extraJdbcUrlParams" value="useSSL=false"/>
        <!--以下配置全部可以选配-->
        <property name="maxIdleTime" value="${delivery.zebra.maxIdleTime}"/>
        <property name="idleConnectionTestPeriod" value="${delivery.zebra.idleConnectionTestPeriod}"/>
        <property name="acquireRetryAttempts" value="${delivery.zebra.acquireRetryAttempts}"/>
        <property name="acquireRetryDelay" value="${delivery.zebra.acquireRetryDelay}"/>
        <property name="maxStatements" value="${delivery.zebra.maxStatements}"/>
        <property name="maxStatementsPerConnection" value="${delivery.zebra.maxStatementsPerConnection}"/>
        <property name="numHelperThreads" value="${delivery.zebra.numHelperThreads}"/>
        <property name="maxAdministrativeTaskTime" value="${delivery.zebra.maxAdministrativeTaskTime}"/>
        <property name="preferredTestQuery" value="${delivery.zebra.preferredTestQuery}"/>
        <property name="timeBetweenEvictionRunsMillis" value="60000"/>
        <property name="useLiteSet" value="true" />
    </bean>

    <bean id="taskSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="taskDataSource"/>
        <property name="configLocation" value="classpath:/mybatis-config.xml"/>
        <property name="mapperLocations">
            <array>
                <!--任务mapper的xml位置-->
                <value>classpath*:/com/sankuai/meituan/shangou/empower/task/dao/**/*.xml</value>
            </array>
        </property>
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <!--使用下面的方式配置参数，一行配置一个 -->
                        <value>
                            helperDialect=mysql
                            reasonable=false
                            supportMethodsArguments=true
                            params=count=countSql
                        </value>
                    </property>
                </bean>
            </array>
        </property>
    </bean>

    <bean id="taskScannerConfigurer" class="com.dianping.zebra.dao.mybatis.ZebraMapperScannerConfigurer">
        <property name="sqlSessionFactoryBeanName" value="taskSqlSessionFactory"/>
        <!--任务dao的位置配置，可以用,;\t\n进行分割-->
        <property name="basePackage" value="com.sankuai.meituan.shangou.empower.task.dao"/>
    </bean>

    <bean id="taskMybatisTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="taskDataSource" />
    </bean>


<!--    <tx:annotation-driven transaction-manager="taskMybatisTransactionManager"-->
<!--                          proxy-target-class="true"/>-->

    <bean id="taskTransactionTemplate"
          class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="taskMybatisTransactionManager"/>
    </bean>
</beans>