<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:rhino="http://code.dianping.com/schema/rhino"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.dianping.com/schema/rhino http://code.dianping.com/schema/rhino/rhino-1.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <context:component-scan base-package="com.sankuai.meituan.shangou.empower.tms, com.sankuai.meituan.shangou.empower.task">
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>

    <rhino:annotation package="com.sankuai.meituan.reco"/>

    <aop:aspectj-autoproxy/>

    <!-- 用来初始化配置 -->
    <bean id="propertyConfigurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <!-- https://wiki.sankuai.com/pages/viewpage.action?pageId=********** -->
        <property name="ignoreUnresolvablePlaceholders" value="true"/>
        <property name="locations">
            <list>
                <!--用来存放和profile相关的配置信息-->
                <value>classpath:META-INF/app.properties</value>
            </list>
        </property>
    </bean>

    <import resource="classpath:/task/task-datasource.xml"/>

    <!--任务执行消息-->
    <bean id="taskExecMessageSender" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="waimai"/>
        <property name="appkey" value="${app.name}"/>
        <property name="topic" value="${mq.producer.task.topic}"/>
        <property name="otherProperties">
            <map>
                <entry key="producer.send.mode" value="async"/>
            </map>
        </property>
    </bean>

    <bean class="com.meituan.mafka.client.bean.MafkaConsumer" init-method="start" destroy-method="close">
        <property name="namespace" value="waimai"/>
        <property name="appkey" value="${app.name}"/>
        <property name="topic" value="${mq.producer.task.topic}"/>
        <property name="group" value="${mq.consumer.task.group}"/>
        <!--以上配置请到消费组页面定制化demo查看 线下：mq.test.sankuai.com, 线上：mq.sankuai.com-->
        <property name="listener" ref="taskMessageListener"/>
        <property name="className" value="java.lang.String"/>
    </bean>

    <!-- FROM-SAAS-TASK END -->

</beans>
