package com.sankuai.meituan.shangou.empower.tms.delivery.task.application;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.meituan.shangou.empower.rider.client.common.PageInfoResp;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.dto.TaskDetailDto;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.dto.TaskItemDto;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.dto.TaskResultDto;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.req.TaskDetailReq;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.req.TaskListReq;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.resp.TaskDetailResp;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.resp.TaskListResp;
import com.sankuai.meituan.shangou.empower.task.dao.TaskFrameWorkMapper;
import com.sankuai.meituan.shangou.empower.task.dao.dbo.TaskInfoDO;
import com.sankuai.meituan.shangou.empower.task.dto.TaskPageQuery;
import com.sankuai.meituan.shangou.empower.task.manager.TaskManager;
import com.sankuai.meituan.shangou.empower.task.vo.PageListVo;
import com.sankuai.meituan.shangou.empower.task.vo.TaskResultVo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.constant.TaskConstants;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.dto.TaskReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.s3.S3ConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.net.URL;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/07/04
 */
@Slf4j
@Component
public class DeliveryTaskAppService {
    /**
     * 列表查询允许的每页最大值
     */
    private static final int MAX_TASK_LIST_PAGE_SIZE = 100;

    @Autowired
    private TaskManager taskManager;

    @Resource(name = "deliveryTaskService")
    private DeliveryTaskService taskService;

    @Resource
    private TaskFrameWorkMapper taskFrameWorkMapper;

    @Autowired
    private S3ConfigProperties s3ConfigProperties;

    @Resource
    private AmazonS3Client amazonS3Client;

    /**
     * 任务提交
     */
    @Transactional(transactionManager = "taskMybatisTransactionManager")
    public Status submit(TaskReq<String> req) {
        log.info("DeliveryTaskAppService.submit start, req:{}", JsonUtil.toJson(req));
        req.validate();
        TaskResultVo result = taskManager.taskSubmit(req.getTypeCode(), req);
        log.info("DeliveryTaskAppService.submit end, req:{}, resp:{}", JsonUtil.toJson(req), JsonUtil.toJson(result));
        return result.isSuccess() ? Status.SUCCESS : new Status(FailureCodeEnum.SYSTEM_ERROR.getCode(), result.getMsg());
    }

    /**
     * 任务列表查询
     */
    @Transactional(transactionManager = "taskMybatisTransactionManager")
    public TaskListResp queryListPageable(TaskListReq req) {
        log.info("DeliveryTaskAppService.queryListPageable start, req:{}", JsonUtil.toJson(req));
        validate(req);
        TaskPageQuery taskPageQuery = buildTaskPageQuery(req);

        PageListVo<TaskInfoDO> pageResult = taskService.queryTaskListByPage(taskPageQuery);
        TaskListResp taskListResp = ofTaskPageListResult(pageResult);
        log.info("DeliveryTaskAppService.queryListPageable end, req:{}, resp:{}", JsonUtil.toJson(req), JsonUtil.toJson(taskListResp));
        return taskListResp;
    }

    /**
     * 任务详情查询
     */
    public TaskDetailResp queryDetail(TaskDetailReq req) {
        validate(req);
        TaskInfoDO task = taskFrameWorkMapper.selectTaskInfoById(req.getTenantId(), req.getTaskId());
        boolean isNotFound = task == null;
        if (isNotFound) {
            return new TaskDetailResp(Status.SUCCESS, null);
        }

        // 暂不启用task_result_detail
        // List<TaskResultDetailDO> details = taskFrameWorkMapper.selectTaskDetailResultListByTaskId(req.getTenantId(), req.getTaskId());

        TaskDetailDto detail = ofTaskDetailItem(task);
        return new TaskDetailResp(Status.SUCCESS, detail);
    }

    private void validate(TaskDetailReq req) {
        Preconditions.checkNotNull(req, "入参为空");

        Preconditions.checkNotNull(req.getOperatorId(), "操作员不能为空");
        Preconditions.checkNotNull(req.getTenantId(), "租户id不能为空");
        Preconditions.checkNotNull(req.getTaskId(), "任务id不能为空");
    }

    private void validate(TaskListReq req) {
        Preconditions.checkNotNull(req, "入参为空");
        Preconditions.checkNotNull(req.getTenantId(), "租户id不能为空");

        Preconditions.checkNotNull(req.getPage(), "页码不能为空");
        Preconditions.checkArgument(req.getPage() > 0, "页码不能不于1");

        Preconditions.checkNotNull(req.getPageSize(), "每页数量不能为空");
        Preconditions.checkArgument(req.getPageSize() > 0, "每页数量不能小于1");

        Preconditions.checkNotNull(req.getBeginTime(), "开始时间不能为空");
        Preconditions.checkNotNull(req.getEndTime(), "结束时间不能为空");

        Preconditions.checkNotNull(req.getOperatorId(), "操作员不能为空");

        Preconditions.checkArgument(req.getPageSize() <= MAX_TASK_LIST_PAGE_SIZE, "每页数量不能超过{}", MAX_TASK_LIST_PAGE_SIZE);

        Preconditions.checkArgument(CollectionUtils.isNotEmpty(req.getTaskTypes()), "任务类型列表不能为空");
        for (Integer type : req.getTaskTypes()) {
            Preconditions.checkNotNull(type, "任务类型不能为空");
            Preconditions.checkArgument(type > TaskConstants.MIN_TASK_TYPE_CODE && type < TaskConstants.MAX_TASK_TYPE_CODE, "无法识别的任务类型：{}", type);
        }
        req.setTaskTypes(req.getTaskTypes().stream().distinct().collect(Collectors.toList()));
    }

    private TaskDetailDto ofTaskDetailItem(TaskInfoDO task) {
        TaskDetailDto detail = new TaskDetailDto();

        detail.setTaskId(task.getId());
        detail.setTaskName(task.getTaskName());
        detail.setTaskResult(transferResultFileName(task.getExecuteResult()));
        detail.setTaskType(task.getTaskType());
        detail.setTotalCount(task.getTotalNum());
        detail.setSuccessCount(task.getSuccessNum());
        detail.setFailedCount(task.getFailedNum());
        detail.setOperateTime(task.getCreateTime().getTime());
        detail.setOperatorName(task.getOperatorAccount());
        detail.setOperatorId(task.getOperatorId());
        detail.setTaskStatus(task.getTaskStatus());
        detail.setBeginTime(task.getBeginTime() == null ? null : task.getBeginTime().getTime());
        detail.setEndTime(task.getEndTime() == null ? null : task.getEndTime().getTime());
        detail.setCreateTime(task.getCreateTime().getTime());

        return detail;
    }

    private TaskItemDto ofTaskItemDto(TaskInfoDO task) {
        TaskItemDto item = new TaskItemDto();
        item.setTaskId(task.getId());
        item.setTaskName(task.getTaskName());
        item.setTaskResult(task.getExecuteResult());
        item.setTaskType(task.getTaskType());
        item.setTotalCount(task.getTotalNum());
        item.setSuccessCount(task.getSuccessNum());
        item.setFailedCount(task.getFailedNum());
        item.setOperateTime(task.getCreateTime().getTime());
        item.setOperatorName(task.getOperatorAccount());
        item.setOperatorId(task.getOperatorId());
        item.setTaskStatus(task.getTaskStatus());
        item.setBeginTime(task.getBeginTime() == null ? null : task.getBeginTime().getTime());
        item.setEndTime(task.getEndTime() == null ? null : task.getEndTime().getTime());
        item.setCreateTime(task.getCreateTime().getTime());
        return item;
    }

    private String transferResultFileName(String executeResult) {
        if (StringUtils.isEmpty(executeResult)) {
            return executeResult;
        }

        TaskResultDto taskResult = JsonUtil.fromJson(executeResult, TaskResultDto.class);
        if (StringUtils.isEmpty(taskResult.getFileName())) {
            return executeResult;
        }

        taskResult.setFileName(fileName2preSignedUrl(s3ConfigProperties.getBucket(), taskResult.getFileName(),
                s3ConfigProperties.getTempUrlExpiredSeconds()));

        return JsonUtil.toJson(taskResult);
    }

    public String fileName2preSignedUrl(String bucket, String objectName, long expiredSeconds) {
        try {
            long milliSeconds = System.currentTimeMillis() + 1000 * expiredSeconds;
            GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bucket, objectName);
            generatePresignedUrlRequest.setMethod(HttpMethod.GET);
            generatePresignedUrlRequest.setExpiration(new Date(milliSeconds));

            URL url = amazonS3Client.generatePresignedUrl(generatePresignedUrlRequest);
            return url.toString();
        } catch (Exception e) {
            log.error("fileName2preSignedUrl error, bucket={}, object={}, expiredSeconds={}, error={}", bucket, objectName,
                    expiredSeconds, e.getMessage(), e);
            throw e;
        }
    }

    private TaskListResp ofTaskPageListResult(PageListVo<TaskInfoDO> result) {
        TaskListResp resp = new TaskListResp();
        resp.setStatus(Status.SUCCESS);
        resp.setPage(new PageInfoResp(result.getPage(), result.getPageSize(), result.getTotalRecords()));
        resp.setItems(result.getData().stream()
                .map(this::ofTaskItemDto)
                .collect(Collectors.toList())
        );
        return resp;
    }

    private TaskPageQuery buildTaskPageQuery(TaskListReq req) {
        TaskPageQuery queryReq = new TaskPageQuery();
        queryReq.setPage(req.getPage());
        queryReq.setPageSize(req.getPageSize());
        queryReq.setTenantId(req.getTenantId());
        queryReq.setTaskTypeList(req.getTaskTypes());
        queryReq.setOperatorId(req.getOperatorId());
        queryReq.setUpdateBeginTime(new Date(req.getBeginTime()));
        queryReq.setUpdateEndTime(new Date(req.getEndTime()));
        return queryReq;
    }
}
