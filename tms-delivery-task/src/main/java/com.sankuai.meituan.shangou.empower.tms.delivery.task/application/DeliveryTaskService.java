package com.sankuai.meituan.shangou.empower.tms.delivery.task.application;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.task.constant.enums.TaskStatusEnum;
import com.sankuai.meituan.shangou.empower.task.dao.dbo.TaskInfoDO;
import com.sankuai.meituan.shangou.empower.task.service.impl.TaskServiceImpl;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.DeliveryTaskFrameWorkMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.request.TaskRangeDuplicateQueryParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 配送excel任务服务

 * <AUTHOR>
 * @since 2022/07/04
 */
@Slf4j
@Service("deliveryTaskService")
@Primary
public class DeliveryTaskService extends TaskServiceImpl {
    @Autowired
    private DeliveryTaskFrameWorkMapper mapper;

    public List<TaskInfoDO> queryRunningAndWaitingTaskByDuplicateCheck(Long tenantId, String executeParam, int taskType, Long operatorId,
                                                                       Date createTime, Date endTime) {
        TaskRangeDuplicateQueryParam param = TaskRangeDuplicateQueryParam.builder()
                .executeParam(executeParam)
                .createBeginTime(createTime)
                .createEndTime(endTime)
                .operatorId(operatorId)
                .taskStatusList(Lists.newArrayList(TaskStatusEnum.READY.getCode(), TaskStatusEnum.RUNNING.getCode()))
                .taskType(taskType)
                .tenantId(tenantId)
                .build();
        return mapper.queryRunningAndWaitingTaskByDuplicateKey(param);
    }
}
