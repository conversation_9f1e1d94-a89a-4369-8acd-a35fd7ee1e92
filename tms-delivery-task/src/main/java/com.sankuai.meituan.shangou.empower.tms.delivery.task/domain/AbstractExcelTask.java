package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.dto.TaskResultDto;
import com.sankuai.meituan.shangou.empower.task.constant.enums.TaskResultCodeEnum;
import com.sankuai.meituan.shangou.empower.task.constant.enums.TaskStatusEnum;
import com.sankuai.meituan.shangou.empower.task.dao.dbo.TaskInfoDO;
import com.sankuai.meituan.shangou.empower.task.domain.TaskDomain;
import com.sankuai.meituan.shangou.empower.task.dto.TaskCreateDTO;
import com.sankuai.meituan.shangou.empower.task.exception.TaskException;
import com.sankuai.meituan.shangou.empower.task.template.AbstractTask;
import com.sankuai.meituan.shangou.empower.task.vo.TaskResultVo;
import com.sankuai.meituan.shangou.empower.task.vo.TaskRunVo;


import com.sankuai.meituan.shangou.empower.tms.delivery.task.application.DeliveryTaskService;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.dto.TaskReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.ExcelTaskConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.config.TaskConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.sankuai.meituan.shangou.empower.task.constant.enums.TaskResultCodeEnum.TASK_REPEAT_ERROR;

/**
 * 异步Excel任务基类。
 *
 * <AUTHOR>
 * @since 2021/10/03
 */
@Slf4j
public abstract class AbstractExcelTask extends AbstractTask<TaskReq<String>, String> {

    @Autowired
    private ExcelTaskConfig excelTaskConfig;

    @Resource(name = "deliveryTaskService")
    private DeliveryTaskService deliveryTaskService;

    private static final ObjectMapper mapper = new ObjectMapper();

    /**
     * 任务通用线程池
     */
    private static ThreadPoolExecutor commonTaskPool = new ThreadPoolExecutor(4, 4, 10, TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(100000),
            new ThreadFactoryBuilder().setNameFormat("purchase-excel-common-task-pool" + "-%d").build());

    static {
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN, true);
        mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    /**
     * 任务并行数量 key:唯一建 value:数量
     * 默认不控制，返回null
     *
     * @param task
     * @return
     */
    protected Pair<String, Integer> parallelRunNum(TaskDomain<String> task) {
        TaskConfig taskConfig = excelTaskConfig.getByTypeCode(getTaskType());
        if (null == taskConfig.getMaxParallelRunNum()) {
            return null;
        }

        String uniqKey = defaultKey(task.getTenantId());
        Integer maxParallelRunNum = taskConfig.getMaxParallelRunNum();
        return Pair.of(uniqKey, maxParallelRunNum);
    }


    /**
     * 默认唯一键生成方式，tenantId + taskType 子类可重写
     *
     * @return
     */
    protected String defaultKey(Long tenantId) {
        return tenantId + "_" + getTaskType();
    }

    /**
     * 是否验证重复任务, 默认不校验重复任务
     */
    protected boolean isVerifyDuplicateTask() {
        TaskConfig taskConfig = excelTaskConfig.getByTypeCode(getTaskType());
        return taskConfig.isVerifyDuplicateTask();
    }


    /**
     * 重复任务校验
     *
     * @param tasks
     */
    protected void verifyDuplicateTask(List<TaskCreateDTO<String>> tasks) {
        if (!isVerifyDuplicateTask()) {
            return;
        }
        TaskCreateDTO<String> taskCreateDTO = tasks.get(0);

        Long tenantId = taskCreateDTO.getTenantId();
        Integer taskType = taskCreateDTO.getTaskType();
        Date endTime = new Date();
        Date beginTime = new DateTime(endTime.getTime()).minusDays(1).toDate();
        String executeParam = taskCreateDTO.getExecuteParam();
        Long operatorId = taskCreateDTO.getOperatorId();
        List<TaskInfoDO> taskList = deliveryTaskService.queryRunningAndWaitingTaskByDuplicateCheck(tenantId, executeParam, taskType, operatorId, beginTime,
                endTime);
        if (!CollectionUtils.isEmpty(taskList)) {
            throw new TaskException(TASK_REPEAT_ERROR);
        }
    }

    /**
     * 获取任务执行线程池，任务使用独立线程时需要自定义
     * <li>***在任务对象初始化时创建线程池，不要在该方法中创建线程池**</li>
     *
     * @return 线程池
     */
    protected ThreadPoolExecutor getTaskThreadPoolExecutor() {
        return commonTaskPool;
    }

    /**
     * 任务参数校验
     * <p>当任务提交到异步消息队列前被调用。如果这里返回错误，任务立即结束并返回</p>
     */
    @Override
    protected TaskResultVo verifyRequestAndBusiness(TaskReq<String> taskReq) {
        return new TaskResultVo(TaskResultCodeEnum.SUCCESS.getCode(), null);
    }

    /**
     * 任务对象创建
     * <p>任务参数校验后被调用。保存到数据库中的，和发送到消息队列的，都是这个结果数据。通常一对一生成，如果需要任务一拆多的，也可以在这里完成</p>
     */
    @Override
    protected List<TaskCreateDTO<String>> createTaskDomain(TaskReq<String> taskReq) {
        TaskCreateDTO<String> createReq = new TaskCreateDTO<>();
        createReq.setTenantId(taskReq.getTenantId());
        createReq.setTaskType(taskReq.getTypeCode());
        createReq.setTaskName(taskReq.getTypeName());
        createReq.setOperatorId(taskReq.getOperatorId());
        createReq.setOperatorAccount(taskReq.getOperatorAccount());
        createReq.setExecuteParam(taskReq.getParam());

        return Collections.singletonList(createReq);
    }

    @Override
    protected final Class<String> getGenericTypeOfParameter() {
        return String.class;
    }

    protected TaskRunVo buildFailureResult(TaskDomain<String> task, String failureMessage, String detailMessage) {
        return buildResult(task, TaskStatusEnum.FAILURE, 0, 0, 0,
                new TaskResultDto(failureMessage, detailMessage, null));
    }

    protected TaskRunVo buildSuccessResult(TaskDomain<String> task, int totalNum, int successNum, int failedNum, TaskResultDto result) {
        return buildResult(task, TaskStatusEnum.SUCCESS, totalNum, successNum, failedNum, result);
    }

    protected TaskRunVo buildResult(TaskDomain<String> task, TaskStatusEnum status, int totalNum, int successNum, int failedNum,
                                    TaskResultDto result) {
        try {
            TaskRunVo runRes = new TaskRunVo();
            runRes.setTenantId(task.getTenantId());
            runRes.setTaskId(task.getTaskId());
            runRes.setTotalNum(totalNum);
            runRes.setSuccessNum(successNum);
            runRes.setFailedNum(failedNum);
            runRes.setTaskStatus(status.getCode());
            runRes.setExecuteResult(result == null ? "{}" : mapper.writeValueAsString(result));
            runRes.setTaskResultDetailList(new ArrayList<>());
            return runRes;
        } catch (JsonProcessingException e) {
            throw new CommonRuntimeException("解析JSON失败", e);
        }
    }
}
