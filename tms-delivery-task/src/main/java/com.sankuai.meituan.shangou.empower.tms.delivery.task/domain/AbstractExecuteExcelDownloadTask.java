package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain;

import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.dto.TaskResultDto;
import com.sankuai.meituan.shangou.empower.task.domain.TaskDomain;
import com.sankuai.meituan.shangou.empower.task.vo.TaskRunVo;

import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.dto.ExecuteTaskRunResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.component.TaskExcelComponent;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.config.TaskConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;

/**
 * 抽象执行业务任务
 *
 * <AUTHOR> on 2021-11-24
 */
@Slf4j
public abstract class AbstractExecuteExcelDownloadTask<T> extends AbstractExcelDownloadTask<T> {

    @Override
    protected TaskRunVo invokeAndBuildResult(TaskDomain<String> task, TaskConfig taskConfig, T param) {
        ExecuteTaskRunResult executeTaskRunResult = execute(task.getTenantId(), task.getOperatorId(), param);
        TaskExcelComponent.Sheet[] sheets = executeTaskRunResult.getResultSheets();
        if (ArrayUtils.isNotEmpty(sheets)) {
            String tempFilePre = task.getOperatorId() + "-" + getTaskType();
            TaskExcelComponent.Result result = taskExcelComponent.uploadExcel(taskConfig.isUploadUseFileBuffer(), tempFilePre, sheets);
            return buildTaskRunResult(task, executeTaskRunResult,
                    new TaskResultDto(taskConfig.getSuccessFileDescription(), null, result.getFileName()));
        }
        return buildTaskRunResult(task, executeTaskRunResult, null);

    }

    @Override
    protected TaskExcelComponent.Sheet[] getSheets(long tenantId, long operatorId, T param) {
        return new TaskExcelComponent.Sheet[]{};
    }

    /**
     * 执行任务, 并返回执行结果
     *
     * @param tenantId
     * @param operatorId
     * @param param
     * @return
     */
    protected abstract ExecuteTaskRunResult execute(long tenantId, long operatorId, T param);

    /**
     * 抽象任务执行返回值
     *
     * @return
     */
    protected TaskRunVo buildTaskRunResult(TaskDomain<String> task,
                                           ExecuteTaskRunResult executeTaskRunResult,
                                           TaskResultDto result) {
        return buildSuccessResult(task, executeTaskRunResult.getTotalNum(), executeTaskRunResult.getSuccessNum(),
                executeTaskRunResult.getFailedNum(), result);
    }
}

