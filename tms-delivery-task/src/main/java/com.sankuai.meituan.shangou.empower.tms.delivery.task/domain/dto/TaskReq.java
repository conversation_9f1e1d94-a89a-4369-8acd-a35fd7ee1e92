package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.dto;


import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2021/09/29
 */
@Getter
@Setter
public class TaskReq<T> {
    private int typeCode;
    private String typeName;

    private long tenantId;
    private long operatorId;
    private String operatorAccount;

    private T param;

    public void validate() {
        Preconditions.checkArgument(tenantId > 0, "租户id异常");
        Preconditions.checkArgument(operatorId > 0, "操作者id异常");
        Preconditions.checkArgument(StringUtils.isNotBlank(operatorAccount), "操作者账号不能为空");
    }
}
