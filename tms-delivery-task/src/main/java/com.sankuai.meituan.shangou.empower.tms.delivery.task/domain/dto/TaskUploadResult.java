package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.dto;

import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.dto.TaskResultDto;
import com.sankuai.meituan.shangou.empower.task.constant.enums.TaskStatusEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2021/10/08
 */
@Getter
@Setter
@ToString
@Builder
public class TaskUploadResult {
    private TaskStatusEnum status;
    private int totalNum;
    private int successNum;
    private int failedNum;
    private TaskResultDto result;
}
