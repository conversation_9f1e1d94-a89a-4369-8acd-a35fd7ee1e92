package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.dto;

import static org.apache.commons.lang.StringUtils.trimToEmpty;

import java.util.List;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.task.dto.TaskResultDetailDTO;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: linjianyu <br>
 * @since: 7/6/21 Time: 6:46 PM
 */
public class TaskRunResult {

    @Getter
    private final List<TaskResultDetailDTO> failedDetails = Lists.newArrayList();
    @Getter
    private final List<TaskResultDetailDTO> successDetails = Lists.newArrayList();

    @Getter
    private final Integer totalNum;

    private int successNum = 0;
    private int failedNum = 0;

    @Setter @Getter
    private String executeResult;

    public TaskRunResult(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public void executeResult(Object obj) {
        this.executeResult = trimToEmpty(JsonUtil.toJson(obj));
    }

    public void addSuccessDetail(TaskResultDetailDTO detail) {
        if (detail != null) {
            this.successDetails.add(detail);
            this.successNum = this.successDetails.size();
        }
    }

    public void addFailedDetail(TaskResultDetailDTO detail) {
        if (detail != null) {
            this.failedDetails.add(detail);
            this.failedNum = this.failedDetails.size();
        }
    }

    public int getSuccessNum() {
        this.successNum = this.successDetails.size();
        return successNum;
    }

    public int getFailedNum() {
        this.failedNum = this.failedDetails.size();
        return failedNum;
    }

    public List<TaskResultDetailDTO> allDetailResults() {
        List<TaskResultDetailDTO> all = Lists.newArrayList();
        all.addAll(successDetails);
        all.addAll(failedDetails);
        return all;
    }
}
