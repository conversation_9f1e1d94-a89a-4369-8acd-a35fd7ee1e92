package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain;

import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.task.dto.TaskResultDto;
import com.sankuai.meituan.shangou.empower.task.constant.enums.TaskResultCodeEnum;
import com.sankuai.meituan.shangou.empower.task.domain.TaskDomain;
import com.sankuai.meituan.shangou.empower.task.vo.TaskResultVo;
import com.sankuai.meituan.shangou.empower.task.vo.TaskRunVo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.dto.TaskReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.ExcelTaskConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.component.TaskExcelComponent;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.config.TaskConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.ParameterizedType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 异步Excel下载任务。
 * <p>上传的任务参数是指定类型的JSON</p>
 *
 * @param <T> 任务请求的JSON字符串所对应的JAVA对象
 * <AUTHOR>
 * @since 2021/10/03
 */
@Slf4j
public abstract class AbstractExcelDownloadTask<T> extends AbstractExcelTask {

    @Autowired
    protected ExcelTaskConfig excelTaskConfig;

    @Autowired
    protected TaskExcelComponent taskExcelComponent;

    @Override
    protected TaskResultVo verifyRequestAndBusiness(TaskReq<String> taskReq) {
        try {
            T param = JsonUtil.fromJson(taskReq.getParam(), getParamType());
            return checkParam(taskReq, param);
        } catch (Exception e) {
            log.error("AbstractExcelDownloadTask, req={}, error={}", JsonUtil.toJson(taskReq), e.getMessage(), e);
            return new TaskResultVo(TaskResultCodeEnum.PARAM_ERROR.getCode(), "入参无法被解析");
        }
    }

    /**
     * 获取当前类（子类）的泛型类
     */
    protected Class<T> getParamType() {
        return (Class) ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    /**
     * 请求参数的检查函数，在任务准备提交消息队列前被调用，如果函数返回错误，任务立即结束并返回
     * <p>必须实现这个检查函数，如果不需要检查的，需要直接返回成功</p>
     * <p>
     * 直接返回成功的示例参考:
     * <code>return new TaskResultVo(TaskResultCodeEnum.SUCCESS.getCode(), null);</code>
     * </p>
     *
     * @param taskReq 原始的任务请求
     * @param param   任务请求中的字符串对应的反序列化后的对象
     * @return 检查结果。请求错误的信息会向上返回
     */
    protected abstract TaskResultVo checkParam(TaskReq<String> taskReq, T param);

    @Override
    protected TaskRunVo run(TaskDomain<String> task) {
        if (StringUtils.isBlank(task.getExecuteParam())) {
            return buildFailureResult(task, "任务参数为空，无法被识别", null);
        }

        TaskConfig taskConfig = excelTaskConfig.getByTypeCode(getTaskType());
        if (taskConfig == null) {
            return buildFailureResult(task, "未找到当前任务的配置信息", null);
        }

        log.info("Task received, type={}, run={}", getTaskType(), JsonUtil.toJson(task));

        T param = JsonUtil.fromJson(task.getExecuteParam(), getParamType());

        try {
            TaskRunVo taskRun = invokeAndBuildResult(task, taskConfig, param);
            log.info("Task end, type={}, run={}, result={}", getTaskType(), JsonUtil.toJson(task), JsonUtil.toJson(taskRun));
            return taskRun;
        } catch (Exception e) {
            log.error("AbstractExcelDownloadTask run error, req={}, error={}", JsonUtil.toJson(task), e.getMessage(), e);
            return buildFailureResult(task, "任务执行时遇到异常，请稍后重试", e.getMessage());
        }
    }

    protected TaskRunVo invokeAndBuildResult(TaskDomain<String> task, TaskConfig taskConfig, T param) {
        TaskExcelComponent.Sheet[] sheets = getSheets(task.getTenantId(), task.getOperatorId(), param);
        String tempFilePre = getExcelName();
        TaskExcelComponent.Result result = taskExcelComponent.uploadExcel(taskConfig.isUploadUseFileBuffer(), tempFilePre, sheets);

        TaskRunVo taskRun =
                buildSuccessResult(task, 1, 1, 0, new TaskResultDto(taskConfig.getSuccessFileDescription(),
                        null, result.getFileName()));
        taskRun.setTotalNum(result.getDataSize());
        taskRun.setSuccessNum(result.getDataSize());
        return taskRun;
    }

    /**
     * 具体的需要生成sheet的属性信息
     */
    protected abstract TaskExcelComponent.Sheet[] getSheets(long tenantId, long operatorId, T param);

    /**
     * 默认实现：获取excel名称自定义前缀部分  整体格式：自定义前缀-时间戳-随机数.xlsx
     * @return
     */
    protected String getExcelName() {
        return "已上报异常导出";
    }
}
