package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.style;

import java.util.HashMap;
import java.util.Map;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Workbook;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.util.StyleUtil;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.AbstractCellStyleStrategy;

/**
 * <AUTHOR>
 * @since 2021/10/27
 */
public abstract class MultiHeadVerticalCellStyleStrategy extends AbstractCellStyleStrategy {

    private Workbook workbook;
    private Map<Integer, Map<Integer, CellStyle>> headCellStyleCache = new HashMap<>();
    private Map<Integer, CellStyle> contentCellStyleCache = new HashMap<>();

    @Override
    protected void initCellStyle(Workbook workbook) {
        this.workbook = workbook;
    }

    @Override
    protected void setHeadCellStyle(Cell cell, Head head, Integer relativeRowIndex) {
        int columnIndex = head.getColumnIndex();
        int rowIndex = cell.getRowIndex();
        Map<Integer, CellStyle> columnMap = headCellStyleCache.get(rowIndex);
        if (columnMap != null && columnMap.containsKey(columnIndex)) {
            CellStyle cellStyle = columnMap.get(columnIndex);
            if (cellStyle != null) {
                cell.setCellStyle(cellStyle);
            }
            return;
        }

        WriteCellStyle headCellStyle = headCellStyle(cell, head);
        columnMap = columnMap != null ? columnMap : headCellStyleCache.computeIfAbsent(rowIndex, k -> new HashMap<>());

        if (headCellStyle == null) {
            columnMap.put(columnIndex, null);
        }
        else {
            CellStyle cellStyle = StyleUtil.buildHeadCellStyle(workbook, headCellStyle);
            columnMap.put(columnIndex, cellStyle);
            cell.setCellStyle(cellStyle);
        }
    }

    @Override
    protected void setContentCellStyle(Cell cell, Head head, Integer relativeRowIndex) {
        int columnIndex = head.getColumnIndex();
        if (contentCellStyleCache.containsKey(columnIndex)) {
            CellStyle cellStyle = contentCellStyleCache.get(columnIndex);
            if (cellStyle != null) {
                cell.setCellStyle(cellStyle);
            }
            return;
        }
        WriteCellStyle contentCellStyle = contentCellStyle(head);
        if (contentCellStyle == null) {
            contentCellStyleCache.put(columnIndex, null);
        }
        else {
            CellStyle cellStyle = StyleUtil.buildContentCellStyle(workbook, contentCellStyle);
            contentCellStyleCache.put(columnIndex, cellStyle);
            cell.setCellStyle(cellStyle);
        }
    }

    /**
     * Returns the column width corresponding to each column head
     */
    protected abstract WriteCellStyle headCellStyle(Cell cell, Head head);

    /**
     * Returns the column width corresponding to each column head
     */
    protected abstract WriteCellStyle contentCellStyle(Head head);
}