package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.config;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import lombok.Getter;
import lombok.Setter;

/**
 * tasks.[item]
 *
 * <AUTHOR>
 * @since 2021/09/30
 */
@Getter
@Setter
public class TaskConfig {
    private int type;
    private boolean uploadUseFileBuffer;
    //是否开启重复任务校验
    private boolean verifyDuplicateTask;
    //指定最大并行任务数量
    private Integer maxParallelRunNum;
    private String successFileDescription = "下载结果";
    private String failureFileDescription = "下载失败信息";
    private List<TaskSheet> sheets;

    public void init() {
        if (sheets != null) {
            Set<Integer> sheetIndexSet = new HashSet<>();
            for (TaskSheet sheet : sheets) {
                if (sheetIndexSet.contains(sheet.getSheetIndex())) {
                    throw new IllegalStateException("重复的sheetIndex: " + sheet.getSheetIndex() + ", sheetName=" + sheet.getName());
                }
                sheetIndexSet.add(sheet.getSheetIndex());
                sheet.init();
            }
        }
    }
}
