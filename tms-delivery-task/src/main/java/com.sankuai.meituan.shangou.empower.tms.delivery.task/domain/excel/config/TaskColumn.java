package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.config;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.context.IllegalMessageListContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cglib.core.ReflectUtils;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;

import java.beans.PropertyDescriptor;
import java.util.List;

/**
 * tasks.sheets.columns
 *
 * <AUTHOR>
 * @since 2021/09/30
 */
@Getter
@Setter
public class TaskColumn {
    private static final ExpressionParser expressionParser = new SpelExpressionParser();

    // 以下来自配置文件

    private String title;
    private String verifyExpression;
    private String customIllegalMessage;
    private String field;

    // 以下来自预置初始化

    private boolean requiredCheck;

    private TaskColumnType columnType;

    private Expression typeExpression;
    private Expression customExpression;

    public void init(Class ormClassType) {
        // 配置了field的列才需要解析
        if (StringUtils.isNotEmpty(field)) {
            analyseFiledAndColumnType(ormClassType);
            initExpression();
            requiredCheck = true;
        } else {
            requiredCheck = false;
        }
    }

    private void initExpression() {
        if (columnType.getExtendVerifyExpression() != null) {
            typeExpression = expressionParser.parseExpression(columnType.getExtendVerifyExpression());
        }

        if (StringUtils.isNotEmpty(verifyExpression)) {
            customExpression = expressionParser.parseExpression(verifyExpression);
        }
    }

    private void analyseFiledAndColumnType(Class ormClassType) {
        PropertyDescriptor[] beanProperties = ReflectUtils.getBeanProperties(ormClassType);
        boolean found = false;
        for (PropertyDescriptor property : beanProperties) {
            if (property.getName().equals(field)) {
                found = true;
                Class<?> propertyType = property.getPropertyType();
                columnType = TaskColumnType.parseFromClass(propertyType);
                if (columnType == null) {
                    throw new IllegalStateException("Class: " + ormClassType.getName() + ", title=" + title + ", field: " + field + " " +
                            "无法被识别为有效的Excel字段");
                }
                break;
            }
        }
        if (!found) {
            throw new IllegalStateException("Class: " + ormClassType.getName() + ", title=" + title + ", field: " + field + " 未找到");
        }
    }

    /**
     * 校验并返回异常信息，如果有异常，返回错误信息，如果没有返回null
     */
    public String checkAndReturnErrorMessage(EvaluationContext expressionContext) {
        // 类型表达式
        if (typeExpression != null) {
            String checkResult = checkTypeExpression(expressionContext);
            if (checkResult != null) {
                return checkResult;
            }
        }

        // 校验表达式
        if (customExpression != null) {
            return checkCustomExpression(expressionContext);
        }

        return null;
    }

    private String checkCustomExpression(EvaluationContext expressionContext) {
        IllegalMessageListContext.clear();
        Boolean value = customExpression.getValue(expressionContext, Boolean.class);
        Preconditions.checkNotNull(value, "校验结果为null, expression={}", verifyExpression);

        if (Boolean.FALSE.equals(value)) {
            if (useCustomIllegalMessage()) {
                return customIllegalMessage;
            }

            List<String> errorList = IllegalMessageListContext.getList();
            StringBuilder builder = new StringBuilder(title);
            for (int i = 0; i < errorList.size(); i++) {
                if (i > 0) {
                    builder.append("或者");
                }
                builder.append(errorList.get(i));
            }
            return builder.toString();
        }

        return null;
    }

    private String checkTypeExpression(EvaluationContext expressionContext) {
        Boolean value = typeExpression.getValue(expressionContext, Boolean.class);
        Preconditions.checkNotNull(value, "校验结果为null, type expression={}", columnType.getExtendVerifyExpression());
        if (!value) {
            return useCustomIllegalMessage() ? customIllegalMessage : title + "要求是" + columnType.getName();
        }

        return null;
    }

    private boolean useCustomIllegalMessage() {
        return StringUtils.isNotBlank(customIllegalMessage);
    }
}
