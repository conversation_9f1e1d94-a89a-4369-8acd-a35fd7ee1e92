package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.config;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/10/02
 */
public enum TaskColumnType {
    INT("整型", "int", "#int(#val)"),
    INT_WRAPPER("包装整型", "java.lang.Integer", "#empty(#val) || #int(#val)"),

    LONG("长整型", "long", "#long(#val)"),
    LONG_WRAPPER("包装长整型", "java.lang.Long", "#empty(#val) || #long(#val)"),

    DOUBLE("浮点型", "double", "#double(#val)"),
    DOUBLE_WRAPPER("包装浮点型", "java.lang.Double", "#empty(#val) || #double(#val)"),

    STRING("字符串", "java.lang.String", null),
    ;

    private String name;
    private String className;
    private String extendVerifyExpression;

    TaskColumnType(String name, String className, String extendVerifyExpression) {
        this.name = name;
        this.className = className;
        this.extendVerifyExpression = extendVerifyExpression;
    }

    public String getClassName() {
        return className;
    }

    public String getName() {
        return name;
    }

    public String getExtendVerifyExpression() {
        return extendVerifyExpression;
    }

    public boolean isDouble() {
        return this == DOUBLE || this == DOUBLE_WRAPPER;
    }

    // static
    // ----------------------------------------------------------------------

    private static final Map<String, TaskColumnType> map;
    static {
        map = Arrays.stream(values()).collect(Collectors.toMap(TaskColumnType::getClassName, Function.identity()));
    }

    public static TaskColumnType parseFromClass(Class<?> classType) {
        return map.get(classType.getName());
    }
}
