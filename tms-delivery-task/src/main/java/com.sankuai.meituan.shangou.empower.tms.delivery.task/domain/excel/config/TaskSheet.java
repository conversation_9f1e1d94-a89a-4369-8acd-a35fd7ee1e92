package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.config;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * tasks.sheets
 *
 * <AUTHOR>
 * @since 2021/09/30
 */
@Getter
@Setter
public class TaskSheet {

    // 以下来自配置文件

    private String name;
    private int sheetIndex;
    private String ormClass;
    private int headRowNumber;
    private int minRowLimit;
    private int maxRowLimit;
    private List<TaskColumn> columns;

    // 以下来自预置初始化

    private Class ormClassType;

    public void init() {
        try {
            ormClassType = Class.forName(ormClass);
        }
        catch (ClassNotFoundException e) {
            throw new IllegalStateException(e);
        }

        columns.forEach(item -> item.init(ormClassType));
    }
}
