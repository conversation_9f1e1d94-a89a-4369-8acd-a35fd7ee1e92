package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel;

import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.config.TaskConfig;

import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.config.TaskConfigs;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.function.VerifyFunction;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.function.VerifyFunctions;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.util.ReflectionUtils;

import com.esotericsoftware.yamlbeans.YamlException;
import com.esotericsoftware.yamlbeans.YamlReader;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/09/30
 */
@Slf4j
public class ExcelTaskConfig {
    /**
     * 任务的配置信息
     */
    private Map<Integer, TaskConfig> codeTaskConfigMap;

    /**
     * 函数工具类集合
     */
    private final Map<String, Method> verifyFunctionMap;

    public ExcelTaskConfig(String configContent) {
        codeTaskConfigMap = createCodeTaskConfigMap(configContent);
        verifyFunctionMap = createVerifyFunctionMap();
    }

    public TaskConfig getByTypeCode(int taskTypeCode) {
        return codeTaskConfigMap.get(taskTypeCode);
    }

    public EvaluationContext createEvaluationContext() {
        StandardEvaluationContext context = new StandardEvaluationContext();
        for (Map.Entry<String, Method> entry : verifyFunctionMap.entrySet()) {
            context.registerFunction(entry.getKey(), entry.getValue());
        }

        return context;
    }

    private Map<String, Method> createVerifyFunctionMap() {
        Map<String, Method> map = new HashMap<>();

        ReflectionUtils.doWithLocalMethods(VerifyFunctions.class, method -> {
            int modifiers = method.getModifiers();
            if (!Modifier.isStatic(modifiers)) {
                // 只保留静态函数
                return;
            }

            VerifyFunction[] functionNames = method.getAnnotationsByType(VerifyFunction.class);
            if (functionNames.length == 0) {
                return;
            }

            for (VerifyFunction name : functionNames) {
                map.put(name.value(), method);
            }
        });

        return map;
    }

    private Map<Integer, TaskConfig> createCodeTaskConfigMap(String configContent) {
        TaskConfigs taskTypeConfigs = createTaskTypeConfigs(configContent);

        Map<Integer, TaskConfig> map = taskTypeConfigs.getTasks().stream()
                .collect(Collectors.toMap(TaskConfig::getType, Function.identity()));

        map.values().forEach(TaskConfig::init);
        return map;
    }

    private TaskConfigs createTaskTypeConfigs(String config) {
        try {
            YamlReader reader = new YamlReader(config);
            // 忽略未知属性
            reader.getConfig().readConfig.setIgnoreUnknownProperties(true);
            return reader.read(TaskConfigs.class);
        } catch (YamlException e) {
            throw new CommonRuntimeException("Failed to parse [Excel Async Task Config]", e);
        }
    }
}
