package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.LinkedHashMap;
import java.util.Map;

import com.sankuai.meituan.shangou.empower.tms.delivery.task.constant.TaskConstants;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.util.StringCheckUtils;
import org.apache.poi.ss.usermodel.ExcelGeneralNumberFormat;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.event.SyncReadListener;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.read.listener.ModelBuildEventListener;
import com.alibaba.excel.read.metadata.holder.ReadHolder;
import com.alibaba.excel.util.ConverterUtils;

/**
 * 自定义的Excel读取，主要解决浮点数的精度问题
 *
 * <AUTHOR>
 * @since 2021/10/18
 */
public class TaskExcelSyncReadListener extends SyncReadListener {
    /**
     * 当Excel数据是double时，浮点数的精度控制。具体参见 {@link ExcelGeneralNumberFormat}
     */
    private static final MathContext TO_10_SF = new MathContext(TaskConstants.MAX_EXCEL_DOUBLE_SCALE, RoundingMode.HALF_UP);

    @Override
    public void invoke(Object object, AnalysisContext context) {
        ReadHolder currentReadHolder = context.currentReadHolder();
        Map<Integer, CellData> cellDataMap = (Map<Integer, CellData>) object;
        Object stringList = buildStringList(cellDataMap, currentReadHolder, context);
        super.invoke(stringList, context);
    }

    /**
     *
     *
     * @param cellDataMap
     * @param currentReadHolder
     * @param context
     * @return
     */
    private Object buildStringList(Map<Integer, CellData> cellDataMap, ReadHolder currentReadHolder, AnalysisContext context) {
        int index = 0;
        Map<Integer, String> map = new LinkedHashMap<Integer, String>(cellDataMap.size() * 4 / 3 + 1);
        for (Map.Entry<Integer, CellData> entry : cellDataMap.entrySet()) {
            Integer key = entry.getKey();
            CellData cellData = entry.getValue();
            while (index < key) {
                map.put(index, null);
                index++;
            }
            index++;
            if (cellData.getType() == CellDataTypeEnum.EMPTY) {
                map.put(key, null);
                continue;
            }

            String stringValue = (String) ConverterUtils.convertToJavaObject(cellData, null, null, currentReadHolder.converterMap(),
                    currentReadHolder.globalConfiguration(), context.readRowHolder().getRowIndex(), key);

            /**
             * Excel转double时，未对精度做控制，但在使用string或软件显示时，又对精度做了默认控制，所以这里需要对double做精度控制，以防止12.2被识别成12.199999999999的问题。
             * 具体参见 {@link ExcelGeneralNumberFormat}
             */
            if (cellData.getType() == CellDataTypeEnum.NUMBER) {
                if (StringCheckUtils.isDouble(stringValue)) {
                    stringValue = new BigDecimal(stringValue).round(TO_10_SF).stripTrailingZeros().toPlainString();
                }
            }

            map.put(key, stringValue);
        }
        return map;
    }
}
