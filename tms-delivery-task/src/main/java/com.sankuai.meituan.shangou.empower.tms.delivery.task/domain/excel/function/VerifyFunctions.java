package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.function;

import java.math.BigDecimal;
import java.text.ParseException;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.constant.TaskConstants;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.context.IllegalMessageListContext;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.util.StringCheckUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;


/**
 * expression表达式，注入到SpEL表达式中
 * <p>当需要增加自定义函数时，可以在此类中增加。本类中定义的 {@link VerifyFunction} 会在服务启动时被扫描到。</p>
 * <p>需要新增函数时注意：当判定条件为false时，需要通过{@link IllegalMessageListContext#appendIllegalMessage} 添加失败的信息描述，
 * 该描述会由框架扫描后添加到提示中</p>
 *
 * <AUTHOR>
 * @since 2021/10/01
 */
public class VerifyFunctions {

    private VerifyFunctions() {

    }

    // 通用
    // ----------------------------------------------------------------------

    /**
     * 必填
     */
    @VerifyFunction("require")
    public static boolean require(Object val) {
        if (val != null && val.toString().length() > 0) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必填");
        return false;
    }

    // String 相关
    // ----------------------------------------------------------------------

    /**
     * 必须是选项列表中的一项
     */
    @VerifyFunction("options")
    public static boolean options(Object val, String... options) {
        if (options == null || options.length == 0) {
            throw new IllegalStateException("VerifyFunctions #options, 可选项必须定义!");
        }

        for (String option : options) {
            if (option.equals(val)) {
                return true;
            }
        }

        IllegalMessageListContext.appendIllegalMessage("必须为(" + String.join(",", options) + ")中的一个");
        return false;
    }

    /**
     * 必须为空。
     * <p>可以用于某项必须为空或者为其他某些限定时。</p>
     */
    @VerifyFunction("empty")
    public static boolean empty(Object val) {
        if (val == null || val.toString().length() == 0) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须为空");
        return false;
    }

    /**
     * 不为空
     */
    @VerifyFunction("notEmpty")
    public static boolean notEmpty(Object val) {
        if (val != null && val.toString().length() > 0) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("不能为空");
        return false;
    }

    /**
     * 不为空白
     */
    @VerifyFunction("notBlank")
    public static boolean notBlank(Object val) {
        if (val != null && StringUtils.isNotBlank(val.toString())) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("不能为空白");
        return false;
    }

    /**
     * 长度必须大于某个值
     */
    @VerifyFunction("lengthGreaterThan")
    public static boolean lengthGreaterThan(Object val, int length) {
        if (val != null && val.toString().length() > length) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("长度必须大于" + length);
        return false;
    }

    /**
     * 长度必须小于某个值。null也是允许的
     */
    @VerifyFunction("lengthLessThan")
    public static boolean lengthLessThan(Object val, int length) {
        if (val == null || val.toString().length() < length) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("长度必须小于" + length);
        return false;
    }

    /**
     * 长度必须等于某个值。null是不允许的
     */
    @VerifyFunction("lengthEquals")
    public static boolean lengthEquals(Object val, int length) {
        if (val != null && val.toString().length() == length) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("长度必须等于" + length);
        return false;
    }

    // int 相关
    // ----------------------------------------------------------------------

    /**
     * 整型
     */
    @VerifyFunction("int")
    public static boolean isInt(Object val) {
        if (val != null && StringCheckUtils.isInteger(val.toString())) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须填入整型数字");
        return false;
    }

    /**
     * 整型，且范围在[min, max]闭区间
     */
    @VerifyFunction("intClosedInterval")
    public static boolean intClosedInterval(Object val, int min, int max) {
        if (!isInt(val)) {
            return false;
        }

        int intVal = Integer.parseInt(val.toString());
        if (intVal >= min && intVal <= max) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须介于" + min + "~" + max + "之间（包含）");
        return false;
    }

    /**
     * 整型，且范围在(min, max)开区间
     */
    @VerifyFunction("intOpenInterval")
    public static boolean intOpenInterval(Object val, int min, int max) {
        if (!isInt(val)) {
            return false;
        }

        int intVal = Integer.parseInt(val.toString());
        if (intVal > min && intVal < max) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须介于" + min + "~" + max + "之间（不包含）");
        return false;
    }

    /**
     * 整型，且范围在(min, max)，根据入参来判断是否是开闭区间
     */
    @VerifyFunction("intInterval")
    public static boolean intInterval(Object val, int min, boolean minClose, int max, boolean maxClose) {
        if (!isInt(val)) {
            return false;
        }

        int intVal = Integer.parseInt(val.toString());
        boolean pass = checkInterval(intVal, min, minClose, max, maxClose);
        if (pass) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage(String.format("必须介于%d(%s) ~ %d(%s)之间",
                min, minClose ? "包含" : "不包含",
                max, maxClose ? "包含" : "不包含"));
        return false;
    }

    private static boolean checkInterval(int intVal, int min, boolean minClose, int max, boolean maxClose) {
        // 闭区间
        if (minClose && intVal < min) {
            return false;
        }

        // 开区间
        if (!minClose && intVal <= min) {
            return false;
        }

        // 闭区间
        if (maxClose && intVal > max) {
            return false;
        }

        // 开区间
        if (!maxClose && intVal >= max) {
            return false;
        }

        return true;
    }

    /**
     * 整型，且 > number
     */
    @VerifyFunction("intGreaterThan")
    public static boolean intGreaterThan(Object val, int number) {
        if (!isInt(val)) {
            return false;
        }

        int intVal = Integer.parseInt(val.toString());
        if (intVal > number) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须大于" + number);
        return false;
    }

    /**
     * 整型，且 >= number
     */
    @VerifyFunction("intGreaterThanOrEquals")
    public static boolean intGreaterThanOrEquals(Object val, int number) {
        if (!isInt(val)) {
            return false;
        }

        int intVal = Integer.parseInt(val.toString());
        if (intVal >= number) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须大于等于" + number);
        return false;
    }

    /**
     * 整型，且 < number
     */
    @VerifyFunction("intLessThan")
    public static boolean intLessThan(Object val, int number) {
        if (!isInt(val)) {
            return false;
        }

        int intVal = Integer.parseInt(val.toString());
        if (intVal < number) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须小于" + number);
        return false;
    }

    /**
     * 整型，且 <= number
     */
    @VerifyFunction("intLessThanOrEquals")
    public static boolean intLessThanOrEquals(Object val, int number) {
        if (!isInt(val)) {
            return false;
        }

        int intVal = Integer.parseInt(val.toString());
        if (intVal <= number) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须小于等于" + number);
        return false;
    }

    // long 相关
    // ----------------------------------------------------------------------

    /**
     * 长整型
     */
    @VerifyFunction("long")
    public static boolean isLong(Object val) {
        if (val != null && StringCheckUtils.isLong(val.toString())) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须填入长整型数字");
        return false;
    }

    /**
     * 长整型，且范围在[min, max]闭区间
     */
    @VerifyFunction("longClosedInterval")
    public static boolean longClosedInterval(Object val, long min, long max) {
        if (!isLong(val)) {
            return false;
        }

        long longVal = Long.parseLong(val.toString());
        if (longVal >= min && longVal <= max) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须介于" + min + "~" + max + "之间（包含）");
        return false;
    }

    /**
     * 长整型，且范围在(min, max)开区间
     */
    @VerifyFunction("longOpenInterval")
    public static boolean longOpenInterval(Object val, long min, long max) {
        if (!isLong(val)) {
            return false;
        }

        long longVal = Long.parseLong(val.toString());
        if (longVal > min && longVal < max) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须介于" + min + "~" + max + "之间（不包含）");
        return false;
    }

    /**
     * 长整型，且范围在(min, max)，根据入参来判断是否是开闭区间
     */
    @VerifyFunction("longInterval")
    public static boolean longInterval(Object val, long min, boolean minClose, long max, boolean maxClose) {
        if (!isLong(val)) {
            return false;
        }

        long longVal = Long.parseLong(val.toString());
        boolean pass = checkInterval(longVal, min, minClose, max, maxClose);
        if (pass) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage(String.format("必须介于%d(%s) ~ %d(%s)之间",
                min, minClose ? "包含" : "不包含",
                max, maxClose ? "包含" : "不包含"));
        return false;
    }

    private static boolean checkInterval(long longVal, long min, boolean minClose, long max, boolean maxClose) {
        // 闭区间
        if (minClose && longVal < min) {
            return false;
        }

        // 开区间
        if (!minClose && longVal <= min) {
            return false;
        }

        // 闭区间
        if (maxClose && longVal > max) {
            return false;
        }

        // 开区间
        if (!maxClose && longVal >= max) {
            return false;
        }

        return true;
    }

    /**
     * 长整型，且 > number
     */
    @VerifyFunction("longGreaterThan")
    public static boolean longGreaterThan(Object val, long number) {
        if (!isLong(val)) {
            return false;
        }

        long longVal = Long.parseLong(val.toString());
        if (longVal > number) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须大于" + number);
        return false;
    }

    /**
     * 长整型，且 >= number
     */
    @VerifyFunction("longGreaterThanOrEquals")
    public static boolean longGreaterThanOrEquals(Object val, long number) {
        if (!isLong(val)) {
            return false;
        }

        long longVal = Long.parseLong(val.toString());
        if (longVal >= number) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须大于等于" + number);
        return false;
    }

    /**
     * 长整型，且 < number
     */
    @VerifyFunction("longLessThan")
    public static boolean longLessThan(Object val, long number) {
        if (!isLong(val)) {
            return false;
        }

        long longVal = Long.parseLong(val.toString());
        if (longVal < number) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须小于" + number);
        return false;
    }

    /**
     * 长整型，且 <= number
     */
    @VerifyFunction("longLessThanOrEquals")
    public static boolean longLessThanOrEquals(Object val, long number) {
        if (!isLong(val)) {
            return false;
        }

        long longVal = Long.parseLong(val.toString());
        if (longVal <= number) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须小于等于" + number);
        return false;
    }

    // double 相关
    // ----------------------------------------------------------------------

    /**
     * 浮点型
     */
    @VerifyFunction("double")
    public static boolean isDouble(Object val) {
        if (val != null && StringCheckUtils.isDouble(val.toString())) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须输入整数或小数");
        return false;
    }

    /**
     * 浮点型，且含有固定小数点位数
     */
    @VerifyFunction("doubleWithScale")
    public static boolean doubleWithScale(Object val, int scale) {
        Preconditions.checkArgument(scale <= TaskConstants.MAX_EXCEL_DOUBLE_SCALE, "doubleWithScale scale 最大值不能超过{}，当前值{}",
                TaskConstants.MAX_EXCEL_DOUBLE_SCALE, scale);

        if (!isDouble(val)) {
            return false;
        }

        BigDecimal bigDecimal = new BigDecimal(val.toString());
        if (bigDecimal.scale() <= scale) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("小数位数不能超过" + scale + "位");
        return false;
    }

    /**
     * 浮点型，且 > number
     */
    @VerifyFunction("doubleGreaterThan")
    public static boolean doubleGreaterThan(Object val, double number) {
        if (!isDouble(val)) {
            return false;
        }

        BigDecimal valDecimal = new BigDecimal(val.toString());
        BigDecimal numberDecimal = BigDecimal.valueOf(number);

        if (valDecimal.compareTo(numberDecimal) > 0) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须大于" + doubleTrimZerosString(number));
        return false;
    }

    /**
     * 浮点型，且 >= number
     */
    @VerifyFunction("doubleGreaterThanOrEquals")
    public static boolean doubleGreaterThanOrEquals(Object val, double number) {
        if (!isDouble(val)) {
            return false;
        }

        BigDecimal valDecimal = new BigDecimal(val.toString());
        BigDecimal numberDecimal = BigDecimal.valueOf(number);

        if (valDecimal.compareTo(numberDecimal) >= 0) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须大于等于" + doubleTrimZerosString(number));
        return false;
    }

    /**
     * 浮点型，且 < number
     */
    @VerifyFunction("doubleLessThan")
    public static boolean doubleLessThan(Object val, double number) {
        if (!isDouble(val)) {
            return false;
        }

        BigDecimal valDecimal = new BigDecimal(val.toString());
        BigDecimal numberDecimal = BigDecimal.valueOf(number);

        if (valDecimal.compareTo(numberDecimal) < 0) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须小于" + doubleTrimZerosString(number));
        return false;
    }

    /**
     * 浮点型，且 <= number
     */
    @VerifyFunction("doubleLessThanOrEquals")
    public static boolean doubleLessThanOrEquals(Object val, double number) {
        if (!isDouble(val)) {
            return false;
        }

        BigDecimal valDecimal = new BigDecimal(val.toString());
        BigDecimal numberDecimal = BigDecimal.valueOf(number);

        if (valDecimal.compareTo(numberDecimal) <= 0) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage("必须小于等于" + doubleTrimZerosString(number));
        return false;
    }

    /**
     * 长整型，且范围在(min, max)，根据入参来判断是否是开闭区间
     */
    @VerifyFunction("doubleInterval")
    public static boolean doubleInterval(Object val, double min, boolean minClose, double max, boolean maxClose) {
        if (!isDouble(val)) {
            return false;
        }

        BigDecimal valDecimal = new BigDecimal(val.toString());
        BigDecimal minDecimal = BigDecimal.valueOf(min);
        BigDecimal maxDecimal = BigDecimal.valueOf(max);

        int minCompare = valDecimal.compareTo(minDecimal);
        int maxCompare = valDecimal.compareTo(maxDecimal);

        boolean pass = checkDoubleInterval(minCompare, maxCompare, minClose, maxClose);
        if (pass) {
            return true;
        }

        IllegalMessageListContext.appendIllegalMessage(String.format("必须介于%s(%s) ~ %s(%s)之间",
                doubleTrimZerosString(min), minClose ? "包含" : "不包含",
                doubleTrimZerosString(max), maxClose ? "包含" : "不包含"));
        return false;
    }

    private static boolean checkDoubleInterval(int minCompare, int maxCompare, boolean minClose, boolean maxClose) {
        // 闭区间
        if (minClose && minCompare < 0) {
            return false;
        }

        // 开区间
        if (!minClose && minCompare <= 0) {
            return false;
        }

        // 闭区间
        if (maxClose && maxCompare > 0) {
            return false;
        }

        // 开区间
        if (!maxClose && maxCompare >= 0) {
            return false;
        }

        return true;
    }

    private static String doubleTrimZerosString(double number) {
        return BigDecimal.valueOf(number).stripTrailingZeros().toPlainString();
    }

    // date 相关
    // ----------------------------------------------------------------------

    /**
     * 符号某种格式的日期
     */
    @VerifyFunction("dateFormat")
    public static boolean dateFormat(Object val, String format) {
        if (val == null || val.toString().length() == 0) {
            IllegalMessageListContext.appendIllegalMessage("不能为空");
            return false;
        }

        try {
            DateUtils.parseDate(val.toString(), format);
            return true;
        } catch (ParseException e) {
            IllegalMessageListContext.appendIllegalMessage("必须为[" + format + "]格式的日期");
            return false;
        }
    }
}
