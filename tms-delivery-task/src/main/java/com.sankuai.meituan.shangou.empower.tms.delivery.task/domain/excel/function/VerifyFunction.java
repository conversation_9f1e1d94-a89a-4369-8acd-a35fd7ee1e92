package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.function;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 函数在SpEL表达式中的名称
 * <AUTHOR>
 * @since 2021/10/01
 */
@Target({ ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface VerifyFunction {
    /**
     * 函数名
     */
    String value();
}
