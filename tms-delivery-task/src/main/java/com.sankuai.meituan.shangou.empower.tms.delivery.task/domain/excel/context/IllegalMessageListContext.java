package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.context;

import java.util.LinkedList;
import java.util.List;

import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.function.VerifyFunction;

/**
 * 违规信息上下文。
 * <p>在校验规则回调 {@link VerifyFunction} 时，会将异常信息填充到这个上下文中，后续逻辑会从中获取失败信息。</p>
 *
 * <AUTHOR>
 * @since 2021/10/01
 */
public class IllegalMessageListContext {
    private static final ThreadLocal<List<String>> contextHolder = new ThreadLocal<>();

    private IllegalMessageListContext() {
    }

    /**
     * 初始化一个空的上下文。在任务开始执行前被调用。
     */
    public static void init() {
        contextHolder.remove();
        contextHolder.set(new LinkedList<>());
    }

    /**
     * 获取违规的信息结果。没有时为空链表
     */
    public static List<String> getList() {
        List<String> list = contextHolder.get();
        if (list == null) {
            throw new IllegalStateException("错误的违规信息上下文获取，IllegalMessageListContext 尚未初始化");
        }
        return list;
    }

    /**
     * 追回违规信息
     */
    public static void appendIllegalMessage(String illegalMessage) {
        getList().add(illegalMessage);
    }

    /**
     * 清空上下文中的链表，注意不是从上下文中删除
     */
    public static void clear() {
        getList().clear();
    }

    /**
     * 删除上下文，在任务结束时被调用
     */
    public static void remove() {
        contextHolder.remove();
    }
}
