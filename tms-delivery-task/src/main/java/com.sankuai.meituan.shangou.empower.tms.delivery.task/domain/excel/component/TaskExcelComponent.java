package com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.function.Supplier;

import javax.annotation.Resource;

import com.dianping.cat.Cat;
import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.utils.MccUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.s3.S3ConfigProperties;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.AbstractCellStyleStrategy;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.AbstractHeadColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.AbstractRowHeightStyleStrategy;
import com.amazonaws.services.s3.AmazonS3Client;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/09/02
 */
@Slf4j
@Component
public class TaskExcelComponent {
    private static final int DEFAULT_COLUMN_WIDTH = 20;

    private static final SecureRandom random = new SecureRandom();

    @Autowired
    private S3ConfigProperties s3ConfigProperties;

    @Resource
    private AmazonS3Client amazonS3Client;

    /**
     * 同步下载Excel
     * <p> 在文件创建、读取、内部函数等的读取上都有可能出现异常，外部需要自行捕获异常并做动作
     *
     * @param uploadUseFileBuffer 生成EXCEL文件时，是否先生成文件再上传S3，否则使用内存拼装。
     * @param filePre             如果使用临时文件的，临时文件的前缀
     * @param sheets              工作表参数
     */
    public Result uploadExcel(boolean uploadUseFileBuffer, String filePre, Sheet... sheets) {

        FileCarrier fileCarrier = null;
        try {
            fileCarrier = new FileCarrier(filePre, uploadUseFileBuffer);
        }
        catch (IOException e) {
            throw new CommonRuntimeException("创建Temp文件失败", e);
        }

        try {
            int dataSize = writeExcel2FileCarrier(fileCarrier, sheets);
            String fileName = sendFile2S3(fileCarrier, filePre);
            return new Result(fileName, dataSize);
        }
        finally {
            if (fileCarrier.getFile() != null) {
                fileCarrier.getFile().deleteOnExit();
            }
            else if (fileCarrier.getOutputStream() != null) {
                try {
                    fileCarrier.getOutputStream().close();
                }
                catch (IOException e) {
                    // 任务到此已执行完成，如果此处出现异常，只打印，并吞掉异常。
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    private String sendFile2S3(FileCarrier fileCarrier, String filePre) {
        if (fileCarrier.getFile() != null) {
            amazonS3Client.putObject(s3ConfigProperties.getBucket(), fileCarrier.getFile().getName(), fileCarrier.getFile());
            return fileCarrier.getFile().getName();
        }

        if (fileCarrier.getOutputStream() != null) {
            String fileName = createFileName(filePre);

            ByteArrayInputStream inputStream = new ByteArrayInputStream(fileCarrier.getOutputStream().toByteArray());

            amazonS3Client.putObject(s3ConfigProperties.getBucket(), fileName, inputStream, null);
            return fileName;
        }

        throw new IllegalStateException("未实现S3上传的文件载体！");
    }

    private String createFileName(String tempFilePre) {
        long n = random.nextLong();
        if (n == Long.MIN_VALUE) {
            n = 0;      // corner case
        }
        else {
            n = Math.abs(n);
        }

        return tempFilePre + "-" + System.currentTimeMillis() + "-" + n + ".xlsx";
    }

    private int writeExcel2FileCarrier(FileCarrier fileCarrier, Sheet[] sheets) {
        ExcelWriter excelWriter = createExcelWriter(fileCarrier);

        int dataSize = 0;

        for (int i = 0; i < sheets.length; i++) {
            Sheet sheet = sheets[i];

            ExcelWriterSheetBuilder writerSheetBuilder = EasyExcel
                    .writerSheet(i + 1, sheet.getSheetName())
                    .registerWriteHandler(sheet.getColumnWidthStyleStrategy() == null
                            ? new SimpleColumnWidthStyleStrategy(sheet.getSimpleColumnWidth())
                            : sheet.getColumnWidthStyleStrategy())
                    .registerWriteHandler(sheet.getCellStyleStrategy() == null ? buildDefaultCellStyle() : sheet.getCellStyleStrategy())
                    .head(sheet.getHead());

            if (sheet.getRowHeightStyleStrategy() != null) {
                writerSheetBuilder.registerWriteHandler(sheet.getRowHeightStyleStrategy());
            }

            WriteSheet writeSheet = writerSheetBuilder.build();

            List<?> data;
            int maxCount = 0;
            int maxNum = MccUtils.maxWhileNum();
            do {
                data = sheet.dataSupplier.get();
                excelWriter.write(data, writeSheet);
                dataSize += data.size();
                if(maxCount++>maxNum){
                    Cat.logEvent("while.break.cat","TaskExcelComponent.writeExcel2FileCarrier");
                    break;
                }
            }
            while (sheet.isCallbackUntilDataEmpty() && CollectionUtils.isNotEmpty(data));
        }
        excelWriter.finish();

        return dataSize;
    }

    private ExcelWriter createExcelWriter(FileCarrier fileCarrier) {
        if (fileCarrier.getFile() != null) {
            return EasyExcel.write(fileCarrier.getFile()).build();
        }
        else {
            return EasyExcel.write(fileCarrier.getOutputStream()).build();
        }
    }

    private HorizontalCellStyleStrategy buildDefaultCellStyle() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();

        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());

        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();

        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);

        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setWrapped(true);

        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);

        contentWriteCellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        contentWriteCellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        contentWriteCellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        contentWriteCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());

        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    @Getter
    @Setter
    @Builder
    public static class Sheet {
        private String sheetName;
        private List<List<String>> head;

        /**
         * 【默认】简易的列宽，所有列宽度都一样
         */
        @Builder.Default
        private int simpleColumnWidth = DEFAULT_COLUMN_WIDTH;

        /**
         * 列宽自定义策略。
         * <p>如果自定义了该策略，将不再使用简易列宽</p>
         */
        private AbstractHeadColumnWidthStyleStrategy columnWidthStyleStrategy;

        /**
         * 高度样式策略。不设置则使用自适应
         */
        private AbstractRowHeightStyleStrategy rowHeightStyleStrategy;

        /**
         * 如果未设置单元格样式策略，将使用默认策略 buildDefaultCellStyle()
         */
        private AbstractCellStyleStrategy cellStyleStrategy;

        /**
         * 数据获取回调函数
         */
        private Supplier<List<List>> dataSupplier;

        /**
         * 是否连续回调获取数据函数，直至返回空列表为止。
         * <li>如果为true，会不断回调函数，直至返回空列表为止。
         * <li>如果为false【默认】，则只会回调一次。
         */
        @Builder.Default
        private boolean callbackUntilDataEmpty = false;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String fileName;
        private int dataSize;
    }

    /**
     * 当前使用的文件载体。
     * <p>file不为空，则使用文件；outputStream不为空，则使用内存</p>
     */
    @Getter
    private static class FileCarrier {
        private File file;
        private ByteArrayOutputStream outputStream;

        FileCarrier(String tempFilePre, boolean uploadUseFileBuffer) throws IOException {
            if (uploadUseFileBuffer) {
                String fileName = tempFilePre + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "-";
                this.file = File.createTempFile(fileName, ".xlsx");
            }
            else {
                outputStream = new ByteArrayOutputStream();
            }
        }
    }
}
