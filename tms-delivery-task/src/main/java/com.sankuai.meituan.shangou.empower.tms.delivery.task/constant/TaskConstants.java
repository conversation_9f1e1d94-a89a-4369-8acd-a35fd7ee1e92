package com.sankuai.meituan.shangou.empower.tms.delivery.task.constant;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/01
 */
public class TaskConstants {
    private TaskConstants() {
    }

    /**
     * 最小（包含）被允许的任务号，小于该值的任务将不被认为是配送域的异步任务
     */
    public static final int MIN_TASK_TYPE_CODE = 190000;

    /**
     * 最大（包含）被允许的任务号，大于该值的任务将不被认为是配送域的异步任务
     */
    public static final int MAX_TASK_TYPE_CODE = 190100;

    /**
     * EXCEL使用double时，最大支持的小数点位数
     */
    public static final int MAX_EXCEL_DOUBLE_SCALE = 10;

    /**
     * Lion配置Key：上传时允许的domain白名单
     */
    public static final String LION_UPLOAD_URL_ACCEPTED_DOMAIN_KEY = "task_upload_accepted_domain";

    /**
     * Lion配置默认值：上传时允许的domain白名单
     */
    public static final List<String> LION_UPLOAD_URL_ACCEPTED_DOMAIN_DEFAULT_VALUE = Arrays.asList("s3plus.sankuai.com",
            "s3plus.vip.sankuai.com", "msstest.sankuai.com", "msstest.vip.sankuai.com");
}
