package com.sankuai.meituan.shangou.empower.tms.delivery.task.exception;


import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import org.slf4j.helpers.MessageFormatter;

public class CommonRuntimeException extends RuntimeException {
    private FailureCodeEnum resultCode = null;

    public CommonRuntimeException(Exception e) {
        super(e);
        if (e instanceof CommonRuntimeException) {
            this.resultCode = ((CommonRuntimeException) e).getResultCode();
        }
    }

    public CommonRuntimeException(String msg) {
        super(msg);
    }

    public CommonRuntimeException(FailureCodeEnum resultCode, String pattern, Object... args) {
        super(formatMessage(pattern, args));
        this.resultCode = resultCode;
    }

    public CommonRuntimeException(String msg, Exception e) {
        super(msg, e);
        if (e instanceof CommonRuntimeException) {
            this.resultCode = ((CommonRuntimeException) e).getResultCode();
        }
    }

    public CommonRuntimeException(FailureCodeEnum resultCode, Exception e) {
        super(e);
        this.resultCode = resultCode;
    }

    public CommonRuntimeException(FailureCodeEnum resultCode) {
        super(resultCode.getMessage());
        this.resultCode = resultCode;
    }

    public FailureCodeEnum getResultCode() {
        return resultCode;
    }

    private static String formatMessage(String pattern, Object[] args) {
        return MessageFormatter.arrayFormat(pattern, args).getMessage();
    }

}
