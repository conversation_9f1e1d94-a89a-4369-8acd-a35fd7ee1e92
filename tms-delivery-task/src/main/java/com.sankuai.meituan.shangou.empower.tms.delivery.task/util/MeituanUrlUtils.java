package com.sankuai.meituan.shangou.empower.tms.delivery.task.util;

import java.util.Map;

import org.apache.commons.collections4.map.CaseInsensitiveMap;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2021/09/30
 */
public class MeituanUrlUtils {
    private static final String DOMAIN_SUFFIX = ".com";

    private static final Map<String, String> protocolDomainMap = new CaseInsensitiveMap<>();
    static {
        protocolDomainMap.put("http://s3plus.sankuai.com", "http://s3plus.vip.sankuai.com");
        protocolDomainMap.put("https://s3plus.sankuai.com", "http://s3plus.vip.sankuai.com");

        protocolDomainMap.put("http://msstest.sankuai.com", "http://msstest.vip.sankuai.com");
        protocolDomainMap.put("https://msstest.sankuai.com", "http://msstest.vip.sankuai.com");
    }

    private MeituanUrlUtils() {
    }

    /**
     * 将美团云外网地址转换为内网地址
     */
    public static String translate2IntranetUrl(String url) {
        String protocolDomain = truncateUrl2ProtocolDomain(url);
        if (StringUtils.isBlank(protocolDomain)) {
            return url;
        }

        String newProtocolDomain = protocolDomainMap.get(protocolDomain);
        if (newProtocolDomain == null) {
            return url;
        }

        return newProtocolDomain + url.substring(protocolDomain.length());
    }

    private static String truncateUrl2ProtocolDomain(String fileUrl) {
        if (StringUtils.isBlank(fileUrl)) {
            return null;
        }

        int index = fileUrl.indexOf(DOMAIN_SUFFIX);
        if (index <= 0) {
            return null;
        }

        return fileUrl.substring(0, index + DOMAIN_SUFFIX.length());
    }
}
