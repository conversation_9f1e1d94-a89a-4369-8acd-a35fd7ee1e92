package com.sankuai.meituan.shangou.empower.tms.delivery.task.util;

/**
 * <AUTHOR>
 * @since 2021/10/02
 */
public class StringCheckUtils {
    private static final String MAX_INT_STRING = Integer.toString(Integer.MAX_VALUE);
    private static final String MIN_INT_STRING = Integer.toString(Integer.MIN_VALUE);
    private static final String MAX_LONG_STRING = Long.toString(Long.MAX_VALUE);
    private static final String MIN_LONG_STRING = Long.toString(Long.MIN_VALUE);

    private StringCheckUtils() {
    }

    /**
     * 判定是否是有效的十进制int字符串，该函数不会抛出异常。
     * <p>范围在 [{@link Integer.MIN_VALUE}, {@link Integer.MAX_VALUE}] 间的整型字符串</p>
     */
    public static boolean isInteger(String s) {
        if (s == null || s.length() == 0) {
            return false;
        }

        char firstChar = s.charAt(0);
        if (firstChar != '-') {
            // 开头不是"-"号的，一律按非负来处理
            return isPositiveOrZeroInteger(s);
        }

        return checkIsNumberString(s, 1, MIN_INT_STRING, 1);
    }

    /**
     * 判定是否是 [0 ~ Integer.MAX_VALUE] 间的整型字符串。
     * <p>注意，-0认为是负数，会返回false</p>
     */
    public static boolean isPositiveOrZeroInteger(String s) {
        if (s == null || s.length() == 0) {
            return false;
        }

        int signLength = 0;
        char firstChar = s.charAt(0);
        if (firstChar < '0') {
            if (firstChar != '+') {
                return false;
            }
            signLength = 1;
        }

        return checkIsNumberString(s, signLength, MAX_INT_STRING, 0);
    }

    /**
     * 判定是否是有效的十进制long字符串，该函数不会抛出异常。
     * <p>范围在 [{@link Long.MIN_VALUE}, {@link Long.MAX_VALUE}] 间的长整型字符串</p>
     */
    public static boolean isLong(String s) {
        if (s == null || s.length() == 0) {
            return false;
        }

        char firstChar = s.charAt(0);
        if (firstChar != '-') {
            // 开头不是"-"号的，一律按非负来处理
            return isPositiveOrZeroLong(s);
        }

        return checkIsNumberString(s, 1, MIN_LONG_STRING, 1);
    }

    /**
     * 判定是否是 [0 ~ Integer.MAX_VALUE] 间的长整型字符串。
     * <p>注意，-0认为是负数，会返回false</p>
     */
    public static boolean isPositiveOrZeroLong(String s) {
        if (s == null || s.length() == 0) {
            return false;
        }

        int signLength = 0;
        char firstChar = s.charAt(0);
        if (firstChar < '0') {
            if (firstChar != '+') {
                return false;
            }
            signLength = 1;
        }

        return checkIsNumberString(s, signLength, MAX_LONG_STRING, 0);
    }

    /**
     * 是否是浮点数。
     * <p>".1"、"1." 在java中是可以允许的double格式，这里也返回true</p>
     */
    public static boolean isDouble(String str) {
        if (str == null || str.length() == 0) {
            return false;
        }

        int n = 0;
        char firstChar = str.charAt(0);
        if (firstChar == '-' || firstChar == '+') {
            if (str.length() == 1) {
                // 不能只有符号位
                return false;
            }
            n++;
        }

        boolean hasPoint = false;
        for (; n < str.length(); n++) {
            char c = str.charAt(n);
            if (c == '.') {
                if (hasPoint) {
                    // "." 只能出现一次
                    return false;
                }
                hasPoint = true;
                continue;
            }

            if (c < '0' || c > '9') {
                return false;
            }
        }

        return true;
    }

    private static boolean checkIsNumberString(String s, int signLength, String confineString, int confineSignLength) {
        int stringNumberLength = s.length() - signLength;
        int confineNumberLength = confineString.length() - confineSignLength;

        // 非符号位为空
        if (stringNumberLength <= 0) {
            return false;
        }

        // 溢出
        if (stringNumberLength > confineNumberLength) {
            return false;
        }

        // 判断非符号位上是否全是数字
        for (int n = signLength; n < s.length(); n++) {
            char c = s.charAt(n);
            if (c < '0' || c > '9') {
                return false;
            }
        }

        // 长度比最大值位数少的，一定不溢出
        if (stringNumberLength < confineNumberLength) {
            return true;
        }

        // 与最大值位数相同的，逐位检查是否溢出
        for (int n = 0; n < stringNumberLength; n++) {
            int cc = confineString.charAt(n + confineSignLength);
            int sc = s.charAt(n + signLength);
            if (sc > cc) {
                return false;
            }
            if (sc != cc) {
                break;
            }
        }

        return true;
    }
}
