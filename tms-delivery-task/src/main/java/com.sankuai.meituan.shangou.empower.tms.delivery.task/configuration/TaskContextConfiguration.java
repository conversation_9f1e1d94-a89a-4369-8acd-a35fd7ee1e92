package com.sankuai.meituan.shangou.empower.tms.delivery.task.configuration;

import com.google.common.collect.Sets;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.common.YmlPropertySourceFactory;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.AbstractExcelTask;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.domain.excel.ExcelTaskConfig;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021/09/29
 */
@PropertySource(value = "classpath:/task/task-application.yml", factory = YmlPropertySourceFactory.class)
@ImportResource("classpath:/task/task-applicationContext.xml")
@Configuration
public class TaskContextConfiguration implements ApplicationContextAware, InitializingBean {
    @Value("${task.config}")
    private String taskConfig;

    private ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        this.context = context;
    }

    @Bean
    public ExcelTaskConfig excelAsyncTaskConfig(ResourceLoader resourceLoader) throws IOException {
        Resource resource = resourceLoader.getResource(taskConfig);

        String configContent;
        try (InputStream inputStream = resource.getInputStream()) {
            configContent = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        }
        return new ExcelTaskConfig(configContent);
    }


    @Override
    public void afterPropertiesSet() {
        checkTaskTypeUnique();
    }

    private void checkTaskTypeUnique() {
        Map<String, AbstractExcelTask> taskMap = context.getBeansOfType(AbstractExcelTask.class);
        Set<Integer> taskTypes = Sets.newHashSetWithExpectedSize(taskMap.size());
        for (AbstractExcelTask item : taskMap.values()) {
            if (taskTypes.contains(item.getTaskType())) {
                throw new IllegalStateException("包含重复的异步任务类型[" + item.getTaskType() + "]");
            }
            taskTypes.add(item.getTaskType());
        }
    }
}
