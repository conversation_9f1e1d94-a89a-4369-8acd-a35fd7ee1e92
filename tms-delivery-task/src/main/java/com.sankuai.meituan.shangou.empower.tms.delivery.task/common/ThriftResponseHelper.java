package com.sankuai.meituan.shangou.empower.tms.delivery.task.common;

import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/09/05
 */
@Slf4j
public class ThriftResponseHelper {
    private ThriftResponseHelper() {
    }

    /**
     * 拼装错误状态并打印日志
     *
     * @param logPrefix     日志打印前缀
     * @param requestString 请求信息。转成字符串后传入（推荐使用JSON）
     * @param exception     异常信息
     */
    public static Status buildFailedStatusAndPrintLog(String logPrefix, String requestString, Exception exception) {
        // 错误的参数信息向上返回
        if (exception instanceof IllegalArgumentException) {
            log.info("{} illegal argument error. request={}", logPrefix, requestString, exception);
            return new Status(FailureCodeEnum.INVALID_PARAM.getCode(), exception.getMessage());
        }

        // 错误的状态信息向上返回
        if (exception instanceof IllegalStateException) {
            log.warn("{} illegal state error. request={}", logPrefix, requestString, exception);
            return new Status(FailureCodeEnum.SYSTEM_ERROR.getCode(), exception.getMessage());
        }
        if (exception instanceof CommonRuntimeException) {
            FailureCodeEnum resultCode = ((CommonRuntimeException) exception).getResultCode();
            if (Objects.nonNull(resultCode)) {
                log.warn("{} illegal state error. request={}", logPrefix, requestString, exception);
                if (StringUtils.isNotBlank(exception.getMessage())) {
                    return new Status(resultCode.getCode(), resultCode.getMessage() + ":" + exception.getMessage());
                }
                return new Status(resultCode.getCode(), resultCode.getMessage());
            }
        }

        // 其他未知的异常
        log.error("{} failed. request={}", logPrefix, requestString, exception);
        return new Status(FailureCodeEnum.SYSTEM_ERROR.getCode(), FailureCodeEnum.SYSTEM_ERROR.getMessage());
    }
}