package com.sankuai.meituan.shangou.empower.tms.delivery.task.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.env.YamlPropertySourceLoader;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.support.DefaultPropertySourceFactory;
import org.springframework.core.io.support.EncodedResource;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/09/29
 */
@Slf4j
public class YmlPropertySourceFactory extends DefaultPropertySourceFactory {
    private static final String SOURCE_PROFILE_KEY = "spring.profiles";

    private static String activeProfile;
    static {
        activeProfile = System.getProperty("spring.profiles.active");
        if (activeProfile == null) {
            activeProfile = "dev";
        }
    }

    @Override
    public PropertySource<?> createPropertySource(String name, EncodedResource resource) throws IOException {
        if (resource == null) {
            return super.createPropertySource(name, resource);
        }
        List<PropertySource<?>> sources = new YamlPropertySourceLoader().load(resource.getResource().getFilename(), resource.getResource());
        if (sources.isEmpty()) {
            return super.createPropertySource(name, resource);
        }

        if (sources.size() == 1) {
            return sources.get(0);
        }

        PropertySource<?> defaultSource = null;
        PropertySource<?> profileSource = null;
        for (PropertySource<?> propertySource : sources) {
            Object profile = propertySource.getProperty(SOURCE_PROFILE_KEY);
            if (profile == null && defaultSource == null) {
                defaultSource = propertySource;
            }
            else if (activeProfile.equals(profile)) {
                if (profileSource != null) {
                    throw new IllegalStateException("配置文件[" + resource + "]有重复的profile.active[" + profile + "]！");
                }
                profileSource = propertySource;
            }
        }

        return new YmlPropertySource(sources.get(0).getName(), defaultSource, profileSource);
    }

    public static class YmlPropertySource extends PropertySource<Object> {
        private PropertySource<?> defaultSource;
        private PropertySource<?> profileSource;

        public YmlPropertySource(String name, PropertySource<?> defaultSource, PropertySource<?> profileSource) {
            super(name);
            this.defaultSource = defaultSource;
            this.profileSource = profileSource;
        }

        @Override
        public Object getProperty(String name) {
            Object value = Optional.ofNullable(profileSource).map(source -> source.getProperty(name)).orElse(null);
            if (value != null) {
                return value;
            }

            value = Optional.ofNullable(defaultSource).map(source -> source.getProperty(name)).orElse(null);
            return value;
        }
    }
}
