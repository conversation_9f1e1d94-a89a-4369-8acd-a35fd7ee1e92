package com.sankuai.meituan.shangou.empower.tms.delivery.task.s3;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.MalformedURLException;
import java.net.URL;

@Configuration
public class S3Configuration {
    @Bean("amazonS3Client")
    public AmazonS3Client amazonS3Client(S3ConfigProperties properties) throws MalformedURLException {
        AWSCredentials credentials = new BasicAWSCredentials(properties.getAccessKey(), properties.getSecretKey());
        ClientConfiguration clientConfig = new ClientConfiguration();

        URL ep = new URL(properties.getUrl());
        if (ep.getProtocol().equalsIgnoreCase("http")) {
            clientConfig.setProtocol(Protocol.HTTP);
        } else if (ep.getProtocol().equalsIgnoreCase("https")) {
            clientConfig.setProtocol(Protocol.HTTPS);
        } else {
            throw new IllegalStateException("Unsupported protocol: " + properties.getUrl());
        }
        String endpoint = ep.getHost();
        if (ep.getPort() > 0) {
            endpoint += ":" + ep.getPort();
        }

        S3ClientOptions s3ClientOptions = new S3ClientOptions();
        s3ClientOptions.setPathStyleAccess(true);
        AmazonS3Client s3conn = new AmazonS3Client(credentials, clientConfig);
        s3conn.setS3ClientOptions(s3ClientOptions);
        s3conn.setEndpoint(endpoint);
        return s3conn;
    }

}
