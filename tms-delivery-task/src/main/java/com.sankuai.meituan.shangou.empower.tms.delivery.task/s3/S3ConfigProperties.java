package com.sankuai.meituan.shangou.empower.tms.delivery.task.s3;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2021/10/07
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "s3")
public class S3ConfigProperties {
    private String accessKey;
    private String secretKey;
    private String url;
    private String bucket;
    private long tempUrlExpiredSeconds;
}
