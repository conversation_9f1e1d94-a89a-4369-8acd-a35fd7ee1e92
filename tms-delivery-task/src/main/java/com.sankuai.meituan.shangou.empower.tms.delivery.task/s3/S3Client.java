package com.sankuai.meituan.shangou.empower.tms.delivery.task.s3;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Date;

@Component
public class S3Client {
    private final AmazonS3Client amazonS3Client;
    private final S3ConfigProperties s3ConfigProperties;


    @Autowired
    public S3Client(AmazonS3Client amazonS3Client,
                    S3ConfigProperties s3ConfigProperties) {
        this.amazonS3Client = amazonS3Client;
        this.s3ConfigProperties = s3ConfigProperties;
    }

    public String generatePublicUrl(String objKey, int expireTime) {
        GeneratePresignedUrlRequest generatePresignedUrlRequest =
                new GeneratePresignedUrlRequest(s3ConfigProperties.getBucket(), objKey);
        generatePresignedUrlRequest.setMethod(HttpMethod.GET);
        generatePresignedUrlRequest.setExpiration(DateUtils.addSeconds(new Date(), expireTime));
        URL url = amazonS3Client.generatePresignedUrl(generatePresignedUrlRequest);
        if (url.getHost().contains(".vip")) {
            try {
                url = new URL(url.getProtocol(), url.getHost().replace(".vip", ""), url.getPort(), url.getFile());
            } catch (MalformedURLException e) {
                throw new CommonRuntimeException(FailureCodeEnum.S3_GENERATE_URL_FAILED, e);
            }
        }

        return url.toString();
    }
}
