<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>reco_fulfillment_tms</artifactId>
        <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>tms-delivery-poi</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>mcc-xframe-boot-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-delivery-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>dms-delivery-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou</groupId>
            <artifactId>store-saas-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.reco.store</groupId>
                    <artifactId>store-saas-infrastructure-shield-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.freemarker</groupId>
                    <artifactId>freemarker</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_platform_common</artifactId>
        </dependency>
    </dependencies>

</project>
