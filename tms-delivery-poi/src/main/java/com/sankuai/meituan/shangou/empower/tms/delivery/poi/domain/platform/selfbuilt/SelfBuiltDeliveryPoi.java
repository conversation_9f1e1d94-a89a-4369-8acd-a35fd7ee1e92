package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt;

import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryIsShowItemNumberEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStrategyEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM;


/**
 * 自建配送门店模型
 * 主体为赋能门店，加上配送相关配置信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/24
 */
@Slf4j
@Getter
@ToString(callSuper = true)
public class SelfBuiltDeliveryPoi extends DeliveryPoi {
	private static final int DEFAULT_SHIFT_CHANNEL_MINUTES = 15;

	/**
	 * 渠道租户配置映射
	 */
	private Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> channelTenantConfigMap = new HashMap<>();

	/**
	 * 渠道门店配置映射
	 */
	private Map<ThirdDeliveryChannelEnum, ChannelStoreConfig> channelStoreConfigMap = new HashMap<>();

	/**
	 * 各线上渠道的配送配置信息
	 */
	private Map<Integer, OrderPlatformDeliveryConfig> orderPlatformDeliveryConfigMap = new HashMap<>();

	/**
	 * 门店地址
	 */
	@Setter
	private Address storeAddress;

	/**
	 * 配送策略
	 */
	private final DeliveryStrategyEnum deliveryStrategy;

	/**
	 * 配送策略对应的配置信息
	 */
	private DeliveryStrategyConfig deliveryStrategyConfig;

	public SelfBuiltDeliveryPoi(Long tenantId,
	                            Long storeId,
	                            Integer cityCode,
	                            String contactPhone,
	                            DeliveryLaunchPoint deliveryLaunchPoint,
	                            Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> channelTenantConfigMap,
	                            Map<ThirdDeliveryChannelEnum, ChannelStoreConfig> channelStoreConfigMap,
								Integer channelType) {
		super(tenantId, storeId, cityCode, contactPhone, SELF_BUILT_DELIVERY_PLATFORM, deliveryLaunchPoint, AUTO_LAUNCH_DELIVERY,channelType);
		this.deliveryStrategy = DeliveryStrategyEnum.SEQUENTIAL_POLLING;
		this.deliveryStrategyConfig = new SequentialPollingStrategyConfig(new ArrayList<>(channelStoreConfigMap.keySet()), DEFAULT_SHIFT_CHANNEL_MINUTES);
		this.channelTenantConfigMap = Optional.ofNullable(channelTenantConfigMap).orElse(new HashMap<>());
		this.channelStoreConfigMap = Optional.ofNullable(channelStoreConfigMap).orElse(new HashMap<>());
	}

	public SelfBuiltDeliveryPoi(DeliveryPoi sourceDeliveryPoi) {
		super(
				sourceDeliveryPoi.getTenantId(),
				sourceDeliveryPoi.getStoreId(),
				sourceDeliveryPoi.getCityCode(),
				sourceDeliveryPoi.getContactPhone(),
				SELF_BUILT_DELIVERY_PLATFORM,
				sourceDeliveryPoi.getDeliveryLaunchPoint(),
				AUTO_LAUNCH_DELIVERY,
				sourceDeliveryPoi.getChannelType()
		);
		this.deliveryStrategy = DeliveryStrategyEnum.SEQUENTIAL_POLLING;
		this.deliveryStrategyConfig = new SequentialPollingStrategyConfig(new ArrayList<>(), DEFAULT_SHIFT_CHANNEL_MINUTES);
	}

	public SelfBuiltDeliveryPoi(Long id,
	                            Long tenantId,
	                            Long storeId,
	                            Integer cityCode,
	                            String contactPhone,
	                            DeliveryLaunchPoint deliveryLaunchPoint,
	                            Address storeAddress,
	                            DeliveryStrategyEnum deliveryStrategy,
	                            DeliveryStrategyConfig deliveryStrategyConfig,
	                            Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> channelTenantConfigMap,
	                            Map<ThirdDeliveryChannelEnum, ChannelStoreConfig> channelStoreConfigMap,
	                            Map<Integer, OrderPlatformDeliveryConfig> orderPlatformDeliveryConfigMap,
								Integer channelType,
								DeliveryPlatformEnum lastDeliveryPlatform) {
		super(id, tenantId, storeId, cityCode, contactPhone, SELF_BUILT_DELIVERY_PLATFORM, deliveryLaunchPoint, AUTO_LAUNCH_DELIVERY,channelType,lastDeliveryPlatform);
		this.storeAddress = storeAddress;
		this.deliveryStrategy = deliveryStrategy;
		this.deliveryStrategyConfig = deliveryStrategyConfig;
		this.channelTenantConfigMap = Optional.ofNullable(channelTenantConfigMap).orElse(new HashMap<>());
		this.channelStoreConfigMap = Optional.ofNullable(channelStoreConfigMap).orElse(new HashMap<>());
		this.orderPlatformDeliveryConfigMap = orderPlatformDeliveryConfigMap;
	}

	public SelfBuiltDeliveryPoi(Long id,
								Long tenantId,
								Long storeId,
								Integer cityCode,
								String contactPhone,
								DeliveryLaunchPoint deliveryLaunchPoint,
								Address storeAddress,
								DeliveryStrategyEnum deliveryStrategy,
								DeliveryStrategyConfig deliveryStrategyConfig,
								Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> channelTenantConfigMap,
								Map<ThirdDeliveryChannelEnum, ChannelStoreConfig> channelStoreConfigMap,
								Map<Integer, OrderPlatformDeliveryConfig> orderPlatformDeliveryConfigMap,
								Integer channelType,
								DeliveryPlatformEnum lastDeliveryPlatform,
								DeliveryIsShowItemNumberEnum deliveryIsShowItemNumberEnum) {
		super(id, tenantId, storeId, cityCode, contactPhone, SELF_BUILT_DELIVERY_PLATFORM, deliveryLaunchPoint, AUTO_LAUNCH_DELIVERY,channelType,lastDeliveryPlatform,deliveryIsShowItemNumberEnum);
		this.storeAddress = storeAddress;
		this.deliveryStrategy = deliveryStrategy;
		this.deliveryStrategyConfig = deliveryStrategyConfig;
		this.channelTenantConfigMap = Optional.ofNullable(channelTenantConfigMap).orElse(new HashMap<>());
		this.channelStoreConfigMap = Optional.ofNullable(channelStoreConfigMap).orElse(new HashMap<>());
		this.orderPlatformDeliveryConfigMap = orderPlatformDeliveryConfigMap;
	}
	/**
	 * 配送门店自我纠正
	 * 针对顺序轮询策略：检查策略配置项中是否有遗漏的配送渠道，如果有，添加到配送顺序的末尾
	 */
	public boolean selfCorrect() {
		AtomicBoolean modified = new AtomicBoolean(false);
		if (this.deliveryStrategy == DeliveryStrategyEnum.SEQUENTIAL_POLLING) {
			SequentialPollingStrategyConfig currentConfig = (SequentialPollingStrategyConfig) this.deliveryStrategyConfig;

			List<ThirdDeliveryChannelEnum> missingChannels = this.channelStoreConfigMap
					.entrySet()
					.stream()
					.filter(it -> it.getValue().isEnabled())
					.map(Map.Entry::getKey)
					.filter(it -> !currentConfig.getOrderedDeliveryChannels().contains(it))
					.collect(Collectors.toList());

			if (CollectionUtils.isNotEmpty(missingChannels)) {
				//补偿的渠道排在最后
				List<ThirdDeliveryChannelEnum> newOrderedChannelList = new ArrayList<>(currentConfig.getOrderedDeliveryChannels());
				newOrderedChannelList.addAll(missingChannels);
				this.deliveryStrategyConfig = new SequentialPollingStrategyConfig(newOrderedChannelList, currentConfig.getTimeoutForShiftDeliveryChannelInMinutes());
				modified.set(true);
			}
		}

		return modified.get();
	}

	public boolean canLaunchDelivery(){
		//没有租户配置，不能发起
		if(MapUtils.isEmpty(this.getChannelTenantConfigMap())){
			return false;
		}

		//没有门店配置，不能发起
		if(MapUtils.isEmpty(this.getChannelStoreConfigMap())){
			return false;
		}

		//判断是否有交集，没有则不能发起
		Set<ThirdDeliveryChannelEnum> intersectionSet = new HashSet<>(this.getChannelStoreConfigMap().keySet());
		intersectionSet.retainAll(this.getChannelTenantConfigMap().keySet());
		return CollectionUtils.isNotEmpty(intersectionSet);
	}
}
