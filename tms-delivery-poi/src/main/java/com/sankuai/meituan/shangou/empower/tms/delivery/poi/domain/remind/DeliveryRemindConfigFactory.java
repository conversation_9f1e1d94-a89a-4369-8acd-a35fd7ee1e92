package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.stereotype.Component;

/**
 * 配送提醒配置工厂.
 *
 * <AUTHOR>
 * @since 2021/10/8 17:55
 */
@Component
public class DeliveryRemindConfigFactory {

    /**
     * 创建新的门店提醒配置
     *
     * @param tenantId
     * @param storeId
     * @param recipients
     * @return
     */
    public DeliveryRemindConfig createDeliveryRemindConfig(long tenantId, long storeId, List<String> recipients, List<String> dhRecipients) {
        return new DeliveryRemindConfig(
                null,
                tenantId,
                storeId,
                recipients,
                true,
                LocalDateTime.now(),
                dhRecipients
        );
    }

}
