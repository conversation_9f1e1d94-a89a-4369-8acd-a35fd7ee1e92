package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 配送提醒配置 仓库.
 *
 * <AUTHOR>
 * @since 2021/10/8 17:12
 */
public interface DeliveryRemindConfigRepository {

    /**
     * 查询门店的配送提醒配置.
     *
     * @param tenantId 租户 ID
     * @param storeId 门店 ID
     * @return 配送提醒配置
     */
    Optional<DeliveryRemindConfig> queryDeliveryRemindConfig(long tenantId, long storeId);

    /**
     * 保存门店的配送提醒配置.
     *
     * @param remindConfig 配送提醒配置
     */
    void saveDeliveryRemindConfig(DeliveryRemindConfig remindConfig);
}
