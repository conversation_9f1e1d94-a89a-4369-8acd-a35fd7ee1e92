package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 跳转导航模式枚举
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@Getter
@AllArgsConstructor
public enum InternalNavigationModeEnum {

    /**
     * 高德导航
     */
    GAODE(1, "高德"),

    /**
     * 美团导航
     */
    MEITUAN(2, "美团");

    /**
     * 导航模式值
     */
    private final Integer mode;

    /**
     * 导航模式描述
     */
    private final String description;

    /**
     * 根据模式值获取枚举
     *
     * @param mode 模式值
     * @return 对应的枚举
     * @throws IllegalArgumentException 当模式值不存在时抛出异常
     */
    public static InternalNavigationModeEnum getByMode(Integer mode) {
        if (mode == null) {
            return null;
        }
        for (InternalNavigationModeEnum modeEnum : values()) {
            if (modeEnum.getMode().equals(mode)) {
                return modeEnum;
            }
        }
        throw new IllegalArgumentException("未知的跳转导航模式: " + mode);
    }
}

