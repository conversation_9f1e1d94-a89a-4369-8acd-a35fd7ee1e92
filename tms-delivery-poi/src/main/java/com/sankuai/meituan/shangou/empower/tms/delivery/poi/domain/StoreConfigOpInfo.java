package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import lombok.Builder;
import lombok.Data;

/**
 * @Description: 门店配置修改信息，对应store_config_op_log表中的op_info列
 * @Author: zhangjian155
 * @Date: 2022/10/14 16:47
 */
@Data
@Builder
public class StoreConfigOpInfo {

    /**
     * 被修改的渠道类型
    */
    private Integer channelType;

    /**
     * 修改后的配送发单节点
     */
    private Integer deliveryPoint;


    /**
     * 修改后的配送平台
     */
    private Integer deliveryPlatform;

    /**
     * 修改后的立即单自动呼叫骑手时间
     */
    private Integer deliveryLaunchDelayMinutes;

    /**
     * 修改后的预约单自动呼叫骑手时间
     */
    private Integer bookingDeliveryLaunchDelayMinutes;


}
