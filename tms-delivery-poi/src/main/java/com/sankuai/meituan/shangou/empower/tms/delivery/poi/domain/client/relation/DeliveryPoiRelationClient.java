package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.relation;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/9
 */
public interface DeliveryPoiRelationClient {

	/**
	 * 同步门店配置渠道
	 */
	void syncStoreChannelInfo(DeliveryChannelInfo channelInfo);

	/**
	 * 解绑配送商门店.
	 *
	 * @param storeId              中台门店ID
	 * @param deliveryChannelId  配送商编码
	 * @param deliveryChannelPoiId 配送商门店ID
	 */
	void unbindDeliveryCompanyPoi(Long storeId, Integer deliveryChannelId, String deliveryChannelPoiId);

	/**
	 * 查询门店三方配送信息
	 */
	List<DeliveryChannelInfo> queryStoreChannelInfo(Long storeId);
}
