package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.servicepackage;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 蜂鸟：一个商家只能签约一个服务包
 * https://open.ele.me/documents/%E5%B9%B3%E5%8F%B0%E7%AE%80%E4%BB%8B
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/13
 */
@Slf4j
@Getter
@SuppressWarnings("SpellCheckingInspection")
public class FengNiaoServicePackage extends ServicePackage {

	/**
	 * 默认蜂鸟服务包
	 */
	private static final List<FengNiaoServicePackage> DEFAULT_FENG_NIAO_SERVICE_PACKAGES = Arrays.asList(
			new FengNiaoServicePackage("1", "蜂鸟配送", 1, false),
			new FengNiaoServicePackage("2", "蜂鸟优送", 2, false),
			new FengNiaoServicePackage("3", "蜂鸟快送", 3, false),
			new FengNiaoServicePackage("911", "联调专用", 4, true)
	);

	@JsonCreator
	public FengNiaoServicePackage(@JsonProperty("code") String code,
	                              @JsonProperty("name") String name,
	                              @JsonProperty("order") int order,
	                              @JsonProperty("testOnly") boolean testOnly) {
		super(code, name, order, testOnly);
	}

	public static List<FengNiaoServicePackage> getAllServicePackages() {
		try {
			return Optional.ofNullable(ConfigUtilAdapter.getString("delivery_service_packages_FENG_NIAO"))
					.map(json -> JsonUtil.fromJson(json, new TypeReference<List<FengNiaoServicePackage>>() {
					}))
					.orElse(new ArrayList<>(DEFAULT_FENG_NIAO_SERVICE_PACKAGES));
		} catch (Exception e) {
			log.error("解析蜂鸟服务包mcc配置失败，将使用默认服务包");
			return new ArrayList<>(DEFAULT_FENG_NIAO_SERVICE_PACKAGES);
		}
	}
}
