package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * 操作人信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/10
 */
@Getter
@ToString
public class Operator {

	//操作员ID
	private final Long operatorId;

	//操作员姓名
	private final String operatorName;

	public Operator(Long operatorId, String operatorName) {
		Preconditions.checkNotNull(operatorId, "operatorId is null");
		Preconditions.checkArgument(StringUtils.isNotEmpty(operatorName), "operatorName is empty");

		this.operatorId = operatorId;
		this.operatorName = operatorName;
	}
}
