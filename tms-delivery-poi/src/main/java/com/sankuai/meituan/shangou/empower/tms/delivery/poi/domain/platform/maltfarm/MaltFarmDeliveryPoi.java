package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.maltfarm;

import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryIsShowItemNumberEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM;

/**
 * 麦芽田配送门店模型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/24
 */
@Getter
@ToString(callSuper = true)
public class MaltFarmDeliveryPoi extends DeliveryPoi {

	@Setter
	private Address storeAddress;

	public MaltFarmDeliveryPoi(Long tenantId,
							   Long storeId,
							   Integer cityCode,
							   String contactPhone,
							   DeliveryLaunchPoint deliveryLaunchPoint,Integer channelType) {
		super(tenantId, storeId, cityCode, contactPhone, MALT_FARM_DELIVERY_PLATFORM, deliveryLaunchPoint, AUTO_LAUNCH_DELIVERY,channelType);
	}

	public MaltFarmDeliveryPoi(DeliveryPoi sourceDeliveryPoi) {
		super(
				sourceDeliveryPoi.getTenantId(),
				sourceDeliveryPoi.getStoreId(),
				sourceDeliveryPoi.getCityCode(),
				sourceDeliveryPoi.getContactPhone(),
				MALT_FARM_DELIVERY_PLATFORM,
				sourceDeliveryPoi.getDeliveryLaunchPoint(),
				AUTO_LAUNCH_DELIVERY,
				sourceDeliveryPoi.getChannelType()
		);
	}

	public MaltFarmDeliveryPoi(Long id,
							   Long tenantId,
							   Long storeId,
							   Integer cityCode,
							   String contactPhone,
							   DeliveryLaunchPoint deliveryLaunchPoint,
							   Address storeAddress,
							   Integer channelType,
							   DeliveryPlatformEnum lastDeliveryPlatform) {
		super(id, tenantId, storeId, cityCode, contactPhone, MALT_FARM_DELIVERY_PLATFORM, deliveryLaunchPoint, AUTO_LAUNCH_DELIVERY,channelType,lastDeliveryPlatform);
		this.storeAddress = storeAddress;
	}

	public MaltFarmDeliveryPoi(Long id,
							   Long tenantId,
							   Long storeId,
							   Integer cityCode,
							   String contactPhone,
							   DeliveryLaunchPoint deliveryLaunchPoint,
							   Address storeAddress,
							   Integer channelType,
							   DeliveryPlatformEnum lastDeliveryPlatform,
							   DeliveryIsShowItemNumberEnum deliveryIsShowItemNumberEnum) {
		super(id, tenantId, storeId, cityCode, contactPhone, MALT_FARM_DELIVERY_PLATFORM, deliveryLaunchPoint, AUTO_LAUNCH_DELIVERY,channelType,lastDeliveryPlatform,deliveryIsShowItemNumberEnum);
		this.storeAddress = storeAddress;
	}
}
