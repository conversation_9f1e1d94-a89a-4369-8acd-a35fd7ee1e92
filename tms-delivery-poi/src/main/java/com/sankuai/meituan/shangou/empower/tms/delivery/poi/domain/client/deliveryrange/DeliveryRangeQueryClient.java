package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.deliveryrange;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/9
 */
public interface DeliveryRangeQueryClient {

	/**
	 * 查询门店在指定渠道的配送范围
	 */
	DeliveryRangeQueryResult queryDeliveryRange(SelfBuiltDeliveryPoi deliveryPoi, ThirdDeliveryChannelEnum deliveryChannel);
}
