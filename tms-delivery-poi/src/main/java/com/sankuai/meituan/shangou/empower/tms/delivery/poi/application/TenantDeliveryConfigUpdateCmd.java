package com.sankuai.meituan.shangou.empower.tms.delivery.poi.application;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/22
 */
@Getter
@ToString
@AllArgsConstructor
public class TenantDeliveryConfigUpdateCmd {

	private final Long tenantId;

	private final List<TenantChannelDeliveryConfigUpdateCmd> channelDeliveryConfigs;

	private final Operator operator;

	@Getter
	@ToString
	@AllArgsConstructor
	public static class TenantChannelDeliveryConfigUpdateCmd {

		private final ThirdDeliveryChannelEnum deliveryChannel;

		private final String appKey;

		private final String secret;

		private final boolean enabled;
	}
}
