package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import com.google.common.base.Preconditions;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 骑手信息模型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class Rider {

	/**
	 * 骑手姓名
	 */
	private String riderName;

	/**
	 * 骑手手机号码
	 */
	private String riderPhone;

	/**
	 * 骑手电话号码加密token
	 */
	private String riderPhoneToken;

	public Rider(String riderName, String riderPhone, String riderPhoneToken) {
		Preconditions.checkArgument(StringUtils.isNotEmpty(riderName), "riderName is empty");
		Preconditions.checkArgument(StringUtils.isNotEmpty(riderPhone), "riderPhone is empty");
		this.riderName = riderName;
		this.riderPhone = riderPhone;
		this.riderPhoneToken = riderPhoneToken;
	}

	/**
	 * 用于清除骑手信息
	 */
	public static Rider buildEmptyRider(){
		Rider rider = new Rider();
		rider.setRiderName(StringUtils.EMPTY);
		rider.setRiderPhone(StringUtils.EMPTY);
		rider.setRiderPhoneToken(StringUtils.EMPTY);
		return rider;
	}
}
