package com.sankuai.meituan.shangou.empower.tms.delivery.poi.application;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ConfigModuleEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.exception.ChannelNotExistException;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.DeliveryChannelLaunchPointDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.DeliveryStoreConfigDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.StoreAggDeliveryConfigDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.relation.DeliveryPoiRelationClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.aggr.AggrDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.LionConfigUtils;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 门店配送渠道及配送规则设置相关服务
 *
 * <AUTHOR>
 * @date 2021/1/5
 */
@Slf4j
@Service
public class DeliveryConfigApplicationService {
	@Resource
	private DeliveryPoiRelationClient deliveryPoiRelationClient;
	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;
	@Resource
	private DeliveryPoiFactory deliveryPoiFactory;

	private static final int DEFAULT_IMMEDIATE_ORDER_DELAY_MINUTES = 0;
	private static final int DEFAULT_EMPOWER_BOOKING_ORDER_DELAY_MINUTES = 60;

	/**
	 * 修改门店配置（包括 配送渠道绑定/解绑、配送规则配置）.
	 */
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public DeliveryPoi saveStoreConfiguration(SaveConfigCmd cmd, DeliveryPoi deliveryPoi) {

		deliveryPoiRepository.saveDeliveryPoi(deliveryPoi);
		if (cmd.getModule().equals(ConfigModuleEnum.CHANNEL.getValue())) {
			deliveryPoiRelationClient.syncStoreChannelInfo(cmd.getChannelInfo());
		}

		return deliveryPoi;
	}


	/**
	 * 歪马构建门店配置
	 */
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public DeliveryPoi buildDeliveryPoiConfig(SaveConfigCmd cmd) {
		DeliveryPlatformEnum deliveryPlatform = translateDeliveryPlatform(cmd.getDeliveryPlatformInfo())
				.orElse(DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM);
		Integer channelType=100;
		if(cmd.getDeliveryPlatformInfo()!=null && cmd.getDeliveryPlatformInfo().getChannelType()!=null){
			channelType = cmd.getDeliveryPlatformInfo().getChannelType();
		}
		final Integer finChannelType=channelType;
		DeliveryPoi deliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithChannelType(cmd.getTenantId(), cmd.getStoreId(),channelType)
				.map(it -> {
					//配送平台变更
					if (it.getDeliveryPlatform() != deliveryPlatform) {
						return deliveryPoiFactory.createNewPlatformPoiByExisting(it, deliveryPlatform);
					} else {
						return it;
					}
				})
				.orElseGet(() -> deliveryPoiFactory.createDefaultDeliveryPoi(deliveryPlatform, cmd.getTenantId(), cmd.getStoreId(),finChannelType));

		//变更实时单配送发起时间点配置
		Optional.ofNullable(cmd.getImmediateOrderDeliveryLaunchPointConfig())
				.ifPresent(it -> deliveryPoi.getDeliveryLaunchPoint().setImmediateOrderDeliveryLaunchPointConfig(it));
		//变更预约单配送发起时间点配置
		Optional.ofNullable(cmd.getBookingOrderDeliveryLaunchPointConfig())
				.ifPresent(it -> deliveryPoi.getDeliveryLaunchPoint().setBookingOrderDeliveryLaunchPointConfig(it));
		//变更配送发起类型
		Optional.ofNullable(cmd.getDeliveryLaunchType()).ifPresent(deliveryPoi::setDeliveryLaunchType);
		//变更自配送策略id
		Optional.ofNullable(cmd.getAutoLaunchStrategyId())
				.filter(it -> deliveryPoi.getDeliveryPlatform() == DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM)
				.ifPresent(autoLaunchStrategyId -> {
					AggrDeliveryPoi aggrDeliveryPoi = (AggrDeliveryPoi) deliveryPoi;
					aggrDeliveryPoi.setAutoLaunchStrategyId(autoLaunchStrategyId);
				});
		return deliveryPoi;
	}

	/**
	 * 赋能构建门店配置
	 */
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public DeliveryPoi buildDeliveryPoiConfigV2(SaveConfigCmd cmd) {
		DeliveryPlatformEnum deliveryPlatform = translateDeliveryPlatform(cmd.getDeliveryPlatformInfo())
				.orElse(DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM);
		Integer channelType=100;
		if(cmd.getDeliveryPlatformInfo()!=null && cmd.getDeliveryPlatformInfo().getChannelType()!=null){
			channelType = cmd.getDeliveryPlatformInfo().getChannelType();
		}
		final Integer finChannelType=channelType;
		DeliveryPoi deliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithChannelType(cmd.getTenantId(), cmd.getStoreId(),channelType)
				.map(it -> {
					//配送平台变更
					if (it.getDeliveryPlatform() != deliveryPlatform) {
						return deliveryPoiFactory.createNewPlatformPoiByExisting(it, deliveryPlatform);
					} else {
						return it;
					}
				})
				.orElseGet(() -> deliveryPoiFactory.createDefaultDeliveryPoi(deliveryPlatform, cmd.getTenantId(), cmd.getStoreId(),finChannelType));


		DeliveryChannelLaunchPointDto deliveryChannelLaunchPointDto = new DeliveryChannelLaunchPointDto(deliveryPoi.getChannelType(), deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint().getCode(),
				deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getDelayMinutes(), deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getConfigMinutes());
		//抖音渠道，且非平台配送，则修改自动呼叫时间为默认值
		if (channelType.equals(DynamicChannelType.DOU_YIN.getChannelId()) && !deliveryPlatform.equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM)) {
			deliveryChannelLaunchPointDto = new DeliveryChannelLaunchPointDto(deliveryPoi.getChannelType(), deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint().getCode(), DEFAULT_IMMEDIATE_ORDER_DELAY_MINUTES, DEFAULT_EMPOWER_BOOKING_ORDER_DELAY_MINUTES);
			updateLaunchPointAndTime(deliveryPoi, deliveryChannelLaunchPointDto);
		} else {
			updateLaunchPointAndTime(deliveryPoi, deliveryChannelLaunchPointDto);
		}
		//变更自配送策略id
		Optional.ofNullable(cmd.getAutoLaunchStrategyId())
				.filter(it -> deliveryPoi.getDeliveryPlatform() == DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM)
				.ifPresent(autoLaunchStrategyId -> {
					AggrDeliveryPoi aggrDeliveryPoi = (AggrDeliveryPoi) deliveryPoi;
					aggrDeliveryPoi.setAutoLaunchStrategyId(autoLaunchStrategyId);
				});
		return deliveryPoi;
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public StoreConfigInfo buildDeliveryPoiConfigList (Long storeId, Long tenantId, List<DeliveryStoreConfigDto> deliveryPlatformConfigList) {
		Map<Integer, DeliveryPoi> allDeliveryPoiMap = deliveryPoiRepository.queryAllDeliveryPoi(tenantId, storeId)
				.stream().collect(Collectors.toMap(DeliveryPoi::getChannelType, deliveryPoi -> deliveryPoi));
		List<DeliveryPoi> dapDeliveryPoi = Lists.newArrayList();
		List<DeliveryPoi> saveDeliveryPoi = Lists.newArrayList();
		Map<String,StoreConfigOpInfo> opInfoMap=new HashMap<>();
		deliveryPlatformConfigList.forEach(it -> {
			DeliveryPlatformEnum deliveryPlatform = translateDeliveryPlatform(AggDeliveryPlatformInfo.builder()
					.openFlag(it.getOpenFlag())
					.platformCode(it.getPlatformCode())
					.channelType(it.getChannelType())
					.build())
					.orElse(DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM);
			DeliveryChannelLaunchPointDto deliveryChannelLaunchPointDto = new DeliveryChannelLaunchPointDto(it.getChannelType(), it.getDeliveryLaunchPoint(), it.getDeliveryLaunchDelayMinutes(), it.getBookingOrderDeliveryLaunchMinutes());
			DeliveryPoi deliveryPoi = allDeliveryPoiMap.get(it.getChannelType());
			if(Objects.isNull(deliveryPoi)) {
				DeliveryPoi createDeliveryPoi = deliveryPoiFactory.createDefaultDeliveryPoi(deliveryPlatform, tenantId, storeId,it.getChannelType());
				//抖音渠道且非平台配送，修改自动呼叫时间为默认值
				if (it.getChannelType().equals(DynamicChannelType.DOU_YIN.getChannelId()) && !deliveryPlatform.equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM)) {
					deliveryChannelLaunchPointDto = new DeliveryChannelLaunchPointDto(it.getChannelType(), it.getDeliveryLaunchPoint(), DEFAULT_IMMEDIATE_ORDER_DELAY_MINUTES, DEFAULT_EMPOWER_BOOKING_ORDER_DELAY_MINUTES);
					updateLaunchPointAndTime(createDeliveryPoi, deliveryChannelLaunchPointDto);
				} else {
					updateLaunchPointAndTime(createDeliveryPoi, deliveryChannelLaunchPointDto);
				}
				if (deliveryPlatform.equals(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM)) {
					dapDeliveryPoi.add(createDeliveryPoi);
				} else {
					saveDeliveryPoi.add(createDeliveryPoi);
				}
				opInfoMap.put(createDeliveryPoi.getStoreId()+"_"+createDeliveryPoi.getChannelType(),
						StoreConfigOpInfo.builder().channelType(createDeliveryPoi.getChannelType())
								.deliveryPoint(it.getDeliveryLaunchPoint())
								.deliveryPlatform(deliveryPlatform.getCode())
								.deliveryLaunchDelayMinutes(it.getDeliveryLaunchDelayMinutes())
								.bookingDeliveryLaunchDelayMinutes(it.getBookingOrderDeliveryLaunchMinutes()).build());
			} else {
				DeliveryPoi poi = deliveryPoiFactory.createNewPlatformPoiByExisting(deliveryPoi, deliveryPlatform);
				//抖音渠道且非平台配送，修改自动呼叫时间为默认值
				if (it.getChannelType().equals(DynamicChannelType.DOU_YIN.getChannelId()) && !deliveryPlatform.equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM)) {
					deliveryChannelLaunchPointDto = new DeliveryChannelLaunchPointDto(it.getChannelType(), it.getDeliveryLaunchPoint(), DEFAULT_IMMEDIATE_ORDER_DELAY_MINUTES, DEFAULT_EMPOWER_BOOKING_ORDER_DELAY_MINUTES);
					updateLaunchPointAndTime(poi, deliveryChannelLaunchPointDto);
				} else {
					updateLaunchPointAndTime(poi, deliveryChannelLaunchPointDto);
				}
				if (deliveryPlatform.equals(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM)) {
					dapDeliveryPoi.add(poi);
				} else {
					saveDeliveryPoi.add(poi);
				}
			}
			opInfoMap.put(deliveryPoi.getStoreId()+"_"+deliveryPoi.getChannelType(),
					StoreConfigOpInfo.builder().channelType(deliveryPoi.getChannelType())
							.deliveryPlatform(deliveryPlatform.getCode())
							.deliveryPoint(deliveryChannelLaunchPointDto.getDeliveryLaunchPoint())
							.deliveryLaunchDelayMinutes(deliveryChannelLaunchPointDto.getDeliveryLaunchDelayMinutes())
							.bookingDeliveryLaunchDelayMinutes(deliveryChannelLaunchPointDto.getBookingOrderDeliveryLaunchMinutes())
							.build());
		});
		StoreConfigInfo storeConfigInfo = new StoreConfigInfo();
		storeConfigInfo.setDeliveryPoiList(dapDeliveryPoi);
		storeConfigInfo.setSaveDeliveryPoiList(saveDeliveryPoi);
		storeConfigInfo.setOpInfoMap(opInfoMap);

		return storeConfigInfo;
	}


	public void batchSaveStoreConfigLog(List<DeliveryPoi> saveDeliveryPoi, Map<String,StoreConfigOpInfo> opInfoMap, Long operatorId, String operatorName) {
		// 插入store_config_op_log表
		List<StoreConfigOpLog> storeConfigOpLogList = transfer2OpLog(saveDeliveryPoi, opInfoMap,
				operatorId, operatorName);
		deliveryPoiRepository.batchSaveStoreConfigOpLog(storeConfigOpLogList);
	}

	/**
	 * 批量导入门店配送配置接口for循环调用此方法
	 * 此方法依然是单个修改
	 */
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public StoreConfigInfo buildBatchImportDeliveryPoiConfigList(Long storeId, Long tenantId, List<DeliveryStoreConfigDto> deliveryPlatformConfigList, Long operatorId, String operatorName) {
		List<DeliveryPoi> newDeliveryPoiList = Lists.newArrayList();
		List<DeliveryPoi> saveDeliveryPoiList = Lists.newArrayList();
		Map<String,StoreConfigOpInfo> opInfoMap = new HashMap<>();

		List<DeliveryPoi> allDeliveryPoiList = deliveryPoiRepository.queryAllDeliveryPoi(tenantId, storeId);
		Map<Integer, DeliveryPoi> allDeliveryPoiMap = allDeliveryPoiList.stream().collect(Collectors.toMap(DeliveryPoi::getChannelType, Function.identity()));

		deliveryPlatformConfigList.forEach(it -> {
			ImmediateOrderDeliveryLaunchPointEnum launchPoint = ImmediateOrderDeliveryLaunchPointEnum.enumOf(it.getDeliveryLaunchPoint());
			DeliveryPlatformEnum deliveryPlatform = translateDeliveryPlatform(AggDeliveryPlatformInfo.builder()
					.openFlag(it.getOpenFlag())
					.platformCode(it.getPlatformCode())
					.channelType(it.getChannelType())
					.build())
					.orElse(DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM);

			DeliveryPoi deliveryPoi = allDeliveryPoiMap.get(it.getChannelType());
			if (Objects.isNull(deliveryPoi)) {
				DeliveryPoi createDeliveryPoi = deliveryPoiFactory.createDefaultDeliveryPoi(deliveryPlatform, tenantId, storeId,it.getChannelType());
				updateLaunchPoint(createDeliveryPoi, it.getDeliveryLaunchPoint());
				newDeliveryPoiList.add(createDeliveryPoi);
				saveDeliveryPoiList.add(createDeliveryPoi);
				opInfoMap.put(createDeliveryPoi.getStoreId()+"_"+createDeliveryPoi.getChannelType(),StoreConfigOpInfo.builder().channelType(createDeliveryPoi.getChannelType()).deliveryPoint(it.getDeliveryLaunchPoint()).deliveryPlatform(deliveryPlatform.getCode()).build());
			}

			if ((!(deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint().equals(launchPoint))) && deliveryPlatform.equals(deliveryPoi.getDeliveryPlatform())) {
				opInfoMap.put(deliveryPoi.getStoreId()+"_"+deliveryPoi.getChannelType(),StoreConfigOpInfo.builder().channelType(deliveryPoi.getChannelType()).deliveryPoint(it.getDeliveryLaunchPoint()).build());
				updateLaunchPoint(deliveryPoi, it.getDeliveryLaunchPoint());
				saveDeliveryPoiList.add(deliveryPoi);
			}

			if (!(deliveryPoi.getDeliveryPlatform().equals(deliveryPlatform))) {
				StoreConfigOpInfo opInfo = StoreConfigOpInfo.builder().channelType(deliveryPoi.getChannelType()).deliveryPlatform(deliveryPlatform.getCode()).build();
				DeliveryPoi poi = deliveryPoiFactory.createNewPlatformPoiByExisting(deliveryPoi, deliveryPlatform);
				if(!(deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint().equals(launchPoint))) {
					opInfo.setDeliveryPoint(launchPoint.getCode());
					updateLaunchPoint(poi, it.getDeliveryLaunchPoint());
				}
				opInfoMap.put(deliveryPoi.getStoreId()+"_"+deliveryPoi.getChannelType(),opInfo);
				saveDeliveryPoiList.add(poi);
				newDeliveryPoiList.add(poi);
			}
		});

		// 插入store_config_op_log表
		List<StoreConfigOpLog> storeConfigOpLogList = transfer2OpLog(saveDeliveryPoiList, opInfoMap, operatorId, operatorName);
		deliveryPoiRepository.batchSaveStoreConfigOpLog(storeConfigOpLogList);

		StoreConfigInfo storeConfigInfo = new StoreConfigInfo();
		storeConfigInfo.setDeliveryPoiList(newDeliveryPoiList);
		storeConfigInfo.setSaveDeliveryPoiList(saveDeliveryPoiList);

		return storeConfigInfo;
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public void modifyStoreAggDeliveryConfig(Long tenantId, Long storeId, Long operatorId, String operatorName,
											 StoreAggDeliveryConfigDto storeAggDeliveryConfigDto) {
		// 更新store_config表
		List<DeliveryPoi> deliveryPoiList = deliveryPoiRepository.queryAggDeliveryPoi(tenantId, storeId);
		List<Integer> channelTypeList = Lists.transform(deliveryPoiList, DeliveryPoi::getChannelType);
		Map<Integer, DeliveryChannelLaunchPointDto> launchPointMapFromReq =
				storeAggDeliveryConfigDto.getDeliveryChannelLaunchPoints().stream()
						.collect(Collectors.toMap(DeliveryChannelLaunchPointDto::getChannelType, Function.identity()));
		boolean checkResult = CollectionUtils.isNotEmpty(channelTypeList) && new HashSet<>(channelTypeList).containsAll(launchPointMapFromReq.keySet());
		if (!checkResult) {
			log.error("修改聚合配送设置失败，下发渠道与数据库渠道不一致");
			throw new ChannelNotExistException(FailureCodeEnum.CHANNEL_NOT_EXIST_EXCEPTION.getCode(),
					FailureCodeEnum.CHANNEL_NOT_EXIST_EXCEPTION.getMessage());
		}

		Map<String,StoreConfigOpInfo> opInfoMap=new HashMap<>();
		List<DeliveryPoi> updateDeliveryPoiList=new ArrayList<>();

		deliveryPoiList.forEach(deliveryPoi -> {
			if (launchPointMapFromReq.containsKey(deliveryPoi.getChannelType())) {
				StoreConfigOpInfo opInfo = StoreConfigOpInfo.builder()
						.channelType(deliveryPoi.getChannelType())
						.deliveryPoint(launchPointMapFromReq.get(deliveryPoi.getChannelType()).getDeliveryLaunchPoint())
						.deliveryLaunchDelayMinutes(launchPointMapFromReq.get(deliveryPoi.getChannelType()).getDeliveryLaunchDelayMinutes())
						.bookingDeliveryLaunchDelayMinutes(launchPointMapFromReq.get(deliveryPoi.getChannelType()).getBookingOrderDeliveryLaunchMinutes())
						.build();
				updateLaunchPointAndTime(deliveryPoi, launchPointMapFromReq.get(deliveryPoi.getChannelType()));
				updateDeliveryPoiList.add(deliveryPoi);
				opInfoMap.put(deliveryPoi.getStoreId()+"_"+deliveryPoi.getChannelType(),opInfo);

			}
		});
		deliveryPoiRepository.batchSaveDeliveryPoi(deliveryPoiList);

		// 插入store_config_op_log表
		List<StoreConfigOpLog> storeConfigOpLogList = transfer2OpLog(updateDeliveryPoiList, opInfoMap,
				operatorId, operatorName);
		deliveryPoiRepository.batchSaveStoreConfigOpLog(storeConfigOpLogList);
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public StoreConfigInfo buildBatchOpenDapShop(List<DeliveryStoreConfigDto> deliveryConfigList, Long tenantId, Long storeId, DeliveryPlatformEnum deliveryPlatform) {
		Map<Integer, DeliveryPoi> allDeliveryPoiMap = deliveryPoiRepository.queryAllDeliveryPoi(tenantId, storeId)
				.stream().collect(Collectors.toMap(DeliveryPoi::getChannelType, deliveryPoi -> deliveryPoi));
		List<DeliveryPoi> deliveryPoiList = Lists.newArrayList();
		Map<String,StoreConfigOpInfo> opInfoMap=new HashMap<>();
		deliveryConfigList.forEach(deliveryConfig -> {
			DeliveryChannelLaunchPointDto deliveryChannelLaunchPointDto = new DeliveryChannelLaunchPointDto(deliveryConfig.getChannelType(), deliveryConfig.getDeliveryLaunchPoint(), DEFAULT_IMMEDIATE_ORDER_DELAY_MINUTES, DEFAULT_EMPOWER_BOOKING_ORDER_DELAY_MINUTES);
			DeliveryPoi deliveryPoi = allDeliveryPoiMap.get(deliveryConfig.getChannelType());
			if(Objects.isNull(deliveryPoi)) {
				DeliveryPoi createDeliveryPoi = deliveryPoiFactory.createDefaultDeliveryPoi(deliveryPlatform, tenantId, storeId,deliveryConfig.getChannelType());
				updateLaunchPointAndTime(createDeliveryPoi, deliveryChannelLaunchPointDto);
				deliveryPoiList.add(createDeliveryPoi);
				opInfoMap.put(createDeliveryPoi.getStoreId()+"_"+createDeliveryPoi.getChannelType(),
						StoreConfigOpInfo.builder().channelType(createDeliveryPoi.getChannelType())
								.deliveryPlatform(deliveryPlatform.getCode())
								.deliveryPoint(deliveryChannelLaunchPointDto.getDeliveryLaunchPoint())
								.deliveryLaunchDelayMinutes(deliveryChannelLaunchPointDto.getDeliveryLaunchDelayMinutes())
								.bookingDeliveryLaunchDelayMinutes(deliveryChannelLaunchPointDto.getBookingOrderDeliveryLaunchMinutes()).build());
			} else {
				DeliveryPoi poi = deliveryPoiFactory.createNewPlatformPoiByExisting(deliveryPoi, deliveryPlatform);
				updateLaunchPointAndTime(poi, deliveryChannelLaunchPointDto);
				opInfoMap.put(deliveryPoi.getStoreId()+"_"+deliveryPoi.getChannelType(),
						StoreConfigOpInfo.builder().channelType(deliveryPoi.getChannelType())
								.deliveryPlatform(deliveryPlatform.getCode())
								.deliveryPoint(deliveryChannelLaunchPointDto.getDeliveryLaunchPoint())
								.deliveryLaunchDelayMinutes(deliveryChannelLaunchPointDto.getDeliveryLaunchDelayMinutes())
								.bookingDeliveryLaunchDelayMinutes(deliveryChannelLaunchPointDto.getBookingOrderDeliveryLaunchMinutes()).build());
				deliveryPoiList.add(poi);
			}
		});
		return StoreConfigInfo.builder().deliveryPoiList(deliveryPoiList).opInfoMap(opInfoMap).build();
	}


	private Optional<DeliveryPlatformEnum> translateDeliveryPlatform(AggDeliveryPlatformInfo deliveryPlatformInfo) {
		if (deliveryPlatformInfo == null) {
			return Optional.empty();
		}

		return Optional.of(Optional.of(deliveryPlatformInfo)
				.filter(it -> it.getOpenFlag() == BigInteger.ONE.intValue())
				.map(it -> DeliveryPlatformEnum.enumOf(it.getPlatformCode()))
				.orElse(DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM));
	}

	//关闭青云聚信平台
	private boolean closeDap(DeliveryPoi deliveryPoi) {
		return deliveryPoi.getDeliveryPlatform().equals(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM);
	}

	private void updateLaunchPoint(DeliveryPoi deliveryPoi, Integer launchPointValue) {
		DeliveryLaunchPoint deliveryLaunchPoint = Optional.ofNullable(deliveryPoi.getDeliveryLaunchPoint())
				.orElseThrow(() -> new BizException("deliveryLaunchPoint is null"));
		DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig immediateOrderDeliveryLaunchPointConfig =
				Optional.ofNullable(deliveryLaunchPoint.getImmediateOrderDeliveryLaunchPointConfig())
						.orElseThrow(() -> new BizException("immediateOrderDeliveryLaunchPointConfig is null"));
		ImmediateOrderDeliveryLaunchPointEnum launchPointEnum =
				Optional.ofNullable(ImmediateOrderDeliveryLaunchPointEnum.enumOf(launchPointValue))
				.orElseThrow(() -> new BizException("launchPointEnum is null"));
		immediateOrderDeliveryLaunchPointConfig.setLaunchPoint(launchPointEnum);
	}

	private void updateLaunchPointAndTime(DeliveryPoi deliveryPoi, DeliveryChannelLaunchPointDto deliveryChannelLaunchPointDto) {
		DeliveryLaunchPoint deliveryLaunchPoint = Optional.ofNullable(deliveryPoi.getDeliveryLaunchPoint())
				.orElseThrow(() -> new BizException("deliveryLaunchPoint is null"));
		DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig immediateOrderDeliveryLaunchPointConfig =
				Optional.ofNullable(deliveryLaunchPoint.getImmediateOrderDeliveryLaunchPointConfig())
						.orElseThrow(() -> new BizException("immediateOrderDeliveryLaunchPointConfig is null"));
		DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig bookingOrderDeliveryLaunchPointConfig =
				Optional.ofNullable(deliveryLaunchPoint.getBookingOrderDeliveryLaunchPointConfig())
						.orElseThrow(() -> new BizException("bookingOrderDeliveryLaunchPointConfig is null"));
		ImmediateOrderDeliveryLaunchPointEnum launchPointEnum =
				Optional.ofNullable(ImmediateOrderDeliveryLaunchPointEnum.enumOf(deliveryChannelLaunchPointDto.getDeliveryLaunchPoint()))
						.orElseThrow(() -> new BizException("launchPointEnum is null"));
		immediateOrderDeliveryLaunchPointConfig.setLaunchPoint(launchPointEnum);
		immediateOrderDeliveryLaunchPointConfig.setDelayMinutes(Optional.ofNullable(deliveryChannelLaunchPointDto.getDeliveryLaunchDelayMinutes()).orElse(DEFAULT_IMMEDIATE_ORDER_DELAY_MINUTES));
		bookingOrderDeliveryLaunchPointConfig.setConfigMinutes(Optional.ofNullable(deliveryChannelLaunchPointDto.getBookingOrderDeliveryLaunchMinutes()).orElse(DEFAULT_EMPOWER_BOOKING_ORDER_DELAY_MINUTES));
	}

	private List<StoreConfigOpLog> transfer2OpLog(List<DeliveryPoi> deliveryPoiList,
												  Map<String,StoreConfigOpInfo> opInfoMap,
												  Long operatorId, String operatorName) {
		return deliveryPoiList.stream().map(deliveryPoi -> {
			Integer channelType = deliveryPoi.getChannelType();
			StoreConfigOpInfo storeConfigOpInfo =opInfoMap.get(deliveryPoi.getStoreId()+"_"+channelType);

			return StoreConfigOpLog.builder()
					.storeConfigId(deliveryPoi.getId())
					.tenantId(deliveryPoi.getTenantId())
					.storeId(deliveryPoi.getStoreId())
					.opType(StoreConfigOpTypeEnum.MODIFY.getValue())
					.opInfo(storeConfigOpInfo== null ? null:JsonUtil.toJson(storeConfigOpInfo))
					.operatorId(operatorId)
					.operatorName(operatorName)
					.build();
		}).collect(Collectors.toList());
	}
}
