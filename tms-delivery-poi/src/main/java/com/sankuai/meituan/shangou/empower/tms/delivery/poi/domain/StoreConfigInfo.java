package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StoreConfigInfo {

    /**
     * 修改门店配送平台配置
     */
    private List<DeliveryPoi> deliveryPoiList;

    /**
     * 保存门店配置
     */
    private List<DeliveryPoi> saveDeliveryPoiList;

    private Map<String,StoreConfigOpInfo> opInfoMap;
}
