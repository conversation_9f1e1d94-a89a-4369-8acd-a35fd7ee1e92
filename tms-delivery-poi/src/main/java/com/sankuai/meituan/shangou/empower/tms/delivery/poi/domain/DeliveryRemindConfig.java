package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import lombok.Data;

/**
 * 配送提醒设置配置
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@Data
public class DeliveryRemindConfig {

    /**
     * 领取超时时间（分钟），0为未开启
     */
    private Integer receiveTimeOutMins;

    /**
     * 取货超时时间（分钟），0为未开启
     */
    private Integer takenTimeOutMins;

    /**
     * 即将超时时间（分钟），0为未开启
     */
    private Integer soonDeliveryTimeoutMinsBeforeEta;

    /**
     * 配送超时配置
     */
    private DeliveryTimeOutsBeforeEtaConfig deliveryTimeOutsBeforeEtaConfig;

    /**
     * 配送超时配置
     */
    @Data
    public static class DeliveryTimeOutsBeforeEtaConfig {

        /**
         * 立即单配送超时时间（分钟）
         */
        private Integer immediateBeforeEtaMins;

        /**
         * 预约单配送超时时间（分钟）
         */
        private Integer bookingBeforeEtaMins;
    }
}

