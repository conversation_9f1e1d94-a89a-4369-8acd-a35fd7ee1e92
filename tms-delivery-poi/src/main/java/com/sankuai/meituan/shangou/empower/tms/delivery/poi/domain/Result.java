package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/20
 */
@Getter
@ToString
public class Result<T> {

	private final T info;

	private final Failure failure;

	public Result(T info) {
		this.info = info;
		this.failure = null;
	}

	public Result(Failure failure) {
		this.info = null;
		this.failure = failure;
	}

	public boolean isSuccess() {
		return failure == null;
	}

	public boolean isFail() {
		return failure != null;
	}

	public boolean failureCodeIs(FailureCodeEnum failureCodeEnum) {
		return this.failure != null && failureCodeEnum != null && this.failure.getFailureCode() == failureCodeEnum.getCode();
	}

}
