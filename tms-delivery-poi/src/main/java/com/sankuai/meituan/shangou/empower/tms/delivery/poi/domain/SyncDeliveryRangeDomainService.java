package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.deliveryrange.DeliveryRangeQueryClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.deliveryrange.DeliveryRangeQueryResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.deliveryrange.OnlineStoreDeliveryRangeUpdateResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.deliveryrange.OrderPlatformDeliveryRangeUpdateClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.DeliveryRange;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.QUERY_CHANNEL_DELIVERY_RANGE_FAIL;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.SYNC_DELIVERY_RANGE_ERROR;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/8
 */
@Slf4j
@Component
public class SyncDeliveryRangeDomainService {

	@Resource
	private DeliveryRangeQueryClient deliveryRangeQueryClient;
	@Resource
	private OrderPlatformDeliveryRangeUpdateClient orderPlatformDeliveryRangeUpdateClient;

	/**
	 * 向指定线上订单平台同步配送范围
	 *
	 * @return 同步失败的线上订单平台及其失败原因映射
	 */
	public Map<Integer, Failure> syncDeliveryRange(SelfBuiltDeliveryPoi deliveryPoi, Set<Integer> channelIds) {
		if (CollectionUtils.isEmpty(channelIds)) {
			return new HashMap<>();
		}

		if (MapUtils.isEmpty(deliveryPoi.getChannelStoreConfigMap())) {
			log.warn("配送门店[{}]没有绑定的配送渠道门店, 将会放弃本次同步并默认返回成功", deliveryPoi.getStoreId());
			return new HashMap<>();
		}

		//查询所有对接的三方配送渠道的配送范围
		Map<ThirdDeliveryChannelEnum, List<DeliveryRange>> deliveryRangeMap = new HashMap<>();
		for (ThirdDeliveryChannelEnum deliveryChannel : deliveryPoi.getChannelStoreConfigMap().keySet()) {
			DeliveryRangeQueryResult rangeQueryResult = deliveryRangeQueryClient.queryDeliveryRange(deliveryPoi, deliveryChannel);
			//范围查询成功
			if (rangeQueryResult.isSuccess()) {
				deliveryRangeMap.put(deliveryChannel, rangeQueryResult.getDeliveryRangeList());

				//失败可重试
			} else if (rangeQueryResult.isNeedRetry()) {
				return channelIds.stream()
						.collect(Collectors.toMap(it -> it, it -> new Failure(true, QUERY_CHANNEL_DELIVERY_RANGE_FAIL)));

				//直接失败
			} else {
				return channelIds.stream()
						.collect(Collectors.toMap(it -> it, it -> new Failure(false, QUERY_CHANNEL_DELIVERY_RANGE_FAIL)));
			}
		}

		//向线上订单平台同步配送范围
		Map<Integer, Failure> failureMap = new HashMap<>();
		for (Integer channelId : channelIds) {
			if (deliveryPoi.getOrderPlatformDeliveryConfigMap().containsKey(channelId)) {
				OnlineStoreDeliveryRangeUpdateResult updateResult = orderPlatformDeliveryRangeUpdateClient.updateDeliveryRange(
						deliveryPoi, channelId, deliveryRangeMap
				);
				if (updateResult.isSuccess()) {
					log.info("门店[{}]同步配送范围到配送商[{}]成功", deliveryPoi.getStoreId(), channelId);
				} else {
					failureMap.put(channelId, new Failure(updateResult.isNeedRetry(), SYNC_DELIVERY_RANGE_ERROR.getCode(), updateResult.getErrorMsg()));
				}
			} else {
				log.warn("订单平台[{}]对应的配送参数不全，将放弃进行同步", channelId);
			}
		}

		return failureMap;
	}
}
