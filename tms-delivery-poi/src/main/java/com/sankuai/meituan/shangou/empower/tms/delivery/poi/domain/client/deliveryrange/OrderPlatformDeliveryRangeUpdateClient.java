package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.deliveryrange;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.DeliveryRange;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/9
 */
public interface OrderPlatformDeliveryRangeUpdateClient {

	/**
	 * 修改订单平台门店配送范围，当前仅支持美团渠道
	 * 支持同时上传多个配送渠道的配送范围
	 */
	OnlineStoreDeliveryRangeUpdateResult updateDeliveryRange(SelfBuiltDeliveryPoi deliveryPoi,
	                                                         Integer channelType,
	                                                         Map<ThirdDeliveryChannelEnum, List<DeliveryRange>> rangeMap);
}
