package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt;

import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 指定顺序轮询配送策略配置信息模型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
@Slf4j
@Getter
@ToString(callSuper = true)
public class SequentialPollingStrategyConfig extends DeliveryStrategyConfig {

	/**
	 * 配送渠道顺序
	 */
	private final List<ThirdDeliveryChannelEnum> orderedDeliveryChannels;

	/**
	 * 自动切换配送渠道时间(未接单分钟数)
	 */
	private final Integer timeoutForShiftDeliveryChannelInMinutes;

	public SequentialPollingStrategyConfig(List<ThirdDeliveryChannelEnum> orderedDeliveryChannels, Integer timeoutForShiftDeliveryChannelInMinutes) {
		Preconditions.checkNotNull(orderedDeliveryChannels, "orderedDeliveryChannels is null");
		Preconditions.checkNotNull(timeoutForShiftDeliveryChannelInMinutes, "timeoutForShiftDeliveryChannelInMinutes is null");

		this.orderedDeliveryChannels = orderedDeliveryChannels;
		this.timeoutForShiftDeliveryChannelInMinutes = timeoutForShiftDeliveryChannelInMinutes;
	}

	public boolean hasNextAvailableDeliveryChannel(ThirdDeliveryChannelEnum currentDeliveryChannel) {
		if (CollectionUtils.isEmpty(this.orderedDeliveryChannels)) {
			log.warn("The orderedDeliveryChannels is empty");
			return false;
		}

		int currentChannelIndex = this.orderedDeliveryChannels.indexOf(currentDeliveryChannel);
		if (currentChannelIndex == -1) {
			log.warn("Current deliveryChannel[{}] isn't in the orderedDeliveryChannels[{}]", currentChannelIndex, this.orderedDeliveryChannels);
			return false;
		}

		return currentChannelIndex + 1 < this.orderedDeliveryChannels.size();
	}
}
