package com.sankuai.meituan.shangou.empower.tms.delivery.poi.application;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.OrderPlatformDeliveryConfig;
import lombok.Getter;
import lombok.ToString;

import java.util.Map;

/**
 * 门店配送范围同步命令
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/8
 */
@Getter
@ToString
public class DeliveryRangeSyncCmd {

	/**
	 * 配送门店
	 */
	private final DeliveryPoi deliveryPoi;

	/**
	 * 需要同步配送范围的订单渠道及其对应的配置信息
	 */
	private final Map<Integer, OrderPlatformDeliveryConfig> orderPlatformDeliveryConfigMap;

	public DeliveryRangeSyncCmd(DeliveryPoi deliveryPoi,
	                            Map<Integer, OrderPlatformDeliveryConfig> orderPlatformDeliveryConfigMap) {
		Preconditions.checkNotNull(deliveryPoi, "deliveryPoi is null");

		this.deliveryPoi = deliveryPoi;
		this.orderPlatformDeliveryConfigMap = orderPlatformDeliveryConfigMap;
	}
}
