package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.servicepackage;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 海葵配送(美团同城配)
 * 商家可以签约一个或多个配送服务包，因此在发起配送时需要选择服务包，默认按照配送效率降序
 * https://peisong.meituan.com/open/doc#section2-1
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/13
 */
@Slf4j
@Getter
@ToString(callSuper = true)
public class HaiKuiServicePackage extends ServicePackage {

	/**
	 * 默认海葵服务包
	 */
	private static final List<HaiKuiServicePackage> DEFAULT_HAI_KUI_SERVICE_PACKAGES = Arrays.asList(
			new HaiKuiServicePackage("4002", "飞速达", 1, false, 45),
			new HaiKuiServicePackage("4011", "快速达", 2, false, 60),
			new HaiKuiServicePackage("4012", "及时达", 3, false, 120),
			new HaiKuiServicePackage("4013", "集中达", 4, false, 120),
			new HaiKuiServicePackage("100000", "光速达-40", 5, false, 40),
			new HaiKuiServicePackage("100001", "光速达-45", 6, false, 45),
			new HaiKuiServicePackage("100002", "光速达-50", 7, false, 50),
			new HaiKuiServicePackage("100003", "光速达-55", 8, false, 60),
			new HaiKuiServicePackage("100004", "快速达-7590", 9, false, 90),
			new HaiKuiServicePackage("100005", "快速达-6090", 10, false, 90),
			new HaiKuiServicePackage("100006", "及时达", 11, false, 120)
	);

	private final int expectedDeliveryMinutes;

	@JsonCreator
	public HaiKuiServicePackage(@JsonProperty("code") String code,
	                            @JsonProperty("name") String name,
	                            @JsonProperty("order") int order,
	                            @JsonProperty("testOnly") boolean testOnly,
	                            @JsonProperty("expectedDeliveryMinutes") int expectedDeliveryMinutes) {
		super(code, name, order, testOnly);
		this.expectedDeliveryMinutes = expectedDeliveryMinutes;
	}

	public static List<HaiKuiServicePackage> getAllServicePackages() {
		try {
			return Optional.ofNullable(ConfigUtilAdapter.getString("delivery_service_packages_HAI_KUI"))
					.map(json -> JsonUtil.fromJson(json, new TypeReference<List<HaiKuiServicePackage>>() {
					}))
					.orElse(new ArrayList<>(DEFAULT_HAI_KUI_SERVICE_PACKAGES));
		} catch (Exception e) {
			log.error("解析海葵服务包mcc配置失败，将使用默认服务包");
			return new ArrayList<>(DEFAULT_HAI_KUI_SERVICE_PACKAGES);
		}
	}

	public static Optional<HaiKuiServicePackage> getServicePackageByCode(String code) {
		if (StringUtils.isBlank(code)) {
			return Optional.empty();
		}

		return getAllServicePackages()
				.stream()
				.filter(it -> StringUtils.equals(it.getCode(), code))
				.findAny();
	}
}
