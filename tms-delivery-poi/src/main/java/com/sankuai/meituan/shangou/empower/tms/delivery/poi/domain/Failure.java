package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/8
 */
@Getter
@ToString
public class Failure {

	private final boolean needRetry;

	private final int failureCode;

	private final String failureMessage;

	public Failure(boolean needRetry, int failureCode, String failureMessage) {
		this.needRetry = needRetry;
		this.failureCode = failureCode;
		this.failureMessage = failureMessage;
	}

	public Failure(FailureCodeEnum failureCodeEnum, Object... params) {
		Preconditions.checkNotNull(failureCodeEnum, "failureCodeEnum is empty");

		this.needRetry = true;
		this.failureCode = failureCodeEnum.getCode();
		this.failureMessage = failureCodeEnum.getMessage(params);
	}

	public Failure(boolean needRetry, FailureCodeEnum failureCodeEnum, Object... params) {
		Preconditions.checkNotNull(failureCodeEnum, "failureCodeEnum is empty");

		this.needRetry = needRetry;
		this.failureCode = failureCodeEnum.getCode();
		this.failureMessage = failureCodeEnum.getMessage(params);
	}
}
