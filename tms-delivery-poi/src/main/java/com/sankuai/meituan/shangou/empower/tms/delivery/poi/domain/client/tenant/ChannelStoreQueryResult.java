package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant;

import java.util.ArrayList;
import java.util.List;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;

import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class ChannelStoreQueryResult {

    /**
     * 是否成功
     */
    private final boolean success;
    /**
     * 失败错误信息
     */
    private final Failure failure;

    private List<TenantChannelStoreInfo> storeInfos;

    public ChannelStoreQueryResult(List<TenantChannelStoreInfo> storeInfos) {
        Preconditions.checkNotNull(storeInfos, "storeInfos is null");
        this.success = true;
        this.failure = null;
        this.storeInfos = storeInfos;
    }

    public ChannelStoreQueryResult(Failure failure) {
        Preconditions.checkNotNull(failure, "failure is null");
        this.success = false;
        this.failure = failure;
        this.storeInfos = new ArrayList<>();
    }
}
