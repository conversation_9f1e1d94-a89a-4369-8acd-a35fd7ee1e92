package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.servicepackage.FengNiaoServicePackage;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.servicepackage.HaiKuiServicePackage;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.servicepackage.ServicePackage;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 三方配送渠道枚举
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
@SuppressWarnings("SpellCheckingInspection")
@Slf4j
public enum ThirdDeliveryChannelEnum {

	/**
	 * 三方配送: 海葵(原美团同城配)
	 */
	HAI_KUI_DELIVERY(900, "美团同城配", DeliveryChannelEnum.HAI_KUI_DELIVERY, HaiKuiServicePackage::getAllServicePackages),

	/**
	 * 三方配送: 蜂鸟配送
	 */
	FENG_NIAO_DELIVERY(800, "蜂鸟即配", DeliveryChannelEnum.FENG_NIAO_DELIVERY, FengNiaoServicePackage::getAllServicePackages);

	private static final Map<DeliveryChannelEnum, ThirdDeliveryChannelEnum> DELIVERY_CHANNEL_ENUM_MAP = new HashMap<>();
	private static final Map<Integer, ThirdDeliveryChannelEnum> CODE_ENUM_MAP = new HashMap<>();

	private final int code;
	private final String name;
	private final DeliveryChannelEnum channel;
	private final Supplier<List<? extends ServicePackage>> servicePackagesSupplier;

	static {
		for (ThirdDeliveryChannelEnum each : values()) {
			DELIVERY_CHANNEL_ENUM_MAP.put(each.getChannel(), each);
			CODE_ENUM_MAP.put(each.getCode(), each);
		}
	}

	ThirdDeliveryChannelEnum(int code, String name, DeliveryChannelEnum channel, Supplier<List<? extends ServicePackage>> servicePackagesSupplier) {
		this.code = code;
		this.name = name;
		this.channel = channel;
		this.servicePackagesSupplier = servicePackagesSupplier;
	}

	public List<String> getOrderedServicePackageCodes() {
		return servicePackagesSupplier.get()
				.stream()
				.sorted(Comparator.comparingInt(ServicePackage::getOrder))
				.map(ServicePackage::getCode)
				.collect(Collectors.toList());
	}

	@JsonValue
	public int getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

	public DeliveryChannelEnum getChannel() {
		return channel;
	}

	public List<ServicePackage> getServicePackages() {
		return servicePackagesSupplier.get()
				.stream()
				.map(it -> (ServicePackage) it)
				.collect(Collectors.toList());
	}

	public boolean needSkipDeliveryCheckStep() {
		try {
			switch (this) {
				case HAI_KUI_DELIVERY:
					return ConfigUtilAdapter.getBoolean("delivery_skip_check_step_HAI_KUI", false);

				case FENG_NIAO_DELIVERY:
					return ConfigUtilAdapter.getBoolean("delivery_skip_check_step_FENG_NIAO", false);

				default:
					return false;
			}
		} catch (Exception e) {
			log.error("needSkipDeliveryCheckStep function run error, will return default false", e);
			return false;
		}
	}

	public static ThirdDeliveryChannelEnum valueOf(DeliveryChannelEnum channel) {
		return DELIVERY_CHANNEL_ENUM_MAP.get(channel);
	}

	@JsonCreator
	public static ThirdDeliveryChannelEnum valueOf(int code) {
		return CODE_ENUM_MAP.get(code);
	}
}
