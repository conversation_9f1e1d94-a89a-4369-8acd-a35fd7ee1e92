package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.merchant;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryIsShowItemNumberEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import lombok.Getter;
import lombok.ToString;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY;

/**
 * 商家自配送门店模型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/24
 */
@Getter
@ToString(callSuper = true)
public class MerchantSelfDeliveryPoi extends DeliveryPoi {

	public MerchantSelfDeliveryPoi(Long tenantId,
	                               Long storeId,
	                               Integer cityCode,
	                               String contactPhone,
	                               DeliveryLaunchPoint deliveryLaunchPoint,Integer channelType) {
		super(tenantId, storeId, cityCode, contactPhone, MERCHANT_SELF_DELIVERY, deliveryLaunchPoint, AUTO_LAUNCH_DELIVERY,channelType);
	}

	public MerchantSelfDeliveryPoi(DeliveryPoi sourceDeliveryPoi) {
		super(
				sourceDeliveryPoi.getTenantId(),
				sourceDeliveryPoi.getStoreId(),
				sourceDeliveryPoi.getCityCode(),
				sourceDeliveryPoi.getContactPhone(),
				MERCHANT_SELF_DELIVERY,
				sourceDeliveryPoi.getDeliveryLaunchPoint(),
				AUTO_LAUNCH_DELIVERY,
				sourceDeliveryPoi.getChannelType()
		);
	}

	public MerchantSelfDeliveryPoi(Long id,
	                               Long tenantId,
	                               Long storeId,
	                               Integer cityCode,
	                               String contactPhone,
	                               DeliveryLaunchPoint deliveryLaunchPoint,
								   Integer channelType,
								   DeliveryPlatformEnum lastDeliveryPlatform) {
		super(id, tenantId, storeId, cityCode, contactPhone, MERCHANT_SELF_DELIVERY, deliveryLaunchPoint, AUTO_LAUNCH_DELIVERY,channelType,lastDeliveryPlatform);
	}

	public MerchantSelfDeliveryPoi(Long id,
								   Long tenantId,
								   Long storeId,
								   Integer cityCode,
								   String contactPhone,
								   DeliveryLaunchPoint deliveryLaunchPoint,
								   Integer channelType,
								   DeliveryPlatformEnum lastDeliveryPlatform,
								   DeliveryIsShowItemNumberEnum deliveryIsShowItemNumberEnum) {
		super(id, tenantId, storeId, cityCode, contactPhone, MERCHANT_SELF_DELIVERY, deliveryLaunchPoint, AUTO_LAUNCH_DELIVERY,channelType,lastDeliveryPlatform,deliveryIsShowItemNumberEnum);
	}
}
