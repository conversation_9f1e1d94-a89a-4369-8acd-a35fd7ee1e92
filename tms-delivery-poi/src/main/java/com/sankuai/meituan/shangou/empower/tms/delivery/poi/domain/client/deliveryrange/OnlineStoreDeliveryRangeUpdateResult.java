package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.deliveryrange;

import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * 订单平台门店配送范围修改结果
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/8
 */
@Getter
@ToString
public class OnlineStoreDeliveryRangeUpdateResult {
	/**
	 * 配送是否发起成功
	 */
	private final boolean success;
	/**
	 * 是否需要重试(网络超时/系统异常等场景需要重试)
	 */
	private final boolean needRetry;

	private final String errorMsg;

	public OnlineStoreDeliveryRangeUpdateResult() {
		this.success = true;
		this.needRetry = false;
		this.errorMsg = StringUtils.EMPTY;
	}

	public OnlineStoreDeliveryRangeUpdateResult(boolean needRetry, String errorMsg) {
		this.success = false;
		this.needRetry = needRetry;
		this.errorMsg = errorMsg;
	}
}
