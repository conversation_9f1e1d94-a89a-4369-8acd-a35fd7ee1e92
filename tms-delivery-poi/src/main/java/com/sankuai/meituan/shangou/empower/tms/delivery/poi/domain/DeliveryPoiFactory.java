package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.aggr.AggrDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.dap.DapDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.maltfarm.MaltFarmDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.merchant.MerchantSelfDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.orderchannel.OrderChannelDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Objects;
import java.util.Optional;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/24
 */
@Component
public class DeliveryPoiFactory {

	@Resource
	private TenantSystemClient tenantSystemClient;

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public DeliveryPoi createDefaultDeliveryPoi(DeliveryPlatformEnum deliveryPlatform, Long tenantId, Long storeId,Integer channelType) {
		Preconditions.checkNotNull(deliveryPlatform, "deliveryPlatform is null");
		Preconditions.checkNotNull(tenantId, "tenantId is null");
		Preconditions.checkNotNull(storeId, "storeId is null");

		TenantStoreInfo storeInfo = tenantSystemClient.queryStoreDetailInfo(tenantId, storeId);
		Integer cityCode = Optional.ofNullable(storeInfo).map(TenantStoreInfo::getCityCode).orElse(null);
		String contactPhone = Optional.ofNullable(storeInfo).map(TenantStoreInfo::getPhone).orElse(null);

		switch (deliveryPlatform) {
			case SELF_BUILT_DELIVERY_PLATFORM:
				return new SelfBuiltDeliveryPoi(tenantId, storeId, cityCode, contactPhone, new DeliveryLaunchPoint(), new HashMap<>(), new HashMap<>(),channelType);

			case AGGREGATION_DELIVERY_PLATFORM:
				return new AggrDeliveryPoi(tenantId, storeId, cityCode, contactPhone, new DeliveryLaunchPoint(), AUTO_LAUNCH_DELIVERY, AggrDeliveryPoi.LOWEST_PRICE_FIRST,channelType);

			case MALT_FARM_DELIVERY_PLATFORM:
				return new MaltFarmDeliveryPoi(tenantId, storeId, cityCode, contactPhone, new DeliveryLaunchPoint(),channelType);

			case MERCHANT_SELF_DELIVERY:
				return new MerchantSelfDeliveryPoi(tenantId, storeId, cityCode, contactPhone, new DeliveryLaunchPoint(),channelType);
			case DAP_DELIVERY_PLATFORM:
				return new DapDeliveryPoi(tenantId, storeId, cityCode, contactPhone, new DeliveryLaunchPoint(),channelType);
			case ORDER_CHANNEL_DELIVERY_PLATFORM:
				return new OrderChannelDeliveryPoi(tenantId, storeId, cityCode, contactPhone, new DeliveryLaunchPoint(), channelType);
			default:
				throw new IllegalStateException("Unexpected value: " + deliveryPlatform);
		}
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public DeliveryPoi createNewPlatformPoiByExisting(DeliveryPoi sourceDeliveryPoi, DeliveryPlatformEnum newDeliveryPlatform) {
		if(sourceDeliveryPoi.getDeliveryPlatform() == newDeliveryPlatform){
			return sourceDeliveryPoi;
		}

		DeliveryPoi deliveryPoi;
		switch (newDeliveryPlatform) {
			case SELF_BUILT_DELIVERY_PLATFORM:
				deliveryPoi = new SelfBuiltDeliveryPoi(sourceDeliveryPoi);
				break;

			case AGGREGATION_DELIVERY_PLATFORM:
				deliveryPoi = new AggrDeliveryPoi(sourceDeliveryPoi);
				break;

			case MALT_FARM_DELIVERY_PLATFORM:
				deliveryPoi = new MaltFarmDeliveryPoi(sourceDeliveryPoi);
				break;

			case MERCHANT_SELF_DELIVERY:
				deliveryPoi = new MerchantSelfDeliveryPoi(sourceDeliveryPoi);
				break;
			case DAP_DELIVERY_PLATFORM:
				deliveryPoi = new DapDeliveryPoi(sourceDeliveryPoi);
				break;
			case ORDER_CHANNEL_DELIVERY_PLATFORM:
				deliveryPoi = new OrderChannelDeliveryPoi(sourceDeliveryPoi);
				break;
			default:
				throw new IllegalStateException("Unexpected value: " + newDeliveryPlatform);
		}

		deliveryPoi.setId(sourceDeliveryPoi.getId());
		deliveryPoi.setLastDeliveryPlatform(sourceDeliveryPoi.getDeliveryPlatform());

		if (needMakeUpStoreInfo(sourceDeliveryPoi)) {
			makeUpStoreInfo(sourceDeliveryPoi, deliveryPoi);
		}

		return deliveryPoi;
	}

	private boolean needMakeUpStoreInfo(DeliveryPoi sourceDeliveryPoi) {
		return Objects.isNull(sourceDeliveryPoi.getCityCode()) || StringUtils.isEmpty(sourceDeliveryPoi.getContactPhone());
	}

	private void makeUpStoreInfo(DeliveryPoi sourceDeliveryPoi, DeliveryPoi deliveryPoi) {
		TenantStoreInfo storeInfo = tenantSystemClient.queryStoreDetailInfo(sourceDeliveryPoi.getTenantId(), sourceDeliveryPoi.getStoreId());
		deliveryPoi.setCityCode(Optional.ofNullable(storeInfo).map(TenantStoreInfo::getCityCode).orElse(sourceDeliveryPoi.getCityCode()));
		deliveryPoi.setContactPhone(Optional.ofNullable(storeInfo).map(TenantStoreInfo::getPhone).orElse(sourceDeliveryPoi.getContactPhone()));
	}
}
