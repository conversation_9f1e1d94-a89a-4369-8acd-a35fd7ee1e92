package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.aggr;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryIsShowItemNumberEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM;


/**
 * 聚合运力配送门店模型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/24
 */
@Getter
@ToString(callSuper = true)
public class AggrDeliveryPoi extends DeliveryPoi {

	public static final int LOWEST_PRICE_FIRST = 1;

	/**
	 * 聚合运力自动发配送策略id
	 * 低价优先:1
	 */
	@Setter
	private Integer autoLaunchStrategyId;

	public AggrDeliveryPoi(Long tenantId,
	                       Long storeId,
	                       Integer cityCode,
	                       String contactPhone,
	                       DeliveryLaunchPoint deliveryLaunchPoint,
	                       DeliveryLaunchTypeEnum deliveryLaunchType,
	                       Integer autoLaunchStrategyId,
						   Integer channelType) {
		super(tenantId, storeId, cityCode, contactPhone, AGGREGATION_DELIVERY_PLATFORM, deliveryLaunchPoint, deliveryLaunchType,channelType);
		this.autoLaunchStrategyId = autoLaunchStrategyId;
	}

	public AggrDeliveryPoi(DeliveryPoi sourceDeliveryPoi) {
		super(
				sourceDeliveryPoi.getTenantId(),
				sourceDeliveryPoi.getStoreId(),
				sourceDeliveryPoi.getCityCode(),
				sourceDeliveryPoi.getContactPhone(),
				AGGREGATION_DELIVERY_PLATFORM,
				sourceDeliveryPoi.getDeliveryLaunchPoint(),
				sourceDeliveryPoi.getDeliveryLaunchType(),
				sourceDeliveryPoi.getChannelType()
		);
		this.autoLaunchStrategyId = LOWEST_PRICE_FIRST;
	}

	public AggrDeliveryPoi(Long id,
	                       Long tenantId,
	                       Long storeId,
	                       Integer cityCode,
	                       String contactPhone,
	                       DeliveryLaunchPoint deliveryLaunchPoint,
	                       DeliveryLaunchTypeEnum deliveryLaunchType,
	                       Integer autoLaunchStrategyId,
						   Integer channelType,
						   DeliveryPlatformEnum lastDeliveryPlatform) {
		super(id, tenantId, storeId, cityCode, contactPhone, AGGREGATION_DELIVERY_PLATFORM, deliveryLaunchPoint, deliveryLaunchType,channelType,lastDeliveryPlatform);
		this.autoLaunchStrategyId = autoLaunchStrategyId;
	}

	public AggrDeliveryPoi(Long id,
						   Long tenantId,
						   Long storeId,
						   Integer cityCode,
						   String contactPhone,
						   DeliveryLaunchPoint deliveryLaunchPoint,
						   DeliveryLaunchTypeEnum deliveryLaunchType,
						   Integer autoLaunchStrategyId,
						   Integer channelType,
						   DeliveryPlatformEnum lastDeliveryPlatform,
						   DeliveryIsShowItemNumberEnum deliveryIsShowItemNumberEnum) {
		super(id, tenantId, storeId, cityCode, contactPhone, AGGREGATION_DELIVERY_PLATFORM, deliveryLaunchPoint, deliveryLaunchType,channelType,lastDeliveryPlatform,deliveryIsShowItemNumberEnum);
		this.autoLaunchStrategyId = autoLaunchStrategyId;
	}
}
