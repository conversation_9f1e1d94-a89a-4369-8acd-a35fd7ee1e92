package com.sankuai.meituan.shangou.empower.tms.delivery.poi.application;

import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableSet;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.TenantDeliveryConfigUpdateCmd.TenantChannelDeliveryConfigUpdateCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.SyncDeliveryRangeDomainService;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ChannelTenantConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.DeliveryTenantRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 配送门店应用服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/8
 */
@Slf4j
@Service
public class DeliveryStoreApplicationService {

	private static final Set<Integer> DELIVERY_RANGE_SUPPORTED_CHANNEL_IDS = ImmutableSet.of(100);

	@Resource
	private DeliveryTenantRepository deliveryTenantRepository;
	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;
	@Resource
	private SyncDeliveryRangeDomainService syncDeliveryRangeDomainService;

	/**
	 * 同步门店配送范围
	 * 从该门店的所有签约配送渠道处查询配送范围，并同步到线上订单平台
	 *
	 * @return 是否操作成功
	 */
	@CatTransaction
	public Map<Integer, Failure> syncDeliveryRange(DeliveryRangeSyncCmd cmd) {
		log.info("DeliveryStoreApplicationService.syncDeliveryRange begin, param[DeliveryRangeSyncCmd={}]", cmd);
		Preconditions.checkNotNull(cmd, "cmd is null");

		Map<Integer, Failure> failureMap = new HashMap<>();

		if (MapUtils.isEmpty(cmd.getOrderPlatformDeliveryConfigMap())) {
			log.warn("No available sync online channel, will fast break this syncing");
			return failureMap;
		}

		if(cmd.getDeliveryPoi().getDeliveryPlatform() != DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM){
			log.warn("当前平台[{}]暂不支持同步配送范围, 将直接返回成功", cmd.getDeliveryPoi().getDeliveryPlatform());
			return failureMap;
		}
		SelfBuiltDeliveryPoi deliveryPoi = (SelfBuiltDeliveryPoi)cmd.getDeliveryPoi();

		Set<Integer> needSyncChannelIds = new HashSet<>(cmd.getOrderPlatformDeliveryConfigMap().keySet());
		needSyncChannelIds.retainAll(DELIVERY_RANGE_SUPPORTED_CHANNEL_IDS);

		//存储更新起送价等相关配置
		cmd.getOrderPlatformDeliveryConfigMap()
				.entrySet()
				.stream()
				.filter(entry -> Objects.nonNull(entry.getValue()))
				.filter(entry -> needSyncChannelIds.contains(entry.getKey()))
				.forEach(entry -> deliveryPoi.getOrderPlatformDeliveryConfigMap().put(entry.getKey(), entry.getValue()));
		deliveryPoiRepository.saveDeliveryPoi(deliveryPoi);

		//执行同步
		failureMap.putAll(syncDeliveryRangeDomainService.syncDeliveryRange(deliveryPoi, needSyncChannelIds));

		log.info("DeliveryStoreApplicationService.syncDeliveryRange finish, failureMap={}", failureMap);
		return failureMap;
	}

	public Optional<Failure> updateTenantDeliveryConfig(TenantDeliveryConfigUpdateCmd cmd) {
		Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> existingChannelTenantMap =
				deliveryTenantRepository.getChannelTenants(cmd.getTenantId(), false);
		log.info("DeliveryConfigService.modifyDeliveryConfigs 租户{}已存在配送配置列表{}", cmd.getTenantId(), existingChannelTenantMap);

		//区分创建还是更新的配送配置
		List<ChannelTenantConfig> saveList = new ArrayList<>();
		for (TenantChannelDeliveryConfigUpdateCmd each : cmd.getChannelDeliveryConfigs()) {
			//当为启用状态时，需校验appKey是否已经被其他租户占用
			if (each.isEnabled()) {
				Long appKeyTenantId = deliveryTenantRepository.getTenantIdByChannelAndAppKey(each.getDeliveryChannel(), each.getAppKey());
				if (appKeyTenantId != null && !appKeyTenantId.equals(cmd.getTenantId())) {
					return Optional.of(new Failure(false, FailureCodeEnum.APP_KEY_CONFIG_ALREADY_ERROR, each.getAppKey()));
				}
			}

			if (existingChannelTenantMap.containsKey(each.getDeliveryChannel())) {
				saveList.add(new ChannelTenantConfig(
						existingChannelTenantMap.get(each.getDeliveryChannel()).getId(),
						each.getDeliveryChannel(),
						each.getAppKey(),
						each.getSecret(),
						each.isEnabled()
				));
			} else {
				saveList.add(new ChannelTenantConfig(
						null,
						each.getDeliveryChannel(),
						StringUtils.defaultString(each.getAppKey()),
						StringUtils.defaultString(each.getSecret()),
						each.isEnabled()
				));
			}
		}

		deliveryTenantRepository.saveChannelTenants(cmd.getTenantId(), saveList, cmd.getOperator());
		return Optional.empty();
	}
}
