package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 配送渠道门店模型
 * 包含门店与配送渠道的配置信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
@Getter
@ToString
@AllArgsConstructor
public class ChannelStoreConfig {

	@Setter
	private Long id;

	/**
	 * 赋能门店id
	 */
	private final Long storeId;

	/**
	 * 配送渠道
	 */
	private final ThirdDeliveryChannelEnum deliveryChannel;

	/**
	 * 配送渠道侧的门店id
	 */
	private final String deliveryChannelStoreId;

	/**
	 * 配送渠道侧的门店名称
	 */
	private final String deliveryChannelStoreName;

	/**
	 * 签约服务包集合
	 */
	private final List<String> servicePackageCodes;

	/**
	 * 是否启用
	 */
	private final boolean enabled;

	/**
	 * 获取排序后的服务包列表
	 */
	public List<String> getOrderedServicePackageCodes() {
		return getDeliveryChannel().getOrderedServicePackageCodes()
				.stream()
				.filter(it -> getServicePackageCodes().contains(it))
				.collect(Collectors.toList());
	}
}
