package com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.remind;

import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfigFactory;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfigRepository;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;

import lombok.extern.slf4j.Slf4j;

/**
 * 配送提醒配置的操作 Application 服务.
 *
 * <AUTHOR>
 * @since 2021/10/8 19:22
 */
@Slf4j
@Service
public class DeliveryRemindConfigOpeAppService {

    @Resource
    private DeliveryRemindConfigRepository deliveryRemindConfigRepository;
    @Resource
    private DeliveryRemindConfigFactory deliveryRemindConfigFactory;
    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    /**
     * 保存门店配送提醒配置.
     *
     * @param tenantId   租户 ID
     * @param storeId    门店 ID
     * @param recipients 提醒接收人
     */
    @CatTransaction
    // @MethodLog(logRequest = false, logResponse = true)
    public void saveStoreDeliveryRemindConfig(long tenantId, long storeId, List<String> recipients, List<String> dhRecipients) {
        // 1. 校验门店的配送配置已存在
        Optional<DeliveryPoi> deliveryPoi = deliveryPoiRepository.queryDeliveryPoi(tenantId, storeId);
        if (!deliveryPoi.isPresent()) {
            throw new BizException("门店暂未配置配送，无法进行配送提醒配置");
        }
        // 2. 查看门店现有配送提醒配置
        Optional<DeliveryRemindConfig> configOptional = deliveryRemindConfigRepository.queryDeliveryRemindConfig(tenantId, storeId);
        DeliveryRemindConfig deliveryRemindConfig = null;
        if (configOptional.isPresent()) {
            deliveryRemindConfig = configOptional.get();
            log.info("门店(tenantId:{}, storeId:{}) 修改配送提醒接收人，修改前：{}，修改后：{}，歪马修改前：{}，歪马修改后：{}",
                    tenantId, storeId, deliveryRemindConfig.getRecipients(), recipients,
                    deliveryRemindConfig.getDhRecipients(), dhRecipients);
            deliveryRemindConfig.changeRecipients(recipients);
            deliveryRemindConfig.changeDhRecipients(dhRecipients);
        } else {
            log.info("门店(tenantId:{}, storeId:{}) 新增配送提醒，提醒接收人：{}，歪马提醒接收人:{}", tenantId, storeId, recipients, dhRecipients);
            deliveryRemindConfig = deliveryRemindConfigFactory.createDeliveryRemindConfig(tenantId, storeId, recipients, dhRecipients);
        }
        deliveryRemindConfigRepository.saveDeliveryRemindConfig(deliveryRemindConfig);
        return;
    }
}
