package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 配送渠道租户模型
 * 包含租户与配送渠道的配置信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
@Getter
@ToString
@AllArgsConstructor
public class ChannelTenantConfig {

	private final Long id;
	/**
	 * 配送渠道
	 */
	private final ThirdDeliveryChannelEnum deliveryChannel;

	/**
	 * 应用key
	 */
	private final String appKey;

	/**
	 * 密钥key
	 */
	private final String secretKey;

	/**
	 * 是否启用
	 */
	private final boolean enabled;
}
