package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

/**
 * @Description: 门店配置操作类型
 * @Author: zhangjian155
 * @Date: 2022/10/12 14:51
 */
public enum StoreConfigOpTypeEnum {

    /**
     * 创建
     */
    CREATE(10),

    /**
     * 修改
    */
    MODIFY(20),

    /**
     * 删除
     */
    DELETE(30);

    private final int value;

    StoreConfigOpTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static StoreConfigOpTypeEnum findByValue(int value) {
        for (StoreConfigOpTypeEnum each : values()) {
            if (each.getValue() == value) {
                return each;
            }
        }
        return null;
    }
}
