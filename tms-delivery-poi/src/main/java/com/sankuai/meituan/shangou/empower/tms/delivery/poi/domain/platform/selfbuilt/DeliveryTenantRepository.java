package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt;


import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;

import java.util.List;
import java.util.Map;

/**
 * 配送租户仓储服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
public interface DeliveryTenantRepository {

	/**
	 * 通过租户id查询配送租户信息
	 */
	DeliveryTenant getValidDeliveryTenant(Long tenantId);

	/**
	 * 通过租户id查询所有渠道租户配置
	 */
	Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> getChannelTenants(Long tenantId, boolean onlyEnabled);

	/**
	 * 通过租户和渠道查询配送租户配置信息
	 */
	ChannelTenantConfig getDeliveryChannelTenant(Long tenantId, ThirdDeliveryChannelEnum deliveryChannel, boolean onlyEnabled);

	/**
	 * 通过渠道与appKey查询租户信息
	 */
	Long getTenantIdByChannelAndAppKey(ThirdDeliveryChannelEnum deliveryChannel, String appKey);

	/**
	 * 保存配送租户
	 */
	void saveChannelTenants(Long tenantId, List<ChannelTenantConfig> channelTenantList, Operator operator);


}
