package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 确认送达操作配置
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryCompleteConfig {

    /**
     * 距离提醒：0-不提示 1-提示
     */
    private Integer distanceReminder;

    /**
     * 送达完成配置详情
     */
    private DeliveryCompleteDetail deliveryCompleteConfig;

    /**
     * 送达完成配置详情
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeliveryCompleteDetail {

        /**
         * 经营类型：1-直营 2-加盟 3-赋能
         */
        private OperationModeEnum operationMode;

        /**
         * 所有示例图片信息列表
         */
        private List<ExamplePicInfo> allExamplePicInfoList;

        /**
         * 特殊商品上传图片配置
         */
        private List<SpecialProductUploadPicConfig> specialProductUploadPicConfig;

        /**
         * 发送图片给顾客提示文案
         */
        private String sendPicToCustomerTips;

        /**
         * 未联系顾客提示文案
         */
        private String notContactCustomerTips;

        /**
         * 最大上传时间阈值（秒）
         */
        private Long uploadImageDurationThreshold;

        /**
         * 是否展示未联系顾客提示（赋能无法使用）
         */
        private Boolean isShowNotContactCustomerTips;
    }

    /**
     * 示例图片信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExamplePicInfo {

        /**
         * 图片类型
         */
        private Integer type;

        /**
         * 图片名称
         */
        private String name;

        /**
         * 图片链接
         */
        private String picUrl;

        /**
         * 排序顺序
         */
        private Integer order;
    }

    /**
     * 特殊商品上传图片配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SpecialProductUploadPicConfig {

        /**
         * 商品类型（封签/高价）
         */
        private String productType;

        /**
         * 图片类型列表
         */
        private List<Integer> picTypeList;

        /**
         * 是否强制上传图片
         */
        private Boolean isForceUploadPic;

        /**
         * 需要上传的照片张数
         */
        private Integer needUploadPicCount;
    }
}

