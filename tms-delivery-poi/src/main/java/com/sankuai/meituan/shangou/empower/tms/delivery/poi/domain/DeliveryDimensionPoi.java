package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/7/02
 */
@Getter
@ToString
@AllArgsConstructor
public class DeliveryDimensionPoi {

	/** 主键ID */
	private Long id;

	/** 租户ID */
	private Long tenantId;

	/** 门店ID */
	private Long storeId;

	/** 自送拣配作业模式 */
	private SelfDeliveryModeEnum selfDeliveryMode;

	/** 跳转导航模式 */
	private InternalNavigationModeEnum internalNavigationMode;

	/** 剩余时长配置*/
	private String assessTimeConfig;

	/** 确认送达操作配置 */
	private DeliveryCompleteMode deliveryCompleteMode;

	/** 配送转骑手范围(角色id列表) */
	private List<Long> riderTransRoles;

	/** 已送达列表排序模式 */
	private CompletedSortModeEnum completedSortMode;

	/** 配送提醒设置 */
	private DeliveryRemindConfig deliveryRemindConfig;

	/** 创建时间 */
	private LocalDateTime createdAt;

	/** 更新时间 */
	private LocalDateTime updatedAt;


}
