package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.util.Map;

/**
 * 配送租户模型
 * 主体为赋能租户，加上其对接的三方配送渠道的配置信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
@Getter
@ToString
@EqualsAndHashCode(of = "tenantId")
@AllArgsConstructor
public class DeliveryTenant {

	/**
	 * 租户ID
	 */
	private final Long tenantId;

	/**
	 * 配送渠道租户列表，包含租户与对应渠道的相关配置信息
	 */
	private final Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> channelTenantConfigMap;
}
