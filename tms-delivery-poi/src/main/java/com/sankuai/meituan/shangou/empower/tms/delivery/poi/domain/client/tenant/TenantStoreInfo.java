package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by liuyonggao on 2021/1/7.
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantStoreInfo {

    private Integer cityCode;

    private String phone;

    private String storeName;

    private Integer areaCode;

    private String address;

    private Integer entityType;

    private Integer shippingMode;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    private String servicePhone;
}
