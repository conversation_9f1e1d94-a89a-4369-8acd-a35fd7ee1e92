package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.dap;

import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryIsShowItemNumberEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM;

@Getter
@ToString(callSuper = true)
public class DapDeliveryPoi extends DeliveryPoi {

    @Setter
    private Address storeAddress;


    public DapDeliveryPoi(Long tenantId,
                               Long storeId,
                               Integer cityCode,
                               String contactPhone,
                               DeliveryLaunchPoint deliveryLaunchPoint, Integer channelType) {
        super(tenantId, storeId, cityCode, contactPhone, DAP_DELIVERY_PLATFORM, deliveryLaunchPoint, AUTO_LAUNCH_DELIVERY,channelType);
    }

    public DapDeliveryPoi(DeliveryPoi sourceDeliveryPoi) {
        super(
                sourceDeliveryPoi.getTenantId(),
                sourceDeliveryPoi.getStoreId(),
                sourceDeliveryPoi.getCityCode(),
                sourceDeliveryPoi.getContactPhone(),
                DAP_DELIVERY_PLATFORM,
                sourceDeliveryPoi.getDeliveryLaunchPoint(),
                AUTO_LAUNCH_DELIVERY,
                sourceDeliveryPoi.getChannelType()
        );
    }

    public DapDeliveryPoi(Long id,
                               Long tenantId,
                               Long storeId,
                               Integer cityCode,
                               String contactPhone,
                               DeliveryLaunchPoint deliveryLaunchPoint,
                               Address storeAddress,
                               Integer channelType,
                          DeliveryPlatformEnum lastDeliveryType) {
        super(id, tenantId, storeId, cityCode, contactPhone, DAP_DELIVERY_PLATFORM, deliveryLaunchPoint, AUTO_LAUNCH_DELIVERY,channelType,lastDeliveryType);
        this.storeAddress = storeAddress;
    }

    public DapDeliveryPoi(Long id,
                          Long tenantId,
                          Long storeId,
                          Integer cityCode,
                          String contactPhone,
                          DeliveryLaunchPoint deliveryLaunchPoint,
                          Address storeAddress,
                          Integer channelType,
                          DeliveryPlatformEnum lastDeliveryType,
                          DeliveryIsShowItemNumberEnum deliveryIsShowItemNumberEnum ) {
        super(id, tenantId, storeId, cityCode, contactPhone, DAP_DELIVERY_PLATFORM, deliveryLaunchPoint, AUTO_LAUNCH_DELIVERY,channelType,lastDeliveryType,deliveryIsShowItemNumberEnum);
        this.storeAddress = storeAddress;
    }
}
