package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.relation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by l<PERSON>yongg<PERSON> on 2021/1/6.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryChannelInfo {


    private Integer deliveryChannelId;

    private String deliveryChannelName;

    //发送同步时才有该字段
    private Integer syncType;

    private String deliveryChannelMerchantId;
    private String deliveryChannelPoiId;

    /**
     * 绑定配送渠道门店所需的扩展信息，JSON 字符串
     */
    private String deliveryChannelPoiExt;

    private Long storeId;
    /**
     * 当前渠道开通状态
     */
    private Integer openFlag;
}
