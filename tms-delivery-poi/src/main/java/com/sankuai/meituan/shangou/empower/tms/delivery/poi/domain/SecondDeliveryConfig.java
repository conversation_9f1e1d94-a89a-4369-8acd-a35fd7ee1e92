package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-16
 * @email <EMAIL>
 */
@Getter
@EqualsAndHashCode
@AllArgsConstructor
public class SecondDeliveryConfig {

    @FieldDoc(description = "二级平台编码")
    private List<Integer> secondPlatformCodeCode;

    @FieldDoc(description = "禁用条件")
    private ForbiddenConditionVO forbiddenCondition;


    /**
     * 禁用条件
     */
    @Data
    public static class ForbiddenConditionVO {
        @FieldDoc(description = "订单标签")
        private List<Long> orderTags;

        @FieldDoc(description = "订单实际支付金额")
        private BigDecimal orderActualPayment;
    }

}
