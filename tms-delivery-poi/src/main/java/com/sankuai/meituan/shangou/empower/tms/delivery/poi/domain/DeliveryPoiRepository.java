package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 配送门店仓储服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/15
 */
public interface DeliveryPoiRepository {

	/**
	 * 查询配送门店信息(美团渠道)
	 */
	Optional<DeliveryPoi> queryDeliveryPoi(Long tenantId, Long storeId);

	/**
	 * 查询配送门店信息
	 */
	Optional<DeliveryPoi> queryDeliveryPoiWithChannelType(Long tenantId, Long storeId,Integer channelType);

	/**
	 * 查询配送门店信息，如果无该channelType的数据则以美团渠道的返回
	 * @param tenantId
	 * @param storeId
	 * @param channelType
	 * @return
	 */
	Optional<DeliveryPoi> queryDeliveryPoiWithDefault(Long tenantId, Long storeId,Integer channelType);

	/**
	 * 查询配送门店信息（所有渠道）
	 */
	List<DeliveryPoi> queryAllDeliveryPoi(Long tenantId, Long storeId);

	/**
	 * @description: 查询聚合配送门店信息（所有渠道）,会用M端的租户渠道过滤
	 * @param: tenantId 租户ID
	 * @param: storeId 门店ID
	 * @return 聚合配送门店信息
	*/
	List<DeliveryPoi> queryAggDeliveryPoi(Long tenantId, Long storeId);

	/**
	 * @description: 查询门店聚合配送设置渠道展示顺序
	 * @return 渠道展示顺序
	*/
	List<Integer> queryChannelOrder();

	/**
	 * @description: 批量查询store_config表，不做兼容补偿的逻辑，直接返回db里的数据
	 * @param: tenantId
	 * @param: storeIdList
	 * @return 租户下门店配送配置
	*/
	List<DeliveryPoi> batchQueryDeliveryPoi(Long tenantId, List<Long> storeIdList);

	/**
	 * @description: 批量查询store_config表，仅查询DB
	 * @param: tenantId
	 * @param: storeIdList
	 * @return 租户下门店配送配置
	 */
	List<DeliveryPoi> batchQueryDeliveryPoiList(Long tenantId, List<Long> storeIdList);

	/**
	 * 根据配送商+配送商门店id查询自建平台配送门店
	 */
	Optional<SelfBuiltDeliveryPoi> querySelfBuiltDeliveryPoi(ThirdDeliveryChannelEnum deliveryChannel, String channelStoreCode);

	/**
	 * 保存配送门店信息
	 */
	void saveDeliveryPoi(DeliveryPoi deliveryPoi);

	void batchSaveDeliveryPoi(List<DeliveryPoi> deliveryPoiList);

	void batchUpdateDeliveryPoi(List<DeliveryPoi> deliveryPoiList);

	void batchSaveStoreConfigOpLog(List<StoreConfigOpLog> storeConfigOpLogList);

	List<StoreConfigOpLog> queryStoreConfigOpLogByCondition(Long storeConfigId, LocalDateTime startTime, LocalDateTime endTime);

	/**
	 * 保存配送门店信息
	 */
	int updateStoreConfigByPrimaryKeySelective(StoreConfig storeConfig);

}
