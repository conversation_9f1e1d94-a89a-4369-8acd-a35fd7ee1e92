package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 租户系统客户端
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/10
 */
public interface TenantSystemClient {

	/**
	 * 查询赋能门店开通的线上渠道
	 */
	StoreOpeningOnlineChannelsQueryResult queryStoreOpeningOnlineChannels(Long tenantId, Long storeId);


	/**
	 * 查询门店的详细信息
	 */
	TenantStoreInfo queryStoreDetailInfo(Long tenantId, Long storeId);

	/**
	 * 查询渠道门店信息
	 */
	ChannelStoreQueryResult queryChannelStoreDetailInfo(Long tenantId, Long storeId);

	Map<Long, List<TenantChannelStoreInfo>> queryChannelStoreDetailInfoList(Long tenantId, List<Long> storeIds);

	TenantInfo queryTenantInfo(Long tenantId);

	Optional<TenantChannelStoreInfo> queryChannelStoreDetailInfoWithAnyChannel(Long tenantId, Long storeId, Integer channelType);

	List<PoiBaseInfo> queryTenantStoreList(Long tenantId);

    /**
     * 查询共享前置仓关联的门店 poi id
     * 
     * @param tenantId
     * @param poiIdList
     * @param reverseRelation true 正向关系（根据共享仓id查询关联的前置仓门店）；
     *            false 逆向关系（根据前置仓门店id查询其对应的共享仓id）
     * @return
     */
    Map<Long, List<Long>> batchQueryRelationMapByPoiIds(Long tenantId, List<Long> poiIdList, boolean reverseRelation);

    String queryChannelPoiIdByPoiId(Long tenantId, Long poiId, Integer channelType);

	List<String> queryTenantCategory(Long tenantId);
}
