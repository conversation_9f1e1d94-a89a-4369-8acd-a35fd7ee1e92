package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.deliveryrange;

import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.DeliveryRange;
import lombok.Getter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * 配送范围查询结果
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/8
 */
@Getter
@ToString
public class DeliveryRangeQueryResult {

	/**
	 * 配送是否发起成功
	 */
	private final boolean success;
	/**
	 * 是否需要重试(网络超时/系统异常等场景需要重试)
	 */
	private final boolean needRetry;
	/**
	 * 门店配送范围
	 */
	private final List<DeliveryRange> deliveryRangeList;

	public DeliveryRangeQueryResult(boolean needRetry) {
		this.success = false;
		this.needRetry = needRetry;
		this.deliveryRangeList = new ArrayList<>();
	}

	public DeliveryRangeQueryResult(List<DeliveryRange> deliveryRangeList) {
		this.success = true;
		this.needRetry = false;
		this.deliveryRangeList = deliveryRangeList;
	}
}
