package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 经营类型枚举
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@Getter
@AllArgsConstructor
public enum OperationModeEnum {

    /**
     * 直营
     */
    DIRECT_OPERATION(1, "直营"),

    /**
     * 加盟
     */
    FRANCHISE(2, "加盟"),

    /**
     * 赋能
     */
    EMPOWERMENT(3, "赋能");

    /**
     * 经营类型值
     */
    private final Integer mode;

    /**
     * 经营类型描述
     */
    private final String description;

    /**
     * 根据模式值获取枚举
     *
     * @param mode 模式值
     * @return 对应的枚举
     * @throws IllegalArgumentException 当模式值不存在时抛出异常
     */
    public static OperationModeEnum getByMode(Integer mode) {
        if (mode == null) {
            return null;
        }
        for (OperationModeEnum modeEnum : values()) {
            if (modeEnum.getMode().equals(mode)) {
                return modeEnum;
            }
        }
        throw new IllegalArgumentException("未知的经营类型: " + mode);
    }
}

