package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import lombok.Getter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * 门店开通的线上渠道查询结果
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/8
 */
@Getter
@ToString
public class StoreOpeningOnlineChannelsQueryResult {

	/**
	 * 配送是否发起成功
	 */
	private final boolean success;
	/**
	 * 失败错误信息
	 */
	private final Failure failure;
	/**
	 * 门店开通的线上渠道集合
	 *
	 * @see OrderBizTypeEnum
	 */
	private final List<Integer> channelIds;

	public StoreOpeningOnlineChannelsQueryResult(List<Integer> channelIds) {
		Preconditions.checkNotNull(channelIds, "channelIds is null");

		this.success = true;
		this.failure = null;
		this.channelIds = channelIds;
	}

	public StoreOpeningOnlineChannelsQueryResult(Failure failure) {
		Preconditions.checkNotNull(failure, "failure is null");

		this.success = false;
		this.failure = failure;
		this.channelIds = new ArrayList<>();
	}
}
