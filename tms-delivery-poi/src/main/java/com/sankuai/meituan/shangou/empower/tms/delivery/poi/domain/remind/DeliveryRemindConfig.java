package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * 配送提醒配置领域对象.
 *
 * <AUTHOR>
 * @since 2021/10/8 17:02
 */
@Slf4j
@Getter
@ToString
@AllArgsConstructor
public class DeliveryRemindConfig {

    /**
     * id
     */
    @Setter
    private Long id;

    /**
     * 租户 ID
     */
    private final Long tenantId;

    /**
     * 门店 ID
     */
    private final Long storeId;

    /**
     * 提醒接收人
     */
    private List<String> recipients;

    /**
     * 是否有效
     */
    private final Boolean isValid;

    /**
     * 创建时间
     */
    private final LocalDateTime createTime;

    /**
     * 歪马提醒接收人
     */
    private List<String> dhRecipients;

    public void changeRecipients(List<String> newRecipients) {
        this.recipients = newRecipients;
    }

    public void changeDhRecipients(List<String> dhRecipients) {
        this.dhRecipients = dhRecipients;
    }
}
