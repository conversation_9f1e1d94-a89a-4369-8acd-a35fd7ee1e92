package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.servicepackage;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 服务包模型定义
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/13
 */
@Getter
@ToString
@AllArgsConstructor
public class ServicePackage {
	/**
	 * 服务包编码
	 */
	private final String code;
	/**
	 * 服务包名称
	 */
	private final String name;
	/**
	 * 服务包优先级
	 * 数值越小，发起配送时越优先选择
	 */
	private final int order;
	/**
	 * 是否仅测试使用的服务包
	 */
	private final boolean testOnly;
}
