package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import lombok.Data;

/**
 * 剩余时长配置（歪马考核时长配置）
 * 注：此配置结构略微复杂，具体字段根据实际业务需求定义
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@Data
public class AssessTimeConfig {

    /**
     * 考核时长相关配置
     * 注：由于文档中提到"略微复杂"，此处预留扩展空间
     * 具体字段需要根据实际业务需求进一步定义
     */
    private Object assessTimeSettings;

    // TODO: 根据实际业务需求，补充具体的配置字段
    // 例如：考核周期、考核时长阈值、考核规则等
}

