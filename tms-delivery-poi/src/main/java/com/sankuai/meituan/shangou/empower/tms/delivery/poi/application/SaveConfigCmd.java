package com.sankuai.meituan.shangou.empower.tms.delivery.poi.application;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.AggDeliveryPlatformInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.relation.DeliveryChannelInfo;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 保存门店信息命令
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/16
 */
@Getter
@ToString
@Builder
public class SaveConfigCmd {

	private final Long tenantId;

	private final Long storeId;

	private final Integer module;

	private final DeliveryLaunchTypeEnum deliveryLaunchType;


	private final DeliveryChannelInfo channelInfo;

	private final AggDeliveryPlatformInfo deliveryPlatformInfo;

	/**
	 * 实时单自动发起配送配置信息
	 * 配送发起触发时间点，发配送延时时长
	 */
	private final ImmediateOrderDeliveryLaunchPointConfig immediateOrderDeliveryLaunchPointConfig;

	/**
	 * 预约单的发起配送的时间点配置.
	 */
	private final BookingOrderDeliveryLaunchPointConfig bookingOrderDeliveryLaunchPointConfig;

	private final Integer autoLaunchStrategyId;

}
