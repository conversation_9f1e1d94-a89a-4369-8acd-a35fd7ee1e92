package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 线上渠道相关配送配置模型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/8
 */
@Data
@EqualsAndHashCode
@AllArgsConstructor
public class OrderPlatformDeliveryConfig {

	/**
	 * 最小订单金额，起送价
	 */
	private final Integer minOrderPrice;

	/**
	 * 配送费
	 */
	private final Integer deliveryFee;

	/**
	 * 配送时长，分钟数
	 */
	private final Integer deliveryMinutes;
}
