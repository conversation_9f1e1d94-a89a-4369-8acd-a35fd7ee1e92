package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 门店配送范围
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/8
 */
@Getter
@ToString
public class DeliveryRange {

	private final int rangeId;
	/**
	 * 坐标系类型
	 */
	private final CoordinateTypeEnum coordinateType;
	/**
	 * 坐标点集合
	 */
	private final List<CoordinatePoint> coordinatePoints;

	public DeliveryRange(int rangeId, CoordinateTypeEnum coordinateType, List<CoordinatePoint> coordinatePoints) {
		Preconditions.checkNotNull(coordinateType, "coordinateType is null");
		Preconditions.checkArgument(CollectionUtils.isNotEmpty(coordinatePoints), "coordinatePoints is empty");

		this.rangeId = rangeId;
		this.coordinateType = coordinateType;
		this.coordinatePoints = coordinatePoints;
	}
}
