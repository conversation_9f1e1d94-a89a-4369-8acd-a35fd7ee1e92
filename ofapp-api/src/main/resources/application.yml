# web服务端口号
server.port: 8080
management:
  endpoints:
    web:
      base-path: /monitor
      path-mapping:
        health: /alive

spring:
  profiles:
    active: '@active-profile@'

sms:
  templateNo: '1742026044120014886'

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-online.xml


---
spring:
  profiles: test

squirrel:
  clusterName: redis-sg-drunkhorse-business_qa

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-offline.xml
  level:
    com.sankuai.shangou.logistics.sdms.dao.mapper: DEBUG # 打印mybatis的sql日志

sms:
  templateNo: '1742026044120014886'

mafka:
  producer:
    - producerName: consumableOutProducer
      namespace: waimai
      topic: pick_select_consumable_items
      appKey: com.sankuai.waimai.sc.pickselectservice
---

spring:
  profiles: staging

squirrel:
  clusterName: redis-sg-drunkhorse-business_product
#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-online.xml

sms:
  templateNo: '1743196410834919506'

mafka:
  producer:
    - producerName: consumableOutProducer
      namespace: waimai
      topic: pick_select_consumable_items
      appKey: com.sankuai.drunkhorsemgmt.wms
---

spring:
  profiles: prod

squirrel:
  clusterName: redis-sg-drunkhorse-business_product

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-online.xml

sms:
  templateNo: '1743196410834919506'

mafka:
  producer:
    - producerName: consumableOutProducer
      namespace: waimai
      topic: pick_select_consumable_items
      appKey: com.sankuai.drunkhorsemgmt.wms

