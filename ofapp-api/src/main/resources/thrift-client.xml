<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean id="redisBean" class="com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory">
        <!-- 集群名称,必填 -->
        <property name="clusterName" value="${squirrel.clusterName}"/>
        <!--读取超时时间,缓存业务建议改成100，存储业务建议改成1000，默认值为1000。选填-->
        <property name="readTimeout" value="1000"/>
        <!--路由策略,默认值是master-only表示只从主节点读取。slave-only表示只读从节点,master-slave表示主从都可以读。选填-->
        <property name="routerType" value="master-slave"/>
        <!--连接redis节点的连接池配置，选填-->
        <property name="poolMaxIdle" value="30"/>
        <property name="poolMaxTotal" value="50"/>
        <property name="poolWaitMillis" value="500"/>

        <!--异步操作线程池配置，选填-->
        <property name="asyncCoreSize" value="16"/>
        <property name="asyncMaxSize" value="64"/>
        <property name="asyncQueueSize" value="1000"/>
    </bean>

    <bean id="authenticateService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.meituan.shangou.sac.thrift.authenticate.AuthenticateService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_timeout','3000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="queryDeliveryInfoThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="componentThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.QueryDeliveryInfoThriftService"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="abnOrderService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="componentThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.AbnOrderService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="depotGoodsThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="componentThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.goodscenter.thrift.DepotGoodsThriftService"/>
        <property name="timeout" value="2000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.goodscenter"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="employThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="componentThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.EmployeeThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="merchantRevenueQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="componentThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.saas.order.management.client.service.revenue.MerChantRevenueQueryService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getInt('order.mng.thrift.service.timeout',5000)}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ordermng"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>


    <bean id="riderQueryRpcService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="componentThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="ocmsQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="componentThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getInt('order.mng.thrift.service.timeout',5000)}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ordermng"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="loginThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="componentThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.auth.thrift.service.LoginThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_login_thrift_timeout','1500')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="accountThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="componentThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.auth.thrift.service.AccountThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_timeout','1500')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="bizOrderThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="componentThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.o2o.service.BizOrderThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getInt('freego.product.thrift.service.timeout',1500)}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="smsThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.saas.message.service.SmsThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasmessage"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="tradeShippingOrderService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="fulfillmentOperateThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.qnh.ofc.ofw.client.thrift.service.operate.FulfillmentOperateThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ofc_ofw_order_search_thrift_timeout','3000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.ofc.ofw"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="deliveryQuestionnaireThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="componentThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.delivery.questionnaire.DeliveryQuestionnaireThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.dmp"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="componentThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('abn_thrift_client_pool_max-active', '100')}"/>
        <property name="maxIdle"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('abn_thrift_client_pool_max-idle', '20')}"/>
        <property name="minIdle"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('abn_thrift_client_pool_min-idle', '1')}"/>
        <property name="maxWait"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('abn_thrift_client_pool_max-wait', '1000')}"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
        <!--Netty IO默认的连接数为1，即一个client使用一个网络连接与服务端进行通信，当一个连接满足不了需求时，可以在MTthriftPoolConfig中设置Netty连接数大小。-->
        <property name="normalSize"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('abn_thrift_client_netty_normalSize', '3')}"/>
        <property name="initialSize"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('abn_thrift_client_netty_initialSize', '1')}"/>
    </bean>

    <bean id="mtUserThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.shangou.saas.service.MtUserThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="ocmsOrderSearchService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getInt('order.mng.thrift.service.timeout',5000)}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ordermng"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>


    <bean id="departmentV2ThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.DepartmentV2ThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="privacyPhoneThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.shangou.saas.service.PrivacyPhoneThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="authThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_timeout','1500')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>


    <bean id="tradeShippingGrayService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.TradeShippingGrayService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tagThriftApi" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="componentThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.xsupply.product.client.service.api.TagThriftApi"/>
        <property name="timeout" value="5000"/>
        <property name="remoteAppkey" value="com.sankuai.sgxsupply.product.management"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="tradeLocationRecommendService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.TradeLocationRecommendService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>


    <bean id="fulfillmentOrderSearchThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="componentThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ofc_ofw_order_search_thrift_timeout','2000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.ofc.ofw"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="selfDeliveryPoiConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="componentThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.dmp"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="oswPoiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.infra.osw.api.poi.TPoiService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.infra.osw"/>
        <property name="timeout" value="5000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tWarehouseService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.infra.osw.api.poi.warehouse.TWarehouseService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.infra.osw"/>
        <property name="timeout" value="5000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="pickingProcessService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.dh.PickingProcessService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="timeout" value="5000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="riderOperateRpcService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.RiderOperateThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="rpcStockQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.reco.store.management.stock.biz.base.thrift.query.QueryStockThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.stockbiz"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="sealContainerLogService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.hu.api.service.SealContainerLogService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.hucenter"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>


    <bean id="verifyTaskThriftService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.sdms.sdk.verify.VerifyTaskThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.sdms"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="tEmployeeService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.infra.osw.api.org.TEmployeeService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.infra.osw"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="inventoryQueryService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.InventoryQueryService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.inventory"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="tOnboardTrainingService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.bizmng.labor.api.training.TOnboardTrainingService"/>
        <property name="timeout" value="3000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.bizmng.labor"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO -->
    </bean>

    <bean id="mApiPermissionThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.auth.thrift.service.MApiPermissionThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="ocmsOrderOperateThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.shangou.saas.service.ocms.OCMSOrderOperateThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="limitAcceptOrderThriftService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.sdms.sdk.limit.LimitAcceptOrderThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.sdms"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>


    <bean id="deliveryOperationThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="deliveryChannelThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.DeliveryChannelThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="promotionMaterialOutboundService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.PromotionMaterialOutboundService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="locationService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.LocationService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.inventory"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="processService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.process.service.ProcessService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.whi"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>


    <bean id="tFortuneWineService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.sgxsupply.wxmall.bizmanagement.client.thrift.pullnew.TFortuneWineService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgxsupply.wxmall.bizmanagement"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="sacAccountSearchThriftService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>


    <bean id="deliveryConfigurationThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="fulfillmentStoreConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.qnh.ofc.ebase.service.FulfillmentStoreConfigThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getInt('fulfill_store_config_thrift_timeout',1500)}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.ofc.ebase"/>
        <property name="nettyIO" value="true"/>
    </bean>


</beans>