package com.sankuai.shangou.supplychain.tof.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.warehouse.PromotionMaterialOutboundService;
import com.sankuai.shangou.logistics.warehouse.dto.MaterialTransInfoDto;
import com.sankuai.shangou.logistics.warehouse.dto.TradeOrderKey;
import com.sankuai.shangou.logistics.warehouse.dto.request.QueryTransMaterialInfoRequest;
import com.sankuai.shangou.logistics.warehouse.dto.request.RiderChangeTransMaterialRequest;
import com.sankuai.shangou.logistics.warehouse.dto.request.RiderTakeAwayTransMaterialRequest;
import com.sankuai.shangou.logistics.warehouse.enums.PromotionMaterialProcessCodeEnum;
import com.sankuai.shangou.logistics.warehouse.enums.PromotionMaterialType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-02-10
 * @email <EMAIL>
 */
@Slf4j
@Rhino
public class OutboundServiceWrapper {

    @Resource
    private PromotionMaterialOutboundService promotionMaterialOutboundService;

    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "OutboundServiceWrapper.transMaterialStockWhenRiderTakeAway", fallBackMethod = "transMaterialStockWhenRiderTakeAwayFallback", timeoutInMilliseconds = 2000)
    public Optional<MaterialTransInfoDto> transMaterialStockWhenRiderTakeAway(RiderTakeAwayTransMaterialRequest request) {
        TResult<List<MaterialTransInfoDto>> result = promotionMaterialOutboundService.transMaterialStockWhenRiderTakeAway(request);
        if (result == null || !result.isSuccess()) {
            throw new ThirdPartyException("调用物流服务异常");
        }
        return CollectionUtils.isEmpty(result.getData()) ? Optional.empty() : Optional.of(result.getData().get(0));
    }

    @Deprecated
    public Optional<MaterialTransInfoDto> transMaterialStockWhenRiderTakeAwayFallback(RiderTakeAwayTransMaterialRequest request) {
        log.warn("调用物流服务transMaterialStockWhenRiderTakeAway异常降级");
        return Optional.empty();
    }


    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "OutboundServiceWrapper.transMaterialStockWhenRiderChange", fallBackMethod = "transMaterialStockWhenRiderChangeFallback", timeoutInMilliseconds = 2000)
    public Optional<MaterialTransInfoDto> transMaterialStockWhenRiderChange(RiderChangeTransMaterialRequest request) {
        TResult<List<MaterialTransInfoDto>> result = promotionMaterialOutboundService.transMaterialStockWhenRiderChange(request);
        if (result == null || !result.isSuccess()) {
            throw new ThirdPartyException("调用物流服务异常");
        }
        return CollectionUtils.isEmpty(result.getData()) ? Optional.empty() : Optional.of(result.getData().get(0));
    }

    @Deprecated
    public Optional<MaterialTransInfoDto> transMaterialStockWhenRiderChangeFallback(RiderChangeTransMaterialRequest request) {
        log.warn("调用物流服务transMaterialStockWhenRiderChange异常降级");
        return Optional.empty();
    }

    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "OutboundServiceWrapper.queryTransMaterialInfo", fallBackMethod = "queryTransMaterialInfoFallback", timeoutInMilliseconds = 2000)
    public Map<String, MaterialTransInfoDto> queryTransMaterialInfo(long currentAccountId, List<TradeOrderKey> tradeOrderKeyList) {
        QueryTransMaterialInfoRequest request = new QueryTransMaterialInfoRequest();
        request.setTradeOrderKeyList(tradeOrderKeyList);
        request.setMaterialType(Lists.newArrayList(PromotionMaterialType.FORTUNE_WINE.getCode()));
        request.setProcessCodeList(Lists.newArrayList(PromotionMaterialProcessCodeEnum.PICK_SELECT.getCode(), PromotionMaterialProcessCodeEnum.RIDER_CHANGE.getCode(), PromotionMaterialProcessCodeEnum.RIDER_TAKE.getCode()));
        TResult<Map<String, MaterialTransInfoDto>> result = promotionMaterialOutboundService.queryTransMaterialInfo(request);
        if (result == null || !result.isSuccess()) {
            log.error("调用物流服务queryTransMaterialInfo失败, reponse = {}", result);
            throw new ThirdPartyException("调用物流服务异常");
        }
        //过滤自己身上的发财酒
        return MapUtils.isEmpty(result.getData()) ? Maps.newHashMap() : result.getData();
    }

    @Deprecated
    public Map<String, MaterialTransInfoDto> queryTransMaterialInfoFallback(long currentAccountId, List<TradeOrderKey> tradeOrderKeyList) {
        log.warn("queryTransMaterialInfo has fallback");
        return Maps.newHashMap();
    }
}
