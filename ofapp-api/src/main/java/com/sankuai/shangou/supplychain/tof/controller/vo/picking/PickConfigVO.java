package com.sankuai.shangou.supplychain.tof.controller.vo.picking;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/14 16:17
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PickConfigVO {
    // 最大上传图片数量
    private Integer maxUploadPicCount;

    // 拍照示例（所有）
    private List<ExamplePicInfo> allExamplePicInfoList;

    // 不同类型商品的拍照示例
    // 被 packingPicConstraints 替代
    @Deprecated
    private List<GoodsTypeConfig> goodsTypeConfigList;

    // 打包相关的配置
    private PackingPicConstraints packingPicConstraints;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExamplePicInfo {
        private Integer type;

        private String name;

        private String url;

        /**
         * 展示顺序
         */
        private Integer order;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class GoodsTypeConfig {
        private Integer type;

        private String typeName;

        private List<Integer> examplePicTypeList;

        private Integer minPicCount;
    }

    /**
     * 打包相关的配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PackingPicConstraints {
        // tab 拍照配置
        private List<TabPictureConfig> picTabList;
    }

    /**
     * Tab 拍照配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TabPictureConfig {
        // tab 名称
        private String tabName;

        // tab 类型：1-商品照; 2-入包照; 3-封签照
        private int type;

        // 最小拍照张数
        private int leastPicCount;

        // 拍照提示文案
        private String description;

        // 拍照指引图片
        private String url;
    }

}
