package com.sankuai.shangou.supplychain.tof.controller.vo.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 骑手信息.
 *
 * <AUTHOR>
 * @since 2021/7/28 23:53
 */
@TypeDoc(
        description = "骑手信息"
)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SelfRiderInfo {

    @FieldDoc(
            description = "骑手账号 ID", requiredness = Requiredness.REQUIRED
    )
    private Long accountId;

    @FieldDoc(
            description = "骑手姓名", requiredness = Requiredness.REQUIRED
    )
    private String name;

    @FieldDoc(
            description = "骑手电话", requiredness = Requiredness.REQUIRED
    )
    private String phone;

    @FieldDoc(
            description = "是否为临时骑手", requiredness = Requiredness.REQUIRED
    )
    private Boolean tempRiderFlag;

    @FieldDoc(
            description = "是否培训通过", requiredness = Requiredness.OPTIONAL
    )
    private Boolean isCompleteTrain;

    @FieldDoc(
            description = "限制项列表", requiredness = Requiredness.OPTIONAL
    )
    private List<LimitItemDetailVO> limitItemList;

}
