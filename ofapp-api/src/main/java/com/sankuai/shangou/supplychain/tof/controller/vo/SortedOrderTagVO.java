package com.sankuai.shangou.supplychain.tof.controller.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.SortedOrderTagVo;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 聚合订单标签VO
 *
 * <AUTHOR>
 * @since 2025/6/26
 */
@Data
public class SortedOrderTagVO {

    @FieldDoc(description = "标签类型、不同Type区分不同的标签(便于扩展)")
    public Integer type;

    @FieldDoc(description = "标签名称")
    public String name;

    @FieldDoc(description = "标签排序")
    public Integer rank;

    /**
     * 构建聚合订单标签VO
     */
    public static SortedOrderTagVO buildOfMng(SortedOrderTagVo sortedOrderTagVo) {
        if (sortedOrderTagVo == null) {
            return null;
        }
        SortedOrderTagVO sortedOrderTagVO = new SortedOrderTagVO();
        sortedOrderTagVO.setType(sortedOrderTagVo.getType());
        sortedOrderTagVO.setName(sortedOrderTagVo.getName());
        sortedOrderTagVO.setRank(sortedOrderTagVo.getRank());
        return sortedOrderTagVO;
    }

    /**
     * 构建聚合订单标签VO列表
     */
    public static List<SortedOrderTagVO> buildListOfMng(List<SortedOrderTagVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().map(SortedOrderTagVO::buildOfMng).filter(Objects::nonNull).collect(Collectors.toList());
    }

}
