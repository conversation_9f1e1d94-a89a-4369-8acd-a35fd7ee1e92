package com.sankuai.shangou.supplychain.tof.controller.vo.picking.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/5/7 21:21
 **/
@Data
public class ConsumableItem {

    @FieldDoc(
            description = "skuId",
            requiredness = Requiredness.OPTIONAL
    )
    private String skuId;

    @FieldDoc(
            description = "数量",
            requiredness = Requiredness.OPTIONAL
    )
    private Integer quantity;

    @FieldDoc(
            description = "耗材类型 参考com.sankuai.shangou.logistics.warehouse.enums.ConsumableGoodsType",
            requiredness = Requiredness.OPTIONAL

    )
    private Integer type;

    @FieldDoc(
            description = "录入方式",
            requiredness = Requiredness.OPTIONAL
    )
    private List<EnteringTypeInfo> enteringTypeInfos;

    @FieldDoc(description = "耗材名称", requiredness = Requiredness.OPTIONAL)
    private String skuName;

    public Optional<String> validate() {
        if (StringUtils.isBlank(skuId)) {
            return Optional.of("耗材skuId不能为空");
        }


        if (quantity == null) {
            return Optional.of("耗材数量不能为空");
        }

        if (CollectionUtils.isNotEmpty(enteringTypeInfos)) {
            for (EnteringTypeInfo enteringTypeInfo : enteringTypeInfos) {
                Optional<String> errMsg = enteringTypeInfo.validate();

                if (errMsg.isPresent()) {
                    return errMsg;
                }
            }
        }

        return Optional.empty();
    }


    @Data
    public static class EnteringTypeInfo {

        @FieldDoc(
                description = "编码",
                requiredness = Requiredness.OPTIONAL
        )
        private String code;

        @FieldDoc(
                description = "录入方式 1-扫码录入 2-手动录入",
                requiredness = Requiredness.OPTIONAL
        )
        private Integer codeEnteringType;

        public Optional<String> validate() {
            if (StringUtils.isBlank(code)) {
                return Optional.of("耗材编码不能为空");
            }

            if (codeEnteringType == null) {
                return Optional.of("耗材编码录入方式不能为空");
            }

            return Optional.empty();
        }
    }
}
