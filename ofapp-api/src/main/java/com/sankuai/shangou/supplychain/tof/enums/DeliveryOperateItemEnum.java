package com.sankuai.shangou.supplychain.tof.enums;

import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public enum DeliveryOperateItemEnum {

    //1取消2转自配送3已送出4联系骑手
    MANUAL_THIRD_PART(101, "手动发三方配送", "发三方配送"),
    RETRY_LAUNCH(102, "重发配送", "重发配送"),
    EXCEPTION_TO_THIRD_PART(103, "平台配送异常转三方配送", "发三方配送"),
    EXCEPTION_TO_SELF(104, "平台配送异常转商家自配送", "自己送"),

    RETRY_LAUNCH_BY_MALTFARM(105, "麦芽田重发配送", "重发配送"),
    RETRY_LAUNCH_BY_HAIKUI(106, "海葵重发配送", "重发配送"),

    DELIVERY_TO_SELF(107,"转自配送","转自配送"),

    DELIVERY_TO_MALTFARM(108,"转麦芽田","转麦芽田"),

    DELIVERY_TO_DAP(109,"转青云聚信平台","转青云聚信平台"),


    ADD_TIP_FEE(110,"加小费","加小费"),
    PAOTUI_CANCEL_DELIVERY(111,"取消配送","取消配送"),
    RECALL_DELIVERY(112,"再次呼叫","再次呼叫"),
    REPORT_EXCEPTION(113,"异常上报","异常上报"),
    AUDIT_EXCEPTION(114,"异常审核","异常审核"),

    DOUYIN_RECALL_DELIVERY(115, "抖音平台配送手动呼叫配送", "抖音平台配送手动呼叫配送"),

    DOUYIN_EXCEPTION_RECALL_DELIVERY(116, "抖音平台配送异常重新呼叫配送", "抖音平台配送异常重新呼叫配送"),

    DELIVERY_TO_FOUR_WHEEL(117,"转汽车配送","转汽车配送"),
    APPEND_FOUR_WHEEL_TYPE(118,"追加车型","追加汽车配送车型"),

    CANCELING(900, "取消中", "取消中"),
    CANCEL_DELIVERY(901, "取消配送", "取消配送"),


    ;


    /**
     * code
     */
    public final Integer type;
    /**
     * 实际的场景
     */
    public final String scene;
    /**
     * 按钮描述
     */
    public final String iconDesc;

    DeliveryOperateItemEnum(Integer type, String scene, String iconDesc) {
        this.type = type;
        this.scene = scene;
        this.iconDesc = iconDesc;
    }

    public static DeliveryOperateItemEnum tmsItemToOperateItem(Integer code){
        if(code==null){
            return null;
        }
        com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryOperateItemEnum codeEnum = com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryOperateItemEnum.TRANS_SELF_DELIVERY.codeToEnum(code);
        if(codeEnum ==null){
            return null;
        }
        switch (codeEnum){
            case TRANS_SELF_DELIVERY:
                return DeliveryOperateItemEnum.DELIVERY_TO_SELF;
            case TRANS_MALT_FARM:
                return DeliveryOperateItemEnum.DELIVERY_TO_MALTFARM;
            case TRANS_DAP_DELIVERY:
                return DeliveryOperateItemEnum.DELIVERY_TO_DAP;
            case ADD_TIP_FEE:
                return DeliveryOperateItemEnum.ADD_TIP_FEE;
            case CANCEL_DELIVERY:
                return DeliveryOperateItemEnum.PAOTUI_CANCEL_DELIVERY;
            case RECALL_DELIVERY:
                return DeliveryOperateItemEnum.RECALL_DELIVERY;
            case REPORT_EXCEPTION:
                return DeliveryOperateItemEnum.REPORT_EXCEPTION;
            case AUDIT_EXCEPTION:
                return DeliveryOperateItemEnum.AUDIT_EXCEPTION;
            case MANUAL_LAUNCH:
                return DeliveryOperateItemEnum.DOUYIN_RECALL_DELIVERY;
            case EXCEPTION_RECALL_LAUNCH:
                return DeliveryOperateItemEnum.DOUYIN_EXCEPTION_RECALL_DELIVERY;
            case TRANS_FOUR_WHEEL_DELIVERY:
                return DeliveryOperateItemEnum.DELIVERY_TO_FOUR_WHEEL;
            case APPEND_FOUR_WHEEL_DELIVERY_TYPE:
                return DeliveryOperateItemEnum.APPEND_FOUR_WHEEL_TYPE;
            default:
                return null;
        }
    }

    public static List<DeliveryOperateItemEnum> tmsItemListToOperateItemList(List<Integer> codeList){
        if(CollectionUtils.isEmpty(codeList)){
            return Collections.emptyList();
        }
        List<DeliveryOperateItemEnum> itemEnumList = new ArrayList<>();
        for (Integer code : codeList){
            DeliveryOperateItemEnum itemEnum = tmsItemToOperateItem(code);
            if(itemEnum!=null){
                itemEnumList.add(itemEnum);
            }
        }
        return itemEnumList;
    }

    public static List<Integer> tmsItemListToOperateItemIntegerList(List<Integer> codeList){
        if(CollectionUtils.isEmpty(codeList)){
            return Collections.emptyList();
        }
        List<Integer> itemList = new ArrayList<>();
        for (Integer code : codeList){
            DeliveryOperateItemEnum itemEnum = tmsItemToOperateItem(code);
            if(itemEnum!=null){
                itemList.add(itemEnum.type);
            }
        }
        return itemList;
    }
}
