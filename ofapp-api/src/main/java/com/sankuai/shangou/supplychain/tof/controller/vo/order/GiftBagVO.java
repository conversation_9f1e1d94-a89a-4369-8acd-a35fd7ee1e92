package com.sankuai.shangou.supplychain.tof.controller.vo.order;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.DrunkHorseGiftBagVo;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/***
 * author : <EMAIL>
 * date : 2024/8/21
 * time : 14:38
 * 描述 :
 **/
@TypeDoc(
        description = "歪马礼袋"
)
@Data
public class GiftBagVO {

    @FieldDoc(
            description = "主品货号"
    )
    private String belongSkuId;

    @FieldDoc(
            description = "礼袋货品skuId"
    )
    private String materialSkuId;

    @FieldDoc(
            description = "礼袋货品名称"
    )
    private String materialSkuName;

    @FieldDoc(
            description = "礼袋图片"
    )
    private String picUrl;

    @FieldDoc(
            description = "礼袋类型，1:品牌礼袋， 2:歪马礼袋"
    )
    private int type;

    @FieldDoc(
            description = "礼袋应赠数量"
    )
    private int cnt;

    @FieldDoc(
            description = "调整后的数量"
    )
    private int resetCnt;

    @FieldDoc(
            description = "退款数量"
    )
    private int refundCnt;

    @FieldDoc(
            description = "规格"
    )
    private String spec;

    @FieldDoc(
            description = "upc"
    )
    private String upc;

    @FieldDoc(
            description = "是否缺货"
    )
    private Boolean isLackStock;


    public static List<GiftBagVO> buildDrunkHorseGiftBag(List<DrunkHorseGiftBagVo> drunkHorseGiftBagVoList) {
        if (CollectionUtils.isEmpty(drunkHorseGiftBagVoList)) {
            return Lists.newArrayList();
        }
        return drunkHorseGiftBagVoList.stream().map(GiftBagVO::buildDrunkHorseGiftBagVO).collect(Collectors.toList());
    }


    private static GiftBagVO buildDrunkHorseGiftBagVO(DrunkHorseGiftBagVo drunkHorseGiftBagVo){
        GiftBagVO giftBagVO = new GiftBagVO();
        giftBagVO.setBelongSkuId(drunkHorseGiftBagVo.getBelongSkuId());
        giftBagVO.setMaterialSkuId(drunkHorseGiftBagVo.getMaterialSkuId());
        giftBagVO.setMaterialSkuName(drunkHorseGiftBagVo.getMaterialSkuName());
        giftBagVO.setPicUrl(drunkHorseGiftBagVo.getPicUrl());
        giftBagVO.setType(drunkHorseGiftBagVo.getType());
        giftBagVO.setCnt(drunkHorseGiftBagVo.getCnt());
        giftBagVO.setResetCnt(drunkHorseGiftBagVo.getResetCnt());
        giftBagVO.setRefundCnt(drunkHorseGiftBagVo.getRefundCnt());
        giftBagVO.setSpec(drunkHorseGiftBagVo.getSpec());
        giftBagVO.setUpc(drunkHorseGiftBagVo.getUpc());
//        drunkHorseGiftBagVO.setIsIncludeStockLackGoods();
        return giftBagVO;
    }

}
