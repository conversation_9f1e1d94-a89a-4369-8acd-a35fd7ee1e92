package com.sankuai.shangou.supplychain.tof.controller;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;
import com.sankuai.shangou.supplychain.tof.assembler.SelfDeliveryOrderAssembler;
import com.sankuai.shangou.supplychain.tof.component.*;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryCompleteConfigVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.LimitItemDetailVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.TabCountVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.*;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.request.QueryRiderWaitToGetOrderRequest;
import com.sankuai.shangou.supplychain.tof.enums.ComponentTypeEnum;
import com.sankuai.shangou.supplychain.tof.enums.FilterSpilitTypeEnum;
import com.sankuai.shangou.supplychain.tof.wrapper.LimitAcceptServiceWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/19 11:30
 **/

@Slf4j
@InterfaceDoc(
        type = "restful",
        displayName = "歪马自营配送相关接口",
        description = "歪马自营配送的查询功能",
        scenarios = "主要应用于歪马自配场景"
)
@RestController
@RequestMapping("/api/orderfulfill/app/rider/delivery")
public class RiderDeliveryQueryController {
    @Resource
    private DeliveryOrderComponent deliveryOrderComponent;

    @Resource
    private TradeOrderInfoComponent tradeOrderInfoComponent;

    @Resource
    private RevenueComponent revenueComponent;

    @Resource
    private DeliveryExceptionComponent deliveryExceptionComponent;

    @Resource
    private DeliveryStatisticComponent deliveryStatisticComponent;

    @Resource
    private QuestionnaireComponent questionnaireComponent;

    @Resource
    private DeliveryRiskControlComponent deliveryRiskControlComponent;

    @Resource
    private SelfDeliveryOrderAssembler selfDeliveryOrderAssembler;

    @Resource
    private LimitAcceptServiceWrapper limitAcceptServiceWrapper;



    @MethodDoc(
            displayName = "分页查询骑手可领取的订单列表",
            description = "分页查询骑手可领取的订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手可领取的订单列表",
                            type = QueryRiderWaitToGetOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/querywaitget",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querywaitget", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Map<String, Object> queryWaitGetOrder(@Valid @RequestBody PageQueryRequest request) {
        List<ComponentTypeEnum> extComponentTypeEnums = Arrays.asList(ComponentTypeEnum.ORDER_REVENUE_INFO,
                ComponentTypeEnum.LACK_STOCK,
                ComponentTypeEnum.TURN_DELIVERY_BUTTON_INFO,
                ComponentTypeEnum.SEAL_DELIVER,
                ComponentTypeEnum.USE_ASSESS_TIME,
                ComponentTypeEnum.EXTERNAL_PRODUCT_INFO);
        return selfDeliveryOrderAssembler.queryRiderOrderTemplate(
                () -> deliveryOrderComponent.pageQueryDeliveryOrderList(request, DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode(), null, FilterSpilitTypeEnum.KEEP_NOT_SPLIT),
                extComponentTypeEnums);
    }

    @MethodDoc(
            displayName = "分页查询骑手待取货的订单列表",
            description = "分页查询骑手待取货的订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手待取货的订单列表",
                            type = Map.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/querywaittakegoods",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querywaittakegoods", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Map<String, Object> queryWaitTakeGoodsOrder(@Valid @RequestBody PageQueryRequest request) {
        List<ComponentTypeEnum> extComponentTypeEnums = Arrays.asList(ComponentTypeEnum.ORDER_REVENUE_INFO,
                ComponentTypeEnum.LACK_STOCK,
                ComponentTypeEnum.TURN_DELIVERY_BUTTON_INFO,
                ComponentTypeEnum.DELIVERY_EXCEPTION_INFO,
                ComponentTypeEnum.SEAL_DELIVER,
                ComponentTypeEnum.USE_ASSESS_TIME,
                ComponentTypeEnum.EXTERNAL_PRODUCT_INFO);
        return selfDeliveryOrderAssembler.queryRiderOrderTemplate(
                () -> deliveryOrderComponent.pageQueryDeliveryOrderList(request, DeliveryStatusEnum.RIDER_ASSIGNED.getCode(), LoginContextUtils.getAppLoginAccountId(), FilterSpilitTypeEnum.NO_FILTER),
                extComponentTypeEnums);
    }


    @MethodDoc(
            displayName = "分页查询骑手配送中的订单列表",
            description = "分页查询骑手配送中的订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手待取货的订单列表",
                            type = Map.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/queryindelivery",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryindelivery", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Map<String, Object> queryIndelivery(@Valid @RequestBody PageQueryRequest request) {
        List<ComponentTypeEnum> extComponentTypeEnums = Arrays.asList(ComponentTypeEnum.ORDER_REVENUE_INFO,
                ComponentTypeEnum.TURN_DELIVERY_BUTTON_INFO,
                ComponentTypeEnum.DELIVERY_EXCEPTION_INFO,
                ComponentTypeEnum.HIGH_PRICE_TAG,
                ComponentTypeEnum.QUESTIONNAIRE,
                ComponentTypeEnum.SEAL_DELIVER,
                ComponentTypeEnum.USE_ASSESS_TIME,
                ComponentTypeEnum.EXTERNAL_PRODUCT_INFO);
        return selfDeliveryOrderAssembler.queryRiderOrderTemplate(
                () -> deliveryOrderComponent.pageQueryDeliveryOrderList(request, DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode(), LoginContextUtils.getAppLoginAccountId(), FilterSpilitTypeEnum.NO_FILTER),
                extComponentTypeEnums);
    }

    @MethodDoc(
            displayName = "分页查询骑手已送达订单列表",
            description = "分页查询骑手已送达订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手已送达订单列表",
                            type = QueryRiderCompletedOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/querycompleted",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querycompleted", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> queryCompleted(@Valid @RequestBody QueryRiderCompletedOrderRequest request) {

        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        return selfDeliveryOrderAssembler.queryCompleteDeliveryOrder(request);
    }

    @MethodDoc(
            displayName = "查询配送完成配置",
            description = "查询配送完成配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询配送完成配置",
                            type = QueryRiderCompletedOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/completeDeliveryConfig", method = {RequestMethod.POST})
    @ResponseBody
    public DeliveryCompleteConfigVO deliveryCompleteConfig() {
        return selfDeliveryOrderAssembler.getDeliveryCompleteConfig();
    }


    @MethodDoc(
            displayName = "分页查询骑手正在进行中的订单列表",
            description = "分页查询骑手正在进行中的订单列表，包括骑手接单，骑手取货",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手正在进行中的订单列表",
                            type = QueryRiderInProgressOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/queryinprogress",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryinprogress", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Map<String, Object> queryRiderInProgressOrder(@Valid @RequestBody QueryRiderInProgressOrderRequest request) {
        List<ComponentTypeEnum> extComponentTypeEnums = new ArrayList<>();
        if (Objects.equals(request.getNeedReturnHighPriceTag(), true)) {
            extComponentTypeEnums.add(ComponentTypeEnum.HIGH_PRICE_TAG);
        }

        if (Objects.equals(request.getNeedReturnSealTag(), true)) {
            extComponentTypeEnums.add(ComponentTypeEnum.SEAL_DELIVER);
        }
        extComponentTypeEnums.add(ComponentTypeEnum.USE_ASSESS_TIME);
        return selfDeliveryOrderAssembler.queryRiderOrderTemplate(
                () -> deliveryOrderComponent.getRiderInProgressDeliveryOrders(request, false),
                extComponentTypeEnums);
    }


    @MethodDoc(
            displayName = "分页查询骑手已送达订单列表",
            description = "分页查询骑手已送达订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手已送达订单列表",
                            type = QueryRiderCompletedOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/queryRoute",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryRoute", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> queryRoute(@Valid @RequestBody QueryRouteRequest request) {
        return selfDeliveryOrderAssembler.queryDeliveryOrderWithRouteInfo(request);
    }

    @MethodDoc(
            displayName = "分页查询骑手仅配送列表",
            description = "分页查询骑手仅配送列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手正在进行中的订单列表",
                            type = QueryRiderInProgressOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/queryWaitReceiveList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryWaitReceiveList", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Map<String, Object> queryWaitReceiveList(@Valid @RequestBody PageQueryRequest request) {
        List<ComponentTypeEnum> extComponentTypeEnums = Lists.newArrayList(
                ComponentTypeEnum.ORDER_REVENUE_INFO,
                ComponentTypeEnum.LACK_STOCK,
                ComponentTypeEnum.TURN_DELIVERY_BUTTON_INFO,
                ComponentTypeEnum.SEAL_DELIVER,
                ComponentTypeEnum.USE_ASSESS_TIME,
                ComponentTypeEnum.EXTERNAL_PRODUCT_INFO
        );
        return selfDeliveryOrderAssembler.queryRiderOrderTemplate(
                () -> deliveryOrderComponent.pageQueryDeliveryOrderList(request, DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode(), null, FilterSpilitTypeEnum.KEEP_SPLIT),
                extComponentTypeEnums);
    }


    @MethodDoc(
            displayName = "查询二级tab去重后的订单数",
            description = "查询二级tab去重后的订单数",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询二级tab去重后的订单数",
                            type = QueryRiderInProgressOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/queryTabCount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryTabCount", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public TabCountVO queryTabCount() {
        return selfDeliveryOrderAssembler.queryTabCount();
    }

    @MethodDoc(
            displayName = "查询限制接单信息",
            description = "查询限制接单信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询限制接单信息",
                            type = QueryRiderInProgressOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/queryLimitAcceptOrderDetail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryLimitAcceptOrderDetail", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Map<String, Object> queryLimitAcceptOrderDetail() {
        if (GrayConfigUtils.judgeIsGrayStore(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginStoreId(), GrayKeyEnum.LIMIT_ACCEPT_ORDER.getGrayKey())){
            List<LimitItemDTO> limitItemDTOS = limitAcceptServiceWrapper.queryLimitItemByAccountId(LoginContextUtils.getAppLoginTenant(),
                    LoginContextUtils.getAppLoginAccountId(), LoginContextUtils.getAppLoginStoreId());
            List<LimitItemDetailVO> limitDetailList = limitItemDTOS.stream().map(dto -> {
                LimitItemDetailVO limitItemDetailVO = new LimitItemDetailVO();
                limitItemDetailVO.setReason(dto.getReason());
                limitItemDetailVO.setReleaseLimitText(dto.getReleaseLimitText());
                limitItemDetailVO.setSkipUrl(dto.getSkipUrl());
                limitItemDetailVO.setSkipUrlType(dto.getSkipUrlType());
                limitItemDetailVO.setSkipButtonText(dto.getSkipButtonText());
                limitItemDetailVO.setLimitMode(dto.getLimitMode());
                limitItemDetailVO.setLimitStartDate(Optional.ofNullable(dto.getLimitStartDate()).map(item -> item.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).orElse(null));
                limitItemDetailVO.setLimitSkuIdNumMap(dto.getLimitSkuIdNumMap());

                limitItemDetailVO.setIsGrayStore(dto.getIsGrayStore());
                return limitItemDetailVO;
            }).collect(Collectors.toList());

            return ImmutableMap.of("limitDetailList", limitDetailList);
        } else {
            return ImmutableMap.of("limitDetailList", Collections.emptyList());
        }

    }

    @MethodDoc(
            displayName = "分页查询骑手已送达订单列表",
            description = "分页查询骑手已送达订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手已送达订单列表",
                            type = QueryRiderCompletedOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/querycompleted",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryIncrementCompleted", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> queryIncrementCompleted(@Valid @RequestBody QueryIncrementRiderCompletedOrderRequest request) {
        if (request.getLastUpdateTime() < TimeUtils.toMilliSeconds(LocalDateTime.now().toLocalDate().atTime(0,0,0))) {
            request.setLastUpdateTime(TimeUtils.toMilliSeconds(LocalDateTime.now().toLocalDate().atTime(0,0,0)));
        }

        return selfDeliveryOrderAssembler.queryIncrementCompleteDeliveryOrder(request);
    }
}
