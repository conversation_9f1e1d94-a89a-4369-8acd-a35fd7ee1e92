package com.sankuai.shangou.supplychain.tof.enums;


/**
 * @TypeDoc( description = "操作项"
 * )
 */
public enum CouldOperateItemEnum {
    /**
     * @FieldDoc( description = "接单"
     * )
     */
    ACCEPT_ORDER(10),
    /**
     * @FieldDoc( description = "完成拣货"
     * )
     */
    COMPLETE_PICK(20),
    /**
     * @FieldDoc( description = "补打小票"
     * )
     */
    PRINT_RECEIPT(30),
    /**
     * @FieldDoc( description = "全单退款"
     * )
     */
    FULL_ORDER_REFUND(40),
    /**
     * @FieldDoc( description = "部分退款"
     * )
     */
    PART_ORDER_REFUND(50),
    RECEIVE_REFUND_PRODUCTS(60),
    WEIGHT_REFUND(70),
    /**
     * @FieldDoc( description = "转三方配送"
     * )
     */
    TURN_AGG_DELIVERY(140),
    /**
     * @FieldDoc( description = "转自配"
     * )
     */
    TURN_SELF_DELIVERY(150),


    /**
     * @FieldDoc( description = "歪马异常单列表-退款按钮"
     * )
     */
    DH_EXCEPTION_REFUND_BTN(300),
    /**
     * @FieldDoc( description = "歪马异常单列表-取消订单按钮"
     * )
     */
    DH_EXCEPTION_CANCEL_BTN(310);

    private final int value;

    CouldOperateItemEnum(int value) {
        this.value = value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Thrift IDL.
     */
    public int getValue() {
        return value;
    }


}
