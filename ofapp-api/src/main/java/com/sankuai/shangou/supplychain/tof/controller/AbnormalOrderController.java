package com.sankuai.shangou.supplychain.tof.controller;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.operate.CentralStockRefreshReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.response.operate.CentralStockRefreshResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.operate.FulfillmentOperateThriftService;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderItemDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.enums.AbnOrderCloseReason;
import com.sankuai.shangou.logistics.warehouse.enums.AbnOrderCreateReason;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import com.sankuai.shangou.supplychain.tof.component.AbnormalOrderComponent;
import com.sankuai.shangou.supplychain.tof.component.DeliveryOrderComponent;
import com.sankuai.shangou.supplychain.tof.component.RevenueComponent;
import com.sankuai.shangou.supplychain.tof.component.TradeOrderInfoComponent;
import com.sankuai.shangou.supplychain.tof.controller.vo.PageVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.AbnOrderItemVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.AbnormalOrderListVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.AbnormalOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.request.QueryVirtualPhoneRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.request.RefreshStockRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.request.SendLackStockMessageRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.response.QueryVirtualPhoneResponse;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.RevenueDetailVo;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.TradeOrderVO;
import com.sankuai.shangou.supplychain.tof.service.LackStockMessageService;
import com.sankuai.shangou.supplychain.tof.wrapper.AuthThriftWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.DepotGoodsWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.OSWServiceWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@InterfaceDoc(
        type = "restful",
        displayName = "订单履约异常单相关接口",
        description = "提供订单履约异常单相关接口的查询/操作功能",
        scenarios = "主要应用于用户信息管理的场景"
)
@RestController
@RequestMapping("/api/orderfulfill/app/abnormal/")
public class AbnormalOrderController {

    @Resource
    private DepotGoodsWrapper depotGoodsWrapper;
    @Resource
    private AbnormalOrderComponent abnormalOrderComponent;
    @Resource
    private DeliveryOrderComponent deliveryOrderComponent;
    @Resource
    private RevenueComponent revenueComponent;
    @Resource
    private TradeOrderInfoComponent tradeOrderInfoComponent;
    @Resource
    private LackStockMessageService lackStockMessageService;
    @Resource
    private TradeShippingOrderService tradeShippingOrderService;
    @Resource
    private FulfillmentOperateThriftService fulfillmentOperateThriftService;
    @Resource
    private AuthThriftWrapper authThriftWrapper;
    @Resource
    private OSWServiceWrapper oswServiceWrapper;



    @MethodDoc(
            displayName = "查询异常单列表",
            description = "查询异常单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询异常单列表请求",
                            type = Void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/app/orderfulfill/abnormal/list",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> abnormalOrderList() {
        Map<String, Object> resMap = new HashMap<>();

            List<AbnormalOrderVO> abnormalOrderVOS = abnormalOrderComponent.abnOrderListComponent(AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId(), getStoreId());
            if (CollectionUtils.isEmpty(abnormalOrderVOS)) {
                resMap.put("pageInfo", new PageVO(1, 0, 1));
                resMap.put("abnormalOrderList", Lists.newArrayList());
                return resMap;
            }
            List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList = abnormalOrderVOS.stream().map(abnormalOrderVO -> new TradeOrderInfoComponent.TradeOrderKey(abnormalOrderVO.getOrderBizType(), abnormalOrderVO.getTradeOrderNo())).collect(Collectors.toList());
            Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap = tradeOrderInfoComponent.tradeOrderInfoComponent(tradeOrderKeyList);
            Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryOrderVO> deliveryOrderVOMap = deliveryOrderComponent.deliveryOrderComponent(tradeOrderVOMap);
            Map<TradeOrderInfoComponent.TradeOrderKey, RevenueDetailVo> tradeOrderKeyRevenueDetailVoMap = revenueComponent.revenueComponent(AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId(), tradeOrderKeyList);
            Map<TradeOrderInfoComponent.TradeOrderKey, List<Integer>> couldOperateItemsMap = tradeOrderInfoComponent.queryAbnOrderCouldOperateItemsMap(tradeOrderVOMap);
            //查询是否直营门店，不影响流程
            boolean isDirectStore = false;
            try {
                WarehouseDTO warehouseDTO = oswServiceWrapper.queryWarehouseByWarehouseId(AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId(), getStoreId());
                if (Objects.nonNull(warehouseDTO) && Objects.equals(warehouseDTO.getOperationMode(), OSWServiceWrapper.DIRECT_OPERATION_MODE)) {
                    isDirectStore = true;
                }
            } catch (Exception e) {
                log.error("查询是否直营门店失败", e);
            }

        boolean finalIsDirectStore = isDirectStore;
        List<AbnormalOrderListVO> abnormalOrderListVOList = abnormalOrderVOS.stream().map(abnormalOrderVO -> {
                TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = new TradeOrderInfoComponent.TradeOrderKey(abnormalOrderVO.getOrderBizType(), abnormalOrderVO.getTradeOrderNo());
                TradeOrderVO tradeOrderVO = tradeOrderVOMap.get(tradeOrderKey);
                DeliveryOrderVO deliveryOrderVO = deliveryOrderVOMap.get(tradeOrderKey);
                RevenueDetailVo revenueDetailVo = tradeOrderKeyRevenueDetailVoMap.get(tradeOrderKey);
                abnormalOrderComponent.fillLackStockGoodsInfo(getStoreId(), abnormalOrderVO, tradeOrderVO);
                List<Integer> couldOperateItem = couldOperateItemsMap.getOrDefault(tradeOrderKey, Lists.newArrayList());
                return AbnormalOrderListVO.buildAbnormalOrderListVO(abnormalOrderVO, tradeOrderVO, deliveryOrderVO, revenueDetailVo, couldOperateItem, finalIsDirectStore);
            }).sorted((o1, o2) -> {
                try {
                    //都用期望的*送达时间*来比较
                    long compareKey1 = Objects.nonNull(o1.getEvaluateArriveDeadline()) ? o1.getEvaluateArriveDeadline() : o1.getEstimateArriveTimeEnd();
                    long compareKey2 = Objects.nonNull(o2.getEvaluateArriveDeadline()) ? o2.getEvaluateArriveDeadline() : o2.getEstimateArriveTimeEnd();
                    return (int) (compareKey1 - compareKey2);
                } catch (Exception e) {
                    log.error("delivery order compare error", e);
                    Cat.logEvent("DELIVERY_ORDER", "COMPARE_ERROR");
                    return (int) (Optional.ofNullable(o1.getEstimateArriveTimeEnd()).orElse(0L) - Optional.ofNullable(o2.getEstimateArriveTimeEnd()).orElse(0L));
                }
            }).collect(Collectors.toList());
            resMap.put("pageInfo", new PageVO(1, abnormalOrderListVOList.size(), 1));
            resMap.put("abnormalOrderList", abnormalOrderListVOList);
            return resMap;

    }

    @MethodDoc(
            displayName = "异常单操作-刷新库存",
            description = "异常单操作-刷新库存",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "异常单操作-刷新库存请求",
                            type = Void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/app/orderfulfill/abnormal/refreshStock",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/refreshStock", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> refreshStock(@Valid @RequestBody RefreshStockRequest request) {

            Map<String, Object> resultMap = Maps.newHashMap();
            AbnOrderDTO abnOrder = abnormalOrderComponent.getUnprocessedByTradeOrderSource(getStoreId(), request.getOrderBizType(), request.getChannelOrderId());
            if (abnOrder == null) {
                resultMap.put("hint", "可售库存充足，请到新任务列表处理");
                return resultMap;
            }
            TResult<List<TradeShippingOrderDTO>> tradeOrderNosResult = tradeShippingOrderService.getByTradeOrderNos(getStoreId(), request.getOrderBizType(), Lists.newArrayList(request.getChannelOrderId()));
            if (!tradeOrderNosResult.isSuccess() || CollectionUtils.isEmpty(tradeOrderNosResult.getData())) {
                throw new SystemException("未查询到出库单，请稍后再试");
            }
            TradeShippingOrderDTO tradeShippingOrderDTO = tradeOrderNosResult.getData().get(0);
            // 刷新异常单时，如果订单已经到达了 FINISH、CANCEL、INVALID 3种状态，则尝试强制关闭异常单
            if (Lists.newArrayList(TradeShippingOrderStatus.FINISH.getCode(), TradeShippingOrderStatus.CANCEL.getCode(), TradeShippingOrderStatus.INVALID.getCode()).contains(tradeShippingOrderDTO.getStatus())) {
                log.info("AbnormalOrderController.refreshStock 拣货单已取消，尝试关闭异常单。tradeOrderNo：{}", tradeShippingOrderDTO.getTradeOrderNo());
                // 关单原因
                AbnOrderCloseReason reason;
                switch (TradeShippingOrderStatus.enumOf(tradeShippingOrderDTO.getStatus())) {
                    case FINISH:
                        reason = AbnOrderCloseReason.OOS_STOCK_FULLFILL;
                        break;
                    case CANCEL:
                        reason = AbnOrderCloseReason.OOS_ORDER_CANCEL;
                        break;
                    default:
                        reason = AbnOrderCloseReason.OOS_ORDER_COMPLETE;
                        break;
                }
                boolean close = abnormalOrderComponent.forceClose(getStoreId(), request.getOrderBizType(), tradeShippingOrderDTO.getTradeOrderNo(), reason.getCode());
                log.info("AbnormalOrderController.refreshStock 关闭异常单结果：{}", close);
                // 事件上报 + 告警：异常单非正常关闭
                if (close) {
                    Cat.logEvent("ABNORMAL_ORDER", "CLOSED_BY_REFRESH_STOCK");
                }
                throw new SystemException("拣货已完成，请刷新列表");
            }
            //只有中央库存的异常，才调用ofc重新锁定
            if (Objects.nonNull(tradeShippingOrderDTO.getFulfillmentOrderId()) && Objects.equals(abnOrder.getCreateReasonCode(), AbnOrderCreateReason.OUT_OF_STOCK_CENTRAL.getCode())) {
                CentralStockRefreshReq req = new CentralStockRefreshReq();
                req.setTenantId(tradeShippingOrderDTO.getMerchantId());
                req.setWarehouseId(tradeShippingOrderDTO.getWarehouseId());
                req.setFulfillmentOrderId(tradeShippingOrderDTO.getFulfillmentOrderId());
                CentralStockRefreshResponse centralStockRefreshResponse = fulfillmentOperateThriftService.refreshCentralStock(req);
                log.info("fulfillmentOperateThriftService.refreshCentralStock req = {}, response = {}", req, centralStockRefreshResponse);
                if (!Objects.equals(centralStockRefreshResponse.getStatus().getCode(), 0)) {
                    throw new SystemException("重新锁定中央库存失败，请稍后再试");
                }
            }

            // 根据sku查找货品信息
            Map<String, DepotGoodsDetailDto> skuId2goods = depotGoodsWrapper.queryBySkuId(AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId(), getStoreId(), abnOrder.getItems().stream()
                    .map(AbnOrderItemDTO::getSkuId).collect(Collectors.toList()));
            // 构造信息
            List<AbnOrderItemVO> abnOrderItemVOList = abnOrder.getItems().stream().map(item -> abnormalOrderComponent.buildAbnormalOrderItemVO(item, skuId2goods)).collect(Collectors.toList());
            resultMap.put("abnOrderList", abnOrderItemVOList);
            resultMap.put("hint", "可售库存仍不足，请处理");
            return resultMap;
    }

    @MethodDoc(
            displayName = "异常单操作-发送缺货短信",
            description = "异常单操作-发送缺货短信",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "异常单操作-发送缺货短信请求",
                            type = Void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/app/orderfulfill/abnormal/sendLackStockMessage",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/sendLackStockMessage", method = {RequestMethod.POST})
    @ResponseBody
    public void sendLackStockMessage(@Valid @RequestBody SendLackStockMessageRequest request) {
        lackStockMessageService.sendLackStockMessage(getStoreId(), request.getOrderBizType(), request.getChannelOrderId());
    }


    @MethodDoc(
            displayName = "异常单操作-查询下单人电话",
            description = "异常单操作-查询下单人电话",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "异常单操作-查询下单人电话请求",
                            type = Void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/app/orderfulfill/abnormal/queryBuyerVirtualPhone",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryBuyerVirtualPhone", method = {RequestMethod.POST})
    @ResponseBody
    public QueryVirtualPhoneResponse queryBuyerVirtualPhone(@Valid @RequestBody QueryVirtualPhoneRequest request) {
        return lackStockMessageService.queryBuyerVirtualPhone(request);
    }


    private long getStoreId() {
        String storeIdStr = AppLoginContextHolder.getAppLoginContext().getStoreIds();
        if (StringUtils.isEmpty(storeIdStr) || storeIdStr.split(",").length > 1) {
            throw new SystemException("仅支持单门店维度");
        }
        return Long.parseLong(AppLoginContextHolder.getAppLoginContext().getStoreIds());
    }
}
