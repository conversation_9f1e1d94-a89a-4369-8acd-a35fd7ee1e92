package com.sankuai.shangou.supplychain.tof.controller.vo.picking.request;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/6/26 10:52
 **/
@Data
public class ReturnSealContainerRequest {
    private String barCode;

    private Boolean skipDeliveryStatusCheck;

    public String validate() {
        if (StringUtils.isBlank(barCode)) {
            return "封签容具编码不能为空";
        }
        return null;
    }
}
