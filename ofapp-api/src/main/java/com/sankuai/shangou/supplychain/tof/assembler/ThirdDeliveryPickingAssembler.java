package com.sankuai.shangou.supplychain.tof.assembler;

import com.dianping.cat.Cat;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.ChannelOrderIdKeyReq;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import com.sankuai.shangou.supplychain.tof.component.*;
import com.sankuai.shangou.supplychain.tof.controller.convert.RiderPickingConvert;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.RevenueDetailVo;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.TradeOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.third.response.ThirdDeliveryPickingOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.third.response.ThirdDeliveryPickingOrderListResponse;
import com.sankuai.shangou.supplychain.tof.enums.ComponentTypeEnum;
import com.sankuai.shangou.supplychain.tof.utils.MemPageUtils;
import com.sankuai.shangou.supplychain.tof.wrapper.DeliveryServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.FulfillmentOrderServiceWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/15 17:09
 **/
@Service
@Slf4j
public class ThirdDeliveryPickingAssembler {
    @Resource
    private TradeOrderInfoComponent tradeOrderInfoComponent;

    @Resource
    private TradeShippingOrderComponent tradeShippingOrderComponent;

    @Resource
    private FulfillmentOrderServiceWrapper fulfillmentOrderServiceWrapper;

    @Resource
    private RevenueComponent revenueComponent;

    @Resource
    private AbnormalOrderComponent abnormalOrderComponent;

    @Resource
    private DeliveryOrderComponent deliveryOrderComponent;

    @Resource
    private DeliveryServiceWrapper deliveryServiceWrapper;

    /**
     * 查询待领取的拣货单列表
     * @param page 页码
     * @param pageSize 页大小
     * @return List<PickingOrderResponseVO>
     */
    public ThirdDeliveryPickingOrderListResponse queryWaitToTake(int page, int pageSize) {
        return queryThirdDeliveryPickingOrderTemplate(() -> {
            return tradeShippingOrderComponent.getRecentListByOperatorIdAndStatusList(
                    LoginContextUtils.getAppLoginStoreId(),
                    null,
                    Lists.newArrayList(TradeShippingOrderStatus.WAITED.getCode()));
        }, page, pageSize, Arrays.asList(ComponentTypeEnum.ORDER_REVENUE_INFO, ComponentTypeEnum.LACK_STOCK));
    }

    /**
     * 查询待拣货的拣货单列表
     * @param page 页码
     * @param pageSize 页大小
     * @return List<PickingOrderResponseVO>
     */
    public ThirdDeliveryPickingOrderListResponse queryWaitToPick(int page, int pageSize) {
        return queryThirdDeliveryPickingOrderTemplate(() -> {
            return tradeShippingOrderComponent.getRecentListByOperatorIdAndStatusList(
                    LoginContextUtils.getAppLoginStoreId(),
                    LoginContextUtils.getAppLoginUser().getAccountId(),
                    Lists.newArrayList(TradeShippingOrderStatus.RECEIVE.getCode()));
        }, page, pageSize, Arrays.asList(ComponentTypeEnum.ORDER_REVENUE_INFO, ComponentTypeEnum.LACK_STOCK));
    }

    private ThirdDeliveryPickingOrderListResponse queryThirdDeliveryPickingOrderTemplate(Supplier<List<TradeShippingOrderDTO>> queryTradeShippingOrderFunction,
                                                                                         int page, int pageSize,
                                                                                         List<ComponentTypeEnum> extInfoEnums) {
        //1. 先查拣货单
        List<TradeShippingOrderDTO> shippingOrderDTOS = queryTradeShippingOrderFunction.get();

        shippingOrderDTOS = IListUtils.nullSafeFilterElement(shippingOrderDTOS, tradeShippingOrderDTO -> Objects.nonNull(tradeShippingOrderDTO.getIsThirdDeliveryPick()) && (tradeShippingOrderDTO.getIsThirdDeliveryPick()));
        if (CollectionUtils.isEmpty(shippingOrderDTOS)) {
            return new ThirdDeliveryPickingOrderListResponse(0, false, Collections.emptyList());
        }
        Map<TradeOrderInfoComponent.TradeOrderKey, TradeShippingOrderDTO> shippingOrderDTOMap = shippingOrderDTOS.stream()
                .collect(Collectors.toMap(tradeShippingOrderDTO -> new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO.getTradeChannelType(), tradeShippingOrderDTO.getTradeOrderNo()), Function.identity(), (k1, k2) -> k2));

        //2. 查履约单 && 内存分页
        List<FulfillmentOrderDetailDTO> fulfillmentOrderDetailDTOS = getFulfillmentOrderDetailDTOS(shippingOrderDTOS);
        List<ViewIdCondition> allViewIdConditions = fulfillmentOrderDetailDTOS
                .stream()
                //只要三方配送的
                .filter(fulfillmentOrderDetailDTO -> Objects.nonNull(fulfillmentOrderDetailDTO.getDeliveryInfo()) && Objects.equals(fulfillmentOrderDetailDTO.getDeliveryInfo().getDeliveryPlatform(), DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode()))
                //排序后内存分页
                .sorted(Comparator.comparingLong(FulfillmentOrderDetailDTO::getEstimatedEndTime))
                .map(fulfillmentOrderDetailDTO -> new ViewIdCondition(fulfillmentOrderDetailDTO.getOrderSource(), fulfillmentOrderDetailDTO.getChannelOrderId()))
                .collect(Collectors.toList());

        MemPageUtils.PageInfo<ViewIdCondition> viewIdConditionPageInfo = MemPageUtils.pagingList(allViewIdConditions, page, pageSize);


        if (CollectionUtils.isEmpty(viewIdConditionPageInfo.getPagedList())) {
            return new ThirdDeliveryPickingOrderListResponse(0, false, Collections.emptyList());
        }

        List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeys = viewIdConditionPageInfo.getPagedList().stream()
                .map(item -> new TradeOrderInfoComponent.TradeOrderKey(item.getOrderBizType(), item.getViewOrderId()))
                .collect(Collectors.toList());

        //3. 查订单信息
        Map<TradeOrderInfoComponent.TradeOrderKey, OCMSOrderVO> ocmsOrderMap = tradeOrderInfoComponent.queryTradeOrderInfoList(new ArrayList<>(shippingOrderDTOMap.keySet()));
        //build订单模块
        Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap = ocmsOrderMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> tradeOrderInfoComponent.buildTradeOrderVO(entry.getValue()),
                        (older, newer) -> newer
                ));

        //4. 查营收数据,失败不阻塞流程
        Map<TradeOrderInfoComponent.TradeOrderKey, RevenueDetailVo> revenueDetailVoMap = Collections.emptyMap();
        if (extInfoEnums.contains(ComponentTypeEnum.ORDER_REVENUE_INFO)) {
            try {
                revenueDetailVoMap = revenueComponent.revenueComponent(LoginContextUtils.getAppLoginTenant(), tradeOrderKeys);
            } catch (Exception e) {
                log.error("查询营收数据失败", e);
            }
        }

        //5. 查库存不足标签,失败不阻塞流程
        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> lackStockTagMap = Collections.emptyMap();
        if (extInfoEnums.contains(ComponentTypeEnum.LACK_STOCK)) {
            try {
                lackStockTagMap = abnormalOrderComponent.getLackStockTag(tradeOrderKeys);
            } catch (Exception e) {
                log.error("查询异常单失败", e);
            }
        }

        //6. 查运单
        List<TDeliveryDetail> tDeliveryDetails = deliveryServiceWrapper.pageQueryThirdDeliveryOrderList(
                LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginStoreId(),
                Lists.newArrayList(DeliveryStatusEnum.DELIVERY_LAUNCHED.getCode(), DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode(),
                        DeliveryStatusEnum.RIDER_ASSIGNED.getCode(), DeliveryStatusEnum.RIDER_ARRIVED_SHOP.getCode(), DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode()), false,
                1, 10);
        Map<TradeOrderInfoComponent.TradeOrderKey, TDeliveryDetail> tDeliveryDetailMap = tDeliveryDetails.stream()
                .collect(Collectors.toMap(tDeliveryDetail -> new TradeOrderInfoComponent.TradeOrderKey(tDeliveryDetail.orderBizType, tDeliveryDetail.channelOrderId), Function.identity(), (k1, k2) -> k2));

        List<ThirdDeliveryPickingOrderVO> thirdDeliveryOrderVOS = RiderPickingConvert.buildThirdDeliveryPickingOrderVOList(tradeOrderKeys, tradeOrderVOMap, revenueDetailVoMap, lackStockTagMap, tDeliveryDetailMap, shippingOrderDTOMap, ocmsOrderMap);

        return new ThirdDeliveryPickingOrderListResponse(allViewIdConditions.size(), viewIdConditionPageInfo.getTotalPage() > page, thirdDeliveryOrderVOS);

    }


    private List<FulfillmentOrderDetailDTO> getFulfillmentOrderDetailDTOS(List<TradeShippingOrderDTO> recentListResult) {
        List<ChannelOrderIdKeyReq> channelOrderIdKeyReqs = recentListResult.stream().map(
                tradeShippingOrderDTO -> new ChannelOrderIdKeyReq(tradeShippingOrderDTO.getTradeChannelType(), tradeShippingOrderDTO.getTradeOrderNo())
        ).collect(Collectors.toList());

        List<FulfillmentOrderDetailDTO> fulfillmentOrderDetailDTOList = fulfillmentOrderServiceWrapper.searchFulfillmentOrderListByBatchFulfillmentOrderId(channelOrderIdKeyReqs);

        //两遍列表不相等就打个点
        if(Objects.equals(fulfillmentOrderDetailDTOList.size(), recentListResult.size())) {
            Cat.logEvent("RIDER_OFFLINE_PROMOTE_ERROR", "SHIPPING_FULFILLMENT_MISMATCH");
        }

        return fulfillmentOrderDetailDTOList;
    }

}
