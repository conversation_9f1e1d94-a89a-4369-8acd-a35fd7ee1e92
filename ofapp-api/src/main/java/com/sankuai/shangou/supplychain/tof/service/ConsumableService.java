package com.sankuai.shangou.supplychain.tof.service;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.meituan.mafka.client.producer.AsyncProducerResult;
import com.meituan.mafka.client.producer.FutureCallback;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.pullnew.adaptor.FortuneWineConfigQueryVO;
import com.sankuai.shangou.commons.auth.login.context.LoginUser;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.retry.RetryTemplateUtil;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.ConsumableItemInfo;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.enums.Activity;
import com.sankuai.shangou.logistics.warehouse.enums.ConsumableGoodsType;
import com.sankuai.shangou.logistics.warehouse.enums.Process;
import com.sankuai.shangou.logistics.warehouse.infrastructure.dto.StoreLocationDTO;
import com.sankuai.shangou.logistics.warehouse.inventory.dto.LocationInventoryDTO;
import com.sankuai.shangou.supplychain.tof.controller.vo.consumable.ConsumableItemVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.consumable.ConsumableOutMsg;
import com.sankuai.shangou.supplychain.tof.controller.vo.consumable.request.ConsumableOutRequest;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import com.sankuai.shangou.supplychain.tof.wrapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/6 15:37
 **/
@Service
@Slf4j
public class ConsumableService {
    @Resource
    private StockWrapper stockWrapper;

    @Resource
    private TradeShippingOrderService tradeShippingOrderService;

    @Resource
    private HuServiceWrapper huServiceWrapper;

    @Resource
    private TFortuneWineServiceWrapper fortuneWineServiceWrapper;

    @Resource
    private LocationServiceWrapper locationServiceWrapper;

    @Resource
    private DepotGoodsWrapper depotGoodsWrapper;

    private final String fortuneWineBizCode = "FA_CAI_WINE";

    @Autowired
    @Qualifier("consumableOutProducer")
    private IProducerProcessor<Object, String> consumableOutProducer;

    public List<ConsumableItemVO> queryConsumableList(String channelOrderId, Integer channelId, Long merchantId,
                                                      Long warehouseId) {
        // 查询普通耗材（type = 0）
        List<ConsumableItemInfo> normalConsumableList = MccConfigUtil.getNormalConsumableList(warehouseId);
        List<ConsumableItemVO> normalConsumableVOList = normalConsumableList.stream().map(ConsumableService::transform2VO).collect(Collectors.toList());
        //查询出库单详情
        TradeShippingOrderDTO shippingOrderDTO = null;
        try {
            shippingOrderDTO = RetryTemplateUtil.simpleWithFixedRetry(3, 100).execute(new RetryCallback<TradeShippingOrderDTO, Exception>() {
                @Override
                public TradeShippingOrderDTO doWithRetry(RetryContext context) throws Exception {
                    TResult<List<TradeShippingOrderDTO>> tResult =
                            tradeShippingOrderService.getByTradeOrderNos(warehouseId, ChannelOrderConvertUtils.convertBizType(channelId), Collections.singletonList(channelOrderId));
                    if (!tResult.isSuccess() || CollectionUtils.isEmpty(tResult.getData())) {
                        throw new BizException("查询出库单详情失败");
                    }
                    return tResult.getData().get(0);
                }
            });
        } catch (Exception e) {
            Cat.logEvent("CONSUMABLE", "QUERY_TRADE_SHIPPING_ORDER_FAIL");
            log.error("耗材接口查询拣货单详情失败", e);
            return normalConsumableVOList;
        }

        //如果是封签交付订单
        if (GrayConfigUtils.judgeIsGrayStore(merchantId, warehouseId, GrayKeyEnum.SEAL_DELIVERY.getGrayKey())) {
            boolean isSealDeliveryOrder = shippingOrderDTO.getItems().stream().anyMatch(item -> Objects.equals(item.getNeedSealDelivery(), true));
            if (isSealDeliveryOrder) {
                List<ConsumableItemVO> sealDeliveryConsumableInfo = getSealDeliveryConsumableInfo(warehouseId, merchantId);
                normalConsumableVOList.addAll(sealDeliveryConsumableInfo);
            }
        }

        //如果是发财酒订单
        boolean isFortuneOrder = Objects.equals(shippingOrderDTO.getIsFortuneWineOrder(), true);
        if (isFortuneOrder) {
            Optional<ConsumableItemVO> fortuneWineConsumableInfoOpt = getFortuneWineConsumableInfo(warehouseId);
            fortuneWineConsumableInfoOpt.ifPresent(normalConsumableVOList::add);
        }

        return normalConsumableVOList;
    }

    public void consumableOut(ConsumableOutRequest request, Long warehouseId, LoginUser user) throws Exception {
        ConsumableOutMsg outMsg = new ConsumableOutMsg(user.getTenantId(),
                warehouseId, request.getViewOrderId(), user.getEmployeeId(),
                String.valueOf(user.getAccountId()), user.getEmployeeName(),
                System.currentTimeMillis(), request.getItems().stream()
                .filter(item -> new BigDecimal(item.getQuantity()).compareTo(BigDecimal.ZERO) > 0)
                .map(item -> new ConsumableOutMsg.ConsumableItem(item.getSkuId(),
                        new BigDecimal(item.getQuantity()))).collect(Collectors.toList()));

        if (org.apache.commons.collections.CollectionUtils.isEmpty(outMsg.getItems())) {
            return;
        }
        consumableOutProducer.sendAsyncMessage(JSON.toJSONString(outMsg), new FutureCallback() {
            @Override
            public void onSuccess(AsyncProducerResult result) {
            }

            @Override
            public void onFailure(Throwable t) {
                throw new SystemException("耗材随单出库失败");
            }
        });
        log.info("发送耗材出库消息成功, msg:{}", JSON.toJSONString(outMsg));
    }

    private List<ConsumableItemVO> getSealDeliveryConsumableInfo(Long warehouseId, Long merchantId) {
        // 获取封签扣、封签容具、安心包
        List<ConsumableItemInfo> sealConsumableItemList = MccConfigUtil.getSealConsumableList(warehouseId);
        List<ConsumableItemVO> sealConsumableItemVOList = sealConsumableItemList.stream().map(ConsumableService::transform2VO).collect(Collectors.toList());
        Map<String, ConsumableItemVO> sealConsumableMap = sealConsumableItemVOList.stream().collect(Collectors.toMap(ConsumableItemVO::getSkuId, Function.identity()));
        List<String> needCheckStockSkuIds = sealConsumableItemVOList.stream().map(ConsumableItemVO::getSkuId).collect(Collectors.toList());
        // 安心包 skuIds
        List<String> safePackageSkuIds = MccConfigUtil.getAllTypeConsumableList(warehouseId)
                .stream()
                .filter(it -> Objects.equals(it.getType(), ConsumableGoodsType.SAFE_PACKAGE.getCode()))
                .map(ConsumableItemInfo::getSkuId)
                .collect(Collectors.toList());
        try {
            // 查询封签扣、封签容具、安心包可用库存
            Map<String, BigDecimal> stockAvailable = RetryTemplateUtil.simpleWithFixedRetry(3, 100)
                    .execute((RetryCallback<Map<String, BigDecimal>, Exception>) context -> {
                        return stockWrapper.querySaleZoneSkuStockAvailable(warehouseId, merchantId, needCheckStockSkuIds);
                    });

            //查询封签容具占用数量
            Integer sealContainerUsingCount = RetryTemplateUtil.simpleWithFixedRetry(3, 100)
                    .execute(new RetryCallback<Integer, Exception>() {
                        @Override
                        public Integer doWithRetry(RetryContext context) throws Exception {
                            return huServiceWrapper.queryWarehouseUsingSealContainerCount(merchantId, warehouseId);
                        }
                    });

            // 计算封签容具可用数量。封签容具可用数量 = max(封签容具可用库存 - 封签容具占用数量, 0)
            stockAvailable.forEach((skuId, value) -> {
                if(Objects.equals(sealConsumableMap.get(skuId).getType(), ConsumableGoodsType.SEAL_CONTAINER.getCode())) {
                    int usableStock =  Math.max(value.subtract(new BigDecimal(sealContainerUsingCount)).intValue(),  BigDecimal.ZERO.intValue());
                    stockAvailable.put(skuId, new BigDecimal(usableStock));
                }
            });
            // 更新 currentStockQuantity 字段，currentStockQuantity 为可用的库存量
            sealConsumableItemVOList.forEach(
                    consumableItemVO -> consumableItemVO.setCurrentStockQuantity(stockAvailable.getOrDefault(consumableItemVO.getSkuId(), BigDecimal.ZERO).stripTrailingZeros().toPlainString())
            );

            // 反查安心包的 SKU 信息，获取 UPC
            if (CollectionUtils.isNotEmpty(safePackageSkuIds)) {
                Map<String, DepotGoodsDetailDto> skuMap = depotGoodsWrapper.queryBySkuId(merchantId, warehouseId, safePackageSkuIds);
                sealConsumableItemVOList.forEach(it -> {
                    if (skuMap.containsKey(it.getSkuId())) {
                        it.setUpcList(skuMap.get(it.getSkuId()).getUpcList());
                    }
                });
            }

        } catch (Exception e) {
            log.error("查询耗材库存失败", e);
            Cat.logEvent("CONSUMABLE", "QUERY_SEAL_STOCK_ERROR");
        }

        return sealConsumableItemVOList;
    }

    private Optional<ConsumableItemVO> getFortuneWineConsumableInfo(Long warehouseId) {
        try {
            //查询发财酒配置
            FortuneWineConfigQueryVO fortuneWineConfigQueryVO = fortuneWineServiceWrapper.queryStoreConfig(warehouseId);
            String fortuneWineSkuId = fortuneWineConfigQueryVO.getGiftGoodsCode();

            //查货品信息
            Map<String, DepotGoodsDetailDto> goodsDetailDtoMap =
                    depotGoodsWrapper.queryBySkuId(LoginContextUtils.getAppLoginTenant(), warehouseId, Collections.singletonList(fortuneWineSkuId));
            if (!goodsDetailDtoMap.containsKey(fortuneWineSkuId)) {
                throw new BizException("未查到发财酒货品信息");
            }
            DepotGoodsDetailDto fortuneWineGoodsDetailDto = goodsDetailDtoMap.get(fortuneWineSkuId);

            //查询发财酒在仓内的库位
            List<StoreLocationDTO> sourceLocationList = locationServiceWrapper.getSourceLocation(warehouseId,
                    Process.PICK_SELECT_OUT.getCode() + "-" + Activity.PICK_SELECT.getCode() , fortuneWineBizCode);
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(sourceLocationList)) {
                throw new BizException("查询发财酒仓内库位失败");
            }
            StoreLocationDTO sourceLocationDTO = sourceLocationList.get(0);

            //查询库位库存
            List<LocationInventoryDTO> locationInventoryDTOS = stockWrapper.queryInventoryByLocationAndSkuId(warehouseId,
                    Collections.singletonList(sourceLocationDTO.getCode()), Collections.singletonList(fortuneWineSkuId));

            //计算可用库存
            BigDecimal availableQuantity = Optional.ofNullable(locationInventoryDTOS)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(locationInventoryDTO -> locationInventoryDTO.getQuantity().subtract(locationInventoryDTO.getLockQuantity()))
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);

            ConsumableItemVO consumableItemVO = new ConsumableItemVO();
            consumableItemVO.setType(ConsumableGoodsType.FORTUNE_WINE.getCode());
            consumableItemVO.setSkuId(fortuneWineConfigQueryVO.getGiftGoodsCode());
            consumableItemVO.setSkuName(fortuneWineGoodsDetailDto.getGoodsName());
            consumableItemVO.setMaxQuantity(fortuneWineConfigQueryVO.getReceiveLimit().toString());
            consumableItemVO.setWarnStockQuantity(fortuneWineConfigQueryVO.getStockWarning().toString());
            consumableItemVO.setCurrentStockQuantity(availableQuantity.toPlainString());
            consumableItemVO.setNeedCheckStockQuantity(true);

            return Optional.of(consumableItemVO);
        } catch (Exception e) {
            log.error("查询发财酒信息失败", e);
            Cat.logEvent("CONSUMABLE", "QUERY_FORTUNE_WINE_INFO_FAIL");
            return Optional.empty();
        }

    }

    private static ConsumableItemVO transform2VO(ConsumableItemInfo info) {
        ConsumableItemVO consumableItemVO = new ConsumableItemVO();
        consumableItemVO.setType(info.getType());
        consumableItemVO.setSkuId(info.getSkuId());
        consumableItemVO.setSkuName(info.getSkuName());
        consumableItemVO.setMaxQuantity(info.getMaxQuantity());
        consumableItemVO.setWarnStockQuantity(info.getWarnStockQuantity());
        consumableItemVO.setNeedCheckStockQuantity(info.getNeedCheckStockQuantity());
        return consumableItemVO;
    }
}
