package com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/14
 */
@TypeDoc(
        description = "骑手操作请求"
)
@Data
public class RiderOperateRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "配送单号", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Long deliveryOrderId;

    @FieldDoc(
            description = "订单号", requiredness = Requiredness.REQUIRED
    )
    private String channelOrderId;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    private Integer channelId;

    @FieldDoc(
            description = "1-领取拣配 2-领取配送", requiredness = Requiredness.REQUIRED
    )
    private Integer acceptType;

    @FieldDoc(
            description = "骑手经度", requiredness =  Requiredness.OPTIONAL
    )
    private String longitude;

    @FieldDoc(
            description = "骑手纬度", requiredness = Requiredness.OPTIONAL
    )
    private String latitude;

    @FieldDoc(
            description = "骑手位置信息", requiredness = Requiredness.OPTIONAL
    )
    private LocationInfo locationInfo;
}
