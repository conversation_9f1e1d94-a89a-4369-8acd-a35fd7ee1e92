package com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 查询骑手正在已送达+已取消的订单的请求.
 */
@TypeDoc(
        description = "查询骑手已送达+已取消的订单的请求"
)
@Data
public class QueryPickCompletedOrderRequest extends PageQueryRequest {


    @FieldDoc(
            description = "送达日期", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private String date;

    public String validate() {
        try {
            LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            return "日期格式不合法";
        }

        return "";
    }
}
