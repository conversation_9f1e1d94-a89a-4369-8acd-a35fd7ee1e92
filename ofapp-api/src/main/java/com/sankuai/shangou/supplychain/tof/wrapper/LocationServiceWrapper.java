package com.sankuai.shangou.supplychain.tof.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.shangou.common.Result;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.logistics.warehouse.LocationService;
import com.sankuai.shangou.logistics.warehouse.definition.dto.StoreLocationTypeDTO;
import com.sankuai.shangou.logistics.warehouse.enums.AccountLocationType;
import com.sankuai.shangou.logistics.warehouse.infrastructure.dto.StoreLocationDTO;
import com.sankuai.shangou.logistics.warehouse.infrastructure.location.LocationDTO;
import com.sankuai.shangou.logistics.warehouse.process.service.ProcessService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/2/10 15:38
 **/
@Rhino
@Slf4j
public class LocationServiceWrapper {
    @Resource
    private LocationService locationService;


    @Resource
    private ProcessService processService;

    @Degrade(rhinoKey = "LocationServiceWrapper.getByAccount", fallBackMethod = "getByAccountFallback", timeoutInMilliseconds = 2000)
    public Map<Long, LocationDTO> getByAccount(Long warehouseId, List<Long> accountIds, AccountLocationType accountLocationType) {
        Result<Map<Long, LocationDTO>> result;
        try {
            log.info("start invoke locationService.getByAccount, warehouseId: {}, accountIds: {}", warehouseId, accountIds);
            result = locationService.getByAccount(warehouseId, accountIds, accountLocationType);
            log.info("end invoke locationService.getByAccount, result: {}", result);
        } catch (Exception e) {
            log.error("查询员工库位失败", e);
            throw new ThirdPartyException("查询员工库位失败");
        }

        if (!result.isSuccess()) {
            throw new BizException("查询员工库位失败");
        }

        return result.getModule();

    }

    @Degrade(rhinoKey = "LocationServiceWrapper.getSourceLocation", fallBackMethod = "getSourceLocationFallback", timeoutInMilliseconds = 2000)
    public List<StoreLocationDTO> getSourceLocation(Long warehouseId, String processActivityCode, String fortuneWineBizCode) {
        Result<List<StoreLocationDTO>> result;
        try {
            log.info("start invoke locationService.getSourceLocation, warehouseId: {}", warehouseId);
            result = processService.getCandidateSourceLocations(warehouseId, processActivityCode, fortuneWineBizCode);
            log.info("end invoke locationService.getSourceLocation, result: {}", result);
        } catch (Exception e) {
            log.error("查询仓内库位失败", e);
            throw new ThirdPartyException("查询仓内库位失败");
        }

        if (!result.isSuccess()) {
            throw new BizException("查询仓内库位失败");
        }

        return result.getModule();
    }

    public List<StoreLocationDTO> getSourceLocationFallback(Long warehouseId, String processActivityCode, String fortuneWineBizCode) {
        log.error("LocationServiceWrapper.getSourceLocation 发生降级");
        return Collections.emptyList();
    }

    public Map<Long, LocationDTO> getByAccountFallback(Long warehouseId, List<Long> accountIds, AccountLocationType accountLocationType) {
        log.error("LocationServiceWrapper.getByAccount 发生降级");
        return Collections.emptyMap();
    }
}
