package com.sankuai.shangou.supplychain.tof.wrapper;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.utils.retry.RetryTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023-12-19
 * @email <EMAIL>
 */
@Slf4j
@Service
public class SquirrelWrapper {

    public static final String LACK_STOCK_MESSAGE_CATEGORY = "LACK_STOCK_MESSAGE_CATEGORY";

    @Resource(name = "redisBean")
    private RedisStoreClient redisClient;

    @MethodLog(logRequest = false, logResponse = true)
    public <T> boolean set(String categoryName, String key, T data) {
        if (StringUtils.isEmpty(key) || data == null || StringUtils.isEmpty(categoryName)) {
            log.error("SquirrelOperateService.set param error . key:{},data:{},categoryName:{}", key, data, categoryName);
            return false;
        }
        try {
            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(3, 50).execute(new RetryCallback<Boolean, Exception>() {
                @Override
                public Boolean doWithRetry(RetryContext retryContext) throws Exception {
                    StoreKey storeKey = new StoreKey(categoryName, key);
                    return redisClient.set(storeKey, JsonUtil.toJson(data));
                }
            });
            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.set error . key:{},data:{},categoryName:{}", key, data, categoryName, e);
        }
        return false;
    }

    @MethodLog(logRequest = false, logResponse = true)
    public <T> boolean setnx(String categoryName, String key, T data) {
        if (StringUtils.isEmpty(key) || data == null || StringUtils.isEmpty(categoryName)) {
            log.error("SquirrelOperateService.set param error . key:{},data:{},categoryName:{}", key, data, categoryName);
            return false;
        }
        try {
            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(3, 50).execute(new RetryCallback<Boolean, Exception>() {
                @Override
                public Boolean doWithRetry(RetryContext retryContext) throws Exception {
                    StoreKey storeKey = new StoreKey(categoryName, key);
                    return redisClient.setnx(storeKey, JsonUtil.toJson(data));
                }
            });
            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.set error . key:{},data:{},categoryName:{}", key, data, categoryName, e);
        }
        return false;
    }


    @MethodLog(logRequest = false, logResponse = true)
    public <T> Optional<T> get(String categoryName, String key, Class<T> clazz) {
        if (StringUtils.isEmpty(key)) {
            log.info("SquirrelOperateService.get key is empty ,key:{}", key);
            return Optional.empty();
        }

        try {
            StoreKey storeKey = new StoreKey(categoryName, key);
            String result = RetryTemplateUtil.simpleWithFixedRetry(3, 50).execute(new RetryCallback<String, Exception>() {
                @Override
                public String doWithRetry(RetryContext retryContext) throws Exception {
                    return redisClient.get(storeKey);
                }
            });
            if (StringUtils.isEmpty(result)) {
                return Optional.empty();
            }
            T valObj = JsonUtil.fromJson(result, clazz);
            if (valObj == null) {
                return Optional.empty();
            }
            return Optional.of(valObj);
        } catch (Exception e) {
            log.info("SquirrelOperateService.get error ,key:{}", key, e);
        }
        return Optional.empty();
    }

    @MethodLog(logRequest = false, logResponse = true)
    public <T> boolean delete(String categoryName, String key) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(categoryName)) {
            log.error("SquirrelOperateService.set param error . key:{}, categoryName:{}", key, categoryName);
            return false;
        }
        try {
            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(3, 50).execute(new RetryCallback<Boolean, Exception>() {
                @Override
                public Boolean doWithRetry(RetryContext retryContext) throws Exception {
                    StoreKey storeKey = new StoreKey(categoryName, key);
                    return redisClient.delete(storeKey);
                }
            });
            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.set error . key:{},categoryName:{}", key, categoryName, e);
        }
        return false;
    }
}
