package com.sankuai.shangou.supplychain.tof.controller.vo.picking;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.TConsumableMaterialInfo;
import com.sankuai.shangou.logistics.warehouse.dto.PickConsumableItemDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderItemDTO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/28 15:23
 **/
@Data
public class PickingOrderVO {
    @FieldDoc(
            description = "仓库id",
            requiredness = Requiredness.REQUIRED
    )
    private Long warehouseId;
    @FieldDoc(
            description = "商家id",
            requiredness = Requiredness.REQUIRED
    )
    private Long merchantId;
    @FieldDoc(
            description = "出库单号",
            requiredness = Requiredness.REQUIRED
    )
    private String orderNo;
    @FieldDoc(
            description = "交易渠道类型",
            requiredness = Requiredness.REQUIRED
    )
    private Integer tradeChannelType;
    @FieldDoc(
            description = "交易订单号",
            requiredness = Requiredness.REQUIRED
    )
    private String tradeOrderNo;
    @FieldDoc(
            description = "单据状态",
            requiredness = Requiredness.REQUIRED
    )
    private Integer status;
    @FieldDoc(
            description = "创单时间",
            requiredness = Requiredness.REQUIRED
    )
    private LocalDateTime createTime;
    @FieldDoc(
            description = "发货时间",
            requiredness = Requiredness.OPTIONAL
    )
    private LocalDateTime shipTime;

    @FieldDoc(
            description = "创单时间",
            requiredness = Requiredness.REQUIRED
    )
    private LocalDateTime updateTime;

    @FieldDoc(
            description = "出库单子项",
            requiredness = Requiredness.REQUIRED
    )
    private List<TradeShippingOrderItemDTO> items;

    /**--- 以下属性只为迁移数据使用，不在标准化协议内容中出现 ---*/
    @FieldDoc(
            description = "id",
            requiredness = Requiredness.OPTIONAL
    )
    @Deprecated
    private Long id;

    @FieldDoc(
            description = "id",
            requiredness = Requiredness.OPTIONAL
    )
    @Deprecated
    private Long operatorId;

    @FieldDoc(
            description = "id",
            requiredness = Requiredness.OPTIONAL
    )
    private List<String> pickingCheckPictureUrlList;

    @FieldDoc(
            description = "履约单id",
            requiredness = Requiredness.OPTIONAL
    )
    private Long fulfillmentOrderId;

    @FieldDoc(
            description = "拣货耗材信息",
            requiredness = Requiredness.OPTIONAL
    )
    private List<PickConsumableItemDTO> pickConsumableItems;

    @FieldDoc(
            description = "封签是否降级",
            requiredness = Requiredness.OPTIONAL
    )
    private Boolean isSealDegrade;

    @FieldDoc(
            description = "封签是否降级",
            requiredness = Requiredness.OPTIONAL
    )
    private Boolean isPickDeliverySplit;


    @FieldDoc(
            description = "操作人",
            requiredness = Requiredness.OPTIONAL
    )
    private String operatorName;
    /**--- end ---*/
}
