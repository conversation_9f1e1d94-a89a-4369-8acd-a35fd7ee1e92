package com.sankuai.shangou.supplychain.tof.controller.vo.seal;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/2 11:29
 **/

@AllArgsConstructor
@NoArgsConstructor
@Data
public class UsingSealContainerVO {
    @FieldDoc(
            description = "门店id"
    )
    private Long warehouseId;

    @FieldDoc(
            description = "容具编码列表"
    )
    private List<String> sealContainerCode;

    @FieldDoc(
            description = "订单号"
    )
    private String tradeOrderNo;

    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    @FieldDoc(
            description = "骑手id"
    )
    private Long riderAccountId;

    @FieldDoc(
            description = "骑手名字"
    )
    private String riderName;

    @FieldDoc(
            description = "骑手账号名"
    )
    private String riderAccountName;

    @FieldDoc(
            description = "配送状态"
    )
    private Integer deliveryStatus;

    @FieldDoc(
            description = "是否已暂停配送"
    )
    private Boolean isLockedDelivery;

    @FieldDoc(
            description = "出库时间"
    )
    private Long stockOutTime;

    @FieldDoc(
            description = "逾期时间 单位: 秒"
    )
    private Integer timeoutDuration;
}
