package com.sankuai.shangou.supplychain.tof.controller.vo.abnormal;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class AbnOrderItemVO {
    @FieldDoc(description = "货品名称")
    private String goodsName;
    @FieldDoc(description = "货品SKU编号")
    private String skuId;
    @FieldDoc(description = "货品实拍图")
    private String picUrl;
    @FieldDoc(description = "货品规格")
    private String spec;
    @FieldDoc(description = "货品upc编号列表")
    private List<String> upcList;
    @FieldDoc(description = "要货量")
    private String needCount;
    @FieldDoc(description = "剩余可售库存")
    private String salableCount;
    @FieldDoc(description = "缺货量")
    private String lackCount;
}
