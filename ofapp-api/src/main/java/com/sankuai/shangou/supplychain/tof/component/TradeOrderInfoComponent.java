package com.sankuai.shangou.supplychain.tof.component;

import com.dianping.lion.client.Lion;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.common.enums.OrderCanOperateItem;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderKey;
import com.meituan.shangou.saas.o2o.dto.request.OCMSOperateCheckRequest;
import com.meituan.shangou.saas.o2o.dto.response.OCMSOperateCheckResponse;
import com.meituan.shangou.saas.o2o.enums.TagModelType;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListViewIdConditionResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.*;
import com.meituan.shangou.saas.order.management.client.enums.SortByEnum;
import com.meituan.shangou.saas.order.management.client.enums.SortFieldEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderTagEnum;
import com.meituan.shangou.saas.service.ocms.OCMSOrderOperateThriftService;
import com.meituan.shangou.saas.utils.AddressSceneConvertUtils;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryOrderType;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.supplychain.tof.config.LionConfigUtils;
import com.sankuai.shangou.supplychain.tof.controller.vo.SortedOrderTagVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.*;
import com.sankuai.shangou.supplychain.tof.enums.AuthCodeEnum;
import com.sankuai.shangou.supplychain.tof.enums.ChannelTypeEnum;
import com.sankuai.shangou.supplychain.tof.enums.CouldOperateItemEnum;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import com.sankuai.shangou.supplychain.tof.wrapper.AuthThriftWrapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-12-18
 * @email <EMAIL>
 */
@Slf4j
@Component
public class TradeOrderInfoComponent {

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;
    @Resource
    private AuthThriftWrapper authThriftWrapper;
    @Resource
    private OCMSOrderOperateThriftService ocmsOrderOperateThriftService;

    //履约标签
    private static final int FULFILLMENT_TAG = 1;
    //拣货标准
    private static final int PICK_TAG = 2;

    private static final List<Integer> TO_CHECK_ITEMS = Lists.newArrayList(
            OrderCanOperateItem.PART_ORDER_REFUND.getValue(),
            OrderCanOperateItem.FULL_ORDER_REFUND.getValue()
    );

    @MethodLog(logRequest = true, logResponse = true)
    public Map<TradeOrderKey, TradeOrderVO> tradeOrderInfoComponent(List<TradeOrderKey> tradeOrderKeyList) {
        try {
            if (CollectionUtils.isEmpty(tradeOrderKeyList)) {
                return Collections.emptyMap();
            }

            OCMSListViewIdConditionRequest viewIdConditionRequest = buildOCMSListViewIdConditionRequestByRiderOrders(tradeOrderKeyList);
            log.info("RiderDeliveryServiceWrapper call OcmsQueryThriftService.queryOrderByViewIdCondition request:{}",
                    viewIdConditionRequest);
            OCMSListViewIdConditionResponse viewIdConditionResponse =
                    ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
            log.info("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition response:{}",
                    viewIdConditionResponse);
            if (viewIdConditionResponse.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                log.warn("获取订单详情失败, viewOrderId:{}",
                        viewIdConditionRequest.getViewIdConditionList().stream().map(ViewIdCondition::getViewOrderId).collect(Collectors.toList()));
                throw new SystemException(viewIdConditionResponse.getStatus().getMessage());
            }
            if (CollectionUtils.isEmpty(viewIdConditionResponse.getOcmsOrderList())) {
                return Maps.newHashMap();
            }
            return viewIdConditionResponse
                    .getOcmsOrderList()
                    .stream()
                    .collect(Collectors.toMap(
                            it -> new TradeOrderKey(it.getOrderBizType(), it.getViewOrderId()),
                            this::buildTradeOrderVO,
                            (older,  newer) -> newer
                    ));
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition error", e);
            throw new SystemException("查订单信息失败", e);
        }
    }


    public Map<TradeOrderKey, OCMSOrderVO> queryTradeOrderInfoList(List<TradeOrderKey> tradeOrderKeyList) {
        try {
            if (CollectionUtils.isEmpty(tradeOrderKeyList)) {
                return Collections.emptyMap();
            }

            OCMSListViewIdConditionRequest viewIdConditionRequest = buildOCMSListViewIdConditionRequestByRiderOrders(tradeOrderKeyList);
            log.info("RiderDeliveryServiceWrapper call OcmsQueryThriftService.queryOrderByViewIdCondition request:{}",
                    viewIdConditionRequest);
            OCMSListViewIdConditionResponse viewIdConditionResponse =
                    ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
            log.info("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition response:{}",
                    viewIdConditionResponse);
            if (viewIdConditionResponse.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                log.warn("获取订单详情失败, viewOrderId:{}",
                        viewIdConditionRequest.getViewIdConditionList().stream().map(ViewIdCondition::getViewOrderId).collect(Collectors.toList()));
                throw new SystemException(viewIdConditionResponse.getStatus().getMessage());
            }
            if (CollectionUtils.isEmpty(viewIdConditionResponse.getOcmsOrderList())) {
                return Maps.newHashMap();
            }
            return viewIdConditionResponse
                    .getOcmsOrderList()
                    .stream()
                    .collect(Collectors.toMap(
                            it -> new TradeOrderKey(it.getOrderBizType(), it.getViewOrderId()),
                            Function.identity(),
                            (older,  newer) -> newer
                    ));
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition error", e);
            throw new SystemException("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition error", e);
        }
    }

    public Map<TradeOrderKey, OCMSOrderVO> queryTradeOrderInfoListByPartition(List<TradeOrderKey> tradeOrderKeyList) {
        try {
            if (CollectionUtils.isEmpty(tradeOrderKeyList)) {
                return Collections.emptyMap();
            }
            List<OCMSOrderVO> ocmsOrderList = Lists.newArrayList();
            List<List<TradeOrderKey>> partition = Lists.partition(tradeOrderKeyList, LionConfigUtils.getPartitionSize());
            for (List<TradeOrderKey> partitionTradeOrderKeys : partition) {
                OCMSListViewIdConditionRequest viewIdConditionRequest = buildOCMSListViewIdConditionRequestByRiderOrders(partitionTradeOrderKeys);
                log.info("RiderDeliveryServiceWrapper call OcmsQueryThriftService.queryOrderByViewIdCondition request:{}",
                        viewIdConditionRequest);
                OCMSListViewIdConditionResponse viewIdConditionResponse =
                        ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
                log.info("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition response:{}",
                        viewIdConditionResponse);
                if (viewIdConditionResponse.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                    log.warn("获取订单详情失败, viewOrderId:{}",
                            viewIdConditionRequest.getViewIdConditionList().stream().map(ViewIdCondition::getViewOrderId).collect(Collectors.toList()));
                    throw new SystemException(viewIdConditionResponse.getStatus().getMessage());
                }
                if (CollectionUtils.isNotEmpty(viewIdConditionResponse.getOcmsOrderList())) {
                    ocmsOrderList.addAll(viewIdConditionResponse.getOcmsOrderList());
                }
            }

            return ocmsOrderList
                    .stream()
                    .collect(Collectors.toMap(
                            it -> new TradeOrderKey(it.getOrderBizType(), it.getViewOrderId()),
                            Function.identity(),
                            (older,  newer) -> newer
                    ));
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition error", e);
            throw new SystemException("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition error", e);
        }
    }

    /**
     * 根据骑手运单，构造查询订单信息的请求.
     *
     * @param tradeOrderKeyList 查询运单条件
     */
    private OCMSListViewIdConditionRequest buildOCMSListViewIdConditionRequestByRiderOrders(List<TradeOrderKey> tradeOrderKeyList) {
        OCMSListViewIdConditionRequest request = new OCMSListViewIdConditionRequest();
        request.setTenantId(AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId());
        request.setViewIdConditionList(tradeOrderKeyList.stream().filter(Objects::nonNull)
                .map(tradeOrderKey -> new ViewIdCondition(tradeOrderKey.getOrderBizType(), tradeOrderKey.getChannelOrderId()))
                .collect(Collectors.toList()));
        request.setSortField(SortFieldEnum.ORDER_CREATE_TIME);
        request.setSort(SortByEnum.DESC);
        request.setNeedSortedTags(Boolean.TRUE);
        return request;
    }

    public TradeOrderVO buildTradeOrderVO(OCMSOrderVO ocmsOrderVO) {
        TradeOrderVO vo = new TradeOrderVO();
        vo.setTenantId(ocmsOrderVO.getTenantId());
        vo.setOrderId(ocmsOrderVO.getOrderId());
        Integer channelId = ChannelOrderConvertUtils.sourceBiz2Mid(ocmsOrderVO.getOrderBizType());
        vo.setChannelId(channelId);
        vo.setOrderBizType(ocmsOrderVO.getOrderBizType());
        vo.setChannelName(Optional.ofNullable(channelId).map(ChannelTypeEnum::findChannelNameByChannelId).orElse(null));
        vo.setStoreId(ocmsOrderVO.getShopId());
        vo.setStoreName(ocmsOrderVO.getShopName());
        vo.setChannelOrderId(ocmsOrderVO.getViewOrderId());
        vo.setSerialNo(ocmsOrderVO.getOrderSerialNumber());
        DeliveryOrderType deliveryOrderType = ocmsOrderVO.getIsBooking() == 1 ?
                DeliveryOrderType.DELIVERY_BY_BOOK_TIME : DeliveryOrderType.DELIVERY_RIGHT_NOW;
        vo.setDeliveryOrderType(deliveryOrderType.getValue());
        vo.setDeliveryOrderTypeName(getDeliveryOrderTypeName(deliveryOrderType.getValue()));
        vo.setCreateTime(ocmsOrderVO.getCreateTime());
        vo.setPayTime(ocmsOrderVO.getPayTime() != null && ocmsOrderVO.getPayTime() > 0 ? ocmsOrderVO.getPayTime() : ocmsOrderVO.getCreateTime());
        vo.setActualPayAmt(ocmsOrderVO.getActualPayAmt());
        OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrderVO.getOcmsDeliveryInfoVO();
        if (ocmsDeliveryInfoVO != null) {
            // 自提配送 or 送货上门
            vo.setDeliveryMethod(ocmsDeliveryInfoVO.getDistributeMethod());
            vo.setDeliveryMethodDesc(ocmsDeliveryInfoVO.getDistributeMethodName());

            vo.setEstimateArriveTimeStart(ocmsDeliveryInfoVO.getArrivalTime());
            vo.setEstimateArriveTimeEnd(ocmsDeliveryInfoVO.getArrivalEndTime());
            vo.setReceiverName(ocmsDeliveryInfoVO.getUserName());
            vo.setReceiverPhone(ocmsDeliveryInfoVO.getUserPhone());
            vo.setReceiverAddress(ocmsDeliveryInfoVO.getUserAddress());

        }
        vo.setUserId(ocmsOrderVO.getUserId());
        // 商品信息 start
        List<OCMSOrderItemVO> ocmsOrderItemVOList = ocmsOrderVO.getOcmsOrderItemVOList();
        Integer itemCount = ocmsOrderItemVOList == null ? 0 : ocmsOrderItemVOList.stream().filter(Objects::nonNull)
                .mapToInt(OCMSOrderItemVO::getQuantity).sum();
        vo.setItemCount(itemCount);
        // 备注不为空且不为0才展示
        if (StringUtils.isNotEmpty(ocmsOrderVO.getComments()) && !ocmsOrderVO.getComments().equals("0")) {
            vo.setComments(ocmsOrderVO.getComments());
        }
        // 商品列表
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(ocmsOrderItemVOList)) {
            vo.setProductList(ocmsOrderItemVOList.stream().filter(Objects::nonNull)
                    .map(item -> this.buildProductVO(ocmsOrderVO.getShopId(), item)).collect(Collectors.toList()));
        }
        vo.setTotalOfflinePrice(ocmsOrderVO.getTotalOfflinePrice());
        // 赠品信息
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(ocmsOrderVO.getOnlineGiftVOS())) {
            vo.setGiftVOList(ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull)
                    .map(giftItem -> this.buildGiftVO(ocmsOrderVO.getShopId(), giftItem)).collect(Collectors.toList()));
            vo.setGiftCount(ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull)
                    .mapToInt(OnlineGiftVO::getGiftQuantity).sum());
        }

        //礼袋信息
        if (MccConfigUtil.getGiftBagSwitch()) {
            vo.setGiftBagList(GiftBagVO.buildDrunkHorseGiftBag(ocmsOrderVO.getGiftBagList()));
        }

        // 商品信息 end

        // 展示用户标签
        vo.setUserTags(UserTagTypeEnum.getTags(ocmsOrderVO.getTags()));
        vo.setSortedTagList(SortedOrderTagVO.buildListOfMng(ocmsOrderVO.getSortedTagList()));
        vo.setOrderUserType(ocmsOrderVO.getUserType());

        vo.setSelfPickPullNewOrder(ocmsOrderVO.getSelfPickPullNewOrder());

        vo.setOrderStatus(ocmsOrderVO.getOrderStatus());
        //场景信息补充
        if (Objects.nonNull(ocmsOrderVO.getOcmsDeliveryInfoVO())
                && StringUtils.isNotBlank(ocmsOrderVO.getOcmsDeliveryInfoVO().getCategory())
                && LionConfigUtils.isDhScenePoi(ocmsOrderVO.getShopId())
        ) {
            vo.setScene(buildScene(ocmsOrderVO.getOcmsDeliveryInfoVO().getCategory()));
        }

        try {
            vo.setIsWiderShippingArea(ocmsOrderVO.getWiderShippingArea());
            vo.setReceiverTailPhoneNumber(buildLastFourDigitsOfUser(ocmsOrderVO));
        } catch (Exception e) {
            log.error("append ReceiverTailPhoneNumber error", e);
        }
        // 名酒馆标签
        vo.setIsMtFamousTavern(ocmsOrderVO.getIsMtFamousTavern());
        vo.setIsFacaiWine(Optional.ofNullable(ocmsOrderVO.getIsFacaiWine()).orElse(false));
        vo.setDeliveryPosition(buildDeliveryPosition(ocmsOrderVO.getDeliveryPosition()));
        return vo;
    }

    /**
     * 放置点处理，添加前缀
     *
     * @param deliveryPosition
     * @return
     */
    public static String buildDeliveryPosition(String deliveryPosition) {
        if (StringUtils.isBlank(deliveryPosition)) {
            return deliveryPosition;
        }
        String deliveryPositionPrefix = LionConfigUtils.getDeliveryPositionPrefix();
        if (StringUtils.isBlank(deliveryPositionPrefix)) {
            return deliveryPosition;
        }
        return deliveryPositionPrefix + deliveryPosition;
    }

    private String buildLastFourDigitsOfUser(OCMSOrderVO ocmsOrderVO) {
        OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrderVO.getOcmsDeliveryInfoVO();
        String userPrivacyPhone = ocmsDeliveryInfoVO.getUserPrivacyPhone();
        // 歪马租户的微商城渠道，收货人手机号和收货人隐私号取值相反
        if (Objects.equals(ocmsOrderVO.getOrderBizType(), DynamicOrderBizType.MEITUAN_DRUNK_HOURSE.getValue())) {
            userPrivacyPhone = ocmsDeliveryInfoVO.getUserPhone();
        }
        if (StringUtils.isBlank(userPrivacyPhone) || userPrivacyPhone.length() <= 4) {
            return userPrivacyPhone;
        }
        return userPrivacyPhone.substring(userPrivacyPhone.length() - 4);
    }

    private ProductVO buildProductVO(long storeId,OCMSOrderItemVO ocmsOrderItemVO) {
        ProductVO productVO = new ProductVO();
        productVO.setSkuId(ocmsOrderItemVO.getCustomerSkuId());
        productVO.setUpcCode(ocmsOrderItemVO.getSkuCode());
        productVO.setSkuName(ocmsOrderItemVO.getSkuName());
        productVO.setPicUrl(ocmsOrderItemVO.getPicUrl());
        productVO.setSpecification(ocmsOrderItemVO.getSpecification());
        productVO.setSellUnit(ocmsOrderItemVO.getSellUnit());
        if (Objects.nonNull(ocmsOrderItemVO.getOriginalPrice())) {
            productVO.setOriginalTotalPrice(ocmsOrderItemVO.getOriginalPrice() * ocmsOrderItemVO.getQuantity());
        }
        productVO.setTotalPayAmount(ocmsOrderItemVO.getTotalPayAmount());
        productVO.setUnitPrice(ocmsOrderItemVO.getUnitPrice());
        productVO.setCount(ocmsOrderItemVO.getQuantity());
        productVO.setOrderItemOfflinePrice(ocmsOrderItemVO.getOfflinePrice());
        //商品标签(需要排除履约和拣货标签)
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVO.getPropertyList())) {
            List<ProductVO.ParsedPropertiesVO> propertiesViewVOList = ocmsOrderItemVO.getPropertyList().stream()
                    .filter(property -> getPropertyColorMap().containsKey(property))
                    .map(property -> new ProductVO.ParsedPropertiesVO(property, getPropertyColorMap().get(property)))
                    .collect(Collectors.toList());
            productVO.setParsedProperties(propertiesViewVOList);
        }

        List<TagInfoVO> tagInfoVOS = org.assertj.core.util.Lists.newArrayList();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(ocmsOrderItemVO.getFulfillmentTagList())) {
            for (String fulfillmentTag : ocmsOrderItemVO.getFulfillmentTagList()) {
                TagInfoVO tagInfoVO = new TagInfoVO();
                tagInfoVO.setName(fulfillmentTag);
                tagInfoVO.setType(FULFILLMENT_TAG);
                tagInfoVOS.add(tagInfoVO);
            }
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(ocmsOrderItemVO.getPickTagList())) {
            for (String pickTag : ocmsOrderItemVO.getPickTagList()) {
                TagInfoVO tagInfoVO = new TagInfoVO();
                tagInfoVO.setName(pickTag);
                tagInfoVO.setType(PICK_TAG);
                tagInfoVOS.add(tagInfoVO);

            }
        }
        //商品标签(需要排除履约和拣货标签)
        productVO.setTagInfos(Optional.ofNullable(ocmsOrderItemVO.getTagInfos())
                .orElse(Collections.emptyList()).stream().map(tag -> {
                    TagInfoVO tagInfoVO = new TagInfoVO();
                    tagInfoVO.setName(tag.getName());
                    tagInfoVO.setType(tag.getType());
                    return tagInfoVO;
                }).collect(Collectors.toList()));
        productVO.setTagInfoList(tagInfoVOS);

        productVO.setCurrentPrice(ocmsOrderItemVO.getCurrentPrice());
        productVO.setOriginalProperties(ocmsOrderItemVO.getPropertyList());
        return productVO;
    }

    private GiftVO buildGiftVO(Long storeId, OnlineGiftVO onlineGiftVO) {
        if (onlineGiftVO != null) {
            GiftVO giftVO = new GiftVO();
            giftVO.setGiftName(onlineGiftVO.getGiftName());
            giftVO.setGiftQuantity(onlineGiftVO.getGiftQuantity());
            giftVO.setBelongSkuId(onlineGiftVO.getMainSkuId());
            giftVO.setSku(onlineGiftVO.getGiftSku());
            if(StringUtils.isNotBlank(onlineGiftVO.getGiftSpec())) {
                giftVO.setSpecification(onlineGiftVO.getGiftSpec());
            }
            return giftVO;
        }
        return null;
    }

    private String getDeliveryOrderTypeName(Integer deliveryOrderType) {
        DeliveryOrderType deliveryOrderTypeEnum = DeliveryOrderType.findByValue(deliveryOrderType);
        if (Objects.isNull(deliveryOrderTypeEnum)) {
            return "未知";
        }
        switch (deliveryOrderTypeEnum) {
            case DELIVERY_RIGHT_NOW:
                return "立即送达";
            case DELIVERY_BY_BOOK_TIME:
                return "预订";
            default:
                return "未知";
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradeOrderKey {

        private int orderBizType;

        private String channelOrderId;

    }

    public static enum UserTagTypeEnum {


        /**
         * 收藏
         */
        FAVORITES(OrderTagEnum.FAVORITES.getCode(), "收藏店铺", 1) {
            @Override
            public String getName(String value) {
                if (String.valueOf(1).equals(value)) {
                    return this.getTemplate();
                }
                return null;
            }
        },

        /**
         * 订单次数
         */
        ORDER_COUNT(OrderTagEnum.ORDER_COUNT.getCode(), "下单{0}次", 0) {
            @Override
            public String getName(String value) {
                // 订单中心获取的次数是已完成的订单、value实际为已支付状态对应的订单、已完成订单不包含当前订单
                int count = NumberUtils.toInt(value, 0);
                return count < 1 ? "门店新客" : MessageFormat.format(this.getTemplate(), count + 1);
            }
        },

        /**
         * 微商城新客
         */
        DRUNK_HORSE_NEW_USER(TagModelType.DRUNK_HORSE_NEW_CUSTOMER.getCode(), "微商城新客", 2) {
            @Override
            public String getName(String value) {
                if (String.valueOf(1).equals(value)) {
                    return this.getTemplate();
                }
                return null;
            }
        };

        /**
         * 根据sort排序
         */
        private static List<UserTagTypeEnum> userTagTypeEnums = Arrays.stream(UserTagTypeEnum.values())
                .sorted(Comparator.comparingInt(UserTagTypeEnum::getSort)).collect(Collectors.toList());

        private int code;

        private int sort;

        private String template;


        UserTagTypeEnum(int code, String template, int sort) {
            this.code = code;
            this.template = template;
            this.sort = sort;
        }


        public int getCode() {
            return code;
        }

        public String getTemplate() {
            return template;
        }

        public int getSort() {
            return sort;
        }

        public String getName(String value) {
            return this.template;
        }

        public static List<TagInfoVO> getTags(List<OrderTagVo> tags) {
            if (CollectionUtils.isEmpty(tags)) {
                return null;
            }
            Map<Integer, OrderTagVo> tagMap = tags.stream()
                    .collect(Collectors.toMap(OrderTagVo::getType, tag -> tag, (old, current) -> old));
            return userTagTypeEnums.stream()
                    .map(userTagTypeEnum -> {
                        OrderTagVo tag = tagMap.get(userTagTypeEnum.getCode());
                        if (tag == null) {
                            return null;
                        }
                        String name = userTagTypeEnum.getName(tag.getValue());
                        if (StringUtils.isEmpty(name)) {
                            return null;
                        }
                        TagInfoVO tagInfoVO = new TagInfoVO();
                        tagInfoVO.setType(tag.getType());
                        tagInfoVO.setName(name);
                        return tagInfoVO;
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
    }


    /**
     * 获取歪马商品侧的温度属性与拣货侧的温度属性映射关系
     * 拣货的配置，统一存放在OIO里
     */
    private static Map<String, String> getPropertyColorMap() {
        Map<String, String> defaultMap = new HashMap<>();
        defaultMap.put("冰冻","#198CFF");
        defaultMap.put("冷藏","#198CFF");
        defaultMap.put("冰镇","#198CFF");
        defaultMap.put("常温","#FF6A00");
        defaultMap.put("一半冰一半不冰","#198CFF");
        defaultMap.put("自定义：备注","#999999");
        defaultMap.put("需要红酒开瓶器","#FF192D");

        return Lion.getConfigRepository("com.sankuai.shangou.logistics.oio").getMap("drunk.horse.property.color.map", String.class, defaultMap);
    }


    private String buildScene(String category) {
        try {
            return AddressSceneConvertUtils.convertCategory2Scene(category);
        } catch (Exception e) {
            log.error("buildScene error", e);
        }
        return null;
    }


    public Map<TradeOrderInfoComponent.TradeOrderKey, List<Integer>> queryAbnOrderCouldOperateItemsMap(Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap) {
        try {
            if (MapUtils.isEmpty(tradeOrderVOMap)) {
                return Maps.newHashMap();
            }

            OCMSOperateCheckRequest request = new OCMSOperateCheckRequest();
            request.setToCheckOperateItems(TO_CHECK_ITEMS);
            request.setOrderList(
                    IListUtils.mapTo(tradeOrderVOMap.values(), tradeOrderVO -> new OCMSOrderKey(tradeOrderVO.getChannelOrderId(), tradeOrderVO.getChannelId()))
            );
            request.setTenantId(AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId());
            OCMSOperateCheckResponse response = ocmsOrderOperateThriftService.checkOrderCouldOperateItems(request);
            log.info("ocmsOrderOperateThriftService.checkOrderCouldOperateItems req = {}, response = {}", request, response);
            if (MapUtils.isEmpty(response.getCouldOperateItems())) {
                return Maps.newHashMap();
            }

            //操作人要有权限
            Map<String, Boolean> permissions = authThriftWrapper.isHasPermission(ImmutableList.of(AuthCodeEnum.DH_EXCEPTION_REFUND_BTN.getAuthCode(), AuthCodeEnum.DH_EXCEPTION_CANCEL_BTN.getAuthCode()));
            boolean hasRefundAuth = permissions.getOrDefault(AuthCodeEnum.DH_EXCEPTION_REFUND_BTN.getAuthCode(), Boolean.FALSE);
            boolean hasCancelAuth = permissions.getOrDefault(AuthCodeEnum.DH_EXCEPTION_CANCEL_BTN.getAuthCode(), Boolean.FALSE);

            Map<TradeOrderKey, List<Integer>> couldOperateItemsMap = response.getCouldOperateItems().entrySet()
                    .stream()
                    .collect(Collectors.toMap(
                            entry -> new TradeOrderKey(DynamicOrderBizType.channelId2OrderBizType(entry.getKey().getChannelType()).getValue(), entry.getKey().getChannelOrderId()),
                            entry -> {
                                List<Integer> thisCouldOperateItems = Lists.newArrayList();
                                if(hasRefundAuth && entry.getValue().contains(OrderCanOperateItem.PART_ORDER_REFUND.getValue())) {
                                    thisCouldOperateItems.add(CouldOperateItemEnum.DH_EXCEPTION_REFUND_BTN.getValue());
                                }
                                if (hasCancelAuth && entry.getValue().contains(OrderCanOperateItem.FULL_ORDER_REFUND.getValue())) {
                                    thisCouldOperateItems.add(CouldOperateItemEnum.DH_EXCEPTION_CANCEL_BTN.getValue());
                                }
                                return thisCouldOperateItems;
                            }
                    ));

            return couldOperateItemsMap;
        } catch (Exception e) {
            log.error("queryTurnDeliveryButton error", e);
            return Maps.newHashMap();
        }
    }

}
