package com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 查询骑手正在已送达+已取消的订单的请求.
 */
@TypeDoc(
        description = "查询骑手已送达+已取消的订单的请求"
)
@Data
public class QueryIncrementRiderCompletedOrderRequest {


    @FieldDoc(
            description = "最后更新时间", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private long lastUpdateTime;

    @FieldDoc(
            description = "是否需要返回统计数据", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Boolean needReturnStatisticData;

}
