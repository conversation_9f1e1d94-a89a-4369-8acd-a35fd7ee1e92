package com.sankuai.shangou.supplychain.tof.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.CompleteWithProofPhotoRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.verify.*;
import com.sankuai.shangou.supplychain.tof.controller.vo.verify.request.AuthorizeRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.verify.request.PostFailResultRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.verify.request.PostSuccessResultRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.verify.request.QueryEndTaskRequest;
import com.sankuai.shangou.supplychain.tof.service.VerifyTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/7/11 20:39
 **/
@Slf4j
@InterfaceDoc(
        type = "restful",
        displayName = "歪马配送人脸/着装/头盔验证相关接口",
        description = "歪马配送人脸/着装/头盔验证相关接口",
        scenarios = "主要应用于歪马自配场景"
)
@RestController
@RequestMapping("/api/orderfulfill/app/rider/verify")
public class VerifyTaskController {

    @Resource
    private VerifyTaskService verifyTaskService;

    @MethodDoc(
            displayName = "查询骑手当前未完成的验证任务",
            description = "查询骑手当前未完成的验证任务",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询骑手当前未完成的验证任务",
                            type = CompleteWithProofPhotoRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/verify/queryTask",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryTask", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    public VerifyTaskInfoVO queryTask() {
        return verifyTaskService.queryTaskInfo();
    }

    @MethodDoc(
            displayName = "获取验证授权码",
            description = "获取验证授权码",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "获取验证授权码",
                            type = CompleteWithProofPhotoRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/verify/authorize",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/authorize", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    public AuthorizeInfo authorize(@RequestBody AuthorizeRequest request) {
        String authorizeCode = verifyTaskService.getAuthorizeCode(request.getTaskId());
        return new AuthorizeInfo(authorizeCode);
    }

    @MethodDoc(
            displayName = "上报验证成功结果",
            description = "上报验证成功结果",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "上报验证成功结果",
                            type = CompleteWithProofPhotoRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/verify/postSuccessResult",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/postSuccessResult", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    public void postSuccessResult(@RequestBody PostSuccessResultRequest request) {
        verifyTaskService.postSuccessResult(request);
    }

    @MethodDoc(
            displayName = "上报验证失败结果",
            description = "上报验证失败结果",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "上报验证失败结果",
                            type = CompleteWithProofPhotoRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/verify/postFailResult",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/postFailResult", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    public PostVerifyResultResponse postFailResult(@RequestBody PostFailResultRequest request) {
        boolean taskCompleted = verifyTaskService.postFailResult(request);
        return new PostVerifyResultResponse(taskCompleted);
    }

    @MethodDoc(
            displayName = "查询已结束的微笑行动任务",
            description = "查询已结束的微笑行动任务",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询已结束的微笑行动任务",
                            type = QueryEndTaskRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/verify/queryEndTask",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = false, logRequest = true)
    @RequestMapping(value = "/queryEndTask", method = {RequestMethod.POST})
    @ResponseBody
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    public VerifyRiderEndTaskResponse queryEndTask(@RequestBody QueryEndTaskRequest request) {
        return verifyTaskService.queryRiderEndTask(request);
    }

    @MethodDoc(
            displayName = "查询骑手待采集的验证任务",
            description = "查询骑手待采集的验证任务",
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/verify/queryPendingTask",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = false)
    @RequestMapping(value = "/queryPendingTask", method = {RequestMethod.POST})
    @ResponseBody
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    public VerifyPendingTaskInfoVO queryPendingTask() {
        return verifyTaskService.queryPendingTask();
    }

}
