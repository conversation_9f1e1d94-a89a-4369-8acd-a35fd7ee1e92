package com.sankuai.shangou.supplychain.tof.controller.vo.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "配送异常详情VO"
)
@ApiModel("配送异常详情VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DeliveryExceptionDetailVO {
    @FieldDoc(
            description = "渠道id"
    )
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单id"
    )
    @ApiModelProperty(value = "渠道订单id", required = true)
    private String channelOrderId;

    @FieldDoc(
            description = "运单id"
    )
    @ApiModelProperty(value = "运单id", required = true)
    private String deliveryOrderId;

    @FieldDoc(
            description = "渠道日流水号"
    )
    @ApiModelProperty(value = "渠道日流水号", required = true)
    private Integer daySeq;

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id", required = true)
    private String storeId;

    @FieldDoc(
            description = "订单支付时间"
    )
    @ApiModelProperty(value = "订单支付时间", required = true)
    private Long payTime;

    @FieldDoc(
            description = "上报骑手id"
    )
    @ApiModelProperty(value = "上报骑手id", required = true)
    private String riderAccountId;

    @FieldDoc(
            description = "上报骑手名字"
    )
    @ApiModelProperty(value = "上报骑手名字", required = true)
    private String riderAccountName;

    @FieldDoc(
            description = "上报异常类型"
    )
    @ApiModelProperty(value = "上报异常类型", required = true)
    private Integer exceptionType;

    @FieldDoc(
            description = "上报异常二级类型"
    )
    @ApiModelProperty(value = "上报异常二级类型", required = true)
    private Integer exceptionSubType;

    @FieldDoc(
            description = "上报异常类型描述"
    )
    @ApiModelProperty(value = "上报异常类型描述", required = true)
    private String exceptionTypeDesc;

    @FieldDoc(
            description = "上报异常二级类型描述"
    )
    @ApiModelProperty(value = "上报异常二级类型描述", required = true)
    private String exceptionSubTypeDesc;

    @FieldDoc(
            description = "上报异常时间"
    )
    @ApiModelProperty(value = "上报异常时间", required = true)
    private Long reportTimeStamp;

    @FieldDoc(
            description = "照片URL列表",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "照片URL列表")
    private List<String> picUrls;

    @FieldDoc(
            description = "备注",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "备注")
    private String comment;

    @FieldDoc(
            description = "用户真实地址",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "用户真实地址")
    private String userRealAddress;

    @FieldDoc(
            description = "修改后的地址",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "修改后的地址")
    private String modifiedAddress;
}
