package com.sankuai.shangou.supplychain.tof.controller.vo.abnormal;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryOrderType;
import com.sankuai.shangou.supplychain.tof.config.LionConfigUtils;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.ShowTagVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.*;
import com.sankuai.shangou.supplychain.tof.enums.ShowTagEnum;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

@TypeDoc(description = "异常单及其缺货详情信息")
@Data
public class AbnormalOrderListVO {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    private Integer orderBizType;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    private String channelName;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    private String storeName;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    private String channelOrderId;

    @FieldDoc(
            description = "订单流水", requiredness = Requiredness.REQUIRED
    )
    private Long serialNo;

    @FieldDoc(
            description = "配送订单类型  1-立即单 2-预约单", requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryOrderType;

    @FieldDoc(
            description = "配送状态 30-待领取 40-骑手已接单 50-骑手已取货", requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryStatus;

    @FieldDoc(
            description = "配送订单类型名称", requiredness = Requiredness.REQUIRED
    )
    private String deliveryOrderTypeName;

    @FieldDoc(
            description = "预计送达时间开始时间", requiredness = Requiredness.REQUIRED
    )
    private Long estimateArriveTimeStart;

    @FieldDoc(
            description = "预计送达时间截止时间", requiredness = Requiredness.REQUIRED
    )
    private Long estimateArriveTimeEnd;

    @FieldDoc(
            description = "订单创建时间戳", requiredness = Requiredness.REQUIRED
    )
    private Long createTime;

    @FieldDoc(
            description = "配送方式,0-未知，1-配送到家，2-到店自提", requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryMethod;

    @FieldDoc(
            description = "改派前的骑手姓名", requiredness = Requiredness.OPTIONAL
    )
    private String fromRiderName;

    @FieldDoc(
            description = "配送方式描述", requiredness = Requiredness.REQUIRED
    )
    private String deliveryMethodDesc;

    @FieldDoc(
            description = "订单用户ID、0或-1为无效ID", requiredness = Requiredness.OPTIONAL
    )
    private Long userId;

    // 收货人信息 start

    @FieldDoc(
            description = "收货人姓名", requiredness = Requiredness.REQUIRED
    )
    private String receiverName;

    @FieldDoc(
            description = "收货人电话号码", requiredness = Requiredness.REQUIRED
    )
    private String receiverPhone;

    @FieldDoc(
            description = "收货人地址", requiredness = Requiredness.REQUIRED
    )
    private String receiverAddress;

    @FieldDoc(
            description = "收货人定位经度，精确到小数点后六位", requiredness = Requiredness.REQUIRED
    )
    private String receiverLongitude;

    @FieldDoc(
            description = "收货人定位纬度，精确到小数点后六位", requiredness = Requiredness.REQUIRED
    )
    private String receiverLatitude;

    // 收货人信息 end

    // 商品信息 start

    @FieldDoc(
            description = "商品总数量", requiredness = Requiredness.REQUIRED
    )
    private Integer itemCount;

    @FieldDoc(
            description = "备注", requiredness = Requiredness.OPTIONAL
    )
    private String comments;

    @FieldDoc(
            description = "商品信息列表", requiredness = Requiredness.REQUIRED
    )
    private List<ProductVO> productList;

    @FieldDoc(
            description = "订单营收数据", requiredness = Requiredness.OPTIONAL
    )
    private RevenueDetailVo revenueDetail;

    @FieldDoc(
            description = "提报价模式,订单线下价格总和", requiredness = Requiredness.OPTIONAL
    )
    private Integer totalOfflinePrice;

    @FieldDoc(
            description = "赠品信息列表", requiredness = Requiredness.OPTIONAL
    )
    private List<GiftVO> giftVOList;

    @FieldDoc(
            description = "赠品总数量", requiredness = Requiredness.OPTIONAL
    )
    private Integer giftCount;

    // 商品信息 end

    @FieldDoc(
            description = "支付时间", requiredness = Requiredness.OPTIONAL
    )
    private Long payTime;

    @FieldDoc(
            description = "用户类型信息 10-普通用户, 15-会员用户", requiredness = Requiredness.OPTIONAL
    )
    private Integer orderUserType;

    @FieldDoc(
            description = "运单状态是否被锁定：0-未锁定；1-锁定",
            example = {}
    )
    public Integer deliveryStatusLocked;

    @FieldDoc(
            description = "运单状态是否可以被锁定：0-不可以；1-可以",
            example = {}
    )
    public Integer canStatusBeLocked;


    @FieldDoc(
            description = "考核送达截止时间", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveDeadline;

    @FieldDoc(
            description = "考核送达剩余时间", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveLeftTime;

    @FieldDoc(
            description = "考核送达超时时间", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveTimeout;

    @FieldDoc(
            description = "配送距离，单位米", requiredness = Requiredness.OPTIONAL
    )
    private Long deliveryDistance;



    /** 异常订单相关内容 */
    //异常单id
    private String abnOrderId;
    //异常单创建原因
    private Integer abnormalOrderType;
    //异常单item vo
    private List<AbnOrderItemVO> lackStockGoodsList;

    /** 操作*/
    //是否已发送短信
    private boolean isAlreadySendMessage;

    //可操作按钮，300-退款 310-取消
    private List<Integer> couldOperateItemList;

    @FieldDoc(
            description = "歪马地推自提订单标记",
            example = {}
    )
    private Boolean selfPickPullNewOrder;

    @FieldDoc(
            description = "场景，如:餐馆", requiredness = Requiredness.OPTIONAL
    )
    private String scene;

    @FieldDoc(
            description = "展示标签，包括 大范围标签等", requiredness = Requiredness.OPTIONAL
    )
    private List<ShowTagVO> showTags;

    @FieldDoc(
            description = "收货人后4位尾号", requiredness = Requiredness.OPTIONAL
    )
    private String receiverTailPhoneNumber;

    @FieldDoc(
            description = "订单是否需要展示奖励文案",
            example = {}
    )
    public Boolean showRewardHint = false;

    @FieldDoc(
            description = "礼袋信息", requiredness = Requiredness.OPTIONAL
    )
    private List<GiftBagVO> giftBagList;

    @FieldDoc(
        description = "是否为美团名酒馆订单，true：是"
    )
    private Boolean isMtFamousTavern;

    @FieldDoc(
            description = "是否为发财酒订单"
    )
    private Boolean isFacaiWine;


    public static AbnormalOrderListVO buildAbnormalOrderListVO(AbnormalOrderVO abnormalOrderVO, TradeOrderVO tradeOrderVO,
                                                               DeliveryOrderVO deliveryOrderVO, RevenueDetailVo revenueDetail, List<Integer> couldOperateItem, boolean isDirectStore) {
        AbnormalOrderListVO abnormalOrderListVO = new AbnormalOrderListVO();
        //tradeOrder
        abnormalOrderListVO.setTenantId(tradeOrderVO.getTenantId());
        abnormalOrderListVO.setChannelId(tradeOrderVO.getChannelId());
        abnormalOrderListVO.setOrderBizType(tradeOrderVO.getOrderBizType());
        abnormalOrderListVO.setChannelName(tradeOrderVO.getChannelName());
        abnormalOrderListVO.setStoreId(tradeOrderVO.getStoreId());
        abnormalOrderListVO.setStoreName(tradeOrderVO.getStoreName());
        abnormalOrderListVO.setChannelOrderId(tradeOrderVO.getChannelOrderId());
        abnormalOrderListVO.setSerialNo(tradeOrderVO.getSerialNo());
        abnormalOrderListVO.setDeliveryOrderType(tradeOrderVO.getDeliveryOrderType());
        abnormalOrderListVO.setDeliveryOrderTypeName(tradeOrderVO.getDeliveryOrderTypeName());
        abnormalOrderListVO.setCreateTime(tradeOrderVO.getCreateTime());
        abnormalOrderListVO.setDeliveryMethod(tradeOrderVO.getDeliveryMethod());
        abnormalOrderListVO.setDeliveryMethodDesc(tradeOrderVO.getDeliveryMethodDesc());
        abnormalOrderListVO.setUserId(tradeOrderVO.getUserId());
        abnormalOrderListVO.setScene(tradeOrderVO.getScene());
        //礼袋信息
        abnormalOrderListVO.setGiftBagList(tradeOrderVO.getGiftBagList());

        //地推标记
        abnormalOrderListVO.setSelfPickPullNewOrder(tradeOrderVO.getSelfPickPullNewOrder());

        // 名酒馆标签
        abnormalOrderListVO.setIsMtFamousTavern(tradeOrderVO.getIsMtFamousTavern());

        //delivery
        if (Objects.nonNull(deliveryOrderVO)) {
            abnormalOrderListVO.setDeliveryStatus(deliveryOrderVO.getDeliveryStatus());
            abnormalOrderListVO.setEstimateArriveTimeStart(deliveryOrderVO.getEstimateArriveTimeStart());
            abnormalOrderListVO.setEstimateArriveTimeEnd(deliveryOrderVO.getEstimateArriveTimeEnd());
            abnormalOrderListVO.setFromRiderName(deliveryOrderVO.getFromRiderName());
            abnormalOrderListVO.setReceiverName(deliveryOrderVO.getReceiverName());
            abnormalOrderListVO.setReceiverPhone(deliveryOrderVO.getReceiverPhone());
            abnormalOrderListVO.setReceiverAddress(deliveryOrderVO.getReceiverAddress());
            abnormalOrderListVO.setReceiverLongitude(deliveryOrderVO.getReceiverLongitude());
            abnormalOrderListVO.setReceiverLatitude(deliveryOrderVO.getReceiverLatitude());
            abnormalOrderListVO.setDeliveryStatusLocked(deliveryOrderVO.getDeliveryStatusLocked());
            abnormalOrderListVO.setCanStatusBeLocked(deliveryOrderVO.getCanStatusBeLocked());
            abnormalOrderListVO.setEvaluateArriveDeadline(deliveryOrderVO.getEvaluateArriveDeadline());
            abnormalOrderListVO.setEvaluateArriveLeftTime(deliveryOrderVO.getEvaluateArriveLeftTime());
            abnormalOrderListVO.setEvaluateArriveTimeout(deliveryOrderVO.getEvaluateArriveTimeout());
            abnormalOrderListVO.setDeliveryDistance(deliveryOrderVO.getDeliveryDistance());
            //只有餐馆 && 实时单场景才展示奖励
            if (MccConfigUtil.getShowRewardHintSwitch()) {
                isDirectStore = true;
            }
            if (StringUtils.isNotBlank(tradeOrderVO.getScene())
                    && LionConfigUtils.getDhRestaurantSceneList().contains(tradeOrderVO.getScene())
                    && Objects.equals(tradeOrderVO.getDeliveryOrderType(), 1)
                    && isDirectStore
            ) {
                abnormalOrderListVO.setShowRewardHint(deliveryOrderVO.getShowRewardHint());
            }
        } else { //兼容无运单情况，目前是地推自提没有运单
            abnormalOrderListVO.setEstimateArriveTimeStart(tradeOrderVO.getEstimateArriveTimeStart());
            abnormalOrderListVO.setEstimateArriveTimeEnd(tradeOrderVO.getEstimateArriveTimeEnd());
            abnormalOrderListVO.setReceiverName(tradeOrderVO.getReceiverName());
            abnormalOrderListVO.setReceiverPhone(tradeOrderVO.getReceiverPhone());
            abnormalOrderListVO.setReceiverAddress(tradeOrderVO.getReceiverAddress());
            //预定单下发时,也没有运单
            if (LionConfigUtils.getNeedEstTimeWithoutDeliveryOrder()) {
                if (Objects.equals(tradeOrderVO.getDeliveryOrderType(), DeliveryOrderType.DELIVERY_BY_BOOK_TIME.getValue()) && Objects.nonNull(tradeOrderVO.getEstimateArriveTimeStart())) {
                    long tsNow = System.currentTimeMillis();
                    if (tsNow > tradeOrderVO.getEstimateArriveTimeStart()) {
                        // 配送中，已经超时
                        abnormalOrderListVO.setEvaluateArriveLeftTime(0L);
                        abnormalOrderListVO.setEvaluateArriveTimeout(tsNow - tradeOrderVO.getEstimateArriveTimeStart());
                    } else {
                        // 配送中，还未超时
                        abnormalOrderListVO.setEvaluateArriveLeftTime(tradeOrderVO.getEstimateArriveTimeStart() - tsNow);
                        abnormalOrderListVO.setEvaluateArriveTimeout(0L);
                    }
                }
            }
        }

        //sku
        abnormalOrderListVO.setItemCount(tradeOrderVO.getItemCount());
        abnormalOrderListVO.setComments(tradeOrderVO.getComments());
        abnormalOrderListVO.setProductList(tradeOrderVO.getProductList());
        abnormalOrderListVO.setTotalOfflinePrice(tradeOrderVO.getTotalOfflinePrice());
        abnormalOrderListVO.setGiftVOList(tradeOrderVO.getGiftVOList());
        abnormalOrderListVO.setGiftCount(tradeOrderVO.getGiftCount());
        abnormalOrderListVO.setPayTime(tradeOrderVO.getPayTime());
        abnormalOrderListVO.setOrderUserType(tradeOrderVO.getOrderUserType());

        List<ShowTagVO> showTags = Lists.newArrayList();
        if (Objects.nonNull(tradeOrderVO.getIsWiderShippingArea()) && tradeOrderVO.getIsWiderShippingArea()) {
            ShowTagVO widerTag = new ShowTagVO();
            widerTag.setTagDesc(LionConfigUtils.getTagHint(ShowTagEnum.WIDER_SHIPPING_AREA.getLionKey(), ShowTagEnum.WIDER_SHIPPING_AREA.getDefaultValue()));
            widerTag.setTagCode(ShowTagEnum.WIDER_SHIPPING_AREA.getCode());
            showTags.add(widerTag);
        }
        abnormalOrderListVO.setShowTags(showTags);

        abnormalOrderListVO.setReceiverTailPhoneNumber(tradeOrderVO.getReceiverTailPhoneNumber());

        if (Objects.nonNull(revenueDetail)) {
            abnormalOrderListVO.setRevenueDetail(revenueDetail);
        }
        //abn
        abnormalOrderListVO.setAbnOrderId(abnormalOrderListVO.getAbnOrderId());
        abnormalOrderListVO.setAbnormalOrderType(abnormalOrderVO.getAbnormalOrderType());
        abnormalOrderListVO.setLackStockGoodsList(abnormalOrderVO.getItems());
        abnormalOrderListVO.setAlreadySendMessage(abnormalOrderVO.isAlreadySendMessage());
        abnormalOrderListVO.setCouldOperateItemList(couldOperateItem);

        abnormalOrderListVO.setIsFacaiWine(tradeOrderVO.getIsFacaiWine());

        return abnormalOrderListVO;
    }
}
