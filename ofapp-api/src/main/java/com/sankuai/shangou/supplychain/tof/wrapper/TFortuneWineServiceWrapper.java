package com.sankuai.shangou.supplychain.tof.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.pullnew.FortuneWineConfigQueryRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.pullnew.adaptor.FortuneWineConfigQueryVO;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.thrift.pullnew.TFortuneWineService;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/2/6 17:15
 **/
@Rhino
@Slf4j
public class TFortuneWineServiceWrapper {
    @Resource
    private TFortuneWineService tFortuneWineService;

    @Degrade(rhinoKey = "TFortuneWineServiceWrapper.queryStoreConfig", fallBackMethod = "queryStoreConfigFallback",  timeoutInMilliseconds = 1000)
    public FortuneWineConfigQueryVO queryStoreConfig(Long warehouseId) {
        FortuneWineConfigQueryRequest request = new FortuneWineConfigQueryRequest();
        request.setStoreId(warehouseId);
        log.info("start invoke tFortuneWineService.queryStoreConfigByStoreId, request: {}", request);
        TResult<FortuneWineConfigQueryVO> tResult = tFortuneWineService.queryStoreConfigByStoreId(request);
        log.info("end invoke tFortuneWineService.queryStoreConfigByStoreId, tResult: {}", tResult);
        if (!tResult.isSuccess()) {
            throw new BizException("查询发财酒配置失败," + tResult.getMsg());
        }
        if (Objects.isNull(tResult.getData())) {
            throw new BizException("门店发财酒配置不存在");
        }
        return tResult.getData();
    }

    public FortuneWineConfigQueryVO queryStoreConfigFallback(Long warehouseId) {
        log.error("TFortuneWineServiceWrapper.queryStoreConfig 发生降级");
        throw new BizException("查询发财酒配置失败");
    }
}
