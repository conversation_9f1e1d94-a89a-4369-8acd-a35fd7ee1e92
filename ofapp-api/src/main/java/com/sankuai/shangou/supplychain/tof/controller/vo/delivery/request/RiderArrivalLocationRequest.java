package com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "上报骑手送达时刻位置请求"
)
@Data
public class RiderArrivalLocationRequest {
    @FieldDoc(
            description = "骑手账号id",requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Long riderAccountId;

    @FieldDoc(
            description = "运单id",requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Long deliveryOrderId;

    @FieldDoc(
            description = "定位信息", requiredness = Requiredness.OPTIONAL
    )
    @NotNull
    private LocationInfo locationInfo;

    @Data
    public static class LocationInfo {
        @NotNull
        private String latitude;

        @NotNull
        private String longitude;

        private String speed;

        private String time;

        private String bearing;

        private String accuracy;

        private String provider;
    }
}
