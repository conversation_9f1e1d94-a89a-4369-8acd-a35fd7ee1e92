package com.sankuai.shangou.supplychain.tof.controller.vo.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-06-12
 * @email <EMAIL>
 */
@Data
public class ShowTagVO {

    @FieldDoc(
            description = "标签文描", requiredness = Requiredness.OPTIONAL
    )
    private String tagDesc;

    @FieldDoc(
            description = "大范围标签", requiredness = Requiredness.OPTIONAL
    )
    private Integer tagCode;

}
