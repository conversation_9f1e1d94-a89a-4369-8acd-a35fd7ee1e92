package com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/03/14
 * desc: 查询虚拟电话请求
 */
@TypeDoc(
        description = "查询虚拟电话请求"
)
@Data
public class QueryVirtualPhoneRequest {

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private String channelOrderId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "查询电话类型  1-用户 2-骑手", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Integer phoneType;

    private Long storeId;

    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    private Integer entityType;
}
