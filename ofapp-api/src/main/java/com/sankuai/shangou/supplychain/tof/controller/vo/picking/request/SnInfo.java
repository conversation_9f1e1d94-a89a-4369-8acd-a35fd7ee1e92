package com.sankuai.shangou.supplychain.tof.controller.vo.picking.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/9/15 11:03
 **/
@TypeDoc(
        description = "sn信息"
)
@Data
public class SnInfo {
    @FieldDoc(
            description = "SN码", requiredness = Requiredness.REQUIRED
    )
    private String snCode;

    @FieldDoc(
            description = "SN码录入方式 1-扫码录入 2-手动录入", requiredness = Requiredness.REQUIRED
    )
    private Integer snCodeEnteringType;
}
