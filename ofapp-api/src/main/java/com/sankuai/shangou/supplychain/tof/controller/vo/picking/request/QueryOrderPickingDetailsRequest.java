package com.sankuai.shangou.supplychain.tof.controller.vo.picking.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Objects;
import java.util.Optional;

/**
 * 查询骑手已领取订单的待拣货内容的请求.
 *
 * <AUTHOR>
 * @since 2021/11/12 14:34
 */
@TypeDoc(
        description = "查询骑手已领取订单的待拣货内容的请求"
)
@Data
public class QueryOrderPickingDetailsRequest {

    @FieldDoc(
            description = "订单渠道", requiredness = Requiredness.REQUIRED
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private String channelOrderId;

    @FieldDoc(
            description = "运单ID", requiredness = Requiredness.REQUIRED
    )
    private Long deliveryOrderId;

    @FieldDoc(
            description = "是否是三方配送", requiredness = Requiredness.OPTIONAL
    )
    private Boolean isThirdDelivery;

    public Optional<String> validate() {
        if (StringUtils.isBlank(channelOrderId)) {
            return Optional.of("渠道订单号无效");
        }

        //非三方配送时必传deliveryOrderId
        if (!Objects.equals(isThirdDelivery, true) && (deliveryOrderId == null || deliveryOrderId <= 0)) {
            return Optional.of("运单 ID 无效");
        }

        return Optional.empty();
    }
}
