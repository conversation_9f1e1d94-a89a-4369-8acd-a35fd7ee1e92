package com.sankuai.shangou.supplychain.tof.controller.vo.delivery;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/28 14:45
 **/
@Data
public class DeliveryCompleteConfigVO {
    /**
     * 经营类型 1-直营 2-加盟 3赋能
     */
    private Integer operationMode;

    /**
     * 所有示例图信息
     */
    private List<ExamplePicInfo> allExamplePicInfoList;

    /**
     * 商品对应的上传送达照片配置
     */
    private List<ProductUploadPicConfig> specialProductUploadPicConfig;


    /**
     * 发送图片给用户提示文案
     */
    private String sendPicToCustomerTips;


    /**
     * 未联系用户提示文案
     */
    private String notContactCustomerTips;

    /**
     * 上传图片时长阈值 单位:秒
     */
    private Integer uploadImageDurationThreshold;

    /**
     * 是否展示未联系顾客提醒
     */
    private Boolean isShowNotContactCustomerTips;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExamplePicInfo {
        /**
         * 示例图类型
         */
        private int type;

        /**
         * 示例图名称
         */
        private String name;

        /**
         * 示例图url
         */
        private String picUrl;

        /**
         * 展示顺序
         */
        private Integer order;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProductUploadPicConfig {
        /**
         * 商品类型
         */
        private Integer productType;

        /**
         * 对应的示例图类型列表
         */
        private List<Integer> picTypeList;

        /**
         * 是否强制上传照片
         */
        private Boolean isForceUploadPic;

        /**
         * 需要上传的照片张数
         */
        private Integer needUploadPicCount;
    }
}
