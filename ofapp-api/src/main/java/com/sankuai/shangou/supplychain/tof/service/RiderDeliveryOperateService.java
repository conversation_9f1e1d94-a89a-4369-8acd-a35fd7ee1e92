package com.sankuai.shangou.supplychain.tof.service;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListViewIdConditionResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.enums.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaProducer;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.message.RiderChangeTransStockMsg;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.RiderOperateThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.CompleteWithProofPhotoTRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.*;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderChangeRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.response.RiderChangeResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.response.RiderOperateTResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryException;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.PageQueryDeliveryExceptionRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryDeliveryExceptionByChannelOrderRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryDeliveryOrderByIdListRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.BatchQueryDeliveryOrderResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.DeliveryExceptionResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.PageQueryDeliveryExceptionListResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.DrunkHorseTurnToMerchantSelfDeliveryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.TurnToMerchantSelfDeliveryResponse;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.shangou.commons.auth.login.context.LoginUser;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.request.UserContext;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.retry.RetryTemplateUtil;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.MaterialTransInfoDto;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.request.RiderChangeTransMaterialRequest;
import com.sankuai.shangou.logistics.warehouse.dto.request.RiderTakeAwayTransMaterialRequest;
import com.sankuai.shangou.logistics.warehouse.enums.PromotionMaterialType;
import com.sankuai.shangou.logistics.warehouse.enums.ResponseCodeEnum;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.CompleteWithProofPhotoRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.LocationInfo;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.RiderArrivalLocationRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.RiderOperateRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryExceptionDetailVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryExceptionInfoVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryExceptionSummaryVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.RiderLocationSyncRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.*;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.response.QueryDeliveryExceptionDetailListResponse;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.response.QueryDeliveryExceptionListResponse;
import com.sankuai.shangou.supplychain.tof.enums.ErrorCodeEnum;
import com.sankuai.shangou.supplychain.tof.utils.LimitTakeOrderUtils;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import com.sankuai.shangou.supplychain.tof.wrapper.DepotGoodsWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.LimitAcceptServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.OSWServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.OutboundServiceWrapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/1 16:56
 **/
@Service
@Slf4j
public class RiderDeliveryOperateService {
    @Resource
    private RiderOperateThriftService riderOperateThriftService;

    @Resource
    private RiderQueryThriftService riderQueryThriftService;

    @Resource
    private TradeShippingOrderService tradeShippingOrderService;

    @Resource
    private LimitAcceptServiceWrapper limitAcceptServiceWrapper;

    @Resource
    private OSWServiceWrapper oswServiceWrapper;

    @Resource
    private DeliveryOperationThriftService deliveryOperationRpcService;

    @Resource
    private OutboundServiceWrapper outboundServiceWrapper;

    @Resource
    private DepotGoodsWrapper depotGoodsWrapper;

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    //支付时间默认查询间隔 单位:月
    private static final int DEFAULT_PAY_TIME_QUERY_DURATION = 3;

    @MafkaProducer(namespace = "com.sankuai.mafka.castle.daojiacommon",
            appkey = "com.sankuai.shangou.supplychain.ofapp",
            topic = "rider_change_trans_material_stock_topic")
    private IProducerProcessor<Object, String> riderChangeTransStockProducer;


    private static final Logger operationLocationRecordLogger = LoggerFactory.getLogger("logger_drunk_horse_operation_location_record");

    public void accept(RiderOperateRequest request) {
        //校验限制
        if (GrayConfigUtils.judgeIsGrayStore(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginStoreId(),
                GrayKeyEnum.LIMIT_ACCEPT_ORDER.getGrayKey(), false)) {
            checkLimitAcceptItem(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginAccountId(), LoginContextUtils.getAppLoginStoreId());
        }

        //领取配送单
        acceptDeliveryOrder(request);

        //领取拣货单
        tryToAcceptPickTask(request);

    }

    public void riderArrivalLocation(RiderArrivalLocationRequest request) {
        RiderArrivalLocationTRequest tRequest = new RiderArrivalLocationTRequest();
        tRequest.setRiderAccountId(LoginContextUtils.getAppLoginAccountId());
        tRequest.setDeliveryOrderId(request.getDeliveryOrderId());
        tRequest.setTenantId(LoginContextUtils.getAppLoginTenant());
        tRequest.setStoreId(LoginContextUtils.getAppLoginStoreId());
        if (Objects.nonNull(request.getLocationInfo())) {
            RiderArrivalLocationTRequest.LocationInfo locationInfo = new RiderArrivalLocationTRequest.LocationInfo();
            locationInfo.setLongitude(request.getLocationInfo().getLongitude());
            locationInfo.setLatitude(request.getLocationInfo().getLatitude());
            locationInfo.setProvider(request.getLocationInfo().getProvider());
            locationInfo.setAccuracy(request.getLocationInfo().getAccuracy());
            locationInfo.setBearing(request.getLocationInfo().getBearing());
            locationInfo.setSpeed(request.getLocationInfo().getSpeed());
            locationInfo.setTime(request.getLocationInfo().getTime());
            locationInfo.setOs(AppLoginContextHolder.getAppLoginContext().getOs());
            tRequest.setLocationInfo(locationInfo);
        }

        RiderOperateTResponse response;
        try {
            log.info("start invoke riderOperateThriftService.riderArrivalLocation, request: {}", tRequest);
            response = riderOperateThriftService.riderArrivalLocation(tRequest);
            log.info("end invoke riderOperateThriftService.riderArrivalLocation, response: {}", response);
        } catch (Exception e) {
            log.error("上报到刻位置失败", e);
            throw new ThirdPartyException("上报到刻位置失败");
        }

        if (response == null || response.getStatus() == null) {
            throw new ThirdPartyException("上报到刻位置失败");
        }

        if (response.getStatus().getCode() != 0) {
            throw new BizException(response.getStatus().getCode(), response.getStatus().getMsg());
        }
    }

    public void completeWithProofPhoto(CompleteWithProofPhotoRequest request) {
        CompleteWithProofPhotoTRequest tRequest = new CompleteWithProofPhotoTRequest();
        tRequest.setDeliveryOrderId(request.getDeliveryOrderId());
        tRequest.setOperatorId(LoginContextUtils.getAppLoginAccountId());
        tRequest.setProofPhotoList(request.getPicList());
        tRequest.setOperatorName(LoginContextUtils.getAppLoginUser().getEmployeeName());
        tRequest.setOperatorPhone(LoginContextUtils.getAppLoginUser().getEmployeePhone());
        tRequest.setSignType(request.getSignType());
        tRequest.setTenantId(LoginContextUtils.getAppLoginTenant());
        tRequest.setStoreId(LoginContextUtils.getAppLoginStoreId());
        tRequest.setIsWeakNetWork(request.getIsWeakNetwork());
        if (request.getLocationInfo() != null) {
            tRequest.setLongitude(request.getLocationInfo().getLongitude());
            tRequest.setLatitude(request.getLocationInfo().getLatitude());
        }
        log.info("start invoke riderOperateRpcService.completeWithProofPhoto, request: {}", request);
        RiderOperateTResponse response = riderOperateThriftService.completeWithProofPhoto(tRequest);
        log.info("end invoke riderOperateRpcService.completeWithProofPhoto, response: {}", response);

        if (!Objects.equals(response.getStatus().getCode(), 0)) {
            throw new BizException(response.getStatus().getCode(), response.getStatus().getMsg());
        }
    }

    public void takeAway(Long deliveryOrderId, LocationInfo locationInfo, Integer channelId, String channelOrderId) {
        RiderOperateTRequest tRequest = new RiderOperateTRequest();
        tRequest.setOperatorPhone(LoginContextUtils.getAppLoginUser().getEmployeePhone());
        tRequest.setOperatorId(LoginContextUtils.getAppLoginUser().getAccountId());
        tRequest.setOperatorName(LoginContextUtils.getAppLoginUser().getEmployeeName());
        tRequest.setDeliveryOrderId(deliveryOrderId);
        tRequest.setStoreId(LoginContextUtils.getAppLoginStoreId());
        tRequest.setTenantId(LoginContextUtils.getAppLoginTenant());
        if (Objects.nonNull(locationInfo)) {
            tRequest.setLongitude(locationInfo.getLongitude());
            tRequest.setLatitude(locationInfo.getLatitude());
        }
        log.info("start invoke riderOperateThriftService.takeAway, request: {}", tRequest);
        RiderOperateTResponse response = riderOperateThriftService.takeAway(tRequest);
        log.info("end invoke riderOperateThriftService.takeAway, response: {}", response);

        if (!Objects.equals(response.getStatus().getCode(), 0)) {
            throw new BizException(response.getStatus().getCode(), response.getStatus().getMsg());
        }
        //取货后转移发财酒
        doTransMaterialStockWhenRiderTakeAway(deliveryOrderId, channelId, channelOrderId);

        try {
            if (MccConfigUtil.getPostOperationLocationSwitch() && Objects.nonNull(locationInfo)) {
                String logMessage = XMDLogFormat.build()
                        .putTag("operate_receipt_id", String.valueOf(deliveryOrderId))
                        .putTag("operation_type", "delivery_order_take_away")
                        .putTag("latitude", locationInfo.getLatitude())
                        .putTag("longitude", locationInfo.getLongitude())
                        .putTag("comment", "operate_receipt_id is deliveryOrderId")
                        .toString();
                operationLocationRecordLogger.info(logMessage);
            }
        } catch (Exception e) {
            log.error("记录位置失败", e);
        }
    }

    public void location(RiderLocationSyncRequest request){
        if(request==null){
            throw new BizException("参数错误");
        }
        RiderLocationRequest locationRequest= convertToLocationRequest(request);
        RiderOperateTResponse tResponse = riderOperateThriftService.riderLocation(locationRequest);
        log.info("RiderDeliveryServiceWrapper-location end, req={}, res={}", locationRequest, tResponse);
        if (tResponse == null || tResponse.getStatus() == null) {
            throw new SystemException("返回参数异常");
        }
        if (tResponse.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new SystemException("上报定位错误");
        }
    }

    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper-changeDeliveryStatusLock",
            fallBackMethod = "changeDeliveryStatusLockFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public void changeDeliveryStatusLock(RiderOperateRequest request, int changeType) {

        RiderOperateTRequest rpcRequest = convertToTRequest(request);
        RiderOperateTResponse tResponse;
        if (changeType == 0) {
            tResponse = riderOperateThriftService.unlockDeliveryStatus(rpcRequest);
        } else {
            tResponse = riderOperateThriftService.lockDeliveryStatus(rpcRequest);
        }
        if (!Objects.equals(tResponse.getStatus().getCode(), com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ResponseCodeEnum.SUCCESS.getValue())) {
            throw new BizException(tResponse.getStatus().getCode(), tResponse.getStatus().getMsg());
        }
    }

    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper-reportException",
            fallBackMethod = "reportExceptionFallback",
            timeoutInMilliseconds = 3000)
    @CatTransaction
    public void reportException(DeliveryExceptionReportRequest request) {
        RiderOperateTResponse tResponse = null;

        OCMSOrderVO ocmsOrderVO = getOCMSOrderVO(request.getChannelOrderId().toString(), request.getChannelId());

        RiderDeliveryExceptionTRequest rpcRequest = convertToTRequest(request, ocmsOrderVO);
        try {
            log.info("RiderDeliveryServiceWrapper.reportException start, req={}", rpcRequest);
            tResponse = riderOperateThriftService.reportDeliveryException(rpcRequest);
            log.info("RiderDeliveryServiceWrapper.reportException end, req={}, res={}", rpcRequest, tResponse);
            if (!Objects.equals(tResponse.getStatus().getCode(), com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ResponseCodeEnum.SUCCESS.getValue())) {
                throw new BizException(tResponse.getStatus().getCode(), tResponse.getStatus().getMsg());
            }
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper.reportException exception, req={}", rpcRequest, e);
            throw new SystemException("上报失败,请重试");
        }
    }

    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper-queryExceptionList",
            fallBackMethod = "queryExceptionListFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public QueryDeliveryExceptionListResponse queryExceptionList(QueryDeliveryExceptionListRequest request) {
        PageQueryDeliveryExceptionRequest rpcRequest = convertToTRequest(request);
        PageQueryDeliveryExceptionListResponse tResponse = null;
        try {
            log.info("RiderDeliveryServiceWrapper.queryExceptionList start, req={}", rpcRequest);
            tResponse = riderQueryThriftService.pageQueryDeliveryExceptionList(rpcRequest);
            log.info("RiderDeliveryServiceWrapper.queryExceptionList end, req={}, res={}", rpcRequest, tResponse);
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper.queryExceptionList exception, req={}", rpcRequest, e);
            throw new SystemException("查询配送异常失败");
        }

        if (tResponse == null || tResponse.getStatus() == null || tResponse.getPageInfo() == null) {
            log.error("RiderDeliveryServiceWrapper.queryExceptionList error, req:{}, resp:{}", rpcRequest,tResponse);
            throw new SystemException("查询配送异常失败");
        }

        if (tResponse.getStatus().getCode() != com.sankuai.meituan.shangou.empower.rider.client.common.ResponseCodeEnum.SUCCESS.getValue()) {
            log.warn("RiderDeliveryServiceWrapper.queryExceptionList fail, req:{}, resp:{}", rpcRequest, tResponse);
            throw new BizException(tResponse.getStatus().getMsg());
        }

        return buildQueryDeliveryExceptionListResponse(tResponse);
    }

    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper-queryExceptionDetail",
            fallBackMethod = "queryExceptionDetailFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public QueryDeliveryExceptionDetailListResponse queryExceptionDetail(String channelOrderId, Integer channelId) {
        QueryDeliveryExceptionByChannelOrderRequest request = new QueryDeliveryExceptionByChannelOrderRequest();

        request.setTenantId(LoginContextUtils.getAppLoginTenant());
        request.setStoreId(LoginContextUtils.getAppLoginStoreId());
        Integer orderBizType = ChannelOrderConvertUtils.convertBizType(channelId);
        request.setChannelOrderInfoList(Collections.singletonList(new QueryDeliveryExceptionByChannelOrderRequest.ChannelOrderInfo(channelOrderId, orderBizType)));

        DeliveryExceptionResponse tResponse = null;

        try {
            log.info("RiderDeliveryServiceWrapper.queryExceptionList start, req={}", request);
            tResponse = riderQueryThriftService.queryDeliveryExceptionByChannelOrder(request);
            log.info("RiderDeliveryServiceWrapper.queryExceptionList end, req={}, res={}", request, tResponse);
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper.queryExceptionList exception, req={}", request, e);
            throw new SystemException("查询配送异常失败");
        }

        if (tResponse == null || tResponse.getStatus() == null ) {
            log.error("RiderDeliveryServiceWrapper.queryExceptionList error, req:{}, resp:{}", request,tResponse);
            throw new SystemException("查询配送异常失败");
        }

        if (tResponse.getStatus().getCode() != com.sankuai.meituan.shangou.empower.rider.client.common.ResponseCodeEnum.SUCCESS.getValue()) {
            log.warn("RiderDeliveryServiceWrapper.queryExceptionList fail, req:{}, resp:{}", request, tResponse);
            throw new BizException(tResponse.getStatus().getMsg());
        }
        return buildQueryDeliveryExceptionDetailListResponse(tResponse);
    }

    private PageQueryDeliveryExceptionRequest convertToTRequest(QueryDeliveryExceptionListRequest request) {

        PageQueryDeliveryExceptionRequest tRequest = new PageQueryDeliveryExceptionRequest();
        tRequest.setPageNum(request.getPageNum());
        tRequest.setPageSize(request.getPageSize());
        tRequest.setExceptionTypeList(request.getExceptionTypeList());

        if (CollectionUtils.isNotEmpty(request.getChannelIds())) {
            List<Integer> orderBizTypes = request.getChannelIds().stream()
                    .map(channel -> DynamicOrderBizType.findOf(ChannelOrderConvertUtils.sourceMid2Biz(channel)))
                    .filter(Objects::nonNull)
                    .map(DynamicOrderBizType::getValue)
                    .collect(Collectors.toList());
            tRequest.setOrderBizTypeList(orderBizTypes);
        }

        //默认筛选三个月内的异常
        if (request.getReportStartTimeStamp() == null || request.getReportEndTimeStamp() == null) {
            tRequest.setReportTimeStartTimeStamp(TimeUtils.toMilliSeconds(LocalDateTime.now().minusMonths(DEFAULT_PAY_TIME_QUERY_DURATION)));
            tRequest.setReportTimeEndTimeStamp(System.currentTimeMillis());
        } else {
            tRequest.setReportTimeStartTimeStamp(request.getReportStartTimeStamp());
            tRequest.setReportTimeEndTimeStamp(request.getReportEndTimeStamp());
        }


        tRequest.setTenantId(LoginContextUtils.getAppLoginTenant());
        tRequest.setRiderAccountId(LoginContextUtils.getAppLoginAccountId());
        tRequest.setStoreIds(Collections.singletonList(LoginContextUtils.getAppLoginStoreId()));

        return tRequest;
    }

    private QueryDeliveryExceptionListResponse buildQueryDeliveryExceptionListResponse(PageQueryDeliveryExceptionListResponse tResponse) {
        QueryDeliveryExceptionListResponse response = new QueryDeliveryExceptionListResponse();
        response.setPageNum(tResponse.getPageInfo().getPage());
        response.setPageSize(tResponse.getPageInfo().getPageSize());
        response.setTotalCount(tResponse.getPageInfo().getTotal());
        response.setHasMore(tResponse.getPageInfo().getTotal() > tResponse.getPageInfo().getPage() * tResponse.getPageInfo().getPageSize());


        List<DeliveryExceptionSummaryVO> deliveryExceptionSummaryVOS = buildDeliveryExceptionSummaryVOList(tResponse.getTRiderDeliveryExceptionList());
        response.setDeliveryExceptionSummaryVOList(deliveryExceptionSummaryVOS);

        return response;
    }

    private List<DeliveryExceptionSummaryVO> buildDeliveryExceptionSummaryVOList(List<TRiderDeliveryException> tRiderDeliveryExceptionList) {

        List<DeliveryExceptionInfoVO> deliveryExceptionInfoVOS = tRiderDeliveryExceptionList.stream()
                .map(this::transform2DeliveryExceptionInfoVO)
                .collect(Collectors.toList());

        //Map化 deliveryOrderId --> DeliveryExceptionInfoVOList
        Map<String, List<DeliveryExceptionInfoVO>> deliveryExceptionInfoVOMap = new HashMap<>();
        for (DeliveryExceptionInfoVO deliveryExceptionInfoVO : deliveryExceptionInfoVOS) {
            if (!deliveryExceptionInfoVOMap.containsKey(deliveryExceptionInfoVO.getDeliveryOrderId())) {
                deliveryExceptionInfoVOMap.put(deliveryExceptionInfoVO.getDeliveryOrderId(), new ArrayList<>());
            }

            deliveryExceptionInfoVOMap.get(deliveryExceptionInfoVO.getDeliveryOrderId()).add(deliveryExceptionInfoVO);
        }

        //Map化 deliveryOrderId --> TRiderDeliveryException
        Map<String, TRiderDeliveryException> tRiderDeliveryExceptionMap = tRiderDeliveryExceptionList.stream()
                .collect(Collectors.toMap(tRiderDeliveryException -> tRiderDeliveryException.getDeliveryOrderId().toString(),
                        Function.identity(), (k1, k2) -> k1));


        return transform2DeliveryExceptionSummaryVO(tRiderDeliveryExceptionMap, deliveryExceptionInfoVOMap);
    }

    private DeliveryExceptionInfoVO transform2DeliveryExceptionInfoVO(TRiderDeliveryException tRiderDeliveryException) {

        DeliveryExceptionInfoVO deliveryExceptionInfoVO = new DeliveryExceptionInfoVO();

        deliveryExceptionInfoVO.setExceptionType(tRiderDeliveryException.getExceptionType());
        deliveryExceptionInfoVO.setReportTimeStamp(tRiderDeliveryException.getCreateTime());
        deliveryExceptionInfoVO.setRiderAccountId(tRiderDeliveryException.getRiderAccountId().toString());
        deliveryExceptionInfoVO.setRiderAccountName(tRiderDeliveryException.getRiderAccountName());
        deliveryExceptionInfoVO.setExceptionTypeDesc(tRiderDeliveryException.getExceptionTypeDesc());
        deliveryExceptionInfoVO.setDeliveryOrderId(tRiderDeliveryException.getDeliveryOrderId().toString());

        return deliveryExceptionInfoVO;
    }

    private List<DeliveryExceptionSummaryVO> transform2DeliveryExceptionSummaryVO(Map<String, TRiderDeliveryException> tRiderDeliveryExceptionMap,
                                                                                  Map<String, List<DeliveryExceptionInfoVO>> exceptionInfoVOMap) {
        return exceptionInfoVOMap.entrySet().stream().map(entry -> {
                    TRiderDeliveryException tRiderDeliveryException = tRiderDeliveryExceptionMap.get(entry.getKey());

                    if (tRiderDeliveryException == null) {
                        return null;
                    }

                    DeliveryExceptionSummaryVO exceptionSummaryVO = new DeliveryExceptionSummaryVO();
                    exceptionSummaryVO.setChannelId(ChannelOrderConvertUtils.convertChannelId(tRiderDeliveryException.getOrderBizType()));
                    exceptionSummaryVO.setChannelOrderId(tRiderDeliveryException.getChannelOrderId().toString());
                    exceptionSummaryVO.setPayTime(tRiderDeliveryException.getPayTime());
                    exceptionSummaryVO.setDaySeq(tRiderDeliveryException.getDaySeq());
                    exceptionSummaryVO.setStoreId(tRiderDeliveryException.getStoreId());
                    exceptionSummaryVO.setDeliveryOrderId(tRiderDeliveryException.getDeliveryOrderId().toString());
                    exceptionSummaryVO.setDeliveryExceptionInfoVOS(entry.getValue());

                    return exceptionSummaryVO;
                }).filter(Objects::nonNull)
                .sorted((o1, o2) -> -(o1.getPayTime().compareTo(o2.getPayTime())))
                .collect(Collectors.toList());
    }


    private QueryDeliveryExceptionDetailListResponse buildQueryDeliveryExceptionDetailListResponse(DeliveryExceptionResponse deliveryExceptionResponse) {
        QueryDeliveryExceptionDetailListResponse response = new QueryDeliveryExceptionDetailListResponse();
        response.setDeliveryExceptionDetailVos(deliveryExceptionResponse.getTRiderDeliveryExceptionList().stream()
                .map(this::transform2DeliveryExceptionDetailVO)
                .sorted(Comparator.comparing(DeliveryExceptionDetailVO::getReportTimeStamp))
                .collect(Collectors.toList()));

        return response;
    }

    private DeliveryExceptionDetailVO transform2DeliveryExceptionDetailVO(TRiderDeliveryException tRiderDeliveryException) {

        DeliveryExceptionDetailVO deliveryExceptionDetailVO = new DeliveryExceptionDetailVO();
        deliveryExceptionDetailVO.setChannelId(ChannelOrderConvertUtils.convertChannelId(tRiderDeliveryException.getOrderBizType()));
        deliveryExceptionDetailVO.setChannelOrderId(tRiderDeliveryException.getChannelOrderId().toString());
        deliveryExceptionDetailVO.setDaySeq(tRiderDeliveryException.getDaySeq());
        deliveryExceptionDetailVO.setComment(tRiderDeliveryException.getComment());
        deliveryExceptionDetailVO.setExceptionSubType(tRiderDeliveryException.getExceptionSubType());
        deliveryExceptionDetailVO.setExceptionType(tRiderDeliveryException.getExceptionType());
        deliveryExceptionDetailVO.setPayTime(tRiderDeliveryException.getPayTime());
        deliveryExceptionDetailVO.setUserRealAddress(tRiderDeliveryException.getUserRealAddress());

        deliveryExceptionDetailVO.setStoreId(tRiderDeliveryException.getStoreId().toString());
        deliveryExceptionDetailVO.setPicUrls(tRiderDeliveryException.getPicUrls());
        deliveryExceptionDetailVO.setReportTimeStamp(tRiderDeliveryException.getCreateTime());
        deliveryExceptionDetailVO.setRiderAccountId(tRiderDeliveryException.getRiderAccountId().toString());
        deliveryExceptionDetailVO.setRiderAccountName(tRiderDeliveryException.getRiderAccountName());
        deliveryExceptionDetailVO.setExceptionTypeDesc(tRiderDeliveryException.getExceptionTypeDesc());
        deliveryExceptionDetailVO.setExceptionSubTypeDesc(tRiderDeliveryException.getExceptionSubTypeDesc());
        deliveryExceptionDetailVO.setModifiedAddress(tRiderDeliveryException.getModifiedAddress());
        deliveryExceptionDetailVO.setDeliveryOrderId(tRiderDeliveryException.getDeliveryOrderId().toString());

        return deliveryExceptionDetailVO;
    }

    private OCMSOrderVO getOCMSOrderVO(String channelOrderId, Integer channelId) {
        OCMSListViewIdConditionRequest viewIdConditionRequest = new OCMSListViewIdConditionRequest();
        viewIdConditionRequest.setViewIdConditionList(Collections.singletonList(new ViewIdCondition(ChannelOrderConvertUtils.convertBizType(channelId),channelOrderId)));
        OCMSListViewIdConditionResponse response = null;
        try {
            log.info("RiderDeliveryServiceWrapper call OcmsQueryThriftService.queryOrderByViewIdCondition request:{}", viewIdConditionRequest);
            response = ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
            log.info("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition response:{}", response);
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition error.requset:{},response:{}", viewIdConditionRequest, response);
            throw new SystemException("查询订单详情失败");
        }
        if (response == null || response.getStatus() == null || response.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
            log.error("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition error.request:{},response:{}", viewIdConditionRequest, response);
            throw new SystemException("查询订单详情失败");
        }

        if(CollectionUtils.isEmpty(response.getOcmsOrderList())) {
            throw new SystemException("订单不存在");
        }

        return response.getOcmsOrderList().get(0);
    }

    private RiderDeliveryExceptionTRequest convertToTRequest(DeliveryExceptionReportRequest request, OCMSOrderVO ocmsOrderVO) {
        RiderDeliveryExceptionTRequest tRequest = new RiderDeliveryExceptionTRequest();

        LoginUser loginUser = AppLoginContextHolder.getAppLoginContext().getLoginUser();
        tRequest.setStoreId(LoginContextUtils.getAppLoginStoreId());
        tRequest.setTenantId(LoginContextUtils.getAppLoginTenant());
        tRequest.setRiderAccountId(loginUser.getAccountId());
        tRequest.setRiderAccountName(loginUser.getEmployeeName());

        tRequest.setChannelOrderId(request.getChannelOrderId());
        tRequest.setDeliveryOrderId(request.getDeliveryOrderId());
        tRequest.setExceptionType(request.getExceptionType());
        tRequest.setExceptionSubType(request.getExceptionSubType());
        tRequest.setPicUrls(request.getPicUrls());
        tRequest.setUserRealAddress(request.getUserRealAddress());
        tRequest.setComment(request.getComment());
        tRequest.setOrderBizType(ChannelOrderConvertUtils.convertBizType(request.getChannelId()));
        tRequest.setModifiedAddress(request.getModifiedAddress());

        tRequest.setDaySeq(ocmsOrderVO.getOrderSerialNumber().intValue());
        tRequest.setPayTime(ocmsOrderVO.getPayTime());

        return tRequest;
    }

    private RiderOperateTRequest convertToTRequest(RiderOperateRequest request) {
        RiderOperateTRequest tRequest = new RiderOperateTRequest();
        tRequest.setDeliveryOrderId(request.getDeliveryOrderId());

        LoginUser loginUser = AppLoginContextHolder.getAppLoginContext().getLoginUser();
        tRequest.setTenantId(loginUser.getTenantId());
        tRequest.setStoreId(request.getStoreId());
        tRequest.setLongitude(request.getLongitude());
        tRequest.setLatitude(request.getLatitude());
        tRequest.setOperatorId(loginUser.getAccountId());
        tRequest.setOperatorName(loginUser.getEmployeeName());
        tRequest.setOperatorPhone(loginUser.getEmployeePhone());
        if(request.getLocationInfo() != null) {
            tRequest.setLongitude(request.getLocationInfo().getLongitude());
            tRequest.setLatitude(request.getLocationInfo().getLatitude());
        }
        return tRequest;
    }

    private RiderLocationRequest convertToLocationRequest(RiderLocationSyncRequest request){

        LoginUser loginUser = AppLoginContextHolder.getAppLoginContext().getLoginUser();
        RiderLocationRequest locationRequest=new RiderLocationRequest();
        locationRequest.setTenantId(loginUser.getTenantId());
        locationRequest.setStoreId(request.getStoreId());
        locationRequest.setRiderAccountId(loginUser.getAccountId());
        locationRequest.setRiderName(loginUser.getAccountName());
        if(StringUtils.isEmpty(locationRequest.getRiderName())){
            locationRequest.setRiderName(loginUser.getEmployeeName());
        }
        locationRequest.setRiderPhone(loginUser.getEmployeePhone());
        locationRequest.setAccuracy(request.getAccuracy());
        locationRequest.setBearing(request.getBearing());
        locationRequest.setLatitude(request.getLatitude());
        locationRequest.setLongitude(request.getLongitude());
        locationRequest.setProvider(request.getProvider());
        locationRequest.setSpeed(request.getSpeed());
        locationRequest.setTime(request.getTime());
        try {
            locationRequest.setOs(AppLoginContextHolder.getAppLoginContext().getOs());
            locationRequest.setAppVersion(AppLoginContextHolder.getAppLoginContext().getAppVersion());
            locationRequest.setUuid(AppLoginContextHolder.getAppLoginContext().getUuid());
        } catch (Exception e) {
            log.error("获取手机os失败", e);
        }

        return locationRequest;
    }

    private void doTransMaterialStockWhenRiderTakeAway(Long deliveryOrderId, Integer channelId, String channelOrderId) {
        try {
            Integer tradeChannelType;
            if (Objects.isNull(channelId) || StringUtils.isBlank(channelOrderId)) {
                QueryDeliveryOrderByIdListRequest queryDeliveryOrderRequest = new QueryDeliveryOrderByIdListRequest();
                queryDeliveryOrderRequest.setTenantId(LoginContextUtils.getAppLoginTenant());
                queryDeliveryOrderRequest.setDeliveryOrderIds(Lists.newArrayList(deliveryOrderId));
                queryDeliveryOrderRequest.setNeedReturnPricingRouteInfo(false);
                queryDeliveryOrderRequest.setStoreId(LoginContextUtils.getAppLoginStoreId());
                BatchQueryDeliveryOrderResponse queryDeliveryOrderResponse = riderQueryThriftService.queryDeliveryOrderByIds(queryDeliveryOrderRequest);
                if (!Objects.equals(queryDeliveryOrderResponse.getStatus().getCode(), Status.SUCCESS.getCode()) || CollectionUtils.isEmpty(queryDeliveryOrderResponse.getTRiderDeliveryOrders())) {
                    throw new SystemException("查询运单失败");
                }
                TRiderDeliveryOrder tRiderDeliveryOrder = queryDeliveryOrderResponse.getTRiderDeliveryOrders().get(0);
                tradeChannelType = tRiderDeliveryOrder.getOrderBizTypeCode();
                channelOrderId = tRiderDeliveryOrder.getChannelOrderId();
            } else {
                tradeChannelType = DynamicOrderBizType.channelId2OrderBizTypeValue(channelId);
            }

            RiderTakeAwayTransMaterialRequest request = new RiderTakeAwayTransMaterialRequest();
            request.setTradeOrderNo(channelOrderId);
            request.setTradeChannelType(tradeChannelType);
            request.setRiderAccountId(LoginContextUtils.getAppLoginUser().getAccountId());
            request.setMaterialType(Lists.newArrayList(PromotionMaterialType.FORTUNE_WINE.getCode()));
            request.setWarehouseId(LoginContextUtils.getAppLoginStoreId());
            request.setUserContext(new UserContext(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginAccountId(), LoginContextUtils.getAppLoginUser().getAccountName(), LoginContextUtils.getAppLoginUser().getEmployeeName(), LoginContextUtils.getAppLoginUser().getEmployeeId()));
            outboundServiceWrapper.transMaterialStockWhenRiderTakeAway(request);
        } catch (Exception e) {
            Cat.logEvent("FORTUNE_WINE", "TAKE_AWAY_TRANS_STOCK_FAIL");
            log.error("骑手取货后转移库存失败", e);
        }
    }

    public void markPickDeliverySplit(Long deliveryOrderId) throws Exception {
        RiderOperateTRequest tRequest = new RiderOperateTRequest();
        tRequest.setDeliveryOrderId(deliveryOrderId);
        tRequest.setOperatorName(LoginContextUtils.getAppLoginUser().getEmployeeName());
        tRequest.setOperatorId(LoginContextUtils.getAppLoginUser().getAccountId());
        tRequest.setOperatorPhone(LoginContextUtils.getAppLoginUser().getEmployeePhone());
        RetryTemplateUtil.simpleWithFixedRetry(3, 100).execute(
                new RetryCallback<Object, Exception>() {
                    @Override
                    public Object doWithRetry(RetryContext context) {
                        log.info("start invoke riderOperateThriftService.markPickDeliverySplit, deliveryOrderId: {}", deliveryOrderId);
                        RiderOperateTResponse tResponse = riderOperateThriftService.markPickDeliverySplit(tRequest);
                        log.info("end invoke riderOperateThriftService.markPickDeliverySplit, tResponse: {}", tResponse);
                        if (tResponse.getStatus().getCode() != 0) {
                            throw new BizException(tResponse.getStatus().getCode(), tResponse.getStatus().getMsg());
                        }
                        return null;
                    }
                }
        );
    }


    public void riderChange(com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.RiderChangeRequest request, LoginUser currentUser, EmployeeDTO employeeDTO) {
        //校验限制项
        if (GrayConfigUtils.judgeIsGrayStore(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginStoreId(),
                GrayKeyEnum.LIMIT_ACCEPT_ORDER.getGrayKey(), false)) {
            checkLimitAcceptItem(LoginContextUtils.getAppLoginTenant(), request.getRiderAccountId(), LoginContextUtils.getAppLoginStoreId());
        }
        //转配送单
        RiderChangeRequest tRequest = new RiderChangeRequest(currentUser.getTenantId(), request.getStoreId(), request.getOrderId(),
                currentUser.getAccountId(), request.getRiderAccountId(), employeeDTO.getEmpName(), employeeDTO.getPhoneNumber(),null);

        RiderChangeResponse riderChangeResponse = riderOperateThriftService.riderChange(tRequest);

        if (!Objects.equals(riderChangeResponse.getStatus().getCode(), 0)) {
            throw new BizException(riderChangeResponse.getStatus().getCode(), riderChangeResponse.getStatus().getMsg());
        }

        //转拣货单
        ShippingAndDeliveryOrderPair shippingAndDeliveryOrderPair = new ShippingAndDeliveryOrderPair();
        tryToTransPickTask(request, employeeDTO, shippingAndDeliveryOrderPair);

        try {
            //NOTE: 这里幂等分两个部分，一个是前端重复点击/超时，导致重复请求；另一个是后端ofapp调oio超时重试，导致重复请求；
            //对于前者，判断每次转单是否真正成功了，注意这里仍有几率校验失败(select-update)，但概率较低，先不处理；
            //对于后者，只要保证重试时，传入的timestamp一致即可；
            long timestamp = System.currentTimeMillis();
            boolean isPickFinish = Objects.nonNull(shippingAndDeliveryOrderPair.getTradeShippingOrderDTO()) && Objects.equals(shippingAndDeliveryOrderPair.getTradeShippingOrderDTO().getStatus(), TradeShippingOrderStatus.FINISH.getCode());
            boolean isTakenGoods = Objects.nonNull(shippingAndDeliveryOrderPair.getTRiderDeliveryOrder()) && shippingAndDeliveryOrderPair.getTRiderDeliveryOrder().getDeliveryStatus() >= DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode();
            //如果不是重复点击 && 拣货任务已经完成 && 骑手已取货
            if (Objects.nonNull(riderChangeResponse.getIsSameRider()) && !riderChangeResponse.getIsSameRider() && isPickFinish && isTakenGoods) {
                RiderChangeTransMaterialRequest riderChangeTransMaterialRequest = buildRiderChangeTransMaterialRequest(request, shippingAndDeliveryOrderPair, timestamp, riderChangeResponse.getBeforeRiderAccountId());
                Optional<MaterialTransInfoDto> materialTransInfoDto = outboundServiceWrapper.transMaterialStockWhenRiderChange(riderChangeTransMaterialRequest);
                if (materialTransInfoDto.isPresent() && materialTransInfoDto.get().getOperateCount() > 0) {
                    RiderChangeTransStockMsg riderChangeTransStockMessage = buildRiderChangeTransStockMsg(employeeDTO, shippingAndDeliveryOrderPair, materialTransInfoDto.get(), riderChangeResponse);
                    riderChangeTransStockProducer.sendMessage(JSON.toJSONString(riderChangeTransStockMessage));
                }
            }
        } catch (Exception e) {
            log.error("骑手转单后转移库存失败", e);
            Cat.logEvent("FORTUNE_WINE", "RIDER_CHANGE_TRANS_STOCK_FAIL");
        }
    }

    private RiderChangeTransMaterialRequest buildRiderChangeTransMaterialRequest(com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.RiderChangeRequest request, ShippingAndDeliveryOrderPair shippingAndDeliveryOrderPair, long timestamp, Long beforeRiderAccountId) {
        RiderChangeTransMaterialRequest riderChangeTransMaterialRequest = new RiderChangeTransMaterialRequest();
        riderChangeTransMaterialRequest.setWarehouseId(shippingAndDeliveryOrderPair.getTradeShippingOrderDTO().getWarehouseId());
        riderChangeTransMaterialRequest.setTradeOrderNo(shippingAndDeliveryOrderPair.getTradeShippingOrderDTO().getTradeOrderNo());
        riderChangeTransMaterialRequest.setTradeChannelType(shippingAndDeliveryOrderPair.getTradeShippingOrderDTO().getTradeChannelType());
        riderChangeTransMaterialRequest.setOldRiderAccountId(beforeRiderAccountId);
        riderChangeTransMaterialRequest.setNewRiderAccountId(request.getRiderAccountId());
        riderChangeTransMaterialRequest.setMaterialType(Lists.newArrayList(PromotionMaterialType.FORTUNE_WINE.getCode()));
        riderChangeTransMaterialRequest.setOperateTimeStamp(timestamp);
        riderChangeTransMaterialRequest.setUserContext(new UserContext(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginAccountId(), LoginContextUtils.getAppLoginUser().getAccountName(), LoginContextUtils.getAppLoginUser().getEmployeeName(), LoginContextUtils.getAppLoginUser().getEmployeeId()));
        return riderChangeTransMaterialRequest;
    }

    private RiderChangeTransStockMsg buildRiderChangeTransStockMsg(EmployeeDTO employeeDTO, ShippingAndDeliveryOrderPair shippingAndDeliveryOrderPair, MaterialTransInfoDto materialTransInfoDto, RiderChangeResponse riderChangeResponse) {
        Map<String, DepotGoodsDetailDto> skuMap = depotGoodsWrapper.queryBySkuId(shippingAndDeliveryOrderPair.getTRiderDeliveryOrder().getTenantId(), shippingAndDeliveryOrderPair.getTRiderDeliveryOrder().getStoreId(), Lists.newArrayList(materialTransInfoDto.getMaterialSkuId()));
        if (MapUtils.isEmpty(skuMap) || !skuMap.containsKey(materialTransInfoDto.getMaterialSkuId())) {
            log.error("查询转移货品信息信息失败");
        }
        DepotGoodsDetailDto depotGoodsDetailDto = skuMap.get(materialTransInfoDto.getMaterialSkuId());
        RiderChangeTransStockMsg riderChangeTransStockMessage = new RiderChangeTransStockMsg();
        riderChangeTransStockMessage.setTenantId(shippingAndDeliveryOrderPair.getTRiderDeliveryOrder().getTenantId());
        riderChangeTransStockMessage.setStoreId(shippingAndDeliveryOrderPair.getTRiderDeliveryOrder().getStoreId());
        riderChangeTransStockMessage.setChannelOrderId(shippingAndDeliveryOrderPair.getTRiderDeliveryOrder().getChannelOrderId());
        riderChangeTransStockMessage.setOrderBizType(shippingAndDeliveryOrderPair.getTRiderDeliveryOrder().getOrderBizTypeCode());
        riderChangeTransStockMessage.setBeforeRiderAccountId(riderChangeResponse.getBeforeRiderAccountId());
        riderChangeTransStockMessage.setBeforeRiderName(riderChangeResponse.getBeforeRiderName());
        riderChangeTransStockMessage.setAfterRiderAccountId(employeeDTO.getAccountId());
        riderChangeTransStockMessage.setAfterRiderName(employeeDTO.getEmpName());
        riderChangeTransStockMessage.setTransStockMaterialInfo(
                new RiderChangeTransStockMsg.TransStockMaterialInfo(
                        materialTransInfoDto.getMaterialSkuId(), depotGoodsDetailDto.getGoodsName(),materialTransInfoDto.getOperateCount(),
                        Objects.nonNull(depotGoodsDetailDto.getGoodsPic()) && org.apache.commons.collections.CollectionUtils.isNotEmpty(depotGoodsDetailDto.getGoodsPic().getRealPicUrlList()) ? depotGoodsDetailDto.getGoodsPic().getRealPicUrlList().get(0) : StringUtils.EMPTY,
                        depotGoodsDetailDto.getSpecName()
                )
        );
        riderChangeTransStockMessage.setOperateTime(System.currentTimeMillis());
        return riderChangeTransStockMessage;
    }


    /**
     * 三方转自送
     */
    public void turnToMerchantSelfDeliveryForDrunkHorse(Long orderId, Long newRiderAccountId) {
        //校验是否限制接单
        if (GrayConfigUtils.judgeIsGrayStore(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginStoreId(),
                GrayKeyEnum.LIMIT_ACCEPT_ORDER.getGrayKey(), false)) {
            checkLimitAcceptItem(LoginContextUtils.getAppLoginTenant(), newRiderAccountId, LoginContextUtils.getAppLoginStoreId());
        }

        List<EmployeeDTO> employeeDTOList = oswServiceWrapper.queryEmpByAccountIds(
                LoginContextUtils.getAppLoginTenant(),
                Collections.singletonList(newRiderAccountId));
        if (CollectionUtils.isEmpty(employeeDTOList)) {
            throw new BizException("账号不存在");
        }

        EmployeeDTO newRiderInfo = employeeDTOList.get(0);
        DrunkHorseTurnToMerchantSelfDeliveryRequest request = new DrunkHorseTurnToMerchantSelfDeliveryRequest();
        request.setOperatorId(LoginContextUtils.getAppLoginAccountId());
        request.setTenantId(LoginContextUtils.getAppLoginTenant());
        request.setStoreId(LoginContextUtils.getAppLoginStoreId());
        request.setNewAccountId(newRiderAccountId);
        request.setNewAccountName(newRiderInfo.getEmpName());
        request.setNewAccountPhone(newRiderInfo.getPhoneNumber());
        request.setOrderId(orderId);
        log.info("start invoke deliveryOperationRpcService.turnToMerchantSelfDeliveryForDrunkHorse, request: {}", request);
        TurnToMerchantSelfDeliveryResponse response = deliveryOperationRpcService.turnToMerchantSelfDeliveryForDrunkHorse(request);
        log.info("end invoke deliveryOperationRpcService.turnToMerchantSelfDeliveryForDrunkHorse, response: {}", response);
        if (response.getStatus().getCode() != 0) {
            throw new BizException(response.getStatus().getCode(), "转自配失败," + response.getStatus().getMsg());
        }
    }

    private void tryToAcceptPickTask(RiderOperateRequest request) {
        try {
            Long tenantId = LoginContextUtils.getAppLoginTenant();
            Long storeId = request.getStoreId();
            if (MccConfigUtil.acceptPickOrderSwitch(storeId)) {
                if(Objects.isNull(request.getAcceptType()) || Objects.equals(request.getAcceptType(), 1)) {
                    String channelOrderId ;
                    Integer orderBizType ;
                    //如果前端传了,直接用前端传的
                    if (StringUtils.isBlank(request.getChannelOrderId()) || Objects.isNull(request.getChannelId())) {
                        QueryDeliveryOrderByIdListRequest queryByIdReq = new QueryDeliveryOrderByIdListRequest();
                        queryByIdReq.setTenantId(tenantId);
                        queryByIdReq.setStoreId(storeId);
                        queryByIdReq.setNeedReturnPricingRouteInfo(false);
                        queryByIdReq.setDeliveryOrderIds(Collections.singletonList(request.getDeliveryOrderId()));
                        BatchQueryDeliveryOrderResponse queryDeliveryOrderByIdsResp = riderQueryThriftService.queryDeliveryOrderByIds(queryByIdReq);
                        log.info("invoke riderQueryRpcService.queryDeliveryOrderByIds end, request: {}, response: {}", JSON.toJSONString(queryByIdReq), JSON.toJSONString(queryDeliveryOrderByIdsResp));
                        TRiderDeliveryOrder tRiderDeliveryOrder = queryDeliveryOrderByIdsResp.getTRiderDeliveryOrders().get(0);
                        channelOrderId = tRiderDeliveryOrder.getChannelOrderId();
                        orderBizType = tRiderDeliveryOrder.getOrderBizTypeCode();
                    } else {
                        channelOrderId = request.getChannelOrderId();
                        orderBizType = ChannelOrderConvertUtils.convertBizType(request.getChannelId());
                    }

                    TResult<Void> tResult = RetryTemplateUtil.simpleWithFixedRetry(3, 100).execute(new RetryCallback<TResult<Void>, Exception>() {
                        @Override
                        public TResult<Void> doWithRetry(RetryContext context) throws Exception {
                            return tradeShippingOrderService.startShipByTradeOrderNo(storeId,
                                    channelOrderId, orderBizType,
                                    LoginContextUtils.getAppLoginAccountId(),
                                    LoginContextUtils.getAppLoginUser().getEmployeeName());
                        }
                    });

                    if (!tResult.isSuccess()) {
                        if(Objects.equals(tResult.getCode(), com.sankuai.shangou.logistics.warehouse.enums.ResponseCodeEnum.SHIP_TASK_ALREADY_BE_ACCEPTED.getCode())) {
                            log.warn("拣货任务已被领取");
                            Cat.logEvent("PICK_DELIVERY_SPLIT", "PICK_TASK_ALREADY_BE_ACCEPTED");
                            throw new BizException(ErrorCodeEnum.PICK_TASK_ALREADY_BE_ACCEPTED.getCode(), ErrorCodeEnum.PICK_TASK_ALREADY_BE_ACCEPTED.getMessage());
                        } else {
                            throw new BizException("拣货任务领取失败," + tResult.getMsg());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("拣货单领取失败", e);
            Cat.logEvent("PICK_DELIVERY_SPLIT", "PICK_TASK_ACCEPT_FAIL");
        }
    }

    private void acceptDeliveryOrder(RiderOperateRequest request) {
        RiderOperateTRequest tRequest = new RiderOperateTRequest();
        tRequest.setOperatorPhone(LoginContextUtils.getAppLoginUser().getEmployeePhone());
        tRequest.setOperatorId(LoginContextUtils.getAppLoginUser().getAccountId());
        tRequest.setOperatorName(LoginContextUtils.getAppLoginUser().getEmployeeName());
        tRequest.setDeliveryOrderId(request.getDeliveryOrderId());
        tRequest.setStoreId(LoginContextUtils.getAppLoginStoreId());
        tRequest.setTenantId(LoginContextUtils.getAppLoginTenant());
        if (Objects.nonNull(request.getLocationInfo())) {
            tRequest.setLongitude(request.getLocationInfo().getLongitude());
            tRequest.setLatitude(request.getLocationInfo().getLatitude());
        }
        log.info("RiderDeliveryServiceWrapper-accept start, req={}", tRequest);
        RiderOperateTResponse tResponse = riderOperateThriftService.accept(tRequest);
        log.info("RiderDeliveryServiceWrapper-accept end, req: {}, res: {}", tRequest, tResponse);
        if (!Objects.equals(tResponse.getStatus().getCode(), 0)) {
            throw new BizException(tResponse.getStatus().getCode(), tResponse.getStatus().getMsg());
        }
    }

    public void checkLimitAcceptItem(Long tenantId, Long accountId, Long storeId) {
        List<LimitItemDTO> limitItemList = limitAcceptServiceWrapper.queryLimitItemByAccountId(tenantId, accountId, storeId);

        if (CollectionUtils.isNotEmpty(limitItemList)) {
            List<String> msg = limitItemList.stream()
                    .filter(Objects::nonNull)
                    // 限制接单类型，1-直接限制直接过，2-先提示后限制，需要先判断是否已经超过限制开始时间
                    .filter(LimitTakeOrderUtils::checkCurrentIsLimit)
                    .map(LimitItemDTO::getReason)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(msg)) {
                return;
            }
            String errMsg = String.format(ErrorCodeEnum.LIMIT_ACCEPT_ORDER.getMessage(), Joiner.on("、")
                    .join(msg));
            throw new BizException(ErrorCodeEnum.LIMIT_ACCEPT_ORDER.getCode(), errMsg);
        }

    }

    private void tryToTransPickTask(com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.RiderChangeRequest request,
                                    EmployeeDTO employeeBaseInfo, ShippingAndDeliveryOrderPair shippingAndDeliveryOrderPair) {
        try {
            Optional<TradeShippingOrderDTO> tradeShippingOrderOpt = Optional.empty();
            long storeId = LoginContextUtils.getAppLoginStoreId();
            if (MccConfigUtil.acceptPickOrderSwitch(storeId)) {
                BatchQueryDeliveryOrderResponse response = RetryTemplateUtil.simpleWithFixedRetry(3, 100).execute(new RetryCallback<BatchQueryDeliveryOrderResponse, Exception>() {
                    @Override
                    public BatchQueryDeliveryOrderResponse doWithRetry(RetryContext context) throws Exception {
                        BatchQueryDeliveryOrderResponse response = riderQueryThriftService.queryDeliveryOrderByOrderIdListV2(Collections.singletonList(request.getOrderId()));
                        log.info("end invoke riderQueryThriftService.queryDeliveryOrderByOrderIdListV2, orderId: {}, response: {}", request.getOrderId(), JSON.toJSONString(response));
                        return response;
                    }
                });

                if (CollectionUtils.isEmpty(response.getTRiderDeliveryOrders())) {
                    log.warn("无生效中的运单");
                    return;
                }
                TRiderDeliveryOrder tRiderDeliveryOrder = response.getTRiderDeliveryOrders().get(0);
                shippingAndDeliveryOrderPair.setTRiderDeliveryOrder(tRiderDeliveryOrder);


                //拣货完成不转拣货单
                TResult<List<TradeShippingOrderDTO>> tradeOrderResult = RetryTemplateUtil.simpleWithFixedRetry(3, 100)
                        .execute(new RetryCallback<TResult<List<TradeShippingOrderDTO>>, Exception>() {
                            @Override
                            public TResult<List<TradeShippingOrderDTO>> doWithRetry(RetryContext context) throws Exception {
                                TResult<List<TradeShippingOrderDTO>> tradeOrderNos = tradeShippingOrderService.getByTradeOrderNos(tRiderDeliveryOrder.getStoreId(),
                                        tRiderDeliveryOrder.getOrderBizTypeCode(), Collections.singletonList(tRiderDeliveryOrder.getChannelOrderId()));
                                log.info("end invoke tradeShippingOrderService.getByTradeOrderNos, tradeOrderNo: {}, response: {}", tRiderDeliveryOrder.getChannelOrderId(), tradeOrderNos);
                                return tradeOrderNos;
                            }
                        });

                if (tradeOrderResult.isSuccess() && CollectionUtils.isNotEmpty(tradeOrderResult.getData())) {
                    TradeShippingOrderDTO shippingOrderDTO = tradeOrderResult.getData().get(0);
                    shippingAndDeliveryOrderPair.setTradeShippingOrderDTO(shippingOrderDTO);
                    if (Objects.equals(shippingOrderDTO.getStatus(), TradeShippingOrderStatus.FINISH.getCode())) {
                        return;
                    }
                }
                //过滤拣配分离订单
                if (Objects.equals(tRiderDeliveryOrder.getPickDeliverySplitTag(), true)) {
                    return;
                }

                TResult<Void> tResult = RetryTemplateUtil.simpleWithFixedRetry(3, 100).execute(new RetryCallback<TResult<Void>, Exception>() {
                    @Override
                    public TResult<Void> doWithRetry(RetryContext context) throws Exception {
                        TResult<Void> tResult = tradeShippingOrderService.changeOperator(tRiderDeliveryOrder.getStoreId(),
                                tRiderDeliveryOrder.getChannelOrderId(),
                                tRiderDeliveryOrder.getOrderBizTypeCode(),
                                request.getRiderAccountId(),
                                employeeBaseInfo.getEmpName());
                        log.info("end invoke tradeShippingOrderService.changeOperator, tradeOrderNo: {}, response: {}", tRiderDeliveryOrder.getChannelOrderId(), tResult);
                        return tResult;
                    }
                });

                if (!tResult.isSuccess()) {
                    throw new BizException("转拣货任务失败," + tResult.getMsg());
                }
            }
        } catch (Exception e) {
            log.error("转拣货任务失败", e);
            Cat.logEvent("PICK_DELIVERY_SPLIT", "TRANS_PICK_TASK_FAIL");
        }
    }


    @Data
    public static class ShippingAndDeliveryOrderPair {
        @Nullable
        private TradeShippingOrderDTO tradeShippingOrderDTO;

        private TRiderDeliveryOrder tRiderDeliveryOrder;
    }
}
