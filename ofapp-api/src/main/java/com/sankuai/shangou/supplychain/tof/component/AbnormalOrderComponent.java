package com.sankuai.shangou.supplychain.tof.component;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderItemModel;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.model.ChildSkuModel;
import com.meituan.shangou.saas.o2o.dto.model.ComposeSkuModel;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.warehouse.AbnOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderItemDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.enums.AbnOrderCloseReason;
import com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.AbnOrderItemVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.AbnormalOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.TradeOrderVO;
import com.sankuai.shangou.supplychain.tof.wrapper.DepotGoodsWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.OrderQueryWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.SquirrelWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-12-11
 * @email <EMAIL>
 */
@Slf4j
@Rhino
public class AbnormalOrderComponent {
    @Resource
    private AbnOrderService abnOrderService;
    @Resource
    private DepotGoodsWrapper depotGoodsWrapper;
    @Resource
    private SquirrelWrapper squirrelWrapper;
    @Resource
    private OrderQueryWrapper orderQueryWrapper;

    /**
     * 获取没有绑定的异常订单，并计算其缺货量
     * @param tenantId 租户id
     * @param warehouseId 门店id
     * @return 异常单详情
     */
    @MethodLog(logRequest = true, logResponse = true)
    public List<AbnormalOrderVO> abnOrderListComponent(Long tenantId, Long warehouseId) {
        // 首先查找没有处理完成的异常单信息
        TResult<List<AbnOrderDTO>> tResult = abnOrderService.getUnprocessed(warehouseId);
        if (tResult.getCode() != 0 || CollectionUtils.isEmpty(tResult.getData())) {
            return new ArrayList<>();
        }
        List<AbnOrderDTO> abnOrders = Optional.ofNullable(tResult.getData()).orElse(new ArrayList<>()).stream()
                .filter(abnOrder -> Objects.nonNull(abnOrder) && !closeIfNoLackStock(warehouseId, abnOrder))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(abnOrders)) {
            return new ArrayList<>();
        }
        List<String> skuIds = abnOrders.stream()
                .flatMap(abnOrder -> abnOrder.getItems().stream())
                .map(AbnOrderItemDTO::getSkuId)
                .distinct()
                .collect(Collectors.toList());
        Map<String, DepotGoodsDetailDto> skuId2goods = depotGoodsWrapper.queryBySkuId(tenantId, warehouseId, skuIds);
        List<AbnormalOrderVO> abnormalOrderVOS = abnOrders.stream().map(abnOrder -> buildAbnormalOrderVO(abnOrder, skuId2goods)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(abnormalOrderVOS)) {
            return Lists.newArrayList();
        }

        return abnormalOrderVOS;
    }

    /**
     * 查询正在处理的异常单，如果异常单可以关闭则直接关闭并且过滤该结果
     * @param warehouseId 门店id
     * @param orderBizType 订单渠道id
     * @param tradeOrderNo 订单编号
     * @return 异常单详情
     */
    @MethodLog(logRequest = true, logResponse = true)
    public AbnOrderDTO getUnprocessedByTradeOrderSource(Long warehouseId, Integer orderBizType, String tradeOrderNo) {
        // 首先查找没有处理完成的异常单信息
        TResult<List<AbnOrderDTO>> tResult = abnOrderService.getUnprocessed(warehouseId);
        if (tResult.getCode() != 0 || CollectionUtils.isEmpty(tResult.getData())) {
            return null;
        }
        AbnOrderDTO abnOrder = tResult.getData().stream()
                .filter(order -> order.getSourceType().equals(orderBizType) && order.getSourceOrderNo().equals(tradeOrderNo))
                .findFirst().orElse(null);
        if (abnOrder == null || closeIfNoLackStock(warehouseId,abnOrder)) {
            return null;
        }
        return abnOrder;
    }

    @Degrade(rhinoKey = "AbnormalOrderComponent.getLackStockTag", fallBackMethod = "getLackStockTagFallback", timeoutInMilliseconds = 2000)
    public Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> getLackStockTag(List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList) {
        TResult<List<AbnOrderDTO>> unprocessedOrderListResult = abnOrderService.getUnprocessed(LoginContextUtils.getAppLoginStoreId());
        log.info("invoke abnOrderService.getUnprocessed, warehouseId = {}, response = {}", LoginContextUtils.getAppLoginStoreId(), unprocessedOrderListResult);
        if (!unprocessedOrderListResult.isSuccess() || org.apache.commons.collections4.CollectionUtils.isEmpty(unprocessedOrderListResult.getData())) {
            return Collections.emptyMap();
        }


        return unprocessedOrderListResult.getData().stream()
                .filter(abnOrderDTO -> tradeOrderKeyList.contains(new TradeOrderInfoComponent.TradeOrderKey(abnOrderDTO.getSourceType(), abnOrderDTO.getSourceOrderNo())))
                .collect(Collectors.toMap(abnOrderDTO -> new TradeOrderInfoComponent.TradeOrderKey(abnOrderDTO.getSourceType(), abnOrderDTO.getSourceOrderNo()), abnOrderDTO -> CollectionUtils.isNotEmpty(abnOrderDTO.getItems())));
    }

    @Degrade(rhinoKey = "AbnormalOrderComponent.getLackStockInfo", fallBackMethod = "getLackStockInfoFallback", timeoutInMilliseconds = 2000)
    public Optional<AbnOrderDTO> getLackStockInfo(TradeShippingOrderDTO shippingOrderDTO) {
        log.info("start invoke abnOrderService.getUnprocessed, warehouseId: {}", LoginContextUtils.getAppLoginStoreId());
        TResult<List<AbnOrderDTO>> unprocessedOrderListResult = abnOrderService.getUnprocessed(LoginContextUtils.getAppLoginStoreId());
        log.info("end invoke abnOrderService.getUnprocessed, warehouseId = {}, response = {}", LoginContextUtils.getAppLoginStoreId(), unprocessedOrderListResult);
        if (!unprocessedOrderListResult.isSuccess() || org.apache.commons.collections4.CollectionUtils.isEmpty(unprocessedOrderListResult.getData())) {
            return Optional.empty();
        }


        return unprocessedOrderListResult.getData()
                .stream()
                .filter(abnOrderDTO -> Objects.equals(new TradeOrderInfoComponent.TradeOrderKey(shippingOrderDTO.getTradeChannelType(), shippingOrderDTO.getTradeOrderNo()),
                        new TradeOrderInfoComponent.TradeOrderKey(abnOrderDTO.getSourceType(), abnOrderDTO.getSourceOrderNo())))
                .findAny();
    }

    public Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> getLackStockTagFallback(List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList) {
        log.error("AbnormalOrderComponent.getLackStockTag 发生降级");
        return Collections.emptyMap();
    }

    public Optional<AbnOrderDTO> getLackStockInfoFallback(TradeShippingOrderDTO shippingOrderDTO) {
        log.error("AbnormalOrderComponent.getLackStockInfo 发生降级");
        return Optional.empty();
    }

    public AbnOrderItemVO buildAbnormalOrderItemVO(AbnOrderItemDTO abnOrderItemDTO,Map<String,DepotGoodsDetailDto> goods) {
        // 查询库存相关信息
        Optional<DepotGoodsDetailDto> good = Optional.ofNullable(goods.get(abnOrderItemDTO.getSkuId()));
        return AbnOrderItemVO.builder()
                .goodsName(good.map(DepotGoodsDetailDto::getGoodsName).orElse(null))
                .skuId(abnOrderItemDTO.getSkuId())
                .picUrl(good.flatMap(g -> Optional.ofNullable(g.getGoodsPic())
                                .map(pic -> CollectionUtils.isEmpty(pic.getRealPicUrlList()) ? null : pic.getRealPicUrlList().get(0)))
                        .orElse(null))
                .spec(good.map(DepotGoodsDetailDto::getSpecName).orElse(null))
                .upcList(good.map(DepotGoodsDetailDto::getUpcList).orElse(null))
                .needCount(String.valueOf(abnOrderItemDTO.getDemandQuantity().intValue()))
                .salableCount(String.valueOf(abnOrderItemDTO.getAvailableQuantity().intValue()))
                .lackCount(String.valueOf(abnOrderItemDTO.getDemandQuantity().subtract(abnOrderItemDTO.getAvailableQuantity()).intValue()))
                .build();
    }

    private AbnormalOrderVO buildAbnormalOrderVO(AbnOrderDTO abnOrder,Map<String,DepotGoodsDetailDto> skuId2goods) {
        List<AbnOrderItemVO> itemVo = abnOrder.getItems().stream().map(item -> buildAbnormalOrderItemVO(item, skuId2goods))
                .collect(Collectors.toList());
        AbnormalOrderVO vo = new AbnormalOrderVO();
        vo.setAbnOrderId(abnOrder.getOrderNo());
        vo.setItems(itemVo);
        vo.setAbnormalOrderType(abnOrder.getCreateReasonCode());
        vo.setOrderBizType(abnOrder.getSourceType());
        vo.setTradeOrderNo(abnOrder.getSourceOrderNo());
        Optional<String> hasSendTag = squirrelWrapper.get(SquirrelWrapper.LACK_STOCK_MESSAGE_CATEGORY, abnOrder.getSourceType() + "_" + abnOrder.getSourceOrderNo(), String.class);
        vo.setAlreadySendMessage(hasSendTag.isPresent());
        return vo;
    }

    /**
     * 如果异常单不再缺货，则关闭；否则不关闭
     * @param warehouseId 门店id
     * @param abnOrder 异常单信息
     * @return 是否关闭
     */
    private boolean closeIfNoLackStock(Long warehouseId, AbnOrderDTO abnOrder) {
        if (CollectionUtils.isNotEmpty(abnOrder.getItems())) {
            return false;
        }
        // 如果订单已经没有缺货量信息，则进行关闭
        try {
            abnOrderService.close(warehouseId,abnOrder.getSourceType(),abnOrder.getSourceOrderNo(), AbnOrderCloseReason.OOS_STOCK_FULLFILL.getCode());
        } catch (Exception e) {
            log.error("关闭异常订单失败，tradeOrderNo:"+ abnOrder.getOrderNo());
        }
        return true;
    }

    public void fillLackStockGoodsInfo(long warehouseId, AbnormalOrderVO abnormalOrderVO, TradeOrderVO tradeOrderVO) {
        try {
            //如果有缺货的货品,需要在对应的销售商品上标识出来
            if (CollectionUtils.isNotEmpty(abnormalOrderVO.getItems())) {

                List<String> lackStockGoodsIds = abnormalOrderVO.getItems().stream().map(AbnOrderItemVO::getSkuId).collect(Collectors.toList());

                //调用订单拿到商品组合关系
                BizOrderModel bizOrderModel = orderQueryWrapper.queryOrderDetail(AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId(), warehouseId, tradeOrderVO.getChannelId(), tradeOrderVO.getChannelOrderId());
                if (bizOrderModel == null) {
                    return;
                }

                //标识出组合关系中包含缺货货品的销售商品
                List<Long> lackStockOrderItemIds = bizOrderModel.getComposeSkuModels().stream().filter(
                        composeSkuModel -> {
                            Optional<String> lackStockSkuIdOpt = composeSkuModel.getChildItemList().stream()
                                    .map(ChildSkuModel::getSkuId)
                                    .filter(lackStockGoodsIds::contains)
                                    .findAny();
                            return lackStockSkuIdOpt.isPresent();
                        }).map(ComposeSkuModel::getOrderItemId).collect(Collectors.toList());

                List<String> lackStockOrderItemSkuIds = bizOrderModel.getBizOrderItemModelList().stream()
                        .filter(orderItem -> lackStockOrderItemIds.contains(orderItem.getOrderItemId()))
                        .map(BizOrderItemModel::getInstoreSkuId2)
                        .collect(Collectors.toList());

                tradeOrderVO.getProductList().forEach(
                        product -> product.setIsIncludeStockLackGoods(lackStockOrderItemSkuIds.contains(product.getSkuId()))
                );
                if (CollectionUtils.isNotEmpty(tradeOrderVO.getGiftVOList())) {
                    tradeOrderVO.getGiftVOList().forEach(
                            giftVO -> giftVO.setIsIncludeStockLackGoods(lackStockOrderItemSkuIds.contains(giftVO.getSku()))
                    );
                }

                if (CollectionUtils.isNotEmpty(tradeOrderVO.getGiftBagList())) {
                    tradeOrderVO.getGiftBagList().forEach(
                            giftBagVO -> giftBagVO.setIsLackStock(lackStockGoodsIds.contains(giftBagVO.getMaterialSkuId()))
                    );
                }
            }

        } catch (Exception e) {
            log.warn("填充缺货信息失败", e);
            Cat.logEvent("PART_REFUND", "FILL_ABN_ORDER_ITEM_FAIL");
        }
    }

    /**
     * 强制关闭异常单
     *
     * @param warehouseId       仓ID
     * @param tradeChannelType  订单渠道
     * @param tradeOrderNo      订单编号
     * @param closeReasonCode   关闭原因Code，对应枚举： {@link AbnOrderCloseReason}
     * @return                  异常单是否关闭成功。如果异常单已经关闭，再次调用该方法，返回的也是 false
     */
    @MethodLog(logRequest = true)
    public boolean forceClose(Long warehouseId, Integer tradeChannelType, String tradeOrderNo, Integer closeReasonCode) {
        try {
            TResult<Boolean> result = abnOrderService.forceClose(warehouseId, tradeChannelType, tradeOrderNo, closeReasonCode);
            log.info("AbnormalOrderComponent.forceClose 强制关闭异常单，result：{}", JsonUtil.toJson(result));
            return Objects.nonNull(result) && Objects.nonNull(result.getData()) && result.getData();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

}
