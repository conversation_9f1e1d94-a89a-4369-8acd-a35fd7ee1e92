package com.sankuai.shangou.supplychain.tof.controller;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import com.sankuai.shangou.supplychain.tof.assembler.SelfDeliveryPickingAssembler;
import com.sankuai.shangou.supplychain.tof.component.PickingOrderComponent;
import com.sankuai.shangou.supplychain.tof.component.TransOrderComponent;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.PageQueryRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.QueryIncrementPickCompletedOrderRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.QueryPickCompletedOrderRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.*;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.request.*;
import com.sankuai.shangou.supplychain.tof.controller.vo.third.request.AcceptThirdDeliveryPickOrderRequest;
import com.sankuai.shangou.supplychain.tof.enums.ComponentTypeEnum;
import com.sankuai.shangou.supplychain.tof.enums.FilterSpilitTypeEnum;
import com.sankuai.shangou.supplychain.tof.service.RiderDeliveryOperateService;
import com.sankuai.shangou.supplychain.tof.service.SelfPickingOperateService;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/4/28 14:55
 **/
@InterfaceDoc(
        displayName = "拣配一体，骑手拣货相关接口",
        type = "restful",
        scenarios = "包含骑手查询拣货任务、操作拣货完成等接口",
        description = "包含骑手查询拣货任务、操作拣货完成等接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@RestController
@RequestMapping("/api/orderfulfill/app/rider/picking")
public class RiderPickingController {

    @Resource
    private SelfDeliveryPickingAssembler selfDeliveryPickingAssembler;

    @Resource
    private SelfPickingOperateService selfPickingOperateService;

    @Resource
    private RiderDeliveryOperateService riderDeliveryOperateService;

    @Resource
    private TransOrderComponent transOrderComponent;

    /**
     * 歪马扫码拣货埋点 TYPE
     */
    private final static String DH_UPC_SCAN_PICK = "DH_UPC_SCAN_PICK";
    @Autowired
    private PickingOrderComponent pickingOrderComponent;

    @MethodDoc(
            displayName = "查询骑手配送单的待拣货内容详情",
            description = "查询骑手配送单的待拣货内容详情",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询骑手已领取订单的待拣货内容的请求",
                            type = QueryOrderPickingDetailsRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/picking/waitpickdetail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/waitpickdetail", method = {RequestMethod.POST})
    @ResponseBody
    public RiderPickingDetailVO queryOrderWaitPickDetails(@Valid @RequestBody QueryOrderPickingDetailsRequest request) {
        Optional<String> validateResult = request.validate();
        if (validateResult.isPresent()) {
            throw new IllegalArgumentException(validateResult.get());
        }
        return selfDeliveryPickingAssembler.queryPickingDetail(request);
    }


    @MethodDoc(
            displayName = "骑手操作拣货完成",
            description = "骑手操作拣货完成",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手操作拣货完成的请求",
                            type = RiderPickingFinishResultVO.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/picking/finish",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/finish", method = {RequestMethod.POST})
    @ResponseBody
    public void finishPicking(@RequestBody RiderFinishPickingRequest request) {
        Optional<String> validateResult = request.validate();
        if (validateResult.isPresent()) {
            throw new IllegalArgumentException(validateResult.get());
        }

        selfPickingOperateService.finishPicking(request);
        Cat.logEvent(DH_UPC_SCAN_PICK, "ORDER_NUM");
        if (request.getUpcScanErrorOccurred()) {
            // 埋点记录：拣货过程中出现 UPC 识别为非订单内商品的订单数
            Cat.logEvent(DH_UPC_SCAN_PICK, "SCAN_ERROR_NUM");
        }
    }


    @MethodDoc(
            displayName = "查询拣货配置",
            description = "查询拣货配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询拣货配置",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/picking/config",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/config", method = {RequestMethod.POST})
    @ResponseBody
    public PickConfigVO queryPickingConfig() {
        return MccConfigUtil.getPickingConfig();
    }

    @MethodDoc(
            displayName = "查询人工降级封签理由",
            description = "查询人工降级封签理由",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询人工降级封签理由",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/picking/seal-degrade/reasonList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/seal-degrade/reasonList", method = {RequestMethod.POST})
    @ResponseBody
    public List<SealDegradeReasonVO> queryDegradeReasonList() {
        return MccConfigUtil.getSealDegradeReasonList();
    }

    @MethodDoc(
            displayName = "查询人工降级封签理由",
            description = "查询人工降级封签理由",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询人工降级封签理由",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/picking/seal-degrade/manualSubmit",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/seal-degrade/manualSubmit", method = {RequestMethod.POST})
    @ResponseBody
    public void manualSubmit(@RequestBody ManualSealDegradeRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }
        selfPickingOperateService.manualSubmitDegrade(request);
    }

    @MethodDoc(
            displayName = "分页查询骑手可领取的拣货列表",
            description = "分页查询骑手可领取的拣货列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手可领取的拣货列表",
                            type = QueryRiderWaitToGetOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/queryWaitReceiveList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryWaitReceiveList", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Map<String, Object> queryWaitReceiveList(@Valid @RequestBody PageQueryRequest request) {
        List<ComponentTypeEnum> extComponentTypeEnums = Arrays.asList(ComponentTypeEnum.ORDER_REVENUE_INFO,
                ComponentTypeEnum.LACK_STOCK,
                ComponentTypeEnum.TURN_DELIVERY_BUTTON_INFO,
                ComponentTypeEnum.SEAL_DELIVER,
                ComponentTypeEnum.USE_ASSESS_TIME,
                ComponentTypeEnum.CHECK_PICK_AND_DELIVERY_ORDER_STATUS,
                ComponentTypeEnum.FILTER_THIRD_PICK);
        return selfDeliveryPickingAssembler.queryPickingListTemplate(
                request,
                () -> pickingOrderComponent.getRecentListByOperatorIdAndStatusList(LoginContextUtils.getAppLoginStoreId(), null, Lists.newArrayList(TradeShippingOrderStatus.WAITED)),
                extComponentTypeEnums, FilterSpilitTypeEnum.NO_FILTER);
    }


    @MethodDoc(
            displayName = "分页查询骑手待拣货的拣货列表",
            description = "分页查询骑手待拣货的拣货列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手待拣货的拣货列表",
                            type = QueryRiderWaitToGetOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/queryWaitPickList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryWaitPickList", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Map<String, Object> queryWaitPickList(@Valid @RequestBody PageQueryRequest request) {
        List<ComponentTypeEnum> extComponentTypeEnums = Arrays.asList(ComponentTypeEnum.ORDER_REVENUE_INFO,
                ComponentTypeEnum.LACK_STOCK,
                ComponentTypeEnum.TURN_DELIVERY_BUTTON_INFO,
                ComponentTypeEnum.SEAL_DELIVER,
                ComponentTypeEnum.USE_ASSESS_TIME,
                ComponentTypeEnum.FILTER_THIRD_PICK);
        return selfDeliveryPickingAssembler.queryPickingListTemplate(
                request,
                () -> pickingOrderComponent.getRecentListByOperatorIdAndStatusList(LoginContextUtils.getAppLoginStoreId(), LoginContextUtils.getAppLoginAccountId(), Lists.newArrayList(TradeShippingOrderStatus.RECEIVE)),
                extComponentTypeEnums, FilterSpilitTypeEnum.KEEP_SPLIT);
    }


    @MethodDoc(
            displayName = "分页查询骑手已拣货的拣货列表",
            description = "分页查询骑手已拣货的拣货列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手已拣货的拣货列表",
                            type = QueryRiderWaitToGetOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/queryPickedList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryPickedList", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Map<String, Object> queryPickedList(@Valid @RequestBody QueryPickCompletedOrderRequest request) {
        List<ComponentTypeEnum> extComponentTypeEnums = Arrays.asList(ComponentTypeEnum.ORDER_REVENUE_INFO,
                ComponentTypeEnum.LACK_STOCK,
                ComponentTypeEnum.TURN_DELIVERY_BUTTON_INFO,
                ComponentTypeEnum.SEAL_DELIVER,
                ComponentTypeEnum.USE_ASSESS_TIME,
                ComponentTypeEnum.FILTER_THIRD_PICK,
                ComponentTypeEnum.SORT_BY_DONE_TIME,
                ComponentTypeEnum.PICKER_STATISTICS);
         return selfDeliveryPickingAssembler.queryPickingListTemplate(
                request,
                () -> pickingOrderComponent.getByOperatorIdAndStatusListAndDate(
                        LoginContextUtils.getAppLoginStoreId(), LoginContextUtils.getAppLoginAccountId(), Lists.newArrayList(TradeShippingOrderStatus.FINISH),
                        LocalDate.parse(request.getDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).atTime(0, 0, 0),
                        LocalDate.parse(request.getDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1).atTime(0, 0, 0)
                ),
                extComponentTypeEnums, FilterSpilitTypeEnum.NO_FILTER);
    }



    @MethodDoc(
            displayName = "领取拣货任务并且触发拣配分离",
            description = "领取拣货任务并且触发拣配分离",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询人工降级封签理由",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/picking/acceptAndSplitPickDelivery",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/acceptAndSplitPickDelivery", method = {RequestMethod.POST})
    @ResponseBody
    public void acceptAndSplitPickDelivery(@RequestBody AcceptAndSplitPickDeliveryRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        //领取拣货单给拣货单打标
        selfPickingOperateService.acceptAndSplitPickDelivery(request);

        //给配送单打标,失败不报错
        try {
            riderDeliveryOperateService.markPickDeliverySplit(request.getDeliveryOrderId());
        } catch (Exception e) {
            log.error("给运单标记拣配分离失败, deliveryOrderId: {}", request.getDeliveryOrderId(), e);
            Cat.logEvent("PICK_DELIVERY_SPLIT", "MARK_DELIVERY_ORDER_ERROR");
        }
    }


    @MethodDoc(
            displayName = "转拣货任务人员名单",
            description = "转拣货任务人员名单",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "转拣货任务人员名单",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/picking/queryTransferablePicker",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryTransferablePicker", method = {RequestMethod.POST})
    @ResponseBody
    public List<PickerInfoVO> queryTransferablePicker(@RequestBody QueryTransferablePickerRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        //转拣货单
        return transOrderComponent.queryTransferablePickerBySearchKey(request.getStoreId(), request.getPickerSearchKey(), request.getCurrentPickerAccountId());
    }

    @MethodDoc(
            displayName = "转拣货任务",
            description = "转拣货任务",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "转拣货任务",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/picking/pickerChange",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pickerChange", method = {RequestMethod.POST})
    @ResponseBody
    public void pickerChange(@RequestBody ChangePickerRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        //转拣货单
        selfPickingOperateService.changePicker(request);
    }

    @MethodDoc(
            displayName = "领取三方配送拣货单",
            description = "领取三方配送拣货单",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "领取三方配送拣货单",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/picking/acceptThirdDeliveryPickOrder",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/acceptThirdDeliveryPickOrder", method = {RequestMethod.POST})
    @ResponseBody
    public void acceptThirdDeliveryPickOrder(@Valid @RequestBody AcceptThirdDeliveryPickOrderRequest request) {
        selfPickingOperateService.acceptThirdDeliveryPickOrder(request.getViewOrderId(), request.getChannelId());
    }

    @MethodDoc(
            displayName = "分页查询骑手已拣货的拣货列表",
            description = "分页查询骑手已拣货的拣货列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手已拣货的拣货列表",
                            type = QueryRiderWaitToGetOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/queryPickedList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryIncrementPickedList", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Map<String, Object> queryIncrementPickedList(@Valid @RequestBody QueryIncrementPickCompletedOrderRequest request) {
        if (request.getLastUpdateTime() < TimeUtils.toMilliSeconds(LocalDateTime.now().toLocalDate().atTime(0,0,0))) {
            request.setLastUpdateTime(TimeUtils.toMilliSeconds(LocalDateTime.now().toLocalDate().atTime(0,0,0)));
        }
        List<ComponentTypeEnum> extComponentTypeEnums = Arrays.asList(ComponentTypeEnum.ORDER_REVENUE_INFO,
                ComponentTypeEnum.LACK_STOCK,
                ComponentTypeEnum.TURN_DELIVERY_BUTTON_INFO,
                ComponentTypeEnum.SEAL_DELIVER,
                ComponentTypeEnum.USE_ASSESS_TIME,
                ComponentTypeEnum.FILTER_THIRD_PICK,
                ComponentTypeEnum.SORT_BY_DONE_TIME);
        return selfDeliveryPickingAssembler.queryIncrementPickingListTemplate(
                request,
                () -> pickingOrderComponent.getByOperatorIdAndStatusListAndDate(
                        LoginContextUtils.getAppLoginStoreId(), LoginContextUtils.getAppLoginAccountId(), Lists.newArrayList(TradeShippingOrderStatus.FINISH),
                        TimeUtils.fromMilliSeconds(request.getLastUpdateTime()),
                        LocalDateTime.now()
                ),
                extComponentTypeEnums, FilterSpilitTypeEnum.NO_FILTER);
    }
}
