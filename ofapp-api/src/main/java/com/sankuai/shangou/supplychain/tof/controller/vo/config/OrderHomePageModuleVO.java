package com.sankuai.shangou.supplychain.tof.controller.vo.config;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "订单首页模块"
)
@Data
@ApiModel("订单首页模块")
public class OrderHomePageModuleVO {

    @FieldDoc(
            description = "是否展示订单tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示订单tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showOrderTab;

    @FieldDoc(
            description = "是否展示骑手领取订单页面，即新配送任务页面 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示骑手领取订单页面，即新配送任务页面 (0:否，1:是)", required = true)
    @NotNull
    private Integer showRiderGetOrderTab;

    @FieldDoc(
            description = "是否展示骑手待取货页面 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示骑手待取货页面 (0:否，1:是)", required = true)
    @NotNull
    private Integer showRiderTakeGoodsTab;

    @FieldDoc(
            description = "是否展示骑手配送中页面 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示骑手配送中页面 (0:否，1:是)", required = true)
    @NotNull
    private Integer showRiderInDeliveryTab;

    @FieldDoc(
            description = "是否展示骑手已完成页面 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示骑手已完成页面 (0:否，1:是)", required = true)
    @NotNull
    private Integer showRiderCompletedTab;

    @FieldDoc(
            description = "是否展示新任务-仅拣货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示新任务-仅拣货tab(0:否，1:是)", required = true)
    private Integer showNewJustPickTaskTab;

    @FieldDoc(
            description = "是否展示新任务-仅配送tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示新任务-仅配送tab (0:否，1:是)", required = true)
    private Integer showNewJustDeliveryTaskTab;

    @FieldDoc(
            description = "是否展示新任务-拣配任务tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示新任务-拣配任务tab (0:否，1:是)", required = true)
    private Integer showNewPickDeliveryTaskTab;

    @FieldDoc(
            description = "是否展示待取货-拣货任务tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待取货-拣货任务tab (0:否，1:是)", required = true)
    private Integer showWaitTakePickTaskTab;

    @FieldDoc(
            description = "是否展示待取货-配送任务tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待取货-配送任务tab (0:否，1:是)", required = true)
    private Integer showWaitTakeDeliveryTaskTab;

    @FieldDoc(
            description = "是否展示已完成-已拣货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示已完成-已拣货tab (0:否，1:是)", required = true)
    private Integer showCompletedPickTaskTab;

    @FieldDoc(
            description = "是否展示已完成-已拣货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示已完成-已拣货tab (0:否，1:是)", required = true)
    private Integer showCompletedDeliveryTaskTab;

    @FieldDoc(
            description = "是否展示推广自提tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示推广自提tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showSelfPickPromoteOrder;

    @FieldDoc(
            description = "是否展示拣配分离错误页面 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示拣配分离错误页面 (0:否，1:是)", required = true)
    private Integer showNewTaskSplitErrorHint;

    public static OrderHomePageModuleVO homePageInit(){
        OrderHomePageModuleVO orderHomePageModuleVO = new OrderHomePageModuleVO();
        orderHomePageModuleVO.setShowOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderGetOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderTakeGoodsTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderInDeliveryTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderCompletedTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        //全部订单、部分订单、评价默认无权限
        orderHomePageModuleVO.setShowOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);


        return orderHomePageModuleVO;
    }

    public static OrderHomePageModuleVO appHomePageInit(){
        OrderHomePageModuleVO orderHomePageModuleVO = new OrderHomePageModuleVO();
        orderHomePageModuleVO.setShowOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowSelfPickPromoteOrder(IntegerBooleanConstants.BOOLEAN_FALSE);
        // 骑手 tab 页配置 start
        orderHomePageModuleVO.setShowRiderGetOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderTakeGoodsTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderInDeliveryTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderCompletedTab(IntegerBooleanConstants.BOOLEAN_FALSE);


        return orderHomePageModuleVO;
    }

    public static OrderHomePageModuleVO homePageInit4App() {
        OrderHomePageModuleVO orderHomePageModuleVO = new OrderHomePageModuleVO();
        orderHomePageModuleVO.setShowOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowSelfPickPromoteOrder(IntegerBooleanConstants.BOOLEAN_FALSE);
        // 骑手 tab 页配置 start
        orderHomePageModuleVO.setShowRiderGetOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderTakeGoodsTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderInDeliveryTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderCompletedTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        // 骑手 tab 页配置 end
        return orderHomePageModuleVO;
    }

}
