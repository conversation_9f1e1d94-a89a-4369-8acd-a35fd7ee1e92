package com.sankuai.shangou.supplychain.tof.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.warehouse.dto.ConsumableItemInfo;
import com.sankuai.shangou.supplychain.tof.controller.vo.consumable.ConsumableItemVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.consumable.request.ConsumableItem;
import com.sankuai.shangou.supplychain.tof.controller.vo.consumable.request.ConsumableOutRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.consumable.request.QueryConsumableListRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.request.QueryOrderPickingDetailsRequest;
import com.sankuai.shangou.supplychain.tof.service.ConsumableService;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/6 15:21
 **/

@InterfaceDoc(
        displayName = "拣配一体，骑手拣货相关接口",
        type = "restful",
        scenarios = "包含骑手查询拣货任务、操作拣货完成等接口",
        description = "包含骑手查询拣货任务、操作拣货完成等接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@RestController
@RequestMapping("/api/orderfulfill/app/rider/consumable")
public class PickingConsumableController {

    @Resource
    private ConsumableService consumableService;

    @MethodDoc(
            displayName = "查询骑手配送单的待拣货内容详情",
            description = "查询骑手配送单的待拣货内容详情",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询骑手已领取订单的待拣货内容的请求",
                            type = QueryOrderPickingDetailsRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/consumable/list",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    @ResponseBody
    public Object consumableList(@RequestBody QueryConsumableListRequest request) {
        List<ConsumableItemVO> consumableItemVOS = consumableService.queryConsumableList(request.getChannelOrderId(), request.getChannelId(),
                LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginStoreId());
        Map<String, List<ConsumableItemVO>> resultMap = new HashMap<>();
        resultMap.put("items",consumableItemVOS);
        return resultMap;
    }

    @MethodDoc(
            displayName = "耗材随单出库接口",
            description = "耗材随单出库接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "耗材随单出库请求",
                            type = ConsumableOutRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/consumable/outWarehouse",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/outWarehouse", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public void consumableOut(@Valid @RequestBody ConsumableOutRequest request) throws Exception {
        //严格管库存的，不走其他出入库
        Set<String> needManageStockConsumableSkus = MccConfigUtil.getNeedManageStockConsumableList(LoginContextUtils.getAppLoginStoreId()).stream().map(ConsumableItemInfo::getSkuId).collect(Collectors.toSet());
        List<ConsumableItem> filteredConsumableItems = request.getItems().stream().filter(consumableItem -> !needManageStockConsumableSkus.contains(consumableItem.getSkuId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredConsumableItems)) {
            return;
        }
        request.setItems(filteredConsumableItems);

        request.valid(MccConfigUtil.getAllTypeConsumableList(LoginContextUtils.getAppLoginStoreId()).stream().collect(Collectors.toMap(ConsumableItemInfo::getSkuId, Function.identity(), (k1, k2) -> k1)));
        consumableService.consumableOut(request, LoginContextUtils.getAppLoginStoreId(), LoginContextUtils.getAppLoginUser());
        return ;
    }

}
