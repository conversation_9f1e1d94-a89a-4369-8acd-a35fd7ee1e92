package com.sankuai.shangou.supplychain.tof.controller.vo.consumable;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.logistics.warehouse.enums.ConsumableGoodsType;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/6 15:25
 **/
@Data
public class ConsumableItemVO {
    @FieldDoc(description = "skuName")
    private String skuName;

    @FieldDoc(description = "skuId")
    private String skuId;

    @NotBlank
    @FieldDoc(description = "单次出库上限")
    private String maxQuantity;

    /**
     * @see ConsumableGoodsType
     */
    @FieldDoc(description = "0-普通耗材 1-封签扣 2-封签容具 3-发财酒 4-安心包")
    private Integer type;

    @FieldDoc(description = "预警库存")
    private String warnStockQuantity;

    @FieldDoc(description = "是否需要校验库存")
    private Boolean needCheckStockQuantity;

    @FieldDoc(description = "库存量")
    private String currentStockQuantity;

    /**
     * UPC列表。目前仅安心包扫码需要
     */
    @FieldDoc(description = "UPC列表")
    private List<String> upcList;
}
