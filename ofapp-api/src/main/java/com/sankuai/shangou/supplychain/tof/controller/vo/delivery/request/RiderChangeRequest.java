package com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "骑手改派（商家自营配送）请求"
)
@Data
public class RiderChangeRequest {

    @FieldDoc(description = "门店id", requiredness = Requiredness.REQUIRED)
    @NotNull
    private Long storeId;

    @FieldDoc(description = "赋能订单号", requiredness = Requiredness.REQUIRED)
    @NotNull
    private Long orderId;

    @FieldDoc(description = "改派后的骑手账号", requiredness = Requiredness.REQUIRED)
    @NotNull
    private Long riderAccountId;
}
