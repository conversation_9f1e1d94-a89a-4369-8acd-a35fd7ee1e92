package com.sankuai.shangou.supplychain.tof.controller.vo.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@TypeDoc(
        description = "标签信息"
)
@Data
public class TagInfoVO {
    @FieldDoc(
            description = "标签类型 1-履约标签 2-挑选标准 3-商品属性", requiredness = Requiredness.REQUIRED
    )
    private Integer type;

    @FieldDoc(
            description = "标签名称", requiredness = Requiredness.REQUIRED
    )
    private String name;


}
