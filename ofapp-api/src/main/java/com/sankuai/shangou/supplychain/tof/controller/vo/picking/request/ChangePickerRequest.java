package com.sankuai.shangou.supplychain.tof.controller.vo.picking.request;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/9/5 16:55
 **/
@Data
public class ChangePickerRequest {
    private String channelOrderId;

    private Integer channelId;

    private Long newPickerAccountId;

    private String newPickerName;

    public String validate() {
        if (StringUtils.isBlank(channelOrderId)) {
            return "订单号不能为空";
        }

        if (channelId == null || channelId <= 0) {
            return "渠道id不合法";
        }

        if (newPickerAccountId == null || newPickerAccountId <= 0L) {
            return "拣货员账号id不合法";
        }

        if (StringUtils.isBlank(newPickerName)) {
            return "拣货员名字不能为空";
        }

        return null;

    }
}
