package com.sankuai.shangou.supplychain.tof.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.reco.store.management.stock.biz.base.thrift.query.ContainerStockInfo;
import com.sankuai.meituan.reco.store.management.stock.biz.base.thrift.query.QueryBySkuIdRequest;
import com.sankuai.meituan.reco.store.management.stock.biz.base.thrift.query.QueryStockThriftService;
import com.sankuai.meituan.reco.store.management.stock.biz.commons.constants.ContainerType;
import com.sankuai.meituan.reco.store.management.stock.biz.commons.constants.RepositoryType;
import com.sankuai.meituan.reco.store.management.stock.biz.commons.constants.StatusEnum;
import com.sankuai.meituan.reco.store.management.stock.biz.commons.constants.StockZoneType;
import com.sankuai.shangou.common.Page;
import com.sankuai.shangou.common.Result;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.warehouse.InventoryQueryService;
import com.sankuai.shangou.logistics.warehouse.inventory.dto.LocationInventoryDTO;
import com.sankuai.shangou.logistics.warehouse.inventory.request.QueryByPageReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Retryable;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-05-15
 * @email <EMAIL>
 */
@Rhino
@Slf4j
public class StockWrapper {


    @Resource
    private QueryStockThriftService.Iface queryStockThriftService;

    @Resource
    private InventoryQueryService inventoryQueryService;

    // 存捡区库位类型
    private static List<ContainerType> saleZoneType = Arrays.asList(
            StockZoneType.SALES_ZONE.getSysContainerType(),
            StockZoneType.SALES_ZONE.getManualContainerType(),
            ContainerType.MANUAL_DEFINED_STORAGE_LOCATION
    );

    public Map<String, BigDecimal> querySaleZoneSkuStockAvailableFallback(Long warehouseId, Long merchantId, List<String> skuIds) {
        log.error("StockWrapper.querySaleZoneSkuStockAvailable 发生降级");
        return Collections.emptyMap();
    }


    /**
     * 根据sku查找仓内存捡区总实物库存情况
     */
    @MethodLog(logRequest = true,logResponse = true)
    @Degrade(rhinoKey = "StockWrapper.querySaleZoneSkuStockAvailable", fallBackMethod = "querySaleZoneSkuStockAvailableFallback", timeoutInMilliseconds = 2000)
    public Map<String, BigDecimal> querySaleZoneSkuStockAvailable(Long warehouseId, Long merchantId, List<String> skuIds) {
        try {
            Result<List<LocationInventoryDTO>> result = inventoryQueryService.queryInventoryBySku(
                    warehouseId, skuIds, IListUtils.mapTo(StockZoneType.SALES_ZONE.getContainerType(), ContainerType::val)
            );
            log.info("inventoryQueryService.queryInventoryBySku response = {}", result);
            if (!Objects.equals(result.getCode(), StatusEnum.SUCCESS.getCode())) {
                throw new ThirdPartyException();
            }

            return IListUtils.nullSafeStream(result.getModule()).collect(Collectors.toMap(
                    LocationInventoryDTO::getSkuId,
                    locationInventoryDTO -> locationInventoryDTO.getQuantity().subtract(locationInventoryDTO.getLockQuantity()),
                    BigDecimal::add
            ));
        } catch (Exception e) {
            throw new ThirdPartyException("queryStockThriftService.queryStockInfo failed");
        }
    }

    /**
     * 根据sku查找指定库区总实物库存情况
     */
    @Degrade(rhinoKey = "StockWrapper.querySkuStockByStockZoneType", fallBackMethod = "querySkuStockByStockZoneTypeFallback", timeoutInMilliseconds = 1000)
    public List<LocationInventoryDTO> querySkuStockByStockZoneType(Long warehouseId, List<String> skuIds, StockZoneType stockZoneType) {
        try {
            log.info("inventoryQueryService.queryInventoryBySku warehouseId: {}, skuId : {}, stockZoneType: {}", warehouseId, skuIds, stockZoneType);
            Result<List<LocationInventoryDTO>> result = inventoryQueryService.queryInventoryBySku(
                    warehouseId, skuIds, IListUtils.mapTo(stockZoneType.getContainerType(), ContainerType::val)
            );
            log.info("inventoryQueryService.queryInventoryBySku response: {}", result);
            if (!Objects.equals(result.getCode(), StatusEnum.SUCCESS.getCode())) {
                throw new BizException("查询库存失败");
            }

            return result.getModule();
        } catch (Exception e) {
            throw new ThirdPartyException("查询库存失败");
        }
    }

    @Degrade(rhinoKey = "StockWrapper.queryInventoryByLocation", fallBackMethod = "queryInventoryByLocationFallback", timeoutInMilliseconds = 2000)
    public List<LocationInventoryDTO> queryInventoryByLocationAndSkuId(Long warehouseId, List<String> locationCodes) {
        Result<List<LocationInventoryDTO>> result;
        try {
            log.info("start invoke inventoryQueryService.queryInventoryByLocation, warehouseId: {}, locationCodes: {}",
                    warehouseId, locationCodes);
            result = inventoryQueryService.queryInventoryByLocation(warehouseId, locationCodes);
            log.info("end invoke inventoryQueryService.queryInventoryByLocation, result: {}", result);
        } catch (Exception e) {
            log.error("查询库位库存失败", e);
            throw new ThirdPartyException("查询库位库存失败");
        }

        if (!result.isSuccess()) {
            throw new BizException("查询库位库存失败");
        }

        return result.getModule();
    }

    @Degrade(rhinoKey = "StockWrapper.queryInventoryByLocationAndSkuId", fallBackMethod = "queryInventoryByLocationAndSkuIdFallback", timeoutInMilliseconds = 2000)
    public List<LocationInventoryDTO> queryInventoryByLocationAndSkuId(Long warehouseId, List<String> locationCodes, List<String> skuIds) {
        TResult<Page<LocationInventoryDTO>> result;
        try {
            log.info("start invoke inventoryQueryService.queryByPage, warehouseId: {}, locationCodes: {}",
                    warehouseId, locationCodes);
            QueryByPageReq request = new QueryByPageReq();
            request.setLocationCodes(locationCodes);
            request.setWarehouseId(warehouseId);
            request.setSkuIds(skuIds);
            request.setPageNo(1);
            request.setPageSize(50);
            result = inventoryQueryService.queryByPage(request);
            log.info("end invoke inventoryQueryService.queryByPage, result: {}", result);
        } catch (Exception e) {
            log.error("查询库位库存失败", e);
            throw new ThirdPartyException("查询库位库存失败");
        }

        if (!result.isSuccess()) {
            throw new BizException("查询库位库存失败");
        }

        return result.getData().getItems();
    }

    public List<LocationInventoryDTO> queryInventoryByLocationFallback(Long warehouseId, List<String> locationCodes) {
        log.error("StockWrapper.queryInventoryByLocation 发生降级");
        return Collections.emptyList();
    }

    public List<LocationInventoryDTO> queryInventoryByLocationAndSkuIdFallback(Long warehouseId, List<String> locationCodes, List<String> skuIds) {
        log.error("StockWrapper.queryInventoryByLocation 发生降级");
        return Collections.emptyList();
    }

    public List<LocationInventoryDTO> querySkuStockByStockZoneTypeFallback(Long warehouseId, List<String> skuIds, StockZoneType stockZoneType) {
        log.error("StockWrapper.querySkuStockByStockZoneType 发生降级");
        return Collections.emptyList();
    }
}
