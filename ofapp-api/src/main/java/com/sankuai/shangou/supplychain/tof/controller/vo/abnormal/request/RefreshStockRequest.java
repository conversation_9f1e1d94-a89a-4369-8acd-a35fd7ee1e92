package com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023-12-11
 * @email <EMAIL>
 */
@TypeDoc(
        description = "异常单操作-刷新库存请求"
)
@Data
public class RefreshStockRequest {
    @FieldDoc(
            description = "订单类型", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Integer orderBizType;
    @FieldDoc(
            description = "订单编号", requiredness = Requiredness.REQUIRED
    )
    private String channelOrderId;
}
