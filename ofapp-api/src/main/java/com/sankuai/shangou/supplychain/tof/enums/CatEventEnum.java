package com.sankuai.shangou.supplychain.tof.enums;

/**
 * <AUTHOR>
 * @date 2023-08-31
 * @email <EMAIL>
 */
public enum CatEventEnum {

    ASSIGN_VERIFY_TASK_SUCCESS("ASSIGN_VERIFY_TASK", "SUCCESS"),

    ASSIGN_VERIFY_TASK_ERROR("ASSIGN_VERIFY_TASK", "ERROR");

    private String type;
    private String name;

    CatEventEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }


    public static CatEventEnum valueOfType(String type) {
        for (CatEventEnum obj : CatEventEnum.values()) {
            if (java.util.Objects.equals(obj.type, type)) {
                return obj;
            }
        }
        return null;
    }

    public static CatEventEnum valueOfName(String name) {
        for (CatEventEnum obj : CatEventEnum.values()) {
            if (java.util.Objects.equals(obj.name, name)) {
                return obj;
            }
        }
        return null;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
