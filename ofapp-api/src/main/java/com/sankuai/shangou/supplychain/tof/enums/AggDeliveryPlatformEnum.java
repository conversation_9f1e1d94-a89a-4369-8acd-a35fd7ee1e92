package com.sankuai.shangou.supplychain.tof.enums;

import com.google.common.collect.ImmutableList;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TAggDeliveryPlatformConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.AggDeliveryPlatformConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.MaltFarmSignUtils;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryRedirectModuleVo;
import com.sankuai.shangou.supplychain.tof.utils.UrlUtils;

import java.text.MessageFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/04/10 20:28
 */
public enum AggDeliveryPlatformEnum {
    /**
     * 麦芽田
     */
    MALT_FARM(2, "使用麦芽田配送", "麦芽田配送设置",
            "麦芽田配送", "查看配送详情>", "去处理异常>",
            ImmutableList.of(-2, 20000, 20001, 20002, 20003, 20004, 20005, 20006, 20023, 20024, 20025, 20026, 20027, 20028, 20029, 20030, 20031, 20032, 20033, 20034, 20035, 20036, 29999)),
    DAP_DELIVERY(4,"使用青云聚信平台","青云聚信平台设置","青云聚信平台","查看配送详情>", "去处理异常>",
            ImmutableList.of(-4, 40000, 40001, 40002, 40003, 40004,40005, 49999, 40017)),

    ;

    AggDeliveryPlatformEnum(Integer code, String configTitle, String configRedirectTitle,
                            String deliveryTitle, String deliveryRedirectTitle, String deliveryExceptionRedirectTitle) {
        this(code, configTitle, configRedirectTitle, deliveryTitle, deliveryRedirectTitle, deliveryExceptionRedirectTitle, null);
    }

    AggDeliveryPlatformEnum(Integer code, String configTitle, String configRedirectTitle,
                            String deliveryTitle, String deliveryRedirectTitle, String deliveryExceptionRedirectTitle,
                            List<Integer> deliveryChannelCodes) {
        this.code = code;
        this.configTitle = configTitle;
        this.configRedirectTitle = configRedirectTitle;
        this.deliveryTitle = deliveryTitle;
        this.deliveryRedirectTitle = deliveryRedirectTitle;
        this.deliveryExceptionRedirectTitle = deliveryExceptionRedirectTitle;
        this.devlieryChannelCodes = Optional.ofNullable(deliveryChannelCodes).orElse(Collections.emptyList());
    }

    private Integer code;

    private String configTitle;

    private String configRedirectTitle;

    private String deliveryTitle;

    private String deliveryRedirectTitle;

    private String deliveryExceptionRedirectTitle;

    private List<Integer> devlieryChannelCodes;

    public Integer getCode() {
        return code;
    }

    public String getConfigTitle() {
        return configTitle;
    }

    public String getConfigRedirectTitle() {
        return configRedirectTitle;
    }

    public String getDeliveryTitle() {
        return deliveryTitle;
    }

    public String getDeliveryRedirectTitle() {
        return deliveryRedirectTitle;
    }

    public List<Integer> getDevlieryChannelCodes() {
        return devlieryChannelCodes;
    }

    public boolean checkChannel(Integer deliveryChannelCode){
        if(deliveryChannelCode == null){
            return false;
        }
        return devlieryChannelCodes.contains(deliveryChannelCode);
    }


    public static AggDeliveryPlatformEnum codeValueOf(int code){
        AggDeliveryPlatformEnum[] data=values();
        for (AggDeliveryPlatformEnum platformEnum : data){
            if(platformEnum.getCode().equals(code)){
                return platformEnum;
            }
        }
        return null;
    }

    public static AggDeliveryPlatformEnum channelCodeValueOf(int channelCode){
        AggDeliveryPlatformEnum[] data=values();
        for (AggDeliveryPlatformEnum platformEnum : data){
            if(platformEnum.getDevlieryChannelCodes().contains(channelCode)){
                return platformEnum;
            }
        }
        return null;
    }

    public DeliveryRedirectModuleVo fillDeliveryRedirectModule(String redirectUrl, boolean isExceptional){

        DeliveryRedirectModuleVo configVo = new DeliveryRedirectModuleVo();
        configVo.setUrl(redirectUrl);
        if (isExceptional) {
            configVo.setUrlText(deliveryExceptionRedirectTitle);
        } else {
            configVo.setUrlText(deliveryRedirectTitle);
        }
        configVo.setTitle(deliveryTitle);
        configVo.setShowButton(true);
        return configVo;
    }

}
