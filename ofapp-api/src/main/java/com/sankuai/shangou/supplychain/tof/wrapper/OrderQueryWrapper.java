package com.sankuai.shangou.supplychain.tof.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryRequest;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderQueryResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-12-22
 * @email <EMAIL>
 */
@Slf4j
@Rhino
@Service
public class OrderQueryWrapper {

    @Resource
    private BizOrderThriftService bizOrderThriftService;

    @Degrade(rhinoKey = "OrderBizRemoteService.queryOrderDetail",
            fallBackMethod = "queryOrderDetailFallback",
            timeoutInMilliseconds = 2000)
    public BizOrderModel queryOrderDetail(Long tenantId, Long storeId, Integer channelId, String viewOrderId) throws TException {

        BizOrderQueryRequest request = new BizOrderQueryRequest();
        request.setTenantId(tenantId);
        request.setViewOrderId(viewOrderId);
        request.setShopId(storeId);
        request.setOrderBizType(ChannelOrderConvertUtils.convertBizType(channelId));
        request.setContainsComposeSku(true);
        log.info("start invoke bizOrderThriftService.query, request: {}", request);
        BizOrderQueryResponse response = bizOrderThriftService.query(request);
        log.info("end invoke bizOrderThriftService.query, response: {}", response);

        if (!Objects.equals(response.getStatus().getCode(), ResultCode.SUCCESS.getCode())) {
            throw new ThirdPartyException("调用订单服务查询订单明细失败");
        }

        return response.getBizOrderModel();
    }

    public BizOrderModel queryOrderDetailFallback(Long tenantId, Long storeId, Integer orderSource, String viewOrderId) throws TException {
        log.warn("OrderBizRemoteService.queryOrderDetail 发生降级");
        return null;
    }


    @Degrade(rhinoKey = "OrderBizRemoteService.queryOrderDetailByBizType",
            fallBackMethod = "queryOrderDetailByBizTypeFallback",
            timeoutInMilliseconds = 2000)
    public BizOrderModel queryOrderDetailByBizType(Long tenantId, Long storeId, Integer orderBizType, String viewOrderId) throws TException {

        BizOrderQueryRequest request = new BizOrderQueryRequest();
        request.setTenantId(tenantId);
        request.setViewOrderId(viewOrderId);
        request.setShopId(storeId);
        request.setOrderBizType(orderBizType);
        request.setContainsComposeSku(true);
        log.info("start invoke bizOrderThriftService.query, request: {}", request);
        BizOrderQueryResponse response = bizOrderThriftService.query(request);
        log.info("end invoke bizOrderThriftService.query, response: {}", response);

        if (!Objects.equals(response.getStatus().getCode(), ResultCode.SUCCESS.getCode())) {
            throw new ThirdPartyException("调用订单服务查询订单明细失败");
        }

        return response.getBizOrderModel();
    }

    public BizOrderModel queryOrderDetailByBizTypeFallback(Long tenantId, Long storeId, Integer orderBizType, String viewOrderId) throws TException {
        log.warn("OrderBizRemoteService.queryOrderDetailByBizType 发生降级");
        return null;
    }

}
