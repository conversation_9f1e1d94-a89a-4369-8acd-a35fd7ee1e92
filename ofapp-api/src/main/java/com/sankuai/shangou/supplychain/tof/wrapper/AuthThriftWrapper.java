package com.sankuai.shangou.supplychain.tof.wrapper;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.ImmutableMap;
import com.meituan.shangou.sac.dto.request.authenticate.AccountAuthPermissionsRequest;
import com.meituan.shangou.sac.dto.response.authenticate.AccountAuthPermissionsResponse;
import com.meituan.shangou.sac.thrift.authenticate.AuthenticateService;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ValidTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PageInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountInfoListResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.QueryAccountListByPoiAndAuthRequest;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 权限相关
 */
@Component
@Slf4j
@Rhino
public class AuthThriftWrapper {

    @Resource
    private AuthenticateService authenticateService;

    @Resource
    private AuthThriftService.Iface authThriftService;

    private static final int MAX_QUERY_COUNT = 5;

    /**
     * 判断登录账号是否有指定权限码的权限
     * @param currPermissionCodes
     * @return
     */
    public Map<String/*permissionCode*/, Boolean> isHasPermission(List<String> currPermissionCodes) {
            SessionInfo sessionInfo = SessionContext.getCurrentSession();
            int appId = AppLoginContextHolder.getAppLoginContext().getAppId();
            return accountAuthPermissions(sessionInfo.getAccountId(), appId, currPermissionCodes);
    }

    /**
     * 批量查询权限code是否有权限
     *
     * @param accountId
     * @param authId
     * @param permissionCodes
     * @return
     */
    private Map<String, Boolean> accountAuthPermissions(long accountId, int authId, List<String> permissionCodes) {
        if (CollectionUtils.isEmpty(permissionCodes)) {
            return Collections.emptyMap();
        }
        Map<String, Boolean> result = permissionCodes
                .stream()
                .distinct()
                .collect(Collectors.toMap(Function.identity(),
                        s -> false));
        AccountAuthPermissionsRequest request = new AccountAuthPermissionsRequest();
        request.setAccountId(accountId);
        request.setAppId(authId);
        request.setPermissionCodes(permissionCodes);
        AccountAuthPermissionsResponse response;
        try {
            log.info("批量查询权限code是否有权限 request:{}", JSON.toJSONString(request));
            response = authenticateService.accountAuthPermissions(request);
            log.info("批量查询权限code是否有权限 response:{}", JSON.toJSONString(response));
        }
        catch (Exception e) {
            log.error("批量查询权限code是否有权限异常 request:{}", request);
            return result;
        }
        if (response == null || response.sacStatus == null || response.sacStatus.code != 0) {
            log.error("批量查询权限code是否有权限失败,request:{},response:{}", request, response);
            return result;
        }
        if (MapUtils.isNotEmpty(response.getAuthResult())) {
            response.getAuthResult().forEach(result::put);
        }
        return result;
    }

    /**
     * 全量查询拥有某个门店权限的账号列表
     * @param tenantId
     * @param storeId
     * @return
     */
    public List<AccountInfoVo> batchQueryAccountListWithPoiPermission(Long tenantId, Long storeId) {
        int pageNum = 1;
        int dataAuthType = PermissionGroupTypeEnum.POI.getValue();
        QueryAccountListByPoiAndAuthRequest request = new QueryAccountListByPoiAndAuthRequest();
        request.setTenantId(tenantId);
        request.setAppId(AppLoginContextHolder.getAppLoginContext().getAppId());
        request.setCodes(Arrays.asList(String.valueOf(storeId)));
        request.setPermissionGroupType(dataAuthType);
        request.setValid(ValidTypeEnum.NORMAL.getValue());
        request.setPageSize(200);

        QueryAccountInfoListResponse resp = null;
        List<AccountInfoVo> result = new ArrayList<>();
        do {
            request.setPageNum(pageNum);
            resp = queryAccountListByPoiAndAuth(request);
            if (CollectionUtils.isNotEmpty(resp.getAccountInfoList())) {
                result.addAll(resp.getAccountInfoList());
            }
            pageNum++;
        } while (Objects.nonNull(resp.getPageInfo()) && resp.getPageInfo().getPages() > pageNum - 1
                && pageNum <= MAX_QUERY_COUNT);

        if (Objects.nonNull(resp.getPageInfo()) && resp.getPageInfo().getPages() > MAX_QUERY_COUNT) {
            log.warn("AuthThriftWrapper.batchQueryAccountListWithPoiPermission pages > 5, request:{}", request);
            Cat.logEvent("AUTH_THRIFT_WRAPPER", "PAGES_MORE_THAN_5");
        }
        return result;

    }

    public List<Long> queryAccountListByPoiAndAuth(Long tenantId, Long storeId, String permissionCode) {
        QueryAccountListByPoiAndAuthRequest request = new QueryAccountListByPoiAndAuthRequest();
        request.setAppId(AppLoginContextHolder.getAppLoginContext().getAppId());
        request.setTenantId(tenantId);
        ImmutableMap<Integer, List<String>> dataAuthMap = ImmutableMap.of(PermissionGroupTypeEnum.POI.getValue(),
                Collections.singletonList(storeId.toString()));
        request.setDataAuthMap(dataAuthMap);
        request.setPermissionCodeList(Collections.singletonList(permissionCode));
        request.setValid(1);
        request.setPageSize(100);
        QueryAccountInfoListResponse response = null;
        List<Long> res = new ArrayList<>();
        do {
            request.setPageNum(Optional.ofNullable(response)
                    .map(QueryAccountInfoListResponse::getPageInfo)
                    .map(PageInfoVo::getPageNumber)
                    .orElse(0) + 1);
            try {
                response = authThriftService.queryAccountListByPoiAndAuth(request);
            } catch (Exception e) {
                log.error("查询权限失败", e);
                throw new ThirdPartyException("查询权限失败");
            }

            if (response.getResult().getCode() != 0) {
                throw new BizException("查询拥有某个门店权限的账号列表异常，请稍后重试.");
            }
            List<Long> accountIds = response.getAccountInfoList().stream().map(AccountInfoVo::getAccountId).collect(Collectors.toList());
            res.addAll(accountIds);
        } while (response.getPageInfo().getPages() > response.getPageInfo().getPageNumber());

        return res;
    }


    private QueryAccountInfoListResponse queryAccountListByPoiAndAuth(QueryAccountListByPoiAndAuthRequest request) {
        QueryAccountInfoListResponse resp = null;
        try {
            resp = authThriftService.queryAccountListByPoiAndAuth(request);
            log.info("queryAccountListWithPoiPermission resp, request:{}, resp:{}", request, resp);
            if (resp.getResult().getCode() != 0) {
                throw new BizException("查询拥有某个门店权限的账号列表异常，请稍后重试.");
            }
            return resp;
        } catch (Exception e) {
            log.warn("查询拥有某个门店权限的账号列表异常: request [{}].", request, e);
            throw new ThirdPartyException("查询拥有某个门店权限的账号列表异常，请稍后重试.");
        }
    }

    public List<String> queryAuthorizedCodes(List<String> authCodes) {
        if (CollectionUtils.isEmpty(authCodes)) {
            return Collections.emptyList();
        }

        SessionInfo sessionInfo = SessionContext.getCurrentSession();

        Map<String, Boolean> userPermissionCodeMap = accountAuthPermissions(sessionInfo.getAccountId(),
                AppLoginContextHolder.getAppLoginContext().getAppId(), authCodes);

        return userPermissionCodeMap.entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());
    }
}
