package com.sankuai.shangou.supplychain.tof.controller.vo.picking.request;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/6/24 19:59
 **/
@Data
public class ManualSealDegradeRequest {
    private Integer reasonCode;

    private String reasonDetail;

    private String tradeShippingOrderNo;

    public String validate() {
        if (Objects.isNull(reasonCode)) {
            return "降级原因不能为空";
        }

        if (StringUtils.isNotBlank(reasonDetail) && reasonDetail.length() > 100) {
            return "情况说明不能超过100个字";
        }
        if (StringUtils.isBlank(tradeShippingOrderNo)) {
            return "出库单号不能为空";
        }

        return null;
    }
}
