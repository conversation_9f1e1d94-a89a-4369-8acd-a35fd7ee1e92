package com.sankuai.shangou.supplychain.tof.controller.convert;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.goodscenter.dto.GoodsExpirationDto;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.DrunkHorseGiftBagVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSDeliveryInfoVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.enums.DeliveryOrderType;
import com.meituan.shangou.saas.utils.ChannelTypeConvertUtils;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.TConsumableMaterialInfo;
import com.sankuai.meituan.reco.stock.operate.center.common.TemperatureType;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderOperateTRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryRiskControlOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.shangou.commons.auth.login.context.LoginUser;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.delivery.questionnaire.dto.DeliveryQuestionnaireDTO;
import com.sankuai.shangou.logistics.warehouse.dto.*;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import com.sankuai.shangou.supplychain.tof.component.TradeOrderInfoComponent;
import com.sankuai.shangou.supplychain.tof.config.LionConfigUtils;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.*;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.RevenueDetailVo;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.TradeOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.PickingOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.RiderPickTaskInfoVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.RiderPickingDetailVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.request.RiderFinishPickingRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.third.response.ThirdDeliveryPickingOrderVO;
import com.sankuai.shangou.supplychain.tof.enums.ChannelTypeEnum;
import com.sankuai.shangou.supplychain.tof.enums.DepotGoodsExpireUnitEnum;
import com.sankuai.shangou.supplychain.tof.enums.ShowTagEnum;
import com.sankuai.shangou.supplychain.tof.utils.ConsumableMaterialParseUtils;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.reco.pickselect.consts.TemperaturePropertyEnum.temperaturePropertyEnumOf;
import static com.sankuai.shangou.supplychain.tof.controller.convert.RiderDeliveryConvert.transfer2GoodsItemVO;

/**
 * <AUTHOR>
 * @since 2024/4/30 16:33
 **/
@Slf4j
public class RiderPickingConvert {
    //品牌礼袋type值
    private static final int BRAND_GIFT_BAG_TYPE = 1;
    //歪马礼袋type值
    private static final int DH_GIFT_BAG_TYPE = 2;


    public static RiderOperateTRequest convert2DeliveryRequest(RiderFinishPickingRequest request, LoginUser user,Long storeId) {
        RiderOperateTRequest tRequest = new RiderOperateTRequest();
        tRequest.setDeliveryOrderId(request.getDeliveryOrderId());

        tRequest.setOperatorId(user.getAccountId());
        tRequest.setOperatorName(user.getEmployeeName());
        tRequest.setOperatorPhone(user.getEmployeePhone());
        tRequest.setTenantId(user.getTenantId());
        tRequest.setStoreId(storeId);
        if (Objects.nonNull(request.getLocationInfo())) {
            tRequest.setLatitude(request.getLocationInfo().getLatitude());
            tRequest.setLongitude(request.getLocationInfo().getLongitude());
        }
        return tRequest;
    }


    public static RiderPickingDetailVO buildRiderPickingDetailVO(TradeShippingOrderDTO shippingOrderDTO,
                                                           OCMSOrderVO ocmsOrderVO,
                                                           Map<RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> recommendLocationBatchMap,
                                                           Map<String, DepotGoodsDetailDto> goodsDetailDtoMap,
                                                           Map<String, Boolean> skuHighPriceTagMap,
                                                           Optional<AbnOrderDTO> abnOrderDTOOpt,
                                                                 Long deliveryOrderId) {

        RiderPickingDetailVO riderPickingDetailVO = new RiderPickingDetailVO();

        //拣货单信息
        riderPickingDetailVO.setTenantId(shippingOrderDTO.getMerchantId());
        riderPickingDetailVO.setStoreId(shippingOrderDTO.getWarehouseId());
        riderPickingDetailVO.setChannelId(ChannelTypeConvertUtils.convert(shippingOrderDTO.getTradeChannelType()));
        riderPickingDetailVO.setChannelName(ChannelTypeEnum.findChannelNameByChannelId(ChannelTypeConvertUtils.convert(shippingOrderDTO.getTradeChannelType())));
        riderPickingDetailVO.setChannelOrderId(shippingOrderDTO.getTradeOrderNo());
        riderPickingDetailVO.setPickWorkOrderId(Long.parseLong(shippingOrderDTO.getOrderNo()));
        riderPickingDetailVO.setPickWorkOrderStatus(shippingOrderDTO.getStatus());

        riderPickingDetailVO.setItemCount(getTotalCount(shippingOrderDTO.getItems()));
        riderPickingDetailVO.setIsWeakCheckSn(MccConfigUtil.checkIsSnWeekCheckStore(shippingOrderDTO.getWarehouseId()));

        //拣货项
        riderPickingDetailVO.setWaitPickTasks(buildPickTaskVoList(shippingOrderDTO, skuHighPriceTagMap, recommendLocationBatchMap, goodsDetailDtoMap));

        //订单信息
        if (Objects.nonNull(ocmsOrderVO)) {
            riderPickingDetailVO.setSerialNo(ocmsOrderVO.getOrderSerialNumber());
            int deliveryOrderType = ocmsOrderVO.getIsBooking() == 1 ? DeliveryOrderType.DELIVERY_BY_BOOK_TIME.getValue() :
                    DeliveryOrderType.DELIVERY_RIGHT_NOW.getValue();
            riderPickingDetailVO.setDeliveryOrderType(deliveryOrderType);
            riderPickingDetailVO.setDeliveryOrderTypeName(getDeliveryOrderTypeName(deliveryOrderType));
            riderPickingDetailVO.setCreateTime(ocmsOrderVO.getCreateTime());

            OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrderVO.getOcmsDeliveryInfoVO();
            if (ocmsDeliveryInfoVO != null) {
                riderPickingDetailVO.setEstimateArriveTimeStart(ocmsDeliveryInfoVO.getArrivalTime());
                riderPickingDetailVO.setEstimateArriveTimeEnd(ocmsDeliveryInfoVO.getArrivalEndTime());
                riderPickingDetailVO.setReceiverName(ocmsDeliveryInfoVO.getUserName());
                riderPickingDetailVO.setReceiverPhone(ocmsDeliveryInfoVO.getUserPhone());
                riderPickingDetailVO.setReceiverAddress(ocmsDeliveryInfoVO.getUserAddress());

            }
            //耗材信息
            riderPickingDetailVO.setConsumableMaterialInfoList(parseConsumableMaterialInfo(ocmsOrderVO));
            riderPickingDetailVO.setComments(ocmsOrderVO.getComments());
        }

        //缺货项
        abnOrderDTOOpt.ifPresent(abnOrderDTO -> riderPickingDetailVO.setLackStockGoodsList(parseLackStockGoodsInfo(abnOrderDTO, goodsDetailDtoMap)));

        riderPickingDetailVO.setDeliveryOrderId(deliveryOrderId);


        int sealQty = IListUtils.nullSafeFilterElement(shippingOrderDTO.getItems(), tradeShippingOrderItemDTO -> Objects.nonNull(tradeShippingOrderItemDTO.getNeedSealDelivery()) && tradeShippingOrderItemDTO.getNeedSealDelivery())
                .stream()
                .mapToInt(tradeShippingOrderItemDTO -> tradeShippingOrderItemDTO.getActualQty().intValue())
                .reduce(Integer::sum)
                .orElse(0);
        // 目前该场景已经不存在，这里统一返回 false
        // riderPickingDetailVO.setIsSealGoodsQtyExceedLimit(sealQty > LionConfigUtils.getSealGoodsQtyExceedLimit());
        riderPickingDetailVO.setIsSealGoodsQtyExceedLimit(false);

        setGiftBagHint(ocmsOrderVO, riderPickingDetailVO);

        riderPickingDetailVO.setDefaultOpenScanner(MccConfigUtil.getDefaultOpenScannerSwitch());
        return riderPickingDetailVO;
    }

    private static void setGiftBagHint(OCMSOrderVO ocmsOrderVO, RiderPickingDetailVO riderPickingDetailVO) {
        try {
            if (CollectionUtils.isNotEmpty(ocmsOrderVO.getGiftBagList()) && LionConfigUtils.getNeedGiftBagHint()) {
                Map<Integer, List<DrunkHorseGiftBagVo>> typeGiftTypeBagMap = IListUtils.nullSafeGroupBy(ocmsOrderVO.getGiftBagList(), DrunkHorseGiftBagVo::getType);
                //如果有歪马礼袋，且替换品牌礼袋量>0
                if (typeGiftTypeBagMap.containsKey(DH_GIFT_BAG_TYPE)) {
                    DrunkHorseGiftBagVo drunkHorseGiftBagVo = typeGiftTypeBagMap.get(DH_GIFT_BAG_TYPE).get(0);
                    if (drunkHorseGiftBagVo.getShouldReplaceCnt() > 0) {
                        riderPickingDetailVO.setGiftBagHint(LionConfigUtils.getGiftBagReplaceHint());
                    }
                }
                //所有品牌礼袋没有替换的，库存充足
                if (typeGiftTypeBagMap.containsKey(BRAND_GIFT_BAG_TYPE)) {
                    List<DrunkHorseGiftBagVo> drunkHorseGiftBagVos = typeGiftTypeBagMap.get(BRAND_GIFT_BAG_TYPE);
                    boolean isAllStockEnough = drunkHorseGiftBagVos.stream().allMatch(drunkHorseGiftBagVo -> Objects.equals(drunkHorseGiftBagVo.getShouldReplaceCnt(), 0));
                    if (isAllStockEnough) {
                        riderPickingDetailVO.setGiftBagHint(LionConfigUtils.getGiftBagEnoughHint());
                    }
                }
            }
        } catch (Exception e) {
            log.error("setGiftBagHint error", e);
        }
    }

    public static Integer getTotalCount(List<TradeShippingOrderItemDTO> tradeOutboundOrderItemDTOS) {
        if (CollectionUtils.isEmpty(tradeOutboundOrderItemDTOS)) {
            return 0;
        }
        return tradeOutboundOrderItemDTOS.stream().mapToInt(task -> Optional.ofNullable(task.getActualQty()).orElse(BigDecimal.ZERO).intValue()).sum();
    }

    public static List<RiderPickingDetailVO.LackStockGoodsVO> parseLackStockGoodsInfo(AbnOrderDTO abnOrder, Map<String, DepotGoodsDetailDto> goodsDetailDtoMap) {
        try {
            List<String> lackStockGoodsIds = abnOrder.getItems().stream().map(AbnOrderItemDTO::getSkuId).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(lackStockGoodsIds)) {
                return Collections.emptyList();
            }

            return  abnOrder.getItems().stream()
                    .map(item -> RiderPickingConvert.buildLackStockGoodsVO(item, goodsDetailDtoMap.get(item.getSkuId())))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("填充缺货信息失败", e);
            Cat.logEvent("PART_REFUND", "FILL_ABN_ORDER_ITEM_FAIL");
            return Collections.emptyList();
        }
    }


    public static RiderPickingDetailVO.LackStockGoodsVO buildLackStockGoodsVO(AbnOrderItemDTO abnOrderItemDTO, DepotGoodsDetailDto goods) {
        if (Objects.isNull(goods)) {
            log.error("未获取到货品信息, skuId: {}", abnOrderItemDTO.getSkuId());
            throw new BizException("未获取到货品信息");
        }

        return RiderPickingDetailVO.LackStockGoodsVO.builder()
                .goodsName(goods.getGoodsName())
                .skuId(abnOrderItemDTO.getSkuId())
                .picUrl(parseRealPicUrl(goods))
                .spec(goods.getSpecName())
                .upcList(goods.getUpcList())
                .needCount(abnOrderItemDTO.getDemandQuantity().intValue())
                .salableCount(abnOrderItemDTO.getAvailableQuantity().intValue())
                .lackCount(abnOrderItemDTO.getDemandQuantity().subtract(abnOrderItemDTO.getAvailableQuantity()).intValue())
                .build();
    }

    public static String parseRealPicUrl(DepotGoodsDetailDto goods) {
        if (Objects.isNull(goods) || Objects.isNull(goods.getGoodsPic())
                || CollectionUtils.isEmpty(goods.getGoodsPic().getRealPicUrlList())) {
            return "";
        }
        return goods.getGoodsPic().getRealPicUrlList().get(0);
    }

    public static List<RiderPickTaskInfoVO> buildPickTaskVoList(TradeShippingOrderDTO shippingOrderDTO,
                                                          Map<String, Boolean> skuHighPriceTagMap,
                                                          Map<RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> recommendLocationBatchMap,
                                                          Map<String, DepotGoodsDetailDto> depotGoodsDetailDtoMap) {
        boolean isSealGrayStore = GrayConfigUtils.judgeIsGrayStore(shippingOrderDTO.getMerchantId(), shippingOrderDTO.getWarehouseId(), GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false);

        return shippingOrderDTO.getItems().stream().map(itemDTO -> {
            RiderPickTaskInfoVO pickTask = new RiderPickTaskInfoVO();

            if (CollectionUtils.isNotEmpty(itemDTO.getImgUrls())) {
                pickTask.setPicUrl(itemDTO.getImgUrls().get(0));
            }

            pickTask.setShowRealPic(true);
            pickTask.setPickTaskId(itemDTO.getId());
            pickTask.setSkuId(itemDTO.getSkuId());
            pickTask.setSkuName(itemDTO.getSkuName());
            pickTask.setUpcCodes(buildUpcAndBoxCodeSet(itemDTO));
            pickTask.setSpecification(itemDTO.getSpecification());
            pickTask.setCount(itemDTO.getActualQty().intValue());
            pickTask.setTemperatureAttributeType(Optional.ofNullable(itemDTO.getTemperatureZoneCode()).orElse(StringUtils.EMPTY));
            pickTask.setIsSnProduct(itemDTO.getIsManagementSnCode());
            pickTask.setHavePartRefundFlag(Objects.nonNull(itemDTO.getRefundQty()) && itemDTO.getRefundQty().compareTo(BigDecimal.ZERO) > 0);
            pickTask.setRealPicUrlList(itemDTO.getImgUrls());

            //封签标识
            try {
                if (isSealGrayStore) {
                    if (Objects.equals(shippingOrderDTO.getIsSealDegrade(), true)) {
                        pickTask.setIsSealDelivery(false);
                    } else {
                        pickTask.setIsSealDelivery(itemDTO.getNeedSealDelivery());
                    }
                }
            } catch(Exception e) {
                log.error("设置封签标识出错", e);
            }


            //高价值标签 封签交付>高价值，只展示其中1个
            pickTask.setIsHighWacGoods(skuHighPriceTagMap.getOrDefault(itemDTO.getSkuId(), false));

            //短保标签
            fillExpirationInfo(pickTask, depotGoodsDetailDtoMap, shippingOrderDTO.getWarehouseId());

            //填充推荐库位
            fillNewLocationBatchInfo(itemDTO, pickTask, recommendLocationBatchMap);

            return pickTask;
        }).collect(Collectors.toList());
    }


    /**
     * 填充保质期等信息, 如果出现异常不阻塞流程
     * 设置是否是短保商品，根据门店灰度取不同的效期字段
     *
     * @param taskVo                     骑手拣货任务信息
     * @param depotGoodsDetailDtoMap     Map<skuId:仓货品信息>
     * @param warehouseId                仓ID
     */
    public static void fillExpirationInfo(RiderPickTaskInfoVO taskVo, Map<String, DepotGoodsDetailDto> depotGoodsDetailDtoMap, Long warehouseId) {
        try {
            String skuId = taskVo.getSkuId();
            DepotGoodsDetailDto depotGoodsDetailDto = depotGoodsDetailDtoMap.get(skuId);
            if (depotGoodsDetailDto == null) {
                return;
            }
            GoodsExpirationDto expirationInfo = depotGoodsDetailDto.getExpirationInfo();
            if (expirationInfo == null) {
                taskVo.setIsShortExpirationGoods(false);
                return;
            }

            DepotGoodsExpireUnitEnum unit = DepotGoodsExpireUnitEnum.findByCode(expirationInfo.getExpireUnit());
            // 设置是否是短保品。根据门店灰度，取不同的字段：
            // （1）灰度门店内：取 (expireForUnit + expireUnit) 两个字段，统一换算成天进行处理
            if (MccConfigUtil.goodsQuerySwitch2NewFields(warehouseId) && expirationInfo.getExpireForUnit() != null && unit != null) {
                taskVo.setIsShortExpirationGoods(MccConfigUtil.getShortExpirationThreshold() > (expirationInfo.getExpireForUnit() * unit.getRatio()));
            }
            else if (expirationInfo.getExpire() != null) {
                // （2）灰度门店外（或者是当新字段为空时使用老字段兜底）：保持老逻辑不变，取 expire 字段
                // 全量一段时间后，可删除这个 else if 和上面的 MccConfigUtil.goodsQuerySwitch2NewFields(warehouseId) 判断条件
                taskVo.setIsShortExpirationGoods(MccConfigUtil.getShortExpirationThreshold() > expirationInfo.getExpire());
            }
            else {
                // isShortExpirationGoods：默认值兜底
                taskVo.setIsShortExpirationGoods(false);
            }

        } catch (Exception e) {
            log.error("fillExpirationInfo error", e);
        }

    }

    public static List<TConsumableMaterialInfo> parseConsumableMaterialInfo(OCMSOrderVO ocmsOrderVO) {
        try {
            log.info("开始解析耗材信息, storeID:{}, viewOrderId: {}", ocmsOrderVO.getShopId(), ocmsOrderVO.getViewOrderId());
            List<TConsumableMaterialInfo> consumableMaterialInfos = ConsumableMaterialParseUtils.parseConsumableInfoFromBizOrderItemModel(ocmsOrderVO.getOcmsOrderItemVOList());
            log.info("结束解析耗材信息, consumableMaterialInfos:{}", consumableMaterialInfos);
            return consumableMaterialInfos;
        } catch (Exception e) {
            Cat.logEvent("CONSUME_CHECK", "PARSE_CONSUMABLE_MATERIAL_INFO_FAIL");
            log.error("解析耗材数量失败, viewOrderId: {}", ocmsOrderVO.getViewOrderId(), e);
            return Collections.emptyList();
        }

    }

    public static List<TConsumableMaterialInfo> parseConsumableMaterialInfo(TradeOrderVO tradeOrderVO) {
        try {
            log.info("开始解析耗材信息, storeID:{}, viewOrderId: {}", tradeOrderVO.getStoreId(), tradeOrderVO.getChannelOrderId());
            List<TConsumableMaterialInfo> consumableMaterialInfos = ConsumableMaterialParseUtils.parseConsumableInfoFromProductVOItemModel(tradeOrderVO.getProductList());
            log.info("结束解析耗材信息, consumableMaterialInfos:{}", consumableMaterialInfos);
            return consumableMaterialInfos;
        } catch (Exception e) {
            Cat.logEvent("CONSUME_CHECK", "PARSE_CONSUMABLE_MATERIAL_INFO_FAIL");
            log.error("解析耗材数量失败, viewOrderId: {}", tradeOrderVO.getChannelOrderId(), e);
            return Collections.emptyList();
        }

    }

    public static void fillNewLocationBatchInfo(TradeShippingOrderItemDTO tradeShippingOrderItemDTO, RiderPickTaskInfoVO taskVo,
                                          Map<RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> skuRecommendLocationListMap) {
        try {
            //填充库位信息、批次信息和所需温区库存是否充足
            RecommendLocationBatch.SkuInfo skuInfo = new RecommendLocationBatch.SkuInfo(tradeShippingOrderItemDTO.getSkuId(),
                    convert2TemperatureAttributeType(tradeShippingOrderItemDTO.getTemperatureZoneCode()));
            List<RecommendLocationBatch> recommendLocationBatches = skuRecommendLocationListMap.getOrDefault(skuInfo, Lists.newArrayList());

            List<RecommendLocationBatch.BatchInfo> batchInfoList = recommendLocationBatches
                    .stream()
                    .map(RecommendLocationBatch::getBatch)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            //无批次展示【无生产日期】
            if (LionConfigUtils.isSwitchBatchStockGrayStore(LoginContextUtils.getAppLoginStoreId())) {
                batchInfoList.forEach(batchInfo -> {
                    if (StringUtils.isBlank(batchInfo.getProductionDate())) {
                        batchInfo.setProductionDate("无生产日期");
                    }
                });
            }

            List<RecommendLocationBatch.LocationInfo> locationInfos = recommendLocationBatches
                    .stream()
                    .map(RecommendLocationBatch::getLocation)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            //所需温区为指定温区且查到的温区和所需温区不同 判定为所需温区库存不足
            boolean requiredTemperatureAreaStockIsNotEnough = false;

            boolean requiredSpecificTemperatureArea = !Objects.equals(skuInfo.getTemperatureType(), TemperatureType.UNDEFINED.getCode());

            if (requiredSpecificTemperatureArea) {
                requiredTemperatureAreaStockIsNotEnough = recommendLocationBatches.stream()
                        .anyMatch(RiderPickingConvert::newRecommendedNotMatchRequiredTemperature);
            }

            //todo 转一下VO
            taskVo.setBatchInfos(batchInfoList);
            taskVo.setLocationInfos(locationInfos);
            taskVo.setRequiredTemperatureAreaStockIsEnough(!requiredTemperatureAreaStockIsNotEnough);
        } catch (Exception e) {
            taskVo.setBatchInfos(Collections.emptyList());
            taskVo.setLocationInfos(Collections.emptyList());
            taskVo.setRequiredTemperatureAreaStockIsEnough(true);
            Cat.logEvent("FILL_LOCATION_BATCH_INFO", "FAIL");
            log.error("fillLocationBatchInfo error, tradeShippingOrderItemDTO:{}, skuRecommendLocationBatchMap:{}",tradeShippingOrderItemDTO, skuRecommendLocationListMap, e);
        }

    }



    public static Integer convert2TemperatureAttributeType(String attributeStr) {
        if (StringUtils.isBlank(attributeStr)) {
            return TemperatureType.UNDEFINED.getCode();
        }

        switch (temperaturePropertyEnumOf(attributeStr)) {
            case NORMAL_TEMPERATURE:
                return TemperatureType.NORMAL.getCode();
            case ICE_TEMPERATURE:
                return TemperatureType.FREEZING.getCode();
            default:
                return TemperatureType.UNDEFINED.getCode();
        }
    }

    public static String getDeliveryOrderTypeName(Integer deliveryOrderType) {
        DeliveryOrderType deliveryOrderTypeEnum = DeliveryOrderType.findByValue(deliveryOrderType);
        if (Objects.isNull(deliveryOrderTypeEnum)) {
            return "未知";
        }
        switch (deliveryOrderTypeEnum) {
            case DELIVERY_RIGHT_NOW:
                return "立即送达";
            case DELIVERY_BY_BOOK_TIME:
                return "预订";
            default:
                return "未知";
        }
    }

    public static boolean newRecommendedNotMatchRequiredTemperature(RecommendLocationBatch recommendLocationBatch) {
        Integer requiredTemperatureArea = Optional.ofNullable(recommendLocationBatch)
                .map(RecommendLocationBatch::getSku)
                .map(RecommendLocationBatch.SkuInfo::getTemperatureType)
                .orElse(null);

        Integer recommendTemperatureArea = Optional.ofNullable(recommendLocationBatch)
                .map(RecommendLocationBatch::getLocation)
                .map(RecommendLocationBatch.LocationInfo::getTemperatureType)
                .orElse(null);

        if (recommendTemperatureArea == null || requiredTemperatureArea == null) {
            return false;
        }

        return !Objects.equals(requiredTemperatureArea, recommendTemperatureArea);
    }

    public static Set<String> buildUpcAndBoxCodeSet(TradeShippingOrderItemDTO taskDto) {
        Set<String> upcAndBoxCodeList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(taskDto.getBarCodes())) {
            upcAndBoxCodeList.addAll(taskDto.getBarCodes());
        }

        if (CollectionUtils.isNotEmpty(taskDto.getBoxCodes())) {
            upcAndBoxCodeList.addAll(taskDto.getBoxCodes());
        }

        return upcAndBoxCodeList;
    }


    public static List<RiderPickingOrderVO> buildRiderPickingOrderVOList(Map<TradeOrderInfoComponent.TradeOrderKey, PickingOrderVO> pickingOrderVOMap,
                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryOrderVO> deliveryOrderVOMap,
                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap,
                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryExceptionSummaryVO> exceptionSummaryVOMap,
                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, RevenueDetailVo> revenueDetailVoMap,
                                                                         Map<Long, List<DeliveryQuestionnaireDTO>> questionnaireMap,
                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, TDeliveryRiskControlOrder> deliveryRiskControlOrderMap,
                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> couldTurnDapDeliveryMap,
                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> lackStockMap,
                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> highPriceTagMap,
                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> sealDeliveryTag,
                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, OCMSOrderVO> ocmsOrderMap) {

        return pickingOrderVOMap.entrySet().stream()
                .filter(entry -> deliveryOrderVOMap.containsKey(entry.getKey()))
                .map(entry -> {
                    TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = entry.getKey();
                    PickingOrderVO pickingOrderVO = entry.getValue();
                    DeliveryOrderVO deliveryOrderVO = deliveryOrderVOMap.get(tradeOrderKey);
                    return buildRiderPickingOrderVO(pickingOrderVO, deliveryOrderVO, tradeOrderVOMap.get(tradeOrderKey),
                            exceptionSummaryVOMap.get(tradeOrderKey), revenueDetailVoMap.get(tradeOrderKey),
                            questionnaireMap.get(deliveryOrderVO.getDeliveryOrderId()),
                            deliveryRiskControlOrderMap.get(tradeOrderKey),
                            couldTurnDapDeliveryMap.getOrDefault(tradeOrderKey, false),
                            lackStockMap.getOrDefault(tradeOrderKey, false),
                            highPriceTagMap.getOrDefault(tradeOrderKey, false),
                            sealDeliveryTag.getOrDefault(tradeOrderKey, false),
                            ocmsOrderMap.get(tradeOrderKey),new ArrayList<>());
                }).collect(Collectors.toList());
    }

    private static RiderPickingOrderVO buildRiderPickingOrderVO(PickingOrderVO pickingOrderVO,
                                                                DeliveryOrderVO deliveryOrderVO,
                                                                TradeOrderVO tradeOrderVO,
                                                                DeliveryExceptionSummaryVO deliveryExceptionSummaryVO,
                                                                RevenueDetailVo revenueDetailVo,
                                                                List<DeliveryQuestionnaireDTO> questionnaires,
                                                                TDeliveryRiskControlOrder tDeliveryRiskControlOrder,
                                                                Boolean couldTurnDapDelivery,
                                                                Boolean isLackStock,
                                                                Boolean isHighPriceOrder,
                                                                Boolean isSealDelivery,
                                                                OCMSOrderVO ocmsOrderVO,
                                                                List<Integer> operatorItemList) {
        RiderDeliveryOrderVO riderDeliveryOrderVO = RiderDeliveryConvert.buildRiderDeliveryOrderVO(pickingOrderVO, deliveryOrderVO, tradeOrderVO, deliveryExceptionSummaryVO,
                revenueDetailVo, questionnaires, tDeliveryRiskControlOrder, couldTurnDapDelivery, isLackStock,
                isHighPriceOrder, isSealDelivery, ocmsOrderVO,  null,operatorItemList);

        //修改拣货单不同的地方
        long evaluateArriveDeadline = deliveryOrderVO.getCreateTime() + LionConfigUtils.getPickTimoutDurationMills();
        riderDeliveryOrderVO.setEvaluateArriveDeadline(evaluateArriveDeadline);

        if (Objects.equals(TradeShippingOrderStatus.FINISH.getCode(), pickingOrderVO.getStatus())) {
            riderDeliveryOrderVO.setEvaluateArriveLeftTime(0L);
            if (pickingOrderVO.getShipTime() != null
                    && TimeUtils.toMilliSeconds(pickingOrderVO.getShipTime()) > evaluateArriveDeadline) {
                // 已拣货完成，已超时
                riderDeliveryOrderVO.setEvaluateArriveTimeout(TimeUtils.toMilliSeconds(pickingOrderVO.getShipTime()) - evaluateArriveDeadline);
            } else {
                // 已拣货完成，未超时
                riderDeliveryOrderVO.setEvaluateArriveTimeout(0L);
            }
        } else if (Objects.equals(TradeShippingOrderStatus.WAITED.getCode(), pickingOrderVO.getStatus())
                || Objects.equals(TradeShippingOrderStatus.RECEIVE.getCode(), pickingOrderVO.getStatus())) {
            long tsNow = System.currentTimeMillis();
            if (tsNow > evaluateArriveDeadline) {
                // 配送中，已经超时
                riderDeliveryOrderVO.setEvaluateArriveLeftTime(0L);
                riderDeliveryOrderVO.setEvaluateArriveTimeout(tsNow - evaluateArriveDeadline);
            } else {
                // 配送中，还未超时
                riderDeliveryOrderVO.setEvaluateArriveLeftTime(evaluateArriveDeadline - tsNow);
                riderDeliveryOrderVO.setEvaluateArriveTimeout(0L);
            }
        } else {
            // 其他状态暂时无需处理
        }

        RiderPickingOrderVO riderPickingOrderVO = new RiderPickingOrderVO();
        BeanUtils.copyProperties(riderDeliveryOrderVO, riderPickingOrderVO);
        return riderPickingOrderVO;
    }

    public static List<ThirdDeliveryPickingOrderVO> buildThirdDeliveryPickingOrderVOList(List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeys,
                                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap,
                                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, RevenueDetailVo> revenueDetailVoMap,
                                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> lackStockTagMap,
                                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, TDeliveryDetail> tDeliveryDetailMap,
                                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, TradeShippingOrderDTO> tradeShippingOrderDTOMap,
                                                                                         Map<TradeOrderInfoComponent.TradeOrderKey, OCMSOrderVO> ocmsOrderMap) {

        //buildVO
        return tradeOrderKeys.stream()
                .map(tradeOrderKey -> buildThirdDeliveryPickingOrderResponseVO(tradeOrderVOMap.get(tradeOrderKey), revenueDetailVoMap.get(tradeOrderKey),
                        lackStockTagMap.get(tradeOrderKey), tDeliveryDetailMap.get(tradeOrderKey), tradeShippingOrderDTOMap.get(tradeOrderKey), ocmsOrderMap.get(tradeOrderKey)))
                .sorted((o1, o2) -> {
                    try {
                        return (int) (o1.getEvaluateArriveDeadline() - o2.getEvaluateArriveDeadline());
                    } catch (Exception e) {
                        return (int) (o1.getEstimateArriveTimeStart() - o2.getEstimateArriveTimeStart());
                    }
                })
                .collect(Collectors.toList());
    }

    private static ThirdDeliveryPickingOrderVO buildThirdDeliveryPickingOrderResponseVO(TradeOrderVO tradeOrderVO, RevenueDetailVo revenueDetailVo,
                                                                                        Boolean isLackStock, TDeliveryDetail tDeliveryDetail,
                                                                                        TradeShippingOrderDTO tradeShippingOrderDTO,
                                                                                        OCMSOrderVO ocmsOrderVO) {
        ThirdDeliveryPickingOrderVO thirdDeliveryPickingOrderVO = new ThirdDeliveryPickingOrderVO();

        if (Objects.nonNull(tradeOrderVO)) {
            thirdDeliveryPickingOrderVO.setTenantId(tradeOrderVO.getTenantId());
            thirdDeliveryPickingOrderVO.setChannelId(tradeOrderVO.getChannelId());
            thirdDeliveryPickingOrderVO.setChannelName(tradeOrderVO.getChannelName());
            thirdDeliveryPickingOrderVO.setStoreId(tradeOrderVO.getStoreId());
            thirdDeliveryPickingOrderVO.setStoreName(tradeOrderVO.getStoreName());
            thirdDeliveryPickingOrderVO.setChannelOrderId(tradeOrderVO.getChannelOrderId());
            thirdDeliveryPickingOrderVO.setUserId(tradeOrderVO.getUserId());
            thirdDeliveryPickingOrderVO.setSerialNo(tradeOrderVO.getSerialNo());
            thirdDeliveryPickingOrderVO.setItemCount(tradeOrderVO.getItemCount());
            thirdDeliveryPickingOrderVO.setReceiverName(tradeOrderVO.getReceiverName());
            thirdDeliveryPickingOrderVO.setReceiverPhone(tradeOrderVO.getReceiverPhone());
            thirdDeliveryPickingOrderVO.setReceiverAddress(tradeOrderVO.getReceiverAddress());

            //商品信息
            thirdDeliveryPickingOrderVO.setProductList(tradeOrderVO.getProductList());
            thirdDeliveryPickingOrderVO.setTotalOfflinePrice(tradeOrderVO.getTotalOfflinePrice());
            thirdDeliveryPickingOrderVO.setGiftVOList(tradeOrderVO.getGiftVOList());
            thirdDeliveryPickingOrderVO.setGiftCount(tradeOrderVO.getGiftCount());
            //礼袋信息
            thirdDeliveryPickingOrderVO.setGiftBagList(tradeOrderVO.getGiftBagList());

            thirdDeliveryPickingOrderVO.setCreateTime(tradeOrderVO.getCreateTime());
            thirdDeliveryPickingOrderVO.setPayTime(tradeOrderVO.getPayTime());
            thirdDeliveryPickingOrderVO.setDeliveryOrderType(tradeOrderVO.getDeliveryOrderType());
            thirdDeliveryPickingOrderVO.setDeliveryOrderTypeName(tradeOrderVO.getDeliveryOrderTypeName());
            thirdDeliveryPickingOrderVO.setEstimateArriveTimeStart(tradeOrderVO.getEstimateArriveTimeStart());
            thirdDeliveryPickingOrderVO.setEstimateArriveTimeEnd(tradeOrderVO.getEstimateArriveTimeEnd());
            thirdDeliveryPickingOrderVO.setComments(tradeOrderVO.getComments());

            // 展示用户标签
            thirdDeliveryPickingOrderVO.setUserTags(tradeOrderVO.getUserTags());
            thirdDeliveryPickingOrderVO.setOrderUserType(tradeOrderVO.getOrderUserType());

            //用户尾号
            thirdDeliveryPickingOrderVO.setReceiverTailPhoneNumber(tradeOrderVO.getReceiverTailPhoneNumber());

            //展示标签汇集
            List<ShowTagVO> showTags = new ArrayList<>();
            if (Objects.nonNull(tradeOrderVO.getIsWiderShippingArea()) && tradeOrderVO.getIsWiderShippingArea()) {
                ShowTagVO widerTag = new ShowTagVO();
                widerTag.setTagDesc(LionConfigUtils.getTagHint(ShowTagEnum.WIDER_SHIPPING_AREA.getLionKey(), ShowTagEnum.WIDER_SHIPPING_AREA.getDefaultValue()));
                widerTag.setTagCode(ShowTagEnum.WIDER_SHIPPING_AREA.getCode());
                showTags.add(widerTag);
            }
            thirdDeliveryPickingOrderVO.setShowTags(showTags);
            // 名酒馆标签
            thirdDeliveryPickingOrderVO.setIsMtFamousTavern(tradeOrderVO.getIsMtFamousTavern());
        }

        //营收模块
        if (Objects.nonNull(revenueDetailVo)) {
            thirdDeliveryPickingOrderVO.setRevenueDetail(revenueDetailVo);
        }

        //是否缺货
        thirdDeliveryPickingOrderVO.setHasLackGoods(isLackStock);

        //填充剩余时间
        fillRemainingTime(tradeOrderVO, tDeliveryDetail, thirdDeliveryPickingOrderVO);

        //货品项信息
        if (Objects.nonNull(tradeShippingOrderDTO) && LionConfigUtils.isShowGoodsItemListGrayStore(tradeShippingOrderDTO.getWarehouseId())) {
            thirdDeliveryPickingOrderVO.setGoodsItemList(transfer2GoodsItemVO(tradeShippingOrderDTO.getItems()));
        }

        //耗材信息
        if (Objects.nonNull(ocmsOrderVO) && LionConfigUtils.isShowGoodsItemListGrayStore(ocmsOrderVO.getShopId())) {
            thirdDeliveryPickingOrderVO.setNeedWineBottleOpener(
                    parseConsumableMaterialInfo(ocmsOrderVO)
                            .stream()
                            .anyMatch(consumable ->  {
                                return LionConfigUtils.getWineBottleOpenerSkuIds().contains(consumable.getSkuId()) && Objects.nonNull(consumable.getCount()) && consumable.getCount() > 0;
                            }));
        }

        return thirdDeliveryPickingOrderVO;
    }

    private static void fillRemainingTime(TradeOrderVO tradeOrderVO, TDeliveryDetail tDeliveryDetail, ThirdDeliveryPickingOrderVO thirdDeliveryPickingOrderVO) {
        if (Objects.nonNull(tradeOrderVO) && Objects.nonNull(tDeliveryDetail)) {
            Long arrivalEndTime = tradeOrderVO.getEstimateArriveTimeEnd();
            // 配送超时考核信息 start
            long evaluateArriveDeadline;
            if (Objects.nonNull(tDeliveryDetail.assessDeliveryTime)) {
                //新逻辑，考核时间与eta脱钩
                evaluateArriveDeadline = tDeliveryDetail.assessDeliveryTime;
            } else {
                //原逻辑
                if (Objects.equals(tradeOrderVO.getDeliveryOrderType(), com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryOrderType.DELIVERY_BY_BOOK_TIME.getValue())) {
                    // 预订单：考核时间=预计送达时间+5分钟
                    if (LionConfigUtils.isNewPreOrderAssessGrayStore(thirdDeliveryPickingOrderVO.getStoreId())) {
                        evaluateArriveDeadline = arrivalEndTime + LionConfigUtils.preOrderAssessTimePlusMills();
                    } else {
                        evaluateArriveDeadline = arrivalEndTime + (5 * 60 * 1000);
                    }
                } else {
                    // 实时单：考核时间=支付时间+25分钟
                    evaluateArriveDeadline = tradeOrderVO.getPayTime() + (25 * 60 * 1000);
                }
            }

            thirdDeliveryPickingOrderVO.setEvaluateArriveDeadline(evaluateArriveDeadline);
            if (RiderDeliveryStatusEnum.DELIVERY_DONE.getCode() == tDeliveryDetail.status) {
                thirdDeliveryPickingOrderVO.setEvaluateArriveLeftTime(0L);
                Long deliveryDoneTime = tDeliveryDetail.deliveryStatusChangeTime;
                if (deliveryDoneTime != null && deliveryDoneTime > evaluateArriveDeadline) {
                    // 已送达，已超时
                    thirdDeliveryPickingOrderVO.setEvaluateArriveTimeout(deliveryDoneTime - evaluateArriveDeadline);
                } else {
                    // 已送达，未超时
                    thirdDeliveryPickingOrderVO.setEvaluateArriveTimeout(0L);
                }
            } else {
                long tsNow = System.currentTimeMillis();
                if (tsNow > evaluateArriveDeadline) {
                    // 配送中，已经超时
                    thirdDeliveryPickingOrderVO.setEvaluateArriveLeftTime(0L);
                    thirdDeliveryPickingOrderVO.setEvaluateArriveTimeout(tsNow - evaluateArriveDeadline);
                } else {
                    // 配送中，还未超时
                    thirdDeliveryPickingOrderVO.setEvaluateArriveLeftTime(evaluateArriveDeadline - tsNow);
                    thirdDeliveryPickingOrderVO.setEvaluateArriveTimeout(0L);
                }
            }
        }
    }
}
