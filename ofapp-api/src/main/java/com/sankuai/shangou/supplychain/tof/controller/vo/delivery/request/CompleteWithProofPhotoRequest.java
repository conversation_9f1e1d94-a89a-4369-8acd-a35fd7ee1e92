package com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/4 20:57
 **/
@Data
public class CompleteWithProofPhotoRequest {

    /**
     * 运单id
     */
    private Long deliveryOrderId;

    /**
     * 签收方式 1-当面签收 2-放在指定地点并拍照
     */
    private Integer signType;

    /**
     * 送达照片
     */
    private List<String> picList;

    /**
     * 是否是弱网环境
     */
    private Boolean isWeakNetwork;

    /**
     * 渠道订单id
     */
    private String channelOrderId;

    /**
     * 渠道id
     */
    private Integer channelId;

    private LocationInfo locationInfo;



    public String validate() {
        if (deliveryOrderId == null) {
            return "运单id不合法";
        }
//
//        if (signType == null) {
//            return "签收方式不合法";
//        }

        return null;
    }

}
