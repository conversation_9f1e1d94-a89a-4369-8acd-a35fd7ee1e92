package com.sankuai.shangou.supplychain.tof.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.RiderLocationSyncRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.RiderArrivalLocationRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.RiderOperateRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.TakeAwayRequest;
import com.sankuai.shangou.supplychain.tof.service.RiderDeliveryOperateService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import com.dianping.cat.Cat;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.CompleteResultVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.CompleteWithProofPhotoRequest;
import com.sankuai.shangou.supplychain.tof.enums.CatEventEnum;
import com.sankuai.shangou.supplychain.tof.service.RiderDeliveryOperateService;
import com.sankuai.shangou.supplychain.tof.service.VerifyTaskService;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/7/1 16:52
 **/
@Slf4j
@InterfaceDoc(
        type = "restful",
        displayName = "歪马自营配送操作相关接口",
        description = "歪马自营配送的操作功能",
        scenarios = "主要应用于歪马自配场景"
)
@RestController
@RequestMapping("/api/orderfulfill/app/rider/delivery")
public class RiderDeliveryOperateController {

    @Resource
    private RiderDeliveryOperateService riderDeliveryOperateService;

    @Resource
    private VerifyTaskService verifyTaskService;


    @MethodDoc(
            displayName = "骑手接单",
            description = "骑手接单",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手接单",
                            type = RiderArrivalLocationRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/accept",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/accept", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public void accept(@Valid @RequestBody RiderOperateRequest request) {
        riderDeliveryOperateService.accept(request);
    }


    @MethodDoc(
            displayName = "上报骑手送达时刻经纬度",
            description = "上报骑手送达时刻经纬度",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "上报骑手送达时刻经纬度请求参数",
                            type = RiderArrivalLocationRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/riderArrivalLocation",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/riderArrivalLocation", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public void riderArrivalLocation(@Valid @RequestBody RiderArrivalLocationRequest request) {
        riderDeliveryOperateService.riderArrivalLocation(request);
    }

    @MethodDoc(
            displayName = "确认送达并且上传送达照片",
            description = "确认送达并且上传送达照片",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "确认送达并且上传送达照片",
                            type = CompleteWithProofPhotoRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/completeWithProofPhoto",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/completeWithProofPhoto", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CompleteResultVO completeWithProofPhoto(@Valid @RequestBody CompleteWithProofPhotoRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        //1.扭转运单状态
        riderDeliveryOperateService.completeWithProofPhoto(request);

        //2.尝试下发验证任务
        try {
            if (!MccConfigUtil.getAssignVerifyTaskSwitch()) {
                return null;
            }

            Optional<Long> verifyTaskIdOpt = verifyTaskService.tryToAssignVerifyTask(request.getDeliveryOrderId(),
                    request.getChannelOrderId(), request.getChannelId());
            if (verifyTaskIdOpt.isPresent()) {
                return new CompleteResultVO(verifyTaskIdOpt.get());
            }
        } catch (Exception e) {
            //失败不阻塞送达流程
            log.error("尝试下发验证任务失败", e);
            Cat.logEvent(CatEventEnum.ASSIGN_VERIFY_TASK_ERROR.getType(), CatEventEnum.ASSIGN_VERIFY_TASK_ERROR.getName());
        }

        return null;
    }


    @MethodDoc(
            displayName = "确认取货",
            description = "确认取货",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "确认取货",
                            type = CompleteWithProofPhotoRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/takeAway",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/takeAway", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public void takeAway(@RequestBody TakeAwayRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        riderDeliveryOperateService.takeAway(request.getDeliveryOrderId(), request.getLocationInfo(), request.getChannelId(), request.getChannelOrderId());
    }


    @MethodDoc(
            displayName = "骑手坐标同步",
            description = "骑手坐标同步",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手坐标同步请求参数",
                            type = RiderLocationSyncRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/location",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @ApiOperation(value = "骑手坐标同步")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/location", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public void location(@Valid @RequestBody RiderLocationSyncRequest request) {
        riderDeliveryOperateService.location(request);
    }


    @MethodDoc(
            displayName = "骑手锁定运单状态（暂停配送）",
            description = "骑手锁定运单状态（暂停配送）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手锁定运单状态（暂停配送）请求参数",
                            type = RiderOperateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/lockDeliveryStatus",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation(value = "骑手锁定运单状态（暂停配送）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/lockDeliveryStatus", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public void lockDeliveryStatus(@Valid @RequestBody RiderOperateRequest request) {
        riderDeliveryOperateService.changeDeliveryStatusLock(request, 1);
    }

    @MethodDoc(
            displayName = "骑手解锁运单状态（继续配送）",
            description = "骑手解锁运单状态（继续配送）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手解锁运单状态（继续配送）请求参数",
                            type = RiderOperateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/unlockDeliveryStatus",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation(value = "骑手解锁运单状态（继续配送）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/unlockDeliveryStatus", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public void unlockDeliveryStatus(@Valid @RequestBody RiderOperateRequest request) {
        riderDeliveryOperateService.changeDeliveryStatusLock(request, 0);
    }

}
