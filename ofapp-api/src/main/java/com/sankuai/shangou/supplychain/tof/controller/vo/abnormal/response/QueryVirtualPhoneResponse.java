package com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.response;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/14
 * desc: 查询虚拟电话响应
 */
@TypeDoc(
        description = "查询虚拟号响应"
)
@Data
public class QueryVirtualPhoneResponse {

    @FieldDoc(
            description = "下单人号码-隐私号", requiredness = Requiredness.REQUIRED
    )
    private String phoneNo;

    @FieldDoc(
            description = "下单人号码-备用隐私号码", requiredness = Requiredness.REQUIRED
    )
    private List<String> backUpPhoneNo = new ArrayList<>();


    @FieldDoc(
            description = "收货人号码-隐私号", requiredness = Requiredness.OPTIONAL
    )
    private String bindPhoneNo;

    @FieldDoc(
            description = "收货人号码-备用隐私号码", requiredness = Requiredness.OPTIONAL
    )
    private List<String> bindBackupPhoneNo = new ArrayList<>();
}