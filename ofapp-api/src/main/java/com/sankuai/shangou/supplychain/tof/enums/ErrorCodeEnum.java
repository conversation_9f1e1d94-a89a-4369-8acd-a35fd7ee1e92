package com.sankuai.shangou.supplychain.tof.enums;

import lombok.Getter;

public enum ErrorCodeEnum {

    ORDER_CANT_BE_CONTACT(20000001, "已超过联系期限，如有需要请联系总部"),

    RIDER_PICK_FINISH_PICK_FAIL(8000, "骑手操作拣货完成失败"),


    NEED_TAKE_PHOTO_BEFORE_FINISH_PICKING(8004,"按照门店作业规范，请先对出库商品进行拍照留存"),

    RIDER_PICK_ALREADY_COMPLETED(8005, "当前拣货任务已经通过美团外卖商家端或百川其他入口完成，请确认商品无误后，点击下方按钮继续进行配送"),

    RIDER_PICK_ALREADY_CANCELED(8006, "拣货任务已取消，不支持进行扫码拣货，请退出页面刷新或联系管理员"),

    EXCEED_SHOULD_PICK_COUNT(30006,"该订单已部分退款，请按最新商品数量出库"),

    SN_CODE_NOT_VALID(30010, "sn码无效"),

    EMPLOYEE_NOT_EXIST(20000080, "员工不存在"),

    STOCK_OUT_STOCK_LACK(20000099, "商品库存数量不足,请处理异常任务后再出库"),

    CONSUMABLE_STOCK_LACK(20000089, "耗材库存量不足"),

    FORTUNE_WINE_STOCK_NOT_ENOUGH(20000090, "当前发财酒可用库存=%s，请调整出库数量"),


    ORDER_IS_IN_DELIVERY(20000091, "当前容具编码所属订单仍在配送中，请确认容具是否使用完成，再进行归还"),

    SEAL_CONTAINER_NOT_NEED_RETURN(20000092, "当前编码不是该门店待归还容具编码，请核实后再扫码或手动添加"),

    SEAL_CONTAINER_ALREADY_RETURN(20000093, "容具已归还，请勿重复操作"),

    DELIVERY_ORDER_STATUS_ALREADY_CHANGED(20000100, "运单状态已改变,请刷新页面"),

    RIDER_ALREADY_CHANGED(20000101, "运单已被转单,请刷新页面"),

    LIMIT_ACCEPT_ORDER(20000102, "骑手已被限制接单,原因: %s"),

    PICK_TASK_ALREADY_BE_ACCEPTED(20000103, "拣货任务已被领取，领配送任务成功"),

    DELIVERY_STATUS_IS_LOCKED(20001111, "运单状态被锁定"),

    ALREADY_OPERATE_BY_OTHER(20011040, "被其他配送员操作，请刷新数据"),

    STATUS_ERROR(20011041, "当前状态{0},无法操作"),

    NOT_FOUND_DELIVERY_RECORD(20011042, "找不到配送记录"),


    PRIVACY_NUMBER_AUTH_INFO_NOT_AUTHORIZED(20000107, "实名信息未认证")







    ;
    ;
    @Getter
    private final int code;

    @Getter
    private final String message;

    ErrorCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
