package com.sankuai.shangou.supplychain.tof.wrapper;

import com.alibaba.fastjson.JSON;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.ArrayListMultimap;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.QueryDeliveryInfoThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryByStatusRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryOperateItemRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.PageQueryDeliveryInfoResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryDeliveryOperateItemResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryDeliveryOrderResponse;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-12-12
 * @email <EMAIL>
 */
@Rhino
@Service
@Slf4j
public class DeliveryServiceWrapper {

    @Resource
    private QueryDeliveryInfoThriftService queryDeliveryRpcService;

    @Degrade(rhinoKey = "TmsServiceWrapper-queryActiveDeliveryInfo",
            fallBackMethod = "queryActiveDeliveryInfoFallback",
            timeoutInMilliseconds = 2000)
    public List<TDeliveryOrder> queryActiveDeliveryInfoByOrderIds(List<Long> orderList) {

        log.info("TmsServiceWrapper-queryDeliveryInfoByOrderIds, req={}", orderList);
        QueryDeliveryOrderResponse response = queryDeliveryRpcService.queryActiveDeliveryOrderByOrderIdList(orderList);
        log.info("TmsServiceWrapper-queryDeliveryInfoByOrderIds, response={}", response);
        return response.getTDeliveryOrders();
    }

    @Degrade(rhinoKey = "TmsServiceWrapper-pageQueryThirdDeliveryOrderList",
            fallBackMethod = "pageQueryThirdDeliveryOrderListFallback",
            timeoutInMilliseconds = 2000)
    public List<TDeliveryDetail> pageQueryThirdDeliveryOrderList(Long tenantId, Long storeId,
                                                                 List<Integer> statusList, Boolean filterException, int page, int pageSize) {
        QueryDeliveryByStatusRequest request = new QueryDeliveryByStatusRequest(tenantId, storeId, statusList, filterException, page, pageSize);
        log.info("TmsServiceWrapper-queryDeliveryInfoByOrderIds, req: {}", JSON.toJSONString(request));
        PageQueryDeliveryInfoResponse response = queryDeliveryRpcService.pageQueryThirdDeliveryOrderList(request);
        log.info("TmsServiceWrapper-queryDeliveryInfoByOrderIds, response: {}", JSON.toJSONString(response));

        if (response.getStatus() == null || response.getStatus().getCode() != 0) {
            throw new BizException("查询三方配送运单失败");
        }
        return response.getTDeliveryDetails();

    }

    private List<TDeliveryOrder> queryActiveDeliveryInfoFallback(List<Long> orderIdList, Throwable t) {
        log.error("queryActiveDeliveryInfoFallback {} {}", orderIdList, t.getMessage(), t);
        return Lists.newArrayList();
    }

    public List<TDeliveryDetail> pageQueryThirdDeliveryOrderListFallback(Long tenantId, Long storeId, List<Integer> statusList,
                                                                         Boolean filterException, int page, int pageSize) {
        log.error("pageQueryThirdDeliveryOrderListFallback 发生降级");
        return Lists.newArrayList();
    }

    public Map<Long,List<Integer>> queryDeliveryOperatorItemList(Long tenantId, Long storeId, List<Long> orderIdList){
        if(CollectionUtils.isEmpty(orderIdList)){
            return Collections.emptyMap();
        }

        try {
            QueryDeliveryOperateItemRequest request = new QueryDeliveryOperateItemRequest();
            request.setTenantId(tenantId);
            request.setStoreId(storeId);
            request.setOrderIdList(orderIdList);
            QueryDeliveryOperateItemResponse response = queryDeliveryRpcService.queryDeliveryOperateItem(request);
            if(response == null || MapUtils.isEmpty(response.getOperateItemMap())){
                return Collections.emptyMap();
            }

            Map<Long,List<Integer>> mapList = new HashMap<>();

            response.getOperateItemMap().forEach((k,v)->{
                if(v==null || CollectionUtils.isEmpty(v.getOperateItemList())){
                    return;
                }
                mapList.put(k,v.getOperateItemList());
            });
            return mapList;
        }catch (Exception e){
            log.error("queryDeliveryOperatorItemList",e);
        }
        return Collections.emptyMap();
    }
}
