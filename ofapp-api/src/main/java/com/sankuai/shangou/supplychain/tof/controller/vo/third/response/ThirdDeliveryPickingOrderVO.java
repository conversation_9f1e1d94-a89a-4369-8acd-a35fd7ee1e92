package com.sankuai.shangou.supplychain.tof.controller.vo.third.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.GoodsItemVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.ShowTagVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/15 16:07
 **/

@Data
public class ThirdDeliveryPickingOrderVO {
    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    private String channelName;

    @FieldDoc(
            description = "仓ID", requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "仓名称", requiredness = Requiredness.REQUIRED
    )
    private String storeName;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    private String channelOrderId;

    @FieldDoc(
            description = "订单用户ID、0或-1为无效ID", requiredness = Requiredness.OPTIONAL
    )
    private Long userId;

    @FieldDoc(
            description = "订单流水", requiredness = Requiredness.REQUIRED
    )
    private Long serialNo;


    // 收货人信息 start
    @FieldDoc(
            description = "收货人姓名", requiredness = Requiredness.REQUIRED
    )
    private String receiverName;

    @FieldDoc(
            description = "收货人电话号码", requiredness = Requiredness.REQUIRED
    )
    private String receiverPhone;

    @FieldDoc(
            description = "收货人地址", requiredness = Requiredness.REQUIRED
    )
    private String receiverAddress;
    // 收货人信息 end

    // 商品信息 start
    @FieldDoc(
            description = "商品总数量", requiredness = Requiredness.REQUIRED
    )
    private Integer itemCount;

    @FieldDoc(
            description = "商品信息列表", requiredness = Requiredness.REQUIRED
    )
    private List<ProductVO> productList;

    @FieldDoc(
            description = "赠品信息列表", requiredness = Requiredness.OPTIONAL
    )
    private List<GiftVO> giftVOList;

    @FieldDoc(
            description = "礼袋信息", requiredness = Requiredness.OPTIONAL
    )
    private List<GiftBagVO> giftBagList;

    @FieldDoc(
            description = "赠品总数量", requiredness = Requiredness.OPTIONAL
    )
    private Integer giftCount;
    //商品信息 end

    @FieldDoc(
            description = "备注", requiredness = Requiredness.OPTIONAL
    )
    private String comments;

    @FieldDoc(
            description = "订单创建时间戳", requiredness = Requiredness.REQUIRED
    )
    private Long createTime;

    @FieldDoc(
            description = "配送订单类型  1-立即单 2-预约单", requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryOrderType;

    @FieldDoc(
            description = "配送订单类型名称", requiredness = Requiredness.REQUIRED
    )
    private String deliveryOrderTypeName;

    @FieldDoc(
            description = "预计送达时间开始时间", requiredness = Requiredness.REQUIRED
    )
    private Long estimateArriveTimeStart;

    @FieldDoc(
            description = "预计送达时间截止时间", requiredness = Requiredness.REQUIRED
    )
    private Long estimateArriveTimeEnd;

    @FieldDoc(
            description = "提报价模式,订单线下价格总和", requiredness = Requiredness.OPTIONAL
    )
    private Integer totalOfflinePrice;

    @FieldDoc(
            description = "订单营收数据", requiredness = Requiredness.OPTIONAL
    )
    private RevenueDetailVo revenueDetail;

    @FieldDoc(
            description = "用户标签信息", requiredness = Requiredness.OPTIONAL
    )
    private List<TagInfoVO> userTags;


    @FieldDoc(
            description = "支付时间", requiredness = Requiredness.REQUIRED
    )
    private Long payTime;


    @FieldDoc(
            description = "用户类型信息 10-普通用户, 15-会员用户 "
    )
    /**
     * @see com.meituan.shangou.saas.order.platform.enums.OrderTypeEnum
     */
    public Integer orderUserType;


    @FieldDoc(
            description = "签收点"
    )
    private String signingPoint;

    @FieldDoc(
            description = "是否有缺货情况", requiredness = Requiredness.OPTIONAL
    )
    private Boolean hasLackGoods = false;

    @FieldDoc(
            description = "是否是大范围配送", requiredness = Requiredness.OPTIONAL
    )
    private Boolean isWiderShippingArea = false;

    @FieldDoc(
            description = "收货人后4位尾号", requiredness = Requiredness.OPTIONAL
    )
    private String receiverTailPhoneNumber;

    @FieldDoc(
            description = "考核送达截止时间", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveDeadline;

    @FieldDoc(
            description = "考核送达剩余时间", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveLeftTime;

    @FieldDoc(
            description = "考核送达超时时间", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveTimeout;

    @FieldDoc(
            description = "展示标签，包括 大范围标签等", requiredness = Requiredness.OPTIONAL
    )
    private List<ShowTagVO> showTags;

    @FieldDoc(
            description = "考核配送时间", requiredness = Requiredness.OPTIONAL
    )
    private Long assessDeliveryTime;

    @FieldDoc(
            description = "货品项列表",
            example = {}
    )
    private List<GoodsItemVO> goodsItemList;

    @FieldDoc(
            description = "是否需要红酒开瓶器",
            example = {}
    )
    private Boolean needWineBottleOpener;

    @FieldDoc(
            description = "是否为美团名酒馆订单，true：是"
    )
    private Boolean isMtFamousTavern;
}
