package com.sankuai.shangou.supplychain.tof.controller.vo.third.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/25 15:07
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ThirdDeliveryPickingOrderListResponse {
    @FieldDoc(
            description = "总条数", requiredness = Requiredness.REQUIRED
    )
    private Integer totalCount;

    @FieldDoc(
            description = "是否还有下一页", requiredness = Requiredness.REQUIRED
    )
    private Boolean hasMore;

    @FieldDoc(
            description = "订单列表", requiredness = Requiredness.REQUIRED
    )
    private List<ThirdDeliveryPickingOrderVO> orderList;
}
