package com.sankuai.shangou.supplychain.tof.controller.vo.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/9/12 19:41
 **/
@Data
public class TabCountVO {
    /**
     * 去重后的新任务订单数量
     */
    private Integer riderWaitToGetOrderCount;

    /**
     * 去重后的待取货的订单数量
     */
    private Integer riderWaitToTakeGoodsCount;

    @FieldDoc(
            description = "新任务-仅拣货订单数", requiredness = Requiredness.REQUIRED
    )
    private Integer newJustPickTaskCount;

    @FieldDoc(
            description = "新任务-仅配送订单数", requiredness = Requiredness.REQUIRED
    )
    private Integer newJustDeliveryTaskCount;

    @FieldDoc(
            description = "新任务-拣配任务订单数", requiredness = Requiredness.REQUIRED
    )
    private Integer newPickDeliveryTaskCount;

    @FieldDoc(
            description = "待取货-拣货任务订单数", requiredness = Requiredness.REQUIRED
    )
    private Integer waitTakePickTaskCount;

    @FieldDoc(
            description = "待取货-配送任务订单数", requiredness = Requiredness.REQUIRED
    )
    private Integer waitTakeDeliveryTaskCount;

    @FieldDoc(
            description = "配送中-配送任务订单数", requiredness = Requiredness.REQUIRED
    )
    private Integer inDeliveryDeliveryTaskCount;
}
