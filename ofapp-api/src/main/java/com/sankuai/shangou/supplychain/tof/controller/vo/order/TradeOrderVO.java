package com.sankuai.shangou.supplychain.tof.controller.vo.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.supplychain.tof.controller.vo.SortedOrderTagVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-18
 * @email <EMAIL>
 */
@Data
public class TradeOrderVO {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "订单侧主键id", requiredness = Requiredness.REQUIRED
    )
    private Long orderId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    private Integer orderBizType;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    private String channelName;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    private String storeName;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    private String channelOrderId;

    @FieldDoc(
            description = "订单流水", requiredness = Requiredness.REQUIRED
    )
    private Long serialNo;

    @FieldDoc(
            description = "配送订单类型  1-立即单 2-预约单", requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryOrderType;

    @FieldDoc(
            description = "配送订单类型名称", requiredness = Requiredness.REQUIRED
    )
    private String deliveryOrderTypeName;


    @FieldDoc(
            description = "订单创建时间戳", requiredness = Requiredness.REQUIRED
    )
    private Long createTime;

    @FieldDoc(
            description = "支付时间", requiredness = Requiredness.OPTIONAL
    )
    private Long payTime;


    @FieldDoc(
            description = "配送方式,0-未知，1-配送到家，2-到店自提", requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryMethod;


    @FieldDoc(
            description = "配送方式描述", requiredness = Requiredness.REQUIRED
    )
    private String deliveryMethodDesc;


    @FieldDoc(
            description = "订单用户ID、0或-1为无效ID", requiredness = Requiredness.OPTIONAL
    )
    private Long userId;


    @FieldDoc(
            description = "商品总数量", requiredness = Requiredness.REQUIRED
    )
    private Integer itemCount;

    @FieldDoc(
            description = "备注", requiredness = Requiredness.OPTIONAL
    )
    private String comments;

    @FieldDoc(
            description = "商品信息列表", requiredness = Requiredness.REQUIRED
    )
    private List<ProductVO> productList;

    @FieldDoc(
            description = "提报价模式,订单线下价格总和", requiredness = Requiredness.OPTIONAL
    )
    private Integer totalOfflinePrice;

    @FieldDoc(
            description = "赠品信息列表", requiredness = Requiredness.OPTIONAL
    )
    private List<GiftVO> giftVOList;

    @FieldDoc(
            description = "赠品总数量", requiredness = Requiredness.OPTIONAL
    )
    private Integer giftCount;

    @FieldDoc(
            description = "用户标签信息", requiredness = Requiredness.OPTIONAL
    )
    private List<TagInfoVO> userTags;

    @FieldDoc(
            description = "用户类型信息 10-普通用户, 15-会员用户", requiredness = Requiredness.OPTIONAL
    )
    private Integer orderUserType;


    @FieldDoc(
            description = "预计送达时间开始时间", requiredness = Requiredness.REQUIRED
    )
    private Long estimateArriveTimeStart;

    @FieldDoc(
            description = "预计送达时间截止时间", requiredness = Requiredness.REQUIRED
    )
    private Long estimateArriveTimeEnd;

    @FieldDoc(
            description = "收货人姓名", requiredness = Requiredness.REQUIRED
    )
    private String receiverName;

    @FieldDoc(
            description = "收货人电话号码", requiredness = Requiredness.REQUIRED
    )
    private String receiverPhone;

    @FieldDoc(
            description = "收货人地址", requiredness = Requiredness.REQUIRED
    )
    private String receiverAddress;

    @FieldDoc(
            description = "歪马地推自提订单标记",
            example = {}
    )
    private Boolean selfPickPullNewOrder;

    @FieldDoc(
            description = "场景，如:餐馆", requiredness = Requiredness.OPTIONAL
    )
    private String scene;

    @FieldDoc(
            description = "用户实付 单位：分", requiredness = Requiredness.OPTIONAL
    )
    private Integer actualPayAmt;

    @FieldDoc(
            description = "是否是大范围配送订单", requiredness = Requiredness.OPTIONAL
    )
    private Boolean isWiderShippingArea;

    @FieldDoc(
            description = "收货人后4位尾号", requiredness = Requiredness.OPTIONAL
    )
    private String receiverTailPhoneNumber;

    @FieldDoc(
            description = "礼袋信息", requiredness = Requiredness.OPTIONAL
    )
    private List<GiftBagVO> giftBagList;

    @FieldDoc(
            description = "订单状态", requiredness = Requiredness.OPTIONAL
    )
    private Integer orderStatus;

    @FieldDoc(
            description = "是否为美团名酒馆订单，true：是"
    )
    private Boolean isMtFamousTavern;

    @FieldDoc(
            description = "是否为发财酒订单，true：是"
    )
    private Boolean isFacaiWine;

    @FieldDoc(
            description = "放置位置，【标签】"
    )
    private String deliveryPosition;

    @FieldDoc(
            description = "排序好的订单标签"
    )
    private List<SortedOrderTagVO> sortedTagList;
}
