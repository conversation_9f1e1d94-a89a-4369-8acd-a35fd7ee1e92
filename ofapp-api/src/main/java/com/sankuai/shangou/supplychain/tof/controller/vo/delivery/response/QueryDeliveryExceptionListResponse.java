package com.sankuai.shangou.supplychain.tof.controller.vo.delivery.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryExceptionSummaryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/06/27
 */
@TypeDoc(
        description = "查询配送异常列表响应"
)
@Data
@ApiModel("查询配送异常列表响应")
@NoArgsConstructor
@AllArgsConstructor
public class QueryDeliveryExceptionListResponse {
    @FieldDoc(
            description = "页码"
    )
    @ApiModelProperty("页码")
    private int pageNum;

    @FieldDoc(
            description = "分页大小"
    )
    @ApiModelProperty("分页大小")
    private int pageSize;

    @FieldDoc(
            description = "总条数"
    )
    @ApiModelProperty("总条数")
    private int totalCount;

    @FieldDoc(
            description = "是否还有下一页"
    )
    @ApiModelProperty("是否还有下一页")
    private Boolean hasMore;

    @FieldDoc(
            description = "配送异常列表"
    )
    @ApiModelProperty("配送异常列表")
    private List<DeliveryExceptionSummaryVO> deliveryExceptionSummaryVOList;
}
