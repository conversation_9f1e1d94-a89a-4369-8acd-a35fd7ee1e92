package com.sankuai.shangou.supplychain.tof.enums;

/**
 * <AUTHOR>
 * @since 2024/4/24 20:46
 **/
public enum ComponentTypeEnum {
    DELIVERY_EXCEPTION_INFO(1, "配送异常信息"),
    ORDER_REVENUE_INFO(2, "营收信息"),
    TURN_DELIVERY_BUTTON_INFO(3, "展示转三方按钮"),
    LACK_STOCK(4, "展示缺货信息"),
    HIGH_PRICE_TAG(5, "展示高价值标签"),
    QUESTIONNAIRE(6, "问卷调查"),
    SEAL_DELIVER(7, "封签交付标签"),
    USE_ASSESS_TIME(8, "使用考核时间"),
    CHECK_PICK_AND_DELIVERY_ORDER_STATUS(9, "检查拣货单和运单状态"),
    USE_PICK_TIMEOUT_TIME(10, "使用拣货超时时间"),
    FILTER_THIRD_PICK(11, "过滤三方"),
    SORT_BY_DONE_TIME(12, "按完成时间排序"),
    PICKER_STATISTICS(13, "已拣货统计信息"),
    EXTERNAL_PRODUCT_INFO(14, "额外商品信息(发财酒)")
    ;

    private int code;
    private String desc;

    ComponentTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
