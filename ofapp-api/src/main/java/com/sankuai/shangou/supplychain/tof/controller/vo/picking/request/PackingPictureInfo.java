package com.sankuai.shangou.supplychain.tof.controller.vo.picking.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * @Author: yujing10
 * @Date: 2025/3/18 14:26
 * @Description:
 */
@TypeDoc(
        description = "打包照片信息"
)
@Data
public class PackingPictureInfo {
    @FieldDoc(
            description = "货品照片",
            requiredness = Requiredness.OPTIONAL
    )
    private List<String> goodsPictureUrlList;

    @FieldDoc(
            description = "包装照片",
            requiredness = Requiredness.OPTIONAL
    )
    private List<String> packingPictureUrlList;

    @FieldDoc(
            description = "封签照片",
            requiredness = Requiredness.OPTIONAL
    )
    private List<String> sealPictureUrlList;


    public boolean isEmpty() {
        return CollectionUtils.isEmpty(goodsPictureUrlList)
                && CollectionUtils.isEmpty(packingPictureUrlList)
                && CollectionUtils.isEmpty(sealPictureUrlList);
    }
}
