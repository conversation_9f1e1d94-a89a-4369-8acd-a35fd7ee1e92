package com.sankuai.shangou.supplychain.tof.controller.vo.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 商品信息
 *
 * <AUTHOR>
 * @since 2023/12/18
 *
 */
@TypeDoc(
        description = "商品信息"
)
@Data
public class ProductVO {

    @FieldDoc(
            description = "skuId", requiredness = Requiredness.OPTIONAL
    )
    private String skuId;

    @FieldDoc(
            description = "upc码", requiredness = Requiredness.REQUIRED
    )
    private String upcCode;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    private String skuName;

    @FieldDoc(
            description = "商品图片URL", requiredness = Requiredness.REQUIRED
    )
    private String picUrl;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    private String specification;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    private String sellUnit;

    @FieldDoc(
            description = "原总价  单位:分", requiredness = Requiredness.REQUIRED
    )
    private Integer originalTotalPrice;

    @FieldDoc(
            description = "实付金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    private Integer totalPayAmount;

    @FieldDoc(
            description = "单价  单位:分", requiredness = Requiredness.REQUIRED
    )
    private Integer unitPrice;

    @FieldDoc(
            description = "购买数量", requiredness = Requiredness.REQUIRED
    )
    private Integer count;

    @FieldDoc(
            description = "商品项线下价格=线下价格*数量", requiredness = Requiredness.REQUIRED
    )
    private Integer orderItemOfflinePrice;

    @FieldDoc(
            description = "商品标签信息列表(1履约标签、2拣货标签)、客户端将type用于修改样式、新标签需要使用tagInfos", requiredness = Requiredness.OPTIONAL
    )
    private List<TagInfoVO> tagInfoList;

    @FieldDoc(
            description = "商品标签信息列表(除1履约标签、2拣货标签之外的其它新标签都用此对象)  3商品属性标签", requiredness = Requiredness.OPTIONAL
    )
    private List<TagInfoVO> tagInfos;

    @FieldDoc(
            description = "商品现价", requiredness = Requiredness.OPTIONAL
    )
    private Integer currentPrice;

    @FieldDoc(
            description = "按配置解析后的属性", requiredness = Requiredness.OPTIONAL
    )
    private List<ParsedPropertiesVO> parsedProperties;

    @FieldDoc(
            description = "是否包含缺货货品", requiredness = Requiredness.OPTIONAL
    )
    private Boolean isIncludeStockLackGoods;

    @FieldDoc(
            description = "属性", requiredness = Requiredness.OPTIONAL
    )
    private List<String> originalProperties;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ParsedPropertiesVO {

        @FieldDoc(
                description = "属性文案", requiredness = Requiredness.OPTIONAL
        )
        private String text;

        @FieldDoc(
                description = "属性颜色", requiredness = Requiredness.OPTIONAL
        )
        private String color;

    }

}
