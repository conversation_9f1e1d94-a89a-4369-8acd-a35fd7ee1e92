package com.sankuai.shangou.supplychain.tof.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.*;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.response.QueryDeliveryExceptionDetailListResponse;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.response.QueryDeliveryExceptionListResponse;
import com.sankuai.shangou.supplychain.tof.service.RiderDeliveryOperateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025-04-23
 * @email <EMAIL>
 */
@Slf4j
@InterfaceDoc(
        type = "restful",
        displayName = "商家自营配送异常相关接口",
        description = "商家自营配送的异常功能",
        scenarios = "主要应用于商家自营配送场景"
)
@RestController
@RequestMapping("/api/orderfulfill/app/rider/delivery/exception")
public class RiderDeliveryExceptionController {

    @Resource
    private RiderDeliveryOperateService riderDeliveryOperateService;

    @MethodDoc(
            displayName = "异常列表",
            description = "异常列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "异常列表",
                            type = RiderArrivalLocationRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/exception/list",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/list", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public QueryDeliveryExceptionListResponse exceptionList(@Valid @RequestBody QueryDeliveryExceptionListRequest request) {
        return riderDeliveryOperateService.queryExceptionList(request);
    }


    @MethodDoc(
            displayName = "异常详情",
            description = "异常详情",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "异常列表",
                            type = RiderArrivalLocationRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/exception/detail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/detail", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public QueryDeliveryExceptionDetailListResponse exceptionDetail(@Valid @RequestBody QueryExceptionDetailRequest request) {
        return riderDeliveryOperateService.queryExceptionDetail(request.getChannelOrderId(), request.getChannelId());
    }

    @MethodDoc(
            displayName = "异常上报",
            description = "异常上报",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "异常上报",
                            type = RiderArrivalLocationRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/delivery/exception/report",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/report", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public void exceptionReport(@Valid @RequestBody DeliveryExceptionReportRequest request) {
        riderDeliveryOperateService.reportException(request);
    }
}
