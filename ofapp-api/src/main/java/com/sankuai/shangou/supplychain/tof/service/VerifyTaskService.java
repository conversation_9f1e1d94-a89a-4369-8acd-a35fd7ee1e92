package com.sankuai.shangou.supplychain.tof.service;

import com.dianping.cat.Cat;
import com.meituan.shangou.saas.utils.ChannelTypeConvertUtils;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.thrift.publisher.response.PaginationList;
import com.sankuai.shangou.commons.thrift.publisher.response.TPageResult;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.sdms.sdk.verify.VerifyTaskThriftService;
import com.sankuai.shangou.logistics.sdms.sdk.verify.dto.*;
import com.sankuai.shangou.logistics.sdms.sdk.verify.request.*;
import com.sankuai.shangou.supplychain.tof.controller.vo.LabelVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.PageVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.verify.VerifyPendingTaskInfoVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.verify.VerifyRiderEndTaskResponse;
import com.sankuai.shangou.supplychain.tof.controller.vo.verify.VerifyRiderEndTaskVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.verify.VerifyTaskInfoVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.verify.request.PostFailResultRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.verify.request.PostSuccessResultRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.verify.request.QueryEndTaskRequest;
import com.sankuai.shangou.supplychain.tof.enums.CatEventEnum;
import com.sankuai.shangou.supplychain.tof.utils.RequestContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/14 23:10
 **/
@Service
@Slf4j
public class VerifyTaskService {
    @Resource
    private VerifyTaskThriftService verifyTaskThriftService;

    public Optional<Long> tryToAssignVerifyTask(Long deliveryOrderId, String channelOrderId, Integer channelId) {
        AssignVerifyTaskRequest request = new AssignVerifyTaskRequest();
        request.setTenantId(LoginContextUtils.getAppLoginTenant());
        request.setStoreId(LoginContextUtils.getAppLoginStoreId());
        request.setDeliveryOrderId(deliveryOrderId);
        request.setRiderAccountId(LoginContextUtils.getAppLoginAccountId());
        request.setRiderEmpId(LoginContextUtils.getAppLoginUser().getEmployeeId());
        request.setViewOrderId(channelOrderId);
        request.setOrderBizType(ChannelTypeConvertUtils.convert2OrderBizType(channelId));

        log.info("start invoke deliveryOperationThriftService.tryToAssignVerifyTask, request: {}", request);
        TResult<TVerifyTaskInfo> tResult = verifyTaskThriftService.tryToAssignVerifyTask(request);
        log.info("end invoke deliveryOperationThriftService.tryToAssignVerifyTask, tResult: {}", tResult);

        if (!tResult.isSuccess()) {
            throw new BizException("尝试下发验证任务失败: " + tResult.getMsg());
        }

        if (Objects.nonNull(tResult.getData()) && Objects.nonNull(tResult.getData().getId())) {
            Cat.logEvent(CatEventEnum.ASSIGN_VERIFY_TASK_SUCCESS.getType(), CatEventEnum.ASSIGN_VERIFY_TASK_SUCCESS.getName());
            return Optional.of(tResult.getData().getId());
        }

        return Optional.empty();
    }

    public VerifyTaskInfoVO queryTaskInfo() {
        QueryVerifyTaskRequest request = new QueryVerifyTaskRequest();
        request.setRiderAccountId(LoginContextUtils.getAppLoginAccountId());

        log.info("start invoke verifyTaskThriftService.queryTaskInfo, request: {}", request);
        TResult<TVerifyTaskInfo> tResult = verifyTaskThriftService.queryTaskInfo(request);
        log.info("end invoke verifyTaskThriftService.queryTaskInfo, result: {}", tResult);
        if(!tResult.isSuccess()) {
            throw new BizException("查询验证任务失败:" + tResult.getMsg());
        }

        return Optional.ofNullable(tResult.getData())
                .map(task -> new VerifyTaskInfoVO(task.getId(), task.getTaskStatus(), task.getRemindTime()))
                .orElse(null);
    }

    public String getAuthorizeCode(Long taskId) {
        GetAuthorizeCodeRequest tRequest = new GetAuthorizeCodeRequest();
        tRequest.setTaskId(taskId);
        tRequest.setRiderAccountId(LoginContextUtils.getAppLoginAccountId());
        tRequest.setTenantId(LoginContextUtils.getAppLoginTenant());
        tRequest.setEmployeeId(LoginContextUtils.getAppLoginUser().getEmployeeId());
        tRequest.setPlatform(AppLoginContextHolder.getAppLoginContext().getOs());
        tRequest.setUuid(AppLoginContextHolder.getAppLoginContext().getUuid());
        tRequest.setIp(RequestContextUtils.getClientIp());
        tRequest.setUserAgent(RequestContextUtils.getUserAgent());
        tRequest.setUserName(LoginContextUtils.getAppLoginUser().getEmployeeName());
        tRequest.setUserPhone(LoginContextUtils.getAppLoginUser().getEmployeePhone());
        tRequest.setVersion(AppLoginContextHolder.getAppLoginContext().getAppVersion());

        log.info("start invoke verifyTaskThriftService.getAuthorizeCode, request: {}", tRequest);
        TResult<AuthorizeCodeInfo> tResult = verifyTaskThriftService.getAuthorizeCode(tRequest);
        log.info("end invoke verifyTaskThriftService.getAuthorizeCode, result: {}", tResult);
        if(!tResult.isSuccess()) {
            throw new BizException(tResult.getCode(), "获取授权码失败:" + tResult.getMsg());
        }

        return tResult.getData().getAuthorizeCode();
    }


    public void postSuccessResult(PostSuccessResultRequest request) {
        PostVerifySuccessResultRequest tRequest = new PostVerifySuccessResultRequest();
        tRequest.setTaskId(request.getTaskId());
        tRequest.setRequestCode(request.getAuthorizeCode());
        tRequest.setResponseCode(request.getResponseCode());
        tRequest.setTenantId(LoginContextUtils.getAppLoginTenant());
        tRequest.setRiderAccountId(LoginContextUtils.getAppLoginAccountId());
        tRequest.setUuid(AppLoginContextHolder.getAppLoginContext().getUuid());


        log.info("start invoke verifyTaskThriftService.postVerifySuccessResult, request: {}", request);
        TResult<Void> tResult = verifyTaskThriftService.postVerifySuccessResult(tRequest);
        log.info("end invoke verifyTaskThriftService.postVerifySuccessResult, result: {}", tResult);
        if(!tResult.isSuccess()) {
            log.error("提交验证成功,结果失败, request: {}, result: {}", request, tResult);
            throw new BizException(tResult.getCode(), tResult.getMsg());
        }
    }

    public boolean postFailResult(PostFailResultRequest request) {
        PostVerifyFailResultRequest tRequest = new PostVerifyFailResultRequest();
        tRequest.setTaskId(request.getTaskId());
        tRequest.setRequestCode(request.getAuthorizeCode());
        tRequest.setErrCode(request.getCallBackCode());
        tRequest.setErrMsg(request.getErrorMsg());
        tRequest.setUserName(LoginContextUtils.getAppLoginUser().getEmployeeName());
        tRequest.setUserAgent(RequestContextUtils.getUserAgent());
        tRequest.setIp(RequestContextUtils.getClientIp());
        tRequest.setUuid(AppLoginContextHolder.getAppLoginContext().getUuid());
        tRequest.setEmployeeId(LoginContextUtils.getAppLoginUser().getEmployeeId());
        tRequest.setTenantId(LoginContextUtils.getAppLoginTenant());
        tRequest.setRiderAccountId(LoginContextUtils.getAppLoginAccountId());

        log.info("start invoke verifyTaskThriftService.postVerifyFailResult, request: {}", request);
        TResult<PostVerifyFailResponse> tResult = verifyTaskThriftService.postVerifyFailResult(tRequest);
        log.info("end invoke verifyTaskThriftService.postVerifyFailResult, result: {}", tResult);
        if(!tResult.isSuccess()) {
            log.error("提交验证失败,结果失败, request: {}, result: {}", request, tResult);
            throw new BizException(tResult.getCode(), tResult.getMsg());
        }

        return Objects.nonNull(tResult.getData()) && Objects.equals(tResult.getData().getTaskIsCompleted(), true);
    }


    public VerifyRiderEndTaskResponse queryRiderEndTask(QueryEndTaskRequest taskRequest) {
        QueryRiderEndTaskRequest request = new QueryRiderEndTaskRequest();
        request.setRiderAccountId(LoginContextUtils.getAppLoginAccountId());
        request.setPage(taskRequest.getPage());
        request.setPageSize(taskRequest.getPageSize());
        if (taskRequest.getBeginTime() == null || taskRequest.getEndTime() == null) {
            request.setBeginTime(LocalDate.now().minusDays(30).atStartOfDay());
            request.setEndTime(LocalDate.now().atTime(23, 59, 59,999));
        }else {
            LocalDate beginTime = LocalDate.parse(taskRequest.getBeginTime());
            LocalDate endTime = LocalDate.parse(taskRequest.getEndTime());
            request.setBeginTime(beginTime.atStartOfDay());
            request.setEndTime(endTime.atTime(23, 59, 59, 999));
        }
        request.setTaskStatus(taskRequest.getTaskStatus());
        request.setVerifyResult(taskRequest.getVerifyResult());
        try {
            log.info("start invoke verifyTaskThriftService.queryRiderEndTask, request: {}", request);
            TPageResult<TVerifyEndTaskDTO> result = verifyTaskThriftService.queryRiderEndTask(request);
            if (!result.isSuccess() || result.getData() == null) {
                log.error("查询骑手验证任务列表失败, result: {}", result);
                throw new BizException("系统异常");
            }
            PaginationList<TVerifyEndTaskDTO> data = result.getData();
            PageVO pageVO = new PageVO();
            pageVO.setPage(data.getPage());
            int total = data.getTotal().intValue();
            pageVO.setTotalSize(total);
            pageVO.setTotalPage(((total - 1) / data.getPageSize()) + 1);
            VerifyRiderEndTaskResponse verifyRiderEndTaskResponse = new VerifyRiderEndTaskResponse();
            verifyRiderEndTaskResponse.setPage(pageVO);
            verifyRiderEndTaskResponse.setTaskList(data.getList().stream().map(this::buildVO).collect(Collectors.toList()));
            return verifyRiderEndTaskResponse;
        } catch (BizException e) {
            log.error("查询骑手验证任务列表异常", e);
            throw new BizException("系统异常");
        }
    }

    public VerifyRiderEndTaskVO buildVO(TVerifyEndTaskDTO taskDTO) {
        VerifyRiderEndTaskVO vo = new VerifyRiderEndTaskVO();
        vo.setTaskId(taskDTO.getTaskId());
        vo.setTaskStatus(taskDTO.getTaskStatus());
        vo.setPushTaskTime(taskDTO.getPushTaskTime());
        vo.setExpireTime(taskDTO.getExpireTime());
        vo.setCompleteTime(taskDTO.getCompleteTime());
        vo.setCollectMode(taskDTO.getCollectMode());
        vo.setFaceIdentifyResult(taskDTO.getFaceIdentifyResult());
        vo.setHelmetIdentifyResult(taskDTO.getHelmetIdentifyResult());
        vo.setDressingIdentifyResult(taskDTO.getDressingIdentifyResult());
        if (CollectionUtils.isNotEmpty(taskDTO.getLabels())) {
            List<LabelVO> labelVOList = taskDTO.getLabels().stream()
                    .map(label -> new LabelVO(label.getLabelName(), label.getLabelDesc()))
                    .collect(Collectors.toList());
            vo.setLabels(labelVOList);
        } else {
            vo.setLabels(Collections.emptyList());
        }
        return vo;
    }

    public VerifyPendingTaskInfoVO queryPendingTask() {
        QueryVerifyTaskRequest request = new QueryVerifyTaskRequest();
        request.setRiderAccountId(LoginContextUtils.getAppLoginAccountId());
        TResult<TVerifyPendingTaskInfo> tResult = verifyTaskThriftService.queryPendingTaskInfo(request);

        if(!tResult.isSuccess()) {
            throw new BizException("查询验证任务失败:" + tResult.getMsg());
        }

        return Optional.ofNullable(tResult.getData())
                .map(task ->{
                    VerifyPendingTaskInfoVO verifyTaskInfoVO = new VerifyPendingTaskInfoVO();
                    verifyTaskInfoVO.setTaskId(task.getId());
                    verifyTaskInfoVO.setTaskStatus(task.getTaskStatus());
                    verifyTaskInfoVO.setTaskRemainTime(task.getRemindTime());
                    verifyTaskInfoVO.setPushTaskTime(task.getPushTaskTime());
                    verifyTaskInfoVO.setCollectMode(task.getCollectMode());
                    verifyTaskInfoVO.setNearExpirationTime(task.getNearExpirationTime());
                    return verifyTaskInfoVO;
                }).orElse(null);
    }
}
