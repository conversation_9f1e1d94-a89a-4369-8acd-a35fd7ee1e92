package com.sankuai.shangou.supplychain.tof.controller.vo.picking;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.TConsumableMaterialInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 骑手的拣货任务详情.
 *
 * <AUTHOR>
 * @since 2021/11/15 16:31
 */
@TypeDoc(
        description = "骑手的拣货任务详情"
)
@Data
public class RiderPickingDetailVO {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    private String channelName;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    private String storeName;

    @FieldDoc(
            description = "运单 ID", requiredness = Requiredness.REQUIRED
    )
    private Long deliveryOrderId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    private String channelOrderId;

    @FieldDoc(
            description = "订单流水", requiredness = Requiredness.REQUIRED
    )
    private Long serialNo;

    @FieldDoc(
            description = "配送订单类型  1-立即单 2-预约单", requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryOrderType;

    @FieldDoc(
            description = "配送订单类型名称", requiredness = Requiredness.REQUIRED
    )
    private String deliveryOrderTypeName;

    @FieldDoc(
            description = "订单创建时间戳", requiredness = Requiredness.REQUIRED
    )
    private Long createTime;

    @FieldDoc(
            description = "预计送达时间开始时间", requiredness = Requiredness.REQUIRED
    )
    private Long estimateArriveTimeStart;

    @FieldDoc(
            description = "预计送达时间截止时间", requiredness = Requiredness.REQUIRED
    )
    private Long estimateArriveTimeEnd;

    @FieldDoc(
            description = "收货人姓名", requiredness = Requiredness.REQUIRED
    )
    private String receiverName;

    @FieldDoc(
            description = "收货人电话号码", requiredness = Requiredness.REQUIRED
    )
    private String receiverPhone;

    @FieldDoc(
            description = "收货人地址", requiredness = Requiredness.REQUIRED
    )
    private String receiverAddress;

    // 收货人信息 end

    // 拣货条目信息 start

    @FieldDoc(
            description = "拣货工单 ID", requiredness = Requiredness.REQUIRED
    )
    private Long pickWorkOrderId;

    @FieldDoc(
            description = "拣货工单状态", requiredness = Requiredness.REQUIRED
    )
    private Integer pickWorkOrderStatus;

    @FieldDoc(
            description = "拣货任务涉及的商品总数量", requiredness = Requiredness.REQUIRED
    )
    private Integer itemCount;

    @FieldDoc(
            description = "备注", requiredness = Requiredness.OPTIONAL
    )
    private String comments;


    @FieldDoc(
            description = "是否弱校验sn", requiredness = Requiredness.REQUIRED
    )
    private Boolean isWeakCheckSn;

    @FieldDoc(
            description = "待拣商品信息列表", requiredness = Requiredness.REQUIRED
    )
    private List<RiderPickTaskInfoVO> waitPickTasks;

    // 拣货条目信息 end
    @FieldDoc(
            description = "待拣商品信息列表", requiredness = Requiredness.REQUIRED
    )
    private List<TConsumableMaterialInfo> consumableMaterialInfoList;

    @FieldDoc(
            description = "异常单缺货项", requiredness = Requiredness.REQUIRED
    )

    private List<LackStockGoodsVO> lackStockGoodsList;

    /**
     * 目前在业务上，一个封签容具大概可以装下2-3个高价值封签交付的商品，一个店员配送一趟最多可以携带2个封签容具
     * 所以，当封签交付的商品数量超过一个阈值时（目前暂定的是5），一趟配送无法完成
     * 此时需要走降低流程，不进行封签交付
     */
    @FieldDoc(
            description = "封签商品数量是否超过了阈值限制", requiredness = Requiredness.REQUIRED
    )
    private Boolean isSealGoodsQtyExceedLimit;

    @FieldDoc(
            description = "背包提示文案，临时支持", requiredness = Requiredness.REQUIRED
    )
    private String giftBagHint;

    @FieldDoc(
            description = "是否默认打开扫描器", requiredness = Requiredness.REQUIRED
    )
    private Boolean defaultOpenScanner;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LackStockGoodsVO {
        @FieldDoc(description = "货品名称")
        private String goodsName;
        @FieldDoc(description = "货品SKU编号")
        private String skuId;
        @FieldDoc(description = "货品实拍图")
        private String picUrl;
        @FieldDoc(description = "货品规格")
        private String spec;
        @FieldDoc(description = "货品upc编号列表")
        private List<String> upcList;
        @FieldDoc(description = "要货量")
        private Integer needCount;
        @FieldDoc(description = "剩余可售库存")
        private Integer salableCount;
        @FieldDoc(description = "缺货量")
        private Integer lackCount;
    }



}
