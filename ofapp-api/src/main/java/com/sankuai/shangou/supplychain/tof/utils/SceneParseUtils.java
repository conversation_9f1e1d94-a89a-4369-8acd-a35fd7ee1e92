package com.sankuai.shangou.supplychain.tof.utils;

import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.utils.AddressSceneConvertUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/4/30 15:40
 **/
@Slf4j
public class SceneParseUtils {
    public static boolean isRestaurant(String scene) {
        return StringUtils.isNotBlank(scene) && MccConfigUtil.getDhTurnAggLimitSceneList().contains(scene);
    }
}
