package com.sankuai.shangou.supplychain.tof.component;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.questionnaire.DeliveryQuestionnaireThriftService;
import com.sankuai.shangou.logistics.delivery.questionnaire.dto.DeliveryQuestionnaireDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/20 10:15
 **/
@Rhino
@Slf4j
public class QuestionnaireComponent {
    @Resource
    private DeliveryQuestionnaireThriftService deliveryQuestionnaireThriftService;

    @Degrade(rhinoKey = "QuestionnaireComponent.queryDeliveryQuestionnaireMap", fallBackMethod = "queryDeliveryQuestionnaireMapFallback", timeoutInMilliseconds = 2000)
    public Map<Long, List<DeliveryQuestionnaireDTO>> queryDeliveryQuestionnaireMap(List<Long> deliveryOrderIdList) {

        if (CollectionUtils.isEmpty(deliveryOrderIdList)) {
            return Collections.emptyMap();
        }

        TResult<List<DeliveryQuestionnaireDTO>> tResult = deliveryQuestionnaireThriftService.queryQuestionnaireByDeliveryOrderIds(deliveryOrderIdList);

        if (!tResult.isSuccess()) {
            log.warn("查询问卷信息失败");
            Cat.logEvent("DELIVERY_QUESTIONNAIRE", "QUERY_FAIL");
            return Collections.emptyMap();
        }

        return tResult.getData().stream().collect(Collectors.groupingBy(DeliveryQuestionnaireDTO::getDeliveryOrderId));

    }

    public Map<Long, List<DeliveryQuestionnaireDTO>> queryDeliveryQuestionnaireMapFallback(List<Long> deliveryOrderIdList) {
        log.error("QuestionnaireComponent.queryDeliveryQuestionnaireMap 发生降级");
        return Collections.emptyMap();
    }
}
