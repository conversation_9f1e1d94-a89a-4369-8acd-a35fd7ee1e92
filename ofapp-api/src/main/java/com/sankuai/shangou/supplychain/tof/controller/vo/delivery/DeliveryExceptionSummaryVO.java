package com.sankuai.shangou.supplychain.tof.controller.vo.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "配送异常概要VO"
)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DeliveryExceptionSummaryVO {

    @FieldDoc(
            description = "运单id"
    )
    private String deliveryOrderId;

    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单id"
    )
    private String channelOrderId;

    @FieldDoc(
            description = "渠道日流水号"
    )
    private Integer daySeq;

    @FieldDoc(
            description = "门店id"
    )

    private Long storeId;
    @FieldDoc(
            description = "订单支付时间"
    )
    private Long payTime;

    @FieldDoc(
            description = "配送异常明细列表"
    )
    private List<DeliveryExceptionInfoVO> deliveryExceptionInfoVOS;
}
