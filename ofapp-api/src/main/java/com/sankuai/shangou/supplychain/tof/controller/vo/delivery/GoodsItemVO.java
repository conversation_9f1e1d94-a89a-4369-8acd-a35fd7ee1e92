package com.sankuai.shangou.supplychain.tof.controller.vo.delivery;

import com.meituan.shangou.goodscenter.enums.LifecycleStatusEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/5 16:00
 **/
@Data
public class GoodsItemVO {
    private String goodsId;

    private String goodsName;

    private List<String> realPicUrlList;

    private String specification;

    private Integer iceCount;

    private Integer normalCount;

    private Integer noTemperatureCount;

    private Integer dependOnRemarkCount;
}
