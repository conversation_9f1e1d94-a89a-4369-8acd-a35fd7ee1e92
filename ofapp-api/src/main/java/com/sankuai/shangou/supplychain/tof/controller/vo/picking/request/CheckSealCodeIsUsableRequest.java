package com.sankuai.shangou.supplychain.tof.controller.vo.picking.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Objects;
import java.util.Optional;

/**
 * 查询骑手已领取订单的待拣货内容的请求.
 *
 * <AUTHOR>
 * @since 2021/11/12 14:34
 */
@TypeDoc(
        description = "查询骑手已领取订单的待拣货内容的请求"
)
@Data
public class CheckSealCodeIsUsableRequest {

    @FieldDoc(
            description = "封签容具编码", requiredness = Requiredness.REQUIRED
    )
    @NotBlank
    private String barCode;

}
