package com.sankuai.shangou.supplychain.tof.controller.vo.picking.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 查询等待骑手领取的订单的请求.
 *
 * <AUTHOR>
 * @since 2021/6/11 15:07
 */
@TypeDoc(
        description = "查询等待骑手领取的订单的请求"
)
@Data
public class QueryRiderWaitToGetOrderRequest {

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Integer size;
}
