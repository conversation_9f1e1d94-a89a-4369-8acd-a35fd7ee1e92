package com.sankuai.shangou.supplychain.tof.enums;

/**
 * <AUTHOR>
 * @since 2024/4/24 20:46
 **/
public enum FilterSpilitTypeEnum {
    NO_FILTER(0, "不过滤"),
    KEEP_SPLIT(1, "保留拣配分离"),
    KEEP_NOT_SPLIT(2, "保留非拣配分离"),
    ;

    private int code;
    private String desc;

    FilterSpilitTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
