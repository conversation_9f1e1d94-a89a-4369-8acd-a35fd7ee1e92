package com.sankuai.shangou.supplychain.tof.component;

import com.alibaba.fastjson.JSON;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.stock.operate.center.common.TemperatureType;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.warehouse.TradeLocationRecommendService;
import com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch;
import com.sankuai.shangou.logistics.warehouse.dto.RecommendSku;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.request.QueryLocationListRequest;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sankuai.meituan.reco.pickselect.consts.TemperaturePropertyEnum.ICE_TEMPERATURE;
import static com.sankuai.meituan.reco.pickselect.consts.TemperaturePropertyEnum.NORMAL_TEMPERATURE;

/**
 * <AUTHOR>
 * @since 2024/4/28 15:42
 **/
@Rhino
@Slf4j
public class RecommendLocationComponent {
    @Resource
    private TradeLocationRecommendService tradeLocationRecommendService;

    @Degrade(rhinoKey = "RecommendLocationComponent.recommendLocations",
            fallBackMethod = "recommendLocationsFallback",
            timeoutInMilliseconds = 5000)
    public Map<RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> recommendLocations(TradeShippingOrderDTO tradeShippingOrderDTO) {
        QueryLocationListRequest request = new QueryLocationListRequest();
        request.setMerchantId(LoginContextUtils.getAppLoginTenant());
        request.setRepositoryId(LoginContextUtils.getAppLoginStoreId());
        request.setRecommendSkus(
                tradeShippingOrderDTO.getItems().stream().map(
                        tradeShippingOrderItemDTO -> {
                            RecommendSku recommendSku = new RecommendSku();
                            recommendSku.setSkuId(tradeShippingOrderItemDTO.getSkuId());
                            recommendSku.setRequestQuantity(tradeShippingOrderItemDTO.getActualQty());
                            recommendSku.setTemperatureType(convertTemperatureAttribute2TemperatureType(tradeShippingOrderItemDTO.getTemperatureZoneCode()));
                            return recommendSku;
                        }
                ).collect(Collectors.toList()));

        TResult<List<RecommendLocationBatch>> result;
        try {
            log.info("start invoke tradeLocationRecommendService.recommendLocations, request: {}", JSON.toJSONString(request));
            result = tradeLocationRecommendService.recommendLocations(request);
            log.info("end invoke tradeLocationRecommendService.recommendLocations, result: {}", JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("查询推荐库位失败", e);
            throw new ThirdPartyException("查询推荐库位失败");
        }

        if (!result.isSuccess()) {
            throw new BizException("查询推荐库位失败");
        }
        return result.getData()
                .stream()
                .collect(Collectors.toMap(
                        RecommendLocationBatch::getSku,
                        Lists::newArrayList,
                        (older, newer) -> {
                            newer.addAll(older);
                            return newer;
                        }
                ));
    }


    public Map<RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> recommendLocationsFallback(TradeShippingOrderDTO tradeShippingOrderDTO) {
        log.warn("recommendLocationsFallback , tradeShippingOrderDTO = {}", tradeShippingOrderDTO);
        return Collections.emptyMap();
    }


    private Integer convertTemperatureAttribute2TemperatureType(String temperatureAttributeStr) {
        if (NORMAL_TEMPERATURE.getDesc().equals(temperatureAttributeStr)) {
            return TemperatureType.NORMAL.getCode();
        }

        if (temperatureAttributeStr.equals(ICE_TEMPERATURE.getDesc())) {
            return TemperatureType.FREEZING.getCode();
        }

        return TemperatureType.UNDEFINED.getCode();
    }
}
