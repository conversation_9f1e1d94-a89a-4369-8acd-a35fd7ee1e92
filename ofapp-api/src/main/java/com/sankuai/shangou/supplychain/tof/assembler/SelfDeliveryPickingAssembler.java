package com.sankuai.shangou.supplychain.tof.assembler;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OnlineBaseDeliveryInfoVO;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DeliveryStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.utils.ChannelTypeConvertUtils;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaProducer;
import com.sankuai.meituan.reco.pickselect.dh.PickingProcessService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.RiderOperateThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryRiskControlOrder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.delivery.questionnaire.dto.DeliveryQuestionnaireDTO;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.enums.CompensateType;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import com.sankuai.shangou.logistics.warehouse.message.CompensateFulfillmentOrderMessage;
import com.sankuai.shangou.supplychain.tof.component.*;
import com.sankuai.shangou.supplychain.tof.config.LionConfigUtils;
import com.sankuai.shangou.supplychain.tof.controller.convert.RiderDeliveryConvert;
import com.sankuai.shangou.supplychain.tof.controller.convert.RiderPickingConvert;
import com.sankuai.shangou.supplychain.tof.controller.vo.PageVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryExceptionSummaryVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.RiderPickingOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.PageQueryRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.QueryIncrementPickCompletedOrderRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.RevenueDetailVo;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.TradeOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.CompletedPickOrderStatisticVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.PickingOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.RiderPickingDetailVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.request.QueryOrderPickingDetailsRequest;
import com.sankuai.shangou.supplychain.tof.enums.ComponentTypeEnum;
import com.sankuai.shangou.supplychain.tof.enums.ErrorCodeEnum;
import com.sankuai.shangou.supplychain.tof.enums.FilterSpilitTypeEnum;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import com.sankuai.shangou.supplychain.tof.utils.MemPageUtils;
import com.sankuai.shangou.supplychain.tof.wrapper.DeliveryServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.OSWServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.StockWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.sankuai.shangou.supplychain.tof.controller.convert.RiderPickingConvert.buildRiderPickingDetailVO;
import static com.sankuai.shangou.supplychain.tof.enums.ComponentTypeEnum.*;

/**
 * <AUTHOR>
 * @since 2024/4/28 15:01
 **/
@Service
@Slf4j
public class SelfDeliveryPickingAssembler {

    @Resource
    private TradeShippingOrderComponent tradeShippingOrderComponent;

    @Resource
    private DeliveryOrderComponent deliveryOrderComponent;

    @Resource
    private GoodsInfoComponent goodsInfoComponent;

    @Resource
    private ProductHighPriceTagComponent productHighPriceTagComponent;

    @Resource
    private AbnormalOrderComponent abnormalOrderComponent;

    @Resource
    private RecommendLocationComponent recommendLocationComponent;

    @Resource
    private TradeOrderInfoComponent tradeOrderInfoComponent;

    @Resource
    private PickingProcessService pickingProcessService;

    @Resource
    private DeliveryServiceWrapper deliveryServiceWrapper;

    @Resource
    private RiderOperateThriftService riderOperateRpcService;

    @Resource
    private RevenueComponent revenueComponent;

    @Resource
    private DeliveryExceptionComponent deliveryExceptionComponent;

    @Resource
    private QuestionnaireComponent questionnaireComponent;

    @Resource
    private DeliveryRiskControlComponent deliveryRiskControlComponent;

    @Resource
    private TurnDeliveryButtonComponent turnDeliveryButtonComponent;

    @Resource
    private SealDeliveryTagComponent sealDeliveryTagComponent;

    @Resource
    private StockWrapper stockWrapper;
    @Resource
    private PickingOrderComponent pickingOrderComponent;

    @Resource
    private OSWServiceWrapper oswServiceWrapper;

    @MafkaProducer(namespace = "com.sankuai.mafka.castle.daojiacommon", appkey = "com.sankuai.shangou.supplychain.ofapp", topic = "compensate_fulfillment_order_message")
    private IProducerProcessor<Object, String> compensateOrderProducer;

    private static int LACK_STOCK_CODE = 20000099;

    private static int CONSUMABLE_STOKE_LAKE = 20000089;

    private static int ONE_YUAN_ORDER_TAG_VALUE = 102;

    public RiderPickingDetailVO queryPickingDetail(QueryOrderPickingDetailsRequest request) {
        TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = new TradeOrderInfoComponent.TradeOrderKey(
                ChannelTypeConvertUtils.convert2OrderBizType(request.getChannelId()), request.getChannelOrderId());

        //查拣货单
        TradeShippingOrderDTO shippingOrderDTO = tradeShippingOrderComponent.queryTradeShippingOrderOrderDetail(tradeOrderKey);

        //查订单详情
        Map<TradeOrderInfoComponent.TradeOrderKey, OCMSOrderVO> tradeOrderVOMap = tradeOrderInfoComponent.queryTradeOrderInfoList(Collections.singletonList(tradeOrderKey));
        OCMSOrderVO ocmsOrder = tradeOrderVOMap.get(tradeOrderKey);

        DeliveryStatusEnum pickStatus = Optional.ofNullable(ocmsOrder.getOcmsDeliveryInfoVO()).map(OnlineBaseDeliveryInfoVO::getDeliveryStatus)
                .map(DeliveryStatusEnum::enumOf).orElse(null);
        OrderStatusEnum orderStatus = OrderStatusEnum.enumOf(ocmsOrder.getOrderStatus());

        // 若订单状态为已拣货或已取消，则放弃查询
        if (Objects.equals(shippingOrderDTO.getStatus(), TradeShippingOrderStatus.FINISH.getCode())) {
            log.warn("拣货已完成，不支持进行扫码拣货, pickStatus:{}, orderStatus:{}", pickStatus, orderStatus);
            Cat.logEvent("DH_ADAPT_SN", "ORDER_IS_COMPLETE");
            throw new BizException(ErrorCodeEnum.RIDER_PICK_ALREADY_COMPLETED.getCode(), ErrorCodeEnum.RIDER_PICK_ALREADY_COMPLETED.getMessage());
        }

        if (pickStatus == DeliveryStatusEnum.CANCELED || orderStatus == OrderStatusEnum.CANCELED) {
            log.warn("拣货已取消，不支持进行扫码拣货, pickStatus:{}, orderStatus:{}", pickStatus, orderStatus);
            throw new BizException(ErrorCodeEnum.RIDER_PICK_ALREADY_CANCELED.getCode(), ErrorCodeEnum.RIDER_PICK_ALREADY_CANCELED.getMessage());
        }

        //查询推荐库位
        Map<RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> recommendLocationBatchMap = Collections.emptyMap();
        try {
            recommendLocationBatchMap = recommendLocationComponent.recommendLocations(shippingOrderDTO);
        } catch (Exception e) {
            log.error("查询推荐库位失败", e);
        }

        //查询货品信息,拿到保质期
        Map<String, DepotGoodsDetailDto> depotGoodsDetailDtos = Collections.emptyMap();
        try {
            depotGoodsDetailDtos = goodsInfoComponent.queryGoodsInfo(shippingOrderDTO);
        } catch (Exception e) {
            log.error("查询货品信息失败", e);
        }

        //查询高价值标签
        Map<String, Boolean> skuHighPriceTagMap = Collections.emptyMap();
        try {
            skuHighPriceTagMap = productHighPriceTagComponent.getProductHighPriceTagBySkuIds(shippingOrderDTO);
        } catch (Exception e) {
            log.error("查询高价值标签信息失败", e);
        }

        //查询缺货信息
        Optional<AbnOrderDTO> abnOrderDTOOpt = Optional.empty();
        try {
            abnOrderDTOOpt = abnormalOrderComponent.getLackStockInfo(shippingOrderDTO);
        } catch (Exception e) {
            log.error("查询异常单失败", e);
        }


        return buildRiderPickingDetailVO(shippingOrderDTO, tradeOrderVOMap.get(tradeOrderKey), recommendLocationBatchMap,
                depotGoodsDetailDtos, skuHighPriceTagMap, abnOrderDTOOpt, request.getDeliveryOrderId());
    }


    public <T extends PageQueryRequest> Map<String, Object> queryPickingListTemplate(
            T request,
            Supplier<List<TradeShippingOrderDTO>> queryPickingOrderSupplier,
            List<ComponentTypeEnum> extInfoEnums,
            FilterSpilitTypeEnum filterSpilitTypeEnum) {
        Map<String, Object> resMap = new HashMap<>();

        Long tenantId = LoginContextUtils.getAppLoginTenant();
        Long storeId = LoginContextUtils.getAppLoginStoreId();
        //查拣货单数据
        List<TradeShippingOrderDTO> tPickingOrders = queryPickingOrderSupplier.get();
        //过滤拣配分离订单
        switch (filterSpilitTypeEnum) {
            case KEEP_SPLIT:
                tPickingOrders = IListUtils.nullSafeFilterElement(tPickingOrders,
                        tradeShippingOrderDTO -> Objects.nonNull(tradeShippingOrderDTO.getIsPickDeliverySplit()) && tradeShippingOrderDTO.getIsPickDeliverySplit()
                );
                break;
            case KEEP_NOT_SPLIT:
                tPickingOrders = tPickingOrders.stream()
                        .filter(tradeShippingOrderDTO -> Objects.isNull(tradeShippingOrderDTO.getIsPickDeliverySplit()) || !tradeShippingOrderDTO.getIsPickDeliverySplit())
                        .collect(Collectors.toList());
                break;
            case NO_FILTER:
            default:
                break;
        }

        if (extInfoEnums.contains(FILTER_THIRD_PICK)) {
            tPickingOrders = tPickingOrders.stream()
                    .filter(tradeShippingOrderDTO -> Objects.isNull(tradeShippingOrderDTO.getIsThirdDeliveryPick()) || !tradeShippingOrderDTO.getIsThirdDeliveryPick())
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(tPickingOrders)) {
            resMap.put("pageInfo", new PageVO(0, 0, 0));
            resMap.put("orderList", Lists.newArrayList());
            if (extInfoEnums.contains(PICKER_STATISTICS)) {
                resMap.put("statisticsInfo", new CompletedPickOrderStatisticVO(0, 0));
            }
            return resMap;
        }

        List<TradeOrderInfoComponent.TradeOrderKey> allTradeOrderKeys = IListUtils.mapTo(tPickingOrders, tradeShippingOrderDTO -> new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO.getTradeChannelType(), tradeShippingOrderDTO.getTradeOrderNo()));
        //查订单数据
        Map<TradeOrderInfoComponent.TradeOrderKey, OCMSOrderVO> ocmsOrderMap = tradeOrderInfoComponent.queryTradeOrderInfoListByPartition(allTradeOrderKeys);

        //查运单数据
        List<TRiderDeliveryOrder> tRiderDeliveryOrders = deliveryOrderComponent.queryAllDeliveryOrderByTradeOrderIds(IListUtils.mapTo(ocmsOrderMap.values(), OCMSOrderVO::getOrderId));
        Map<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> tRiderDeliveryOrderMap = IListUtils.nullSafeAndOverrideCollectToMap(tRiderDeliveryOrders, tRiderDeliveryOrder -> new TradeOrderInfoComponent.TradeOrderKey(tRiderDeliveryOrder.getOrderBizTypeCode(), tRiderDeliveryOrder.getChannelOrderId()), Function.identity());

        //内存分页
        if (extInfoEnums.contains(SORT_BY_DONE_TIME)) {
            tPickingOrders.sort(
                    (tradeShippingOrderDTO1, tradeShippingOrderDTO2) -> {
                        try {
                            return (int) (TimeUtils.toMilliSeconds(tradeShippingOrderDTO2.getShipTime()) - TimeUtils.toMilliSeconds(tradeShippingOrderDTO1.getShipTime()));
                        } catch (Exception e) {
                            return (int) (TimeUtils.toMilliSeconds(tradeShippingOrderDTO2.getCreateTime()) - TimeUtils.toMilliSeconds(tradeShippingOrderDTO1.getCreateTime()));
                        }
                    }
            );
        } else {
            tPickingOrders.sort(
                    //fixme：先用运单创建时间代表拣货任务下发时间
                    (tradeShippingOrderDTO1, tradeShippingOrderDTO2) -> {
                        TRiderDeliveryOrder tRiderDeliveryOrder1 = tRiderDeliveryOrderMap.get(new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO1.getTradeChannelType(), tradeShippingOrderDTO1.getTradeOrderNo()));
                        TRiderDeliveryOrder tRiderDeliveryOrder2 = tRiderDeliveryOrderMap.get(new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO2.getTradeChannelType(), tradeShippingOrderDTO2.getTradeOrderNo()));
                        if (Objects.isNull(tRiderDeliveryOrder1) || Objects.isNull(tRiderDeliveryOrder2)) {
                            return 0;
                        }
                        return (int) (tRiderDeliveryOrder1.getCreateTime() - tRiderDeliveryOrder2.getCreateTime());
                    }
            );
        }

        MemPageUtils.PageInfo<TradeShippingOrderDTO> pagingList = MemPageUtils.pagingList(tPickingOrders, request.getPage(), request.getSize());
        PageVO pageVO = new PageVO(request.getPage(), pagingList.getTotal(), pagingList.getTotalPage());
        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, PickingOrderVO> pickingOrderVOMap = IListUtils.nullSafeStream(pagingList.getPagedList())
                .collect(Collectors.toMap(
                        tradeShippingOrderDTO -> new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO.getTradeChannelType(), tradeShippingOrderDTO.getTradeOrderNo()),
                        tradeShippingOrderDTO -> pickingOrderComponent.buildPickingOrderVO(tradeShippingOrderDTO),
                        (o1, o2) -> o1, LinkedHashMap::new
                ));

        //build运单模块
        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, DeliveryOrderVO> deliveryOrderVOMap = new LinkedHashMap<>();


        //补偿逻辑
        if (extInfoEnums.contains(CHECK_PICK_AND_DELIVERY_ORDER_STATUS)) {
            List<TradeShippingOrderDTO> pagedTradeShippingOrderList = pagingList.getPagedList();
            for (TradeShippingOrderDTO tradeShippingOrderDTO : pagedTradeShippingOrderList) {
                TRiderDeliveryOrder matchRiderDeliveryOrder = tRiderDeliveryOrderMap.get(new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO.getTradeChannelType(), tradeShippingOrderDTO.getTradeOrderNo()));
                //有拣货单，没运单。补偿tms生单消息
                if (Objects.isNull(matchRiderDeliveryOrder)) {
                    Cat.logEvent("PICK_DELIVERY_MISMATCH", "DELIVERY_MISS");
                    sendCompensateForDeliveryMessage(tradeShippingOrderDTO);
                }
            }
        }

        //是否使用考核时间
        boolean useAssessTime = Optional.ofNullable(extInfoEnums).orElse(com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists.newArrayList()).contains(USE_ASSESS_TIME);
        tRiderDeliveryOrderMap.forEach((key, value) -> {
            deliveryOrderVOMap.putIfAbsent(key, deliveryOrderComponent.buildDeliveryOrderVO(value, ocmsOrderMap.get(key), useAssessTime && LionConfigUtils.isNewDeliveryListGrayStore(value.getStoreId())));
        });

        //build订单模块
        Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap = ocmsOrderMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> tradeOrderInfoComponent.buildTradeOrderVO(entry.getValue()),
                        (older, newer) -> newer
                ));


        //配送风控单标签 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, TDeliveryRiskControlOrder> deliveryRiskControlOrderMap = new HashMap<>();
        try {
            deliveryRiskControlOrderMap = deliveryRiskControlComponent.queryRiskControlOrderMap(new ArrayList<>(deliveryOrderVOMap.keySet()));
        } catch (Exception e) {
            log.error("查风控单标签失败", e);
        }

        //配送异常模块 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryExceptionSummaryVO> exceptionSummaryVOMap = new HashMap<>();
        if (extInfoEnums.contains(DELIVERY_EXCEPTION_INFO)) {
            try {
                exceptionSummaryVOMap = deliveryExceptionComponent.queryRiderReportException(new ArrayList<>(deliveryOrderVOMap.keySet()));
            } catch (Exception e) {
                log.error("查询配送异常失败", e);
            }
        }

        //营收模块 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, RevenueDetailVo> revenueDetailVoMap = new HashMap<>();
        if (extInfoEnums.contains(ORDER_REVENUE_INFO)) {
            try {
                revenueDetailVoMap = revenueComponent.revenueComponent(tenantId, new ArrayList<>(deliveryOrderVOMap.keySet()));
            } catch (Exception e) {
                log.error("查询营收数据失败", e);
            }
        }

        //配送问卷模块 失败不影响流程
        Map<Long, List<DeliveryQuestionnaireDTO>> questionnaireMap = new HashMap<>();
        if (extInfoEnums.contains(QUESTIONNAIRE) && MccConfigUtil.getQuestionnaireSwitch()) {
            try {
                questionnaireMap = questionnaireComponent.queryDeliveryQuestionnaireMap(deliveryOrderVOMap.values().stream().map(DeliveryOrderVO::getDeliveryOrderId).collect(Collectors.toList()));
            } catch (Exception e) {
                log.error("查询配送问卷失败", e);
            }
        }

        //查高价值标签 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> highPriceTagMap = Collections.emptyMap();
        if (extInfoEnums.contains(HIGH_PRICE_TAG)) {
            try {
                highPriceTagMap = productHighPriceTagComponent.batchGetProductHighPriceTag(new ArrayList<>(deliveryOrderVOMap.keySet()));
            } catch (Exception e) {
                log.error("查高价值标签失败", e);
            }
        }

        //查缺货标签 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> lackStockMap = Collections.emptyMap();
        try {
            if (extInfoEnums.contains(LACK_STOCK)) {
                lackStockMap = abnormalOrderComponent.getLackStockTag(new ArrayList<>(deliveryOrderVOMap.keySet()));
            }
        } catch (Exception e) {
            log.error("查是否缺货失败", e);
        }

        //查询封签交付标签
        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> sealDeliveryTagMap = Collections.emptyMap();
        try {
            if (extInfoEnums.contains(SEAL_DELIVER) && GrayConfigUtils.judgeIsGrayStore(tenantId, storeId, GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false)) {
                sealDeliveryTagMap = sealDeliveryTagComponent.batchGetSealDeliveryTag(new ArrayList<>(deliveryOrderVOMap.keySet()));
            }
        } catch (Exception e) {
            log.error("查询封签交付标签失败", e);
        }

        //查是否展示转青云按钮 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> couldTurnDapDeliveryMap = Collections.emptyMap();
        try {
            //查询是否可以转配送，这里是自营，目前是直接转青云
            if (extInfoEnums.contains(TURN_DELIVERY_BUTTON_INFO)) {
                //如果之前没有查封签信息，这里补查一次
                if (GrayConfigUtils.judgeIsGrayStore(tenantId, storeId, GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false) && MapUtils.isEmpty(sealDeliveryTagMap)) {
                    couldTurnDapDeliveryMap = turnDeliveryButtonComponent.queryTurnDeliveryButton(tradeOrderVOMap, sealDeliveryTagComponent.batchGetSealDeliveryTag(new ArrayList<>(deliveryOrderVOMap.keySet())));
                } else {
                    couldTurnDapDeliveryMap = turnDeliveryButtonComponent.queryTurnDeliveryButton(tradeOrderVOMap, sealDeliveryTagMap);
                }

            }
        } catch (Exception e) {
            log.error("查询是否可以转青云失败", e);
        }

        //查询是否直营门店，不影响流程
        boolean isDirectStore = false;
        try {
            WarehouseDTO warehouseDTO = oswServiceWrapper.queryWarehouseByWarehouseId(tenantId, storeId);
            if (Objects.nonNull(warehouseDTO) && Objects.equals(warehouseDTO.getOperationMode(), OSWServiceWrapper.DIRECT_OPERATION_MODE)) {
                isDirectStore = true;
            }
        } catch (Exception e) {
            log.error("查询是否直营门店失败", e);
        }

        List<RiderPickingOrderVO> riderDeliveryOrderVOS =
                RiderPickingConvert.buildRiderPickingOrderVOList(pickingOrderVOMap, deliveryOrderVOMap,
                        tradeOrderVOMap,
                        exceptionSummaryVOMap,
                        revenueDetailVoMap,
                        questionnaireMap,
                        deliveryRiskControlOrderMap,
                        couldTurnDapDeliveryMap,
                        lackStockMap,
                        highPriceTagMap,
                        sealDeliveryTagMap,
                        ocmsOrderMap
                        );

        resMap.put("pageInfo", pageVO);
        resMap.put("orderList", riderDeliveryOrderVOS);

        if (extInfoEnums.contains(PICKER_STATISTICS)) {
            long oneYuanOrderCount = tPickingOrders.stream()
                    .map(tPickingOrder -> ocmsOrderMap.get(new TradeOrderInfoComponent.TradeOrderKey(tPickingOrder.getTradeChannelType(), tPickingOrder.getTradeOrderNo())))
                    .filter(Objects::nonNull)
                    .filter(this::judgeIsOneYuanOrder)
                    .count();

            CompletedPickOrderStatisticVO statisticVO = new CompletedPickOrderStatisticVO();
            statisticVO.setTotal(tPickingOrders.size());
            statisticVO.setOneYuanOrderCount((int) oneYuanOrderCount);
            resMap.put("statisticsInfo", statisticVO);
        }

        return resMap;
    }

    private boolean judgeIsOneYuanOrder(OCMSOrderVO order) {
        try {
            if (order == null) {
                return false;
            }

            return order.getTags().stream().anyMatch(tag -> Objects.equals(tag.getType(), ONE_YUAN_ORDER_TAG_VALUE));

        } catch (Exception e) {
            Cat.logEvent("PARSE_ONE_YUAN_ORDER", "ERROR");
            log.error("解析一元单错误", e);
            return false;
        }
    }


    public  Map<String, Object> queryIncrementPickingListTemplate(
            QueryIncrementPickCompletedOrderRequest request,
            Supplier<List<TradeShippingOrderDTO>> queryPickingOrderSupplier,
            List<ComponentTypeEnum> extInfoEnums,
            FilterSpilitTypeEnum filterSpilitTypeEnum) {
        Map<String, Object> resMap = new HashMap<>();

        Long tenantId = LoginContextUtils.getAppLoginTenant();
        Long storeId = LoginContextUtils.getAppLoginStoreId();
        //查拣货单数据
        List<TradeShippingOrderDTO> tPickingOrders = queryPickingOrderSupplier.get();
        //过滤拣配分离订单
        switch (filterSpilitTypeEnum) {
            case KEEP_SPLIT:
                tPickingOrders = IListUtils.nullSafeFilterElement(tPickingOrders,
                        tradeShippingOrderDTO -> Objects.nonNull(tradeShippingOrderDTO.getIsPickDeliverySplit()) && tradeShippingOrderDTO.getIsPickDeliverySplit()
                );
                break;
            case KEEP_NOT_SPLIT:
                tPickingOrders = tPickingOrders.stream()
                        .filter(tradeShippingOrderDTO -> Objects.isNull(tradeShippingOrderDTO.getIsPickDeliverySplit()) || !tradeShippingOrderDTO.getIsPickDeliverySplit())
                        .collect(Collectors.toList());
                break;
            case NO_FILTER:
            default:
                break;
        }

        if (extInfoEnums.contains(FILTER_THIRD_PICK)) {
            tPickingOrders = tPickingOrders.stream()
                    .filter(tradeShippingOrderDTO -> Objects.isNull(tradeShippingOrderDTO.getIsThirdDeliveryPick()) || !tradeShippingOrderDTO.getIsThirdDeliveryPick())
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(tPickingOrders)) {
            resMap.put("pageInfo", new PageVO(0, 0, 0));
            resMap.put("orderList", Lists.newArrayList());
            return resMap;
        }

        List<TradeOrderInfoComponent.TradeOrderKey> allTradeOrderKeys = IListUtils.mapTo(tPickingOrders, tradeShippingOrderDTO -> new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO.getTradeChannelType(), tradeShippingOrderDTO.getTradeOrderNo()));
        //查订单数据
        Map<TradeOrderInfoComponent.TradeOrderKey, OCMSOrderVO> ocmsOrderMap = tradeOrderInfoComponent.queryTradeOrderInfoListByPartition(allTradeOrderKeys);

        //查运单数据
        List<TRiderDeliveryOrder> tRiderDeliveryOrders = deliveryOrderComponent.queryAllDeliveryOrderByTradeOrderIds(IListUtils.mapTo(ocmsOrderMap.values(), OCMSOrderVO::getOrderId));
        Map<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> tRiderDeliveryOrderMap = IListUtils.nullSafeAndOverrideCollectToMap(tRiderDeliveryOrders, tRiderDeliveryOrder -> new TradeOrderInfoComponent.TradeOrderKey(tRiderDeliveryOrder.getOrderBizTypeCode(), tRiderDeliveryOrder.getChannelOrderId()), Function.identity());

        //内存分页
        if (extInfoEnums.contains(SORT_BY_DONE_TIME)) {
            tPickingOrders.sort(
                    (tradeShippingOrderDTO1, tradeShippingOrderDTO2) -> {
                        try {
                            return (int) (TimeUtils.toMilliSeconds(tradeShippingOrderDTO2.getShipTime()) - TimeUtils.toMilliSeconds(tradeShippingOrderDTO1.getShipTime()));
                        } catch (Exception e) {
                            return (int) (TimeUtils.toMilliSeconds(tradeShippingOrderDTO2.getCreateTime()) - TimeUtils.toMilliSeconds(tradeShippingOrderDTO1.getCreateTime()));
                        }
                    }
            );
        } else {
            tPickingOrders.sort(
                    //fixme：先用运单创建时间代表拣货任务下发时间
                    (tradeShippingOrderDTO1, tradeShippingOrderDTO2) -> {
                        TRiderDeliveryOrder tRiderDeliveryOrder1 = tRiderDeliveryOrderMap.get(new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO1.getTradeChannelType(), tradeShippingOrderDTO1.getTradeOrderNo()));
                        TRiderDeliveryOrder tRiderDeliveryOrder2 = tRiderDeliveryOrderMap.get(new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO2.getTradeChannelType(), tradeShippingOrderDTO1.getTradeOrderNo()));
                        if (Objects.isNull(tRiderDeliveryOrder1) || Objects.isNull(tRiderDeliveryOrder2)) {
                            return 0;
                        }
                        return (int) (tRiderDeliveryOrder1.getCreateTime() - tRiderDeliveryOrder2.getCreateTime());
                    }
            );
        }

        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, PickingOrderVO> pickingOrderVOMap = IListUtils.nullSafeStream(tPickingOrders)
                .collect(Collectors.toMap(
                        tradeShippingOrderDTO -> new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO.getTradeChannelType(), tradeShippingOrderDTO.getTradeOrderNo()),
                        tradeShippingOrderDTO -> pickingOrderComponent.buildPickingOrderVO(tradeShippingOrderDTO),
                        (o1, o2) -> o1, LinkedHashMap::new
                ));

        //build运单模块
        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, DeliveryOrderVO> deliveryOrderVOMap = new LinkedHashMap<>();


        //补偿逻辑
        if (extInfoEnums.contains(CHECK_PICK_AND_DELIVERY_ORDER_STATUS)) {
            for (TradeShippingOrderDTO tradeShippingOrderDTO : tPickingOrders) {
                TRiderDeliveryOrder matchRiderDeliveryOrder = tRiderDeliveryOrderMap.get(new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO.getTradeChannelType(), tradeShippingOrderDTO.getTradeOrderNo()));
                //有拣货单，没运单。补偿tms生单消息
                if (Objects.isNull(matchRiderDeliveryOrder)) {
                    Cat.logEvent("PICK_DELIVERY_MISMATCH", "DELIVERY_MISS");
                    sendCompensateForDeliveryMessage(tradeShippingOrderDTO);
                }
            }
        }

        //是否使用考核时间
        boolean useAssessTime = Optional.ofNullable(extInfoEnums).orElse(com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists.newArrayList()).contains(USE_ASSESS_TIME);
        tRiderDeliveryOrderMap.forEach((key, value) -> {
            deliveryOrderVOMap.putIfAbsent(key, deliveryOrderComponent.buildDeliveryOrderVO(value, ocmsOrderMap.get(key), useAssessTime && LionConfigUtils.isNewDeliveryListGrayStore(value.getStoreId())));
        });

        //build订单模块
        Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap = ocmsOrderMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> tradeOrderInfoComponent.buildTradeOrderVO(entry.getValue()),
                        (older, newer) -> newer
                ));


        //配送风控单标签 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, TDeliveryRiskControlOrder> deliveryRiskControlOrderMap = new HashMap<>();
        try {
            deliveryRiskControlOrderMap = deliveryRiskControlComponent.queryRiskControlOrderMap(new ArrayList<>(deliveryOrderVOMap.keySet()));
        } catch (Exception e) {
            log.error("查风控单标签失败", e);
        }

        //配送异常模块 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryExceptionSummaryVO> exceptionSummaryVOMap = new HashMap<>();
        if (extInfoEnums.contains(DELIVERY_EXCEPTION_INFO)) {
            try {
                exceptionSummaryVOMap = deliveryExceptionComponent.queryRiderReportException(new ArrayList<>(deliveryOrderVOMap.keySet()));
            } catch (Exception e) {
                log.error("查询配送异常失败", e);
            }
        }

        //营收模块 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, RevenueDetailVo> revenueDetailVoMap = new HashMap<>();
        if (extInfoEnums.contains(ORDER_REVENUE_INFO)) {
            try {
                revenueDetailVoMap = revenueComponent.revenueComponent(tenantId, new ArrayList<>(deliveryOrderVOMap.keySet()));
            } catch (Exception e) {
                log.error("查询营收数据失败", e);
            }
        }

        //配送问卷模块 失败不影响流程
        Map<Long, List<DeliveryQuestionnaireDTO>> questionnaireMap = new HashMap<>();
        if (extInfoEnums.contains(QUESTIONNAIRE) && MccConfigUtil.getQuestionnaireSwitch()) {
            try {
                questionnaireMap = questionnaireComponent.queryDeliveryQuestionnaireMap(deliveryOrderVOMap.values().stream().map(DeliveryOrderVO::getDeliveryOrderId).collect(Collectors.toList()));
            } catch (Exception e) {
                log.error("查询配送问卷失败", e);
            }
        }

        //查高价值标签 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> highPriceTagMap = Collections.emptyMap();
        if (extInfoEnums.contains(HIGH_PRICE_TAG)) {
            try {
                highPriceTagMap = productHighPriceTagComponent.batchGetProductHighPriceTag(new ArrayList<>(deliveryOrderVOMap.keySet()));
            } catch (Exception e) {
                log.error("查高价值标签失败", e);
            }
        }

        //查缺货标签 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> lackStockMap = Collections.emptyMap();
        try {
            if (extInfoEnums.contains(LACK_STOCK)) {
                lackStockMap = abnormalOrderComponent.getLackStockTag(new ArrayList<>(deliveryOrderVOMap.keySet()));
            }
        } catch (Exception e) {
            log.error("查是否缺货失败", e);
        }

        //查询封签交付标签
        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> sealDeliveryTagMap = Collections.emptyMap();
        try {
            if (extInfoEnums.contains(SEAL_DELIVER) && GrayConfigUtils.judgeIsGrayStore(tenantId, storeId, GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false)) {
                sealDeliveryTagMap = sealDeliveryTagComponent.batchGetSealDeliveryTag(new ArrayList<>(deliveryOrderVOMap.keySet()));
            }
        } catch (Exception e) {
            log.error("查询封签交付标签失败", e);
        }

        //查是否展示转青云按钮 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> couldTurnDapDeliveryMap = Collections.emptyMap();
        try {
            //查询是否可以转配送，这里是自营，目前是直接转青云
            if (extInfoEnums.contains(TURN_DELIVERY_BUTTON_INFO)) {
                //如果之前没有查封签信息，这里补查一次
                if (GrayConfigUtils.judgeIsGrayStore(tenantId, storeId, GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false) && MapUtils.isEmpty(sealDeliveryTagMap)) {
                    couldTurnDapDeliveryMap = turnDeliveryButtonComponent.queryTurnDeliveryButton(tradeOrderVOMap, sealDeliveryTagComponent.batchGetSealDeliveryTag(new ArrayList<>(deliveryOrderVOMap.keySet())));
                } else {
                    couldTurnDapDeliveryMap = turnDeliveryButtonComponent.queryTurnDeliveryButton(tradeOrderVOMap, sealDeliveryTagMap);
                }

            }
        } catch (Exception e) {
            log.error("查询是否可以转青云失败", e);
        }
        //查询是否直营门店，不影响流程
        boolean isDirectStore = false;
        try {
            WarehouseDTO warehouseDTO = oswServiceWrapper.queryWarehouseByWarehouseId(tenantId, storeId);
            if (Objects.nonNull(warehouseDTO) && Objects.equals(warehouseDTO.getOperationMode(), OSWServiceWrapper.DIRECT_OPERATION_MODE)) {
                isDirectStore = true;
            }
        } catch (Exception e) {
            log.error("查询是否直营门店失败", e);
        }

        List<RiderPickingOrderVO> riderDeliveryOrderVOS =
                RiderPickingConvert.buildRiderPickingOrderVOList(pickingOrderVOMap, deliveryOrderVOMap,
                        tradeOrderVOMap,
                        exceptionSummaryVOMap,
                        revenueDetailVoMap,
                        questionnaireMap,
                        deliveryRiskControlOrderMap,
                        couldTurnDapDeliveryMap,
                        lackStockMap,
                        highPriceTagMap,
                        sealDeliveryTagMap,
                        ocmsOrderMap);

        resMap.put("orderList", riderDeliveryOrderVOS);

        return resMap;
    }


    public List<RiderPickingOrderVO> queryBasePickingListTemplate(Supplier<List<TradeShippingOrderDTO>> queryPickingOrderSupplier,
                                                                  AggregateOrderAssembler.AggregateContext aggregateContext) {

        //查拣货单数据
        List<TradeShippingOrderDTO> tPickingOrders = queryPickingOrderSupplier.get();

        if (CollectionUtils.isEmpty(tPickingOrders)) {
            return Lists.newArrayList();
        }

        List<TradeOrderInfoComponent.TradeOrderKey> allTradeOrderKeys = IListUtils.mapTo(tPickingOrders, tradeShippingOrderDTO -> new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO.getTradeChannelType(), tradeShippingOrderDTO.getTradeOrderNo()));
        //查订单数据
        Map<TradeOrderInfoComponent.TradeOrderKey, OCMSOrderVO> ocmsOrderMap = tradeOrderInfoComponent.queryTradeOrderInfoListByPartition(allTradeOrderKeys);

        //查运单数据
        List<TRiderDeliveryOrder> tRiderDeliveryOrders = deliveryOrderComponent.queryAllDeliveryOrderByTradeOrderIds(IListUtils.mapTo(ocmsOrderMap.values(), OCMSOrderVO::getOrderId));
        Map<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> tRiderDeliveryOrderMap = IListUtils.nullSafeAndOverrideCollectToMap(tRiderDeliveryOrders, tRiderDeliveryOrder -> new TradeOrderInfoComponent.TradeOrderKey(tRiderDeliveryOrder.getOrderBizTypeCode(), tRiderDeliveryOrder.getChannelOrderId()), Function.identity());

        //内存分页
        tPickingOrders.sort(
                (tradeShippingOrderDTO1, tradeShippingOrderDTO2) -> {
                    TRiderDeliveryOrder tRiderDeliveryOrder1 = tRiderDeliveryOrderMap.get(new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO1.getTradeChannelType(), tradeShippingOrderDTO1.getTradeOrderNo()));
                    TRiderDeliveryOrder tRiderDeliveryOrder2 = tRiderDeliveryOrderMap.get(new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO2.getTradeChannelType(), tradeShippingOrderDTO2.getTradeOrderNo()));
                    if (Objects.isNull(tRiderDeliveryOrder1) || Objects.isNull(tRiderDeliveryOrder2)) {
                        return 0;
                    }
                    return (int) (tRiderDeliveryOrder1.getCreateTime() - tRiderDeliveryOrder2.getCreateTime());
                }
        );

        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, PickingOrderVO> pickingOrderVOMap = IListUtils.nullSafeStream(tPickingOrders)
                .collect(Collectors.toMap(
                        tradeShippingOrderDTO -> new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO.getTradeChannelType(), tradeShippingOrderDTO.getTradeOrderNo()),
                        tradeShippingOrderDTO -> pickingOrderComponent.buildPickingOrderVO(tradeShippingOrderDTO),
                        (o1, o2) -> o1, LinkedHashMap::new
                ));

        //build运单模块
        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, DeliveryOrderVO> deliveryOrderVOMap = new LinkedHashMap<>();
        tRiderDeliveryOrderMap.forEach((key, value) -> {
            deliveryOrderVOMap.putIfAbsent(key, deliveryOrderComponent.buildDeliveryOrderVO(value, ocmsOrderMap.get(key), true));
        });

        //build订单模块
        Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap = ocmsOrderMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> tradeOrderInfoComponent.buildTradeOrderVO(entry.getValue()),
                        (older, newer) -> newer
                ));
        aggregateContext.appendTradeOrderVOMap(tradeOrderVOMap);

        //配送风控单标签 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, TDeliveryRiskControlOrder> deliveryRiskControlOrderMap = new HashMap<>();
        try {
            deliveryRiskControlOrderMap = deliveryRiskControlComponent.queryRiskControlOrderMap(new ArrayList<>(deliveryOrderVOMap.keySet()));
        } catch (Exception e) {
            log.error("查风控单标签失败", e);
        }

        List<RiderPickingOrderVO> riderDeliveryOrderVOS =
                RiderDeliveryConvert.buildBaseRiderPickingOrderVOList(pickingOrderVOMap, deliveryOrderVOMap,
                        tradeOrderVOMap,
                        deliveryRiskControlOrderMap
                );

        return riderDeliveryOrderVOS;
    }

    private void sendCompensateForDeliveryMessage(TradeShippingOrderDTO tradeShippingOrderDTO) {
        try {
            compensateOrderProducer.sendMessage(JSON.toJSONString(buildCompensateMessageFromShippingOrder(tradeShippingOrderDTO)));
        } catch (Exception e) {
            log.error("compensateOrderProducer.sendMessage error", e);
        }
    }


    private CompensateFulfillmentOrderMessage buildCompensateMessageFromShippingOrder(TradeShippingOrderDTO tradeShippingOrderDTO) {
        CompensateFulfillmentOrderMessage message = new CompensateFulfillmentOrderMessage();
        message.setMerchantId(tradeShippingOrderDTO.getMerchantId());
        message.setWarehouseId(tradeShippingOrderDTO.getWarehouseId());
        message.setTradeOrderNo(tradeShippingOrderDTO.getTradeOrderNo());
        message.setTradeOrderBizType(tradeShippingOrderDTO.getTradeChannelType());
        message.setCompensateForPickType(CompensateType.COMPENSATE_FOR_NEW_DELIVERY.getCode());
        return message;
    }

    private List<RiderPickingOrderVO> filterSplitOrders(List<RiderPickingOrderVO> orders, FilterSpilitTypeEnum filterSpilitTypeEnum) {
        switch (filterSpilitTypeEnum) {
            case KEEP_SPLIT:
                return orders.stream()
                        .filter(order -> Objects.nonNull(order.getIsPickDeliverySplit()) && order.getIsPickDeliverySplit())
                        .collect(Collectors.toList());
            case KEEP_NOT_SPLIT:
                return orders.stream()
                        .filter(order -> Objects.isNull(order.getIsPickDeliverySplit()) || !order.getIsPickDeliverySplit())
                        .collect(Collectors.toList());
            case NO_FILTER:
            default:
                return orders;
        }
    }

    private List<RiderPickingOrderVO> filterThirdPick(List<RiderPickingOrderVO> orders) {
        return orders.stream()
                .filter(order -> Objects.isNull(order.getIsThirdDelivery()) || !order.getIsThirdDelivery())
                .collect(Collectors.toList());
    }

    private List<TradeOrderInfoComponent.TradeOrderKey> getAllTradeOrderKeys(List<RiderPickingOrderVO> orders) {
        return orders.stream()
                .map(order -> new TradeOrderInfoComponent.TradeOrderKey(DynamicOrderBizType.channelId2OrderBizType(order.getChannelId()).getValue(), order.getChannelOrderId()))
                .collect(Collectors.toList());
    }

    private void sortByDoneTime(List<RiderPickingOrderVO> orders) {
        orders.sort((o1, o2) -> {
            try {
                return (int) (o2.getPickDoneTime() - o1.getPickDoneTime());
            } catch (Exception e) {
                return (int) (o2.getCreateTime() - o1.getCreateTime());
            }
        });
    }




}
