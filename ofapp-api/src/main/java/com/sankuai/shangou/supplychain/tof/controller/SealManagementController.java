package com.sankuai.shangou.supplychain.tof.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.request.CheckSealCodeIsUsableRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.request.QueryOrderPickingDetailsRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.request.ReturnSealContainerRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.seal.UsingSealContainerVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.seal.request.QueryUsingSealContainerRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.seal.response.CheckSealCodeResponse;
import com.sankuai.shangou.supplychain.tof.service.SealManagementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024-06-27
 * @email <EMAIL>
 */
@InterfaceDoc(
        displayName = "容具管理相关接口",
        type = "restful",
        scenarios = "容具管理相关接口",
        description = "容具管理相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@RestController
@RequestMapping("/api/orderfulfill/app/rider/seal-container")
public class SealManagementController {

    @Resource
    private SealManagementService sealManagementService;


    @MethodDoc(
            displayName = "查询容具是否被占用",
            description = "查询容具是否被占用",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询容具是否被占用请求",
                            type = QueryOrderPickingDetailsRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/picking/waitpickdetail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/checkIsUsable", method = {RequestMethod.POST})
    @ResponseBody
    public CheckSealCodeResponse checkIsUsable(@Valid @RequestBody CheckSealCodeIsUsableRequest request) {
        return  sealManagementService.checkIsUsable(request);
    }

    @MethodDoc(
            displayName = "归还封签容具",
            description = "归还封签容具",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "归还封签容具",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/seal-container/return",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/return", method = {RequestMethod.POST})
    @ResponseBody
    public void returnSealContainer(@RequestBody ReturnSealContainerRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }
        sealManagementService.returnSealContainer(request);
    }


    @MethodDoc(
            displayName = "查看门店待归还封签容具列表",
            description = "查看门店待归还封签容具列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查看门店待归还封签容具列表",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/seal-container/usingContainerList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/usingContainerList", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> usingContainerList(@RequestBody QueryUsingSealContainerRequest request) {
        List<UsingSealContainerVO> usingSealContainerVOS = sealManagementService.queryUsingSealContainerList(request.getOnlyShowCurrRiderRecord());
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("usingContainerList", usingSealContainerVOS);
        resultMap.put("currRiderUsingContainerCount", usingSealContainerVOS.stream().filter(vo -> Objects.equals(LoginContextUtils.getAppLoginAccountId(), vo.getRiderAccountId())).map(UsingSealContainerVO::getSealContainerCode).mapToLong(Collection::size).sum());
        resultMap.put("totalUsingContainerCount", usingSealContainerVOS.stream().map(UsingSealContainerVO::getSealContainerCode).mapToLong(Collection::size).sum());
        return resultMap;
    }

}
