package com.sankuai.shangou.supplychain.tof.controller.vo.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.supplychain.tof.controller.vo.SortedOrderTagVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 返回给前端的骑手配送单VO
 *
 * <AUTHOR>
 * @since 2021/6/11 15:37
 */
@TypeDoc(
        description = "返回给前端的骑手配送单VO"
)
@Data
@EqualsAndHashCode(callSuper = true)
public class RiderDeliveryOrderVO extends AggregateOrderVO {

    @FieldDoc(
            description = "运单ID", requiredness = Requiredness.REQUIRED
    )

    private Long deliveryOrderId;

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryChannelId;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    private String channelName;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    private String storeName;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    private String channelOrderId;

    @FieldDoc(
            description = "赋能订单号", requiredness = Requiredness.REQUIRED
    )
    private Long empowerOrderId;

    @FieldDoc(
            description = "订单流水", requiredness = Requiredness.REQUIRED
    )
    private Long serialNo;

    @FieldDoc(
            description = "配送订单类型  1-立即单 2-预约单", requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryOrderType;

    @FieldDoc(
            description = "配送状态 30-待领取 40-骑手已接单 50-骑手已取货", requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryStatus;

    @FieldDoc(
            description = "拣货状态", requiredness = Requiredness.REQUIRED
    )
    private Integer pickStatus;

    @FieldDoc(
            description = "配送订单类型名称", requiredness = Requiredness.REQUIRED
    )
    private String deliveryOrderTypeName;

    @FieldDoc(
            description = "预计送达时间开始时间", requiredness = Requiredness.REQUIRED
    )
    private Long estimateArriveTimeStart;

    @FieldDoc(
            description = "预计送达时间截止时间", requiredness = Requiredness.REQUIRED
    )
    private Long estimateArriveTimeEnd;

    @FieldDoc(
            description = "订单创建时间戳", requiredness = Requiredness.REQUIRED
    )
    private Long createTime;

    @FieldDoc(
            description = "拣货下发时间", requiredness = Requiredness.REQUIRED
    )
    private Long pickPushDownTime;

    @FieldDoc(
            description = "配送方式,0-未知，1-配送到家，2-到店自提", requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryMethod;

    @FieldDoc(
            description = "改派前的骑手姓名", requiredness = Requiredness.OPTIONAL
    )
    private String fromRiderName;

    @FieldDoc(
            description = "配送方式描述", requiredness = Requiredness.REQUIRED
    )
    private String deliveryMethodDesc;

    @FieldDoc(
            description = "订单用户ID、0或-1为无效ID", requiredness = Requiredness.OPTIONAL
    )

    private Long userId;

    // 收货人信息 start

    @FieldDoc(
            description = "收货人姓名", requiredness = Requiredness.REQUIRED
    )

    private String receiverName;

    @FieldDoc(
            description = "收货人电话号码", requiredness = Requiredness.REQUIRED
    )

    private String receiverPhone;

    @FieldDoc(
            description = "收货人地址", requiredness = Requiredness.REQUIRED
    )

    private String receiverAddress;

    @FieldDoc(
            description = "收货人定位经度，精确到小数点后六位", requiredness = Requiredness.REQUIRED
    )

    private String receiverLongitude;

    @FieldDoc(
            description = "收货人定位纬度，精确到小数点后六位", requiredness = Requiredness.REQUIRED
    )

    private String receiverLatitude;

    // 收货人信息 end

    // 商品信息 start

    @FieldDoc(
            description = "商品总数量", requiredness = Requiredness.REQUIRED
    )
    private Integer itemCount;

    @FieldDoc(
            description = "备注", requiredness = Requiredness.OPTIONAL
    )
    private String comments;

    @FieldDoc(
            description = "商品信息列表", requiredness = Requiredness.REQUIRED
    )
    private List<ProductVO> productList;

    @FieldDoc(
            description = "订单营收数据", requiredness = Requiredness.OPTIONAL
    )
    private RevenueDetailVo revenueDetail;

    @FieldDoc(
            description = "提报价模式,订单线下价格总和", requiredness = Requiredness.OPTIONAL
    )
    private Integer totalOfflinePrice;

    @FieldDoc(
            description = "赠品信息列表", requiredness = Requiredness.OPTIONAL
    )
    private List<GiftVO> giftVOList;

    @FieldDoc(
            description = "赠品总数量", requiredness = Requiredness.OPTIONAL
    )
    private Integer giftCount;

    @FieldDoc(
            description = "礼袋信息", requiredness = Requiredness.OPTIONAL
    )

    private List<GiftBagVO> giftBagList;

    // 商品信息 end

    @FieldDoc(
            description = "支付时间", requiredness = Requiredness.OPTIONAL
    )
    private Long payTime;


    @FieldDoc(
            description = "用户标签信息", requiredness = Requiredness.OPTIONAL
    )
    private List<TagInfoVO> userTags;

    @FieldDoc(
            description = "用户类型信息 10-普通用户, 15-会员用户", requiredness = Requiredness.OPTIONAL
    )
    private Integer orderUserType;

    @FieldDoc(
            description = "运单状态是否被锁定：0-未锁定；1-锁定",
            example = {}
    )

    public Integer deliveryStatusLocked;

    @FieldDoc(
            description = "运单状态是否可以被锁定：0-不可以；1-可以",
            example = {}
    )

    public Integer canStatusBeLocked;

    @FieldDoc(
            description = "骑手上报异常",
            example = {}
    )
    public DeliveryExceptionSummaryVO deliveryExceptionSummaryVOS;

    @FieldDoc(
            description = "考核送达截止时间", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveDeadline;

    @FieldDoc(
            description = "考核送达截止时间-拣货相关页面用", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveDeadlineForPick;

    @FieldDoc(
            description = "考核送达剩余时间", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveLeftTime;

    @FieldDoc(
            description = "考核送达剩余时间-拣货相关页面用", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveLeftTimeForPick;

    @FieldDoc(
            description = "考核送达超时时间", requiredness = Requiredness.OPTIONAL
    )

    private Long evaluateArriveTimeout;

    @FieldDoc(
            description = "考核送达超时时间", requiredness = Requiredness.OPTIONAL
    )

    private Long evaluateArriveTimeoutForPick;

    @FieldDoc(
            description = "配送距离，单位米", requiredness = Requiredness.OPTIONAL
    )

    private Long deliveryDistance;


    @FieldDoc(
            description = "是否需要填写问卷", requiredness = Requiredness.OPTIONAL
    )

    private Boolean isNeedAnswerQuestionnaire;

    @FieldDoc(
            description = "是否有缺货情况", requiredness = Requiredness.OPTIONAL
    )

    private Boolean hasLackGoods;

    @FieldDoc(
            description = "代收点", requiredness = Requiredness.OPTIONAL
    )

    private String signingPoint;

    @FieldDoc(
            description = "是否包含高价值商品", requiredness = Requiredness.OPTIONAL
    )

    private Boolean isContainsHighWaxGoods;


    @FieldDoc(
            description = "配送完成时间", requiredness = Requiredness.OPTIONAL
    )

    private Long deliveryDoneTime;


    @FieldDoc(
            description = "是否是风控单", requiredness = Requiredness.OPTIONAL
    )

    private Boolean isRiskControlOrder;

    @FieldDoc(
            description = "是否是一元单", requiredness = Requiredness.OPTIONAL
    )

    private Boolean isOneYuanOrder;

    @FieldDoc(
            description = "场景，如餐馆", requiredness = Requiredness.OPTIONAL
    )

    private String scene;

    @FieldDoc(
            description = "可以展示的按钮列表,100-转青云配送 110-转自配", requiredness = Requiredness.OPTIONAL
    )
    private List<Integer> couldOperateItemList;

    @FieldDoc(
            description = "订单是否是封签交付", requiredness = Requiredness.OPTIONAL
    )
    private Boolean isSealDelivery;

    @FieldDoc(
            description = "定价路线信息",
            example = {}
    )
    public RouteInfoVO routeInfo;

    @FieldDoc(
            description = "展示标签，包括 大范围标签等", requiredness = Requiredness.OPTIONAL
    )
    private List<ShowTagVO> showTags;

    @FieldDoc(
            description = "收货人后4位尾号", requiredness = Requiredness.OPTIONAL
    )
    private String receiverTailPhoneNumber;

    @FieldDoc(
            description = "订单是否需要展示奖励文案",
            example = {}
    )
    public Boolean showRewardHint = false;


    @FieldDoc(
            description = "是否拣货完成",
            example = {}
    )
    private Boolean pickFinished = false;

    @FieldDoc(
            description = "是否拣配分离订单",
            example = {}
    )
    private Boolean isPickDeliverySplit = false;

    @FieldDoc(
            description = "是否三方配送单",
            example = {}
    )
    private Boolean isThirdDelivery = false;

    @FieldDoc(
            description = "是否三方异常单",
            example = {}
    )
    private Boolean isThirdException = false;

    @FieldDoc(
            description = "拣货完成时间",
            example = {}
    )
    private Long pickDoneTime;

    @FieldDoc(
            description = "取货信息",
            example = {}
    )
    private TakenGoodsInfo takenGoodsInfo;

    @FieldDoc(
            description = "货品项列表",
            example = {}
    )
    private List<GoodsItemVO> goodsItemList;

    @FieldDoc(
            description = "是否需要红酒开瓶器",
            example = {}
    )
    private Boolean needWineBottleOpener;

    @FieldDoc(
            description = "状态",
            example = {}
    )
    private Integer status;

    @FieldDoc(
            description = "聚合运力平台code", requiredness = Requiredness.OPTIONAL
    )
    private Integer deliveryPlatformCode;

    @FieldDoc(
            description = "聚合运力平台描述", requiredness = Requiredness.OPTIONAL
    )
    private String deliveryPlatformDesc;

    @FieldDoc(
            description = "聚合运力配送模块跳转信息", requiredness = Requiredness.OPTIONAL
    )
    private DeliveryRedirectModuleVo deliveryRedirectModule;

    //历史上这里是三方的叫这个，而自配的是couldOperateItems，待整合
    @FieldDoc(
            description = "订单可操作列表：10-接单操作，20-完成拣货，30-补打小票，40-全单退款，50-部分退款，60-收到退货 ，70-退差价", requiredness = Requiredness.OPTIONAL
    )
    private List<Integer> orderCouldOperateItems;

    @FieldDoc(
            description = "配送状态描述,三方用", requiredness = Requiredness.REQUIRED
    )
    private String distributeStatusDesc;

    @FieldDoc(
            description = "是否为美团名酒馆订单，true：是"
    )
    private Boolean isMtFamousTavern;

    @FieldDoc(
            description = "是否为发财酒订单，true：是"
    )
    private Boolean isFacaiWine;

    @FieldDoc(
            description = "发财酒二维码剩余有效时间"
    )
    private Long facaiQrLeftTime;

    @FieldDoc(
            description = "额外商品列表，目前主要是发财酒"
    )
    private List<ExternalProductVO> externalProductList;

    @FieldDoc(
            description = "是否为取货后的转移库存"
    )
    private Boolean isAfterRiderTakenTransfer = false;

    @FieldDoc(
            description = "放置位置，【标签】"
    )
    private String deliveryPosition;

    @FieldDoc(
            description = "排序好的订单标签"
    )
    private List<SortedOrderTagVO> sortedTagList;


    @FieldDoc(
            description = "配送可操作按钮"
    )
    private List<Integer> deliveryOperateItems;
}
