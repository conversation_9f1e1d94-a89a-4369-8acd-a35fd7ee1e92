package com.sankuai.shangou.supplychain.tof.component;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.goodscenter.dto.GoodsExpirationDto;
import com.meituan.shangou.goodscenter.request.DepotGoodsBatchQueryRequest;
import com.meituan.shangou.goodscenter.response.DepotGoodsDetailListResponse;
import com.meituan.shangou.goodscenter.thrift.DepotGoodsThriftService;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/4/28 15:55
 **/
@Slf4j
@Rhino
public class GoodsInfoComponent {
    @Resource
    private DepotGoodsThriftService depotGoodsThriftService;

    @Degrade(rhinoKey = "GoodsInfoComponent.queryGoodsInfo", fallBackMethod = "queryGoodsInfoFallback", timeoutInMilliseconds = 2000)
    public Map<String, DepotGoodsDetailDto> queryGoodsInfo(TradeShippingOrderDTO tradeShippingOrderDTO) {
        List<String> skuIds = tradeShippingOrderDTO.getItems().stream()
                .map(TradeShippingOrderItemDTO::getSkuId)
                .distinct()
                .collect(Collectors.toList());
        DepotGoodsBatchQueryRequest request = new DepotGoodsBatchQueryRequest();
        request.setGoodsIdList(skuIds);
        request.setDepotId(LoginContextUtils.getAppLoginStoreId());
        request.setTenantId(LoginContextUtils.getAppLoginTenant());
        DepotGoodsDetailListResponse response;
        try {
            log.info("start invoke depotGoodsThriftService.batchQueryDepotGoodsListByGoodsId, request: {}", request);
            response = depotGoodsThriftService.batchQueryDepotGoodsListByGoodsId(request);
            log.info("end invoke depotGoodsThriftService.batchQueryDepotGoodsListByGoodsId, response: {}", response);
        } catch (Exception e) {
            throw new ThirdPartyException("查询货品信息失败");
        }

        if (!response.success()) {
            throw new BizException("查货品信息失败");
        }

        return response.getData().stream().collect(Collectors.toMap(DepotGoodsDetailDto::getGoodsId, Function.identity(), (k1, k2) -> k2));
    }

    public Map<String, DepotGoodsDetailDto> queryGoodsInfoFallback(TradeShippingOrderDTO tradeShippingOrderDTO) {
        log.error("GoodsInfoComponent.queryGoodsInfo 发生降级");
        return Collections.emptyMap();
    }

}
