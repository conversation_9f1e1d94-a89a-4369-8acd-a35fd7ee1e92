package com.sankuai.shangou.supplychain.tof.component;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.request.revenue.MerchantOrderRevenueDetailRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.MerchantOrderListRevenueDetailResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderAmountInfo;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderRevenueDetailResponse;
import com.meituan.shangou.saas.order.management.client.enums.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.service.revenue.MerChantRevenueQueryService;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.RevenueDetailVo;
import com.sankuai.shangou.supplychain.tof.enums.AuthCodeEnum;
import com.sankuai.shangou.supplychain.tof.wrapper.AuthThriftWrapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-12-18
 * @email <EMAIL>
 */
@Slf4j
@Rhino
public class RevenueComponent {

    @Resource
    private AuthThriftWrapper authThriftWrapper;
    @Resource
    private MerChantRevenueQueryService merChantRevenueQueryService;


    @Degrade(rhinoKey = "RevenueComponent.revenueComponent", fallBackMethod = "revenueComponentFallback", timeoutInMilliseconds = 2000)
    public Map<TradeOrderInfoComponent.TradeOrderKey, RevenueDetailVo> revenueComponent(long tenantId, List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList) {
        Map<String, Boolean> permissions = authThriftWrapper.isHasPermission(ImmutableList.of(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode()));
        boolean showSalePrice = permissions.getOrDefault(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode(), Boolean.FALSE);
        // 查询订单营收
        List<OrderRevenueDetailResponse> orderRevenueDetailList = showSalePrice ? getOrderListRevenueDetail(tenantId, tradeOrderKeyList) : Collections.emptyList();
        if (CollectionUtils.isEmpty(orderRevenueDetailList)) {
            return Maps.newHashMap();
        }
        return orderRevenueDetailList
                .stream()
                .collect(Collectors.toMap(
                        it -> new TradeOrderInfoComponent.TradeOrderKey(it.getOrderBizType(), it.getOrderViewId()),
                        this::convertFromDTO,
                        (older, newer) -> newer
                ));
    }

    public Map<TradeOrderInfoComponent.TradeOrderKey, RevenueDetailVo> revenueComponentFallback(long tenantId, List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList) {
        log.error("RevenueComponent.revenueComponent 发生降级");
        return Collections.emptyMap();
    }

    public RevenueDetailVo convertFromDTO(OrderRevenueDetailResponse orderRevenueDetailDTO) {
        OrderAmountInfo orderAmountInfo = orderRevenueDetailDTO.getOrderAmountInfo();
        RevenueDetailVo vo = new RevenueDetailVo();
        vo.setPromotionInfos(orderRevenueDetailDTO.getPromotionInfos());
        vo.setActualPayAmount(orderAmountInfo.getActualPayAmt());
        vo.setBizActivityAmount(orderAmountInfo.getBizCharge());
        vo.setDeliveryAmount(orderAmountInfo.getDeliveryFee());
        vo.setPackageAmount(orderAmountInfo.getPackageAmount());
        vo.setRevenueAmount(orderAmountInfo.getBizReceiveAmount());
        vo.setFastDeliveryAmt(orderAmountInfo.getFastDeliveryAmt());
        return vo;
    }

    /**
     * 查询订单营收信息.
     *
     * @param tenantId      租户 ID
     * @param tradeOrderKeyList 订单列表
     * @return List<OrderRevenueDetailResponse>
     */
    private List<OrderRevenueDetailResponse> getOrderListRevenueDetail(long tenantId, List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList) {
        if (CollectionUtils.isEmpty(tradeOrderKeyList)) {
            return Collections.emptyList();
        }

        MerchantOrderRevenueDetailRequest request = new MerchantOrderRevenueDetailRequest();
        request.setTenantId(tenantId);
        List<ViewIdCondition> conditions = tradeOrderKeyList.stream().map(order -> ViewIdCondition.builder()
                        .orderBizType(order.getOrderBizType())
                        .viewOrderId(order.getChannelOrderId()).build())
                .collect(Collectors.toList());
        request.setViewIdConditionList(conditions);

        try {
            log.info("RiderDeliveryServiceWrapper call MerChantRevenueQueryService.orderRevenueDetail request:{}", request);
            MerchantOrderListRevenueDetailResponse response = merChantRevenueQueryService
                    .orderListRevenueDetail(request);
            log.info("RiderDeliveryServiceWrapper call MerChantRevenueQueryService.orderRevenueDetail esponse:{}", response);
            if (response == null) {
                log.warn("RiderDeliveryServiceWrapper call MerChantRevenueQueryService.orderRevenueDetail wrong. request:{}, response:{}"
                        , request, response);
                return Collections.emptyList();
            }
            if (Integer.valueOf(StatusCodeEnum.SUCCESS.getCode()).equals(response.getStatus())) {
                List<OrderRevenueDetailResponse> orderListRevenueDetailResponse = response.getOrderListRevenueDetailResponse();
                if (orderListRevenueDetailResponse == null) {
                    return Collections.emptyList();
                }
                return orderListRevenueDetailResponse;
            } else {
                log.warn("RiderDeliveryServiceWrapper call MerChantRevenueQueryService.orderRevenueDetail fail. request:{}, response:{}"
                        , request, response);
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.warn("RiderDeliveryServiceWrapper call MerChantRevenueQueryService.orderRevenueDetail error. request:{}", request);
            return Collections.emptyList();
        }
    }

}
