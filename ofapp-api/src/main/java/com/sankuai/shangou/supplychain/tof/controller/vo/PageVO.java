package com.sankuai.shangou.supplychain.tof.controller.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.rider.client.common.TPageInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-12-21
 * @email <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageVO {

    @FieldDoc(
            description = "页码", requiredness = Requiredness.OPTIONAL
    )
    private int page;

    @FieldDoc(
            description = "总大小", requiredness = Requiredness.OPTIONAL
    )
    private int totalSize;

    @FieldDoc(
            description = "总页数", requiredness = Requiredness.OPTIONAL
    )
    private int totalPage;


    public PageVO(TPageInfo pageInfo) {
        this.page = pageInfo.getPage();
        this.totalSize = pageInfo.getTotal();
        this.totalPage = ((pageInfo.getTotal() - 1) / pageInfo.getPageSize()) + 1;
    }
}
