package com.sankuai.shangou.supplychain.tof.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.supplychain.tof.assembler.ThirdDeliveryPickingAssembler;
import com.sankuai.shangou.supplychain.tof.controller.vo.PageQueryRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.third.request.DrunkHorseTurnToMerchantSelfDeliveryRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.third.response.ThirdDeliveryPickingOrderListResponse;
import com.sankuai.shangou.supplychain.tof.service.RiderDeliveryOperateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/8/14 17:49
 **/

@InterfaceDoc(
        displayName = "三方配送相关接口",
        type = "restful",
        scenarios = "三方配送相关接口",
        description = "三方配送相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@RestController
@RequestMapping("/api/orderfulfill/app/rider/third")
public class ThirdDeliveryController {

    @Resource
    private ThirdDeliveryPickingAssembler thirdDeliveryPickingAssembler;

    @Resource
    private RiderDeliveryOperateService riderDeliveryOperateService;

    @MethodDoc(
            displayName = "分页查询待领取的拣货任务列表",
            description = "分页查询待领取的拣货任务列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询待领取的拣货任务列表",
                            type = PageQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/third/queryWaitToTake",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryWaitToTake", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public ThirdDeliveryPickingOrderListResponse queryWaitToTake(@Valid @RequestBody PageQueryRequest request) {
        return thirdDeliveryPickingAssembler.queryWaitToTake(request.getPage(), request.getPageSize());
    }

    @MethodDoc(
            displayName = "分页查询待拣货的拣货任务列表",
            description = "分页查询待拣货的拣货任务列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询待领取的拣货任务列表",
                            type = PageQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/third/queryWaitToPick",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryWaitToPick", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public ThirdDeliveryPickingOrderListResponse queryWaitToPick(@Valid @RequestBody PageQueryRequest request) {
        return thirdDeliveryPickingAssembler.queryWaitToPick(request.getPage(), request.getPageSize());
    }

    @MethodDoc(
            displayName = "转自配",
            description = "转自配",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "转自配",
                            type = PageQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/rider/third/turnToSelfDelivery",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/turnToSelfDelivery", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public void turnToSelfDelivery(@Valid @RequestBody DrunkHorseTurnToMerchantSelfDeliveryRequest request) {
        riderDeliveryOperateService.turnToMerchantSelfDeliveryForDrunkHorse(request.getOrderId(), request.getRiderAccountId());
    }
}
