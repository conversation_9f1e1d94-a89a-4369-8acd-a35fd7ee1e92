package com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "查询配送异常明细请求"
)
@ApiModel("查询配送异常明细请求")
@Data
public class QueryExceptionDetailRequest {
    @FieldDoc(
            description = "渠道id",
            requiredness = Requiredness.OPTIONAL
    )
    @NotNull(message = "渠道id不能为空")
    @ApiModelProperty(value = "渠道id")
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单id",
            requiredness = Requiredness.REQUIRED
    )
    @NotNull(message = "渠道订单id不能为空")
    @ApiModelProperty(value = "渠道订单id")
    private String channelOrderId;
}