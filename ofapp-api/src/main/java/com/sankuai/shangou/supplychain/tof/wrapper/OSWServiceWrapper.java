package com.sankuai.shangou.supplychain.tof.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.infra.osw.api.org.TEmployeeService;
import com.sankuai.shangou.infra.osw.api.org.dto.request.AccountIdsRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.request.EmpIdsRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import com.sankuai.shangou.infra.osw.api.poi.TPoiService;
import com.sankuai.shangou.infra.osw.api.poi.dto.model.PoiDTO;
import com.sankuai.shangou.infra.osw.api.poi.dto.request.QueryOperatePoiRequest;
import com.sankuai.shangou.infra.osw.api.poi.dto.request.QueryPoiByIdsRequest;
import com.sankuai.shangou.infra.osw.api.poi.dto.response.BusinessPoiDTO;
import com.sankuai.shangou.infra.osw.api.poi.store.dto.request.WarehouseIdsRequest;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.TWarehouseService;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/12/20 17:38
 **/
@Rhino
@Slf4j
public class OSWServiceWrapper {
    @Resource
    private TPoiService oswPoiService;

    @Resource
    private TEmployeeService tEmployeeService;

    @Resource
    private TWarehouseService tWarehouseService;

    public static final int DIRECT_OPERATION_MODE = 1;

    @Degrade(rhinoKey = "OSWServiceWrapper.queryOperatePoiByPoiId", fallBackMethod = "queryOperatePoiByPoiIdFallBack", timeoutInMilliseconds = 2000)
    public BusinessPoiDTO queryOperatePoiByPoiId(Long tenantId, Long storeId, Long accountId) {
        QueryOperatePoiRequest request = new QueryOperatePoiRequest();
        request.setPoiId(storeId);
        request.setTenantId(tenantId);
        request.setAccountId(accountId);
        TResult<BusinessPoiDTO> result;
        try {
            log.info("start invoke OSWServiceWrapper.queryOperatePoiByPoiId, request: {}", request);
            result = oswPoiService.queryOperatePoiByPoiId(request);
            log.info("end invoke OSWServiceWrapper.queryOperatePoiByPoiId, response: {}", result);
        } catch (Exception e) {
            throw new ThirdPartyException(e);
        }

        if (!result.isSuccess()) {
            throw new ThirdPartyException("查询店仓基础信息失败");
        }

        return result.getData();
    }

    @Degrade(rhinoKey = "OSWServiceWrapper.queryWarehouseByWarehouseId", fallBackMethod = "queryWarehouseByWarehouseIdFallback", timeoutInMilliseconds = 2000)
    public WarehouseDTO queryWarehouseByWarehouseId(Long tenantId, Long storeId) {
        WarehouseIdsRequest request = new WarehouseIdsRequest();
        request.setWarehouseIds(Lists.newArrayList(storeId));
        request.setTenantId(tenantId);
        TResult<List<WarehouseDTO>> result;
        try {
            log.info("start invoke OSWServiceWrapper.queryOperatePoiByPoiId, request: {}", request);
            result = tWarehouseService.batchQueryWarehouseById(request);
            log.info("end invoke OSWServiceWrapper.queryOperatePoiByPoiId, response: {}", result);
        } catch (Exception e) {
            throw new ThirdPartyException(e);
        }

        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            throw new ThirdPartyException("查询店仓基础信息失败");
        }

        return result.getData().get(0);
    }

    @Degrade(rhinoKey = "OSWServiceWrapper.queryEmpByAccountIds", fallBackMethod = "queryEmpByAccountIdsFallBack", timeoutInMilliseconds = 2000)
    public List<EmployeeDTO> queryEmpByAccountIds(Long tenantId, List<Long> accountIds) {
        AccountIdsRequest request = new AccountIdsRequest();
        request.setAccountIds(accountIds);
        request.setTenantId(tenantId);
        TResult<List<EmployeeDTO>> result;
        try {
            log.info("start invoke tEmployeeService.queryEmpByAccountIds, request: {}", request);
            result = tEmployeeService.queryEmpByAccountIds(request);
            log.info("end invoke tEmployeeService.queryEmpByAccountIds, response: {}", result);
        } catch (Exception e) {
            log.info("invoke tEmployeeService.queryEmpByAccountIds error", e);
            throw new ThirdPartyException(e);
        }

        if (!result.isSuccess()) {
            throw new BizException("查询店仓基础信息失败:" + result.getMsg());
        }

        return result.getData();
    }


    public Map<Long, EmployeeDTO> queryEmpByIds(Long tenantId, List<Long> employeeIds) {
        if (CollectionUtils.isEmpty(employeeIds)) {
            return Collections.emptyMap();
        }

        EmpIdsRequest request = new EmpIdsRequest();
        request.setEmpIds(employeeIds);
        request.setTenantId(tenantId);
        TResult<List<EmployeeDTO>> result;
        try {
            log.info("start invoke tEmployeeService.queryEmpByIds, request: {}", request);
            result = tEmployeeService.queryEmpByIds(request);
            log.info("end invoke tEmployeeService.queryEmpByIds, response: {}", result);
        } catch (Exception e) {
            log.info("invoke tEmployeeService.queryEmpByIds error", e);
            throw new ThirdPartyException(e);
        }

        if (!result.isSuccess()) {
            throw new BizException("查询员工信息失败:" + result.getMsg());
        }

        return result.getData().stream().collect(Collectors.toMap(EmployeeDTO::getEmpId, Function.identity(), (k1, k2) -> k2));
    }


    public BusinessPoiDTO queryOperatePoiByPoiIdFallBack(Long tenantId, Long storeId, Long accountId) {
        log.warn("OSWServiceWrapper.queryOperatePoiByPoiId 发生降级");
        return null;
    }

    public List<EmployeeDTO> queryEmpByAccountIdsFallBack(Long tenantId, List<Long> accountIds) {
        log.warn("OSWServiceWrapper.queryEmpByAccountIds 发生降级");
        return Collections.emptyList();
    }

    public PoiDTO queryWarehouseByWarehouseIdFallback(Long tenantId, Long storeId) {
        log.warn("OSWServiceWrapper.queryWarehouseByWarehouseId 发生降级");
        return null;
    }
}
