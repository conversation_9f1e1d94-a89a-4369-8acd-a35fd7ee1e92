package com.sankuai.shangou.supplychain.tof.advisor;

import com.dianping.cat.Cat;
import com.meituan.mtrace.Tracer;
import com.sankuai.shangou.commons.exception.common.code.CodeUtils;
import com.sankuai.shangou.commons.exception.common.code.ExceptionCategory;
import com.sankuai.shangou.commons.exception.common.code.IExceptionCodeData;
import com.sankuai.shangou.commons.exception.common.code.PreDefineCode;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Maps;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 处理controller接口返回的数据，会默认加上code和msg，并把接口返回数据设置到data中。也会把异常的code、msg、data返回给前端。
 *
 * <p>用法：只需要继承ResponseBodyWithCodeMsgAdvisor并增加@RestControllerAdvice，根据需要限制basePackages和使用@ExceptionHandler对特定异常的处理。
 *
 * <p>所有方法都可以被重载
 *
 * <p>注意：目前只有正常请求才会到走到{@link #supports},@ExceptionHandler有异常时直接给前端回包
 *
 * <p>其他：多个RestControllerAdvice，如果有一个没有实现ResponseBodyAdvice，似乎会导致在异常情况下也会走到beforeBodyWrite()中。
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice(basePackages = {"com.sankuai.shangou.supplychain.tof.controller"})
public class ResponseBodyWithCodeMsgAdvisor implements ResponseBodyAdvice {

    private static final Integer DEFAULT_EXCEPTION =
            CodeUtils.genFullCode(ExceptionCategory.SYSTEM_INTERNAL, PreDefineCode.SYSTEM_INTERNAL);

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        // 如果需要支持其他类型，此处判断即可
        boolean result = converterType == MappingJackson2HttpMessageConverter.class;
        log.debug("supports, return:{}, convert:{} result: {}", returnType, converterType, result);
        return result;
    }

    @Override
    public Object beforeBodyWrite(
            Object body, MethodParameter returnType, MediaType selectedContentType,
            Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        log.debug(" beforeBodyWrite, body:{}, returnType:{}, content:{}, convert:{}", body, returnType,
                selectedContentType, selectedConverterType);
        log.debug("httpServletRequest {}  httpServletResponse {}", request, response);
        Result<Object> result = new Result<>(0, null,null, body);
        log.debug("beforeBodyWrite, body:{}, result {}", body, result);
        catReport(result, request.getURI().getPath());
        return result;
    }

    @ExceptionHandler(Exception.class)
    public Result<Object> onException(Exception exception, HttpServletRequest httpServletRequest,
                                      HandlerMethod handlerMethod) {
        log.info("请求异常, httpServletRequest:{} message:{}", httpServletRequest, exception.getMessage(), exception);

        Result<Object> result = new Result<>();
        IExceptionCodeData exceptionCodeData = CodeUtils.getCodeData(exception);
        //1.如果是可识别的异常，直接取异常code
        if (Objects.nonNull(exceptionCodeData)) {
            result.setCode(exceptionCodeData.getCode());
            result.setMsg(exceptionCodeData.getMessage());
            result.setMessage(exceptionCodeData.getMessage());

            if (Objects.nonNull(exceptionCodeData.getData())) {
                result.setData(exceptionCodeData.getData());
            }

            //这种情况也要当作系统异常来处理
            if ((exception instanceof SystemException) || (exception instanceof ThirdPartyException)) {
                String message = String.format("接口 [%s] 出现异常，方法：%s.%s，异常摘要：%s",
                        httpServletRequest.getRequestURI(),
                        handlerMethod.getBean().getClass().getName(),
                        handlerMethod.getMethod().getName(),
                        exception.getMessage());
                log.error("系统异常, message:{}", message);

                //系统异常则上报raptor请求异常
                httpServletRequest.setAttribute("cat-state", "Exception");
            }
        } else {
            //2. 不可识别的异常 当作系统异常处理
            String message = String.format("接口 [%s] 出现异常，方法：%s.%s，异常摘要：%s",
                    httpServletRequest.getRequestURI(),
                    handlerMethod.getBean().getClass().getName(),
                    handlerMethod.getMethod().getName(),
                    exception.getMessage());
            log.error("系统异常, message:{}", message);
            result.setCode(DEFAULT_EXCEPTION);
            result.setMsg("系统繁忙,请稍后重试");
            result.setMessage("系统繁忙,请稍后重试");

            //系统异常则上报raptor请求异常
            httpServletRequest.setAttribute("cat-state", "Exception");
        }

        //3.raptor埋点上报
        catReport(result, httpServletRequest.getRequestURI());

        return result;
    }

    private void catReport(Result<Object> result, String url) {
        String code ;
        String msg;
        if (result == null || result.getCode() == null) {
            code = "NullResponse";
            msg = "";
        } else {
            code = String.valueOf(result.getCode());
            msg = result.getMsg();
        }

        if (!Objects.equals(code, "0")) {
            log.warn("logResponseCode url {} code {} msg {} trace {}", url, code, msg, Tracer.id());
            Cat.logMetricForCount("ErrRespCode_" + url, Maps.newHashMap("code", code));
        } else {
            Cat.logMetricForCount("RespCode_" + url, Maps.newHashMap("code",  code));
        }
    }
}
