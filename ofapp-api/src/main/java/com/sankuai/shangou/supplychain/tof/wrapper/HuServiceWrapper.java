package com.sankuai.shangou.supplychain.tof.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.logistics.hu.api.constants.enums.SealContainerOperateType;
import com.sankuai.shangou.logistics.hu.api.dto.Result;
import com.sankuai.shangou.logistics.hu.api.dto.SealContainerLogDTO;
import com.sankuai.shangou.logistics.hu.api.service.SealContainerLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/6/26 14:52
 **/
@Slf4j
@Rhino
public class HuServiceWrapper {

    @Resource
    private SealContainerLogService sealContainerLogService;

    private final Integer ZERO = 0;
    @Degrade(rhinoKey = "HuServiceWrapper.queryLatestSealContainerOperateLog", fallBackMethod = "queryLatestSealContainerOperateLogFallback", timeoutInMilliseconds = 2000)
    public List<SealContainerLogDTO> queryLatestSealContainerOperateLog(Long tenantId, List<Long> warehouseIds, List<String> containerCodeList) {
        Result<List<SealContainerLogDTO>> result;
        try {
            log.info("start invoke sealContainerLogQueryService.queryLatestSealContainerOperateLog, tenantId:{}, warehouseIds: {}, containerCodeList:{}", tenantId, warehouseIds, containerCodeList);
            result = sealContainerLogService.queryLatestSealContainerOperateLog(Collections.singletonList(LoginContextUtils.getAppLoginStoreId()),
                    LoginContextUtils.getAppLoginTenant(), containerCodeList);
            log.info("end invoke sealContainerLogQueryService.queryLatestSealContainerOperateLog, result: {}", result);
        } catch (Exception e) {
            log.error("查询容具使用记录失败", e);
            throw new ThirdPartyException("查询容具使用记录失败");
        }

        if (result.getCode() != 0) {
            throw new BizException(result.getMessage());
        }

        return result.getModule();
    }

    @Degrade(rhinoKey = "HuServiceWrapper.queryWarehouseUsingSealContainerCount", fallBackMethod = "queryWarehouseUsingSealContainerCountFallback", timeoutInMilliseconds = 2000)
    public Integer queryWarehouseUsingSealContainerCount(Long merchantId, Long warehouseId) {
        Result<List<SealContainerLogDTO>> result;
        try {
            log.info("start invoke sealContainerLogService.queryLatestSealContainerOperateLog, warehouseId: {}", warehouseId);
            result = sealContainerLogService.queryLatestSealContainerOperateLog(Collections.singletonList(warehouseId), merchantId, Collections.emptyList());
            log.info("end invoke sealContainerLogService.queryLatestSealContainerOperateLog, result: {}", result);
        } catch (Exception e) {
            log.error("查询容具使用记录失败", e);
            throw new BizException("查询容具使用记录失败");
        }

        if (result.getCode() != 0) {
            throw new BizException(result.getMessage());
        }

        if (CollectionUtils.isEmpty(result.getModule())) {
            return ZERO;
        }

        //只看现在是使用中的容具
        return (int) result.getModule().stream()
                .filter(opLog -> Objects.equals(opLog.getOpType(), SealContainerOperateType.STOCK_OUT.getCode())
                        || Objects.equals(opLog.getOpType(), SealContainerOperateType.TRANSFER.getCode())).count();

    }

    @Degrade(rhinoKey = "HuServiceWrapper.queryWarehouseUsingSealContainerCount", fallBackMethod = "queryWarehouseUsingSealContainerCountFallback", timeoutInMilliseconds = 2000)
    public Integer queryWarehouseUsingSealContainerCountFallback(Long merchantId, Long warehouseId) {
        log.error("HuServiceWrapper.queryWarehouseUsingSealContainerCount 发生降级");
        return 0;
    }



        public List<SealContainerLogDTO> queryLatestSealContainerOperateLogFallback(Long tenantId, List<Long> warehouseIds, List<String> containerCodeList) {
        log.error("HuServiceWrapper.queryLatestSealContainerOperateLog 发生降级");
        return Collections.emptyList();
    }
}
