/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.shangou.supplychain.tof.utils;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <br><br>
 * Author: linjianyu <br>
 * Date: 2019-02-28 Time: 17:14
 */
@Component
@SuppressWarnings("unused")
public class SpringAppContext implements ApplicationContextAware {

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        AppContext.setApplicationContextHolder(applicationContext);
    }

    @SuppressWarnings({"unused", "WeakerAccess", "unchecked"})
    public static class AppContext {
        private static ApplicationContext applicationContextHolder;

        public static void setApplicationContextHolder(ApplicationContext context) {
            applicationContextHolder = context;
        }

        public static <T> T getBean(Class<T> t) {
            return applicationContextHolder.getBean(t);

        }

        public static <T> T getBean(Class<T> clazz, String beanName) {
            return applicationContextHolder.getBean(beanName, clazz);
        }

        public static <T> Map<String, T> getBeanOfType(Class<T> clazz) {
            return applicationContextHolder.getBeansOfType(clazz);
        }

        public static <T> T getBean(String beanName) {
            return (T) applicationContextHolder.getBean(beanName);
        }
    }
}
