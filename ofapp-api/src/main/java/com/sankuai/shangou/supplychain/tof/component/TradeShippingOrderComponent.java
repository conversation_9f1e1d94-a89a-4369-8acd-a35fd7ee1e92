package com.sankuai.shangou.supplychain.tof.component;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/28 15:05
 **/
@Rhino
@Slf4j
public class TradeShippingOrderComponent {
    @Resource
    private TradeShippingOrderService tradeShippingOrderService;

    @Degrade(rhinoKey = "PickingOrderComponent.queryTradeShippingOrderOrderDetail", fallBackMethod = "queryTradeShippingOrderOrderDetailFallback", timeoutInMilliseconds = 2000)
    public TradeShippingOrderDTO queryTradeShippingOrderOrderDetail(TradeOrderInfoComponent.TradeOrderKey tradeOrderKey) {
        Long storeId = LoginContextUtils.getAppLoginStoreId();
        log.info("start invoke tradeShippingOrderService.getByTradeOrderNos, tradeOrderKey: {}", tradeOrderKey);
        TResult<List<TradeShippingOrderDTO>> shippingOrderResult = tradeShippingOrderService.getByTradeOrderNos(storeId, tradeOrderKey.getOrderBizType(), Collections.singletonList(tradeOrderKey.getChannelOrderId()));
        log.info("end invoke tradeShippingOrderService.getByTradeOrderNos, shippingOrderResult: {}", shippingOrderResult);
        if (!shippingOrderResult.isSuccess()) {
            throw new BizException("查询拣货详情失败");
        }

        if (CollectionUtils.isEmpty(shippingOrderResult.getData())) {
            throw new BizException("未查询到出库单");
        }

        return shippingOrderResult.getData().get(0);
    }

    @Degrade(rhinoKey = "PickingOrderComponent.getRecentListByOperatorIdAndStatusList", fallBackMethod = "getRecentListByOperatorIdAndStatusListFallback", timeoutInMilliseconds = 2000)
    public List<TradeShippingOrderDTO> getRecentListByOperatorIdAndStatusList(long warehouseId, Long accountId, List<Integer> statusList) {
        Long storeId = LoginContextUtils.getAppLoginStoreId();
        log.info("start invoke tradeShippingOrderService.getRecentListByOperatorIdAndStatusList, warehouseId: {}, statusList: {}, accountId: {}", warehouseId, statusList, accountId);
        TResult<List<TradeShippingOrderDTO>> recentListResult = tradeShippingOrderService.getRecentListByOperatorIdAndStatusList(
                warehouseId, accountId, statusList
        );
        log.info("end invoke tradeShippingOrderService.getRecentListByOperatorIdAndStatusList, recentListResult: {}", recentListResult);
        if (!recentListResult.isSuccess()) {
            throw new BizException("查询出库单失败");
        }

        return recentListResult.getData();
    }

    public TradeShippingOrderDTO queryTradeShippingOrderOrderDetailFallback(TradeOrderInfoComponent.TradeOrderKey tradeOrderKey) {
        log.info("PickingOrderComponent.queryTradeShippingOrderOrderDetail 发生降级");
        throw new BizException("查询拣货详情失败");
    }

    public List<TradeShippingOrderDTO> getRecentListByOperatorIdAndStatusListFallback(long warehouseId, Long accountId, List<Integer> statusList) {
        log.info("PickingOrderComponent.getRecentListByOperatorIdAndStatusList 发生降级");
        return Collections.emptyList();
    }
}
