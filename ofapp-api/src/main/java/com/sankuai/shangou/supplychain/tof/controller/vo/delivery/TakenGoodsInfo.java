package com.sankuai.shangou.supplychain.tof.controller.vo.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-03
 * @email <EMAIL>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TakenGoodsInfo {

    @FieldDoc(
            description = "封签码", requiredness = Requiredness.OPTIONAL
    )
    private List<String> sealCodes;

    @FieldDoc(
            description = "出库图片", requiredness = Requiredness.OPTIONAL
    )
    private List<String> stockOutPics;

    @FieldDoc(
            description = "操作人", requiredness = Requiredness.OPTIONAL
    )
    private String operatorName;

    @FieldDoc(
            description = "出库状态", requiredness = Requiredness.OPTIONAL
    )
    private Integer outboundStatus;

}
