package com.sankuai.shangou.supplychain.tof.component;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.rider.client.common.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryCompletedStatisticRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.QueryCompletedStatisticResponse;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.CompletedDeliveryOrderStatisticVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/3/19 15:23
 **/
@Rhino
@Slf4j
public class DeliveryStatisticComponent {
    @Resource
    private RiderQueryThriftService riderQueryThriftService;

    @Degrade(rhinoKey = "DeliveryStatisticComponent.queryDeliveryStatisticComponent", fallBackMethod = "queryDeliveryStatisticComponentFallback", timeoutInMilliseconds = 2000)
    public CompletedDeliveryOrderStatisticVO queryDeliveryStatisticComponent(LocalDate date) {

        QueryCompletedStatisticRequest request = new QueryCompletedStatisticRequest();
        request.setTenantId(AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId());
        request.setStoreId(Long.parseLong(AppLoginContextHolder.getAppLoginContext().getStoreIds()));
        request.setDate(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        request.setRiderAccountId(AppLoginContextHolder.getAppLoginContext().getLoginUser().getAccountId());
        log.info("start invoke riderQueryThriftService.queryCompletedStatistic, request: {}", request);
        QueryCompletedStatisticResponse response = riderQueryThriftService.queryCompletedStatistic(request);
        log.info("start invoke riderQueryThriftService.queryCompletedStatistic, respose: {}", request);

        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new BizException(response.getStatus().getMsg());
        }

        return buildCompletedDeliveryOrderStatisticVO(response);


    }

    public CompletedDeliveryOrderStatisticVO queryDeliveryStatisticComponentFallback(LocalDate date) {
        log.error("DeliveryStatisticComponent.queryDeliveryStatisticComponent 发生降级");
        return null;
    }

    private CompletedDeliveryOrderStatisticVO buildCompletedDeliveryOrderStatisticVO(QueryCompletedStatisticResponse response) {
        CompletedDeliveryOrderStatisticVO deliveryOrderStatisticVO = new CompletedDeliveryOrderStatisticVO();
        deliveryOrderStatisticVO.setTotal(Optional.ofNullable(response.getCompletedTotalCnt()).map(Long::intValue).orElse(null));
        deliveryOrderStatisticVO.setOneYuanOrderCount(Optional.ofNullable(response.getOneYuanOrderCnt()).map(Long::intValue).orElse(null));
        deliveryOrderStatisticVO.setRiskControlOrderCount(Optional.ofNullable(response.getRiskControlCnt()).map(Long::intValue).orElse(null));
        deliveryOrderStatisticVO.setRiskControlDataIsReady(response.getRiskControlDataIsReady());

        return deliveryOrderStatisticVO;
    }
}
