package com.sankuai.shangou.supplychain.tof.component;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.CombinationProductItemDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderItemDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.BatchFulfillmentOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.ChannelOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.FulfillmentOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.response.order.FulfillmentOrderDetailResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderItemDTO;
import com.sankuai.shangou.supplychain.tof.wrapper.FulfillmentOrderServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.SupplyProductTagWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/4/25 11:52
 **/
@Rhino
@Slf4j
public class ProductHighPriceTagComponent {

    @Resource
    private FulfillmentOrderSearchThriftService fulfillmentOrderSearchThriftService;

    @Resource
    private SupplyProductTagWrapper supplyProductTagWrapper;

    @Resource
    private FulfillmentOrderServiceWrapper fulfillmentOrderServiceWrapper;

    @Degrade(rhinoKey = "ProductHighPriceTagComponent.batchGetProductHighPriceTag", fallBackMethod = "batchGetProductHighPriceTagFallback", timeoutInMilliseconds = 2000)
    public Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> batchGetProductHighPriceTag(List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList) {
        List<ChannelOrderIdKeyReq> channelOrderIdKeyReqs = tradeOrderKeyList.stream()
                .map(tradeOrderKey -> new ChannelOrderIdKeyReq(tradeOrderKey.getOrderBizType(), tradeOrderKey.getChannelOrderId()))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(channelOrderIdKeyReqs)) {
            return Collections.emptyMap();
        }

        List<FulfillmentOrderDetailDTO> fulfillmentOrderDetailDTOS = fulfillmentOrderServiceWrapper.searchFulfillmentOrderListByBatchFulfillmentOrderId(channelOrderIdKeyReqs);


        List<String> skuIds = fulfillmentOrderDetailDTOS.stream()
                .map(FulfillmentOrderDetailDTO::getItemList)
                .flatMap(Collection::stream)
                .filter(item -> new BigDecimal(item.getQuantity()).subtract(new BigDecimal(item.getRefundCount())).compareTo(BigDecimal.ZERO) > 0)
                .map(FulfillmentOrderItemDetailDTO::getCombinationProductItemList)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(CombinationProductItemDTO::getSkuId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        Map<String, Boolean> isHighPriceMap = supplyProductTagWrapper.batchGetProductHighPriceTag(LoginContextUtils.getAppLoginTenant(), skuIds);

        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> orderIsContainHighPriceMap = new HashMap<>();
        for (FulfillmentOrderDetailDTO fulfillmentOrderDetailDTO : fulfillmentOrderDetailDTOS) {
            boolean containHighPrice = fulfillmentOrderDetailDTO.getItemList()
                    .stream()
                    .filter(item -> new BigDecimal(item.getQuantity()).subtract(new BigDecimal(item.getRefundCount())).compareTo(BigDecimal.ZERO) > 0)
                    .map(FulfillmentOrderItemDetailDTO::getCombinationProductItemList)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .map(CombinationProductItemDTO::getSkuId)
                    .filter(Objects::nonNull)
                    .anyMatch(skuId -> Objects.equals(isHighPriceMap.get(skuId), true));
            orderIsContainHighPriceMap.putIfAbsent(new TradeOrderInfoComponent.TradeOrderKey(fulfillmentOrderDetailDTO.getOrderSource(), fulfillmentOrderDetailDTO.getChannelOrderId()), containHighPrice);
        }

        return orderIsContainHighPriceMap;
    }


    @Degrade(rhinoKey = "ProductHighPriceTagComponent.getProductHighPriceTagBySkuIds", fallBackMethod = "getProductHighPriceTagBySkuIdsFallback", timeoutInMilliseconds = 2000)
    public Map<String, Boolean> getProductHighPriceTagBySkuIds(TradeShippingOrderDTO tradeShippingOrderDTO) {
        List<String> skuIds = tradeShippingOrderDTO.getItems().stream()
                .map(TradeShippingOrderItemDTO::getSkuId)
                .distinct().collect(Collectors.toList());

        return supplyProductTagWrapper.batchGetProductHighPriceTag(LoginContextUtils.getAppLoginTenant(), skuIds);
    }

    public Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> batchGetProductHighPriceTagFallback(List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList) {
        log.error("ProductHighPriceTagComponent.batchGetProductHighPriceTag 发生降级");
        return Collections.emptyMap();
    }

    public Map<String, Boolean> getProductHighPriceTagBySkuIdsFallback(TradeShippingOrderDTO tradeShippingOrderDTO) {
        log.error("ProductHighPriceTagComponent.getProductHighPriceTagBySkuIds 发生降级");
        return Collections.emptyMap();
    }
}
