package com.sankuai.shangou.supplychain.tof.controller.vo.delivery;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(
        description = "配送异常信息VO"
)
public class DeliveryExceptionInfoVO {
    @FieldDoc(
            description = "运单id"
    )
    private String deliveryOrderId;

    @FieldDoc(
            description = "渠道订单id"
    )
    private String channelOrderId;

    @FieldDoc(
            description = "订单业务类型"
    )
    private Integer orderBizType;

    @FieldDoc(
            description = "上报骑手id"
    )
    private String riderAccountId;

    @FieldDoc(
            description = "上报骑手名字"
    )
    private String riderAccountName;

    @FieldDoc(
            description = "上报异常类型"
    )
    private Integer exceptionType;

    @FieldDoc(
            description = "上报异常类型描述"
    )
    private String exceptionTypeDesc;


    @FieldDoc(
            description = "上报异常时间"
    )
    private Long reportTimeStamp;

    @FieldDoc(
            description = "支付时间"
    )
    public Long payTime;

    @FieldDoc(
            description = "日流水号"
    )
    public Integer daySeq;

    @FieldDoc(
            description = "门店id"
    )
    public Long storeId;

}
