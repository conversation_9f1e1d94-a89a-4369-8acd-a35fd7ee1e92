package com.sankuai.shangou.supplychain.tof.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.api.tag.ProductTagDTO;
import com.sankuai.meituan.shangou.xsupply.product.client.request.tag.ProductTagConditionQueryRequest;
import com.sankuai.meituan.shangou.xsupply.product.client.response.api.tag.ProductTagListResponse;
import com.sankuai.meituan.shangou.xsupply.product.client.service.api.TagThriftApi;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/2/29 14:34
 **/

@Rhino
@Slf4j
public class SupplyProductTagWrapper {
    @Resource
    private TagThriftApi tagThriftApi;

    private final int BATCH_SIZE = 50;

    private static final String SEAL_TAG_CODE = "SEAL_GOODS";

    private static final String NEED_SEAL_TAG_VALUE_CODE = "SEAL_TRUE";

    @Degrade(rhinoKey = "SupplyProductWrapper.batchGetProductHighPriceTag", fallBackMethod = "batchGetProductHighPriceTagFallBack", timeoutInMilliseconds = 1000)
    @MethodLog(logResponse = true, logRequest = true)
    public Map<String, Boolean> batchGetProductHighPriceTag(Long tenantId, List<String> skuIds) {
        MccConfigUtil.HighPriceTagConfig highPriceTagConfig = MccConfigUtil.getHighPriceTagConfig();
        if (highPriceTagConfig == null || StringUtils.isBlank(highPriceTagConfig.getTagCode()) || StringUtils.isBlank(highPriceTagConfig.getTagValueCode())) {
            log.warn("标签配置不合法, highPriceTagConfig: {}", highPriceTagConfig);
            return Collections.emptyMap();
        }

        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }

        List<List<String>> lists = Lists.partition(skuIds.stream().distinct().collect(Collectors.toList()), BATCH_SIZE);
        List<ProductTagDTO> productTagDTOS = new ArrayList<>();
        for (List<String> subList : lists) {
            ProductTagConditionQueryRequest request = new ProductTagConditionQueryRequest();
            request.setTenantId(tenantId);
            request.setProductIdList(subList);
            request.setMaxSize(500);
            request.setTagCodeList(Arrays.asList(highPriceTagConfig.getTagCode()));
            log.info("start invoke tagThriftApi.batchQueryTagRelationList, request: {}", request);
            ProductTagListResponse response = tagThriftApi.batchQueryTagRelationList(request);
            log.info("end invoke tagThriftApi.batchQueryTagRelationList, response: {}", response);

            if (response == null || response.getStatus() == null || !Objects.equals(response.getStatus().getCode(), 0)) {
                throw new ThirdPartyException("查询高价值标签失败");
            }

            productTagDTOS.addAll(Optional.ofNullable(response.getProductTagList()).orElse(Collections.emptyList()));
        }


        return productTagDTOS
                .stream()
                .collect(Collectors.toMap(
                        ProductTagDTO::getProductId,
                        productTagDTO -> Objects.equals(productTagDTO.getTagCode(), highPriceTagConfig.getTagCode()) && Objects.equals(productTagDTO.getTagValueCode(), highPriceTagConfig.getTagValueCode()),
                        (k1, k2) -> k1));
    }

    public Map<String, Boolean> batchGetProductHighPriceTagFallBack(Long tenantId, List<String> skuIds) {
        log.error("SupplyProductWrapper.batchGetProductHighPriceTag 发生降级, skuIds: {}", skuIds);
        return Collections.emptyMap();
    }
}
