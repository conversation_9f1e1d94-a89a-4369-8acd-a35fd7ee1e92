package com.sankuai.shangou.supplychain.tof.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OnlineBaseOrderVO;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.utils.ChannelTypeConvertUtils;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TStaffRider;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.ResponseCode;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import com.sankuai.shangou.logistics.hu.api.constants.enums.SealContainerOperateType;
import com.sankuai.shangou.logistics.hu.api.dto.Result;
import com.sankuai.shangou.logistics.hu.api.dto.SealContainerLogDTO;
import com.sankuai.shangou.logistics.hu.api.service.SealContainerLogService;
import com.sankuai.shangou.supplychain.tof.component.DeliveryOrderComponent;
import com.sankuai.shangou.supplychain.tof.component.TradeOrderInfoComponent;
import com.sankuai.shangou.supplychain.tof.config.LionConfigUtils;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.request.CheckSealCodeIsUsableRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.request.ReturnSealContainerRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.seal.UsingSealContainerVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.seal.response.CheckSealCodeResponse;
import com.sankuai.shangou.supplychain.tof.enums.ErrorCodeEnum;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import com.sankuai.shangou.supplychain.tof.wrapper.HuServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.OSWServiceWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.zookeeper.Login;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-06-27
 * @email <EMAIL>
 */
@Slf4j
@Service
public class SealManagementService {

    @Resource
    private SealContainerLogService sealContainerLogService;

    @Resource
    private HuServiceWrapper huServiceWrapper;

    @Resource
    private DeliveryOrderComponent deliveryOrderComponent;

    @Resource
    private TradeOrderInfoComponent tradeOrderInfoComponent;

    @Resource
    private OSWServiceWrapper oswServiceWrapper;

    private static final String SEAL_CONTAINER_PREFIX = "FQRJ";

    private static final Long DEFAULT_DELIVERY_DONE_TIME = TimeUtils.toMilliSeconds(LocalDateTime.of(1970, 1,1,0,0,0));

    @MethodLog(logRequest = true, logResponse = true)
    public CheckSealCodeResponse checkIsUsable(CheckSealCodeIsUsableRequest request) {

        if (StringUtils.isBlank(request.getBarCode())
                || !request.getBarCode().startsWith(SEAL_CONTAINER_PREFIX)
                || request.getBarCode().length() != LionConfigUtils.getSealContainerCodeLength()) {
            throw new BizException("识别错误，非封签扣非门店可用容具编码，请确认后重新扫码或手动添加");
        }

        Result<List<SealContainerLogDTO>> result = sealContainerLogService.queryLatestSealContainerOperateLog(
                Lists.newArrayList(Long.parseLong(AppLoginContextHolder.getAppLoginContext().getStoreIds())), AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId(), Lists.newArrayList(request.getBarCode())
        );
        if (!Objects.equals(result.getCode(), ResponseCode.SUCCESSED)) {
            throw new ThirdPartyException("查询容器占用情况失败");
        }
        //没使用过 或 已归还 都能用
        if (CollectionUtils.isEmpty(result.getModule())
                || Objects.equals(result.getModule().get(0).getOpType(), SealContainerOperateType.STOCK_IN.getCode())) {
            return new CheckSealCodeResponse(true, null);
        }
        return new CheckSealCodeResponse(false, result.getModule().get(0).getTradeOrderNo());
    }

    /**
     * 封签-归还封签容具
     */
    public void returnSealContainer(ReturnSealContainerRequest request) {
        //根据编码查到关联订单
        List<SealContainerLogDTO> sealContainerLogDTOS = huServiceWrapper.queryLatestSealContainerOperateLog(LoginContextUtils.getAppLoginTenant(),
                Collections.singletonList(LoginContextUtils.getAppLoginStoreId()), Collections.singletonList(request.getBarCode()));

        if (CollectionUtils.isEmpty(sealContainerLogDTOS)) {
            throw new BizException(ErrorCodeEnum.SEAL_CONTAINER_NOT_NEED_RETURN.getCode(), ErrorCodeEnum.SEAL_CONTAINER_NOT_NEED_RETURN.getMessage());
        }

        SealContainerLogDTO sealContainerLogDTO = sealContainerLogDTOS.get(0);

        if (MccConfigUtil.getReturnFixSwitch()
                && Objects.equals(sealContainerLogDTO.getOpType(), SealContainerOperateType.STOCK_IN.getCode())
                && Objects.equals(sealContainerLogDTO.getOperatorId(), LoginContextUtils.getAppLoginUser().getAccountId())) {
            throw new BizException(ErrorCodeEnum.SEAL_CONTAINER_ALREADY_RETURN.getCode(), ErrorCodeEnum.SEAL_CONTAINER_ALREADY_RETURN.getMessage());
        }

        if (!Objects.equals(sealContainerLogDTO.getOpType(), SealContainerOperateType.STOCK_OUT.getCode()) &&
                !Objects.equals(sealContainerLogDTO.getOpType(), SealContainerOperateType.TRANSFER.getCode())) {
            throw new BizException(ErrorCodeEnum.SEAL_CONTAINER_NOT_NEED_RETURN.getCode(), ErrorCodeEnum.SEAL_CONTAINER_NOT_NEED_RETURN.getMessage());
        }

        //查询订单的配送状态 是否还在配送中
        if (!Objects.equals(request.getSkipDeliveryStatusCheck(), true)) {
            TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = new TradeOrderInfoComponent.TradeOrderKey(sealContainerLogDTO.getTradeOrderBizType(), sealContainerLogDTO.getTradeOrderNo());
            Map<TradeOrderInfoComponent.TradeOrderKey, OCMSOrderVO> ocmsOrderVOMap = tradeOrderInfoComponent.queryTradeOrderInfoList(Collections.singletonList(tradeOrderKey));
            OCMSOrderVO ocmsOrderVO = ocmsOrderVOMap.get(tradeOrderKey);
            Long orderId = ocmsOrderVO.getOrderId();
            Map<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> tRiderDeliveryOrderMap =
                    deliveryOrderComponent.queryDeliveryOrderByOrderIdList(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginStoreId(), Collections.singletonList(orderId), false);
            TRiderDeliveryOrder tRiderDeliveryOrder = tRiderDeliveryOrderMap.get(tradeOrderKey);
            if (Objects.nonNull(tRiderDeliveryOrder) && Objects.equals(tRiderDeliveryOrder.getDeliveryStatus(), RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode())) {
                throw new BizException(ErrorCodeEnum.ORDER_IS_IN_DELIVERY.getCode(), ErrorCodeEnum.ORDER_IS_IN_DELIVERY.getMessage());
            }
        }

        //归还容具
        SealContainerLogDTO returnLogDTO = buildSealContainerLogDTO(sealContainerLogDTO.getTradeOrderNo(),
                sealContainerLogDTO.getTradeOrderBizType(), request.getBarCode());
        Result<List<SealContainerLogDTO>> result;
        try {
            log.info("start invoke sealContainerLogService.appendLog, logDTO: {}", returnLogDTO);
            result = sealContainerLogService.appendLog(LoginContextUtils.getAppLoginStoreId(), LoginContextUtils.getAppLoginTenant(), Collections.singletonList(returnLogDTO));
            log.info("end invoke sealContainerLogService.appendLog, result: {}", result);
        } catch (Exception e) {
            log.error("归还容具失败", e);
            throw new ThirdPartyException("归还容具失败");
        }

        if (result.getCode() != 0) {
            throw new BizException(result.getMessage());
        }
    }


    /**
     * 查询当前门店待归还的容具列表
     */
    public List<UsingSealContainerVO> queryUsingSealContainerList(Boolean onlyShowCurrRiderRecord) {
        List<SealContainerLogDTO> usingSealContainerLatestLogs = queryLatestSealContainerOperateLog(LoginContextUtils.getAppLoginTenant(), null, Collections.singletonList(LoginContextUtils.getAppLoginStoreId()));

        //只看现在是使用中的容具
        usingSealContainerLatestLogs = usingSealContainerLatestLogs.stream()
                .filter(opLog -> Objects.equals(opLog.getOpType(), SealContainerOperateType.STOCK_OUT.getCode())
                        || Objects.equals(opLog.getOpType(), SealContainerOperateType.TRANSFER.getCode()))
                .collect(Collectors.toList());


        if (CollectionUtils.isEmpty(usingSealContainerLatestLogs)) {
            return Collections.emptyList();
        }

        //拿到最近一次操作记录是转单的容具code 查询对应容具最后一次出库的记录
        Map<String, SealContainerLogDTO> transferSealContainerLogMap = usingSealContainerLatestLogs.stream()
                .filter(opLog -> Objects.equals(opLog.getOpType(), SealContainerOperateType.TRANSFER.getCode()))
                .collect(Collectors.toMap(SealContainerLogDTO::getSealContainerCode, Function.identity(), (k1, k2) -> k2));

        //所有现在使用中的容具的出库日志 = 有转过单的 + 没转过单的
        List<SealContainerLogDTO> allUsingContainerStockOutLogs = new ArrayList<>();
        List<SealContainerLogDTO> stockOutWithTransferSealContainerLogs =
                queryLatestSealContainerOperateLogByOpTye(LoginContextUtils.getAppLoginTenant(),
                        Collections.singletonList(LoginContextUtils.getAppLoginStoreId()),
                        new ArrayList<>(transferSealContainerLogMap.keySet()),
                        Collections.singletonList(SealContainerOperateType.STOCK_OUT.getCode()));

        List<SealContainerLogDTO> stockOutWithoutTransferSealContainerLogs =  usingSealContainerLatestLogs.stream()
                .filter(opLog -> Objects.equals(opLog.getOpType(), SealContainerOperateType.STOCK_OUT.getCode()))
                .collect(Collectors.toList());

        allUsingContainerStockOutLogs.addAll(stockOutWithTransferSealContainerLogs);
        allUsingContainerStockOutLogs.addAll(stockOutWithoutTransferSealContainerLogs);

        //查询关联订单
        List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeys = usingSealContainerLatestLogs.stream()
                .map(log -> new TradeOrderInfoComponent.TradeOrderKey(log.getTradeOrderBizType(), log.getTradeOrderNo()))
                .collect(Collectors.toList());
        Map<TradeOrderInfoComponent.TradeOrderKey, OCMSOrderVO> ocmsOrderVOMap = tradeOrderInfoComponent.queryTradeOrderInfoList(tradeOrderKeys);

        //查询关联运单
        List<Long> orderIdList = ocmsOrderVOMap.values().stream().map(OnlineBaseOrderVO::getOrderId).collect(Collectors.toList());
        Map<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> tRiderDeliveryOrderMap =
                deliveryOrderComponent.queryDeliveryOrderByOrderIdList(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginStoreId(), orderIdList, true);

        //查询骑手账号名
        List<Long> riderAccountIdList = tRiderDeliveryOrderMap.values().stream()
                .map(TRiderDeliveryOrder::getCurrentRider)
                .filter(Objects::nonNull)
                .map(TStaffRider::getAccountId)
                .distinct()
                .collect(Collectors.toList());
        List<EmployeeDTO> employeeDTOS = oswServiceWrapper.queryEmpByAccountIds(LoginContextUtils.getAppLoginTenant(), riderAccountIdList);
        Map<Long, String> accountId2AccountNameMap = employeeDTOS.stream().collect(Collectors.toMap(EmployeeDTO::getAccountId, EmployeeDTO::getAccountName, (k1, k2) -> k2));

        return buildUsingSealContainerVOList(allUsingContainerStockOutLogs, tRiderDeliveryOrderMap, accountId2AccountNameMap, onlyShowCurrRiderRecord);
    }

    private List<UsingSealContainerVO> buildUsingSealContainerVOList(List<SealContainerLogDTO> allUsingContainerStockOutLogs,
                                                                     Map<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> tRiderDeliveryOrderMap,
                                                                     Map<Long, String> accountId2AccountNameMap,
                                                                     Boolean onlyShowCurrRiderRecord) {
        List<UsingSealContainerVO> containerVOList = allUsingContainerStockOutLogs.stream()
                .distinct()
                .map(containerLogDTO -> {
                    TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = new TradeOrderInfoComponent.TradeOrderKey(containerLogDTO.getTradeOrderBizType(), containerLogDTO.getTradeOrderNo());
                    TRiderDeliveryOrder tRiderDeliveryOrder = tRiderDeliveryOrderMap.get(tradeOrderKey);

                    UsingSealContainerVO usingSealContainerVO = new UsingSealContainerVO();
                    usingSealContainerVO.setSealContainerCode(Collections.singletonList(containerLogDTO.getSealContainerCode()));
                    usingSealContainerVO.setChannelId(ChannelTypeConvertUtils.convert(containerLogDTO.getTradeOrderBizType()));
                    usingSealContainerVO.setWarehouseId(containerLogDTO.getWarehouseId());
                    usingSealContainerVO.setTradeOrderNo(containerLogDTO.getTradeOrderNo());
                    usingSealContainerVO.setStockOutTime(TimeUtils.toMilliSeconds(containerLogDTO.getOpTime()));
                    if (Objects.nonNull(tRiderDeliveryOrder)) {
                        usingSealContainerVO.setRiderName(Optional.ofNullable(tRiderDeliveryOrder.getCurrentRider()).map(TStaffRider::getName).orElse(null));
                        usingSealContainerVO.setRiderAccountId(Optional.ofNullable(tRiderDeliveryOrder.getCurrentRider()).map(TStaffRider::getAccountId).orElse(null));
                        usingSealContainerVO.setDeliveryStatus(tRiderDeliveryOrder.getDeliveryStatus());
                        usingSealContainerVO.setIsLockedDelivery(Objects.equals(tRiderDeliveryOrder.getStatusLocked(), 1));
                        if (Objects.nonNull(tRiderDeliveryOrder.getCurrentRider()) && Objects.nonNull(tRiderDeliveryOrder.getCurrentRider().getAccountId())) {
                            usingSealContainerVO.setRiderAccountName(accountId2AccountNameMap.get(tRiderDeliveryOrder.getCurrentRider().getAccountId()));
                        }

                        if (Objects.equals(tRiderDeliveryOrder.getDeliveryStatus(), RiderDeliveryStatusEnum.DELIVERY_DONE.getCode())
                                && Objects.nonNull(tRiderDeliveryOrder.getDeliveryDoneTime())
                                && !Objects.equals(tRiderDeliveryOrder.getDeliveryDoneTime(), DEFAULT_DELIVERY_DONE_TIME)) {
                            usingSealContainerVO.setTimeoutDuration((int)((TimeUtils.toMilliSeconds(LocalDateTime.now()) - tRiderDeliveryOrder.getDeliveryDoneTime()) / 1000));
                        }

                        //配送没有单独记录取消时间 用上一次状态改变的时间代替
                        if (Objects.equals(tRiderDeliveryOrder.getDeliveryStatus(), RiderDeliveryStatusEnum.DELIVERY_CANCELLED.getCode()) && Objects.nonNull(tRiderDeliveryOrder.getLastEventTime())) {
                            usingSealContainerVO.setTimeoutDuration((int)((TimeUtils.toMilliSeconds(LocalDateTime.now()) - tRiderDeliveryOrder.getLastEventTime()) / 1000));
                        }
                    }


                    return usingSealContainerVO;

                }).collect(Collectors.toList());

        //合并相同订单 并按照出库时间排序
        return containerVOList.stream()
                .collect(Collectors.toMap(vo -> vo.getTradeOrderNo() + "_" + vo.getChannelId(),
                        Function.identity(), (v1, v2) -> {
                            List<String> allContainerCodeList = new ArrayList<>();
                            allContainerCodeList.addAll(v1.getSealContainerCode());
                            allContainerCodeList.addAll(v2.getSealContainerCode());
                            v1.setSealContainerCode(allContainerCodeList);
                            return v1;
                        })).values().stream()
                .sorted(Comparator.comparingLong(UsingSealContainerVO::getStockOutTime))
                .filter(vo -> Objects.equals(onlyShowCurrRiderRecord, false) || Objects.equals(vo.getRiderAccountId(), LoginContextUtils.getAppLoginAccountId()))
                .collect(Collectors.toList());
    }

    private List<SealContainerLogDTO> queryLatestSealContainerOperateLog(long tenantId, String sealContainerCode, List<Long> warehouseIds) {
        Result<List<SealContainerLogDTO>> result;
        try {
            List<String> containerCodeList = null;
            if (StringUtils.isNotBlank(sealContainerCode)) {
                containerCodeList = Collections.singletonList(sealContainerCode);
            }
            log.info("start invoke sealContainerLogService.queryLatestSealContainerOperateLog, storeIdList: {}", warehouseIds);
            result = sealContainerLogService.queryLatestSealContainerOperateLog(warehouseIds, tenantId, containerCodeList);
            log.info("end invoke sealContainerLogService.queryLatestSealContainerOperateLog, result: {}", result);
        } catch (Exception e) {
            log.error("查询容具使用记录失败", e);
            throw new BizException("查询容具使用记录失败");
        }

        if (result.getCode() != 0) {
            throw new BizException(result.getMessage());
        }

        return result.getModule();
    }

    private List<SealContainerLogDTO> queryLatestSealContainerOperateLogByOpTye(long tenantId, List<Long> warehouseIds, List<String> sealContainerCodes,
                                                                               List<Integer> sealContainerOperateTypeList) {
        Result<List<SealContainerLogDTO>> result;

        if (CollectionUtils.isEmpty(sealContainerCodes) || CollectionUtils.isEmpty(sealContainerOperateTypeList)) {
            return Collections.emptyList();
        }
        try {
            log.info("start invoke sealContainerLogService.queryLatestSealContainerOperateLogByOpTye, storeIdList: {}", warehouseIds);
            result = sealContainerLogService.queryLatestSealContainerOperateLogByOpTye(warehouseIds, tenantId, sealContainerCodes, sealContainerOperateTypeList);
            log.info("end invoke sealContainerLogService.queryLatestSealContainerOperateLogByOpTye, result: {}", result);
        } catch (Exception e) {
            log.error("查询容具使用记录失败", e);
            throw new BizException("查询容具使用记录失败");
        }

        if (result.getCode() != 0) {
            throw new BizException(result.getMessage());
        }

        return result.getModule();
    }

    private SealContainerLogDTO buildSealContainerLogDTO(String tradeOrderNo, Integer orderBizType, String sealContainerCode) {
        SealContainerLogDTO dto = new SealContainerLogDTO();
        dto.setTenantId(LoginContextUtils.getAppLoginTenant());
        dto.setWarehouseId(LoginContextUtils.getAppLoginStoreId());
        dto.setTradeOrderBizType(orderBizType);
        dto.setTradeOrderNo(tradeOrderNo);
        dto.setOpType(SealContainerOperateType.STOCK_IN.getCode());
        dto.setOpTime(LocalDateTime.now());
        dto.setSealContainerCode(sealContainerCode);
        dto.setOperatorId(LoginContextUtils.getAppLoginUser().getAccountId());
        dto.setOperatorName(LoginContextUtils.getAppLoginUser().getEmployeeName());
        Map<String, Object> map = new HashMap<>();
        map.put("source", "APP");
        dto.setExtInfo(JSON.toJSONString(map));

        return dto;
    }

}
