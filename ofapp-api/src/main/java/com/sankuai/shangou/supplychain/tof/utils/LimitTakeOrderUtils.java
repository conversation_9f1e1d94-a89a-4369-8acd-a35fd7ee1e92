package com.sankuai.shangou.supplychain.tof.utils;

import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 限制接单工具类
 *
 * <AUTHOR>
 * @since 2025/6/26
 */
public class LimitTakeOrderUtils {

    /**
     * 判断当前是否已经是限制接单的状态
     */
    public static boolean checkCurrentIsLimit(LimitItemDTO item) {
        if (Objects.isNull(item)) {
            return false;
        }
        //直接限制
        boolean directLimit = Objects.equals(item.getLimitMode(), 1);

        //先提示后限制，如果已经超过限制开始时间，就是限制接单
        boolean hintAfterLimit = Objects.equals(item.getLimitMode(), 2)
                && Objects.nonNull(item.getLimitStartDate())
                && item.getLimitStartDate().isBefore(LocalDateTime.now());

        return directLimit || hintAfterLimit;
    }

}
