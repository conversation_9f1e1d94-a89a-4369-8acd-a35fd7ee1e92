package com.sankuai.shangou.supplychain.tof.component;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.CombinationProductItemDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderItemDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.ChannelOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService;
import com.sankuai.shangou.supplychain.tof.wrapper.FulfillmentOrderServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.SupplyProductTagWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/5/7 17:08
 **/
@Rhino
@Slf4j
public class SealDeliveryTagComponent {
    @Resource
    private FulfillmentOrderSearchThriftService fulfillmentOrderSearchThriftService;

    @Resource
    private SupplyProductTagWrapper supplyProductTagWrapper;

    @Resource
    private FulfillmentOrderServiceWrapper fulfillmentOrderServiceWrapper;

    @Degrade(rhinoKey = "SealDeliveryTagComponent.batchGetSealDeliveryTag", fallBackMethod = "batchGetSealDeliveryTagFallback", timeoutInMilliseconds = 2000)
    public Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> batchGetSealDeliveryTag(List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList) {
        List<ChannelOrderIdKeyReq> channelOrderIdKeyReqs = tradeOrderKeyList.stream()
                .map(tradeOrderKey -> new ChannelOrderIdKeyReq(tradeOrderKey.getOrderBizType(), tradeOrderKey.getChannelOrderId()))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(channelOrderIdKeyReqs)) {
            return Collections.emptyMap();
        }

        List<FulfillmentOrderDetailDTO> fulfillmentOrderDetailDTOS = fulfillmentOrderServiceWrapper.searchFulfillmentOrderListByBatchFulfillmentOrderId(channelOrderIdKeyReqs);

        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> orderIsContainSealTagMap = new HashMap<>();
        for (FulfillmentOrderDetailDTO fulfillmentOrderDetailDTO : fulfillmentOrderDetailDTOS) {
            boolean containSealProduct = Objects.equals(fulfillmentOrderDetailDTO.getIsContainSealProduct(), true);
            orderIsContainSealTagMap.putIfAbsent(new TradeOrderInfoComponent.TradeOrderKey(fulfillmentOrderDetailDTO.getOrderSource(), fulfillmentOrderDetailDTO.getChannelOrderId()), containSealProduct);
        }

        return orderIsContainSealTagMap;
    }

    public Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> batchGetSealDeliveryTagFallback(List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList) {
        log.error("SealDeliveryTagComponent.batchGetSealDeliveryTag 发生降级");
        return Collections.emptyMap();
    }
}
