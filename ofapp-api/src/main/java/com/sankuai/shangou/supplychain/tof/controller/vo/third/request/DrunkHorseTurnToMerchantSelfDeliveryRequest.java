package com.sankuai.shangou.supplychain.tof.controller.vo.third.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023/8/31 17:08
 **/
@Data
public class DrunkHorseTurnToMerchantSelfDeliveryRequest {
    @FieldDoc(
            description = "赋能订单号", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Long orderId;

    @FieldDoc(
            description = "转单后的骑手id", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Long riderAccountId;
}
