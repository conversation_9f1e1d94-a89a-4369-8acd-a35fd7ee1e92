package com.sankuai.shangou.supplychain.tof.controller.vo.picking;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 骑手拣货商品信息.
 *
 * <AUTHOR>
 * @since 2021/11/15 17:03
 */
@TypeDoc(
        description = "骑手拣货任务信息"
)
@Data
public class RiderPickTaskInfoVO {

    // 商品的拣货信息 start

    @FieldDoc(
            description = "拣货任务 ID", requiredness = Requiredness.REQUIRED
    )
    private Long pickTaskId;

    // 商品的拣货信息 end

    // 商品基本信息 start

    @FieldDoc(
            description = "skuId", requiredness = Requiredness.OPTIONAL
    )
    private String skuId;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    private String skuName;

    @FieldDoc(
            description = "upc码", requiredness = Requiredness.REQUIRED
    )
    private Set<String> upcCodes;

    @FieldDoc(
            description = "商品图片URL", requiredness = Requiredness.REQUIRED
    )
    private String picUrl;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    private String specification;

    @FieldDoc(
            description = "购买数量", requiredness = Requiredness.REQUIRED
    )
    private Integer count;

    @FieldDoc(
            description = "温度属性",requiredness = Requiredness.OPTIONAL
    )
    private String temperatureAttributeType;


    @FieldDoc(
            description = "推荐库位列表",requiredness = Requiredness.OPTIONAL
    )
    private List<RecommendLocationBatch.LocationInfo> locationInfos ;

    @FieldDoc(
            description = "推荐批次列表",requiredness = Requiredness.OPTIONAL
    )
    private List<RecommendLocationBatch.BatchInfo> batchInfos ;

    @FieldDoc(
            description = "所需温区库存是否充足",requiredness = Requiredness.OPTIONAL
    )
    private Boolean requiredTemperatureAreaStockIsEnough ;

    @FieldDoc(
            description = "商品是否是sn商品",requiredness = Requiredness.OPTIONAL
    )
    private Boolean isSnProduct;

    // 商品基本信息 end

    @FieldDoc(
            description = "该拣货任务明细是否有部分退款标记",requiredness = Requiredness.OPTIONAL
    )
    private boolean havePartRefundFlag;

    @FieldDoc(
            description = "是否是高采购价货品",requiredness = Requiredness.OPTIONAL
    )
    private Boolean isHighWacGoods ;

    @FieldDoc(
            description = "是否是短保货品",requiredness = Requiredness.OPTIONAL
    )
    private Boolean isShortExpirationGoods;

    @FieldDoc(
            description = "实拍图Url",requiredness = Requiredness.OPTIONAL
    )
    private List<String> realPicUrlList;

    @FieldDoc(
            description = "是否展示实拍图",requiredness = Requiredness.OPTIONAL
    )
    private Boolean showRealPic;

    @FieldDoc(
            description = "拣货项是否是封签交付",requiredness = Requiredness.OPTIONAL
    )
    private Boolean isSealDelivery;
}
