package com.sankuai.shangou.supplychain.tof.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 渠道枚举类
 */
@AllArgsConstructor
@Getter
@Slf4j
public enum ChannelTypeEnum {
    MEITUAN(100, "美团", "美团"),
    ELEM(200, "饿了么", "饿了么"),
    JD2HOME(300, "京东到家", "京东"),
    MT_MEDICINE(400, "美团医药", "美团医药"),
    YOU_ZAN(500, "有赞", "有赞"),
    MT_DRUNK_HORSE(700, "微商城", "微商城"),
    QUAN_QIU_WA(800, "全球蛙", "全球"),
    SELF_CHANNEL(900, "自有渠道", "自有");
    /**
     * 渠道编码
     */
    private int channelId;
    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道简称
     */
    private String channelAbbrName;

    /**
     * 注：此方法不支持私有渠道，已废弃
     */
    @Deprecated
    public static ChannelTypeEnum findByChannelId(int channelId) {
        for (ChannelTypeEnum channelTypeEnum : ChannelTypeEnum.values()) {
            if (channelTypeEnum.getChannelId() == channelId) {
                return channelTypeEnum;
            }
        }
        return null;
    }

    /**
     * 通过渠道ID查询渠道名称
     *
     * 注：此方法为兼容性方法，兼容之前通过枚举获取名称的逻辑，因此不抛出异常
     */
    public static String findChannelNameByChannelId(int channelId) {
        // 默认走枚举类
        for (ChannelTypeEnum channelTypeEnum : ChannelTypeEnum.values()) {
            if (channelTypeEnum.getChannelId() == channelId) {
                return channelTypeEnum.getChannelName();
            }
        }

        return null;
    }

}
