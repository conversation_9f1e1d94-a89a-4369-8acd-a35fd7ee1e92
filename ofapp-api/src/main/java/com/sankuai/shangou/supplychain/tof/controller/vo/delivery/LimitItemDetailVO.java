package com.sankuai.shangou.supplychain.tof.controller.vo.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/11/4 16:34
 **/
@SuppressWarnings("unused")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LimitItemDetailVO {
    private String reason;

    private String skipUrl;

    private String skipButtonText;

    private String releaseLimitText;

    private Integer skipUrlType;

    @FieldDoc(description = "限制接单类型，1-直接限制，2-先提示后限制")
    private Integer limitMode;

    @FieldDoc(description = "限制开始时间，配合limitMode=2使用")
    private String limitStartDate;

    @FieldDoc(description = "限制装备skuId，当限制是装备未领取的时候有值")
    private Map<String, Integer> limitSkuIdNumMap;

    @FieldDoc(description = "是否灰度门店")
    private Boolean isGrayStore;

}
