package com.sankuai.shangou.supplychain.tof.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/5/14
 * 商货品效期单位枚举类
 */
@Getter
public enum DepotGoodsExpireUnitEnum {

    /**
     * 最小效期单位，天。DAY = (1 * DAY)
     */
    DAY("DAY", 1, "日"),

    /**
     * 月。 MONTH = (30 * DAY)
     */
    MONTH("MONTH", 30, "月"),

    /**
     * 年。 YEAR = (365 * DAY)
     */
    YEAR("YEAR", 365, "年");


    /** 效期单位 code */
    private final String code;

    /** 效期单位转换成最小单位（天）的比率 */
    private final int ratio;

    /** 描述 */
    private final String description;

    DepotGoodsExpireUnitEnum(String code, int ratio, String description) {
        this.code = code;
        this.ratio = ratio;
        this.description = description;
    }

    /**
     * 根据 code 查找
     * @param code 效期单位 code
     * @return     DepotGoodsExpireUnitEnum
     */
    public static DepotGoodsExpireUnitEnum findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (DepotGoodsExpireUnitEnum expireUnit : values()) {
            if (expireUnit.code.equals(code)) {
                return expireUnit;
            }
        }

        return null;
    }

}
