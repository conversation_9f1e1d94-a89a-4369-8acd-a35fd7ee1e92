package com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/8/30 16:17
 **/
@Data
public class TakeAwayRequest {

    @FieldDoc(
            description = "运单id", requiredness = Requiredness.OPTIONAL
    )
    private Long deliveryOrderId;

    @FieldDoc(
            description = "定位信息", requiredness = Requiredness.OPTIONAL
    )
    private LocationInfo locationInfo;

    @FieldDoc(
            description = "定位信息", requiredness = Requiredness.OPTIONAL
    )
    private Integer channelId;


    @FieldDoc(
            description = "定位信息", requiredness = Requiredness.OPTIONAL
    )
    private String channelOrderId;

    public String validate() {
        if (deliveryOrderId == null || deliveryOrderId <= 0L) {
            return "运单id不合法";
        }

        return null;
    }
}
