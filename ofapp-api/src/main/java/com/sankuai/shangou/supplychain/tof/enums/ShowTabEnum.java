package com.sankuai.shangou.supplychain.tof.enums;

/**
 * <AUTHOR>
 * @date 2024-11-07
 * @email <EMAIL>
 */
public enum ShowTabEnum {

    NEW_TASK(10, "新任务"),
    NEW_TASK_PICK_ONLY(11, "新任务-仅拣货"),
    NEW_TASK_DELIVERY_ONLY(12, "新任务-仅配送"),
    NEW_TASK_PICK_AND_DELIVERY(13, "新任务-拣配"),

    WAIT_TAKEN_GOODS(20, "待取货"),
    WAIT_TAKEN_GOODS_PICK_ONLY(21, "待取货-拣配任务"),
    WAIT_TAKEN_GOODS_PICK_AND_DELIVERY(22, "待取货-配送任务"),

    IN_DELIVERY(30, "配送中"),


    THIRD_NEW_PICKING(41, "三方-拣货任务"),
    THIRD_WAIT_PICKING(42, "三方-待拣货"),
    THIRD_IN_DELIVERY(43, "三方-配送中"),
    THIRD_EXCEPTION(44, "三方-异常"),


    ;
    private int code;
    private String desc;

    ShowTabEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


}
