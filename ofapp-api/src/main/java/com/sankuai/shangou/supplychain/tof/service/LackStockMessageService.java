package com.sankuai.shangou.supplychain.tof.service;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.common.enums.ChannelTypeEnum;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.dto.request.AxBPrivacyPhoneRequest;
import com.meituan.shangou.saas.dto.response.PrivacyPhoneResponse;
import com.meituan.shangou.saas.dto.response.UserDataResponse;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderDetailReq;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsOrderDetailResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderBaseVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderDetailVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderOptLogVo;
import com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchService;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.service.MtUserThriftService;
import com.meituan.shangou.saas.service.PrivacyPhoneThriftService;
import com.meituan.shangou.saas.tenant.thrift.DepartmentV2ThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.dto.EmployeeInfoV2Dto;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.request.EmployeeInfoInAllDepartmentsDetailV2Request;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.response.EmployeeInfoInAllDepartmentsDetailV2Response;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QuerySimpleAccountInfoListResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QuerySimpleAccountInfoRequest;
import com.sankuai.meituan.shangou.saas.message.request.sms.SmsSendRequest;
import com.sankuai.meituan.shangou.saas.message.response.sms.SmsSendResponse;
import com.sankuai.meituan.shangou.saas.message.service.SmsThriftService;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.thrift.publisher.response.ResponseCode;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.warehouse.AbnOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import com.sankuai.shangou.supplychain.tof.config.LionConfigUtils;
import com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.request.QueryVirtualPhoneRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.response.QueryVirtualPhoneResponse;
import com.sankuai.shangou.supplychain.tof.enums.ErrorCodeEnum;
import com.sankuai.shangou.supplychain.tof.wrapper.OrderQueryWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.SquirrelWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-12-19
 * @email <EMAIL>
 */
@Slf4j
@Service
public class LackStockMessageService {

    @Resource
    private SquirrelWrapper squirrelWrapper;
    @Resource
    private AbnOrderService abnOrderService;
    @Resource
    private SmsThriftService smsThriftService;
    @Resource
    private OrderQueryWrapper orderQueryWrapper;
    @Value("${sms.templateNo}")
    private String smsTemplateNo;
    @Resource
    private MtUserThriftService mtUserThriftService;
    @Resource
    private OcmsOrderSearchService ocmsOrderSearchService;
    @Resource
    private DepartmentV2ThriftService departmentV2ThriftService;
    @Resource
    private AuthThriftService.Iface authThriftService;
    @Resource
    private PrivacyPhoneThriftService privacyPhoneThriftService;

    private static final int RIDER_CALL_CUSTOMER_CODE = 13;

    private static final int AUTH_INFO_NOT_AUTHORIZED = 107;


    @MethodLog(logRequest = true, logResponse = true)
    public void sendLackStockMessage(long warehouseId, int orderBizType, String channelOrderId) {
        TResult<List<AbnOrderDTO>> unprocessedAbnOrderListResult = abnOrderService.getUnprocessed(warehouseId);
        if (!unprocessedAbnOrderListResult.isSuccess() || CollectionUtils.isEmpty(unprocessedAbnOrderListResult.getData())) {
            throw new SystemException("未找到异常单，可能已处理");
        }
        Optional<AbnOrderDTO> abnOrderDTOOptional = unprocessedAbnOrderListResult.getData()
                .stream()
                .filter(abnOrderDTO -> Objects.equals(abnOrderDTO.getSourceOrderNo(), channelOrderId) && Objects.equals(orderBizType, abnOrderDTO.getSourceType()))
                .findFirst();
        if (!abnOrderDTOOptional.isPresent()) {
            throw new SystemException("未找到异常单，可能已处理");
        }

        boolean setSuccess = squirrelWrapper.setnx(SquirrelWrapper.LACK_STOCK_MESSAGE_CATEGORY, buildSquirrelKey(orderBizType, channelOrderId), StringUtils.EMPTY);
        if (!setSuccess) {
            throw new BizException("已发送过短信，无需再次发送！");
        }
        AbnOrderDTO abnOrderDTO = abnOrderDTOOptional.get();

        try {
            BizOrderModel bizOrderModel = orderQueryWrapper.queryOrderDetailByBizType(AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId(), warehouseId, abnOrderDTO.getSourceType(), abnOrderDTO.getSourceOrderNo());

            String phone;

            Optional<String> buyerPhone = queryBuyerPhone(bizOrderModel.getUserId());
            //有下单人取下单人 失败了取收货人
            if (buyerPhone.isPresent()) {
                phone = buyerPhone.get();
            } else {
                if (Objects.equals(bizOrderModel.getOrderBizType(), DynamicOrderBizType.MEITUAN_DRUNK_HOURSE.getValue())) {
                    //订单侧其他渠道的的隐私号和正常号赋值反了。
                    //微商城渠道是正确的，但其他渠道取隐私号反而是正常号码
                    phone = bizOrderModel.getDeliveryModel().getUserPhone();
                } else {
                    phone = bizOrderModel.getRecipientRealPhone();
                }
            }
            SmsSendRequest request = new SmsSendRequest();
            request.setTenantId(AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId());
            request.setTemplateNo(smsTemplateNo);
            request.setMobile(phone);
            Map<String, String> templateParamMap = Maps.newHashMap();
            templateParamMap.put("orderNo", bizOrderModel.getViewOrderId());
            request.setTemplateParam(JSON.toJSONString(templateParamMap));
            SmsSendResponse sendResponse = smsThriftService.send(request);
            log.info("invoke smsThriftService.send, request = {}, response ={}", request, sendResponse);
            if (!Objects.equals(sendResponse.getCode(), 0)) {
                throw new SystemException("调取发送短信服务异常");
            }
        } catch (Exception e) {
            //有异常要把锁释放了
            squirrelWrapper.delete(SquirrelWrapper.LACK_STOCK_MESSAGE_CATEGORY, buildSquirrelKey(orderBizType, channelOrderId));
            log.error("send sms message error", e);
            throw new SystemException("调取发送短信服务异常");
        }
    }

    @MethodLog(logRequest = true, logResponse = true)
    public QueryVirtualPhoneResponse queryBuyerVirtualPhone(QueryVirtualPhoneRequest request) {
//        try {
            Long tenantId = AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId();
            QueryVirtualPhoneResponse virtualPhoneResponse = new QueryVirtualPhoneResponse();
            OrderDetailVo orderDetail = getOrderDetailFromMng(request, tenantId);
            if (orderDetail == null) {
                throw new BizException("获取订单详情失败");
            }


            // 判断订单是否超过终态指定长度，若是不能联系用户
            if (!isOrderCanCallUser(orderDetail.getOrderOpLogList(), request.getStoreId(), tenantId, AppLoginContextHolder.getAppLoginContext().getLoginUser().getEmployeeId())) {
                throw new BizException(ErrorCodeEnum.ORDER_CANT_BE_CONTACT.getCode(), ErrorCodeEnum.ORDER_CANT_BE_CONTACT.getMessage());
            }

            // 如果没有使用隐私号，直接返回真实手机号
            OrderBaseVo orderBase = orderDetail.getOrderBaseDto();
            if (NumberUtils.INTEGER_ZERO.equals(orderBase.getUsePrivacyPhone())) {
                // 歪马订单，没有选中隐私号保护的订单，订单完成后24小时后隐藏手机号
                if (isOrderCompleteOverTime(orderBase)){
                    throw new BizException(ErrorCodeEnum.ORDER_CANT_BE_CONTACT.getCode(), ErrorCodeEnum.ORDER_CANT_BE_CONTACT.getMessage());
                }
                virtualPhoneResponse.setPhoneNo(orderBase.getReceiverPhone());
                return virtualPhoneResponse;
            }


            // 获取百川登录账号手机号
            Map<Long, AccountInfoVo> accountInfoMap = querySimplyAccountInfoByIds(Lists.newArrayList(AppLoginContextHolder.getAppLoginContext().getLoginUser().getAccountId()));
            AccountInfoVo accountInfo = accountInfoMap.get(AppLoginContextHolder.getAppLoginContext().getLoginUser().getAccountId());
            if (accountInfo == null || StringUtils.isBlank(accountInfo.getMobile())) {
                throw new BizException("获取登录账号信息失败--账号或其手机号为空");
            }
            String mtBindPhone = mtUserThriftService.getMtUserInfo(orderBase.getUserId()).getPhone();
            // 调用AXB获取下单人备用隐私号
            PrivacyPhoneResponse riderBindPhoneResponse = applyAxB(orderBase, accountInfo.getMobile(), mtBindPhone);
            // 调用成功
            if (riderBindPhoneResponse.getStatus().code.equals(StatusCodeEnum.SUCCESS.getCode())) {
                virtualPhoneResponse.setPhoneNo(riderBindPhoneResponse.getPrivacyPhone());
                virtualPhoneResponse.setBackUpPhoneNo(riderBindPhoneResponse.getBackupPrivacyPhones());
            } else if (Objects.equals(riderBindPhoneResponse.getStatus().getCode(), StatusCodeEnum.AUTH_INFO_NOT_AUTHORIZED.getCode())) {
                throw new BizException(ErrorCodeEnum.PRIVACY_NUMBER_AUTH_INFO_NOT_AUTHORIZED.getCode(), ErrorCodeEnum.PRIVACY_NUMBER_AUTH_INFO_NOT_AUTHORIZED.getMessage());
            }
            else {
                throw new BizException("未查询到下单人隐私号:" + riderBindPhoneResponse.getStatus().getMessage());
            }

            // 使用了隐私号，去获取真实手机号
            String receiverPhone = null;
            if (request.getChannelId() == ChannelTypeEnum.MT_DRUNK_HORSE.getValue()) {
                receiverPhone = orderBase.getReceiverPhone();
            } else if (request.getChannelId() == ChannelTypeEnum.MEITUAN.getValue()) {
                receiverPhone = orderBase.getReceiverPrivacyPhone();
            }
            if (StringUtils.isNotBlank(receiverPhone)) {
                if (!receiverPhone.equals(mtBindPhone)) {
                    PrivacyPhoneResponse riderReceiverResponse = applyAxB(orderBase, accountInfo.getMobile(), receiverPhone);
                    // 获取隐私号失败--使用原逻辑返回拨打号码
                    if (riderReceiverResponse.getStatus().code.equals(StatusCodeEnum.SUCCESS.getCode())) {
                        virtualPhoneResponse.setBindPhoneNo(riderReceiverResponse.getPrivacyPhone());
                        virtualPhoneResponse.setBindBackupPhoneNo(riderReceiverResponse.getBackupPrivacyPhones());
                    }
                }
            }

            return virtualPhoneResponse;
//        } catch (Exception e) {
//            log.error("invoke orderQueryWrapper.queryOrderDetailByBizType error", e);
//            throw new SystemException("查询用户隐私号异常，请稍后再试");
//        }
    }


    private boolean isOrderCompleteOverTime(OrderBaseVo orderBase) {
        Integer orderStatus = orderBase.getOrderStatus();
        if (Objects.equals(OrderStatusEnum.COMPLETED.getValue(), orderStatus) || Objects.equals(OrderStatusEnum.CANCELED.getValue(), orderStatus)){
            Long completeTime = Objects.equals(OrderStatusEnum.COMPLETED.getValue(), orderStatus) && orderBase.getCompleteTime() != null
                    ? orderBase.getCompleteTime() : orderBase.getUpdateTime();
            int thresholdHour = LionConfigUtils.getDrunkhorsePrivatePhoneShowHour();
            long now = System.currentTimeMillis();
            return completeTime != null && TimeUnit.MILLISECONDS.toHours( now - completeTime) >= thresholdHour;
        }
        return false;
    }

    /**
     * 判断是否可以联系用户
     *
     */
    private boolean isOrderCanCallUser(List<OrderOptLogVo> orderOpLogList, Long storeId, Long tenantId, Long employeeId) {
        // 若为空，肯定未超过联系期限
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(orderOpLogList)) {
            return true;
        }
        // 获取订单取消/完成时间
        long cancelTime = 0, doneTime = 0;
        for (OrderOptLogVo ele: orderOpLogList) {
            if (OrderStatusEnum.CANCELED.getDesc().equals(ele.getOptContent())) {
                cancelTime = ele.getOptTime() / 1000;
            } else if (OrderStatusEnum.COMPLETED.getDesc().equals(ele.getOptContent())) {
                doneTime = ele.getOptTime() / 1000;
            }
        }

        long currTime = System.currentTimeMillis() / 1000;
        if (Lion.getConfigRepository().getBooleanValue("minus_canCallUser_time_switch", true)
                && isMinusPrivatePhoneTimePosition(tenantId, employeeId)) {
            log.info("enter 隐私号码入口时间缩短,employeeId:{}, storeId:{}", employeeId, storeId);
            if (cancelTime != 0 && (currTime - cancelTime) > LionConfigUtils.newContactUserDuration()) {
                return false;
            }
            return doneTime == 0 || (currTime - doneTime) <= LionConfigUtils.newContactUserDuration();
        }
        // 取消时间距离当前超过7天
        if (cancelTime != 0 && (currTime - cancelTime) > LionConfigUtils.contactUserDuration()) {
            return false;
        }
        // 完成时间距离当前超过7天
        return doneTime == 0 || (currTime - doneTime) <= LionConfigUtils.contactUserDuration();
    }


    private static String buildSquirrelKey(int orderBizType, String channelOrderId) {
        return orderBizType + "_" + channelOrderId;
    }

    private Optional<String> queryBuyerPhone(Long userId) {
        try {
            UserDataResponse response = mtUserThriftService.getMtUserInfo(userId);
            log.info("invoke mtUserThriftService.getMtUserInfo, userId = {}, response = {}", userId, response);
            if (response == null
                    || response.getStatus() == null
                    || !response.getStatus().code.equals(StatusCodeEnum.SUCCESS.getCode())
                    || StringUtils.isBlank(response.getPhone())) {
                throw new SystemException("获取美团用户信息失败");
            }
            return Optional.ofNullable(response.getPhone());
        } catch (Exception e) {
            log.info("获取美团用户信息失败", e);
            return Optional.empty();
        }
    }

    /**
     * 从ordermng获取订单详情
     * @param virtualPhoneReq 请求
     * @param tenantId 租户ID
     */
    private OrderDetailVo getOrderDetailFromMng(QueryVirtualPhoneRequest virtualPhoneReq, long tenantId) {
        OcmsOrderDetailReq orderDetailReq = new OcmsOrderDetailReq();
        orderDetailReq.setChannelId(virtualPhoneReq.getChannelId());
        orderDetailReq.setTenantId(tenantId);
        orderDetailReq.setChannelOrderId(virtualPhoneReq.getChannelOrderId());
        orderDetailReq.setOperator(AppLoginContextHolder.getAppLoginContext().getLoginUser().getAccountId());
        try {
            OcmsOrderDetailResponse orderDetailResponse = ocmsOrderSearchService.orderDetail(orderDetailReq);
            log.info("invoke ocmsOrderSearchService.orderDetail, request = {}, response = {}", orderDetailReq, orderDetailResponse);
            if (orderDetailResponse.getResponseStatus() != 0) {
                log.warn("get orderDetail from ordermng fail, channelOrderId:{}, tenantId:{}, channelId:{}, reason:{}",
                        virtualPhoneReq.getChannelOrderId(), tenantId, virtualPhoneReq.getChannelId(), orderDetailResponse.getMsg());
                return null;
            }
            return orderDetailResponse.order;
        } catch (Exception ex) {
            log.warn("get orderDetail from ordermng fail, channelOrderId:{}, tenantId:{}, channelId:{}, reason:{}",
                    virtualPhoneReq.getChannelOrderId(), tenantId, virtualPhoneReq.getChannelId(), ex.getMessage());
            return null;
        }
    }

    private Boolean isMinusPrivatePhoneTimePosition(Long tenantId, Long employeeId) {

        try {
            List<Long> configPositionIds = LionConfigUtils.getMinusPrivatePhonePositionIds();
            EmployeeInfoInAllDepartmentsDetailV2Request req = new EmployeeInfoInAllDepartmentsDetailV2Request();
            req.setTenantId(tenantId);
            req.setEmployeeId(employeeId);
            EmployeeInfoInAllDepartmentsDetailV2Response response = departmentV2ThriftService.queryEmployeeInfoInAllDepartments(req);
            log.info("EmployeeInfoInAllDepartmentsDetailV2Response:{}", response);
            Long positionId = 0l;
            List<Long> positionIdList = new ArrayList<>();
            if(response != null && org.apache.commons.collections4.CollectionUtils.isNotEmpty(response.getEmployeeInfoList())) {
                positionIdList = response.getEmployeeInfoList().stream().filter(employeeInfoV2Dto -> employeeId.equals(employeeInfoV2Dto.getEmployeeId())).map(EmployeeInfoV2Dto::getPositionId).collect(Collectors.toList());
                log.info("positionIdList:{}", positionIdList);
            }
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(positionIdList)) {
                positionId = positionIdList.get(0);
            }
            return configPositionIds.contains(positionId);
        } catch (Exception e) {
            log.error("isMinusPrivatePhoneTimePosition error", e);
            return false;
        }
    }

    private Map<Long, AccountInfoVo> querySimplyAccountInfoByIds(List<Long> accountIds) {

        if (org.apache.commons.collections.CollectionUtils.isEmpty(accountIds)) {
            return Maps.newHashMap();
        }
        Set<Long> finalAccountIds = accountIds.stream().filter(Objects::nonNull).collect(Collectors.toSet());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(accountIds)) {
            return Maps.newHashMap();
        }

        QuerySimpleAccountInfoRequest request = new QuerySimpleAccountInfoRequest();
        request.setAccountIds(Lists.newArrayList(finalAccountIds));
        try {
            QuerySimpleAccountInfoListResponse response = authThriftService.querySimpleAccountInfoList(request);
            if (response.getResult().code != ResultCodeEnum.SUCCESS.getValue()) {
                log.info("获取账号信息错误 response [{}].", request);
                throw new com.sankuai.meituan.shangou.saas.common.exception.BizException(response.result.getCode(), response.result.getMsg());
            }

            return org.apache.commons.collections.CollectionUtils.isEmpty(response.getAccountInfoList()) ? Maps.newHashMap()
                    : Maps.uniqueIndex(response.getAccountInfoList(), AccountInfoVo::getAccountId);
        } catch (Exception e) {
            log.warn("查询账号信息异常：", e);
            throw new com.sankuai.meituan.shangou.saas.common.exception.BizException("查询账号信息异常");
        }
    }

    /**
     * 获取订单对应的axb隐私号
     *
     * @param orderBaseVo 订单基本信息
     * @param callPhone   拨打手机号
     * @param calledPhone 被拨打手机号
     * @return
     */
    private PrivacyPhoneResponse applyAxB(OrderBaseVo orderBaseVo, String callPhone, String calledPhone) {
        AxBPrivacyPhoneRequest request = new AxBPrivacyPhoneRequest();
        request.setUserId(orderBaseVo.getUserId());
        request.setUuid(orderBaseVo.getChannelOrderId());
        request.setChannelId(orderBaseVo.getChannelId());
        request.setUserPhone(callPhone);
        request.setCalledPhone(calledPhone);
        request.setCityId(orderBaseVo.getCityId());
        request.setDuration(LionConfigUtils.axbPrivacyPhoneValidTime());
        // 骑手呼叫用户
        request.setScenarioType(RIDER_CALL_CUSTOMER_CODE);
        PrivacyPhoneResponse privacyPhoneResponse = privacyPhoneThriftService.applyAxbPhone(request);
        log.info("invoke privacyPhoneThriftService.applyAxbPhone request = {}, response = {}", request, privacyPhoneResponse);
        return privacyPhoneResponse;
    }
}
