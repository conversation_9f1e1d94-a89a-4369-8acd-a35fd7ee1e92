package com.sankuai.shangou.supplychain.tof.component;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.shangou.empower.rider.client.common.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryRiskControlOrder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryDeliveryRiskControlOrderRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.QueryDeliveryRiskControlOrderResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/21 20:08
 **/
@Rhino
@Slf4j
public class DeliveryRiskControlComponent {
    @Resource
    private RiderQueryThriftService riderQueryThriftService;

    @Degrade(rhinoKey = "DeliveryRiskControlComponent.queryRiskControlOrderMap", fallBackMethod = "queryRiskControlOrderMapFallback", timeoutInMilliseconds = 2000)
    public Map<TradeOrderInfoComponent.TradeOrderKey, TDeliveryRiskControlOrder> queryRiskControlOrderMap(List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList) {
        if (CollectionUtils.isEmpty(tradeOrderKeyList)) {
            return Collections.emptyMap();
        }

        QueryDeliveryRiskControlOrderRequest request = new QueryDeliveryRiskControlOrderRequest();

        List<QueryDeliveryRiskControlOrderRequest.ViewOrderKey> viewOrderKeys = tradeOrderKeyList.stream()
                .map(tradeOrderKey -> new QueryDeliveryRiskControlOrderRequest.ViewOrderKey(tradeOrderKey.getChannelOrderId(), tradeOrderKey.getOrderBizType()))
                .collect(Collectors.toList());
        request.setViewOrderKeys(viewOrderKeys);
        log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryRiskControlOrderByViewOrderIdList begin. request:{}", request);
        QueryDeliveryRiskControlOrderResponse response = riderQueryThriftService.queryRiskControlOrderByViewOrderIdList(request);
        log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryRiskControlOrderByViewOrderIdList end. request:{}, response:{}",
                request, response);
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new BizException(response.getStatus().getMsg());
        }

        return Optional.ofNullable(response.getDeliveryRiskControlOrders())
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(tDeliveryRiskControlOrder -> {
                    return new TradeOrderInfoComponent.TradeOrderKey(tDeliveryRiskControlOrder.getOrderBizType(), tDeliveryRiskControlOrder.getViewOrderId());
                }, Function.identity()));
    }

    public Map<TradeOrderInfoComponent.TradeOrderKey, TDeliveryRiskControlOrder> queryRiskControlOrderMapFallback(List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList) {
        log.error("DeliveryRiskControlComponent.queryRiskControlOrderMap 发生降级");
        return Collections.emptyMap();
    }
}
