package com.sankuai.shangou.supplychain.tof;

import com.dianping.rhino.spring.RhinoConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;
import org.springframework.retry.annotation.EnableRetry;

@ImportAutoConfiguration()
@RhinoConfiguration
@PropertySource("classpath:META-INF/app.properties")
@ComponentScan({"com.sankuai.shangou.supplychain.tof", "com.sankuai.shangou.commons.auth.login.app", "com.sankuai.shangou.commons.utils.log"})
@ImportResource("classpath:thrift-client.xml")
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@EnableRetry
public class StartApp {
    private static final Logger log = LoggerFactory.getLogger(StartApp.class);

    public static void main(String[] args) {
        SpringApplication.run(StartApp.class, args);
        log.info("服务启动成功！");
    }
}