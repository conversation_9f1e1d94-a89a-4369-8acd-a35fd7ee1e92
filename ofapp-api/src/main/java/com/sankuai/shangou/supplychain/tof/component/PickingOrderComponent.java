package com.sankuai.shangou.supplychain.tof.component;

import com.google.common.collect.Lists;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.PageQueryRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.PickingOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-09-03
 * @email <EMAIL>
 */
@Slf4j
@Service
public class PickingOrderComponent {

    @Resource
    private TradeShippingOrderService tradeShippingOrderService;

    public PickingOrderVO buildPickingOrderVO(TradeShippingOrderDTO tradeShippingOrderDTO) {
        PickingOrderVO pickingOrderVO = new PickingOrderVO();
        pickingOrderVO.setWarehouseId(tradeShippingOrderDTO.getWarehouseId());
        pickingOrderVO.setMerchantId(tradeShippingOrderDTO.getMerchantId());
        pickingOrderVO.setOrderNo(tradeShippingOrderDTO.getOrderNo());
        pickingOrderVO.setTradeChannelType(tradeShippingOrderDTO.getTradeChannelType());
        pickingOrderVO.setTradeOrderNo(tradeShippingOrderDTO.getTradeOrderNo());
        pickingOrderVO.setStatus(tradeShippingOrderDTO.getStatus());
        pickingOrderVO.setCreateTime(tradeShippingOrderDTO.getCreateTime());
        pickingOrderVO.setShipTime(tradeShippingOrderDTO.getShipTime());
        pickingOrderVO.setUpdateTime(tradeShippingOrderDTO.getUpdateTime());
        pickingOrderVO.setItems(tradeShippingOrderDTO.getItems());
        pickingOrderVO.setId(tradeShippingOrderDTO.getId());
        pickingOrderVO.setOperatorId(tradeShippingOrderDTO.getOperatorId());
        pickingOrderVO.setPickingCheckPictureUrlList(tradeShippingOrderDTO.getPickingCheckPictureUrlList());
        pickingOrderVO.setFulfillmentOrderId(tradeShippingOrderDTO.getFulfillmentOrderId());
        pickingOrderVO.setPickConsumableItems(tradeShippingOrderDTO.getPickConsumableItems());
        pickingOrderVO.setIsSealDegrade(tradeShippingOrderDTO.getIsSealDegrade());
        pickingOrderVO.setIsPickDeliverySplit(Objects.nonNull(tradeShippingOrderDTO.getIsPickDeliverySplit()) && tradeShippingOrderDTO.getIsPickDeliverySplit());
        pickingOrderVO.setOperatorName(tradeShippingOrderDTO.getOperatorName());
        return pickingOrderVO;
    }

    public List<TradeShippingOrderDTO> queryByTradeOrderIds(long warehouseId, List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList) {
        if (CollectionUtils.isEmpty(tradeOrderKeyList)) {
            return Lists.newArrayList();
        }
        Map<Integer, List<TradeOrderInfoComponent.TradeOrderKey>> orderBizTypeMap = tradeOrderKeyList.stream().collect(Collectors.groupingBy(TradeOrderInfoComponent.TradeOrderKey::getOrderBizType));
        List<TradeShippingOrderDTO> res = Lists.newArrayList();
        for (Map.Entry<Integer, List<TradeOrderInfoComponent.TradeOrderKey>> entry : orderBizTypeMap.entrySet()) {
            TResult<List<TradeShippingOrderDTO>> result = tradeShippingOrderService.getByTradeOrderNos(warehouseId, entry.getKey(), IListUtils.mapTo(entry.getValue(), TradeOrderInfoComponent.TradeOrderKey::getChannelOrderId));
            if (!result.isSuccess()) {
                throw new ThirdPartyException("查询拣货出库单失败");
            }
            res.addAll(Optional.ofNullable(result.getData()).orElse(Lists.newArrayList()));
        }
        return res;
    }

    public List<TradeShippingOrderDTO> getRecentListByOperatorIdAndStatusList(Long warehouseId, @Nullable Long operatorId, List<TradeShippingOrderStatus> tradeShippingOrderStatus) {
        TResult<List<TradeShippingOrderDTO>> result = tradeShippingOrderService.getRecentListByOperatorIdAndStatusList(warehouseId, operatorId, IListUtils.mapTo(tradeShippingOrderStatus, TradeShippingOrderStatus::getCode));
        if (!result.isSuccess()) {
            throw new ThirdPartyException("查询拣货出库单列表错误");
        }
        return result.getData();
    }

    public List<TradeShippingOrderDTO> getByOperatorIdAndStatusListAndDate(Long warehouseId, @Nullable Long operatorId, List<TradeShippingOrderStatus> tradeShippingOrderStatus, LocalDateTime startTime, LocalDateTime endTime) {
        TResult<List<TradeShippingOrderDTO>> result = tradeShippingOrderService.getByOperatorIdAndStatusListAndDate(warehouseId, operatorId, IListUtils.mapTo(tradeShippingOrderStatus, TradeShippingOrderStatus::getCode), startTime, endTime);
        if (!result.isSuccess()) {
            throw new ThirdPartyException("查询拣货出库单列表错误");
        }
        return result.getData();
    }


    public List<TradeShippingOrderDTO> queryAllTradeShippingOrders(Long warehouseId, long accountId,long lastUpdateTime) {
        TResult<List<TradeShippingOrderDTO>> result = tradeShippingOrderService.queryAllTradeShippingOrders(warehouseId, accountId, lastUpdateTime);
        if (!result.isSuccess()) {
            throw new ThirdPartyException("查询拣货出库单列表错误");
        }
        return result.getData();
    }
}
