package com.sankuai.shangou.supplychain.tof.controller.vo.abnormal.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-12-11
 * @email <EMAIL>
 */
@TypeDoc(
        description = "异常单操作-发送缺货短信"
)
@Data
public class SendLackStockMessageRequest {

    @FieldDoc(
            description = "订单渠道", requiredness = Requiredness.REQUIRED
    )
    private Integer orderBizType;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    private String channelOrderId;

}
