package com.sankuai.shangou.supplychain.tof.controller.vo.abnormal;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

@TypeDoc(description = "异常单及其缺货详情信息")
@Data
public class AbnormalOrderVO {

    /** 异常订单相关内容 */
    //异常单id
    private String abnOrderId;
    //异常单创建原因
    private Integer abnormalOrderType;
    //异常单item vo
    private List<AbnOrderItemVO> items;

    private int orderBizType;

    private String tradeOrderNo;

    /** 操作*/
    //是否已发送短信
    private boolean isAlreadySendMessage;

    //可操作按钮，300-退款 310-取消
    private List<Integer> couldOperateItemList;

}
