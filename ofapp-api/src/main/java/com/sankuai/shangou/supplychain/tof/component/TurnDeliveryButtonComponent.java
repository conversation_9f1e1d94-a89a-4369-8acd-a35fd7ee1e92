package com.sankuai.shangou.supplychain.tof.component;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.saas.common.utils.CollectionUtils;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigThriftService;
import com.sankuai.shangou.logistics.delivery.poi.dto.SelfDeliveryPoiConfigDTO;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.TradeOrderVO;
import com.sankuai.shangou.supplychain.tof.enums.AuthCodeEnum;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import com.sankuai.shangou.supplychain.tof.utils.SceneParseUtils;
import com.sankuai.shangou.supplychain.tof.wrapper.AuthThriftWrapper;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/4/25 15:14
 **/
@Rhino
@Slf4j
public class TurnDeliveryButtonComponent {

    @Resource
    private SelfDeliveryPoiConfigThriftService selfDeliveryPoiConfigThriftService;

    @Resource
    private AuthThriftWrapper authThriftWrapper;

    private static final Integer BOOLEAN_TRUE = 1;

    @Degrade(rhinoKey = "TurnDeliveryButtonComponent.queryTurnDeliveryButton", fallBackMethod = "queryTurnDeliveryButtonFallback", timeoutInMilliseconds = 2000)
    public Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> queryTurnDeliveryButton(Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap,
                                                                                       Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> sealDeliveryTagMap) {
        //1.门店需要开启转配送
        TResult<SelfDeliveryPoiConfigDTO> selfDeliveryPoiConfigDTOTResult = selfDeliveryPoiConfigThriftService.querySelfDeliveryConfig(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginStoreId());
        if (!selfDeliveryPoiConfigDTOTResult.isSuccess() || Objects.isNull(selfDeliveryPoiConfigDTOTResult.getData())) {
            return Collections.emptyMap();
        }
        if (!Objects.equals(selfDeliveryPoiConfigDTOTResult.getData().getEnableTurnDelivery(), BOOLEAN_TRUE)) {
            return Collections.emptyMap();
        }

        //2.操作人要有权限
        Map<String, Boolean> permissions = authThriftWrapper.isHasPermission(ImmutableList.of(AuthCodeEnum.TURN_AGG_DELIVERY.getAuthCode()));
        boolean hasAuth = permissions.getOrDefault(AuthCodeEnum.TURN_AGG_DELIVERY.getAuthCode(), Boolean.FALSE);
        if (!hasAuth) {
            return Collections.emptyMap();
        }

        //3.订单实付金额小于150元 && 不是餐馆 && 不是封签交付订单 && 不是发财酒订单
        return tradeOrderVOMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                entry -> {
                    TradeOrderVO orderVO = entry.getValue();
                    boolean payAmtSatisfied = Objects.nonNull(orderVO.getActualPayAmt()) && orderVO.getActualPayAmt() < MccConfigUtil.getMaxActualPayAmtForThirdDelivery();
                    boolean isRestaurant = SceneParseUtils.isRestaurant(orderVO.getScene());
                    boolean isSealDelivery = sealDeliveryTagMap.getOrDefault(entry.getKey(), false);
                    boolean isFacaiWineOrder = Optional.ofNullable(orderVO.getIsFacaiWine()).orElse(false);
                    return payAmtSatisfied && !isRestaurant && !isSealDelivery && !isFacaiWineOrder;
                }
        ));
    }

    @Degrade(rhinoKey = "TurnDeliveryButtonComponent.queryTurnDeliveryButton", fallBackMethod = "queryTurnDeliveryButtonFallback", timeoutInMilliseconds = 2000)
    public Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> queryTurnDeliveryButtonFallback(Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap,
                                                                                               Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> sealDeliveryTagMap) {
        log.error("TurnDeliveryButtonComponent.queryTurnDeliveryButton 发生降级");
        return Collections.emptyMap();
    }
}
