package com.sankuai.shangou.supplychain.tof.controller.vo.seal.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.TConsumableMaterialInfo;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.RiderPickTaskInfoVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 骑手的拣货任务详情.
 *
 * <AUTHOR>
 * @since 2021/11/15 16:31
 */
@TypeDoc(
        description = "骑手的拣货任务详情"
)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckSealCodeResponse {

    @FieldDoc(
            description = "是否可用", requiredness = Requiredness.REQUIRED
    )
    private Boolean isUsable;

    @FieldDoc(
            description = "占用订单号", requiredness = Requiredness.REQUIRED
    )
    private String usingChannelOrderId;

}
