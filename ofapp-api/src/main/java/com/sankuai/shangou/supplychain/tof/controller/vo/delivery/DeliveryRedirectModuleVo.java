package com.sankuai.shangou.supplychain.tof.controller.vo.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/04/10 21:26
 */
@TypeDoc(
        description = "聚合运力配送跳转模块信息"
)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryRedirectModuleVo {

    @FieldDoc(
            description = "提示文案", requiredness = Requiredness.OPTIONAL
    )
    private String title;

    @FieldDoc(
            description = "链接文案", requiredness = Requiredness.OPTIONAL
    )
    private String urlText;

    @FieldDoc(
            description = "跳转url", requiredness = Requiredness.OPTIONAL
    )
    private String url;

    @FieldDoc(
            description = "是否展示跳转按钮", requiredness = Requiredness.OPTIONAL
    )
    private Boolean showButton;
}
