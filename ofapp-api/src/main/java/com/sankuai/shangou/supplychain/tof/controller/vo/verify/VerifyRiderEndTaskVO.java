package com.sankuai.shangou.supplychain.tof.controller.vo.verify;

import com.sankuai.shangou.supplychain.tof.controller.vo.LabelVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-03-27
 */
@Data
public class VerifyRiderEndTaskVO {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务状态
     */
    private Integer taskStatus;

    /**
     * 下发时间
     */
    private Long pushTaskTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 完成时间
     */
    private Long completeTime;

    /**
     * 采集模式
     */
    private Integer collectMode;

    /**
     * 识别结果
     */
    private Boolean faceIdentifyResult;

    /**
     * 头盔识别结果
     */
    private Boolean helmetIdentifyResult;

    /**
     * 着装识别结果
     */
    private Boolean dressingIdentifyResult;

    /**
     * 审核备注
     */
    private List<LabelVO> labels;
}
