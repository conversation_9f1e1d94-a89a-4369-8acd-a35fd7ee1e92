package com.sankuai.shangou.supplychain.tof.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.supplychain.tof.controller.vo.config.request.DeliveryTabRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.config.response.DeliveryTabResponse;
import com.sankuai.shangou.supplychain.tof.controller.vo.store.request.StoreGrayRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.store.response.StoreGrayResponse;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025-08-15
 * @email <EMAIL>
 */
@Slf4j
@InterfaceDoc(
        type = "restful",
        displayName = "门店配置相关接口",
        description = "门店配置相关接口",
        scenarios = "门店配置接口"
)
@RestController
@RequestMapping("/api/orderfulfill/app/config")
public class DeliveryConfigController {

    @MethodDoc(
            displayName = "自配送tab查询",
            description = "自配送tab查询",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自配送tab查询",
                            type = StoreGrayRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/app/config/deliveryTabs",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/deliveryTabs", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public DeliveryTabResponse queryDeliveryTabs(@Valid @RequestBody DeliveryTabRequest request) {

        return null;
    }

}
