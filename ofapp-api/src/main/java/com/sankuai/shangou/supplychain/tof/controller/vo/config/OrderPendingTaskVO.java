package com.sankuai.shangou.supplychain.tof.controller.vo.config;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "订单首页模块代办"
)
@Data
@ApiModel("订单首页模块代办")
public class OrderPendingTaskVO {

    @FieldDoc(
            description = "待接单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待接单数量", required = true)
    @NotNull
    private Integer waitToTakeOrderCount;


    @FieldDoc(
            description = "待拣货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待拣货数量", required = true)
    @NotNull
    private Integer waitToPickCount;


    @FieldDoc(
            description = "配送异常数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送异常数量", required = true)
    @NotNull
    private Integer deliveryErrorCount;


    @FieldDoc(
            description = "退款待审核数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款待审核数量", required = true)
    @NotNull
    private Integer waitToAuditRefundCount;


    @FieldDoc(
            description = "退货退款待审核数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退货退款待审核数量", required = true)
    @NotNull
    private Integer waitToAuditRefundGoodsCount;

    @FieldDoc(
            description = "待配送数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待配送数量", required = true)
    @NotNull
    private Integer waitToDeliveryCount;

    /**
     * 骑手tab start
     */
    @FieldDoc(
            description = "等待骑手领取订单的订单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "等待骑手领取订单的订单数量", required = true)
    @NotNull
    private Integer riderWaitToGetOrderCount;

    @FieldDoc(
            description = "骑手尚未取货的订单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "骑手尚未取货的订单数量", required = true)
    @NotNull
    private Integer riderWaitToTakeGoodsCount;

    @FieldDoc(
            description = "骑手配送中的订单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "骑手配送中的订单数量", required = true)
    @NotNull
    private Integer riderInDeliveryCount;
    /**
     * 骑手tab end
     */


    @FieldDoc(
            description = "待自提的订单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待自提的订单数量", required = true)
    @NotNull
    private Integer waitToSelfFetchCount;

    @FieldDoc(
            description = "推广自提待完成订单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "推广自提待完成订单数量", required = true)
    @NotNull
    private Integer waitToFinishSelfPickPromoteOrderCount;

    @FieldDoc(
            description = "推广自提待完成订单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "推广自提待完成订单数量", required = true)
    @NotNull
    private Integer dhExceptionCount;

    @FieldDoc(
            description = "新任务-仅拣货订单数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "新任务-仅拣货订单数", required = true)
    private Integer newJustPickTaskCount;

    @FieldDoc(
            description = "新任务-仅配送订单数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "新任务-仅配送订单数", required = true)
    private Integer newJustDeliveryTaskCount;

    @FieldDoc(
            description = "新任务-拣配任务订单数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "新任务-拣配任务订单数", required = true)
    private Integer newPickDeliveryTaskCount;

    @FieldDoc(
            description = "待取货-拣货任务订单数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待取货-拣货任务订单数", required = true)
    private Integer waitTakePickTaskCount;

    @FieldDoc(
            description = "待取货-配送任务订单数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待取货-配送任务订单数", required = true)
    private Integer waitTakeDeliveryTaskCount;

    public static OrderPendingTaskVO homePageInit(){
        OrderPendingTaskVO orderPendingTaskVO = new OrderPendingTaskVO();
        orderPendingTaskVO.setWaitToPickCount(0);
        orderPendingTaskVO.setWaitToTakeOrderCount(0);
        orderPendingTaskVO.setDeliveryErrorCount(0);
        orderPendingTaskVO.setWaitToAuditRefundCount(0);
        orderPendingTaskVO.setWaitToDeliveryCount(0);
        orderPendingTaskVO.setWaitToSelfFetchCount(0);
        orderPendingTaskVO.setRiderWaitToGetOrderCount(0);
        orderPendingTaskVO.setRiderWaitToTakeGoodsCount(0);
        orderPendingTaskVO.setRiderInDeliveryCount(0);
        orderPendingTaskVO.setWaitToAuditRefundGoodsCount(0);
        orderPendingTaskVO.setDhExceptionCount(0);
        orderPendingTaskVO.setWaitToFinishSelfPickPromoteOrderCount(0);
        return orderPendingTaskVO;
    }


}
