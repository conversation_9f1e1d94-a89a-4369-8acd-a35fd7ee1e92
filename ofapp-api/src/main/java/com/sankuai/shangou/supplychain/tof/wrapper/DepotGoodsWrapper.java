package com.sankuai.shangou.supplychain.tof.wrapper;

import com.google.common.collect.Lists;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.goodscenter.request.DepotGoodsBatchQueryRequest;
import com.meituan.shangou.goodscenter.response.DepotGoodsDetailListResponse;
import com.meituan.shangou.goodscenter.thrift.DepotGoodsThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class DepotGoodsWrapper {
    @Resource
    private DepotGoodsThriftService depotGoodsThriftService;

    @MethodLog(logResponse = true)
    public Map<String,DepotGoodsDetailDto> queryBySkuId(Long tenantId, Long repositoryId, List<String> skuIds) {
        DepotGoodsBatchQueryRequest req = new DepotGoodsBatchQueryRequest();
        req.setTenantId(tenantId);
        req.setDepotId(repositoryId);
        Map<String,DepotGoodsDetailDto> result = new HashMap<>();
        Lists.partition(skuIds, 50).forEach(partialSkuIds -> {
            req.setGoodsIdList(partialSkuIds);
            DepotGoodsDetailListResponse resp = depotGoodsThriftService.batchQueryDepotGoodsListByGoodsId(req);
            if (resp.getCode() != 0) {
                throw new ThirdPartyException("depotGoodsThriftService.batchQueryDepotGoodsListByGoodsId failed");
            }
            resp.getData().forEach(good -> result.put(good.getGoodsId(),good));
        });
        return result;
    }
}
