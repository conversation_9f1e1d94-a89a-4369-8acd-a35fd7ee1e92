package com.sankuai.shangou.supplychain.tof.controller.vo.picking.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.shangou.logistics.warehouse.enums.SealDegradeReasonEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 骑手完成拣货请求.
 *
 * <AUTHOR>
 * @since 2021/11/12 16:11
 */
@TypeDoc(
        description = "骑手完成拣货请求"
)
@Data
public class RiderFinishPickingRequest {

    @FieldDoc(
            description = "订单渠道", requiredness = Requiredness.REQUIRED
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    private String channelOrderId;

    @FieldDoc(
            description = "运单 ID", requiredness = Requiredness.REQUIRED
    )
    private Long deliveryOrderId;

    @FieldDoc(
            description = "拣货工单 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long pickingWoId;

    @FieldDoc(
            description = "拣货任务列表",
            requiredness = Requiredness.REQUIRED
    )
    private List<RiderPickTask> pickTasks;

    @FieldDoc(
            description = "订单是否出现过 UPC 扫码错误，用于统计扫码错误订单率",
            requiredness = Requiredness.REQUIRED
    )
    private Boolean upcScanErrorOccurred = false;

    @Deprecated   //被PackingPictureInfo取代，前端上线后，这个字段可以去掉
    @FieldDoc(
            description = "拣货复核照片Url（支持多张照片）",
            requiredness = Requiredness.OPTIONAL
    )
    private List<String> pickingCheckPictureUrlList;

    @FieldDoc(
            description = "打包拍照信息",
            requiredness = Requiredness.OPTIONAL
    )
    private PackingPictureInfo packingPictureInfo;

    @FieldDoc(
            description = "是否是三方配送", requiredness = Requiredness.OPTIONAL
    )
    private Boolean isThirdDelivery;

    @FieldDoc(
            description = "耗材列表", requiredness = Requiredness.OPTIONAL
    )
    private List<ConsumableItem> consumableItems;

    @FieldDoc(
            description = "定位信息", requiredness = Requiredness.OPTIONAL
    )
    private LocationInfo locationInfo;

    /**
     * @see SealDegradeReasonEnum
     */
    @FieldDoc(
            description = "降级原因", requiredness = Requiredness.OPTIONAL
    )
    private Integer sealDegradeReasonCode;

    @FieldDoc(
            description = "开始拣货时间", requiredness = Requiredness.OPTIONAL
    )
    private Long startPickingTime;

    public Optional<String> validate() {
        if (channelId == null || ChannelOrderConvertUtils.sourceMid2Biz(channelId) == null) {
            return Optional.of("订单渠道无效");
        }
        if (StringUtils.isBlank(channelOrderId)) {
            return Optional.of("渠道订单号无效");
        }
        //非三方配送时必传deliveryOrderId
        if (!Objects.equals(isThirdDelivery, true) && (deliveryOrderId == null || deliveryOrderId <= 0)) {
            return Optional.of("运单 ID 无效");
        }
        if (pickingWoId == null || pickingWoId <= 0) {
            return Optional.of("拣货工单 ID 无效");
        }
        if (CollectionUtils.isEmpty(pickTasks)) {
            return Optional.of("拣货任务列表不能为空");
        }
        if (upcScanErrorOccurred == null) {
            return Optional.of("参数错误");
        }
        for (RiderPickTask pickTask : pickTasks) {
            if (pickTask == null) {
                return Optional.of("存在无效拣货任务数据");
            }
            Optional<String> taskValidate = pickTask.validate();
            if (taskValidate.isPresent()) {
                return taskValidate;
            }
        }

        if (CollectionUtils.isNotEmpty(consumableItems)) {
            for (ConsumableItem consumableItem : consumableItems) {
                Optional<String> errMsg = consumableItem.validate();
                if (errMsg.isPresent()) {
                    return errMsg;
                }
            }
        }
        return Optional.empty();
    }

    public boolean packingPictureInfoIsEmpty() {
        return Objects.isNull(packingPictureInfo) || packingPictureInfo.isEmpty();
    }

}
