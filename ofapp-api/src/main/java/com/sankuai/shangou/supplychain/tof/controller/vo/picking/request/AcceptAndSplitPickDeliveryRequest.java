package com.sankuai.shangou.supplychain.tof.controller.vo.picking.request;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/8/29 17:45
 **/
@Data
public class AcceptAndSplitPickDeliveryRequest {
    private String channelOrderId;

    private Integer channelId;

    private Long deliveryOrderId;

    public String validate() {
        if (StringUtils.isBlank(channelOrderId)) {
            return "订单id不合法";
        }

        if (channelId == null || channelId <= 0L) {
            return "渠道id不合法";
        }

        if (deliveryOrderId == null || deliveryOrderId <= 0L) {
            return "运单id不合法";
        }

        return null;
    }
}
