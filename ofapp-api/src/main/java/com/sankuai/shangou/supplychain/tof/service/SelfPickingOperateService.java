package com.sankuai.shangou.supplychain.tof.service;

import com.dianping.cat.Cat;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OnlineBaseDeliveryInfoVO;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.enums.DeliveryStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.utils.ChannelTypeConvertUtils;
import com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum;
import com.sankuai.meituan.reco.pickselect.dh.PickingProcessService;
import com.sankuai.meituan.reco.pickselect.dh.dto.ConsumableItemDTO;
import com.sankuai.meituan.reco.pickselect.dh.dto.EnteringTypeInfoDTO;
import com.sankuai.meituan.reco.pickselect.dh.dto.PackingPictureInfo;
import com.sankuai.meituan.reco.pickselect.dh.dto.TradeOutboundItemOperateDTO;
import com.sankuai.meituan.reco.pickselect.dh.dto.request.FinishPickRequest;
import com.sankuai.meituan.reco.pickselect.dh.dto.response.FinishPickResponse;
import com.sankuai.meituan.reco.pickselect.dh.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.RiderOperateThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderOperateTRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.response.RiderOperateTResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.ChannelOrderIdKeyReq;
import com.sankuai.shangou.commons.auth.login.context.LoginUser;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.request.UserContext;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.ConsumableItemInfo;
import com.sankuai.shangou.logistics.warehouse.enums.ConsumableGoodsType;
import com.sankuai.shangou.logistics.warehouse.enums.SealDegradeReasonEnum;
import com.sankuai.shangou.supplychain.tof.component.TradeOrderInfoComponent;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.LackProductVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.SealDegradeReasonVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.request.*;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.response.FinishPickResp;
import com.sankuai.shangou.supplychain.tof.enums.ErrorCodeEnum;
import com.sankuai.shangou.supplychain.tof.utils.LimitTakeOrderUtils;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import com.sankuai.shangou.supplychain.tof.wrapper.DeliveryServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.FulfillmentOrderServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.LimitAcceptServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.StockWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.shangou.supplychain.tof.controller.convert.RiderPickingConvert.convert2DeliveryRequest;

/**
 * <AUTHOR>
 * @since 2024/6/24 20:34
 **/
@Service
@Slf4j
public class SelfPickingOperateService {

    @Resource
    private TradeShippingOrderService tradeShippingOrderService;

    @Resource
    private TradeOrderInfoComponent tradeOrderInfoComponent;

    @Resource
    private PickingProcessService pickingProcessService;

    @Resource
    private StockWrapper stockWrapper;

    @Resource
    private RiderOperateThriftService riderOperateRpcService;

    @Resource
    private DeliveryServiceWrapper deliveryServiceWrapper;

    @Resource
    private FulfillmentOrderServiceWrapper fulfillmentOrderServiceWrapper;

    @Resource
    private LimitAcceptServiceWrapper limitAcceptServiceWrapper;

    private static int LACK_STOCK_CODE = 20000099;

    private static int CONSUMABLE_STOKE_LAKE = 20000089;

    private static final Logger operationLocationRecordLogger = LoggerFactory.getLogger("logger_drunk_horse_operation_location_record");

    /**
     * 封签-人工操作降级
     */
    public void manualSubmitDegrade(ManualSealDegradeRequest request) {
        List<SealDegradeReasonVO> sealDegradeReasonList = MccConfigUtil.getSealDegradeReasonList();
        Map<Integer, SealDegradeReasonVO> reasonVOMap = sealDegradeReasonList.stream().collect(Collectors.toMap(SealDegradeReasonVO::getReasonCode, Function.identity()));

        if (!reasonVOMap.containsKey(request.getReasonCode())) {
            throw new BizException("降级原因无法识别");
        }
        String reasonDesc = reasonVOMap.get(request.getReasonCode()).getReasonDesc();
        TResult<Void> tResult;
        try {
            log.info("start invoke tradeShippingOrderService.manualDegradeSeal, tradeShippingOrderNo: {}, " +
                            "reasonCode: {}, reasonDesc: {}, reasonDetail: {}", request.getTradeShippingOrderNo(),
                    request.getReasonCode(), reasonDesc, request.getReasonDetail());
            tResult = tradeShippingOrderService.manualDegradeSeal(LoginContextUtils.getAppLoginStoreId(),
                    request.getTradeShippingOrderNo(),
                    request.getReasonCode(),
                    reasonDesc,
                    request.getReasonDetail());
            log.info("end invoke tradeShippingOrderService.manualDegradeSeal, result: {}", tResult);
        } catch (Exception e) {
            log.error("人工降级失败", e);
            throw new ThirdPartyException("人工降级失败,请重试或联系管理员");
        }

        if (!tResult.isSuccess()) {
            throw new BizException(tResult.getMsg());
        }
    }

    public void finishPicking(RiderFinishPickingRequest request) {
        // 1.1 获取订单信息并校验
        OrderBizTypeEnum orderBizType = OrderBizTypeEnum.enumOf(ChannelOrderConvertUtils.sourceMid2Biz(request.getChannelId()));
        if (orderBizType == null) {
            log.warn("订单渠道无效, channelId:{}", request.getChannelId());
            throw new BizException("订单渠道无效");
        }
        TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = new TradeOrderInfoComponent.TradeOrderKey(orderBizType.getValue(), request.getChannelOrderId());
        Map<TradeOrderInfoComponent.TradeOrderKey, OCMSOrderVO> ocmsOrderVOMap = tradeOrderInfoComponent.queryTradeOrderInfoList(Collections.singletonList(tradeOrderKey));
        OCMSOrderVO ocmsOrder = ocmsOrderVOMap.get(tradeOrderKey);

        if (Objects.isNull(ocmsOrder)) {
            throw new BizException("未查询到订单信息");
        }

        DeliveryStatusEnum pickStatus = Optional.ofNullable(ocmsOrder.getOcmsDeliveryInfoVO()).map(OnlineBaseDeliveryInfoVO::getDeliveryStatus)
                .map(DeliveryStatusEnum::enumOf).orElse(null);
        OrderStatusEnum orderStatus = OrderStatusEnum.enumOf(ocmsOrder.getOrderStatus());
        // 若订单状态为已取消，则放弃拣货
        if (pickStatus == DeliveryStatusEnum.CANCELED || orderStatus == OrderStatusEnum.CANCELED) {
            log.warn("订单已取消，不支持进行扫码拣货, pickStatus:{}, orderStatus:{}", pickStatus, orderStatus);
            // 客户端对 ResultCode.RIDER_PICK_FINISH_PICK_FAIL 进行弹窗提醒
            throw new BizException(ErrorCodeEnum.RIDER_PICK_FINISH_PICK_FAIL.getCode(), "订单已取消，请勿出库");
        }

        //若配送状态为暂停配送，则放弃拣货
        if (ocmsOrder.getOcmsDeliveryInfoVO() != null && ocmsOrder.getOcmsDeliveryInfoVO().getDeliveryPauseFlag() != null
                && ocmsOrder.getOcmsDeliveryInfoVO().getDeliveryPauseFlag() == 1) {
            log.warn("运单状态为暂停配送，不能扫码出库");
            throw new BizException(ErrorCodeEnum.RIDER_PICK_FINISH_PICK_FAIL.getCode(), "订单已暂停配送，无法出库");
        }

        if (pickStatus == DeliveryStatusEnum.PICKED) {
            log.warn("拣货已完成，不支持进行扫码拣货, pickStatus:{}, orderStatus:{}", pickStatus, orderStatus);
            Cat.logEvent("DH_ADAPT_SN", "ORDER_COMPLETE");
            throw new BizException(ErrorCodeEnum.RIDER_PICK_FINISH_PICK_FAIL.getCode(), "订单已完成，请退出重试");
        }

        // 2. 进行拣货出库
        Long storeId = LoginContextUtils.getAppLoginStoreId();
        LoginUser user = LoginContextUtils.getAppLoginUser();

        newRiderFinishPick(request, storeId, user);

        //如果是三方配送,不扭转运单状态,直接return
        if (checkIsThirdOrPickDeliverySplitOrder(request, ocmsOrder.getOrderId())) {
            log.info("三方配送单或拣配分离运单, 不扭转运单状态, orderId:{}", ocmsOrder.getOrderId());
            return;
        }

        // 3. 进行骑手配送状态流转
        boolean deliverySuccess = riderTakeAway(request, user,storeId);
        if (!deliverySuccess) {
            // 骑手完成拣货时，若配送状态流转失败，仍返回成功，等待 TMS 消费拣货完成的消息，兜底进行配送状态流转
            Cat.logEvent("RIDER_PICK", "PICK_SUCCESS_BUT_TAKE_AWAY_FAIL");
            log.error("骑手完成拣货时，配送状态流转失败. request:{}", request);
        }

    }


    private void newRiderFinishPick(RiderFinishPickingRequest request, long storeId, LoginUser user) {
        FinishPickResp finishPickResponse = new FinishPickResp();

        FinishPickRequest tRequest = new FinishPickRequest();
        tRequest.setWarehouseId(storeId);
        tRequest.setTenantId(user.getTenantId());
        tRequest.setStartPickingTime(request.getStartPickingTime());
        tRequest.setTradeOutboundOrderNo(String.valueOf(request.getPickingWoId()));
        tRequest.setItemOperateDTOList(
                request.getPickTasks().stream()
                        .map(task -> {
                            TradeOutboundItemOperateDTO tradeOutboundItemOperateDTO = new TradeOutboundItemOperateDTO();
                            tradeOutboundItemOperateDTO.setItemId(task.getPickingTaskId());
                            tradeOutboundItemOperateDTO.setPickedCount(task.getPickedCount());
                            tradeOutboundItemOperateDTO.setEnteringType(task.getPickItemStockOutEnteringType());

                            //添加sn码
                            List<com.sankuai.meituan.reco.pickselect.dh.dto.SnInfoDto> snInfoDtos = Optional.ofNullable(task.getSnInfoList()).orElse(Collections.emptyList())
                                    .stream().map(snInfo -> new com.sankuai.meituan.reco.pickselect.dh.dto.SnInfoDto(snInfo.getSnCode(), snInfo.getSnCodeEnteringType()))
                                    .collect(Collectors.toList());

                            tradeOutboundItemOperateDTO.setSnInfoDtoList(snInfoDtos);
                            return tradeOutboundItemOperateDTO;
                        }).collect(Collectors.toList())
        );
        tRequest.setPickingCheckPictureUrlList(request.getPickingCheckPictureUrlList());
        // 保真送2.0开始支持出库订单图片分成3组展示
        if (!request.packingPictureInfoIsEmpty()) {
            tRequest.setPackingPictureInfo(new PackingPictureInfo(
                    request.getPackingPictureInfo().getGoodsPictureUrlList(),
                    request.getPackingPictureInfo().getPackingPictureUrlList(),
                    request.getPackingPictureInfo().getSealPictureUrlList()));
        }
        tRequest.setIsThirdDeliveryPick(request.getIsThirdDelivery());
        tRequest.setOperateAccountId(user.getAccountId());
        tRequest.setOperateName(user.getEmployeeName());
        tRequest.setConsumableItems(
                Optional.ofNullable(request.getConsumableItems())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .map(consumableItem -> new ConsumableItemDTO(
                                        consumableItem.getSkuId(),
                                        String.valueOf(consumableItem.getQuantity()),
                                        Optional.ofNullable(consumableItem.getEnteringTypeInfos())
                                                .orElse(Lists.newArrayList())
                                                .stream().map(
                                                        enteringTypeInfo -> new EnteringTypeInfoDTO(enteringTypeInfo.getCode(), enteringTypeInfo.getCodeEnteringType())
                                                ).collect(Collectors.toList()),
                                        consumableItem.getType(),
                                        consumableItem.getSkuName()
                                )
                        )
                        .collect(Collectors.toList())
        );
        tRequest.setSealDegradeReasonCode(request.getSealDegradeReasonCode());
        tRequest.setUserContext(new UserContext(LoginContextUtils.getAppLoginTenant(),
                LoginContextUtils.getAppLoginAccountId(),
                LoginContextUtils.getAppLoginUser().getAccountName(),
                LoginContextUtils.getAppLoginUser().getEmployeeName(),
                LoginContextUtils.getAppLoginUser().getEmployeeId()));

        //数量超出降级且有封签扣||安心包||封签容具,不做降级（长期封签容具不作为耗材）
        if (Objects.nonNull(request.getSealDegradeReasonCode())
                && Objects.equals(request.getSealDegradeReasonCode(), SealDegradeReasonEnum.ITEM_COUNT_EXCEED.getCode())) {
            boolean hasSeal = IListUtils.nullSafeStream(request.getConsumableItems())
                    .anyMatch(consumableItem -> MccConfigUtil.getSealConsumableMap(LoginContextUtils.getAppLoginStoreId()).containsKey(consumableItem.getSkuId()));
            if (hasSeal) {
                tRequest.setSealDegradeReasonCode(SealDegradeReasonEnum.NONE.getCode());
            }
        }


        TResult<FinishPickResponse> response = null;
        try {
            response = pickingProcessService.finishPick(tRequest);
            log.info("Call RiderPickingThriftService#finishPick. request:{}, response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("Call RiderPickingThriftService#finishPick error. request:{}", tRequest, e);
            throw new ThirdPartyException("执行出库失败，请稍后重试或联系管理员");
        }

        if (response == null) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            throw new SystemException("执行出库失败，请稍后重试或联系管理员");
        }

        // 把需要拍照留存的错误码直接返回给前端
        if (Objects.equals(response.getCode(), ResultCodeEnum.NEED_TAKE_PHOTO_BEFORE_FINISH_PICKING.getCode())) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            throw new BizException(ErrorCodeEnum.NEED_TAKE_PHOTO_BEFORE_FINISH_PICKING.getCode(), ErrorCodeEnum.NEED_TAKE_PHOTO_BEFORE_FINISH_PICKING.getMessage());
        }

        //考虑到前端的兼容,code还是用8004
        if (Objects.equals(response.getCode(), ResponseCodeEnum.CHECK_PICTURE_NUM_FAIL.getCode())) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            throw new BizException(ErrorCodeEnum.NEED_TAKE_PHOTO_BEFORE_FINISH_PICKING.getCode(), response.getMsg());
        }

        if (Objects.equals(response.getCode(), ResultCodeEnum.EXCEED_SHOULD_PICK_COUNT.getCode())) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            throw new BizException(ErrorCodeEnum.EXCEED_SHOULD_PICK_COUNT.getCode(), ErrorCodeEnum.EXCEED_SHOULD_PICK_COUNT.getMessage());
        }

        if (Objects.equals(response.getCode(), LACK_STOCK_CODE)) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            throw new BizException(ErrorCodeEnum.STOCK_OUT_STOCK_LACK.getCode(), ErrorCodeEnum.STOCK_OUT_STOCK_LACK.getMessage());
        }

        if (Objects.equals(response.getCode(), ResponseCodeEnum.FORTUNE_WINE_STOCK_NOT_ENOUGH.getCode())) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            throw new BizException(ErrorCodeEnum.FORTUNE_WINE_STOCK_NOT_ENOUGH.getCode(), response.getMsg());
        }

        if (Objects.equals(response.getCode(), CONSUMABLE_STOKE_LAKE)) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            // 查询封签扣、安心包的可用库存
            List<ConsumableItemInfo> sealConsumableList = MccConfigUtil.getSealConsumableList(storeId);
            List<String> skuIds = IListUtils.nullSafeFilterElement(sealConsumableList, consumableItemDetail ->
                            Objects.equals(consumableItemDetail.getType(), ConsumableGoodsType.SEAL_TAG.getCode()) ||
                            Objects.equals(consumableItemDetail.getType(), ConsumableGoodsType.SAFE_PACKAGE.getCode()))
                    .stream()
                    .map(ConsumableItemInfo::getSkuId)
                    .collect(Collectors.toList());

            Map<String, BigDecimal> skuIdStockMap =  stockWrapper.querySaleZoneSkuStockAvailable(storeId, user.getTenantId(), skuIds);
            // 统计出封签扣、安心包中，哪些 SKU 的可用库存数量不足
            finishPickResponse.setLackProducts(findLackConsumes(skuIdStockMap, request.getConsumableItems()));

            throw new BizException(ErrorCodeEnum.CONSUMABLE_STOCK_LACK.getCode(), ErrorCodeEnum.CONSUMABLE_STOCK_LACK.getMessage()) {
                @Override
                public Object getData() {
                    return finishPickResponse;
                }
            };
        }

        if(!response.isSuccess()) {
            if (Objects.nonNull(response.getData()) && CollectionUtils.isNotEmpty(response.getData().getInvalidSnCodeList())) {
                finishPickResponse.setInvalidSnCodeList(response.getData().getInvalidSnCodeList());
                throw new BizException(ErrorCodeEnum.SN_CODE_NOT_VALID.getCode(), ErrorCodeEnum.SN_CODE_NOT_VALID.getMessage()) {
                    @Override
                    public Object getData() {
                        return finishPickResponse;
                    }
                };
            }

            if (Objects.nonNull(response.getData()) && CollectionUtils.isNotEmpty(response.getData().getLackStockGoods())) {
                finishPickResponse.setLackProducts(response.getData().getLackStockGoods().stream().map(
                        lackStockGoodsDTO -> new LackProductVO(lackStockGoodsDTO.getSkuId(), lackStockGoodsDTO.getCurrentStock())
                ).collect(Collectors.toList()));
                throw new BizException(ErrorCodeEnum.CONSUMABLE_STOCK_LACK.getCode(), ErrorCodeEnum.CONSUMABLE_STOCK_LACK.getMessage()) {
                    @Override
                    public Object getData() {
                        return finishPickResponse;
                    }
                };
            }

            throw new BizException(ErrorCodeEnum.RIDER_PICK_FINISH_PICK_FAIL.getCode(),
                    StringUtils.isBlank(response.getMsg()) ? "执行出库失败，请稍后重试或联系管理员" : response.getMsg());
        }

        try {
            if (MccConfigUtil.getPostOperationLocationSwitch() && Objects.nonNull(request.getLocationInfo())) {
                String logMessage = XMDLogFormat.build()
                        .putTag("operate_receipt_id", request.getChannelOrderId())
                        .putTag("operation_type", "pick_finish")
                        .putTag("latitude", request.getLocationInfo().getLatitude())
                        .putTag("longitude", request.getLocationInfo().getLongitude())
                        .putTag("comment", "operate_receipt_id is channelOrderId")
                        .toString();
                operationLocationRecordLogger.info(logMessage);
            }
        } catch (Exception e) {
            log.error("记录位置失败", e);
        }
    }

    private List<LackProductVO> findLackConsumes(Map<String, BigDecimal> skuIdStockMap, List<ConsumableItem> consumableItems) {
        if (CollectionUtils.isEmpty(consumableItems)) {
            return new ArrayList<>();
        }
        List<LackProductVO> lackList = new ArrayList<>();
        consumableItems.forEach(item -> {
            if (Objects.equals(item.getType(), ConsumableGoodsType.SEAL_TAG.getCode()) || Objects.equals(item.getType(), ConsumableGoodsType.SAFE_PACKAGE.getCode())) {
                // 当前 SKU 的可用库存
                BigDecimal stock = skuIdStockMap.getOrDefault(item.getSkuId(), BigDecimal.ZERO);
                if (stock.compareTo(new BigDecimal(item.getQuantity())) < 0) {
                    LackProductVO lack = new LackProductVO(item.getSkuId(), stock.stripTrailingZeros().toPlainString());
                    lackList.add(lack);
                }
            }
        });

        return lackList;
    }

    private Boolean checkIsThirdOrPickDeliverySplitOrder(RiderFinishPickingRequest request, Long orderId) {
        List<TDeliveryOrder> tDeliveryOrders =
                deliveryServiceWrapper.queryActiveDeliveryInfoByOrderIds(Collections.singletonList(orderId));

        if (CollectionUtils.isEmpty(tDeliveryOrders) || tDeliveryOrders.size() > 1) {
            Cat.logEvent("DH_ADAPT_DAP", "PICK_FINISH_ERROR");
            log.error("当前订单没有生效中的运单或有多个生效中的运单,请联系管理员, orderId: {}", orderId);
            throw new BizException("当前订单没有生效中的运单或有多个生效中的运单,请联系管理员");
        }

        //如果是拣配分离 拣货完成不改变运单状态
        if (Objects.equals(tDeliveryOrders.get(0).getPickDeliverySplitTag(), true)) {
            return true;
        } else {
            //如果不是拣配分离：校验配送方式
            if (Objects.isNull(request.getIsThirdDelivery()) || Objects.equals(request.getIsThirdDelivery(), false)) {
                return false;
            }

            //如果运单是自营配送运单(即不是三方配送运单)
            if (Objects.equals(tDeliveryOrders.get(0).getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())) {
                Cat.logEvent("DH_ADAPT_DAP", "PICK_FINISH_ERROR");
                log.error("当前运单不是三方配送运单,请到自营配送页面操作, orderId: {}, deliveryId:{}", orderId, tDeliveryOrders.get(0).getId());
                throw new BizException("当前运单不是三方配送运单,请到自营配送页面操作");
            }

            return true;
        }
    }


    /**
     * 骑手完成取货操作，流转配送状态.
     *
     * @param request 用户请求
     * @param user    用户信息
     * @return 是否取货成功
     */
    public boolean riderTakeAway(RiderFinishPickingRequest request, LoginUser user,Long storeId) {
        RiderOperateTRequest tRequest = convert2DeliveryRequest(request, user,storeId);

        RiderOperateTResponse response = null;
        try {
            response = riderOperateRpcService.takeAway(tRequest);
            log.info("Call RiderOperateThriftService#takeAway. request:{}, response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("Call RiderOperateThriftService#takeAway error. request:{}", tRequest, e);
            return false;
        }
        if (response == null || response.getStatus() == null ||
                response.getStatus().getCode() != com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("骑手操作拣货完成时，骑手配送状态流转失败, request:{}, response:{}", tRequest, response);
            return false;
        }


        try {
            if (MccConfigUtil.getPostOperationLocationSwitch() && Objects.nonNull(request.getLocationInfo())) {
                String logMessage = XMDLogFormat.build()
                        .putTag("operate_receipt_id", String.valueOf(request.getDeliveryOrderId()))
                        .putTag("operation_type", "delivery_order_take_away")
                        .putTag("latitude", request.getLocationInfo().getLatitude())
                        .putTag("longitude", request.getLocationInfo().getLongitude())
                        .putTag("comment", "operate_receipt_id is deliveryOrderId")
                        .toString();
                operationLocationRecordLogger.info(logMessage);
            }
        } catch (Exception e) {
            log.error("记录位置失败", e);
        }

        return true;
    }

    /**
     * 领取拣货单并且触发拣配分离
     */
    public void acceptAndSplitPickDelivery(AcceptAndSplitPickDeliveryRequest request) {
        //校验限制接单项
        if (GrayConfigUtils.judgeIsGrayStore(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginStoreId(),
                GrayKeyEnum.LIMIT_ACCEPT_ORDER.getGrayKey(), false)) {
            checkLimitAcceptItem(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginAccountId(), LoginContextUtils.getAppLoginStoreId());
        }

        log.info("start invoke tradeShippingOrderService.startShipAndMarkPickDeliverySplit, channelOrderId, {}", request.getChannelOrderId());
        TResult<Void> tResult = tradeShippingOrderService.startShipAndMarkPickDeliverySplit(LoginContextUtils.getAppLoginStoreId(),
                request.getChannelOrderId(),
                ChannelTypeConvertUtils.convert2OrderBizType(request.getChannelId()),
                LoginContextUtils.getAppLoginAccountId(),
                LoginContextUtils.getAppLoginUser().getEmployeeName());
        log.info("end invoke tradeShippingOrderService.startShipAndMarkPickDeliverySplit, tResult, {}", tResult);
        if (!tResult.isSuccess()) {
            throw new BizException("领取拣货单失败," + tResult.getMsg());
        }
    }

    /**
     * 转拣货
     */
    public void changePicker(ChangePickerRequest request) {
        //校验限制接单
        if (GrayConfigUtils.judgeIsGrayStore(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginStoreId(),
                GrayKeyEnum.LIMIT_ACCEPT_ORDER.getGrayKey(), false)) {
            checkLimitAcceptItem(LoginContextUtils.getAppLoginTenant(), request.getNewPickerAccountId(), LoginContextUtils.getAppLoginStoreId());
        }

        log.info("start invoke tradeShippingOrderService.changeOperator, channelOrderId: {}, newPickerAccountId: {}, newPickerName: {}",
                request.getChannelOrderId(), request.getNewPickerAccountId(), request.getNewPickerName());
        TResult<Void> tResult = tradeShippingOrderService.changeOperator(LoginContextUtils.getAppLoginStoreId(),
                request.getChannelOrderId(),
                ChannelTypeConvertUtils.convert2OrderBizType(request.getChannelId()),
                request.getNewPickerAccountId(),
                request.getNewPickerName());
        log.info("end invoke tradeShippingOrderService.changeOperator, tResult, {}", tResult);
        if (!tResult.isSuccess()) {
            throw new BizException("领取拣货单失败," + tResult.getMsg());
        }
    }

    /**
     * 领取三方配送的拣货单
     * @param
     * @return
     */
    public void acceptThirdDeliveryPickOrder(String viewOrderId, Integer channelId) {
        //校验限制项
        if (GrayConfigUtils.judgeIsGrayStore(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginStoreId(),
                GrayKeyEnum.LIMIT_ACCEPT_ORDER.getGrayKey(), false)) {
            checkLimitAcceptItem(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginAccountId(), LoginContextUtils.getAppLoginStoreId());
        }

        Integer bizType = ChannelTypeConvertUtils.convert2OrderBizType(channelId);
        FulfillmentOrderDetailDTO fulfillmentOrderDetailDTO = fulfillmentOrderServiceWrapper.searchFulfillmentOrderFromMaster(
                LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginStoreId(), new ChannelOrderIdKeyReq(bizType, viewOrderId));
        if (Objects.isNull(fulfillmentOrderDetailDTO.getDeliveryInfo()) ||
                !Objects.equals(fulfillmentOrderDetailDTO.getDeliveryInfo().getDeliveryPlatform(), DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode())) {
            throw new BizException("该拣货单不是三方配送拣货单, 不可操作领取");
        }

        tradeShippingOrderService.startShipByTradeOrderNo(LoginContextUtils.getAppLoginStoreId(),
                viewOrderId, bizType, LoginContextUtils.getAppLoginAccountId(), LoginContextUtils.getAppLoginUser().getEmployeeName());
    }


    public void checkLimitAcceptItem(Long tenantId, Long accountId, Long storeId) {
        List<LimitItemDTO> limitItemList = limitAcceptServiceWrapper.queryLimitItemByAccountId(tenantId, accountId, storeId);

        if (CollectionUtils.isNotEmpty(limitItemList)) {
            List<String> msg = limitItemList.stream()
                    .filter(Objects::nonNull)
                    // 限制接单类型，1-直接限制直接过，2-先提示后限制，需要先判断是否已经超过限制开始时间
                    .filter(LimitTakeOrderUtils::checkCurrentIsLimit)
                    .map(LimitItemDTO::getReason)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(msg)) {
                return;
            }
            String errMsg = String.format(ErrorCodeEnum.LIMIT_ACCEPT_ORDER.getMessage(), Joiner.on("、")
                    .join(msg));
            throw new BizException(ErrorCodeEnum.LIMIT_ACCEPT_ORDER.getCode(), errMsg);
        }

    }
}
