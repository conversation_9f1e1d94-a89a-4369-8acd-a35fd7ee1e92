package com.sankuai.shangou.supplychain.tof.controller.vo.consumable.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.logistics.warehouse.dto.ConsumableItemInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConsumableOutRequest {

    @FieldDoc(description = "订单viewID")
    private String viewOrderId;

    @FieldDoc(description = "耗材明细列表")
    private List<ConsumableItem> items;

    public void valid(Map<String, ConsumableItemInfo> configItems) {
        if (StringUtils.isBlank(viewOrderId)) {
            throw new IllegalArgumentException();
        }
        if (CollectionUtils.isEmpty(items)) {
            throw new IllegalArgumentException();
        }
        for (ConsumableItem item : items) {
            item.valid(configItems.get(item.getSkuId()));
        }
    }
}
