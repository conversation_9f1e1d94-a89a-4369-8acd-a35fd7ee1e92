package com.sankuai.shangou.supplychain.tof.controller.vo.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2020-01-15 14:57
 * @Description:
 */
@TypeDoc(
        description = "赠品信息"
)
@Data
public class GiftVO {
    @FieldDoc(
            description = "赠品名称", requiredness = Requiredness.REQUIRED
    )
    private String giftName;

    @FieldDoc(
            description = "赠品数量", requiredness = Requiredness.OPTIONAL
    )
    private Integer giftQuantity;

    @FieldDoc(
            description = "属于哪一个主品", requiredness = Requiredness.OPTIONAL
    )
    private String belongSkuId;

    @FieldDoc(
            description = "赠品sku", requiredness = Requiredness.REQUIRED
    )
    private String sku;

    @FieldDoc(
            description = "是否包含缺货货品", requiredness = Requiredness.OPTIONAL
    )
    private Boolean isIncludeStockLackGoods;

    @FieldDoc(
            description = "赠品规格", requiredness = Requiredness.OPTIONAL
    )
    private String specification;
}
