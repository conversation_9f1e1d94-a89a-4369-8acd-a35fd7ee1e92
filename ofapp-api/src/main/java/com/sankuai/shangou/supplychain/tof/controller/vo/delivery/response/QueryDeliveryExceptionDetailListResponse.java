package com.sankuai.shangou.supplychain.tof.controller.vo.delivery.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryExceptionDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@TypeDoc(
        description = "异常详情列表响应"
)
@ApiModel("异常详情列表响应")
public class QueryDeliveryExceptionDetailListResponse {

    @FieldDoc(
            description = "异常详情列表"
    )
    @ApiModelProperty("异常详情列表")
    private List<DeliveryExceptionDetailVO> deliveryExceptionDetailVos;
}
