package com.sankuai.shangou.supplychain.tof.controller.vo.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * 订单营收详情
 *
 * <AUTHOR>
 * @since 2023/12/18
 */
@TypeDoc(
        description = "订单营收信息"
)
@Data
public class RevenueDetailVo {

    @FieldDoc(
            description = "商家预计收入", requiredness = Requiredness.OPTIONAL
    )
    private Integer revenueAmount;

    @FieldDoc(
            description = "打包费", requiredness = Requiredness.OPTIONAL
    )
    private Integer packageAmount;

    @FieldDoc(
            description = "配送费", requiredness = Requiredness.OPTIONAL
    )
    private Integer deliveryAmount;

    @FieldDoc(
            description = "活动支出", requiredness = Requiredness.OPTIONAL
    )
    private Integer bizActivityAmount;

    @FieldDoc(
            description = "用户实付金额", requiredness = Requiredness.OPTIONAL
    )
    private Integer actualPayAmount;

    @FieldDoc(
            description = "商品活动详情", requiredness = Requiredness.OPTIONAL
    )
    private List<String> promotionInfos;

    @FieldDoc(
            description = "订单预计毛利", requiredness = Requiredness.OPTIONAL
    )
    private Integer netProfitOnline;

    @FieldDoc(
            description = "订单预计毛利是否包含配送成本", requiredness = Requiredness.OPTIONAL
    )
    private Boolean withDeliveryCost;

    @FieldDoc(
            description = "闪电送费用", requiredness = Requiredness.OPTIONAL
    )
    private Integer fastDeliveryAmt;
}
