package com.sankuai.shangou.supplychain.tof.advisor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * api协议的返回结果
 *
 * <AUTHOR>
 */
@TypeDoc(description = "接口返回结果")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Result<T> {

    @FieldDoc(description = "消息码", requiredness = Requiredness.REQUIRED)
    private Integer code;

    @FieldDoc(description = "错误信息", requiredness = Requiredness.OPTIONAL)
    private String msg;

    /**
     * api兼容期间用这个字段 后续回去掉
     */
    @FieldDoc(description = "错误信息", requiredness = Requiredness.OPTIONAL)
    private String message;

    @FieldDoc(description = "返回对象", requiredness = Requiredness.OPTIONAL)
    private T data;

    public Result(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
        this.message = msg;
    }

    public static <T> Result<T> fail(Integer code, String msg) {
        return new Result<>(code, msg);
    }

    public static <T> Result<T> fail(Integer code, String msg, T data) {
        return new Result<>(code, msg, msg, data);
    }

}
