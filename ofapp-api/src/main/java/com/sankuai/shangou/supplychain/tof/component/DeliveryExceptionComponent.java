package com.sankuai.shangou.supplychain.tof.component;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryException;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryDeliveryExceptionByChannelOrderRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.DeliveryExceptionResponse;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryExceptionInfoVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryExceptionSummaryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/19 14:14
 **/
@Rhino
@Slf4j
public class DeliveryExceptionComponent {

    @Resource
    private RiderQueryThriftService riderQueryRpcService;

    @Degrade(rhinoKey = "DeliveryExceptionComponent.queryRiderReportException", fallBackMethod = "queryRiderReportExceptionFallback", timeoutInMilliseconds = 2000)
    public Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryExceptionSummaryVO> queryRiderReportException(List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList) {
        if (CollectionUtils.isEmpty(tradeOrderKeyList)) {
            return Collections.emptyMap();
        }

        QueryDeliveryExceptionByChannelOrderRequest request = new QueryDeliveryExceptionByChannelOrderRequest();
        request.setTenantId(AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId());
        request.setStoreId(Long.parseLong(AppLoginContextHolder.getAppLoginContext().getStoreIds()));
        request.setChannelOrderInfoList(tradeOrderKeyList.stream().map(tradeOrderKey ->
                        new QueryDeliveryExceptionByChannelOrderRequest.ChannelOrderInfo(tradeOrderKey.getChannelOrderId(), tradeOrderKey.getOrderBizType()))
                .collect(Collectors.toList()));
        DeliveryExceptionResponse response = null;

        response = riderQueryRpcService.queryDeliveryExceptionByChannelOrder(request);
        if (response == null || response.getStatus() == null ||
                response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
            log.warn("RiderDeliveryServiceWrapper call riderQueryRpcService.queryRiderReportException error. request:{}, response:{}", request, response);
            return Collections.emptyMap();
        }
        return buildDeliveryExceptionSummaryVOList(response.getTRiderDeliveryExceptionList());



    }

    public Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryExceptionSummaryVO> queryRiderReportExceptionFallback(List<TradeOrderInfoComponent.TradeOrderKey> tradeOrderKeyList) {
        log.error("DeliveryExceptionComponent.queryRiderReportException 发生降级");
        return Collections.emptyMap();
    };

    private Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryExceptionSummaryVO> buildDeliveryExceptionSummaryVOList(List<TRiderDeliveryException> tRiderDeliveryExceptionList) {

        List<DeliveryExceptionInfoVO> deliveryExceptionInfoVOS = tRiderDeliveryExceptionList.stream()
                .map(this::transform2DeliveryExceptionInfoVO)
                .collect(Collectors.toList());

        //Map化 TradeOrderKey --> DeliveryExceptionInfoVOList
        Map<TradeOrderInfoComponent.TradeOrderKey, List<DeliveryExceptionInfoVO>> deliveryExceptionInfoVOMap = deliveryExceptionInfoVOS.stream().collect(Collectors.groupingBy(deliveryExceptionInfoVO -> {
            return new TradeOrderInfoComponent.TradeOrderKey(deliveryExceptionInfoVO.getOrderBizType(), deliveryExceptionInfoVO.getChannelOrderId());
        }));


        return transform2DeliveryExceptionSummaryVO(deliveryExceptionInfoVOMap);
    }

    private DeliveryExceptionInfoVO transform2DeliveryExceptionInfoVO(TRiderDeliveryException tRiderDeliveryException) {

        DeliveryExceptionInfoVO deliveryExceptionInfoVO = new DeliveryExceptionInfoVO();

        deliveryExceptionInfoVO.setExceptionType(tRiderDeliveryException.getExceptionType());
        deliveryExceptionInfoVO.setReportTimeStamp(tRiderDeliveryException.getCreateTime());
        deliveryExceptionInfoVO.setRiderAccountId(tRiderDeliveryException.getRiderAccountId().toString());
        deliveryExceptionInfoVO.setRiderAccountName(tRiderDeliveryException.getRiderAccountName());
        deliveryExceptionInfoVO.setExceptionTypeDesc(tRiderDeliveryException.getExceptionTypeDesc());
        deliveryExceptionInfoVO.setDeliveryOrderId(tRiderDeliveryException.getDeliveryOrderId().toString());
        deliveryExceptionInfoVO.setChannelOrderId(tRiderDeliveryException.getChannelOrderId().toString());
        deliveryExceptionInfoVO.setOrderBizType(tRiderDeliveryException.getOrderBizType());

        return deliveryExceptionInfoVO;
    }

    private Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryExceptionSummaryVO> transform2DeliveryExceptionSummaryVO(
            Map<TradeOrderInfoComponent.TradeOrderKey, List<DeliveryExceptionInfoVO>> deliveryExceptionInfoVOMap) {

        Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryExceptionSummaryVO> deliveryExceptionSummaryMap = new HashMap<>();

        deliveryExceptionInfoVOMap.forEach((key, value) -> {
            DeliveryExceptionInfoVO deliveryExceptionInfoVO = value.get(0);

            DeliveryExceptionSummaryVO exceptionSummaryVO = new DeliveryExceptionSummaryVO();
            exceptionSummaryVO.setChannelId(ChannelOrderConvertUtils.convertChannelId(deliveryExceptionInfoVO.getOrderBizType()));
            exceptionSummaryVO.setChannelOrderId(deliveryExceptionInfoVO.getChannelOrderId());
            exceptionSummaryVO.setPayTime(deliveryExceptionInfoVO.getPayTime());
            exceptionSummaryVO.setDaySeq(deliveryExceptionInfoVO.getDaySeq());
            exceptionSummaryVO.setStoreId(deliveryExceptionInfoVO.getStoreId());
            exceptionSummaryVO.setDeliveryOrderId(deliveryExceptionInfoVO.getDeliveryOrderId());
            exceptionSummaryVO.setDeliveryExceptionInfoVOS(value);

            deliveryExceptionSummaryMap.put(key, exceptionSummaryVO);
        });

        return deliveryExceptionSummaryMap;
    }
}
