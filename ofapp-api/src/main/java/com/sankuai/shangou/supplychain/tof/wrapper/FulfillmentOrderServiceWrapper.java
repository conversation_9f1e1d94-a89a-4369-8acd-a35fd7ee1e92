package com.sankuai.shangou.supplychain.tof.wrapper;

import com.google.common.collect.Lists;
import com.sankuai.qnh.ofc.ofw.client.thrift.common.base.OfcStatus;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.ChannelOrderKeyDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.operate.FulfillmentPickDispatchRequest;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.BatchFulfillmentOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.ChannelOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.FulfillmentOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.response.operate.FulfillmentPickDispatchResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.response.order.FulfillmentOrderDetailResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.operate.FulfillmentOperateThriftService;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.zookeeper.Login;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-01-28
 * @email <EMAIL>
 */
@Slf4j
@Service
public class FulfillmentOrderServiceWrapper {

    @Resource
    private FulfillmentOrderSearchThriftService fulfillmentOrderSearchThriftService;
    @Resource
    private FulfillmentOperateThriftService fulfillmentOperateThriftService;

    @MethodLog(logRequest = true, logResponse = true)
    public List<FulfillmentOrderDetailDTO> searchFulfillmentOrderListByBatchFulfillmentOrderId(List<ChannelOrderIdKeyReq> channelOrderIdKeyReqs) {
        List<FulfillmentOrderDetailDTO> fulfillmentOrderDetailDTOList = Lists.newArrayList();
        //OFC最大查询100
        List<List<ChannelOrderIdKeyReq>> partitionChannelOrderIdKey = Lists.partition(channelOrderIdKeyReqs, 100);
        for (List<ChannelOrderIdKeyReq> orderIdKeyReqs : partitionChannelOrderIdKey) {
            BatchFulfillmentOrderIdKeyReq req = new BatchFulfillmentOrderIdKeyReq();
            req.setTenantId(LoginContextUtils.getAppLoginTenant());
            req.setWarehouseId(LoginContextUtils.getAppLoginStoreId());
            req.setChannelOrderIdKeyList(orderIdKeyReqs);
            FulfillmentOrderDetailResponse response = fulfillmentOrderSearchThriftService.searchFulfillmentOrderListByBatchFulfillmentOrderId(req);
            if (!Objects.equals(response.getStatus().getCode(), OfcStatus.SUCCESS.getCode())) {
                log.info("fulfillmentOrderSearchThriftService.searchFulfillmentOrderListByBatchFulfillmentOrderId, req = {}, response = {}", req, response);
                throw new BizException("查询履约单失败");
            }
            fulfillmentOrderDetailDTOList.addAll(Optional.ofNullable(response.getFulfillmentOrderList()).orElse(Lists.newArrayList()));

        }

        return fulfillmentOrderDetailDTOList;
    }

    @MethodLog(logRequest = true, logResponse = true)
    public FulfillmentOrderDetailDTO searchFulfillmentOrderFromMaster(long tenantId, long warehouseId, ChannelOrderIdKeyReq channelOrderIdKey) {
        FulfillmentOrderIdKeyReq req = new FulfillmentOrderIdKeyReq();
        req.setTenantId(tenantId);
        req.setWarehouseId(warehouseId);
        req.setMaster(true);
        req.setChannelOrderIdKey(channelOrderIdKey);

        FulfillmentOrderDetailResponse response = fulfillmentOrderSearchThriftService.searchFulfillmentOrderByFulfillmentOrderId(req);
        if (!Objects.equals(response.getStatus().getCode(), OfcStatus.SUCCESS.getCode()) || CollectionUtils.isEmpty(response.getFulfillmentOrderList())) {
            log.info("fulfillmentOrderSearchThriftService.searchFulfillmentOrderListByBatchFulfillmentOrderId, req = {}, response = {}", req, response);
            throw new BizException("查询履约单失败");
        }

        return  response.getFulfillmentOrderList().get(0);

    }
}
