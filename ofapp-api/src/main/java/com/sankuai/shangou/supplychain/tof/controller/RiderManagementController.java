package com.sankuai.shangou.supplychain.tof.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.shangou.commons.auth.login.context.LoginUser;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import com.sankuai.shangou.supplychain.tof.assembler.SelfDeliveryOrderAssembler;
import com.sankuai.shangou.supplychain.tof.component.TransOrderComponent;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.SelfRiderInfo;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.QueryTransferableRiderRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.RiderArrivalLocationRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.RiderChangeRequest;
import com.sankuai.shangou.supplychain.tof.enums.ErrorCodeEnum;
import com.sankuai.shangou.supplychain.tof.service.RiderDeliveryOperateService;
import com.sankuai.shangou.supplychain.tof.wrapper.OSWServiceWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/11/1 16:15
 **/
@Slf4j
@InterfaceDoc(
        type = "restful",
        displayName = "歪马自营配送操作相关接口",
        description = "歪马自营配送的操作功能",
        scenarios = "主要应用于歪马自配场景"
)
@RestController
@RequestMapping("/api/orderfulfill/app/rider/delivery")
public class RiderManagementController {
    @Resource
    private RiderDeliveryOperateService riderDeliveryOperateService;

    @Resource
    private TransOrderComponent transOrderComponent;

    @Resource
    private OSWServiceWrapper oswServiceWrapper;


    @MethodDoc(
            displayName = "改派骑手",
            description = "改派骑手",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "改派骑手",
                            type = RiderArrivalLocationRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "api/orderfulfill/app/rider/delivery/queryTransferableRider",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryTransferableRider", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public List<SelfRiderInfo> queryTransferableRider(@Valid @RequestBody QueryTransferableRiderRequest request) {
        String validateResult = request.validate();
        if (validateResult != null) {
            throw new IllegalArgumentException(validateResult);
        }
        return transOrderComponent.queryTransferableRiderBySearchKey(request.getStoreId(),
                request.getRiderSearchKey(), request.getCurrentRiderPhone());
    }

    @MethodDoc(
            displayName = "改派骑手",
            description = "改派骑手",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "改派骑手",
                            type = RiderArrivalLocationRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "api/orderfulfill/app/rider/delivery/riderChange",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/riderChange", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public void riderChange(@Valid @RequestBody RiderChangeRequest request) throws TException {
        List<EmployeeDTO> employeeDTOS = oswServiceWrapper.queryEmpByAccountIds(
                LoginContextUtils.getAppLoginTenant(), Collections.singletonList(request.getRiderAccountId()));
        if (CollectionUtils.isEmpty(employeeDTOS)) {
            throw new BizException(ErrorCodeEnum.EMPLOYEE_NOT_EXIST.getCode(), ErrorCodeEnum.EMPLOYEE_NOT_EXIST.getMessage());
        }
        LoginUser currentUser = LoginContextUtils.getAppLoginUser();

        riderDeliveryOperateService.riderChange(request, currentUser, employeeDTOS.get(0));
    }
}
