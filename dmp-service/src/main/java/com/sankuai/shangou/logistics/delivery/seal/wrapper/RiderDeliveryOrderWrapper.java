package com.sankuai.shangou.logistics.delivery.seal.wrapper;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryDeliveryOrderByOrderIdListRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.BatchQueryDeliveryOrderResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/28 15:51
 **/
@Service
@Slf4j
public class RiderDeliveryOrderWrapper {
    @Resource
    private RiderQueryThriftService riderQueryThriftService;

    public List<TRiderDeliveryOrder> queryDeliveryOrderByOrderIdList(Long tenantId, Long storeId, List<Long> orderIds) {
        QueryDeliveryOrderByOrderIdListRequest request = new QueryDeliveryOrderByOrderIdListRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setOrderIds(orderIds);

        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }

        BatchQueryDeliveryOrderResponse response;
        try {
            log.info("start invoke riderQueryThriftService.pageQueryDeliveryOrder, request: {}", JSON.toJSONString(request));
            response = riderQueryThriftService.queryDeliveryOrderByOrderIdList(request);
            log.info("end invoke riderQueryThriftService.pageQueryDeliveryOrder, response: {}", JSON.toJSONString(response));

        } catch (Exception e) {
            log.error("查询运单信息失败", e);
            throw new ThirdPartyException("查询运单信息失败");
        }

        if (response.getStatus().getCode() != 0) {
            throw new BizException("查询配送单列表失败");
        }

        if (CollectionUtils.isEmpty(response.getTRiderDeliveryOrders())) {

        }
        return response.getTRiderDeliveryOrders();
    }
}
