package com.sankuai.shangou.logistics.delivery.shippingarea.utils;

import com.dianping.lion.client.Lion;
import org.assertj.core.util.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/24 20:46
 **/
public class MccUtils {
    public static String getShippingManagementLink() {
        return Lion.getConfigRepository().get("region.select.shipping.link", "https://qingting.shangou.st.meituan" +
                ".com/home.html#/waimaM/regional/atlas/delivery-region");
    }

    public static List<String> getShippingAdjustReceiverList() {
        return Lion.getConfigRepository().getList("region.select.shipping.adjust.receiver.list", String.class,
                Lists.newArrayList());
    }

    // 大象udb租户标识，线上：meituan.com 线下：sankuai.info
    public static String getElephantUdbTenantTag() {
        return Lion.getConfigRepository().get("elephant.udb.tenant.tag", "sankuai.info");
    }

    /**
     * 地图开放平台接口授权Key
     *
     * @return
     */
    public static String getMapOpenPlatformApiAuthorizeKey() {
        return Lion.getString("com.sankuai.sgxsupply.wxmall.eapi","map.openplatform.api.authorize.key", "");
    }

    /**
     * 业务权限拦截器
     *
     * @return
     */
    public static boolean enableShippingBusinessAuthCheck() {
        return Lion.getConfigRepository().getBooleanValue("shipping.business.auth.check.enable", false);
    }


    /**
     * 区域规划-歪马租户id
     *
     * @return
     */
    public static String getRegionSelectDrunkHorseTenantId() {
        return Lion.getString( "com.sankuai.sgshopmgmt.regionselection","region.select.drunk.horse.tenant.id", "-3");
    }


    public static String getShippingAdjustPusherKey() {
        return Lion.getConfigRepository().get("region.select.shipping.adjust.pusher.key", "2112C2400n125701");
    }

    public static Long getShippingAdjustPusherId() {
        return Lion.getConfigRepository().getLongValue("region.select.shipping.adjust.pusher.uid", 137439199496L);
    }

    public static int getQueryShippingAreasBatchSize() {
        return Lion.getConfigRepository().getIntValue("query.shipping.areas.batch.size", 200);
    }

    /**
     * 区域规划-百川账号白名单
     *
     * @return
     */
    public static List<String> getRegionSelectAccountWhiteList() {
        return Lion.getList("com.sankuai.sgxsupply.wxmall.eapi","region.select.account.white.list", String.class);
    }

    public static String getRegionAuthApprovalLink() {
        return Lion.getString("com.sankuai.sgxsupply.wxmall.eapi", "region.select.auth.approval.link"
                , "https://km.sankuai.com/page/**********");
    }
}
