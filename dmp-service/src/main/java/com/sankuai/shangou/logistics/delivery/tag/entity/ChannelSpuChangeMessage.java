package com.sankuai.shangou.logistics.delivery.tag.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-28
 * @email <EMAIL>
 * 见 <a href="https://km.sankuai.com/collabpage/2231416406#id-%E6%9B%B4%E6%B8%A0%E9%81%93%E5%95%86%E5%93%81%E5%8F%98%E6%9B%B4">...</a>
 */
@Data
public class ChannelSpuChangeMessage {

    private String operationType;

    private Long tenantId;

    private Long storeId;

    private Integer channelId;

    private String spuId;

    private String skuId;

    private DiffContent before;

    private DiffContent after;

    private List<String> diffFields;


    @Data
    public static class DiffContent {

        private Long retailPrice;

    }
}
