package com.sankuai.shangou.logistics.delivery.seal.wrapper;

import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.PaginationList;
import com.sankuai.shangou.commons.thrift.publisher.response.TPageResult;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.hu.api.dto.Result;
import com.sankuai.shangou.logistics.hu.api.dto.SealContainerLogDTO;
import com.sankuai.shangou.logistics.hu.api.service.QuerySealContainerLogCondition;
import com.sankuai.shangou.logistics.hu.api.service.SealContainerLogService;
import com.sankuai.shangou.logistics.hu.api.service.req.QueryBySealCodeKeysCondition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/27 16:33
 **/
@Service
@Slf4j
public class SealContainerOpLogServiceWrapper {
    @Resource
    private SealContainerLogService sealContainerLogService;


    public List<SealContainerLogDTO> queryLatestSealContainerOperateLog(long tenantId, String sealContainerCode, List<Long> warehouseIds) {
        Result<List<SealContainerLogDTO>> result;
        try {
            List<String> containerCodeList = null;
            if (StringUtils.isNotBlank(sealContainerCode)) {
                containerCodeList = Collections.singletonList(sealContainerCode);
            }
            log.info("start invoke sealContainerLogService.queryLatestSealContainerOperateLog, storeIdList: {}", warehouseIds);
            result = sealContainerLogService.queryLatestSealContainerOperateLog(warehouseIds, tenantId, containerCodeList);
            log.info("end invoke sealContainerLogService.queryLatestSealContainerOperateLog, result: {}", result);
        } catch (Exception e) {
            log.error("查询容具使用记录失败", e);
            throw new BizException("查询容具使用记录失败");
        }

        if (result.getCode() != 0) {
            throw new BizException(result.getMessage());
        }

       return result.getModule();
    }


    public List<SealContainerLogDTO> queryLatestSealContainerOperateLogByOpTye(long tenantId, List<Long> warehouseIds, List<String> sealContainerCodes,
                                                                               List<Integer> sealContainerOperateTypeList) {
        Result<List<SealContainerLogDTO>> result;

        if (CollectionUtils.isEmpty(sealContainerCodes) || CollectionUtils.isEmpty(sealContainerOperateTypeList)) {
            return Collections.emptyList();
        }
        try {
            log.info("start invoke sealContainerLogService.queryLatestSealContainerOperateLogByOpTye, storeIdList: {}", warehouseIds);
            result = sealContainerLogService.queryLatestSealContainerOperateLogByOpTye(warehouseIds, tenantId, sealContainerCodes, sealContainerOperateTypeList);
            log.info("end invoke sealContainerLogService.queryLatestSealContainerOperateLogByOpTye, result: {}", result);
        } catch (Exception e) {
            log.error("查询容具使用记录失败", e);
            throw new BizException("查询容具使用记录失败");
        }

        if (result.getCode() != 0) {
            throw new BizException(result.getMessage());
        }

        return result.getModule();
    }

    public void appendLog(Long warehouseId, long merchantId, List<SealContainerLogDTO> sealContainerLogDTOS) {

        Result<List<SealContainerLogDTO>> result;
        try {
            log.info("start invoke sealContainerLogService.appendLog, warehouseId: {}", warehouseId);
            result = sealContainerLogService.appendLog(warehouseId, merchantId, sealContainerLogDTOS);
            log.info("start invoke sealContainerLogService.appendLog, result: {}", result);
        } catch (Exception e) {
            log.error("新增容具操作记录失败", e);
            throw new BizException("新增容具操作记录失败");
        }

        if (result.getCode() != 0) {
            throw new BizException(result.getMessage());
        }

    }

    public PaginationList<SealContainerLogDTO> queryLogsByCondition(QuerySealContainerLogCondition condition) {
        TPageResult<SealContainerLogDTO> result = sealContainerLogService.queryLogsByCondition(condition);
        if (!result.isSuccess()) {
            throw new ThirdPartyException("查询封签容器日志失败");
        }
        return result.getData();
    }

    public List<SealContainerLogDTO> queryLogsBySealCodeKeys(QueryBySealCodeKeysCondition condition) {
        TResult<List<SealContainerLogDTO>> result = sealContainerLogService.queryLogsBySealCodeKeys(condition);
        if (!result.isSuccess()) {
            throw new ThirdPartyException("查询封签容器日志失败");
        }
        return result.getData();
    }
}
