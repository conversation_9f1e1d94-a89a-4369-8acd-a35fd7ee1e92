package com.sankuai.shangou.logistics.delivery.realtimeboard.utils;

import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;

import java.util.concurrent.*;

public class CommonThreadPool {
    private final static int CORE_POOL_SIZE = 50;

    private final static int MAX_POOL_SIZE = 50;
    private final static long KEEP_ALIVE_TIME = 60;

    private final static int MAX_QUEUE_SIZE = 200;

    private static final ExecutorService commonThreadPool;

    static {
        ThreadFactory namedThreadFactory = (Runnable r) -> new Thread(r, "common_thread_pool_" + r.hashCode());
        commonThreadPool = new ExecutorServiceTraceWrapper(new ThreadPoolExecutor(CORE_POOL_SIZE, MAX_POOL_SIZE,
                KEEP_ALIVE_TIME, TimeUnit.SECONDS, new ArrayBlockingQueue<>(MAX_QUEUE_SIZE),
                namedThreadFactory, new ThreadPoolExecutor.CallerRunsPolicy()));
    }

    public static ExecutorService commonThreadPool() {
        return commonThreadPool;
    }
}
