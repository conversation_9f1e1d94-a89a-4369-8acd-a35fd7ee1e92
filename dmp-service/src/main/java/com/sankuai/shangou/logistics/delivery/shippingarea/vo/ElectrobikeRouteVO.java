package com.sankuai.shangou.logistics.delivery.shippingarea.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "电动车导航信息"
)
@Data
@NoArgsConstructor
public class ElectrobikeRouteVO {

    @FieldDoc(
            description = "在批量接口中，返回可能乱序，用于唯一标识一组起终点",
            requiredness = Requiredness.OPTIONAL
    )
    private String serialId;

    @FieldDoc(
            description = "导航距离 (单位：米)"
    )
    private double distance;

    @FieldDoc(
            description = "使用固定速度计算的时间，骑行速度17.0km/h  (单位：秒)"
    )
    private double duration;

    @FieldDoc(
            description = "路线经纬度序列",
            requiredness = Requiredness.OPTIONAL
    )
    private String polyline;
}
