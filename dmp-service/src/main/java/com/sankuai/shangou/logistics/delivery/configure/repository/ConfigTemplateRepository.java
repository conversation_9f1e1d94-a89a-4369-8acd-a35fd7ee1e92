package com.sankuai.shangou.logistics.delivery.configure.repository;

import com.alibaba.fastjson.JSON;
import com.sankuai.shangou.logistics.delivery.common.enums.DimensionEnum;
import com.sankuai.shangou.logistics.delivery.enums.OperationModeEnum;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.BatchTaskConfigContent;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.ConfigTemplateItemModel;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.ConfigTemplateModel;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.ConfigTemplateDOMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-01
 */
@Service
public class ConfigTemplateRepository {
    @Resource
    private ConfigTemplateDOMapper configTemplateDOMapper;
    @Resource
    private ConfigTemplateItemRepository configTemplateItemRepository;

    @Transactional(rollbackFor = Exception.class)
    public int save(ConfigTemplateModel model) {
        ConfigTemplateDO configTemplateDO = new ConfigTemplateDO();
        configTemplateDO.setTenantId(model.getTenantId());
        configTemplateDO.setName(model.getName());
        configTemplateDO.setDimensionType(model.getDimensionType().getCode());
        if (DimensionEnum.TEMPLATE_ENUMS.contains(model.getDimensionType())) {
            configTemplateDO.setDimensionId(model.getOperationMode().getType());
        }
        configTemplateDO.setIsSyncWhenCreate(model.getIsSyncWhenCreate() ? 1 : 0);
        configTemplateDO.setStatus(1);
        configTemplateDO.setCreateBy(model.getCreateBy());
        configTemplateDOMapper.insertSelective(configTemplateDO);
        model.setId(configTemplateDO.getId());

        List<ConfigTemplateItemDO> itemDOS = model.getConfigContents().stream().map(item -> {
            ConfigTemplateItemDO configTemplateItemDO = new ConfigTemplateItemDO();
            configTemplateItemDO.setTenantId(model.getTenantId());
            configTemplateItemDO.setConfigTemplateId(configTemplateDO.getId());
            configTemplateItemDO.setTemplateType(item.getType().getCode());
            configTemplateItemDO.setConfigContent(JSON.toJSONString(item.getContent()));
            configTemplateItemDO.setCreateBy(model.getCreateBy());
            return configTemplateItemDO;
        }).collect(Collectors.toList());
        configTemplateItemRepository.batchInsert(itemDOS);
        return 1;
    }

    public ConfigTemplateModel selectByPrimaryKey(Long configTemplateId, boolean isNeedItem) {
        if (configTemplateId == null) {
            return null;
        }
        ConfigTemplateDO configTemplateDO = configTemplateDOMapper.selectByPrimaryKey(configTemplateId);
        if (configTemplateDO == null) {
            return null;
        }
        return translate(configTemplateDO, isNeedItem);
    }

    private ConfigTemplateModel translate(ConfigTemplateDO configTemplateDO, boolean isNeedItem) {
        ConfigTemplateModel model = new ConfigTemplateModel();
        model.setId(configTemplateDO.getId());
        model.setTenantId(configTemplateDO.getTenantId());
        model.setName(configTemplateDO.getName());
        model.setDimensionType(DimensionEnum.enumOf(configTemplateDO.getDimensionType()));
        if (model.getDimensionType() == DimensionEnum.OPERATION_MODE) {
            model.setOperationMode(OperationModeEnum.enumOf(configTemplateDO.getDimensionId()));
        }
        model.setIsSyncWhenCreate(configTemplateDO.getIsSyncWhenCreate() == 1);
        model.setStatus(configTemplateDO.getStatus());
        model.setCreatedAt(configTemplateDO.getCreatedAt());
        model.setUpdateTime(configTemplateDO.getUpdateTime());
        model.setCreateBy(configTemplateDO.getCreateBy());
        if (isNeedItem) {
            List<ConfigTemplateItemModel> itemModelList = configTemplateItemRepository.queryByConfigTemplateId(configTemplateDO.getId());
            List<BatchTaskConfigContent> configContents = itemModelList.stream().map(item -> {
                BatchTaskConfigContent content = new BatchTaskConfigContent();
                content.setType(item.getTemplateType());
                content.setContent(item.getConfigContent());
                return content;
            }).collect(Collectors.toList());
            model.setConfigContents(configContents);
        }
        return model;
    }
}
