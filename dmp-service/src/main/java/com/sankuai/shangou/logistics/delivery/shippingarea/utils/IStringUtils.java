package com.sankuai.shangou.logistics.delivery.shippingarea.utils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class IStringUtils {
    /**
     * 判断字符数组中的元素是否都一样
     *
     * @param strs
     * @return
     */
    public static boolean compareSelf(List<String> strs) {
        if (CollectionUtils.isEmpty(strs)) {
            return true;
        }
        return strs.stream().allMatch(t -> StringUtils.equals(strs.get(0), t));
    }

    public static String getCurrentMillisString(){
        return String.valueOf(System.currentTimeMillis());
    }
}
