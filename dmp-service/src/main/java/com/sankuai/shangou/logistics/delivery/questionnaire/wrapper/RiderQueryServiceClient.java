package com.sankuai.shangou.logistics.delivery.questionnaire.wrapper;

import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryDeliveryOrderByOrderIdListRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.BatchQueryDeliveryOrderResponse;
import com.sankuai.shangou.commons.utils.rpc.RpcInvokeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/15 15:16
 **/
@Service
@Slf4j
public class RiderQueryServiceClient {
    @Resource
    private RiderQueryThriftService riderQueryThriftService;

    public List<TRiderDeliveryOrder> queryDeliveryOrderByOrderIds(Long tenantId, Long storeId, List<Long> orderIds) {
        QueryDeliveryOrderByOrderIdListRequest request = new QueryDeliveryOrderByOrderIdListRequest();
        request.setOrderIds(orderIds);
        request.setStoreId(storeId);
        request.setTenantId(tenantId);
        log.info("start invoke riderQueryThriftService.queryDeliveryOrderByOrderIdList, question: {}", request);
        BatchQueryDeliveryOrderResponse response = RpcInvokeUtils.rpcInvokeTemplate(
                () -> riderQueryThriftService.queryDeliveryOrderByOrderIdList(request),
                "查询运单失败",
                resp -> resp.getStatus().getCode(),
                resp -> resp.getStatus().getMsg());

        return response.getTRiderDeliveryOrders();
    }
}
