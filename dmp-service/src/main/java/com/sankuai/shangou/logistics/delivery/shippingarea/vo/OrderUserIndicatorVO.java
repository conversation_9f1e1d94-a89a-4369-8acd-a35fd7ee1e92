package com.sankuai.shangou.logistics.delivery.shippingarea.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(
        description = "订单/用户指标"
)
@Data
public class OrderUserIndicatorVO {
    @JsonIgnore
    @FieldDoc(
            description = "位置id"
    )
    private Long loiId;

    @FieldDoc(
            description = "常驻人口数"
    )
    private Long mapResidentCnt;
    @FieldDoc(
            description = "外卖订单数"
    )
    private Long wmOrderCnt;
    @FieldDoc(
            description = "外卖用户数"
    )
    private Long wmUserCnt;
    @FieldDoc(
            description = "闪购用户数"
    )
    private Long sgUserCnt;
    @FieldDoc(
            description = "闪购订单数"
    )
    private Long sgOrderCnt;
    @FieldDoc(
            description = "门店订单数"
    )
    private Long storeOrderCnt;

    @FieldDoc(
            description = "歪马微商城日均订单量"
    )
    private Long dhWxOrdNum = 0L;

    @FieldDoc(
            description = "歪马外卖门店日均单量"
    )
    private Long dhWmOrdNum = 0L;

    @FieldDoc(
            description = "超配率"
    )
    private Double outScopeRate = null;

    private String sgBeverageGmvCoverRate = null;
}
