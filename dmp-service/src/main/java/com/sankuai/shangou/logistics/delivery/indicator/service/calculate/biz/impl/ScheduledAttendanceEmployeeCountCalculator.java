package com.sankuai.shangou.logistics.delivery.indicator.service.calculate.biz.impl;

import com.sankuai.shangou.logistics.delivery.indicator.service.calculate.biz.AbstractBizIndicatorCalculator;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BaseIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BizIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorDTO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/7/24 20:55
 **/
@Component
public class ScheduledAttendanceEmployeeCountCalculator extends AbstractBizIndicatorCalculator {
    @Override
    public List<BaseIndicatorEnum> getRelateBaseIndicators() {
        return Collections.singletonList(BaseIndicatorEnum.SCHEDULED_ATTENDANCE_EMPLOYEE_COUNT);
    }

    @Override
    public BizIndicatorEnum getSupportIndicator() {
        return BizIndicatorEnum.SCHEDULED_ATTENDANCE_EMPLOYEE_COUNT;
    }

    @Override
    public List<IndicatorDTO> calc(Map<String, List<IndicatorDTO>> baseIndicatorMap) {
        return calcBySingleBaseIndicator(baseIndicatorMap);
    }
}
