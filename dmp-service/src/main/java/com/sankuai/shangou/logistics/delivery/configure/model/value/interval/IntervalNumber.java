package com.sankuai.shangou.logistics.delivery.configure.model.value.interval;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/2/23
 * @email jianglilin02@meituan
 */
@Getter
public class IntervalNumber implements Comparable<IntervalNumber> {

    // 正无穷
    public final static String POSITIVE_INFINITY = "infinity";
    // 负无穷
    public final static String NEGATIVE_INFINITY = "-infinity";

    // 正无穷
    public final static String POSITIVE_FORMATTED_INFINITY = "∞️";
    // 负无穷
    public final static String NEGATIVE_FORMATTED_INFINITY = "-∞️";

    private String value;

    public IntervalNumber() {
    }

    public IntervalNumber(String value) {
        if (StringUtils.isBlank(value) || (!NumberUtils.isCreatable(value) && !Objects.equals(value,
                POSITIVE_INFINITY) && !Objects.equals(value, NEGATIVE_INFINITY))) {
            throw new IllegalArgumentException("init IntervalNumber error");
        }
        this.value = value;
    }

    public boolean isGreaterThan(BigDecimal bigDecimal) {
        if (Objects.isNull(bigDecimal)) {
            throw new IllegalArgumentException("param is null");
        }

        if (isPositiveInfinity()) {
            return true;
        } else if (isNegativeInfinity()) {
            return false;
        }
        return new BigDecimal(value).compareTo(bigDecimal) > 0;
    }

    public boolean isEqualTo(BigDecimal bigDecimal) {
        if (isPositiveInfinity() || isNegativeInfinity()) {
            return false;
        }
        return new BigDecimal(value).compareTo(bigDecimal) == 0;
    }

    public boolean isLessThan(BigDecimal bigDecimal) {
        return !isGreaterThan(bigDecimal) && !isEqualTo(bigDecimal);
    }

    public boolean isPositiveInfinity() {
        return Objects.equals(POSITIVE_INFINITY, value);
    }

    public boolean isNegativeInfinity() {
        return Objects.equals(NEGATIVE_INFINITY, value);
    }

    public boolean isNotInfinity() {
        return !isPositiveInfinity() && !isNegativeInfinity();
    }

    public static boolean canInstance(String value) {
        boolean isBlank = StringUtils.isBlank(value);
        boolean isCreatable = NumberUtils.isCreatable(value);
        boolean isPositiveInfinity = Objects.equals(value, POSITIVE_INFINITY);
        boolean isNegativeInfinity = Objects.equals(value, NEGATIVE_INFINITY);

        return !isBlank && (isCreatable || isPositiveInfinity || isNegativeInfinity);
    }

    public BigDecimal convertToBigDecimal() {
        if (!isNotInfinity()) {
            throw new IllegalArgumentException("存在无尽数，无法转化为BigDecimal");
        }
        return new BigDecimal(value);
    }

    @Override
    public int compareTo(IntervalNumber o) {
        if (this == o || Objects.equals(this.getValue(), o.getValue())) {
            return 0;
        }
        if (isPositiveInfinity()) {
            return 1;
        } else if (isNegativeInfinity()) {
            return -1;
        }
        return new BigDecimal(value).compareTo(new BigDecimal(o.getValue()));
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        IntervalNumber that = (IntervalNumber) o;
        return com.google.common.base.Objects.equal(value, that.value);
    }

    @Override
    public int hashCode() {
        return com.google.common.base.Objects.hashCode(value);
    }
}
