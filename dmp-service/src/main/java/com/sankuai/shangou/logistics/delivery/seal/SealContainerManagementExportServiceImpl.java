package com.sankuai.shangou.logistics.delivery.seal;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.response.PaginationList;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import com.sankuai.shangou.logistics.delivery.common.wrapper.OswClient;
import com.sankuai.shangou.logistics.delivery.seal.enums.SealContainerStatusEnum;
import com.sankuai.shangou.logistics.delivery.seal.request.QuerySealContainerListReq;
import com.sankuai.shangou.logistics.delivery.seal.request.QuerySealContainerListRequest;
import com.sankuai.shangou.logistics.delivery.seal.request.QuerySealContainerLogByConditionRequest;
import com.sankuai.shangou.logistics.delivery.seal.request.SealContainerOpLogWebDTO;
import com.sankuai.shangou.logistics.delivery.seal.wrapper.SealContainerOpLogServiceWrapper;
import com.sankuai.shangou.logistics.hu.api.constants.enums.SealContainerOperateType;
import com.sankuai.shangou.logistics.hu.api.dto.SealContainerKey;
import com.sankuai.shangou.logistics.hu.api.dto.SealContainerLogDTO;
import com.sankuai.shangou.logistics.hu.api.dto.SealContainerLogExtInfo;
import com.sankuai.shangou.logistics.hu.api.service.QuerySealContainerLogCondition;
import com.sankuai.shangou.logistics.hu.api.service.req.QueryBySealCodeKeysCondition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.comparator.Comparators;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/6/28 15:31
 **/
@Slf4j
@ShangouThriftServer
public class SealContainerManagementExportServiceImpl implements SealContainerManagementQueryService {
    @Resource
    private SealContainerOpLogServiceWrapper sealContainerOpLogServiceWrapper;

    @Resource
    private OswClient oswClient;

    @Override
    public PaginationList<SealContainerOpLogWebDTO> queryStoreUsingSealContainerList(QuerySealContainerListReq request) {
        List<SealContainerLogDTO> usingSealContainerLatestLogs = sealContainerOpLogServiceWrapper.queryLatestSealContainerOperateLog(request.getTenantId(), request.getSealContainerCode(), request.getWarehouseIds());

        //只看现在是使用中的容具
        usingSealContainerLatestLogs = usingSealContainerLatestLogs.stream()
                .filter(opLog -> Objects.equals(opLog.getOpType(), SealContainerOperateType.STOCK_OUT.getCode())
                        || Objects.equals(opLog.getOpType(), SealContainerOperateType.TRANSFER.getCode()))
                .collect(Collectors.toList());


        //按照交易单号筛选
        if (StringUtils.isNotBlank(request.getTradeOrderNo())) {
            usingSealContainerLatestLogs = usingSealContainerLatestLogs.stream()
                    .filter(log -> Objects.equals(log.getTradeOrderNo(), request.getTradeOrderNo()))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(usingSealContainerLatestLogs) || usingSealContainerLatestLogs.size() < (request.getPage() - 1) * request.getPageSize()) {
            return new PaginationList<>(request.getPage(), request.getPageSize(), 0L, Collections.emptyList());
        }

        //拿到最近一次操作记录是转单的容具code 查询对应容具最后一次出库的记录
        Map<String, SealContainerLogDTO> transferSealContainerLogMap = usingSealContainerLatestLogs.stream()
                .filter(opLog -> Objects.equals(opLog.getOpType(), SealContainerOperateType.TRANSFER.getCode()))
                .collect(Collectors.toMap(SealContainerLogDTO::getSealContainerCode, Function.identity(), (k1, k2) -> k2));

        List<SealContainerLogDTO> stockOutSealContainerLogs = sealContainerOpLogServiceWrapper.queryLatestSealContainerOperateLogByOpTye(request.getTenantId(), request.getWarehouseIds(), new ArrayList<>(transferSealContainerLogMap.keySet()), Collections.singletonList(SealContainerOperateType.STOCK_OUT.getCode()));

        //合并最后一次出库和最后一次转单的出库记录
        Map<String, SealContainerLogDTO> stockOutSealContainerLogMap = new HashMap<>();
        stockOutSealContainerLogMap.putAll(usingSealContainerLatestLogs.stream()
                .filter(opLog -> Objects.equals(opLog.getOpType(), SealContainerOperateType.STOCK_OUT.getCode()))
                .collect(Collectors.toMap(SealContainerLogDTO::getSealContainerCode, Function.identity(), (k1, k2) -> k2)));
        stockOutSealContainerLogMap.putAll(
                stockOutSealContainerLogs.stream().collect(Collectors.toMap(SealContainerLogDTO::getSealContainerCode, Function.identity(), (k1, k2) -> k2))
        );

        //拼接同一个container的出库日志和转单日志
        List<SealContainerOpLogWebDTO> sealContainerOpLogVOS = usingSealContainerLatestLogs.stream().map(opLog -> {
            String containerCode = opLog.getSealContainerCode();
            return buildOpLogDTO(stockOutSealContainerLogMap.get(containerCode), transferSealContainerLogMap.get(containerCode), null, SealContainerStatusEnum.USING);
        }).sorted(new Comparator<SealContainerOpLogWebDTO>() {
            @Override
            public int compare(SealContainerOpLogWebDTO o1, SealContainerOpLogWebDTO o2) {
                return -o1.getRiderStartDeliveryTime().compareTo(o2.getRiderStartDeliveryTime());
            }
        }).collect(Collectors.toList());

        //内存分页
        List<SealContainerOpLogWebDTO> pageList = sealContainerOpLogVOS.subList((request.getPage() - 1) * request.getPageSize(), Math.min(request.getPage() * request.getPageSize(), sealContainerOpLogVOS.size()));

        //填充门店名
        Map<Long, WarehouseDTO> warehouseInfoMap = oswClient.queryWarehouseInfoList(request.getTenantId(),
                pageList.stream().map(SealContainerOpLogWebDTO::getWarehouseId).collect(Collectors.toList()));
        pageList.forEach(sealContainerOpLogVO -> {
            if (warehouseInfoMap.containsKey(sealContainerOpLogVO.getWarehouseId())) {
                sealContainerOpLogVO.setWarehouseName(warehouseInfoMap.get(sealContainerOpLogVO.getWarehouseId()).getName());
            }
        });

        return new PaginationList<>(request.getPage(), request.getPageSize(), (long) sealContainerOpLogVOS.size(), pageList);
    }



    @Override
    public PaginationList<SealContainerOpLogWebDTO> querySealContainerLogList(QuerySealContainerLogByConditionRequest request) {
        QuerySealContainerLogCondition condition = new QuerySealContainerLogCondition();
        condition.setWarehouseIds(request.getWarehouseIds());
        condition.setSealContainerCode(request.getSealContainerCode());
        condition.setGiveBackTimestampStart(request.getGiveBackTimestampStart());
        condition.setGiveBackTimestampEnd(request.getGiveBackTimestampEnd());
        condition.setOpType(SealContainerOperateType.STOCK_IN.getCode());
        condition.setPageNo(request.getPage());
        condition.setPageSize(request.getPageSize());
        PaginationList<SealContainerLogDTO> paginationList = sealContainerOpLogServiceWrapper.queryLogsByCondition(condition);
        if (CollectionUtils.isEmpty(paginationList.getList())) {
            return new PaginationList<>( request.getPage(), request.getPageSize(), paginationList.getTotal(), Lists.newArrayList());
        }
        //根据查出来的归还流水做二次查询
        List<SealContainerKey> sealContainerKeys = IListUtils.mapTo(paginationList.getList(), dto -> new SealContainerKey(dto.getSealContainerCode(), dto.getTradeOrderNo()));
        List<SealContainerLogDTO> transferAndStockOutSealContainerLogDTOS = sealContainerOpLogServiceWrapper.queryLogsBySealCodeKeys(
                new QueryBySealCodeKeysCondition(sealContainerKeys, null, null,
                        Lists.newArrayList(SealContainerOperateType.TRANSFER.getCode(), SealContainerOperateType.STOCK_OUT.getCode()))
        );

        Map<LogGroupKey, List<SealContainerLogDTO>> logGroupMap = IListUtils.nullSafeGroupBy(paginationList.getList(), sealContainerLogDTO -> new LogGroupKey(sealContainerLogDTO.getSealContainerCode(), sealContainerLogDTO.getTradeOrderNo()));
        Map<LogGroupKey, List<SealContainerLogDTO>> transferAndStockOutLogGroupMap = IListUtils.nullSafeGroupBy(transferAndStockOutSealContainerLogDTOS, sealContainerLogDTO -> new LogGroupKey(sealContainerLogDTO.getSealContainerCode(), sealContainerLogDTO.getTradeOrderNo()));

        List<SealContainerOpLogWebDTO> sealContainerOpLogVOS = IListUtils.mapTo(logGroupMap.entrySet(), entry -> {
            List<SealContainerLogDTO> transferAndStockOutSealContainerLogDTOs = transferAndStockOutLogGroupMap.get(entry.getKey());
            Map<Integer, List<SealContainerLogDTO>> opTypeMap = IListUtils.nullSafeGroupBy(transferAndStockOutSealContainerLogDTOs, SealContainerLogDTO::getOpType);
            //转单可能转多次
            Optional<SealContainerLogDTO> maxTransferLogDTO = IListUtils.nullSafeStream(opTypeMap.get(SealContainerOperateType.TRANSFER.getCode()))
                    .max(Comparator.comparing(SealContainerLogDTO::getOpTime));
            return buildOpLogDTOByStockInLog(Optional.ofNullable(opTypeMap.get(SealContainerOperateType.STOCK_OUT.getCode())).map(list -> list.get(0)).orElse(null), maxTransferLogDTO.orElse(null), entry.getValue().get(0), SealContainerStatusEnum.RETURN);
        });

        sealContainerOpLogVOS = IListUtils.nullSafeStream(sealContainerOpLogVOS)
                .sorted((o1, o2) -> (int) (o2.getGiveBackTime() - o1.getGiveBackTime()))
                .collect(Collectors.toList());

        //填充门店名
        List<Long> thisWarehouseIds = IListUtils.mapTo(sealContainerOpLogVOS, SealContainerOpLogWebDTO::getWarehouseId);
        Map<Long, WarehouseDTO> longWarehouseDTOMap = oswClient.queryWarehouseInfoList(request.getTenantId(), thisWarehouseIds);
        sealContainerOpLogVOS.forEach(sealContainerOpLogVO -> {
            if (longWarehouseDTOMap.containsKey(sealContainerOpLogVO.getWarehouseId())) {
                sealContainerOpLogVO.setWarehouseName(longWarehouseDTOMap.get(sealContainerOpLogVO.getWarehouseId()).getName());
            }
        });

        return new PaginationList<>(
                paginationList.getPage(),
                paginationList.getPageSize(),
                paginationList.getTotal(),
                sealContainerOpLogVOS
        );
    }

    private SealContainerOpLogWebDTO buildOpLogDTOByStockInLog(@Nullable SealContainerLogDTO stockOutLogDto, @Nullable SealContainerLogDTO transferOutLogDto, SealContainerLogDTO stockInLogDto,
                                                   SealContainerStatusEnum sealContainerStatusEnum) {
        SealContainerOpLogWebDTO sealContainerOpLogVO = new SealContainerOpLogWebDTO();


        sealContainerOpLogVO.setGiveBackTime(TimeUtils.toMilliSeconds(stockInLogDto.getOpTime()));
        sealContainerOpLogVO.setGiveBackTimeStr(stockInLogDto.getOpTime().format(TimeUtils.DEFAULT_DATE_TIME_FORMATTER));
        sealContainerOpLogVO.setGiveBackOperatorName(stockInLogDto.getOperatorName());
        setGiveBackType(stockInLogDto, sealContainerOpLogVO);

        sealContainerOpLogVO.setWarehouseId(stockInLogDto.getWarehouseId());
        sealContainerOpLogVO.setSealContainerCode(stockInLogDto.getSealContainerCode());
        sealContainerOpLogVO.setTradeOrderNo(stockInLogDto.getTradeOrderNo());
        sealContainerOpLogVO.setChannelId(ChannelOrderConvertUtils.convertChannelId(stockInLogDto.getTradeOrderBizType()));
        sealContainerOpLogVO.setStatus(sealContainerStatusEnum.getCode());
        sealContainerOpLogVO.setStatusDesc(sealContainerStatusEnum.getDesc());

        if (Objects.nonNull(stockOutLogDto)) {
            sealContainerOpLogVO.setStockOutOperatorName(stockOutLogDto.getOperatorName());
            sealContainerOpLogVO.setStockOutTime(TimeUtils.toMilliSeconds(stockOutLogDto.getOpTime()));
            sealContainerOpLogVO.setStockOutTimeStr(stockOutLogDto.getOpTime().format(TimeUtils.DEFAULT_DATE_TIME_FORMATTER));
        }

        // 使用三元运算符简化逻辑
        SealContainerLogDTO effectiveLogDto = Objects.nonNull(transferOutLogDto) ? transferOutLogDto : stockOutLogDto;
        if (Objects.nonNull(effectiveLogDto)) {
            sealContainerOpLogVO.setRiderName(effectiveLogDto.getOperatorName());
            sealContainerOpLogVO.setRiderStartDeliveryTime(TimeUtils.toMilliSeconds(effectiveLogDto.getOpTime()));
            sealContainerOpLogVO.setRiderStartDeliveryTimeStr(effectiveLogDto.getOpTime().format(TimeUtils.DEFAULT_DATE_TIME_FORMATTER));
        }


        return sealContainerOpLogVO;
    }

    private static void setGiveBackType(SealContainerLogDTO stockInLogDto, SealContainerOpLogWebDTO sealContainerOpLogWebDTO) {
        try {
            if (StringUtils.isNotBlank(stockInLogDto.getExtInfo())) {
                String source = Optional.ofNullable(JSON.parseObject(stockInLogDto.getExtInfo(), SealContainerLogExtInfo.class)).map(SealContainerLogExtInfo::getSource).orElse(StringUtils.EMPTY);
                sealContainerOpLogWebDTO.setGiveBackTypeDesc(source);
                if (Objects.equals(source, "WEB")) {
                    sealContainerOpLogWebDTO.setGiveBackType(2);
                } else if (Objects.equals(source, "APP")) {
                    sealContainerOpLogWebDTO.setGiveBackType(1);
                }
            }
        } catch (Exception e) {
            log.error("setGiveBackType error", e);
        }
    }

    private Pair<SealContainerLogDTO, SealContainerLogDTO> getStockOutAndUseLogPair(List<SealContainerLogDTO> sealContainerLogDTOS) {
        if (CollectionUtils.isEmpty(sealContainerLogDTOS)) {
            return null;
        }
        if (sealContainerLogDTOS.size() == 1) {
            return Pair.of(sealContainerLogDTOS.get(0), sealContainerLogDTOS.get(0));
        }
        Optional<SealContainerLogDTO> stockOutOpt = sealContainerLogDTOS.stream().filter(SealContainerLogDTO -> Objects.equals(SealContainerLogDTO.getOpType(), SealContainerOperateType.STOCK_OUT.getCode())).findFirst();
        Optional<SealContainerLogDTO> transferOpt = sealContainerLogDTOS.stream().filter(SealContainerLogDTO -> Objects.equals(SealContainerLogDTO.getOpType(), SealContainerOperateType.TRANSFER.getCode())).findFirst();
        //transfer时间必须晚于stockOut时间才有效
        if (stockOutOpt.isPresent()) {
            return stockOutOpt.map(stockOutLog -> {
                SealContainerLogDTO useLog = transferOpt.filter(transferLog -> transferLog.getOpTime().isBefore(stockOutLog.getOpTime()))
                        .orElse(stockOutLog);
                return Pair.of(stockOutLog, useLog);
            }).orElse(null);
        }
        return null;
    }

    //注意：门店名之后再填充
    private SealContainerOpLogWebDTO buildOpLogDTO(SealContainerLogDTO stockOutLogDto, @Nullable SealContainerLogDTO transferOutLogDto, @Nullable SealContainerLogDTO stockInLogDto,
                                              SealContainerStatusEnum sealContainerStatusEnum) {
        SealContainerOpLogWebDTO sealContainerOpLogDTO = new SealContainerOpLogWebDTO();
        sealContainerOpLogDTO.setWarehouseId(stockOutLogDto.getWarehouseId());
        sealContainerOpLogDTO.setSealContainerCode(stockOutLogDto.getSealContainerCode());
        sealContainerOpLogDTO.setTradeOrderNo(stockOutLogDto.getTradeOrderNo());
        sealContainerOpLogDTO.setChannelId(ChannelOrderConvertUtils.convertChannelId(stockOutLogDto.getTradeOrderBizType()));
        sealContainerOpLogDTO.setStatus(sealContainerStatusEnum.getCode());
        sealContainerOpLogDTO.setStatusDesc(sealContainerStatusEnum.getDesc());
        sealContainerOpLogDTO.setStockOutOperatorName(stockOutLogDto.getOperatorName());
        sealContainerOpLogDTO.setStockOutTime(TimeUtils.toMilliSeconds(stockOutLogDto.getOpTime()));
        sealContainerOpLogDTO.setStockOutTimeStr(stockOutLogDto.getOpTime().format(TimeUtils.DEFAULT_DATE_TIME_FORMATTER));

        // 使用三元运算符简化逻辑
        SealContainerLogDTO effectiveLogDto = Objects.nonNull(transferOutLogDto) ? transferOutLogDto : stockOutLogDto;
        sealContainerOpLogDTO.setRiderName(effectiveLogDto.getOperatorName());
        sealContainerOpLogDTO.setRiderStartDeliveryTime(TimeUtils.toMilliSeconds(effectiveLogDto.getOpTime()));
        sealContainerOpLogDTO.setRiderStartDeliveryTimeStr(effectiveLogDto.getOpTime().format(TimeUtils.DEFAULT_DATE_TIME_FORMATTER));
        if (Objects.nonNull(stockInLogDto)) {
            sealContainerOpLogDTO.setGiveBackTime(TimeUtils.toMilliSeconds(stockInLogDto.getOpTime()));
            sealContainerOpLogDTO.setGiveBackOperatorName(stockInLogDto.getOperatorName());
            setGiveBackType(stockInLogDto, sealContainerOpLogDTO);
        }
        return sealContainerOpLogDTO;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LogGroupKey {

        private String sealContainerCode;

        private String tradeOrderNo;
    }
}
