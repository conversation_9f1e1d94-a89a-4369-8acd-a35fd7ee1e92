package com.sankuai.shangou.logistics.delivery.indicator.service.calculate.biz.impl;

import com.sankuai.shangou.logistics.delivery.indicator.service.calculate.biz.AbstractBizIndicatorCalculator;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BaseIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BizIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/7/24 20:12
 **/
@Slf4j
@Component
public class WaitPickByUnscheduledOrderCountCalculator extends AbstractBizIndicatorCalculator {
    @Override
    public List<BaseIndicatorEnum> getRelateBaseIndicators() {
        return Collections.singletonList(BaseIndicatorEnum.WAIT_PICK_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT);
    }

    @Override
    public BizIndicatorEnum getSupportIndicator() {
        return BizIndicatorEnum.WAIT_PICK_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT;
    }

    @Override
    public List<IndicatorDTO> calc(Map<String, List<IndicatorDTO>> baseIndicatorMap) {
        return calcBySingleBaseIndicator(baseIndicatorMap);
    }
}
