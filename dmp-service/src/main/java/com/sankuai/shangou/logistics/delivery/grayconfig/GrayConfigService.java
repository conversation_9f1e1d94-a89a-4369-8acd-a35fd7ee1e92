package com.sankuai.shangou.logistics.delivery.grayconfig;


import com.dianping.cat.Cat;
import com.dianping.squirrel.client.StoreKey;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.utils.retry.RetryTemplateUtil;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import com.sankuai.shangou.logistics.delivery.grayconfig.cache.GrayConfigCache;
import com.sankuai.shangou.logistics.delivery.grayconfig.dto.GrayConfig;
import com.sankuai.shangou.logistics.delivery.grayconfig.repository.GrayConfigRepository;
import com.sankuai.shangou.logistics.delivery.common.wrapper.OswClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/4/19 15:51
 **/
@Service
@Slf4j
public class GrayConfigService {
    @Resource
    private GrayConfigRepository grayConfigRepository;

    @Resource
    private OswClient oswClient;

    @Resource
    private GrayConfigCache grayConfigCache;

    @MethodLog(logRequest = true, logResponse = true)
    public Boolean judgeIsGrayStore(String grayKey, Long tenantId,  Long storeId) {
        //先读缓存,缓存能取到直接返回
        Optional<Boolean> isGrayStoreOpt = grayConfigCache.get(grayKey, storeId);
        if (isGrayStoreOpt.isPresent()) {
            return isGrayStoreOpt.get();
        } else {
            //查门店信息
            WarehouseDTO warehouseDTO = null;
            try {
                warehouseDTO = RetryTemplateUtil.simpleWithFixedRetry(3, 50)
                        .execute(new RetryCallback<WarehouseDTO, Exception>() {
                            @Override
                            public WarehouseDTO doWithRetry(RetryContext context) {
                                return oswClient.queryWarehouseInfo(tenantId, storeId);
                            }
                        });
            } catch (Exception e) {
                log.error("查询门店信息失败", e);
                throw new ThirdPartyException("查询门店信息失败");
            }


            //读取灰度配置
            GrayConfig grayConfig = grayConfigRepository.query(tenantId, grayKey, warehouseDTO.getOperationMode());

            if (Objects.isNull(grayConfig)) {
                return false;
            }

            //判断是否命中灰度名单
            boolean isGrayStore = judgeIsGrayStore(grayConfig, warehouseDTO);

            //写入缓存
            grayConfigCache.set(grayKey, storeId, isGrayStore);
            return isGrayStore;
        }
    }



    @MethodLog(logRequest = true, logResponse = true)
    public List<Long> batchJudgeIsGrayStore(String grayKey, Long tenantId,  List<Long> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Collections.emptyList();
        }

        //查门店信息
        Map<Long, WarehouseDTO> warehouseInfoMap = oswClient.queryWarehouseInfoList(tenantId, storeIds);

        //读取灰度配置
        List<GrayConfig> grayConfigList = grayConfigRepository.query(tenantId, grayKey);
        Map<Integer, GrayConfig> grayConfigMap = grayConfigList.stream()
                .collect(Collectors.toMap(GrayConfig::getStoreOperationMode, Function.identity(), (k1, k2) -> k1));

        //筛选出命中灰度名单的门店
        return storeIds
                .stream()
                .filter(storeId -> {
                    WarehouseDTO warehouseDTO = warehouseInfoMap.get(storeId);

                    //异常情况打点
                    if (Objects.isNull(warehouseDTO)) {
                        log.error("没查到门店信息, storeId: {}", storeId);
                        Cat.logEvent("GRAY_UTILS", "NOT_GET_STORE_INFO");
                        return false;
                    }

                    //不区分经营模式
                    GrayConfig allOperationModeGrayConfig = grayConfigMap.get(-1);
                    if (Objects.nonNull(allOperationModeGrayConfig)) {
                        return judgeIsGrayStore(allOperationModeGrayConfig, warehouseDTO);
                    }

                    //区分经营模式
                    GrayConfig grayConfig = grayConfigMap.get(warehouseDTO.getOperationMode());
                    if (Objects.isNull(grayConfig)) {
                        return false;
                    }

                    return judgeIsGrayStore(grayConfig, warehouseDTO);
                }).collect(Collectors.toList());

    }

    /**
     * 参考 https://km.sankuai.com/collabpage/2204954898#id-5.3%20%E7%81%B0%E5%BA%A6%E6%96%B9%E6%A1%88
     * @param grayConfig 灰度配置
     * @param warehouseDTO 门店信息
     * @return 是否命中灰度
     */
    private boolean judgeIsGrayStore(GrayConfig grayConfig, WarehouseDTO warehouseDTO) {
        Long storeId = warehouseDTO.getId();
        Integer cityId = warehouseDTO.getRegion().getCityId();

        List<Integer> grayCityList = grayConfig.getGrayCityList();
        List<Long> grayStoreList = grayConfig.getGrayStoreList();

        //如果城市全量,不管门店配置什么,都算全量
        if (CollectionUtils.isNotEmpty(grayCityList) && Objects.equals(grayCityList.get(0), -1)) {
            return true;
        }

        //如果未指定城市,门店配置全量,也算全量
        if (CollectionUtils.isEmpty(grayCityList) && CollectionUtils.isNotEmpty(grayStoreList)
                && Objects.equals(grayStoreList.get(0), -1L)) {
            return true;
        }

        return grayCityList.contains(cityId) || grayStoreList.contains(storeId);
    }
}
