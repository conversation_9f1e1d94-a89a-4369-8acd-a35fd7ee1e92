package com.sankuai.shangou.logistics.delivery.seal.request;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/6/28 15:45
 **/
@Data
public class ReturnContainerRequest {
    private Long warehouseId;

    private String sealContainerCode;

    private Boolean skipDeliveryStatusCheck;

    public String validate() {
        if (Objects.isNull(warehouseId)) {
            return "仓id不能为空";
        }

        if (StringUtils.isBlank(sealContainerCode)) {
            return "容具编码不能为空";
        }

        return null;
    }
}
