package com.sankuai.shangou.logistics.delivery.indicator.task;

import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.shangou.logistics.delivery.indicator.service.es.ESConfigService;
import com.sankuai.shangou.logistics.delivery.indicator.utils.IndicatorIndexNameUtils;
import com.sankuai.shangou.logistics.delivery.indicator.utils.MccUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/11/3 14:34
 **/

@Service
@Slf4j
public class ESIndexConfigCraneTask {

    @Resource
    private ESConfigService esConfigService;

    @Crane("create-next-month-indicator-index")
    public void createNextMonthIndexAndAlias() throws Exception {
        //创建索引和索引别名
        createDrunkHorseIndexAndAlias();

        //删除2个月前的索引关联的索引别名
        remove2monthAgoIndexAlias();
    }

    private void createDrunkHorseIndexAndAlias() throws Exception {
        //获取下个月的索引名字
        LocalDateTime nextMonthFirstDay = LocalDateTime.now().plusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
        String nextMonthIndexName = IndicatorIndexNameUtils.getIndexName(nextMonthFirstDay);

        //创建索引
        if (!esConfigService.existsIndex(nextMonthIndexName)) {
            log.info("创建歪马索引:{}", nextMonthIndexName);
            esConfigService.createIndex(nextMonthIndexName);
        }

        // 获取索引别名
        String indexAlias = MccUtils.getLast3MonthsIndexAlias();

        // 新增索引别名
        if (!esConfigService.existsIndexAlias(nextMonthIndexName, indexAlias)) {
            esConfigService.modifyAlias(nextMonthIndexName, Collections.singletonList(indexAlias), Collections.emptyList());
            log.info("创建索引别名成功->{}, indexAlias:{}", nextMonthIndexName, indexAlias);
        }

    }

    private void remove2monthAgoIndexAlias() throws Exception {
        log.info("开始删除2个月前的索引关联的索引别名");

        LocalDateTime twoMonthAgoFirstDay = LocalDateTime.now().minusMonths(2).with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);

        String twoMonthAgoIndexName = IndicatorIndexNameUtils.getIndexName(twoMonthAgoFirstDay);
        String indexAlias = MccUtils.getLast3MonthsIndexAlias();

        //删除索引别名
        if (!esConfigService.existsIndexAlias(twoMonthAgoIndexName, indexAlias)) {
            log.warn("要删除的索引别名不存在, 跳过删除, index:{}, alias:{}", twoMonthAgoIndexName, indexAlias);
            return;
        }
        esConfigService.modifyAlias(twoMonthAgoIndexName, Collections.emptyList(), Collections.singletonList(indexAlias));
        log.info("删除2个月前的索引关联的索引别名成功->{}, aliasConfig:{}", twoMonthAgoIndexName, indexAlias);
    }
}
