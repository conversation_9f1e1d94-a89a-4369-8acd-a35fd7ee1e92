package com.sankuai.shangou.logistics.delivery.configure.pojo.request;

import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.logistics.delivery.common.enums.DimensionEnum;
import com.sankuai.shangou.logistics.delivery.enums.OperationModeEnum;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.enums.DeliveryConfigTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-25
 */
@Data
public class DeliveryConfigBatchSaveParam {
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * tab类型： 1-按租户设置,2-按经营模式设置,3-按指定门店设置
     *
     * @see DimensionEnum
     */
    private Integer tabType;
    /**
     * tab子类型
     *
     * @see OperationModeEnum 经营模式: 1-直营, 2-加盟
     */
    private Integer tabChildType;

    /**
     * 是否修改存量门店
     */
    private Boolean isSyncWhenSave;

    /**
     * 门店列表
     */
    private List<Long> poiList;

    /**
     * 配置类型
     *
     * @see DeliveryConfigTypeEnum code
     */
    private List<Integer> configTypeList;

    /**
     * 配送平台配置
     */
    private List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> deliveryPlatformConfig;

    /**
     * 自配送配置
     */
    private DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfig;


    private String operator;

    private String operatorName;

    private Long tenantId;

    public void validate() {
        if (StringUtils.isBlank(taskName)) {
            throw new BizException("任务名称不能为空");
        }
        if (tabType == null) {
            throw new BizException("tab类型不能为空");
        }
        if (tabType == 2) {
            OperationModeEnum.enumOf(tabChildType);
        }
        if (CollectionUtils.isEmpty(configTypeList)) {
            throw new BizException("配置类型不能为空");
        }
        if (Objects.isNull(deliveryPlatformConfig) || Objects.isNull(selfDeliveryConfig)) {
            throw new BizException("配送配置不能为空");
        }
        if (tabType == 3 && CollectionUtils.isEmpty(poiList)) {
            throw new BizException("选择门店不能为空");
        }
    }

}
