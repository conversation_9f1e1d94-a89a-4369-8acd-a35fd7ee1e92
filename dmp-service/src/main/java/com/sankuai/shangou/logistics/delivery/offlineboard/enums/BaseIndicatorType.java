package com.sankuai.shangou.logistics.delivery.offlineboard.enums;

import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/3/13 17:45
 **/
public enum BaseIndicatorType {
    STRING(1),
    NUMBER(2),
    BOOLEAN(3);

    int code;


    BaseIndicatorType(int code) {
        this.code = code;
    }

    public static Object formatValue(String value, BaseIndicatorType type) {

        switch (type) {
            case STRING:
                if (value == null) {
                    return null;
                }
                return value;
            case NUMBER:
                if (value == null) {
                    return 0;
                }
                return StringUtils.isBlank(value) ? null : new BigDecimal(value);
            case BOOLEAN:
                if (value == null) {
                    return null;
                }
                return StringUtils.isBlank(value) ? null : new Boolean(value);

            default:
                throw new BizException("不识别的类型");
        }
    }
}
