package com.sankuai.shangou.logistics.delivery.poi.utils;

import com.dianping.lion.client.Lion;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/3 21:20
 **/
public class MccUtils {
    public static int getRhinoRetryMaxCount(){
        return Lion.getConfigRepository().getIntValue("dap.store.sync.retry.max.count", 3);
    }

    public static int getDapStoreSyncRhinoSleepTime(){
        return Lion.getConfigRepository().getIntValue("dap.store.sync.rhino.sleep.time", 1000);
    }

    public static List<Long> getNotInitShippingAreaRelationStoreIds(){
        return Lion.getConfigRepository().getList("not.init.shipping.area.relation.storeIds", Long.class);
    }
}
