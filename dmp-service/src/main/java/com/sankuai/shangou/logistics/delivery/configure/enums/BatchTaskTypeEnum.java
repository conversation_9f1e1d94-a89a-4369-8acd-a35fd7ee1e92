package com.sankuai.shangou.logistics.delivery.configure.enums;

import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 批量任务类型
 * @date 2025-07-01
 */
@Getter
public enum BatchTaskTypeEnum {
    QNH_DELIVERY_CONFIG_TEMPLATE_SYNC(1, "配置模版批量同步"),
    QNH_DELIVERY_CONFIG_POI(2, "门店批量变更"),

    CHANNEL_SHIPPING_AREA_RULE(3, "渠道配送范围规则批量设置");

    private final int code;
    private final String desc;

    BatchTaskTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BatchTaskTypeEnum enumOf(Integer code) {
        return Stream.of(values()).filter(item -> Objects.equals(item.getCode(),code))
                .findFirst().orElse(null);
    }
}
