package com.sankuai.shangou.logistics.delivery.xm.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description 卡片标题枚举
 * @date 2025-05-21
 */
@Getter
public enum CardTitleEnum {
    LAUNCH_FAILED("配送发单失败", "配送发单失败处理", CardColorEnum.RED),
    LAUNCH_FAILED_HANDLED("配送发单失败-补偿完成", "配送发单失败处理", CardColorEnum.GREEN),
    LAUNCH_FAILED_CANCEL("配送发单失败-补偿取消", "配送发单失败处理", CardColorEnum.GRAY),
    ;
    private final String title;
    private final String desc;
    private final CardColorEnum color;

    CardTitleEnum(String title, String desc, CardColorEnum color) {
        this.title = title;
        this.desc = desc;
        this.color = color;
    }
}
