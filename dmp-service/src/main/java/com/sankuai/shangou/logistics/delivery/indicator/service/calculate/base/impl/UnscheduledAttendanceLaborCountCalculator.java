package com.sankuai.shangou.logistics.delivery.indicator.service.calculate.base.impl;

import com.google.common.collect.Lists;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ShiftAndScheduleCountDTO;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrderWithCurrentRider;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BaseIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.repository.dbo.IndicatorDoc;
import com.sankuai.shangou.logistics.delivery.indicator.service.calculate.base.AbstractBaseIndicatorCalculator;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.indicator.utils.IndicatorSquirrelKeyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-07-03
 * @email <EMAIL>
 */
@Slf4j
@Service
public class UnscheduledAttendanceLaborCountCalculator extends AbstractBaseIndicatorCalculator {

    @Override
    public BaseIndicatorEnum getSupportIndicator() {
        return BaseIndicatorEnum.UNSCHEDULED_ATTENDANCE_EMPLOYEE_COUNT;
    }

    @Override
    public List<IndicatorDoc> calc(LocalDateTime fetchTime, List<IndicatorSquirrelKeyUtils.IndicatorStoreKey> indicatorStoreKeys,
                                   List<TRiderDeliveryOrderWithCurrentRider> thisDeliveryOrders,
                                   Map<Long, ShiftAndScheduleCountDTO> idShiftDTOMap,
                                   Map<Long, Long> employeeAccountIdMap, Map<Long, List<ShiftAndScheduleCountDTO>> poiIdAndShiftDTOListMap) {
        List<IndicatorDoc> indicatorDocList = Lists.newArrayList();
        Map<Long, List<IndicatorSquirrelKeyUtils.IndicatorStoreKey>> shiftIdListMap = IListUtils.nullSafeGroupBy(indicatorStoreKeys, IndicatorSquirrelKeyUtils.IndicatorStoreKey::getShiftId);
        for (Map.Entry<Long, List<IndicatorSquirrelKeyUtils.IndicatorStoreKey>> entry : shiftIdListMap.entrySet()) {
            ShiftAndScheduleCountDTO shiftAndScheduleCountDTO = idShiftDTOMap.get(entry.getKey());
            if (Objects.isNull(shiftAndScheduleCountDTO) && !Objects.equals(entry.getKey(), IndicatorSquirrelKeyUtils.UNSCHEDULED_SHIFT_ID)) {
                continue;
            }
            List<IndicatorSquirrelKeyUtils.IndicatorStoreKey> unscheduledList = IListUtils
                    .nullSafeStream(entry.getValue())
                    .filter(indicatorStoreKey -> !indicatorStoreKey.isShiftScheduled())
                    .collect(Collectors.toList());
            indicatorDocList.addAll(
                    buildByShiftGroupIndicatorDocList(entry, idShiftDTOMap, new BigDecimal(unscheduledList.size()))
            );
        }

        return indicatorDocList;
    }
}
