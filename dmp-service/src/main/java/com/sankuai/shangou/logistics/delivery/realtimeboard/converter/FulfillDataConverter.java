package com.sankuai.shangou.logistics.delivery.realtimeboard.converter;


import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ScheduledEmployeeDTO;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryRiderStatistics;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryStoreStatistics;
import com.sankuai.shangou.bizmng.labor.api.employee.dto.EmployeeAttendanceInfoDTO;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmpDepRelDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.PositionDTO;
import com.sankuai.shangou.logistics.delivery.fulfill.dto.*;
import com.sankuai.shangou.logistics.delivery.fulfill.enums.ChannelTypeEnum;
import com.sankuai.shangou.logistics.delivery.fulfill.enums.CompareTypeEnum;
import com.sankuai.shangou.logistics.delivery.fulfill.enums.IndicatorTypeEnum;
import com.sankuai.shangou.logistics.delivery.fulfill.request.SegmentationFulfillDataQueryRequest;
import com.sankuai.shangou.logistics.delivery.realtimeboard.utils.MccUtils;
import com.sankuai.waimai.dws.protocol.structs.DWSResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.shangou.logistics.delivery.fulfill.enums.IndicatorTypeEnum.*;


/**
 * <AUTHOR>
 * @Date 2022/7/29
 **/
public class FulfillDataConverter {

    private static final String EMPTY_STRING = "";

    private static final int TIME_AXIS_GRID = 5;

    private static final List<String> timeAxisList = new ArrayList<>();

    public static final LocalTime SIX_AM = LocalTime.of(6, 0);

    private static final String SEGMENT_DATA_DEFAULT_VALUE = "0";

    private static final Integer ZERO = 0;

    private static final String SPLIT_STRING = ",";

    static {
        generateTimeAxis();
    }



    public static FulfillSummaryRealTimeResp buildFulfillSummaryRealTimeResp(List<Long> storeList,
                                                                             Map<Long, PoiInfoDto> storeDetails,
                                                                             Map<Long, TDeliveryStoreStatistics> dbData,
                                                                             Map<Long, List<ScheduledEmployeeDTO>> onDutyEmployeeListMap,
                                                                             List<EmpDepRelDTO> empDepRelDTOS) {
        //总骑手数
        int deliveringRiderNum = 0;
        //总运单数
        int totalUndoneTask = 0;
        //门店维度超载人均负载
        List<OverLoadStoreInfo> overLoadStoreInfoList = new ArrayList<>();
        FulfillSummaryRealTimeResp response = new FulfillSummaryRealTimeResp();
        for (Long storeId : storeList) {
            TDeliveryStoreStatistics storeData = dbData.get(storeId);
            if (storeData != null) {
                // 处理各阶段单量 上游递storeData的时候，有默认值
                response.setWaitToAcceptOrder(response.getWaitToAcceptOrder() +
                        Optional.ofNullable(storeData.getWaitToAccept()).orElse(ZERO));
                response.setPickingOrder(response.getPickingOrder() +
                        Optional.ofNullable(storeData.getPicking()).orElse(ZERO));
                response.setDeliveringOrder(response.getDeliveringOrder() +
                        Optional.ofNullable(storeData.getDelivering()).orElse(ZERO));

                // 处理各阶段超时单量
                response.setWaitToAcceptJudgeTimeoutOrder(response.getWaitToAcceptJudgeTimeoutOrder() +
                        Optional.ofNullable(storeData.getWaitToAcceptJudgeTimeout()).orElse(ZERO));
                response.setWaitToAcceptEtaTimeoutOrder(response.getWaitToAcceptEtaTimeoutOrder() +
                        Optional.ofNullable(storeData.getWaitToAcceptJudgeTimeoutV2()).orElse(ZERO));

                response.setPickingJudgeTimeoutOrder(response.getPickingJudgeTimeoutOrder() +
                        Optional.ofNullable(storeData.getPickingJudgeTimeout()).orElse(ZERO));
                response.setPickingEtaTimeoutOrder(response.getPickingEtaTimeoutOrder() +
                        Optional.ofNullable(storeData.getPickingJudgeTimeoutV2()).orElse(ZERO));

                response.setDeliveringJudgeTimeoutOrder(response.getDeliveringJudgeTimeoutOrder() +
                        Optional.ofNullable(storeData.getDeliveringJudgeTimeout()).orElse(ZERO));
                response.setDeliveringEtaTimeoutOrder(response.getDeliveringEtaTimeoutOrder() +
                        Optional.ofNullable(storeData.getDeliveringJudgeTimeoutV2()).orElse(ZERO));

                totalUndoneTask += Optional.ofNullable(storeData.getFulfilling()).orElse(ZERO);
                // 处理store的人均负载，即超载门店信息
                deliveringRiderNum += Optional.ofNullable(storeData.getFulfillingRider()).orElse(ZERO);
                if (storeData.getFulfillingRider() != null && storeData.getFulfillingRider() > 0) {
                    String avgRiderLoadString = divide(storeData.getFulfilling(), storeData.getFulfillingRider(), 2);
                    BigDecimal avgRiderLoad = new BigDecimal(Optional.ofNullable(avgRiderLoadString).orElse("0"));
                    //人均负载大于3才算超载
                    if (avgRiderLoad.compareTo(new BigDecimal(3)) > 0) {
                        overLoadStoreInfoList.add(new OverLoadStoreInfo(storeId,
                                Optional.ofNullable(storeDetails.get(
                                        storeData.getStoreId())).map(PoiInfoDto::getPoiName).orElse(null),
                                avgRiderLoadString));
                    }
                }
            }
        }
        overLoadStoreInfoList.sort((o1, o2) -> {
            if (StringUtils.isNotBlank(o1.getAvgRiderLoad()) && StringUtils.isNotBlank(o2.getAvgRiderLoad())) {
                return new BigDecimal(o2.getAvgRiderLoad()).compareTo(new BigDecimal(o1.getAvgRiderLoad()));
            } else if (StringUtils.isNotBlank(o1.getAvgRiderLoad())) {
                return -1;
            } else {
                return 0;
            }
        });
        // 截断处理
        List<OverLoadStoreInfo> finalOverLoadStoreInfos = overLoadStoreInfoList.subList(0, Math.min(overLoadStoreInfoList.size(), 10));
        // 对全局信息处理
        response.setOverLoadStoreInfos(finalOverLoadStoreInfos);
        response.setDeliveringRider(deliveringRiderNum);
        //计算空闲中骑手数
        if (!onDutyEmployeeListMap.isEmpty()) {
            Map<Long, Boolean> employeeIsPickerMap = empDepRelDTOS.stream()
                    .collect(Collectors.groupingBy(EmpDepRelDTO::getEmpId))
                    .entrySet()
                    .stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream().anyMatch(empDepRelDTO -> MccUtils.pickerPositionIds().contains(empDepRelDTO.getPositionId()))));
            //过滤拣货员
            Integer onDutyEmployeeTotalCount = Math.toIntExact(onDutyEmployeeListMap.values()
                    .stream()
                    .flatMap(Collection::stream)
                    .filter(employee -> !employeeIsPickerMap.getOrDefault(employee.getEmployeeId(), false))
                    .count());

            response.setIdleRider(onDutyEmployeeTotalCount - deliveringRiderNum);
        }


        // 处理履约中人均负载
        response.setAvgRiderLoad(divide(totalUndoneTask, deliveringRiderNum, 2));
        return response;
    }
    public static FulfillSummaryCumulativeResp buildFulfillSummaryCumulativeResp(DWSResponse responseData) {
        if (Objects.isNull(responseData) || Objects.isNull(responseData.getData()) || CollectionUtils.isEmpty(responseData.getData().getDataList())) {
            throw new BizException("数据为空");
        }

        Map<String, String> dataMap = responseData.getData().getDataList().get(0);

        FulfillSummaryCumulativeResp resp = new FulfillSummaryCumulativeResp();
        resp.setTotalValidOrder(Optional.ofNullable(dataMap.get("finOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setRefTotalValidOrder(Optional.ofNullable(dataMap.get("refFinOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setChannelValidOrderCountList(transform2ValidOrderCountChannelInfoList(dataMap.get("wmFinOrdNum"), dataMap.get("wscFinOrdNum")));
        resp.setDeliveredOrder(Optional.ofNullable(dataMap.get("arrivedOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setRefDeliveredOrder(Optional.ofNullable(dataMap.get("refArrivedOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setSeriousTimeoutOrder(Optional.ofNullable(dataMap.get("badOvertimeOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setRefSeriousTimeoutOrder(Optional.ofNullable(dataMap.get("refBadOvertimeOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setJudgeTimeoutOrder(Optional.ofNullable(dataMap.get("checkOvertimeOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setRefJudgeTimeoutOrder(Optional.ofNullable(dataMap.get("refCheckOvertimeOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setWorkedEmployeeNum(Optional.ofNullable(dataMap.get("attendanceNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setRefWorkedEmployee(Optional.ofNullable(dataMap.get("refAttendanceNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setNinetiethFulfillDuration(processPrecision(dataMap.get("ninePerformanceDuration")));
        resp.setRefNinetyFulfillDuration(processPrecision(dataMap.get("refNinePerformanceDuration")));
        resp.setAvgFulfillDuration(processPrecision(dataMap.get("performanceDuration")));
        resp.setRefAvgFulfillDuration(processPrecision(dataMap.get("refPerformanceDuration")));
        // 严重超时率：严重超时订单量 / 已送达订单量
        resp.setSeriousTimeoutRatio(calPercent(dataMap.get("badOvertimeOrdNum"), dataMap.get("arrivedOrdNum")));
        resp.setRefSeriousTimeoutRatio(calPercent(dataMap.get("refBadOvertimeOrdNum"),dataMap.get("refArrivedOrdNum")));
        // ETA超时率：（实际送达时间-预计送达时间）>0min的送达订单量 / 已送达订单量
        resp.setEtaTimeoutRatio(calPercent(dataMap.get("etaOvertimeOrdNum"), dataMap.get("arrivedOrdNum")));
        resp.setRefEtaTimeoutRatio(calPercent(dataMap.get("refEtaOvertimeOrdNum"), dataMap.get("refArrivedOrdNum")));

        //只有有已送达订单时，才去计算送达率
        if (Objects.nonNull(dataMap.get("arrivedOrdNoPreNum")) && !Objects.equals(Integer.valueOf(dataMap.get("arrivedOrdNoPreNum")), 0)) {
            resp.setDeliveredRateIn25min(calPercent(dataMap.get("twentyFiveMinArrOrdNum"), dataMap.get("arrivedOrdNoPreNum")));
            resp.setDeliveredRateIn15min(calPercent(dataMap.get("fifteenMinArrOrdNum"), dataMap.get("arrivedOrdNoPreNum")));
        }

        if (Objects.nonNull(dataMap.get("refArrivedOrdNoPreNum")) && !Objects.equals(Integer.valueOf(dataMap.get("arrivedOrdNoPreNum")), 0)) {
            resp.setRefDeliveredRateIn25min(calPercent(dataMap.get("refTwentyFiveMinArrOrdNum"), dataMap.get("refArrivedOrdNoPreNum")));
            resp.setRefDeliveredRateIn15min(calPercent(dataMap.get("refFifteenMinArrOrdNum"), dataMap.get("refArrivedOrdNoPreNum")));
        }

        // ETA超时口径调整新增一套指标
        resp.setEtaOvertimeOrdNumV2(Optional.ofNullable(dataMap.get("etaOvertimeOrdNumV2")).map(Long::parseLong).orElse(ZERO.longValue()));
        resp.setEtaBadOvertimeOrdNumV2(Optional.ofNullable(dataMap.get("etaBadOvertimeOrdNumV2")).map(Long::parseLong).orElse(ZERO.longValue()));
        resp.setRefEtaOvertimeOrdNumV2(Optional.ofNullable(dataMap.get("refEtaOvertimeOrdNumV2")).map(Long::parseLong).orElse(ZERO.longValue()));
        resp.setRefEtaBadOvertimeOrdNumV2(Optional.ofNullable(dataMap.get("refEtaBadOvertimeOrdNumV2")).map(Long::parseLong).orElse(ZERO.longValue()));
        resp.setEtaOntimeRatioV2(
                calPercent(
                    Optional.ofNullable(dataMap.get("arrivedOrdNum")).map(BigDecimal::new).orElse(BigDecimal.ZERO)
                        .subtract(Optional.ofNullable(dataMap.get("etaOvertimeOrdNumV2")).map(BigDecimal::new).orElse(BigDecimal.ZERO)),
                    Optional.ofNullable(dataMap.get("arrivedOrdNum")).map(BigDecimal::new).orElse(BigDecimal.ZERO)
                )
        );
        resp.setEtaBadOvertimeRatioV2(calPercent(dataMap.get("etaBadOvertimeOrdNumV2"), dataMap.get("arrivedOrdNum")));
        resp.setRefEtaOntimeRatioV2(
                calPercent(
                        Optional.ofNullable(dataMap.get("refArrivedOrdNum")).map(BigDecimal::new).orElse(BigDecimal.ZERO)
                                .subtract(Optional.ofNullable(dataMap.get("refEtaOvertimeOrdNumV2")).map(BigDecimal::new).orElse(BigDecimal.ZERO)),
                        Optional.ofNullable(dataMap.get("refArrivedOrdNum")).map(BigDecimal::new).orElse(BigDecimal.ZERO)
                )
        );
        resp.setRefEtaBadOvertimeRatioV2(calPercent(dataMap.get("refEtaBadOvertimeOrdNumV2"), dataMap.get("refArrivedOrdNum")));
        return resp;
    }

    public static FulfillSummaryCarrierResp buildFulfillSummaryCarrierResp(List<Map<String,String>> responseData) {
        if (CollectionUtils.isEmpty(responseData)) {
            throw new BizException("数据为空");
        }
        // 填充数仓信息
        FulfillSummaryCarrierResp resp = new FulfillSummaryCarrierResp();
        Map<String, String> dataMap = responseData.get(0);
        resp.setTransferOrder(Optional.ofNullable(dataMap.get("threeAllDelieryOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setRefTransferOrder(Optional.ofNullable(dataMap.get("refThreeAllDelieryOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setArrivedOrder(Optional.ofNullable(dataMap.get("threeArrivedOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setRefArrivedOrder(Optional.ofNullable(dataMap.get("refThreeArrivedOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setCanceledOrder(Optional.ofNullable(dataMap.get("threeCancelOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setRefCanceledOrder(Optional.ofNullable(dataMap.get("refThreeCancelOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setCanceledAfterAcceptingOrder(Optional.ofNullable(dataMap.get("threeJiedanCancelOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setRefCanceledAfterAcceptingOrder(Optional.ofNullable(dataMap.get("refThreeJiedanCancelOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setCanceledAfterPickingOrder(Optional.ofNullable(dataMap.get("threeQuhuoCancelOrdNum")).map(Integer::parseInt).orElse(ZERO));
        resp.setRefCanceledAfterPickingOrder(Optional.ofNullable(dataMap.get("refThreeQuhuoCancelOrdNum")).map(Integer::parseInt).orElse(ZERO));

        resp.setAcceptRatio(calPercent(dataMap.get("threeHave2PoiOrdNum"), dataMap.get("threeAllDelieryOrdNum")));
        resp.setRefAcceptRatio(calPercent(dataMap.get("refThreeHave2PoiOrdNum"), dataMap.get("refThreeAllDelieryOrdNum")));

        resp.setArriveAfterTransferRatio(calPercent(dataMap.get("threeArrivedOrdNum"), dataMap.get("threeAllDelieryOrdNum")));
        resp.setRefArriveAfterTransferRatio(calPercent(dataMap.get("refThreeArrivedOrdNum"), dataMap.get("refThreeAllDelieryOrdNum")));
        resp.setArriveAfterAcceptingRatio(calPercent(dataMap.get("threeArrivedOrdNum"), dataMap.get("threeHave2PoiOrdNum")));
        resp.setRefArriveAfterAcceptingRatio(calPercent(dataMap.get("refThreeArrivedOrdNum"), dataMap.get("refThreeHave2PoiOrdNum")));
        resp.setAvgDeliveringCost(divide(dataMap.get("threeDeliverFee"),dataMap.get("threeArrivedOrdNum")));
        resp.setRefAvgDeliveringCost(divide(dataMap.get("refThreeDeliverFee"),dataMap.get("refThreeArrivedOrdNum")));

        resp.setTips(processPrecision(dataMap.get("tipAmount")));
        resp.setRefTips(processPrecision(dataMap.get("refTipAmount")));

        resp.setAvgAcceptDuration(divide(dataMap.get("threeJieDanDuration"),dataMap.get("threeHave2PoiOrdNum")));
        resp.setRefAvgAcceptDuration(divide(dataMap.get("refThreeJieDanDuration"),dataMap.get("refThreeHave2PoiOrdNum")));
        resp.setAvgFulfillDuration(divide(dataMap.get("threePerformanceDuration"),dataMap.get("threeArrivedOrdNoPreNum")));
        resp.setRefAvgFulfillDuration(divide(dataMap.get("refThreePerformanceDuration"),dataMap.get("refThreeArrivedOrdNoPreNum")));
        resp.setAvgCarrierFulfillDuration(divide(dataMap.get("threeYunliPerformanceDuration"),dataMap.get("threeArrivedOrdNum")));
        resp.setRefAvgCarrierFulfillDuration(divide(dataMap.get("refThreeYunliPerformanceDuration"),dataMap.get("refThreeArrivedOrdNum")));
        resp.setAvgDeliveryDuration(divide(dataMap.get("threePeisongDuration"),dataMap.get("threeArrivedOrdNum")));
        resp.setRefAvgDeliveryDuration(divide(dataMap.get("refThreePeisongDuration"),dataMap.get("refThreeArrivedOrdNum")));

        resp.setDeliveredRateIn25min(calPercent(dataMap.get("threeTwentyFiveMinArrOrdNum"),dataMap.get("threeArrivedOrdNoPreNum")));
        resp.setRefDeliveredRateIn25min(calPercent(dataMap.get("refThreeTwentyFiveMinArrOrdNum"),dataMap.get("refThreeArrivedOrdNoPreNum")));
        resp.setDeliveredRateIn45min(calPercent(dataMap.get("threeFortyFiveMinArrOrdNum"),dataMap.get("threeArrivedOrdNoPreNum")));
        resp.setRefDeliveredRateIn45min(calPercent(dataMap.get("refThreeFortyFiveMinArrOrdNum"),dataMap.get("refThreeArrivedOrdNoPreNum")));

        resp.setEtaOntimeRatioV2(calPercent(
                Optional.ofNullable(dataMap.get("threeArrivedOrdNum")).map(BigDecimal::new).orElse(BigDecimal.ZERO)
                        .subtract(Optional.ofNullable(dataMap.get("etaOvertimeOrdNumV2")).map(BigDecimal::new).orElse(BigDecimal.ZERO)),
                Optional.ofNullable(dataMap.get("threeArrivedOrdNum")).map(BigDecimal::new).orElse(BigDecimal.ZERO)
        ));
        resp.setEtaBadOvertimeRatioV2(calPercent(dataMap.get("etaBadOvertimeOrdNumV2"), dataMap.get("threeArrivedOrdNum")));
        resp.setRefEtaOntimeRatioV2(calPercent(
                Optional.ofNullable(dataMap.get("refThreeArrivedOrdNum")).map(BigDecimal::new).orElse(BigDecimal.ZERO)
                        .subtract(Optional.ofNullable(dataMap.get("refEtaOvertimeOrdNumV2")).map(BigDecimal::new).orElse(BigDecimal.ZERO)),
                Optional.ofNullable(dataMap.get("refThreeArrivedOrdNum")).map(BigDecimal::new).orElse(BigDecimal.ZERO)
        ));
        resp.setRefEtaBadOvertimeRatioV2(calPercent(dataMap.get("refEtaBadOvertimeOrdNumV2"), dataMap.get("refThreeArrivedOrdNum")));
        return resp;
    }

    public static List<FulfillCarrierStatisticsDTO> buildFulfillCarrierStatisticsVO(List<Map<String, String>> responseData,
                                                                                    Map<Long, PoiInfoDto> storeId2Info,
                                                                                    Map<Long, String> storeId2Tier3Name,
                                                                                    Map<Long, String> storeId2Tier2Name,
                                                                                    Map<Integer, String> carrierCode2Name) {
        return Optional.ofNullable(responseData).orElse(Lists.newArrayList()).stream().map(data -> {
            Long poiId = Optional.ofNullable(data.get("poiId")).map(Long::valueOf).orElse(0L);
            FulfillCarrierStatisticsDTO vo = new FulfillCarrierStatisticsDTO();
            vo.setStoreId(poiId);
            vo.setStoreName(Optional.ofNullable(storeId2Info.get(poiId)).map(PoiInfoDto::getPoiName).orElse(null));
            vo.setDepartmentThirdTierName(storeId2Tier3Name.get(poiId));
            vo.setDepartmentSecondTierName(storeId2Tier2Name.get(poiId));
            Optional.ofNullable(data.get("carrierCode")).map(Integer::valueOf)
                    .ifPresent(carrierCode -> {
                        vo.setCarrierCode(carrierCode);
                        vo.setCarrierName(carrierCode2Name.get(carrierCode));
                    });
            vo.setTransferOrder(Optional.ofNullable(data.get("threeAllDelieryOrdNum")).map(Integer::valueOf).orElse(ZERO));
            vo.setArrivedOrder(Optional.ofNullable(data.get("threeArrivedOrdNum")).map(Integer::valueOf).orElse(ZERO));
            vo.setCanceledAfterAcceptingOrder(Optional.ofNullable(data.get("threeJiedanCancelOrdNum")).map(Integer::valueOf).orElse(ZERO));
            vo.setCanceledAfterPickingOrder(Optional.ofNullable(data.get("threeQuhuoCancelOrdNum")).map(Integer::valueOf).orElse(ZERO));
            Optional.ofNullable(calPercent(data.get("threeArrivedOrdNum"), data.get("threeHave2PoiOrdNum"))).ifPresent(percent -> {
                vo.setArrivedAfterAcceptingRatio(percent + "%");
            });

            vo.setAvgDeliveringCost(divide(data.get("threeDeliverFee"), data.get("threeArrivedOrdNum")));
            vo.setTips(Optional.ofNullable(processPrecision(data.get("tipAmount"))).orElse(ZERO.toString()));
            vo.setAvgAcceptDuration(divide(data.get("threeJieDanDuration"), data.get("threeHave2PoiOrdNum")));
            vo.setAvgFulfillDuration(divide(data.get("threePerformanceDuration"), data.get("threeArrivedOrdNoPreNum")));
            vo.setAvgCarrierFulfillDuration(divide(data.get("threeYunliPerformanceDuration"), data.get("threeArrivedOrdNum")));
            vo.setAvgDeliveryDuration(divide(data.get("threePeisongDuration"), data.get("threeArrivedOrdNum")));
            return vo;
        }).collect(Collectors.toList());

    }

    public static List<FulfillStoreStatisticsDTO> buildFulfillStoreStatistics(List<Long> storeIds,
                                                                              Map<Long, PoiInfoDto> storeDetails,
                                                                              Map<Long, String> departmentNameMap,
                                                                              Map<Long, SgStoreData> dwData,
                                                                              Map<Long, TDeliveryStoreStatistics> dbData,
                                                                              Map<Long, Integer> onDutyEmployeeCount) {
        return storeIds.stream().map(
                (storeId) -> {
                    SgStoreData dwStoreData = dwData.get(storeId);
                    TDeliveryStoreStatistics dbStoreData = dbData.get(storeId);
                    FulfillStoreStatisticsDTO responseVO = new FulfillStoreStatisticsDTO();
                    responseVO.setStoreId(storeId);
                    responseVO.setStoreName(Optional.ofNullable(storeDetails.get(storeId)).map(PoiInfoDto::getPoiName).orElse(null));

                    if (dwStoreData != null) {
                        responseVO.setValidOrder(dwStoreData.getFinOrdNum());
                        responseVO.setDeliveredOrder(dwStoreData.getArrivedOrdNum());
                        responseVO.setNinetiethFulfillDuration(dwStoreData.getNinePerformanceDuration());
                        responseVO.setAvgFulfillDuration(dwStoreData.getPerformanceDuration());
                        responseVO.setJudgeTimeoutOrder(dwStoreData.getCheckOvertimeOrdNum());
                        responseVO.setSeriousTimeoutOrder(dwStoreData.getBadOvertimeOrdNum());
                        responseVO.setWorkedEmployeeNum(dwStoreData.getAttendanceNum());

                        responseVO.setSeriousTimeoutOrderList(buildDeliveryOrderChannelInfos(dwStoreData.getBadOrds()));
                        responseVO.setJudgeTimeoutOrderList(buildDeliveryOrderChannelInfos(dwStoreData.getCheckOrds()));
                        responseVO.setDeliveredRateIn15Min(
                                calPercent(String.valueOf(dwStoreData.getFifteenMinArrOrdNum()),
                                        String.valueOf(dwStoreData.getArrivedOrdNoPreNum())));
                        responseVO.setDeliveredRateIn15MinDesc(convert2PercentDec(calPercent(String.valueOf(dwStoreData.getFifteenMinArrOrdNum()),
                                String.valueOf(dwStoreData.getArrivedOrdNoPreNum()))));
                        responseVO.setDeliveredRateIn25Min(
                                calPercent(String.valueOf(dwStoreData.getTwentyFiveMinArrOrdNum()),
                                        String.valueOf(dwStoreData.getArrivedOrdNoPreNum())));
                        responseVO.setDeliveredRateIn25MinDesc(convert2PercentDec(calPercent(String.valueOf(dwStoreData.getTwentyFiveMinArrOrdNum()),
                                String.valueOf(dwStoreData.getArrivedOrdNoPreNum()))));

                        responseVO.setEtaOvertimeOrdNumV2(dwStoreData.getEtaOvertimeOrdNumV2());
                        responseVO.setEtaBadOvertimeOrdNumV2(dwStoreData.getEtaBadOvertimeOrdNumV2());
                        responseVO.setEtaOntimeRatioV2(calPercent(
                                BigDecimal.valueOf(dwStoreData.getArrivedOrdNum()).subtract(BigDecimal.valueOf(dwStoreData.getEtaOvertimeOrdNumV2())),
                                BigDecimal.valueOf(dwStoreData.getArrivedOrdNum())
                        ));
                        responseVO.setEtaOntimeRatioV2Desc(convert2PercentDec(responseVO.getEtaOntimeRatioV2()));
                        responseVO.setEtaBadOvertimeRatioV2(calPercent(String.valueOf(dwStoreData.getEtaBadOvertimeOrdNumV2()), String.valueOf(dwStoreData.getArrivedOrdNum())));
                        responseVO.setEtaBadOvertimeRatioV2Desc(convert2PercentDec(responseVO.getEtaBadOvertimeRatioV2()));


                        if (Objects.nonNull(dwStoreData.getNearPoiFirstDepartmentId())) {
                            responseVO.setUpOneLevelDepartmentName(trimDepartmentName(departmentNameMap.get(dwStoreData.getNearPoiFirstDepartmentId())));
                        }
                        if (Objects.nonNull(dwStoreData.getNearPoiSecondDepartmentId())) {
                            responseVO.setUpTwoLevelDepartmentName(trimDepartmentName(departmentNameMap.get(dwStoreData.getNearPoiSecondDepartmentId())));
                        }

                        responseVO.setEtaTimeoutRate(calPercent(String.valueOf(dwStoreData.getEtaOvertimeOrdNum()),
                                String.valueOf(dwStoreData.getArrivedOrdNum())));
                        responseVO.setEtaTimeoutRateDesc(convert2PercentDec(calPercent(String.valueOf(dwStoreData.getEtaOvertimeOrdNum()),
                                String.valueOf(dwStoreData.getArrivedOrdNum()))));
                        responseVO.setEtaSeriouslyTimeoutRate(calPercent(String.valueOf(dwStoreData.getBadOvertimeOrdNum()),
                                String.valueOf(dwStoreData.getArrivedOrdNum())));
                        responseVO.setEtaSeriouslyTimeoutRateDesc(convert2PercentDec(calPercent(String.valueOf(dwStoreData.getBadOvertimeOrdNum()),
                                String.valueOf(dwStoreData.getArrivedOrdNum()))));

                    }
                    if (dbStoreData != null) {
                        responseVO.setWaitToAcceptOrder(dbStoreData.getWaitToAccept());
                        responseVO.setWaitToAcceptJudgeTimeoutOrder(dbStoreData.getWaitToAcceptJudgeTimeout());
                        responseVO.setPickingOrder(dbStoreData.getPicking());
                        responseVO.setPickingJudgeTimeoutOder(dbStoreData.getPickingJudgeTimeout());
                        responseVO.setPickingEtaOvertimeOrderV2(
                                Optional.ofNullable(dbStoreData.getPickingJudgeTimeoutV2()).map(Long::valueOf).orElse(0L));
                        responseVO.setDeliveringOrder(dbStoreData.getDelivering());
                        responseVO.setDeliveringJudgeTimeoutOrder(dbStoreData.getDeliveringJudgeTimeout());
                        responseVO.setDeliveringEtaOvertimeOrderV2(
                                Optional.ofNullable(dbStoreData.getDeliveringJudgeTimeoutV2()).map(Long::valueOf).orElse(0L));
                        responseVO.setFulfillRiderNum(dbStoreData.getFulfillingRider());
                        responseVO.setWaitToAcceptEtaTimeoutOrder(
                                Optional.ofNullable(dbStoreData.getWaitToAcceptJudgeTimeoutV2()).map(Long::valueOf).orElse(0L));
                        if (onDutyEmployeeCount.containsKey(storeId)) {
                            responseVO.setIdleRiderNum(onDutyEmployeeCount.get(storeId) - dbStoreData.getFulfillingRider());
                        }

                        responseVO.setAvgRiderLoad(
                                divide(dbStoreData.getFulfilling(),
                                        dbStoreData.getFulfillingRider(), 2));
                    }

                    return responseVO;
                }


        ).collect(Collectors.toList());
    }

    public static List<FulfillRiderStatisticsDTO> buildFulfillRiderStatistics(Long storeId, Map<Long, PoiInfoDto> storeDetails,
                                                                              Map<Long, StoreOrgData> storeOrgInfoMap,
                                                                              List<StaffRider> riderStaffs,
                                                                              Map<Long, SgRiderData> dwData,
                                                                              Map<Long, TDeliveryRiderStatistics> dbData,
                                                                              Map<Long, EmployeeAttendanceInfoDTO> riderAttendanceInfoMap,
                                                                              Map<Long, PositionDTO> positionDTOMap,
                                                                              Map<Long, List<EmpDepRelDTO>> employeeDepRelMap,
                                                                              Map<Long, String> departmentNameMap,
                                                                              Map<Long, String> accountNameMap) {
        return riderStaffs.stream().map(
                (riderStaff) -> {
                    SgRiderData dwRiderData = dwData.get(riderStaff.getAccountId());
                    TDeliveryRiderStatistics dbRiderData = dbData.get(riderStaff.getAccountId());
                    EmployeeAttendanceInfoDTO employeeAttendanceInfoDTO = riderAttendanceInfoMap.get(riderStaff.getAccountId());
                    StoreOrgData storeOrgData = storeOrgInfoMap.get(storeId);

                    FulfillRiderStatisticsDTO responseVO = new FulfillRiderStatisticsDTO();
                    responseVO.setRiderName(riderStaff.getName());
                    responseVO.setRiderAccountName(accountNameMap.get(riderStaff.getAccountId()));
                    responseVO.setStoreName(Optional.ofNullable(storeDetails.get(storeId)).map(PoiInfoDto::getPoiName).orElse(null));


                    if (dwRiderData != null) {
                        responseVO.setDeliveredOrder(dwRiderData.getArrivedOrdNum());
                        responseVO.setNinetiethFulfillDuration(dwRiderData.getNinePerformanceDuration());
                        responseVO.setAvgFulfillDuration(dwRiderData.getPerformanceDuration());
                        responseVO.setJudgeTimeoutOrder(dwRiderData.getCheckOvertimeOrdNum());
                        responseVO.setJudgeTimeoutOrderList(buildDeliveryOrderChannelInfos(dwRiderData.getCheckOrds()));
                        responseVO.setSeriousTimeoutOrder(dwRiderData.getBadOvertimeOrdNum());
                        responseVO.setSeriousTimeoutOrderList(buildDeliveryOrderChannelInfos(dwRiderData.getBadOrds()));

                        responseVO.setDeliveredRateIn15Min(
                                calPercent(String.valueOf(dwRiderData.getFifteenMinArrOrdNum()),
                                        String.valueOf(dwRiderData.getArrivedOrdNoPreNum())));
                        responseVO.setDeliveredRateIn15MinDesc(convert2PercentDec(calPercent(String.valueOf(dwRiderData.getFifteenMinArrOrdNum()),
                                String.valueOf(dwRiderData.getArrivedOrdNoPreNum()))));
                        responseVO.setDeliveredRateIn25Min(
                                calPercent(String.valueOf(dwRiderData.getTwentyFiveMinArrOrdNum()),
                                        String.valueOf(dwRiderData.getArrivedOrdNoPreNum())));
                        responseVO.setDeliveredRateIn25MinDesc(convert2PercentDec(calPercent(String.valueOf(dwRiderData.getTwentyFiveMinArrOrdNum()),
                                String.valueOf(dwRiderData.getArrivedOrdNoPreNum()))));

                        responseVO.setEtaTimeoutRate(calPercent(String.valueOf(dwRiderData.getEtaOvertimeOrdNum()),
                                String.valueOf(dwRiderData.getArrivedOrdNum())));
                        responseVO.setEtaTimeoutRateDesc(convert2PercentDec(calPercent(String.valueOf(dwRiderData.getEtaOvertimeOrdNum()),
                                String.valueOf(dwRiderData.getArrivedOrdNum()))));
                        responseVO.setEtaSeriouslyTimeoutRate(calPercent(String.valueOf(dwRiderData.getBadOvertimeOrdNum()),
                                String.valueOf(dwRiderData.getArrivedOrdNum())));
                        responseVO.setEtaSeriouslyTimeoutRateDesc(convert2PercentDec(calPercent(String.valueOf(dwRiderData.getBadOvertimeOrdNum()),
                                String.valueOf(dwRiderData.getArrivedOrdNum()))));

                        responseVO.setEtaOvertimeOrdNumV2(dwRiderData.getEtaOvertimeOrdNumV2());
                        responseVO.setEtaBadOvertimeOrdNumV2(dwRiderData.getEtaBadOvertimeOrdNumV2());
                        responseVO.setEtaOntimeRatioV2(calPercent(
                                BigDecimal.valueOf(dwRiderData.getArrivedOrdNum()).subtract(BigDecimal.valueOf(dwRiderData.getEtaOvertimeOrdNumV2())),
                                BigDecimal.valueOf(dwRiderData.getArrivedOrdNum())));
                        responseVO.setEtaOntimeRatioV2Desc(convert2PercentDec(responseVO.getEtaOntimeRatioV2()));
                        responseVO.setEtaBadOvertimeRatioV2(calPercent(String.valueOf(dwRiderData.getEtaBadOvertimeOrdNumV2()),
                                String.valueOf(dwRiderData.getArrivedOrdNum())));
                        responseVO.setEtaBadOvertimeRatioV2Desc(convert2PercentDec(responseVO.getEtaBadOvertimeRatioV2()));

                    } else {
                        //如果数仓没返回 这里手动补默认值 原因:员工维度数据是根据权限+角色来确定人员名单的,数仓只能通过组织结构来查人员名单,无法补齐数据
                        responseVO.setDeliveredOrder(ZERO);
                        responseVO.setJudgeTimeoutOrder(ZERO);
                        responseVO.setJudgeTimeoutOrderList(Collections.emptyList());
                        responseVO.setSeriousTimeoutOrder(ZERO);
                        responseVO.setSeriousTimeoutOrderList(Collections.emptyList());
                        responseVO.setDeliveredRateIn15MinDesc(convert2PercentDec(null));
                        responseVO.setDeliveredRateIn25MinDesc(convert2PercentDec(null));
                        responseVO.setEtaTimeoutRateDesc(convert2PercentDec(null));
                        responseVO.setEtaSeriouslyTimeoutRateDesc(convert2PercentDec(null));
                    }
                    if (dbRiderData != null) {
                        responseVO.setDelivering(dbRiderData.getDelivering());
                        responseVO.setDeliveringJudgeTimeoutOrder(dbRiderData.getDeliveringJudgeTimeout());
                        responseVO.setPicking(dbRiderData.getPicking());
                        responseVO.setPickingJudgeTimeout(dbRiderData.getPickingJudgeTimeout());
                        responseVO.setPickingEtaOvertimeOrderV2(
                                Optional.ofNullable(dbRiderData.getPickingJudgeTimeoutV2()).map(Long::valueOf).orElse(0L));
                        responseVO.setDeliveringEtaOvertimeOrderV2(
                                Optional.ofNullable(dbRiderData.getDeliveringJudgeTimeoutV2()).map(Long::valueOf).orElse(0L));
                    }
                    if (employeeAttendanceInfoDTO != null) {
                        responseVO.setOnboardDays(employeeAttendanceInfoDTO.getOnboardDays().intValue());
                        responseVO.setAttendanceDaysOfCurMonth(employeeAttendanceInfoDTO.getAttendanceDays());
                        responseVO.setEmployeeStatus(employeeAttendanceInfoDTO.getEmployeeStatus());
                        responseVO.setEmployeeStatusDesc(convertEmployeeStatusDesc(employeeAttendanceInfoDTO.getEmployeeStatus()));
                        responseVO.setPositionNameList(employeeDepRelMap.getOrDefault(employeeAttendanceInfoDTO.getEmployeeId(), Collections.emptyList())
                                .stream()
                                .map(depRel -> positionDTOMap.get(depRel.getPositionId()))
                                .filter(Objects::nonNull)
                                .map(PositionDTO::getName)
                                .distinct()
                                .collect(Collectors.toList()));
                    }
                    if(storeOrgData != null) {
                        if (Objects.nonNull(storeOrgData.getNearPoiFirstDepartmentId())) {
                            responseVO.setUpOneLevelDepartmentName(trimDepartmentName(departmentNameMap.get(storeOrgData.getNearPoiFirstDepartmentId())));
                        }
                        if (Objects.nonNull(storeOrgData.getNearPoiSecondDepartmentId())) {
                            responseVO.setUpTwoLevelDepartmentName(trimDepartmentName(departmentNameMap.get(storeOrgData.getNearPoiSecondDepartmentId())));
                        }
                    }
                    return responseVO;
                }
        // https://fsd.sankuai.com/ones/requirement/detail/85520625  改为按已送达订单量排序，高的排在上面
        ).sorted((o1, o2) -> o2.getDeliveredOrder() - o1.getDeliveredOrder())
         .collect(Collectors.toList());
    }

    public static List<DepartmentFulfillDataDTO> buildDepartmentStatistics(Map<Long, SgDepartmentData> dwDataMap,
                                                                           Map<Long, TDeliveryStoreStatistics> dbData,
                                                                           Map<Long, Integer> onDutyEmployeeCount,
                                                                           Map<Long, String> departmentNameMap) {

        return dwDataMap.values().stream().map(
                (dwData) -> {

                    DepartmentFulfillDataDTO responseVO = new DepartmentFulfillDataDTO();

                    responseVO.setValidOrder(dwData.getFinOrdNum());
                    responseVO.setDeliveredOrder(dwData.getArrivedOrdNum());
                    if (StringUtils.isNotBlank(dwData.getNinePerformanceDuration())) {
                        BigDecimal ninePerformanceDurationBigDecimal = new BigDecimal(dwData.getNinePerformanceDuration());
                        responseVO.setNinetiethFulfillDuration(ninePerformanceDurationBigDecimal.setScale(2, RoundingMode.HALF_UP).toString());
                    }

                    if (StringUtils.isNotBlank(dwData.getPerformanceDuration())) {
                        BigDecimal performanceDurationBigDecimal = new BigDecimal(dwData.getPerformanceDuration());
                        responseVO.setAvgFulfillDuration(performanceDurationBigDecimal.setScale(2, RoundingMode.HALF_UP).toString());
                    }

                    responseVO.setJudgeTimeoutOrder(dwData.getCheckOvertimeOrdNum());
                    responseVO.setSeriousTimeoutOrder(dwData.getBadOvertimeOrdNum());;

                    responseVO.setWorkedEmployeeNum(dwData.getAttendanceNum());

                    responseVO.setDeliveredRateIn15Min(
                            calPercent(String.valueOf(dwData.getFifteenMinArrOrdNum()),
                                    String.valueOf(dwData.getArrivedOrdNoPreNum())));
                    responseVO.setDeliveredRateIn25Min(
                            calPercent(String.valueOf(dwData.getTwentyFiveMinArrOrdNum()),
                                    String.valueOf(dwData.getArrivedOrdNoPreNum())));

                    responseVO.setDeliveredRateIn15MinDesc(convert2PercentDec(calPercent(String.valueOf(dwData.getFifteenMinArrOrdNum()),
                            String.valueOf(dwData.getArrivedOrdNoPreNum()))));
                    responseVO.setDeliveredRateIn25MinDesc(convert2PercentDec(calPercent(String.valueOf(dwData.getTwentyFiveMinArrOrdNum()),
                            String.valueOf(dwData.getArrivedOrdNoPreNum()))));

                    responseVO.setDepartmentName(trimDepartmentName(departmentNameMap.get(dwData.getGroupColumn())));


                    responseVO.setEtaTimeoutRate(calPercent(String.valueOf(dwData.getEtaOvertimeOrdNum()),
                            String.valueOf(dwData.getArrivedOrdNum())));
                    responseVO.setEtaTimeoutRateDesc(convert2PercentDec(calPercent(String.valueOf(dwData.getEtaOvertimeOrdNum()),
                            String.valueOf(dwData.getArrivedOrdNum()))));
                    responseVO.setEtaSeriouslyTimeoutRate(calPercent(String.valueOf(dwData.getBadOvertimeOrdNum()),
                            String.valueOf(dwData.getArrivedOrdNum())));
                    responseVO.setEtaSeriouslyTimeoutRateDesc(convert2PercentDec(calPercent(String.valueOf(dwData.getBadOvertimeOrdNum()),
                            String.valueOf(dwData.getArrivedOrdNum()))));

                    responseVO.setStoreCnt(dwData.getPoiCnt());

                    responseVO.setEtaOvertimeOrdNumV2(dwData.getEtaOvertimeOrdNumV2());
                    responseVO.setEtaBadOvertimeOrdNumV2(dwData.getEtaBadOvertimeOrdNumV2());
                    responseVO.setEtaOntimeRatioV2(calPercent(
                            BigDecimal.valueOf(dwData.getArrivedOrdNum()).subtract(BigDecimal.valueOf(dwData.getEtaOvertimeOrdNumV2())),
                            BigDecimal.valueOf(dwData.getArrivedOrdNum())));
                    responseVO.setEtaOntimeRatioV2Desc(convert2PercentDec(responseVO.getEtaOntimeRatioV2()));
                    responseVO.setEtaBadOvertimeRatioV2(calPercent(String.valueOf(dwData.getEtaBadOvertimeOrdNumV2()),
                            String.valueOf(dwData.getArrivedOrdNum())));
                    responseVO.setEtaBadOvertimeRatioV2Desc(convert2PercentDec(responseVO.getEtaBadOvertimeRatioV2()));

                    if (StringUtils.isNotBlank(dwData.getPoiIds())) {
                        List<Long> belongStoreIds = Arrays.stream(StringUtils.split(dwData.getPoiIds(), ","))
                                .map(Long::valueOf).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(belongStoreIds)) {
                            List<TDeliveryStoreStatistics> dbStatisticDetails = belongStoreIds.stream()
                                    .map(dbData::get)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList());
                            responseVO.setWaitToAcceptOrder(dbStatisticDetails.stream().map(TDeliveryStoreStatistics::getWaitToAccept).reduce(Integer::sum).orElse(0));
                            responseVO.setWaitToAcceptJudgeTimeoutOrder(dbStatisticDetails.stream().map(TDeliveryStoreStatistics::getWaitToAcceptJudgeTimeout).reduce(Integer::sum).orElse(0));
                            responseVO.setPickingOrder(dbStatisticDetails.stream().map(TDeliveryStoreStatistics::getPicking).reduce(Integer::sum).orElse(0));
                            responseVO.setPickingJudgeTimeoutOder(dbStatisticDetails.stream().map(TDeliveryStoreStatistics::getPickingJudgeTimeout).reduce(Integer::sum).orElse(0));
                            // 切换
                            responseVO.setPickingEtaOvertimeOrderV2(dbStatisticDetails.stream().map(TDeliveryStoreStatistics::getPickingJudgeTimeout).mapToLong(Integer::longValue).reduce(Long::sum).orElse(0));
                            responseVO.setDeliveringOrder(dbStatisticDetails.stream().map(TDeliveryStoreStatistics::getDelivering).reduce(Integer::sum).orElse(0));
                            responseVO.setDeliveringJudgeTimeoutOrder(dbStatisticDetails.stream().map(TDeliveryStoreStatistics::getDeliveringJudgeTimeout).reduce(Integer::sum).orElse(0));
                            responseVO.setDeliveringEtaOvertimeOrderV2(dbStatisticDetails.stream().map(TDeliveryStoreStatistics::getDeliveringJudgeTimeout).mapToLong(Integer::longValue).reduce(Long::sum).orElse(0));
                            responseVO.setFulfillRiderNum(dbStatisticDetails.stream().map(TDeliveryStoreStatistics::getFulfillingRider).reduce(Integer::sum).orElse(0));
                            responseVO.setWaitToAcceptEtaTimeoutOrder(dbStatisticDetails.stream().map(TDeliveryStoreStatistics::getWaitToAcceptJudgeTimeoutV2).mapToLong(Integer::longValue).reduce(Long::sum).orElse(0));

                            Integer onDutyEmployeeCnt = belongStoreIds.stream().map(onDutyEmployeeCount::get)
                                    .filter(Objects::nonNull)
                                    .reduce(Integer::sum)
                                    .orElse(0);

                            responseVO.setIdleRiderNum(onDutyEmployeeCnt - responseVO.getFulfillRiderNum());


                            responseVO.setAvgRiderLoad(
                                    divide(dbStatisticDetails.stream().map(TDeliveryStoreStatistics::getFulfilling).reduce(Integer::sum).orElse(0),
                                            dbStatisticDetails.stream().map(TDeliveryStoreStatistics::getFulfillingRider).reduce(Integer::sum).orElse(0), 2));
                        }

                    }


                    return responseVO;
                }


        ).collect(Collectors.toList());

    }


    public static SegmentationFulfillDataResponse buildFulfillSegmentationDataResp(DWSResponse dwsResponse,
                                                                                   SegmentationFulfillDataQueryRequest request) {
        SegmentationFulfillDataResponse response = new SegmentationFulfillDataResponse();

        //返回结果Map化 key:时间 value:数据
        Map<String, Map<String, String>> timeDataMap = dwsResponse.getData().getDataList().stream()
                .collect(Collectors.toMap(dataMap -> dataMap.get("time"), dataMap -> dataMap, (k1, k2) -> k1));

        //将数据填到数据轴上 无数据则补零
        List<SegmentDataPointDTO> todayDataPoints = timeAxisList.stream()
                .map(item -> fillRealTimeData(item, timeDataMap, request))
                .map(FulfillDataConverter::unifyZeroValue)
                .collect(Collectors.toList());

        List<SegmentDataPointDTO> refDataPoints = timeAxisList.stream()
                .map(item -> fillRefTimeData(item, timeDataMap, request))
                .map(FulfillDataConverter::unifyZeroValue)
                .collect(Collectors.toList());

        //实时数据只展示到当前时刻（超过当前时刻的数据都为0，容易产生歧义），丢弃晚于当前时刻的无效数据点
        todayDataPoints = concatRealTimeSegmentData(todayDataPoints);

        response.setIndicatorType(request.getIndicatorType());
        response.setTodayDataPoints(todayDataPoints);
        response.setRefDataPoints(refDataPoints);

        return response;

    }


    public static List<AbnormalOrderDTO> buildAbnormalStatistics(List<SgAbnormalData> abnormalDataList,
                                                                 Map<Long, PoiInfoDto> storeDetails,
                                                                 Map<Long, String> departmentNameMap,
                                                                 Map<Long, EmployeeAttendanceInfoDTO> riderAttendanceInfoDTOMap,
                                                                 Map<Long, List<EmpDepRelDTO>> employeeDepRelMap,
                                                                 Map<Long, PositionDTO> positionDTOMap,
                                                                 Map<Long, String> accountNameMap) {
        return abnormalDataList.stream().map(sgAbnormalData -> {
            EmployeeAttendanceInfoDTO employeeAttendanceInfoDTO = riderAttendanceInfoDTOMap.get(sgAbnormalData.getRiderAccountId());
            AbnormalOrderDTO abnormalOrderDTO = new AbnormalOrderDTO();
            abnormalOrderDTO.setRiderAccountName(accountNameMap.get(sgAbnormalData.getRiderAccountId()));
            abnormalOrderDTO.setPoiName(Optional.ofNullable(storeDetails.get(sgAbnormalData.getPoiId())).map(PoiInfoDto::getPoiName).orElse(null));
            abnormalOrderDTO.setRiderName(sgAbnormalData.getRiderName());
            abnormalOrderDTO.setViewOrderId(sgAbnormalData.getOrderIdView());
            abnormalOrderDTO.setIsBooking(Objects.equals(sgAbnormalData.getIsPrevious(), 1L));
            abnormalOrderDTO.setOrderTypeDesc(Objects.equals(sgAbnormalData.getIsPrevious(), 1L) ? "预订单":"实时单");
            abnormalOrderDTO.setPayTime(sgAbnormalData.getPayTime());

            abnormalOrderDTO.setUpTwoLevelDepartmentName(trimDepartmentName(departmentNameMap.get(sgAbnormalData.getNearPoiSecondDepartmentId())));
            abnormalOrderDTO.setUpOneLevelDepartmentName(trimDepartmentName(departmentNameMap.get(sgAbnormalData.getNearPoiFirstDepartmentId())));


            if (employeeAttendanceInfoDTO != null) {
                abnormalOrderDTO.setOnboardDays(employeeAttendanceInfoDTO.getOnboardDays().intValue());
                abnormalOrderDTO.setAttendanceDaysOfCurMonth(employeeAttendanceInfoDTO.getAttendanceDays());
                abnormalOrderDTO.setEmployeeStatus(employeeAttendanceInfoDTO.getEmployeeStatus());
                abnormalOrderDTO.setEmployeeStatusDesc(convertEmployeeStatusDesc(employeeAttendanceInfoDTO.getEmployeeStatus()));
                abnormalOrderDTO.setPositionNameList(employeeDepRelMap.getOrDefault(employeeAttendanceInfoDTO.getEmployeeId(), Collections.emptyList())
                        .stream()
                        .map(depRel -> positionDTOMap.get(depRel.getPositionId()))
                        .filter(Objects::nonNull)
                        .map(PositionDTO::getName)
                        .distinct()
                        .collect(Collectors.toList()));

            }

            abnormalOrderDTO.setIsSeriouslyTimeout(Objects.equals(sgAbnormalData.getIsBadOvertimeOrd(), 1L));
            abnormalOrderDTO.setIsSeriouslyTimeoutDesc(convert2BooleanDesc(Objects.equals(sgAbnormalData.getIsBadOvertimeOrd(), 1L)));
            abnormalOrderDTO.setIsJudgeTimeout(Objects.equals(sgAbnormalData.getIsCheckOvertimeOrd(), 1L));
            abnormalOrderDTO.setIsJudgeTimeoutDesc(convert2BooleanDesc(Objects.equals(sgAbnormalData.getIsCheckOvertimeOrd(), 1L)));
            abnormalOrderDTO.setIsEtaOvertimeDesc(convert2BooleanDesc(Objects.equals(sgAbnormalData.getIsEtaOvertimeOrdV2(), 1L)));
            abnormalOrderDTO.setIsEtaBadOvertimeDesc(convert2BooleanDesc(Objects.equals(sgAbnormalData.getIsEtaBadOvertimeOrdV2(), 1L)));

            return abnormalOrderDTO;

        }).collect(Collectors.toList());

    }

    /**
     * 获取查询数仓数据时传的开始日期和结束日期 以06:00为分隔
     * 若当前时间早于06:00 查询时间为昨天～今天
     * 若当前时间晚于06:00 查询时间为今天～明天
     *
     * @return
     */
    public static Pair<LocalDate, LocalDate> getStartAndEndDate() {
        LocalDateTime now = LocalDateTime.now(ZoneId.of("+8"));
        LocalDate startDate;
        LocalDate endDate;
        if (now.toLocalTime().isBefore(SIX_AM)) {
            startDate = now.minusDays(1).toLocalDate();
            endDate = now.toLocalDate();
        } else {
            startDate = now.toLocalDate();
            endDate = now.plusDays(1).toLocalDate();
        }
        return Pair.of(startDate, endDate);
    }

    /**
     * 根据compareType获取查询数仓数据时传的数据对比日期
     *
     * @param startAndEndDate 开始日期和结束日期
     * @param compareType     比较类型
     * @return 数据对比日期
     * @see CompareTypeEnum
     */
    public static Pair<LocalDate, LocalDate> getRefStartAndEndDate(Pair<LocalDate, LocalDate> startAndEndDate, @NotNull Integer compareType) {
        LocalDate startDate = startAndEndDate.getLeft();
        LocalDate endDate = startAndEndDate.getRight();


        CompareTypeEnum compareTypeEnum = CompareTypeEnum.enumOf(compareType);

        if (Objects.isNull(compareTypeEnum)) {
            return startAndEndDate;
        }

        switch (compareTypeEnum) {
            case COMPARE_WITH_YESTERDAY:
                //日同比
                return Pair.of(startDate.minusDays(1), endDate.minusDays(1));
            case COMPARE_WITH_LAST_WEEK_DAY:
                //周同比
                return Pair.of(startDate.minusWeeks(1), endDate.minusWeeks(1));
            case COMPARE_WITH_LAST_MONTH_DAY:
                //月同比
                return Pair.of(startDate.minusMonths(1), endDate.minusMonths(1));
            default:
                return startAndEndDate;

        }
    }

    //计算百分率 单位：% 结果保留2位小数
    private static String calPercent(String partitionNumStr, String totalNumStr) {
        if (StringUtils.isEmpty(partitionNumStr) || StringUtils.isEmpty(totalNumStr)) {
            return null;
        }

        BigDecimal partitionNumBigDecimal = new BigDecimal(partitionNumStr);
        BigDecimal totalNumBigDecimal = new BigDecimal(totalNumStr);

        return calPercent(partitionNumBigDecimal, totalNumBigDecimal);
    }

    private static String calPercent(BigDecimal partitionNumBigDecimal, BigDecimal totalNumBigDecimal) {
        if (partitionNumBigDecimal == null || totalNumBigDecimal == null) {
            return null;
        }
        if (totalNumBigDecimal.equals(BigDecimal.ZERO) && partitionNumBigDecimal.equals(BigDecimal.ZERO)) {
            return BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toString();
        }

        if (totalNumBigDecimal.equals(BigDecimal.ZERO) && !partitionNumBigDecimal.equals(BigDecimal.ZERO)) {
            return null;
        }

        BigDecimal result = partitionNumBigDecimal.multiply(new BigDecimal(100))
                .divide(totalNumBigDecimal, 2, RoundingMode.HALF_UP);

        return result.toString();
    }

    private static String divide(Integer molecular, Integer denominator, int bits) {
        if (molecular == null || denominator == null || denominator == 0) {
            return null;
        }
        BigDecimal molecularNumBigDecimal = new BigDecimal(molecular);
        BigDecimal denominatorBigDecimal = new BigDecimal(denominator);
        return molecularNumBigDecimal.divide(denominatorBigDecimal, bits, RoundingMode.HALF_UP).toString();
    }

    private static List<ValidOrderCountChannelInfo> transform2ValidOrderCountChannelInfoList(String wmChannelOrdNumStr, String dhChannelOrdNumStr) {

        List<ValidOrderCountChannelInfo> result = new ArrayList<>();
        if (StringUtils.isNotBlank(wmChannelOrdNumStr)) {
            result.add(new ValidOrderCountChannelInfo(ChannelTypeEnum.MEITUAN.getChannelId(), Integer.valueOf(wmChannelOrdNumStr)));
        }

        if (StringUtils.isNotBlank(dhChannelOrdNumStr)) {
            result.add(new ValidOrderCountChannelInfo(ChannelTypeEnum.MT_DRUNK_HORSE.getChannelId(), Integer.valueOf(dhChannelOrdNumStr)));
        }

        return result;
    }

    private static SegmentDataPointDTO fillRealTimeData(String timePoint, Map<String, Map<String, String>> responseTimeDataMap, SegmentationFulfillDataQueryRequest request) {
        if (responseTimeDataMap.containsKey(timePoint)) {
            return transform2RealTimeSegmentDataPointVO(responseTimeDataMap.get(timePoint), request);
        }

        return new SegmentDataPointDTO(timePoint, SEGMENT_DATA_DEFAULT_VALUE);
    }

    private static SegmentDataPointDTO fillRefTimeData(String timePoint, Map<String, Map<String, String>> responseTimeDataMap, SegmentationFulfillDataQueryRequest request) {
        if (responseTimeDataMap.containsKey(timePoint)) {
            return transform2RefSegmentDataPointVO(responseTimeDataMap.get(timePoint), request);
        }

        return new SegmentDataPointDTO(timePoint, SEGMENT_DATA_DEFAULT_VALUE);
    }

    private static SegmentDataPointDTO transform2RealTimeSegmentDataPointVO(Map<String, String> dataMap, SegmentationFulfillDataQueryRequest request) {
        IndicatorTypeEnum indicatorTypeEnum = Objects.requireNonNull(enumOf(request.getIndicatorType()));
        SegmentDataPointDTO dataPointVO = new SegmentDataPointDTO();
        dataPointVO.setMoment(dataMap.get("time"));

        if (Objects.equals(indicatorTypeEnum, DELIVERY_RATE_IN_15_MIN) || Objects.equals(indicatorTypeEnum, DELIVERY_RATE_IN_25_MIN)) {
            String[] fields = map2Fields(indicatorTypeEnum).getLeft().split(SPLIT_STRING);
            dataPointVO.setValue(calPercent(dataMap.get(fields[0]), dataMap.get(fields[1])));
            return dataPointVO;
        }

        if (Objects.equals(indicatorTypeEnum, NINETIETH_FULFILL_DURATION) || Objects.equals(indicatorTypeEnum, AVG_FULFILL_DURATION)) {
            dataPointVO.setValue(processPrecision(dataMap.get(map2Fields(indicatorTypeEnum).getLeft())));
            return dataPointVO;
        }

        dataPointVO.setValue(dataMap.get(map2Fields(indicatorTypeEnum).getLeft()));

        return dataPointVO;
    }

    private static SegmentDataPointDTO transform2RefSegmentDataPointVO(Map<String, String> dataMap, SegmentationFulfillDataQueryRequest request) {
        IndicatorTypeEnum indicatorTypeEnum = Objects.requireNonNull(enumOf(request.getIndicatorType()));
        SegmentDataPointDTO dataPointVO = new SegmentDataPointDTO();
        dataPointVO.setMoment(dataMap.get("time"));

        if (Objects.equals(indicatorTypeEnum, DELIVERY_RATE_IN_15_MIN) || Objects.equals(indicatorTypeEnum, DELIVERY_RATE_IN_25_MIN)) {
            String[] fields = map2Fields(indicatorTypeEnum).getRight().split(SPLIT_STRING);
            dataPointVO.setValue(calPercent(dataMap.get(fields[0]), dataMap.get(fields[1])));
            return dataPointVO;
        }

        if (Objects.equals(indicatorTypeEnum, NINETIETH_FULFILL_DURATION) || Objects.equals(indicatorTypeEnum, AVG_FULFILL_DURATION)) {
            dataPointVO.setValue(processPrecision(dataMap.get(map2Fields(indicatorTypeEnum).getRight())));
            return dataPointVO;
        }

        dataPointVO.setValue(dataMap.get(map2Fields(indicatorTypeEnum).getRight()));

        return dataPointVO;
    }

    /**
     * 实时分段数据需要根据当前时间进行截断，因为当前时间之后的数据点还没有被采集到，如果直接给前端范围null会有歧义
     *
     * @param todayDataPoints 实时分段数据 时间范围为（02:00,02:00]
     * @return 截断后的分段数据 例如现在15:23 截断后的时间范围为(02:00,15:20]
     */
    private static List<SegmentDataPointDTO> concatRealTimeSegmentData(List<SegmentDataPointDTO> todayDataPoints) {
        LocalTime nowLocalTime = LocalTime.now(ZoneId.of("+8"));
        int minute = nowLocalTime.getMinute();
        int hour = nowLocalTime.getHour();

        //计算截断的时间点
        int concatMin;
        if (minute % TIME_AXIS_GRID == 0) {
            concatMin = minute;
        } else {
            concatMin = (minute / TIME_AXIS_GRID) * TIME_AXIS_GRID;
        }

        String concatTimePoint = LocalTime.of(hour, concatMin).format(DateTimeFormatter.ofPattern("HH:mm"));

        //计算截断时间点的位置
        int concatIndex = todayDataPoints.size();
        for (int index = 0; index < todayDataPoints.size(); index++) {
            SegmentDataPointDTO points = todayDataPoints.get(index);
            if (StringUtils.equals(points.getMoment(), concatTimePoint)) {
                concatIndex = index + 1;
                break;
            }
        }

        return todayDataPoints.subList(0, concatIndex);
    }

    /**
     * 将指标映射成数仓侧规定的指标名Pair left->实时指标名 right->对比指标名
     *
     * @param indicatorTypeEnum
     * @return
     */
    public static Pair<String, String> map2Fields(IndicatorTypeEnum indicatorTypeEnum) {
        switch (indicatorTypeEnum) {
            case VALID_ORDER_COUNT:
                return Pair.of("finOrdNum", "refFinOrdNum");
            case DELIVERER_ORDER_COUNT:
                return Pair.of("arrivedOrdNum", "refArrivedOrdNum");
            case SERIOUS_TIMEOUT_ORDER_COUNT:
                return Pair.of("badOvertimeOrdNum", "refBadOvertimeOrdNum");
            case JUDGE_TIMEOUT_ORDER_COUNT:
                return Pair.of("checkOvertimeOrdNum", "refCheckOvertimeOrdNum");
            case NINETIETH_FULFILL_DURATION:
                return Pair.of("ninePerformanceDuration", "refNinePerformanceDuration");
            case AVG_FULFILL_DURATION:
                return Pair.of("performanceDuration", "refPerformanceDuration");
            case WORDED_EMPLOYEE_COUNT:
                return Pair.of("attendanceNum", "refAttendanceNum");
            case DELIVERY_RATE_IN_15_MIN:
                return Pair.of(Joiner.on(",").join("fifteenMinArrOrdNum", "arrivedOrdNoPreNum"),
                        Joiner.on(",").join("refFifteenMinArrOrdNum", "refArrivedOrdNoPreNum"));
            case DELIVERY_RATE_IN_25_MIN:
                return Pair.of(Joiner.on(",").join("twentyFiveMinArrOrdNum", "arrivedOrdNoPreNum"),
                        Joiner.on(",").join("refTwentyFiveMinArrOrdNum", "refArrivedOrdNoPreNum"));
            case ETA_TIMEOUT_ORDER_COUNT:
                return Pair.of("etaOvertimeOrdNumV2", "refEtaOvertimeOrdNumV2");
            case ETA_BAD_TIMEOUT_ORDER_COUNT:
                return Pair.of("etaBadOvertimeOrdNumV2", "refEtaBadOvertimeOrdNumV2");
            default:
                return Pair.of(EMPTY_STRING, EMPTY_STRING);

        }
    }


    /**
     * 生成时间轴
     * 范围(今日02:00,次日02:00] ：5分钟
     */
    private static void generateTimeAxis() {
        //先生成(02:00,00:00]区间的点
        LocalTime localTime = SIX_AM;
        while (localTime.isAfter(LocalTime.MIN)) {
            localTime = localTime.plusMinutes(TIME_AXIS_GRID);
            timeAxisList.add(localTime.format(DateTimeFormatter.ofPattern("HH:mm")));

        }

        //再生成(00:00,02:00]区间的点
        localTime = LocalTime.MIN;
        while (localTime.isBefore(SIX_AM)) {
            localTime = localTime.plusMinutes(TIME_AXIS_GRID);
            timeAxisList.add(localTime.format(DateTimeFormatter.ofPattern("HH:mm")));
        }
    }

    /**
     * 处理浮点数字符串的精度 结果保留两位小数
     * 例如23.6476237467823  ==> 23.65
     */
    private static String processPrecision(String doubleNumStr) {
        if (StringUtils.isBlank(doubleNumStr)) {
            return null;
        }

        BigDecimal decimal = new BigDecimal(doubleNumStr);

        return decimal.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
    }

    private static String divide(String dividend,String divisor) {
        if (Objects.isNull(divisor) || Double.valueOf(0L).equals(Double.valueOf(divisor))) {
            return null;
        }
        if (Objects.isNull(dividend)) {
            return ZERO.toString();
        }
        return BigDecimal.valueOf(Double.parseDouble(dividend) / Double.parseDouble(divisor))
                .setScale(2, RoundingMode.HALF_UP).toString();
    }

    public static Map<Long, SgDepartmentData> convertDepartmentData(List<Map<String, String>> dwDataList) {
        return dwDataList.stream().map(
                (data) -> {
                    return JSON.parseObject(JSON.toJSONString(data), SgDepartmentData.class);
                }).collect(Collectors.toMap(SgDepartmentData::getGroupColumn, Function.identity()));
    }


    public static List<SgAbnormalData> convertAbnormalData(List<Map<String, String>> dwDataList) {
        return dwDataList.stream().map(dwData -> {
            return JSON.parseObject(JSON.toJSONString(dwData), SgAbnormalData.class);
        }).collect(Collectors.toList());
    }

    public static Map<Long, SgRiderData> convertRiderDwData(List<Map<String, String>> dwDataList) {
        //防御式编程：riderAccountId原本不可能为null
        return dwDataList.stream().map(
                (data) -> {
                    SgRiderData sgRiderData = new SgRiderData();
                    sgRiderData.setRiderAccountId(Long.parseLong(data.get("riderAccountId")));
                    sgRiderData.setRiderName(data.get("riderName"));
                    sgRiderData.setArrivedOrdNum(Optional.ofNullable(data.get("arrivedOrdNum")).map(Integer::parseInt).orElse(ZERO));
                    sgRiderData.setNinePerformanceDuration(processPrecision(data.get("ninePerformanceDuration")));
                    sgRiderData.setPerformanceDuration(processPrecision(data.get("performanceDuration")));
                    sgRiderData.setCheckOvertimeOrdNum(Optional.ofNullable(data.get("checkOvertimeOrdNum")).map(Integer::parseInt).orElse(ZERO));
                    sgRiderData.setBadOvertimeOrdNum(Optional.ofNullable(data.get("badOvertimeOrdNum")).map(Integer::parseInt).orElse(ZERO));
                    sgRiderData.setTwentyFiveMinArrOrdNum(Optional.ofNullable(data.get("twentyFiveMinArrOrdNum")).map(Integer::parseInt).orElse(ZERO));
                    sgRiderData.setArrivedOrdNoPreNum(Optional.ofNullable(data.get("arrivedOrdNoPreNum")).map(Integer::parseInt).orElse(ZERO));
                    sgRiderData.setFifteenMinArrOrdNum(Optional.ofNullable(data.get("fifteenMinArrOrdNum")).map(Integer::parseInt).orElse(ZERO));
                    sgRiderData.setCheckOrds(data.get("checkOrds"));
                    sgRiderData.setBadOrds(data.get("badOrds"));
                    sgRiderData.setEtaOvertimeOrdNumV2(Optional.ofNullable(data.get("etaOvertimeOrdNumV2")).map(Long::parseLong).orElse(0L));
                    sgRiderData.setEtaBadOvertimeOrdNumV2(Optional.ofNullable(data.get("etaBadOvertimeOrdNumV2")).map(Long::parseLong).orElse(0L));
                    sgRiderData.setEtaOvertimeOrdNum(Optional.ofNullable(data.get("etaOvertimeOrdNum")).map(Integer::parseInt).orElse(ZERO));
                    return sgRiderData;
                }).collect(Collectors.toMap(SgRiderData::getRiderAccountId, Function.identity()));
    }

    public static List<StoreOrgData> convertStoreOrgInfo(List<Map<String, String>> dwDataList) {
        return dwDataList.stream().map(dataMap -> {
            return JSON.parseObject(JSON.toJSONString(dataMap), StoreOrgData.class);
        }).collect(Collectors.toList());
    }

    public static Map<Long, TDeliveryRiderStatistics> convertRiderDbData(List<TDeliveryRiderStatistics> dbDataList) {
        return dbDataList.stream().collect(Collectors.toMap(TDeliveryRiderStatistics::getRiderAccountId, Function.identity()));
    }


    public static Map<Long, SgStoreData> convertStoreDwData(List<Map<String, String>> dwDataList) {
        return dwDataList.stream().filter(data -> data.get("poiId") != null).map(
                (data) -> {
                    SgStoreData sgStoreData = new SgStoreData();
                    sgStoreData.setPoiId(Long.parseLong(data.get("poiId")));
                    sgStoreData.setPoiName(data.get("poiName"));
                    sgStoreData.setFinOrdNum(Optional.ofNullable(data.get("finOrdNum")).map(Integer::parseInt).orElse(ZERO));
                    sgStoreData.setArrivedOrdNum(Optional.ofNullable(data.get("arrivedOrdNum")).map(Integer::parseInt).orElse(ZERO));
                    sgStoreData.setBadOvertimeOrdNum(Optional.ofNullable(data.get("badOvertimeOrdNum")).map(Integer::parseInt).orElse(ZERO));
                    sgStoreData.setCheckOvertimeOrdNum(Optional.ofNullable(data.get("checkOvertimeOrdNum")).map(Integer::parseInt).orElse(ZERO));
                    sgStoreData.setAttendanceNum(Optional.ofNullable(data.get("attendanceNum")).map(Integer::parseInt).orElse(ZERO));
                    sgStoreData.setNinePerformanceDuration(processPrecision(data.get("ninePerformanceDuration")));
                    sgStoreData.setPerformanceDuration(processPrecision(data.get("performanceDuration")));
                    sgStoreData.setArrivedOrdNoPreNum(Optional.ofNullable(data.get("arrivedOrdNoPreNum")).map(Integer::parseInt).orElse(ZERO));
                    sgStoreData.setFifteenMinArrOrdNum(Optional.ofNullable(data.get("fifteenMinArrOrdNum")).map(Integer::parseInt).orElse(ZERO));
                    sgStoreData.setTwentyFiveMinArrOrdNum(Optional.ofNullable(data.get("twentyFiveMinArrOrdNum")).map(Integer::parseInt).orElse(ZERO));
                    sgStoreData.setBadOrds(data.get("badOrds"));
                    sgStoreData.setCheckOrds(data.get("checkOrds"));
                    if (StringUtils.isNotBlank(data.get("nearPoiFirstDepartmentId"))) {
                        sgStoreData.setNearPoiFirstDepartmentId(Long.valueOf(data.get("nearPoiFirstDepartmentId")));
                    }

                    if (StringUtils.isNotBlank(data.get("nearPoiSecondDepartmentId"))) {
                        sgStoreData.setNearPoiSecondDepartmentId(Long.valueOf(data.get("nearPoiSecondDepartmentId")));
                    }

                    sgStoreData.setEtaOvertimeOrdNum(Optional.ofNullable(data.get("etaOvertimeOrdNum")).map(Integer::parseInt).orElse(ZERO));
                    sgStoreData.setEtaOvertimeOrdNumV2(Optional.ofNullable(data.get("etaOvertimeOrdNumV2")).map(Long::parseLong).orElse(0L));
                    sgStoreData.setEtaBadOvertimeOrdNumV2(Optional.ofNullable(data.get("etaBadOvertimeOrdNumV2")).map(Long::parseLong).orElse(0L));
                    return sgStoreData;
                }).collect(Collectors.toMap(SgStoreData::getPoiId, Function.identity()));
    }

    public static Map<Long, TDeliveryStoreStatistics> convertStoreDbData(List<TDeliveryStoreStatistics> dbDataList) {
        return dbDataList.stream().collect(Collectors.toMap(TDeliveryStoreStatistics::getStoreId, x -> x));
    }


    private static List<DeliveryOrderChannelInfo> buildDeliveryOrderChannelInfos(String orderAndChannels) {
        if (StringUtils.isBlank(orderAndChannels)) {
            return Collections.emptyList();
        }
        return Arrays.stream(orderAndChannels.split(SPLIT_STRING)).map(
                (orderAndChannelString) -> {
                    String[] orderAndChannel = orderAndChannelString.split(":");
                    return new DeliveryOrderChannelInfo(Integer.parseInt(orderAndChannel[0]),
                            Integer.parseInt(orderAndChannel[1]));
                }
        ).collect(Collectors.toList());
    }

    //统一0值的展示 0.000 或 0.0 都展位为0
    private static SegmentDataPointDTO unifyZeroValue(SegmentDataPointDTO pointVO) {
        if (Objects.isNull(pointVO)) {
            return null;
        }

        if (new BigDecimal(pointVO.getValue()).compareTo(BigDecimal.ZERO) == 0) {
            pointVO.setValue(BigDecimal.ZERO.toString());
        }

        return pointVO;
    }

    private static String convertEmployeeStatusDesc(Integer employeeStatus) {
        if (Objects.equals(employeeStatus, 1)) {
            return "在职";
        }

        if (Objects.equals(employeeStatus, 2)) {
            return "离职";
        }

        return "--";
    }

    private static String convert2BooleanDesc(Boolean boolVal) {
        if (Objects.isNull(boolVal)) {
            return "--";
        }
        return boolVal ? "是" : "否";
    }

    private static String convert2PercentDec(String percentVal) {
        if (StringUtils.isBlank(percentVal)) {
            return "--";
        }

        return percentVal + "%";
    }

    private static String trimDepartmentName(String departmentName) {
        if (StringUtils.isNotBlank(departmentName) && departmentName.startsWith("加盟-")) {
            return departmentName.substring("加盟-".length());
        }

        return departmentName;
    }
}
