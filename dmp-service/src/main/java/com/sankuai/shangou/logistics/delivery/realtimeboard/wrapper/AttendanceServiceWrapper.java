package com.sankuai.shangou.logistics.delivery.realtimeboard.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.sankuai.drunkhorsemgmt.labor.thrift.AttendanceThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ScheduledEmployeeDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.request.QueryPoiOnDutyEmployeeCountRequest;
import com.sankuai.drunkhorsemgmt.labor.thrift.request.QueryPoiOnDutyEmployeeListRequest;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.QueryPoiOnDutyEmployeeCountResp;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.QueryPoiOnDutyEmployeeListResp;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.bizmng.labor.api.employee.TEmployeeInfoService;
import com.sankuai.shangou.bizmng.labor.api.employee.dto.EmployeeAttendanceInfoDTO;
import com.sankuai.shangou.bizmng.labor.api.employee.request.EmployeeAttendanceInfoReq;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.realtimeboard.utils.CommonThreadPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @since 2024/4/8 11:12
 **/
@Rhino
@Slf4j
public class AttendanceServiceWrapper {

    @Resource
    private AttendanceThriftService attendanceThriftService;

    @Resource
    private TEmployeeInfoService tEmployeeInfoService;

    @Degrade(rhinoKey = "AttendanceServiceWrapper.batchQueryPoiOnDutyEmployeeCount", fallBackMethod = "batchQueryPoiOnDutyEmployeeCountFallBack", timeoutInMilliseconds = 4000)
    public Map<Long,Integer> batchQueryPoiOnDutyEmployeeCount(Long tenantId, List<Long> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Collections.emptyMap();
        }

        int batchSize = 20;
        if (storeIds.size() <= batchSize) {
            return queryPoiOnDutyEmployeeCount(tenantId, storeIds);
        }

        List<List<Long>> storeIdListList = Lists.partition(storeIds, batchSize);
        List<Future<Map<Long, Integer>>> futures = new ArrayList<>();
        for (List<Long> storeIdSubList : storeIdListList) {
            futures.add(CommonThreadPool.commonThreadPool()
                    .submit(() -> queryPoiOnDutyEmployeeCount(tenantId, storeIdSubList)));
        }

        Map<Long, Integer> resultMap = new HashMap<>();
        for (Future<Map<Long, Integer>> future : futures) {
            try {
                resultMap.putAll(future.get());
            } catch (Exception e) {
                throw new BizException("批量查询考勤数据失败");
            }
        }

        return resultMap;
    }

    @Degrade(rhinoKey = "AttendanceServiceWrapper.batchQueryPoiOnDutyEmployeeList", fallBackMethod = "batchQueryPoiOnDutyEmployeeListFallBack", timeoutInMilliseconds = 4000)
    public Map<Long, List<ScheduledEmployeeDTO>> batchQueryPoiOnDutyEmployeeList(Long tenantId, List<Long> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Collections.emptyMap();
        }

        int batchSize = 20;
        if (storeIds.size() <= batchSize) {
            return queryPoiOnDutyEmployeeList(tenantId, storeIds);
        }

        List<List<Long>> storeIdListList = Lists.partition(storeIds, batchSize);
        List<Future<Map<Long, List<ScheduledEmployeeDTO>>>> futures = new ArrayList<>();
        for (List<Long> storeIdSubList : storeIdListList) {
            futures.add(CommonThreadPool.commonThreadPool()
                    .submit(() -> queryPoiOnDutyEmployeeList(tenantId, storeIdSubList)));
        }

        Map<Long, List<ScheduledEmployeeDTO>> resultMap = new HashMap<>();
        for (Future<Map<Long, List<ScheduledEmployeeDTO>>> future : futures) {
            try {
                resultMap.putAll(future.get());
            } catch (Exception e) {
                throw new BizException("批量查询考勤数据失败");
            }
        }

        return resultMap;
    }

    public Map<Long, List<ScheduledEmployeeDTO>> batchQueryPoiOnDutyEmployeeListFallBack(Long tenantId, List<Long> storeIds) {
        log.error("AttendanceServiceWrapper.batchQueryPoiOnDutyEmployeeListFallBack 发生降级");
        return Collections.emptyMap();
    }

    public Map<Long,Integer> batchQueryPoiOnDutyEmployeeCountFallBack(Long tenantId, List<Long> storeIds) {
        log.error("AttendanceServiceWrapper.batchQueryPoiOnDutyEmployeeCount 发生降级");
        return Collections.emptyMap();
    }

    @Degrade(rhinoKey = "AttendanceServiceWrapper.queryEmployeeAttendanceInfo", fallBackMethod = "queryEmployeeAttendanceInfoFallBack", timeoutInMilliseconds = 2000)
    public List<EmployeeAttendanceInfoDTO> queryEmployeeAttendanceInfo(List<Long> accountIds, Long tenantId, LocalDate startDate, LocalDate endDate) {
        EmployeeAttendanceInfoReq request = new EmployeeAttendanceInfoReq();
        request.setAccountIdList(accountIds);
        request.setTenantId(tenantId);
        request.setStartDateStr(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        request.setEndDateStr(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        log.info("start invoke tEmployeeInfoService.queryEmployeeAttendanceInfo, request: {}", request);
        TResult<List<EmployeeAttendanceInfoDTO>> result = tEmployeeInfoService.queryEmployeeAttendanceInfo(request);
        log.info("end invoke tEmployeeInfoService.queryEmployeeAttendanceInfo, request: {}, result: {}", request, result);
        if (!result.isSuccess()) {
            throw new BizException("查询员工考勤数据出错");
        }

        return result.getData();

    }


    public  List<EmployeeAttendanceInfoDTO> queryEmployeeAttendanceInfoFallBack(List<Long> accountIds, Long tenantId, LocalDate startDate, LocalDate endDate) {
        log.info("AttendanceServiceWrapper.queryEmployeeAttendanceInfo发生降级");
        return Collections.emptyList();
    }

    private Map<Long,Integer> queryPoiOnDutyEmployeeCount(Long tenantId, List<Long> storeIds) {
        QueryPoiOnDutyEmployeeCountRequest request = new QueryPoiOnDutyEmployeeCountRequest();
        request.setStoreIds(storeIds);
        request.setTenantId(tenantId);
        log.info("invoke attendanceThriftService.queryPoiOnDutyEmployeeCount start, req:{}", request);
        QueryPoiOnDutyEmployeeCountResp resp = attendanceThriftService.queryPoiOnDutyEmployeeCount(request);
        log.info("invoke attendanceThriftService.queryPoiOnDutyEmployeeCount end, req:{}, resp:{}", request, resp);
        if (resp == null || resp.getStatus() == null) {
            throw new BizException("查询门店在岗员工人数失败");
        }

        if (resp.getStatus().getCode() != com.sankuai.meituan.shangou.empower.rider.client.common.Status.SUCCESS.getCode()) {
            throw new BizException(StringUtils.defaultString(resp.getStatus().getMsg(), "查询门店在岗员工人数失败"));
        }

        return resp.getPoiOnDutyEmployeeCount();
    }

    private Map<Long, List<ScheduledEmployeeDTO>> queryPoiOnDutyEmployeeList(Long tenantId, List<Long> storeIds) {
        QueryPoiOnDutyEmployeeListRequest request = new QueryPoiOnDutyEmployeeListRequest();
        request.setStoreIds(storeIds);
        request.setTenantId(tenantId);
        log.info("invoke attendanceThriftService.queryPoiOnDutyEmployeeList start, req:{}", request);
        QueryPoiOnDutyEmployeeListResp resp = attendanceThriftService.queryPoiOnDutyEmployeeList(request);
        log.info("invoke attendanceThriftService.queryPoiOnDutyEmployeeList end, req:{}, resp:{}", request, resp);
        if (resp == null || resp.getStatus() == null) {
            throw new BizException("查询门店在岗员工人数失败");
        }

        if (resp.getStatus().getCode() != com.sankuai.meituan.shangou.empower.rider.client.common.Status.SUCCESS.getCode()) {
            throw new BizException(StringUtils.defaultString(resp.getStatus().getMsg(), "查询门店在岗员工人数失败"));
        }

        return resp.getPoiOnDutyEmployeeList();
    }
}
