package com.sankuai.shangou.logistics.delivery.alert.sevice;


import com.sankuai.shangou.logistics.delivery.alert.utils.MccUtils;
import com.sankuai.shangou.logistics.delivery.common.utils.TimeUtils;
import com.sankuai.shangou.logistics.delivery.deliveryorder.repository.es.DeliveryOrderEagleRepository;
import com.sankuai.shangou.logistics.delivery.push.DxCardService;
import com.sankuai.shangou.logistics.delivery.push.msg.card.LaunchFailCardData;
import com.sankuai.shangou.logistics.delivery.xm.enums.CardEnum;
import com.sankuai.shangou.logistics.delivery.xm.enums.CardTitleEnum;
import com.sankuai.shangou.logistics.delivery.xm.sevice.QueryUserIdentityClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description 配送告警回调处理
 * @date 2025-05-15
 */
@Slf4j
@Service
public class DeliveryAlertCallbackService {
    @Resource
    private DeliveryOrderEagleRepository deliveryOrderEagleRepository;
    @Resource
    private QueryUserIdentityClient queryUserIdentityClient;
    @Resource
    private DxCardService dxCardService;

    public void launchFailHandle() {
        LocalDateTime endTime = LocalDateTime.now().withSecond(0).withNano(0);
        LocalDateTime beginTime = endTime.minusHours(MccUtils.getLaunchFailBeforeHours());
        Pair<List<Long>, Long> pair = deliveryOrderEagleRepository.searchLaunchFailOrderIds(beginTime, endTime, 0, 20);
        if (pair.getRight() == 0) {
            log.info("没有查询到配送发单失败订单");
            return;
        }
        List<Long> empIds = queryUserIdentityClient.queryEmpIdByMisList(MccUtils.getExecuteMisList());
        LaunchFailCardData cardData = new LaunchFailCardData();
        cardData.setTemplateId(MccUtils.getDeliveryFailDxCardTemplateId());
        cardData.setGid(MccUtils.getGid());
        cardData.setPrivateDataEmpIds(empIds);
        cardData.setRequestId(UUID.randomUUID().toString());
        cardData.setCardType(CardEnum.DELIVERY_LAUNCH_FAILED.getType());
        cardData.setCardTitle(CardTitleEnum.LAUNCH_FAILED);
        cardData.setBeginTime(beginTime.format(TimeUtils.DATE_TIME_FORMATTER_DOT));
        cardData.setEndTime(endTime.format(TimeUtils.DATE_TIME_FORMATTER_DOT));
        cardData.setOrderIds(pair.getLeft().toString().trim());
        cardData.setOrderCount(String.valueOf(pair.getRight()));
        dxCardService.sendGroupExclusionCard(cardData);
    }

}
