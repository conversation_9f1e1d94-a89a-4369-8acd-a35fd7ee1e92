package com.sankuai.shangou.logistics.delivery.indicator.dto;

import com.sankuai.shangou.logistics.delivery.indicator.repository.dbo.IndicatorDoc;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023/6/28 16:20
 **/
@EqualsAndHashCode(of = {"code", "logisticsUnitId", "bizTime", "property"})
@Data
@Builder
public class IndicatorDTO {
    /**
     * 指标唯一编码
     */
    private String code;
    /**
     * 物流单元ID。
     */
    private Long logisticsUnitId;
    /**
     * 反映业务情况的指标时间点。
     */
    private LocalDateTime bizTime;
    /**
     * 特征值，可以理解为组成指标的子指标的唯一特质值。
     */
    private String property;
    /**
     * 指标值
     */
    private BigDecimal value;
    /**
     * 指标的加工生产时间
     */
    private LocalDateTime calculateTime;

    /**
     * 指标名称
     */
    private String fragmentName;


    public static IndicatorDTO fromDoc(IndicatorDoc doc) {
        return IndicatorDTO.builder().bizTime(doc.getBizTime())
                .code(doc.getCode())
                .property(doc.getProperty())
                .logisticsUnitId(doc.getLogisticsUnitId())
                .calculateTime(doc.getCalculateTime())
                .bizTime(doc.getBizTime())
                .value(doc.getValue())
                .fragmentName(doc.getFragmentName())
                .build();
    }
}
