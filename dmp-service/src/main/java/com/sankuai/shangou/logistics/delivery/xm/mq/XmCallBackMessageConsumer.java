package com.sankuai.shangou.logistics.delivery.xm.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.shangou.logistics.delivery.xm.enums.CallBackEnum;
import com.sankuai.shangou.logistics.delivery.xm.enums.CardEnum;
import com.sankuai.shangou.logistics.delivery.xm.msg.LaunchFailHandleCallbackMessage;
import com.sankuai.shangou.logistics.delivery.xm.sevice.CardCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 办公开放平台回调消息消费
 * @date 2025-05-20
 */
@Slf4j
@Component
@MafkaConsumer(namespace = "com.sankuai.mafka.castle.daojiacommon",
        topic = "xm_call_back_message_topic",
        group = "xm_call_back_message_consumer",
        deadLetter = true)
public class XmCallBackMessageConsumer implements IMessageListener {
    private final static String CALLBACK_TYPE = "callBackType";
    private final static String CARD_TYPE = "cardType";

    @Autowired
    private CardCallbackService cardCallbackService;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
        try {
            log.info("开始消费办公开放平台回调消息: {}", mafkaMessage);
            String messageBody = (String) mafkaMessage.getBody();
            if (StringUtils.isEmpty(messageBody)) {
                log.info("messageBody is empty");
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            JSONObject jsonObject = JSON.parseObject(messageBody);
            CallBackEnum callBackEnum = CallBackEnum.ofType(jsonObject.getString(CALLBACK_TYPE));
            if (callBackEnum == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            switch (callBackEnum) {
                case CARD_CALLBACK:
                    cardCallbackHandle(jsonObject);
                    break;
                case EVENT_CALLBACK:
                    break;
                default:
                    break;
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("消费办公开放平台回调消息失败", e);
            return ConsumeStatus.RECONSUME_LATER;
        }
    }

    private void cardCallbackHandle(JSONObject jsonObject) {
        CardEnum cardEnum = CardEnum.ofType(jsonObject.getString(CARD_TYPE));
        if (cardEnum == null) {
            return;
        }
        switch (cardEnum) {
            case DELIVERY_LAUNCH_FAILED:
                LaunchFailHandleCallbackMessage launchFailHandleCallbackMessage = jsonObject.toJavaObject(LaunchFailHandleCallbackMessage.class);
                if (launchFailHandleCallbackMessage == null) {
                    log.info("launchFailHandleCallbackMessage is null");
                    return;
                }
                cardCallbackService.handleLaunchFail(launchFailHandleCallbackMessage);
                break;
        }
    }
}
