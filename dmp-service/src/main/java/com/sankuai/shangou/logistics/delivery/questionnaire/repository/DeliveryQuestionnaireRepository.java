package com.sankuai.shangou.logistics.delivery.questionnaire.repository;

import com.sankuai.shangou.logistics.delivery.mapper.DeliveryQuestionnaireDOMapper;
import com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDO;
import com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDOExample;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/11/15 14:41
 **/
@Repository
public class DeliveryQuestionnaireRepository {
    @Resource
    private DeliveryQuestionnaireDOMapper deliveryQuestionnaireDOMapper;


    /**
     * 获取骑手最后一次被投放问卷的记录
     * @param riderAccountId 骑手id
     * @return 最后一次被投放问卷的记录
     */
    public Optional<DeliveryQuestionnaireDO> queryRiderLatestQuestionnaireRecord(Long riderAccountId) {
        DeliveryQuestionnaireDOExample example = new DeliveryQuestionnaireDOExample();
        example.createCriteria()
                .andRiderAccountIdEqualTo(riderAccountId)
                .andIsValidEqualTo(1)
                .andReleaseTimeBetween(LocalDateTime.now().minusHours(24), LocalDateTime.now());


        example.setOrderByClause("release_time desc");
        //todo limit 1

        List<DeliveryQuestionnaireDO> deliveryQuestionnaireDOList = deliveryQuestionnaireDOMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(deliveryQuestionnaireDOList)) {
            return Optional.empty();
        }

        return Optional.of(deliveryQuestionnaireDOList.get(0));
    }


    /**
     * 批量插入问卷
     * @param deliveryQuestionnaireDOList 问卷信息
     */
    public void batchInsert(List<DeliveryQuestionnaireDO> deliveryQuestionnaireDOList) {
        if (CollectionUtils.isEmpty(deliveryQuestionnaireDOList)) {
            return;
        }

        deliveryQuestionnaireDOMapper.batchInsert(deliveryQuestionnaireDOList);
    }

    public List<DeliveryQuestionnaireDO> queryQuestionnaireByDeliveryOrderIds(List<Long> deliveryOrderIds) {
        if (CollectionUtils.isEmpty(deliveryOrderIds)) {
            return Collections.emptyList();
        }

        DeliveryQuestionnaireDOExample example = new DeliveryQuestionnaireDOExample();
        example.createCriteria()
                .andDeliveryOrderIdIn(deliveryOrderIds)
                .andIsValidEqualTo(1);

        return deliveryQuestionnaireDOMapper.selectByExample(example);
    }

    @Transactional
    public void batchUpdate(List<DeliveryQuestionnaireDO> deliveryQuestionnaireDOList) {
        if (CollectionUtils.isEmpty(deliveryQuestionnaireDOList)) {
            return;
        }

        deliveryQuestionnaireDOList.forEach(record -> deliveryQuestionnaireDOMapper.updateByPrimaryKeySelective(record));
    }

    public void invalidQuestionnaire(Long deliveryOrderId) {
        DeliveryQuestionnaireDOExample example = new DeliveryQuestionnaireDOExample();
        example.createCriteria()
                .andDeliveryOrderIdEqualTo(deliveryOrderId)
                .andIsValidEqualTo(1);

        DeliveryQuestionnaireDO questionnaireDO = new DeliveryQuestionnaireDO();
        questionnaireDO.setIsValid(0);
        deliveryQuestionnaireDOMapper.updateByExampleSelective(questionnaireDO, example);
    }

}
