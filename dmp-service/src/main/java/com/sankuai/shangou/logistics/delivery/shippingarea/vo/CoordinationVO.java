package com.sankuai.shangou.logistics.delivery.shippingarea.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

/**
 * <AUTHOR>
 * @since 2023/8/15 17:11
 **/
@TypeDoc(
        description = "坐标点VO"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CoordinationVO {
    @FieldDoc(
            description = "经度"
    )
    private Double longitude;

    @FieldDoc(
            description = "维度"
    )
    private Double latitude;
}
