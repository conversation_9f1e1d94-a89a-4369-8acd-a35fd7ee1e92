package com.sankuai.shangou.logistics.delivery.offlineboard.enums;

import com.sankuai.meituan.shangou.saas.common.exception.BizException;

/**
 * <AUTHOR>
 * @since 2024/3/13 17:38
 **/
public enum BizIndicatorType {
    STRING(1),
    NUMBER(2),
    BOOLEAN(3);

    int code;


    BizIndicatorType(int code) {
        this.code = code;
    }

    public static Object formatValue(Object value, BizIndicatorType type) {
        if (value == null) {
            return null;
        }
        switch (type) {
            case STRING:
                return value.toString();
            case NUMBER:
                return Double.valueOf(value.toString()).intValue();
            case BOOLEAN:
                return new Boolean(value.toString());

            default:
                throw new BizException("不识别的类型");
        }
    }
}
