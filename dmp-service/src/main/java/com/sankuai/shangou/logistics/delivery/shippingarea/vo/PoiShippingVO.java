package com.sankuai.shangou.logistics.delivery.shippingarea.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/8/15 17:08
 **/
@TypeDoc(
        description = "中台门店配送信息"
)
@Builder
@Data
public class PoiShippingVO {
    @FieldDoc(
            description = "门店id(中台门店id)"
    )
    private Long storeId;

    @FieldDoc(
            description = "门店名称"
    )
    private String storeName;

    @FieldDoc(
            description = "门店负责人"
    )
    private String storeOwner;

    @FieldDoc(
            description = "点位坐标"
    )
    private CoordinationVO coordinate;

    @FieldDoc(
            description = "门店正常时段配送范围（多渠道合并配送范围）"
    )
    private String regularPeriodDeliveryRegion;

    @FieldDoc(
            description = "门店特殊时段配送范围（多渠道合并配送范围）"
    )
    private String specialPeriodDeliveryRegion;
    @FieldDoc(
            description = "门店可编辑状态（只有拥有百川权限的门店可编辑）：1-可编辑; 0-不可编辑"
    )
    private Integer editable;
}
