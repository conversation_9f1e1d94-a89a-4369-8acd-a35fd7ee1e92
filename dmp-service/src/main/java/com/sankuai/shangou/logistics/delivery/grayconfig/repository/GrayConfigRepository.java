package com.sankuai.shangou.logistics.delivery.grayconfig.repository;

import com.alibaba.fastjson.JSON;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.grayconfig.GrayConfigService;
import com.sankuai.shangou.logistics.delivery.grayconfig.dto.GrayConfig;
import com.sankuai.shangou.logistics.delivery.mapper.GrayConfigDOMapper;
import com.sankuai.shangou.logistics.delivery.model.GrayConfigDO;
import com.sankuai.shangou.logistics.delivery.model.GrayConfigDOExample;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/4/19 16:46
 **/
@Repository
public class GrayConfigRepository {
    @Resource
    private GrayConfigDOMapper grayConfigDOMapper;

    @MethodLog(logRequest = true, logResponse = true)
    public GrayConfig query(Long tenantId, String grayKey, Integer operationMode) {
        if (Objects.isNull(tenantId) || StringUtils.isBlank(grayKey) || Objects.isNull(operationMode)) {
            throw new SystemException("参数不合法");
        }

        GrayConfigDOExample example = new GrayConfigDOExample();
        example.createCriteria()
                .andGrayKeyEqualTo(grayKey)
                .andTenantIdEqualTo(tenantId)
                .andStoreOperationModeIn(Arrays.asList(operationMode, -1));

        List<GrayConfigDO> grayConfigDOS = grayConfigDOMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(grayConfigDOS)) {
            return null;
        }

        return transform(grayConfigDOS.get(0));
    }


    @MethodLog(logRequest = true, logResponse = true)
    public List<GrayConfig> query(Long tenantId, String grayKey) {
        if (Objects.isNull(tenantId) || StringUtils.isBlank(grayKey)) {
            throw new SystemException("参数不合法");
        }

        GrayConfigDOExample example = new GrayConfigDOExample();
        example.createCriteria()
                .andGrayKeyEqualTo(grayKey)
                .andTenantIdEqualTo(tenantId);

        List<GrayConfigDO> grayConfigDOS = grayConfigDOMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(grayConfigDOS)) {
            return Collections.emptyList();
        }

        return grayConfigDOS.stream().map(this::transform).collect(Collectors.toList());
    }

    private GrayConfig transform(GrayConfigDO grayConfigDO) {
        GrayConfig grayConfig = new GrayConfig();
        grayConfig.setId(grayConfigDO.getId());
        grayConfig.setGrayKey(grayConfigDO.getGrayKey());
        grayConfig.setStoreOperationMode(grayConfigDO.getStoreOperationMode());

        if (StringUtils.isNotBlank(grayConfigDO.getGrayCityList())) {
            grayConfig.setGrayCityList(JSON.parseArray(grayConfigDO.getGrayCityList(), Integer.class));
        } else {
            grayConfig.setGrayCityList(Collections.emptyList());
        }

        if (StringUtils.isNotBlank(grayConfigDO.getGrayStoreList())) {
            grayConfig.setGrayStoreList(JSON.parseArray(grayConfigDO.getGrayStoreList(), Long.class));
        } else {
            grayConfig.setGrayStoreList(Collections.emptyList());
        }

        return grayConfig;
    }
}
