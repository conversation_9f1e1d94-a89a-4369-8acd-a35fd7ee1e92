package com.sankuai.shangou.logistics.delivery.configure.external;

import com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiRelationThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.TenantThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 租户系统客户端实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/10
 */
@Slf4j
@Service
public class TenantStoreClient {
	private static final int SUCCESS = 0;

	@Resource
	private ChannelPoiManageThriftService channelPoiManageThriftServiceClient;
	@Resource
	private PoiThriftService poiThriftServiceClient;

	@Resource
	private TenantThriftService tenantThriftService;

    @Resource
    private PoiRelationThriftService poiRelationThriftService;


	@CatTransaction
	public PoiInfoDto queryStoreDetailInfo(Long tenantId, Long storeId) {
		try {
			PoiMapResponse response = poiThriftServiceClient.queryTenantPoiInfoMapByPoiIds(Lists.newArrayList(storeId), tenantId);
			if (response.getStatus().getCode().equals(StatusCodeEnum.SUCCESS.getCode())){
                return response.getOne(storeId);
			}
		} catch (Exception e) {
			log.error("queryStoreDetailInfo error", e);
		}
		return null;
	}

	@CatTransaction
	public Map<Long, PoiInfoDto> queryStoresDetailInfo(Long tenantId, List<Long> storeIds) {
		try {
			PoiMapResponse response = poiThriftServiceClient.queryTenantPoiInfoMapByPoiIds(storeIds, tenantId);
			if (response.getStatus().getCode().equals(StatusCodeEnum.SUCCESS.getCode())){
				return response.getPoiInfoMap();
			}
		} catch (Exception e) {
			log.error("queryStoresDetailInfo error", e);
		}
		return Collections.emptyMap();
	}


}
