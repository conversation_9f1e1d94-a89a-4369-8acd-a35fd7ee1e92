package com.sankuai.shangou.logistics.delivery.offlineboard.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/2/16 14:21
 **/
public enum OfflineFulfillDataSortFieldEnum {

    EMPLOYEE_NAME("employeeName", "employeeName"),

    STORE_NAME("storeName", "poiName"),

    DELIVERED_ORDER_NUM("deliveredOrderNum", "arrivedOrdNum"),

    CANCEL_ORDER_NUM("cancelOrderNum", "notArrCancelOrdNum"),

    JUDGE_TIMEOUT_ORDER_NUM("judgeTimeoutOrderNum", "checkOvertimeOrdNum"),

    COMPLETE_BEFORE_DELIVERED_ORDER_NUM("completeBeforeDeliveredOrderNum", "aheadArrivedOrdNum"),

    COMPLETE_BEFORE_DELIVERED_ORDER_PERCENT("completeBeforeDeliveredOrderPercent", "aheadArrivedOrdNum/arrivedOrdNum"),

    DELIVERY_DISTANCE_AVG("deliveryDistanceAvg", "deliveryDistance/arrivedOrdNum"),

    FULFILL_DURATION_AVG("fulfillDurationAvg", "performanceDuration/arrivedOrdNoPreNum"),

    NINETIETH_FULFILL_DURATION("ninetiethFulfillDuration", "ninePerformanceDuration"),

    OUT_WAREHOUSE_DURATION_AVG("outWarehouseDurationAvg", "storehouseOutDuration/arrivedOrdNum"),

    DELIVERY_RATE_IN_25_MIN("deliveredRateIn25min", "twentyFiveMinArrOrdNum/arrivedOrdNoPreNum"),

    DELIVERY_RATE_IN_15_MIN("deliveredRateIn15min", "fifteenMinArrOrdNum/arrivedOrdNoPreNum"),

    DELIVERY_RATE_IN_45_MIN("deliveredRateIn45min", "fortyFiveMinArrOrdNum/arrivedOrdNoPreNum"),

    BAD_COMMENT_ORDER_NUM("badCommentOrderNum", "deliveryNegCommentOrdNum"),

    BAD_COMMENT_ORDER_PERCENT("badCommentOrderPercent", "deliveryNegCommentOrdNum/arrivedOrdNumHaveOne"),

    DELIVERY_LABOR_EFFICIENCY("deliveryLaborEfficiency", "arrivedOrdNum/employeeDays"),

    VIEW_ORDER_ID("viewOrderId", "orderIdView"),

    IS_COMPLETE_BEFORE_DELIVERED("isCompeteBeforeDelivered", "isHeadArrivedOrd"),

    IS_JUDGE_TIMEOUT("isJudgeTimeout", "isCheckOvertimeOrd"),

    IS_BAD_COMMENT("isBadComment", "isDeliveryNegComment"),

    TIMEOUT_DURATION("timeOutDuration", "overtimeDuration"),

    RISK_CONTROL_ORDER_NUM("riskControlOrderNum", "riskControlOrdNum"),

    IS_RISK_CONTROLLER_ORDER("isRiskControlOrder", "isUserPhoneEqualEmployee"),

    PERFORMANCE_DURATION_AVG("performanceDurationAvg", "performanceDuration/arrivedOrdNoPreNum"),

    DELIVERY_PERCENTAGE_IN_25_MIN("twentyFiveMinArrOrdNumPercentage", "twentyFiveMinArrOrdNum/arrivedOrdNoPreNum"),

    DELIVERY_PERCENTAGE_IN_15_MIN("fifteenMinArrOrdNumPercentage", "fifteenMinArrOrdNum/arrivedOrdNoPreNum"),

    DELIVERY_PERCENTAGE_IN_45_MIN("fortyFiveMinArrOrdNumPercentage", "fortyFiveMinArrOrdNum/arrivedOrdNoPreNum"),

    BAD_COMMENT_ORDER_PERCENTAGE("deliveryNegCommentOrdPercentage", "deliveryNegCommentOrdNum/arrivedOrdNumHaveOne"),

    STORE_WAREHOUSE_OUT_DURATION_AVG("storehouseOutDuration", "storehouseOutDuration/arrivedOrdNum"),

    PICK_GOODS_DURATION_AVG("pickGoodsDurationAvg", "pickGoodsDuration/arrivedOrdNum"),

    BAD_OVERTIME_ORD_PERCENTAGE("badOvertimeOrdPercentage", "badOvertimeOrdNum/arrivedOrdNum"),

    ETA_OVERTIME_ORD_PERCENTAGE("etaOvertimeOrdPercentage", "etaOvertimeOrdNum/arrivedOrdNum"),

    FULL_TIME_ACC_DAY_EFFICIENCY("fullTimeAccDayEfficiency", "fullTimeArrivedOrdNum/fullTimeAccDayNum"),

    PART_TIME_ARRIVED_ORD_PERCENTAGE("partTimeArrivedOrdPercentage", "partTimeArrivedOrdNum/arrivedOrdNum"),

    PART_TIME_ACC_DAY_EFFICIENCY("partTimeAccDayEfficiency", "partTimeArrivedOrdNum/partTimeAccDayNum"),

    ;


    private String fieldName;

    private String sortRule;

    OfflineFulfillDataSortFieldEnum(String fieldName, String sortRule) {
        this.fieldName = fieldName;
        this.sortRule = sortRule;
    }

    public static OfflineFulfillDataSortFieldEnum enumOf(String fieldName) {
        for (OfflineFulfillDataSortFieldEnum fieldEnum : values()) {
            if (StringUtils.equals(fieldName, fieldEnum.fieldName)) {
                return fieldEnum;
            }
        }

        return null;
    }

    public String getFieldName() {
        return fieldName;
    }

    public String getSortRule() {
        return sortRule;
    }
}
