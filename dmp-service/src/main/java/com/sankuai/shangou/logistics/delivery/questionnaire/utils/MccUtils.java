package com.sankuai.shangou.logistics.delivery.questionnaire.utils;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/11/15 14:45
 **/
@Slf4j
public class MccUtils {
    public static boolean checkIsDHTenant(Long tenantId) {

        if (Objects.isNull(tenantId)) {
            log.warn("tenantId is null");
            return false;
        }

        List<Long> dhTenantIds = Lion.getConfigRepository().getList("dh.tenant.ids", Long.class, Collections.singletonList(1000395L));

        return dhTenantIds.contains(tenantId);
    }

    public static boolean checkIsQuestionnaireGrayCity(Integer cityId) {
        if (cityId == null) {
            return false;
        }

        List<Integer> grayCityList = Lion.getConfigRepository().getList("questionnair.gray.city", Integer.class);

        if (CollectionUtils.isEmpty(grayCityList)) {
            return false;
        }

        if (grayCityList.size() == 1 && Objects.equals(grayCityList.get(0), -1)) {
            return true;
        }

        return grayCityList.contains(cityId);
    }

    public static boolean checkIsQuestionnaireGrayStore(Long storeId) {
        if (storeId == null) {
            return false;
        }

        List<Long> grayStoreList = Lion.getConfigRepository().getList("questionnaire.gray.store", Long.class);

        if (CollectionUtils.isEmpty(grayStoreList)) {
            return false;
        }

        if (grayStoreList.size() == 1 && Objects.equals(grayStoreList.get(0), -1L)) {
            return true;
        }

        return grayStoreList.contains(storeId);
    }

    /**
     * 获取投放问卷的时间间隔 单位: 分钟
     * @return 投放问卷的时间间隔
     */
    public static Integer getReleaseQuestionnairePeriod() {
        return Lion.getConfigRepository().getIntValue("release.questionnaire.period", 180);
    }

    /**
     * 获取投放的问卷题目和答案选项
     */
    public static Map<String, List<String>> getQuestionnaireInfo() {
        String jsonStr = Lion.getConfigRepository().get("questionnaire.question.and.answers", "{}");

        try {
            return JsonUtil.fromJson(jsonStr, new TypeReference<Map<String, List<String>>>() {});
        } catch (Exception e) {
            log.error("解析问卷问题和答案失败", e);
            Cat.logEvent("DELIVERY_QUESTIONNAIRE", "PARSE_QUESTION_ANSWER_FAIL");
            return Collections.emptyMap();
        }
    }
}
