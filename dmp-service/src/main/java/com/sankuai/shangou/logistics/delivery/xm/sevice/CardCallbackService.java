package com.sankuai.shangou.logistics.delivery.xm.sevice;

import com.google.common.collect.Lists;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaProducer;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.resp.UserIdentity;
import com.sankuai.shangou.logistics.delivery.alert.utils.MccUtils;
import com.sankuai.shangou.logistics.delivery.common.utils.TimeUtils;
import com.sankuai.shangou.logistics.delivery.deliveryorder.repository.es.DeliveryOrderEagleRepository;
import com.sankuai.shangou.logistics.delivery.push.DxCardService;
import com.sankuai.shangou.logistics.delivery.push.msg.card.LaunchFailCardUpdateData;
import com.sankuai.shangou.logistics.delivery.xm.enums.CardOperationEnum;
import com.sankuai.shangou.logistics.delivery.xm.enums.CardTitleEnum;
import com.sankuai.shangou.logistics.delivery.xm.msg.LaunchFailHandleCallbackMessage;
import com.sankuai.xm.openplatform.api.entity.CardDataTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 卡片回调处理服务
 * @date 2025-05-20
 */
@Slf4j
@Service
public class CardCallbackService {
    @Autowired
    private DxCardService dxCardService;
    @Autowired
    private DeliveryOrderEagleRepository deliveryOrderEagleRepository;
    @Autowired
    private QueryUserIdentityClient queryUserIdentityClient;

    @MafkaProducer(namespace = "waimai",
            appkey = "com.sankuai.sgfulfillment.tms",
            topic = "shangou_empower_delivery_hu_sync_topic")
    private IProducerProcessor deliveryHuSyncMessageProducer;


    public void handleLaunchFail(LaunchFailHandleCallbackMessage message) {
        operatorLog(message.getEmpId());
        if (CardOperationEnum.EXECUTE.equals(message.getOperationType())) {
            // 查询时间段发单失败订单
            List<Long> orderIds = getOrderIds(message);
            //补单
            reissue(orderIds);
            // 修改卡片信息
            LaunchFailCardUpdateData data = buildLaunchFailCardUpdateData(message);
            data.setCardTitle(CardTitleEnum.LAUNCH_FAILED_HANDLED);
            dxCardService.updateGroupExclusionCard(data);
        } else if (CardOperationEnum.REJECT.equals(message.getOperationType())) {
            // 修改卡片信息
            LaunchFailCardUpdateData data = buildLaunchFailCardUpdateData(message);
            data.setCardTitle(CardTitleEnum.LAUNCH_FAILED_CANCEL);
            dxCardService.updateGroupExclusionCard(data);
        } else {
            log.info("不支持的操作类型");
        }
    }

    private void reissue(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        int failCount = 0;
        for (Long orderId : orderIds) {
            try {
                deliveryHuSyncMessageProducer.sendMessage(orderId.toString());
            } catch (Exception e) {
                failCount++;
            }
        }
        log.info("补发结果：成功{}个，失败{}个", orderIds.size() - failCount, failCount);
    }

    private List<Long> getOrderIds(LaunchFailHandleCallbackMessage message) {
        LocalDateTime beginTime = LocalDateTime.parse(message.getBeginTime(), TimeUtils.DATE_TIME_FORMATTER_DOT);
        LocalDateTime endTime = LocalDateTime.parse(message.getEndTime(), TimeUtils.DATE_TIME_FORMATTER_DOT);
        int size = 200;
        int from = 0;
        Pair<List<Long>, Long> pair = deliveryOrderEagleRepository.searchLaunchFailOrderIds(beginTime, endTime, from, size);
        int count = pair.getRight().intValue();
        if (count < size) {
            return pair.getLeft();
        }
        List<Long> orderIds = new ArrayList<>(pair.getLeft());
        from += size;
        while (from < count) {
            pair = deliveryOrderEagleRepository.searchLaunchFailOrderIds(beginTime, endTime, from, size);
            if (CollectionUtils.isNotEmpty(pair.getLeft())) {
                orderIds.addAll(pair.getLeft());
            }
            from += size;
        }
        return orderIds;
    }

    private LaunchFailCardUpdateData buildLaunchFailCardUpdateData(LaunchFailHandleCallbackMessage message) {
        LaunchFailCardUpdateData data = new LaunchFailCardUpdateData();
        data.setRequestId(message.getRequestId());
        data.setTemplateId(MccUtils.getDeliveryFailDxCardTemplateId());
        data.setCardDataType(CardDataTypeEnum.PUBLIC);
        data.setUpdateCardDataByKey(true);
        return data;
    }

    private void operatorLog(Long empId) {
        if (!Objects.isNull(empId)) {
            Map<Long, UserIdentity> identityMap = queryUserIdentityClient.queryByEmpIds(Lists.newArrayList(empId));
            UserIdentity userIdentity = identityMap.get(empId);
            log.info("操作用户信息: {}", userIdentity);
        }
    }
}
