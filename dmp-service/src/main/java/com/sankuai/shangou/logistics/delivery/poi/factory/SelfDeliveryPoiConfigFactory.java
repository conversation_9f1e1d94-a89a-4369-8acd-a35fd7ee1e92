package com.sankuai.shangou.logistics.delivery.poi.factory;

import com.sankuai.shangou.logistics.delivery.constants.DapSyncStatusEnum;
import com.sankuai.shangou.logistics.delivery.constants.DeleteEnum;
import com.sankuai.shangou.logistics.delivery.poi.domain.SelfDeliveryPoiConfig;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023/9/1 16:08
 **/
public class SelfDeliveryPoiConfigFactory {
    private static Long SYSTEM_OPERATOR_ID = 0L;

    private static String SYSTEM_OPERATOR_NAME = "System";

    public static SelfDeliveryPoiConfig initSelfDeliveryPoiConfig(Long tenantId, Long poiId) {
        return new SelfDeliveryPoiConfig(null, tenantId, poiId, 0, DapSyncStatusEnum.NOT_SYNC,
                SYSTEM_OPERATOR_NAME, SYSTEM_OPERATOR_ID, LocalDateTime.now(),
                DeleteEnum.NOT_DELETE, LocalDateTime.now(), LocalDateTime.now());
    }


}
