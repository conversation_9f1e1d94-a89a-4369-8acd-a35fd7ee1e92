package com.sankuai.shangou.logistics.delivery.xm.enums;

import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 卡片类型枚举
 * @date 2025-05-16
 */
@Getter
public enum CardEnum {
    DELIVERY_LAUNCH_FAILED("DELIVERY_LAUNCH_FAILED", "配送发单失败"),
    ;

    private final String type;
    private final String desc;

    CardEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static CardEnum ofType(String type) {
        return Stream.of(values())
                .filter(cardEnum -> cardEnum.getType().equals(type))
                .findFirst().orElse(null);
    }
}
