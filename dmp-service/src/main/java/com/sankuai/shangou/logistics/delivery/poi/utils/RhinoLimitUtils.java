package com.sankuai.shangou.logistics.delivery.poi.utils;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.onelimiter.LimitResult;
import com.dianping.rhino.onelimiter.OneLimiter;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;

import java.util.Random;

@Slf4j
public class RhinoLimitUtils {

    private static String DAP_STORE_SYNC_LIMIT_KEY = "dap.store.sync.limit.key";

    private static OneLimiter oneLimiter = Rhino.newOneLimiter();

    /**
     * 同步青云限流
     * @param count
     * @return
     */
    public static void syncDapLimiter(int count) {
        if (count <= 0) {
            return;
        }
        int tryCount = 0;

        try {
            while (tryCount < MccUtils.getRhinoRetryMaxCount() && (!rhinoLimiter(DAP_STORE_SYNC_LIMIT_KEY, count))) {
                tryCount++;
                Thread.sleep(getRandomSleepTime());
            }
            if (tryCount >= MccUtils.getRhinoRetryMaxCount()) {
                throw new BizException("flushOrderLimiter 已到限定次数");
            }
            return;
        } catch (Exception e) {
            log.error("flushOrderLimiter error ,count:{}", count, e);
        }
        throw new BizException("flushOrderLimiter error");
    }

    /**
     * rhino限流
     * @param limitKey
     * @param count
     * @return true 通过 false 限流
     */
    public static boolean rhinoLimiter(String limitKey, int count) {
        try {
            LimitResult result = oneLimiter.run(limitKey);
            return result.isPass();
        } catch (Exception | Error e) {
            log.error("rhinoLimiter error ,limitKey:{},count:{}", limitKey, count, e);
            return false;
        }
    }

    /**
     * 随机获取sleep时间
     */
    private static Integer getRandomSleepTime() {
        Random random = new Random();
        return random.nextInt(MccUtils.getDapStoreSyncRhinoSleepTime());
    }

}
