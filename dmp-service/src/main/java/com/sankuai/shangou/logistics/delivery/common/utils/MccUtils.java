package com.sankuai.shangou.logistics.delivery.common.utils;

import com.dianping.lion.client.Lion;

/**
 * <AUTHOR>
 * @description
 * @date 2025-05-14
 */
public class MccUtils {

    private MccUtils() {
    }

    /**
     * 缓存操作重试次数
     */
    public static int getSquirrelOperateRetryCount(){
        return Lion.getConfigRepository().getIntValue("squirrel.operate.retry.count", 3);
    }

    /**
     * 缓存操作重试间隔 毫秒
     */
    public static int getSquirrelOperateRetryPeriod(){
        return Lion.getConfigRepository().getIntValue("squirrel.operate.retry.period", 100);
    }
}
