package com.sankuai.shangou.logistics.delivery.poi.client;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.SiteTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.OpenAggDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.request.QueryAggStoreConfigUrlRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.response.QueryAggStoreConfigUrlResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.CreateAggregationDeliveryPoiRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.QueryAggStoreSettingsLinkRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.QueryAggLinkResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.utils.rpc.RpcInvokeUtils;
import com.sankuai.shangou.logistics.delivery.constants.Operator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-08-29
 * @email <EMAIL>
 */
@Slf4j
@Service
public class OpenAggDeliveryClient {

    @Resource
    private OpenAggDeliveryThriftService openAggDeliveryThriftService;

    @Resource
    private DeliveryOperationThriftService deliveryOperationThriftService;

    public String queryDapStoreConfig(long tenantId, long poiId, String token) {
        QueryAggStoreConfigUrlRequest request = new QueryAggStoreConfigUrlRequest();
        request.setTenantId(tenantId);
        request.setPoiId(poiId);
        request.setDeliveryPlatform(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode());
        if(StringUtils.isNotBlank(token)){
            request.setToken(token);
        }
        request.setDeviceType(SiteTypeEnum.PC.getCode());
        QueryAggStoreConfigUrlResponse response = openAggDeliveryThriftService.queryAggStoreConfigUrlInfo(request);
        if (!Objects.equals(response.getStatus().getCode(), Status.SUCCESS.code) || Objects.isNull(response.getTDeliveryPlatformUrl())) {
            throw new ThirdPartyException("查询青云门店配置url错误");
        }

        return response.getTDeliveryPlatformUrl().getLinkUrl();
    }

    public String queryDapStoreSettings(long tenantId, long poiId, String token, Long employeeId, String employeeName) {
        QueryAggStoreSettingsLinkRequest request = new QueryAggStoreSettingsLinkRequest();
        request.setPlatformCode(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode());
        request.setEToken(token);
        request.setTenantId(tenantId);
        request.setPoiId(poiId);
        request.setDeviceType(SiteTypeEnum.PC.getCode());
        request.setOperatorAccount(String.valueOf(employeeId));
        request.setOperatorName(employeeName);
        QueryAggLinkResponse response = deliveryOperationThriftService.queryAggStoreSettingsLink(request);
        if (response == null || !Objects.equals(response.getStatus().getCode(), Status.SUCCESS.code) || StringUtils.isBlank(response.getUrl())) {
            log.error("queryDapStoreSettings is error; request:{}, response:{}", request, response);
            throw new ThirdPartyException("获取青云门店配置链接异常");
        }
        return response.getUrl();
    }


    public void syncDapStore(Long tenantId, Long storeId) {
        CreateAggregationDeliveryPoiRequest request = new CreateAggregationDeliveryPoiRequest(tenantId, storeId,
                DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode(),
                Operator.system.getOperatorId(), Operator.system.getOperatorName());
        RpcInvokeUtils.rpcInvokeTemplate(() -> openAggDeliveryThriftService.createAggregationDeliveryPoi(request),
                "同步青云门店失败",
                resp -> resp.getStatus().getCode(), resp -> resp.getStatus().getMsg());
    }



}
