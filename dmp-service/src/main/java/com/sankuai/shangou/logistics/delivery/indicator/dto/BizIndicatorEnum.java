package com.sankuai.shangou.logistics.delivery.indicator.dto;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/6/28 19:40
 **/
public enum BizIndicatorEnum {
    WAIT_ACCEPT_ORDER_COUNT("wait_accept_order_count", 1, "待领取单量", IndicatorGroupType.BY_SINGLE_INDICATOR, IndicatorPurposeEnum.SHOW),

    WAIT_PICK_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT("wait_pick_by_scheduled_employee_order_count", 1, "当前排班员工待拣货单量", IndicatorGroupType.BY_SINGLE_INDICATOR, IndicatorPurposeEnum.SHOW),

    WAIT_PICK_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT("wait_pick_by_unscheduled_employee_order_count", 1, "当前未排班员工待拣货单量", IndicatorGroupType.BY_SINGLE_INDICATOR, IndicatorPurposeEnum.SHOW),


    DELIVERED_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT("delivered_by_scheduled_employee_order_count", 1, "当前排班员工配送中单量", IndicatorGroupType.BY_SINGLE_INDICATOR, IndicatorPurposeEnum.SHOW),

    DELIVERED_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT("delivered_by_unscheduled_employee_order_count", 1, "当前未排班员工配送中单量", IndicatorGroupType.BY_SINGLE_INDICATOR, IndicatorPurposeEnum.SHOW),

    SCHEDULED_EMPLOYEE_COUNT("scheduled_employee_count", 1, "当前排班员工数", IndicatorGroupType.BY_SHIFT, IndicatorPurposeEnum.SHOW),

    SCHEDULED_ATTENDANCE_EMPLOYEE_COUNT("scheduled_attendance_employee_count", 1, "当前班次出勤员工数", IndicatorGroupType.BY_SHIFT, IndicatorPurposeEnum.SHOW),

    UNSCHEDULED_ATTENDANCE_EMPLOYEE_COUNT("unscheduled_attendance_employee_count", 1, "非当前班次出勤员工数", IndicatorGroupType.BY_SHIFT, IndicatorPurposeEnum.SHOW),

    ATTENDANCE_DELIVERY_LOAD("attendance_delivery_load", 1,"出勤员工人均负载", IndicatorGroupType.BY_SINGLE_INDICATOR, IndicatorPurposeEnum.SHOW_AND_ETA_CALC),

    SCHEDULED_EMPLOYEE_DELIVERY_LOAD_INCLUDE_UNSCHEDULED_ORDER("scheduled_employee_delivery_load_include_unscheduled_order", 1,"排班员工人均负载(未剔除未排班员工运单)", IndicatorGroupType.BY_SINGLE_INDICATOR, IndicatorPurposeEnum.SHOW_AND_ETA_CALC),

    SCHEDULED_EMPLOYEE_DELIVERY_LOAD_EXCLUDE_UNSCHEDULED_ORDER("scheduled_employee_delivery_load_exclude_unscheduled_order", 1,"排班员工人均负载(剔除未排班员工运单)", IndicatorGroupType.BY_SINGLE_INDICATOR, IndicatorPurposeEnum.SHOW_AND_ETA_CALC),

    FULFILL_EMPLOYEE_DELIVERY_LOAD_INCLUDE_WAIT_ACCEPT_ORDER("fulfill_employee_delivery_code_include_wait_accept_order", 1, "履约中人均负载", IndicatorGroupType.BY_SINGLE_INDICATOR, IndicatorPurposeEnum.SHOW_AND_ETA_CALC),

    FULFILL_EMPLOYEE_DELIVERY_LOAD_EXCLUDE_WAIT_ACCEPT_ORDER("fulfill_employee_delivery_code_exclude_wait_accept_order", 1, "骑手负载", IndicatorGroupType.BY_SINGLE_INDICATOR, IndicatorPurposeEnum.SHOW_AND_ETA_CALC),

    FULFILLING_EMPLOYEE_COUNT("fulfilling_employee_count", 1, "履约中员工数",  IndicatorGroupType.BY_SINGLE_INDICATOR , IndicatorPurposeEnum.SHOW),
    ;


    private String indicatorCode;
    /** 1表示仓 */
    private Integer logisticsUnitType;
    private String desc;

    private IndicatorGroupType indicatorGroupType;
    private IndicatorPurposeEnum indicatorPurposeEnum;


    BizIndicatorEnum(String indicatorCode, Integer logisticsUnitType, String desc, IndicatorGroupType indicatorGroupType, IndicatorPurposeEnum indicatorPurposeEnum) {
        this.indicatorCode = indicatorCode;
        this.logisticsUnitType = logisticsUnitType;
        this.desc = desc;
        this.indicatorGroupType = indicatorGroupType;
        this.indicatorPurposeEnum = indicatorPurposeEnum;
    }

    public String getIndicatorCode() {
        return indicatorCode;
    }

    public Integer getLogisticsUnitType() {
        return logisticsUnitType;
    }

    public String getDesc() {
        return desc;
    }

    public IndicatorGroupType getIndicatorGroupType() {
        return indicatorGroupType;
    }

    public IndicatorPurposeEnum getIndicatorPurposeEnum() {
        return indicatorPurposeEnum;
    }

    public static BizIndicatorEnum enumOf(String indicatorCode) {
        for (BizIndicatorEnum value : values()) {
            if (Objects.equals(value.getIndicatorCode(), indicatorCode)) {
                return value;
            }
        }

        throw new IllegalArgumentException("业务指标枚举值不合法");
    }
}
