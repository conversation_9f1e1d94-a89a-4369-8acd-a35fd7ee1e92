package com.sankuai.shangou.logistics.delivery.indicator.service;

import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BaseIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorDTO;

import com.sankuai.shangou.logistics.delivery.indicator.dto.BizIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.repository.BaseIndicatorEagleRepository;
import com.sankuai.shangou.logistics.delivery.indicator.service.calculate.biz.BizIndicatorCalculator;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/6/28 16:28
 **/
@Component
@Slf4j
public class IndicatorReader {
    @Resource
    private BaseIndicatorEagleRepository baseIndicatorEagleRepository;

    @Resource
    private List<BizIndicatorCalculator> bizIndicatorCalculators;

    @MethodLog(logResponse = true, logRequest = true)
    public Map<LocalDateTime, List<IndicatorDTO>> readBizIndicator(Long logisticsUnitId, List<LocalDateTime> bizTimeList,
                                                                   List<BizIndicatorEnum> bizIndicatorEnums) {
        if (CollectionUtils.isEmpty(bizTimeList) || CollectionUtils.isEmpty(bizIndicatorEnums)) {
            return Collections.emptyMap();
        }

        Map<BizIndicatorEnum, BizIndicatorCalculator> bizCalculatorMap = bizIndicatorCalculators.stream()
                .collect(Collectors.toMap(BizIndicatorCalculator::getSupportIndicator, Function.identity()));

        //业务指标转基础指标
        List<String> baseIndicatorCodes = bizIndicatorEnums.stream()
                .map(bizCalculatorMap::get)
                .filter(Objects::nonNull)
                .map(BizIndicatorCalculator::getRelateBaseIndicators)
                .flatMap(Collection::stream)
                .distinct()
                .map(BaseIndicatorEnum::getIndicatorCode)
                .collect(Collectors.toList());

        //查询ES
        Map<LocalDateTime, List<IndicatorDTO>> baseIndicatorDTOMap = baseIndicatorEagleRepository.queryIndicator(logisticsUnitId, bizTimeList, baseIndicatorCodes);

        //查询结果去重
        Map<LocalDateTime, List<IndicatorDTO>> removeDuplicateBaseIndicator = baseIndicatorDTOMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> removeDuplicateIndicator(entry.getValue())));

        //计算业务指标 最后按bizTime聚合
        Map<LocalDateTime, List<IndicatorDTO>> result = removeDuplicateBaseIndicator.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                    return bizIndicatorEnums.stream()
                            .map(bizIndicatorEnum -> bizCalculatorMap.get(bizIndicatorEnum).calc(IListUtils.nullSafeGroupBy(entry.getValue(), IndicatorDTO::getCode)))
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());

                }));
        log.info("execute IndicatorReader.readBizIndicator end, result:{}", result);
        return result;
    }

    //重复数据取计算时间最早的那个
    private List<IndicatorDTO> removeDuplicateIndicator(List<IndicatorDTO> indicatorDTOS) {
        return new ArrayList<>(
                indicatorDTOS.stream()
                        .collect(Collectors.toMap(Function.identity(), Function.identity(),
                                (k1, k2) -> k1.getCalculateTime().isBefore(k2.getCalculateTime()) ? k1 : k2))
                        .keySet());
    }
}
