package com.sankuai.shangou.logistics.delivery.constants;

/**
 * <AUTHOR>
 * @since 2023/9/3 19:43
 **/
public enum SelfDeliveryPoiConfigOpTypeEnum {
    ENABLE_TURN_DELIVERY(10, "开启转配送方式"),
    DISABLE_TURN_DELIVERY(11, "关闭转配送方式"),

    ENABLE_PICK_DELIVERY_SPLIT(20, "开启拣配分离"),
    DISABLE_PICK_DELIVERY_SPLIT(21, "关闭拣配分离")
    ;
    private int code;
    private String desc;

    SelfDeliveryPoiConfigOpTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SelfDeliveryPoiConfigOpTypeEnum valueOfCode(int code) {
        for (SelfDeliveryPoiConfigOpTypeEnum obj : SelfDeliveryPoiConfigOpTypeEnum.values()) {
            if (java.util.Objects.equals(obj.code, code)) {
                return obj;
            }
        }
        return null;
    }

    public static SelfDeliveryPoiConfigOpTypeEnum valueOfDesc(String desc) {
        for (SelfDeliveryPoiConfigOpTypeEnum obj : SelfDeliveryPoiConfigOpTypeEnum.values()) {
            if (java.util.Objects.equals(obj.desc, desc)) {
                return obj;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
