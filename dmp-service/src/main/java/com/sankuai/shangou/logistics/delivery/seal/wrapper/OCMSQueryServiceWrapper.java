package com.sankuai.shangou.logistics.delivery.seal.wrapper;

import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListViewIdConditionResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * <AUTHOR>
 * @since 2024/6/28 16:22
 **/
@Service
@Slf4j
public class OCMSQueryServiceWrapper {

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    public OCMSOrderVO queryOrderByViewIdCondition(Long tenantId, String channelOrderId, Integer orderBizType) {
        OCMSListViewIdConditionRequest request = new OCMSListViewIdConditionRequest();
        request.setTenantId(tenantId);
        request.setViewIdConditionList(Collections.singletonList(new ViewIdCondition(orderBizType, channelOrderId)));

        OCMSListViewIdConditionResponse response;
        try {
            log.info("start invoke ocmsQueryThriftService.queryOrderByViewIdCondition, request: {}", request);
            response = ocmsQueryThriftService.queryOrderByViewIdCondition(request);
            log.info("end invoke ocmsQueryThriftService.queryOrderByViewIdCondition, result: {}", response);
        } catch (Exception e) {
            log.error("查询订单详情失败", e);
            throw new ThirdPartyException("查询订单详情失败");
        }

        if (response == null || response.getStatus() == null || response.getStatus().getCode() != 0) {
            throw new BizException("查询订单详情失败");
        }

        if (CollectionUtils.isEmpty(response.getOcmsOrderList())) {
            throw new BizException("查询订单详情失败,未查询到订单信息");
        }
        return response.getOcmsOrderList().get(0);
    }
}
