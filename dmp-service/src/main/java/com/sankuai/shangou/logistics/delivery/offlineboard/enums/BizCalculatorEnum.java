package com.sankuai.shangou.logistics.delivery.offlineboard.enums;

/**
 * <AUTHOR>
 * @since 2024/3/14 20:08
 **/
public enum BizCalculatorEnum {

    OFFLINE_SUMMARY_FULFILL_DATA("offline_summary_fulfill_data"),

    THIRD_OFFLINE_SUMMARY_FULFILL_DATA("third_offline_summary_fulfill_data"),

    OFFLINE_STORE_FULFILL_DATA("offline_store_fulfill_data"),

    OFFLINE_RIDER_FULFILL_DATA("offline_rider_fulfill_data"),

    OFFLINE_ABNORMAL_ORDER_FULFILL_DATA("offline_abnormal_order_fulfill_data"),

    OFFLINE_DELIVERY_CHANNEL_DATA("offline_delivery_channel_data"),

    ORG_PERFORMANCE_DATA("org_performance_data"),

    ;
    private String calculatorName;

    BizCalculatorEnum(String calculatorName) {
        this.calculatorName = calculatorName;
    }


    public String getCalculatorName() {
        return calculatorName;
    }
}
