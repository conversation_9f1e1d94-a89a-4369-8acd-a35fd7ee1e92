package com.sankuai.shangou.logistics.delivery.indicator.utils;

import com.dianping.lion.client.Lion;

/**
 * <AUTHOR>
 * @since 2023/11/3 14:55
 **/
public class MccUtils {
    public static String getIndicatorIndexName() {
        return Lion.getConfigRepository().get("indicator.index.name", "delivery_load_indicator");
    }

    public static String getLast3MonthsIndexAlias() {
        return Lion.getConfigRepository().get("last.3.months.index.alias", "delivery_load_indicator_latest_3_months");
    }

    public static Boolean getMonthDimensionIndexSwitch() {
        return Lion.getConfigRepository().getBooleanValue("month.dimension.index.switch", false);
    }

    public static Boolean getQuiteWriteESSwitch() {
        return Lion.getConfigRepository().getBooleanValue("quite.write.es.switch", false);
    }
}
