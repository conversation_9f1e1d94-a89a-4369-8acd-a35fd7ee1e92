package com.sankuai.shangou.logistics.delivery.tag.entity;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.api.tag.ProductTagDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-22
 * @email <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = {"skuId", "retailPrice", "goodsId"})
public class SkuGoodsKey {

    private String skuId;

    private BigDecimal retailPrice;

    private String goodsId;

    /**
     * 是否需要封签交付
     */
    private Boolean needSealDelivery = false;

    /**
     * 需要去除的tag
     */
    private List<ProductTagDTO> needRemoveTags;

    public SkuGoodsKey(String skuId, BigDecimal retailPrice, String goodsId) {
        this.skuId = skuId;
        this.retailPrice = retailPrice;
        this.goodsId = goodsId;
    }
}
