package com.sankuai.shangou.logistics.delivery.indicator.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 经营时刻表，Operation Management Time Table。
 * 表达经营实体的业务口径起始时间、开业时间、休业时间。
 */
public class OmTimetable {
    private final int startTimeMin;
    private final int openTimeMin;
    private final int closeTimeMin;
    // 最小间隔时间
    private final static int MINIMAL_INTERVAL_MIN = 5;
    private final static int MAXIMAL_INTERVAL_MIN = 60;

    public static OmTimetable getInstance(LocalTime startTime, LocalTime openTime, LocalTime closeTime) {
        return new OmTimetable(startTime, openTime, closeTime);
    }

    private OmTimetable(LocalTime startTime, LocalTime openTime, LocalTime closeTime) {
        validateOmTime(startTime, openTime, closeTime);
        startTimeMin = startTime.getHour() * 60 + startTime.getMinute();
        int openTimeMin = openTime.getHour() * 60 + openTime.getMinute();
        int closeTimeMin = closeTime.getHour() * 60 + closeTime.getMinute();

        if (closeTimeMin < startTimeMin && startTimeMin < openTimeMin) {
            this.closeTimeMin = closeTimeMin + 24 * 60;
            this.openTimeMin = openTimeMin;
        } else {
            this.closeTimeMin = closeTimeMin;
            this.openTimeMin = openTimeMin;
        }
    }

    private void validateOmTime(LocalTime startTime, LocalTime openTime, LocalTime closeTime) {
        if (startTime == null || openTime == null || closeTime == null) {
            throw new NullPointerException("经营起始时间、开业时间、闭店时间均不能为空");
        }
        if ((openTime.isBefore(closeTime) && startTime.isAfter(openTime) && startTime.isBefore(closeTime)) ||
                (openTime.isAfter(closeTime) && (startTime.isAfter(openTime) || startTime.isBefore(closeTime)))) {
            throw new IllegalArgumentException("经营起始时间不能在开业时间和闭店时间之间");
        }
    }

    public boolean isPreparing(LocalTime localTime) {
        int localTimeMin = parse(localTime);
        return (localTimeMin <= startTimeMin && localTimeMin + 24 * 60 < openTimeMin) || (localTimeMin >= startTimeMin && localTimeMin < openTimeMin);
    }

    public boolean isResting(LocalTime localTime) {
        int localTimeMin = parse(localTime);
        return (localTimeMin >= closeTimeMin && localTimeMin > startTimeMin) || (localTimeMin + 24 * 60 >= closeTimeMin && localTimeMin < startTimeMin);
    }

    public boolean isOpening(LocalTime localTime) {
        int localTimeMin = parse(localTime);
        return (localTimeMin + 24 * 60 >= openTimeMin && localTimeMin + 24 * 60 < closeTimeMin) || (localTimeMin >= openTimeMin && localTimeMin < closeTimeMin);
    }

    public LocalDate getBizDay(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            localDateTime = LocalDateTime.now();
        }
        int localTimeMin = parse(localDateTime.toLocalTime());
        if (localTimeMin < startTimeMin) {
            return localDateTime.minusDays(1).toLocalDate();
        }
        return localDateTime.toLocalDate();
    }

    private int parse(LocalTime localTime) {
        if (localTime == null) {
            localTime = LocalTime.now();
        }
        return localTime.getHour() * 60 + localTime.getMinute();
    }

    public int getPointSize(int bizIntervalMin) {
        int adjOpenTimeMin = openTimeMin, adjCloseTimeMin = closeTimeMin;
        if (openTimeMin % bizIntervalMin > 0) {
            adjOpenTimeMin -= adjOpenTimeMin % bizIntervalMin;
        }
        if (closeTimeMin % bizIntervalMin > 0) {
            adjCloseTimeMin += bizIntervalMin - adjCloseTimeMin % bizIntervalMin;
        }
        return (adjCloseTimeMin - adjOpenTimeMin) / bizIntervalMin;
    }

    /**
     * @param bizDay               业务口径天
     * @param bizIntervalMin
     * @param calculateIntervalMin
     * @return
     */
    public List<LocalDateTime> getHistoryPoints(LocalDate bizDay, int bizIntervalMin, int calculateIntervalMin) {
        validateInterval(bizIntervalMin, calculateIntervalMin);
        List<LocalDateTime> result = new ArrayList<>();
        // 业务口径天为空，默认等于当前天
        if (bizDay == null || !bizDay.isBefore(getBizDay(null))) {
            return result;
        }

        // 获取历史某天的所有点
        int adjOpenTimeMin = openTimeMin;
        if (openTimeMin % bizIntervalMin > 0) {
            adjOpenTimeMin -= adjOpenTimeMin % bizIntervalMin;
        }
        int intervalTimes = getPointSize(bizIntervalMin);

        for (int i = 0; i < intervalTimes; i++) {
            int min = adjOpenTimeMin + (i + 1) * bizIntervalMin - calculateIntervalMin;
            if (min >= 24 * 60) {
                min = min - 24 * 60;
                result.add(LocalDateTime.of(bizDay.plusDays(1), LocalTime.of(min / 60, min % 60)));
            } else {
                result.add(LocalDateTime.of(bizDay, LocalTime.of(min / 60, min % 60)));
            }
        }
        return result;
    }

    public List<LocalDateTime> getCurrentPoints(LocalTime localTime, int bizIntervalMin, int calculateIntervalMin) {
        validateInterval(bizIntervalMin, calculateIntervalMin);
        List<LocalDateTime> result = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        LocalDate nowDay = now.toLocalDate();
        LocalTime nowTime = now.toLocalTime();
        if (isPreparing(nowTime) || isResting(nowTime)) {
            return result;
        }

        // 校准localTime
        int localTimeMin;
        if (localTime == null || !isOpening(localTime)) {
            localTimeMin = openTimeMin;
        } else {
            localTimeMin = parse(localTime);
        }

        int nowMin = parse(nowTime);
        if (localTimeMin < openTimeMin) {
            localTimeMin += 24 * 60;
        }
        if (nowMin < openTimeMin) {
            nowMin += 24 * 60;
        }
        if (nowMin < localTimeMin) {
            localTimeMin = nowMin;
        }
        int adjNowMin = nowMin - nowMin % bizIntervalMin;
        int adjLocalTimeMin = localTimeMin - localTimeMin % bizIntervalMin;
        int margin = (adjNowMin - adjLocalTimeMin) / bizIntervalMin;

        // 当前时间在开业时间段内
        for (int i = 0; i < margin; i++) {
            int min = adjLocalTimeMin + (i + 1) * bizIntervalMin - calculateIntervalMin;
            if (min >= 24 * 60) {
                min = min - 24 * 60;
                result.add(LocalDateTime.of(nowDay.plusDays(1), LocalTime.of(min / 60, min % 60)));
            } else {
                result.add(LocalDateTime.of(nowDay, LocalTime.of(min / 60, min % 60)));
            }
        }
        // 计算最后一个时间点
        int lastCalculatePointMin = now.getMinute();
        if (lastCalculatePointMin % calculateIntervalMin > 0) {
            lastCalculatePointMin -= lastCalculatePointMin % calculateIntervalMin;
        }
        result.add(LocalDateTime.of(now.toLocalDate(), LocalTime.of(now.getHour(), lastCalculatePointMin)));

        return result;
    }

    private void validateInterval(int bizIntervalMin, int calculateIntervalMin) {
        if (bizIntervalMin < MINIMAL_INTERVAL_MIN || calculateIntervalMin < MINIMAL_INTERVAL_MIN ||
                bizIntervalMin > MAXIMAL_INTERVAL_MIN || calculateIntervalMin > MAXIMAL_INTERVAL_MIN) {
            throw new IllegalArgumentException("间隔时间设置必须大于等于" + MINIMAL_INTERVAL_MIN + "分钟且小于等于" + MAXIMAL_INTERVAL_MIN + "分钟");
        }
        if (60 % bizIntervalMin > 0 || 60 % calculateIntervalMin > 0) {
            throw new IllegalArgumentException("间隔时间设置必须能被60整除");
        }
        if (bizIntervalMin % calculateIntervalMin > 0 || bizIntervalMin == calculateIntervalMin) {
            throw new IllegalArgumentException("业务间隔时间必须是计算间隔时间一倍以上的整数倍");
        }
    }

}
