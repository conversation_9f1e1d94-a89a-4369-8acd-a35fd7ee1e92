package com.sankuai.shangou.logistics.delivery.shippingarea.service;

import com.dianping.cat.Cat;
import com.dianping.lion.common.util.JsonUtils;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Assert;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.DistrictDto;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.map.open.platform.api.MapOpenApiService;

import com.sankuai.meituan.gis.remote.service.thrift.TAdminDivisionService;
import com.sankuai.meituan.gis.remote.vo.thrift.TAdAPIResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PermissionGroupVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.*;
import com.sankuai.sgxsupply.wxmall.infrastructure.sdk.operation.OpLog;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.logistics.delivery.shippingarea.constant.Const;
import com.sankuai.shangou.logistics.delivery.shippingarea.convert.Convert;
import com.sankuai.shangou.logistics.delivery.shippingarea.domain.ChannelPoiShippingInfo;
import com.sankuai.shangou.logistics.delivery.shippingarea.domain.ShippingAreaUpdateMessage;
import com.sankuai.shangou.logistics.delivery.shippingarea.request.ResetShippingAreaRequest;
import com.sankuai.shangou.logistics.delivery.shippingarea.dto.ShippingAreaRelationDto;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.ChannelPoiShippingAreaOperationEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.StatusEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.mq.MafkaMessageProducer;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.IGeoUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.MccUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.ShippingUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.UUIDUtil;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.AdministrativeTreeVO;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.CityShippingAreaInfoVO;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.ElectrobikeRouteVO;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.PoiChannelDeliveryInfoVO;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.request.ElectricBikeRouteParam;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.ChannelTypeEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.request.BatchUpsertChannelPoiShippingParam;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.request.DeleteChannelPoiShippingParam;
import com.sankuai.shangou.logistics.delivery.shippingarea.wrapper.OCMSChannelPoiShippingServiceWrapper;
import com.sankuai.wmarch.map.thriftClient.route.Destination;
import com.sankuai.wmarch.map.thriftClient.route.Origin;
import com.sankuai.wmarch.map.thriftClient.route.RouteRequest;
import com.sankuai.wmarch.map.thriftClient.route.RouteResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.locationtech.jts.geom.Polygon;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/14 20:30
 **/
@Slf4j
@Service
public class ShippingAreaService {
    @Resource
    private TAdminDivisionService.Iface tAdminDivisionService;

    @Resource
    private AuthThriftService.Iface authThriftService;

    @Resource
    private PoiThriftService poiThriftService;

    @Resource
    private ChannelPoiService channelPoiService;

    @Resource
    private OCMSChannelPoiShippingServiceWrapper ocmsChannelPoiShippingServiceWrapper;

    @Resource
    private ShippingAreaRelationService shippingAreaRelationService;

    @Resource
    private ShippingAreaNotifyService shippingAreaNotifyService;

    @Resource
    private MapOpenApiService.Iface mapOpenApiService;

    @Resource
    MafkaMessageProducer<ShippingAreaUpdateMessage> shippingAreaUpdateMessageProducer;

    /**
     * 获取有权限的城市和门店列表
     * @return AdministrativeTreeVO
     */
    public AdministrativeTreeVO getAdministrativeTree(Long tenantId, Long accountId) {
        // 获取当前用户拥有权限的门店
        List<PoiInfoDto> poiListAuthorized = queryPoiListAuthorized(tenantId, accountId);
        if (CollectionUtils.isEmpty(poiListAuthorized)) {
            return null;
        }
        AdministrativeTreeVO all = new AdministrativeTreeVO(String.valueOf(Const.NATION_CODE), "全国", "");
        Map<Integer, DistrictDto> districtDtoMap = new HashMap<>();
        poiListAuthorized.stream()
                .map(PoiInfoDto::getDistrict)
                .forEach(district -> districtDtoMap.put(district.getCityId(), district));
        log.info("all cities: {}", JsonUtils.toJson(districtDtoMap));

        Map<Integer, AdministrativeTreeVO> provinceItems = new TreeMap<>();
        try {
            List<TAdAPIResponse> cityAdInfoList =
                    tAdminDivisionService.findByIds(new ArrayList<>(districtDtoMap.keySet()));
            for (TAdAPIResponse cityAdInfo : cityAdInfoList) {
                if (districtDtoMap.containsKey(cityAdInfo.getId())) {
                    DistrictDto districtDto = districtDtoMap.get(cityAdInfo.getId());
                    AdministrativeTreeVO provinceItem = provinceItems.getOrDefault(districtDto.getProvinceId(),
                            new AdministrativeTreeVO(String.valueOf(districtDto.getProvinceId()),
                                    districtDto.getProvinceName(), ""));
                    provinceItem.addItem(new AdministrativeTreeVO(String.valueOf(cityAdInfo.getId()),
                            cityAdInfo.getChineseName(), cityAdInfo.getAdCode()));
                    provinceItems.put(districtDto.getProvinceId(), provinceItem);
                }
            }

            provinceItems.values().forEach(all::addItem);
            return all;
        } catch (TException e) {
            log.error("get administrative tree error", e);
            throw new ThirdPartyException("getAdministrativeTree TException, ", e);
        }
    }


    /**
     * 获取城市下所有门店的配送范围(合并后)
     * @param cityId 城市id
     * @return 城市配送范围信息
     * @throws TException
     */
    public CityShippingAreaInfoVO getCityDeliveryInfo(Integer cityId, Long tenantId, Long accountId) throws TException {
        // 从百川拿到该城市下所有门店列表
        List<PoiDetailInfoDTO> poiDetailInfoDTOList =
                channelPoiService.queryPoiInfoListUnderCity(tenantId, cityId);
        if (CollectionUtils.isEmpty(poiDetailInfoDTOList)) {
            return new CityShippingAreaInfoVO(cityId, Collections.emptyList());
        }

        //校验权限
        List<Long> permissionPoiIdList = queryPermissionInfoOfCurrentUser(tenantId, accountId);
        Bssert.throwIfTrue(CollectionUtils.isEmpty(permissionPoiIdList), "无该城市权限");
        Bssert.throwIfTrue(!CollectionUtils.containsAny(permissionPoiIdList,
                Optional.of(poiDetailInfoDTOList).orElse(Collections.emptyList()).stream().map(PoiDetailInfoDTO::getPoiId)
                        .collect(Collectors.toList())), "无该城市权限");

        //获取该城市下所有门店的配送范围
        Pair<Map<Long, Polygon>, Map<Long, Polygon>> mergedAreasMap = queryChannelPoiShippingInfoList(poiDetailInfoDTOList, tenantId);


        return Convert.buildCityShippingAreaInfoVO(cityId, poiDetailInfoDTOList, permissionPoiIdList, mergedAreasMap);
    }


    public PoiChannelDeliveryInfoVO getPoiChannelDeliveryInfo(Long storeId, Long tenantId) {
        if (Objects.isNull(tenantId)) {
            throw new IllegalArgumentException("获取用户信息失败,请先登陆");
        }

        PoiDetailInfoDTO poiDetailInfoDTO = channelPoiService.queryPoiInfoByPoiId(tenantId, storeId);
        if (Objects.isNull(poiDetailInfoDTO)) {
            log.warn("no such storeId:{}", storeId);
            return null;
        }

        //查询美团渠道配送范围
        List<PoiShippingAreaInfoDTO> mtShippingInfoList = ocmsChannelPoiShippingServiceWrapper.getChannelPoiShippingInfoList(tenantId, poiDetailInfoDTO, ChannelTypeEnum.MEITUAN.getChannelId());

        //查询微商城渠道配送范围
        List<PoiShippingAreaInfoDTO> dhShippingInfoList = ocmsChannelPoiShippingServiceWrapper.getChannelPoiShippingInfoList(tenantId, poiDetailInfoDTO, ChannelTypeEnum.MT_DRUNK_HORSE.getChannelId());

        ArrayList<Long> shippingAreaIdList = new ArrayList<>();
        shippingAreaIdList.addAll(Fun.map(mtShippingInfoList, PoiShippingAreaInfoDTO::getShippingAreaId));
        shippingAreaIdList.addAll(Fun.map(dhShippingInfoList, PoiShippingAreaInfoDTO::getShippingAreaId));
        List<ShippingAreaRelationDto> shippingAreaRelationDtos =
                shippingAreaRelationService.queryShippingAreaByShippingAreaIds(shippingAreaIdList);


        return Convert.buildPoiChannelDeliveryInfoVO(poiDetailInfoDTO, mtShippingInfoList, dhShippingInfoList, shippingAreaRelationDtos);
    }


    @OpLog(BussinessId = "waima-regionselect", SubType = "poi-shipping", OpType = "deleteShipping",OpUser =
            "{T(com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder).getWebLoginContext().getLoginUser().getAccountId()}", detail =
            "{#request}", EntityId = "{#request.storeId}")
    public void deleteChannelPoiShipping(DeleteChannelPoiShippingParam request, Long tenantId, Long accountId,
                                         String operatorAccount, String operatorUserName) {
        if (Objects.isNull(tenantId)) {
            throw new IllegalArgumentException("获取用户信息失败,请先登陆");
        }

        checkPoiPermission(request.getStoreId(), tenantId, accountId);
        PoiDetailInfoDTO poiDetailInfoDTO = channelPoiService.queryPoiInfoByPoiId(tenantId, request.getStoreId());
        Bssert.throwIfNull(poiDetailInfoDTO, "未知门店");

        List<Long> failList = new ArrayList<>();
        StringBuilder errorMsgBuilder = new StringBuilder();

        List<Long> needDeleteAreaIds = request.getChannelIdShippingAreaIdMap().values().stream().flatMap(Collection::stream)
                .map(DeleteChannelPoiShippingParam.DeleteShippingAreaInfo::getShippingAreaId)
                .collect(Collectors.toList());

        List<PoiShippingAreaInfoDTO> successDeleteAreaInfoList = new ArrayList<>();

        //获取areaId对应的面积
        Map<Long, Double> areaId2OldAreaMap = request.getChannelIdShippingAreaIdMap().values().stream().flatMap(Collection::stream)
                        .collect(Collectors.toMap(DeleteChannelPoiShippingParam.DeleteShippingAreaInfo::getShippingAreaId, areaInfo -> Double.parseDouble(areaInfo.getOldDeliveryArea())));

        request.getChannelIdShippingAreaIdMap().forEach((channelId, shippingAreaInfoList) -> {
            //查询原来的配送范围
            List<PoiShippingAreaInfoDTO> channelPoiShippingInfoList = ocmsChannelPoiShippingServiceWrapper.getChannelPoiShippingInfoList(tenantId, poiDetailInfoDTO, channelId);
            Map<Long, PoiShippingAreaInfoDTO> oldShippingAreaMap = channelPoiShippingInfoList
                    .stream()
                    .collect(Collectors.toMap(PoiShippingAreaInfoDTO::getShippingAreaId, Function.identity()));

            if (CollectionUtils.isEmpty(channelPoiShippingInfoList) ) {
                throw new BizException("需要删除的配送范围不存在, 请刷新页面");
            }

            Optional<Long> notExistShippingAreaIdOpt = shippingAreaInfoList.stream()
                    .map(DeleteChannelPoiShippingParam.DeleteShippingAreaInfo::getShippingAreaId)
                    .filter(areaId -> !oldShippingAreaMap.containsKey(areaId))
                    .findAny();

            if (notExistShippingAreaIdOpt.isPresent()) {
                log.warn("shipping area is not exits, areaId:{}", notExistShippingAreaIdOpt.get());
                throw new BizException("需要删除的配送范围不存在, 请刷新页面");
            }

            //删除配送范围
            shippingAreaInfoList.forEach(needDeleteShippingAreaInfo -> {
                try {
                    Long needDeleteShippingAreaId = needDeleteShippingAreaInfo.getShippingAreaId();

                    ocmsChannelPoiShippingServiceWrapper.deleteChannelShippingArea(tenantId, request.getStoreId(), channelId, needDeleteShippingAreaId);

                    successDeleteAreaInfoList.add(oldShippingAreaMap.get(needDeleteShippingAreaId));

                    //删除关联关系
                    shippingAreaRelationService.deleteShippingAreaRelations(tenantId, request.getStoreId(), Collections.singletonList(needDeleteShippingAreaId));

                } catch (Exception e) {
                    failList.add(needDeleteShippingAreaInfo.getShippingAreaId());
                    errorMsgBuilder.append(e.getMessage()).append("\n");
                    log.warn("delete shipping area fail, shippingAreaId:{}", needDeleteShippingAreaInfo.getShippingAreaId(), e);
                }
            });
        });


        //发送大象通知
        if (CollectionUtils.isNotEmpty(successDeleteAreaInfoList)) {
            shippingAreaNotifyService.deleteShippingNotify(poiDetailInfoDTO, successDeleteAreaInfoList, areaId2OldAreaMap,
                    operatorAccount, operatorUserName);
        }

        //部分失败
        if (CollectionUtils.isNotEmpty(failList) && failList.size() < needDeleteAreaIds.size()) {
            throw new BizException("删除配送范围部分失败: " + errorMsgBuilder);
        }

        //全部失败
        if (CollectionUtils.isNotEmpty(failList) && failList.size() == needDeleteAreaIds.size()) {
            throw new BizException("删除配送范围失败: " + errorMsgBuilder);
        }
    }

    @OpLog(BussinessId = "waima-regionselect", SubType = "poi-shipping", OpType = "updateShipping",OpUser =
            "{T(com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder).getWebLoginContext().getLoginUser().getAccountId()}", detail =
            "{#request}", EntityId = "{#request.storeId}")
    public void batchUpdateChannelPoiRegularPeriodShipping(BatchUpsertChannelPoiShippingParam request, Long tenantId,
                                                           Long accountId, String operatorAccount, String operatorUserName) {
        if (Objects.isNull(tenantId)) {
            throw new IllegalArgumentException("获取用户信息失败,请先登陆");
        }
        checkPoiPermission(request.getStoreId(), tenantId, accountId);
        PoiDetailInfoDTO poiDetailInfoDTO = channelPoiService.queryPoiInfoByPoiId(tenantId, request.getStoreId());
        Bssert.throwIfNull(poiDetailInfoDTO, "未知门店");

        //查询美团渠道配送范围
        List<PoiShippingAreaInfoDTO> mtShippingInfoList = ocmsChannelPoiShippingServiceWrapper.getChannelPoiShippingInfoList(tenantId, poiDetailInfoDTO, ChannelTypeEnum.MEITUAN.getChannelId());

        //查询微商城渠道配送范围
        List<PoiShippingAreaInfoDTO> dhShippingInfoList = ocmsChannelPoiShippingServiceWrapper.getChannelPoiShippingInfoList(tenantId, poiDetailInfoDTO, ChannelTypeEnum.MT_DRUNK_HORSE.getChannelId());

        Map<Integer, List<PoiShippingAreaInfoDTO>> channelId2ShippingListMap = new HashMap<>();
        channelId2ShippingListMap.put(ChannelTypeEnum.MEITUAN.getChannelId(), mtShippingInfoList);
        channelId2ShippingListMap.put(ChannelTypeEnum.MT_DRUNK_HORSE.getChannelId(), dhShippingInfoList);


        List<ChannelPoiShippingInfo> oldShippingList =
                Convert.toOldChannelPoiRegularPeriodShippingList(request, channelId2ShippingListMap);

        List<ChannelPoiShippingInfo> newShippingList =
                Convert.toNewChannelPoiRegularPeriodShippingList(request, channelId2ShippingListMap);


        List<Exception> errors = Lists.newArrayList();
        newShippingList.forEach(newShippingArea -> {
            try {
                Long newAreaId = null;
                switch (newShippingArea.getOperate()) {
                    case NEW:
                        newAreaId = ocmsChannelPoiShippingServiceWrapper.updateRegularPeriodShippingArea(buildUpsertRequest(newShippingArea, tenantId));
                        newShippingArea.setShippingAreaId(newAreaId);
                        break;
                    case UPDATE:
                        ocmsChannelPoiShippingServiceWrapper.updateRegularPeriodShippingArea(buildUpsertRequest(newShippingArea, tenantId));
                        break;
                    case REPLACE:
                        newAreaId = ocmsChannelPoiShippingServiceWrapper.updateRegularPeriodShippingArea(buildUpsertRequest(newShippingArea, tenantId));
                        ocmsChannelPoiShippingServiceWrapper.deleteChannelShippingArea(buildDeleteNewShippingArea(newShippingArea, tenantId));
                        newShippingArea.setOldAreaId(newShippingArea.getShippingAreaId());
                        newShippingArea.setShippingAreaId(newAreaId);
                        break;
                    default:
                        log.warn("no valid operate");
                }
            } catch (Exception e) {
                log.error("sync shipping fail: {}", JsonUtils.toJson(newShippingArea), e);
                errors.add(e);
            }
        });

        // throw first error
        Bssert.throwIfTrue(CollectionUtils.isNotEmpty(errors),
                errors.stream().findFirst().orElse(new com.sankuai.shangou.commons.exception.common.exceptions.BizException("渠道配送范围同步失败")).getMessage());

        updateShippingAreaRelation(tenantId, oldShippingList, newShippingList);

        //推送配送范围变更
        shippingAreaNotifyService.updateRegularPeriodShippingNotify(poiDetailInfoDTO, oldShippingList, newShippingList,
                operatorAccount, operatorUserName);

        //推送变更消息
        newShippingList.forEach(newArea -> {
            if (Objects.equals(newArea.getOperate(), ChannelPoiShippingAreaOperationEnum.NEW)) {
                shippingAreaUpdateMessageProducer.sendMessage(new ShippingAreaUpdateMessage(tenantId,  request.getStoreId(), 1));
            }

            if (Objects.equals(newArea.getOperate(), ChannelPoiShippingAreaOperationEnum.UPDATE)) {
                shippingAreaUpdateMessageProducer.sendMessage(new ShippingAreaUpdateMessage(tenantId,  request.getStoreId(), 2));
            }

        });

    }

    @OpLog(BussinessId = "waima-regionselect", SubType = "poi-shipping", OpType = "updateShipping",OpUser =
            "{T(com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder).getWebLoginContext().getLoginUser().getAccountId()}", detail =
            "{#request}", EntityId = "{#request.storeId}")
    public void batchUpdateChannelPoiSpecialPeriodShipping(BatchUpsertChannelPoiShippingParam request,
                                                           Long tenantId, Long accountId,
                                                           String operatorAccount, String operatorUserName) {
        if (Objects.isNull(tenantId)) {
            throw new IllegalArgumentException("获取用户信息失败,请先登陆");
        }
        checkPoiPermission(request.getStoreId(), tenantId, accountId);
        PoiDetailInfoDTO poiDetailInfoDTO = channelPoiService.queryPoiInfoByPoiId(tenantId, request.getStoreId());
        Bssert.throwIfNull(poiDetailInfoDTO, "未知门店");

        Map<Integer, ChannelPoiInfoDTO> channel2channelPoi =
                poiDetailInfoDTO.getChannelPoiInfoList().stream()
                        .collect(Collectors.toMap(ChannelPoiInfoDTO::getChannelId, Function.identity()));

        //查询美团渠道配送范围
        List<PoiShippingAreaInfoDTO> mtShippingInfoList = ocmsChannelPoiShippingServiceWrapper.getChannelPoiShippingInfoList(tenantId, poiDetailInfoDTO, ChannelTypeEnum.MEITUAN.getChannelId());

        //查询微商城渠道配送范围
        List<PoiShippingAreaInfoDTO> dhShippingInfoList = ocmsChannelPoiShippingServiceWrapper.getChannelPoiShippingInfoList(tenantId, poiDetailInfoDTO, ChannelTypeEnum.MT_DRUNK_HORSE.getChannelId());

        Map<Integer, List<PoiShippingAreaInfoDTO>> channelId2ShippingListMap = new HashMap<>();
        channelId2ShippingListMap.put(ChannelTypeEnum.MEITUAN.getChannelId(), mtShippingInfoList);
        channelId2ShippingListMap.put(ChannelTypeEnum.MT_DRUNK_HORSE.getChannelId(), dhShippingInfoList);


        List<ChannelPoiShippingInfo> oldShippingList =
                Convert.toOldChannelPoiSpecialPeriodShippingList(request, channelId2ShippingListMap);

        List<ChannelPoiShippingInfo> newShippingList =
                Convert.toNewChannelPoiSpecialPeriodShippingList(request, channelId2ShippingListMap);

        List<Exception> errors = Lists.newArrayList();
        newShippingList.forEach(newShippingArea-> {
            try {
                Long newAreaId = null;
                Bssert.throwIfTrue(!channel2channelPoi.containsKey(newShippingArea.getChannelId()),
                        "缺失渠道对应门店: ", newShippingArea.getChannelId());
                switch (newShippingArea.getOperate()) {
                    case NEW:
                        newAreaId = ocmsChannelPoiShippingServiceWrapper.updateSpecialPeriodShippingArea(buildSpecialShippingUpsertRequest(newShippingArea, tenantId));
                        newShippingArea.setShippingAreaId(newAreaId);
                        break;
                    case UPDATE:
                        ocmsChannelPoiShippingServiceWrapper.updateSpecialPeriodShippingArea(buildSpecialShippingUpsertRequest(newShippingArea, tenantId));
                        break;
                    case REPLACE:
                        newAreaId = ocmsChannelPoiShippingServiceWrapper.updateSpecialPeriodShippingArea(buildSpecialShippingUpsertRequest(newShippingArea, tenantId));
                        ocmsChannelPoiShippingServiceWrapper.deleteChannelShippingArea(buildDeleteNewShippingArea(newShippingArea, tenantId));
                        newShippingArea.setOldAreaId(newShippingArea.getShippingAreaId());
                        newShippingArea.setShippingAreaId(newAreaId);
                        break;
                    default:
                        log.warn("no valid operate");
                }
            } catch (Exception e) {
                log.error("sync shipping fail: {}", JsonUtils.toJson(newShippingArea), e);
                errors.add(e);
            }
        });

        // throw first error
        Bssert.throwIfTrue(CollectionUtils.isNotEmpty(errors),
                errors.stream().findFirst().orElse(new BizException("渠道配送范围同步失败")).getMessage());

        //只更新时间,不更新关联关系
        if (!Objects.equals(request.getOnlyUpdatePeriodTime(), true)) {
            updateShippingAreaRelation(tenantId, oldShippingList, newShippingList);
        }

        //推送配送范围变更
        shippingAreaNotifyService.updateSpecialPeriodShippingNotify(poiDetailInfoDTO, oldShippingList,
                newShippingList, operatorAccount, operatorUserName);
    }

    public void resetChannelPoiShippingArea(ResetShippingAreaRequest request) {
        //查门店信息
        PoiDetailInfoDTO poiDetailInfoDTO = channelPoiService.queryPoiInfoByPoiId(request.getTenantId(), request.getStoreId());
        Bssert.throwIfNull(poiDetailInfoDTO, "未知门店");

        //重置美团渠道的配送范围
        Optional<ResetShippingAreaRequest.ChannelShippingAreaParam> mtChannelRegionDTO = request.getChannelRegionDTOS()
                .stream()
                .filter(dto -> Objects.equals(dto.getChannelId(), ChannelTypeEnum.MEITUAN.getChannelId()))
                .findAny();
        mtChannelRegionDTO.ifPresent(channelShippingAreaParam -> ocmsChannelPoiShippingServiceWrapper.resetShippingArea(buildResetShippingRequest(request.getTenantId(), request.getStoreId(),
                ChannelTypeEnum.MEITUAN.getChannelId(), channelShippingAreaParam)));

        //重置微商城渠道的配送范围
        Optional<ResetShippingAreaRequest.ChannelShippingAreaParam> dhChannelRegionDTO = request.getChannelRegionDTOS()
                .stream()
                .filter(dto -> Objects.equals(dto.getChannelId(), ChannelTypeEnum.MT_DRUNK_HORSE.getChannelId()))
                .findAny();
        dhChannelRegionDTO.ifPresent(channelShippingAreaParam -> ocmsChannelPoiShippingServiceWrapper.resetShippingArea(buildResetShippingRequest(request.getTenantId(),
                request.getStoreId(), ChannelTypeEnum.MT_DRUNK_HORSE.getChannelId(), channelShippingAreaParam)));

        //推送配送范围变更
        shippingAreaNotifyService.resetShippingNotify(poiDetailInfoDTO, Convert.toResetShippingList(request));

        //异步去重置配送范围关联关系
        shippingAreaUpdateMessageProducer.sendMessage(new ShippingAreaUpdateMessage(request.getTenantId(), request.getStoreId(),
                ChannelPoiShippingAreaOperationEnum.RESET.getCode()));

    }

    public ElectrobikeRouteVO getElectricBikeRoutePlan(ElectricBikeRouteParam electrobikeRouteRequest,
                                                       String serialId) {
        RouteRequest routeRequest = new RouteRequest();
        routeRequest.setKey(MccUtils.getMapOpenPlatformApiAuthorizeKey());
        Origin origin = new Origin();
        origin.setLocation(electrobikeRouteRequest.getOriginCoordinate());
        routeRequest.setOrigin(origin);
        Destination destination = new Destination();
        destination.setLocation(electrobikeRouteRequest.getDestinationCoordinate());
        routeRequest.setDestination(destination);

        try {
            RouteResponse response = mapOpenApiService.electrobike(routeRequest);
            log.info("invoke mapOpenApiService.electrobike, request= {}, response = {}",
                    JsonUtils.toJson(routeRequest), JsonUtils.toJson(response));
            Bssert.throwIfNull(response, "未能获取到接口状态");

            //如果是可忽略的异常，则上报cat 不报错
            if (Const.CAN_BE_IGNORED_MAP_OPEN_API_ERROR_CODES.contains(response.getStatus())) {
                Cat.logEvent("mapOpenApiService.electrobike", String.valueOf(response.getStatus()));
                return null;
            }

            Bssert.throwIfTrue(response.getStatus() != Const.MAP_OPEN_API_SUCCESS_CODE,
                    StringUtils.defaultIfBlank(response.getMsg(), "接口返回错误"));

            if (CollectionUtils.isEmpty(response.getRoute())) {
                log.warn("no electrobike route resp");
                return null;
            }

            return Convert.toVO(response.getRoute().get(0), serialId);

        } catch (TException e) {
            log.error("get electrobike routeplan fail, args:{}", JsonUtils.toJson(routeRequest));
            throw new ThirdPartyException("请求地图开放平台-电单车路径规划接口异常", e);
        }
    }

    /**
     * 维护配送范围的关联关系
     * @param newChannelPoiShippingInfos
     */
    private void updateShippingAreaRelation(Long tenantId,
                                            List<ChannelPoiShippingInfo> oldChannelPoiShippingInfos,
                                            List<ChannelPoiShippingInfo> newChannelPoiShippingInfos) {

        if (CollectionUtils.isEmpty(newChannelPoiShippingInfos)) {
            return;
        }

        List<Long> shippingAreaIds = newChannelPoiShippingInfos
                .stream()
                .map(ChannelPoiShippingInfo::getShippingAreaId)
                .collect(Collectors.toList());

        Long storeId = newChannelPoiShippingInfos.get(0).getStoreId();

        Map<Long, ChannelPoiShippingInfo> oldShippingAreaMap = oldChannelPoiShippingInfos.stream()
                .collect(Collectors.toMap(ChannelPoiShippingInfo::getShippingAreaId, Function.identity()));

        //同时更新两个范围 && 渠道不同 && 配送范围相同
        if (newChannelPoiShippingInfos.size() == 2
                && !Objects.equals(newChannelPoiShippingInfos.get(0).getChannelId(), newChannelPoiShippingInfos.get(1).getChannelId())
                && Objects.equals(newChannelPoiShippingInfos.get(0).getDeliveryRegion(), newChannelPoiShippingInfos.get(1).getDeliveryRegion())) {
            //1. 两个配送范围都是新增的 ==> 插入新的关联关系
            if (Objects.equals(newChannelPoiShippingInfos.get(0).getOperate(), ChannelPoiShippingAreaOperationEnum.NEW)
                    && Objects.equals(newChannelPoiShippingInfos.get(1).getOperate(), ChannelPoiShippingAreaOperationEnum.NEW)) {
                shippingAreaRelationService.addShippingAreaRelations(buildShippingAreaRelations(newChannelPoiShippingInfos, tenantId));
            }

            //否则 删除原有的相关的关联关系, 插入新的的关联关系
            shippingAreaRelationService.deleteShippingAreaRelations(tenantId, storeId, shippingAreaIds);
            shippingAreaRelationService.addShippingAreaRelations(buildShippingAreaRelations(newChannelPoiShippingInfos, tenantId));
        }

        //只更新一个渠道的范围
        if (newChannelPoiShippingInfos.size() == 1) {
            ChannelPoiShippingInfo newShippingArea = newChannelPoiShippingInfos.get(0);
            //如果是改了标签,删除原来areaId的关联关系，否则直接删除现在的areaId的关联关系
            if (Objects.equals(newShippingArea.getOperate(), ChannelPoiShippingAreaOperationEnum.REPLACE)) {
                shippingAreaRelationService.deleteShippingAreaRelations(tenantId, storeId, Collections.singletonList(newShippingArea.getOldAreaId()));
            } else {
                shippingAreaRelationService.deleteShippingAreaRelations(tenantId, storeId, Collections.singletonList(newShippingArea.getShippingAreaId()));
            }
        }

    }

    private Boolean regionHasChanged(ChannelPoiShippingInfo newShippingArea, ChannelPoiShippingInfo oldShippingArea) {
        if (newShippingArea == null || oldShippingArea == null) {
            return false;
        }

        Polygon oldRegion = IGeoUtils.convertDeliveryArea(IGeoUtils.createCoordinateDtoList(oldShippingArea.getDeliveryRegion()));
        Polygon newRegion = IGeoUtils.convertDeliveryArea(IGeoUtils.createCoordinateDtoList(newShippingArea.getDeliveryRegion()));
        return !Objects.equals(oldRegion, newRegion);
    }

    private List<ShippingAreaRelationDto> buildShippingAreaRelations(List<ChannelPoiShippingInfo> channelPoiShippingInfos, Long tenantId) {
        //创建relationId
        Long relationId = UUIDUtil.uuidToLong(UUID.randomUUID());

        return channelPoiShippingInfos.stream().map(area -> {
            ShippingAreaRelationDto relationDto = new ShippingAreaRelationDto();
            relationDto.setRelationId(relationId);
            relationDto.setAreaId(area.getShippingAreaId());
            relationDto.setStoreId(area.getStoreId());
            relationDto.setTenantId(tenantId);
            relationDto.setChannelId(area.getChannelId());
            relationDto.setStatus(StatusEnum.VALID.getCode());
            relationDto.setCreateTime(LocalDateTime.now());

            return relationDto;
        }).collect(Collectors.toList());
    }


    private UpdatePoiRegularPeriodShippingByShippingAreaIdRequest buildUpsertRequest(ChannelPoiShippingInfo newShippingArea, Long tenantId) {
        UpdatePoiRegularPeriodShippingByShippingAreaIdRequest updateRequest =
                new UpdatePoiRegularPeriodShippingByShippingAreaIdRequest(tenantId, newShippingArea.getStoreId(),
                        newShippingArea.getChannelId(),
                        newShippingArea.getMinPrice(),
                        newShippingArea.getShippingFee(),
                        IGeoUtils.createCoordinateDtoList(newShippingArea.getDeliveryRegion()));

        updateRequest.setAppShippingCode(newShippingArea.getAppShippingCode());
        if (Objects.equals(newShippingArea.getOperate(), ChannelPoiShippingAreaOperationEnum.REPLACE)) {
            return updateRequest;
        }

        if(newShippingArea.getShippingAreaId() != null) {
            updateRequest.setShippingAreaId(newShippingArea.getShippingAreaId());
        }
        return updateRequest;
    }

    private UpdatePoiSpecialPeriodShippingByShippingAreaIdRequest buildSpecialShippingUpsertRequest(ChannelPoiShippingInfo newShippingArea, Long tenantId) {
        UpdatePoiSpecialPeriodShippingByShippingAreaIdRequest updateRequest =
                new UpdatePoiSpecialPeriodShippingByShippingAreaIdRequest(tenantId, newShippingArea.getStoreId(),
                        newShippingArea.getChannelId(),
                        newShippingArea.getMinPrice(),
                        newShippingArea.getShippingFee(),
                        IGeoUtils.createCoordinateDtoList(newShippingArea.getDeliveryRegion()),
                        newShippingArea.getTimeRangeBegin(),
                        newShippingArea.getTimeRangeEnd());

        updateRequest.setAppShippingCode(newShippingArea.getAppShippingCode());
        if (Objects.equals(newShippingArea.getOperate(), ChannelPoiShippingAreaOperationEnum.REPLACE)) {
            return updateRequest;
        }

        if (newShippingArea.getShippingAreaId() != null) {
            updateRequest.setShippingAreaId(newShippingArea.getShippingAreaId());
        }

        return updateRequest;

    }

    private DeletePoiShippingByShippingIdRequest buildDeleteNewShippingArea(ChannelPoiShippingInfo newShippingArea, Long tenantId) {
        DeletePoiShippingByShippingIdRequest request = new DeletePoiShippingByShippingIdRequest();
        request.setShippingAreaId(newShippingArea.getShippingAreaId());
        request.setChannelId(newShippingArea.getChannelId());
        request.setTenantId(tenantId);
        request.setShopId(newShippingArea.getStoreId());

        return request;
    }

    private ResetPoiShippingRequest buildResetShippingRequest(Long tenantId, Long storeId,
                                                              Integer channelId,
                                                              ResetShippingAreaRequest.ChannelShippingAreaParam param) {
        ResetPoiShippingRequest request = new ResetPoiShippingRequest();
        //把元转成分
        request.setShippingFee((int) (param.getShippingFee() * 100));
        request.setMinOrderPrice((int) (param.getMinPrice() * 100));
        request.setCoordinates(IGeoUtils.createCoordinateDtoList(param.getDeliveryRegion()));
        request.setChannelId(channelId);
        request.setShopId(storeId);
        request.setTenantId(tenantId);
        request.setApp_shipping_code(ShippingUtils.createAppShippingCode(param.getShippingTagList()));

        return request;
    }

    private void checkPoiPermission(Long poiId, Long tenantId, Long accountId) {
        List<Long> poiIdList = queryPermissionInfoOfCurrentUser(tenantId, accountId);
        boolean hasPermission = (CollectionUtils.isNotEmpty(poiIdList) && poiIdList.contains(poiId));
        Assert.throwIfTrue(!hasPermission, "您没有该门店权限");
    }

    private Pair<Map<Long, Polygon>, Map<Long, Polygon>> queryChannelPoiShippingInfoList(List<PoiDetailInfoDTO> poiDetailInfoDTOList, Long tenantId) throws TException {
        if (Objects.isNull(tenantId)) {
            throw new IllegalArgumentException("获取用户信息失败,请先登陆");
        }

        List<PoiShippingAreaInfoDTO> mtShippingInfoList = ocmsChannelPoiShippingServiceWrapper.batchQueryShippingInfo(tenantId, poiDetailInfoDTOList, ChannelTypeEnum.MEITUAN.getChannelId());

        List<PoiShippingAreaInfoDTO> dhShippingInfoList = ocmsChannelPoiShippingServiceWrapper.batchQueryShippingInfo(tenantId, poiDetailInfoDTOList, ChannelTypeEnum.MT_DRUNK_HORSE.getChannelId());

        ArrayList<PoiShippingAreaInfoDTO> regularPeriodShippingAreas = new ArrayList<>();
        ArrayList<PoiShippingAreaInfoDTO> specialPeriodShippingAreas = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mtShippingInfoList)) {
            mtShippingInfoList.forEach(poiShippingInfoDTO -> {
                if (isSpecialPeriodShippingAres(poiShippingInfoDTO)) {
                    specialPeriodShippingAreas.add(poiShippingInfoDTO);
                } else {
                    regularPeriodShippingAreas.add(poiShippingInfoDTO);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(dhShippingInfoList)) {
            dhShippingInfoList.forEach(poiShippingInfoDTO -> {
                if (isSpecialPeriodShippingAres(poiShippingInfoDTO)) {
                    specialPeriodShippingAreas.add(poiShippingInfoDTO);
                } else {
                    regularPeriodShippingAreas.add(poiShippingInfoDTO);
                }
            });
        }

        //合并正常时段配送范围
        Map<Long, Polygon> mergedRegularPeriodAreasMap = mergeStoreAreas(regularPeriodShippingAreas);

        //合并特殊时段配送范围
        Map<Long, Polygon> mergedSpecialPeriodAreasMap = mergeStoreAreas(specialPeriodShippingAreas);

        return Pair.of(mergedRegularPeriodAreasMap, mergedSpecialPeriodAreasMap);
    }

    private Map<Long, Polygon> mergeStoreAreas(List<PoiShippingAreaInfoDTO> areas) {
        Map<Long, List<PoiShippingAreaInfoDTO>> regularPeriodAreasMap =
                areas.stream().collect(Collectors.groupingBy(PoiShippingAreaInfoDTO::getPoiId));

        Map<Long, Polygon> mergedAreasMap = new HashMap<>();

        for (Map.Entry<Long, List<PoiShippingAreaInfoDTO>> entry : regularPeriodAreasMap.entrySet()) {
            if (entry.getValue().size() > 1) {
                log.warn("channel store {} has multi delivery area.", entry);
            }

            Optional<Polygon> polygonOpt = convertAndMergeDeliveryArea(entry.getValue());
            polygonOpt.ifPresent(polygon -> mergedAreasMap.put(entry.getKey(), polygon));
        }

        return mergedAreasMap;
    }

    private Optional<Polygon> convertAndMergeDeliveryArea(List<PoiShippingAreaInfoDTO> poiShippingInfoDTOList) {
        if (CollectionUtils.isEmpty(poiShippingInfoDTOList)) {
            return Optional.empty();
        }
        List<Polygon> polygonList =
                poiShippingInfoDTOList.stream().map(PoiShippingAreaInfoDTO::getArea).map(IGeoUtils::convertDeliveryArea).collect(Collectors.toList());
        return IGeoUtils.combineDeliveryArea(polygonList);
    }

    public static boolean isSpecialPeriodShippingAres(PoiShippingAreaInfoDTO poiShippingInfoDTO) {
        if (Objects.isNull(poiShippingInfoDTO)) {
            throw new SystemException("配送范围为null");
        }

        return !StringUtils.equals(poiShippingInfoDTO.getTimeRangeBegin(), "00:00") || !StringUtils.equals(poiShippingInfoDTO.getTimeRangeEnd(), "24:00");
    }

    public static boolean isSpecialPeriodShippingAres(ChannelPoiShippingInfo channelPoiShippingInfo) {
        if (Objects.isNull(channelPoiShippingInfo)) {
            throw new SystemException("配送范围为null");
        }

        return !StringUtils.equals(channelPoiShippingInfo.getTimeRangeBegin(), "00:00") || !StringUtils.equals(channelPoiShippingInfo.getTimeRangeEnd(), "24:00");
    }

    /**
     * 获取当前账号有权限的门店列表
     *
     * @return
     */
    private List<PoiInfoDto> queryPoiListAuthorized(Long tenantId, Long accountId) {
        List<Long> poiIdList = queryPermissionInfoOfCurrentUser(tenantId, accountId);
        if (CollectionUtils.isEmpty(poiIdList)) {
            return Lists.newArrayList();
        }

        List<PoiInfoDto> result = new ArrayList<>();
        List<List<Long>> poiIdPartitions = ListUtils.partition(poiIdList, 100);
        for (List<Long> partition : poiIdPartitions) {
            Map<Long, PoiInfoDto> poiInfoDtoMap = queryPoiByIds(tenantId, partition);
            result.addAll(Lists.newArrayList(poiInfoDtoMap.values()));
        }
        return result;
    }

    private List<Long> queryPermissionInfoOfCurrentUser(Long tenantId, Long accountId) {
        if (Objects.isNull(tenantId) || Objects.isNull(accountId)) {
            throw new IllegalStateException("获取用户信息失败,请先登陆");
        }

        return queryPermissionInfoOfCurrentUser(tenantId, accountId,
                Const.SAAS_B_APP_ID,
                Const.AUTH_TYPE_OF_POI, Long::valueOf);
    }

    private  <T> List<T> queryPermissionInfoOfCurrentUser(long tenantId, long accountId, int appId, int type,
                                                        Function<String, T> transfer) {
        QueryPermissionGroupRequest request = new QueryPermissionGroupRequest();
        request.setAccountId(accountId);
        request.setAppId(appId);
        request.setTenantId(tenantId);
        request.setType(type);
        try {
            QueryPermissionGroupResponse response =
                    authThriftService.queryPermissionGroupByTokenAndPermissionType(request);
            log.info("invoke AuthThriftService.Iface.queryPermissionGroupByTokenAndPermissionType, request= {}",
                    JsonUtils.toJson(request));
            Bssert.throwIfNull(response.getResult(), "未能获取到接口状态");
            Bssert.throwIfTrue(response.getResult().getCode() != 0,
                    StringUtils.defaultIfBlank(response.getResult().getMsg(), "接口返回错误"));

            List<PermissionGroupVo> list = response.getPermissionGroupCodeList();
            if (list == null) {
                return Collections.emptyList();
            }

            List<T> poiIdList =
                    list.stream().map(PermissionGroupVo::getCode).map(transfer).collect(Collectors.toList());
            log.info("authorized storeId list: {}", JsonUtils.toJson(poiIdList));
            return poiIdList;
        } catch (TException e) {
            log.error("AuthClient queryRepositoryOfCurrentUser error", e);
            throw new IllegalStateException(e);
        }
    }

    private Map<Long, PoiInfoDto> queryPoiByIds(Long tenantId, List<Long> poiIds) {
        PoiMapResponse response = poiThriftService.queryTenantPoiInfoMapByPoiIds(poiIds, tenantId);
        log.info("invoke PoiThriftService.queryTenantPoiInfoMapByPoiIds, request= {} {}, response = {}",
                tenantId, JsonUtils.toJson(poiIds), JsonUtils.toJson(response));

        Bssert.throwIfNull(response.getStatus(), "未能获取到接口状态");
        Bssert.throwIfNull(response.getStatus().getCode(), "未能获取到接口状态");
        Bssert.throwIfTrue(response.getStatus().getCode() != 0,
                StringUtils.defaultIfBlank(response.getStatus().getMessage(), "接口返回错误"));

        return response.getPoiInfoMap();
    }
}
