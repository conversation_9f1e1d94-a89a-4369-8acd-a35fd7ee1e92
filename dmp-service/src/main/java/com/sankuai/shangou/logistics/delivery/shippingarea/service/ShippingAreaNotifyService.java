package com.sankuai.shangou.logistics.delivery.shippingarea.service;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.common.util.JsonUtils;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.exception.BusinessException;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.tenant.thrift.DepartmentV2ThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiApprovalManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiApprovalScenesTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.DistrictDto;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.dto.EmployeeInfoV2Dto;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.request.EmployeeInfoInDepartmentQueryV2Request;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.response.EmployeeInfoInDepartmentQueryV2Response;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiApprovalFullEmployeeChainRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiApprovalFullEmployeeChainResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountDetailByTenantIdAndStaffIdsRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountInfoListResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.PoiShippingAreaInfoDTO;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ShippingTagEnum;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.shippingarea.domain.ChannelPoiShippingInfo;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.ChannelPoiShippingAreaOperationEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.ChannelTypeEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.PositionPushConfigEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.DateTimeUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.MccUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.PositionPushHelper;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import com.sankuai.xm.pubapi.thrift.PusherInfo;
import com.sankuai.xm.udb.common.UdbServiceI;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/24 19:28
 **/
@Slf4j
@Service
public class ShippingAreaNotifyService {
    @Resource
    private PushMessageServiceI.Iface shippingPushMessageService;

    @Resource
    private PoiApprovalManageThriftService poiApprovalManageThriftService;

    @Resource
    private UdbServiceI.Iface udbService;

    @Resource
    private DepartmentV2ThriftService departmentV2ThriftService;
    @Resource
    private AuthThriftService.Iface authThriftService;
    @Resource
    private PoiThriftService poiThriftServiceClient;


    @Autowired
    @Qualifier("poiShippingAreaPusher")
    private PusherInfo pusherInfo;


    private static final int SUCCESS_CODE = 0;

    private static final int ACCOUNT_VALID = 1;

    private static final int FRANCHISEE_OPERATION_MODE = 2;

    @MethodLog(logRequest = true, logResponse = true)
    public void updateRegularPeriodShippingNotify(PoiDetailInfoDTO poi, List<ChannelPoiShippingInfo> oldShippingList,
                                                  List<ChannelPoiShippingInfo> newShippingList,
                                                  String operatorAccount, String operatorUserName) {
        try {
            DistrictDto district = poi.getDistrict();

            StringBuilder text = new StringBuilder();
            text.append("【配送范围调整通知】\n");
            text.append(String.format("【门店】%s%s\n", district.getCityName(), poi.getPoiName()));
            text.append("【范围变动】\n");
            text.append(genShippingDeliveryAdjustDetail(oldShippingList, newShippingList));
            text.append("【费率变动】\n");
            text.append(genShippingFeeAdjustDetail(oldShippingList, newShippingList));

            text.append(String.format("【操作人】%s/%s\n", operatorUserName, operatorAccount));
            text.append(String.format("【操作时间】%s\n", DateTimeUtils.fmtLocalDateTime(LocalDateTime.now())));
            text.append(String.format("如需查看和处理，请点击链接前往：[配送范围管理|%s]", MccUtils.getShippingManagementLink()));


            List<String> misIdList = MccUtils.getShippingAdjustReceiverList();
            try {
                misIdList.addAll(getCityManagerMisIdList(poi.getTenantId(), poi.getPoiId()));
            } catch (BusinessException e) {
                log.warn("getCityManagerMisIdList fail", e);
                text.append("\nwarning: " + e.getMessage());
            }

            JSONObject bodyJson = new JSONObject();
            bodyJson.put("text", text.toString());

            String resp = shippingPushMessageService.pushMessageCrossEnterpriseWithCid(System.currentTimeMillis(), "text",
                    bodyJson.toJSONString(), misIdList, pusherInfo, 1);
            log.info("invoke pushMessageService.pushExtensionMessageWithUids, text={}, resp = {}", text, resp);
        } catch (Exception e) {
            log.error("配送范围变更推送失败", e);
            throw new SystemException("配送范围变更推送失败");
        }
    }


    @MethodLog(logRequest = true, logResponse = true)
    public void updateSpecialPeriodShippingNotify(PoiDetailInfoDTO poi, List<ChannelPoiShippingInfo> oldShippingList,
                                                  List<ChannelPoiShippingInfo> newShippingList,
                                                  String operatorAccount, String operatorUserName) {
        try {
            DistrictDto district = poi.getDistrict();

            StringBuilder text = new StringBuilder();
            text.append("【特殊时段配送范围调整通知】\n");
            text.append(String.format("【门店】%s%s\n", district.getCityName(), poi.getPoiName()));
            text.append("【时段变动】\n");
            text.append(genPeriodAdjustDetail(oldShippingList, newShippingList));
            text.append("【范围变动】\n");
            text.append(genShippingDeliveryAdjustDetail(oldShippingList, newShippingList));
            text.append("【费率变动】\n");
            text.append(genShippingFeeAdjustDetail(oldShippingList, newShippingList));


            text.append(String.format("【操作人】%s/%s\n", operatorUserName, operatorAccount));
            text.append(String.format("【操作时间】%s\n", DateTimeUtils.fmtLocalDateTime(LocalDateTime.now())));
            text.append(String.format("如需查看和处理，请点击链接前往：[配送范围管理|%s]", MccUtils.getShippingManagementLink()));


            List<String> misIdList = MccUtils.getShippingAdjustReceiverList();
            try {
                misIdList.addAll(getCityManagerMisIdList(poi.getTenantId(), poi.getPoiId()));
            } catch (BusinessException e) {
                log.warn("getCityManagerMisIdList fail", e);
                text.append("\nwarning: " + e.getMessage());
            }

            JSONObject bodyJson = new JSONObject();
            bodyJson.put("text", text.toString());

            String resp = shippingPushMessageService.pushMessageCrossEnterpriseWithCid(System.currentTimeMillis(), "text",
                    bodyJson.toJSONString(), misIdList, pusherInfo, 1);
            log.info("invoke pushMessageService.pushExtensionMessageWithUids, text={}, resp = {}", text, resp);
        } catch (Exception e) {
            log.error("配送范围变更推送失败", e);
            throw new SystemException("配送范围变更推送失败");
        }
    }

    @MethodLog(logRequest = true, logResponse = true)
    public void resetShippingNotify(PoiDetailInfoDTO poi,
                                    List<ChannelPoiShippingInfo> newShippingList) {
        try {
            DistrictDto district = poi.getDistrict();

            StringBuilder text = new StringBuilder();
            text.append("【配送范围重置通知】\n");
            text.append(String.format("【门店】%s%s\n", district.getCityName(), poi.getPoiName()));
            text.append("【范围变动】\n");
            text.append(genShippingDeliveryAdjustDetail(Collections.emptyList(), newShippingList));
            text.append("【费率变动】\n");
            text.append(genShippingFeeAdjustDetail(Collections.emptyList(), newShippingList));


            text.append(String.format("【操作人】%s/%s\n", "系统", "system"));
            text.append(String.format("【操作时间】%s\n", DateTimeUtils.fmtLocalDateTime(LocalDateTime.now())));
            text.append(String.format("如需查看和处理，请点击链接前往：[配送范围管理|%s]", MccUtils.getShippingManagementLink()));


            List<String> misIdList = MccUtils.getShippingAdjustReceiverList();
            try {
                misIdList.addAll(getCityManagerMisIdList(poi.getTenantId(), poi.getPoiId()));
            } catch (BusinessException e) {
                log.warn("getCityManagerMisIdList fail", e);
                text.append("\nwarning: " + e.getMessage());
            }

            JSONObject bodyJson = new JSONObject();
            bodyJson.put("text", text.toString());

            String resp = shippingPushMessageService.pushMessageCrossEnterpriseWithCid(System.currentTimeMillis(), "text",
                    bodyJson.toJSONString(), misIdList, pusherInfo, 1);
            log.info("invoke pushMessageService.pushExtensionMessageWithUids, text={}, resp = {}", text, resp);
        } catch (Exception e) {
            log.error("配送范围变更推送失败", e);
            throw new SystemException("配送范围变更推送失败");
        }
    }

    public void deleteShippingNotify(PoiDetailInfoDTO poi, List<PoiShippingAreaInfoDTO> oldShippingInfoList,
                                     Map<Long,Double> areaId2OldAreaMap,
                                     String operatorAccount, String operatorUserName) {
        try {
            DistrictDto district = poi.getDistrict();

            StringBuilder text = new StringBuilder();
            text.append("【配送范围删除通知】\n");
            text.append(String.format("【门店】%s%s\n", district.getCityName(), poi.getPoiName()));
            text.append("【时段变动】\n");
            text.append(genShippingPeriodDeleteDetail(oldShippingInfoList));
            text.append("【范围变动】\n");
            text.append(genShippingDeliveryDeleteDetail(oldShippingInfoList, areaId2OldAreaMap));
            text.append("【费率变动】\n");
            text.append(genShippingFeeDeleteDetail(oldShippingInfoList));


            text.append(String.format("【操作人】%s/%s\n", operatorUserName, operatorAccount));
            text.append(String.format("【操作时间】%s\n", DateTimeUtils.fmtLocalDateTime(LocalDateTime.now())));
            text.append(String.format("如需查看和处理，请点击链接前往：[配送范围管理|%s]", MccUtils.getShippingManagementLink()));


            List<String> misIdList = MccUtils.getShippingAdjustReceiverList();
            try {
                misIdList.addAll(getCityManagerMisIdList(poi.getTenantId(), poi.getPoiId()));
            } catch (BusinessException e) {
                log.warn("getCityManagerMisIdList fail", e);
                text.append("\nwarning: " + e.getMessage());
            }

            JSONObject bodyJson = new JSONObject();
            bodyJson.put("text", text.toString());

            //List<Long> uidList = getUdbUidsByMisIds(misIdList);
            String resp = shippingPushMessageService.pushMessageCrossEnterpriseWithCid(System.currentTimeMillis(), "text",
                    bodyJson.toJSONString(), misIdList, pusherInfo, 1);
            log.info("invoke pushMessageService.pushExtensionMessageWithUids, text={}, resp = {}", text, resp);
        } catch (Exception e) {
            log.error("配送范围变更推送失败", e);
            throw new SystemException("配送范围变更推送失败");
        }
    }

    private String genShippingDeliveryDeleteDetail(List<PoiShippingAreaInfoDTO> oldShippingInfoList,
                                                   Map<Long,Double> areaId2OldAreaMap) {
        StringBuilder deleteStringDetail = new StringBuilder();
        for (PoiShippingAreaInfoDTO oldShippingArea : oldShippingInfoList) {
            deleteStringDetail.append(String.format("%s配送范围已删除，已删除范围面积为%s平方公里\n", ChannelTypeEnum.enumOf(oldShippingArea.getChannelId()).getChannelName(),
                    areaId2OldAreaMap.get(oldShippingArea.getShippingAreaId())));
        }

        return deleteStringDetail.toString();
    }

    private String genShippingPeriodDeleteDetail(List<PoiShippingAreaInfoDTO> oldShippingInfoList) {
        StringBuilder deleteStringDetail = new StringBuilder();
        for (PoiShippingAreaInfoDTO oldShippingArea : oldShippingInfoList) {
            if (ShippingAreaService.isSpecialPeriodShippingAres(oldShippingArea)) {
                deleteStringDetail.append(String.format("%s删除特殊时段,%s~%s\n",
                        ChannelTypeEnum.enumOf(oldShippingArea.getChannelId()).getChannelName(),
                        oldShippingArea.getTimeRangeBegin(),
                        oldShippingArea.getTimeRangeEnd()));
            } else {
                deleteStringDetail.append(String.format("%s删除正常时段\n", ChannelTypeEnum.enumOf(oldShippingArea.getChannelId()).getChannelName()));
            }
        }

        return deleteStringDetail.toString();
    }

    private String genShippingFeeDeleteDetail(List<PoiShippingAreaInfoDTO> oldShippingInfoList) {
        StringBuilder deleteStringDetail = new StringBuilder();
        for (PoiShippingAreaInfoDTO oldShippingArea : oldShippingInfoList) {
            deleteStringDetail.append(String.format("%s配送范围已删除，已删除范围起送价%s元，配送费%s元\n",
                    ChannelTypeEnum.enumOf(oldShippingArea.getChannelId()).getChannelName(),
                    oldShippingArea.getMinPrice(),
                    oldShippingArea.getShippingFee()));
        }

        return deleteStringDetail.toString();
    }

    private String genShippingDeliveryAdjustDetail(List<ChannelPoiShippingInfo> oldShippingList,
                                                   List<ChannelPoiShippingInfo> newShippingList) {
        Map<Long, ChannelPoiShippingInfo> oldShippingMap = Fun.toMap(oldShippingList, ChannelPoiShippingInfo::getShippingAreaId);

        List<String> summary = new ArrayList<>();
        StringBuilder deliveryAdjustDetail = new StringBuilder();
        for (ChannelPoiShippingInfo newcs : newShippingList) {
            switch (newcs.getOperate()) {
                case UPDATE: {
                    ChannelPoiShippingInfo oldcs = oldShippingMap.get(newcs.getShippingAreaId());

                    String detail = calAreaChange(newcs, oldcs);
                    if (StringUtils.isNotBlank(detail)) {
                        summary.add(oldcs.getChannelType().getChannelName() + "配送范围已修改");
                        deliveryAdjustDetail.append(detail);
                    }
                    break;
                }


                case REPLACE: {
                    ChannelPoiShippingInfo oldcs = oldShippingMap.get(newcs.getOldAreaId());

                    String detail = calAreaChange(newcs, oldcs);
                    if (StringUtils.isNotBlank(detail)) {
                        summary.add(oldcs.getChannelType().getChannelName() + "配送范围已修改");
                        deliveryAdjustDetail.append(detail);
                    }
                    break;
                }

                case NEW: {
                    summary.add(newcs.getChannelType().getChannelName() + "配送范围新增");
                    String detail = String.format("%s新增范围：面积为%s平方公里，%s\n",
                            newcs.getChannelType().getChannelName(),
                            newcs.getDeliveryArea(),
                            genShippingTagAdjustDetail(null, newcs));
                    deliveryAdjustDetail.append(detail);
                    break;
                }

                case RESET: {
                    summary.add(newcs.getChannelType().getChannelName() + "配送范围重置");
                    String detail = String.format("%s配送范围：面积为%s平方公里，%s\n",
                            newcs.getChannelType().getChannelName(),
                            newcs.getDeliveryArea(),
                            genShippingTagAdjustDetail(null, newcs));
                    deliveryAdjustDetail.append(detail);
                    break;
                }

                default:
                    break;
            }
        }

        if (CollectionUtils.isEmpty(summary) && StringUtils.isBlank(deliveryAdjustDetail)) {
            return "无变动\n";
        }

        return String.join("，", summary) + "\n" + deliveryAdjustDetail;
    }

    private String calAreaChange(ChannelPoiShippingInfo newcs, ChannelPoiShippingInfo oldcs) {
        BigDecimal newArea = new BigDecimal(newcs.getDeliveryArea());
        BigDecimal oldArea = new BigDecimal(oldcs.getDeliveryArea());
        String detail = null;
        switch (oldArea.compareTo(newArea)) {
            case -1: { // less than
                // (修改后面积-修改前面积）/修改前面积
                BigDecimal rate;
                if (oldArea.compareTo(BigDecimal.ZERO) == 0) {
                    rate = new BigDecimal(100);
                } else {
                    /// divide不整除时，出现无限循环小数时，就会抛异常Non-terminating, 解决方法增加精度
                    rate =
                            newArea.subtract(oldArea).divide(oldArea, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2,
                                    BigDecimal.ROUND_HALF_UP);
                }
                detail = String.format("%s配送范围变动：面积由%s平方公里到%s平方公里，%s %s%%，%s\n", oldcs.getChannelType().getChannelName(),
                        oldcs.getDeliveryArea(), newcs.getDeliveryArea(), "扩大", rate.toPlainString(),
                        genShippingTagAdjustDetail(oldcs, newcs));
                break;
            }
            case 0: { // equal
                List<String> changeDetails = new ArrayList<>();
                if (!oldcs.getDeliveryRegion().equals(newcs.getDeliveryRegion())) {
                    changeDetails.add("区域变动面积无变动");
                }

                if (!CollectionUtils.isEqualCollection(oldcs.getShippingTagList(), newcs.getShippingTagList())) {
                    changeDetails.add(genShippingTagAdjustDetail(oldcs, newcs));
                }

                if (CollectionUtils.isNotEmpty(changeDetails)) {
                    detail = String.format("%s配送范围变动：%s\n", oldcs.getChannelType().getChannelName(),
                            Strings.join(changeDetails, "，"));
                }
                break;
            }
            case 1: {// greater than
                BigDecimal rate =
                        oldArea.subtract(newArea).divide(oldArea, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2,
                                BigDecimal.ROUND_HALF_UP);
                detail = String.format("%s配送范围变动：面积由%s平方公里到%s平方公里，%s %s%%，%s\n", oldcs.getChannelType().getChannelName(),
                        oldcs.getDeliveryArea(), newcs.getDeliveryArea(), "缩小", rate.toPlainString(),
                        genShippingTagAdjustDetail(oldcs, newcs));
                break;
            }
            default: {
                break;
            }
        }

        return detail;
    }

    private String genShippingTagAdjustDetail(ChannelPoiShippingInfo oldcs, ChannelPoiShippingInfo newcs) {
        if (ChannelPoiShippingAreaOperationEnum.NEW.equals(newcs.getOperate())
                || ChannelPoiShippingAreaOperationEnum.RESET.equals(newcs.getOperate())) {
            // 新增
            StringBuilder tagAdjustDetail = new StringBuilder();
            for (ShippingTagEnum shippingTag : ShippingTagEnum.values()) {
                if (newcs.getShippingTagList().contains(shippingTag)) {
                    tagAdjustDetail.append(String.format("为%s", shippingTag.getDesc()));
                } else {
                    tagAdjustDetail.append(String.format("非%s", shippingTag.getDesc()));
                }
            }
            return tagAdjustDetail.toString();
        }

        if (ChannelPoiShippingAreaOperationEnum.REPLACE.equals(newcs.getOperate())) {
            StringBuilder tagAdjustDetail = new StringBuilder();
            for (ShippingTagEnum shippingTag : oldcs.getShippingTagList()) {
                if (!newcs.getShippingTagList().contains(shippingTag)) {
                    tagAdjustDetail.append(String.format("取消%s标签", shippingTag.getDesc()));
                }
            }

            for (ShippingTagEnum shippingTag : newcs.getShippingTagList()) {
                if (!oldcs.getShippingTagList().contains(shippingTag)) {
                    tagAdjustDetail.append(String.format("新增%s标签", shippingTag.getDesc()));
                }
            }

            return tagAdjustDetail.toString();
        }

        if (CollectionUtils.isEqualCollection(oldcs.getShippingTagList(), newcs.getShippingTagList())) {
            return "范围标签无变更";
        }

        Cat.logEvent("SHIPPING_MANAGEMENT", "PARSE_TAG_ADJUST_FAIL");
        log.warn("parse tag adjust detail fail, newArea:{}, oldArea:{}", newcs, oldcs);
        return "";
    }

    private String genShippingFeeAdjustDetail(List<ChannelPoiShippingInfo> oldShippingList,
                                              List<ChannelPoiShippingInfo> newShippingList) {
        Map<Long, ChannelPoiShippingInfo> oldShippings = Fun.toMap(oldShippingList, ChannelPoiShippingInfo::getShippingAreaId);

        StringBuilder feeAdjustDetail = new StringBuilder();
        for (ChannelPoiShippingInfo newcs : newShippingList) {
            if (ChannelPoiShippingAreaOperationEnum.NEW.equals(newcs.getOperate())) {
                feeAdjustDetail.append(String.format("%s新增范围费率: 起送价%s元，配送费%s元\n", ChannelTypeEnum.enumOf(newcs.getChannelId()).getChannelName(),
                        newcs.getMinPrice(), newcs.getShippingFee()));
                continue;
            }

            if (ChannelPoiShippingAreaOperationEnum.REPLACE.equals(newcs.getOperate())) {
                ChannelPoiShippingInfo oldcs = oldShippings.get(newcs.getOldAreaId());
                if (Objects.equals(oldcs.getShippingFee(), newcs.getShippingFee()) && Objects.equals(oldcs.getMinPrice(), newcs.getMinPrice())) {
                    continue;
                }

                feeAdjustDetail.append(String.format("%s起送价%s元，配送费%s元 到起送价%s元，配送费%s元\n", ChannelTypeEnum.enumOf(newcs.getChannelId()).getChannelName(),
                        oldcs.getMinPrice(), oldcs.getShippingFee(), newcs.getMinPrice(), newcs.getShippingFee()));

            }

            if (ChannelPoiShippingAreaOperationEnum.UPDATE.equals(newcs.getOperate())) {
                ChannelPoiShippingInfo oldcs = oldShippings.get(newcs.getShippingAreaId());
                if (Objects.equals(oldcs.getShippingFee(), newcs.getShippingFee()) && Objects.equals(oldcs.getMinPrice(), newcs.getMinPrice())) {
                    continue;
                }

                feeAdjustDetail.append(String.format("%s起送价%s元，配送费%s元 到起送价%s元，配送费%s元\n", ChannelTypeEnum.enumOf(newcs.getChannelId()).getChannelName(),
                        oldcs.getMinPrice(), oldcs.getShippingFee(), newcs.getMinPrice(), newcs.getShippingFee()));
            }

            if (ChannelPoiShippingAreaOperationEnum.RESET.equals(newcs.getOperate())) {
                feeAdjustDetail.append(String.format("%s重置范围费率:起送价%s元，配送费%s元\n", ChannelTypeEnum.enumOf(newcs.getChannelId()).getChannelName(),
                         newcs.getMinPrice(), newcs.getShippingFee()));
            }

        }

        if (StringUtils.isBlank(feeAdjustDetail)) {
            return "无变动\n";
        }

        return feeAdjustDetail.toString();
    }

    public List<String> getCityManagerMisIdList(long tenantId, Long poiId) {
        //查询门店信息
        PoiMapResponse poiMapResponse = poiThriftServiceClient.queryTenantPoiInfoMapByPoiIds(
                Lists.newArrayList(poiId), tenantId
        );
        if (SUCCESS_CODE != poiMapResponse.getStatus().getCode() || !poiMapResponse.getPoiInfoMap().containsKey(poiId)) {
            log.warn("Query poi[{}] info failed. ", poiId);
            throw new SystemException("查询门店失败");
        }
        PoiInfoDto poiInfoDto = poiMapResponse.getOne(poiId);

        if (PositionPushHelper.getDHByPositionSwitch(tenantId, poiId)) {
            return queryMisListByPositions(tenantId, poiId, PositionPushHelper.getPositionIds(poiInfoDto, PositionPushConfigEnum.SHIPPING_AREA_CHANGE));
        } else {
            PoiApprovalFullEmployeeChainRequest request = new PoiApprovalFullEmployeeChainRequest();
            request.setTenantId(tenantId);
            request.setPoiId(poiId);
            request.setPoiApprovalScenesType(PoiApprovalScenesTypeEnum.COMMON.getType());
            PoiApprovalFullEmployeeChainResponse resp =
                    poiApprovalManageThriftService.queryApprovalFullEmployeeChain(request);
            log.info("invoke poiApprovalManageThriftService.queryApprovalFullEmployeeChain, poi = {}, response = {}",
                    poiId, JsonUtils.toJson(resp));
            Bssert.throwIfTrue(Objects.isNull(resp) || Objects.isNull(resp.getStatus()) || Objects.isNull(resp.getStatus().getCode()), "未能获取到接口状态");
            Bssert.throwIfTrue(resp.getStatus().getCode() != 0, StringUtils.defaultIfBlank(resp.getStatus().getMessage(),
                    "推送失败，未绑定城市经理"));
            // 第二级为城市经理
            Bssert.throwIfTrue(CollectionUtils.isEmpty(resp.getEmployeeChain()) || resp.getEmployeeChain().size() < 2,
                    "推送失败，未绑定城市经理");

            return resp.getEmployeeChain().get(1);
        }
    }


    public String genPeriodAdjustDetail(List<ChannelPoiShippingInfo> oldShippingList,
                                        List<ChannelPoiShippingInfo> newShippingList) {

        Map<Long, ChannelPoiShippingInfo> oldShippings = Fun.toMapQuietly(oldShippingList, ChannelPoiShippingInfo::getShippingAreaId);

        StringBuilder periodAdjustDetail = new StringBuilder();
        for (ChannelPoiShippingInfo newcs : newShippingList) {
            if (!oldShippings.containsKey(newcs.getShippingAreaId())) {
                // 新增
                periodAdjustDetail.append(String.format("%s新增特殊时段: %s ~ %s \n", ChannelTypeEnum.enumOf(newcs.getChannelId()).getChannelName(),
                        newcs.getTimeRangeBegin(), newcs.getTimeRangeEnd()));
                continue;
            }
            ChannelPoiShippingInfo oldcs = oldShippings.get(newcs.getShippingAreaId());

            if (Objects.equals(oldcs.getTimeRangeBegin(), newcs.getTimeRangeBegin()) && Objects.equals(oldcs.getTimeRangeEnd(), newcs.getTimeRangeEnd())) {
                continue;
            }

            periodAdjustDetail.append(String.format("%s修改特殊时段，从%s~%s到%s~%s\n", ChannelTypeEnum.enumOf(newcs.getChannelId()).getChannelName(),
                    oldcs.getTimeRangeBegin(), oldcs.getTimeRangeEnd(), newcs.getTimeRangeBegin(), newcs.getTimeRangeEnd()));
        }

        if (StringUtils.isBlank(periodAdjustDetail)) {
            return "无变动\n";
        }

        return periodAdjustDetail.toString();

    }

    private List<String> queryMisListByPositions(long tenantId, long storeId, List<Long> positionIds) {
        try {
            EmployeeInfoInDepartmentQueryV2Response employeeResponse = getEmployeePositionResponse(tenantId, storeId, positionIds);
            Map<Long, List<EmployeeInfoV2Dto>> positionAndEmployeeInfoMap = employeeResponse.getPositionAndEmployeeInfoMap();
            if(MapUtils.isEmpty(positionAndEmployeeInfoMap)) {
                return Lists.newArrayList();
            }
            QueryAccountInfoListResponse accountResponse = getQueryAccountInfoListResponse(tenantId, positionAndEmployeeInfoMap);
            List<AccountInfoVo> validAccountInfoList =
                    Optional.ofNullable(accountResponse.getAccountInfoList())
                            .orElse(Lists.newArrayList())
                            .stream()
                            .filter(accountInfoVo -> Objects.equals(accountInfoVo.getValid(), ACCOUNT_VALID))
                            .collect(Collectors.toList());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(validAccountInfoList)) {
                return Lists.newArrayList();
            }

            return validAccountInfoList.stream().filter(accountVO -> StringUtils.isNotBlank(accountVO.getMisId())).map(AccountInfoVo::getMisId).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("queryPushInfoByPositions error", e);
            throw new SystemException("查询push信息失败");
        }
    }

    private QueryAccountInfoListResponse getQueryAccountInfoListResponse(long tenantId, Map<Long, List<EmployeeInfoV2Dto>> positionAndEmployeeInfoMap) throws TException {
        QueryAccountDetailByTenantIdAndStaffIdsRequest request = new QueryAccountDetailByTenantIdAndStaffIdsRequest();
        request.setTenantId(tenantId);
        request.setStaffIds(positionAndEmployeeInfoMap.values().stream().flatMap(List::stream).map(EmployeeInfoV2Dto::getEmployeeId).collect(Collectors.toList()));

        QueryAccountInfoListResponse accountResponse = authThriftService.queryAccountDetailByTenantIdAndStaffIds(request);
        log.info("authThriftService.queryAccountDetailByTenantIdAndStaffIds request = {}, response = {}", request, accountResponse);
        if (!Objects.equals(accountResponse.getResult().getCode(), SUCCESS_CODE)) {
            throw new SystemException("authThriftService.queryAccountDetailByTenantIdAndStaffIds fail");
        }
        return accountResponse;
    }

    private EmployeeInfoInDepartmentQueryV2Response getEmployeePositionResponse(long tenantId, long storeId, List<Long> positionIds) {
        EmployeeInfoInDepartmentQueryV2Request request = new EmployeeInfoInDepartmentQueryV2Request();
        request.setTenantId(tenantId);
        request.setResourceType("poi");
        request.setResourceId(String.valueOf(storeId));
        request.setFilterPositionIdList(positionIds);
        request.setAlsoQueryInParentDepartment(true);
        request.setAlsoQueryInChildDepartment(false);
        EmployeeInfoInDepartmentQueryV2Response employeeResponse = departmentV2ThriftService.queryEmployeeInfoListInDepartmentByCondition(request);
        log.info("departmentV2ThriftService.queryEmployeeInfoListInDepartmentByCondition request ={}, response = {}", request, employeeResponse);
        if (!Objects.equals(employeeResponse.getStatus().getCode(), SUCCESS_CODE)) {
            throw new SystemException("departmentV2ThriftService.queryEmployeeInfoListInDepartmentByCondition fail");
        }
        return employeeResponse;
    }
}
