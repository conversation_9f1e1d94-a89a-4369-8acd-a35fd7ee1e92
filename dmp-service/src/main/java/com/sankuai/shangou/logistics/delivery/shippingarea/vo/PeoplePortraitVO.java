package com.sankuai.shangou.logistics.delivery.shippingarea.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.indicator.PeoplePortraitDto;
import lombok.Data;

@TypeDoc(
        description = "人口画像相关指标",
        authors = {
                "daiyuan03"
        }
)
@Data
public class PeoplePortraitVO extends PeoplePortraitDto {

    @FieldDoc(
            description = "数据时间"
    )
    private String dt;
}
