package com.sankuai.shangou.logistics.delivery.push.msg.card;

import com.sankuai.shangou.logistics.delivery.xm.enums.CardTitleEnum;
import com.sankuai.xm.openplatform.api.entity.CardDataTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 卡片更新数据
 * @date 2025-05-20
 */
public abstract class AbstractCardUpdateData {
    /**
     * 请求ID(推送卡片的id)
     */
    private String requestId;
    /**
     * 卡片模版ID
     */
    private Long templateId;
    /**
     * 卡片数据类型
     */
    private CardDataTypeEnum cardDataType;
    /**
     * 是否根据key更新卡片数据
     */
    private Boolean updateCardDataByKey;
    /**
     * 个性化数据员工id
     */
    private List<Long> empIds;
    /**
     * 卡片标题
     */
    private CardTitleEnum cardTitle;

    public abstract String getPublicDataJsonStr();

    public abstract String getPrivateDataJsonStr();

    protected void setCardTitleMap(Map<String, Object> map) {
        map.put("title", getCardTitle().getTitle());
        map.put("titleColor", getCardTitle().getColor().getType());
    }


    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public CardDataTypeEnum getCardDataType() {
        return cardDataType;
    }

    public void setCardDataType(CardDataTypeEnum cardDataType) {
        this.cardDataType = cardDataType;
    }

    public Boolean getUpdateCardDataByKey() {
        return updateCardDataByKey;
    }

    public void setUpdateCardDataByKey(Boolean updateCardDataByKey) {
        this.updateCardDataByKey = updateCardDataByKey;
    }

    public List<Long> getEmpIds() {
        return empIds;
    }

    public void setEmpIds(List<Long> empIds) {
        this.empIds = empIds;
    }

    public CardTitleEnum getCardTitle() {
        return cardTitle;
    }

    public void setCardTitle(CardTitleEnum cardTitle) {
        this.cardTitle = cardTitle;
    }
}
