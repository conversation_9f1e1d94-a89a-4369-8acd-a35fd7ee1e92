package com.sankuai.shangou.logistics.delivery.questionnaire.mq;

import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023/11/15 10:53
 **/

@Slf4j
@Getter
public enum MQConsumerEnum {

    RELEASE_DELIVERY_QUESTIONNAIRE_CONSUMER("shangou_empower_delivery_change_async_out_topic","release_delivery_questionnaire_consumer","com.sankuai.shangou.logistics.dmp");

    private static final int DEFAULT_MAX_RETRY_TIMES = 3;

    private final String topic;
    private final String consumerGroup;
    private final String appkey;

    MQConsumerEnum(String topic, String consumerGroup, String appkey) {
		this.topic = topic;
		this.consumerGroup = consumerGroup;
		this.appkey = appkey;
    }

    public int getMaxRetryTimes() {
        String mccKey = "";
        try {
            mccKey = String.format("mq.max_retry_times.%s", this.topic);
            return ConfigUtilAdapter.getInt(mccKey, DEFAULT_MAX_RETRY_TIMES);
        } catch (Exception e) {
            log.error("MCC[{}] parse error, will use default max retry times", mccKey, e);
            return DEFAULT_MAX_RETRY_TIMES;
        }
    }
}
