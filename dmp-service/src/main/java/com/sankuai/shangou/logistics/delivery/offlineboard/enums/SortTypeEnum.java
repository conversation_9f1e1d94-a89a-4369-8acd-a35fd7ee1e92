package com.sankuai.shangou.logistics.delivery.offlineboard.enums;

/**
 * <AUTHOR>
 * @since 2023/2/21 16:41
 **/
public enum SortTypeEnum {
    ASC(1, "asc"),
    DESC(2, "desc");

    private int code;
    private String sqlStr;

    SortTypeEnum(int code, String sqlStr) {
        this.code = code;
        this.sqlStr = sqlStr;
    }

    public int getCode() {
        return code;
    }

    public String getSqlStr() {
        return sqlStr;
    }

    public static SortTypeEnum enumOf(int code) {
        for (SortTypeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }

        return null;
    }

}
