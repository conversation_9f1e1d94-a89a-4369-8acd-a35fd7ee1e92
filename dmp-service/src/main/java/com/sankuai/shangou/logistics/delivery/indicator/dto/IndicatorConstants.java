package com.sankuai.shangou.logistics.delivery.indicator.dto;

import java.time.LocalTime;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/7/9 01:42
 **/
public class IndicatorConstants {

    public static final Integer CAL_INTERVAL = 5;

    /**
     * omTable参数
     */
    public static LocalTime startTime = LocalTime.of(8, 0);
    public static LocalTime openTime = LocalTime.of(10, 0);
    public static LocalTime closeTime = LocalTime.of(4, 0);

}
