package com.sankuai.shangou.logistics.delivery.shippingarea.vo.request;

import com.meituan.linz.boot.util.Assert;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.IGeoUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2022/06/24
 */
@TypeDoc(
        description = "区域aoi分类指标查询接口参数",
        authors = {
                "daiyuan03"
        }
)
@Getter
@Setter
public class LoiMapAoiClassifyQueryParam {

    @FieldDoc(
            description = "城市id"
    )
    private Integer cityId;
    @FieldDoc(
            description = "范围边界"
    )
    private String region;
    @FieldDoc(
            description = "数据时间, 非必传"
    )
    private String dt;

    public void validate() {
        Assert.throwIfTrue(this.cityId == null || this.cityId <= 0L, "城市id不正确");
        Assert.throwIfTrue(StringUtils.isBlank(this.region), "范围不能为空");
        //IGeoUtils.validateCoordinate(region);
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"cityId\":")
                .append(cityId);
        sb.append(",\"region\":")
                .append(region);
        sb.append('}');
        return sb.toString();
    }
}
