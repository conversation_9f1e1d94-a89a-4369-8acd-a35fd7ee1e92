package com.sankuai.shangou.logistics.delivery.configure.mq;

import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaProducer;
import com.sankuai.shangou.logistics.delivery.configure.pojo.msg.BatchConfigTaskMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-01
 */
@Slf4j
@Component
public class BatchConfigTaskProducer {

    @MafkaProducer(namespace = "com.sankuai.mafka.castle.daojiacommon", topic = "batch_config_task_topic")
    private IProducerProcessor<Object, BatchConfigTaskMessage> batchConfigTaskProducer;


    public void sendMessage(BatchConfigTaskMessage message) {
        try {
            ProducerResult producerResult = batchConfigTaskProducer.sendMessage(message);
            log.info("batchConfigTask sendMessage success messageID={}", producerResult.getMessageID());
        } catch (Exception e) {
            log.error("batchConfigTask sendMessage fail, message:{}", message, e);
        }
    }

}
