package com.sankuai.shangou.logistics.delivery.push;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.commons.utils.retry.RetryTemplateUtil;
import com.sankuai.shangou.logistics.delivery.push.msg.card.AbstractCardData;
import com.sankuai.shangou.logistics.delivery.push.msg.card.AbstractCardUpdateData;
import com.sankuai.xm.openplatform.api.entity.*;
import com.sankuai.xm.openplatform.api.service.open.OpenCardServiceI;
import com.sankuai.xm.openplatform.common.enums.ResCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 大象卡片服务
 * @date 2025-05-14
 */
@Slf4j
@Service
public class DxCardService extends AbstractDxService {
    @Resource
    private OpenCardServiceI.Iface openCardService;

    public void sendGroupExclusionCard(AbstractCardData data) {
        try {
            CardFieldData cardFieldData = new CardFieldData();
            cardFieldData.setImGroupChat(new IMGroupChat(data.getGid(), null));

            PublicCardData publicCardData = new PublicCardData()
                    .setTemplateId(data.getTemplateId())
                    .setVariableValue(data.getPublicDataJsonStr())
                    .setAbstractText(data.getAbstractText());
            List<PrivateCardData> privateCardDataList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(data.getPrivateDataEmpIds())) {
                PrivateCardData privateCardData = new PrivateCardData()
                        .setTemplateId(data.getTemplateId())
                        .setEmpIds(new HashSet<>(data.getPrivateDataEmpIds()))
                        .setVariableValue(data.getPrivateDataJsonStr())
                        .setAbstractText(data.getAbstractText());
                privateCardDataList.add(privateCardData);
            }
            CardData cardData = new CardData()
                    .setVersion(System.currentTimeMillis())
                    .setPublicData(publicCardData)
                    .setPrivateData(privateCardDataList);

            CardOption cardOption = new CardOption()
                    .setUpdateType(CardUpdateTypeEnum.PUSH)
                    .setEnableForward(false);

            SendExclusionCardReq req = new SendExclusionCardReq()
                    .setRequestId(data.getRequestId())
                    .setSerialNum(data.getRequestId())
                    .setFieldTypes(Sets.newHashSet(CardFieldTypeEnum.GROUP_CHAT))
                    .setCardFieldData(cardFieldData)
                    .setCardData(cardData)
                    .setCardOption(cardOption);

            log.info("sendGroupExclusionCard req:{}", JSON.toJSONString(req));
            SendExclusionCardResp cardResp = openCardService.sendExclusionCard(getToken(), req);
            // token过期或者异常从新获取
            if (Objects.equals(cardResp.getStatus().getCode(), ResCodeEnum.EXPIRED_TOKEN.getCode())) {
                accessToken();
                cardResp = openCardService.sendExclusionCard(getToken(), req);
            }
            if (!Objects.equals(cardResp.getStatus().getCode(), ResCodeEnum.SUCCESS.getCode())) {
                log.warn("sendGroupExclusionCard resp: {}", cardResp.getStatus());
            }
            // 遇到系统异常，重试3次
            if (Objects.equals(cardResp.getStatus().getCode(), ResCodeEnum.SYSTEM_BUSY_ERROR.getCode())) {
                //  https://km.sankuai.com/collabpage/1571716878#b-ed837c8467204620a8cc526f90ebd91c
                RetryTemplate retryTemplate = RetryTemplateUtil.simpleWithFixedRetry(3, 100);
                retryTemplate.execute((RetryCallback<Boolean, Exception>) retryContext -> {
                    SendExclusionCardResp res = openCardService.sendExclusionCard(getToken(), req);
                    if (Objects.equals(res.getStatus().getCode(), ResCodeEnum.SYSTEM_BUSY_ERROR.getCode())) {
                        throw new BizException("大象消息发送失败");
                    }
                    return true;
                });
            }
        } catch (Exception e) {
            log.error("大象消息发送失败", e);
            throw new BizException("大象消息发送失败");
        }
    }

    public void updateGroupExclusionCard(AbstractCardUpdateData data) {
        try {
            UpdateExclusionCardReq req = new UpdateExclusionCardReq()
                    .setRequestId(data.getRequestId())
                    .setVersion(System.currentTimeMillis())
                    .setUpdateCardDataByKey(data.getUpdateCardDataByKey())
                    .setUpdateCardDataType(data.getCardDataType());

            if (data.getCardDataType() == CardDataTypeEnum.PUBLIC) {
                UpdatePublicCardData publicCardData = new UpdatePublicCardData()
                        .setTemplateId(data.getTemplateId())
                        .setVariableValue(data.getPublicDataJsonStr());
                req.setPublicData(publicCardData);
            }
            if (data.getCardDataType() == CardDataTypeEnum.PRIVATE) {
                UpdatePrivateCardData privateCardData = new UpdatePrivateCardData()
                        .setTemplateId(data.getTemplateId())
                        .setVariableValue(data.getPrivateDataJsonStr())
                        .setEmpIds(new HashSet<>(data.getEmpIds()));
                req.setPrivateData(privateCardData);
            }
            log.info("updateGroupExclusionCard req:{}", JSON.toJSONString(req));
            UpdateExclusionCardResp resp = openCardService.updateExclusionCard(getToken(), req);
            log.info("updateGroupExclusionCard resp:{}", JSON.toJSONString(resp));
        } catch (TException e) {
            log.error("大象卡片更新失败", e);
        }
    }
}
