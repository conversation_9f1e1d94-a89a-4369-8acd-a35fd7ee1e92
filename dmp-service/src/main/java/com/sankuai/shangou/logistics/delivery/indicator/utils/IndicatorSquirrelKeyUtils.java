package com.sankuai.shangou.logistics.delivery.indicator.utils;

import com.dianping.cat.Cat;
import com.dianping.squirrel.client.StoreKey;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023-06-30
 * @email <EMAIL>
 */
@Slf4j
public class IndicatorSquirrelKeyUtils {

    private IndicatorSquirrelKeyUtils() {
    }

    private static final String SPLIT_MARK = "_";

    private static final int IN_SCHEDULED_SHIFT = 1;

    public static final long UNSCHEDULED_SHIFT_ID = 0L;

    private static final DateTimeFormatter SQUIRREL_KEY_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final DateTimeFormatter SQUIRREL_KEY_TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");

    public static StoreKey buildScanIndicatorStoreKeys(String category, long warehouseId, LocalDate bizLocalDate) {
        String patternKey = warehouseId + SPLIT_MARK + bizLocalDate.format(SQUIRREL_KEY_DATE_FORMATTER) + "*";
        return new StoreKey(category, patternKey);
    }

    public static Optional<IndicatorStoreKey> parseIndicatorStoreKey(StoreKey originStoreKey) {
        try{
            String finalKey = getParamKey(originStoreKey);

            String[] split = finalKey.split(SPLIT_MARK);
            IndicatorStoreKey indicatorStoreKey = new IndicatorStoreKey();
            indicatorStoreKey.setWarehouseId(Long.parseLong(split[0]));
            indicatorStoreKey.setBizDate(LocalDate.parse(split[1], SQUIRREL_KEY_DATE_FORMATTER));
            indicatorStoreKey.setEmployeeId(Long.parseLong(split[2]));
            indicatorStoreKey.setShiftId(Long.parseLong(split[3]));
            indicatorStoreKey.setShiftStartTime(LocalTime.parse(split[4],SQUIRREL_KEY_TIME_FORMATTER ));
            indicatorStoreKey.setShiftEndTime(LocalTime.parse(split[5],SQUIRREL_KEY_TIME_FORMATTER ));
            indicatorStoreKey.setShiftScheduled(Integer.valueOf(split[6]).equals(IN_SCHEDULED_SHIFT));

            return Optional.of(indicatorStoreKey);
        } catch (Exception e) {
            log.error("parseIndicatorStoreKey error, key = {}", originStoreKey);
            Cat.logEvent("IndicatorKey", "parseError");
            return Optional.empty();
        }
    }

    /**
     * key的拼接方法：category.template_version
     * 见：<a href="https://km.sankuai.com/page/28308348"/>
     */
    private static String getParamKey(StoreKey originStoreKey) {
        String finalKey = originStoreKey.toString();
        int categoryLineIndex = finalKey.indexOf(".");
        int versionUnderLineIndex = finalKey.lastIndexOf("_");
        finalKey = finalKey.substring(categoryLineIndex + 1, versionUnderLineIndex);
        return finalKey;
    }

    @Data
    public static class IndicatorStoreKey {

        /**
         * 仓库id
         */
        private long warehouseId;

        /**
         * 业务日期
         */
        private LocalDate bizDate;

        /**
         * 人员id
         */
        private long employeeId;

        /**
         * 班次id
         */
        private long shiftId;

        /**
         * 排班开始物理时间（兼容黑户班次）
         */
        private LocalTime shiftStartTime;

        /**
         * 排班结束物理时间（兼容黑户班次）
         */
        private LocalTime shiftEndTime;

        /**
         * 是否在班次内
         */
        private boolean isShiftScheduled;

    }

}
