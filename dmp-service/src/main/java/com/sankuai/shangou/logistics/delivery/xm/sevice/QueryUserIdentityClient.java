package com.sankuai.shangou.logistics.delivery.xm.sevice;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.DxService;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.req.QueryEmpIdentityByEmpIdListReq;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.req.QueryEmpIdentityByMisListReq;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.resp.QueryEmpIdentityByEmpIdListResp;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.resp.QueryEmpIdentityByMisListResp;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.resp.UserIdentity;
import com.sankuai.shangou.logistics.delivery.push.AbstractDxService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 查询用户身份
 * @date 2025-05-21
 */
@Slf4j
@Rhino
public class QueryUserIdentityClient extends AbstractDxService {
    @Autowired
    private DxService dxService;

    @Degrade(rhinoKey = "QueryUserIdentityClient-queryEmpIdByMisList",
            fallBackMethod = "queryEmpIdByMisListFallback",
            timeoutInMilliseconds = 3000)
    public List<Long> queryEmpIdByMisList(List<String> misList) {
        List<Long> uidList = new ArrayList<>();
        try {
            if (CollectionUtils.isEmpty(misList)) {
                return new ArrayList<>();
            }
            QueryEmpIdentityByMisListReq req = new QueryEmpIdentityByMisListReq();
            req.setMisList(misList);
            QueryEmpIdentityByMisListResp resp = dxService.queryEmpIdentityByMisList(getToken(), req);
            if (resp == null || resp.getStatus().getCode() != 0) {
                return uidList;
            }
            if (resp.getData() == null || resp.getData().getData() == null) {
                return uidList;
            }
            resp.getData().getData().values().forEach(userIdentity -> uidList.add(userIdentity.getEmpId()));
        } catch (Exception e) {
            log.error("查询用户身份信息失败", e);
        }
        return uidList;
    }

    @Degrade(rhinoKey = "QueryUserIdentityClient-queryByEmpIds",
            fallBackMethod = "queryByEmpIdsFallback",
            timeoutInMilliseconds = 3000)
    public Map<Long, UserIdentity> queryByEmpIds(List<Long> empIds) {
        try {
            if (CollectionUtils.isEmpty(empIds)) {
                return new HashMap<>();
            }
            QueryEmpIdentityByEmpIdListReq req = new QueryEmpIdentityByEmpIdListReq();
            req.setEmpIdList(empIds);
            QueryEmpIdentityByEmpIdListResp resp = dxService.queryEmpIdentityByEmpIdList(getToken(), req);
            if (resp == null || resp.getStatus().getCode() != 0) {
                return new HashMap<>();
            }
            if (resp.getData() == null || resp.getData().getData() == null) {
                return new HashMap<>();
            }
            return resp.getData().getData();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public List<Long> queryEmpIdByMisListFallback(List<String> misList) {
        log.info("mis查询uid降级");
        return new ArrayList<>();
    }

    public Map<Long, UserIdentity> queryByEmpIdsFallback(List<Long> empIds) {
        log.info("empIds查询用户身份信息降级");
        return new HashMap<>();
    }
}
