package com.sankuai.shangou.logistics.delivery.configure.pojo.model;

import com.sankuai.shangou.logistics.delivery.common.enums.DimensionEnum;
import com.sankuai.shangou.logistics.delivery.enums.OperationModeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 配置模板
 * @date 2025-07-01
 */
@Data
public class ConfigTemplateModel {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 名称
     */
    private String name;

    /**
     * 维度类型
     */
    private DimensionEnum dimensionType;

    /**
     * 经营模式
     */
    private OperationModeEnum operationMode;

    /**
     * 是否同步存量门店
     */
    private Boolean isSyncWhenCreate;

    /**
     * 状态: 0-失效, 1-生效
     */
    private Integer status;

    /**
     * 配置内容
     */
    private List<BatchTaskConfigContent> configContents;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人(牵牛花账号)
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createName;
}
