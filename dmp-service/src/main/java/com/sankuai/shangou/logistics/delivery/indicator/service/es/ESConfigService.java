package com.sankuai.shangou.logistics.delivery.indicator.service.es;

import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.meituan.shangou.saas.order.platform.utils.GsonUtil;
import com.sankuai.meituan.poros.client.PorosRestHighLevelClient;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ContentType;
import org.apache.http.nio.entity.NStringEntity;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/3 15:16
 **/
@Service
@Slf4j
public class ESConfigService {
    @Resource
    private PorosRestHighLevelClient esClientRepository;

    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @throws Exception 异常信息
     */
    public void createIndex(String indexName) throws Exception {
        RestClient restClient = esClientRepository.getLowLevelClient();
        Response response = restClient.performRequest(HttpPut.METHOD_NAME, "/" + indexName);
        Preconditions.checkState(getStatusCode4Response(response) == HttpStatus.SC_OK);
    }

    public int getStatusCode4Response(Response response) {
        return Optional.ofNullable(response).map(Response::getStatusLine).map(StatusLine::getStatusCode)
                .orElse(HttpStatus.SC_BAD_REQUEST);
    }

    public boolean existsIndex(String indexName) throws Exception {
        return existsIndex(Arrays.asList(indexName)).contains(indexName);
    }

    /**
     * 过滤掉不存在的索引
     * Response:
     * [
     * {
     * "index":"city_spu_price_trend"
     * },
     * {
     * "index":"app_saas_poi_trade_sd_v2"
     * },
     * {
     * "index":"shangou_empower_order_test_2020_12_v1.1"
     * }
     * ]
     *
     * @param indexNames 索引名称集合
     * @return true 存在、false不存在
     */
    public List<String> existsIndex(List<String> indexNames) throws Exception {

        if (CollectionUtils.isEmpty(indexNames)) {
            return Collections.emptyList();
        }

        List<String> allIndexes = getAllIndex();
        return Optional.ofNullable(allIndexes).orElse(Collections.emptyList()).stream()
                .filter(index -> Objects.nonNull(index) && indexNames.contains(index))
                .collect(Collectors.toList());
    }

    public boolean existsIndexAlias(String indexName, String alias) throws Exception {
        Set<String> indexAlias = getIndexAlias(indexName);

        return indexAlias.contains(alias);
    }


    public List<String> getAllIndex() throws Exception {

        RestClient restClient = esClientRepository.getLowLevelClient();
        Response response = restClient.performRequest(HttpGet.METHOD_NAME, IndexInfo.INDEX_GET);

        Preconditions.checkState(getStatusCode4Response(response) == HttpStatus.SC_OK);

        if (response == null || response.getEntity() == null) {
            return Collections.emptyList();
        }

        String indexes = EntityUtils.toString(response.getEntity());
        if (StringUtils.isEmpty(indexes)) {
            return Collections.emptyList();
        }
        return Optional.ofNullable(toTypeBean(indexes, new TypeToken<List<IndexInfo>>() {
                }, null)).orElse(Collections.emptyList()).stream()
                .filter(Objects::nonNull)
                .map(IndexInfo::getIndex)
                .collect(Collectors.toList());
    }

    /**
     * 索引信息
     */
    @Data
    static class IndexInfo {

        /**
         * 以json的形式返回所有的索引名称
         */
        public static final String INDEX_GET = "/_cat/indices?v&h=index&format=json";

        private String index;
    }

    /**
     * 根据索引名称获取索引所有别名
     * Response:
     * {
     * "my_index_v1":{
     * "aliases":{
     * "my_index":{}
     * }
     * }
     * }
     *
     * @param indexName 索引名称
     * @return 索引对应的所有别名
     */
    public Set<String> getIndexAlias(String indexName) throws Exception {
        RestClient restClient = esClientRepository.getLowLevelClient();
        Response response = restClient.performRequest(HttpGet.METHOD_NAME, MessageFormat.format("/{0}/_alias/*",
                indexName));
        Preconditions.checkState(getStatusCode4Response(response) == HttpStatus.SC_OK);
        if (response == null || response.getEntity() == null) {
            return Collections.emptySet();
        }

        String content = EntityUtils.toString(response.getEntity());
        if (StringUtils.isEmpty(content)) {
            return Collections.emptySet();
        }
        Map<String, Map<String, Map<String, Map<String, String>>>> indexAliasMap = toTypeBean(content,
                new TypeToken<Map<String, Map<String, Map<String, Map<String, String>>>>>() {
                }, null);
        return Optional.ofNullable(indexAliasMap)
                .filter(allAliasMap -> allAliasMap.containsKey(indexName))
                .map(allAliasMap -> allAliasMap.get(indexName))
                .map(aliasesMap -> aliasesMap.get("aliases"))
                .map(Map::keySet).orElse(Collections.emptySet());
    }


    /**
     * 批量新增或者移除索引别名(单索引多别名)
     *
     * @param indexName       索引名称
     * @param addAliasList    需要新增的别名
     * @param removeAliasList 需要删除的别名
     * @throws Exception 修改别名异常
     */
    public void modifyAlias(String indexName, List<String> addAliasList, List<String> removeAliasList)
            throws Exception {
        modifyAlias(ImmutableMap.of(indexName, addAliasList), ImmutableMap.of(indexName, removeAliasList));
    }

    /**
     * 批量新增或者移除索引别名(多索引多别名)
     * Request:
     * {
     * "actions": [
     * { "remove": { "index": "my_index_v1", "alias": "my_index" }},
     * { "add":    { "index": "my_index_v2", "alias": "my_index" }}
     * ]
     * }
     *
     * @param addIndexAliasMap    新增的索引和其对应的别名
     * @param removeIndexAliasMap 删除的索引和其对应的别名
     * @throws Exception 修改别名异常
     */
    public void modifyAlias(Map<String, List<String>> addIndexAliasMap, Map<String, List<String>> removeIndexAliasMap)
            throws Exception {
        ModifyAlias modifyAlias = ModifyAlias.builder();
        Optional.ofNullable(addIndexAliasMap).orElse(Collections.emptyMap())
                .forEach(modifyAlias::addAlias);
        Optional.ofNullable(removeIndexAliasMap).orElse(Collections.emptyMap())
                .forEach(modifyAlias::removeAlias);
        if (!modifyAlias.check()) {
            return;
        }
        RestClient restClient = esClientRepository.getLowLevelClient();
        HttpEntity entity = new NStringEntity(GsonUtil.toJSONString(modifyAlias), ContentType.APPLICATION_JSON);
        Response response = restClient.performRequest(HttpPost.METHOD_NAME, ModifyAlias.ALIAS_POST,
                Collections.emptyMap(), entity);
        Preconditions.checkState(getStatusCode4Response(response) == HttpStatus.SC_OK);
    }

    public static <T> T toTypeBean(String jsonStr, TypeToken<T> typeToken, T defaultValue) {
        try {
            Gson gson = new Gson();
            if (StringUtils.isEmpty(jsonStr) || Objects.isNull(typeToken)) {
                return defaultValue;
            }
            return Optional.<T>ofNullable(gson.fromJson(jsonStr, typeToken.getType())).orElse(defaultValue);
        } catch (Exception e) {
            log.error("GsonUtil.toTypeToken exception jsonStr:{}", jsonStr, e);
        }
        return defaultValue;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class Alias {

        private String index;

        private String alias;
    }

    static class ModifyAlias {

        public static final String ALIAS_POST = "/_aliases";

        private ModifyAlias() {

        }

        private List<AbstractOperateAlias> actions;

        public static ModifyAlias builder() {
            ModifyAlias modifyAlias = new ModifyAlias();
            modifyAlias.actions = new ArrayList<>();
            return modifyAlias;
        }

        protected boolean check(String indexName, String aliasName) {
            return StringUtils.isNotEmpty(indexName) && StringUtils.isNotEmpty(aliasName);
        }

        /**
         * 构造新增索引别名 action
         *
         * @param indexName 索引名称
         * @param aliasName 索引别名
         * @return 别名修改对象
         */
        public ModifyAlias addAlias(String indexName, String aliasName) {
            if (check(indexName, aliasName)) {
                AddAlias addAlias = new AddAlias();
                addAlias.buildAliasToSet(indexName, aliasName);
                actions.add(addAlias);
            }
            return this;
        }

        public ModifyAlias addAlias(String indexName, List<String> aliasNames) {
            Optional.ofNullable(aliasNames)
                    .orElse(Collections.emptyList())
                    .forEach(aliasName -> addAlias(indexName, aliasName));
            return this;
        }

        /**
         * 构造删除索引别名 action
         *
         * @param indexName 索引名称
         * @param aliasName 索引别名
         * @return 别名修改对象
         */
        public ModifyAlias removeAlias(String indexName, String aliasName) {
            if (check(indexName, aliasName)) {
                RemoveAlias removeAlias = new RemoveAlias();
                removeAlias.buildAliasToSet(indexName, aliasName);
                actions.add(removeAlias);
            }
            return this;
        }

        public ModifyAlias removeAlias(String indexName, List<String> aliasNames) {
            Optional.ofNullable(aliasNames)
                    .orElse(Collections.emptyList())
                    .forEach(aliasName -> removeAlias(indexName, aliasName));
            return this;
        }

        public boolean check() {
            return CollectionUtils.isNotEmpty(actions);
        }

        /**
         * 别名操作基类、外部封装统一调用buildAliasToSet、且实现setAlias
         */
        static abstract class AbstractOperateAlias {

            /**
             * 构建别名信息并且设置值
             *
             * @param indexName
             * @param aliasName
             */
            public void buildAliasToSet(String indexName, String aliasName) {
                Alias alias = new Alias();
                alias.setIndex(indexName);
                alias.setAlias(aliasName);
                setAlias(alias);
            }

            /**
             * 设置别名
             *
             * @param alias 别名
             */
            protected abstract void setAlias(Alias alias);
        }

        /**
         * 构造删除别名数据
         */
        static class RemoveAlias extends AbstractOperateAlias {

            private Alias remove;

            public Alias getRemove() {
                return remove;
            }

            public void setRemove(Alias remove) {
                this.remove = remove;
            }

            @Override
            protected void setAlias(Alias alias) {
                setRemove(alias);
            }
        }

        /**
         * 构造添加别名数据
         */
        static class AddAlias extends AbstractOperateAlias {

            private Alias add;

            public Alias getAdd() {
                return add;
            }

            public void setAdd(Alias add) {
                this.add = add;
            }

            @Override
            protected void setAlias(Alias alias) {
                setAdd(alias);
            }
        }
    }
}
