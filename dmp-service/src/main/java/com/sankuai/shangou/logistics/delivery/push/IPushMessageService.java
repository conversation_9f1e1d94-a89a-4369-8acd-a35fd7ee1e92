package com.sankuai.shangou.logistics.delivery.push;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.shangou.logistics.delivery.enums.FreeMarkTemplateEnum;
import com.sankuai.shangou.logistics.delivery.push.mq.msg.FulfillmentDXPushMessage;

public interface IPushMessageService {

    ConsumeStatus doHandler(FulfillmentDXPushMessage pushMessage);

    FreeMarkTemplateEnum getTemplateEnum();

}
