package com.sankuai.shangou.logistics.delivery.shippingarea.service;
;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiIdListQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.BatchQueryPoiShippingAreaRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.PoiShippingAreaInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.QueryPoiShippingAreaResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPoiShippingThriftService;
import com.sankuai.shangou.commons.utils.rpc.RpcInvokeUtils;
import com.sankuai.shangou.logistics.delivery.poi.utils.MccUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.dto.ShippingAreaRelationDto;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.ChannelTypeEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.StatusEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.repository.ShippingAreaRelationRepository;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.IGeoUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.UUIDUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.locationtech.jts.geom.Polygon;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/21 15:01
 **/
@Service
@Slf4j
public class ShippingAreaRelationService {

    @Resource
    private ShippingAreaRelationRepository shippingAreaRelationRepository;

    @Resource
    private PoiThriftService poiThriftService;

    @Resource
    private ChannelPoiShippingThriftService.Iface channelPoiShippingThriftService;

    public List<ShippingAreaRelationDto> queryShippingAreaByShippingAreaIds(List<Long> shippingAreaIds) {
        if (CollectionUtils.isEmpty(shippingAreaIds)) {
            return Collections.emptyList();
        }

        return shippingAreaRelationRepository.query(shippingAreaIds);
    }

    public void addShippingAreaRelations(List<ShippingAreaRelationDto> shippingAreaRelationDtos) {
        if(CollectionUtils.isEmpty(shippingAreaRelationDtos)) {
            return;
        }

        shippingAreaRelationRepository.save(shippingAreaRelationDtos);
    }

    public void deleteShippingAreaRelations(Long tenantId, Long storeId, List<Long> areaIds) {
        if (CollectionUtils.isEmpty(areaIds)) {
            return;
        }

        shippingAreaRelationRepository.delete(tenantId, storeId, areaIds);
    }

    public void initShippingArea(Long tenantId, List<Long> storeIds) {
        List<PoiInfoDto> needInitStoreList;

        //查门店信息
        if (CollectionUtils.isEmpty(storeIds)) {
            PoiListResponse poiListResponse = RpcInvokeUtils.rpcInvokeTemplate(() -> poiThriftService.queryTenantPoiList(tenantId),
                    "查询租户门店列表失败",
                    resp -> resp.getStatus().getCode(), resp -> resp.getStatus().getMessage());
            needInitStoreList = poiListResponse.getPoiList().stream().filter(poiInfoDto -> poiInfoDto.getPoiStatus() == 1).collect(Collectors.toList());
        } else {
            PoiMapResponse poiMapResponse = RpcInvokeUtils.rpcInvokeTemplate(() -> poiThriftService.queryTenantPoiInfoMapByPoiIds(storeIds, tenantId),
                    "查询门店信息失败",
                    resp -> resp.getStatus().getCode(), resp -> resp.getStatus().getMessage());
            needInitStoreList = new ArrayList<>(poiMapResponse.getPoiInfoMap().values());
        }

        //查未初始化的门店
        List<Long> initializedStoreIds = shippingAreaRelationRepository.queryInitializedStoreIds(tenantId);
        List<Long> unInitStoreList = needInitStoreList.stream()
                .map(PoiInfoDto::getPoiId)
                .filter(poiId -> !initializedStoreIds.contains(poiId))
                .filter(poiId -> !MccUtils.getNotInitShippingAreaRelationStoreIds().contains(poiId)) //排除不需要初始化的门店
                .collect(Collectors.toList());

        //初始化
        ArrayList<ShippingAreaRelationDto> initRelationDtos = new ArrayList<>();
        List<List<Long>> lists = Lists.partition(unInitStoreList, 20);
        for (List<Long> subList: lists) {
            //查询两个渠道的配送范围
            Map<Long, List<PoiShippingAreaInfoDTO>> mtChannelShippingAreaMap =
                    batchQueryShippingInfo(tenantId, subList, 100)
                            .getPoiShippingInfoDTOList()
                            .stream()
                            .collect(Collectors.groupingBy(PoiShippingAreaInfoDTO::getPoiId));
            Map<Long, List<PoiShippingAreaInfoDTO>> dhChannelShippingAreaMap =
                    batchQueryShippingInfo(tenantId, subList, 700)
                            .getPoiShippingInfoDTOList()
                            .stream()
                            .collect(Collectors.groupingBy(PoiShippingAreaInfoDTO::getPoiId));

            //比较两个渠道的配送范围 范围相同&&时段相同 ==> 创建关联关系
            mtChannelShippingAreaMap.forEach((storeId, mtAreaList) -> {
                mtAreaList.forEach(mtArea -> {
                    List<PoiShippingAreaInfoDTO> dhAreas = dhChannelShippingAreaMap.getOrDefault(storeId, Collections.emptyList());
                    for (PoiShippingAreaInfoDTO dhArea : dhAreas) {
                        if (areaIsSame(dhArea, mtArea) && periodIsSame(dhArea, mtArea)) {
                            initRelationDtos.addAll(buildShippingAreaRelations(tenantId, storeId, dhArea, mtArea));
                            dhAreas.remove(dhArea);
                            break;
                        }
                    }
                });
            });
        }

        //插入关联关系
        shippingAreaRelationRepository.save(initRelationDtos);
    }

    public void resetShippingAreaRelations(Long tenantId, Long storeId) {
        //查询两个渠道的配送范围
        List<PoiShippingAreaInfoDTO> mtChannelShippingAreaList =
                batchQueryShippingInfo(tenantId, Collections.singletonList(storeId), ChannelTypeEnum.MEITUAN.getChannelId())
                        .getPoiShippingInfoDTOList();
        List<PoiShippingAreaInfoDTO> dhChannelShippingAreaList =
                batchQueryShippingInfo(tenantId, Collections.singletonList(storeId), ChannelTypeEnum.MT_DRUNK_HORSE.getChannelId())
                        .getPoiShippingInfoDTOList();

        ArrayList<ShippingAreaRelationDto> resetRelations = new ArrayList<>();
        //比较两个渠道的配送范围 范围相同&&时段相同 ==> 创建关联关系
        mtChannelShippingAreaList.forEach(mtArea -> {
                for (PoiShippingAreaInfoDTO dhArea : dhChannelShippingAreaList) {
                    if (areaIsSame(dhArea, mtArea) && periodIsSame(dhArea, mtArea)) {
                        resetRelations.addAll(buildShippingAreaRelations(tenantId, storeId, dhArea, mtArea));
                        dhChannelShippingAreaList.remove(dhArea);
                        break;
                    }
                }
            });

        log.info("resetRelations: {}", JSON.toJSONString(resetRelations));
        shippingAreaRelationRepository.reset(tenantId, storeId, resetRelations);
    }

    private List<ShippingAreaRelationDto> buildShippingAreaRelations(Long tenantId, Long storeId,
                                                               PoiShippingAreaInfoDTO dhArea,
                                                               PoiShippingAreaInfoDTO mtArea) {
        long relationId = UUIDUtil.uuidToLong(UUID.randomUUID());

        ShippingAreaRelationDto dhRelation = new ShippingAreaRelationDto();
        dhRelation.setRelationId(relationId);
        dhRelation.setAreaId(dhArea.getShippingAreaId());
        dhRelation.setStatus(StatusEnum.VALID.getCode());
        dhRelation.setChannelId(DynamicChannelType.MT_DRUNK_HORSE.getChannelId());
        dhRelation.setStoreId(storeId);
        dhRelation.setTenantId(tenantId);
        dhRelation.setCreateTime(LocalDateTime.now());

        ShippingAreaRelationDto mtRelation = new ShippingAreaRelationDto();
        mtRelation.setRelationId(relationId);
        mtRelation.setAreaId(mtArea.getShippingAreaId());
        mtRelation.setStatus(StatusEnum.VALID.getCode());
        mtRelation.setChannelId(DynamicChannelType.MEITUAN.getChannelId());
        mtRelation.setStoreId(storeId);
        mtRelation.setTenantId(tenantId);
        mtRelation.setCreateTime(LocalDateTime.now());

        return Arrays.asList(dhRelation, mtRelation);
    }

    private Boolean areaIsSame(PoiShippingAreaInfoDTO area1, PoiShippingAreaInfoDTO area2) {
        if (area1 == null || area2 == null) {
            return false;
        }

        Polygon polygon1 = IGeoUtils.convertDeliveryArea(area1.getArea());
        Polygon polygon2 = IGeoUtils.convertDeliveryArea(area2.getArea());

        return Objects.equals(polygon1, polygon2);
    }

    private Boolean periodIsSame(PoiShippingAreaInfoDTO area1, PoiShippingAreaInfoDTO area2) {
        if (area1 == null || area2 == null) {
            return false;
        }

        return Objects.equals(area1.getTimeRangeBegin(), area2.getTimeRangeBegin())
                && Objects.equals(area1.getTimeRangeEnd(), area2.getTimeRangeEnd());
    }

    private QueryPoiShippingAreaResponse batchQueryShippingInfo(Long tenantId, List<Long> storeIds, Integer channelId) {
        return RpcInvokeUtils.rpcInvokeTemplate(
                () -> {
                    try {
                        return channelPoiShippingThriftService.batchQueryShippingInfo(new BatchQueryPoiShippingAreaRequest(tenantId, storeIds, channelId));
                    } catch (TException e) {
                        throw new RuntimeException(e);
                    }
                },
                "批量查询渠道配送范围失败",
                QueryPoiShippingAreaResponse::getCode,
                QueryPoiShippingAreaResponse::getMsg);
    }
}
