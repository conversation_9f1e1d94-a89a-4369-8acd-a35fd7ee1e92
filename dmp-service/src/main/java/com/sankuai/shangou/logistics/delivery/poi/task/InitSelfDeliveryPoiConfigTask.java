package com.sankuai.shangou.logistics.delivery.poi.task;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/9/1 15:55
 **/

@Slf4j
@CraneConfiguration
@Service
public class InitSelfDeliveryPoiConfigTask {
    @Resource
    private SelfDeliveryPoiConfigService selfDeliveryPoiConfigService;

    @Crane("init-self-delivery-poi-config-task")
    public void initConfig(Long tenantId) {
        log.info("sync-delivery-poi-config-task start, tenantId: {}", tenantId);
        int operatePoiNum = selfDeliveryPoiConfigService.initConfig(tenantId);
        log.info("sync-delivery-poi-config-task end, operatorPoiNum: {}", operatePoiNum);
    }
}
