package com.sankuai.shangou.logistics.delivery.configure.pojo.model;

import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.shangou.logistics.delivery.configure.enums.BatchTaskStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 配置任务明细
 * @date 2025-07-02
 */
@Data
public class ConfigTaskItemModel {
    /**
     * 主键
     */
    private Long id;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 渠道类型
     */
    private DynamicChannelType channelType;

    /**
     * 渠道门店id
     */
    private String channelStoreId;

    /**
     * 状态: 0-初始化 1-处理中 2-已完成 3-处理失败
     */
    private BatchTaskStatusEnum status;

    /**
     * 失败原因(ext)
     */
    private String failReason;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 完成时间
     */
    private LocalDateTime finishedAt;
}
