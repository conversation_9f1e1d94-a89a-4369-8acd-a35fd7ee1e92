package com.sankuai.shangou.logistics.delivery.configure.pojo.vo;

import com.sankuai.shangou.logistics.delivery.configure.enums.DeliveryConfigTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfigItemDetailVo {
    /**
     * 配置类型
     * @see DeliveryConfigTypeEnum
     */
    private Integer type;
    /**
     * 配置内容
     * 分渠道的配置：key为渠道id， value为配置内容
     */
    private Map<String, Object> content;
}
