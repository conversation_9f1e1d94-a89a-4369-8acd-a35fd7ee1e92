package com.sankuai.shangou.logistics.delivery.indicator.task;

import com.cip.crane.client.common.util.ExecutionContext;
import com.cip.crane.client.common.util.ShardItemsContext;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiListResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.retry.RetryTemplateUtil;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.logistics.delivery.constants.Constants;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorConstants;
import com.sankuai.shangou.logistics.delivery.indicator.service.IndicatorService;
import com.sankuai.shangou.logistics.delivery.indicator.service.OmTimetable;
import com.sankuai.shangou.logistics.delivery.indicator.task.param.FetchBaseIndicatorParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.retry.RetryCallback;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-06-30
 * @email <EMAIL>
 */
@Slf4j
@CraneConfiguration
@Service
public class FetchBaseIndicatorCraneTask {

    @Resource
    private PoiThriftService poiThriftService;
    @Resource
    private IndicatorService indicatorService;

    @Crane("fetch-base-indicator-task")
    public void fetchBaseIndicatorData(FetchBaseIndicatorParam param) {
        log.info("fetchBaseIndicatorData start, param = {}", param);
        long tenantId = Objects.nonNull(param.getTenantId()) ? param.getTenantId() : Constants.DEFAULT_TENANT_ID;

        LocalDateTime fetchTime = TimeUtils.convertDateToLocalDateTime(ExecutionContext.getScheduleTime());
        //判断是否在营业时间，不在则不会继续
        //目前是4～8都不跑数据
        if (fetchTime.toLocalTime().isBefore(IndicatorConstants.startTime) && fetchTime.toLocalTime().isAfter(IndicatorConstants.closeTime)) {
            return;
        }

        try {
            List<Long> poiIdList = getPoiIdList(tenantId);
            //本机分配到分片items,注意items时从0开始的
            List<Integer> shardItems = ShardItemsContext.getShardItems();
            //分片总数
            int shardCount = ShardItemsContext.getShardCount();
            if (CollectionUtils.isEmpty(poiIdList) || CollectionUtils.isEmpty(shardItems)) {
                log.warn("empty poiList or no shardItems in this shard");
                return;
            }

            //取模按poiId分配
            List<Long> shardPoiIdList = IListUtils.nullSafeFilterElement(
                    poiIdList,
                    poiId -> {
                        long mod = poiId % shardCount;
                        return shardItems.contains(Long.valueOf(mod).intValue());
                    }
            );

            if (CollectionUtils.isEmpty(shardPoiIdList)) {
                log.warn("no shard poi found");
                return;
            }

            // TODO: 4.7.23 暂时不提供回刷功能
            indicatorService.syncIndicator(
                    tenantId,
                    fetchTime,
                    fetchTime,
                    shardPoiIdList
            );

        } catch (Exception e) {
            log.error("invoke fetchBaseIndicatorData throws exp", e);
            throw new SystemException("invoke fetchBaseIndicatorData error");
        }
    }

    // TODO: 2.7.23 这里可能需要推动租户侧缓存数据，上线后观察下调用时间
    private List<Long> getPoiIdList(long tenantId) throws Exception {
        List<PoiInfoDto> poiInfoDtoList = RetryTemplateUtil.defaultRetryTemplate.execute(
                (RetryCallback<List<PoiInfoDto>, Exception>) context -> {
                    PoiListResponse poiListResponse = poiThriftService.queryTenantPoiList(tenantId);
                    if (!poiListResponse.getStatus().isSuccess()) {
                        throw new SystemException("invoke poiThriftService.queryTenantPoiList error");
                    }
                    return poiListResponse.getPoiList();
                }
        );
        return IListUtils
                .nullSafeStream(poiInfoDtoList)
                .map(PoiInfoDto::getPoiId)
                .collect(Collectors.toList());
    }

}
