package com.sankuai.shangou.logistics.delivery.shippingarea.constant;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class Const {
    private Const() {

    }

    public static final int NATION_CODE = -1;


    public static final Long INVALID_LAYER_ID = -1L;
    public static final Long INVALID_LOI_ID = -1L;
    public static final Long INVALID_MT_SHIPPING_AREA_ID = 0L;
    /**
     * B端商家的appId
     */
    public static final Integer SAAS_B_APP_ID = 3;

    public static final int AUTH_TYPE_OF_POI = 1;

    /**
     * 默认配送范围id
     */
    public static final String DEFAULT_APP_SHIPPING_CODE = "1";

    /**
     * 最大配送费
     */
    public static final BigDecimal MAX_SHIPPING_FEE = new BigDecimal(Integer.MAX_VALUE);

    /**
     * 地图开放平台接口成功返回状态码 状态码说明参考:https://km.sankuai.com/page/1061208920
     */
    public static final int MAP_OPEN_API_SUCCESS_CODE = 200;

    /**
     * 限频
     */
    public static final int MAP_OPEN_API_RATE_LIMITED = 120;

    /**
     * 访问已超出日访问量
     */
    public static final int MAP_OPEN_API_DAY_DAILY_TRAFFIC_EXCEEDED_LIMIT = 121;

    /**
     * 起终点距离过长
     */
    public static final int MAP_OPEN_API_ROUTE_TOO_LONG_CODE = 603;

    /**
     * 起终点附近没有路
     */
    public static final int MAP_OPEN_API_HAS_NO_ROUTE_AROUND_COORDINATION = 604;

    /**
     * 没有路径
     */
    public static final int MAP_OPEN_API_NO_ROUTE_BETWEEN_COORDINATION = 605;

    /**
     * 起终点坐标错误
     */
    public static final int MAP_OPEN_API_COORDINATION_ERROR = 607;

    /**
     * 路径规划距离过近
     */
    public static final int MAP_OPEN_API_ROUTE_TOO_SHORT = 608;

    public static final Set<Integer> CAN_BE_IGNORED_MAP_OPEN_API_ERROR_CODES =
            new HashSet<>(Arrays.asList(MAP_OPEN_API_RATE_LIMITED, MAP_OPEN_API_DAY_DAILY_TRAFFIC_EXCEEDED_LIMIT,
                    MAP_OPEN_API_ROUTE_TOO_LONG_CODE, MAP_OPEN_API_HAS_NO_ROUTE_AROUND_COORDINATION,
                    MAP_OPEN_API_NO_ROUTE_BETWEEN_COORDINATION, MAP_OPEN_API_COORDINATION_ERROR, MAP_OPEN_API_ROUTE_TOO_SHORT));
}
