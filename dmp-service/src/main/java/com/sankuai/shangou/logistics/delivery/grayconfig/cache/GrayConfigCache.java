package com.sankuai.shangou.logistics.delivery.grayconfig.cache;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/5/16 15:42
 **/
@Service
public class GrayConfigCache {
    @Resource(name = "businessRedisStoreClient")
    private RedisStoreClient businessRedisStoreClient;

    private String CATEGORY_NAME = "gray_config_cache";

    @MethodLog(logResponse = true, logRequest = true)
    public Optional<Boolean> get(String grayKey, Long storeId) {
        StoreKey storeKey = new StoreKey(CATEGORY_NAME, grayKey + "_" + storeId);
        Boolean result = businessRedisStoreClient.get(storeKey);
        if (Objects.nonNull(result)) {
            return Optional.of(result);
        }

        return Optional.empty();
    }

    @MethodLog(logResponse = true, logRequest = true)
    public Boolean set(String grayKey, Long storeId, Boolean val) {
        StoreKey storeKey = new StoreKey(CATEGORY_NAME, grayKey + "_" + storeId);
        return businessRedisStoreClient.set(storeKey, val);
    }

}
