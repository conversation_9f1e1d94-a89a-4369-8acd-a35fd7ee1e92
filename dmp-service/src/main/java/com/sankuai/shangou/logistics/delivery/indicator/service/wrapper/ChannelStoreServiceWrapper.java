package com.sankuai.shangou.logistics.delivery.indicator.service.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiDetailInfosResponse;
import com.sankuai.drunkhorsemgmt.labor.exception.SystemException;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2023-07-26
 * @email <EMAIL>
 */
@Slf4j
@Component
@Rhino
public class ChannelStoreServiceWrapper {

    @Resource
    private ChannelPoiManageThriftService channelPoiManageThriftService;

    @Degrade(rhinoKey = "ChannelStoreServiceWrapper-queryPoiDetailInfoByPoiIds",
            fallBackMethod = "queryPoiDetailInfoByPoiIdsFallback",
            timeoutInMilliseconds = 3000)
    public Map<Long, List<ChannelPoiInfoDTO> > queryPoiDetailInfoByPoiIds(long tenantId, List<Long> poiIds) {
        PoiDetailInfosResponse response = channelPoiManageThriftService.queryPoiDetailInfoByPoiIds(tenantId, poiIds);
        log.info("invoke channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiIds, poiId = {}, response = {}", poiIds, response);
        if (!response.getStatus().isSuccess()) {
            throw new SystemException("invoke channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiIds error");
        }
        return IListUtils.nullSafeAndOverrideCollectToMap(response.getPoiDetailInfoDTOs(), PoiDetailInfoDTO::getPoiId, PoiDetailInfoDTO::getChannelPoiInfoList);
    }

    private Map<Long, PoiDetailInfoDTO> queryPoiDetailInfoByPoiIdsFallback(long tenantId, List<Long> poiIds) {
        log.warn("queryPoiDetailInfoByPoiIds fallback, will return empty map");
        return Maps.newHashMap();
    }

}
