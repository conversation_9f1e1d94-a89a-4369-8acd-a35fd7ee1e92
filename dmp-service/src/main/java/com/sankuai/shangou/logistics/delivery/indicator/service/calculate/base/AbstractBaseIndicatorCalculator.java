package com.sankuai.shangou.logistics.delivery.indicator.service.calculate.base;

import com.google.common.collect.Lists;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ShiftAndScheduleCountDTO;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BaseIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.repository.dbo.IndicatorDoc;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.indicator.utils.IndicatorSquirrelKeyUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-07-03
 * @email <EMAIL>
 */
public abstract class AbstractBaseIndicatorCalculator implements BaseIndicatorCalculator {

    private static final String DEFAULT_FRAGMENT_NAME = "默认库位";

    public List<IndicatorDoc> buildByShiftGroupIndicatorDocList(Map.Entry<Long, List<IndicatorSquirrelKeyUtils.IndicatorStoreKey>> entry,
                                                                Map<Long, ShiftAndScheduleCountDTO> idShiftDTOMap, BigDecimal calcResult) {
        BaseIndicatorEnum supportIndicator = getSupportIndicator();

        IndicatorDoc indicatorDoc = new IndicatorDoc();
        indicatorDoc.setCode(supportIndicator.getIndicatorCode());
        indicatorDoc.setLogisticsUnitId(entry.getValue().get(0).getWarehouseId());
        indicatorDoc.setValue(calcResult);
        if (entry.getKey().equals(IndicatorSquirrelKeyUtils.UNSCHEDULED_SHIFT_ID)) {
            indicatorDoc.setProperty(StringUtils.EMPTY);
            indicatorDoc.setFragmentName(DEFAULT_FRAGMENT_NAME);
        } else {
            ShiftAndScheduleCountDTO shiftDTO = idShiftDTOMap.get(entry.getKey());
            if (Objects.isNull(shiftDTO) && !Objects.equals(entry.getKey(), IndicatorSquirrelKeyUtils.UNSCHEDULED_SHIFT_ID)) {
                // TODO: 4.7.23 说明班次被删了，这时候需要处理白->黑的情况么？
                return Lists.newArrayList();
            } else if (Objects.nonNull(shiftDTO)){
                indicatorDoc.setProperty(shiftDTO.getColor());
                indicatorDoc.setFragmentName(shiftDTO.getName());
            }
        }
        return Lists.newArrayList(indicatorDoc);
    }


    public List<IndicatorDoc> buildSingIndicaotrGroupIndicatorDocList(long warehouseId, BigDecimal calcResult) {
        BaseIndicatorEnum supportIndicator = getSupportIndicator();
        //按单聚合不需要按班次拆分
        IndicatorDoc indicatorDoc = new IndicatorDoc();
        indicatorDoc.setCode(supportIndicator.getIndicatorCode());
        indicatorDoc.setLogisticsUnitId(warehouseId);
        indicatorDoc.setValue(calcResult);
        return Lists.newArrayList(indicatorDoc);
    }


    protected List<Long> getScheduledEmployeeIds(@NotNull List<IndicatorSquirrelKeyUtils.IndicatorStoreKey> indicatorStoreKeys) {
        return indicatorStoreKeys.stream().filter(IndicatorSquirrelKeyUtils.IndicatorStoreKey::isShiftScheduled).map(IndicatorSquirrelKeyUtils.IndicatorStoreKey::getEmployeeId).collect(Collectors.toList());
    }

    protected List<Long> getScheduledAccountIds(@NotNull List<IndicatorSquirrelKeyUtils.IndicatorStoreKey> indicatorStoreKeys,
                                                Map<Long, Long> empIdAccountIdMap) {
        List<Long> employeeIds = getScheduledEmployeeIds(indicatorStoreKeys);
        return IListUtils.nullSafeStream(employeeIds)
                .filter(empIdAccountIdMap::containsKey)
                .map(empIdAccountIdMap::get)
                .collect(Collectors.toList());
    }

    protected List<Long> getUnscheduledEmployeeIds(@NotNull List<IndicatorSquirrelKeyUtils.IndicatorStoreKey> indicatorStoreKeys) {
        return indicatorStoreKeys.stream().filter(indicatorStoreKey -> !indicatorStoreKey.isShiftScheduled()).map(IndicatorSquirrelKeyUtils.IndicatorStoreKey::getEmployeeId).collect(Collectors.toList());
    }

    protected List<Long> getUnscheduledAccountIds(@NotNull List<IndicatorSquirrelKeyUtils.IndicatorStoreKey> indicatorStoreKeys,
                                                  Map<Long, Long> empIdAccountIdMap) {
        List<Long> unscheduledEmployeeIds = getUnscheduledEmployeeIds(indicatorStoreKeys);
        return IListUtils.nullSafeStream(unscheduledEmployeeIds)
                .filter(empIdAccountIdMap::containsKey)
                .map(empIdAccountIdMap::get)
                .collect(Collectors.toList());
    }

}
