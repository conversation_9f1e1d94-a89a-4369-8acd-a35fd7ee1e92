package com.sankuai.shangou.logistics.delivery.questionnaire.wrapper;

import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListViewIdConditionResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.utils.rpc.RpcInvokeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/15 12:08
 **/
@Service
@Slf4j
public class OrderSystemClient {

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    public List<OCMSOrderVO> queryOCMSOrderList(List<Pair<Integer, String>> bizTypeViewOrderIdPairs) {
        OCMSListViewIdConditionRequest request = new OCMSListViewIdConditionRequest();
        request.setViewIdConditionList(bizTypeViewOrderIdPairs.stream().map(pair -> new ViewIdCondition(pair.getLeft(), pair.getRight())).collect(Collectors.toList()));
        log.info("invoke ocmsQueryThriftService.queryOrderByViewIdCondition start, request: {}", request);
        OCMSListViewIdConditionResponse response = RpcInvokeUtils.rpcInvokeTemplate(() -> ocmsQueryThriftService.queryOrderByViewIdCondition(request),
                "查询订单信息失败", resp -> resp.getStatus().getCode(), resp -> resp.getStatus().getMessage());
        return response.getOcmsOrderList();
    }
}
