package com.sankuai.shangou.logistics.delivery.push.freemark.constants;

import com.google.common.collect.ImmutableMap;
import com.sankuai.shangou.logistics.delivery.enums.FreeMarkTemplateEnum;

import java.util.Map;

public class TemplateMappingCons {

    private static final String DH= "dh/";

    private static final Map<FreeMarkTemplateEnum, String> DX_TEMPLATE_MAPPING = buildDXTemplateMapping();

    private static Map<FreeMarkTemplateEnum, String> buildDXTemplateMapping() {
        return ImmutableMap.<FreeMarkTemplateEnum, String>builder()
//                .put(FreeMarkTemplateEnum.DH_WILL_AUTO_CANCEL_ORDER,DH+"delivery-will-cancel-order.ftl")
                .build();
    }

    public static String getDXTemplateByEnum(FreeMarkTemplateEnum templateEnum) {
        if (templateEnum == null) {
            return null;
        }
        return DX_TEMPLATE_MAPPING.get(templateEnum);
    }

}

