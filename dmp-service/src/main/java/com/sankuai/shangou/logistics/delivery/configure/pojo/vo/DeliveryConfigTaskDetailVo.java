package com.sankuai.shangou.logistics.delivery.configure.pojo.vo;

import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.enums.DeliveryConfigTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-03
 */
@Data
public class DeliveryConfigTaskDetailVo {

    private Long taskId;

    private String taskName;

    /**
     * 是否修改存量门店
     */
    private Boolean isSyncWhenSave;

    /**
     * 门店列表
     */
    private List<PoiVo> poiList;

    /**
     * 配置类型
     *
     * @see DeliveryConfigTypeEnum code
     */
    private List<Integer> configTypeList;

    /**
     * 配送平台配置
     */
    private List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> deliveryPlatformConfig;

    /**
     * 自配送配置
     */
    private DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfig;


}
