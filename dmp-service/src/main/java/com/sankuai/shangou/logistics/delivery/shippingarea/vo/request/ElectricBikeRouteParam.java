package com.sankuai.shangou.logistics.delivery.shippingarea.vo.request;

import com.meituan.linz.boot.util.Assert;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(
        description = "电单车路线规划",
        authors = {
                "daiyuan"
        }
)
@Getter
@Setter
public class ElectricBikeRouteParam {

    @FieldDoc(
            description = "在批量接口中，返回可能乱序，用于唯一标识一组起终点",
            requiredness = Requiredness.OPTIONAL
    )
    private String serialId;

    @FieldDoc(
            description = "起始点坐标"
    )
    private String originCoordinate;

    @FieldDoc(
            description = "终点坐标"
    )
    private String destinationCoordinate;

    public void validate() {
        Assert.throwIfTrue(StringUtils.isAnyBlank(this.destinationCoordinate, this.originCoordinate), "坐标不能为空");
    }
}
