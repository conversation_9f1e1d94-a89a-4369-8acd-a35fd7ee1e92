package com.sankuai.shangou.logistics.delivery.tag.wrapper;

import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.goodscenter.dto.GoodsSkuRelationDto;
import com.meituan.shangou.goodscenter.dto.TenantGoodsDetailDto;
import com.meituan.shangou.goodscenter.request.DepotGoodsBatchQueryRequest;
import com.meituan.shangou.goodscenter.request.GoodsSkuRelationSkuIdQueryRequest;
import com.meituan.shangou.goodscenter.request.TenantGoodsIdQueryRequest;
import com.meituan.shangou.goodscenter.response.DepotGoodsDetailListResponse;
import com.meituan.shangou.goodscenter.response.GoodsSkuRelationListResponse;
import com.meituan.shangou.goodscenter.response.TenantGoodsDetailListResponse;
import com.meituan.shangou.goodscenter.thrift.DepotGoodsThriftService;
import com.meituan.shangou.goodscenter.thrift.GoodsSkuRelationThriftService;
import com.meituan.shangou.goodscenter.thrift.TenantGoodsThriftService;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.Status;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.logistics.delivery.constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-04-22
 * @email <EMAIL>
 */
@Slf4j
@Service
public class GoodsServiceWrapper {

    @Resource
    private GoodsSkuRelationThriftService goodsSkuRelationThriftService;
    @Resource
    private TenantGoodsThriftService tenantGoodsThriftService;
    @Resource
    private DepotGoodsThriftService depotGoodsThriftService;

    @Retryable(backoff = @Backoff(delay = 100))
    public List<GoodsSkuRelationDto> queryRelationBySkuIds(long tenantId, List<String> skuIds) {

        return BatchRequestHelper.batchQuest(
                Constants.QUERY_SKU_CENTER_PAGE_SIZE,
                skuIds,
                batchedSkuIds -> {
                    GoodsSkuRelationSkuIdQueryRequest request = new GoodsSkuRelationSkuIdQueryRequest();
                    request.setTenantId(tenantId);
                    request.setSkuIdList(batchedSkuIds);
                    GoodsSkuRelationListResponse response = goodsSkuRelationThriftService.queryBySkuIds(request);
                    log.info("invoke goodsSkuRelationThriftService.queryBySkuIds, request = {}, resp = {}", request, response);
                    if (!Objects.equals(response.getCode(), Status.SUCCESS.getCode())) {
                        throw new ThirdPartyException("查询门店sku错误");
                    }
                    return response.getData();
                }
        );
    }

    @Retryable(backoff = @Backoff(delay = 100))
    public List<DepotGoodsDetailDto> batchQueryDepotGoodsListByGoodsId(long tenantId, long warehouseId, List<String> goodsIdList) {
        return BatchRequestHelper.batchQuest(
                Constants.QUERY_SKU_CENTER_PAGE_SIZE,
                goodsIdList,
                batchedGoodsIds -> {
                    DepotGoodsBatchQueryRequest request = new DepotGoodsBatchQueryRequest();
                    request.setTenantId(tenantId);
                    request.setDepotId(warehouseId);
                    request.setGoodsIdList(batchedGoodsIds);
                    DepotGoodsDetailListResponse response = depotGoodsThriftService.batchQueryDepotGoodsListByGoodsId(request);
                    log.info("invoke depotGoodsThriftService.batchQueryDepotGoodsListByGoodsId, request = {}, resp = {}", request, response);
                    if (!Objects.equals(response.getCode(), Status.SUCCESS.getCode())) {
                        throw new ThirdPartyException("查询门店货品错误");
                    }

                    return response.getData();
                }
        );
    }
}
