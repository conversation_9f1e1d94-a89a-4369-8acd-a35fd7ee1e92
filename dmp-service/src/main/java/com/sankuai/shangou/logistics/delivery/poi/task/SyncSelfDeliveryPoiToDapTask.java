package com.sankuai.shangou.logistics.delivery.poi.task;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigService;
import com.sankuai.shangou.logistics.delivery.poi.task.param.SyncSelfDeliveryPoiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/1 16:44
 **/
@Service
@CraneConfiguration
@Slf4j
public class SyncSelfDeliveryPoiToDapTask {

    @Resource
    private SelfDeliveryPoiConfigService selfDeliveryPoiConfigService;

    @Crane("sync-self-delivery-poi-dap-task")
    public void syncSelfDeliveryPoiDap(SyncSelfDeliveryPoiParam param) {
        log.info("sync-self-delivery-poi-dap-task start, param: {}", param);
        Long tenantId = param.getTenantId();
        List<Long> storeIds = param.getStoreIds();
        int operatePoiNum = selfDeliveryPoiConfigService.syncDapDeliveryPoi(tenantId, storeIds);
        log.info("sync-self-delivery-poi-dap-task end, operatorPoiNum: {}", operatePoiNum);
    }
}
