package com.sankuai.shangou.logistics.delivery.indicator.service.calculate.base.impl;

import com.google.common.collect.Lists;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ShiftAndScheduleCountDTO;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BaseIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.repository.dbo.IndicatorDoc;
import com.sankuai.shangou.logistics.delivery.indicator.service.calculate.base.AbstractBaseIndicatorCalculator;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.indicator.utils.IndicatorSquirrelKeyUtils;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrderWithCurrentRider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-07-03
 * @email <EMAIL>
 */
@Slf4j
@Service
public class ScheduledLaborCountCalculator extends AbstractBaseIndicatorCalculator {

    @Override
    public BaseIndicatorEnum getSupportIndicator() {
        return BaseIndicatorEnum.SCHEDULED_EMPLOYEE_COUNT;
    }

    @Override
    public List<IndicatorDoc> calc(LocalDateTime fetchTime, List<IndicatorSquirrelKeyUtils.IndicatorStoreKey> indicatorStoreKeys,
                                   List<TRiderDeliveryOrderWithCurrentRider> thisDeliveryOrders,
                                   Map<Long, ShiftAndScheduleCountDTO> idShiftDTOMap,
                                   Map<Long, Long> employeeAccountIdMap, Map<Long, List<ShiftAndScheduleCountDTO>> poiIdAndShiftDTOListMap) {
        List<IndicatorDoc> indicatorDocList = Lists.newArrayList();
        BaseIndicatorEnum supportIndicator = getSupportIndicator();
        for (Map.Entry<Long, List<ShiftAndScheduleCountDTO>> entry : poiIdAndShiftDTOListMap.entrySet()) {
            List<ShiftAndScheduleCountDTO> shiftAndScheduleCountDTOS = entry.getValue();
            for (ShiftAndScheduleCountDTO shiftAndScheduleCountDTO : shiftAndScheduleCountDTOS) {
                if (!TimeUtils.isInRangeInOneDayAndNextDay(TimeUtils.convertFormatStrToLocalTime(shiftAndScheduleCountDTO.getStartWorkTime()), TimeUtils.convertFormatStrToLocalTime(shiftAndScheduleCountDTO.getEndWorkTime()), fetchTime.toLocalTime())) {
                    continue;
                }
                //build
                IndicatorDoc indicatorDoc = new IndicatorDoc();
                indicatorDoc.setCode(supportIndicator.getIndicatorCode());
                indicatorDoc.setLogisticsUnitId(entry.getKey());
                indicatorDoc.setValue(new BigDecimal(shiftAndScheduleCountDTO.getScheduleCount()));
                indicatorDoc.setProperty(shiftAndScheduleCountDTO.getColor());
                indicatorDoc.setFragmentName(shiftAndScheduleCountDTO.getName());
                indicatorDocList.add(indicatorDoc);
            }
        }

        //处理当前没有排班的门店，直接填0
        Map<Long, List<IndicatorDoc>> logisticsUnitIdMap = IListUtils.nullSafeGroupBy(indicatorDocList, IndicatorDoc::getLogisticsUnitId);
        List<Long> noSchedulePoi = IListUtils.nullSafeFilterElement(poiIdAndShiftDTOListMap.keySet(), poiId -> !logisticsUnitIdMap.containsKey(poiId));
        if (CollectionUtils.isNotEmpty(noSchedulePoi)) {
            for (Long poiId : noSchedulePoi) {
                IndicatorDoc zeroIndicatorDoc = new IndicatorDoc();
                zeroIndicatorDoc.setCode(supportIndicator.getIndicatorCode());
                zeroIndicatorDoc.setLogisticsUnitId(poiId);
                zeroIndicatorDoc.setValue(BigDecimal.ZERO);
                zeroIndicatorDoc.setProperty(StringUtils.EMPTY);
                zeroIndicatorDoc.setFragmentName(StringUtils.EMPTY);
                indicatorDocList.add(zeroIndicatorDoc);
            }
        }

        return indicatorDocList;
    }
}
