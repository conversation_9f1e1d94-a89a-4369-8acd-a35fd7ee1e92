package com.sankuai.shangou.logistics.delivery.tag.wrapper;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.Status;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.api.tag.ProductTagDTO;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.tag.SystemMarkingDataDto;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.tag.SystemUnmarkingDataDto;
import com.sankuai.meituan.shangou.xsupply.product.client.request.tag.BatchMarkingRequest;
import com.sankuai.meituan.shangou.xsupply.product.client.request.tag.ProductTagConditionQueryRequest;
import com.sankuai.meituan.shangou.xsupply.product.client.request.tag.SystemBatchMarkRequest;
import com.sankuai.meituan.shangou.xsupply.product.client.request.tag.SystemBatchUnmarkRequest;
import com.sankuai.meituan.shangou.xsupply.product.client.response.api.tag.ProductTagListResponse;
import com.sankuai.meituan.shangou.xsupply.product.client.response.tag.BatchMarkingResponse;
import com.sankuai.meituan.shangou.xsupply.product.client.response.tag.SystemBatchMarkResponse;
import com.sankuai.meituan.shangou.xsupply.product.client.response.tag.SystemBatchUnmarkResponse;
import com.sankuai.meituan.shangou.xsupply.product.client.service.TagThriftService;
import com.sankuai.meituan.shangou.xsupply.product.client.service.api.TagThriftApi;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.logistics.delivery.constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-04-24
 * @email <EMAIL>
 */
@Slf4j
@Service
public class ProductTagServiceWrapper {

    @Resource
    private TagThriftApi tagThriftApi;
    @Resource
    private TagThriftService tagThriftService;

    private static final int QUERY_TAG_MAX_SIZE = 500;

    @Retryable(backoff = @Backoff(delay = 100))
    public List<ProductTagDTO> batchQuerySealTagRelationList(long tenantId, long warehouseId, String tagCode, List<String> goodsIdList) {
        return BatchRequestHelper.batchQuest(
                Constants.QUERY_SKU_CENTER_PAGE_SIZE,
                goodsIdList,
                batchedGoodsIdList -> {
                    ProductTagConditionQueryRequest request = new ProductTagConditionQueryRequest();
                    request.setTenantId(tenantId);
                    request.setTagCodeList(Lists.newArrayList(tagCode));
                    request.setProductIdList(batchedGoodsIdList);
                    request.setDimIdList(Lists.newArrayList(String.valueOf(warehouseId)));
                    request.setMaxSize(QUERY_TAG_MAX_SIZE);

                    ProductTagListResponse response = tagThriftApi.batchQueryTagRelationList(request);
                    log.info("invoke tagThriftApi.batchQueryTagRelationList, request = {}, resp = {}", request, response);
                    if (!Objects.equals(response.getStatus().getCode(), Status.SUCCESS.getCode())) {
                        throw new ThirdPartyException("查询标签关系错误");
                    }
                    return response.getProductTagList();
                }
        );

    }

    @Retryable(backoff = @Backoff(delay = 100))
    public void systemBatchMark(long tenantId, String tagCode, List<SystemMarkingDataDto> sysMarkingData) {
        BatchRequestHelper.batchQuest(
                Constants.QUERY_SKU_CENTER_PAGE_SIZE,
                sysMarkingData,
                batchedSystemMarkingDataDto -> {
                    try {
                        SystemBatchMarkRequest request = new SystemBatchMarkRequest();
                        request.setTenantId(tenantId);
                        request.setSysMarkingCode(tagCode);
                        request.setSysMarkingData(batchedSystemMarkingDataDto);

                        SystemBatchMarkResponse response = tagThriftService.systemBatchMark(request);
                        log.info("invoke tagThriftService.systemBatchMark, request = {}, resp = {}", request, response);
                        if (!Objects.equals(response.getStatus().getCode(), Status.SUCCESS.getCode())) {
                            throw new ThirdPartyException("批量打标错误");
                        }
                        return null;
                    } catch (Exception e) {
                        log.error("invoke tagThriftService.systemBatchMark error", e);
                        throw new ThirdPartyException("批量打标错误");
                    }
                }
        );
    }

    @Retryable(backoff = @Backoff(delay = 100))
    public void systemBatchUnmark(long tenantId, String tagCode, List<SystemUnmarkingDataDto> sysMarkingData) {
        BatchRequestHelper.batchQuest(
                Constants.QUERY_SKU_CENTER_PAGE_SIZE,
                sysMarkingData,
                batchedSystemMarkingDataDto -> {
                    try {
                        SystemBatchUnmarkRequest request = new SystemBatchUnmarkRequest();
                        request.setTenantId(tenantId);
                        request.setSysMarkingCode(tagCode);
                        request.setSysUnmarkingData(batchedSystemMarkingDataDto);

                        SystemBatchUnmarkResponse response = tagThriftService.systemBatchUnmark(request);
                        log.info("invoke tagThriftService.systemBatchUnmark, request = {}, resp = {}", request, response);
                        if (!Objects.equals(response.getStatus().getCode(), Status.SUCCESS.getCode())) {
                            throw new ThirdPartyException("批量解除标记错误");
                        }
                        return null;
                    } catch (Exception e) {
                        log.error("invoke tagThriftService.systemBatchUnmark error", e);
                        throw new ThirdPartyException("批量解除标记错误");
                    }
                }
        );

    }
}
