package com.sankuai.shangou.logistics.delivery.common.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.infra.osw.api.org.TEmployeeService;
import com.sankuai.shangou.infra.osw.api.org.TOrgService;
import com.sankuai.shangou.infra.osw.api.org.TPositionService;
import com.sankuai.shangou.infra.osw.api.org.dto.request.AccountIdsRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.request.BatchQueryDepByEmpRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.request.QueryDepByIdsRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.response.DepartmentDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmpDepRelDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.PositionDTO;
import com.sankuai.shangou.infra.osw.api.poi.TPoiService;
import com.sankuai.shangou.infra.osw.api.poi.dto.request.QueryOperatePoiRequest;
import com.sankuai.shangou.infra.osw.api.poi.dto.response.BusinessPoiDTO;
import com.sankuai.shangou.infra.osw.api.poi.store.dto.request.WarehouseIdsRequest;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.TWarehouseService;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Rhino
@Slf4j
public class OswClient {
    private static final Integer QUERY_PARENT_DEPARTMENT_PAGE_SIZE = 100;

    @Resource
    private TEmployeeService tEmployeeService;

    @Resource
    private TPositionService tPositionService;

    @Resource
    private TOrgService tOrgService;

    @Resource
    private TPoiService tPoiService;

    @Resource
    private TWarehouseService tWarehouseService;

    @MethodLog(logRequest = true,logResponse = true)
    public List<DepartmentDTO> queryByDepartmentId(Long tenantId, List<Long> departmentIds) {
        QueryDepByIdsRequest request = new QueryDepByIdsRequest();
        request.setTenantId(tenantId);
        // 根据组织架构id查询父级id
        List<DepartmentDTO> result = new ArrayList<>(departmentIds.size());
        try {
            for (List<Long> chunk : Lists.partition(departmentIds,QUERY_PARENT_DEPARTMENT_PAGE_SIZE)) {
                request.setDepIds(chunk);
                TResult<List<DepartmentDTO>> resp = tOrgService.queryDepByIds(request);
                if (resp.getCode() != 0) {
                    throw new ThirdPartyException("TOrgService.queryDepByIds failed, code:" + resp.getCode());
                }
                result.addAll(resp.getData());
            }
        } catch (Exception e) {
            throw new ThirdPartyException("TOrgService.queryDepByIds failed",e);
        }
        return result;
    }

    @Degrade(rhinoKey = "OswClient.batchQueryBelongDepByEmp", fallBackMethod = "batchQueryBelongDepByEmpFallback", timeoutInMilliseconds = 2000)
    public List<EmpDepRelDTO> batchQueryBelongDepByEmp(Long tenantId, List<Long> employeeIds) {
        List<List<Long>> employeeLists = Lists.partition(employeeIds, 200);

        ArrayList<EmpDepRelDTO> empDepRelDTOS = new ArrayList<>();
        for (List<Long> employeeSubList : employeeLists) {
            BatchQueryDepByEmpRequest request = new BatchQueryDepByEmpRequest();
            request.setTenantId(tenantId);
            request.setEmpIds(employeeSubList);
            TResult<List<EmpDepRelDTO>> tResult;
            try {
                log.info("start invoke tEmployeeService.batchQueryBelongDepByEmp, request: {}", request);
                tResult = tEmployeeService.batchQueryBelongDepByEmp(request);
                log.info("start invoke tEmployeeService.batchQueryBelongDepByEmp, request: {}, result: {}", request, tResult);
            } catch (Exception e) {
                log.error("查询员工所属部门信息失败", e);
                throw new ThirdPartyException("查询员工所属部门信息失败");
            }

            if (!tResult.isSuccess()) {
                throw new BizException("查询员工所属部门信息失败");
            }

            empDepRelDTOS.addAll(tResult.getData());
        }

        return empDepRelDTOS;
    }

    public List<EmpDepRelDTO> batchQueryBelongDepByEmpFallback(Long tenantId, List<Long> employeeIds) {
        log.error("OswClient.batchQueryBelongDepByEmp 发生降级");
        return Collections.emptyList();
    }

    @Degrade(rhinoKey = "OswClient.queryPositionByIds", fallBackMethod = "queryPositionByIdsFallback", timeoutInMilliseconds = 2000)
    public List<PositionDTO> queryPositionByIds(Long tenantId, List<Long> positionIds) {
        log.info("start invoke tPositionService.queryPositionByIds, positionIds: {}", positionIds);
        TResult<List<PositionDTO>> tResult = tPositionService.queryPositionByIds(tenantId, positionIds);
        log.info("end invoke tPositionService.queryPositionByIds, result: {}", tResult);

        if (!tResult.isSuccess()) {
            throw new BizException("查询岗位信息失败");
        }

        return tResult.getData();
    }

    public List<PositionDTO> queryPositionByIdsFallback(Long tenantId, List<Long> positionIds) {
        log.error("OswClient.queryPositionByIds 发生降级");
        return Collections.emptyList();
    }

    @Degrade(rhinoKey = "OswClient.queryDepByIds", fallBackMethod = "queryDepByIdsFallback", timeoutInMilliseconds = 2000)
    public List<DepartmentDTO> queryDepByIds(Long tenantId, List<Long> depIds) {
        QueryDepByIdsRequest request = new QueryDepByIdsRequest();
        request.setTenantId(tenantId);
        request.setDepIds(depIds);
        log.info("start invoke tOrgService.queryDepByIds, request: {}", request);
        TResult<List<DepartmentDTO>> tResult = tOrgService.queryDepByIds(request);
        log.info("end invoke tOrgService.queryDepByIds, request: {}, result: {}", request, tResult);
        if (!tResult.isSuccess()) {
            throw new BizException("查询部门信息失败");
        }

        return tResult.getData();
    }

    public List<DepartmentDTO> queryDepByIdsFallback(Long tenantId, List<Long> depIds) {
        log.error("OswClient.queryDepByIds 发生降级");
        return Collections.emptyList();
    }


    @Degrade(rhinoKey = "OswClient.queryWarehouseInfo", fallBackMethod = "queryWarehouseInfoFallback", timeoutInMilliseconds = 2000)
    public WarehouseDTO queryWarehouseInfo(Long tenantId, Long warehouseId) {
        WarehouseIdsRequest request = new WarehouseIdsRequest();
        request.setTenantId(tenantId);
        request.setWarehouseIds(Collections.singletonList(warehouseId));

        TResult<List<WarehouseDTO>> tResult;
        try {
            log.info("start invoke tWarehouseService.batchQueryWarehouseById, request: {}", request);
            tResult = tWarehouseService.batchQueryWarehouseById(request);
            log.info("end invoke tWarehouseService.batchQueryWarehouseById, tResult: {}", tResult);
        } catch (Exception e) {
            log.error("查询门店信息失败", e);
            throw new ThirdPartyException("查询门店信息失败");
        }

        if (!tResult.isSuccess()) {
            throw new BizException("查询门店信息失败");
        }

        if (CollectionUtils.isEmpty(tResult.getData())) {
            throw new BizException("未查询到门店信息");
        }

        return tResult.getData().get(0);
    }

    @Degrade(rhinoKey = "OswClient.queryWarehouseInfoList", fallBackMethod = "queryWarehouseInfoListFallback", timeoutInMilliseconds = 2000)
    public Map<Long, WarehouseDTO> queryWarehouseInfoList(Long tenantId, List<Long> warehouseIds) {
        if (CollectionUtils.isEmpty(warehouseIds)) {
            return Collections.emptyMap();
        }

        HashMap<Long, WarehouseDTO> warehouseInfoMap = new HashMap<>();

        List<List<Long>> lists = Lists.partition(warehouseIds, 200);
        for (List<Long> subList : lists) {
            WarehouseIdsRequest request = new WarehouseIdsRequest();
            request.setTenantId(tenantId);
            request.setWarehouseIds(subList);

            TResult<List<WarehouseDTO>> tResult;
            try {
                log.info("start invoke tWarehouseService.batchQueryWarehouseById, request: {}", request);
                tResult = tWarehouseService.batchQueryWarehouseById(request);
                log.info("end invoke tWarehouseService.batchQueryWarehouseById, tResult: {}", tResult);
            } catch (Exception e) {
                log.error("查询门店信息失败", e);
                throw new ThirdPartyException("查询门店信息失败");
            }

            if (!tResult.isSuccess()) {
                throw new BizException("查询门店信息失败");
            }

            if (CollectionUtils.isEmpty(tResult.getData())) {
                throw new BizException("未查询到门店信息");
            }

            warehouseInfoMap.putAll(tResult.getData().stream().collect(Collectors.toMap(WarehouseDTO::getId, Function.identity(), (k1, k2) -> k1)));
        }

        return warehouseInfoMap;
    }

    @Degrade(rhinoKey = "OswClient.batchQueryEmployeeInfo", fallBackMethod = "batchQueryEmployeeInfoFallback", timeoutInMilliseconds = 2000)
    public Map<Long, EmployeeDTO> batchQueryEmployeeInfo(Long tenantId, List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }

        AccountIdsRequest request = new AccountIdsRequest();
        request.setTenantId(tenantId);
        request.setAccountIds(accountIds);
        TResult<List<EmployeeDTO>> tResult;
        try {
            log.info("start invoke tEmployeeService.queryEmpByAccountIds, request: {}", request);
            tResult = tEmployeeService.queryEmpByAccountIds(request);
            log.info("end invoke tEmployeeService.queryEmpByAccountIds, result: {}", tResult);
        } catch (Exception e) {
            log.error("查询员工信息失败", e);
            throw new ThirdPartyException("查询员工信息失败");
        }

        if (!tResult.isSuccess()) {
            throw new BizException(tResult.getMsg());
        }

        if (CollectionUtils.isEmpty(tResult.getData())) {
            return Collections.emptyMap();
        }

        return tResult.getData().stream().collect(Collectors.toMap(EmployeeDTO::getAccountId, Function.identity(), (k1, k2) -> k2));
    }


    public Map<Long, WarehouseDTO> queryWarehouseInfoListFallback(Long tenantId, List<Long> warehouseIds) {
        log.error("OswClient.queryWarehouseInfoList 发生降级");
        return Collections.emptyMap();
    }

    public WarehouseDTO queryWarehouseInfoFallback(Long tenantId, Long warehouseId) {
        log.error("OswClient.tWarehouseService 发生降级");
        throw new BizException("店仓接口降级");
    }

    public Map<Long, EmployeeDTO> batchQueryEmployeeInfoFallback(Long tenantId, List<Long> accountIds) {
        log.error("OswClient.batchQueryEmployeeInfo 发生降级");
        return Collections.emptyMap();
    }
}