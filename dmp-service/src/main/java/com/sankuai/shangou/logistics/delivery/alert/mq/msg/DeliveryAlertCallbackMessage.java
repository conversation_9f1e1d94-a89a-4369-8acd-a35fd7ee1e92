package com.sankuai.shangou.logistics.delivery.alert.mq.msg;

import com.sankuai.shangou.logistics.delivery.alert.enums.DeliveryAlertCallbackEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 配送告警回调消息
 * @date 2025-05-15
 */
@Data
public class DeliveryAlertCallbackMessage {
    /**
     * @see DeliveryAlertCallbackEnum
     */
    private String type;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"type\":\"")
                .append(type).append('\"');

        sb.append('}');
        return sb.toString();
    }
}
