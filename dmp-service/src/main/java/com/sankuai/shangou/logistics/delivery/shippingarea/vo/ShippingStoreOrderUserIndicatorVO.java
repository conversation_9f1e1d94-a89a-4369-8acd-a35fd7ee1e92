package com.sankuai.shangou.logistics.delivery.shippingarea.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "订单用户指标"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShippingStoreOrderUserIndicatorVO extends OrderUserIndicatorVO{

    @FieldDoc(
            description = "外卖起送价，单位：元(人民币)" )
    private Double mtMinPrice;

    @FieldDoc(
            description = "外卖配送费，单位：元(人民币)"
    )
    private Double mtShippingFee;

    @FieldDoc(
            description = "微商城起送价，单位：元(人民币)" )
    private Double mtWmMinPrice;

    @FieldDoc(
            description = "微商城配送费，单位：元(人民币)"
    )
    private Double mtWmShippingFee;

}
