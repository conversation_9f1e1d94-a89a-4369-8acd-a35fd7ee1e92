package com.sankuai.shangou.logistics.delivery.xm.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2025-05-21
 */
@Getter
public enum CardColorEnum {
    BLUE("blue", "蓝色"),
    GREEN("green", "绿色"),
    ORANGE("orange", "橙色"),
    RED("red", "红色"),
    PURPLE("purple", "紫色"),
    GRAY("gray", "黄色"),
    ;

    private final String type;
    private final String desc;

    CardColorEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
