package com.sankuai.shangou.logistics.delivery.indicator.service.calculate.biz.impl;

import com.sankuai.shangou.logistics.delivery.indicator.service.calculate.biz.AbstractBizIndicatorCalculator;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BaseIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BizIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/7/24 21:04
 **/
@Slf4j
@Component
public class ScheduledDeliveryLoadIncludeUnscheduledOrderCalculator extends AbstractBizIndicatorCalculator {
    @Override
    public List<BaseIndicatorEnum> getRelateBaseIndicators() {
        return Arrays.asList(BaseIndicatorEnum.WAIT_ACCEPT_ORDER_COUNT,
                BaseIndicatorEnum.WAIT_PICK_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT,
                BaseIndicatorEnum.WAIT_PICK_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT,
                BaseIndicatorEnum.DELIVERED_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT,
                BaseIndicatorEnum.DELIVERED_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT,
                BaseIndicatorEnum.SCHEDULED_EMPLOYEE_COUNT);
    }

    @Override
    public BizIndicatorEnum getSupportIndicator() {
        return BizIndicatorEnum.SCHEDULED_EMPLOYEE_DELIVERY_LOAD_INCLUDE_UNSCHEDULED_ORDER;
    }

    @Override
    public List<IndicatorDTO> calc(Map<String, List<IndicatorDTO>> baseIndicatorMap) {
        //如果基础指标数据不合法 直接返回空
        if (!checkBaseIndicatorDataIsValid(baseIndicatorMap)) {
            log.error("基础指标数据不全或不合法, baseIndicatorMap: {}, bizIndicatorEnum: {}", baseIndicatorMap, getSupportIndicator());
            return Collections.emptyList();
        }

        BigDecimal waitAcceptOrderCnt = baseIndicatorMap.get(BaseIndicatorEnum.WAIT_ACCEPT_ORDER_COUNT.getIndicatorCode()).get(0).getValue();
        BigDecimal waitPickByScheduledOrderCnt = baseIndicatorMap.get(BaseIndicatorEnum.WAIT_PICK_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT.getIndicatorCode()).get(0).getValue();
        BigDecimal waitPickByUnscheduledOrderCnt = baseIndicatorMap.get(BaseIndicatorEnum.WAIT_PICK_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT.getIndicatorCode()).get(0).getValue();
        BigDecimal deliveredByScheduledOrderCnt = baseIndicatorMap.get(BaseIndicatorEnum.DELIVERED_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT.getIndicatorCode()).get(0).getValue();
        BigDecimal deliveryByUnscheduledOrderCnt = baseIndicatorMap.get(BaseIndicatorEnum.DELIVERED_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT.getIndicatorCode()).get(0).getValue();
        Optional<BigDecimal> scheduledEmployeeCountOpt = baseIndicatorMap.get(BaseIndicatorEnum.SCHEDULED_EMPLOYEE_COUNT.getIndicatorCode()).stream().map(IndicatorDTO::getValue).reduce(BigDecimal::add);

        BigDecimal orderCount = waitAcceptOrderCnt.add(waitPickByScheduledOrderCnt).add(waitPickByUnscheduledOrderCnt).add(deliveredByScheduledOrderCnt)
                .add(deliveryByUnscheduledOrderCnt);

        BigDecimal scheduledEmployeeCount = scheduledEmployeeCountOpt.get();

        //分子不为0 分母为0
        if (!BigDecimal.ZERO.equals(orderCount) && BigDecimal.ZERO.equals(scheduledEmployeeCount)) {
            log.warn("scheduledEmployeeCount is zero");
            return Collections.emptyList();
        }

        //分子分母同时为0
        if (BigDecimal.ZERO.equals(orderCount) && BigDecimal.ZERO.equals(scheduledEmployeeCount)) {
            IndicatorDTO bizIndicator = buildWithBaseIndicatorAndBizValue(baseIndicatorMap.get(BaseIndicatorEnum.WAIT_ACCEPT_ORDER_COUNT.getIndicatorCode()).get(0), BigDecimal.ZERO);
            return Collections.singletonList(bizIndicator);
        }

        BigDecimal bizValue = orderCount.divide(scheduledEmployeeCount, 2, RoundingMode.HALF_UP);
        IndicatorDTO bizIndicator = buildWithBaseIndicatorAndBizValue(baseIndicatorMap.get(BaseIndicatorEnum.WAIT_ACCEPT_ORDER_COUNT.getIndicatorCode()).get(0), bizValue);
        return Collections.singletonList(bizIndicator);
    }
}
