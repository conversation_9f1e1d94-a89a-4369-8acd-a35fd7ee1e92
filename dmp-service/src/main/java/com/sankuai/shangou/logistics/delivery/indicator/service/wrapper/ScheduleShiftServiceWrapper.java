package com.sankuai.shangou.logistics.delivery.indicator.service.wrapper;

import com.sankuai.drunkhorsemgmt.labor.common.Status;
import com.sankuai.drunkhorsemgmt.labor.thrift.ScheduleThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ShiftAndScheduleCountDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.QueryScheduleShiftAndScheduleCountMapResponse;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.QueryScheduleShiftAndScheduleCountResponse;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-07-03
 * @email <EMAIL>
 */
@Slf4j
@Service
public class ScheduleShiftServiceWrapper {

    @Resource
    private ScheduleThriftService scheduleThriftService;

    public List<ShiftAndScheduleCountDTO> queryScheduleShiftListByIdList(List<Long> idList, LocalDate queryDate) {
        QueryScheduleShiftAndScheduleCountResponse response = scheduleThriftService.queryScheduleShiftListByIdList(idList, TimeUtils.convertLocalDateToMillTimestamp(queryDate));
        if(response.getStatus().getCode() != Status.SUCCESS.getCode()) {
            throw new SystemException("invoke queryScheduleShiftListByIdList error");
        }
        return response.getShiftDTOList();
    }

    @MethodLog(logRequest = true, logResponse = true)
    public Map<Long, List<ShiftAndScheduleCountDTO>> queryScheduleShiftListByPoiIdAndDate(List<Long> poiIds, LocalDate queryDate) {
        QueryScheduleShiftAndScheduleCountMapResponse response = scheduleThriftService.queryScheduleShiftListByPoiIdAndDate(poiIds, TimeUtils.convertLocalDateToMillTimestamp(queryDate));
        if(response.getStatus().getCode() != Status.SUCCESS.getCode()) {
            throw new SystemException("invoke queryScheduleShiftListByIdList error");
        }
        return response.getPoiIdAndShiftDTOListMap();
    }
}
