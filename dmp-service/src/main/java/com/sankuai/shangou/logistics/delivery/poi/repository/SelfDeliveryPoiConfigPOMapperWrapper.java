package com.sankuai.shangou.logistics.delivery.poi.repository;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.constants.DapSyncStatusEnum;
import com.sankuai.shangou.logistics.delivery.constants.DeleteEnum;
import com.sankuai.shangou.logistics.delivery.constants.Operator;
import com.sankuai.shangou.logistics.delivery.mapper.SelfDeliveryPoiConfigPOMapper;
import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPO;
import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPOExample;
import com.sankuai.shangou.logistics.delivery.poi.domain.SelfDeliveryPoiConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @email <EMAIL>
 */
@Slf4j
@Service
public class SelfDeliveryPoiConfigPOMapperWrapper {

    @Resource
    private SelfDeliveryPoiConfigPOMapper selfDeliveryPoiConfigMapper;

    public SelfDeliveryPoiConfigPO querySelfDeliveryConfig(long tenantId, long poiId) {
        SelfDeliveryPoiConfigPOExample example = new SelfDeliveryPoiConfigPOExample();
        example
                .createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andPoiIdEqualTo(poiId)
                .andIsDeletedEqualTo(DeleteEnum.NOT_DELETE.getCode());
        List<SelfDeliveryPoiConfigPO> selfDeliveryPoiConfigList = selfDeliveryPoiConfigMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(selfDeliveryPoiConfigList)) {
            return null;
        }
        return selfDeliveryPoiConfigList.get(0);
    }

    public List<SelfDeliveryPoiConfigPO> querySelfDeliveryConfig(long tenantId) {
        SelfDeliveryPoiConfigPOExample example = new SelfDeliveryPoiConfigPOExample();
        example
                .createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andIsDeletedEqualTo(DeleteEnum.NOT_DELETE.getCode());
        return selfDeliveryPoiConfigMapper.selectByExample(example);
    }

    public PageInfo<SelfDeliveryPoiConfigPO> pageQuerySelfDeliveryConfigList(List<Long> poiIds, Integer filterEnableTurnToDap, Integer filterEnablePickDeliverySplit,int pageNo, int pageSize) {
        try (Page<SelfDeliveryPoiConfigPO> page = PageHelper.startPage(pageNo, pageSize)) {
            SelfDeliveryPoiConfigPOExample example = new SelfDeliveryPoiConfigPOExample();
            SelfDeliveryPoiConfigPOExample.Criteria criteria = example
                    .createCriteria()
                    .andPoiIdIn(poiIds)
                    .andIsDeletedEqualTo(DeleteEnum.NOT_DELETE.getCode());
            if (Objects.nonNull(filterEnableTurnToDap)) {
                criteria.andEnableTurnDeliveryEqualTo(filterEnableTurnToDap);
            }
            if (Objects.nonNull(filterEnablePickDeliverySplit)) {
                criteria.andEnablePickDeliverySplitEqualTo(filterEnablePickDeliverySplit);
            }
            example.setOrderByClause("last_operate_time desc, id desc");
            List<SelfDeliveryPoiConfigPO> selfDeliveryPoiConfigList = selfDeliveryPoiConfigMapper.selectByExample(example);
            return new PageInfo<>(selfDeliveryPoiConfigList);
        }
    }

    public void batchUpdateNeedTurnDelivery(Long tenantId, List<Long> poiIds, Integer enableTurnDelivery, Operator operator) {
        SelfDeliveryPoiConfigPOExample example = new SelfDeliveryPoiConfigPOExample();
        example
                .createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andPoiIdIn(poiIds)
                .andIsDeletedEqualTo(DeleteEnum.NOT_DELETE.getCode());
        SelfDeliveryPoiConfigPO po =  new SelfDeliveryPoiConfigPO();
        po.setEnableTurnDelivery(enableTurnDelivery);
        po.setLastOperatorId(operator.getOperatorId());
        po.setLastOperatorName(operator.getOperatorName());
        po.setLastOperateTime(LocalDateTime.now());
        selfDeliveryPoiConfigMapper.updateByExampleSelective(po, example);
    }

    public void batchUpdatePickDeliverySplit(Long tenantId, List<Long> poiIds, Integer enablePickDeliverySplit, Operator operator) {
        SelfDeliveryPoiConfigPOExample example = new SelfDeliveryPoiConfigPOExample();
        example
                .createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andPoiIdIn(poiIds)
                .andIsDeletedEqualTo(DeleteEnum.NOT_DELETE.getCode());
        SelfDeliveryPoiConfigPO po =  new SelfDeliveryPoiConfigPO();
        po.setEnablePickDeliverySplit(enablePickDeliverySplit);
        po.setLastOperatorId(operator.getOperatorId());
        po.setLastOperatorName(operator.getOperatorName());
        po.setLastOperateTime(LocalDateTime.now());
        selfDeliveryPoiConfigMapper.updateByExampleSelective(po, example);
    }

    public int batchUpdateDapSyncStatus(Long tenantId, List<Long> poiIds, DapSyncStatusEnum dapSyncStatusEnum, Operator operator) {
        if (CollectionUtils.isEmpty(poiIds)) {
            return 0;
        }
        SelfDeliveryPoiConfigPOExample example = new SelfDeliveryPoiConfigPOExample();
        example.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andPoiIdIn(poiIds)
                .andIsDeletedEqualTo(DeleteEnum.NOT_DELETE.getCode());
        SelfDeliveryPoiConfigPO po = new SelfDeliveryPoiConfigPO();
        po.setDapSyncStatus(dapSyncStatusEnum.getCode());
        po.setLastOperatorId(operator.getOperatorId());
        po.setLastOperatorName(operator.getOperatorName());
        po.setLastOperateTime(LocalDateTime.now());
        return selfDeliveryPoiConfigMapper.updateByExampleSelective(po, example);
    }

    public int batchInsert(List<SelfDeliveryPoiConfig> selfDeliveryPoiConfigList) {
        if (CollectionUtils.isEmpty(selfDeliveryPoiConfigList)) {
            return 0;
        }

        return selfDeliveryPoiConfigMapper.batchInsert(IListUtils.mapTo(selfDeliveryPoiConfigList, this::convert2PO));
    }


    public List<SelfDeliveryPoiConfigPO> queryUnSyncToDapStoreList(Long tenantId, List<Long> storeIds) {
        SelfDeliveryPoiConfigPOExample example = new SelfDeliveryPoiConfigPOExample();
        SelfDeliveryPoiConfigPOExample.Criteria criteria = example.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDapSyncStatusEqualTo(DapSyncStatusEnum.NOT_SYNC.getCode())
                .andIsDeletedEqualTo(DeleteEnum.NOT_DELETE.getCode());

        if (CollectionUtils.isNotEmpty(storeIds)) {
            criteria.andPoiIdIn(storeIds);
        }

        return selfDeliveryPoiConfigMapper.selectByExample(example);
    }
    private SelfDeliveryPoiConfigPO convert2PO(SelfDeliveryPoiConfig selfDeliveryPoiConfig) {
        SelfDeliveryPoiConfigPO po = new SelfDeliveryPoiConfigPO();
        po.setTenantId(selfDeliveryPoiConfig.getTenantId());
        po.setPoiId(selfDeliveryPoiConfig.getPoiId());
        po.setDapSyncStatus(selfDeliveryPoiConfig.getDapSyncStatus().getCode());
        po.setIsDeleted(selfDeliveryPoiConfig.getIsDeleted().getCode());
        po.setEnableTurnDelivery(selfDeliveryPoiConfig.getEnableTurnDelivery());
        po.setLastOperatorName(selfDeliveryPoiConfig.getLastOperatorName());
        po.setLastOperatorId(selfDeliveryPoiConfig.getLastOperatorId());
        po.setLastOperateTime(selfDeliveryPoiConfig.getLastOperateTime());
        po.setCreateTime(selfDeliveryPoiConfig.getCreateTime() == null ? LocalDateTime.now() : selfDeliveryPoiConfig.getCreateTime());
        po.setUpdateTime(selfDeliveryPoiConfig.getUpdateTime() == null ? LocalDateTime.now() : selfDeliveryPoiConfig.getUpdateTime());
        return po;
    }

    private SelfDeliveryPoiConfig fromPO(SelfDeliveryPoiConfigPO po) {
        SelfDeliveryPoiConfig deliveryPoiConfig = new SelfDeliveryPoiConfig();
        deliveryPoiConfig.setTenantId(po.getTenantId());
        deliveryPoiConfig.setPoiId(po.getPoiId());
        deliveryPoiConfig.setDapSyncStatus(DapSyncStatusEnum.valueOfCode(po.getDapSyncStatus()));
        deliveryPoiConfig.setIsDeleted(DeleteEnum.valueOfCode(po.getIsDeleted()));
        deliveryPoiConfig.setEnableTurnDelivery(po.getEnableTurnDelivery());
        deliveryPoiConfig.setLastOperatorName(po.getLastOperatorName());
        deliveryPoiConfig.setLastOperatorId(po.getLastOperatorId());
        deliveryPoiConfig.setLastOperateTime(po.getLastOperateTime());
        deliveryPoiConfig.setCreateTime(po.getCreateTime());
        deliveryPoiConfig.setUpdateTime(po.getUpdateTime());
        return deliveryPoiConfig;
    }
}
