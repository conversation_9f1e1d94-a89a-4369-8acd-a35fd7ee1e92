package com.sankuai.shangou.logistics.delivery.indicator.service.wrapper;

import com.meituan.shangou.saas.tenant.thrift.UserThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.user.UserDto;
import com.meituan.shangou.saas.tenant.thrift.dto.user.response.UserListResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-07-04
 * @email <EMAIL>
 */
@Slf4j
@Service
public class UserServiceWrapper {

    @Resource
    private UserThriftService userThriftService;

    private static final int SUCCESS_CODE = 0;

    public List<UserDto> queryTenantUserByAccountIds(long tenantId, List<Long> accountIds) {
        UserListResponse response = userThriftService.queryTenantUserByAccountIds(tenantId, accountIds);
        if (!Objects.equals(response.getStatus().getCode(), SUCCESS_CODE)) {
            throw new SystemException("invoke userThriftService.queryTenantUserByAccountIds error");
        }
        return response.getUserList();
    }

}
