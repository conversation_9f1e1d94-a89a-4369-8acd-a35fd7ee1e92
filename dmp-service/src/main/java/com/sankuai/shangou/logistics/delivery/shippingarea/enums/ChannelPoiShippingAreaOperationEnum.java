package com.sankuai.shangou.logistics.delivery.shippingarea.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/8/22 14:57
 **/
@AllArgsConstructor
@Getter
public enum ChannelPoiShippingAreaOperationEnum {
    REPLACE(0,"替换配送范围，先增后删"),
    NEW(1,"新增配送范围"),
    UPDATE(2,"修改配送范围"),

    DELETE(3,"删除配送范围"),
    RESET(4,"重置配送范围");

    private int code;
    private String desc;
}
