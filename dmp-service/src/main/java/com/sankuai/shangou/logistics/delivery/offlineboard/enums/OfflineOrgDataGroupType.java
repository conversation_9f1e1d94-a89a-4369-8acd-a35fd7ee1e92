package com.sankuai.shangou.logistics.delivery.offlineboard.enums;

/**
 * <AUTHOR>
 * @date 2024-05-30
 * @email <EMAIL>
 */
public enum OfflineOrgDataGroupType {

    SECOND_AGG(2, "near_poi_first_department_id", "near_poi_first_department_name"),
    THIRD_AGG(3, "near_poi_second_department_id", "near_poi_second_department_name"),
    ;


    private int type;

    private String powerApiGroupKey;

    private String powerApiGroupNameKey;

    private String columnSortKey;

    OfflineOrgDataGroupType(int type, String powerApiGroupKey, String powerApiGroupNameKey) {
        this.type = type;
        this.powerApiGroupKey = powerApiGroupKey;
        this.powerApiGroupNameKey = powerApiGroupNameKey;
    }

    public static OfflineOrgDataGroupType valueOfType(int type) {
        for (OfflineOrgDataGroupType obj : OfflineOrgDataGroupType.values()) {
            if (java.util.Objects.equals(obj.type, type)) {
                return obj;
            }
        }
        return null;
    }

    public int getType() {
        return type;
    }

    public String getPowerApiGroupKey() {
        return powerApiGroupKey;
    }

    public String getColumnSortKey() {
        return columnSortKey;
    }

    public String getPowerApiGroupNameKey() {
        return powerApiGroupNameKey;
    }
}
