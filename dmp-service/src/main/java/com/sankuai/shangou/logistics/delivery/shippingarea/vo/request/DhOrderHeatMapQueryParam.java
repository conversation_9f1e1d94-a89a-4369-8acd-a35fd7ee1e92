package com.sankuai.shangou.logistics.delivery.shippingarea.vo.request;

import com.meituan.linz.boot.util.Assert;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.enums.DateTypeEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.DateTimeUtils;
import lombok.Data;
import org.apache.commons.lang3.EnumUtils;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2023/3/20 15:54
 **/
@TypeDoc(
        description = "查询城市歪马订单热力图请求",
        authors = {
                "linxiaorui"
        }
)
@Data
public class DhOrderHeatMapQueryParam {
    @FieldDoc(
            description = "城市id"
    )
    private Integer cityId;

    @FieldDoc(
            description = "日期类型"
    )
    private String dateType = DateTypeEnum.WEEK.name();

    @FieldDoc(
            description = "查询日期, 格式 yyyy-MM-dd"
    )
    private String dt;

    public void validate() {
        Assert.throwIfTrue(this.cityId == null || this.cityId <= 0L, "城市id不正确");
        Assert.throwIfTrue(!EnumUtils.isValidEnum(DateTypeEnum.class, this.dateType), "日期类型不正确");
        Assert.throwIfTrue(!DateTimeUtils.isIsoLocalDate(this.dt), "日期格式不正确");
        Assert.throwIfTrue(DateTimeUtils.parseIsoLocalDateString(this.dt).isBefore(LocalDate.now().minusYears(1)), "最多查询近一年的数据");
    }
}
