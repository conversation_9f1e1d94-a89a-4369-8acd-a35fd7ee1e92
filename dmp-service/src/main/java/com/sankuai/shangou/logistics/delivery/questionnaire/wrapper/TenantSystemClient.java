package com.sankuai.shangou.logistics.delivery.questionnaire.wrapper;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.shangou.commons.utils.rpc.RpcInvokeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/11/15 11:13
 **/
@Service
@Slf4j
public class TenantSystemClient {

    @Resource
    private PoiThriftService poiThriftServiceClient;

    @CatTransaction
    public PoiInfoDto queryStoreDetailInfo(Long tenantId, Long storeId) {

        PoiMapResponse poiMapResponse = RpcInvokeUtils.rpcInvokeTemplate(() -> poiThriftServiceClient.queryTenantPoiInfoMapByPoiIds(Lists.newArrayList(storeId), tenantId),
                "查询门店信息失败", resp -> resp.getStatus().getCode(), resp -> resp.getStatus().getMessage());

        return poiMapResponse.getPoiInfoMap().get(storeId);
    }

}
