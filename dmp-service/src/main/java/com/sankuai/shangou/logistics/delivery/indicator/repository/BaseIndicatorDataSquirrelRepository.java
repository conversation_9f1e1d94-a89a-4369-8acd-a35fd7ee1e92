package com.sankuai.shangou.logistics.delivery.indicator.repository;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Lists;
import com.sankuai.shangou.logistics.delivery.indicator.utils.IndicatorSquirrelKeyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-30
 * @email <EMAIL>
 */
@Slf4j
@Repository
public class BaseIndicatorDataSquirrelRepository {

    @Resource
    private RedisStoreClient redisStoreClient;


    private final static int SQUIRREL_SCAN_COUNT = 1024;

    private final static String indicatorCategory = "labor_attendance_tracking";

    /**
     * 扫描今日（业务时间的今日，非物理时间）基础指标数据
     * redis里存的话只能是物理时间，非班次信息不能用bizTime，如 配送中订单数
     */
    public List<StoreKey> scanBizTodayBaseIndicatorData(long warehouseId, LocalDate bizDay) {

        Iterable<List<StoreKey>> iterableKey = redisStoreClient.scan(IndicatorSquirrelKeyUtils.buildScanIndicatorStoreKeys(indicatorCategory, warehouseId, bizDay), SQUIRREL_SCAN_COUNT);
        List<StoreKey> allStoreKey = Lists.newArrayList();
        for (List<StoreKey> scanKeys : iterableKey) {
            allStoreKey.addAll(scanKeys);
        }
        log.info("scan key finished, warehouseId = {}, bizDay = {}, result = {}", warehouseId, bizDay, allStoreKey);
        return allStoreKey;
    }

}
