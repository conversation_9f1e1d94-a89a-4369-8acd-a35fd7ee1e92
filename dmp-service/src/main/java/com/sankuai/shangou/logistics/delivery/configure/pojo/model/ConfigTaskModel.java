package com.sankuai.shangou.logistics.delivery.configure.pojo.model;

import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.shangou.logistics.delivery.configure.enums.BatchTaskStatusEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.BatchTaskTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 配置任务
 * @date 2025-07-02
 */
@Data
public class ConfigTaskModel {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店ids
     */
    private List<Long> poiIds;

    /**
     * 配置模版id
     */
    private Long configTemplateId;

    /**
     * 任务类型
     */
    private BatchTaskTypeEnum taskType;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 同步的渠道
     */
    private List<DynamicChannelType> channelTypes;

    /**
     * 状态
     */
    private BatchTaskStatusEnum status;

    /**
     * 配置内容
     */
    private List<BatchTaskConfigContent> configContents;

    /**
     * 成功数量
     */
    private Integer successNum;

    /**
     * 失败数量
     */
    private Integer failNum;

    /**
     * 任务总数
     */
    private Integer totalNum;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 完成时间
     */
    private LocalDateTime finishedAt;

    /**
     * 牵牛花账号
     */
    private String operator;

    /**
     * 操作人姓名
     */
    private String operatorName;
}
