package com.sankuai.shangou.logistics.delivery.push.msg.card;


import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 发单失败卡片数据
 * @date 2025-05-16
 */
@Data
public class LaunchFailCardData extends AbstractCardData{
    /**
     * 开始时间
     */
    private String beginTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 订单ID列表
     */
    private String orderIds;
    /**
     * 订单数量
     */
    private String orderCount;
    /**
     * 按钮禁用状态
     */
    private Boolean disabledButton = Boolean.TRUE;


    @Override
    public String getPublicDataJsonStr() {
        JSONObject jsonObject = new JSONObject();
        getCommonJsonStr(jsonObject);
        jsonObject.put("disabledButton", disabledButton);
        return jsonObject.toJSONString();
    }

    @Override
    public String getPrivateDataJsonStr() {
        JSONObject jsonObject = new JSONObject();
        getCommonJsonStr(jsonObject);
        jsonObject.put("disabledButton", !disabledButton);
        return jsonObject.toJSONString();
    }

    private void getCommonJsonStr(JSONObject jsonObject) {
        setCardTitleMap(jsonObject);
        jsonObject.put("cardType", super.getCardType());
        jsonObject.put("beginTime", beginTime);
        jsonObject.put("endTime", endTime);
        jsonObject.put("orderIds", orderIds);
        jsonObject.put("orderCount", orderCount);
    }
}
