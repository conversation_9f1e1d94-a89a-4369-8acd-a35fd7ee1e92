package com.sankuai.shangou.logistics.delivery.shippingarea.vo.request;

import com.meituan.linz.boot.util.Assert;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.DateTimeUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.IGeoUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(
        description = "人口画像查询请求参数",
        authors = {
                "daiyuan03"
        }
)
@Getter
@Setter
public class PeoplePortraitQueryParam {

    @FieldDoc(
            description = "城市id"
    )
    private Integer cityId;

    @FieldDoc(
            description = "范围边界"
    )
    private String region;

    @FieldDoc(
            description = "查询日期, 格式 yyyy-MM, 默认上月"
    )
    private String dt = DateTimeUtils.getLastMonthDt();

    public void validate() {
        Assert.throwIfTrue(this.cityId == null || this.cityId <= 0L, "城市id不正确");
        Assert.throwIfTrue(!DateTimeUtils.isMapApiDate(this.dt), "日期格式不正确");
        Assert.throwIfTrue(!DateTimeUtils.isAfterMinusMonth(this.dt, 6), "无所选时间数据，请重新选择近6个月的时间");
        Assert.throwIfTrue(StringUtils.isBlank(this.region), "范围不能为空");
    }
}
