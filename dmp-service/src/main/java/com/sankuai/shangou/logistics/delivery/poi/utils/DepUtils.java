package com.sankuai.shangou.logistics.delivery.poi.utils;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.dto.DepartmentV2Dto;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> qiuli05
 * @date : 2023/6/19
 * @description :
 */
public class DepUtils {

    /**
     * 根据部门信息,获取部门Id(包含下级)列表
     * @param originDepIds
     * @param depParentMaps
     * @param depth
     * @return
     */
    public static List<Long> getDepIdsIncludeNext(List<Long> originDepIds, Map<Long, List<DepartmentV2Dto>> depParentMaps, int depth) {

        if (CollectionUtils.isEmpty(originDepIds)) {
            return Lists.newArrayList();
        }

        if (depth > 20) {
            // 部门结构深度不应太深,除非出现死循环
            return originDepIds;
        }

        // 先将当前的depIds加载到返回结果集
        List<Long> depIds = Lists.newArrayList(originDepIds);

        for (Long originDepId : originDepIds) {
            List<DepartmentV2Dto> childDeps = depParentMaps.get(originDepId);
            if (CollectionUtils.isNotEmpty(childDeps)) {
                // 存在下级,需要将下级也添加到返回结果,此处递归
                for (DepartmentV2Dto childDep : childDeps) {
                    depIds.addAll(getDepIdsIncludeNext(Lists.newArrayList(childDep.getDepartmentId()), depParentMaps, depth + 1));
                }
            }
        }

        return depIds;
    }
}
