package com.sankuai.shangou.logistics.delivery.questionnaire.mq.message;


import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryAsyncOutTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.RiderDeliveryExceptionEnum;
import com.sankuai.shangou.logistics.delivery.questionnaire.domain.Failure;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@SuppressWarnings("rawtypes")
public class DeliveryChangeSyncOutMessage<T extends DeliveryChangeSyncOutMessage.Body> {

    /**
     * 运单变更类型(支持添加更多类型)：
     * @see DeliveryAsyncOutTypeEnum
     *
     * 1-暂停配送（即锁定状态）；
     * 2-继续配送（解锁状态）；
     * 3-异常上报；
     */
    private Integer changeType;

    /**
     * 消息头，包含所有公共的字段，不要随意往该class添加字段
     */
    private Head head;

    /**
     * 具体的消息body，Json结构体，不同的changeType类型对应不同的Body子类（封装性）
     */
    private String body;

    public DeliveryChangeSyncOutMessage(Integer changeType, Head head, T body) {
        this.changeType = changeType;
        this.head = head;
        this.body = JacksonUtils.toJson(body);
    }

    /**
     * 返回body值
     * @param
     */
    public T fetchBody() {
        return (T) JacksonUtils.parse(this.body, this.typeReferenceMap().get(this.changeType));
    }

    /**
     * 该方法用于构造该消息体对应的类型Map，供typeReference方法使用，该Map本身不属于message消息体，所以不用成员变量来存储该map
     * @return
     */
    private Map<Integer, TypeReference> typeReferenceMap() {
        Map<Integer, TypeReference> map = new HashMap<>();
        map.put(DeliveryAsyncOutTypeEnum.REPORT_EXCEPTION.getValue(),
                new TypeReference<RiderReportExceptionBody>(){});
        map.put(DeliveryAsyncOutTypeEnum.LOCK_DELIVERY_STATUS.getValue(),
                new TypeReference<DeliveryStatusLockUnlockBody>(){});
        map.put(DeliveryAsyncOutTypeEnum.UNLOCK_DELIVERY_STATUS.getValue(),
                new TypeReference<DeliveryStatusLockUnlockBody>(){});
        map.put(DeliveryAsyncOutTypeEnum.DELIVER_COMPLETE.getValue(),
                new TypeReference<DeliveryCompleteBody>(){});
        map.put(DeliveryAsyncOutTypeEnum.DELIVERY_ACCEPT_OR_RIDER_CHANGE.getValue(),
                new TypeReference<DeliveryRiderChangeBody>(){});
        return map;
    }

    public void validCheck() {
        if (DeliveryAsyncOutTypeEnum.getInstance(changeType) == null) {
            throw new IllegalArgumentException("changeType is illegal");
        }
        head.checkValid();
        this.fetchBody().validCheck();
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Head {
        /**
         * 租户id
         */
        private Long tenantId;
        /**
         * 门店id
         */
        private Long shopId;

        /**
         * 运单号，对应delivery_order表的id主键
         */
        private Long deliveryId;

        /**
         * 订单业务类型
         * @see com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType
         */
        private Integer orderBizType;
        /**
         * 闪购赋能内部订单号
         */
        private Long orderId;
        /**
         * 渠道订单号，有的地方又叫viewOrderId
         */
        private String channelOrderId;

        /**
         * 配送状态
         * @see DeliveryStatusEnum
         */
        private Integer deliveryStatus;

        public Optional<Failure> checkValid() {
            if (tenantId == null) {
                return Optional.of(new Failure(false, FailureCodeEnum.INVALID_PARAM.getCode(),
                        "tenantId is illegal"));
            }
            if (shopId == null) {
                return Optional.of(new Failure(false, FailureCodeEnum.INVALID_PARAM.getCode(),
                        "shopId is illegal"));
            }
            //这里对orderBizType只做判空，因为本身该字段就支持任意值
            if (orderBizType == null) {
                return Optional.of(new Failure(false, FailureCodeEnum.INVALID_PARAM.getCode(),
                        "orderBizType is illegal"));
            }
            if (orderId == null) {
                return Optional.of(new Failure(false, FailureCodeEnum.INVALID_PARAM.getCode(),
                        "orderId is illegal"));
            }
            if (StringUtils.isBlank(channelOrderId)) {
                return Optional.of(new Failure(false, FailureCodeEnum.INVALID_PARAM.getCode(),
                        "viewOrderId is illegal"));
            }
            if (DeliveryStatusEnum.valueOf(deliveryStatus) == null) {
                return Optional.of(new Failure(false, FailureCodeEnum.INVALID_PARAM.getCode(),
                        "deliveryStatus is illegal"));
            }
            return Optional.empty();
        }
    }



    /**
     * 运单锁定/解锁消息Body
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class DeliveryStatusLockUnlockBody extends Body {

        private String riderName;

        @Override
        public void validCheck() {
            if (StringUtils.isBlank(riderName)) {
                throw new IllegalArgumentException("riderName is illegal");
            }
        }
    }

    /**
     * 运单配送完成Body
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class DeliveryCompleteBody extends Body {

        private String riderName;

        @Override
        public void validCheck() {
            if (StringUtils.isBlank(riderName)) {
                throw new IllegalArgumentException("riderName is illegal");
            }
        }
    }


    /**
     * 骑手上报异常消息Body
     */
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class RiderReportExceptionBody extends Body {

        /**
         * 异常类型
         */
        private Integer reportExceptionType;

        /**
         * 异常子类型
         */
        private Integer reportExceptionSubType;

        /**
         * 异常类型描述
         */
        private String reportExceptionTypeDesc;

        /**
         * 异常子类型描述
         */
        private String reportExceptionSubTypeDesc;

        /**
         * 用户真实地址
         */
        private String userRealAddress;

        /**
         * 骑手姓名
         */
        private String riderName;

        @Override
        public void validCheck() {
            if (StringUtils.isBlank(riderName)) {
                throw new IllegalArgumentException("riderName is illegal");
            }

            if (reportExceptionType == null || reportExceptionSubType == null ) {
                throw new IllegalArgumentException("reportExceptionType or reportExceptionSubType is illegal");
            }

            if (RiderDeliveryExceptionEnum.enumOf(reportExceptionType,reportExceptionSubType) == null) {
                throw new IllegalArgumentException("reportExceptionType or reportExceptionSubType is illegal");
            }
        }
    }

    /**
     * 运单接单或转单Body
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class DeliveryRiderChangeBody extends Body {

        /**
         * 骑手账号id
         */
        private Long riderAccountId;

        /**
         * 变更时间戳
         */
        private long changeTimestamp;

        /**
         * 配送状态
         * @see DeliveryStatusEnum
         */
        private Integer deliveryStatus;

        /**
         * 事件类型 1-接单 2-转单
         */
        private Integer eventType;

        @Override
        public void validCheck() {
            if (Objects.isNull(riderAccountId)) {
                throw new IllegalArgumentException("riderAccountId is illegal");
            }
        }
    }



    /**
     * Body为消息体的公共父类，不同类型的消息实现该类，不同类型的消息封装在子类中，降低修改影响范围
     */
    public static class Body {
        public void validCheck() {
            throw new IllegalArgumentException("body is illegal");
        }
    }
}
