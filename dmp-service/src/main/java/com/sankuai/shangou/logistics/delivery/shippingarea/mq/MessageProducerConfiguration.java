package com.sankuai.shangou.logistics.delivery.shippingarea.mq;


import com.sankuai.shangou.logistics.delivery.shippingarea.domain.ShippingAreaUpdateMessage;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;



/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/10
 */
@Configuration
public class MessageProducerConfiguration {

	/**
	 * 配送范围修改时通知外部
	 */
	@Bean
	public MafkaMessageProducer<ShippingAreaUpdateMessage> shippingAreaUpdateMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(MQProducerEnum.SHIPPING_AREA_UPDATE_TOPIC);
	}

}
