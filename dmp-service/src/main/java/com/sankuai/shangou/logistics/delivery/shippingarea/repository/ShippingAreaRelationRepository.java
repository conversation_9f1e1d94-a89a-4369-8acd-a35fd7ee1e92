package com.sankuai.shangou.logistics.delivery.shippingarea.repository;

import com.sankuai.shangou.logistics.delivery.mapper.ShippingAreaRelationPOExMapper;
import com.sankuai.shangou.logistics.delivery.mapper.ShippingAreaRelationPOMapper;
import com.sankuai.shangou.logistics.delivery.model.ShippingAreaRelationPO;
import com.sankuai.shangou.logistics.delivery.model.ShippingAreaRelationPOExample;
import com.sankuai.shangou.logistics.delivery.shippingarea.dto.ShippingAreaRelationDto;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.StatusEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/23 20:35
 **/
@Repository
public class ShippingAreaRelationRepository {
    @Resource
    private ShippingAreaRelationPOMapper shippingAreaRelationPOMapper;

    @Resource
    private ShippingAreaRelationPOExMapper shippingAreaRelationPOExMapper;

    @Resource(name = "sdmsTransactionTemplate")
    private TransactionTemplate sdmsTransactionTemplate;

    public void save(List<ShippingAreaRelationDto> relationDtos) {
        if (CollectionUtils.isEmpty(relationDtos)) {
            return;
        }
        shippingAreaRelationPOMapper.batchInsert(convert2POs(relationDtos));
    }

    public void delete(Long tenantId, Long storeId, List<Long> areaIds) {
        if (CollectionUtils.isEmpty(areaIds)) {
            return;
        }

        ShippingAreaRelationPOExample example = new ShippingAreaRelationPOExample();
        example.createCriteria().andTenantIdEqualTo(tenantId)
                .andStoreIdEqualTo(storeId)
                .andAreaIdIn(areaIds)
                .andStatusEqualTo(StatusEnum.VALID.getCode());

        ShippingAreaRelationPO relationPO = new ShippingAreaRelationPO();
        relationPO.setStatus(StatusEnum.INVALID.getCode());
        relationPO.setUpdateTime(LocalDateTime.now());
        shippingAreaRelationPOMapper.updateByExampleSelective(relationPO, example);
    }

    public void reset(Long tenantId, Long storeId, List<ShippingAreaRelationDto> relationDtos) {
        if (CollectionUtils.isEmpty(relationDtos)) {
            return;
        }
        ShippingAreaRelationPOExample example = new ShippingAreaRelationPOExample();
        example.createCriteria().andTenantIdEqualTo(tenantId)
                .andStoreIdEqualTo(storeId)
                .andStatusEqualTo(StatusEnum.VALID.getCode());

        ShippingAreaRelationPO relationPO = new ShippingAreaRelationPO();
        relationPO.setStatus(StatusEnum.INVALID.getCode());
        relationPO.setUpdateTime(LocalDateTime.now());

        sdmsTransactionTemplate.execute(new TransactionCallback<Boolean>() {
            @Override
            public Boolean doInTransaction(TransactionStatus status) {
                shippingAreaRelationPOMapper.updateByExampleSelective(relationPO, example);
                shippingAreaRelationPOMapper.batchInsert(convert2POs(relationDtos));
                return true;
            }
        });
    }

    public List<ShippingAreaRelationDto> query(List<Long> shippingAreaIds) {
        if (CollectionUtils.isEmpty(shippingAreaIds)) {
            return Collections.emptyList();
        }

        ShippingAreaRelationPOExample example = new ShippingAreaRelationPOExample();
        example.createCriteria()
                .andAreaIdIn(shippingAreaIds)
                .andStatusEqualTo(StatusEnum.VALID.getCode());

        List<ShippingAreaRelationPO> areaRelationPOS = shippingAreaRelationPOMapper.selectByExample(example);
        return convert2DtoList(areaRelationPOS);
    }

    public List<Long> queryInitializedStoreIds(Long tenantId) {
        return shippingAreaRelationPOExMapper.queryInitializedStoreIds(tenantId);
    }

    public void updateAreaId(Long tenantId, Long storeId, Long oldAreaId, Long newAreaId) {
        ShippingAreaRelationPOExample example = new ShippingAreaRelationPOExample();
        example.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andStoreIdEqualTo(storeId)
                .andAreaIdEqualTo(oldAreaId)
                .andStatusEqualTo(StatusEnum.VALID.getCode());

        ShippingAreaRelationPO po = new ShippingAreaRelationPO();
        po.setAreaId(newAreaId);
        po.setUpdateTime(LocalDateTime.now());
        shippingAreaRelationPOMapper.updateByExampleSelective(po, example);
    }

    List<ShippingAreaRelationPO> convert2POs(List<ShippingAreaRelationDto> relationDtos) {
        if(CollectionUtils.isEmpty(relationDtos)) {
            return Collections.emptyList();
        }

        return relationDtos.stream().filter(Objects::nonNull).map(this::convert2PO).collect(Collectors.toList());
    }

    ShippingAreaRelationPO convert2PO(ShippingAreaRelationDto relationDto) {
        ShippingAreaRelationPO po = new ShippingAreaRelationPO();
        po.setAreaId(relationDto.getAreaId());
        po.setTenantId(relationDto.getTenantId());
        po.setChannelId(relationDto.getChannelId());
        po.setStoreId(relationDto.getStoreId());
        po.setRelationId(relationDto.getRelationId());
        po.setStatus(relationDto.getStatus());
        po.setUpdateTime(LocalDateTime.now());
        po.setCreateTime(relationDto.getCreateTime() == null ? LocalDateTime.now() : relationDto.getCreateTime());

        return po;
    }

    List<ShippingAreaRelationDto> convert2DtoList(List<ShippingAreaRelationPO> poList) {
        if(CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }

        return poList.stream().filter(Objects::nonNull).map(this::convert2Dto).collect(Collectors.toList());
    }

    ShippingAreaRelationDto convert2Dto(ShippingAreaRelationPO po) {
        ShippingAreaRelationDto dto = new ShippingAreaRelationDto();
        dto.setAreaId(po.getAreaId());
        dto.setTenantId(po.getTenantId());
        dto.setChannelId(po.getChannelId());
        dto.setStoreId(po.getStoreId());
        dto.setRelationId(po.getRelationId());
        dto.setId(po.getId());
        dto.setCreateTime(po.getCreateTime());

        return dto;
    }

}
