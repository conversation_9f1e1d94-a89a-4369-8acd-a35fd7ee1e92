package com.sankuai.shangou.logistics.delivery.shippingarea.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "账号数据权限信息"
)
@Data
@NoArgsConstructor
public class AccountDataPermissionVO {

    @FieldDoc(
            description = "指标权限：1-有权限; 0-无权限"
    )
    private Integer indicatorPermission = 0;

}
