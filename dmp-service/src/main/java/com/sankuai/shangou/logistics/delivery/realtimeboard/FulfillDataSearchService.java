package com.sankuai.shangou.logistics.delivery.realtimeboard;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.sac.dto.model.SacAccountDto;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ScheduledEmployeeDTO;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryRiderStatistics;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryStoreStatistics;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TStaffRider;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.RiderStatisticsRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.StoreRiderAccountListRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.StoreRiderAccountListResponse;
import com.sankuai.shangou.bizmng.labor.api.employee.dto.EmployeeAttendanceInfoDTO;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.PaginationList;
import com.sankuai.shangou.commons.thrift.publisher.response.TPageResult;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.infra.osw.api.org.dto.response.DepartmentDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmpDepRelDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.PositionDTO;
import com.sankuai.shangou.logistics.delivery.common.wrapper.OswClient;
import com.sankuai.shangou.logistics.delivery.fulfill.dto.*;
import com.sankuai.shangou.logistics.delivery.fulfill.enums.IndicatorTypeEnum;
import com.sankuai.shangou.logistics.delivery.fulfill.request.*;
import com.sankuai.shangou.logistics.delivery.realtimeboard.converter.FulfillDataConverter;
import com.sankuai.shangou.logistics.delivery.realtimeboard.utils.MccUtils;
import com.sankuai.shangou.logistics.delivery.realtimeboard.wrapper.*;
import com.sankuai.shangou.logistics.delivery.tag.wrapper.BatchRequestHelper;
import com.sankuai.waimai.dws.protocol.structs.DWSParam;
import com.sankuai.waimai.dws.protocol.structs.DWSResponse;
import com.sankuai.waimai.dws.protocol.structs.Data;
import com.sankuai.waimai.dws.protocol.structs.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.shangou.logistics.delivery.constants.Constants.QUERY_POSITION_PAGE_SIZE;
import static com.sankuai.shangou.logistics.delivery.fulfill.enums.IndicatorTypeEnum.*;
import static com.sankuai.shangou.logistics.delivery.realtimeboard.converter.FulfillDataConverter.SIX_AM;
import static com.sankuai.shangou.logistics.delivery.realtimeboard.converter.FulfillDataConverter.convertStoreOrgInfo;


/**
 * <AUTHOR>
 * @Date 2022/7/28
 **/
@Service
@Slf4j
public class FulfillDataSearchService {

    @Resource
    private TenantPoiClient tenantPoiClient;

    @Autowired
    private PowerApiFacade powerApiFacade;

    @Resource
    private RiderQueryThriftService riderQueryThriftService;

    @Resource
    private RiderDeliveryStatisticsWrapper riderDeliveryStatisticsWrapper;

    @Resource
    private AttendanceServiceWrapper attendanceService;

    @Resource
    private OswClient oswClient;

    @Resource
    private DeliveryClient deliveryClient;

    @Resource
    private SacAccountServiceWrapper sacAccountServiceWrapper;

    @Value("${app.name}")
    private String appKey;

    private final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");


    /**
     * 查询履约累计汇总数据
     *
     * @param req
     * @return
     */
    public FulfillSummaryCumulativeResp queryCumulativeSummary(FulfillDataQueryRequest req) {
        Pair<LocalDate, LocalDate> startAndEndDate = FulfillDataConverter.getStartAndEndDate();
        Pair<LocalDate, LocalDate> refStartAndEndDate = FulfillDataConverter.getRefStartAndEndDate(startAndEndDate, req.getCompareType());
        Map<String, String> sqlParam = new HashMap<>();
        sqlParam.put("referenceBeginDate", refStartAndEndDate.getLeft().format(DATE_FORMATTER));
        sqlParam.put("referenceEndDate", refStartAndEndDate.getRight().format(DATE_FORMATTER));
        sqlParam.put("beginDate", startAndEndDate.getLeft().format(DATE_FORMATTER));
        sqlParam.put("endDate", startAndEndDate.getRight().format(DATE_FORMATTER));
        sqlParam.put("poiIds", Joiner.on(",").join(req.getStoreIds()));
        sqlParam.put("startHour", String.valueOf(SIX_AM.getHour()));
        sqlParam.put("endHour", String.valueOf(SIX_AM.getHour()));

        DWSParam dwsParam = buildDwsParam("b_service_waima_performance_overview_data_v2", sqlParam);
        DWSResponse dwsResponse = powerApiFacade.queryFromDW(dwsParam);


        return FulfillDataConverter.buildFulfillSummaryCumulativeResp(dwsResponse);

    }

    public TPageResult<FulfillCarrierStatisticsDTO> queryCarrierStatistic(FulfillDataPageQueryDTO req) {
        // 1.查询三方数仓 https://km.sankuai.com/collabpage/2100828792
        Pair<Long, DWSResponse> dwDataResponse = queryCarrierStatistic(req.getStoreIds(), req.getPageNo(), req.getPageSize());
        Data dwData = dwDataResponse.getRight().getData();
        Map<Long, PoiInfoDto> storeInfos = queryStoreInfoFromDwCarrierStatisticData(req.getTenantId(), dwData.getDataList());
        Map<Integer, String> carrierCode2Name = queryCarrierNameFromDwCarrierStatisticData(dwData.getDataList());
        // 2.根据门店信息查找组织架构，并拼接信息
        Map<Long, Long> departmentId2StoreId = storeInfos.entrySet().stream()
                .collect(Collectors.toMap(entry -> entry.getValue().getDepartmentId(), Map.Entry::getKey));
        List<DepartmentDTO> storeDepartmentInfos = oswClient.queryByDepartmentId(req.getTenantId(), new ArrayList<>(departmentId2StoreId.keySet()));
        List<Long> level3DepartmentIds = storeDepartmentInfos.stream().map(DepartmentDTO::getParentId).filter(Objects::nonNull).collect(Collectors.toList());
        List<DepartmentDTO> level3DepartmentInfos = oswClient.queryByDepartmentId(req.getTenantId(), level3DepartmentIds);
        List<Long> level2DepartmentIds = level3DepartmentInfos.stream().map(DepartmentDTO::getParentId).filter(Objects::nonNull).collect(Collectors.toList());
        List<DepartmentDTO> level2DepartmentInfos = oswClient.queryByDepartmentId(req.getTenantId(), level2DepartmentIds);
        return new TPageResult<>(0, null,
                new PaginationList<>(req.getPageNo(), req.getPageSize(), dwDataResponse.getLeft(), FulfillDataConverter.buildFulfillCarrierStatisticsVO(
                        dwData.getDataList(), storeInfos,
                        convertToStoreId2DepartmentName(departmentId2StoreId, storeDepartmentInfos, level3DepartmentInfos),
                        convertToStoreId2DepartmentName(departmentId2StoreId, storeDepartmentInfos, level3DepartmentInfos, level2DepartmentInfos),
                        carrierCode2Name
                ))
        );
    }

    public FulfillSummaryCarrierResp querySummaryCarrier(FulfillDataQueryRequest req) {
        // 查询三方数仓 https://km.sankuai.com/collabpage/2100828792
        Pair<LocalDate, LocalDate> startAndEndDate = FulfillDataConverter.getStartAndEndDate();
        Pair<LocalDate, LocalDate> refStartAndEndDate = FulfillDataConverter.getRefStartAndEndDate(startAndEndDate, req.getCompareType());
        Map<String, String> specialParam = new HashMap<>();
        specialParam.put("beginDate", startAndEndDate.getLeft().format(DATE_FORMATTER));
        specialParam.put("endDate", startAndEndDate.getRight().format(DATE_FORMATTER));
        specialParam.put("referenceBeginDate", refStartAndEndDate.getLeft().format(DATE_FORMATTER));
        specialParam.put("referenceEndDate", refStartAndEndDate.getRight().format(DATE_FORMATTER));
        specialParam.put("startHour", String.valueOf(SIX_AM.getHour()));
        specialParam.put("endHour", String.valueOf(SIX_AM.getHour()));
        // 排序，默认按照 仓ID + 三方运力Id 排序，固定写死
        specialParam.put("poiIds", Joiner.on(",").join(req.getStoreIds()));
        DWSResponse dwData = powerApiFacade.queryFromDW(buildDwsParam("b_service_waima_performance_overview_data_three_v2", specialParam));

        return FulfillDataConverter.buildFulfillSummaryCarrierResp(dwData.getData().getDataList());
    }

    /**
     * 查询分段趋势图-实时数据
     *
     * @param req
     * @return
     */
    public SegmentationFulfillDataResponse querySegmentationRealTime(SegmentationFulfillDataQueryRequest req) {
        Optional<String> checkMsg = req.validate();
        if (checkMsg.isPresent()) {
            log.error("请求参数不合法," + checkMsg.get() + "req:{}", req);
            throw new IllegalArgumentException(checkMsg.get());
        }

        //校验指标类型是否属于实时指标
        if (!isRealTimeIndicator(IndicatorTypeEnum.enumOf(req.getIndicatorType()))) {
            log.error("请求参数不合法,指标类型不合法,req:{}", req);
            throw new IllegalArgumentException("指标类型不属于实时指标");
        }

        Pair<String, String> indicatorStringPair = FulfillDataConverter.map2Fields(Objects.requireNonNull(IndicatorTypeEnum.enumOf(req.getIndicatorType())));
        Pair<LocalDate, LocalDate> startAndEndDate = FulfillDataConverter.getStartAndEndDate();
        Pair<LocalDate, LocalDate> refStartAndEndDate = FulfillDataConverter.getRefStartAndEndDate(startAndEndDate, req.getCompareType());

        Map<String, String> sqlParam = new HashMap<>();
        sqlParam.put("beginDate", startAndEndDate.getLeft().format(DATE_FORMATTER));
        sqlParam.put("endDate", startAndEndDate.getRight().format(DATE_FORMATTER));
        sqlParam.put("referenceBeginDate", refStartAndEndDate.getLeft().format(DATE_FORMATTER));
        sqlParam.put("referenceEndDate", refStartAndEndDate.getRight().format(DATE_FORMATTER));
        sqlParam.put("poiIds", Joiner.on(",").join(req.getStoreIds()));
        sqlParam.put("fields", Joiner.on(",").join(indicatorStringPair.getLeft(), indicatorStringPair.getRight()));
        sqlParam.put("startHour", String.valueOf(SIX_AM.getHour()));
        sqlParam.put("endHour", String.valueOf(SIX_AM.getHour()));
        DWSParam dwsParam = buildDwsParam("b_service_waima_time_segment_trend_chart_data_v2", sqlParam);

        DWSResponse dwsResponse = powerApiFacade.queryFromDW(dwsParam);


        return FulfillDataConverter.buildFulfillSegmentationDataResp(dwsResponse, req);

    }

    /**
     * 查询分段趋势图-累计数据
     *
     * @param req
     * @return
     */
    public SegmentationFulfillDataResponse querySegmentationCumulative(SegmentationFulfillDataQueryRequest req) {
        Optional<String> checkMsg = req.validate();
        if (checkMsg.isPresent()) {
            log.error("请求参数不合法," + checkMsg.get() + "req:{}", req);
            throw new IllegalArgumentException(checkMsg.get());
        }

        //校验指标类型是否属于累计指标
        if (!isCumulativeIndicator(IndicatorTypeEnum.enumOf(req.getIndicatorType()))) {
            log.error("请求参数不合法,指标类型不合法,req:{}", req);
            throw new IllegalArgumentException("指标类型不属于累计指标");
        }

        Pair<LocalDate, LocalDate> startAndEndDate = FulfillDataConverter.getStartAndEndDate();
        Pair<LocalDate, LocalDate> refStartAndEndDate = FulfillDataConverter.getRefStartAndEndDate(startAndEndDate, req.getCompareType());
        Pair<String, String> indicatorStringPair = FulfillDataConverter.map2Fields(Objects.requireNonNull(IndicatorTypeEnum.enumOf(req.getIndicatorType())));

        Map<String, String> sqlParam = new HashMap<>();
        sqlParam.put("beginDate", startAndEndDate.getLeft().format(DATE_FORMATTER));
        sqlParam.put("endDate", startAndEndDate.getRight().format(DATE_FORMATTER));
        sqlParam.put("referenceBeginDate", refStartAndEndDate.getLeft().format(DATE_FORMATTER));
        sqlParam.put("referenceEndDate", refStartAndEndDate.getRight().format(DATE_FORMATTER));
        sqlParam.put("poiIds", Joiner.on(",").join(req.getStoreIds()));
        sqlParam.put("fields", Joiner.on(",").join(indicatorStringPair.getLeft(), indicatorStringPair.getRight()));

        sqlParam.put("startHour", String.valueOf(SIX_AM.getHour()));
        sqlParam.put("endHour", String.valueOf(SIX_AM.getHour()));
        DWSParam dwsParam = buildDwsParam("b_service_waima_acc_trend_chart_data_v2", sqlParam);

        DWSResponse dwsResponse = powerApiFacade.queryFromDW(dwsParam);

        return FulfillDataConverter.buildFulfillSegmentationDataResp(dwsResponse, req);

    }

    public FulfillSummaryRealTimeResp querySummaryRealTime(FulfillDataQueryRequest request) {

        //查询tms获取门店维度统计信息
        List<TDeliveryStoreStatistics> storeStatisticsList =
                riderDeliveryStatisticsWrapper.batchQueryStoreStatisticsList(request.getTenantId(), request.getStoreIds());

        //根据门店id查询门店信息
        Map<Long, PoiInfoDto> storeDetailMap = tenantPoiClient.batchQueryPoiByIds(request.getTenantId(),
                request.getStoreIds());

        //查看门店当前有排班的员工
        Map<Long, List<ScheduledEmployeeDTO>> onDutyEmployeeListMap = attendanceService.batchQueryPoiOnDutyEmployeeList(request.getTenantId(), request.getStoreIds());

        //查看门店排班员工的岗位
        List<EmpDepRelDTO> empDepRelDTOS = oswClient.batchQueryBelongDepByEmp(request.getTenantId(), onDutyEmployeeListMap.values().stream().flatMap(Collection::stream).map(ScheduledEmployeeDTO::getEmployeeId).collect(Collectors.toList()));
        return FulfillDataConverter.buildFulfillSummaryRealTimeResp(request.getStoreIds(), storeDetailMap, FulfillDataConverter.convertStoreDbData(storeStatisticsList), onDutyEmployeeListMap, empDepRelDTOS);
    }

    public TPageResult<FulfillStoreStatisticsDTO> queryStoreStatistics(PageFulfillDataQueryRequest request) {
        //这里通过拆分storeId来达到分页的效果
        List<Long> sortedStoreIds = request.getStoreIds().stream().sorted(Long::compare).collect(Collectors.toList());
        List<List<Long>> storeIdLists = Lists.partition(sortedStoreIds, request.getPageSize());
        List<Long> pageStoreIdList = storeIdLists.get(request.getPageNo() - 1);

        //查询数仓
        DWSResponse dwsResponse = this.syncQueryDWStoreStatistics(pageStoreIdList);
        List<Map<String, String>> dwsDataList = dwsResponse.getData().getDataList();

        //查询tms
        List<TDeliveryStoreStatistics> storeStatisticsList =
                riderDeliveryStatisticsWrapper.batchQueryStoreStatisticsList(request.getTenantId(), pageStoreIdList);

        //查询门店排班
        Map<Long, Integer> onDutyEmployeeCount = attendanceService.batchQueryPoiOnDutyEmployeeCount(request.getTenantId(), pageStoreIdList);

        //查询门店信息
        Map<Long, PoiInfoDto> storeDetails = tenantPoiClient.batchQueryPoiByIds(request.getTenantId(), pageStoreIdList);

        //查组织结构
        Map<Long, SgStoreData> storeDwData = FulfillDataConverter.convertStoreDwData(dwsDataList);
        Set<Long> nearPoiFirstDepartmentIds = storeDwData.values().stream().map(SgStoreData::getNearPoiFirstDepartmentId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> nearPoiSecondDepartmentIds = storeDwData.values().stream().map(SgStoreData::getNearPoiSecondDepartmentId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<Long> departmentIds = new ArrayList<>();
        departmentIds.addAll(nearPoiFirstDepartmentIds);
        departmentIds.addAll(nearPoiSecondDepartmentIds);
        List<DepartmentDTO> departmentDTOS = oswClient.queryDepByIds(request.getTenantId(), departmentIds);
        Map<Long, String> departmentNameMap = IListUtils.nullSafeAndOverrideCollectToMap(departmentDTOS, DepartmentDTO::getDepartmentId, DepartmentDTO::getDepartmentName);


        List<FulfillStoreStatisticsDTO> storeStatisticsDTOS = FulfillDataConverter.buildFulfillStoreStatistics(
                pageStoreIdList,
                storeDetails,
                departmentNameMap,
                storeDwData,
                FulfillDataConverter.convertStoreDbData(storeStatisticsList), onDutyEmployeeCount);
        return TPageResult.buildSuccess(request.getPageNo(), request.getPageSize(), (long) request.getStoreIds().size(), storeStatisticsDTOS);
    }

    public TPageResult<FulfillRiderStatisticsDTO> queryRiderStatistics(PageFulfillDataQueryRequest request) {

        // 获取门店骑手信息并截取相应数量
        TPageResult<StaffRider> staffRiderPageVO = queryStaffRiderListByPage(request.getTenantId(), request.getStoreId(),
                new PageRequest(request.getPageNo(), request.getPageSize()), request.getAppId());
        if (CollectionUtils.isEmpty(staffRiderPageVO.getData().getList())) {
            return TPageResult.buildSuccess(staffRiderPageVO.getData().getPage(), staffRiderPageVO.getData().getPageSize(),
                    staffRiderPageVO.getData().getTotal(), Collections.emptyList());
        }
        List<Long> riderIdList = staffRiderPageVO.getData().getList().stream().map(StaffRider::getAccountId).collect(Collectors.toList());

        //查账号信息
        List<SacAccountDto> sacAccountDtos = sacAccountServiceWrapper.batchQueryAccountByAccountIdList(riderIdList);
        Map<Long, String> accountNameMap = sacAccountDtos.stream().collect(Collectors.toMap(SacAccountDto::getAccountId, SacAccountDto::getAccountName, (k1, k2) -> k1));


        //查询tms
        List<TDeliveryRiderStatistics> riderStatisticsList = riderDeliveryStatisticsWrapper.queryRiderStatistics(
                new RiderStatisticsRequest(request.getTenantId(), Collections.singletonList(request.getStoreId()), riderIdList));

        //查询数仓
        DWSResponse dwsResponse = syncQueryDWRiderStatistics(Collections.singletonList(request.getStoreId()), riderIdList);

        DWSResponse orgDwsResponse = syncQueryDWStoreOrgInfo(Collections.singletonList(request.getStoreId()));
        List<StoreOrgData> storeOrgData = convertStoreOrgInfo(orgDwsResponse.getData().getDataList());
        Map<Long, StoreOrgData> storeOrgInfoMap = storeOrgData.stream()
                .collect(Collectors.toMap(StoreOrgData::getPoiId, Function.identity(), (k1, k2) -> k2));

        //查询考勤数据
        LocalDate startOfMonth = LocalDate.of(LocalDate.now().getYear(), LocalDate.now().getMonth(), 1);
        LocalDate endOfMonth = startOfMonth.plusMonths(1).minusDays(1);
        List<EmployeeAttendanceInfoDTO> employeeAttendanceInfoDTOS = attendanceService.queryEmployeeAttendanceInfo(riderIdList, request.getTenantId(), startOfMonth, endOfMonth);
        Map<Long, EmployeeAttendanceInfoDTO> riderAttendanceInfoDTOMap =
                IListUtils.nullSafeAndOverrideCollectToMap(employeeAttendanceInfoDTOS, EmployeeAttendanceInfoDTO::getAccountId, Function.identity());

        //查询岗位信息
        List<Long> employeeIds = employeeAttendanceInfoDTOS.stream()
                .map(EmployeeAttendanceInfoDTO::getEmployeeId)
                .collect(Collectors.toList());
        List<EmpDepRelDTO> empDepRelDTOS = null;

        empDepRelDTOS = oswClient.batchQueryBelongDepByEmp(request.getTenantId(), employeeIds);

        // 去重 positionIds一次查询超过200个接口会报错 经查看日志发现有大量重复数据，故添加去重操作
        List<Long> positionIds = empDepRelDTOS.stream().map(EmpDepRelDTO::getPositionId).distinct().collect(Collectors.toList());
        // 分批查询岗位信息
        List<PositionDTO> positionDTOS = BatchRequestHelper.batchQuest(QUERY_POSITION_PAGE_SIZE, positionIds,
                subPositionIds -> oswClient.queryPositionByIds(request.getTenantId(), subPositionIds));
        Map<Long, PositionDTO> positionDTOMap = IListUtils.nullSafeAndOverrideCollectToMap(positionDTOS, PositionDTO::getId, Function.identity());
        Map<Long, List<EmpDepRelDTO>> employeeDepRelMap = empDepRelDTOS.stream().collect(Collectors.groupingBy(EmpDepRelDTO::getEmpId));

        //查门店信息
        Map<Long, PoiInfoDto> storeDetails = tenantPoiClient.batchQueryPoiByIds(request.getTenantId(), Collections.singletonList(request.getStoreId()));

        //查询组织结构信息
        Set<Long> nearPoiFirstDepartmentIds = storeOrgInfoMap.values().stream().map(StoreOrgData::getNearPoiFirstDepartmentId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> nearPoiSecondDepartmentIds = storeOrgInfoMap.values().stream().map(StoreOrgData::getNearPoiSecondDepartmentId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<Long> departmentIds = new ArrayList<>();
        departmentIds.addAll(nearPoiFirstDepartmentIds);
        departmentIds.addAll(nearPoiSecondDepartmentIds);
        List<DepartmentDTO> departmentDTOS = oswClient.queryDepByIds(request.getTenantId(), departmentIds);
        Map<Long, String> departmentNameMap = IListUtils.nullSafeAndOverrideCollectToMap(departmentDTOS, DepartmentDTO::getDepartmentId, DepartmentDTO::getDepartmentName);

        List<FulfillRiderStatisticsDTO> fulfillRiderStatisticsDTOS =
                FulfillDataConverter.buildFulfillRiderStatistics(request.getStoreId(),
                        storeDetails,
                        storeOrgInfoMap,
                        staffRiderPageVO.getData().getList(),
                        FulfillDataConverter.convertRiderDwData(dwsResponse.getData().getDataList()),
                        FulfillDataConverter.convertRiderDbData(riderStatisticsList),
                        riderAttendanceInfoDTOMap,
                        positionDTOMap,
                        employeeDepRelMap,
                        departmentNameMap,
                        accountNameMap);
        return TPageResult.buildSuccess(staffRiderPageVO.getData().getPage(), staffRiderPageVO.getData().getPageSize(),
                staffRiderPageVO.getData().getTotal(), fulfillRiderStatisticsDTOS);
    }

    /**
     * 查询部门维度统计
     * @param request
     * @return
     */
    public TPageResult<DepartmentFulfillDataDTO> queryDepartmentLevelStatistic(DepartmentFulfillDataQueryRequest request) {
        //查询数仓
        Pair<Long, DWSResponse> dwsResponsePair = this.syncQueryDepartmentLevelStatistics(request);
        Long totalCount = dwsResponsePair.getLeft();
        List<Map<String, String>> dwsDataList = dwsResponsePair.getRight().getData().getDataList();
        if (CollectionUtils.isEmpty(dwsDataList)) {
            return TPageResult.buildSuccess(request.getPageNo(), request.getPageSize(), totalCount, Collections.emptyList());
        }

        //获取门店id
        Map<Long, SgDepartmentData> departmentDwDataMap = FulfillDataConverter.convertDepartmentData(dwsDataList);
        List<Long> subPoiIds = departmentDwDataMap.values().stream()
                .map(SgDepartmentData::getPoiIds)
                .map(poiIdsStr -> {
                    if (StringUtils.isNotBlank(poiIdsStr)) {
                         return Arrays.asList(poiIdsStr.split(",")).stream().map(Long::valueOf).collect(Collectors.toList());
                    }

                    return new ArrayList<Long>();
                }).flatMap(Collection::stream).distinct().collect(Collectors.toList());

        //查询tms
        List<TDeliveryStoreStatistics> storeStatisticsList =
                riderDeliveryStatisticsWrapper.batchQueryStoreStatisticsList(request.getTenantId(), subPoiIds);


        //查询门店排班
        Map<Long, Integer> onDutyEmployeeCount = attendanceService.batchQueryPoiOnDutyEmployeeCount(request.getTenantId(), subPoiIds);


        //查组织结构
        List<DepartmentDTO> departmentDTOS = oswClient.queryDepByIds(request.getTenantId(), new ArrayList<>(departmentDwDataMap.keySet()));
        Map<Long, String> departmentNameMap = IListUtils.nullSafeAndOverrideCollectToMap(departmentDTOS, DepartmentDTO::getDepartmentId, DepartmentDTO::getDepartmentName);


        List<DepartmentFulfillDataDTO> departmentFulfillDataDTOS = FulfillDataConverter.buildDepartmentStatistics(departmentDwDataMap,
                FulfillDataConverter.convertStoreDbData(storeStatisticsList), onDutyEmployeeCount, departmentNameMap);

        return TPageResult.buildSuccess(request.getPageNo(), request.getPageSize(), totalCount, departmentFulfillDataDTOS);
    }


    /**
     * 查询异常单列表
     * @param request
     */
    public TPageResult<AbnormalOrderDTO> queryAbnormalOrderList(PageFulfillDataQueryRequest request) {
        //查询数仓
        Pair<Long, DWSResponse> abnormalOrderResponsePair = this.syncQueryAbnormalOrder(request);
        DWSResponse dwsResponse = abnormalOrderResponsePair.getRight();
        Long total = abnormalOrderResponsePair.getLeft();
        if (Objects.isNull(dwsResponse) || Objects.isNull(dwsResponse.getData()) || CollectionUtils.isEmpty(dwsResponse.getData().getDataList())) {
            return TPageResult.buildSuccess(request.getPageNo(), request.getPageSize(), total, Collections.emptyList());
        }

        List<SgAbnormalData> abnormalDataList = FulfillDataConverter.convertAbnormalData(abnormalOrderResponsePair.getRight().getData().getDataList());

        //查询门店信息
        Map<Long, PoiInfoDto> storeDetails = tenantPoiClient.batchQueryPoiByIds(request.getTenantId(), request.getStoreIds());

        //查账号信息
        List<SacAccountDto> sacAccountDtos = sacAccountServiceWrapper.batchQueryAccountByAccountIdList(abnormalDataList.stream().map(SgAbnormalData::getRiderAccountId).collect(Collectors.toList()));
        Map<Long, String> accountNameMap = sacAccountDtos.stream().collect(Collectors.toMap(SacAccountDto::getAccountId, SacAccountDto::getAccountName, (k1, k2) -> k1));


        //查组织结构
        Set<Long> nearPoiFirstDepartmentIds = abnormalDataList.stream().map(SgAbnormalData::getNearPoiFirstDepartmentId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> nearPoiSecondDepartmentIds = abnormalDataList.stream().map(SgAbnormalData::getNearPoiSecondDepartmentId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<Long> departmentIds = new ArrayList<>();
        departmentIds.addAll(nearPoiFirstDepartmentIds);
        departmentIds.addAll(nearPoiSecondDepartmentIds);
        List<DepartmentDTO> departmentDTOS = oswClient.queryDepByIds(request.getTenantId(), departmentIds);
        Map<Long, String> departmentNameMap = IListUtils.nullSafeAndOverrideCollectToMap(departmentDTOS, DepartmentDTO::getDepartmentId, DepartmentDTO::getDepartmentName);


        //查询考勤数据
        LocalDate startOfMonth = LocalDate.of(LocalDate.now().getYear(), LocalDate.now().getMonth(), 1);
        LocalDate endOfMonth = startOfMonth.plusMonths(1).minusDays(1);
        List<Long> riderIdList = abnormalDataList.stream().map(SgAbnormalData::getRiderAccountId).collect(Collectors.toList());
        List<EmployeeAttendanceInfoDTO> employeeAttendanceInfoDTOS = attendanceService.queryEmployeeAttendanceInfo(riderIdList, request.getTenantId(), startOfMonth, endOfMonth);
        Map<Long, EmployeeAttendanceInfoDTO> riderAttendanceInfoDTOMap =
                IListUtils.nullSafeAndOverrideCollectToMap(employeeAttendanceInfoDTOS, EmployeeAttendanceInfoDTO::getAccountId, Function.identity());

        //查询岗位信息
        List<Long> employeeIds = employeeAttendanceInfoDTOS.stream()
                .map(EmployeeAttendanceInfoDTO::getEmployeeId)
                .collect(Collectors.toList());
        List<EmpDepRelDTO> empDepRelDTOS = null;
        empDepRelDTOS = oswClient.batchQueryBelongDepByEmp(request.getTenantId(), employeeIds);
        List<Long> positionIds = empDepRelDTOS.stream().map(EmpDepRelDTO::getPositionId).collect(Collectors.toList());
        List<PositionDTO> positionDTOS = oswClient.queryPositionByIds(request.getTenantId(), positionIds);
        Map<Long, PositionDTO> positionDTOMap = IListUtils.nullSafeAndOverrideCollectToMap(positionDTOS, PositionDTO::getId, Function.identity());
        Map<Long, List<EmpDepRelDTO>> employeeDepRelMap = empDepRelDTOS.stream().collect(Collectors.groupingBy(EmpDepRelDTO::getEmpId));


        List<AbnormalOrderDTO> abnormalOrderDTOS = FulfillDataConverter.buildAbnormalStatistics(abnormalDataList, storeDetails, departmentNameMap,
                riderAttendanceInfoDTOMap, employeeDepRelMap, positionDTOMap, accountNameMap);
        return TPageResult.buildSuccess(request.getPageNo(), request.getPageSize(), total, abnormalOrderDTOS);
    }

    public static Map<Long,String> convertToStoreId2DepartmentName(Map<Long,Long> departmentId2StoreId,List<DepartmentDTO>... departmentInfos) {
        Map<Long, String> result = new HashMap<>();
        walkRootAndLeafNode((storeInfo, departmentInfo) -> {
            result.put(departmentId2StoreId.get(storeInfo.getDepartmentId()),departmentInfo.getDepartmentName());
        },departmentInfos);
        return result;
    }


    /**
     * 逐级遍历节点关系，最后调用runner传入 祖先节点和叶子节点
    * */
    @SafeVarargs
    private static void walkRootAndLeafNode(BiConsumer<DepartmentDTO,DepartmentDTO> runner, List<DepartmentDTO>... departmentInfos) {
        if(departmentInfos.length < 1) {
            return;
        }
        // 简单暴力便利
        for (DepartmentDTO storeInfo : departmentInfos[0]) {
            Long parentId = storeInfo.getParentId();
            for (List<DepartmentDTO> departmentInfo : Lists.newArrayList(departmentInfos).subList(1,departmentInfos.length)) {
                Optional<DepartmentDTO> parentInfo = departmentInfo.stream().filter(departmentDTO -> departmentDTO.getDepartmentId().equals(parentId))
                        .findFirst();
                if (!parentInfo.isPresent()) {
                    break;
                }
            }
            List<DepartmentDTO> parentDepartmentInfos = departmentInfos[departmentInfos.length - 1];
            parentDepartmentInfos
                    .stream()
                    .filter(v -> v.getDepartmentId().equals(parentId))
                    .findFirst()
                    .ifPresent(parent -> runner.accept(storeInfo,parent));
        }
    }

    private TPageResult<StaffRider> queryStaffRiderListByPage(Long tenantId, Long storeId, PageRequest pageRequest, Integer appId) {
        StoreRiderAccountListRequest request = new StoreRiderAccountListRequest(tenantId, storeId, appId);
        StoreRiderAccountListResponse response = riderQueryThriftService.storeRiderAccountList(request);
        if (response == null || response.getStatus().getCode() != Status.SUCCESS.getCode()) {
            throw new BizException("查询骑手接口失败");
        }
        if (CollectionUtils.isEmpty(response.getRiderList())) {
            return TPageResult.buildSuccess(pageRequest.getPageNo(), pageRequest.getPageSize(), 0L, Collections.emptyList());
        }
        response.getRiderList().sort(Comparator.comparing(TStaffRider::getAccountId));


        int startIndex = (pageRequest.getPageNo() - 1) * pageRequest.getPageSize();
        int endIndex = Math.min(pageRequest.getPageNo() * pageRequest.getPageSize(), response.getRiderList().size());
        if (startIndex >= response.getRiderList().size()) {
            return TPageResult.buildSuccess(pageRequest.getPageNo(), pageRequest.getPageSize(),
                    (long) response.getRiderList().size(), Collections.emptyList());
        }
        return TPageResult.buildSuccess(pageRequest.getPageNo(), pageRequest.getPageSize(), (long) response.getRiderList().size(),
                response.getRiderList().subList(startIndex, endIndex).stream().map(tStaffRider -> {
                    return new StaffRider(tStaffRider.getName(),tStaffRider.getPhone(),tStaffRider.getAccountId());
                }).collect(Collectors.toList()));
    }

    private Map<Long, PoiInfoDto> queryStoreInfoFromDwCarrierStatisticData(Long tenantId, List<Map<String, String>> dwData) {
        if (CollectionUtils.isEmpty(dwData)) {
            return Collections.emptyMap();
        }
        List<Long> storeIds = dwData.stream().map(list -> list.get("poiId"))
                .filter(Objects::nonNull)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
        return tenantPoiClient.batchQueryPoiByIds(tenantId, storeIds);
    }

    private Map<Integer, String> queryCarrierNameFromDwCarrierStatisticData(List<Map<String, String>> dwData) {
        if (CollectionUtils.isEmpty(dwData)) {
            return Collections.emptyMap();
        }
        Set<Integer> carrierCodes = dwData.stream().map(list -> list.get("carrierCode"))
                .filter(Objects::nonNull)
                .map(Integer::valueOf)
                .collect(Collectors.toSet());
        return deliveryClient.batchQueryDeliveryNameByCarrierCodes(carrierCodes);
    }

    public Pair<Long,DWSResponse> queryCarrierStatistic(List<Long> poiIds,Integer pageNo,Integer pageSize) {
        Pair<LocalDate, LocalDate> beginAndEndDate = FulfillDataConverter.getStartAndEndDate();
        Map<String, String> specialParam = new HashMap<String,String>(){{
            put("beginDate", beginAndEndDate.getLeft().format(DATE_FORMATTER));
            put("endDate", beginAndEndDate.getRight().format(DATE_FORMATTER));
            // 排序，默认按照 仓ID + 三方运力Id 排序
            put("sortcolumn","poiId desc,threeAllDelieryOrdNum desc");
            put("poiIds", Joiner.on(",").join(poiIds));
            put("startHour", String.valueOf(SIX_AM.getHour()));
            put("endHour", String.valueOf(SIX_AM.getHour()));
        }};
        DWSParam dwsParam = buildPageQueryDwsParam("b_service_waima_poi_performance_data_three_v2",  specialParam,
                pageNo, pageSize);
        Long count = powerApiFacade.queryCount(dwsParam);;
        return Pair.of(count, powerApiFacade.queryFromDW(dwsParam));
    }

    public DWSResponse syncQueryDWStoreStatistics(List<Long> storeIds) {
        Pair<LocalDate, LocalDate> beginAndEndDate = FulfillDataConverter.getStartAndEndDate();
        Map<String, String> sqlParam = new HashMap<>();
        sqlParam.put("poiIds", StringUtils.join(storeIds, ","));
        sqlParam.put("startHour", String.valueOf(SIX_AM.getHour()));
        sqlParam.put("endHour", String.valueOf(SIX_AM.getHour()));
        sqlParam.put("beginDate", beginAndEndDate.getLeft().format(DATE_FORMATTER));
        sqlParam.put("endDate", beginAndEndDate.getRight().format(DATE_FORMATTER));

        return powerApiFacade.queryFromDW(buildDwsParam("b_service_waima_poi_performance_data_v2", sqlParam));
    }

    public Pair<Long, DWSResponse> syncQueryDepartmentLevelStatistics(DepartmentFulfillDataQueryRequest request) {
        Map<String, String> sqlParam = new HashMap<>();
        Pair<LocalDate, LocalDate> beginAndEndDate = FulfillDataConverter.getStartAndEndDate();
        sqlParam.put("poiIds", StringUtils.join(request.getStoreIds(), ","));
        sqlParam.put("groupColumn", request.getDepartmentLevel());
        sqlParam.put("beginDate", beginAndEndDate.getLeft().format(DATE_FORMATTER));
        sqlParam.put("endDate", beginAndEndDate.getRight().format(DATE_FORMATTER));
        sqlParam.put("startHour", String.valueOf(SIX_AM.getHour()));
        sqlParam.put("endHour", String.valueOf(SIX_AM.getHour()));
        sqlParam.put("columnSort", "groupColumn asc");
        DWSParam pageQueryDwsParam = buildPageQueryDwsParam("b_service_waima_poi_org_one_two_performance_data", sqlParam, request.getPageNo(), request.getPageSize());

        Long totalCount = powerApiFacade.queryCount(pageQueryDwsParam);
        DWSResponse dwsResponse = powerApiFacade.queryFromDW(pageQueryDwsParam);
        return Pair.of(totalCount, dwsResponse);
    }

    private DWSResponse syncQueryDWStoreOrgInfo(List<Long> storeIds) {
        Map<String, String> sqlParam = new HashMap<>();
        sqlParam.put("poiIds", StringUtils.join(storeIds, ","));
        return powerApiFacade.queryFromDW(buildDwsParam("b_service_waima_poi_org_data", sqlParam));
    }

    private DWSResponse syncQueryDWRiderStatistics(List<Long> storeIds, List<Long> riderAccountIds) {
        Pair<LocalDate, LocalDate> beginAndEndDate = FulfillDataConverter.getStartAndEndDate();
        Map<String, String> sqlParam = new HashMap<>();
        sqlParam.put("poiIds", StringUtils.join(storeIds, ","));
        sqlParam.put("riderAccountIds", StringUtils.join(riderAccountIds, ","));
        sqlParam.put("startHour", String.valueOf(SIX_AM.getHour()));
        sqlParam.put("endHour", String.valueOf(SIX_AM.getHour()));
        sqlParam.put("beginDate", beginAndEndDate.getLeft().format(DATE_FORMATTER));
        sqlParam.put("endDate", beginAndEndDate.getRight().format(DATE_FORMATTER));
        return powerApiFacade.queryFromDW(buildDwsParam("b_service_waima_employee_performance_data_v2", sqlParam));
    }

    private Pair<Long, DWSResponse> syncQueryAbnormalOrder(PageFulfillDataQueryRequest request) {
        Pair<LocalDate, LocalDate> startAndEndDate = FulfillDataConverter.getStartAndEndDate();
        Map<String, String> sqlParam = new HashMap<>();
        sqlParam.put("poiIds", StringUtils.join(request.getStoreIds(), ","));
        sqlParam.put("startHour", String.valueOf(SIX_AM.getHour()));
        sqlParam.put("endHour", String.valueOf(SIX_AM.getHour()));
        sqlParam.put("beginDate", startAndEndDate.getLeft().format(DATE_FORMATTER));
        sqlParam.put("endDate", startAndEndDate.getRight().format(DATE_FORMATTER));
        sqlParam.put("columnSort", "riderName asc, orderIdView asc");

        if (CollectionUtils.isNotEmpty(request.getOperationModes())) {
            // 1-直营前置仓 2-加盟前置仓
            if (request.getOperationModes().contains(1) && request.getOperationModes().contains(2)) {
                sqlParam.put("is_eta_overtime_ord_v2_filter", "is_eta_overtime_v2");
                sqlParam.put("is_check_overtime_ord_filter", "is_chec_overtime");
            } else if (request.getOperationModes().contains(1)) {
                sqlParam.put("is_eta_overtime_ord_v2_filter", "is_eta_overtime_v2");
            } else if (request.getOperationModes().contains(2)) {
                sqlParam.put("is_check_overtime_ord_filter", "is_chec_overtime");
            }
        }

        //查分页数据
        DWSParam pageQueryDwsParam = buildPageQueryDwsParam("b_service_waima_poi_error_performance_data", sqlParam, request.getPageNo(), request.getPageSize());
        Long count = powerApiFacade.queryCount(pageQueryDwsParam);
        DWSResponse dwsResponse = powerApiFacade.queryFromDW(pageQueryDwsParam);

        return Pair.of(count, dwsResponse);
    }

    private DWSParam buildDwsParam(String apiCode, Map<String, String> sqlParam) {
        DWSParam dwsParam = new DWSParam();
        dwsParam.setAppKey(appKey);
        dwsParam.setAppCode("shangou_reco");
        dwsParam.setApiCode(apiCode);
        dwsParam.setToken(MccUtils.getDwsToken());
        dwsParam.setSqlParam(sqlParam);

        return dwsParam;
    }

    private DWSParam buildPageQueryDwsParam(String apiCode, Map<String, String> sqlParam, int pageNo, int pageSize) {
        DWSParam dwsParam = new DWSParam();
        dwsParam.setAppKey(appKey);
        dwsParam.setAppCode("shangou_reco");
        dwsParam.setApiCode(apiCode);
        dwsParam.setToken(MccUtils.getDwsToken());
        dwsParam.setSqlParam(sqlParam);
        dwsParam.setPage(new Page(pageNo, pageSize));
        dwsParam.setIsPage(true);

        return dwsParam;
    }

    private boolean isRealTimeIndicator(IndicatorTypeEnum indicatorTypeEnum) {
        List<IndicatorTypeEnum> realTimeIndicatorList = Arrays.asList(VALID_ORDER_COUNT,
                DELIVERER_ORDER_COUNT,
                NINETIETH_FULFILL_DURATION,
                AVG_FULFILL_DURATION,
                SERIOUS_TIMEOUT_ORDER_COUNT,
                JUDGE_TIMEOUT_ORDER_COUNT,
                DELIVERY_RATE_IN_25_MIN,
                DELIVERY_RATE_IN_15_MIN,
                ETA_TIMEOUT_ORDER_COUNT,
                ETA_BAD_TIMEOUT_ORDER_COUNT
                );
        return realTimeIndicatorList.contains(indicatorTypeEnum);
    }

    private boolean isCumulativeIndicator(IndicatorTypeEnum indicatorTypeEnum) {
        List<IndicatorTypeEnum> cumulativeSegmentIndicatorList = Arrays.asList(VALID_ORDER_COUNT,
                DELIVERER_ORDER_COUNT,
                SERIOUS_TIMEOUT_ORDER_COUNT,
                JUDGE_TIMEOUT_ORDER_COUNT,
                DELIVERY_RATE_IN_25_MIN,
                DELIVERY_RATE_IN_15_MIN,
                ETA_TIMEOUT_ORDER_COUNT,
                ETA_BAD_TIMEOUT_ORDER_COUNT);
        return cumulativeSegmentIndicatorList.contains(indicatorTypeEnum);
    }
}
