package com.sankuai.shangou.logistics.delivery.indicator.dto;

/**
 * <AUTHOR>
 * @date 2023-07-04
 * @email <EMAIL>
 */
public enum IndicatorPurposeEnum {

    SHOW(1, "展示用"),
    //ETA_CALC(2, "ETA计算用"), //暂时没有这个场景，备用
    SHOW_AND_ETA_CALC(3, "展示和计算用")
    ;

    private int type;
    private String desc;

    IndicatorPurposeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
