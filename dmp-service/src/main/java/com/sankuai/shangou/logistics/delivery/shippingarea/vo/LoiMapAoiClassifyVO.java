package com.sankuai.shangou.logistics.delivery.shippingarea.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.indicator.MapAoiClassifyDto;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "区域aoi分类指标"
)
@Data
public class LoiMapAoiClassifyVO {
    @FieldDoc(
            description = "位置id"
    )
    private Long loiId;

    @FieldDoc(
            description = "数据时间"
    )
    private String dt;

    @FieldDoc(
            description = "aoi分类信息列表"
    )
    private List<MapAoiClassifyDto> aoiClassifyList;
}
