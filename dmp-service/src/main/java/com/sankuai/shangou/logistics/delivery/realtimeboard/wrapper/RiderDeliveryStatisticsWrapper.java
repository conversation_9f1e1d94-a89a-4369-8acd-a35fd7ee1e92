package com.sankuai.shangou.logistics.delivery.realtimeboard.wrapper;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderDeliveryStatisticThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryRiderStatistics;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryStoreStatistics;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.RiderStatisticsRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.StoreStatisticsRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.RiderStatisticsResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.StoreStatisticsResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.realtimeboard.utils.CommonThreadPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;

@Component
@Slf4j
public class RiderDeliveryStatisticsWrapper {

    @Resource
    private RiderDeliveryStatisticThriftService riderDeliveryStatisticThriftService;

    @MethodLog(logResponse = true, logRequest = true)
    public List<TDeliveryRiderStatistics> queryRiderStatistics(RiderStatisticsRequest request) {
        RiderStatisticsResponse response = riderDeliveryStatisticThriftService.queryRiderStatistics(request);
        if (response == null || response.getStatus().getCode() != Status.SUCCESS.getCode()
                || CollectionUtils.isEmpty(response.getRiderStatisticsList())) {
            throw new BizException("调用riderDeliveryStatisticThriftService查询rider接口失败");
        }
        return response.getRiderStatisticsList();
    }

    @MethodLog(logResponse = true, logRequest = true)
    public List<TDeliveryStoreStatistics> queryStoreStatistics(StoreStatisticsRequest request) {
        StoreStatisticsResponse response = riderDeliveryStatisticThriftService.queryStoreStatistics(request);
        if (response == null || response.getStatus().getCode() != Status.SUCCESS.getCode()
                || CollectionUtils.isEmpty(response.getStoreStatisticsList())) {
            throw new BizException("调用riderDeliveryStatisticThriftService查询门店接口失败");
        }
        return response.getStoreStatisticsList();
    }

    @MethodLog(logResponse = true, logRequest = true)
    public List<TDeliveryStoreStatistics> batchQueryStoreStatisticsList(Long tenantId, List<Long> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Collections.emptyList();
        }

        if (storeIds.size() <= 50) {
            return this.queryStoreStatistics(new StoreStatisticsRequest(tenantId, storeIds));
        }
        List<List<Long>> storeIdListList = Lists.partition(storeIds, 50);
        List<Future<List<TDeliveryStoreStatistics>>> futureList = new ArrayList<>();
        for (List<Long> storeIdList : storeIdListList) {
            futureList.add(CommonThreadPool.commonThreadPool().submit(() ->
                    this.queryStoreStatistics(new StoreStatisticsRequest(tenantId, storeIdList))));
        }

        List<TDeliveryStoreStatistics> storeStatisticsList = new ArrayList<>();

        try {
            for (Future<List<TDeliveryStoreStatistics>> future : futureList) {
                storeStatisticsList.addAll(future.get());
            }
        } catch (Exception e) {
            log.error("riderDeliveryStatisticsWrapper.queryStoreStatistics error, msg:{}", e.getMessage(), e);
            throw new BizException(e.getMessage());
        }
        return storeStatisticsList;
    }
}
