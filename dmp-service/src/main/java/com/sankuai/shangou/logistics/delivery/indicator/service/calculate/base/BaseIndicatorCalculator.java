package com.sankuai.shangou.logistics.delivery.indicator.service.calculate.base;

import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ShiftAndScheduleCountDTO;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BaseIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.repository.dbo.IndicatorDoc;
import com.sankuai.shangou.logistics.delivery.indicator.utils.IndicatorSquirrelKeyUtils;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrderWithCurrentRider;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-07-03
 * @email <EMAIL>
 */
public interface BaseIndicatorCalculator {

    BaseIndicatorEnum getSupportIndicator();

    List<IndicatorDoc> calc(LocalDateTime fetchTime,
                            List<IndicatorSquirrelKeyUtils.IndicatorStoreKey> indicatorStoreKeys,
                            List<TRiderDeliveryOrderWithCurrentRider> thisDeliveryOrders,
                            Map<Long, ShiftAndScheduleCountDTO> idShiftDTOMap,
                            Map<Long, Long> employeeAccountIdMap,
                            Map<Long, List<ShiftAndScheduleCountDTO>> poiIdAndShiftDTOListMap);

}
