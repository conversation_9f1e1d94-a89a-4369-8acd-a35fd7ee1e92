package com.sankuai.shangou.logistics.delivery.seal.request;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/27 17:03
 **/
@Data
public class QuerySealContainerListRequest {
    private List<Long> warehouseIds;

    private String sealContainerCode;

    private String tradeOrderNo;

    private int pageSize;

    private int page;

    public String validate() {
        if (CollectionUtils.isEmpty(warehouseIds)) {
            return "门店列表不能为空";
        }

        if (pageSize <= 0) {
            return "pageSize不合法";
        }

        if (page <= 0) {
            return "pageNo不合法";
        }
        return null;
    }

}
