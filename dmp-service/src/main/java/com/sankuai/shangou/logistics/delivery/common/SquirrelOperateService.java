package com.sankuai.shangou.logistics.delivery.common;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.shangou.commons.utils.retry.RetryTemplateUtil;
import com.sankuai.shangou.logistics.delivery.common.utils.MccUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.RetryCallback;

import javax.annotation.Resource;
import java.util.Optional;

@Slf4j
public abstract class SquirrelOperateService {

    @Resource(name = "redisStoreOfcClient")
    private RedisStoreClient redisStoreOfcClient;

    protected RedisStoreClient getRedisClient() {
        return redisStoreOfcClient;
    }

    public abstract String getCategoryName();

    public <T> boolean set(String key, T data) {
        if (StringUtils.isEmpty(key) || data == null || StringUtils.isEmpty(getCategoryName())) {
            log.error("SquirrelOperateService.set param error . key:{},data:{},categoryName:{}", key, data, getCategoryName());
            return false;
        }
        try {

            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(MccUtils.getSquirrelOperateRetryCount(), MccUtils.getSquirrelOperateRetryPeriod())
                    .execute((RetryCallback<Boolean, Exception>) retryContext -> {
                        StoreKey storeKey = new StoreKey(getCategoryName(), key);
                        return getRedisClient().set(storeKey, JsonUtil.toJson(data));
                    });
            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.set error . key:{},data:{},categoryName:{}", key, data, getCategoryName(), e);
        }
        return false;
    }

    public <T> boolean set(String key, T data, int expireSeconds) {
        if (StringUtils.isEmpty(key) || data == null || StringUtils.isEmpty(getCategoryName())) {
            log.error("SquirrelOperateService.set param error. key:{},data:{}, categoryName:{}", key, data, getCategoryName());
            return false;
        }
        try {
            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(MccUtils.getSquirrelOperateRetryCount(),
                    MccUtils.getSquirrelOperateRetryPeriod()).execute((RetryCallback<Boolean, Exception>) retryContext -> {
                StoreKey storeKey = new StoreKey(getCategoryName(), key);
                return getRedisClient().set(storeKey, JsonUtil.toJson(data), expireSeconds);
            });
            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.set error. key:{}, data:{}, categoryName:{}", key, data, getCategoryName(), e);
        }
        return false;
    }

    public <T> Optional<T> get(String key, Class<T> clazz) {
        if (StringUtils.isEmpty(key)) {
            log.info("SquirrelOperateService.get key is empty ,key:{}", key);
            return Optional.empty();
        }

        try {
            StoreKey storeKey = new StoreKey(getCategoryName(), key);
            String result = RetryTemplateUtil.simpleWithFixedRetry(MccUtils.getSquirrelOperateRetryCount(), MccUtils.getSquirrelOperateRetryPeriod())
                    .execute((RetryCallback<String, Exception>) retryContext -> getRedisClient().get(storeKey));
            if (StringUtils.isEmpty(result)) {
                return Optional.empty();
            }
            T valObj = JsonUtil.fromJson(result, clazz);
            if (valObj == null) {
                return Optional.empty();
            }
            return Optional.of(valObj);
        } catch (Exception e) {
            log.info("SquirrelOperateService.get error ,key:{}", key, e);
        }
        return Optional.empty();
    }

    public boolean delete(String key) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(getCategoryName())) {
            log.error("SquirrelOperateService.delete param error. key:{}, categoryName:{}", key, getCategoryName());
            return false;
        }
        try {
            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(MccUtils.getSquirrelOperateRetryCount(), MccUtils.getSquirrelOperateRetryPeriod())
                    .execute((RetryCallback<Boolean, Exception>) retryContext -> {
                        StoreKey storeKey = new StoreKey(getCategoryName(), key);
                        return getRedisClient().delete(storeKey);
                    });
            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.delete error. key:{}, categoryName:{}", key, getCategoryName(), e);
        }
        return false;
    }
}
