package com.sankuai.shangou.logistics.delivery.configure.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.google.common.collect.Lists;
import com.sankuai.shangou.logistics.delivery.configure.model.value.AssessTimeModel;
import com.sankuai.shangou.logistics.delivery.configure.value.*;
import lombok.Getter;

import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-26
 */
public enum DeliveryConfigTypeEnum {
    RULES(10, "配送规则", DeliveryPlatformConfig.class),
    RULES_HELPER_WAY(11, "配送规则-手动转辅配方式(仅可设置转聚合)", SecondDeliveryWayConfig.class),
    RULES_HELPER_RULES(12, "配送规则-手动转辅配规则(仅对商家自送生效)", SecondDeliveryForbiddenRuleConfig.class),

    DISPLAY(30, "界面信息", null),
    DISPLAY_TIME_REMAINING(31, "界面信息-剩余时长", AssessTimeModel.class),
    DISPLAY_CONFIRM_ACTION(32, "界面信息-确认送达操作", DeliveryCompleteMode.class),
    DISPLAY_RIDER_TRANS(33, "界面信息-配送转骑手范围", RiderTransConfig.class),
    DISPLAY_NAVIGATION(34, "界面信息-跳转导航", NavigationConfig.class),

    REMIND(50, "配送提醒", DeliveryRemindConfig.class);

    private final Integer code;
    @Getter
    private final String desc;
    @Getter
    private final Class<?> cls;

    DeliveryConfigTypeEnum(Integer code, String desc, Class<?> cls) {
        this.code = code;
        this.desc = desc;
        this.cls = cls;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    @JsonCreator
    public static DeliveryConfigTypeEnum enumOf(Integer code) {
        return Stream.of(values()).filter(item -> Objects.equals(item.getCode(), code))
                .findFirst().orElse(null);
    }

    /**
     * 门店配送维度配置类型
     */
    public static final List<DeliveryConfigTypeEnum> deliveryDimensionConfigTypes = Lists.newArrayList(DISPLAY_TIME_REMAINING, DISPLAY_CONFIRM_ACTION,
            DISPLAY_RIDER_TRANS, DISPLAY_NAVIGATION, REMIND);
    /**
     * 渠道门店维度配置类型
     */
    public static final List<DeliveryConfigTypeEnum> deliveryPoiConfigTypes = Lists.newArrayList(RULES, RULES_HELPER_WAY, RULES_HELPER_RULES);
}
