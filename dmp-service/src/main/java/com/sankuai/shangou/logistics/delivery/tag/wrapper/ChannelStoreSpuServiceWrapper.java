package com.sankuai.shangou.logistics.delivery.tag.wrapper;

import com.sankuai.meituan.shangou.xsupply.product.client.dto.Status;
import com.sankuai.meituan.shangou.xsupply.product.client.request.channelspu.ChannelSpuPageQueryRequest;
import com.sankuai.meituan.shangou.xsupply.product.client.response.channelspu.ChannelSpuPageQueryResponse;
import com.sankuai.meituan.shangou.xsupply.product.client.service.ChannelStoreSpuThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024-04-22
 * @email <EMAIL>
 */
@Slf4j
@Service
public class ChannelStoreSpuServiceWrapper {

    @Resource
    private ChannelStoreSpuThriftService channelStoreSpuThriftService;

    @Retryable(backoff = @Backoff(delay = 100))
    public ChannelSpuPageQueryResponse pageQuerySpuList(long tenantId, Set<Long> storeIds, Set<Integer> channelIds, int pageNo) {
        try {
            ChannelSpuPageQueryRequest request = new ChannelSpuPageQueryRequest();
            request.setTenantId(tenantId);
            request.setStoreIdSet(storeIds);
            request.setChannelIdSet(channelIds);
            request.setPageNo(pageNo);
            request.setPageSize(Constants.QUERY_SKU_CENTER_PAGE_SIZE);
            ChannelSpuPageQueryResponse response = channelStoreSpuThriftService.pageQuery(request);
            log.info("invoke channelStoreSpuThriftService.pageQuery, req = {} , resp = {}", request, response);
            if (!Objects.equals(response.getStatus().getCode(), Status.SUCCESS.getCode())) {
                throw new SystemException("查询门店spu错误");
            }
            return response;
        } catch (Exception e) {
            log.error("invoke channelStoreSpuThriftService.pageQuery error", e);
            throw new SystemException("查询门店spu错误");
        }
    }
}
