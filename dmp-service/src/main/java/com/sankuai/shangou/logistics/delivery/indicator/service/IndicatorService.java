package com.sankuai.shangou.logistics.delivery.indicator.service;


import com.dianping.cat.Cat;
import com.dianping.squirrel.client.StoreKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ChannelOrderIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderInfoVo;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.user.UserDto;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ShiftAndScheduleCountDTO;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.logistics.delivery.indicator.constants.CatEventEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.*;
import com.sankuai.shangou.logistics.delivery.indicator.repository.BaseIndicatorDataSquirrelRepository;
import com.sankuai.shangou.logistics.delivery.indicator.repository.BaseIndicatorEagleRepository;
import com.sankuai.shangou.logistics.delivery.indicator.repository.dbo.IndicatorDoc;
import com.sankuai.shangou.logistics.delivery.indicator.service.calculate.base.BaseIndicatorCalculator;
import com.sankuai.shangou.logistics.delivery.indicator.service.calculate.biz.BizIndicatorCalculator;
import com.sankuai.shangou.logistics.delivery.indicator.service.wrapper.*;
import com.sankuai.shangou.logistics.delivery.indicator.utils.MccUtils;
import com.sankuai.shangou.logistics.delivery.mapper.WaiMaDeliveryLoadPOMapper;
import com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryLoadPO;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.indicator.utils.IndicatorSquirrelKeyUtils;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrderWithCurrentRider;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-07-02
 * @email <EMAIL>
 */
@Slf4j
@Service
public class IndicatorService {

    @Resource
    private BaseIndicatorDataSquirrelRepository baseIndicatorDataSquirrelRepository;
    @Resource
    private RiderQueryServiceWrapper riderQueryServiceWrapper;
    @Resource
    private ScheduleShiftServiceWrapper scheduleShiftServiceWrapper;
    @Resource
    private UserServiceWrapper userServiceWrapper;
    @Resource
    private List<BaseIndicatorCalculator> baseIndicatorCalculators;
    @Resource
    private BaseIndicatorEagleRepository baseIndicatorEagleRepository;
    @Resource
    private List<BizIndicatorCalculator> bizIndicatorCalculators;
    @Resource
    private ChannelStoreServiceWrapper channelStoreServiceWrapper;
    @Resource
    private WaiMaDeliveryLoadPOMapper waiMaDeliveryLoadPOMapper;
    @Resource
    private OrderServiceWrapper orderServiceWrapper;


    public void syncIndicator(long tenantId, LocalDateTime fetchTime, LocalDateTime taskTime, List<Long> shardPoiIdList) {
        LocalDate bizDay = OmTimetable.getInstance(IndicatorConstants.startTime, IndicatorConstants.openTime, IndicatorConstants.closeTime).getBizDay(fetchTime);
        Map<Long, List<IndicatorSquirrelKeyUtils.IndicatorStoreKey>> poiIndicatorStoreKeyMap = queryActiveLaborInfo(bizDay, fetchTime, shardPoiIdList);

        //没有数据的由展示层兜底，不在写入侧兜底，不然插入大量无用数据影响性能
        //这里查询太多内存遭不住,对远程服务还有影响; 查的太少循环多，尽量适中
        List<Long> hasActiveLaborPoi = shardPoiIdList.stream().filter(poiIndicatorStoreKeyMap::containsKey).collect(Collectors.toList());

        List<List<Long>> partitionShardPoiIdList = Lists.partition(hasActiveLaborPoi, RiderQueryServiceWrapper.MAX_QUERY_SIZE);
        //分批查询，分批写入es
        for (List<Long> poiIds : partitionShardPoiIdList) {
            //1.查运单
            List<TRiderDeliveryOrderWithCurrentRider> thisRiderDeliveryOrders = getRiderDeliveryOrder(tenantId, poiIds);
            Map<Long, List<TRiderDeliveryOrderWithCurrentRider>> poiDeliveryOrderMap = IListUtils.nullSafeGroupBy(thisRiderDeliveryOrders, TRiderDeliveryOrderWithCurrentRider::getStoreId);

            //2.查班次
            Map<Long, ShiftAndScheduleCountDTO> idShiftDTOMap;
            List<Long> shiftIds = IListUtils
                    .nullSafeStream(poiIds)
                    .map(poiIndicatorStoreKeyMap::get)
                    .flatMap(Collection::stream)
                    .map(IndicatorSquirrelKeyUtils.IndicatorStoreKey::getShiftId)
                    .filter(shiftId -> !Objects.equals(shiftId, IndicatorSquirrelKeyUtils.UNSCHEDULED_SHIFT_ID))
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, List<ShiftAndScheduleCountDTO>> poiIdAndShiftDTOListMap = scheduleShiftServiceWrapper.queryScheduleShiftListByPoiIdAndDate(poiIds, bizDay);
            if (CollectionUtils.isNotEmpty(shiftIds)) {
                List<ShiftAndScheduleCountDTO> shiftAndScheduleCountDTOS = IListUtils.nullSafeStream(poiIdAndShiftDTOListMap.entrySet()).flatMap(entry -> entry.getValue().stream()).collect(Collectors.toList());
                idShiftDTOMap = IListUtils.nullSafeAndOverrideCollectToMap(shiftAndScheduleCountDTOS, ShiftAndScheduleCountDTO::getId, Function.identity());
            } else {
                idShiftDTOMap = Maps.newHashMap();
            }

            //3.查employeeId和AccountId的映射
            Map<Long, Long> accountIdEmployeeIdMap = getAccountIdEmployeeIdMap(tenantId, thisRiderDeliveryOrders);

            //4.process sing poi
            List<IndicatorDoc> indicatorDocList = Lists.newArrayList();
            for (Long poiId : poiIds) {
                List<TRiderDeliveryOrderWithCurrentRider> thisDeliveryOrders = poiDeliveryOrderMap.getOrDefault(poiId, Lists.newArrayList());
                //不需要判空了，上面判断过了
                List<IndicatorSquirrelKeyUtils.IndicatorStoreKey> indicatorStoreKeys = poiIndicatorStoreKeyMap.getOrDefault(poiId, Lists.newArrayList());

                for (BaseIndicatorEnum baseIndicatorEnum : BaseIndicatorEnum.values()) {
                    for (BaseIndicatorCalculator baseIndicatorCalculator : baseIndicatorCalculators) {
                        if (!Objects.equals(baseIndicatorCalculator.getSupportIndicator(), baseIndicatorEnum)) {
                            continue;
                        }
                        indicatorDocList.addAll(baseIndicatorCalculator.calc(fetchTime, indicatorStoreKeys, thisDeliveryOrders, idShiftDTOMap, accountIdEmployeeIdMap, poiIdAndShiftDTOListMap));
                    }
                }
            }
            //5.补时间数据
            indicatorDocList.forEach(indicatorDoc -> indicatorDoc.fillTime(fetchTime, fetchTime));

            //6.负载结果写MySQL，供ETA计算用（重要）
            Map<Long, List<IndicatorDoc>> logisticsUnitIdIndicatorMap = IListUtils.nullSafeGroupBy(indicatorDocList, IndicatorDoc::getLogisticsUnitId);
            Map<BizIndicatorEnum, BizIndicatorCalculator> bizCalculatorMap = bizIndicatorCalculators.stream().collect(Collectors.toMap(BizIndicatorCalculator::getSupportIndicator, Function.identity()));
            Map<Long, List<ChannelPoiInfoDTO>> poiChannelMap = channelStoreServiceWrapper.queryPoiDetailInfoByPoiIds(tenantId, poiIds);

            //7.拿到LogisticsUnitId -> bizIndicator 映射
            Map<Long, List<IndicatorDTO>> logisticsUnitIdBizIndicatorMap = getLogisticsUnitIdBizIndicatorMap(logisticsUnitIdIndicatorMap, bizCalculatorMap);

            //8.转换为PO
            List<WaiMaDeliveryLoadPO> waiMaDeliveryLoadPOList = convertToPOList(fetchTime, poiChannelMap, logisticsUnitIdBizIndicatorMap);
            //batch insert
            waiMaDeliveryLoadPOMapper.batchInsert(waiMaDeliveryLoadPOList);

            //9.写ES,供展示用（次要）
            if(!MccUtils.getQuiteWriteESSwitch()) {
                baseIndicatorEagleRepository.batchUpsert(indicatorDocList);
            }

        }
        //10.处理没有活跃的门店，补0
        processNoActiveWarehouse(tenantId, fetchTime, shardPoiIdList, hasActiveLaborPoi);

    }

    private List<TRiderDeliveryOrderWithCurrentRider> getRiderDeliveryOrder(long tenantId, List<Long> poiIds) {
        List<TRiderDeliveryOrderWithCurrentRider> thisRiderDeliveryOrders = riderQueryServiceWrapper.queryDeliveryOrderByPoiAndStatusList(
                tenantId, poiIds, Lists.newArrayList(
                        DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode(),
                        DeliveryStatusEnum.RIDER_ASSIGNED.getCode(),
                        DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode()
                )
        );
        //过滤一元单
        if (CollectionUtils.isNotEmpty(thisRiderDeliveryOrders)) {
            Map<String, OrderInfoVo> channelOrderMap = orderServiceWrapper.orderList(
                    tenantId,
                    IListUtils.mapTo(
                            thisRiderDeliveryOrders,
                            deliveryOrder -> new ChannelOrderIdCondition(deliveryOrder.getChannelOrderId(), ChannelOrderConvertUtils.sourceBiz2Mid(deliveryOrder.getOrderBizTypeCode()))
                    )
            );
            thisRiderDeliveryOrders = IListUtils.nullSafeFilterElement(thisRiderDeliveryOrders, deliveryOrder -> {
                if (channelOrderMap.containsKey(deliveryOrder.getChannelOrderId())) {
                    OrderInfoVo orderInfoVo = channelOrderMap.get(deliveryOrder.getChannelOrderId());
                    return IListUtils.nullSafeStream(orderInfoVo.getTags()).noneMatch(tag -> Objects.equals(tag.getType(), 102) && Objects.equals(tag.getValue(), "一元购"));
                }
                //没包含也算上
                return true;
            });
        }
        return thisRiderDeliveryOrders;
    }

    private Map<Long, Long> getAccountIdEmployeeIdMap(long tenantId, List<TRiderDeliveryOrderWithCurrentRider> thisRiderDeliveryOrders) {
        Map<Long, Long> accountIdEmployeeIdMap = Maps.newHashMap();
        List<Long> accountIds = IListUtils.nullSafeStream(thisRiderDeliveryOrders)
                .filter(deliveryOrder -> Objects.nonNull(deliveryOrder.getCurrentRider()))
                .map(deliveryOrder -> deliveryOrder.getCurrentRider().getAccountId())
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(accountIds)) {
            List<UserDto> userDtos = userServiceWrapper.queryTenantUserByAccountIds(tenantId, accountIds);
            accountIdEmployeeIdMap = IListUtils.nullSafeAndOverrideCollectToMap(
                    userDtos, UserDto::getEmpId, UserDto::getAccountId
            );
        }
        return accountIdEmployeeIdMap;
    }

    private void processNoActiveWarehouse(long tenantId, LocalDateTime fetchTime, List<Long> shardPoiIdList, List<Long> hasActiveLaborPoi) {
        List<Long> noActivePoiIdList = IListUtils.nullSafeFilterElement(shardPoiIdList, poiId -> !hasActiveLaborPoi.contains(poiId));
        List<IndicatorDoc> nonActiveDocList = Lists.newArrayList();
        List<WaiMaDeliveryLoadPO> nonActivePOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(noActivePoiIdList)) {
            for (BaseIndicatorEnum value : BaseIndicatorEnum.values()) {
                for (Long noActivePoiId : noActivePoiIdList) {
                    nonActiveDocList.add(IndicatorDoc.buildDefaultZeroIndicatorDoc(noActivePoiId, value.getIndicatorCode(), fetchTime, fetchTime));
                }
            }

            Map<Long, List<ChannelPoiInfoDTO>> poiChannelMap = channelStoreServiceWrapper.queryPoiDetailInfoByPoiIds(tenantId, noActivePoiIdList);
            for (Map.Entry<Long, List<ChannelPoiInfoDTO>> entry : poiChannelMap.entrySet()) {
                for (ChannelPoiInfoDTO channelPoiInfoDTO : entry.getValue()) {
                    WaiMaDeliveryLoadPO waiMaDeliveryLoadPO = new WaiMaDeliveryLoadPO();
                    waiMaDeliveryLoadPO.setLogisticsUnitId(entry.getKey());
                    waiMaDeliveryLoadPO.setChannelId(channelPoiInfoDTO.getChannelId());
                    waiMaDeliveryLoadPO.setChannelPoiId(channelPoiInfoDTO.getChannelPoiId());
                    waiMaDeliveryLoadPO.setScheduledEmployeeDeliveryLoadIncludeUnscheduledOrder(BigDecimal.ZERO);
                    waiMaDeliveryLoadPO.setScheduledEmployeeDeliveryLoadExcludeUnscheduledOrder(BigDecimal.ZERO);
                    waiMaDeliveryLoadPO.setAttendanceDeliveryLoad(BigDecimal.ZERO);
                    waiMaDeliveryLoadPO.setFulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder(BigDecimal.ZERO);
                    waiMaDeliveryLoadPO.setFulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder(BigDecimal.ZERO);
                    waiMaDeliveryLoadPO.setBizTime(fetchTime);
                    waiMaDeliveryLoadPO.setCreateTime(LocalDateTime.now());
                    waiMaDeliveryLoadPO.setUpdateTime(LocalDateTime.now());
                    waiMaDeliveryLoadPO.setIsDeleted(0);
                    nonActivePOList.add(waiMaDeliveryLoadPO);
                }
            }
        }
        //batch insert
        if (CollectionUtils.isNotEmpty(nonActivePOList)) {
            waiMaDeliveryLoadPOMapper.batchInsert(nonActivePOList);
        }

        //insert es
        if (CollectionUtils.isNotEmpty(nonActiveDocList)) {
            if (!MccUtils.getQuiteWriteESSwitch()) {
                baseIndicatorEagleRepository.batchUpsert(nonActiveDocList);
            }
        }
    }

    private List<WaiMaDeliveryLoadPO> convertToPOList(LocalDateTime fetchTime, Map<Long, List<ChannelPoiInfoDTO>> poiChannelMap, Map<Long, List<IndicatorDTO>> logisticsUnitIdBizIndicatorMap) {
        List<WaiMaDeliveryLoadPO> waiMaDeliveryLoadPOList = Lists.newArrayList();
        for (Map.Entry<Long, List<IndicatorDTO>> entry : logisticsUnitIdBizIndicatorMap.entrySet()) {
            List<ChannelPoiInfoDTO> channelPoiInfoDTOS = poiChannelMap.get(entry.getKey());
            if (CollectionUtils.isEmpty(channelPoiInfoDTOS)) {
                Cat.logEvent(CatEventEnum.MISS_CHANNEL.getType(), CatEventEnum.MISS_CHANNEL.getName());
                continue;
            }
            Map<BizIndicatorEnum, BigDecimal> bizIndicatorValueMap = IListUtils
                    .nullSafeStream(entry.getValue())
                    .collect(Collectors.toMap(
                                    it -> BizIndicatorEnum.enumOf(it.getCode()),
                                    IndicatorDTO::getValue,
                                    BigDecimal::add
                            )
                    );

            for (ChannelPoiInfoDTO channelPoiInfoDTO : channelPoiInfoDTOS) {
                WaiMaDeliveryLoadPO waiMaDeliveryLoadPO = new WaiMaDeliveryLoadPO();
                waiMaDeliveryLoadPO.setLogisticsUnitId(entry.getKey());
                waiMaDeliveryLoadPO.setChannelId(channelPoiInfoDTO.getChannelId());
                waiMaDeliveryLoadPO.setChannelPoiId(channelPoiInfoDTO.getChannelPoiId());
                waiMaDeliveryLoadPO.setScheduledEmployeeDeliveryLoadIncludeUnscheduledOrder(processResult(bizIndicatorValueMap, BizIndicatorEnum.SCHEDULED_EMPLOYEE_DELIVERY_LOAD_INCLUDE_UNSCHEDULED_ORDER));
                waiMaDeliveryLoadPO.setScheduledEmployeeDeliveryLoadExcludeUnscheduledOrder(processResult(bizIndicatorValueMap, BizIndicatorEnum.SCHEDULED_EMPLOYEE_DELIVERY_LOAD_EXCLUDE_UNSCHEDULED_ORDER));
                waiMaDeliveryLoadPO.setAttendanceDeliveryLoad(processResult(bizIndicatorValueMap, BizIndicatorEnum.ATTENDANCE_DELIVERY_LOAD));
                waiMaDeliveryLoadPO.setFulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder(processResult(bizIndicatorValueMap, BizIndicatorEnum.FULFILL_EMPLOYEE_DELIVERY_LOAD_INCLUDE_WAIT_ACCEPT_ORDER));
                waiMaDeliveryLoadPO.setFulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder(processResult(bizIndicatorValueMap, BizIndicatorEnum.FULFILL_EMPLOYEE_DELIVERY_LOAD_EXCLUDE_WAIT_ACCEPT_ORDER));
                waiMaDeliveryLoadPO.setBizTime(fetchTime);
                waiMaDeliveryLoadPO.setCreateTime(LocalDateTime.now());
                waiMaDeliveryLoadPO.setUpdateTime(LocalDateTime.now());
                waiMaDeliveryLoadPO.setIsDeleted(0);
                waiMaDeliveryLoadPOList.add(waiMaDeliveryLoadPO);
            }
        }
        return waiMaDeliveryLoadPOList;
    }

    private BigDecimal processResult(Map<BizIndicatorEnum, BigDecimal> bizIndicatorValueMap, BizIndicatorEnum bizIndicatorEnum) {
        BigDecimal bigDecimal = bizIndicatorValueMap.getOrDefault(bizIndicatorEnum, BigDecimal.ZERO);
        return Optional.ofNullable(bigDecimal).orElse(BigDecimal.ZERO);
    }

    private Map<Long, List<IndicatorDTO>> getLogisticsUnitIdBizIndicatorMap(Map<Long, List<IndicatorDoc>> logisticsUnitIdIndicatorMap, Map<BizIndicatorEnum, BizIndicatorCalculator> bizCalculatorMap) {
        Map<Long, List<IndicatorDTO>> logisticsUnitIdBizIndicatorMap = Maps.newHashMap();
        for (Map.Entry<Long, List<IndicatorDoc>> entry : logisticsUnitIdIndicatorMap.entrySet()) {
            List<IndicatorDTO> resultLogisticsUnitIndicatorDTOs = Lists.newArrayList();
            for (BizIndicatorEnum bizIndicatorEnum : BizIndicatorEnum.values()) {
                if (Objects.equals(bizIndicatorEnum.getIndicatorPurposeEnum(), IndicatorPurposeEnum.SHOW)) {
                    continue;
                }

                List<IndicatorDTO> indicatorDTOS = IListUtils.mapTo(entry.getValue(), IndicatorDTO::fromDoc);
                List<IndicatorDTO> bizIndicatorList = bizCalculatorMap.get(bizIndicatorEnum).calc(IListUtils.nullSafeGroupBy(indicatorDTOS, IndicatorDTO::getCode));
                if (CollectionUtils.isEmpty(bizIndicatorList)) {
                    Cat.logEvent(CatEventEnum.MISS_RESULT.getType(), CatEventEnum.MISS_RESULT.getName());
                    continue;
                }
                resultLogisticsUnitIndicatorDTOs.addAll(bizIndicatorList);
            }
            logisticsUnitIdBizIndicatorMap.put(entry.getKey(), resultLogisticsUnitIndicatorDTOs);
        }
        return logisticsUnitIdBizIndicatorMap;
    }

    private Map<Long/*poiId*/, List<IndicatorSquirrelKeyUtils.IndicatorStoreKey>> queryActiveLaborInfo(LocalDate bizDay, LocalDateTime fetchTime, List<Long> shardPoiIdList) {
        Map<Long/*poiId*/, List<IndicatorSquirrelKeyUtils.IndicatorStoreKey>> resultMap = Maps.newHashMap();
        for (Long poiId : shardPoiIdList) {
            //这里按bizDay来scan出来的
            List<StoreKey> storeKeys = baseIndicatorDataSquirrelRepository.scanBizTodayBaseIndicatorData(poiId, bizDay);
            if (CollectionUtils.isEmpty(storeKeys)) {
                continue;
            }
            List<IndicatorSquirrelKeyUtils.IndicatorStoreKey> validIndicatorStoreKeys =
                    Lists.newArrayList(IListUtils.nullSafeStream(storeKeys)
                            .map(IndicatorSquirrelKeyUtils::parseIndicatorStoreKey)
                            .filter(Optional::isPresent)
                            .map(Optional::get)
                            //筛选班次时间在fetchTime的
                            .filter(indicatorStoreKey -> TimeUtils.isInRangeInOneDayAndNextDay(indicatorStoreKey.getShiftStartTime(), indicatorStoreKey.getShiftEndTime(), fetchTime.toLocalTime()))
                            //过滤人，每个人只算一次
                            .collect(Collectors.toMap(
                                            IndicatorSquirrelKeyUtils.IndicatorStoreKey::getEmployeeId,
                                            it -> it,
                                            (older, newer) -> {
                                                if (older.isShiftScheduled()) {
                                                    return older;
                                                } else {
                                                    return newer;
                                                }
                                            }
                                    )
                            ).values()
                    );


            if (CollectionUtils.isEmpty(validIndicatorStoreKeys)) {
                continue;
            }

            resultMap.put(poiId, validIndicatorStoreKeys);
        }
        return resultMap;
    }

    @Data
    private static class ActiveLaborAndShiftInfo {
        private Map<Long, ShiftAndScheduleCountDTO> idShiftDTOMap;

        private Map<Long/*warehouseId*/, List<IndicatorSquirrelKeyUtils.IndicatorStoreKey>> warehouseActiveLaborMap;
    }
}
