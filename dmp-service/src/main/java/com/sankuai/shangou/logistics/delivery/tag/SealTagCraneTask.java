package com.sankuai.shangou.logistics.delivery.tag;

import com.alibaba.fastjson.JSON;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.shangou.goodscenter.dto.GoodsCategoryDto;
import com.meituan.shangou.goodscenter.dto.GoodsSkuRelationDto;
import com.meituan.shangou.goodscenter.dto.TenantGoodsDetailDto;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiListResponse;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.api.tag.ProductTagDTO;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.channelspu.ChannelSkuDTO;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.channelspu.ChannelSpuPageQueryDTO;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.tag.SystemMarkingDataDto;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.tag.SystemUnmarkingDataDto;
import com.sankuai.meituan.shangou.xsupply.product.client.response.channelspu.ChannelSpuPageQueryResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.retry.RetryTemplateUtil;
import com.sankuai.shangou.logistics.delivery.constants.Constants;
import com.sankuai.shangou.logistics.delivery.tag.entity.ChannelSpuKey;
import com.sankuai.shangou.logistics.delivery.tag.entity.SealRule;
import com.sankuai.shangou.logistics.delivery.tag.entity.SkuGoodsKey;
import com.sankuai.shangou.logistics.delivery.tag.entity.TagSealGoods;
import com.sankuai.shangou.logistics.delivery.tag.service.SealTagService;
import com.sankuai.shangou.logistics.delivery.tag.wrapper.ChannelStoreSpuServiceWrapper;
import com.sankuai.shangou.logistics.delivery.tag.wrapper.GoodsServiceWrapper;
import com.sankuai.shangou.logistics.delivery.tag.wrapper.ProductTagServiceWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.RetryCallback;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-04-22
 * @email <EMAIL>
 */
@Slf4j
@CraneConfiguration
@Service
public class SealTagCraneTask {

    @Resource
    private PoiThriftService poiThriftService;
    @Resource
    private ChannelStoreSpuServiceWrapper channelStoreSpuServiceWrapper;
    @Resource
    private SealTagService sealTagService;

    private static final String TAG_SEAL_RETRY_LION_KEY = "tag.seal.retry.goods.stores";
    private static final String TAG_SEAL_RETRY_LION_KEY_USERNAME = "dmp-admin";
    private static final String TAG_SEAL_RETRY_LION_KEY_PASSWORD = "59J1608118";


    @Crane("dh-seal-tag-task")
    public void tagSealGoods(TagSealGoods param) {

        long tenantId = Objects.nonNull(param.getTenantId()) ? param.getTenantId() : Constants.DEFAULT_TENANT_ID;

        //先尝试重试的
        List<Long> storeIdlist = Lion.getConfigRepository().getList(TAG_SEAL_RETRY_LION_KEY, Long.class, Lists.newArrayList());
        if (CollectionUtils.isEmpty(storeIdlist)) {
            storeIdlist = getPoiIdList(tenantId);
        }

        List<Long> failStoreIds = Lists.newArrayList();
        for (Long storeId : storeIdlist) {
            try {
                ChannelSpuPageQueryResponse channelSpuPageQueryResponse = channelStoreSpuServiceWrapper.pageQuerySpuList(tenantId, Sets.newHashSet(storeId), Sets.newHashSet(DynamicChannelType.MEITUAN.getChannelId(), DynamicChannelType.MT_DRUNK_HORSE.getChannelId()), 1);
                if (CollectionUtils.isEmpty(channelSpuPageQueryResponse.getSpuList())) {
                    continue;
                }
                sealTagService.syncSealTag(tenantId, storeId, convertToChannelSpuKeyList(channelSpuPageQueryResponse.getSpuList()));
                if (channelSpuPageQueryResponse.getPageInfo().getTotalPage() > 1) {
                    for (int i = 2; i <= channelSpuPageQueryResponse.getPageInfo().getTotalPage(); i++) {
                        ChannelSpuPageQueryResponse thisChannelSpuPageQueryResponse = channelStoreSpuServiceWrapper.pageQuerySpuList(tenantId, Sets.newHashSet(storeId), Sets.newHashSet(DynamicChannelType.MEITUAN.getChannelId(), DynamicChannelType.MT_DRUNK_HORSE.getChannelId()), i);
                        sealTagService.syncSealTag(tenantId, storeId, convertToChannelSpuKeyList(thisChannelSpuPageQueryResponse.getSpuList()));
                    }
                }
            } catch (Exception e) {
                log.error("process syncSealTag error, storeId = {}", storeId, e);
                failStoreIds.add(storeId);
            }
        }
        if (CollectionUtils.isNotEmpty(failStoreIds)) {
            //添加需要重试的门店
            Lion.getConfigRepository(
                    "com.sankuai.shangou.logistics.oio", TAG_SEAL_RETRY_LION_KEY_USERNAME, TAG_SEAL_RETRY_LION_KEY_PASSWORD
            ).setValue(TAG_SEAL_RETRY_LION_KEY, JSON.toJSONString(storeIdlist));
            throw new SystemException("存在打标失败的门店,storeIds = {}" + storeIdlist);
        }

    }


    private List<Long> getPoiIdList(long tenantId) {
        try {
            List<PoiInfoDto> poiInfoDtoList = RetryTemplateUtil.defaultRetryTemplate.execute(
                    (RetryCallback<List<PoiInfoDto>, Exception>) context -> {
                        PoiListResponse poiListResponse = poiThriftService.queryTenantPoiList(tenantId);
                        if (!poiListResponse.getStatus().isSuccess()) {
                            throw new SystemException("invoke poiThriftService.queryTenantPoiList error");
                        }
                        return poiListResponse.getPoiList();
                    }
            );
            return IListUtils.mapTo(poiInfoDtoList, PoiInfoDto::getPoiId);
        } catch (Exception e) {
            log.error("查询门店错误", e);
            throw new SystemException("查询门店错误", e);
        }
    }

    private List<ChannelSpuKey> convertToChannelSpuKeyList(List<ChannelSpuPageQueryDTO> spuDTOList) {
        return IListUtils.mapTo(
                spuDTOList,
                channelSpuPageQueryDTO -> {
                    ChannelSpuKey channelSpuKey = new ChannelSpuKey();
                    channelSpuKey.setSpuId(channelSpuPageQueryDTO.getSpuId());
                    channelSpuKey.setStoreId(channelSpuPageQueryDTO.getStoreId());
                    channelSpuKey.setChannelId(channelSpuPageQueryDTO.getChannelId());
                    channelSpuKey.setSkuList(
                            IListUtils.mapTo(
                                    channelSpuPageQueryDTO.getSkuList(),
                                    channelSkuDTO -> {
                                        ChannelSpuKey.ChannelSkuKey channelSkuKey = new ChannelSpuKey.ChannelSkuKey();
                                        channelSkuKey.setSkuId(channelSkuDTO.getSkuId());
                                        channelSkuKey.setRetailPrice(channelSkuDTO.getRetailPrice());
                                        return channelSkuKey;
                                    }
                            )
                    );
                    return channelSpuKey;
                }
        );
    }

}
