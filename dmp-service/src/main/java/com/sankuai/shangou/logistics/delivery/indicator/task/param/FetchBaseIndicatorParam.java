package com.sankuai.shangou.logistics.delivery.indicator.task.param;

import lombok.Data;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-30
 * @email <EMAIL>
 */
@Data
public class FetchBaseIndicatorParam {

    private Long tenantId;

    //活跃分片数，如果部分分片失败，可以指定分片重刷；为空时所有分片都响应
    @Nullable
    private List<Integer> activeShardItems;

    //取数时间，主要用于失败后重刷
    @Nullable
    private String fetchTime;
}
