package com.sankuai.shangou.logistics.delivery.configure.model;

import com.sankuai.shangou.logistics.delivery.configure.enums.CompletedSortModeEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.InternalNavigationModeEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.SelfDeliveryModeEnum;
import com.sankuai.shangou.logistics.delivery.configure.model.value.AssessTimeConfig;
import com.sankuai.shangou.logistics.delivery.configure.value.DeliveryCompleteMode;
import com.sankuai.shangou.logistics.delivery.configure.value.DeliveryRemindConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/7/02
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
public class DeliveryDimensionPoi {

	/** 主键ID */
	private Long id;

	/** 租户ID */
	private Long tenantId;

	/** 门店ID */
	private Long storeId;

	/** 跳转导航模式 */
	private InternalNavigationModeEnum internalNavigationMode;

	/** 剩余时长配置*/
	private List<AssessTimeConfig> assessTimeConfigs;

	/** 确认送达操作配置 */
	private DeliveryCompleteMode deliveryCompleteMode;

	/** 配送转骑手范围(角色id列表) */
	private List<Long> riderTransRoles;

	/** 已送达列表排序模式 */
	private CompletedSortModeEnum completedSortMode;

	/** 配送提醒设置 */
	private DeliveryRemindConfig deliveryRemindConfig;

	/** 创建时间 */
	private LocalDateTime createdAt;

	/** 更新时间 */
	private LocalDateTime updatedAt;


}
