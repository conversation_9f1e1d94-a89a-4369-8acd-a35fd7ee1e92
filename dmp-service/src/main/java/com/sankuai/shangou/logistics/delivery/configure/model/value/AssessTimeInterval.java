package com.sankuai.shangou.logistics.delivery.configure.model.value;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-16
 * @email <EMAIL>
 */
@Data
public class AssessTimeInterval {

    @FieldDoc(description = "值")
    private List<String> values;

    @FieldDoc(description = "区间类型")
    private Integer intervalType;

}
