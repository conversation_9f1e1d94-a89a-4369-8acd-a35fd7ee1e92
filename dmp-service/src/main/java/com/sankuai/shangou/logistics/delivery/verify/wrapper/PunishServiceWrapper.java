package com.sankuai.shangou.logistics.delivery.verify.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.waima.support.api.pl.dto.punish.PunishTicketSimpleDto;
import com.sankuai.shangou.waima.support.api.pl.request.punish.PunishTicketFormAssociationQueryReq;
import com.sankuai.shangou.waima.support.api.service.punish.TPunishService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/14 14:53
 **/
@Rhino
@Slf4j
public class PunishServiceWrapper {

    @Resource
    private TPunishService tPunishService;

    @Degrade(rhinoKey = "PunishServiceWrapper.queryPunishTicketFormAssociation", fallBackMethod = "queryPunishTicketFormAssociationFallback", timeoutInMilliseconds = 2000)
    public List<PunishTicketSimpleDto> queryPunishTicketFormAssociation(Long tenantId, List<String> associationKeys, List<String> dataSources) {
        PunishTicketFormAssociationQueryReq request = new PunishTicketFormAssociationQueryReq();
        request.setTenantId(tenantId);
        request.setAssociationKeys(associationKeys);
        request.setDataSources(dataSources);
        log.info("start invoke tPunishService.queryPunishTicketFormAssociation, request: {}", request);
        TResult<List<PunishTicketSimpleDto>> tResult = tPunishService.queryPunishTicketFormAssociation(request);
        log.info("end invoke tPunishService.queryPunishTicketFormAssociation, result: {}", tResult);
        if (!tResult.isSuccess()) {
            throw new BizException("查询落罚单信息失败," + tResult.getMsg());
        }

        return tResult.getData();
    }

    public List<PunishTicketSimpleDto> queryPunishTicketFormAssociationFallback(Long tenantId, List<String> associationKeys, List<String> dataSources) {
        log.error("PunishServiceWrapper.queryPunishTicketFormAssociation 发生降级");
        return Collections.emptyList();
    }
}
