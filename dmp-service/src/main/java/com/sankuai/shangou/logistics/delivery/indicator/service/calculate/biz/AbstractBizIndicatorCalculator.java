package com.sankuai.shangou.logistics.delivery.indicator.service.calculate.biz;


import com.sankuai.shangou.logistics.delivery.indicator.dto.BaseIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BizIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorDTO;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorGroupType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/24 19:43
 **/
@Slf4j
public abstract class AbstractBizIndicatorCalculator implements BizIndicatorCalculator {
    @Override
    public abstract List<BaseIndicatorEnum> getRelateBaseIndicators();

    @Override
    public abstract BizIndicatorEnum getSupportIndicator();

    @Override
    public abstract List<IndicatorDTO> calc(Map<String, List<IndicatorDTO>> baseIndicatorMap);

    @Override
    public boolean checkBaseIndicatorDataIsValid(Map<String, List<IndicatorDTO>> baseIndicatorMap) {
        List<BaseIndicatorEnum> baseIndicatorEnums = getRelateBaseIndicators();
        boolean hasInvalidData = baseIndicatorEnums.stream().anyMatch(baseIndicatorEnum -> {
            return !(baseIndicatorMap.containsKey(baseIndicatorEnum.getIndicatorCode())) || CollectionUtils.isEmpty(baseIndicatorMap.get(baseIndicatorEnum.getIndicatorCode()))
                    || baseIndicatorMap.get(baseIndicatorEnum.getIndicatorCode()).stream().anyMatch(indicatorDTO -> indicatorDTO.getValue() == null);
        });

        return !hasInvalidData;
    }

    protected List<IndicatorDTO> calcBySingleBaseIndicator(Map<String, List<IndicatorDTO>> baseIndicatorMap) {
        //如果基础指标数据不合法 直接返回空
        if (!checkBaseIndicatorDataIsValid(baseIndicatorMap)) {
            log.error("基础指标数据不全或不合法, baseIndicatorMap: {}, bizIndicatorEnum: {}", baseIndicatorMap, getSupportIndicator());
            return Collections.emptyList();
        }

        //只有单一指标才能使用默认计算方法
        if (!aggregateBySingleBaseIndicator()) {
            log.error("该业务指标不是通过单一基础指标聚合的，请自己实现计算方式");
            throw new RuntimeException();
        }

        if (getSupportIndicator().getIndicatorGroupType() == IndicatorGroupType.BY_SINGLE_INDICATOR) {
            IndicatorDTO baseIndicator = baseIndicatorMap.get(getRelateBaseIndicators().get(0).getIndicatorCode()).get(0);
            return Collections.singletonList(buildWithSingleBaseIndicator(buildWithSingleBaseIndicator(baseIndicator)));
        } else if (getSupportIndicator().getIndicatorGroupType() == IndicatorGroupType.BY_SHIFT) {
            return baseIndicatorMap.get(getRelateBaseIndicators().get(0).getIndicatorCode())
                    .stream()
                    .map(this::buildWithSingleBaseIndicator).collect(Collectors.toList());
        }

        log.error("不识别的指标聚合方式, indicatorGroupType: {}", getSupportIndicator().getIndicatorGroupType());
        throw new RuntimeException();
    }

    protected boolean aggregateBySingleBaseIndicator() {
        return CollectionUtils.isNotEmpty(getRelateBaseIndicators()) && getRelateBaseIndicators().size() == 1 ;
    }

    protected IndicatorDTO buildWithSingleBaseIndicator(IndicatorDTO baseIndicator) {
        return IndicatorDTO.builder()
                .logisticsUnitId(baseIndicator.getLogisticsUnitId())
                .code(getSupportIndicator().getIndicatorCode())
                .fragmentName(getSupportIndicator().getDesc())
                .bizTime(baseIndicator.getBizTime())
                .calculateTime(LocalDateTime.now())
                .value(baseIndicator.getValue())
                .property(baseIndicator.getProperty())
                .build();
    }

    protected IndicatorDTO buildWithBaseIndicatorAndBizValue(IndicatorDTO baseIndicator, BigDecimal bizValue) {
        return IndicatorDTO.builder()
                .logisticsUnitId(baseIndicator.getLogisticsUnitId())
                .code(getSupportIndicator().getIndicatorCode())
                .fragmentName(getSupportIndicator().getDesc())
                .bizTime(baseIndicator.getBizTime())
                .calculateTime(LocalDateTime.now())
                .value(bizValue)
                .build();
    }
}
