package com.sankuai.shangou.logistics.delivery.push.freemark;

import com.sankuai.shangou.logistics.delivery.enums.FreeMarkTemplateEnum;
import com.sankuai.shangou.logistics.delivery.push.freemark.constants.TemplateMappingCons;
import freemarker.template.Configuration;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerConfigurationFactoryBean;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import javax.annotation.Resource;
import java.io.IOException;

@Service
@Slf4j
public class FreeMarkerTemplateService {

    @Resource
    private Configuration freeMarkerConfigurationFactoryBean;

    public String getDXTemplateMessage(FreeMarkTemplateEnum templateEnum, Object dataView) throws Exception{
        String url= TemplateMappingCons.getDXTemplateByEnum(templateEnum);
        if(StringUtils.isBlank(url)){
            throw new Exception("未配置模板");
        }
        try {
            return FreeMarkerTemplateUtils.processTemplateIntoString(
                    freeMarkerConfigurationFactoryBean.getTemplate(url), dataView);
        } catch (IOException ioe) {
            log.info("FreeMarkerTemplateService error",ioe);
            throw ioe;
        } catch (TemplateException te) {
            log.info("getDXTemplateMessage error",te);
            throw te;
        }
    }

}

