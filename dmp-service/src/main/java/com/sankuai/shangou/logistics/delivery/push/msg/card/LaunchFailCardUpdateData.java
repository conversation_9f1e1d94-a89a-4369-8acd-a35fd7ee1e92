package com.sankuai.shangou.logistics.delivery.push.msg.card;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 发单失败卡片更新数据
 * @date 2025-05-20
 */
@Data
public class LaunchFailCardUpdateData extends AbstractCardUpdateData {
    /**
     * 按钮禁用状态
     */
    private Boolean disabledButton = Boolean.TRUE;


    @Override
    public String getPublicDataJsonStr() {
        JSONObject jsonObject = new JSONObject();
        setCardTitleMap(jsonObject);
        jsonObject.put("disabledButton", disabledButton);
        return jsonObject.toJSONString();
    }

    @Override
    public String getPrivateDataJsonStr() {
        JSONObject jsonObject = new JSONObject();
        setCardTitleMap(jsonObject);
        jsonObject.put("disabledButton", disabledButton);
        return jsonObject.toJSONString();
    }
}
