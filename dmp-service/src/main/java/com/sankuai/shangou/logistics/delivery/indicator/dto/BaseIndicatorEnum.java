package com.sankuai.shangou.logistics.delivery.indicator.dto;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/7/3 10:48
 **/
public enum BaseIndicatorEnum {

    WAIT_ACCEPT_ORDER_COUNT("wait_accept_order_count", 1, "待领取单量", IndicatorGroupType.BY_SINGLE_INDICATOR),

    WAIT_PICK_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT("wait_pick_by_scheduled_employee_order_count", 1, "当前有排班的员工待取货单量", IndicatorGroupType.BY_SINGLE_INDICATOR),

    WAIT_PICK_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT("wait_pick_by_unscheduled_employee_order_count", 1, "当前未排班的员工待取货单量", IndicatorGroupType.BY_SINGLE_INDICATOR),

    DELIVERED_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT("delivered_by_scheduled_employee_order_count", 1, "当前有排班的员工配送中单量", IndicatorGroupType.BY_SINGLE_INDICATOR),

    DELIVERED_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT("delivered_by_unscheduled_employee_order_count", 1, "当前未排班的员工配送中单量", IndicatorGroupType.BY_SINGLE_INDICATOR),

    SCHEDULED_EMPLOYEE_COUNT("scheduled_employee_count", 1, "当前排班员工数量", IndicatorGroupType.BY_SHIFT),

    SCHEDULED_ATTENDANCE_EMPLOYEE_COUNT("scheduled_attendance_employee_count", 1, "当前有排班的出勤员工数", IndicatorGroupType.BY_SHIFT),

    UNSCHEDULED_ATTENDANCE_EMPLOYEE_COUNT("unscheduled_attendance_employee_count", 1, "当前未排班的出勤员工数",  IndicatorGroupType.BY_SHIFT),

    //身上有运单的员工数
    FULFILLING_EMPLOYEE_COUNT("fulfilling_employee_count", 1, "履约中员工数",  IndicatorGroupType.BY_SINGLE_INDICATOR),
    ;

    private String indicatorCode;
    /** 1表示仓 */
    private Integer logisticsUnitType;
    private String desc;
    private IndicatorGroupType indicatorGroupType;



    BaseIndicatorEnum(String indicatorCode, Integer logisticsUnitType, String desc, IndicatorGroupType indicatorGroupType) {
        this.indicatorCode = indicatorCode;
        this.logisticsUnitType = logisticsUnitType;
        this.desc = desc;
        this.indicatorGroupType = indicatorGroupType;
    }

    public String getIndicatorCode() {
        return indicatorCode;
    }

    public Integer getLogisticsUnitType() {
        return logisticsUnitType;
    }

    public String getDesc() {
        return desc;
    }

    public IndicatorGroupType getIndicatorGroupType() {
        return indicatorGroupType;
    }

    public boolean isGroupBySingleIndicator() {
        return Objects.equals(this.getIndicatorGroupType(), IndicatorGroupType.BY_SINGLE_INDICATOR);
    }
}
