package com.sankuai.shangou.logistics.delivery.xm.enums;

import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 回调类型枚举
 * @date 2025-05-20
 */
@Getter
public enum CallBackEnum {
    CARD_CALLBACK("CARD_CALLBACK", "卡片回调"),
    EVENT_CALLBACK("EVENT_CALLBACK", "事件回调"),
    ;

    private final String type;
    private final String desc;

    CallBackEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static CallBackEnum ofType(String type) {
        return Stream.of(values())
                .filter(callBackEnum -> callBackEnum.getType().equals(type))
                .findFirst().orElse(null);
    }
}
