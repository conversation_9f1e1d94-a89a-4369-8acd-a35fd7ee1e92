package com.sankuai.shangou.logistics.delivery.common.utils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @description
 * @date 2025-05-15
 */
public class TimeUtils {
    public static final DateTimeFormatter DEFAULT_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_TIME_FORMATTER_DOT = DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm");


    private TimeUtils() {
    }


    public static Long toMilliSeconds(LocalDateTime time) {
        if (time == null) {
            return null;
        }
        return time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}
