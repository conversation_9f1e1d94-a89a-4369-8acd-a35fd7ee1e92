package com.sankuai.shangou.logistics.delivery.config;

import com.dianping.squirrel.client.impl.redis.RedisClientBuilder;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.zebra.zebraproxy.jdbc.ZebraDataSource;
import com.sankuai.meituan.poros.client.PorosHighLevelClientBuilder;
import com.sankuai.meituan.poros.client.PorosRestHighLevelClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * <AUTHOR>
 * @date 2023-06-30
 * @email <EMAIL>
 */
@Configuration
public class StorageBeanConfig {
    @Value("${app.name}")
    private String appKey;
    @Value("${es.accessKey}")
    private String eagleAccessKey;
    @Value("${es.clusterGroupName}")
    private String eagleClusterName;
    // 配送单ES
    @Value("${es.deliveryOrderAccessKey}")
    private String deliveryOrderAccessKey;
    @Value("${es.deliveryOrderClusterName}")
    private String deliveryOrderClusterName;

    @Value("${squirrel.clusterName}")
    private String squirrelClusterName;

    @Value("${squirrel.ofcClusterName}")
    private String squirrelOfcClusterName;

    @Value("${squirrel.businessClusterName}")
    private String businessClusterName;

    @Value("${delivery.zebra.dhRuleName}")
    private String dhRuleName;


    @Bean("redisStoreClient")
    public RedisStoreClient redisStoreClientBean() {
        return new RedisClientBuilder(squirrelClusterName)
                .readTimeout(1000)
                .build();
    }

    @Bean("redisStoreOfcClient")
    public RedisStoreClient redisStoreOfcClientBean() {
        return new RedisClientBuilder(squirrelOfcClusterName)
                .readTimeout(1000)
                .build();
    }

    @Bean("esClientRepository")
    public PorosRestHighLevelClient porosRestHighLevelClient() {
        return PorosHighLevelClientBuilder.builder()
                .clusterName(eagleClusterName)
                .appKey(appKey)
                .accessKey(eagleAccessKey)
                .httpIOThreadCount(20)
                .callPorosAsync(false)
                .callESDirectly(true)
                .timeoutMillis(30000)
                .build();
    }

    /**
     * es客户端
     */
    @Bean("esDeliveryOrderClient")
    public PorosRestHighLevelClient getRestHighLevelClient() {
        return PorosHighLevelClientBuilder.builder()
                .appKey(appKey)
                .clusterName(deliveryOrderClusterName)
                .accessKey(deliveryOrderAccessKey)
                .httpIOThreadCount(20)
                .callPorosAsync(false)
                .callESDirectly(true)
                .timeoutMillis(30000)
                .build();
    }

    @Bean("sdmsTransactionTemplate")
    public TransactionTemplate getSdmsTransactionTemplate(@Qualifier("sdmsTransactionManager") DataSourceTransactionManager dataSourceTransactionManager){
        return new TransactionTemplate(dataSourceTransactionManager);
    }

    @Bean("businessRedisStoreClient")
    public RedisStoreClient businessRedisStoreClientBean() {
        return new RedisClientBuilder(businessClusterName)
                .readTimeout(1000)
                .build();
    }

//    @Bean(name = "zebraDataSource", initMethod = "init", destroyMethod = "close")
//    public ZebraDataSource zebraDataSource() {
//        ZebraDataSource zebraDataSource = new ZebraDataSource();
//        zebraDataSource.setZebraKey(dhRuleName);
//        return zebraDataSource;
//    }

}
