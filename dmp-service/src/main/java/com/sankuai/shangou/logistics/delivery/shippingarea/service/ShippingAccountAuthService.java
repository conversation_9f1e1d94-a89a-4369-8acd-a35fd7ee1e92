package com.sankuai.shangou.logistics.delivery.shippingarea.service;

import com.sankuai.shangou.logistics.delivery.shippingarea.utils.MccUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.AccountDataPermissionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ShippingAccountAuthService {

    public AccountDataPermissionVO getDataPermission(String accountName) {
        AccountDataPermissionVO vo = new AccountDataPermissionVO();
        if (MccUtils.getRegionSelectAccountWhiteList().contains(accountName)) {
            vo.setIndicatorPermission(1);
        }
        return vo;
    }
}
