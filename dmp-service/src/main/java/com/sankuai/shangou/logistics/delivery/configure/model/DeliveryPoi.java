package com.sankuai.shangou.logistics.delivery.configure.model;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryIsShowItemNumberEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.Address;
import com.sankuai.shangou.logistics.delivery.configure.value.BookingPushDownTimeConfig;
import com.sankuai.shangou.logistics.delivery.configure.value.DeliveryLaunchPoint;
import com.sankuai.shangou.logistics.delivery.configure.value.SecondDeliveryConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/24
 */
@Getter
@ToString
@AllArgsConstructor
public class DeliveryPoi {

	@Setter
	private Long id;

	/**
	 * 赋能租户id
	 */
	private final Long tenantId;

	/**
	 * 赋能门店id
	 */
	private final Long storeId;

	/**
	 * 城市码,美团物理城市code
	 */
	@Setter
	private Integer cityCode;

	/**
	 * 门店联系方式
	 */
	@Setter
	private String contactPhone;

	/**
	 * 配送平台
	 */
	@Setter
	private DeliveryPlatformEnum deliveryPlatform;

	/**
	 * 配送发起时间点
	 */
	@Setter
	private DeliveryLaunchPoint deliveryLaunchPoint;

	/**
	 * 配送发起类型
	 * 自动发起/手动发起
	 */
	@Setter
	private DeliveryLaunchTypeEnum deliveryLaunchType;

	/**
	 * 渠道类型
	 */
	@Setter
	private Integer channelType;

	@Setter
	private DeliveryPlatformEnum lastDeliveryPlatform;

	@Setter
	private DeliveryIsShowItemNumberEnum deliveryIsShowItemNumberEnum;

	@Setter
	private Address storeAddress;

	@Setter
	private BookingPushDownTimeConfig selfAssessDeliveryConfig;

	@Setter
	private SecondDeliveryConfig secondDeliveryPlatform;

}
