package com.sankuai.shangou.logistics.delivery.xm.enums;

import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 卡片操作枚举
 * @date 2025-05-16
 */
@Getter
public enum CardOperationEnum {
    EXECUTE("EXECUTE", "执行"),
    REJECT("REJECT", "拒绝"),
    ;

    private final String type;
    private final String desc;

    CardOperationEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static CardOperationEnum ofType(String type) {
        return Stream.of(values())
                .filter(typeEnum -> typeEnum.getType().equals(type))
                .findFirst().orElse(null);
    }
}
