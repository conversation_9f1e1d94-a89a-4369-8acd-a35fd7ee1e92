package com.sankuai.shangou.logistics.delivery.configure.pojo.model;

import com.sankuai.shangou.logistics.delivery.configure.enums.DeliveryConfigTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 配置模板配置明细
 * @date 2025-07-02
 */
@Data
public class ConfigTemplateItemModel {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 配置模版id
     */
    private Long configTemplateId;

    /**
     * 模板类型
     */
    private DeliveryConfigTypeEnum templateType;

    /**
     * 配置信息
     */
    private Map<String, Object> configContent;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 创建人(牵牛花账号)
     */
    private String createBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
