package com.sankuai.shangou.logistics.delivery.realtimeboard.wrapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.DeliveryChannelThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.request.DeliveryChannelBatchQueryByCarrierCodeRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.response.DeliveryChannelBatchQueryResponse;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DeliveryClient {
    @Resource
    private DeliveryChannelThriftService deliveryChannelThriftService;

    @MethodLog
    public Map<Integer,String> batchQueryDeliveryNameByCarrierCodes(Set<Integer> codes) {
        try {
            DeliveryChannelBatchQueryByCarrierCodeRequest req = new DeliveryChannelBatchQueryByCarrierCodeRequest();
            req.setCarrierCodeSet(codes);
            DeliveryChannelBatchQueryResponse result = deliveryChannelThriftService.batchQueryDeliveryChannelByCarrierCodeList(req);
            if(result.getStatus().getCode() != Status.SUCCESS.code) {
                throw new ThirdPartyException("call deliveryChannelThriftService.batchQueryDeliveryChannelByCarrierCodeList failed");
            }
            return result.getDeliveryChannelDtoList().stream().collect(Collectors.toMap(DeliveryChannelDto::getCarrierCode,DeliveryChannelDto::getCarrierName));
        } catch (Exception e) {
            log.error("call deliveryChannelThriftService.batchQueryDeliveryChannelByCarrierCodeList failed",e);
            throw new BizException("查询承运商信息失败");
        }
    }
}
