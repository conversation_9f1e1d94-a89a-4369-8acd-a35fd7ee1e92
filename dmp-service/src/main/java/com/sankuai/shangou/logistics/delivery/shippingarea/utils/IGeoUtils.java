package com.sankuai.shangou.logistics.delivery.shippingarea.utils;

import com.google.common.base.CharMatcher;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Assert;
import com.meituan.linz.boot.util.Fun;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.geom.util.GeometryCombiner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/8/21 11:19
 **/
@Slf4j
public class IGeoUtils {

    private static final GeometryFactory geometryFactory = new GeometryFactory();

    public static final double MAX_LATITUDE = 90.0;
    public static final double MAX_LONGITUDE = 180.0;

    /**
     * 坐标之间的分隔符
     */
    public static final char COORDINATE_SPLITTER = ';';

    /**
     * 经纬度分隔符
     */
    public static final char LNG_LAT_SPLITTER = ',';

    private static final double EARTH_RADIUS = 6371000; // 地球半径，单位：米

    public static Polygon convertDeliveryArea(List<com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.Coordinate> coordinateList) {
        return IGeoUtils.createPolygon(Fun.map(coordinateList, coordinate -> IGeoUtils.createCoordinate(coordinate.getLongitude(), coordinate.getLatitude())));
    }


    /**
     * 创建平面几何区域
     *
     * @param pointList 顶点列表
     * @return 几何区域
     */
    public static Polygon createPolygon(List<Coordinate> pointList) {
        checkLinearRing(pointList);
        Coordinate start = pointList.get(0);
        Coordinate end = pointList.get(pointList.size() - 1);
        List<Coordinate> tempCoordinates = new ArrayList<>(pointList);
        if (!start.equals2D(end)) {
            tempCoordinates.add(new Coordinate(start));
        }
        Coordinate[] coordinates = tempCoordinates.toArray(new Coordinate[0]);
        return geometryFactory.createPolygon(coordinates);
    }

    public static void checkLinearRing(List<Coordinate> pointList) {
        Assert.throwIfEmpty(pointList, "顶点列表不得为空");
        Coordinate start = pointList.get(0);
        if (start.equals(pointList.get(pointList.size() - 1))) {
            // 首尾同一个点时
            Assert.throwIfTrue(pointList.size() < 4, "首尾点相同时, 至少4个顶点才能组成区域 input:{}", pointList);
        } else {
            Assert.throwIfTrue(pointList.size() < 3, "至少3个顶点才能组成区域 input:{}", pointList);
        }
    }

    /**
     * 创建几何坐标
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 几何坐标
     */
    public static Coordinate createCoordinate(double longitude, double latitude) {
        checkCoordinate(longitude, latitude);
        return new Coordinate(longitude, latitude);
    }

    /**
     * 将一个区域的顶点列表转化成一个String
     *
     * @param region 区域
     * @return 顶点列表: 123.2333123123,87.233232355;123.2333123123,87.233232355;123.2333123123,87.233232355
     */
    public static String polygonToString(Polygon region) {
        if (region == null) {
            return null;
        }
        List<String> coordinates = Fun.map(Arrays.asList(region.getCoordinates()), IGeoUtils::coordinateToLngLatString);
        return Joiner.on(COORDINATE_SPLITTER).join(coordinates);
    }

    /**
     * 将坐标格式化成
     *
     * @param coordinate 坐标
     * @return 坐标点Str: 123.2333123123,87.233232355
     */
    public static String coordinateToLngLatString(Coordinate coordinate) {
        if (coordinate == null) {
            return null;
        }
        return coordinate.x + String.valueOf(LNG_LAT_SPLITTER) + coordinate.y;
    }

    public static List<com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.Coordinate> createCoordinateDtoList(String region) {
        List<List<Double>> pointList = splitBound(region);
        return Fun.map(pointList, point -> IGeoUtils.createCoordinateDto(point.get(0), point.get(1)));
    }


    public static com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.Coordinate createCoordinateDto(double longitude, double latitude) {
        checkCoordinate(longitude, latitude);
        return new com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.Coordinate(longitude,
                latitude);
    }

    /**
     * 分隔边界
     *
     * @param boundStr str分隔边界
     * @return
     */
    private static List<List<Double>> splitBound(String boundStr) {
        List<String> stringValues = Lists.newArrayList(Splitter.on(
                CharMatcher.forPredicate(c -> LNG_LAT_SPLITTER == c || COORDINATE_SPLITTER == c)
        ).split(boundStr));
        List<Double> values = Fun.map(stringValues, Double::valueOf);
        Assert.throwIfTrue(values.size() % 2 != 0, "边界数据问题 boundStr:{}", boundStr);
        return Lists.partition(values, 2);
    }

    /**
     * 经纬度检查
     *
     * @param longitude 经度
     * @param latitude  纬度
     */
    public static void checkCoordinate(double longitude, double latitude) {
        Assert.throwIfTrue(Math.abs(longitude) > MAX_LONGITUDE, "经度取值范围是 -180° ~ 180°, input: {}",
                longitude);
        Assert.throwIfTrue(Math.abs(latitude) > MAX_LATITUDE, "纬度取值范围是 -90° ~ 90°, input: {}", latitude);
    }

    public static Optional<Polygon> combineDeliveryArea(List<Polygon> polygonList) {
        if (CollectionUtils.isEmpty(polygonList)) {
            return Optional.empty();
        }
        if (polygonList.size() == 1) {
            return Optional.of(polygonList.get(0));
        }

        // 如果围栏经纬度完全一样则跳过合并
        if (IStringUtils.compareSelf(Fun.map(polygonList, Polygon::toText))) {
            return Optional.of(polygonList.get(0));
        }

        Geometry geometry = GeometryCombiner.combine(polygonList).union();
        if (geometry instanceof MultiPolygon) {
            log.warn("combine fail: contains multi polygon");
            return Optional.empty();
        }

        if (!(geometry instanceof Polygon)) {
            log.warn("combine fail: error geometry type {}", geometry.getClass());
            return Optional.empty();
        }

        Polygon combinedPolygon = (Polygon) geometry;
        if (combinedPolygon.getNumInteriorRing() > 0) {
            // a polygon must have at least one exterior ring. (multiple exterior rings are MultiPolygon)
            // Interior rings (aka "holes") can only exist within a parent exterior ring.
            // 约定返回第一块区域
            log.warn("combine polygon contains holes");
            return polygonList.stream().findFirst();
        }

        return Optional.of(combinedPolygon);
    }

    /**
     * 校验经纬度串
     *
     * @param coordinateStr
     * @return
     */
    public static void validateCoordinate(String coordinateStr) {
        Assert.throwIfBlank(coordinateStr, "坐标字符串不得为空");
        List<String> valueList = Splitter.on(LNG_LAT_SPLITTER).splitToList(coordinateStr);
        Assert.throwIfTrue(valueList.size() != 2 || !NumberUtils.isCreatable(valueList.get(0)) ||
                !NumberUtils.isCreatable(valueList.get(1)), "非法的坐标字符串");
        checkCoordinate(Double.parseDouble(valueList.get(0)), Double.parseDouble(valueList.get(1)));
    }

    /**
     * 根据经纬度点集合算区域面积 单位:平方公里
     */
    public static double calArea(String region) {
        List<com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.Coordinate> coordinateDtoList = createCoordinateDtoList(region);
        double area = 0.0;
        if (coordinateDtoList.size() > 2) {
            for (int i = 0; i < coordinateDtoList.size() - 1; i++) {
                com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.Coordinate p1 = coordinateDtoList.get(i);
                com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.Coordinate p2 = coordinateDtoList.get(i + 1);
                area += rad(p2.longitude - p1.longitude) * (2 + Math.sin(rad(p1.latitude)) + Math.sin(rad(p2.latitude)));
            }
            area = area * EARTH_RADIUS * EARTH_RADIUS / 2.0;
        }
        return Math.abs(area) / 1000000.0;
    }

    private static double rad(double degree) {
        return degree * Math.PI / 180.0;
    }
}
