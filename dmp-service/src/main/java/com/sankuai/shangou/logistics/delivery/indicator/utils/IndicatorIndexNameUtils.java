package com.sankuai.shangou.logistics.delivery.indicator.utils;

import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2023/11/3 17:14
 **/
@Slf4j
public class IndicatorIndexNameUtils {

    private static final String UNDER_LINE = "_";
    public static String getIndexName(LocalDateTime bizTime) {
        String indexName = MccUtils.getIndicatorIndexName();
        if (StringUtils.isEmpty(indexName)) {
            throw new BizException("无法获取指标索引名称");
        }

        StringBuilder sb = new StringBuilder();
        sb.append(indexName);
        sb.append(UNDER_LINE);
        sb.append(bizTime.format(DateTimeFormatter.ofPattern("yyyy_MM")));
        log.info("索引名称,{}", sb);
        return sb.toString();
    }
}
