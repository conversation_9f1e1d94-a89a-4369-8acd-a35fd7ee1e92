package com.sankuai.shangou.logistics.delivery.shippingarea.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.meituan.linz.boot.util.Assert;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.shangou.logistics.delivery.shippingarea.domain.ShippingAreaUpdateMessage;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.ChannelPoiShippingAreaOperationEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.service.ShippingAreaRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/6/12 15:32
 **/
@Component
@Slf4j
public class ShippingAreaUpdateMessageConsumer {
    @Resource
    private ShippingAreaRelationService shippingAreaRelationService;

    @MafkaConsumer(namespace = "waimai", topic = "shipping_area_update_message", group = "shipping_area_relation_consumer")
    public ConsumeStatus onRecvMessage(String msgBody) {
        log.info("开始消费配送范围变更消息: {}", msgBody);
        ShippingAreaUpdateMessage message = translate(msgBody);

        if (!Objects.equals(message.getOperationType(), ChannelPoiShippingAreaOperationEnum.RESET.getCode())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        shippingAreaRelationService.resetShippingAreaRelations(message.getTenantId(), message.getStoreId());

        return ConsumeStatus.CONSUME_SUCCESS;
    }


    private ShippingAreaUpdateMessage translate(String msgBody) {
        ShippingAreaUpdateMessage message = JSON.parseObject(msgBody, ShippingAreaUpdateMessage.class);
        Assert.throwIfNull(message, "消息为空");
        Assert.throwIfNull(message.getTenantId(), "租户id为空");
        Assert.throwIfNull(message.getStoreId(), "门店id为空");

        return message;
    }

}
