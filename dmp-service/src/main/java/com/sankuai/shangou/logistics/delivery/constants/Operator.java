package com.sankuai.shangou.logistics.delivery.constants;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-08-29
 * @email <EMAIL>
 */
@Getter
@ToString
@EqualsAndHashCode
public class Operator {
    public final static Operator system = new Operator("System", 0L, LocalDateTime.now());

    private String operatorName;

    private Long operatorId;

    private LocalDateTime operateTime;

    public Operator(String operatorName, Long operatorId, LocalDateTime operateTime) {
        this.operatorName = operatorName;
        this.operatorId = operatorId;
        this.operateTime = operateTime;
    }
}
