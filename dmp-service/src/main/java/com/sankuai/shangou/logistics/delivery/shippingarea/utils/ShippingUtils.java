package com.sankuai.shangou.logistics.delivery.shippingarea.utils;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ShippingTagEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/7
 */
public class ShippingUtils {
    private static final String SHIPPING_TAG_SPLIT_CHAR = "-";

    public static List<ShippingTagEnum> parseShippingTag(String appShippingCode) {
        if (StringUtils.isBlank(appShippingCode)) {
            return Lists.emptyList();
        }

        List<ShippingTagEnum> tags = new ArrayList<>();
        for (ShippingTagEnum shippingTagEnum : ShippingTagEnum.values()) {
            if (appShippingCode.contains(shippingTagEnum.getShippingCodePrefix())) {
                tags.add(shippingTagEnum);
            }
        }
        return tags;
    }

    public static ShippingTagEnum codeOfShippingTag(int code) {
        for (ShippingTagEnum shippingTagEnum : ShippingTagEnum.values()) {
            if (shippingTagEnum.getCode() == code) {
                return shippingTagEnum;
            }
        }
        throw new IllegalArgumentException("无效的配送范围标签: " + code);
    }

    public static String createAppShippingCode(List<Integer> shippingTags) {
        String appShippingCode = IStringUtils.getCurrentMillisString();
        StringBuilder stringBuilder = new StringBuilder();

        if (CollectionUtils.isNotEmpty(shippingTags)) {
            for (Integer shippingTag : shippingTags) {
                stringBuilder.append(ShippingUtils.codeOfShippingTag(shippingTag).getShippingCodePrefix());
            }

            stringBuilder.append(SHIPPING_TAG_SPLIT_CHAR);
        }

        stringBuilder.append(appShippingCode);
        return stringBuilder.toString();
    }
}
