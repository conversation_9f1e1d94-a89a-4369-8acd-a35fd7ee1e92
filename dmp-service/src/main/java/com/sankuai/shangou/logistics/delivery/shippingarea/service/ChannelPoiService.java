package com.sankuai.shangou.logistics.delivery.shippingarea.service;

import com.dianping.lion.common.util.JsonUtils;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.request.PoiInfoListQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiDetailInfoResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiInfoListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/15 17:23
 **/
@Service
@Slf4j
public class ChannelPoiService {
    @Resource
    private ChannelPoiManageThriftService channelPoiManageThriftService;

    public List<PoiDetailInfoDTO> queryPoiInfoListUnderCity(Long tenantId, Integer cityId) {
        if (Objects.isNull(tenantId) || Objects.isNull(cityId)) {
            throw new IllegalArgumentException("tenantId or cityId is null");
        }
        PoiInfoListQueryRequest request = new PoiInfoListQueryRequest();
        request.setCityId(cityId);
        request.setTenantId(tenantId);

        PoiInfoListResponse response = channelPoiManageThriftService.queryPoiInfoListUnderCond(request);
        log.info("invoke channelPoiManageThriftService.queryPoiInfoListUnderCond, request= {}, response = {}",
                JsonUtils.toJson(request), JsonUtils.toJson(response));

        Bssert.throwIfNull(response.getStatus(), "未能获取到接口状态");
        Bssert.throwIfNull(response.getStatus().getCode(), "未能获取到接口状态");
        Bssert.throwIfTrue(response.getStatus().getCode() != 0,
                StringUtils.defaultIfBlank(response.getStatus().getMessage(), "接口返回错误"));

        return response.getPoiInfoList().stream().filter(poiInfo -> Objects.equals(poiInfo.getPoiStatus(), PoiStatusEnum.ONLINE.getKey()))
                .collect(Collectors.toList());
    }

    public PoiDetailInfoDTO queryPoiInfoByPoiId(Long tenantId, Long poiId) {
        PoiDetailInfoResponse response =
                channelPoiManageThriftService.queryPoiDetailInfoByPoiId(tenantId, poiId);
        log.info("invoke channelPoiManageThriftService.queryPoiDetailInfoByPoiId, request= {}, response = {}", poiId,
                JsonUtils.toJson(response));

        Bssert.throwIfNull(response.getStatus(), "未能获取到接口状态");
        Bssert.throwIfNull(response.getStatus().getCode(), "未能获取到接口状态");
        Bssert.throwIfTrue(response.getStatus().getCode() != 0,
                StringUtils.defaultIfBlank(response.getStatus().getMessage(), "接口返回错误"));

        return response.getPoiDetailInfoDTO();
    }
}
