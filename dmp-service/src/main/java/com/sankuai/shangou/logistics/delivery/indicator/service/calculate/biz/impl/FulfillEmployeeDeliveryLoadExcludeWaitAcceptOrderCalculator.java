package com.sankuai.shangou.logistics.delivery.indicator.service.calculate.biz.impl;

import com.sankuai.shangou.logistics.delivery.indicator.dto.BaseIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BizIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorDTO;
import com.sankuai.shangou.logistics.delivery.indicator.service.calculate.biz.AbstractBizIndicatorCalculator;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/7/24 21:08
 **/
@Slf4j
@Component
public class FulfillEmployeeDeliveryLoadExcludeWaitAcceptOrderCalculator extends AbstractBizIndicatorCalculator {
    @Override
    public List<BaseIndicatorEnum> getRelateBaseIndicators() {
        return Arrays.asList(BaseIndicatorEnum.WAIT_PICK_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT,
                BaseIndicatorEnum.WAIT_PICK_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT,
                BaseIndicatorEnum.DELIVERED_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT,
                BaseIndicatorEnum.DELIVERED_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT,
                BaseIndicatorEnum.FULFILLING_EMPLOYEE_COUNT);
    }

    @Override
    public BizIndicatorEnum getSupportIndicator() {
        return BizIndicatorEnum.FULFILL_EMPLOYEE_DELIVERY_LOAD_EXCLUDE_WAIT_ACCEPT_ORDER;
    }

    @Override
    public List<IndicatorDTO> calc(Map<String, List<IndicatorDTO>> baseIndicatorMap) {
        //如果基础指标数据不合法 直接返回空
        if (!checkBaseIndicatorDataIsValid(baseIndicatorMap)) {
            log.error("基础指标数据不全或不合法, baseIndicatorMap: {}, bizIndicatorEnum: {}", baseIndicatorMap, getSupportIndicator());
            return Collections.emptyList();
        }

        BigDecimal waitPickByScheduledOrderCnt = baseIndicatorMap.get(BaseIndicatorEnum.WAIT_PICK_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT.getIndicatorCode()).get(0).getValue();
        BigDecimal waitPickByUnscheduledOrderCnt = baseIndicatorMap.get(BaseIndicatorEnum.WAIT_PICK_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT.getIndicatorCode()).get(0).getValue();
        BigDecimal deliveredByScheduledOrderCnt = baseIndicatorMap.get(BaseIndicatorEnum.DELIVERED_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT.getIndicatorCode()).get(0).getValue();
        BigDecimal deliveryByUnscheduledOrderCnt = baseIndicatorMap.get(BaseIndicatorEnum.DELIVERED_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT.getIndicatorCode()).get(0).getValue();
        BigDecimal fulfillingEmployeeCount = baseIndicatorMap.get(BaseIndicatorEnum.FULFILLING_EMPLOYEE_COUNT.getIndicatorCode()).get(0).getValue();

        BigDecimal orderCount = waitPickByScheduledOrderCnt.add(waitPickByUnscheduledOrderCnt).add(deliveredByScheduledOrderCnt)
                .add(deliveryByUnscheduledOrderCnt);

        //分子不为0 分母为0
        if (!BigDecimal.ZERO.equals(orderCount) && BigDecimal.ZERO.equals(fulfillingEmployeeCount)) {
            log.warn("fulfillingEmployeeCount is zero");
            return Collections.emptyList();
        }

        //分子分母同时为0
        if (BigDecimal.ZERO.equals(orderCount) && BigDecimal.ZERO.equals(fulfillingEmployeeCount)) {
            IndicatorDTO bizIndicator = buildWithBaseIndicatorAndBizValue(baseIndicatorMap.get(BaseIndicatorEnum.WAIT_PICK_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT.getIndicatorCode()).get(0), BigDecimal.ZERO);
            return Collections.singletonList(bizIndicator);
        }

        BigDecimal bizValue = orderCount.divide(fulfillingEmployeeCount, 2, RoundingMode.HALF_UP);
        IndicatorDTO bizIndicator = buildWithBaseIndicatorAndBizValue(baseIndicatorMap.get(BaseIndicatorEnum.WAIT_PICK_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT.getIndicatorCode()).get(0), bizValue);
        return Collections.singletonList(bizIndicator);


    }
}
