package com.sankuai.shangou.logistics.delivery.realtimeboard.wrapper;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class TenantPoiClient {
    @Resource
    private PoiThriftService poiThriftService;

    @MethodLog(logRequest = true, logResponse = true)
    public Map<Long, PoiInfoDto> batchQueryPoiByIds(Long tenantId, List<Long> storeIds) {
        try {
            Map<Long, PoiInfoDto> storeDetailMap = new HashMap<>(storeIds.size());
            Lists.partition(storeIds, 100).forEach(repoIds -> {
                storeDetailMap.putAll(this.queryPoiByIds(tenantId, repoIds));
            });
            return storeDetailMap;
        } catch (Exception e) {
            log.error("tenantPoiClient.queryPoiByIds error, msg:{}", e.getMessage(), e);
            throw new ThirdPartyException("批量获取门店信息失败", e);
        }
    }

    @MethodLog(logRequest = true, logResponse = true)
    private Map<Long, PoiInfoDto> queryPoiByIds(Long tenantId, List<Long> poiIds) {
        if (CollectionUtils.isEmpty(poiIds)) {
            return Maps.newHashMap();
        }

        PoiMapResponse response = poiThriftService.queryTenantPoiInfoMapByPoiIds(poiIds, tenantId);
        Bssert.throwIfNull(response, "未能获取到接口返回");
        Bssert.throwIfNull(response.getStatus(), "未能获取到接口状态");
        Bssert.throwIfNull(response.getStatus().getCode(), "未能获取到接口状态");
        Bssert.throwIfTrue(response.getStatus().getCode() != 0,
                StringUtils.defaultIfBlank(response.getStatus().getMessage(), "接口返回错误"));

        return Optional.ofNullable(response.getPoiInfoMap()).orElse(Collections.emptyMap());
    }
}
