package com.sankuai.shangou.logistics.delivery.shippingarea.service;


import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.LoiIndicatorThriftService;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.indicator.HeatmapPointDto;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.indicator.OutOfScopePointDto;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.request.*;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.response.*;
import com.sankuai.shangou.commons.utils.rpc.RpcInvokeUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.DateTimeUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.MccUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.*;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.request.CityOutOfScopePointsQueryParam;
import com.sankuai.shangou.logistics.delivery.shippingarea.convert.IndicatorConverter;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.request.LoiMapAoiClassifyQueryParam;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.request.LoiOrderUserIndicatorQueryParam;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.request.PeoplePortraitQueryParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/27 20:06
 **/
@Slf4j
@Service
public class MapIndicatorService {

    @Resource
    private LoiIndicatorThriftService loiIndicatorThriftService;

    @Resource
    private IndicatorConverter indicatorConverter;

    @Resource
    private ShippingAreaService shippingAreaService;

    public List<OutOfScopePointDto> queryOutOfScopePoints(CityOutOfScopePointsQueryParam queryParam) {
        CityOutOfScopePointsQueryRequest request = new CityOutOfScopePointsQueryRequest();
        request.setCityId(queryParam.getCityId());
        request.setTenantCode(MccUtils.getRegionSelectDrunkHorseTenantId());
        request.setDateType(queryParam.getDateType());
        request.setDt(queryParam.getDt());

        log.info("invoke loiIndicatorThriftService.queryOutOfScopePointsInCity, request:{}", request);
        CityOutOfScopePointsResp response = RpcInvokeUtils.rpcInvokeTemplate(() -> {
                    try {
                        return loiIndicatorThriftService.queryOutOfScopePointsInCity(request);
                    } catch (TException e) {
                        throw new RuntimeException(e);
                    }
                },
                "查询超配数据失败",
                resp -> resp.getStatus().getCode(),
                resp -> resp.getStatus().getMsg());
        return response.getOutOfScopePointDtoList();

    }

    public List<HeatmapPointDto> queryCityHeatmap(QueryCityHeatmapRequest request) {
        CityHeatmapPointListResp response = RpcInvokeUtils.rpcInvokeTemplate(
                () -> {
                    try {
                        return loiIndicatorThriftService.queryCityHeatmapByCityId(request);
                    } catch (TException e) {
                        throw new RuntimeException(e);
                    }
                },
                "查询订单热力图失败",
                resp -> resp.getStatus().getCode(),
                resp -> resp.getStatus().getMsg());
        return response.getHeatPoints();
    }

    public PeoplePortraitVO queryPeoplePortrait(PeoplePortraitQueryParam queryParam, Long tenantId) {
        BoundaryPeoplePortraitQueryRequest request = new BoundaryPeoplePortraitQueryRequest();
        request.setTenantId(tenantId);
        request.setBoundary(queryParam.getRegion());
        request.setDt(queryParam.getDt());
        request.setCityId(queryParam.getCityId());

        PeoplePortraitListResp response = RpcInvokeUtils.rpcInvokeTemplate(
                () -> {
                    try {
                        return loiIndicatorThriftService.queryPeoplePortraitWithBoundary(request);
                    } catch (TException e) {
                        throw new RuntimeException(e);
                    }
                },
                "查询人口画像失败",
                resp -> resp.getStatus().getCode(),
                resp -> resp.getStatus().getMsg());

        if (CollectionUtils.isEmpty(response.getPeoplePortraitList())) {
                return new PeoplePortraitVO();
        }
        return indicatorConverter.toPeoplePortraitVO(response.getPeoplePortraitList().get(0), request.getDt());

    }

    public LoiMapAoiClassifyVO queryLoiMapAoiClassify(LoiMapAoiClassifyQueryParam queryParam, Long tenantId) {
        BoundaryAoiClassifyQueryRequest request = new BoundaryAoiClassifyQueryRequest();
        request.setTenantId(tenantId);
        request.setBoundary(queryParam.getRegion());
        String dt = DateTimeUtils.fmtMinusDayIsoDt(2);
        if (StringUtils.isNotBlank(queryParam.getDt())) {
            dt = queryParam.getDt();
        }
        request.setDt(dt);
        request.setCityId(queryParam.getCityId());

        LoiMapAoiClassifyResp response = RpcInvokeUtils.rpcInvokeTemplate(() -> {
                    try {
                        return loiIndicatorThriftService.queryMapAoiClassifyWithBoundary(request);
                    } catch (TException e) {
                        throw new RuntimeException(e);
                    }
                },
                "查询loi分类失败", resp -> resp.getStatus().getCode(), resp -> resp.getStatus().getMsg());

        //return indicatorConverter.toLoiMapAoiClassifyVO(response.getMapAoiClassifyDtoList(), queryParam.getLoiId(), dt);
        return indicatorConverter.toLoiMapAoiClassifyVO(response.getMapAoiClassifyDtoList(), null, dt);

    }


    public ShippingStoreOrderUserIndicatorVO queryLoiOrderUserIndicator(LoiOrderUserIndicatorQueryParam queryParam, Long tenantId) {
        DhStoreOrderUserIndicatorQueryRequest request = new DhStoreOrderUserIndicatorQueryRequest();
        request.setTenantId(tenantId);

        request.setBoundary(queryParam.getRegion());
        request.setDt(queryParam.getDt());
        request.setDateType(queryParam.getDateType());
        request.setCityId(queryParam.getCityId());
        request.setPoiId(queryParam.getStoreId());

        LoiOrderUserIndicatorResp response = RpcInvokeUtils.rpcInvokeTemplate(
                () -> loiIndicatorThriftService.queryDhStoreOrderUserIndicator(request),
                "查询loi分类失败",
                resp -> resp.getStatus().getCode(),
                resp -> resp.getStatus().getMsg());

        PoiChannelDeliveryInfoVO poiChannelDeliveryInfo = shippingAreaService.getPoiChannelDeliveryInfo(request.getPoiId(), tenantId);

        return indicatorConverter.toShippingStoreOrderUserIndicatorVO(indicatorConverter.toOrderUserIndicatorVO(response.getOrderUserIndicator(), null), poiChannelDeliveryInfo);
    }
}
