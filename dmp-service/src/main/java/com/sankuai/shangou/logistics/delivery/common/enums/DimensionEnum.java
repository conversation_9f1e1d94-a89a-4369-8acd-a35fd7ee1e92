package com.sankuai.shangou.logistics.delivery.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-30
 */
public enum DimensionEnum {
    TENANT(1, "租户维度"),

    OPERATION_MODE(2, "经营维度"),

    POI(3, "门店维度");

    private final int code;
    private final String desc;

    DimensionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    @JsonCreator
    public static DimensionEnum enumOf(Integer code) {
        return Arrays.stream(DimensionEnum.values())
                .filter(dimensionEnum -> Objects.equals(dimensionEnum.getCode(), code))
                .findFirst().orElse(null);
    }

    public static final List<DimensionEnum> TEMPLATE_ENUMS = Lists.newArrayList(TENANT, OPERATION_MODE);

    public static boolean isTemplateEnums(Integer code) {
        return TEMPLATE_ENUMS.contains(enumOf(code));
    }
}
