package com.sankuai.shangou.logistics.delivery.questionnaire;

import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDO;
import com.sankuai.shangou.logistics.delivery.questionnaire.repository.DeliveryQuestionnaireRepository;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/15 16:15
 **/
@Service
public class DeliveryQuestionnaireService {

    @Resource
    private DeliveryQuestionnaireRepository deliveryQuestionnaireRepository;

    public List<DeliveryQuestionnaireDO> queryQuestionnaireByDeliveryOrderIds(List<Long> deliveryOrderIds) {
        return deliveryQuestionnaireRepository.queryQuestionnaireByDeliveryOrderIds(deliveryOrderIds);
    }

    public Optional<DeliveryQuestionnaireDO> queryRiderLatestQuestionnaireRecord(Long riderAccountId) {
        return deliveryQuestionnaireRepository.queryRiderLatestQuestionnaireRecord(riderAccountId);
    }

    public void releaseQuestionnaire(List<DeliveryQuestionnaireDO> deliveryQuestionnaireDOList) {
        deliveryQuestionnaireRepository.batchInsert(deliveryQuestionnaireDOList);
    }

    public void invalidQuestionnaire(Long deliveryOrderId) {
        deliveryQuestionnaireRepository.invalidQuestionnaire(deliveryOrderId);
    }

    public void submitAnswers(Map<String, String> question2AnswerMap, Long deliveryOrderId, Long riderAccountId, Integer scene) {
        List<DeliveryQuestionnaireDO> deliveryQuestionnaireDOList = deliveryQuestionnaireRepository.queryQuestionnaireByDeliveryOrderIds(Collections.singletonList(deliveryOrderId));

        if (CollectionUtils.isEmpty(deliveryQuestionnaireDOList)) {
            throw new BizException("问卷信息不存在");
        }

        if (!Objects.equals(deliveryQuestionnaireDOList.get(0).getRiderAccountId(), riderAccountId)) {
            throw new BizException("问卷已被投放给其他骑手");
        }

        List<DeliveryQuestionnaireDO> newDoList = deliveryQuestionnaireDOList.stream()
                .filter(questionnaire -> question2AnswerMap.containsKey(questionnaire.getQuestion()))
                .map(questionnaire -> {
                    DeliveryQuestionnaireDO newDo = new DeliveryQuestionnaireDO();
                    newDo.setId(questionnaire.getId());
                    newDo.setAnswer(question2AnswerMap.get(questionnaire.getQuestion()));
                    newDo.setFillInTime(LocalDateTime.now());
                    newDo.setUpdateTime(LocalDateTime.now());
                    newDo.setScene(scene);

                    return newDo;
                }).collect(Collectors.toList());

        deliveryQuestionnaireRepository.batchUpdate(newDoList);
    }



}
