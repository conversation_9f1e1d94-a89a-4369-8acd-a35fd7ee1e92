package com.sankuai.shangou.logistics.delivery.shippingarea.vo.request;

import com.meituan.linz.boot.util.Assert;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@TypeDoc(
        description = "批量更新渠道配送范围",
        authors = {
                "daiyuan"
        }
)
@Getter
@Setter
public class BatchUpsertChannelPoiShippingParam {
    @FieldDoc(
            description = "城市id"
    )
    private Integer cityId;

    @FieldDoc(
            description = "门店id(百川门店id)"
    )
    private Long storeId;

    @FieldDoc(
            description = "是否只更新时间段"
    )
    private Boolean onlyUpdatePeriodTime;

    @FieldDoc(
            description = "是否解绑关联关系"
    )
    private Boolean isUnbindRelation;

    @FieldDoc(
            description = "渠道配送信息列表"
    )
    private List<UpsertChannelPoiShippingParam> channelRegionList;

    public void validate() {
        Assert.throwIfTrue(cityId== null || cityId<= 0, "错误的城市id");
        Assert.throwIfTrue(storeId == null || storeId <= 0, "错误的门店id");
        Assert.throwIfTrue(CollectionUtils.isEmpty(channelRegionList), "渠道配送信息列表不能为空");
        channelRegionList.forEach(UpsertChannelPoiShippingParam::validate);
    }

    public boolean checkIsUnbindRelation() {
        return isUnbindRelation != null && isUnbindRelation;
    }
}
