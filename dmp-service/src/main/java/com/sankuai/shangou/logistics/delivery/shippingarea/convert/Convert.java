package com.sankuai.shangou.logistics.delivery.shippingarea.convert;

import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.PoiShippingAreaInfoDTO;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ShippingTagEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;

import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.logistics.delivery.shippingarea.domain.ChannelPoiShippingInfo;
import com.sankuai.shangou.logistics.delivery.shippingarea.request.ResetShippingAreaRequest;
import com.sankuai.shangou.logistics.delivery.shippingarea.dto.ShippingAreaRelationDto;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.ShippingUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.*;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.ChannelPoiShippingAreaOperationEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.ChannelTypeEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.service.ShippingAreaService;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.IGeoUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.request.BatchUpsertChannelPoiShippingParam;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.request.UpsertChannelPoiShippingParam;
import com.sankuai.wmarch.map.thriftClient.route.RouteInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.locationtech.jts.geom.Polygon;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.shangou.logistics.delivery.shippingarea.service.ShippingAreaService.isSpecialPeriodShippingAres;

/**
 * <AUTHOR>
 * @since 2023/8/21 11:55
 **/
@Slf4j
public class Convert {

    private static final String DEFAULT_START_TIME = "00:00";
    private static final String DEFAULT_END_TIME = "24:00";

    public static CityShippingAreaInfoVO buildCityShippingAreaInfoVO(Integer cityId, List<PoiDetailInfoDTO> poiDetailInfoDTOList,
                                                                     List<Long> permissionPoiIdList,
                                                                     Pair<Map<Long, Polygon>, Map<Long, Polygon>> mergedAreasMap) {
        CityShippingAreaInfoVO cityShippingAreaInfoVO = new CityShippingAreaInfoVO();
        cityShippingAreaInfoVO.setCityId(cityId);

        Map<Long, Polygon> storeMergedRegularPeriodShippingAreaMap = mergedAreasMap.getLeft();
        Map<Long, Polygon> storeMergedSpecialPeriodShippingAreaMap = mergedAreasMap.getRight();
        List<PoiShippingVO> poiShippingVOS = poiDetailInfoDTOList.stream().map(poi -> {
            return PoiShippingVO.builder()
                    .storeId(poi.getPoiId())
                    .storeName(poi.getPoiName())
                    .storeOwner(poi.getManager())
                    .coordinate(getChannelPoiLocation(poi))
                    .regularPeriodDeliveryRegion(Optional.ofNullable(storeMergedRegularPeriodShippingAreaMap.get(poi.getPoiId())).map(IGeoUtils::polygonToString).orElse(""))
                    .specialPeriodDeliveryRegion(Optional.ofNullable(storeMergedSpecialPeriodShippingAreaMap.get(poi.getPoiId())).map(IGeoUtils::polygonToString).orElse(""))
                    .editable(permissionPoiIdList.contains(poi.getPoiId()) ? 1 : 0)
                    .build();
        }).collect(Collectors.toList());

        cityShippingAreaInfoVO.setStoreList(poiShippingVOS);

        return cityShippingAreaInfoVO;
    }

    private static CoordinationVO getChannelPoiLocation(PoiDetailInfoDTO poiDetailInfoDTO) {
        if (poiDetailInfoDTO == null) {
            return null;
        }

        List<ChannelPoiInfoDTO> channelPoiInfoList = poiDetailInfoDTO.getChannelPoiInfoList();
        if (CollectionUtils.isEmpty(channelPoiInfoList)) {
            return null;
        }

        Map<Integer, ChannelPoiInfoDTO> channelPoiInfoDTOMap =
                poiDetailInfoDTO.getChannelPoiInfoList().stream().collect(Collectors.toMap(ChannelPoiInfoDTO::getChannelId, Function.identity(), (k1, k2) -> k2));
        //优先取微商城
        if(channelPoiInfoDTOMap.containsKey(ChannelTypeEnum.MT_DRUNK_HORSE.getChannelId())) {
            ChannelPoiInfoDTO channelPoiInfoDTO = channelPoiInfoDTOMap.get(ChannelTypeEnum.MT_DRUNK_HORSE.getChannelId());
            return new CoordinationVO(channelPoiInfoDTO.getLongitude(), channelPoiInfoDTO.getLatitude());
        }

        if(channelPoiInfoDTOMap.containsKey(ChannelTypeEnum.MEITUAN.getChannelId())) {
            ChannelPoiInfoDTO channelPoiInfoDTO = channelPoiInfoDTOMap.get(ChannelTypeEnum.MEITUAN.getChannelId());
            return new CoordinationVO(channelPoiInfoDTO.getLongitude(), channelPoiInfoDTO.getLatitude());
        }

        return null;
    }

    public static PoiChannelDeliveryInfoVO buildPoiChannelDeliveryInfoVO(PoiDetailInfoDTO poiDetailInfoDTO,
                                                                         List<PoiShippingAreaInfoDTO> mtShippingInfoList,
                                                                         List<PoiShippingAreaInfoDTO> dhShippingInfoList,
                                                                         List<ShippingAreaRelationDto> shippingAreaRelationDtos) {
        PoiChannelDeliveryInfoVO poiChannelDeliveryInfoVO = new PoiChannelDeliveryInfoVO();
        poiChannelDeliveryInfoVO.setStoreId(poiDetailInfoDTO.getPoiId());
        poiChannelDeliveryInfoVO.setStoreName(poiDetailInfoDTO.getPoiName());
        poiChannelDeliveryInfoVO.setStoreOwner(poiDetailInfoDTO.getManager());
        poiChannelDeliveryInfoVO.setStoreStatus(poiDetailInfoDTO.getPoiStatus());
        poiChannelDeliveryInfoVO.setCoordinate(getChannelPoiLocation(poiDetailInfoDTO));

        List<PoiShippingAreaInfoDTO> regularPeriodShippingAreas = new ArrayList<>();
        List<PoiShippingAreaInfoDTO> specialPeriodShippingAreas = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mtShippingInfoList)) {
            mtShippingInfoList.forEach(poiShippingInfoDTO -> {
                if (isSpecialPeriodShippingAres(poiShippingInfoDTO)) {
                    specialPeriodShippingAreas.add(poiShippingInfoDTO);
                } else {
                    regularPeriodShippingAreas.add(poiShippingInfoDTO);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(dhShippingInfoList)) {
            dhShippingInfoList.forEach(poiShippingInfoDTO -> {
                if (isSpecialPeriodShippingAres(poiShippingInfoDTO)) {
                    specialPeriodShippingAreas.add(poiShippingInfoDTO);
                } else {
                    regularPeriodShippingAreas.add(poiShippingInfoDTO);
                }
            });
        }

        poiChannelDeliveryInfoVO.setRegularPeriodShippingAreaList(buildRegularPeriodShippingAreaList(regularPeriodShippingAreas, shippingAreaRelationDtos));
        poiChannelDeliveryInfoVO.setSpecialPeriodShippingAreaList(buildSpecialPeriodShippingAreaList(specialPeriodShippingAreas, shippingAreaRelationDtos));

        return poiChannelDeliveryInfoVO;
    }

    private static List<Map<Integer, ChannelShippingAreaVO>> buildRegularPeriodShippingAreaList(List<PoiShippingAreaInfoDTO> specialPeriodShippingAreas,
                                                                                                List<ShippingAreaRelationDto> shippingAreaRelationDtos) {
        return buildChannelShippingAreaVOMapList(specialPeriodShippingAreas, shippingAreaRelationDtos);
    }

    private static List<ShippingAreaGroupVO> buildSpecialPeriodShippingAreaList(List<PoiShippingAreaInfoDTO> specialPeriodShippingAreas,
                                                                                List<ShippingAreaRelationDto> shippingAreaRelationDtos) {
        Map<String, List<PoiShippingAreaInfoDTO>> specialPeriodShippingAreaMap = specialPeriodShippingAreas.stream()
                .collect(Collectors.groupingBy(Convert::buildSpecialPeriodStr));

        List<ShippingAreaGroupVO> groupVOList = specialPeriodShippingAreaMap.values().stream().map(poiShippingAreaInfoDTOS -> {
            ShippingAreaGroupVO shippingAreaGroupVO = new ShippingAreaGroupVO();
            shippingAreaGroupVO.setStartTime(poiShippingAreaInfoDTOS.get(0).getTimeRangeBegin());
            shippingAreaGroupVO.setEndTime(poiShippingAreaInfoDTOS.get(0).getTimeRangeEnd());
            shippingAreaGroupVO.setIsActive(checkSpecialPeriodShippingAreaIsActive(poiShippingAreaInfoDTOS.get(0)));
            shippingAreaGroupVO.setChannelShippingAreaVOList(buildChannelShippingAreaVOMapList(poiShippingAreaInfoDTOS, shippingAreaRelationDtos));
            return shippingAreaGroupVO;
        }).collect(Collectors.toList());

        return groupVOList.stream().sorted((o1,o2) -> {
            ChannelShippingAreaVO area1 = o1.getChannelShippingAreaVOList().get(0)
                    .values().stream()
                    .min(Comparator.comparing(ChannelShippingAreaVO::getShippingAreaId))
                    .get();

            ChannelShippingAreaVO area2 = o2.getChannelShippingAreaVOList().get(0)
                    .values().stream()
                    .min(Comparator.comparing(ChannelShippingAreaVO::getShippingAreaId))
                    .get();

            return area1.getShippingAreaId().compareTo(area2.getShippingAreaId());
        }).collect(Collectors.toList());
    }

    /**
     * 根据关联关系对配送范围分组
     * @param poiShippingInfoDTOList 配送范围列表
     * @param shippingAreaRelationDtos 配送范围关联关系
     * @return
     */
    private static List<Map<Integer, ChannelShippingAreaVO>> buildChannelShippingAreaVOMapList(List<PoiShippingAreaInfoDTO> poiShippingInfoDTOList,
                                                                                      List<ShippingAreaRelationDto> shippingAreaRelationDtos) {
        log.info("poiShippingInfoDTOList: {}", JsonUtil.toJson(poiShippingInfoDTOList));
        Map<Long, PoiShippingAreaInfoDTO> shippingAreaMap = poiShippingInfoDTOList.stream()
                .collect(Collectors.toMap(PoiShippingAreaInfoDTO::getShippingAreaId, Function.identity()));

        //过滤掉不相关的配送范围关联关系
        Map<Long, List<ShippingAreaRelationDto>> relateAreaListMap = shippingAreaRelationDtos.stream()
                .filter(relationDto -> shippingAreaMap.containsKey(relationDto.getAreaId()))
                .collect(Collectors.groupingBy(ShippingAreaRelationDto::getRelationId));

        List<Map<Integer, ChannelShippingAreaVO>> regularPeriodShippingAreaList = new ArrayList<>();

        relateAreaListMap.forEach((key, value) -> {
            List<PoiShippingAreaInfoDTO> relateShippingAreaList = value.stream()
                    .map(relationDto -> shippingAreaMap.get(relationDto.getAreaId()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (hasSameAreaOnDifferentChannel(relateShippingAreaList)) {
                Map<Integer, ChannelShippingAreaVO> relateShippingAreaMap = relateShippingAreaList.stream()
                        .collect(Collectors.toMap(PoiShippingAreaInfoDTO::getChannelId, Convert::buildChannelShippingAreaVO));
                regularPeriodShippingAreaList.add(relateShippingAreaMap);
            } else {
                List<Map<Integer, ChannelShippingAreaVO>> shippingAreaMaps = relateShippingAreaList.stream()
                        .map(shippingDto -> {
                            Map<Integer, ChannelShippingAreaVO> map = new HashMap<>();
                            map.put(shippingDto.getChannelId(), buildChannelShippingAreaVO(shippingDto));
                            return map;
                        }).collect(Collectors.toList());
                regularPeriodShippingAreaList.addAll(shippingAreaMaps);
            }

            //移除已经添加的shippingArea
            value.forEach(area -> shippingAreaMap.remove(area.getAreaId()));
            });

        //添加剩余的没有关联关系的配送范围
        shippingAreaMap.forEach((key, shippingDto) -> {
            HashMap<Integer, ChannelShippingAreaVO> map = new HashMap<>();
            map.put(shippingDto.getChannelId(), buildChannelShippingAreaVO(shippingDto));
            regularPeriodShippingAreaList.add(map);
        });

        //排序 按shippingAreaId正序排列，统一分组内的配送范围按照shippingAreaId最小的算
        return regularPeriodShippingAreaList.stream().sorted((o1, o2) -> {
            ChannelShippingAreaVO earliestShippingArea1 = o1.values().stream()
                    .min(Comparator.comparing(ChannelShippingAreaVO::getShippingAreaId))
                    .get();
            ChannelShippingAreaVO earliestShippingArea2 = o2.values().stream()
                    .min(Comparator.comparing(ChannelShippingAreaVO::getShippingAreaId))
                    .get();

            return earliestShippingArea1.getShippingAreaId().compareTo(earliestShippingArea2.getShippingAreaId());
        }).collect(Collectors.toList());
    }



    public static ChannelShippingAreaVO buildChannelShippingAreaVO(PoiShippingAreaInfoDTO poiShippingInfoDTO) {
        ChannelShippingAreaVO channelShippingAreaVO = new ChannelShippingAreaVO();
        channelShippingAreaVO.setChannelId(poiShippingInfoDTO.getChannelId());
        channelShippingAreaVO.setChannelPoiId(poiShippingInfoDTO.getChannelPoiId());
        channelShippingAreaVO.setAppShippingCode(poiShippingInfoDTO.getAppShippingCode());
        channelShippingAreaVO.setShippingAreaId(poiShippingInfoDTO.getShippingAreaId());
        channelShippingAreaVO.setShippingFee(poiShippingInfoDTO.getShippingFee());
        channelShippingAreaVO.setMinPrice(poiShippingInfoDTO.getMinPrice());
        channelShippingAreaVO.setShippingTagList(parseShippingTagCode(poiShippingInfoDTO.getAppShippingCode()));
        channelShippingAreaVO.setStartTime(poiShippingInfoDTO.getTimeRangeBegin());
        channelShippingAreaVO.setEndTime(poiShippingInfoDTO.getTimeRangeEnd());
        channelShippingAreaVO.setDeliveryRegion(Optional.ofNullable(IGeoUtils.convertDeliveryArea(poiShippingInfoDTO.getArea())).map(IGeoUtils::polygonToString).orElse(null));
        channelShippingAreaVO.setDeliveryName("配送范围"+poiShippingInfoDTO.getShippingAreaId());

        return channelShippingAreaVO;
    }

    public static String buildSpecialPeriodStr(PoiShippingAreaInfoDTO poiShippingInfoDTO) {
        return poiShippingInfoDTO.getTimeRangeBegin() + "-" + poiShippingInfoDTO.getTimeRangeEnd();
    }

    public static Boolean checkSpecialPeriodShippingAreaIsActive(PoiShippingAreaInfoDTO poiShippingAreaInfoDTO) {
        LocalTime now = LocalTime.now();
        LocalTime startTime = LocalTime.parse(poiShippingAreaInfoDTO.getTimeRangeBegin(), DateTimeFormatter.ofPattern("HH:mm"));
        LocalTime endTime = LocalTime.parse(poiShippingAreaInfoDTO.getTimeRangeEnd(), DateTimeFormatter.ofPattern("HH:mm"));

        if(Objects.equals(startTime, endTime)) {
            return false;
        }
        //特殊时段时间范围不跨天
        if (endTime.isAfter(startTime)) {
            return now.isAfter(startTime) && now.isBefore(endTime);
        }

        //特殊时段时间范围跨天
        return now.isAfter(startTime) || now.isBefore(endTime);
    }


    public static List<Integer> parseShippingTagCode(String appShippingCode) {
        if (StringUtils.isBlank(appShippingCode)) {
            return Collections.emptyList();
        }

        List<Integer> tags = new ArrayList<>();
        for (ShippingTagEnum shippingTagEnum : ShippingTagEnum.values()) {
            if (appShippingCode.contains(shippingTagEnum.getShippingCodePrefix())) {
                tags.add(shippingTagEnum.getCode());
            }
        }
        return tags;
    }


    public static boolean hasSameAreaOnDifferentChannel(List<PoiShippingAreaInfoDTO> relateShippingAreaList) {
        if (relateShippingAreaList.size() == 2 && !Objects.equals(relateShippingAreaList.get(0).getChannelId(), relateShippingAreaList.get(1).getChannelId())) {
            Polygon polygon1 = IGeoUtils.convertDeliveryArea(relateShippingAreaList.get(0).getArea());
            Polygon polygon2 = IGeoUtils.convertDeliveryArea(relateShippingAreaList.get(1).getArea());

            return Objects.equals(polygon1, polygon2);
        }

        return false;
    }


    public static List<ChannelPoiShippingInfo> toOldChannelPoiSpecialPeriodShippingList(BatchUpsertChannelPoiShippingParam request,
                                                                                        Map<Integer, List<PoiShippingAreaInfoDTO>> channel2shippingMap) {

        Map<Long, UpsertChannelPoiShippingParam> updateShippingAreaMap = request.getChannelRegionList()
                .stream()
                .filter(upsertChannelPoiShippingParam -> upsertChannelPoiShippingParam.getShippingAreaId() != null && upsertChannelPoiShippingParam.getShippingAreaId() > 0)
                .collect(Collectors.toMap(UpsertChannelPoiShippingParam::getShippingAreaId, Function.identity()));

        List<ChannelPoiShippingInfo> list = new ArrayList<>();
        channel2shippingMap.forEach((channelId, voList) -> {
            for (PoiShippingAreaInfoDTO vo : voList) {
                ChannelPoiShippingInfo shippingInfo = new ChannelPoiShippingInfo();
                shippingInfo.setChannelId(vo.getChannelId());
                shippingInfo.setStoreId(vo.getPoiId());
                shippingInfo.setChannelType(ChannelTypeEnum.enumOf(vo.getChannelId()));
                shippingInfo.setDeliveryRegion(IGeoUtils.polygonToString(IGeoUtils.convertDeliveryArea(vo.getArea())));
                shippingInfo.setMinPrice(vo.getMinPrice());
                shippingInfo.setShippingFee(vo.getShippingFee());
                shippingInfo.setShippingAreaId(vo.getShippingAreaId());
                shippingInfo.setShippingTagList(ShippingUtils.parseShippingTag(vo.getAppShippingCode()));
                shippingInfo.setTimeRangeBegin(vo.getTimeRangeBegin());
                shippingInfo.setTimeRangeEnd(vo.getTimeRangeEnd());

                if (updateShippingAreaMap.containsKey(vo.getShippingAreaId())) {
                    shippingInfo.setDeliveryArea(updateShippingAreaMap.get(vo.getShippingAreaId()).getOldDeliveryArea());
                }

                list.add(shippingInfo);
            }
        });
        return list;
    }

    public static List<ChannelPoiShippingInfo> toOldChannelPoiRegularPeriodShippingList(BatchUpsertChannelPoiShippingParam request,
                                                                                        Map<Integer, List<PoiShippingAreaInfoDTO>> channel2shippingMap) {

        Map<Long, UpsertChannelPoiShippingParam> updateShippingAreaMap = request.getChannelRegionList()
                .stream()
                .filter(upsertChannelPoiShippingParam -> upsertChannelPoiShippingParam.getShippingAreaId() != null && upsertChannelPoiShippingParam.getShippingAreaId() > 0)
                .collect(Collectors.toMap(UpsertChannelPoiShippingParam::getShippingAreaId, Function.identity()));

        List<ChannelPoiShippingInfo> list = new ArrayList<>();
        channel2shippingMap.forEach((channelId, voList) -> {
            for (PoiShippingAreaInfoDTO vo : voList) {
                ChannelPoiShippingInfo shippingInfo = new ChannelPoiShippingInfo();
                shippingInfo.setChannelId(vo.getChannelId());
                shippingInfo.setStoreId(vo.getPoiId());
                shippingInfo.setChannelType(ChannelTypeEnum.enumOf(vo.getChannelId()));
                shippingInfo.setDeliveryRegion(IGeoUtils.polygonToString(IGeoUtils.convertDeliveryArea(vo.getArea())));
                shippingInfo.setMinPrice(vo.getMinPrice());
                shippingInfo.setShippingFee(vo.getShippingFee());
                shippingInfo.setShippingAreaId(vo.getShippingAreaId());
                shippingInfo.setShippingTagList(ShippingUtils.parseShippingTag(vo.getAppShippingCode()));

                if (updateShippingAreaMap.containsKey(vo.getShippingAreaId())) {
                    shippingInfo.setDeliveryArea(updateShippingAreaMap.get(vo.getShippingAreaId()).getOldDeliveryArea());
                }

                list.add(shippingInfo);
            }
        });
        return list;
    }

    /**
     *
     * @param request
     * @param channelId2ShippingListMap
     * @return
     */
    public static List<ChannelPoiShippingInfo> toNewChannelPoiRegularPeriodShippingList(BatchUpsertChannelPoiShippingParam request,
                                                                                        Map<Integer, List<PoiShippingAreaInfoDTO>> channelId2ShippingListMap) {
        List<ChannelPoiShippingInfo> newChannelShippingList = new ArrayList<>();
        request.getChannelRegionList().forEach(updateRegionInfo -> {
            //新增
            if (Objects.isNull(updateRegionInfo.getShippingAreaId())) {
                ChannelPoiShippingInfo newShippingAreaInfo = buildChannelPoiShippingInfo(updateRegionInfo, request.getStoreId(), ChannelPoiShippingAreaOperationEnum.NEW);
                //校验是否已存在重复的配送范围
                checkAlreadyHasSameShippingArea(newShippingAreaInfo, channelId2ShippingListMap.get(updateRegionInfo.getChannelId()));

                newChannelShippingList.add(newShippingAreaInfo);
            } else {
                //修改
                List<PoiShippingAreaInfoDTO> poiShippingInfoDTOS = channelId2ShippingListMap.get(updateRegionInfo.getChannelId());
                Map<Long, PoiShippingAreaInfoDTO> shippingInfoDTOMap = poiShippingInfoDTOS.stream()
                        .collect(Collectors.toMap(PoiShippingAreaInfoDTO::getShippingAreaId, Function.identity()));
                if (!shippingInfoDTOMap.containsKey(updateRegionInfo.getShippingAreaId())) {
                    //原来不包含修改的配送范围 报错
                    throw new SystemException("没有历史配送范围:" + updateRegionInfo.getShippingAreaId());
                }

                if (checkTagIsChanged(updateRegionInfo)) {
                    //先增后删
                    newChannelShippingList.add(buildChannelPoiShippingInfo(updateRegionInfo, request.getStoreId(), ChannelPoiShippingAreaOperationEnum.REPLACE));
                } else {
                    //更新原有配送范围
                    newChannelShippingList.add(buildChannelPoiShippingInfo(updateRegionInfo, request.getStoreId(), ChannelPoiShippingAreaOperationEnum.UPDATE));
                }
            }
        });

        return newChannelShippingList;

    }


    /**
     *
     * @param request
     * @param channelId2ShippingListMap
     * @return
     */
    public static List<ChannelPoiShippingInfo> toNewChannelPoiSpecialPeriodShippingList(BatchUpsertChannelPoiShippingParam request,
                                                                                        Map<Integer, List<PoiShippingAreaInfoDTO>> channelId2ShippingListMap) {
        List<ChannelPoiShippingInfo> newChannelShippingList = new ArrayList<>();
        request.getChannelRegionList().forEach(updateRegionInfo -> {
            //新增
            if (Objects.isNull(updateRegionInfo.getShippingAreaId())) {

                ChannelPoiShippingInfo newShippingAreaInfo = buildChannelPoiShippingInfo(updateRegionInfo, request.getStoreId(), ChannelPoiShippingAreaOperationEnum.NEW);

                //新增时校验是否重复创建
                checkAlreadyHasSameShippingArea(newShippingAreaInfo, channelId2ShippingListMap.get(updateRegionInfo.getChannelId()));

                //校验是否存在配送时段交叉
                checkPeriodIsPartialOverlap(newShippingAreaInfo, channelId2ShippingListMap.get(updateRegionInfo.getChannelId()), request.getOnlyUpdatePeriodTime());

                newChannelShippingList.add(newShippingAreaInfo);
            } else {
                //修改
                List<PoiShippingAreaInfoDTO> poiShippingInfoDTOS = channelId2ShippingListMap.get(updateRegionInfo.getChannelId());
                Map<Long, PoiShippingAreaInfoDTO> shippingInfoDTOMap = poiShippingInfoDTOS.stream()
                        .collect(Collectors.toMap(PoiShippingAreaInfoDTO::getShippingAreaId, Function.identity()));
                if (!shippingInfoDTOMap.containsKey(updateRegionInfo.getShippingAreaId())) {
                    //原来不包含修改的配送范围 报错
                    throw new SystemException("没有历史配送范围:" + updateRegionInfo.getShippingAreaId());
                }

                if (checkTagIsChanged(updateRegionInfo)) {
                    //先增后删
                    ChannelPoiShippingInfo newShippingAreaInfo = buildChannelPoiShippingInfo(updateRegionInfo, request.getStoreId(), ChannelPoiShippingAreaOperationEnum.REPLACE);

                    //校验是否存在配送时段交叉
                    checkPeriodIsPartialOverlap(newShippingAreaInfo, channelId2ShippingListMap.get(updateRegionInfo.getChannelId()), request.getOnlyUpdatePeriodTime());

                    newChannelShippingList.add(newShippingAreaInfo);
                } else {
                    //更新原有配送范围
                    ChannelPoiShippingInfo newShippingAreaInfo = buildChannelPoiShippingInfo(updateRegionInfo, request.getStoreId(), ChannelPoiShippingAreaOperationEnum.UPDATE);

                    //校验是否存在配送时段交叉
                    checkPeriodIsPartialOverlap(newShippingAreaInfo, channelId2ShippingListMap.get(updateRegionInfo.getChannelId()), request.getOnlyUpdatePeriodTime());

                    newChannelShippingList.add(newShippingAreaInfo);
                }
            }
        });

        return newChannelShippingList;

    }
    public static List<ChannelPoiShippingInfo> toResetShippingList(ResetShippingAreaRequest request) {
        return request.getChannelRegionDTOS().stream()
                .map(dto -> {
                    ChannelPoiShippingInfo shippingInfo = new ChannelPoiShippingInfo();
                    shippingInfo.setDeliveryArea(String.format("%.2f", IGeoUtils.calArea(dto.getDeliveryRegion())));
                    shippingInfo.setChannelType(ChannelTypeEnum.enumOf(dto.getChannelId()));
                    shippingInfo.setChannelId(dto.getChannelId());
                    shippingInfo.setOperate(ChannelPoiShippingAreaOperationEnum.RESET);
                    shippingInfo.setShippingTagList(Fun.map(dto.getShippingTagList(), ShippingUtils::codeOfShippingTag));
                    shippingInfo.setShippingFee(dto.getShippingFee());
                    shippingInfo.setDeliveryRegion(dto.getDeliveryRegion());
                    shippingInfo.setMinPrice(dto.getMinPrice());
                    shippingInfo.setStoreId(request.getStoreId());
                    return shippingInfo;
                }).collect(Collectors.toList());
    }


    private static void checkAlreadyHasSameShippingArea(ChannelPoiShippingInfo newShippingInfo, List<PoiShippingAreaInfoDTO> oldPoiShippingAreaInfoList) {
        if (CollectionUtils.isEmpty(oldPoiShippingAreaInfoList)) {
            return;
        }

        Optional<PoiShippingAreaInfoDTO> repeatShippingAreaOpt = oldPoiShippingAreaInfoList.stream()
                .filter(oldPoiShippingAreaInfo -> Objects.equals(oldPoiShippingAreaInfo.getChannelId(), newShippingInfo.getChannelId()))
                .filter(oldPoiShippingAreaInfo -> {
                    Polygon oldArea = IGeoUtils.convertDeliveryArea(oldPoiShippingAreaInfo.getArea());
                    Polygon newArea = IGeoUtils.convertDeliveryArea(IGeoUtils.createCoordinateDtoList(newShippingInfo.getDeliveryRegion()));

                    //如果指定了时段 判断相同时段下是否有同样的配送范围
                    if (StringUtils.isNotBlank(newShippingInfo.getTimeRangeBegin()) && StringUtils.isNotBlank(newShippingInfo.getTimeRangeEnd())) {
                        return Objects.equals(oldPoiShippingAreaInfo.getTimeRangeBegin(), newShippingInfo.getTimeRangeBegin()) &&
                                Objects.equals(oldPoiShippingAreaInfo.getTimeRangeEnd(), newShippingInfo.getTimeRangeEnd()) &&
                                Objects.equals(oldArea, newArea);

                    }

                    //如果未指定时段 判断正常时段下是否有同样的配送范围
                    return !isSpecialPeriodShippingAres(oldPoiShippingAreaInfo) && Objects.equals(oldArea, newArea);
                }).findAny();

        if (repeatShippingAreaOpt.isPresent()) {
            throw new BizException("该渠道下已经存在相同的配送范围, 请直接在已有的配送范围上修改");
        }
    }

    private static void checkPeriodIsPartialOverlap(ChannelPoiShippingInfo newShippingInfo,
                                                    List<PoiShippingAreaInfoDTO> oldPoiShippingAreaInfoList,
                                                    Boolean onlyChangeTimePeriod) {
        if (CollectionUtils.isEmpty(oldPoiShippingAreaInfoList)) {
            return;
        }

        //非特殊时段不需要校验
        if (!isSpecialPeriodShippingAres(newShippingInfo)) {
            return;
        }

        Map<Long, PoiShippingAreaInfoDTO> oldShippingMap = oldPoiShippingAreaInfoList.stream().collect(Collectors.toMap(PoiShippingAreaInfoDTO::getShippingAreaId, Function.identity()));
        PoiShippingAreaInfoDTO oldShippingArea = oldShippingMap.get(newShippingInfo.getShippingAreaId());

        Optional<PoiShippingAreaInfoDTO> partialOverlapShippingAreaWithNewShippingAreaOtp = oldPoiShippingAreaInfoList.stream()
                .filter(ShippingAreaService::isSpecialPeriodShippingAres)  //只跟特殊时段配送范围比较
                .filter(oldPoiShippingArea -> !Objects.equals(oldPoiShippingArea.getShippingAreaId(), newShippingInfo.getShippingAreaId())) //排除自己
                .filter(oldPoiShippingArea -> oldShippingArea == null || !((Objects.equals(onlyChangeTimePeriod, true) && Objects.equals(oldPoiShippingArea.getTimeRangeBegin(), oldShippingArea.getTimeRangeBegin()) && Objects.equals(oldPoiShippingArea.getTimeRangeEnd(), oldShippingArea.getTimeRangeEnd())))) //排除跟自己原来一样的配送范围
                .filter(oldPoiShippingArea -> Objects.equals(oldPoiShippingArea.getChannelId(), newShippingInfo.getChannelId()))
                .filter(oldPoiShippingArea -> {
                    Pair<LocalTime, LocalTime> newPeriod = Pair.of(LocalTime.parse(newShippingInfo.getTimeRangeBegin(), DateTimeFormatter.ofPattern("HH:mm")),
                            LocalTime.parse(newShippingInfo.getTimeRangeEnd(), DateTimeFormatter.ofPattern("HH:mm")));


                    Pair<LocalTime, LocalTime> oldPeriod = Pair.of(LocalTime.parse(oldPoiShippingArea.getTimeRangeBegin(), DateTimeFormatter.ofPattern("HH:mm")),
                            LocalTime.parse(oldPoiShippingArea.getTimeRangeEnd(), DateTimeFormatter.ofPattern("HH:mm")));


                    //两个时段完全相同范围false
                    if (Objects.equals(newPeriod.getLeft(), oldPeriod.getLeft()) && Objects.equals(newPeriod.getRight(), oldPeriod.getRight())) {
                        return false;
                    }

                    return checkPeriodIsOverLap(newPeriod, oldPeriod);
                }).findAny();

        if (partialOverlapShippingAreaWithNewShippingAreaOtp.isPresent()) {
            throw new BizException(String.format("保存失败, %s渠道已有配送范围时间与当前时段有交叉,请返回修改", ChannelTypeEnum.enumOf(newShippingInfo.getChannelId()).getChannelName()));
        }

    }

    private static boolean checkPeriodIsOverLap(Pair<LocalTime, LocalTime> period1, Pair<LocalTime, LocalTime> period2) {
        boolean period1IsCrossDay = period1.getRight().isBefore(period1.getLeft());
        boolean period2IsCrossDay = period2.getRight().isBefore(period2.getLeft());

        //1.两个时段都不跨天
        if (!period1IsCrossDay && !period2IsCrossDay) {
            return (isAfterOrEqual(period1.getRight(), period2.getLeft()) && isBeforeOrEqual(period1.getRight(), period2.getRight()))
                    || (isAfterOrEqual(period2.getRight(), period1.getLeft()) && isBeforeOrEqual(period2.getRight(), period1.getRight()));
        }

        //2.两个时段都跨天
        if (period1IsCrossDay && period2IsCrossDay) {
            return true;
        }

        //3.一个时段跨天 一个时段不跨天
        if (period1IsCrossDay) {
            return checkPeriodIsOverLap(period2, Pair.of(LocalTime.MIN, period1.getRight())) ||
                    checkPeriodIsOverLap(period2, Pair.of(period1.getLeft(), LocalTime.MAX));
        }else {
            return checkPeriodIsOverLap(period1, Pair.of(LocalTime.MIN, period2.getRight())) ||
                    checkPeriodIsOverLap(period1, Pair.of(period2.getLeft(), LocalTime.MAX));
        }
    }

    private static ChannelPoiShippingInfo buildChannelPoiShippingInfo(UpsertChannelPoiShippingParam param, Long storeId, ChannelPoiShippingAreaOperationEnum operation) {
        ChannelPoiShippingInfo shippingInfo = new ChannelPoiShippingInfo();
        if (Objects.equals(operation, ChannelPoiShippingAreaOperationEnum.REPLACE) ||
                Objects.equals(operation, ChannelPoiShippingAreaOperationEnum.NEW)) {
            shippingInfo.setAppShippingCode(ShippingUtils.createAppShippingCode(param.getShippingTagList()));
        } else {
            shippingInfo.setAppShippingCode(param.getAppShippingCode());
        }
        shippingInfo.setShippingAreaId(param.getShippingAreaId());
        shippingInfo.setStoreId(storeId);
        shippingInfo.setShippingFee(param.getShippingFee());
        shippingInfo.setDeliveryRegion(param.getDeliveryRegion());
        shippingInfo.setChannelId(param.getChannelId());
        shippingInfo.setChannelType(ChannelTypeEnum.enumOf(param.getChannelId()));
        shippingInfo.setMinPrice(param.getMinPrice());
        shippingInfo.setTimeRangeBegin(StringUtils.isBlank(param.getStartTime()) ? DEFAULT_START_TIME : param.getStartTime());
        shippingInfo.setTimeRangeEnd(StringUtils.isBlank(param.getEndTime()) ? DEFAULT_END_TIME : param.getEndTime());
        shippingInfo.setOperate(operation);
        shippingInfo.setDeliveryArea(param.getNewDeliveryArea());
        shippingInfo.setShippingTagList(Fun.map(param.getShippingTagList(), ShippingUtils::codeOfShippingTag));
        return shippingInfo;
    }

    private static boolean checkTagIsChanged(UpsertChannelPoiShippingParam param) {
        List<ShippingTagEnum> oldShippingTag = ShippingUtils.parseShippingTag(param.getAppShippingCode());
        List<ShippingTagEnum> newShippingTag = Fun.map(param.getShippingTagList(), ShippingUtils::codeOfShippingTag);
        return !CollectionUtils.isEqualCollection(oldShippingTag, newShippingTag);
    }

    public static ElectrobikeRouteVO toVO(RouteInfo route, String serialId) {
        if (Objects.isNull(route)) {
            return null;
        }
        ElectrobikeRouteVO vo = new ElectrobikeRouteVO();

        vo.setSerialId(serialId);
        vo.setDistance(route.getDistance());
        vo.setPolyline(route.getPolyline());
        vo.setDuration(route.getDuration());
        return vo;
    }

    private static boolean isAfterOrEqual(LocalTime time1, LocalTime time2) {
        return time1.isAfter(time2) || time1.equals(time2);
    }

    private static boolean isBeforeOrEqual(LocalTime time1, LocalTime time2) {
        return time1.isBefore(time2) || time1.equals(time2);
    }

}
