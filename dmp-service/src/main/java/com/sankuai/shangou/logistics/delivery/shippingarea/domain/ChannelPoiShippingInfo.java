package com.sankuai.shangou.logistics.delivery.shippingarea.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ShippingTagEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.ChannelPoiShippingAreaOperationEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.ChannelTypeEnum;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChannelPoiShippingInfo {
    @FieldDoc(
            description = "业务方配送范围id(商家端创建的配送范围，此值为空)"
    )
    private String appShippingCode;

    @FieldDoc(
            description = "门店id(百川门店id)"
    )
    private Long storeId;

    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道类型"
    )
    private ChannelTypeEnum channelType;

    @FieldDoc(
            description = "门店配送范围"
    )
    private String deliveryRegion;

    @FieldDoc(
            description = "起送价，单位：元(人民币)"
    )
    private double minPrice;

    @FieldDoc(
            description = "配送费，单位：元(人民币)"
    )
    private double shippingFee;

    @FieldDoc(
            description = "配送范围底层唯一键"
    )
    private Long shippingAreaId;

    @FieldDoc(
            description = "门店配送面积，单位：平方公里"
    )
    private String deliveryArea = "0";

    @FieldDoc(
            description = "生效时间段-开始时间"
    )
    private String timeRangeBegin;

    @FieldDoc(
            description = "生效时间段-结束时间"
    )
    private String timeRangeEnd;


    private ChannelPoiShippingAreaOperationEnum operate;

    private Long oldAreaId;

    private List<ShippingTagEnum> shippingTagList = new ArrayList<>();

}
