package com.sankuai.shangou.logistics.delivery.configure.model.value;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-09
 * @email <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExpressionNode {

    private ExpressionCondition condition;

    private List<ExpressionNode> subs;

    private String formula;

    public static ExpressionNode genEmptyTree() {
        return new ExpressionNode(null, Lists.newArrayList(), null);
    }
}
