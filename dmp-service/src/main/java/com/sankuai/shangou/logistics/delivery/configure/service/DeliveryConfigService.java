package com.sankuai.shangou.logistics.delivery.configure.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.drunkhorsemgmt.labor.exception.SystemException;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.BookingOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigSaveRequest;
import com.sankuai.shangou.logistics.delivery.configure.enums.CompletedSortModeEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.InternalNavigationModeEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.SelfDeliveryModeEnum;
import com.sankuai.shangou.logistics.delivery.configure.model.DeliveryDimensionPoi;
import com.sankuai.shangou.logistics.delivery.configure.model.DeliveryPoi;
import com.sankuai.shangou.logistics.delivery.configure.model.value.AssessTimeConfig;
import com.sankuai.shangou.logistics.delivery.configure.repository.DeliveryDimensionPoiRepository;
import com.sankuai.shangou.logistics.delivery.configure.repository.DeliveryPoiRepository;
import com.sankuai.shangou.logistics.delivery.configure.service.converter.AssessTimeConfigConverter;
import com.sankuai.shangou.logistics.delivery.configure.service.wrapper.FulfillConfigServiceWrapper;
import com.sankuai.shangou.logistics.delivery.configure.service.wrapper.TenantChannelServiceWrapper;
import com.sankuai.shangou.logistics.delivery.configure.value.*;
import com.sankuai.shangou.qnh.ofc.ebase.consts.OfcEbaseResultCodeEnum;
import com.sankuai.shangou.qnh.ofc.ebase.consts.OrderTypeEnum;
import com.sankuai.shangou.qnh.ofc.ebase.dto.FulfillConfigDTO;
import com.sankuai.shangou.qnh.ofc.ebase.request.QueryFulfillConfigRequest;
import com.sankuai.shangou.qnh.ofc.ebase.request.SaveFulfillConfigRequest;
import com.sankuai.shangou.qnh.ofc.ebase.response.QueryFulfillConfigResponse;
import com.sankuai.shangou.qnh.ofc.ebase.response.SaveFulfillConfigResponse;
import com.sankuai.shangou.qnh.ofc.ebase.service.FulfillmentStoreConfigThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 配送配置服务
 *
 * <AUTHOR>
 * @date 2025-07-04
 * @email <EMAIL>
 */
@Slf4j
@Service
public class DeliveryConfigService {

    @Resource
    private DeliveryDimensionPoiRepository deliveryDimensionPoiRepository;
    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;
    @Resource
    private TenantChannelServiceWrapper tenantChannelServiceWrapper;
    @Resource
    private FulfillConfigServiceWrapper fulfillConfigServiceWrapper;

    /**
     * 查询门店配置明细
     *
     * @param tenantId 租户ID
     * @param storeId  门店ID
     * @return 门店配置明细
     */
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public DeliveryConfigDetailVO queryConfigDetail(Long tenantId, Long storeId) {
        try {
            log.info("开始查询门店配置明细, tenantId: {}, storeId: {}", tenantId, storeId);

            // 查询基础配送配置
            List<Integer> tenantChannelTypes = tenantChannelServiceWrapper.getTenantChannelType(tenantId);
            List<DeliveryPoi> deliveryPois = Lists.newArrayList();
            if (CollectionUtils.isEmpty(tenantChannelTypes)) {
                deliveryPois = deliveryPoiRepository.queryDeliveryPoi(tenantId, storeId, tenantChannelTypes);
            }

            // 查询配送维度配置
            DeliveryDimensionPoi deliveryDimensionPoi = deliveryDimensionPoiRepository.queryDeliveryDimensionPoi(tenantId, storeId);

            //查ofc履约配置
            FulfillConfigDTO fulfillConfigDTO = fulfillConfigServiceWrapper.queryFulfillConfig(tenantId, storeId);

            // 构建返回结果
            DeliveryConfigDetailVO result = new DeliveryConfigDetailVO();
            result.setTenantId(tenantId);
            result.setStoreId(storeId);

            // 构建配送平台配置列表
            if (CollectionUtils.isNotEmpty(deliveryPois)) {
                List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> platformConfigList = deliveryPois.stream().map(this::buildDeliveryPlatformConfig).collect(Collectors.toList());
                result.setDeliveryPlatformConfig(platformConfigList);
            }

            // 构建自送配置
            if (Objects.nonNull(deliveryDimensionPoi)) {
                DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfig = buildSelfDeliveryConfig(deliveryDimensionPoi);
                if (Objects.nonNull(fulfillConfigDTO)) {
                    selfDeliveryConfig.setSelfDeliveryMode(fulfillConfigDTO.getPickDeliveryWorkMode());
                }
                result.setSelfDeliveryConfig(selfDeliveryConfig);
            }

            log.info("查询门店配置明细成功, tenantId: {}, storeId: {}", tenantId, storeId);
            return result;

        } catch (Exception e) {
            log.error("查询门店配置明细失败, tenantId: {}, storeId: {}", tenantId, storeId, e);
            throw new RuntimeException("查询门店配置明细失败", e);
        }
    }

    /**
     * 保存门店配置明细
     *
     * @param request 保存请求
     */
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public void saveConfigDetail(DeliveryConfigSaveRequest request) {
        try {
            DeliveryConfigDetailVO detailVO = request.getDeliveryConfigDetailVO();
            if (detailVO == null) {
                throw new IllegalArgumentException("配置详情不能为空");
            }

            Long tenantId = detailVO.getTenantId();
            Long storeId = detailVO.getStoreId();

            log.info("开始保存门店配置明细, tenantId: {}, storeId: {}", tenantId, storeId);

            // 保存配送平台配置
            if (!CollectionUtils.isEmpty(detailVO.getDeliveryPlatformConfig())) {
                saveDeliveryPlatformConfig(tenantId, storeId, detailVO.getDeliveryPlatformConfig());
            }

            // 保存自送配置
            if (Objects.nonNull(detailVO.getSelfDeliveryConfig())) {
                saveSelfDeliveryConfig(tenantId, storeId, detailVO.getSelfDeliveryConfig());
            }

            if (Objects.nonNull(request.getDeliveryConfigDetailVO().getSelfDeliveryConfig().getSelfDeliveryMode())) {
                fulfillConfigServiceWrapper.saveFulfillConfig(tenantId, storeId, new FulfillConfigDTO(request.getDeliveryConfigDetailVO().getSelfDeliveryConfig().getSelfDeliveryMode()));
            }

            log.info("保存门店配置明细成功, tenantId: {}, storeId: {}", tenantId, storeId);

        } catch (Exception e) {
            log.error("保存门店配置明细失败, request: {}", JSON.toJSONString(request), e);
            throw new RuntimeException("保存门店配置明细失败", e);
        }
    }

    /**
     * 构建配送平台配置
     */
    private DeliveryConfigDetailVO.DeliveryPlatformConfigVO buildDeliveryPlatformConfig(DeliveryPoi deliveryPoi) {
        DeliveryConfigDetailVO.DeliveryPlatformConfigVO platformConfig = new DeliveryConfigDetailVO.DeliveryPlatformConfigVO();

        // 基础信息
        platformConfig.setChannelType(deliveryPoi.getChannelType());

        // 配送平台
        if (deliveryPoi.getDeliveryPlatform() != null) {
            platformConfig.setPlatformCode(deliveryPoi.getDeliveryPlatform().getCode());
        }

        // 状态 - 默认启用
        platformConfig.setStatus(1);

        // 配送发起时间点配置
        if (deliveryPoi.getDeliveryLaunchPoint() != null) {
            fillPlatformLaunchPointConfig(platformConfig, deliveryPoi.getDeliveryLaunchPoint());
        }

        // 二级配送配置
        if (deliveryPoi.getSecondDeliveryPlatform() != null) {
            DeliveryConfigDetailVO.SecondDeliveryConfigVO secondConfig = buildSecondDeliveryConfig(deliveryPoi);
            platformConfig.setSecondDeliveryConfig(secondConfig);
        }

        return platformConfig;
    }

    /**
     * 填充平台配送发起时间点配置
     */
    public void fillPlatformLaunchPointConfig(DeliveryConfigDetailVO.DeliveryPlatformConfigVO platformConfig, DeliveryLaunchPoint launchPoint) {
        // 立即单配送发起时间点配置
        if (launchPoint.getImmediateOrderDeliveryLaunchPointConfig() != null) {
            DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig immediateConfig = launchPoint.getImmediateOrderDeliveryLaunchPointConfig();

            if (immediateConfig.getLaunchPoint() != null) {
                platformConfig.setDeliveryLaunchPoint(immediateConfig.getLaunchPoint().getCode());
            }
            if (immediateConfig.getDelayMinutes() != null) {
                platformConfig.setDeliveryLaunchDelayMinutes(immediateConfig.getDelayMinutes());
            }
        }

        // 预约单配送发起时间点配置
        if (launchPoint.getBookingOrderDeliveryLaunchPointConfig() != null) {
            DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig bookingConfig = launchPoint.getBookingOrderDeliveryLaunchPointConfig();

            if (bookingConfig.getConfigMinutes() != null) {
                platformConfig.setBookingOrderDeliveryLaunchMinutes(bookingConfig.getConfigMinutes());
            }
        }
    }

    /**
     * 构建二级配送配置
     */
    private DeliveryConfigDetailVO.SecondDeliveryConfigVO buildSecondDeliveryConfig(DeliveryPoi deliveryPoi) {
        DeliveryConfigDetailVO.SecondDeliveryConfigVO secondConfig = new DeliveryConfigDetailVO.SecondDeliveryConfigVO();

        // 解析二级配送平台配置
        if (deliveryPoi.getSecondDeliveryPlatform() != null) {
            try {
                SecondDeliveryConfig secondDeliveryConfig =
                        deliveryPoi.getSecondDeliveryPlatform();

                secondConfig.setSecondPlatformCodeCode(secondDeliveryConfig.getSecondPlatformCodeCode());
                secondConfig.setRedirectUrl(secondDeliveryConfig.getRedirectUrl());

                // 构建禁用条件
                if (secondDeliveryConfig.getForbiddenCondition() != null) {
                    DeliveryConfigDetailVO.ForbiddenConditionVO forbiddenConditionVO = new DeliveryConfigDetailVO.ForbiddenConditionVO();
                    forbiddenConditionVO.setOrderTags(secondDeliveryConfig.getForbiddenCondition().getOrderTags());
                    forbiddenConditionVO.setOrderActualPayment(secondDeliveryConfig.getForbiddenCondition().getOrderActualPayment());
                    secondConfig.setForbiddenCondition(forbiddenConditionVO);
                }
            } catch (Exception e) {
                log.warn("解析二级配送平台配置失败: {}", e.getMessage());
            }
        }

        return secondConfig;
    }

    /**
     * 构建自送配置
     */
    private DeliveryConfigDetailVO.SelfDeliveryConfigVO buildSelfDeliveryConfig(DeliveryDimensionPoi dimensionPoi) {
        DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfig = new DeliveryConfigDetailVO.SelfDeliveryConfigVO();


        // 跳转导航模式
        if (dimensionPoi.getInternalNavigationMode() != null) {
            selfDeliveryConfig.setInternalNavigationMode(dimensionPoi.getInternalNavigationMode().getMode());
        }

        // 已送达列表排序模式
        if (dimensionPoi.getCompletedSortMode() != null) {
            selfDeliveryConfig.setCompletedSortMode(dimensionPoi.getCompletedSortMode().getMode());
        }

        // 配送转骑手范围 - 转换Long列表为Integer列表
        if (dimensionPoi.getRiderTransRoles() != null) {
            selfDeliveryConfig.setRiderTransRoles(dimensionPoi.getRiderTransRoles());
        }

        // 剩余时长配置
        if (CollectionUtils.isNotEmpty(dimensionPoi.getAssessTimeConfigs())) {
            List<DeliveryConfigDetailVO.AssertTimeVO> assertTimeList = buildAssertTimeConfig(dimensionPoi.getAssessTimeConfigs());
            selfDeliveryConfig.setAssertTime(assertTimeList);
        }

        // 确认送达操作配置
        if (dimensionPoi.getDeliveryCompleteMode() != null) {
            DeliveryConfigDetailVO.DeliveryCompleteModeVO deliveryCompleteMode = buildDeliveryCompleteModeConfig(dimensionPoi.getDeliveryCompleteMode());
            selfDeliveryConfig.setDeliveryCompleteMode(deliveryCompleteMode);
        }

        // 配送提醒设置
        if (dimensionPoi.getDeliveryRemindConfig() != null) {
            DeliveryConfigDetailVO.DeliveryRemindConfigVO deliveryRemindConfig = buildDeliveryRemindConfig(dimensionPoi.getDeliveryRemindConfig());
            selfDeliveryConfig.setDeliveryRemindConfig(deliveryRemindConfig);
        }

        return selfDeliveryConfig;
    }

    /**
     * 构建考核时间配置
     */
    public List<DeliveryConfigDetailVO.AssertTimeVO> buildAssertTimeConfig(List<AssessTimeConfig> assessTimeConfig) {
        if (assessTimeConfig == null) {
            return new ArrayList<>();
        }

        return assessTimeConfig.stream().map(AssessTimeConfigConverter::convertToVO).collect(Collectors.toList());
    }

    /**
     * 构建确认送达操作配置
     */
    public DeliveryConfigDetailVO.DeliveryCompleteModeVO buildDeliveryCompleteModeConfig(DeliveryCompleteMode deliveryCompleteMode) {
        DeliveryConfigDetailVO.DeliveryCompleteModeVO completeModeVO = new DeliveryConfigDetailVO.DeliveryCompleteModeVO();
        completeModeVO.setDistanceReminder(deliveryCompleteMode.getDistanceReminder());

        if (deliveryCompleteMode.getDeliveryCompleteConfig() != null) {
            DeliveryCompleteMode.DeliveryCompleteConfig config = deliveryCompleteMode.getDeliveryCompleteConfig();
            DeliveryConfigDetailVO.DeliveryCompleteConfigVO configVO = new DeliveryConfigDetailVO.DeliveryCompleteConfigVO();

            configVO.setOperationMode(config.getOperationMode());
            configVO.setSendPicToCustomerTips(config.getSendPicToCustomerTips());
            configVO.setNotContactCustomerTips(config.getNotContactCustomerTips());
            configVO.setUploadImageDurationThreshold(config.getUploadImageDurationThreshold());
            configVO.setIsShowNotContactCustomerTips(config.getIsShowNotContactCustomerTips());

            // 转换示例图片信息列表
            if (!CollectionUtils.isEmpty(config.getAllExamplePicInfoList())) {
                List<DeliveryConfigDetailVO.ExamplePicInfoVO> examplePicList = new ArrayList<>();
                config.getAllExamplePicInfoList().forEach(pic -> {
                    DeliveryConfigDetailVO.ExamplePicInfoVO picVO = new DeliveryConfigDetailVO.ExamplePicInfoVO();
                    picVO.setType(pic.getType());
                    picVO.setName(pic.getName());
                    picVO.setPicUrl(pic.getPicUrl());
                    picVO.setOrder(pic.getOrder());
                    examplePicList.add(picVO);
                });
                configVO.setAllExamplePicInfoList(examplePicList);
            }

            // 转换特殊商品上传图片配置
            if (!CollectionUtils.isEmpty(config.getSpecialProductUploadPicConfig())) {
                List<DeliveryConfigDetailVO.SpecialProductUploadPicConfigVO> specialConfigList = new ArrayList<>();
                config.getSpecialProductUploadPicConfig().forEach(specialConfig -> {
                    DeliveryConfigDetailVO.SpecialProductUploadPicConfigVO specialVO = new DeliveryConfigDetailVO.SpecialProductUploadPicConfigVO();
                    specialVO.setProductType(specialConfig.getProductType());
                    specialVO.setPicTypeList(specialConfig.getPicTypeList());
                    specialVO.setIsForceUploadPic(specialConfig.getIsForceUploadPic());
                    specialVO.setNeedUploadPicCount(specialConfig.getNeedUploadPicCount());
                    specialConfigList.add(specialVO);
                });
                configVO.setSpecialProductUploadPicConfig(specialConfigList);
            }

            completeModeVO.setDeliveryCompleteConfig(configVO);
        }

        return completeModeVO;
    }

    /**
     * 构建配送提醒配置
     */
    public DeliveryConfigDetailVO.DeliveryRemindConfigVO buildDeliveryRemindConfig(DeliveryRemindConfig deliveryRemindConfig) {
        DeliveryConfigDetailVO.DeliveryRemindConfigVO remindConfigVO = new DeliveryConfigDetailVO.DeliveryRemindConfigVO();
        remindConfigVO.setReceiveTimeOutMins(deliveryRemindConfig.getReceiveTimeOutMins());
        remindConfigVO.setTakenTimeOutMins(deliveryRemindConfig.getTakenTimeOutMins());
        remindConfigVO.setSoonDeliveryTimeoutMinsBeforeEta(deliveryRemindConfig.getSoonDeliveryTimeoutMinsBeforeEta());

        if (deliveryRemindConfig.getDeliveryTimeOutsBeforeEtaConfig() != null) {
            DeliveryRemindConfig.DeliveryTimeOutsBeforeEtaConfig etaConfig = deliveryRemindConfig.getDeliveryTimeOutsBeforeEtaConfig();
            DeliveryConfigDetailVO.DeliveryTimeOutsBeforeEtaConfigVO etaConfigVO = new DeliveryConfigDetailVO.DeliveryTimeOutsBeforeEtaConfigVO();
            etaConfigVO.setImmediateBeforeEtaMins(etaConfig.getImmediateBeforeEtaMins());
            etaConfigVO.setBookingBeforeEtaMins(etaConfig.getBookingBeforeEtaMins());
            remindConfigVO.setDeliveryTimeOutsBeforeEtaConfig(etaConfigVO);
        }

        return remindConfigVO;
    }

    /**
     * 保存配送平台配置
     */
    private void saveDeliveryPlatformConfig(Long tenantId, Long storeId,
                                            List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> platformConfigList) {
        List<DeliveryPoi> deliveryPois = deliveryPoiRepository.queryDeliveryPoi(tenantId, storeId, IListUtils.mapTo(platformConfigList, DeliveryConfigDetailVO.DeliveryPlatformConfigVO::getChannelType));
        Map<Integer, DeliveryPoi> channelTypeDeliveryPoiMap = IListUtils.nullSafeAndOverrideCollectToMap(deliveryPois, DeliveryPoi::getChannelType, Function.identity());
        for (DeliveryConfigDetailVO.DeliveryPlatformConfigVO platformConfig : platformConfigList) {
            // 查询现有配置
            DeliveryPoi existingPoiOpt = channelTypeDeliveryPoiMap.get(platformConfig.getChannelType());

            // 更新现有配置
            DeliveryPoi deliveryPoi = updateDeliveryPoiFromRequest(existingPoiOpt, platformConfig);

            // 保存配置
            deliveryPoiRepository.saveDeliveryPoi(deliveryPoi);
            log.info("保存配送平台配置成功, tenantId: {}, storeId: {}, channelType: {}",
                    tenantId, storeId, platformConfig.getChannelType());
        }
    }

    /**
     * 保存自送配置
     */
    private void saveSelfDeliveryConfig(Long tenantId, Long storeId,
                                        DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfig) {
        // 查询现有配置
        DeliveryDimensionPoi existingDimensionPoi =
                deliveryDimensionPoiRepository.queryDeliveryDimensionPoi(tenantId, storeId);

        if (Objects.nonNull(existingDimensionPoi)) {
            // 更新现有配置
            DeliveryDimensionPoi updatedDimensionPoi = updateDeliveryDimensionPoiFromRequest(
                    existingDimensionPoi, selfDeliveryConfig);
            deliveryDimensionPoiRepository.updateDeliveryDimensionPoi(updatedDimensionPoi);
        } else {
            throw new SystemException("没有有效的配置");
        }

        log.info("保存自送配置成功, tenantId: {}, storeId: {}", tenantId, storeId);
    }

    /**
     * 从请求更新DeliveryPoi
     */
    public DeliveryPoi updateDeliveryPoiFromRequest(DeliveryPoi existingPoi,
                                                    DeliveryConfigDetailVO.DeliveryPlatformConfigVO platformConfig) {
        // 构建配送发起时间点配置
        DeliveryLaunchPoint launchPoint = buildDeliveryLaunchPointFromRequest(platformConfig);

        // 获取配送平台枚举
        DeliveryPlatformEnum deliveryPlatform = DeliveryPlatformEnum.enumOf(platformConfig.getPlatformCode());
        if (deliveryPlatform == null) {
            deliveryPlatform = DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM;
        }

        // 构建二级配送配置
        SecondDeliveryConfig secondDeliveryConfig = buildSecondDeliveryConfigFromRequest(platformConfig.getSecondDeliveryConfig());

        // 构建自送预约配送规则配置
        BookingPushDownTimeConfig selfAssessDeliveryConfig = buildBookingPushDownTimeConfigFromRequest(platformConfig.getSelfDeliveryBookingDeliveryRule());

        // 创建新的DeliveryPoi对象，充分使用platformConfig中的配置

        return new DeliveryPoi(
                existingPoi.getId(),
                existingPoi.getTenantId(),
                existingPoi.getStoreId(),
                existingPoi.getCityCode(),
                existingPoi.getContactPhone(),
                deliveryPlatform,
                launchPoint,
                DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY,
                platformConfig.getChannelType(),
                existingPoi.getDeliveryPlatform(),
                existingPoi.getDeliveryIsShowItemNumberEnum(),
                existingPoi.getStoreAddress(),
                selfAssessDeliveryConfig != null ? selfAssessDeliveryConfig : existingPoi.getSelfAssessDeliveryConfig(),
                secondDeliveryConfig
        );
    }

    /**
     * 从请求构建DeliveryLaunchPoint
     */
    public DeliveryLaunchPoint buildDeliveryLaunchPointFromRequest(
            DeliveryConfigDetailVO.DeliveryPlatformConfigVO platformConfig) {

        // 立即单配送发起时间点配置
        ImmediateOrderDeliveryLaunchPointEnum immediatePoint =
                ImmediateOrderDeliveryLaunchPointEnum.enumOf(platformConfig.getDeliveryLaunchPoint());
        if (immediatePoint == null) {
            immediatePoint = ImmediateOrderDeliveryLaunchPointEnum.MERCHANT_ACCEPT;
        }

        DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig immediateConfig =
                new DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(
                        immediatePoint,
                        platformConfig.getDeliveryLaunchDelayMinutes() != null ?
                                platformConfig.getDeliveryLaunchDelayMinutes() : 0
                );

        // 预约单配送发起时间点配置
        DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig bookingConfig =
                new DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(
                        BookingOrderDeliveryLaunchPointEnum.ORDER_PAID, // 默认商家接单
                        platformConfig.getBookingOrderDeliveryLaunchMinutes() != null ?
                                platformConfig.getBookingOrderDeliveryLaunchMinutes() : 0
                );

        return new DeliveryLaunchPoint(immediateConfig, bookingConfig);
    }

    /**
     * 从请求更新DeliveryDimensionPoi
     */
    public DeliveryDimensionPoi updateDeliveryDimensionPoiFromRequest(DeliveryDimensionPoi existingPoi,
                                                                      DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfig) {
        return new DeliveryDimensionPoi(
                existingPoi.getId(),
                existingPoi.getTenantId(),
                existingPoi.getStoreId(),
                InternalNavigationModeEnum.getByMode(selfDeliveryConfig.getInternalNavigationMode()),
                buildAssessTimeConfigFromRequest(selfDeliveryConfig.getAssertTime()),
                buildDeliveryCompleteModeFromRequest(selfDeliveryConfig.getDeliveryCompleteMode()),
                selfDeliveryConfig.getRiderTransRoles(),
                CompletedSortModeEnum.getByMode(selfDeliveryConfig.getCompletedSortMode()),
                buildDeliveryRemindConfigFromRequest(selfDeliveryConfig.getDeliveryRemindConfig()), LocalDateTime.now(), LocalDateTime.now()
        );
    }

    /**
     * 从请求构建AssessTimeConfig
     */
    public List<AssessTimeConfig> buildAssessTimeConfigFromRequest(List<DeliveryConfigDetailVO.AssertTimeVO> assertTimeList) {
        if (CollectionUtils.isEmpty(assertTimeList)) {
            return null;
        }

        return assertTimeList.stream().map(AssessTimeConfigConverter::convertFromVO).collect(Collectors.toList());
    }

    /**
     * 从请求构建DeliveryCompleteMode
     */
    public DeliveryCompleteMode buildDeliveryCompleteModeFromRequest(
            DeliveryConfigDetailVO.DeliveryCompleteModeVO deliveryCompleteModeVO) {
        if (deliveryCompleteModeVO == null) {
            return null;
        }

        DeliveryCompleteMode deliveryCompleteMode = new DeliveryCompleteMode();
        deliveryCompleteMode.setDistanceReminder(deliveryCompleteModeVO.getDistanceReminder());

        if (deliveryCompleteModeVO.getDeliveryCompleteConfig() != null) {
            DeliveryCompleteMode.DeliveryCompleteConfig config = buildDeliveryCompleteConfigFromRequest(
                    deliveryCompleteModeVO.getDeliveryCompleteConfig());
            deliveryCompleteMode.setDeliveryCompleteConfig(config);
        }

        return deliveryCompleteMode;
    }

    /**
     * 从请求构建DeliveryCompleteConfig
     */
    private DeliveryCompleteMode.DeliveryCompleteConfig buildDeliveryCompleteConfigFromRequest(
            DeliveryConfigDetailVO.DeliveryCompleteConfigVO configVO) {
        if (configVO == null) {
            return null;
        }

        DeliveryCompleteMode.DeliveryCompleteConfig config = new DeliveryCompleteMode.DeliveryCompleteConfig();
        config.setOperationMode(configVO.getOperationMode());
        config.setSendPicToCustomerTips(configVO.getSendPicToCustomerTips());
        config.setNotContactCustomerTips(configVO.getNotContactCustomerTips());
        config.setUploadImageDurationThreshold(configVO.getUploadImageDurationThreshold());
        config.setIsShowNotContactCustomerTips(configVO.getIsShowNotContactCustomerTips());

        // 构建示例图片信息列表
        if (!CollectionUtils.isEmpty(configVO.getAllExamplePicInfoList())) {
            List<DeliveryCompleteMode.ExamplePicInfo> examplePicInfoList = new ArrayList<>();
            for (DeliveryConfigDetailVO.ExamplePicInfoVO picVO : configVO.getAllExamplePicInfoList()) {
                DeliveryCompleteMode.ExamplePicInfo picInfo = new DeliveryCompleteMode.ExamplePicInfo();
                picInfo.setType(picVO.getType());
                picInfo.setName(picVO.getName());
                picInfo.setPicUrl(picVO.getPicUrl());
                picInfo.setOrder(picVO.getOrder());
                examplePicInfoList.add(picInfo);
            }
            config.setAllExamplePicInfoList(examplePicInfoList);
        }

        // 构建特殊商品上传图片配置
        if (!CollectionUtils.isEmpty(configVO.getSpecialProductUploadPicConfig())) {
            List<DeliveryCompleteMode.SpecialProductUploadPicConfig> specialProductConfigList = new ArrayList<>();
            for (DeliveryConfigDetailVO.SpecialProductUploadPicConfigVO specialVO : configVO.getSpecialProductUploadPicConfig()) {
                DeliveryCompleteMode.SpecialProductUploadPicConfig specialConfig = new DeliveryCompleteMode.SpecialProductUploadPicConfig();
                specialConfig.setProductType(specialVO.getProductType());
                specialConfig.setPicTypeList(specialVO.getPicTypeList());
                specialConfig.setIsForceUploadPic(specialVO.getIsForceUploadPic());
                specialConfig.setNeedUploadPicCount(specialVO.getNeedUploadPicCount());
                specialProductConfigList.add(specialConfig);
            }
            config.setSpecialProductUploadPicConfig(specialProductConfigList);
        }

        return config;
    }

    /**
     * 从请求构建DeliveryRemindConfig
     */
    public DeliveryRemindConfig buildDeliveryRemindConfigFromRequest(
            DeliveryConfigDetailVO.DeliveryRemindConfigVO remindConfigVO) {
        if (remindConfigVO == null) {
            return null;
        }

        DeliveryRemindConfig deliveryRemindConfig = new DeliveryRemindConfig();
        deliveryRemindConfig.setReceiveTimeOutMins(remindConfigVO.getReceiveTimeOutMins());
        deliveryRemindConfig.setTakenTimeOutMins(remindConfigVO.getTakenTimeOutMins());
        deliveryRemindConfig.setSoonDeliveryTimeoutMinsBeforeEta(remindConfigVO.getSoonDeliveryTimeoutMinsBeforeEta());

        if (remindConfigVO.getDeliveryTimeOutsBeforeEtaConfig() != null) {
            DeliveryRemindConfig.DeliveryTimeOutsBeforeEtaConfig etaConfig = new DeliveryRemindConfig.DeliveryTimeOutsBeforeEtaConfig();
            etaConfig.setImmediateBeforeEtaMins(remindConfigVO.getDeliveryTimeOutsBeforeEtaConfig().getImmediateBeforeEtaMins());
            etaConfig.setBookingBeforeEtaMins(remindConfigVO.getDeliveryTimeOutsBeforeEtaConfig().getBookingBeforeEtaMins());
            deliveryRemindConfig.setDeliveryTimeOutsBeforeEtaConfig(etaConfig);
        }

        return deliveryRemindConfig;
    }

    /**
     * 从请求构建SecondDeliveryConfig
     */
    public SecondDeliveryConfig buildSecondDeliveryConfigFromRequest(
            DeliveryConfigDetailVO.SecondDeliveryConfigVO secondDeliveryConfigVO) {
        if (secondDeliveryConfigVO == null) {
            return null;
        }

        SecondDeliveryConfig secondDeliveryConfig = new SecondDeliveryConfig();
        secondDeliveryConfig.setSecondPlatformCodeCode(secondDeliveryConfigVO.getSecondPlatformCodeCode());
        secondDeliveryConfig.setRedirectUrl(secondDeliveryConfigVO.getRedirectUrl());

        // 构建禁用条件
        if (secondDeliveryConfigVO.getForbiddenCondition() != null) {
            SecondDeliveryForbiddenCondition forbiddenCondition = new SecondDeliveryForbiddenCondition();
            forbiddenCondition.setOrderTags(secondDeliveryConfigVO.getForbiddenCondition().getOrderTags());
            forbiddenCondition.setOrderActualPayment(secondDeliveryConfigVO.getForbiddenCondition().getOrderActualPayment());
            secondDeliveryConfig.setForbiddenCondition(forbiddenCondition);
        }

        return secondDeliveryConfig;
    }

    /**
     * 从请求构建BookingPushDownTimeConfig
     */
    public BookingPushDownTimeConfig buildBookingPushDownTimeConfigFromRequest(
            DeliveryConfigDetailVO.SelfDeliveryBookingDeliveryRuleVO selfDeliveryBookingDeliveryRuleVO) {
        if (selfDeliveryBookingDeliveryRuleVO == null) {
            return null;
        }

        BookingPushDownTimeConfig bookingPushDownTimeConfig = new BookingPushDownTimeConfig();

        // 处理预约下推时间配置列表
        if (CollectionUtils.isNotEmpty(selfDeliveryBookingDeliveryRuleVO.getBookingPushDownTimeConfig())) {
            // 这里简化处理，取第一个配置项的信息
            // 实际业务中可能需要更复杂的逻辑来处理多个配置项
            DeliveryConfigDetailVO.BookingPushDownTimeConfigVO firstConfig =
                    selfDeliveryBookingDeliveryRuleVO.getBookingPushDownTimeConfig().get(0);

            bookingPushDownTimeConfig.setOrderTags(firstConfig.getOrderTags());
            bookingPushDownTimeConfig.setCondition(firstConfig.getCondition());
            bookingPushDownTimeConfig.setFormula(firstConfig.getFormula());
        }

        return bookingPushDownTimeConfig;
    }

}
