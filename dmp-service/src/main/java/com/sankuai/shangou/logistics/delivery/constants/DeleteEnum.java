package com.sankuai.shangou.logistics.delivery.constants;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @email <EMAIL>
 */
public enum DeleteEnum {

    NOT_DELETE(0, "未删除"),
    DELETED(1, "已删除")
    ;
    private int code;
    private String desc;

    DeleteEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DeleteEnum valueOfCode(int code) {
        for (DeleteEnum obj : DeleteEnum.values()) {
            if (java.util.Objects.equals(obj.code, code)) {
                return obj;
            }
        }
        return null;
    }

    public static DeleteEnum valueOfDesc(String desc) {
        for (DeleteEnum obj : DeleteEnum.values()) {
            if (java.util.Objects.equals(obj.desc, desc)) {
                return obj;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
