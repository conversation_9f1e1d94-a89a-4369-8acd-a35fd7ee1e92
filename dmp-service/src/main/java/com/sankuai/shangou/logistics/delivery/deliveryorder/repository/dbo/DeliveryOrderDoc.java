package com.sankuai.shangou.logistics.delivery.deliveryorder.repository.dbo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.shangou.logistics.delivery.indicator.repository.es.AbstractEsDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 配送单ES文档
 * @date 2025-05-23
 */
@Data
public class DeliveryOrderDoc extends AbstractEsDoc {

    /**
     * 运单主键ID
     */
    @JsonProperty("delivery_order_id")
    private Long deliveryOrderId;

    /**
     * 租户id
     */
    @JsonProperty("tenant_id")
    private Long tenantId;

    /**
     * 门店id
     */
    @JsonProperty("store_id")
    private Long storeId;

    /**
     * 赋能订单id
     */
    @JsonProperty("order_id")
    private Long orderId;

    @Override
    public String calcEsId() {
        // dmp服务不执行写操作
        return null;
    }
}
