package com.sankuai.shangou.logistics.delivery.shippingarea.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(
        description = "门店扩展订单指标"
)
@Data
public class StoreExtendOrderIndicatorVO {
    @FieldDoc(
            description = "微商城门店ID"
    )
    private Long wxPoiId;

    @FieldDoc(
            description = "微商城日均订单量"
    )
    private Long wxOrdNum;

    @FieldDoc(
            description = "外卖门店ID"
    )
    private Long wmPoiId;

    @FieldDoc(
            description = "外卖门店日均单量"
    )
    private Long wmOrdNum;

}
