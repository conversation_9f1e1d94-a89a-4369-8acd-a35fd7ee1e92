package com.sankuai.shangou.logistics.delivery.poi.client;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.shangou.saas.tenant.thrift.DepartmentV2ThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.constants.PoiFields;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.dto.DepartmentV2Dto;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.request.DepartmentListV2Request;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.response.DepartmentListV2Response;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiBaseInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiMngPageRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiSearchRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiListQueryResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMngPageResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiSearchResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.BatchQueryPermissionGroupRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PermissionGroupVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.exception.ShangouBizException;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.poi.client.dto.PoiBaseInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @email <EMAIL>
 */
@Slf4j
@Service
public class PoiQueryClient {

    @Resource
    private PoiThriftService poiThriftService;
    @Resource
    private PoiManageThriftService poiManageThriftService;
    @Resource
    private AuthThriftService.Iface authThriftService;
    @Resource
    private DepartmentV2ThriftService departmentV2ThriftService;

    private static final int SUCCESS_CODE = 0;
    private static final int DEFAULT_PAGE_SIZE = 200;
    List<Integer> ENTITY_LIST = Lists.newArrayList(PoiEntityTypeEnum.STORE.code(), PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code());
    private static final Set<String> SEARCH_POI_FIELDS = Sets.newHashSet(PoiFields.POI_ID, PoiFields.POI_NAME, PoiFields.ENTITY_TYPE,
            PoiFields.TENANT_ID, PoiFields.MANAGE_MODE);

    public List<PoiBaseInfoDto> queryPoiByDepIdList(long tenantId, List<Long> depIdList) {
        PoiListQueryResponse response = poiThriftService.queryPoiListByDepartmentId(tenantId, depIdList);
        if (response.getStatus().getCode() != SUCCESS_CODE) {
            throw new ThirdPartyException("按部门查询门店失败");
        }

        return response.getPoiList();
    }

    public List<PoiDetailInfoDTO> queryAllPermissionPoiList(long tenantId, long accountId, int appId) {

        int page = 1;
        boolean hasMore = true;
        List<PoiDetailInfoDTO> poiInfoList = Lists.newArrayList();

        try {
            int deep = 0;
            while (hasMore) {
                PoiMngPageRequest request = new PoiMngPageRequest();
                request.setTenantId(tenantId);
                request.setPage(page);
                request.setPageSize(DEFAULT_PAGE_SIZE);
                request.setStatus(PoiStatusEnum.ONLINE.getKey());
                PoiMngPageResponse thisResponse = poiManageThriftService.queryPoiMngPageInfo(request);
                if (thisResponse.getStatus().getCode() != SUCCESS_CODE) {
                    throw new RuntimeException();
                }
                poiInfoList.addAll(thisResponse.getPoiInfoList());
                page++;
                deep++;
                hasMore = thisResponse.getPage() * thisResponse.getPageSize() < thisResponse.getTotal();
                if (deep > 200) {
                    log.error("foreach too deep, break, accountId = {}", accountId);
                    break;
                }
            }

            BatchQueryPermissionGroupRequest request = new BatchQueryPermissionGroupRequest();
            request.setTenantId(tenantId);
            request.setAccountId(accountId);
            request.setAppId(appId);
            request.setTypeList(Lists.newArrayList(PermissionGroupTypeEnum.POI.getValue(), PermissionGroupTypeEnum.WAREHOUSE.getValue()));
            QueryPermissionGroupResponse response = authThriftService.batchQueryPermissionGroupByTokenAndPermissionType(request);
            if (response.getResult().getCode() != SUCCESS_CODE) {
                throw new ThirdPartyException("查询权限任务失败");
            }
            List<String> permissionStoreIds = IListUtils.mapTo(response.getPermissionGroupCodeList(), PermissionGroupVo::getCode);

            List<PoiDetailInfoDTO> permissionPoiDetailInfoDTOList = IListUtils.nullSafeStream(poiInfoList)
                    //权限
                    .filter(poiDetailInfoDTO -> permissionStoreIds.contains(String.valueOf(poiDetailInfoDTO.getPoiId())))
                    .collect(Collectors.toList());

            return permissionPoiDetailInfoDTOList;
        } catch (TException e) {
            log.error("invoke getAllPermissionPoiList error", e);
            throw new ThirdPartyException("查询有数据权限门店失败");
        }
    }

    public Map<Long, List<DepartmentV2Dto>> getDepsGroupByParentId(Long tenantId) {

        List<DepartmentV2Dto> departments = getDepartments(tenantId);

        Map<Long, List<DepartmentV2Dto>> depMaps = IListUtils.nullSafeCollectToListMap(departments, DepartmentV2Dto::getParentId, Lists::newArrayList);
        return depMaps;
    }

    public List<DepartmentV2Dto> getDepartments(Long tenantId) {

        DepartmentListV2Request request = new DepartmentListV2Request();
        request.setTenantId(tenantId);
        request.setAlsoQueryChildCount(false);
        request.setAlsoQueryEmployeeCount(false);
        request.setAlsoQueryDepMgtScope(false);

        DepartmentListV2Response response = departmentV2ThriftService.queryDepartmentList(request);
        if (response.getStatus().getCode() != SUCCESS_CODE) {
            throw new ShangouBizException("查询部门信息失败");
        }

        return response.getDepartmentList();
    }

    /**
     * 查询租户下所有门店(启用的，门店和共享仓)
     *
     * @param tenantId 租户ID
     */
    public List<PoiBaseInfo> queryAllStoreByTenant(Long tenantId) {
        PoiSearchRequest poiSearchRequest = new PoiSearchRequest();
        poiSearchRequest.setTenantId(tenantId);
        poiSearchRequest.setEntityTypeList(ENTITY_LIST);
        poiSearchRequest.setFields(SEARCH_POI_FIELDS);
        poiSearchRequest.setPoiStatusList(Lists.newArrayList(PoiStatusEnum.ONLINE.getKey()));
        poiSearchRequest.setNeedPagination(false);
        return poiSearch(poiSearchRequest);
    }

    /**
     * 查询经营模式下所有门店(启用的，门店和共享仓)
     *
     * @param tenantId   租户ID
     * @param manageMode 经营模式: 1-直营 2-加盟
     */
    public List<PoiBaseInfo> queryAllStoreByManageMode(Long tenantId, Integer manageMode) {
        PoiSearchRequest poiSearchRequest = new PoiSearchRequest();
        poiSearchRequest.setTenantId(tenantId);
        poiSearchRequest.setManageModeList(Lists.newArrayList(manageMode));
        poiSearchRequest.setEntityTypeList(ENTITY_LIST);
        poiSearchRequest.setFields(SEARCH_POI_FIELDS);
        poiSearchRequest.setPoiStatusList(Lists.newArrayList(PoiStatusEnum.ONLINE.getKey()));
        poiSearchRequest.setNeedPagination(false);
        return poiSearch(poiSearchRequest);
    }

    /**
     * 查询门店基础信息
     */
    public List<PoiBaseInfo> queryPoiBaseInfo(List<Long> poiIds) {
        PoiSearchRequest poiSearchRequest = new PoiSearchRequest();
        poiSearchRequest.setFields(SEARCH_POI_FIELDS);
        poiSearchRequest.setPoiIdList(poiIds);
        poiSearchRequest.setNeedPagination(false);
        return poiSearch(poiSearchRequest);
    }

    public List<PoiBaseInfo> poiSearch(PoiSearchRequest poiSearchRequest) {
        PoiSearchResponse response = poiThriftService.search(poiSearchRequest);
        if (response == null || response.getStatus() == null) {
            log.error("invoke poiThriftService.search error, request:{}", poiSearchRequest);
            throw new BizException("查询租户门店列表失败");

        }

        if (response.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
            log.error("invoke poiThriftService.search fail, request:{}, resp:{}", poiSearchRequest, response);
            throw new BizException("查询租户门店列表失败");
        }

        return response.getPoiList().stream().map(poiBaseInfoDto -> PoiBaseInfo.builder()
                        .tenantId(poiBaseInfoDto.getTenantId())
                        .manageMode(poiBaseInfoDto.getManageMode())
                        .poiId(poiBaseInfoDto.getPoiId())
                        .poiName(poiBaseInfoDto.getPoiName())
                        .entityType(poiBaseInfoDto.getEntityType())
                        .build())
                .collect(Collectors.toList());
    }

}
