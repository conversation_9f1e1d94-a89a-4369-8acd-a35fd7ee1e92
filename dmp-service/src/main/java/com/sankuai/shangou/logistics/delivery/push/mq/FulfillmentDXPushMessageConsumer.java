package com.sankuai.shangou.logistics.delivery.push.mq;

import com.alibaba.fastjson.JSON;
import com.meituan.linz.boot.util.Assert;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.shangou.logistics.delivery.enums.FreeMarkTemplateEnum;
import com.sankuai.shangou.logistics.delivery.push.IPushMessageService;
import com.sankuai.shangou.logistics.delivery.push.mq.msg.FulfillmentDXPushMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class FulfillmentDXPushMessageConsumer {

    @Autowired
    private Map<FreeMarkTemplateEnum, IPushMessageService> pushMessageServiceMap;

//    @MafkaConsumer(namespace = "com.sankuai.mafka.castle.daojiacommon", topic = "fulfillment_dx_push_message_topic", group = "fulfillment_dx_push_message_consumer")
    public ConsumeStatus onRecMessage(String msgBody) {
        log.info("开始消费大象通知消息: {}", msgBody);
        if(StringUtils.isEmpty(msgBody)){
            log.info("msgBody is empty");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        Optional<FulfillmentDXPushMessage> messageOptional = translate(msgBody);
        if(!messageOptional.isPresent()){
            log.info("messageObj is empty");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        FulfillmentDXPushMessage pushMessage = messageOptional.get();
        try {
            FreeMarkTemplateEnum templateEnum = FreeMarkTemplateEnum.typeToEnum(pushMessage.getTemplateType());
            if(templateEnum == null){
                log.error("templateEnum is null");
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            if(pushMessageServiceMap == null || (!pushMessageServiceMap.containsKey(templateEnum))){
                log.error("pushMessageService is null");
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            return pushMessageServiceMap.get(templateEnum).doHandler(pushMessage);
        }catch (Exception e){
            log.error("doHandler error",e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private Optional<FulfillmentDXPushMessage> translate(String msgBody) {
        try {
            FulfillmentDXPushMessage message = JSON.parseObject(msgBody, FulfillmentDXPushMessage.class);
            Assert.throwIfNull(message, "消息为空");
            return Optional.of(message);
        }catch (Exception e){
            log.error("translate error",e);
        }
        return Optional.empty();
    }
}
