package com.sankuai.shangou.logistics.delivery.push;

import com.alibaba.fastjson.JSON;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.logistics.delivery.push.cache.DxTokenCache;
import com.sankuai.xm.openplatform.auth.entity.AccessTokenResp;
import com.sankuai.xm.openplatform.auth.entity.AppAuthInfo;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @date 2025-05-19
 */
@Slf4j
public abstract class AbstractDxService {
    private static final String APP_NAME = "com.sankuai.shangou.logistics.dmp";

    @Resource
    private XmAuthServiceI.Iface xmAuthService;
    @Resource
    private DxTokenCache dxTokenCache;

    public String getToken() {
        Optional<String> token = dxTokenCache.getToken();
        if (token.isPresent()) {
            return token.get();
        }
        return accessToken();
    }

    public String accessToken() {
        String appId;
        String appSecret;
        try {
            appId = Kms.getByName(APP_NAME, "fulfillment.robot.app.id");
            appSecret = Kms.getByName(APP_NAME, "fulfillment.robot.app.secret");
        } catch (KmsResultNullException e) {
            log.error("获取大象kms信息失败", e);
            throw new BizException("获取大象kms信息失败");
        }

        AppAuthInfo appAuthInfo = new AppAuthInfo();
        appAuthInfo.setAppkey(appId);
        appAuthInfo.setAppSecret(appSecret);

        try {
            AccessTokenResp resp = xmAuthService.accessToken(appAuthInfo);
            if (resp.status.getCode() == 0) {
                String accessToken = resp.getAccessToken().getToken();
                dxTokenCache.saveToken(accessToken);
                return accessToken;
            } else {
                log.error("获取大象Token失败, resp={}", JSON.toJSONString(resp));
                throw new BizException("获取大象Token失败");
            }
        } catch (TException e) {
            log.error("获取accessToken异常, e={}, appAuthInfo={}", e, JSON.toJSONString(appAuthInfo));
            throw new BizException("获取大象Token失败");
        }
    }

}
