package com.sankuai.shangou.logistics.delivery.indicator.service.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrderWithCurrentRider;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryDeliveryOrderByPoiAndStatusListRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.QueryDeliveryOrderWithCurrentRiderResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-03
 * @email <EMAIL>
 */
@Slf4j
@Service
@Rhino
public class RiderQueryServiceWrapper {

    @Resource
    private RiderQueryThriftService riderQueryThriftService;

    public static final int MAX_QUERY_SIZE = 5;

    @Degrade(rhinoKey = "RiderQueryServiceWrapper-queryDeliveryOrderByPoiAndStatusList",
            fallBackMethod = "queryDeliveryOrderByPoiAndStatusListFallback",
            timeoutInMilliseconds = 3000)
    public List<TRiderDeliveryOrderWithCurrentRider> queryDeliveryOrderByPoiAndStatusList(long tenantId, List<Long> poiIds, List<Integer> deliveryStatusList) {
        QueryDeliveryOrderByPoiAndStatusListRequest request = new QueryDeliveryOrderByPoiAndStatusListRequest();
        request.setTenantId(tenantId);
        request.setStoreIdList(poiIds);
        request.setDeliveryStatusList(deliveryStatusList);
        QueryDeliveryOrderWithCurrentRiderResponse response = riderQueryThriftService.queryDeliveryOrderByPoiAndStatusList(request);
        log.info("riderQueryThriftService.queryDeliveryOrderByPoiAndStatusList result = {}", response);
        if (response.getStatus().getCode() != Status.SUCCESS.getCode()) {
            throw new SystemException("riderQueryThriftService.queryDeliveryOrderByPoiAndStatusList return error");
        }

        return response.getTRiderDeliveryOrders();
    }

    private List<TRiderDeliveryOrderWithCurrentRider> queryDeliveryOrderByPoiAndStatusListFallback(long tenantId, List<Long> poiIds, List<Integer> deliveryStatusList) {
        throw new ThirdPartyException("riderQueryThriftService.queryDeliveryOrderByPoiAndStatusList已降级");
    }

}
