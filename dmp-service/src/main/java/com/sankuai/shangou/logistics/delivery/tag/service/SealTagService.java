package com.sankuai.shangou.logistics.delivery.tag.service;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.goodscenter.dto.GoodsCategoryDto;
import com.meituan.shangou.goodscenter.dto.GoodsSkuRelationDto;
import com.meituan.shangou.goodscenter.dto.TenantGoodsDetailDto;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.api.tag.ProductTagDTO;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.tag.SystemMarkingDataDto;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.tag.SystemUnmarkingDataDto;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.tag.entity.ChannelSpuKey;
import com.sankuai.shangou.logistics.delivery.tag.entity.SealRule;
import com.sankuai.shangou.logistics.delivery.tag.entity.SkuGoodsKey;
import com.sankuai.shangou.logistics.delivery.tag.wrapper.GoodsServiceWrapper;
import com.sankuai.shangou.logistics.delivery.tag.wrapper.ProductTagServiceWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-04-28
 * @email <EMAIL>
 */
@Slf4j
@Service
public class SealTagService {
    @Resource
    private GoodsServiceWrapper goodsServiceWrapper;
    @Resource
    private ProductTagServiceWrapper productTagServiceWrapper;

    private static final String SEAL_TAG_CODE = "SEAL_GOODS";
    private static final String NEED_SEAL_TAG_VALUE_CODE = "SEAL_TRUE";
    private static final String NO_SEAL_TAG_VALUE_CODE = "SEAL_FALSE";
    private static final int TAG_STORE_DIM_TYPE = 30;

    @MethodLog(logRequest = true, logResponse = true)
    public void syncSealTag(long tenantId, Long storeId, List<ChannelSpuKey> channelSpuKeys) {
        Map<String, ChannelSpuKey.ChannelSkuKey> channelSkuMap = channelSpuKeys
                .stream()
                .filter(dto -> CollectionUtils.isNotEmpty(dto.getSkuList()))
                .flatMap(dto -> dto.getSkuList().stream())
                .filter(channelSkuDTO -> StringUtils.isNotBlank(channelSkuDTO.getSkuId()))
                .filter(channelSkuDTO -> Objects.nonNull(channelSkuDTO.getRetailPrice()))
                .collect(Collectors.toMap(
                        ChannelSpuKey.ChannelSkuKey::getSkuId,
                        it -> it,
                        (older, newer) -> {
                            //产品逻辑：取售价更高的
                            if (older.getRetailPrice() > newer.getRetailPrice()) {
                                return older;
                            }
                            return newer;
                        }
                ));

        //NOTE：已和@liuwenfei确认:一个spu可能有多个sku，但一个sku只会属于一个spu
        //2.查商货关系
        List<String> skuIds = channelSpuKeys
                .stream()
                .filter(dto -> CollectionUtils.isNotEmpty(dto.getSkuList()))
                .flatMap(dto -> dto.getSkuList().stream())
                .filter(channelSkuDTO -> StringUtils.isNotBlank(channelSkuDTO.getSkuId()))
                .map(ChannelSpuKey.ChannelSkuKey::getSkuId)
                .distinct()
                .collect(Collectors.toList());
        List<GoodsSkuRelationDto> goodsSkuRelationDtos = goodsServiceWrapper.queryRelationBySkuIds(tenantId, skuIds);
        if (CollectionUtils.isEmpty(goodsSkuRelationDtos)) {
            return;
        }
        //一个sku可能由多个good组成
        Map<String, List<GoodsSkuRelationDto>> skuRelationMap = IListUtils.nullSafeGroupBy(goodsSkuRelationDtos, GoodsSkuRelationDto::getSkuId);

        List<SkuGoodsKey> skuGoodsKeyList = skuRelationMap.entrySet()
                .stream()
                //取由一个good组成
                .filter(entry -> CollectionUtils.isNotEmpty(entry.getValue()) && Objects.equals(entry.getValue().size(), 1))
                //取good的数量是1
                .filter(entry -> new BigDecimal(entry.getValue().get(0).getAmount()).compareTo(new BigDecimal("1")) == 0)
                //排除下没查出来关系的
                .filter(entry -> channelSkuMap.containsKey(entry.getKey()))
                .map(entry -> {
                    ChannelSpuKey.ChannelSkuKey channelSkuKey = channelSkuMap.get(entry.getKey());
                    return new SkuGoodsKey(channelSkuKey.getSkuId(), convertPriceFromLong(channelSkuKey.getRetailPrice()), entry.getValue().get(0).getGoodsId());
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuGoodsKeyList)) {
            return;
        }

        //过滤逻辑: 一个基础商品规格为1的销售商品sku可能有多个。按产品逻辑取售价最高的
        skuGoodsKeyList = new ArrayList<>(skuGoodsKeyList
                .stream()
                .collect(Collectors.toMap(
                        SkuGoodsKey::getGoodsId,
                        Function.identity(),
                        (key1, key2) -> {
                            if (key1.getRetailPrice().compareTo(key2.getRetailPrice()) > 0) {
                                return key1;
                            } else {
                                return key2;
                            }
                        }
                )).values());
        if (CollectionUtils.isEmpty(skuGoodsKeyList)) {
            return;
        }

        //4.查类目，拼规则
        List<String> goodsIdList = IListUtils.mapTo(skuGoodsKeyList, SkuGoodsKey::getGoodsId);
        List<DepotGoodsDetailDto> depotGoodsDetailDtos = goodsServiceWrapper.batchQueryDepotGoodsListByGoodsId(tenantId, storeId, goodsIdList);
        Map<String, DepotGoodsDetailDto> goodsIdGoodsDetailDtoMap = IListUtils.nullSafeAndOverrideCollectToMap(depotGoodsDetailDtos, DepotGoodsDetailDto::getGoodsId, Function.identity());

        //过滤规则2：（线上不会存在，线下脏数据）门店的sku对应的goods被删了
        skuGoodsKeyList = skuGoodsKeyList
                .stream()
                .filter(skuGoodsKey -> goodsIdGoodsDetailDtoMap.containsKey(skuGoodsKey.getGoodsId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuGoodsKeyList)) {
            return;
        }

        //5.开始判断标记,哪些需要打标
        judgeSealTag(skuGoodsKeyList, goodsIdGoodsDetailDtoMap);

        //6.开始打标
        doSyncSealTag(tenantId, storeId, skuGoodsKeyList, goodsIdList);
    }

    private void judgeSealTag(List<SkuGoodsKey> skuGoodsKeyList, Map<String, DepotGoodsDetailDto> goodsIdGoodsDetailDtoMap) {
        List<SealRule> sealRuleList = getSealRuleList();
        for (SkuGoodsKey skuGoodsKey : skuGoodsKeyList) {
            if (!goodsIdGoodsDetailDtoMap.containsKey(skuGoodsKey.getGoodsId())) {
                log.error("门店货品不存在，goodsId = {}", skuGoodsKey.getGoodsId());
                continue;
            }
            GoodsCategoryDto goodsCategoryDto = goodsIdGoodsDetailDtoMap.get(skuGoodsKey.getGoodsId()).getCategoryInfo();
            if (Objects.isNull(goodsCategoryDto) || CollectionUtils.isEmpty(goodsCategoryDto.getNamePathList())) {
                continue;
            }

            //先找到有当前货品的
            List<SealRule> validRuleList = sealRuleList
                    .stream()
                    .filter(saleRule -> {
                        //从最低级类目往前做匹配，要求全路径一致.
                        boolean isValidRule = false;
                        for (int i = goodsCategoryDto.getNamePathList().size() - 1; i >= 0; i--) {
                            List<String> subNamePath = goodsCategoryDto.getNamePathList().subList(0, i + 1);
                            if (ListUtils.isEqualList(subNamePath, saleRule.getCategoryNamePath())) {
                                isValidRule = true;
                            }
                        }
                        return isValidRule;
                    })
                    .collect(Collectors.toList());
            log.info("货品{} 品类{}匹配到saleRule = {}", skuGoodsKey.getGoodsId(), goodsCategoryDto.getNamePathList(), validRuleList);
            if (CollectionUtils.isEmpty(validRuleList)) {
                continue;
            }

            boolean needSealDelivery = false;
            for (SealRule sealRule : validRuleList) {
                if (skuGoodsKey.getRetailPrice().compareTo(sealRule.getSealFloorPrice()) >= 0) {
                    needSealDelivery = true;
                    break;
                }
            }
            skuGoodsKey.setNeedSealDelivery(needSealDelivery);
        }
    }

    private void doSyncSealTag(long tenantId, Long storeId, List<SkuGoodsKey> skuGoodsKeyList, List<String> goodsIdList) {
        //注意商品侧是没有做标签的互斥逻辑的。需要业务侧自己保证
        List<ProductTagDTO> productTagDTOS = productTagServiceWrapper.batchQuerySealTagRelationList(tenantId, storeId, SEAL_TAG_CODE, goodsIdList);
        Map<String, List<ProductTagDTO>> goodsIdTagMap = IListUtils.nullSafeGroupBy(productTagDTOS, ProductTagDTO::getProductId);

        List<SkuGoodsKey> needChangeSkuGoodsKeys = skuGoodsKeyList
                .stream()
                //先去除标记和当前标记相同的
                .filter(skuGoodsKey -> {
                    List<ProductTagDTO> existTagList = goodsIdTagMap.get(skuGoodsKey.getGoodsId());
                    //只有一个tag且tag和要打的tag相同,就不打标了
                    if (CollectionUtils.isNotEmpty(existTagList) && existTagList.size() == 1) {
                        boolean b = convertTagValueCodeToBool(existTagList.get(0).getTagValueCode());
                        return !Objects.equals(b, skuGoodsKey.getNeedSealDelivery());
                    }
                    return true;
                }).peek(skuGoodsKey -> {
                            List<ProductTagDTO> existTagList = goodsIdTagMap.get(skuGoodsKey.getGoodsId());
                            skuGoodsKey.setNeedRemoveTags(existTagList);
                        }
                ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needChangeSkuGoodsKeys)) {
            return;
        }

        //先解绑需要解绑的
        List<SystemUnmarkingDataDto> systemUnmarkingDataDtos = needChangeSkuGoodsKeys
                .stream()
                .filter(skuGoodsKey -> CollectionUtils.isNotEmpty(skuGoodsKey.getNeedRemoveTags()))
                .map(skuGoodsKey -> {
                    List<ProductTagDTO> needRemoveTags = skuGoodsKey.getNeedRemoveTags();

                    SystemUnmarkingDataDto systemUnmarkingDataDto = new SystemUnmarkingDataDto();
                    systemUnmarkingDataDto.setProductId(skuGoodsKey.getGoodsId());
                    systemUnmarkingDataDto.setTagValueCodeList(needRemoveTags.stream().map(ProductTagDTO::getTagValueCode).collect(Collectors.toList()));
                    systemUnmarkingDataDto.setDimType(TAG_STORE_DIM_TYPE);
                    systemUnmarkingDataDto.setDimId(String.valueOf(storeId));

                    return systemUnmarkingDataDto;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(systemUnmarkingDataDtos)) {
            productTagServiceWrapper.systemBatchUnmark(tenantId, SEAL_TAG_CODE, systemUnmarkingDataDtos);
        }

        //绑定新的
        List<SystemMarkingDataDto> systemMarkingDataDtos = needChangeSkuGoodsKeys
                .stream()
                .map(skuGoodsKey -> {
                    SystemMarkingDataDto systemMarkingDataDto = new SystemMarkingDataDto();
                    systemMarkingDataDto.setProductId(skuGoodsKey.getGoodsId());
                    systemMarkingDataDto.setTagValueCodeList(Lists.newArrayList(convertTagValueCodeFromBool(skuGoodsKey.getNeedSealDelivery())));
                    systemMarkingDataDto.setDimType(TAG_STORE_DIM_TYPE);
                    systemMarkingDataDto.setDimId(String.valueOf(storeId));

                    return systemMarkingDataDto;
                }).collect(Collectors.toList());
        productTagServiceWrapper.systemBatchMark(tenantId, SEAL_TAG_CODE, systemMarkingDataDtos);
    }

    private BigDecimal convertPriceFromLong(long centPrice) {
        return new BigDecimal(centPrice).divide(new BigDecimal(100), 2, RoundingMode.CEILING);
    }


    private List<SealRule> getSealRuleList() {
        return Lion.getConfigRepository().getList("seal.rule.config", SealRule.class, Lists.newArrayList());
    }

    private boolean convertTagValueCodeToBool(String tagValueCode) {
        if (Objects.equals(tagValueCode, NEED_SEAL_TAG_VALUE_CODE)) {
            return true;
        }
        if (Objects.equals(tagValueCode, NO_SEAL_TAG_VALUE_CODE)) {
            return false;
        }
        throw new SystemException("不正确的tagValueCode，tagValueCode = " + tagValueCode);
    }

    private String convertTagValueCodeFromBool(boolean needSealDelivery) {
        if (needSealDelivery) {
            return NEED_SEAL_TAG_VALUE_CODE;
        } else {
            return NO_SEAL_TAG_VALUE_CODE;
        }
    }

}
