package com.sankuai.shangou.logistics.delivery.indicator.service.calculate.biz.impl;

import com.sankuai.shangou.logistics.delivery.indicator.service.calculate.biz.AbstractBizIndicatorCalculator;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BaseIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BizIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/7/24 19:53
 **/
@Slf4j
@Component
public class BizWaitAcceptOrderCountCalculator extends AbstractBizIndicatorCalculator {
    @Override
    public List<BaseIndicatorEnum> getRelateBaseIndicators() {
        return Collections.singletonList(BaseIndicatorEnum.WAIT_ACCEPT_ORDER_COUNT);
    }

    @Override
    public BizIndicatorEnum getSupportIndicator() {
        return BizIndicatorEnum.WAIT_ACCEPT_ORDER_COUNT;
    }

    @Override
    public List<IndicatorDTO> calc(Map<String, List<IndicatorDTO>> baseIndicatorMap) {
        //如果基础指标数据不合法 直接返回空
        if (!checkBaseIndicatorDataIsValid(baseIndicatorMap)) {
            log.error("基础指标数据不全或不合法, baseIndicatorMap: {}, bizIndicatorEnum: {}", baseIndicatorMap, getSupportIndicator());
            return Collections.emptyList();
        }

        IndicatorDTO baseIndicator = baseIndicatorMap.get(BaseIndicatorEnum.WAIT_ACCEPT_ORDER_COUNT.getIndicatorCode()).get(0);
        IndicatorDTO bizIndicator = IndicatorDTO.builder()
                .logisticsUnitId(baseIndicator.getLogisticsUnitId())
                .code(getSupportIndicator().getIndicatorCode())
                .bizTime(baseIndicator.getBizTime())
                .calculateTime(baseIndicator.getCalculateTime())
                .value(baseIndicator.getValue())
                .fragmentName(baseIndicator.getFragmentName())
                .property(baseIndicator.getProperty())
                .build();

        return Collections.singletonList(bizIndicator);
    }
}
