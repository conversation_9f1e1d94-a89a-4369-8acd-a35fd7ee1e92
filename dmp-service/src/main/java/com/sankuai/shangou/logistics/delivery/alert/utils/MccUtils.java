package com.sankuai.shangou.logistics.delivery.alert.utils;

import com.dianping.lion.client.Lion;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-05-16
 */
public class MccUtils {
    private MccUtils() {}

    /**
     * 执行
     */
    public static List<String> getExecuteMisList() {
        return Lion.getConfigRepository().getList("delivery.compensate.mis.List", String.class, Collections.emptyList());
    }

    public static Long getGid() {
        return Lion.getConfigRepository().getLongValue("delivery.compensate.execute.gid", 0L);
    }

    public static Long getEleOrderStaticGid() {
        return Lion.getConfigRepository().getLongValue("ele.order.stat.gid", 0L);
    }

    public static Long getAllOrderStaticGid() {
        return Lion.getConfigRepository().getLongValue("all.order.stat.gid", 0L);
    }

    public static Long getDeliveryFailDxCardTemplateId() {
        return Lion.getConfigRepository().getLongValue("delivery.fail.dx.card.id", 0L);
    }

    public static Integer getLaunchFailBeforeHours() {
        return Lion.getConfigRepository().getIntValue("launch.fail.before.hours", 1);
    }
}
