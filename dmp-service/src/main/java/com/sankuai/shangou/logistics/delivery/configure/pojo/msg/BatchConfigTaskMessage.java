package com.sankuai.shangou.logistics.delivery.configure.pojo.msg;

import com.sankuai.shangou.logistics.delivery.configure.enums.BatchTaskTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-01
 */
@Data
public class BatchConfigTaskMessage implements Serializable {

    /**
     * 任务类型
     */
    private BatchTaskTypeEnum batchTaskType;
    /**
     * 批量任务id
     */
    private Long configTaskId;
}
