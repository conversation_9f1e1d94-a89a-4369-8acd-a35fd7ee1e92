package com.sankuai.shangou.logistics.delivery.shippingarea.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/10/9 11:09
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShippingAreaUpdateMessage {
    private Long tenantId;

    private Long storeId;

    /**
     * 1-新增
     * 2-更新
     * 3-删除
     * 4-重置
     */
    private int operationType;
}
