package com.sankuai.shangou.logistics.delivery.offlineboard;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Joiner;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.sankuai.meituan.shangou.empower.auth.sdk.utils.JacksonUtils;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.commons.thrift.publisher.response.PaginationList;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.config.LionConfigUtils;
import com.sankuai.shangou.logistics.delivery.offlineboard.clinet.OfflinePowerApiFacade;
import com.sankuai.shangou.logistics.delivery.offlineboard.converter.OfflineFulfillDataConverter;
import com.sankuai.shangou.logistics.delivery.offlineboard.define.BaseIndicatorDefine;
import com.sankuai.shangou.logistics.delivery.offlineboard.define.DivideFunction;
import com.sankuai.shangou.logistics.delivery.offlineboard.define.IndicatorCalculatorDefine;
import com.sankuai.shangou.logistics.delivery.offlineboard.enums.*;
import com.sankuai.shangou.logistics.delivery.offlineboard.request.OfflineDataQueryBaseRequest;
import com.sankuai.shangou.logistics.delivery.offlineboard.request.OfflineSummaryFulfillDataQueryRequest;
import com.sankuai.shangou.logistics.delivery.offlineboard.response.OfflineSummaryFulfillDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024-06-04
 * @email <EMAIL>
 */
@Slf4j
@Service
public class OfflineBoardService {

    @Resource
    private OfflinePowerApiFacade offlinePowerApiFacade;

    /**
     * 查总览数据
     * @param request OfflineSummaryFulfillDataQueryRequest
     * @return OfflineSummaryFulfillDataVO
     */
    public OfflineSummaryFulfillDataDTO queryOfflineSummaryFulfillData(OfflineSummaryFulfillDataQueryRequest request) {
        Map<String, String> paramMap = toParamMap(request.getStoreIds(), request.getStartDate(), request.getEndDate());

        List<OfflineFulfillSummaryData> fulfillSummaryDataList =
                offlinePowerApiFacade.query(PowerApiApiCodeEnum.FULFILL_OFFLINE_OVERVIEW, paramMap, OfflineFulfillSummaryData.class);

        if (CollectionUtils.isEmpty(fulfillSummaryDataList)) {
            return OfflineSummaryFulfillDataDTO.createDefaultDataVO();
        }

        return OfflineFulfillDataConverter.toDTO(fulfillSummaryDataList.get(0));
    }

    private Map<String, String> toParamMap(List<Long> storeIds, Long beginDate, Long endDate) {
        HashMap<String, String> param = new HashMap<>();
        param.put("poiIds", Joiner.on(",").join(storeIds));
        param.put("beginDate", convertTimeStampByPattern(beginDate));
        param.put("endDate", convertTimeStampByPattern(endDate));

        return param;
    }

    @MethodLog(logRequest = true, logResponse = true)
    public <R extends OfflineDataQueryBaseRequest> PaginationList<HashMap<String, Object>> pageQueryOfflineDataTemplate(BizCalculatorEnum bizCalculatorEnum,
                                                                                                                        PowerApiApiCodeEnum powerApiApiCodeEnum,
                                                                                                                        R request) {
        //查总数
        Long count = offlinePowerApiFacade.pageCount(powerApiApiCodeEnum, toPageQueryParamMapTemplate(request, JacksonUtils.toJsonString(request)));

        //查列表
        List<Map<String, Object>> resultMapList = queryOfflineDataListConfigurableTemplate(bizCalculatorEnum, powerApiApiCodeEnum, request);

        //兼容老流程 把Map转为HashMap
        List<HashMap<String, Object>> result = new ArrayList<>();
        resultMapList.forEach(resultMap -> result.add(new HashMap<>(resultMap)));
        return new PaginationList<>(request.getPageNo(), request.getPageSize(), count, result);
    }

    private <R extends OfflineDataQueryBaseRequest> List<Map<String, Object>> queryOfflineDataListConfigurableTemplate(BizCalculatorEnum bizCalculatorEnum, PowerApiApiCodeEnum powerApiApiCodeEnum, R request) {
        Map<String, String> paramMap = toPageQueryParamMapTemplate(request, JacksonUtils.toJsonString(request));

        //读源数据
        List<Map<String, String>> sourceData = offlinePowerApiFacade.pageQuery(powerApiApiCodeEnum, paramMap, request.getPageNo(), request.getPageSize());

        if (CollectionUtils.isEmpty(sourceData)) {
            return Collections.emptyList();
        }

        //计算指标
        return calcBizIndicator(bizCalculatorEnum, sourceData);
    }


    private List<Map<String, Object>> calcBizIndicator(BizCalculatorEnum bizCalculatorEnum, List<Map<String, String>> sourceData) {
        //1.读配置
        IndicatorCalculatorDefine indicatorCalculatorDefine = LionConfigUtils.getIndicatorCalculatorDefineForExport(bizCalculatorEnum.getCalculatorName());
        List<BaseIndicatorDefine> baseIndicatorDefineList = LionConfigUtils.getBaseIndicatorDefineList();

        //2.源数据预处理
        List<Map<String, Object>> baseIndicatorList = preProcessSourceData(sourceData, baseIndicatorDefineList);

        AviatorEvaluatorInstance evaluatorInstance = AviatorEvaluator.newInstance();

        //3.添加自定义函数
        evaluatorInstance.addFunction(new DivideFunction());

        //4.计算
        List<Map<String, Object>> resultTable = new ArrayList<>();

        baseIndicatorList.forEach(
                baseIndicatorRow -> {
                    HashMap<String, Object> resultRow = new HashMap<>();
                    indicatorCalculatorDefine.getBizIndicatorDefineList().forEach(
                            bizIndicatorDefine -> {
                                // 使用自定义函数执行表达式
                                try {
                                    Object originalResult = evaluatorInstance.execute(bizIndicatorDefine.getExpress(), baseIndicatorRow);
                                    resultRow.put(bizIndicatorDefine.getName(), BizIndicatorType.formatValue(originalResult, bizIndicatorDefine.getType()));

                                } catch (Exception e) {
                                    log.error("计算指标失败, express: {}, baseIndicatorRow: {}", bizIndicatorDefine.getExpress(), baseIndicatorRow, e);
                                    throw e;
                                }
                            }
                    );

                    resultTable.add(resultRow);
                }

        );

        return resultTable;
    }

    private List<Map<String, Object>> preProcessSourceData(List<Map<String, String>> sourceData, List<BaseIndicatorDefine> baseIndicatorDefineList) {
        List<Map<String, Object>> baseIndicatorList = new ArrayList<>();
        sourceData.forEach(
                sourceDataRow -> {
                    Map<String, Object> baseIndicatorRow = new HashMap<>();
                    baseIndicatorDefineList.forEach(baseIndicatorDefine -> {
                        Object baseIndicator = BaseIndicatorType.formatValue(sourceDataRow.get(baseIndicatorDefine.getSourceIndicatorName()), baseIndicatorDefine.getType());
                        baseIndicatorRow.put(baseIndicatorDefine.getName(), baseIndicator);
                    });
                    baseIndicatorList.add(baseIndicatorRow);
                }
        );

        return baseIndicatorList;
    }


    private <R extends OfflineDataQueryBaseRequest>  Map<String, String> toPageQueryParamMapTemplate(R request, String requestJson) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            //老字段，保持不变
            JsonNode jsonNode = mapper.readTree(requestJson);
            HashMap<String, String> param = new HashMap<>();
            param.put("poiIds", Joiner.on(",").join(request.getStoreIds()));
            param.put("beginDate", convertTimeStampByPattern(request.getStartDate()));
            param.put("endDate", convertTimeStampByPattern(request.getEndDate()));


            //新增字段聚合字段
            if (Objects.nonNull(jsonNode.get("groupColumn"))) {
                param.put("groupColumn", jsonNode.get("groupColumn").textValue());
                param.put("groupColumnName", jsonNode.get("groupColumnName").textValue());
            }

            OfflineFulfillDataSortFieldEnum sortFieldEnum = OfflineFulfillDataSortFieldEnum.enumOf(request.getSortField());
            //排序字段解析，没配就按原始的来
            StringBuilder sortColumnBuilder = new StringBuilder();
            if (Objects.nonNull(sortFieldEnum)) {
                sortColumnBuilder.append(sortFieldEnum.getSortRule());
            } else {
                sortColumnBuilder.append(request.getSortField());
            }

            sortColumnBuilder
                    .append(StringUtils.SPACE)
                    .append(Objects.requireNonNull(SortTypeEnum.enumOf(request.getSortType())).getSqlStr());
            //后端默认排序
            if (CollectionUtils.isNotEmpty(request.getSortRuleList())) {
                for (OfflineDataQueryBaseRequest.SortRule sortRule : request.getSortRuleList()) {
                    sortColumnBuilder.append(",");
                    sortColumnBuilder
                            .append(sortRule.getSortField())
                            .append(StringUtils.SPACE)
                            .append(Objects.requireNonNull(SortTypeEnum.enumOf(sortRule.getSortType())).getSqlStr());
                }
            }

            if (CollectionUtils.isNotEmpty(request.getOperationModes())) {
                if (CollectionUtils.isNotEmpty(request.getOperationModes())) {
                    // 1-直营前置仓 2-加盟前置仓
                    if (request.getOperationModes().contains(1) && request.getOperationModes().contains(2)) {
                        param.put("poi_type_id", "3");
                    } else if (request.getOperationModes().contains(1)) {
                        param.put("poi_type_id", "1");
                    } else if (request.getOperationModes().contains(2)) {
                        param.put("poi_type_id", "2");
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(request.getAbnormalTypes())) {
                request.getAbnormalTypes().forEach(abnormalType -> {
                    param.put(abnormalType, abnormalType);
                });
            }

            param.put("columnSort", sortColumnBuilder.toString());
            return param;
        } catch (Exception e) {
            log.error("请求参数解析失败", e);
            throw new BizException("请求参数解析失败");
        }
    }

    private static String convertTimeStampByPattern(Long timestamps){
        if (timestamps == null){
            return StringUtils.EMPTY;
        }
        return DateFormatUtils.format(timestamps, "yyyyMMdd");
    }
}
