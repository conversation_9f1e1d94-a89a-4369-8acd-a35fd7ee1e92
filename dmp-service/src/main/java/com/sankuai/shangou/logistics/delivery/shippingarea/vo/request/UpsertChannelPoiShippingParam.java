package com.sankuai.shangou.logistics.delivery.shippingarea.vo.request;

import com.meituan.linz.boot.util.Assert;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.logistics.delivery.shippingarea.constant.Const;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.ChannelTypeEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.ShippingUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

@TypeDoc(
        description = "渠道配送范围更新参数",
        authors = {
                "daiyuan"
        }
)
@Getter
@Setter
public class UpsertChannelPoiShippingParam {
    @FieldDoc(
            description = "渠道门店id"
    )
    private Long channelPoiId;

    @FieldDoc(
            description = "渠道编号"
    )
    private Integer channelId;

    @FieldDoc(
            description = "业务方配送范围id(商家端创建的配送范围，此值为空)"
    )
    private String appShippingCode;

    @FieldDoc(
            description = "配送范围id(配送范围底层唯一键)"
    )
    private Long shippingAreaId;

    @FieldDoc(
            description = "门店配送范围"
    )
    private String deliveryRegion;

    @FieldDoc(
            description = "起送价，单位：元(人民币)"
    )
    private double minPrice;

    @FieldDoc(
            description = "配送费，单位：元(人民币)"
    )
    private double shippingFee;

    @FieldDoc(
            description = "门店配送面积，单位：平方公里"
    )
    private String oldDeliveryArea = "0";

    @FieldDoc(
            description = "门店配送面积，单位：平方公里"
    )
    private String newDeliveryArea = "0";

    @FieldDoc(
            description = "范围标签列表，1：大范围（目前只支持'大范围'标签）"
    )
    private List<Integer> shippingTagList;

    @FieldDoc(
            description = "配送范围生效时段-开始时间 例如00:00 更新正常时段配送范围时不传"
    )
    private String startTime;

    @FieldDoc(
            description = "配送范围生效时段-结束时间 例如24:00 更新正常时段配送范围时不传"
    )
    private String endTime;


    public void validate() {
        Assert.throwIfTrue(channelId == null || channelId <= 0, "错误的渠道id");
        ChannelTypeEnum.enumOf(channelId);
        Assert.throwIfTrue(StringUtils.isBlank(deliveryRegion), "配送范围不能为空");

        BigDecimal minPriceDecimal = BigDecimal.valueOf(minPrice);
        BigDecimal shippingFeeDecimal = BigDecimal.valueOf(shippingFee);
        Assert.throwIfTrue(minPrice < 0 || minPriceDecimal.scale() > 2 || minPriceDecimal.compareTo(Const.MAX_SHIPPING_FEE) > 0,
                "错误的起送价");
        Assert.throwIfTrue(shippingFee < 0 || shippingFeeDecimal.scale() > 2 || shippingFeeDecimal.compareTo(Const.MAX_SHIPPING_FEE) > 0
                , "错误的配送费");

        if (CollectionUtils.isNotEmpty(shippingTagList)) {
            shippingTagList.forEach(ShippingUtils::codeOfShippingTag);
        }
    }
}
