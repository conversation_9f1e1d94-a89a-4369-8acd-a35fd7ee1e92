package com.sankuai.shangou.logistics.delivery.indicator.service.calculate.base.impl;

import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ShiftAndScheduleCountDTO;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BaseIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.repository.dbo.IndicatorDoc;
import com.sankuai.shangou.logistics.delivery.indicator.service.calculate.base.AbstractBaseIndicatorCalculator;
import com.sankuai.shangou.logistics.delivery.indicator.utils.IndicatorSquirrelKeyUtils;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrderWithCurrentRider;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-07-03
 * @email <EMAIL>
 */
@Slf4j
@Service
public class ScheduledWaitPickOrderCountCalculator extends AbstractBaseIndicatorCalculator {

    @Override
    public BaseIndicatorEnum getSupportIndicator() {
        return BaseIndicatorEnum.WAIT_PICK_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT;
    }

    @Override
    public List<IndicatorDoc> calc(LocalDateTime fetchTime, List<IndicatorSquirrelKeyUtils.IndicatorStoreKey> indicatorStoreKeys,
                                   List<TRiderDeliveryOrderWithCurrentRider> thisDeliveryOrders,
                                   Map<Long, ShiftAndScheduleCountDTO> idShiftDTOMap,
                                   Map<Long, Long> employeeAccountIdMap, Map<Long, List<ShiftAndScheduleCountDTO>> poiIdAndShiftDTOListMap) {
        List<Long> scheduledAccountIds = getScheduledAccountIds(indicatorStoreKeys, employeeAccountIdMap);


        long scheduledWaitPickOrderCount = IListUtils.nullSafeStream(thisDeliveryOrders)
                .filter(deliveryOrder -> Objects.equals(deliveryOrder.getDeliveryStatus(), DeliveryStatusEnum.RIDER_ASSIGNED.getCode()))
                .filter(deliveryOrder -> Objects.nonNull(deliveryOrder.getCurrentRider()))
                .filter(deliveryOrder -> scheduledAccountIds.contains(deliveryOrder.getCurrentRider().getAccountId()))
                .count();

        return buildSingIndicaotrGroupIndicatorDocList(indicatorStoreKeys.get(0).getWarehouseId(), new BigDecimal(scheduledWaitPickOrderCount));
    }
}
