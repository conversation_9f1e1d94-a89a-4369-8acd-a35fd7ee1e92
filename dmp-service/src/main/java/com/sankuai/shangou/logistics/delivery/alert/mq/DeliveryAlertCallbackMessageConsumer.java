package com.sankuai.shangou.logistics.delivery.alert.mq;

import com.alibaba.fastjson.JSON;
import com.meituan.linz.boot.util.Assert;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.shangou.logistics.delivery.alert.sevice.DeliveryAlertCallbackService;
import com.sankuai.shangou.logistics.delivery.alert.enums.DeliveryAlertCallbackEnum;
import com.sankuai.shangou.logistics.delivery.alert.mq.msg.DeliveryAlertCallbackMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 配送告警回调消息消费
 * @date 2025-05-15
 */
@Slf4j
@Component
@MafkaConsumer(namespace = "com.sankuai.mafka.castle.daojiacommon",
        topic = "delivery_alert_callback_message_topic",
        group = "delivery_alert_callback_message_consumer",
        deadLetter = true)
public class DeliveryAlertCallbackMessageConsumer implements IMessageListener {

    @Resource
    private DeliveryAlertCallbackService deliveryAlertCallbackService;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext messagetContext) {
        try {
            log.info("开始消费配送告警回调消息: {}", message);
            String body = (String) message.getBody();
            if (StringUtils.isEmpty(body)) {
                log.info("messageBody is empty");
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            Optional<DeliveryAlertCallbackMessage> messageOptional = translate(body);
            if (!messageOptional.isPresent()) {
                log.info("messageObj is empty");
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            DeliveryAlertCallbackMessage callbackMessage = messageOptional.get();
            DeliveryAlertCallbackEnum callbackEnum = DeliveryAlertCallbackEnum.ofType(callbackMessage.getType());
            if (callbackEnum == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            switch (callbackEnum) {
                case DELIVERY_LAUNCH_FAILED:
                    deliveryAlertCallbackService.launchFailHandle();
                    break;
                default:
                    break;
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("消费配送告警回调消息失败", e);
            return ConsumeStatus.RECONSUME_LATER;
        }
    }


    private Optional<DeliveryAlertCallbackMessage> translate(String msgBody) {
        try {
            DeliveryAlertCallbackMessage message = JSON.parseObject(msgBody, DeliveryAlertCallbackMessage.class);
            Assert.throwIfNull(message, "消息为空");
            return Optional.of(message);
        } catch (Exception e) {
            log.error("translate error", e);
        }
        return Optional.empty();
    }
}
