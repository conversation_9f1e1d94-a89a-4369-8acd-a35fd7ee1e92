package com.sankuai.shangou.logistics.delivery.shippingarea.utils;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.regex.Pattern;

import static java.time.temporal.ChronoField.*;

public class DateTimeUtils {

//    private static final DateTimeFormatter MONTH_DT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");
    private static final DateTimeFormatter MONTH_DT_FORMATTER =
            new DateTimeFormatterBuilder().appendPattern("yyyy-MM")
                    .parseDefaulting(DAY_OF_MONTH, 1)
                    .toFormatter();

    private static final DateTimeFormatter LOCAL_DATE_TIME_FORMATTER =
            new DateTimeFormatterBuilder()
                    .parseCaseInsensitive()
                    .append(DateTimeFormatter.ISO_LOCAL_DATE)
                    .appendLiteral(' ')
                    .appendValue(HOUR_OF_DAY, 2)
                    .appendLiteral(':')
                    .appendValue(MINUTE_OF_HOUR, 2)
                    .optionalStart()
                    .appendLiteral(':')
                    .appendValue(SECOND_OF_MINUTE, 2)
                    .toFormatter();

    public static String getLastMonthDt() {
        return LocalDateTime.now().plusMonths(-1).format(MONTH_DT_FORMATTER);
    }

    // yyyy-MM-dd
    private static final String ISO_LOCAL_DATE_REGEX =
            "[1-9][0-9]{3}-([0][1-9]|[1][0-2])-([0][1-9]|[1-2][0-9]|[3][0-1])";

    private static final String MAP_API_DATE_REGEX =
            "[1-9][0-9]{3}-([0][1-9]|[1][0-2])";

    public static boolean isIsoLocalDate(String dt) {
        if(StringUtils.isBlank(dt)){
            return false;
        }
        Pattern pattern = Pattern.compile(ISO_LOCAL_DATE_REGEX);
        return pattern.matcher(dt).matches();
    }

    public static boolean isMapApiDate(String dt){
        if(StringUtils.isBlank(dt)){
            return false;
        }
        Pattern pattern = Pattern.compile(MAP_API_DATE_REGEX);
        return pattern.matcher(dt).matches();
    }

    public static boolean isAfterMinusMonth(String dt, int months){
        if(StringUtils.isBlank(dt)){
            return false;
        }
        LocalDate dateTime = LocalDate.parse(dt, MONTH_DT_FORMATTER);
        return dateTime.isAfter(LocalDate.now().minusMonths(months));
    }

    // the ISO-8601 extended local date format: yyyy-MM-dd
    public static String fmtMinusDayIsoDt(int daysToSubtract) {
        return LocalDate.now().minusDays(daysToSubtract).format(DateTimeFormatter.ISO_LOCAL_DATE);
    }


    // yyyy-MM-dd hh:mm:ss
    public static String fmtLocalDateTime(LocalDateTime  dateTime){
        return dateTime.format(LOCAL_DATE_TIME_FORMATTER);
    }

    // yyyy-MM-dd
    public static LocalDate parseIsoLocalDateString(String dateTime){
        return LocalDate.parse(dateTime, DateTimeFormatter.ISO_LOCAL_DATE);
    }
}
