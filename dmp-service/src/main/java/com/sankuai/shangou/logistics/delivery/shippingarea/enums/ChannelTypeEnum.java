package com.sankuai.shangou.logistics.delivery.shippingarea.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2021/12/22
 */
@Getter
public enum ChannelTypeEnum {

    MEITUAN(100, "美团外卖"),
    MT_DRUNK_HORSE(700, "微商城");

    private int channelId;

    private String channelName;

    ChannelTypeEnum(int channelId, String channelName){
        this.channelId = channelId;
        this.channelName = channelName;
    }

    public static ChannelTypeEnum enumOf(int channelId) {
        for (ChannelTypeEnum channelTypeEnum : values()) {
            if (Objects.equals(channelTypeEnum.getChannelId(), channelId)) {
                return channelTypeEnum;
            }
        }

        throw new IllegalArgumentException("不识别的渠道");
    }
}
