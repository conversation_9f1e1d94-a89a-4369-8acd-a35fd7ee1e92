package com.sankuai.shangou.logistics.delivery.poi.repository;

import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.mapper.SelfDeliveryPoiConfigOpLogPOMapper;
import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigOpLogPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @email <EMAIL>
 */
@Slf4j
@Service
public class SelfDeliveryPoiConfigOpLogMapperWrapper {

    @Resource
    private SelfDeliveryPoiConfigOpLogPOMapper selfDeliveryPoiConfigOpLogPOMapper;

    @MethodLog(logRequest = true, logResponse = true)
    public void insert(SelfDeliveryPoiConfigOpLogPO selfDeliveryPoiConfigOpLogPO) {
        selfDeliveryPoiConfigOpLogPOMapper.insertSelective(selfDeliveryPoiConfigOpLogPO);
    }

}
