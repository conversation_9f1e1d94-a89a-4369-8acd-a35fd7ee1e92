package com.sankuai.shangou.logistics.delivery.tag.wrapper;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2024-05-11
 * @email <EMAIL>
 */
public class BatchRequestHelper {

    private BatchRequestHelper() {

    }

    public static  <R, Q> List<R> batchQuest(int batchSize, List<Q> queryKeyList, Function<List<Q>, List<R>> singQueryFunc) {
        if (CollectionUtils.isEmpty(queryKeyList)) {
            return Lists.newArrayList();
        }
        List<R> resList = new ArrayList<>();
        for (List<Q> thisQueryKeyList : Lists.partition(queryKeyList, batchSize)) {
            resList.addAll(Optional.ofNullable(singQueryFunc.apply(thisQueryKeyList)).orElse(Lists.newArrayList()));
        }
        return resList;
    }

}
