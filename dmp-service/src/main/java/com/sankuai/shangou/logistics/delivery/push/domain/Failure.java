package com.sankuai.shangou.logistics.delivery.push.domain;

import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class Failure {

    private final boolean needRetry;

    private final int failureCode;

    private final String failureMessage;

    public Failure(boolean needRetry, int failureCode, String failureMessage) {
        this.needRetry = needRetry;
        this.failureCode = failureCode;
        this.failureMessage = failureMessage;
    }
}
