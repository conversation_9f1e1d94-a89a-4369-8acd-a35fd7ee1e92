package com.sankuai.shangou.logistics.delivery.offlineboard.enums;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/2/16 15:47
 **/
@Data
public class OfflineFulfillSummaryData {
    /**
     * 已送达订单量（不排除一元单）
     */
    private Long arrivedOrdNumHaveOne;

    /**
     * 已送达订单量
     */
    private Long arrivedOrdNum;

    /**
     * 提前点送达订单量
     */
    private Long aheadArrivedOrdNum;

    /**
     * 送达前取消订单数
     */
    private Long notArrCancelOrdNum;

    /**
     * 考核超时订单量
     */
    private Long checkOvertimeOrdNum;

    /**
     * 配送距离【单位：米】
     */
    private Long deliveryDistance;

    /**
     * 九分位履约时长，秒
     */
    private Double ninePerformanceDuration;

    /**
     * 整单履约时长，秒
     */
    private Long performanceDuration;

    /**
     * 已送达订单量，剔除预订单
     */
    private Long arrivedOrdNoPreNum;

    /**
     * 15分钟送达订单数
     */
    private Long fifteenMinArrOrdNum;

    /**
     * 25分钟送达订单数
     */
    private Long twentyFiveMinArrOrdNum;

    /**
     * 45分钟送达订单数
     */
    private Long fortyFiveMinArrOrdNum;

    /**
     * 配送差评量
     */
    private Long deliveryNegCommentOrdNum;

    /**
     * 出仓时长，秒
     */
    private Long storehouseOutDuration;

    /**
     * 员工有订单上班天数
     */
    private Long employeeDays;
}
