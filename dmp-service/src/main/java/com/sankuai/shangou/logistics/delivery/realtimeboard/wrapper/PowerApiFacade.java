package com.sankuai.shangou.logistics.delivery.realtimeboard.wrapper;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.waimai.dws.protocol.openapi.OpenapiQueryService;
import com.sankuai.waimai.dws.protocol.structs.DWSFormatResponse;
import com.sankuai.waimai.dws.protocol.structs.DWSParam;
import com.sankuai.waimai.dws.protocol.structs.DWSResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/7/29
 **/
@Slf4j
@Service
public class PowerApiFacade {

    @Resource
    private OpenapiQueryService.Iface powerApiService;


    public DWSResponse queryFromDW(DWSParam request) {
        if (Objects.isNull(request)) {
            throw new IllegalArgumentException("参数不合法");
        }

        try {
            log.info("start invoke powerApiService.query, request: {}", JSON.toJSONString(request));
            DWSResponse queryResponse = powerApiService.query(request);
            log.info("end invoke powerApiService.query, request: {}, result: {}", JSON.toJSONString(request), JSON.toJSONString(queryResponse));
            if (queryResponse == null) {
                throw new ThirdPartyException("调用数仓查询接口失败");
            }

            if (queryResponse.getCode() != 0 || Objects.isNull(queryResponse.getData())) {
                throw new BizException(StringUtils.isBlank(queryResponse.getMsg()) ? "调用数仓查询接口失败" : queryResponse.getMsg());
            }

            return queryResponse;
        } catch (Exception e) {
            log.error("invoke dataQueryThriftService.query error, request:{}", request, e);
            throw new ThirdPartyException("调用数仓查询接口失败");
        }

    }

    public Long queryCount(DWSParam request) {
        try {
            log.info("start invoke powerApiService.queryCount, request: {}", JSON.toJSONString(request));
            DWSFormatResponse queryResponse = powerApiService.queryCount(request);
            log.info("end invoke powerApiService.queryCount, request: {}, result: {}", JSON.toJSONString(request), JSON.toJSONString(queryResponse));
            if (queryResponse == null) {
                throw new ThirdPartyException("调用数仓查询接口失败");
            }

            if (queryResponse.getCode() != 0 || Objects.isNull(queryResponse.getData())) {
                throw new BizException(StringUtils.isBlank(queryResponse.getMsg()) ? "调用数仓查询接口失败" : queryResponse.getMsg());
            }
            return Long.valueOf(queryResponse.getData());
        } catch (Exception e) {
            log.error("invoke dataQueryThriftService.query error, request:{}", request, e);
            throw new ThirdPartyException("调用数仓查询接口失败");
        }
    }
}
