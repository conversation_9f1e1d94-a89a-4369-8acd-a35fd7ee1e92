package com.sankuai.shangou.logistics.delivery.offlineboard.define;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/3/11 15:27
 **/
@Slf4j
public class DivideFunction extends AbstractFunction {
    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2, AviatorObject arg3) {
        if(Objects.isNull(arg1.getValue(env)) || Objects.isNull(arg2.getValue(env))) {
            return null;
        }

        BigDecimal a = new BigDecimal(arg1.getValue(env).toString());
        BigDecimal b = new BigDecimal(arg2.getValue(env).toString());
        int scale = ((Long) arg3.getValue(env)).intValue();

        if (BigDecimal.ZERO.compareTo(b) == 0) {
            log.warn("divide zero");
            return null;
        }
        return AviatorDecimal.valueOf(a.divide(b, scale, BigDecimal.ROUND_HALF_UP));
    }

    @Override
    public String getName() {
        return "divide";
    }
}
