package com.sankuai.shangou.logistics.delivery.tag;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.channelspu.ChannelSkuDTO;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.channelspu.ChannelSpuPageQueryDTO;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.delivery.tag.entity.ChannelSpuChangeMessage;
import com.sankuai.shangou.logistics.delivery.tag.entity.ChannelSpuKey;
import com.sankuai.shangou.logistics.delivery.tag.service.SealTagService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-04-28
 * @email <EMAIL>
 */
@Slf4j
@Service
public class ChannelSpuChangeConsumer {

    @Resource
    private SealTagService sealTagService;

    private static final String PRICE_DIFF_FILED_KEY = "retailPrice";
    private static final String INSERT_OPERATION_TYPE = "insert";
    private static final String UPDATE_OPERATION_TYPE = "update";

    @MafkaConsumer(namespace = "waimai",
            topic = "xsupply_channel_product_change_notify_topic",
            group = "xsupply_channel_product_change_notify_dmp_consumer")
    public boolean consume(String messageBody) {
        Optional<ChannelSpuChangeMessage> channelSpuChangeMessageOpt = translateMessage(messageBody);
        if (!channelSpuChangeMessageOpt.isPresent()) {
            //消息格式就错了，重试也没用。
            return true;
        }
        ChannelSpuChangeMessage channelSpuChangeMessage = channelSpuChangeMessageOpt.get();
        log.info("consume xsupply_channel_product_change_notify_dmp_consumer, msg = {}", channelSpuChangeMessage);
        if (Lion.getConfigRepository().getBooleanValue("ignore.product.change.notify",true)) {
            return true;
        }

        //优先过滤非价格变更 && 且是sku变更
        if (StringUtils.isNotBlank(channelSpuChangeMessage.getSkuId()) &&
                CollectionUtils.isNotEmpty(channelSpuChangeMessage.getDiffFields())
                && channelSpuChangeMessage.getDiffFields().contains(PRICE_DIFF_FILED_KEY)) {
            //新商品需要打标
            boolean isNewChannelSpu = Objects.equals(channelSpuChangeMessage.getOperationType(), INSERT_OPERATION_TYPE);
            //产品逻辑：变更的情况下，只有涨价商品需要重新打标
            boolean isPriceIncreaseUpdate =
                    Objects.equals(channelSpuChangeMessage.getOperationType(), UPDATE_OPERATION_TYPE)
                    && channelSpuChangeMessage.getAfter().getRetailPrice() > Optional.ofNullable(channelSpuChangeMessage.getBefore()).map(ChannelSpuChangeMessage.DiffContent::getRetailPrice).orElse(0L);

            if (isNewChannelSpu || isPriceIncreaseUpdate) {
                ChannelSpuKey channelSpuKey = new ChannelSpuKey();
                channelSpuKey.setStoreId(channelSpuChangeMessage.getStoreId());
                channelSpuKey.setChannelId(channelSpuChangeMessage.getChannelId());
                channelSpuKey.setSpuId(channelSpuChangeMessage.getSpuId());

                ChannelSpuKey.ChannelSkuKey channelSkuKey = new  ChannelSpuKey.ChannelSkuKey();
                channelSkuKey.setSkuId(channelSpuChangeMessage.getSkuId());
                channelSkuKey.setRetailPrice(channelSpuChangeMessage.getAfter().getRetailPrice());
                channelSpuKey.setSkuList(Lists.newArrayList(channelSkuKey));

                sealTagService.syncSealTag(channelSpuChangeMessage.getTenantId(), channelSpuChangeMessage.getStoreId(), Lists.newArrayList(channelSpuKey));
            }
        }

        return true;
    }

    private Optional<ChannelSpuChangeMessage> translateMessage(String messageBody) {
        try {
            return Optional.of(JSON.parseObject(messageBody, ChannelSpuChangeMessage.class));
        } catch (Exception e) {
            log.error("translateMessage ChannelSpuChangeMessage error, msg = {}", messageBody,e);
            return Optional.empty();
        }
    }

}
