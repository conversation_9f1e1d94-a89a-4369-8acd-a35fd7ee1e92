package com.sankuai.shangou.logistics.delivery.poi.domain;

import com.sankuai.shangou.logistics.delivery.constants.DapSyncStatusEnum;
import com.sankuai.shangou.logistics.delivery.constants.DeleteEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023/9/1 16:11
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SelfDeliveryPoiConfig {
    private Long id;

    private Long tenantId;

    private Long poiId;

    private Integer enableTurnDelivery;

    private DapSyncStatusEnum dapSyncStatus;

    private String lastOperatorName;

    private Long lastOperatorId;

    private LocalDateTime lastOperateTime;

    private DeleteEnum isDeleted;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
