package com.sankuai.shangou.logistics.delivery.configure.repository;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.ConfigTemplateItemModel;
import com.sankuai.shangou.logistics.delivery.configure.enums.DeliveryConfigTypeEnum;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.ConfigTemplateItemDOMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension.ConfigTemplateItemDOExMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDOExample;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-30
 */
@Service
public class ConfigTemplateItemRepository {
    @Resource
    private ConfigTemplateItemDOExMapper configTemplateItemDOExMapper;
    @Resource
    private ConfigTemplateItemDOMapper configTemplateItemDOMapper;


    public Map<Long /* templateId */, List<Integer> /* templateType */> queryTemplateTypeMap(Collection<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyMap();
        }
        List<ConfigTemplateItemDO> list = configTemplateItemDOExMapper.queryTemplateTypeByTemplateId(templateIds);
        Map<Long, List<Integer>> templateTypeMap = new HashMap<>();
        for (ConfigTemplateItemDO item : list) {
            if (templateTypeMap.containsKey(item.getConfigTemplateId())) {
                templateTypeMap.get(item.getConfigTemplateId()).add(item.getTemplateType());
            } else {
                templateTypeMap.put(item.getConfigTemplateId(), Lists.newArrayList(item.getTemplateType()));
            }
        }
        return templateTypeMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<ConfigTemplateItemDO> itemDOS) {
        if (CollectionUtils.isEmpty(itemDOS)) {
            return 0;
        }
        return configTemplateItemDOExMapper.batchInsert(itemDOS);
    }

    public List<ConfigTemplateItemModel> queryByConfigTemplateId(Long configTemplateId) {
        if (configTemplateId == null) {
            return Collections.emptyList();
        }
        ConfigTemplateItemDOExample example = new ConfigTemplateItemDOExample();
        example.createCriteria().andConfigTemplateIdEqualTo(configTemplateId)
                .andIsDeleteEqualTo(0);
        List<ConfigTemplateItemDO> itemDOList = configTemplateItemDOMapper.selectByExample(example);
        return itemDOList.stream().map(this::translate).collect(Collectors.toList());
    }

    private ConfigTemplateItemModel translate(ConfigTemplateItemDO itemDO) {
        ConfigTemplateItemModel model = new ConfigTemplateItemModel();
        model.setId(itemDO.getId());
        model.setTenantId(itemDO.getTenantId());
        model.setConfigTemplateId(itemDO.getConfigTemplateId());
        model.setTemplateType(DeliveryConfigTypeEnum.enumOf(itemDO.getTemplateType()));
        model.setConfigContent(JSON.parseObject(itemDO.getConfigContent()));
        model.setCreatedAt(itemDO.getCreatedAt());
        model.setCreateBy(itemDO.getCreateBy());
        model.setUpdateTime(itemDO.getUpdateTime());
        return model;
    }
}
