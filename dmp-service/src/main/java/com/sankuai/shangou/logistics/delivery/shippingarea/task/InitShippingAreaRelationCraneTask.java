package com.sankuai.shangou.logistics.delivery.shippingarea.task;

import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.shangou.logistics.delivery.shippingarea.service.ShippingAreaRelationService;
import com.sankuai.shangou.logistics.delivery.shippingarea.task.param.InitShippingAreaParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/10/8 21:07
 **/
@Service
@Slf4j
public class InitShippingAreaRelationCraneTask {

    @Resource
    private ShippingAreaRelationService shippingAreaRelationService;

    @Crane("init-shipping-area-relation-crane-task")
    public void initShippingAreaRelation(InitShippingAreaParam param) {
        log.info("init-shipping-area-relation-crane-task start, param:{}", param);
        shippingAreaRelationService.initShippingArea(param.getTenantId(), param.getStoreIds());
        log.info("init-shipping-area-relation-crane-task end");
    }
}
