package com.sankuai.shangou.logistics.delivery.configure.crane;

import com.alibaba.fastjson.JSON;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderLabelEnum;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelTimeOutConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.FulfillConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.FulfillConfigQueryResponse;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.StoreFulfillConfigThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.retry.RetryTemplateUtil;
import com.sankuai.shangou.logistics.delivery.config.LionConfigUtils;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.crane.param.InitDeliveryConfigTaskParam;
import com.sankuai.shangou.logistics.delivery.configure.value.DeliveryRemindConfig;
import com.sankuai.shangou.logistics.delivery.configure.value.SecondDeliveryForbiddenCondition;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.IStoreConfigDOMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.StoreConfigDOMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.StoreDimensionConfigDOMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDOExample;
import com.sankuai.shangou.logistics.delivery.dao.config.model.StoreDimensionConfigDO;
import com.sankuai.shangou.logistics.delivery.mapper.SelfDeliveryPoiConfigPOMapper;
import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPO;
import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPOExample;
import com.sankuai.shangou.qnh.ofc.ebase.consts.PickDeliveryWorkModeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.RetryCallback;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-08-05
 * @email <EMAIL>
 */
@Slf4j
@CraneConfiguration
@Service
public class DeliveryConfigInitCraneTask {

    @Resource
    private StoreDimensionConfigDOMapper storeDimensionConfigDOMapper;
    @Resource
    private StoreConfigDOMapper storeConfigDOMapper;
    @Resource
    private IStoreConfigDOMapper iStoreConfigDOMapper;
    @Resource
    private SelfDeliveryPoiConfigPOMapper selfDeliveryPoiConfigPOMapper;
    @Resource
    private StoreFulfillConfigThriftService.Iface storeFulfillConfigThriftService;

    private final static List<Integer> CAN_TURN_SECOND_PLATFORM = Lists.newArrayList(
            DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode(),
            DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode(),
            DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode()
    );

    /**
     * NOTE
     */
    @Crane("init-delivery-config-task")
    public void initDeliveryConfigTask(InitDeliveryConfigTaskParam param) throws Exception {
        log.info("initDeliveryConfigTask begin, param = {}", param);
        int fetchLimit = LionConfigUtils.getFetchLimit();
        //1.迁移渠道配置
        if (CollectionUtils.isEmpty(param.getIncludeTenantIds()) && CollectionUtils.isEmpty(param.getIncludeStoreIds())) {
            //全量处理 - 使用基于store_id的游标分页确保数据完整性
            processAllStoreConfigsWithStoreIdCursor(fetchLimit, param.isForceInit());
        } else {
            if (CollectionUtils.isNotEmpty(param.getIncludeTenantIds())) {
                for (Long includeTenantId : param.getIncludeTenantIds()) {
                    StoreConfigDOExample storeConfigDOExample = new StoreConfigDOExample();
                    StoreConfigDOExample.Criteria criteria = storeConfigDOExample.createCriteria()
                            .andTenantIdEqualTo(includeTenantId);
                    if (CollectionUtils.isNotEmpty(param.getIncludeStoreIds())) {
                        criteria.andStoreIdIn(param.getIncludeStoreIds());
                    }
                    List<StoreConfigDO> storeConfigDOS = iStoreConfigDOMapper.selectByExampleWithLimit(storeConfigDOExample, fetchLimit);
                    //处理逻辑
                    handleInitStoreConfig(storeConfigDOS, param.isForceInit());
                    handleInitDimensionConfig(storeConfigDOS);
                }

            }
        }
    }

    /**
     * 使用基于store_id的游标分页处理全量数据，确保同一store_id的数据完整性
     */
    private void processAllStoreConfigsWithStoreIdCursor(int fetchLimit, boolean forceInit) {
        log.info("开始全量数据处理，使用store_id游标分页");

        int maxStoreRecords = 50; // 单个store_id最大记录数
        Long nextStoreId = null;
        Long nextId = null; // 在相同store_id内的游标
        int totalProcessed = 0;

        while (true) {
            // 查询一批数据，包含缓冲区以确保store_id完整性
            List<StoreConfigDO> batch = queryBatchDataWithStoreIdCursor(nextStoreId, nextId, fetchLimit + maxStoreRecords);

            if (CollectionUtils.isEmpty(batch)) {
                log.info("全量数据处理完成，总共处理了 {} 条数据", totalProcessed);
                break;
            }

            // 提取完整的store_id组
            ProcessResult processResult = extractCompleteStoreGroups(batch, fetchLimit);
            List<StoreConfigDO> completeGroups = processResult.getCompleteData();

            if (CollectionUtils.isEmpty(completeGroups)) {
                log.warn("未能提取到完整的store_id组，可能数据异常，batch size: {}", batch.size());
                break;
            }

            // 处理完整的store_id组数据
            handleInitStoreConfig(completeGroups, forceInit);
            handleInitDimensionConfig(completeGroups);

            // 更新游标
            nextStoreId = processResult.getNextStoreId();
            nextId = processResult.getNextId();
            totalProcessed += completeGroups.size();

            log.info("处理了 {} 条数据，累计处理 {} 条，下一批从store_id: {}, id: {} 开始",
                    completeGroups.size(), totalProcessed, nextStoreId, nextId);

            // 如果没有更多数据需要处理
            if (nextStoreId == null) {
                log.info("全量数据处理完成，总共处理了 {} 条数据", totalProcessed);
                break;
            }
        }
    }

    private void handleInitStoreConfig(List<StoreConfigDO> storeConfigDOS, boolean forceInit) {
        if (CollectionUtils.isEmpty(storeConfigDOS)) {
            return;
        }
        //需要新增的渠道配置
        List<StoreConfigDO> insertStoreConfigDOList = Lists.newArrayList();
        //需要更新的渠道配置
        List<StoreConfigDO> updateStoreConfigDOList = Lists.newArrayList();

        //歪马和赋能处理不同，分开处理
        List<StoreConfigDO> dhStoreConfigs = IListUtils.nullSafeFilterElement(storeConfigDOS, storeConfigDO -> LionConfigUtils.getDHTenantIdList().contains(String.valueOf(storeConfigDO.getTenantId())));
        List<StoreConfigDO> qnhStoreConfigs = IListUtils.nullSafeFilterElement(storeConfigDOS, storeConfigDO -> !LionConfigUtils.getDHTenantIdList().contains(String.valueOf(storeConfigDO.getTenantId())));
        //歪马
        if (CollectionUtils.isNotEmpty(dhStoreConfigs)) {
            Map<Long, List<StoreConfigDO>> storeIdConfigListMap =
                    dhStoreConfigs.stream()
                            //如果已经初始化过了 就放弃初始化.
                            .filter(storeConfigDO -> forceInit || StringUtils.isBlank(storeConfigDO.getSecondDeliveryPlatform()))
                            .collect(Collectors.groupingBy(StoreConfigDO::getStoreId));
            //歪马根据是否开启转青云来控制的
            SelfDeliveryPoiConfigPOExample selfDeliveryPoiConfigPOExample = new SelfDeliveryPoiConfigPOExample();
            selfDeliveryPoiConfigPOExample.createCriteria().andPoiIdIn(Lists.newArrayList(storeIdConfigListMap.keySet())).andIsDeletedEqualTo(0);
            List<SelfDeliveryPoiConfigPO> selfDeliveryPoiConfigPOS = selfDeliveryPoiConfigPOMapper.selectByExample(selfDeliveryPoiConfigPOExample);
            Map<Long, Integer> storeIdEnableTurnAggMap = IListUtils.nullSafeAndOverrideCollectToMap(selfDeliveryPoiConfigPOS, SelfDeliveryPoiConfigPO::getPoiId, SelfDeliveryPoiConfigPO::getEnableTurnDelivery);

            for (Map.Entry<Long, List<StoreConfigDO>> entry : storeIdConfigListMap.entrySet()) {
                //自送是必须的
                Set<Integer> turnDeliveryPlatforms = Sets.newHashSet(DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode());
                if (storeIdEnableTurnAggMap.containsKey(entry.getKey()) && storeIdEnableTurnAggMap.get(entry.getKey()) == 1) {
                    turnDeliveryPlatforms.add(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode());
                }

                //订单实付金额小于150元 && 不是餐馆 && 不是封签交付订单 && 不是发财酒订单
                SecondDeliveryForbiddenCondition secondDeliveryForbiddenCondition = new SecondDeliveryForbiddenCondition();
                secondDeliveryForbiddenCondition.setOrderTags(Lists.newArrayList(OrderLabelEnum.SEAL_DELIVERY_ORDER.getId(), OrderLabelEnum.PRIORITY_DELIVERY_ORDER.getId(), OrderLabelEnum.MT_FACAI_WINE.getId()));
                secondDeliveryForbiddenCondition.setOrderActualPayment("150");

                for (StoreConfigDO storeConfigDO : entry.getValue()) {
                    StoreConfigDO updateStoreConfigDO = new StoreConfigDO();
                    updateStoreConfigDO.setId(storeConfigDO.getId());
                    updateStoreConfigDO.setSecondDeliveryPlatform(JSON.toJSONString(Lists.newArrayList(turnDeliveryPlatforms)));
                    updateStoreConfigDO.setTurnSecondDeliveryPlatformCondition(JSON.toJSONString(secondDeliveryForbiddenCondition));
                    updateStoreConfigDOList.add(updateStoreConfigDO);
                }
                //只有美团，需要补微商城渠道
                if (entry.getValue().size() == 1 && Objects.equals(entry.getValue().get(0).getChannelType(), DynamicChannelType.MEITUAN.getChannelId())) {
                    StoreConfigDO newStoreConfigDO = JSON.parseObject(JSON.toJSONString(entry.getValue().get(0)), StoreConfigDO.class);
                    newStoreConfigDO.setChannelType(DynamicChannelType.MT_DRUNK_HORSE.getChannelId());
                    insertStoreConfigDOList.add(newStoreConfigDO);
                }
            }
        }

        //赋能
        if (CollectionUtils.isNotEmpty(qnhStoreConfigs)) {
            Map<Long, List<StoreConfigDO>> storeIdConfigListMap =
                    qnhStoreConfigs.stream()
                            //如果已经初始化过了 就放弃初始化.
                            .filter(storeConfigDO -> forceInit || StringUtils.isBlank(storeConfigDO.getSecondDeliveryPlatform()))
                            .collect(Collectors.groupingBy(StoreConfigDO::getStoreId));

            //手动转辅配,赋能原本是看每个渠道开通了什么
            //批量查询所有门店的配置，减少RPC调用次数
            if (!storeIdConfigListMap.isEmpty()) {
                // 收集所有需要查询的门店ID和租户ID
                List<Long> storeIds = new ArrayList<>(storeIdConfigListMap.keySet());
                Long tenantId = storeIdConfigListMap.values().iterator().next().get(0).getTenantId();

                // 批量查询所有门店的配置
                Map<Long, Map<Integer, Integer>> batchBookingPushDownMap = batchQueryBookingPushDownMap(tenantId, storeIds);

                // 处理每个门店的配置
                for (List<StoreConfigDO> singStoreConfigList : storeIdConfigListMap.values()) {
                    Long storeId = singStoreConfigList.get(0).getStoreId();
                    // 从批量查询结果中获取该门店的配置，如果没有则使用默认值
                    Map<Integer, Integer> storeBookingPushDownMap = batchBookingPushDownMap.getOrDefault(storeId, Maps.newHashMap());

                    Set<Integer> turnDeliveryPlatforms = singStoreConfigList.stream().map(StoreConfigDO::getOpenAggrPlatform).filter(openAggrPlat -> CAN_TURN_SECOND_PLATFORM.contains(openAggrPlat)).collect(Collectors.toSet());
                    //自送是必须的
                    turnDeliveryPlatforms.add(DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode());
                    String secondDeliveryConfigJson = JSON.toJSONString(Lists.newArrayList(turnDeliveryPlatforms));
                    singStoreConfigList.forEach(storeConfigDO -> {
                        StoreConfigDO updateStoreConfigDO = new StoreConfigDO();
                        updateStoreConfigDO.setId(storeConfigDO.getId());
                        updateStoreConfigDO.setSecondDeliveryPlatform(secondDeliveryConfigJson);
                        updateStoreConfigDO.setBookingOrderDeliveryLaunchMinutes(storeBookingPushDownMap.getOrDefault(storeConfigDO.getChannelType(), 40));
                        updateStoreConfigDOList.add(updateStoreConfigDO);
                    });
                }
            }

            //执行SQL
            storeConfigDOMapper.batchInsert(insertStoreConfigDOList);
            for (StoreConfigDO storeConfigDO : updateStoreConfigDOList) {
                storeConfigDOMapper.updateByPrimaryKeySelective(storeConfigDO);
            }
        }
    }

    private void handleInitDimensionConfig(List<StoreConfigDO> storeConfigDOS) {

        List<StoreDimensionConfigDO> newStoreDimensionConfigs = Lists.newArrayList();
        //歪马和赋能处理不同，分开处理
        List<StoreConfigDO> dhStoreConfigs = IListUtils.nullSafeFilterElement(storeConfigDOS, storeConfigDO -> LionConfigUtils.getDHTenantIdList().contains(String.valueOf(storeConfigDO.getTenantId())));
        List<StoreConfigDO> qnhStoreConfigs = IListUtils.nullSafeFilterElement(storeConfigDOS, storeConfigDO -> !LionConfigUtils.getDHTenantIdList().contains(String.valueOf(storeConfigDO.getTenantId())));

        //赋能逻辑
        if (CollectionUtils.isNotEmpty(qnhStoreConfigs)) {
            Map<Long, List<StoreConfigDO>> storeIdsConfigMap = qnhStoreConfigs.stream().collect(Collectors.groupingBy(StoreConfigDO::getStoreId));
            for (Map.Entry<Long, List<StoreConfigDO>> entry : storeIdsConfigMap.entrySet()) {
                StoreDimensionConfigDO storeDimensionConfigDO = new StoreDimensionConfigDO();
                storeDimensionConfigDO.setTenantId(entry.getValue().get(0).getTenantId());
                storeDimensionConfigDO.setStoreId(entry.getKey());
                storeDimensionConfigDO.setInternalNavigationMode(1);
                storeDimensionConfigDO.setAssessTimeConfig(null);
                storeDimensionConfigDO.setDeliveryCompleteMode(null);
                storeDimensionConfigDO.setRiderTransRoles(null);
                storeDimensionConfigDO.setDeliveryRemindConfig(null);
                storeDimensionConfigDO.setEnable(1);
                storeDimensionConfigDO.setCreatedAt(LocalDateTime.now());
                storeDimensionConfigDO.setUpdatedAt(LocalDateTime.now());
                newStoreDimensionConfigs.add(storeDimensionConfigDO);
            }
        }

        //歪马逻辑
        if (CollectionUtils.isNotEmpty(dhStoreConfigs)) {
            Map<Long, List<StoreConfigDO>> storeIdsConfigMap = qnhStoreConfigs.stream().collect(Collectors.groupingBy(StoreConfigDO::getStoreId));
            for (Map.Entry<Long, List<StoreConfigDO>> entry : storeIdsConfigMap.entrySet()) {
                StoreDimensionConfigDO storeDimensionConfigDO = new StoreDimensionConfigDO();
                storeDimensionConfigDO.setTenantId(entry.getValue().get(0).getTenantId());
                storeDimensionConfigDO.setStoreId(entry.getKey());
                storeDimensionConfigDO.setInternalNavigationMode(1);
                storeDimensionConfigDO.setAssessTimeConfig(null);
                storeDimensionConfigDO.setDeliveryCompleteMode(null);
                storeDimensionConfigDO.setRiderTransRoles(JSON.toJSONString(Lists.newArrayList(29708, 28086, 28084, 38308, 45959, 42742, 42682, 42676, 42680, 69619, 122018, 153984, 83147)));

                DeliveryRemindConfig deliveryRemindConfig = new DeliveryRemindConfig();
                deliveryRemindConfig.setReceiveTimeOutMins(4);
                deliveryRemindConfig.setSoonDeliveryTimeoutMinsBeforeEta(3);
                storeDimensionConfigDO.setDeliveryRemindConfig(JSON.toJSONString(deliveryRemindConfig));

                storeDimensionConfigDO.setEnable(1);
                storeDimensionConfigDO.setCreatedAt(LocalDateTime.now());
                storeDimensionConfigDO.setUpdatedAt(LocalDateTime.now());
                newStoreDimensionConfigs.add(storeDimensionConfigDO);
            }
        }

        storeDimensionConfigDOMapper.batchInsert(newStoreDimensionConfigs);
    }

    /**
     * 查询一批数据，支持基于store_id的游标分页
     */
    private List<StoreConfigDO> queryBatchDataWithStoreIdCursor(Long fromStoreId, Long fromId, int limit) {
        StoreConfigDOExample example = new StoreConfigDOExample();
        StoreConfigDOExample.Criteria criteria = example.createCriteria();

        if (fromStoreId != null) {
            if (fromId != null) {
                // 同一个store_id内继续查询
                criteria.andStoreIdEqualTo(fromStoreId);
                criteria.andIdGreaterThan(fromId);
            } else {
                // 从指定store_id开始查询
                criteria.andStoreIdGreaterThanOrEqualTo(fromStoreId);
            }
        }

        example.setOrderByClause("store_id, id asc");
        return iStoreConfigDOMapper.selectByExampleWithLimit(example, limit);
    }

    /**
     * 提取完整的store_id组，确保不会在store_id边界处分割数据
     */
    private ProcessResult extractCompleteStoreGroups(List<StoreConfigDO> batch, int targetSize) {
        if (CollectionUtils.isEmpty(batch)) {
            return new ProcessResult(new ArrayList<>(), null, null);
        }

        List<StoreConfigDO> completeGroups = new ArrayList<>();
        Long currentStoreId = null;
        int processedCount = 0;

        for (int i = 0; i < batch.size(); i++) {
            StoreConfigDO data = batch.get(i);

            if (currentStoreId == null) {
                currentStoreId = data.getStoreId();
            }

            // 如果遇到新的store_id
            if (!currentStoreId.equals(data.getStoreId())) {
                // 如果已经处理了足够的数据，且遇到新的store_id，检查是否应该停止
                if (processedCount >= targetSize) {
                    // 检查这个新的store_id组是否完整
                    if (!isCompleteStoreGroup(batch, i, data.getStoreId())) {
                        // 如果不完整，就不包含这个store_id的数据，返回当前结果
                        return new ProcessResult(completeGroups, data.getStoreId(), null);
                    }
                }
                currentStoreId = data.getStoreId();
            }

            completeGroups.add(data);
            processedCount++;
        }

        // 检查最后一个store_id组是否完整
        if (!completeGroups.isEmpty()) {
            Long lastStoreId = completeGroups.get(completeGroups.size() - 1).getStoreId();
            if (!isLastStoreGroupComplete(batch, lastStoreId)) {
                // 如果最后一组不完整，需要继续从这个store_id查询
                Long lastId = completeGroups.get(completeGroups.size() - 1).getId();
                return new ProcessResult(completeGroups, lastStoreId, lastId);
            }
        }

        // 所有数据都处理完了
        return new ProcessResult(completeGroups, null, null);
    }

    /**
     * 检查从指定位置开始的store_id组是否完整
     */
    private boolean isCompleteStoreGroup(List<StoreConfigDO> batch, int startIndex, Long storeId) {
        // 检查从startIndex开始的这个store_id组是否完整
        for (int i = startIndex; i < batch.size(); i++) {
            if (!storeId.equals(batch.get(i).getStoreId())) {
                return true; // 找到了下一个不同的store_id，说明当前组是完整的
            }
        }

        // 如果到了batch末尾还是同一个store_id，可能不完整
        // 但如果batch大小明显小于预期，说明已经是最后的数据了
        return batch.size() < 1000; // 如果batch小于基础大小，认为是完整的
    }

    /**
     * 检查最后一个store_id组是否完整
     */
    private boolean isLastStoreGroupComplete(List<StoreConfigDO> batch, Long lastStoreId) {
        // 统计最后一个store_id的记录数
        long lastStoreIdCount = batch.stream()
                .filter(item -> lastStoreId.equals(item.getStoreId()))
                .count();

        // 如果最后一个store_id的记录数达到了最大值，可能不完整
        // 或者如果batch大小明显小于预期，说明已经是最后的数据了
        return lastStoreIdCount < 50 || batch.size() < 1000;
    }

    /**
     * 处理结果封装类
     */
    private static class ProcessResult {
        private final List<StoreConfigDO> completeData;
        private final Long nextStoreId;
        private final Long nextId;

        public ProcessResult(List<StoreConfigDO> completeData, Long nextStoreId, Long nextId) {
            this.completeData = completeData;
            this.nextStoreId = nextStoreId;
            this.nextId = nextId;
        }

        public List<StoreConfigDO> getCompleteData() {
            return completeData;
        }

        public Long getNextStoreId() {
            return nextStoreId;
        }

        public Long getNextId() {
            return nextId;
        }
    }


    /**
     * 批量查询门店的预约推送配置，减少RPC调用次数
     *
     * @param tenantId 租户ID
     * @param storeIds 门店ID列表
     * @return 门店ID -> (渠道ID -> 推送时间) 的映射
     */
    private Map<Long/*storeId*/, Map<Integer/*channelId*/, Integer/*bookingPushDownTime*/>> batchQueryBookingPushDownMap(Long tenantId, List<Long> storeIds) {
        try {
            Map<Long, Map<Integer, Integer>> result = Maps.newHashMap();

            // 批量调用RPC接口
            FulfillConfigQueryResponse response = RetryTemplateUtil.simpleWithFixedRetry(3, 100).execute(
                    (RetryCallback<FulfillConfigQueryResponse, Exception>) context -> storeFulfillConfigThriftService.batchQuery(tenantId, storeIds, 1L)
            );

            if (response == null || CollectionUtils.isEmpty(response.getFulfillConfigList())) {
                // 如果查询失败，为所有门店返回默认配置
                storeIds.forEach(storeId -> result.put(storeId, Maps.newHashMap()));
                return result;
            }

            // 处理返回的配置列表
            List<FulfillConfig> fulfillConfigList = response.getFulfillConfigList();
            Map<Long, FulfillConfig> storeConfigMap = fulfillConfigList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(FulfillConfig::getStoreId, config -> config, (a, b) -> a));

            // 为每个门店构建配置映射
            for (Long storeId : storeIds) {
                Map<Integer, Integer> channelBookingPushDownMap = Maps.newHashMap();
                FulfillConfig fulfillConfig = storeConfigMap.get(storeId);

                if (fulfillConfig != null) {
                    // 这里暂时使用空的channelIds，因为在批量查询场景下我们主要关注的是能否成功调用RPC
                    // 实际的渠道配置会在后续处理中设置
                    buildMinutesOfPushDownInAdvanceByOrderBizType(fulfillConfig, channelBookingPushDownMap);
                }

                result.put(storeId, channelBookingPushDownMap);
            }

            return result;
        } catch (Exception e) {
            log.error("batchQueryBookingPushDownMap error, tenantId:{}, storeIds:{}", tenantId, storeIds, e);
            // 发生异常时，为所有门店返回默认配置
            Map<Long, Map<Integer, Integer>> result = Maps.newHashMap();
            storeIds.forEach(storeId -> result.put(storeId, Maps.newHashMap()));
            return result;
        }
    }


    private void buildMinutesOfPushDownInAdvanceByOrderBizType(FulfillConfig fulfillConfig, Map<Integer/*channelId*/, Integer/*bookingPushDownTime*/> channelBookingPushDownMap) {
        try {
            if (fulfillConfig.getTimeoutType() == 0) {
                channelBookingPushDownMap.entrySet().forEach(entry -> {
                    entry.setValue(fulfillConfig.getMinutesOfPushDownInAdvance());
                });
            } else {
                channelBookingPushDownMap.entrySet().forEach(entry -> {
                    entry.setValue(getBookingPushDownMinsByOrderBizType(fulfillConfig, Optional.ofNullable(DynamicOrderBizType.channelId2OrderBizType(entry.getKey())).map(DynamicOrderBizType::getValue).orElse(null)));
                });
            }
        } catch (Exception e) {
            Cat.logEvent("GET_TIMEOUT_ERROR", "ERROR");
            log.error("getMinutesOfPushDownInAdvanceByOrderBizType error", e);
        }
    }


    private Integer getBookingPushDownMinsByOrderBizType(FulfillConfig fulfillConfig, Integer orderBizType) {
        // orderBiz相关的渠道 取不到，取美团的，美团的取不到，取默认
        final Integer orderBizTypeEnum = Optional.ofNullable(orderBizType)
                .orElse(DynamicOrderBizType.MEITUAN_WAIMAI.getValue());
        final Map<Integer, ChannelTimeOutConfig> timeOutConfigInfoMap = Optional.ofNullable(fulfillConfig.getChannelTimeOutConfig()).orElse(Collections.emptyList())
                .stream().filter(item -> Objects.nonNull(item.getOrderBizType()))
                .collect(Collectors.toMap(ChannelTimeOutConfig::getOrderBizType, it -> it, (a, b) -> a));
        final ChannelTimeOutConfig channelTimeOutConfigInfo = Optional
                .ofNullable(timeOutConfigInfoMap.get(orderBizTypeEnum))
                .orElse(timeOutConfigInfoMap.get(DynamicOrderBizType.MEITUAN_WAIMAI.getValue()));
        if (channelTimeOutConfigInfo == null) {
            // 默认为90分钟下发
            return 40;
        } else {
            return channelTimeOutConfigInfo.getMinutesOfPushDownInAdvance();
        }
    }
}
