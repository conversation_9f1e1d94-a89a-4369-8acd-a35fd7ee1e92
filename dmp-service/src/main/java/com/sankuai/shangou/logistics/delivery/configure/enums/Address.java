package com.sankuai.shangou.logistics.delivery.configure.enums;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import lombok.Getter;
import lombok.ToString;

/**
 * 地址信息模型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
@Getter
@ToString
public class Address {

	/**
	 * 详细地址描述
	 */
	private final String addressDetail;

	/**
	 * 坐标系类型
	 */
	private final CoordinateTypeEnum coordinateType;

	/**
	 * 标准坐标点
	 */
	private final CoordinatePoint coordinatePoint;

	public Address(String addressDetail, CoordinateTypeEnum coordinateType, CoordinatePoint coordinatePoint) {
		this.addressDetail = addressDetail;
		this.coordinateType = coordinateType;
		this.coordinatePoint = coordinatePoint;
	}

	@JsonIgnore
	public boolean isValid() {
		//检查坐标类型是否合法
		if (coordinateType == null || coordinateType == CoordinateTypeEnum.UNKNOWN) {
			return false;
		}

		return coordinatePoint != null;
	}
}
