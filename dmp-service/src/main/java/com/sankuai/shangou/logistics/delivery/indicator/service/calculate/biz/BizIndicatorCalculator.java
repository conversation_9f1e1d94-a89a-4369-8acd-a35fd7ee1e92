package com.sankuai.shangou.logistics.delivery.indicator.service.calculate.biz;



import com.sankuai.shangou.logistics.delivery.indicator.dto.BaseIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BizIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/7/24 19:36
 **/
public interface BizIndicatorCalculator {
    List<BaseIndicatorEnum> getRelateBaseIndicators();

    BizIndicatorEnum getSupportIndicator();

    List<IndicatorDTO> calc(Map<String, List<IndicatorDTO>> baseIndicatorMap);

    boolean checkBaseIndicatorDataIsValid(Map<String, List<IndicatorDTO>> baseIndicatorMap);

}
