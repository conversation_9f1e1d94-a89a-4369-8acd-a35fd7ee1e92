package com.sankuai.shangou.logistics.delivery.deliveryorder.repository.es;

import com.sankuai.meituan.poros.client.PorosRestHighLevelClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.shangou.logistics.delivery.deliveryorder.constants.TimeConstants;
import com.sankuai.shangou.logistics.delivery.deliveryorder.repository.dbo.DeliveryOrderDoc;
import com.sankuai.shangou.logistics.delivery.indicator.repository.es.AbstractEagleRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.sankuai.shangou.logistics.delivery.deliveryorder.constants.DeliveryOrderConstants.FIELD_CREATE_TIME;
import static com.sankuai.shangou.logistics.delivery.deliveryorder.constants.DeliveryOrderConstants.FIELD_DELIVERY_STATUS;
import static com.sankuai.shangou.logistics.delivery.deliveryorder.constants.DeliveryOrderConstants.FIELD_ORDER_ID;
import static org.elasticsearch.index.query.QueryBuilders.rangeQuery;
import static org.elasticsearch.index.query.QueryBuilders.termsQuery;

/**
 * <AUTHOR>
 * @description
 * @date 2025-05-23
 */
@Slf4j
@Repository
public class DeliveryOrderEagleRepository extends AbstractEagleRepository<DeliveryOrderDoc> {

    @Resource
    @Qualifier("esDeliveryOrderClient")
    private PorosRestHighLevelClient esDeliveryOrderClient;
    @Value("${es.deliveryOrderIndex}")
    private String index;

    @Override
    protected PorosRestHighLevelClient getEsClientRepository() {
        return esDeliveryOrderClient;
    }

    @Override
    protected String getReadIndicatorIndex() {
        return index;
    }

    @Override
    protected String getWriteIndicatorIndex(DeliveryOrderDoc deliveryOrderDoc) {
        // dmp服务不执行写操作
        return null;
    }

    /**
     * 查询发配送失败的订单
     */
    public Pair<List<Long>, Long> searchLaunchFailOrderIds(LocalDateTime beginTime, LocalDateTime endTime, Integer from, Integer size) {
        try {
            RangeQueryBuilder rangeQuery = rangeQuery(FIELD_CREATE_TIME);
            rangeQuery.gte(beginTime.format(TimeConstants.FMT_YMD_HMS_MS_FORMATTER));
            rangeQuery.lte(endTime.format(TimeConstants.FMT_YMD_HMS_MS_FORMATTER));
            Set<Integer> deliveryStatus = new HashSet<>();
            deliveryStatus.add(DeliveryStatusEnum.DELIVERY_REJECTED.getCode());
            deliveryStatus.add(DeliveryStatusEnum.DELIVERY_FAILED.getCode());
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                    .filter(rangeQuery)
                    .filter(termsQuery(FIELD_DELIVERY_STATUS, deliveryStatus));

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                    .query(queryBuilder)
                    .fetchSource(new String[]{FIELD_ORDER_ID}, null)
                    .from(from)
                    .size(size);
            EsSearchResult<DeliveryOrderDoc> searchResult = search(searchSourceBuilder);
            List<Long> orderIds = searchResult.getResultList().stream()
                    .map(DeliveryOrderDoc::getOrderId)
                    .collect(Collectors.toList());
            return Pair.of(orderIds, searchResult.getTotalHits());
        } catch (Exception e) {
            log.error("查询发配送失败的订单异常", e);
            return Pair.of(Collections.emptyList(), 0L);
        }
    }
}
