package com.sankuai.shangou.logistics.delivery.push.config;

import com.sankuai.shangou.logistics.delivery.enums.FreeMarkTemplateEnum;
import com.sankuai.shangou.logistics.delivery.push.IPushMessageService;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class PushMessageServiceConfig {

    @Bean
    public Map<FreeMarkTemplateEnum, IPushMessageService> pushMessageServiceMap(ApplicationContext applicationContext) {
        Map<FreeMarkTemplateEnum, IPushMessageService> map = new HashMap<>();
        String[] beanNames = applicationContext.getBeanNamesForType(IPushMessageService.class);

        for (String beanName : beanNames) {
            IPushMessageService service = (IPushMessageService) applicationContext.getBean(beanName);
            map.put(service.getTemplateEnum(), service);
        }

        return map;
    }

}
