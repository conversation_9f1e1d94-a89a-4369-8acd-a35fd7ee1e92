package com.sankuai.shangou.logistics.delivery.configure.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 批量任务状态枚举
 * @date 2025-07-02
 */
public enum BatchTaskStatusEnum {
    INIT(0, "初始化"),

    PROCESSING(1, "处理中"),

    COMPLETED(2, "处理完成"),

    FAIL(3, "处理失败");

    private final int code;
    private final String desc;

    BatchTaskStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    @JsonCreator
    public static BatchTaskStatusEnum enumOf(Integer code) {
        return Stream.of(values()).filter(item -> Objects.equals(item.getCode(), code))
                .findFirst().orElse(null);
    }
}
