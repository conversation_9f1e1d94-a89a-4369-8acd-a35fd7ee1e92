package com.sankuai.shangou.logistics.delivery.questionnaire.mq;

import com.google.common.base.Preconditions;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TStaffRider;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryAsyncOutTypeEnum;
import com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDO;
import com.sankuai.shangou.logistics.delivery.questionnaire.DeliveryQuestionnaireService;
import com.sankuai.shangou.logistics.delivery.questionnaire.factory.DeliveryQuestionnaireFactory;
import com.sankuai.shangou.logistics.delivery.questionnaire.mq.message.DeliveryChangeSyncOutMessage;
import com.sankuai.shangou.logistics.delivery.questionnaire.utils.MccUtils;
import com.sankuai.shangou.logistics.delivery.questionnaire.wrapper.OrderSystemClient;
import com.sankuai.shangou.logistics.delivery.questionnaire.wrapper.RiderQueryServiceClient;
import com.sankuai.shangou.logistics.delivery.questionnaire.wrapper.TenantSystemClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * <AUTHOR>
 * @since 2023/11/10 14:36
 **/
@Service
@Slf4j
public class RiderChangeMessageListener extends AbstractDeadLetterConsumer {

    @Resource
    private TenantSystemClient tenantSystemClient;

    @Resource
    private OrderSystemClient orderSystemClient;

    @Resource
    private RiderQueryServiceClient riderQueryServiceClient;

    @Resource
    private DeliveryQuestionnaireService deliveryQuestionnaireService;

    private final Integer ONE_YUAN_ORDER_TAG = 102;

    private final Integer TRANS_RIDER_EVENT_TYPE = 2;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.RELEASE_DELIVERY_QUESTIONNAIRE_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
        log.info("开始消费骑手变更消息：{}", mafkaMessage);
        DeliveryChangeSyncOutMessage message = translateMessage(mafkaMessage);
        if (message == null) {
            return CONSUME_SUCCESS;
        }

        //过滤其他类型的消息
        if(!Objects.equals(message.getChangeType(), DeliveryAsyncOutTypeEnum.DELIVERY_ACCEPT_OR_RIDER_CHANGE.getValue()) &&
                !Objects.equals(message.getChangeType(), DeliveryAsyncOutTypeEnum.DELIVERY_CANCEL.getValue())) {
            log.info("非骑手接单、骑手转单、配送取消消息, 放弃消费");
            return CONSUME_SUCCESS;
        }

        Long tenantId = message.getHead().getTenantId();
        Long storeId = message.getHead().getShopId();

        //过滤非歪马
        if (!MccUtils.checkIsDHTenant(tenantId)) {
            return CONSUME_SUCCESS;
        }

        //如果是订单取消 把投放的问卷置为无效
        if (Objects.equals(message.getChangeType(), DeliveryAsyncOutTypeEnum.DELIVERY_CANCEL.getValue())) {
            deliveryQuestionnaireService.invalidQuestionnaire(message.getHead().getDeliveryId());
            return CONSUME_SUCCESS;
        }

        //如果是订单取消 把之前投放的问卷置为无效
        DeliveryChangeSyncOutMessage.DeliveryRiderChangeBody body = (DeliveryChangeSyncOutMessage.DeliveryRiderChangeBody) message.fetchBody();
        if (Objects.equals(body.getEventType(), TRANS_RIDER_EVENT_TYPE)) {
            deliveryQuestionnaireService.invalidQuestionnaire(message.getHead().getDeliveryId());
        }

        //查询门店信息
        PoiInfoDto storeInfo = tenantSystemClient.queryStoreDetailInfo(tenantId, storeId);
        Bssert.throwIfNull(storeInfo, "门店信息查询失败");


        //过滤非灰度门店 && 非灰度城市
        if (!MccUtils.checkIsQuestionnaireGrayCity(storeInfo.getDistrict().getCityId()) && !MccUtils.checkIsQuestionnaireGrayStore(storeId)) {
            return CONSUME_SUCCESS;
        }


        //过滤非一元单
        List<OCMSOrderVO> ocmsOrderVOList =
                orderSystemClient.queryOCMSOrderList(Collections.singletonList(Pair.of(message.getHead().getOrderBizType(), message.getHead().getChannelOrderId())));

        if (CollectionUtils.isEmpty(ocmsOrderVOList)) {
            log.error("未查询到订单详情信息, viewOrderId:{}", message.getHead().getChannelOrderId());
            return CONSUME_FAILURE;
        }

        OCMSOrderVO ocmsOrderVO = ocmsOrderVOList.get(0);
        boolean isOneYuanOrder = Optional.ofNullable(ocmsOrderVO.getTags())
                .orElse(Collections.emptyList())
                .stream()
                .anyMatch(tag -> Objects.equals(tag.getType(), ONE_YUAN_ORDER_TAG));

        if (isOneYuanOrder) {
            log.info("一元单不投放, viewOrderId:{}", message.getHead().getChannelOrderId());
            return CONSUME_SUCCESS;
        }

        //查看运单
        List<TRiderDeliveryOrder> deliveryOrderList = riderQueryServiceClient.queryDeliveryOrderByOrderIds(
                message.getHead().getTenantId(),
                message.getHead().getShopId(),
                Collections.singletonList(message.getHead().getOrderId()));
        if (CollectionUtils.isEmpty(deliveryOrderList)) {
            log.warn("未查询到运单, 放弃投放问卷, orderId: {}", message.getHead().getOrderId());
            return CONSUME_SUCCESS;
        }

        TRiderDeliveryOrder deliveryOrder = deliveryOrderList.get(0);

        if (!Objects.equals(deliveryOrder.getDeliveryOrderId(), message.getHead().getDeliveryId())) {
            log.warn("订单对应的运单已经改变, 放弃投放问卷, orderId: {}", message.getHead().getOrderId());
            return CONSUME_SUCCESS;
        }

        if (isFinalStatus(deliveryOrder)) {
            log.info("运单已到达终态, 放弃投放问卷, deliveryOrderId: {}", message.getHead().getDeliveryId());
            return CONSUME_SUCCESS;
        }

        TStaffRider riderInfo = deliveryOrder.getCurrentRider();
        if (Objects.isNull(riderInfo) || Objects.isNull(riderInfo.getAccountId())) {
            log.warn("骑手信息为空, 放弃投放问卷, deliveryOrderId: {}", message.getHead().getDeliveryId());
            return CONSUME_SUCCESS;
        }

        //查询骑手最后一次被投放时间是否满足投放时间间隔
        Optional<DeliveryQuestionnaireDO> latestQuestionnaireRecordOpt =
                deliveryQuestionnaireService.queryRiderLatestQuestionnaireRecord(riderInfo.getAccountId());
        if (!latestQuestionnaireRecordOpt.isPresent() || satisfyNextReleasePeriod(latestQuestionnaireRecordOpt.get())) {
            //投放问卷,落库
            List<DeliveryQuestionnaireDO> questionnaireDOList
                    = DeliveryQuestionnaireFactory.releaseQuestionnaireForDeliveryOrder(deliveryOrder, LocalDateTime.now(), storeInfo.getDistrict().getCityId());
            deliveryQuestionnaireService.releaseQuestionnaire(questionnaireDOList);
            return CONSUME_SUCCESS;
        }

        return CONSUME_SUCCESS;
    }

    private boolean satisfyNextReleasePeriod(DeliveryQuestionnaireDO deliveryQuestionnaireDO) {
        if (Objects.isNull(deliveryQuestionnaireDO)) {
            log.error("deliveryQuestionnaireDO is null");
            throw new RuntimeException();
        }

        if (Objects.isNull(deliveryQuestionnaireDO.getReleaseTime())) {
            log.error("releaseTime is null");
            throw new RuntimeException();
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime latestReleaseTime = deliveryQuestionnaireDO.getReleaseTime();
        return Duration.between(latestReleaseTime, now).getSeconds() / 60 > MccUtils.getReleaseQuestionnairePeriod();
    }

    DeliveryChangeSyncOutMessage translateMessage(MafkaMessage mafkaMessage) {
        DeliveryChangeSyncOutMessage message = translateMessage(mafkaMessage, DeliveryChangeSyncOutMessage.class);

        Preconditions.checkNotNull(message, "empty mafkaMessage");
        Preconditions.checkNotNull(message.getChangeType(), "change type is null");
        Preconditions.checkNotNull(message.getHead(), "head is null");
        return message;
    }


    private boolean isFinalStatus(TRiderDeliveryOrder deliveryOrder) {
        return Objects.equals(RiderDeliveryStatusEnum.DELIVERY_DONE.getCode(), deliveryOrder.getDeliveryStatus()) ||
                Objects.equals(RiderDeliveryStatusEnum.DELIVERY_CANCELLED.getCode(), deliveryOrder.getDeliveryStatus());
    }
}
