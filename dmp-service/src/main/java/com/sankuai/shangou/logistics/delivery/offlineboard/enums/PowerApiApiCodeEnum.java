package com.sankuai.shangou.logistics.delivery.offlineboard.enums;

/***
 * author : <EMAIL> 
 * date : 2022/10/31 
 * time : 4:05 PM
 * 描述 :
 **/
public enum PowerApiApiCodeEnum {

    REAL_TIME_OVERVIEW("歪马实时数据概览", "b_service_wmsj_baichuan_overview_rt_data"),

    OFFLINE_OVERVIEW("歪马离线数据数据概览-日、周、月", "b_service_wmsj_baichuan_overview_offline_data_d"),

    SELL_DATA("歪马实时销售数据", "b_service_wmsj_baichuan_prod_sale_rt_data"),

    REAL_TIME_TREND("实时时段趋势图", "b_service_wmsj_baichuan_time_segment_trend_chart_data"),

    REAL_TIME_CUMULATIVE("实时累积趋势图", "b_service_wmsj_baichuan_acc_trend_chart_data"),


    OFFLINE_TREND("离线趋势图-日、周、月", "b_service_wmsj_baichuan_trend_chart_offline_data"),

    FULFILL_OFFLINE_OVERVIEW("B端服务-歪马门店履约配送总览离线数据", "b_service_waima_performance_overview_offline_data"),

    FULFILL_OFFLINE_STORE_DATA("B端服务-歪马门店维度离线数据", "b_service_waima_performance_poi_offline_data_v2"),

    FULFILL_OFFLINE_RIDER_DATA("B端服务-歪马员工维度离线数据", "b_service_waima_performance_employee_offline_data_v2"),

    FULFILL_OFFLINE_ABNORMAL_ORDER_OLD("B端服务-歪马履约配送异常订单离线数据", "b_service_waima_performance_err_ord_offline_data"),
    FULFILL_OFFLINE_ABNORMAL_ORDER_V3("B端服务-歪马履约配送异常订单离线数据", "b_service_waima_performance_err_ord_offline_data_v3"),
    FULFILL_OFFLINE_ABNORMAL_ORDER_V4("B端服务-歪马履约配送异常订单离线数据", "b_service_waima_performance_err_ord_offline_data_v4"),

    FULFILL_OFFLINE_DATA_CHECK("B端服务-歪马履约配送数据是否就绪", "b_service_waima_performance_check_data_prepare"),

    FULFILL_OFFLINE_ORG_DATA("B端服务-歪马组织维度离线数据", "b_service_waima_poi_org_one_two_performance_data_offline"),
    FULFILL_OFFLINE_THIRD_DELIVERY_DATA("B端服务-歪马履约配送总览累积数据【三方】", "b_service_waima_performance_overview_data_three_offline"),
    FULFILL_OFFLINE_THIRD_PERFORMANCE_DATA("B端服务-履约配送门店运力承运商维度", "b_service_waima_poi_performance_data_three_offline"),

    FULFILL_OFFLINE_THIRD_OVERVIEW_DATA("B端服务-履约三方配送汇总数据", "b_service_waima_performance_overview_data_three_offline"),
    ;

    PowerApiApiCodeEnum(String name, String appCode) {
        this.name = name;
        this.appCode = appCode;
    }


    private final String name;

    private final String appCode;


    public String getName() {
        return name;
    }

    public String getAppCode() {
        return appCode;
    }
}
