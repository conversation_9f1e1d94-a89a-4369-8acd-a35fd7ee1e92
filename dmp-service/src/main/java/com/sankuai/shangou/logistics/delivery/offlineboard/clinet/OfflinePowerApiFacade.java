package com.sankuai.shangou.logistics.delivery.offlineboard.clinet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.logistics.delivery.offlineboard.enums.PowerApiApiCodeEnum;
import com.sankuai.waimai.dws.protocol.openapi.OpenapiQueryService;
import com.sankuai.waimai.dws.protocol.structs.DWSFormatResponse;
import com.sankuai.waimai.dws.protocol.structs.DWSParam;
import com.sankuai.waimai.dws.protocol.structs.DWSResponse;
import com.sankuai.waimai.dws.protocol.structs.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/***
 * author : <EMAIL> 
 * date : 2022/10/31 
 * time : 3:56 PM
 * 描述 :
 **/
@Component
@Slf4j
public class OfflinePowerApiFacade {

    @Autowired
    private OpenapiQueryService.Iface powerapiService;


    private static final String appKey = "com.sankuai.sgxsupply.wxmall.eapi";


    public <T> List<T> query(PowerApiApiCodeEnum apiCode, Object param, Class<T> responseClass){

        DWSParam request = new DWSParam();

        request.setAppCode(Lion.getConfigRepository().get("power.api.app.code", "wmsj_rt"));

        request.setAppKey(appKey);

        request.setToken(Lion.getConfigRepository(appKey).get("power.api.token"));

        request.setApiCode(apiCode.getAppCode());

        request.setSqlParam(convertMap(param));


        try {
            DWSResponse result = powerapiService.query(request);
            log.info("请求powerApi， request:{}, result:{}", JacksonUtils.toJson(request), JacksonUtils.toJson(result));

            if (result.getCode() != 0) {
                log.error("查询数据出错，请稍后再试");
                throw new BizException(result.getMsg());
            }

            if (result.getData() != null){
                List<Map<String, String>> dataList = result.getData().getDataList();
                return JacksonUtils.parseList(JacksonUtils.toJson(dataList), responseClass);
            }
            return Lists.newArrayList();

        } catch (Exception e) {
            log.error("请求powerApi错误,request:{}", request, e);
            throw new BizException("查询数据出错，请稍后再试", e);
        }

    }

    public <T> List<T> query(PowerApiApiCodeEnum apiCode, Map<String,String> param, Class<T> responseClass){

        DWSParam request = new DWSParam();

        request.setAppCode(Lion.getConfigRepository().get("power.api.app.code", "wmsj_rt"));

        request.setAppKey(appKey);

        request.setToken(Lion.getConfigRepository(appKey).get("power.api.token"));

        request.setApiCode(apiCode.getAppCode());

        request.setSqlParam(param);


        try {
            DWSResponse result = powerapiService.query(request);
            log.info("请求powerApi， request:{}, result:{}", JacksonUtils.toJson(request), JacksonUtils.toJson(result));
            if (result.getCode() != 0) {
                log.error("查询数据出错，请稍后再试");
                throw new BizException(result.getMsg());
            }

            if (result.getData() != null){
                List<Map<String, String>> dataList = result.getData().getDataList();
                return JacksonUtils.parseList(JacksonUtils.toJson(dataList), responseClass);
            }
            return Lists.newArrayList();

        } catch (Exception e) {
            log.error("请求powerApi错误,request:{}", request, e);
            throw new BizException("查询数据出错，请稍后再试", e);
        }

    }

    public List<Map<String, String>> query(PowerApiApiCodeEnum apiCode, Map<String, String> param) {

        DWSParam request = new DWSParam();

        request.setAppCode(Lion.getConfigRepository().get("power.api.app.code", "wmsj_rt"));

        request.setAppKey(appKey);

        request.setToken(Lion.getConfigRepository(appKey).get("power.api.token"));

        request.setApiCode(apiCode.getAppCode());

        request.setSqlParam(param);


        try {
            DWSResponse result = powerapiService.query(request);
            log.info("请求powerApi， request:{}, result:{}", JacksonUtils.toJson(request), JacksonUtils.toJson(result));

            if (result.getCode() != 0) {
                log.error("查询数据出错，请稍后再试");
                throw new BizException(result.getMsg());
            }

            if (result.getData() != null){
                return result.getData().getDataList();
            }
            return Lists.newArrayList();

        } catch (Exception e) {
            log.error("请求powerApi错误,request:{}", request, e);
            throw new BizException("查询数据出错，请稍后再试", e);
        }

    }


    public  List<Map<String, String>> pageQuery(PowerApiApiCodeEnum apiCode, Map<String, String> param, int pageNo, int pageSize){

        DWSParam request = new DWSParam();

        request.setAppCode(Lion.getConfigRepository().get("power.api.app.code", "wmsj_rt"));

        request.setAppKey(appKey);

        request.setToken(Lion.getConfigRepository(appKey).get("power.api.token"));

        request.setApiCode(apiCode.getAppCode());

        request.setSqlParam(param);

        request.setPage(new Page(pageNo, pageSize));

        request.setIsPage(true);


        try {
            DWSResponse result = powerapiService.query(request);
            log.info("请求powerApi， request:{}, result:{}", JacksonUtils.toJson(request), JacksonUtils.toJson(result));

            if (result.getCode() != 0) {
                log.error("查询数据出错，请稍后再试");
                throw new BizException(result.getMsg());
            }

            if (result.getData() != null){
                return result.getData().getDataList();
            }
            return Lists.newArrayList();

        } catch (Exception e) {
            log.error("请求powerApi错误,request:{}", request, e);
            throw new BizException("查询数据出错，请稍后再试", e);
        }

    }

    public <T> List<T> pageQuery(PowerApiApiCodeEnum apiCode, Map<String, String> param, int pageNo, int pageSize, Class<T> responseClass){

        DWSParam request = new DWSParam();

        request.setAppCode(Lion.getConfigRepository().get("power.api.app.code", "wmsj_rt"));

        request.setAppKey(appKey);

        request.setToken(Lion.getConfigRepository(appKey).get("power.api.token"));

        request.setApiCode(apiCode.getAppCode());

        request.setSqlParam(param);

        request.setPage(new Page(pageNo, pageSize));

        request.setIsPage(true);


        try {
            DWSResponse result = powerapiService.query(request);
            log.info("请求powerApi， request:{}, result:{}", JacksonUtils.toJson(request), JacksonUtils.toJson(result));
            if (result.getCode() != 0) {
                log.error("查询数据出错，请稍后再试");
                throw new BizException(result.getMsg());
            }

            if (result.getData() != null){
                List<Map<String, String>> dataList = result.getData().getDataList();
                return JacksonUtils.parseList(JacksonUtils.toJson(dataList), responseClass);
            }
            return Lists.newArrayList();

        } catch (Exception e) {
            log.error("请求powerApi错误,request:{}", request, e);
            throw new BizException("查询数据出错，请稍后再试", e);
        }

    }

    public Long pageCount(PowerApiApiCodeEnum apiCode, Map<String, String> param){

        DWSParam request = new DWSParam();

        request.setAppCode(Lion.getConfigRepository().get("power.api.app.code", "wmsj_rt"));

        request.setAppKey(appKey);

        request.setToken(Lion.getConfigRepository(appKey).get("power.api.token"));

        request.setApiCode(apiCode.getAppCode());

        request.setSqlParam(param);


        try {
            DWSFormatResponse countResult = powerapiService.queryCount(request);
            log.info("请求powerApi.queryCount, request:{}, result:{}", JacksonUtils.toJson(request), JacksonUtils.toJson(countResult));

            if (countResult.getCode() != 0) {
                log.error("查询数据出错，请稍后再试");
                throw new BizException(countResult.getMsg());
            }

            return Long.parseLong(countResult.getData());
         } catch (Exception e) {
            log.error("请求powerApi错误,request:{}", request, e);
            throw new BizException("查询数据出错，请稍后再试", e);
        }

    }



    private Map<String, String> convertMap(Object param) {

        JSONObject jsonParam = JSONObject.parseObject(JSON.toJSONString(param));

        return jsonParam.entrySet().stream()
                .filter(entry -> StringUtils.isNotBlank(entry.getKey()) && entry.getValue() != null)
                .collect(Collectors.toMap(Map.Entry::getKey, e -> String.valueOf(e.getValue())));
    }

}
