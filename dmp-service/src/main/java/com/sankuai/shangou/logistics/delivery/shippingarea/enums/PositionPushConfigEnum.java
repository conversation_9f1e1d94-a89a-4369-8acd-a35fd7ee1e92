package com.sankuai.shangou.logistics.delivery.shippingarea.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-07
 * @email <EMAIL>
 */
public enum PositionPushConfigEnum {

    SHIPPING_AREA_CHANGE("配送范围变更发大象", "shipping.area.positions", Lists.newArrayList("主管", "城市经理"), Lists.newArrayList()),
    ;

    private String desc;
    private String nameKey;
    private List<String> defaultDirectNameList;
    private List<String> defaultFranchiseeDirectNameList;

    PositionPushConfigEnum(String desc, String nameKey, List<String> defaultDirectNameList, List<String> franchiseeDirectNameList) {
        this.desc = desc;
        this.nameKey = nameKey;
        this.defaultDirectNameList = defaultDirectNameList;
        this.defaultFranchiseeDirectNameList = franchiseeDirectNameList;
    }

    public String getNameKey() {
        return nameKey;
    }

    public List<String> getDefaultDirectNameList() {
        return defaultDirectNameList;
    }

    public List<String> getDefaultFranchiseeDirectNameList() {
        return defaultFranchiseeDirectNameList;
    }
}
