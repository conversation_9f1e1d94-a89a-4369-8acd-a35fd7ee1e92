package com.sankuai.shangou.logistics.delivery.shippingarea.convert;

import com.dianping.lion.client.util.JsonUtils;
import com.meituan.linz.boot.util.Assert;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.download.StoreIndicatorDto;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.geo.GeoStoreDto;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.indicator.*;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.enums.AoiTypeEnum;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.ChannelTypeEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IndicatorConverter {
    public StoreIndicatorDto toStoreIndicatorDto(GeoStoreDto geoStoreDto, OrderUserIndicatorDto orderUserIndicatorDto) {
        StoreIndicatorDto storeIndicatorDto = new StoreIndicatorDto();
        storeIndicatorDto.setStoreDto(geoStoreDto);
        storeIndicatorDto.setOrderUserIndicator(orderUserIndicatorDto);
        return storeIndicatorDto;
    }

    public OrderUserIndicatorDto zeroOrderUserIndicatorDto() {
        OrderUserIndicatorDto orderUserIndicatorDto = new OrderUserIndicatorDto();
        orderUserIndicatorDto.setSgOrderCnt(0L);
        orderUserIndicatorDto.setSgUserCnt(0L);
        orderUserIndicatorDto.setWmOrderCnt(0L);
        orderUserIndicatorDto.setWmUserCnt(0L);
        orderUserIndicatorDto.setStoreOrderCnt(0L);
        orderUserIndicatorDto.setMapResidentCnt(0L);
        return orderUserIndicatorDto;
    }

    public StoreIndicatorDto toStoreIndicatorDto(GeoStoreDto geoStoreDto) {
        return toStoreIndicatorDto(geoStoreDto, zeroOrderUserIndicatorDto());
    }

    public OrderUserIndicatorVO toOrderUserIndicatorVO(OrderUserIndicatorDto dto, Long loiId) {
        OrderUserIndicatorVO vo = new OrderUserIndicatorVO();
        vo.setMapResidentCnt(dto.getMapResidentCnt());
        vo.setWmOrderCnt(dto.getWmOrderCnt());
        vo.setWmUserCnt(dto.getWmUserCnt());
        vo.setSgOrderCnt(dto.getSgOrderCnt());
        vo.setSgUserCnt(dto.getSgUserCnt());
        vo.setStoreOrderCnt(dto.getStoreOrderCnt());
        vo.setLoiId(loiId);
        vo.setOutScopeRate(dto.getOutScopeRate());
        vo.setSgBeverageGmvCoverRate(dto.getSgBeverageGmvCoverRate());

        String storeExtendInfo = dto.getStoreExtendInfo();
        if (StringUtils.isNotBlank(storeExtendInfo)) {
            try {
                StoreExtendOrderIndicatorVO extendIndicator = JsonUtils.fromJson(storeExtendInfo,
                        StoreExtendOrderIndicatorVO.class);
                vo.setDhWmOrdNum(extendIndicator.getWmOrdNum());
                vo.setDhWxOrdNum(extendIndicator.getWxOrdNum());
            } catch (IOException e) {
                log.error("解析门店扩展订单指标失败", e);
                throw new SystemException("获取门店扩展指标失败");
            }
        }
        return vo;
    }

    public ShippingStoreOrderUserIndicatorVO toShippingStoreOrderUserIndicatorVO(OrderUserIndicatorVO orderUserIndicatorVO,
                                                                                 PoiChannelDeliveryInfoVO poiShippingVO) {
        ShippingStoreOrderUserIndicatorVO vo = new ShippingStoreOrderUserIndicatorVO();
        if (Objects.isNull(orderUserIndicatorVO)) {
            return vo;
        }
        BeanUtils.copyProperties(orderUserIndicatorVO, vo);

        if (Objects.isNull(poiShippingVO) || CollectionUtils.isEmpty(poiShippingVO.getRegularPeriodShippingAreaList())) {
            return vo;
        }
        //初始化
        vo.setMtMinPrice(0d);
        vo.setMtShippingFee(0d);

        vo.setMtWmMinPrice(0d);
        vo.setMtWmShippingFee(0d);

        for (Map<Integer, ChannelShippingAreaVO> channelPoiShippingVOMap : poiShippingVO.getRegularPeriodShippingAreaList()) {

            // 每个渠道有多个配送范围，展示每个渠道最高的起送价、配送费
            ChannelShippingAreaVO mtShippingVo = channelPoiShippingVOMap.get(ChannelTypeEnum.MEITUAN.getChannelId());
            ChannelShippingAreaVO dhShippingVo = channelPoiShippingVOMap.get(ChannelTypeEnum.MT_DRUNK_HORSE.getChannelId());

            if (mtShippingVo != null) {
                if (vo.getMtMinPrice().compareTo(mtShippingVo.getMinPrice()) < 0) {
                    vo.setMtMinPrice(mtShippingVo.getMinPrice());
                }
                if (vo.getMtShippingFee().compareTo(mtShippingVo.getShippingFee()) < 0) {
                    vo.setMtShippingFee(mtShippingVo.getShippingFee());
                }
            }

            if (dhShippingVo != null) {
                if (vo.getMtWmMinPrice().compareTo(dhShippingVo.getMinPrice()) < 0) {
                    vo.setMtWmMinPrice(dhShippingVo.getMinPrice());
                }
                if (vo.getMtWmShippingFee().compareTo(dhShippingVo.getShippingFee()) < 0) {
                    vo.setMtWmShippingFee(dhShippingVo.getShippingFee());
                }
            }
        }
        return vo;
    }

    public LoiMapAoiClassifyVO toLoiMapAoiClassifyVO(List<MapAoiClassifyDto> dtoList,
                                                     Long loiId,
                                                     String dt) {
        LoiMapAoiClassifyVO vo = new LoiMapAoiClassifyVO();
        // 暂时不需要other(其它类型)，过滤掉
        vo.setAoiClassifyList(dtoList.stream().filter(dto -> !AoiTypeEnum.other.getCode().equals(dto.getAoiCode()))
                .collect(Collectors.toList()));
        vo.setDt(dt);
        vo.setLoiId(loiId);
        return vo;
    }

    public PeoplePortraitVO toPeoplePortraitVO(PeoplePortraitDto dto, String dt) {
        PeoplePortraitVO vo = new PeoplePortraitVO();
        BeanUtils.copyProperties(dto, vo);
        vo.setDt(dt);
        return vo;
    }

    public Map toUeIndicators(LoiUeIndicatorDto dto) {
        Assert.throwIfTrue(Objects.isNull(dto) || CollectionUtils.isEmpty(dto.getIndicatorList()), "无所选时间数据，请重新选择近6个月的时间");

        Map indicators = Fun.toMap(dto.getIndicatorList(),
                UeIndicatorDto::getDefineIdentifier, UeIndicatorDto::getIndicatorValue);
        return indicators;
    }

}
