package com.sankuai.shangou.logistics.delivery.shippingarea.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/14 20:33
 **/
@Data
@AllArgsConstructor
public class AdministrativeTreeVO {
    private String name;

    private String value;

    private String adCode;

    private List<AdministrativeTreeVO> items = new ArrayList<>();

    public AdministrativeTreeVO(String name, String value, String adCode) {
        this.name = name;
        this.value = value;
        this.adCode = adCode;
    }


    public void addItem(AdministrativeTreeVO attr) {
        this.items.add(attr);
    }
}
