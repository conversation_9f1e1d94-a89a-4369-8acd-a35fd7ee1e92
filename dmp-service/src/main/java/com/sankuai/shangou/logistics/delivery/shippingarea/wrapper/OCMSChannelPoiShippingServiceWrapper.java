package com.sankuai.shangou.logistics.delivery.shippingarea.wrapper;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPoiShippingThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.utils.rpc.RpcInvokeUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.MccUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/6 14:37
 **/
@Service
@Slf4j
public class OCMSChannelPoiShippingServiceWrapper {

    @Resource
    private ChannelPoiShippingThriftService.Iface channelPoiShippingThriftService;

    public List<PoiShippingAreaInfoDTO> getChannelPoiShippingInfoList(Long tenantId, PoiDetailInfoDTO poiDetailInfoDTO, Integer channelId) {
        if(!checkIsOpenedTargetChannel(poiDetailInfoDTO, channelId)) {
            return Collections.emptyList();
        }

        QueryPoiShippingAreaResponse response = RpcInvokeUtils.rpcInvokeTemplate(
                () -> {
                    try {
                        return channelPoiShippingThriftService.queryPoiShippingAreaInfo(new QueryPoiShippingRequest(tenantId, poiDetailInfoDTO.getPoiId(), channelId));
                    } catch (TException e) {
                        throw new RuntimeException(e);
                    }
                },
                "查询渠道配送范围失败",
                QueryPoiShippingAreaResponse::getCode,
                QueryPoiShippingAreaResponse::getMsg);

        return response.getPoiShippingInfoDTOList();
    }


    public void deleteChannelShippingArea(Long tenantId, Long storeId, Integer channelId, Long areaId) {
        RpcInvokeUtils.rpcInvokeTemplate(() -> {
            try {
                return channelPoiShippingThriftService.deletePoiShippingByShippingAreaId(
                        new DeletePoiShippingByShippingIdRequest(tenantId, storeId, channelId, areaId));
            } catch (TException e) {
                throw new RuntimeException(e);
            }

        }, "删除配送范围失败", ResultStatus::getCode, ResultStatus::getMsg);
    }

    public void deleteChannelShippingArea(DeletePoiShippingByShippingIdRequest request) {
        RpcInvokeUtils.rpcInvokeTemplate(() -> {
            try {
                return channelPoiShippingThriftService.deletePoiShippingByShippingAreaId(request);
            } catch (TException e) {
                throw new RuntimeException(e);
            }

        }, "删除配送范围失败", ResultStatus::getCode, ResultStatus::getMsg);
    }

    public List<PoiShippingAreaInfoDTO> batchQueryShippingInfo(Long tenantId, List<PoiDetailInfoDTO> poiDetailInfoDTOList, Integer channelId) {
        if (CollectionUtils.isEmpty(poiDetailInfoDTOList)) {
            return Collections.emptyList();
        }

        List<Long> openedTargetChannelPoiIds = poiDetailInfoDTOList
                .stream()
                .filter(poiDetailInfoDTO -> checkIsOpenedTargetChannel(poiDetailInfoDTO, channelId))
                .map(PoiDetailInfoDTO::getPoiId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(openedTargetChannelPoiIds)) {
            return Collections.emptyList();
        }

        List<List<Long>> lists = Lists.partition(openedTargetChannelPoiIds, MccUtils.getQueryShippingAreasBatchSize());
        List<PoiShippingAreaInfoDTO> shippingAreaList = new ArrayList<>();
        for (List<Long> subStoreIdList : lists) {
            QueryPoiShippingAreaResponse response = RpcInvokeUtils.rpcInvokeTemplate(
                    () -> {
                        try {
                            return channelPoiShippingThriftService.batchQueryShippingInfo(new BatchQueryPoiShippingAreaRequest(tenantId, subStoreIdList, channelId));
                        } catch (TException e) {
                            throw new RuntimeException(e);
                        }
                    },
                    "批量查询渠道配送范围失败",
                    QueryPoiShippingAreaResponse::getCode,
                    QueryPoiShippingAreaResponse::getMsg);

            shippingAreaList.addAll(response.getPoiShippingInfoDTOList());
        }

        return shippingAreaList;
    }

    public Long updateRegularPeriodShippingArea(UpdatePoiRegularPeriodShippingByShippingAreaIdRequest request) {
        ResultStatus result = RpcInvokeUtils.rpcInvokeTemplate(() -> {
            try {
                return channelPoiShippingThriftService.updatePoiRegularPeriodShippingByShippingAreaId(request);
            } catch (TException e) {
                throw new RuntimeException(e);
            }
        }, "更新正常时段配送失败", ResultStatus::getCode, ResultStatus::getMsg);

        //新增area时回写areaId
        if (request.getShippingAreaId() == 0) {
            return Long.parseLong(result.getData());
        }

        return null;
    }


    public Long updateSpecialPeriodShippingArea(UpdatePoiSpecialPeriodShippingByShippingAreaIdRequest request) {
        ResultStatus result = RpcInvokeUtils.rpcInvokeTemplate(() -> {
            try {
                return channelPoiShippingThriftService.updatePoiSpecialPeriodShippingByShippingAreaId(request);
            } catch (TException e) {
                throw new RuntimeException(e);
            }
        }, "更新特殊时段配送失败", ResultStatus::getCode, ResultStatus::getMsg);

        //新增area时回写areaId
        if (request.getShippingAreaId() == 0) {
            return Long.parseLong(result.getData());
        }

        return null;
    }

    //注意：这个接口的配送价和起送费单位是分 而不是元
    public void resetShippingArea(ResetPoiShippingRequest request) {
        ResultStatus result = null;
        try {
            log.info("invoke channelPoiShippingThriftService.resetPoiShipping start, request: {}", request);
            result = channelPoiShippingThriftService.resetPoiShipping(request);
            log.info("invoke channelPoiShippingThriftService.resetPoiShipping end, result: {}", result);
        } catch (Exception e) {
            log.error("重置配送范围失败", e);
            throw new ThirdPartyException("重置配送范围失败");
        }

        if (Objects.isNull(result) || result.getCode() != 0) {
            throw new BizException("重置配送范围失败:" + result.getMsg());
        }

    }

    public static Boolean checkIsOpenedTargetChannel(PoiDetailInfoDTO poiDetailInfoDTO, Integer targetChannelId) {
        if (Objects.isNull(poiDetailInfoDTO)) {
            return false;
        }


        if (CollectionUtils.isEmpty(poiDetailInfoDTO.getChannelPoiInfoList())) {
            return false;
        }

        return poiDetailInfoDTO.getChannelPoiInfoList().stream()
                .filter(Objects::nonNull)
                .anyMatch(channelPoiInfoDTO -> Objects.equals(channelPoiInfoDTO.getChannelId(), targetChannelId));
    }

}
