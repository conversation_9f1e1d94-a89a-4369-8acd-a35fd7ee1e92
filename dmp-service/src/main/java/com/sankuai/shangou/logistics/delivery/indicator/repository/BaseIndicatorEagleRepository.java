package com.sankuai.shangou.logistics.delivery.indicator.repository;

import com.meituan.linz.boot.exception.BusinessException;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.linz.boot.util.Strings;
import com.sankuai.meituan.poros.client.PorosRestHighLevelClient;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorDTO;
import com.sankuai.shangou.logistics.delivery.indicator.repository.dbo.IndicatorDoc;
import com.sankuai.shangou.logistics.delivery.indicator.repository.es.AbstractEagleRepository;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.indicator.utils.IndicatorIndexNameUtils;
import com.sankuai.shangou.logistics.delivery.indicator.utils.MccUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/6/29 16:05
 **/
@Component
@Slf4j
public class BaseIndicatorEagleRepository extends AbstractEagleRepository<IndicatorDoc> {
    @Resource
    private PorosRestHighLevelClient esClientRepository;

    @Value("${es.indicatorIndex}")
    private String indicatorIndex;

    /**
     * 批量查询
     */
    public Map<LocalDateTime, List<IndicatorDTO>> queryIndicator(Long logisticsUnitId, List<LocalDateTime> bizTimeList,
                                                                 List<String> indicatorCodes) {
        try {
            long offset = 0;
            int pageSize = 5000;
            List<IndicatorDTO> indicatorDTOS = new ArrayList<>();
            while (offset < Integer.MAX_VALUE) {
                SearchSourceBuilder searchBuilder = new SearchSourceBuilder()
                        .fetchSource(true)
                        .query(toQueryBuilder(logisticsUnitId, bizTimeList, indicatorCodes))
                        .from((int) offset)
                        .size(pageSize);
                ;
                log.info("searchRequest: {}", searchBuilder);

                EsSearchResult<IndicatorDoc> esSearchResult = search(searchBuilder);

                if (CollectionUtils.isNotEmpty(esSearchResult.getResultList())) {
                    indicatorDTOS.addAll(IListUtils.mapTo(esSearchResult.getResultList(), IndicatorDTO::fromDoc));
                }

                if (esSearchResult.getTotalHits() < offset + pageSize) {
                    break;
                }

                offset += pageSize;
            }
            return indicatorDTOS.stream().collect(Collectors.groupingBy(IndicatorDTO::getBizTime));

        } catch (IOException e) {
            throw new BusinessException(Strings.of("查询配送负载原子指标失败, logisticsUnitId:{}, bizTimeList:{} , indicatorCodes:{}", logisticsUnitId,
                    bizTimeList, indicatorCodes), e);
        }
    }


    private IndicatorDTO doc2Dto(IndicatorDoc doc) {
       return IndicatorDTO.builder().bizTime(doc.getBizTime())
                .code(doc.getCode())
                .property(doc.getProperty())
                .logisticsUnitId(doc.getLogisticsUnitId())
                .calculateTime(doc.getCalculateTime())
                .bizTime(doc.getBizTime())
                .value(doc.getValue())
                .fragmentName(doc.getFragmentName())
                .build();
    }

    private BoolQueryBuilder toQueryBuilder(Long logisticsUnitId, List<LocalDateTime> bizTimeList,
                                           List<String> indicatorCodes) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        Bssert.throwIfNull(logisticsUnitId, "logisticsUnitId is null");
        Bssert.throwIfEmpty(indicatorCodes, "indicatorCodes is empty");
        Bssert.throwIfEmpty(bizTimeList, "bizTime is empty");

        boolQueryBuilder.filter(QueryBuilders.termQuery("logisticsUnitId", logisticsUnitId));
        boolQueryBuilder.filter(QueryBuilders.termsQuery("code", indicatorCodes));
        boolQueryBuilder.filter(QueryBuilders.termsQuery("bizTime", bizTimeList));

        return boolQueryBuilder;
    }

    @Override
    protected PorosRestHighLevelClient getEsClientRepository() {
        return esClientRepository;
    }

    @Override
    protected String getReadIndicatorIndex() {
        if(!MccUtils.getMonthDimensionIndexSwitch()) {
            return indicatorIndex;
        }

        return MccUtils.getLast3MonthsIndexAlias();
    }

    @Override
    protected String getWriteIndicatorIndex(IndicatorDoc indicatorDoc) {
        if (!MccUtils.getMonthDimensionIndexSwitch()) {
            return indicatorIndex;
        }

        return IndicatorIndexNameUtils.getIndexName(indicatorDoc.getBizTime());
    }
}
