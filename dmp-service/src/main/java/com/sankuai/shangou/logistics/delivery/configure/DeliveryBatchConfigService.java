package com.sankuai.shangou.logistics.delivery.configure;

import com.github.pagehelper.PageInfo;
import com.sankuai.shangou.logistics.delivery.configure.pojo.request.DeliveryConfigTaskDetailParam;
import com.sankuai.shangou.logistics.delivery.configure.pojo.request.QueryBatchTaskItemPageParam;
import com.sankuai.shangou.logistics.delivery.configure.pojo.vo.BatchTaskItemVo;
import com.sankuai.shangou.logistics.delivery.configure.pojo.vo.BatchTaskVo;
import com.sankuai.shangou.logistics.delivery.configure.pojo.vo.BatchTemplateVo;
import com.sankuai.shangou.logistics.delivery.configure.pojo.request.DeliveryConfigBatchSaveParam;
import com.sankuai.shangou.logistics.delivery.configure.pojo.request.QueryBatchTaskParam;
import com.sankuai.shangou.logistics.delivery.configure.pojo.vo.DeliveryConfigTaskDetailVo;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-25
 */
public interface DeliveryBatchConfigService {

    PageInfo<BatchTemplateVo> queryBatchTemplateList(QueryBatchTaskParam req, Long tenantId);

    PageInfo<BatchTaskVo> queryBatchTaskPoiSettingList(QueryBatchTaskParam req, Long tenantId);

    DeliveryConfigTaskDetailVo queryBatchTaskDetail(DeliveryConfigTaskDetailParam req);

    PageInfo<BatchTaskItemVo> queryBatchTaskItemList(QueryBatchTaskItemPageParam req);

    void batchSaveQnhConfig(DeliveryConfigBatchSaveParam param);

    /**
     * 根据模板同步配送配置
     */
    void syncDeliveryConfigByTemplateTask(Long configTaskId);

    void batchPoiConfigTask(Long configTaskId);
}
