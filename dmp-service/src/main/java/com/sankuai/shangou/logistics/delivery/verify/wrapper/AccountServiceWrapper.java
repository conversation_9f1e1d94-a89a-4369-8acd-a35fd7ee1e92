package com.sankuai.shangou.logistics.delivery.verify.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.sac.dto.request.authenticate.AccountAuthPermissionsRequest;
import com.meituan.shangou.sac.dto.request.search.AccountSearchRequest;
import com.meituan.shangou.sac.dto.response.authenticate.AccountAuthPermissionsResponse;
import com.meituan.shangou.sac.dto.response.search.AccountSearchResponse;
import com.meituan.shangou.sac.thrift.authenticate.AuthenticateService;
import com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AccountThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountValueVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.response.GetAccountResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.swing.*;
import javax.swing.text.html.Option;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/7/23 14:20
 **/
@Rhino
@Slf4j
public class AccountServiceWrapper {
    @Resource
    private AccountThriftService.Iface authAccountThriftService;

    @Resource
    private AuthenticateService authenticateService;

    @Resource
    private SacAccountSearchThriftService sacAccountSearchThriftService;

    // 单次查询返回结果的最大数量
    private static final Integer SEARCH_SIZE = 1000;

    @Degrade(rhinoKey = "AccountServiceWrapper.queryAccountByName", fallBackMethod = "queryAccountByNameFallback", timeoutInMilliseconds = 2000)
    public Optional<Long> queryAccountByNameOld(String accountName) {
        GetAccountResponse response;
        try {
            log.info("start invoke accountThriftService.getAccountByName, accountName: {}", accountName);
            response = authAccountThriftService.getAccountByName(accountName);
            log.info("end invoke accountThriftService.getAccountByName, response: {}", response);
        } catch (Exception e) {
            log.info("查询账号信息失败", e);
            throw new ThirdPartyException("查询账号信息失败");
        }

        if(response.getResult().getCode() != 0) {
            throw new BizException("查询账号信息失败," + response.getResult().getMsg());
        }

        if (response.getAccountValueVO() != null) {
            return Optional.of(response.getAccountValueVO().getAccountId());
        }

        return Optional.empty();
    }

    @Degrade(rhinoKey = "AccountServiceWrapper.queryAccountByName", fallBackMethod = "queryAccountByNameFallbackNew", timeoutInMilliseconds = 2000)
    public Optional<Long> queryAccountByNameNew(Long tenantId ,String accountName) {
        AccountSearchRequest tRequest = new AccountSearchRequest();
        tRequest.setTenantId(tenantId);
        tRequest.setAccountNames(Collections.singletonList(accountName));
        tRequest.setSize(SEARCH_SIZE);

        AccountSearchResponse response;
        try {
            log.info("start invoke sacAccountSearchThriftService.search, accountName: {}", accountName);
            response = sacAccountSearchThriftService.search(tRequest);
            log.info("end invoke sacAccountSearchThriftService.search, response: {}", response);
        } catch (Exception e) {
            log.info("查询账号信息失败", e);
            throw new ThirdPartyException("查询账号信息失败");
        }

        if(response.getSacStatus().getCode() != 0) {
            throw new BizException("查询账号信息失败," + response.getSacStatus().getMessage());
        }

        if (response.getData() != null) {
            return Optional.of(response.getData().get(0).getAccountId());
        }

        return Optional.empty();
    }

    @Degrade(rhinoKey = "AccountServiceWrapper.queryPermissionByAuthCode", fallBackMethod = "queryPermissionByAuthCodeFallback", timeoutInMilliseconds = 2000)
    public Map<String, Boolean> queryPermissionByAuthCode(Long accountId, Integer appId, List<String> permissionCode) {
        AccountAuthPermissionsRequest request = new AccountAuthPermissionsRequest();
        request.setAccountId(accountId);
        request.setAppId(appId);
        request.setPermissionCodes(permissionCode);
        log.info("start invoke authenticateService.accountAuthPermissions, request: {}", request);
        AccountAuthPermissionsResponse response = authenticateService.accountAuthPermissions(request);
        log.info("end invoke authenticateService.accountAuthPermissions, response: {}", response);
        if (response.getSacStatus().getCode() != 0) {
            throw new BizException("查询权限失败");
        }

        return response.getAuthResult();
    }

    public Map<String, Boolean> queryPermissionByAuthCodeFallback(Long accountId, Integer appId, List<String> permissionCode) {
        log.info("AccountServiceWrapper.queryPermissionByAuthCodeFallback 发生降级");
        return Collections.emptyMap();
    }

    public Optional<Long> queryAccountByNameFallback(String accountName) {
        log.error("AccountServiceWrapper.queryAccountByName 发生降级");
        return Optional.empty();
    }

    public Optional<Long> queryAccountByNameFallbackNew(Long tenantId, String accountName) {
        log.error("AccountServiceWrapper.queryAccountByName 发生降级, tenantId: {}, accountName: {}", tenantId, accountName);
        return Optional.empty();
    }

}
