package com.sankuai.shangou.logistics.delivery.configure.service.converter;

import com.google.common.collect.Sets;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.model.value.AssessTimeConfig;
import com.sankuai.shangou.logistics.delivery.configure.model.value.ExpressionCondition;
import com.sankuai.shangou.logistics.delivery.configure.model.value.ExpressionNode;
import com.sankuai.shangou.logistics.delivery.configure.model.value.interval.Interval;
import com.sankuai.shangou.logistics.delivery.configure.model.value.interval.IntervalNumber;
import com.sankuai.shangou.logistics.delivery.configure.model.value.interval.IntervalTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-07-18
 * @email <EMAIL>
 */
@Slf4j
@Component
public class AssessTimeConfigConverter {

    /**
     * orderTag相关的标识符前缀，用于识别orderTag条件
     * 可以根据实际业务需求调整这个列表
     */
    private static final Set<String> ORDER_TAG_IDENTIFIERS = Sets.newHashSet(
            "${order_tag}",
            "${order_label}",
            "${order_mark}"
    );

    public static DeliveryConfigDetailVO.AssertTimeVO convertToVO(AssessTimeConfig assessTimeConfig) {
        if (assessTimeConfig == null) {
            return null;
        }

        DeliveryConfigDetailVO.AssertTimeVO vo = new DeliveryConfigDetailVO.AssertTimeVO();
        vo.setType(assessTimeConfig.getType());
        vo.setHint(assessTimeConfig.getHint());

        ExpressionNode expressionNode = assessTimeConfig.getExpressionNode();
        if (expressionNode == null) {
            vo.setOrderTags(Collections.emptyList());
            vo.setDistanceConfigVOS(Collections.emptyList());
            return vo;
        }

        // 解析表达式树
        ExpressionTreeParseResult parseResult = parseExpressionTree(expressionNode);

        // 设置orderTags
        vo.setOrderTags(parseResult.getOrderTags());

        // 设置distanceConfigVOS
        vo.setDistanceConfigVOS(parseResult.getDistanceConfigs());

        return vo;
    }

    /**
     * 将VO转换为AssessTimeConfig
     */
    public static AssessTimeConfig convertFromVO(DeliveryConfigDetailVO.AssertTimeVO vo) {
        if (vo == null) {
            return null;
        }

        AssessTimeConfig config = new AssessTimeConfig();
        config.setType(vo.getType());
        config.setHint(vo.getHint());

        // 构建表达式树
        ExpressionNode expressionNode = buildExpressionTree(vo.getOrderTags(), vo.getDistanceConfigVOS());
        config.setExpressionNode(expressionNode);

        return config;
    }

    /**
     * 解析表达式树，提取orderTags和distanceConfigs
     */
    private static ExpressionTreeParseResult parseExpressionTree(ExpressionNode rootNode) {
        ExpressionTreeParseResult result = new ExpressionTreeParseResult();

        // 深度优先遍历，收集所有从根到叶子的路径
        List<PathInfo> allPaths = new ArrayList<>();
        collectAllPaths(rootNode, new ArrayList<>(), allPaths);

        // 分析路径，提取orderTags和distanceConfigs
        Set<Integer> orderTagsSet = new HashSet<>();
        List<DeliveryConfigDetailVO.DistanceConfigVO> distanceConfigs = new ArrayList<>();

        for (PathInfo path : allPaths) {
            // 提取路径中的orderTag条件
            List<Integer> pathOrderTags = extractOrderTagsFromPath(path.getConditions());
            orderTagsSet.addAll(pathOrderTags);

            // 构建DistanceConfigVO（包含非orderTag条件和公式）
            DeliveryConfigDetailVO.DistanceConfigVO distanceConfig = buildDistanceConfig(path);
            if (distanceConfig != null) {
                distanceConfigs.add(distanceConfig);
            }
        }

        result.setOrderTags(new ArrayList<>(orderTagsSet));
        result.setDistanceConfigs(distanceConfigs);

        return result;
    }

    /**
     * 深度优先遍历收集所有从根到叶子的路径
     */
    private static void collectAllPaths(ExpressionNode node, List<ExpressionCondition> currentPath, List<PathInfo> allPaths) {
        if (node == null) {
            return;
        }

        // 将当前节点的条件加入路径（根节点条件为null，不加入）
        List<ExpressionCondition> newPath = new ArrayList<>(currentPath);
        if (node.getCondition() != null) {
            newPath.add(node.getCondition());
        }

        // 如果是叶子节点（有公式），记录完整路径
        if (CollectionUtils.isEmpty(node.getSubs()) && StringUtils.isNotBlank(node.getFormula())) {
            PathInfo pathInfo = new PathInfo();
            pathInfo.setConditions(new ArrayList<>(newPath));
            pathInfo.setFormula(node.getFormula());
            allPaths.add(pathInfo);
            return;
        }

        // 继续遍历子节点
        if (CollectionUtils.isNotEmpty(node.getSubs())) {
            for (ExpressionNode subNode : node.getSubs()) {
                collectAllPaths(subNode, newPath, allPaths);
            }
        }
    }

    /**
     * 从路径条件中提取orderTag
     */
    private static List<Integer> extractOrderTagsFromPath(List<ExpressionCondition> conditions) {
        List<Integer> orderTags = new ArrayList<>();

        for (ExpressionCondition condition : conditions) {
            if (isOrderTagCondition(condition)) {
                List<Integer> tags = extractOrderTagValues(condition);
                orderTags.addAll(tags);
            }
        }

        return orderTags;
    }

    /**
     * 判断条件是否为orderTag条件
     */
    private static boolean isOrderTagCondition(ExpressionCondition condition) {
        if (condition == null || StringUtils.isBlank(condition.getIdentifier())) {
            return false;
        }

        return ORDER_TAG_IDENTIFIERS.contains(condition.getIdentifier());
    }

    /**
     * 从orderTag条件中提取具体的tag值
     */
    private static List<Integer> extractOrderTagValues(ExpressionCondition condition) {
        List<Integer> tags = new ArrayList<>();

        if (condition.getInterval() != null && CollectionUtils.isNotEmpty(condition.getInterval().values())) {
            for (String value : condition.getInterval().values()) {
                try {
                    tags.add(Integer.parseInt(value));
                } catch (NumberFormatException e) {
                    log.warn("无法解析orderTag值: {}", value, e);
                }
            }
        }

        return tags;
    }

    /**
     * 构建DistanceConfigVO，包含非orderTag条件和公式
     */
    private static DeliveryConfigDetailVO.DistanceConfigVO buildDistanceConfig(PathInfo pathInfo) {
        // 过滤出非orderTag条件
        List<ExpressionCondition> nonOrderTagConditions = pathInfo.getConditions().stream()
                .filter(condition -> !isOrderTagCondition(condition))
                .collect(Collectors.toList());

        // 如果没有非orderTag条件且没有公式，返回null
        if (CollectionUtils.isEmpty(nonOrderTagConditions) && StringUtils.isBlank(pathInfo.getFormula())) {
            return null;
        }

        DeliveryConfigDetailVO.DistanceConfigVO distanceConfig = new DeliveryConfigDetailVO.DistanceConfigVO();

        // 转换条件
        List<DeliveryConfigDetailVO.ConditionVO> conditionVOs = nonOrderTagConditions.stream()
                .map(AssessTimeConfigConverter::convertToConditionVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        distanceConfig.setCondition(conditionVOs);
        distanceConfig.setFormula(pathInfo.getFormula());

        return distanceConfig;
    }

    /**
     * 转换ExpressionCondition为ConditionVO
     */
    private static DeliveryConfigDetailVO.ConditionVO convertToConditionVO(ExpressionCondition expressionCondition) {
        if (expressionCondition == null) {
            return null;
        }

        DeliveryConfigDetailVO.ConditionVO conditionVO = new DeliveryConfigDetailVO.ConditionVO();
        conditionVO.setIdentifer(expressionCondition.getIdentifier());

        if (expressionCondition.getInterval() != null) {
            DeliveryConfigDetailVO.IntervalVO intervalVO = convertToIntervalVO(expressionCondition.getInterval());
            conditionVO.setInterval(intervalVO);
        }

        return conditionVO;
    }

    /**
     * 转换Interval为IntervalVO
     */
    private static DeliveryConfigDetailVO.IntervalVO convertToIntervalVO(Interval interval) {
        if (interval == null) {
            return null;
        }

        DeliveryConfigDetailVO.IntervalVO intervalVO = new DeliveryConfigDetailVO.IntervalVO();
        intervalVO.setValues(interval.values());
        intervalVO.setIntervalType(interval.getIntervalType() != null ? interval.getIntervalType().getType() : null);

        return intervalVO;
    }

    /**
     * 根据orderTags和distanceConfigs构建表达式树
     */
    private static ExpressionNode buildExpressionTree(List<Integer> orderTags, List<DeliveryConfigDetailVO.DistanceConfigVO> distanceConfigs) {
        // 创建根节点
        ExpressionNode rootNode = new ExpressionNode();
        rootNode.setCondition(null); // 根节点条件为空
        rootNode.setFormula(null);

        if (CollectionUtils.isEmpty(distanceConfigs)) {
            rootNode.setSubs(Collections.emptyList());
            return rootNode;
        }

        List<ExpressionNode> rootChildren = new ArrayList<>();

        // 如果有orderTags，需要为每个distanceConfig创建orderTag分支
        if (CollectionUtils.isNotEmpty(orderTags)) {
            for (DeliveryConfigDetailVO.DistanceConfigVO distanceConfig : distanceConfigs) {
                // 为每个orderTag创建分支
                for (Integer orderTag : orderTags) {
                    ExpressionNode branchNode = buildBranchWithOrderTag(orderTag, distanceConfig);
                    if (branchNode != null) {
                        rootChildren.add(branchNode);
                    }
                }
            }
        } else {
            // 没有orderTags，直接创建distanceConfig分支
            for (DeliveryConfigDetailVO.DistanceConfigVO distanceConfig : distanceConfigs) {
                ExpressionNode branchNode = buildBranchFromDistanceConfig(distanceConfig);
                if (branchNode != null) {
                    rootChildren.add(branchNode);
                }
            }
        }

        rootNode.setSubs(rootChildren);
        return rootNode;
    }

    /**
     * 构建包含orderTag的分支
     */
    private static ExpressionNode buildBranchWithOrderTag(Integer orderTag, DeliveryConfigDetailVO.DistanceConfigVO distanceConfig) {
        if (orderTag == null || distanceConfig == null) {
            return null;
        }

        // 创建orderTag条件节点
        ExpressionNode orderTagNode = new ExpressionNode();
        orderTagNode.setCondition(createOrderTagCondition(orderTag));
        orderTagNode.setFormula(null);

        // 创建子节点（距离配置）
        ExpressionNode childNode = buildBranchFromDistanceConfig(distanceConfig);
        if (childNode != null) {
            orderTagNode.setSubs(Collections.singletonList(childNode));
        } else {
            orderTagNode.setSubs(Collections.emptyList());
        }

        return orderTagNode;
    }

    /**
     * 从DistanceConfigVO构建分支
     */
    private static ExpressionNode buildBranchFromDistanceConfig(DeliveryConfigDetailVO.DistanceConfigVO distanceConfig) {
        if (distanceConfig == null) {
            return null;
        }

        List<DeliveryConfigDetailVO.ConditionVO> conditions = distanceConfig.getCondition();
        String formula = distanceConfig.getFormula();

        // 如果没有条件，创建只有公式的叶子节点
        if (CollectionUtils.isEmpty(conditions)) {
            if (StringUtils.isNotBlank(formula)) {
                ExpressionNode leafNode = new ExpressionNode();
                leafNode.setCondition(null);
                leafNode.setSubs(Collections.emptyList());
                leafNode.setFormula(formula);
                return leafNode;
            }
            return null;
        }

        // 构建条件链：condition1 -> condition2 -> ... -> formula
        return buildConditionChain(conditions, formula, 0);
    }

    /**
     * 递归构建条件链
     */
    private static ExpressionNode buildConditionChain(List<DeliveryConfigDetailVO.ConditionVO> conditions, String formula, int index) {
        if (index >= conditions.size()) {
            // 到达条件链末尾，创建公式节点
            if (StringUtils.isNotBlank(formula)) {
                ExpressionNode leafNode = new ExpressionNode();
                leafNode.setCondition(null);
                leafNode.setSubs(Collections.emptyList());
                leafNode.setFormula(formula);
                return leafNode;
            }
            return null;
        }

        DeliveryConfigDetailVO.ConditionVO conditionVO = conditions.get(index);
        ExpressionCondition expressionCondition = convertFromConditionVO(conditionVO);

        ExpressionNode currentNode = new ExpressionNode();
        currentNode.setCondition(expressionCondition);
        currentNode.setFormula(null);

        // 递归构建下一级
        ExpressionNode childNode = buildConditionChain(conditions, formula, index + 1);
        if (childNode != null) {
            currentNode.setSubs(Collections.singletonList(childNode));
        } else {
            currentNode.setSubs(Collections.emptyList());
            // 如果没有子节点且是最后一个条件，设置公式
            if (index == conditions.size() - 1 && StringUtils.isNotBlank(formula)) {
                currentNode.setFormula(formula);
            }
        }

        return currentNode;
    }

    /**
     * 创建orderTag条件
     */
    private static ExpressionCondition createOrderTagCondition(Integer orderTag) {
        ExpressionCondition condition = new ExpressionCondition();
        condition.setIdentifier("${order_tag}"); // 使用默认的orderTag标识符
        condition.setName("订单标签");
        condition.setFormula(null);

        // 创建等于条件的区间
        Interval interval = new Interval();
        interval.setIntervalType(IntervalTypeEnum.EQUAL.getType());
        interval.setValues(Collections.singletonList(new IntervalNumber(orderTag.toString())));

        condition.setInterval(interval);
        return condition;
    }

    /**
     * 将ConditionVO转换为ExpressionCondition
     */
    private static ExpressionCondition convertFromConditionVO(DeliveryConfigDetailVO.ConditionVO conditionVO) {
        if (conditionVO == null) {
            return null;
        }

        ExpressionCondition condition = new ExpressionCondition();
        condition.setIdentifier(conditionVO.getIdentifer());
        condition.setName(null); // VO中没有name字段，设为null
        condition.setFormula(null);

        if (conditionVO.getInterval() != null) {
            Interval interval = convertFromIntervalVO(conditionVO.getInterval());
            condition.setInterval(interval);
        }

        return condition;
    }

    /**
     * 将IntervalVO转换为Interval
     */
    private static Interval convertFromIntervalVO(DeliveryConfigDetailVO.IntervalVO intervalVO) {
        if (intervalVO == null) {
            return null;
        }

        Interval interval = new Interval();

        // 设置区间类型
        if (intervalVO.getIntervalType() != null) {
            interval.setIntervalType(intervalVO.getIntervalType());
        }

        // 设置区间值
        if (CollectionUtils.isNotEmpty(intervalVO.getValues())) {
            List<IntervalNumber> intervalNumbers = intervalVO.getValues().stream()
                    .map(IntervalNumber::new)
                    .collect(Collectors.toList());
            interval.setValues(intervalNumbers);
        }

        return interval;
    }

    /**
     * 路径信息，包含条件列表和公式
     */
    private static class PathInfo {
        private List<ExpressionCondition> conditions;
        private String formula;

        public List<ExpressionCondition> getConditions() {
            return conditions;
        }

        public void setConditions(List<ExpressionCondition> conditions) {
            this.conditions = conditions;
        }

        public String getFormula() {
            return formula;
        }

        public void setFormula(String formula) {
            this.formula = formula;
        }
    }

    /**
     * 表达式树解析结果
     */
    private static class ExpressionTreeParseResult {
        private List<Integer> orderTags;
        private List<DeliveryConfigDetailVO.DistanceConfigVO> distanceConfigs;

        public List<Integer> getOrderTags() {
            return orderTags;
        }

        public void setOrderTags(List<Integer> orderTags) {
            this.orderTags = orderTags;
        }

        public List<DeliveryConfigDetailVO.DistanceConfigVO> getDistanceConfigs() {
            return distanceConfigs;
        }

        public void setDistanceConfigs(List<DeliveryConfigDetailVO.DistanceConfigVO> distanceConfigs) {
            this.distanceConfigs = distanceConfigs;
        }
    }
}
