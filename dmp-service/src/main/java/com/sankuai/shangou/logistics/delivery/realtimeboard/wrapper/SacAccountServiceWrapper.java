package com.sankuai.shangou.logistics.delivery.realtimeboard.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.sac.dto.model.SacAccountDto;
import com.meituan.shangou.sac.dto.response.search.SacAccountListResponse;
import com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/10 17:20
 **/
@Rhino
@Slf4j
public class SacAccountServiceWrapper {
    @Resource
    private SacAccountSearchThriftService sacAccountSearchThriftService;

    @Degrade(rhinoKey = "SacAccountServiceWrapper.batchQueryAccountByAccountIdList", fallBackMethod = "batchQueryAccountByAccountIdListFallback", timeoutInMilliseconds = 2000)
    public List<SacAccountDto> batchQueryAccountByAccountIdList(List<Long> accountIds) {
        log.info("start invoke sacAccountSearchThriftService.batchQueryAccountByAccountIdList, accountIds: {}", accountIds);
        SacAccountListResponse response = sacAccountSearchThriftService.batchQueryAccountByAccountIdList(accountIds);
        log.info("end invoke sacAccountSearchThriftService.batchQueryAccountByAccountIdList, response: {}", response);

        if (response.getSacStatus().getCode() != 0) {
            throw new BizException("查询账号信息失败");
        }

        return response.getAccountDtoList();
    }

    public List<SacAccountDto> batchQueryAccountByAccountIdListFallback(List<Long> accountIds) {
        log.error("SacAccountServiceWrapper.batchQueryAccountByAccountIdList 发生降级");
        return Collections.emptyList();
    }
}
