package com.sankuai.shangou.logistics.delivery.poi;

import com.dianping.cat.Cat;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.dto.DepartmentV2Dto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiBaseInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiListResponse;
import com.sankuai.drunkhorsemgmt.labor.types.Result;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.commons.utils.rpc.RpcInvokeUtils;
import com.sankuai.shangou.logistics.delivery.constants.*;
import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigOpLogPO;
import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPO;
import com.sankuai.shangou.logistics.delivery.poi.client.OpenAggDeliveryClient;
import com.sankuai.shangou.logistics.delivery.poi.client.PoiQueryClient;
import com.sankuai.shangou.logistics.delivery.poi.domain.SelfDeliveryPoiConfig;
import com.sankuai.shangou.logistics.delivery.poi.factory.SelfDeliveryPoiConfigFactory;
import com.sankuai.shangou.logistics.delivery.poi.repository.SelfDeliveryPoiConfigOpLogMapperWrapper;
import com.sankuai.shangou.logistics.delivery.poi.repository.SelfDeliveryPoiConfigPOMapperWrapper;
import com.sankuai.shangou.logistics.delivery.poi.utils.DepUtils;
import com.sankuai.shangou.logistics.delivery.poi.utils.RhinoLimitUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-08-31
 * @email <EMAIL>
 */
@Slf4j
@Service
public class SelfDeliveryPoiConfigService {

    @Resource
    private SelfDeliveryPoiConfigPOMapperWrapper selfDeliveryPoiConfigPOMapperWrapper;
    @Resource
    private SelfDeliveryPoiConfigOpLogMapperWrapper selfDeliveryPoiConfigOpLogMapperWrapper;
    @Resource
    private PoiQueryClient poiQueryClient;
    @Resource
    private OpenAggDeliveryClient openAggDeliveryClient;

    @Resource
    private PoiThriftService poiThriftService;


    @MethodLog(logRequest = true, logResponse = true)
    public String queryDapStoreConfigUrl(long tenantId, long poiId, String token) {
        return openAggDeliveryClient.queryDapStoreConfig(tenantId, poiId, token);
    }

    @MethodLog(logRequest = true, logResponse = true)
    public String queryDapStoreSettingsLink(long tenantId, long poiId, String token, Long employeeId, String employeeName) {
        return openAggDeliveryClient.queryDapStoreSettings(tenantId, poiId, token, employeeId, employeeName);
    }

    @MethodLog(logRequest = true, logResponse = true)
    public SelfDeliveryPoiConfigPO querySelfDeliveryConfig(Long tenantId, Long poiId) {
        SelfDeliveryPoiConfigPO po = selfDeliveryPoiConfigPOMapperWrapper.querySelfDeliveryConfig(tenantId, poiId);
        if (Objects.isNull(po)) {
            return null;
        }
        return po;
    }

    @MethodLog(logRequest = true, logResponse = true)
    public PageResp<Pair<SelfDeliveryPoiConfigPO, String/*poiId*/>> querySelfDeliveryConfigList(long tenantId, long accountId, Integer enableTurnToDap, Integer enablePickDeliverySplit,
                                                                                                @Nullable List<Long> depIdList, PageReq pageReq) {
        Map<Long, String> poiIdAndNameMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(depIdList)) {
            List<PoiDetailInfoDTO> poiDetailInfoDTOS = poiQueryClient.queryAllPermissionPoiList(tenantId, accountId, Constants.BAI_CHUAN_APP_ID);
            poiIdAndNameMap = IListUtils.nullSafeAndOverrideCollectToMap(
                    poiDetailInfoDTOS, PoiDetailInfoDTO::getPoiId, PoiDetailInfoDTO::getPoiName
            );
        } else {
            Map<Long, List<DepartmentV2Dto>> depParentMap = poiQueryClient.getDepsGroupByParentId(tenantId);

            // 需要查询dep及其下级的组织ID集合
            List<Long> departmentIdsAndNext = DepUtils.getDepIdsIncludeNext(depIdList, depParentMap, 0);
            List<PoiBaseInfoDto> poiBaseInfoDtos = poiQueryClient.queryPoiByDepIdList(tenantId, departmentIdsAndNext);
            poiIdAndNameMap = IListUtils.nullSafeAndOverrideCollectToMap(
                    poiBaseInfoDtos, PoiBaseInfoDto::getPoiId, PoiBaseInfoDto::getPoiName
            );
        }

        if (MapUtils.isEmpty(poiIdAndNameMap)) {
            return new PageResp<>(Lists.newArrayList(), 0, false);
        }
        PageInfo<SelfDeliveryPoiConfigPO> selfDeliveryPoiConfigPOPageInfo = selfDeliveryPoiConfigPOMapperWrapper.pageQuerySelfDeliveryConfigList(
                Lists.newArrayList(poiIdAndNameMap.keySet()), enableTurnToDap, enablePickDeliverySplit, pageReq.getPage(), pageReq.getPageSize()
        );

        Map<Long, String> finalPoiIdAndNameMap = poiIdAndNameMap;
        return new PageResp<>(IListUtils.mapTo(
                selfDeliveryPoiConfigPOPageInfo.getList(),
                po -> Pair.of(po, finalPoiIdAndNameMap.getOrDefault(po.getPoiId(), StringUtils.EMPTY))
                ),
                (int)selfDeliveryPoiConfigPOPageInfo.getTotal(),selfDeliveryPoiConfigPOPageInfo.isHasNextPage()
        );
    }

    @MethodLog(logRequest = true, logResponse = true)
    public void batchUpdateNeedTurnDelivery(Long tenantId, List<Long> poiIds, int enableTurnDelivery, Operator operator) {
        selfDeliveryPoiConfigPOMapperWrapper.batchUpdateNeedTurnDelivery(tenantId, poiIds, enableTurnDelivery, operator);
        insertOpLogs(tenantId, poiIds,
                enableTurnDelivery == 1 ? SelfDeliveryPoiConfigOpTypeEnum.ENABLE_TURN_DELIVERY : SelfDeliveryPoiConfigOpTypeEnum.DISABLE_TURN_DELIVERY,
                operator
        );
    }

    @MethodLog(logRequest = true, logResponse = true)
    public void batchUpdatePickDeliverySplit(Long tenantId, List<Long> poiIds, Integer enablePickDeliverySplit, Operator operator) {
        selfDeliveryPoiConfigPOMapperWrapper.batchUpdatePickDeliverySplit(tenantId, poiIds, enablePickDeliverySplit, operator);
        insertOpLogs(tenantId, poiIds,
                enablePickDeliverySplit == 1 ? SelfDeliveryPoiConfigOpTypeEnum.ENABLE_PICK_DELIVERY_SPLIT : SelfDeliveryPoiConfigOpTypeEnum.DISABLE_PICK_DELIVERY_SPLIT,
                operator
        );
    }

    private void insertOpLogs(Long tenantId, List<Long> poiIds, SelfDeliveryPoiConfigOpTypeEnum opTypeEnum, Operator operator) {
        for (Long poiId : poiIds) {
            try {
                SelfDeliveryPoiConfigOpLogPO logPO = new SelfDeliveryPoiConfigOpLogPO();
                logPO.setTenantId(tenantId);
                logPO.setPoiId(poiId);
                logPO.setOpType(opTypeEnum.getCode());
                logPO.setOperatorId(operator.getOperatorId());
                logPO.setOperatorName(operator.getOperatorName());
                logPO.setOpTime(LocalDateTime.now());
                selfDeliveryPoiConfigOpLogMapperWrapper.insert(logPO);
            } catch (Exception e) {
                log.error("selfDeliveryPoiConfigOpLogMapperWrapper.insert error,poiId = {}, opTypeEnum = {}", poiId, opTypeEnum.getDesc(), e);
            }
        }
    }


    @MethodLog(logRequest = true, logResponse = true)
    public int initConfig(Long tenantId) {
        Bssert.throwIfNull(tenantId, "租户id不能为空");

        PoiListResponse poiListResponse = RpcInvokeUtils.rpcInvokeTemplate(() -> poiThriftService.queryTenantPoiList(tenantId),
                "查询租户门店列表失败",
                resp -> resp.getStatus().getCode(), resp -> resp.getStatus().getMessage());
        List<PoiInfoDto> poiList = poiListResponse.getPoiList();

        List<SelfDeliveryPoiConfigPO> existPoiConfigList = selfDeliveryPoiConfigPOMapperWrapper.querySelfDeliveryConfig(tenantId);
        Map<Long, SelfDeliveryPoiConfigPO> existPoiConfigMap = existPoiConfigList.stream()
                .collect(Collectors.toMap(SelfDeliveryPoiConfigPO::getPoiId, Function.identity()));

        List<PoiInfoDto> unInitPoiList = poiList.stream()
                .filter(poiInfoDto -> !existPoiConfigMap.containsKey(poiInfoDto.getPoiId()))
                .collect(Collectors.toList());

        int batchSize = 50;
        List<List<PoiInfoDto>> poiPartitions = Lists.partition(unInitPoiList, batchSize);
        int operatePoiNum = 0;
        for (List<PoiInfoDto> poiInfoList : poiPartitions) {
            List<SelfDeliveryPoiConfig> configs = poiInfoList.stream()
                    .map(poiInfo -> SelfDeliveryPoiConfigFactory.initSelfDeliveryPoiConfig(tenantId, poiInfo.getPoiId()))
                    .collect(Collectors.toList());

            log.info("batch insert poiIdList: {}", poiInfoList);
            operatePoiNum += selfDeliveryPoiConfigPOMapperWrapper.batchInsert(configs);
        }

        return operatePoiNum;
    }


    @MethodLog(logRequest = true, logResponse = true)
    public int initConfig(Long tenantId, Long poiId) {
        Bssert.throwIfNull(tenantId, "租户id不能为空");
        Bssert.throwIfNull(poiId, "门店id不能为空");

        SelfDeliveryPoiConfigPO configPO = selfDeliveryPoiConfigPOMapperWrapper.querySelfDeliveryConfig(tenantId, poiId);
        if (Objects.nonNull(configPO)) {
            log.info("配置已存在, poiId:{}", poiId);
            return 0;
        }

        SelfDeliveryPoiConfig deliveryPoiConfig = SelfDeliveryPoiConfigFactory.initSelfDeliveryPoiConfig(tenantId, poiId);

        return selfDeliveryPoiConfigPOMapperWrapper.batchInsert(Collections.singletonList(deliveryPoiConfig));
    }

    @MethodLog(logRequest = true, logResponse = true)
    public int syncDapDeliveryPoi(Long tenantId, List<Long> storeIds) {
        Bssert.throwIfNull(tenantId, "租户id不能为空");

        List<SelfDeliveryPoiConfigPO> unSyncToDapStoreList =
                selfDeliveryPoiConfigPOMapperWrapper.queryUnSyncToDapStoreList(tenantId, storeIds);

        List<Long> successStoreIds = new ArrayList<>();;
        for (SelfDeliveryPoiConfigPO poiConfigPO : unSyncToDapStoreList) {
            try {
                //限频调用
                RhinoLimitUtils.syncDapLimiter(1);
                openAggDeliveryClient.syncDapStore(poiConfigPO.getTenantId(), poiConfigPO.getPoiId());
                successStoreIds.add(poiConfigPO.getPoiId());
            } catch (Exception e) {
                Cat.logEvent("DH_ADAPT_DAP", "SYNC_DAP_STORE_FAIL");
                log.warn("同步青云门店失败, tenantId: {}, storeId:{}", tenantId, poiConfigPO.getPoiId());
            }
        }

        return selfDeliveryPoiConfigPOMapperWrapper.batchUpdateDapSyncStatus(tenantId, successStoreIds,
                DapSyncStatusEnum.SYNCED, Operator.system);
    }

}
