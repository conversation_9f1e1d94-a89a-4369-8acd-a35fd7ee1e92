package com.sankuai.shangou.logistics.delivery.configure.pojo.request;

import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.logistics.delivery.common.enums.DimensionEnum;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-03
 */
@Data
public class DeliveryConfigTaskDetailParam {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * tab类型： 1-按租户设置,2-按经营模式设置,3-按指定门店设置
     *
     * @see DimensionEnum
     */
    private Integer tabType;

    public void validate() {
        if (Objects.isNull(taskId)) {
            throw new BizException("任务ID不能为空");
        }
        if (Objects.isNull(DimensionEnum.enumOf(tabType))) {
            throw new BizException("tab类型不能为空");
        }
    }
}
