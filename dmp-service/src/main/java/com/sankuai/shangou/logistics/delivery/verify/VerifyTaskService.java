package com.sankuai.shangou.logistics.delivery.verify;

import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.thrift.publisher.response.TPageResult;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import com.sankuai.shangou.logistics.delivery.common.wrapper.OswClient;
import com.sankuai.shangou.logistics.delivery.config.LionConfigUtils;
import com.sankuai.shangou.logistics.delivery.verify.config.MccConfigUtils;
import com.sankuai.shangou.logistics.delivery.verify.dto.TPageDTO;
import com.sankuai.shangou.logistics.delivery.verify.dto.PunishInfoDTO;
import com.sankuai.shangou.logistics.delivery.verify.dto.VerifyTaskViewAndExportDTO;
import com.sankuai.shangou.logistics.delivery.verify.request.VerifyTaskExportRequest;
import com.sankuai.shangou.logistics.delivery.verify.wrapper.AccountServiceWrapper;
import com.sankuai.shangou.logistics.delivery.verify.wrapper.PunishServiceWrapper;
import com.sankuai.shangou.logistics.sdms.sdk.config.dto.request.QueryByTaskIdRequest;
import com.sankuai.shangou.logistics.sdms.sdk.config.dto.request.QueryVerifyImageRequest;
import com.sankuai.shangou.logistics.sdms.sdk.verify.VerifyTaskThriftService;
import com.sankuai.shangou.logistics.sdms.sdk.verify.dto.TVerifyTaskDTO;
import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.CollectContentTypeEnum;
import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.CollectionModeEnum;
import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.PunishTypeEnum;
import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.VerifyTaskStatusEnum;
import com.sankuai.shangou.logistics.sdms.sdk.verify.request.SearchVerifyTaskRequest;
import com.sankuai.shangou.waima.support.api.pl.dto.punish.PunishTicketSimpleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/22 17:51
 **/
@Service
@Slf4j
public class VerifyTaskService {
    @Resource
    private VerifyTaskThriftService verifyTaskThriftService;

    @Resource
    private OswClient oswClient;

    @Resource
    private AccountServiceWrapper accountServiceWrapper;

    @Resource
    private PunishServiceWrapper punishServiceWrapper;

    private static final String PUNISH_DATA_SOURCE = "deliverySmile";

    private static final String VIEW_VERIFY_RESULT_ALL = "VIEW_VERIFY_RESULT_ALL";

    private static final String VIEW_VERIFY_RESULT_FAIL = "VIEW_VERIFY_RESULT_FAIL";

    private static final String VIEW_YODA_REQUEST_CODE = "VIEW_YODA_REQUEST_CODE";

    private static final String VIEW_DEVICE_CODE = "VIEW_DEVICE_CODE";

    private static final Integer APPEAL_PASS = 4;

    private static final int VERIFY_PASS = 1;
    private static final int VERIFY_FAIL = 0;

    public TPageDTO<VerifyTaskViewAndExportDTO> searchVerifyTask(VerifyTaskExportRequest request) {
        SearchVerifyTaskRequest tRequest = new SearchVerifyTaskRequest();
        tRequest.setPage(request.getPage());
        tRequest.setPageSize(request.getPageSize());
        tRequest.setTenantId(request.getTenantId());
        tRequest.setStoreIdList(request.getStoreIds());
        tRequest.setPushTimeBegin(TimeUtils.fromMilliSeconds(request.getTaskPushTimeBegin()));
        tRequest.setPushTimeEnd(TimeUtils.fromMilliSeconds(request.getTaskPushTimeEnd()));
        tRequest.setStatusList(request.getStatusList());
        tRequest.setTaskId(request.getTaskId());
        if (Objects.nonNull(request.getDressingIdentifyResult())) {
            tRequest.setDressingIdentifyResult(Objects.equals(request.getDressingIdentifyResult(), VERIFY_PASS));
        }
        if (Objects.nonNull(request.getHelmetIdentifyResult())) {
            tRequest.setHelmetIdentifyResult(Objects.equals(request.getHelmetIdentifyResult(), VERIFY_PASS));
        }
        if (Objects.nonNull(request.getFaceIdentifyResult())) {
            tRequest.setFaceIdentifyResult(Objects.equals(request.getFaceIdentifyResult(), VERIFY_PASS));
        }

        if (StringUtils.isNotBlank(request.getAccountName())) {
            Optional<Long> accountIdOpt;
            if (LionConfigUtils.isNewAuthApiGrayTenant(request.getTenantId())) {
                accountIdOpt = accountServiceWrapper.queryAccountByNameNew(request.getTenantId(), request.getAccountName());
            } else {
                accountIdOpt = accountServiceWrapper.queryAccountByNameOld(request.getAccountName());
            }
            if (accountIdOpt.isPresent()) {
                tRequest.setRiderAccountId(accountIdOpt.get());
            } else {
                log.info("未查询到骑手账号信息");
                return new TPageDTO<>(Collections.emptyList(), request.getPage(), request.getPageSize(), 0);
            }
        }

        log.info("start invoke verifyTaskThriftService.searchVerifyTask, request: {}", tRequest);
        TPageResult<TVerifyTaskDTO> tResult = verifyTaskThriftService.searchVerifyTask(tRequest);
        log.info("end invoke verifyTaskThriftService.searchVerifyTask, tResult: {}", tResult);

        if (!tResult.isSuccess()) {
            throw new BizException(tResult.getMsg());
        }

        if (CollectionUtils.isEmpty(tResult.getData().getList())) {
            return new TPageDTO(Collections.emptyList(), tResult.getData().getPage(), tResult.getData().getPageSize(), tResult.getData().getTotal().intValue());
        }

        //查询账号信息
        List<Long> riderAccountIds = tResult.getData().getList().stream().map(TVerifyTaskDTO::getRiderAccountId).collect(Collectors.toList());
        Map<Long, EmployeeDTO> employeeDTOMap = oswClient.batchQueryEmployeeInfo(request.getTenantId(), riderAccountIds);


        //查询门店信息
        List<Long> storeIds = tResult.getData().getList().stream().map(TVerifyTaskDTO::getStoreId).collect(Collectors.toList());
        Map<Long, WarehouseDTO> warehouseDTOMap = oswClient.queryWarehouseInfoList(request.getTenantId(), storeIds);

        //查权限点
        Map<String, Boolean> perimissionMap = accountServiceWrapper.queryPermissionByAuthCode(request.getAccountId(), request.getAppId(),
                Arrays.asList(VIEW_YODA_REQUEST_CODE, VIEW_DEVICE_CODE));

        //查落罚信息
        List<String> verifyTaskIds = tResult.getData().getList().stream()
                .map(TVerifyTaskDTO::getTaskId)
                .map(Objects::toString)
                .collect(Collectors.toList());
        List<String> dataSources = Arrays.stream(CollectContentTypeEnum.values())
                .map(value -> PUNISH_DATA_SOURCE + "_" + value.name())
                .collect(Collectors.toList());
        List<PunishTicketSimpleDto> punishTicketSimpleDtos = punishServiceWrapper.queryPunishTicketFormAssociation(request.getTenantId(),
                verifyTaskIds, dataSources);

        return buildVerifyTaskExportVO(tResult, employeeDTOMap, warehouseDTOMap, punishTicketSimpleDtos, perimissionMap);
    }

    private TPageDTO<VerifyTaskViewAndExportDTO> buildVerifyTaskExportVO(TPageResult<TVerifyTaskDTO> tResult,
                                                                       Map<Long, EmployeeDTO> employeeDTOMap,
                                                                       Map<Long, WarehouseDTO> warehouseDTOMap,
                                                                       List<PunishTicketSimpleDto> punishTicketSimpleDtos,
                                                                       Map<String, Boolean> perimissionMap) {
        Map<String, List<PunishTicketSimpleDto>> taskId2PunishTicketMap = punishTicketSimpleDtos.stream()
                .collect(Collectors.groupingBy(PunishTicketSimpleDto::getAssociationKey));
        List<TVerifyTaskDTO> verifyTaskDTOS = tResult.getData().getList();
        List<VerifyTaskViewAndExportDTO> taskVOList = verifyTaskDTOS.stream()
                .map(dto -> transform2ExportVO(dto, employeeDTOMap.get(dto.getRiderAccountId()),
                        warehouseDTOMap.get(dto.getStoreId()),
                        taskId2PunishTicketMap.getOrDefault(dto.getTaskId().toString(), Collections.emptyList()), perimissionMap))
                .collect(Collectors.toList());

        return new TPageDTO<>(taskVOList, tResult.getData().getPage(), tResult.getData().getPageSize(), tResult.getData().getTotal().intValue());

    }

    public String queryVerifyImageBase64(Long taskId, Long tenantId, Integer appId, Long accountId) {
        //验证是否有权限
        Map<String, Boolean> permissionMap = accountServiceWrapper.queryPermissionByAuthCode(
                accountId, appId,
                Arrays.asList(VIEW_VERIFY_RESULT_ALL, VIEW_VERIFY_RESULT_FAIL));

        if (permissionMap.getOrDefault(VIEW_VERIFY_RESULT_ALL, false)) {
            QueryVerifyImageRequest request = new QueryVerifyImageRequest();
            request.setTaskId(taskId);
            request.setOperatorId(accountId);
            TResult<String> tResult = verifyTaskThriftService.queryVerifyImageBase64(request);
            if (!tResult.isSuccess()) {
                log.error("invoke verifyTaskThriftService.queryVerifyImageBase64 fail, result: {}", tResult);
                throw new BizException("查询采集图片失败," + tResult.getMsg());
            }
            return tResult.getData();
        }

        if (permissionMap.getOrDefault(VIEW_VERIFY_RESULT_FAIL, false)) {
            QueryByTaskIdRequest request = new QueryByTaskIdRequest();
            request.setTaskId(taskId);
            TResult<TVerifyTaskDTO> tResult = verifyTaskThriftService.queryByTaskId(request);
            if (!tResult.isSuccess()) {
                throw new BizException("查询采集任务失败");
            }
            TVerifyTaskDTO tVerifyTaskDTO = tResult.getData();
            if (tVerifyTaskDTO.getDressingIdentifyResult() && tVerifyTaskDTO.getFaceIdentifyResult() && tVerifyTaskDTO.getHelmetIdentifyResult()) {
                throw new BizException("当前用户无权限查看采集图片");
            }

            QueryVerifyImageRequest queryVerifyImageRequest = new QueryVerifyImageRequest();
            queryVerifyImageRequest.setTaskId(taskId);
            queryVerifyImageRequest.setOperatorId(accountId);
            TResult<String> imageTResult = verifyTaskThriftService.queryVerifyImageBase64(queryVerifyImageRequest);
            if (!imageTResult.isSuccess()) {
                throw new BizException("查询采集图片失败");
            }

            return imageTResult.getData();
        }

        throw new BizException("当前用户无权限查看采集图片");
    }


    private VerifyTaskViewAndExportDTO transform2ExportVO(TVerifyTaskDTO dto, EmployeeDTO employeeDTO, WarehouseDTO warehouseDTO,
                                                          List<PunishTicketSimpleDto> punishTicketSimpleDtos,
                                                          Map<String, Boolean> perimissionMap) {
        Map<String, PunishTicketSimpleDto> ticketSimpleDtoMap = punishTicketSimpleDtos.stream()
                .collect(Collectors.toMap(PunishTicketSimpleDto::getTicketDataSourceCode, Function.identity(), (k1, k2) -> k2));

        VerifyTaskViewAndExportDTO viewAndExportDTO = new VerifyTaskViewAndExportDTO();
        viewAndExportDTO.setTaskId(dto.getTaskId().toString());
        viewAndExportDTO.setStoreName(Optional.ofNullable(warehouseDTO).map(WarehouseDTO::getName).orElse(null));
        viewAndExportDTO.setRiderAccountName(Optional.ofNullable(employeeDTO).map(EmployeeDTO::getAccountName).orElse(null));
        viewAndExportDTO.setRiderName(Optional.ofNullable(employeeDTO).map(EmployeeDTO::getEmpName).orElse(null));
        viewAndExportDTO.setStatus(dto.getTaskStatus());
        viewAndExportDTO.setStatusDesc(VerifyTaskStatusEnum.enumOf(dto.getTaskStatus()).getDesc());
        viewAndExportDTO.setChannelOrderId(dto.getChannelOrderId());
        viewAndExportDTO.setCollectMode(dto.getCollectMode());
        viewAndExportDTO.setCollectModeDesc(CollectionModeEnum.valueOfCode(dto.getCollectMode()).getDesc());
        viewAndExportDTO.setCollectContentList(dto.getCollectContents());
        viewAndExportDTO.setCollectContentDescList(dto.getCollectContents().stream().map(CollectContentTypeEnum::valueOfCode).map(CollectContentTypeEnum::getDesc).collect(Collectors.toList()));
        viewAndExportDTO.setChannelOrderId(dto.getChannelOrderId());
        viewAndExportDTO.setPushTime(TimeUtils.toMilliSeconds(dto.getTaskPushTime()));
        viewAndExportDTO.setPushTimeDesc(Optional.ofNullable(dto.getTaskPushTime()).map(time -> time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).orElse(""));
        viewAndExportDTO.setCompleteTime(TimeUtils.toMilliSeconds(dto.getTaskCompleteTime()));
        viewAndExportDTO.setCompleteTimeDesc(Optional.ofNullable(dto.getTaskCompleteTime()).map(time -> time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).orElse(""));
        viewAndExportDTO.setExpireTime(TimeUtils.toMilliSeconds(dto.getExpireTime()));
        viewAndExportDTO.setExpireTimeDesc(Optional.ofNullable(dto.getExpireTime()).map(time -> time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).orElse(""));

        viewAndExportDTO.setFaceIdentifyResult(dto.getFaceIdentifyResult());
        viewAndExportDTO.setFaceIdentifyResultDesc(boolean2String(dto.getFaceIdentifyResult()));
        viewAndExportDTO.setFaceIdentifyPunishInfo(buildPunishInfoDTO(CollectContentTypeEnum.FACE, dto, ticketSimpleDtoMap));
        viewAndExportDTO.setFaceIdentifyPunishInfoDesc(buildPunishInfoDesc(buildPunishInfoDTO(CollectContentTypeEnum.FACE, dto, ticketSimpleDtoMap), dto.getFaceIdentifyResult()));

        viewAndExportDTO.setDressingIdentifyResult(dto.getDressingIdentifyResult());
        viewAndExportDTO.setDressingIdentifyResultDesc(boolean2String(dto.getDressingIdentifyResult()));
        viewAndExportDTO.setDressingIdentifyPunishInfo(buildPunishInfoDTO(CollectContentTypeEnum.DRESS, dto, ticketSimpleDtoMap));
        viewAndExportDTO.setDressingIdentifyPunishInfoDesc(buildPunishInfoDesc(buildPunishInfoDTO(CollectContentTypeEnum.DRESS, dto, ticketSimpleDtoMap), dto.getDressingIdentifyResult()));

        viewAndExportDTO.setHelmetIdentifyResult(dto.getHelmetIdentifyResult());
        viewAndExportDTO.setHelmetIdentifyResultDesc(boolean2String(dto.getHelmetIdentifyResult()));
        viewAndExportDTO.setHelmetIdentifyPunishInfo(buildPunishInfoDTO(CollectContentTypeEnum.HELMET, dto, ticketSimpleDtoMap));
        viewAndExportDTO.setHelmetIdentifyPunishInfoDesc(buildPunishInfoDesc(buildPunishInfoDTO(CollectContentTypeEnum.HELMET, dto, ticketSimpleDtoMap), dto.getHelmetIdentifyResult()));

        //如果是过期 填入过期管控状态
        if (Objects.equals(viewAndExportDTO.getStatus(), VerifyTaskStatusEnum.EXPIRE.getCode())) {
            viewAndExportDTO.setTaskExpirePunishInfo(buildPunishInfoDTO(CollectContentTypeEnum.EXPIRE, dto, ticketSimpleDtoMap));
            viewAndExportDTO.setTaskExpirePunishInfoDesc(buildPunishInfoDesc(buildPunishInfoDTO(CollectContentTypeEnum.EXPIRE, dto, ticketSimpleDtoMap), null));
        } else {
            viewAndExportDTO.setTaskExpirePunishInfoDesc("无");
        }
        viewAndExportDTO.setCallbackCode(dto.getCallbackCode());
        viewAndExportDTO.setCallbackMsg(dto.getCallbackMsg());


        if (Objects.equals(viewAndExportDTO.getStatus(), VerifyTaskStatusEnum.COMPLETED.getCode())) {
            viewAndExportDTO.setImageIsExpire(TimeUtils.fromMilliSeconds(viewAndExportDTO.getCompleteTime()).plusDays(MccConfigUtils.getFaceImageExpireDuration()).isBefore(LocalDateTime.now()));
        }

        //是否展示requestCode
        if (perimissionMap.getOrDefault(VIEW_YODA_REQUEST_CODE, false)) {
            viewAndExportDTO.setRequestCode(dto.getRequestCode());
        }

        //是否展示uuid
        if (perimissionMap.getOrDefault(VIEW_DEVICE_CODE, false)) {
            viewAndExportDTO.setUuid(dto.getUuid());
        }

        viewAndExportDTO.setRiderNameWithAccountName(StringUtils.defaultString(viewAndExportDTO.getRiderName(), "")  + StringUtils.defaultString(viewAndExportDTO.getRiderAccountName(), ""));

        return viewAndExportDTO;
    }

    public static String boolean2String(Boolean boolVal) {
        if (boolVal == null) {
            return "-";
        }

        return boolVal ? "是" : "否";
    }
    private PunishInfoDTO buildPunishInfoDTO(CollectContentTypeEnum contentTypeEnum, TVerifyTaskDTO dto, Map<String, PunishTicketSimpleDto> ticketSimpleDtoMap) {
        //未管控直接返回null
        if (MapUtils.isEmpty(dto.getPunishInfoMap()) || !dto.getPunishInfoMap().containsKey(String.valueOf(contentTypeEnum.getCode()))) {
            return null;
        }

        Integer punishType = dto.getPunishInfoMap().get(String.valueOf(contentTypeEnum.getCode()));
        //如果是系统落罚并且生成了罚单 填入落罚id和罚单审核状态
        String dataSourceCode = PUNISH_DATA_SOURCE + "_" + contentTypeEnum.name();
        if (Objects.equals(punishType, PunishTypeEnum.BY_PUNISH_SYSTEM.getCode()) && ticketSimpleDtoMap.containsKey(dataSourceCode)) {
            PunishTicketSimpleDto faceIdentifyPunishTicket = ticketSimpleDtoMap.get(dataSourceCode);
            PunishInfoDTO punishInfoDTO = new PunishInfoDTO();
            punishInfoDTO.setPunishType(PunishTypeEnum.BY_PUNISH_SYSTEM.getCode());
            punishInfoDTO.setAppealStatus(faceIdentifyPunishTicket.getAppealStatus());
            punishInfoDTO.setPunishId(faceIdentifyPunishTicket.getId());
            return punishInfoDTO;
        }

        //如果是人工落罚
        if (Objects.equals(punishType, PunishTypeEnum.BY_MANUAL.getCode())) {
            PunishInfoDTO punishInfoDTO = new PunishInfoDTO();
            punishInfoDTO.setPunishType(PunishTypeEnum.BY_MANUAL.getCode());
            return punishInfoDTO;
        }

        return null;
    }

    public static String buildPunishInfoDesc(PunishInfoDTO punishInfoDTO, Boolean verifyResult) {
        if (Objects.equals(verifyResult, true)) {
            return "无";
        }

        if (punishInfoDTO == null) {
            return "-";
        }

        if (Objects.equals(punishInfoDTO.getPunishType(), PunishTypeEnum.BY_PUNISH_SYSTEM.getCode()) && Objects.nonNull(punishInfoDTO.getPunishId())) {
            if (Objects.equals(punishInfoDTO.getAppealStatus(), APPEAL_PASS)) {
                return "已落罚(申诉通过)";
            } else {
                return "已落罚";
            }
        }

        if (Objects.equals(punishInfoDTO.getPunishType(), PunishTypeEnum.BY_MANUAL.getCode())) {
            return "已通知";
        }

        return "-";
    }
}
