# ExpressionNode转换器使用说明

## 概述

`AssessTimeConfigConverter` 是一个用于将 `ExpressionNode` 表达式树转换为 VO 对象的转换器。它能够解析复杂的表达式树结构，提取其中的 orderTag 条件和其他条件，并将它们转换为前端可用的数据结构。

## 核心功能

### 1. ExpressionNode结构解析

ExpressionNode是一个树形结构：
- **根节点**：condition为null，作为整个表达式树的入口
- **中间节点**：包含条件condition，可能包含子节点
- **叶子节点**：包含条件condition和公式formula

### 2. 深度优先遍历

转换器使用深度优先遍历算法，收集从根节点到每个叶子节点的所有路径。每个路径代表一个完整的条件组合和对应的公式。

### 3. OrderTag识别

转换器能够识别特定的orderTag条件，当前支持的标识符包括：
- `${order_tag}`
- `${order_label}`
- `${order_mark}`

可以通过修改 `ORDER_TAG_IDENTIFIERS` 常量来调整识别规则。

### 4. 条件分类

转换器将条件分为两类：
- **OrderTag条件**：提取到 `orderTags` 字段中
- **其他条件**：与公式一起组成 `DistanceConfigVO`

## 使用方法

### 基本用法

#### ExpressionNode转VO（convertToVO）

```java
@Autowired
private AssessTimeConfigConverter converter;

public void convertToVOExample() {
    // 创建AssessTimeConfig
    AssessTimeConfig config = new AssessTimeConfig();
    config.setType(1);
    config.setHint("测试配置");
    config.setExpressionNode(expressionNode); // 设置表达式树

    // 转换为VO
    DeliveryConfigDetailVO.AssertTimeVO result = converter.convertToVO(config);

    // 获取结果
    List<Integer> orderTags = result.getOrderTags();
    List<DeliveryConfigDetailVO.DistanceConfigVO> distanceConfigs = result.getDistanceConfigVOS();
}
```

#### VO转ExpressionNode（convertFromVO）

```java
@Autowired
private AssessTimeConfigConverter converter;

public void convertFromVOExample() {
    // 创建VO
    DeliveryConfigDetailVO.AssertTimeVO vo = new DeliveryConfigDetailVO.AssertTimeVO();
    vo.setType(1);
    vo.setHint("测试配置");
    vo.setOrderTags(Arrays.asList(301, 302));

    // 创建距离配置
    DeliveryConfigDetailVO.DistanceConfigVO distanceConfig = new DeliveryConfigDetailVO.DistanceConfigVO();
    distanceConfig.setFormula("30");
    // ... 设置条件
    vo.setDistanceConfigVOS(Collections.singletonList(distanceConfig));

    // 转换为AssessTimeConfig
    AssessTimeConfig result = converter.convertFromVO(vo);

    // 获取表达式树
    ExpressionNode expressionNode = result.getExpressionNode();
}
```

#### 往返转换

```java
// 原始配置 -> VO -> 新配置
AssessTimeConfig originalConfig = createConfig();
DeliveryConfigDetailVO.AssertTimeVO vo = converter.convertToVO(originalConfig);
AssessTimeConfig newConfig = converter.convertFromVO(vo);
```

### 表达式树构建示例

#### 示例1：简单距离条件

```
根节点(null)
└── 距离条件[0,5] + 公式"30"
```

转换结果：
- orderTags: []
- distanceConfigs: [{ condition: [距离条件[0,5]], formula: "30" }]

#### 示例2：包含OrderTag

```
根节点(null)
└── OrderTag条件[301,302]
    └── 距离条件[0,5] + 公式"25"
```

转换结果：
- orderTags: [301, 302]
- distanceConfigs: [{ condition: [距离条件[0,5]], formula: "25" }]

#### 示例3：复杂多分支

```
根节点(null)
├── OrderTag条件[301]
│   └── 距离条件[0,3] + 公式"20"
└── OrderTag条件[302]
    └── 距离条件[3,10] + 公式"35"
```

转换结果：
- orderTags: [301, 302]
- distanceConfigs: [
    { condition: [距离条件[0,3]], formula: "20" },
    { condition: [距离条件[3,10]], formula: "35" }
  ]

## 反向转换说明（convertFromVO）

### 转换逻辑

`convertFromVO`方法将VO结构转换回ExpressionNode树结构，遵循以下规则：

1. **根节点创建**：始终创建一个condition为null的根节点
2. **OrderTag处理**：如果VO中有orderTags，为每个tag创建对应的条件节点
3. **分支构建**：为每个DistanceConfigVO创建对应的分支
4. **条件链构建**：将DistanceConfigVO中的多个条件按顺序构建成链式结构

### 转换示例

#### 示例1：简单VO转换

输入VO：
```json
{
  "type": 1,
  "hint": "测试",
  "orderTags": [],
  "distanceConfigVOS": [{
    "condition": [{"identifer": "${delivery_distance}", "interval": {"values": ["0", "5"], "intervalType": 4}}],
    "formula": "30"
  }]
}
```

转换结果ExpressionNode：
```
根节点(null)
└── 距离条件[0,5] + 公式"30"
```

#### 示例2：包含OrderTag的VO转换

输入VO：
```json
{
  "orderTags": [301],
  "distanceConfigVOS": [{
    "condition": [{"identifer": "${delivery_distance}", "interval": {"values": ["0", "5"], "intervalType": 4}}],
    "formula": "25"
  }]
}
```

转换结果ExpressionNode：
```
根节点(null)
└── OrderTag条件[301]
    └── 距离条件[0,5] + 公式"25"
```

#### 示例3：多OrderTag和多DistanceConfig

输入VO：
```json
{
  "orderTags": [301, 302],
  "distanceConfigVOS": [{
    "condition": [{"identifer": "${delivery_distance}", "interval": {"values": ["0", "3"], "intervalType": 4}}],
    "formula": "20"
  }]
}
```

转换结果ExpressionNode：
```
根节点(null)
├── OrderTag条件[301]
│   └── 距离条件[0,3] + 公式"20"
└── OrderTag条件[302]
    └── 距离条件[0,3] + 公式"20"
```

### 特殊情况处理

1. **空DistanceConfigs**：创建只有根节点的空树
2. **无OrderTags**：直接从DistanceConfigs创建分支
3. **多条件链**：按顺序构建条件链，最后一个条件节点包含公式
4. **空条件**：如果DistanceConfig没有条件，创建只包含公式的叶子节点

## 数据结构说明

### ExpressionNode
```java
public class ExpressionNode {
    private ExpressionCondition condition;  // 条件
    private List<ExpressionNode> subs;      // 子节点列表
    private String formula;                 // 公式（仅叶子节点有）
}
```

### ExpressionCondition
```java
public class ExpressionCondition {
    private String name;        // 条件名称
    private String identifier;  // 标识符（用于识别条件类型）
    private String formula;     // 条件公式
    private Interval interval;  // 区间值
}
```

### 转换结果VO
```java
public class AssertTimeVO {
    private Integer type;                                    // 类型
    private String hint;                                     // 提示
    private List<Integer> orderTags;                         // 订单标签列表
    private List<DistanceConfigVO> distanceConfigVOS;        // 距离配置列表
}

public class DistanceConfigVO {
    private List<ConditionVO> condition;  // 条件列表
    private String formula;               // 公式
}
```

## 注意事项

### convertToVO注意事项

1. **根节点处理**：根节点的condition必须为null，否则可能导致解析错误
2. **叶子节点识别**：只有同时满足"没有子节点"和"有公式"的节点才被认为是叶子节点
3. **OrderTag识别**：基于identifier字段进行识别，需要确保标识符格式正确
4. **空值处理**：转换器对null值有良好的处理，不会抛出异常
5. **性能考虑**：对于深度很大的表达式树，遍历可能会比较耗时

### convertFromVO注意事项

1. **OrderTag分支扩展**：如果有多个orderTags和多个distanceConfigs，会创建笛卡尔积的分支数量
2. **条件链顺序**：DistanceConfigVO中的条件列表顺序会影响生成的表达式树结构
3. **默认OrderTag标识符**：反向转换时使用`${order_tag}`作为默认标识符
4. **区间类型映射**：确保IntervalVO中的intervalType值与IntervalTypeEnum匹配
5. **公式位置**：公式总是放在条件链的最后一个节点上

### 往返转换兼容性

1. **数据完整性**：某些信息在往返转换中可能会丢失（如condition的name字段）
2. **结构变化**：反向转换可能会产生与原始结构略有不同的表达式树
3. **OrderTag合并**：多个相同条件的orderTag分支在正向转换时会被合并

## 扩展说明

### 添加新的OrderTag标识符

修改 `ORDER_TAG_IDENTIFIERS` 常量：

```java
private static final Set<String> ORDER_TAG_IDENTIFIERS = Sets.newHashSet(
    "${order_tag}",
    "${order_label}",
    "${order_mark}",
    "${new_order_identifier}"  // 添加新的标识符
);
```

### 自定义条件处理逻辑

可以重写 `isOrderTagCondition` 方法来实现更复杂的识别逻辑：

```java
private boolean isOrderTagCondition(ExpressionCondition condition) {
    // 自定义识别逻辑
    return condition != null && 
           condition.getIdentifier() != null && 
           condition.getIdentifier().contains("order");
}
```

## 测试

### 运行示例

运行 `ExpressionNodeExample` 类可以查看转换器的工作示例，包括：
- ExpressionNode到VO的转换示例
- VO到ExpressionNode的反向转换示例
- 往返转换测试

### 单元测试

运行 `AssessTimeConfigConverterTest` 类可以执行完整的单元测试，包括：
- `convertToVO`方法的各种场景测试
- `convertFromVO`方法的各种场景测试
- 往返转换的兼容性测试
- 边界情况和异常处理测试

### 测试用例覆盖

- ✅ 空值输入处理
- ✅ 简单距离条件转换
- ✅ 包含OrderTag的复杂转换
- ✅ 多分支表达式树处理
- ✅ 反向转换功能
- ✅ 往返转换兼容性
- ✅ 边界情况处理
