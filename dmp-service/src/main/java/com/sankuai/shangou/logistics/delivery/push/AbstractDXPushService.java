package com.sankuai.shangou.logistics.delivery.push;

import com.sankuai.shangou.logistics.delivery.enums.FreeMarkTemplateEnum;
import com.sankuai.shangou.logistics.delivery.push.domain.Failure;
import com.sankuai.shangou.logistics.delivery.push.freemark.FreeMarkerTemplateService;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import com.sankuai.xm.pubapi.thrift.PusherInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Slf4j
public abstract class AbstractDXPushService {

    @Resource
    private PushMessageServiceI.Iface pushMessageThriftServiceClient;

    @Resource
    private FreeMarkerTemplateService freeMarkerTemplateService;

    protected abstract PusherInfo buildPusherInfo();

    public Optional<Failure> pushDXMessage(FreeMarkTemplateEnum freeMarkTemplateEnum, List<String> misList,Object data){
        if(freeMarkTemplateEnum == null || CollectionUtils.isEmpty(misList)){
            return Optional.of(new Failure(false,1,"参数错误"));
        }
        try {
            String pushData = freeMarkerTemplateService.getDXTemplateMessage(freeMarkTemplateEnum,data);
            if(StringUtils.isEmpty(pushData)){
                return Optional.of(new Failure(false,2,"推送内容为空"));
            }
            PusherInfo pusherInfo = buildPusherInfo();
            if(pusherInfo == null){
                return Optional.of(new Failure(false,3,"推送公众号信息为空"));
            }
            String pushResult = pushMessageThriftServiceClient.pushTextMessage(System.currentTimeMillis(),pushData,misList,buildPusherInfo());
            log.info("推送结果："+pushResult);
        }catch (TException te){
            return Optional.of(new Failure(true,4,te.getMessage()));
        }catch (Exception e) {
            return Optional.of(new Failure(false,5,e.getMessage()));
        }
        return Optional.empty();
    }
}
