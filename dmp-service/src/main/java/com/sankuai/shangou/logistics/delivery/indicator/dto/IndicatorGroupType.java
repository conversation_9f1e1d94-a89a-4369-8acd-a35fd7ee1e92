package com.sankuai.shangou.logistics.delivery.indicator.dto;

/**
 * <AUTHOR>
 * @date 2023-07-04
 * @email <EMAIL>
 */
public enum IndicatorGroupType {

    BY_SHIFT(1, "按班次聚合"),
    BY_SINGLE_INDICATOR(2, "按单一元素聚合")
    ;

    private int type;
    private String desc;

    IndicatorGroupType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
