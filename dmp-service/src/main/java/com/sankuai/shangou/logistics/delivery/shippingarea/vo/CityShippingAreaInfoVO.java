package com.sankuai.shangou.logistics.delivery.shippingarea.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/15 17:06
 **/
@TypeDoc(
        description = "城市配送范围信息"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CityShippingAreaInfoVO {
    @FieldDoc(
            description = "城市id"
    )
    private Integer cityId;

    @FieldDoc(
            description = "门店列表"
    )
    private List<PoiShippingVO> storeList;
}
