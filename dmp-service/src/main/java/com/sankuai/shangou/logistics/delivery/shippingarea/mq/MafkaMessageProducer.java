package com.sankuai.shangou.logistics.delivery.shippingarea.mq;

import com.google.common.base.Preconditions;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PreDestroy;
import java.util.Properties;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/10
 */
@Slf4j
@SuppressWarnings("SpellCheckingInspection")
public class MafkaMessageProducer<T> {
	private static final String DEFAULT_NAME_SPACE = "waimai";

	private static final String CREDIT_AUDIT_NAME_SPACE = "common";

	private final IProducerProcessor<Object, String> processor;

	public MafkaMessageProducer(MQProducerEnum producer) throws Exception {
		Preconditions.checkNotNull(producer, "producer is null");

		Properties properties = new Properties();

		properties.setProperty(ConsumerConstants.MafkaBGNamespace, DEFAULT_NAME_SPACE);

		properties.setProperty(ConsumerConstants.MafkaClientAppkey, producer.getAppkey());

		processor = MafkaClient.buildProduceFactory(properties, producer.getTopic(), false);
	}

	@PreDestroy
	public void destroy() {
		try {
			this.processor.close();
		} catch (Exception e) {
			log.error("Shut down mq producer failed", e);
		}
	}

	/**
	 * 发送Mafka消息
	 *
	 * @param message 消息体
	 */
	public void sendMessage(T message) {
		Preconditions.checkNotNull(message, "message is null");

		ProducerResult produceResult;
		String messageJson = JsonUtil.toJson(message);
		try {
			produceResult = processor.sendMessage(messageJson);
		} catch (Exception e) {
			log.error("发送Mafka消息失败, type=[{}] message=[{}]", message.getClass().getName(), messageJson, e);
			throw new MessageSendFailedException(e);
		}

		if (produceResult != null && ProducerStatus.SEND_OK == produceResult.getProducerStatus()) {
			log.info("发送Mafka消息成功, type=[{}] message=[{}], messageId=[{}]", message.getClass().getName(), messageJson, produceResult.getMessageID());
		} else {
			log.error("发送Mafka消息失败, type=[{}] message=[{}]", message.getClass().getName(), messageJson);
			throw new MessageSendFailedException("发送Mafka消息失败");
		}
	}

	/**
	 * 指定分区字段，发送Mafka消息
	 *
	 * @param message 消息体
	 */
	public void sendMessage(T message, Object partKey) {
		Preconditions.checkNotNull(message, "message is null");

		ProducerResult produceResult;
		String messageJson = JsonUtil.toJson(message);
		try {
			produceResult = processor.sendMessage(messageJson, partKey);
		} catch (Exception e) {
			log.error("发送Mafka消息失败, type=[{}] message=[{}] partKey=[{}]", message.getClass().getName(), messageJson, partKey, e);
			throw new MessageSendFailedException(e);
		}

		if (produceResult != null && ProducerStatus.SEND_OK == produceResult.getProducerStatus()) {
			log.info("发送Mafka消息成功, type=[{}] message=[{}] partKey=[{}]", message.getClass().getName(), messageJson, partKey);
		} else {
			log.error("发送Mafka消息失败, type=[{}] message=[{}] partKey=[{}]", message.getClass().getName(), messageJson, partKey);
			throw new MessageSendFailedException("发送Mafka消息失败");
		}
	}
}
