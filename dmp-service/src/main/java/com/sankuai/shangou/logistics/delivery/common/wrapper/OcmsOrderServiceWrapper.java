package com.sankuai.shangou.logistics.delivery.common.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ChannelOrderIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderListReq;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsOrderSearchResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderInfoVo;
import com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchService;
import com.sankuai.drunkhorsemgmt.labor.exception.SystemException;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2023-08-01
 * @email <EMAIL>
 */
@Slf4j
@Rhino
public class OcmsOrderServiceWrapper {

    @Resource
    private OcmsOrderSearchService ocmsOrderSearchService;

    private static final int MAX_PAGE_SIZE = 30;

    @Degrade(rhinoKey = "OrderServiceWrapper-orderList",
            fallBackMethod = "orderListFallback",
            timeoutInMilliseconds = 3000)
    public Map<String, OrderInfoVo> orderList(long tenantId, List<ChannelOrderIdCondition> channelOrderList) {
        if (CollectionUtils.isEmpty(channelOrderList)) {
            return Maps.newHashMap();
        }
        try {
            List<OrderInfoVo> orders = Lists.newArrayList();
            OcmsOrderListReq req = new OcmsOrderListReq();

            List<List<ChannelOrderIdCondition>> partitionChannelOrderIdConditions = Lists.partition(channelOrderList, MAX_PAGE_SIZE);
            for (List<ChannelOrderIdCondition> partitionChannelOrderIdCondition : partitionChannelOrderIdConditions) {
                OcmsOrderSearchResponse response = getOcmsOrderSearchResponse(tenantId, req, partitionChannelOrderIdCondition);
                if (response.getResponseStatus() != 0) {
                    log.info("this order error resp = {}", JsonUtil.toJson(response));
                    throw new SystemException("查询订单错误");
                }
                orders.addAll(response.getOrderListResp().getOrders());
            }

            return IListUtils.nullSafeAndOverrideCollectToMap(orders, OrderInfoVo::getChannelOrderId, Function.identity());
        } catch (Exception e) {
            log.error("invoke ocmsOrderSearchService.orderList error, channelOrderList = {}", channelOrderList, e);
            throw new SystemException("查询订单错误");
        }
    }

    public Map<String, OrderInfoVo> orderListFallback(long tenantId, List<ChannelOrderIdCondition> channelOrderList) {
        return Maps.newHashMap();
    }

    private OcmsOrderSearchResponse getOcmsOrderSearchResponse(long tenantId, OcmsOrderListReq req, List<ChannelOrderIdCondition> channelOrderList) throws TException {
        req.setTenantId(tenantId);
        req.setPage(1);
        req.setPageSize(MAX_PAGE_SIZE);
        req.setOrderReqList(channelOrderList);
        OcmsOrderSearchResponse response = ocmsOrderSearchService.orderList(req);
        log.info("ocmsOrderSearchService.orderList req = {}, resp = {}", req, response);
        return response;
    }


}
