package com.sankuai.shangou.logistics.delivery.shippingarea.vo.request;

import com.meituan.linz.boot.util.Assert;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.enums.DateTypeEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.DateTimeUtils;
import lombok.Data;
import org.apache.commons.lang3.EnumUtils;

/**
 * <AUTHOR>
 * @since 2022/8/22
 */
@Data
public class CityOutOfScopePointsQueryParam {
    @FieldDoc(
            description = "城市id"
    )
    private Integer cityId;

    @FieldDoc(
            description = "时间类型"
    )
    private String dateType;

    @FieldDoc(
            description = "时间"
    )
    private String dt;

    public void validate() {
        Assert.throwIfTrue(cityId == null || cityId <= 0L, "城市id不正确");
        Assert.throwIfTrue(!EnumUtils.isValidEnum(DateTypeEnum.class, dateType), "日期类型不正确");
        Assert.throwIfTrue(!DateTimeUtils.isIsoLocalDate(this.dt), "日期格式不正确");
    }
}
