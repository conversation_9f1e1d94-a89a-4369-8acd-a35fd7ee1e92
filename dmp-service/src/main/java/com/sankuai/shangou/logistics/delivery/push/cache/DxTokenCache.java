package com.sankuai.shangou.logistics.delivery.push.cache;

import com.sankuai.shangou.logistics.delivery.common.SquirrelOperateService;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/5/16 15:42
 **/
@Component
public class DxTokenCache extends SquirrelOperateService {
    private static final String CATEGORY_NAME = "token_cache";
    private static final String DX_TOKEN = "dx_token";

    @Override
    public String getCategoryName() {
        return CATEGORY_NAME;
    }

    public Optional<String> getToken() {
        return get(DX_TOKEN, String.class);
    }

    public Boolean saveToken(String token) {
        return set(DX_TOKEN, token);
    }

}
