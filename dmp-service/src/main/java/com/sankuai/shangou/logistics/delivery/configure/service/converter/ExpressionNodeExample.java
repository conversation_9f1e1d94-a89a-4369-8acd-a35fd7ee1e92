package com.sankuai.shangou.logistics.delivery.configure.service.converter;

import com.alibaba.fastjson.JSON;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.model.value.AssessTimeConfig;
import com.sankuai.shangou.logistics.delivery.configure.model.value.ExpressionCondition;
import com.sankuai.shangou.logistics.delivery.configure.model.value.ExpressionNode;
import com.sankuai.shangou.logistics.delivery.configure.model.value.interval.Interval;
import com.sankuai.shangou.logistics.delivery.configure.model.value.interval.IntervalNumber;
import com.sankuai.shangou.logistics.delivery.configure.model.value.interval.IntervalTypeEnum;

import java.util.Arrays;
import java.util.Collections;

/**
 * ExpressionNode转换示例
 *
 * <AUTHOR>
 * @date 2025-07-18
 * @email <EMAIL>
 */
public class ExpressionNodeExample {

    public static void main(String[] args) {
        AssessTimeConfigConverter converter = new AssessTimeConfigConverter();
        
        // 示例1：简单的距离条件
        System.out.println("=== 示例1：简单的距离条件 ===");
        AssessTimeConfig config1 = createSimpleDistanceConfig();
        DeliveryConfigDetailVO.AssertTimeVO result1 = converter.convertToVO(config1);
        System.out.println("输入ExpressionNode: " + JSON.toJSONString(config1.getExpressionNode()));
        System.out.println("转换结果: " + JSON.toJSONString(result1));
        System.out.println();

        // 示例2：包含orderTag条件
        System.out.println("=== 示例2：包含orderTag条件 ===");
        AssessTimeConfig config2 = createOrderTagConfig();
        DeliveryConfigDetailVO.AssertTimeVO result2 = converter.convertToVO(config2);
        System.out.println("输入ExpressionNode: " + JSON.toJSONString(config2.getExpressionNode()));
        System.out.println("转换结果: " + JSON.toJSONString(result2));
        System.out.println();

        // 示例3：复杂的多分支表达式树
        System.out.println("=== 示例3：复杂的多分支表达式树 ===");
        AssessTimeConfig config3 = createComplexConfig();
        DeliveryConfigDetailVO.AssertTimeVO result3 = converter.convertToVO(config3);
        System.out.println("输入ExpressionNode: " + JSON.toJSONString(config3.getExpressionNode()));
        System.out.println("转换结果: " + JSON.toJSONString(result3));
        System.out.println();

        // 示例4：反向转换 - 从VO转换为ExpressionNode
        System.out.println("=== 示例4：反向转换（VO -> ExpressionNode）===");
        DeliveryConfigDetailVO.AssertTimeVO vo = createSampleVO();
        AssessTimeConfig configFromVO = converter.convertFromVO(vo);
        System.out.println("输入VO: " + JSON.toJSONString(vo));
        System.out.println("转换结果ExpressionNode: " + JSON.toJSONString(configFromVO.getExpressionNode()));
        System.out.println();

        // 示例5：往返转换测试
        System.out.println("=== 示例5：往返转换测试 ===");
        AssessTimeConfig originalConfig = createSimpleDistanceConfig();
        DeliveryConfigDetailVO.AssertTimeVO voFromConfig = converter.convertToVO(originalConfig);
        AssessTimeConfig configFromVOAgain = converter.convertFromVO(voFromConfig);
        System.out.println("原始ExpressionNode: " + JSON.toJSONString(originalConfig.getExpressionNode()));
        System.out.println("往返后ExpressionNode: " + JSON.toJSONString(configFromVOAgain.getExpressionNode()));
    }

    /**
     * 创建简单的距离条件配置
     * 结构：根节点 -> 距离条件[0,5] -> 公式"30"
     */
    private static AssessTimeConfig createSimpleDistanceConfig() {
        AssessTimeConfig config = new AssessTimeConfig();
        config.setType(1);
        config.setHint("简单距离条件");

        // 创建叶子节点：距离条件 + 公式
        ExpressionNode leafNode = new ExpressionNode();
        leafNode.setCondition(createCondition("${delivery_distance}", "0", "5"));
        leafNode.setSubs(Collections.emptyList());
        leafNode.setFormula("30");

        // 创建根节点
        ExpressionNode rootNode = new ExpressionNode();
        rootNode.setCondition(null); // 根节点条件为空
        rootNode.setSubs(Collections.singletonList(leafNode));
        rootNode.setFormula(null);

        config.setExpressionNode(rootNode);
        return config;
    }

    /**
     * 创建包含orderTag条件的配置
     * 结构：根节点 -> orderTag条件[301,302] -> 距离条件[0,5] -> 公式"25"
     */
    private static AssessTimeConfig createOrderTagConfig() {
        AssessTimeConfig config = new AssessTimeConfig();
        config.setType(2);
        config.setHint("包含orderTag条件");

        // 创建叶子节点：距离条件 + 公式
        ExpressionNode leafNode = new ExpressionNode();
        leafNode.setCondition(createCondition("${delivery_distance}", "0", "5"));
        leafNode.setSubs(Collections.emptyList());
        leafNode.setFormula("25");

        // 创建orderTag条件节点
        ExpressionNode orderTagNode = new ExpressionNode();
        orderTagNode.setCondition(createCondition("${order_tag}", "301", "302"));
        orderTagNode.setSubs(Collections.singletonList(leafNode));
        orderTagNode.setFormula(null);

        // 创建根节点
        ExpressionNode rootNode = new ExpressionNode();
        rootNode.setCondition(null); // 根节点条件为空
        rootNode.setSubs(Collections.singletonList(orderTagNode));
        rootNode.setFormula(null);

        config.setExpressionNode(rootNode);
        return config;
    }

    /**
     * 创建复杂的多分支表达式树
     * 结构：
     * 根节点
     * ├── orderTag条件[301] -> 距离条件[0,3] -> 公式"20"
     * └── orderTag条件[302] -> 距离条件[3,10] -> 公式"35"
     */
    private static AssessTimeConfig createComplexConfig() {
        AssessTimeConfig config = new AssessTimeConfig();
        config.setType(3);
        config.setHint("复杂多分支条件");

        // 分支1：orderTag[301] -> 距离[0,3] -> 公式"20"
        ExpressionNode leafNode1 = new ExpressionNode();
        leafNode1.setCondition(createCondition("${delivery_distance}", "0", "3"));
        leafNode1.setSubs(Collections.emptyList());
        leafNode1.setFormula("20");

        ExpressionNode orderTagNode1 = new ExpressionNode();
        orderTagNode1.setCondition(createCondition("${order_tag}", "301"));
        orderTagNode1.setSubs(Collections.singletonList(leafNode1));
        orderTagNode1.setFormula(null);

        // 分支2：orderTag[302] -> 距离[3,10] -> 公式"35"
        ExpressionNode leafNode2 = new ExpressionNode();
        leafNode2.setCondition(createCondition("${delivery_distance}", "3", "10"));
        leafNode2.setSubs(Collections.emptyList());
        leafNode2.setFormula("35");

        ExpressionNode orderTagNode2 = new ExpressionNode();
        orderTagNode2.setCondition(createCondition("${order_tag}", "302"));
        orderTagNode2.setSubs(Collections.singletonList(leafNode2));
        orderTagNode2.setFormula(null);

        // 创建根节点
        ExpressionNode rootNode = new ExpressionNode();
        rootNode.setCondition(null); // 根节点条件为空
        rootNode.setSubs(Arrays.asList(orderTagNode1, orderTagNode2));
        rootNode.setFormula(null);

        config.setExpressionNode(rootNode);
        return config;
    }

    /**
     * 创建示例VO用于反向转换测试
     */
    private static DeliveryConfigDetailVO.AssertTimeVO createSampleVO() {
        DeliveryConfigDetailVO.AssertTimeVO vo = new DeliveryConfigDetailVO.AssertTimeVO();
        vo.setType(4);
        vo.setHint("从VO创建的配置");

        // 设置orderTags
        vo.setOrderTags(Arrays.asList(301, 302));

        // 创建距离配置
        DeliveryConfigDetailVO.DistanceConfigVO distanceConfig = new DeliveryConfigDetailVO.DistanceConfigVO();
        distanceConfig.setFormula("40");

        // 创建条件
        DeliveryConfigDetailVO.ConditionVO condition = new DeliveryConfigDetailVO.ConditionVO();
        condition.setIdentifer("${delivery_distance}");

        // 创建区间
        DeliveryConfigDetailVO.IntervalVO interval = new DeliveryConfigDetailVO.IntervalVO();
        interval.setIntervalType(4); // ALL_CLOSE
        interval.setValues(Arrays.asList("0", "8"));

        condition.setInterval(interval);
        distanceConfig.setCondition(Collections.singletonList(condition));
        vo.setDistanceConfigVOS(Collections.singletonList(distanceConfig));

        return vo;
    }

    /**
     * 创建条件（单个值）
     */
    private static ExpressionCondition createCondition(String identifier, String value) {
        ExpressionCondition condition = new ExpressionCondition();
        condition.setIdentifier(identifier);
        condition.setInterval(createInterval(value));
        return condition;
    }

    /**
     * 创建条件（区间值）
     */
    private static ExpressionCondition createCondition(String identifier, String value1, String value2) {
        ExpressionCondition condition = new ExpressionCondition();
        condition.setIdentifier(identifier);
        condition.setInterval(createInterval(value1, value2));
        return condition;
    }

    /**
     * 创建区间（单个值，等于条件）
     */
    private static Interval createInterval(String value) {
        Interval interval = new Interval();
        interval.setIntervalType(IntervalTypeEnum.EQUAL.getType());
        interval.setValues(Collections.singletonList(new IntervalNumber(value)));
        return interval;
    }

    /**
     * 创建区间（范围值，闭区间）
     */
    private static Interval createInterval(String value1, String value2) {
        Interval interval = new Interval();
        interval.setIntervalType(IntervalTypeEnum.ALL_CLOSE.getType());
        interval.setValues(Arrays.asList(new IntervalNumber(value1), new IntervalNumber(value2)));
        return interval;
    }
}
