package com.sankuai.shangou.logistics.delivery.questionnaire.factory;

import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDO;
import com.sankuai.shangou.logistics.delivery.questionnaire.utils.MccUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/11/14 19:57
 **/
public class DeliveryQuestionnaireFactory {
    public static List<DeliveryQuestionnaireDO> releaseQuestionnaireForDeliveryOrder(TRiderDeliveryOrder riderDeliveryOrder,
                                                                                     LocalDateTime releaseTime, Integer cityId) {
        Map<String, List<String>> questionnaireAnswerMap = MccUtils.getQuestionnaireInfo();
        ArrayList<DeliveryQuestionnaireDO> deliveryQuestionnaireDOList = new ArrayList<>();
        questionnaireAnswerMap.forEach((question, answers) -> {
                    DeliveryQuestionnaireDO questionnaireDo = new DeliveryQuestionnaireDO();
                    questionnaireDo.setOrderId(riderDeliveryOrder.getOrderId());
                    questionnaireDo.setCityId(cityId);
                    questionnaireDo.setDeliveryOrderId(riderDeliveryOrder.getDeliveryOrderId());
                    questionnaireDo.setQuestion(question);
                    questionnaireDo.setReleaseTime(releaseTime);
                    questionnaireDo.setRiderAccountId(riderDeliveryOrder.getCurrentRider().getAccountId());
                    questionnaireDo.setIsValid(1);
                    questionnaireDo.setStoreId(riderDeliveryOrder.getStoreId());
                    questionnaireDo.setAnswerOptionsSnapshot(JsonUtil.toJson(answers));
                    questionnaireDo.setCreateTime(LocalDateTime.now());
                    questionnaireDo.setUpdateTime(LocalDateTime.now());

                    deliveryQuestionnaireDOList.add(questionnaireDo);
                }
        );

        return deliveryQuestionnaireDOList;
    }
}
