package com.sankuai.shangou.logistics.delivery.indicator.repository.dbo;

import com.sankuai.shangou.logistics.delivery.indicator.repository.es.AbstractEsDoc;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023/6/29 17:11
 **/
@Data
public class IndicatorDoc extends AbstractEsDoc {

    /**
     * 指标唯一编码
     */
    private String code;

    /**
     * 物流单元ID。
     */
    private Long logisticsUnitId;

    /**
     * 反映业务情况的指标时间点。
     */
    private LocalDateTime bizTime;

    /**
     * 特征值，可以理解为组成指标的子指标的唯一特质值。
     */
    private String property = StringUtils.EMPTY;

    /**
     * 指标值
     */
    private BigDecimal value;

    /**
     * 指标的加工生产时间
     */
    private LocalDateTime calculateTime;

    /**
     * 指标名称
     */
    private String fragmentName;

    private static final String UNDER_LINE = "_";

    public void fillTime(LocalDateTime bizTime, LocalDateTime calculateTime) {
        this.bizTime = bizTime;
        this.calculateTime = calculateTime;
    }

    @Override
    public String calcEsId() {
        return logisticsUnitId + UNDER_LINE
                + code + UNDER_LINE
                + bizTime + UNDER_LINE
                + property;
    }

    public static IndicatorDoc buildDefaultZeroIndicatorDoc(long logisticsUnitId, String code,
                                                            LocalDateTime bizTime, LocalDateTime calcTime) {
        IndicatorDoc doc = new IndicatorDoc();
        doc.setCode(code);
        doc.setLogisticsUnitId(logisticsUnitId);
        doc.setBizTime(bizTime);
        doc.setCalculateTime(calcTime);
        doc.setProperty(StringUtils.EMPTY);
        doc.setValue(BigDecimal.ZERO);
        doc.setFragmentName(StringUtils.EMPTY);
        return doc;
    }

}
