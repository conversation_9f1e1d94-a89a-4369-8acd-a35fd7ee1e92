package com.sankuai.shangou.logistics.delivery.configure.service.converter;

import com.alibaba.fastjson.JSON;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.model.value.AssessTimeConfig;
import com.sankuai.shangou.logistics.delivery.configure.model.value.ExpressionCondition;
import com.sankuai.shangou.logistics.delivery.configure.model.value.ExpressionNode;
import com.sankuai.shangou.logistics.delivery.configure.model.value.interval.Interval;
import com.sankuai.shangou.logistics.delivery.configure.model.value.interval.IntervalNumber;
import com.sankuai.shangou.logistics.delivery.configure.model.value.interval.IntervalTypeEnum;

import java.util.Arrays;
import java.util.Collections;

/**
 * 转换器功能验证脚本
 *
 * <AUTHOR>
 * @date 2025-07-18
 * @email <EMAIL>
 */
public class ConversionValidation {

    public static void main(String[] args) {
        AssessTimeConfigConverter converter = new AssessTimeConfigConverter();
        
        System.out.println("=== ExpressionNode转换器功能验证 ===\n");
        
        // 验证1：基本转换功能
        validateBasicConversion(converter);
        
        // 验证2：反向转换功能
        validateReverseConversion(converter);
        
        // 验证3：往返转换一致性
        validateRoundTripConsistency(converter);
        
        // 验证4：边界情况处理
        validateEdgeCases(converter);
        
        System.out.println("=== 所有验证完成 ===");
    }

    /**
     * 验证基本转换功能
     */
    private static void validateBasicConversion(AssessTimeConfigConverter converter) {
        System.out.println("1. 验证基本转换功能（ExpressionNode -> VO）");
        
        // 创建包含orderTag和距离条件的表达式树
        AssessTimeConfig config = createTestConfig();
        DeliveryConfigDetailVO.AssertTimeVO result = converter.convertToVO(config);
        
        System.out.println("输入: " + JSON.toJSONString(config.getExpressionNode()));
        System.out.println("输出: " + JSON.toJSONString(result));
        
        // 验证结果
        assert result != null : "转换结果不应为null";
        assert result.getOrderTags().contains(301) : "应包含orderTag 301";
        assert result.getDistanceConfigVOS().size() > 0 : "应包含距离配置";
        
        System.out.println("✅ 基本转换功能验证通过\n");
    }

    /**
     * 验证反向转换功能
     */
    private static void validateReverseConversion(AssessTimeConfigConverter converter) {
        System.out.println("2. 验证反向转换功能（VO -> ExpressionNode）");
        
        // 创建测试VO
        DeliveryConfigDetailVO.AssertTimeVO vo = createTestVO();
        AssessTimeConfig result = converter.convertFromVO(vo);
        
        System.out.println("输入: " + JSON.toJSONString(vo));
        System.out.println("输出: " + JSON.toJSONString(result.getExpressionNode()));
        
        // 验证结果
        assert result != null : "转换结果不应为null";
        assert result.getExpressionNode() != null : "表达式树不应为null";
        assert result.getExpressionNode().getCondition() == null : "根节点条件应为null";
        assert result.getExpressionNode().getSubs().size() > 0 : "应有子节点";
        
        System.out.println("✅ 反向转换功能验证通过\n");
    }

    /**
     * 验证往返转换一致性
     */
    private static void validateRoundTripConsistency(AssessTimeConfigConverter converter) {
        System.out.println("3. 验证往返转换一致性");
        
        // 原始配置 -> VO -> 新配置
        AssessTimeConfig originalConfig = createTestConfig();
        DeliveryConfigDetailVO.AssertTimeVO vo = converter.convertToVO(originalConfig);
        AssessTimeConfig newConfig = converter.convertFromVO(vo);
        
        System.out.println("原始配置: " + JSON.toJSONString(originalConfig));
        System.out.println("中间VO: " + JSON.toJSONString(vo));
        System.out.println("最终配置: " + JSON.toJSONString(newConfig));
        
        // 验证基本属性一致性
        assert originalConfig.getType().equals(newConfig.getType()) : "类型应保持一致";
        assert originalConfig.getHint().equals(newConfig.getHint()) : "提示应保持一致";
        
        System.out.println("✅ 往返转换一致性验证通过\n");
    }

    /**
     * 验证边界情况处理
     */
    private static void validateEdgeCases(AssessTimeConfigConverter converter) {
        System.out.println("4. 验证边界情况处理");
        
        // 测试null输入
        DeliveryConfigDetailVO.AssertTimeVO nullResult1 = converter.convertToVO(null);
        AssessTimeConfig nullResult2 = converter.convertFromVO(null);
        assert nullResult1 == null : "null输入应返回null";
        assert nullResult2 == null : "null输入应返回null";
        
        // 测试空表达式树
        AssessTimeConfig emptyConfig = new AssessTimeConfig();
        emptyConfig.setType(1);
        emptyConfig.setHint("空配置");
        emptyConfig.setExpressionNode(null);
        
        DeliveryConfigDetailVO.AssertTimeVO emptyResult = converter.convertToVO(emptyConfig);
        assert emptyResult != null : "空配置应返回有效结果";
        assert emptyResult.getOrderTags().isEmpty() : "空配置的orderTags应为空";
        assert emptyResult.getDistanceConfigVOS().isEmpty() : "空配置的distanceConfigs应为空";
        
        // 测试空VO
        DeliveryConfigDetailVO.AssertTimeVO emptyVO = new DeliveryConfigDetailVO.AssertTimeVO();
        emptyVO.setType(1);
        emptyVO.setHint("空VO");
        emptyVO.setOrderTags(Collections.emptyList());
        emptyVO.setDistanceConfigVOS(Collections.emptyList());
        
        AssessTimeConfig emptyConfigResult = converter.convertFromVO(emptyVO);
        assert emptyConfigResult != null : "空VO应返回有效结果";
        assert emptyConfigResult.getExpressionNode() != null : "应创建表达式树";
        assert emptyConfigResult.getExpressionNode().getSubs().isEmpty() : "空VO的表达式树应无子节点";
        
        System.out.println("✅ 边界情况处理验证通过\n");
    }

    /**
     * 创建测试配置
     */
    private static AssessTimeConfig createTestConfig() {
        AssessTimeConfig config = new AssessTimeConfig();
        config.setType(1);
        config.setHint("测试配置");

        // 创建表达式树：根节点 -> orderTag[301] -> 距离[0,5] -> 公式"30"
        ExpressionNode leafNode = new ExpressionNode();
        leafNode.setCondition(createCondition("${delivery_distance}", "0", "5"));
        leafNode.setSubs(Collections.emptyList());
        leafNode.setFormula("30");

        ExpressionNode orderTagNode = new ExpressionNode();
        orderTagNode.setCondition(createCondition("${order_tag}", "301"));
        orderTagNode.setSubs(Collections.singletonList(leafNode));
        orderTagNode.setFormula(null);

        ExpressionNode rootNode = new ExpressionNode();
        rootNode.setCondition(null);
        rootNode.setSubs(Collections.singletonList(orderTagNode));
        rootNode.setFormula(null);

        config.setExpressionNode(rootNode);
        return config;
    }

    /**
     * 创建测试VO
     */
    private static DeliveryConfigDetailVO.AssertTimeVO createTestVO() {
        DeliveryConfigDetailVO.AssertTimeVO vo = new DeliveryConfigDetailVO.AssertTimeVO();
        vo.setType(2);
        vo.setHint("测试VO");
        vo.setOrderTags(Arrays.asList(301, 302));

        DeliveryConfigDetailVO.DistanceConfigVO distanceConfig = new DeliveryConfigDetailVO.DistanceConfigVO();
        distanceConfig.setFormula("25");

        DeliveryConfigDetailVO.ConditionVO condition = new DeliveryConfigDetailVO.ConditionVO();
        condition.setIdentifer("${delivery_distance}");

        DeliveryConfigDetailVO.IntervalVO interval = new DeliveryConfigDetailVO.IntervalVO();
        interval.setIntervalType(4); // ALL_CLOSE
        interval.setValues(Arrays.asList("0", "5"));

        condition.setInterval(interval);
        distanceConfig.setCondition(Collections.singletonList(condition));
        vo.setDistanceConfigVOS(Collections.singletonList(distanceConfig));

        return vo;
    }

    /**
     * 创建条件（单值）
     */
    private static ExpressionCondition createCondition(String identifier, String value) {
        ExpressionCondition condition = new ExpressionCondition();
        condition.setIdentifier(identifier);
        condition.setInterval(createInterval(value));
        return condition;
    }

    /**
     * 创建条件（区间）
     */
    private static ExpressionCondition createCondition(String identifier, String value1, String value2) {
        ExpressionCondition condition = new ExpressionCondition();
        condition.setIdentifier(identifier);
        condition.setInterval(createInterval(value1, value2));
        return condition;
    }

    /**
     * 创建区间（单值）
     */
    private static Interval createInterval(String value) {
        Interval interval = new Interval();
        interval.setIntervalType(IntervalTypeEnum.EQUAL.getType());
        interval.setValues(Collections.singletonList(new IntervalNumber(value)));
        return interval;
    }

    /**
     * 创建区间（范围）
     */
    private static Interval createInterval(String value1, String value2) {
        Interval interval = new Interval();
        interval.setIntervalType(IntervalTypeEnum.ALL_CLOSE.getType());
        interval.setValues(Arrays.asList(new IntervalNumber(value1), new IntervalNumber(value2)));
        return interval;
    }
}
