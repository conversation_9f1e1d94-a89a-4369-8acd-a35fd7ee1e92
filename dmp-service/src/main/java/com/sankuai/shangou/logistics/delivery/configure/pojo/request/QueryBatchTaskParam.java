package com.sankuai.shangou.logistics.delivery.configure.pojo.request;

import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.logistics.delivery.common.enums.DimensionEnum;
import com.sankuai.shangou.logistics.delivery.enums.OperationModeEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.DeliveryConfigTypeEnum;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-26
 */
@Data
public class QueryBatchTaskParam {

    private Integer pageSize;

    private Integer pageNum;
    /**
     * tab类型： 1-按租户设置,2-按经营模式设置,3-按指定门店设置
     *
     * @see DimensionEnum
     */
    private Integer tabType;
    /**
     * tab子类型
     *
     * @see OperationModeEnum 经营模式: 1-直营, 2-加盟
     */
    private Integer tabChildType;
    /**
     * 配置类型
     *
     * @see DeliveryConfigTypeEnum code
     */
    private List<Integer> configTypeList;
    /**
     * 开始时间
     */
    private Long beginTime;
    /**
     * 结束时间
     */
    private Long endTime;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 操作人姓名/牵牛花账号
     */
    private String operator;

    public void validate() {
        if (Objects.isNull(pageSize) || Objects.isNull(pageNum)) {
            throw new BizException("分页参数不正确");
        }
        if (Objects.isNull(beginTime) || Objects.isNull(endTime)) {
            throw new BizException("时间范围不正确");
        }
    }
}
