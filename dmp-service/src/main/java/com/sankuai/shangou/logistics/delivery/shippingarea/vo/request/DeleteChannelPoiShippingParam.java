package com.sankuai.shangou.logistics.delivery.shippingarea.vo.request;

import com.meituan.linz.boot.util.Assert;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

@TypeDoc(
        description = "删除配送范围",
        authors = {
                "daiyuan"
        }
)
@Getter
@Setter
public class DeleteChannelPoiShippingParam {
    @FieldDoc(
            description = "门店id(百川门店id)"
    )
    private Long storeId;

    @FieldDoc(
            description = "渠道id和DeleteShippingAreaInfo map"
    )
    private Map<Integer, List<DeleteShippingAreaInfo>> channelIdShippingAreaIdMap;

    @Data
    public static class DeleteShippingAreaInfo {
        private Long shippingAreaId;

        private String oldDeliveryArea = "0";
    }

    public void validate() {
        Assert.throwIfTrue(storeId == null || storeId <= 0, "错误的门店id");
        Assert.throwIfTrue(MapUtils.isEmpty(channelIdShippingAreaIdMap) , "请至少同步一个渠道并选择保存过的范围进行删除");
    }
}
