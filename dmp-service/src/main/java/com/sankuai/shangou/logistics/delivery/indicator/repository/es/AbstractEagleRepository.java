package com.sankuai.shangou.logistics.delivery.indicator.repository.es;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.meituan.poros.client.PorosRestHighLevelClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-24
 * @email <EMAIL>
 */
@Slf4j
public abstract class AbstractEagleRepository<T extends AbstractEsDoc> {

    /**
     * 批量操作建议数量最大建议在1000-5000个doc，如果单个文档大小很大，数量要更小。
     */
    public void batchUpsert(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        BulkRequest request = new BulkRequest();
        for (T t : list) {
            UpdateRequest updateRequest = new UpdateRequest(getWriteIndicatorIndex(t), t.calcEsId())
                    .doc(JsonUtil.toJson(t), XContentType.JSON)
                    .upsert(JsonUtil.toJson(t), XContentType.JSON);
            request.add(updateRequest);
        }
        batchOperate(request);
    }

    public EsSearchResult<T> search(SearchSourceBuilder searchBuilder) throws IOException {
        SearchRequest searchRequest = new SearchRequest(getReadIndicatorIndex())
                .source(searchBuilder);
        log.info("searchRequest: {}", searchRequest);
        SearchResponse searchResponse = getEsClientRepository().search(searchRequest, RequestOptions.DEFAULT);

        List<T> result = Lists.newArrayList();
        for (SearchHit hit : searchResponse.getHits().getHits()) {
            result.add(JsonUtil.fromJson(hit.getSourceAsString(), getGenericClass()));
        }
        EsSearchResult<T> esSearchResult = new EsSearchResult<>();
        esSearchResult.setResultList(result);
        esSearchResult.setTotalHits(searchResponse.getHits().getTotalHits().value);
        return esSearchResult;
    }

    /**
     * 支持批量的插入、更新、删除操作
     *
     * @param request
     */
    private void batchOperate(BulkRequest request) {
        try {
            request.timeout(TimeValue.timeValueMinutes(3));
            BulkResponse responses = getEsClientRepository().bulk(request, RequestOptions.DEFAULT);
            if (responses.hasFailures()) {

                List<Pair<Integer, String>> failures = Lists.newArrayList();

                for (int i = 0; i < responses.getItems().length; i++) {
                    BulkItemResponse item = responses.getItems()[i];
                    if (item.isFailed()) {
                        log.error("修改数据失败" + item.getFailureMessage());
                        failures.add(Pair.of(i, item.getId()));
                    }
                }
                if (CollectionUtils.isNotEmpty(failures)) {
                    throw new SystemException("eagle执行批量修改数据失败， failures=" + failures);
                }
            }
        } catch (Exception e) {
            log.error("eagle执行批量修改数据异常", e);
            throw new SystemException("eagle执行批量修改数据异常");
        }
    }

    protected abstract PorosRestHighLevelClient getEsClientRepository();

    protected abstract String getReadIndicatorIndex();

    protected abstract String getWriteIndicatorIndex(T t);

    @SuppressWarnings("unchecked")
    protected Class<T> getGenericClass() {
        ParameterizedType parameterizedType = (ParameterizedType) getClass().getGenericSuperclass();
        return (Class<T>) parameterizedType.getActualTypeArguments()[0];
    }

    @Data
    protected static class EsSearchResult<T extends AbstractEsDoc> {
        private List<T> resultList;

        private long totalHits;
    }

}
