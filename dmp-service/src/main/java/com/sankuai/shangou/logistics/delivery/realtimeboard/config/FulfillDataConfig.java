package com.sankuai.shangou.logistics.delivery.realtimeboard.config;

import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.AttendanceThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderDeliveryStatisticThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.DeliveryChannelThriftService;
import com.sankuai.sgdata.query.thrift.DataQueryThriftService;
import com.sankuai.shangou.bizmng.labor.api.employee.TEmployeeInfoService;
import com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy;
import com.sankuai.shangou.infra.osw.api.org.TEmployeeService;
import com.sankuai.shangou.infra.osw.api.org.TOrgService;
import com.sankuai.shangou.infra.osw.api.org.TPositionService;
import com.sankuai.waimai.dws.protocol.openapi.OpenapiQueryService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
//@ComponentScan("com.sankuai.shangou.logistics.delivery.fulfill")
public class FulfillDataConfig {
    @Bean
    public OpenapiQueryService.Iface powerApiService() throws Exception {
        ThriftClientProxy client = new ThriftClientProxy();
        client.setServiceInterface(OpenapiQueryService.class);
        client.setTimeout(35000);
        client.setRemoteAppkey("com.sankuai.powerapi.openapi");
        client.setRemoteServerPort(9001);
        client.afterPropertiesSet();
        return (OpenapiQueryService.Iface) client.getObject();
    }

    @Bean
    public TOrgService tOrgService() throws Exception {
        ThriftClientProxy client = new ThriftClientProxy();
        client.setServiceInterface(TOrgService.class);
        client.setTimeout(2000);
        client.setRemoteAppkey("com.sankuai.shangou.infra.osw");
        client.setFilterByServiceName(true);
        client.setNettyIO(true);
        client.afterPropertiesSet();
        return (TOrgService) client.getObject();
    }

    @Bean
    public DeliveryChannelThriftService deliveryChannelThriftService() throws Exception {
        ThriftClientProxy client = new ThriftClientProxy();
        client.setServiceInterface(DeliveryChannelThriftService.class);
        client.setTimeout(3000);
        client.setRemoteAppkey("com.sankuai.sgfulfillment.tms");
        client.setFilterByServiceName(true);
        client.setNettyIO(true);
        client.afterPropertiesSet();
        return (DeliveryChannelThriftService) client.getObject();
    }

    @Bean
    public AttendanceThriftService attendanceThriftService() throws Exception {
        ThriftClientProxy client = new ThriftClientProxy();
        client.setServiceInterface(AttendanceThriftService.class);
        client.setTimeout(3000);
        client.setRemoteAppkey("com.sankuai.drunkhorsemgmt.labor.mng");
        client.setFilterByServiceName(true);
        client.setNettyIO(true);
        client.afterPropertiesSet();
        return (AttendanceThriftService) client.getObject();
    }

    @Bean
    public TEmployeeInfoService tEmployeeInfoService() throws Exception {
        ThriftClientProxy client = new ThriftClientProxy();
        client.setServiceInterface(TEmployeeInfoService.class);
        client.setTimeout(3000);
        client.setRemoteAppkey("com.sankuai.shangou.bizmng.labor");
        client.setFilterByServiceName(true);
        client.setNettyIO(true);
        client.afterPropertiesSet();
        return (TEmployeeInfoService) client.getObject();
    }

    @Bean
    public TPositionService tPositionService() throws Exception {
        ThriftClientProxy client = new ThriftClientProxy();
        client.setServiceInterface(TPositionService.class);
        client.setTimeout(3000);
        client.setRemoteAppkey("com.sankuai.shangou.infra.osw");
        client.setFilterByServiceName(true);
        client.setNettyIO(true);
        client.afterPropertiesSet();
        return (TPositionService) client.getObject();
    }

    @Bean
    public TEmployeeService tEmployeeService() throws Exception {
        ThriftClientProxy client = new ThriftClientProxy();
        client.setServiceInterface(TEmployeeService.class);
        client.setTimeout(3000);
        client.setRemoteAppkey("com.sankuai.shangou.infra.osw");
        client.setFilterByServiceName(true);
        client.setNettyIO(true);
        client.afterPropertiesSet();
        return (TEmployeeService) client.getObject();
    }

    @Bean
    public RiderDeliveryStatisticThriftService riderDeliveryStatisticThriftService() throws Exception {
        ThriftClientProxy client = new ThriftClientProxy();
        client.setServiceInterface(RiderDeliveryStatisticThriftService.class);
        client.setTimeout(3000);
        client.setRemoteAppkey("com.sankuai.sgfulfillment.tms");
        client.setFilterByServiceName(true);
        client.setNettyIO(true);
        client.afterPropertiesSet();
        return (RiderDeliveryStatisticThriftService) client.getObject();
    }

    @Bean
    public DataQueryThriftService dataQueryThriftService() throws Exception {
        ThriftClientProxy client = new ThriftClientProxy();
        client.setServiceInterface(DataQueryThriftService.class);
        client.setTimeout(3000);
        client.setRemoteAppkey("com.sankuai.sgdata.queryapi");
        client.setFilterByServiceName(true);
        client.setNettyIO(true);
        client.afterPropertiesSet();
        return (DataQueryThriftService) client.getObject();
    }
}
