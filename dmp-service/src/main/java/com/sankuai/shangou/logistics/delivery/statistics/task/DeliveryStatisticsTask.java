package com.sankuai.shangou.logistics.delivery.statistics.task;

import com.cip.crane.client.common.util.ExecutionContext;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ChannelOrderIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderInfoVo;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiListResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.retry.RetryTemplateUtil;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.logistics.delivery.config.LionConfigUtils;
import com.sankuai.shangou.logistics.delivery.indicator.service.wrapper.ChannelStoreServiceWrapper;
import com.sankuai.shangou.logistics.delivery.mapper.WaiMaDeliveryStatisticsPOMapper;
import com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryStatisticsPO;
import com.sankuai.shangou.logistics.delivery.statistics.dao.mapper.DeliveryOrderDOMapper;
import com.sankuai.shangou.logistics.delivery.statistics.dao.model.DeliveryOrderDO;
import com.sankuai.shangou.logistics.delivery.statistics.dao.model.DeliveryOrderDOExample;
import com.sankuai.shangou.logistics.delivery.common.wrapper.OcmsOrderServiceWrapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.math3.stat.descriptive.rank.Percentile;
import org.springframework.retry.RetryCallback;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-02-03
 * @email <EMAIL>
 */
@Slf4j
@CraneConfiguration
@Service
public class DeliveryStatisticsTask {

    @Resource
    private DeliveryOrderDOMapper deliveryOrderDOMapper;
    @Resource
    private WaiMaDeliveryStatisticsPOMapper waiMaDeliveryStatisticsPOMapper;
    @Resource
    private PoiThriftService poiThriftService;
    @Resource
    private ChannelStoreServiceWrapper channelStoreServiceWrapper;
    @Resource
    private OcmsOrderServiceWrapper ocmsOrderServiceWrapper;


    private static final int IN_TIME_CODE = 0;
    private static final int ONE_YUAN_ORDER_CODE = 102;
    private static final String ONE_YUAN_ORDER_DESC = "一元购";

    @Crane("delivery-statistics-task")
    public void doStatistics() throws Exception {
        List<String> dhTenantIdList = LionConfigUtils.getDHTenantIdList();
        if (CollectionUtils.isEmpty(dhTenantIdList)) {
            return;
        }
        long tenantId = Long.parseLong(dhTenantIdList.get(0));
        List<Long> poiIdList = getPoiIdList(tenantId);

        List<DeliveryOrderDO> recentDeliveryDoneDeliveryOrders = getRecentDeliveryDoneDeliveryOrders(tenantId, poiIdList);

        Map<Long, List<DeliveryOrderDO>> storeIdDeliveryOrderMap = IListUtils.nullSafeGroupBy(recentDeliveryDoneDeliveryOrders, DeliveryOrderDO::getStoreId);

        Map<Long,DeliveryStatInfo> poiDeliveryStatInfoMap = Maps.newHashMap();
        List<DeliveryOrderDO> failList = Lists.newArrayList();
        for (List<Map.Entry<Long, List<DeliveryOrderDO>>> partitionStoreEntry : Lists.partition(Lists.newArrayList(storeIdDeliveryOrderMap.entrySet()), LionConfigUtils.getStatBatchStoreNum())) {
            List<ChannelOrderIdCondition> channelOrderIdConditionList = partitionStoreEntry
                    .stream()
                    .map(Map.Entry::getValue)
                    .flatMap(Collection::stream)
                    .map(deliveryOrderDO -> new ChannelOrderIdCondition(deliveryOrderDO.getChannelOrderId(), ChannelOrderConvertUtils.sourceBiz2Mid(deliveryOrderDO.getOrderBizType())))
                    .collect(Collectors.toList());
            Map<String, OrderInfoVo> channelOrderIdOorderMap = ocmsOrderServiceWrapper.orderList(tenantId, channelOrderIdConditionList);
            Map<Long, DeliveryStatInfo> deliveryStatInfoMap = partitionStoreEntry
                    .stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            singleStoreEntry -> {
                                double[] durationSeconds = calcFulfillmentDurationSeconds(singleStoreEntry.getValue(), channelOrderIdOorderMap, failList);
                                if (ArrayUtils.isEmpty(durationSeconds)) {
                                    return new DeliveryStatInfo();
                                }
                                Percentile percentile = new Percentile().withEstimationType(Percentile.EstimationType.R_8);
                                DeliveryStatInfo deliveryStatInfo = new DeliveryStatInfo();
                                deliveryStatInfo.setStoreId(singleStoreEntry.getKey());
                                deliveryStatInfo.setNinePercentileFulfillmentDuration((int) percentile.evaluate(durationSeconds, 90));
                                deliveryStatInfo.setEightPointFivePercentileFulfillmentDuration((int) percentile.evaluate(durationSeconds, 85));
                                deliveryStatInfo.setEightPercentileFulfillmentDuration((int) percentile.evaluate(durationSeconds, 80));
                                //需要过滤的去调
                                deliveryStatInfo.setSelfDeliveryOrderCount(durationSeconds.length);
                                return deliveryStatInfo;
                            },(o1, o2) -> o2
                    ));
            poiDeliveryStatInfoMap.putAll(deliveryStatInfoMap);
        }

        for (List<Long> poiIds : Lists.partition(poiIdList, LionConfigUtils.getStatBatchStoreNum())) {
            Map<Long, List<ChannelPoiInfoDTO>> poiChannelMap = channelStoreServiceWrapper.queryPoiDetailInfoByPoiIds(tenantId, poiIds);
            List<WaiMaDeliveryStatisticsPO> toInsertPos = Lists.newArrayList();
            //门店维度
            for (Long poiId : poiIds) {
                if (!poiChannelMap.containsKey(poiId) ) {
                    continue;
                }
                List<ChannelPoiInfoDTO> channelPoiInfoDTOS = poiChannelMap.get(poiId);
                //渠道门店维度
                for (ChannelPoiInfoDTO channelPoiInfoDTO : channelPoiInfoDTOS) {
                    WaiMaDeliveryStatisticsPO po = new WaiMaDeliveryStatisticsPO();
                    //过滤上面这种为空的情况,任意取一个
                    if (poiDeliveryStatInfoMap.containsKey(poiId) && Objects.isNull(poiDeliveryStatInfoMap.get(poiId).getEightPointFivePercentileFulfillmentDuration())) {
                        buildDefaultStatPO(poiId, channelPoiInfoDTO, po);
                        toInsertPos.add(po);
                        continue;
                    }
                    if (poiDeliveryStatInfoMap.containsKey(poiId)) {
                        buildStatPO(poiDeliveryStatInfoMap, poiId, channelPoiInfoDTO, po);
                    } else {
                        buildDefaultStatPO(poiId, channelPoiInfoDTO, po);
                    }
                    toInsertPos.add(po);
                }
            }
            if (CollectionUtils.isNotEmpty(toInsertPos)) {
                waiMaDeliveryStatisticsPOMapper.batchInsert(toInsertPos);
            }
        }

        if (CollectionUtils.isNotEmpty(failList)) {
            throw new SystemException("存在未查询到订单兜底的情况");
        }
    }

    private static void buildDefaultStatPO(Long poiId, ChannelPoiInfoDTO channelPoiInfoDTO, WaiMaDeliveryStatisticsPO po) {
        //没有的填null，不要填0
        po.setLogisticsUnitId(poiId);
        po.setChannelId(channelPoiInfoDTO.getChannelId());
        po.setChannelPoiId(channelPoiInfoDTO.getChannelPoiId());
        po.setNinePercentileFulfillmentDuration(0);
        po.setEightPointFivePercentileFulfillmentDuration(0);
        po.setEightPercentileFulfillmentDuration(0);
        po.setSelfDeliveryOrderCount(0);
        po.setBizTime(TimeUtils.convertDateToLocalDateTime(ExecutionContext.getScheduleTime()));
        po.setCreateTime(LocalDateTime.now());
        po.setUpdateTime(LocalDateTime.now());
        po.setIsDeleted(0);
    }

    private static void buildStatPO(Map<Long, DeliveryStatInfo> poiDeliveryStatInfoMap, Long poiId, ChannelPoiInfoDTO channelPoiInfoDTO, WaiMaDeliveryStatisticsPO po) {
        DeliveryStatInfo deliveryStatInfo = poiDeliveryStatInfoMap.get(poiId);
        po.setLogisticsUnitId(poiId);
        po.setChannelId(channelPoiInfoDTO.getChannelId());
        po.setChannelPoiId(channelPoiInfoDTO.getChannelPoiId());
        //fixme: 闪购侧临时让改为分钟，记得改数据库文案
        po.setNinePercentileFulfillmentDuration(secondToMinus(deliveryStatInfo.getNinePercentileFulfillmentDuration()));
        po.setEightPointFivePercentileFulfillmentDuration(secondToMinus(deliveryStatInfo.getEightPointFivePercentileFulfillmentDuration()));
        po.setEightPercentileFulfillmentDuration(secondToMinus(deliveryStatInfo.getEightPercentileFulfillmentDuration()));
        po.setSelfDeliveryOrderCount(deliveryStatInfo.getSelfDeliveryOrderCount());
        po.setBizTime(TimeUtils.convertDateToLocalDateTime(ExecutionContext.getScheduleTime()));
        po.setCreateTime(LocalDateTime.now());
        po.setUpdateTime(LocalDateTime.now());
        po.setIsDeleted(0);
    }

    private static int secondToMinus(int second) {
        BigDecimal minus = new BigDecimal(second).divide(new BigDecimal(60), 0, RoundingMode.HALF_UP);
        return minus.intValue();
    }



    private double[] calcFulfillmentDurationSeconds(List<DeliveryOrderDO> singleStoreDeliveryOrders,
                                                    Map<String, OrderInfoVo> channelOrderIdOorderMap,
                                                    List<DeliveryOrderDO> failList) {
        return IListUtils.nullSafeStream(singleStoreDeliveryOrders)
                .map(deliveryOrderDO -> {
                    if (!channelOrderIdOorderMap.containsKey(deliveryOrderDO.getChannelOrderId())) {
                        log.error("未查询到订单,channelOrderId = {}", deliveryOrderDO.getChannelOrderId());
                        failList.add(deliveryOrderDO);
                        return null;
                    }
                    OrderInfoVo orderInfoVo = channelOrderIdOorderMap.get(deliveryOrderDO.getChannelOrderId());
                    //排除一元单
                    if (CollectionUtils.isNotEmpty(orderInfoVo.getTags())
                            && orderInfoVo.getTags().stream().anyMatch(orderTagVo -> Objects.equals(orderTagVo.getType(), ONE_YUAN_ORDER_CODE) && Objects.equals(orderTagVo.getValue(), ONE_YUAN_ORDER_DESC))) {
                        return null;
                    }
                    long seconds = Duration.between(
                            TimeUtils.fromMilliSeconds(orderInfoVo.getPayTime()),
                            deliveryOrderDO.getDeliveryDoneTime()
                    ).getSeconds();
                    if (seconds < 0L) {
                        return null;
                    }
                    return seconds;
                }).filter(Objects::nonNull)
                .mapToDouble(Double::valueOf)
                .toArray();
    }

    private List<DeliveryOrderDO> getRecentDeliveryDoneDeliveryOrders(long tenantId, List<Long> poiIdList) {
        List<DeliveryOrderDO> resultList = Lists.newArrayList();
        //这里不要取scheudleTime，永远只取调度时间之前的1个小时。避免加载大量数据(该任务不应该重试)
        //临时方案是从DB取，暂时歪马一个小时订单数目前在1000左右。后面需要改为从es取
        //自营 + 实时单 的 完成 运单
        int maxPage = LionConfigUtils.getQueryDeliveryOrderMaxPage();
        int pagesize = LionConfigUtils.getQueryDeliveryOrderPageSize();
        List<List<Long>> partitionPoiIds = Lists.partition(poiIdList, 200);

        for (List<Long> partitionPoiId : partitionPoiIds) {
            for (int i = 1; i <= maxPage; i++) {
                try (Page page = PageHelper.startPage(i, pagesize)) {
                    DeliveryOrderDOExample example = new DeliveryOrderDOExample();
                    example
                            .createCriteria()
                            .andTenantIdEqualTo(tenantId)
                            .andStoreIdIn(partitionPoiId)
                            .andCreateTimeGreaterThanOrEqualTo(LocalDateTime.now().minusHours(1))
                            //过去10分钟内，及时单 + 自配 + 完成的
                            .andDeliveryDoneTimeGreaterThanOrEqualTo(LocalDateTime.now().minusMinutes(LionConfigUtils.getStatRangeTimeMinus()))
                            .andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
                            .andReservedEqualTo(IN_TIME_CODE)
                            .andDeliveryStatusEqualTo(DeliveryStatusEnum.DELIVERY_DONE.getCode());
                    List<DeliveryOrderDO> deliveryOrderDOS = deliveryOrderDOMapper.selectByExample(example);
                    log.info("query deliveryOrderIdList = {}", IListUtils.mapTo(deliveryOrderDOS, DeliveryOrderDO::getId));
                    resultList.addAll(Optional.ofNullable(deliveryOrderDOS).orElse(Lists.newArrayList()));

                    if (CollectionUtils.isEmpty(deliveryOrderDOS)
                            || deliveryOrderDOS.size() < pagesize) {
                        break;
                    }
                    //超过这个数告警+直接放弃，别给DMP搞挂了
                    // 执行到这里，说明还有后续
                    if (i == maxPage) {
                        throw new SystemException("运单统计超过最大值");
                    }
                }
            }
        }

        return resultList;
    }

    @Data
    private static class DeliveryStatInfo {

        private long storeId;

        private Integer ninePercentileFulfillmentDuration;

        private Integer eightPointFivePercentileFulfillmentDuration;

        private Integer eightPercentileFulfillmentDuration;

        private Integer selfDeliveryOrderCount;
    }


    private List<Long> getPoiIdList(long tenantId) throws Exception {
        List<PoiInfoDto> poiInfoDtoList = RetryTemplateUtil.defaultRetryTemplate.execute(
                (RetryCallback<List<PoiInfoDto>, Exception>) context -> {
                    PoiListResponse poiListResponse = poiThriftService.queryTenantPoiList(tenantId);
                    if (!poiListResponse.getStatus().isSuccess()) {
                        throw new SystemException("invoke poiThriftService.queryTenantPoiList error");
                    }
                    return poiListResponse.getPoiList();
                }
        );
        return IListUtils
                .nullSafeStream(poiInfoDtoList)
                .map(PoiInfoDto::getPoiId)
                .collect(Collectors.toList());
    }
}
