package com.sankuai.shangou.logistics.delivery.offlineboard.converter;

import com.sankuai.shangou.logistics.delivery.offlineboard.enums.OfflineFulfillSummaryData;
import com.sankuai.shangou.logistics.delivery.offlineboard.response.OfflineSummaryFulfillDataDTO;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.Optional;


/**
 * <AUTHOR>
 * @since 2023/2/16 16:06
 **/
public class OfflineFulfillDataConverter {
    public static final int ZERO = 0;

    public static OfflineSummaryFulfillDataDTO toDTO(OfflineFulfillSummaryData summaryData) {
        if (Objects.isNull(summaryData)) {
            return null;
        }

        return OfflineSummaryFulfillDataDTO.builder()
                .deliveredOrderNum(summaryData.getArrivedOrdNum().intValue())
                .cancelOrderNum(summaryData.getNotArrCancelOrdNum().intValue())
                .completeBeforeDeliveredOrderNum(summaryData.getAheadArrivedOrdNum().intValue())
                .completeBeforeDeliveredOrderPercent(calPercent(summaryData.getAheadArrivedOrdNum(), summaryData.getArrivedOrdNum(), 2))
                .judgeTimeoutOrderNum(Optional.ofNullable(summaryData.getCheckOvertimeOrdNum()).map(Long::intValue).orElse(ZERO))
                .deliveredRateIn15min(calPercent(summaryData.getFifteenMinArrOrdNum(), summaryData.getArrivedOrdNoPreNum(), 2))
                .deliveredRateIn25min(calPercent(summaryData.getTwentyFiveMinArrOrdNum(), summaryData.getArrivedOrdNoPreNum(), 2))
                .deliveredRateIn45min(calPercent(summaryData.getFortyFiveMinArrOrdNum(), summaryData.getArrivedOrdNoPreNum(), 2))
                .deliveryDistanceAvg(Optional.ofNullable(calDivide(summaryData.getDeliveryDistance(), summaryData.getArrivedOrdNum(), 0))
                        .map(Integer::parseInt).orElse(null))
                .fulfillDurationAvg(Optional.ofNullable(calDivide(summaryData.getPerformanceDuration(), summaryData.getArrivedOrdNoPreNum(), 0))
                        .map(Integer::parseInt).orElse(null))
                .ninetiethFulfillDuration(Optional.ofNullable(summaryData.getNinePerformanceDuration()).map(Double::intValue).orElse(ZERO))
                .deliveryLaborEfficiency(calDivide(summaryData.getArrivedOrdNum(), summaryData.getEmployeeDays(), 2))
                .outWarehouseDurationAvg(Optional.ofNullable(calDivide(summaryData.getStorehouseOutDuration(), summaryData.getArrivedOrdNoPreNum(), 0))
                        .map(Integer::parseInt).orElse(null))
                .badCommentOrderNum(Optional.ofNullable(summaryData.getDeliveryNegCommentOrdNum()).map(Long::intValue).orElse(ZERO))
                .badCommentOrderPercent(calPercent(summaryData.getDeliveryNegCommentOrdNum(), summaryData.getArrivedOrdNumHaveOne(), 2))
                .build();

    }


    //计算百分率 单位：% 结果保留x位小数
    public static String calPercent(Long dividend, Long divisor, int bits) {
        if (Objects.isNull(dividend) || Objects.isNull(divisor) || Objects.equals(divisor, 0L)) {
            return null;
        }

        BigDecimal dividendBigDecimal = new BigDecimal(dividend);
        BigDecimal divisorBigDecimal = new BigDecimal(divisor);

        return dividendBigDecimal.multiply(new BigDecimal(100))
                .divide(divisorBigDecimal, bits, RoundingMode.HALF_UP)
                .toString();
    }

    //计算除法 单位：% 结果x位小数
    public static String calDivide(Long dividend, Long divisor, int bits) {
        if (Objects.isNull(dividend) || Objects.isNull(divisor) || Objects.equals(divisor, 0L)) {
            return null;
        }

        BigDecimal dividendBigDecimal = new BigDecimal(dividend);
        BigDecimal divisorBigDecimal = new BigDecimal(divisor);

        return dividendBigDecimal.divide(divisorBigDecimal, bits, RoundingMode.HALF_UP)
                .toString();
    }

}
