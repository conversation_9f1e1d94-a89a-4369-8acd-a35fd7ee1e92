package com.sankuai.shangou.logistics.delivery.shippingarea.enums;

import com.google.common.collect.ImmutableMap;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;

@Getter
public enum CatRegionSelectEventEnum {
    REGION_SELECT_DELETE_SHIPPING("regionselect.deleteChannelShipping", "删除渠道配送范围事件"),
    REGION_SELECT_UPSERT_SHIPPING("regionselect.upsertChannelShipping", "更新渠道配送范围事件"),
    REGION_SELECT_ALIGN_SHIPPING("regionselect.alignChannelShipping", "对齐渠道配送范围事件"),
    REGION_SELECT_INDICATOR_ACCESS("regionselect.indicatorAccess", "指标访问事件"),
    REGION_SELECT_EXPORT("regionselect.export", "导出事件"),
    REGION_SELECT_INIT_LAYER("regionselect.initLayer", "图层初始化事件");

    private String type;
    private String desc;

    CatRegionSelectEventEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static Map<String, String> formatShippingEventTags(Long storeId, String employeeName) {
        return ImmutableMap.of("storeId", String.valueOf(storeId),
                "operator", employeeName);
    }

    public static Map<String, String> formatIndicatorAccessEventTags(String indicatorType, String employeeName) {
        return ImmutableMap.of(
                "indicator", indicatorType,
                "operator", employeeName);
    }

    private static final int MAX_TAG_KEYS_SIZE = 6;

    public static String joinKeys(List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return "";
        }
        int size = keys.size();
        if (keys.size() > MAX_TAG_KEYS_SIZE) {
            keys = keys.subList(0, MAX_TAG_KEYS_SIZE);
        }

        String newKey = String.join("-", keys);
        return String.format("%s.%s", newKey, size);
    }
}
