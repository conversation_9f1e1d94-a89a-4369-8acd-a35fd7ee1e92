package com.sankuai.shangou.logistics.delivery.push.msg.card;

import com.sankuai.shangou.logistics.delivery.xm.enums.CardTitleEnum;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 卡片数据
 * @date 2025-05-20
 */
public abstract class AbstractCardData {
    /**
     * 卡片模板ID
     */
    private Long templateId;
    /**
     * 群id
     */
    private Long gid;
    /**
     * 个性数据sso员工id
     */
    private List<Long> privateDataEmpIds;
    /**
     * 请求ID(幂等唯一)
     */
    private String requestId;
    /**
     * 卡片类型
     */
    private String cardType;
    /**
     * 卡片标题
     */
    private CardTitleEnum cardTitle;
    /**
     * 卡片摘要
     */
    public String getAbstractText() {
        return getCardTitle().getDesc();
    }

    public abstract String getPublicDataJsonStr();

    public abstract String getPrivateDataJsonStr();

    protected void setCardTitleMap(Map<String, Object> map) {
        map.put("title", getCardTitle().getTitle());
        map.put("titleColor", getCardTitle().getColor().getType());
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Long getGid() {
        return gid;
    }

    public void setGid(Long gid) {
        this.gid = gid;
    }

    public List<Long> getPrivateDataEmpIds() {
        return privateDataEmpIds;
    }

    public void setPrivateDataEmpIds(List<Long> privateDataEmpIds) {
        this.privateDataEmpIds = privateDataEmpIds;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public CardTitleEnum getCardTitle() {
        return cardTitle;
    }

    public void setCardTitle(CardTitleEnum cardTitle) {
        this.cardTitle = cardTitle;
    }
}
