package com.sankuai.shangou.logistics.delivery.tag.entity;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.channelspu.ChannelSkuDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-28
 * @email <EMAIL>
 */
@Data
public class ChannelSpuKey {

    @FieldDoc(
            description = "百川商品id",
            requiredness = Requiredness.REQUIRED
    )
    private String spuId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    private Long storeId;
    @FieldDoc(
            description = "渠道id",
            requiredness = Requiredness.REQUIRED
    )
    private Integer channelId;


    @FieldDoc(
            description = "规格列表，如果查询请求中指定了货号",
            requiredness = Requiredness.REQUIRED
    )
    private List<ChannelSkuKey> skuList;

    @Data
    public static class ChannelSkuKey {

        @FieldDoc(
                description = "百川规格id",
                requiredness = Requiredness.REQUIRED
        )
        private String skuId;

        @FieldDoc(
                description = "零售价，单位：分",
                requiredness = Requiredness.REQUIRED
        )
        private Long retailPrice;

    }
}
