package com.sankuai.shangou.logistics.delivery.shippingarea.vo.request;

import com.meituan.linz.boot.util.Assert;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.enums.DateTypeEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.DateTimeUtils;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.IGeoUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2021/11/24
 */
@TypeDoc(
        description = "兴趣位置相关指标查询接口参数",
        authors = {
                "gonglei"
        }
)
@Getter
@Setter
public class LoiOrderUserIndicatorQueryParam {

    @FieldDoc(
            description = "城市id"
    )
    private Integer cityId;

    @FieldDoc(
            description = "范围边界"
    )
    private String region;
    @FieldDoc(
            description = "日期类型"
    )
    private String dateType;
    @FieldDoc(
            description = "查询日期, 格式 yyyy-MM-dd"
    )
    private String dt;

    @FieldDoc(
            description = "门店id"
    )
    private Long storeId;

    public void validate() {
        Assert.throwIfTrue(this.cityId == null || this.cityId <= 0L, "城市id不正确");
        //IGeoUtils.validateCoordinate(region);
        Assert.throwIfTrue(!EnumUtils.isValidEnum(DateTypeEnum.class, this.dateType), "日期类型不正确");
        Assert.throwIfTrue(!DateTimeUtils.isIsoLocalDate(this.dt), "日期格式不正确");
        Assert.throwIfTrue(storeId == null || storeId <= 0L, "门店id不正确");
        Assert.throwIfTrue(StringUtils.isBlank(this.region), "范围不能为空");
    }
}
