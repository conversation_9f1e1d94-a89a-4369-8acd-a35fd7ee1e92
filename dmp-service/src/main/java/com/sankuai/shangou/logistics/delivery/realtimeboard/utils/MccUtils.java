package com.sankuai.shangou.logistics.delivery.realtimeboard.utils;

import com.dianping.lion.client.Lion;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/14 16:45
 **/
public class MccUtils {
    public static String getDwsToken() {
        return Lion.getConfigRepository().get("realtime.board.dws");
    }

    public static List<Long> pickerPositionIds() {
        return Lion.getConfigRepository().getList("picker.position.ids", Long.class, Collections.emptyList());
    }
}
