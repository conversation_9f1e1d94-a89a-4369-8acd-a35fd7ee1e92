<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.mapper.TradeShippingOrderItemDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderItemDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId" />
    <result column="outbound_order_id" jdbcType="BIGINT" property="outboundOrderId" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="planned_qty" jdbcType="DECIMAL" property="plannedQty" />
    <result column="actual_qty" jdbcType="DECIMAL" property="actualQty" />
    <result column="refund_qty" jdbcType="DECIMAL" property="refundQty" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="temperature_zone_code" jdbcType="VARCHAR" property="temperatureZoneCode" />
    <result column="bar_codes" jdbcType="VARCHAR" property="barCodes" />
    <result column="box_codes" jdbcType="VARCHAR" property="boxCodes" />
    <result column="img_urls" jdbcType="VARCHAR" property="imgUrls" />
    <result column="is_management_sn" jdbcType="INTEGER" property="isManagementSn" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, warehouse_id, outbound_order_id, sku_id, sku_name, planned_qty, actual_qty, 
    refund_qty, specification, temperature_zone_code, bar_codes, box_codes, img_urls, 
    is_management_sn, ext_info, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderItemDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from trade_shipping_order_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from trade_shipping_order_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from trade_shipping_order_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderItemDOExample">
    delete from trade_shipping_order_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderItemDO">
    insert into trade_shipping_order_item (id, tenant_id, warehouse_id, 
      outbound_order_id, sku_id, sku_name, 
      planned_qty, actual_qty, refund_qty, 
      specification, temperature_zone_code, bar_codes, 
      box_codes, img_urls, is_management_sn, 
      ext_info, create_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, #{warehouseId,jdbcType=BIGINT}, 
      #{outboundOrderId,jdbcType=BIGINT}, #{skuId,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, 
      #{plannedQty,jdbcType=DECIMAL}, #{actualQty,jdbcType=DECIMAL}, #{refundQty,jdbcType=DECIMAL}, 
      #{specification,jdbcType=VARCHAR}, #{temperatureZoneCode,jdbcType=VARCHAR}, #{barCodes,jdbcType=VARCHAR}, 
      #{boxCodes,jdbcType=VARCHAR}, #{imgUrls,jdbcType=VARCHAR}, #{isManagementSn,jdbcType=INTEGER}, 
      #{extInfo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderItemDO">
    insert into trade_shipping_order_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="warehouseId != null">
        warehouse_id,
      </if>
      <if test="outboundOrderId != null">
        outbound_order_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="skuName != null">
        sku_name,
      </if>
      <if test="plannedQty != null">
        planned_qty,
      </if>
      <if test="actualQty != null">
        actual_qty,
      </if>
      <if test="refundQty != null">
        refund_qty,
      </if>
      <if test="specification != null">
        specification,
      </if>
      <if test="temperatureZoneCode != null">
        temperature_zone_code,
      </if>
      <if test="barCodes != null">
        bar_codes,
      </if>
      <if test="boxCodes != null">
        box_codes,
      </if>
      <if test="imgUrls != null">
        img_urls,
      </if>
      <if test="isManagementSn != null">
        is_management_sn,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="warehouseId != null">
        #{warehouseId,jdbcType=BIGINT},
      </if>
      <if test="outboundOrderId != null">
        #{outboundOrderId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="plannedQty != null">
        #{plannedQty,jdbcType=DECIMAL},
      </if>
      <if test="actualQty != null">
        #{actualQty,jdbcType=DECIMAL},
      </if>
      <if test="refundQty != null">
        #{refundQty,jdbcType=DECIMAL},
      </if>
      <if test="specification != null">
        #{specification,jdbcType=VARCHAR},
      </if>
      <if test="temperatureZoneCode != null">
        #{temperatureZoneCode,jdbcType=VARCHAR},
      </if>
      <if test="barCodes != null">
        #{barCodes,jdbcType=VARCHAR},
      </if>
      <if test="boxCodes != null">
        #{boxCodes,jdbcType=VARCHAR},
      </if>
      <if test="imgUrls != null">
        #{imgUrls,jdbcType=VARCHAR},
      </if>
      <if test="isManagementSn != null">
        #{isManagementSn,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderItemDOExample" resultType="java.lang.Long">
    select count(*) from trade_shipping_order_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update trade_shipping_order_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.warehouseId != null">
        warehouse_id = #{record.warehouseId,jdbcType=BIGINT},
      </if>
      <if test="record.outboundOrderId != null">
        outbound_order_id = #{record.outboundOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.skuId != null">
        sku_id = #{record.skuId,jdbcType=VARCHAR},
      </if>
      <if test="record.skuName != null">
        sku_name = #{record.skuName,jdbcType=VARCHAR},
      </if>
      <if test="record.plannedQty != null">
        planned_qty = #{record.plannedQty,jdbcType=DECIMAL},
      </if>
      <if test="record.actualQty != null">
        actual_qty = #{record.actualQty,jdbcType=DECIMAL},
      </if>
      <if test="record.refundQty != null">
        refund_qty = #{record.refundQty,jdbcType=DECIMAL},
      </if>
      <if test="record.specification != null">
        specification = #{record.specification,jdbcType=VARCHAR},
      </if>
      <if test="record.temperatureZoneCode != null">
        temperature_zone_code = #{record.temperatureZoneCode,jdbcType=VARCHAR},
      </if>
      <if test="record.barCodes != null">
        bar_codes = #{record.barCodes,jdbcType=VARCHAR},
      </if>
      <if test="record.boxCodes != null">
        box_codes = #{record.boxCodes,jdbcType=VARCHAR},
      </if>
      <if test="record.imgUrls != null">
        img_urls = #{record.imgUrls,jdbcType=VARCHAR},
      </if>
      <if test="record.isManagementSn != null">
        is_management_sn = #{record.isManagementSn,jdbcType=INTEGER},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update trade_shipping_order_item
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      warehouse_id = #{record.warehouseId,jdbcType=BIGINT},
      outbound_order_id = #{record.outboundOrderId,jdbcType=BIGINT},
      sku_id = #{record.skuId,jdbcType=VARCHAR},
      sku_name = #{record.skuName,jdbcType=VARCHAR},
      planned_qty = #{record.plannedQty,jdbcType=DECIMAL},
      actual_qty = #{record.actualQty,jdbcType=DECIMAL},
      refund_qty = #{record.refundQty,jdbcType=DECIMAL},
      specification = #{record.specification,jdbcType=VARCHAR},
      temperature_zone_code = #{record.temperatureZoneCode,jdbcType=VARCHAR},
      bar_codes = #{record.barCodes,jdbcType=VARCHAR},
      box_codes = #{record.boxCodes,jdbcType=VARCHAR},
      img_urls = #{record.imgUrls,jdbcType=VARCHAR},
      is_management_sn = #{record.isManagementSn,jdbcType=INTEGER},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderItemDO">
    update trade_shipping_order_item
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="warehouseId != null">
        warehouse_id = #{warehouseId,jdbcType=BIGINT},
      </if>
      <if test="outboundOrderId != null">
        outbound_order_id = #{outboundOrderId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        sku_name = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="plannedQty != null">
        planned_qty = #{plannedQty,jdbcType=DECIMAL},
      </if>
      <if test="actualQty != null">
        actual_qty = #{actualQty,jdbcType=DECIMAL},
      </if>
      <if test="refundQty != null">
        refund_qty = #{refundQty,jdbcType=DECIMAL},
      </if>
      <if test="specification != null">
        specification = #{specification,jdbcType=VARCHAR},
      </if>
      <if test="temperatureZoneCode != null">
        temperature_zone_code = #{temperatureZoneCode,jdbcType=VARCHAR},
      </if>
      <if test="barCodes != null">
        bar_codes = #{barCodes,jdbcType=VARCHAR},
      </if>
      <if test="boxCodes != null">
        box_codes = #{boxCodes,jdbcType=VARCHAR},
      </if>
      <if test="imgUrls != null">
        img_urls = #{imgUrls,jdbcType=VARCHAR},
      </if>
      <if test="isManagementSn != null">
        is_management_sn = #{isManagementSn,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderItemDO">
    update trade_shipping_order_item
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      warehouse_id = #{warehouseId,jdbcType=BIGINT},
      outbound_order_id = #{outboundOrderId,jdbcType=BIGINT},
      sku_id = #{skuId,jdbcType=VARCHAR},
      sku_name = #{skuName,jdbcType=VARCHAR},
      planned_qty = #{plannedQty,jdbcType=DECIMAL},
      actual_qty = #{actualQty,jdbcType=DECIMAL},
      refund_qty = #{refundQty,jdbcType=DECIMAL},
      specification = #{specification,jdbcType=VARCHAR},
      temperature_zone_code = #{temperatureZoneCode,jdbcType=VARCHAR},
      bar_codes = #{barCodes,jdbcType=VARCHAR},
      box_codes = #{boxCodes,jdbcType=VARCHAR},
      img_urls = #{imgUrls,jdbcType=VARCHAR},
      is_management_sn = #{isManagementSn,jdbcType=INTEGER},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into trade_shipping_order_item
    (id, tenant_id, warehouse_id, outbound_order_id, sku_id, sku_name, planned_qty, actual_qty, 
      refund_qty, specification, temperature_zone_code, bar_codes, box_codes, img_urls, 
      is_management_sn, ext_info, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.tenantId,jdbcType=BIGINT}, #{item.warehouseId,jdbcType=BIGINT}, 
        #{item.outboundOrderId,jdbcType=BIGINT}, #{item.skuId,jdbcType=VARCHAR}, #{item.skuName,jdbcType=VARCHAR}, 
        #{item.plannedQty,jdbcType=DECIMAL}, #{item.actualQty,jdbcType=DECIMAL}, #{item.refundQty,jdbcType=DECIMAL}, 
        #{item.specification,jdbcType=VARCHAR}, #{item.temperatureZoneCode,jdbcType=VARCHAR}, 
        #{item.barCodes,jdbcType=VARCHAR}, #{item.boxCodes,jdbcType=VARCHAR}, #{item.imgUrls,jdbcType=VARCHAR}, 
        #{item.isManagementSn,jdbcType=INTEGER}, #{item.extInfo,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>