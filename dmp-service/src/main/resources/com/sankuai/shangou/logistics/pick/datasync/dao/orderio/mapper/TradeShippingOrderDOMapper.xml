<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.mapper.TradeShippingOrderDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="fulfillment_order_id" jdbcType="BIGINT" property="fulfillmentOrderId" />
    <result column="trade_order_no" jdbcType="VARCHAR" property="tradeOrderNo" />
    <result column="trade_order_biz_type" jdbcType="INTEGER" property="tradeOrderBizType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="done_time" jdbcType="TIMESTAMP" property="doneTime" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="INTEGER" property="version" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, warehouse_id, order_no, fulfillment_order_id, trade_order_no, trade_order_biz_type, 
    `status`, receive_time, done_time, ext_info, operator_id, operator_name, create_time, 
    update_time, version
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderDOExample" resultMap="BaseResultMap">
    /*+zebra:oi=true*/
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from trade_shipping_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from trade_shipping_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from trade_shipping_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderDOExample">
    delete from trade_shipping_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderDO">
    insert into trade_shipping_order (id, tenant_id, warehouse_id, 
      order_no, fulfillment_order_id, trade_order_no, 
      trade_order_biz_type, `status`, receive_time, 
      done_time, ext_info, operator_id, 
      operator_name, create_time, update_time, 
      version)
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, #{warehouseId,jdbcType=BIGINT}, 
      #{orderNo,jdbcType=VARCHAR}, #{fulfillmentOrderId,jdbcType=BIGINT}, #{tradeOrderNo,jdbcType=VARCHAR}, 
      #{tradeOrderBizType,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{receiveTime,jdbcType=TIMESTAMP}, 
      #{doneTime,jdbcType=TIMESTAMP}, #{extInfo,jdbcType=VARCHAR}, #{operatorId,jdbcType=BIGINT}, 
      #{operatorName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{version,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderDO">
    insert into trade_shipping_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="warehouseId != null">
        warehouse_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="fulfillmentOrderId != null">
        fulfillment_order_id,
      </if>
      <if test="tradeOrderNo != null">
        trade_order_no,
      </if>
      <if test="tradeOrderBizType != null">
        trade_order_biz_type,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="receiveTime != null">
        receive_time,
      </if>
      <if test="doneTime != null">
        done_time,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="warehouseId != null">
        #{warehouseId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="fulfillmentOrderId != null">
        #{fulfillmentOrderId,jdbcType=BIGINT},
      </if>
      <if test="tradeOrderNo != null">
        #{tradeOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeOrderBizType != null">
        #{tradeOrderBizType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="doneTime != null">
        #{doneTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderDOExample" resultType="java.lang.Long">
    select count(*) from trade_shipping_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update trade_shipping_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.warehouseId != null">
        warehouse_id = #{record.warehouseId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.fulfillmentOrderId != null">
        fulfillment_order_id = #{record.fulfillmentOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.tradeOrderNo != null">
        trade_order_no = #{record.tradeOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeOrderBizType != null">
        trade_order_biz_type = #{record.tradeOrderBizType,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.receiveTime != null">
        receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.doneTime != null">
        done_time = #{record.doneTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=BIGINT},
      </if>
      <if test="record.operatorName != null">
        operator_name = #{record.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update trade_shipping_order
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      warehouse_id = #{record.warehouseId,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      fulfillment_order_id = #{record.fulfillmentOrderId,jdbcType=BIGINT},
      trade_order_no = #{record.tradeOrderNo,jdbcType=VARCHAR},
      trade_order_biz_type = #{record.tradeOrderBizType,jdbcType=INTEGER},
      `status` = #{record.status,jdbcType=INTEGER},
      receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      done_time = #{record.doneTime,jdbcType=TIMESTAMP},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      operator_id = #{record.operatorId,jdbcType=BIGINT},
      operator_name = #{record.operatorName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      version = #{record.version,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderDO">
    update trade_shipping_order
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="warehouseId != null">
        warehouse_id = #{warehouseId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="fulfillmentOrderId != null">
        fulfillment_order_id = #{fulfillmentOrderId,jdbcType=BIGINT},
      </if>
      <if test="tradeOrderNo != null">
        trade_order_no = #{tradeOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeOrderBizType != null">
        trade_order_biz_type = #{tradeOrderBizType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="receiveTime != null">
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="doneTime != null">
        done_time = #{doneTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.pick.datasync.dao.orderio.model.TradeShippingOrderDO">
    update trade_shipping_order
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      warehouse_id = #{warehouseId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      fulfillment_order_id = #{fulfillmentOrderId,jdbcType=BIGINT},
      trade_order_no = #{tradeOrderNo,jdbcType=VARCHAR},
      trade_order_biz_type = #{tradeOrderBizType,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      done_time = #{doneTime,jdbcType=TIMESTAMP},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=BIGINT},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into trade_shipping_order
    (id, tenant_id, warehouse_id, order_no, fulfillment_order_id, trade_order_no, trade_order_biz_type, 
      `status`, receive_time, done_time, ext_info, operator_id, operator_name, create_time, 
      update_time, version)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.tenantId,jdbcType=BIGINT}, #{item.warehouseId,jdbcType=BIGINT}, 
        #{item.orderNo,jdbcType=VARCHAR}, #{item.fulfillmentOrderId,jdbcType=BIGINT}, #{item.tradeOrderNo,jdbcType=VARCHAR}, 
        #{item.tradeOrderBizType,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER}, #{item.receiveTime,jdbcType=TIMESTAMP}, 
        #{item.doneTime,jdbcType=TIMESTAMP}, #{item.extInfo,jdbcType=VARCHAR}, #{item.operatorId,jdbcType=BIGINT}, 
        #{item.operatorName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.version,jdbcType=INTEGER})
    </foreach>
  </insert>
</mapper>