<?xml version="1.0" encoding="UTF-8"?>
<configuration status="debug">
    <appenders>

        <!-- Console 只在本地调试时打开，线上请注释掉-->
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="%d{yyyy/MM/dd HH:mm:ss.SSS} %t [%p] %c{1} (%F:%L) %msg%n" />
        </Console>


        <!--异步磁盘appender，默认按天&按512M文件大小切分日志，默认最多保留30个日志文件，默认为noblocking写日志模式-->
        <XMDFile name="requestLog" fileName="request.log" rolloverMax="5"></XMDFile>

        <!--ERROR日志、WARN日志单独输出到一个文件-->
        <XMDFile name="errorLog" fileName="error.log">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>
        <XMDFile name="warnLog" fileName="warn.log">
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>

        <!--日志远程上报-->
        <Scribe name="ScribeAppender">
            <!-- 在指定日志名方面，scribeCategory 和 appkey 两者至少存在一种，且 scribeCategory 高于 appkey。-->
            <!-- <Property name="scribeCategory">data_update_test_lc</Property> -->
            <LcLayout/>
        </Scribe>
        <Async name="ScribeAsyncAppender" blocking="false">
            <AppenderRef ref="ScribeAppender"/>
        </Async>

        <!-- 集成CAT -->
        <CatAppender name="CatAppender" />

    </appenders>

    <loggers>
        <logger name="com.meituan" level="info"/>

        <logger name="org.springframework" level="info"/>

        <logger name="com.sankuai.shangou.advisor" level="debug" additivity="false">
            <appender-ref ref="requestLog"/>
            <appender-ref ref="warnLog"/>
            <appender-ref ref="errorLog"/>
            <appender-ref ref="ScribeAsyncAppender"/>
            <appender-ref ref="Console" />
        </logger>

        <root level="info">
            <appender-ref ref="requestLog"/>
            <appender-ref ref="warnLog"/>
            <appender-ref ref="errorLog"/>
            <appender-ref ref="ScribeAsyncAppender"/>
            <appender-ref ref="CatAppender" />
            <appender-ref ref="Console" />
        </root>
    </loggers>
</configuration>