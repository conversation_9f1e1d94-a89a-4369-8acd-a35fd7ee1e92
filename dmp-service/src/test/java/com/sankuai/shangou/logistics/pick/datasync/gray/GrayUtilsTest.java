package com.sankuai.shangou.logistics.pick.datasync.gray;

import com.sankuai.shangou.logistics.delivery.config.ExternalServiceBeanConfig;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.pick.datasync.TestStartApp;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @since 2024/5/11 15:54
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestStartApp.class)
@ActiveProfiles("test")
@ContextConfiguration(classes = {ExternalServiceBeanConfig.class})
@Slf4j
public class GrayUtilsTest {

    @Test
    public void test() {
        GrayConfigUtils.judgeIsGrayStore(1000L, 110L, "hahah");
    }
}
