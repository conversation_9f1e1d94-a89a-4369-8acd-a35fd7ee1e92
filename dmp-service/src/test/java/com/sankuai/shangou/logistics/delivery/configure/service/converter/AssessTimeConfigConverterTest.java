package com.sankuai.shangou.logistics.delivery.configure.service.converter;

import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.model.value.AssessTimeConfig;
import com.sankuai.shangou.logistics.delivery.configure.model.value.ExpressionCondition;
import com.sankuai.shangou.logistics.delivery.configure.model.value.ExpressionNode;
import com.sankuai.shangou.logistics.delivery.configure.model.value.interval.Interval;
import com.sankuai.shangou.logistics.delivery.configure.model.value.interval.IntervalNumber;
import com.sankuai.shangou.logistics.delivery.configure.model.value.interval.IntervalTypeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AssessTimeConfigConverter测试类
 *
 * <AUTHOR>
 * @date 2025-07-18
 * @email <EMAIL>
 */
@ExtendWith(MockitoExtension.class)
class AssessTimeConfigConverterTest {

    @InjectMocks
    private AssessTimeConfigConverter converter;

    private AssessTimeConfig assessTimeConfig;
    private ExpressionNode rootNode;

    @BeforeEach
    void setUp() {
        assessTimeConfig = new AssessTimeConfig();
        assessTimeConfig.setType(1);
        assessTimeConfig.setHint("测试提示");
    }

    @Test
    void testConvertToVO_NullInput() {
        DeliveryConfigDetailVO.AssertTimeVO result = converter.convertToVO(null);
        assertNull(result);
    }

    @Test
    void testConvertToVO_NullExpressionNode() {
        assessTimeConfig.setExpressionNode(null);
        
        DeliveryConfigDetailVO.AssertTimeVO result = converter.convertToVO(assessTimeConfig);
        
        assertNotNull(result);
        assertEquals(1, result.getType());
        assertEquals("测试提示", result.getHint());
        assertTrue(result.getOrderTags().isEmpty());
        assertTrue(result.getDistanceConfigVOS().isEmpty());
    }

    @Test
    void testConvertToVO_SimpleExpressionTree() {
        // 构建简单的表达式树：根节点 -> 距离条件 -> 叶子节点(公式)
        ExpressionNode leafNode = createLeafNode("${delivery_distance}", "[0,5]", "30");
        rootNode = createRootNode(Collections.singletonList(leafNode));
        assessTimeConfig.setExpressionNode(rootNode);

        DeliveryConfigDetailVO.AssertTimeVO result = converter.convertToVO(assessTimeConfig);

        assertNotNull(result);
        assertEquals(1, result.getType());
        assertEquals("测试提示", result.getHint());
        assertTrue(result.getOrderTags().isEmpty());
        assertEquals(1, result.getDistanceConfigVOS().size());
        
        DeliveryConfigDetailVO.DistanceConfigVO distanceConfig = result.getDistanceConfigVOS().get(0);
        assertEquals("30", distanceConfig.getFormula());
        assertEquals(1, distanceConfig.getCondition().size());
        assertEquals("${delivery_distance}", distanceConfig.getCondition().get(0).getIdentifer());
    }

    @Test
    void testConvertToVO_WithOrderTagConditions() {
        // 构建包含orderTag条件的表达式树
        ExpressionNode orderTagNode = createConditionNode("${order_tag}", "[301,302]");
        ExpressionNode distanceNode = createLeafNode("${delivery_distance}", "[0,5]", "25");
        orderTagNode.setSubs(Collections.singletonList(distanceNode));
        
        rootNode = createRootNode(Collections.singletonList(orderTagNode));
        assessTimeConfig.setExpressionNode(rootNode);

        DeliveryConfigDetailVO.AssertTimeVO result = converter.convertToVO(assessTimeConfig);

        assertNotNull(result);
        assertEquals(2, result.getOrderTags().size());
        assertTrue(result.getOrderTags().contains(301));
        assertTrue(result.getOrderTags().contains(302));
        
        assertEquals(1, result.getDistanceConfigVOS().size());
        DeliveryConfigDetailVO.DistanceConfigVO distanceConfig = result.getDistanceConfigVOS().get(0);
        assertEquals("25", distanceConfig.getFormula());
        assertEquals(1, distanceConfig.getCondition().size());
        assertEquals("${delivery_distance}", distanceConfig.getCondition().get(0).getIdentifer());
    }

    @Test
    void testConvertToVO_ComplexExpressionTree() {
        // 构建复杂的表达式树：多个分支，包含orderTag和其他条件
        ExpressionNode orderTagNode1 = createConditionNode("${order_tag}", "[301]");
        ExpressionNode distanceNode1 = createLeafNode("${delivery_distance}", "[0,3]", "20");
        orderTagNode1.setSubs(Collections.singletonList(distanceNode1));

        ExpressionNode orderTagNode2 = createConditionNode("${order_tag}", "[302]");
        ExpressionNode distanceNode2 = createLeafNode("${delivery_distance}", "[3,10]", "35");
        orderTagNode2.setSubs(Collections.singletonList(distanceNode2));

        rootNode = createRootNode(Arrays.asList(orderTagNode1, orderTagNode2));
        assessTimeConfig.setExpressionNode(rootNode);

        DeliveryConfigDetailVO.AssertTimeVO result = converter.convertToVO(assessTimeConfig);

        assertNotNull(result);
        assertEquals(2, result.getOrderTags().size());
        assertTrue(result.getOrderTags().contains(301));
        assertTrue(result.getOrderTags().contains(302));
        
        assertEquals(2, result.getDistanceConfigVOS().size());
    }

    @Test
    void testConvertFromVO_NullInput() {
        AssessTimeConfig result = converter.convertFromVO(null);
        assertNull(result);
    }

    @Test
    void testConvertFromVO_SimpleDistanceConfig() {
        // 创建简单的VO
        DeliveryConfigDetailVO.AssertTimeVO vo = new DeliveryConfigDetailVO.AssertTimeVO();
        vo.setType(1);
        vo.setHint("测试提示");
        vo.setOrderTags(Collections.emptyList());

        DeliveryConfigDetailVO.DistanceConfigVO distanceConfig = new DeliveryConfigDetailVO.DistanceConfigVO();
        distanceConfig.setFormula("30");
        distanceConfig.setCondition(Collections.singletonList(createConditionVO("${delivery_distance}", "[0,5]")));
        vo.setDistanceConfigVOS(Collections.singletonList(distanceConfig));

        AssessTimeConfig result = converter.convertFromVO(vo);

        assertNotNull(result);
        assertEquals(1, result.getType());
        assertEquals("测试提示", result.getHint());
        assertNotNull(result.getExpressionNode());

        // 验证表达式树结构
        ExpressionNode rootNode = result.getExpressionNode();
        assertNull(rootNode.getCondition()); // 根节点条件为空
        assertEquals(1, rootNode.getSubs().size());

        ExpressionNode childNode = rootNode.getSubs().get(0);
        assertNotNull(childNode.getCondition());
        assertEquals("${delivery_distance}", childNode.getCondition().getIdentifier());
        assertEquals("30", childNode.getFormula());
    }

    @Test
    void testConvertFromVO_WithOrderTags() {
        // 创建包含orderTag的VO
        DeliveryConfigDetailVO.AssertTimeVO vo = new DeliveryConfigDetailVO.AssertTimeVO();
        vo.setType(2);
        vo.setHint("包含orderTag");
        vo.setOrderTags(Arrays.asList(301, 302));

        DeliveryConfigDetailVO.DistanceConfigVO distanceConfig = new DeliveryConfigDetailVO.DistanceConfigVO();
        distanceConfig.setFormula("25");
        distanceConfig.setCondition(Collections.singletonList(createConditionVO("${delivery_distance}", "[0,5]")));
        vo.setDistanceConfigVOS(Collections.singletonList(distanceConfig));

        AssessTimeConfig result = converter.convertFromVO(vo);

        assertNotNull(result);
        assertEquals(2, result.getType());
        assertNotNull(result.getExpressionNode());

        // 验证表达式树结构：应该有2个orderTag分支（301和302）
        ExpressionNode rootNode = result.getExpressionNode();
        assertNull(rootNode.getCondition());
        assertEquals(2, rootNode.getSubs().size()); // 两个orderTag分支

        // 验证第一个分支
        ExpressionNode firstBranch = rootNode.getSubs().get(0);
        assertNotNull(firstBranch.getCondition());
        assertEquals("${order_tag}", firstBranch.getCondition().getIdentifier());
        assertEquals(1, firstBranch.getSubs().size());

        ExpressionNode leafNode = firstBranch.getSubs().get(0);
        assertEquals("${delivery_distance}", leafNode.getCondition().getIdentifier());
        assertEquals("25", leafNode.getFormula());
    }

    @Test
    void testConvertFromVO_EmptyDistanceConfigs() {
        DeliveryConfigDetailVO.AssertTimeVO vo = new DeliveryConfigDetailVO.AssertTimeVO();
        vo.setType(3);
        vo.setHint("空配置");
        vo.setOrderTags(Collections.emptyList());
        vo.setDistanceConfigVOS(Collections.emptyList());

        AssessTimeConfig result = converter.convertFromVO(vo);

        assertNotNull(result);
        assertEquals(3, result.getType());
        assertNotNull(result.getExpressionNode());

        ExpressionNode rootNode = result.getExpressionNode();
        assertNull(rootNode.getCondition());
        assertTrue(rootNode.getSubs().isEmpty());
    }

    @Test
    void testRoundTripConversion() {
        // 测试往返转换：VO -> Config -> VO
        DeliveryConfigDetailVO.AssertTimeVO originalVO = new DeliveryConfigDetailVO.AssertTimeVO();
        originalVO.setType(1);
        originalVO.setHint("往返测试");
        originalVO.setOrderTags(Arrays.asList(301));

        DeliveryConfigDetailVO.DistanceConfigVO distanceConfig = new DeliveryConfigDetailVO.DistanceConfigVO();
        distanceConfig.setFormula("30");
        distanceConfig.setCondition(Collections.singletonList(createConditionVO("${delivery_distance}", "[0,5]")));
        originalVO.setDistanceConfigVOS(Collections.singletonList(distanceConfig));

        // VO -> Config
        AssessTimeConfig config = converter.convertFromVO(originalVO);
        assertNotNull(config);

        // Config -> VO
        DeliveryConfigDetailVO.AssertTimeVO resultVO = converter.convertToVO(config);
        assertNotNull(resultVO);

        // 验证基本字段
        assertEquals(originalVO.getType(), resultVO.getType());
        assertEquals(originalVO.getHint(), resultVO.getHint());
        assertEquals(originalVO.getOrderTags().size(), resultVO.getOrderTags().size());
        assertTrue(resultVO.getOrderTags().contains(301));
    }

    // 辅助方法：创建根节点
    private ExpressionNode createRootNode(List<ExpressionNode> subs) {
        ExpressionNode root = new ExpressionNode();
        root.setCondition(null); // 根节点条件为空
        root.setSubs(subs);
        root.setFormula(null);
        return root;
    }

    // 辅助方法：创建条件节点
    private ExpressionNode createConditionNode(String identifier, String values) {
        ExpressionNode node = new ExpressionNode();
        node.setCondition(createCondition(identifier, values));
        node.setSubs(Collections.emptyList());
        node.setFormula(null);
        return node;
    }

    // 辅助方法：创建叶子节点
    private ExpressionNode createLeafNode(String identifier, String values, String formula) {
        ExpressionNode node = new ExpressionNode();
        node.setCondition(createCondition(identifier, values));
        node.setSubs(Collections.emptyList());
        node.setFormula(formula);
        return node;
    }

    // 辅助方法：创建条件
    private ExpressionCondition createCondition(String identifier, String values) {
        ExpressionCondition condition = new ExpressionCondition();
        condition.setIdentifier(identifier);
        condition.setInterval(createInterval(values));
        return condition;
    }

    // 辅助方法：创建区间
    private Interval createInterval(String values) {
        Interval interval = new Interval();
        interval.setIntervalType(IntervalTypeEnum.ALL_CLOSE.getType());
        
        // 解析values字符串，例如 "[301,302]" -> ["301", "302"]
        String cleanValues = values.replaceAll("[\\[\\]]", "");
        String[] valueArray = cleanValues.split(",");
        List<IntervalNumber> intervalNumbers = Arrays.stream(valueArray)
                .map(String::trim)
                .map(IntervalNumber::new)
                .collect(java.util.stream.Collectors.toList());
        
        interval.setValues(intervalNumbers);
        return interval;
    }

    // 辅助方法：创建ConditionVO
    private DeliveryConfigDetailVO.ConditionVO createConditionVO(String identifier, String values) {
        DeliveryConfigDetailVO.ConditionVO conditionVO = new DeliveryConfigDetailVO.ConditionVO();
        conditionVO.setIdentifer(identifier);
        conditionVO.setInterval(createIntervalVO(values));
        return conditionVO;
    }

    // 辅助方法：创建IntervalVO
    private DeliveryConfigDetailVO.IntervalVO createIntervalVO(String values) {
        DeliveryConfigDetailVO.IntervalVO intervalVO = new DeliveryConfigDetailVO.IntervalVO();
        intervalVO.setIntervalType(4); // ALL_CLOSE

        // 解析values字符串，例如 "[0,5]" -> ["0", "5"]
        String cleanValues = values.replaceAll("[\\[\\]]", "");
        String[] valueArray = cleanValues.split(",");
        List<String> valueList = Arrays.stream(valueArray)
                .map(String::trim)
                .collect(Collectors.toList());

        intervalVO.setValues(valueList);
        return intervalVO;
    }
}
