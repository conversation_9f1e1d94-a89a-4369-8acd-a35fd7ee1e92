package com.sankuai.shangou.logistics.delivery.configure.service;

import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.repository.DeliveryDimensionPoiRepository;
import com.sankuai.shangou.logistics.delivery.configure.repository.DeliveryPoiRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * DeliveryConfigService测试类
 *
 * <AUTHOR>
 * @date 2025-07-04
 * @email <EMAIL>
 */
class DeliveryConfigServiceTest {

    @Mock
    private DeliveryDimensionPoiRepository deliveryDimensionPoiRepository;

    @Mock
    private DeliveryPoiRepository deliveryPoiRepository;

    @InjectMocks
    private DeliveryConfigService deliveryConfigService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testQueryConfigDetail_Success() {
        // Given
        Long tenantId = 1L;
        Long storeId = 100L;

        // Mock repository返回空结果
        when(deliveryPoiRepository.queryDeliveryPoi(anyLong(), anyLong()))
                .thenReturn(Optional.empty());
        when(deliveryDimensionPoiRepository.queryDeliveryDimensionPoi(anyLong(), anyLong()))
                .thenReturn(Optional.empty());

        // When
        DeliveryConfigDetailVO result = deliveryConfigService.queryConfigDetail(tenantId, storeId);

        // Then
        assertNotNull(result);
        assertEquals(tenantId, result.getTenantId());
        assertEquals(storeId, result.getStoreId());
    }

    @Test
    void testQueryConfigDetail_WithException() {
        // Given
        Long tenantId = 1L;
        Long storeId = 100L;

        // Mock repository抛出异常
        when(deliveryPoiRepository.queryDeliveryPoi(anyLong(), anyLong()))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            deliveryConfigService.queryConfigDetail(tenantId, storeId);
        });

        assertTrue(exception.getMessage().contains("查询门店配置明细失败"));
    }
}
