# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a TMS (Transport Management System) delivery application for Meituan's fulfillment system. It's a multi-module Maven project that handles delivery orders across multiple platforms and channels, with sophisticated rider management and real-time tracking capabilities.

## Development Commands

### Building the Project
```bash
# Build all modules
mvn clean compile

# Build and run tests
mvn clean test

# Package the application
mvn clean package

# Skip tests when building
mvn clean package -DskipTests

# Build specific module
mvn clean package -pl reco_fulfillment_tms-server
```

### Running Tests
```bash
# Run all tests
mvn test

# Run tests for specific module
mvn test -pl reco_fulfillment_tms-server

# Run specific test class
mvn test -Dtest=LocalTest -pl reco_fulfillment_tms-server

# Run tests with specific profile
mvn test -Dspring.profiles.active=test
```

### Running the Application
```bash
# Run with default profile (local)
mvn spring-boot:run -pl reco_fulfillment_tms-server

# Run with specific profile
mvn spring-boot:run -pl reco_fulfillment_tms-server -Dspring.profiles.active=dev

# Run with JVM arguments
mvn spring-boot:run -pl reco_fulfillment_tms-server -Dspring-boot.run.jvmArguments="-Xmx2g"
```

### MyBatis Code Generation
```bash
# Generate DAO classes using MyBatis Generator
mvn mybatis-generator:generate -pl reco_fulfillment_tms-delivery-dao
```

## Architecture Overview

### Module Structure
The project follows a **multi-module Maven architecture** with clear separation of concerns:

- **`reco_fulfillment_tms-server`** - Main Spring Boot application with business logic
- **`reco_fulfillment_tms-delivery-client`** - Client SDK and Thrift API contracts
- **`reco_fulfillment_tms-delivery-dao`** - Data access layer with MyBatis mappers
- **`reco_fulfillment_tms-rider-delivery-client`** - Rider-specific client APIs
- **`tms-delivery-access`** - Application services and access layer
- **`tms-delivery-rider`** - Rider domain services and business logic
- **`tms-delivery-poi`** - Point of Interest related services
- **`tms-delivery-platform`** - Platform integration layer
- **`tms-delivery-task`** - Task and batch processing services
- **`dms-delivery-base`** - Base domain models and shared components

### Key Domain Concepts

**Core Business Flow:**
1. **Order Creation** - DeliveryOrder entities created with customer and store info
2. **Channel Selection** - Multiple delivery channels (聚合运力, 麦芽田, 青云聚信, etc.)
3. **Rider Assignment** - Intelligent rider matching and assignment
4. **Real-time Tracking** - Location synchronization and status updates
5. **Delivery Completion** - Final status updates and completion handling

**Delivery Status Flow:**
```
INIT → DELIVERY_LAUNCHED → WAITING_TO_ASSIGN_RIDER → 
RIDER_ASSIGNED → RIDER_ARRIVED_SHOP → RIDER_TAKEN_GOODS → 
DELIVERY_DONE
```

### Technology Stack

- **Framework**: Spring Boot 2.x with Spring Framework
- **Database**: MySQL with Zebra sharding middleware
- **ORM**: MyBatis with MyBatis Generator
- **Caching**: Redis (Squirrel) with multiple cluster support
- **RPC**: Apache Thrift for service-to-service communication
- **Messaging**: Mafka (Kafka) for asynchronous processing
- **Search**: Elasticsearch for analytics and search
- **Testing**: JUnit 4, Mockito 2, PowerMock, DBUnit

### Configuration Management

The application uses **Spring profiles** for environment-specific configurations:
- **local** - Local development (default)
- **dev** - Development environment
- **test** - Test environment
- **staging** - Staging environment
- **prod** - Production environment

Database configurations are managed through **Zebra** with different `jdbcRef` values per environment.

## Development Guidelines

### Code Style (from .cursor/rules/general.mdc)

**Core Principles:**
- Follow SOLID, DRY, KISS, and YAGNI principles
- Follow OWASP security best practices
- Use Java 8 features appropriately
- Maintain consistent code style with existing codebase

**Key Requirements:**
- Use `@Resource` for dependency injection (not `@Autowired`)
- Use `@Slf4j` for logging (not System.out or other logging frameworks)
- Use MyBatis Generator for all database code (never modify generated code)
- Use enums for status-type fields with proper error handling
- Use `@Data` annotation for entity classes
- Repository classes must be interfaces with `@Repository` implementations
- Use `@Transactional` for multi-database operations
- Use logical deletion only (never physical deletion)

**Application Layer Rules:**
- Keep core methods around 50 lines
- Extract private methods when code blocks exceed 10 lines
- Make external service calls explicit in main methods
- Don't catch exceptions in app services (let global handlers manage them)
- Use static inner classes for entity definitions when appropriate

### Testing Strategy

**Test Base Classes:**
- Extend `TestBase` for Spring Boot integration tests
- Use `StartAppTest` as the test application class
- Database testing with DbUnit and Spring Test DbUnit

**Test Frameworks:**
- JUnit 4 for test structure
- Mockito 2 for mocking
- PowerMock for advanced mocking scenarios
- DBUnit for database testing

### Database Development

**MyBatis Generator Usage:**
- All database interaction code must be generated using MyBatis Generator
- Generated code (PO objects, Mapper interfaces, XML files, Example classes) must never be modified
- Use Example classes for building query conditions
- Use PageHelper plugin for pagination

**Database Best Practices:**
- Use logical deletion only (all tables have logical deletion fields)
- Use `@Transactional` for operations involving multiple database calls
- Leverage Zebra sharding for performance and scalability

## Common Development Patterns

### External Service Integration
Multiple delivery platforms are integrated through unified client abstractions:
- Implement platform-specific clients under `facade/` package
- Use consistent error handling and response mapping
- Implement proper retry mechanisms for platform calls

### Event-Driven Architecture
The system uses Mafka for asynchronous processing:
- Message producers are configured in `MessageProducerConfiguration`
- Consumers should implement proper error handling and dead letter queues
- Use consistent message serialization/deserialization

### Caching Strategy
Multiple Redis clusters are used for different purposes:
- `redis-sg-common` - General application cache
- `redis-sg-newsupply-ofc` - Supply chain specific cache
- `redis-sg-drunkhorse-business` - Business logic cache

## Important Notes

- The system is designed for multi-tenant operation with proper tenant isolation
- All configurations are environment-specific and managed through Spring profiles
- External platform integrations require proper error handling and fallback mechanisms
- Real-time location tracking requires careful handling of high-frequency updates
- The system supports multiple delivery channels with different business rules and workflows