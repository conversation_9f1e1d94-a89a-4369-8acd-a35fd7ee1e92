<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.dao.config.mapper.ConfigTemplateItemDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="config_template_id" jdbcType="BIGINT" property="configTemplateId" />
    <result column="template_type" jdbcType="INTEGER" property="templateType" />
    <result column="config_content" jdbcType="CHAR" property="configContent" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, config_template_id, template_type, config_content, created_at, create_by, 
    update_time, is_delete
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from config_template_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from config_template_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from config_template_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDOExample">
    delete from config_template_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDO" useGeneratedKeys="true">
    insert into config_template_item (tenant_id, config_template_id, template_type, 
      config_content, created_at, create_by,
      update_time, is_delete)
    values (#{tenantId,jdbcType=BIGINT}, #{configTemplateId,jdbcType=BIGINT}, #{templateType,jdbcType=INTEGER}, 
      #{configContent,jdbcType=CHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDO" useGeneratedKeys="true">
    insert into config_template_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="configTemplateId != null">
        config_template_id,
      </if>
      <if test="templateType != null">
        template_type,
      </if>
      <if test="configContent != null">
        config_content,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="configTemplateId != null">
        #{configTemplateId,jdbcType=BIGINT},
      </if>
      <if test="templateType != null">
        #{templateType,jdbcType=INTEGER},
      </if>
      <if test="configContent != null">
        #{configContent,jdbcType=CHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDOExample" resultType="java.lang.Long">
    select count(*) from config_template_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update config_template_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.configTemplateId != null">
        config_template_id = #{record.configTemplateId,jdbcType=BIGINT},
      </if>
      <if test="record.templateType != null">
        template_type = #{record.templateType,jdbcType=INTEGER},
      </if>
      <if test="record.configContent != null">
        config_content = #{record.configContent,jdbcType=CHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update config_template_item
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      config_template_id = #{record.configTemplateId,jdbcType=BIGINT},
      template_type = #{record.templateType,jdbcType=INTEGER},
      config_content = #{record.configContent,jdbcType=CHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_delete = #{record.isDelete,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDO">
    update config_template_item
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="configTemplateId != null">
        config_template_id = #{configTemplateId,jdbcType=BIGINT},
      </if>
      <if test="templateType != null">
        template_type = #{templateType,jdbcType=INTEGER},
      </if>
      <if test="configContent != null">
        config_content = #{configContent,jdbcType=CHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDO">
    update config_template_item
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      config_template_id = #{configTemplateId,jdbcType=BIGINT},
      template_type = #{templateType,jdbcType=INTEGER},
      config_content = #{configContent,jdbcType=CHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>