<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension.ConfigTaskDOExMapper">

    <select id="queryBatchTaskList" resultMap="com.sankuai.shangou.logistics.delivery.dao.config.mapper.ConfigTaskDOMapper.BaseResultMap">
        select <include refid="com.sankuai.shangou.logistics.delivery.dao.config.mapper.ConfigTaskDOMapper.Base_Column_List" />
        from config_task
        where tenant_id = #{tenantId}
            and status = #{status}
            and config_template_id = #{configTemplateId}
            and task_type = #{taskType}
            and created_at between #{beginTime} and #{endTime}
            <if test="taskName != null">
                and task_name like '%' || #{taskName} || '%'
            </if>
            <if test="operator != null">
                and (operator like '%' || #{operator} || '%' or operator_name like '%' || #{operator} || '%')
            </if>
    </select>
</mapper>