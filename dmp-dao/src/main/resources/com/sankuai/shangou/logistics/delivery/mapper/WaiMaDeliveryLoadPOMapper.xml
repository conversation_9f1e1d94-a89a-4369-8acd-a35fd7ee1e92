<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.mapper.WaiMaDeliveryLoadPOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryLoadPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="logistics_unit_id" jdbcType="BIGINT" property="logisticsUnitId" />
    <result column="channel_id" jdbcType="INTEGER" property="channelId" />
    <result column="channel_poi_id" jdbcType="VARCHAR" property="channelPoiId" />
    <result column="scheduled_employee_delivery_load_include_unscheduled_order" jdbcType="DECIMAL" property="scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder" />
    <result column="scheduled_employee_delivery_load_exclude_unscheduled_order" jdbcType="DECIMAL" property="scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder" />
    <result column="attendance_delivery_load" jdbcType="DECIMAL" property="attendanceDeliveryLoad" />
    <result column="fulfill_employee_delivery_load_include_wait_accept_order" jdbcType="DECIMAL" property="fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder" />
    <result column="fulfill_employee_delivery_load_exclude_wait_accept_order" jdbcType="DECIMAL" property="fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder" />
    <result column="biz_time" jdbcType="TIMESTAMP" property="bizTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, logistics_unit_id, channel_id, channel_poi_id, scheduled_employee_delivery_load_include_unscheduled_order, 
    scheduled_employee_delivery_load_exclude_unscheduled_order, attendance_delivery_load, 
    fulfill_employee_delivery_load_include_wait_accept_order, fulfill_employee_delivery_load_exclude_wait_accept_order, 
    biz_time, create_time, update_time, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryLoadPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from waima_delivery_load
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from waima_delivery_load
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from waima_delivery_load
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryLoadPOExample">
    delete from waima_delivery_load
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryLoadPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into waima_delivery_load (logistics_unit_id, channel_id, channel_poi_id, 
      scheduled_employee_delivery_load_include_unscheduled_order, scheduled_employee_delivery_load_exclude_unscheduled_order, 
      attendance_delivery_load, fulfill_employee_delivery_load_include_wait_accept_order, 
      fulfill_employee_delivery_load_exclude_wait_accept_order, biz_time, 
      create_time, update_time, is_deleted
      )
    values (#{logisticsUnitId,jdbcType=BIGINT}, #{channelId,jdbcType=INTEGER}, #{channelPoiId,jdbcType=VARCHAR}, 
      #{scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder,jdbcType=DECIMAL}, #{scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder,jdbcType=DECIMAL}, 
      #{attendanceDeliveryLoad,jdbcType=DECIMAL}, #{fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder,jdbcType=DECIMAL}, 
      #{fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder,jdbcType=DECIMAL}, #{bizTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryLoadPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into waima_delivery_load
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="logisticsUnitId != null">
        logistics_unit_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="channelPoiId != null">
        channel_poi_id,
      </if>
      <if test="scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder != null">
        scheduled_employee_delivery_load_include_unscheduled_order,
      </if>
      <if test="scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder != null">
        scheduled_employee_delivery_load_exclude_unscheduled_order,
      </if>
      <if test="attendanceDeliveryLoad != null">
        attendance_delivery_load,
      </if>
      <if test="fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder != null">
        fulfill_employee_delivery_load_include_wait_accept_order,
      </if>
      <if test="fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder != null">
        fulfill_employee_delivery_load_exclude_wait_accept_order,
      </if>
      <if test="bizTime != null">
        biz_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="logisticsUnitId != null">
        #{logisticsUnitId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="channelPoiId != null">
        #{channelPoiId,jdbcType=VARCHAR},
      </if>
      <if test="scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder != null">
        #{scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder,jdbcType=DECIMAL},
      </if>
      <if test="scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder != null">
        #{scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder,jdbcType=DECIMAL},
      </if>
      <if test="attendanceDeliveryLoad != null">
        #{attendanceDeliveryLoad,jdbcType=DECIMAL},
      </if>
      <if test="fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder != null">
        #{fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder,jdbcType=DECIMAL},
      </if>
      <if test="fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder != null">
        #{fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder,jdbcType=DECIMAL},
      </if>
      <if test="bizTime != null">
        #{bizTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryLoadPOExample" resultType="java.lang.Long">
    select count(*) from waima_delivery_load
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update waima_delivery_load
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.logisticsUnitId != null">
        logistics_unit_id = #{record.logisticsUnitId,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=INTEGER},
      </if>
      <if test="record.channelPoiId != null">
        channel_poi_id = #{record.channelPoiId,jdbcType=VARCHAR},
      </if>
      <if test="record.scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder != null">
        scheduled_employee_delivery_load_include_unscheduled_order = #{record.scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder,jdbcType=DECIMAL},
      </if>
      <if test="record.scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder != null">
        scheduled_employee_delivery_load_exclude_unscheduled_order = #{record.scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder,jdbcType=DECIMAL},
      </if>
      <if test="record.attendanceDeliveryLoad != null">
        attendance_delivery_load = #{record.attendanceDeliveryLoad,jdbcType=DECIMAL},
      </if>
      <if test="record.fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder != null">
        fulfill_employee_delivery_load_include_wait_accept_order = #{record.fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder,jdbcType=DECIMAL},
      </if>
      <if test="record.fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder != null">
        fulfill_employee_delivery_load_exclude_wait_accept_order = #{record.fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder,jdbcType=DECIMAL},
      </if>
      <if test="record.bizTime != null">
        biz_time = #{record.bizTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update waima_delivery_load
    set id = #{record.id,jdbcType=BIGINT},
      logistics_unit_id = #{record.logisticsUnitId,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=INTEGER},
      channel_poi_id = #{record.channelPoiId,jdbcType=VARCHAR},
      scheduled_employee_delivery_load_include_unscheduled_order = #{record.scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder,jdbcType=DECIMAL},
      scheduled_employee_delivery_load_exclude_unscheduled_order = #{record.scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder,jdbcType=DECIMAL},
      attendance_delivery_load = #{record.attendanceDeliveryLoad,jdbcType=DECIMAL},
      fulfill_employee_delivery_load_include_wait_accept_order = #{record.fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder,jdbcType=DECIMAL},
      fulfill_employee_delivery_load_exclude_wait_accept_order = #{record.fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder,jdbcType=DECIMAL},
      biz_time = #{record.bizTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryLoadPO">
    update waima_delivery_load
    <set>
      <if test="logisticsUnitId != null">
        logistics_unit_id = #{logisticsUnitId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=INTEGER},
      </if>
      <if test="channelPoiId != null">
        channel_poi_id = #{channelPoiId,jdbcType=VARCHAR},
      </if>
      <if test="scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder != null">
        scheduled_employee_delivery_load_include_unscheduled_order = #{scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder,jdbcType=DECIMAL},
      </if>
      <if test="scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder != null">
        scheduled_employee_delivery_load_exclude_unscheduled_order = #{scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder,jdbcType=DECIMAL},
      </if>
      <if test="attendanceDeliveryLoad != null">
        attendance_delivery_load = #{attendanceDeliveryLoad,jdbcType=DECIMAL},
      </if>
      <if test="fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder != null">
        fulfill_employee_delivery_load_include_wait_accept_order = #{fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder,jdbcType=DECIMAL},
      </if>
      <if test="fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder != null">
        fulfill_employee_delivery_load_exclude_wait_accept_order = #{fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder,jdbcType=DECIMAL},
      </if>
      <if test="bizTime != null">
        biz_time = #{bizTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryLoadPO">
    update waima_delivery_load
    set logistics_unit_id = #{logisticsUnitId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=INTEGER},
      channel_poi_id = #{channelPoiId,jdbcType=VARCHAR},
      scheduled_employee_delivery_load_include_unscheduled_order = #{scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder,jdbcType=DECIMAL},
      scheduled_employee_delivery_load_exclude_unscheduled_order = #{scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder,jdbcType=DECIMAL},
      attendance_delivery_load = #{attendanceDeliveryLoad,jdbcType=DECIMAL},
      fulfill_employee_delivery_load_include_wait_accept_order = #{fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder,jdbcType=DECIMAL},
      fulfill_employee_delivery_load_exclude_wait_accept_order = #{fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder,jdbcType=DECIMAL},
      biz_time = #{bizTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into waima_delivery_load
    (logistics_unit_id, channel_id, channel_poi_id, scheduled_employee_delivery_load_include_unscheduled_order, 
      scheduled_employee_delivery_load_exclude_unscheduled_order, attendance_delivery_load, 
      fulfill_employee_delivery_load_include_wait_accept_order, fulfill_employee_delivery_load_exclude_wait_accept_order, 
      biz_time, create_time, update_time, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.logisticsUnitId,jdbcType=BIGINT}, #{item.channelId,jdbcType=INTEGER}, #{item.channelPoiId,jdbcType=VARCHAR}, 
        #{item.scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder,jdbcType=DECIMAL}, 
        #{item.scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder,jdbcType=DECIMAL}, 
        #{item.attendanceDeliveryLoad,jdbcType=DECIMAL}, #{item.fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder,jdbcType=DECIMAL}, 
        #{item.fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder,jdbcType=DECIMAL}, #{item.bizTime,jdbcType=TIMESTAMP}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=TINYINT}
        )
    </foreach>
  </insert>
</mapper>