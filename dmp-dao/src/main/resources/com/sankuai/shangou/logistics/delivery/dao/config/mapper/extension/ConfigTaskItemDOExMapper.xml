<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension.ConfigTaskItemDOExMapper">

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into config_task_item
            (task_id, store_id, channel_type, channel_store_id, `status`, ext_info, finished_at )
        values
        <foreach collection="recordList" item="item" index="index" separator=",">
           (#{item.taskId,jdbcType=BIGINT},
            #{item.storeId,jdbcType=BIGINT},
            #{item.channelType,jdbcType=INTEGER},
            #{item.channelStoreId,jdbcType=VARCHAR},
            #{item.status,jdbcType=INTEGER},
            #{item.extInfo,jdbcType=VARCHAR},
            #{item.finishedAt,jdbcType=TIMESTAMP} )
        </foreach>
    </insert>
    <update id="batchUpdate">
        <foreach collection="recordList" item="item" index="index" separator=";">
            update config_task_item
            set
                `status` = #{item.status,jdbcType=INTEGER},
                ext_info = #{item.extInfo,jdbcType=VARCHAR},
                finished_at = #{item.finishedAt,jdbcType=TIMESTAMP}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>