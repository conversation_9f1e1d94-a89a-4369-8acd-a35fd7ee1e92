<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension.ConfigTemplateItemDOExMapper">

    <select id="queryTemplateTypeByTemplateId" resultType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDO">
        select id, config_template_id, template_type from config_template_item where config_template_id in
        <foreach collection="templateIdList" item="templateId" open="(" separator="," close=")"/>
    </select>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into config_template_item
            (config_template_id, template_type, config_content, create_by)
        values
        <foreach collection="recordList" item="item" index="index" separator=",">
          ( #{item.configTemplateId,jdbcType=BIGINT},
            #{item.templateType,jdbcType=INTEGER},
            #{item.configContent,jdbcType=CHAR},
            #{item.createBy,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>