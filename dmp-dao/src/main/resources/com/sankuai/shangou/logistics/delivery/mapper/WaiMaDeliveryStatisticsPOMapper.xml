<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.mapper.WaiMaDeliveryStatisticsPOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryStatisticsPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="logistics_unit_id" jdbcType="BIGINT" property="logisticsUnitId" />
    <result column="channel_id" jdbcType="INTEGER" property="channelId" />
    <result column="channel_poi_id" jdbcType="VARCHAR" property="channelPoiId" />
    <result column="nine_percentile_fulfillment_duration" jdbcType="INTEGER" property="ninePercentileFulfillmentDuration" />
    <result column="eight_point_five_percentile_fulfillment_duration" jdbcType="INTEGER" property="eightPointFivePercentileFulfillmentDuration" />
    <result column="eight_percentile_fulfillment_duration" jdbcType="INTEGER" property="eightPercentileFulfillmentDuration" />
    <result column="self_delivery_order_count" jdbcType="INTEGER" property="selfDeliveryOrderCount" />
    <result column="biz_time" jdbcType="TIMESTAMP" property="bizTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, logistics_unit_id, channel_id, channel_poi_id, nine_percentile_fulfillment_duration, 
    eight_point_five_percentile_fulfillment_duration, eight_percentile_fulfillment_duration, 
    self_delivery_order_count, biz_time, create_time, update_time, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryStatisticsPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from waima_delivery_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from waima_delivery_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from waima_delivery_statistics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryStatisticsPOExample">
    delete from waima_delivery_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryStatisticsPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into waima_delivery_statistics (logistics_unit_id, channel_id, channel_poi_id, 
      nine_percentile_fulfillment_duration, eight_point_five_percentile_fulfillment_duration, 
      eight_percentile_fulfillment_duration, self_delivery_order_count, 
      biz_time, create_time, update_time, 
      is_deleted)
    values (#{logisticsUnitId,jdbcType=BIGINT}, #{channelId,jdbcType=INTEGER}, #{channelPoiId,jdbcType=VARCHAR}, 
      #{ninePercentileFulfillmentDuration,jdbcType=INTEGER}, #{eightPointFivePercentileFulfillmentDuration,jdbcType=INTEGER}, 
      #{eightPercentileFulfillmentDuration,jdbcType=INTEGER}, #{selfDeliveryOrderCount,jdbcType=INTEGER}, 
      #{bizTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryStatisticsPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into waima_delivery_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="logisticsUnitId != null">
        logistics_unit_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="channelPoiId != null">
        channel_poi_id,
      </if>
      <if test="ninePercentileFulfillmentDuration != null">
        nine_percentile_fulfillment_duration,
      </if>
      <if test="eightPointFivePercentileFulfillmentDuration != null">
        eight_point_five_percentile_fulfillment_duration,
      </if>
      <if test="eightPercentileFulfillmentDuration != null">
        eight_percentile_fulfillment_duration,
      </if>
      <if test="selfDeliveryOrderCount != null">
        self_delivery_order_count,
      </if>
      <if test="bizTime != null">
        biz_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="logisticsUnitId != null">
        #{logisticsUnitId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="channelPoiId != null">
        #{channelPoiId,jdbcType=VARCHAR},
      </if>
      <if test="ninePercentileFulfillmentDuration != null">
        #{ninePercentileFulfillmentDuration,jdbcType=INTEGER},
      </if>
      <if test="eightPointFivePercentileFulfillmentDuration != null">
        #{eightPointFivePercentileFulfillmentDuration,jdbcType=INTEGER},
      </if>
      <if test="eightPercentileFulfillmentDuration != null">
        #{eightPercentileFulfillmentDuration,jdbcType=INTEGER},
      </if>
      <if test="selfDeliveryOrderCount != null">
        #{selfDeliveryOrderCount,jdbcType=INTEGER},
      </if>
      <if test="bizTime != null">
        #{bizTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryStatisticsPOExample" resultType="java.lang.Long">
    select count(*) from waima_delivery_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update waima_delivery_statistics
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.logisticsUnitId != null">
        logistics_unit_id = #{record.logisticsUnitId,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=INTEGER},
      </if>
      <if test="record.channelPoiId != null">
        channel_poi_id = #{record.channelPoiId,jdbcType=VARCHAR},
      </if>
      <if test="record.ninePercentileFulfillmentDuration != null">
        nine_percentile_fulfillment_duration = #{record.ninePercentileFulfillmentDuration,jdbcType=INTEGER},
      </if>
      <if test="record.eightPointFivePercentileFulfillmentDuration != null">
        eight_point_five_percentile_fulfillment_duration = #{record.eightPointFivePercentileFulfillmentDuration,jdbcType=INTEGER},
      </if>
      <if test="record.eightPercentileFulfillmentDuration != null">
        eight_percentile_fulfillment_duration = #{record.eightPercentileFulfillmentDuration,jdbcType=INTEGER},
      </if>
      <if test="record.selfDeliveryOrderCount != null">
        self_delivery_order_count = #{record.selfDeliveryOrderCount,jdbcType=INTEGER},
      </if>
      <if test="record.bizTime != null">
        biz_time = #{record.bizTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update waima_delivery_statistics
    set id = #{record.id,jdbcType=BIGINT},
      logistics_unit_id = #{record.logisticsUnitId,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=INTEGER},
      channel_poi_id = #{record.channelPoiId,jdbcType=VARCHAR},
      nine_percentile_fulfillment_duration = #{record.ninePercentileFulfillmentDuration,jdbcType=INTEGER},
      eight_point_five_percentile_fulfillment_duration = #{record.eightPointFivePercentileFulfillmentDuration,jdbcType=INTEGER},
      eight_percentile_fulfillment_duration = #{record.eightPercentileFulfillmentDuration,jdbcType=INTEGER},
      self_delivery_order_count = #{record.selfDeliveryOrderCount,jdbcType=INTEGER},
      biz_time = #{record.bizTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryStatisticsPO">
    update waima_delivery_statistics
    <set>
      <if test="logisticsUnitId != null">
        logistics_unit_id = #{logisticsUnitId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=INTEGER},
      </if>
      <if test="channelPoiId != null">
        channel_poi_id = #{channelPoiId,jdbcType=VARCHAR},
      </if>
      <if test="ninePercentileFulfillmentDuration != null">
        nine_percentile_fulfillment_duration = #{ninePercentileFulfillmentDuration,jdbcType=INTEGER},
      </if>
      <if test="eightPointFivePercentileFulfillmentDuration != null">
        eight_point_five_percentile_fulfillment_duration = #{eightPointFivePercentileFulfillmentDuration,jdbcType=INTEGER},
      </if>
      <if test="eightPercentileFulfillmentDuration != null">
        eight_percentile_fulfillment_duration = #{eightPercentileFulfillmentDuration,jdbcType=INTEGER},
      </if>
      <if test="selfDeliveryOrderCount != null">
        self_delivery_order_count = #{selfDeliveryOrderCount,jdbcType=INTEGER},
      </if>
      <if test="bizTime != null">
        biz_time = #{bizTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryStatisticsPO">
    update waima_delivery_statistics
    set logistics_unit_id = #{logisticsUnitId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=INTEGER},
      channel_poi_id = #{channelPoiId,jdbcType=VARCHAR},
      nine_percentile_fulfillment_duration = #{ninePercentileFulfillmentDuration,jdbcType=INTEGER},
      eight_point_five_percentile_fulfillment_duration = #{eightPointFivePercentileFulfillmentDuration,jdbcType=INTEGER},
      eight_percentile_fulfillment_duration = #{eightPercentileFulfillmentDuration,jdbcType=INTEGER},
      self_delivery_order_count = #{selfDeliveryOrderCount,jdbcType=INTEGER},
      biz_time = #{bizTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into waima_delivery_statistics
    (logistics_unit_id, channel_id, channel_poi_id, nine_percentile_fulfillment_duration, 
      eight_point_five_percentile_fulfillment_duration, eight_percentile_fulfillment_duration, 
      self_delivery_order_count, biz_time, create_time, update_time, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.logisticsUnitId,jdbcType=BIGINT}, #{item.channelId,jdbcType=INTEGER}, #{item.channelPoiId,jdbcType=VARCHAR}, 
        #{item.ninePercentileFulfillmentDuration,jdbcType=INTEGER}, #{item.eightPointFivePercentileFulfillmentDuration,jdbcType=INTEGER}, 
        #{item.eightPercentileFulfillmentDuration,jdbcType=INTEGER}, #{item.selfDeliveryOrderCount,jdbcType=INTEGER}, 
        #{item.bizTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=TINYINT})
    </foreach>
  </insert>
</mapper>