<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.mapper.GrayConfigDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.delivery.model.GrayConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gray_key" jdbcType="VARCHAR" property="grayKey" />
    <result column="store_operation_mode" jdbcType="INTEGER" property="storeOperationMode" />
    <result column="gray_city_list" jdbcType="CHAR" property="grayCityList" />
    <result column="gray_store_list" jdbcType="CHAR" property="grayStoreList" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, gray_key, store_operation_mode, gray_city_list, gray_store_list, tenant_id
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.GrayConfigDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gray_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gray_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from gray_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.GrayConfigDOExample">
    delete from gray_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.logistics.delivery.model.GrayConfigDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gray_config (gray_key, store_operation_mode, gray_city_list, 
      gray_store_list, tenant_id)
    values (#{grayKey,jdbcType=VARCHAR}, #{storeOperationMode,jdbcType=INTEGER}, #{grayCityList,jdbcType=CHAR}, 
      #{grayStoreList,jdbcType=CHAR}, #{tenantId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.logistics.delivery.model.GrayConfigDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gray_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="grayKey != null">
        gray_key,
      </if>
      <if test="storeOperationMode != null">
        store_operation_mode,
      </if>
      <if test="grayCityList != null">
        gray_city_list,
      </if>
      <if test="grayStoreList != null">
        gray_store_list,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="grayKey != null">
        #{grayKey,jdbcType=VARCHAR},
      </if>
      <if test="storeOperationMode != null">
        #{storeOperationMode,jdbcType=INTEGER},
      </if>
      <if test="grayCityList != null">
        #{grayCityList,jdbcType=CHAR},
      </if>
      <if test="grayStoreList != null">
        #{grayStoreList,jdbcType=CHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.GrayConfigDOExample" resultType="java.lang.Long">
    select count(*) from gray_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gray_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.grayKey != null">
        gray_key = #{record.grayKey,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOperationMode != null">
        store_operation_mode = #{record.storeOperationMode,jdbcType=INTEGER},
      </if>
      <if test="record.grayCityList != null">
        gray_city_list = #{record.grayCityList,jdbcType=CHAR},
      </if>
      <if test="record.grayStoreList != null">
        gray_store_list = #{record.grayStoreList,jdbcType=CHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gray_config
    set id = #{record.id,jdbcType=BIGINT},
      gray_key = #{record.grayKey,jdbcType=VARCHAR},
      store_operation_mode = #{record.storeOperationMode,jdbcType=INTEGER},
      gray_city_list = #{record.grayCityList,jdbcType=CHAR},
      gray_store_list = #{record.grayStoreList,jdbcType=CHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.delivery.model.GrayConfigDO">
    update gray_config
    <set>
      <if test="grayKey != null">
        gray_key = #{grayKey,jdbcType=VARCHAR},
      </if>
      <if test="storeOperationMode != null">
        store_operation_mode = #{storeOperationMode,jdbcType=INTEGER},
      </if>
      <if test="grayCityList != null">
        gray_city_list = #{grayCityList,jdbcType=CHAR},
      </if>
      <if test="grayStoreList != null">
        gray_store_list = #{grayStoreList,jdbcType=CHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.delivery.model.GrayConfigDO">
    update gray_config
    set gray_key = #{grayKey,jdbcType=VARCHAR},
      store_operation_mode = #{storeOperationMode,jdbcType=INTEGER},
      gray_city_list = #{grayCityList,jdbcType=CHAR},
      gray_store_list = #{grayStoreList,jdbcType=CHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into gray_config
    (gray_key, store_operation_mode, gray_city_list, gray_store_list, tenant_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.grayKey,jdbcType=VARCHAR}, #{item.storeOperationMode,jdbcType=INTEGER}, #{item.grayCityList,jdbcType=CHAR}, 
        #{item.grayStoreList,jdbcType=CHAR}, #{item.tenantId,jdbcType=BIGINT})
    </foreach>
  </insert>
</mapper>