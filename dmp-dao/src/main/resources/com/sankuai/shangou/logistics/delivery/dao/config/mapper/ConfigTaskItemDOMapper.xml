<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.dao.config.mapper.ConfigTaskItemDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="channel_type" jdbcType="INTEGER" property="channelType" />
    <result column="channel_store_id" jdbcType="VARCHAR" property="channelStoreId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="finished_at" jdbcType="TIMESTAMP" property="finishedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, store_id, channel_type, channel_store_id, `status`, ext_info, created_at, 
    updated_at, finished_at
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from config_task_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from config_task_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from config_task_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDOExample">
    delete from config_task_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDO" useGeneratedKeys="true">
    insert into config_task_item (task_id, store_id, channel_type, 
      channel_store_id, `status`, ext_info, 
      created_at, updated_at, finished_at
      )
    values (#{taskId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{channelType,jdbcType=INTEGER}, 
      #{channelStoreId,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{extInfo,jdbcType=VARCHAR}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{finishedAt,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDO" useGeneratedKeys="true">
    insert into config_task_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="channelType != null">
        channel_type,
      </if>
      <if test="channelStoreId != null">
        channel_store_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="finishedAt != null">
        finished_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=INTEGER},
      </if>
      <if test="channelStoreId != null">
        #{channelStoreId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="finishedAt != null">
        #{finishedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDOExample" resultType="java.lang.Long">
    select count(*) from config_task_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update config_task_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.channelType != null">
        channel_type = #{record.channelType,jdbcType=INTEGER},
      </if>
      <if test="record.channelStoreId != null">
        channel_store_id = #{record.channelStoreId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.finishedAt != null">
        finished_at = #{record.finishedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update config_task_item
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      channel_type = #{record.channelType,jdbcType=INTEGER},
      channel_store_id = #{record.channelStoreId,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      finished_at = #{record.finishedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDO">
    update config_task_item
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=INTEGER},
      </if>
      <if test="channelStoreId != null">
        channel_store_id = #{channelStoreId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="finishedAt != null">
        finished_at = #{finishedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDO">
    update config_task_item
    set task_id = #{taskId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      channel_type = #{channelType,jdbcType=INTEGER},
      channel_store_id = #{channelStoreId,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      finished_at = #{finishedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>