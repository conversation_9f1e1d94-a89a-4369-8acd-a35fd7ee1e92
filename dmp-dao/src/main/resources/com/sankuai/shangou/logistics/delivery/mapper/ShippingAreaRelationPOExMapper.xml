<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.mapper.ShippingAreaRelationPOExMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.delivery.model.ShippingAreaRelationPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="area_id" jdbcType="BIGINT" property="areaId" />
    <result column="relation_id" jdbcType="BIGINT" property="relationId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="channel_id" jdbcType="INTEGER" property="channelId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  <select id="queryInitializedStoreIds" resultType="java.lang.Long">
    select store_id from shipping_area_rel
    where tenant_id = #{tenantId,jdbcType=BIGINT}
    and status = 1
    group by store_id;
  </select>
</mapper>