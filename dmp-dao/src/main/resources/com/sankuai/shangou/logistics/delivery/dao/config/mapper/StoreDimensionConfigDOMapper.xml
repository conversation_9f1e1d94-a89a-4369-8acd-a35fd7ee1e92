<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.dao.config.mapper.StoreDimensionConfigDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreDimensionConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="self_delivery_mode" jdbcType="TINYINT" property="selfDeliveryMode" />
    <result column="internal_navigation_mode" jdbcType="TINYINT" property="internalNavigationMode" />
    <result column="assess_time_config" jdbcType="CHAR" property="assessTimeConfig" />
    <result column="delivery_complete_mode" jdbcType="CHAR" property="deliveryCompleteMode" />
    <result column="rider_trans_roles" jdbcType="VARCHAR" property="riderTransRoles" />
    <result column="completed_sort_mode" jdbcType="TINYINT" property="completedSortMode" />
    <result column="delivery_remind_config" jdbcType="CHAR" property="deliveryRemindConfig" />
    <result column="enable" jdbcType="TINYINT" property="enable" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, store_id, self_delivery_mode, internal_navigation_mode, assess_time_config, 
    delivery_complete_mode, rider_trans_roles, completed_sort_mode, delivery_remind_config, 
    `enable`, created_at, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreDimensionConfigDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from store_dimension_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from store_dimension_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from store_dimension_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreDimensionConfigDOExample">
    delete from store_dimension_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreDimensionConfigDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into store_dimension_config (tenant_id, store_id, self_delivery_mode, 
      internal_navigation_mode, assess_time_config, 
      delivery_complete_mode, rider_trans_roles, completed_sort_mode, 
      delivery_remind_config, `enable`, created_at, 
      updated_at)
    values (#{tenantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{selfDeliveryMode,jdbcType=TINYINT}, 
      #{internalNavigationMode,jdbcType=TINYINT}, #{assessTimeConfig,jdbcType=CHAR}, 
      #{deliveryCompleteMode,jdbcType=CHAR}, #{riderTransRoles,jdbcType=VARCHAR}, #{completedSortMode,jdbcType=TINYINT}, 
      #{deliveryRemindConfig,jdbcType=CHAR}, #{enable,jdbcType=TINYINT}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreDimensionConfigDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into store_dimension_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="selfDeliveryMode != null">
        self_delivery_mode,
      </if>
      <if test="internalNavigationMode != null">
        internal_navigation_mode,
      </if>
      <if test="assessTimeConfig != null">
        assess_time_config,
      </if>
      <if test="deliveryCompleteMode != null">
        delivery_complete_mode,
      </if>
      <if test="riderTransRoles != null">
        rider_trans_roles,
      </if>
      <if test="completedSortMode != null">
        completed_sort_mode,
      </if>
      <if test="deliveryRemindConfig != null">
        delivery_remind_config,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="selfDeliveryMode != null">
        #{selfDeliveryMode,jdbcType=TINYINT},
      </if>
      <if test="internalNavigationMode != null">
        #{internalNavigationMode,jdbcType=TINYINT},
      </if>
      <if test="assessTimeConfig != null">
        #{assessTimeConfig,jdbcType=CHAR},
      </if>
      <if test="deliveryCompleteMode != null">
        #{deliveryCompleteMode,jdbcType=CHAR},
      </if>
      <if test="riderTransRoles != null">
        #{riderTransRoles,jdbcType=VARCHAR},
      </if>
      <if test="completedSortMode != null">
        #{completedSortMode,jdbcType=TINYINT},
      </if>
      <if test="deliveryRemindConfig != null">
        #{deliveryRemindConfig,jdbcType=CHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreDimensionConfigDOExample" resultType="java.lang.Long">
    select count(*) from store_dimension_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update store_dimension_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.selfDeliveryMode != null">
        self_delivery_mode = #{record.selfDeliveryMode,jdbcType=TINYINT},
      </if>
      <if test="record.internalNavigationMode != null">
        internal_navigation_mode = #{record.internalNavigationMode,jdbcType=TINYINT},
      </if>
      <if test="record.assessTimeConfig != null">
        assess_time_config = #{record.assessTimeConfig,jdbcType=CHAR},
      </if>
      <if test="record.deliveryCompleteMode != null">
        delivery_complete_mode = #{record.deliveryCompleteMode,jdbcType=CHAR},
      </if>
      <if test="record.riderTransRoles != null">
        rider_trans_roles = #{record.riderTransRoles,jdbcType=VARCHAR},
      </if>
      <if test="record.completedSortMode != null">
        completed_sort_mode = #{record.completedSortMode,jdbcType=TINYINT},
      </if>
      <if test="record.deliveryRemindConfig != null">
        delivery_remind_config = #{record.deliveryRemindConfig,jdbcType=CHAR},
      </if>
      <if test="record.enable != null">
        `enable` = #{record.enable,jdbcType=TINYINT},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update store_dimension_config
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      self_delivery_mode = #{record.selfDeliveryMode,jdbcType=TINYINT},
      internal_navigation_mode = #{record.internalNavigationMode,jdbcType=TINYINT},
      assess_time_config = #{record.assessTimeConfig,jdbcType=CHAR},
      delivery_complete_mode = #{record.deliveryCompleteMode,jdbcType=CHAR},
      rider_trans_roles = #{record.riderTransRoles,jdbcType=VARCHAR},
      completed_sort_mode = #{record.completedSortMode,jdbcType=TINYINT},
      delivery_remind_config = #{record.deliveryRemindConfig,jdbcType=CHAR},
      `enable` = #{record.enable,jdbcType=TINYINT},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreDimensionConfigDO">
    update store_dimension_config
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="selfDeliveryMode != null">
        self_delivery_mode = #{selfDeliveryMode,jdbcType=TINYINT},
      </if>
      <if test="internalNavigationMode != null">
        internal_navigation_mode = #{internalNavigationMode,jdbcType=TINYINT},
      </if>
      <if test="assessTimeConfig != null">
        assess_time_config = #{assessTimeConfig,jdbcType=CHAR},
      </if>
      <if test="deliveryCompleteMode != null">
        delivery_complete_mode = #{deliveryCompleteMode,jdbcType=CHAR},
      </if>
      <if test="riderTransRoles != null">
        rider_trans_roles = #{riderTransRoles,jdbcType=VARCHAR},
      </if>
      <if test="completedSortMode != null">
        completed_sort_mode = #{completedSortMode,jdbcType=TINYINT},
      </if>
      <if test="deliveryRemindConfig != null">
        delivery_remind_config = #{deliveryRemindConfig,jdbcType=CHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreDimensionConfigDO">
    update store_dimension_config
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      self_delivery_mode = #{selfDeliveryMode,jdbcType=TINYINT},
      internal_navigation_mode = #{internalNavigationMode,jdbcType=TINYINT},
      assess_time_config = #{assessTimeConfig,jdbcType=CHAR},
      delivery_complete_mode = #{deliveryCompleteMode,jdbcType=CHAR},
      rider_trans_roles = #{riderTransRoles,jdbcType=VARCHAR},
      completed_sort_mode = #{completedSortMode,jdbcType=TINYINT},
      delivery_remind_config = #{deliveryRemindConfig,jdbcType=CHAR},
      `enable` = #{enable,jdbcType=TINYINT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into store_dimension_config
    (tenant_id, store_id, self_delivery_mode, internal_navigation_mode, assess_time_config, 
      delivery_complete_mode, rider_trans_roles, completed_sort_mode, delivery_remind_config, 
      `enable`, created_at, updated_at)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.tenantId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.selfDeliveryMode,jdbcType=TINYINT}, 
        #{item.internalNavigationMode,jdbcType=TINYINT}, #{item.assessTimeConfig,jdbcType=CHAR}, 
        #{item.deliveryCompleteMode,jdbcType=CHAR}, #{item.riderTransRoles,jdbcType=VARCHAR}, 
        #{item.completedSortMode,jdbcType=TINYINT}, #{item.deliveryRemindConfig,jdbcType=CHAR}, 
        #{item.enable,jdbcType=TINYINT}, #{item.createdAt,jdbcType=TIMESTAMP}, #{item.updatedAt,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>

  <update id="batchUpdateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreDimensionConfigDO">
    <foreach collection="list" item="item" separator=";">
      update store_dimension_config
      set
      self_delivery_mode = #{item.selfDeliveryMode,jdbcType=TINYINT},
      internal_navigation_mode = #{item.internalNavigationMode,jdbcType=TINYINT},
      assess_time_config = #{item.assessTimeConfig,jdbcType=CHAR},
      delivery_complete_mode = #{item.deliveryCompleteMode,jdbcType=CHAR},
      rider_trans_roles = #{item.riderTransRoles,jdbcType=VARCHAR},
      completed_sort_mode = #{item.completedSortMode,jdbcType=TINYINT},
      delivery_remind_config = #{item.deliveryRemindConfig,jdbcType=CHAR},
      `enable` = #{item.enable,jdbcType=TINYINT},
      updated_at = #{item.updatedAt,jdbcType=TIMESTAMP}
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>