<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.mapper.DeliveryQuestionnaireDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="delivery_order_id" jdbcType="BIGINT" property="deliveryOrderId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="city_id" jdbcType="INTEGER" property="cityId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="rider_account_id" jdbcType="BIGINT" property="riderAccountId" />
    <result column="question" jdbcType="VARCHAR" property="question" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="scene" jdbcType="TINYINT" property="scene" />
    <result column="is_valid" jdbcType="TINYINT" property="isValid" />
    <result column="release_time" jdbcType="TIMESTAMP" property="releaseTime" />
    <result column="fill_in_time" jdbcType="TIMESTAMP" property="fillInTime" />
    <result column="answer_options_snapshot" jdbcType="VARCHAR" property="answerOptionsSnapshot" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, delivery_order_id, order_id, city_id, store_id, rider_account_id, question, answer, 
    create_time, update_time, scene, is_valid, release_time, fill_in_time, answer_options_snapshot
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from delivery_questionnaire
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from delivery_questionnaire
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from delivery_questionnaire
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDOExample">
    delete from delivery_questionnaire
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into delivery_questionnaire (delivery_order_id, order_id, city_id, 
      store_id, rider_account_id, question, 
      answer, create_time, update_time, 
      scene, is_valid, release_time, 
      fill_in_time, answer_options_snapshot)
    values (#{deliveryOrderId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{cityId,jdbcType=INTEGER}, 
      #{storeId,jdbcType=BIGINT}, #{riderAccountId,jdbcType=BIGINT}, #{question,jdbcType=VARCHAR}, 
      #{answer,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{scene,jdbcType=TINYINT}, #{isValid,jdbcType=TINYINT}, #{releaseTime,jdbcType=TIMESTAMP}, 
      #{fillInTime,jdbcType=TIMESTAMP}, #{answerOptionsSnapshot,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into delivery_questionnaire
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deliveryOrderId != null">
        delivery_order_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="riderAccountId != null">
        rider_account_id,
      </if>
      <if test="question != null">
        question,
      </if>
      <if test="answer != null">
        answer,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="scene != null">
        scene,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="releaseTime != null">
        release_time,
      </if>
      <if test="fillInTime != null">
        fill_in_time,
      </if>
      <if test="answerOptionsSnapshot != null">
        answer_options_snapshot,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deliveryOrderId != null">
        #{deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=INTEGER},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="riderAccountId != null">
        #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="question != null">
        #{question,jdbcType=VARCHAR},
      </if>
      <if test="answer != null">
        #{answer,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scene != null">
        #{scene,jdbcType=TINYINT},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=TINYINT},
      </if>
      <if test="releaseTime != null">
        #{releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fillInTime != null">
        #{fillInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="answerOptionsSnapshot != null">
        #{answerOptionsSnapshot,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDOExample" resultType="java.lang.Long">
    select count(*) from delivery_questionnaire
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update delivery_questionnaire
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.deliveryOrderId != null">
        delivery_order_id = #{record.deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.cityId != null">
        city_id = #{record.cityId,jdbcType=INTEGER},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.riderAccountId != null">
        rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.question != null">
        question = #{record.question,jdbcType=VARCHAR},
      </if>
      <if test="record.answer != null">
        answer = #{record.answer,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.scene != null">
        scene = #{record.scene,jdbcType=TINYINT},
      </if>
      <if test="record.isValid != null">
        is_valid = #{record.isValid,jdbcType=TINYINT},
      </if>
      <if test="record.releaseTime != null">
        release_time = #{record.releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.fillInTime != null">
        fill_in_time = #{record.fillInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.answerOptionsSnapshot != null">
        answer_options_snapshot = #{record.answerOptionsSnapshot,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update delivery_questionnaire
    set id = #{record.id,jdbcType=BIGINT},
      delivery_order_id = #{record.deliveryOrderId,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      city_id = #{record.cityId,jdbcType=INTEGER},
      store_id = #{record.storeId,jdbcType=BIGINT},
      rider_account_id = #{record.riderAccountId,jdbcType=BIGINT},
      question = #{record.question,jdbcType=VARCHAR},
      answer = #{record.answer,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      scene = #{record.scene,jdbcType=TINYINT},
      is_valid = #{record.isValid,jdbcType=TINYINT},
      release_time = #{record.releaseTime,jdbcType=TIMESTAMP},
      fill_in_time = #{record.fillInTime,jdbcType=TIMESTAMP},
      answer_options_snapshot = #{record.answerOptionsSnapshot,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDO">
    update delivery_questionnaire
    <set>
      <if test="deliveryOrderId != null">
        delivery_order_id = #{deliveryOrderId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=INTEGER},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="riderAccountId != null">
        rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      </if>
      <if test="question != null">
        question = #{question,jdbcType=VARCHAR},
      </if>
      <if test="answer != null">
        answer = #{answer,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scene != null">
        scene = #{scene,jdbcType=TINYINT},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="releaseTime != null">
        release_time = #{releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fillInTime != null">
        fill_in_time = #{fillInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="answerOptionsSnapshot != null">
        answer_options_snapshot = #{answerOptionsSnapshot,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDO">
    update delivery_questionnaire
    set delivery_order_id = #{deliveryOrderId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      city_id = #{cityId,jdbcType=INTEGER},
      store_id = #{storeId,jdbcType=BIGINT},
      rider_account_id = #{riderAccountId,jdbcType=BIGINT},
      question = #{question,jdbcType=VARCHAR},
      answer = #{answer,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      scene = #{scene,jdbcType=TINYINT},
      is_valid = #{isValid,jdbcType=TINYINT},
      release_time = #{releaseTime,jdbcType=TIMESTAMP},
      fill_in_time = #{fillInTime,jdbcType=TIMESTAMP},
      answer_options_snapshot = #{answerOptionsSnapshot,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into delivery_questionnaire
    (delivery_order_id, order_id, city_id, store_id, rider_account_id, question, answer, 
      create_time, update_time, scene, is_valid, release_time, fill_in_time, answer_options_snapshot
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.deliveryOrderId,jdbcType=BIGINT}, #{item.orderId,jdbcType=BIGINT}, #{item.cityId,jdbcType=INTEGER}, 
        #{item.storeId,jdbcType=BIGINT}, #{item.riderAccountId,jdbcType=BIGINT}, #{item.question,jdbcType=VARCHAR}, 
        #{item.answer,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.scene,jdbcType=TINYINT}, #{item.isValid,jdbcType=TINYINT}, #{item.releaseTime,jdbcType=TIMESTAMP}, 
        #{item.fillInTime,jdbcType=TIMESTAMP}, #{item.answerOptionsSnapshot,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
</mapper>