<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.dao.config.mapper.IStoreConfigDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="address" jdbcType="CHAR" property="address" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="delivery_strategy" jdbcType="TINYINT" property="deliveryStrategy" />
    <result column="delivery_strategy_config" jdbcType="CHAR" property="deliveryStrategyConfig" />
    <result column="delivery_launch_point" jdbcType="TINYINT" property="deliveryLaunchPoint" />
    <result column="delivery_launch_delay_minutes" jdbcType="INTEGER" property="deliveryLaunchDelayMinutes" />
    <result column="booking_order_delivery_launch_point" jdbcType="TINYINT" property="bookingOrderDeliveryLaunchPoint" />
    <result column="booking_order_delivery_launch_minutes" jdbcType="INTEGER" property="bookingOrderDeliveryLaunchMinutes" />
    <result column="order_platform_delivery_config" jdbcType="CHAR" property="orderPlatformDeliveryConfig" />
    <result column="enabled" jdbcType="TINYINT" property="enabled" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_auto_launch" jdbcType="TINYINT" property="isAutoLaunch" />
    <result column="delivery_launch_rule" jdbcType="TINYINT" property="deliveryLaunchRule" />
    <result column="open_aggr_platform" jdbcType="TINYINT" property="openAggrPlatform" />
    <result column="city_code" jdbcType="INTEGER" property="cityCode" />
    <result column="channel_type" jdbcType="INTEGER" property="channelType" />
    <result column="last_platform_type" jdbcType="INTEGER" property="lastPlatformType" />
    <result column="is_show_item_number" jdbcType="TINYINT" property="isShowItemNumber" />
    <result column="self_assess_delivery_config" jdbcType="CHAR" property="selfAssessDeliveryConfig" />
    <result column="second_delivery_platform" jdbcType="VARCHAR" property="secondDeliveryPlatform" />
    <result column="turn_second_delivery_platform_condition" jdbcType="VARCHAR" property="turnSecondDeliveryPlatformCondition" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Base_Column_List">
    id, tenant_id, store_id, address, contact_phone, delivery_strategy, delivery_strategy_config, 
    delivery_launch_point, delivery_launch_delay_minutes, booking_order_delivery_launch_point, 
    booking_order_delivery_launch_minutes, order_platform_delivery_config, enabled, create_time, 
    update_time, is_auto_launch, delivery_launch_rule, open_aggr_platform, city_code, 
    channel_type, last_platform_type, is_show_item_number, self_assess_delivery_config, 
    second_delivery_platform, turn_second_delivery_platform_condition
  </sql>
  <select id="selectByExampleWithLimit" parameterType="map" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from store_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limitNum != null">
      limit ${limitNum}
    </if>
  </select>
</mapper>