<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.dao.config.mapper.ConfigTaskDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="config_template_id" jdbcType="BIGINT" property="configTemplateId" />
    <result column="task_type" jdbcType="INTEGER" property="taskType" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="channel_type" jdbcType="VARCHAR" property="channelType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="config_detail" jdbcType="CHAR" property="configDetail" />
    <result column="success_num" jdbcType="INTEGER" property="successNum" />
    <result column="fail_num" jdbcType="INTEGER" property="failNum" />
    <result column="total_num" jdbcType="INTEGER" property="totalNum" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="finished_at" jdbcType="TIMESTAMP" property="finishedAt" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, config_template_id, task_type, task_name, channel_type, `status`, 
    config_detail, success_num, fail_num, total_num, ext_info, created_at, updated_at, 
    finished_at, `operator`, operator_name
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from config_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from config_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from config_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDOExample">
    delete from config_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDO" useGeneratedKeys="true">
    insert into config_task (tenant_id, config_template_id, task_type, 
      task_name, channel_type, `status`, 
      config_detail, success_num, fail_num, 
      total_num, ext_info, created_at, 
      updated_at, finished_at, `operator`, 
      operator_name)
    values (#{tenantId,jdbcType=BIGINT}, #{configTemplateId,jdbcType=BIGINT}, #{taskType,jdbcType=INTEGER}, 
      #{taskName,jdbcType=VARCHAR}, #{channelType,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{configDetail,jdbcType=CHAR}, #{successNum,jdbcType=INTEGER}, #{failNum,jdbcType=INTEGER},
      #{totalNum,jdbcType=INTEGER}, #{extInfo,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedAt,jdbcType=TIMESTAMP}, #{finishedAt,jdbcType=TIMESTAMP}, #{operator,jdbcType=VARCHAR}, 
      #{operatorName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDO" useGeneratedKeys="true">
    insert into config_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="configTemplateId != null">
        config_template_id,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="channelType != null">
        channel_type,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="configDetail != null">
        config_detail,
      </if>
      <if test="successNum != null">
        success_num,
      </if>
      <if test="failNum != null">
        fail_num,
      </if>
      <if test="totalNum != null">
        total_num,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="finishedAt != null">
        finished_at,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="configTemplateId != null">
        #{configTemplateId,jdbcType=BIGINT},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=INTEGER},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="configDetail != null">
        #{configDetail,jdbcType=CHAR},
      </if>
      <if test="successNum != null">
        #{successNum,jdbcType=INTEGER},
      </if>
      <if test="failNum != null">
        #{failNum,jdbcType=INTEGER},
      </if>
      <if test="totalNum != null">
        #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="finishedAt != null">
        #{finishedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDOExample" resultType="java.lang.Long">
    select count(*) from config_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update config_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.configTemplateId != null">
        config_template_id = #{record.configTemplateId,jdbcType=BIGINT},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=INTEGER},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.channelType != null">
        channel_type = #{record.channelType,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.configDetail != null">
        config_detail = #{record.configDetail,jdbcType=CHAR},
      </if>
      <if test="record.successNum != null">
        success_num = #{record.successNum,jdbcType=INTEGER},
      </if>
      <if test="record.failNum != null">
        fail_num = #{record.failNum,jdbcType=INTEGER},
      </if>
      <if test="record.totalNum != null">
        total_num = #{record.totalNum,jdbcType=INTEGER},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.finishedAt != null">
        finished_at = #{record.finishedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operator != null">
        `operator` = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorName != null">
        operator_name = #{record.operatorName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update config_task
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      config_template_id = #{record.configTemplateId,jdbcType=BIGINT},
      task_type = #{record.taskType,jdbcType=INTEGER},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      channel_type = #{record.channelType,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      config_detail = #{record.configDetail,jdbcType=CHAR},
      success_num = #{record.successNum,jdbcType=INTEGER},
      fail_num = #{record.failNum,jdbcType=INTEGER},
      total_num = #{record.totalNum,jdbcType=INTEGER},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      finished_at = #{record.finishedAt,jdbcType=TIMESTAMP},
      `operator` = #{record.operator,jdbcType=VARCHAR},
      operator_name = #{record.operatorName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDO">
    update config_task
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="configTemplateId != null">
        config_template_id = #{configTemplateId,jdbcType=BIGINT},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=INTEGER},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="configDetail != null">
        config_detail = #{configDetail,jdbcType=CHAR},
      </if>
      <if test="successNum != null">
        success_num = #{successNum,jdbcType=INTEGER},
      </if>
      <if test="failNum != null">
        fail_num = #{failNum,jdbcType=INTEGER},
      </if>
      <if test="totalNum != null">
        total_num = #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="finishedAt != null">
        finished_at = #{finishedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDO">
    update config_task
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      config_template_id = #{configTemplateId,jdbcType=BIGINT},
      task_type = #{taskType,jdbcType=INTEGER},
      task_name = #{taskName,jdbcType=VARCHAR},
      channel_type = #{channelType,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      config_detail = #{configDetail,jdbcType=CHAR},
      success_num = #{successNum,jdbcType=INTEGER},
      fail_num = #{failNum,jdbcType=INTEGER},
      total_num = #{totalNum,jdbcType=INTEGER},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      finished_at = #{finishedAt,jdbcType=TIMESTAMP},
      `operator` = #{operator,jdbcType=VARCHAR},
      operator_name = #{operatorName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>