<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.mapper.SelfDeliveryPoiConfigPOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="poi_id" jdbcType="BIGINT" property="poiId" />
    <result column="enable_turn_delivery" jdbcType="TINYINT" property="enableTurnDelivery" />
    <result column="enable_pick_delivery_split" jdbcType="TINYINT" property="enablePickDeliverySplit" />
    <result column="dap_sync_status" jdbcType="TINYINT" property="dapSyncStatus" />
    <result column="last_operator_name" jdbcType="VARCHAR" property="lastOperatorName" />
    <result column="last_operator_id" jdbcType="BIGINT" property="lastOperatorId" />
    <result column="last_operate_time" jdbcType="TIMESTAMP" property="lastOperateTime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, poi_id, enable_turn_delivery, enable_pick_delivery_split, dap_sync_status, 
    last_operator_name, last_operator_id, last_operate_time, is_deleted, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from self_delivery_poi_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from self_delivery_poi_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from self_delivery_poi_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPOExample">
    delete from self_delivery_poi_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into self_delivery_poi_config (tenant_id, poi_id, enable_turn_delivery, 
      enable_pick_delivery_split, dap_sync_status, 
      last_operator_name, last_operator_id, last_operate_time, 
      is_deleted, create_time, update_time
      )
    values (#{tenantId,jdbcType=BIGINT}, #{poiId,jdbcType=BIGINT}, #{enableTurnDelivery,jdbcType=TINYINT}, 
      #{enablePickDeliverySplit,jdbcType=TINYINT}, #{dapSyncStatus,jdbcType=TINYINT}, 
      #{lastOperatorName,jdbcType=VARCHAR}, #{lastOperatorId,jdbcType=BIGINT}, #{lastOperateTime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into self_delivery_poi_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="poiId != null">
        poi_id,
      </if>
      <if test="enableTurnDelivery != null">
        enable_turn_delivery,
      </if>
      <if test="enablePickDeliverySplit != null">
        enable_pick_delivery_split,
      </if>
      <if test="dapSyncStatus != null">
        dap_sync_status,
      </if>
      <if test="lastOperatorName != null">
        last_operator_name,
      </if>
      <if test="lastOperatorId != null">
        last_operator_id,
      </if>
      <if test="lastOperateTime != null">
        last_operate_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="poiId != null">
        #{poiId,jdbcType=BIGINT},
      </if>
      <if test="enableTurnDelivery != null">
        #{enableTurnDelivery,jdbcType=TINYINT},
      </if>
      <if test="enablePickDeliverySplit != null">
        #{enablePickDeliverySplit,jdbcType=TINYINT},
      </if>
      <if test="dapSyncStatus != null">
        #{dapSyncStatus,jdbcType=TINYINT},
      </if>
      <if test="lastOperatorName != null">
        #{lastOperatorName,jdbcType=VARCHAR},
      </if>
      <if test="lastOperatorId != null">
        #{lastOperatorId,jdbcType=BIGINT},
      </if>
      <if test="lastOperateTime != null">
        #{lastOperateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPOExample" resultType="java.lang.Long">
    select count(*) from self_delivery_poi_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update self_delivery_poi_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.poiId != null">
        poi_id = #{record.poiId,jdbcType=BIGINT},
      </if>
      <if test="record.enableTurnDelivery != null">
        enable_turn_delivery = #{record.enableTurnDelivery,jdbcType=TINYINT},
      </if>
      <if test="record.enablePickDeliverySplit != null">
        enable_pick_delivery_split = #{record.enablePickDeliverySplit,jdbcType=TINYINT},
      </if>
      <if test="record.dapSyncStatus != null">
        dap_sync_status = #{record.dapSyncStatus,jdbcType=TINYINT},
      </if>
      <if test="record.lastOperatorName != null">
        last_operator_name = #{record.lastOperatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.lastOperatorId != null">
        last_operator_id = #{record.lastOperatorId,jdbcType=BIGINT},
      </if>
      <if test="record.lastOperateTime != null">
        last_operate_time = #{record.lastOperateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update self_delivery_poi_config
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      poi_id = #{record.poiId,jdbcType=BIGINT},
      enable_turn_delivery = #{record.enableTurnDelivery,jdbcType=TINYINT},
      enable_pick_delivery_split = #{record.enablePickDeliverySplit,jdbcType=TINYINT},
      dap_sync_status = #{record.dapSyncStatus,jdbcType=TINYINT},
      last_operator_name = #{record.lastOperatorName,jdbcType=VARCHAR},
      last_operator_id = #{record.lastOperatorId,jdbcType=BIGINT},
      last_operate_time = #{record.lastOperateTime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPO">
    update self_delivery_poi_config
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="poiId != null">
        poi_id = #{poiId,jdbcType=BIGINT},
      </if>
      <if test="enableTurnDelivery != null">
        enable_turn_delivery = #{enableTurnDelivery,jdbcType=TINYINT},
      </if>
      <if test="enablePickDeliverySplit != null">
        enable_pick_delivery_split = #{enablePickDeliverySplit,jdbcType=TINYINT},
      </if>
      <if test="dapSyncStatus != null">
        dap_sync_status = #{dapSyncStatus,jdbcType=TINYINT},
      </if>
      <if test="lastOperatorName != null">
        last_operator_name = #{lastOperatorName,jdbcType=VARCHAR},
      </if>
      <if test="lastOperatorId != null">
        last_operator_id = #{lastOperatorId,jdbcType=BIGINT},
      </if>
      <if test="lastOperateTime != null">
        last_operate_time = #{lastOperateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPO">
    update self_delivery_poi_config
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      poi_id = #{poiId,jdbcType=BIGINT},
      enable_turn_delivery = #{enableTurnDelivery,jdbcType=TINYINT},
      enable_pick_delivery_split = #{enablePickDeliverySplit,jdbcType=TINYINT},
      dap_sync_status = #{dapSyncStatus,jdbcType=TINYINT},
      last_operator_name = #{lastOperatorName,jdbcType=VARCHAR},
      last_operator_id = #{lastOperatorId,jdbcType=BIGINT},
      last_operate_time = #{lastOperateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into self_delivery_poi_config
    (tenant_id, poi_id, enable_turn_delivery, enable_pick_delivery_split, dap_sync_status, 
      last_operator_name, last_operator_id, last_operate_time, is_deleted, create_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.tenantId,jdbcType=BIGINT}, #{item.poiId,jdbcType=BIGINT}, #{item.enableTurnDelivery,jdbcType=TINYINT}, 
        #{item.enablePickDeliverySplit,jdbcType=TINYINT}, #{item.dapSyncStatus,jdbcType=TINYINT}, 
        #{item.lastOperatorName,jdbcType=VARCHAR}, #{item.lastOperatorId,jdbcType=BIGINT}, 
        #{item.lastOperateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>