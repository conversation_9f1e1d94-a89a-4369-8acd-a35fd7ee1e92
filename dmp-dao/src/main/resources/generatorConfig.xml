<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="MybatisGenerator" targetRuntime="MyBatis3">
        <property name="autoDelimitKeywords" value="true"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"/>
        <plugin type="com.meituan.xframe.mybatis.generator.plugins.BatchInsertPlugin"/>
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="true"/>
            <property name="javaFileEncoding" value="UTF-8"/>
        </commentGenerator>

        <!--替换成自己数据库的jdbcRef -->
        <zebra jdbcRef="sgfulfilltms_delivery_test0_test"/>

        <javaTypeResolver>
            <property name="useJSR310Types" value="true"/>
        </javaTypeResolver>

        <!--load targetProject from properties-->
        <javaModelGenerator targetPackage="com.sankuai.shangou.logistics.delivery.dao.config.model"
                            targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- sql语句相关的xml或者注解的生成包路径 -->
        <sqlMapGenerator targetPackage="com.sankuai.shangou.logistics.delivery.dao.config.mapper"
                         targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!--load targetProject from properties-->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.shangou.logistics.delivery.dao.config.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!-- 示例 -->
<!--        <table tableName="delivery_order" domainObjectName="DeliveryOrderDO">-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            <columnOverride column="delivery_exception_type" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="cancel_mark" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="current_status_lock" javaType="java.lang.Integer"/>-->

<!--            <columnOverride column="receiver_address_coordinate_type" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="valid" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="order_source" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="delivery_exception_code" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="reserved" javaType="java.lang.Integer"/>-->

<!--        </table>-->

        <table tableName="store_config" domainObjectName="StoreConfigDO">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
            <columnOverride column="delivery_strategy" javaType="java.lang.Integer"/>
            <columnOverride column="delivery_launch_point" javaType="java.lang.Integer"/>
            <columnOverride column="booking_order_delivery_launch_point" javaType="java.lang.Integer"/>
            <columnOverride column="enabled" javaType="java.lang.Integer"/>
            <columnOverride column="is_auto_launch" javaType="java.lang.Integer"/>
            <columnOverride column="delivery_launch_rule" javaType="java.lang.Integer"/>
            <columnOverride column="open_aggr_platform" javaType="java.lang.Integer"/>
            <columnOverride column="is_show_item_number" javaType="java.lang.Integer"/>
        </table>

        <table tableName="store_dimension_config" domainObjectName="StoreDimensionConfigDO">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
            <columnOverride column="self_delivery_mode" javaType="java.lang.Integer"/>
            <columnOverride column="internal_navigation_mode" javaType="java.lang.Integer"/>
            <columnOverride column="completed_sort_mode" javaType="java.lang.Integer"/>
            <columnOverride column="enable" javaType="java.lang.Integer"/>
        </table>

    </context>
</generatorConfiguration>