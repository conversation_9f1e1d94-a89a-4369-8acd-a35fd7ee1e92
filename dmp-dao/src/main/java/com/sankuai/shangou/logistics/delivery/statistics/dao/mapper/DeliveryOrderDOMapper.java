package com.sankuai.shangou.logistics.delivery.statistics.dao.mapper;

import com.sankuai.shangou.logistics.delivery.statistics.dao.model.DeliveryOrderDO;
import com.sankuai.shangou.logistics.delivery.statistics.dao.model.DeliveryOrderDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeliveryOrderDOMapper {
    long countByExample(DeliveryOrderDOExample example);

    int deleteByExample(DeliveryOrderDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DeliveryOrderDO record);

    int insertSelective(DeliveryOrderDO record);

    List<DeliveryOrderDO> selectByExample(DeliveryOrderDOExample example);

    DeliveryOrderDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DeliveryOrderDO record, @Param("example") DeliveryOrderDOExample example);

    int updateByExample(@Param("record") DeliveryOrderDO record, @Param("example") DeliveryOrderDOExample example);

    int updateByPrimaryKeySelective(DeliveryOrderDO record);

    int updateByPrimaryKey(DeliveryOrderDO record);

    int batchInsert(@Param("list") List<DeliveryOrderDO> list);
}