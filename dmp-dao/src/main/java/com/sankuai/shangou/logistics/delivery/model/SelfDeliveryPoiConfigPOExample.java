package com.sankuai.shangou.logistics.delivery.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class SelfDeliveryPoiConfigPOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public SelfDeliveryPoiConfigPOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(Long value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(Long value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(Long value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(Long value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(Long value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<Long> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<Long> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(Long value1, Long value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(Long value1, Long value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPoiIdIsNull() {
            addCriterion("poi_id is null");
            return (Criteria) this;
        }

        public Criteria andPoiIdIsNotNull() {
            addCriterion("poi_id is not null");
            return (Criteria) this;
        }

        public Criteria andPoiIdEqualTo(Long value) {
            addCriterion("poi_id =", value, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdNotEqualTo(Long value) {
            addCriterion("poi_id <>", value, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdGreaterThan(Long value) {
            addCriterion("poi_id >", value, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdGreaterThanOrEqualTo(Long value) {
            addCriterion("poi_id >=", value, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdLessThan(Long value) {
            addCriterion("poi_id <", value, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdLessThanOrEqualTo(Long value) {
            addCriterion("poi_id <=", value, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdIn(List<Long> values) {
            addCriterion("poi_id in", values, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdNotIn(List<Long> values) {
            addCriterion("poi_id not in", values, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdBetween(Long value1, Long value2) {
            addCriterion("poi_id between", value1, value2, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdNotBetween(Long value1, Long value2) {
            addCriterion("poi_id not between", value1, value2, "poiId");
            return (Criteria) this;
        }

        public Criteria andEnableTurnDeliveryIsNull() {
            addCriterion("enable_turn_delivery is null");
            return (Criteria) this;
        }

        public Criteria andEnableTurnDeliveryIsNotNull() {
            addCriterion("enable_turn_delivery is not null");
            return (Criteria) this;
        }

        public Criteria andEnableTurnDeliveryEqualTo(Integer value) {
            addCriterion("enable_turn_delivery =", value, "enableTurnDelivery");
            return (Criteria) this;
        }

        public Criteria andEnableTurnDeliveryNotEqualTo(Integer value) {
            addCriterion("enable_turn_delivery <>", value, "enableTurnDelivery");
            return (Criteria) this;
        }

        public Criteria andEnableTurnDeliveryGreaterThan(Integer value) {
            addCriterion("enable_turn_delivery >", value, "enableTurnDelivery");
            return (Criteria) this;
        }

        public Criteria andEnableTurnDeliveryGreaterThanOrEqualTo(Integer value) {
            addCriterion("enable_turn_delivery >=", value, "enableTurnDelivery");
            return (Criteria) this;
        }

        public Criteria andEnableTurnDeliveryLessThan(Integer value) {
            addCriterion("enable_turn_delivery <", value, "enableTurnDelivery");
            return (Criteria) this;
        }

        public Criteria andEnableTurnDeliveryLessThanOrEqualTo(Integer value) {
            addCriterion("enable_turn_delivery <=", value, "enableTurnDelivery");
            return (Criteria) this;
        }

        public Criteria andEnableTurnDeliveryIn(List<Integer> values) {
            addCriterion("enable_turn_delivery in", values, "enableTurnDelivery");
            return (Criteria) this;
        }

        public Criteria andEnableTurnDeliveryNotIn(List<Integer> values) {
            addCriterion("enable_turn_delivery not in", values, "enableTurnDelivery");
            return (Criteria) this;
        }

        public Criteria andEnableTurnDeliveryBetween(Integer value1, Integer value2) {
            addCriterion("enable_turn_delivery between", value1, value2, "enableTurnDelivery");
            return (Criteria) this;
        }

        public Criteria andEnableTurnDeliveryNotBetween(Integer value1, Integer value2) {
            addCriterion("enable_turn_delivery not between", value1, value2, "enableTurnDelivery");
            return (Criteria) this;
        }

        public Criteria andEnablePickDeliverySplitIsNull() {
            addCriterion("enable_pick_delivery_split is null");
            return (Criteria) this;
        }

        public Criteria andEnablePickDeliverySplitIsNotNull() {
            addCriterion("enable_pick_delivery_split is not null");
            return (Criteria) this;
        }

        public Criteria andEnablePickDeliverySplitEqualTo(Integer value) {
            addCriterion("enable_pick_delivery_split =", value, "enablePickDeliverySplit");
            return (Criteria) this;
        }

        public Criteria andEnablePickDeliverySplitNotEqualTo(Integer value) {
            addCriterion("enable_pick_delivery_split <>", value, "enablePickDeliverySplit");
            return (Criteria) this;
        }

        public Criteria andEnablePickDeliverySplitGreaterThan(Integer value) {
            addCriterion("enable_pick_delivery_split >", value, "enablePickDeliverySplit");
            return (Criteria) this;
        }

        public Criteria andEnablePickDeliverySplitGreaterThanOrEqualTo(Integer value) {
            addCriterion("enable_pick_delivery_split >=", value, "enablePickDeliverySplit");
            return (Criteria) this;
        }

        public Criteria andEnablePickDeliverySplitLessThan(Integer value) {
            addCriterion("enable_pick_delivery_split <", value, "enablePickDeliverySplit");
            return (Criteria) this;
        }

        public Criteria andEnablePickDeliverySplitLessThanOrEqualTo(Integer value) {
            addCriterion("enable_pick_delivery_split <=", value, "enablePickDeliverySplit");
            return (Criteria) this;
        }

        public Criteria andEnablePickDeliverySplitIn(List<Integer> values) {
            addCriterion("enable_pick_delivery_split in", values, "enablePickDeliverySplit");
            return (Criteria) this;
        }

        public Criteria andEnablePickDeliverySplitNotIn(List<Integer> values) {
            addCriterion("enable_pick_delivery_split not in", values, "enablePickDeliverySplit");
            return (Criteria) this;
        }

        public Criteria andEnablePickDeliverySplitBetween(Integer value1, Integer value2) {
            addCriterion("enable_pick_delivery_split between", value1, value2, "enablePickDeliverySplit");
            return (Criteria) this;
        }

        public Criteria andEnablePickDeliverySplitNotBetween(Integer value1, Integer value2) {
            addCriterion("enable_pick_delivery_split not between", value1, value2, "enablePickDeliverySplit");
            return (Criteria) this;
        }

        public Criteria andDapSyncStatusIsNull() {
            addCriterion("dap_sync_status is null");
            return (Criteria) this;
        }

        public Criteria andDapSyncStatusIsNotNull() {
            addCriterion("dap_sync_status is not null");
            return (Criteria) this;
        }

        public Criteria andDapSyncStatusEqualTo(Integer value) {
            addCriterion("dap_sync_status =", value, "dapSyncStatus");
            return (Criteria) this;
        }

        public Criteria andDapSyncStatusNotEqualTo(Integer value) {
            addCriterion("dap_sync_status <>", value, "dapSyncStatus");
            return (Criteria) this;
        }

        public Criteria andDapSyncStatusGreaterThan(Integer value) {
            addCriterion("dap_sync_status >", value, "dapSyncStatus");
            return (Criteria) this;
        }

        public Criteria andDapSyncStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("dap_sync_status >=", value, "dapSyncStatus");
            return (Criteria) this;
        }

        public Criteria andDapSyncStatusLessThan(Integer value) {
            addCriterion("dap_sync_status <", value, "dapSyncStatus");
            return (Criteria) this;
        }

        public Criteria andDapSyncStatusLessThanOrEqualTo(Integer value) {
            addCriterion("dap_sync_status <=", value, "dapSyncStatus");
            return (Criteria) this;
        }

        public Criteria andDapSyncStatusIn(List<Integer> values) {
            addCriterion("dap_sync_status in", values, "dapSyncStatus");
            return (Criteria) this;
        }

        public Criteria andDapSyncStatusNotIn(List<Integer> values) {
            addCriterion("dap_sync_status not in", values, "dapSyncStatus");
            return (Criteria) this;
        }

        public Criteria andDapSyncStatusBetween(Integer value1, Integer value2) {
            addCriterion("dap_sync_status between", value1, value2, "dapSyncStatus");
            return (Criteria) this;
        }

        public Criteria andDapSyncStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("dap_sync_status not between", value1, value2, "dapSyncStatus");
            return (Criteria) this;
        }

        public Criteria andLastOperatorNameIsNull() {
            addCriterion("last_operator_name is null");
            return (Criteria) this;
        }

        public Criteria andLastOperatorNameIsNotNull() {
            addCriterion("last_operator_name is not null");
            return (Criteria) this;
        }

        public Criteria andLastOperatorNameEqualTo(String value) {
            addCriterion("last_operator_name =", value, "lastOperatorName");
            return (Criteria) this;
        }

        public Criteria andLastOperatorNameNotEqualTo(String value) {
            addCriterion("last_operator_name <>", value, "lastOperatorName");
            return (Criteria) this;
        }

        public Criteria andLastOperatorNameGreaterThan(String value) {
            addCriterion("last_operator_name >", value, "lastOperatorName");
            return (Criteria) this;
        }

        public Criteria andLastOperatorNameGreaterThanOrEqualTo(String value) {
            addCriterion("last_operator_name >=", value, "lastOperatorName");
            return (Criteria) this;
        }

        public Criteria andLastOperatorNameLessThan(String value) {
            addCriterion("last_operator_name <", value, "lastOperatorName");
            return (Criteria) this;
        }

        public Criteria andLastOperatorNameLessThanOrEqualTo(String value) {
            addCriterion("last_operator_name <=", value, "lastOperatorName");
            return (Criteria) this;
        }

        public Criteria andLastOperatorNameLike(String value) {
            addCriterion("last_operator_name like", value, "lastOperatorName");
            return (Criteria) this;
        }

        public Criteria andLastOperatorNameNotLike(String value) {
            addCriterion("last_operator_name not like", value, "lastOperatorName");
            return (Criteria) this;
        }

        public Criteria andLastOperatorNameIn(List<String> values) {
            addCriterion("last_operator_name in", values, "lastOperatorName");
            return (Criteria) this;
        }

        public Criteria andLastOperatorNameNotIn(List<String> values) {
            addCriterion("last_operator_name not in", values, "lastOperatorName");
            return (Criteria) this;
        }

        public Criteria andLastOperatorNameBetween(String value1, String value2) {
            addCriterion("last_operator_name between", value1, value2, "lastOperatorName");
            return (Criteria) this;
        }

        public Criteria andLastOperatorNameNotBetween(String value1, String value2) {
            addCriterion("last_operator_name not between", value1, value2, "lastOperatorName");
            return (Criteria) this;
        }

        public Criteria andLastOperatorIdIsNull() {
            addCriterion("last_operator_id is null");
            return (Criteria) this;
        }

        public Criteria andLastOperatorIdIsNotNull() {
            addCriterion("last_operator_id is not null");
            return (Criteria) this;
        }

        public Criteria andLastOperatorIdEqualTo(Long value) {
            addCriterion("last_operator_id =", value, "lastOperatorId");
            return (Criteria) this;
        }

        public Criteria andLastOperatorIdNotEqualTo(Long value) {
            addCriterion("last_operator_id <>", value, "lastOperatorId");
            return (Criteria) this;
        }

        public Criteria andLastOperatorIdGreaterThan(Long value) {
            addCriterion("last_operator_id >", value, "lastOperatorId");
            return (Criteria) this;
        }

        public Criteria andLastOperatorIdGreaterThanOrEqualTo(Long value) {
            addCriterion("last_operator_id >=", value, "lastOperatorId");
            return (Criteria) this;
        }

        public Criteria andLastOperatorIdLessThan(Long value) {
            addCriterion("last_operator_id <", value, "lastOperatorId");
            return (Criteria) this;
        }

        public Criteria andLastOperatorIdLessThanOrEqualTo(Long value) {
            addCriterion("last_operator_id <=", value, "lastOperatorId");
            return (Criteria) this;
        }

        public Criteria andLastOperatorIdIn(List<Long> values) {
            addCriterion("last_operator_id in", values, "lastOperatorId");
            return (Criteria) this;
        }

        public Criteria andLastOperatorIdNotIn(List<Long> values) {
            addCriterion("last_operator_id not in", values, "lastOperatorId");
            return (Criteria) this;
        }

        public Criteria andLastOperatorIdBetween(Long value1, Long value2) {
            addCriterion("last_operator_id between", value1, value2, "lastOperatorId");
            return (Criteria) this;
        }

        public Criteria andLastOperatorIdNotBetween(Long value1, Long value2) {
            addCriterion("last_operator_id not between", value1, value2, "lastOperatorId");
            return (Criteria) this;
        }

        public Criteria andLastOperateTimeIsNull() {
            addCriterion("last_operate_time is null");
            return (Criteria) this;
        }

        public Criteria andLastOperateTimeIsNotNull() {
            addCriterion("last_operate_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastOperateTimeEqualTo(LocalDateTime value) {
            addCriterion("last_operate_time =", value, "lastOperateTime");
            return (Criteria) this;
        }

        public Criteria andLastOperateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("last_operate_time <>", value, "lastOperateTime");
            return (Criteria) this;
        }

        public Criteria andLastOperateTimeGreaterThan(LocalDateTime value) {
            addCriterion("last_operate_time >", value, "lastOperateTime");
            return (Criteria) this;
        }

        public Criteria andLastOperateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_operate_time >=", value, "lastOperateTime");
            return (Criteria) this;
        }

        public Criteria andLastOperateTimeLessThan(LocalDateTime value) {
            addCriterion("last_operate_time <", value, "lastOperateTime");
            return (Criteria) this;
        }

        public Criteria andLastOperateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_operate_time <=", value, "lastOperateTime");
            return (Criteria) this;
        }

        public Criteria andLastOperateTimeIn(List<LocalDateTime> values) {
            addCriterion("last_operate_time in", values, "lastOperateTime");
            return (Criteria) this;
        }

        public Criteria andLastOperateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("last_operate_time not in", values, "lastOperateTime");
            return (Criteria) this;
        }

        public Criteria andLastOperateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_operate_time between", value1, value2, "lastOperateTime");
            return (Criteria) this;
        }

        public Criteria andLastOperateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_operate_time not between", value1, value2, "lastOperateTime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}