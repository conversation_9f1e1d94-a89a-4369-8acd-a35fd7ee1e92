package com.sankuai.shangou.logistics.delivery.model;

public class GrayConfigDO {
    private Long id;

    private String grayKey;

    private Integer storeOperationMode;

    private String grayCityList;

    private String grayStoreList;

    private Long tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGrayKey() {
        return grayKey;
    }

    public void setGrayKey(String grayKey) {
        this.grayKey = grayKey == null ? null : grayKey.trim();
    }

    public Integer getStoreOperationMode() {
        return storeOperationMode;
    }

    public void setStoreOperationMode(Integer storeOperationMode) {
        this.storeOperationMode = storeOperationMode;
    }

    public String getGrayCityList() {
        return grayCityList;
    }

    public void setGrayCityList(String grayCityList) {
        this.grayCityList = grayCityList == null ? null : grayCityList.trim();
    }

    public String getGrayStoreList() {
        return grayStoreList;
    }

    public void setGrayStoreList(String grayStoreList) {
        this.grayStoreList = grayStoreList == null ? null : grayStoreList.trim();
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        GrayConfigDO other = (GrayConfigDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getGrayKey() == null ? other.getGrayKey() == null : this.getGrayKey().equals(other.getGrayKey()))
            && (this.getStoreOperationMode() == null ? other.getStoreOperationMode() == null : this.getStoreOperationMode().equals(other.getStoreOperationMode()))
            && (this.getGrayCityList() == null ? other.getGrayCityList() == null : this.getGrayCityList().equals(other.getGrayCityList()))
            && (this.getGrayStoreList() == null ? other.getGrayStoreList() == null : this.getGrayStoreList().equals(other.getGrayStoreList()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getGrayKey() == null) ? 0 : getGrayKey().hashCode());
        result = prime * result + ((getStoreOperationMode() == null) ? 0 : getStoreOperationMode().hashCode());
        result = prime * result + ((getGrayCityList() == null) ? 0 : getGrayCityList().hashCode());
        result = prime * result + ((getGrayStoreList() == null) ? 0 : getGrayStoreList().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        return result;
    }
}