package com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension;

import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.condition.BatchTaskQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-21
 */
public interface ConfigTaskDOExMapper {
    List<ConfigTaskDO> queryBatchTaskList(BatchTaskQuery query);
}
