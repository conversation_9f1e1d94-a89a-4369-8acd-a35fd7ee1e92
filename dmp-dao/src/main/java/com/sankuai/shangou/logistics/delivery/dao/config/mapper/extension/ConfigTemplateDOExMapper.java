package com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension;

import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.condition.BatchTemplateQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-30
 */
public interface ConfigTemplateDOExMapper {
    List<ConfigTemplateDO> queryBatchTaskListIncludeConfigType(BatchTemplateQuery query);

    List<ConfigTemplateDO> queryBatchTaskListExcludeConfigType(BatchTemplateQuery query);
}
