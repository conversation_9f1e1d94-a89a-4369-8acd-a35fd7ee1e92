package com.sankuai.shangou.logistics.delivery.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class WaiMaDeliveryLoadPO {
    private Long id;

    private Long logisticsUnitId;

    private Integer channelId;

    private String channelPoiId;

    private BigDecimal scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder;

    private BigDecimal scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder;

    private BigDecimal attendanceDeliveryLoad;

    private BigDecimal fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder;

    private BigDecimal fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder;

    private LocalDateTime bizTime;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer isDeleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getLogisticsUnitId() {
        return logisticsUnitId;
    }

    public void setLogisticsUnitId(Long logisticsUnitId) {
        this.logisticsUnitId = logisticsUnitId;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public String getChannelPoiId() {
        return channelPoiId;
    }

    public void setChannelPoiId(String channelPoiId) {
        this.channelPoiId = channelPoiId == null ? null : channelPoiId.trim();
    }

    public BigDecimal getScheduledEmployeeDeliveryLoadIncludeUnscheduledOrder() {
        return scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder;
    }

    public void setScheduledEmployeeDeliveryLoadIncludeUnscheduledOrder(BigDecimal scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder) {
        this.scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder = scheduledEmployeeDeliveryLoadIncludeUnscheduledOrder;
    }

    public BigDecimal getScheduledEmployeeDeliveryLoadExcludeUnscheduledOrder() {
        return scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder;
    }

    public void setScheduledEmployeeDeliveryLoadExcludeUnscheduledOrder(BigDecimal scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder) {
        this.scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder = scheduledEmployeeDeliveryLoadExcludeUnscheduledOrder;
    }

    public BigDecimal getAttendanceDeliveryLoad() {
        return attendanceDeliveryLoad;
    }

    public void setAttendanceDeliveryLoad(BigDecimal attendanceDeliveryLoad) {
        this.attendanceDeliveryLoad = attendanceDeliveryLoad;
    }

    public BigDecimal getFulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder() {
        return fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder;
    }

    public void setFulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder(BigDecimal fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder) {
        this.fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder = fulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder;
    }

    public BigDecimal getFulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder() {
        return fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder;
    }

    public void setFulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder(BigDecimal fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder) {
        this.fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder = fulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder;
    }

    public LocalDateTime getBizTime() {
        return bizTime;
    }

    public void setBizTime(LocalDateTime bizTime) {
        this.bizTime = bizTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        WaiMaDeliveryLoadPO other = (WaiMaDeliveryLoadPO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getLogisticsUnitId() == null ? other.getLogisticsUnitId() == null : this.getLogisticsUnitId().equals(other.getLogisticsUnitId()))
            && (this.getChannelId() == null ? other.getChannelId() == null : this.getChannelId().equals(other.getChannelId()))
            && (this.getChannelPoiId() == null ? other.getChannelPoiId() == null : this.getChannelPoiId().equals(other.getChannelPoiId()))
            && (this.getScheduledEmployeeDeliveryLoadIncludeUnscheduledOrder() == null ? other.getScheduledEmployeeDeliveryLoadIncludeUnscheduledOrder() == null : this.getScheduledEmployeeDeliveryLoadIncludeUnscheduledOrder().equals(other.getScheduledEmployeeDeliveryLoadIncludeUnscheduledOrder()))
            && (this.getScheduledEmployeeDeliveryLoadExcludeUnscheduledOrder() == null ? other.getScheduledEmployeeDeliveryLoadExcludeUnscheduledOrder() == null : this.getScheduledEmployeeDeliveryLoadExcludeUnscheduledOrder().equals(other.getScheduledEmployeeDeliveryLoadExcludeUnscheduledOrder()))
            && (this.getAttendanceDeliveryLoad() == null ? other.getAttendanceDeliveryLoad() == null : this.getAttendanceDeliveryLoad().equals(other.getAttendanceDeliveryLoad()))
            && (this.getFulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder() == null ? other.getFulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder() == null : this.getFulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder().equals(other.getFulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder()))
            && (this.getFulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder() == null ? other.getFulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder() == null : this.getFulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder().equals(other.getFulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder()))
            && (this.getBizTime() == null ? other.getBizTime() == null : this.getBizTime().equals(other.getBizTime()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getIsDeleted() == null ? other.getIsDeleted() == null : this.getIsDeleted().equals(other.getIsDeleted()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getLogisticsUnitId() == null) ? 0 : getLogisticsUnitId().hashCode());
        result = prime * result + ((getChannelId() == null) ? 0 : getChannelId().hashCode());
        result = prime * result + ((getChannelPoiId() == null) ? 0 : getChannelPoiId().hashCode());
        result = prime * result + ((getScheduledEmployeeDeliveryLoadIncludeUnscheduledOrder() == null) ? 0 : getScheduledEmployeeDeliveryLoadIncludeUnscheduledOrder().hashCode());
        result = prime * result + ((getScheduledEmployeeDeliveryLoadExcludeUnscheduledOrder() == null) ? 0 : getScheduledEmployeeDeliveryLoadExcludeUnscheduledOrder().hashCode());
        result = prime * result + ((getAttendanceDeliveryLoad() == null) ? 0 : getAttendanceDeliveryLoad().hashCode());
        result = prime * result + ((getFulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder() == null) ? 0 : getFulfillEmployeeDeliveryLoadIncludeWaitAcceptOrder().hashCode());
        result = prime * result + ((getFulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder() == null) ? 0 : getFulfillEmployeeDeliveryLoadExcludeWaitAcceptOrder().hashCode());
        result = prime * result + ((getBizTime() == null) ? 0 : getBizTime().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDeleted() == null) ? 0 : getIsDeleted().hashCode());
        return result;
    }
}