package com.sankuai.shangou.logistics.delivery.dao.config.mapper;

import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ConfigTemplateItemDOMapper {
    long countByExample(ConfigTemplateItemDOExample example);

    int deleteByExample(ConfigTemplateItemDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ConfigTemplateItemDO record);

    int insertSelective(ConfigTemplateItemDO record);

    List<ConfigTemplateItemDO> selectByExample(ConfigTemplateItemDOExample example);

    ConfigTemplateItemDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ConfigTemplateItemDO record, @Param("example") ConfigTemplateItemDOExample example);

    int updateByExample(@Param("record") ConfigTemplateItemDO record, @Param("example") ConfigTemplateItemDOExample example);

    int updateByPrimaryKeySelective(ConfigTemplateItemDO record);

    int updateByPrimaryKey(ConfigTemplateItemDO record);
}