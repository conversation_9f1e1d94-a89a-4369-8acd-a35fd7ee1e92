package com.sankuai.shangou.logistics.delivery.dao.config.mapper;

import com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDOExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface IStoreConfigDOMapper {

    List<StoreConfigDO> selectByExampleWithLimit(@Param("example") StoreConfigDOExample example, @Param("limitNum") Integer limitNum);

}