package com.sankuai.shangou.logistics.delivery.dao.config.mapper;

import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ConfigTemplateDOMapper {
    long countByExample(ConfigTemplateDOExample example);

    int deleteByExample(ConfigTemplateDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ConfigTemplateDO record);

    int insertSelective(ConfigTemplateDO record);

    List<ConfigTemplateDO> selectByExample(ConfigTemplateDOExample example);

    ConfigTemplateDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ConfigTemplateDO record, @Param("example") ConfigTemplateDOExample example);

    int updateByExample(@Param("record") ConfigTemplateDO record, @Param("example") ConfigTemplateDOExample example);

    int updateByPrimaryKeySelective(ConfigTemplateDO record);

    int updateByPrimaryKey(ConfigTemplateDO record);
}