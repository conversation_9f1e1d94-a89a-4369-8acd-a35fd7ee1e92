package com.sankuai.shangou.logistics.delivery.dao.config.mapper;

import com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StoreConfigDOMapper {
    long countByExample(StoreConfigDOExample example);

    int deleteByExample(StoreConfigDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StoreConfigDO record);

    int insertSelective(StoreConfigDO record);

    List<StoreConfigDO> selectByExample(StoreConfigDOExample example);

    StoreConfigDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StoreConfigDO record, @Param("example") StoreConfigDOExample example);

    int updateByExample(@Param("record") StoreConfigDO record, @Param("example") StoreConfigDOExample example);

    int updateByPrimaryKeySelective(StoreConfigDO record);

    int updateByPrimaryKey(StoreConfigDO record);

    int batchInsert(@Param("list") List<StoreConfigDO> list);

    int batchUpdateByPrimaryKey(@Param("list") List<StoreConfigDO> list);
}