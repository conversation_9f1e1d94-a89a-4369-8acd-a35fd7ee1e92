package com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension;

import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-03
 */
public interface ConfigTaskItemDOExMapper {
    int batchInsert(@Param("recordList") List<ConfigTaskItemDO> recordList);

    int batchUpdate(List<ConfigTaskItemDO> collect);
}
