package com.sankuai.shangou.logistics.delivery.dao.config.model.condition;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-30
 */
@Data
public class BatchTemplateQuery {
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 维度类型： 1-按租户设置,2-按经营模式设置
     */
    private Integer dimensionType;
    /**
     * 维度id
     */
    private Integer dimensionId;
    /**
     * 模版类型
     */
    private List<Integer> templateTypeList;
    /**
     * 开始时间
     */
    private LocalDateTime beginTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 操作人/牵牛花账号
     */
    private String operator;
}
