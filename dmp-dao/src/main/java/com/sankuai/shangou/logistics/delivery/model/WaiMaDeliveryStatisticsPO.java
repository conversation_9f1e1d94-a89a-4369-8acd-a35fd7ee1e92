package com.sankuai.shangou.logistics.delivery.model;

import java.time.LocalDateTime;

public class WaiMaDeliveryStatisticsPO {
    private Long id;

    private Long logisticsUnitId;

    private Integer channelId;

    private String channelPoiId;

    private Integer ninePercentileFulfillmentDuration;

    private Integer eightPointFivePercentileFulfillmentDuration;

    private Integer eightPercentileFulfillmentDuration;

    private Integer selfDeliveryOrderCount;

    private LocalDateTime bizTime;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer isDeleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getLogisticsUnitId() {
        return logisticsUnitId;
    }

    public void setLogisticsUnitId(Long logisticsUnitId) {
        this.logisticsUnitId = logisticsUnitId;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public String getChannelPoiId() {
        return channelPoiId;
    }

    public void setChannelPoiId(String channelPoiId) {
        this.channelPoiId = channelPoiId == null ? null : channelPoiId.trim();
    }

    public Integer getNinePercentileFulfillmentDuration() {
        return ninePercentileFulfillmentDuration;
    }

    public void setNinePercentileFulfillmentDuration(Integer ninePercentileFulfillmentDuration) {
        this.ninePercentileFulfillmentDuration = ninePercentileFulfillmentDuration;
    }

    public Integer getEightPointFivePercentileFulfillmentDuration() {
        return eightPointFivePercentileFulfillmentDuration;
    }

    public void setEightPointFivePercentileFulfillmentDuration(Integer eightPointFivePercentileFulfillmentDuration) {
        this.eightPointFivePercentileFulfillmentDuration = eightPointFivePercentileFulfillmentDuration;
    }

    public Integer getEightPercentileFulfillmentDuration() {
        return eightPercentileFulfillmentDuration;
    }

    public void setEightPercentileFulfillmentDuration(Integer eightPercentileFulfillmentDuration) {
        this.eightPercentileFulfillmentDuration = eightPercentileFulfillmentDuration;
    }

    public Integer getSelfDeliveryOrderCount() {
        return selfDeliveryOrderCount;
    }

    public void setSelfDeliveryOrderCount(Integer selfDeliveryOrderCount) {
        this.selfDeliveryOrderCount = selfDeliveryOrderCount;
    }

    public LocalDateTime getBizTime() {
        return bizTime;
    }

    public void setBizTime(LocalDateTime bizTime) {
        this.bizTime = bizTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        WaiMaDeliveryStatisticsPO other = (WaiMaDeliveryStatisticsPO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getLogisticsUnitId() == null ? other.getLogisticsUnitId() == null : this.getLogisticsUnitId().equals(other.getLogisticsUnitId()))
            && (this.getChannelId() == null ? other.getChannelId() == null : this.getChannelId().equals(other.getChannelId()))
            && (this.getChannelPoiId() == null ? other.getChannelPoiId() == null : this.getChannelPoiId().equals(other.getChannelPoiId()))
            && (this.getNinePercentileFulfillmentDuration() == null ? other.getNinePercentileFulfillmentDuration() == null : this.getNinePercentileFulfillmentDuration().equals(other.getNinePercentileFulfillmentDuration()))
            && (this.getEightPointFivePercentileFulfillmentDuration() == null ? other.getEightPointFivePercentileFulfillmentDuration() == null : this.getEightPointFivePercentileFulfillmentDuration().equals(other.getEightPointFivePercentileFulfillmentDuration()))
            && (this.getEightPercentileFulfillmentDuration() == null ? other.getEightPercentileFulfillmentDuration() == null : this.getEightPercentileFulfillmentDuration().equals(other.getEightPercentileFulfillmentDuration()))
            && (this.getSelfDeliveryOrderCount() == null ? other.getSelfDeliveryOrderCount() == null : this.getSelfDeliveryOrderCount().equals(other.getSelfDeliveryOrderCount()))
            && (this.getBizTime() == null ? other.getBizTime() == null : this.getBizTime().equals(other.getBizTime()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getIsDeleted() == null ? other.getIsDeleted() == null : this.getIsDeleted().equals(other.getIsDeleted()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getLogisticsUnitId() == null) ? 0 : getLogisticsUnitId().hashCode());
        result = prime * result + ((getChannelId() == null) ? 0 : getChannelId().hashCode());
        result = prime * result + ((getChannelPoiId() == null) ? 0 : getChannelPoiId().hashCode());
        result = prime * result + ((getNinePercentileFulfillmentDuration() == null) ? 0 : getNinePercentileFulfillmentDuration().hashCode());
        result = prime * result + ((getEightPointFivePercentileFulfillmentDuration() == null) ? 0 : getEightPointFivePercentileFulfillmentDuration().hashCode());
        result = prime * result + ((getEightPercentileFulfillmentDuration() == null) ? 0 : getEightPercentileFulfillmentDuration().hashCode());
        result = prime * result + ((getSelfDeliveryOrderCount() == null) ? 0 : getSelfDeliveryOrderCount().hashCode());
        result = prime * result + ((getBizTime() == null) ? 0 : getBizTime().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDeleted() == null) ? 0 : getIsDeleted().hashCode());
        return result;
    }
}