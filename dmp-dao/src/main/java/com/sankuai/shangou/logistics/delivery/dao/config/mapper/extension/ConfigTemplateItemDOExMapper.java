package com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension;

import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-30
 */
public interface ConfigTemplateItemDOExMapper {
    List<ConfigTemplateItemDO> queryTemplateTypeByTemplateId(@Param("templateIds") Collection<Long> templateIds);

    int batchInsert(@Param("recordList") List<ConfigTemplateItemDO> recordList);
}
