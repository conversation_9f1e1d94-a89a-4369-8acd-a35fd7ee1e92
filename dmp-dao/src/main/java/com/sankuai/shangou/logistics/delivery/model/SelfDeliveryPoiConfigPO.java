package com.sankuai.shangou.logistics.delivery.model;

import java.time.LocalDateTime;

public class SelfDeliveryPoiConfigPO {
    private Long id;

    private Long tenantId;

    private Long poiId;

    private Integer enableTurnDelivery;

    private Integer enablePickDeliverySplit;

    private Integer dapSyncStatus;

    private String lastOperatorName;

    private Long lastOperatorId;

    private LocalDateTime lastOperateTime;

    private Integer isDeleted;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getPoiId() {
        return poiId;
    }

    public void setPoiId(Long poiId) {
        this.poiId = poiId;
    }

    public Integer getEnableTurnDelivery() {
        return enableTurnDelivery;
    }

    public void setEnableTurnDelivery(Integer enableTurnDelivery) {
        this.enableTurnDelivery = enableTurnDelivery;
    }

    public Integer getEnablePickDeliverySplit() {
        return enablePickDeliverySplit;
    }

    public void setEnablePickDeliverySplit(Integer enablePickDeliverySplit) {
        this.enablePickDeliverySplit = enablePickDeliverySplit;
    }

    public Integer getDapSyncStatus() {
        return dapSyncStatus;
    }

    public void setDapSyncStatus(Integer dapSyncStatus) {
        this.dapSyncStatus = dapSyncStatus;
    }

    public String getLastOperatorName() {
        return lastOperatorName;
    }

    public void setLastOperatorName(String lastOperatorName) {
        this.lastOperatorName = lastOperatorName == null ? null : lastOperatorName.trim();
    }

    public Long getLastOperatorId() {
        return lastOperatorId;
    }

    public void setLastOperatorId(Long lastOperatorId) {
        this.lastOperatorId = lastOperatorId;
    }

    public LocalDateTime getLastOperateTime() {
        return lastOperateTime;
    }

    public void setLastOperateTime(LocalDateTime lastOperateTime) {
        this.lastOperateTime = lastOperateTime;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SelfDeliveryPoiConfigPO other = (SelfDeliveryPoiConfigPO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()))
            && (this.getPoiId() == null ? other.getPoiId() == null : this.getPoiId().equals(other.getPoiId()))
            && (this.getEnableTurnDelivery() == null ? other.getEnableTurnDelivery() == null : this.getEnableTurnDelivery().equals(other.getEnableTurnDelivery()))
            && (this.getEnablePickDeliverySplit() == null ? other.getEnablePickDeliverySplit() == null : this.getEnablePickDeliverySplit().equals(other.getEnablePickDeliverySplit()))
            && (this.getDapSyncStatus() == null ? other.getDapSyncStatus() == null : this.getDapSyncStatus().equals(other.getDapSyncStatus()))
            && (this.getLastOperatorName() == null ? other.getLastOperatorName() == null : this.getLastOperatorName().equals(other.getLastOperatorName()))
            && (this.getLastOperatorId() == null ? other.getLastOperatorId() == null : this.getLastOperatorId().equals(other.getLastOperatorId()))
            && (this.getLastOperateTime() == null ? other.getLastOperateTime() == null : this.getLastOperateTime().equals(other.getLastOperateTime()))
            && (this.getIsDeleted() == null ? other.getIsDeleted() == null : this.getIsDeleted().equals(other.getIsDeleted()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        result = prime * result + ((getPoiId() == null) ? 0 : getPoiId().hashCode());
        result = prime * result + ((getEnableTurnDelivery() == null) ? 0 : getEnableTurnDelivery().hashCode());
        result = prime * result + ((getEnablePickDeliverySplit() == null) ? 0 : getEnablePickDeliverySplit().hashCode());
        result = prime * result + ((getDapSyncStatus() == null) ? 0 : getDapSyncStatus().hashCode());
        result = prime * result + ((getLastOperatorName() == null) ? 0 : getLastOperatorName().hashCode());
        result = prime * result + ((getLastOperatorId() == null) ? 0 : getLastOperatorId().hashCode());
        result = prime * result + ((getLastOperateTime() == null) ? 0 : getLastOperateTime().hashCode());
        result = prime * result + ((getIsDeleted() == null) ? 0 : getIsDeleted().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }
}