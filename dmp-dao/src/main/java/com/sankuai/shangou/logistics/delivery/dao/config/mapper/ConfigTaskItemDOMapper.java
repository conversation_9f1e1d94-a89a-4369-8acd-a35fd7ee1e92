package com.sankuai.shangou.logistics.delivery.dao.config.mapper;

import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ConfigTaskItemDOMapper {
    long countByExample(ConfigTaskItemDOExample example);

    int deleteByExample(ConfigTaskItemDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ConfigTaskItemDO record);

    int insertSelective(ConfigTaskItemDO record);

    List<ConfigTaskItemDO> selectByExample(ConfigTaskItemDOExample example);

    ConfigTaskItemDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ConfigTaskItemDO record, @Param("example") ConfigTaskItemDOExample example);

    int updateByExample(@Param("record") ConfigTaskItemDO record, @Param("example") ConfigTaskItemDOExample example);

    int updateByPrimaryKeySelective(ConfigTaskItemDO record);

    int updateByPrimaryKey(ConfigTaskItemDO record);
}