package com.sankuai.shangou.logistics.delivery.dao.config.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 配置模版
 * config_template
 */
@Data
public class ConfigTemplateDO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 名称
     */
    private String name;

    /**
     * 维度类型 1-租户, 2-经营模式
     */
    private Integer dimensionType;

    /**
     * 维度id
     */
    private Integer dimensionId;

    /**
     * 是否同步存量门店
     */
    private Integer isSyncWhenCreate;

    /**
     * 状态: 0-失效, 1-生效
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 创建人(牵牛花账号)
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-未删除, 1-已删除
     */
    private Integer isDelete;

    private static final long serialVersionUID = 1L;
}