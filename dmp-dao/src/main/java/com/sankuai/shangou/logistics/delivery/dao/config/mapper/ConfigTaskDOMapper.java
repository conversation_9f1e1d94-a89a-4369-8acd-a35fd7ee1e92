package com.sankuai.shangou.logistics.delivery.dao.config.mapper;

import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ConfigTaskDOMapper {
    long countByExample(ConfigTaskDOExample example);

    int deleteByExample(ConfigTaskDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ConfigTaskDO record);

    int insertSelective(ConfigTaskDO record);

    List<ConfigTaskDO> selectByExample(ConfigTaskDOExample example);

    ConfigTaskDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ConfigTaskDO record, @Param("example") ConfigTaskDOExample example);

    int updateByExample(@Param("record") ConfigTaskDO record, @Param("example") ConfigTaskDOExample example);

    int updateByPrimaryKeySelective(ConfigTaskDO record);

    int updateByPrimaryKey(ConfigTaskDO record);
}