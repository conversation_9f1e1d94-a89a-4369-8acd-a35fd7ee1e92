package com.sankuai.shangou.logistics.delivery.mapper;

import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPO;
import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SelfDeliveryPoiConfigPOMapper {
    long countByExample(SelfDeliveryPoiConfigPOExample example);

    int deleteByExample(SelfDeliveryPoiConfigPOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SelfDeliveryPoiConfigPO record);

    int insertSelective(SelfDeliveryPoiConfigPO record);

    List<SelfDeliveryPoiConfigPO> selectByExample(SelfDeliveryPoiConfigPOExample example);

    SelfDeliveryPoiConfigPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SelfDeliveryPoiConfigPO record, @Param("example") SelfDeliveryPoiConfigPOExample example);

    int updateByExample(@Param("record") SelfDeliveryPoiConfigPO record, @Param("example") SelfDeliveryPoiConfigPOExample example);

    int updateByPrimaryKeySelective(SelfDeliveryPoiConfigPO record);

    int updateByPrimaryKey(SelfDeliveryPoiConfigPO record);

    int batchInsert(@Param("list") List<SelfDeliveryPoiConfigPO> list);
}