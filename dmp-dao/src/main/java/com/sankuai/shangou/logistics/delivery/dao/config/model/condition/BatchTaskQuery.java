package com.sankuai.shangou.logistics.delivery.dao.config.model.condition;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-21
 */
@Data
public class BatchTaskQuery {
    /**
     * 租户id
     */
    private Long tenantId;

    private Integer status;

    private Long configTemplateId;
    /**
     * 任务类型
     * @see BatchTaskTypeEnum
     */
    private Integer taskType;
    /**
     * 开始时间
     */
    private LocalDateTime beginTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 操作人/牵牛花账号
     */
    private String operator;
}
