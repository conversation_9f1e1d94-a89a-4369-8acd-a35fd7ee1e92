package com.sankuai.shangou.logistics.delivery.mapper;

import com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryStatisticsPO;
import com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryStatisticsPOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WaiMaDeliveryStatisticsPOMapper {
    long countByExample(WaiMaDeliveryStatisticsPOExample example);

    int deleteByExample(WaiMaDeliveryStatisticsPOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(WaiMaDeliveryStatisticsPO record);

    int insertSelective(WaiMaDeliveryStatisticsPO record);

    List<WaiMaDeliveryStatisticsPO> selectByExample(WaiMaDeliveryStatisticsPOExample example);

    WaiMaDeliveryStatisticsPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") WaiMaDeliveryStatisticsPO record, @Param("example") WaiMaDeliveryStatisticsPOExample example);

    int updateByExample(@Param("record") WaiMaDeliveryStatisticsPO record, @Param("example") WaiMaDeliveryStatisticsPOExample example);

    int updateByPrimaryKeySelective(WaiMaDeliveryStatisticsPO record);

    int updateByPrimaryKey(WaiMaDeliveryStatisticsPO record);

    int batchInsert(@Param("list") List<WaiMaDeliveryStatisticsPO> list);
}