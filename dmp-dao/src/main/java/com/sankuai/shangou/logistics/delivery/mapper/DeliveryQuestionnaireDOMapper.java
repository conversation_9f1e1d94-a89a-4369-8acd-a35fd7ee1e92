package com.sankuai.shangou.logistics.delivery.mapper;

import com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDO;
import com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeliveryQuestionnaireDOMapper {
    long countByExample(DeliveryQuestionnaireDOExample example);

    int deleteByExample(DeliveryQuestionnaireDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DeliveryQuestionnaireDO record);

    int insertSelective(DeliveryQuestionnaireDO record);

    List<DeliveryQuestionnaireDO> selectByExample(DeliveryQuestionnaireDOExample example);

    DeliveryQuestionnaireDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DeliveryQuestionnaireDO record, @Param("example") DeliveryQuestionnaireDOExample example);

    int updateByExample(@Param("record") DeliveryQuestionnaireDO record, @Param("example") DeliveryQuestionnaireDOExample example);

    int updateByPrimaryKeySelective(DeliveryQuestionnaireDO record);

    int updateByPrimaryKey(DeliveryQuestionnaireDO record);

    int batchInsert(@Param("list") List<DeliveryQuestionnaireDO> list);
}