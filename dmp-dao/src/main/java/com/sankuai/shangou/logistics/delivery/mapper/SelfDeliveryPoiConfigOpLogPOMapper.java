package com.sankuai.shangou.logistics.delivery.mapper;

import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigOpLogPO;
import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigOpLogPOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SelfDeliveryPoiConfigOpLogPOMapper {
    long countByExample(SelfDeliveryPoiConfigOpLogPOExample example);

    int deleteByExample(SelfDeliveryPoiConfigOpLogPOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SelfDeliveryPoiConfigOpLogPO record);

    int insertSelective(SelfDeliveryPoiConfigOpLogPO record);

    List<SelfDeliveryPoiConfigOpLogPO> selectByExample(SelfDeliveryPoiConfigOpLogPOExample example);

    SelfDeliveryPoiConfigOpLogPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SelfDeliveryPoiConfigOpLogPO record, @Param("example") SelfDeliveryPoiConfigOpLogPOExample example);

    int updateByExample(@Param("record") SelfDeliveryPoiConfigOpLogPO record, @Param("example") SelfDeliveryPoiConfigOpLogPOExample example);

    int updateByPrimaryKeySelective(SelfDeliveryPoiConfigOpLogPO record);

    int updateByPrimaryKey(SelfDeliveryPoiConfigOpLogPO record);

    int batchInsert(@Param("list") List<SelfDeliveryPoiConfigOpLogPO> list);
}