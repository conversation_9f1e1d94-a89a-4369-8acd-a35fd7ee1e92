package com.sankuai.shangou.logistics.delivery.dao.config.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class StoreConfigDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public StoreConfigDOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(Long value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(Long value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(Long value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(Long value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(Long value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<Long> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<Long> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(Long value1, Long value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(Long value1, Long value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andAddressIsNull() {
            addCriterion("address is null");
            return (Criteria) this;
        }

        public Criteria andAddressIsNotNull() {
            addCriterion("address is not null");
            return (Criteria) this;
        }

        public Criteria andAddressEqualTo(String value) {
            addCriterion("address =", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotEqualTo(String value) {
            addCriterion("address <>", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThan(String value) {
            addCriterion("address >", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThanOrEqualTo(String value) {
            addCriterion("address >=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThan(String value) {
            addCriterion("address <", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThanOrEqualTo(String value) {
            addCriterion("address <=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLike(String value) {
            addCriterion("address like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotLike(String value) {
            addCriterion("address not like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressIn(List<String> values) {
            addCriterion("address in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotIn(List<String> values) {
            addCriterion("address not in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressBetween(String value1, String value2) {
            addCriterion("address between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotBetween(String value1, String value2) {
            addCriterion("address not between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNull() {
            addCriterion("contact_phone is null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNotNull() {
            addCriterion("contact_phone is not null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneEqualTo(String value) {
            addCriterion("contact_phone =", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotEqualTo(String value) {
            addCriterion("contact_phone <>", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThan(String value) {
            addCriterion("contact_phone >", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("contact_phone >=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThan(String value) {
            addCriterion("contact_phone <", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThanOrEqualTo(String value) {
            addCriterion("contact_phone <=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLike(String value) {
            addCriterion("contact_phone like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotLike(String value) {
            addCriterion("contact_phone not like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIn(List<String> values) {
            addCriterion("contact_phone in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotIn(List<String> values) {
            addCriterion("contact_phone not in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneBetween(String value1, String value2) {
            addCriterion("contact_phone between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotBetween(String value1, String value2) {
            addCriterion("contact_phone not between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyIsNull() {
            addCriterion("delivery_strategy is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyIsNotNull() {
            addCriterion("delivery_strategy is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyEqualTo(Integer value) {
            addCriterion("delivery_strategy =", value, "deliveryStrategy");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyNotEqualTo(Integer value) {
            addCriterion("delivery_strategy <>", value, "deliveryStrategy");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyGreaterThan(Integer value) {
            addCriterion("delivery_strategy >", value, "deliveryStrategy");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_strategy >=", value, "deliveryStrategy");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyLessThan(Integer value) {
            addCriterion("delivery_strategy <", value, "deliveryStrategy");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_strategy <=", value, "deliveryStrategy");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyIn(List<Integer> values) {
            addCriterion("delivery_strategy in", values, "deliveryStrategy");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyNotIn(List<Integer> values) {
            addCriterion("delivery_strategy not in", values, "deliveryStrategy");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyBetween(Integer value1, Integer value2) {
            addCriterion("delivery_strategy between", value1, value2, "deliveryStrategy");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_strategy not between", value1, value2, "deliveryStrategy");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyConfigIsNull() {
            addCriterion("delivery_strategy_config is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyConfigIsNotNull() {
            addCriterion("delivery_strategy_config is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyConfigEqualTo(String value) {
            addCriterion("delivery_strategy_config =", value, "deliveryStrategyConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyConfigNotEqualTo(String value) {
            addCriterion("delivery_strategy_config <>", value, "deliveryStrategyConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyConfigGreaterThan(String value) {
            addCriterion("delivery_strategy_config >", value, "deliveryStrategyConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyConfigGreaterThanOrEqualTo(String value) {
            addCriterion("delivery_strategy_config >=", value, "deliveryStrategyConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyConfigLessThan(String value) {
            addCriterion("delivery_strategy_config <", value, "deliveryStrategyConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyConfigLessThanOrEqualTo(String value) {
            addCriterion("delivery_strategy_config <=", value, "deliveryStrategyConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyConfigLike(String value) {
            addCriterion("delivery_strategy_config like", value, "deliveryStrategyConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyConfigNotLike(String value) {
            addCriterion("delivery_strategy_config not like", value, "deliveryStrategyConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyConfigIn(List<String> values) {
            addCriterion("delivery_strategy_config in", values, "deliveryStrategyConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyConfigNotIn(List<String> values) {
            addCriterion("delivery_strategy_config not in", values, "deliveryStrategyConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyConfigBetween(String value1, String value2) {
            addCriterion("delivery_strategy_config between", value1, value2, "deliveryStrategyConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryStrategyConfigNotBetween(String value1, String value2) {
            addCriterion("delivery_strategy_config not between", value1, value2, "deliveryStrategyConfig");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchPointIsNull() {
            addCriterion("delivery_launch_point is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchPointIsNotNull() {
            addCriterion("delivery_launch_point is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchPointEqualTo(Integer value) {
            addCriterion("delivery_launch_point =", value, "deliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchPointNotEqualTo(Integer value) {
            addCriterion("delivery_launch_point <>", value, "deliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchPointGreaterThan(Integer value) {
            addCriterion("delivery_launch_point >", value, "deliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchPointGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_launch_point >=", value, "deliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchPointLessThan(Integer value) {
            addCriterion("delivery_launch_point <", value, "deliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchPointLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_launch_point <=", value, "deliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchPointIn(List<Integer> values) {
            addCriterion("delivery_launch_point in", values, "deliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchPointNotIn(List<Integer> values) {
            addCriterion("delivery_launch_point not in", values, "deliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchPointBetween(Integer value1, Integer value2) {
            addCriterion("delivery_launch_point between", value1, value2, "deliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchPointNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_launch_point not between", value1, value2, "deliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchDelayMinutesIsNull() {
            addCriterion("delivery_launch_delay_minutes is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchDelayMinutesIsNotNull() {
            addCriterion("delivery_launch_delay_minutes is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchDelayMinutesEqualTo(Integer value) {
            addCriterion("delivery_launch_delay_minutes =", value, "deliveryLaunchDelayMinutes");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchDelayMinutesNotEqualTo(Integer value) {
            addCriterion("delivery_launch_delay_minutes <>", value, "deliveryLaunchDelayMinutes");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchDelayMinutesGreaterThan(Integer value) {
            addCriterion("delivery_launch_delay_minutes >", value, "deliveryLaunchDelayMinutes");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchDelayMinutesGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_launch_delay_minutes >=", value, "deliveryLaunchDelayMinutes");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchDelayMinutesLessThan(Integer value) {
            addCriterion("delivery_launch_delay_minutes <", value, "deliveryLaunchDelayMinutes");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchDelayMinutesLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_launch_delay_minutes <=", value, "deliveryLaunchDelayMinutes");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchDelayMinutesIn(List<Integer> values) {
            addCriterion("delivery_launch_delay_minutes in", values, "deliveryLaunchDelayMinutes");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchDelayMinutesNotIn(List<Integer> values) {
            addCriterion("delivery_launch_delay_minutes not in", values, "deliveryLaunchDelayMinutes");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchDelayMinutesBetween(Integer value1, Integer value2) {
            addCriterion("delivery_launch_delay_minutes between", value1, value2, "deliveryLaunchDelayMinutes");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchDelayMinutesNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_launch_delay_minutes not between", value1, value2, "deliveryLaunchDelayMinutes");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchPointIsNull() {
            addCriterion("booking_order_delivery_launch_point is null");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchPointIsNotNull() {
            addCriterion("booking_order_delivery_launch_point is not null");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchPointEqualTo(Integer value) {
            addCriterion("booking_order_delivery_launch_point =", value, "bookingOrderDeliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchPointNotEqualTo(Integer value) {
            addCriterion("booking_order_delivery_launch_point <>", value, "bookingOrderDeliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchPointGreaterThan(Integer value) {
            addCriterion("booking_order_delivery_launch_point >", value, "bookingOrderDeliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchPointGreaterThanOrEqualTo(Integer value) {
            addCriterion("booking_order_delivery_launch_point >=", value, "bookingOrderDeliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchPointLessThan(Integer value) {
            addCriterion("booking_order_delivery_launch_point <", value, "bookingOrderDeliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchPointLessThanOrEqualTo(Integer value) {
            addCriterion("booking_order_delivery_launch_point <=", value, "bookingOrderDeliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchPointIn(List<Integer> values) {
            addCriterion("booking_order_delivery_launch_point in", values, "bookingOrderDeliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchPointNotIn(List<Integer> values) {
            addCriterion("booking_order_delivery_launch_point not in", values, "bookingOrderDeliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchPointBetween(Integer value1, Integer value2) {
            addCriterion("booking_order_delivery_launch_point between", value1, value2, "bookingOrderDeliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchPointNotBetween(Integer value1, Integer value2) {
            addCriterion("booking_order_delivery_launch_point not between", value1, value2, "bookingOrderDeliveryLaunchPoint");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchMinutesIsNull() {
            addCriterion("booking_order_delivery_launch_minutes is null");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchMinutesIsNotNull() {
            addCriterion("booking_order_delivery_launch_minutes is not null");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchMinutesEqualTo(Integer value) {
            addCriterion("booking_order_delivery_launch_minutes =", value, "bookingOrderDeliveryLaunchMinutes");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchMinutesNotEqualTo(Integer value) {
            addCriterion("booking_order_delivery_launch_minutes <>", value, "bookingOrderDeliveryLaunchMinutes");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchMinutesGreaterThan(Integer value) {
            addCriterion("booking_order_delivery_launch_minutes >", value, "bookingOrderDeliveryLaunchMinutes");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchMinutesGreaterThanOrEqualTo(Integer value) {
            addCriterion("booking_order_delivery_launch_minutes >=", value, "bookingOrderDeliveryLaunchMinutes");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchMinutesLessThan(Integer value) {
            addCriterion("booking_order_delivery_launch_minutes <", value, "bookingOrderDeliveryLaunchMinutes");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchMinutesLessThanOrEqualTo(Integer value) {
            addCriterion("booking_order_delivery_launch_minutes <=", value, "bookingOrderDeliveryLaunchMinutes");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchMinutesIn(List<Integer> values) {
            addCriterion("booking_order_delivery_launch_minutes in", values, "bookingOrderDeliveryLaunchMinutes");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchMinutesNotIn(List<Integer> values) {
            addCriterion("booking_order_delivery_launch_minutes not in", values, "bookingOrderDeliveryLaunchMinutes");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchMinutesBetween(Integer value1, Integer value2) {
            addCriterion("booking_order_delivery_launch_minutes between", value1, value2, "bookingOrderDeliveryLaunchMinutes");
            return (Criteria) this;
        }

        public Criteria andBookingOrderDeliveryLaunchMinutesNotBetween(Integer value1, Integer value2) {
            addCriterion("booking_order_delivery_launch_minutes not between", value1, value2, "bookingOrderDeliveryLaunchMinutes");
            return (Criteria) this;
        }

        public Criteria andOrderPlatformDeliveryConfigIsNull() {
            addCriterion("order_platform_delivery_config is null");
            return (Criteria) this;
        }

        public Criteria andOrderPlatformDeliveryConfigIsNotNull() {
            addCriterion("order_platform_delivery_config is not null");
            return (Criteria) this;
        }

        public Criteria andOrderPlatformDeliveryConfigEqualTo(String value) {
            addCriterion("order_platform_delivery_config =", value, "orderPlatformDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andOrderPlatformDeliveryConfigNotEqualTo(String value) {
            addCriterion("order_platform_delivery_config <>", value, "orderPlatformDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andOrderPlatformDeliveryConfigGreaterThan(String value) {
            addCriterion("order_platform_delivery_config >", value, "orderPlatformDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andOrderPlatformDeliveryConfigGreaterThanOrEqualTo(String value) {
            addCriterion("order_platform_delivery_config >=", value, "orderPlatformDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andOrderPlatformDeliveryConfigLessThan(String value) {
            addCriterion("order_platform_delivery_config <", value, "orderPlatformDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andOrderPlatformDeliveryConfigLessThanOrEqualTo(String value) {
            addCriterion("order_platform_delivery_config <=", value, "orderPlatformDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andOrderPlatformDeliveryConfigLike(String value) {
            addCriterion("order_platform_delivery_config like", value, "orderPlatformDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andOrderPlatformDeliveryConfigNotLike(String value) {
            addCriterion("order_platform_delivery_config not like", value, "orderPlatformDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andOrderPlatformDeliveryConfigIn(List<String> values) {
            addCriterion("order_platform_delivery_config in", values, "orderPlatformDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andOrderPlatformDeliveryConfigNotIn(List<String> values) {
            addCriterion("order_platform_delivery_config not in", values, "orderPlatformDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andOrderPlatformDeliveryConfigBetween(String value1, String value2) {
            addCriterion("order_platform_delivery_config between", value1, value2, "orderPlatformDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andOrderPlatformDeliveryConfigNotBetween(String value1, String value2) {
            addCriterion("order_platform_delivery_config not between", value1, value2, "orderPlatformDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andEnabledIsNull() {
            addCriterion("enabled is null");
            return (Criteria) this;
        }

        public Criteria andEnabledIsNotNull() {
            addCriterion("enabled is not null");
            return (Criteria) this;
        }

        public Criteria andEnabledEqualTo(Integer value) {
            addCriterion("enabled =", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotEqualTo(Integer value) {
            addCriterion("enabled <>", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledGreaterThan(Integer value) {
            addCriterion("enabled >", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledGreaterThanOrEqualTo(Integer value) {
            addCriterion("enabled >=", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledLessThan(Integer value) {
            addCriterion("enabled <", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledLessThanOrEqualTo(Integer value) {
            addCriterion("enabled <=", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledIn(List<Integer> values) {
            addCriterion("enabled in", values, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotIn(List<Integer> values) {
            addCriterion("enabled not in", values, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledBetween(Integer value1, Integer value2) {
            addCriterion("enabled between", value1, value2, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotBetween(Integer value1, Integer value2) {
            addCriterion("enabled not between", value1, value2, "enabled");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIsAutoLaunchIsNull() {
            addCriterion("is_auto_launch is null");
            return (Criteria) this;
        }

        public Criteria andIsAutoLaunchIsNotNull() {
            addCriterion("is_auto_launch is not null");
            return (Criteria) this;
        }

        public Criteria andIsAutoLaunchEqualTo(Integer value) {
            addCriterion("is_auto_launch =", value, "isAutoLaunch");
            return (Criteria) this;
        }

        public Criteria andIsAutoLaunchNotEqualTo(Integer value) {
            addCriterion("is_auto_launch <>", value, "isAutoLaunch");
            return (Criteria) this;
        }

        public Criteria andIsAutoLaunchGreaterThan(Integer value) {
            addCriterion("is_auto_launch >", value, "isAutoLaunch");
            return (Criteria) this;
        }

        public Criteria andIsAutoLaunchGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_auto_launch >=", value, "isAutoLaunch");
            return (Criteria) this;
        }

        public Criteria andIsAutoLaunchLessThan(Integer value) {
            addCriterion("is_auto_launch <", value, "isAutoLaunch");
            return (Criteria) this;
        }

        public Criteria andIsAutoLaunchLessThanOrEqualTo(Integer value) {
            addCriterion("is_auto_launch <=", value, "isAutoLaunch");
            return (Criteria) this;
        }

        public Criteria andIsAutoLaunchIn(List<Integer> values) {
            addCriterion("is_auto_launch in", values, "isAutoLaunch");
            return (Criteria) this;
        }

        public Criteria andIsAutoLaunchNotIn(List<Integer> values) {
            addCriterion("is_auto_launch not in", values, "isAutoLaunch");
            return (Criteria) this;
        }

        public Criteria andIsAutoLaunchBetween(Integer value1, Integer value2) {
            addCriterion("is_auto_launch between", value1, value2, "isAutoLaunch");
            return (Criteria) this;
        }

        public Criteria andIsAutoLaunchNotBetween(Integer value1, Integer value2) {
            addCriterion("is_auto_launch not between", value1, value2, "isAutoLaunch");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchRuleIsNull() {
            addCriterion("delivery_launch_rule is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchRuleIsNotNull() {
            addCriterion("delivery_launch_rule is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchRuleEqualTo(Integer value) {
            addCriterion("delivery_launch_rule =", value, "deliveryLaunchRule");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchRuleNotEqualTo(Integer value) {
            addCriterion("delivery_launch_rule <>", value, "deliveryLaunchRule");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchRuleGreaterThan(Integer value) {
            addCriterion("delivery_launch_rule >", value, "deliveryLaunchRule");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchRuleGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_launch_rule >=", value, "deliveryLaunchRule");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchRuleLessThan(Integer value) {
            addCriterion("delivery_launch_rule <", value, "deliveryLaunchRule");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchRuleLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_launch_rule <=", value, "deliveryLaunchRule");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchRuleIn(List<Integer> values) {
            addCriterion("delivery_launch_rule in", values, "deliveryLaunchRule");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchRuleNotIn(List<Integer> values) {
            addCriterion("delivery_launch_rule not in", values, "deliveryLaunchRule");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchRuleBetween(Integer value1, Integer value2) {
            addCriterion("delivery_launch_rule between", value1, value2, "deliveryLaunchRule");
            return (Criteria) this;
        }

        public Criteria andDeliveryLaunchRuleNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_launch_rule not between", value1, value2, "deliveryLaunchRule");
            return (Criteria) this;
        }

        public Criteria andOpenAggrPlatformIsNull() {
            addCriterion("open_aggr_platform is null");
            return (Criteria) this;
        }

        public Criteria andOpenAggrPlatformIsNotNull() {
            addCriterion("open_aggr_platform is not null");
            return (Criteria) this;
        }

        public Criteria andOpenAggrPlatformEqualTo(Integer value) {
            addCriterion("open_aggr_platform =", value, "openAggrPlatform");
            return (Criteria) this;
        }

        public Criteria andOpenAggrPlatformNotEqualTo(Integer value) {
            addCriterion("open_aggr_platform <>", value, "openAggrPlatform");
            return (Criteria) this;
        }

        public Criteria andOpenAggrPlatformGreaterThan(Integer value) {
            addCriterion("open_aggr_platform >", value, "openAggrPlatform");
            return (Criteria) this;
        }

        public Criteria andOpenAggrPlatformGreaterThanOrEqualTo(Integer value) {
            addCriterion("open_aggr_platform >=", value, "openAggrPlatform");
            return (Criteria) this;
        }

        public Criteria andOpenAggrPlatformLessThan(Integer value) {
            addCriterion("open_aggr_platform <", value, "openAggrPlatform");
            return (Criteria) this;
        }

        public Criteria andOpenAggrPlatformLessThanOrEqualTo(Integer value) {
            addCriterion("open_aggr_platform <=", value, "openAggrPlatform");
            return (Criteria) this;
        }

        public Criteria andOpenAggrPlatformIn(List<Integer> values) {
            addCriterion("open_aggr_platform in", values, "openAggrPlatform");
            return (Criteria) this;
        }

        public Criteria andOpenAggrPlatformNotIn(List<Integer> values) {
            addCriterion("open_aggr_platform not in", values, "openAggrPlatform");
            return (Criteria) this;
        }

        public Criteria andOpenAggrPlatformBetween(Integer value1, Integer value2) {
            addCriterion("open_aggr_platform between", value1, value2, "openAggrPlatform");
            return (Criteria) this;
        }

        public Criteria andOpenAggrPlatformNotBetween(Integer value1, Integer value2) {
            addCriterion("open_aggr_platform not between", value1, value2, "openAggrPlatform");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(Integer value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(Integer value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(Integer value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(Integer value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(Integer value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<Integer> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<Integer> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(Integer value1, Integer value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andChannelTypeIsNull() {
            addCriterion("channel_type is null");
            return (Criteria) this;
        }

        public Criteria andChannelTypeIsNotNull() {
            addCriterion("channel_type is not null");
            return (Criteria) this;
        }

        public Criteria andChannelTypeEqualTo(Integer value) {
            addCriterion("channel_type =", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeNotEqualTo(Integer value) {
            addCriterion("channel_type <>", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeGreaterThan(Integer value) {
            addCriterion("channel_type >", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("channel_type >=", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeLessThan(Integer value) {
            addCriterion("channel_type <", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeLessThanOrEqualTo(Integer value) {
            addCriterion("channel_type <=", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeIn(List<Integer> values) {
            addCriterion("channel_type in", values, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeNotIn(List<Integer> values) {
            addCriterion("channel_type not in", values, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeBetween(Integer value1, Integer value2) {
            addCriterion("channel_type between", value1, value2, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("channel_type not between", value1, value2, "channelType");
            return (Criteria) this;
        }

        public Criteria andLastPlatformTypeIsNull() {
            addCriterion("last_platform_type is null");
            return (Criteria) this;
        }

        public Criteria andLastPlatformTypeIsNotNull() {
            addCriterion("last_platform_type is not null");
            return (Criteria) this;
        }

        public Criteria andLastPlatformTypeEqualTo(Integer value) {
            addCriterion("last_platform_type =", value, "lastPlatformType");
            return (Criteria) this;
        }

        public Criteria andLastPlatformTypeNotEqualTo(Integer value) {
            addCriterion("last_platform_type <>", value, "lastPlatformType");
            return (Criteria) this;
        }

        public Criteria andLastPlatformTypeGreaterThan(Integer value) {
            addCriterion("last_platform_type >", value, "lastPlatformType");
            return (Criteria) this;
        }

        public Criteria andLastPlatformTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_platform_type >=", value, "lastPlatformType");
            return (Criteria) this;
        }

        public Criteria andLastPlatformTypeLessThan(Integer value) {
            addCriterion("last_platform_type <", value, "lastPlatformType");
            return (Criteria) this;
        }

        public Criteria andLastPlatformTypeLessThanOrEqualTo(Integer value) {
            addCriterion("last_platform_type <=", value, "lastPlatformType");
            return (Criteria) this;
        }

        public Criteria andLastPlatformTypeIn(List<Integer> values) {
            addCriterion("last_platform_type in", values, "lastPlatformType");
            return (Criteria) this;
        }

        public Criteria andLastPlatformTypeNotIn(List<Integer> values) {
            addCriterion("last_platform_type not in", values, "lastPlatformType");
            return (Criteria) this;
        }

        public Criteria andLastPlatformTypeBetween(Integer value1, Integer value2) {
            addCriterion("last_platform_type between", value1, value2, "lastPlatformType");
            return (Criteria) this;
        }

        public Criteria andLastPlatformTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("last_platform_type not between", value1, value2, "lastPlatformType");
            return (Criteria) this;
        }

        public Criteria andIsShowItemNumberIsNull() {
            addCriterion("is_show_item_number is null");
            return (Criteria) this;
        }

        public Criteria andIsShowItemNumberIsNotNull() {
            addCriterion("is_show_item_number is not null");
            return (Criteria) this;
        }

        public Criteria andIsShowItemNumberEqualTo(Integer value) {
            addCriterion("is_show_item_number =", value, "isShowItemNumber");
            return (Criteria) this;
        }

        public Criteria andIsShowItemNumberNotEqualTo(Integer value) {
            addCriterion("is_show_item_number <>", value, "isShowItemNumber");
            return (Criteria) this;
        }

        public Criteria andIsShowItemNumberGreaterThan(Integer value) {
            addCriterion("is_show_item_number >", value, "isShowItemNumber");
            return (Criteria) this;
        }

        public Criteria andIsShowItemNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_show_item_number >=", value, "isShowItemNumber");
            return (Criteria) this;
        }

        public Criteria andIsShowItemNumberLessThan(Integer value) {
            addCriterion("is_show_item_number <", value, "isShowItemNumber");
            return (Criteria) this;
        }

        public Criteria andIsShowItemNumberLessThanOrEqualTo(Integer value) {
            addCriterion("is_show_item_number <=", value, "isShowItemNumber");
            return (Criteria) this;
        }

        public Criteria andIsShowItemNumberIn(List<Integer> values) {
            addCriterion("is_show_item_number in", values, "isShowItemNumber");
            return (Criteria) this;
        }

        public Criteria andIsShowItemNumberNotIn(List<Integer> values) {
            addCriterion("is_show_item_number not in", values, "isShowItemNumber");
            return (Criteria) this;
        }

        public Criteria andIsShowItemNumberBetween(Integer value1, Integer value2) {
            addCriterion("is_show_item_number between", value1, value2, "isShowItemNumber");
            return (Criteria) this;
        }

        public Criteria andIsShowItemNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("is_show_item_number not between", value1, value2, "isShowItemNumber");
            return (Criteria) this;
        }

        public Criteria andSelfAssessDeliveryConfigIsNull() {
            addCriterion("self_assess_delivery_config is null");
            return (Criteria) this;
        }

        public Criteria andSelfAssessDeliveryConfigIsNotNull() {
            addCriterion("self_assess_delivery_config is not null");
            return (Criteria) this;
        }

        public Criteria andSelfAssessDeliveryConfigEqualTo(String value) {
            addCriterion("self_assess_delivery_config =", value, "selfAssessDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andSelfAssessDeliveryConfigNotEqualTo(String value) {
            addCriterion("self_assess_delivery_config <>", value, "selfAssessDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andSelfAssessDeliveryConfigGreaterThan(String value) {
            addCriterion("self_assess_delivery_config >", value, "selfAssessDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andSelfAssessDeliveryConfigGreaterThanOrEqualTo(String value) {
            addCriterion("self_assess_delivery_config >=", value, "selfAssessDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andSelfAssessDeliveryConfigLessThan(String value) {
            addCriterion("self_assess_delivery_config <", value, "selfAssessDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andSelfAssessDeliveryConfigLessThanOrEqualTo(String value) {
            addCriterion("self_assess_delivery_config <=", value, "selfAssessDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andSelfAssessDeliveryConfigLike(String value) {
            addCriterion("self_assess_delivery_config like", value, "selfAssessDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andSelfAssessDeliveryConfigNotLike(String value) {
            addCriterion("self_assess_delivery_config not like", value, "selfAssessDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andSelfAssessDeliveryConfigIn(List<String> values) {
            addCriterion("self_assess_delivery_config in", values, "selfAssessDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andSelfAssessDeliveryConfigNotIn(List<String> values) {
            addCriterion("self_assess_delivery_config not in", values, "selfAssessDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andSelfAssessDeliveryConfigBetween(String value1, String value2) {
            addCriterion("self_assess_delivery_config between", value1, value2, "selfAssessDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andSelfAssessDeliveryConfigNotBetween(String value1, String value2) {
            addCriterion("self_assess_delivery_config not between", value1, value2, "selfAssessDeliveryConfig");
            return (Criteria) this;
        }

        public Criteria andSecondDeliveryPlatformIsNull() {
            addCriterion("second_delivery_platform is null");
            return (Criteria) this;
        }

        public Criteria andSecondDeliveryPlatformIsNotNull() {
            addCriterion("second_delivery_platform is not null");
            return (Criteria) this;
        }

        public Criteria andSecondDeliveryPlatformEqualTo(String value) {
            addCriterion("second_delivery_platform =", value, "secondDeliveryPlatform");
            return (Criteria) this;
        }

        public Criteria andSecondDeliveryPlatformNotEqualTo(String value) {
            addCriterion("second_delivery_platform <>", value, "secondDeliveryPlatform");
            return (Criteria) this;
        }

        public Criteria andSecondDeliveryPlatformGreaterThan(String value) {
            addCriterion("second_delivery_platform >", value, "secondDeliveryPlatform");
            return (Criteria) this;
        }

        public Criteria andSecondDeliveryPlatformGreaterThanOrEqualTo(String value) {
            addCriterion("second_delivery_platform >=", value, "secondDeliveryPlatform");
            return (Criteria) this;
        }

        public Criteria andSecondDeliveryPlatformLessThan(String value) {
            addCriterion("second_delivery_platform <", value, "secondDeliveryPlatform");
            return (Criteria) this;
        }

        public Criteria andSecondDeliveryPlatformLessThanOrEqualTo(String value) {
            addCriterion("second_delivery_platform <=", value, "secondDeliveryPlatform");
            return (Criteria) this;
        }

        public Criteria andSecondDeliveryPlatformLike(String value) {
            addCriterion("second_delivery_platform like", value, "secondDeliveryPlatform");
            return (Criteria) this;
        }

        public Criteria andSecondDeliveryPlatformNotLike(String value) {
            addCriterion("second_delivery_platform not like", value, "secondDeliveryPlatform");
            return (Criteria) this;
        }

        public Criteria andSecondDeliveryPlatformIn(List<String> values) {
            addCriterion("second_delivery_platform in", values, "secondDeliveryPlatform");
            return (Criteria) this;
        }

        public Criteria andSecondDeliveryPlatformNotIn(List<String> values) {
            addCriterion("second_delivery_platform not in", values, "secondDeliveryPlatform");
            return (Criteria) this;
        }

        public Criteria andSecondDeliveryPlatformBetween(String value1, String value2) {
            addCriterion("second_delivery_platform between", value1, value2, "secondDeliveryPlatform");
            return (Criteria) this;
        }

        public Criteria andSecondDeliveryPlatformNotBetween(String value1, String value2) {
            addCriterion("second_delivery_platform not between", value1, value2, "secondDeliveryPlatform");
            return (Criteria) this;
        }

        public Criteria andTurnSecondDeliveryPlatformConditionIsNull() {
            addCriterion("turn_second_delivery_platform_condition is null");
            return (Criteria) this;
        }

        public Criteria andTurnSecondDeliveryPlatformConditionIsNotNull() {
            addCriterion("turn_second_delivery_platform_condition is not null");
            return (Criteria) this;
        }

        public Criteria andTurnSecondDeliveryPlatformConditionEqualTo(String value) {
            addCriterion("turn_second_delivery_platform_condition =", value, "turnSecondDeliveryPlatformCondition");
            return (Criteria) this;
        }

        public Criteria andTurnSecondDeliveryPlatformConditionNotEqualTo(String value) {
            addCriterion("turn_second_delivery_platform_condition <>", value, "turnSecondDeliveryPlatformCondition");
            return (Criteria) this;
        }

        public Criteria andTurnSecondDeliveryPlatformConditionGreaterThan(String value) {
            addCriterion("turn_second_delivery_platform_condition >", value, "turnSecondDeliveryPlatformCondition");
            return (Criteria) this;
        }

        public Criteria andTurnSecondDeliveryPlatformConditionGreaterThanOrEqualTo(String value) {
            addCriterion("turn_second_delivery_platform_condition >=", value, "turnSecondDeliveryPlatformCondition");
            return (Criteria) this;
        }

        public Criteria andTurnSecondDeliveryPlatformConditionLessThan(String value) {
            addCriterion("turn_second_delivery_platform_condition <", value, "turnSecondDeliveryPlatformCondition");
            return (Criteria) this;
        }

        public Criteria andTurnSecondDeliveryPlatformConditionLessThanOrEqualTo(String value) {
            addCriterion("turn_second_delivery_platform_condition <=", value, "turnSecondDeliveryPlatformCondition");
            return (Criteria) this;
        }

        public Criteria andTurnSecondDeliveryPlatformConditionLike(String value) {
            addCriterion("turn_second_delivery_platform_condition like", value, "turnSecondDeliveryPlatformCondition");
            return (Criteria) this;
        }

        public Criteria andTurnSecondDeliveryPlatformConditionNotLike(String value) {
            addCriterion("turn_second_delivery_platform_condition not like", value, "turnSecondDeliveryPlatformCondition");
            return (Criteria) this;
        }

        public Criteria andTurnSecondDeliveryPlatformConditionIn(List<String> values) {
            addCriterion("turn_second_delivery_platform_condition in", values, "turnSecondDeliveryPlatformCondition");
            return (Criteria) this;
        }

        public Criteria andTurnSecondDeliveryPlatformConditionNotIn(List<String> values) {
            addCriterion("turn_second_delivery_platform_condition not in", values, "turnSecondDeliveryPlatformCondition");
            return (Criteria) this;
        }

        public Criteria andTurnSecondDeliveryPlatformConditionBetween(String value1, String value2) {
            addCriterion("turn_second_delivery_platform_condition between", value1, value2, "turnSecondDeliveryPlatformCondition");
            return (Criteria) this;
        }

        public Criteria andTurnSecondDeliveryPlatformConditionNotBetween(String value1, String value2) {
            addCriterion("turn_second_delivery_platform_condition not between", value1, value2, "turnSecondDeliveryPlatformCondition");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}