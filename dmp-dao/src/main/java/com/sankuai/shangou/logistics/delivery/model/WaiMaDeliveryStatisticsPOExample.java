package com.sankuai.shangou.logistics.delivery.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class WaiMaDeliveryStatisticsPOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WaiMaDeliveryStatisticsPOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andLogisticsUnitIdIsNull() {
            addCriterion("logistics_unit_id is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsUnitIdIsNotNull() {
            addCriterion("logistics_unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsUnitIdEqualTo(Long value) {
            addCriterion("logistics_unit_id =", value, "logisticsUnitId");
            return (Criteria) this;
        }

        public Criteria andLogisticsUnitIdNotEqualTo(Long value) {
            addCriterion("logistics_unit_id <>", value, "logisticsUnitId");
            return (Criteria) this;
        }

        public Criteria andLogisticsUnitIdGreaterThan(Long value) {
            addCriterion("logistics_unit_id >", value, "logisticsUnitId");
            return (Criteria) this;
        }

        public Criteria andLogisticsUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("logistics_unit_id >=", value, "logisticsUnitId");
            return (Criteria) this;
        }

        public Criteria andLogisticsUnitIdLessThan(Long value) {
            addCriterion("logistics_unit_id <", value, "logisticsUnitId");
            return (Criteria) this;
        }

        public Criteria andLogisticsUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("logistics_unit_id <=", value, "logisticsUnitId");
            return (Criteria) this;
        }

        public Criteria andLogisticsUnitIdIn(List<Long> values) {
            addCriterion("logistics_unit_id in", values, "logisticsUnitId");
            return (Criteria) this;
        }

        public Criteria andLogisticsUnitIdNotIn(List<Long> values) {
            addCriterion("logistics_unit_id not in", values, "logisticsUnitId");
            return (Criteria) this;
        }

        public Criteria andLogisticsUnitIdBetween(Long value1, Long value2) {
            addCriterion("logistics_unit_id between", value1, value2, "logisticsUnitId");
            return (Criteria) this;
        }

        public Criteria andLogisticsUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("logistics_unit_id not between", value1, value2, "logisticsUnitId");
            return (Criteria) this;
        }

        public Criteria andChannelIdIsNull() {
            addCriterion("channel_id is null");
            return (Criteria) this;
        }

        public Criteria andChannelIdIsNotNull() {
            addCriterion("channel_id is not null");
            return (Criteria) this;
        }

        public Criteria andChannelIdEqualTo(Integer value) {
            addCriterion("channel_id =", value, "channelId");
            return (Criteria) this;
        }

        public Criteria andChannelIdNotEqualTo(Integer value) {
            addCriterion("channel_id <>", value, "channelId");
            return (Criteria) this;
        }

        public Criteria andChannelIdGreaterThan(Integer value) {
            addCriterion("channel_id >", value, "channelId");
            return (Criteria) this;
        }

        public Criteria andChannelIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("channel_id >=", value, "channelId");
            return (Criteria) this;
        }

        public Criteria andChannelIdLessThan(Integer value) {
            addCriterion("channel_id <", value, "channelId");
            return (Criteria) this;
        }

        public Criteria andChannelIdLessThanOrEqualTo(Integer value) {
            addCriterion("channel_id <=", value, "channelId");
            return (Criteria) this;
        }

        public Criteria andChannelIdIn(List<Integer> values) {
            addCriterion("channel_id in", values, "channelId");
            return (Criteria) this;
        }

        public Criteria andChannelIdNotIn(List<Integer> values) {
            addCriterion("channel_id not in", values, "channelId");
            return (Criteria) this;
        }

        public Criteria andChannelIdBetween(Integer value1, Integer value2) {
            addCriterion("channel_id between", value1, value2, "channelId");
            return (Criteria) this;
        }

        public Criteria andChannelIdNotBetween(Integer value1, Integer value2) {
            addCriterion("channel_id not between", value1, value2, "channelId");
            return (Criteria) this;
        }

        public Criteria andChannelPoiIdIsNull() {
            addCriterion("channel_poi_id is null");
            return (Criteria) this;
        }

        public Criteria andChannelPoiIdIsNotNull() {
            addCriterion("channel_poi_id is not null");
            return (Criteria) this;
        }

        public Criteria andChannelPoiIdEqualTo(String value) {
            addCriterion("channel_poi_id =", value, "channelPoiId");
            return (Criteria) this;
        }

        public Criteria andChannelPoiIdNotEqualTo(String value) {
            addCriterion("channel_poi_id <>", value, "channelPoiId");
            return (Criteria) this;
        }

        public Criteria andChannelPoiIdGreaterThan(String value) {
            addCriterion("channel_poi_id >", value, "channelPoiId");
            return (Criteria) this;
        }

        public Criteria andChannelPoiIdGreaterThanOrEqualTo(String value) {
            addCriterion("channel_poi_id >=", value, "channelPoiId");
            return (Criteria) this;
        }

        public Criteria andChannelPoiIdLessThan(String value) {
            addCriterion("channel_poi_id <", value, "channelPoiId");
            return (Criteria) this;
        }

        public Criteria andChannelPoiIdLessThanOrEqualTo(String value) {
            addCriterion("channel_poi_id <=", value, "channelPoiId");
            return (Criteria) this;
        }

        public Criteria andChannelPoiIdLike(String value) {
            addCriterion("channel_poi_id like", value, "channelPoiId");
            return (Criteria) this;
        }

        public Criteria andChannelPoiIdNotLike(String value) {
            addCriterion("channel_poi_id not like", value, "channelPoiId");
            return (Criteria) this;
        }

        public Criteria andChannelPoiIdIn(List<String> values) {
            addCriterion("channel_poi_id in", values, "channelPoiId");
            return (Criteria) this;
        }

        public Criteria andChannelPoiIdNotIn(List<String> values) {
            addCriterion("channel_poi_id not in", values, "channelPoiId");
            return (Criteria) this;
        }

        public Criteria andChannelPoiIdBetween(String value1, String value2) {
            addCriterion("channel_poi_id between", value1, value2, "channelPoiId");
            return (Criteria) this;
        }

        public Criteria andChannelPoiIdNotBetween(String value1, String value2) {
            addCriterion("channel_poi_id not between", value1, value2, "channelPoiId");
            return (Criteria) this;
        }

        public Criteria andNinePercentileFulfillmentDurationIsNull() {
            addCriterion("nine_percentile_fulfillment_duration is null");
            return (Criteria) this;
        }

        public Criteria andNinePercentileFulfillmentDurationIsNotNull() {
            addCriterion("nine_percentile_fulfillment_duration is not null");
            return (Criteria) this;
        }

        public Criteria andNinePercentileFulfillmentDurationEqualTo(Integer value) {
            addCriterion("nine_percentile_fulfillment_duration =", value, "ninePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andNinePercentileFulfillmentDurationNotEqualTo(Integer value) {
            addCriterion("nine_percentile_fulfillment_duration <>", value, "ninePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andNinePercentileFulfillmentDurationGreaterThan(Integer value) {
            addCriterion("nine_percentile_fulfillment_duration >", value, "ninePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andNinePercentileFulfillmentDurationGreaterThanOrEqualTo(Integer value) {
            addCriterion("nine_percentile_fulfillment_duration >=", value, "ninePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andNinePercentileFulfillmentDurationLessThan(Integer value) {
            addCriterion("nine_percentile_fulfillment_duration <", value, "ninePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andNinePercentileFulfillmentDurationLessThanOrEqualTo(Integer value) {
            addCriterion("nine_percentile_fulfillment_duration <=", value, "ninePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andNinePercentileFulfillmentDurationIn(List<Integer> values) {
            addCriterion("nine_percentile_fulfillment_duration in", values, "ninePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andNinePercentileFulfillmentDurationNotIn(List<Integer> values) {
            addCriterion("nine_percentile_fulfillment_duration not in", values, "ninePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andNinePercentileFulfillmentDurationBetween(Integer value1, Integer value2) {
            addCriterion("nine_percentile_fulfillment_duration between", value1, value2, "ninePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andNinePercentileFulfillmentDurationNotBetween(Integer value1, Integer value2) {
            addCriterion("nine_percentile_fulfillment_duration not between", value1, value2, "ninePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPointFivePercentileFulfillmentDurationIsNull() {
            addCriterion("eight_point_five_percentile_fulfillment_duration is null");
            return (Criteria) this;
        }

        public Criteria andEightPointFivePercentileFulfillmentDurationIsNotNull() {
            addCriterion("eight_point_five_percentile_fulfillment_duration is not null");
            return (Criteria) this;
        }

        public Criteria andEightPointFivePercentileFulfillmentDurationEqualTo(Integer value) {
            addCriterion("eight_point_five_percentile_fulfillment_duration =", value, "eightPointFivePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPointFivePercentileFulfillmentDurationNotEqualTo(Integer value) {
            addCriterion("eight_point_five_percentile_fulfillment_duration <>", value, "eightPointFivePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPointFivePercentileFulfillmentDurationGreaterThan(Integer value) {
            addCriterion("eight_point_five_percentile_fulfillment_duration >", value, "eightPointFivePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPointFivePercentileFulfillmentDurationGreaterThanOrEqualTo(Integer value) {
            addCriterion("eight_point_five_percentile_fulfillment_duration >=", value, "eightPointFivePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPointFivePercentileFulfillmentDurationLessThan(Integer value) {
            addCriterion("eight_point_five_percentile_fulfillment_duration <", value, "eightPointFivePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPointFivePercentileFulfillmentDurationLessThanOrEqualTo(Integer value) {
            addCriterion("eight_point_five_percentile_fulfillment_duration <=", value, "eightPointFivePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPointFivePercentileFulfillmentDurationIn(List<Integer> values) {
            addCriterion("eight_point_five_percentile_fulfillment_duration in", values, "eightPointFivePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPointFivePercentileFulfillmentDurationNotIn(List<Integer> values) {
            addCriterion("eight_point_five_percentile_fulfillment_duration not in", values, "eightPointFivePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPointFivePercentileFulfillmentDurationBetween(Integer value1, Integer value2) {
            addCriterion("eight_point_five_percentile_fulfillment_duration between", value1, value2, "eightPointFivePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPointFivePercentileFulfillmentDurationNotBetween(Integer value1, Integer value2) {
            addCriterion("eight_point_five_percentile_fulfillment_duration not between", value1, value2, "eightPointFivePercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPercentileFulfillmentDurationIsNull() {
            addCriterion("eight_percentile_fulfillment_duration is null");
            return (Criteria) this;
        }

        public Criteria andEightPercentileFulfillmentDurationIsNotNull() {
            addCriterion("eight_percentile_fulfillment_duration is not null");
            return (Criteria) this;
        }

        public Criteria andEightPercentileFulfillmentDurationEqualTo(Integer value) {
            addCriterion("eight_percentile_fulfillment_duration =", value, "eightPercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPercentileFulfillmentDurationNotEqualTo(Integer value) {
            addCriterion("eight_percentile_fulfillment_duration <>", value, "eightPercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPercentileFulfillmentDurationGreaterThan(Integer value) {
            addCriterion("eight_percentile_fulfillment_duration >", value, "eightPercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPercentileFulfillmentDurationGreaterThanOrEqualTo(Integer value) {
            addCriterion("eight_percentile_fulfillment_duration >=", value, "eightPercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPercentileFulfillmentDurationLessThan(Integer value) {
            addCriterion("eight_percentile_fulfillment_duration <", value, "eightPercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPercentileFulfillmentDurationLessThanOrEqualTo(Integer value) {
            addCriterion("eight_percentile_fulfillment_duration <=", value, "eightPercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPercentileFulfillmentDurationIn(List<Integer> values) {
            addCriterion("eight_percentile_fulfillment_duration in", values, "eightPercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPercentileFulfillmentDurationNotIn(List<Integer> values) {
            addCriterion("eight_percentile_fulfillment_duration not in", values, "eightPercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPercentileFulfillmentDurationBetween(Integer value1, Integer value2) {
            addCriterion("eight_percentile_fulfillment_duration between", value1, value2, "eightPercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andEightPercentileFulfillmentDurationNotBetween(Integer value1, Integer value2) {
            addCriterion("eight_percentile_fulfillment_duration not between", value1, value2, "eightPercentileFulfillmentDuration");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryOrderCountIsNull() {
            addCriterion("self_delivery_order_count is null");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryOrderCountIsNotNull() {
            addCriterion("self_delivery_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryOrderCountEqualTo(Integer value) {
            addCriterion("self_delivery_order_count =", value, "selfDeliveryOrderCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryOrderCountNotEqualTo(Integer value) {
            addCriterion("self_delivery_order_count <>", value, "selfDeliveryOrderCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryOrderCountGreaterThan(Integer value) {
            addCriterion("self_delivery_order_count >", value, "selfDeliveryOrderCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("self_delivery_order_count >=", value, "selfDeliveryOrderCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryOrderCountLessThan(Integer value) {
            addCriterion("self_delivery_order_count <", value, "selfDeliveryOrderCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("self_delivery_order_count <=", value, "selfDeliveryOrderCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryOrderCountIn(List<Integer> values) {
            addCriterion("self_delivery_order_count in", values, "selfDeliveryOrderCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryOrderCountNotIn(List<Integer> values) {
            addCriterion("self_delivery_order_count not in", values, "selfDeliveryOrderCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("self_delivery_order_count between", value1, value2, "selfDeliveryOrderCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeliveryOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("self_delivery_order_count not between", value1, value2, "selfDeliveryOrderCount");
            return (Criteria) this;
        }

        public Criteria andBizTimeIsNull() {
            addCriterion("biz_time is null");
            return (Criteria) this;
        }

        public Criteria andBizTimeIsNotNull() {
            addCriterion("biz_time is not null");
            return (Criteria) this;
        }

        public Criteria andBizTimeEqualTo(LocalDateTime value) {
            addCriterion("biz_time =", value, "bizTime");
            return (Criteria) this;
        }

        public Criteria andBizTimeNotEqualTo(LocalDateTime value) {
            addCriterion("biz_time <>", value, "bizTime");
            return (Criteria) this;
        }

        public Criteria andBizTimeGreaterThan(LocalDateTime value) {
            addCriterion("biz_time >", value, "bizTime");
            return (Criteria) this;
        }

        public Criteria andBizTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("biz_time >=", value, "bizTime");
            return (Criteria) this;
        }

        public Criteria andBizTimeLessThan(LocalDateTime value) {
            addCriterion("biz_time <", value, "bizTime");
            return (Criteria) this;
        }

        public Criteria andBizTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("biz_time <=", value, "bizTime");
            return (Criteria) this;
        }

        public Criteria andBizTimeIn(List<LocalDateTime> values) {
            addCriterion("biz_time in", values, "bizTime");
            return (Criteria) this;
        }

        public Criteria andBizTimeNotIn(List<LocalDateTime> values) {
            addCriterion("biz_time not in", values, "bizTime");
            return (Criteria) this;
        }

        public Criteria andBizTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("biz_time between", value1, value2, "bizTime");
            return (Criteria) this;
        }

        public Criteria andBizTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("biz_time not between", value1, value2, "bizTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}