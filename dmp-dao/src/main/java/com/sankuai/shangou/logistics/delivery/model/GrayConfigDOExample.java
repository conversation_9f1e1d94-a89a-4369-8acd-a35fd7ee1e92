package com.sankuai.shangou.logistics.delivery.model;

import java.util.ArrayList;
import java.util.List;

public class GrayConfigDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public GrayConfigDOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGrayKeyIsNull() {
            addCriterion("gray_key is null");
            return (Criteria) this;
        }

        public Criteria andGrayKeyIsNotNull() {
            addCriterion("gray_key is not null");
            return (Criteria) this;
        }

        public Criteria andGrayKeyEqualTo(String value) {
            addCriterion("gray_key =", value, "grayKey");
            return (Criteria) this;
        }

        public Criteria andGrayKeyNotEqualTo(String value) {
            addCriterion("gray_key <>", value, "grayKey");
            return (Criteria) this;
        }

        public Criteria andGrayKeyGreaterThan(String value) {
            addCriterion("gray_key >", value, "grayKey");
            return (Criteria) this;
        }

        public Criteria andGrayKeyGreaterThanOrEqualTo(String value) {
            addCriterion("gray_key >=", value, "grayKey");
            return (Criteria) this;
        }

        public Criteria andGrayKeyLessThan(String value) {
            addCriterion("gray_key <", value, "grayKey");
            return (Criteria) this;
        }

        public Criteria andGrayKeyLessThanOrEqualTo(String value) {
            addCriterion("gray_key <=", value, "grayKey");
            return (Criteria) this;
        }

        public Criteria andGrayKeyLike(String value) {
            addCriterion("gray_key like", value, "grayKey");
            return (Criteria) this;
        }

        public Criteria andGrayKeyNotLike(String value) {
            addCriterion("gray_key not like", value, "grayKey");
            return (Criteria) this;
        }

        public Criteria andGrayKeyIn(List<String> values) {
            addCriterion("gray_key in", values, "grayKey");
            return (Criteria) this;
        }

        public Criteria andGrayKeyNotIn(List<String> values) {
            addCriterion("gray_key not in", values, "grayKey");
            return (Criteria) this;
        }

        public Criteria andGrayKeyBetween(String value1, String value2) {
            addCriterion("gray_key between", value1, value2, "grayKey");
            return (Criteria) this;
        }

        public Criteria andGrayKeyNotBetween(String value1, String value2) {
            addCriterion("gray_key not between", value1, value2, "grayKey");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeIsNull() {
            addCriterion("store_operation_mode is null");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeIsNotNull() {
            addCriterion("store_operation_mode is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeEqualTo(Integer value) {
            addCriterion("store_operation_mode =", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeNotEqualTo(Integer value) {
            addCriterion("store_operation_mode <>", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeGreaterThan(Integer value) {
            addCriterion("store_operation_mode >", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("store_operation_mode >=", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeLessThan(Integer value) {
            addCriterion("store_operation_mode <", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeLessThanOrEqualTo(Integer value) {
            addCriterion("store_operation_mode <=", value, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeIn(List<Integer> values) {
            addCriterion("store_operation_mode in", values, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeNotIn(List<Integer> values) {
            addCriterion("store_operation_mode not in", values, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeBetween(Integer value1, Integer value2) {
            addCriterion("store_operation_mode between", value1, value2, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andStoreOperationModeNotBetween(Integer value1, Integer value2) {
            addCriterion("store_operation_mode not between", value1, value2, "storeOperationMode");
            return (Criteria) this;
        }

        public Criteria andGrayCityListIsNull() {
            addCriterion("gray_city_list is null");
            return (Criteria) this;
        }

        public Criteria andGrayCityListIsNotNull() {
            addCriterion("gray_city_list is not null");
            return (Criteria) this;
        }

        public Criteria andGrayCityListEqualTo(String value) {
            addCriterion("gray_city_list =", value, "grayCityList");
            return (Criteria) this;
        }

        public Criteria andGrayCityListNotEqualTo(String value) {
            addCriterion("gray_city_list <>", value, "grayCityList");
            return (Criteria) this;
        }

        public Criteria andGrayCityListGreaterThan(String value) {
            addCriterion("gray_city_list >", value, "grayCityList");
            return (Criteria) this;
        }

        public Criteria andGrayCityListGreaterThanOrEqualTo(String value) {
            addCriterion("gray_city_list >=", value, "grayCityList");
            return (Criteria) this;
        }

        public Criteria andGrayCityListLessThan(String value) {
            addCriterion("gray_city_list <", value, "grayCityList");
            return (Criteria) this;
        }

        public Criteria andGrayCityListLessThanOrEqualTo(String value) {
            addCriterion("gray_city_list <=", value, "grayCityList");
            return (Criteria) this;
        }

        public Criteria andGrayCityListLike(String value) {
            addCriterion("gray_city_list like", value, "grayCityList");
            return (Criteria) this;
        }

        public Criteria andGrayCityListNotLike(String value) {
            addCriterion("gray_city_list not like", value, "grayCityList");
            return (Criteria) this;
        }

        public Criteria andGrayCityListIn(List<String> values) {
            addCriterion("gray_city_list in", values, "grayCityList");
            return (Criteria) this;
        }

        public Criteria andGrayCityListNotIn(List<String> values) {
            addCriterion("gray_city_list not in", values, "grayCityList");
            return (Criteria) this;
        }

        public Criteria andGrayCityListBetween(String value1, String value2) {
            addCriterion("gray_city_list between", value1, value2, "grayCityList");
            return (Criteria) this;
        }

        public Criteria andGrayCityListNotBetween(String value1, String value2) {
            addCriterion("gray_city_list not between", value1, value2, "grayCityList");
            return (Criteria) this;
        }

        public Criteria andGrayStoreListIsNull() {
            addCriterion("gray_store_list is null");
            return (Criteria) this;
        }

        public Criteria andGrayStoreListIsNotNull() {
            addCriterion("gray_store_list is not null");
            return (Criteria) this;
        }

        public Criteria andGrayStoreListEqualTo(String value) {
            addCriterion("gray_store_list =", value, "grayStoreList");
            return (Criteria) this;
        }

        public Criteria andGrayStoreListNotEqualTo(String value) {
            addCriterion("gray_store_list <>", value, "grayStoreList");
            return (Criteria) this;
        }

        public Criteria andGrayStoreListGreaterThan(String value) {
            addCriterion("gray_store_list >", value, "grayStoreList");
            return (Criteria) this;
        }

        public Criteria andGrayStoreListGreaterThanOrEqualTo(String value) {
            addCriterion("gray_store_list >=", value, "grayStoreList");
            return (Criteria) this;
        }

        public Criteria andGrayStoreListLessThan(String value) {
            addCriterion("gray_store_list <", value, "grayStoreList");
            return (Criteria) this;
        }

        public Criteria andGrayStoreListLessThanOrEqualTo(String value) {
            addCriterion("gray_store_list <=", value, "grayStoreList");
            return (Criteria) this;
        }

        public Criteria andGrayStoreListLike(String value) {
            addCriterion("gray_store_list like", value, "grayStoreList");
            return (Criteria) this;
        }

        public Criteria andGrayStoreListNotLike(String value) {
            addCriterion("gray_store_list not like", value, "grayStoreList");
            return (Criteria) this;
        }

        public Criteria andGrayStoreListIn(List<String> values) {
            addCriterion("gray_store_list in", values, "grayStoreList");
            return (Criteria) this;
        }

        public Criteria andGrayStoreListNotIn(List<String> values) {
            addCriterion("gray_store_list not in", values, "grayStoreList");
            return (Criteria) this;
        }

        public Criteria andGrayStoreListBetween(String value1, String value2) {
            addCriterion("gray_store_list between", value1, value2, "grayStoreList");
            return (Criteria) this;
        }

        public Criteria andGrayStoreListNotBetween(String value1, String value2) {
            addCriterion("gray_store_list not between", value1, value2, "grayStoreList");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(Long value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(Long value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(Long value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(Long value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(Long value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<Long> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<Long> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(Long value1, Long value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(Long value1, Long value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}