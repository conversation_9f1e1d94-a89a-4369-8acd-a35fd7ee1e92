package com.sankuai.shangou.logistics.delivery.mapper;

import com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryLoadPO;
import com.sankuai.shangou.logistics.delivery.model.WaiMaDeliveryLoadPOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WaiMaDeliveryLoadPOMapper {
    long countByExample(WaiMaDeliveryLoadPOExample example);

    int deleteByExample(WaiMaDeliveryLoadPOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(WaiMaDeliveryLoadPO record);

    int insertSelective(WaiMaDeliveryLoadPO record);

    List<WaiMaDeliveryLoadPO> selectByExample(WaiMaDeliveryLoadPOExample example);

    WaiMaDeliveryLoadPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") WaiMaDeliveryLoadPO record, @Param("example") WaiMaDeliveryLoadPOExample example);

    int updateByExample(@Param("record") WaiMaDeliveryLoadPO record, @Param("example") WaiMaDeliveryLoadPOExample example);

    int updateByPrimaryKeySelective(WaiMaDeliveryLoadPO record);

    int updateByPrimaryKey(WaiMaDeliveryLoadPO record);

    int batchInsert(@Param("list") List<WaiMaDeliveryLoadPO> list);
}