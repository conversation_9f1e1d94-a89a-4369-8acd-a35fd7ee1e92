package com.sankuai.shangou.logistics.delivery.mapper;

import com.sankuai.shangou.logistics.delivery.model.ShippingAreaRelationPO;
import com.sankuai.shangou.logistics.delivery.model.ShippingAreaRelationPOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ShippingAreaRelationPOMapper {
    long countByExample(ShippingAreaRelationPOExample example);

    int deleteByExample(ShippingAreaRelationPOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ShippingAreaRelationPO record);

    int insertSelective(ShippingAreaRelationPO record);

    List<ShippingAreaRelationPO> selectByExample(ShippingAreaRelationPOExample example);

    ShippingAreaRelationPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ShippingAreaRelationPO record, @Param("example") ShippingAreaRelationPOExample example);

    int updateByExample(@Param("record") ShippingAreaRelationPO record, @Param("example") ShippingAreaRelationPOExample example);

    int updateByPrimaryKeySelective(ShippingAreaRelationPO record);

    int updateByPrimaryKey(ShippingAreaRelationPO record);

    int batchInsert(@Param("list") List<ShippingAreaRelationPO> list);
}