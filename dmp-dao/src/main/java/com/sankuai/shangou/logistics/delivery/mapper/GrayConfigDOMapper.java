package com.sankuai.shangou.logistics.delivery.mapper;

import com.sankuai.shangou.logistics.delivery.model.GrayConfigDO;
import com.sankuai.shangou.logistics.delivery.model.GrayConfigDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GrayConfigDOMapper {
    long countByExample(GrayConfigDOExample example);

    int deleteByExample(GrayConfigDOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(GrayConfigDO record);

    int insertSelective(GrayConfigDO record);

    List<GrayConfigDO> selectByExample(GrayConfigDOExample example);

    GrayConfigDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") GrayConfigDO record, @Param("example") GrayConfigDOExample example);

    int updateByExample(@Param("record") GrayConfigDO record, @Param("example") GrayConfigDOExample example);

    int updateByPrimaryKeySelective(GrayConfigDO record);

    int updateByPrimaryKey(GrayConfigDO record);

    int batchInsert(@Param("list") List<GrayConfigDO> list);
}