package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.bo.ReturnDataPoiBo;
import com.sankuai.shangou.qnh.orderapi.domain.result.ReturnDataSecurity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Objects;


@Data
@ToString
@TypeDoc(
        description = "超时订单对象"
)
public class OrderTrackVO implements ReturnDataSecurity {


    @FieldDoc(
            description = "轨迹"
    )
    @ApiModelProperty(value = "轨迹", required = true)
    private List<OrderTrackDetailVO> orderOperatorLogList;

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private Long poiId;

    @FieldDoc(
            description = "转单门店ID"
    )
    @ApiModelProperty(value = "转单门店ID", required = true)
    private Long dispatchShopId;

    @Override
    public List<ReturnDataPoiBo> fetchReturnDataPoiBoList() {
        if (Objects.nonNull(this.poiId)) {
            ReturnDataPoiBo poiBo = ReturnDataPoiBo.builder().poiId(this.poiId).dispatchShopId(this.dispatchShopId)
                    .build();
            return Lists.newArrayList(poiBo);
        }
        return null;
    }

}
