package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.shangou.qnh.orderapi.domain.request.pc.ApproveRefundRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/2 17:05
 * @Description:
 */
@Setter
@Getter
@NoArgsConstructor
@ToString
@EqualsAndHashCode
public class ApproveRefundBO {

    private String orderId;

    private boolean agree;

    private Long tenantId;

    private Long operatorId;

    private String operator;

    private String reason;

    private int channelId;

    private int refundTagId;

    private String serviceId;

    private Integer afsApplyType;

    public ApproveRefundBO(ApproveRefundRequest request) {
        orderId = request.getOrderId();
        agree = StringUtils.equalsIgnoreCase("y", request.getDecision());
        reason = request.getNote();
        serviceId = request.getServiceId();
        afsApplyType = ConverterUtils.nonNullConvert(request.getAfsApplyType(), Integer::valueOf, 0);
    }
}
