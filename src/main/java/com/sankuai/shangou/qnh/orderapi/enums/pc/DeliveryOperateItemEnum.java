package com.sankuai.shangou.qnh.orderapi.enums.pc;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/7
 */
public enum DeliveryOperateItemEnum {

    //1取消2转自配送3已送出4联系骑手
    MANUAL_THIRD_PART(101, "手动发三方配送", "发三方配送"),
    RETRY_LAUNCH(102, "重发配送", "重发配送"),
    EXCEPTION_TO_THIRD_PART(103, "平台配送异常转三方配送", "发三方配送"),
    EXCEPTION_TO_SELF(104, "平台配送异常转商家自配送", "自己送"),

    RETRY_LAUNCH_BY_MALTFARM(105, "麦芽田重发配送", "重发配送"),
    RETRY_LAUNCH_BY_HAIKUI(106, "海葵重发配送", "重发配送"),

    DELIVERY_TO_FOUR_WHEEL(117,"转汽车配送","转汽车配送"),
    APPEND_FOUR_WHEEL_TYPE(118,"追加车型","追加汽车配送车型"),


    CANCELING(900, "取消中", "取消中"),
    CANCEL_DELIVERY(901, "取消配送", "取消配送");


    /**
     * code
     */
    public final Integer type;
    /**
     * 实际的场景
     */
    public final String scene;
    /**
     * 按钮描述
     */
    public final String iconDesc;

    DeliveryOperateItemEnum(Integer type, String scene, String iconDesc) {
        this.type = type;
        this.scene = scene;
        this.iconDesc = iconDesc;
    }
}

