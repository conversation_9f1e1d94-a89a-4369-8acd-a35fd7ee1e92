package com.sankuai.shangou.qnh.orderapi.enums.pc;

import org.apache.commons.lang3.StringUtils;

public enum TenantStockHoldingCostTypeEnum {

	/**
	 * 固定成本，默认供货关系采购价
	 */
	FIXED("fixed"),

	/**
	 * 加权平均成本
	 */
	WEIGHTED("Weighted");

	public static final int CONFIG_ID = 39;
	public static final String CONFIG_KEY = "calcType";
	private final String code;

	TenantStockHoldingCostTypeEnum(String code) {
		this.code = code;
	}

	public String getCode() {
		return code;
	}

	public static TenantStockHoldingCostTypeEnum enumOf(String code) {
		if (StringUtils.isBlank(code)) {
			return null;
		}

		for (TenantStockHoldingCostTypeEnum each : values()) {
			if (StringUtils.equals(each.getCode(), code)) {
				return each;
			}
		}

		return null;
	}
}
