/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.shangou.qnh.orderapi.enums.store;

/**
 * 首页模块角标类型
 * <br><br>
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <br>
 * Date: 2019-06-03 Time: 11:01
 */
public enum PendingTaskType {

    NUMBER(0, "数字角标"),
    RED_DOT(1, "红点角标"),
    ;

    public final int type;
    public final String desc;

    PendingTaskType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
