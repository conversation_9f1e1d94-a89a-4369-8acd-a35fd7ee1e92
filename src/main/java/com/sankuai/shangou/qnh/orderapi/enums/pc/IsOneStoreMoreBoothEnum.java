package com.sankuai.shangou.qnh.orderapi.enums.pc;

public enum IsOneStoreMoreBoothEnum {
    UN_KNOW(-1, "未知状态"),
    NO_ONE_STORE_MORE_BOOTH(0, "非一门店多摊位租户"),
    IS_ONE_STORE_MORE_BOOTH(1, "一门店多摊位租户");

    private int value;
    private String desc;

    private IsOneStoreMoreBoothEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return this.value;
    }

    public String getDesc() {
        return this.desc;
    }
}
