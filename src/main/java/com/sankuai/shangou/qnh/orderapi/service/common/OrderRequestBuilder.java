package com.sankuai.shangou.qnh.orderapi.service.common;

import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSRevenueListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderListReq;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.request.revenue.OCMSListOrderRevenueDetailRequest;
import com.meituan.shangou.saas.order.management.client.enums.SortByEnum;
import com.meituan.shangou.saas.order.management.client.enums.SortFieldEnum;
import com.meituan.shangou.saas.order.management.client.enums.WaitToDeliveryOrderSubTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.FuseOrderStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.User;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderListRequest;
import com.sankuai.shangou.qnh.orderapi.utils.app.DeliveryChannelUtils;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.TimeUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/7/16
 **/
@Component
public class OrderRequestBuilder {
    private static final long MIN_VALID_TIMESTAMP = 1000 + 1;//订单系统时间的出事值是1000

    private OCMSListOrderRevenueDetailRequest buildOCMSListOrderRequestBasic(Long tenantId, List<Long> storeIdList, Integer page, Integer size, Integer entityType) {
        OCMSListOrderRevenueDetailRequest ocmsListOrderRequest = new OCMSListOrderRevenueDetailRequest();
        ocmsListOrderRequest.setTenantId(tenantId);
        ocmsListOrderRequest.setShopIdList(storeIdList);
        if (Objects.equals(PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code(), entityType)) {
            ocmsListOrderRequest.setShopIdList(Lists.newArrayList());
            ocmsListOrderRequest.setWarehouseIdList(storeIdList);
        }
        //查询当前时间之前7天的订单
        ocmsListOrderRequest.setBeginCreateTime(TimeUtils.getBeforeDayTimeStamp(MccConfigUtil.queryOrderCreateTimeBefore()));
        ocmsListOrderRequest.setEndCreateTime(System.currentTimeMillis());
        ocmsListOrderRequest.setPage(page);
        ocmsListOrderRequest.setSize(size);
        return ocmsListOrderRequest;
    }

    public OCMSListOrderRevenueDetailRequest queryWaitConfirmOrderRequest(OrderListRequestContext request) {
        OCMSListOrderRevenueDetailRequest ocmsListOrderRequest = buildOCMSListOrderRequestBasic(request.getTenantId(), request.getStoreIdList(), request.getPage(), request.getSize(), request.getEntityType());
        ocmsListOrderRequest.setFuseOrderStatusList(Lists.newArrayList(FuseOrderStatusEnum.SUBMIT.getValue()));
        ocmsListOrderRequest.setSortField(SortFieldEnum.ORDER_CREATE_TIME);
        ocmsListOrderRequest.setSort(SortByEnum.ASC);
        ocmsListOrderRequest.setIncludeRevenueDetail(request.isShowSalePrice());
        return ocmsListOrderRequest;
    }

    public OCMSListOrderRevenueDetailRequest queryWaitSelfFetchOrderRequest(OrderListRequestContext request) {
        OCMSListOrderRevenueDetailRequest ocmsListOrderRequest = buildOCMSListOrderRequestBasic(request.getTenantId(), request.getStoreIdList(), request.getPage(), request.getSize(), request.getEntityType());
        ocmsListOrderRequest.setViewFuseOrderStatusList(Lists.newArrayList(FuseOrderStatusEnum.WAIT_SELF_FETCH.getValue()));
        ocmsListOrderRequest.setSortField(SortFieldEnum.ORDER_CREATE_TIME);
        ocmsListOrderRequest.setSort(SortByEnum.ASC);
        ocmsListOrderRequest.setIncludeRevenueDetail(request.isShowSalePrice());
        return ocmsListOrderRequest;
    }

    public OCMSListOrderRevenueDetailRequest buildWaitToDeliveryOCMSListOrderRequest(OrderListRequestContext request) {
        List<Long> storeIdList = request.getStoreIdList();
        OCMSListOrderRevenueDetailRequest ocmsListOrderRequest = buildOCMSListOrderRequestBasic(request.getTenantId(), storeIdList, request.getPage(), request.getSize(), request.getEntityType());
        //拣货已完成，且等待分配骑手
        ocmsListOrderRequest.setViewFuseOrderStatusList(Lists.newArrayList(
                FuseOrderStatusEnum.PICKED.getValue(),
                FuseOrderStatusEnum.SHIPPING.getValue()));//查询退款处理中的订单

        if (Objects.nonNull(request.getSubType())){
            WaitToDeliveryOrderSubTypeEnum waitToDeliveryOrderSubTypeEnum = WaitToDeliveryOrderSubTypeEnum.valueOf(request.getSubType());
            if (Objects.nonNull(waitToDeliveryOrderSubTypeEnum)) {
                ocmsListOrderRequest.setDeliveryStatusList(buildDistributeStatusList(waitToDeliveryOrderSubTypeEnum.getDistributeStatusList()));
            }
        }
        //订单未完成过，已完成的订单也能发起退款，如果不指定该条件，可能会查出该类订单
        ocmsListOrderRequest.setEndCompletedTime(MIN_VALID_TIMESTAMP);
        ocmsListOrderRequest.setSortField(SortFieldEnum.ESTIMATED_ARRIVAL_TIME);
        ocmsListOrderRequest.setSort(SortByEnum.ASC);
        ocmsListOrderRequest.setIncludeRevenueDetail(request.isShowSalePrice());
        // 配送到家
//        ocmsListOrderRequest.setDistributeMethodList(ImmutableList.of(DistributeMethodEnum.HOME_DELIVERY.getValue()));
        return ocmsListOrderRequest;
    }

    private List<Integer> buildDistributeStatusList(List<DistributeStatusEnum> distributeStatusEnumList) {
        return Optional.ofNullable(distributeStatusEnumList)
                .map(List::stream)
                .orElse(Stream.empty())
                .map(distributeStatusEnum -> distributeStatusEnum.getValue())
                .collect(Collectors.toList());
    }

    public  com.sankuai.meituan.reco.pickselect.query.thrift.fulfill.request.PageQueryFulfillingOrderRequest buildPageQueryFulfillingOrderRequest(Long tenantId, Long storeId, Integer page, Integer size) {
        com.sankuai.meituan.reco.pickselect.query.thrift.fulfill.request.PageQueryFulfillingOrderRequest pageQueryFulfillingOrderRequest = new com.sankuai.meituan.reco.pickselect.query.thrift.fulfill.request.PageQueryFulfillingOrderRequest();
        pageQueryFulfillingOrderRequest.setEmpowerStoreId(storeId);
        pageQueryFulfillingOrderRequest.setTenantId(tenantId);
        pageQueryFulfillingOrderRequest.setPageNo(page);
        pageQueryFulfillingOrderRequest.setPageSize(size);
        return pageQueryFulfillingOrderRequest;
    }


    public OCMSRevenueListViewIdConditionRequest buildOCMSListByViewOrderIdRequest(Long tenantId, List<ViewIdCondition> orders, boolean includeRevenueDetail) {
        OCMSRevenueListViewIdConditionRequest request = new OCMSRevenueListViewIdConditionRequest();
        request.setViewIdConditionList(orders);
        request.setTenantId(tenantId);
        request.setSortField(SortFieldEnum.ESTIMATED_ARRIVAL_TIME);
        request.setSort(SortByEnum.ASC);
        request.setIncludeRevenueDetail(includeRevenueDetail);
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (identityInfo.getUser() != null){
            request.setTenantId(identityInfo.getUser().getTenantId());
        }
        return request;
    }

    public OCMSListViewIdConditionRequest buildOCMSListByViewOrderIdRequest(Long tenantId, List<ViewIdCondition> orders) {
        OCMSRevenueListViewIdConditionRequest request = new OCMSRevenueListViewIdConditionRequest();
        request.setViewIdConditionList(orders);
        request.setSortField(SortFieldEnum.ESTIMATED_ARRIVAL_TIME);
        request.setSort(SortByEnum.ASC);
        request.setTenantId(tenantId);

        return request;
    }


    public OcmsOrderListReq buildOcmsOrderListReq(OrderListRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OcmsOrderListReq tRequest = new OcmsOrderListReq();
        tRequest.setTenantId(user.getTenantId());
        if (Objects.isNull(request.getEntityType()) || request.getEntityType() == PoiEntityTypeEnum.STORE.code()) {
            tRequest.setShopId(request.getStoreId());
        } else  if (request.getEntityType() == PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code()){
            tRequest.setWarehouseIdList(Lists.newArrayList(request.getStoreId()));
        }
        tRequest.setOperator(user.getAccountId());
        // 订单创建时间范围
        tRequest.setBeginCreateTime(LocalDate.parse(request.getBeginCreateDate()).atStartOfDay(ZoneId.systemDefault())
                .toInstant().toEpochMilli());
        tRequest.setEndCreateTime(LocalDate.parse(request.getEndCreateDate()).plusDays(1L).atStartOfDay(ZoneId.systemDefault())
                .toInstant().toEpochMilli());
        // 订单渠道范围
        tRequest.setChannelIdList(request.getChannelIdList());
        // 配送订单类型
        if (Objects.nonNull(request.getDeliveryOrderType())) {
            tRequest.setDeliveryOrderType(request.getDeliveryOrderType());
        }
        // 聚合订单状态
        tRequest.setAggregationOrderStatus(request.getAggregationOrderStatus());

        // 设置搜索关键词
        tRequest.setSmartQuery(request.getKeyword());

        tRequest.setPage(request.getPage());
        tRequest.setPageSize(request.getPageSize());
        // 和deliveryOrderType互斥 如果同时传递，会取deliveryOrderType字段的数据查询
        if(Objects.isNull(request.getDeliveryOrderType()) && CollectionUtils.isNotEmpty(request.getDeliveryOrderTypeList())){
            tRequest.setDeliveryOrderTypeList(request.getDeliveryOrderTypeList());
        }
        if(CollectionUtils.isNotEmpty(request.getDistributeTypeList())){
            tRequest.setDistributeTypeList(request.getDistributeTypeList());
        }
        if(Objects.nonNull(request.getDeliveryChannelType())) {
            tRequest.setDeliveryChannelTypeList(DeliveryChannelUtils.getDeliveryChannelIdList(request.getDeliveryChannelType()));
        }
        return tRequest;
    }

}
