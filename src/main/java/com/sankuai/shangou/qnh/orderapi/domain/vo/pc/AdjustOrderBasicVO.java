package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: shihuifeng
 * @Date: 2019/4/16
 * @Description: 调整订单所展示的的基本订单信息
 */

@TypeDoc(
        description = "调整订单所展示的的基本订单信息"
)
@ApiModel("调整订单所展示的的基本订单信息")
@Data
public class AdjustOrderBasicVO {

    @FieldDoc(
            description = "订单号"
    )
    @ApiModelProperty(value = "订单号", required = true)
    private String orderId;


    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id", required = true)
    private String poiId;


    @FieldDoc(
            description = "门店名称"
    )
    @ApiModelProperty(value = "门店名称", required = true)
    private String poiName;


    @FieldDoc(
            description = "渠道名称"
    )
    @ApiModelProperty(value = "渠道名称", required = true)
    private String channelName;

    @FieldDoc(
            description = "可操作列表(10接单 20完成拣货 30补打小票 40全单退款 50部分退款 60接收退款商品 70退差价 80订单调整)"
    )
    private List<String> couldOperateItemList;


    @FieldDoc(
            description = "商品列表"
    )
    @ApiModelProperty(value = "商品列表", required = true)
    private List<ItemBasicInfoVO> itemBasicInfoVOList;


    @TypeDoc(
            description = "商品项基本信息"
    )
    @ApiModel("商品项基本信息")
    @Data
    public static class ItemBasicInfoVO {

        @FieldDoc(
                description = "商品项id"
        )
        @ApiModelProperty(value = "商品项id", required = true)
        private String orderItemId;


        @FieldDoc(
                description = "商品名称"
        )
        @ApiModelProperty(value = "商品名称", required = true)
        private String skuName;


        @FieldDoc(
                description = "商品sku编码"
        )
        @ApiModelProperty(value = "商品sku编码", required = true)
        private String sku;


        @FieldDoc(
                description = "规格"
        )
        @ApiModelProperty(value = "规格", required = true)
        private String spec;


        @FieldDoc(
                description = "单价"
        )
        @ApiModelProperty(value = "单价", required = true)
        private String unitPrice;


        @FieldDoc(
                description = "购买数量"
        )
        @ApiModelProperty(value = "购买数量", required = true)
        private String quantity;


        @FieldDoc(
                description = "摊位id", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(value = "摊位id")
        private String boothId;


        @FieldDoc(
                description = "商品线下售价", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(value = "商品线下售价")
        private String offlinePrice;

        @FieldDoc(
                description = "上次更新时间"
        )
        @ApiModelProperty(value = "上次更新时间", required = true)
        private String updateTime;

        @FieldDoc(
                description = "商品结算类型,1后结，2现结(外采)"
        )
        @ApiModelProperty(value = "商品结算类型", required = true)
        private String settleType;

    }


}
