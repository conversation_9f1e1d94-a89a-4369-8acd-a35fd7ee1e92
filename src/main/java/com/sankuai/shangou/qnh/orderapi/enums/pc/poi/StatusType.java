package com.sankuai.shangou.qnh.orderapi.enums.pc.poi;

public enum StatusType {
    INVALID(0, false, "删除"),
    VALID(1, true, "正常");
    private int value;
    private boolean flag;
    private String desc;
    StatusType(int value, boolean flag, String desc) {
        this.value = value;
        this.flag = flag;
        this.desc = desc;
    }
    public int getValue() {
        return value;
    }
    public String getDesc() {
        return desc;
    }
    public boolean getFlag() {
        return flag;
    }
}