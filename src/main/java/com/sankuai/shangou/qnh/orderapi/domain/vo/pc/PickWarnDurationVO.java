package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@TypeDoc(
        description = "拣货持续时长"
)
@ApiModel("拣货持续时长")
@Data
public class PickWarnDurationVO {


    @FieldDoc(
            description = "超时时间配置"
    )
    @ApiModelProperty(name = "超时时间配置", required = true)
    public Integer warnDuration;

    @FieldDoc(
            description = "下发时间"
    )
    @ApiModelProperty(name = "下发时间", required = true)
    public Long pushTaskTime;

    @FieldDoc(
            description = "送达时间"
    )
    @ApiModelProperty(name = "送达时间", required = true)
    public Long deliverTime;

    @FieldDoc(
            description = "是否为预订单"
    )
    @ApiModelProperty(name = "是否为预订单", required = true)
    public Boolean isReserved = false;


    @FieldDoc(
            description = "当前时间"
    )
    @ApiModelProperty(name = "当前时间", required = true)
    public Long currentTime;

    @FieldDoc(
            description = "拣货截止时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货截止时间", required = false)
    private Long pickTimeOutTime;



}
