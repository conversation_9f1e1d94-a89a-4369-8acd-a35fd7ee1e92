package com.sankuai.shangou.qnh.orderapi.service.common.query;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.util.MetricHelper;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSQueryOrderQuantityRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSRevenueListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListViewIdConditionResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSQueryOrderQuantityResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSWaitAuditOrderVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OnlineBaseOrderVO;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderAmountInfo;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderRevenueDetailResponse;
import com.meituan.shangou.saas.order.management.client.enums.PriceDisplayType;
import com.meituan.shangou.saas.order.management.client.enums.QueryOrderTypeQuantityEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;

import com.meituan.shangou.saas.order.management.client.service.revenue.MerChantRevenueQueryService;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderLabelModel;
import com.meituan.shangou.saas.order.platform.enums.LabelSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.LabelTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderCouldOperateItem;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEntityEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.RefundApplyListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.*;
import com.sankuai.shangou.qnh.orderapi.enums.app.AuthCodeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.OrderViewStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pda.OrderTabSubTypeEnum;
import com.sankuai.shangou.qnh.orderapi.remote.AuthRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.OrderLabelRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.SaasCrmDataRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.app.AppendDeliveryInfoService;
import com.sankuai.shangou.qnh.orderapi.service.common.*;
import com.sankuai.shangou.qnh.orderapi.service.store.OrderVoService;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.TimeUtils;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/7/17
 **/
@Service
@Slf4j
public class QueryOrderService {
    @Resource
    private MerChantRevenueQueryService merChantRevenueQueryService;
    @Resource
    private PrintService printService;
    @Resource
    private OrderProfitService orderProfitService;
    @Resource
    private OrderOperateItemsService orderOperateItemsService;
    @Resource
    private OrderVoConvertUtil orderVoConvertUtil;
    @Resource
    private OrderListResonseBuilder orderListResonseBuilder;
    @Resource
    private OrderTransferService orderTransferService;
    @Resource
    private DeliveryService deliveryService;
    @Resource
    private OrderRequestBuilder orderRequestBuilder;
    @Resource
    private AuthRemoteService authThriftWrapper;
    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;
    @Resource
    private OrderVoService orderVoService;

    @Resource(name = "appDeliveryInfoService")
    private AppendDeliveryInfoService appendDeliveryInfoService;

    @Resource
    private ComposeProductService composeProductService;

    @Resource
    private OrderLabelRemoteService orderLabelRemoteService;

    private static final String RHINO_KEY = "orderapi_query_parallel_task";

    private static final String THREAD_NAME_SUFFIX = "_thread-%d";


    protected boolean supportMultiStore(){
        return true;
    }

    private static final Integer CORE_THREAD_SIZE = 50;

    private static final Integer MAX_THREAD_SIZE = 50;

    private static boolean parallelSwitchClose = false;

    private static final ThreadPool THREAD_POOL =  Rhino.newThreadPool(RHINO_KEY, DefaultThreadPoolProperties.Setter()
            .withCoreSize(CORE_THREAD_SIZE)
            .withMaxSize(MAX_THREAD_SIZE)
            .withMaxQueueSize(50)
            .withRejectHandler(new CallerRunsPolicy())
            .withThreadFactory(new ThreadFactoryBuilder()
                    .setNameFormat(RHINO_KEY + THREAD_NAME_SUFFIX)
                    .setUncaughtExceptionHandler(new CustomUncaughtExceptionHandler())
                    .build())
    );
    public static class CallerRunsPolicy implements RejectedExecutionHandler {
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            parallelSwitchClose = true;
            log.error("订单列表并行查询线程池已满, 自动切换为串行执行");
            r.run();
        }
    }

        public static void execute(Runnable runnable) {
        THREAD_POOL.execute(runnable);
    }



    public static class CustomUncaughtExceptionHandler implements Thread.UncaughtExceptionHandler {
        @Override
        public void uncaughtException(Thread t, Throwable e) {
            //自定义异常处理逻辑
            log.error("orderapi-query处理线程池[orderapi_query_parallel_task]，任务：[{}]遇到未捕获异常", t.getName(), e);
        }
    }

    protected boolean supportParallelQuery(){
        return true;
    }

    protected void checkOrderListRequest(OrderListRequestContext request){
        boolean multiStore = CollectionUtils.isNotEmpty(request.getStoreIdList()) && request.getStoreIdList().size() > 1;
        if (CollectionUtils.isEmpty(request.getStoreIdList())){
            log.error("门店为空：request={}", request);
            throw new BizException(ResultCodeEnum.FAIL.getCode(), "门店为空");
        }
        if (multiStore && !supportMultiStore()) {
            log.error("订单展示只支持单门店/仓模式：storeIdList={}", request.getStoreIdList());
            throw new BizException(ResultCodeEnum.FAIL.getCode(), "只支持单门店模式");
        }
    }

    public OrderListResponse queryOrderList(OrderListRequestContext request) {
        initOrderListRequestContext(request);
        checkOrderListRequest(request);
        Pair<List<OCMSOrderVO>, PageInfoVO> listPageInfoVOPair = queryOrderInfo(request);
        List<OrderLabelModel> showLabelList = queryOrderShowLabel(request.getTenantId());
        OrderListResponse orderListResponse = buildOrderListResponse(request, listPageInfoVOPair, showLabelList);
        addExtraInfo(orderListResponse, request);
        return orderListResponse;
    }

    protected void initOrderListRequestContext(OrderListRequestContext request) {
        Map<String, Boolean> permissions = authThriftWrapper.isHasPermission(ImmutableList.of(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode(), AuthCodeEnum.MALT_FARM.getAuthCode()));
        boolean showSalePrice = permissions.getOrDefault(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode(), Boolean.FALSE);
        request.setShowSalePrice(showSalePrice);
        request.setHasMaltFarmPermission(permissions.getOrDefault(AuthCodeEnum.MALT_FARM.getAuthCode(), Boolean.FALSE));
    }

    protected RefundApplyListResponse queryAfterSaleOrderList(OrderListRequestContext request) {
        throw new UnsupportedOperationException();
    }

    protected void addExtraInfo(OrderListResponse orderListResponse, OrderListRequestContext request) {

    }

    protected RefundApplyListResponse buildRefundApplyListResponse(OrderListRequestContext request, Pair<List<OCMSWaitAuditOrderVO>, PageInfoVO> pair, List<OrderLabelModel> showLabelList) {
        if (CollectionUtils.isEmpty(pair.getKey())) {
            return emptyRefundApplyListResponse(pair.getValue());
        }
        // 设置退款展示类型
        orderVoConvertUtil.setRefundPriceDisplayType4WaitAuditOrderList(pair.getKey(),
                request.isShowSalePrice() ? PriceDisplayType.REFUND_AMOUNT.getCode() : PriceDisplayType.OFFLINE_PRICE.getCode());
        RefundApplyListResponse orderListResponse;
        if (supportParallelQuery() && parallelQuerySwitch(request.getIdentityInfo())){
            orderListResponse = parallelBuildRefundApplyListResponse(request, pair, showLabelList);
        }else {
            orderListResponse = syncBuildRefundApplyListResponse(request, pair, showLabelList);
        }
        //数据去重
        uniqueOrder(orderListResponse);
        return orderListResponse;
    }

    private void uniqueOrder(RefundApplyListResponse orderListResponse) {
        if (CollectionUtils.isEmpty(orderListResponse.getRefundApplyRecordVOList())){
            return;
        }
        List<RefundApplyRecordVO> applyRecordVOS = Lists.newArrayList();
        List<String> channelIds = Lists.newArrayList();
        for (RefundApplyRecordVO refundApplyRecordVO : orderListResponse.getRefundApplyRecordVOList()){
            if (channelIds.contains(refundApplyRecordVO.getOrderVO().getChannelOrderId())){
                continue;
            }
            channelIds.add(refundApplyRecordVO.getOrderVO().getChannelOrderId());
            applyRecordVOS.add(refundApplyRecordVO);
        }
        orderListResponse.setRefundApplyRecordVOList(applyRecordVOS);

    }

    private void fillRefundOrderPrintAndDeliveryInfo(List<RefundApplyRecordVO> refundApplyRecordVOList, OrderListRequestContext request){
        fillRefundOrderPrintInfoAndPrintTimes(refundApplyRecordVOList, request);
        fillRefundPickerNamesInfo(refundApplyRecordVOList);
        fillRefundDeliveryInfo(refundApplyRecordVOList, request);
    }

    private void fillRefundOrderOperateAndProfitInfo(RefundApplyListResponse refundApplyListResponse, OrderListRequestContext request, List<OCMSOrderVO> ocmsOrderVOList ){
        fillRefundOrderProfitInfo(refundApplyListResponse.getRefundApplyRecordVOList(), ocmsOrderVOList, request);
        orderOperateItemsService.setCouldOperateItems(refundApplyListResponse);
        composeProductService.dealRefundComposeProduct(refundApplyListResponse.getRefundApplyRecordVOList());
        orderVoConvertUtil.setRefundApplyListResponseViewStatus(refundApplyListResponse);
    }

    private RefundApplyListResponse parallelBuildRefundApplyListResponse(OrderListRequestContext request, Pair<List<OCMSWaitAuditOrderVO>, PageInfoVO> pair, List<OrderLabelModel> showLabelList) {
        RefundApplyListResponse refundApplyListResponse = orderListResonseBuilder.buildBasicRefundApplyListResponse(pair.getKey(), pair.getValue(), showLabelList);
        CountDownLatch latch = new CountDownLatch(1);
        execute(() -> {
            try {
                fillRefundOrderPrintAndDeliveryInfo(refundApplyListResponse.getRefundApplyRecordVOList(), request);
            }finally {
                latch.countDown();
            }
        });
        List<OCMSOrderVO> ocmsOrderVOList = pair.getKey().stream().map(v -> (OCMSOrderVO) v).collect(Collectors.toList());
        fillRefundOrderOperateAndProfitInfo(refundApplyListResponse, request, ocmsOrderVOList);
        //  设置最大等待超时
        try {
            boolean await = latch.await(MccConfigUtil.getParallelQueryAwaitTime(), TimeUnit.MILLISECONDS);
            if (!await){
                log.error("并行查询订单列表超时,请求信息:{}", request);
            }
        } catch (InterruptedException e) {
            log.error("query pending task error, msg:{}", e.getMessage(), e);
        }
        return refundApplyListResponse;
    }

    protected RefundApplyListResponse syncBuildRefundApplyListResponse(OrderListRequestContext request, Pair<List<OCMSWaitAuditOrderVO>, PageInfoVO> pair, List<OrderLabelModel> showLabelList) {
        RefundApplyListResponse refundApplyListResponse = orderListResonseBuilder.buildBasicRefundApplyListResponse(pair.getKey(), pair.getValue(), showLabelList);
        fillRefundOrderPrintAndDeliveryInfo(refundApplyListResponse.getRefundApplyRecordVOList(), request);
        List<OCMSOrderVO> ocmsOrderVOList = pair.getKey().stream().map(v -> (OCMSOrderVO) v).collect(Collectors.toList());
        fillRefundOrderOperateAndProfitInfo(refundApplyListResponse, request, ocmsOrderVOList);
        return refundApplyListResponse;
    }

    //接口越权了
    public List<OCMSOrderVO> queryOCMSVoByViewOrderId(Long tenantId, List<ViewIdCondition> viewIdConditionList, boolean isShowSalePrice) {
        OCMSRevenueListViewIdConditionRequest viewIdConditionRequest = orderRequestBuilder.buildOCMSListByViewOrderIdRequest(tenantId, viewIdConditionList, isShowSalePrice);
        log.info("ocmsQueryThriftService.queryOrderByViewIdCondition request:{}", viewIdConditionRequest);
        OCMSListViewIdConditionResponse viewIdConditionResponse = merChantRevenueQueryService.queryOrderRevenueDetailByViewIdCondition(viewIdConditionRequest);
        log.info("ocmsQueryThriftService.queryOrderByViewIdCondition response:{}", viewIdConditionResponse);
        if (viewIdConditionResponse.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
            log.info("获取订单详情失败, viewIdConditionRequest:{}", viewIdConditionRequest);
            throw new BizException(ResultCodeEnum.FAIL.getCode(), viewIdConditionResponse.getStatus().getMessage());
        }
        checkViewResponseCount(viewIdConditionRequest, viewIdConditionResponse);
        return viewIdConditionResponse.getOcmsOrderList();
    }

    public List<OCMSOrderVO> queryOCMSVoByViewOrderIdWithoutRevenueQuery(Long tenantId, List<ViewIdCondition> viewIdConditionList) {
        OCMSListViewIdConditionRequest viewIdConditionRequest = orderRequestBuilder.buildOCMSListByViewOrderIdRequest(tenantId, viewIdConditionList);
        log.info("ocmsQueryThriftService.queryOrderByViewIdCondition request:{}",
                viewIdConditionRequest);
        OCMSListViewIdConditionResponse viewIdConditionResponse = ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
        log.info("ocmsQueryThriftService.queryOrderByViewIdCondition response:{}", viewIdConditionResponse);
        if (viewIdConditionResponse.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
            log.info("获取订单详情失败, viewIdConditionRequest:{}", viewIdConditionRequest);
            throw new BizException(ResultCodeEnum.FAIL.getCode(), viewIdConditionResponse.getStatus().getMessage());
        }
        checkViewResponseCount(viewIdConditionRequest, viewIdConditionResponse);
        return viewIdConditionResponse.getOcmsOrderList();
    }

    protected Pair<List<OCMSOrderVO>, PageInfoVO> queryOrderInfo(OrderListRequestContext request) {
        return null;
    }

    protected Integer countAll(Long tenantId, List<Long> storeIdList, Integer entityType) {
        throw new UnsupportedOperationException();
    }


    protected Map<OrderTabSubTypeEnum, Integer> countSubTap(Long tenantId, List<Long> storeIdList, Integer entityType) {
        throw new UnsupportedOperationException();
    }

    protected OrderListResponse buildOrderListResponse(OrderListRequestContext request, Pair<List<OCMSOrderVO>, PageInfoVO> pair, List<OrderLabelModel> showLabelList) {
        if (CollectionUtils.isEmpty(pair.getKey())) {
            return emptyOrderListResponse(pair.getValue());
        }
        // 设置退款展示类型
        orderVoConvertUtil.setRefundPriceDisplayType4OrderList(pair.getKey(), request.isShowSalePrice() ?
                PriceDisplayType.REFUND_AMOUNT.getCode() : PriceDisplayType.OFFLINE_PRICE.getCode());
        OrderListResponse orderListResponse;
        if (supportParallelQuery() && parallelQuerySwitch(request.getIdentityInfo())){
            orderListResponse = parallelBuildOrderListResponse(request, pair, showLabelList);
        }else {
            orderListResponse = syncBuildOrderListResponse(request, pair, showLabelList);
        }
        // 订单根据配送方式决定是否展示【贵品取货码】
        isShowExpensiveProductPickupCode(orderListResponse);
        return orderListResponse;
    }

    /**
     * 订单根据配送方式决定是否展示【贵品取货码】
     * @param orderListResponse
     */
    private void isShowExpensiveProductPickupCode(OrderListResponse orderListResponse){
        try {
            if (Objects.isNull(orderListResponse) || CollectionUtils.isEmpty(orderListResponse.getOrderList())) {
                return;
            }
            orderListResponse.getOrderList().forEach(order -> {
                // 如果不是平台配送，则不返回贵品取货码
                if (StringUtils.isNotEmpty(order.getExpensiveProductPickupCode()) && Objects.nonNull(order.getDeliveryChannelType()) && !Objects.equals(DeliveryEntityEnum.PLATFORM.getValue(), order.getDeliveryChannelType())) {
                    order.setExpensiveProductPickupCode(null);
                }
            });
        }catch (Exception e){
            log.warn("isShowExpensiveProductPickupCode is error! orderListResponse:{}", JSON.toJSONString(orderListResponse), e);
        }
    }


    private OrderListResponse parallelBuildOrderListResponse(OrderListRequestContext request, Pair<List<OCMSOrderVO>, PageInfoVO> pair, List<OrderLabelModel> showLabelList) {
        OrderListResponse orderListResponse = orderListResonseBuilder.buildBasicOrderListResponse(pair.getKey(), pair.getValue(), showLabelList);
        CountDownLatch latch = new CountDownLatch(1);
        execute(() -> {
            try {
                asyncOrderTask(orderListResponse, request);
            }finally {
                latch.countDown();
            }
        });
        syncOrderTask(orderListResponse, request, pair.getKey());
        //  设置最大等待超时
        try {
            boolean await = latch.await(MccConfigUtil.getParallelQueryAwaitTime(), TimeUnit.MILLISECONDS);
            if (!await){
                log.error("并行查询订单列表超时,请求信息:{}", request);
            }
        } catch (InterruptedException e) {
            log.error("query pending task error, msg:{}", e.getMessage(), e);
        }
        return orderListResponse;
    }

    private OrderListResponse syncBuildOrderListResponse(OrderListRequestContext request, Pair<List<OCMSOrderVO>, PageInfoVO> pair, List<OrderLabelModel> showLabelList) {
        OrderListResponse orderListResponse = orderListResonseBuilder.buildBasicOrderListResponse(pair.getKey(), pair.getValue(), showLabelList);
        asyncOrderTask(orderListResponse, request);
        syncOrderTask(orderListResponse, request, pair.getKey());
        return orderListResponse;
    }

    protected boolean needFillOrderRevenueDetailInfoAlone(OrderListRequestContext request){
        return false;
    }

    private void syncOrderTask(OrderListResponse orderListResponse, OrderListRequestContext request, List<OCMSOrderVO> ocmsOrderVOList){
        if (needFillOrderRevenueDetailInfoAlone(request)) {
            fillOrderRevenueDetailInfo(orderListResponse.getOrderList(), ocmsOrderVOList, request);
        }
        fillOrderProfitInfo(orderListResponse.getOrderList(), ocmsOrderVOList, request);
        // 配置可操作按钮
        orderOperateItemsService.setCouldOperateItems(request.getTenantId(), orderListResponse, null, Lists.newArrayList(OrderCouldOperateItem.ACCEPT_ORDER), request.getStoreId());
        //只能拣货前订单，方法内会判读状态
        orderTransferService.setOrderRecommendTransfer(orderListResponse);
        fillOrderPrintInfoAndPrintTimes(orderListResponse.getOrderList(), request);
    }

    private void asyncOrderTask(OrderListResponse orderListResponse, OrderListRequestContext request){
        fillDeliveryInfo(orderListResponse.getOrderList(), request);
        fillPickerNamesInfo(orderListResponse.getOrderList());

    }

    private void fillOrderProfitInfo(List<OrderVO> orderList, List<OCMSOrderVO> ocmsOrderVOList, OrderListRequestContext request){
        Map<String, SaasCrmDataRemoteService.OrderProfitView> orderProfitMap =  request.isShowSalePrice() ?
                orderProfitService.getOrderProfitMapV2(request.getTenantId(), ocmsOrderVOList) : Collections.emptyMap();
        if (MapUtils.isEmpty(orderProfitMap) || CollectionUtils.isEmpty(orderList)){
            return;
        }
        for (OrderVO orderVO : orderList){
            SaasCrmDataRemoteService.OrderProfitView orderProfit = orderProfitMap.get(orderVO.getChannelOrderId());
            if (Objects.nonNull(orderProfit) && Objects.nonNull(orderVO.getRevenueDetail())) {
                orderVO.getRevenueDetail().setNetProfitOnline(orderProfit.getProfit().intValue());
                orderVO.getRevenueDetail().setWithDeliveryCost(orderProfit.getWithDeliveryCost());
            }
        }
    }

    private void fillOrderRevenueDetailInfo(List<OrderVO> orderList, List<OCMSOrderVO> ocmsOrderVOList, OrderListRequestContext request){
        List<OrderRevenueDetailResponse> orderProfitList =  request.isShowSalePrice() ?
                orderProfitService.getOrderListRevenueDetail4TenantAndViewIds(request.getTenantId(), ocmsOrderVOList) : Collections.emptyList();
        if (CollectionUtils.isEmpty(orderProfitList) || CollectionUtils.isEmpty(orderList)){
            return;
        }
        Map<String, OrderRevenueDetailResponse> orderRevenueDetailResponseMap = orderProfitList.stream().collect(Collectors.toMap(OrderRevenueDetailResponse::getOrderViewId, Function.identity(), (v1, v2) -> v2));
        for (OrderVO orderVO : orderList){
            OrderRevenueDetailResponse orderRevenueDetailResponse = orderRevenueDetailResponseMap.get(orderVO.getChannelOrderId());
            if(orderRevenueDetailResponse != null && orderRevenueDetailResponse.getOrderAmountInfo() != null){
                OrderAmountInfo orderAmountInfo = orderRevenueDetailResponse.getOrderAmountInfo();
                RevenueDetailVo revenueDetailVo = new RevenueDetailVo();
                revenueDetailVo.setPromotionInfos(orderRevenueDetailResponse.getPromotionInfos());
                revenueDetailVo.setActualPayAmount(orderAmountInfo.getActualPayAmt());
                if(!Integer.valueOf(OrderSourceEnum.GLORY.getValue()).equals(orderVO.getOrderSource())){
                    // todo 牵牛花一期不返回活动分摊信息、二期适配后再放开
                    revenueDetailVo.setBizActivityAmount(orderAmountInfo.getBizCharge());
                }
                revenueDetailVo.setDeliveryAmount(orderAmountInfo.getDeliveryFee());
                revenueDetailVo.setPackageAmount(orderAmountInfo.getPackageAmount());
                revenueDetailVo.setRevenueAmount(orderAmountInfo.getBizReceiveAmount());
                orderVO.setRevenueDetail(revenueDetailVo);
            }
        }
    }

    private void fillRefundOrderProfitInfo(List<RefundApplyRecordVO> orderList, List<OCMSOrderVO> ocmsOrderVOList, OrderListRequestContext request){
        Map<String, SaasCrmDataRemoteService.OrderProfitView> orderProfitMap =  request.isShowSalePrice() ?
                orderProfitService.getOrderProfitMapV2(request.getTenantId(), ocmsOrderVOList) : Collections.emptyMap();
        if (MapUtils.isEmpty(orderProfitMap) || CollectionUtils.isEmpty(orderList)){
            return;
        }
        for (RefundApplyRecordVO orderVO : orderList){
            SaasCrmDataRemoteService.OrderProfitView orderProfit = orderProfitMap.get(orderVO.getOrderVO().getChannelOrderId());
            if (Objects.nonNull(orderProfit) && Objects.nonNull(orderVO.getOrderVO().getRevenueDetail())) {
                orderVO.getOrderVO().getRevenueDetail().setNetProfitOnline(orderProfit.getProfit().intValue());
                orderVO.getOrderVO().getRevenueDetail().setWithDeliveryCost(orderProfit.getWithDeliveryCost());
            }
        }
    }


    private void fillOrderPrintInfoAndPrintTimes(List<OrderVO> orderList, OrderListRequestContext request){
        List<com.sankuai.meituan.reco.pickselect.query.thrift.print.dto.OrderPrintResultDto> orderPrintResultDtos = printService.queryOrderPrintResultV2(request.getTenantId(), orderList);
        if (CollectionUtils.isEmpty(orderPrintResultDtos)){
            return;
        }
        Map<String, com.sankuai.meituan.reco.pickselect.query.thrift.print.dto.OrderPrintResultDto> printResultDtoMap = Optional.ofNullable(orderPrintResultDtos).map(List::stream).orElse(Stream.empty())
                .collect(Collectors.toMap(com.sankuai.meituan.reco.pickselect.query.thrift.print.dto.OrderPrintResultDto::getOrderNo, v -> v, (f, s) -> f));
        for (OrderVO orderVO : orderList){
            com.sankuai.meituan.reco.pickselect.query.thrift.print.dto.OrderPrintResultDto printResultDto = printResultDtoMap.get(orderVO.getChannelOrderId());
            if (printResultDto != null) {
                OrderPrintStatusVo printStatusVo = printService.buildPrintStatusVo(printResultDto);
                orderVO.setPrintStatus(printStatusVo);
                orderVO.setPrintTimes(orderVoService.getPrintTimesStr(printResultDto.getSuccessfulTimes()));
            }
        }
    }

    private void fillRefundOrderPrintInfoAndPrintTimes(List<RefundApplyRecordVO> orderList, OrderListRequestContext request){
        List<OrderVO> orderVOList = orderList.stream().map(RefundApplyRecordVO::getOrderVO).collect(Collectors.toList());
        List<com.sankuai.meituan.reco.pickselect.query.thrift.print.dto.OrderPrintResultDto> orderPrintResultDtos = printService.queryOrderPrintResultV2(request.getTenantId(), orderVOList);
        if (CollectionUtils.isEmpty(orderPrintResultDtos)){
            return;
        }
        Map<String, com.sankuai.meituan.reco.pickselect.query.thrift.print.dto.OrderPrintResultDto> printResultDtoMap = Optional.ofNullable(orderPrintResultDtos).map(List::stream).orElse(Stream.empty())
                .collect(Collectors.toMap(com.sankuai.meituan.reco.pickselect.query.thrift.print.dto.OrderPrintResultDto::getOrderNo, v -> v, (f, s) -> f));
        for (RefundApplyRecordVO refundApplyRecordVO : orderList){
            OrderVO orderVO = refundApplyRecordVO.getOrderVO();
            com.sankuai.meituan.reco.pickselect.query.thrift.print.dto.OrderPrintResultDto printResultDto = printResultDtoMap.get(orderVO.getChannelOrderId());
            if (printResultDto != null) {
                OrderPrintStatusVo printStatusVo = printService.buildPrintStatusVo(printResultDto);
                orderVO.setPrintStatus(printStatusVo);
                orderVO.setPrintTimes(orderVoService.getPrintTimesStr(printResultDto.getSuccessfulTimes()));
            }
        }
    }

    private void fillDeliveryInfo(List<OrderVO> orderList, OrderListRequestContext request){
        //添加实时配送信息，和配送操作按钮
        if (CollectionUtils.isNotEmpty(orderList)) {
            javafx.util.Pair<Map<Long, TDeliveryDetail>, Map<Integer, DeliveryChannelDto>> deliveryDetailMap = appendDeliveryInfoService.appendDeliveryInfo(orderList);
            deliveryService.fillOrderMaltDeliveryPlatModuleWithSwitch(orderList, deliveryDetailMap.getKey(), deliveryDetailMap.getValue(), request.getTenantId(), request.getStoreId(),request.isPadClient());
            deliveryService.fillDeliveryOperateItem(deliveryDetailMap.getKey(), orderList, request.getStoreId());
        }
    }

    private void fillRefundDeliveryInfo(List<RefundApplyRecordVO> orderList, OrderListRequestContext request){
        //添加实时配送信息，和配送操作按钮
        if (CollectionUtils.isNotEmpty(orderList)) {
            List<OrderVO> orders =
                    orderList.stream().map(RefundApplyRecordVO::getOrderVO).collect(Collectors.toList());
            javafx.util.Pair<Map<Long, TDeliveryDetail>, Map<Integer, DeliveryChannelDto>> deliveryDetailMap = appendDeliveryInfoService.appendDeliveryInfo(orders);
            if(request.isHasMaltFarmPermission()){
                deliveryService.fillOrderMaltDeliveryPlatModuleWithSwitch(orders, deliveryDetailMap.getKey(),deliveryDetailMap.getValue(), request.getTenantId(), request.getStoreId(),request.isPadClient());
            }
        }
    }

    private void fillPickerNamesInfo(List<OrderVO> orderList){
        // 添加拣货人信息
        if (CollectionUtils.isNotEmpty(orderList)) {
            orderVoService.setOrderVOPickerNamesInfo(orderList);
        }
    }

    private void fillRefundPickerNamesInfo(List<RefundApplyRecordVO> orderList){
        // 添加拣货人信息
        if (CollectionUtils.isNotEmpty(orderList)) {
            orderVoService.setRefundApplyRecordVOPickerNamesInfo(orderList);
        }
    }

    protected OrderListResponse emptyOrderListResponse(PageInfoVO pageInfoVO){
        OrderListResponse orderListResponse = new OrderListResponse();
        orderListResponse.setOrderList(Collections.emptyList());
        orderListResponse.setPageInfo(pageInfoVO);
        return orderListResponse;
    }

    protected RefundApplyListResponse emptyRefundApplyListResponse(PageInfoVO pageInfoVO){
        RefundApplyListResponse orderListResponse = new RefundApplyListResponse();
        orderListResponse.setRefundApplyRecordVOList(Collections.emptyList());
        orderListResponse.setPageInfo(pageInfoVO);
        return orderListResponse;
    }


    public void appendDeliveryInfo(List<OrderVO> orderList, OrderListRequestContext request){
        javafx.util.Pair<Map<Long, TDeliveryDetail>, Map<Integer, DeliveryChannelDto>> deliveryDetailMap = appendDeliveryInfoService.appendDeliveryInfo(orderList);
        deliveryService.fillOrderMaltDeliveryPlatModuleWithSwitch(orderList, deliveryDetailMap.getKey(), deliveryDetailMap.getValue(), request.getTenantId(), request.getStoreId(), request.isPadClient());
    }

    public void addOrderDeliveryStatusChangeTimeWithPayTime(List<OrderVO> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        for (OrderVO each : orderList) {
            each.setDeliveryStatusChangeTime(each.getPayTime());
        }
    }

    public void fillWait2ConfirmOrderDeliveryStatusChangeTimeWithPayTime(List<OrderVO> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        for (OrderVO each : orderList) {
            if (each.getOrderStatus() == OrderStatusEnum.SUBMIT.getValue()) {
                each.setDeliveryStatusChangeTime(each.getPayTime());
            }
        }
    }

    public void deleteAfterSaleOrderViewFuseOrderStatus(List<OrderVO> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        for (OrderVO each : orderList) {
            if (TimeUtils.getBeforeDayTimeStamp(MccConfigUtil.queryOrderCreateTimeBefore()) <= each.getCreateTime()) {
                continue;
            }
            if (!Objects.equals(each.getOrderStatus(), OrderStatusEnum.REFUND_APPLIED.getValue())){
                continue;
            }
            each.setViewFuseOrderStatus(null);

        }
    }




    public Integer queryOrderCountByTab(QueryOrderTypeQuantityEnum queryOrderTypeQuantityEnum, Long tenantId, List<Long> storeIdList, Integer entityType){
        return MapUtils.getInteger(queryOrderCountByTab(Lists.newArrayList(queryOrderTypeQuantityEnum.getValue()), tenantId, storeIdList, entityType), 0);
    }

    public Map<Integer, Integer> queryOrderCountByTab(List<Integer> queryOrderTypeQuantityEnumValues, Long tenantId, List<Long> storeIdList, Integer entityType){
        //请求订单数据源
        OCMSQueryOrderQuantityRequest request = new OCMSQueryOrderQuantityRequest();
        request.setTenantId(tenantId);
        request.setShopIdList(storeIdList);
        request.setOrderQuantityTypeList(queryOrderTypeQuantityEnumValues);
        //查询当前时间之前7天的订单
        request.setBeginCreateTime(TimeUtils.getBeforeDayTimeStamp(MccConfigUtil.queryOrderCreateTimeBefore()));
        request.setEndCreateTime(System.currentTimeMillis());
        if(Objects.equals(entityType, PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code())){
            request.setShopIdList(new ArrayList<>());
            request.setWarehouseIdList(storeIdList);
        }
        try {
            OCMSQueryOrderQuantityResponse orderQuantityResponse = ocmsQueryThriftService.queryOrderQuantityV2(request);
            if (orderQuantityResponse.getStatus() != null
                    && StatusCodeEnum.SUCCESS.getCode() == orderQuantityResponse.getStatus().getCode()
                    && MapUtils.isNotEmpty(orderQuantityResponse.getOrderTypeQuantityMap())){
                return orderQuantityResponse.getOrderTypeQuantityMap();
            }
        }catch (Exception e){
            log.error( "查询订单数量异常, request:{}", request);
        }
        return Maps.newHashMap();
    }

    protected void setOrderListResponseViewStatus(OrderListResponse response, OrderViewStatusEnum orderViewStatusEnum) {
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getOrderList()) || Objects.isNull(orderViewStatusEnum)) {
            return;
        }
        for (OrderVO each : response.getOrderList()) {
            each.setViewStatus(orderViewStatusEnum.getCode());
        }
    }

    private void checkViewResponseCount(OCMSListViewIdConditionRequest viewIdConditionRequest, OCMSListViewIdConditionResponse viewIdConditionResponse) {
        if (collectionSize(viewIdConditionRequest.getViewIdConditionList()) != collectionSize(viewIdConditionResponse.getOcmsOrderList())){
            MetricHelper.build().name("waitToPickOrderCountNotEqual").count();
            log.info("查询订单详情返回数量不等,viewId:{}, responseOrder:{}",
                    Optional.ofNullable(viewIdConditionRequest.getViewIdConditionList()).map(List::stream).orElse(Stream.empty()).map(ViewIdCondition::getViewOrderId).collect(Collectors.toList()),
                    Optional.ofNullable(viewIdConditionResponse.getOcmsOrderList()).map(List::stream).orElse(Stream.empty()).map(OnlineBaseOrderVO::getViewOrderId).collect(Collectors.toList()));
        }
    }

    private int collectionSize(Collection<?> collection){
        return collection != null ? collection.size() : 0;
    }



    private boolean parallelQuerySwitch(IdentityInfo identityInfo){
        if (parallelSwitchClose) {
            if (MccConfigUtil.resetParallelSwitch()){
                parallelSwitchClose = false;
                log.info("orderapi 并行查询开关强制开启");
            }
            return false;
        }
        Long tenantId = identityInfo.getUser().getTenantId();
        if (MccConfigUtil.isDrunkHorseTenant(tenantId)){
            return false;
        }
        if (CollectionUtils.isEmpty(identityInfo.getStoreIdList()) || identityInfo.getStoreIdList().size() > 1){
            return false;
        }
        return MccConfigUtil.hitNewOrderApiParallelGrayTenant(tenantId);
    }

    public List<OrderLabelModel> queryOrderShowLabel(Long tenantId) {
        try {
            if(!com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil.isUseNewOrderMark(tenantId)){
                return new ArrayList<>();
            }
            List<OrderLabelModel> orderLabelModelList = orderLabelRemoteService.queryTenantLabel(tenantId, Lists.newArrayList(LabelSourceEnum.COMMON.getValue(), LabelSourceEnum.CUSTOM.getValue()), Lists.newArrayList(LabelTypeEnum.MAIN_ORDER.getValue()), true);
            if (CollectionUtils.isEmpty(orderLabelModelList)) {
                return new ArrayList<>();
            }
            return orderLabelModelList.stream()
                    .filter(OrderLabelModel::getIsShow)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("FuseOrderServiceImpl.queryOrderShowLabel error", e);
            return new ArrayList<>();
        }
    }

}
