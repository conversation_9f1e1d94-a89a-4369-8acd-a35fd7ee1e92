package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Optionable;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 退单状态
 */
@AllArgsConstructor
@Getter
public enum RefundStatusEnum implements Optionable {


    ALL(0, "全部"),
    DEAL_ING(10, "待处理"),
    CANCEL(30, "已取消"),
    COMPLETE(40, "已完成"),
    REJECT(50, "已拒绝"),
    ;


    private int code;

    private String desc;


    public static RefundStatusEnum getByCode(Integer code) {

        if (Objects.isNull(code)) {
            return null;
        }

        for (RefundStatusEnum e : RefundStatusEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }

        return null;
    }


    @Override
    public List<UiOption> toOptions() {
        return Arrays.asList(RefundStatusEnum.values()).stream()
                .map(e -> new UiOption(String.valueOf(e.getCode()), e.getDesc())).collect(Collectors.toList());
    }
}
