package com.sankuai.shangou.qnh.orderapi.converter.store;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.common.enums.OrderCanOperateItem;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.*;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.DeliveryInfoDTO;
import com.meituan.shangou.saas.order.management.client.enums.AfterSaleWhoApplyType;
import com.meituan.shangou.saas.order.management.client.enums.ChannelType;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderLabelModel;
import com.meituan.shangou.saas.order.management.client.utils.DesensitizeReceiverInfoUtil;
import com.meituan.shangou.saas.order.management.client.utils.OrderUtil;
import com.meituan.shangou.saas.order.management.client.utils.param.DesensitizeReceiverInfoExtParam;
import com.meituan.shangou.saas.order.management.client.utils.result.DesensitizeReceiverInfoResult;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.*;
import com.meituan.shangou.saas.order.platform.utils.GsonUtil;
import com.meituan.shangou.saas.utils.AddressSceneConvertUtils;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.PropertiesViewVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ExchangeItemInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderTagVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.AfterSaleRecordDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.AfterSaleRecordVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ExchangeProductVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderExtDataVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ProductBatchInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ProductVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PromotionVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.TagInfoVO;
import com.sankuai.shangou.qnh.orderapi.enums.store.UserTagTypeEnum;
import com.sankuai.shangou.qnh.orderapi.service.common.ExchangeItemUtil;
import com.sankuai.shangou.qnh.orderapi.service.common.OrderOperateItemsService;
import com.sankuai.shangou.qnh.orderapi.utils.CombinationProductUtil;
import com.sankuai.shangou.qnh.orderapi.utils.CommonUsedUtil;
import com.sankuai.shangou.qnh.orderapi.utils.CompensationUtil;
import com.sankuai.shangou.qnh.orderapi.utils.ProductLabelUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.OrderUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.store.PhoneNumberUtils;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.meituan.shangou.saas.order.platform.ExtDataKeyConstants.ExchangeEk.EXE_CHANGE_ORDER_ITEM_CNT_FROM_KEY;
import static com.meituan.shangou.saas.order.platform.ExtDataKeyConstants.ExchangeEk.EXE_CHANGE_ORDER_ITEM_ID_FROM_KEY;
import static com.meituan.shangou.saas.order.platform.ExtDataKeyConstants.ExchangeEk.EXE_CHANGE_ORDER_QUANTITY_KEY;

/**
 * <AUTHOR>
 * @since 2021/04/27 19:35
 */
@Slf4j
public class ChannelOrderConverter {

    private static final String SYMBOL_COMMA = ",";

    private static final int IS_SELF_DELIVERY_NO = 0;

    public static List<OrderVO> buildOrderVOList(List<OrderInfoVo> orders) {
        List<OrderVO> orderVOList = Lists.newArrayList();
        for (OrderInfoVo orderInfo : orders) {
            OrderVO orderVO = new OrderVO();
            orderVO.setTenantId(orderInfo.getTenantId());
            orderVO.setChannelId(orderInfo.channelId);
            orderVO.setChannelName(orderInfo.getChannelName());
            orderVO.setStoreId(orderInfo.getShopId());
            orderVO.setStoreName(orderInfo.getShopName());
            orderVO.setChannelOrderId(orderInfo.getChannelOrderId());
            orderVO.setSerialNo(orderInfo.getOrderSerialNumber());
            orderVO.setSerialNoStr(orderInfo.getOrderSerialNumberStr());
            orderVO.setItemCount(orderInfo.getItemCount());
            orderVO.setActualPayAmt(orderInfo.getActualPayAmt());
            orderVO.setBizReceiveAmt(orderInfo.getBizReceiveAmt());
            orderVO.setDeliveryMethod(orderInfo.getDeliveryMethod());
            orderVO.setDeliveryMethodDesc(orderInfo.getDeliveryName());
            orderVO.setDeliveryUserName(orderInfo.getRiderName());
            orderVO.setDeliveryUserPhone(orderInfo.getRiderPhone());
            orderVO.setReceiverName(orderInfo.getReceiverName());
            orderVO.setReceiverPhone(orderInfo.getReceiverPhone());
            orderVO.setReceiveAddress(orderInfo.getReceiveAddress());
            orderVO.setChannelOrderStatus(orderInfo.getChannelOrderStatus());
            orderVO.setChannelOrderStatusDesc(orderInfo.getChannelOrderStatusDesc());
            orderVO.setAggregationOrderStatus(orderInfo.getAggregationOrderStatus());
            orderVO.setCreateTime(orderInfo.getCreateTime());
            orderVO.setRefundAmt(orderInfo.getRefundAmt());
            orderVO.setRefundTagId(orderInfo.getRefundTagId());
            orderVO.setRefundTagDesc(orderInfo.getRefundTagDesc());
            orderVO.setAuditingRefundTagId(orderInfo.getAuditingRefundTagId());
            orderVO.setAuditingRefundTagDesc(orderInfo.getAuditingRefundTagDesc());
            orderVO.setCouldOperateItemList(orderInfo.getCouldOperateItemList());
            orderVO.setDeliveryOrderType(orderInfo.getDeliveryOrderType());
            orderVO.setDeliveryOrderTypeName(orderInfo.getDeliveryOrderTypeTypeName());
            orderVO.setEstimatedSendArriveTimeStart(orderInfo.getEstimatedSendArriveTimeStart());
            orderVO.setEstimatedSendArriveTimeEnd(orderInfo.getEstimatedSendArriveTimeEnd());
            orderVO.setPickupStatus(orderInfo.getDistributeStatus());
            orderVO.setPickupCompleteTime(orderInfo.getPickupCompleteTime());
            orderVO.setDistributeStatus(orderInfo.getDistributeStatus());
            orderVO.setOfflineOrderStatus(orderInfo.getOfflineOrderStatus());
            orderVO.setUpdateTime(orderInfo.getUpdateTime());
            orderVO.setChannelExtraOrderId(orderInfo.getChannelExtraOrderId());
            orderVO.setComments(orderInfo.getComments());
            orderVO.setProductList(buildProductVOList(orderInfo.getTenantId(), orderInfo.getShopId(), orderInfo.getProducts()));
            if (orderInfo.getUserId() != null && orderInfo.getUserId() > BigInteger.ZERO.longValue()) {
                orderVO.setUserId(orderInfo.getUserId());
            }
            if (CollectionUtils.isNotEmpty(orderInfo.getTags())) {
                orderVO.setUserTags(UserTagTypeEnum.getTagList(orderInfo.getTags()));
            }
            // 设置订单支付时间
            orderVO.setPayTime(orderInfo.getPayTime() != null && orderInfo.getPayTime() > 0 ? orderInfo.getPayTime() : orderInfo.getCreateTime());
            orderVO.setOrderUserType(orderInfo.getOrderUserType());
            orderVOList.add(orderVO);
        }
        return orderVOList;
    }

    public static List<ProductVO> buildProductVOList(long tenantId, long storeId, List<ProductInfoVo> productInfoList) {
        //履约标签
        int FULFILLMENT_TAG = 1;

        //拣货标准
        int PICK_TAG = 2;
        if (CollectionUtils.isEmpty(productInfoList)) {
            return Lists.newArrayList();
        }
        List<ProductVO> productVOList = Lists.newArrayList();
        Map<Long, List<ExchangeProductVO>> exchangeProductMap = new HashMap<>();
        for (ProductInfoVo productInfo : productInfoList) {
            ProductVO productVO = new ProductVO();
            productVO.setOrderItemId(productInfo.getOrderItemId());
            productVO.setSkuId(productInfo.getSkuId());
            productVO.setCustomerSkuId(productInfo.getCustomerSkuId());
            productVO.setUpcCode(productInfo.getUpcCode());
            productVO.setSkuName(productInfo.getSkuName());

            productVO.setTotalDiscountAmount(productInfo.getTotalDiscountAmount());
            productVO.setPicUrl(productInfo.getPicUrl());
            //增加字段，返回库中所有图片url
            productVO.setMultiPicUrl(processMultiPicUrl(productInfo.getPicUrl()));
            productVO.setSpecification(productInfo.getSpecification());
            productVO.setSellUnit(productInfo.getSellUnit());
            productVO.setUnitPrice(productInfo.getUnitPrice());
            productVO.setCount(productInfo.getCount());
            productVO.setIsRefund(Optional.ofNullable(productInfo.getIsRefund()).orElse(Boolean.FALSE) ? 1 : 0);
            productVO.setRefundCount(productInfo.getRefundCount());
            productVO.setBoothName(productInfo.getBoothName());
            /**
             * ordermng做了特殊处理，如果线下价格不存在(找不到商品的情况，或者没有设置线下价格的情况)，设置了默认值-1分，导致app端展示-0.01
             * 在storeapi侧做特殊值校验，如果是小于0的线下价格，则不展示出来
             */

            productVO.setStallSettleAmt(productInfo.getStallSettleAmt());
            productVO.setCustomerSkuId(productInfo.getCustomerSkuId());
            productVO.setErpItemCode(productInfo.getErpItemCode());
            productVO.setGoodsCode(productInfo.getGoodsCode());
            productVO.setOriginalPrice(productInfo.getOriginalPrice());
            productVO.setCurrentPrice(productInfo.getCurrentPrice());
            List<TagInfoVO> tagInfoVOS = org.assertj.core.util.Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(productInfo.getFulfillmentTagList())) {
                for (String fulfillmentTag : productInfo.getFulfillmentTagList()) {
                    TagInfoVO tagInfoVO = new TagInfoVO();
                    tagInfoVO.setName(fulfillmentTag);
                    tagInfoVO.setType(FULFILLMENT_TAG);
                    tagInfoVOS.add(tagInfoVO);
                }
            }
            if (CollectionUtils.isNotEmpty(productInfo.getPickTagList())) {
                for (String pickTag : productInfo.getPickTagList()) {
                    TagInfoVO tagInfoVO = new TagInfoVO();
                    tagInfoVO.setName(pickTag);
                    tagInfoVO.setType(PICK_TAG);
                    tagInfoVOS.add(tagInfoVO);

                }
            }
            productVO.setTagInfoList(tagInfoVOS);
            //设置标签信息tagInfoList字段客户端用法错误、不通用
            //新增tagInfos字段、强制要求客户端根据type处理
            productVO.setTagInfos(Optional.ofNullable(productInfo.getTagInfos()).orElse(Collections.emptyList()).stream().map(tag -> {
                TagInfoVO tagInfoVO = new TagInfoVO();
                tagInfoVO.setName(tag.getName());
                tagInfoVO.setType(tag.getType());
                return tagInfoVO;
            }).collect(Collectors.toList()));
            //批次相关
            productVO.setBatchInfo(extractBatchInfo(productInfo.getExtData()));

            //换货相关数据
            Map<String, Object> stringObjectMap = GsonUtil.toObjMap(productInfo.getExtData());
            int quantity = productVO.getCount();
            if (!stringObjectMap.containsKey(EXE_CHANGE_ORDER_ITEM_ID_FROM_KEY)
                    && stringObjectMap.containsKey(EXE_CHANGE_ORDER_QUANTITY_KEY)) {
                Object extOrderQuantity = stringObjectMap.get(EXE_CHANGE_ORDER_QUANTITY_KEY);
                quantity = Integer.parseInt(String.valueOf(extOrderQuantity));
            }
            productVO.setOrderQuantity(quantity);

            productVO.setOriginalTotalPrice(quantity * productInfo.getUnitPrice());
            productVO.setTotalPayAmount(quantity * productInfo.getCurrentPrice());
            productVO.setOfflinePrice(productInfo.getOfflinePrice() < 0 ? null : productInfo.getOfflinePrice());
            if(CollectionUtils.isNotEmpty(productInfo.getCombinationChildProductVoList())){
                productVO.setSubProductVoList(CombinationProductUtil.buildSubProductVoList(productInfo.getOrderItemId(), productInfo.getCombinationChildProductVoList()));
            }
            //为歪马填充新属性
            if (MccDynamicConfigUtil.getWmsjTenantIds().contains(tenantId) && MccDynamicConfigUtil.isNewPickGrayStore(storeId)) {
                if (CollectionUtils.isNotEmpty(productInfo.getPropertyList())) {
                    List<PropertiesViewVO> propertiesViewVOList = productInfo.getPropertyList().stream()
                            .filter(property -> MccDynamicConfigUtil.getPropertyColorMap().containsKey(property))
                            .map(property -> new PropertiesViewVO(property, MccDynamicConfigUtil.getPropertyColorMap().get(property)))
                            .collect(Collectors.toList());
                    productVO.setParsedProperties(propertiesViewVOList);
                }
                //歪马tags不需要了,用parsedProperty代替
                productVO.setTagInfos(Lists.newArrayList());
            }

            // extData确认是否为赠品，设置赠品参数 0-平台赠品，1-自定义赠品
            productVO.setGiftType(OrderUtils.getExtDataAsInt("giftType", productInfo.getExtData()));
            productVO.setChannelLabelList(ProductLabelUtil.buildChannelLabelVOList(productInfo.getChannelLabelList()));
            productVO.setLabelSubDesc(ProductLabelUtil.buildChannelLabelSubDesc(productInfo.getChannelLabelList(), productInfo.getExtData()));
            if (StringUtils.isNotBlank(productInfo.getExtData())) {
                Object exchangeOrderItemId = stringObjectMap.get(EXE_CHANGE_ORDER_ITEM_ID_FROM_KEY);
                if (Objects.isNull(exchangeOrderItemId)) {
                    productVOList.add(productVO);
                    continue;
                } else {
                    Long sourceOrderItemId = Long.parseLong(String.valueOf(exchangeOrderItemId));
                    Object exchangeOrderItemCnt = stringObjectMap.get(EXE_CHANGE_ORDER_ITEM_CNT_FROM_KEY);
                    ExchangeProductVO exchangeProductVO = new ExchangeProductVO();
                    productVO.setOrderQuantity(0);//换货商品，无下单时数量
                    exchangeProductVO.setProductVO(productVO);
                    exchangeProductVO.setExchangeFromCnt(Integer.parseInt(String.valueOf(exchangeOrderItemCnt)));
                    exchangeProductVO.setExchangeToCnt(productVO.getCount());
                    if (exchangeProductMap.containsKey(sourceOrderItemId)) {
                        exchangeProductMap.get(sourceOrderItemId).add(exchangeProductVO);
                    } else {
                        List<ExchangeProductVO> list = new ArrayList<>();
                        list.add(exchangeProductVO);
                        exchangeProductMap.put(sourceOrderItemId, list);
                    }
                }
            } else {
                productVOList.add(productVO);
            }
        }
        productVOList.stream().filter(e -> exchangeProductMap.containsKey(e.getOrderItemId())).forEach(e -> {
            e.setExchangeOrderDetailVOList(exchangeProductMap.get(e.getOrderItemId()));
            e.setExchangeCount(exchangeProductMap.get(e.getOrderItemId()).stream()
                    .map(ExchangeProductVO::getExchangeFromCnt).reduce(0, Integer::sum));
            e.setExchangeGoodsCount(exchangeProductMap.get(e.getOrderItemId()).stream()
                    .map(ExchangeProductVO::getExchangeToCnt).reduce(0, Integer::sum));
        });
        return productVOList;
    }

    private static List<ProductBatchInfoVO> extractBatchInfo(String extData) {
        OrderExtDataVO OrderExtDataVO = GsonUtil.toJavaBean(extData, OrderExtDataVO.class);
        return OrderExtDataVO.getBatchInfoModelList() == null ? new ArrayList<>() : OrderExtDataVO.getBatchInfoModelList();
    }

    private static String processPicUrl(String picUrl) {
        if (Strings.isEmpty(picUrl)) {
            return picUrl;
        }

        if (picUrl.contains(SYMBOL_COMMA)) {
            String[] picUrls = picUrl.split(SYMBOL_COMMA);
            if (picUrls.length > 0) {
                return picUrls[0];
            }
        }

        return picUrl;
    }

    private static List<String> processMultiPicUrl(String picUrls) {
        if (Strings.isEmpty(picUrls)) {
            return new ArrayList<String>();
        }
        return Arrays.asList(picUrls.split(SYMBOL_COMMA));
    }


    public static String convertOperateMsg(OperatorTypeEnum operatorTypeEnum) {
        Map<String, String> operateTypeMsgMap = MccConfigUtil.getOrderStatusOperateMsm();
        String msg = null;
        if (operateTypeMsgMap != null) {
            msg = operateTypeMsgMap.get(String.valueOf(operatorTypeEnum.getValue()));
        }
        return StringUtils.isBlank(msg) ? operatorTypeEnum.getDesc() : msg;
    }

    public static OrderDetailVO buildOrderDetailVO(OrderDetailVo orderDetail, List<Integer> operateItemList, Set<Long> authedStoreIds, List<OrderLabelModel> showLabelList) {
        if (Objects.isNull(orderDetail) || Objects.isNull(orderDetail.getOrderBaseDto())) {
            return null;
        }
        OrderBaseVo orderBase = orderDetail.getOrderBaseDto();
        OrderDetailVO orderDetailVO = new OrderDetailVO();

        orderDetailVO.setOrderId(orderBase.getOrderId());
        orderDetailVO.setChannelOrderId(orderBase.getChannelOrderId());
        orderDetailVO.setStoreId(orderBase.getShopId());
        orderDetailVO.setStoreName(orderBase.getShopName());
        orderDetailVO.setDispatchShopId(orderBase.getDispatchShopId());
        orderDetailVO.setDispatchShopName(orderBase.getDispatchShopName());
        orderDetailVO.setDispatchTenantId(orderBase.getDispatchTenantId());
        orderDetailVO.setDispatchSerialNo(orderBase.getDispatchSerialNo());
        orderDetailVO.setDispatchTime(orderBase.getDispatchTime());
        orderDetailVO.setChannelId(orderBase.getChannelId());
        orderDetailVO.setChannelName(orderBase.getChannelName());
        orderDetailVO.setItemCount(orderBase.getItemCount());
        orderDetailVO.setReceiverName(orderBase.getReceiverName());
        orderDetailVO.setReceiverPhone(orderBase.getReceiverPhone());
        // 美团名酒馆平台配送不展示手机号
        if (BooleanUtils.isTrue(orderBase.getIsMtFamousTavern()) && Objects.equals(orderBase.getIsSelfDelivery(), 0)) {
            orderDetailVO.setReceiverPhone(null);
        }
        // 区分订单渠道返回用户手机尾号ChannelType和OrderBizType。OCMSUtils.convertChannel2OrderBizType
        String userAxPhone = orderBase.getReceiverPhone();
        String receiverNameSuf = "";
        try {
            String tail4PhoneFormat = "%s(尾号：%s)";
            String receiverPhone = orderBase.getReceiverPhone();
            if (orderBase.getChannelId().equals(ChannelType.MEITUAN.getValue()) && StringUtils.isNotBlank(orderBase.getReceiverPrivacyPhone())) {
                orderDetailVO.setReceiverName(String.format(tail4PhoneFormat, orderBase.getReceiverName(), orderBase.getReceiverPrivacyPhone().substring(orderBase.getReceiverPrivacyPhone().length() - 4)));
                receiverPhone = orderBase.getReceiverPrivacyPhone();
                receiverNameSuf = orderDetailVO.getReceiverName().substring(orderBase.getReceiverName().length());
                // 如果是歪马渠道 && 使用了隐私号 && 隐私号不为空
            } else if (orderBase.getChannelId().equals(ChannelType.MT_DRUNK_HORSE.getValue()) && NumberUtils.INTEGER_ONE.equals(orderBase.getUsePrivacyPhone()) && StringUtils.isNotBlank(orderBase.getReceiverPrivacyPhone())) {
                userAxPhone = orderBase.getReceiverPrivacyPhone();
                orderDetailVO.setReceiverName(String.format(tail4PhoneFormat, orderBase.getReceiverName(), orderBase.getReceiverPhone().substring(orderBase.getReceiverPhone().length() - 4)));
            }
            // 只有歪马租户--才展示真实手机号脱敏格式
            if (MccDynamicConfigUtil.getAxbPrivacyPhoneTenantIds().contains(String.valueOf(orderBase.getTenantId()))) {
                orderDetailVO.setReceiverPhone(PhoneNumberUtils.transferToPrivacyPhone(receiverPhone));
            }
        } catch (Exception ex) {
            log.warn("电话号码非法");
        }
        orderDetailVO.setReceiverAxPhone(userAxPhone);
        orderDetailVO.setReceiveAddress(orderBase.getReceiveAddress());

        // 订单状态已完结 && 非歪马租户 && 租户支持隐藏隐私号
        Long orderStatusTime = OrderUtil.getOrderTimeByLogVo(orderBase.getOrderStatus(), orderDetail.getOrderOpLogList());
        if (OrderUtil.isOrderEnd(orderBase.getOrderStatus())
                && !com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil.isDrunkHorseTenant(orderBase.getTenantId())
                && MccDynamicConfigUtil.checkSupportDesensitizeReceiverInfoTenant(orderBase.getTenantId())){
            // 订单超过时间，需要隐藏隐私号
            if(OrderUtil.isOverConfigTime(orderStatusTime, MccDynamicConfigUtil.getDesensitizeReceiverInfoTime())){
                orderDetailVO.setReceiverPhone(null);
            }
        }

        // 订单收货人信息脱敏处理 -- 收货人姓名需要用原数据脱敏操作
        DesensitizeReceiverInfoExtParam receiverInfoExtParam = DesensitizeReceiverInfoExtParam.build(
                orderBase.getReceiverName(),orderDetailVO.getReceiveAddress(),orderDetailVO.getReceiverPhone(),
                orderDetailVO.getReceiverAxPhone(), orderBase.getTenantId(), orderBase.getOrderStatus(), orderStatusTime);
        DesensitizeReceiverInfoResult desensitizeReceiverInfoResult = DesensitizeReceiverInfoUtil.desensitizeReceiverInfo(receiverInfoExtParam);
        if(Objects.nonNull(desensitizeReceiverInfoResult)) {
            orderDetailVO.setReceiverName(desensitizeReceiverInfoResult.getReceiverName() + receiverNameSuf);
            orderDetailVO.setReceiveAddress(desensitizeReceiverInfoResult.getReceiverAddress());
            orderDetailVO.setReceiverPhone(StringUtils.isEmpty(desensitizeReceiverInfoResult.getReceiverPhone()) ? "隐私号已过期" : desensitizeReceiverInfoResult.getReceiverPhone());
            orderDetailVO.setReceiverAxPhone(desensitizeReceiverInfoResult.getReceiverPrivacyPhone());
        }

        // 自提
        boolean selfMention = DistributeMethodEnum.STORE_DELIVERY.getDesc().equals(orderBase.getDeliveryMethodName());
        if (selfMention && orderBase.getChannelId() != null && orderBase.getChannelId().equals(ChannelType.YOU_ZAN.getValue())) {
            // 有赞渠道自提隐藏用户地址
            orderDetailVO.setReceiveAddress("到店自取@#到店自取");
        }
        handleDrunkHorseLocationField(orderBase, orderDetail, orderDetailVO);

        orderDetailVO.setOriginalAmt(orderBase.getOriginalAmt());
        orderDetailVO.setActualPayAmt(orderBase.getActualPayAmt());
        orderDetailVO.setProductTotalPayAmount(calculateProductTotalPayAmount(orderDetail.getProductInfoList()));
        orderDetailVO.setBizReceiveAmt(orderBase.getBizReceiveAmt());
        orderDetailVO.setFreight(orderBase.getFreight());
        orderDetailVO.setPackageAmt(orderBase.getPackageAmt());
        orderDetailVO.setPlatformFee(orderBase.getPlatformFee());
        orderDetailVO.setIsNeedInvoice(Optional.ofNullable(orderBase.getIsNeedInvoice()).orElse(Boolean.FALSE) ? 1 : 0);
        orderDetailVO.setInvoiceTitle(orderBase.getInvoiceTitle());
        orderDetailVO.setTaxNo(orderBase.getTaxNo());
        orderDetailVO.setDeliveryMethod(orderBase.getDeliveryMethod());
        if (orderBase.getDeliveryMethod() != null && DistributeMethodEnum.STORE_DELIVERY.getValue() == orderBase.getDeliveryMethod()) {
            orderDetailVO.setDeliveryMethodName("到店自提");
        } else {
            orderDetailVO.setDeliveryMethodName(orderBase.getDeliveryMethodName());
        }
        orderDetailVO.setDeliveryUserName(orderBase.getDeliveryUserName());
        orderDetailVO.setDeliveryUserPhone(orderBase.getDeliveryUserPhone());
        orderDetailVO.setSupportDeliveryUserPrivacyPhone(judgeSupportDeliveryUserPrivacyPhone(orderBase.getIsSelfDelivery()));
        orderDetailVO.setIsDeliveryOvertime(orderBase.getIsDeliveryOvertime());
        orderDetailVO.setDeliveryExceptionDescription(orderBase.getDeliveryExceptionDescription());
        orderDetailVO.setDeliveryChannelId(orderBase.getDeliveryChannelId());
        orderDetailVO.setDistributeType(orderBase.getDistributeType());
        //获取配送类型原始描述
        orderDetailVO.setDistributeTypeName(orderBase.getDeliveryMethodName());
        orderDetailVO.setPayMethod(orderBase.getPayMethod());
        orderDetailVO.setPayMethodDesc(orderBase.getPayMethodDesc());
        orderDetailVO.setChannelOrderStatus(orderBase.getChannelOrderStatus());
        orderDetailVO.setChannelOrderStatusDesc(orderBase.getChannelOrderStatusDesc());
        orderDetailVO.setAggregationOrderStatus(orderBase.getAggregationOrderStatus());
        orderDetailVO.setCreateTime(orderBase.getCreateTime());
        orderDetailVO.setLastAfterSaleApplyRefundTagId(orderBase.getLastAfterSaleApplyRefundTagId());
        orderDetailVO.setLastAfterSaleApplyReason(orderBase.getLastAfterSaleApplyReason());
        orderDetailVO.setLastAfterSaleApplyStatus(orderBase.getLastAfterSaleApplyStatus());
        orderDetailVO.setLastAfterSaleApplyRejectReason(orderBase.getLastAfterSaleApplyRejectReason());
        orderDetailVO.setRefundableOrderAmount(orderBase.getRefundableOrderAmount());
        orderDetailVO.setEstimatedSendArriveTimeStart(orderBase.getEstimatedSendArriveTimeStart());
        orderDetailVO.setEstimatedSendArriveTimeEnd(orderBase.getEstimatedSendArriveTimeEnd());
        orderDetailVO.setUpdateTime(orderBase.getUpdateTime());
        orderDetailVO.setDistributeStatus(orderBase.getDistributeStatus());
        DistributeStatusEnum status = DistributeStatusEnum.enumOf(orderBase.getDistributeStatus());
        orderDetailVO.setDistributeStatusName(status != null ? status.getDesc() : "");
        orderDetailVO.setPickupStatus(orderBase.getDeliveryStatus());
        orderDetailVO.setChannelExtraOrderId(orderBase.getChannelExtraOrderId());
        orderDetailVO.setComments(orderBase.getComments());
        orderDetailVO.setSerialNo(orderBase.getOrderSerialNumber());
        orderDetailVO.setSerialNoStr(orderBase.getOrderSerialNumberStr());
        orderDetailVO.setDeliveryOrderType(orderBase.getDeliveryOrderType());
        orderDetailVO.setDeliveryOrderTypeName(orderBase.getDeliveryOrderTypeName());
        orderDetailVO.setCouldOperateItemList(operateItemList);

        // 【库位指引】按钮
        if(OrderOperateItemsService.checkRepertoryGuideOp(orderBase.getTenantId(), orderBase.getOrderStatus(), orderBase.getDeliveryStatus())){
            if(orderDetailVO.getCouldOperateItemList() == null) {
                orderDetailVO.setCouldOperateItemList(new ArrayList<>());
            }
            orderDetailVO.getCouldOperateItemList().add(OrderCanOperateItem.REPERTORY_GUIDE.getValue());
        }
        if (Objects.nonNull(orderDetailVO.getDispatchShopId()) && !authedStoreIds.contains(orderDetailVO.getDispatchShopId())) {
            orderDetailVO.setCouldOperateItemList(orderDetailVO.getCouldOperateItemList().stream().filter(e -> Objects.equals(e, OrderCanOperateItem.PRINT_RECEIPT.getValue())).collect(Collectors.toList()));
        }

        orderDetailVO.setProductList(buildProductVOList(orderBase.getTenantId(), orderBase.getShopId(), orderDetail.getProductInfoList()));
        orderDetailVO.setPromotionList(buildPromotionVOList(orderDetail.getPromotionInfoList()));
        Map<Integer, ProductLabelVO> productLabelVOMap = ProductLabelUtil.buildMapFromProductInfoVo(orderDetail.getProductInfoList());
        Map<Long, ProductInfoVo> productMap = orderDetail.getProductInfoList().stream()
                .collect(Collectors.toMap(ProductInfoVo::getOrderItemId, v -> v));
        Map<Long, CombinationChildProductVo> combinationChildProductMap = CollectionUtils
                .isNotEmpty(orderDetail.getCombinationChildProductVoList())
                        ? orderDetail.getCombinationChildProductVoList().stream()
                                .filter(v -> Objects.nonNull(v.getServiceId()))
                                .collect(Collectors.toMap(CombinationChildProductVo::getServiceId, v -> v))
                        : new HashMap();
        Pair<Map<Long, ProductInfoVo>, Map<Long, CombinationChildProductVo>> afsDetailBuildPair = new Pair<>(productMap,
                combinationChildProductMap);
        orderDetailVO.setAfterSaleRecordList(
                buildAfterSaleRecordVOList(orderDetail.getAfterSaleRecords(), productLabelVOMap, orderDetailVO.getReceiveAddress(), afsDetailBuildPair));
        if (orderBase.getUserId() != null && orderBase.getUserId() > BigInteger.ZERO.longValue()) {
            orderDetailVO.setUserId(orderBase.getUserId());
        }
        if (CollectionUtils.isNotEmpty(orderBase.getTags())) {
            orderDetailVO.setUserTags(UserTagTypeEnum.getTagList(orderBase.getTags()));
        }
        // 设置订单支付时间
        orderDetailVO.setPayTime(orderBase.getPayTime() != null && orderBase.getPayTime() > 0 ? orderBase.getPayTime() : orderBase.getCreateTime());
        orderDetailVO.setWarehouseName(orderBase.getWarehouseName());
        orderDetailVO.setWarehouseId(orderBase.getWarehouseId());
        orderDetailVO.setOrderUserType(orderBase.getOrderUserType());
        if (Objects.nonNull(orderBase.getOrderExtend())) {
            orderDetailVO.setSelfPickPullNewOrder(orderBase.getOrderExtend().getSelfPickPullNewOrder());
        }
        orderDetailVO.setToken(OrderUtils.generateMD5Token(orderBase.getChannelOrderId(), orderBase.getChannelId(), orderBase.getTenantId()));
        if (orderDetail.getDeliveryInfoDTO() != null && isDrunkHorseTenant(orderBase.getTenantId()) && isDhScenePoi(orderBase.getShopId())) {
            orderDetailVO.setScene(AddressSceneConvertUtils.convertCategory2Scene(orderDetail.getDeliveryInfoDTO().getCategory()));
        }

        orderDetailVO.setCompensationModelList(CompensationUtil.buildCompensationModelList(orderBase.getCompensation()));
        orderDetailVO.setIsMtFamousTavern(orderBase.getIsMtFamousTavern());
        orderDetailVO.setIsFacaiWine(orderBase.getIsFacaiWine());
        orderDetailVO.setOrderTagList(OrderTagVO.convertOrderTagListV2(orderBase.getOrderMark(), showLabelList));
        return orderDetailVO;
    }

    private static void handleDrunkHorseLocationField(OrderBaseVo orderBase, OrderDetailVo orderDetail, OrderDetailVO orderDetailVO) {
        if (MccConfigUtil.isDrunkHorseTenant(orderBase.getTenantId()) && !MccConfigUtil.hasHideAddress(orderBase.getReceiveAddress())
                && orderDetail.getDeliveryInfoDTO() != null && orderDetail.getDeliveryInfoDTO().getExtData() != null){
            // 歪马租户，如果地址没有隐藏需要返回地址对应的经纬度
            DeliveryInfoDTO deliveryInfoDTO = orderDetail.getDeliveryInfoDTO();
            orderDetailVO.setReceiverLatitude(deliveryInfoDTO.getExtData().getReceiverLatitude());
            orderDetailVO.setReceiverLongitude(deliveryInfoDTO.getExtData().getReceiverLongitude());
        }
    }

    private static int calculateProductTotalPayAmount(List<ProductInfoVo> productInfoList) {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return 0;
        }

        return productInfoList.stream().filter(productInfo -> {
            if (StringUtils.isBlank(productInfo.getExtData())) {
                return true;
            }
            Map<String, Object> stringObjectMap = GsonUtil.toObjMap(productInfo.getExtData());
            if (!stringObjectMap.containsKey(EXE_CHANGE_ORDER_ITEM_ID_FROM_KEY)) {
                return true;
            }
            return false;
        }).mapToInt(productInfo -> {
            Integer orderQuantity;
            if (StringUtils.isBlank(productInfo.getExtData())) {
                orderQuantity = productInfo.getCount();
            } else {
                Map<String, Object> stringObjectMap = GsonUtil.toObjMap(productInfo.getExtData());
                if (stringObjectMap.containsKey(EXE_CHANGE_ORDER_QUANTITY_KEY)) {
                    String orderQuantityStr = String.valueOf(stringObjectMap.get(EXE_CHANGE_ORDER_QUANTITY_KEY));
                    orderQuantity = NumberUtils.isDigits(orderQuantityStr) ? Integer.parseInt(orderQuantityStr) : productInfo.getCount();
                } else {
                    orderQuantity = productInfo.getCount();
                }
            }
            if (orderQuantity == null) {
                return 0;
            } else {
                return orderQuantity * productInfo.getCurrentPrice();
            }
        }).sum();
    }

    private static List<PromotionVO> buildPromotionVOList(List<PromotionInfoVo> promotionInfoList) {
        if (CollectionUtils.isEmpty(promotionInfoList)) {
            return Lists.newArrayList();
        }

        List<PromotionVO> promotionVOList = Lists.newArrayList();
        for (PromotionInfoVo promotionInfo : promotionInfoList) {
            PromotionVO promotionVO = new PromotionVO();
            promotionVO.setDescription(promotionInfo.getDescription());
            promotionVO.setDiscount(promotionInfo.getDiscount());
            promotionVO.setPlatformBearFee(promotionInfo.getPlatformBearFee());
            promotionVO.setBizBearFee(promotionInfo.getBizBearFee());
            promotionVO.setAgentBearFee(promotionInfo.getAgentBearFee());
            promotionVO.setLogisticsBearCharge(promotionInfo.getLogisticsBearCharge());
            promotionVOList.add(promotionVO);
        }
        return promotionVOList;
    }

    private static List<AfterSaleRecordVO> buildAfterSaleRecordVOList(List<AfterSaleRecordVo> afterSaleRecords, Map<Integer, ProductLabelVO> productLabelVOMap, String receiveAddress, Pair<Map<Long, ProductInfoVo>, Map<Long, CombinationChildProductVo>> afsDetailBuildPair) {
        if (CollectionUtils.isEmpty(afterSaleRecords)) {
            return Lists.newArrayList();
        }
        List<AfterSaleRecordVO> afterSaleRecordVOList = Lists.newArrayList();
        for (AfterSaleRecordVo afterSaleRecord : afterSaleRecords) {
            if (afterSaleRecord.getStatus() == AfterSaleApplyStatusEnum.DRAFT.getValue() ||
                    afterSaleRecord.getStatus() == AfterSaleApplyStatusEnum.DRAFT_DONE.getValue()) {
                // 过滤掉暂存相关的售后单
                continue;
            }
            AfterSaleRecordVO afterSaleRecordVO = new AfterSaleRecordVO();
            afterSaleRecordVO.setStoreId(afterSaleRecord.getShopId());
            afterSaleRecordVO.setDispatchShopId(afterSaleRecord.getDispatchShopId());
            afterSaleRecordVO.setDealTime(afterSaleRecord.getDealTime());
            afterSaleRecordVO.setTenantId(afterSaleRecord.getTenantId());
            afterSaleRecordVO.setChannelId(afterSaleRecord.getChannelId());
            afterSaleRecordVO.setServiceId(afterSaleRecord.getServiceId());
            afterSaleRecordVO.setChannelAfterSaleId(afterSaleRecord.getAfterSaleId());
            afterSaleRecordVO.setIsAudit(afterSaleRecord.getIsAudit());
            afterSaleRecordVO.setStatus(afterSaleRecord.getStatus());
            afterSaleRecordVO.setApplyReason(afterSaleRecord.getApplyReason());
            afterSaleRecordVO.setAfsPattern(afterSaleRecord.getAfsPattern());
            afterSaleRecordVO.setRefundAmt(afterSaleRecord.getRefundAmt());
            afterSaleRecordVO.setCreateTime(afterSaleRecord.getCreateTime());
            afterSaleRecordVO.setUpdateTime(afterSaleRecord.getUpdateTime());
            afterSaleRecordVO.setAfsApplyType(afterSaleRecord.getAfsApplyType());
            afterSaleRecordVO.setWhoApplyType(afterSaleRecord.getWhoApplyType());
            // 追加发起者到原因后面
            if (afterSaleRecord.getWhoApplyType() != null
                    && StringUtils.isNotBlank(afterSaleRecord.getApplyReason())) {
                StringBuilder reason = new StringBuilder(afterSaleRecord.getApplyReason());
                reason.append("（发起者：")
                        .append(AfterSaleWhoApplyType.findByValue(afterSaleRecord.getWhoApplyType()).getDesc())
                        .append("）");
                afterSaleRecordVO.setApplyReason(reason.toString());
            }
            afterSaleRecordVO.setAfterSaleRecordDetailList(buildAfterSaleRecordDetailVOList(afterSaleRecord.getAfterSaleRecordDetails(), productLabelVOMap, afsDetailBuildPair));
            afterSaleRecordVO.setRefundPicList(afterSaleRecord.getRefundPicList());
            //地址变更退费
            CommonUsedUtil.dealAddressChangeFeeToAfterSaleRecordVO(afterSaleRecord.getAddressChangeFee(), afterSaleRecordVO, true);

            afterSaleRecordVO.setReturnGoodsStatus(afterSaleRecord.getReturnGoodsStatus());
            // 地址相同屏蔽退货地址
            if (Objects.nonNull(afterSaleRecord.getPickUpRefundGoodsAddress())
                    && !receiveAddress.equals(CommonConstant.PRIVACY_PROTECT_ADDRESS)
                    && !afterSaleRecord.getPickUpRefundGoodsAddress().equals(receiveAddress)) {
                afterSaleRecordVO.setPickUpRefundGoodsAddress(afterSaleRecord.getPickUpRefundGoodsAddress());
            }
            afterSaleRecordVO.setRefundApplyType(afterSaleRecord.getRefundApplyType());
            afterSaleRecordVO.setChannelExtRefundType(afterSaleRecord.getChannelExtRefundType());
            afterSaleRecordVO.setIsNotImport(Objects.equals(afterSaleRecord.getExchangeStatusType(),
                    AfterSaleExchangeTypeEnum.NOT_IMPORT.getValue()));
            afterSaleRecordVO.setPreReturnFreight(afterSaleRecord.getPreReturnFreight());
            afterSaleRecordVO.setRefundGoodFreightType(afterSaleRecord.getRefundGoodFreightType());
            afterSaleRecordVO.setRefundGoodWay(afterSaleRecord.getRefundGoodWay());
            afterSaleRecordVOList.add(afterSaleRecordVO);
        }
        return afterSaleRecordVOList;
    }

    private static List<AfterSaleRecordDetailVO> buildAfterSaleRecordDetailVOList(List<AfterSaleRecordDetailVo> afterSaleRecordDetails, Map<Integer, ProductLabelVO> productLabelVOMap, Pair<Map<Long, ProductInfoVo>, Map<Long, CombinationChildProductVo>> afsDetailBuildPair) {
        List<AfterSaleRecordDetailVO> afterSaleRecordDetailVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(afterSaleRecordDetails)) {
            afterSaleRecordDetails.stream().forEach(detail -> {
                AfterSaleRecordDetailVO afterSaleRecordDetailVO = new AfterSaleRecordDetailVO();
                afterSaleRecordDetailVO.setChannelId(detail.getChannelId());
                afterSaleRecordDetailVO.setServiceId(detail.getServiceId());
                afterSaleRecordDetailVO.setSkuId(detail.getSkuId());
                afterSaleRecordDetailVO.setUpcCode(detail.getUpcCode());
                afterSaleRecordDetailVO.setSkuName(detail.getSkuName());
                afterSaleRecordDetailVO.setSpecification(detail.getSpecification());
                afterSaleRecordDetailVO.setSellUnit(detail.getSellUnit());
                afterSaleRecordDetailVO.setUnitPrice(detail.getUnitPrice());
                afterSaleRecordDetailVO.setCount(detail.getCount());
                afterSaleRecordDetailVO.setRefundAmt(detail.getRefundAmount());
                afterSaleRecordDetailVO.setRefundWeight(detail.getRefundWeight());
                afterSaleRecordDetailVO.setSubProductVoList(CombinationProductUtil.getAfsDetailComposeProductList(detail, afsDetailBuildPair.getValue()));
                afterSaleRecordDetailVO.setChannelLabelList(ProductLabelUtil.buildChannelLabelVOList(detail.getChannelLabel(), productLabelVOMap));
                afterSaleRecordDetailVO.setLabelSubDesc(ProductLabelUtil.buildAfterSaleChannelLabelSubDesc(detail.getChannelLabel(), detail.getChannelPickingStart(), detail.getChannelPickingEnd()));
                afterSaleRecordDetailVO.setAfsExchangeProduct(ExchangeItemUtil.buildAfsExchangeProduct(detail, afsDetailBuildPair.getKey()));
                afterSaleRecordDetailVOList.add(afterSaleRecordDetailVO);
            });
        }
        return afterSaleRecordDetailVOList;
    }

    /**
     * 判断是否支持拨打骑手隐私号（目前仅平台配送支持查询骑手隐私号）
     *
     * @param isSelfDelivery 是否为商家自配送 1:是  0:否
     * @return 是否支持拨打骑手隐私号
     */
    private static boolean judgeSupportDeliveryUserPrivacyPhone(Integer isSelfDelivery) {
        if (Objects.equals(isSelfDelivery, IS_SELF_DELIVERY_NO)) {
            // 平台配送，支持查询骑手隐私号
            return true;
        }
        return false;
    }

    public static Integer convertChannelId2OrderBizType(Integer channelId) {
        Integer resultBizType = null;
        if (channelId != null) {
            resultBizType = DynamicOrderBizType.channelId2OrderBizTypeValue(channelId);
            if (resultBizType == null) {
                log.error("未知渠道类型:{}", channelId);
            }
        }
        return resultBizType;
    }


    public static Boolean isDhScenePoi(Long storeId) {
        try {
            List<Long> stores = Lion.getConfigRepository().getList("dh_scene_poi_switch", Long.class, Lists.newArrayList());
            //全量逻辑
            if (stores.size() == 1 && stores.get(0).equals(-1L)) {
                return true;
            }
            return stores.contains(storeId);
        } catch (Exception e) {
            log.error("isDhScenePoi error", e);
            return false;
        }
    }

    private static Boolean isDrunkHorseTenant(Long tenantId) {
        List<Long> drunkHorseTenants = Lion.getConfigRepository().getList("drunkhorse.tenantIds", Long.class, new ArrayList<>());
        if (tenantId == null || CollectionUtils.isEmpty(drunkHorseTenants)) {
            return false;
        }
        return drunkHorseTenants.contains(tenantId);
    }

}
