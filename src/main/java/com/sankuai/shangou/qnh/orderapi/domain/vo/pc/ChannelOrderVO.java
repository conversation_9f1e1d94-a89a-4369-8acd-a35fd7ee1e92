package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/2 14:12
 * @Description:
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TypeDoc(
        description = "订单列表返回值"
)
public class ChannelOrderVO {

    /**
     * 订单ID
     */
    private String orderId;


    /**
     * 渠道编码
     */
    private String channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 门店 ID
     */
    private Long poiId;

    /**
     * 门店名称
     */
    private String poiName;


    /**
     * 数量
     */
    private int quantity;


    /**
     * 付款金额
     */
    private String paidAmount;


    /**
     * 商家实收金额
     */
    private String merchantAmount;


    /**
     * 配送方式
     */
    private String deliveryMethod;

    /**
     * 收货人名称
     */
    private String receiverName;

    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 收费人地址
     */
    private String receiverAddress;


    /**
     * 订单状态
     */
    private String status;


    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 预计送达时间
     */
    private String arrivalTime;

    /**
     * 退款状态
     */
    private String refundType;


    @FieldDoc(
            description = "可操作列表(10接单20完成拣货30补打小票40全单退款50部分退款60接收退款商品70退差价80订单调整)"
    )
    private List<String> couldOperateItemList;


    private String currentAfsType;

    private String orderType;

    private String memberCard;

    /**
     * 订单流水号
     */
    private long orderSerialNumber;

    /**
     * 商家活动支出
     */
    private String bizActivityAmt;

    /**
     * 线下总价
     */
    private String offlineTotalAmt;
    /**
     * 销售利润
     */
    private String saleBenefit;

    /**
     * 仓id
     */
    private Long warehouseId;

    /**
     * 仓名
     */
    private String warehouseName;

    /**
     * 订单用户类型
     * @see com.meituan.shangou.saas.order.platform.enums.UserTypeEnum
     */
    private Integer orderUserType;

    /**
     * 真实医药无人仓erp订单来源渠道id
     */
    private String realMedicineOrderChannelId;

    /**
     * 真实医药无人仓er订单来源渠道名称
     */
    private String realMedicineOrderChannelName;

    /**
     * 门店经营模式 1 直营 2 加盟
     */
    private Integer manageMode;

    /**
     * 地推单 1 推广现提  0/null 空
     */
    private Integer pullNewOrder;

    /**
     * 用户身份tag
     */
    private String userTag;

    /**
     * 实际送达时间
     */
    private String finishDeliveryTime;

    /**
     * 骑手牵牛花账号名
     */
    private String riderAccount;

    /**
     * 骑手名
     */
    private String riderName;

    /**
     * 组织结构二级
     */
    private String parentOrgName;

    /**
     * 组织结构三级
     */
    private String grandParentOrgName;

    /**
     * 订单重量
     */
    private Long deliveryWeight;

    /**
     * 配送导航距离
     */
    private Long deliveryDistance;

    /**
     * 加盟且灰度门店且未计算完成时返回false
     */
    private Boolean isMerchantAmountCalculated;

    /**
     * 是否为美团名酒馆订单，true：是
     */
    private Boolean isMtFamousTavern;

    /**
     * 是否为美团发财酒订单，true：是
     */
    private Boolean isFacaiWine;

    /**
     * 密钥
     */
    private String token;
}
