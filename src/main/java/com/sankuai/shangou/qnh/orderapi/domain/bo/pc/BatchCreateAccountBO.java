package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.BatchCreateAccountInfoDto;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/3/31
 * @desc
 */
@Data
public class BatchCreateAccountBO {
    private Long poiId;
    private String poiName;
    private String mobile;
    private Long empId;
    /**
     * 组织结构ID
     */
    private Long depId;


    /**
     * 如果这里roleIds为空的话，则在权限会读当前租户下的配置文件
     * @return
     */
    public BatchCreateAccountInfoDto buildAccountInfoDto(){
        BatchCreateAccountInfoDto batchCreateAccountInfoDto = new BatchCreateAccountInfoDto();
        batchCreateAccountInfoDto.setMobile(this.getMobile());
        batchCreateAccountInfoDto.setPoiId(this.getPoiId());
        batchCreateAccountInfoDto.setPoiName(this.getPoiName());
        batchCreateAccountInfoDto.setRoleIds(Lists.newArrayList());
        batchCreateAccountInfoDto.setEmpId(this.getEmpId());
        batchCreateAccountInfoDto.setDepId(this.getDepId());
        return batchCreateAccountInfoDto;

    }

    public static BatchCreateAccountBO buildThisByPoiInfoDto(PoiInfoDto poiInfoDto) {
        BatchCreateAccountBO batchCreateAccountBO = new BatchCreateAccountBO();
        batchCreateAccountBO.setPoiId(poiInfoDto.getPoiId());
        batchCreateAccountBO.setPoiName(poiInfoDto.getPoiName());
        batchCreateAccountBO.setMobile(poiInfoDto.getMobile());
        batchCreateAccountBO.setEmpId(poiInfoDto.getEmpId());
        batchCreateAccountBO.setDepId(poiInfoDto.getDepartmentId());
        return batchCreateAccountBO;
    }
}
