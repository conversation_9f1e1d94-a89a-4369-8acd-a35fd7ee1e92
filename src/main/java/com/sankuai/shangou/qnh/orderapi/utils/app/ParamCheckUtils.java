package com.sankuai.shangou.qnh.orderapi.utils.app;


import com.dianping.cat.util.StringUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;

public class ParamCheckUtils {
    public static void emptyCheck(String param, String errorMsg) {
        if(StringUtils.isEmpty(param)) {
            throw new IllegalArgumentException(errorMsg);
        }
    }

    public static void nullCheck(Object param, String errorMsg) {
        if(param == null) {
            throw new IllegalArgumentException(errorMsg);
        }
    }

    public static void pageNoCheck(int pageNo) {
        if(pageNo < 1) {
            throw new IllegalArgumentException("pageNo must >= 1");
        }
    }

    public static void pageSizeCheck(int pageSize) {
        if(pageSize < 1) {
            throw new IllegalArgumentException("pageSize must >= 1");
        }
    }

    public static boolean collectionAllEmpty(Collection... collections) {
        for (Collection collection : collections) {
            if (CollectionUtils.isNotEmpty(collection)) {
                return false;
            }
        }
        return true;
    }
}