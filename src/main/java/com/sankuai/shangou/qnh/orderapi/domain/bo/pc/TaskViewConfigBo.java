package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.shangou.qnh.orderapi.enums.pc.TaskModeEnum;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 任务中心，下拉任务列表配置
 *
 * <AUTHOR>
 * @date 2023/5/22
 */
@Data
@ToString
public class TaskViewConfigBo {

    public static final TaskViewConfigBo DEFAULT = new TaskViewConfigBo();

    /**
     * 是否禁用此任务，禁用后不透出给前端
     */
    private boolean disabled = false;

    /**
     * 该任务支持的业务线，不配置时使用默认使用任务类型上的配置 {@link TaskModeEnum#defaultSupportedBizModes}
     */
    private List<String> bizModes = Collections.emptyList();


    /**
     * 是否支持当前业务线
     *
     * @param taskMode 任务模式
     * @param bizMode  业务线
     * @return bool
     */
    public boolean supportBizMode(String taskMode, String bizMode) {
        if (this.disabled) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(this.bizModes)) {
            return this.bizModes.contains(bizMode);
        }
        TaskModeEnum taskModeEnum = TaskModeEnum.fromValue(taskMode);
        return taskModeEnum.getDefaultSupportedBizModes().contains(bizMode);
    }

}
