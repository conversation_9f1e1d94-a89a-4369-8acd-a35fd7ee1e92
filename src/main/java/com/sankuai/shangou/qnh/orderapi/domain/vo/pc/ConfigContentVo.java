package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigContentDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/4/26
 */
@Data
@ToString
public class ConfigContentVo {

    @FieldDoc(
            description = "配置id"
    )
    public Integer configId;

    @FieldDoc(
            description = "配置内容"
    )
    public String configContent;


    public static ConfigContentVo fromDto(ConfigContentDto dto) {
        ConfigContentVo vo = new ConfigContentVo();
        vo.setConfigId(dto.getConfigId());
        vo.setConfigContent(dto.getConfigContent());
        return vo;
    }

}
