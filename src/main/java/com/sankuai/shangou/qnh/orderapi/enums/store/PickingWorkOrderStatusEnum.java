package com.sankuai.shangou.qnh.orderapi.enums.store;

public enum PickingWorkOrderStatusEnum{
    /**
     * 初始状态
     */
    INITIAL(0),
    /**
     * 失效
     */
    EXPIRE(80),

    /**
     * 取消
     */
    CANCELED(90),
    /**
     * 已完成
     */
    PICK_DONE(100);

    private int code;

    public int getCode() {
        return code;
    }

    public static boolean isFinalStatus(int code) {
        return code == PICK_DONE.getCode()
                || code == CANCELED.getCode()
                || code == EXPIRE.getCode();
    }

    PickingWorkOrderStatusEnum(int code) {
        this.code = code;
    }

}
