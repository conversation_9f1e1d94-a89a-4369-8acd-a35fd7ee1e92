package com.sankuai.shangou.qnh.orderapi.service.common.query;

import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.dto.request.online.Wait2DeliveryCountRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.revenue.OCMSListOrderRevenueDetailRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListOrderResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsWaitToDeliveryOrderSubTypeCountResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.management.client.service.revenue.MerChantRevenueQueryService;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.FuseOrderStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.PageInfoVO;
import com.sankuai.shangou.qnh.orderapi.enums.pda.OrderTabSubTypeEnum;
import com.sankuai.shangou.qnh.orderapi.service.common.OrderListRequestContext;
import com.sankuai.shangou.qnh.orderapi.service.common.OrderRequestBuilder;
import com.sankuai.shangou.qnh.orderapi.service.common.OrderStatusUtil;
import com.sankuai.shangou.qnh.orderapi.service.common.PageUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.TimeUtils;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;


/**
 * <AUTHOR>
 * @since 2024/7/17
 **/
@Service
@Slf4j
public class Wait2DeliveryQueryOrderService extends QueryOrderService {

    @Resource
    private OrderRequestBuilder orderRequestBuilder;

    @Resource
    private MerChantRevenueQueryService merChantRevenueQueryService;

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    protected Pair<List<OCMSOrderVO>, PageInfoVO> queryOrderInfo(OrderListRequestContext request) {
        OCMSListOrderRevenueDetailRequest ocmsListOrderRequest = orderRequestBuilder.buildWaitToDeliveryOCMSListOrderRequest(request);
        log.info("OrderService.queryWaitToDeliveryOrderBySubType  调用ocmsQueryThriftService.listOrder request:{}", ocmsListOrderRequest);
        OCMSListOrderResponse response = merChantRevenueQueryService.listOrderRevenueDetail(ocmsListOrderRequest);
        if (response.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
        }
        return new Pair<>(response.getOcmsOrderList(), PageUtil.buildPageInfoVO(request.getPage(), request.getSize(), response.getTotalCount()));
    }

    public Integer countAll(Long tenantId, List<Long> storeIdList, Integer entityType) {
        Wait2DeliveryCountRequest request = buildWait2DeliveryCountRequest(tenantId, storeIdList, entityType, true);
        try {
            OcmsWaitToDeliveryOrderSubTypeCountResponse orderQuantityResponse = ocmsQueryThriftService.queryWaitToDeliveryOrderSubTypeCountV2(request);
            if (Objects.nonNull(orderQuantityResponse) && Objects.nonNull(orderQuantityResponse.getStatus())
                    && StatusCodeEnum.SUCCESS.getCode() == orderQuantityResponse.getStatus().getCode()){
                return orderQuantityResponse.getAllSubTypeCount();
            }
        }catch (Exception e){
            log.error( "查询配送订单数量异常, request:{}", request);
        }
        return 0;
    }

    private Wait2DeliveryCountRequest buildWait2DeliveryCountRequest(Long tenantId, List<Long> storeIdList, Integer entityType, boolean onlyTotalCount) {
        Wait2DeliveryCountRequest request = new Wait2DeliveryCountRequest();
        request.setTenantId(tenantId);
        request.setShopIdList(storeIdList);
        request.setDeliveryStatusList(Lists.newArrayList(
                DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER.getValue(),
                DistributeStatusEnum.DISTRIBUTE_REJECTED.getValue(),
                DistributeStatusEnum.RIDER_ASSIGNED.getValue(),
                DistributeStatusEnum.RIDER_REACH_SHOP.getValue(),
                DistributeStatusEnum.RIDER_TAKE_GOODS_FAILED.getValue(),
                DistributeStatusEnum.DISTRIBUTE_CANCELED.getValue(),
                DistributeStatusEnum.UN_KNOWN.getValue(),
                DistributeStatusEnum.DISTRIBUTE_UNKNOWN.getValue(),
                DistributeStatusEnum.RIDER_TAKE_GOODS.getValue()
        ));
        request.setOnlyTotalCount(onlyTotalCount);
        //查询当前时间之前7天的订单
        request.setBeginCreateTime(TimeUtils.getBeforeDayTimeStamp(MccConfigUtil.queryOrderCreateTimeBefore()));
        request.setEndCreateTime(System.currentTimeMillis());
        request.setViewFuseOrderStatusList(Lists.newArrayList(FuseOrderStatusEnum.PICKED.getValue(), FuseOrderStatusEnum.SHIPPING.getValue()));
        if(Objects.equals(entityType, PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code())){
            request.setShopIdList(new ArrayList<>());
            request.setWarehouseIdList(storeIdList);
        }
        return request;
    }

    public Map<OrderTabSubTypeEnum, Integer> countSubTap(Long tenantId, List<Long> storeIdList, Integer entityType) {
        Wait2DeliveryCountRequest request = buildWait2DeliveryCountRequest(tenantId, storeIdList, entityType, false);
        Map<OrderTabSubTypeEnum, Integer> rs = Maps.newHashMap();
        try {
            OcmsWaitToDeliveryOrderSubTypeCountResponse orderQuantityResponse = ocmsQueryThriftService.queryWaitToDeliveryOrderSubTypeCountV2(request);
            if (Objects.nonNull(orderQuantityResponse) && Objects.nonNull(orderQuantityResponse.getStatus())
                    && StatusCodeEnum.SUCCESS.getCode() == orderQuantityResponse.getStatus().getCode()){
                rs.put(OrderTabSubTypeEnum.ALL, orderQuantityResponse.getAllSubTypeCount());
                rs.put(OrderTabSubTypeEnum.WAIT_TO_RIDER_ACCEPT, orderQuantityResponse.getWaitToRiderAcceptCount());
                rs.put(OrderTabSubTypeEnum.WAIT_TO_TAKE_GOODS, orderQuantityResponse.getWaitToTakeGoodsCount());
                rs.put(OrderTabSubTypeEnum.WAIT_TO_ARRIVE_SHOP, orderQuantityResponse.getWaitToArriveShopCount());
                rs.put(OrderTabSubTypeEnum.DELIVERING, orderQuantityResponse.getDeliveringCount());
                rs.put(OrderTabSubTypeEnum.WAIT_TO_SEND_DELIVERY, orderQuantityResponse.getWaitToSendDeliveryCount());
                return rs;
            }
        }catch (Exception e){
            log.error( "查询配送订单数量异常, request:{}", request);
        }
        return rs;
    }

    @Override
    public void  addExtraInfo(OrderListResponse orderListResponse, OrderListRequestContext request) {
        OrderStatusUtil.setOrderListResponseViewStatusByWaitToDeliverySubType(orderListResponse);
    }

}
