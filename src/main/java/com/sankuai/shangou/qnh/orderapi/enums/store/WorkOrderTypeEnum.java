package com.sankuai.shangou.qnh.orderapi.enums.store;

import java.util.HashMap;
import java.util.Map;
public enum WorkOrderTypeEnum {
    FULFILL(0),
    DISPATCH(1),
    PICK(2),
    MERGE_FLOW(3),
    LACK_STOCK(4),
    PREPARE(5),

    /**
     * 出库类型工单
     */
    OUT_WAREHOUSE(6),
    ;

    private int value;

    WorkOrderTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    private static Map<Integer, WorkOrderTypeEnum> CODE_AND_WORK_ORDER_ENUM = new HashMap<>();

    static {
        for (WorkOrderTypeEnum each : values()) {
            CODE_AND_WORK_ORDER_ENUM.put(each.getValue(), each);
        }
    }

    public static WorkOrderTypeEnum fromCode(int code) {
        return CODE_AND_WORK_ORDER_ENUM.get(code);
    }
}
