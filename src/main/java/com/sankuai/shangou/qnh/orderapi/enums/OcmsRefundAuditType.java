package com.sankuai.shangou.qnh.orderapi.enums;

/***
 * author : <EMAIL>
 * data : 2022/12/6
 * time : 下午2:35
 **/
public enum OcmsRefundAuditType {

    Apply(1, "申请退款"),
    AgreeRefund(12, "同意退款"),
    DenyRefund(13, "驳回退款"),
    CancelRefund(20, "用户取消退款"),
    WeightRefund(30,"退差价")
    ;

    OcmsRefundAuditType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 渠道编码
     */
    private final int code;
    /**
     * 渠道名称
     */
    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

