package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.ResourceInfoVo;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/25
 * 应用权限业务对象
 **/
@Data
public class AppAuthBo {

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 权限树列表
     */
    private List<AuthBo> authList = Lists.newArrayList();

    /**
     * 权限ID映射
     */
    @JsonIgnore
    @JSONField(serialize = false)
    private Map<Long, AuthBo> authIdMap = Maps.newHashMap();


    public AppAuthBo build(Integer appId, String appName, List<ResourceInfoVo> resourceInfoList) {

        resourceInfoList = new ArrayList<>(resourceInfoList.stream().collect(Collectors.toMap(ResourceInfoVo::getId, a -> a, (oValue,nValue) -> nValue)).values());
        Collections.sort(resourceInfoList, Comparator.comparingInt(ResourceInfoVo::getRank));
        this.appId = appId;
        this.appName = appName;
        // 建立父节点和子节点列表的映射
        ArrayListMultimap<String, ResourceInfoVo> parentCodeMultiMap = ArrayListMultimap.create();
        resourceInfoList.forEach(e -> {
            // 排除脏数据，防止递归死锁
            if (!e.getParentCode().equals(e.getCode())) {
                parentCodeMultiMap.put(e.getParentCode(), e);
            }
        });
        // 获取根节点的权限ID
        List<Long> rootAuthIds = parentCodeMultiMap.get("").stream().map(ResourceInfoVo::getId).collect(Collectors.toList());
        // 从根开始递子节点
        buildSubAuth("", parentCodeMultiMap, authIdMap);
        // 初始化权限树列表
        rootAuthIds.forEach(e -> authList.add(authIdMap.get(e)));
        return this;
    }

    private List<AuthBo> buildSubAuth(String parentCode, ArrayListMultimap<String, ResourceInfoVo> parentCodeMultiMap, Map<Long, AuthBo> authIdMap) {
        List<AuthBo> subAuthList = Lists.newArrayList();
        // 不存在子节点则返回
        if (CollectionUtils.isEmpty(parentCodeMultiMap.get(parentCode))) {
            return subAuthList;
        }
        List<ResourceInfoVo> subResourceList = Lists.newArrayList(parentCodeMultiMap.get(parentCode));
        // 排除已经遍历过的节点，防止相互引用
        parentCodeMultiMap.removeAll(parentCode);
        // 遍历子权限列表
        subResourceList.forEach(item -> {
            Long authId = item.getId();
            AuthBo auth = authIdMap.get(authId);
            // 节点不存在则添加
            if (auth == null) {
                auth = new AuthBo().build(item);
                authIdMap.put(authId, auth);
            }
            subAuthList.add(auth);
            // 递归子权限的下级列表
            List<AuthBo> itemSubAuthList = buildSubAuth(item.getCode(), parentCodeMultiMap, authIdMap);
            // 设置子权限的下级列表
            auth.setSubAuthList(itemSubAuthList);
        });
        return subAuthList;
    }


}
