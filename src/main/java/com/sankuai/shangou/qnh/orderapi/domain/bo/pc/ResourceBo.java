package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.meituan.shangou.empower.auth.thrift.vo.DataPermissionVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PermissionVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.ResourceInfoVo;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2018/12/27 下午3:21
 */
@Data
public class ResourceBo {

    /**
     * 权限Id
     */
    private Long permissionId;

    /**
     * 权限Id
     */
    private Integer permissionAppId;

    /**
     * 权限编码
     */
    private String permissionCode;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 资源Id
     */
    private Long id;

    /**
     * appId
     */
    private Integer appId;

    /**
     * 资源code
     */
    private String code;

    /**
     * 父级资源code
     */
    private String parentCode;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 字段权限列标识,如 name=手机号, field=mobile  业务系统自行定义规则，用于存储列明。
     */
    private String field;

    /**
     * 资源url
     */
    private String uri;

    /**
     * 资源方法
     */
    private Integer method;

    /**
     * 资源类型 1-菜单 2-页面 3-按钮  4-字段
     */
    private Integer type;

    /**
     * 状态，是否有效0-停用，1-有效
     */
    private Integer valid;

    /**
     * 是否有权限（针对元素权限和列权限），0-无权限，1-有权限
     */
    private Integer isAuth;


    /**
     * 根据权限vo构造resource
     * @param vo
     */
    public ResourceBo(PermissionVo vo){
        this.permissionId = vo.getId();
        this.permissionAppId = vo.getAppId();
        this.permissionCode = vo.getCode();
        this.permissionName = vo.getName();

        List<ResourceInfoVo> resourceList = vo.getResourceInfoList();

        //权限系统中，resource和permission是一一对应的，但是形式上却是包含关系。
        resourceList.forEach( resourceInfoVo -> {
            this.id = resourceInfoVo.getId();
            this.appId = resourceInfoVo.getAppId();
            this.code = resourceInfoVo.getCode();
            this.parentCode = resourceInfoVo.getParentCode();
            this.name = resourceInfoVo.getName();
            this.field = resourceInfoVo.getField();
            this.uri = resourceInfoVo.getUrl();
            this.method = resourceInfoVo.getMethod();
            this.type = resourceInfoVo.getType();
            this.valid = resourceInfoVo.getValid();
        });
    }

    /**
     * 根据数据鉴权vo构造resource
     * @param vo
     */
    public ResourceBo(DataPermissionVo vo){
        this.id = vo.getResourceInfoVo().getId();
        this.appId = vo.getResourceInfoVo().getAppId();
        this.code = vo.getResourceInfoVo().getCode();
        this.parentCode = vo.getResourceInfoVo().getParentCode();
        this.name = vo.getResourceInfoVo().getName();
        this.field = vo.getResourceInfoVo().getField();
        this.uri = vo.getResourceInfoVo().getUrl();
        this.method = vo.getResourceInfoVo().getMethod();
        this.type = vo.getResourceInfoVo().getType();
        this.valid = vo.getResourceInfoVo().getValid();
        this.isAuth = vo.getIsAuth();
    }

}
