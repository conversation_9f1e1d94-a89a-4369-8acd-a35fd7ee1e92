package com.sankuai.shangou.qnh.orderapi.cache.store;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.meituan.shangou.saas.common.json.GsonUtils;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.H5AuthCodeDetail;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.H5LoginInfo;
import com.taobao.tair3.client.util.SerializableUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class H5LoginCache {

    @Autowired
    private RedisStoreClient redisStoreClient;

    private static final String CATEGORY = "storeapi";

    public void addCacheInfo(String key, Object value, int expireTimeInSeconds) {
        try {
            byte[] serialized = SerializableUtil.serialize(value);
            StoreKey storeKey = new StoreKey(CATEGORY, new Object[]{key});
            redisStoreClient.set(storeKey, serialized, expireTimeInSeconds);
        } catch (Exception e) {
            log.error("squirrel put error: key:{}, value:{}, msg:{}", new Object[]{key, GsonUtils.gson().toJson(value), e.getMessage(), e});
        }
    }

    public H5LoginInfo getCookie(String cookieStr) {
        byte[] result = redisStoreClient.get(new StoreKey(CATEGORY, new Object[]{cookieStr}));
        if (result != null) {
            return SerializableUtil.deserialize(result, H5LoginInfo.class);
        } else {
            log.info("获取缓存数据为空, key:{}", cookieStr);
            return null;
        }
    }

    public H5AuthCodeDetail getVerifyCode(String mobile) {
        byte[] result = redisStoreClient.get(new StoreKey(CATEGORY, new Object[]{mobile}));
        if (result != null) {
            return SerializableUtil.deserialize(result, H5AuthCodeDetail.class);
        } else {
            log.info("获取缓存数据为空, key:{}", mobile);
            return null;
        }
    }
}
