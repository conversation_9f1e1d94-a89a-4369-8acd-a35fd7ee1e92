package com.sankuai.shangou.qnh.orderapi.controller.store;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.meituan.reco.pickselect.common.flowcontrol.annotation.RhinoValve;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.goodscenter.dto.GoodsExpirationDto;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderItemModel;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.model.ChildSkuModel;
import com.meituan.shangou.saas.o2o.dto.model.ComposeSkuModel;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.RiderPickTaskDTO;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.RiderPickWorkOrderDTO;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.QueryDeliveryProofPhotoResponse;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderItemDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderItemDTO;
import com.sankuai.shangou.qnh.orderapi.annotation.store.Auth;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseFinanceDetailBO;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.*;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.DeliveryCompleteInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.GiftVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.LackStockGoodsVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.*;
import com.sankuai.shangou.qnh.orderapi.enums.DepotGoodsExpireUnitEnum;
import com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.remote.*;
import com.sankuai.shangou.qnh.orderapi.service.pc.FuseOrderService;
import com.sankuai.shangou.qnh.orderapi.service.store.AuthThriftWrapperService;
import com.sankuai.shangou.qnh.orderapi.service.store.SaleReturnService;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.store.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/5/5
 * desc:
 */
@InterfaceDoc(
        displayName = "中台订单服务",
        type = "restful",
        scenarios = "包含查询摊位订单列表和摊位订单详情",
        description = "查询摊位订单列表和摊位订单详情",
        host = "https://storeapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "中台订单服务")
@RestController
@RequestMapping("/storemanagement/ocms/order")
public class OCMSOrderController {

    @Resource
    private AuthThriftWrapperService authThriftWrapper;

    @Resource
    private OCMSRemoteService ocmsServiceWrapper;

    @Resource
    private OCMSOrderRemoteService ocmsOrderServiceWrapper;

    @Resource
    private TenantRemoteService tenantWrapper;

    @Resource(name = "storeSaleReturnService")
    private SaleReturnService saleReturnService;

    @Resource
    private PickSelectStoreRemoteService pickSelectFacade;

    @Resource
    private RiderDeliveryRemoteService riderDeliveryFacade;

    @Resource
    private AbnOrderRemoteService abnOrderRemoteService;
    @Resource
    private DepotGoodsService depotGoodsService;
    @Resource
    private OrderBizRemoteService orderBizRemoteService;

    @Resource
    private GoodsCenterRemoteService goodsCenterRemoteService;
    @Resource
    private TradeShippingOrderRemoteService tradeShippingOrderRemoteService;

    @Resource
    private SupplyProductClient supplyProductClient;

    @Autowired
    private FuseOrderService fuseOrderService;


    @MethodDoc(
            description = "查询摊位订单列表",
            displayName = "查询摊位订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询摊位订单列表请求",
                            type = GetOrderForBoothRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"\",\n" +
                    "    \"data\": {\n" +
                    "        \"totalAmount\": 23.49,\n" +
                    "        \"orders\": [\n" +
                    "            {\n" +
                    "                \"channelOrderId\": \"a9169d19-a721a-4e81-b\",\n" +
                    "                \"channelId\": 200,\n" +
                    "                \"amount\": 12.34,\n" +
                    "                \"orderItems\": null\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"channelOrderId\": \"a9169d19-a72a-4e81-b\",\n" +
                    "                \"channelId\": 200,\n" +
                    "                \"amount\": 11.15,\n" +
                    "                \"orderItems\": null\n" +
                    "            }\n" +
                    "        ]\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/order/getorderforboothlist",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/getorderforboothlist", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<GetOrderForBoothResponseVO> getOrderForBoothList(@Valid @RequestBody GetOrderForBoothRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        List<String> boothIdList = getBoothIdList(request.getStoreId());
        return ocmsServiceWrapper.getOrderForBoothList(user, boothIdList, request);
    }

    @MethodDoc(
            description = "查询摊位订单详情",
            displayName = "查询摊位订单详情",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询摊位订单详情请求",
                            type = GetOrderDetailForBootRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"\",\n" +
                    "    \"data\": {\n" +
                    "        \"channelOrderId\": \"a9169d19-a721a-4e81-b\",\n" +
                    "        \"channelId\": 200,\n" +
                    "        \"amount\": 12.34,\n" +
                    "        \"orderItems\": [\n" +
                    "            {\n" +
                    "                \"skuName\": \"摊位1\",\n" +
                    "                \"quantity\": 1,\n" +
                    "                \"sellUnit\": \"g\",\n" +
                    "                \"skuCode\": \"1234\",\n" +
                    "                \"offlinePrice\": 12.34,\n" +
                    "                \"amount\": 12.34\n" +
                    "            }\n" +
                    "        ]\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/order/getorderdetailforbooth",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/getorderdetailforbooth", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<OrderForBoothVO> getOrderDetailForBooth(@Valid @RequestBody GetOrderDetailForBootRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        List<String> boothIdList = getBoothIdList(request.getStoreId());
        return ocmsServiceWrapper.getOrderDetailForBooth(user, boothIdList, request);
    }

    @MethodDoc(
            displayName = "分页查询订单列表",
            description = "分页查询订单列表，分页查询订单概要信息，包含对应商品列表。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询订单列表请求",
                            type = OrderListRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": {\n" +
                    "        \"pageInfo\": {\n" +
                    "            \"page\": 1,\n" +
                    "            \"size\": 2,\n" +
                    "            \"totalPage\": 20,\n" +
                    "            \"totalSize\": 39\n" +
                    "        },\n" +
                    "        \"orderList\": [\n" +
                    "            {\n" +
                    "                \"tenantId\": \"1000011\",\n" +
                    "                \"channelId\": 300,\n" +
                    "                \"channelName\": \"京东到家\",\n" +
                    "                \"storeId\": \"1000017\",\n" +
                    "                \"storeName\": \"中台测试门店(共用饿了么京东渠道)\",\n" +
                    "                \"channelOrderId\": \"916923738000441\",\n" +
                    "                \"serialNo\": \"0\",\n" +
                    "                \"itemCount\": 7,\n" +
                    "                \"actualPayAmt\": 3810,\n" +
                    "                \"bizReceiveAmt\": 3091,\n" +
                    "                \"deliveryMethod\": 1,\n" +
                    "                \"deliveryMethodDesc\": \"达达专送\",\n" +
                    "                \"deliveryUserName\": null,\n" +
                    "                \"deliveryUserPhone\": null,\n" +
                    "                \"receiverName\": \"闻雄\",\n" +
                    "                \"receiverPhone\": \"13107111899,1425\",\n" +
                    "                \"receiveAddress\": \"长沙市岳麓区通程商业广场(麓山店)A座测试1号\",\n" +
                    "                \"channelOrderStatus\": 50,\n" +
                    "                \"channelOrderStatusDesc\": \"订单已取消\",\n" +
                    "                \"createTime\": \"1563195738000\",\n" +
                    "                \"refundAmt\": 0,\n" +
                    "                \"refundTagId\": 100,\n" +
                    "                \"refundTagDesc\": \"无\",\n" +
                    "                \"auditingRefundTagId\": 0,\n" +
                    "                \"auditingRefundTagDesc\": null,\n" +
                    "                \"couldOperateItemList\": [],\n" +
                    "                \"deliveryOrderType\": 0,\n" +
                    "                \"deliveryOrderTypeName\": \"即时订单\",\n" +
                    "                \"estimatedSendArriveTimeStart\": \"1563268500000\",\n" +
                    "                \"estimatedSendArriveTimeEnd\": \"1563268500000\",\n" +
                    "                \"pickupStatus\": 1,\n" +
                    "                \"pickupCompleteTime\": \"1563195817095\",\n" +
                    "                \"distributeStatus\": 1,\n" +
                    "                \"offlineOrderStatus\": 25,\n" +
                    "                \"updateTime\": \"1563246618000\",\n" +
                    "                \"channelExtraOrderId\": null,\n" +
                    "                \"comments\": null,\n" +
                    "                \"productList\": [\n" +
                    "                    {\n" +
                    "                        \"skuId\": \"1145223620684083287\",\n" +
                    "                        \"upcCode\": \"\",\n" +
                    "                        \"skuName\": \"快捷优化00006 无\",\n" +
                    "                        \"picUrl\": null,\n" +
                    "                        \"specification\": \"500g/份\",\n" +
                    "                        \"sellUnit\": \"1\",\n" +
                    "                        \"originalTotalPrice\": null,\n" +
                    "                        \"totalPayAmount\": null,\n" +
                    "                        \"totalDiscountAmount\": null,\n" +
                    "                        \"unitPrice\": 480,\n" +
                    "                        \"count\": 7,\n" +
                    "                        \"isRefund\": 1,\n" +
                    "                        \"refundCount\": 7,\n" +
                    "                        \"boothName\": null,\n" +
                    "                        \"offlinePrice\": 750,\n" +
                    "                        \"stallSettleAmt\": 5250,\n" +
                    "                        \"realQuantity\": null,\n" +
                    "                        \"customerSkuId\": \"1145223620684083287\"\n" +
                    "                    }\n" +
                    "                ]\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"tenantId\": \"1000011\",\n" +
                    "                \"channelId\": 300,\n" +
                    "                \"channelName\": \"京东到家\",\n" +
                    "                \"storeId\": \"1000017\",\n" +
                    "                \"storeName\": \"中台测试门店(共用饿了么京东渠道)\",\n" +
                    "                \"channelOrderId\": \"916921041000242\",\n" +
                    "                \"serialNo\": \"0\",\n" +
                    "                \"itemCount\": 7,\n" +
                    "                \"actualPayAmt\": 3810,\n" +
                    "                \"bizReceiveAmt\": 3091,\n" +
                    "                \"deliveryMethod\": 1,\n" +
                    "                \"deliveryMethodDesc\": \"达达专送\",\n" +
                    "                \"deliveryUserName\": null,\n" +
                    "                \"deliveryUserPhone\": null,\n" +
                    "                \"receiverName\": \"闻雄\",\n" +
                    "                \"receiverPhone\": \"13107216795,1232\",\n" +
                    "                \"receiveAddress\": \"长沙市岳麓区通程商业广场(麓山店)A座测试1号\",\n" +
                    "                \"channelOrderStatus\": 50,\n" +
                    "                \"channelOrderStatusDesc\": \"订单已取消\",\n" +
                    "                \"createTime\": \"1563193202000\",\n" +
                    "                \"refundAmt\": 0,\n" +
                    "                \"refundTagId\": 200,\n" +
                    "                \"refundTagDesc\": \"全部\",\n" +
                    "                \"auditingRefundTagId\": 0,\n" +
                    "                \"auditingRefundTagDesc\": null,\n" +
                    "                \"couldOperateItemList\": [],\n" +
                    "                \"deliveryOrderType\": 0,\n" +
                    "                \"deliveryOrderTypeName\": \"即时订单\",\n" +
                    "                \"estimatedSendArriveTimeStart\": \"1563277500000\",\n" +
                    "                \"estimatedSendArriveTimeEnd\": \"1563277500000\",\n" +
                    "                \"pickupStatus\": 48,\n" +
                    "                \"pickupCompleteTime\": \"1563278626916\",\n" +
                    "                \"distributeStatus\": 48,\n" +
                    "                \"offlineOrderStatus\": 25,\n" +
                    "                \"updateTime\": \"1563332845000\",\n" +
                    "                \"channelExtraOrderId\": null,\n" +
                    "                \"comments\": null,\n" +
                    "                \"productList\": [\n" +
                    "                    {\n" +
                    "                        \"skuId\": \"1145223620684083287\",\n" +
                    "                        \"upcCode\": \"\",\n" +
                    "                        \"skuName\": \"快捷优化00006 无\",\n" +
                    "                        \"picUrl\": null,\n" +
                    "                        \"specification\": \"500g/份\",\n" +
                    "                        \"sellUnit\": \"1\",\n" +
                    "                        \"originalTotalPrice\": null,\n" +
                    "                        \"totalPayAmount\": null,\n" +
                    "                        \"totalDiscountAmount\": null,\n" +
                    "                        \"unitPrice\": 480,\n" +
                    "                        \"count\": 7,\n" +
                    "                        \"isRefund\": 1,\n" +
                    "                        \"refundCount\": 7,\n" +
                    "                        \"boothName\": null,\n" +
                    "                        \"offlinePrice\": 750,\n" +
                    "                        \"stallSettleAmt\": 5250,\n" +
                    "                        \"realQuantity\": null,\n" +
                    "                        \"customerSkuId\": \"1145223620684083287\"\n" +
                    "                    }\n" +
                    "                ]\n" +
                    "            }\n" +
                    "        ]\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/order/orderlist",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/orderlist", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<OrderListResponse> orderList(@Valid @RequestBody OrderListRequest request) {
        CommonResponse<OrderListResponse> orderListResponseCommonResponse = ocmsOrderServiceWrapper.orderList(request);
        if (MccDynamicConfigUtil.getWmsjTenantIds().contains(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())
        && MccDynamicConfigUtil.isNewPickGrayStore(request.getStoreId())) {
            if (Objects.nonNull(orderListResponseCommonResponse.getData()) && CollectionUtils.isNotEmpty(orderListResponseCommonResponse.getData().getOrderList())) {
                appendLackStockInfo(request, orderListResponseCommonResponse);
            }
        }
        return orderListResponseCommonResponse;
    }

    private void appendLackStockInfo(OrderListRequest request, CommonResponse<OrderListResponse> orderListResponseCommonResponse) {
        try {
            if (orderListResponseCommonResponse.getCode() == 0
                    && Objects.nonNull(orderListResponseCommonResponse.getData())
                    && CollectionUtils.isNotEmpty(orderListResponseCommonResponse.getData().getOrderList())) {
                List<AbnOrderDTO> allUnprocessedAbnOrders = abnOrderRemoteService.getAllUnprocessedAbnOrder(request.getStoreId());
                if (CollectionUtils.isEmpty(allUnprocessedAbnOrders)) {
                    return;
                }
                Map<String, AbnOrderDTO> sourceOrderNoAbnMap = allUnprocessedAbnOrders.stream().collect(Collectors.toMap(
                        AbnOrderDTO::getSourceOrderNo,
                        Function.identity(),
                        (older, newer) -> newer
                ));
                orderListResponseCommonResponse.getData().getOrderList().forEach(
                    orderVO -> {
                        if (sourceOrderNoAbnMap.containsKey(orderVO.getChannelOrderId()) && CollectionUtils.isNotEmpty(sourceOrderNoAbnMap.get(orderVO.getChannelOrderId()).getItems())) {
                            orderVO.setHasLackGoods(true);
                        }
                    }
                );
            }

        } catch (Exception e) {
            log.error("appendLackStockInfo error", e);
        }
    }

    // 牵牛花APP端订单详情入口
    @MethodDoc(
            displayName = "查询订单详情",
            description = "查询订单详情，查询制定渠道订单详情，包含商品、售后、促销、订单状态日志、配送状态日志等信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询订单详情",
                            type = OrderDetailRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": {\n" +
                    "        \"orderDetail\": {\n" +
                    "            \"channelOrderId\": \"916923738000441\",\n" +
                    "            \"storeId\": \"1000017\",\n" +
                    "            \"storeName\": \"京东到家\",\n" +
                    "            \"channelName\": \"京东到家\",\n" +
                    "            \"receiverName\": \"闻雄\",\n" +
                    "            \"receiverPhone\": \"13107111899,1425\",\n" +
                    "            \"receiveAddress\": \"长沙市岳麓区通程商业广场(麓山店)A座测试1号\",\n" +
                    "            \"originalAmt\": 3810,\n" +
                    "            \"actualPayAmt\": 3810,\n" +
                    "            \"bizReceiveAmt\": 3091,\n" +
                    "            \"freight\": 400,\n" +
                    "            \"packageAmt\": 50,\n" +
                    "            \"platformFee\": 0,\n" +
                    "            \"isNeedInvoice\": 0,\n" +
                    "            \"invoiceTitle\": null,\n" +
                    "            \"taxNo\": null,\n" +
                    "            \"deliveryMethod\": 1,\n" +
                    "            \"deliveryMethodName\": \"达达专送\",\n" +
                    "            \"deliveryUserName\": \"未知\",\n" +
                    "            \"deliveryUserPhone\": \"未知\",\n" +
                    "            \"estimatedSendArriveTimeStart\": \"0\",\n" +
                    "            \"estimatedSendArriveTimeEnd\": \"0\",\n" +
                    "            \"payMethod\": 0,\n" +
                    "            \"payMethodDesc\": \"未知\",\n" +
                    "            \"channelOrderStatus\": 50,\n" +
                    "            \"channelOrderStatusDesc\": \"订单已取消\",\n" +
                    "            \"createTime\": \"1563195738000\",\n" +
                    "            \"lastAfterSaleApplyRefundTagId\": 200,\n" +
                    "            \"lastAfterSaleApplyReason\": \"不想买了\",\n" +
                    "            \"lastAfterSaleApplyStatus\": 6,\n" +
                    "            \"lastAfterSaleApplyRejectReason\": null,\n" +
                    "            \"refundableOrderAmount\": 0,\n" +
                    "            \"updateTime\": \"1563246618000\",\n" +
                    "            \"distributeStatus\": 1,\n" +
                    "            \"pickupStatus\": 0,\n" +
                    "            \"channelExtraOrderId\": null,\n" +
                    "            \"comments\": null,\n" +
                    "            \"productList\": [\n" +
                    "                {\n" +
                    "                    \"skuId\": \"1145223620684083287\",\n" +
                    "                    \"upcCode\": \"\",\n" +
                    "                    \"skuName\": \"快捷优化00006 无\",\n" +
                    "                    \"picUrl\": null,\n" +
                    "                    \"specification\": \"500g/份\",\n" +
                    "                    \"sellUnit\": \"1\",\n" +
                    "                    \"originalTotalPrice\": null,\n" +
                    "                    \"totalPayAmount\": null,\n" +
                    "                    \"totalDiscountAmount\": null,\n" +
                    "                    \"unitPrice\": 480,\n" +
                    "                    \"count\": 7,\n" +
                    "                    \"isRefund\": 1,\n" +
                    "                    \"refundCount\": 7,\n" +
                    "                    \"boothName\": null,\n" +
                    "                    \"offlinePrice\": 750,\n" +
                    "                    \"stallSettleAmt\": 5250,\n" +
                    "                    \"realQuantity\": null,\n" +
                    "                    \"customerSkuId\": \"1145223620684083287\"\n" +
                    "                    \"exchangeOrderDetailList\":[\n" +
                    "                       {\n" +
                    "                           \"exchangeFromCnt\":1,\n" +
                    "                           \"exchangeToCnt\":2,\n" +
                    "                           \"productVO\":{}\n" +
                    "                       }\n" +
                    "                   ]\n" +
                    "                }\n" +
                    "            ],\n" +
                    "            \"promotionList\": [],\n" +
                    "            \"orderStatuslogList\": [],\n" +
                    "            \"afterSaleRecordList\": [\n" +
                    "                {\n" +
                    "                    \"storeId\": \"1000017\",\n" +
                    "                    \"tenantId\": \"1000011\",\n" +
                    "                    \"channelId\": 300,\n" +
                    "                    \"serviceId\": \"1150965739566592023\",\n" +
                    "                    \"isAudit\": 1,\n" +
                    "                    \"status\": 6,\n" +
                    "                    \"applyReason\": \"不想买了\",\n" +
                    "                    \"afsPattern\": 1,\n" +
                    "                    \"refundAmt\": 3810,\n" +
                    "                    \"createTime\": \"1563246590000\",\n" +
                    "                    \"updateTime\": \"1563246618000\",\n" +
                    "                    \"afsApplyType\": 1,\n" +
                    "                    \"whoApplyType\": 1,\n" +
                    "                    \"afterSaleRecordDetailList\": []\n" +
                    "                }\n" +
                    "            ]\n" +
                    "        }\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/order/orderdetail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/orderdetail", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<OrderDetailResponse> orderDetail(@Valid @RequestBody OrderDetailRequest request) {
        CommonResponse<OrderDetailResponse> orderDetailResponse = ocmsOrderServiceWrapper.orderDetail(request);

        if(Objects.nonNull(orderDetailResponse) && Objects.nonNull(orderDetailResponse.getData())
                && Objects.nonNull(orderDetailResponse.getData().getOrderDetail())) {
            OrderDetailVO orderDetail = orderDetailResponse.getData().getOrderDetail();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            OrderFuseFinanceDetailBO orderFuseFinanceDetailBO = fuseOrderService.queryFinanceDetail(orderDetail.getOrderId(), orderDetail.getStoreId(),
                    user.getTenantId(), 1);
            orderDetail.mergeFinanceInfo(orderFuseFinanceDetailBO);
            //判断是否有售后组合商品
            List<Long> refundComposeServiceIds = getRefundComposeDetailServiceId(orderDetail.getAfterSaleRecordList());
            if(CollectionUtils.isNotEmpty(refundComposeServiceIds)){
                List<OrderFuseFinanceDetailBO> orderFuseFinanceDetailBOList = fuseOrderService.queryFinanceDetails(refundComposeServiceIds, orderDetail.getStoreId(),
                        user.getTenantId(), 2);
                log.info("OCMSOrderController.orderDetail orderFuseFinanceDetailBOList: {}", JSON.toJSONString(orderFuseFinanceDetailBOList));
                orderDetail.mergeFinanceRefundCompose(orderFuseFinanceDetailBOList);
            }
        }


        saleReturnService.tryAppendSaleReturnOrdersInfo(orderDetailResponse);

        //填充拣货信息
        appendPickInfo(orderDetailResponse, request);

        //填充配送信息
        appendDeliveryInfo(orderDetailResponse);

        //填充缺货信息
        appendLackStockInfo(orderDetailResponse, request);

        return orderDetailResponse;
    }

    private void appendPickInfo(CommonResponse<OrderDetailResponse> orderDetailResponse, OrderDetailRequest request) {
        if (MccDynamicConfigUtil.getWmsjTenantIds().contains(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {

            if (orderDetailResponse == null || orderDetailResponse.getData() == null || orderDetailResponse.getData().getOrderDetail() == null) {
                return;
            }

            try {
                DynamicOrderBizType dynamicOrderBizType = DynamicOrderBizType.channelId2OrderBizType(request.getChannelId());
                TradeShippingOrderDTO tradeShippingOrderDTO = tradeShippingOrderRemoteService.getByChannelOrderId(request.getStoreId(), dynamicOrderBizType.getValue(), request.getChannelOrderId());
                Map<String, GoodsExpirationDto> goodsExpirationMap = getGoodsExpirationMap(request.getTenantId(), request.getStoreId(), tradeShippingOrderDTO);
                if (Objects.nonNull(tradeShippingOrderDTO)) {
                    orderDetailResponse.getData().setPickingCheckPictureUrlList(tradeShippingOrderDTO.getPickingCheckPictureUrlList());
                    orderDetailResponse.getData().setPickItemInfoList(buildPickItemInfoList(tradeShippingOrderDTO, goodsExpirationMap));

                    PickInfoVO pickInfoVO = new PickInfoVO();
                    pickInfoVO.setPickItemInfoList(buildPickItemInfoList(tradeShippingOrderDTO, goodsExpirationMap));
                    pickInfoVO.setPickStatus(tradeShippingOrderDTO.getStatus());
                    pickInfoVO.setIsPickDeliverySplit(tradeShippingOrderDTO.getIsPickDeliverySplit());
                    pickInfoVO.setPickAccountId(tradeShippingOrderDTO.getOperatorId());
                    pickInfoVO.setPickerName(tradeShippingOrderDTO.getOperatorName());
                    pickInfoVO.setPickingCheckPictureUrlList(tradeShippingOrderDTO.getPickingCheckPictureUrlList());
                    orderDetailResponse.getData().setPickInfo(pickInfoVO);
                }

                appendHighPriceTag(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), orderDetailResponse);
            } catch (Exception e) {
                //不阻塞流程
                Cat.logEvent("ADAPT_SH", "ORDER_DETAIL_QUERY_PICK_INFO_FAIL");
                log.warn("查询拣货工单详情失败", e);
            }
        }
    }

    private void appendDeliveryInfo(CommonResponse<OrderDetailResponse> orderDetailResponse) {
        if (MccDynamicConfigUtil.getWmsjTenantIds().contains(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {

            if (orderDetailResponse == null || orderDetailResponse.getData() == null || orderDetailResponse.getData().getOrderDetail() == null) {
                return;
            }

            try {
                QueryDeliveryProofPhotoResponse proofPhotoResponse = riderDeliveryFacade.getDeliveryProofPhotoInfo(orderDetailResponse.getData().getOrderDetail().getOrderId());

                List<DeliveryCompleteInfoVO.ProofPhotoInfo> proofPhotoInfoList = proofPhotoResponse.getTDeliveryProofPhotoInfoList().stream().map(photoInfo -> {
                    DeliveryCompleteInfoVO.ProofPhotoInfo proofPhotoInfoVO = new DeliveryCompleteInfoVO.ProofPhotoInfo();
                    proofPhotoInfoVO.setAuditStatus(photoInfo.getAuditStatus());
                    proofPhotoInfoVO.setProofPhotoUrl(photoInfo.getUrl());
                    return proofPhotoInfoVO;
                }).collect(Collectors.toList());
                orderDetailResponse.getData().setDeliveryCompleteInfo(new DeliveryCompleteInfoVO(proofPhotoInfoList, proofPhotoResponse.getSignType(), proofPhotoResponse.getIsWeakNetwork()));
                orderDetailResponse.getData().getOrderDetail().setIsPickDeliverySplit(proofPhotoResponse.getIsPickDeliverySplit());
            } catch (Exception e) {
                //不阻塞流程
                log.warn("查询送达照片失败，orderDetailResponse: {}", orderDetailResponse, e);
            }
        }
    }

    private void appendLackStockInfo(CommonResponse<OrderDetailResponse> orderDetailResponse, OrderDetailRequest request) {
        if (MccDynamicConfigUtil.getWmsjTenantIds().contains(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {

            if (orderDetailResponse == null || orderDetailResponse.getData() == null || orderDetailResponse.getData().getOrderDetail() == null) {
                return;
            }

            try {
                if (Objects.nonNull(orderDetailResponse.getData()) && MccDynamicConfigUtil.isNewPickGrayStore(request.getStoreId())) {
                    //调用订单拿到商品组合关系
                    BizOrderModel bizOrderModel = orderBizRemoteService.queryOrderDetail(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), orderDetailResponse.getData().getOrderDetail().getStoreId(),
                            orderDetailResponse.getData().getOrderDetail().getChannelId(), orderDetailResponse.getData().getOrderDetail().getChannelOrderId());
                    fillGiftInfo(orderDetailResponse.getData(), bizOrderModel);
                    fillLackStockGoodsInfo(orderDetailResponse.getData(), bizOrderModel);
                }
            } catch (Exception e) {
                //不阻塞流程
                log.error("查询异常缺货信息失败, orderDetailResponse: {}", orderDetailResponse, e);
            }

        }
    }

    private void appendHighPriceTag(Long tenantId, CommonResponse<OrderDetailResponse> orderDetailResponse){
        try {
            if (Objects.isNull(orderDetailResponse)
                    || Objects.isNull(orderDetailResponse.getData())
                    || Objects.isNull(orderDetailResponse.getData().getOrderDetail())
                    || !MccConfigUtil.isHighPriceTagGrayStore(orderDetailResponse.getData().getOrderDetail().getStoreId())) {
                return ;
            }

            if (CollectionUtils.isEmpty(orderDetailResponse.getData().getPickItemInfoList())) {
                return;
            }

            List<String> skuIds = orderDetailResponse.getData().getPickItemInfoList()
                    .stream()
                    .map(PickItemVO::getSkuId)
                    .collect(Collectors.toList());

            Map<String, Boolean> productIsHighPriceMap = supplyProductClient.batchGetProductHighPriceTag(tenantId, skuIds);

            for (PickItemVO pickItem : orderDetailResponse.getData().getPickItemInfoList()) {
                pickItem.setIsHighWacGoods(productIsHighPriceMap.getOrDefault(pickItem.getSkuId(), false));
            }
        } catch (Exception e) {
            log.warn("查询商品高价值标签失败", e);
            Cat.logEvent("QUERY_HIGH_PRICE_TAG", "FAIL");
        }
    }

    private List<Long> getRefundComposeDetailServiceId(List<AfterSaleRecordVO> afterSaleRecordList) {
        List<Long> list = new ArrayList<>();
        try {
            if(CollectionUtils.isEmpty(afterSaleRecordList)){
                return list;
            }
            log.info("OCMSOrderController.hasRefundComposeDetail afterSaleRecordList: {}", JSON.toJSONString(afterSaleRecordList));
            for(AfterSaleRecordVO afterSaleRecordVO : afterSaleRecordList){
                if(CollectionUtils.isEmpty(afterSaleRecordVO.getAfterSaleRecordDetailList())){
                    continue;
                }
                for (AfterSaleRecordDetailVO afterSaleRecordDetailVO : afterSaleRecordVO.getAfterSaleRecordDetailList()){
                    if(CollectionUtils.isEmpty(afterSaleRecordDetailVO.getSubProductVoList())){
                        continue;
                    }
                    Long serviceId = afterSaleRecordDetailVO.getServiceId();
                    list.add(serviceId);
                    continue;
                }
            }
            //去重复
            if(CollectionUtils.isNotEmpty(list)){
                return list.stream().distinct().collect(Collectors.toList());
            }

        }catch (Exception e){
            log.warn("OCMSOrderController.hasRefundComposeDetail is error afterSaleRecordList: {} , e= ", JSON.toJSONString(afterSaleRecordList), e);
        }
        return list;
    }

    @MethodDoc(
            displayName = "部分退款",
            description = "部分退款，针对指定渠道订单的指定商品列表进行部分退款。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "部分退款",
                            type = PartRefundRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/storemanagement/ocms/order/partrefund",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/partrefund", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse partRefund(@Valid @RequestBody PartRefundRequest request) {
        return ocmsOrderServiceWrapper.partRefund(request);
    }

    @MethodDoc(
            displayName = "取消订单",
            description = "取消订单，即完成指定渠道订单整单退款。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "取消订单",
                            type = CancelOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/storemanagement/ocms/order/cancelorder",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/cancelorder", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse cancelOrder(@Valid @RequestBody CancelOrderRequest request) throws TException{
        return ocmsOrderServiceWrapper.cancelOrder(request);
    }

    @MethodDoc(
            displayName = "打印小票",
            description = "打印小票，打印指定渠道订单小票。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "打印小票",
                            type = PrintReceiptRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/storemanagement/ocms/order/printreceipt",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/printreceipt", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<PrintReceiptResponse> printReceipt(@Valid @RequestBody PrintReceiptRequest request) {
        return ocmsOrderServiceWrapper.printReceipt(request);
    }

    @MethodDoc(
            displayName = "查询打印状态",
            description = "查询打印状态，根据printId查询对应打印状态。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询打印状态",
                            type = QueryPrintStatusRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/storemanagement/ocms/order/queryprintstatus",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryprintstatus", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<QueryPrintStatusResponse> queryPrintStatus(@Valid @RequestBody QueryPrintStatusRequest request) {
        return ocmsOrderServiceWrapper.queryPrintStatus(request);
    }

    @MethodDoc(
            description = "查询虚拟电话",
            displayName = "查询虚拟电话",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询虚拟电话",
                            type = QueryVirtualPhoneRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\\n\" +\n" +
                    "                    \"    \\\"code\\\": 0,\\n\" +\n" +
                    "                    \"    \\\"message\\\": \\\"操作成功\\\",\\n\" +\n" +
                    "                    \"    \\\"data\\\": {\\n\" +\n" +
                    "                    \"        \\\"phoneNo\\\": \\\"13107111899,1425\\\"\\n\" +\n" +
                    "                    \"    }\\n\" +\n" +
                    "                    \"}",
            restExampleUrl = "/storemanagement/ocms/order/queryvirtualphone",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/queryvirtualphone", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<QueryVirtualPhoneResponse> queryVirtualPhone(@Valid @RequestBody QueryVirtualPhoneRequest request) {
        return ocmsOrderServiceWrapper.queryVirtualPhone(request);
    }

    @MethodDoc(
            description = "查询退款金额",
            displayName = "查询退款金额",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询退款金额",
                            type = QueryRefundableAmountRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": {\n" +
                    "        \"refundCent\": -1\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/order/queryrefundableamount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/queryrefundableamount", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<QueryRefundableAmountResponse> queryRefundableAmount(@Valid @RequestBody QueryRefundableAmountRequest request) {
        return ocmsOrderServiceWrapper.queryRefundableAmount(request);
    }

    @MethodDoc(
            displayName = "查询系统信息",
            description = "查询系统信息，包括登录账户对应租户已开通渠道信息",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"SUCCESS\",\n" +
                    "    \"data\": {\n" +
                    "        \"channelInfos\": [\n" +
                    "            {\n" +
                    "                \"code\": \"MEITUAN\",\n" +
                    "                \"id\": 100,\n" +
                    "                \"name\": \"美团外卖\",\n" +
                    "                \"abbreviation\": \"美团\"\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"code\": \"ELEM\",\n" +
                    "                \"id\": 200,\n" +
                    "                \"name\": \"饿了么\",\n" +
                    "                \"abbreviation\": \"饿了么\"\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"code\": \"JD2HOME\",\n" +
                    "                \"id\": 300,\n" +
                    "                \"name\": \"京东到家\",\n" +
                    "                \"abbreviation\": \"京东\"\n" +
                    "            }\n" +
                    "        ]\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/order/querysysteminfo",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querysysteminfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<QuerySystemInfoResponse> querySystemInfo() {
        return ocmsOrderServiceWrapper.querySystemInfo();
    }

    @MethodDoc(
            displayName = "检查退款",
            description = "检查退款，同时会将退款理由列表返回",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "检查退款",
                            type = CheckRefundRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": -1,\n" +
                    "    \"message\": \"已取消或锁定中的订单不能发起订单取消\",\n" +
                    "    \"data\": {\n" +
                    "        \"refundReasons\": [],\n" +
                    "        \"orderInfo\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/order/checkrefund",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/checkrefund", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<CheckRefundResponse> checkRefund(@Valid @RequestBody CheckRefundRequest checkRefundRequest) {
        return ocmsOrderServiceWrapper.checkRefund(checkRefundRequest);
    }


    @MethodDoc(
            displayName = "克重退款信息查询",
            description = "克重退款信息查询，克重退款页面查询可退数量，渠道重量",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "检查退款",
                            type = WeightRefundCheckRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": -1,\n" +
                    "    \"message\": \"已取消或锁定中的订单不能发起订单取消\",\n" +
                    "    \"data\": {\n" +
                    "        \"refundReasons\": [],\n" +
                    "        \"orderInfo\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/order/weightrefundcheck",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/weightrefundcheck", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<WeightRefundCheckResponse> weightRefundCheck(@Valid @RequestBody WeightRefundCheckRequest weightRefundCheckRequest) {
        return ocmsOrderServiceWrapper.weightRefundCheck(weightRefundCheckRequest);
    }

    @MethodDoc(
            description = "发起按克重退差价",
            displayName = "发起按克重退差价",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "发起按克重退差价",
                            type = RefundByWeightRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\\n\" +\n" +
                    "                    \"    \\\"code\\\": 0,\\n\" +\n" +
                    "                    \"    \\\"message\\\": \\\"操作成功\\\",\\n\" +\n" +
                    "                    \"    \\\"data\\\": {}\\n\" +\n" +
                    "                    \"}",
            restExampleUrl = "/storemanagement/ocms/order/weightrefund",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "发起按克重退差价")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/weightrefund", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse weightRefund(@Valid @RequestBody RefundByWeightRequest request) {
        return ocmsOrderServiceWrapper.tenantWeightRefund(request);
    }


    @MethodDoc(
            displayName = "获取配送路径静态信息",
            description = "获取配送路径静态信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询打印状态",
                            type = OrderDetailRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/storemanagement/delivery/path/getImmutableInfo",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "获取配送路径静态信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string")
    })
    @RequestMapping(value = "/path/getImmutableInfo", method = RequestMethod.POST)
    @MethodLog(logResponse = true, logRequest = true)
    @RhinoValve(entrance = "/storemanagement/delivery/path/getImmutableInfo")
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse<DeliveryPathImmutableInfoVO> getImmutableInfo(@Valid @RequestBody GetImmutableInfoReq request){
        CommonResponse result = null;
        try {
            result = ocmsOrderServiceWrapper.getDeliveryPathImmutableInfo(request);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            return CommonResponse.fail(500, e.getMessage());
        }
        return result;
    }

    @MethodDoc(
            displayName = "获取配送路径动态信息",
            description = "获取配送路径动态信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询打印状态",
                            type = OrderDetailRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/storemanagement/ocms/order/path/getDynamicInfo",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "获取配送日志信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string")
    })
    @RequestMapping(value = "/path/getDynamicInfo", method = RequestMethod.POST)
    @MethodLog(logResponse = true, logRequest = true)
    @RhinoValve(entrance = "/storemanagement/delivery/path/getDynamicInfo")
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse<DeliveryDynamicInfoVO> getDynamicInfo(@Valid @RequestBody GetDynamicInfoReq req){
        CommonResponse result = null;
        try {
            result = ocmsOrderServiceWrapper.getDeliveryPathDynamicInfo(req);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            return CommonResponse.fail(500, e.getMessage());
        }
        return result;
    }

    private List<String> getBoothIdList(Long storeId) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        List<String> boothIdList = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.BOOTH, String::valueOf);
        if (user.getAccountType() != AccountTypeEnum.BOOTH.getValue()) {
            List<Long> bootIdListFromStore = tenantWrapper.queryBoothListByStoreId(user.getTenantId(), storeId);
            for (Long boothId : bootIdListFromStore) {
                if (!boothIdList.contains(String.valueOf(boothId))) {
                    boothIdList.add(String.valueOf(boothId));
                }
            }
        }

        return boothIdList;
    }

    private Map<String, GoodsExpirationDto> getGoodsExpirationMap(Long tenantId, Long storeId, RiderPickWorkOrderDTO pickWorkOrder) {
        try {
            if(!MccConfigUtil.isPickPhotoConfigurableGrayStore(storeId)) {
                return Collections.emptyMap();
            }

            List<String> skuIds = pickWorkOrder.getPickTasks().stream()
                    .map(RiderPickTaskDTO::getStoreSkuId)
                    .distinct()
                    .collect(Collectors.toList());
            List<DepotGoodsDetailDto> depotGoodsDetailDtos = goodsCenterRemoteService.queryGoodsInfo(tenantId, storeId, skuIds);
            return depotGoodsDetailDtos.stream()
                    .collect(Collectors.toMap(DepotGoodsDetailDto::getGoodsId, DepotGoodsDetailDto::getExpirationInfo, (k1,k2) -> k2));
        } catch (Exception e) {
            log.warn("查询商品保质期失败", e);
            Cat.logEvent("SWITCH_TO_GOODS_CENTER", "GET_GOODS_EXPIRATION_FAIL");
            return Collections.emptyMap();
        }
    }

    private Map<String, GoodsExpirationDto> getGoodsExpirationMap(Long tenantId, Long storeId, TradeShippingOrderDTO tradeShippingOrderDTO) {
        try {
            if(!MccConfigUtil.isPickPhotoConfigurableGrayStore(storeId)) {
                return Collections.emptyMap();
            }

            List<String> skuIds = tradeShippingOrderDTO.getItems().stream()
                    .map(TradeShippingOrderItemDTO::getSkuId)
                    .distinct()
                    .collect(Collectors.toList());
            List<DepotGoodsDetailDto> depotGoodsDetailDtos = goodsCenterRemoteService.queryGoodsInfo(tenantId, storeId, skuIds);
            return depotGoodsDetailDtos.stream()
                    .collect(Collectors.toMap(DepotGoodsDetailDto::getGoodsId, DepotGoodsDetailDto::getExpirationInfo, (k1,k2) -> k2));
        } catch (Exception e) {
            log.warn("查询商品保质期失败", e);
            Cat.logEvent("SWITCH_TO_GOODS_CENTER", "GET_GOODS_EXPIRATION_FAIL");
            return Collections.emptyMap();
        }
    }

    private List<PickItemVO> buildPickItemInfoList(RiderPickWorkOrderDTO riderPickWorkOrderDTO, Map<String, GoodsExpirationDto> goodsExpirationMap) {
        if (Objects.isNull(riderPickWorkOrderDTO)) {
            return Collections.emptyList();
        }

        return riderPickWorkOrderDTO.getPickTasks().stream().map(task -> {
            PickItemVO pickItemVO = new PickItemVO();
            pickItemVO.setSkuId(task.getStoreSkuId());
            pickItemVO.setAttribute(task.getTemperatureAttribute());
            pickItemVO.setShouldPickNum(task.getCount() - task.getRefundCount());
            pickItemVO.setProductName(task.getSkuName());
            pickItemVO.setIsSnProduct(task.getIsManagementSnCode());
            pickItemVO.setSnCodeEnteringType(task.getSnCodeEnteringTypeMap());
            pickItemVO.setStockoutEnteringType(task.getDrunkHorsePickType());
            pickItemVO.setUpc(buildUpcAndBoxCodeSet(task));
            pickItemVO.setSpec(task.getSpecification());
            pickItemVO.setPicUrl(task.getPicUrl());
            pickItemVO.setRealPicUrlList(task.getRealPicUrls());
            //由于前后端灰度节奏不同，前端比后端先全量，这个字段用来告诉前端展示实拍图还是展示原来的商品图片
            pickItemVO.setShowRealPic(MccDynamicConfigUtil.isReadGoodsGrayStore(riderPickWorkOrderDTO.getStoreId()));

            fillExpirationInfo(pickItemVO, goodsExpirationMap, riderPickWorkOrderDTO.getStoreId());

            return pickItemVO;
        }).collect(Collectors.toList());
    }

    private List<PickItemVO> buildPickItemInfoList(TradeShippingOrderDTO tradeShippingOrderDTO, Map<String, GoodsExpirationDto> goodsExpirationMap) {
        if (Objects.isNull(tradeShippingOrderDTO)) {
            return Collections.emptyList();
        }

        boolean isSealGrayStore = GrayConfigUtils.judgeIsGrayStore(tradeShippingOrderDTO.getMerchantId(),
                tradeShippingOrderDTO.getWarehouseId(), GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false);

        return tradeShippingOrderDTO.getItems().stream()
                .map(items -> {
            PickItemVO pickItemVO = new PickItemVO();
            pickItemVO.setSkuId(items.getSkuId());
            pickItemVO.setAttribute(items.getTemperatureZoneCode());
            pickItemVO.setShouldPickNum(items.getActualQty().intValue());
            pickItemVO.setProductName(items.getSkuName());
            pickItemVO.setIsSnProduct(items.getIsManagementSnCode());
            pickItemVO.setSnCodeEnteringType(items.getSnCodeEnteringTypeMap());
            pickItemVO.setStockoutEnteringType(items.getDrunkHorsePickType());
            pickItemVO.setUpc(buildUpcAndBoxCodeSet(items));
            pickItemVO.setSpec(items.getSpecification());
            pickItemVO.setPicUrl(StringUtils.EMPTY);
            pickItemVO.setRealPicUrlList(items.getImgUrls());
            //由于前后端灰度节奏不同，前端比后端先全量，这个字段用来告诉前端展示实拍图还是展示原来的商品图片
            pickItemVO.setShowRealPic(MccDynamicConfigUtil.isReadGoodsGrayStore(tradeShippingOrderDTO.getWarehouseId()));

            fillExpirationInfo(pickItemVO, goodsExpirationMap, tradeShippingOrderDTO.getWarehouseId());

            if(isSealGrayStore) {
                pickItemVO.setIsSealDelivery(items.getNeedSealDelivery());
            }


            return pickItemVO;
        }).collect(Collectors.toList());
    }

    private void fillExpirationInfo(PickItemVO taskVo, Map<String, GoodsExpirationDto> goodsExpirationMap, Long warehouseId) {
        try {
            String skuId = taskVo.getSkuId();
            GoodsExpirationDto expirationDto = goodsExpirationMap.get(skuId);
            if (expirationDto == null) {
                return;
            }

            DepotGoodsExpireUnitEnum unit = DepotGoodsExpireUnitEnum.findByCode(expirationDto.getExpireUnit());
            // 设置是否是短保品。根据门店灰度，取不同的字段：
            // （1）灰度门店内：取 (expireForUnit + expireUnit) 两个字段，统一换算成天进行处理
            if (com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil.goodsQuerySwitch2NewFields(warehouseId)
                    && expirationDto.getExpireForUnit() != null
                    && unit != null) {
                taskVo.setIsShortExpirationGoods(MccConfigUtil.getShortExpirationThreshold() > (expirationDto.getExpireForUnit() * unit.getRatio()));
            }
            else if (expirationDto.getExpire() != null) {
                // （2）灰度门店外（或者是当新字段为空时使用老字段兜底）：保持老逻辑不变，取 expire 字段
                // 全量一段时间后，可删除这个 else if 和上面的 MccConfigUtil.goodsQuerySwitch2NewFields(warehouseId) 判断条件
                taskVo.setIsShortExpirationGoods(MccConfigUtil.getShortExpirationThreshold() > expirationDto.getExpire());
            }
            else {
                // isShortExpirationGoods：默认值兜底
                taskVo.setIsShortExpirationGoods(false);
            }

        } catch (Exception e) {
            log.warn("fillExpirationInfo error", e);
            Cat.logEvent("SWITCH_TO_GOODS_CENTER", "FILL_EXPIRATION_INFO_FAIL");
        }

    }

    private Set<String> buildUpcAndBoxCodeSet(RiderPickTaskDTO taskDto) {
        Set<String> upcAndBoxCodeList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(taskDto.getUpcList())) {
            upcAndBoxCodeList.addAll(taskDto.getUpcList());
        }

        if (CollectionUtils.isNotEmpty(taskDto.getBoxCodes())) {
            upcAndBoxCodeList.addAll(taskDto.getBoxCodes());
        }

        return upcAndBoxCodeList;
    }

    private Set<String> buildUpcAndBoxCodeSet(TradeShippingOrderItemDTO tradeShippingOrderItemDTO) {
        Set<String> upcAndBoxCodeList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(tradeShippingOrderItemDTO.getBarCodes())) {
            upcAndBoxCodeList.addAll(tradeShippingOrderItemDTO.getBarCodes());
        }

        if (CollectionUtils.isNotEmpty(tradeShippingOrderItemDTO.getBoxCodes())) {
            upcAndBoxCodeList.addAll(tradeShippingOrderItemDTO.getBoxCodes());
        }

        return upcAndBoxCodeList;
    }

    private void fillGiftInfo(OrderDetailResponse response, BizOrderModel bizOrderModel) {
        try {
            //过滤非歪马租户
            if (!MccConfigUtil.isDrunkHorseTenant(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
                return;
            }
            if (CollectionUtils.isNotEmpty(bizOrderModel.getBizOrderGiftModelList())) {
                response.getOrderDetail().setGiftVOList(
                        bizOrderModel.getBizOrderGiftModelList()
                                .stream()
                                .map(
                                        bizOrderGiftModel -> {
                                            GiftVO giftVO = new GiftVO();
                                            giftVO.setGiftName(bizOrderGiftModel.getName());
                                            giftVO.setGiftQuantity(bizOrderGiftModel.getQuantity());
                                            giftVO.setSku(bizOrderGiftModel.getSkuId());
                                            giftVO.setBelongSkuId(bizOrderGiftModel.getMainSkuId());
                                            if (StringUtils.isNotBlank(bizOrderGiftModel.getGiftSpec())) {
                                                giftVO.setSpecification(bizOrderGiftModel.getGiftSpec());
                                            }
                                            return giftVO;
                                        }
                                )
                                .collect(Collectors.toList())
                );
            }

        } catch (Exception e) {
            log.error("填充赠品信息失败", e);
            Cat.logEvent("PART_REFUND", "FILL_ABN_ORDER_ITEM_FAIL");
        }
    }

    private void fillLackStockGoodsInfo(OrderDetailResponse response, BizOrderModel bizOrderModel) {
        try {
            //过滤非歪马租户
            if (!MccConfigUtil.isDrunkHorseTenant(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
                return;
            }

            //查异常单
            Optional<AbnOrderDTO> abnOrderOpt = abnOrderRemoteService.getUnprocessedAbnOrderBySource(response.getOrderDetail().getStoreId(),
                    response.getOrderDetail().getChannelId(), response.getOrderDetail().getChannelOrderId());

            if (!abnOrderOpt.isPresent() || CollectionUtils.isEmpty(abnOrderOpt.get().getItems())) {
                return;
            }

            AbnOrderDTO abnOrder = abnOrderOpt.get();

            List<String> lackStockGoodsIds = abnOrder.getItems().stream().map(AbnOrderItemDTO::getSkuId).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(lackStockGoodsIds)) {
                return;
            }

            Map<String, DepotGoodsDetailDto> goodsDetailDtoMap = depotGoodsService.queryByGoodsIds(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), response.getOrderDetail().getStoreId(), lackStockGoodsIds);

            List<LackStockGoodsVO> lackStockGoodsList = abnOrder.getItems().stream()
                    .map(item -> this.buildLackStockGoodsVO(item, goodsDetailDtoMap.get(item.getSkuId())))
                    .collect(Collectors.toList());

            //填充缺货货品列表
            response.getOrderDetail().setLackStockGoodsList(lackStockGoodsList);

            //如果有缺货的货品,需要在对应的销售商品上标识出来
            if (CollectionUtils.isNotEmpty(lackStockGoodsList)) {

                if (bizOrderModel == null) {
                    return;
                }

                //标识出组合关系中包含缺货货品的销售商品
                List<Long> lackStockOrderItemIds = bizOrderModel.getComposeSkuModels().stream().filter(
                        composeSkuModel -> {
                            Optional<String> lackStockSkuIdOpt = composeSkuModel.getChildItemList().stream()
                                    .map(ChildSkuModel::getSkuId)
                                    .filter(lackStockGoodsIds::contains)
                                    .findAny();
                            return lackStockSkuIdOpt.isPresent();
                        }).map(ComposeSkuModel::getOrderItemId).collect(Collectors.toList());

                List<String> lackStockOrderItemSkuIds = bizOrderModel.getBizOrderItemModelList().stream()
                        .filter(orderItem -> lackStockOrderItemIds.contains(orderItem.getOrderItemId()))
                        .map(BizOrderItemModel::getInstoreSkuId2)
                        .collect(Collectors.toList());

                response.getOrderDetail().getProductList().forEach(
                        product -> product.setIsIncludeStockLackGoods(lackStockOrderItemSkuIds.contains(product.getSkuId()))
                );
                if (CollectionUtils.isNotEmpty(response.getOrderDetail().getGiftVOList())) {
                    response.getOrderDetail().getGiftVOList().forEach(
                            giftVO -> giftVO.setIsIncludeStockLackGoods(lackStockOrderItemSkuIds.contains(giftVO.getSku()))
                    );
                }
                //礼袋是否缺货设置
                if (CollectionUtils.isNotEmpty(response.getOrderDetail().getGiftBagList())){
                    response.getOrderDetail().getGiftBagList().forEach(
                            giftBagVO -> giftBagVO.setIsIncludeStockLackGoods(lackStockGoodsIds.contains(giftBagVO.getMaterialSkuId()))
                    );
                }
            }

        } catch (Exception e) {
            log.error("填充缺货信息失败", e);
            Cat.logEvent("PART_REFUND", "FILL_ABN_ORDER_ITEM_FAIL");
        }
    }

    private LackStockGoodsVO buildLackStockGoodsVO(AbnOrderItemDTO abnOrderItemDTO, DepotGoodsDetailDto goods) {
        if (Objects.isNull(goods)) {
            log.error("未获取到货品信息, skuId: {}", abnOrderItemDTO.getSkuId());
            throw new CommonRuntimeException("未获取到货品信息");
        }

        return LackStockGoodsVO.builder()
                .goodsName(goods.getGoodsName())
                .skuId(abnOrderItemDTO.getSkuId())
                .picUrl(parseRealPicUrl(goods))
                .spec(goods.getSpecName())
                .upcList(goods.getUpcList())
                .needCount(abnOrderItemDTO.getDemandQuantity().intValue())
                .salableCount(abnOrderItemDTO.getAvailableQuantity().intValue())
                .lackCount(abnOrderItemDTO.getDemandQuantity().subtract(abnOrderItemDTO.getAvailableQuantity()).intValue())
                .build();
    }

    private String parseRealPicUrl(DepotGoodsDetailDto goods) {
        if (Objects.isNull(goods) || Objects.isNull(goods.getGoodsPic())
                || CollectionUtils.isEmpty(goods.getGoodsPic().getRealPicUrlList())) {
            return "";
        }
        return goods.getGoodsPic().getRealPicUrlList().get(0);
    }
}
