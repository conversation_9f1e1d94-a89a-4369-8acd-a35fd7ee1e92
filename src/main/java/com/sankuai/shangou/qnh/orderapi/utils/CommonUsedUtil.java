package com.sankuai.shangou.qnh.orderapi.utils;

import com.meituan.shangou.saas.common.enums.PickRecordButtonEnum;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DeliveryStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ChannelOnlineTypeEnum;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseDetailBO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AfsBaseInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.BillInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.AfterSaleRecordVO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Slf4j
public class CommonUsedUtil {

    public static Integer setPickGoodsRecordButton(Integer channelId, Integer pickStatus, Long tenantId, Boolean isErpOrDrunkHorseTenant) {

        try {
            log.info("CommonUsedUtil.setPickGoodsRecordButton channelId: {}, pickStatus: {}, tenantId: {}", channelId, pickStatus, tenantId);
            if(pickStatus == null || pickStatus < DeliveryStatusEnum.PICKED.getValue()){
                return PickRecordButtonEnum.CLOSE_BUTTON.getValue();
            }
            // 需要判断是否pos渠道 pos渠道不打开
            DynamicOrderBizType orderBizType = DynamicOrderBizType.findOf(channelId);
            if(Objects.nonNull(orderBizType) && (ChannelOnlineTypeEnum.isOfflinePos(orderBizType.getChannelStandard()) ||
                    ChannelOnlineTypeEnum.isPrivate(orderBizType.getChannelStandard()))){
                log.info("CommonUsedUtil.setPickGoodsRecordButton pos渠道订单 tenantId: {}", tenantId);
                return PickRecordButtonEnum.CLOSE_BUTTON.getValue();
            }
            if(Boolean.TRUE.equals(isErpOrDrunkHorseTenant)){
                log.info("CommonUsedUtil.setPickGoodsRecordButton erp或者歪马租户 tenantId: {}", tenantId);
                return PickRecordButtonEnum.CLOSE_BUTTON.getValue();
            }
            return PickRecordButtonEnum.OPEN_BUTTON.getValue();
        } catch (Exception e) {
            log.error("CommonUsedUtil.setPickGoodsRecordButton is error e= ", e);
        }
        return PickRecordButtonEnum.CLOSE_BUTTON.getValue();
    }

    /**
     * 处理地址变更费和注释以及默认值
     */
    public static void dealAddressChangeFee(Integer channelId, BillInfoVO billInfo, boolean isApp) {
        try {
            if(Objects.isNull(billInfo)
                    || Objects.isNull(channelId)) {
                return;
            }
            //必须对应渠道才给默认值，前端会按该字段是否为空进行展示
            String addressChangeFee = billInfo.getAddressChangeFee();
            if(StringUtils.isBlank(addressChangeFee)) {
                return;
            }
            if(Objects.equals(channelId, DynamicChannelType.MEITUAN.getChannelId())
                    || Objects.equals(channelId, DynamicChannelType.MT_MEDICINE.getChannelId())
                    || Objects.equals(channelId, DynamicChannelType.MT_DRUNK_HORSE.getChannelId())) {
                billInfo.setAddressChangeFeeNotes(getMTAddressChangeFeeNotes(isApp));
                billInfo.setAddressChangeFee(addressChangeFee);
                return;
            }

            if(Objects.equals(channelId, DynamicChannelType.ELEM.getChannelId())) {
                billInfo.setAddressChangeFeeNotes(getElmAddressChangeFeeNotes(isApp));
                billInfo.setAddressChangeFee(addressChangeFee);
            }
        } catch (Exception e) {
            log.error("CommonUsedUtil.dealAddressChangeFee is error e= ", e);
        }

    }

    public static void dealAddressChangeFeeToBaseInfo(String addressChangeFee, OrderFuseDetailBO.BaseInfo baseInfo, boolean isApp) {
        try {
            if(Objects.isNull(baseInfo)
                    || Objects.isNull(baseInfo.getChannelId())) {
                return;
            }
            if(StringUtils.isBlank(addressChangeFee)) {
                return;
            }
            //必须对应渠道才给默认值，前端会按该字段是否为空进行展示
            Integer channelId = baseInfo.getChannelId();
            if(Objects.equals(channelId, DynamicChannelType.MEITUAN.getChannelId())
                    || Objects.equals(channelId, DynamicChannelType.MT_MEDICINE.getChannelId())
                    || Objects.equals(channelId, DynamicChannelType.MT_DRUNK_HORSE.getChannelId())) {
                baseInfo.setAddressChangeFeeNotes(getMTAddressChangeFeeNotes(isApp));
                baseInfo.setAddressChangeFee(addressChangeFee);
                return;
            }

            if(Objects.equals(channelId, DynamicChannelType.ELEM.getChannelId())) {
                baseInfo.setAddressChangeFeeNotes(getElmAddressChangeFeeNotes(isApp));
                baseInfo.setAddressChangeFee(addressChangeFee);
            }
        } catch (Exception e) {
            log.error("CommonUsedUtil.dealAddressChangeFeeToBaseInfo is error e= ", e);
        }

    }

    public static void dealAddressChangeFeeToAfterSaleRecordVO(String addressChangeFee, AfterSaleRecordVO afterSaleRecordVO, boolean isApp) {
        try {
            if(Objects.isNull(afterSaleRecordVO)
                    || Objects.isNull(afterSaleRecordVO.getChannelId())) {
                return;
            }
            if(StringUtils.isBlank(addressChangeFee)) {
                return;
            }
            //必须对应渠道才给默认值，前端会按该字段是否为空进行展示
            Integer channelId = afterSaleRecordVO.getChannelId();
            if(Objects.equals(channelId, DynamicChannelType.MEITUAN.getChannelId())
                    || Objects.equals(channelId, DynamicChannelType.MT_MEDICINE.getChannelId())
                    || Objects.equals(channelId, DynamicChannelType.MT_DRUNK_HORSE.getChannelId())) {
                afterSaleRecordVO.setAddressChangeFeeNotes(getMTAddressChangeFeeNotes(isApp));
                afterSaleRecordVO.setAddressChangeFee(addressChangeFee);
                return;
            }

            if(Objects.equals(channelId, DynamicChannelType.ELEM.getChannelId())) {
                afterSaleRecordVO.setAddressChangeFeeNotes(getElmAddressChangeFeeNotes(isApp));
                afterSaleRecordVO.setAddressChangeFee(addressChangeFee);
            }
        } catch (Exception e) {
            log.error("CommonUsedUtil.dealAddressChangeFeeToAfterSaleRecordVO is error e= ", e);
        }
    }

    public static void dealAddressChangeFeeToOrderVO(String addressChangeFee, OrderVO orderVO, boolean isApp) {
        try {
            if(Objects.isNull(orderVO)
                    || Objects.isNull(orderVO.getChannelId())) {
                return;
            }
            //必须对应渠道才给默认值，前端会按该字段是否为空进行展示
            if(StringUtils.isBlank(addressChangeFee)) {
                return;
            }
            Integer channelId = orderVO.getChannelId();
            if(Objects.equals(channelId, DynamicChannelType.MEITUAN.getChannelId())
                    || Objects.equals(channelId, DynamicChannelType.MT_MEDICINE.getChannelId())
                    || Objects.equals(channelId, DynamicChannelType.MT_DRUNK_HORSE.getChannelId())) {
                orderVO.setAddressChangeFeeNotes(getMTAddressChangeFeeNotes(isApp));
                orderVO.setAddressChangeFee(addressChangeFee);
                return;
            }

            if(Objects.equals(channelId, DynamicChannelType.ELEM.getChannelId())) {
                orderVO.setAddressChangeFeeNotes(getElmAddressChangeFeeNotes(isApp));
                orderVO.setAddressChangeFee(addressChangeFee);
            }
        } catch (Exception e) {
            log.error("CommonUsedUtil.dealAddressChangeFeeToOrderVO is error e= ", e);
        }
    }

    public static String getMTAddressChangeFeeNotes(boolean isApp) {
        if(isApp) {
            return LionUtils.getAddressChangeFeeNotesApp();
        }
        return LionUtils.getAddressChangeFeeNotesWeb();
    }

    public static String getElmAddressChangeFeeNotes(boolean isApp) {
        if(isApp) {
            return LionUtils.getAddressChangeFeeNotesAppEleM();
        }
        return LionUtils.getAddressChangeFeeNotesWebEleM();
    }
}
