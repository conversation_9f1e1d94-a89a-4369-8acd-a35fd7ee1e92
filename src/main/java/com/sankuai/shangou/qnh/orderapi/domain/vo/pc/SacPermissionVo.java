package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.sac.dto.model.SacPermissionDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 功能
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-12-01
 **/
@TypeDoc(
        description = "权限对象"
)
@Data
public class SacPermissionVo {

    /**
     * 功能权限id
     */
    @FieldDoc(
            description = "功能权限id"
    )
    public Long sacPermissionId;

    /**
     * 功能权限code
     */
    @FieldDoc(
            description = "功能权限code"
    )
    public String sacPermissionCode;

    /**
     * 功能权限名称
     */
    @FieldDoc(
            description = "功能权限名称"
    )
    public String sacPermissionName;

    /**
     * 应用id
     */
    @FieldDoc(
            description = "应用id"
    )
    public Integer appId;

    /**
     * 应用名称
     */
    @FieldDoc(
            description = "应用名称"
    )
    public String appName;

    public String buttonResourceUrl;

    public SacPermissionVo build(SacPermissionDTO sacPermissionDTO) {

        this.sacPermissionId = sacPermissionDTO.getSacPermissionId();
        this.sacPermissionCode = sacPermissionDTO.getSacPermissionCode();
        this.sacPermissionName = sacPermissionDTO.getSacPermissionName();
        this.appId = sacPermissionDTO.getAppId();
        this.appName = sacPermissionDTO.getAppName();
        this.buttonResourceUrl = sacPermissionDTO.getButtonResourceUrl();
        return this;

    }

    public static List<SacPermissionVo> buildBySacPermissionDTOList(List<SacPermissionDTO> sacPermissionDTOList) {

        List<SacPermissionVo> sacPermissionVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(sacPermissionDTOList)) {
            return sacPermissionVos;
        }
        for (SacPermissionDTO sacPermissionDTO : sacPermissionDTOList) {
            sacPermissionVos.add(buildSacPermissionVo(sacPermissionDTO));
        }
        return sacPermissionVos;

    }

    public static SacPermissionVo buildSacPermissionVo(SacPermissionDTO sacPermissionDTO) {
        SacPermissionVo sacPermissionVO = new SacPermissionVo();
        sacPermissionVO.build(sacPermissionDTO);
        return sacPermissionVO;
    }

}
