package com.sankuai.shangou.qnh.orderapi.utils;

import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSAfterSaleApplyDetailVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderItemVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.ProductInfoVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.ProductLabelVO;
import com.meituan.shangou.saas.order.platform.enums.OrderProductLabelEnum;
import com.meituan.shangou.saas.order.platform.utils.GsonUtil;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ChannelLabelVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ChannelPickingDateVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ProductFuseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.JsonNode;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ProductLabelUtil {

    private static final ObjectMapper objMapper = new ObjectMapper();

    /**
     * 构建商品渠道标签列表
     * @param ocmsOrderItemVOList
     * @return
     */
    public static Map<Integer, ProductLabelVO> buildMapFromOCMSOrderItemVO(List<OCMSOrderItemVO> ocmsOrderItemVOList){
        try {
            return ocmsOrderItemVOList.stream()
                    .map(OCMSOrderItemVO::getChannelLabelList)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(ProductLabelVO::getType, v -> v, (a, b) -> a));
        }catch (Exception e){
           log.error("buildMapFromOCMSOrderItemVO error", e);
        }
        return new HashMap<>();
    }

    /**
     * 构建商品渠道标签列表
     * @param productInfoVoList
     * @return
     */
    public static Map<Integer, ProductLabelVO> buildMapFromProductInfoVo(List<ProductInfoVo> productInfoVoList){
        try {
            return productInfoVoList.stream()
                    .map(ProductInfoVo::getChannelLabelList)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(ProductLabelVO::getType, v -> v, (a, b) -> a));
        }catch (Exception e){
            log.error("buildMapFromProductInfoVo error", e);
        }
        return new HashMap<>();
    }

    /**
     * 构建商品渠道标签列表
     * @param productLabelVOList
     * @return
     */
    public static Map<Integer, ProductLabelVO> buildMapFromProductLabelVo(List<ProductLabelVO> productLabelVOList){
        try {
            return Optional.ofNullable(productLabelVOList).orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.toMap(ProductLabelVO::getType, v -> v, (a, b) -> a));
        }catch (Exception e){
            log.error("buildMapFromProductInfoVo error", e);
        }
        return new HashMap<>();
    }

    /**
     * 构建商品渠道标签列表
     * @param channelLabelList
     * @return
     */
    public static List<ChannelLabelVO> buildChannelLabelVOList(List<ProductLabelVO> channelLabelList) {
        try {
            if (CollectionUtils.isEmpty(channelLabelList)) {
                return new ArrayList<>();
            }
            Map<Integer, Integer> sortNumMap = OrderProductLabelEnum.getSortNumMap();
            return channelLabelList.stream()
                    .filter(item -> Objects.nonNull(OrderProductLabelEnum.enumOf(item.getType())))
                    .map(item -> {
                        ChannelLabelVO channelLabelVO = new ChannelLabelVO();
                        channelLabelVO.setOverServiceTime(false);
                        channelLabelVO.setType(item.getType());
                        channelLabelVO.setDesc(item.getDesc());
                        channelLabelVO.setName(item.getName());
                        return channelLabelVO;
                    }).sorted(Comparator.comparingInt(item ->
                            sortNumMap.getOrDefault(Optional.ofNullable(item.getType()).orElse(0), 99)))
                    .collect(Collectors.toList());
        }catch (Exception e){
            log.error("buildChannelLabelVOList error", e);
        }
        return new ArrayList<>();
    }

    /**
     * 构建商品渠道副标签描述
     * @param channelLabelList
     * @return
     */
    public static String buildChannelLabelSubDesc(List<ProductLabelVO> channelLabelList, String extData) {
        try {
            if(CollectionUtils.isEmpty(channelLabelList)){
                return null;
            }
            if(StringUtils.isBlank(extData)){
                return null;
            }
            ChannelPickingDateVO channelPickingDateVO = GsonUtil.toJavaBean(extData, ChannelPickingDateVO.class);
            if(channelPickingDateVO == null) {
                return null;
            }
            String channelPickingStart = channelPickingDateVO.getChannelPickingStart();
            String channelPickingEnd = channelPickingDateVO.getChannelPickingEnd();
            if(channelLabelList.stream()
                    .anyMatch(value -> Objects.equals(value.getType(), OrderProductLabelEnum.COMPENSATION_FOR_PROMISE.getValue()))
                    && StringUtils.isNotBlank(channelPickingStart)
                    && StringUtils.isNotBlank(channelPickingEnd)){
                SimpleDateFormat sdfInput = new SimpleDateFormat("yyyyMMdd");
                SimpleDateFormat sdfTOutPut = new SimpleDateFormat("MM月dd日");
                Date dateStart = sdfInput.parse(channelPickingStart);
                Date dateEnd = sdfInput.parse(channelPickingEnd);
                return "生产日期需在" + sdfTOutPut.format(dateStart) + "至" + sdfTOutPut.format(dateEnd) + "之间";
            }

        }catch (Exception e){
            log.error("buildChannelLabelSubDesc error", e);
        }
        return null;
    }

    /**
     * 构建退单商品渠道标签列表
     * @param label
     * @return
     */
    public static List<ChannelLabelVO> buildChannelLabelVOList(String label, Map<Integer, ProductLabelVO> productLabelVOMap) {
        List<ChannelLabelVO> channelLabelVOList = new ArrayList<>();
        try {
            if(StringUtils.isEmpty(label)){
                return channelLabelVOList;
            }
            JsonNode jsonNode = objMapper.readTree(label);
            if(!jsonNode.isArray()){
                return channelLabelVOList;
            }
            channelLabelVOList = JacksonUtils.fromJson(label, new TypeReference<List<ChannelLabelVO>>() {});
            if(MapUtils.isEmpty(productLabelVOMap)){
                return channelLabelVOList;
            }
            channelLabelVOList.forEach(channelLabelVO -> {
                ProductLabelVO productLabelVO = productLabelVOMap.get(channelLabelVO.getType());
                if(Objects.nonNull(productLabelVO)){
                    channelLabelVO.setName(productLabelVO.getName());
                    channelLabelVO.setDesc(productLabelVO.getDesc());
                }
            });
            Map<Integer, Integer> sortNumMap = OrderProductLabelEnum.getSortNumMap();
            channelLabelVOList.sort(Comparator.comparingInt(item ->
                    sortNumMap.getOrDefault(Optional.ofNullable(item.getType()).orElse(0), 99)));
        }catch (Exception e){
            log.error("buildChannelLabelVOList error", e);
        }
        return channelLabelVOList;
    }

    /**
     * 构建退单商品渠道副标签描述
     * @return
     */
    public static String buildAfterSaleChannelLabelSubDesc(String label, String channelPickingStart, String channelPickingEnd) {
        try {
            if(StringUtils.isEmpty(label)){
                return null;
            }
            JsonNode jsonNode = objMapper.readTree(label);
            if(!jsonNode.isArray()){
                return null;
            }
            List<ChannelLabelVO> channelLabelVOList = JacksonUtils.fromJson(label, new TypeReference<List<ChannelLabelVO>>() {});
            if(CollectionUtils.isEmpty(channelLabelVOList)){
                return null;
            }
            if(channelLabelVOList.stream()
                    .anyMatch(value -> Objects.equals(value.getType(), OrderProductLabelEnum.COMPENSATION_FOR_PROMISE.getValue()))
                    && StringUtils.isNotBlank(channelPickingStart)
                    && StringUtils.isNotBlank(channelPickingEnd)){
                SimpleDateFormat sdfInput = new SimpleDateFormat("yyyyMMdd");
                SimpleDateFormat sdfTOutPut = new SimpleDateFormat("MM月dd日");
                Date dateStart = sdfInput.parse(channelPickingStart);
                Date dateEnd = sdfInput.parse(channelPickingEnd);
                return "生产日期需在" + sdfTOutPut.format(dateStart) + "至" + sdfTOutPut.format(dateEnd) + "之间";
            }

        }catch (Exception e){
            log.error("buildChannelLabelSubDesc error", e);
        }
        return null;
    }

    /**
     * 构建退单商品渠道标签列表
     * @param label
     * @param channelLabelList
     * @return
     */
    public static List<ChannelLabelVO> buildChannelLabelVOList(String label, List<ProductLabelVO> channelLabelList) {
        List<ChannelLabelVO> channelLabelVOList = new ArrayList<>();
        try {
            return buildChannelLabelVOList(label, buildMapFromProductLabelVo(channelLabelList));
        }catch (Exception e){
            log.error("buildChannelLabelVOList error", e);
        }
        return channelLabelVOList;
    }

    public static List<ChannelLabelVO> buildChannelLabelVOList(List<ProductFuseVO> productList, List<OCMSAfterSaleApplyDetailVO> afterSaleApplyDetailVOList){
        List<ChannelLabelVO> channelLabelVOList = new ArrayList<>();
        try{
            Map<Integer, ChannelLabelVO> channelLabelVOMap = Optional.ofNullable(productList).orElse(new ArrayList<>())
                    .stream().flatMap(productFuseVO -> productFuseVO.getChannelLabelList().stream())
                    .collect(Collectors.toMap(ChannelLabelVO::getType, v -> v, (a, b) -> a));
            afterSaleApplyDetailVOList.forEach(item -> {
                if(StringUtils.isEmpty(item.getChannelLabel())){
                    return;
                }
                channelLabelVOList.addAll(buildChannelLabelVOListV2(item.getChannelLabel(), channelLabelVOMap));
            });
            return Optional.of(channelLabelVOList).orElse(new ArrayList<>()).stream().distinct().collect(Collectors.toList());
        }catch (Exception e){
            log.error("buildChannelLabelVOList error", e);
        }
        return channelLabelVOList;
    }

    /**
     * 构建退单商品渠道标签列表
     * @param label
     * @return
     */
    public static List<ChannelLabelVO> buildChannelLabelVOListV2(String label, Map<Integer, ChannelLabelVO> channelLabelVOMap) {
        List<ChannelLabelVO> channelLabelVOList = new ArrayList<>();
        try {
            if(StringUtils.isEmpty(label)){
                return channelLabelVOList;
            }
            JsonNode jsonNode = objMapper.readTree(label);
            if(!jsonNode.isArray()){
                return channelLabelVOList;
            }
            channelLabelVOList = JacksonUtils.fromJson(label, new TypeReference<List<ChannelLabelVO>>() {});
            if(MapUtils.isEmpty(channelLabelVOMap)){
                return channelLabelVOList;
            }
            channelLabelVOList.forEach(channelLabelVO -> {
                ChannelLabelVO productLabelVO = channelLabelVOMap.get(channelLabelVO.getType());
                if(Objects.nonNull(productLabelVO)){
                    channelLabelVO.setName(productLabelVO.getName());
                    channelLabelVO.setDesc(productLabelVO.getDesc());
                }
            });
        }catch (Exception e){
            log.error("buildChannelLabelVOList error", e);
        }
        return channelLabelVOList;
    }
}
