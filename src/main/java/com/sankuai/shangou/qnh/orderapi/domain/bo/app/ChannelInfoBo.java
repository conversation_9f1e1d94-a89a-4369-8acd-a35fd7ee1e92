package com.sankuai.shangou.qnh.orderapi.domain.bo.app;

import com.meituan.shangou.saas.tenant.thrift.dto.channel.ChannelInfoDto;
import lombok.Data;

@Data
public class ChannelInfoBo {

    /**
     * 渠道ID
     */
    public Integer channelId;

    /**
     * 渠道编码
     */
    public String channelCode;

    /**
     * 渠道名称
     */
    public String channelName;

    /**
     * 渠道类型 1-公有渠道 0-私有渠道 -1 - pos渠道
     */
    public Integer standard;

    public String logo;

    /**
     * 颜色
     */
    public String color;

    /**
     * 渠道状态 0-停用 1-启用
     */
    public Integer status;

    /**
     * 订单域类型
     */
    public Integer orderBizType;

    /**
     * 渠道缩写
     */
    public String channelAbbr;

    /**
     * 渠道打印logo
     */
    public String channelPrintLogo;


    public static ChannelInfoBo fromChannelInfoDto(ChannelInfoDto channelInfoDto) {
        ChannelInfoBo channelInfoBo = new ChannelInfoBo();
        channelInfoBo.setChannelId(channelInfoDto.getChannelId());
        channelInfoBo.setChannelCode(channelInfoDto.getChannelCode());
        channelInfoBo.setChannelName(channelInfoDto.getChannelName());
        channelInfoBo.setStandard(channelInfoDto.getStandard());
        channelInfoBo.setLogo(channelInfoDto.getLogo());
        channelInfoBo.setColor(channelInfoDto.getColor());
        channelInfoBo.setStatus(channelInfoDto.getStatus());
        channelInfoBo.setOrderBizType(channelInfoDto.getOrderBizType());
        channelInfoBo.setChannelAbbr(channelInfoDto.getChannelAbbr());
        channelInfoBo.setChannelPrintLogo(channelInfoDto.getChannelPrintLogo());
        return channelInfoBo;
    }
}
