package com.sankuai.shangou.qnh.orderapi.utils.app;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.joda.time.DateTime;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 日期工具类
 * <AUTHOR>
 *
 **/
@Slf4j
public class DateUtils {

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss SSS";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String MM_DD_BY_DOT = "MM.dd";
    public static final String YYYYMMDD = "yyyyMMdd";
    public static final String YYYY_MM = "yyyy-MM";
    public static final ZoneOffset ZON_OFFSET = OffsetDateTime.now().getOffset();
    static final DateTimeFormatter YYYY_MM_DD_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    /**
     * 将时间字符串转为Date
     *
     * @param dateStr 日期字符串
     *
     * @param format 日期格式
     * @return 给定字符串描述的日期对象。
     */
    public static Date parse(String dateStr, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date date = null;
        try {
            date = sdf.parse(dateStr);
        } catch (ParseException e) {
            log.error("date format error, dateStr:{}, format:{}", dateStr, format, e);
        }
        return date;
    }


    /**
     * 根据给定的格式与时间(Date类型的)，返回时间字符串
     *
     * @param date 指定的日期
     * @param format 日期格式字符串
     * @return String 指定格式的日期字符串.
     */
    public static String format(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    /**
     * 获取日期开始时间 当日00:00:00.000 对应时间戳
     * @param date
     * @return
     */
    public static Date getDayBeginTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取日期结束时间 当日23:59:59.999 对应时间戳
     * @param date
     * @return
     */
    public static Date getDayEndTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 校验日期格式
     * @param format
     */
    public static boolean isValidDate(String str, String format) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        boolean convertSuccess = true;
        FastDateFormat fdf = FastDateFormat.getInstance(format);
        try {
            fdf.parse(str);
        } catch (ParseException e) {
            convertSuccess=false;
        }
        return convertSuccess;
    }

    /**
     * 获取某月的最后一天
     * @param year
     * @param month
     * @return
     */
    public static String getLastDayOfMonth(int year, int month) {
        String date = getFirstDayOfMonth(year, month);
        LocalDate firstDayOfMonth = LocalDate.parse(date);
        if(Objects.isNull(firstDayOfMonth)){
            return null;
        }
        LocalDate endDayOfMonth = firstDayOfMonth.plusMonths(1L).plusDays(-1L);
        return endDayOfMonth.toString();
    }

    /**
     * 获取某月的第一天
     * @param year
     * @param month
     * @return
     */
    public static String getFirstDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, month - 1);
        cal.set(Calendar.DAY_OF_MONTH, cal.getMinimum(Calendar.DATE));
        return new SimpleDateFormat(YYYY_MM_DD).format(cal.getTime());
    }

    public static Long cutMillisAtBeginOfDay(long timestamp) {
        return cutMillisAtBeginOfDay(new Date(timestamp));
    }

    public static Long cutMillisAtBeginOfDay(Date date) {
        DateTime dt = new DateTime(date);
        return new DateTime(dt.getYear(), dt.getMonthOfYear(), dt.getDayOfMonth(), 0, 0, 0, 0).getMillis();
    }
    /**
     * 格式化日期
     * @param date 日期
     * @param format 格式
     * @return 日期字符串
     */
    public static String format(LocalDate date, String format) {

        if (date == null) {
            return null;
        }

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        return dateTimeFormatter.format(date);
    }

    /**
     * 获取指定格式日期字符串
     * @param dateStr
     * @param originalFormat
     * @param targetFormat
     * @return
     */
    public static String format(String dateStr, String originalFormat, String targetFormat) {

        if (org.apache.commons.lang3.StringUtils.isEmpty(dateStr)) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }

        DateTimeFormatter originalFormatter = DateTimeFormatter.ofPattern(originalFormat);
        DateTimeFormatter targetFormatter = DateTimeFormatter.ofPattern(targetFormat);

        LocalDate originalDate = LocalDate.parse(dateStr, originalFormatter);
        return targetFormatter.format(originalDate);
    }

    /**
     * 获取两个日期之间的所有日期
     * @param beginDateStr 开始日期
     * @param endDateStr 结束日期
     * @param format 格式
     * @return 间隔天数
     *
     * 举例: beginDateStr = "2020-03-01" , endDateStr = "2020-03-02"
     * 返回结果: [2020-02-27, 2020-02-28, 2020-02-29, 2020-03-01]
     */
    public static List<String> getBetweenDateList(String beginDateStr, String endDateStr, String format) {
        List<String> list = new ArrayList<>();
        LocalDate beginDate;
        LocalDate endDate;
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        try {
            beginDate = LocalDate.parse(beginDateStr, dateTimeFormatter);
            endDate = LocalDate.parse(endDateStr, dateTimeFormatter);
        } catch (Exception e) {
            throw new IllegalArgumentException("日期格式错误");
        }

        if (beginDate.compareTo(endDate) > 0) {
            throw new IllegalArgumentException("开始日期不能大于结束日期");
        }

        long betweenDays = ChronoUnit.DAYS.between(beginDate, endDate);
        for (long i = 0; i <= betweenDays; i++) {
            LocalDate localDate = beginDate.plusDays(i);
            list.add(dateTimeFormatter.format(localDate));
        }
        return list;
    }

    /**
     * 当前时间之前往前推days天对应的当天开始时间
     * @param days 天数
     * @return 当天开始时间
     */
    public static long minusDays(int days) {
        return LocalDateTime.of(LocalDate.now(), LocalTime.MIN)
                .minusDays(days)
                .toInstant(DateUtils.ZON_OFFSET)
                .toEpochMilli();
    }

    /**
     * 在当天开始时间上追加N天
     */
    public static long addDays(int days) {
        return LocalDateTime.of(LocalDate.now(), LocalTime.MIN).plusDays(days)
                .toEpochSecond(DateUtils.ZON_OFFSET);
    }

    public static String minusDaysFormatter(int days) {
        return formatter(LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minusDays(days));
    }

    public static String addDaysFormatter(int days) {
        return formatter(LocalDateTime.of(LocalDate.now(), LocalTime.MIN).plusDays(days));
    }

    public static String addDaysFormatter() {
        return formatter(LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
    }

    public static String formatter(LocalDateTime time) {
        return formatter(time, null);
    }

    public static String formatter(LocalDateTime time, DateTimeFormatter formatter) {
        if (Objects.isNull(time)) {
            time = LocalDateTime.now();
        }
        if (Objects.isNull(formatter)) {
            return YYYY_MM_DD_FORMAT.format(time);
        }
        return formatter.format(time);
    }
}