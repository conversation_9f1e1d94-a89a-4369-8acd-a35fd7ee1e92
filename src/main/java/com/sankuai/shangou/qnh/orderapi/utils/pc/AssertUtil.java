package com.sankuai.shangou.qnh.orderapi.utils.pc;

import com.sankuai.shangou.qnh.orderapi.exception.pc.ParamInvalidException;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * 校验工具类
 *
 * <AUTHOR>
 */
public class AssertUtil {

    private AssertUtil() {

    }

    /**
     * Assert a boolean expression, throwing an {@code ParamInvalidException}
     * if the expression evaluates to {@code false}.
     * <pre class="code">Assert.isTrue(i &gt; 0, "The value must be greater than zero");</pre>
     *
     * @param expression a boolean expression
     * @param message    the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if {@code expression} is {@code false}
     */
    public static void isTrue(boolean expression, String message) {
        isTrue(expression, message,null);
    }

    /**
     * Assert a boolean expression, throwing an {@code ParamInvalidException}
     * if the expression evaluates to {@code false}.
     * <pre class="code">Assert.isTrue(i &gt; 0, "The value must be greater than zero");</pre>
     *
     * @param expression a boolean expression
     * @param message    the exceptions message to use if the assertion fails
     * @param fieldName fieldName
     * @throws ParamInvalidException if {@code expression} is {@code false}
     */
    public static void isTrue(boolean expression, String message, String fieldName) {
        if (!expression) {
            throw new ParamInvalidException(message, fieldName);
        }
    }

    /**
     * Assert that an object is {@code null}.
     * <pre class="code">Assert.isNull(value, "The value must be null");</pre>
     *
     * @param object  the object to check
     * @param message the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the object is not {@code null}
     */
    public static void isNull(Object object, String message) {
        isNull(object, message, null);
    }


    /**
     * Assert that an object is {@code null}.
     * <pre class="code">Assert.isNull(value, "The value must be null");</pre>
     *
     * @param object  the object to check
     * @param message the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the object is not {@code null}
     */
    public static void isNull(Object object, String message, String fieldName) {
        if (object != null) {
            throw new ParamInvalidException(message, fieldName);
        }
    }


    /**
     * Assert that an object is not {@code null}.
     * <pre class="code">Assert.notNull(clazz, "The class must not be null");</pre>
     *
     * @param object  the object to check
     * @param message the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the object is {@code null}
     */
    public static void notNull(Object object, String message) {
        notNull(object, message, null);
    }


    /**
     * Assert that an object is not {@code null}.
     * <pre class="code">Assert.notNull(clazz, "The class must not be null");</pre>
     *
     * @param object  the object to check
     * @param message the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the object is {@code null}
     */
    public static void notNull(Object object, String message, String fieldName) {
        if (object == null) {
            throw new ParamInvalidException(message, fieldName);
        }
    }

    /**
     * Assert that the given String contains valid text content; that is, it must not
     * be {@code null} and must contain at least one non-whitespace character.
     * <pre class="code">Assert.hasText(name, "'name' must not be empty");</pre>
     *
     * @param text    the String to check
     * @param message the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the text does not contain valid text content
     * @see StringUtils#hasText
     */
    public static void notBlank(String text, String message) {
        if (!StringUtils.hasText(text)) {
            throw new ParamInvalidException(message);
        }
    }


    /**
     * Assert that the given String contains valid text content; that is, it must not
     * be {@code null} and must contain at least one non-whitespace character.
     * <pre class="code">Assert.hasText(name, "'name' must not be empty");</pre>
     *
     * @param text    the String to check
     * @param message the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the text does not contain valid text content
     * @see StringUtils#hasText
     */
    public static void notBlank(String text, String message, String fieldName) {
        if (!StringUtils.hasText(text)) {
            throw new ParamInvalidException(message, fieldName);
        }
    }


    /**
     * Assert that the given String contains valid text content; that is, it must not
     * be {@code null} and must contain at least one non-whitespace character.
     * <pre class="code">Assert.hasText(name, "'name' must not be empty");</pre>
     *
     * @param text    the String to check
     * @param message the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the text does not contain valid text content
     * @see StringUtils#isEmpty
     */
    public static void notEmpty(String text, String message) {
        notEmpty(text, message, null);
    }


    /**
     * Assert that the given String contains valid text content; that is, it must not
     * be {@code null} and must contain at least one non-whitespace character.
     * <pre class="code">Assert.hasText(name, "'name' must not be empty");</pre>
     *
     * @param text    the String to check
     * @param message the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the text does not contain valid text content
     * @see StringUtils#isEmpty
     */
    public static void notEmpty(String text, String message, String fieldName) {
        if (StringUtils.isEmpty(text)) {
            throw new ParamInvalidException(message, fieldName);
        }
    }

    /**
     * Assert that an array contains no {@code null} elements.
     * <p>Note: Does not complain if the array is empty!
     * <pre class="code">Assert.noNullElements(array, "The array must contain non-null elements");</pre>
     *
     * @param array   the array to check
     * @param message the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the object array contains a {@code null} element
     */
    public static void noNullElements(Object[] array, String message) {
        noNullElements(array, message, null);
    }

    /**
     * Assert that an array contains no {@code null} elements.
     * <p>Note: Does not complain if the array is empty!
     * <pre class="code">Assert.noNullElements(array, "The array must contain non-null elements");</pre>
     *
     * @param array   the array to check
     * @param message the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the object array contains a {@code null} element
     */
    public static void noNullElements(Object[] array, String message, String fieldName) {
        if (array != null) {
            for (Object element : array) {
                if (element == null) {
                    throw new ParamInvalidException(message, fieldName);
                }
            }
        }
    }

    /**
     * Assert that an array contains elements; that is, it must not be
     * {@code null} and must contain at least one element.
     * <pre class="code">Assert.notEmpty(array, "The array must contain elements");</pre>
     *
     * @param array   the array to check
     * @param message the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the object array is {@code null} or contains no elements
     */
    public static void notEmpty(Object[] array, String message) {
        notEmpty(array, message, null);
    }



    /**
     * Assert that an array contains elements; that is, it must not be
     * {@code null} and must contain at least one element.
     * <pre class="code">Assert.notEmpty(array, "The array must contain elements");</pre>
     *
     * @param array   the array to check
     * @param message the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the object array is {@code null} or contains no elements
     */
    public static void notEmpty(Object[] array, String message, String fieldName) {
        if (ObjectUtils.isEmpty(array)) {
            throw new ParamInvalidException(message, fieldName);
        }
    }

    /**
     * Assert that a collection contains elements; that is, it must not be
     * {@code null} and must contain at least one element.
     * <pre class="code">Assert.notEmpty(collection, "Collection must contain elements");</pre>
     *
     * @param collection the collection to check
     * @param message    the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the collection is {@code null} or
     *                               contains no elements
     */
    public static void notEmpty(Collection<?> collection, String message) {
        notEmpty(collection, message, null);
    }



    /**
     * Assert that a collection contains elements; that is, it must not be
     * {@code null} and must contain at least one element.
     * <pre class="code">Assert.notEmpty(collection, "Collection must contain elements");</pre>
     *
     * @param collection the collection to check
     * @param message    the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the collection is {@code null} or
     *                               contains no elements
     */
    public static void notEmpty(Collection<?> collection, String message, String fieldName) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new ParamInvalidException(message, fieldName);
        }
    }

    /**
     * Assert that a Map contains entries; that is, it must not be {@code null}
     * and must contain at least one entry.
     * <pre class="code">Assert.notEmpty(map, "Map must contain entries");</pre>
     *
     * @param map     the map to check
     * @param message the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the map is {@code null} or contains no entries
     */
    public static void notEmpty(Map<?, ?> map, String message) {
        notEmpty(map, message, null);
    }



    /**
     * Assert that a Map contains entries; that is, it must not be {@code null}
     * and must contain at least one entry.
     * <pre class="code">Assert.notEmpty(map, "Map must contain entries");</pre>
     *
     * @param map     the map to check
     * @param message the exceptions message to use if the assertion fails
     * @throws ParamInvalidException if the map is {@code null} or contains no entries
     */
    public static void notEmpty(Map<?, ?> map, String message, String fieldName) {
        if (CollectionUtils.isEmpty(map)) {
            throw new ParamInvalidException(message, fieldName);
        }
    }


    /**
     * @param i
     * @param message
     * @desc 是否是正数
     */
    public static void isPositiveNumber(Integer i, String message) {
        isPositiveNumber(i, message, null);
    }



    /**
     * @param i
     * @param message
     * @desc 是否是正数
     */
    public static void isPositiveNumber(Integer i, String message, String fieldName) {
        if (null == i) {
            throw new ParamInvalidException(message, fieldName);
        }
        if (i.compareTo(0) < 1) {
            throw new ParamInvalidException(message, fieldName);
        }
    }

    /**
     * @param l
     * @param message
     * @desc 是否是正数
     */
    public static void isPositiveNumber(Long l, String message) {
        isPositiveNumber(l, message, null);
    }



    /**
     * @param l
     * @param message
     * @desc 是否是正数
     */
    public static void isPositiveNumber(Long l, String message, String fieldName) {
        if (null == l) {
            throw new ParamInvalidException(message, fieldName);
        }
        if (l.compareTo(0L) < 1L) {
            throw new ParamInvalidException(message, fieldName);
        }
    }

    /**
     * @param i
     * @param message
     * @desc 是否是负数
     */
    public static void isNegativeNumber(Integer i, String message) {
        isNegativeNumber(i, message, null);
    }

    /**
     * @param i
     * @param message
     * @desc 是否是负数
     */
    public static void isNegativeNumber(Integer i, String message, String fieldName) {
        if (null == i) {
            throw new ParamInvalidException(message, fieldName);
        }
        if (i.compareTo(0) < 0) {
            throw new ParamInvalidException(message, fieldName);
        }
    }


    /**
     * 闭区间比价
     *
     * @param number       比较值
     * @param low          最小值
     * @param high         最大值
     * @param errorMessage 错误提示
     */
    public static void betweenClose(Number number, Number low, Number high, String errorMessage) {
        betweenClose(number, low, high, errorMessage, null);
    }


    /**
     * 闭区间比价
     *
     * @param number       比较值
     * @param low          最小值
     * @param high         最大值
     * @param errorMessage 错误提示
     */
    public static void betweenClose(Number number, Number low, Number high, String errorMessage, String fieldName) {
        if (number == null || low == null || high == null) {
            throw new ParamInvalidException(errorMessage, fieldName);
        }
        if (!(low.doubleValue() <= number.doubleValue() && number.doubleValue() <= high.doubleValue())) {
            throw new ParamInvalidException(errorMessage, fieldName);
        }
    }

    public static void numInSet(Number number, Set<? extends Number> numberSet, String errorMessage) {
        numInSet(number, numberSet, errorMessage, null);
    }

    public static void numInSet(Number number, Set<? extends Number> numberSet, String errorMessage, String fieldName) {
        if (number == null || numberSet == null) {
            throw new ParamInvalidException(errorMessage, fieldName);
        }
        if (!numberSet.contains(number)) {
            throw new ParamInvalidException(errorMessage, fieldName);
        }
    }

}
