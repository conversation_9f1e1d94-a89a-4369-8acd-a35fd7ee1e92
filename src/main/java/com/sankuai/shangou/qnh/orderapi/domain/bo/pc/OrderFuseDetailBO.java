package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.dianping.lion.client.spi.Order;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.*;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderExtend;
import com.meituan.shangou.saas.order.management.client.enums.AfterSaleWhoApplyType;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderLabelModel;
import com.meituan.shangou.saas.order.management.client.utils.DesensitizeReceiverInfoUtil;
import com.meituan.shangou.saas.order.management.client.utils.OrderUtil;
import com.meituan.shangou.saas.order.management.client.utils.param.DesensitizeReceiverInfoExtParam;
import com.meituan.shangou.saas.order.management.client.utils.result.DesensitizeReceiverInfoResult;
import com.meituan.shangou.saas.order.platform.common.LabelModel;

import com.meituan.shangou.saas.order.platform.common.util.OrderItemExtInfoUtil;
import com.meituan.shangou.saas.order.platform.enums.*;
import com.meituan.shangou.saas.order.platform.utils.GsonUtil;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.AfterSaleRecord;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.DeliveryStatusLogDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderOptLogDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ProductInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.PromotionInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderRefundTag;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ChannelLabelVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.*;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChanelOrderApplicantEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelOrderNewStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelOrderStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.PosStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.UserTagTypeEnum;
import com.sankuai.shangou.qnh.orderapi.utils.CombinationProductUtil;
import com.sankuai.shangou.qnh.orderapi.utils.CommonUsedUtil;
import com.sankuai.shangou.qnh.orderapi.utils.ProductLabelUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ExchangeGoodsUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.NumberUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.OrderUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.*;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meituan.shangou.saas.order.platform.ExtDataKeyConstants.ExchangeEk.*;

/**
 * 订单详情
 *
 * @Author: <EMAIL>
 * @Date: 2022/1/2 15:09
 * @Description:
 */
@Setter
@Getter
@NoArgsConstructor
@ToString
public class OrderFuseDetailBO {


    public static List<Integer> ONLINE_PAYMETHODS = PayMethodEnum.ONLINE.getChannelEnumList().stream().map(TradeChannelEnum::getValue).collect(Collectors.toList());


    /**
     * 基础信息
     */
    private BaseInfo baseInfo;

    /**
     * 账单财务信息
     */
    private BillInfo billInfo;

    /**
     * 商品信息
     */
    private List<ItemInfo> itemInfo;


    private LabelModel orderTag;

    private List<Long> labelIdList;


    private List<TagInfoVO> userTags;


    /**
     * 优惠信息
     */
    private List<PromotionInfo> promotionInfo;

    /**
     * 操作日志
     */
    private List<OperateLog> operateLog;

    /**
     * 配送详情
     */
    private List<DeliveryDetail> deliveryDetail;

    /**
     * 订单可操作列表：10-接单操作，20-完成拣货，30-补打小票，40-全单退款，50-部分退款，60-收到退货，70-退差价
     */
    private List<Integer> orderCouldOperateItems;

    /**
     * 订单当前拣货状态
     */
    private Integer pickStatus;

    /**
     * 订单标签
     */
    private List<OrderLabelModel> labelModelList;

    public OrderFuseDetailBO(OrderDetailDTO detail) {

        if (detail.getOrderBaseDto() != null) {
            //基础信息
            baseInfo = new BaseInfo();
            baseInfo.setAmount(detail.getOrderBaseDto().getOriginalAmt() / 100.0);
            baseInfo.setPaidAmount(detail.getOrderBaseDto().getActualPayAmt() / 100.0);
            baseInfo.setMerchantAmount(detail.getOrderBaseDto().getBizReceiveAmt() / 100.0);
            baseInfo.setChannelName(detail.getOrderBaseDto().getChannelName());
            baseInfo.setOrderId(detail.getOrderBaseDto().getChannelOrderId());
            baseInfo.setPackageAmount(detail.getOrderBaseDto().getPackageAmt() / 100.0);
            baseInfo.setDeliveryAmount(detail.getOrderBaseDto().getFreight() / 100.0);
            baseInfo.setPackageAmount(detail.getOrderBaseDto().getPackageAmt() / 100.0);
            baseInfo.setNeedInvoice(detail.getOrderBaseDto().isIsNeedInvoice());
            baseInfo.setInvoiceTitle(detail.getOrderBaseDto().getInvoiceTitle());
            baseInfo.setInvoiceTaxNo(detail.getOrderBaseDto().getTaxNo());
            baseInfo.setCreateTime(new Date(detail.getOrderBaseDto().getCreateTime()));
            baseInfo.setPlatformAmount(detail.getOrderBaseDto().getPlatformFee() / 100.0);
            baseInfo.setPaidOnline(ONLINE_PAYMETHODS.contains(detail.getOrderBaseDto().getPayMethod()));
            baseInfo.setReceiverAddress(detail.getOrderBaseDto().getReceiveAddress());
            baseInfo.setReceiverName(detail.getOrderBaseDto().getReceiverName());
            baseInfo.setReceiverPhone(detail.getOrderBaseDto().getReceiverPhone());
            baseInfo.setReceiverPrivacyPhone(detail.getOrderBaseDto().getReceiverPrivacyPhone());
            baseInfo.setPoiId(detail.getOrderBaseDto().getShopId());
            baseInfo.setPoiName(detail.getOrderBaseDto().getShopName());
            baseInfo.setRiderPhone(detail.getOrderBaseDto().getDeliveryUserPhone());
            baseInfo.setDeliveryMethod(detail.getOrderBaseDto().getDeliveryMethodName());
            baseInfo.setRiderName(detail.getOrderBaseDto().getDeliveryUserName());
            baseInfo.setStatus(ConverterUtils.nonNullConvert(
                    ChannelOrderStatusEnum.getByCode(detail.getOrderBaseDto().getChannelOrderStatus())
                    , s -> s.getDesc(), detail.getOrderBaseDto().getChannelOrderStatusDesc()));
            baseInfo.setAfterSaleApplyStatus(String.valueOf(detail.getOrderBaseDto().getLastAfterSaleApplyStatus()));
            baseInfo.setBizActivityAmt(
                    new BigDecimal(String.valueOf(detail.getOrderBaseDto().getBizActivityAmt())).divide(
                            new BigDecimal("100")).doubleValue());
            baseInfo.setOfflineTotalAmt(
                    new BigDecimal(String.valueOf(detail.getOrderBaseDto().getOfflinePriceTotal())).divide(
                            new BigDecimal("100")).doubleValue());
            baseInfo.setSaleBenefit(new BigDecimal(String.valueOf(detail.getOrderBaseDto().getSaleBenefit())).divide(
                    new BigDecimal("100")).doubleValue());
            baseInfo.setArrivalTime(new Date(detail.getOrderBaseDto().getEstimatedSendArriveTimeEnd()));
            baseInfo.setComment(
                    detail.getOrderBaseDto().getComments().equals("0") ? "" : detail.getOrderBaseDto().getComments());

            if (AfterSaleApplyStatusEnum.enumof(detail.getOrderBaseDto()
                    .getLastAfterSaleApplyStatus()) == AfterSaleApplyStatusEnum.AUDITED_REJECT) {
                baseInfo.setRefundReason(detail.getOrderBaseDto().getLastAfterSaleApplyRejectReason());
            } else {
                baseInfo.setRefundReason(detail.getOrderBaseDto().getLastAfterSaleApplyReason());
            }
            //暂时把退差价映射为部分退款，以后迭代会改
            int refundTagId = detail.getOrderBaseDto().getLastAfterSaleApplyRefundTagId();
            if (refundTagId == OrderRefundTag.WEIGHT_REFUND.getValue()) {
                refundTagId = OrderRefundTag.PART_REFUND.getValue();
            }
            baseInfo.setRefundTagId(refundTagId);
            baseInfo.setRefundableOrderAmount(detail.getOrderBaseDto().getRefundableOrderAmount() / 100.0);

            baseInfo.setOrderType(detail.getOrderBaseDto().getOrderBookingType() == 0 ? "实时订单" : "预约订单");
            baseInfo.setMemberCard(
                    StringUtils.isNotBlank(detail.getOrderBaseDto().getMemberCardNum()) ? detail.getOrderBaseDto()
                            .getMemberCardNum() : "非会员");
            if (CollectionUtils.isNotEmpty(detail.getAfterSaleRecords())) {
                Collections.sort(detail.getAfterSaleRecords(),
                        (a, b) -> (int) ((b.getCreateTime() - a.getCreateTime()) / 1000));
                AfterSaleRecord afterSaleRecord = detail.getAfterSaleRecords().get(0);
                baseInfo.setServiceId(afterSaleRecord.getAfterSaleId());
                baseInfo.setAfsApplyType(String.valueOf(afterSaleRecord.getAfsApplyType()));
                baseInfo.setApplicant(ConverterUtils.nonNullConvert(
                        ChanelOrderApplicantEnum.getByCode(afterSaleRecord.getWhoApplyType()),
                        ChanelOrderApplicantEnum::getDesc));
            }
        }

        List<OrderOptLogDTO> weightRefundLog = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(detail.getAfterSaleRecords())) {
            for (AfterSaleRecord afterSaleRecord : detail.getAfterSaleRecords()) {
                if (afterSaleRecord.getAfsPattern() == AfterSalePatternEnum.WEIGHT.getValue()) {
                    OrderOptLogDTO operateLogDto = new OrderOptLogDTO();
                    operateLogDto.setOptContent("商家按重量退差价");
                    operateLogDto.setOptTime(afterSaleRecord.getUpdateTime());
                    operateLogDto.setOptDesc(afterSaleRecord.getApplyReason());
                    weightRefundLog.add(operateLogDto);
                }
            }
        }
        //商品信息
        itemInfo = ConverterUtils.convertList(detail.getProducInfotList(), ItemInfo::new);

        //促销信息
        promotionInfo = ConverterUtils.convertList(detail.getPromotionInfoList(), PromotionInfo::new);

        List<OrderOptLogDTO> operateLogs = detail.getOrderOpLogList();
        if (CollectionUtils.isNotEmpty(weightRefundLog)) {
            operateLogs.addAll(weightRefundLog);
            operateLogs = operateLogs.stream()
                    .sorted(Comparator.comparingLong(OrderOptLogDTO::getOptTime).reversed())
                    .collect(Collectors.toList());
        }
        //操作日志
        operateLog = ConverterUtils.convertList(operateLogs, OperateLog::new);

        //配送详情
        deliveryDetail = ConverterUtils.convertList(detail.getDeliveryStateLogList(), DeliveryDetail::new);

    }

    public void mergeFinanceInfo(OrderFuseFinanceDetailBO orderFuseFinanceDetailBO){
        if (orderFuseFinanceDetailBO != null){
            billInfo = new BillInfo();
            billInfo.setBizReceiveAmt(orderFuseFinanceDetailBO.getBizReceiveAmt());
            billInfo.setSelfPickService(orderFuseFinanceDetailBO.getSelfPickServiceFee());
            billInfo.setPerformService(orderFuseFinanceDetailBO.getPerformanceServiceFee());
            billInfo.setPlatItemDiscount(orderFuseFinanceDetailBO.getPlatItemPromotion());
            billInfo.setPlatDiscount(orderFuseFinanceDetailBO.getPlatPromotion());
            billInfo.setPoiItemDiscount(orderFuseFinanceDetailBO.getPoiItemPromotion());
            billInfo.setPoiDiscount(orderFuseFinanceDetailBO.getPoiPromotion());
            billInfo.setCommission(orderFuseFinanceDetailBO.getCommission());
            billInfo.setLogisticsCommission(orderFuseFinanceDetailBO.getLogisticsCommission());
            billInfo.setPlatPackage(orderFuseFinanceDetailBO.getPlatPackageIncome());
            billInfo.setPoiPackage(orderFuseFinanceDetailBO.getPoiPackageIncome());
            billInfo.setPlatPackageDiscount(orderFuseFinanceDetailBO.getPlatPackagePromotion());
            billInfo.setPoiPackageDiscount(orderFuseFinanceDetailBO.getPoiPackagePromotion());
            billInfo.setActualPayPackage(orderFuseFinanceDetailBO.getPayPackageFee());
            billInfo.setTotalPackage(orderFuseFinanceDetailBO.getOriginalPackageFee());
            billInfo.setOriginalLogisticsAmt(orderFuseFinanceDetailBO.getOriginalLogisticsFee());
            billInfo.setPlatLogisticsDiscount(orderFuseFinanceDetailBO.getPlatLogisticsPromotion());
            billInfo.setPoiLogisticsDiscount(orderFuseFinanceDetailBO.getPoiLogisticsPromotion());
            billInfo.setPoiLogisticsIncome(orderFuseFinanceDetailBO.getPoiLogisticsIncome());
            billInfo.setCustomerLogisticsTips(orderFuseFinanceDetailBO.getCustomerLogisticsTips());
            billInfo.setPoiLogisticsTips(orderFuseFinanceDetailBO.getPoiLogisticsTips());
            billInfo.setBaseFreight(Optional.ofNullable(orderFuseFinanceDetailBO.getBaseFreight()).orElse(0.0));
            billInfo.setWeightFreight(Optional.ofNullable(orderFuseFinanceDetailBO.getWeightFreight()).orElse(0.0));
            billInfo.setDistanceFreight(Optional.ofNullable(orderFuseFinanceDetailBO.getDistanceFreight()).orElse(0.0));
            billInfo.setTimeFrameFreight(Optional.ofNullable(orderFuseFinanceDetailBO.getTimeFrameFreight()).orElse(0.0));
            billInfo.setPoiFarDistanceFreight(Optional.ofNullable(orderFuseFinanceDetailBO.getPoiFarDistanceFreight()).orElse(0.0));
            billInfo.setPurchaseAmt(orderFuseFinanceDetailBO.getPurchaseAmt());
            billInfo.setPurchaseDiscountAmt(orderFuseFinanceDetailBO.getPurchaseDiscountAmt());
            billInfo.setSupplierMarketDiscount(orderFuseFinanceDetailBO.getSupplierMarketPromotion());
            billInfo.setPoiMarketDiscount(orderFuseFinanceDetailBO.getPoiMarketPromotion());
            billInfo.setOriginalAmt(orderFuseFinanceDetailBO.getOriginalAmt());
            billInfo.setActualPayAmt(orderFuseFinanceDetailBO.getActualPayAmt());
            billInfo.setDonationAmt(orderFuseFinanceDetailBO.getDonationAmt());
            billInfo.setScoreDeduction(orderFuseFinanceDetailBO.getScoreDeduction());
            billInfo.setItemSaleAmt(orderFuseFinanceDetailBO.getItemSaleAmt());
            billInfo.setItemOriginalAmt(orderFuseFinanceDetailBO.getItemOriginalAmt());
            billInfo.setBoxAmt(orderFuseFinanceDetailBO.getBoxAmt());
            billInfo.setPayLogisticsAmt(orderFuseFinanceDetailBO.getPayFreightFee());
            billInfo.setShopCardTotalFee(orderFuseFinanceDetailBO.getShopCardTotalFee());
            billInfo.setShopCardBaseFee(orderFuseFinanceDetailBO.getShopCardBaseFee());
            billInfo.setShopCardGiveFee(orderFuseFinanceDetailBO.getShopCardGiveFee());
            billInfo.setShopCardPlatformGiveFeeShare(orderFuseFinanceDetailBO.getShopCardPlatformGiveFeeShare());
            billInfo.setShopCardShopGiveFeeShare(orderFuseFinanceDetailBO.getShopCardShopGiveFeeShare());
            billInfo.setRedpackAmountTotal(orderFuseFinanceDetailBO.getRedpackAmountTotal());
            billInfo.setRedpackAmountPlatform(orderFuseFinanceDetailBO.getRedpackAmountPlatform());
            billInfo.setRedpackAmountMerchant(orderFuseFinanceDetailBO.getRedpackAmountMerchant());
            billInfo.setFastDeliveryAmt(orderFuseFinanceDetailBO.getFastDeliveryAmt());
            billInfo.setBaseServiceFee(orderFuseFinanceDetailBO.getBaseServiceFee());
            if(StringUtils.isNotBlank(orderFuseFinanceDetailBO.getAddressChangeFee())){
                billInfo.setAddressChangeFee(orderFuseFinanceDetailBO.getAddressChangeFee());
            }
            itemInfo.forEach(v->{
                if (CollectionUtils.isNotEmpty(orderFuseFinanceDetailBO.getItemInfos())){
                    Optional<OrderFuseFinanceDetailBO.ItemFinanceInfo> itemFinanceInfo = orderFuseFinanceDetailBO.getItemInfos()
                            .stream().filter(item->Objects.equals(v.getOrderItemId(), item.getOrderItemId())).findFirst();
                    itemFinanceInfo.ifPresent(v::mergeFinanceInfo);
                }
            });
        }
    }

    public OrderFuseDetailBO(OrderDetailVo detail) {

        if (detail.getOrderBaseDto() != null) {
            // 基础信息
            baseInfo = new BaseInfo();
            baseInfo.setEmpowerOrderId(String.valueOf(detail.getOrderBaseDto().getOrderId()));
            baseInfo.setChannelId(detail.getOrderBaseDto().channelId);
            baseInfo.setTenantId(detail.getOrderBaseDto().tenantId);
            baseInfo.setAmount(detail.getOrderBaseDto().getOriginalAmt() / 100.0);
            baseInfo.setPaidAmount(detail.getOrderBaseDto().getActualPayAmt() / 100.0);
            baseInfo.setMerchantAmount(detail.getOrderBaseDto().getBizReceiveAmt() / 100.0);
            baseInfo.setChannelName(detail.getOrderBaseDto().getChannelName());
            baseInfo.setOrderId(detail.getOrderBaseDto().getChannelOrderId());
            baseInfo.setPackageAmount(detail.getOrderBaseDto().getPackageAmt() / 100.0);
            baseInfo.setDeliveryAmount(detail.getOrderBaseDto().getFreight() / 100.0);
            baseInfo.setPackageAmount(detail.getOrderBaseDto().getPackageAmt() / 100.0);
            baseInfo.setNeedInvoice(Optional.ofNullable(detail.getOrderBaseDto().getIsNeedInvoice()).orElse(Boolean.FALSE));
            baseInfo.setInvoiceTitle(detail.getOrderBaseDto().getInvoiceTitle());
            baseInfo.setInvoiceTaxNo(detail.getOrderBaseDto().getTaxNo());
            baseInfo.setInvoiceType(detail.getOrderBaseDto().getInvoiceType());
            if (detail.getOrderBaseDto().getOrderExtend() != null) {
                baseInfo.setInvoiceMoney(detail.getOrderBaseDto().getOrderExtend().getInvoiceMoney());
            } else {
                baseInfo.setInvoiceMoney(0);
            }
            baseInfo.setCreateTime(new Date(detail.getOrderBaseDto().getCreateTime()));
            baseInfo.setPlatformAmount(detail.getOrderBaseDto().getPlatformFee() / 100.0);
            baseInfo.setPaidOnline(ONLINE_PAYMETHODS.contains(detail.getOrderBaseDto().getPayMethod()));
            baseInfo.setReceiverAddress(detail.getOrderBaseDto().getReceiveAddress());
            baseInfo.setReceiverName(detail.getOrderBaseDto().getReceiverName());
            baseInfo.setReceiverPhone(detail.getOrderBaseDto().getReceiverPhone());
            baseInfo.setDispatchShopId(detail.orderBaseDto.getDispatchShopId());
            // 使用了隐私号 && 歪马渠道 && 隐私号不为空
            if(NumberUtils.INTEGER_ONE.equals(detail.getOrderBaseDto().getUsePrivacyPhone())
                    && detail.getOrderBaseDto().getChannelId().equals(ChannelType.MT_DRUNK_HORSE.getValue())
                    && StringUtils.isNotBlank(detail.getOrderBaseDto().getReceiverPrivacyPhone())) {
                baseInfo.setReceiverPhone(detail.getOrderBaseDto().getReceiverPrivacyPhone());
            }
            baseInfo.setReceiverPrivacyPhone(detail.getOrderBaseDto().getReceiverPrivacyPhone());
            baseInfo.setPoiId(detail.getOrderBaseDto().getShopId());
            baseInfo.setPoiName(detail.getOrderBaseDto().getShopName());
            baseInfo.setRiderPhone(detail.getOrderBaseDto().getDeliveryUserPhone());
            baseInfo.setDeliveryMethod(detail.getOrderBaseDto().getDeliveryMethodName());
            baseInfo.setRiderName(detail.getOrderBaseDto().getDeliveryUserName());
            baseInfo.setStatus(ConverterUtils.nonNullConvert(ChannelOrderStatusEnum.getByCode(detail.getOrderBaseDto().getChannelOrderStatus())
                    , s -> s.getDesc(), detail.getOrderBaseDto().getChannelOrderStatusDesc()));
            baseInfo.setAfterSaleApplyStatus(String.valueOf(detail.getOrderBaseDto().getLastAfterSaleApplyStatus()));
            if(OrderSourceEnum.GLORY.getValue() != detail.getOrderBaseDto().getOrderSource()){
                baseInfo.setBizActivityAmt(new BigDecimal(String.valueOf(detail.getOrderBaseDto().getBizActivityAmt())).divide(new BigDecimal("100")).doubleValue());
            }
            baseInfo.setOfflineTotalAmt(new BigDecimal(String.valueOf(detail.getOrderBaseDto().getOfflinePriceTotal())).divide(new BigDecimal("100")).doubleValue());
            baseInfo.setSaleBenefit(new BigDecimal(String.valueOf(detail.getOrderBaseDto().getSaleBenefit())).divide(new BigDecimal("100")).doubleValue());
            baseInfo.setArrivalTimeStart(new Date(detail.getOrderBaseDto().getEstimatedSendArriveTimeStart()));
            baseInfo.setArrivalTime(new Date(detail.getOrderBaseDto().getEstimatedSendArriveTimeEnd()));
            baseInfo.setComment(detail.getOrderBaseDto().getComments().equals("0")?"":detail.getOrderBaseDto().getComments());

            if (AfterSaleApplyStatusEnum.enumof(detail.getOrderBaseDto().getLastAfterSaleApplyStatus()) == AfterSaleApplyStatusEnum.AUDITED_REJECT) {
                baseInfo.setRefundReason(detail.getOrderBaseDto().getLastAfterSaleApplyRejectReason());
            } else {
                baseInfo.setRefundReason(detail.getOrderBaseDto().getLastAfterSaleApplyReason());
            }
            // 暂时把退差价映射为部分退款，以后迭代会改
            int refundTagId=detail.getOrderBaseDto().getLastAfterSaleApplyRefundTagId();
            if (refundTagId==OrderRefundTag.WEIGHT_REFUND.getValue()){
                refundTagId=OrderRefundTag.PART_REFUND.getValue();
            }
            baseInfo.setRefundTagId(refundTagId);
            baseInfo.setDistributeMethod(detail.getOrderBaseDto().getDistributeMethod());
            baseInfo.setRefundableOrderAmount(detail.getOrderBaseDto().getRefundableOrderAmount() / 100.0);

            baseInfo.setOrderType(detail.getOrderBaseDto().getOrderBookingType() == 0 ? "实时订单" : "预约订单");
            baseInfo.setMemberCard(StringUtils.isNotBlank(detail.getOrderBaseDto().getMemberCardNum()) ? detail.getOrderBaseDto().getMemberCardNum() : "非会员");
            if (CollectionUtils.isNotEmpty(detail.getAfterSaleRecords())) {
                Collections.sort(detail.getAfterSaleRecords(), (a, b) -> (int) ((b.getCreateTime() - a.getCreateTime()) / 1000));
                AfterSaleRecordVo afterSaleRecord = detail.getAfterSaleRecords().get(0);
                baseInfo.setServiceId(afterSaleRecord.getAfterSaleId());
                baseInfo.setAfsApplyType(String.valueOf(afterSaleRecord.getAfsApplyType()));
                baseInfo.setApplicant(AfterSaleWhoApplyType.findByValue(afterSaleRecord.getWhoApplyType()).getDesc());
            }

            baseInfo.setOfflineOrderId(detail.getOrderBaseDto().getOrderId());
            baseInfo.setWarehouseId(detail.getOrderBaseDto().getWarehouseId());
            baseInfo.setWarehouseName(detail.getOrderBaseDto().getWarehouseName());
            baseInfo.setDeliveryStatus(detail.getOrderBaseDto().getDeliveryStatus());
            if (detail.getOrderBaseDto().getDeliveryStatus() != null) {
                baseInfo.setDeliveryStatusDesc(Optional.ofNullable(DeliveryStatusEnum.enumOf(detail.getOrderBaseDto().getDeliveryStatus())).map(DeliveryStatusEnum::getDesc).orElse(null));
            }
            if (detail.getOrderBaseDto().getReceiveTime() != null && detail.getOrderBaseDto().getReceiveTime() > 0){
                baseInfo.setReceiveTime(new Date(detail.getOrderBaseDto().getReceiveTime()));
            }
            if (Objects.nonNull(detail.getOrderBaseDto().getPosTime())  && detail.getOrderBaseDto().getPosTime() > 1000) {
                baseInfo.setWriteOffTime(new Date(detail.getOrderBaseDto().getPosTime()));
            }
            baseInfo.setWriteOffStatus(Optional.ofNullable(PosStatusEnum.converterFromOrderPosStatus(detail.getOrderBaseDto().getPosStatus())).map(PosStatusEnum::getCode).orElse(null));
            baseInfo.setPosErrorMsg(detail.getOrderBaseDto().getPosErrorMsg());
            baseInfo.setPosCheckTag(detail.getOrderBaseDto().getPosCheckTag());
            baseInfo.setOrderSerialNumber(detail.getOrderBaseDto().getOrderSerialNumber());
            baseInfo.setOrderSerialNumberStr(detail.getOrderBaseDto().getOrderSerialNumberStr());
            baseInfo.setDistributeStatus(detail.getDeliveryInfoDTO().getDistributeStatus());
            baseInfo.setFuseOrderStatusCode(detail.getOrderBaseDto().getFuseOrderStatus() == null ? detail.getOrderBaseDto().getOrderStatus(): detail.getOrderBaseDto().getFuseOrderStatus());

            String fuseOrderStatus = Optional.ofNullable(baseInfo.getHistoryOrderStatus(detail.getOrderBaseDto().getFuseOrderStatus(), detail.getOrderBaseDto().getOrderStatus()))
                    .map(ChannelOrderNewStatusEnum::getDesc)
                    .orElse(null);
            baseInfo.setFuseOrderStatus(fuseOrderStatus);

            //订单当前拣货状态
            pickStatus = detail.getDeliveryInfoDTO().getDeliveryStatus();

            if (detail.getOrderBaseDto().getCompleteTime() != null && detail.getOrderBaseDto().getCompleteTime() > 0){
                baseInfo.setCompleteTime(new Date(detail.getOrderBaseDto().getCompleteTime()));
            }
            if (Objects.equals(OrderStatusEnum.CANCELED.getValue(), detail.getOrderBaseDto().getOrderStatus())){
                baseInfo.setCancelTime(getCancelTime(detail.getOrderOpLogList()));
            }
            //地址变更费
            OrderExtend orderExtend = detail.getOrderBaseDto().getOrderExtend();
            String addressChangeFee = Objects.nonNull(orderExtend) ? orderExtend.getAddressChangeFee() : null;
            CommonUsedUtil.dealAddressChangeFeeToBaseInfo(addressChangeFee, baseInfo, false);

            List<OrderOptLogVo> weightRefundLog= Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(detail.getAfterSaleRecords())) {
                for (AfterSaleRecordVo afterSaleRecord : detail.getAfterSaleRecords()) {
                    if (afterSaleRecord.getAfsPattern() == AfterSalePatternEnum.WEIGHT.getValue()) {
                        OrderOptLogVo operateLog = new OrderOptLogVo();
                        operateLog.setOptContent("商家按重量退差价");
                        operateLog.setOptTime(afterSaleRecord.getUpdateTime());
                        operateLog.setOptDesc(afterSaleRecord.getApplyReason());
                        weightRefundLog.add(operateLog);
                    }
                }
            }
            // 商品信息
            itemInfo = ConverterUtils.convertList(detail.getProductInfoList(), ItemInfo::new);

            baseInfo.setWeight(itemInfo.stream().map(ItemInfo::getWeight).reduce(0,Integer::sum));

            baseInfo.setSelfFetchCode(detail.getOrderBaseDto().getSelfFetchCode());
            baseInfo.setSelfFetchStatus(detail.getOrderBaseDto().getSelfFetchStatus());
            baseInfo.setExpensiveProductPickupCode(detail.getOrderBaseDto().getExpensiveProductPickupCode());
            baseInfo.setOriginalDistributeType(detail.getDeliveryInfoDTO().getOriginalDistributeType());
            baseInfo.setSelfDelivery(detail.getDeliveryInfoDTO().getSelfDelivery());
            baseInfo.setDeliveryChannelId(detail.getDeliveryInfoDTO().getDeliveryChannelId());
            baseInfo.setIsFastOrder(detail.getOrderBaseDto().getIsFastOrder());
            baseInfo.setIsFastToSelfDelivery(detail.getOrderBaseDto().getIsFastToSelfDelivery());
            baseInfo.setFastDeliveryAmt(detail.getOrderBaseDto().getFastDeliveryAmt());

            userTags = UserTagTypeEnum.getTags(detail.getOrderBaseDto().getTags());

            // 促销信息
            promotionInfo = ConverterUtils.convertList(detail.getPromotionInfoList(), PromotionInfo::new);


            List<OrderOptLogVo> operateLogs = detail.getOrderOpLogList();
            if (CollectionUtils.isNotEmpty(weightRefundLog)){
                operateLogs.addAll(weightRefundLog);
                operateLogs=operateLogs.stream().sorted(Comparator.comparingLong(OrderOptLogVo::getOptTime).reversed()).collect(Collectors.toList());
            }
            // 操作日志
            operateLog = ConverterUtils.convertList(operateLogs, OperateLog::new);
            // 订单已完结
            if (OrderUtil.isOrderEnd(detail.getOrderBaseDto().getOrderStatus())){
                baseInfo.setFinalOrderEndTime(OrderUtil.getOrderTimeByLogVo(detail.getOrderBaseDto().getOrderStatus(), operateLogs));
            }
            // 配送详情
            deliveryDetail = ConverterUtils.convertList(detail.getDeliveryStateLogList(), DeliveryDetail::new);
            if (StringUtils.isNotBlank(detail.getOrderBaseDto().getOrderMark())){
                orderTag = new LabelModel(detail.getOrderBaseDto().getOrderMark());
                labelIdList = Arrays.stream(detail.getOrderBaseDto().getOrderMark().split(","))
                        .filter(StringUtils::isNotBlank)
                        .map(Long::parseLong)
                        .collect(Collectors.toList());

            }else {
                orderTag = new LabelModel();
                labelIdList = Lists.newArrayList();
            }
        }
    }

    private Date getCancelTime(List<OrderOptLogVo> orderOpLogList) {
        if (CollectionUtils.isNotEmpty(orderOpLogList)){
            Optional<Long> cancelTime = orderOpLogList.stream()
                    .filter(v -> Objects.equals(v.getTargetStatus(), OrderStatusEnum.CANCELED.getValue()))
                    .map(OrderOptLogVo::getOptTime)
                    .sorted()
                    .findFirst();
            if (cancelTime.isPresent()){
                return new Date(cancelTime.get());
            }
        }
        return null;
    }


    /**
     * 转换为VO对象
     *
     * @return
     */
    public OrderFuseDetailVO toOrderFuseDetailVO() {
        OrderFuseDetailVO orderFuseDetailVO = new OrderFuseDetailVO();
        orderFuseDetailVO.setBaseInfo(ConverterUtils.nonNullConvert(baseInfo, BaseInfo::toBaseInfoVO));
        orderFuseDetailVO.setBillInfo(ConverterUtils.nonNullConvert(billInfo, BillInfo::toBillInfoVO));
        orderFuseDetailVO.setUserTags(userTags);
        orderFuseDetailVO.setOrderTag(convert2OrderTagVO(orderTag, labelIdList, labelModelList));
        //itemInfo中包含换货信息，单独处理一下
        orderFuseDetailVO.setItemInfo(buildItemInfoVO2ExchangeSchme());
        orderFuseDetailVO.setPromotionInfo(ConverterUtils.convertList(promotionInfo, PromotionInfo::toPromotionInfoVO));
        orderFuseDetailVO.setOperateLog(ConverterUtils.convertList(operateLog, OperateLog::toOperateLogVO));
        orderFuseDetailVO.setDeliveryDetail(ConverterUtils.convertList(deliveryDetail, DeliveryDetail::toDeliveryDetailVO));
        orderFuseDetailVO.setOrderCouldOperateItems(orderCouldOperateItems);

        int productCount = itemInfo.stream().map(ItemInfo::getQuantity).reduce(0, Integer::sum);
        long productCategoryCount = itemInfo.stream().map(v-> v.getSku() + v.getCustomSkuId() + v.getSkuName()).distinct().count();
        double productAmount = itemInfo.stream().map(v->v.getSalePrice() * v.getQuantity()).reduce(0D, Double::sum);

        orderFuseDetailVO.getBaseInfo().setProductCategoryCount((int) productCategoryCount);
        orderFuseDetailVO.getBaseInfo().setProductAmount(ConverterUtils.formatMoney(productAmount));
        orderFuseDetailVO.getBaseInfo().setProductCount(productCount);
        CommonUsedUtil.dealAddressChangeFee(orderFuseDetailVO.getBaseInfo().getChannelId(), orderFuseDetailVO.getBillInfo(), false);

        return orderFuseDetailVO;
    }

    private List<OrderTagVO> convert2OrderTagVO(LabelModel label, List<Long> labelIdList, List<OrderLabelModel> orderLabelModelList) {
        if (label == null){
            return Lists.newArrayList();
        }
        if(CollectionUtils.isNotEmpty(labelModelList)){
            return OrderTagVO.convertOrderTagList(labelIdList, labelModelList);
        }
        return label.getLabels().stream().map(v->new OrderTagVO(v.getId(), v.getParentId(), v.getDesc()))
                .filter(v -> v.getParentType() != null)
                .collect(Collectors.toList());
    }

    /**
     * 将扁平的itemList组合成有所属关系的结构
     *
     * @return
     */
    private List<ItemInfoVO> buildItemInfoVO2ExchangeSchme() {
        List<ItemInfoVO> result = new ArrayList<>();
        Map<Long, List<ExchangeItemInfoVO>> exchangeProductMap = new HashMap<>();
        for (ItemInfo currentItemInfo:itemInfo) {
            if(StringUtils.isBlank(currentItemInfo.getExtData())){
                result.add(currentItemInfo.toItemInfoVO());
                continue;
            }

            Map<String, Object> stringObjectMap = GsonUtil.toObjMap(currentItemInfo.getExtData());
            Object exchangeOrderItemId = stringObjectMap.get(EXE_CHANGE_ORDER_ITEM_ID_FROM_KEY);
            if(Objects.isNull(exchangeOrderItemId)){
                result.add(currentItemInfo.toItemInfoVO());
                continue;
            }

            Long sourceOrderItemId = Long.parseLong(String.valueOf(exchangeOrderItemId));
            Object exchangeOrderItemCnt = stringObjectMap.get(EXE_CHANGE_ORDER_ITEM_CNT_FROM_KEY);
            ExchangeItemInfoVO exchangeItemInfoVO = new ExchangeItemInfoVO();

            exchangeItemInfoVO.setItemInfoVO(currentItemInfo.toItemInfoVO());
            exchangeItemInfoVO.setExchangeFromCnt(Integer.parseInt(String.valueOf(exchangeOrderItemCnt)));
            exchangeItemInfoVO.setExchangeToCnt(currentItemInfo.getQuantity());
            //换货商品原/现售价 重量 在extData字段 需要单独赋值
            ExchangeGoodsUtil.dealSetPriceAndWeight(exchangeItemInfoVO, stringObjectMap);

            if(exchangeProductMap.containsKey(sourceOrderItemId)){
                exchangeProductMap.get(sourceOrderItemId).add(exchangeItemInfoVO);
            }else{
                List<ExchangeItemInfoVO> list = new ArrayList<>();
                list.add(exchangeItemInfoVO);
                exchangeProductMap.put(sourceOrderItemId,list);
            }
        }
        result.stream()
                .filter(e->exchangeProductMap.containsKey(e.getOrderItemId()))
                .forEach(e->{
                    e.setExchangeItemInfoVOList(exchangeProductMap.get(e.getOrderItemId()));
                    e.setExchangeCount(exchangeProductMap.get(e.getOrderItemId()).stream().map(ExchangeItemInfoVO::getExchangeFromCnt).reduce(0,Integer::sum));
                    e.setExchangeGoodsCount(exchangeProductMap.get(e.getOrderItemId()).stream().map(ExchangeItemInfoVO::getExchangeToCnt).reduce(0,Integer::sum));
                });

        return result;
    }

    /**
     * 将赠品放入商品列表
     * @param items
     */
    public void addGift2Item(List<ItemInfoVO> items) {
        if(CollectionUtils.isEmpty(promotionInfo)) {
            return;
        }

        promotionInfo.stream()
                .filter(Objects::nonNull)
                .map(PromotionInfo::getOnlineGiftBO)
                .filter(Objects::nonNull)
                .filter(giftItem -> StringUtils.isNotBlank(giftItem.getGiftName())
                        && giftItem.getGiftQuantity() != null && giftItem.getGiftQuantity() > 0)
                .collect(Collectors.toMap(giftItem -> giftItem.getGiftSku() + giftItem.getGiftName(), Function.identity(), (oldItem, newItem) -> {
                    oldItem.setGiftQuantity(oldItem.getGiftQuantity() + newItem.getGiftQuantity());
                    return oldItem;
                }))
                .values()
                .forEach(giftItem -> items.add(giftItem.toItemInfoVO()));
    }


    public List<DeliveryInfoVo> toDeliveryInfoVo(List<DeliveryInfoBO> deliveryInfoBOList, Map<Integer,String> channelMap){

        if(CollectionUtils.isEmpty(deliveryDetail)){
            return Collections.emptyList();
        }
        Map<Integer,DeliveryInfoBO> deliveryInfoBOMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(deliveryInfoBOList)){
            deliveryInfoBOMap=deliveryInfoBOList.stream().collect(Collectors.toMap(DeliveryInfoBO::getDeliveryCount,a->a,(k1,k2)->k2));
        }
        ArrayListMultimap<Integer, DeliveryDetail> deliveryDetailListMap= ArrayListMultimap.create();
        for (DeliveryDetail detail : deliveryDetail){
            Integer deliveryCount=1;
            if(detail.getDeliveryCount()!=null){
                deliveryCount=detail.getDeliveryCount();
            }
            deliveryDetailListMap.put(deliveryCount,detail);
        }
        List<DeliveryInfoVo> infoVoList=new ArrayList<>();
        for (Integer count : deliveryDetailListMap.keySet()){
            List<DeliveryDetail> detailList= deliveryDetailListMap.get(count);
            DeliveryInfoBO infoBO=deliveryInfoBOMap.get(count);
            DeliveryInfoVo infoVo=new DeliveryInfoVo();
            DeliveryInfoVo.DeliveryFeeInfoVO feeInfoVO=new DeliveryInfoVo.DeliveryFeeInfoVO();
            feeInfoVO.setDeliveryCount(count);
            if(infoBO!=null){
                if (MapUtils.isEmpty(channelMap)){
                    DeliveryChannelEnum channelEnum =DeliveryChannelEnum.valueOf(infoBO.getDeliveryChannel());
                    feeInfoVO.setDeliveryChannelName(channelEnum==null ? "":channelEnum.getName());
                }else {
                    feeInfoVO.setDeliveryChannelName((channelMap.getOrDefault(infoBO.getDeliveryChannel(),"")));
                }
                feeInfoVO.setDeliveryFee(infoBO.getDeliveryFee()==null ? "0":new DecimalFormat("#.##").format(infoBO.getDeliveryFee()));
                feeInfoVO.setTipAmount(infoBO.getTipAmount()==null ? "0":new DecimalFormat("#.##").format(infoBO.getTipAmount()));
                feeInfoVO.setRiderName(infoBO.getRiderName());
                feeInfoVO.setRiderPhone(infoBO.getRiderPhone());
            }
            infoVo.setDeliveryFeeInfo(feeInfoVO);
            List<DeliveryDetailVO> detailVOList=new ArrayList<>();
            if(CollectionUtils.isNotEmpty(detailList)){
                detailVOList=ConverterUtils.convertList(detailList, DeliveryDetail::toDeliveryDetailVO);
            }
            infoVo.setDeliveryLogList(detailVOList);
            infoVoList.add(infoVo);
        }
        return infoVoList;
    }


    @Getter
    @Setter
    @NoArgsConstructor
    public static class BaseInfo {

        /**
         * 订单号
         */
        private String orderId;

        /**
         * 订单号
         */
        private String empowerOrderId;

        /**
         * 门店 ID
         */
        private Long poiId;

        /**
         * 门店名称
         */
        private String poiName;


        /**
         * 渠道名称
         */
        private String channelName;

        /**
         * 渠道id
         */
        private Integer channelId;


        /**
         * 收货人名称
         */
        private String receiverName;

        /**
         * 收货人电话
         */
        private String receiverPhone;

        /**
         * 收货人隐私号
         */
        private String receiverPrivacyPhone;

        /**
         * 收货人地址
         */
        private String receiverAddress;

        /**
         * 订单金额
         */
        private double amount;

        /**
         * 实付金额
         */
        private double paidAmount;

        /**
         * 1：配送 2：到店自提
         */
        private Integer distributeMethod;

        /**
         * 商家实收金额
         */
        private double merchantAmount;

        /**
         *
         */
        private double deliveryAmount;

        /**
         * 餐盒费
         */
        private double packageAmount;

        /**
         * 平台服务费
         */
        private double platformAmount;


        /**
         * 是否需要发票
         */
        private boolean needInvoice;


        /**
         * 发票抬头
         */
        private String invoiceTitle;

        /**
         * 税号
         */
        private String invoiceTaxNo;

        /**
         * 发票类型
         */
        private Integer invoiceType;

        /**
         * 发票金额
         */
        private Integer invoiceMoney;


        /**
         * 配送单创建时间
         */
        private Date deliveryOrderCreateTime;


        /**
         * 配送方式
         */
        private String deliveryMethod;


        /**
         * 配送员姓名
         */
        private String riderName;


        /**
         * 配送员电话
         */
        private String riderPhone;

        /**
         * 是否在线支付
         */
        private boolean paidOnline;

        /**
         * 订单状态
         */
        private String status;

        /**
         * 融合订单状态
         */
        private String fuseOrderStatus;


        /**
         * 融合订单状态
         */
        private Integer fuseOrderStatusCode;


        /**
         * 售后审核状态
         */
        private String afterSaleApplyStatus;

        /**
         * 创建时间
         */
        private Date createTime;

        /**
         * 取消时间
         */
        private Date cancelTime;

        /**
         * 退款原因
         */
        private String refundReason;


        /**
         * 退款状态编码
         */
        private int refundTagId;


        private double refundableOrderAmount;


        /**
         * 服务单号
         */
        private String serviceId;

        /**
         * 发起者
         */
        private String applicant;


        /**
         * 售后请求状态
         */
        private String afsApplyType;


        private String orderType;

        private String memberCard;

        /**
         * 商家活动支出
         */
        private Double bizActivityAmt;

        /**
         * 线下总价
         */
        private double offlineTotalAmt;
        /**
         * 销售利润
         */
        private double saleBenefit;

        /**
         * 预计送达开始时间
         */
        private Date arrivalTimeStart;

        /**
         *预计送达时间
         */
        private Date arrivalTime;

        /**
         *完成时间
         */
        private Date completeTime;

        /**
         * 备注
         */
        private String comment;

        /**
         * 百川订单号
         */
        private Long offlineOrderId;

        /**
         * 仓库id
         */
        private Long warehouseId;

        /**
         * 仓库名称
         */
        private String warehouseName;

        private Integer weight;

        //接收订单时间，落库时间
        private Date receiveTime;

        /**
         * 订单流水号
         */
        private Long orderSerialNumber;
        /**
         * 订单序号
         */
        private String orderSerialNumberStr;

        private Integer distributeStatus;
        private String distributeStatusDesc;


        private Integer deliveryStatus;

        private String deliveryStatusDesc;

        /**
         * 核销状态
         */
        private Integer writeOffStatus;

        /**
         * 核销时间
         */
        private Date writeOffTime;

        private String posErrorMsg;

        private Integer posCheckTag;

        private String selfFetchCode;

        private Integer selfFetchStatus;

        /**
         * 贵品取货码
         */
        private String expensiveProductPickupCode;

        /**
         * 配送运力渠道
         */
        private String deliveryChannel;

        /**
         * 配送类型 平台：0 三方：1 自配送：2 其他：-1
         */
        private Integer deliveryChannelType;

        /**
         * 原始配送方式
         */
        private Integer originalDistributeType;

        /**
         * 迁移标识
         */
        private Boolean migrateFlag;

        private Integer selfDelivery;

        private Integer deliveryChannelId;

        private Long dispatchShopId;

        /**
         * 是否闪电送订单，1：是，0：否
         */
        private Integer isFastOrder;

        /**
         * 闪电送费用
         */
        private Integer fastDeliveryAmt;

        /**
         * 是否为闪电送转自送订单
         */
        public Boolean isFastToSelfDelivery;

        /**
         * 租户id
         */
        private Long tenantId;

        /**
         * 地址变更费
         */
        private String addressChangeFee;

        /**
         * 地址变更费注释
         */
        private String addressChangeFeeNotes;

        /**
         * 三方物流单号列表
         */
        private List<String> originWaybillNoList;

        /**
         * 最晚订单完结时间（订单在完结状态【已完成/已取消/已关闭】才有）
         */
        private Long finalOrderEndTime;

        public BaseInfoVO toBaseInfoVO() {
            BaseInfoVO baseInfoVO = new BaseInfoVO();

            baseInfoVO.setEmpowerOrderId(empowerOrderId);
            baseInfoVO.setChannelName(channelName);
            baseInfoVO.setOrderId(orderId);
            baseInfoVO.setPoiId(poiId);
            baseInfoVO.setPoiName(poiName);
            baseInfoVO.setRefundReason(refundReason);
            baseInfoVO.setStatus(status);
            baseInfoVO.setChannelId(channelId);
            baseInfoVO.setWarehouseId(warehouseId);
            baseInfoVO.setDispatchShopId(dispatchShopId);

            //收货人信息
            baseInfoVO.setReceiverName(receiverName);
            baseInfoVO.setReceiverPhone(receiverPhone);
            baseInfoVO.setReceiverAddress(receiverAddress);
            baseInfoVO.setReceiverPrivacyPhone(receiverPrivacyPhone);

            // 订单状态已完结 && 非歪马租户 && 租户支持隐藏隐私号
            if (OrderUtil.isOrderEnd(fuseOrderStatusCode)
                    && !MccConfigUtil.isDrunkHorseTenant(tenantId)
                    && MccDynamicConfigUtil.checkSupportDesensitizeReceiverInfoTenant(tenantId)){
                // 订单超过时间，需要隐藏隐私号
                if(OrderUtil.isOverConfigTime(finalOrderEndTime, MccDynamicConfigUtil.getDesensitizeReceiverInfoTime())){
                    baseInfoVO.setReceiverPhone(null);
                }
            } else if(orderFinishedOverTwoDays(completeTime, createTime) && isOrderNoRefundRecord(afterSaleApplyStatus)){
                //隐私号 完成48小时之后隐藏
                baseInfoVO.setReceiverPhone(null);
                baseInfoVO.setReceiverAddress(CommonConstant.PRIVACY_PROTECT_ADDRESS);
            }else if (orderCancelOverTwoDays(createTime, cancelTime) && isOrderNoRefundRecord(afterSaleApplyStatus)){
                baseInfoVO.setReceiverPhone(null);
                baseInfoVO.setReceiverAddress(CommonConstant.PRIVACY_PROTECT_ADDRESS);
            }

            // 订单收货人信息脱敏处理
            DesensitizeReceiverInfoExtParam receiverInfoExtParam = DesensitizeReceiverInfoExtParam.build(
                    baseInfoVO.getReceiverName(),baseInfoVO.getReceiverAddress(),baseInfoVO.getReceiverPhone(),
                    baseInfoVO.getReceiverPrivacyPhone(), tenantId, fuseOrderStatusCode, finalOrderEndTime);
            DesensitizeReceiverInfoResult desensitizeReceiverInfoResult = DesensitizeReceiverInfoUtil.desensitizeReceiverInfo(receiverInfoExtParam);
            if(Objects.nonNull(desensitizeReceiverInfoResult)) {
                baseInfoVO.setReceiverName(desensitizeReceiverInfoResult.getReceiverName());
                baseInfoVO.setReceiverAddress(desensitizeReceiverInfoResult.getReceiverAddress());
                baseInfoVO.setReceiverPhone(desensitizeReceiverInfoResult.getReceiverPhone());
                baseInfoVO.setReceiverPrivacyPhone(desensitizeReceiverInfoResult.getReceiverPrivacyPhone());
            }

            //金额相关字段
            baseInfoVO.setAmount(ConverterUtils.formatMoney(amount));
            baseInfoVO.setPaidAmount(ConverterUtils.formatMoney(paidAmount));
            baseInfoVO.setMerchantAmount(ConverterUtils.formatMoney(merchantAmount));
            baseInfoVO.setDeliveryAmount(ConverterUtils.formatMoney(deliveryAmount));
            baseInfoVO.setPackageAmount(ConverterUtils.formatMoney(packageAmount));
            baseInfoVO.setPlatformAmount(ConverterUtils.formatMoney(platformAmount));


            //发票相关字段
            baseInfoVO.setNeedInvoice(ConverterUtils.wrap(needInvoice));
            baseInfoVO.setInvoiceTitle(invoiceTitle);
            baseInfoVO.setInvoiceTaxNo(invoiceTaxNo);
            baseInfoVO.setInvoiceType(invoiceType);
            baseInfoVO.setInvoiceMoney(invoiceMoney);

            //骑手
            baseInfoVO.setRiderName(riderName);
            baseInfoVO.setRiderPhone(riderPhone);
            baseInfoVO.setDeliveryMethod(deliveryMethod);

            baseInfoVO.setPaidOnline(ConverterUtils.wrap(paidOnline));
            baseInfoVO.setCreateTime(ConverterUtils.nonNullConvert(createTime, time -> DateFormatUtils.format(time, Constants.DateFormats.SHOW_FORMAT)));
            baseInfoVO.setReceiveTime(ConverterUtils.nonNullConvert(receiveTime, time -> DateFormatUtils.format(time, Constants.DateFormats.SHOW_FORMAT)));

            baseInfoVO.setRefundTagId(String.valueOf(refundTagId));
            baseInfoVO.setAfterSaleApplyStatus(afterSaleApplyStatus);
            baseInfoVO.setRefundableOrderAmount(ConverterUtils.formatMoney(refundableOrderAmount));

            baseInfoVO.setServiceId(serviceId);
            baseInfoVO.setApplicant(applicant);
            baseInfoVO.setAfsApplyType(afsApplyType);

            baseInfoVO.setOrderType(orderType);
            baseInfoVO.setMemberCard(memberCard);

            baseInfoVO.setBizActivityAmt(bizActivityAmt == null ? "-" : ConverterUtils.formatMoney(bizActivityAmt));
            baseInfoVO.setOfflineTotalAmt(ConverterUtils.formatMoney(offlineTotalAmt));
            baseInfoVO.setSaleBenefit(ConverterUtils.formatMoney(saleBenefit));

            baseInfoVO.setArrivalTimeStart(ConverterUtils.nonNullConvert(arrivalTimeStart, time -> DateFormatUtils.format(time, Constants.DateFormats.SHOW_FORMAT)));
            baseInfoVO.setArrivalTime(ConverterUtils.nonNullConvert(arrivalTime, time -> DateFormatUtils.format(time, Constants.DateFormats.SHOW_FORMAT)));
            baseInfoVO.setComment(comment);
            baseInfoVO.setWarehouseId(warehouseId);
            baseInfoVO.setWarehouseName(warehouseName);
            baseInfoVO.setWeight(weight);
            baseInfoVO.setOrderSerialNumber(orderSerialNumber);
            baseInfoVO.setOrderSerialNumberStr(orderSerialNumberStr);
            baseInfoVO.setDistributeStatus(distributeStatus);
            baseInfoVO.setDistributeMethod(distributeMethod);
            baseInfoVO.setFuseOrderStatus(fuseOrderStatus);
            baseInfoVO.setFuseOrderStatusCode(fuseOrderStatusCode);
            baseInfoVO.setDeliveryStatus(deliveryStatus);
            baseInfoVO.setDeliveryStatusDesc(deliveryStatusDesc);
            baseInfoVO.setWriteOffTime(ConverterUtils.nonNullConvert(writeOffTime, time -> DateFormatUtils.format(time, Constants.DateFormats.SHOW_FORMAT)));
            baseInfoVO.setWriteOffStatus(writeOffStatus);
            baseInfoVO.setPosErrorMsg(posErrorMsg);
            baseInfoVO.setPosCheckTag(posCheckTag);
            if (StringUtils.isNotBlank(distributeStatusDesc)){
                baseInfoVO.setDistributeStatusDesc(distributeStatusDesc);
            } else if (distributeStatus != null){
                baseInfoVO.setDistributeStatusDesc(Optional.ofNullable(DistributeStatusEnum.enumOf(distributeStatus))
                        .map(DistributeStatusEnum::getDesc).orElse(null));
            }
            baseInfoVO.setSelfFetchCode(selfFetchCode);
            baseInfoVO.setSelfFetchStatus(selfFetchStatus);
            baseInfoVO.setExpensiveProductPickupCode(expensiveProductPickupCode);
            baseInfoVO.setOriginalDistributeType(originalDistributeType);
            baseInfoVO.setDeliveryChannel(deliveryChannel);
            baseInfoVO.setDeliveryChannelType(deliveryChannelType);
            baseInfoVO.setToken(OrderUtils.generateMD5Token(orderId, channelId, tenantId));
            baseInfoVO.setIsFastOrder(isFastOrder);
            baseInfoVO.setFastDeliveryAmt(fastDeliveryAmt);
            baseInfoVO.setIsFastToSelfDelivery(isFastToSelfDelivery);
            baseInfoVO.setOriginWaybillNoList(originWaybillNoList);
            return baseInfoVO;
        }

        private boolean orderCancelOverTwoDays(Date createTime, Date cancelTime) {
            if (cancelTime == null || createTime == null){
                return false;
            }
            if (cancelTime.before(createTime)){
                return false;
            }

            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -2);
            return calendar.getTime().after(createTime);
        }

        private boolean orderFinishedOverTwoDays(Date completeTime, Date createTime) {
            if (completeTime == null || createTime == null){
                return false;
            }
            if (completeTime.before(createTime)){
                return false;
            }

            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -2);
            return calendar.getTime().after(completeTime);
        }


        private ChannelOrderNewStatusEnum getHistoryOrderStatus(Integer fuseOrderStatus, Integer orderStatus){
            if(Objects.nonNull(ChannelOrderNewStatusEnum.getByCode(fuseOrderStatus))){
                return ChannelOrderNewStatusEnum.getByCode(fuseOrderStatus);
            }

            OrderStatusEnum orderStatusEnum = OrderStatusEnum.enumOf(orderStatus);
            if (orderStatusEnum == null){
                return null;
            }
            switch (orderStatusEnum){
                case SUBMIT:
                case MERCHANT_CONFIRMED:
                case COMPLETED:
                case REFUND_APPLIED:
                case CANCELED:
                    return ChannelOrderNewStatusEnum.getByCode(orderStatusEnum.getValue());
                case PICKING:
                    return ChannelOrderNewStatusEnum.SHIPPING;
                default:
                    return null;

            }
        }

        private boolean isOrderNoRefundRecord(String afterSaleApplyStatus) {
            return afterSaleApplyStatus.equals(String.valueOf(AfterSaleApplyStatusEnum.CANCEL.getValue()))
                    || afterSaleApplyStatus.equals(String.valueOf(AfterSaleApplyStatusEnum.FINISH.getValue()))
                    || afterSaleApplyStatus.equals(String.valueOf(AfterSaleApplyStatusEnum.AUDITED.getValue()))
                    || afterSaleApplyStatus.equals(String.valueOf(AfterSaleApplyStatusEnum.AUDITED_REJECT.getValue()))
                    || afterSaleApplyStatus.equals(String.valueOf(AfterSaleApplyStatusEnum.AUTO_AUDITED.getValue()));
        }
    }


    @Getter
    @Setter
    @NoArgsConstructor
    @ToString
    public static class ItemInfo {

        /**
         * sku编码
         */
        private String sku;

        /**
         * 渠道sku编码
         */
        private String customSkuId;

        /**
         * 商品名称
         */
        private String skuName;

        /**
         * upc编码
         */
        private String upc;

        /**
         * 规格
         */
        private String spec;

        /**
         * 单位
         */
        private String unit;

        /**
         * 单价
         */
        private double unitPrice;

        /**
         * 数量
         */
        private int quantity;

        /**
         * 总价
         */
        private double totalPrice;


        /**
         * 是否申请取消
         */
        private boolean isRefund;


        /**
         * 取消数量
         */
        private int refundCount;

        /**
         * 换货数量
         */
        private int exchangeCount;
        /**
         * 摊位名称
         */
        private String boothName;
        /**
         * 商品线下售价
         */
        private String offlinePrice;
        /**
         * 实际拣货数量
         */
        private int realQuantity;
        /**
         * 摊位结算金额
         */
        private String boothSettleAmount;

        /**
         * 属性
         * **/
        private List<String> propertyList;

        /**
         * orderItemId
         * @param productInfo
         */
        private Long orderItemId;

        private String erpItemCode;

        /**
         * extData,包换换货信息
         */
        private String extData;

        /**
         * 下单时的商品数量
         */
        private Long orderQuantity;

        /**
         * 原价
         */
        private double originPrice;

        /**
         * 售价
         */
        private double salePrice;

        /**
         * 记账金额
         */
        private Double billPrice;

        /**
         * 记账金额
         */
        private Double billAmt;


        /**
         * 平台整单优惠
         */
        private Double platPromotion;

        /**
         * 平台单品优惠
         */
        private Double platItemPromotion;

        /**
         * 商家整单优惠
         */
        private Double poiPromotion;

        /**
         * 商家单品优惠
         */
        private Double poiItemPromotion;


        /**
         * 采购价
         */
        private Double purchaseAmt;

        /**
         * 采购折扣
         */
        private Double purchaseDiscountAmt;

        /**
         * 整合营销推广结算(商家）
         */
        private Double poiMarketPromotion;

        /**
         * 整合营销推广结算(供应商）
         */
        private Double supplierMarketPromotion;

        /**
         * 商品行重量
         */
        private Integer weight;

        /**
         * 商品行重量
         */
        private String picUrl;
        /**
         * 组合商品
         */
        private List<SubProductVo> subProduct;


        /**
         * 闪电送费用
         */
        public String fastDeliveryAmt;

        /**
         * 商品标签信息
         */
        private List<ChannelLabelVO> channelLabelList;

        /**
         * 商品标签附加信息
         */
        private String labelSubDesc;

        /**
         *
         * @param productInfo
         */
        public ItemInfo(ProductInfoDTO productInfo) {
            sku = productInfo.getSkuId();
            customSkuId = productInfo.getCustomerSkuId();
            skuName = productInfo.getSkuName();
            upc = productInfo.getUpcCode();
            spec = productInfo.getSpecification();
            unit = productInfo.getSellUnit();
            unitPrice = productInfo.getUnitPrice() / 100.0;
            quantity = productInfo.getCount();
            totalPrice = unitPrice * quantity;
            isRefund = productInfo.isIsRefund();
            refundCount = productInfo.getRefundCount();
            boothName = productInfo.getBoothName();
            if (productInfo.getOfflinePrice() == -1) {
                offlinePrice = "";
            } else {
                offlinePrice = ConverterUtils.formatMoney(productInfo.getOfflinePrice());
            }
            realQuantity = productInfo.getRealQuantity() / 1000;
            if (productInfo.getStallSettleAmt() == -1) {
                boothSettleAmount = "";
            } else {
                boothSettleAmount = ConverterUtils.formatMoney(productInfo.getStallSettleAmt());
            }

        }

        public ItemInfo(ProductInfoVo productInfo) {
            sku = productInfo.getSkuId();
            customSkuId = productInfo.getCustomerSkuId();
            skuName = productInfo.getSkuName();
            upc = productInfo.getUpcCode();
            spec = productInfo.getSpecification();
            unit = productInfo.getSellUnit();
            unitPrice = productInfo.getUnitPrice() / 100.0;
            quantity = productInfo.getCount();

            totalPrice = unitPrice * quantity;
            isRefund = Optional.ofNullable(productInfo.getIsRefund()).orElse(Boolean.FALSE);
            refundCount = productInfo.getRefundCount();
            boothName = productInfo.getBoothName();
            if (productInfo.getOfflinePrice() == -1) {
                offlinePrice = "";
            } else {
                offlinePrice = ConverterUtils.formatMoney(productInfo.getOfflinePrice());
            }
            realQuantity = productInfo.getRealQuantity() / 1000;
            if (productInfo.getStallSettleAmt() == -1) {
                boothSettleAmount = "";
            } else {
                boothSettleAmount = ConverterUtils.formatMoney(productInfo.getStallSettleAmt());
            }
            propertyList = productInfo.getPropertyList();

            orderItemId = productInfo.getOrderItemId();

            extData = productInfo.getExtData();
            weight = BigDecimal.valueOf(productInfo.getWeight() * quantity)
                    .setScale(0, RoundingMode.HALF_UP).intValue();
            picUrl = productInfo.getPicUrl();
            erpItemCode = productInfo.getErpItemCode();
            //组合商品
            subProduct = CombinationProductUtil.getSubProductVoList(productInfo.getCombinationChildProductVoList());
            channelLabelList = ProductLabelUtil.buildChannelLabelVOList(productInfo.getChannelLabelList());
            labelSubDesc = ProductLabelUtil.buildChannelLabelSubDesc(productInfo.getChannelLabelList(), productInfo.getExtData());
        }


        public ItemInfoVO toItemInfoVO() {
            ItemInfoVO itemInfoVO = new ItemInfoVO();
            itemInfoVO.setSku(sku);
            itemInfoVO.setCustomSkuId(customSkuId);
            itemInfoVO.setSkuName(skuName);
            itemInfoVO.setUpc(upc);
            itemInfoVO.setSpec(spec);
            itemInfoVO.setUnit(unit);
            itemInfoVO.setUnitPrice(ConverterUtils.formatMoney(unitPrice));
            itemInfoVO.setQuantity(quantity);
            itemInfoVO.setTotalPrice(ConverterUtils.formatMoney(totalPrice));
            itemInfoVO.setIsRefund(isRefund ? "是" : "否");
            if (isRefund) {
                itemInfoVO.setRefundCount(String.valueOf(refundCount));
            }
            itemInfoVO.setBoothName(boothName);
            itemInfoVO.setOfflinePrice(offlinePrice);
            itemInfoVO.setRealQuantity(String.valueOf(realQuantity));
            itemInfoVO.setPropertyList(propertyList);
            itemInfoVO.setBoothSettleAmount(boothSettleAmount);
            if(StringUtils.isBlank(extData)){
                itemInfoVO.setOrderQuantity(quantity);
            }else{
                Map<String, Object> stringObjectMap = GsonUtil.toObjMap(extData);
                if(stringObjectMap.containsKey(EXE_CHANGE_ORDER_QUANTITY_KEY)){
                    Object orderQuantityObject = stringObjectMap.get(EXE_CHANGE_ORDER_QUANTITY_KEY);
                    itemInfoVO.setOrderQuantity(Integer.parseInt(String.valueOf(orderQuantityObject)));
                } else {
                    itemInfoVO.setOrderQuantity(quantity);
                }
            }
            itemInfoVO.setOrderItemId(orderItemId);
            itemInfoVO.setPlatItemDiscount(ConverterUtils.formatMoney(platItemPromotion));
            itemInfoVO.setPlatDiscount(ConverterUtils.formatMoney(platPromotion));
            itemInfoVO.setPoiItemDiscount(ConverterUtils.formatMoney(poiItemPromotion));
            itemInfoVO.setPoiDiscount(ConverterUtils.formatMoney(poiPromotion));
            itemInfoVO.setBillPrice(ConverterUtils.formatMoney(billPrice));
            itemInfoVO.setBillValue(ConverterUtils.formatMoney(billAmt));
            itemInfoVO.setPoiMarketDiscount(ConverterUtils.formatMoney(poiMarketPromotion));
            itemInfoVO.setSupplierMarketDiscount(ConverterUtils.formatMoney(supplierMarketPromotion));
            itemInfoVO.setOriginPrice(ConverterUtils.formatMoney(originPrice));
            itemInfoVO.setSalePrice(ConverterUtils.formatMoney(salePrice));
            itemInfoVO.setPurchaseAmt(ConverterUtils.formatMoney(purchaseAmt));
            itemInfoVO.setPurchaseDiscountAmt(ConverterUtils.formatMoney(purchaseDiscountAmt));
            itemInfoVO.setWeight(weight);
            itemInfoVO.setPicUrl(picUrl);
            itemInfoVO.setErpItemCode(erpItemCode);
            itemInfoVO.setBatchInfo(OrderItemExtInfoUtil.extractBatchInfo(extData));
            itemInfoVO.setGoodsCode(OrderUtils.getExtDataAsString("goodsCode", extData));
            itemInfoVO.setSubProduct(subProduct);

            // extData确认是否为赠品，设置赠品参数 0-平台赠品，1-自定义赠品
            itemInfoVO.setGiftType(OrderUtils.getExtDataAsInt("giftType", extData));
            itemInfoVO.setChannelLabelList(channelLabelList);
            itemInfoVO.setLabelSubDesc(labelSubDesc);
            return itemInfoVO;
        }


        public void mergeFinanceInfo(OrderFuseFinanceDetailBO.ItemFinanceInfo itemFinanceInfo) {
            if (itemFinanceInfo != null){
                platItemPromotion = itemFinanceInfo.getPlatItemPromotion();
                platPromotion = itemFinanceInfo.getPlatPromotion();
                poiItemPromotion = itemFinanceInfo.getPoiItemPromotion();
                poiPromotion = itemFinanceInfo.getPoiPromotion();

                supplierMarketPromotion = itemFinanceInfo.getSupplierMarketPromotion();
                poiMarketPromotion = itemFinanceInfo.getPoiMarketPromotion();
                purchaseAmt = itemFinanceInfo.getPurchaseAmt();
                purchaseDiscountAmt = itemFinanceInfo.getPurchaseDiscountAmt();

                billAmt = itemFinanceInfo.getBillAmt();
                billPrice = itemFinanceInfo.getBillPrice();
                originPrice = itemFinanceInfo.getOriginPrice();
                salePrice = itemFinanceInfo.getSalePrice();
                totalPrice = itemFinanceInfo.getItemSaleAmt();

                if(itemFinanceInfo.getComposeItemFinanceInfos() != null){
                    Map<Long, OrderFuseFinanceDetailBO.ComposeItemFinanceInfo> financeInfoMap = itemFinanceInfo.getComposeItemFinanceInfos().stream().collect(Collectors.toMap(OrderFuseFinanceDetailBO.ComposeItemFinanceInfo::getServiceId, item -> item));
                    subProduct.forEach(item -> {
                        if (MapUtils.isNotEmpty(financeInfoMap) && Objects.nonNull(financeInfoMap.get(item.getServiceId()))) {
                            OrderFuseFinanceDetailBO.ComposeItemFinanceInfo composeItemFinanceInfo = financeInfoMap.get(item.getServiceId());
                            item.setOriginPrice(ConverterUtils.formatMoney(composeItemFinanceInfo.getOriginPrice()));
                            item.setSalePrice(ConverterUtils.formatMoney(composeItemFinanceInfo.getSalePrice()));
                            item.setTotalPrice(ConverterUtils.formatMoney(composeItemFinanceInfo.getItemSaleAmt()));
                            item.setPlatItemDiscount(ConverterUtils.formatMoney(composeItemFinanceInfo.getPlatItemPromotion()));
                            item.setPlatDiscount(ConverterUtils.formatMoney(composeItemFinanceInfo.getPlatPromotion()));
                            item.setPoiDiscount(ConverterUtils.formatMoney(composeItemFinanceInfo.getPoiPromotion()));
                            item.setPoiItemDiscount(ConverterUtils.formatMoney(composeItemFinanceInfo.getPoiItemPromotion()));
                            item.setBillPrice(ConverterUtils.formatMoney(composeItemFinanceInfo.getBillPrice()));
                            item.setBillValue(ConverterUtils.formatMoney(composeItemFinanceInfo.getBillAmt()));
                            item.setServiceId(composeItemFinanceInfo.getServiceId() != null ? composeItemFinanceInfo.getServiceId() : 0);
                        }
                   });
                }
            }
        }
    }


    @Setter
    @Getter
    @ToString
    public static class PromotionInfo {

        /**
         * 优惠名称
         */
        private String promotionName;

        /**
         * 优惠金额
         */
        private Double promotionAmount;

        /**
         * 平台承担金额
         */
        private Double platformAmount;

        /**
         * 商家承担金额
         */
        private Double merchantAmount;

        /**
         * 代理商承担金额
         */
        private Double agentAmount;

        /**
         * 物流承担金额
         */
        private Double logisticsBearAmount;

        private OnlineGiftBO onlineGiftBO;


        public PromotionInfo(PromotionInfoDTO promotion) {
            promotionName = promotion.getDescription();
            promotionAmount = promotion.getDiscount() / 100.0;
            platformAmount = promotion.getPlatformBearFee() / 100.0;
            merchantAmount = promotion.getBizBearFee() / 100.0;
            agentAmount = promotion.getAgentBearFee() / 100.0;
            logisticsBearAmount = promotion.getLogisticsBearCharge() / 100.0;
        }

        public PromotionInfo(PromotionInfoVo promotion) {
            promotionName = promotion.getDescription();
            promotionAmount = promotion.getDiscount() / 100.0;
            platformAmount = promotion.getPlatformBearFee() / 100.0;
            merchantAmount = promotion.getBizBearFee() / 100.0;
            agentAmount = promotion.getAgentBearFee() / 100.0;
            logisticsBearAmount = promotion.getLogisticsBearCharge() / 100.0;

            if (null != promotion.getOnlineGiftVO()) {
                onlineGiftBO = new OnlineGiftBO(promotion.getOnlineGiftVO());
            }
        }


        public PromotionInfoVO toPromotionInfoVO() {
            PromotionInfoVO promotionInfoVO = new PromotionInfoVO();
            promotionInfoVO.setPromotionName(promotionName);
            promotionInfoVO.setPromotionAmount(ConverterUtils.formatMoney(promotionAmount));
            promotionInfoVO.setPlatformAmount(ConverterUtils.formatMoney(platformAmount));
            promotionInfoVO.setMerchantAmount(ConverterUtils.formatMoney(merchantAmount));
            promotionInfoVO.setAgentAmount(ConverterUtils.formatMoney(agentAmount));
            promotionInfoVO.setLogisticsBearAmount(ConverterUtils.formatMoney(logisticsBearAmount));
            return promotionInfoVO;
        }

    }

    @Setter
    @Getter
    @NoArgsConstructor
    public static class OnlineGiftBO {
        private static final String DEFAULT_PRICE = "0.00";

        /**
         * 赠品名称
         */
        public String giftName;

        /**
         * 赠品数量
         */
        public Integer giftQuantity;

        /**
         * 赠品skuId,赋能sku
         */
        private String giftSku;

        /**
         * 主品skuId
         */
        private String mainSkuId;

        /**
         * 赠品规格
         */
        private String giftSpec;

        /**
         * 赠品货架码
         */
        private String giftGoodsCode;

        public OnlineGiftBO(OnlineGiftVO onlineGiftVO) {
            giftName = onlineGiftVO.getGiftName();
            giftQuantity = onlineGiftVO.getGiftQuantity();
            giftSku = onlineGiftVO.getGiftSku();
            mainSkuId = onlineGiftVO.getMainSkuId();
            giftSpec = onlineGiftVO.getGiftSpec();
            giftGoodsCode = onlineGiftVO.getGiftGoodsCode();
        }

        public ItemInfoVO toItemInfoVO() {
            ItemInfoVO itemInfoVO = new ItemInfoVO();
            itemInfoVO.setSkuName(giftName);
            itemInfoVO.setSku(giftSku);
            itemInfoVO.setQuantity(giftQuantity);
            itemInfoVO.setGoodsCode(giftGoodsCode);
            itemInfoVO.setOriginPrice(DEFAULT_PRICE);
            itemInfoVO.setSalePrice(DEFAULT_PRICE);
            itemInfoVO.setTotalPrice(DEFAULT_PRICE);
            itemInfoVO.setBillPrice(DEFAULT_PRICE);
            itemInfoVO.setBillValue(DEFAULT_PRICE);
            itemInfoVO.setPlatDiscount(DEFAULT_PRICE);
            itemInfoVO.setPoiDiscount(DEFAULT_PRICE);
            itemInfoVO.setPlatItemDiscount(DEFAULT_PRICE);
            itemInfoVO.setPoiItemDiscount(DEFAULT_PRICE);
            return itemInfoVO;
        }
    }


    @Setter
    @Getter
    @NoArgsConstructor
    public static class OperateLog {

        /**
         * 操作员
         */
        private String operator;

        /**
         * 操作时间
         */
        private Date operateTime;

        /**
         * 操作类型
         */
        private String operateType;

        /**
         * 描述
         */
        private String desc;

        public OperateLog(OrderOptLogDTO log) {
            operator = log.getOperator();
            operateTime = new Date(log.getOptTime());
            operateType = log.getOptContent();
            desc = log.getOptDesc();
        }

        public OperateLog(OrderOptLogVo log) {
            operator = log.getOperator();
            operateTime = new Date(log.getOptTime());
            operateType = log.getOptContent();
            desc = log.getOptDesc();
            // 如果是取消或者订单退款状态--拼接发起方和原因
            if (StringUtils.isNotBlank(log.getOptDesc())
                    && (OrderStatusEnum.CANCELED.getDesc().equals(operateType)
                    || OrderStatusEnum.REFUND_APPLIED.getDesc().equals(operateType))) {
                OperatorTypeEnum operatorTypeEnum = OperatorTypeEnum.enumOf(log.getOperatorType());
                desc = (OperatorTypeEnum.CALL_CENTER.equals(operatorTypeEnum) ? "客服" : operatorTypeEnum.getDesc())
                        + "发起" + desc;
            }
        }

        public OperateLogVO toOperateLogVO() {
            OperateLogVO operateLogVO = new OperateLogVO();
            operateLogVO.setOperator(operator);
            operateLogVO.setOperateTime(ConverterUtils.nonNullConvert(operateTime, time -> DateFormatUtils.format(time, Constants.DateFormats.SHOW_FORMAT)));
            operateLogVO.setOperateType(operateType);
            operateLogVO.setDesc(desc);
            return operateLogVO;
        }

    }

    @Getter
    @Setter
    public static class BillInfo{

        /**
         * 订单原总金额(单位:分)
         */
        private Double originalAmt;

        /**
         * 商家收入金额
         */
        private Double bizReceiveAmt;

        /**
         * 支付总金额
         */
        private Double actualPayAmt;

        /**
         * 商品原价总金额
         */
        private Double itemOriginalAmt;

        /**
         * 商品售价总金额
         */
        private Double itemSaleAmt;

        /**
         * 总的佣金
         */
        private Double totalCommission;

        /**
         * 商品佣金
         */
        private Double commission;

        /**
         * 运费佣金
         */
        private Double logisticsCommission;

        /**
         * 美团店铺环保捐赠
         */
        private Double donationAmt;

        /**
         * 积分抵扣金额
         */
        private Double scoreDeduction;

        /**
         * 履约服务费
         */
        private Double performService;

        /**
         * 自提服务费
         */
        private Double selfPickService;

        /**
         * 总的包装费
         */
        private Double totalPackage;

        /**
         * 实付包装费
         */
        private Double actualPayPackage;

        /**
         * 平台包装费优惠
         */
        private Double platPackageDiscount;

        /**
         * 商家包装费优惠
         */
        private Double poiPackageDiscount;

        /**
         * 平台包装费收入
         */
        private Double platPackage;

        /**
         * 商家包装费收入
         */
        private Double poiPackage;

        /**
         * 整单总优惠
         */
        private Double totalDiscount;

        /**
         * 单品总优惠
         */
        private Double totalItemDiscount;

        /**
         * 平台整单优惠
         */
        private Double platDiscount;

        /**
         * 平台单品优惠
         */
        private Double platItemDiscount;

        /**
         * 商家整单优惠
         */
        private Double poiDiscount;

        /**
         * 商家单品优惠
         */
        private Double poiItemDiscount;

        /**
         * 平台运费优惠
         */
        private Double platLogisticsDiscount;

        /**
         * 商家运费优惠
         */
        private Double poiLogisticsDiscount;

        /**
         * 运费（骑手）小费（商家+用户）
         */
        private Double totalLogisticsTips;

        /**
         * 商家运费小费
         */
        private Double poiLogisticsTips;

        /**
         * 用户运费小费
         */
        private Double customerLogisticsTips;

        /**
         * 总运费
         */
        private Double originalLogisticsAmt;

        /**
         * 商家运费收入
         */
        private Double poiLogisticsIncome;

        /**
         * 基础运费
         */
        private Double baseFreight;

        /**
         * 重量运费
         */
        private Double weightFreight;

        /**
         * 距离运费
         */
        private Double distanceFreight;

        /**
         * 时段运费
         */
        private Double timeFrameFreight;

        /**
         * 商家支付远距离运费
         */
        private Double poiFarDistanceFreight;

        /**
         * 整合营销推广结算(商家)
         */
        private Double poiMarketDiscount;

        /**
         * 整合营销推广结算(供应商)
         */
        private Double supplierMarketDiscount;

        /**
         * 采购金额
         */
        private Double purchaseAmt;

        /**
         * 采购折扣
         */
        private Double purchaseDiscountAmt;

        /**
         * 包装费
         */
        private Double boxAmt;

        /**
         * 支付运费
         */
        private Double payLogisticsAmt;

        // 使用购物卡总金额
        public Double shopCardTotalFee;

        //本金金额
        public Double shopCardBaseFee;

        // 赠金金额
        public Double shopCardGiveFee;

        // @FieldDoc(description = "赠金平台分摊")
        public Double shopCardPlatformGiveFeeShare;

        //  @FieldDoc(description = "赠金商家分摊")
        public Double shopCardShopGiveFeeShare;

        /**
         * 闪电送费用
         */
        private Double fastDeliveryAmt;

        /**
         * 闪电送费用
         */
        public String addressChangeFee;

        // 红包支付总金额
        private Double redpackAmountTotal;

        // 红包支付金额平台承担
        private Double redpackAmountPlatform;

        // 红包支付金额商家承担
        private Double redpackAmountMerchant;

        /**
         * 基础服务费
         */
        private Double baseServiceFee;

        public BillInfoVO toBillInfoVO() {
            BillInfoVO billInfo = new BillInfoVO();
            billInfo.setBizReceiveAmt(ConverterUtils.formatMoney(bizReceiveAmt));
            billInfo.setItemOriginalAmt(ConverterUtils.formatMoney(itemOriginalAmt));
            billInfo.setItemSaleAmt(ConverterUtils.formatMoney(itemSaleAmt));
            billInfo.setSelfPickService(ConverterUtils.formatMoney(selfPickService));
            billInfo.setPerformService(ConverterUtils.formatMoney(performService));
            billInfo.setPlatItemDiscount(ConverterUtils.formatMoney(platItemDiscount));
            billInfo.setPlatDiscount(ConverterUtils.formatMoney(platDiscount));
            billInfo.setPoiItemDiscount(ConverterUtils.formatMoney(poiItemDiscount));
            billInfo.setPoiDiscount(ConverterUtils.formatMoney(poiDiscount));
            billInfo.setCommission(ConverterUtils.formatMoney(commission));
            billInfo.setLogisticsCommission(ConverterUtils.formatMoney(logisticsCommission));
            billInfo.setTotalCommission(ConverterUtils.formatMoney(NumberUtil.add(logisticsCommission,commission)));
            billInfo.setPlatPackage(ConverterUtils.formatMoney(platPackage));
            billInfo.setPoiPackage(ConverterUtils.formatMoney(poiPackage));
            billInfo.setPlatPackageDiscount(ConverterUtils.formatMoney(platPackageDiscount));
            billInfo.setPoiPackageDiscount(ConverterUtils.formatMoney(poiPackageDiscount));
            billInfo.setActualPayPackage(ConverterUtils.formatMoney(actualPayPackage));
            billInfo.setTotalPackage(ConverterUtils.formatMoney(totalPackage));
            billInfo.setOriginalLogisticsAmt(ConverterUtils.formatMoney(originalLogisticsAmt));
            billInfo.setPlatLogisticsDiscount(ConverterUtils.formatMoney(platLogisticsDiscount));
            billInfo.setPoiLogisticsDiscount(ConverterUtils.formatMoney(poiLogisticsDiscount));
            billInfo.setPoiLogisticsIncome(ConverterUtils.formatMoney(poiLogisticsIncome));
            billInfo.setBaseFreight(ConverterUtils.formatMoney(Optional.ofNullable(baseFreight).orElse(0.0)));
            billInfo.setWeightFreight(ConverterUtils.formatMoney(Optional.ofNullable(weightFreight).orElse(0.0)));
            billInfo.setDistanceFreight(ConverterUtils.formatMoney(Optional.ofNullable(distanceFreight).orElse(0.0)));
            billInfo.setTimeFrameFreight(ConverterUtils.formatMoney(Optional.ofNullable(timeFrameFreight).orElse(0.0)));
            billInfo.setPoiFarDistanceFreight(ConverterUtils.formatMoney(Optional.ofNullable(poiFarDistanceFreight).orElse(0.0)));
            billInfo.setCustomerLogisticsTips(ConverterUtils.formatMoney(customerLogisticsTips));
            billInfo.setPoiLogisticsTips(ConverterUtils.formatMoney(poiLogisticsTips));
            billInfo.setTotalLogisticsTips(ConverterUtils.formatMoney(NumberUtil.add(customerLogisticsTips, poiLogisticsTips)));
            billInfo.setTotalDiscount(ConverterUtils.formatMoney(NumberUtil.add(poiDiscount, platDiscount)));
            billInfo.setTotalItemDiscount(ConverterUtils.formatMoney(NumberUtil.add(poiItemDiscount, platItemDiscount)));
            billInfo.setPackageDiscount(ConverterUtils.formatMoney(NumberUtil.add(platPackageDiscount, poiPackageDiscount)));
            billInfo.setLogisticsDiscount(ConverterUtils.formatMoney(NumberUtil.add(poiLogisticsDiscount, platLogisticsDiscount)));
            billInfo.setPayLogisticsAmt(ConverterUtils.formatMoney(NumberUtil.subtract(originalLogisticsAmt, NumberUtil.add(poiLogisticsDiscount, platLogisticsDiscount))));
            billInfo.setPurchaseAmt(ConverterUtils.formatMoney(purchaseAmt));
            billInfo.setPurchaseDiscountAmt(ConverterUtils.formatMoney(purchaseDiscountAmt));
            billInfo.setSupplierMarketDiscount(ConverterUtils.formatMoney(supplierMarketDiscount));
            billInfo.setPoiMarketDiscount(ConverterUtils.formatMoney(poiMarketDiscount));
            billInfo.setOriginalAmt(ConverterUtils.formatMoney(originalAmt));
            billInfo.setActualPayAmt(ConverterUtils.formatMoney(actualPayAmt));
            billInfo.setDonationAmt(ConverterUtils.formatMoney(donationAmt));
            billInfo.setScoreDeduction(ConverterUtils.formatMoney(scoreDeduction));
            billInfo.setBoxAmt(ConverterUtils.formatMoney(boxAmt));
            billInfo.setPayLogisticsAmt(ConverterUtils.formatMoney(payLogisticsAmt));
            billInfo.setShopCardTotalFee(ConverterUtils.formatMoney(shopCardTotalFee));
            billInfo.setShopCardBaseFee(ConverterUtils.formatMoney(shopCardBaseFee));
            billInfo.setShopCardGiveFee(ConverterUtils.formatMoney(shopCardGiveFee));
            billInfo.setShopCardPlatformGiveFeeShare(ConverterUtils.formatMoney(shopCardPlatformGiveFeeShare));
            billInfo.setShopCardShopGiveFeeShare(ConverterUtils.formatMoney(shopCardShopGiveFeeShare));
            billInfo.setRedpackAmountTotal(ConverterUtils.formatMoney(redpackAmountTotal));
            billInfo.setRedpackAmountPlatform(ConverterUtils.formatMoney(redpackAmountPlatform));
            billInfo.setRedpackAmountMerchant(ConverterUtils.formatMoney(redpackAmountMerchant));
            billInfo.setFastDeliveryAmt(ConverterUtils.formatMoney(fastDeliveryAmt));
            billInfo.setBaseServiceFee(ConverterUtils.formatMoney(baseServiceFee));
            if (StringUtils.isNotBlank(addressChangeFee)){
                billInfo.setAddressChangeFee(addressChangeFee);
            }
            return billInfo;
        }
    }

    @Setter
    @Getter
    @ToString
    public static class DeliveryDetail {

        private Date updateTime;

        private String deliveryStatus;

        private Integer deliveryCount;

        public DeliveryDetail(DeliveryStatusLogDTO deliveryStateLog) {
            updateTime = new Date(deliveryStateLog.getUpdateTime());
            deliveryStatus = deliveryStateLog.getStateDesc();
        }

        public DeliveryDetail(DeliveryStatusLogVo deliveryStateLog) {
            updateTime = new Date(deliveryStateLog.getUpdateTime());
            deliveryStatus = deliveryStateLog.getStateDesc();
            if(StringUtils.isNotEmpty(deliveryStateLog.getDeliveryCount())){
                deliveryCount=Integer.parseInt(deliveryStateLog.getDeliveryCount());
            }else {
                deliveryCount = 1;
            }
        }

        public DeliveryDetailVO toDeliveryDetailVO() {
            DeliveryDetailVO deliveryDetailVO = new DeliveryDetailVO();
            deliveryDetailVO.setUpdateTime(ConverterUtils.nonNullConvert(updateTime, time -> DateFormatUtils.format(time, Constants.DateFormats.SHOW_FORMAT)));
            deliveryDetailVO.setDeliveryStatus(deliveryStatus);
            return deliveryDetailVO;
        }
    }

}

