package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@Data
@TypeDoc(
        description = "配送状态VO"
)
public class DeliveryStatusVO {
    @FieldDoc(
            description = "配送类型 平台：0 三方：1 自配送：2 其他：-1 ", requiredness = Requiredness.OPTIONAL
    )
    private Integer deliveryChannelType;
}
