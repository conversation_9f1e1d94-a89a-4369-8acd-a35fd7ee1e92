package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigDto;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2019/7/2 15:36
 * @Description:
 */
@Data
public class TenantConfigBO {

    private Long tenantId;


    private Long subjectId;


    private String configValue;


    public TenantConfigBO(ConfigDto configDto) {
        this.tenantId = configDto.getTenantId();
        this.subjectId = configDto.getSubjectId();
        this.configValue = configDto.getConfigContent();
    }

}
