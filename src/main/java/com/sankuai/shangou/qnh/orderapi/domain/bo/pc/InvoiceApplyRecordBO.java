package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.sgxsupply.wxmall.bizmanagement.client.enums.invoice.InvoiceApplyStatusEnum;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.dto.InvoiceApplyRecordDto;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

@Data
public class InvoiceApplyRecordBO {


    //申请时间
    private String applyInvoiceTime;

    //发票抬头
    private String title;

    //税号
    private String taxpayerNo;

    //开票金额
    private String InvoiceAmount;

    //发票状态
    private Integer InvoiceStatus;

    //开票完成时间
    private String completeInvoiceTime;

    //发票号码
    private String invoiceNumber;

    //发票下载链接
    private String invoiceDownloadUrl;

    public static InvoiceApplyRecordBO buildInvoiceApplyRecordBO(InvoiceApplyRecordDto invoiceApplyRecordDto) {
        InvoiceApplyRecordBO invoiceApplyRecordBO = new InvoiceApplyRecordBO();
        if (Objects.nonNull(invoiceApplyRecordDto)) {
            invoiceApplyRecordBO.setApplyInvoiceTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(invoiceApplyRecordDto.getCreateTime())));
            invoiceApplyRecordBO.setTitle(invoiceApplyRecordDto.getTitle());
            invoiceApplyRecordBO.setTaxpayerNo(invoiceApplyRecordDto.getTaxpayerNo());
            invoiceApplyRecordBO.setInvoiceAmount(invoiceApplyRecordDto.getInvoiceAmt());
            invoiceApplyRecordBO.setInvoiceStatus(InvoiceApplyStatusEnum.findByCode(invoiceApplyRecordDto.getInvoiceStatus()).getCode() );
            invoiceApplyRecordBO.setInvoiceDownloadUrl(invoiceApplyRecordDto.getInvoiceDownloadUrl());
            invoiceApplyRecordBO.setInvoiceNumber(invoiceApplyRecordDto.getInvoiceNumber());
            if(Objects.equals(invoiceApplyRecordDto.getInvoiceStatus(), InvoiceApplyStatusEnum.MAKE_SUCCEED.getCode())) {
                invoiceApplyRecordBO.setCompleteInvoiceTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(invoiceApplyRecordDto.getUpdateTime())));
            }
        }
        return invoiceApplyRecordBO;
    }


}
