package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Optionable;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 售后发起方
 */
@AllArgsConstructor
@Getter
public enum ApplyUserEnum implements Optionable {

    MERCHANT(10, "商家"),
    CUSTOMER(20, "顾客"),
    CHANNEL(30, "渠道平台"),
    OTHER(40, "其他"),
    ;


    private int code;

    private String desc;


    public static ApplyUserEnum getByCode(Integer code) {

        if (Objects.isNull(code)) {
            return null;
        }

        for (ApplyUserEnum e : ApplyUserEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }

        return null;
    }


    @Override
    public List<UiOption> toOptions() {
        return Arrays.asList(ApplyUserEnum.values()).stream()
                .map(e -> new UiOption(String.valueOf(e.getCode()), e.getDesc())).collect(Collectors.toList());
    }
}
