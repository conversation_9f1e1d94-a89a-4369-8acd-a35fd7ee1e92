package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.shangou.qnh.orderapi.domain.request.pc.CommercialAgentConfigRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2020/10/21
 * 实时结算配置业务对象
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RealTimeSettleConfigBo {

    /**
     * 合作商ID
     */
    private Long commercialAgentId;

    /**
     * 是否开启外采现结(0否1是)
     */
    private Integer useRealTimeSettle;

    /**
     * 结算价限额(不限额为-1)
     */
    private Double settleLimitRatio;

    public static RealTimeSettleConfigBo build(CommercialAgentConfigRequest request) {
        RealTimeSettleConfigBo bo = new RealTimeSettleConfigBo();
        bo.setCommercialAgentId(request.getCommercialAgentId());
        bo.setUseRealTimeSettle(request.getUseRealTimeSettle());
        bo.setSettleLimitRatio(request.getSettleLimitRatio());
        return bo;
    }
}
