package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Setter
@Getter
@ToString
public class AfsBaseInfoVO {
    @FieldDoc(description = "订单号", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "订单号", required = true)
    private String orderId;

    @FieldDoc(description = "门店 ID", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "门店 ID", required = true)
    private Long poiId;

    @FieldDoc(description = "门店名称", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "门店名称", required = true)
    private String poiName;

    @FieldDoc(description = "渠道名称", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "渠道名称", required = true)
    private String channelName;

    @FieldDoc(description = "渠道id", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId;

    @FieldDoc(description = "创建时间", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "创建时间", required = true)
    private Date createTime;

    @FieldDoc(description = "服务单号", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "服务单号", required = true)
    private String serviceId;

    @FieldDoc(description = "售后请求状态", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "售后请求状态", required = true)
    private String afsApplyType;

    @FieldDoc(description = "百川订单号", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "百川订单号", required = true)
    private Long offlineOrderId;

    @FieldDoc(description = "仓库id", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "仓库id", required = true)
    private Long warehouseId;

    @FieldDoc(description = "仓库名称", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "仓库名称", required = true)
    private String warehouseName;

    @FieldDoc(description = "接收订单时间，落库时间", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "接收订单时间，落库时间", required = true)
    private Date receiveTime;

    @FieldDoc(description = "订单流水号", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "订单流水号", required = true)
    private Long orderSerialNumber;

    @FieldDoc(description = "订单序号", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "订单序号", required = true)
    private String orderSerialNumberStr;

    @FieldDoc(description = "退款图片", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "退款图片", required = true)
    private String refundPic;

    @FieldDoc(description = "售后换货信息录入是否未录入，ture：未录入，false：已录入")
    @ApiModelProperty(value = "售后换货信息录入是否未录入，ture：未录入，false：已录入")
    private Boolean isNotImport;

    @FieldDoc(description = "转单门店ID")
    @ApiModelProperty(value = "转单门店ID")
    private Long dispatchShopId;
}