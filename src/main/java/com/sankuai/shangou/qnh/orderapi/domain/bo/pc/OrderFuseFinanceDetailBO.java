package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.shangou.saas.order.platform.enums.PayMethodEnum;
import com.meituan.shangou.saas.order.platform.enums.TradeChannelEnum;
import com.sankuai.meituan.shangou.empower.finance.dto.model.OrderFinanceComposeDetailModel;
import com.sankuai.meituan.shangou.empower.finance.dto.model.OrderFinanceDetailModel;
import com.sankuai.meituan.shangou.empower.finance.dto.model.OrderFinanceModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 订单详情
 *
 * @Author: <EMAIL>
 * @Date: 2022/1/2 15:09
 * @Description:
 */
@Setter
@Getter
@NoArgsConstructor
@ToString
public class OrderFuseFinanceDetailBO {


    public static List<Integer> ONLINE_PAYMETHODS = PayMethodEnum.ONLINE.getChannelEnumList().stream().map(TradeChannelEnum::getValue).collect(Collectors.toList());


    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 渠道订单id
     */
    private String viewOrderId;

    /**
     * 订单ID，调整单id
     */
    private Long orderId;

    /**
     * 售后ID
     */
    private String channelServiceId;

    /**
     * 售后ID
     */
    private Long serviceId;

    /**
     * 订单原总金额
     */
    private Double originalAmt;

    /**
     * 用户实际支付金额
     */
    private Double actualPayAmt;

    /**
     * 商品原总金额
     */
    private Double itemOriginalAmt;

    /**
     * 商品销售金额
     */
    private Double itemSaleAmt;


    /**
     * 佣金
     */
    private Double commission;

    /**
     * 商家收入金额
     */
    private Double bizReceiveAmt;


    /**
     * 运费佣金
     */
    private Double logisticsCommission;


    /**
     * 环保捐助
     */
    private Double donationAmt;


    /**
     * 积分抵扣，
     */
    private Double scoreDeduction;


    /**
     * 履约服务费
     */
    private Double performanceServiceFee;


    /**
     * 自提服务费
     */
    private Double selfPickServiceFee;


    /**
     * 总包装费
     */
    private Double originalPackageFee;

    /**
     * 支付包装费
     */
    private Double payPackageFee;


    /**
     * 平台包装费优惠
     */
    private Double platPackagePromotion;


    /**
     * 商家包装费优惠
     */
    private Double poiPackagePromotion;


    /**
     * 平台包装费收入
     */
    private Double platPackageIncome;


    /**
     * 商家包装费收入
     */
    private Double poiPackageIncome;


    /**
     * 平台整单优惠
     */
    private Double platPromotion;


    /**
     * 平台单品优惠
     */
    private Double platItemPromotion;

    /**
     * 商家整单优惠
     */
    private Double poiPromotion;


    /**
     * 商家单品优惠
     */
    private Double poiItemPromotion;


    /**
     * 总运费
     */
    private Double originalLogisticsFee;


    /**
     * 商家运费收入
     */
    private Double poiLogisticsIncome;


    /**
     * 商家运费优惠
     */
    private Double poiLogisticsPromotion;


    /**
     * 平台运费优惠
     */
    private Double platLogisticsPromotion;



    /**
     * 商家运费小费
     */
    private Double poiLogisticsTips;

    /**
     * 基础运费
     */
    private Double baseFreight;
    /**
     * 重量运费
     */
    private Double weightFreight;
    /**
     * 距离运费
     */
    private Double distanceFreight;
    /**
     * 时段运费
     */
    private Double timeFrameFreight;
    /**
     * 商家支付远距离运费
     */
    private Double poiFarDistanceFreight;


    /**
     * 顾客支付运费小费
     */
    private Double customerLogisticsTips;

    /**
     * 采购价
     */
    private Double purchaseAmt;

    /**
     * 采购折扣
     */
    private Double purchaseDiscountAmt;

    /**
     * 整合营销推广结算(商家）
     */
    private Double poiMarketPromotion;

    /**
     * 整合营销推广结算(供应商）
     */
    private Double supplierMarketPromotion;

    private List<ItemFinanceInfo> itemInfos;

    /**
     * 支付运费
     */
    private Double payFreightFee;

    /**
     * 餐盒费
     */
    private Double boxAmt;
    /**
     * 放心退运费
     */
    private Integer returnFreight;

    // 使用购物卡总金额
    public Double shopCardTotalFee;
    //本金金额

    public Double shopCardBaseFee;
    // 赠金金额

    public Double shopCardGiveFee;
    // @FieldDoc(description = "赠金平台分摊")
    public Double shopCardPlatformGiveFeeShare;
    //  @FieldDoc(description = "赠金商家分摊")
    public Double shopCardShopGiveFeeShare;

    /**
     * 闪电送费用
     */
    public Double fastDeliveryAmt;

    /**
     * 地址变更费
     */
    public String addressChangeFee;

    private Double redpackAmountTotal;

    private Double redpackAmountPlatform;

    private Double redpackAmountMerchant;

    private Double baseServiceFee;

    public OrderFuseFinanceDetailBO(OrderFinanceModel orderFinanceModel) {
        if (orderFinanceModel != null){
            selfPickServiceFee = divideHundred(orderFinanceModel.getSelfPickServiceFee());
            performanceServiceFee = divideHundred(orderFinanceModel.getPerformanceServiceFee());
            platItemPromotion = divideHundred(orderFinanceModel.getPlatItemPromotion());
            platPromotion = divideHundred(orderFinanceModel.getPlatPromotion());
            poiItemPromotion = divideHundred(orderFinanceModel.getPoiItemPromotion());
            poiPromotion = divideHundred(orderFinanceModel.getPoiPromotion());
            commission = divideHundred(orderFinanceModel.getCommission());
            logisticsCommission = divideHundred(orderFinanceModel.getLogisticsCommission());
            platPackageIncome = divideHundred(orderFinanceModel.getPlatPackageIncome());
            poiPackageIncome = divideHundred(orderFinanceModel.getPoiPackageIncome());
            platPackagePromotion = divideHundred(orderFinanceModel.getPlatPackagePromotion());
            poiPackagePromotion = divideHundred(orderFinanceModel.getPoiPackagePromotion());
            payPackageFee = divideHundred(orderFinanceModel.getPayPackageFee());
            originalPackageFee = divideHundred(orderFinanceModel.getOriginalPackageFee());

            originalLogisticsFee = divideHundred(orderFinanceModel.getOriginalLogisticsFee());
            platLogisticsPromotion = divideHundred(orderFinanceModel.getPlatLogisticsPromotion());
            poiLogisticsPromotion = divideHundred(orderFinanceModel.getPoiLogisticsPromotion());
            poiLogisticsIncome = divideHundred(orderFinanceModel.getPoiLogisticsIncome());
            baseFreight = divideHundred(Optional.ofNullable(orderFinanceModel.getBaseFreight()).orElse(0));
            weightFreight = divideHundred(Optional.ofNullable(orderFinanceModel.getWeightFreight()).orElse(0));
            distanceFreight = divideHundred(Optional.ofNullable(orderFinanceModel.getDistanceFreight()).orElse(0));
            timeFrameFreight = divideHundred(Optional.ofNullable(orderFinanceModel.getTimeFrameFreight()).orElse(0));
            poiFarDistanceFreight = divideHundred(Optional.ofNullable(orderFinanceModel.getPoiFarDistanceFreight()).orElse(0));
            customerLogisticsTips = divideHundred(orderFinanceModel.getCustomerLogisticsTips());
            poiLogisticsTips = divideHundred(orderFinanceModel.getPoiLogisticsTips());

            purchaseAmt = divideHundred(orderFinanceModel.getPurchaseAmt());
            purchaseDiscountAmt = divideHundred(orderFinanceModel.getPurchaseDiscountAmt());
            poiMarketPromotion = divideHundred(orderFinanceModel.getPoiMarketPromotion());
            supplierMarketPromotion = divideHundred(orderFinanceModel.getSupplierMarketPromotion());
            originalAmt = divideHundred(orderFinanceModel.getOriginalAmt());
            actualPayAmt = divideHundred(orderFinanceModel.getActualPayAmt());
            donationAmt = divideHundred(orderFinanceModel.getDonationAmt());
            scoreDeduction = divideHundred(orderFinanceModel.getScoreDeduction());

            itemInfos = orderFinanceModel.getOrderItemCreateModelList().stream().map(ItemFinanceInfo::new).collect(Collectors.toList());
            bizReceiveAmt = divideHundred(orderFinanceModel.getBizReceiveAmt());
            itemSaleAmt = divideHundred(orderFinanceModel.getItemSaleAmt());
            itemOriginalAmt = divideHundred(orderFinanceModel.getItemOriginalAmt());
            boxAmt = divideHundred(orderFinanceModel.getBoxAmt());
            payFreightFee = divideHundred(
                    Optional.ofNullable(orderFinanceModel.getOriginalLogisticsFee()).orElse(0) -
                            Optional.ofNullable(orderFinanceModel.getPoiLogisticsPromotion()).orElse(0) -
                            Optional.ofNullable(orderFinanceModel.getPlatLogisticsPromotion()).orElse(0) +
                            Optional.ofNullable(orderFinanceModel.getFastDeliveryAmt()).orElse(0));
            returnFreight= orderFinanceModel.getReturnFreight();
            shopCardTotalFee =  divideHundred(orderFinanceModel.getShopCardTotalFee());
            shopCardBaseFee =  divideHundred(orderFinanceModel.getShopCardBaseFee());
            shopCardGiveFee =  divideHundred(orderFinanceModel.getShopCardGiveFee());
            shopCardPlatformGiveFeeShare =  divideHundred(orderFinanceModel.getShopCardPlatformGiveFeeShare());
            shopCardShopGiveFeeShare =  divideHundred(orderFinanceModel.getShopCardShopGiveFeeShare());
            redpackAmountTotal = divideHundred(orderFinanceModel.getRedpackAmountTotal());
            redpackAmountPlatform = divideHundred(orderFinanceModel.getRedpackAmountPlatform());
            redpackAmountMerchant = divideHundred(orderFinanceModel.getRedpackAmountMerchant());
            serviceId = orderFinanceModel.getServiceId();
            fastDeliveryAmt = divideHundred(orderFinanceModel.getFastDeliveryAmt());
            addressChangeFee = StringUtils.isNotBlank(orderFinanceModel.getAddressChangeFee()) ? orderFinanceModel.getAddressChangeFee() : "";
            baseServiceFee = divideHundred(orderFinanceModel.getBaseServiceFee());
        }
    }
    
    public static double divideHundred(Integer value){
        if (value == null){
            return 0D;
        }
        return BigDecimal.valueOf(value).divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP).doubleValue();
    }


    @Getter
    @Setter
    @NoArgsConstructor
    public static class ItemFinanceInfo {
        /**
         * 订单序号
         */
        private Long orderItemId;

        /**
         * skuCode
         */
        private String skuCode;

        /**
         * 商品名称
         */
        private String skuName;


        /**
         * 商品数量
         */
        private Double quantity;

        /**
         * 原价
         */
        private Double originPrice;

        /**
         * 售价
         */
        private Double salePrice;

        /**
         * 记账金额
         */
        private Double billPrice;

        /**
         * 记账金额
         */
        private Double billAmt;

        /**
         * 商品原总金额
         */
        private Double itemOriginalAmt;

        /**
         * 商品总金额
         */
        private Double itemSaleAmt;

        /**
         * 平台整单优惠
         */
        private Double platPromotion;

        /**
         * 平台单品优惠
         */
        private Double platItemPromotion;

        /**
         * 商家整单优惠
         */
        private Double poiPromotion;

        /**
         * 商家单品优惠
         */
        private Double poiItemPromotion;


        /**
         * 采购价
         */
        private Double purchaseAmt;

        /**
         * 采购折扣
         */
        private Double purchaseDiscountAmt;

        /**
         * 整合营销推广结算(商家）
         */
        private Double poiMarketPromotion;

        /**
         * 整合营销推广结算(供应商）
         */
        private Double supplierMarketPromotion;

        /**
         * 履约服务费
         */
        private Double performanceServiceFee;

        /**
         * 商品重量
         */
        private String weight;

        /**
         * 组合子商品
         */
        private List<ComposeItemFinanceInfo> composeItemFinanceInfos;

        public ItemFinanceInfo(OrderFinanceDetailModel item){
            performanceServiceFee = divideHundred(item.getPerformanceServiceFee());
            platItemPromotion = divideHundred(item.getPlatItemPromotion());
            platPromotion = divideHundred(item.getPlatPromotion());
            poiItemPromotion = divideHundred(item.getPoiItemPromotion());
            poiPromotion = divideHundred(item.getPoiPromotion());
            quantity = item.getCount();
            purchaseAmt = divideHundred(item.getPurchaseAmt());
            purchaseDiscountAmt = divideHundred(item.getPurchaseDiscountAmt());
            poiMarketPromotion = divideHundred(item.getPoiMarketPromotion());
            supplierMarketPromotion = divideHundred(item.getSupplierMarketPromotion());
            itemOriginalAmt = divideHundred(item.getItemOriginalAmt());
            itemSaleAmt = divideHundred(item.getItemSaleAmt());
            billAmt = divideHundred(item.getBillAmt());
            billPrice = divideHundred(item.getBillPrice());
            originPrice = divideHundred(item.getOriginalPrice());
            orderItemId = item.getOrderItemId();
            skuCode = item.getSkuCode();
            salePrice = divideHundred(item.getSalePrice());
            weight = item.getWeight();
            if(CollectionUtils.isNotEmpty(item.getOrderItemComposeDetailModelList())){
                composeItemFinanceInfos = item.getOrderItemComposeDetailModelList().stream().map(ComposeItemFinanceInfo::new).collect(Collectors.toList());
            }
        }

    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class ComposeItemFinanceInfo {
        /**
         * 订单序号
         */
        private Long orderItemId;

        /**
         * skuCode
         */
        private String skuCode;

        /**
         * 商品名称
         */
        private String skuName;


        /**
         * 商品数量
         */
        private Double quantity;

        /**
         * 原价
         */
        private Double originPrice;

        /**
         * 售价
         */
        private Double salePrice;

        /**
         * 记账金额
         */
        private Double billPrice;

        /**
         * 记账金额
         */
        private Double billAmt;

        /**
         * 商品原总金额
         */
        private Double itemOriginalAmt;

        /**
         * 商品总金额
         */
        private Double itemSaleAmt;

        /**
         * 平台整单优惠
         */
        private Double platPromotion;

        /**
         * 平台单品优惠
         */
        private Double platItemPromotion;

        /**
         * 商家整单优惠
         */
        private Double poiPromotion;

        /**
         * 商家单品优惠
         */
        private Double poiItemPromotion;

        /**
         * 履约服务费
         */
        private Double performanceServiceFee;

        /**
         * 服务ID
         */
        private Long serviceId;

        /**
         * erpCode
         */
        private String erpItemCode;

        /**
         * 餐盒费
         */
        private Double boxAmt;

        /**
         * 佣金
         */
        private Double commission;

        /**
         * 重量
         */
        private Double weight;

        /**
         * 原总价
         */
        private Double originalTotalPrice;

        /**
         * 实付金额
         */
        private Double totalPayAmount;


        public ComposeItemFinanceInfo(OrderFinanceComposeDetailModel item){
            performanceServiceFee = divideHundred(item.getPerformanceServiceFee());
            platItemPromotion = divideHundred(item.getPlatItemPromotion());
            platPromotion = divideHundred(item.getPlatPromotion());
            poiItemPromotion = divideHundred(item.getPoiItemPromotion());
            poiPromotion = divideHundred(item.getPoiPromotion());
            quantity = item.getQuantity();
            itemOriginalAmt = divideHundred(item.getItemOriginalAmt());
            itemSaleAmt = divideHundred(item.getItemSaleAmt());
            billAmt = divideHundred(item.getBillAmt());
            billPrice = divideHundred(item.getBillPrice());
            originPrice = divideHundred(item.getOriginalPrice());
            orderItemId = item.getOrderItemId();
            skuCode = item.getSkuCode();
            salePrice = divideHundred(item.getSalePrice());
            serviceId = item.getOrderItemId();
            erpItemCode = item.getErpItemCode();
            boxAmt = divideHundred(item.getBoxAmt());
            commission = divideHundred(item.getCommission());
            weight = item.getWeight();
            skuName = item.getSkuName();
            originalTotalPrice = divideHundred(item.getItemOriginalAmt());
            totalPayAmount = divideHundred(item.getItemSaleAmt());
        }

    }
}

