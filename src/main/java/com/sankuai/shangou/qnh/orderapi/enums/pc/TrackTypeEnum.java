package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Optionable;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单日志类型
 */
@AllArgsConstructor
@Getter
public enum TrackTypeEnum implements Optionable {

    ORDER(10,"订单日志"),
    VERIFICATION(30,"核销日志"),
    DELIVERY(50,"配送日志");


    private int code;

    private String desc;


    public static TrackTypeEnum getByCode(Integer code) {

        if (Objects.isNull(code)) {
            return null;
        }

        for (TrackTypeEnum e : TrackTypeEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }

        return null;
    }


    @Override
    public List<UiOption> toOptions() {
        return Arrays.asList(TrackTypeEnum.values()).stream()
                .map(e -> new UiOption(String.valueOf(e.getCode()),e.getDesc())).collect(Collectors.toList());
    }
}
