package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Optionable;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单分派/转派管理员类型
 */
@AllArgsConstructor
@Getter
public enum TransferPickerTypeEnum implements Optionable {

    USER_TRANSFER(1, "拣货员转派"),
    ADMIN_TRANSFER(2, "管理员分派"),

    ADMIN_TRANSFER_RECEIVE(3, "管理员分配拣货员-待领取的单据"),

    ADMIN_TRANSFER_PICK(4, "管理员转派拣货员-已领取，待拣货/拣货中的单据");


    private int code;

    private String desc;


    public static TransferPickerTypeEnum getByCode(Integer code) {

        if (Objects.isNull(code)) {
            return null;
        }

        for (TransferPickerTypeEnum e : TransferPickerTypeEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }

        return null;
    }

    public static TransferPickerTypeEnum convertType(Integer type) {
        if (type == null) {
            return ADMIN_TRANSFER;
        }

        switch (type) {
            case 0:
                return ADMIN_TRANSFER;
            case 1:
                return ADMIN_TRANSFER_RECEIVE;
            case 2:
                return ADMIN_TRANSFER_PICK;
            default:
                return ADMIN_TRANSFER;
        }
    }


    @Override
    public List<UiOption> toOptions() {
        return Arrays.asList(TransferPickerTypeEnum.values()).stream()
                .map(e -> new UiOption(String.valueOf(e.getCode()),e.getDesc())).collect(Collectors.toList());
    }
}
