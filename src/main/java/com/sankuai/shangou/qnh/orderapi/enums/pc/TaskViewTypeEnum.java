package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.sankuai.meituan.shangou.empower.settlement.constant.ExportTaskTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 批量任务枚举
 *
 * <AUTHOR>
 * Date: 2022/08/22
 * Description:
 */
@AllArgsConstructor
@Getter
public enum TaskViewTypeEnum {

    BATCH_EXPORT_STOCK("库存查询导出","BATCH_EXPORT_STOCK"),
    BATCH_EXPORT_STOCK_FLOW("库存流水查询导出","BATCH_EXPORT_STOCK_FLOW"),
    BATCH_EXPORT_BATCH_STOCK("批次库存查询导出","BATCH_EXPORT_BATCH_STOCK"),
    BATCH_EXPORT_BATCH_STOCK_FLOW("批次库存流水查询导出","BATCH_EXPORT_BATCH_STOCK_FLOW"),
    BATCH_EXPORT_BATCH_STOCK_EXPIRE("批次效期管理查询导出", "BATCH_EXPORT_BATCH_STOCK_EXPIRE"),
    BATCH_EXPORT_DAMAGE_ORDER("报损单查询导出","BATCH_EXPORT_DAMAGE_ORDER"),
    BATCH_EXPORT_STOCK_ADJUST_ORDER("库存调整单查询导出","BATCH_EXPORT_STOCK_ADJUST_ORDER"),
    BATCH_EXPORT_OUT_WAREHOUSE_ORDER("调拨出库单查询导出","BATCH_EXPORT_OUT_WAREHOUSE_ORDER"),
    BATCH_EXPORT_ALLOT_DIFF_ORDER("调拨差异单查询导出","BATCH_EXPORT_ALLOT_DIFF_ORDER"),
    BATCH_EXPORT_ALLOT_DIFF_SKU("调拨差异明细导出", "BATCH_EXPORT_ALLOT_DIFF_SKU"),
    BATCH_EXPORT_COST_ACCOUNTING("库存成本明细导出","BATCH_EXPORT_COST_ACCOUNTING"),
    SKU_EXPORT("商品导出","SKU_EXPORT"),
    TENANT_BATCH_ADD_SKU("商品主档-批量新增","TENANT_BATCH_ADD_SKU"),
    TENANT_BATCH_UPDATE_SKU("商品主档-批量修改","TENANT_BATCH_UPDATE_SKU"),
    TENANT_BATCH_UPLOAD_PIC("商品主档-批量传图","TENANT_BATCH_UPLOAD_PIC"),
    TENANT_BATCH_UPDATE_PERSONAL_INFO("商品主档-批量修改线上个性化信息","TENANT_BATCH_UPDATE_PERSONAL_INFO"),
    TENANT_BATCH_IMPORT_BRAND("商品主档-批量导入品牌","TENANT_BATCH_IMPORT_BRAND"),
    BATCH_CREATE_REGION_SKU("从总库选择创建城市商品","BATCH_CREATE_REGION_SKU"),
    REGION_SKU_ADD("批量新增城市商品","REGION_SKU_ADD"),
    REGION_SKU_UPDATE("批量修改城市商品","REGION_SKU_UPDATE"),
    STORE_BATCH_SHELF_SKU("门店商品-批量上下架","STORE_BATCH_SHELF_SKU"),
    STORE_BATCH_ADD_SKU("门店商品-批量新增","STORE_BATCH_ADD_SKU"),
    STORE_BATCH_UPDATE_SKU("门店商品-批量修改","STORE_BATCH_UPDATE_SKU"),
    BATCH_CHANNEL_SKU_PRICE_STRATEGY_CHANGE("门店商品-批量定单品提价规则","BATCH_CHANNEL_SKU_PRICE_STRATEGY_CHANGE"),
    STORE_SPU_BATCH_COPY ("门店商品-批量复制","STORE_SPU_BATCH_COPY"),
    TENANT_BATCH_IMPORT_CATEGORY("批量导入商品类目","TENANT_BATCH_IMPORT_CATEGORY"),
    TENANT_BATCH_IMPORT_STORE_AREA("批量导入库位信息","TENANT_BATCH_IMPORT_STORE_AREA"),
    TENANT_BATCH_IMPORT_DELIVERY_TAKE("批量导入配货单","TENANT_BATCH_IMPORT_DELIVERY_TAKE"),
    BONUS_UPDATE("结算金额导入","BONUS_UPDATE"),
    COPY_STORE("复制门店","COPY_STORE"),
    PULL_SKU_FROM_ONLINE("从线上拉取","PULL_SKU_FROM_ONLINE"),
    ELM_PULL_SKU_FROM_ONLINE("饿了么线上拉取","ELM_PULL_SKU_FROM_ONLINE"),
    MT_PULL_SKU_FROM_ONLINE("总部管品-美团线上拉取","MT_PULL_SKU_FROM_ONLINE"),
    PRICE_WORK_TILE_ADJUST_CHANNEL_PRICE("定价任务","PRICE_WORK_TILE_ADJUST_CHANNEL_PRICE"),
    STORE_ACTIVITY("促销-创建促销活动","STORE_ACTIVITY"),
    MERCHANT_STORE_CATEGORY_ADJUST_TASK("商品主档店内分类调整任务","MERCHANT_STORE_CATEGORY_ADJUST_TASK"),
    STORE_BATCH_UPDATE_FRONT_CATEGORY("门店商品-批量修改前台分类","STORE_BATCH_UPDATE_FRONT_CATEGORY"),
    TENANT_BATCH_IMPORT_BOOTH_RELATION("批量导入摊位关联","TENANT_BATCH_IMPORT_BOOTH_RELATION"),
    PRICE_STRATEGY_SYNC_PRICE("提价策略-同步价格","PRICE_STRATEGY_SYNC_PRICE"),
    STORE_BATCH_UPDATE_ONLINE_PRICE("门店商品-批量定零售价","STORE_BATCH_UPDATE_ONLINE_PRICE"),
    ADJUST_SETTLE_AMT("调整对账金额","ADJUST_SETTLE_AMT"),
    COPY_REGION_SKU("复制城市商品","COPY_REGION_SKU"),
    SKU_STOCK_BATCH_UPDATE_QUERY("库存-批量修改库存","SKU_STOCK_BATCH_UPDATE_QUERY"),
    POI_BATCH_ADD("批量导入门店", "POI_BATCH_ADD"),
    POI_BATCH_UPDATE("批量更新门店", "POI_BATCH_UPDATE"),
    ACCOUNT_BATCH_ADD("批量导入账号", "ACCOUNT_BATCH_ADD"),
    POI_BATCH_RELATE_CHANNEL("门店批量关联渠道", "POI_BATCH_RELATE_CHANNEL"),
    CHANNEL_POI_PULL("拉取渠道门店","CHANNEL_POI_PULL"),
    APPRAISE_PLAN_UPDATE("商户考核批量订正","APPRAISE_PLAN_UPDATE"),
    CORE_SKU_STOCK_EXPORT("核心品库存导出","CORE_SKU_STOCK_EXPORT"),
    QUOTE_REVIEW_EXPORT("价格审核记录导出","QUOTE_REVIEW_EXPORT"),
    TAB_DOWNLOAD("数据分析导出","TAB_DOWNLOAD"),
    APPRAISE_RESULT_EXPORT("考核结果导出","APPRAISE_RESULT_EXPORT"),
    STORE_CATEGORY_BATCH_MANAGEMENT("店内分类批量设置","STORE_CATEGORY_BATCH_MANAGEMENT"),
    BATCH_UPDATE_STORE_SKU_WARNING_THRESHOLD("库存-批量更新安全库存","BATCH_UPDATE_STORE_SKU_WARNING_THRESHOLD"),
    BATCH_EXPORT_POI_ONLINE_STOCK_TASK("门店库存-导出门店线上库存","BATCH_EXPORT_POI_ONLINE_STOCK_TASK"),
    BATCH_IMPORT_STOCK_ADJUSTMENT("批量导入库存调整","BATCH_IMPORT_STOCK_ADJUSTMENT"),
    BATCH_UPDATE_STORE_CATEGORY_ORDER_GOODS_DAYS("订货-导入类目订货天数","BATCH_UPDATE_STORE_CATEGORY_ORDER_GOODS_DAYS"),
    BATCH_EXPORT_ORDER_REFER("订货参考导出","BATCH_EXPORT_ORDER_REFER"),
    BATCH_IMPORT_PURCHASE_PLAN_TASK("导入新建采购计划单","BATCH_IMPORT_PURCHASE_PLAN_TASK"),
    BATCH_UPDATE_PURCHASE_RECEIPT("批量修改采购单","BATCH_UPDATE_PURCHASE_RECEIPT"),
    BATCH_IMPORT_ALLOCATION_ORDER_TASK("导入调拨单","BATCH_IMPORT_ALLOCATION_ORDER_TASK"),
    BATCH_IMPORT_DISTRIBUTION_ORDER_TASK("导入配销单","BATCH_IMPORT_DISTRIBUTION_ORDER_TASK"),
    EXPORT_ALLOCATION_ORDER_SKU_DETAILS("调拨计划明细导出","EXPORT_ALLOCATION_ORDER_SKU_DETAILS"),
    BATCH_EXPORT_SKU_RELATION("批量导出供货关系","BATCH_EXPORT_SKU_RELATION"),
    BATCH_EXPORT_SKU_RELATION_V2("导出供货关系","BATCH_EXPORT_SKU_RELATION_V2"),
    BATCH_IMPORT_SKU_RELATION("批量导入供货关系","BATCH_IMPORT_SKU_RELATION"),
    BATCH_CLONE_SUPPLY_RELATION("复制供货关系","BATCH_CLONE_SUPPLY_RELATION"),
    EXPORT_PRICE_NOT_EQUAL("价格不一致数据导出","EXPORT_PRICE_NOT_EQUAL"),
    BATCH_IMPORT_SCM_DELIVERY_TASK("按单收货批量导入","BATCH_IMPORT_SCM_DELIVERY_TASK"),
    PRODUCT_BIZ_BATCH_TASK_QUERY("商品批量任务","PRODUCT_BIZ_BATCH_TASK_QUERY"),
    BATCH_UPDATE_STORE_UNLIMITED_STOCK("库存-线上无限库存批量设置","BATCH_UPDATE_STORE_UNLIMITED_STOCK"),
    EXPORT_WECHAT_PROMOTION_SUMMARY("微信渠道推广-整体","EXPORT_WECHAT_PROMOTION_SUMMARY"),
    EXPORT_WECHAT_PROMOTION_DETAIL("微信渠道推广-明细","EXPORT_WECHAT_PROMOTION_DETAIL"),
    BATCH_IMPORT_STORAGE_BIN("库存-批量导入新建库位","BATCH_IMPORT_STORAGE_BIN"),
    SPU_COVER_STORE_TASK("商品主档覆盖门店任务","SPU_COVER_STORE_TASK"),
    BATCH_IMPORT_REPLENISH_SETTING("批量导入补货设置","BATCH_IMPORT_REPLENISH_SETTING"),
    BATCH_EXPORT_REPLENISH_SETTING("批量导出补货设置","BATCH_EXPORT_REPLENISH_SETTING"),
    BATCH_EXPORT_REPLENISH_REFERENCE("批量导出补货参考","BATCH_EXPORT_REPLENISH_REFERENCE"),
    BATCH_EXPORT_SCM_DELIVERY_ORDERS("收货单导出","BATCH_EXPORT_SCM_DELIVERY_ORDERS"),
    UWMS_STANDARD_PRODUCT_EXPORT("药品三维数据库查询导出","UWMS_STANDARD_PRODUCT_EXPORT"),
    UWMS_STANDARD_PRODUCT_EXPORT_BY_FILE("药品三维数据库批量查询","UWMS_STANDARD_PRODUCT_EXPORT_BY_FILE"),
    BATCH_UPDATE_PURCHASE_SKU_TYPE("批量修改采购模式","BATCH_UPDATE_PURCHASE_SKU_TYPE"),
    BATCH_UPDATE_PURCHASE_SKU_STATUS("批量修改采购状态","BATCH_UPDATE_PURCHASE_SKU_STATUS"),
    BATCH_UPDATE_SUPPLIER("批量更新供应商到货天数","BATCH_UPDATE_SUPPLIER"),
    BATCH_EXPORT_SUPPLIER("批量导出供应商信息","BATCH_EXPORT_SUPPLIER"),
    BATCH_EXPORT_ORDER_MERCHANDISE_SKU_FLAT("要货商品明细导出","BATCH_EXPORT_ORDER_MERCHANDISE_SKU_FLAT"),
    BATCH_EXPORT_PURCHASE_PLAN_SKU_FLAT("采购商品明细导出","BATCH_EXPORT_PURCHASE_PLAN_SKU_FLAT"),
    BATCH_EXPORT_ALLOCATION_ORDER_SKU_FLAT("调拨商品明细导出","BATCH_EXPORT_ALLOCATION_ORDER_SKU_FLAT"),
    BATCH_EXPORT_DISTIRBUTE_ORDER_SKU_FLAT("配销商品明细导出","BATCH_EXPORT_DISTIRBUTE_ORDER_SKU_FLAT"),
    BATCH_EXPORT_OUTBOUND_SKUS("出库明细导出","BATCH_EXPORT_OUTBOUND_SKUS"),
    BATCH_EXPORT_OUTBOUND_ORDERS("出库单导出","BATCH_EXPORT_OUTBOUND_ORDERS"),
    BATCH_EXPORT_CT_OUTBOUND_SKUS("采退出库明细导出","BATCH_EXPORT_CT_OUTBOUND_SKUS"),
    BATCH_EXPORT_CT_OUTBOUND_ORDERS("采退出库单导出","BATCH_EXPORT_CT_OUTBOUND_ORDERS"),
    BATCH_IMPORT_LOCATION_SORT("库位顺序初始化导入","BATCH_IMPORT_LOCATION_SORT"),
    ERP_STORE_LOCATION_SKU_RELATION_IMPORT("ERP库位区域任务","ERP_STORE_LOCATION_SKU_RELATION_IMPORT"),
    FULFILL_DATA_DOWNLOAD("歪马履约数据导出","FULFILL_DATA_DOWNLOAD"),
    BATCH_IMPORT_REPLENISH_SETTING_FOR_REGIONAL_WAREHOUSE("批量导入补货设置(中心仓)","BATCH_IMPORT_REPLENISH_SETTING_FOR_REGIONAL_WAREHOUSE"),
    BATCH_UPDATE_PURCHASE_SKU_STATUS_FOR_REGIONAL_WAREHOUSE("批量修改采购状态(中心仓)","BATCH_UPDATE_PURCHASE_SKU_STATUS_FOR_REGIONAL_WAREHOUSE"),
    BATCH_EXPORT_REPLENISH_REFERENCE_FOR_REGIONAL_WAREHOUSE("批量导出补货参考（中心仓）","BATCH_EXPORT_REPLENISH_REFERENCE_FOR_REGIONAL_WAREHOUSE"),
    BATCH_UPDATE_PURCHASE_SKU_DISTRIBUTE_TYPE("批量修改送货方式", "BATCH_UPDATE_PURCHASE_SKU_DISTRIBUTE_TYPE"),
    REPLENISH_LIST_CONFIRM_CREATE_REPLENISH_ORDER("补货单创建任务", "REPLENISH_LIST_CONFIRM_CREATE_REPLENISH_ORDER"),
    BATCH_UPSERT_REPLENISH_LIST_SKU_TYPE("批量新增或更新补货清单", "BATCH_UPSERT_REPLENISH_LIST_SKU_TYPE"),
    BATCH_EXPORT_REPLENISH_LIST("导出补货清单", "BATCH_EXPORT_REPLENISH_LIST"),
    TENANT_MULTI_CLEAN_DATA_EXPORT("商品数据生成", "TENANT_MULTI_CLEAN_DATA_EXPORT"),

    TENANT_MULTI_CHECK_DATA("商品清洗数据检验", "TENANT_MULTI_CHECK_DATA"),
    STORE_GROUP_CATEGORY_MIGRATE_TASK("门店商品-同步分组分类到渠道", "STORE_GROUP_CATEGORY_MIGRATE_TASK"),
    BATCH_IMPORT_CREATE_SUPPLIER("批量创建供应商","BATCH_IMPORT_CREATE_SUPPLIER"),
    ERP_BATCH_EXPORT_STOCK_FLOW("ERP库存流水查询导出", "ERP_BATCH_EXPORT_STOCK_FLOW"),
    BATCH_EXPORT_ERP_STOCK("ERP库存查询导出", "BATCH_EXPORT_ERP_STOCK"),

    FUSE_ORDER_LIST_EXPORT("订单列表导出","FUSE_ORDER_LIST_EXPORT"),
    FUSE_REFUDN_LIST_EXPORT("退单列表导出","FUSE_REFUDN_LIST_EXPORT"),
    FUSE_ORDER_ITEM_EXPORT("订单商品明细导出","FUSE_ORDER_ITEM_EXPORT"),
    FUSE_REFUND_ITEM_EXPORT("退单商品明细导出","FUSE_REFUND_ITEM_EXPORT"),
    FUSE_ORDER_LIST_AND_DETAIL_EXPORT("订单列表+明细导出","FUSE_ORDER_LIST_AND_DETAIL_EXPORT"),
    AGGREGATION_FINANCE_EXPORT_TASK("核销对账-汇总-导出", "AGGREGATION_FINANCE_EXPORT_TASK"),
    ORDER_FINANCE_EXPORT_TASK("核销对账-订单-导出", "ORDER_FINANCE_EXPORT_TASK"),
    ORDER_DETAIL_FINANCE_EXPORT_TASK("核销对账-订单明细-导出", "ORDER_DETAIL_FINANCE_EXPORT_TASK"),
    ACTIVITY_SHARE_FINANCE_EXPORT_TASK("活动对账-导出", "ACTIVITY_SHARE_FINANCE_EXPORT_TASK"),
    OTHER("其他","OTHER"),


    QNH_DAILY_COMMISSION_BILL_EXPORT(ExportTaskTypeEnum.QNH_DAILY_COMMISSION_BILL_EXPORT.getDesc(), ExportTaskTypeEnum.QNH_DAILY_COMMISSION_BILL_EXPORT.getType()),
    QNH_DAILY_COMMISSION_BILL_DETAIL_EXPORT(ExportTaskTypeEnum.QNH_DAILY_COMMISSION_BILL_DETAIL_EXPORT.getDesc(), ExportTaskTypeEnum.QNH_DAILY_COMMISSION_BILL_DETAIL_EXPORT.getType()),


    BATCH_EXPORT_ERP_SPU("ERP商品主档-导出", "ERP_GOODS_EXPORT_TASK"),
    ERP_SPU_ERP_ADD_TASK("ERP商品主档-批量导入筛选", "SPU_ERP_ADD"),
    ERP_SPU_ERP_ADD_BY_UPC_TASK("商品主档-Excel新增条码导入", "SPU_ERP_ADD_BY_UPC"),
    ERP_SPU_ERP_UPDATE_TASK("商品主档-Excel修改商品主档", "SPU_ERP_UPDATE"),
    ERP_SALE_STATUS_UPDATE_TASK("商品主档-Excel修改商品售卖状态", "SALE_STATUS_UPDATE"),
    ERP_SPU_PICTURE_TASK("商品主档-Excel导入商品图", "SPU_PICTURE"),
    ERP_SPU_CONTENT_PICTURE_TASK("商品主档-Excel导入详情图", "SPU_CONTENT_PICTURE"),
    ERP_SPU_DELETE("商品主档-Excel删除商品", "SPU_DELETE"),
    ERP_SPU_ADD_BY_STANDARD("商品主档-Excel匹配商品资料库导入", "SPU_ADD_BY_STANDARD"),
    EXPORT_ERP_ITEM_WHITE_TASK("商品创建记录", "EXPORT_ERP_ITEM_WHITE_TASK"),
    ERP_ZIP_PIC_UPDATE("商品主档-zip打包导入商品图", "PICTURE_ZIP_ADD"),
    STORE_SPU_FROM_EXPORT("门店商品-导出门店商品", "STORE_SPU_FROM_EXPORT"),
    MERCHANT_STORE_CATEGORY_IMPORT_TASK("商品主档店内分类新增导入", "MERCHANT_STORE_CATEGORY_IMPORT_TASK"),
    MERCHANT_STORE_CATEGORY_EXPORT_TASK("商品主档店内分类导出", "MERCHANT_STORE_CATEGORY_EXPORT_TASK"),
    MERCHANT_STORE_CATEGORY_IMPORT_TASK4ERP("线上分类设置-Excel导入分类", "MERCHANT_STORE_CATEGORY_IMPORT_TASK4ERP"),
    MERCHANT_STORE_CATEGORY_EXPORT_TASK4ERP("线上分类设置-导出", "MERCHANT_STORE_CATEGORY_EXPORT_TASK4ERP"),
    ERP_TENANT_SPU_EXPORT("商品主档-导出商品信息","ERP_TENANT_SPU_EXPORT"),
    UNIT_CONVERT_FACTOR("商品主档-Excel导入单位转换系数","UNIT_CONVERT_FACTOR"),
    ERP_STORE_SPU_PRICE_AND_STOCK_IMPORT("门店商品-Excel设置价格库存", "ERP_STORE_SPU_PRICE_AND_STOCK_IMPORT"),
    ERP_STORE_SPU_PLU_IMPORT("门店商品-Excel导入门店PLU码", "ERP_STORE_SPU_PLU_IMPORT"),
    ERP_STORE_SPU_SELL_OUT_ON_SHELF_IMPORT("门店商品-Excel设置商品售罄下架状态", "ERP_STORE_SPU_SELL_OUT_ON_SHELF_IMPORT"),
    ERP_STORE_SPU_MANUAL_OFF_SHELF_EXPORT("商品主档-手工下架记录导出", "ERP_STORE_SPU_MANUAL_OFF_SHELF_EXPORT"),
    ERP_STORE_SPU_CHANNEL_PRICE_ADJUST_RATE("门店商品-Excel设置商品渠道加价率", "ERP_STORE_SPU_CHANNEL_PRICE_ADJUST_RATE"),
    ERP_STORE_SPU_BUSINESS_CHANNEL("门店商品-Excel设置经营渠道", "ERP_STORE_SPU_BUSINESS_CHANNEL"),
    ERP_STORE_SPU_STATUS("门店商品-Excel设置商品状态", "ERP_STORE_SPU_STATUS"),
    BATCH_IMPORT_AUTO_SHELF("上下架计划导入", "BATCH_IMPORT_AUTO_SHELF"),
    BATCH_EXPORT_AUTO_SHELF("上下架计划导出", "BATCH_EXPORT_AUTO_SHELF"),
    IMPORT_POI_BAG("购物袋导入","IMPORT_POI_BAG"),
    EXPORT_POI_BAG("购物袋导出","EXPORT_POI_BAG"),
    SAFE_STOCK_IMPORT("商品安全库存导入", "SAFE_STOCK_IMPORT"),
    MERCHANT_SPU_BATCH_SYNC_CHANNEL( "总部商品-批量同步到渠道", "MERCHANT_SPU_BATCH_SYNC_CHANNEL"),
    MERCHANT_SPU_SINGLE_SYNC_CHANNEL( "总部商品-单个同步到渠道", "MERCHANT_SPU_SINGLE_SYNC_CHANNEL"),
    STORE_SPU_BATCH_SYNC_CHANNEL("门店商品-批量同步到渠道", "STORE_SPU_BATCH_SYNC_CHANNEL"),
    STORE_SPU_SINGLE_SYNC_CHANNEL("门店商品-单个同步到渠道", "STORE_SPU_SINGLE_SYNC_CHANNEL"),
    MERCHANT_SPU_BATCH_ADD_WATER_MARK("主档商品-批量添加图片水印", "MERCHANT_SPU_BATCH_ADD_WATER_MARK"),
    BATCH_IMPORT_CHANNEL_BLACK("渠道黑名单-Excel添加商品", "BATCH_IMPORT_CHANNEL_BLACK"),
    BATCH_EXPORT_CHANNEL_BLACK("渠道黑名单-导出", "BATCH_EXPORT_CHANNEL_BLACK"),
    MULTI_CODE_BY_BARCODE_QUERY_EXPORT("一品多码-一条码多编码商品导出", "MULTI_CODE_BY_BARCODE_QUERY_EXPORT"),
    MULTI_BARCODE_BY_CODE_QUERY_EXPORT("一品多码-一编码多条码商品导出", "MULTI_BARCODE_BY_CODE_QUERY_EXPORT"),
    BATCH_IMPORT_ADD_DISCOUNT_ITEM("单品直降商品-导入", "BATCH_IMPORT_ADD_DISCOUNT_ITEM"),
    BATCH_IMPORT_ADD_CASH_BACK_ITEM("满减活动商品-导入","BATCH_IMPORT_ADD_CASH_BACK_ITEM"),
    BATCH_IMPORT_ADD_FLASH_SALE_ITEM("秒杀活动商品-导入","BATCH_IMPORT_ADD_FLASH_SALE_ITEM"),
    BATCH_IMPORT_CANCEL_CHANNEL_ITEM("取消活动商品-导入", "BATCH_IMPORT_CANCEL_CHANNEL_ITEM"),
    BATCH_EXPORT_CHANNEL_ACTIVITY_ITEM("活动商品列表-导出", "BATCH_EXPORT_CHANNEL_ACTIVITY_ITEM"),

    DELIVERY_CONFIG_BATCH_IMPORT("批量导入门店配送配置", "DELIVERY_CONFIG_BATCH_IMPORT"),

    FRANCHISE_EXPORT_ORDER_WITH_ITEM("B端销售单（包含明细）-导出", "FRANCHISE_EXPORT_ORDER_WITH_ITEM"),

    ERP_STORE_STOCK_CONFIG_IMPORT_TASK("门店安全库存导入", "ERP_STORE_STOCK_CONFIG_IMPORT_TASK"),
    ERP_STORE_STOCK_CONFIG_EXPORT_TASK("门店安全库存导出", "ERP_STORE_STOCK_CONFIG_EXPORT_TASK"),

    LONG_NAME_MERCHANT_SPU("商品主档-名称过长导出","LONG_NAME_MERCHANT_SPU"),
    REPEAT_NAME_MERCHANT_SPU("商品主档-名称重复导出","REPEAT_NAME_MERCHANT_SPU"),
    EXPORT_HU_LIST("容器导出","EXPORT_HU_LIST"),
    SUPPLY_RELATION_ASYNC_CLONE("同步更新供货关系", "SUPPLY_RELATION_ASYNC_CLONE"),
    ;

    private String desc;
    private String type;
    /**
     * @param type type
     * @return enum
     */
    public static TaskViewTypeEnum getByType(String type) {
        for (TaskViewTypeEnum e : TaskViewTypeEnum.values()) {
            if (StringUtils.equalsIgnoreCase(e.getType(), type)) {
                return e;
            }
        }
        return null;
    }
}
