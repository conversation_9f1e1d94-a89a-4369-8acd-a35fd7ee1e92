package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Optionable;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * @Author: <EMAIL>
 * @Date: 2022/12/03 19:54
 * @Description:订单类型枚举,是否是预订单
 */
@Getter
@AllArgsConstructor
public enum OrderTypeEnum implements Optionable {
    // 预订单
    RESERVE(2, "是"),
    // 实时单
    REAL_TIME(1, "否");
    ;

    /**
     * 编码
     */
    private int code;

    /**
     * 描述
     */
    private String desc;


    @Override
    public List<UiOption> toOptions() {
        return Arrays.asList(OrderTypeEnum.values()).stream()
                .map(e -> new UiOption(String.valueOf(e.getCode()),e.getDesc())).collect(Collectors.toList());
    }


    public static OrderTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }

        for (OrderTypeEnum e : OrderTypeEnum.values()) {
            if (e.getCode() == code && code != 0) {
                return e;
            }
        }

        return null;
    }

}
