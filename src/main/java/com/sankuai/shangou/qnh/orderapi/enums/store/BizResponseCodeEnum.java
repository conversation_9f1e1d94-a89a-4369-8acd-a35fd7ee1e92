package com.sankuai.shangou.qnh.orderapi.enums.store;

/***
 * author : <EMAIL> 
 * data : 2021/5/10 
 * time : 下午3:02
 **/
public enum BizResponseCodeEnum {

    SUCCESS(0, "成功"),

    PRINT_EXCEED_MAX_SKU(1001001, "超出最大打印商品数"),

    ORDER_CANT_BE_CONTACT(1001002, "已超过联系期限，如有需要请联系总部"),

    ;

    public final int code;

    public final String message;

    BizResponseCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
