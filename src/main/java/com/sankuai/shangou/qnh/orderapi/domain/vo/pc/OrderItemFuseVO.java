package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.platform.common.model.extdata.BatchInfoModel;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ChannelLabelVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 订单商品信息
 *
 * <AUTHOR>
 * @since 2022/12/06
 */
@TypeDoc(
        description = "订单商品信息"
)
@ApiModel("订单商品信息")
@Data
public class OrderItemFuseVO {

    /**
     * 渠道ID
     */
    private Integer channelId;


    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 订单流水
     */
    private Long serialNo;

    /**
     * 订单序号
     */
    private String serialNoStr;


    /**
     * 渠道订单号
     */
    private String channelOrderId;


    /**
     * 订单ID
     */
    private Long orderId;


    /**
     * 门店ID
     */
    private Long poiId;

    /**
     * 门店名称
     */
    private String poiName;

    /**
     * erp门店编码
     */
    private String outShopId;

    /**
     * 仓库id
     */
    private Long warehouseId;
    
    /**
     * 仓库名称
     */
    private String warehouseName;
    
    
    
    /**
     * 融合订单状态
     */
    private Integer fuseOrderStatus;

    /**
     * 订单状态描述
     */
    private String fuseOrderStatusDesc;


    /**
     * 商品分类
     */
    private String categoryList;


    /**
     * 商品分类条码
     */
    private String upcCode;


    /**
     * 商品名称
     */
    private String skuName;


    /**
     * 商品sku
     */
    private String skuId;


    /**
     * erp条码
     */
    private String erpCode;

    /**
     * 单个商品应付金额
     */
    private String currentPrice;

    /**
     * 规格
     */
    private String specification;


    /**
     * 商品类型
     */
    private Integer itemType;

    /**
     * 订单商品ID
     */
    private String orderItemId;

    /**
     * 商品原单价
     */
    private String originalPrice;

    /**
     * 商品图片URL
     */
    private String picUrl;

    /**
     * 数量或者份数
     */
    private Integer quantity;

    /**
     * 用户当前商品支付总金额
     */
    private String totalPayAmount;

    /**
     * 订单商品创建时间
     */
    private Long createTime;

    /**
     * 订单商品更新时间
     */
    private Long updateTime;

    /**
     * 批次信息
     */
    private List<BatchInfoModel> batchInfo;

    /**
     * 店内分类list
     */
    private List<InStoreCategoryVO> inStoreCategoryList;

    /**
     * 转单门店ID
     */
    private Long dispatchShopId;
    /**
     * 转单门店名称
     */
    private String dispatchShopName;
    /**
     * 转单门店后的订单序列编码
     */
    private String dispatchSerialNo;
    /**
     * 转单门店所在租户
     */
    private Long dispatchTenantId;
    /**
     * 转单时间
     */
    private Long dispatchTime;

    /**
     * 组合商品
     */
    private List<SubProductVo> subProduct;

    /**
     * 赠品类型，0-平台赠品，1-自定义拣货赠品
     */
    private Integer giftType;

    /**
     * 换货商品信息
     */
    private List<ExchangeOrderItemFuseVO> exchangeItemInfoVOList;

    /**
     * 换货数量（缺货商品缺货数量）
     */
    private Integer exchangeCount = 0;


    /**
     * 换货商品数量
     */
    private Integer exchangeGoodsCount = 0;

    /**
     * 商品渠道标签列表
     */
    private List<ChannelLabelVO> channelLabelList;


    /**
     * 订单标签列表
     */
    private List<OrderTagVO> orderTagList;

    /**
     * 标签副描述
     */
    private String labelSubDesc;



    public OrderItemFuseVO toItemInfoVO() {
        OrderItemFuseVO ocmsOrderVO = new OrderItemFuseVO();
        ocmsOrderVO.setOrderId(orderId);
        ocmsOrderVO.setOrderItemId(orderItemId);
        ocmsOrderVO.setCreateTime(createTime);
        ocmsOrderVO.setFuseOrderStatus(fuseOrderStatus);
        ocmsOrderVO.setFuseOrderStatusDesc(fuseOrderStatusDesc);
        ocmsOrderVO.setItemType(itemType);
        ocmsOrderVO.setChannelId(channelId);
        ocmsOrderVO.setChannelName(channelName);
        ocmsOrderVO.setChannelOrderId(channelOrderId);
        ocmsOrderVO.setCurrentPrice(currentPrice);
        ocmsOrderVO.setErpCode(erpCode);
        ocmsOrderVO.setOriginalPrice(originalPrice);
        ocmsOrderVO.setPicUrl(picUrl);
        ocmsOrderVO.setPoiId(poiId);
        ocmsOrderVO.setPoiName(poiName);
        ocmsOrderVO.setOutShopId(outShopId);
        ocmsOrderVO.setQuantity(quantity);
        ocmsOrderVO.setSerialNo(serialNo);
        ocmsOrderVO.setSerialNoStr(serialNoStr);
        ocmsOrderVO.setSpecification(specification);
        ocmsOrderVO.setSkuId(skuId);
        ocmsOrderVO.setSkuName(skuName);
        ocmsOrderVO.setTenantId(tenantId);
        ocmsOrderVO.setTotalPayAmount(totalPayAmount);
        ocmsOrderVO.setUpcCode(upcCode);
        ocmsOrderVO.setUpdateTime(updateTime);
        ocmsOrderVO.setWarehouseId(warehouseId);
        ocmsOrderVO.setBatchInfo(batchInfo);
        ocmsOrderVO.setInStoreCategoryList(inStoreCategoryList);
        ocmsOrderVO.setDispatchShopId(dispatchShopId);
        ocmsOrderVO.setDispatchSerialNo(dispatchSerialNo);
        ocmsOrderVO.setDispatchShopName(dispatchShopName);
        ocmsOrderVO.setDispatchTenantId(dispatchTenantId);
        ocmsOrderVO.setDispatchTime(dispatchTime);
        //处理组合商品
        ocmsOrderVO.setSubProduct(subProduct);

        // 新增赠品类型
        ocmsOrderVO.setGiftType(giftType);

        return ocmsOrderVO;
    }
}
