package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.shangou.saas.order.platform.common.model.extdata.BatchInfoModel;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ChannelLabelVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
public  class ItemInfoVO {
    /**
     * sku编码
     */
    private String sku;

    /**
     * 线上渠道sku编码
     */
    private String customSkuId;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品线下编码
     */
    private String erpItemCode;

    /**
     * 线上一级分类
     */
    private String category;

    /**
     * 线上二级分类
     */
    private String subCategory;

    /**
     * upc编码
     */
    private String upc;

    /**
     * 规格
     */
    private String spec;

    /**
     * 单位
     */
    private String unit;

    /**
     * 单价
     */
    private String unitPrice;

    /**
     * 数量
     */
    private int  quantity;

    /**
     * 总价
     */
    private String totalPrice;


    /**
     * 是否取消
     */
    private String isRefund;

    /**
     * 取消数量
     */
    private String refundCount;

    /**
     * 换货数量
     */
    private Integer exchangeCount = 0;
    /**
     * 摊位名称
     */
    private String boothName;
    /**
     * 商品线下售价
     */
    private String offlinePrice;
    /**
     * 实际拣货数量
     */
    private String realQuantity;
    /**
     * 摊位结算金额
     */
    private String boothSettleAmount;

    /**
     * 属性
     * **/
    private List<String> propertyList;
    /**
     * 换货商品信息
     */
    private List<ExchangeItemInfoVO> exchangeItemInfoVOList;
    /**
     * 单据原始数量
     */
    private Long orderItemId;
    /**
     * 用户下单时候的quantity
     */
    private Integer orderQuantity;

    /**
     * 原价
     */
    private String originPrice;

    /**
     * 售价
     */
    private String salePrice;

    /**
     * 记账金额
     */
    private String billPrice;

    /**
     * 记账金额
     */
    private String billValue;


    /**
     * 佣金
     */
    private String commission;

    /**
     * 平台整单优惠
     */
    private String platDiscount;

    /**
     * 平台单品优惠
     */
    private String platItemDiscount;

    /**
     * 商家整单优惠
     */
    private String poiDiscount;

    /**
     * 商家单品优惠
     */
    private String poiItemDiscount;


    /**
     * 采购价
     */
    private String purchaseAmt;

    /**
     * 采购折扣
     */
    private String purchaseDiscountAmt;

    /**
     * 整合营销推广结算(商家）
     */
    private String poiMarketDiscount;

    /**
     * 整合营销推广结算(供应商）
     */
    private String supplierMarketDiscount;

    /**
     * 履约服务费
     */
    private String performanceServiceFee;

    /**
     * 履约服务费
     */
    private Integer weight;

    public String picUrl;

    /**
     * 批次信息
     */
    private List<BatchInfoModel> batchInfo;

    /**
     * 货号
     */
    private String goodsCode;

    /**
     * 组合商品
     */
    private List<SubProductVo> subProduct;
    /**
     * 赠品类型，0-平台赠品，1-自定义拣货赠品
     */
    private Integer giftType;

    /**
     * 换货商品数量
     */
    private Integer exchangeGoodsCount = 0;

    /**
     * 商品标签信息
     */
    private List<ChannelLabelVO> channelLabelList;

    /**
     * 商品标签附加信息
     */
    private String labelSubDesc;
}
