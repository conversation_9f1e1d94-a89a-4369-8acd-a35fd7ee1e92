package com.sankuai.shangou.qnh.orderapi.utils.pc;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/28
 */
public class ListConvertUtil {

    public static <T> List<T> convertToNullSafeList(List<T> listOrNull) {
        return Optional.ofNullable(listOrNull)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

}
