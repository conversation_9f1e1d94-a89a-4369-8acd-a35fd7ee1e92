package com.sankuai.shangou.qnh.orderapi.utils.pc;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.google.common.base.Splitter;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.shangou.logistics.warehouse.TradeShippingGrayService;
import com.sankuai.shangou.qnh.orderapi.configuration.pc.SpringUtils;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import com.sankuai.shangou.qnh.orderapi.constant.pc.ConfigDefaultValueConstant;
import com.sankuai.shangou.qnh.orderapi.constant.pc.ConfigKeyConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * MCC配置取值
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MccConfigUtil {

    /**
     * 歪马送酒租户Id列表 MCC配置Key
     */
    private static final String WMSJ_TENANT_IDS = "wmsj.tenant.ids";

    /**
     * 【三方物流单号】进行展示，默认的配送状态列表
     */
    private static final List<Integer> DEFAULT_ORIGIN_WAYBILL_VIEW_DELIVERY_STATUS = Lists.newArrayList(
            DeliveryStatusEnum.DELIVERY_LAUNCHED.getCode(), DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode(),
            DeliveryStatusEnum.RIDER_ASSIGNED.getCode(), DeliveryStatusEnum.RIDER_ARRIVED_SHOP.getCode(),
            DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode(), DeliveryStatusEnum.MERCHANT_DELIVERING.getCode(),
            DeliveryStatusEnum.DELIVERY_DONE.getCode());

    private static final String OCMS_CHANNEL_APP_KEY = "com.sankuai.shangou.empower.ocmschannel";

    public static boolean disableRefreshTicket() {
        return Lion.getConfigRepository().getBooleanValue(ConfigKeyConstant.DISABLE_REFRESH_TICKET, Boolean.TRUE);
    }

    public static String getLoginSecretKey() {
        return Lion.getConfigRepository().get(ConfigKeyConstant.LOGIN_SECRET_KEY);
    }

    public static int getLoginExpireTimeInSeconds() {
        int expireTimeInSeconds = ConfigDefaultValueConstant.DEFAULT_LOGIN_EXPIRE_TIME_IN_SECONDS;
        String expireTimeStr = Lion.getConfigRepository().get(ConfigKeyConstant.LOGIN_EXPIRE_TIME_IN_SECONDS);
        if (StringUtils.isNotBlank(expireTimeStr)) {
            try {
                expireTimeInSeconds = Integer.parseInt(expireTimeStr);
            } catch (Exception e) {
                log.error(String.format("获取MCC配置错误,key:%s", ConfigKeyConstant.LOGIN_EXPIRE_TIME_IN_SECONDS), e);
            }
        }
        return expireTimeInSeconds;
    }

    public static String getAllowDomainWhitelist() {
        return Lion.getConfigRepository().get(ConfigKeyConstant.ALLOW_DOMAIN_WHITELIST, ConfigDefaultValueConstant.DEFAULT_ALLOW_DOMAIN_WHITELIST);
    }

    public static boolean isAllServiceUnavailable() {
        return Lion.getConfigRepository().getBooleanValue(ConfigKeyConstant.API_REJECT_SWITCH, false);
    }

    public static String getRejectText() {
        return Lion.getConfigRepository().get(ConfigKeyConstant.API_REJECT_TEXT, "系统维护中，请00:00点之后再进行操作，如有疑问请咨询您的渠道经理。");
    }

    public static boolean tenantNeedShowTestServicePackage(Long tenantId) {
        try {
            List<String> whiteList = Optional.ofNullable(Lion.getConfigRepository().get("delivery_show_test_service_package_tenant_list"))
                    .map(it -> it.split(CommonConstant.COMMA))
                    .map(Arrays::asList)
                    .orElse(new ArrayList<>());
            return whiteList.contains(String.valueOf(tenantId));
        } catch (Exception e) {
            log.error("Decide tenantNeedShowTestServicePackage failed, will use default [false]", e);
            return false;
        }
    }

    public static boolean validAccountBizAppId() {
        return Lion.getConfigRepository().getBooleanValue(ConfigKeyConstant.VALID_ACCOUNT_BIZ_APP_ID, false);
    }

    /**
     * 订单调整相关功能是否走结算系统
     * 影响范围：订单是否可调整、订单调整界面中商品摊位和进货价展示、订单调整数据落入哪个系统
     *
     * @return
     */
    public static boolean orderAdjustFromSettlement() {
        boolean adjustFromSettlement = Lion.getConfigRepository().getBooleanValue("order_adjust_from_settlement", true);
        log.info("订单调整相关功能是否走结算系统 adjustFromSettlement:{}", adjustFromSettlement);
        return adjustFromSettlement;
    }

    /**
     * 查询订单调整记录是否走结算系统
     * 影响范围：订单调整记录查询哪个系统
     *
     * @return
     */
    public static boolean queryOrderAdjustRecordFromSettlement() {
        boolean adjustFromSettlement = Lion.getConfigRepository().getBooleanValue("query_order_adjust_record_from_settlement", false);
        log.info("查询订单调整记录是否走结算系统 adjustFromSettlement:{}", adjustFromSettlement);
        return adjustFromSettlement;
    }

    /**
     * 结算系统所支持渠道类型
     *
     * @return
     */
    public static List<Integer> settlementSupportChannelIds() {
        String settlementSupportChannelIds = Lion.getConfigRepository().get("settlement_support_channels", StringUtils.EMPTY);
        if (StringUtils.isBlank(settlementSupportChannelIds)) {
            return Arrays.asList(ChannelType.MEITUAN.getValue(), ChannelType.ELEM.getValue());
        }
        return Splitter.on(",")
                .trimResults()
                .splitToList(settlementSupportChannelIds).stream()
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    /**
     * 歪马租户id
     *
     * @return
     */
    public static List<Long> getDrunkHorseTenantId() {
        String dhTenantIds = Lion.getConfigRepository().get("drunk_horse_tenant_id", "1000395");
        return Splitter.on(",")
                .trimResults()
                .splitToList(dhTenantIds).stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());

    }

    public static boolean isDhTenant(Long tenantId) {
        List<Long> tenantIds = MccConfigUtil.getDrunkHorseTenantId();
        return tenantIds.contains(tenantId);
    }

    /**
     * 查询歪马送酒租户Id列表
     *
     * @return List<Long> 歪马送酒租户Id列表
     */
    public static List<Long> getWmsjTenantIds() {
        String config = Lion.getConfigRepository().get(WMSJ_TENANT_IDS, "[]");
        List<Long> result = org.apache.commons.lang3.StringUtils.isBlank(config) ?
                Collections.emptyList() : com.meituan.linz.boot.util.JacksonUtils.parseList(config, Long.class);
        return CollectionUtils.isEmpty(result) ? Lists.emptyList() : result;

    }

    public static boolean useSacAuthenticationV2() {
        return Lion.getConfigRepository("com.sankuai.sgshopmgmt.empower.pieapi").getBooleanValue("useSacAuthentication2", false);
    }

    /**
     * 歪马送酒租户id
     * @return
     */
    public static List<String> getDHTenantIdList(){
        try {
            String tenantIdStr = Lion.getConfigRepository("com.sankuai.sgfulfillment.tms")
                    .get("drunk.horse.tenant.id.list", "1000395");
            if(org.apache.commons.lang.StringUtils.isBlank(tenantIdStr)){
                return Collections.emptyList();
            }
            return Splitter.on(",").splitToList(tenantIdStr);
        } catch (Exception exception) {
            log.error("getDHTenantIdList error", exception);
            return Collections.emptyList();
        }
    }

    public static boolean isDeliveryChannelConfigStore(Long storeId) {
        try {
            String storeIdListStr = Lion.getConfigRepository("com.sankuai.sgfulfillment.tms")
                    .get("delivery.channel.config.storeId.list", org.apache.commons.lang3.StringUtils.EMPTY);
            // 如果开关对应的字符串为空，全量放开
            if(org.apache.commons.lang3.StringUtils.isEmpty(storeIdListStr)){
                return true;
            }
            return Splitter.on(",").splitToList(storeIdListStr).contains(String.valueOf(storeId));
        } catch (Exception exception) {
            log.error("get deliveryChannelConfigStoreIds error", exception);
            return false;
        }
    }

    public static boolean isDeliveryChannelQuery4Enum() {
        try {
            return Lion.getConfigRepository("com.sankuai.sgfulfillment.tms").getBooleanValue("delivery.channel.query.degrade.switch", false);
        } catch (Exception exception) {
            log.error("get isDeliveryChannelQuery4Enum get(delivery.channel.query.degrade.switch) error", exception);
            return false;
        }
    }

    public static boolean hitConfigKey(String key,List<String> configKeyList) {
        if (CollectionUtils.isEmpty(configKeyList)){
            return false;
        }
        String firstKey = configKeyList.get(0);
        if (StringUtils.equalsIgnoreCase("ALL", firstKey)){
            return true;
        }
        return configKeyList.contains(key);

    }

    public static List<String> getQuerySettlementBizModes(){
        try {
            String bizModes = Lion.getConfigRepository()
                    .get("bizMode_operate_item_from_settlement", "");
            if(StringUtils.isBlank(bizModes)){
                return Collections.emptyList();
            }
            return Splitter.on(",").splitToList(bizModes);
        } catch (Exception exception) {
            log.error("bizMode_operate_item_from_settlement error", exception);
            return Collections.emptyList();
        }
    }

    public static boolean isUmwOrderItemsIncludeGift(Long tenantId) {
        if (null == tenantId) {
            return false;
        }

        List<String> whiteTenantIds = Lion.getConfigRepository().getList("umw_order_items_include_gift_tenants", String.class);
        if (CollectionUtils.isEmpty(whiteTenantIds)) {
            return false;
        }

        return whiteTenantIds.contains("*") || whiteTenantIds.contains(String.valueOf(tenantId));
    }

    /**
     * 检查【三方物流单号】的配送状态，配送状态在配置中的才进行展示
     * 
     * @param deliveryStatus
     * @return
     */
    public static boolean checkOriginWaybillDeliveryStatus(Integer deliveryStatus) {
        List<Integer> deliveryStatusList = Lion.getConfigRepository().getList("originWaybill.view.deliveryStatus",
                Integer.class, DEFAULT_ORIGIN_WAYBILL_VIEW_DELIVERY_STATUS);
        return deliveryStatusList.contains(deliveryStatus);
    }

    // lion配置的key -- 商家支持开票操作功能的租户和门店
    private final static String MERCHANT_SUPPORT_INVOICE_TENANTS_AND_SHOPS = "merchant.support.invoice.tenants.and.shops";

    /**
     * 获取商家支持开票操作功能的租户和门店
     * @return
     */
    public static String getMerchantSupportInvoiceTenantsAndShops() {
        try {
            return Lion.getConfigRepository("com.sankuai.shangou.empower.orderbiz").get(MERCHANT_SUPPORT_INVOICE_TENANTS_AND_SHOPS, "");
        }catch (Exception e){
            log.error("getMerchantSupportInvoiceTenantsAndShops error", e);
            return null;
        }
    }

    /**
     * 校验租户或门店是否支持商家【开发票】操作
     * @param tenantId
     * @param shopId
     * @return
     */
    public static boolean checkMerchantSupportInvoiceTenantsAndShops(Long tenantId, Long shopId) {
        try {
            // 参数不合法，不支持
            if (null == tenantId || 0L == tenantId || null == shopId || 0L == shopId) {
                return false;
            }
            // 获取lion配置
            String configStr = StringUtils.deleteWhitespace(getMerchantSupportInvoiceTenantsAndShops());
            // 无lion配置
            if (StringUtils.isEmpty(configStr)) {
                return false;
            }
            // 如果配置的值是ALL，则表示所有租户，所有门店都支持
            if (StringUtils.equalsIgnoreCase("ALL", configStr)) {
                return true;
            }
            // 如果不是ALL，则转换成Map判断具体租户配置 数据结构：{"tenantId1":"shopId1,shopId2", "tenantId2":"ALL"}
            Map<String, String> configMap = JSONObject.parseObject(configStr, new TypeReference<Map<String, String>>(){}.getType());
            String shopConfigStr = StringUtils.deleteWhitespace(configMap.get(String.valueOf(tenantId)));
            // 租户未配置，则不支持
            if (StringUtils.isBlank(shopConfigStr)) {
                return false;
            }
            // 租户配置的门店数据
            List<String> shopConfigList = getConfigValueList(shopConfigStr, ",");
            return hitConfigKey(String.valueOf(shopId), shopConfigList);
        }catch (Exception e){
            log.error("checkMerchantSupportInvoiceTenantsAndShops error", e);
            return false;
        }
    }

    // lion配置的key -- 租户是否支持【库位指引】操作
    private static final String SUPPORT_REPERTORY_GUIDE_OP_TENANTS = "support_repertory_guide_op_tenants";

    /**
     * 校验租户是否支持【库位指引】操作
     * @param tenantId
     * @return
     */
    public static boolean checkSupportRepertoryGuideOpTenants(Long tenantId) {
        try {
            // 参数不合法，不支持
            if (null == tenantId || 0L == tenantId ) {
                return false;
            }
            String tenantIdsStr = Lion.getConfigRepository().get(SUPPORT_REPERTORY_GUIDE_OP_TENANTS, "");
            // 命中【租户ID】或者【ALL】，则表示支持
            return hitConfigKey(String.valueOf(tenantId), getConfigValueList(StringUtils.deleteWhitespace(tenantIdsStr), ","));
        }catch (Exception e){
            log.error("checkSupportRepertoryGuideOpTenants error", e);
            return false;
        }
    }


    /**
     * 检查该租户是否支持订单明细层级分类
     *
     * @return
     */
    public static boolean checkSupportOrderItemLevelCategoryErpTenants(Long tenantId) {
        try {
            if (Objects.isNull(tenantId)) {
                return false;
            }
            String tenantIdStr = String.valueOf(tenantId);
            List<String> configKeyList = getSupportOrderItemLevelCategoryErpTenants();
            return hitConfigKey(tenantIdStr, configKeyList);
        } catch (Exception e) {
            log.error("checkSupportOrderItemLevelCategoryErpTenants error", e);
            return false;
        }
    }

    // lion配置的key -- 支持订单明细层级分类的ERP租户
    private static String SUPPORT_ORDER_ITEM_LEVEL_CATEGORY_ERP_TENANTS = "support.order.item.level.category.erp.tenants";

    /**
     *
     * 获取支持订单明细层级分类的ERP租户列表
     *
     * @return
     */
    public static List<String> getSupportOrderItemLevelCategoryErpTenants() {
        try {
            String tenantIdsStr = Lion.getConfigRepository("com.sankuai.shangou.empower.orderbiz").get(SUPPORT_ORDER_ITEM_LEVEL_CATEGORY_ERP_TENANTS, "");
            return getConfigValueList(StringUtils.deleteWhitespace(tenantIdsStr), ",");
        } catch (NoSuchElementException nse) {
            log.warn("get support.order.Item.level.category.erp.tenants 配置项不存在", nse);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("getSupportOrderItemLevelCategoryErpTenants error", e);
            return Collections.emptyList();
        }
    }


    /**
     * 检查该租户是否支持根据配置更新库存
     *
     * @return
     */
    public static boolean checkSupportUpdateStockByConfigTenants(Long tenantId) {
        try {
            if (Objects.isNull(tenantId)) {
                return false;
            }
            String tenantIdStr = String.valueOf(tenantId);
            List<String> configKeyList = getSupportUpdateStockByConfigTenants();
            return hitConfigKey(tenantIdStr, configKeyList);
        } catch (Exception e) {
            log.error("checkSupportUpdateStockByConfigTenants error", e);
            return false;
        }
    }

    // lion配置的key -- 支持根据配置更新库存的租户
    private static String SUPPORT_UPDATE_STOCK_BY_CONFIG_TENANTS = "support_update_stock_by_config_tenants";

    /**
     *
     * 获取支持根据配置更新库存的租户列表
     *
     * @return
     */
    public static List<String> getSupportUpdateStockByConfigTenants() {
        try {
            String tenantIdsStr = Lion.getConfigRepository().get(SUPPORT_UPDATE_STOCK_BY_CONFIG_TENANTS, "");
            return getConfigValueList(StringUtils.deleteWhitespace(tenantIdsStr), ",");
        } catch (Exception e) {
            log.error("getSupportUpdateStockByConfigTenants error", e);
            return Collections.emptyList();
        }
    }


    /**
     * 获取配置值分割成列表
     * @param configValue
     * @param splitter
     * @return
     */
    private static List<String> getConfigValueList(String configValue, String splitter) {
        if (StringUtils.isEmpty(configValue)) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(configValue.split(splitter));
    }



    private static final String INVOICE_URL_FOR_SHOP_CONFIG = "invoice.url.for.shop.config";
    /**
     * 获取门店发票二维码链接配置
     *
     * @return
     */
    public static String getInvoiceUrlForShopConfig() {
        try {
            return Lion.getConfigRepository("com.sankuai.shangou.empower.orderbiz").get(INVOICE_URL_FOR_SHOP_CONFIG, "");
        } catch (Exception e) {
            log.error("get invoice.url.for.shop.config error", e);
            return "";
        }
    }


    public static boolean enableCheckOrderMode(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            return false;
        }
        List<Long> enableTenantIds = Lion.getConfigRepository(OCMS_CHANNEL_APP_KEY)
                .getList("config.txd.enableCheckOrderModeTenantIds", Long.class, Collections.emptyList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(enableTenantIds)) {
            return false;
        }
        return enableTenantIds.contains(tenantId);
    }

    public static boolean isSingleModeByLion(Long tenantId) {

        if (Objects.isNull(tenantId)) {
            return false;
        }
        List<Long> singleModeTenantIds = Lion.getConfigRepository(OCMS_CHANNEL_APP_KEY)
                .getList("config.txd.singleModeTenantIds", Long.class, Collections.emptyList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(singleModeTenantIds)) {
            return false;
        }
        return singleModeTenantIds.contains(tenantId);

    }

    public static boolean isDoubleModeByLion(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            return false;
        }
        List<Long> doubleModeTenantIds = Lion.getConfigRepository(OCMS_CHANNEL_APP_KEY)
                .getList("config.txd.doubleModeTenantIds", Long.class, Collections.emptyList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(doubleModeTenantIds)) {
            return false;
        }
        return doubleModeTenantIds.contains(tenantId);

    }

    /**
     * 是否为歪马加盟费率的灰度门店
     * @return
     */
    public static Boolean checkIsFranchiseeFeePoi(Long tenantId, Long storeId) {
        if (!isDhTenant(tenantId)) {
            return false;
        }
        try {
            List<Long> stores = Lion.getConfigRepository("com.sankuai.shangou.empower.orderbiz").getList("franchisee.fee.gray.stores", Long.class, com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists.newArrayList());
            if (CollectionUtils.isEmpty(stores)) {
                return false;
            }
            //全量逻辑
            if (stores.size() == 1 && stores.get(0).equals(-1L)) {
                return true;
            }
            return stores.contains(storeId);
        } catch (Exception e) {
            log.error("getDHByPositionSwitch error", e);
            return false;
        }
    }

    /**
     * 查看订单收货地址的权限CODE
     *
     * @return
     */
    public static String getOrderRecvAddressPermissionCode() {
        return Lion.getConfigRepository().get(ConfigKeyConstant.ORDER_RECV_ADDRESS_PERMISSION_CODE, "order_recv_address");
    }

    /**
     * 查看订单收货地址的权限CODE
     *
     * @return
     */
    public static String getOrderPrivatePhonePermissionCode() {
        return Lion.getConfigRepository().get(ConfigKeyConstant.ORDER_RECV_ADDRESS_PERMISSION_CODE, "order_real_phone");
    }

    /**
     * 判断门店是否支持开发票-歪马
     *
     * @param poiId
     * @return
     */
    public static boolean checkDhShopOrderCreateInvoice(Long poiId) {
        List<Long> dhShopConfig = Lion.getConfigRepository().getList("dhShop.orderCreateInvoice.switch", Long.class);
        if (CollectionUtils.isEmpty(dhShopConfig)) {
            return false;
        }
        return dhShopConfig.contains(-1L) || dhShopConfig.contains(poiId);
    }

    public static Boolean isHighPriceTagGrayStore(Long storeId) {
        if (storeId == null) {
            return false;
        }
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.sgshopmgmt.empower.pieapi")
                .getList("high.price.tag.gray.store", Long.class, Collections.emptyList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && grayStoreIds.contains(-1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static Integer getShortExpirationThreshold() {
        return Lion.getConfigRepository("com.sankuai.sgshopmgmt.empower.pieapi").getIntValue("short.expiration.threshold", 90);
    }

    public static boolean isNewPickGrayStore(long storeId) {
        try {
            //配置统一在oio上
            TradeShippingGrayService tradeShippingGrayService = SpringUtils.getBean(TradeShippingGrayService.class);
            return tradeShippingGrayService.isGrayStore(storeId);
        } catch (Exception e) {
            log.error("get isNewPickGrayStore error", e);
            return false;
        }
    }

    /**
     * 判断该租户是否使用新标签规则
     * 默认不使用，-1为全量
     *
     * @return
     */
    public static boolean isUseNewOrderMark(Long tenantId) {
        List<Long> tenantIds = Lion.getConfigRepository().getList("use.new.order.mark.tenantIds", Long.class,
                Lists.newArrayList());
        if (com.dianping.pigeon.util.CollectionUtils.isEmpty(tenantIds)) {
            return false;
        }
        return tenantIds.contains(tenantId) || tenantIds.contains(-1L);
    }

    /**
     * 结果数据校验开关
     * 
     * @param tenantId
     * @return
     */
    public static boolean resultDataSecuritySwitch(Long tenantId) {
        List<Long> tenantIds = Lion.getConfigRepository().getList("resultData.security.tenantIds", Long.class,
                Lists.newArrayList());
        if (CollectionUtils.isEmpty(tenantIds)) {
            return false;
        }
        return tenantIds.stream().anyMatch(item -> item.equals(-1L) || item.equals(tenantId));
    }

    /**
     * 货品查询接口返回信息取值，是否切换到最新的字段
     * @param warehouseId  仓ID
     * @return             boolean
     */
    public static boolean goodsQuerySwitch2NewFields(Long warehouseId) {
        if (Objects.isNull(warehouseId)) {
            return false;
        }
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.shangou.logistics.oio")
                                      .getList("goods.query.switch.new.fields.stores", Long.class, Collections.emptyList());
        // -1 表示全量
        if (grayStoreIds.size() == 1 && Objects.equals(-1L, grayStoreIds.get(0))) {
            return true;
        }
        return grayStoreIds.contains(warehouseId);
    }

    /**
     * 查询订单操作项最大限制
     * @return
     */
    public static int getQueryOrderOperateItemsMaxSize() {
        int defaultSize = 200;
        try {
            return Lion.getConfigRepository().getIntValue("query_order_operate_items_max_size", defaultSize);
        } catch (Exception e) {
            log.error("getQueryOrderOperateItemsMaxSize error", e);
            return defaultSize;
        }
    }

}
