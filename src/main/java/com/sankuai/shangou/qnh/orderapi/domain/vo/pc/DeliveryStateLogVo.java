package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Getter;
import lombok.ToString;

/**
 * DeliveryStatusLogVo
 *
 * <AUTHOR>
 * @since 2023/3/6
 */
@Getter
@ToString
public class DeliveryStateLogVo extends DeliveryLogVo {

    @FieldDoc(description = "第N次配送")
    private final Integer deliveryCount;

    @FieldDoc(description = "骑手电话")
    private final String riderPhoneNo;

    public DeliveryStateLogVo(String recordedName, String description, int deliveryCount, long time, String riderPhoneNo) {
        super(recordedName, description, DeliveryLogCategory.STATUS_LOG, time);
        this.deliveryCount = deliveryCount;
        this.riderPhoneNo = riderPhoneNo;
    }

}
