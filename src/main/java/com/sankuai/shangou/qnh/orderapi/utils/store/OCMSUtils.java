package com.sankuai.shangou.qnh.orderapi.utils.store;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.TenantSwitchGetRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.quote.QuoteSkuPriceDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.quote.QuoteCommitRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.dto.sku.CdqStoreSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.dto.sku.CdqStoreSkuWithChannelInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.dto.sku.CdqSuggestStoreSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.request.sku.CdqStoreSkuDetailQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.request.sku.CdqStoreSkuSaveRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.request.sku.CdqSuggestStoreSkuRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.response.sku.CdqStoreSkuDetailQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.response.sku.CdqSuggestStoreSkuListResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.AllAssemblyMultiChannelStoreCategoryQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.AssemblyChannelStoreFrontCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.AssemblyChannelStoreQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreFrontCategoryKey;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.BizTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeviceTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OperateSourceEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.SkuOperateSourceType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.reponse.sku.SkuChannelStatusChangeResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.reponse.sku.StoreSkuSaveResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.reponse.sku.TenantSkuPageInfoQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.reponse.sku.TenantSkuQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.request.sku.SkuChannelStatusChangeRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.request.sku.StoreSkuSaveRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.request.sku.TenantSkuPageInfoQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.request.sku.TenantSkuQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.request.sku.TenantSkuSaveRequest;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.PageQueryBrandCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.Page;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.SkuBrandInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.SkuSortInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.BrandPageResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.SkuSortInfoResult;
import com.sankuai.meituan.shangou.saas.common.money.PriceUtils;
import com.sankuai.shangou.qnh.orderapi.constant.store.ProjectConstants;
import com.sankuai.shangou.qnh.orderapi.domain.dto.store.ChannelStoreFrontCategoryKeyDTO;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.BatchQuerySkuInfoByUpcListRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.CdqSuggestStoreSkuQueryRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.ChangeMultiChannelSkuStatusRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.ChangeStoreSkuAutoUploadTimeRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.ChgChannelSkuStatusForAppRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.CreateBrandRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.CreateStoreSkuReq;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.FindChannelFrontCategoryRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.FindSkuInfoForAppRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.GetAllAssemblyChannelStoreCategoryRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.GetChannelBrandRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.GetChannelCategoryRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.GetSystemRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.PageQueryBrandRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.PicturePageInfoByNameQueryRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.QueryCdqStoreSkuDetailRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.QueryPictureByUpcRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.QuerySkuChannelsInfoRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.QuerySkuPageInfoBySkuNameRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.QueryStoreSkuInfoV2Req;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.QueryTenantSkuPageInfoRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.QueryTenantSkuRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.SaveCdqStoreSkuRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.SaveStoreSkuRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.SaveTenantSkuRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.SkuListPageForAppRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.UpdatePriceAndStockForAppRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.UpdateStoreSkuReq;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.BatchQuerySkuInfoByUpcListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CdqSuggestStoreSkuListQueryResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.ChangeMultiChannelSkuStatusResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CreateBrandResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CreateStoreSkuResp;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.FindChannelStoreSkuFrontCategoryResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.GetAllAssemblyChannelStoreCategoryResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.GetChannelBrandResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.GetChannelCategoryResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.PictureByUpcResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.PicturePageInfoByNameResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QuerySkuChannelsInfoResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QuerySkuPageInfoBySkuNameResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QueryStoreOnlineSkuDetailResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QueryTenantSkuPageInfoResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QueryTenantSkuResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QuoteSkuPriceResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.SaveStoreSkuResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.SaveTenantSkuResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.AssemblyChannelStoreFrontCategoryVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CdqStoreSkuVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CdqStoreSkuWithChannelInfoVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CdqSuggestStoreSkuVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChangeChannelSkuStatusForAppResponseVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelBrandVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelCategoryRelationVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelCategoryVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelCategoryWithPathVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelPriceAndStockVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelSkuDetailInfoVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelSkuForAppVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelSkuInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelStatusChangeParamVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelStoreFrontCategoryKeyVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CreateStoreSkuVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ErrorRecordVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.FirstCategoryWithSkuCountVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.FrontCategoryVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.GetOrderForBoothResponseVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderForBoothVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderItemForBoothVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PageInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PageQueryBrandVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.QuerySkuSortInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuBasicInfoVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuBrandInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuChannelStatusChangeResultVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuListPageForAppResponseVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuPriceAndStock;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuSortInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.StoreSkuPropertyVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.StoreSkuVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SystemInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.TenantSku;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.TimeSlotVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.UpdateFailedRecordVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.UpdatePriceStockResponseVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.User;
import com.sankuai.shangou.qnh.orderapi.enums.store.SaveType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.codehaus.jackson.type.TypeReference;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2019/4/15
 * desc: 中台工具类
 */
@Slf4j
public class OCMSUtils {

    private OCMSUtils() {

    }

    private static final int AUTO_ON_SALE_NEXT_DAY_YES = 1;
    private static final int AUTO_ON_SALE_YES = 1;
    private static final int SKU_STATUS_ONLINE = 1;
    private static final String ANDROID = "android";
    private static final String IOS = "ios";
    private static final String PHONE_MUSK = "**";

    //门店打标  0-未知，1-门店未添加，2-门店已添加
    private static final Integer STORE_ON_SALE_ADD = 2;

    private static final int TENANT_CONFIG_ID_INFINITE_SKU = 4;

    private static final int HEADQUARTERS_PRICE_CONFIG = 17;

    //创建门店商品请求来源-APP
    private static final int CREATE_STORE_SKU_REQUEST_SOURCE_APP = 3;

    //门店无限库存配置
    public static final int POI_INFINITE_INVENTORY=27;


    public static CreateStoreSkuResp convertCreateStoreSkuResp(StoreSkuSaveResponse storeSkuSaveResponse) {
        CreateStoreSkuResp createStoreSkuResp = new CreateStoreSkuResp();
        createStoreSkuResp.setSkuId(storeSkuSaveResponse.getSkuId());
        createStoreSkuResp.setSkuChannelsInfoList(convertChannelSkuInfoVOList(storeSkuSaveResponse.getChannelSkuInfoList()));
        return createStoreSkuResp;
    }

    public static CreateBrandResponse convertCreateBrandResponse(com.sankuai.meituan.shangou.empower.ocms.thrift.dto.CreateBrandResponse response) {
        CreateBrandResponse createBrandResponse = new CreateBrandResponse();
        createBrandResponse.setBrandCode(response.getBrandCode());
        return createBrandResponse;
    }

    public static SaveTenantSkuResponse convertSaveTenantSkuResponse(Response response) {
        SaveTenantSkuResponse saveTenantSkuResponse = new SaveTenantSkuResponse();
        saveTenantSkuResponse.setErrorRecordVOList(convertErrorRecordVOList(response.getErrorRecordList()));
        return saveTenantSkuResponse;
    }

    public static GetAllAssemblyChannelStoreCategoryResponse convertGetAllAssemblyChannelStoreCategoryResponse(AssemblyChannelStoreQueryResponse response) {
        GetAllAssemblyChannelStoreCategoryResponse getAllAssemblyChannelStoreCategoryResponse = new GetAllAssemblyChannelStoreCategoryResponse();
        getAllAssemblyChannelStoreCategoryResponse.setAssemblyChannelStoreCategoryList(convertAssemblyChannelStoreFrontCategoryVOList(response.getAssemblyChannelStoreCategoryList()));
        return getAllAssemblyChannelStoreCategoryResponse;
    }

    public static StoreSkuVO convertStoreSkuVO(QueryStoreSkuInfoResponse response) {
        if (Objects.isNull(response.getStoreSkuInfo())) {
            return null;
        }

        StoreSkuDetailDTO storeSkuDetailDTO = response.getStoreSkuInfo();

        StoreSkuVO storeSkuVO = new StoreSkuVO();
        storeSkuVO.setSku(storeSkuDetailDTO.getSku());
        storeSkuVO.setName(storeSkuDetailDTO.getName());
        storeSkuVO.setUpcList(storeSkuDetailDTO.getUpcList());
        storeSkuVO.setImageList(storeSkuDetailDTO.getImages());
        storeSkuVO.setSpec(storeSkuDetailDTO.getSpec());
        storeSkuVO.setUnit(storeSkuDetailDTO.getUnit());
        storeSkuVO.setBrandCode(storeSkuDetailDTO.getBrandCode());
        storeSkuVO.setBrandName(storeSkuDetailDTO.getBrandName());
        storeSkuVO.setBrandCodePath(storeSkuDetailDTO.getBrandCodePath());
        storeSkuVO.setBrandNamePath(storeSkuDetailDTO.getBrandNamePath());
        storeSkuVO.setCategoryCode(storeSkuDetailDTO.getCategoryCode());
        storeSkuVO.setCategoryName(storeSkuDetailDTO.getCategoryName());
        storeSkuVO.setCategoryCodePath(storeSkuDetailDTO.getCategoryCodePath());
        storeSkuVO.setCategoryNamePath(storeSkuDetailDTO.getCategoryNamePath());
        storeSkuVO.setWeightType(storeSkuDetailDTO.getNeedWeight());
        storeSkuVO.setSkuType(storeSkuDetailDTO.getType());
        storeSkuVO.setAvailable(storeSkuDetailDTO.getAvailable());
        storeSkuVO.setAllowSale(storeSkuDetailDTO.getAllowSale());
        storeSkuVO.setPrice(storeSkuDetailDTO.getStorePrice());
        storeSkuVO.setStock(storeSkuDetailDTO.getStoreStock());
        storeSkuVO.setBoothId(storeSkuDetailDTO.getBoothId());
        storeSkuVO.setWeight(storeSkuDetailDTO.getWeight());
        storeSkuVO.setOldBoothId(storeSkuDetailDTO.getOldBoothId());
        storeSkuVO.setStoreStatus(storeSkuDetailDTO.getStoreStatus());
        storeSkuVO.setAutoResumeInfiniteStock(storeSkuDetailDTO.getAutoResumeInfiniteStock());
        storeSkuVO.setCustomizeStockQuantity(storeSkuDetailDTO.getCustomizeStockQuantity());
        storeSkuVO.setCustomizeStockFlag(storeSkuDetailDTO.getCustomizeStockFlag());
        storeSkuVO.setSkuChannelsInfoList(convertToChannelSkuInfoVOList(storeSkuDetailDTO.getChannelSkuInfoList()));

        if (Objects.nonNull(storeSkuDetailDTO.getAvailableTimes()) && storeSkuDetailDTO.getAvailableTimes().size() > 0) {
            if (Objects.nonNull(storeSkuDetailDTO.getAvailableTimes()) && storeSkuDetailDTO.getAvailableTimes().size() > 0) {
                Map<Integer, List<TimeSlotVO>> timeSlotMap = new HashMap<>();
                for(Map.Entry<Integer, List<TimeSlotDTO>> entry : storeSkuDetailDTO.getAvailableTimes().entrySet()){
                    if(Objects.nonNull(entry.getKey()) && CollectionUtils.isNotEmpty(entry.getValue())){
                        timeSlotMap.put(entry.getKey(), entry.getValue().stream().map(TimeSlotVO::new).collect(Collectors.toList()));
                    }
                }
                storeSkuVO.setAvailableTimes(timeSlotMap);
            }
        }
        storeSkuVO.setSpecialty(storeSkuDetailDTO.getSpecialty());
        storeSkuVO.setDescription(storeSkuDetailDTO.getDescription());
        if(CollectionUtils.isNotEmpty(storeSkuDetailDTO.getProperties())){
            storeSkuVO.setProperties(storeSkuDetailDTO.getProperties().stream().map(StoreSkuPropertyVO::new).collect(Collectors.toList()));
        }
        return storeSkuVO;
    }

    private static List<ChannelSkuInfoVO> convertToChannelSkuInfoVOList(List<ChannelSkuDetailInfoDTO> channelSkuDetailInfoDTOList) {
        List<ChannelSkuInfoVO> channelSkuInfoVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(channelSkuDetailInfoDTOList)) {
            channelSkuDetailInfoDTOList.forEach(channelSkuDetailInfoDTO -> {
                ChannelSkuInfoVO channelSkuInfoVO = new ChannelSkuInfoVO();
                channelSkuInfoVO.setChannelId(channelSkuDetailInfoDTO.getChannelId());
                channelSkuInfoVO.setSkuStatus(skuStatusNew2Old(channelSkuDetailInfoDTO.getSkuStatus()));
                channelSkuInfoVO.setPrice(channelSkuDetailInfoDTO.getPrice());
                channelSkuInfoVO.setStock(channelSkuDetailInfoDTO.getStock());
                channelSkuInfoVO.setFrontCategoryId(channelSkuDetailInfoDTO.getFrontCategoryCode());
                channelSkuInfoVO.setFrontCategoryName(channelSkuDetailInfoDTO.getFrontCategoryName());
                channelSkuInfoVO.setFrontCategoryCodePath(channelSkuDetailInfoDTO.getFrontCategoryCodePath());
                channelSkuInfoVO.setFrontCategoryNamePath(channelSkuDetailInfoDTO.getFrontCategoryNamePath());
                channelSkuInfoVO.setAtPromotion(channelSkuDetailInfoDTO.isAtPromotion());
                channelSkuInfoVO.setPriceEqualSign(channelSkuDetailInfoDTO.isPriceEqualSign());
                channelSkuInfoVO.setPresentPrice(channelSkuDetailInfoDTO.getPresentPrice());

                // 根据平台商品审核状态转换渠道商品可售状态(美团)
                channelSkuInfoVO.setAllowSale(ChannelAuditStatusEnum.ofCode(channelSkuDetailInfoDTO.getAuditStatus()).toAllowSale());
                channelSkuInfoVO.setAuditStatus(channelSkuDetailInfoDTO.getAuditStatus());

                // 渠道类目
                channelSkuInfoVO.setChannelCategoryCode(channelSkuDetailInfoDTO.getChannelCategoryCode());
                channelSkuInfoVO.setChannelCategoryName(channelSkuDetailInfoDTO.getChannelCategoryName());
                channelSkuInfoVO.setChannelCategoryCodePath(channelSkuDetailInfoDTO.getChannelCategoryCodePath());
                channelSkuInfoVO.setChannelCategoryNamePath(channelSkuDetailInfoDTO.getChannelCategoryNamePath());

                channelSkuInfoVOList.add(channelSkuInfoVO);
            });
        }
        return channelSkuInfoVOList;
    }

    private static int skuStatusNew2Old(int skuStatus) {
        switch (skuStatus) {
            //未上线
            case -1:
                return 3;
            //已上架
            case 1:
                return 1;
            //已下架
            case 2:
                return 2;
            default:
                return skuStatus;
        }
    }

    private static List<AssemblyChannelStoreFrontCategoryVO> convertAssemblyChannelStoreFrontCategoryVOList(List<AssemblyChannelStoreFrontCategoryDTO> assemblyChannelStoreFrontCategoryDTOList) {
        List<AssemblyChannelStoreFrontCategoryVO> assemblyChannelStoreFrontCategoryVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(assemblyChannelStoreFrontCategoryDTOList)) {
            assemblyChannelStoreFrontCategoryDTOList.forEach(category -> {
                AssemblyChannelStoreFrontCategoryVO assemblyChannelStoreFrontCategoryVO = new AssemblyChannelStoreFrontCategoryVO();
                assemblyChannelStoreFrontCategoryVO.setName(category.getName());
                assemblyChannelStoreFrontCategoryVO.setParentName(category.getParentName());
                assemblyChannelStoreFrontCategoryVO.setLevel(category.getLevel());
                assemblyChannelStoreFrontCategoryVO.setHasSku(category.isHasSku() ? 1 : 0);
                assemblyChannelStoreFrontCategoryVO.setSubAmount(category.getSubAmount());
                assemblyChannelStoreFrontCategoryVO.setChannelIdList(category.getChannelIdList());
                assemblyChannelStoreFrontCategoryVO.setNamePath(category.getNamePath());
                assemblyChannelStoreFrontCategoryVO.setParentNamePath(category.getParentNamePath());
                assemblyChannelStoreFrontCategoryVO.setCategoryList(convertChannelStoreFrontCategoryKeyVOList(category.getCategories()));
                assemblyChannelStoreFrontCategoryVOList.add(assemblyChannelStoreFrontCategoryVO);
            });
        }
        return assemblyChannelStoreFrontCategoryVOList;
    }

    private static List<ChannelStoreFrontCategoryKeyVO> convertChannelStoreFrontCategoryKeyVOList(List<ChannelStoreFrontCategoryKey> channelStoreFrontCategoryKeyList) {
        List<ChannelStoreFrontCategoryKeyVO> channelStoreFrontCategoryKeyVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(channelStoreFrontCategoryKeyList)) {
            channelStoreFrontCategoryKeyList.forEach(categoryKey -> {
                ChannelStoreFrontCategoryKeyVO channelStoreFrontCategoryKeyVO = new ChannelStoreFrontCategoryKeyVO();
                channelStoreFrontCategoryKeyVO.setChannelId(categoryKey.getChannelId());
                channelStoreFrontCategoryKeyVO.setStoreId(categoryKey.getStoreId());
                channelStoreFrontCategoryKeyVO.setFrontCategoryId(categoryKey.getFrontCategoryId());
                channelStoreFrontCategoryKeyVOList.add(channelStoreFrontCategoryKeyVO);
            });
        }
        return channelStoreFrontCategoryKeyVOList;
    }

    public static QuerySkuChannelsInfoResponse convertResponse(SkuChannelsInfoQueryResponse skuChannelsInfoQueryResponse) {
        if (Objects.isNull(skuChannelsInfoQueryResponse)) {
            return null;
        }
        QuerySkuChannelsInfoResponse response = new QuerySkuChannelsInfoResponse();
        response.setSkuCode(skuChannelsInfoQueryResponse.getSkuBaseInfo() != null ? skuChannelsInfoQueryResponse.getSkuBaseInfo().getSkuCode() : null);
        response.setSkuName(skuChannelsInfoQueryResponse.getSkuBaseInfo() != null ? skuChannelsInfoQueryResponse.getSkuBaseInfo().getSkuName() : null);
        response.setSkuChannelsInfoList(convertChannelSkuInfoVOList(skuChannelsInfoQueryResponse.getSkuChannelsInfo()));
        return response;
    }

    public static SaveStoreSkuResponse convertResponse(StoreSkuSaveResponse storeSkuSaveResponse) {
        SaveStoreSkuResponse response = new SaveStoreSkuResponse();
        response.setSkuId(storeSkuSaveResponse.getSkuId());
        response.setCreated(storeSkuSaveResponse.isCreated() ? 1 : 0);
        response.setChannelSkuInfoList(convertChannelSkuInfoVOList(storeSkuSaveResponse.getChannelSkuInfoList()));
        return response;
    }

    public static List<ChannelSkuInfoVO> convertChannelSkuInfoVOList(List<ChannelSkuInfoDTO> channelSkuInfoDTOList) {
        if (Objects.isNull(channelSkuInfoDTOList)) {
            return Lists.newArrayList();
        }

        List<ChannelSkuInfoVO> channelSkuInfoVOList = Lists.newArrayList();
        for (ChannelSkuInfoDTO channelSkuInfoDTO : channelSkuInfoDTOList) {
            ChannelSkuInfoVO channelSkuInfoVO = new ChannelSkuInfoVO();
            channelSkuInfoVO.setChannelId(channelSkuInfoDTO.getChannelId());
            channelSkuInfoVO.setSkuStatus(skuStatusNew2Old(channelSkuInfoDTO.getSkuStatus()));
            channelSkuInfoVO.setPrice(channelSkuInfoDTO.getPrice());
            channelSkuInfoVO.setStock(channelSkuInfoDTO.getStock());
            channelSkuInfoVO.setFrontCategoryId(channelSkuInfoDTO.getFrontCategoryCode());
            channelSkuInfoVO.setFrontCategoryName(channelSkuInfoDTO.getFrontCategoryName());
            channelSkuInfoVO.setFrontCategoryCodePath(channelSkuInfoDTO.getFrontCategoryCodePath());
            channelSkuInfoVO.setFrontCategoryNamePath(channelSkuInfoDTO.getFrontCategoryNamePath());
            channelSkuInfoVOList.add(channelSkuInfoVO);
        }
        return channelSkuInfoVOList;
    }

    public static PageQueryBrandVO convertResponse(BrandPageResult brandPageResult) {
        PageQueryBrandVO pageQueryBrandVO = new PageQueryBrandVO();
        pageQueryBrandVO.setBrandList(convertSkuBrandInfoVOList(brandPageResult.getBrandList()));
        pageQueryBrandVO.setPageInfo(convert(brandPageResult.getPage()));
        return pageQueryBrandVO;
    }

    private static PageInfoVO convert(Page page) {
        if (Objects.isNull(page)) {
            return null;
        }
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(page.getPageNum());
        pageInfoVO.setSize(page.getPageSize());
        pageInfoVO.setTotalPage(page.getTotalPage());
        pageInfoVO.setTotalSize(page.getTotalCount());
        return pageInfoVO;
    }

    private static List<SkuBrandInfoVO> convertSkuBrandInfoVOList(List<SkuBrandInfo> skuBrandInfoList) {
        if (Objects.isNull(skuBrandInfoList)) {
            return Lists.newArrayList();
        }

        List<SkuBrandInfoVO> skuBrandInfoVOList = Lists.newArrayList();
        for (SkuBrandInfo skuBrandInfo : skuBrandInfoList) {
            SkuBrandInfoVO skuBrandInfoVO = new SkuBrandInfoVO();
            skuBrandInfoVO.setBrandCode(skuBrandInfo.getBrandCode());
            skuBrandInfoVO.setParentBrandCode(skuBrandInfo.getParentBrandCode());
            skuBrandInfoVO.setCodePath(skuBrandInfo.getCodePath());
            skuBrandInfoVO.setNamePath(skuBrandInfo.getNamePath());
            skuBrandInfoVO.setEnName(skuBrandInfo.getEnName());
            skuBrandInfoVO.setZhName(skuBrandInfo.getZhName());
            skuBrandInfoVO.setBrandType(skuBrandInfo.getBrandType());
            skuBrandInfoVO.setBrandStatus(skuBrandInfo.getBrandStatus());
            skuBrandInfoVO.setLevel(skuBrandInfo.getLevel());
            skuBrandInfoVO.setLogoPic(skuBrandInfo.getLogoPic());
            skuBrandInfoVOList.add(skuBrandInfoVO);
        }
        return skuBrandInfoVOList;
    }

    public static FindChannelStoreSkuFrontCategoryResponse convertResponse(SkuFrontCategoryResponse skuFrontCategoryResponse) {
        FindChannelStoreSkuFrontCategoryResponse response = new FindChannelStoreSkuFrontCategoryResponse();
        response.setCategoryList(convertStoreFrontCategoryVOList(skuFrontCategoryResponse.getFrontCategoryList()));
        return response;
    }

    private static List<FrontCategoryVO> convertStoreFrontCategoryVOList(List<FrontCategoryDTO> frontCategoryDTOList) {
        if (Objects.isNull(frontCategoryDTOList)) {
            return Lists.newArrayList();
        }

        List<FrontCategoryVO> frontCategoryVOList = Lists.newArrayList();
        for (FrontCategoryDTO frontCategoryDTO : frontCategoryDTOList) {
            FrontCategoryVO frontCategoryVO = new FrontCategoryVO();
            frontCategoryVO.setId(frontCategoryDTO.getId());
            frontCategoryVO.setName(frontCategoryDTO.getName());
            frontCategoryVO.setLevel(frontCategoryDTO.getLevel());
            frontCategoryVO.setParentId(frontCategoryDTO.getParentId());
            frontCategoryVO.setSort(frontCategoryDTO.getSort());
            frontCategoryVO.setHasSku(frontCategoryDTO.isHasSku() ? 1 : 0);
            frontCategoryVO.setSubAmount(frontCategoryDTO.getSubAmount());
            frontCategoryVO.setChannelId(frontCategoryDTO.getChannelId());
            frontCategoryVO.setStoreCategoryId(frontCategoryDTO.getPoiCategoryId());
            frontCategoryVOList.add(frontCategoryVO);
        }
        return frontCategoryVOList;
    }

    public static QueryTenantSkuResponse convertResponse(TenantSkuQueryResponse tenantSkuQueryResponse) {
        if (Objects.isNull(tenantSkuQueryResponse)) {
            return null;
        }
        QueryTenantSkuResponse response = new QueryTenantSkuResponse();
        response.setTenantSku(convertTenantSku(tenantSkuQueryResponse.getTenantSkuInfo()));
        return response;
    }

    private static TenantSku convertTenantSku(TenantSkuDTO tenantSkuDTO) {
        if (Objects.isNull(tenantSkuDTO)) {
            return null;
        }
        TenantSku tenantSku = new TenantSku();
        tenantSku.setSkuId(tenantSkuDTO.getSkuId());
        tenantSku.setImageUrls(tenantSkuDTO.getImageUrls());
        tenantSku.setSkuName(tenantSkuDTO.getSkuName());
        tenantSku.setSpec(tenantSkuDTO.getSpec());
        tenantSku.setUpcList(tenantSkuDTO.getUpcList());
        tenantSku.setBrandCode(tenantSkuDTO.getBrandCode());
        tenantSku.setBrandName(tenantSkuDTO.getBrandName());
        tenantSku.setBrandCodePath(tenantSkuDTO.getBrandCodePath());
        tenantSku.setBrandNamePath(tenantSkuDTO.getBrandNamePath());
        tenantSku.setCategoryCode(tenantSkuDTO.getCategoryCode());
        tenantSku.setCategoryName(tenantSkuDTO.getCategoryName());
        tenantSku.setCategoryCodePath(tenantSkuDTO.getCategoryCodePath());
        tenantSku.setCategoryNamePath(tenantSkuDTO.getCategoryNamePath());
        tenantSku.setBasicSalePrice(tenantSkuDTO.getBasicSalePrice());
        tenantSku.setUnit(tenantSkuDTO.getUnit());
        tenantSku.setWeight(tenantSkuDTO.getWeight());
        tenantSku.setWeightType(tenantSkuDTO.getWeightType());
        tenantSku.setSkuType(tenantSkuDTO.getSkuType());
        tenantSku.setStoreOnSale(tenantSkuDTO.getStoreOnSale());
        tenantSku.setJdBrandId(tenantSkuDTO.getJdBrandId());
        tenantSku.setJdBrandName(tenantSkuDTO.getJdBrandName());
        tenantSku.setJdCategoryId(tenantSkuDTO.getJdCategoryId());
        tenantSku.setJdCategoryName(tenantSkuDTO.getJdCategoryName());
        tenantSku.setMtChannelCategory(transferChannelCategoryWithPathVO(tenantSkuDTO.getMtCategory()));

        return tenantSku;
    }

    private static ChannelCategoryWithPathVO transferChannelCategoryWithPathVO(ChannelCategoryWithPathDTO mtCategory) {
        ChannelCategoryWithPathVO vo = new ChannelCategoryWithPathVO();
        if (mtCategory == null) {
            return vo;
        }
        vo.setChannelCategoryCode(mtCategory.getChannelCategoryCode());
        vo.setChannelCategoryName(mtCategory.getChannelCategoryName());
        vo.setChannelCategoryCodePath(mtCategory.getChannelCategoryCodePath());
        vo.setChannelCategoryNamePath(mtCategory.getChannelCategoryNamePath());
        return vo;
    }


    public static QueryTenantSkuPageInfoResponse convertResponse(TenantSkuPageInfoQueryResponse tenantSkuPageInfoQueryResponse) {
        QueryTenantSkuPageInfoResponse response = new QueryTenantSkuPageInfoResponse();
        response.setTenantSkuList(convertTenantSkuList(tenantSkuPageInfoQueryResponse.getTenantSkusInfo()));
        response.setHasMore(tenantSkuPageInfoQueryResponse.isHasMore() ? 1 : 0);
        response.setTotalPageCount(tenantSkuPageInfoQueryResponse.getTotalPageCount());
        response.setTotalCount(tenantSkuPageInfoQueryResponse.getTotalCount());
        return response;
    }

    private static List<TenantSku> convertTenantSkuList(List<TenantSkuDTO> tenantSkuDTOList) {
        if (Objects.isNull(tenantSkuDTOList)) {
            return Lists.newArrayList();
        }

        List<TenantSku> tenantSkuList = Lists.newArrayList();
        for (TenantSkuDTO tenantSkuDTO : tenantSkuDTOList) {
            TenantSku tenantSku = convertTenantSku(tenantSkuDTO);
            tenantSkuList.add(tenantSku);
        }
        return tenantSkuList;
    }

    public static ChangeMultiChannelSkuStatusResponse convertResponse(SkuChannelStatusChangeResponse skuChannelStatusChangeResponse) {
        ChangeMultiChannelSkuStatusResponse response = new ChangeMultiChannelSkuStatusResponse();
        response.setChannelStatusChangeResults(convertSkuChannelStatusChangeResultVOList(skuChannelStatusChangeResponse.getChannelStatusChangeResults()));
        return response;
    }

    private static List<SkuChannelStatusChangeResultVO> convertSkuChannelStatusChangeResultVOList(List<SkuChannelStatusChangeResult> skuChannelStatusChangeResultList) {
        if (Objects.isNull(skuChannelStatusChangeResultList)) {
            return Lists.newArrayList();
        }

        List<SkuChannelStatusChangeResultVO> skuChannelStatusChangeResultVOList = Lists.newArrayList();
        for (SkuChannelStatusChangeResult skuChannelStatusChangeResult : skuChannelStatusChangeResultList) {
            SkuChannelStatusChangeResultVO skuChannelStatusChangeResultVO = new SkuChannelStatusChangeResultVO();
            skuChannelStatusChangeResultVO.setChannelId(skuChannelStatusChangeResult.getChannelId());
            skuChannelStatusChangeResultVO.setSkuChannelStatus(skuChannelStatusChangeResult.getSkuChannelStatus());
            skuChannelStatusChangeResultVO.setSuccess(skuChannelStatusChangeResult.isSuccess() ? 1 : 0);
            skuChannelStatusChangeResultVO.setFailReason(skuChannelStatusChangeResult.getFailReason());
            skuChannelStatusChangeResultVO.setCode(skuChannelStatusChangeResult.getCode());
            skuChannelStatusChangeResultVOList.add(skuChannelStatusChangeResultVO);
        }
        return skuChannelStatusChangeResultVOList;
    }

    public static OrderForBoothVO convertResponse(BoothOrderDetailResp boothOrderDetailResp) {
        if (Objects.isNull(boothOrderDetailResp.getOrderDetail())) {
            return null;
        }
        OrderForBoothVO orderForBoothVO = new OrderForBoothVO();
        orderForBoothVO.setChannelOrderId(boothOrderDetailResp.getOrderDetail().getChannelOrderId());
        orderForBoothVO.setChannelId(boothOrderDetailResp.getOrderDetail().getChannelId());
        orderForBoothVO.setAmount(BigDecimal.valueOf(boothOrderDetailResp.getOrderDetail().getTotalOfflineAmount()).divide(BigDecimal.valueOf(100)).doubleValue());
        orderForBoothVO.setOrderItems(convertOrderItemForBoothVOList(boothOrderDetailResp.getOrderDetail().getOrderItemForBoothList()));
        return orderForBoothVO;
    }

    private static List<OrderItemForBoothVO> convertOrderItemForBoothVOList(List<OrderItemForBooth> orderItemForBoothList) {
        if (Objects.isNull(orderItemForBoothList)) {
            return Lists.newArrayList();
        }

        List<OrderItemForBoothVO> orderItemForBoothVOList = Lists.newArrayList();
        for (OrderItemForBooth orderItemForBooth : orderItemForBoothList) {
            OrderItemForBoothVO orderItemForBoothVO = new OrderItemForBoothVO();
            orderItemForBoothVO.setSkuName(orderItemForBooth.getSkuName());
            orderItemForBoothVO.setQuantity(orderItemForBooth.getCount());
            orderItemForBoothVO.setSellUnit(orderItemForBooth.getSellUnit());
            orderItemForBoothVO.setSkuCode(orderItemForBooth.getSkuCode());
            orderItemForBoothVO.setOfflinePrice(BigDecimal.valueOf(orderItemForBooth.getOfflinePrice()).divide(BigDecimal.valueOf(100)).doubleValue());
            orderItemForBoothVO.setAmount(BigDecimal.valueOf(orderItemForBooth.getOfflineTotalPrice()).divide(BigDecimal.valueOf(100)).doubleValue());
            orderItemForBoothVOList.add(orderItemForBoothVO);
        }

        return orderItemForBoothVOList;
    }

    public static GetOrderForBoothResponseVO convertResponse(BoothOrderLineResp boothOrderLineResp) {
        GetOrderForBoothResponseVO getOrderForBoothResponseVO = new GetOrderForBoothResponseVO();
        getOrderForBoothResponseVO.setTotalAmount(BigDecimal.valueOf(boothOrderLineResp.getTotalAmountCent()).divide(BigDecimal.valueOf(100)).doubleValue());
        getOrderForBoothResponseVO.setOrders(convertOrderForBoothVOList(boothOrderLineResp.getBoothOrderLineList()));
        return getOrderForBoothResponseVO;
    }

    private static List<OrderForBoothVO> convertOrderForBoothVOList(List<BoothOrderLine> boothOrderLineList) {
        if (Objects.isNull(boothOrderLineList)) {
            return Lists.newArrayList();
        }

        List<OrderForBoothVO> orderForBoothVOList = Lists.newArrayList();
        for (BoothOrderLine boothOrderLine : boothOrderLineList) {
            OrderForBoothVO orderForBoothVO = new OrderForBoothVO();
            orderForBoothVO.setChannelOrderId(boothOrderLine.getChannelOrderId());
            orderForBoothVO.setChannelId(boothOrderLine.getChannelId());
            orderForBoothVO.setAmount(BigDecimal.valueOf(boothOrderLine.getOfflineTotalAmount()).divide(BigDecimal.valueOf(100)).doubleValue());
            orderForBoothVOList.add(orderForBoothVO);
        }

        return orderForBoothVOList;
    }

    public static UpdatePriceStockResponseVO convertResponse(UpdatePriceStockResponse response) {
        UpdatePriceStockResponseVO updatePriceStockResponseVO = new UpdatePriceStockResponseVO();
        updatePriceStockResponseVO.setErrorCode(response.getCode());
        updatePriceStockResponseVO.setErrorRecordList(convertUpdateFailedRecordVOList(response.getErrorRecordList()));
        updatePriceStockResponseVO.setComment(response.getComment());
        updatePriceStockResponseVO.setNeedReview(response.isNeedReview() ? 1 : 0);
        return updatePriceStockResponseVO;
    }

    public static UpdatePriceStockResponseVO convertResponse(CommonResponse<QuoteSkuPriceResponse> response) {
        UpdatePriceStockResponseVO updatePriceStockResponseVO = new UpdatePriceStockResponseVO();
        updatePriceStockResponseVO.setErrorCode(response.getCode());
        updatePriceStockResponseVO.setErrorRecordList(Lists.newArrayList());
        updatePriceStockResponseVO.setComment(response.getData().getInfo());
        updatePriceStockResponseVO.setNeedReview(response.getData().isNeedReview() ? 1 : 0);
        return updatePriceStockResponseVO;
    }

    private static List<UpdateFailedRecordVO> convertUpdateFailedRecordVOList(List<UpdateFailedRecordDTO> updateFailedRecordDTOList) {
        if (Objects.isNull(updateFailedRecordDTOList)) {
            return Lists.newArrayList();
        }

        List<UpdateFailedRecordVO> updateFailedRecordVOList = Lists.newArrayList();

        for (UpdateFailedRecordDTO updateFailedRecordDTO : updateFailedRecordDTOList) {
            UpdateFailedRecordVO updateFailedRecordVO = new UpdateFailedRecordVO();
            updateFailedRecordVO.setStoreId(updateFailedRecordDTO.getStoreId());
            updateFailedRecordVO.setSkuId(updateFailedRecordDTO.getSkuId());
            updateFailedRecordVO.setErrorType(updateFailedRecordDTO.getErrorType());
            updateFailedRecordVO.setErrorMsg(updateFailedRecordDTO.getErrorMsg());
            updateFailedRecordVO.setChannelId(updateFailedRecordDTO.getChannelId());
            updateFailedRecordVOList.add(updateFailedRecordVO);
        }

        return updateFailedRecordVOList;
    }

    public static QuerySkuSortInfoVO convertResponse(SkuSortInfoResult response) {
        QuerySkuSortInfoVO  querySkuSortInfoVO = new QuerySkuSortInfoVO();
        querySkuSortInfoVO.setSkuSortInfos(convertSkuSortInfoVOList(response.getSkuSortInfoList()));
        return querySkuSortInfoVO;
    }

    private static List<SkuSortInfoVO> convertSkuSortInfoVOList(List<SkuSortInfo> skuSortInfoList) {
        if (Objects.isNull(skuSortInfoList)) {
            return Lists.newArrayList();
        }

        List<SkuSortInfoVO> skuSortInfoVOList = Lists.newArrayList();

        for (SkuSortInfo skuSortInfo : skuSortInfoList) {
            SkuSortInfoVO skuSortInfoVO = new SkuSortInfoVO();
            skuSortInfoVO.setSortCode(skuSortInfo.getSortCode());
            skuSortInfoVO.setSortName(skuSortInfo.getSortName());
            skuSortInfoVO.setImgUrl(skuSortInfo.getImgUrl());
            skuSortInfoVO.setShorthandCode(skuSortInfo.getShorthandCode());
            skuSortInfoVO.setOrderDeadline(skuSortInfo.getOrderDeadline());
            skuSortInfoVO.setOrderMoreDeadline(skuSortInfo.getOrderMoreDeadline());
            skuSortInfoVO.setSequence(skuSortInfo.getSequence());
            skuSortInfoVO.setParentSortCode(skuSortInfo.getParentSortCode());
            skuSortInfoVOList.add(skuSortInfoVO);
        }

        return skuSortInfoVOList;
    }

    public static SkuListPageForAppResponseVO convertResponse(SkuListPageForAppResponse response) {
        SkuListPageForAppResponseVO skuListPageForAppResponseVO = new SkuListPageForAppResponseVO();
        skuListPageForAppResponseVO.setSkuInfoList(convertChannelSkuForAppVOList(response.getSkuInfoList()));
        skuListPageForAppResponseVO.setPageInfo(convert(response.getPageInfo()));
        return skuListPageForAppResponseVO;
    }

    private static PageInfoVO convert(PageInfoDTO pageInfoDTO) {
        if (Objects.isNull(pageInfoDTO)) {
            return null;
        }
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(pageInfoDTO.getPage());
        pageInfoVO.setSize(pageInfoDTO.getSize());
        pageInfoVO.setTotalPage(pageInfoDTO.getTotalPage());
        pageInfoVO.setTotalSize(pageInfoDTO.getTotalSize());
        return pageInfoVO;
    }

    private static List<ChannelSkuForAppVO> convertChannelSkuForAppVOList(List<ChannelSkuForAppDTO> channelSkuForAppDTOList) {
        if (Objects.isNull(channelSkuForAppDTOList)) {
            return Lists.newArrayList();
        }

        List<ChannelSkuForAppVO> channelSkuForAppVOList = Lists.newArrayList();

        for (ChannelSkuForAppDTO channelSkuForAppDTO : channelSkuForAppDTOList) {
            ChannelSkuForAppVO channelSkuForAppVO = new ChannelSkuForAppVO();
            channelSkuForAppVO.setTenantId(channelSkuForAppDTO.getTenantId());
            channelSkuForAppVO.setSku(channelSkuForAppDTO.getSku());
            channelSkuForAppVO.setName(channelSkuForAppDTO.getName());
            channelSkuForAppVO.setStoreId(channelSkuForAppDTO.getStoreId());
            channelSkuForAppVO.setImages(channelSkuForAppDTO.getImages());
            channelSkuForAppVO.setSpec(channelSkuForAppDTO.getSpec());
            channelSkuForAppVO.setWeight(channelSkuForAppDTO.getWeight());
            channelSkuForAppVO.setWeightType(channelSkuForAppDTO.getWeightType());
            channelSkuForAppVO.setBasicUnit(channelSkuForAppDTO.getBasicUnit());
            channelSkuForAppVO.setUpcInfo(channelSkuForAppDTO.getUpcInfo());
            channelSkuForAppVO.setChannels(convertChannelPriceAndStockVOList(channelSkuForAppDTO.getChannelPriceAndStocks()));
            channelSkuForAppVO.setCustomizeStockFlag(channelSkuForAppDTO.getCustomizeStockFlag());
            channelSkuForAppVO.setAutoResumeInfiniteStock(channelSkuForAppDTO.getAutoResumeInfiniteStock());
            channelSkuForAppVO.setCustomizeStockQuantity(channelSkuForAppDTO.getCustomizeStockQuantity());
            channelSkuForAppVO.setChannelId2FirstFrontCategoryNameMap(channelSkuForAppDTO.getChannelId2FirstFrontCategoryNameMap());

            channelSkuForAppVOList.add(channelSkuForAppVO);
        }

        return channelSkuForAppVOList;
    }

    private static List<ChannelPriceAndStockVO> convertChannelPriceAndStockVOList(List<ChannelPriceAndStockDTO> channelPriceAndStockDTOList) {
        if (Objects.isNull(channelPriceAndStockDTOList)) {
            return Lists.newArrayList();
        }

        List<ChannelPriceAndStockVO> channelPriceAndStockVOList = Lists.newArrayList();
        for (ChannelPriceAndStockDTO channelPriceAndStockDTO : channelPriceAndStockDTOList) {
            ChannelPriceAndStockVO channelPriceAndStockVO = new ChannelPriceAndStockVO();
            channelPriceAndStockVO.setChannelId(channelPriceAndStockDTO.getChannelId());
            channelPriceAndStockVO.setPrice(channelPriceAndStockDTO.getPrice());
            channelPriceAndStockVO.setStock(channelPriceAndStockDTO.getStock());
            channelPriceAndStockVO.setStatus(channelPriceAndStockDTO.getStatus());
            channelPriceAndStockVO.setAllowSale(ChannelAuditStatusEnum.ofCode(channelPriceAndStockDTO.getAuditStatus()).toAllowSale());
            channelPriceAndStockVO.setAuditStatus(channelPriceAndStockDTO.getAuditStatus());
            channelPriceAndStockVO.setAtPromotion(channelPriceAndStockDTO.atPromotion);
            channelPriceAndStockVO.setPriceEqualSign(channelPriceAndStockDTO.priceEqualSign);
            channelPriceAndStockVOList.add(channelPriceAndStockVO);
        }

        return channelPriceAndStockVOList;
    }

    public static ChangeChannelSkuStatusForAppResponseVO convertResponse(Response response) {
        ChangeChannelSkuStatusForAppResponseVO changeChannelSkuStatusForAppResponseVO = new ChangeChannelSkuStatusForAppResponseVO();
        changeChannelSkuStatusForAppResponseVO.setErrorCode(response.getCode());
        changeChannelSkuStatusForAppResponseVO.setErrorRecordList(convertErrorRecordVOList(response.getErrorRecordList()));
        return changeChannelSkuStatusForAppResponseVO;
    }

    public static List<ErrorRecordVO> convertErrorRecordVOList(List<FailedRecordDTO> failedRecordDTOS) {
        if (Objects.isNull(failedRecordDTOS)) {
            return Lists.newArrayList();
        }

        List<ErrorRecordVO> errorRecordVOList = Lists.newArrayList();
        for (FailedRecordDTO failedRecordDTO : failedRecordDTOS) {
            ErrorRecordVO errorRecordVO = new ErrorRecordVO();
            errorRecordVO.setSku(failedRecordDTO.getSkuId());
            errorRecordVO.setStoreId(failedRecordDTO.getStoreId());
            errorRecordVO.setChannelId(failedRecordDTO.getChannelId());
            errorRecordVO.setErrorMsg(failedRecordDTO.getErrorMsg());
            errorRecordVO.setErrorType(failedRecordDTO.getErrorCode());
            errorRecordVOList.add(errorRecordVO);
        }

        return errorRecordVOList;
    }

    public static ChannelSkuForAppVO convertResponse(ChannelSkuForAppResponse response) {
        if (Objects.isNull(response.getChannelSkuDetail())) {
            return null;
        }
        ChannelSkuForAppVO channelSkuForAppVO = new ChannelSkuForAppVO();
        channelSkuForAppVO.setTenantId(response.getChannelSkuDetail().getTenantId());
        channelSkuForAppVO.setSku(response.getChannelSkuDetail().getSku());
        channelSkuForAppVO.setName(response.getChannelSkuDetail().getName());
        channelSkuForAppVO.setStoreId(response.getChannelSkuDetail().getStoreId());
        channelSkuForAppVO.setStoreName(response.getChannelSkuDetail().getStoreName());
        channelSkuForAppVO.setImages(response.getChannelSkuDetail().getImages());
        channelSkuForAppVO.setSpec(response.getChannelSkuDetail().getSpec());
        channelSkuForAppVO.setWeight(response.getChannelSkuDetail().getWeight());
        channelSkuForAppVO.setWeightType(response.getChannelSkuDetail().getWeightType());
        channelSkuForAppVO.setBasicUnit(response.getChannelSkuDetail().getBasicUnit());
        channelSkuForAppVO.setUpcInfo(response.getChannelSkuDetail().getUpcInfo());
        channelSkuForAppVO.setCustomizeStockFlag(response.getChannelSkuDetail().getCustomizeStockFlag());
        channelSkuForAppVO.setAutoResumeInfiniteStock(response.getChannelSkuDetail().getAutoResumeInfiniteStock());
        channelSkuForAppVO.setCustomizeStockQuantity(response.getChannelSkuDetail().getCustomizeStockQuantity());
        channelSkuForAppVO.setChannels(convertChannelPriceAndStockVOList(response.getChannelSkuDetail().getChannelPriceAndStocks()));
        channelSkuForAppVO.setChannelId2FirstFrontCategoryNameMap(response.getChannelSkuDetail().getChannelId2FirstFrontCategoryNameMap());
        return channelSkuForAppVO;
    }

    public static SystemInfoVO convertResponse(GetStoreSkuStockSetTypeResponse getStoreSkuStockSetTypeResponse) {
        if (Objects.isNull(getStoreSkuStockSetTypeResponse.getData())) {
            return null;
        }
        SystemInfoVO stockSetTypeVO = new SystemInfoVO();
        stockSetTypeVO.setTenantId(getStoreSkuStockSetTypeResponse.getData().getTenantId());
        stockSetTypeVO.setStockSetType(getStoreSkuStockSetTypeResponse.getData().getSetType());
        return stockSetTypeVO;
    }

    public static SkuChannelsInfoQueryRequest convertRequest(QuerySkuChannelsInfoRequest querySkuChannelsInfoRequest, User user) {
        SkuChannelsInfoQueryRequest request = new SkuChannelsInfoQueryRequest();
        request.setTenantId(user.getTenantId());
        request.setStoreId(querySkuChannelsInfoRequest.getStoreId());
        request.setSkuId(querySkuChannelsInfoRequest.getSkuId());
        return request;
    }

    public static ChannelBrandQueryRequest convertRequest(GetChannelBrandRequest getChannelBrandRequest) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        ChannelBrandQueryRequest request = new ChannelBrandQueryRequest();
        request.setTenantId(user.getTenantId());
        request.setChannelId(getChannelBrandRequest.getChannelId());
        if (StringUtils.isNotEmpty(getChannelBrandRequest.getBrandName())) {
            request.setBrandName(getChannelBrandRequest.getBrandName());
        }
        request.setPage(getChannelBrandRequest.getPage());
        request.setPageSize(getChannelBrandRequest.getSize());
        return request;
    }

    public static ChannelCategoryQueryRequest convertRequest(GetChannelCategoryRequest getChannelCategoryRequest) {
        ChannelCategoryQueryRequest request = new ChannelCategoryQueryRequest();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        request.setTenantId(user.getTenantId());
        request.setChannelId(getChannelCategoryRequest.getChannelId());
        request.setParentIds(getChannelCategoryRequest.getParentIdList());
        if (Objects.nonNull(getChannelCategoryRequest.getDepth())) {
            request.setDepth(getChannelCategoryRequest.getDepth());
        }
        return request;
    }

    public static AllAssemblyMultiChannelStoreCategoryQueryRequest convertRequest(GetAllAssemblyChannelStoreCategoryRequest getMultiChannelStoreCategoryRequest) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        AllAssemblyMultiChannelStoreCategoryQueryRequest request = new AllAssemblyMultiChannelStoreCategoryQueryRequest();
        request.setTenantId(user.getTenantId());
        request.setStoreId(getMultiChannelStoreCategoryRequest.getStoreId());
        request.setMultiChannelSetOperationType(getMultiChannelStoreCategoryRequest.getMultiChannelSetOperationType());
        request.setChannelIds(getMultiChannelStoreCategoryRequest.getChannelIdList());
        return request;
    }

    private static List<ChannelStoreFrontCategoryKey> convertChannelStoreFrontCategoryKeyList(List<ChannelStoreFrontCategoryKeyDTO> channelStoreFrontCategoryKeyDTOList, Long storeId) {
        List<ChannelStoreFrontCategoryKey> channelStoreFrontCategoryKeyList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(channelStoreFrontCategoryKeyDTOList)) {
            channelStoreFrontCategoryKeyDTOList.forEach(categoryKey -> {
                ChannelStoreFrontCategoryKey channelStoreFrontCategoryKey = new ChannelStoreFrontCategoryKey();
                channelStoreFrontCategoryKey.setChannelId(categoryKey.getChannelId());
                channelStoreFrontCategoryKey.setStoreId(storeId);
                channelStoreFrontCategoryKey.setFrontCategoryId(categoryKey.getFrontCategoryId());
                channelStoreFrontCategoryKeyList.add(channelStoreFrontCategoryKey);
            });
        }
        return channelStoreFrontCategoryKeyList;
    }

    public static ConfigQueryRequest convertConfigQueryRequest(User user) {
        ConfigQueryRequest request = new ConfigQueryRequest();
        request.setTenantId(user.getTenantId());
        request.setSubjectId(user.getTenantId());
        request.setConfigId(TENANT_CONFIG_ID_INFINITE_SKU);
        return request;
    }

    public static TenantSwitchGetRequest convertPriceConfigQueryRequest(User user) {
        TenantSwitchGetRequest request = new TenantSwitchGetRequest();
        request.setTenantId(user.getTenantId());
        request.setSwitchKey(Lists.newArrayList(ProjectConstants.TENANT_INITIAL_PRICE_STRATEGY));
        return request;
    }

    public static StoreSkuSaveRequest convertRequest(SaveStoreSkuRequest saveStoreSkuRequest, User user) {
        StoreSkuSaveRequest storeSkuSaveRequest = new StoreSkuSaveRequest();
        storeSkuSaveRequest.setTenantId(user.getTenantId());
        storeSkuSaveRequest.setStoreId(saveStoreSkuRequest.getStoreId());
        storeSkuSaveRequest.setSalePrice(saveStoreSkuRequest.getSalePrice());
        if (Objects.nonNull(saveStoreSkuRequest.getStock())) {
            storeSkuSaveRequest.setStock(saveStoreSkuRequest.getStock());
        }
        saveStoreSkuRequest.getTenantSkuInfo().setStoreOnSale(STORE_ON_SALE_ADD);
        storeSkuSaveRequest.setTenantSkuInfo(convertTenantSkuDTO(user, saveStoreSkuRequest.getTenantSkuInfo()));
        storeSkuSaveRequest.setSkipCreateTenantSku(saveStoreSkuRequest.getSkipCreateTenantSku().equals(1) ? true : false);
        storeSkuSaveRequest.setOperator(user.getUsername());
        storeSkuSaveRequest.setOperatorId(user.getAccountId());
        storeSkuSaveRequest.setInfiniteInventory(saveStoreSkuRequest.getIsInfiniteStock().equals(1) ? true : false);
        storeSkuSaveRequest.setChannelStoreCategoryMap(saveStoreSkuRequest.getChannelStoreCategoryMap());
        if (Objects.nonNull(saveStoreSkuRequest.getBizType()) && Objects.nonNull(BizTypeEnum.findByValue(saveStoreSkuRequest.getBizType()))) {
            storeSkuSaveRequest.setBizType(BizTypeEnum.findByValue(saveStoreSkuRequest.getBizType()));
        }
        return storeSkuSaveRequest;
    }

    private static TenantSkuDTO convertTenantSkuDTO(User user, TenantSku tenantSku) {
        TenantSkuDTO tenantSkuDTO = new TenantSkuDTO();
        tenantSkuDTO.setSource(SkuOperateSourceType.APP.getValue());
        tenantSkuDTO.setTenantId(user.getTenantId());
        tenantSkuDTO.setSkuId(tenantSku.getSkuId());
        tenantSkuDTO.setSkuName(tenantSku.getSkuName());
        tenantSkuDTO.setUpcList(tenantSku.getUpcList());
        tenantSkuDTO.setImageUrls(tenantSku.getImageUrls());
        tenantSkuDTO.setSpec(tenantSku.getSpec());
        tenantSkuDTO.setBrandCode(tenantSku.getBrandCode());
        tenantSkuDTO.setBrandName(tenantSku.getBrandName());
        tenantSkuDTO.setBrandCodePath(tenantSku.getBrandCodePath());
        tenantSkuDTO.setBrandNamePath(tenantSku.getBrandNamePath());
        tenantSkuDTO.setCategoryCode(tenantSku.getCategoryCode());
        tenantSkuDTO.setCategoryCodePath(tenantSku.getCategoryCodePath());
        tenantSkuDTO.setCategoryName(tenantSku.getCategoryName());
        tenantSkuDTO.setCategoryNamePath(tenantSku.getCategoryNamePath());
        tenantSkuDTO.setBasicSalePrice(tenantSku.getBasicSalePrice());
        tenantSkuDTO.setUnit(tenantSku.getUnit());
        if (Objects.nonNull(tenantSku.getWeight())) {
            tenantSkuDTO.setWeight(tenantSku.getWeight());
        }
        if (Objects.nonNull(tenantSku.getWeightType())) {
            tenantSkuDTO.setWeightType(tenantSku.getWeightType());
        }
        if (Objects.nonNull(tenantSku.getSkuType())) {
            tenantSkuDTO.setSkuType(tenantSku.getSkuType());
        }
        tenantSkuDTO.setJdBrandId(tenantSku.getJdBrandId());
        tenantSkuDTO.setJdBrandName(tenantSku.getJdBrandName());
        tenantSkuDTO.setJdCategoryId(tenantSku.getJdCategoryId());
        tenantSkuDTO.setJdCategoryName(tenantSku.getJdCategoryName());
        tenantSkuDTO.setStoreOnSale(tenantSku.getStoreOnSale());
        return tenantSkuDTO;
    }

    private static TenantSkuDTO convertTenantSkuDTO(CreateStoreSkuVO createStoreSkuVO) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        TenantSkuDTO tenantSkuDTO = new TenantSkuDTO();
        tenantSkuDTO.setSource(SkuOperateSourceType.APP.getValue());
        tenantSkuDTO.setTenantId(user.getTenantId());
        tenantSkuDTO.setSkuId(createStoreSkuVO.getSkuId());
        tenantSkuDTO.setSkuName(createStoreSkuVO.getName());
        tenantSkuDTO.setUpcList(createStoreSkuVO.getUpcList());
        tenantSkuDTO.setImageUrls(createStoreSkuVO.getImageList());
        tenantSkuDTO.setSpec(createStoreSkuVO.getSpec());
        tenantSkuDTO.setBrandCode(createStoreSkuVO.getBrandCode());
        tenantSkuDTO.setCategoryCode(createStoreSkuVO.getCategoryCode());
        if (Objects.nonNull(createStoreSkuVO.getPrice())) {
            tenantSkuDTO.setBasicSalePrice(createStoreSkuVO.getPrice());
        }
        tenantSkuDTO.setUnit(createStoreSkuVO.getUnit());
        if (Objects.nonNull(createStoreSkuVO.getWeight())) {
            tenantSkuDTO.setWeight(createStoreSkuVO.getWeight());
        }
        if (Objects.nonNull(createStoreSkuVO.getWeightType())) {
            tenantSkuDTO.setWeightType(createStoreSkuVO.getWeightType());
        }
        if (Objects.nonNull(createStoreSkuVO.getSkuType())) {
            tenantSkuDTO.setSkuType(createStoreSkuVO.getSkuType());
        }
        tenantSkuDTO.setJdBrandId(createStoreSkuVO.getJdBrandCode());
        tenantSkuDTO.setJdBrandName(createStoreSkuVO.getJdBrandName());
        tenantSkuDTO.setJdCategoryId(createStoreSkuVO.getJdCategoryCode());
        tenantSkuDTO.setJdCategoryName(createStoreSkuVO.getJdCategoryName());
        return tenantSkuDTO;
    }

    private static TenantSkuDTO convertTenantSkuDTO(CreateStoreSkuReq req) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        TenantSkuDTO tenantSkuDTO = new TenantSkuDTO();
        tenantSkuDTO.setSource(SkuOperateSourceType.APP.getValue());
        tenantSkuDTO.setTenantId(user.getTenantId());
        tenantSkuDTO.setSkuId(req.getStoreSku().getSkuId());
        tenantSkuDTO.setSkuName(req.getStoreSku().getName());
        tenantSkuDTO.setUpcList(req.getStoreSku().getUpcList());
        tenantSkuDTO.setImageUrls(req.getStoreSku().getImageList());
        tenantSkuDTO.setSpec(req.getStoreSku().getSpec());
        tenantSkuDTO.setBrandCode(req.getStoreSku().getBrandCode());
        tenantSkuDTO.setCategoryCode(req.getStoreSku().getCategoryCode());
        tenantSkuDTO.setUnit(req.getStoreSku().getUnit());
        if (Objects.nonNull(req.getStoreSku().getWeight())) {
            tenantSkuDTO.setWeight(req.getStoreSku().getWeight());
        }
        if (Objects.nonNull(req.getStoreSku().getWeightType())) {
            tenantSkuDTO.setWeightType(req.getStoreSku().getWeightType());
        }
        if (Objects.nonNull(req.getStoreSku().getSkuType())) {
            tenantSkuDTO.setSkuType(req.getStoreSku().getSkuType());
        }
        tenantSkuDTO.setJdBrandName(req.getStoreSku().getJdBrandName());
        tenantSkuDTO.setJdCategoryName(req.getStoreSku().getJdCategoryName());
        // 新增美团渠道类目
        ChannelCategoryWithPathDTO category = new ChannelCategoryWithPathDTO();
        category.setChannelCategoryCode(req.getStoreSku().getMtCategoryCode());
        category.setChannelCategoryName(req.getStoreSku().getMtCategoryName());
        tenantSkuDTO.setMtCategory(category);
        return tenantSkuDTO;
    }

    private static TenantSkuDTO convertTenantSkuDTO(UpdateStoreSkuReq req) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        TenantSkuDTO tenantSkuDTO = new TenantSkuDTO();
        tenantSkuDTO.setSource(SkuOperateSourceType.APP.getValue());
        tenantSkuDTO.setTenantId(user.getTenantId());
        tenantSkuDTO.setSkuId(req.getStoreSku().getSkuId());
        tenantSkuDTO.setSkuName(req.getStoreSku().getName());
        tenantSkuDTO.setUpcList(req.getStoreSku().getUpcList());
        tenantSkuDTO.setImageUrls(req.getStoreSku().getImageList());
        tenantSkuDTO.setSpec(req.getStoreSku().getSpec());
        tenantSkuDTO.setBrandCode(req.getStoreSku().getBrandCode());
        tenantSkuDTO.setCategoryCode(req.getStoreSku().getCategoryCode());
        tenantSkuDTO.setUnit(req.getStoreSku().getUnit());
        if (Objects.nonNull(req.getStoreSku().getWeight())) {
            tenantSkuDTO.setWeight(req.getStoreSku().getWeight());
        }
        if (Objects.nonNull(req.getStoreSku().getWeightType())) {
            tenantSkuDTO.setWeightType(req.getStoreSku().getWeightType());
        }
        if (Objects.nonNull(req.getStoreSku().getSkuType())) {
            tenantSkuDTO.setSkuType(req.getStoreSku().getSkuType());
        }
        tenantSkuDTO.setJdBrandName(req.getStoreSku().getJdBrandName());
        tenantSkuDTO.setJdCategoryName(req.getStoreSku().getJdCategoryName());
        return tenantSkuDTO;
    }

    public static com.sankuai.meituan.shangou.empower.ocms.thrift.dto.CreateBrandRequest convertRequest(CreateBrandRequest createBrandRequest, User user) {
        com.sankuai.meituan.shangou.empower.ocms.thrift.dto.CreateBrandRequest request = new com.sankuai.meituan.shangou.empower.ocms.thrift.dto.CreateBrandRequest();
        request.setTenantId(user.getTenantId());
        request.setOperator(user.getUsername());
        request.setOperatorId(user.getAccountId());
        request.setStoreId(createBrandRequest.getStoreId());
        request.setBrandName(createBrandRequest.getBrandName());
        return request;
    }

    public static PageQueryBrandCommand convertRequest(PageQueryBrandRequest pageQueryBrandRequest, User user) {
        PageQueryBrandCommand pageQueryBrandCommand = new PageQueryBrandCommand();
        pageQueryBrandCommand.setTenantId(user.getTenantId());
        pageQueryBrandCommand.setKeywords(pageQueryBrandRequest.getKeywords());
        pageQueryBrandCommand.setPageNo(pageQueryBrandRequest.getPageNum());
        pageQueryBrandCommand.setPageSize(pageQueryBrandRequest.getPageSize());
        return pageQueryBrandCommand;
    }

    public static StoreSkuFrontCategoryQueryRequest convertRequest(FindChannelFrontCategoryRequest findChannelFrontCategoryRequest, User user) {
        StoreSkuFrontCategoryQueryRequest request = new StoreSkuFrontCategoryQueryRequest();
        request.setTenantId(user.getTenantId());
        request.setParentCategoryId(findChannelFrontCategoryRequest.getParentCategoryId());
        request.setOperator(user.getUsername());
        request.setOperatorId(user.getAccountId());
        request.setChannelId(findChannelFrontCategoryRequest.getChannelId());
        request.setStoreId(findChannelFrontCategoryRequest.getStoreId());
        return request;
    }

    public static TenantSkuQueryRequest convertRequest(QueryTenantSkuRequest queryTenantSkuRequest, User user) {
        TenantSkuQueryRequest request = new TenantSkuQueryRequest();
        request.setTenantId(user.getTenantId());
        request.setSkuId(queryTenantSkuRequest.getSkuId());
        return request;
    }

    public static SkuPageQueryForAppRequest convertRequest(SkuListPageForAppRequest request, User user, String boothId) {
        SkuPageQueryForAppRequest skuPageQueryForAppRequest = new SkuPageQueryForAppRequest();
        skuPageQueryForAppRequest.setPage(request.getPage());
        skuPageQueryForAppRequest.setSize(request.getSize());
        skuPageQueryForAppRequest.setKeyword(request.getKeyword());
        skuPageQueryForAppRequest.setStoreId(request.getStoreId());
        skuPageQueryForAppRequest.setCategories(request.getCategories());
        skuPageQueryForAppRequest.setTenantId(user.getTenantId());
        skuPageQueryForAppRequest.setChannelStoreCategoryMap(request.getChannelStoreCategoryMap());
        if (Strings.isNotEmpty(boothId)) {
            skuPageQueryForAppRequest.setBoothIds(Arrays.asList(Long.valueOf(boothId)));
        }
        if (Objects.nonNull(request.getSkuStatus())) {
            skuPageQueryForAppRequest.setSkuStatus(request.getSkuStatus());
        }
        skuPageQueryForAppRequest.setChannelIds(request.getChannelIds());
        if (Objects.nonNull(request.getMtAllowSale())) {
            skuPageQueryForAppRequest.setAuditStatusList(toAuditStatusList(request.getMtAllowSale()));
        }
        return skuPageQueryForAppRequest;
    }

    public static ChangeChannelSkuStatusForAppRequest convertRequest(ChgChannelSkuStatusForAppRequest request, User user) {
        ChangeChannelSkuStatusForAppRequest changeChannelSkuStatusForAppRequest = new ChangeChannelSkuStatusForAppRequest();
        changeChannelSkuStatusForAppRequest.setTenantId(user.getTenantId());
        changeChannelSkuStatusForAppRequest.setStoreId(request.getStoreId());
        changeChannelSkuStatusForAppRequest.setOperator(user.getUsername());
        changeChannelSkuStatusForAppRequest.setOperatorId(user.getAccountId());
        changeChannelSkuStatusForAppRequest.setChannelSkuList(convertChannelStoreSkuForAppDTOList(request.getStoreId(), request.getSkuList()));
        changeChannelSkuStatusForAppRequest.setSkuStatus(request.getSkuStatus());
        if (Objects.nonNull(request.getAutoOnSale())) {
            changeChannelSkuStatusForAppRequest.setAutoOnSale(request.getAutoOnSale() == AUTO_ON_SALE_YES);
        }else{
            changeChannelSkuStatusForAppRequest.setAutoOnSale(false);
        }
        changeChannelSkuStatusForAppRequest.setChannelIds(request.getChannelIds());
        changeChannelSkuStatusForAppRequest.setCheckPermit(request.isCheckPermit());

        return changeChannelSkuStatusForAppRequest;
    }

    private static List<ChannelStoreSkuForAppDTO> convertChannelStoreSkuForAppDTOList(Long storeId, List<String> skuList) {
        if (Objects.isNull(skuList)) {
            return Lists.newArrayList();
        }

        List<ChannelStoreSkuForAppDTO> channelStoreSkuForAppDTOList = Lists.newArrayList();
        for (String sku : skuList) {
            ChannelStoreSkuForAppDTO channelStoreSkuForAppDTO = new ChannelStoreSkuForAppDTO();
            channelStoreSkuForAppDTO.setSku(sku);
            channelStoreSkuForAppDTO.setStoreId(storeId);
            channelStoreSkuForAppDTOList.add(channelStoreSkuForAppDTO);
        }
        return channelStoreSkuForAppDTOList;
    }

    public static ChannelSkuForAppRequest convertRequest(FindSkuInfoForAppRequest request, User user) {
        ChannelSkuForAppRequest channelSkuForAppRequest = new ChannelSkuForAppRequest();
        channelSkuForAppRequest.setTenantId(user.getTenantId());
        channelSkuForAppRequest.setStoreId(request.getStoreId());
        channelSkuForAppRequest.setSkuId(request.getSkuId());
        return channelSkuForAppRequest;
    }

    public static SaveOnlinePriceAndStockRequest convertRequest(UpdatePriceAndStockForAppRequest request, User user) {
        SaveOnlinePriceAndStockRequest saveOnlinePriceAndStockRequest = new SaveOnlinePriceAndStockRequest();
        saveOnlinePriceAndStockRequest.setTenantId(user.getTenantId());
        saveOnlinePriceAndStockRequest.setOperatorId(user.getAccountId());
        saveOnlinePriceAndStockRequest.setOperatorName(user.getUsername());
        saveOnlinePriceAndStockRequest.setAccountType(user.getAccountType());
        saveOnlinePriceAndStockRequest.setSkuOnlinePriceAndStocks(convertSkuOnlinePriceAndStockDTOList(request.getStoreId(), request.getSkuPriceAndStocks()));
        if (ANDROID.equalsIgnoreCase(ApiMethodParamThreadLocal.getIdentityInfo().getOs())) {
            saveOnlinePriceAndStockRequest.setSource(DeviceTypeEnum.ANDROID);
        } else if (IOS.equalsIgnoreCase(ApiMethodParamThreadLocal.getIdentityInfo().getOs())) {
            saveOnlinePriceAndStockRequest.setSource(DeviceTypeEnum.IOS);
        }

        return saveOnlinePriceAndStockRequest;
    }

    public static QuoteCommitRequest convertRequest(Long storeId, List<SkuPriceAndStock> skuPriceAndStocks, User user) {
        QuoteCommitRequest request = new QuoteCommitRequest();
        request.setTenantId(user.getTenantId());
        request.setStoreId(storeId);
        request.setQuoterId(user.getAccountId());
        request.setQuoterName(user.getUsername());
        request.setSpuId(skuPriceAndStocks.get(0).getSkuId());
        if (ANDROID.equalsIgnoreCase(ApiMethodParamThreadLocal.getIdentityInfo().getOs())) {
            request.setSource(com.sankuai.meituan.shangou.empower.ocms.client.price.enums.quote.DeviceTypeEnum.ANDROID);
        } else if (IOS.equalsIgnoreCase(ApiMethodParamThreadLocal.getIdentityInfo().getOs())) {
            request.setSource(com.sankuai.meituan.shangou.empower.ocms.client.price.enums.quote.DeviceTypeEnum.IOS);
        }
        request.setQuotePriceList(Lists.newArrayList());
        skuPriceAndStocks.forEach(skuPrice -> {
            QuoteSkuPriceDTO skuPriceDTO = new QuoteSkuPriceDTO();
            skuPriceDTO.setSkuId(skuPrice.getSkuId());
            skuPriceDTO.setQuotePrice(PriceUtils.yuan2Fen(skuPrice.getPrice()));
            request.getQuotePriceList().add(skuPriceDTO);
        });

        return request;
    }

    public static SkuChannelStatusChangeRequest convertRequest(ChangeMultiChannelSkuStatusRequest request, User user) {
        SkuChannelStatusChangeRequest skuChannelStatusChangeRequest = new SkuChannelStatusChangeRequest();
        skuChannelStatusChangeRequest.setTenantId(user.getTenantId());
        skuChannelStatusChangeRequest.setStoreId(request.getStoreId());
        skuChannelStatusChangeRequest.setSkuId(request.getSkuId());
        skuChannelStatusChangeRequest.setChannelStatusChangeParamList(convertSkuChannelStatusChangeParamList(request.getChannelStatusChangeParamList()));
        skuChannelStatusChangeRequest.setOperator(user.getUsername());
        skuChannelStatusChangeRequest.setOperatorId(user.getAccountId());
        if (Objects.nonNull(request.getBizType()) && Objects.nonNull(BizTypeEnum.findByValue(request.getBizType()))) {
            skuChannelStatusChangeRequest.setBizType(BizTypeEnum.findByValue(request.getBizType()));
        }
        skuChannelStatusChangeRequest.setCheckPermit(request.isCheckPermit());
        return skuChannelStatusChangeRequest;
    }

    public static TenantSkuSaveRequest convertRequest(SaveTenantSkuRequest request, User user) {
        TenantSkuSaveRequest tenantSkuSaveRequest = new TenantSkuSaveRequest();
        tenantSkuSaveRequest.setTenantId(user.getTenantId());
        tenantSkuSaveRequest.setTenantSkuInfo(convertTenantSkuDTO(request.getTenantSku(), user));
        tenantSkuSaveRequest.setOperator(user.getUsername());
        tenantSkuSaveRequest.setOperatorId(user.getAccountId());
        if (Objects.nonNull(request.getStoreId())) {
            tenantSkuSaveRequest.setStoreId(request.getStoreId());
        }
        if (Objects.nonNull(request.getBizType()) && Objects.nonNull(BizTypeEnum.findByValue(request.getBizType()))) {
            tenantSkuSaveRequest.setBizType(BizTypeEnum.findByValue(request.getBizType()));
        }
        tenantSkuSaveRequest.setChannelStoreCategoryMap(request.getChannelStoreCategoryMap());

        return tenantSkuSaveRequest;
    }

    public static TenantSkuPageInfoQueryRequest convertRequest(QueryTenantSkuPageInfoRequest request, User user) {
        TenantSkuPageInfoQueryRequest tenantSkuPageInfoQueryRequest = new TenantSkuPageInfoQueryRequest();
        tenantSkuPageInfoQueryRequest.setTenantId(user.getTenantId());
        tenantSkuPageInfoQueryRequest.setKeyword(request.getKeyword());
        tenantSkuPageInfoQueryRequest.setCategoryCodes(request.getCategoryCodes());
        tenantSkuPageInfoQueryRequest.setPageNum(request.getPageNum());
        tenantSkuPageInfoQueryRequest.setPageSize(request.getPageSize());
        tenantSkuPageInfoQueryRequest.setMarkStoreId(request.getStoreId());
        return tenantSkuPageInfoQueryRequest;
    }

    public static QueryStoreSkuAutoUploadTimeRequest convertRequest(Long storeId){
        QueryStoreSkuAutoUploadTimeRequest queryStoreSkuAutoUploadTimeRequest = new QueryStoreSkuAutoUploadTimeRequest();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        queryStoreSkuAutoUploadTimeRequest.setTenantId(user.getTenantId());
        queryStoreSkuAutoUploadTimeRequest.setStoreId(storeId);
        queryStoreSkuAutoUploadTimeRequest.setOperatorId(user.getAccountId());
        queryStoreSkuAutoUploadTimeRequest.setOperatorName(user.getUsername());
        return queryStoreSkuAutoUploadTimeRequest;
    }

    public static SaveStoreSkuAutoUploadTimeRequest convertRequest(ChangeStoreSkuAutoUploadTimeRequest request){
        SaveStoreSkuAutoUploadTimeRequest saveStoreSkuAutoUploadTimeRequest = new SaveStoreSkuAutoUploadTimeRequest();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        saveStoreSkuAutoUploadTimeRequest.setTenantId(user.getTenantId());
        saveStoreSkuAutoUploadTimeRequest.setStoreId(request.getStoreId());
        saveStoreSkuAutoUploadTimeRequest.setOperatorId(user.getAccountId());
        saveStoreSkuAutoUploadTimeRequest.setOperatorName(user.getUsername());
        saveStoreSkuAutoUploadTimeRequest.setAutoUploadTime(request.getAutoUploadTime());
        return saveStoreSkuAutoUploadTimeRequest;
    }

    public static CreateStoreSkuWithCatRequest convertRequest(CreateStoreSkuReq req) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        CreateStoreSkuWithCatRequest createStoreSkuWithCatRequest = new CreateStoreSkuWithCatRequest();
        createStoreSkuWithCatRequest.setTenantId(user.getTenantId());
        createStoreSkuWithCatRequest.setOperatorId(user.getAccountId());
        createStoreSkuWithCatRequest.setOperator(user.getUsername());
        createStoreSkuWithCatRequest.setStoreSku(convertCreateStoreSkuDTO(req));
        createStoreSkuWithCatRequest.setInfiniteInventory(req.getIsInfiniteStock().equals(1) ? true : false);
        createStoreSkuWithCatRequest.setChannelStoreCategoryMap(req.getChannelStoreCategoryMap());
        if (Objects.nonNull(req.getBizType()) && Objects.nonNull(BizTypeEnum.findByValue(req.getBizType()))) {
            createStoreSkuWithCatRequest.setBizType(BizTypeEnum.findByValue(req.getBizType()));
        }
        createStoreSkuWithCatRequest.setSource(CREATE_STORE_SKU_REQUEST_SOURCE_APP);
        return createStoreSkuWithCatRequest;
    }

    public static StoreSkuSaveRequest convertStoreSkuSaveRequest(CreateStoreSkuReq req) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        StoreSkuSaveRequest storeSkuSaveRequest = new StoreSkuSaveRequest();
        storeSkuSaveRequest.setTenantId(user.getTenantId());
        storeSkuSaveRequest.setStoreId(req.getStoreId());
        storeSkuSaveRequest.setSalePrice(req.getStoreSku().getPrice().intValue());
        if (Objects.nonNull(req.getStoreSku().getStock())) {
            storeSkuSaveRequest.setStock(req.getStoreSku().getStock());
        }
        storeSkuSaveRequest.setTenantSkuInfo(convertTenantSkuDTO(req));
        storeSkuSaveRequest.getTenantSkuInfo().setStoreOnSale(STORE_ON_SALE_ADD);
        storeSkuSaveRequest.setSkipCreateTenantSku(req.getSkipCreateTenantSku().equals(1) ? true : false);
        storeSkuSaveRequest.setOperator(user.getUsername());
        storeSkuSaveRequest.setOperatorId(user.getAccountId());
        storeSkuSaveRequest.setInfiniteInventory(req.getIsInfiniteStock().equals(1) ? true : false);
        storeSkuSaveRequest.setChannelStoreCategoryMap(req.getChannelStoreCategoryMap());
        if (Objects.nonNull(req.getBizType()) && Objects.nonNull(BizTypeEnum.findByValue(req.getBizType()))) {
            storeSkuSaveRequest.setBizType(BizTypeEnum.findByValue(req.getBizType()));
        }
        return storeSkuSaveRequest;
    }

    public static TenantSkuSaveRequest convertTenantSkuSaveRequest(UpdateStoreSkuReq req) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        TenantSkuSaveRequest tenantSkuSaveRequest = new TenantSkuSaveRequest();
        tenantSkuSaveRequest.setTenantId(user.getTenantId());
        tenantSkuSaveRequest.setTenantSkuInfo(convertTenantSkuDTO(req.getStoreSku()));
        tenantSkuSaveRequest.setOperator(user.getUsername());
        tenantSkuSaveRequest.setOperatorId(user.getAccountId());
        if (Objects.nonNull(req.getStoreId())) {
            tenantSkuSaveRequest.setStoreId(req.getStoreId());
        }
        if (Objects.nonNull(req.getBizType()) && Objects.nonNull(BizTypeEnum.findByValue(req.getBizType()))) {
            tenantSkuSaveRequest.setBizType(BizTypeEnum.findByValue(req.getBizType()));
        }
        tenantSkuSaveRequest.setChannelStoreCategoryMap(req.getChannelStoreCategoryMap());

        return tenantSkuSaveRequest;
    }

    public static UpdateStoreSkuWithCatRequest convertRequest(UpdateStoreSkuReq request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        UpdateStoreSkuWithCatRequest updateStoreSkuWithCatRequest = new UpdateStoreSkuWithCatRequest();
        updateStoreSkuWithCatRequest.setTenantId(user.getTenantId());
        updateStoreSkuWithCatRequest.setOperatorId(user.getAccountId());
        updateStoreSkuWithCatRequest.setOperator(user.getUsername());
        updateStoreSkuWithCatRequest.setStoreSku(convertCreateStoreSkuDTO(request));
        updateStoreSkuWithCatRequest.setChannelStoreCategoryMap(request.getChannelStoreCategoryMap());
        if (Objects.nonNull(request.getBizType()) && Objects.nonNull(BizTypeEnum.findByValue(request.getBizType()))) {
            updateStoreSkuWithCatRequest.setBizType(BizTypeEnum.findByValue(request.getBizType()));
        }
        updateStoreSkuWithCatRequest.setSource(CREATE_STORE_SKU_REQUEST_SOURCE_APP);

        return updateStoreSkuWithCatRequest;
    }

    public static QueryStoreSkuInfoRequest convertRequest(QueryStoreSkuInfoV2Req request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        QueryStoreSkuInfoRequest queryStoreSkuInfoRequest = new QueryStoreSkuInfoRequest();
        queryStoreSkuInfoRequest.setTenantId(user.getTenantId());
        queryStoreSkuInfoRequest.setSkuId(request.getSkuId());
        queryStoreSkuInfoRequest.setStoreId(request.getStoreId());
        return queryStoreSkuInfoRequest;
    }

    private static CreateStoreSkuDTO convertCreateStoreSkuDTO(CreateStoreSkuReq req) {
        CreateStoreSkuDTO createStoreSkuDTO = new CreateStoreSkuDTO();
        createStoreSkuDTO.setSkuId(req.getStoreSku().getSkuId());
        createStoreSkuDTO.setName(req.getStoreSku().getName());
        createStoreSkuDTO.setSpec(req.getStoreSku().getSpec());
        createStoreSkuDTO.setUpcList(req.getStoreSku().getUpcList());
        if (Objects.nonNull(req.getStoreSku().getWeight())) {
            createStoreSkuDTO.setWeight(req.getStoreSku().getWeight());
        }

        createStoreSkuDTO.setBrandCode(req.getStoreSku().getBrandCode());
        createStoreSkuDTO.setCategoryCode(req.getStoreSku().getCategoryCode());
        if (Objects.nonNull(req.getStoreSku().getWeightType())) {
            createStoreSkuDTO.setWeightType(req.getStoreSku().getWeightType());
        }
        if (Objects.nonNull(req.getStoreSku().getSkuType())) {
            createStoreSkuDTO.setSkuType(req.getStoreSku().getSkuType());
        }
        createStoreSkuDTO.setImageList(req.getStoreSku().getImageList());
        createStoreSkuDTO.setPrice(req.getStoreSku().getPrice());
        createStoreSkuDTO.setUnit(req.getStoreSku().getUnit());
        if (Objects.nonNull(req.getStoreSku().getStock())) {
            createStoreSkuDTO.setStock(req.getStoreSku().getStock());
        }
        createStoreSkuDTO.setJdCategoryCode(req.getStoreSku().getJdCategoryCode());
        createStoreSkuDTO.setJdCategoryName(req.getStoreSku().getJdCategoryName());
        createStoreSkuDTO.setJdBrandCode(req.getStoreSku().getJdBrandCode());
        createStoreSkuDTO.setJdBrandName(req.getStoreSku().getJdBrandName());
        createStoreSkuDTO.setStoreId(req.getStoreId());

        createStoreSkuDTO.setMtCategoryCode(req.getStoreSku().getMtCategoryCode());
        createStoreSkuDTO.setMtCategoryName(req.getStoreSku().getMtCategoryName());

        if (Objects.nonNull(req.getStoreSku().getAvailableTimes()) && req.getStoreSku().getAvailableTimes().size() > 0) {
            Map<Integer, List<TimeSlotDTO>> timeSlotMap = new HashMap<>();
            for(Map.Entry<Integer, List<TimeSlotVO>> entry : req.getStoreSku().getAvailableTimes().entrySet()){
                if(Objects.nonNull(entry.getKey()) && CollectionUtils.isNotEmpty(entry.getValue())){
                    timeSlotMap.put(entry.getKey(), entry.getValue().stream().map(TimeSlotVO::buildTimeSlotDTO).collect(Collectors.toList()));
                }
            }
            createStoreSkuDTO.setAvailableTimes(timeSlotMap);
        }
        if(Objects.nonNull(req.getStoreSku().getSpecialty())){
            createStoreSkuDTO.setSpecialty(req.getStoreSku().getSpecialty());
        }
        createStoreSkuDTO.setDescription(req.getStoreSku().getDescription());
        if(CollectionUtils.isNotEmpty(req.getStoreSku().getProperties())){
            createStoreSkuDTO.setProperties(req.getStoreSku().getProperties().stream().map(StoreSkuPropertyVO::buildStoreSkuPropertyDTO).collect(Collectors.toList()));
        }
        return createStoreSkuDTO;
    }

    private static CreateStoreSkuDTO convertCreateStoreSkuDTO(UpdateStoreSkuReq req) {
        CreateStoreSkuDTO createStoreSkuDTO = new CreateStoreSkuDTO();
        createStoreSkuDTO.setSkuId(req.getStoreSku().getSkuId());
        createStoreSkuDTO.setName(req.getStoreSku().getName());
        createStoreSkuDTO.setSpec(req.getStoreSku().getSpec());
        createStoreSkuDTO.setUpcList(req.getStoreSku().getUpcList());
        if (Objects.nonNull(req.getStoreSku().getWeight())) {
            createStoreSkuDTO.setWeight(req.getStoreSku().getWeight());
        }
        createStoreSkuDTO.setBrandCode(req.getStoreSku().getBrandCode());
        createStoreSkuDTO.setCategoryCode(req.getStoreSku().getCategoryCode());
        if (Objects.nonNull(req.getStoreSku().getWeightType())) {
            createStoreSkuDTO.setWeightType(req.getStoreSku().getWeightType());
        }
        if (Objects.nonNull(req.getStoreSku().getSkuType())) {
            createStoreSkuDTO.setSkuType(req.getStoreSku().getSkuType());
        }
        createStoreSkuDTO.setImageList(req.getStoreSku().getImageList());
        if (Objects.nonNull(req.getStoreSku().getPrice())) {
            createStoreSkuDTO.setPrice(req.getStoreSku().getPrice());
        }
        createStoreSkuDTO.setUnit(req.getStoreSku().getUnit());
        if (Objects.nonNull(req.getStoreSku().getStock())) {
            createStoreSkuDTO.setStock(req.getStoreSku().getStock());
        }
        createStoreSkuDTO.setJdCategoryCode(req.getStoreSku().getJdCategoryCode());
        createStoreSkuDTO.setJdCategoryName(req.getStoreSku().getJdCategoryName());
        createStoreSkuDTO.setJdBrandCode(req.getStoreSku().getJdBrandCode());
        createStoreSkuDTO.setJdBrandName(req.getStoreSku().getJdBrandName());
        createStoreSkuDTO.setStoreId(req.getStoreId());

        if (Objects.nonNull(req.getStoreSku().getAvailableTimes()) && req.getStoreSku().getAvailableTimes().size() > 0) {
            Map<Integer, List<TimeSlotDTO>> timeSlotMap = new HashMap<>();
            for(Map.Entry<Integer, List<TimeSlotVO>> entry : req.getStoreSku().getAvailableTimes().entrySet()){
                if(Objects.nonNull(entry.getKey()) && CollectionUtils.isNotEmpty(entry.getValue())){
                    timeSlotMap.put(entry.getKey(), entry.getValue().stream().map(TimeSlotVO::buildTimeSlotDTO).collect(Collectors.toList()));
                }
            }
            createStoreSkuDTO.setAvailableTimes(timeSlotMap);
        }
        if(Objects.nonNull(req.getStoreSku().getSpecialty())){
            createStoreSkuDTO.setSpecialty(req.getStoreSku().getSpecialty());
        }
        createStoreSkuDTO.setDescription(req.getStoreSku().getDescription());
        if(CollectionUtils.isNotEmpty(req.getStoreSku().getProperties())){
            createStoreSkuDTO.setProperties(req.getStoreSku().getProperties().stream().map(StoreSkuPropertyVO::buildStoreSkuPropertyDTO).collect(Collectors.toList()));
        }
        return createStoreSkuDTO;
    }


    private static TenantSkuDTO convertTenantSkuDTO(TenantSku tenantSku, User user) {
        TenantSkuDTO tenantSkuDTO = new TenantSkuDTO();
        tenantSkuDTO.setSource(SkuOperateSourceType.APP.getValue());
        tenantSkuDTO.setTenantId(user.getTenantId());
        tenantSkuDTO.setSkuId(tenantSku.getSkuId());
        tenantSkuDTO.setSkuName(tenantSku.getSkuName());
        tenantSkuDTO.setUpcList(tenantSku.getUpcList());
        tenantSkuDTO.setImageUrls(tenantSku.getImageUrls());
        tenantSkuDTO.setSpec(tenantSku.getSpec());
        tenantSkuDTO.setBrandCode(tenantSku.getBrandCode());
        tenantSkuDTO.setBrandName(tenantSku.getBrandName());
        tenantSkuDTO.setBrandCodePath(tenantSku.getBrandCodePath());
        tenantSkuDTO.setBrandNamePath(tenantSku.getBrandNamePath());
        tenantSkuDTO.setCategoryCode(tenantSku.getCategoryCode());
        tenantSkuDTO.setCategoryCodePath(tenantSku.getCategoryCodePath());
        tenantSkuDTO.setCategoryName(tenantSku.getCategoryName());
        tenantSkuDTO.setCategoryNamePath(tenantSku.getCategoryNamePath());
        tenantSkuDTO.setBasicSalePrice(tenantSku.getBasicSalePrice());
        tenantSkuDTO.setUnit(tenantSku.getUnit());
        if (Objects.nonNull(tenantSku.getWeight())) {
            tenantSkuDTO.setWeight(tenantSku.getWeight());
        }
        if (Objects.nonNull(tenantSku.getWeightType())) {
            tenantSkuDTO.setWeightType(tenantSku.getWeightType());
        }
        if (Objects.nonNull(tenantSku.getSkuType())) {
            tenantSkuDTO.setSkuType(tenantSku.getSkuType());
        }
        tenantSkuDTO.setJdBrandId(tenantSku.getJdBrandId());
        tenantSkuDTO.setJdBrandName(tenantSku.getJdBrandName());
        tenantSkuDTO.setJdCategoryId(tenantSku.getJdCategoryId());
        tenantSkuDTO.setJdCategoryName(tenantSku.getJdCategoryName());
        return tenantSkuDTO;
    }


    private static List<SkuChannelStatusChangeParam> convertSkuChannelStatusChangeParamList(List<ChannelStatusChangeParamVO> channelStatusChangeParamVOList) {
        if (Objects.isNull(channelStatusChangeParamVOList)) {
            return Lists.newArrayList();
        }

        List<SkuChannelStatusChangeParam> skuChannelStatusChangeParamList = Lists.newArrayList();
        for (ChannelStatusChangeParamVO channelStatusChangeParamVO : channelStatusChangeParamVOList) {
            SkuChannelStatusChangeParam skuChannelStatusChangeParam = new SkuChannelStatusChangeParam();
            skuChannelStatusChangeParam.setChannelId(channelStatusChangeParamVO.getChannelId());
            skuChannelStatusChangeParam.setFrontCategoryCode(channelStatusChangeParamVO.getFrontCategoryId());
            skuChannelStatusChangeParam.setSkuChannelStatus(channelStatusChangeParamVO.getSkuChannelStatus());
            skuChannelStatusChangeParamList.add(skuChannelStatusChangeParam);
        }
        return skuChannelStatusChangeParamList;
    }

    private static List<SkuOnlinePriceAndStockDTO> convertSkuOnlinePriceAndStockDTOList(Long storeId, List<SkuPriceAndStock> skuPriceAndStockList) {
        if (Objects.isNull(skuPriceAndStockList)) {
            return Lists.newArrayList();
        }

        List<SkuOnlinePriceAndStockDTO> skuOnlinePriceAndStockDTOList = Lists.newArrayList();
        for (SkuPriceAndStock skuPriceAndStock : skuPriceAndStockList) {
            SkuOnlinePriceAndStockDTO skuOnlinePriceAndStockDTO = new SkuOnlinePriceAndStockDTO();
            skuOnlinePriceAndStockDTO.setStoreId(storeId);
            skuOnlinePriceAndStockDTO.setSkuId(skuPriceAndStock.getSkuId());
            if (Objects.nonNull(skuPriceAndStock.getPrice())) {
                skuOnlinePriceAndStockDTO.setPrice(skuPriceAndStock.getPrice());
            }
            if (Objects.nonNull(skuPriceAndStock.getStock())) {
                skuOnlinePriceAndStockDTO.setStock(skuPriceAndStock.getStock());
            }
            skuOnlinePriceAndStockDTO.setChannelId(skuPriceAndStock.getChannelId());
            skuOnlinePriceAndStockDTO.setAutoResumeInfiniteStock(skuPriceAndStock.getAutoResumeInfiniteStock());
            skuOnlinePriceAndStockDTO.setCustomizeStockFlag(skuPriceAndStock.getCustomizeStockFlag());
            skuOnlinePriceAndStockDTOList.add(skuOnlinePriceAndStockDTO);

        }

        return skuOnlinePriceAndStockDTOList;
    }

    public static GetStoreSkuStockSetTypeRequest convertRequest(User user) {
        GetStoreSkuStockSetTypeRequest getStoreSkuStockSetTypeRequest = new GetStoreSkuStockSetTypeRequest();
        getStoreSkuStockSetTypeRequest.setTenantId(user.getTenantId());
        return getStoreSkuStockSetTypeRequest;
    }

    public static QueryChannelStoreInfoRequest convertRequest(GetSystemRequest getSystemRequest) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        QueryChannelStoreInfoRequest request = new QueryChannelStoreInfoRequest();
        request.setTenantId(user.getTenantId());
        List<Long> storeIds = Lists.newArrayList();
        storeIds.add(getSystemRequest.getStoreId());
        request.setStoreIds(storeIds);
        return request;
    }

    public static List<ChannelInfoVO> convertChannelInfoVOList(Map<Long, List<ChannelStoreDTO>> channelStoreMap) {
        if (MapUtils.isEmpty(channelStoreMap)) {
            return Lists.newArrayList();
        }

        Map<Integer, String> channelNameMap = Maps.newHashMap();
        channelNameMap.put(-1, "线下");
        channelNameMap.put(100, "美团");
        channelNameMap.put(200, "饿了么");
        channelNameMap.put(300, "京东到家");


        List<ChannelInfoVO> channelInfoVOList = Lists.newArrayList();

        for (Map.Entry<Long, List<ChannelStoreDTO>> entry : channelStoreMap.entrySet()) {
            for (ChannelStoreDTO channelStoreDTO : entry.getValue()) {
                ChannelInfoVO channelInfoVO = new ChannelInfoVO();
                channelInfoVO.setId(channelStoreDTO.getChannel());
                channelInfoVO.setName(channelNameMap.get(channelStoreDTO.getChannel()));
                channelInfoVOList.add(channelInfoVO);
            }
        }

        return channelInfoVOList;
    }


    public static List<ChannelInfoVO> convertChannelInfoVOList(List<Channel> channels) {
        if (Objects.isNull(channels)) {
            return Lists.newArrayList();
        }

        List<ChannelInfoVO> channelInfoVOList = Lists.newArrayList();

        for (Channel channel : channels) {
            ChannelInfoVO channelInfoVO = new ChannelInfoVO();
            channelInfoVO.setId(channel.getId());
            channelInfoVO.setName(channel.getName());
            channelInfoVOList.add(channelInfoVO);
        }

        return channelInfoVOList;
    }

    public static GetChannelBrandResponse convertGetChannelBrandResponse(ChannelBrandPageResponse channelBrandPageResponse) {
        GetChannelBrandResponse response = new GetChannelBrandResponse();
        response.setBrandList(convertChannelBrandVOList(channelBrandPageResponse.getBrandList()));
        response.setPageInfo(convert(channelBrandPageResponse.getPageInfo()));
        return response;
    }

    private static List<ChannelBrandVO> convertChannelBrandVOList(List<ChannelBrandDTO> channelBrandDTOList) {
        List<ChannelBrandVO> channelBrandVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(channelBrandDTOList)) {
            channelBrandDTOList.forEach(channelBrand -> {
                ChannelBrandVO channelBrandVO = new ChannelBrandVO();
                channelBrandVO.setBrandCode(channelBrand.getBrandId());
                channelBrandVO.setZhName(channelBrand.getBrandName());
                channelBrandVO.setChannelId(channelBrand.getChannel());
                channelBrandVOList.add(channelBrandVO);
            });
        }
        return channelBrandVOList;
    }

    public static GetChannelCategoryResponse convertGetChannelCategoryResponse(ChannelCategoryResponse channelCategoryResponse) {
        GetChannelCategoryResponse response = new GetChannelCategoryResponse();
        response.setCategoryList(convertChannelCategoryVOList(channelCategoryResponse.getCategoryList()));
        return response;
    }

    private static List<ChannelCategoryVO> convertChannelCategoryVOList(List<ChannelCategoryDTO> channelCategoryDTOList) {
        List<ChannelCategoryVO> channelCategoryVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(channelCategoryDTOList)) {
            channelCategoryDTOList.forEach(channelCategory -> {
                ChannelCategoryVO channelCategoryVO = new ChannelCategoryVO();
                channelCategoryVO.setSortCode(channelCategory.getCategoryId());
                channelCategoryVO.setParentSortCode(channelCategory.getParentId());
                channelCategoryVO.setSortName(channelCategory.getCategoryName());
                channelCategoryVO.setDepth(channelCategory.getDepth());
                channelCategoryVO.setSubAmount(channelCategory.getSubAmount());
                channelCategoryVOList.add(channelCategoryVO);
            });
        }
        return channelCategoryVOList;
    }

    public static Integer convertChannel2OrderBizType(Integer channelId) {
        if (channelId != null) {
            return ChannelOrderConvertUtils.convertBizType(channelId);
        }
        return null;
    }

    public static Integer convertOrderBizType2ChannelId(Integer orderBizType) {
        if (orderBizType != null) {
            return ChannelOrderConvertUtils.convertChannelId(orderBizType);
        }
        return null;
    }

    public static List<com.sankuai.shangou.qnh.orderapi.domain.vo.store.order.ChannelInfoVO> buildChannelAbbreviationInfoVOList(List<Channel> channelList) {
        if (CollectionUtils.isEmpty(channelList)) {
            return Lists.newArrayList();
        }
        String codes = MccConfigUtil.getOrderExcludeChannelCodes();
        List<com.sankuai.shangou.qnh.orderapi.domain.vo.store.order.ChannelInfoVO> channelInfoVOList = Lists.newArrayList();
        Map<Integer, String> channelAbbreviationMap = getChannelAbbreviationMap();
        for (Channel channel : channelList) {
            if(codes.contains(String.valueOf(channel.getId()))){
                continue;
            }
            com.sankuai.shangou.qnh.orderapi.domain.vo.store.order.ChannelInfoVO channelInfoVO = new com.sankuai.shangou.qnh.orderapi.domain.vo.store.order.ChannelInfoVO();
            channelInfoVO.setCode(channel.getCode());
            channelInfoVO.setId(channel.getId());
            channelInfoVO.setName(channel.getName());
            channelInfoVO.setAbbreviation(channelAbbreviationMap.containsKey(String.valueOf(channel.getId())) ? channelAbbreviationMap.get(String.valueOf(channel.getId())) : channel.getName());
            channelInfoVOList.add(channelInfoVO);
        }
        return channelInfoVOList;
    }

    private static Map<Integer, String> getChannelAbbreviationMap() {
        String channelAbbreviation = MccConfigUtil.getChannelAbbreviation();
        Map<Integer, String> channelAbbreviationMap = Maps.newHashMap();
        try {
            channelAbbreviationMap = JacksonUtils.fromJson(channelAbbreviation, new TypeReference<Map<Integer, String>>() {
            });
        } catch (Exception e) {
            log.warn("OCMSOrderServiceWrapper.getChannelAbbreviationMap  解析渠道简称配置错误", e);
        }
        return channelAbbreviationMap;
    }


    public static GetPictureByUpcRequest convertRequest(QueryPictureByUpcRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        GetPictureByUpcRequest getPictureByUpcRequest = new GetPictureByUpcRequest();
        getPictureByUpcRequest.setTenantId(user.getTenantId());
        getPictureByUpcRequest.setUpc(request.getUpc());
        return getPictureByUpcRequest;
    }

    public static PictureByUpcResponse convertGetPictureByUpcResponse(GetPictureByUpcResponse response) {
        PictureByUpcResponse pictureByUpcResponse = new PictureByUpcResponse();
        pictureByUpcResponse.setPictureUrl(response.getPictureUrl());
        return pictureByUpcResponse;
    }

    public static GetPicturePageInfoByNameRequest convertRequest(PicturePageInfoByNameQueryRequest request) {
        GetPicturePageInfoByNameRequest getPicturePageInfoByNameRequest = new GetPicturePageInfoByNameRequest();
        getPicturePageInfoByNameRequest.setTenantId(request.getTenantId());
        getPicturePageInfoByNameRequest.setPageNum(request.getPageNum());
        getPicturePageInfoByNameRequest.setPageSize(request.getPageSize());
        getPicturePageInfoByNameRequest.setSkuName(request.getSkuName());
        return getPicturePageInfoByNameRequest;
    }

    public static PicturePageInfoByNameResponse convertGetPicturePageInfoByNameResponse(GetPicturePageInfoByNameResponse response) {
        PicturePageInfoByNameResponse picturePageInfoByNameResponse = new PicturePageInfoByNameResponse();
        picturePageInfoByNameResponse.setPageInfo(convert(response.getPageInfo()));
        picturePageInfoByNameResponse.setPicList(response.getPictureUrls());
        return picturePageInfoByNameResponse;
    }

    public static GetSkuPageInfoBySkuNameRequest convertRequest(QuerySkuPageInfoBySkuNameRequest request){
        GetSkuPageInfoBySkuNameRequest getSkuPageInfoBySkuNameRequest = new GetSkuPageInfoBySkuNameRequest();
        getSkuPageInfoBySkuNameRequest.setTenantId(request.getTenantId());
        getSkuPageInfoBySkuNameRequest.setSkuName(request.getSkuName());
        getSkuPageInfoBySkuNameRequest.setPageNum(request.getPageNo());
        getSkuPageInfoBySkuNameRequest.setPageSize(request.getPageSize());
        return getSkuPageInfoBySkuNameRequest;
    }


    /**
     * 根据标品名称分页查询标品信息
     * @param response
     * @return
     */
    public static QuerySkuPageInfoBySkuNameResponse convertQuerySkuPageInfoBySkuNameResponse(GetSkuPageInfoBySkuNameResponse response){
        QuerySkuPageInfoBySkuNameResponse querySkuPageInfoBySkuNameReponse = new QuerySkuPageInfoBySkuNameResponse();
        querySkuPageInfoBySkuNameReponse.setPageInfo(convert(response.getPageInfo()));
        if (CollectionUtils.isNotEmpty(response.getSkuInfos())){

            List<SkuBasicInfoVo> skuBasicInfoVos = new ArrayList<>();
            response.getSkuInfos().forEach(spSkuInfo -> {
                SkuBasicInfoVo skuBasicInfoVo = convert(spSkuInfo);
                if (null != skuBasicInfoVo){
                    skuBasicInfoVos.add(skuBasicInfoVo);
                }
            });
            querySkuPageInfoBySkuNameReponse.setSkuLIst(skuBasicInfoVos);
        }
        return querySkuPageInfoBySkuNameReponse;
    }


    public static BatchGetSkuInfoByUpcListRequest convertRequest(BatchQuerySkuInfoByUpcListRequest request){
        BatchGetSkuInfoByUpcListRequest batchGetSkuInfoByUpcListRequest = new BatchGetSkuInfoByUpcListRequest();
        batchGetSkuInfoByUpcListRequest.setTenantId(request.getTenantId());
        if (CollectionUtils.isEmpty(request.getUpcList())){
            batchGetSkuInfoByUpcListRequest.setUpcList(Collections.emptyList());
            return batchGetSkuInfoByUpcListRequest;
        }
        batchGetSkuInfoByUpcListRequest.setUpcList(request.getUpcList());
        return batchGetSkuInfoByUpcListRequest;
    }

    public static BatchQuerySkuInfoByUpcListResponse convertBatchQuerySkuInfoByUpcListResponse(BatchGetSkuInfoByUpcListResponse response){

        BatchQuerySkuInfoByUpcListResponse batchQuerySkuInfoByUpcListResponse = new BatchQuerySkuInfoByUpcListResponse();
        if (CollectionUtils.isEmpty(response.getSkuInfos())){
            batchQuerySkuInfoByUpcListResponse.setSkuList(Collections.emptyList());
            return batchQuerySkuInfoByUpcListResponse;
        }
        List<SkuBasicInfoVo> skuBasicInfoVos = new ArrayList<>();
        response.getSkuInfos().forEach(spSkuInfo -> {
            SkuBasicInfoVo skuBasicInfoVo = convert(spSkuInfo);
            if (null != skuBasicInfoVo){
                skuBasicInfoVos.add(skuBasicInfoVo);
            }
        });
        batchQuerySkuInfoByUpcListResponse.setSkuList(skuBasicInfoVos);
        return batchQuerySkuInfoByUpcListResponse;
    }


    private static SkuBasicInfoVo convert(SpSkuInfo spSkuInfo){

        if (null == spSkuInfo){
            return null;
        }
        SkuBasicInfoVo skuBasicInfoVo = new SkuBasicInfoVo();
        skuBasicInfoVo.setName(spSkuInfo.getSkuName());
        skuBasicInfoVo.setUpc(spSkuInfo.getUpc());
        skuBasicInfoVo.setSpec(spSkuInfo.getSpec());
        skuBasicInfoVo.setUnit(spSkuInfo.getUnit());
        skuBasicInfoVo.setPic(spSkuInfo.getPicUrl());
        skuBasicInfoVo.setWeight(spSkuInfo.getWeight());
        skuBasicInfoVo.setWeightForUnit(spSkuInfo.getWeightForUnit());
        skuBasicInfoVo.setWeightUnit(spSkuInfo.getWeightUnit());
        skuBasicInfoVo.setCategoryName(spSkuInfo.getCategoryName());
        skuBasicInfoVo.setBrandId(spSkuInfo.getBrandId());
        skuBasicInfoVo.setBrandName(spSkuInfo.getBrandName());
        skuBasicInfoVo.setOriginName(spSkuInfo.getOriginName());
        return skuBasicInfoVo;

    }


    public static CdqStoreSkuDetailQueryRequest convertRequest(QueryCdqStoreSkuDetailRequest queryCdqStoreSkuDetailRequest, User user) {
        CdqStoreSkuDetailQueryRequest request = new CdqStoreSkuDetailQueryRequest();
        request.setTenantId(user.getTenantId());
        request.setStoreId(queryCdqStoreSkuDetailRequest.getStoreId());
        request.setSkuId(queryCdqStoreSkuDetailRequest.getStoreSkuId());
        return request;
    }

    public static QueryStoreOnlineSkuDetailResponse convertQueryStoreOnlineSkuDetailResponse(CdqStoreSkuDetailQueryResponse response){

        QueryStoreOnlineSkuDetailResponse queryStoreOnlineSkuDetailResponse = new QueryStoreOnlineSkuDetailResponse();
        if (response.getStoreSku() == null){
            return queryStoreOnlineSkuDetailResponse;
        }
        CdqStoreSkuWithChannelInfoDTO cdqStoreSkuWithChannelInfoDTO = response.getStoreSku();

        CdqStoreSkuDTO cdqStoreSkuDTO = cdqStoreSkuWithChannelInfoDTO.getStoreSkuInfo();
        CdqStoreSkuVo cdqStoreSkuVo = new CdqStoreSkuVo(cdqStoreSkuDTO);

        List<ChannelSkuDetailInfoDTO> channelSkuDetailInfoDTOList = cdqStoreSkuWithChannelInfoDTO.getChannelSkuInfoList();
        List<ChannelSkuDetailInfoVo> channelSkuDetailInfoVoList = convertChannelSkuDetailInfoVoList(channelSkuDetailInfoDTOList);

        CdqStoreSkuWithChannelInfoVo cdqStoreSkuWithChannelInfoVo = new CdqStoreSkuWithChannelInfoVo();
        cdqStoreSkuWithChannelInfoVo.setCdqStoreSkuVo(cdqStoreSkuVo);
        cdqStoreSkuWithChannelInfoVo.setChannelSkuDetailInfoVos(channelSkuDetailInfoVoList);

        queryStoreOnlineSkuDetailResponse.setStoreSku(cdqStoreSkuWithChannelInfoVo);
        return queryStoreOnlineSkuDetailResponse;
    }

    public static CdqStoreSkuSaveRequest convertRequest(SaveCdqStoreSkuRequest saveCdqStoreSkuRequest, User user) throws Exception {
        CdqStoreSkuSaveRequest request = new CdqStoreSkuSaveRequest();
        request.setSaveType(saveCdqStoreSkuRequest.getSaveType());
        request.setStoreId(saveCdqStoreSkuRequest.getStoreId());
        request.setTenantId(saveCdqStoreSkuRequest.getTenantId());
        request.setOperatorId(user.getAccountId());
        request.setOperatorName(user.getUsername());
        request.setOperateSource(OperateSourceEnum.EMPOWER_ASSISTENT_APP);
        if (saveCdqStoreSkuRequest.getInfiniteInventory() != null){
            request.setInfiniteInventory(saveCdqStoreSkuRequest.getInfiniteInventory());
        }

        CdqStoreSkuWithChannelInfoDTO cdqStoreSkuWithChannelInfoDTO = new CdqStoreSkuWithChannelInfoDTO();

        CdqStoreSkuWithChannelInfoVo cdqStoreSkuWithChannelInfoVo = saveCdqStoreSkuRequest.getCdqStoreSkuWithChannelInfoVo();
        CdqStoreSkuVo cdqStoreSkuVo = cdqStoreSkuWithChannelInfoVo.getCdqStoreSkuVo();
        List<ChannelSkuDetailInfoVo> channelSkuDetailInfoVoList = cdqStoreSkuWithChannelInfoVo.getChannelSkuDetailInfoVos();

        if (SaveType.SAVE_TYPE.getValue() == saveCdqStoreSkuRequest.getSaveType()
                && (Double.compare(cdqStoreSkuVo.getStorePrice(), 0) < 0 || Double.compare(cdqStoreSkuVo.getStorePrice(), 30000.00) > 0)){
            log.error("创建门店商品时，CdqStoreSkuVo storePrice价格范围需要满足[0-30000.00]");
            throw new Exception("创建门店商品时，CdqStoreSkuVo storePrice参数不合法");
        }
        CdqStoreSkuDTO cdqStoreSkuDTO = cdqStoreSkuVo.buildCdqStoreSkuDTO();
        cdqStoreSkuDTO.setStorePrice(cdqStoreSkuVo.getStorePrice() == null ? 0 : cdqStoreSkuVo.getStorePrice());
        cdqStoreSkuDTO.setTenantId(request.getTenantId());
        cdqStoreSkuDTO.setStoreId(request.getStoreId());
        List<ChannelSkuDetailInfoDTO> channelSkuDetailInfoDTOList = convertChannelSkuDetailInfoDTOList(channelSkuDetailInfoVoList);

        cdqStoreSkuWithChannelInfoDTO.setStoreSkuInfo(cdqStoreSkuDTO);
        cdqStoreSkuWithChannelInfoDTO.setChannelSkuInfoList(channelSkuDetailInfoDTOList);
        request.setStoreSkuWithChannelInfo(cdqStoreSkuWithChannelInfoDTO);

        return request;

    }



    private static List<ChannelSkuDetailInfoVo> convertChannelSkuDetailInfoVoList(List<ChannelSkuDetailInfoDTO> channelSkuDetailInfoDTOList){

        List<ChannelSkuDetailInfoVo> channelSkuDetailInfoVoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(channelSkuDetailInfoDTOList)){
            return channelSkuDetailInfoVoList;
        }
        for (ChannelSkuDetailInfoDTO channelSkuDetailInfoDTO : channelSkuDetailInfoDTOList){
            channelSkuDetailInfoVoList.add(new ChannelSkuDetailInfoVo(channelSkuDetailInfoDTO));
        }
        return channelSkuDetailInfoVoList;
    }




    private static List<ChannelSkuDetailInfoDTO> convertChannelSkuDetailInfoDTOList(List<ChannelSkuDetailInfoVo> channelSkuDetailInfoVoList){

        List<ChannelSkuDetailInfoDTO> channelSkuDetailInfoDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(channelSkuDetailInfoVoList)){
            return channelSkuDetailInfoDTOList;
        }
        for (ChannelSkuDetailInfoVo channelSkuDetailInfoVo : channelSkuDetailInfoVoList){
            channelSkuDetailInfoDTOList.add(channelSkuDetailInfoVo.buildChannelSkuDetailInfoDTO());
        }
        return channelSkuDetailInfoDTOList;
    }


    public static CdqSuggestStoreSkuRequest convertRequest(CdqSuggestStoreSkuQueryRequest cdqSuggestStoreSkuQueryRequest, User user) {

        CdqSuggestStoreSkuRequest request = new CdqSuggestStoreSkuRequest();
        request.setTenantId(user.getTenantId());
        request.setSkuName(cdqSuggestStoreSkuQueryRequest.getSkuName());
        request.setTargetStoreId(cdqSuggestStoreSkuQueryRequest.getStoreId());
        request.setPageNo(cdqSuggestStoreSkuQueryRequest.getPageNo());
        request.setPageSize(cdqSuggestStoreSkuQueryRequest.getPageSize());
        return request;

    }


    public static CdqSuggestStoreSkuListQueryResponse convertCdqSuggestStoreSkuListResponse(CdqSuggestStoreSkuListResponse cdqSuggestStoreSkuListResponse){

        CdqSuggestStoreSkuListQueryResponse response = new CdqSuggestStoreSkuListQueryResponse();
        List<CdqSuggestStoreSkuVo> cdqSuggestStoreSkuVoList = new ArrayList<>();
        if (ResultCodeEnum.SUCCESS.getValue() == cdqSuggestStoreSkuListResponse.getCode() && CollectionUtils.isNotEmpty(cdqSuggestStoreSkuListResponse.getSuggestStoreSku())){
            for (CdqSuggestStoreSkuDTO cdqSuggestStoreSkuDTO : cdqSuggestStoreSkuListResponse.getSuggestStoreSku()){
                cdqSuggestStoreSkuVoList.add(new CdqSuggestStoreSkuVo(cdqSuggestStoreSkuDTO));
            }
        }
        response.setSuggestStoreSku(cdqSuggestStoreSkuVoList);
        PageInfoVO pageInfoVO = convert(cdqSuggestStoreSkuListResponse.getPageInfo());
        if (null != pageInfoVO){
            response.setPageInfo(pageInfoVO);
        }
        return response;

    }


    public static List<FirstCategoryWithSkuCountVO> convert2FirstCategoryWithSkuCountVOList(List<CategoryWithSkuCountDTO> firstCategoryWithSkuCountDTOList) {
        if (CollectionUtils.isEmpty(firstCategoryWithSkuCountDTOList)) {
            return Collections.emptyList();
        }

        List<FirstCategoryWithSkuCountVO> firstCategoryWithSkuCountVOList = Lists.newArrayList();
        for (CategoryWithSkuCountDTO dto : firstCategoryWithSkuCountDTOList) {
            FirstCategoryWithSkuCountVO vo = new FirstCategoryWithSkuCountVO();
            vo.setFirstCategoryCode(dto.getCategoryCode());
            vo.setFirstCategoryName(dto.getCategoryName());
            vo.setSkuCount((long)dto.getSkuCount());
            firstCategoryWithSkuCountVOList.add(vo);
        }

        return firstCategoryWithSkuCountVOList;
    }

    public static List<Integer> toAuditStatusList(Integer mtAllowSale){
        return ChannelAuditStatusEnum.ofAllowSale(mtAllowSale).stream()
                .map(ChannelAuditStatusEnum::getCode).collect(Collectors.toList());
    }

    public static ChannelCategoryRelationVO convertQueryChannelCategoryResponse(QueryChannelCategoryResponse response) {
        ChannelCategoryRelationVO relationVO  = new ChannelCategoryRelationVO();
        List<ChannelCategoryWithPathVO> categoryVOList = Lists.newArrayList();

        if (ResultCodeEnum.SUCCESS.getValue() == response.getCode() && Objects.nonNull(response.getChannelCategoryInfo())){
            CategoryBindChannelCategoryDTO channelCategoryInfo = response.getChannelCategoryInfo();
            Map<Integer, ChannelCategoryWithPathDTO> channelCategoryMap = channelCategoryInfo.getChannelCategoryMap();
            if (!channelCategoryMap.isEmpty()) {
                channelCategoryMap.entrySet().forEach(entry -> {
                    Integer channelId = entry.getKey();
                    ChannelCategoryWithPathDTO value = entry.getValue();
                    ChannelCategoryWithPathVO vo = new ChannelCategoryWithPathVO();
                    vo.setChannelId(channelId);
                    vo.setChannelCategoryCode(value.getChannelCategoryCode());
                    vo.setChannelCategoryName(value.getChannelCategoryName());
                    vo.setChannelCategoryCodePath(value.getChannelCategoryCodePath());
                    vo.setChannelCategoryNamePath(value.getChannelCategoryNamePath());
                    categoryVOList.add(vo);
                });
            }
        }
        relationVO.setCategoryVOList(categoryVOList);

        return relationVO;
    }

    /**
     * List类型转换
     * @param srcList
     * @param mapFunction
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F,T> List<T> convertList(List<F> srcList, Function<F,T> mapFunction) {
        return Optional.ofNullable(srcList).map(List::stream).orElse(Stream.empty())
                .filter(Objects::nonNull).map(mapFunction).collect(Collectors.toList());
    }

    public static boolean isPhoneHasMusk(String phoneNo) {
        return StringUtils.contains(phoneNo, PHONE_MUSK);
    }
}
