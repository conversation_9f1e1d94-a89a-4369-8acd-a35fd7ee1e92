package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;
// Copyright (C) 2019 Meituan
// All rights reserved

import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelCommentStatEnum;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.PoiResp;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommentStatBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommentStoreStatBO;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;

import static com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelCommentStatEnum.BAD_COMMENT_COUNT;
import static com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelCommentStatEnum.BAD_COMMENT_RATE;
import static com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelCommentStatEnum.GOOD_COMMENT_COUNT;
import static com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelCommentStatEnum.GOOD_COMMENT_RATE;
import static com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelCommentStatEnum.NOT_REPLY_BAD_COMMENT_COUNT;
import static com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelCommentStatEnum.NOT_REPLY_BAD_COMMENT_RATE;
import static com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelCommentStatEnum.TOTAL_COMMENT_COUNT;


/**
 * @author: <EMAIL>
 * @class: CommentStoreStatVO
 * @date: 2019-07-08 15:24:26
 * @desc:
 */
@Data
public class CommentStoreStatVO {

    /**
     * 门店Id
     */
    private Long poiId;

    /**
     * 门店名称
     */
    private String storeName;

    private String erpId;

    private boolean isErpTenant;

    /**
     * 汇总数据
     */
    private Map<String, String> totalStatistics;

    /**
     * channel2Static
     * return Map<评价标签, Map<渠道Id, 标签数>>
     */
    private Map<String, Map<String, String>> detailStatistics;

    /**
     * 由CommentStoreStatBO构造CommentStoreStatVO
     * @param bo
     * @param poiInfoMap    storeId to StoreName Map
     */
    public CommentStoreStatVO(CommentStoreStatBO bo, Map<Long, PoiResp> poiInfoMap, boolean isErpTenant, boolean isDownload) {
        this.poiId = bo.getPoiId();
        if (MapUtils.isNotEmpty(poiInfoMap) && poiInfoMap.get(bo.getPoiId()) != null) {
            PoiResp poiResp = poiInfoMap.get(bo.getPoiId());
            this.storeName = poiResp.getPoiName();
            if (isErpTenant) {
                if (!isDownload) {
                    this.storeName = "[" + poiResp.getOutPoiId() + "]" + this.storeName;
                }
                this.erpId = poiResp.getOutPoiId();
            }
        }
        this.isErpTenant = isErpTenant;
        Map<Integer, Map<String, String>> channel2Label2Count = new HashMap<>();
        for (Map.Entry<Integer, List<CommentStatBO>> channelInfo : bo.getStatistics().entrySet()) {
            channel2Label2Count.put(channelInfo.getKey(), CommentStatBO.toStatisticsStringMap(channelInfo.getValue()));

            String notReplyBadCommentCount = getCommentCountWithDefault(channel2Label2Count, channelInfo.getKey(), NOT_REPLY_BAD_COMMENT_COUNT);
            String badCommentCount = getCommentCountWithDefault(channel2Label2Count, channelInfo.getKey(), BAD_COMMENT_COUNT);
            String goodCommentCount = getCommentCountWithDefault(channel2Label2Count, channelInfo.getKey(), GOOD_COMMENT_COUNT);
            String totalCommentCount = getCommentCountWithDefault(channel2Label2Count, channelInfo.getKey(), TOTAL_COMMENT_COUNT);

            String notReplyBadCommentRate = badCommentCount.equals("0") ? "0" : stringDiv(notReplyBadCommentCount, badCommentCount);
            String badCommentRate = totalCommentCount.equals("0") ? "0" : stringDiv(badCommentCount, totalCommentCount);
            String goodCommentRate = totalCommentCount.equals("0") ? "0" : stringDiv(goodCommentCount, totalCommentCount);
            channel2Label2Count.get(channelInfo.getKey()).put(NOT_REPLY_BAD_COMMENT_RATE.name(), notReplyBadCommentRate);
            channel2Label2Count.get(channelInfo.getKey()).put(BAD_COMMENT_RATE.name(), badCommentRate);
            channel2Label2Count.get(channelInfo.getKey()).put(GOOD_COMMENT_RATE.name(), goodCommentRate);
        }
        this.detailStatistics = this.toLabel2Channel2Count(channel2Label2Count);
        this.calcTotal();
    }

    private String getCommentCountWithDefault(Map<Integer, Map<String, String>> channel2Label2Count, Integer key, ChannelCommentStatEnum commentStatEnum) {
        String defaultValue = "0";
        if (!channel2Label2Count.containsKey(key)){
            return defaultValue;
        }
        if (Objects.isNull(channel2Label2Count.get(key)) || !channel2Label2Count.get(key).containsKey(commentStatEnum.name())){
            return defaultValue;
        }
        return channel2Label2Count.get(key).get(commentStatEnum.name());
    }


    public void calcTotal() {
        Map<String, String> totalStatistics = new HashMap<>();
        if (MapUtils.isNotEmpty(this.detailStatistics)) {
            for (Map.Entry<String, Map<String, String>> labelInfo : detailStatistics.entrySet()) {
                if (labelInfo.getKey().equals(BAD_COMMENT_RATE.name())
                        || labelInfo.getKey().equals( NOT_REPLY_BAD_COMMENT_RATE.name())
                        || labelInfo.getKey().equals(GOOD_COMMENT_RATE.name())) {
                    continue;
                }
                int labelCount = 0;
                for (Map.Entry<String, String> channelInfo : labelInfo.getValue().entrySet()) {
                    labelCount += Integer.valueOf(channelInfo.getValue());
                }
                totalStatistics.put(labelInfo.getKey(), String.valueOf(labelCount));
            }
            totalStatistics.put(NOT_REPLY_BAD_COMMENT_RATE.name(), totalStatistics.get(BAD_COMMENT_COUNT.name()).equals("0") ? "0" : stringDiv(totalStatistics.get(NOT_REPLY_BAD_COMMENT_COUNT.name()), totalStatistics.get(BAD_COMMENT_COUNT.name())));
            totalStatistics.put(BAD_COMMENT_RATE.name(), totalStatistics.get(TOTAL_COMMENT_COUNT.name()).equals("0") ? "0" : stringDiv(totalStatistics.get(BAD_COMMENT_COUNT.name()), totalStatistics.get(TOTAL_COMMENT_COUNT.name())));
            totalStatistics.put(GOOD_COMMENT_RATE.name(), totalStatistics.get(TOTAL_COMMENT_COUNT.name()).equals("0") ? "0" : stringDiv(totalStatistics.get(GOOD_COMMENT_COUNT.name()), totalStatistics.get(TOTAL_COMMENT_COUNT.name())));
        }
        this.totalStatistics = totalStatistics;
    }


    public HashMap<String, String> getExcelData(Integer sortId) {
        HashMap<String, String> dataMap = new HashMap<>();
        dataMap.put("序号", String.valueOf(sortId));
        dataMap.put("门店名称", this.storeName);
        if (isErpTenant) {
            dataMap.put("erp门店Id", this.erpId);
        }

        for (Map.Entry<String, String> label2Count : this.totalStatistics.entrySet()) {
            dataMap.put(ChannelCommentStatEnum.valueOf(label2Count.getKey()).getDesc(), label2Count.getValue());
        }

        for (Map.Entry<String, Map<String, String>> labelInfo : this.detailStatistics.entrySet()) {
            for (Map.Entry<String, String> channelInfo : labelInfo.getValue().entrySet()) {
                dataMap.put(DynamicChannelType.findOf(Integer.valueOf(channelInfo.getKey())).getDesc() + ChannelCommentStatEnum.valueOf(labelInfo.getKey()).getDesc(), channelInfo.getValue());
            }
        }

        return dataMap;
    }

    public static List<String> getHeaders(List<Integer> channelIds, boolean isErpTenant) {
        Collections.sort(channelIds);
        List<String> headers = Lists.newArrayList("序号", "门店名称");
        if (isErpTenant) {
            headers.add("erp门店Id");
        }

        headers.add("评价总数");
        for (Integer channelId : channelIds) {
            headers.add(DynamicChannelType.findOf(channelId).getDesc() + "评价总数");
        }
        headers.add("好评总数");
        for (Integer channelId : channelIds) {
            headers.add(DynamicChannelType.findOf(channelId).getDesc() + "好评总数");
        }

        headers.add("差评总数");
        for (Integer channelId : channelIds) {
            headers.add(DynamicChannelType.findOf(channelId).getDesc() + "差评总数");
        }

        headers.add("差评率");
        for (Integer channelId : channelIds) {
            headers.add(DynamicChannelType.findOf(channelId).getDesc() + "差评率");
        }

        headers.add("未回复差评数");
        for (Integer channelId : channelIds) {
            headers.add(DynamicChannelType.findOf(channelId).getDesc() + "未回复差评数");
        }

        headers.add("未回复差评率");
        for (Integer channelId : channelIds) {
            headers.add(DynamicChannelType.findOf(channelId).getDesc() + "未回复差评率");
        }

        headers.add("未回复评价数");
        for (Integer channelId : channelIds) {
            headers.add(DynamicChannelType.findOf(channelId).getDesc() + "未回复评价数");
        }
        return headers;
    }

    /**
     * string的除法，且返回结果为带一位小数的百分比
     *
     * @param num1 被除数
     * @param num2 除数
     * @return string类型的结果
     */
    private String stringDiv(String num1, String num2) {
        if (StringUtils.isBlank(num1) || StringUtils.isBlank(num2)) {
            return "0";
        }
        BigDecimal res = new BigDecimal(num1).divide(new BigDecimal(num2), 3, RoundingMode.HALF_UP);
        if (res.compareTo(BigDecimal.ZERO) > 0) {
            DecimalFormat df = new DecimalFormat("0.#%");
            return df.format(new BigDecimal(String.valueOf(res)));
        } else {
            return "0";
        }
    }

    /**
     * 由Map<渠道id, Map<label, count>>  转换为 Map<label, Map<渠道id, count>>的映射
     * @param channel2Label2Count
     * @return
     */
    private Map<String, Map<String, String>> toLabel2Channel2Count(Map<Integer, Map<String, String>> channel2Label2Count) {
        Map<String, Map<String, String>> label2Channel2Count = new HashMap<>();
        for (Map.Entry<Integer, Map<String, String>> channelInfo : channel2Label2Count.entrySet()) {
            for (Map.Entry<String, String> labelInfo : channelInfo.getValue().entrySet()) {
                if (label2Channel2Count.containsKey(labelInfo.getKey())) {
                    Map<String, String> channel2Count = label2Channel2Count.get(labelInfo.getKey());
                    channel2Count.put(String.valueOf(channelInfo.getKey()), labelInfo.getValue());
                } else {
                    Map<String, String> channel2Count = new HashMap<>();
                    channel2Count.put(String.valueOf(channelInfo.getKey()), labelInfo.getValue());
                    label2Channel2Count.put(labelInfo.getKey(), channel2Count);
                }
            }
        }
        return label2Channel2Count;
    }
}
