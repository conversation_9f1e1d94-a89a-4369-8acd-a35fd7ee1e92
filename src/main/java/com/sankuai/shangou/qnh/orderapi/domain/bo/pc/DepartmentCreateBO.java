package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.tenant.thrift.dto.DistrictSimDto;
import com.meituan.shangou.saas.tenant.thrift.dto.department.request.DepartmentAddRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.DepartmentCreateRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.DistrictSim;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/4/30 16:09
 * @Description:
 */
@Data
@NoArgsConstructor
public class DepartmentCreateBO {


    protected Integer departmentType;

    protected String departmentName;


    protected Long parentDepartmentId;

    /**
     * 组织节点的物理城市，即组织节点的管理范围
     */
    protected List<DistrictSim> depMgtScope;

    /**
     * 合作商组织节点的关联合作商ID
     */
    protected Long associatedCommercialAgentId;

    /******         以下为新建摊位相关字段         ******/

    protected Integer settleType;

    /**
     * 摊位备货类型 1-自采 2-外采
     */
    protected Integer boothPickingType;

    protected String bankCode;

    protected String bankName;

    protected String bankCardNo;

    protected String accountName;

    protected String qrCodeUrl;

    protected Long tenantId;

    /**
     * 操作人Id
     */
    protected Long optId;

    /**
     * 操作人账号名
     */
    protected String opUser;

    protected Long poiId;

    protected String mobilePayAccount;

    /**
     * 摊位折扣比例
     */
    protected String boothSettleRatio;

    /**
     * 摊位检索码
     */
    protected String quickSearchCode;

    /**
     * 现结支付宝账户
     */
    protected String onSiteAlipayAccount;

    /**
     * 现结支付宝账户的真实姓名
     */
    protected String onSiteAlipayRealname;

    public DepartmentCreateBO(DepartmentCreateRequest request) {
        this.departmentName = request.getDepartmentName();
        this.departmentType = request.getDepartmentType();
        this.parentDepartmentId = request.getParentDepartmentId();
        this.depMgtScope = request.getDepMgtScope();
        this.associatedCommercialAgentId = request.getAssociatedCommercialAgentId();
        this.settleType = request.getSettleType();
        this.boothPickingType = request.getBoothPickingType();
        this.bankCode = request.getBankCode();
        this.bankName = request.getBankName();
        this.bankCardNo = request.getBankCardNo();
        this.accountName = request.getAccountName();
        this.qrCodeUrl = request.getQrCodeUrl();
        this.poiId = ConverterUtils.nonNullConvert(request.getPoiId(), Long::valueOf);
        this.mobilePayAccount = request.getMobilePayAccount();
        this.boothSettleRatio = request.getBoothSettleRatio();
        this.quickSearchCode = request.getBoothQuickSearchCode();
        this.onSiteAlipayAccount = request.getBoothOnsiteAlipayAccount();
        this.onSiteAlipayRealname = request.getBoothOnsiteAlipayRealname();
    }


    public DepartmentAddRequest convert2Add() {
        DepartmentAddRequest req = new DepartmentAddRequest();
        req.setDepartmentName(departmentName);
        req.setDepartmentType(departmentType);
        req.setTenantId(tenantId);
        req.setOptUser(opUser);
        req.setParentId(parentDepartmentId);
        req.setDepMgtScope(ConverterUtils.convertList(depMgtScope, DistrictSim::toDto));
        return req;
    }
}
