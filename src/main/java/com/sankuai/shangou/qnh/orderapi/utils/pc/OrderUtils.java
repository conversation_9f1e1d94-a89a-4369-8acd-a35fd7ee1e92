package com.sankuai.shangou.qnh.orderapi.utils.pc;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.meituan.shangou.saas.order.platform.utils.GsonUtil;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

@Slf4j
public class OrderUtils {

    public static String getExtDataAsString(String key, String extData) {
        if (StringUtils.isBlank(key)) {
            return null;
        }

        JsonObject jsonObject = GsonUtil.toJsonObject(extData);
        if (jsonObject == null) {
            return null;
        }

        JsonElement goodsCode = jsonObject.get(key);

        if (goodsCode == null) {
            return null;
        }

        try {
            return goodsCode.getAsString();
        } catch (Exception e) {
            log.error("提取信息失败key:{}, extData:{}", key, extData, e);
            return null;
        }
    }

    public static Integer getExtDataAsInt(String key, String extData) {
        if (StringUtils.isBlank(key) || StringUtils.isBlank(extData)) {
            return null;
        }

        try {
            Map<String, Object> stringObjectMap = GsonUtil.toObjMap(extData);
            if (stringObjectMap.containsKey(key)) {
                return Integer.parseInt(stringObjectMap.get(key).toString());
            }
        } catch (Exception e) {
            log.error("提取信息失败key:{}, extData:{}", key, extData, e);
        }

        return null;
    }

    /**
     *  生成MD5令牌
     * @param viewOrderId
     * @param channleId
     * @param tenantId
     * @return
     */
    public static String generateMD5Token(String viewOrderId, Integer channleId, Long tenantId) {
        try {
            StringBuilder builder = new StringBuilder();
            String data = builder.append(viewOrderId).append(channleId).append(tenantId).toString();
            return DigestUtils.md5Hex(data);
        }catch (Exception e) {
            log.error("生成MD5令牌失败", e);
            return Strings.EMPTY;
        }
    }

    public static Long getStoreId(Long dispatchShopId, Long warehouseId, Long shopId) {
       return Objects.nonNull(dispatchShopId) ? dispatchShopId : (Objects.nonNull(warehouseId) ? warehouseId : shopId);
    }
}
