package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
public class BaseInfoVO {
    /**
     * 渠道订单号
     */
    private String orderId;

    /**
     * 赋能订单号
     */
    private String empowerOrderId;

    /**
     * 门店 ID
     */
    private Long poiId;

    /**
     * 门店名称
     */
    private String poiName;


    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道名称
     */
    private Integer channelId;

    /**
     * 1：配送 2：到店自提
     */
    private Integer distributeMethod;


    /**
     * 收货人名称
     */
    private String receiverName;

    /**
     * 订单创建时间
     */
    private String receiveTime;

    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 收货人隐私号码
     */
    private String receiverPrivacyPhone;

    /**
     * 收货人地址
     */
    private String receiverAddress;

    /**
     * 订单金额
     */
    private String amount;

    /**
     * 实付金额
     */
    private String paidAmount;


    /**
     * 商家实收金额
     */
    private String merchantAmount;

    /**
     *
     */
    private String deliveryAmount;

    /**
     * 餐盒费
     */
    private String packageAmount;

    /**
     * 平台服务费
     */
    private String platformAmount;


    /**
     * 是否需要发票
     */
    private int needInvoice;


    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 税号
     */
    private String invoiceTaxNo;


    /**
     * 配送方式
     */
    private String deliveryMethod;


    /**
     * 配送单创建时间
     */
    private String deliveryOrderCreateTime;


    /**
     * 配送员姓名
     */
    private String riderName;


    /**
     * 配送员电话
     */
    private String riderPhone;

    /**
     * 是否在线支付
     */
    private int paidOnline;


    /**
     * 订单状态
     */
    private String status;

    /**
     * 融合订单状态
     */
    private String fuseOrderStatus;

    /**
     * 融合订单状态
     */
    private Integer fuseOrderStatusCode;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 申请退款原因
     */
    private String refundReason;


    /**
     * 退款状态编码
     */
    private String refundTagId;


    /**
     * 售后审核状态
     */
    private String afterSaleApplyStatus;


    private String refundableOrderAmount;


    /**
     * 服务单号
     */
    private String serviceId;

    /**
     * 发起者
     */
    private String applicant;


    /**
     * 售后请求状态
     */
    private String afsApplyType;


    private String orderType;

    private String memberCard;

    /**
     * 商家活动支出
     */
    private String bizActivityAmt;

    /**
     * 线下总价
     */
    private String offlineTotalAmt;
    /**
     * 销售利润
     */
    private String saleBenefit;

    /**
     * 预计送达开始时间
     */
    private String arrivalTimeStart;

    /**
     * 预计送达时间
     */
    private String arrivalTime;

    /**
     * 备注
     */
    private String comment;

    /**
     * 仓 ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 商品数量
     */
    private Integer productCount;

    /**
     * 总重量
     */
    private Integer weight;


    /**
     * 商品种类数量
     */
    private Integer productCategoryCount;

    /**
     * 商品金额
     */
    private String productAmount;

    /**
     *配送状态
     */
    private Integer distributeStatus;

    /**
     *配送状态
     */
    private String distributeStatusDesc;



    /**
     * 配送状态
     */
    private Integer deliveryStatus;

    /**
     * 配送状态描述
     */
    private String deliveryStatusDesc;

    /**
     * 核销状态
     */
    private Integer writeOffStatus;

    /**
     * 核销错误描述
     */
    private String posErrorMsg;

    /**
     * 核销检查标签
     */
    private Integer posCheckTag;

    /**
     * 核销时间
     */
    private String writeOffTime;


    /**
     * 订单流水号
     */
    private Long orderSerialNumber;

    /**
     * 订单序号
     */
    private String orderSerialNumberStr;


    /**
     * 自提码
     */
    private String selfFetchCode;


    /**
     * 自提状态
     */
    private Integer selfFetchStatus;

    /**
     * 贵品取货码
     */
    private String expensiveProductPickupCode;

    /**
     * 配送运力渠道
     */
    private String deliveryChannel;

    /**
     * 配送类型 平台：0 三方：1 自配送：2 其他：-1
     */
    private Integer deliveryChannelType;

    /**
     * 原始配送方式
     */
    private Integer originalDistributeType;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 发票金额
     */
    private Integer invoiceMoney;

    /**
     * 秘钥
     */
    private String token;

    /**
     * 是否闪电送订单，1：是，0：否
     */
    private Integer isFastOrder;

    /**
     * 闪电送费用
     */
    private Integer fastDeliveryAmt;

    /**
     * 是否为闪电送转自送订单
     */
    public Boolean isFastToSelfDelivery;
    /*
     * 转单门店id
     */
    private Long dispatchShopId;


    /**
     * 地址变更费
     */
    private String addressChangeFee;

    /**
     * 地址变更费注释
     */
    private String addressChangeFeeNotes;

    /**
     * 三方物流单号列表
     */
    private List<String> originWaybillNoList;
}
