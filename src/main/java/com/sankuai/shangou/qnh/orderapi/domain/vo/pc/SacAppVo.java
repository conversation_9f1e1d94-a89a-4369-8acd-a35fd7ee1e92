package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.shangou.sac.dto.model.SacAppDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020-12-21
 */
@Data
public class SacAppVo {

    /**
     * 应用id
     */
    public Integer appId;

    /**
     * 应用名称
     */
    public String appName;

    /**
     * 应用状态
     */
    public Integer valid;

    /**
     * 应用排序
     */
    public Integer rank;

    /**
     * 应用类型
     */
    public Integer appType;


    public SacAppVo buildBySacAppDTO(SacAppDTO sacAppDto){
        this.appId = sacAppDto.getAppId();
        this.appName = sacAppDto.getAppName();
        this.appType = sacAppDto.getAppType();
        this.rank = sacAppDto.getRank();
        this.valid = sacAppDto.getValid();
        return this;
    }

}
