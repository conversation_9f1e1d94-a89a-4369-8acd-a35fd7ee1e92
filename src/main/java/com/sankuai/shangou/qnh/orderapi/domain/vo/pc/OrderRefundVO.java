package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/***
 * author : <EMAIL>
 * data : 2022/12/6
 * time : 上午11:41
 **/
@TypeDoc(
        description = "订单退款信息"
)
@ApiModel("订单退款信息")
@Data
public class OrderRefundVO {

    @FieldDoc(
            description = "等待处理的退款记录", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "等待处理的退款记录", required = true)
    private RefundingRecordVO waitAuditRefund;

    @FieldDoc(
            description = "等待处理的退款记录列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "等待处理的退款记录列表", required = false)
    private List<RefundingRecordVO> waitAuditRefundList;

    @FieldDoc(
            description = "退款日志流（时间倒序排序）", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款日志流（时间倒序排序）", required = true)
    private List<RefundLog> refundLogs;


}
