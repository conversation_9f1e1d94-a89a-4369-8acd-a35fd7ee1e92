package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 退单日志
 */
@Setter
@Getter
@NoArgsConstructor
@ToString
public class AfterSaleStatusLogBO {

    @FieldDoc(
            description = "操作时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "操作时间", required = true)
    private String operationTime;


    @FieldDoc(
            description = "日志类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "日志类型", required = true)
    private Integer operatorType;


    @FieldDoc(
            description = "描述", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "源状态", required = false)
    private String operatorDesc;

}