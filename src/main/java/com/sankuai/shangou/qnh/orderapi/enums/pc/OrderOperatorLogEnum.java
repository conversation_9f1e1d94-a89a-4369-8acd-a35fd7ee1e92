package com.sankuai.shangou.qnh.orderapi.enums.pc;

/**
 * 订单操作日志枚举
 */
public enum OrderOperatorLogEnum {
    PLACEORDER(1, "下单时间"),
    RECEIVE(2, "接收时间"),
    CONFIRM(3, "接单时间"),
    STARTPICK(4, "开始拣货时间"),
    FINISHPICK(5, "拣货完成时间"),
    DELIVERY(6, "配送开始时间"),
    RIDER_DELIVERED(7, "配送送达时间"),
    FINISH(8, "完成时间"),
    POS(9, "核销时间"),
    CANCEL(10, "取消时间"),
    SELF_FETCH_FINISH(11, "自提完成时间");


    private int value;
    private String desc;

    OrderOperatorLogEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderOperatorLogEnum getByValue(int value) {
        for (OrderOperatorLogEnum e : OrderOperatorLogEnum.values()) {
            if (e.getValue() == value) {
                return e;
            }
        }
        return OrderOperatorLogEnum.PLACEORDER;
    }
}
