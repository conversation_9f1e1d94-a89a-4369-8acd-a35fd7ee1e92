package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.tenant.thrift.dto.DistrictSimDto;
import com.meituan.shangou.saas.tenant.thrift.dto.department.DepartmentDto;
import com.meituan.shangou.saas.tenant.thrift.dto.department.DepartmentWithCodeDto;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/23
 * 部门业务对象
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentBo {

    /**
     * 部门ID
     */
    private Long departmentId;
    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门类型(1部门2门店组3门店4仓库)
     */
    private Integer departmentType;

    /**
     * 父部门ID
     */
    private Long parentId;

    /**
     * 部门关联的实体ID
     */
    private Long relId;

    /**
     * 部门的物理城市
     */
    private List<DistrictSimDto> depCities;

    /**
     * 部门实体类型，和 {@link  #departmentType} 的区别在于区分了门店和中心仓
     * 以便于按 POI 实体类型区分图标
     */
    private Integer departmentEntityType;

    public static DepartmentBo build(DepartmentDto dto) {
        return builder()
                .departmentId(dto.getDepartmentId())
                .departmentName(dto.getDepartmentName())
                .departmentType(dto.getDepartmentType())
                .parentId(dto.getParentId())
                .depCities(dto.getDepMgtScope())
                .departmentEntityType(dto.getDepartmentType())
                .build();
    }

    public static DepartmentBo build(DepartmentBo bo) {
        return builder()
                .departmentId(bo.getDepartmentId())
                .departmentName(bo.getDepartmentName())
                .departmentType(bo.getDepartmentType())
                .parentId(bo.getParentId())
                .depCities(bo.getDepCities())
                .departmentEntityType(bo.getDepartmentEntityType())
                .build();
    }

    public static DepartmentBo build(DepartmentWithCodeDto dto) {
        return builder()
                .departmentId(dto.getDepartmentId())
                .departmentName(dto.getDepartmentName())
                .departmentType(dto.getDepartmentType())
                .parentId(dto.getParentId())
                .relId(ConverterUtils.nonNullConvert(dto.getCode(), Long::valueOf))
                .depCities(dto.getDepMgtScope())
                .departmentEntityType(dto.getDepartmentType())
                .build();
    }
}
