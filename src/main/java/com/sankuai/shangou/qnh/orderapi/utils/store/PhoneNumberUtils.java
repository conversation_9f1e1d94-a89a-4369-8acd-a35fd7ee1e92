package com.sankuai.shangou.qnh.orderapi.utils.store;

import lombok.extern.slf4j.Slf4j;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @createTime 2019/9/29
 * @description .
 */
@Slf4j
public class PhoneNumberUtils {

    /**
     * 判断是否是11位手机号码
     *
     * @param mobileNumber
     * @return
     */
    public static boolean isValidElevenNumberMobileNumber(String mobileNumber) {
        if (mobileNumber == null) {
            return false;
        }
        return Pattern.matches("\\d{11}", mobileNumber);
    }

    /**
     * 入参必须是11位手机号码，转换为隐私号码格式
     *
     * @param mobileNumber 15812349248
     * @return 158****9248
     */
    public static String transferToPrivacyPhone(String mobileNumber) {
        boolean numberValid = isValidElevenNumberMobileNumber(mobileNumber);
        if (!numberValid) {
            log.info("入参不是合法的11位手机号，不脱敏。phone:{}", mobileNumber);
            return mobileNumber;
        }
        StringBuilder sb = new StringBuilder(mobileNumber);
        return sb.replace(3, 7, "****").toString();
    }
}
