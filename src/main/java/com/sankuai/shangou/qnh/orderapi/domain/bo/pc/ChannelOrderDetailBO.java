package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.meituan.shangou.empower.uwmsplatform.enums.OrderBizType;
import com.meituan.shangou.saas.common.enums.AuditTypeEnum;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.*;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderExtend;
import com.meituan.shangou.saas.order.management.client.enums.AfterSaleWhoApplyType;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.AfterSalePatternEnum;
import com.meituan.shangou.saas.order.platform.enums.OperatorTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.PayMethodEnum;
import com.meituan.shangou.saas.order.platform.enums.TradeChannelEnum;
import com.meituan.shangou.saas.order.platform.utils.GsonUtil;
import com.meituan.shangou.saas.utils.AddressSceneConvertUtils;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.AfterSaleRecord;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.DeliveryStatusLogDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderOptLogDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ProductInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderRefundTag;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.converter.pc.UwmsOrderChannelConvert;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.*;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChanelOrderApplicantEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelOrderStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.UserTagTypeEnum;
import com.sankuai.shangou.qnh.orderapi.service.pc.ChannelRedisServiceUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.meituan.shangou.saas.order.platform.ExtDataKeyConstants.ExchangeEk.EXE_CHANGE_ORDER_ITEM_CNT_FROM_KEY;
import static com.meituan.shangou.saas.order.platform.ExtDataKeyConstants.ExchangeEk.EXE_CHANGE_ORDER_ITEM_ID_FROM_KEY;
import static com.meituan.shangou.saas.order.platform.ExtDataKeyConstants.ExchangeEk.EXE_CHANGE_ORDER_QUANTITY_KEY;

/**
 * 订单详情
 *
 * @Author: <EMAIL>
 * @Date: 2019/1/2 15:09
 * @Description:
 */
@Setter
@Getter
@NoArgsConstructor
@ToString
@Slf4j
public class ChannelOrderDetailBO {


    public static List<Integer> ONLINE_PAYMETHODS = PayMethodEnum.ONLINE.getChannelEnumList().stream().map(TradeChannelEnum::getValue).collect(Collectors.toList());


    /**
     * 基础信息
     */
    private BaseInfo baseInfo;

    /**
     * 商品信息
     */
    private List<ItemInfo> itemInfo;

    /**
     * 优惠信息
     */
    private List<PromotionInfo> promotionInfo;

    /**
     * 操作日志
     */
    private List<OperateLog> operateLog;

    /**
     * 配送详情
     */
    private List<DeliveryDetail> deliveryDetail;

    private List<AfterSaleBO> afterSaleList;

    /**
     * 发票详情
     */
    private List<InvoiceDetailBO> invoiceList;

    /**
     *
     */
    private List<DrunkhorseRefundFeeBO> drunkhorseRefundFeeBOS;

    private List<MaterialSkuWithTagBO> materialSkuWithTagBOS;

    private List<DrunkHorseGiftBagVO> giftBagList;



    //private static final String BEFORE_DELIVERY_POSITION_CONTEXT = "联系不上顾客放置点：";

    private static final String NO_DELIVERY_POSITION_CONTEXT = "未设置";

    //全部退款
    private static final Integer ALL_ORDER_REFUND = 1;

    //退款状态，前端枚举，1-申请中，2-同意，3-驳回，4-已撤销
    private static final Integer APPLY_REFUND_STATUS = 1;

    private static final Integer AGREE_REFUND_STATUS = 2;

    private static final Integer REJECT_REFUND_STATUS = 3;

    private static final Integer CANCEL_REFUND_STATUS = 4;

    //申请退款
    private static final Integer APPLY_AFTER_SALE_STATUS = 1;

    //同意退款
    private static final Integer AGREE_AFTER_SALE_STATUS = 4;

    //驳回退款
    private static final Integer REJECT_AFTER_SALE_STATUS = 5;

    //自动同意退款
    private static final Integer AUTO_AGREE_AFTER_SALE_STATUS = 6;

    //撤销退款
    private static final Integer CANCEL_AUDIT_AFTER_SALE_STATUS = 20;

    public ChannelOrderDetailBO(OrderDetailDTO detail) {

        if (detail.getOrderBaseDto() != null) {
            //基础信息
            baseInfo = new BaseInfo();
            baseInfo.setAmount(detail.getOrderBaseDto().getOriginalAmt() / 100.0);
            baseInfo.setPaidAmount(detail.getOrderBaseDto().getActualPayAmt() / 100.0);
            baseInfo.setMerchantAmount(detail.getOrderBaseDto().getBizReceiveAmt() / 100.0);
            baseInfo.setChannelName(detail.getOrderBaseDto().getChannelName());
            baseInfo.setOrderId(detail.getOrderBaseDto().getChannelOrderId());
            baseInfo.setPackageAmount(detail.getOrderBaseDto().getPackageAmt() / 100.0);
            baseInfo.setDeliveryAmount(detail.getOrderBaseDto().getFreight() / 100.0);
            baseInfo.setPackageAmount(detail.getOrderBaseDto().getPackageAmt() / 100.0);
            baseInfo.setNeedInvoice(detail.getOrderBaseDto().isIsNeedInvoice());
            baseInfo.setInvoiceTitle(detail.getOrderBaseDto().getInvoiceTitle());
            baseInfo.setInvoiceTaxNo(detail.getOrderBaseDto().getTaxNo());
            baseInfo.setCreateTime(new Date(detail.getOrderBaseDto().getCreateTime()));
            baseInfo.setPlatformAmount(detail.getOrderBaseDto().getPlatformFee() / 100.0);
            baseInfo.setPaidOnline(ONLINE_PAYMETHODS.contains(detail.getOrderBaseDto().getPayMethod()));
            baseInfo.setReceiverAddress(detail.getOrderBaseDto().getReceiveAddress());
            baseInfo.setReceiverName(detail.getOrderBaseDto().getReceiverName());
            baseInfo.setReceiverPhone(detail.getOrderBaseDto().getReceiverPhone());
            baseInfo.setReceiverPrivacyPhone(detail.getOrderBaseDto().getReceiverPrivacyPhone());
            baseInfo.setPoiId(detail.getOrderBaseDto().getShopId());
            baseInfo.setPoiName(detail.getOrderBaseDto().getShopName());
            baseInfo.setRiderPhone(detail.getOrderBaseDto().getDeliveryUserPhone());
            baseInfo.setDeliveryMethod(detail.getOrderBaseDto().getDeliveryMethodName());
            baseInfo.setRiderName(detail.getOrderBaseDto().getDeliveryUserName());
            baseInfo.setStatus(ConverterUtils.nonNullConvert(
                    ChannelOrderStatusEnum.getByCode(detail.getOrderBaseDto().getChannelOrderStatus())
                    , s -> s.getDesc(), detail.getOrderBaseDto().getChannelOrderStatusDesc()));
            baseInfo.setAfterSaleApplyStatus(String.valueOf(detail.getOrderBaseDto().getLastAfterSaleApplyStatus()));
            baseInfo.setBizActivityAmt(
                    new BigDecimal(String.valueOf(detail.getOrderBaseDto().getBizActivityAmt())).divide(
                            new BigDecimal("100")).doubleValue());
            baseInfo.setOfflineTotalAmt(
                    new BigDecimal(String.valueOf(detail.getOrderBaseDto().getOfflinePriceTotal())).divide(
                            new BigDecimal("100")).doubleValue());
            baseInfo.setSaleBenefit(new BigDecimal(String.valueOf(detail.getOrderBaseDto().getSaleBenefit())).divide(
                    new BigDecimal("100")).doubleValue());
            baseInfo.setArrivalTime(new Date(detail.getOrderBaseDto().getEstimatedSendArriveTimeEnd()));
            baseInfo.setComment(
                    detail.getOrderBaseDto().getComments().equals("0") ? "" : detail.getOrderBaseDto().getComments());

            if (AfterSaleApplyStatusEnum.enumof(detail.getOrderBaseDto()
                    .getLastAfterSaleApplyStatus()) == AfterSaleApplyStatusEnum.AUDITED_REJECT) {
                baseInfo.setRefundReason(detail.getOrderBaseDto().getLastAfterSaleApplyRejectReason());
            } else {
                baseInfo.setRefundReason(detail.getOrderBaseDto().getLastAfterSaleApplyReason());
            }
            //暂时把退差价映射为部分退款，以后迭代会改
            int refundTagId = detail.getOrderBaseDto().getLastAfterSaleApplyRefundTagId();
            if (refundTagId == OrderRefundTag.WEIGHT_REFUND.getValue()) {
                refundTagId = OrderRefundTag.PART_REFUND.getValue();
            }
            baseInfo.setRefundTagId(refundTagId);
            baseInfo.setRefundableOrderAmount(detail.getOrderBaseDto().getRefundableOrderAmount() / 100.0);

            baseInfo.setOrderType(detail.getOrderBaseDto().getOrderBookingType() == 0 ? "实时订单" : "预约订单");
            baseInfo.setMemberCard(
                    StringUtils.isNotBlank(detail.getOrderBaseDto().getMemberCardNum()) ? detail.getOrderBaseDto()
                            .getMemberCardNum() : "非会员");
            if (CollectionUtils.isNotEmpty(detail.getAfterSaleRecords())) {
                Collections.sort(detail.getAfterSaleRecords(),
                        (a, b) -> (int) ((b.getCreateTime() - a.getCreateTime()) / 1000));
                AfterSaleRecord afterSaleRecord = detail.getAfterSaleRecords().get(0);
                baseInfo.setServiceId(afterSaleRecord.getAfterSaleId());
                baseInfo.setAfsApplyType(String.valueOf(afterSaleRecord.getAfsApplyType()));
                baseInfo.setApplicant(ConverterUtils.nonNullConvert(
                        ChanelOrderApplicantEnum.getByCode(afterSaleRecord.getWhoApplyType()),
                        ChanelOrderApplicantEnum::getDesc));
            }

        }

        List<OrderOptLogDTO> weightRefundLog = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(detail.getAfterSaleRecords())) {
            for (AfterSaleRecord afterSaleRecord : detail.getAfterSaleRecords()) {
                if (afterSaleRecord.getAfsPattern() == AfterSalePatternEnum.WEIGHT.getValue()) {
                    OrderOptLogDTO operateLog = new OrderOptLogDTO();
                    operateLog.setOptContent("商家按重量退差价");
                    operateLog.setOptTime(afterSaleRecord.getUpdateTime());
                    operateLog.setOptDesc(afterSaleRecord.getApplyReason());
                    weightRefundLog.add(operateLog);
                }
            }
        }
        //商品信息
        itemInfo = ConverterUtils.convertList(detail.getProducInfotList(), ItemInfo::new);

        //促销信息
        promotionInfo = ConverterUtils.convertList(detail.getPromotionInfoList(), PromotionInfo::new);

        List<OrderOptLogDTO> operateLogs = detail.getOrderOpLogList();
        if (CollectionUtils.isNotEmpty(weightRefundLog)) {
            operateLogs.addAll(weightRefundLog);
            operateLogs = operateLogs.stream()
                    .sorted(Comparator.comparingLong(OrderOptLogDTO::getOptTime).reversed())
                    .collect(Collectors.toList());
        }
        //操作日志
        operateLog = ConverterUtils.convertList(operateLogs, OperateLog::new);

        //配送详情
        deliveryDetail = ConverterUtils.convertList(detail.getDeliveryStateLogList(), DeliveryDetail::new);

    }

    public ChannelOrderDetailBO(OrderDetailVo detail){
        this(detail, false);
    }

    public ChannelOrderDetailBO(OrderDetailVo detail, boolean privatePhonePermission) {

        if (detail.getOrderBaseDto() != null) {
            // 基础信息
            baseInfo = new BaseInfo();
            baseInfo.setOrderStatus(detail.getOrderBaseDto().getOrderStatus());
            baseInfo.setAmount(detail.getOrderBaseDto().getOriginalAmt() / 100.0);
            baseInfo.setPaidAmount(detail.getOrderBaseDto().getActualPayAmt() / 100.0);
            baseInfo.setMerchantAmount(detail.getOrderBaseDto().getBizReceiveAmt() / 100.0);
            baseInfo.setChannelName(detail.getOrderBaseDto().getChannelName());
            baseInfo.setClientType(detail.getOrderBaseDto().getClientType());
            baseInfo.setOrderId(detail.getOrderBaseDto().getChannelOrderId());
            baseInfo.setPackageAmount(detail.getOrderBaseDto().getPackageAmt() / 100.0);
            baseInfo.setDeliveryAmount(detail.getOrderBaseDto().getFreight() / 100.0);
            baseInfo.setPackageAmount(detail.getOrderBaseDto().getPackageAmt() / 100.0);
            baseInfo.setNeedInvoice(Optional.ofNullable(detail.getOrderBaseDto().getIsNeedInvoice()).orElse(Boolean.FALSE));
            baseInfo.setInvoiceTitle(detail.getOrderBaseDto().getInvoiceTitle());
            baseInfo.setInvoiceTaxNo(detail.getOrderBaseDto().getTaxNo());
            baseInfo.setCreateTime(new Date(detail.getOrderBaseDto().getCreateTime()));
            baseInfo.setPayTime(detail.getOrderBaseDto().getPayTime());
            baseInfo.setPlatformAmount(detail.getOrderBaseDto().getPlatformFee() / 100.0);
            baseInfo.setPaidOnline(ONLINE_PAYMETHODS.contains(detail.getOrderBaseDto().getPayMethod()));
            baseInfo.setReceiverAddress(detail.getOrderBaseDto().getReceiveAddress());
            baseInfo.setReceiverName(detail.getOrderBaseDto().getReceiverName());
            baseInfo.setReceiverPhone(detail.getOrderBaseDto().getReceiverPhone());
            // 使用了隐私号 && 没有隐私号查看权限 && 歪马渠道 && 隐私号不为空
            if (NumberUtils.INTEGER_ONE.equals(detail.getOrderBaseDto().getUsePrivacyPhone())
                    && !privatePhonePermission
                    && detail.getOrderBaseDto().getChannelId().equals(ChannelType.MT_DRUNK_HORSE.getValue())
                    && StringUtils.isNotBlank(detail.getOrderBaseDto().getReceiverPrivacyPhone())) {
                baseInfo.setReceiverPhone(detail.getOrderBaseDto().getReceiverPrivacyPhone());
            }
            baseInfo.setReceiverPrivacyPhone(detail.getOrderBaseDto().getReceiverPrivacyPhone());
            baseInfo.setPoiId(detail.getOrderBaseDto().getShopId());
            baseInfo.setDispatchShopId(detail.getOrderBaseDto().getDispatchShopId());
            baseInfo.setPoiName(detail.getOrderBaseDto().getShopName());
            baseInfo.setRiderPhone(detail.getOrderBaseDto().getDeliveryUserPhone());
            baseInfo.setDeliveryMethod(detail.getOrderBaseDto().getDeliveryMethodName());
            baseInfo.setDeliveryChannelId(detail.getOrderBaseDto().getDeliveryChannelId());
            baseInfo.setRiderName(detail.getOrderBaseDto().getDeliveryUserName());
            if (Objects.nonNull(detail.getDeliveryInfoDTO()) && MccConfigUtil.isDhTenant(detail.getOrderBaseDto().getTenantId())) {
                baseInfo.setRiderAccountName(detail.getDeliveryInfoDTO().getRiderAccountName());
            }
            baseInfo.setStatus(ConverterUtils.nonNullConvert(ChannelOrderStatusEnum.getByCode(detail.getOrderBaseDto().getChannelOrderStatus())
                    , s -> s.getDesc(), detail.getOrderBaseDto().getChannelOrderStatusDesc()));
            baseInfo.setAfterSaleApplyStatus(String.valueOf(detail.getOrderBaseDto().getLastAfterSaleApplyStatus()));
            if (OrderSourceEnum.GLORY.getValue() != detail.getOrderBaseDto().getOrderSource()) {
                baseInfo.setBizActivityAmt(new BigDecimal(String.valueOf(detail.getOrderBaseDto().getBizActivityAmt())).divide(new BigDecimal("100")).doubleValue());
                if(detail.getOrderBaseDto().getBizActivityAmtWithoutConsignment() != null) {
                    baseInfo.setBizActivityAmt(new BigDecimal(String.valueOf(detail.getOrderBaseDto().getBizActivityAmtWithoutConsignment())).divide(new BigDecimal("100")).doubleValue());
                }
            }
            baseInfo.setOfflineTotalAmt(new BigDecimal(String.valueOf(detail.getOrderBaseDto().getOfflinePriceTotal())).divide(new BigDecimal("100")).doubleValue());
            baseInfo.setSaleBenefit(new BigDecimal(String.valueOf(detail.getOrderBaseDto().getSaleBenefit())).divide(new BigDecimal("100")).doubleValue());
            baseInfo.setArrivalTime(new Date(detail.getOrderBaseDto().getEstimatedSendArriveTimeEnd()));
            baseInfo.setComment(detail.getOrderBaseDto().getComments().equals("0") ? "" : detail.getOrderBaseDto().getComments());

            if (AfterSaleApplyStatusEnum.enumof(detail.getOrderBaseDto().getLastAfterSaleApplyStatus()) == AfterSaleApplyStatusEnum.AUDITED_REJECT) {
                baseInfo.setRefundReason(detail.getOrderBaseDto().getLastAfterSaleApplyRejectReason());
            } else {
                baseInfo.setRefundReason(detail.getOrderBaseDto().getLastAfterSaleApplyReason());
            }
            // 暂时把退差价映射为部分退款，以后迭代会改
            int refundTagId = detail.getOrderBaseDto().getLastAfterSaleApplyRefundTagId();
            if (refundTagId == OrderRefundTag.WEIGHT_REFUND.getValue()) {
                refundTagId = OrderRefundTag.PART_REFUND.getValue();
            }
            baseInfo.setRefundTagId(refundTagId);
            baseInfo.setRefundableOrderAmount(detail.getOrderBaseDto().getRefundableOrderAmount() / 100.0);

            baseInfo.setOrderType(detail.getOrderBaseDto().getOrderBookingType() == 0 ? "实时订单" : "预约订单");
            baseInfo.setIsBooking(detail.getOrderBaseDto().getOrderBookingType() != 0);
            baseInfo.setMemberCard(StringUtils.isNotBlank(detail.getOrderBaseDto().getMemberCardNum()) ? detail.getOrderBaseDto().getMemberCardNum() : "非会员");
            if (CollectionUtils.isNotEmpty(detail.getAfterSaleRecords())) {
                Collections.sort(detail.getAfterSaleRecords(), (a, b) -> (int) ((b.getCreateTime() - a.getCreateTime()) / 1000));
                AfterSaleRecordVo afterSaleRecord = detail.getAfterSaleRecords().get(0);
                baseInfo.setServiceId(afterSaleRecord.getAfterSaleId());
                baseInfo.setAfsApplyType(String.valueOf(afterSaleRecord.getAfsApplyType()));
                baseInfo.setApplicant(AfterSaleWhoApplyType.findByValue(afterSaleRecord.getWhoApplyType()).getDesc());
            }

            baseInfo.setOfflineOrderId(detail.getOrderBaseDto().getOrderId());
            baseInfo.setWarehouseId(detail.getOrderBaseDto().getWarehouseId());
            baseInfo.setWarehouseName(detail.getOrderBaseDto().getWarehouseName());
            baseInfo.setOrderUserType(detail.getOrderBaseDto().getOrderUserType());
            baseInfo.setOrderExtend(detail.getOrderBaseDto().getOrderExtend());
            baseInfo.setUsePrivacyPhone(detail.getOrderBaseDto().getUsePrivacyPhone());
            if (MccConfigUtil.isDhTenant(detail.getOrderBaseDto().getTenantId()) && Objects.equals(ChannelType.MEITUAN.getValue(), detail.getOrderBaseDto().getChannelId())) {
                String deliveryPosition = null;
                if (detail.getOrderBaseDto().getDeliveryPosition() == null) {
                    deliveryPosition = NO_DELIVERY_POSITION_CONTEXT;
                } else {
                    deliveryPosition = detail.getOrderBaseDto().getDeliveryPosition();
                }
                baseInfo.setDeliveryPosition(deliveryPosition);
            }
            if (detail.getDeliveryInfoDTO() != null && MccConfigUtil.isDhTenant(detail.getOrderBaseDto().getTenantId())) {
                baseInfo.setScene(AddressSceneConvertUtils.convertCategory2Scene(detail.getDeliveryInfoDTO().getCategory()));
                baseInfo.setCategory(transferCategory(detail.getDeliveryInfoDTO().getCategory()));
            }
            if (CollectionUtils.isNotEmpty(detail.getOrderBaseDto().getTags())) {
                TagInfoVO tagInfoVO = CollectionUtils.isNotEmpty(UserTagTypeEnum.getTagList(detail.getOrderBaseDto().getTags())) ? UserTagTypeEnum.getTagList(detail.getOrderBaseDto().getTags()).get(0) : null;
                baseInfo.setUserTag(tagInfoVO != null ? tagInfoVO.getName() : null);
            }
            if (detail.getOrderBaseDto().getOrderExtend() != null) {
                baseInfo.setPullNewOrder(Objects.equals(detail.getOrderBaseDto().getOrderExtend().getSelfPickPullNewOrder(), true) ? 1 : 0);
            }
            if (detail.getOrderBaseDto().getFinishDeliveryTime() != null) {
                baseInfo.setFinishDeliveryTime(DateTimeUtils.getDateStrFromLong(detail.getOrderBaseDto().getFinishDeliveryTime()));
            }
            if (MccConfigUtil.checkIsFranchiseeFeePoi(detail.getOrderBaseDto().getTenantId(), detail.getOrderBaseDto().getShopId())) {
                if (Objects.equals(detail.getOrderBaseDto().getFranchiseeOrder(), CommonConstant.FRANCHISEE_ORDER)) {
                    baseInfo.setIsFranchiseeOrder(true);
                    baseInfo.setFranchiseeMerchantAmount(MoneyUtils.centToYuan(detail.getOrderBaseDto().getSettleAmount()));
                    baseInfo.setCommisionAmount(MoneyUtils.centToYuan(detail.getOrderBaseDto().getCommisionAmount()));
                    drunkhorseRefundFeeBOS = ConverterUtils.convertList(detail.getAfterSaleRecords(), DrunkhorseRefundFeeBO::build);
                    fixHistoryFeeShow(baseInfo, detail.getOrderBaseDto());
                } else {
                    if (Objects.equals(detail.getOrderBaseDto().getChannelId(), ChannelType.MEITUAN.getValue())) {
                        baseInfo.setCommisionAmount(MoneyUtils.centToYuan(detail.getOrderBaseDto().getPlatformFee()));
                    }
                }
            }
            baseInfo.setIsConsignment(detail.getOrderBaseDto().getConsignmentOrder());
            baseInfo.setIsFacaiWine(detail.getOrderBaseDto().getIsFacaiWine());
        }

        List<OrderOptLogVo> weightRefundLog= Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(detail.getAfterSaleRecords())) {
            for (AfterSaleRecordVo afterSaleRecord : detail.getAfterSaleRecords()) {
                if (afterSaleRecord.getAfsPattern() == AfterSalePatternEnum.WEIGHT.getValue()) {
                    OrderOptLogVo operateLog = new OrderOptLogVo();
                    operateLog.setOptContent("商家按重量退差价");
                    operateLog.setOptTime(afterSaleRecord.getUpdateTime());
                    operateLog.setOptDesc(afterSaleRecord.getApplyReason());
                    weightRefundLog.add(operateLog);
                }
            }
        }
        // 商品信息
        itemInfo = ConverterUtils.convertList(detail.getProductInfoList(), ItemInfo::new);

        // 促销信息
        promotionInfo = ConverterUtils.convertList(detail.getPromotionInfoList(), PromotionInfo::new);

        giftBagList = DrunkHorseGiftBagVO.buildDrunkHorseGiftBag(detail.getDrunkHorseGiftBagVoList(), itemInfo);

        List<OrderOptLogVo> operateLogs = detail.getOrderOpLogList();
        if (CollectionUtils.isNotEmpty(weightRefundLog)){
            operateLogs.addAll(weightRefundLog);
            operateLogs=operateLogs.stream().sorted(Comparator.comparingLong(OrderOptLogVo::getOptTime).reversed()).collect(Collectors.toList());
        }
        // 操作日志
        operateLog = ConverterUtils.convertList(operateLogs, OperateLog::new);

        // 配送详情
        deliveryDetail = ConverterUtils.convertList(detail.getDeliveryStateLogList(), DeliveryDetail::new);

        if(MccConfigUtil.isDhTenant(detail.getOrderBaseDto().getTenantId()) &&
                (Objects.equals(detail.getOrderBaseDto().getOrderStatus(), OrderStatusEnum.CANCELED.getValue()) || CollectionUtils.isNotEmpty(detail.getAfterSaleRecords()))) {
            //退款信息模块
            afterSaleList = buildAfterSaleList(detail);
        }


    }

    /**
     * 2024-08-08 01:00:00全量前的加盟标识订单，若无新佣金实收字段则读老字段
     **/
    public void fixHistoryFeeShow(BaseInfo order, OrderBaseVo orderBaseVo) {
        try {
            if (order == null || order.getCreateTime() == null || orderBaseVo == null) {
                return;
            }
            String targetDateTimeStr = "2024-08-08 01:00:00";
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date targetDateTime = sdf.parse(targetDateTimeStr);
            boolean isBefore = order.getCreateTime().before(targetDateTime);
            if(isBefore && order.getFranchiseeMerchantAmount() == null){
                baseInfo.setIsFranchiseeOrder(false);
                if (Objects.equals(orderBaseVo.getChannelId(), ChannelType.MEITUAN.getValue())) {
                    baseInfo.setCommisionAmount(MoneyUtils.centToYuan(orderBaseVo.getPlatformFee()));
                }
            }
        } catch (Exception e) {
            log.error("fixHistoryFeeShow error", e);
        }
    }


    /**
     * 转换为VO对象
     *
     * @return
     */
    public ChannelOrderDetailVO toChannelOrderDetailVO() {
        ChannelOrderDetailVO channelOrderDetailVO = new ChannelOrderDetailVO();
        channelOrderDetailVO.setBaseInfo(ConverterUtils.nonNullConvert(baseInfo, BaseInfo::toBaseInfoVO));
        //itemInfo中包含换货信息，单独处理一下
        channelOrderDetailVO.setItemInfo(buildItemInfoVO2ExchangeSchme());
        channelOrderDetailVO.setPromotionInfo(ConverterUtils.convertList(promotionInfo, PromotionInfo::toPromotionInfoVO));
        channelOrderDetailVO.setOperateLog(ConverterUtils.convertList(operateLog, OperateLog::toOperateLogVO));
        channelOrderDetailVO.setDeliveryDetail(ConverterUtils.convertList(deliveryDetail, DeliveryDetail::toDeliveryDetailVO));
        channelOrderDetailVO.setAfterSaleList(ConverterUtils.convertList(afterSaleList, AfterSaleBO::toBuildAfterSaleVO));
        channelOrderDetailVO.setInvoiceList(ConverterUtils.convertList(invoiceList, InvoiceDetailBO::buildInvoiceVO));
        channelOrderDetailVO.setMaterialSkuList(ConverterUtils.convertList(materialSkuWithTagBOS, MaterialSkuWithTagBO::toVO));
        channelOrderDetailVO.setGiftBagList(giftBagList);
        return channelOrderDetailVO;
    }

    /**
     * 将扁平的itemList组合成有所属关系的结构
     *
     * @return
     */
    private List<ChannelOrderDetailVO.ItemInfoVO> buildItemInfoVO2ExchangeSchme() {
        List<ChannelOrderDetailVO.ItemInfoVO> result = new ArrayList<>();
        Map<Long, List<ChannelOrderDetailVO.ExchangeItemInfoVO>> exchangeProductMap = new HashMap<>();
        for (ItemInfo currentItemInfo:itemInfo) {
            if(StringUtils.isBlank(currentItemInfo.getExtData())){
                result.add(currentItemInfo.toItemInfoVO());
                continue;
            }

            Map<String, Object> stringObjectMap = GsonUtil.toObjMap(currentItemInfo.getExtData());
            Object exchangeOrderItemId = stringObjectMap.get(EXE_CHANGE_ORDER_ITEM_ID_FROM_KEY);
            if(Objects.isNull(exchangeOrderItemId)){
                result.add(currentItemInfo.toItemInfoVO());
                continue;
            }

            Long sourceOrderItemId = Long.parseLong(String.valueOf(exchangeOrderItemId));
            Object exchangeOrderItemCnt = stringObjectMap.get(EXE_CHANGE_ORDER_ITEM_CNT_FROM_KEY);
            ChannelOrderDetailVO.ExchangeItemInfoVO exchangeItemInfoVO = new ChannelOrderDetailVO.ExchangeItemInfoVO();

            exchangeItemInfoVO.setItemInfoVO(currentItemInfo.toItemInfoVO());
            exchangeItemInfoVO.setExchangeFromCnt(Integer.parseInt(String.valueOf(exchangeOrderItemCnt)));
            exchangeItemInfoVO.setExchangeToCnt(currentItemInfo.getQuantity());
            if(exchangeProductMap.containsKey(sourceOrderItemId)){
                exchangeProductMap.get(sourceOrderItemId).add(exchangeItemInfoVO);
            }else{
                List<ChannelOrderDetailVO.ExchangeItemInfoVO> list = new ArrayList<>();
                list.add(exchangeItemInfoVO);
                exchangeProductMap.put(sourceOrderItemId,list);
            }
        }
        result.stream()
                .filter(e->exchangeProductMap.containsKey(e.getOrderItemId()))
                .forEach(e->{
                    e.setExchangeItemInfoVOList(exchangeProductMap.get(e.getOrderItemId()));
                });
        return result;
    }


    public List<DeliveryInfoVo> toDeliveryInfoVo(List<DeliveryInfoBO> deliveryInfoBOList, Map<Integer,String> channelMap){

        if(CollectionUtils.isEmpty(deliveryDetail)){
            return Collections.emptyList();
        }
        Map<Integer,DeliveryInfoBO> deliveryInfoBOMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(deliveryInfoBOList)){
            deliveryInfoBOMap=deliveryInfoBOList.stream().collect(Collectors.toMap(DeliveryInfoBO::getDeliveryCount,a->a,(k1,k2)->k2));
        }
        ArrayListMultimap<Integer,DeliveryDetail> deliveryDetailListMap= ArrayListMultimap.create();
        for (DeliveryDetail detail : deliveryDetail){
            Integer deliveryCount=1;
            if(detail.getDeliveryCount()!=null){
                deliveryCount=detail.getDeliveryCount();
            }
            deliveryDetailListMap.put(deliveryCount,detail);
        }
        List<DeliveryInfoVo> infoVoList=new ArrayList<>();
        for (Integer count : deliveryDetailListMap.keySet()){
            List<DeliveryDetail> detailList= deliveryDetailListMap.get(count);
            DeliveryInfoBO infoBO=deliveryInfoBOMap.get(count);
            DeliveryInfoVo infoVo=new DeliveryInfoVo();
            DeliveryInfoVo.DeliveryFeeInfoVO feeInfoVO=new DeliveryInfoVo.DeliveryFeeInfoVO();
            feeInfoVO.setDeliveryCount(count);
            if(infoBO!=null){
                if (MapUtils.isEmpty(channelMap)){
                    DeliveryChannelEnum channelEnum =DeliveryChannelEnum.valueOf(infoBO.getDeliveryChannel());
                    feeInfoVO.setDeliveryChannelName(channelEnum==null ? "":channelEnum.getName());
                    if(DeliveryChannelUtils.isDapDeliveryChannel(infoBO.getDeliveryChannel())) {
                        feeInfoVO.setDeliveryChannelName("青云配送");
                    }
                    feeInfoVO.setDeliveryPlatform(DeliveryChannelUtils.getDeliveryPlatform(infoBO.getDeliveryChannel(), infoBO.getStatus()));
                }else {
                    feeInfoVO.setDeliveryChannelName((channelMap.getOrDefault(infoBO.getDeliveryChannel(),"")));
                }
                feeInfoVO.setDeliveryFee(infoBO.getDeliveryFee()==null ? "0":new DecimalFormat("#.##").format(infoBO.getDeliveryFee()));
                feeInfoVO.setTipAmount(infoBO.getTipAmount()==null ? "0":new DecimalFormat("#.##").format(infoBO.getTipAmount()));
                feeInfoVO.setRiderName(infoBO.getRiderName());
                feeInfoVO.setRiderPhone(infoBO.getRiderPhone());
            }
            infoVo.setDeliveryFeeInfo(feeInfoVO);
            List<DeliveryDetailVO> detailVOList=new ArrayList<>();
            if(CollectionUtils.isNotEmpty(detailList)){
                detailVOList=ConverterUtils.convertList(detailList, DeliveryDetail::toDeliveryDetailVO);
            }
            infoVo.setDeliveryLogList(detailVOList);
            infoVoList.add(infoVo);
        }
        return infoVoList;
    }

    public List<AfterSaleBO> buildAfterSaleList(OrderDetailVo detail) {
        try {
            Collections.sort(detail.getAfterSaleRecords(), (a, b) -> (int) ((b.getCreateTime() - a.getCreateTime()) / 1000));
            //一、查询auditType,审核场景
            Integer auditType = queryAuditTypeFromDB(detail);
            AuditTypeEnum auditTypeEnum = auditType != null ? AuditTypeEnum.codeOf(auditType) : AuditTypeEnum.UNKNOWN_AUDIT;
            if(Objects.equals(auditTypeEnum, AuditTypeEnum.UNKNOWN_AUDIT)) {
                auditType = queryAuditTypeFromRedis(detail);
                auditTypeEnum = auditType != null ? AuditTypeEnum.codeOf(auditType) : AuditTypeEnum.UNKNOWN_AUDIT;
            }
            //二、退款模块信息构建
            Collections.sort(detail.getOrderOpLogList(), (a, b) -> (int) ((b.getOptTime() - a.getOptTime()) / 1000l));
            AfterSaleBO afterSaleBO = new AfterSaleBO();
            if (CollectionUtils.isEmpty(detail.getAfterSaleRecords())) { //无售后信息必是订单取消类的
                OrderOptLogVo cancelOpt = detail.getOrderOpLogList().stream().filter(op -> Objects.equals(op.getTargetStatus(), OrderStatusEnum.CANCELED.getValue())).findFirst().orElse(null);
                afterSaleBO.setAfsPattern(ALL_ORDER_REFUND);
                afterSaleBO.setApplyOperatorType(auditTypeEnum.getCancelOperatorType().getValue());
                if(cancelOpt != null) {
                    afterSaleBO.setApplyTime(cancelOpt.getOptTime());
                    afterSaleBO.setApplyReason(cancelOpt.getOptDesc());
                    afterSaleBO.setApplyOperatorName(cancelOpt.getOperator());
                    afterSaleBO.setAuditTime(cancelOpt.getOptTime());
                }
                afterSaleBO.setAfterSaleStatus(AGREE_REFUND_STATUS);
                afterSaleBO.setAuditType(OperatorTypeEnum.SYSTEM.getValue());
                afterSaleBO.setAuditReason(auditTypeEnum.getContent());
                afterSaleBO.setAuditOperatorName(null);
            } else {
                AfterSaleRecordVo afterSaleRecord = detail.getAfterSaleRecords().get(0);
                OrderOptLogVo applyOpt = detail.getOrderOpLogList().stream().filter(op -> Objects.equals(op.getTargetStatus(), OrderStatusEnum.REFUND_APPLIED.getValue()) || Objects.equals(op.getTargetStatus(), OrderStatusEnum.APPEAL_APPLIED.getValue())).findFirst().orElse(null);
                afterSaleBO.setApplyOperatorType(auditTypeEnum.getCancelOperatorType().getValue());
                OrderOptLogVo cancelOrAuditOpt = detail.getOrderOpLogList().stream().filter(op -> Objects.equals(op.getTargetStatus(), OrderStatusEnum.CANCELED.getValue())).findFirst().orElse(null);
                if(applyOpt != null) {
                    afterSaleBO.setApplyTime(applyOpt.getOptTime());
                    afterSaleBO.setApplyOperatorName(applyOpt.getOperator());
                } else if(cancelOrAuditOpt != null){
                    afterSaleBO.setApplyTime(cancelOrAuditOpt.getOptTime());
                }
                afterSaleBO.setApplyReason(afterSaleRecord.getApplyReason());
                if(cancelOrAuditOpt != null) { //有取消单状态，最新退款一定是全部退、同意退款
                    if(Objects.equals(auditTypeEnum, AuditTypeEnum.MERCHANTS_CANCEL) || Objects.equals(auditTypeEnum, AuditTypeEnum.STAFF_CANCEL)) {
                        afterSaleBO.setApplyTime(cancelOrAuditOpt.getOptTime());
                        afterSaleBO.setApplyReason(cancelOrAuditOpt.getOptDesc());
                        afterSaleBO.setApplyOperatorName(cancelOrAuditOpt.getOperator());
                    }
                    afterSaleBO.setAfsPattern(ALL_ORDER_REFUND);
                    afterSaleBO.setAuditType(auditTypeEnum.getAuditOperatorType().getValue());
                    afterSaleBO.setAuditTime(cancelOrAuditOpt.getOptTime());
                    afterSaleBO.setAuditReason(auditTypeEnum.getContent());
                    if(Objects.equals(AuditTypeEnum.MERCHANTS_AUDIT, auditTypeEnum)) {
                        afterSaleBO.setAuditReason("商家发起同意全部退款");
                    }
                    if(Objects.equals(AuditTypeEnum.STAFF_AUDIT, auditTypeEnum)) {
                        afterSaleBO.setAuditReason("客服发起同意全部退款");
                    }
                    afterSaleBO.setAuditOperatorName(cancelOrAuditOpt.getOperator());
                    afterSaleBO.setAfterSaleStatus(AGREE_REFUND_STATUS);
                } else { //最新退款为部分退或驳回则无取消单，查询最近的一次商家已接单（部分退的订单状态由申请退款->商家已接单）
                    afterSaleBO.setAfsPattern(afterSaleRecord.getAfsPattern());
                    if (Objects.equals(afterSaleRecord.getStatus(), APPLY_AFTER_SALE_STATUS)) { //申请退款中
                        afterSaleBO.setAfterSaleStatus(APPLY_REFUND_STATUS);
                        afterSaleBO.setApplyOperatorType(applyOpt.getOperatorType());
                    } else if(Objects.equals(afterSaleRecord.getStatus(), CANCEL_AUDIT_AFTER_SALE_STATUS)) {
                        afterSaleBO.setAfterSaleStatus(CANCEL_REFUND_STATUS);
                        afterSaleBO.setApplyOperatorType(OperatorTypeEnum.CUSTOMER.getValue());
                    } else {
                        OrderOptLogVo partOrRejectAuditOpt = selectLastAuditRefundOpt(detail.getOrderOpLogList());
                        afterSaleBO.setAuditType(auditTypeEnum.getAuditOperatorType().getValue());
                        if(partOrRejectAuditOpt != null) {
                            afterSaleBO.setAuditTime(partOrRejectAuditOpt.getOptTime());
                            afterSaleBO.setAuditOperatorName(partOrRejectAuditOpt.getOperator());
                        }
                        afterSaleBO.setAuditReason(auditTypeEnum.getContent());
                        //商家审批原因构造
                        if (Objects.equals(AuditTypeEnum.MERCHANTS_AUDIT, auditTypeEnum) && partOrRejectAuditOpt != null) {
                            afterSaleBO.setAuditReason(afterSaleBO.getAuditReason() + partOrRejectAuditOpt.getOptDesc());
                        }
                        //客服审批原因构造
                        if (Objects.equals(AuditTypeEnum.STAFF_AUDIT, auditTypeEnum) && partOrRejectAuditOpt != null) {
                            String reason = afterSaleBO.getAuditReason();
                            String backReason = "";
                            if (Objects.equals(afterSaleRecord.getStatus(), AGREE_AFTER_SALE_STATUS) || Objects.equals(afterSaleRecord.getStatus(), AUTO_AGREE_AFTER_SALE_STATUS)) {
                                reason = reason + "同意";
                            } else {
                                reason = reason + "驳回";
                                backReason = backReason +  ":" + partOrRejectAuditOpt.getOptDesc();

                            }
                            if (Objects.equals(afterSaleRecord.getAfsPattern(), ALL_ORDER_REFUND)) {
                                reason = reason + "全部退款";
                            } else {
                                reason = reason + "部分退款";
                            }
                            afterSaleBO.setAuditReason(reason + backReason);
                        }
                        if (Objects.equals(afterSaleRecord.getStatus(), AGREE_AFTER_SALE_STATUS) || Objects.equals(afterSaleRecord.getStatus(), AUTO_AGREE_AFTER_SALE_STATUS)) {
                            afterSaleBO.setAfterSaleStatus(AGREE_REFUND_STATUS);
                        } else if (Objects.equals(afterSaleRecord.getStatus(), REJECT_AFTER_SALE_STATUS)) {
                            afterSaleBO.setAfterSaleStatus(REJECT_REFUND_STATUS);
                        }
                    }
                }

            }
            //兜底逻辑，防止操作人缺失
            setAfterSaleOperator(detail, auditTypeEnum, afterSaleBO);
            //闪购超时未审批文案改为3h
            if(Objects.equals(auditTypeEnum, AuditTypeEnum.MERCHANTS_TIMEOUT_AUDIT) && Objects.equals(detail.getOrderBaseDto().getChannelId(), ChannelType.MEITUAN.getValue())) {
                afterSaleBO.setAuditReason("商家3小时未审批，退款自动通过");
            }
            //名酒馆退单的售后处理
            setMtFamousTavernAfterSaleBO(detail, afterSaleBO);
            return Arrays.asList(afterSaleBO);
        } catch (Exception ex) {
            log.error("构建退款信息error,ex:{}", ex);
            return new ArrayList<>();
        }
    }

    //查询审批退款的log.操作log按时间倒叙，查询申请退款log的前一条
    public OrderOptLogVo selectLastAuditRefundOpt(List<OrderOptLogVo> optList) {
        for(int i = 0; i < optList.size() - 1; i++) {
            OrderOptLogVo opt = optList.get(i);
            OrderOptLogVo beforeTimeOpt = optList.get(i + 1);
            if(Objects.equals(OrderStatusEnum.APPEAL_APPLIED.getValue(), beforeTimeOpt.getTargetStatus()) || Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), beforeTimeOpt.getTargetStatus())) {
                return opt;
            }
        }
        return null;
    }

    public Integer queryAuditTypeFromDB(OrderDetailVo detail) {
        Integer orderStatus = detail.getOrderBaseDto().getOrderStatus();
        Integer auditType = -1;
        if (Objects.equals(orderStatus, OrderStatusEnum.CANCELED.getValue())) { //case1:订单取消或全部退款
            OrderExtend ext = detail.getOrderBaseDto().getOrderExtend();
            if (ext != null) { //优先查询主表的扩展字段
                auditType = ext.getAuditType();
            }
            if (auditType != null && !Objects.equals(auditType, AuditTypeEnum.UNKNOWN_AUDIT.getCode())) { //主表查询成功
                ;
            } else if (CollectionUtils.isNotEmpty(detail.getAfterSaleRecords())) { //主表查询失败,查询售后表
                AfterSaleRecordVo afterSaleRecord = detail.getAfterSaleRecords().get(0);
                auditType = afterSaleRecord.getAuditType();
            }
        } else if (CollectionUtils.isNotEmpty(detail.getAfterSaleRecords())) { //case2:部分退或退款中或被驳回
            AfterSaleRecordVo afterSaleRecord = detail.getAfterSaleRecords().get(0);
            auditType = afterSaleRecord.getAuditType();
        } else { //case3:无退款信息，直接结束
            return null;
        }
        return auditType;
    }

    public Integer queryAuditTypeFromRedis(OrderDetailVo detail) {
        Integer auditType = ChannelRedisServiceUtil.getAuditType(Long.valueOf(detail.getOrderBaseDto().getChannelOrderId()));
        log.info("兜底查询auditType:{},viewOrderId:{}", auditType, detail.getOrderBaseDto().getChannelOrderId());
        return auditType;
    }

    public void setAfterSaleOperator(OrderDetailVo detail, AuditTypeEnum auditTypeEnum, AfterSaleBO afterSaleBO) {
        AfterSaleOperatorBo afterSaleOperatorBo = ChannelRedisServiceUtil.getOperatorDetail(detail.getOrderBaseDto().getChannelOrderId());
        String operatorName = "";
        if(afterSaleOperatorBo != null) {
            operatorName = afterSaleOperatorBo.getOperatorName();
        }
        if(Objects.equals(auditTypeEnum, AuditTypeEnum.MERCHANTS_PART) || Objects.equals(auditTypeEnum, AuditTypeEnum.MERCHANTS_CANCEL)) {
            if(StringUtils.isBlank(afterSaleBO.getApplyOperatorName())) {
                afterSaleBO.setApplyOperatorName(operatorName);
            }
            if(Objects.equals(auditTypeEnum, AuditTypeEnum.MERCHANTS_CANCEL) && StringUtils.isBlank(afterSaleBO.getApplyOperatorName())) {
                afterSaleBO.setApplyOperatorName(afterSaleBO.getAuditOperatorName());
            }
            afterSaleBO.setAuditOperatorName("");
        }
        else if(Objects.equals(auditTypeEnum, AuditTypeEnum.MERCHANTS_AUDIT)) {
            if(StringUtils.isBlank(afterSaleBO.getAuditOperatorName())) {
                afterSaleBO.setAuditOperatorName(operatorName);
            }
            afterSaleBO.setApplyOperatorName("");
        } else { //非商家操作不展示操作人（读取了商家操作人的缓存）
            afterSaleBO.setApplyOperatorName("");
            afterSaleBO.setAuditOperatorName("");
        }
        if(afterSaleBO.getApplyOperatorType() == null || Objects.equals(afterSaleBO.getApplyOperatorType(), OperatorTypeEnum.O2O.getValue())) {
            afterSaleBO.setApplyOperatorType(auditTypeEnum.getCancelOperatorType().getValue());
        }
    }

    /**
     * 设置美团名酒馆的退单售后信息
     *
     * @param detail
     * @param afterSaleBO
     */
    private void setMtFamousTavernAfterSaleBO(OrderDetailVo detail, AfterSaleBO afterSaleBO) {
        if (BooleanUtils.isTrue(detail.getOrderBaseDto().getIsMtFamousTavern())) {
            // 退款发起人从applicant字段的逻辑中获取
            AfterSaleRecordVo afterSaleRecordVo = detail.getAfterSaleRecords().get(0);
            if (Objects.equals(ChanelOrderApplicantEnum.USER.getCode(), afterSaleRecordVo.getWhoApplyType())) {
                afterSaleBO.setApplyOperatorType(OperatorTypeEnum.CUSTOMER.getValue());
            }
            // 美团名酒馆的发起方不为用户，则为美团名酒馆
            if (!Objects.equals(afterSaleBO.getApplyOperatorType(), OperatorTypeEnum.CUSTOMER.getValue())) {
                afterSaleBO.setApplyOperatorType(OperatorTypeEnum.MT_FAMOUS_TAVERN.getValue());
            }
            // 美团名酒馆的审核完成的订单-审核方和审核操作人固定为美团名酒馆
            if (Objects.equals(afterSaleBO.getAfterSaleStatus(), AGREE_REFUND_STATUS)
                    || Objects.equals(afterSaleBO.getAfterSaleStatus(), REJECT_REFUND_STATUS)) {
                afterSaleBO.setAuditType(OperatorTypeEnum.MT_FAMOUS_TAVERN.getValue());
                afterSaleBO.setAuditOperatorName(OperatorTypeEnum.MT_FAMOUS_TAVERN.getDesc());
            }
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class BaseInfo {

        /**
         * 订单号
         */
        private String orderId;

        /**
         * 门店 ID
         */
        private Long poiId;

        /**
         * 转单门店ID
         */
        private Long dispatchShopId;

        /**
         * 门店名称
         */
        private String poiName;


        /**
         * 渠道名称
         */
        private String channelName;


        /**
         * 收货人名称
         */
        private String receiverName;

        /**
         * 收货人电话
         */
        private String receiverPhone;

        /**
         * 收货人隐私号
         */
        private String receiverPrivacyPhone;

        /**
         * 收货人地址
         */
        private String receiverAddress;

        /**
         * 订单金额
         */
        private double amount;

        /**
         * 实付金额
         */
        private double paidAmount;


        /**
         * 商家实收金额
         */
        private double merchantAmount;

        /**
         *
         */
        private double deliveryAmount;

        /**
         * 餐盒费
         */
        private double packageAmount;

        /**
         * 平台服务费
         */
        private double platformAmount;


        /**
         * 是否需要发票
         */
        private boolean needInvoice;


        /**
         * 发票抬头
         */
        private String invoiceTitle;

        /**
         * 税号
         */
        private String invoiceTaxNo;


        /**
         * 配送单创建时间
         */
        private Date deliveryOrderCreateTime;


        /**
         * 配送方式
         */
        private String deliveryMethod;


        /**
         * 配送员姓名
         */
        private String riderName;

        /**
         * 配送员账号名
         */
        private String riderAccountName;


        /**
         * 配送员电话
         */
        private String riderPhone;

        /**
         * 是否在线支付
         */
        private boolean paidOnline;

        /**
         * 订单状态
         */
        private String status;


        /**
         * 售后审核状态
         */
        private String afterSaleApplyStatus;

        /**
         * 创建时间
         */
        private Date createTime;

        /**
         * 退款原因
         */
        private String refundReason;


        /**
         * 退款状态编码
         */
        private int refundTagId;


        private double refundableOrderAmount;


        /**
         * 服务单号
         */
        private String serviceId;

        /**
         * 发起者
         */
        private String applicant;


        /**
         * 售后请求状态
         */
        private String afsApplyType;


        private String orderType;

        private String memberCard;

        /**
         * 商家活动支出
         */
        private Double bizActivityAmt;

        /**
         * 线下总价
         */
        private double offlineTotalAmt;
        /**
         * 销售利润
         */
        private double saleBenefit;

        /**
         *预计送达时间
         */
        private Date arrivalTime;

        /**
         * 备注
         */
        private String comment;

        /**
         * 百川订单号
         */
        private Long offlineOrderId;

        /**
         * 仓库id
         */
        private Long warehouseId;

        /**
         * 仓库名称
         */
        private String warehouseName;

        /**
         * 订单用户类型
         */
        private Integer orderUserType;

        /**
         * 订单附加信息，对应订单extData中的一部分字段
         */
        private OrderExtend orderExtend;

        /**
         * 是否使用隐私号
         * **/
        private Integer usePrivacyPhone;

        private Integer deliveryChannelId;

        /**
         * 代签点信息
         * @return
         */
        private String deliveryPosition;

        /**
         * 场景
         */
        private String scene;

        /**
         * 品类
         */
        private String category;

        /**
         * 是否是预订单
         */
        private Boolean isBooking;

        /**
         * 支付时间
         *
         */
        private Long payTime;

        /**
         * 门店经营模式 1 直营 2 加盟
         */
        private Integer manageMode;

        /**
         * 地推单 1 推广现提  0/null 空
         */
        private Integer pullNewOrder;

        /**
         * 用户身份tag
         */
        private String userTag;

        /**
         * 实际送达时间
         */
        private String finishDeliveryTime;


        /**
         * 组织结构二级
         */
        private String parentOrgName;

        /**
         * 组织结构三级
         */
        private String grandParentOrgName;

        /**
         * 订单重量
         */
        private Long deliveryWeight;

        /**
         * 配送导航距离
         */
        private Long deliveryDistance;

        /**
         * 物理城市
         */
        private String cityName;

        /**
         * 是否为命中加盟费率灰度的加盟单
         */
        private Boolean isFranchiseeOrder = false;

        /**
         * 商家实收(加盟）
         */
        private String franchiseeMerchantAmount;

        /**
         * 商家实收hover后的文案
         */
        private String merchantAmountHover;

        /**
         * 佣金
         */
        private String commisionAmount;

        /**
         * 佣金hover后的文案
         */
        private String commisionAmountHover;

        /**
         * 商品成本
         */
        private String costOfGoodsSold;

        /**
         * 商品成本hover后的文案
         */
        private String costOfGoodsSoldHover;

        /**
         * 实收毛利额
         */
        private String actualGrossProfit;

        /**
         * 实收毛利额hover后的文案
         */
        private String actualGrossProfitHover;

        /**
         * 实收毛率
         */
        private String actualGrossRate;

        /**
         * 实收毛率hover后的文案
         */
        private String actualGrossRateHover;

        /**
         * 是否代销品订单
         */
        private Boolean isConsignment;

        /**
         * 商家活动支出hover文案
         */
        private String totalDiscountHover;

        /**
         * 订单来源
         */
        private String clientType;

        /**
         * 是否发财酒订单
         */
        private Boolean isFacaiWine;

        /**
         * 牵牛花订单状态
         */
        private Integer orderStatus;

        public ChannelOrderDetailVO.BaseInfoVO toBaseInfoVO() {
            ChannelOrderDetailVO.BaseInfoVO baseInfoVO = new ChannelOrderDetailVO.BaseInfoVO();

            baseInfoVO.setChannelName(channelName);
            baseInfoVO.setOrderId(orderId);
            baseInfoVO.setPoiId(poiId);
            baseInfoVO.setDispatchShopId(dispatchShopId);
            baseInfoVO.setPoiName(poiName);
            baseInfoVO.setRefundReason(refundReason);
            baseInfoVO.setStatus(status);

            //收货人信息
            baseInfoVO.setReceiverName(receiverName);
            baseInfoVO.setReceiverPhone(receiverPhone);
            baseInfoVO.setReceiverAddress(receiverAddress);
            baseInfoVO.setReceiverPrivacyPhone(receiverPrivacyPhone);

            //金额相关字段
            baseInfoVO.setAmount(ConverterUtils.formatMoney(amount));
            baseInfoVO.setPaidAmount(ConverterUtils.formatMoney(paidAmount));
            baseInfoVO.setMerchantAmount(ConverterUtils.formatMoney(merchantAmount));
            baseInfoVO.setDeliveryAmount(ConverterUtils.formatMoney(deliveryAmount));
            baseInfoVO.setPackageAmount(ConverterUtils.formatMoney(packageAmount));
            baseInfoVO.setPlatformAmount(ConverterUtils.formatMoney(platformAmount));


            //发票相关字段
            baseInfoVO.setNeedInvoice(ConverterUtils.wrap(needInvoice));
            baseInfoVO.setInvoiceTitle(invoiceTitle);
            baseInfoVO.setInvoiceTaxNo(invoiceTaxNo);

            //骑手
            baseInfoVO.setRiderName(riderName);
            baseInfoVO.setRiderPhone(riderPhone);
            baseInfoVO.setDeliveryMethod(deliveryMethod);
            baseInfoVO.setPaidOnline(ConverterUtils.wrap(paidOnline));
            baseInfoVO.setCreateTime(ConverterUtils.nonNullConvert(createTime, time -> DateFormatUtils.format(time, Constants.DateFormats.SHOW_FORMAT)));

            baseInfoVO.setRefundTagId(String.valueOf(refundTagId));
            baseInfoVO.setAfterSaleApplyStatus(afterSaleApplyStatus);
            baseInfoVO.setRefundableOrderAmount(ConverterUtils.formatMoney(refundableOrderAmount));

            baseInfoVO.setServiceId(serviceId);
            baseInfoVO.setApplicant(applicant);
            baseInfoVO.setAfsApplyType(afsApplyType);

            baseInfoVO.setOrderType(orderType);
            baseInfoVO.setMemberCard(memberCard);

            baseInfoVO.setBizActivityAmt(bizActivityAmt == null ? "-" : ConverterUtils.formatMoney(bizActivityAmt));
            baseInfoVO.setOfflineTotalAmt(ConverterUtils.formatMoney(offlineTotalAmt));
            baseInfoVO.setSaleBenefit(ConverterUtils.formatMoney(saleBenefit));

            baseInfoVO.setArrivalTime(ConverterUtils.nonNullConvert(arrivalTime, time -> DateFormatUtils.format(time, Constants.DateFormats.SHOW_FORMAT)));
            baseInfoVO.setComment(comment);
            baseInfoVO.setWarehouseId(warehouseId);
            baseInfoVO.setWarehouseName(warehouseName);
            baseInfoVO.setOrderUserType(orderUserType);

            // 最后做医药的真实渠道重映射，避免影响其他需要根据渠道id处理的逻辑
            remapMedicineChannelInfo(baseInfoVO);
            //  判断是否为发财酒订单
            baseInfoVO.setIsFacaiWine(isFacaiWine);

            if(MccConfigUtil.isDhTenant(ContextHolder.currentUserTenantId())) {
                if (DeliveryChannelUtils.isDapDeliveryChannel(deliveryChannelId)) {
                    baseInfoVO.setDeliveryMethod("青云配送");
                }
                if (deliveryChannelId == null || DeliveryChannelUtils.isMerchantDeliveryChannel(deliveryChannelId)) {
                    baseInfoVO.setDeliveryMethod("自营配送");
                }
                baseInfoVO.setDeliveryPlatform(DeliveryChannelUtils.getDeliveryPlatform(deliveryChannelId));
            }
            baseInfoVO.setDeliveryPosition(deliveryPosition);
            baseInfoVO.setScene(scene);
            baseInfoVO.setCategory(category);
            baseInfoVO.setManageMode(manageMode);
            baseInfoVO.setPullNewOrder(pullNewOrder);
            baseInfoVO.setUserTag(userTag);
            baseInfoVO.setFinishDeliveryTime(finishDeliveryTime);
            baseInfoVO.setParentOrgName(parentOrgName);
            baseInfoVO.setGrandParentOrgName(grandParentOrgName);
            baseInfoVO.setDeliveryWeight(deliveryWeight);
            baseInfoVO.setDeliveryDistance(deliveryDistance);
            baseInfoVO.setCityName(cityName);
            if(isFranchiseeOrder == true) { //含加盟标识
                baseInfoVO.setMerchantAmount(franchiseeMerchantAmount);
            }
            baseInfoVO.setMerchantAmountHover(merchantAmountHover);
            baseInfoVO.setCommisionAmount(commisionAmount);
            baseInfoVO.setCommisionAmountHover(commisionAmountHover);
            baseInfoVO.setCostOfGoodsSold(costOfGoodsSold);
            baseInfoVO.setCostOfGoodsSoldHover(costOfGoodsSoldHover);
            baseInfoVO.setActualGrossProfit(actualGrossProfit);
            baseInfoVO.setActualGrossProfitHover(actualGrossProfitHover);
            baseInfoVO.setActualGrossRate(actualGrossRate);
            baseInfoVO.setActualGrossRateHover(actualGrossRateHover);
            baseInfoVO.setIsConsignment(isConsignment);
            baseInfoVO.setTotalDiscountHover(totalDiscountHover);
            return baseInfoVO;
        }

        /**
         * 重映射医药的渠道信息，将erp的自有渠道映射到真实的订单渠道
         */
        private void remapMedicineChannelInfo(ChannelOrderDetailVO.BaseInfoVO baseInfoVO) {
            if (this.getOrderExtend() == null || this.getOrderExtend().getMedicineRealOrderChannel() == null
                    || this.getOrderExtend().getMedicineRealOrderChannel() <= 0) {
                return;
            }

            OrderBizType realOrderBizType = UwmsOrderChannelConvert.INSTANCE
                    .convertUwmsOrderChannelCode2OrderBizType(this.getOrderExtend().getMedicineRealOrderChannel());

            if (Objects.equals(OrderBizType.UN_KNOW, realOrderBizType)) {
                return;
            }

            OrderBizTypeEnum centerOrderTypeEnum = OrderBizTypeEnum.enumOf(realOrderBizType.getCode());

            if (centerOrderTypeEnum == null || Objects.equals(OrderBizTypeEnum.UN_KNOW, centerOrderTypeEnum)) {
                return;
            }

            Integer channelId = ChannelOrderConvertUtils.sourceBiz2Mid(centerOrderTypeEnum.getValue());
            String channelName = ChannelOrderConvertUtils.getChannelNameByChannelId(channelId);
            baseInfoVO.setRealMedicineOrderChannelId(String.valueOf(channelId));
            baseInfoVO.setRealMedicineOrderChannelName(channelName);
        }

    }


    @Getter
    @Setter
    @NoArgsConstructor
    @ToString
    public static class ItemInfo {

        /**
         * sku编码
         */
        private String sku;

        /**
         * 渠道sku编码
         */
        private String customSkuId;

        /**
         * 商品名称
         */
        private String skuName;

        /**
         * upc编码
         */
        private String upc;

        /**
         * 规格
         */
        private String spec;

        /**
         * 单位
         */
        private String unit;

        /**
         * 单价
         */
        private double unitPrice;

        /**
         * 数量
         */
        private int quantity;

        /**
         * 总价
         */
        private double totalPrice;


        /**
         * 是否申请取消
         */
        private boolean isRefund;


        /**
         * 取消数量
         */
        private int refundCount;
        /**
         * 摊位名称
         */
        private String boothName;
        /**
         * 商品线下售价
         */
        private String offlinePrice;
        /**
         * 实际拣货数量
         */
        private int realQuantity;
        /**
         * 摊位结算金额
         */
        private String boothSettleAmount;

        /**
         * 属性
         * **/
        private List<String> propertyList;

        /**
         * orderItemId
         * @param productInfo
         */
        private Long orderItemId;

        /**
         * extData,包换换货信息
         */
        private String extData;

        /**
         * 下单时的商品数量
         */
        private Long orderQuantity;

        /**
         * 是否代销商品
         */
        private Boolean isConsignment;

        /**
         * 商补
         */
        private Integer tenantCost;




        /**
         *
         * @param productInfo
         */
        public ItemInfo(ProductInfoDTO productInfo) {
            sku = productInfo.getSkuId();
            customSkuId = productInfo.getCustomerSkuId();
            skuName = productInfo.getSkuName();
            upc = productInfo.getUpcCode();
            spec = productInfo.getSpecification();
            unit = productInfo.getSellUnit();
            unitPrice = productInfo.getUnitPrice() / 100.0;
            quantity = productInfo.getCount();
            totalPrice = unitPrice * quantity;
            isRefund = productInfo.isIsRefund();
            refundCount = productInfo.getRefundCount();
            boothName = productInfo.getBoothName();
            if (productInfo.getOfflinePrice() == -1) {
                offlinePrice = "";
            } else {
                offlinePrice = ConverterUtils.formatMoney(productInfo.getOfflinePrice());
            }
            realQuantity = productInfo.getRealQuantity() / 1000;
            if (productInfo.getStallSettleAmt() == -1) {
                boothSettleAmount = "";
            } else {
                boothSettleAmount = ConverterUtils.formatMoney(productInfo.getStallSettleAmt());
            }

        }

        public ItemInfo(ProductInfoVo productInfo) {
            sku = productInfo.getSkuId();
            customSkuId = productInfo.getCustomerSkuId();
            skuName = productInfo.getSkuName();
            upc = productInfo.getUpcCode();
            spec = productInfo.getSpecification();
            unit = productInfo.getSellUnit();
            unitPrice = productInfo.getUnitPrice() / 100.0;
            quantity = productInfo.getCount();
            totalPrice = unitPrice * quantity;
            isRefund = Optional.ofNullable(productInfo.getIsRefund()).orElse(Boolean.FALSE);
            refundCount = productInfo.getRefundCount();
            boothName = productInfo.getBoothName();
            if (productInfo.getOfflinePrice() == -1) {
                offlinePrice = "";
            } else {
                offlinePrice = ConverterUtils.formatMoney(productInfo.getOfflinePrice());
            }
            realQuantity = productInfo.getRealQuantity() / 1000;
            if (productInfo.getStallSettleAmt() == -1) {
                boothSettleAmount = "";
            } else {
                boothSettleAmount = ConverterUtils.formatMoney(productInfo.getStallSettleAmt());
            }
            propertyList = productInfo.getPropertyList();

            orderItemId = productInfo.getOrderItemId();

            extData = productInfo.getExtData();

            isConsignment = productInfo.getConsignmentProduct();


        }


        public ChannelOrderDetailVO.ItemInfoVO toItemInfoVO() {
            ChannelOrderDetailVO.ItemInfoVO itemInfoVO = new ChannelOrderDetailVO.ItemInfoVO();
            itemInfoVO.setSku(sku);
            itemInfoVO.setCustomSkuId(customSkuId);
            itemInfoVO.setSkuName(skuName);
            itemInfoVO.setUpc(upc);
            itemInfoVO.setSpec(spec);
            itemInfoVO.setUnit(unit);
            itemInfoVO.setUnitPrice(ConverterUtils.formatMoney(unitPrice));
            itemInfoVO.setQuantity(quantity);
            itemInfoVO.setTotalPrice(ConverterUtils.formatMoney(totalPrice));
            itemInfoVO.setIsRefund(isRefund ? "是" : "否");
            if (isRefund) {
                itemInfoVO.setRefundCount(String.valueOf(refundCount));
            }
            itemInfoVO.setBoothName(boothName);
            itemInfoVO.setOfflinePrice(offlinePrice);
            itemInfoVO.setRealQuantity(String.valueOf(realQuantity));
            itemInfoVO.setPropertyList(propertyList);
            itemInfoVO.setBoothSettleAmount(boothSettleAmount);
            if(StringUtils.isBlank(extData)){
                itemInfoVO.setOrderQuantity(quantity);
            }else{
                Map<String, Object> stringObjectMap = GsonUtil.toObjMap(extData);
                if(stringObjectMap.containsKey(EXE_CHANGE_ORDER_QUANTITY_KEY)){
                    Object orderQuantity = stringObjectMap.get(EXE_CHANGE_ORDER_QUANTITY_KEY);
                    itemInfoVO.setOrderQuantity(Integer.parseInt(String.valueOf(orderQuantity)));
                } else {
                    itemInfoVO.setOrderQuantity(quantity);
                }
            }
            itemInfoVO.setOrderItemId(orderItemId);
            itemInfoVO.setIsConsignment(isConsignment);
            return itemInfoVO;
        }


    }


    @Setter
    @Getter
    @ToString
    public static class PromotionInfo {

        /**
         * 优惠名称
         */
        private String promotionName;

        /**
         * 优惠金额
         */
        private double promotionAmount;

        /**
         * 平台承担金额
         */
        private double platformAmount;

        /**
         * 商家承担金额
         */
        private double merchantAmount;

        /**
         * 代理商承担金额
         */
        private double agentAmount;

        /**
         * 物流承担金额
         */
        private double logisticsBearAmount;

        /**
         * uoc活动类型
         */
        private String uocActType;


        public PromotionInfo(com.sankuai.meituan.shangou.empower.ocms.thrift.dto.PromotionInfoDTO promotion) {
            promotionName = promotion.getDescription();
            promotionAmount = promotion.getDiscount() / 100.0;
            platformAmount = promotion.getPlatformBearFee() / 100.0;
            merchantAmount = promotion.getBizBearFee() / 100.0;
            agentAmount = promotion.getAgentBearFee() / 100.0;
            logisticsBearAmount = promotion.getLogisticsBearCharge() / 100.0;
        }

        public PromotionInfo(PromotionInfoVo promotion) {
            promotionName = promotion.getDescription();
            promotionAmount = promotion.getDiscount() / 100.0;
            platformAmount = promotion.getPlatformBearFee() / 100.0;
            merchantAmount = promotion.getBizBearFee() / 100.0;
            agentAmount = promotion.getAgentBearFee() / 100.0;
            logisticsBearAmount = promotion.getLogisticsBearCharge() / 100.0;
            uocActType = promotion.getUocActType();
        }


        public ChannelOrderDetailVO.PromotionInfoVO toPromotionInfoVO() {
            ChannelOrderDetailVO.PromotionInfoVO promotionInfoVO = new ChannelOrderDetailVO.PromotionInfoVO();
            promotionInfoVO.setPromotionName(promotionName);
            promotionInfoVO.setPromotionAmount(ConverterUtils.formatMoney(promotionAmount));
            promotionInfoVO.setPlatformAmount(ConverterUtils.formatMoney(platformAmount));
            promotionInfoVO.setMerchantAmount(ConverterUtils.formatMoney(merchantAmount));
            promotionInfoVO.setAgentAmount(ConverterUtils.formatMoney(agentAmount));
            promotionInfoVO.setLogisticsBearAmount(ConverterUtils.formatMoney(logisticsBearAmount));
            promotionInfoVO.setUocActType(uocActType);
            return promotionInfoVO;
        }

    }


    @Setter
    @Getter
    @NoArgsConstructor
    public static class OperateLog {

        /**
         * 操作员
         */
        private String operator;

        /**
         * 操作时间
         */
        private Date operateTime;

        /**
         * 操作类型
         */
        private String operateType;

        /**
         * 描述
         */
        private String desc;


        /**
         * 状态
         */
        private Integer targetStatus;

        public OperateLog(OrderOptLogDTO log) {
            operator = log.getOperator();
            operateTime = new Date(log.getOptTime());
            operateType = log.getOptContent();
            desc = log.getOptDesc();
        }

        public OperateLog(OrderOptLogVo log) {
            operator = log.getOperator();
            operateTime = new Date(log.getOptTime());
            operateType = log.getOptContent();
            desc = log.getOptDesc();
            // 如果是取消或者订单退款状态--拼接发起方和原因
            if (StringUtils.isNotBlank(log.getOptDesc())
                    && (OrderStatusEnum.CANCELED.getDesc().equals(operateType)
                    || OrderStatusEnum.REFUND_APPLIED.getDesc().equals(operateType))) {
                OperatorTypeEnum operatorTypeEnum = OperatorTypeEnum.enumOf(log.getOperatorType());
                desc = (ChannelOrderConverter.convertOperateMsg(operatorTypeEnum))
                        + "发起" + desc;
            }
            targetStatus = log.getTargetStatus();
        }

        public ChannelOrderDetailVO.OperateLogVO toOperateLogVO() {
            ChannelOrderDetailVO.OperateLogVO operateLogVO = new ChannelOrderDetailVO.OperateLogVO();
            operateLogVO.setOperator(operator);
            operateLogVO.setOperateTime(ConverterUtils.nonNullConvert(operateTime, time -> DateFormatUtils.format(time, Constants.DateFormats.SHOW_FORMAT)));
            operateLogVO.setOperateType(operateType);
            operateLogVO.setDesc(desc);
            return operateLogVO;
        }

    }


    @Setter
    @Getter
    @ToString
    public static class DeliveryDetail {
        private Date updateTime;
        private String deliveryStatus;
        private Integer deliveryCount;

        private Integer targetStatus;


        public DeliveryDetail(DeliveryStatusLogDTO deliveryStateLog) {
            updateTime = new Date(deliveryStateLog.getUpdateTime());
            deliveryStatus = deliveryStateLog.getStateDesc();
        }

        public DeliveryDetail(DeliveryStatusLogVo deliveryStateLog) {
            updateTime = new Date(deliveryStateLog.getUpdateTime());
            deliveryStatus = deliveryStateLog.getStateDesc();
            if(StringUtils.isNotEmpty(deliveryStateLog.getDeliveryCount())){
                deliveryCount=Integer.parseInt(deliveryStateLog.getDeliveryCount());
            }else {
                deliveryCount = 1;
            }
            targetStatus = deliveryStateLog.getTargetStatus();
        }


        public DeliveryDetailVO toDeliveryDetailVO() {
            DeliveryDetailVO deliveryDetailVO = new DeliveryDetailVO();
            deliveryDetailVO.setUpdateTime(ConverterUtils.nonNullConvert(updateTime, time -> DateFormatUtils.format(time, Constants.DateFormats.SHOW_FORMAT)));
            deliveryDetailVO.setDeliveryStatus(deliveryStatus);
            return deliveryDetailVO;
        }
    }

    public String transferCategory(String oldCategory) {
        try {
            if(StringUtils.isBlank(oldCategory)) {
                return null;
            }
            return oldCategory.replace(";", "-");
        } catch (Exception ex) {
            log.error("transferCategory");
            return null;
        }

    }

}
