package com.sankuai.shangou.qnh.orderapi.utils.store;

import com.dianping.lion.client.Lion;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.shangou.logistics.warehouse.TradeShippingGrayService;
import com.sankuai.shangou.qnh.orderapi.context.store.SpringAppContext;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.QnhKaTenantChannelConfig;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.QnhKaTenantChannelVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.dianping.lion.client.Lion.getConfigRepository;

/**
 * MCC 控制开关
 */
@Slf4j
@Service
public class MccDynamicConfigUtil {

    /**
     * 歪马送酒租户Id列表 MCC配置Key
     */
    private static final String WMSJ_TENANT_IDS = "wmsj.tenant.ids";

    private static final Map<Integer, String> defaultOffLinePriceRejectReasonMap = new HashMap<Integer, String>() {
        {
            put(101, "溢价率过高");
            put(102, "规格/重量不正确");
            put(199, "其他");
        }
    };
    private static final Map<Integer, String> defaultOnLinePriceRejectReasonMap = new HashMap<Integer, String>() {
        {
            put(201, "溢价率过高");
            put(202, "规格/重量不正确");
            put(203, "活动商品不能改价");
            put(299, "其他");
        }
    };
    private static final Map<Integer, Map<Integer, String>> defaultRejectReasonMap = new HashMap<Integer, Map<Integer, String>>() {
        {
            put(1, defaultOffLinePriceRejectReasonMap);
            put(2, defaultOnLinePriceRejectReasonMap);
        }
    };

    /**
     * 价格图标是否直接展示
     *
     * @return boolean
     */
    public static boolean isPriceTrendIconDirectShow() {
        return Lion.getConfigRepository().getBooleanValue("price.trend.icon.direct.show");
    }

    public static Map<Integer, Map<Integer, String>> queryRejectReasonTypeMap() {
        String rejectReasonTypeConfig = Lion.getConfigRepository().get("reject.reason.type.config", "");
        if (StringUtils.isBlank(rejectReasonTypeConfig)) {
            return defaultRejectReasonMap;
        }

        try {
            return JacksonUtils.fromJson(rejectReasonTypeConfig, new TypeReference<Map<Integer, Map<Integer, String>>>() {
            });
        } catch (Exception e) {
            log.warn("reject reason type config is error, config [{}].", rejectReasonTypeConfig);
        }
        return defaultRejectReasonMap;
    }

    public static boolean useSacAuthentication() {
        return Lion.getConfigRepository().getBooleanValue("useSacAuthentication", false);
    }

    public static List<Integer> ignoreOrderBizTypeConditions() {
        try {
            // 没传orderbiz时忽略查询条件开关：true,返回空数据；false，返回全部渠道类型
            return Lion.getConfigRepository().getBooleanValue("ignore.search.orderbiztype.condition", false)
                    ? new ArrayList<>() : DynamicChannelType.getAllChannelIds();
        } catch (Exception e) {
            log.warn("ignore.search.orderbiztype.condition is error");
        }
        return DynamicChannelType.getAllChannelIds();
    }

    /**
     * 查询歪马送酒租户Id列表
     *
     * @return List<Long> 歪马送酒租户Id列表
     */
    public static List<Long> getWmsjTenantIds() {
        String config = Lion.getConfigRepository().get(WMSJ_TENANT_IDS, "[]");
        List<Long> result = org.apache.commons.lang3.StringUtils.isBlank(config) ?
                Collections.emptyList() : com.meituan.linz.boot.util.JacksonUtils.parseList(config, Long.class);
        return CollectionUtils.isEmpty(result) ? Lists.emptyList() : result;

    }

    /**
     * 获取qnh ka租户渠道配置
     *
     * @return QnhKaTenantChannelVO
     */
    public static QnhKaTenantChannelVO getQnhKaChannels(long tenantId) {
        String configStr = Lion.getConfigRepository().get("qnh_ka_channels", "");
        if (StringUtils.isEmpty(configStr)) {
            return null;
        }
        QnhKaTenantChannelConfig channelConfig = JacksonUtils.fromJson(configStr, QnhKaTenantChannelConfig.class);
        return channelConfig.getConfigs().stream()
                .filter(config -> config.getTenantId().equals(tenantId)).findFirst().orElse(null);
    }


    /**
     * 获取axb隐私号灰度门店wmPoiId -- 默认测试门店
     *
     * @return 灰度门店列表
     */
    public static Set<String> getAxBPrivacyPhonePoiIds() {
        String configStr = Lion.getConfigRepository().get("axb.privacy.phone.poiIds", "730620");
        if (StringUtils.isEmpty(configStr)) {
            return new HashSet<>();
        }
        String[] poiIds = configStr.split(",");
        return new HashSet<>(Arrays.asList(poiIds));
    }

    /**
     * 是否全部使用axb隐私号 -- 默认false
     *
     * @return .
     */
    public static boolean isAllUserAxBPrivacyPhone() {
        return Lion.getConfigRepository().getBooleanValue("axb.privacy.phone.switch", false);
    }

    /**
     * AXB 隐私号有效时长 -- 默认三小时:单位秒
     *
     * @return .
     */
    public static Integer axbPrivacyPhoneValidTime() {
        return Lion.getConfigRepository().getIntValue("axb.privacy.phone.validTime", 10800);
    }

    /**
     * 骑手端联系用户入口有效时长 -- 默认7天: 单位秒
     *
     * @return .
     */
    public static Long contactUserDuration() {
        return Lion.getConfigRepository().getLongValue("contact.user.duration", 7 * 24 * 60 * 60L);
    }

    /**
     * 骑手端(短时间)联系用户入口有效时长 -- 默认3h: 单位秒
     *
     * @return .
     */
    public static Long newContactUserDuration() {
        return Lion.getConfigRepository().getLongValue("short.contact.user.duration", 3 * 60 * 60L);
    }

    /**
     * 骑手端联系用户入口有效时长 -- 默认1天: 单位秒
     * 参考： https://tt.sankuai.com/ticket/detail?id=309512761
     *
     * @return .
     */
    public static Long contactUserDuration24Hour() {
        return Lion.getConfigRepository().getLongValue("24hour.contact.user.duration", 24 * 60 * 60L);
    }

    private final static Long DEFAULT_HOUR_24 = 24 * 60 * 60L;

    /**
     * 获取脱敏收货人信息的配置时长 -- 默认1天: 单位秒
     * @return .
     */
    public static Long getDesensitizeReceiverInfoTime() {
        try {
            return Lion.getConfigRepository().getLongValue("desensitize_receiver_info_time", DEFAULT_HOUR_24);
        }catch (Exception e){
            log.info("getDesensitizeReceiverInfoTime error",e);
        }
        return DEFAULT_HOUR_24;
    }

    private static final String SUPPORT_DESENSITIZE_RECEIVER_INFO_TENANTS = "support_desensitize_receiver_info_tenants";

    /**
     * 检查租户是否支持按配置脱敏收货人信息
     * @return
     */
    public static boolean checkSupportDesensitizeReceiverInfoTenant(Long tenantId) {
        try{
            List<String> tenantList = getSupportDesensitizeReceiverInfoTenants();
            return hitConfigKey(String.valueOf(tenantId), tenantList);
        }catch (Exception e){
            log.info("checkLimitTimeContactUserTenant error",e);
        }
        return false;
    }

    /**
     * 获取支持按配置脱敏收货人信息的租户列表
     * @return
     */
    public static List<String> getSupportDesensitizeReceiverInfoTenants() {
        try {
            String configStr = Lion.getConfigRepository().get(SUPPORT_DESENSITIZE_RECEIVER_INFO_TENANTS, "");
            return getConfigValueList(configStr, ",");
        }catch (Exception e){
            log.info("getLimitTimeContactUserTenants error",e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取限制时间联系用户租户列表
     * @return
     */
    public static boolean checkLimitTimeContactUserTenant(Long tenantId) {
        try{
            List<String> tenantList = getLimitTimeContactUserTenants();
            return hitConfigKey(String.valueOf(tenantId), tenantList);
        }catch (Exception e){
            log.info("checkLimitTimeContactUserTenant error",e);
        }
        return false;
    }

    private static final String LIMIT_TIME_CONTACT_USER_TENANTS = "limit_time_contact_user_tenants";

    /**
     * 获取限制时间联系用户租户列表
     * @return
     */
    public static List<String> getLimitTimeContactUserTenants() {
        try {
            String configStr = Lion.getConfigRepository().get(LIMIT_TIME_CONTACT_USER_TENANTS, "");
            return getConfigValueList(configStr, ",");
        }catch (Exception e){
            log.info("getLimitTimeContactUserTenants error",e);
        }
        return new ArrayList<>();
    }


    /**
     * 获取配置值分割成列表
     * @param configValue
     * @param splitter
     * @return
     */
    private static List<String> getConfigValueList(String configValue, String splitter) {
        if (StringUtils.isEmpty(configValue)) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(StringUtils.deleteWhitespace(configValue).split(splitter));
    }

    /**
     * 判断是否命中配置key
     * @param key
     * @param configKeyList
     * @return
     */
    private static boolean hitConfigKey(String key,List<String> configKeyList) {
        if (CollectionUtils.isEmpty(configKeyList)){
            return false;
        }
        String firstKey = configKeyList.get(0);
        if (StringUtils.equalsIgnoreCase("ALL", firstKey)){
            return true;
        }
        return configKeyList.contains(key);

    }

    /**
     * 获取使用AXB隐私号的租户ID
     *
     * @return .
     */
    public static Set<String> getAxbPrivacyPhoneTenantIds() {
        String configStr = Lion.getConfigRepository().get("axb.privacy.phone.tenantIds", "1001197");
        if (StringUtils.isEmpty(configStr)) {
            return new HashSet<>();
        }
        String[] tenantIds = configStr.split(",");
        return new HashSet<>(Arrays.asList(tenantIds));
    }

    public static boolean ssrfCheckUrlSwitch(){
        return Lion.getConfigRepository().getBooleanValue("ssrf_check_url_switch", false);
    }

    public static Boolean interruptUrlSsrf() {
        return Lion.getConfigRepository().getBooleanValue("interrupt_url_ssrf", false);
    }

    public static List<String> getUrlWhileList() {
        String urls = Lion.getConfigRepository().get("url_white_list", "[]");
        try {
            return JacksonUtils.fromJson(urls, new TypeReference<List<String>>() {
            });
        } catch (Exception e) {
            log.warn("getUrlWhileList [{}] mcc config exception. ", urls, e);
        }
        return Collections.emptyList();
    }

    public static boolean isNewPickGrayStore(long storeId) {
        try {
            return true;
        } catch (Exception e) {
            log.error("get isNewPickGrayStore error", e);
            return false;
        }
    }


    /**
     * 获取歪马商品侧的温度属性与拣货侧的温度属性映射关系
     * 拣货的配置，统一存放在OIO里
     */
    public static Map<String, String> getPropertyColorMap() {
        Map<String, String> defaultMap = new HashMap<>();
        defaultMap.put("冰冻","#198CFF");
        defaultMap.put("冷藏","#198CFF");
        defaultMap.put("冰镇","#198CFF");
        defaultMap.put("常温","#FF6A00");
        defaultMap.put("一半冰一半不冰","#198CFF");
        defaultMap.put("自定义：备注","#999999");
        defaultMap.put("需要红酒开瓶器","#FF192D");

        return Lion.getConfigRepository("com.sankuai.shangou.logistics.oio").getMap("drunk.horse.property.color.map", String.class, defaultMap);
    }

    public static boolean isReadGoodsGrayStore(Long offlineStoreId) {
        if (offlineStoreId == null) {
            log.warn("offlineStoreId is null");
            return false;
        }

        List<Long> storeList = getConfigRepository("com.sankuai.waimai.sc.pickselectservice")
                .getList("read.goods.gray.store", Long.class, Collections.emptyList());

        if (CollectionUtils.isEmpty(storeList)) {
            return false;
        }

        if (storeList.size() == 1 && Objects.equals(storeList.get(0), -1L)) {
            return true;
        }

        return storeList.contains(offlineStoreId);
    }
}
