package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.*;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/8 16:43
 * @Description:
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "渠道信息")
public class ChannelVO {

    /**
     * 渠道编码
     */
    @FieldDoc(description = "渠道编码")
    private String channelId;

    /**
     * 渠道名称
     */
    @FieldDoc(description = "渠道名称")
    private String channelName;

    /**
     * 是否标准API渠道
     */
    @FieldDoc(description = "是否标准渠道")
    private Integer standard = 1;
}
