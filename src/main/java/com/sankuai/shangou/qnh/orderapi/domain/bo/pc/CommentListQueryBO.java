package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.sgfulfillment.comment.thrift.dto.CommentListQueryRequest;
import com.sankuai.sgfulfillment.comment.thrift.enums.CommentContactEnum;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评价列表查询条件BO
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
public class CommentListQueryBO {
    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id列表
     */
    private List<Long> poiIds;

    /**
     * 渠道id列表
     */
    private List<Integer> channelIds;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 评价级别
     */
    private String commentLevel;

    /**
     * 回复状态列表
     */
    private List<String> replyStatusList;

    /**
     * 评价内容类型
     */
    private String commentContentType;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 每页记录数
     */
    private Integer pageSize;

    /**
     * 导出场景
     */
    private boolean exportScene;

    /**
     * 渠道订单编号
     */
    private String channelOrderId;

    /**
     * 评价状态
     */
    private Integer isValid;

    public CommentListQueryRequest convertToCommentListQueryRequest() {
        CommentListQueryRequest request = new CommentListQueryRequest();
        request.setTenantId(this.tenantId);
        request.setStoreIds(this.poiIds);
        request.setChannelIds(this.channelIds);
        request.setStartTime(this.startTime != null ?
                DateUtil.formatLocalDateTime(this.startTime, DateUtil.YYYY_MM_DD_HH_MM_SS) : null);
        request.setEndTime(this.endTime != null ?
                DateUtil.formatLocalDateTime(this.endTime, DateUtil.YYYY_MM_DD_HH_MM_SS) : null);
        request.setCommentLevel(this.commentLevel);
        request.setReplyStatusList(this.replyStatusList);
        request.setCommentContentType(this.commentContentType);
        request.setPageNum(this.page);
        request.setPageSize(this.pageSize);
        request.setExportScene(this.exportScene);
        //web端没有过滤是否已联系。
        request.setCommentContactType(CommentContactEnum.ALL.getValue());
        request.setChannelOrderId(this.channelOrderId);
        request.setIsValid(this.isValid);
        return request;
    }
}
