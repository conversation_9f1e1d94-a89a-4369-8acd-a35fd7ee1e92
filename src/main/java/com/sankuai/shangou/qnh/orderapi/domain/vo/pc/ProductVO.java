package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/10
 * desc: 商品信息
 */
@TypeDoc(
        description = "商品信息"
)
@ApiModel("商品信息")
@Data
public class ProductVO {

    @FieldDoc(
            description = "skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "skuId")
    private String skuId;

    @FieldDoc(
            description = "upc码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "upc码", required = true)
    private String upcCode;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "商品图片URL", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片URL", required = true)
    private String picUrl;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格", required = true)
    private String specification;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售卖单位", required = true)
    private String sellUnit;

    @FieldDoc(
            description = "原总价  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "原总价  单位:分", required = true)
    private Integer originalTotalPrice;

    @FieldDoc(
            description = "实付金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "实付金额  单位:分", required = true)
    private Integer totalPayAmount;

    @FieldDoc(
            description = "优惠金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "优惠金额  单位:分", required = true)
    private Integer totalDiscountAmount;

    @FieldDoc(
            description = "单价  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "单价  单位:分", required = true)
    private Integer unitPrice;

    @FieldDoc(
            description = "购买数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "购买数量", required = true)
    private Integer count;

    @FieldDoc(
            description = "是否退货 0-否 1-是", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否退货 0-否 1-是", required = true)
    private Integer isRefund;

    @FieldDoc(
            description = "申请取消数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "申请取消数量", required = true)
    private Integer refundCount;

    @FieldDoc(
            description = "摊位名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "摊位名称", required = true)
    private String boothName;

    @FieldDoc(
            description = "线下售卖价格  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "线下售卖价格  单位:分", required = true)
    private Integer offlinePrice;

    @FieldDoc(
            description = "摊位结算金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "摊位结算金额  单位:分", required = true)
    private Integer stallSettleAmt;

    @FieldDoc(
            description = "实际拣货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "实际拣货数量", required = true)
    private Integer realQuantity;

    @FieldDoc(
            description = "商家在渠道的skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商家在渠道的skuId")
    private String customerSkuId;


    @FieldDoc(
            description = "商品标签信息列表(1履约标签、2拣货标签)、客户端将type用于修改样式、新标签需要使用tagInfos", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品标签信息列表(1履约标签、2拣货标签)、客户端将type用于修改样式、新标签需要使用tagInfos")
    private List<TagInfoVO> tagInfoList;

    @FieldDoc(
            description = "商品标签信息列表(除1履约标签、2拣货标签之外的其它新标签都用此对象)  3商品属性标签", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品标签信息列表(除1履约标签、2拣货标签之外的其它新标签都用此对象)  3商品属性标签")
    private List<TagInfoVO> tagInfos;
}
