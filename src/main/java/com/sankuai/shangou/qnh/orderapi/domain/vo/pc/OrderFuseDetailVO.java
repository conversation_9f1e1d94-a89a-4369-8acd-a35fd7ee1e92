package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.bo.ReturnDataPoiBo;
import com.sankuai.shangou.qnh.orderapi.domain.result.ReturnDataSecurity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2022/12/8 16:13
 * @Description:
 */
@TypeDoc(
        description = "融合订单详情"
)
@ApiModel("融合订单详情")
@Setter
@Getter
@NoArgsConstructor
@ToString
public class OrderFuseDetailVO implements ReturnDataSecurity {


    /**
     * 基础信息
     */
    private BaseInfoVO baseInfo;

    /**
     * 财务信息
     */
    private BillInfoVO billInfo;

    /**
     * 商品列表
     */
    private List<ItemInfoVO> itemInfo;

    private List<OrderTagVO> orderTag;

    private List<Long> labelIdList;

    /**
     * 用户标签信息
     */
    private List<TagInfoVO> userTags;



    /**
     * 促销信息
     */
    private List<PromotionInfoVO> promotionInfo;

    /**
     * 操作日志
     */
    private List<OperateLogVO> operateLog;


    /**
     * 配置详情
     */
    private List<DeliveryDetailVO> deliveryDetail;


    /**
     * 订单是否有调整
     */
    @FieldDoc(
            description = "订单是否有调整"
    )
    @ApiModelProperty(value = "订单是否有调整", required = true)
    private Boolean hasOrderAdjustLog;

    /**
     * 配送信息列表
     */
    private List<DeliveryInfoVo> deliveryInfoList;

    /**
     * 拣货复核照片
     */
    @FieldDoc(
            description = "拣货复核照片URL"
    )
    @ApiModelProperty(value = "拣货复核照片URL", required = false)
    private String pickingCheckPictureUrl;

    @FieldDoc(
            description = "拣货复核照片URL（支持多张照片）"
    )
    @ApiModelProperty(value = "拣货复核照片URL（支持多张照片）", required = false)
    private List<String> pickingCheckPictureUrlList;

    /**
     * 订单可操作列表：10-接单操作，20-完成拣货，30-补打小票，40-全单退款，50-部分退款，60-收到退货，70-退差价
     */
    private List<Integer> orderCouldOperateItems;

    @FieldDoc(
            description = "是否展示拣货记录按钮,1展示"
    )
    @ApiModelProperty(value = "是否展示拣货记录按钮,1展示", required = false)
    private Integer pickRecordButton;

    @FieldDoc(
            description = "是否一门店多摊位租户 1是"
    )
    @ApiModelProperty(value = "是否一门店多摊位租户 1是", required = false)
    private Integer isOneStoreMoreBooth;

    @Override
    public List<ReturnDataPoiBo> fetchReturnDataPoiBoList() {
        if (Objects.nonNull(this.baseInfo)) {
            ReturnDataPoiBo poiBo = ReturnDataPoiBo.builder().poiId(this.baseInfo.getPoiId())
                    .dispatchShopId(this.baseInfo.getDispatchShopId()).build();
            return Lists.newArrayList(poiBo);
        }
        return null;
    }
}
