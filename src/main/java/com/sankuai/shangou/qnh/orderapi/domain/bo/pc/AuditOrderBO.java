package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.AfterSaleRecordVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderInfoVo;
import com.meituan.shangou.saas.order.management.client.utils.BusinessIdTracer;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.AfterSaleRecord;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderInfoDTO;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AuditOrderVO;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChanelOrderApplicantEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelOrderRefundTypeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelOrderStatusEnum;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/7 11:37
 * @Description:
 */
@Setter
@Getter
@ToString
public class AuditOrderBO {

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 渠道编码
     */
    private int channelId;

    /**
     *  渠道名称
     */
    private String channelName;

    /**
     * 门店ID
     */
    private Long poiId;

    /**
     * 门店名称
     */
    private String poiName;


    /**
     * 退款类型
     */
    private String refundType;


    /**
     * 退款类型ID
     */
    private int refundTagId;


    /**
     * 退款金额
     */
    private double refundAmt;


    /**
     * 实付金额
     */
    private double paidAmt;


    /**
     * 订单状态
     */
    private String status;


    /**
     * 订单创建时间
     */
    private Date createTime;

    /**
     * 收货人姓名
     */
    private String receiverName;


    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 收货人地址
     */
    private String receiveAddress;


    /**
     * 发起者
     */
    private String applicant;

    private String serviceId;

    private int afsApplyType;

    private String refundReason;

    private int currentAfsType;

    private long orderSerialNumber;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 是否为美团名酒馆订单，true：是
     */
    private Boolean isMtFamousTavern;

    /**
     * 是否为美团发财酒订单，true：是
     */
    private Boolean isFacaiWine;


    public AuditOrderBO(OrderInfoDTO detail) {
        orderId = detail.getChannelOrderId();
        channelId = detail.getChannelId();
        channelName = detail.getChannelName();
        poiId = detail.getShopId();
        poiName = detail.getShopName();
        refundType = ConverterUtils.nonNullConvert(ChannelOrderRefundTypeEnum.getByCode(detail.getAuditingRefundTagId()), s -> s.getDesc(), detail.getAuditingRefundTagDesc());
        refundAmt =  detail.getRefundAmt() / 100.0;
        paidAmt = detail.getActualPayAmt() / 100.0;
        status = ConverterUtils.nonNullConvert(ChannelOrderStatusEnum.getByCode(detail.getChannelOrderStatus()), s->s.getDesc(),detail.getChannelOrderStatusDesc());
        createTime = new Date(detail.getCreateTime());
        receiverName = detail.getReceiverName();
        receiverPhone = detail.getReceiverPhone();
        receiveAddress = detail.getReceiveAddress();
        refundTagId = detail.getAuditingRefundTagId();
        currentAfsType = detail.getCurrentAfsType();
        orderSerialNumber = detail.getOrderSerialNumber();

        List<AfterSaleRecord> afterSaleRecords = detail.getAfterSaleRecords();
        if (CollectionUtils.isNotEmpty(afterSaleRecords)) {
            Collections.sort(afterSaleRecords, (a,b) -> (int)((b.getCreateTime() - a.getCreateTime())/1000));
            AfterSaleRecord afterSaleRecord = afterSaleRecords.get(0);
            serviceId = afterSaleRecord.getAfterSaleId();
            afsApplyType = afterSaleRecord.getAfsApplyType();
            applicant = ConverterUtils.nonNullConvert(ChanelOrderApplicantEnum.getByCode(afterSaleRecord.getWhoApplyType()), ChanelOrderApplicantEnum::getDesc);
            refundReason = afterSaleRecord.getApplyReason();

        }
    }

    public AuditOrderBO(OrderInfoVo detail) {
        orderId = detail.getChannelOrderId();
        channelId = detail.getChannelId();
        channelName = detail.getChannelName();
        poiId = detail.getShopId();
        poiName = detail.getShopName();
        refundType = ConverterUtils.nonNullConvert(ChannelOrderRefundTypeEnum.getByCode(detail.getAuditingRefundTagId()), s -> s.getDesc(), detail.getAuditingRefundTagDesc());
        refundAmt =  detail.getRefundAmt() / 100.0;
        paidAmt = detail.getActualPayAmt() / 100.0;
        status = ConverterUtils.nonNullConvert(ChannelOrderStatusEnum.getByCode(detail.getChannelOrderStatus()), s->s.getDesc(),detail.getChannelOrderStatusDesc());
        createTime = new Date(detail.getCreateTime());
        receiverName = detail.getReceiverName();
        receiverPhone = detail.getReceiverPhone();
        if (BusinessIdTracer.isDrunkHorseTenant(detail.getTenantId())){
            // 歪马租户，在审批页不展示手机号
            receiverPhone = "";
        }
        receiveAddress = detail.getReceiveAddress();
        refundTagId = detail.getAuditingRefundTagId();
        currentAfsType = detail.getCurrentAfsType();
        orderSerialNumber = detail.getOrderSerialNumber();
        warehouseId = detail.getWarehouseId();
        warehouseName = detail.getWarehouseName();
        isMtFamousTavern = detail.getIsMtFamousTavern();
        isFacaiWine = detail.getIsFacaiWine();


        List<AfterSaleRecordVo> afterSaleRecords = detail.getAfterSaleRecords();
        if (CollectionUtils.isNotEmpty(afterSaleRecords)) {
            Collections.sort(afterSaleRecords, (a,b) -> (int)((b.getCreateTime() - a.getCreateTime())/1000));
            AfterSaleRecordVo afterSaleRecord = afterSaleRecords.get(0);
            serviceId = afterSaleRecord.getAfterSaleId();
            afsApplyType = afterSaleRecord.getAfsApplyType();
            applicant = ConverterUtils.nonNullConvert(ChanelOrderApplicantEnum.getByCode(afterSaleRecord.getWhoApplyType()), ChanelOrderApplicantEnum::getDesc);
            refundReason = afterSaleRecord.getApplyReason();
        }
    }


    public AuditOrderVO toChannelOrderVO () {
        AuditOrderVO channelOrderVO = new AuditOrderVO();

        channelOrderVO.setChannelId(String.valueOf(channelId));
        channelOrderVO.setChannelName(channelName);
        channelOrderVO.setOrderId(String.valueOf(orderId));
        channelOrderVO.setRefundType(refundType);
        channelOrderVO.setRefundTagId(String.valueOf(refundTagId));
        channelOrderVO.setPoiName(poiName);
        channelOrderVO.setPaidAmt(ConverterUtils.formatMoney(paidAmt));
        channelOrderVO.setRefundAmt(refundTagId == 200 ? "全部金额" :  ConverterUtils.formatMoney(refundAmt));
        channelOrderVO.setStatus(status);
        channelOrderVO.setCreateTime(DateFormatUtils.format(createTime, Constants.DateFormats.SHOW_FORMAT));
        channelOrderVO.setReceiverName(receiverName);
        channelOrderVO.setReceiverPhone(receiverPhone);
        channelOrderVO.setReceiveAddress(receiveAddress);
        channelOrderVO.setServiceId(serviceId);
        channelOrderVO.setAfsApplyType(String.valueOf(afsApplyType));
        channelOrderVO.setApplicant(applicant);
        channelOrderVO.setCurrentAfsType(String.valueOf(currentAfsType));
        channelOrderVO.setRefundReason(refundReason);
        channelOrderVO.setOrderSerialNumber(orderSerialNumber);
        channelOrderVO.setWarehouseId(warehouseId);
        channelOrderVO.setWarehouseName(warehouseName);
        channelOrderVO.setIsMtFamousTavern(isMtFamousTavern);
        channelOrderVO.setIsFacaiWine(isFacaiWine);
        return channelOrderVO;
    }

}
