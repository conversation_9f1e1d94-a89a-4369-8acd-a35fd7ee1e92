package com.sankuai.shangou.qnh.orderapi.enums.store;

import com.sankuai.shangou.qnh.orderapi.remote.TenantRemoteService;
import com.sankuai.shangou.qnh.orderapi.context.store.SpringAppContext;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 渠道枚举类
 */
@AllArgsConstructor
@Getter
@Slf4j
public enum ChannelTypeEnum {
    MEITUAN(100, "美团外卖"),
    ELEM(200, "饿了么"),
    JD2HOME(300, "京东到家"),
    YOU_ZAN(500, "有赞"),
    MT_DRUNK_HORSE(700, "微商城"),
    QUAN_QIU_WA(800, "全球蛙"),
    SELF_CHANNEL(900, "自有渠道");
    /**
     * 渠道编码
     */
    private int channelId;
    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 注：此方法不支持私有渠道，已废弃
     */
    @Deprecated
    public static ChannelTypeEnum findByChannelId(int channelId) {
        for (ChannelTypeEnum channelTypeEnum : ChannelTypeEnum.values()) {
            if (channelTypeEnum.getChannelId() == channelId) {
                return channelTypeEnum;
            }
        }
        return null;
    }


    /**
     * 通过渠道ID查询渠道名称
     *
     * 注：此方法为兼容性方法，兼容之前通过枚举获取名称的逻辑，因此不抛出异常
     */
    public static String findChannelNameByChannelId(int channelId) {
        // 默认走枚举类
        for (ChannelTypeEnum channelTypeEnum : ChannelTypeEnum.values()) {
            if (channelTypeEnum.getChannelId() == channelId) {
                return channelTypeEnum.getChannelName();
            }
        }
        // 走不到时 fallback 为查询
        try {
            log.info("findChannelNameByChannelId fallback 为远程查询");
            TenantRemoteService tenantWrapper = SpringAppContext.AppContext.getBean(TenantRemoteService.class);
            return tenantWrapper.queryChannelNameByChannelId(channelId);
        } catch (Throwable e) {
            log.error("findChannelNameByChannelId 远程查询异常", e);
        }

        return null;
    }

}
