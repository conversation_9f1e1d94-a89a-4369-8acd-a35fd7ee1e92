package com.sankuai.shangou.qnh.orderapi.utils.pc;

import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.OrderViewStatusEnum;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ConvertUtils {

    public static Integer getOrderViewStatusByExceptionOrderSubType(DeliveryExceptionSubTypeEnum deliveryExceptionSubTypeEnum){
        if (deliveryExceptionSubTypeEnum == null) {
            return null;
        }
        Integer orderViewStatus = null;
        switch (deliveryExceptionSubTypeEnum) {
            case NO_RIDER_ACCEPT:
                orderViewStatus = OrderViewStatusEnum.NO_RIDER_ACCEPT.getCode();
                break;
            case NO_ARRIVAL_STORE:
                orderViewStatus = OrderViewStatusEnum.NO_ARRIVAL_STORE.getCode();
                break;
            case NO_RIDER_TAKE_GOODS:
                orderViewStatus = OrderViewStatusEnum.NO_RIDER_TAKE_GOODS.getCode();
                break;
            case DELIVERY_TIMEOUT:
                orderViewStatus = OrderViewStatusEnum.DELIVERY_TIMEOUT.getCode();
                break;
            case SYSTEM_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.SYSTEM_EXCEPTION.getCode();
                break;
            case REPORT_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.REPORT_EXCEPTION.getCode();
                break;
            case TAKE_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.TAKE_EXCEPTION.getCode();
                break;
            default:
                return null;
        }
        return orderViewStatus;
    }

    public static Integer convertChannelId2OrderBizType(Integer channelId) {
        Integer resultBizType = null;
        if (channelId != null) {
            resultBizType = DynamicOrderBizType.channelId2OrderBizTypeValue(channelId);

            if (resultBizType == null) {
                log.error("未知渠道类型:{}", channelId);
            }
        }

        return resultBizType;
    }

}
