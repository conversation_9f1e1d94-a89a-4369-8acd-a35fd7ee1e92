package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.tenant.thrift.dto.DistrictDto;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.area.CityInfoVO;
import lombok.*;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/16 11:16
 * @Description:
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class AreaBO {

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 地区编码
     */
    private String areaCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 地区名称
     */
    private String areaName;



    public AreaBO(DistrictDto districtDto) {

        provinceCode = ConverterUtils.nonNullConvert(districtDto.getProvinceId(),String::valueOf);
        cityCode = ConverterUtils.nonNullConvert(districtDto.getCityId(),String::valueOf);
        areaCode = ConverterUtils.nonNullConvert(districtDto.getAreaId(),String::valueOf);

        provinceName = districtDto.getProvinceName();
        cityName = districtDto.getCityName();
        areaName = districtDto.getAreaName();
    }



    public CityInfoVO toCityInfoVO() {
        CityInfoVO cityInfoVO = new CityInfoVO();
        cityInfoVO.setCode(provinceCode+"_"+cityCode);
        cityInfoVO.setName(cityName);
        return cityInfoVO;
    }


}
