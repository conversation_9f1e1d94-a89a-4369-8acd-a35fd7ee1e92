package com.sankuai.shangou.qnh.orderapi.utils.store;

import com.sankuai.meituan.reco.store.management.enums.TaskType;
import com.sankuai.meituan.reco.store.management.receipt.thrift.enums.ContainerTypeEnum;
import com.sankuai.meituan.reco.store.management.receipt.thrift.enums.EntityTypeEnum;
import com.sankuai.meituan.reco.store.management.receipt.thrift.enums.OperationTypeEnum;
import com.sankuai.meituan.reco.store.management.receipt.thrift.enums.TransferTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.thrift.TException;

import java.math.BigDecimal;
import java.util.Collection;

/**
 * Created by yang<PERSON> on 18/9/28.
 */
public class CommonParamVerify {

    private static final int MAX_QUANTITY_SCALE = 4;

    private CommonParamVerify() {
    }

    public static void boolCheck(boolean bool, String msg) {
        if (!bool) {
            throw new IllegalArgumentException(msg);
        }
    }

    public static void checkLessThanOrEqualZero(long value, String msg) {
        if (value <= 0) {
            throw new IllegalArgumentException(msg);
        }
    }

    public static void checkNullObject(Object object, String msg) {
        if (object == null) {
            throw new IllegalArgumentException(msg);
        }
    }

    public static void checkCollection(Collection coll, String msg) {
        if (CollectionUtils.isEmpty(coll)) {
            throw new IllegalArgumentException(msg);
        }
    }

    public static void checkArray(Object[] array, String msg) {
        if (ArrayUtils.isEmpty(array)) {
            throw new IllegalArgumentException(msg);
        }
    }

    public static void checkNullRequest(Object request) throws TException {
        if (request == null) {
            throw new TException("request is null");
        }
    }

    public static void checkTenantId(long tenantId) {
        if (tenantId <= 0) {
            throw new IllegalArgumentException("tenantId <= 0");
        }
    }

    public static void checkEntityId(long entityId) {
        if (entityId <= 0) {
            throw new IllegalArgumentException("entityId <= 0");
        }
    }

    public static void checkStoreId(long storeId) {
        if (storeId <= 0) {
            throw new IllegalArgumentException("storeId <= 0");
        }
    }

    public static void checkEntityType(int entityType) {
        if (!EntityTypeEnum.contains(entityType)) {
            throw new IllegalArgumentException("entityType = " + entityType + ", is not valid");
        }
    }

    public static void checkUserId(long userId) {
        if (userId < 0) {
            throw new IllegalArgumentException("userId < 0");
        }
    }

    public static void checkUserId(String userId) {
        if (StringUtils.isEmpty(userId)) {
            throw new IllegalArgumentException("userId is empty");
        }
    }

    public static void checkQuantityString(String quantity) {
        if (StringUtils.isEmpty(quantity)) {
            throw new IllegalArgumentException("quantity string is empty");
        }

        try {
            BigDecimal val = new BigDecimal(quantity);

            if (val.compareTo(BigDecimal.ZERO) <= 0) {
                throw new IllegalArgumentException("negative quantity, quantity = " + quantity);
            }

            if (val.scale() > MAX_QUANTITY_SCALE) {
                throw new IllegalArgumentException("quantity scale is too long, quantity" + quantity);
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("quantity string is not valid, cause = " + e.getMessage());
        }
    }

    public static void checkContainerType(int containerType) {
        if (!ContainerTypeEnum.contains(containerType)) {
            throw new IllegalArgumentException("containerType " + containerType + ", is not valid");
        }
    }

    public static void checkContainerId(long containerId) {
        if (containerId <= 0) {
            throw new IllegalArgumentException("containerId <= 0");
        }
    }

    public static void checkSkuId(String skuId) {
        if (StringUtils.isEmpty(skuId)) {
            throw new IllegalArgumentException("skuId is empty");
        }
    }

    public static void checkUpcCode(String upcCode) {
        if (StringUtils.isEmpty(upcCode)) {
            throw new IllegalArgumentException("upcCode is empty");
        }
    }

    public static void checkSkuName(String skuName) {
        if (StringUtils.isEmpty(skuName)) {
            throw new IllegalArgumentException("skuName is empty");
        }
    }

    public static void checkUserName(String userName) {
        if (StringUtils.isEmpty(userName)) {
            throw new IllegalArgumentException("userName is empty");
        }
    }

    public static void checkUnit(String unit) {
        if (StringUtils.isEmpty(unit)) {
            throw new IllegalArgumentException("unit is empty");
        }
    }

    public static void checkWeightString(String weight) {
        if (StringUtils.isEmpty(weight)) {
            throw new IllegalArgumentException("weight string is empty");
        }

        try {
            BigDecimal val = new BigDecimal(weight);

            if (val.compareTo(BigDecimal.ZERO) < 0) {
                throw new IllegalArgumentException("negative weight, weight = " + weight);
            }
            if (val.scale() > MAX_QUANTITY_SCALE) {
                throw new IllegalArgumentException("quantity scale is too long, weight" + weight);
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("weight string is not valid, cause = " + e.getMessage());
        }
    }

    public static void checkOrderNo(String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            throw new IllegalArgumentException("orderNo is empty");
        }
    }

    public static void checkPage(int page) {
        if (page <= 0) {
            throw new IllegalArgumentException("page <= 0");
        }
    }

    public static void checkTransferType(int transferType) {
        if (!TransferTypeEnum.contains(transferType)) {
            throw new IllegalArgumentException("invalid transferType, transferType = " + transferType);
        }
    }

    public static void checkTimestamp(long timestamp) {
        if (timestamp <= 0 || String.valueOf(timestamp).length() < 13) {
            throw new IllegalArgumentException("invalid timestamp, timestamp = " + timestamp);
        }
    }

    public static void checkLocationCode(String locationCode) {
        if (StringUtils.isEmpty(locationCode)) {
            throw new IllegalArgumentException("locationCode is empty");
        }
    }

    public static void checkOperationType(int operationType) {
        if (OperationTypeEnum.getEnumByValue(operationType) == OperationTypeEnum.UNKNOWN) {
            throw new IllegalArgumentException("operationType undefined");
        }
    }

    public static void checkEmpty(String value, String msg) {
        if (StringUtils.isEmpty(value)) {
            throw new IllegalArgumentException(msg);
        }
    }

    public static void checkTaskType(int taskType) {
        if (TaskType.getInstance(taskType) == null) {
            throw new IllegalArgumentException("taskType=" + taskType + " undefined");
        }
    }


    public static void checkTaskNo(String taskNo){
        if(StringUtils.isEmpty(taskNo)){
            throw new IllegalArgumentException("taskNo is empty");
        }
    }

}
