package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderPaymentVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Date: 2024-05-28 11:08
 * @Description:
 */
@TypeDoc(
        description = "支付信息"
)
@ApiModel("支付信息")
@Data
public class PaymentVO {

    @FieldDoc(
            description = "支付名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "支付名称", required = false)
    private String channelPayName;

    @FieldDoc(
            description = "支付金额", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "支付金额", required = false)
    private Integer channelPayment;

    public PaymentVO(OrderPaymentVO paymentVO){
        this.channelPayment = paymentVO.getPayAmt();
        this.channelPayName = paymentVO.getChannelPayName();
    }

}
