package com.sankuai.shangou.qnh.orderapi.enums.app;


/**
 * 配送异常码枚举.
 *
 * <AUTHOR>
 * @since 2021/4/19 18:59
 */
public enum DeliveryExceptionCodeEnum {

    NO_RIDE_ACCEPT(1, "长时间无骑手接单，建议增加小费吸引骑手接单"),
    NO_ARRIVAL_STORE(2, "骑手接单后长时间未到店，建议点击查看详情去查看骑手位置，或电讯骑手到店时间"),
    DELIVERY_TIME_OUT(3, "骑手超时未送达，建议点击查看详情去查看骑手位置，或电讯骑手配送进度"),
    CHANNEL_CANCEL(4, "配送取消，建议点击处理异常去重新发起配送"),
    SEND_DELIVERY_FAIL(5, "配送发单失败，建议点击处理异常去重新手动发单"),
    SEND_CANCEL_FAIL(6, "配送取消失败"),
    ESTIMATED_TIME_OUT(7, "当前订单已超过预计送达时间，建议联系骑手加快配送进度"),

    SYNC_CREATE_FAIL(10, "配送发单失败，建议点击处理异常去重新手动发单"),
    SYNC_CANCEL_FAIL(11, "配送取消失败，请点击处理异常去手动取消配送"),

    RIDER_PICK_TIME_OUT(12, "骑手到店超时未取货"),

    DELIVERY_STATUS_ROLLBACK(13, "发生骑手取消或转单，如长时间无新骑手接单建议取消配送后重新发起"),

    SELF_DELIVERY(14,"配送运力不足，请重点关注"),

    /**
     * 异常上报
     */
    RIDER_REPORT_FAIL(15, "发生骑手异常上报，请及时进入配送详情审核"),

    /**
     * 取货失败待审核
     */
    RIDER_TAKE_FAIL_AUDITING(16, "请尽快与骑手联系"),

    /**
     * 取货失败
     */
    RIDER_TAKE_FAIL(17, "请尽快与骑手联系"),

    /**
     * 二次呼叫
     */
    RECALL_RIDER_FAIL(18, "发生骑手异常上报，请及时查看处理"),

    /**
     * 二次呼叫以及转自送
     */
    RECALL_SELF_RIDER_FAIL(19, "发生骑手异常上报，请及时查看处理"),

    /**
     * elm配送异常上报
     */
    DELIVERY_EXCEPTION_UPLOAD(20,"发生骑手异常上报，请及时查看处理"),

    LACK_MONEY(98, " 余额不足，建议先前往配送管理>配送设置>钱包进行充值，再点击处理异常去重新发起配送"),

    SYSTEM_EXCEPTION_DELIVERY_TIME_OUT(100, "企客系统异常导致发配失败，建议自配或等待系统恢复"),



    UNKNOWN(10000, "出现未知异常，建议点击[去处理异常]查看详情"),


    ;

    private final int code;
    private final String desc;


    DeliveryExceptionCodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DeliveryExceptionCodeEnum enumOf(int value) {
        for (DeliveryExceptionCodeEnum each : values()) {
            if (each.getCode() == value) {
                return each;
            }
        }

        return UNKNOWN;
    }


    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
