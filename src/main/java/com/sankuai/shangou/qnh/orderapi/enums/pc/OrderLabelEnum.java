package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.sankuai.shangou.qnh.orderapi.domain.result.pc.MultiStageOptionable;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiChildOption;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOptionMultiStage;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 * @Author: <EMAIL>
 * @Date: 2022/12/03 19:54
 * @Description:tag类型
 */
@Getter
public enum OrderLabelEnum implements MultiStageOptionable {
    POS(2L,"pos","核销"),
    OVER_SOLD(201L,2L,"overSold","超卖"),

    AFTER_SALE(3L, "afterSale", "售后"),
    REFUND(301L, 3L, "refund", "存在退款单"),
    EXCHANGE(302L, 3L, "exchange","换货"),
    NO_REFUND(303L, 3L, "noRefund","无有效退单"),
    PART_REFUND(304L, 3L, "partRefund", "已部分退"),
    ALL_REFUND(305L, 3L, "allRefund","已全单退"),

    SUB_CHANNEL(4L,"subBusinessChannel","子渠道"),
    WJTZ(401L,4L,"fitSurvival","物竞天择"),
    TX(402L,4L,"selection","天选"),

    SPECIAL_ORDER_TYPE(5L, "specialOrderType","特殊订单类型"),
    OPEN_DELIVERY(501L,5L,"openDelivery","营业即送"),
    GROUP_PURCHASE(502L,5L,"groupPurchase","团购订单"),
    INTEGRATED_MARKETING(504L, 5L, "integratedMarketing", "整合营销"),
    CONTAIN_COMPOSE_PRODUCT(503L, 5L, "containComposeProduct", "包含组合品"),
    ORDER_INFO_CHANGE(505L, 5L, "orderInfoChange", "有变更订单"),
    CONTAIN_GIFT_PRODUCT(506L, 5L, "containGiftProduct", "包含赠品订单"),
    FAST_DELIVERY(507L, 5L, "fastDelivery", "闪电送订单"),
    DISPATCH_ORDER(508L, 5L, "dispatchOrder", "转单订单"),

    DRUNK_HORSE_LABLE(6L,"drunkhorseLable","歪马标签"),
    MT_FAMOUS_TAVERN(509L, 6L, "mtFamousTavern", "美团名酒馆"),
    MT_FACAI_WINE(510L, 6L, "FA_CAI_WINE", "发财酒订单"),

    VALID_ORDER(7L, "validOrder","有效订单"),

    ORDER_PRODUCT_LABEL(8L,"productLabel","商品标签"),
    SEVEN_RETURN(801L,8L,"sevenReturn","7天无理由"),
    COMPENSATION_FOR_NOT_ICE(802L,8L, "compensationForNotIce","不冰必赔"),
    COMPENSATION_FOR_THAW(803L,8L, "compensationForThaw","融化必赔"),
    COMPENSATION_FOR_BROKEN(804L,8L, "compensationForBroken","坏必赔"),
    PRIVACY_GOODS(805L,8L, "privacyGoods","隐私商品"),
    COMPENSATION_FOR_PROMISE(806L,8L, "compensationForPromise","新鲜承诺"),

    ORDER_OFFICE_PRICE_IS_NULL(9L, "orderOfficePriceIsNull","进货价为空")
    ;

    OrderLabelEnum(Long id, Long parentId, String code, String desc){
        this.id = id;
        this.parentId = parentId;
        this.code = code;
        this.desc = desc;
    }

    public static OrderLabelEnum getByCode(String code){
        for (OrderLabelEnum label: OrderLabelEnum.values()){
            if (Objects.equals(label.code, code)){
                return label;
            }
        }
        return null;
    }

    public static OrderLabelEnum getById(Integer id){
        for (OrderLabelEnum label: OrderLabelEnum.values()){
            if (Objects.equals(label.id, id)){
                return label;
            }
        }
        return null;
    }

    OrderLabelEnum(Long id, String code, String desc){
        this(id, null, code, desc);
    }

    private Long id;

    private Long parentId;

    private String code;

    private String desc;


    @Override
    public List<UiOptionMultiStage> toOptions() {
        return Arrays.asList(OrderLabelEnum.values()).stream().filter(item -> Objects.isNull(item.getParentId()))
                .map(e -> new UiOptionMultiStage(Math.toIntExact(e.getId()), e.getCode(), e.getDesc(), getChildOptionList(e.getId()))).collect(Collectors.toList());
    }

    public List<UiChildOption> getChildOptionList(Long parentId){
        List<UiChildOption> childOptionList = new ArrayList<>();
        for (OrderLabelEnum label: OrderLabelEnum.values()){
            if(Objects.nonNull(label.getParentId()) && parentId.equals(label.getParentId())){
                childOptionList.add(new UiChildOption(Math.toIntExact(label.getId()), label.getCode(), Math.toIntExact(label.getParentId()), label.getDesc()));
            }
        }
        return childOptionList;
    }

    @Override
    public List<UiOptionMultiStage> toOptions(List<MultiStageOptionable> orderLabelEnumList) {
        if (CollectionUtils.isEmpty(orderLabelEnumList)) {
            return null;
        }
        // 实现这个方法
        return orderLabelEnumList.stream().filter(item -> item instanceof OrderLabelEnum)
                .map(item -> (OrderLabelEnum)item).map(e -> new UiOptionMultiStage(Math.toIntExact(e.getId()),
                        e.getCode(), e.getDesc(), getChildOptionList(e.getId())))
                .collect(Collectors.toList());
    }

}

