package com.sankuai.shangou.qnh.orderapi.utils.pc;

import com.sankuai.meituan.reco.store.management.enums.TaskType;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @Author: <EMAIL>
 * @Date: 2018/12/29 16:34
 * @Description:
 */
@Slf4j
public class ConverterUtils {


    /**
     * 包装boolean类型,供前端使用
     * @param b
     * @return
     */
    public static int wrap(boolean b) {
        return b ? 1 : 0;
    }


    /**
     *
     * @param c
     * @return
     */
    public static int size(Collection c) {
        if (CollectionUtils.isEmpty(c)) {
            return 0;
        }

        return CollectionUtils.size(c);
    }


    /**
     * 包装前端的 "是/否" 参数
     * @param option
     * @return
     */
    public static boolean wrapRequestOption(String option) {
        return StringUtils.equalsIgnoreCase(option,"y");
    }


    /**
     * 类型转换(防止NPE),如果接收对象为基本类型,请指定基本类型的默认值
     * @param src
     * @param mapFunction
     * @param defaultValue
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F,T> T nonNullConvert(F src, Function<F,T> mapFunction, T defaultValue) {
        try {
            return Optional.ofNullable(src).map(mapFunction).orElse(defaultValue);
        } catch (Exception e) {
            log.warn("convert error", e);
            return defaultValue;
        }

    }


    public static <T> T nonNullConvert(String src, Function<String,T> mapFunction, T defaultValue) {
        try {
            return Optional.ofNullable(src).filter(StringUtils::isNotEmpty).map(mapFunction).orElse(defaultValue);
        } catch (Exception e) {
            log.warn("convert error", e);
            return defaultValue;
        }

    }


    /**
     * 解析开始时间
     * @param startDate
     * @return
     */
    public static Date parseStartDate(String startDate) {
        return ConverterUtils.nonNullConvert(startDate, t -> Constants.Functions.DATE_PARSE_FUNCTION.apply(t.trim()+"000000"));
    }

    /**
     * 解析开始时间
     * @param startDate
     * @return
     */
    public static Date parseStartDateDay(String startDate) {
        return ConverterUtils.nonNullConvert(startDate, t -> Constants.Functions.DATE_DAY_PARSE_FUNCTION.apply(t.trim()));
    }


    /**
     * 解析结束时间
     * @param endDate
     * @return
     */
    public static Date parseEndDate(String endDate) {
        return ConverterUtils.nonNullConvert(endDate, t -> Constants.Functions.DATE_PARSE_FUNCTION.apply(t.trim()+"235959"));
    }


    public static String formatPercent(Double d) {
        return new DecimalFormat("0.##%").format(d);
    }



    /**
     * 格式化金额
     * @param fen
     * @return
     */
    public static String formatMoney(int fen) {
        return new DecimalFormat("0.00").format(fen / 100.0);
    }

    /**
     * 格式化金额加单位
     * @param fen
     * @return
     */
    public static String formatMoneyWithYuan(int fen) {
        return new DecimalFormat("0.00").format(fen / 100.0) + "元";
    }

    /**
     * 格式化金额加单位
     * @param fen
     * @return
     */
    public static String formatMoneyWithYuan(long fen) {
        return new DecimalFormat("0.00").format(fen / 100.0) + "元";
    }

    /**
     * 格式化金额
     * @param fen
     * @return
     */
    public static String formatMoney(Long fen, boolean isSetFlag) {
        return isSetFlag && Objects.nonNull(fen) ? new DecimalFormat("0.00").format(fen / 100.0) : null;
    }

    /**
     * 格式化金额
     * @param yuan
     * @return
     */
    public static String formatMoney(double yuan) {
        return new DecimalFormat("0.00").format(yuan);
    }

    /**
     * 格式化金额
     * @param yuan
     * @return
     */
    public static String formatMoney(Double yuan) {
        return new DecimalFormat("0.00").format(Optional.ofNullable(yuan).orElse(0D));
    }

    public static double divideHundred(Integer value){
        if (value == null){
            return 0D;
        }
        return BigDecimal.valueOf(value).divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP).doubleValue();
    }

    public static double divideHundred(String value){
        return valueOf(value).divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP).doubleValue();
    }

    public static String formatDecimal(String value) {
        try {
            return new DecimalFormat("0.###").format(Double.parseDouble(value));
        }catch (Exception e){
            return Strings.EMPTY;
        }

    }





    /**
     * 类型转换(防止NPE),如果接收对象为基本类型,请指定基本类型的默认值
     * @param src
     * @param mapFunction
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F,T> T nonNullConvert(F src, Function<F,T> mapFunction) {
        return nonNullConvert(src, mapFunction,null);
    }


    public static <T> T nonNullConvert(String src, Function<String,T> mapFunction) {
        return nonNullConvert(src, mapFunction,null);
    }


    /**
     * 外部门店编码到门店ID的转换
     * @param outPoiGetter
     * @param poiSetter
     * @param poiQueryFunction
     * @param notMatcherResult
     * @param <T>
     * @return
     */
    public static <T> T poiConvert(Supplier<String> outPoiGetter, Consumer<String> poiSetter
            , Function<String,Long> poiQueryFunction
            , Supplier<T> notMatcherResult) {

        if (StringUtils.isEmpty(outPoiGetter.get())) {
            return null;
        }

        Long poiId = poiQueryFunction.apply(outPoiGetter.get());
        if (poiId != null) {
            poiSetter.accept(String.valueOf(poiId));
            return null;
        } else {
            return notMatcherResult.get();
        }
    }


    /**
     *
     * @param value
     * @param predicate
     * @param consumer
     * @param <T>
     */
    public static <T> void predicateSet(T value, Predicate<T> predicate, Consumer<T> consumer) {
        if (predicate != null && predicate.test(value)) {
            consumer.accept(value);
        }
    }






    /**
     * List类型转换
     * @param srcList
     * @param mapFunction
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F,T> List<T> convertList(List<F> srcList, Function<F,T> mapFunction) {
        return Optional.ofNullable(srcList).map(List::stream).orElse(Stream.empty())
                .filter(Objects::nonNull).map(mapFunction).collect(Collectors.toList());
    }

    /**
     * List类型转换
     * @param srcList
     * @param mapFunction
     * @param <T> target
     * @return
     */
    public static <F,T> List<T> convertList(List<F> srcList, Function<F,T> mapFunction, Predicate<F> predicate) {
        return Optional.ofNullable(srcList).map(List::stream).orElse(Stream.empty())
                .filter(Objects::nonNull).filter(predicate).map(mapFunction).collect(Collectors.toList());
    }

    /**
     * List类型转换
     *
     * @param srcList
     * @param mapFunction
     * @param <S> source
     * @param <T> target
     * @return
     */
    public static <S, T, K> Map<K, T> listStreamMapToMap(List<S> srcList, Function<S, K> keyFunction, Function<S, T> mapFunction) {
        return Optional.ofNullable(srcList).map(List::stream).orElse(Stream.empty())
                .filter(Objects::nonNull).collect(Collectors.toMap(keyFunction, mapFunction, (k1, k2) -> k2));
    }

    /**
     * List类型转换
     *
     * @param srcList
     * @param mapFunction
     * @param <S> source
     * @param <T> target
     * @return
     */
    public static <S, T> List<T> listStreamMap(List<S> srcList, Function<S, T> mapFunction) {
        return Optional.ofNullable(srcList).map(List::stream).orElse(Stream.empty())
                .filter(Objects::nonNull).map(mapFunction).collect(Collectors.toList());
    }


    /**
     * 获取list中某个对象的某个元素
     * @param srcList
     * @param index
     * @param func
     * @param defaultValue
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F,T> T getSpecificItemOfList(List<F> srcList, int index, Function<F,T> func, T defaultValue) {
        if (CollectionUtils.isNotEmpty(srcList) && srcList.get(index) != null) {
            return func.apply(srcList.get(index));
        }
        return defaultValue;
    }
    /**
     * 获取list中的某个元素
     * @param srcList
     * @param index
     * @param defaultValue
     * @param <F>
     * @return
     */
    public static<F> F getSpecificItemOfList (List<F> srcList, int index, F defaultValue) {
        if (CollectionUtils.isNotEmpty(srcList) && srcList.get(index) != null) {
            return srcList.get(index);
        }
        return defaultValue;
    }



    public static String convert(int i) {
        return i > 0 ? String.valueOf(i): null;
    }


    public static String convert(double d) {
        return d > 0.0D ? String.valueOf(d): null;
    }

    public static String convert(long l) {
        return l > 0L ? String.valueOf(l): null;
    }

    public static String convertNullString(String value, String defaultValue) {
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        return value;
    }

    public static<F> List<F> convertNullList(List<F> list) {
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 格式化百分比
     * @param ratio
     * @return
     */
    public static String formatRatio(double ratio) {
        return new DecimalFormat("0.00").format(ratio);
    }

    public static <F, T> Set<T> convertList2Set(List<F> srcList, Function<F, T> mapFunction) {
        return Optional.ofNullable(srcList).map(List::stream).orElse(Stream.empty())
                .filter(Objects::nonNull).map(mapFunction).collect(Collectors.toSet());
    }

    /**
     * 格式化状态
     * @param status
     * @return
     */
    public static List<Integer> convertStatus2StatusList(Integer status) {


        if (status == null || status == 0 )
            return null;
        ArrayList<Integer> statusList = new ArrayList<>();
        statusList.add(status);
        return statusList;
    }


    public static List<Integer> convertTaskType2TaskTypeList(Integer taskType){
        ArrayList<Integer> statusList = new ArrayList<>();
        if (taskType == null || taskType == 0)
        {
            statusList.add(TaskType.RANDOM_STOCK_TAKE.getVal());
            statusList.add(TaskType.PLAN_STOCK_TAKE.getVal());
            statusList.add(TaskType.ABNORMAL_STOCK_TAKE.getVal());
        }
        else
            statusList.add(taskType);
        return statusList;
    }

    public static String convertListToString(List<String> stringList){
        if (CollectionUtils.isEmpty(stringList)){
            return Strings.EMPTY;
        }
        StringBuilder sb = new StringBuilder();
        stringList.forEach(s -> sb.append(s).append(","));
        sb.deleteCharAt(sb.length() - 1);

        return sb.toString();
    }
    
    public static BigDecimal valueOf(String value) {
        try {
            return StringUtils.isNotBlank(value) ? new BigDecimal(value) : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("BigDecimal valueOf error", e);
        }
        return BigDecimal.ZERO;
    }

    public static BigDecimal valueOf(Double value) {
        try {
            if (Objects.isNull(value)) {
                return BigDecimal.ZERO;
            }
            return BigDecimal.valueOf(value);
        } catch (Exception e) {
            log.error("BigDecimal valueOf error", e);
        }
        return BigDecimal.ZERO;
    }

}