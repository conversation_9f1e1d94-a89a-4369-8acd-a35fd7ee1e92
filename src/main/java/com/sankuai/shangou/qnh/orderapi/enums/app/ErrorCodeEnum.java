package com.sankuai.shangou.qnh.orderapi.enums.app;

import lombok.Getter;

/**
 * @Title: ErrorCodeEnum
 * @Description:
 * <AUTHOR>
 * @Date 2020/3/31 15:33
 */
public enum ErrorCodeEnum {
    SUCCESS(0, "成功"),

    QUOTE_REVIEWING(1001, "报价审核中，审核通过后请及时上架"),
    QUOTE_FAILED(1003, "报价失败，请手动提交报价"),

    ORDER_CANT_BE_CONTACT(1001002, "已超过联系期限，如有需要请联系总部"),
    ORDER_CANT_BE_CONTACT_24_HOUR(1001002, "订单完成24小时后虚拟号已失效，无法电话联系顾客"),
    ORDER_CANT_BE_CONTACT_OVER_HOUR(1001002, "订单完成后虚拟号已失效，无法电话联系顾客"),
    QUERY_ORDER_VIRTUAL_PHONE_FAIL(1001003, "获取隐私号失败，请稍后再试"),
    QUERY_MT_FAMOUS_TAVERN_GATHER_AFTER_SALES_PHONE_RESULT(1001004, "渠道规则限制，美团名酒馆平台配送订单无法直接联系顾客，可先联系名酒馆店员，联系方式"),
    QUERY_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND(1001005, "无法获取到名酒馆联系方式，可先到商家端后台处理。"),
    /**
     * 美团名酒馆主动发起退款提示
     */
    REFUND_CHECK_MT_FAMOUS_TAVERN_PROMPT(1001006, "名酒馆订单需联系名酒馆店员发起售后，联系方式"),
    /**
     * 美团名酒馆pc/web-当未能查到集合店电话时
     */
    WEB_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND(1001007, "名酒馆订单需联系名酒馆店员处理，无法获取到名酒馆联系方式，可先到商家端后台处理。"),
    /**
     * 美团名酒馆售后审核提示
     */
    REFUND_AUDIT_MT_FAMOUS_TAVERN_PROMPT(1001008, "名酒馆订单需联系名酒馆店员处理，联系方式"),
    /**
     * 美团名酒馆app/pda-当未能查到集合店电话时
     */
    APP_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND(1001009, "无法获取到名酒馆联系方式，可先到商家端后台处理。"),


    SPU_PUSH_CHANNEL_FAILED(11004, "商品推送渠道失败"),
    VIRTUAL_PHONE_AUTH_INFO_NOT_AUTHORIZED(20000107, "实名信息未认证"),
    ;
    @Getter
    private final int code;

    @Getter
    private final String message;

    ErrorCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
