package com.sankuai.shangou.qnh.orderapi.utils.pc;

import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;


/***
 * author : <EMAIL> 
 * data : 2019/1/3 
 * time : 上午11:20
 **/
@Slf4j
public class OfflineOrderConstantsHelper {


    public static  boolean isOnSale(Integer offlineOrderStatus, Integer distributeStatus){
        if(offlineOrderStatus==null || distributeStatus==null){
            return true;
        }
        return offlineOrderStatus <= OfflineOrderStatus.FULFILLMENT && distributeStatus < DistributeStatusEnum.RIDER_TAKE_GOODS.getValue();
    }


    public static interface OfflineOrderStatus {
        int SUBMIT = OrderStatusEnum.SUBMIT.getValue();
        int MERCHANT_CONFIRM = OrderStatusEnum.MERCHANT_CONFIRMED.getValue();
        int FULFILLMENT = OrderStatusEnum.PICKING.getValue(); //履约中
        int COMPLETED = OrderStatusEnum.COMPLETED.getValue(); //已完成
        int CLOSED = OrderStatusEnum.CLOSED.getValue(); //已关闭
        int REFUND_APPLIED = OrderStatusEnum.REFUND_APPLIED.getValue();//已发起退款,正在处理中
        int CANCELED = OrderStatusEnum.CANCELED.getValue();//订单已取消
        int LOCKED = OrderStatusEnum.LOCKED.getValue();//锁定中
        int APPEAL_LOCKED = OrderStatusEnum.APPEAL_APPLIED.getValue(); //申述锁定中
    }



}
