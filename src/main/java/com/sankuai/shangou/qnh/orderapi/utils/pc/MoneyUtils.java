package com.sankuai.shangou.qnh.orderapi.utils.pc;

import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Objects;

/**
 * <AUTHOR>
 * @createTime 2019/11/29
 * @description
 */
public class MoneyUtils {

    private static final BigDecimal HUNDRED = new BigDecimal("100");

    /**
     * 分转换成元，四舍五入保留两位小数
     * @param cent
     */
    public static String centToYuanByHalfUp(Integer cent) {
        if (cent == null) {
            return "";
        }
        return BigDecimal.valueOf(cent).divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP).toString();
    }


    /**
     * 分转换成元，只保留整数
     * @param cent
     */
    public static String centToYuanByHalfUp(Long cent) {
        if (cent == null) {
            return "";
        }
        return BigDecimal.valueOf(cent).divide(BigDecimal.valueOf(100),0, RoundingMode.HALF_UP).toString();
    }

    public static long yuanToCent(double yuan) {
        return BigDecimal.valueOf(yuan).multiply(BigDecimal.valueOf(100)).intValue();
    }

    /**
     * 元转分 int范围
     * @param yuan
     * @return
     */
    public static Integer yuanToCentInt(Integer yuan) {
        if (Objects.isNull(yuan)) {
            return null;
        }

        return BigDecimal.valueOf(yuan).multiply(BigDecimal.valueOf(100)).intValue();
    }

    /**
     * 分转元 int范围
     * @param cent
     * @return
     */
    public static Integer centToYuanInt(Integer cent) {
        if (Objects.isNull(cent)) {
            return null;
        }

        return BigDecimal.valueOf(cent).divide(BigDecimal.valueOf(100),2, RoundingMode.CEILING).intValue();
    }


    public static String centToYuan(Integer cent) {
        if (cent == null) {
            return null;
        }
        return BigDecimal.valueOf(cent).divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP).toString();
    }

    /**
     * 分转换成元,保留两位小数
     * @param cent
     */
    public static Double centToYuan(Long cent) {
        if (cent == null) {
            return 0D;
        }
        return BigDecimal.valueOf(cent).divide(BigDecimal.valueOf(100),2, RoundingMode.CEILING).doubleValue();
    }

    /**
     * 分转换成元,保留两位小数
     * @param cent
     */
    public static String centToYuanStr(Long cent) {
        if (cent == null) {
            return "";
        }
        return BigDecimal.valueOf(cent).divide(BigDecimal.valueOf(100),2, RoundingMode.CEILING).toString();
    }

    /**
     * 厘转换成元,保留三位小数
     * @param li
     */
    public static String liToYuanStr(Long li) {
        BigDecimal decimal = new BigDecimal(Long.toString(li));
        BigDecimal divide = decimal.divide(new BigDecimal("1000"), 16, RoundingMode.HALF_UP);
        DecimalFormat decimalFormat = new DecimalFormat();
        decimalFormat.setGroupingUsed(false);
        // 默认最多保留3位小数
        decimalFormat.setMaximumFractionDigits(3);
        return decimalFormat.format(divide);
    }

    public static Double centToYuanByDown(Long cent){
        return BigDecimal.valueOf(cent).divide(BigDecimal.valueOf(100),2, RoundingMode.DOWN).doubleValue();
    }

    public static BigDecimal centToYuanDecimalByDown(Long cent){
        return BigDecimal.valueOf(cent).divide(BigDecimal.valueOf(100),2, RoundingMode.DOWN);
    }

    public static String convertFenToYuanWithFormat(Long fen) {
        if (fen == null) {
            return "";
        }
        return new DecimalFormat("0.00").format(BigDecimal.valueOf(fen).divide(HUNDRED));
    }

    public static Double yuanStrToYuanDouble(String yuan) {
        if(StringUtils.isBlank(yuan)){
            return null;
        }
        BigDecimal bigDecimalValue = new BigDecimal(yuan);
        BigDecimal scaledValue = bigDecimalValue.setScale(2, RoundingMode.HALF_UP);
        return scaledValue.doubleValue();
    }
}
