package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.MetricHelper;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.dto.response.model.PartRefundGoodsModel;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderKey;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderDetailReq;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsOrderDetailResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.AfterSaleRecordDetailVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.AfterSaleRecordVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderBaseVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderDetailVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.ProductInfoVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.PromotionInfoVo;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.DeliveryInfoDTO;
import com.meituan.shangou.saas.order.management.client.enums.ChannelType;
import com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchService;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.common.model.OrderItemExchangeModel;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.ExtDataKeyConstants;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.sankuai.meituan.reco.pickselect.consts.OpenOpType;
import com.sankuai.meituan.reco.pickselect.thrift.OpenPickThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTraceDetail;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTraceQueryRequest;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTraceResponse;
import com.sankuai.meituan.reco.pickselect.thrift.TaskDetail;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderDetailResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AfterSaleRecordDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AfterSaleRecordVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderStatusLogVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ProductVOForRefund;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PromotionVO;
import com.sankuai.shangou.qnh.orderapi.enums.pc.OrderCouldOperateItemEnum;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.remote.OrderBizRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.common.ExchangeItemUtil;
import com.sankuai.shangou.qnh.orderapi.utils.CombinationProductUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.OrderUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderMngService {
    private static final Integer SUCCESS = 0;
    private static final Integer ERROR = 1;
    private static final String SYMBOL_COMMA = ",";

    @Autowired
    private OcmsOrderSearchService ocmsOrderSearchService;
    @Autowired
    private OpenPickThriftService.Iface openPickThriftService;
    @Autowired
    private OrderBizRemoteService orderBizRemoteService;

    @MethodLog(logRequest = true, logResponse = true)
    public OrderDetailResponse getOrderDetail(OcmsOrderDetailReq req) {
        log.info("调用channelOrderTenantThriftService.orderDetail request:{}", req);
        try {
            OrderDetailResponse orderDetailResponse = new OrderDetailResponse();
            orderDetailResponse.setCode(SUCCESS);
            OcmsOrderDetailResponse response = ocmsOrderSearchService.orderDetail(req);
            if (response == null || response.getOrder() == null) {
                log.info("查询订单订单详情为空, request:{}", response);
                MetricHelper.build().name("orderDetailIsNull").count();
                fillCodeAndMsg(orderDetailResponse, response);
                return orderDetailResponse;
            }
            log.info("调用channelOrderTenantThriftService.orderDetail response:{}", response);
            Integer code = Optional.of(response).map(OcmsOrderDetailResponse::getResponseStatus).orElse(ERROR);
            if (SUCCESS.equals(code)) {
                Map<OCMSOrderKey, List<Integer>> keyListMap = orderBizRemoteService.queryOrderOperateItemsFromOrderBiz(req.getTenantId(),
                        ImmutableList.of(OCMSOrderKey.builder().channelType(req.getChannelId()).channelOrderId(req.getChannelOrderId()).build()),
                        ImmutableList.of(OrderCouldOperateItemEnum.ACCEPT_ORDER.getValue(),
                                OrderCouldOperateItemEnum.COMPLETE_PICK.getValue(),
                                OrderCouldOperateItemEnum.PRINT_RECEIPT.getValue(),
                                OrderCouldOperateItemEnum.FULL_ORDER_REFUND.getValue(),
                                OrderCouldOperateItemEnum.PART_ORDER_REFUND.getValue()));
                orderDetailResponse.setOrderDetail(buildOrderDetailVO(response.getOrder(), findOperateItemList(req.getChannelId(),
                        req.getChannelOrderId(), keyListMap)));
                return orderDetailResponse;
            }
            fillCodeAndMsg(orderDetailResponse, response);
            return orderDetailResponse;
        } catch (Exception e) {
            log.info("调用channelOrderTenantThriftService.orderDetail exception:", e);
            throw new CommonRuntimeException(e);
        }
    }

    private OrderDetailVO buildOrderDetailVO(OrderDetailVo orderDetail, List<Integer> operateItemList) {
        if (Objects.isNull(orderDetail) || Objects.isNull(orderDetail.getOrderBaseDto())) {
            return null;
        }
        OrderBaseVo orderBaseDto = orderDetail.getOrderBaseDto();
        OrderDetailVO orderDetailVO = new OrderDetailVO();

        orderDetailVO.setChannelOrderId(orderBaseDto.getChannelOrderId());
        orderDetailVO.setStoreId(orderBaseDto.getShopId());
        orderDetailVO.setStoreName(orderBaseDto.getChannelName());
        orderDetailVO.setChannelId(orderBaseDto.getChannelId());
        orderDetailVO.setChannelName(orderBaseDto.getChannelName());
        orderDetailVO.setItemCount(orderBaseDto.getItemCount());
        orderDetailVO.setReceiverName(orderBaseDto.getReceiverName());
        orderDetailVO.setReceiverPhone(orderBaseDto.getReceiverPhone());
        orderDetailVO.setReceiveAddress(orderBaseDto.getReceiveAddress());
        orderDetailVO.setOriginalAmt(orderBaseDto.getOriginalAmt());
        orderDetailVO.setActualPayAmt(orderBaseDto.getActualPayAmt());
        orderDetailVO.setProductTotalPayAmount(calculateProductTotalPayAmount(orderDetail.getProductInfoList()));
        orderDetailVO.setBizReceiveAmt(orderBaseDto.getBizReceiveAmt());
        orderDetailVO.setFreight(orderBaseDto.getFreight());
        orderDetailVO.setPackageAmt(orderBaseDto.getPackageAmt());
        orderDetailVO.setPlatformFee(orderBaseDto.getPlatformFee());
        orderDetailVO.setIsNeedInvoice(Optional.ofNullable(orderBaseDto.getIsNeedInvoice()).orElse(Boolean.FALSE) ? 1 : 0);
        orderDetailVO.setInvoiceTitle(orderBaseDto.getInvoiceTitle());
        orderDetailVO.setTaxNo(orderBaseDto.getTaxNo());
        orderDetailVO.setDeliveryMethod(orderBaseDto.getDeliveryMethod());
        orderDetailVO.setDeliveryMethodName(orderBaseDto.getDeliveryMethodName());
        orderDetailVO.setDeliveryUserName(orderBaseDto.getDeliveryUserName());
        orderDetailVO.setDeliveryUserPhone(orderBaseDto.getDeliveryUserPhone());
        orderDetailVO.setIsDeliveryOvertime(orderBaseDto.getIsDeliveryOvertime());
        orderDetailVO.setDeliveryExceptionDescription(orderBaseDto.getDeliveryExceptionDescription());
        orderDetailVO.setDistributeType(orderBaseDto.getDistributeType());
        orderDetailVO.setDistributeTypeName(orderBaseDto.getDeliveryMethodName());
        orderDetailVO.setPayMethod(orderBaseDto.getPayMethod());
        orderDetailVO.setPayMethodDesc(orderBaseDto.getPayMethodDesc());
        orderDetailVO.setChannelOrderStatus(orderBaseDto.getChannelOrderStatus());
        orderDetailVO.setChannelOrderStatusDesc(orderBaseDto.getChannelOrderStatusDesc());
        orderDetailVO.setAggregationOrderStatus(orderBaseDto.getAggregationOrderStatus());
        orderDetailVO.setCreateTime(orderBaseDto.getCreateTime());
        orderDetailVO.setLastAfterSaleApplyRefundTagId(orderBaseDto.getLastAfterSaleApplyRefundTagId());
        orderDetailVO.setLastAfterSaleApplyReason(orderBaseDto.getLastAfterSaleApplyReason());
        orderDetailVO.setLastAfterSaleApplyStatus(orderBaseDto.getLastAfterSaleApplyStatus());
        orderDetailVO.setLastAfterSaleApplyRejectReason(orderBaseDto.getLastAfterSaleApplyRejectReason());
        orderDetailVO.setRefundableOrderAmount(orderBaseDto.getRefundableOrderAmount());
        orderDetailVO.setEstimatedSendArriveTimeStart(orderBaseDto.getEstimatedSendArriveTimeStart());
        orderDetailVO.setEstimatedSendArriveTimeEnd(orderBaseDto.getEstimatedSendArriveTimeEnd());
        orderDetailVO.setUpdateTime(orderBaseDto.getUpdateTime());
        orderDetailVO.setDistributeStatus(orderBaseDto.getDistributeStatus());
        DistributeStatusEnum status = DistributeStatusEnum.enumOf(orderBaseDto.getDistributeStatus());
        orderDetailVO.setDistributeStatusName(status != null ? status.getDesc() : "");
        if (Objects.equals(DynamicChannelType.TAO_XIAN_DA.getChannelId(), orderBaseDto.getChannelId())) {
            orderDetailVO.setPickupStatus(Optional.ofNullable(orderDetail.getDeliveryInfoDTO()).orElse(new DeliveryInfoDTO()).getDeliveryStatus());
        } else {
            orderDetailVO.setPickupStatus(orderBaseDto.getDeliveryStatus());
        }
        orderDetailVO.setChannelExtraOrderId(orderBaseDto.getChannelExtraOrderId());
        orderDetailVO.setComments(orderBaseDto.getComments());
        orderDetailVO.setSerialNo(orderBaseDto.getOrderSerialNumber());
        orderDetailVO.setDeliveryOrderType(orderBaseDto.getDeliveryOrderType());
        orderDetailVO.setDeliveryOrderTypeName(orderBaseDto.getDeliveryOrderTypeName());
        orderDetailVO.setCouldOperateItemList(operateItemList);
        orderDetailVO.setProductList(buildProductVOList(orderDetail.getProductInfoList(), orderBaseDto.getChannelId()));
        orderDetailVO.setPromotionList(buildPromotionVOList(orderDetail.getPromotionInfoList()));
        orderDetailVO.setOrderStatuslogList(buildOrderStatusLogs(orderDetail));
        orderDetailVO.setAfterSaleRecordList(buildAfterSaleRecordVOList(orderDetail.getAfterSaleRecords()));
        // 设置支付时间
        orderDetailVO.setPayTime(orderBaseDto.getPayTime() != null && orderBaseDto.getPayTime() > 0 ? orderBaseDto.getPayTime() : orderBaseDto.getCreateTime());
        orderDetailVO.setOrderSource(orderBaseDto.getOrderSource());
        orderDetailVO.setFuseOrderStatus(orderBaseDto.getFuseOrderStatus());
        orderDetailVO.setIsMtFamousTavern(orderBaseDto.getIsMtFamousTavern());
        orderDetailVO.setIsFacaiWine(orderBaseDto.getIsFacaiWine());
        return orderDetailVO;
    }

    private List<AfterSaleRecordVO> buildAfterSaleRecordVOList(List<AfterSaleRecordVo> afterSaleRecords) {
        if (CollectionUtils.isEmpty(afterSaleRecords)) {
            return Lists.newArrayList();
        }
        List<AfterSaleRecordVO> afterSaleRecordVOList = Lists.newArrayList();
        for (AfterSaleRecordVo afterSaleRecord : afterSaleRecords) {
            AfterSaleRecordVO afterSaleRecordVO = new AfterSaleRecordVO();
            afterSaleRecordVO.setStoreId(afterSaleRecord.getShopId());
            afterSaleRecordVO.setTenantId(afterSaleRecord.getTenantId());
            afterSaleRecordVO.setChannelId(afterSaleRecord.getChannelId());
            afterSaleRecordVO.setServiceId(afterSaleRecord.getServiceId());
            afterSaleRecordVO.setIsAudit(afterSaleRecord.getIsAudit());
            afterSaleRecordVO.setStatus(afterSaleRecord.getStatus());
            afterSaleRecordVO.setApplyReason(afterSaleRecord.getApplyReason());
            afterSaleRecordVO.setAfsPattern(afterSaleRecord.getAfsPattern());
            afterSaleRecordVO.setRefundAmt(afterSaleRecord.getRefundAmt());
            afterSaleRecordVO.setCreateTime(afterSaleRecord.getCreateTime());
            afterSaleRecordVO.setUpdateTime(afterSaleRecord.getUpdateTime());
            afterSaleRecordVO.setAfsApplyType(afterSaleRecord.getAfsApplyType());
            afterSaleRecordVO.setWhoApplyType(afterSaleRecord.getWhoApplyType());
            afterSaleRecordVO.setAfterSaleRecordDetailList(buildAfterSaleRecordDetailVOList(afterSaleRecord.getAfterSaleRecordDetails()));
            afterSaleRecordVOList.add(afterSaleRecordVO);
        }
        return afterSaleRecordVOList;
    }

    private List<AfterSaleRecordDetailVO> buildAfterSaleRecordDetailVOList(List<AfterSaleRecordDetailVo> afterSaleRecordDetailList) {
        List<AfterSaleRecordDetailVO> afterSaleRecordDetailVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(afterSaleRecordDetailList)) {
            afterSaleRecordDetailList.stream().forEach(detail -> {
                        AfterSaleRecordDetailVO afterSaleRecordDetailVO = new AfterSaleRecordDetailVO();
                        afterSaleRecordDetailVO.setChannelId(detail.getChannelId());
                        afterSaleRecordDetailVO.setServiceId(detail.getServiceId());
                        afterSaleRecordDetailVO.setSkuId(detail.getSkuId());
                        afterSaleRecordDetailVO.setUpcCode(detail.getUpcCode());
                        afterSaleRecordDetailVO.setSkuName(detail.getSkuName());
                        afterSaleRecordDetailVO.setSpecification(detail.getSpecification());
                        afterSaleRecordDetailVO.setSellUnit(detail.getSellUnit());
                        afterSaleRecordDetailVO.setUnitPrice(detail.getUnitPrice());
                        afterSaleRecordDetailVO.setCount(detail.getCount());
                        afterSaleRecordDetailVO.setRefundAmt(detail.getRefundAmount());
                        afterSaleRecordDetailVOList.add(afterSaleRecordDetailVO);
                    }
            );
        }
        return afterSaleRecordDetailVOList;
    }

    public List<OrderStatusLogVO> buildOrderStatusLogs(OrderDetailVo orderDetail) {
        List<OrderStatusLogVO> orderStatusLists = Lists.newArrayList();
        // 订单状态流
        if (CollectionUtils.isNotEmpty(orderDetail.getOrderOpLogList())) {
            orderDetail.getOrderOpLogList().stream().forEach(e -> {
                if (StringUtils.isNotBlank(e.getOptContent())) {
                    OrderStatusLogVO logViewVo = new OrderStatusLogVO();
                    StringBuilder desc = new StringBuilder();
                    desc.append(StringUtils.trimToEmpty(e.getOperator()) + e.getOptContent());
                    if (StringUtils.isNotBlank(e.getOptDesc())) {
                        desc.append("[").append(e.getOptDesc()).append("]");
                    }
                    logViewVo.setStatusDesc(desc.toString());
                    logViewVo.setTime(e.getOptTime());
                    orderStatusLists.add(logViewVo);
                }
            });
        }

        // 配送状态流
        if (CollectionUtils.isNotEmpty(orderDetail.getDeliveryStateLogList())) {
            orderDetail.getDeliveryStateLogList().stream().forEach(e -> {
                OrderStatusLogVO logViewVo = new OrderStatusLogVO();
                logViewVo.setStatusDesc(e.getStateDesc());
                logViewVo.setTime(e.getUpdateTime());
                orderStatusLists.add(logViewVo);
            });
        }

        // 拣货状态流
        try {
            String channelOrderID = orderDetail.getOrderBaseDto().getChannelOrderId();
            OpenTraceResponse openTraceResponse = openPickThriftService.queryOrderTrace(
                    new OpenTraceQueryRequest()
                            .setSource(convertBizType(orderDetail.getOrderBaseDto().getChannelId()))
                            .setUnifyOrderId(channelOrderID)
            );
            if (openTraceResponse.getDetail() != null) {
                OpenTraceDetail openTraceDetail = openTraceResponse.getDetail();
                if (openTraceDetail.getPickFinishTime() > 0) {
                    OrderStatusLogVO logViewVo = new OrderStatusLogVO();
                    logViewVo.setStatusDesc("出餐完成");
                    logViewVo.setTime(TimeUnit.SECONDS.toMillis(openTraceDetail.getPickFinishTime()));
                    orderStatusLists.add(logViewVo);
                }
                if (CollectionUtils.isNotEmpty(openTraceDetail.getTaskDetails())) {
                    // 任务领取
                    List<TaskDetail> receiveTask = openTraceDetail.getTaskDetails().stream().filter(e -> OpenOpType.receiveEvent == e.getOpType())
                            .sorted((o1, o2) -> o1.getOpTime() - o2.getOpTime() > 0 ? -1 : 1).collect(Collectors.toList());

                    String name = Joiner.on(",").join(receiveTask.stream().map(e -> e.getOpName()).distinct().collect(Collectors.toList()));
                    receiveTask.stream().findFirst()
                            .ifPresent(e -> {
                                OrderStatusLogVO logViewVo = new OrderStatusLogVO();
                                logViewVo.setStatusDesc(name + "领取任务");
                                logViewVo.setTime(TimeUnit.SECONDS.toMillis(e.getOpTime()));
                                orderStatusLists.add(logViewVo);
                            });
                    //拣货
                    List<TaskDetail> pickTask = openTraceDetail.getTaskDetails().stream().filter(e -> OpenOpType.pickEvent == e.getOpType())
                            .sorted((o1, o2) -> o1.getOpTime() - o2.getOpTime() > 0 ? -1 : 1).collect(Collectors.toList());

                    String pickNames = Joiner.on(",").join(pickTask.stream().map(e -> e.getOpName()).distinct().collect(Collectors.toList()));
                    pickTask.stream().findFirst()
                            .ifPresent(e -> {
                                OrderStatusLogVO logViewVo = new OrderStatusLogVO();
                                logViewVo.setStatusDesc(pickNames + "拣货完成");
                                logViewVo.setTime(TimeUnit.SECONDS.toMillis(e.getOpTime()));
                                orderStatusLists.add(logViewVo);
                            });
                    // 合流
                    List<TaskDetail> mergeTask = openTraceDetail.getTaskDetails().stream().filter(e -> OpenOpType.mergeEvent == e.getOpType())
                            .sorted((o1, o2) -> o1.getOpTime() - o2.getOpTime() > 0 ? -1 : 1).collect(Collectors.toList());

                    String mergeNames = Joiner.on(",").join(mergeTask.stream().map(e -> e.getOpName()).distinct().collect(Collectors.toList()));
                    mergeTask.stream().findFirst()
                            .ifPresent(e -> {
                                OrderStatusLogVO logViewVo = new OrderStatusLogVO();
                                logViewVo.setStatusDesc(mergeNames + "合流完成");
                                logViewVo.setTime(TimeUnit.SECONDS.toMillis(e.getOpTime()));
                                orderStatusLists.add(logViewVo);
                            });
                }

            }
        }
        catch (Exception e) {
            log.error("获取拣货状态流失败,将忽略拣货状态。order:{}", orderDetail.getOrderBaseDto().getChannelOrderId(), e);
        }
        // 排序
        Collections.sort(orderStatusLists, Comparator.comparing(OrderStatusLogVO::getTime).reversed());
        return orderStatusLists;
    }

    public static Integer convertBizType(Integer channelId) {
        return ChannelOrderConvertUtils.convertBizType(channelId);
    }

    private int calculateProductTotalPayAmount(List<ProductInfoVo> productInfoList) {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return 0;
        }
        return productInfoList.stream().mapToInt(ProductInfoVo::getTotalCurrentPrice).sum();
    }

    private List<ProductVOForRefund> buildProductVOList(List<ProductInfoVo> productInfoList, Integer channelId) {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return Lists.newArrayList();
        }

        List<ProductVOForRefund> productVOList = Lists.newArrayList();
        for (ProductInfoVo productInfo : productInfoList) {
            ProductVOForRefund productVO = new ProductVOForRefund();
            productVO.setSkuId(productInfo.getSkuId());
            productVO.setCustomerSkuId(productInfo.getCustomerSkuId());
            productVO.setCustomerSpuId(productInfo.getCustomSpu());
            productVO.setUpcCode(productInfo.getUpcCode());
            productVO.setSkuName(productInfo.getSkuName());
            productVO.setOriginalTotalPrice(productInfo.getTotalOriginAmount());
            productVO.setTotalPayAmount(productInfo.getTotalCurrentPrice());
            productVO.setTotalDiscountAmount(productInfo.getTotalDiscountAmount());
            productVO.setPicUrl(processPicUrl(productInfo.getPicUrl()));
            productVO.setSpecification(productInfo.getSpecification());
            productVO.setSellUnit(productInfo.getSellUnit());
            productVO.setUnitPrice(productInfo.getUnitPrice());
            productVO.setCount(productInfo.getCount());
            productVO.setIsRefund(Optional.ofNullable(productInfo.getIsRefund()).orElse(Boolean.FALSE) ? 1 : 0);
            productVO.setRefundCount(productInfo.getRefundCount());
            productVO.setBoothName(productInfo.getBoothName());
            productVO.setOfflinePrice(productInfo.getOfflinePrice() == null || productInfo.getOfflinePrice() < 0 ? null :
                    productInfo.getOfflinePrice());
            productVO.setStallSettleAmt(productInfo.getStallSettleAmt());
            productVO.setCustomerSkuId(productInfo.getCustomerSkuId());
            productVO.setSpu(productInfo.getSpu());
            productVO.setCanRefundCount(productInfo.getCanRefundCount());
            if(Objects.equals(channelId, ChannelType.JD2HOME.getValue())){
                productVO.setCanRefundCount(productInfo.getCount() - Optional.ofNullable(productInfo.getRefundCount()).orElse(0));
            }
            productVO.setRefundPrice(productVO.getCount() == 0 ? "0" : String.valueOf(productVO.getTotalPayAmount() / productVO.getCount()));
            //处理CustomerSkuId无值时 extCustomSkuId（目前只有京东门店自创商品时会出现该情形）
            dealExtCustomSkuId(productVO, productInfo);
            // 组合商品
            productVO.setSubProduct(CombinationProductUtil.buildSubProduct(productInfo.getCombinationChildProductVoList(), productInfo.getCount()));
            productVO.setOrderItemId(productInfo.getOrderItemId());
            productVO.setOutItemId(processExtDataOutItemId(productInfo.getExtData()));

            // extData确认是否为赠品，设置赠品参数 0-平台赠品，1-自定义赠品
            productVO.setGiftType(OrderUtils.getExtDataAsInt("giftType", productInfo.getExtData()));
            //识别是否是换货商品
            productVO.setIsExchangeProduct(isExchangeProduct(productInfo.getExtData()));
            productVO.setExchangeProductVoList(ExchangeItemUtil.getExchangeProductList(productInfo, productInfoList));
            OrderItemExchangeModel exchangeModel = OrderItemExchangeModel.build(productInfo.getExtData());
            productVO.setExchangeFromCount(exchangeModel.getExchangeSourceOrderItemCnt());
            productVO.setExchangeToCount(exchangeModel.getExchangeOrderItemToCnt());
            productVOList.add(productVO);
        }
        return productVOList;
    }

    private Boolean isExchangeProduct(String extData) {
        try {
            if (StringUtils.isBlank(extData)) {
                return false;
            }
            JSONObject jsonObject = JSON.parseObject(extData);
            Long outItemId = jsonObject.getLong(ExtDataKeyConstants.ExchangeEk.EXE_CHANGE_ORDER_ITEM_ID_FROM_KEY);
            if (outItemId != null) {
                return true;
            }
        } catch (Exception e) {
            log.info("isExchangeProduct error e= ",e);
        }
        return false;
    }

    private Long processExtDataOutItemId(String extData) {
        try {
            if (StringUtils.isBlank(extData)) {
                return 0L;
            }
            JSONObject jsonObject = JSON.parseObject(extData);
            Long outItemId = jsonObject.getLong("outItemId");
            if (outItemId != null) {
                return outItemId;
            }
        } catch (Exception e) {
            log.error("proccessExtDataOutItem error",e);
        }
        return 0L;
    }

    private void dealExtCustomSkuId(ProductVOForRefund productVO, ProductInfoVo productInfo) {
        try {
            if(StringUtils.isNotBlank(productInfo.getCustomerSkuId()) || StringUtils.isBlank(productInfo.getExtData())){
                return;
            }
            JSONObject extData = JSON.parseObject(productInfo.getExtData());
            if(Objects.isNull(extData) || StringUtils.isBlank(extData.getString("extCustomSkuId"))){
                return;
            }
            productVO.setExtCustomSkuId(extData.getString("extCustomSkuId"));
        }catch (Exception e){
            log.info("OrderMngFacade.dealExtCustomerSkuId error :",e);
        }
    }

    private PartRefundGoodsModel getPartRefundGoodsModel(ProductInfoVo productInfo, Map<String, PartRefundGoodsModel> partMap) {

        PartRefundGoodsModel partRefundGoodsModel = partMap.get(String.valueOf(productInfo.getCustomerSkuId()));

        if (partRefundGoodsModel == null) {
            partRefundGoodsModel = Optional.ofNullable(partMap.get(String.valueOf(productInfo.getCustomerSkuId()))).orElse(new PartRefundGoodsModel());
        }

        return partRefundGoodsModel;
    }

    private String processPicUrl(String picUrl) {
        if (Strings.isEmpty(picUrl)) {
            return picUrl;
        }

        if (picUrl.contains(SYMBOL_COMMA)) {
            String[] picUrls = picUrl.split(SYMBOL_COMMA);
            if (picUrls.length > 0) {
                return picUrls[0];
            }
        }

        return picUrl;
    }

    private List<PromotionVO> buildPromotionVOList(List<PromotionInfoVo> promotionInfoList) {
        if (CollectionUtils.isEmpty(promotionInfoList)) {
            return Lists.newArrayList();
        }

        List<PromotionVO> promotionVOList = Lists.newArrayList();
        for (PromotionInfoVo promotionInfo : promotionInfoList) {
            PromotionVO promotionVO = new PromotionVO();
            promotionVO.setDescription(promotionInfo.getDescription());
            promotionVO.setDiscount(promotionInfo.getDiscount());
            promotionVO.setPlatformBearFee(promotionInfo.getPlatformBearFee());
            promotionVO.setBizBearFee(promotionInfo.getBizBearFee());
            promotionVO.setAgentBearFee(promotionInfo.getAgentBearFee());
            promotionVO.setLogisticsBearCharge(promotionInfo.getLogisticsBearCharge());
            promotionVOList.add(promotionVO);
        }
        return promotionVOList;
    }


    private void fillCodeAndMsg(OrderDetailResponse orderDetailResponse, OcmsOrderDetailResponse response) {
        orderDetailResponse.setCode(Optional.ofNullable(response).map(OcmsOrderDetailResponse::getResponseStatus).orElse(201));
        orderDetailResponse.setMsg(Optional.ofNullable(response).map(OcmsOrderDetailResponse::getMsg).orElse("订单不存在"));
    }

    private List<Integer> findOperateItemList(Integer channelId, String chanelOrderId, Map<OCMSOrderKey, List<Integer>> ocmsOrderKeyListMap) {
        if (MapUtils.isEmpty(ocmsOrderKeyListMap)) {
            return Collections.emptyList();
        }
        return ocmsOrderKeyListMap.entrySet().stream()
                .filter(entry -> entry.getKey().getChannelOrderId().equals(chanelOrderId)
                        && Objects.equals(channelId, entry.getKey().getChannelType()))
                .map(Map.Entry::getValue).findAny().orElse(Collections.emptyList());
    }

}