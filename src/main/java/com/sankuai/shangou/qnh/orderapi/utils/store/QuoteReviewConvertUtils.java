package com.sankuai.shangou.qnh.orderapi.utils.store;

import com.sankuai.shangou.qnh.orderapi.constant.store.PriceTrendConstants;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QueryQuoteRecordResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QueryQuoteReviewResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QuerySkuInfoResponse;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.*;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.*;
import com.sankuai.shangou.qnh.orderapi.enums.store.QuoteTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.quote.*;
import com.sankuai.meituan.shangou.empower.ocms.client.price.enums.quote.*;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.quote.BatchQuoteReviewRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.quote.QueryQuoteRecordsRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.quote.QueryQuoteReviewsRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.quote.QuoteCommitRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.quote.QueryQuoteRecordsResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.quote.QueryQuoteReviewsResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StoreSkuBaseDetailDTO;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.QueryChannelSkuInfoByKeyWordsCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.ExtendSkuParts;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.SkuInfoPageResult;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class QuoteReviewConvertUtils {

    /** 标识所有的审核状态**/
    private static final int ALL_STATUS_FLAG = 0;
    private static final int REVIEWING_STATUS_FLAG = 1;
    /** 标识所有的已审核状态**/
    private static final int REVIEWED_STATUS_FLAG = 2;

    private static final String START_TIME_TAIL = " 00:00:00";
    private static final String END_TIME_TAIL = " 23:59:59";

    /** 审核状态转换关系 **/
    private static Map<Integer, List<ReviewStatusEnum>> reviewMap = new HashMap<Integer, List<ReviewStatusEnum>>() {
        {
            put(ALL_STATUS_FLAG, Arrays.asList(ReviewStatusEnum.TO_REVIEW, ReviewStatusEnum.PASSED,
                    ReviewStatusEnum.INVALID, ReviewStatusEnum.REJECTED));
            put(REVIEWED_STATUS_FLAG, Arrays.asList(ReviewStatusEnum.PASSED, ReviewStatusEnum.INVALID, ReviewStatusEnum.REJECTED));
            put(ReviewStatusEnum.TO_REVIEW.getValue(), Arrays.asList(ReviewStatusEnum.TO_REVIEW));
            put(ReviewStatusEnum.PASSED.getValue(), Arrays.asList(ReviewStatusEnum.PASSED));
            put(ReviewStatusEnum.INVALID.getValue(), Arrays.asList(ReviewStatusEnum.INVALID));
            put(ReviewStatusEnum.REJECTED.getValue(), Arrays.asList(ReviewStatusEnum.REJECTED));
        }
    };

    public static QueryQuoteRecordResponse convertResponse(QueryQuoteRecordsResponse response, Map<Long, String> accountNameMap,
                                                           List<StoreSkuBaseDetailDTO> skuBaseDetailDTOS) {
        QueryQuoteRecordResponse recordResponse = new QueryQuoteRecordResponse();
        recordResponse.setHasMore(response.getPageInfo().getTotalPage() > response.getPageInfo().getPage());
        recordResponse.setTotalCount(response.getPageInfo().getTotalSize().intValue());
        recordResponse.setQueryQuoteRecords(convertQuoteRecordVOS(response.getQuoteReviewList(), accountNameMap, skuBaseDetailDTOS));
        return recordResponse;
    }

    private static List<QueryQuoteRecordVO> convertQuoteRecordVOS(List<QuoteRecordDTO> recordDTOS, Map<Long, String> accountNameMap,
                                                                  List<StoreSkuBaseDetailDTO> skuBaseDetailDTOS) {
        if (CollectionUtils.isEmpty(recordDTOS)) {
            return null;
        }

        Map<String, Integer> skuWeightTypeMap = skuBaseDetailDTOS.stream().
                collect(Collectors.toMap(StoreSkuBaseDetailDTO::getSkuId, StoreSkuBaseDetailDTO::getWeightType));

        List<QueryQuoteRecordVO> queryQuoteRecordVOS = new ArrayList<>();
        Map<Integer, Map<Integer, String>> rejectMap = MccDynamicConfigUtil.queryRejectReasonTypeMap();
        for (QuoteRecordDTO appDTO : recordDTOS) {
            QueryQuoteRecordVO recordVO = convertQuoteRecordVO(appDTO, accountNameMap,
                    rejectMap.get(QuoteTypeEnum.OFF_LINE_PRICE_QUOTE.getCode()));
            recordVO.setWeightType(skuWeightTypeMap.get(appDTO.getSkuId()));

            queryQuoteRecordVOS.add(recordVO);
        }

        return queryQuoteRecordVOS;
    }

    private static QueryQuoteRecordVO convertQuoteRecordVO(QuoteRecordDTO dto, Map<Long, String> accountNameMap,
                                                           Map<Integer, String> rejectMap) {
        QueryQuoteRecordVO recordVO = new QueryQuoteRecordVO();
        recordVO.setQuoteId(dto.getQuoteRecordId());
        recordVO.setSkuId(dto.getSkuId());
        recordVO.setSkuName(dto.getSkuName());
        recordVO.setPicUrl(dto.getPicUrl());
        recordVO.setSkuSpec(dto.getSkuSpec());
        recordVO.setWeight(dto.getWeight());
        recordVO.setQuotePrice(smaller100Times(dto.getQuotePrice()));
        recordVO.setQuotePricePer500g(smaller100Times(dto.getQuotePriceOf500g()));
        recordVO.setQuoteTime(TimeConvertUtils.convertDisplayTime(dto.getQuoteTime()));
        recordVO.setReviewStatus(dto.getReviewStatus().getValue());
        recordVO.setReviewerId(dto.getReviewerId());
        recordVO.setReviewTime(TimeConvertUtils.convertReviewTime(dto.getReviewTime()));
        recordVO.setReviewDescription(genReviewDescription(dto.getRejectReason(), dto.getRejectReasonType(), rejectMap));

        String name = accountNameMap.containsKey(dto.getReviewerId()) ? accountNameMap.get(dto.getReviewerId()) :
                dto.getReviewerName();
        recordVO.setReviewerName(name);

        return recordVO;
    }

    public static String smaller100Times(Long priceValue) {
        if (Objects.isNull(priceValue)) {
            return "";
        }
        BigDecimal value = BigDecimal.valueOf(priceValue).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN);
        return value.toString();
    }

    public static QueryQuoteRecordsRequest convertQuoteRecordListQueryRequest(QueryQuoteRecordRequest request, User user,
                                                                              QueryQuoteBoothInfoVO infoVO) {
        QueryQuoteRecordsRequest queryRequest = new QueryQuoteRecordsRequest();
        queryRequest.setTenantId(user.getTenantId());
        queryRequest.setSkuIds(request.getSkuIds());
        queryRequest.setStoreIds(infoVO.getPoiIds());
        queryRequest.setQuoteStartTime(TimeUtils.convertStr2TimeStamp(request.getQuoteStartTime() + START_TIME_TAIL));
        queryRequest.setQuoteEndTime(TimeUtils.convertStr2TimeStamp(request.getQuoteEndTime() + END_TIME_TAIL));
        if (CollectionUtils.isNotEmpty(request.getReviewStatusList())) {
            queryRequest.setReviewStatusList(request.getReviewStatusList().stream().map(ReviewStatusEnum::convertEnum)
                    .collect(Collectors.toList()));
        }


        queryRequest.setPageNum(request.getPageNum());
        queryRequest.setPageSize(request.getPageSize());

        if (CollectionUtils.isNotEmpty(request.getReviewStatusList()) && request.getReviewStatusList().size() == 1
                && request.getReviewStatusList().get(0).intValue() == ReviewStatusEnum.TO_REVIEW.getValue()) {
            queryRequest.setAsceSeq(true);
        } else {
            queryRequest.setAsceSeq(false);
        }

        // 若是非摊位账号，则查询店铺；否则查询账号
        if (user.getAccountType() != AccountTypeEnum.BOOTH.getValue()) {
            queryRequest.setStoreIds(infoVO.getPoiIds());
        } else {
            queryRequest.setBoothId(infoVO.getBoothId());
        }

        queryRequest.setQueryModeType(request.getQueryModeType().intValue() == ALL_STATUS_FLAG ? QueryQuoteModeEnum.ALL : QueryQuoteModeEnum.IS_LATEST);

        return queryRequest;
    }

    public static QueryQuoteRecordsRequest convertQuoteRecordListQueryRequest(QueryQuoteRecordDefulatRequest request,
                                                                              User user, QueryQuoteBoothInfoVO infoVO) {
        QueryQuoteRecordsRequest queryRequest = new QueryQuoteRecordsRequest();
        queryRequest.setTenantId(user.getTenantId());
        queryRequest.setReviewStatusList(reviewMap.get(request.getReviewStatus()));
        queryRequest.setStoreIds(infoVO.getPoiIds());
        queryRequest.setSortByReviewTime(request.getReviewStatus().intValue() == ReviewStatusEnum.REJECTED.getValue());
        if (BooleanUtils.isTrue(queryRequest.getSortByReviewTime())) {
            queryRequest.setAsceSeq(false);
        } else {
            queryRequest.setAsceSeq(request.getReviewStatus().intValue() == ReviewStatusEnum.TO_REVIEW.getValue());
        }
        queryRequest.setPageNum(request.getPageNum() == null ? 0 : request.getPageNum());
        queryRequest.setPageSize(request.getPageSize() == null ? 10 : request.getPageSize());
        queryRequest.setQueryModeType( ALL_STATUS_FLAG == request.getReviewStatus().intValue() ?
                QueryQuoteModeEnum.ALL : QueryQuoteModeEnum.IS_LATEST);

        // 若是非摊位账号，则查询店铺；否则查询账号
        if (user.getAccountType() != AccountTypeEnum.BOOTH.getValue()) {
            queryRequest.setStoreIds(infoVO.getPoiIds());
        } else {
            queryRequest.setBoothId(infoVO.getBoothId());
        }

        return queryRequest;
    }

    public static QueryQuoteReviewsRequest convertQuoteReviewListQueryRequest(QueryQuoteReivewDefulatRequest request, User user) {
        QueryQuoteReviewsRequest queryRequest = new QueryQuoteReviewsRequest();
        queryRequest.setTenantId(user.getTenantId());
        queryRequest.setReviewStatus(reviewMap.get(request.getReviewStatus()));
        queryRequest.setSortByReviewTime(request.getReviewStatus().intValue() == REVIEWED_STATUS_FLAG);
        if (BooleanUtils.isTrue(queryRequest.getSortByReviewTime())) {
            queryRequest.setAsceSeq(false);
        } else {
            queryRequest.setAsceSeq(request.getReviewStatus().intValue() == ReviewStatusEnum.TO_REVIEW.getValue());
        }
        queryRequest.setPageNum(request.getPageNum());
        queryRequest.setPageSize(request.getPageSize());
        queryRequest.setQueryQuoteMode(REVIEWING_STATUS_FLAG == request.getReviewStatus().intValue()
                ? QueryQuoteModeEnum.IS_LATEST : QueryQuoteModeEnum.ALL);

        return queryRequest;
    }

    public static QueryQuoteReviewResponse convertResponse(QueryQuoteReviewsResponse response, boolean hasReviewPermission,
                                                           Map<Long, String> empNameMap, Map<String, Boolean> priceTrendPermissionMap) {
        QueryQuoteReviewResponse reviewResponse = new QueryQuoteReviewResponse();
        reviewResponse.setHasMore(response.getPageInfo().getTotalPage() > response.getPageInfo().getPage());
        reviewResponse.setTotalCount(response.getPageInfo().getTotalSize().intValue());
        reviewResponse.setQuoteReviews(convertQuoteReviewVOS(QuoteTypeEnum.OFF_LINE_PRICE_QUOTE, response.getQuoteReviewList(), hasReviewPermission,
                empNameMap, priceTrendPermissionMap));

        return reviewResponse;
    }


    private static List<QueryQuoteReviewVO> convertQuoteReviewVOS(QuoteTypeEnum quoteTypeEnum, List<QuoteReviewDTO> reviewForAppDTOS, boolean hasReviewPermission,
                                                                  Map<Long, String> empNameMap, Map<String, Boolean> priceTrendPermissionMap) {
        if (CollectionUtils.isEmpty(reviewForAppDTOS)) {
            return null;
        }

        List<QueryQuoteReviewVO> queryQuoteReviewVOS = new ArrayList<>();
        Map<Integer, Map<Integer, String>> rejectMap = MccDynamicConfigUtil.queryRejectReasonTypeMap();
        for (QuoteReviewDTO dto : reviewForAppDTOS) {
            queryQuoteReviewVOS.add(convertQuoteReviewVO(dto, hasReviewPermission, empNameMap, priceTrendPermissionMap, rejectMap.get(quoteTypeEnum.getCode())));
        }

        return queryQuoteReviewVOS;
    }

    private static QueryQuoteReviewVO convertQuoteReviewVO(QuoteReviewDTO dto, boolean hasReviewPermission,
                                                           Map<Long, String> empNameMap, Map<String, Boolean> priceTrendPermissionMap,
                                                           Map<Integer, String> rejectMap) {
        QueryQuoteReviewVO reviewVO = new QueryQuoteReviewVO();
        reviewVO.setQuoteRecordId(dto.getQuoteRecordId());
        reviewVO.setPicUrl(dto.getImages());
        reviewVO.setStoreName(dto.getStoreName());
        reviewVO.setSkuCode(dto.getSkuId());
        reviewVO.setSkuName(dto.getSkuName());
        reviewVO.setSpec(dto.getSkuSpec());
        reviewVO.setWeight(dto.getWeight());
        reviewVO.setQuotePrice(smaller100Times(dto.getQuotePrice()));
        reviewVO.setQuotePricePer500g(smaller100Times(dto.getQuotePriceOf500g()));
        reviewVO.setReviewStatus(dto.getQuoteStatus().getValue());
        reviewVO.setCheckFlag(hasReviewPermission);
        reviewVO.setQuoteTime(TimeConvertUtils.convertDisplayTime(dto.getQuoteTime()));
        reviewVO.setQuoterName(empNameMap.get(dto.getQuoterId()));
        reviewVO.setChannelRetailPriceInfos(genChannelRetailPriceInfo(dto));

        // 线下价
        if (Objects.nonNull(dto.getCurrentPriceInfo())) {
            reviewVO.setCurrentPricePer500g(smaller100Times(dto.getCurrentPriceInfo().getPrice()));
            reviewVO.setCurrentPricePer500gRatio(dto.getCurrentPriceInfo().getModifyRatio());
            reviewVO.setCurrentPricePer500gRatioFlag(dto.getCurrentPriceInfo().getChainResultFlag().getValue());
        }

        // 审核信息填充
        ReviewAbstractDTO reviewInfo = dto.getReviewAbstractInfo();
        if (Objects.nonNull(reviewInfo)) {
            String reviewerName = empNameMap.containsKey(reviewInfo.getReviewerId())
                    ? empNameMap.get(reviewInfo.getReviewerId()) : reviewInfo.getReviewerName();
            reviewVO.setReviewerName(reviewerName);
            reviewVO.setReviewTime(TimeConvertUtils.convertDisplayTime(reviewInfo.getReviewTime()));
            reviewVO.setReviewDescription(genReviewDescription(reviewInfo.getRejectReason(),
                    reviewInfo.getRejectReasonType(), rejectMap));
        }

        // 填充市调价
        fillPriceTrendInfo(dto, priceTrendPermissionMap, reviewVO);

        return reviewVO;
    }

    private static String genReviewDescription(String rejectReason, Integer rejectType, Map<Integer, String> rejectMap) {
        StringBuilder sb = new StringBuilder();
        String reasonTypeDesc = rejectMap.get(rejectType);

        if (StringUtils.isNotBlank(reasonTypeDesc)) {
            sb.append(reasonTypeDesc);
        }

        if (StringUtils.isNotBlank(reasonTypeDesc) && StringUtils.isNotBlank(rejectReason)) {
            sb.append("（");
        }

        if (StringUtils.isNotBlank(rejectReason)) {
            sb.append(rejectReason);
        }

        if (StringUtils.isNotBlank(reasonTypeDesc) && StringUtils.isNotBlank(rejectReason)) {
            sb.append("）");
        }

        return sb.toString();
    }

    private static void fillPriceTrendInfo(QuoteReviewDTO dto, Map<String, Boolean> permissionMap, QueryQuoteReviewVO vo) {
        if (Objects.isNull(dto.getPriceTrend())) {
            return;
        }

        QuotePriceTrendVO priceTrendInfoVO = new QuotePriceTrendVO();
        priceTrendInfoVO.setDateList(dto.getPriceTrend().getDateList());

        // 线下价价格趋势，默认权限
        if (CollectionUtils.isNotEmpty(dto.getPriceTrend().getStorePriceList())) {
            priceTrendInfoVO.setStorePriceList(convert(dto.getPriceTrend().getStorePriceList()));
            vo.setPriceTrendInfo(priceTrendInfoVO);
        }

        // 市调价
        if (permissionMap.get(PriceTrendConstants.MR_PRICE_TREND_PERMISSION_CODE) &&
                CollectionUtils.isNotEmpty(dto.getPriceTrend().getMrPriceList())) {
            vo.setAvgReferencePricePer500g(smaller100Times(dto.getMrPriceInfo().getPrice()));
            vo.setAvgReferencePricePer500gRatio(dto.getMrPriceInfo().getModifyRatio());
            vo.setAvgReferencePricePer500gRatioFlag(dto.getMrPriceInfo().getChainResultFlag().getValue());
            priceTrendInfoVO.setMrPriceList(convert(dto.getPriceTrend().getMrPriceList()));
            vo.setPriceTrendInfo(priceTrendInfoVO);
        }

        // 基础价
        if (permissionMap.get(PriceTrendConstants.CITY_BASE_PRICE_TREND_PERMISSION_CODE) &&
                CollectionUtils.isNotEmpty(dto.getPriceTrend().getCityBasePriceList())) {
            vo.setBasePricePer500g(smaller100Times(dto.getBasePriceInfo().getPrice()));
            vo.setBasePricePer500gRatio(dto.getBasePriceInfo().getModifyRatio());
            vo.setBasePricePer500gRatioFlag(dto.getBasePriceInfo().getChainResultFlag().getValue());
            priceTrendInfoVO.setCityBasePriceList(convert(dto.getPriceTrend().getCityBasePriceList()));
            vo.setPriceTrendInfo(priceTrendInfoVO);
        }
    }

    private static List<QuotePriceOfDateVO> convert(List<PriceOfDateRecordDTO> recordDTOS) {
        if (CollectionUtils.isEmpty(recordDTOS)) {
            return null;
        }

        return recordDTOS.stream().map(dto -> {
            QuotePriceOfDateVO priceOfDateVO = new QuotePriceOfDateVO();
            priceOfDateVO.setDate(dto.getDate());
            priceOfDateVO.setValue(smaller100Times(dto.getPrice()));
            return priceOfDateVO;
        }).collect(Collectors.toList());
    }

    private static List<ChannelRetailPriceInfoVO> genChannelRetailPriceInfo(QuoteReviewDTO dto) {
        if (CollectionUtils.isEmpty(dto.getQuoteChannelPriceInfos())) {
            return Collections.emptyList();
        }

        dto.getQuoteChannelPriceInfos().sort(new Comparator<QuoteChannelPriceDTO>() {
            @Override
            public int compare(QuoteChannelPriceDTO o1, QuoteChannelPriceDTO o2) {
                return o1.getChannelId().compareTo(o2.getChannelId());
            }
        });

        List<ChannelRetailPriceInfoVO> priceInfoVOS = dto.getQuoteChannelPriceInfos().stream().map(channelPriceInfo -> {
            ChannelRetailPriceInfoVO priceInfoVO = new ChannelRetailPriceInfoVO();
            priceInfoVO.setChannelId(channelPriceInfo.getChannelId());
            priceInfoVO.setStrategyType(channelPriceInfo.getSyncStrategyType().getValue());
            priceInfoVO.setRaisePrice(smaller100Times(channelPriceInfo.getRaisePrice()));
            priceInfoVO.setRaisePercent(smaller100Times(channelPriceInfo.getRaisePercent()));
            priceInfoVO.setRetailPrice(smaller100Times(channelPriceInfo.getOnlinePrice()));
            priceInfoVO.setRetailPricePer500g(smaller100Times(channelPriceInfo.getOnlinePriceOf500g()));
            priceInfoVO.setDescription(channelPriceInfo.getComment());

            return priceInfoVO;
        }).collect(Collectors.toList());

        return priceInfoVOS;
    }

    public static QueryQuoteReviewsRequest convertQuoteReviewListQueryRequest(QueryQuoteReviewRequest request, User user) {
        QueryQuoteReviewsRequest queryRequest = new QueryQuoteReviewsRequest();
        queryRequest.setTenantId(user.getTenantId());
        queryRequest.setStoreIds(request.getStoreIds());
        if (CollectionUtils.isNotEmpty(request.getSkuStoreInfos()) && CollectionUtils.isNotEmpty(request.getStoreIds())) {
            queryRequest.setSkuIds(request.getSkuStoreInfos().stream()
                    .filter(skuStoreInfoVO -> request.getStoreIds().contains(skuStoreInfoVO.getStoreId()))
                    .map(vo -> vo.getSkuId())
                    .collect(Collectors.toList()));
        }
        queryRequest.setQuoteStartTime(TimeUtils.convertStr2TimeStamp(request.getQuoteStartTime() + START_TIME_TAIL));
        queryRequest.setQuoteEndTime(TimeUtils.convertStr2TimeStamp(request.getQuoteEndTime() + END_TIME_TAIL));
        if (CollectionUtils.isNotEmpty(request.getReviewStatusList())) {
            queryRequest.setReviewStatus(request.getReviewStatusList().stream().map(ReviewStatusEnum::convertEnum).collect(Collectors.toList()));
        }

        queryRequest.setPageNum(request.getPageNum());
        queryRequest.setPageSize(request.getPageSize());

        if (CollectionUtils.isNotEmpty(request.getReviewStatusList()) && request.getReviewStatusList().size() == 1
                && request.getReviewStatusList().get(0).intValue() == ReviewStatusEnum.TO_REVIEW.getValue()) {
            queryRequest.setAsceSeq(true);
        } else {
            queryRequest.setAsceSeq(false);
        }

        queryRequest.setQueryQuoteMode(ALL_STATUS_FLAG == request.getQueryModeType().intValue() ? QueryQuoteModeEnum.ALL : QueryQuoteModeEnum.IS_LATEST);
        return queryRequest;
    }

    public static BatchQuoteReviewRequest convertBatchReviewRequest(QuoteReviewedRequest reviewedRequest, User use) {
        BatchQuoteReviewRequest batchReviewRequest = new BatchQuoteReviewRequest();
        batchReviewRequest.setTenantId(use.getTenantId());
        batchReviewRequest.setQuoteIds(reviewedRequest.getQuoteRecordIds());
        batchReviewRequest.setReviewOperate(ReviewOperateEnum.convertEnum(reviewedRequest.getOperateType()));
        batchReviewRequest.setReviewerId(use.getAccountId());
        batchReviewRequest.setReviewerName(use.getUsername());
        batchReviewRequest.setRejectReason(StringUtils.isNotBlank(reviewedRequest.getRejectReason()) ? reviewedRequest.getRejectReason().trim() : null);
        batchReviewRequest.setRejectReasonType(reviewedRequest.getRejectReasonNo());
        batchReviewRequest.setReviewClientType(ReviewClientEnum.APP);

        return batchReviewRequest;
    }

    public static QuoteCommitRequest convertQuoteCommitRequest(QuoteSkuPriceRequest request) {
        QuoteCommitRequest commitRequest = new QuoteCommitRequest();
        commitRequest.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        commitRequest.setStoreId(request.getStoreId());
        commitRequest.setSpuId(request.getSkuId());
        commitRequest.setSkuId(request.getSkuId());
        commitRequest.setQuotePrice(yuan2fen(request.getPrice()));
        commitRequest.setQuoterId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
        commitRequest.setQuoterName(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getUsername());

        if (DeviceTypeEnum.ANDROID.name().equalsIgnoreCase(ApiMethodParamThreadLocal.getIdentityInfo().getOs())) {
            commitRequest.setSource(DeviceTypeEnum.ANDROID);
        } else if (DeviceTypeEnum.IOS.name().equalsIgnoreCase(ApiMethodParamThreadLocal.getIdentityInfo().getOs())) {
            commitRequest.setSource(DeviceTypeEnum.IOS);
        }

        return commitRequest;
    }

    public static QuoteCommitRequest convertBatchQuotePriceRequest(BatchQuotePriceRequest request) {
        QuoteCommitRequest commitRequest = new QuoteCommitRequest();
        commitRequest.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        commitRequest.setQuoterId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
        commitRequest.setQuoterName(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getUsername());
        commitRequest.setStoreId(request.getStoreId());

        if (DeviceTypeEnum.ANDROID.name().equalsIgnoreCase(ApiMethodParamThreadLocal.getIdentityInfo().getOs())) {
            commitRequest.setSource(DeviceTypeEnum.ANDROID);
        } else if (DeviceTypeEnum.IOS.name().equalsIgnoreCase(ApiMethodParamThreadLocal.getIdentityInfo().getOs())) {
            commitRequest.setSource(DeviceTypeEnum.IOS);
        }

        commitRequest.setSpuId(request.getSpuId());
        if (StringUtils.isBlank(commitRequest.getSpuId())) {
            commitRequest.setSpuId(request.getQuotes().get(0).getSkuId());
        }

        commitRequest.setQuotePriceList(Lists.newArrayList());
        request.getQuotes().forEach(quote -> {
            QuoteSkuPriceDTO quoteSkuPriceDTO = new QuoteSkuPriceDTO();
            quoteSkuPriceDTO.setSkuId(quote.getSkuId());
            quoteSkuPriceDTO.setQuotePrice(yuan2fen(quote.getOfflinePrice()));

            commitRequest.getQuotePriceList().add(quoteSkuPriceDTO);
        });

        return commitRequest;
    }

    private static Long yuan2fen(String yuan) {
        return BigDecimal.valueOf(Double.valueOf(yuan)).multiply(BigDecimal.valueOf(100)).longValue();
    }

    public static QueryChannelSkuInfoByKeyWordsCommand convertQueryChannelSkuInfoByKeyWordsCommand(Long tenantId, QueryQuoteSkuInfoRequest request, List<Long> storeIds) {
        QueryChannelSkuInfoByKeyWordsCommand keyWordsCommand = new QueryChannelSkuInfoByKeyWordsCommand();
        keyWordsCommand.setKeyWords(request.getSkuName());
        keyWordsCommand.setStoreIdSet(new HashSet<>(storeIds));
        keyWordsCommand.setTenantId(tenantId);
        keyWordsCommand.setPageNo(request.getPageNum());
        keyWordsCommand.setPageSize(request.getPageSize());
        ExtendSkuParts extendSkuParts = new ExtendSkuParts();
        extendSkuParts.setStoreSku(true);
        extendSkuParts.setChannelSku(false);
        keyWordsCommand.setExtendParts(extendSkuParts);

        return keyWordsCommand;
    }

    public static QuerySkuInfoResponse convertQuerySkuInfoResponse(SkuInfoPageResult pageResult) {
        QuerySkuInfoResponse skuInfoResponse = new QuerySkuInfoResponse();
        skuInfoResponse.setHasMore(false);

        if (pageResult.getStatus().getCode() != 0 || pageResult.getSkuPageInfo() == null
                || CollectionUtils.isEmpty(pageResult.getSkuPageInfo().getSkuInfoList())) {
            skuInfoResponse.setTotalCount(0);
            return skuInfoResponse;
        }

        List<QuoteSkuInfoQueryVO> skuInfoQueryVOS = pageResult.getSkuPageInfo().getSkuInfoList().stream().map(skuInfo -> {
            QuoteSkuInfoQueryVO quoteSkuInfoQueryVO = new QuoteSkuInfoQueryVO();
            quoteSkuInfoQueryVO.setSkuId(skuInfo.getSkuId());
            quoteSkuInfoQueryVO.setSkuName(skuInfo.getName());
            quoteSkuInfoQueryVO.setStoreId(skuInfo.getStoreSkuInfo() != null ? skuInfo.getStoreSkuInfo().getStoreId() : 0);
            return quoteSkuInfoQueryVO;
        }).collect(Collectors.toList());

        skuInfoResponse.setTotalCount(pageResult.getSkuPageInfo().getTotalCount());
        skuInfoResponse.setSkuInfoList(skuInfoQueryVOS);

        return skuInfoResponse;
    }
}
