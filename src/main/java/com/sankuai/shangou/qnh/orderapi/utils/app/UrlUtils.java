package com.sankuai.shangou.qnh.orderapi.utils.app;


import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class UrlUtils {


    public static UrlInfo buildUrlInfo4Url(String url) {
        if (StringUtils.isEmpty(url)) {
            return new UrlInfo().setSearchParams(Maps.newHashMap());
        }
        String[] urlAndSearchParams = url.split("[?]", 2);
        UrlInfo urlInfo = new UrlInfo().setBaseUrl(urlAndSearchParams[0]);
        Map<String, String> parameters = Maps.newHashMap();
        if (urlAndSearchParams.length == 2) {
            List<String> searchParameters = Splitter.on("&").omitEmptyStrings().trimResults()
                    .splitToList(urlAndSearchParams[1]);
            searchParameters.forEach(v -> {
                String[] kv = v.split("=", 2);
                if (ArrayUtils.isNotEmpty(kv) && kv.length > 1) {
                    parameters.put(kv[0], kv[1]);
                }
            });
        }
        urlInfo.setSearchParams(parameters);
        return urlInfo;
    }

    public static String buildUrl4UrlInfo(UrlInfo urlInfo) {
        if (Objects.isNull(urlInfo) || StringUtils.isEmpty(urlInfo.getBaseUrl())) {
            return "";
        }
        if (MapUtils.isEmpty(urlInfo.getSearchParams())) {
            return urlInfo.getBaseUrl();
        }
        return Joiner.on("?").join(urlInfo.getBaseUrl(),
                Joiner.on("&").withKeyValueSeparator("=").join(urlInfo.getSearchParams()));
    }

    public static String insertPrams2Url(String url, Map<String, String> params) {
        if (MapUtils.isEmpty(params)) {
            return url;
        }
        UrlInfo urlInfo = buildUrlInfo4Url(url);
        if (Objects.nonNull(urlInfo)) {
            urlInfo.getSearchParams().putAll(params);
        }
        return buildUrl4UrlInfo(urlInfo);
    }

    public static class UrlInfo {

        /**
         * 去除参数后的url对象
         */
        private String baseUrl;
        /**
         * url参数对象
         */
        private Map<String, String> searchParams;

        public String getBaseUrl() {
            return baseUrl;
        }

        public UrlInfo setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
            return this;
        }

        public Map<String, String> getSearchParams() {
            if(Objects.isNull(searchParams)){
                this.searchParams = Maps.newHashMap();
            }
            return searchParams;
        }

        public UrlInfo setSearchParams(Map<String, String> searchParams) {
            this.searchParams = searchParams;
            return this;
        }
    }

}