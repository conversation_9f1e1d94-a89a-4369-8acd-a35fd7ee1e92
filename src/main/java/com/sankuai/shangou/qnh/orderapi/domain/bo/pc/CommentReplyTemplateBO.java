package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentReplyTemplateDTO;import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 评价回复模板BO
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CommentReplyTemplateBO {
    /**
     * 模板id
     */
    private Long templateId;
    /**
     * 模板内容
     */
    private String templateContent;

    public static CommentReplyTemplateBO build(ChannelCommentReplyTemplateDTO commentReplyTemplateDTO) {
        if (commentReplyTemplateDTO == null) {
            return null;
        }

        CommentReplyTemplateBO commentReplyTemplateBO = new CommentReplyTemplateBO();
        commentReplyTemplateBO.setTemplateId(commentReplyTemplateDTO.getTemplateId());
        commentReplyTemplateBO.setTemplateContent(commentReplyTemplateDTO.getTemplateContent());
        return commentReplyTemplateBO;
    }
}
