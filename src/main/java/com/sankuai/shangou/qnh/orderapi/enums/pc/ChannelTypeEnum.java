package com.sankuai.shangou.qnh.orderapi.enums.pc;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 渠道枚举类
 */
@AllArgsConstructor
@Getter
@Deprecated // 使用DynamicChannelType替代
public enum ChannelTypeEnum {
    MEITUAN(100, "美团外卖", "美团外卖"),
    ELEM(200, "饿了么", "饿了么"),
    JD2HOME(300, "京东到家", "京东到家"),
    MT_MEDICINE(400, "美团医药", "美团医药"),
    YOU_ZAN(500, "有赞", "有赞"),
    MT_DRUNK_HORSE(700, "微商城", "微商城"),
    QUAN_QIU_WA(800, "全球蛙", "全球蛙"),
    SELF_CHANNEL(900, "自有渠道", "自有渠道");
    /**
     * 渠道编码
     */
    private int channelId;
    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道简称
     */
    private String channelAbbrName;

    @Deprecated
    public static ChannelTypeEnum findByChannelId(int channelId) {
        for (ChannelTypeEnum channelTypeEnum : ChannelTypeEnum.values()) {
            if (channelTypeEnum.getChannelId() == channelId) {
                return channelTypeEnum;
            }
        }
        return null;
    }
}

