package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发配送规则 VO.
 *
 * <AUTHOR>
 * @since 2021/3/8 19:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LaunchRuleVO {
    @FieldDoc(
            description = "发配送规则的编码"
    )
    @ApiModelProperty(value = "发配送规则的编码")
    private Integer code;

    @FieldDoc(
            description = "发配送规则的名称"
    )
    @ApiModelProperty(value = "发配送规则的名称")
    private String name;
}
