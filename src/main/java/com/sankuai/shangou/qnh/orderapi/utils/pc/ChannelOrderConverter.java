package com.sankuai.shangou.qnh.orderapi.utils.pc;

import com.dianping.lion.client.Lion;
import com.meituan.shangou.saas.common.enums.ChannelTypeEnum;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OperatorTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.RefundTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelOrderStatus;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderRefundTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/01/02
 * @desc
 */
@Slf4j
public class ChannelOrderConverter {

    // private static final Map<Integer, Integer> CHANNEL_TYPE_BIZ_ORDER_TYPE_MAP = new HashMap<>();

    private static final Map<Integer, Integer> CHANNEL_REFUND_TAG_BIZ_REFUND_TYPE = new HashMap<>();

    private static final Map<Integer, Integer> CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS = new HashMap<>();


    static {

//        CHANNEL_TYPE_BIZ_ORDER_TYPE_MAP.put(ChannelType.ELEM.getValue(), OrderBizTypeEnum.ELE_ME.getValue());
//        CHANNEL_TYPE_BIZ_ORDER_TYPE_MAP.put(ChannelType.JD2HOME.getValue(), OrderBizTypeEnum.JING_DONG.getValue());
//        CHANNEL_TYPE_BIZ_ORDER_TYPE_MAP.put(ChannelType.MEITUAN.getValue(), OrderBizTypeEnum.MEITUAN_WAIMAI.getValue());
//        CHANNEL_TYPE_BIZ_ORDER_TYPE_MAP.put(ChannelType.MT_MEDICINE.getValue(), OrderBizTypeEnum.MEITUAN_MEDICINE.getValue());
//        CHANNEL_TYPE_BIZ_ORDER_TYPE_MAP.put(ChannelType.YOU_ZAN.getValue(), OrderBizTypeEnum.YOU_ZAN_MIDDLE.getValue());
//        CHANNEL_TYPE_BIZ_ORDER_TYPE_MAP.put(ChannelTypeEnum.MT_DRUNK_HORSE.getValue(), OrderBizTypeEnum.MEITUAN_DRUNK_HOURSE.getValue());
//        CHANNEL_TYPE_BIZ_ORDER_TYPE_MAP.put(ChannelTypeEnum.QUAN_QIU_WA.getValue(), OrderBizTypeEnum.QUAN_QIU_WA.getValue());
//        CHANNEL_TYPE_BIZ_ORDER_TYPE_MAP.put(ChannelTypeEnum.SELF_CHANNEL.getValue(), OrderBizTypeEnum.SELF_PLATFORM.getValue());


        CHANNEL_REFUND_TAG_BIZ_REFUND_TYPE.put(OrderRefundTag.HAS_NO_REFUND.getValue(), RefundTypeEnum.NO.getValue());
        CHANNEL_REFUND_TAG_BIZ_REFUND_TYPE.put(OrderRefundTag.PART_REFUND.getValue(), RefundTypeEnum.PART.getValue());
        CHANNEL_REFUND_TAG_BIZ_REFUND_TYPE.put(OrderRefundTag.FULL_REFUND.getValue(), RefundTypeEnum.ALL.getValue());

        CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.put(ChannelOrderStatus.NEW_ORDER.getValue(), OrderStatusEnum.SUBMIT.getValue());
        CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.put(ChannelOrderStatus.BIZ_CONFIRMED.getValue(), OrderStatusEnum.MERCHANT_CONFIRMED.getValue());
        CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.put(ChannelOrderStatus.FULFILLMENT.getValue(), OrderStatusEnum.PICKING.getValue());
        CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.put(ChannelOrderStatus.FINISHED.getValue(), OrderStatusEnum.COMPLETED.getValue());
        CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.put(ChannelOrderStatus.CANCEL_APPLIED.getValue(), OrderStatusEnum.REFUND_APPLIED.getValue());
        CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.put(ChannelOrderStatus.CANCELED.getValue(), OrderStatusEnum.CANCELED.getValue());
        CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.put(ChannelOrderStatus.LOCKED.getValue(), OrderStatusEnum.LOCKED.getValue());

    }

    public static Integer sourceMid2Biz(Integer channelType) {
        return DynamicOrderBizType.channelId2OrderBizTypeValue(channelType);
    }

    public static Integer sourceBiz2Mid(Integer orderBizType) {
        return DynamicOrderBizType.orderBizTypeValue2ChannelId(orderBizType);
    }

    public static Integer refundTypeMid2Biz(Integer channelRefundTag) {
        return CHANNEL_REFUND_TAG_BIZ_REFUND_TYPE.get(channelRefundTag);
    }


    public static Integer refundTypeBiz2Mid(Integer bizRefundType) {
        for (Map.Entry<Integer, Integer> entry : CHANNEL_REFUND_TAG_BIZ_REFUND_TYPE.entrySet()) {
            if (entry.getValue().equals(bizRefundType)) {
                return entry.getKey();
            }
        }
        return null;
    }

    public static List<Integer> refundTypeMid2BizList(List<Integer> channelRefundTagList) {
        List<Integer> bizRefundTypeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(channelRefundTagList)) {
            for (Integer channelRefundTag : channelRefundTagList) {
                Integer bizRefundType = refundTypeMid2Biz(channelRefundTag);
                if (bizRefundType != null) {
                    bizRefundTypeList.add(bizRefundType);
                }
            }
        }

        return bizRefundTypeList;
    }


    public static Integer orderStatusMid2Biz(Integer channelOrderStatus) {
        Integer bizOrderStatus = CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.get(channelOrderStatus);
        if (bizOrderStatus == null){
            log.error("订单状态转换失败，channelOrderStatus:{}", channelOrderStatus);
        }
        return bizOrderStatus;
    }


    public static Integer orderStatusBiz2Mid(Integer bizOrderStatus) {
        //用户申述特殊处理
        if (bizOrderStatus == OrderStatusEnum.APPEAL_APPLIED.getValue()){
            return ChannelOrderStatus.CANCEL_APPLIED.getValue();
        }
        for (Map.Entry<Integer, Integer> entry : CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.entrySet()) {
            if (entry.getValue().equals(bizOrderStatus)) {
                return entry.getKey();
            }
        }
        log.error("订单状态转换失败，bizOrderStatus:{}", bizOrderStatus);
        return null;
    }

    public static List<Integer> orderStatusMid2BizList(List<Integer> channelOrderStatusList) {
        List<Integer> bizOrderStatusList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(channelOrderStatusList)) {
            for (Integer channelOrderStatus : channelOrderStatusList) {
                Integer bizOrderStatus = orderStatusMid2Biz(channelOrderStatus);
                if (bizOrderStatus != null) {
                    bizOrderStatusList.add(bizOrderStatus);
                }
            }
        }

        return bizOrderStatusList;
    }

    public static String getChannelNameByChannelId(Integer channelId) {
        if (channelId != null) {

            if (channelId.equals(ChannelType.ELEM.getValue())) {
                return "饿了么";
            } else if (channelId.equals(ChannelType.JD2HOME.getValue())) {
                return "京东到家";
            } else if (channelId.equals(ChannelType.MEITUAN.getValue())) {
                return "美团外卖";
            } else if(channelId.equals(ChannelType.YOU_ZAN.getValue())){
                return "有赞";
            }else if(channelId.equals(ChannelTypeEnum.MT_DRUNK_HORSE.getValue())){
                return "微商城";
            }
        }
        return null;
    }

    public static String convertOperateMsg(OperatorTypeEnum operatorTypeEnum) {
        Map<String, String> operateTypeMsgMap = Lion.getConfigRepository().getMap("order.status.operateMsm");
        String msg = null;
        if (operateTypeMsgMap != null) {
            msg = operateTypeMsgMap.get(String.valueOf(operatorTypeEnum.getValue()));
        }
        return StringUtils.isBlank(msg) ? operatorTypeEnum.getDesc() : msg;
    }
}
