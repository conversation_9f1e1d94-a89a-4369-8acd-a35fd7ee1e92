package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import lombok.Data;

/**
 * 门店无限库存配置
 *
 * <AUTHOR>
 * @since 2021/5/24
 */
@Data
public class PoiStockLimitConfigBo {

    private Integer stockUnLimit;

    public static PoiStockLimitConfigBo of(boolean stockUnLimit) {
        PoiStockLimitConfigBo configBo = new PoiStockLimitConfigBo();
        configBo.setStockUnLimit(stockUnLimit ? 1 : 0);
        return configBo;
    }
}
