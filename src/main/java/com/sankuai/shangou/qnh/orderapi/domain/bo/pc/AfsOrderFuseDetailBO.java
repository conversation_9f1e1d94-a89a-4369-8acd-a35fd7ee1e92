package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderDetailVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.ProductInfoVo;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.AfterSaleApply;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.AfterSaleApplyDetail;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.AfterSaleExchangeTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.AfterSalePatternEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.sankuai.shangou.qnh.orderapi.domain.vo.AfsExchangeProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ChannelLabelVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.*;
import com.sankuai.shangou.qnh.orderapi.service.common.ExchangeItemUtil;
import com.sankuai.shangou.qnh.orderapi.utils.CombinationProductUtil;
import com.sankuai.shangou.qnh.orderapi.utils.CommonUsedUtil;
import com.sankuai.shangou.qnh.orderapi.utils.ProductLabelUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.NumberUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 售后单详情
 *
 * @Author: <EMAIL>
 * @Date: 2022/1/2 15:09
 * @Description:
 */
@Setter
@Getter
@NoArgsConstructor
@ToString
public class AfsOrderFuseDetailBO {

    /**
     * 基础信息
     */
    private BaseInfo baseInfo;

    /**
     * 账单财务信息
     */
    private BillInfo billInfo;

    /**
     * 商品信息
     */
    private List<ItemInfo> itemInfoList;

    public void init(OrderDetailVo detail, AfterSaleApply afterSaleApply) {
        baseInfo = new BaseInfo();
        baseInfo.setOrderId(detail.getOrderBaseDto().getChannelOrderId());
        baseInfo.setChannelName(detail.getOrderBaseDto().getChannelName());
        baseInfo.setPoiId(detail.getOrderBaseDto().getShopId());
        baseInfo.setDispatchShopId(detail.getOrderBaseDto().getDispatchShopId());
        baseInfo.setPoiName(detail.getOrderBaseDto().getShopName());
        baseInfo.setChannelId(detail.getOrderBaseDto().getChannelId());
        baseInfo.setOfflineOrderId(detail.getOrderBaseDto().getOrderId());
        baseInfo.setServiceId(afterSaleApply.getAfterSaleId());
        baseInfo.setWarehouseId(detail.getOrderBaseDto().getWarehouseId());
        baseInfo.setWarehouseName(detail.getOrderBaseDto().getWarehouseName());
        baseInfo.setOrderSerialNumber(detail.getOrderBaseDto().getOrderSerialNumber());
        baseInfo.setOrderSerialNumberStr(detail.getOrderBaseDto().getOrderSerialNumberStr());
        baseInfo.setChannelName(detail.getOrderBaseDto().getChannelName());
        if (CollectionUtils.isNotEmpty(afterSaleApply.getRefundPicList())){
            baseInfo.setRefundPic(String.join("," , afterSaleApply.getRefundPicList()));
        }
        baseInfo.setIsNotImport(Objects.equals(afterSaleApply.getExchangeStatusType(),
                AfterSaleExchangeTypeEnum.NOT_IMPORT.getValue()));

        Map<Long, ProductInfoVo> productInfoVoMap = detail.getProductInfoList()
                .stream()
                .collect(Collectors.toMap(ProductInfoVo::getOrderItemId, v->v));
        itemInfoList = afterSaleApply.getAfterSaleApplyDetailList().stream()
                .map(v -> new ItemInfo(v, productInfoVoMap, afterSaleApply)).collect(Collectors.toList());
        billInfo = new BillInfo();
        Integer refundAmt = afterSaleApply.getAfterSaleApplyDetailList().stream().map(AfterSaleApplyDetail::getRefundAmt).reduce(0,Integer::sum);
        billInfo.setActualPayAmt(ConverterUtils.divideHundred(refundAmt));
        billInfo.setBizReceiveAmt(ConverterUtils.divideHundred(refundAmt));

    }

    public void mergeFinanceInfo(OrderFuseFinanceDetailBO orderFuseFinanceDetailBO, AfterSaleApply afterSaleApply, OrderDetailVo detail) {
        if (orderFuseFinanceDetailBO != null){
            billInfo = new BillInfo();
            billInfo.setBizReceiveAmt(orderFuseFinanceDetailBO.getBizReceiveAmt());
            billInfo.setSelfPickService(orderFuseFinanceDetailBO.getSelfPickServiceFee());
            billInfo.setPerformService(orderFuseFinanceDetailBO.getPerformanceServiceFee());
            billInfo.setPlatItemDiscount(orderFuseFinanceDetailBO.getPlatItemPromotion());
            billInfo.setPlatDiscount(orderFuseFinanceDetailBO.getPlatPromotion());
            billInfo.setPoiItemDiscount(orderFuseFinanceDetailBO.getPoiItemPromotion());
            billInfo.setPoiDiscount(orderFuseFinanceDetailBO.getPoiPromotion());
            billInfo.setCommission(orderFuseFinanceDetailBO.getCommission());
            billInfo.setLogisticsCommission(orderFuseFinanceDetailBO.getLogisticsCommission());
            billInfo.setPlatPackage(orderFuseFinanceDetailBO.getPlatPackageIncome());
            billInfo.setPoiPackage(orderFuseFinanceDetailBO.getPoiPackageIncome());
            billInfo.setPlatPackageDiscount(orderFuseFinanceDetailBO.getPlatPackagePromotion());
            billInfo.setPoiPackageDiscount(orderFuseFinanceDetailBO.getPoiPackagePromotion());
            billInfo.setActualPayPackage(orderFuseFinanceDetailBO.getPayPackageFee());
            billInfo.setTotalPackage(orderFuseFinanceDetailBO.getOriginalPackageFee());

            billInfo.setOriginalLogisticsAmt(orderFuseFinanceDetailBO.getOriginalLogisticsFee());
            billInfo.setPlatLogisticsDiscount(orderFuseFinanceDetailBO.getPlatLogisticsPromotion());
            billInfo.setPoiLogisticsDiscount(orderFuseFinanceDetailBO.getPoiLogisticsPromotion());
            billInfo.setPoiLogisticsIncome(orderFuseFinanceDetailBO.getPoiLogisticsIncome());
            billInfo.setCustomerLogisticsTips(orderFuseFinanceDetailBO.getCustomerLogisticsTips());
            billInfo.setPoiLogisticsTips(orderFuseFinanceDetailBO.getPoiLogisticsTips());

            billInfo.setBaseFreight(Optional.ofNullable(orderFuseFinanceDetailBO.getBaseFreight()).orElse(0.0));
            billInfo.setWeightFreight(Optional.ofNullable(orderFuseFinanceDetailBO.getWeightFreight()).orElse(0.0));
            billInfo.setDistanceFreight(Optional.ofNullable(orderFuseFinanceDetailBO.getDistanceFreight()).orElse(0.0));
            billInfo.setTimeFrameFreight(Optional.ofNullable(orderFuseFinanceDetailBO.getTimeFrameFreight()).orElse(0.0));
            billInfo.setPoiFarDistanceFreight(Optional.ofNullable(orderFuseFinanceDetailBO.getPoiFarDistanceFreight()).orElse(0.0));

            billInfo.setPurchaseAmt(orderFuseFinanceDetailBO.getPurchaseAmt());
            billInfo.setPurchaseDiscountAmt(orderFuseFinanceDetailBO.getPurchaseDiscountAmt());
            billInfo.setSupplierMarketDiscount(orderFuseFinanceDetailBO.getSupplierMarketPromotion());
            billInfo.setPoiMarketDiscount(orderFuseFinanceDetailBO.getPoiMarketPromotion());
            billInfo.setOriginalAmt(orderFuseFinanceDetailBO.getOriginalAmt());
            billInfo.setActualPayAmt(orderFuseFinanceDetailBO.getActualPayAmt());
            billInfo.setDonationAmt(orderFuseFinanceDetailBO.getDonationAmt());
            billInfo.setScoreDeduction(orderFuseFinanceDetailBO.getScoreDeduction());
            billInfo.setItemSaleAmt(orderFuseFinanceDetailBO.getItemSaleAmt());
            billInfo.setItemOriginalAmt(orderFuseFinanceDetailBO.getItemOriginalAmt());
            billInfo.setBoxAmt(orderFuseFinanceDetailBO.getBoxAmt());
            billInfo.setReturnFreight(orderFuseFinanceDetailBO.getReturnFreight());
            billInfo.setShopCardTotalFee(orderFuseFinanceDetailBO.getShopCardTotalFee());
            billInfo.setShopCardBaseFee(orderFuseFinanceDetailBO.getShopCardBaseFee());
            billInfo.setShopCardGiveFee(orderFuseFinanceDetailBO.getShopCardGiveFee());
            billInfo.setShopCardPlatformGiveFeeShare(orderFuseFinanceDetailBO.getShopCardPlatformGiveFeeShare());
            billInfo.setShopCardShopGiveFeeShare(orderFuseFinanceDetailBO.getShopCardShopGiveFeeShare());
            billInfo.setRedpackAmountTotal(orderFuseFinanceDetailBO.getRedpackAmountTotal());
            billInfo.setRedpackAmountPlatform(orderFuseFinanceDetailBO.getRedpackAmountPlatform());
            billInfo.setRedpackAmountMerchant(orderFuseFinanceDetailBO.getRedpackAmountMerchant());
            billInfo.setFastDeliveryAmt(orderFuseFinanceDetailBO.getFastDeliveryAmt());
            billInfo.setAddressChangeFee(orderFuseFinanceDetailBO.getAddressChangeFee());
            billInfo.setBaseServiceFee(orderFuseFinanceDetailBO.getBaseServiceFee());

            Map<Long, OrderFuseFinanceDetailBO.ItemFinanceInfo> itemFinanceInfoMap = orderFuseFinanceDetailBO
                    .getItemInfos().stream().collect(Collectors.toMap(OrderFuseFinanceDetailBO.ItemFinanceInfo::getOrderItemId, v->v, (v1,v2)->v2));

            //组合商品需要从正单取图片
            Map<Long, ProductInfoVo> productInfoVoMap = detail.getProductInfoList()
                    .stream()
                    .collect(Collectors.toMap(ProductInfoVo::getOrderItemId, v->v));
            //以财务数据为准
            List<ItemInfo> newItemInfoList = Lists.newArrayList();
            for (ItemInfo itemInfo: itemInfoList){
                if (itemFinanceInfoMap.containsKey(itemInfo.getOrderItemId())){
                    itemInfo.mergeFinanceInfo(itemFinanceInfoMap.get(itemInfo.orderItemId),afterSaleApply, productInfoVoMap.get(itemInfo.orderItemId));
                    newItemInfoList.add(itemInfo);
                }
            }
            itemInfoList = newItemInfoList;
        }
    }

    public AfsOrderFuseDetailVO toAfsOrderFuseDetailVO() {
        AfsOrderFuseDetailVO afsOrderFuseDetailVO = new AfsOrderFuseDetailVO();
        afsOrderFuseDetailVO.setBaseInfo(ConverterUtils.nonNullConvert(baseInfo, BaseInfo::toBaseInfoVO));
        afsOrderFuseDetailVO.setBillInfo(ConverterUtils.nonNullConvert(billInfo, BillInfo::toBillInfoVO));
        //itemInfo中包含换货信息，单独处理一下
        afsOrderFuseDetailVO.setItemInfo(buildItemInfoVO2ExchangeSchme());
        //赋值地址变更费和注释（控制默认值）
        CommonUsedUtil.dealAddressChangeFee(afsOrderFuseDetailVO.getBaseInfo().getChannelId(), afsOrderFuseDetailVO.getBillInfo(), false);
        return afsOrderFuseDetailVO;
    }

    private List<AfsItemInfoVO> buildItemInfoVO2ExchangeSchme() {
        List<AfsItemInfoVO> afsItemInfoVOS = new ArrayList<>();
        for (ItemInfo itemInfo : itemInfoList){
            AfsItemInfoVO itemInfoVO = new AfsItemInfoVO();
            itemInfoVO.setSku(itemInfo.getSku());
            itemInfoVO.setCustomSkuId(itemInfo.getCustomSkuId());
            itemInfoVO.setSkuName(itemInfo.getSkuName());
            itemInfoVO.setUnit(itemInfo.getUnit());
            itemInfoVO.setUpc(itemInfo.getUpc());
            itemInfoVO.setSpec(itemInfo.getSpec());
            itemInfoVO.setErpItemCode(itemInfo.getErpItemCode());
            itemInfoVO.setOrderItemId(itemInfo.getOrderItemId());
            if(OrderBizTypeEnum.YOU_ZAN_MIDDLE.getValue() == itemInfo.getOrderBizType()){
                itemInfoVO.setRefundCount(new DecimalFormat("0.00").format(Optional.ofNullable(itemInfo.getRefundCount()).orElse(0D)));
            }else {
                itemInfoVO.setRefundCount(new DecimalFormat("0.000").format(Optional.ofNullable(itemInfo.getRefundCount()).orElse(0D)));
            }
            itemInfoVO.setPlatItemDiscount(ConverterUtils.formatMoney(itemInfo.getPlatItemPromotion()));
            itemInfoVO.setTotalPrice(ConverterUtils.formatMoney(itemInfo.getItemSaleAmt()));
            itemInfoVO.setPlatDiscount(ConverterUtils.formatMoney(itemInfo.getPlatPromotion()));
            itemInfoVO.setPoiItemDiscount(ConverterUtils.formatMoney(itemInfo.getPoiItemPromotion()));
            itemInfoVO.setPoiDiscount(ConverterUtils.formatMoney(itemInfo.getPoiPromotion()));
            itemInfoVO.setPoiMarketDiscount(ConverterUtils.formatMoney(itemInfo.getPoiMarketPromotion()));
            itemInfoVO.setSupplierMarketDiscount(ConverterUtils.formatMoney(itemInfo.getSupplierMarketPromotion()));
            itemInfoVO.setOriginPrice(ConverterUtils.formatMoney(itemInfo.getOriginPrice()));
            itemInfoVO.setSalePrice(ConverterUtils.formatMoney(itemInfo.getSalePrice()));
            itemInfoVO.setPurchaseAmt(ConverterUtils.formatMoney(itemInfo.getPurchaseAmt()));
            itemInfoVO.setPurchaseDiscountAmt(ConverterUtils.formatMoney(itemInfo.getPurchaseDiscountAmt()));
            itemInfoVO.setPicUrl(itemInfo.getPicUrl());
            if(OrderBizTypeEnum.YOU_ZAN_MIDDLE.getValue() == itemInfo.getOrderBizType()){
                String weight = StringUtils.isNotEmpty(itemInfo.getRefundWeight()) ? itemInfo.getRefundWeight() : "0.0";
                itemInfoVO.setWeight(weight);
            }else {
                int totalItemWeight = BigDecimal.valueOf(Optional.ofNullable(itemInfo.getWeight()).orElse(0D))
                        .multiply(BigDecimal.valueOf(Optional.ofNullable(itemInfo.getRefundCount()).orElse(0D)))
                        .setScale(0, RoundingMode.HALF_UP)
                        .intValue();
                itemInfoVO.setWeight(String.valueOf(totalItemWeight));
            }
            itemInfoVO.setSubProduct(itemInfo.getSubProduct());
            //处理父商品的数量，需要与产品确认是否全部都设置 有赞目前时两位小数 其余都是三位小数带.000
            itemInfoVO.setRefundCount(CombinationProductUtil.formatThreeDecimal(itemInfoVO.getRefundCount()));
            itemInfoVO.setChannelLabelList(itemInfo.getChannelLabelList());
            itemInfoVO.setLabelSubDesc(itemInfo.getLabelSubDesc());
            //售后商品对应的换货商品信息
            itemInfoVO.setAfsExchangeProduct(itemInfo.getExchangeProduct());
            itemInfoVO.setQuantity(itemInfo.getQuantity());
            afsItemInfoVOS.add(itemInfoVO);
        }
        return afsItemInfoVOS;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class BaseInfo {

        /**
         * 订单号
         */
        private String orderId;

        /**
         * 门店 ID
         */
        private Long poiId;

        /**
         * 转单门店ID
         */
        private Long dispatchShopId;

        /**
         * 门店名称
         */
        private String poiName;


        /**
         * 渠道名称
         */
        private String channelName;

        /**
         * 渠道id
         */
        private Integer channelId;


        /**
         * 创建时间
         */
        private Date createTime;


        /**
         * 服务单号
         */
        private String serviceId;


        /**
         * 售后请求状态
         */
        private String afsApplyType;


        private String orderType;

        /**
         * 百川订单号
         */
        private Long offlineOrderId;

        /**
         * 仓库id
         */
        private Long warehouseId;

        /**
         * 仓库名称
         */
        private String warehouseName;

        //接收订单时间，落库时间
        private Date receiveTime;

        /**
         * 订单流水号
         */
        private Long orderSerialNumber;

        /**
         * 订单序号
         */
        private String orderSerialNumberStr;

        private String refundPic;

        /**
         * 售后换货信息录入是否未录入，ture：未录入，false：已录入
         */
        private Boolean isNotImport;

        public AfsBaseInfoVO toBaseInfoVO() {
            AfsBaseInfoVO baseInfoVO = new AfsBaseInfoVO();

            baseInfoVO.setChannelName(channelName);
            baseInfoVO.setOrderId(orderId);
            baseInfoVO.setPoiId(poiId);
            baseInfoVO.setDispatchShopId(dispatchShopId);
            baseInfoVO.setPoiName(poiName);
            baseInfoVO.setOrderId(orderId);
            baseInfoVO.setCreateTime(createTime);
            baseInfoVO.setChannelId(channelId);
            baseInfoVO.setServiceId(serviceId);
            baseInfoVO.setAfsApplyType(afsApplyType);
            baseInfoVO.setOfflineOrderId(offlineOrderId);
            baseInfoVO.setWarehouseId(warehouseId);
            baseInfoVO.setWarehouseName(warehouseName);
            baseInfoVO.setOrderSerialNumber(orderSerialNumber);
            baseInfoVO.setOrderSerialNumberStr(orderSerialNumberStr);
            baseInfoVO.setReceiveTime(receiveTime);
            baseInfoVO.setRefundPic(refundPic);
            baseInfoVO.setIsNotImport(isNotImport);
            return baseInfoVO;
        }
    }



    @Getter
    @Setter
    @NoArgsConstructor
    @ToString
    public static class ItemInfo {

        /**
         * sku编码
         */
        private String sku;

        /**
         * 渠道sku编码
         */
        private String customSkuId;

        /**
         * 商品名称
         */
        private String skuName;

        /**
         * upc编码
         */
        private String upc;

        /**
         * 规格
         */
        private String spec;

        /**
         * 单位
         */
        private String unit;


        /**
         * 退款数量数量
         */
        private Double refundCount;

        private Double weight;


        /**
         * orderItemId
         * @param productInfo
         */
        private Long orderItemId;

        /**
         * 商品金额
         */
        private Double itemSaleAmt;

        /**
         * 原价
         */
        private double originPrice;

        /**
         * 售价
         */
        private double salePrice;

        private Double count;

        /**
         * 平台整单优惠
         */
        private Double platPromotion;

        /**
         * 平台单品优惠
         */
        private Double platItemPromotion;

        /**
         * 商家整单优惠
         */
        private Double poiPromotion;

        /**
         * 商家单品优惠
         */
        private Double poiItemPromotion;


        /**
         * 采购价
         */
        private Double purchaseAmt;

        /**
         * 采购折扣
         */
        private Double purchaseDiscountAmt;

        /**
         * 整合营销推广结算(商家）
         */
        private Double poiMarketPromotion;

        /**
         * 整合营销推广结算(供应商）
         */
        private Double supplierMarketPromotion;

        /**
         * 商品行重量
         */
        private String picUrl;

        private String erpItemCode;

        /**
         * 订单业务类型
         */
        private Integer orderBizType;

        /**
         * 退单商品重量，适用于有赞
         */
        private String refundWeight;

        /**
         * 组合商品
         */
        private List<SubProductVo> subProduct;

        /**
         * 换货商品
         */
        private AfsExchangeProductVo exchangeProduct;

        /**
         * 退款明细数量字段,非换货商品为订单上的商品数量
         */
        private String quantity;

        /**
         * 商品标签信息
         */
        private List<ChannelLabelVO> channelLabelList;

        /**
         * 商品标签附加信息
         */
        private String labelSubDesc;

        public ItemInfo(AfterSaleApplyDetail afterSaleApplyDetail, Map<Long, ProductInfoVo> productInfoVoMap,AfterSaleApply afterSaleApply) {
            ProductInfoVo productInfo = productInfoVoMap.get(afterSaleApplyDetail.getOrderItemId());
            skuName = afterSaleApplyDetail.getSkuName();
            spec = afterSaleApplyDetail.getSpecification();
            orderItemId = afterSaleApplyDetail.getOrderItemId();
            if (productInfo != null){
                sku = productInfo.getSkuId();
                upc = productInfo.getUpcCode();
                customSkuId = productInfo.getCustomerSkuId();
                picUrl = productInfo.getPicUrl();
                unit = productInfo.getSellUnit();
                if(OrderBizTypeEnum.YOU_ZAN_MIDDLE.getValue() == afterSaleApplyDetail.getOrderBizType()){
                    weight = Objects.nonNull(afterSaleApplyDetail.getRefundWeight()) ? afterSaleApplyDetail.getRefundWeight() : 0D;
                }else {
                    weight = Double.valueOf(productInfo.getWeight());
                }
                erpItemCode = productInfo.getErpItemCode();
                //处理不存在的订单
                //美团金额退显示退款数量
                if (AfterSalePatternEnum.AMOUNT.getValue() == afterSaleApply.getAfsPattern()
                        && DynamicOrderBizType.MEITUAN_WAIMAI.getValue() == afterSaleApply.getOrderBizType()) {
                    refundCount = afterSaleApplyDetail.getPartialRefundCount();
                } else if (DynamicOrderBizType.YOU_ZAN_MIDDLE.getValue() == afterSaleApply.getOrderBizType()) {
                    String refundCountDecimal = afterSaleApplyDetail.getRefundCountDecimal();
                    if (StringUtils.isNotEmpty(refundCountDecimal)) {
                        refundCount =  Double.valueOf(refundCountDecimal);
                    } else {
                        refundCount = Double.valueOf(afterSaleApplyDetail.getCount());
                    }
                } else if(DynamicOrderBizType.DOU_YIN.getValue() == afterSaleApply.getOrderBizType() && !Objects.equals(afterSaleApplyDetail.getPartialRefundCount(),0)){
                    refundCount = afterSaleApplyDetail.getPartialRefundCount();
                }
                else if (Objects.equals(afterSaleApplyDetail.getCount(),0) && afterSaleApplyDetail.getRefundWeight() !=null && afterSaleApplyDetail.getRefundWeight() > 0){
                    refundCount = BigDecimal.valueOf(afterSaleApplyDetail.getRefundWeight()).divide(BigDecimal.valueOf(weight), 3, RoundingMode.HALF_UP).doubleValue();
                } else {
                    refundCount = Double.valueOf(afterSaleApplyDetail.getCount());
                }
                itemSaleAmt = ConverterUtils.divideHundred(afterSaleApplyDetail.getRefundAmt());
                salePrice = ConverterUtils.divideHundred(productInfo.getCurrentPrice());
                originPrice = BigDecimal.valueOf(ConverterUtils.divideHundred(productInfo.getTotalOriginAmount()))
                        .divide(BigDecimal.valueOf(productInfo.getCount()),2, RoundingMode.HALF_UP)
                        .doubleValue();
                orderBizType = afterSaleApplyDetail.getOrderBizType();
                channelLabelList = ProductLabelUtil.buildChannelLabelVOList(afterSaleApplyDetail.getChannelLabel(), productInfo.getChannelLabelList());
                labelSubDesc = ProductLabelUtil.buildAfterSaleChannelLabelSubDesc(afterSaleApplyDetail.getChannelLabel(), afterSaleApplyDetail.getChannelPickingStart(), afterSaleApplyDetail.getChannelPickingEnd());
                //处理售后明细的换货商品信息，包含原商品明细信息和换入的商品信息
                exchangeProduct = ExchangeItemUtil.buildAfsExchangeProduct(afterSaleApplyDetail, productInfoVoMap);
                quantity = String.valueOf(productInfo.getCount());
            }
            //组合商品
            subProduct = CombinationProductUtil.buildCombinationProduct(afterSaleApplyDetail, productInfo);

        }

        public void mergeFinanceInfo(OrderFuseFinanceDetailBO.ItemFinanceInfo itemFinanceInfo, AfterSaleApply afterSaleApply, ProductInfoVo productInfo) {
            if (itemFinanceInfo != null){
                platItemPromotion = itemFinanceInfo.getPlatItemPromotion();
                platPromotion = itemFinanceInfo.getPlatPromotion();
                poiItemPromotion = itemFinanceInfo.getPoiItemPromotion();
                poiPromotion = itemFinanceInfo.getPoiPromotion();
                if (LionUtils.getRefundCountDisplayConfig()){
                    refundCount = itemFinanceInfo.getQuantity();
                } else {
                    if (DynamicOrderBizType.MEITUAN_WAIMAI.getValue() != afterSaleApply.getOrderBizType()
                            && AfterSalePatternEnum.AMOUNT.getValue() != afterSaleApply.getAfsPattern()){
                        refundCount = itemFinanceInfo.getQuantity();
                    }
                }
                supplierMarketPromotion = itemFinanceInfo.getSupplierMarketPromotion();
                poiMarketPromotion = itemFinanceInfo.getPoiMarketPromotion();
                purchaseAmt = itemFinanceInfo.getPurchaseAmt();
                purchaseDiscountAmt = itemFinanceInfo.getPurchaseDiscountAmt();
                originPrice = itemFinanceInfo.getOriginPrice();
                salePrice = itemFinanceInfo.getSalePrice();
                itemSaleAmt = itemFinanceInfo.getItemSaleAmt();
                refundWeight = itemFinanceInfo.getWeight();
                //存在组合品，以财务数据为准
                if(CollectionUtils.isNotEmpty(itemFinanceInfo.getComposeItemFinanceInfos())){
                    Map<Long, AfterSaleApplyDetail> afterSaleApplyDetailMap = afterSaleApply.getAfterSaleApplyDetailList()
                            .stream().collect(Collectors.toMap(AfterSaleApplyDetail::getOrderItemId, Function.identity(),
                                    (oldValue, newValue) -> oldValue));
                    subProduct = CombinationProductUtil.getFinanceComposeDetails(itemFinanceInfo.getComposeItemFinanceInfos(), productInfo, afterSaleApplyDetailMap.get(itemFinanceInfo.getOrderItemId()));

                }
            }
        }
    }

    @Getter
    @Setter
    public static class BillInfo{

        /**
         * 订单原总金额(单位:分)
         */
        private Double originalAmt;

        /**
         * 商家收入金额
         */
        private Double bizReceiveAmt;

        /**
         * 支付总金额
         */
        private Double actualPayAmt;

        /**
         * 商品原价总金额
         */
        private Double itemOriginalAmt;

        /**
         * 商品售价总金额
         */
        private Double itemSaleAmt;

        /**
         * 总的佣金
         */
        private Double totalCommission;

        /**
         * 商品佣金
         */
        private Double commission;

        /**
         * 运费佣金
         */
        private Double logisticsCommission;

        /**
         * 美团店铺环保捐赠
         */
        private Double donationAmt;

        /**
         * 积分抵扣金额
         */
        private Double scoreDeduction;

        /**
         * 履约服务费
         */
        private Double performService;

        /**
         * 自提服务费
         */
        private Double selfPickService;

        /**
         * 总的包装费
         */
        private Double totalPackage;

        /**
         * 实付包装费
         */
        private Double actualPayPackage;

        /**
         * 平台包装费优惠
         */
        private Double platPackageDiscount;

        /**
         * 商家包装费优惠
         */
        private Double poiPackageDiscount;

        /**
         * 平台包装费收入
         */
        private Double platPackage;

        /**
         * 商家包装费收入
         */
        private Double poiPackage;

        /**
         * 整单总优惠
         */
        private Double totalDiscount;

        /**
         * 单品总优惠
         */
        private Double totalItemDiscount;

        /**
         * 平台整单优惠
         */
        private Double platDiscount;

        /**
         * 平台单品优惠
         */
        private Double platItemDiscount;

        /**
         * 商家整单优惠
         */
        private Double poiDiscount;

        /**
         * 商家单品优惠
         */
        private Double poiItemDiscount;

        /**
         * 平台运费优惠
         */
        private Double platLogisticsDiscount;

        /**
         * 商家运费优惠
         */
        private Double poiLogisticsDiscount;

        /**
         * 运费（骑手）小费（商家+用户）
         */
        private Double totalLogisticsTips;

        /**
         * 商家运费小费
         */
        private Double poiLogisticsTips;

        /**
         * 用户运费小费
         */
        private Double customerLogisticsTips;

        /**
         * 总运费
         */
        private Double originalLogisticsAmt;

        /**
         * 商家运费收入
         */
        private Double poiLogisticsIncome;

        /**
         * 基础运费
         */
        private Double baseFreight;
        /**
         * 重量运费
         */
        private Double weightFreight;
        /**
         * 距离运费
         */
        private Double distanceFreight;
        /**
         * 时段运费
         */
        private Double timeFrameFreight;
        /**
         * 商家支付远距离运费
         */
        private Double poiFarDistanceFreight;

        /**
         * 整合营销推广结算(商家)
         */
        private Double poiMarketDiscount;

        /**
         * 整合营销推广结算(供应商)
         */
        private Double supplierMarketDiscount;

        /**
         * 采购金额
         */
        private Double purchaseAmt;

        /**
         * 采购折扣
         */
        private Double purchaseDiscountAmt;

        /**
         * 餐盒费
         */
        private Double boxAmt;

        /**
         * 放心退运费
         */
        private Integer returnFreight;

        // 使用购物卡总金额
        public Double shopCardTotalFee;

        //本金金额
        public Double shopCardBaseFee;

        // 赠金金额
        public Double shopCardGiveFee;

        // @FieldDoc(description = "赠金平台分摊")
        public Double shopCardPlatformGiveFeeShare;

        //  @FieldDoc(description = "赠金商家分摊")
        public Double shopCardShopGiveFeeShare;

        /**
         * 闪电送费用
         */
        public Double fastDeliveryAmt;

        /**
         * 地址变更费
         */
        public String addressChangeFee;

        // 红包支付总金额
        private Double redpackAmountTotal;

        // 红包支付金额平台承担
        private Double redpackAmountPlatform;

        // 红包支付金额商家承担
        private Double redpackAmountMerchant;

        // 基础服务费
        private Double baseServiceFee;

        public BillInfoVO toBillInfoVO() {
            BillInfoVO billInfo = new BillInfoVO();
            //退单审核之前这个字段可能为0或者负数，展示为【-】
            if (bizReceiveAmt == null || bizReceiveAmt <= 0) {
                billInfo.setBizReceiveAmt("-");
            } else {
                billInfo.setBizReceiveAmt(ConverterUtils.formatMoney(bizReceiveAmt));
            }
            billInfo.setItemOriginalAmt(ConverterUtils.formatMoney(itemOriginalAmt));
            billInfo.setItemSaleAmt(ConverterUtils.formatMoney(itemSaleAmt));
            billInfo.setSelfPickService(ConverterUtils.formatMoney(selfPickService));
            billInfo.setPerformService(ConverterUtils.formatMoney(performService));
            billInfo.setPlatItemDiscount(ConverterUtils.formatMoney(platItemDiscount));
            billInfo.setPlatDiscount(ConverterUtils.formatMoney(platDiscount));
            billInfo.setPoiItemDiscount(ConverterUtils.formatMoney(poiItemDiscount));
            billInfo.setPoiDiscount(ConverterUtils.formatMoney(poiDiscount));
            billInfo.setCommission(ConverterUtils.formatMoney(commission));
            billInfo.setLogisticsCommission(ConverterUtils.formatMoney(logisticsCommission));
            billInfo.setTotalCommission(ConverterUtils.formatMoney(NumberUtil.add(logisticsCommission,commission)));
            billInfo.setPlatPackage(ConverterUtils.formatMoney(platPackage));
            billInfo.setPoiPackage(ConverterUtils.formatMoney(poiPackage));
            billInfo.setPlatPackageDiscount(ConverterUtils.formatMoney(platPackageDiscount));
            billInfo.setPoiPackageDiscount(ConverterUtils.formatMoney(poiPackageDiscount));
            billInfo.setActualPayPackage(ConverterUtils.formatMoney(actualPayPackage));
            billInfo.setTotalPackage(ConverterUtils.formatMoney(totalPackage));
            billInfo.setOriginalLogisticsAmt(ConverterUtils.formatMoney(originalLogisticsAmt));
            billInfo.setPlatLogisticsDiscount(ConverterUtils.formatMoney(platLogisticsDiscount));
            billInfo.setPoiLogisticsDiscount(ConverterUtils.formatMoney(poiLogisticsDiscount));
            billInfo.setPoiLogisticsIncome(ConverterUtils.formatMoney(poiLogisticsIncome));
            billInfo.setBaseFreight(ConverterUtils.formatMoney(Optional.ofNullable(baseFreight).orElse(0.0)));
            billInfo.setWeightFreight(ConverterUtils.formatMoney(Optional.ofNullable(weightFreight).orElse(0.0)));
            billInfo.setDistanceFreight(ConverterUtils.formatMoney(Optional.ofNullable(distanceFreight).orElse(0.0)));
            billInfo.setTimeFrameFreight(ConverterUtils.formatMoney(Optional.ofNullable(timeFrameFreight).orElse(0.0)));
            billInfo.setPoiFarDistanceFreight(ConverterUtils.formatMoney(Optional.ofNullable(poiFarDistanceFreight).orElse(0.0)));
            billInfo.setCustomerLogisticsTips(ConverterUtils.formatMoney(customerLogisticsTips));
            billInfo.setPoiLogisticsTips(ConverterUtils.formatMoney(poiLogisticsTips));
            billInfo.setTotalLogisticsTips(ConverterUtils.formatMoney(NumberUtil.add(customerLogisticsTips, poiLogisticsTips)));
            billInfo.setTotalDiscount(ConverterUtils.formatMoney(NumberUtil.add(poiDiscount, platDiscount)));
            billInfo.setTotalItemDiscount(ConverterUtils.formatMoney(NumberUtil.add(poiItemDiscount, platItemDiscount)));
            billInfo.setPackageDiscount(ConverterUtils.formatMoney(NumberUtil.add(platPackageDiscount, poiPackageDiscount)));
            billInfo.setLogisticsDiscount(ConverterUtils.formatMoney(NumberUtil.add(poiLogisticsDiscount, platLogisticsDiscount)));
            billInfo.setPayLogisticsAmt(ConverterUtils.formatMoney(NumberUtil.subtract(NumberUtil.add(originalLogisticsAmt, fastDeliveryAmt), NumberUtil.add(poiLogisticsDiscount, platLogisticsDiscount))));
            billInfo.setPurchaseAmt(ConverterUtils.formatMoney(purchaseAmt));
            billInfo.setPurchaseDiscountAmt(ConverterUtils.formatMoney(purchaseDiscountAmt));
            billInfo.setSupplierMarketDiscount(ConverterUtils.formatMoney(supplierMarketDiscount));
            billInfo.setPoiMarketDiscount(ConverterUtils.formatMoney(poiMarketDiscount));
            billInfo.setOriginalAmt(ConverterUtils.formatMoney(originalAmt));
            billInfo.setActualPayAmt(ConverterUtils.formatMoney(actualPayAmt));
            billInfo.setDonationAmt(ConverterUtils.formatMoney(donationAmt));
            billInfo.setScoreDeduction(ConverterUtils.formatMoney(scoreDeduction));
            billInfo.setBoxAmt(ConverterUtils.formatMoney(boxAmt));
            billInfo.setReturnFreight(ConverterUtils.formatMoney(Optional.ofNullable(returnFreight).orElse(0)));
            billInfo.setShopCardTotalFee(ConverterUtils.formatMoney(shopCardTotalFee));
            billInfo.setShopCardBaseFee(ConverterUtils.formatMoney(shopCardBaseFee));
            billInfo.setShopCardGiveFee(ConverterUtils.formatMoney(shopCardGiveFee));
            billInfo.setShopCardPlatformGiveFeeShare(ConverterUtils.formatMoney(shopCardPlatformGiveFeeShare));
            billInfo.setShopCardShopGiveFeeShare(ConverterUtils.formatMoney(shopCardShopGiveFeeShare));
            billInfo.setRedpackAmountTotal(ConverterUtils.formatMoney(redpackAmountTotal));
            billInfo.setRedpackAmountPlatform(ConverterUtils.formatMoney(redpackAmountPlatform));
            billInfo.setRedpackAmountMerchant(ConverterUtils.formatMoney(redpackAmountMerchant));
            billInfo.setFastDeliveryAmt(ConverterUtils.formatMoney(fastDeliveryAmt));
            billInfo.setBaseServiceFee(ConverterUtils.formatMoney(baseServiceFee));
            if (StringUtils.isNotBlank(addressChangeFee)){
                billInfo.setAddressChangeFee(addressChangeFee);
            }
            return billInfo;
        }
    }
}

