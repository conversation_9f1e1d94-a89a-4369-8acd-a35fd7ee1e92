package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.cat.util.Pair;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.annotation.Degrade;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.common.enums.OrderCanOperateItem;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.dto.request.ocms.TenantWeightRefundRequest;
import com.meituan.shangou.saas.dto.response.OrderTrackDetailResponse;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderWeightRefundItemModel;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderKey;
import com.meituan.shangou.saas.o2o.dto.model.OCMSRefundReasonAndCodeModel;
import com.meituan.shangou.saas.o2o.dto.request.OCMSCheckRefundRequest;
import com.meituan.shangou.saas.o2o.dto.request.OCMSOperateCheckRequest;
import com.meituan.shangou.saas.o2o.dto.response.OCMSOperateCheckResponse;
import com.meituan.shangou.saas.o2o.dto.response.OCMSRefundCheckResponse;
import com.meituan.shangou.saas.order.management.client.dto.request.OrderDetailSearchRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.OrderSearchRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderDetailReq;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderListReq;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsOrderDetailResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsOrderSearchResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.AfterSaleRecordVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.DeliveryStatusLogVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.DrunkHorseGiftBagVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderBaseVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderDetailVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderInfoVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderListPageVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.PageInfoVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.ProductInfoVo;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.OrderSearchResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.AfterSaleApply;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.AfterSaleApplyDetail;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.DeliveryInfoDTO;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.DeliveryInfoExtend;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderDetail;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderItem;
import com.meituan.shangou.saas.order.management.client.enums.ChannelType;
import com.meituan.shangou.saas.order.management.client.service.OrderSearchService;
import com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchService;
import com.meituan.shangou.saas.order.management.client.utils.BusinessIdTracer;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderLabelModel;
import com.meituan.shangou.saas.order.management.client.utils.param.DesensitizeReceiverBaseInfoParam;
import com.meituan.shangou.saas.order.management.client.utils.result.DesensitizeReceiverInfoResult;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.AfterSalePatternEnum;
import com.meituan.shangou.saas.order.platform.enums.DistributeMethodEnum;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.OperatorTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.service.ocms.OCMSOrderOperateThriftService;
import com.meituan.shangou.saas.service.ocms.OCMSOrderThriftService;
import com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiDetailInfoResponse;
import com.sankuai.meituan.reco.pickselect.consts.OpenOpType;
import com.sankuai.meituan.reco.pickselect.thrift.OpenPickThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTraceDetail;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTraceQueryRequest;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTraceResponse;
import com.sankuai.meituan.reco.pickselect.thrift.TaskDetail;
import com.sankuai.meituan.reco.pickselect.thrift.picking.dto.PickWODetailDTO;
import com.sankuai.meituan.reco.pickselect.thrift.print.OpenPrintResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery.DeliveryPathRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery.DeliveryPathResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.AfterSaleRecord;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.AfterSaleRecordDetail;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelThriftResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ClientInfo;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderBaseDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderDetailReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderDetailResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderListReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderSearchResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.PageInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.PrintReceiptReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.PrintReceiptResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.PrintStatusResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ProductInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.PromotionInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryVirtualPhoneReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryVirtualPhoneResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.RefundCheckReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.RefundCheckResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.RefundReasonAndCode;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.RefundableAmountResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.RefundableMoneyReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SimpleOrderStatusLogViewDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.TenantCancelOrderReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.TenantCancelOrderResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.TenantPartRefundProductInfo;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.TenantPartRefundReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.TenantPartRefundResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeviceTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderCouldOperateItem;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelOrderTenantThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.JDDeliveryFailTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.DeliveryOperateItem;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.QueryOrderDeliveryInfoKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryDeliveryOrderResponse;
import com.sankuai.meituan.shangou.saas.common.data.PageResult;
import com.sankuai.shangou.qnh.orderapi.configuration.store.OrderGrayConfiguration;
import com.sankuai.shangou.qnh.orderapi.constant.app.ProjectConstants;
import com.sankuai.shangou.qnh.orderapi.converter.store.ChannelOrderConverter;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.BaseOrderReq;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.CancelOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.CheckRefundRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.GetDynamicInfoReq;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.GetImmutableInfoReq;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.OrderDetailRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.OrderListRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.PartRefundRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.PrintReceiptRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.QueryPrintStatusRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.QueryRefundableAmountRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.QueryVirtualPhoneRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.RefundByWeightRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.WeightRefundCheckRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CheckRefundResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.OrderDetailResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.PrintReceiptResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QueryPrintStatusResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QueryRefundableAmountResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QuerySystemInfoResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QueryVirtualPhoneResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.WeightRefundCheckResponse;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Result;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.ResultBuilder;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.AfterSaleRecordDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.AfterSaleRecordVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CoordinateVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CustomerInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.DeliveryDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.DeliveryDynamicInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.DeliveryLogVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.DeliveryOperationLogVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.DeliveryPathImmutableInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.DrunkHorseGiftBagVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderItemWeightRefundCheckVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderListByCommentVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderStatusLogVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PageInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PartRefundProductVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ProductVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PromotionVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.QnhKaTenantChannelVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.RefundReasonAndCodeVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.RiderInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.TagInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.User;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.WeightRefundAfterSaleRecordVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.WeightRefundProductVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.order.ChannelInfoVO;
import com.sankuai.shangou.qnh.orderapi.enums.store.ApiDeliveryOperateItemEnum;
import com.sankuai.shangou.qnh.orderapi.enums.store.ChannelTypeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.store.FulfillWorkOrderStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.store.OrderCouldOperateItemEnum;
import com.sankuai.shangou.qnh.orderapi.enums.store.PickingWorkOrderStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.store.UserTagTypeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.store.WorkOrderTypeEnum;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonLogicException;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.service.InvoiceService;
import com.sankuai.shangou.qnh.orderapi.service.pc.FuseOrderService;
import com.sankuai.shangou.qnh.orderapi.service.pc.TenantService;
import com.sankuai.shangou.qnh.orderapi.service.store.AppendDeliveryInfoService;
import com.sankuai.shangou.qnh.orderapi.service.store.AuthThriftWrapperService;
import com.sankuai.shangou.qnh.orderapi.utils.CommonUsedUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.OrderUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.utils.store.ConvertUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.store.MoneyUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.OCMSUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.ToIntFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sankuai.shangou.qnh.orderapi.enums.store.ChannelTypeEnum.MEITUAN;
import static com.meituan.shangou.saas.order.management.client.utils.DesensitizeReceiverInfoUtil.desensitizeReceiverInfo;

/**
 * <AUTHOR>
 * @date 2019/7/15
 * desc: 中台订单服务
 */
@Service
@Slf4j
public class OCMSOrderRemoteService {

    private static final String SYMBOL_COMMA = ",";

    private static final String RIDER_NAME_FORMAT = "（%s）";

    private final static List<Integer> DEFAULT_CHECK_ITEMS =
            ImmutableList.copyOf(Arrays.stream(OrderCouldOperateItemEnum.values())
                    .map(OrderCouldOperateItemEnum::getValue).collect(Collectors.toList()));

    private final static String DEFAULT_COORDINATE_VALUE = "0";

    @Resource
    private ChannelOrderTenantThriftService.Iface channelOrderTenantThriftService;

    @Resource
    private ChannelThriftService.Iface channelThriftService;

    @Resource
    private ChannelDeliveryThriftService.Iface channelDeliveryThriftService;

    @Resource
    private OCMSOrderOperateThriftService ocmsOrderOperateThriftService;

    @Resource
    private OCMSOrderThriftService ocmsOrderThriftService;

    @Resource
    private OrderSearchService orderSearchService;

    @Resource
    private AuthThriftWrapperService authThriftWrapper;

    @Resource
    private OcmsChannelRemoteService ocmsChannelFacade;

    @Resource
    private PickSelectStoreRemoteService pickSelectFacade;

    @Resource
    private OrderBizForStoreRemoteService orderBizFacade;

    @Autowired
    private OcmsOrderSearchService ocmsOrderSearchService;

    @Autowired
    private OpenPickThriftService.Iface openPickThriftService;

    @Autowired
    private ChannelPoiManageThriftService channelPoiManageThriftService;

    @Autowired
    private DeliveryRemoteService deliveryServiceWrapper;

    @Autowired
    private AppendDeliveryInfoService appendDeliveryInfoService;
    @Autowired
    private DeliveryChannelRemoteService deliveryChannelWrapper;

    @Autowired
    private PoiRemoteService poiRemoteService;

    @Autowired
    private InvoiceService invoiceService;

    @Resource
    private LoginRemoteService loginRemoteService;

    @Resource
    private TenantService tenantService;

    @Autowired
    private FuseOrderService fuseOrderService;


    @Degrade(rhinoKey = "OCMSOrderRemoteService.orderList",
            fallBackMethod = "orderListFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<OrderListResponse> orderList(OrderListRequest request) {
        if (!OrderGrayConfiguration.hitOcmsMigration(request.getStoreId())) {
            return orderListFromOcms(request);
        }
        OcmsOrderListReq orderListReq = buildOcmsOrderListReq(request);
        log.info("OCMSOrderRemoteService.orderList  调用ocmsOrderSearchService.orderList request:{}", orderListReq);
        try {
            OcmsOrderSearchResponse response = ocmsOrderSearchService.orderList(orderListReq);
            log.info("OCMSOrderRemoteService.orderList  调用ocmsOrderSearchService.orderList response:{}", response);
            Integer code = Optional.ofNullable(response).map(OcmsOrderSearchResponse::getResponseStatus).orElse(-1);
            List<OrderInfoVo> orders = Optional.ofNullable(response).map(OcmsOrderSearchResponse::getOrderListResp)
                    .map(OrderListPageVo::getOrders).orElse(Collections.emptyList());
            if (code == 0 && !orders.isEmpty()) {
                Set<Integer> operateItems = new TreeSet<>();
                if (CollectionUtils.isNotEmpty(request.getShowOperateItems())) {
                    operateItems.addAll(request.getShowOperateItems());
                    operateItems.add(OrderCouldOperateItemEnum.WEIGHT_REFUND.getValue());
                }
                fillOperateItem(operateItems.isEmpty() ? DEFAULT_CHECK_ITEMS : new ArrayList<>(operateItems), orders);
            }
            OrderListResponse orderListResponse = buildOrderListResponse(response);
            // 过滤员工的元素权限
            filterAccountPermission(orderListResponse.getOrderList(), request.getStoreId());
            return new CommonResponse(response.getResponseStatus(), response.getMsg(), orderListResponse);
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.orderList  调用cocmsOrderSearchService.orderLis error", e);
            throw new CommonRuntimeException(e);
        }
    }

    private void fillOperateItem(List<Integer> showOperateItems, List<OrderInfoVo> orders) throws TException {
        Map<OCMSOrderKey, OrderInfoVo> orderKeyMap = orders.stream()
                .collect(Collectors.toMap(order -> OCMSOrderKey.builder()
                        .channelOrderId(order.getChannelOrderId())
                        .channelType(order.getChannelId()).build(), order -> order, (first, second) -> first));
        Map<OCMSOrderKey, List<Integer>> orderKeyListMap = orderBizFacade.queryOrderOperateItems(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
                new ArrayList<>(orderKeyMap.keySet()), showOperateItems);
        if (MapUtils.isNotEmpty(orderKeyListMap)) {
            orderKeyMap.forEach((key, value) -> value.setCouldOperateItemList(orderKeyListMap.get(key)));
        }
    }

    @Deprecated
    public CommonResponse<OrderListResponse> orderListFromOcms(OrderListRequest request) {
        OrderListReq orderListReq = buildOrderListReq(request);
        log.info("OCMSOrderRemoteService.orderList  调用channelOrderTenantThriftService.orderList request:{}", orderListReq);
        try {
            OrderSearchResp response = channelOrderTenantThriftService.orderList(orderListReq);
            log.info("OCMSOrderRemoteService.orderList  调用channelOrderTenantThriftService.orderList response:{}", response);
            OrderListResponse orderListResponse = buildOrderListResponse(response);
            //退差价入口查询并设置
            setWeightRefundOperateItem(orderListResponse.getOrderList());
            //过滤员工的元素权限
            filterAccountPermission(orderListResponse.getOrderList(), request.getStoreId());
            return new CommonResponse(response.getStatus().getCode(), response.getStatus().getMsg(), orderListResponse);
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.orderList  调用channelOrderTenantThriftService.orderLis error", e);
            throw new CommonRuntimeException(e);
        }
    }

    protected void filterAccountPermission(List<OrderVO> orderList, Long storeId) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (isUserValid(identityInfo) && CollectionUtils.isNotEmpty(orderList) && Objects.nonNull(storeId)) {
            Map<Integer, Boolean> accountAuthCodeMap = queryAccountCode(storeId);
            orderList.stream().filter(orderVo -> CollectionUtils.isNotEmpty(orderVo.getCouldOperateItemList())).forEach(orderVO -> {
                //权限code与订单可操作项diff
                List<Integer> couldOperateItems = orderVO.getCouldOperateItemList();
                List<Integer> finalCouldOperateItems = couldOperateItems.stream().filter(operateItem -> {
                    return Objects.isNull(accountAuthCodeMap.get(operateItem)) || BooleanUtils.isTrue(accountAuthCodeMap.get(operateItem));//没有配置，或者权限没有返回都当做有权限
                }).collect(Collectors.toList());
                log.info("权限过滤，order:{}, account:{}, couldOperateItems:{}, finalCouldOperateItems:{}", orderVO.getChannelOrderId(), identityInfo.getUser().getAccountId(), couldOperateItems, finalCouldOperateItems);
                orderVO.setCouldOperateItemList(finalCouldOperateItems);
            });
        }
    }

    private void filterOrderDetailPermission(OrderDetailVO orderDetail, Long storeId) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (isUserValid(identityInfo) && Objects.nonNull(orderDetail) &&
                CollectionUtils.isNotEmpty(orderDetail.getCouldOperateItemList()) && Objects.nonNull(storeId)) {
            Map<Integer, Boolean> accountAuthCodeMap = queryAccountCode(storeId);
            //权限code与订单可操作项diff
            List<Integer> couldOperateItems = orderDetail.getCouldOperateItemList();
            List<Integer> finalCouldOperateItems = couldOperateItems.stream().filter(operateItem -> {
                return Objects.isNull(accountAuthCodeMap.get(operateItem)) || BooleanUtils.isTrue(accountAuthCodeMap.get(operateItem));//没有配置，或者权限没有返回都当做有权限
            }).collect(Collectors.toList());
            log.info("权限过滤，order:{}, account:{}, couldOperateItems:{}, finalCouldOperateItems:{}", orderDetail.getChannelOrderId(), identityInfo.getUser().getAccountId(), couldOperateItems, finalCouldOperateItems);
            orderDetail.setCouldOperateItemList(finalCouldOperateItems);
        }
    }

    private boolean isUserValid(IdentityInfo identityInfo) {
        return Objects.nonNull(identityInfo) && Objects.nonNull(identityInfo.getUser());
    }

    private Map<Integer, Boolean> queryAccountCode(Long storeId) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Map<String, Integer> permissionCodeMap = fetchPermissionFromConfig();
        if (identityInfo != null && identityInfo.getUser() != null && permissionCodeMap != null && permissionCodeMap.size() > 0) {
            try {
                User user = identityInfo.getUser();
                long accountId = user.getAccountId();
                Map<String, Boolean> authMap = authThriftWrapper.authPermissionAndDataAuth(accountId, storeId, org.assertj.core.util.Lists.newArrayList(permissionCodeMap.keySet()));
                log.info("请求订单元素权限：{}， result:{}", accountId, authMap);
                return authMap.entrySet().stream().collect(Collectors.toMap(entry -> permissionCodeMap.get(entry.getKey()), entry -> entry.getValue(), (f, s) -> s));
            } catch (Exception e) {
                log.error("请求权限系统接口异常", e);
            }
        }
        log.info("没有配置待检查的元素权限");
        return Maps.newHashMap();
    }

    private Map<String, Integer> fetchPermissionFromConfig() {
        String permissionCodes = MccConfigUtil.getOrderOperatorAuthCodes();
        if (StringUtils.isNotBlank(permissionCodes)) {
            /**
             * 解析  ACCEPT_ORDER,1;REFUND,2;.....
             * ***/
            List<String> permissionMap = Splitter.on(";").splitToList(permissionCodes);
            return permissionMap.stream().map(e -> Splitter.on(",").splitToList(e)).filter(e -> e != null && e.size() >= 2).collect(Collectors.toMap(e -> e.get(0), e -> NumberUtils.toInt(e.get(1)), (f, s) -> s));
        }
        return Maps.newHashMap();
    }

    public CommonResponse<OrderListResponse> orderListFallback(OrderListRequest request) {
        log.info("OCMSOrderRemoteService.orderList  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }

    @Degrade(rhinoKey = "OCMSOrderRemoteService.orderDetail",
            fallBackMethod = "orderDetailFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<OrderDetailResponse> orderDetail(OrderDetailRequest request) {
        OcmsOrderDetailReq orderDetailReq = buildOcmsOrderDetailReq(request);
        log.info("OCMSOrderRemoteService.orderDetail  调用ocmsOrderSearchService.orderDetail request:{}", orderDetailReq);
        try {
            OcmsOrderDetailResponse response = ocmsOrderSearchService.orderDetail(orderDetailReq);
            log.info("OCMSOrderRemoteService.orderDetail  调用ocmsOrderSearchService.orderDetail response:{}", response);
            OrderDetailResponse orderDetailResponse = buildOcmsOrderDetailResponse(response,
                    ImmutableList.of(OrderCouldOperateItemEnum.WEIGHT_REFUND), getAuthedStoreIds(request));
            // TODO 需要确定权限是否要改
            filterOrderDetailPermission(orderDetailResponse.getOrderDetail(), request.getStoreId());
            setOrderDeliveryInfo(orderDetailResponse);
            return new CommonResponse(response.getResponseStatus(), response.getMsg(), orderDetailResponse);
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.orderList  调用ocmsOrderSearchService.orderDetail error", e);
            throw new CommonRuntimeException(e);
        }
    }

    // 获取当前登录用户有权限的门店ID（用于转单场景下履约权校验）
    private Set<Long> getAuthedStoreIds(OrderDetailRequest request) {
        Long currentStoreId = getCurrentStoreIdFromHeader();
        // 兼容小程序端header中无storeId参数
        if (Objects.isNull(currentStoreId)) {
            currentStoreId = request.getStoreId();
        }
        if (Objects.isNull(currentStoreId)) {
            log.warn("#OCMSOrderRemoteService.getAuthedStoreIds#header&request中storeId均为空:request_({})", request);
        }
        if (Objects.nonNull(currentStoreId) && ProjectConstants.FULL_STORE_ID_LONG != currentStoreId) {
            // 按接口协议约定，此请求为单门店模式
            return Sets.newHashSet(currentStoreId);
        } else {
            // 按接口协议约定，此请求为全部门店模式
            return Sets.newHashSet(loginRemoteService.getCurrentUserFullStoreList());
        }
    }


    @Degrade(rhinoKey = "OCMSOrderRemoteService.getDeliveryPathImmutableInfo",
            fallBackMethod = "orderDetailFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<DeliveryPathImmutableInfoVO> getDeliveryPathImmutableInfo(GetImmutableInfoReq request) {
        OcmsOrderDetailReq orderDetailReq = buildOcmsOrderDetailReq(request);
        log.info("OCMSOrderRemoteService.orderList  调用ocmsOrderSearchService.orderDetail request:{}", orderDetailReq);
        try {
            long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
            OcmsOrderDetailResponse response = ocmsOrderSearchService.orderDetail(orderDetailReq);
            log.info("OCMSOrderRemoteService.orderList  调用ocmsOrderSearchService.orderDetail response:{}", response);

            if(response.getResponseStatus()!=StatusCodeEnum.SUCCESS.getCode()){
                return CommonResponse.fail(response.getResponseStatus(),"获取订单详情失败");
            }
            DeliveryPathImmutableInfoVO deliveryPathImmutableInfoVO = new DeliveryPathImmutableInfoVO();
            DeliveryInfoDTO deliveryInfoDTO = response.getOrder().getDeliveryInfoDTO();
            CoordinateVO customerCoordinate = new CoordinateVO();
            DeliveryInfoExtend deliveryInfoExtend = deliveryInfoDTO.getExtData();
            boolean isNeedDesensitize = false;
            if (com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil.isSupportDesensitizeReceiverInfoTenants(response.getOrder().getOrderBaseDto().getTenantId())) {
                isNeedDesensitize = isReceiverInfoNeedDesensitize(response);
            }
            // 由于距离由前端计算 故还是返回收货人坐标，前端根据标识控制地图收货人图标是否展示
            customerCoordinate.setLatitude(processNullOrEmpty(deliveryInfoExtend.getReceiverLatitude()));
            customerCoordinate.setLongitude(processNullOrEmpty(deliveryInfoExtend.getReceiverLongitude()));
            deliveryPathImmutableInfoVO.setCustomerCoordinate(customerCoordinate);

            Long shopId = response.getOrder().getOrderBaseDto().getShopId();

            CoordinateVO shopCoordinate = new CoordinateVO();
            PoiDetailInfoResponse poiDetailInfoResponse = channelPoiManageThriftService.queryPoiDetailInfoByPoiId(tenantId, shopId);

            if(poiDetailInfoResponse.getStatus().getCode()!=StatusCodeEnum.SUCCESS.getCode() || poiDetailInfoResponse.getPoiDetailInfoDTO()==null){
                return CommonResponse.fail(poiDetailInfoResponse.getStatus().getCode(),"获取百川商家信息失败");
            }
            if(CollectionUtils.isEmpty(poiDetailInfoResponse.getPoiDetailInfoDTO().getChannelPoiInfoList())){
                return CommonResponse.fail(poiDetailInfoResponse.getStatus().getCode(),"未关联渠道门店");
            }
            Optional<ChannelPoiInfoDTO> channelPoiOptional = poiDetailInfoResponse
                    .getPoiDetailInfoDTO()
                    .getChannelPoiInfoList()
                    .stream()
                    .filter(e->Objects.equals(e.getChannelId(),request.getChannelId())).findAny();
            if(!channelPoiOptional.isPresent()){
                return CommonResponse.fail(poiDetailInfoResponse.getStatus().getCode(),"未找到对应渠道门店");
            }
            ChannelPoiInfoDTO channelPoiInfoDTO = channelPoiOptional.get();
            shopCoordinate.setLatitude(getShopCoordinate(poiDetailInfoResponse.getPoiDetailInfoDTO(), channelPoiInfoDTO).getKey());
            shopCoordinate.setLongitude(getShopCoordinate(poiDetailInfoResponse.getPoiDetailInfoDTO(), channelPoiInfoDTO).getValue());
            deliveryPathImmutableInfoVO.setShopCoordinate(shopCoordinate);
            DesensitizeReceiverInfoResult desensitizeReceiverInfoResult = new DesensitizeReceiverInfoResult(deliveryInfoDTO.getUserName(), deliveryInfoDTO.getUserAddress(), deliveryInfoDTO.getUserPhone(), null);
            if (isNeedDesensitize) {
                DesensitizeReceiverInfoResult result = desensitizeReceiverInfo(new DesensitizeReceiverBaseInfoParam(deliveryInfoDTO.getUserName(), deliveryInfoDTO.getUserAddress(), deliveryInfoDTO.getUserPhone(), null));
                if (Objects.nonNull(result)) {
                    // 脱敏时发生异常 result可能为空 故判空
                    desensitizeReceiverInfoResult = result;
                }
            }
            CustomerInfoVO customerInfoVO = new CustomerInfoVO();
            customerInfoVO.setName(desensitizeReceiverInfoResult.getReceiverName());
            customerInfoVO.setAddress(desensitizeReceiverInfoResult.getReceiverAddress());
            customerInfoVO.setPhoneNum(desensitizeReceiverInfoResult.getReceiverPhone());
            customerInfoVO.setTags(UserTagTypeEnum.getTagList(response.getOrder().getOrderBaseDto().getTags()));
            customerInfoVO.setExpectArrayTime(String.valueOf(deliveryInfoDTO.getArrivalTime()));
            deliveryPathImmutableInfoVO.setCustomerInfo(customerInfoVO);
            deliveryPathImmutableInfoVO.setOrderDetailVo(response.getOrder());
            deliveryPathImmutableInfoVO.setIsDesensitizedReceiverInfo(isNeedDesensitize);

            deliveryPathImmutableInfoVO.setDeliveryDetailVO(getDeliveryDetailVO(response.getOrder()));
            int totalWeight = 0;
            if(CollectionUtils.isNotEmpty(response.getOrder().getProductInfoList())){
                totalWeight = response.getOrder().getProductInfoList().stream().mapToInt(new ToIntFunction<ProductInfoVo>() {
                    @Override
                    public int applyAsInt(ProductInfoVo value) {
                        if(value==null || value.getCount()==null || value.getWeight()==null){
                            return 0;
                        }
                        return value.getCount() * value.getWeight();
                    }
                }).sum();
            }
            deliveryPathImmutableInfoVO.setTotalWeight(totalWeight+"");
            deliveryPathImmutableInfoVO.setCurrentTime(System.currentTimeMillis()+"");
            return new CommonResponse(0, "success", deliveryPathImmutableInfoVO);
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.getDeliveryPathImmutableInfo ", e);
            throw new CommonRuntimeException(e);
        }

    }


    /**
     * 判断是否需要脱敏
     * @param response 订单详情
     * @return 是否脱敏
     */
    private boolean isReceiverInfoNeedDesensitize(OcmsOrderDetailResponse response) {
        DeliveryInfoDTO deliveryInfoDTO = response.getOrder().getDeliveryInfoDTO();
        if (Objects.nonNull(deliveryInfoDTO.getDistributeStatus())
                && CollectionUtils.isNotEmpty(response.getOrder().getDeliveryStateLogList())
                && (DistributeStatusEnum.enumOf(deliveryInfoDTO.getDistributeStatus()) == DistributeStatusEnum.RIDER_DELIVERED
                 || DistributeStatusEnum.enumOf(deliveryInfoDTO.getDistributeStatus()) == DistributeStatusEnum.DISTRIBUTE_CANCELED)) {
            Optional<DeliveryStatusLogVo> deliveryStatusLogVoOptional = response.getOrder().getDeliveryStateLogList()
                    .stream()
                    .sorted(Comparator.comparingLong(DeliveryStatusLogVo::getUpdateTime).reversed())
                    .filter(e -> Objects.equals(e.getTargetStatus(), deliveryInfoDTO.getDistributeStatus()))
                    .findFirst();
            if (deliveryStatusLogVoOptional.isPresent()) {
                Long updateTime = deliveryStatusLogVoOptional.get().getUpdateTime();
                return System.currentTimeMillis() - updateTime > MccDynamicConfigUtil.getDesensitizeReceiverInfoTime() * 1000;
            }
        }

        return false;
    }
    /**
     * 优先返回渠道门店坐标，渠道门店坐标不存在再返回中台门店坐标
     */
    private Pair<String, String> getShopCoordinate(PoiDetailInfoDTO poiDetailInfoDTO, ChannelPoiInfoDTO channelPoiInfoDTO) {
        if (Objects.isNull(poiDetailInfoDTO) || Objects.isNull(channelPoiInfoDTO)) {
            log.error("getShopCoordinate, poiDetailInfoDTO or channelPoiInfoDTO is null");
            return new Pair<>(DEFAULT_COORDINATE_VALUE, DEFAULT_COORDINATE_VALUE);
        }

        Double channelLatitude = channelPoiInfoDTO.getLatitude();
        Double channelLongitude = channelPoiInfoDTO.getLongitude();
        if (isCoordinateValid(channelLatitude) && isCoordinateValid(channelLongitude)) {
            return new Pair<>(processNullOrEmpty(String.valueOf(channelLatitude)), processNullOrEmpty(String.valueOf(channelLongitude)));
        }

        Double qnhLatitude = poiDetailInfoDTO.getLatitude();
        Double qnhLongitude = poiDetailInfoDTO.getLongitude();
        if (isCoordinateValid(qnhLatitude) && isCoordinateValid(qnhLongitude)) {
            return new Pair<>(processNullOrEmpty(String.valueOf(qnhLatitude)), processNullOrEmpty(String.valueOf(qnhLongitude)));
        }

        return new Pair<>(DEFAULT_COORDINATE_VALUE, DEFAULT_COORDINATE_VALUE);
    }

    private boolean isCoordinateValid(Double coordinate) {
        return Objects.nonNull(coordinate) && coordinate > NumberUtils.DOUBLE_ZERO;
    }

    private QueryOrderDeliveryInfoKey buildQueryOrderKey(OrderDetailVo order) {
        Long shopId=order.getOrderBaseDto().getShopId();
        if(order.getOrderBaseDto().getWarehouseId()!=null){
            shopId=order.getOrderBaseDto().getWarehouseId();
        }
        long orderId = order.getOrderBaseDto().getOrderId();
        return new QueryOrderDeliveryInfoKey(order.getOrderBaseDto().getTenantId(), shopId, orderId, order.getOrderBaseDto().getOrderStatus(), order.getOrderBaseDto().getOrderSource(), order.getOrderBaseDto().getDeliveryMethod(), order.getOrderBaseDto().getIsSelfDelivery());

    }

    private DeliveryDetailVO getDeliveryDetailVO(OrderDetailVo order) {
        DeliveryDetailVO deliveryDetailVO = new DeliveryDetailVO();
        try {

            Map<Long, TDeliveryDetail> deliveryDetailMap = appendDeliveryInfoService.queryRealTimeDeliveryInfo(Arrays.asList(buildQueryOrderKey(order)));

            if (org.apache.commons.collections.MapUtils.isNotEmpty(deliveryDetailMap) && deliveryDetailMap.containsKey(order.getOrderBaseDto().getOrderId())) {
                TDeliveryDetail deliveryOrder = deliveryDetailMap.get(order.getOrderBaseDto().getOrderId());
                if(deliveryOrder!=null && deliveryOrder.status!=null){
                    deliveryDetailVO.setDeliveryTipAmount(deliveryOrder.tipFee);
                    deliveryDetailVO.setDeliveryFeeAmount(deliveryOrder.deliveryFee);
                    deliveryDetailVO.setExceptionCode(deliveryOrder.deliveryExceptionCode);
                    if(StringUtils.isNotEmpty(deliveryOrder.allowLatestAuditTime)){
                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        Date date = (Date) formatter.parse(deliveryOrder.allowLatestAuditTime);
                        deliveryDetailVO.setAllowLatestAuditTime(date.getTime());
                    }

                    DeliveryExceptionSubTypeEnum deliveryExceptionSubTypeEnum = DeliveryExceptionSubTypeEnum.deliveryStatusCodeValueOfWithOutAll(
                            Optional.ofNullable(deliveryOrder.deliveryExceptionType).orElse(null),
                            deliveryOrder.deliveryExceptionCode,
                            deliveryOrder.status);
                    deliveryDetailVO.setExceptionViewCode(ConvertUtils.getOrderViewStatusByExceptionOrderSubType(deliveryExceptionSubTypeEnum));
                }
            }
        } catch (Exception e) {
            log.error("OCMSOrderRemoteService.getDeliveryTipAmount,查询小费信息异常 order:{}", order, e);
        }
        return deliveryDetailVO;
    }

    private String processNullOrEmpty(String source) {
        try{
            return String.valueOf(Double.parseDouble(source));
        }catch (Exception e){
            return "0";
        }
    }

    @Degrade(rhinoKey = "OCMSOrderRemoteService.getDeliveryPathDynamicInfo",
            fallBackMethod = "orderDetailFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<DeliveryDynamicInfoVO> getDeliveryPathDynamicInfo(GetDynamicInfoReq request) {
        OcmsOrderDetailReq orderDetailReq = buildOcmsOrderDetailReq(request);
        log.info("OCMSOrderRemoteService.findOrderCoordinate  调用ocmsOrderSearchService.findOrderCoordinate request:{}", orderDetailReq);
        try {
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            long tenantId = user.getTenantId();

            OcmsOrderDetailResponse response = ocmsOrderSearchService.orderDetail(orderDetailReq);
            log.info("OCMSOrderRemoteService.findOrderCoordinate  调用ocmsOrderSearchService.findOrderCoordinate response:{}",
                    response);
            OrderDetailVo order = response.getOrder();
            if (order == null) {
                return CommonResponse.fail(0, "无订单信息");
            }
            DeliveryInfoDTO deliveryInfoDTO = order.getDeliveryInfoDTO();
            if (deliveryInfoDTO == null) {
                return CommonResponse.fail(0, "订单无配送信息");
            }

            DeliveryDynamicInfoVO deliveryPathDynamicInfoVO = new DeliveryDynamicInfoVO();
            List<DeliveryStatusLogVo> deliveryStatusLogVoList = order
                    .getDeliveryStateLogList()
                    .stream()
                    .peek(e -> {
                        String riderName = String.format(RIDER_NAME_FORMAT, e.getRiderName());
                        String newDesc = e.getStateDesc().replace(riderName, "");
                        e.setStateDesc(newDesc);
                    })
                    .collect(Collectors.toList());
            deliveryPathDynamicInfoVO.setPathLog(deliveryStatusLogVoList);
            deliveryPathDynamicInfoVO.setLogs(queryAndBuildDeliveryLogs(request));
            deliveryPathDynamicInfoVO.setDeliveryStatus(String.valueOf(deliveryInfoDTO.getDistributeStatus()));
            deliveryPathDynamicInfoVO.setRiderName(deliveryInfoDTO.getRiderName());
            deliveryPathDynamicInfoVO.setRiderPhone(deliveryInfoDTO.getRiderPhone());
            deliveryPathDynamicInfoVO.setDistributeMethodName(deliveryInfoDTO.getDistributeMethodName());
            deliveryPathDynamicInfoVO.setLockOrderState(Objects.nonNull(deliveryInfoDTO.getLockOrderState()) ? deliveryInfoDTO.getLockOrderState() : 0);

            if(response.getOrder().getOrderBaseDto().getChannelId() == MEITUAN.getChannelId()){
                Long shopId = order.getOrderBaseDto().getShopId();
                PoiDetailInfoResponse poiDetailInfoResponse = channelPoiManageThriftService.queryPoiDetailInfoByPoiId(
                        tenantId, shopId);
                Optional<ChannelPoiInfoDTO> channelPoiOptional = poiDetailInfoResponse
                        .getPoiDetailInfoDTO()
                        .getChannelPoiInfoList()
                        .stream()
                        .filter(e -> Objects.equals(e.getChannelId(), request.getChannelId())).findAny();
                if (!channelPoiOptional.isPresent()) {
                    return CommonResponse.fail(poiDetailInfoResponse.getStatus().getCode(), "未找到对应渠道门店");
                }

                String appPoiCode = channelPoiOptional.get().getChannelInnerPoiId();
                DeliveryPathRequest deliveryPathRequest = new DeliveryPathRequest()
                        .setDeliveryChannelId(getDeliveryChannelId(request.getChannelId()))
                        .setChannelId(request.getChannelId())
                        .setTenantId(tenantId)
                        .setViewOrderId(Long.parseLong(request.getChannelOrderId()))
                        .setStoreId(shopId)
                        .setAppPoiCode(appPoiCode);
                DeliveryPathResponse deliveryPathResponse = channelDeliveryThriftService.queryDeliveryPath(
                        deliveryPathRequest);

                CoordinateVO riderCoordinateVO = new CoordinateVO();
                riderCoordinateVO.setLongitude(processNullOrEmpty(transUnit(deliveryPathResponse.getLongitude())));
                riderCoordinateVO.setLatitude(processNullOrEmpty(transUnit(deliveryPathResponse.getLatitude())));
                riderCoordinateVO.setTimestamp(deliveryPathResponse.getTime());
                deliveryPathDynamicInfoVO.setRiderCoordinate(riderCoordinateVO);
            }

            Map<Long, DeliveryOperateItem> operateItemMap = deliveryServiceWrapper.queryDeliveryOperateItem(tenantId, request.getStoreId(), Collections.singletonList(response.getOrder().getOrderBaseDto().getOrderId()));
            if(MapUtils.isNotEmpty(operateItemMap) && operateItemMap.containsKey(response.getOrder().getOrderBaseDto().getOrderId()) && CollectionUtils.isNotEmpty(operateItemMap.get(response.getOrder().getOrderBaseDto().getOrderId()).getOperateItemList())){
                List<Integer> operateCodeList = ApiDeliveryOperateItemEnum.tmsItemListToOperateItemCodeList(operateItemMap.get(response.getOrder().getOrderBaseDto().getOrderId())
                        .getOperateItemList());
                deliveryPathDynamicInfoVO.setDeliveryOperateCode(operateCodeList);
            }


            return new CommonResponse(0, "success", deliveryPathDynamicInfoVO);
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.getDeliveryPathImmutableInfo ", e);
            throw new CommonRuntimeException(e);
        }
    }

    private List<DeliveryLogVo> queryAndBuildDeliveryLogs(BaseOrderReq req) {
        Integer orderBizType = DynamicOrderBizType.channelId2OrderBizTypeValue(req.getChannelId());
        if (orderBizType == null) {
            log.error("Unknown channelId: {}", req.getChannelId());
            return Collections.emptyList();
        }
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        long tenantId = user.getTenantId();
        List<OrderTrackDetailResponse> orderTracks = orderBizFacade.queryOrderTrack(tenantId,
                req.getChannelOrderId(), orderBizType, Collections.singletonList(TrackSource.DELIVERY.getType()));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(orderTracks)) {
            return Collections.emptyList();
        }

        return orderTracks.stream()
                .map(this::buildDeliveryLogVo)
                .sorted(DeliveryLogVo::compareTo)
                .collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    private DeliveryOperationLogVo buildDeliveryLogVo(OrderTrackDetailResponse track) {
        TrackOpType trackOpType = TrackOpType.codeOf(track.getTrackOpType());
        Map<String, Object> ext = resolveExtData(track);
        String operatorName = Optional.ofNullable(ext.get("operatorName"))
                .map(Object::toString).orElse("");
        int opTypeCode = trackOpType.getOpType();
        Long operateTime = track.getOperateTime();

        switch (trackOpType) {
            case DELIVERY_EXCEPTION_UPLOAD_RIDER:
                // 骑手上报举证
                List<String> details = Optional.ofNullable(ext.get("failureType"))
                        .map(failureTypes -> Stream.of(StringUtils.split((String) failureTypes, ','))
                                .map(JDDeliveryFailTypeEnum::toEnum)
                                .filter(Objects::nonNull)
                                .map(JDDeliveryFailTypeEnum::getDesc)
                                .collect(Collectors.toList()))
                        .orElse(Collections.emptyList());
                String reportExceptionDesc = trackOpType.getMsg();
                if (!details.isEmpty()) {
                    reportExceptionDesc = String.format("骑手上报%d项异常", details.size());
                }
                return new DeliveryOperationLogVo(opTypeCode, operatorName, reportExceptionDesc,
                        operateTime, details, Collections.emptyList());
            case DELIVERY_EXCEPTION_UPLOAD_MERCHANT:
                // 商家上报举证
                List<String> pictureUrls = Optional.ofNullable(ext.get("pictureUrls"))
                        .map(e -> (List<String>) e)
                        .orElse(Collections.emptyList());
                return new DeliveryOperationLogVo(opTypeCode, operatorName, trackOpType.getMsg(),
                        operateTime, Collections.emptyList(), pictureUrls);
            case DELIVERY_RAISE_TIP:
                double tipAmount = Optional.ofNullable(ext.get("tipAmount"))
                        .map(tip -> MoneyUtils.centToYuan((int) tip))
                        .orElse(0.0);
                String raiseTipDesc = String.format("加小费%.2f元", tipAmount);
                return new DeliveryOperationLogVo(opTypeCode, operatorName, raiseTipDesc, operateTime);
            case DISPATCH_FOUR_WHEEL:
            case ADD_DISPATCH_FOUR_WHEEL:
                String fourWheelDesc = trackOpType.getMsg();
                List<String> detailList = Optional.ofNullable(ext.get("vehicleNameListStr"))
                        .map(Object::toString)
                        .map(str -> StringUtils.split(str, ","))
                        .map(Arrays::asList)
                        .filter(CollectionUtils::isNotEmpty)
                        .orElse(Collections.emptyList());
                return new DeliveryOperationLogVo(opTypeCode, operatorName, fourWheelDesc, operateTime, detailList, Collections.emptyList());
            default:
                return new DeliveryOperationLogVo(opTypeCode, operatorName, trackOpType.getMsg(), operateTime);
        }
    }

    private Map<String, Object> resolveExtData(OrderTrackDetailResponse track) {
        if (StringUtils.isNotBlank(track.getExtData())) {
            return JacksonUtils.fromJsonToMap(track.getExtData());
        }
        return Collections.emptyMap();
    }

    private String transUnit(long source) {
        double doubleValue = (double) source;
        return String.valueOf(doubleValue/1000000);
    }

    private int getDeliveryChannelId(Integer channelId) {
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.findByChannelId(channelId);
        switch (channelTypeEnum){
            case MEITUAN:
                return 900;
            default:
                return 0;
        }
    }

    public void setOrderDeliveryInfo(OrderDetailResponse orderDetailResponse) {
        if (orderDetailResponse == null || orderDetailResponse.getOrderDetail() == null) {
            return;
        }
        QueryDeliveryOrderResponse response = deliveryServiceWrapper.queryDeliveryInfoByOrderId(orderDetailResponse.getOrderDetail().getOrderId());
        if (response == null || CollectionUtils.isEmpty(response.getTDeliveryOrders())) {
            return;
        }
        ArrayListMultimap<Integer, TDeliveryOrder> deliveryListMultimap = ArrayListMultimap.create();
        Integer maxCount = 1;
        for (TDeliveryOrder deliveryOrder : response.getTDeliveryOrders()) {
            Integer deliveryCount = deliveryOrder.getDeliveryCount() == null ? 1 : deliveryOrder.getDeliveryCount();
            deliveryListMultimap.put(deliveryCount, deliveryOrder);
            if (maxCount < deliveryCount) {
                maxCount = deliveryCount;
            }
        }
        if (maxCount <= 1) {
            return;
        }
        List<RiderInfoVO> riderInfoList = new ArrayList<>();
        Set<Integer> channelCodes = deliveryListMultimap.values().stream().filter(e -> !deliveryChannelWrapper.checkTenantAndStore(e.getTenantId(), e.getStoreId())).map(tDeliveryDetail -> tDeliveryDetail.deliveryChannel).collect(Collectors.toSet());
        Map<Integer, String> deliveryChannelMap = deliveryChannelWrapper.getDeliveryChannelNameMap(channelCodes);

        for (Integer deliveryCount : deliveryListMultimap.keySet()) {
            List<TDeliveryOrder> deliveryOrderList = deliveryListMultimap.get(deliveryCount);
            if (maxCount.equals(deliveryCount)) {
                Optional<TDeliveryOrder> deliveryOrderOptional = deliveryOrderList.stream().max(Comparator.comparing(new Function<TDeliveryOrder, Long>() {
                    @Override
                    public Long apply(TDeliveryOrder tDeliveryOrder) {
                        if(tDeliveryOrder.getCreateTime() == null){
                            return 0L;
                        }
                        return tDeliveryOrder.getCreateTime();
                    }
                }));
                if (deliveryOrderOptional.isPresent()) {
                    TDeliveryOrder deliveryOrder = deliveryOrderOptional.get();
                    DeliveryStatusEnum statusEnum = DeliveryStatusEnum.valueOf(deliveryOrder.getStatus());
                    orderDetailResponse.getOrderDetail().setDistributeStatusName(statusEnum == null ? "" : statusEnum.getDesc());
                    orderDetailResponse.getOrderDetail().setDeliveryMethodName(deliveryChannelWrapper.getDeliveryChannelName(deliveryOrder.getTenantId(),deliveryOrder.getStoreId(),deliveryOrder.getDeliveryChannel(),deliveryChannelMap));
                    orderDetailResponse.getOrderDetail().setDeliveryUserName(deliveryOrder.getRiderName());
                    orderDetailResponse.getOrderDetail().setDeliveryUserPhone(deliveryOrder.getRiderPhone());
                }
                continue;
            }
            Optional<TDeliveryOrder> deliveryOrderOptional = deliveryOrderList.stream().filter(new Predicate<TDeliveryOrder>() {
                @Override
                public boolean test(TDeliveryOrder deliveryOrder) {
                    return deliveryOrder.getStatus() == DeliveryStatusEnum.DELIVERY_DONE.getCode();
                }
            }).findAny();
            if (!deliveryOrderOptional.isPresent()) {
                continue;
            }
            TDeliveryOrder deliveryOrder = deliveryOrderOptional.get();
            RiderInfoVO riderInfoVO = new RiderInfoVO();
            riderInfoVO.setDeliveryCount(deliveryCount + "");
            riderInfoVO.setRiderName(deliveryOrder.getRiderName());
            riderInfoVO.setRiderPhone(deliveryOrder.getRiderPhone());
            riderInfoVO.setDeliveryChannelName(deliveryChannelWrapper.getDeliveryChannelName(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(),deliveryOrder.getDeliveryChannel(),deliveryChannelMap));
            riderInfoVO.setStatusDes(DeliveryStatusEnum.valueOf(deliveryOrder.getStatus()).getDesc());
            riderInfoList.add(riderInfoVO);
        }
        orderDetailResponse.getOrderDetail().setRiderInfoList(riderInfoList);
    }

    @Deprecated
    public CommonResponse<OrderDetailResponse> orderDetailFromOcms(OrderDetailRequest request) {
        OrderDetailReq orderDetailReq = buildOrderDetailReq(request);
        log.info("OCMSOrderRemoteService.orderList  调用channelOrderTenantThriftService.orderDetail request:{}", orderDetailReq);
        try {
            OrderDetailResp response = channelOrderTenantThriftService.orderDetail(orderDetailReq);
            log.info("OCMSOrderRemoteService.orderList  调用channelOrderTenantThriftService.orderDetail response:{}", response);
            OrderDetailResponse orderDetailResponse = buildOrderDetailResponse(response);
            setWeightRefundOperateItem((orderDetailResponse.getOrderDetail()));
            filterOrderDetailPermission(orderDetailResponse.getOrderDetail(), request.getStoreId());
            return new CommonResponse(response.getStatus().getCode(), response.getStatus().getMsg(), orderDetailResponse);
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.orderList  调用channelOrderTenantThriftService.orderLis error", e);
            throw new CommonRuntimeException(e);
        }
    }


    public CommonResponse<OrderDetailResponse> orderDetailFallback(OrderDetailRequest request) {
        log.info("OCMSOrderRemoteService.orderDetail  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }

    @Degrade(rhinoKey = "OCMSOrderRemoteService.partRefund",
            fallBackMethod = "partRefundFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse partRefund(PartRefundRequest request) {
        if (!OrderGrayConfiguration.hitOcmsMigration(request.getStoreId())) {
            return partRefundThroughOCMS(request);
        }
        return orderBizFacade.tenantPartRefund(request);
    }

    private CommonResponse partRefundThroughOCMS(PartRefundRequest request) {
        TenantPartRefundReq tenantPartRefundReq = buildTenantPartRefundReq(request);
        log.info("OCMSOrderRemoteService.partRefund  调用channelOrderTenantThriftService.tenantPartRefundOrder request:{}", tenantPartRefundReq);
        try {
            TenantPartRefundResp response = channelOrderTenantThriftService.tenantPartRefundOrder(tenantPartRefundReq);
            log.info("OCMSOrderRemoteService.partRefund  调用channelOrderTenantThriftService.tenantPartRefundOrder response:{}", response);
            return new CommonResponse(response.getStatus().getCode(), response.getStatus().getMsg(), null);
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.partRefund  调用channelOrderTenantThriftService.tenantPartRefundOrder error", e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse partRefundFallback(PartRefundRequest request) {
        log.info("OCMSOrderRemoteService.partRefund  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }

    @Degrade(rhinoKey = "OCMSOrderRemoteService.cancelOrder",
            fallBackMethod = "cancelOrderFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse cancelOrder(CancelOrderRequest request) {
        if (!OrderGrayConfiguration.hitOcmsMigration()) {
            return cancelOrderThroughOcms(request);
        }
        return orderBizFacade.tenantCancelOrder(request);
    }

    @Deprecated
    private CommonResponse cancelOrderThroughOcms(CancelOrderRequest request) {
        TenantCancelOrderReq tenantPartRefundReq = buildTenantCancelOrderReq(request);
        log.info("OCMSOrderRemoteService.cancelOrder  调用channelOrderTenantThriftService.tenantCancelOrder request:{}", tenantPartRefundReq);
        try {
            TenantCancelOrderResp response = channelOrderTenantThriftService.tenantCancelOrder(tenantPartRefundReq);
            log.info("OCMSOrderRemoteService.cancelOrder  调用channelOrderTenantThriftService.tenantCancelOrder response:{}", response);
            return new CommonResponse(response.getStatus().getCode(), response.getStatus().getMsg(), null);
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.cancelOrder  调用channelOrderTenantThriftService.tenantCancelOrder error", e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse cancelOrderFallback(PartRefundRequest request) {
        log.info("OCMSOrderRemoteService.cancelOrder  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }

    @Degrade(rhinoKey = "OCMSOrderRemoteService.printReceipt",
            fallBackMethod = "printReceiptFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<PrintReceiptResponse> printReceipt(PrintReceiptRequest request) {
        if (!OrderGrayConfiguration.hitOcmsMigration()) {
            return printReceiptThroughOcms(request);
        }
        OpenPrintResponse openPrintResponse = pickSelectFacade.printReceipt(
                ChannelOrderConverter.convertChannelId2OrderBizType(request.getChannelId()),
                request.getChannelOrderId(),
                request.getStoreId()
        );
        return new CommonResponse(openPrintResponse.getCode(), openPrintResponse.getMsg(), buildPrintReceiptResponse(openPrintResponse));
    }


    @Deprecated
    private CommonResponse<PrintReceiptResponse> printReceiptThroughOcms(PrintReceiptRequest request) {
        PrintReceiptReq tenantPartRefundReq = buildPrintReceiptReq(request);
        log.info("OCMSOrderRemoteService.printReceipt  调用channelOrderTenantThriftService.printReceipt request:{}", tenantPartRefundReq);
        try {
            PrintReceiptResp response = channelOrderTenantThriftService.printReceipt(tenantPartRefundReq);
            log.info("OCMSOrderRemoteService.printReceipt  调用channelOrderTenantThriftService.printReceipt response:{}", response);
            return new CommonResponse(response.getStatus().getCode(), response.getStatus().getMsg(), buildPrintReceiptResponse(response));
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.printReceipt  调用channelOrderTenantThriftService.printReceipt error", e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<PrintReceiptResponse> printReceiptFallback(PrintReceiptRequest request) {
        log.info("OCMSOrderRemoteService.printReceipt  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }

    @Degrade(rhinoKey = "OCMSOrderRemoteService.queryPrintStatus",
            fallBackMethod = "queryPrintStatusFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<QueryPrintStatusResponse> queryPrintStatus(QueryPrintStatusRequest request) {
        if (!OrderGrayConfiguration.hitOcmsMigration()) {
            return queryPrintStatusThroughOcms(request);
        }
        try {
            return CommonResponse.success(pickSelectFacade.queryPrintStatus(request.getPrintId()));
        } catch (TException e) {
            log.error("queryPrintStatus, 调用queryPrintStatus error", e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<QueryPrintStatusResponse> queryPrintStatusFallback(QueryPrintStatusRequest request) {
        log.info("OCMSOrderRemoteService.queryPrintStatus  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }

    @Deprecated
    private CommonResponse<QueryPrintStatusResponse> queryPrintStatusThroughOcms(QueryPrintStatusRequest request) {
        log.info("OCMSOrderRemoteService.queryPrintStatus  调用channelOrderTenantThriftService.queryPrintStatus request:{}", request.getPrintId());
        try {
            PrintStatusResp response = channelOrderTenantThriftService.queryPrintStatus(request.getPrintId());
            log.info("OCMSOrderRemoteService.queryPrintStatus  调用channelOrderTenantThriftService.queryPrintStatus response:{}", response);
            return new CommonResponse(response.getStatus().getCode(), response.getStatus().getMsg(), buildQueryPrintStatusResponse(response));
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.printReceipt  调用channelOrderTenantThriftService.queryPrintStatus error", e);
            throw new CommonRuntimeException(e);
        }
    }

    @Degrade(rhinoKey = "OCMSOrderRemoteService.queryVirtualPhone",
            fallBackMethod = "queryVirtualPhoneFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<QueryVirtualPhoneResponse> queryVirtualPhone(@Valid @RequestBody QueryVirtualPhoneRequest request) {
        if (!OrderGrayConfiguration.hitOcmsMigration()) {
            return queryVirtualPhoneThroughOcms(request);
        }
        try {
            return ocmsChannelFacade.queryVirtualPhoneThroughChannel(request);
        } catch (Exception e) {
            log.error("OCMSOrderRemoteService.printReceipt  调用queryVirtualPhone error", e);
            throw new CommonRuntimeException(e);
        }

    }

    @Deprecated
    private CommonResponse<QueryVirtualPhoneResponse> queryVirtualPhoneThroughOcms(QueryVirtualPhoneRequest request) {
        QueryVirtualPhoneReq queryVirtualPhoneReq = buildQueryVirtualPhoneReq(request);
        log.info("OCMSOrderRemoteService.queryVirtualPhone  调用channelOrderTenantThriftService.queryVirtualPhone request:{}", queryVirtualPhoneReq);
        try {
            QueryVirtualPhoneResp response = channelOrderTenantThriftService.queryVirtualPhone(queryVirtualPhoneReq);
            log.info("OCMSOrderRemoteService.queryVirtualPhone  调用channelOrderTenantThriftService.queryVirtualPhone response:{}", response);
            return new CommonResponse(response.getStatus().getCode(), response.getStatus().getMsg(), buildQueryVirtualPhoneResponse(response));
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.queryVirtualPhone  调用channelOrderTenantThriftService.queryVirtualPhone error", e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<PrintReceiptResponse> queryVirtualPhoneFallback(PrintReceiptRequest request) {
        log.info("OCMSOrderRemoteService.queryVirtualPhone  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }

    @Degrade(rhinoKey = "OCMSOrderRemoteService.refundReason",
            fallBackMethod = "checkRefundFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<CheckRefundResponse> checkRefund(CheckRefundRequest request) {
        if (!OrderGrayConfiguration.hitOcmsMigration()) {
            return checkRefundThroughOcms(request);
        }

        try {
            //校验是否可退
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            OCMSRefundCheckResponse checkResponse = ocmsOrderOperateThriftService.refundCheck(OCMSCheckRefundRequest.builder()
                    .orderBizType(ChannelOrderConverter.convertChannelId2OrderBizType(request.getChannelId()))
                    .refundType(request.getRefundType())
                    .tenantId(user.getTenantId())
                    .viewOrderId(request.getChannelOrderId())
                    .build());
            if (success(checkResponse.getStatus())) {
                CheckRefundResponse response = new CheckRefundResponse();
                response.setRefundReasons(checkResponse.getPossibleRefundReasons().stream()
                        .map(this::convertRefundReason)
                        .collect(Collectors.toList()));
                //组装订单详情
                OcmsOrderDetailReq orderDetailReq = buildOcmsOrderDetailRequest(request);
                log.info("OCMSOrderRemoteService.checkRefund  调用ocmsOrderSearchService.orderDetail request:{}", orderDetailReq);
                OcmsOrderDetailResponse orderDetailResp = ocmsOrderSearchService.orderDetail(orderDetailReq);
                log.info("OCMSOrderRemoteService.checkRefund  调用ocmsOrderSearchService.orderDetail response:{}", orderDetailResp);
                if (Objects.nonNull(orderDetailResp.getOrder())) {
                    OrderDetailResponse orderDetailResponse = buildOcmsOrderDetailResponse(orderDetailResp, Collections.emptyList(),
                            Sets.newHashSet(getCurrentStoreIdFromHeader()));
                    // 过滤线下赠品商品
                    if (Objects.nonNull(orderDetailResponse.getOrderDetail())
                            && CollectionUtils.isNotEmpty(orderDetailResponse.getOrderDetail().getProductList())) {
                        List<ProductVO> filteredProducts = orderDetailResponse.getOrderDetail().getProductList().stream()
                                .filter(product -> !Objects.equals(1, product.getGiftType()))
                                .collect(Collectors.toList());
                        // 抖音同时过滤线上赠品
                        if(Objects.equals(DynamicChannelType.DOU_YIN.getChannelId(), request.getChannelId())){
                            filteredProducts = filteredProducts.stream()
                                    .filter(product -> !Objects.equals(0, product.getGiftType()))
                                    .collect(Collectors.toList());
                        }
                        orderDetailResponse.getOrderDetail().setProductList(filteredProducts);
                    }
                    response.setOrderInfo(orderDetailResponse.getOrderDetail());
                }
                return CommonResponse.success(response);
            }
            return CommonResponse.fail(checkResponse.getStatus().getCode(), checkResponse.getStatus().getMessage());
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.checkRefund  调用channelOrderTenantThriftService.checkRefund error", e);
            throw new CommonRuntimeException(e);
        }
    }

    private static Long getCurrentStoreIdFromHeader() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        try {
            return Long.parseLong(servletRequestAttributes.getRequest().getHeader("storeid"));
        } catch (Exception e) {
            return null;
        }
    }

    @Deprecated
    private CommonResponse<CheckRefundResponse> checkRefundThroughOcms(CheckRefundRequest request) {
        RefundCheckReq refundCheckReq = buildRefundCheckReq(request);
        log.info("OCMSOrderRemoteService.checkRefund  调用channelOrderTenantThriftService.checkRefund request:{}", refundCheckReq);
        try {
            RefundCheckResp response = channelOrderTenantThriftService.checkRefund(refundCheckReq);
            log.info("OCMSOrderRemoteService.checkRefund  调用channelOrderTenantThriftService.checkRefund response:{}", response);
            CommonResponse<CheckRefundResponse> commonResponse = new CommonResponse(response.getStatus().getCode(), response.getStatus().getMsg(), buildCheckRefundResponse(response));
            if (0 == response.getStatus().getCode()) {
                OrderDetailReq orderDetailReq = buildOrderDetailReq(request);
                log.info("OCMSOrderRemoteService.checkRefund  调用channelOrderTenantThriftService.orderDetail request:{}", orderDetailReq);
                OrderDetailResp orderDetailResp = channelOrderTenantThriftService.orderDetail(orderDetailReq);
                log.info("OCMSOrderRemoteService.checkRefund  调用channelOrderTenantThriftService.orderDetail response:{}", orderDetailResp);
                if (Objects.nonNull(commonResponse.getData())) {
                    commonResponse.getData().setOrderInfo(buildOrderDetailVO(orderDetailResp.getOrder()));
                }
            }
            return commonResponse;
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.checkRefund  调用channelOrderTenantThriftService.checkRefund error", e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<CheckRefundResponse> checkRefundFallback(CheckRefundRequest request) {
        log.info("OCMSOrderRemoteService.checkRefund  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }

    @Deprecated
    @Degrade(rhinoKey = "OCMSOrderRemoteService.queryRefundableAmount",
            fallBackMethod = "queryRefundableAmountFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<QueryRefundableAmountResponse> queryRefundableAmount(QueryRefundableAmountRequest request) {
        if (OrderGrayConfiguration.hitOcmsMigration(request.getStoreId())) {
            //ocms也没有实现该接口，前端已经不再使用这个接口，只有履约助手会调用
            QueryRefundableAmountResponse response = new QueryRefundableAmountResponse();
            response.setRefundCent(-1);
            return CommonResponse.success(response);
        }
        RefundableMoneyReq refundReasonAndCodeReq = buildRefundableMoneyReq(request);
        log.info("OCMSOrderRemoteService.queryRefundableAmount  调用channelOrderTenantThriftService.queryRefundableAmount request:{}", refundReasonAndCodeReq);
        try {
            RefundableAmountResp response = channelOrderTenantThriftService.queryRefundableAmount(refundReasonAndCodeReq);
            log.info("OCMSOrderRemoteService.queryRefundableAmount  调用channelOrderTenantThriftService.queryRefundableAmount response:{}", response);
            return new CommonResponse(response.getStatus().getCode(), response.getStatus().getMsg(), buildQueryRefundableAmountResponse(response));
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.refundReason  调用channelOrderTenantThriftService.queryRefundableAmount error", e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<QueryRefundableAmountResponse> queryRefundableAmountFallback(QueryRefundableAmountRequest request) {
        log.info("OCMSOrderRemoteService.queryRefundableAmount  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }

    @Degrade(rhinoKey = "OCMSOrderRemoteService.querySystemInfo",
            fallBackMethod = "querySystemInfoFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<QuerySystemInfoResponse> querySystemInfo() {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        log.info("OCMSOrderRemoteService.querySystemInfo  调用channelThriftService.getTenantChannels request:{}", user.getTenantId());
        try {
            long tenantId = user.getTenantId();
            //牵牛花ka租户直接走mcc渠道配置，不需要绑定
            QnhKaTenantChannelVO kaTenantChannelVO = MccDynamicConfigUtil.getQnhKaChannels(tenantId);
            if (kaTenantChannelVO != null) {
                return CommonResponse.success((new QuerySystemInfoResponse(buildChannelInfoVO(kaTenantChannelVO.getChannels()))));
            }
            ChannelThriftResponse response = channelThriftService.getTenantChannels(tenantId);
            log.info("OCMSOrderRemoteService.querySystemInfo  调用channelThriftService.getTenantChannels response:{}", response);
            return new CommonResponse(response.getCode(), response.getMsg(), buildQuerySystemInfoResponse(response));
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.querySystemInfo  调用channelThriftService.getTenantChannels error", e);
            throw new CommonRuntimeException(e);
        }
    }

    private List<ChannelInfoVO> buildChannelInfoVO(List<ChannelVO> channelVOS) {
        if (CollectionUtils.isEmpty(channelVOS)) {
            return new ArrayList<>();
        }
        return channelVOS.stream().map(this::convertToChannelInfoVO).collect(Collectors.toList());
    }

    private ChannelInfoVO convertToChannelInfoVO(ChannelVO channelVO) {
        ChannelInfoVO infoVO = new ChannelInfoVO();
        infoVO.setId(channelVO.getChannelId());
        infoVO.setName(channelVO.getChannelName());
        infoVO.setAbbreviation(channelVO.getChannelName());
        return infoVO;
    }

    public CommonResponse<QuerySystemInfoResponse> querySystemInfoFallback() {
        log.info("OCMSOrderRemoteService.querySystemInfo  调用降级方法");
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }

    @Degrade(rhinoKey = "OCMSOrderRemoteService.tenantWeightRefund",
            fallBackMethod = "tenantWeightRefundFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse tenantWeightRefund(RefundByWeightRequest request) {
        CommonResponse commonResponse = CommonResponse.success(null);
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        TenantWeightRefundRequest tenantWeightRefundRequest = new TenantWeightRefundRequest();
        tenantWeightRefundRequest.setTenantId(user.getTenantId());
        tenantWeightRefundRequest.setChannelId(request.getChannelId());
        tenantWeightRefundRequest.setOperatorUserId(user.getAccountId());
        tenantWeightRefundRequest.setOperatorUserName("");
        tenantWeightRefundRequest.setViewOrderId(request.getChannelOrderId());
        tenantWeightRefundRequest.setBizOrderWeightRefundItemModels(buildBizOrderWeightRefundItemModelList(request.getWeightRefundProductVOList()));
        try {
            log.info("OCMSOrderRemoteService.tenantWeightRefund  ocmsOrderThriftService.tenantWeightRefund request:{}", tenantWeightRefundRequest);
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse response = ocmsOrderThriftService.tenantWeightRefund(tenantWeightRefundRequest);
            if (response.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
                commonResponse = CommonResponse.fail(StatusCodeEnum.FAIL.getCode(), response.getStatus().getMessage());
            }
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.tenantWeightRefund  ocmsOrderThriftService.tenantWeightRefund TException", e);
            throw new CommonRuntimeException(e);
        }
        return commonResponse;
    }

    public CommonResponse tenantWeightRefundFallback() {
        log.info("OCMSOrderRemoteService.tenantWeightRefund  调用降级方法");
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }

    public CommonResponse<WeightRefundCheckResponse> weightRefundCheck(WeightRefundCheckRequest request) {
        OrderDetailSearchRequest orderDetailSearchRequest = new OrderDetailSearchRequest();
        orderDetailSearchRequest.setViewOrderId(request.getChannelOrderId());
        orderDetailSearchRequest.setTenantId(request.getTenantId());
        orderDetailSearchRequest.setOrderBizType(ChannelOrderConverter.convertChannelId2OrderBizType(request.getChannelId()));
        log.info("OCMSOrderRemoteService.queryLatestOrder  orderSearchService.getOrderDetail request:{}", orderDetailSearchRequest);
        com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.OrderDetailResponse response = null;
        WeightRefundCheckResponse weightRefundCheckResponse = null;
        try {
            response = orderSearchService.getOrderDetail(orderDetailSearchRequest);
            log.info("OCMSOrderRemoteService.queryLatestOrder  orderSearchService.getOrderDetail response:{}", response);
            if (Objects.isNull(response)) {
                return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
            }
            if (response.getResponseStatus() != StatusCodeEnum.SUCCESS.getCode()) {
                return CommonResponse.fail(response.getResponseStatus(), response.getMsg());
            }

            // 过滤线下赠品商品
            if (Objects.nonNull(response.getOrderDetail())
                    && CollectionUtils.isNotEmpty(response.getOrderDetail().getOrderItemList())) {
                List<OrderItem> filteredProducts = response.getOrderDetail().getOrderItemList().stream()
                        .filter(product -> !Objects.equals(1, OrderUtils.getExtDataAsInt("giftType", product.getExtData())))
                        .collect(Collectors.toList());
                response.getOrderDetail().setOrderItemList(filteredProducts);
            }

            weightRefundCheckResponse = buildWeightRefundCheckResponse(response);
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.weightRefundCheck  orderSearchService.getOrderDetail TException", e);
            throw new CommonRuntimeException(e);
        }

        return CommonResponse.success(weightRefundCheckResponse);

    }

    private List<ProductVO> buildProductInfoVOList(List<ProductInfoDTO> productInfoDTOList) {
        List<ProductVO> productVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(productInfoDTOList)) {
            productInfoDTOList.stream().forEach(product -> {
                ProductVO productVO = new ProductVO();
                productVO.setSkuId(product.getSkuId());
                productVO.setUpcCode(product.getUpcCode());
                productVO.setSkuName(product.getSkuName());
                productVO.setPicUrl(product.getPicUrl());
                productVO.setSpecification(product.getSpecification());
                productVO.setCount(product.getCount());
                productVO.setIsRefund(product.isIsRefund() ? 1 : 0);
                productVO.setRefundCount(product.getRefundCount());
                productVOList.add(productVO);
            });
        }
        return productVOList;
    }

    private QuerySystemInfoResponse buildQuerySystemInfoResponse(ChannelThriftResponse channelThriftResponse) {
        QuerySystemInfoResponse response = new QuerySystemInfoResponse();
        response.setChannelInfos(OCMSUtils.buildChannelAbbreviationInfoVOList(channelThriftResponse.getChannelList()));
        return response;
    }


    private QueryRefundableAmountResponse buildQueryRefundableAmountResponse(RefundableAmountResp refundableAmountResp) {
        QueryRefundableAmountResponse response = new QueryRefundableAmountResponse();
        response.setRefundCent(refundableAmountResp.getRefundCent());
        return response;
    }

    private RefundableMoneyReq buildRefundableMoneyReq(QueryRefundableAmountRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        RefundableMoneyReq refundableMoneyReq = new RefundableMoneyReq();
        refundableMoneyReq.setTenantId(user.getTenantId());
        refundableMoneyReq.setChannelId(request.getChannelId());
        refundableMoneyReq.setChannelOrderId(request.getChannelOrderId());
        refundableMoneyReq.setRefundType(request.getRefundType());
        refundableMoneyReq.setClientInfo(buildClientInfo());
        refundableMoneyReq.setPartRefundProductInfo(buildTenantPartRefundProductInfoList(request.getPartRefundProductList()));
        refundableMoneyReq.setShopId(request.getStoreId());
        return refundableMoneyReq;
    }

    private CheckRefundResponse buildCheckRefundResponse(RefundCheckResp refundCheckResp) {
        CheckRefundResponse response = new CheckRefundResponse();
        response.setRefundReasons(buildRefundReasonAndCodeList(refundCheckResp.getPossibleRefundReasons()));
        return response;
    }

    private List<RefundReasonAndCodeVO> buildRefundReasonAndCodeList(List<RefundReasonAndCode> refundReasonAndCodeList) {
        if (CollectionUtils.isEmpty(refundReasonAndCodeList)) {
            return Lists.newArrayList();
        }
        List<RefundReasonAndCodeVO> refundReasonAndCodeVOList = Lists.newArrayList();
        for (RefundReasonAndCode refundReasonAndCode : refundReasonAndCodeList) {
            RefundReasonAndCodeVO refundReasonAndCodeVO = new RefundReasonAndCodeVO();
            refundReasonAndCodeVO.setCode(refundReasonAndCode.getCode());
            refundReasonAndCodeVO.setReason(refundReasonAndCode.getReason());
            refundReasonAndCodeVOList.add(refundReasonAndCodeVO);
        }
        return refundReasonAndCodeVOList;
    }

    private RefundCheckReq buildRefundCheckReq(CheckRefundRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        RefundCheckReq refundCheckReq = new RefundCheckReq();
        refundCheckReq.setTenantId(user.getTenantId());
        refundCheckReq.setChannelId(request.getChannelId());
        refundCheckReq.setChannelOrderId(request.getChannelOrderId());
        refundCheckReq.setRefundType(request.getRefundType());
        refundCheckReq.setClientInfo(buildClientInfo());
        return refundCheckReq;
    }

    private QueryVirtualPhoneResponse buildQueryVirtualPhoneResponse(QueryVirtualPhoneResp queryVirtualPhoneResp) {
        QueryVirtualPhoneResponse response = new QueryVirtualPhoneResponse();
        response.setPhoneNo(queryVirtualPhoneResp.getPhoneNo());
        return response;
    }

    private QueryVirtualPhoneReq buildQueryVirtualPhoneReq(QueryVirtualPhoneRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        QueryVirtualPhoneReq queryVirtualPhoneReq = new QueryVirtualPhoneReq();
        if (!isValidStore(request.getStoreId()) || Objects.equals(request.getEntityType(), PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code())) {
            OrderDetail orderDetail = getOrderDetail(request.getChannelOrderId(), request.getChannelId());
            queryVirtualPhoneReq.setStoreId(orderDetail.getOrderBase().getShopId());
        } else {
            queryVirtualPhoneReq.setStoreId(request.getStoreId());
        }

        queryVirtualPhoneReq.setChannelOrderId(request.getChannelOrderId());
        queryVirtualPhoneReq.setChannelId(request.getChannelId());
        queryVirtualPhoneReq.setTenantId((int) user.getTenantId());
        queryVirtualPhoneReq.setPhoneType(request.getPhoneType());

        return queryVirtualPhoneReq;
    }

    private boolean isValidStore(Long shopId) {
        return shopId != null && shopId > 0;
    }

    private OrderDetail getOrderDetail(String channelOrderId, Integer channelId) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OrderDetailSearchRequest orderDetailSearchRequest = new OrderDetailSearchRequest();
        orderDetailSearchRequest.setViewOrderId(channelOrderId);
        orderDetailSearchRequest.setTenantId(user.getTenantId());
        orderDetailSearchRequest.setOrderBizType(ChannelOrderConverter.convertChannelId2OrderBizType(channelId));
        com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.OrderDetailResponse response = null;
        try {
            response = orderSearchService.getOrderDetail(orderDetailSearchRequest);
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.buildQueryVirtualPhoneReq  orderSearchService.getOrderDetail TException", e);
            throw new CommonRuntimeException(e);
        }
        return response.getOrderDetail();
    }

    public OrderDetail getOrderDetail(String channelOrderId, Integer channelId, Long tenantId) {
        OrderDetailSearchRequest orderDetailSearchRequest = new OrderDetailSearchRequest();
        orderDetailSearchRequest.setViewOrderId(channelOrderId);
        orderDetailSearchRequest.setTenantId(tenantId);
        orderDetailSearchRequest.setOrderBizType(ChannelOrderConverter.convertChannelId2OrderBizType(channelId));
        com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.OrderDetailResponse response = null;
        try {
            response = orderSearchService.getOrderDetail(orderDetailSearchRequest);
        } catch (TException e) {
            log.error("OCMSOrderRemoteService.buildQueryVirtualPhoneReq  orderSearchService.getOrderDetail TException", e);
            throw new CommonRuntimeException(e);
        }
        return response.getOrderDetail();
    }

    private QueryPrintStatusResponse buildQueryPrintStatusResponse(PrintStatusResp printStatusResp) {
        QueryPrintStatusResponse response = new QueryPrintStatusResponse();
        response.setPrintStatus(printStatusResp.getPrintStatus());
        response.setMsg(printStatusResp.getMsg());
        return response;
    }


    private PrintReceiptResponse buildPrintReceiptResponse(PrintReceiptResp receiptResp) {
        PrintReceiptResponse receiptResponse = new PrintReceiptResponse();
        receiptResponse.setPrintId(receiptResp.getPrintId());
        return receiptResponse;
    }

    private PrintReceiptResponse buildPrintReceiptResponse(OpenPrintResponse openPrintResponse) {
        PrintReceiptResponse receiptResponse = new PrintReceiptResponse();
        receiptResponse.setPrintId(openPrintResponse.getRequestId());
        return receiptResponse;
    }

    private PrintReceiptReq buildPrintReceiptReq(PrintReceiptRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        PrintReceiptReq printReceiptReq = new PrintReceiptReq();
        printReceiptReq.setTenantId(user.getTenantId());
        printReceiptReq.setChannelId(request.getChannelId());
        printReceiptReq.setChannelOrderId(request.getChannelOrderId());
        printReceiptReq.setOptUserName(user.getUsername());
        printReceiptReq.setOptUserId(user.getAccountId());
        printReceiptReq.setClientInfo(buildClientInfo());
        return printReceiptReq;
    }

    private TenantCancelOrderReq buildTenantCancelOrderReq(CancelOrderRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OrderDetail orderDetail = getOrderDetail(request.getChannelOrderId(), request.getChannelId());

        TenantCancelOrderReq tenantCancelOrderReq = new TenantCancelOrderReq();
        tenantCancelOrderReq.setStoreId(orderDetail.getOrderBase().getShopId());
        tenantCancelOrderReq.setTenantId(user.getTenantId());
        tenantCancelOrderReq.setChannelId(request.getChannelId());
        tenantCancelOrderReq.setChannelOrderId(request.getChannelOrderId());
        tenantCancelOrderReq.setReason(request.getReason());
        tenantCancelOrderReq.setReasonCode(request.getReasonCode());
        tenantCancelOrderReq.setOptUserName(user.getUsername());
        tenantCancelOrderReq.setOptUserId(user.getAccountId());
        tenantCancelOrderReq.setClientInfo(buildClientInfo());
        return tenantCancelOrderReq;
    }

    private TenantPartRefundReq buildTenantPartRefundReq(PartRefundRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        TenantPartRefundReq tenantPartRefundReq = new TenantPartRefundReq();
        tenantPartRefundReq.setTenantId(user.getTenantId());
        tenantPartRefundReq.setChannelId(request.getChannelId());
        tenantPartRefundReq.setChannelOrderId(request.getChannelOrderId());
        tenantPartRefundReq.setReason(request.getReason());
        tenantPartRefundReq.setOptUserName(user.getUsername());
        tenantPartRefundReq.setOptUserId(user.getAccountId());
        tenantPartRefundReq.setShopId(request.getStoreId());
        tenantPartRefundReq.setClientInfo(buildClientInfo());
        tenantPartRefundReq.setPartRefundProductInfo(buildTenantPartRefundProductInfoList(request.getPartRefundProductList()));
        return tenantPartRefundReq;
    }

    private List<TenantPartRefundProductInfo> buildTenantPartRefundProductInfoList(List<PartRefundProductVO> partRefundProductVOList) {
        if (CollectionUtils.isEmpty(partRefundProductVOList)) {
            return Lists.newArrayList();
        }
        List<TenantPartRefundProductInfo> tenantPartRefundProductInfoList = Lists.newArrayList();
        for (PartRefundProductVO partRefundProductVO : partRefundProductVOList) {
            TenantPartRefundProductInfo tenantPartRefundProductInfo = new TenantPartRefundProductInfo();
            tenantPartRefundProductInfo.setSkuId(partRefundProductVO.getSkuId());
            tenantPartRefundProductInfo.setCustomSkuId(partRefundProductVO.getCustomSkuId());
            tenantPartRefundProductInfo.setSkuName(partRefundProductVO.getSkuName());
            tenantPartRefundProductInfo.setCount(partRefundProductVO.getCount());
            tenantPartRefundProductInfoList.add(tenantPartRefundProductInfo);
        }
        return tenantPartRefundProductInfoList;
    }

    private OrderDetailResponse buildOrderDetailResponse(OrderDetailResp orderDetailResp) {
        OrderDetailResponse response = new OrderDetailResponse();
        response.setOrderDetail(buildOrderDetailVO(orderDetailResp.getOrder()));
        return response;
    }

    private OrderDetailResponse buildOcmsOrderDetailResponse(OcmsOrderDetailResponse orderDetailResp,
            List<OrderCouldOperateItemEnum> operateItemEnums, Set<Long> authedStoreIds) throws TException {
        OrderDetailResponse response = new OrderDetailResponse();
        if (orderDetailResp.getOrder() != null) {
            OrderBaseVo orderBaseDto = orderDetailResp.getOrder().getOrderBaseDto();
            OCMSOrderKey orderKey = OCMSOrderKey.builder().channelOrderId(orderBaseDto.getChannelOrderId())
                    .channelType(orderBaseDto.getChannelId()).build();
            List<Integer> operateItems = Lists.newArrayList(OrderCouldOperateItemEnum.ACCEPT_ORDER.getValue(),
                    OrderCouldOperateItemEnum.COMPLETE_PICK.getValue(), OrderCouldOperateItemEnum.PRINT_RECEIPT.getValue(),
                    OrderCouldOperateItemEnum.FULL_ORDER_REFUND.getValue(),
                    OrderCouldOperateItemEnum.PART_ORDER_REFUND.getValue(),
                    OrderCouldOperateItemEnum.CREATE_INVOICE.getValue(),
                    OrderCouldOperateItemEnum.SETTING_ORDER_TAG.getValue()
            );
            if (CollectionUtils.isNotEmpty(operateItemEnums)) {
                operateItemEnums.forEach(itemEnum -> operateItems.add(itemEnum.getValue()));
            }
            if (BusinessIdTracer.isDrunkHorseTenant(orderBaseDto.getTenantId())
                    && Lion.getConfigRepository().getBooleanValue("drunkhorse.only.refund.gift.switch", true)){
                operateItems.add(OrderCouldOperateItemEnum.ONLY_REFUND_GIFT_BAG.getValue());
            }

            Map<OCMSOrderKey, List<Integer>> ocmsOrderKeyListMap = orderBizFacade.queryOrderOperateItems(orderBaseDto.getTenantId(),
                    ImmutableList.of(orderKey), operateItems);
            Map<Long, String> shopId2ErpShopCodeMap = poiRemoteService.queryShopId2ErpShopCode(orderBaseDto.getTenantId(), orderBaseDto.getShopId());

            List<OrderLabelModel> showLabelList = fuseOrderService.queryOrderShowLabel(orderBaseDto.getTenantId());

            // 订单详情解析
            OrderDetailVO orderDetailVO = ChannelOrderConverter.buildOrderDetailVO(orderDetailResp.getOrder(), ocmsOrderKeyListMap.get(orderKey), authedStoreIds, showLabelList);

            // 开票按钮权限
            List<Long> createInvoiceShopList = invoiceService.getCreateInvoiceShopList(orderBaseDto.getTenantId());
            Map<String, Map<String, String>> invoiceUrlShopConfig = invoiceService.getInvoiceUrlShopConfig();
            String invoiceUrlForTenant = invoiceService.getInvoiceUrlForTenant(orderBaseDto.getTenantId());
            if(invoiceService.checkOrderCreateInvoice(createInvoiceShopList, orderBaseDto.getShopId(), orderBaseDto.getOrderStatus(), orderBaseDto.getTenantId(),invoiceUrlForTenant, invoiceUrlShopConfig)){
                orderDetailVO.getCouldOperateItemList().add(OrderCanOperateItem.CREATE_INVOICE.getValue());
            }

            OpenTraceResponse openTraceResponse = getOpenTraceResponse(orderDetailResp.getOrder());
            orderDetailVO.setOrderStatuslogList(buildOrderStatusLogs(orderDetailResp.getOrder(), openTraceResponse));
            orderDetailVO.setErpShopCode(StringUtils.isBlank(shopId2ErpShopCodeMap.get(orderDetailVO.getStoreId())) ?
                                                 Strings.EMPTY :
                                                 shopId2ErpShopCodeMap.get(orderDetailVO.getStoreId()));
            if (com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil.getOrderShowPickerNamesSwitch()){
                orderDetailVO.setPickerNameList(getPickNames(openTraceResponse));
            }
            orderDetailVO.setGiftBagList(DrunkHorseGiftBagVO.buildDrunkHorseGiftBag(orderDetailResp.getOrder().getDrunkHorseGiftBagVoList()));
            //添加是否展开拣货记录按钮
            setPickRecordButton(orderDetailVO, orderDetailResp.getOrder());
            // 歪马微商城渠道名称区分
            if (DynamicChannelType.MT_DRUNK_HORSE.getChannelId() == orderBaseDto.getChannelId()) {
                orderDetailVO.setChannelName(ChannelOrderConvertUtils
                        .getDrunkHorseChannelNameByChannelId(orderBaseDto.getChannelId(), orderBaseDto.getClientType()));
            }
            response.setOrderDetail(orderDetailVO);
        }
        return response;
    }

    private void setPickRecordButton(OrderDetailVO orderDetailVO, OrderDetailVo order) {
        try {
            if(Objects.isNull(orderDetailVO)){
                return;
            }
            if(Objects.isNull(order) || Objects.isNull(order.getDeliveryInfoDTO()) || Objects.isNull(order.getOrderBaseDto())){
                return;
            }
            Long tenantId = order.getOrderBaseDto().getTenantId();
            if(Objects.isNull(tenantId)){
                return;
            }
            orderDetailVO.setPickRecordButton(CommonUsedUtil.setPickGoodsRecordButton(orderDetailVO.getChannelId(), order.getDeliveryInfoDTO().getDeliveryStatus(), tenantId, isErpOrDrunkHorseTenant(tenantId)));
        } catch (Exception e) {
            log.info("OCMSOrderRemoteService.setPickRecordButton app is error e= ", e);
        }
    }

    /**
     * 是否erp租户
     *
     * @return
     */
    public Boolean isErpOrDrunkHorseTenant(Long tenantId) {
        try {
            if(Objects.isNull(tenantId)){
                log.info("OCMSOrderRemoteService.isErpOrDrunkHorseTenant tenantId is null");
                return false;
            }
            // erp租户
            if(tenantService.queryTenantHasErp(tenantId)){
                return true;
            }
            //判断是否歪马 医药无需隔离
            return MccConfigUtil.isDrunkHorseTenant(tenantId);
        } catch (Exception e) {
            log.error("OCMSOrderRemoteService.isErpOrDrunkHorseTenant is error e= ", e);
        }
        return false;
    }

    private OrderDetailVO buildOrderDetailVO(OrderDetailDTO orderDetailDTO) {
        if (Objects.isNull(orderDetailDTO) || Objects.isNull(orderDetailDTO.getOrderBaseDto())) {
            return null;
        }
        OrderBaseDTO orderBaseDTO = orderDetailDTO.getOrderBaseDto();
        OrderDetailVO orderDetailVO = new OrderDetailVO();

        orderDetailVO.setChannelOrderId(orderBaseDTO.getChannelOrderId());
        orderDetailVO.setStoreId(orderBaseDTO.getShopId());
        orderDetailVO.setStoreName(orderBaseDTO.getChannelName());
        orderDetailVO.setChannelId(orderBaseDTO.getChannelId());
        orderDetailVO.setChannelName(orderBaseDTO.getChannelName());
        orderDetailVO.setItemCount(orderBaseDTO.getItemCount());
        orderDetailVO.setReceiverName(orderBaseDTO.getReceiverName());
        orderDetailVO.setReceiverPhone(orderBaseDTO.getReceiverPhone());
        orderDetailVO.setReceiveAddress(orderBaseDTO.getReceiveAddress());
        // 自提
        boolean selfMention = DistributeMethodEnum.STORE_DELIVERY.getDesc().equals(orderBaseDTO.getDeliveryMethodName());
        if (selfMention && orderBaseDTO.getChannelId() == ChannelType.YOU_ZAN.getValue()) {
            // 有赞渠道自提隐藏用户地址
            orderDetailVO.setReceiveAddress("到店自取@#到店自取");
        }
        orderDetailVO.setOriginalAmt(orderBaseDTO.getOriginalAmt());
        orderDetailVO.setActualPayAmt(orderBaseDTO.getActualPayAmt());
        orderDetailVO.setProductTotalPayAmount(calculateProductTotalPayAmount(orderDetailDTO.getProducInfotList()));
        orderDetailVO.setBizReceiveAmt(orderBaseDTO.getBizReceiveAmt());
        orderDetailVO.setFreight(orderBaseDTO.getFreight());
        orderDetailVO.setPackageAmt(orderBaseDTO.getPackageAmt());
        orderDetailVO.setPlatformFee(orderBaseDTO.getPlatformFee());
        orderDetailVO.setIsNeedInvoice(orderBaseDTO.isIsNeedInvoice() ? 1 : 0);
        orderDetailVO.setInvoiceTitle(orderBaseDTO.getInvoiceTitle());
        orderDetailVO.setTaxNo(orderBaseDTO.getTaxNo());
        orderDetailVO.setDeliveryMethod(orderBaseDTO.getDeliveryMethod());
        orderDetailVO.setDeliveryMethodName(orderBaseDTO.getDeliveryMethodName());
        orderDetailVO.setDeliveryUserName(orderBaseDTO.getDeliveryUserName());
        orderDetailVO.setDeliveryUserPhone(orderBaseDTO.getDeliveryUserPhone());
        orderDetailVO.setIsDeliveryOvertime(orderBaseDTO.getIsDeliveryOvertime());
        orderDetailVO.setDeliveryExceptionDescription(orderBaseDTO.getDeliveryExceptionDescription());
        orderDetailVO.setDistributeType(orderBaseDTO.getDistributeType());
        //获取配送类型原始描述
        orderDetailVO.setDistributeTypeName(orderBaseDTO.getDeliveryMethodName());
        orderDetailVO.setPayMethod(orderBaseDTO.getPayMethod());
        orderDetailVO.setPayMethodDesc(orderBaseDTO.getPayMethodDesc());
        orderDetailVO.setChannelOrderStatus(orderBaseDTO.getChannelOrderStatus());
        orderDetailVO.setChannelOrderStatusDesc(orderBaseDTO.getChannelOrderStatusDesc());
        orderDetailVO.setAggregationOrderStatus(orderBaseDTO.getAggregationOrderStatus());
        orderDetailVO.setCreateTime(orderBaseDTO.getCreateTime());
        orderDetailVO.setLastAfterSaleApplyRefundTagId(orderBaseDTO.getLastAfterSaleApplyRefundTagId());
        orderDetailVO.setLastAfterSaleApplyReason(orderBaseDTO.getLastAfterSaleApplyReason());
        orderDetailVO.setLastAfterSaleApplyStatus(orderBaseDTO.getLastAfterSaleApplyStatus());
        orderDetailVO.setLastAfterSaleApplyRejectReason(orderBaseDTO.getLastAfterSaleApplyRejectReason());
        orderDetailVO.setRefundableOrderAmount(orderBaseDTO.getRefundableOrderAmount());
        orderDetailVO.setEstimatedSendArriveTimeStart(orderBaseDTO.getEstimatedSendArriveTimeStart());
        orderDetailVO.setEstimatedSendArriveTimeEnd(orderBaseDTO.getEstimatedSendArriveTimeEnd());
        orderDetailVO.setUpdateTime(orderBaseDTO.getUpdateTime());
        orderDetailVO.setDistributeStatus(orderBaseDTO.getDistributeStatus());
        DistributeStatusEnum status = DistributeStatusEnum.enumOf(orderBaseDTO.getDistributeStatus());
        orderDetailVO.setDistributeStatusName(status != null ? status.getDesc() : "");
        orderDetailVO.setPickupStatus(orderBaseDTO.getDeliveryStatus());
        orderDetailVO.setChannelExtraOrderId(orderBaseDTO.getChannelExtraOrderId());
        orderDetailVO.setComments(orderBaseDTO.getComments());
        orderDetailVO.setSerialNo(orderBaseDTO.getOrderSerialNumber());
        orderDetailVO.setDeliveryOrderType(orderBaseDTO.getDeliveryOrderType());
        orderDetailVO.setDeliveryOrderTypeName(orderBaseDTO.getDeliveryOrderTypeName());
        orderDetailVO.setCouldOperateItemList(orderBaseDTO.getCouldOperateItemList());
        orderDetailVO.setProductList(buildProductVOList(orderDetailDTO.getProducInfotList()));
        orderDetailVO.setPromotionList(buildPromotionVOList(orderDetailDTO.getPromotionInfoList()));
        List<OrderStatusLogVO> orderStatusLogVOS = buildOrderStatusLogVOList(orderDetailDTO.getOrderStatusLogs());
        orderDetailVO.setAfterSaleRecordList(buildAfterSaleRecordVOList(orderDetailDTO.getAfterSaleRecords()));
        for (AfterSaleRecord afterSaleRecord : orderDetailDTO.getAfterSaleRecords()) {
            if (afterSaleRecord.getAfsPattern() == AfterSalePatternEnum.WEIGHT.getValue()) {
                OrderStatusLogVO orderStatusLogVO = new OrderStatusLogVO();
                orderStatusLogVO.setTime(afterSaleRecord.getUpdateTime());
                orderStatusLogVO.setStatusDesc("商家按重量退差价");
                orderStatusLogVOS.add(orderStatusLogVO);
            }
        }
        orderStatusLogVOS = orderStatusLogVOS.stream().sorted(Comparator.comparingLong(OrderStatusLogVO::getTime).reversed()).collect(Collectors.toList());
        orderDetailVO.setOrderStatuslogList(orderStatusLogVOS);
        if (orderBaseDTO.getUserId() > BigInteger.ZERO.longValue()) {
            orderDetailVO.setUserId(orderBaseDTO.getUserId());
        }
        if (CollectionUtils.isNotEmpty(orderBaseDTO.getTags())) {
            orderDetailVO.setUserTags(UserTagTypeEnum.getTags(orderBaseDTO.getTags()));
        }
        return orderDetailVO;
    }

    private int calculateProductTotalPayAmount(List<ProductInfoDTO> productInfoDTOList) {
        if (CollectionUtils.isEmpty(productInfoDTOList)) {
            return 0;
        }

        return productInfoDTOList.stream().mapToInt(ProductInfoDTO::getTotalCurrentPrice).sum();
    }

    private List<OrderStatusLogVO> buildOrderStatusLogVOList(List<SimpleOrderStatusLogViewDTO> simpleOrderStatusLogViewDTOList) {
        List<OrderStatusLogVO> orderStatusLogVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(simpleOrderStatusLogViewDTOList)) {
            simpleOrderStatusLogViewDTOList.stream().forEach(log -> {
                OrderStatusLogVO orderStatusLogVO = new OrderStatusLogVO();
                orderStatusLogVO.setStatusDesc(log.getDesc());
                orderStatusLogVO.setTime(log.getTime());
                orderStatusLogVOList.add(orderStatusLogVO);
            });
        }
        return orderStatusLogVOList;
    }

    private List<AfterSaleRecordVO> buildAfterSaleRecordVOList(List<AfterSaleRecord> afterSaleRecordList) {
        if (CollectionUtils.isEmpty(afterSaleRecordList)) {
            return Lists.newArrayList();
        }
        List<AfterSaleRecordVO> afterSaleRecordVOList = Lists.newArrayList();
        for (AfterSaleRecord afterSaleRecord : afterSaleRecordList) {
            AfterSaleRecordVO afterSaleRecordVO = new AfterSaleRecordVO();
            afterSaleRecordVO.setStoreId(afterSaleRecord.getShopId());
            afterSaleRecordVO.setTenantId(afterSaleRecord.getTenantId());
            afterSaleRecordVO.setChannelId(afterSaleRecord.getChannelId());
            afterSaleRecordVO.setServiceId(afterSaleRecord.getServiceId());
            afterSaleRecordVO.setChannelAfterSaleId(afterSaleRecord.getAfterSaleId());
            afterSaleRecordVO.setIsAudit(afterSaleRecord.getIsAudit());
            afterSaleRecordVO.setStatus(afterSaleRecord.getStatus());
            afterSaleRecordVO.setApplyReason(afterSaleRecord.getApplyReason());
            afterSaleRecordVO.setAfsPattern(afterSaleRecord.getAfsPattern());
            afterSaleRecordVO.setRefundAmt(afterSaleRecord.getRefundAmt());
            afterSaleRecordVO.setCreateTime(afterSaleRecord.getCreateTime());
            afterSaleRecordVO.setUpdateTime(afterSaleRecord.getUpdateTime());
            afterSaleRecordVO.setAfsApplyType(afterSaleRecord.getAfsApplyType());
            afterSaleRecordVO.setWhoApplyType(afterSaleRecord.getWhoApplyType());
            afterSaleRecordVO.setAfterSaleRecordDetailList(buildAfterSaleRecordDetailVOList(afterSaleRecord.getAfterSaleRecordDetails()));
            afterSaleRecordVOList.add(afterSaleRecordVO);
        }
        return afterSaleRecordVOList;
    }

    private List<AfterSaleRecordDetailVO> buildAfterSaleRecordDetailVOList(List<AfterSaleRecordDetail> afterSaleRecordDetailList) {
        List<AfterSaleRecordDetailVO> afterSaleRecordDetailVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(afterSaleRecordDetailList)) {
            afterSaleRecordDetailList.stream().forEach(detail -> {
                        AfterSaleRecordDetailVO afterSaleRecordDetailVO = new AfterSaleRecordDetailVO();
                        afterSaleRecordDetailVO.setChannelId(detail.getChannelId());
                        afterSaleRecordDetailVO.setServiceId(detail.getServiceId());
                        afterSaleRecordDetailVO.setSkuId(detail.getSkuId());
                        afterSaleRecordDetailVO.setUpcCode(detail.getUpcCode());
                        afterSaleRecordDetailVO.setSkuName(detail.getSkuName());
                        afterSaleRecordDetailVO.setSpecification(detail.getSpecification());
                        afterSaleRecordDetailVO.setSellUnit(detail.getSellUnit());
                        afterSaleRecordDetailVO.setUnitPrice(detail.getUnitPrice());
                        afterSaleRecordDetailVO.setCount(detail.getCount());
                        afterSaleRecordDetailVO.setRefundAmt(detail.getRefundAmount());
                        afterSaleRecordDetailVO.setRefundWeight(detail.getRefundWeight());
                        afterSaleRecordDetailVOList.add(afterSaleRecordDetailVO);
                    }
            );
        }
        return afterSaleRecordDetailVOList;
    }

    private List<PromotionVO> buildPromotionVOList(List<PromotionInfoDTO> promotionInfoDTOList) {
        if (CollectionUtils.isEmpty(promotionInfoDTOList)) {
            return Lists.newArrayList();
        }

        List<PromotionVO> promotionVOList = Lists.newArrayList();
        for (PromotionInfoDTO promotionInfoDTO : promotionInfoDTOList) {
            PromotionVO promotionVO = new PromotionVO();
            promotionVO.setDescription(promotionInfoDTO.getDescription());
            promotionVO.setDiscount(promotionInfoDTO.getDiscount());
            promotionVO.setPlatformBearFee(promotionInfoDTO.getPlatformBearFee());
            promotionVO.setBizBearFee(promotionInfoDTO.getBizBearFee());
            promotionVO.setAgentBearFee(promotionInfoDTO.getAgentBearFee());
            promotionVO.setLogisticsBearCharge(promotionInfoDTO.getLogisticsBearCharge());
            promotionVOList.add(promotionVO);
        }
        return promotionVOList;
    }

    private OrderDetailReq buildOrderDetailReq(OrderDetailRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OrderDetailReq orderDetailReq = new OrderDetailReq();
        orderDetailReq.setChannelOrderId(request.getChannelOrderId());
        orderDetailReq.setChannelId(request.getChannelId());
        orderDetailReq.setTenantId(user.getTenantId());
        orderDetailReq.setOperator(user.getAccountId());
        orderDetailReq.setClientInfo(buildClientInfo());
        return orderDetailReq;
    }

    private OcmsOrderDetailReq buildOcmsOrderDetailReq(BaseOrderReq request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OcmsOrderDetailReq orderDetailReq = new OcmsOrderDetailReq();
        orderDetailReq.setChannelOrderId(request.getChannelOrderId());
        orderDetailReq.setChannelId(request.getChannelId());
        orderDetailReq.setTenantId(user.getTenantId());
        orderDetailReq.setOperator(user.getAccountId());
        return orderDetailReq;
    }

    private OrderDetailReq buildOrderDetailReq(CheckRefundRequest checkRefundRequest) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OrderDetailReq req = new OrderDetailReq();
        req.setTenantId(user.getTenantId());
        req.setOperator(user.getAccountId());
        req.setClientInfo(buildClientInfo());
        req.setChannelOrderId(checkRefundRequest.getChannelOrderId());
        req.setChannelId(checkRefundRequest.getChannelId());
        return req;
    }

    private OcmsOrderDetailReq buildOcmsOrderDetailRequest(CheckRefundRequest checkRefundRequest) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OcmsOrderDetailReq req = new OcmsOrderDetailReq();
        req.setTenantId(user.getTenantId());
        req.setOperator(user.getAccountId());
        req.setChannelOrderId(checkRefundRequest.getChannelOrderId());
        req.setChannelId(checkRefundRequest.getChannelId());
        return req;
    }

    private OrderListResponse buildOrderListResponse(OcmsOrderSearchResponse orderSearchResp) {
        OrderListResponse response = new OrderListResponse();
        if (Objects.nonNull(orderSearchResp.getOrderListResp()) && Objects.nonNull(orderSearchResp.getOrderListResp().getPage())) {
            response.setPageInfo(buildPageInfoVO(orderSearchResp.getOrderListResp().getPage()));
        }
        if (Objects.nonNull(orderSearchResp.getOrderListResp()) && Objects.nonNull(orderSearchResp.getOrderListResp().getOrders())) {
            response.setOrderList(ChannelOrderConverter.buildOrderVOList(orderSearchResp.getOrderListResp().getOrders()));
        }
        return response;
    }

    private OrderListResponse buildOrderListResponse(OrderSearchResp orderSearchResp) {
        OrderListResponse response = new OrderListResponse();
        if (Objects.nonNull(orderSearchResp.getOrderListResp()) && Objects.nonNull(orderSearchResp.getOrderListResp().getPage())) {
            response.setPageInfo(buildPageInfoVO(orderSearchResp.getOrderListResp().getPage()));
        }
        if (Objects.nonNull(orderSearchResp.getOrderListResp()) && Objects.nonNull(orderSearchResp.getOrderListResp().getOrders())) {
            response.setOrderList(buildOrderVOList(orderSearchResp.getOrderListResp().getOrders()));
        }
        return response;
    }

    private List<OrderVO> buildOrderVOList(List<OrderInfoDTO> orderInfoDTOList) {
        List<OrderVO> orderVOList = Lists.newArrayList();
        for (OrderInfoDTO orderInfoDTO : orderInfoDTOList) {
            OrderVO orderVO = new OrderVO();
            orderVO.setTenantId(orderInfoDTO.getTenantId());
            orderVO.setChannelId(orderInfoDTO.channelId);
            orderVO.setChannelName(orderInfoDTO.getChannelName());
            orderVO.setStoreId(orderInfoDTO.getShopId());
            orderVO.setStoreName(orderInfoDTO.getShopName());
            orderVO.setChannelOrderId(orderInfoDTO.getChannelOrderId());
            orderVO.setSerialNo(orderInfoDTO.getOrderSerialNumber());
            orderVO.setItemCount(orderInfoDTO.getItemCount());
            orderVO.setActualPayAmt(orderInfoDTO.getActualPayAmt());
            orderVO.setBizReceiveAmt(orderInfoDTO.getBizReceiveAmt());
            orderVO.setDeliveryMethod(orderInfoDTO.getDeliveryMethod());
            orderVO.setDeliveryMethodDesc(orderInfoDTO.getDeliveryName());
            orderVO.setDeliveryUserName(orderInfoDTO.getRiderName());
            orderVO.setDeliveryUserPhone(orderInfoDTO.getRiderPhone());
            orderVO.setReceiverName(orderInfoDTO.getReceiverName());
            orderVO.setReceiverPhone(orderInfoDTO.getReceiverPhone());
            orderVO.setReceiveAddress(orderInfoDTO.getReceiveAddress());
            orderVO.setChannelOrderStatus(orderInfoDTO.getChannelOrderStatus());
            orderVO.setChannelOrderStatusDesc(orderInfoDTO.getChannelOrderStatusDesc());
            orderVO.setAggregationOrderStatus(orderInfoDTO.getAggregationOrderStatus());
            orderVO.setCreateTime(orderInfoDTO.getCreateTime());
            orderVO.setRefundAmt(orderInfoDTO.getRefundAmt());
            orderVO.setRefundTagId(orderInfoDTO.getRefundTagId());
            orderVO.setRefundTagDesc(orderInfoDTO.getRefundTagDesc());
            orderVO.setAuditingRefundTagId(orderInfoDTO.getAuditingRefundTagId());
            orderVO.setAuditingRefundTagDesc(orderInfoDTO.getAuditingRefundTagDesc());
            orderVO.setCouldOperateItemList(orderInfoDTO.getCouldOperateItemList());
            orderVO.setDeliveryOrderType(orderInfoDTO.getDeliveryOrderType());
            orderVO.setDeliveryOrderTypeName(orderInfoDTO.getDeliveryOrderTypeTypeName());
            orderVO.setEstimatedSendArriveTimeStart(orderInfoDTO.getEstimatedSendArriveTimeStart());
            orderVO.setEstimatedSendArriveTimeEnd(orderInfoDTO.getEstimatedSendArriveTimeEnd());
            orderVO.setPickupStatus(orderInfoDTO.getDistributeStatus());
            orderVO.setPickupCompleteTime(orderInfoDTO.getPickupCompleteTime());
            orderVO.setDistributeStatus(orderInfoDTO.getDistributeStatus());
            orderVO.setOfflineOrderStatus(orderInfoDTO.getOfflineOrderStatus());
            orderVO.setUpdateTime(orderInfoDTO.getUpdateTime());
            orderVO.setChannelExtraOrderId(orderInfoDTO.getChannelExtraOrderId());
            orderVO.setComments(orderInfoDTO.getComments());
            orderVO.setProductList(buildProductVOList(orderInfoDTO.getProducts()));
            if (orderInfoDTO.getUserId() > BigInteger.ZERO.longValue()) {
                orderVO.setUserId(orderInfoDTO.getUserId());
            }
            if (CollectionUtils.isNotEmpty(orderInfoDTO.getTags())) {
                orderVO.setUserTags(UserTagTypeEnum.getTags(orderInfoDTO.getTags()));
            }
            // 支付时间
            orderVO.setPayTime(orderInfoDTO.getPayTime() > 0 ? orderInfoDTO.getPayTime() : orderInfoDTO.getCreateTime());
            orderVOList.add(orderVO);
        }
        return orderVOList;
    }

    private List<ProductVO> buildProductVOList(List<ProductInfoDTO> productInfoDTOList) {
        //履约标签
        int FULFILLMENT_TAG = 1;

        //拣货标准
        int PICK_TAG = 2;
        if (CollectionUtils.isEmpty(productInfoDTOList)) {
            return Lists.newArrayList();
        }
        List<ProductVO> productVOList = Lists.newArrayList();
        for (ProductInfoDTO productInfoDTO : productInfoDTOList) {
            ProductVO productVO = new ProductVO();
            productVO.setSkuId(productInfoDTO.getSkuId());
            productVO.setCustomerSkuId(productInfoDTO.getCustomerSkuId());
            productVO.setUpcCode(productInfoDTO.getUpcCode());
            productVO.setSkuName(productInfoDTO.getSkuName());
            productVO.setOriginalTotalPrice(productInfoDTO.getTotalOriginAmount());
            productVO.setTotalPayAmount(productInfoDTO.getTotalCurrentPrice());
            productVO.setTotalDiscountAmount(productInfoDTO.getTotalDiscountAmount());
            productVO.setPicUrl(processPicUrl(productInfoDTO.getPicUrl()));
            productVO.setSpecification(productInfoDTO.getSpecification());
            productVO.setSellUnit(productInfoDTO.getSellUnit());
            productVO.setUnitPrice(productInfoDTO.getUnitPrice());
            productVO.setCount(productInfoDTO.getCount());
            productVO.setIsRefund(productInfoDTO.isIsRefund() ? 1 : 0);
            productVO.setRefundCount(productInfoDTO.getRefundCount());
            productVO.setBoothName(productInfoDTO.getBoothName());
            /**
             * ordermng做了特殊处理，如果线下价格不存在(找不到商品的情况，或者没有设置线下价格的情况)，设置了默认值-1分，导致app端展示-0.01
             * 在storeapi侧做特殊值校验，如果是小于0的线下价格，则不展示出来
             */
            productVO.setOfflinePrice(productInfoDTO.getOfflinePrice() < 0 ? null : productInfoDTO.getOfflinePrice());
            productVO.setStallSettleAmt(productInfoDTO.getStallSettleAmt());
            productVO.setCustomerSkuId(productInfoDTO.getCustomerSkuId());
            List<TagInfoVO> tagInfoVOS = org.assertj.core.util.Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(productInfoDTO.getFulfillmentTagList())) {
                for (String fulfillmentTag : productInfoDTO.getFulfillmentTagList()) {
                    TagInfoVO tagInfoVO = new TagInfoVO();
                    tagInfoVO.setName(fulfillmentTag);
                    tagInfoVO.setType(FULFILLMENT_TAG);
                    tagInfoVOS.add(tagInfoVO);
                }
            }
            if (CollectionUtils.isNotEmpty(productInfoDTO.getPickTagList())) {
                for (String pickTag : productInfoDTO.getPickTagList()) {
                    TagInfoVO tagInfoVO = new TagInfoVO();
                    tagInfoVO.setName(pickTag);
                    tagInfoVO.setType(PICK_TAG);
                    tagInfoVOS.add(tagInfoVO);

                }
            }
            productVO.setTagInfoList(tagInfoVOS);
            //设置标签信息tagInfoList字段客户端用法错误、不通用
            //新增tagInfos字段、强制要求客户端根据type处理
            productVO.setTagInfos(
                    Optional.ofNullable(productInfoDTO.getTagInfos()).orElse(Collections.emptyList()).stream()
                            .map(tag -> {
                                TagInfoVO tagInfoVO = new TagInfoVO();
                                tagInfoVO.setName(tag.getName());
                                tagInfoVO.setType(tag.getType());
                                return tagInfoVO;
                            }).collect(Collectors.toList()));
            productVOList.add(productVO);
        }
        return productVOList;
    }

    private String processPicUrl(String picUrl) {
        if (Strings.isEmpty(picUrl)) {
            return picUrl;
        }

        if (picUrl.contains(SYMBOL_COMMA)) {
            String[] picUrls = picUrl.split(SYMBOL_COMMA);
            if (picUrls.length > 0) {
                return picUrls[0];
            }
        }

        return picUrl;
    }

    private PageInfoVO buildPageInfoVO(PageInfoVo pageInfo) {
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(pageInfo.getPage());
        pageInfoVO.setSize(pageInfo.getSize());
        pageInfoVO.setTotalPage(pageInfo.getTotalPage());
        pageInfoVO.setTotalSize(pageInfo.getTotalSize());
        return pageInfoVO;
    }

    private PageInfoVO buildPageInfoVO(PageInfoDTO pageInfoDTO) {
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(pageInfoDTO.getPage());
        pageInfoVO.setSize(pageInfoDTO.getSize());
        pageInfoVO.setTotalPage(pageInfoDTO.getTotalPage());
        pageInfoVO.setTotalSize(pageInfoDTO.getTotalSize());
        return pageInfoVO;
    }

    private OrderListReq buildOrderListReq(OrderListRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OrderListReq orderListReq = new OrderListReq();
        orderListReq.setShopId(request.getStoreId());
        orderListReq.setRefundTagIdList(request.getRefundTagIdList());
        orderListReq.setChannelIdList(request.getChannelIdList());
        orderListReq.setChannelOrderStatusList(request.getChannelOrderStatusList());
        if (StringUtils.isNotEmpty(request.getBeginCreateDate())) {
            LocalDate startDate = LocalDate.parse(request.getBeginCreateDate());
            orderListReq.setBeginCreateTime(org.joda.time.DateTime.parse(startDate.toString()).getMillis());
        }
        if (StringUtils.isNotEmpty(request.getEndCreateDate())) {
            LocalDate endDate = LocalDate.parse(request.getEndCreateDate());
            orderListReq.setEndCreateTime(org.joda.time.DateTime.parse(endDate.toString()).plusDays(1).getMillis());
        }

        orderListReq.setTenantId(user.getTenantId());
        orderListReq.setPage(request.getPage());
        orderListReq.setPageSize(request.getSize());
        orderListReq.setOperator(user.getAccountId());
        orderListReq.setShowOperateItems(request.getShowOperateItems());
        orderListReq.setClientInfo(buildClientInfo());
        orderListReq.setSmartQuery(request.getKeyword());
        if (Objects.nonNull(request.getAggregationOrderStatus())) {
            orderListReq.setAggregationOrderStatus(request.getAggregationOrderStatus());
        }
        if (Objects.nonNull(request.getDeliveryOrderType())) {
            orderListReq.setDeliveryOrderType(request.getDeliveryOrderType());
        }
        return orderListReq;
    }

    private OcmsOrderListReq buildOcmsOrderListReq(OrderListRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OcmsOrderListReq orderListReq = new OcmsOrderListReq();
        orderListReq.setShopId(request.getStoreId());
        orderListReq.setRefundTagIdList(request.getRefundTagIdList());
        orderListReq.setChannelIdList(request.getChannelIdList());
        orderListReq.setChannelOrderStatusList(request.getChannelOrderStatusList());
        if (StringUtils.isNotEmpty(request.getBeginCreateDate())) {
            LocalDate startDate = LocalDate.parse(request.getBeginCreateDate());
            orderListReq.setBeginCreateTime(org.joda.time.DateTime.parse(startDate.toString()).getMillis());
        }
        if (StringUtils.isNotEmpty(request.getEndCreateDate())) {
            LocalDate endDate = LocalDate.parse(request.getEndCreateDate());
            orderListReq.setEndCreateTime(org.joda.time.DateTime.parse(endDate.toString()).plusDays(1).getMillis());
        }

        orderListReq.setTenantId(user.getTenantId());
        orderListReq.setPage(request.getPage());
        orderListReq.setPageSize(request.getSize());
        orderListReq.setOperator(user.getAccountId());
        orderListReq.setSmartQuery(request.getKeyword());
        if (Objects.nonNull(request.getAggregationOrderStatus())) {
            orderListReq.setAggregationOrderStatus(request.getAggregationOrderStatus());
        }
        if (Objects.nonNull(request.getDeliveryOrderType())) {
            orderListReq.setDeliveryOrderType(request.getDeliveryOrderType());
        }
        return orderListReq;
    }

    private ClientInfo buildClientInfo() {
        String os = ApiMethodParamThreadLocal.getIdentityInfo().getOs();
        ClientInfo clientInfo = new ClientInfo();
        if (StringUtils.isNotEmpty(os) && Objects.nonNull(DeviceTypeEnum.valueOf(os.toUpperCase()))) {
            clientInfo.setClientType(DeviceTypeEnum.valueOf(os.toUpperCase()).getValue());
        }
        return clientInfo;
    }

    private Map<String, List<Integer>> findOrderCanOperateMap(List<OCMSOrderKey> ocmsOrderKeyList, List<Integer> toCheckItems) {
        Map<String, List<Integer>> map = Maps.newHashMap();
        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        if (CollectionUtils.isNotEmpty(ocmsOrderKeyList)) {
            OCMSOperateCheckRequest request = new OCMSOperateCheckRequest();
            request.setTenantId(tenantId);
            request.setToCheckOperateItems(toCheckItems);
            request.setOrderList(ocmsOrderKeyList);
            OCMSOperateCheckResponse response = null;

            try {
                response = ocmsOrderOperateThriftService.checkOrderCouldOperateItems(request);
                log.info("查询订单可操作项，request:{}, response:{}", request, response);
                if (response.getCouldOperateItems() != null && !response.getCouldOperateItems().isEmpty()) {
                    for (Map.Entry<OCMSOrderKey, List<Integer>> entry : response.getCouldOperateItems().entrySet()) {
                        map.put(entry.getKey().channelOrderId, entry.getValue());
                    }
                }
            } catch (TException e) {
                log.error("查询可操作列表失败,request:{}", request, e);
            }
        }

        return map;
    }

    private void setWeightRefundOperateItem(List<OrderVO> orderList) {
        if (CollectionUtils.isNotEmpty(orderList)) {
            List<OCMSOrderKey> ocmsOrderKeyList = orderList.stream().map(e -> OCMSOrderKey.builder()
                    .channelOrderId(e.getChannelOrderId())
                    .channelType(e.getChannelId())
                    .build()).collect(Collectors.toList());
            Map<String, List<Integer>> map = findOrderCanOperateMap(ocmsOrderKeyList, Lists.newArrayList(OrderCouldOperateItem.WEIGHT_REFUND.getValue()));
            for (OrderVO orderVO : orderList) {
                List<Integer> operateList = map.get(orderVO.getChannelOrderId());
                if (CollectionUtils.isNotEmpty(operateList) && orderVO.getCouldOperateItemList() != null) {
                    orderVO.getCouldOperateItemList().addAll(operateList);
                }
            }
        }
    }

    private void setWeightRefundOperateItem(OrderDetailVO orderDetailVO) {
        if (Objects.nonNull(orderDetailVO)) {
            List<OCMSOrderKey> ocmsOrderKeyList = Lists.newArrayList(OCMSOrderKey.builder()
                    .channelOrderId(orderDetailVO.getChannelOrderId())
                    .channelType(orderDetailVO.getChannelId())
                    .build());
            Map<String, List<Integer>> map = findOrderCanOperateMap(ocmsOrderKeyList, Lists.newArrayList(OrderCouldOperateItem.WEIGHT_REFUND.getValue()));
            List<Integer> operates = map.get(orderDetailVO.getChannelOrderId());
            if (CollectionUtils.isNotEmpty(operates) && orderDetailVO.getCouldOperateItemList() != null) {
                orderDetailVO.getCouldOperateItemList().addAll(operates);
            }
        }
    }

    private List<BizOrderWeightRefundItemModel> buildBizOrderWeightRefundItemModelList(List<WeightRefundProductVO> VOList) {
        List<BizOrderWeightRefundItemModel> orderWeightRefundItemModels = org.assertj.core.util.Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(VOList)) {
            for (WeightRefundProductVO VO : VOList) {
                if (Objects.isNull(VO) || Objects.isNull(VO.getApplyWeight()) || StringUtils.isBlank(VO.getCustomSkuId())) {
                    return null;
                }
                BizOrderWeightRefundItemModel model = new BizOrderWeightRefundItemModel();
                model.setSkuId(VO.getSkuId());
                model.setCustomSkuId(VO.getCustomSkuId());
                model.setApplyWeight(VO.getApplyWeight());
                model.setSkuName(VO.getSkuName());
                orderWeightRefundItemModels.add(model);
            }
        }
        return orderWeightRefundItemModels;
    }

    private WeightRefundCheckResponse buildWeightRefundCheckResponse(com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.OrderDetailResponse response) {
        if (Objects.isNull(response)) {
            return null;
        }
        WeightRefundCheckResponse weightRefundCheckResponse = new WeightRefundCheckResponse();
        List<OrderItemWeightRefundCheckVO> orderItemWeightRefundCheckVOS = org.assertj.core.util.Lists.newArrayList();
        for (OrderItem orderItem : response.getOrderDetail().getOrderItemList()) {
            OrderItemWeightRefundCheckVO orderItemWeightRefundCheckVO = new OrderItemWeightRefundCheckVO();
            orderItemWeightRefundCheckVO.setInnerSkuId(orderItem.getInstoreSkuId());
            orderItemWeightRefundCheckVO.setCustomerSkuId(orderItem.getCustomerSkuId());
            orderItemWeightRefundCheckVO.setSkuName(orderItem.getSkuName());
            orderItemWeightRefundCheckVO.setChannelWeight(orderItem.getChannelWeight());
            orderItemWeightRefundCheckVO.setCanRefundCount(orderItem.getCanWeightRefundCount());
            orderItemWeightRefundCheckVO.setPicUrl(orderItem.getPicUrl());
            orderItemWeightRefundCheckVO.setSpecification(orderItem.getSpecification());
            orderItemWeightRefundCheckVO.setWeightRefundAfterSaleRecordVOS(buildWeightRefundAfterSaleRecordList(orderItem, response.getOrderDetail().getAfterSaleApplyList()));
            orderItemWeightRefundCheckVOS.add(orderItemWeightRefundCheckVO);
        }
        weightRefundCheckResponse.setWeightRefundCheckVOList(orderItemWeightRefundCheckVOS);
        return weightRefundCheckResponse;
    }

    private List<WeightRefundAfterSaleRecordVO> buildWeightRefundAfterSaleRecordList(OrderItem orderItem, List<AfterSaleApply> afterSaleApplyList) {
        List<WeightRefundAfterSaleRecordVO> weightRefundAfterSaleRecordVOS = org.assertj.core.util.Lists.newArrayList();
        for (AfterSaleApply afterSaleApply : afterSaleApplyList) {
            for (AfterSaleApplyDetail afterSaleApplyDetail : afterSaleApply.getAfterSaleApplyDetailList()) {
                //若当前商品项发生过克重退款
                if (orderItem.getOrderItemId().equals(afterSaleApplyDetail.getOrderItemId()) && afterSaleApply.getAfsPattern() == AfterSalePatternEnum.WEIGHT.getValue()) {
                    WeightRefundAfterSaleRecordVO weightRefundAfterSaleRecordVO = new WeightRefundAfterSaleRecordVO();
                    weightRefundAfterSaleRecordVO.setRefundAmount(afterSaleApplyDetail.getRefundAmt());
                    weightRefundAfterSaleRecordVO.setRefundWeight(afterSaleApplyDetail.getRefundWeight());
                    weightRefundAfterSaleRecordVO.setSkuName(afterSaleApplyDetail.getSkuName());
                    weightRefundAfterSaleRecordVOS.add(weightRefundAfterSaleRecordVO);
                }

            }
        }


        return weightRefundAfterSaleRecordVOS;
    }

    private RefundReasonAndCodeVO convertRefundReason(OCMSRefundReasonAndCodeModel model) {
        RefundReasonAndCodeVO vo = new RefundReasonAndCodeVO();
        vo.setCode(model.getCode());
        vo.setReason(model.getReason());
        return vo;
    }

    private boolean success(com.meituan.shangou.saas.dto.Status status) {
        return Objects.equals(ResultCode.SUCCESS.getCode(), status.getCode());
    }

    public List<OrderStatusLogVO> buildOrderStatusLogs(OrderDetailVo orderDetail, OpenTraceResponse openTraceResponse) {
        List<OrderStatusLogVO> orderStatusLists = Lists.newArrayList();
        // 订单状态流
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderDetail.getOrderOpLogList())) {
            orderDetail.getOrderOpLogList().stream().forEach(e -> {
                if (StringUtils.isNotBlank(e.getOptContent())) {
                    OrderStatusLogVO logViewVo = new OrderStatusLogVO();
                    StringBuilder desc = new StringBuilder();
                    desc.append(StringUtils.trimToEmpty(e.getOptContent()));
                    if (StringUtils.isNotBlank(e.getOptDesc())) {
                        desc.append("[");
                        // 追加发起人(商家发起/用户发起)
                        if (e.getOperatorType() != null
                                && (OrderStatusEnum.REFUND_APPLIED.getDesc().equals(e.getOptContent())
                                || OrderStatusEnum.CANCELED.getDesc().equals(e.getOptContent()))) {
                            OperatorTypeEnum operatorType = OperatorTypeEnum.enumOf(e.getOperatorType());
                            desc.append(ChannelOrderConverter.convertOperateMsg(operatorType))
                                    .append("发起");
                        }
                        desc.append(e.getOptDesc()).append("]");
                    }
                    if (StringUtils.isNotBlank(e.getOperator())) {
                        desc.append("\n[操作人:").append(e.getOperator()).append("]");
                    }
                    logViewVo.setStatusDesc(desc.toString());
                    logViewVo.setTime(e.getOptTime());
                    orderStatusLists.add(logViewVo);
                }
            });
        }

        // 配送状态流
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderDetail.getDeliveryStateLogList())) {
            orderDetail.getDeliveryStateLogList().stream().forEach(e -> {
                OrderStatusLogVO logViewVo = new OrderStatusLogVO();
                if (StringUtils.isNotEmpty(e.getDeliveryCount()) && NumberUtils.toInt(e.getDeliveryCount()) > 1) {
                    logViewVo.setStatusDesc(e.getStateDesc() + "[第" + e.getDeliveryCount() + "次配送]");
                } else {
                    logViewVo.setStatusDesc(e.getStateDesc());
                }
                logViewVo.setTime(e.getUpdateTime());
                orderStatusLists.add(logViewVo);
            });
        }

        // 拣货状态流
        try {
            if (null !=  openTraceResponse && openTraceResponse.getDetail() != null) {
                OpenTraceDetail openTraceDetail = openTraceResponse.getDetail();
                if (openTraceDetail.getPickFinishTime() > 0) {
                    OrderStatusLogVO logViewVo = new OrderStatusLogVO();
                    logViewVo.setStatusDesc("出餐完成");
                    logViewVo.setTime(TimeUnit.SECONDS.toMillis(openTraceDetail.getPickFinishTime()));
                    orderStatusLists.add(logViewVo);
                }
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(openTraceDetail.getTaskDetails())) {
                    // 任务领取
                    List<TaskDetail> receiveTask = openTraceDetail.getTaskDetails().stream().filter(e -> OpenOpType.receiveEvent == e.getOpType())
                            .sorted((o1, o2) -> o1.getOpTime() - o2.getOpTime() > 0 ? -1 : 1).collect(Collectors.toList());

                    String name = Joiner.on(",").join(receiveTask.stream().map(e -> e.getOpName()).distinct().collect(Collectors.toList()));
                    receiveTask.stream().findFirst()
                            .ifPresent(e -> {
                                OrderStatusLogVO logViewVo = new OrderStatusLogVO();
                                logViewVo.setStatusDesc(name + "领取任务");
                                logViewVo.setTime(TimeUnit.SECONDS.toMillis(e.getOpTime()));
                                orderStatusLists.add(logViewVo);
                            });
                    //拣货
                    List<TaskDetail> pickTask = openTraceDetail.getTaskDetails().stream().filter(e -> OpenOpType.pickEvent == e.getOpType())
                            .sorted((o1, o2) -> o1.getOpTime() - o2.getOpTime() > 0 ? -1 : 1).collect(Collectors.toList());
                    List<TaskDetail> mergeTask =
                            openTraceDetail.getTaskDetails().stream().filter(e -> OpenOpType.mergeEvent == e.getOpType())
                                    .sorted((o1, o2) -> o1.getOpTime() - o2.getOpTime() > 0 ? -1 : 1).collect(Collectors.toList());
                    List<PickWODetailDTO> workOrderDetailList = Collections.emptyList();
                    if((CollectionUtils.isNotEmpty(pickTask) || CollectionUtils.isNotEmpty(mergeTask))
                            && openTraceDetail.getOrderId() > 0){

                        Long tenantId = orderDetail.getOrderBaseDto().getTenantId();
                        Long offlineStoreId = orderDetail.getOrderBaseDto().getShopId();
                        if(orderDetail.getOrderBaseDto().getWarehouseId()!=null){
                            offlineStoreId = orderDetail.getOrderBaseDto().getWarehouseId();
                        }
                        if(orderDetail.getOrderBaseDto().getDispatchShopId()!=null){
                            offlineStoreId = orderDetail.getOrderBaseDto().getDispatchShopId();
                        }

                       workOrderDetailList = pickSelectFacade.getPickWorkOrder4OrderIdAndTypeList(
                                openTraceDetail.getOrderId(),
                                ImmutableList.of( WorkOrderTypeEnum.FULFILL.getValue(),
                                        WorkOrderTypeEnum.DISPATCH.getValue(),
                                        WorkOrderTypeEnum.PICK.getValue(),
                                        WorkOrderTypeEnum.MERGE_FLOW.getValue()),tenantId,offlineStoreId);
                    }

                    Pair<String, Long> pickNamesAndOpTime = getPickCompleteOperatorNames(pickTask, workOrderDetailList);
                    if(StringUtils.isNotBlank(pickNamesAndOpTime.getKey())){
                        OrderStatusLogVO logViewVo = new OrderStatusLogVO();
                        logViewVo.setStatusDesc(pickNamesAndOpTime.getKey() + "拣货完成");
                        logViewVo.setTime(TimeUnit.SECONDS.toMillis(pickNamesAndOpTime.getValue()));
                        orderStatusLists.add(logViewVo);
                    }
                    // 合流、目前按单拣货无合流流程、拣货员在订单页面点击拣货完成系统会写入合流完成流水、需要根据是否存在合流工单判断
                    if(CollectionUtils.isEmpty(workOrderDetailList)
                            || workOrderDetailList
                            .stream()
                            .anyMatch(work -> Objects.equals(work.getType(), WorkOrderTypeEnum.MERGE_FLOW.getValue()))){
                        String mergeNames = Joiner.on(",").join(mergeTask.stream().map(e -> e.getOpName()).distinct().collect(Collectors.toList()));
                        mergeTask.stream().findFirst()
                                .ifPresent(e -> {
                                    OrderStatusLogVO logViewVo = new OrderStatusLogVO();
                                    logViewVo.setStatusDesc(mergeNames + "合流完成");
                                    logViewVo.setTime(TimeUnit.SECONDS.toMillis(e.getOpTime()));
                                    orderStatusLists.add(logViewVo);
                                });
                    }
                }

            }
        } catch (Exception e) {
            log.error("获取拣货状态流失败,将忽略拣货状态。order:{}", orderDetail.getOrderBaseDto().getChannelOrderId(), e);
        }

        for (AfterSaleRecordVo afterSaleRecord : Optional.ofNullable(orderDetail.getAfterSaleRecords()).orElse(Collections.emptyList())) {
            if (afterSaleRecord.getAfsPattern() == AfterSalePatternEnum.WEIGHT.getValue()) {
                OrderStatusLogVO orderStatusLogVO = new OrderStatusLogVO();
                orderStatusLogVO.setTime(afterSaleRecord.getUpdateTime());
                orderStatusLogVO.setStatusDesc("商家按重量退差价");
                orderStatusLists.add(orderStatusLogVO);
            }
        }
        // 排序
        Collections.sort(orderStatusLists, Comparator.comparing(OrderStatusLogVO::getTime).reversed());
        return orderStatusLists;
    }

    public static List<String> getPickNames(OpenTraceResponse openTraceResponse) {
        try{
            if (Objects.nonNull(openTraceResponse) && Objects.nonNull(openTraceResponse.getDetail()) && CollectionUtils.isNotEmpty(openTraceResponse.getDetail().getTaskDetails())){
                //获取订单的拣货工单的操作员名称list
                List<String> pickOperatorNames = openTraceResponse.getDetail().getTaskDetails().stream()
                        .filter(e -> OpenOpType.pickEvent == e.getOpType())
                        .map(TaskDetail::getOpName)
                        .distinct()
                        .collect(Collectors.toList());
                return pickOperatorNames;
            }
            return Collections.emptyList();
        }catch (Exception e){
            log.error("获取拣货员名称失败：", e);
        }
        return Collections.emptyList();
    }

    private OpenTraceResponse getOpenTraceResponse(OrderDetailVo orderDetail) throws TException {
        try {
            String channelOrderID = orderDetail.getOrderBaseDto().getChannelOrderId();
            OpenTraceResponse openTraceResponse = openPickThriftService.queryOrderTrace(
                    new OpenTraceQueryRequest()
                            .setSource(convertBizType(orderDetail.getOrderBaseDto().getChannelId()))
                            .setUnifyOrderId(channelOrderID)
            );
            return openTraceResponse;
        } catch (TException e) {
            log.error("获取拣货流水失败 ERROR", e);
        }
        return null;
    }

    private Pair<String, Long> getPickCompleteOperatorNames(List<TaskDetail> pickTask,
            List<PickWODetailDTO> workOrderDetailList) {
        if(CollectionUtils.isEmpty(pickTask)){
            return new Pair<>(StringUtils.EMPTY, 0L);
        }
        // 获取工单异常降级为老逻辑
        String operatorNames = Joiner.on(",")
                .join(pickTask.stream().map(TaskDetail::getOpName).distinct().collect(Collectors.toList()));
        if(CollectionUtils.isEmpty(workOrderDetailList)){
            return new Pair<>(operatorNames, pickTask.get(0).getOpTime());
        }
        Map<String, List<PickWODetailDTO>> operatorOrderMap = workOrderDetailList.stream()
                .collect(Collectors.groupingBy(order -> String.valueOf(order.getType())));
        boolean finalStatus = operatorOrderMap.getOrDefault(String.valueOf(WorkOrderTypeEnum.FULFILL.getValue()),
                                Collections.emptyList())
                .stream()
                .allMatch(order -> FulfillWorkOrderStatusEnum.isFinalStatus(order.getStatus()));
        // 履约工单是终态直接返回
        if(finalStatus){
            return new Pair<>(operatorNames, pickTask.get(0).getOpTime());
        }
        List<PickWODetailDTO> pickWODetailDTOList = operatorOrderMap.getOrDefault(
                String.valueOf(WorkOrderTypeEnum.PICK.getValue()), Collections.emptyList());
        // 如果是无需领取(没有待下发工单任务)、拣货完成需要所有拣货工单工单为终态
        if(CollectionUtils.isEmpty(operatorOrderMap.get(String.valueOf(WorkOrderTypeEnum.DISPATCH.getValue())))){
            return pickWODetailDTOList.stream()
                    .allMatch((order -> PickingWorkOrderStatusEnum.isFinalStatus(order.getStatus())))
                    ? new Pair<>(operatorNames, pickTask.get(0).getOpTime())
                    : new Pair<>(StringUtils.EMPTY, 0L);
        }
        // 有领取流程、账号信息对应的工单为终态才算拣货完成
        List<Long> operatorIdList = pickTask.stream().map(TaskDetail::getOperatorId).filter(opId -> pickWODetailDTOList.stream()
                        .filter(work -> Objects.equals(work.getOperatorId(), opId))
                        .allMatch(work -> PickingWorkOrderStatusEnum.isFinalStatus(work.getStatus())))
                .distinct()
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(operatorIdList)){
            Long maxOpTime = pickTask
                    .stream()
                    .filter(detail -> operatorIdList.contains(detail.getOperatorId()))
                    .findFirst().get().getOpTime();
            return new Pair<>(Joiner
                    .on(",")
                    .join(pickTask
                            .stream()
                            .filter(detail -> operatorIdList.contains(detail.getOperatorId()))
                    .map(TaskDetail::getOpName).distinct().collect(Collectors.toList())), maxOpTime);
        }
        return new Pair<>(StringUtils.EMPTY, 0L);
    }

    public static Integer convertBizType(Integer channelId) {
        return ChannelOrderConverter.convertChannelId2OrderBizType(channelId);
    }

    /**
     * 溯源订单列表查询
     *
     * @param orderSearchRequest
     * @param matchChannelOrderIds
     * @return
     */
    public Result<Object> getOrderListByPcComment(OrderSearchRequest orderSearchRequest, List<String> matchChannelOrderIds) {
        try {
            log.info("getOrderListByPcComment orderSearchRequest:{}", orderSearchRequest);
            OrderSearchResponse orderSearchResponse = orderSearchService.listV2(orderSearchRequest);
            if (Objects.isNull(orderSearchResponse)) {
                return ResultBuilder.buildFailResult(ResultCode.INTERNAL_SERVER_ERROR);
            }
            if (orderSearchResponse.getResponseStatus() != StatusCodeEnum.SUCCESS.getCode()) {
                return ResultBuilder.buildFailResult(orderSearchResponse.getResponseStatus(),
                        orderSearchResponse.getMsg());
            }
            List<OrderListByCommentVO> orderListByCommentVOS = OrderListByCommentVO
                    .buildByOrderList(orderSearchResponse.getOrderList(), matchChannelOrderIds);
            return ResultBuilder.buildSuccess(new PageResult<>(orderListByCommentVOS, 1, orderListByCommentVOS.size(),
                    orderListByCommentVOS.size()));
        } catch (TException e) {
            log.error("orderSearchService.getOrderListByAppComment TException", e);
            return ResultBuilder.buildFailResult(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 溯源订单查询
     *
     * @param orderSearchRequest
     * @param matchChannelOrderIds
     * @return
     */
    public CommonResponse<Object> getOrderListByAppComment(OrderSearchRequest orderSearchRequest, List<String> matchChannelOrderIds) {
        try {
            log.info("getOrderListByAppComment orderSearchRequest:{}", orderSearchRequest);
            OrderSearchResponse orderSearchResponse = orderSearchService.listV2(orderSearchRequest);
            if (Objects.isNull(orderSearchResponse)) {
                return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR.getCode(),
                        ResultCode.INTERNAL_SERVER_ERROR.getErrorMessage());
            }
            if (orderSearchResponse.getResponseStatus() != StatusCodeEnum.SUCCESS.getCode()) {
                return CommonResponse.fail(orderSearchResponse.getResponseStatus(), orderSearchResponse.getMsg());
            }
            List<OrderListByCommentVO> orderListByCommentVOS = OrderListByCommentVO
                    .buildByOrderList(orderSearchResponse.getOrderList(), matchChannelOrderIds);
            return CommonResponse.success(new PageResult<>(orderListByCommentVOS, 1, orderListByCommentVOS.size(),
                    orderListByCommentVOS.size()));
        } catch (TException e) {
            log.error("rderSearchService.getOrderListByAppComment TException", e);
            return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR.getCode(),
                    ResultCode.INTERNAL_SERVER_ERROR.getErrorMessage());
        }
    }

}
