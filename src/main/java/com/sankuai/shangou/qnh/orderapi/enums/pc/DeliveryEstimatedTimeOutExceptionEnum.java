package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;

import java.util.List;

public enum DeliveryEstimatedTimeOutExceptionEnum {

    /**
     * 未接单
     */
    NO_RIDER_ACCEPT(
            Lists.newArrayList(DeliveryStatusEnum.INIT, DeliveryStatusEnum.DELIVERY_LAUNCHED
                    , DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, DeliveryStatusEnum.DELIVERY_REJECTED
                    , DeliveryStatusEnum.DELIVERY_FAILED, DeliveryStatusEnum.DELIVERY_CANCELLED),
            DeliveryExceptionCodeEnum.NO_RIDE_ACCEPT),
    /**
     * 未到店
     */
    NO_ARRIVAL_STORE(Lists.newArrayList(DeliveryStatusEnum.RIDER_ASSIGNED),
            DeliveryExceptionCodeEnum.NO_ARRIVAL_STORE),
    /**
     * 未取货
     */
    NO_RIDER_TAKE_GOODS(Lists.newArrayList(DeliveryStatusEnum.RIDER_ARRIVED_SHOP),
            DeliveryExceptionCodeEnum.RIDER_PICK_TIME_OUT),
    /**
     * 配送超时
     */
    DELIVERY_TIMEOUT(
            Lists.newArrayList(DeliveryStatusEnum.RIDER_TAKEN_GOODS),
            DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT);

    private final List<DeliveryStatusEnum> deliveryStatusList;
    private final DeliveryExceptionCodeEnum deliveryExceptionCodeEnum;

    DeliveryEstimatedTimeOutExceptionEnum(List<DeliveryStatusEnum> deliveryStatusList, DeliveryExceptionCodeEnum deliveryExceptionCodeEnum) {
        this.deliveryStatusList = deliveryStatusList;
        this.deliveryExceptionCodeEnum = deliveryExceptionCodeEnum;
    }

    public List<DeliveryStatusEnum> getDeliveryStatusList() {
        return deliveryStatusList;
    }

    public DeliveryExceptionCodeEnum getDeliveryExceptionCodeEnum() {
        return deliveryExceptionCodeEnum;
    }

    public boolean check(Integer deliveryStatus) {
        if (deliveryStatus == null) {
            return false;
        }
        DeliveryStatusEnum deliveryStatusEnum = DeliveryStatusEnum.valueOf(deliveryStatus);
        return deliveryStatusEnum != null && deliveryStatusList.contains(deliveryStatusEnum);
    }

    public static DeliveryExceptionCodeEnum deliveryStatusCodeValueOf(Integer deliveryStatus) {
        for (DeliveryEstimatedTimeOutExceptionEnum exceptionEnum : values()) {
            if (exceptionEnum.check(deliveryStatus)) {
                return exceptionEnum.getDeliveryExceptionCodeEnum();
            }
        }
        return DELIVERY_TIMEOUT.getDeliveryExceptionCodeEnum();
    }

}
