package com.sankuai.shangou.qnh.orderapi.utils.app;


import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.shangou.saas.common.data.BaseResult;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2021/12/29 上午10:54
 **/
@Slf4j
public class RpcInvoker {

    /**
     * 远程调用，返回结果不为空
     *
     * @param c
     * @param <T>
     * @return
     */
    public static <T> T invokeRetry(Callable<T> c, int retryTimes) {
        T t = null;
        int excuteTimes = 0;
        do {
            try {
                excuteTimes++;
                t = invoke(c);
                break;
            } catch (Exception e) {
                log.info("invokeRetry fail", e);
            }
        }while (excuteTimes <= retryTimes);
        if (t == null) {
            throw new BizException("系统异常，请重试");
        }
        return t;
    }

    /**
     * 远程调用，返回结果不为空
     *
     * @param c
     * @param <T>
     * @return
     */
    public static <T> T invokeReturn(Callable<T> c) {
        T t = invoke(c);
        if (t == null) {
            throw new BizException("系统异常，请重试");
        }
        return t;
    }

    /**
     * 远程调用,返回结果不为空
     * 拋出特定异常
     *
     * @param c
     * @param baseResult 异常信息
     * @param <T>
     * @return
     */
    public static <T> T invokeReturn(Callable<T> c, BaseResult baseResult) {
        T t = invoke(c, baseResult);
        if (t == null) {
            throw new BizException(BaseResult.build(baseResult.getCode(), baseResult.getCode() + "返回null"));
        }
        return t;
    }


    /**
     * 远程调用，捕获异常
     *
     * @param c
     * @param <T>
     * @return
     */
    public static <T> T invokeCatch(Callable<T> c) {
        T t = null;
        try {
            t = c.call();
        } catch (Exception e) {
            log.error("系统异常!", e);
        }
        return t;
    }


    /**
     * 远程调用
     *
     * @param c
     * @param <T>
     * @return
     */
    public static <T> T invoke(Callable<T> c) {
        T t;
        try {
            t = c.call();
        } catch (Exception e) {
            log.error("远程调用失败", e);
            throw new BizException("不好意思出错了，请稍候再试", e);
        }
        return t;
    }



    public static <R,V> V invoke(R r, Function<R,V> function) {
        V t;
        try {
            log("rpc request",r);
            t = function.apply(r);
            log("rpc response",t);
        } catch (Exception e) {
            throw new BizException("系统异常，请重试", e);
        }
        return t;
    }

    private static <R> void log(String prefix, R r) {
        try {
            log.info("{}:{}", prefix, JSONObject.toJSON(r));
        } catch (Exception e) {
            log.error("log error", e);
        }
    }


    /**
     * 远程调用
     * 拋出特定异常
     *
     * @param c
     * @param <T>
     * @return
     * @Param baseResult 异常信息
     */
    public static <T> T invoke(Callable<T> c, BaseResult baseResult) {
        T t;
        try {
            t = c.call();
        } catch (Exception e) {
            throw new BizException(baseResult, e);
        }
        return t;
    }



    public static <T, R> T handleCode(R r, Function<? super R, Boolean> s, Function<? super R, T> function1, BaseResult baseResult) {

        if(null == r) {
            log.error("调用远程接口返回数据异常，结果为：{}", r);
            throw new BizException("远程接口数据返回null数据");
        }

        if (s.apply(r)) {

            return function1.apply(r);

        } else {
            log.error("调用远程接口发生异常，错误码：{}。错误信息：{}", baseResult.getCode(), baseResult.getMsg());

            throw new BizException(baseResult);
        }

    }

}
