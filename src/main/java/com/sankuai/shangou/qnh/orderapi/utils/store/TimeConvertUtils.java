package com.sankuai.shangou.qnh.orderapi.utils.store;

import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

@Slf4j
public class TimeConvertUtils {

    public static final int MONTH_START_INDEX = 5;
    public static final int MONTH_END_INDEX = 7;
    public static final int DAY_START_INDEX = 8;
    public static final int DAY_END_INDEX = 10;

    public static final long MAX_DAY_INTERVAL = 10;
    public static final long ONE_DAY = 1;
    public static final long TWO_DAY = 2;

    public static final String TIME_DEADLINE = "2019-01-01 00:00:00";

    /**
    * author: <EMAIL>
    * date: 2019-07-18 21:12:28
    *
    * method: convertDisplayTime
    * params: [dateTimeStr]
    * return: java.lang.String
    * desc: 时间格式转换
     * 当天内
     * 若传入时间大于当前时间，返回“--”
     * 若传入时间小于当前时间 < 2分钟， 返回“1分钟前”
     * 若传入时间小于当前时间 < 1小时，返回“xx分钟前”
     * 若传入时间小于当前时间 < 1天，返回 “XX小时前”
     *
     * 昨天和前天，返回“昨天”和“前天”
     * 2 ＜ 差值 ≤ 10天，按实际展示“XX天前”
     * 日期差 > 10天，格式为“06.05”
     *
     * 跨年，则展示 2019.03.23
    */
    public static String convertDisplayTime(long dateTimeValue) {
        if (dateTimeValue < DateUtil.parse(TIME_DEADLINE, DateUtil.YYYY_MM_DD_HH_MM_SS).getTime()) {
            return "";
        }
        LocalDateTime dateTime = DateUtil.toLocalDateTime(new Date(dateTimeValue));

        LocalDateTime now = LocalDateTime.now();

        Duration duration = Duration.between(dateTime, now);

        if (dateTime.isAfter(now)) {
            return "--";
        }
        LocalDateTime tmpNow = now.minusDays(MAX_DAY_INTERVAL);

        // 按日期展示
        if ( duration.toDays() >= MAX_DAY_INTERVAL + 1 ||
                (duration.toDays() == MAX_DAY_INTERVAL && dateTime.getDayOfMonth() != tmpNow.getDayOfMonth())) {
            return convertDateDisplay(dateTime, now);
        }

        // 按天展示
        if (duration.toDays() > 0 || dateTime.getDayOfMonth() != now.getDayOfMonth()) {
            return convertDayDisplay(dateTime, now);
        }
        // 按小时展示
        else if (duration.toHours() > 0) {
            return duration.toHours() + "小时前";
        }
        // 按分钟展示
        else {
            return duration.toMinutes() > 0 ? duration.toMinutes() + "分钟前" : "1分钟内";
        }
    }

    /**
    * author: <EMAIL>
    * date: 2019-07-26 17:15:10
    *
    * method: convertDateDisplay
    * params: [dateTime, now]
    * return: java.lang.String
    * desc: 按日期展示
    */
    private static String convertDateDisplay(LocalDateTime dateTime, LocalDateTime now) {
        String dateTimeStr= DateUtil.formatLocalDateTime(dateTime, DateUtil.YYYY_MM_DD_HH_MM_SS);
        StringBuilder sb = new StringBuilder();
        sb.append(dateTimeStr.substring(MONTH_START_INDEX, MONTH_END_INDEX)).append(".");
        sb.append(dateTimeStr.substring(DAY_START_INDEX, DAY_END_INDEX));
        return sb.toString();
    }

    /**
    * author: <EMAIL>
    * date: 2019-07-26 17:15:22
    *
    * method: convertDayDisplay
    * params: [dateTime, now]
    * return: java.lang.String
    * desc: 按天展示
    */
    private static String convertDayDisplay(LocalDateTime dateTime, LocalDateTime now) {
        LocalDateTime tmpDate = dateTime.plusDays(ONE_DAY);
        if (tmpDate.getDayOfMonth() == now.getDayOfMonth()) {
            return "昨天";
        }
        tmpDate = dateTime.plusDays(TWO_DAY);
        if (tmpDate.getDayOfMonth() == now.getDayOfMonth()) {
            return "前天";
        }
        int tmpDay = 2;
        long max = MAX_DAY_INTERVAL;

        // 防止异常情况出现死循环，通常此处一定小于10天
        while ((max--) > 0) {
            tmpDate = tmpDate.plusDays(ONE_DAY);
            tmpDay++;
            if (tmpDate.getDayOfMonth() == now.getDayOfMonth()) {
                return tmpDay + "天前";
            }
        }
        return "--";
    }

    public static String convertReviewTime(Long dateTime) {
        if (Objects.isNull(dateTime)) {
            return "";
        }
        try {
            return DateUtil.getStringDateFromMilliSeconds(dateTime, "yyyy.MM.dd HH:mm");
        } catch (Exception e) {
            log.error("审核时间格式错误，dateTime [{}]", dateTime, e);
        }
        return "";
    }
}
