package com.sankuai.shangou.qnh.orderapi.enums.pc;

import org.apache.commons.lang3.StringUtils;

import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.TaskType;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.ProductTaskTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 导入模板类型枚举
 *
 * @author: <EMAIL>
 * Date: 2019/1/14 15:04
 * Description:
 */
@AllArgsConstructor
@Getter
public enum ImportTemplateTypeEnum {

    /**
     *
     */
    STORE_COMPOSE_SKU_BATCH_IMPORT("STORE_COMPOSE_SKU_BATCH_IMPORT", "组合商品导入模板"),
    WAREHOUSE_COMPOSE_SKU_BATCH_IMPORT("WAREHOUSE_COMPOSE_SKU_BATCH_IMPORT", "共享仓组合商品导入模板"),
    SKU_ADD_TEMPLATE("SKU_ADD_TEMPLATE", "SKU导入模板"),
    UPC_ADD_TEMPLATE("UPC_ADD_TEMPLATE", "UPC导入模板"),
    UPC_UPDATE_TEMPLATE("UPC_UPDATE_TEMPLATE", "UPC导入修改模板"),
    SKU_UPDATE_TEMPLATE("SKU_UPDATE_TEMPLATE", "SKU导入修改模板"),
    PRICE_TEMPLATE("PRICE_TEMPLATE", "价格导入模板"),
    PRICE_MEDICINE_TEMPLATE("PRICE_MEDICINE_TEMPLATE", "医药价格导入模板"),
    CHANNEL_BRAND_TEMPLATE("CHANNEL_BRAND_TEMPLATE", "品牌批量导入模板"),
    SKU_TEMPLATE("SKU_TEMPLATE", "总库商品导入模板"),
    SKU_TEMPLATE_V2("SKU_TEMPLATE_V2", "总库商品导入模板"),
    BRAND_TEMPLATE("BRAND_TEMPLATE", "品牌批量导入模板"),
    CATEGORY_TEMPLATE("CATEGORY_TEMPATE", "后台分类批量导入模板"),
    POI_PRICE_TEMPLATE("POI_PRICE_TEMPLATE", "门店价格批量导入模板"),
    BOOTH_CATEGORY_TEMPLATE("BOOTH_CATEGORY_TEMPLATE", "分类摊位批量导入模板"),
    UWMS_STOCK_PLAN_TEMPLATE("UWMS_STOCK_PLAN_TEMPLATE", "无人仓商品计划上架数导入模板"),
    BOOTH_SKU_TEMPLATE("BOOTH_SKU_TEMPLATE", "摊位商品批量导入模板"),
    QUICK_SKU_ADD_TEMPLATE("QUICK_SKU_ADD_TEMPLATE", "商品快捷上线模板"),
    QUICK_SKU_ADD_TEMPLATE_V2("QUICK_SKU_ADD_TEMPLATE_V2", "商品快捷上线模板"),
    QUICK_POI_SKU_ADD_TEMPLATE("QUICK_POI_SKU_ADD_TEMPLATE", "门店商品快捷上线模板"),
    QUICK_POI_SKU_ADD_TEMPLATE_V2("QUICK_POI_SKU_ADD_TEMPLATE_V2", "门店商品快捷上线模板"),
    POI_PRICE_TEMPLATE_V2("POI_PRICE_TEMPLATE_V2", "门店价格批量导入模板"),
    POI_ADD_TEMPATE("POI_ADD_TEMPATE", "门店批量导入模板"),
    POI_BATCH_ADD_TEMPLATE("POI_BATCH_ADD_TEMPLATE", "门店批量导入模板V2"),
    POI_BATCH_RELATE_CHANNEL_TEMPLATE("POI_BATCH_RELATE_CHANNEL_TEMPLATE", "门店批量关联渠道模板"),
    NEED_BOOTH_SETTLE_POI_ADD_TEMPATE("NEED_BOOTH_SETTLE_POI_ADD_TEMPATE", "门店批量导入模板"),
    ACCOUNT_BATCH_ADD_TEMPLATE("ACCOUNT_BATCH_ADD_TEMPLATE", "批量导入账号"),
    SETTLEMENT_ADJUST_TEMPLATE("SETTLEMENT_ADJUST_TEMPLATE", "摊位调整应结金额模板"),
    SKU_ONLINE_SKU("SKU_ONLINE_SKU", "线上商品信息(sku)"),
    SKU_ONLINE_UPC("SKU_ONLINE_UPC", "线上商品信息(upc)"),
    SKU_ONLINE_SPEC("SKU_ONLINE_SPEC", "线上商品信息(spec)"),
    DELIVERY_SKU_TEMPLATE("DELIVERY_SKU_TEMPLATE", "配货单批量导入模板"),
    QUICK_MATCH_SPU_BY_UPC_TEMPLATE("QUICK_MATCH_SPU_BY_UPC_TEMPLATE", "根据UPC匹配新增标品并上线模板"),
    MATCH_SPU_BY_UPC_TEMPLATE("MATCH_SPU_BY_UPC_TEMPLATE", "根据UPC匹配新增标品"),
    BATCH_STORE_PRICE_TEMPLATE_V2("BATCH_STORE_PRICE_TEMPLATE_V2", "门店价格批量导入模板"),

    BATCH_CHANNEL_SKU_PRICE_STRATEGY_CHANGE("BATCH_CHANNEL_SKU_PRICE_STRATEGY_CHANGE", "批量修改单品提价比例模板"),

    /**
     * 中台1.8.6门店级别增加商品属性后，相关导入模版
     */
    POI_SKU_BATCH_IMPORT_TEMPLATE("POI_SKU_BATCH_IMPORT_TEMPLATE", "批量导入门店商品模版"),
    POI_SKU_FAST_ONLINE_TEMPLATE("POI_SKU_FAST_ONLINE_TEMPLATE", "门店商品快捷上线模版"),
    COUNTRY_SKU_ADD_TEMPLATE("COUNTRY_SKU_ADD_TEMPLATE", "批量新增全国商品模板"),
    POI_SKU_BATCH_UPDATE_TEMPLATE("POI_SKU_BATCH_UPDATE_TEMPLATE", "批量修改门店商品模版"),
    REGION_SKU_ADD_TEMPLATE("REGION_SKU_ADD_TEMPLATE", "批量新增区域商品模板"),
    REGION_SKU_UPDATE_TEMPLATE("REGION_SKU_UPDATE_TEMPLATE", "批量新增区域商品模板"),
    BONUS_UPDATE_TEMPLATE("BONUS_UPDATE_TEMPLATE", "结算导入模板"),
    BONUS_UPDATE_SERVICE_STAR_TEMPLATE("BONUS_UPDATE_SERVICE_STAR_TEMPLATE", "服务之星订正模板", "服务之星批量订正模板_1606463708715.xlsx", "服务之星批量订正模板"
            , true, TaskType.TENANT_BONUS_UPDATE_SERVICE_STAR.getValue()),
    BONUS_UPDATE_PUNISHMENT_TEMPLATE("BONUS_UPDATE_PUNISHMENT_TEMPLATE", "惩罚订正模板", "惩罚批量订正模板_1606463570001.xlsx", "惩罚批量订正模板", true
            , TaskType.TENANT_BONUS_UPDATE_PUNISHMENT.getValue()),
    BONUS_UPDATE_GOAL_TEMPLATE("BONUS_UPDATE_GOAL_TEMPLATE", "目标制激励订正模板", "目标制激励批量订正模板_1606463751334.xlsx", "目标制激励批量订正模板", true
            , TaskType.TENANT_BONUS_UPDATE_GOAL.getValue()),
    BONUS_MEET_CRITERION_APPRAISAL_TEMPLATE("BONUS_MEET_CRITERION_APPRAISAL_TEMPLATE", "红线订正模板", "红线考核批量订正模板_1606463731379.xlsx", "红线考核批量订正模板", true
            , TaskType.TENANT_BONUS_MEET_CRITERION_APPRAISAL.getValue()),
    COST_ACCOUNTING_TEMPLATE("COST_ACCOUNTING_TEMPLATE", "成本核算单价导入任务"),
    STORE_SPU_BATCH_ADD_FIX_PRICE_TEMPLATE("STORE_SPU_BATCH_ADD_FIX_PRICE_TEMPLATE", "Excel快速导入门店手动定价商品模板"),
    STORE_SPU_BATCH_ADD_AUTO_TEMPLATE("STORE_SPU_BATCH_ADD_MANUAL_TEMPLATE", "Excel快速导入门店通用提价商品模板"),
    STORE_SPU_BATCH_ADD_WAIMAI_TEMPLATE("STORE_SPU_BATCH_ADD_WAIMAI_TEMPLATE", "Excel快速导入门店商家端模式商品模板"),


    /**
     * 中台菜大全门店级别增加商品属性后，相关导入模版
     */
    POI_SKU_FAST_ONLINE_CDQ_TEMPLATE("POI_SKU_FAST_ONLINE_CDQ_TEMPLATE", "门店商品快捷上线模版"),
    POI_SKU_BATCH_CREATE_CDQ_TEMPLATE("POI_SKU_BATCH_CREATE_CDQ_TEMPLATE", "门店商品快捷上线模版"),
    POI_SKU_BATCH_UPDATE_CDQ_TEMPLATE("POI_SKU_BATCH_UPDATE_CDQ_TEMPLATE", "批量修改门店商品模版"),

    /**
     * spu
     */
    SPU_ADD_TEMPLATE("SPU_ADD_TEMPLATE", "商品主档批量新增模板"),
    SPU_UPDATE_TEMPLATE("SPU_UPDATE_TEMPLATE", "商品主档批量修改模板"),
    SPU_BOOTH_RELATION_ADD_TEMPLATE("SPU_BOOTH_RELATION_ADD_TEMPLATE", "摊位商品批量导入模板"),
    REGION_SPU_ADD_TEMPLATE("REGION_SPU_ADD_TEMPLATE", "批量新增城市商品"),
    REGION_SPU_UPDATE_TEMPLATE("REGION_SPU_UPDATE_TEMPLATE", "批量修改城市商品"),
    STORE_SPU_UPDATE_STATUS_TEMPLATE("STORE_SPU_UPDATE_STATUS_TEMPLATE", "门店商品批量上下架"),

    MERCHANT_STORE_CATEGORY_TEMPLATE("MERCHANT_STORE_CATEGORY_TEMPLATE", "线上分类导入模板"),
    MERCHANT_STORE_CATEGORY_TEMPLATE4ERP("MERCHANT_STORE_CATEGORY_TEMPLATE4ERP", "线上分类导入模板"),

    POI_SPU_BATCH_IMPORT_TEMPLATE("POI_SPU_BATCH_IMPORT_TEMPLATE", "批量导入门店商品"),
    POI_SPU_BATCH_UPDATE_TEMPLATE("POI_SPU_BATCH_UPDATE_TEMPLATE", "批量修改门店商品"),
    POI_SPU_FAST_ONLINE_TEMPLATE("POI_SPU_FAST_ONLINE_TEMPLATE", "批量导入门店商品并上线"),
    STORE_UPC_IMPORT_AND_ONLINE_TEMPLATE("STORE_UPC_IMPORT_AND_ONLINE_TEMPLATE", "批量导入门店标品并上线"),

    /**
     * 医药任务模板
     */
    MEDICINE_STORE_SKU_UPDATE_STATUS_TEMPLATE("MEDICINE_STORE_SKU_UPDATE_STATUS_TEMPLATE", "门店商品批量上下架"),

    /**
     * 配送配置
     */
    DELIVERY_SHOP_CONFIG_TEMPLATE("DELIVERY_SHOP_CONFIG_TEMPLATE", "门店配送配置导入模板"),
    DELIVERY_CONFIG_BATCH_IMPORT_TEMPLATE("DELIVERY_CONFIG_BATCH_IMPORT_TEMPLATE", "门店配送配置批量导入模板"),

    /**
     * 市调相关模板
     */
    MARKET_RESEARCH_SKU_IMPORT_TEMPLATE("MARKET_RESEARCH_SKU_IMPORT_TEMPLATE", "市调选品导入模版"),
    BATCH_IMPORT_MR_PRICE_TEMPLATE("BATCH_IMPORT_MR_PRICE_TEMPLATE", "批量导入市调价格模板"),

    /**
     * 医药需求
     */
    MEDICINE_TENANT_SKU_UPDATE_TEMPLATE("MEDICINE_TENANT_SKU_UPDATE_TEMPLATE", "商品主档批量修改模板"),
    MEDICINE_TENANT_ADD_SKU_TEMPLATE("MEDICINE_TENANT_ADD_SKU_TEMPLATE", "医药商品主档批量导入模板"),
    MEDICINE_POI_ADD_SKU_TEMPLATE("MEDICINE_POI_ADD_SKU_TEMPLATE", "医药门店商品批量导入模板"),


    /**
     * 库存
     **/
    STOCK_WARNING_THRESHOLD_UPDATE_TEMPLATE("STOCK_WARNING_THRESHOLD_UPDATE_TEMPLATE", "商品主档批量修改库存预警值模板"),

    /**
     * 批量修改商品无限库存标记
     */
    UN_LIMITED_UPDATE_TEMPLATE("UN_LIMITED_UPDATE_TEMPLATE", "商品主档批量修改无限库存标记模板"),

    BATCH_POI_STOCK_UPDATE_TEMPLATE("BATCH_POI_STOCK_UPDATE_TEMPLATE", "门店批量更新线上库存模版"),

    /**
     * 按单收货导入模版
     */
    BATCH_IMPORT_SCM_DELIVERY_TASK_TEMPLATE("BATCH_IMPORT_SCM_DELIVERY_TASK_TEMPLATE", "按单收货批量导入模版"),
    BATCH_IMPORT_SCM_DELIVERY_TASK_TEMPLATE_V2("BATCH_IMPORT_SCM_DELIVERY_TASK_TEMPLATE_V2", "按单收货批量导入模版V2"),

    /**
     * 采购计划导入模版
     */
    BATCH_IMPORT_PURCHASE_PLAN_TEMPLATE("BATCH_IMPORT_PURCHASE_PLAN_TEMPLATE", "采购计划导入模版"),

    /**
     * 调拨单导入模版
     */
    BATCH_IMPORT_ALLOCATION_ORDER_TASK_TEMPLATE("BATCH_IMPORT_ALLOCATION_ORDER_TASK_TEMPLATE", "调拨单导入模版"),

    /**
     * 采购单导入模版
     */
    BATCH_UPDATE_PURCHASE_RECEIPT_TEMPLATE("BATCH_UPDATE_PURCHASE_RECEIPT_TEMPLATE", "采购单导入模版"),

    PRICE_WORKTILE_IMPORT_SKU_TEMPLATE("PRICE_WORKTILE_IMPORT_SKU_TEMPLATE", "定价工作台导入商品和成本价"),

    BATCH_IMPORT_STORAGE_BIN(TaskTypeEnum.BATCH_IMPORT_STORAGE_BIN.name(), TaskTypeEnum.BATCH_IMPORT_STORAGE_BIN.getDesc()),


    BATCH_IMPORT_REPLENISH_SETTING_TEMPLATE("BATCH_IMPORT_REPLENISH_SETTING_TEMPLATE", "导入补货设置模板"),

    BATCH_IMPORT_REPLENISH_SETTING_TEMPLATE_FOR_REGIONAL_WAREHOUSE("BATCH_IMPORT_REPLENISH_SETTING_TEMPLATE_FOR_REGIONAL_WAREHOUSE", "导入补货设置模板(中心仓)"),

    BATCH_IMPORT_STOCK_ADJUSTMENT_TEMPLATE("BATCH_IMPORT_STOCK_ADJUSTMENT_TEMPLATE", "库存调整批量导入模板"),
    BATCH_IMPORT_STOCK_ADJUSTMENT_TEMPLATE_V2("BATCH_IMPORT_STOCK_ADJUSTMENT_TEMPLATE_V2", "批次且一品多位库存调整批量导入模板"),

    BATCH_IMPORT_LOCATION_SORT_TEMPLATE("BATCH_IMPORT_LOCATION_SORT_TEMPLATE", "库位顺序初始化模板"),

    /**
     * 三维库导出模板
     */
    UWMS_THREE_DIMENSIONAL_TEMPLATE("UWMS_THREE_DIMENSIONAL_TEMPLATE", "三维库批量查询模板"),

    /**
     * 批量修改采购模式模版
     */
    BATCH_IMPORT_PURCHASE_SKU_TYPE_TEMPLATE("BATCH_IMPORT_PURCHASE_SKU_TYPE_TEMPLATE", "批量修改采购模式模板"),

    /**
     * 批量修改采购状态模版
     */
    BATCH_IMPORT_PURCHASE_SKU_STATUS_TEMPLATE("BATCH_IMPORT_PURCHASE_SKU_STATUS_TEMPLATE", "批量修改采购状态模板"),
    /**
     * 批量修改采购状态模版 中心仓
     */
    BATCH_IMPORT_PURCHASE_SKU_STATUS_TEMPLATE_FOR_REGIONAL_WAREHOUSE("BATCH_IMPORT_PURCHASE_SKU_STATUS_TEMPLATE_FOR_REGIONAL_WAREHOUSE", "批量修改采购状态模板(中心仓)"),
    /**
     * 批量修改到货天数模版
     */
    BATCH_UPDATE_SUPPLIER_ARRIVAL_DAYS("BATCH_UPDATE_SUPPLIER_ARRIVAL_DAYS_TEMPLATE", "批量修改到货天数模板"),
    /**
     * 批量修改送货方式模版
     */
    BATCH_UPDATE_PURCHASE_SKU_DISTRIBUTE_TYPE("BATCH_UPDATE_PURCHASE_SKU_DISTRIBUTE_TYPE", "批量修改送货方式模板"),
    /**
     * 店仓补货导入添加补货商品模版
     */
    BATCH_UPSERT_REPLENISH_LIST_SKU_TYPE("BATCH_UPSERT_REPLENISH_LIST_SKU_TYPE", "店仓补货导入添加补货商品模版"),

    /**
     * 中心仓补货导入添加补货商品模版
     */
    REGIONAL_WAREHOUSE_BATCH_UPSERT_REPLENISH_LIST_SKU_TYPE("REGIONAL_WAREHOUSE_BATCH_UPSERT_REPLENISH_LIST_SKU_TYPE", "店仓补货导入添加补货商品模版"),


    BATCH_CRATE_SUPPLIER("BATCH_CRATE_SUPPLIER","批量创建供应商模板"),


    /**
     * 批量导入筛选商品
     */
    BATCH_IMPORT_SPU_ERP_ADD("SPU_ERP_ADD","批量导入筛选商品"),
    /**
     * 新增商品条码导入
     */
    BATCH_IMPORT_SPU_ERP_ADD_BY_UPC("SPU_ERP_ADD_BY_UPC","新增商品条码导入"),
    /**
     * 商品主档修改导入
     */
    BATCH_IMPORT_SPU_ERP_UPDATE("SPU_ERP_UPDATE","商品主档修改导入"),
    /**
     * 商品售卖状态导入
     */
    BATCH_IMPORT_SALE_STATUS_UPDATE("SALE_STATUS_UPDATE","商品售卖状态导入"),
    /**
     * 商品图导入
     */
    BATCH_IMPORT_SPU_PICTURE("SPU_PICTURE","商品图导入"),
    /**
     * 商品详情图导入
     */
    BATCH_IMPORT_SPU_CONTENT_PICTURE("SPU_CONTENT_PICTURE","商品详情图导入"),
    /**
     * 匹配商品资料库导入
     */
    BATCH_IMPORT_SPU_ADD_BY_STANDARD("SPU_ADD_BY_STANDARD", "匹配商品资料库导入"),
    /**
     * 删除商品
     */
    BATCH_IMPORT_SPU_DELETE("SPU_DELETE", "删除商品"),
    /**
     * 导入单位转换系数
     */
    IMPORT_UNIT_CONVERT_FACTOR("UNIT_CONVERT_FACTOR","商品主档-Excel导入单位转换系数"),
    IMPORT_STORE_SPU_BUSINESS_CHANNEL("IMPORT_STORE_SPU_BUSINESS_CHANNEL","门店经营渠道导入"),
    IMPORT_STORE_SPU_PRICE_ADJUST_RATE("IMPORT_STORE_SPU_PRICE_ADJUST_RATE","门店商品加价率导入"),
    IMPORT_STORE_SPU_STATUS("IMPORT_STORE_SPU_STATUS","门店商品状态导入"),
    IMPORT_STORE_SPU_PLU("IMPORT_STORE_SPU_PLU","门店商品PLU导入"),
    IMPORT_STORE_SPU_PRICE_AND_STOCK("IMPORT_STORE_SPU_PRICE_AND_STOCK","门店商品库存价格导入"),
    IMPORT_STORE_SPU_SELL_OUT_ON_SHELF("IMPORT_STORE_SPU_SELL_OUT_ON_SHELF","门店商品售罄下架状态导入"),
    /**
     * 批量导入上下架计划
     */
    BATCH_IMPORT_AUTO_SHELF("BATCH_IMPORT_AUTO_SHELF", "批量导入上下架计划", "上下架计划商品导入模版_2023.xlsx",
            "上下架计划商品导入模版", true,
            ProductTaskTypeEnum.AUTO_SHELF_IMPORT.getType()),
    /**
     * 批量导出上下架计划
     */
    BATCH_EXPORT_AUTO_SHELF("BATCH_EXPORT_AUTO_SHELF", "批量导出上下架计划"),

    /**
     * 预留库存导入
     */
    SAFE_STOCK_IMPORT("SAFE_STOCK_IMPORT","商品安全库存导入"),
    /**
     * 批量导入渠道黑名单
     */
    BATCH_IMPORT_CHANNEL_BLACK("BATCH_IMPORT_CHANNEL_BLACK", "批量导入渠道黑名单", "商品渠道黑名单导入模板_2023.xlsx", "商品渠道黑名单导入模板", true,
            ProductTaskTypeEnum.CHANNEL_BLACK_IMPORT.getType()),
    /**
     * 批量导出渠道黑名单
     */
    BATCH_EXPORT_CHANNEL_BLACK("BATCH_EXPORT_CHANNEL_BLACK", "批量导出禁售渠道"),

    BATCH_IMPORT_CREATE_DISTRIBUTE_ORDER("BATCH_IMPORT_DISTRIBUTION_ORDER_TASK_TEMPLATE", "批量创建配销单"),
    /**
     * 促销导入模版
     */
    BATCH_IMPORT_ADD_DISCOUNT_ITEM_TEMPLATE("BATCH_IMPORT_ADD_DISCOUNT_ITEM_TEMPLATE", "批量新增-单品直降活动商品模版"),
    BATCH_IMPORT_ADD_CASH_BACK_ITEM_TEMPLATE("BATCH_IMPORT_ADD_CASH_BACK_ITEM_TEMPLATE", "批量新增-满减活动商品模版"),
    BATCH_IMPORT_ADD_FLASH_SALE_ITEM_TEMPLATE("BATCH_IMPORT_ADD_FLASH_SALE_ITEM_TEMPLATE", "批量新增-秒杀活动商品模版"),
    BATCH_IMPORT_CANCEL_CHANNEL_ITEM_TEMPLATE("BATCH_IMPORT_CANCEL_CHANNEL_ITEM_TEMPLATE", "批量导出-渠道活动商品模版"),


    IMPORT_POI_BAG("IMPORT_POI_BAG","购物袋导入"),

    /**
     * 批量同步总部商品到渠道
     */
    MERCHANT_SPU_BATCH_SYNC_CHANNEL("MERCHANT_SPU_BATCH_SYNC_CHANNEL", "总部商品-批量同步到渠道模板"),

    /**
     * 批量同步门店商品到渠道
     */
    STORE_SPU_BATCH_SYNC_CHANNEL("STORE_SPU_BATCH_SYNC_CHANNEL", "门店商品-批量同步到渠道模板"),

    /**
     * 批量添加图片水印
     */
    MERCHANT_SPU_BATCH_ADD_WATER_MARK("MERCHANT_SPU_BATCH_ADD_WATER_MARK", "商品主档-批量添加图片水印"),

    /**
     * 批量导入门店安全库存
     */
    ERP_STORE_STOCK_CONFIG_IMPORT_TEMPLATE("ERP_STORE_STOCK_CONFIG_IMPORT_TEMPLATE", "批量导入门店安全库存模板"),
    ;

    private String type;

    private String desc;

    private String remoteFileName;

    private String localFileName;

    private boolean needCheckTemplate;

    private int taskType;

    ImportTemplateTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static ImportTemplateTypeEnum getByType(String type) {
        for (ImportTemplateTypeEnum e : ImportTemplateTypeEnum.values()) {
            if (StringUtils.equalsIgnoreCase(e.getType(), type)) {
                return e;
            }
        }

        return null;
    }


    public static ImportTemplateTypeEnum getByTaskType(int taskType) {
        for (ImportTemplateTypeEnum e : ImportTemplateTypeEnum.values()) {
            if (e.getTaskType() == taskType) {
                return e;
            }
        }

        return null;
    }


}
