package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.common.enums.YesNoEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.ResourceInfoVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/25
 * 权限的业务对象
 **/
@Data
public class AuthBo {

    /**
     * 权限ID
     */
    private Long authId;

    /**
     * 授权ID,账号授权时需要传递
     */
    private Long permissionId;

    /**
     * 权限编码
     */
    private String authCode;

    /**
     * 权限类型
     *
     */
    private Integer authType;

    /**
     * 权限名称
     */
    private String authName;

    /**
     * 资源url
     */
    private String url;

    /**
     * 是否关联
     */
    private Integer isRel;


    /**
     * 下级权限列表
     */
    private List<AuthBo> subAuthList = Lists.newArrayList();


    public AuthBo build(ResourceInfoVo vo) {
        this.authId = vo.getId();
        this.authCode = vo.getCode();
        this.authType = vo.getType();
        this.authName = vo.getName();
        this.url = vo.getUrl();
        this.isRel = YesNoEnum.NO.getKey();
        return this;
    }

    public AuthBo build(Long permissionId) {
        this.permissionId = permissionId;
        return this;
    }


}
