package com.sankuai.shangou.qnh.orderapi.utils.app;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.enums.ChannelOrderStatus;
import com.sankuai.shangou.qnh.orderapi.constant.app.MiniAppConstants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.dianping.lion.client.Lion.getConfigRepository;

/**
 * @program: qnh_order_api
 * @description:
 * @author: jinyi
 * @create: 2023-10-23 17:26
 **/
@Slf4j
public class MccConfigUtil {


    /**
     * 一个订单是否展示多条售后
     *
     * @return
     */
    public static boolean isShowWaitAuditRefundList() {
        return Lion.getConfigRepository().getBooleanValue("show.wait.audit.refund.list", false);
    }



    public static boolean useSacAuthenticationV2() {
        return Lion.getConfigRepository().getBooleanValue("useSacAuthentication2", false);
    }


    /**
     * 歪马送酒租户id
     * @return
     */
    public static List<String> getDHTenantIdList(){
        String tenantIdStr = Lion.getConfigRepository().get("drunk.horse.pieapi.tenant.list","1000395");
        if(org.apache.commons.lang3.StringUtils.isEmpty(tenantIdStr)){
            return Collections.emptyList();
        }
        return Splitter.on(",").splitToList(tenantIdStr);
    }

    /**
     * 订单多门店模式-查询配送异常订单数量时，调tms查询异常订单次数上限
     * @return
     */
    public static int getOrderDeliveryErrorBySubTypeQueryTmsRoundLimit() {
        return Lion.getConfigRepository().getIntValue("order.deliveryError.multiStore.queryTmsRound.limit",5);
    }

    /**
     * 获取axb隐私号灰度门店wmPoiId -- 默认测试门店
     *
     * @return 灰度门店列表
     */
    public static Set<String> getAxBPrivacyPhonePoiIds() {
        String configStr = Lion.getConfigRepository().get("axb.privacy.phone.poiIds", "730620");
        if (StringUtils.isEmpty(configStr)) {
            return new HashSet<>();
        }
        String[] poiIds = configStr.split(",");
        return new HashSet<>(Arrays.asList(poiIds));
    }

    /**
     * 是否全部使用axb隐私号 -- 默认false
     *
     * @return .
     */
    public static boolean isAllUserAxBPrivacyPhone() {
        return Lion.getConfigRepository().getBooleanValue("axb.privacy.phone.switch", false);
    }


    /**
     * 获取使用AXB隐私号的租户ID
     *
     * @return .
     */
    public static Set<String> getAxbPrivacyPhoneTenantIds() {
        String configStr = Lion.getConfigRepository().get("axb.privacy.phone.tenantIds", "1001197");
        if (StringUtils.isEmpty(configStr)) {
            return new HashSet<>();
        }
        String[] tenantIds = configStr.split(",");
        return new HashSet<>(Arrays.asList(tenantIds));
    }




    public static boolean isDrunkHorseTenant(Long tenantId){
        try {
            String tenantIdStr = Lion.getConfigRepository("com.sankuai.shangou.empower.orderbiz").get("drunk_horse_tenant");
            if (StringUtils.isBlank(tenantIdStr) || tenantId == null) {
                return false;
            }
            Set<Long> tenantIdSet = Splitter.on(",")
                    .trimResults()
                    .splitToList(tenantIdStr).stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toSet());
            return tenantIdSet.contains(tenantId);
        }catch (Exception e){
            log.error("isDrunkHorseTenant error",e);
        }
        return false;
    }


    public static boolean isShowGoodsItemListGrayStore(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.shangou.supplychain.ofapp").getList("show.goods.item.list.gray.store.ids", Long.class, Collections.emptyList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(-1L, grayStoreIds.get(0))) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static Map<Integer,Integer> getPlatformToSelfConfig(){
        String content = Lion.getConfigRepository().get("delivery_platform_to_self_config", "100=15|300=10");
        if(StringUtils.isEmpty(content)){
            return Collections.emptyMap();
        }
        List<String> strList=Splitter.on("|").splitToList(content);
        if(CollectionUtils.isEmpty(strList)){
            return Collections.emptyMap();
        }
        Map<Integer,Integer> configMap = new HashMap<>();
        strList.forEach(str->{
            List<String> dataList = Splitter.on("=").splitToList(str);
            if(CollectionUtils.isEmpty(dataList) || dataList.size()!=2){
                return;
            }
            configMap.put(Integer.parseInt(dataList.get(0)),Integer.parseInt(dataList.get(1)));
        });
        return configMap;
    }

    public static boolean isShowDeliveryOperateItem() {
        return Lion.getConfigRepository().getBooleanValue("is_show_delivery_operate_item", false);
    }



    public static Integer getQueryOrderListBatchSize() {
        return Lion.getConfigRepository().getIntValue("query.order.list.batch.size", 30);
    }

    public static Integer getMaxActualPayAmtForThirdDelivery() {
        return Lion.getConfigRepository().getIntValue("max.actual.pay.third.delivery", 150 * 100);
    }

    public static List<String> getDhTurnAggLimitSceneList() {
        try {
            return getConfigRepository().getList("dh.turn.agg.limit.scene", String.class, Lists.newArrayList("餐馆"));
        } catch (Exception e) {
            log.error("isDhScenePoi error", e);
            return Lists.newArrayList();
        }
    }

    public static boolean isDHAggDeliveryGrayStore(long poiId) {
        try {
            List<Long> grayPoiList = Lion.getConfigRepository("com.sankuai.sgfulfillment.tms").getList("dh.agg.delivery.gray", Long.class);
            //-1则全量
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(grayPoiList) && grayPoiList.size() == 1 && grayPoiList.get(0).equals(-1L)) {
                return true;
            }
            return Optional.ofNullable(grayPoiList).orElse(Lists.newArrayList()).contains(poiId);
        } catch (Exception e) {
            log.error("isDHAggDeliveryGrayStore error", e);
            return false;
        }

    }

    public static boolean showDeliveryOperateItemSwitch() {
        return Lion.getConfigRepository().getBooleanValue("show_delivery_operate_item", true);
    }

    public static boolean getinterceptor_enable() {
        return Lion.getConfigRepository().getBooleanValue("interceptor_enable", true);
    }

    /**
     * 报错文案配置
     *
     * @return
     */
    public static String getErrorInformation() {
        return Lion.getConfigRepository().get("errmsg_standard_config", "系统维护中，请22:00点之后再进行操作，如有疑问请咨询您的渠道经理。");
    }

    public static String getOrderOperatorAuthCodes() {
        return Lion.getConfigRepository().get("app.order.operator.auth.codes");
    }

    public static String getOrderOcmsMigrationShopId() {
        return Lion.getConfigRepository().get("order.ocms.migration.shopId");
    }

    public static String getOrderOcmsMigrationTenantId() {
        return Lion.getConfigRepository().get("order.ocms.migration.tenantId");
    }

    public static boolean getOrderOcmsMigrationSwitch() {
        return Lion.getConfigRepository().getBooleanValue("order.ocms.migration.switch", false);
    }

    public static boolean isRpcParallel(){
        return Lion.getConfigRepository().getBooleanValue("rpc.parallel",false);
    }

    public static boolean getOrderTabAppendTmsDeliveryInfoSwitch() {
        return Lion.getConfigRepository().getBooleanValue("order.tab.append.tmsDeliveryInfo.switch", true);
    }

    public static boolean getDeliveryOperateItemRetry() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.retry", true);
    }

    public static boolean getDeliveryOperateItemSelfDelivery() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.selfDelivery", true);
    }

    public static boolean getDeliveryOperateItemExceptionToThird() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.exceptionToThird", true);
    }

    public static boolean getDeliveryOperateItemManualThird() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.manualThird", true);
    }

    public static boolean getDeliveryOperateItemCanCancel() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.canCancel", true);
    }

    public static boolean getDeliveryOperateItemCanceling() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.canceling", true);
    }

    public static boolean getDeliveryOperateItemRetryByMaltfarm() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.retryByMaltfarm", true);
    }

    public static boolean getDeliveryOperateItemRetryByHaiKui() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.retryByHaiKui", true);
    }

    public static String getErrorMessageByResultCode(int code, String defaultMessage){
        return Lion.getConfigRepository().get("resultCode." + code, defaultMessage);
    }

    public static int queryOrderCreateTimeBefore(){
        return Lion.getConfigRepository().getIntValue("query.order.create.time.before",7);
    }

    public static int queryAfsOrderCreateTimeMonthBefore(){
        return Lion.getConfigRepository().getIntValue("query.afs.order.create.time.month.before",2);
    }

    public static String getOrderPrintTimeoutTips(){
        return Lion.getConfigRepository().get("order.print.timeout.tips", "打印超时，若未出票请检查打印机状态后重试");
    }

    public static String getOrderPrintFailTips(){
        return Lion.getConfigRepository().get("order.print.fail.tips", "打印失败，请检查打印机状态后重试");
    }

    public static boolean getSelfFetchCodeToLowercase() {
        return Lion.getConfigRepository().getBooleanValue("self_fetch_code_to_lowercase", false);
    }

    public static String getTenantWeightRefundProportionLimit(){
        return Lion.getConfigRepository().get("tenant_weight_refund_proportion_limit","");
    }

    public static Integer getAppLocalCacheDayLength(){
        return Lion.getConfigRepository().getIntValue("app_local_cache_day_length",7);
    }

    public static Integer getAppLocalCacheMaxDayLength(){
        return Lion.getConfigRepository().getIntValue("app_local_cache_max_day_length",7);
    }


    public static int getMiniAppOrderSearchMaximumDay(){
        return Lion.getConfigRepository().getIntValue(MiniAppConstants.ORDER_SEARCH_MAXIMUM_DAY, MiniAppConstants.ORDER_SEARCH_DEFAULT_MAXIMUM_DAY);
    }

    public static int getMiniAppOrderMaxStoreSize(){
        return Lion.getConfigRepository().getIntValue(MiniAppConstants.DEFAULT_STORE_SIZE_CONFIG, 50);
    }

    public static int getMiniAppOrderListBeginTime(){
       return Lion.getConfigRepository().getIntValue(MiniAppConstants.DEFAULT_TIME_CONFIG, 7);
    }

    /**
     * 获取并行查询最大等待时间
     *
     * @return
     */
    public static int getParallelQueryAwaitTime() {
        try{
            return Lion.getConfigRepository().getIntValue("api_parallel_await_mill_second", 200);
        }catch (Exception e){
            log.error("getParallelQueryAwaitTime error", e);
            return 200;
        }
    }

    public static boolean getPickQuerySwitch(){
        return Lion.getConfigRepository().getBooleanValue("pickselect.query.switch", true);
    }

    public static boolean isNotRecommendTransferOrderTenant(Long tenantId) {
        List<String> tenantList =  Lion.getConfigRepository().getList("transfer_order_not_recommend_tenant", String.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(tenantList)) {
            return false;
        }

        return tenantList.contains(String.valueOf(tenantId));
    }

    public static Boolean isPickPhotoConfigurableGrayStore(Long storeId) {
        if (storeId == null) {
            return false;
        }
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.waimai.sc.pickselectservice").getList("pick.photo.configurable.gray.store", Long.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && grayStoreIds.contains(-1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static Integer getShortExpirationThreshold() {
        return Lion.getConfigRepository().getIntValue("short.expiration.threshold", 90);
    }

    public static List<String> getOpenOrderMccKeys() {
        String keys = Lion.getConfigRepository().get("open_order_mcc_config_key_list","");
        if(org.apache.commons.lang3.StringUtils.isEmpty(keys)){
            return Collections.emptyList();
        }
        return Splitter.on(",").splitToList(keys);
    }

    public static Boolean isHighPriceTagGrayStore(Long storeId) {
        if (storeId == null) {
            return false;
        }
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.sgshopmgmt.empower.pieapi")
                .getList("high.price.tag.gray.store", Long.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && grayStoreIds.contains(-1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static HighPriceTagConfig getHighPriceTagConfig() {
        String jsonStr = getConfigRepository("com.sankuai.sgshopmgmt.empower.pieapi").get("high.price.tag.pair");
        return JSON.parseObject(jsonStr, HighPriceTagConfig.class);
    }

    /**
     * 订单显示拣货员姓名开关
     */
    public static Boolean getOrderShowPickerNamesSwitch (){
        return Lion.getConfigRepository().getBooleanValue("order.show.picker.names.swtich", true);
    }

    /**
     *
     * @param tenantId
     * @return
     */
    public static Boolean showPrintTimesSwitch(Optional<Long> tenantId) {
        try {
            if (!tenantId.isPresent()) {
                return false;
            }
            String tenantIdList = Lion.getConfigRepository().get("show.print.times.switch", "all");
            if (org.apache.commons.lang3.StringUtils.isEmpty(tenantIdList)) {
                return false;
            } else if ("all".equalsIgnoreCase(tenantIdList)) {
                return true;
            } else {
                return Arrays.stream(tenantIdList.split(",")).collect(Collectors.toList()).contains(String.valueOf(tenantId.get()));
            }

        } catch (Exception e) {
            log.error("get showPrintTimesSwitch error", e);
            return false;
        }
    }


    public static boolean getGrayWiderShippingAreaStores(long storeId) {
        List<Long> grayStores = getConfigRepository("com.sankuai.shangou.supplychain.ofapp").getList("wider.shipping.area.stores", Long.class);
        if (CollectionUtils.isNotEmpty(grayStores) && grayStores.size() == 1 && grayStores.get(0).equals(-1L)) {
            return true;
        }
        return grayStores.contains(storeId);
    }

    public static String getWiderShippingAreaHint() {
        return getConfigRepository("com.sankuai.shangou.supplychain.ofapp").get("wider.shipping.area.hint", "大范围");
    }

    public static boolean hitNewOrderApiGrayTenant(Long tenantId) {
        try {
            List<Long> tenantIds = Lion.getConfigRepository().getList("api.performance.promotion.switch", Long.class);
            if (CollectionUtils.isEmpty(tenantIds)) {
                return false;
            }
            return tenantIds.contains(tenantId) || tenantIds.contains(-1L);
        }catch (Exception e){
            return false;
        }
    }

    public static boolean hitAppOrderListNotFillDeliveryUrlTenant(Long tenantId) {
        try {
            List<Long> tenantIds = Lion.getConfigRepository().getList("api.orderlist.notfill.deliveryurl.tenant", Long.class);
            if (CollectionUtils.isEmpty(tenantIds)) {
                return false;
            }
            return tenantIds.contains(tenantId) || tenantIds.contains(-1L);
        }catch (Exception e){
            return false;
        }
    }

    public static boolean hitNewOrderApiParallelGrayTenant(Long tenantId) {
        try {
            List<Long> tenantIds = Lion.getConfigRepository().getList("api.performance.parallel.switch", Long.class);
            if (CollectionUtils.isEmpty(tenantIds)) {
                return false;
            }
            return tenantIds.contains(tenantId) || tenantIds.contains(-1L);
        }catch (Exception e){
            return false;
        }
    }

    public static boolean resetParallelSwitch() {
        try {
            return Lion.getConfigRepository().getBooleanValue("reset.api.parallel.switch", false);
        }catch (Exception e){
            return false;
        }
    }

    public static boolean hitPickCompleteWithNewApiGrayTenant(Long tenantId) {
        try {
            List<Long> tenantIds = Lion.getConfigRepository().getList("pick.complete.new.api", Long.class);
            if (CollectionUtils.isEmpty(tenantIds)) {
                return false;
            }
            return tenantIds.contains(tenantId) || tenantIds.contains(-1L);
        }catch (Exception e){
            return false;
        }
    }


    public static boolean orderListNotReturnDeliveryUrl(Long tenantId){
        if (MccConfigUtil.isDrunkHorseTenant(tenantId)){
            return false;
        }
        return hitAppOrderListNotFillDeliveryUrlTenant(tenantId);
    }

    /**
     * 是否为歪马加盟费率的灰度门店
     * @return
     */
    public static Boolean checkIsFranchiseeFeePoi(Long tenantId, Long storeId) {
        if (!isDrunkHorseTenant(tenantId)) {
            return false;
        }
        try {
            List<Long> stores = Lion.getConfigRepository("com.sankuai.shangou.empower.orderbiz").getList("franchisee.fee.gray.stores", Long.class, com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists.newArrayList());
            if (CollectionUtils.isEmpty(stores)) {
                return false;
            }
            //全量逻辑
            if (stores.size() == 1 && stores.get(0).equals(-1L)) {
                return true;
            }
            return stores.contains(storeId);
        } catch (Exception e) {
            log.error("getDHByPositionSwitch error", e);
            return false;
        }
    }


    @Data
    public static class HighPriceTagConfig {
        private String tagCode;

        private String tagValueCode;
    }

    /**
     * 获取订单模块支持多门店模式的租户业务模式
     */
    public static List<String> getOrderSupportMultiStoreBizModes() {
        return Lion.getConfigRepository().getList("order_support_multi_store_tenant_biz_modes", String.class);
    }

    public static String getTxdRefundAuditErrorMessage() {
        return Lion.getConfigRepository().get("config.txd.refundAuditErrorMessage",
                "淘鲜达渠道规则限制，订单完成前顾客发起售后，无需商家审核，门店发货或者骑手配送任意一方拦截成功则平台自动通过，退单状态会根据拦截结果自动流转。");
    }

    public static boolean needAuditRefund(int status) {
        List<Integer> needAuditRefundStatus = getConfigRepository().getList("config.txd.needAuditRefundOrderStatus",
                Integer.class, Lists.newArrayList(ChannelOrderStatus.FINISHED.getValue()));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(needAuditRefundStatus)) {
            return false;
        }
        return needAuditRefundStatus.contains(status);
    }

    /**
     * 获取订单模块支持多门店模式的租户业务模式
     */
    public static List<String> getNewOrderServiceNotSupportBizModes() {
        return Lion.getConfigRepository().getList("order_new_service_not_support_tenant_biz_modes", String.class);
    }

    /**
     * 获取订单模块多门店模式需屏蔽的操作项
     */
    public static List<Integer> getOrderMultiStoreModeBlockOperateItems() {
        return Lion.getConfigRepository().getList("order_multi_store_block_op_items", Integer.class);
    }

    /**
     * 查询租户使用使用【queryNetProfitV2】查询毛利
     *
     * @param tenantId
     * @return
     */
    public static boolean isQueryNetProfitV2(Long tenantId) {
        List<Long> tenantList = getConfigRepository().getList("crmData.queryNetProfitV2.tenantIds", Long.class,
                Collections.emptyList());
        if (CollectionUtils.isEmpty(tenantList)) {
            return false;
        }
        // 配置为-1，则全量开关
        if (tenantList.contains(-1L)) {
            return true;
        }
        return tenantList.contains(tenantId);
    }
    /**
     * 订单-待拣货界面排序方式，为true代表从前端指定的规则排序
     */
    public static boolean isWaitPickSortingRuleByParam() {
        try{
            return Lion.getConfigRepository("com.sankuai.qnh.fulfill.pickquery").getBooleanValue("wait.pick.sorting.by.param", true);
        }catch (Exception e){
            log.warn("isWaitPickSortingRuleByParam error", e);
            return true;
        }
    }

    public static List<String> getWineBottleOpenerSkuIds() {
        return Lion.getConfigRepository("com.sankuai.shangou.supplychain.ofapp").getList("wine.bottle.opener.sku.ids", String.class, Collections.singletonList("70013100302"));
    }

    public static boolean isPickDeliverySplitOptimizeGrayStore(long poiId) {
        try {
            List<Long> grayPoiList = Lion.getConfigRepository().getList("pick.delivery.optimize.gray", Long.class);
            //-1则全量
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(grayPoiList) && grayPoiList.size() == 1 && grayPoiList.get(0).equals(-1L)) {
                return true;
            }
            return Optional.ofNullable(grayPoiList).orElse(Lists.newArrayList()).contains(poiId);
        } catch (Exception e) {
            log.error("isDHAggDeliveryGrayStore error", e);
            return false;
        }

    }

    /**
     * 青云配送订单详情无授权链接开关
     */
    public static boolean dapLinkNoAuthSwitch() {
        return Lion.getConfigRepository().getBooleanValue("dap.order.link.no.auth.switch", true);
    }

    /**
     * pda青云配送订单详情无授权链接开关
     */
    public static boolean dapLinkNoAuthPdaSwitch() {
        return Lion.getConfigRepository().getBooleanValue("dap.order.link.no.auth.pda.switch", false);
    }

    /**
     * 麦芽田配送订单详情无授权链接开关
     */
    public static boolean maltLinkNoAuthSwitch() {
        return Lion.getConfigRepository().getBooleanValue("malt.order.link.no.auth.switch", true);
    }

    /**
     * 是否是支持脱敏收货人信息的租户
     * @param tenantId 租户ID
     * @return true 是  false否
     */
    public static boolean isSupportDesensitizeReceiverInfoTenants(Long tenantId) {
        String configTenantIds = getConfigRepository().get("support_desensitize_receiver_info_tenants", "");
        // 配置的租户列表第一个为ALL, 表示全量
        List<String> tenantIds = Lists.newArrayList(StringUtils.split(configTenantIds, ","));
        if (CollectionUtils.isNotEmpty(tenantIds) && tenantIds.get(0).equalsIgnoreCase("ALL")) {
            return true;
        }
        // 配置的租户列表包含当前租户, 表示当前租户执行灰度逻辑
        return tenantIds.contains(String.valueOf(tenantId));
    }

    public static boolean getFusionGaryV1(Long tenantId,Long storeId){
        List<Long> grayTenantIdList = Lion.getConfigRepository("com.sankuai.shangou.supplychain.ofapp").getList("fusion.gary.v1.tenant.list",Long.class,Collections.emptyList());
        if(tenantId!=null && CollectionUtils.isNotEmpty(grayTenantIdList)){
            if(grayTenantIdList.contains(-1L) || grayTenantIdList.contains(tenantId)){
                return true;
            }
        }
        List<Long> grayStoreIdList = Lion.getConfigRepository("com.sankuai.shangou.supplychain.ofapp").getList("fusion.gary.v1.store.list",Long.class,Collections.emptyList());
        if(storeId!=null && CollectionUtils.isNotEmpty(grayStoreIdList)){
            return grayStoreIdList.contains(-1L) || grayStoreIdList.contains(storeId);
        }
        return false;
    }
}
