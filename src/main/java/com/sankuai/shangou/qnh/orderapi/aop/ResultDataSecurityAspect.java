package com.sankuai.shangou.qnh.orderapi.aop;

import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.shangou.qnh.orderapi.domain.bo.ReturnDataPoiBo;
import com.sankuai.shangou.qnh.orderapi.domain.result.ReturnDataSecurity;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Result;
import com.sankuai.shangou.qnh.orderapi.remote.AuthRemoteService;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 返回结果数据校验的aop
 */
@Component
@Aspect
@Slf4j
public class ResultDataSecurityAspect {

    private final ResultHandlerManager resultHandlerManager = new ResultHandlerManager();

    @Resource
    private AuthRemoteService authRemoteService;

    @Around("@annotation(com.sankuai.shangou.qnh.orderapi.annotation.ResultDataSecurity)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 执行目标方法
        Object result = joinPoint.proceed();
        if (Objects.isNull(result)) {
            return result;
        }
        // 获取当前会话信息，登录拦截器时进行初始化
        SessionInfo currentSession = SessionContext.getCurrentSession();
        // 开关控制是否进行返回结果校验
        if (Objects.isNull(currentSession) || !MccConfigUtil.resultDataSecuritySwitch(currentSession.getTenantId())) {
            return result;
        }
        // 获取返回类对应的处理类
        ResultHandler handler = resultHandlerManager.findHandler(result);
        if (Objects.isNull(handler)) {
            return result;
        }
        List<ReturnDataPoiBo> poiBoList = handler.getReturnDataPoiBoList(result);
        if (CollectionUtils.isEmpty(poiBoList)) {
            return result;
        }
        if (!hasResultDataPoiBoAuth(poiBoList, currentSession.getTenantId(), currentSession.getAccountId())) {
            return handler.getFailedResult(result);
        }
        return result;
    }

    /**
     * 校验账号是否有数据的门店权限或者转单门店权限
     *
     * @param poiBoList
     * @param tenantId
     * @param accountId
     * @return
     */
    public boolean hasResultDataPoiBoAuth(List<ReturnDataPoiBo> poiBoList, Long tenantId, Long accountId) {
        try {
            // 收集所有需要校验的门店ID
            List<Long> allPoiIds = poiBoList.stream().filter(bo -> Objects.nonNull(bo.getPoiId())).flatMap(bo -> {
                List<Long> ids = new ArrayList<>();
                ids.add(bo.getPoiId());
                Optional.ofNullable(bo.getDispatchShopId()).ifPresent(ids::add);
                return ids.stream();
            }).distinct().collect(Collectors.toList());
            // 门店数据为空，则默认为有权限
            if (CollectionUtils.isEmpty(allPoiIds)) {
                return true;
            }
            Map<Long, Boolean> authMap = authRemoteService.getAuthPoiDataAuth(allPoiIds, tenantId, accountId);
            if (MapUtils.isEmpty(authMap)) {
                log.warn("hasResultDataPoiBoAuth 权限校验失败 - 权限映射为空，tenantId: {}, accountId: {}, poiIds: {}", tenantId,
                        accountId, allPoiIds);
                return false;
            }
            // 校验权限
            return poiBoList.stream().filter(bo -> Objects.nonNull(bo.getPoiId())).allMatch(bo -> {
                boolean hasPoiAuth = BooleanUtils.isTrue(authMap.get(bo.getPoiId()));
                if (hasPoiAuth) {
                    return true;
                }
                // 无门店权限，则校验是否有转单门店权限
                boolean hasDispatchShopAuth = Objects.nonNull(bo.getDispatchShopId())
                        && BooleanUtils.isTrue(authMap.get(bo.getDispatchShopId()));
                if (!hasDispatchShopAuth) {
                    // 存在鉴权失败时，记录日志，便于排查问题，理论上不会太多
                    log.info("hasResultDataPoiBoAuth 数据鉴权失败 - poiId: {}, dispatchShopId: {}, authMap: {}",
                            bo.getPoiId(), bo.getDispatchShopId(), authMap);
                }
                return hasDispatchShopAuth;
            });
        } catch (Exception e) {
            log.error("hasResultDataPoiBoAuth tenantId:{}, accountId:{}, poiBoList:{}, error", tenantId, accountId,
                    poiBoList, e);
        }
        return true;
    }

    /**
     * 返回结果处理器
     */
    public class ResultHandlerManager {

        private final Map<Class<?>, ResultHandler> resultHandlerMap = new HashMap<>();

        public ResultHandlerManager() {
            // 注册策略
            resultHandlerMap.put(Result.class, new PcResultHandler());
        }

        public ResultHandler findHandler(Object result) {
            return resultHandlerMap.get(result.getClass());
        }
    }

    /**
     * 返回结果处理器
     */
    public interface ResultHandler {

        /**
         * 数据鉴权失败时响应 code
         */
        Integer dataAuthFailedCode = 403;

        /**
         * 数据鉴权失败时响应 message
         */
        String dataAuthFailedMessage = "数据鉴权失败，您没有该门店/仓库数据权限";

        /**
         * 获取返回结果中的门店和转单门店信息，后续对用户的门店和转单门店进行鉴权
         *
         * @param result
         * @return
         */
        List<ReturnDataPoiBo> getReturnDataPoiBoList(Object result);

        /**
         * 获取鉴权失败的返回结果
         * 
         * @param result
         * @return
         */
        Object getFailedResult(Object result);
    }

    /**
     * PC端返回结果类型（Result）处理器
     */
    public static class PcResultHandler implements ResultHandler {

        @Override
        public List<ReturnDataPoiBo> getReturnDataPoiBoList(Object result) {
            try {
                Object data = ((Result<?>)result).getData();
                if (Objects.isNull(data)) {
                    return null;
                }
                if (data instanceof ReturnDataSecurity) {
                    return ((ReturnDataSecurity)data).fetchReturnDataPoiBoList();
                }
            } catch (Exception e) {
                log.error("PcResultHandler.getReturnDataPoiBoList result:{} 异常", result, e);
            }
            return null;
        }

        @Override
        public Object getFailedResult(Object result) {
            // 返回失败结果
            return Result.fail(dataAuthFailedCode, dataAuthFailedMessage);
        }
    }
}
