package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/3/30 14:51
 * @Description:
 */
@Data
public class ConfirmOrderVO {

    private String poiName;

    private String channelId;

    private String channelName;

    private String orderId;

    private List<String> skuNames;

    private String orderAmt;

    private String orderType;

    private String beginTime;

    private String expectedTime;

    private String confirmStatus;

    private String pickStatus;

    private String pickFinishTime;


    private List<String> couldOperateItemList;

    private Long orderSerialNumber;

    private Long warehouseId;

    private String warehouseName;

    private Long shopId;

    /**
     * 是否为美团名酒馆订单，true：是
     */
    private Boolean isMtFamousTavern;

    /**
     * 是否为美团发财酒订单，true：是
     */
    private Boolean isFacaiWine;
}
