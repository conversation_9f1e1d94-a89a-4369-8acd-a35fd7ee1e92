package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.shangou.saas.order.management.client.dto.response.model.DhDeliverExtraInfoDTO;
import lombok.Data;

@Data
public class DhDeliveryExtraVO {

    private Long deliveryWeight;

    private Long deliveryDistance;

    public static DhDeliveryExtraVO buildDhDeliveryExtraVO(DhDeliverExtraInfoDTO orderOfflineDTO) {
        DhDeliveryExtraVO orderOfflineVO = new DhDeliveryExtraVO();
        if (orderOfflineDTO != null) {
            orderOfflineVO.setDeliveryWeight(orderOfflineDTO.getDeliveryWeight());
            orderOfflineVO.setDeliveryDistance(orderOfflineDTO.getDeliveryDistance());
        }
        return orderOfflineVO;
    }
}
