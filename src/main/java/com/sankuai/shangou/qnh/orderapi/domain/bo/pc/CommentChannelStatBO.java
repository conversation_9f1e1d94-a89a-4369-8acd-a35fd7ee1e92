package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentStatChannelDTO;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @class: CommentChannelStatBO
 * @date: 2019-08-05 19:31:23
 * @desc:
 */
@Data
public class CommentChannelStatBO {
    /**
     * 渠道Id
     */
    private Integer channelId;

    /**
     * 返回统计数据
     */
    List<CommentStatBO> channelCommentStatBOS;

    public CommentChannelStatBO(ChannelCommentStatChannelDTO dto, List<String> apiNeed) {
        this.channelId = dto.getChannelId();
        this.channelCommentStatBOS = dto.getCommentLabelStatDTOList().stream().filter(statDto -> apiNeed.contains(statDto.getCommentLabel())).map(CommentStatBO::new).collect(Collectors.toList());
    }

}
