package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 评价规则BO
 *
 * <AUTHOR>
 */
@TypeDoc(
        name = "评价规则BO",
        description = "评价规则BO"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CommentRuleBO {

    /**
     * 模板内容
     */
    private List<String> commentLevelRules;

}
