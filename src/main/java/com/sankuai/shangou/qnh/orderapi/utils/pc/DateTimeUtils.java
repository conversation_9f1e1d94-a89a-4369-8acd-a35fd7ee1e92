package com.sankuai.shangou.qnh.orderapi.utils.pc;

import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @since 2021/09/01
 */
public class DateTimeUtils {

    /**
     * 工程内通用的日期到字符串格式。
     */
    private static final DateTimeFormatter DATE_TIME_GENERAL_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取北京(上海)时区当前时间
     *
     * @return 时间
     */
    public static LocalDateTime now() {
        return LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
    }


    public static final long DAY_IN_MILLISECONDS = 1000 * 60 * 60 * 24L;

    /**
     * 转为毫秒时间戳。默认是中国时区。
     *
     * @param localDateTime
     * @return
     */
    public static Long toMillisecond(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
    }

    public static long getLastMillisecondWithAdd(LocalDateTime dateTime, Integer dateNum) {
        LocalDateTime plusDays = dateTime.plusDays(dateNum);
        return toMillisecond(LocalDateTime.of(plusDays.toLocalDate(), LocalTime.MAX));
    }

    public static long getStartMillisecondWithAdd(LocalDateTime dateTime, Integer dateNum) {
        LocalDateTime plusDays = dateTime.plusDays(dateNum);
        return toMillisecond(LocalDateTime.of(plusDays.toLocalDate(), LocalTime.MIN));
    }
    /**
     * 获取N月前的第一个时间戳
     * @param monthAgo
     * @return
     */
    public static Long getMonthAgoFirstDay(int monthAgo) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, -(monthAgo - 1));
        calendar.set(Calendar.DATE, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    /**
     * 对比inputDateStr是否比compareDate小，小于等于：true
     * @param inputDateStr
     * @param compareDateTime
     * @return
     */
    public static boolean compareDateForLower(String inputDateStr, Long compareDateTime) {
        if (StringUtils.isBlank(inputDateStr) || StringUtils.equals("null", inputDateStr) || compareDateTime == null) {
            return false;
        }

        if (Long.parseLong(inputDateStr) <= compareDateTime.longValue()) {
            return true;
        }
        return false;
    }

    /**
     * long类型时间戳转为yyyy-MM-dd HH:mm:ss格式的str
     */
    public static String getDateStrFromLong(Long time) {
        if (time == null) {
            return StringUtils.EMPTY;
        }
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(time));
    }
}
