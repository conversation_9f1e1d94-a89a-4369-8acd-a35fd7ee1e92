package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/9/17 21:50
 **/
@Data
public class PickItemVO {
    private Map<String, Integer> snCodeEnteringType;

    private String productName;

    private String attribute;

    private Integer shouldPickNum;

    private Integer stockoutEnteringType;

    private Boolean isSnProduct;

    private String skuId;

    private List<String> upcList;

    private Integer expiration;

    private String expirationUnit;

    private Boolean isHighWacGoods;

    private Boolean isShortExpirationGoods;

    private Boolean isSealDelivery;
}
