package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.MaterialSkuVO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/30 17:07
 */
@Data
public class MaterialSkuWithTagBO {

    private String materialSkuId;

    private String materialSkuName;

    private Integer quantity;

    private String unit;

    /**
     * 大单标签
     */
    private Boolean majorItemTag;

    public MaterialSkuVO toVO() {
        MaterialSkuVO materialSkuVO = new MaterialSkuVO();
        materialSkuVO.setMaterialSkuId(materialSkuId);
        materialSkuVO.setMaterialSkuName(materialSkuName);
        materialSkuVO.setQuantity(quantity);
        materialSkuVO.setUnit(unit);
        materialSkuVO.setMajorItemTag(majorItemTag);

        return materialSkuVO;
    }
}
