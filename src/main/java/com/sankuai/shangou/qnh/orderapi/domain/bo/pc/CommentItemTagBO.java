package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by su<PERSON>oyu on 2023/3/2 14:35
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class CommentItemTagBO {
    /**
     * 渠道商品id
     */
    private Long customerSkuId;

    /**
     * 渠道商品name
     */
    private String customerSkuName;

    /**
     * 商品标签
     */
    private List<String> itemTags;
}
