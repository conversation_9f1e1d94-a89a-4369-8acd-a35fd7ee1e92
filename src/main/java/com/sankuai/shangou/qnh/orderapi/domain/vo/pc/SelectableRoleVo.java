package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.shangou.sac.dto.model.SelectableRoleDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 可选角色列表
 *
 * <AUTHOR>
 * @since 2020-12-21
 */
@Data
public class SelectableRoleVo {

    private Long sacRoleId;

    private Long tenantId;

    private String sacRoleName;

    private Integer operatorAccountOpType;

    private Boolean editAccountHave;

    private List<Long> sacPermissionIds;

    public static List<SelectableRoleVo> buildBySelectableRoleDtos(List<SelectableRoleDTO> selectableRoleDtos) {

        if (CollectionUtils.isEmpty(selectableRoleDtos)) {
            return Collections.emptyList();
        }
        List<SelectableRoleVo> selectableRoleVos = new ArrayList<>();
        for (SelectableRoleDTO selectableRoleDto : selectableRoleDtos) {
            SelectableRoleVo selectableRoleVo = buildBySelectableRoleDto(selectableRoleDto);
            if (selectableRoleVo != null) {
                selectableRoleVos.add(selectableRoleVo);
            }
        }
        return selectableRoleVos;

    }

    public static SelectableRoleVo buildBySelectableRoleDto(SelectableRoleDTO selectableRoleDTO) {

        if (selectableRoleDTO == null) {
            return null;
        }
        SelectableRoleVo selectableRoleVo = new SelectableRoleVo();
        selectableRoleVo.setSacRoleId(selectableRoleDTO.getSacRoleId());
        selectableRoleVo.setSacRoleName(selectableRoleDTO.getSacRoleName());
        selectableRoleVo.setTenantId(selectableRoleVo.getTenantId());
        selectableRoleVo.setEditAccountHave(selectableRoleDTO.getCurrentAccountHave());
        selectableRoleVo.setOperatorAccountOpType(selectableRoleDTO.getOperatorAccountOpType());
        return selectableRoleVo;

    }

}
