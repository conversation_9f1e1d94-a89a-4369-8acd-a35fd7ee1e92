package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.TaskType;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.ProductTaskTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 导入模板类型枚举
 *
 * @author: <EMAIL>
 * Date: 2019/1/14 15:04
 * Description:
 */

@Getter
public enum ReportSortKeyEnum {

    /**
     *
     */
    ORDER("order", "总单量优先排序"),
    DEALING_ORDER("dealingOrder", "履约中单量优先排序"),
    EXCEPTION("exception", "总异常优先排序"),
    PICK_EXCEPTION("pickException", "拣货异常排序"),
    DELIVERY_EXCEPTION("deliveryException", "配送异常排序"),
    COMMENT_EXCEPTION("commentException", "评价异常排序"),
    ;


    private String type;

    private String desc;



    ReportSortKeyEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static ReportSortKeyEnum getByType(String type) {
        for (ReportSortKeyEnum e : ReportSortKeyEnum.values()) {
            if (StringUtils.equalsIgnoreCase(e.getType(), type)) {
                return e;
            }
        }

        return null;
    }
}



