package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.DrunkHorseGiftBagVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.ChannelOrderDetailBO;

import java.util.List;
import java.util.stream.Collectors;

/***
 * author : <EMAIL>
 * date : 2024/8/21
 * time : 14:38
 * 描述 :
 **/
@TypeDoc(
        description = "歪马礼袋"
)
@ApiModel("歪马礼袋")
@Data
public class DrunkHorseGiftBagVO {

    @FieldDoc(
            description = "主品货号"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private String belongSkuId;

    @FieldDoc(
            description = "礼袋货品skuId"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private String materialSkuId;

    @FieldDoc(
            description = "礼袋货品名称"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private String materialSkuName;

    @FieldDoc(
            description = "礼袋图片"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private String picUrl;


    @FieldDoc(
            description = "礼袋类型，1:品牌礼袋， 2:歪马礼袋"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private int type;

    @FieldDoc(
            description = "礼袋应赠数量"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private int cnt;

    @FieldDoc(
            description = "调整后的数量"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private int resetCnt;

    @FieldDoc(
            description = "退款数量"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private int refundCnt;

    @FieldDoc(
            description = "规格"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private String spec;

    @FieldDoc(
            description = "upc"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private String upc;


    @FieldDoc(
            description = "主品skuId"
    )
    @ApiModelProperty(name = "主品skuId", required = false)
    private String parentSkuId;

    @FieldDoc(
            description = "主品名称"
    )
    @ApiModelProperty(name = "主品名称", required = false)
    private String parentSkuName;

    @FieldDoc(
            description = "主品upc"
    )
    @ApiModelProperty(name = "主品upc", required = false)
    private String parentSkuUpc;


    public static List<DrunkHorseGiftBagVO> buildDrunkHorseGiftBag(List<DrunkHorseGiftBagVo> drunkHorseGiftBagVoList, List<ChannelOrderDetailBO.ItemInfo> itemList) {
        if (CollectionUtils.isEmpty(drunkHorseGiftBagVoList)) {
            return Lists.newArrayList();
        }

        return drunkHorseGiftBagVoList.stream().map(giftBag -> buildDrunkHorseGiftBagVO(giftBag, itemList)).collect(Collectors.toList());
    }


    private static DrunkHorseGiftBagVO buildDrunkHorseGiftBagVO(DrunkHorseGiftBagVo drunkHorseGiftBagVo, List<ChannelOrderDetailBO.ItemInfo> itemList){
        ChannelOrderDetailBO.ItemInfo itemInfoVO = itemList.stream().filter(item -> StringUtils.equals(item.getCustomSkuId(), drunkHorseGiftBagVo.getBelongSkuId()))
                .findFirst().orElse(null);
        DrunkHorseGiftBagVO drunkHorseGiftBagVO = new DrunkHorseGiftBagVO();
        drunkHorseGiftBagVO.setBelongSkuId(drunkHorseGiftBagVo.getBelongSkuId());
        drunkHorseGiftBagVO.setMaterialSkuId(drunkHorseGiftBagVo.getMaterialSkuId());
        drunkHorseGiftBagVO.setMaterialSkuName(drunkHorseGiftBagVo.getMaterialSkuName());
        drunkHorseGiftBagVO.setPicUrl(drunkHorseGiftBagVo.getPicUrl());
        drunkHorseGiftBagVO.setType(drunkHorseGiftBagVo.getType());
        drunkHorseGiftBagVO.setCnt(drunkHorseGiftBagVo.getCnt());
        drunkHorseGiftBagVO.setResetCnt(drunkHorseGiftBagVo.getResetCnt());
        drunkHorseGiftBagVO.setRefundCnt(drunkHorseGiftBagVo.getRefundCnt());
        drunkHorseGiftBagVO.setSpec(drunkHorseGiftBagVo.getSpec());
        drunkHorseGiftBagVO.setUpc(drunkHorseGiftBagVo.getUpc());
        drunkHorseGiftBagVO.setParentSkuId(drunkHorseGiftBagVo.getBelongSkuId());
        if (itemInfoVO != null){
            drunkHorseGiftBagVO.setParentSkuUpc(itemInfoVO.getUpc());
            drunkHorseGiftBagVO.setParentSkuName(itemInfoVO.getSkuName());
        }
        return drunkHorseGiftBagVO;
    }

}
