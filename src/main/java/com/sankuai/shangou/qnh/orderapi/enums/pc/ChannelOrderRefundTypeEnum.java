package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Optionable;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/22 15:59
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum ChannelOrderRefundTypeEnum implements Optionable {

    NO_REFUND(100,"无退款"),
    ALL_REFUND(200,"全部退款"),
    PART_REFUND(300,"部分退款");


    private int code;

    private String desc;

    public static ChannelOrderRefundTypeEnum getByCode(int code) {
        for (ChannelOrderRefundTypeEnum e : ChannelOrderRefundTypeEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }

        return null;
    }


    @Override
    public List<UiOption> toOptions() {
        return Arrays.asList(ChannelOrderRefundTypeEnum.values()).stream()
                .map(e -> new UiOption(String.valueOf(e.getCode()),e.getDesc())).collect(Collectors.toList());
    }
}
