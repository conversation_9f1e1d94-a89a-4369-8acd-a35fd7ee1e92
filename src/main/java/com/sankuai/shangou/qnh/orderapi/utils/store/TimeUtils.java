package com.sankuai.shangou.qnh.orderapi.utils.store;

import com.sankuai.shangou.qnh.orderapi.domain.dto.store.SearchTimeDto;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018/9/26
 */
public class TimeUtils {
    private static final long DAY_TIME_STAMP = (long) 1000 * 3600 * 24;

    private static final String pattern = "yyyy-MM-dd HH:mm:ss";

    private static final String ymdPattern = "yyyy-MM-dd";

    private static final String mdhmPattern = "MM-dd HH:mm";

    private static final String ymdhmPattern = "yyyy-MM-dd HH:mm";

    private static final String mdhmsPattern = "MM-dd HH:mm:ss";

    // 搜索启始时间(1970-01-01 08:00:00),所有搜索输入的合法时间不能小于它
    private static final long SEARCH_BEGIN_TIME = 0L;

    public static String convertTimeStamp2Str(long timestamp) {

        return DateFormatUtils.format(timestamp, pattern);
    }

    public static String convertTimeStamp2SimpleStr(long timestamp) {

        return DateFormatUtils.format(timestamp, ymdPattern);
    }

    public static long convertStr2TimeStamp(String timeStr) {
        try {
            return new SimpleDateFormat(pattern).parse(timeStr).getTime();
        } catch (ParseException e) {
            throw new CommonRuntimeException(timeStr + "时间解析错误");
        }
    }

    public static long getBeforeDayTimeStamp(int beforeDay) {
        if (beforeDay <= 0) {
            return System.currentTimeMillis();
        }
        return System.currentTimeMillis() - DAY_TIME_STAMP * beforeDay;
    }

    public static String convertTimeStamp2MDHMStr(long timestamp) {

        return DateFormatUtils.format(timestamp, mdhmPattern);
    }

    public static String convertTimeStamp2YMDHMStr(long timestamp) {

        return DateFormatUtils.format(timestamp, ymdhmPattern);
    }

    public static String convertTimeStamp2MDHMSStr(Long timestamp) {

        if (Objects.isNull(timestamp)) {
            return "";
        }
        return DateFormatUtils.format(timestamp, mdhmsPattern);
    }

    /**
     * 时间规范
     *
     * @param startTime
     * @param endTime
     * @param keywords
     * @return
     */
    public static SearchTimeDto standardTime(Long startTime, Long endTime, String... keywords) {
        // 如果没有关键字(或关键字均为空),不需要规范时间
        if (isAllEmpty(keywords) && startTime == null && endTime == null) {
            return null;
        }

        // 如果有关键字,检测输入的时间是否符合要求,并规范化
        long currentTime = System.currentTimeMillis();
        int caseNum = (startTime == null || startTime <= SEARCH_BEGIN_TIME) ? 0 : 1;
        caseNum += (endTime == null || endTime <= SEARCH_BEGIN_TIME) ? 0 : 2;

        switch (caseNum) {
            case 0:
                return new SearchTimeDto(getTime(currentTime, -6, SEARCH_BEGIN_TIME), currentTime);
            case 1:
                return new SearchTimeDto(startTime, getTime(startTime, 6, SEARCH_BEGIN_TIME));
            case 2:
                return new SearchTimeDto(getTime(endTime, -6, SEARCH_BEGIN_TIME), endTime);
            case 3:
                // 粗略计算时间差是否合法(结束时间-开始时间大于6个月或者小于等于0均为不合法),不合法默认以endTime倒推6个月
                long differentTime = endTime - startTime;
                if (differentTime > DAY_TIME_STAMP * 30 * 6 || differentTime <= 0) {
                    return new SearchTimeDto(getTime(endTime, -6, SEARCH_BEGIN_TIME), endTime);
                }
                return new SearchTimeDto(startTime, endTime);
            default:
                return null;
        }


    }


    private static boolean isAllEmpty(String[] array) {
        if (ArrayUtils.isEmpty(array)) {
            return true;
        }

        for (String str : array) {
            if (StringUtils.isNotEmpty(str)) {
                return false;
            }
        }
        return true;
    }

    private static long getTime(long initialTime, int month, long minTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(initialTime);
        calendar.add(calendar.MONTH, month);
        long resultTime = calendar.getTimeInMillis();
        if (resultTime < minTime) {
            return minTime;
        }
        return resultTime;
    }

    public static SearchTimeDto getProcessQueryTime(long startTime, long endTime) {
        long currentTime = System.currentTimeMillis();
        // 如果有关键字,检测输入的时间是否符合要求,并规范化
        int caseNum = startTime <= SEARCH_BEGIN_TIME ? 0 : 1;
        caseNum += endTime <= SEARCH_BEGIN_TIME ? 0 : 2;

        switch (caseNum) {
            case 0:
                return new SearchTimeDto(getTime(currentTime, -1, SEARCH_BEGIN_TIME), currentTime);
            case 1:
                return new SearchTimeDto(startTime, getTime(startTime, 1, SEARCH_BEGIN_TIME));
            case 2:
                return new SearchTimeDto(getTime(endTime, -1, SEARCH_BEGIN_TIME), endTime);
            case 3:
                // 粗略计算时间差是否合法(结束时间-开始时间大于6个月或者小于等于0均为不合法),不合法默认以endTime倒推6个月
                long differentTime = endTime - startTime;
                if (differentTime > DAY_TIME_STAMP * 31 * 1 || differentTime <= 0) {
                    return new SearchTimeDto(getTime(endTime, -1, SEARCH_BEGIN_TIME), endTime);
                }
                return new SearchTimeDto(startTime, endTime);
            default:
                return null;
        }
    }

    public static Long convertToTimestamp(LocalDateTime datetime){
        if (datetime == null){
            return null;
        }
        return datetime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}
