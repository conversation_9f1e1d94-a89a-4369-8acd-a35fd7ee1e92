package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.shangou.qnh.orderapi.constant.pc.BoothConstant;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AdjustOrderBasicVO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单基本信息
 *
 * @Author: shihuifeng
 * @Date: 2020/04/17
 * @Description: 订单基本信息
 */
@Setter
@Getter
@NoArgsConstructor
@ToString
public class ChannelOrderBasicBO {

    /**
     * 渠道编码
     */
    private String channelId;

    /**
     * 渠道订单号
     */
    private String channelOrderId;

    /**
     * 门店id
     */
    private Long poiId;


    /**
     * 门店名称
     */
    private String poiName;


    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 可操作列表
     */
    private List<Integer> canOperateItemList;

    /**
     * 商品基本信息
     */
    private List<ItemBasicInfo> itemBasicInfoList;


    @Getter
    @Setter
    @NoArgsConstructor
    @ToString
    public static class ItemBasicInfo {

        /**
         * 商品项id
         */
        private Long orderItemId;


        /**
         * 商品名称
         */
        private String skuName;

        /**
         * 单价(分)
         */
        private Integer unitPrice;

        /**
         * 规格
         */
        private String spec;

        /**
         * 数量
         */
        private Integer quantity;

        /**
         * sku编码
         */
        private String skuId;

        /**
         * 摊位
         */
        private Long boothId;

        /**
         * 商品线下售价(分)
         */
        private Integer offlinePrice;

        /**
         * 上次更新时间
         */
        private Long lastUpdateTime;

        /**
         * 结算类型，1后结，2现结(外采)
         */
        private Integer settleType;

    }


    public AdjustOrderBasicVO toAdjustOrderBasicVO() {
        AdjustOrderBasicVO vo = new AdjustOrderBasicVO();
        vo.setOrderId(this.getChannelOrderId());
        vo.setPoiId(String.valueOf(this.getPoiId()));
        vo.setPoiName(this.getPoiName());
        vo.setChannelName(this.getChannelName());
        vo.setCouldOperateItemList(this.canOperateItemList == null ? Lists.newArrayList() : this.canOperateItemList.stream().map(i -> String.valueOf(i)).collect(Collectors.toList()));
        List<AdjustOrderBasicVO.ItemBasicInfoVO> itemBasicInfoVOList = Lists.newArrayList();
        for (ItemBasicInfo itemBasicInfo : this.getItemBasicInfoList()) {
            AdjustOrderBasicVO.ItemBasicInfoVO itemBasicInfoVO = new AdjustOrderBasicVO.ItemBasicInfoVO();
            itemBasicInfoVO.setOrderItemId(String.valueOf(itemBasicInfo.getOrderItemId()));
            itemBasicInfoVO.setSkuName(itemBasicInfo.getSkuName());
            itemBasicInfoVO.setSku(itemBasicInfo.getSkuId());
            itemBasicInfoVO.setSpec(itemBasicInfo.getSpec());
            itemBasicInfoVO.setUnitPrice(itemBasicInfo.getUnitPrice() == null ? "" : ConverterUtils.formatMoney(itemBasicInfo.getUnitPrice()));
            itemBasicInfoVO.setQuantity(itemBasicInfo.getQuantity() == null ? "" : String.valueOf(itemBasicInfo.getQuantity()));
            itemBasicInfoVO.setBoothId(itemBasicInfo.getBoothId() == null ? BoothConstant.DEFAULT_BOOTH : String.valueOf(itemBasicInfo.getBoothId()));
            itemBasicInfoVO.setOfflinePrice(itemBasicInfo.getOfflinePrice() == null ? "" : ConverterUtils.formatMoney(itemBasicInfo.getOfflinePrice()));
            itemBasicInfoVO.setUpdateTime(String.valueOf(itemBasicInfo.getLastUpdateTime()));
            itemBasicInfoVO.setSettleType(itemBasicInfo.getSettleType()==null?"":String.valueOf(itemBasicInfo.getSettleType()));
            itemBasicInfoVOList.add(itemBasicInfoVO);
        }
        vo.setItemBasicInfoVOList(itemBasicInfoVOList);
        return vo;
    }
}
