package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
public class DeliveryInfoVo {

    //配送日志
    private List<DeliveryDetailVO> deliveryLogList;
    //配送费信息
    private DeliveryFeeInfoVO deliveryFeeInfo;

    @Setter
    @Getter
    @ToString
    public static class DeliveryFeeInfoVO {
        //配送渠道名称
        private String deliveryChannelName;
        // 骑手名字
        private String riderName;
        // 骑手电话
        private String riderPhone;
        // 配送费，元
        private String deliveryFee;
        // 小费，元
        private String tipAmount;
        //第N次配送
        private Integer deliveryCount;
        //承运商
        private String deliveryPlatform;
    }
}
