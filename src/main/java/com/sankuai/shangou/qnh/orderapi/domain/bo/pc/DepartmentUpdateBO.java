package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;


import com.meituan.shangou.saas.tenant.thrift.dto.department.request.DepartmentModRequest;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.DepartmentModifyRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.DistrictSim;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2019/5/4 14:53
 * @Description:
 */
@Data
public class DepartmentUpdateBO extends DepartmentCreateBO {

    private Long departmentId;


    public DepartmentUpdateBO(DepartmentModifyRequest request) {
        this.departmentId = request.getDepartmentId();
        this.departmentName = request.getDepartmentName();
        this.departmentType = request.getDepartmentType();
        this.parentDepartmentId = request.getParentDepartmentId();
        this.depMgtScope = request.getDepMgtScope();
        this.associatedCommercialAgentId = request.getAssociatedCommercialAgentId();
        this.settleType = request.getSettleType();
        this.boothPickingType = request.getBoothPickingType();
        this.bankCode = request.getBankCode();
        this.bankName = request.getBankName();
        this.bankCardNo = request.getBankCardNo();
        this.accountName = request.getAccountName();
        this.qrCodeUrl = request.getQrCodeUrl();
        this.poiId = ConverterUtils.nonNullConvert(request.getPoiId(), Long::valueOf);
        this.mobilePayAccount = request.getMobilePayAccount();
        this.boothSettleRatio = request.getBoothSettleRatio();
        this.quickSearchCode = request.getBoothQuickSearchCode();
        this.onSiteAlipayAccount = request.getBoothOnsiteAlipayAccount();
        this.onSiteAlipayRealname = request.getBoothOnsiteAlipayRealname();
    }



    public DepartmentModRequest convert2Mod() {
        DepartmentModRequest req = new DepartmentModRequest();
        req.setDepartmentId(departmentId);
        req.setDepartmentName(departmentName);
        req.setDepartmentType(departmentType);
        req.setTenantId(ContextHolder.currentUserTenantId());
        req.setOptUser(ContextHolder.currentAccount());
        req.setParentId(parentDepartmentId);
        req.setDepMgtScope(ConverterUtils.convertList(depMgtScope, DistrictSim::toDto));
        return req;
    }

}
