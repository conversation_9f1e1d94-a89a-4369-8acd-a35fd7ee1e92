package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.PageInfoDTO;
import com.sankuai.meituan.shangou.empower.rider.client.common.TPageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.Objects;

@TypeDoc(
        description = "分页信息"
)
@Data
@ApiModel("分页信息")
@Builder
@AllArgsConstructor
public class PageInfoVO {

    @FieldDoc(
            description = "当前页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "当前页", required = true)
    private Integer page;

    @FieldDoc(
            description = "当前行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "当前行数", required = true)
    private Integer size;

    @FieldDoc(
            description = "总页数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "总页数", required = true)
    private Integer totalPage;

    @FieldDoc(
            description = "总行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "总行数", required = true)
    private Integer totalSize;

    @FieldDoc(
            description = "游标，查询非第一页时需要传入", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "游标")
    private String scrollId;

    public PageInfoVO buildPageInfoVO(PageInfoDTO pageInfoDTO) {

        if (Objects.isNull(pageInfoDTO)) {
            return null;
        }

        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(pageInfoDTO.getPage());
        pageInfoVO.setSize(pageInfoDTO.getSize());
        pageInfoVO.setTotalPage(pageInfoDTO.getTotalPage());
        pageInfoVO.setTotalSize(pageInfoDTO.getTotalSize());

        return pageInfoVO;
    }
    public PageInfoVO() {
    }

    public PageInfoVO(com.sankuai.meituan.shangou.empower.productplatform.thrift.common.dto.PageInfoDTO pageInfo) {
        this.page = pageInfo.getPageNum();
        this.size = pageInfo.getPageSize();
        this.totalSize = pageInfo.getTotalSize();
        this.totalPage = pageInfo.getPageCount();
    }
    public PageInfoVO buildPageInfoVO(com.sankuai.meituan.shangou.empower.price.client.dto.PageInfoDTO pageInfoDTO) {

        if (Objects.isNull(pageInfoDTO)) {
            return null;
        }

        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(pageInfoDTO.getPage());
        pageInfoVO.setSize(pageInfoDTO.getSize());
        pageInfoVO.setTotalPage(pageInfoDTO.getTotalPage());
        pageInfoVO.setTotalSize(pageInfoDTO.getTotalSize().intValue());

        return pageInfoVO;
    }


    public static PageInfoVO convert(com.sankuai.meituan.shangou.empower.productbiz.client.dto.PageInfoDTO pageInfoDTO) {
        if (Objects.isNull(pageInfoDTO)) {
            return null;
        }
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(pageInfoDTO.getPage());
        pageInfoVO.setSize(pageInfoDTO.getSize());
        pageInfoVO.setTotalPage(pageInfoDTO.getTotalPage());
        pageInfoVO.setTotalSize(pageInfoDTO.getTotal().intValue());
        return pageInfoVO;
    }
    public static PageInfoVO initPageInfoVO(Integer page,Integer size){
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(page);
        pageInfoVO.setSize(size);
        pageInfoVO.setTotalPage(0);
        pageInfoVO.setTotalSize(0);
        return pageInfoVO;
    }

    public PageInfoVO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.PageInfoDTO pageInfo) {
        this.page = pageInfo.getPage();
        this.size = pageInfo.getSize();
        this.totalSize = pageInfo.getTotal().intValue();
        this.totalPage = pageInfo.getTotalPage();
    }

    public PageInfoVO(TPageInfo pageInfo) {
        this.page = pageInfo.getPage();
        this.size = pageInfo.getPageSize();
        this.totalSize = pageInfo.getTotal();
        this.totalPage = ((pageInfo.getTotal() - 1) / pageInfo.getPageSize()) + 1;
    }

    public PageInfoVO(Integer page, Integer size, Integer totalSize) {
        this.page = page;
        this.size = size;
        this.totalSize = totalSize;
        this.totalPage = ((totalSize - 1) / size) + 1;
    }
}
