package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: wangjian146
 * @Date: 2022/07/29
 * @Description:
 */
@TypeDoc(
        name = "门店分组操作日志",
        description = "商品操作日志VO对象"
)
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class PoiGroupLogVO {

    /**
     * 操作人
     */
    private String opUser;

    /**
     * 操作时间
     */
    private String opTime;

    /**
     * 操作内容
     */
    private String detail;

}
