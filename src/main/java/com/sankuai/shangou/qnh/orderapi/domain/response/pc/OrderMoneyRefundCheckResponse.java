package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderItemMoneyRefundCheckVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.RefundReasonAndCodeVO;
import lombok.Data;

import java.util.List;

/**
 * Created by suxiaoyu on 2023/3/7 14:19
 */
@TypeDoc(
        description = "金额退页面检查的response"
)
@Data
public class OrderMoneyRefundCheckResponse {
    @FieldDoc(
            description = "金额退页面检查的商品信息"
    )
    private List<OrderItemMoneyRefundCheckVO> moneyRefundCheckVOList;

    @FieldDoc(
            description = "退款原因及编码"
    )
    private List<RefundReasonAndCodeVO> refundReasons;

}
