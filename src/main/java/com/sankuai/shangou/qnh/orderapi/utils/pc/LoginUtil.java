package com.sankuai.shangou.qnh.orderapi.utils.pc;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.sankuai.shangou.qnh.orderapi.enums.pc.CookieTokenTypeEnum;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import com.sankuai.it.sso.sdk.utils.SSOUtil;
import com.sankuai.it.sso.sdk.utils.WebUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;

/**
 * 登录辅助类
 *
 * <AUTHOR>
 */
public class LoginUtil {

    private static final String PW_SEPARATOR = "&";
    private static final String LOGIN_TICKET_COOKIE_NAME = "_t";

    /**灰度链路*/
    private static final String LOGIN_TICKET_COOKIE_LITESET = "_liteset";

    private LoginUtil() {

    }

    /**
     * 生成md5密码
     *
     * @param password   密码
     * @param ctime 创建时间
     * @return
     */
    public static String generateMD5Password(String password, Long ctime) {

        StringBuilder builder = new StringBuilder();

        String loginSecretKey = getLoginSecretKey();
        long dynamicMd5Salt = ctime;
        String data = builder.append(loginSecretKey).append(PW_SEPARATOR).append(password)
                .append(PW_SEPARATOR).append(dynamicMd5Salt).toString();
        String md5Password = DigestUtils.md5Hex(data);

        return md5Password;
    }

    /**
     * 生成ticket
     *
     * @return
     */
    public static String generateTicket(String account) {
        String loginSecretKey = getLoginSecretKey();
        String random = UUIDUtil.uuid();
        String ticket = DigestUtils.md5Hex(loginSecretKey + random + account);
        return ticket;
    }

    /**
     * 添加凭证cookie
     *
     * @param ticket
     * @param request
     * @param response
     */
    public static void addLoginTicketCookie(CookieTokenTypeEnum cookieTokenTypeEnum, String ticket, HttpServletRequest request,
                                            HttpServletResponse response) {
        String domain = request.getServerName();
        String path = "/";
        int maxAge = -1;
        CookieUtil.addCookie(response, cookieTokenTypeEnum.getName(), ticket, domain, path, maxAge);

        //增加一个顶级域名的cookie
        if (StringUtils.contains(domain, CommonConstant.DOT)) {
            String[] ds = StringUtils.split(domain, CommonConstant.DOT);
            String flDomain = StringUtils.join(ArrayUtils.subarray(ds,ds.length-2,ds.length),".");
            CookieUtil.addCookie(response, cookieTokenTypeEnum.getName(), ticket, flDomain, path, maxAge);
        }

    }

    public static void addLoginTicketCookieWithSamesite(CookieTokenTypeEnum cookieTokenTypeEnum,String ticket, HttpServletRequest request,
                                            HttpServletResponse response) {
        String domain = request.getServerName();
        String path = "/";
        int maxAge = -1;
        CookieUtil.addCookieWithSameSite(response, cookieTokenTypeEnum.getName(), ticket, domain, path, maxAge);

        //增加一个顶级域名的cookie
        if (StringUtils.contains(domain,CommonConstant.DOT)) {
            String[] ds = StringUtils.split(domain, CommonConstant.DOT);
            String flDomain = StringUtils.join(ArrayUtils.subarray(ds,ds.length-2,ds.length),".");
            CookieUtil.addCookieWithSameSite(response, cookieTokenTypeEnum.getName(), ticket, flDomain, path, maxAge);
        }

    }

    public static void addCookieUnTop(CookieTokenTypeEnum cookieTokenTypeEnum,String ticket, HttpServletRequest request,
                                            HttpServletResponse response) {
        String domain = request.getServerName();
        String path = "/";
        int maxAge = -1;
        CookieUtil.addCookieWithSameSite(response, cookieTokenTypeEnum.getName(), ticket, domain, path, maxAge);
    }


        /**
         * 添加灰度链路cookie
         *
         * @param liteSet
         * @param request
         * @param response
         */
    public static void addLiteSetCookie(String liteSet, HttpServletRequest request,
        HttpServletResponse response) {

        if(StringUtils.isEmpty(liteSet)){return;}

        String domain = request.getServerName();
        String path = "/";
        int maxAge = -1;
        CookieUtil.addCookie(response, LOGIN_TICKET_COOKIE_LITESET, liteSet, domain, path, maxAge);

    }

    /**
     * 删除登录凭证Cookie
     *
     * @param request
     * @param response
     */
    public static void removeLoginCookie(HttpServletRequest request, HttpServletResponse response) {
        String domain = request.getServerName();
        String path = "/";
        CookieUtil.removeCookie(request, response, LOGIN_TICKET_COOKIE_NAME, domain, path);
        CookieUtil.removeCookie(request, response,CookieTokenTypeEnum.E_TOKEN.getName() , domain, path);
        CookieUtil.removeCookie(request, response,CookieTokenTypeEnum.APP_ID.getName() , domain, path);
        CookieUtil.removeCookie( request, response,CookieTokenTypeEnum.BIZ_APP_ID.getName(),domain,path);
        CookieUtil.removeCookie( request, response,CookieTokenTypeEnum.QNH_ACCOUNT_ID.getName(),domain,path);
        CookieUtil.removeCookie(request, response, LOGIN_TICKET_COOKIE_LITESET, domain, path);
        if (StringUtils.contains(domain,CommonConstant.DOT)) {
            String[] ds = StringUtils.split(domain, CommonConstant.DOT);
            String flDomain = StringUtils.join(ArrayUtils.subarray(ds,ds.length-2,ds.length),".");
            CookieUtil.removeCookie(request, response, LOGIN_TICKET_COOKIE_NAME, flDomain, path);
            CookieUtil.removeCookie(request, response, CookieTokenTypeEnum.E_TOKEN.getName(), flDomain, path);
        }
    }


    public static void removeSsoCookie(String ssoClientId, HttpServletRequest request, HttpServletResponse response) {
        WebUtil.removeCookie(response, SSOUtil.genSSOIdCookieName(ssoClientId));
    }



    public static Pair<CookieTokenTypeEnum,String> getLoginTicket(HttpServletRequest request) {

        String authToken = CookieUtil.getCookieValue(request, LOGIN_TICKET_COOKIE_NAME);
        if (StringUtils.isNotBlank(authToken)){
            return Pair.of(CookieTokenTypeEnum.AUTH_TOKEN,authToken);
        }
        String eToken = CookieUtil.getCookieValue(request, CookieTokenTypeEnum.E_TOKEN.getName());
        return Pair.of(CookieTokenTypeEnum.E_TOKEN,eToken);
    }


    private static String getLoginSecretKey() {
        String loginSecretKey = MccConfigUtil.getLoginSecretKey();
        if (StringUtils.isBlank(loginSecretKey)) {
            throw new BizException("系统登录参数未配置, 请联系管理员");
        }
        return loginSecretKey.trim();
    }

    public static Integer getAppId(HttpServletRequest request){
        String appId = CookieUtil.getCookieValue(request, CookieTokenTypeEnum.APP_ID.getName());
        //如果appId 不存在 直接返回null
        if (StringUtils.isBlank(appId)){
            return null;
        }
        return Integer.parseInt(appId);
    }

    public static Integer getBizAppId(HttpServletRequest request){
        String appId = CookieUtil.getCookieValue(request, CookieTokenTypeEnum.BIZ_APP_ID.getName());
        //如果appId 不存在 返回null
        if (StringUtils.isBlank(appId)){
            return  null;
        }
        return Integer.parseInt(appId);
    }

}
