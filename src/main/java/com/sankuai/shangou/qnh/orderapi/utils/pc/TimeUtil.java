package com.sankuai.shangou.qnh.orderapi.utils.pc;

import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.exception.pc.ParamInvalidException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Author：<EMAIL>
 * @Date: 2018/9/30 上午10:08
 */
@Slf4j
public class TimeUtil {

    private static final long DAY_TIME_STAMP = (long) 1000 * 3600 * 24;

    // 搜索启始时间(1970-01-01 08:00:00),所有搜索输入的合法时间不能小于它
    private static final long SEARCH_BEGIN_TIME = 0L;

    public static final String LONG_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String DATE_SLASH_PATTERN = "yyyy/MM/dd";

    public static final String POINT_DATE_FORMAT = "yyyy.MM.dd HH:mm:ss";

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String MM_DD_BY_DOT = "MM.dd";

    public static final String MM_DD_HH_MM = "MM.dd HH:mm";
    public static final String YYYY_MM_DD_HH_MM = "yyyy.MM.dd HH:mm";


    public static final String SHORT_TIME_FORMAT = "HH:mm:ss";

    public static final String TIME_SEPARATOR = "-";

    public static final String DOT_DATE_FORMAT = "yyyy.MM.dd";
    public static final String INT_DATE_FORMAT = "yyyyMMdd";

    /**
     * @author: <EMAIL>
     * @date: 2018-09-30 10:09:07
     * @method: isValidDate
     * @params: [strDate, format]
     * @return: boolean
     * @desc: 判断时间格式是否正确, 正确返回true，错误返回false
     */
    public static boolean isValidDate(String strDate, String format) {

        if (StringUtils.isEmpty(strDate)) {
            log.error("date value is blank.");
            throw new ParamInvalidException("时间不能为null");
        }

        Date tmpDate = DateUtil.parse(strDate, format);
        return tmpDate != null;
    }

    public static long convert2EpochMilliseconds(LocalDateTime time) {
        if (time == null) {
            return 0L;
        }
        return time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static String convertTimeStamp2NormalStr(long timestamp) {
        return DateFormatUtils.format(timestamp, LONG_DATE_FORMAT);
    }

    public static String convertLocalDateTime2NormalStr(LocalDateTime localDateTime) {
        return DateFormatUtils.format(convertLocalDateTimeToDate(localDateTime), LONG_DATE_FORMAT);
    }

    public static String convertTimeStamp2PointStr(long timestamp) {
        return DateFormatUtils.format(timestamp, POINT_DATE_FORMAT);
    }

    public static String convertTimeStamp2Y_M_D(long timestamp){
        return DateFormatUtils.format(timestamp, YYYY_MM_DD);
    }

    public static String convertTimeStamp2DotStr(long timestamp){
        return DateFormatUtils.format(timestamp,DOT_DATE_FORMAT );
    }


    public static long convertStringTime(String time) {

        try {
            return DateUtil.parse(time, Constants.DateFormats.SHOW_FORMAT_HLINE).getTime();
        } catch (Exception e) {
            throw new BizException("日期格式错误("+time+"),期望格式:yyyy-MM-dd HH:mm:ss");
        }
    }

    public static long convertY_M_D2TimeStamp(String time) {
        try {
            return DateUtil.parse(time, YYYY_MM_DD).getTime();
        } catch (Exception e) {
            throw new BizException("日期格式错误("+time+"),期望格式:yyyy-MM-dd");
        }
    }

    /**
    * author: <EMAIL>
    * date: 2019-12-18 16:24:27
    *
    * method: isValidQueryTime
    * params: [startTime, endTime, maxDay]
    * return: boolean
    * desc: 检测查询时间范围是否合法
     * 其中最大的查询天数maxDay，不包括今天
    */
    public static boolean isValidQueryTime(String startTime, String endTime, int maxDay) {
        if (StringUtils.compare(startTime, endTime) > 0) {
            log.warn("开始时间不能大于截止时间: startTime [{}], endTime [{}].", startTime, endTime);
            return false;
        }

        // 时间格式必须合法
        Date startDate = DateUtil.parse(startTime + " 00:00:00", DateUtil.YYYY_MM_DD_HH_MM_SS);
        Date endDate = DateUtil.parse(endTime + " 23:59:59", DateUtil.YYYY_MM_DD_HH_MM_SS);
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            log.warn("时间格式非法: startTime [{}], endTime [{}].", startTime, endTime);
            return false;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        calendar.add(Calendar.DAY_OF_YEAR, maxDay);

        return calendar.getTimeInMillis() > endDate.getTime();
    }

    public static boolean isValidLongQueryTime(long startTime, long endTime, int maxDay) {
        if (startTime > endTime) {
            log.warn("开始时间不能大于截止时间: startTime [{}], endTime [{}].", startTime, endTime);
            return false;
        }

        // 时间格式必须合法
        Date startDate = new Date(startTime);
        Date endDate = new Date(endTime);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        calendar.add(Calendar.DAY_OF_YEAR, maxDay);

        return calendar.getTimeInMillis() > endDate.getTime();
    }

    public static Date parseStartTime(String startTime) {
        return DateUtil.parse(startTime + " 00:00:00", DateUtil.YYYY_MM_DD_HH_MM_SS);
    }


    public static Date parseEndTime(String endTime) {;
        return DateUtil.parse(endTime + " 23:59:59", DateUtil.YYYY_MM_DD_HH_MM_SS);
    }

    public static Long getDayStartTime(String dateStr, String dateFormat) {
        Date date = DateUtil.parse(dateStr + " 00:00:00", dateFormat + "HH:mm:ss");
        if (date == null) {
            throw new IllegalArgumentException("日期格式错误");
        }
        return date.getTime();
    }

    public static Long getDayEndTime(String dateStr, String dateFormat) {
        Date date = DateUtil.parse(dateStr + " 23:59:59", dateFormat + "HH:mm:ss");
        if (date == null) {
            throw new IllegalArgumentException("日期格式错误");
        }
        return date.getTime();
    }

    public static Date convertLocalDateTimeToDate(LocalDateTime startTime) {

        return Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDateTime convertDateToLocalDateTime(Date date) {

        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * 格式化日期
     * @param date 日期
     * @param format 格式
     * @return 日期字符串
     */
    public static String format(LocalDate date, String format) {

        if (date == null) {
            return null;
        }

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        return dateTimeFormatter.format(date);
    }

    /**
     * 获取指定格式日期字符串
     * @param dateStr
     * @param originalFormat
     * @param targetFormat
     * @return
     */
    public static String format(String dateStr, String originalFormat, String targetFormat) {

        if (StringUtils.isEmpty(dateStr)) {
            return StringUtils.EMPTY;
        }

        DateTimeFormatter originalFormatter = DateTimeFormatter.ofPattern(originalFormat);
        DateTimeFormatter targetFormatter = DateTimeFormatter.ofPattern(targetFormat);

        LocalDate originalDate = LocalDate.parse(dateStr, originalFormatter);
        return targetFormatter.format(originalDate);
    }

    public static Integer dotDateToIntDate(String dotDate) {
        String intDate = TimeUtil.format(dotDate, TimeUtil.DOT_DATE_FORMAT, TimeUtil.INT_DATE_FORMAT);
        if (StringUtils.isBlank(intDate)) {
            throw new IllegalArgumentException("日期格式错误");
        }
        return Integer.valueOf(intDate);
    }

    public static String intDateToDotDate(Integer intDate) {
        if (intDate == null) {
            throw new IllegalArgumentException("日期格式错误");
        }
        String dotDate = TimeUtil.format(String.valueOf(intDate), TimeUtil.INT_DATE_FORMAT, TimeUtil.DOT_DATE_FORMAT);
        if (StringUtils.isBlank(dotDate)) {
            throw new IllegalArgumentException("日期格式错误");
        }
        return dotDate;
    }

    /**
     * 获取两个日期之间的所有日期
     * @param beginDateStr 开始日期
     * @param endDateStr 结束日期
     * @param format 格式
     * @return 间隔天数
     *
     * 举例: beginDateStr = "2020-03-01" , endDateStr = "2020-03-02"
     * 返回结果: [2020-02-27, 2020-02-28, 2020-02-29, 2020-03-01]
     */
    public static List<String> getBetweenDateList(String beginDateStr, String endDateStr, String format) {
        List<String> list = new ArrayList<>();
        LocalDate beginDate;
        LocalDate endDate;
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        try {
            beginDate = LocalDate.parse(beginDateStr, dateTimeFormatter);
            endDate = LocalDate.parse(endDateStr, dateTimeFormatter);
        } catch (Exception e) {
            throw new IllegalArgumentException("日期格式错误");
        }

        if (beginDate.compareTo(endDate) > 0) {
            throw new IllegalArgumentException("开始日期不能大于结束日期");
        }

        long betweenDays = ChronoUnit.DAYS.between(beginDate, endDate);
        for (long i = 0; i <= betweenDays; i++) {
            LocalDate localDate = beginDate.plusDays(i);
            list.add(dateTimeFormatter.format(localDate));
        }
        return list;
    }

    /**
     * 获取包装加工单查询时间
     * @param startTime
     * @param endTime
     * @return
     */
    public static Pair<Long,Long> getProcessQueryTime(long startTime, long endTime) {
        long currentTime = System.currentTimeMillis();
        // 如果有关键字,检测输入的时间是否符合要求,并规范化
        int caseNum = startTime <= SEARCH_BEGIN_TIME ? 0 : 1;
        caseNum += endTime <= SEARCH_BEGIN_TIME ? 0 : 2;

        switch (caseNum) {
            case 0:
                return Pair.of(getTime(currentTime, -1, SEARCH_BEGIN_TIME), currentTime);
            case 1:
                return Pair.of(startTime, getTime(startTime, 1, SEARCH_BEGIN_TIME));
            case 2:
                return Pair.of(getTime(endTime, -1, SEARCH_BEGIN_TIME), endTime);
            case 3:
                // 粗略计算时间差是否合法(结束时间-开始时间大于1个月或者小于等于0均为不合法),不合法默认以endTime倒推1个月
                long differentTime = endTime - startTime;
                if (differentTime > DAY_TIME_STAMP * 31 * 1 || differentTime <= 0) {
                    return Pair.of(getTime(endTime, -1, SEARCH_BEGIN_TIME), endTime);
                }
                return Pair.of(startTime, endTime);
            default:
                return null;
        }
    }

    private static long getTime(long initialTime, int month, long minTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(initialTime);
        calendar.add(calendar.MONTH, month);
        long resultTime = calendar.getTimeInMillis();
        if (resultTime < minTime) {
            return minTime;
        }
        return resultTime;
    }


    public static Pair<Long, Long> buildStartTimeAndEndTime(Long startTime, Long endTime) {
        long daysInMillis = TimeUnit.DAYS.toMillis(31);
        if (startTime == null || endTime == null) {
            return Pair.of(System.currentTimeMillis() - daysInMillis, System.currentTimeMillis());
        }
        if (endTime - startTime > daysInMillis) {
            return Pair.of(System.currentTimeMillis() - daysInMillis, System.currentTimeMillis());
        }
        return Pair.of(startTime, endTime);
    }

    /**
     * 开始、结束时间校验是否在最近几个月内
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param month 几个月
     */
    public static void checkQueryStockFlowDate(Date startDate, Date endDate, int month) {
        if (startDate == null || endDate == null) {
            log.error("checkQueryStockFlowDate error, " + "请选择最近" + month + "个月的时间");
            throw new BizException("请选择最近" + month + "个月的时间");
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        //今天零点时间
        Date todayStart = calendar.getTime();
        //n个月前日期的零点时间
        calendar.add(Calendar.MONTH, -1 * month);
        Date beforeDate = calendar.getTime();
        if (startDate.before(beforeDate) || startDate.after(todayStart)) {
            /**
             * 开始时间在最近n个月内
             */
            log.error("checkQueryStockFlowDate error, " + "最多选择最近" + month +"个月的时间");
            throw new BizException("最多选择最近" + month +"个月的时间");
        }
        if (endDate.before(beforeDate) || endDate.after(todayStart)) {
            /**
             * 结束时间在最近n个月内
             */
            log.error("checkQueryStockFlowDate error, " + "最多选择最近" + month +"个月的时间");
            throw new BizException("最多选择最近" + month +"个月的时间");
        }
    }

    /**
     * 校验库存流水查询时间范围
     * 1. 开始、结束时间需要在最近几个月内
     * 2. 开始、结束时间的时间跨度不能超过多少个月
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param month 最近多少个月
     * @param range 时间跨度多少个月
     */
    public static void checkStockFlowTimeRange(Date startDate, Date endDate, int month, int range) {
        if (startDate == null || endDate == null) {
            log.error("checkStockFlowTimeRange error, " + "请选择最近" + month + "个月的时间");
            throw new BizException("请选择最近" + month + "个月的时间");
        }
        Date today = ConverterUtils.parseStartDateDay(new SimpleDateFormat(Constants.DateFormats.DAY_FORMAT2).format(new Date()));
        Calendar todayCalendar = getCalendarOfDateBegin(today);
        //今天零点时间
        Date todayTime = todayCalendar.getTime();
        todayCalendar.add(Calendar.MONTH, -1 * month);
        //n个月前日期的零点时间
        Date startTime = todayCalendar.getTime();
        if (startDate.before(startTime) || startDate.after(todayTime)) {
            throw new BizException("请选择最近" + month + "个月的时间");
        }
        if (endDate.before(startTime) || endDate.after(todayTime)) {
            throw new BizException("请选择最近" + month + "个月的时间");
        }
        if (endDate.before(startDate)) {
            throw new BizException("开始日期不能晚于结束日期");
        }
        Calendar endDateCalendar = getCalendarOfDateBegin(endDate);
        endDateCalendar.add(Calendar.MONTH, -1 * range);
        Date rangeStartTime = endDateCalendar.getTime();
        if (rangeStartTime.after(startDate)) {
            throw new BizException("选择的时间范围跨度，不能超过" + range + "个月");
        }
    }

    /**
     * 获取某天零点时间的日历
     * @param date 某天
     * @return
     */
    public static Calendar getCalendarOfDateBegin(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar;
    }

    public static String convertToString(Long timestamp, String format){
        if (timestamp == null) {
            return StringUtils.EMPTY;
        }
        return new DateTime(timestamp).toString(format);
    }

    /**
     * 获取某一天的最晚时间
     * @param dateTime
     * @return
     */
    public static Date convertEndTime(Date dateTime) {
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(dateTime);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    public static long getBeforeDayTimeStamp(int beforeDay) {
        if (beforeDay <= 0) {
            return System.currentTimeMillis();
        }
        return System.currentTimeMillis() - DAY_TIME_STAMP * beforeDay;
    }

    public static Long getDayBeginTimeStamp(String dateTime, String format) {
        if (StringUtils.isBlank(dateTime) || StringUtils.isBlank(format)) {
            throw new IllegalArgumentException("参数不能为空");
        }
        try {
            Date parse = DateUtil.parse(dateTime, format);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(parse);
            calendar.set(Calendar.AM_PM, Calendar.AM);
            calendar.set(Calendar.HOUR, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            return calendar.getTimeInMillis();
        } catch (Exception e) {
            throw new IllegalArgumentException("获取某日起始时间戳异常");
        }
    }

    public static Long getDayEndTimeStamp(String dateTime, String format) {
        if (StringUtils.isBlank(dateTime) || StringUtils.isBlank(format)) {
            throw new IllegalArgumentException("参数不能为空");
        }
        try {
            Date parse = DateUtil.parse(dateTime, format);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(parse);
            calendar.set(Calendar.AM_PM, Calendar.AM);
            calendar.set(Calendar.HOUR, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            calendar.add(Calendar.DATE, 1);
            return calendar.getTimeInMillis() - 1;
        } catch (Exception e) {
            throw new IllegalArgumentException("获取某日截止时间戳异常");
        }
    }

    public static Long getCurrYearFirst(){
        try {
            Calendar currCal = Calendar.getInstance();
            int currentYear = currCal.get(Calendar.YEAR);
            return getYearFirst(currentYear).getTime();
        } catch (Exception e) {
            throw new IllegalArgumentException("获取当年开始时间戳异常");
        }
    }

    public static Date getYearFirst(int year){
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        Date currYearFirst = calendar.getTime();
        return currYearFirst;
    }
}
