package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.shangou.saas.tenant.thrift.dto.config.AggBizModuleDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/26
 */
@Data
@ToString
public class AggBizModuleVo {

    @FieldDoc(
            description = "业务模块ID"
    )
    public Long moduleId;

    @FieldDoc(
            description = "业务模块名称，例如：基础业务"
    )
    public String moduleName;

    @FieldDoc(
            description = "业务模块编码，例如：BASE_MODULE"
    )
    public String moduleCode;

    @FieldDoc(
            description = "模块关联配置ID列表"
    )
    public List<Integer> relConfigIds;

    public static AggBizModuleVo fromDto(AggBizModuleDto dto) {
        AggBizModuleVo vo = new AggBizModuleVo();
        vo.setModuleId(dto.getModuleId());
        vo.setModuleName(dto.getModuleName());
        vo.setModuleCode(dto.getModuleCode());
        vo.setRelConfigIds(dto.getRelConfigIds());
        return vo;
    }

}
