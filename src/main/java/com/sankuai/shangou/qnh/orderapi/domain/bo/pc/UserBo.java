package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@EqualsAndHashCode
@ToString
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserBo {

    public static final UserBo SYSTEM = new UserBo("system", "系统");

    @FieldDoc(
            description = "账户名"
    )
    private String accountName;

    @FieldDoc(description = "员工姓名")
    private String empName;

    public String getDisplayName() {
        return empName + "-" + accountName;
    }

}
