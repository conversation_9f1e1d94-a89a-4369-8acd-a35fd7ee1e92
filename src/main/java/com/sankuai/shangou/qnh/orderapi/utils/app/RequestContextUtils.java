package com.sankuai.shangou.qnh.orderapi.utils.app;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * <AUTHOR>
 * @since 2022/10/19 17:13
 **/

public class RequestContextUtils {

    private RequestContextUtils() {

    }

    private static final String HEADER_USER_AGENT = "User-Agent";

    private static final String HEADER_STORE_ID = "storeid";


    public static String getClientIp() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        return servletRequestAttributes.getRequest().getRemoteAddr();
    }

    public static String getUserAgent() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        return servletRequestAttributes.getRequest().getHeader(HEADER_USER_AGENT);
    }

    public static Long getHeaderStoreId() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        try{
            return Long.parseLong(servletRequestAttributes.getRequest().getHeader(HEADER_STORE_ID));
        }catch (Exception e){
            return null;
        }
    }

}
