package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        name = "订单履约看板渠道门店信息",
        description = "订单履约看板渠道门店信息"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ChannelShopInfo {

    @FieldDoc(
            description = "渠道"
    )
    private String channel;
    @FieldDoc(
            description = "渠道名称"
    )
    private String channelName;

    @FieldDoc(
            description = "渠道门店编码"
    )
    private String channelPoiId;

    @FieldDoc(
            description = "渠道门店名称"
    )
    private String channelPoiName;

    @FieldDoc(
            description = "渠道门店状态"
    )
    private int channelPoiStatus;
}
