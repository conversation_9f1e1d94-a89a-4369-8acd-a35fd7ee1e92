package com.sankuai.shangou.qnh.orderapi.utils.pc;

import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Base64;

public abstract class WebUtil {

    private static final String DEFAULT_CHARSET = CommonConstant.CHARSET_UTF8;

    private WebUtil() {
    }

    /**
     * 使用默认的UTF-8字符集反编码请求参数值。
     *
     * @param value 参数值
     * @return 反编码后的参数值
     */
    public static String decode(String value) {
        return decode(value, DEFAULT_CHARSET);
    }

    /**
     * 使用默认的UTF-8字符集编码请求参数值。
     *
     * @param value 参数值
     * @return 编码后的参数值
     */
    public static String encode(String value) {
        return encode(value, DEFAULT_CHARSET);
    }

    /**
     * 使用指定的字符集反编码请求参数值。
     *
     * @param value   参数值
     * @param charset 字符集
     * @return 反编码后的参数值
     */
    public static String decode(String value, String charset) {
        String result = null;
        if (!StringUtils.isEmpty(value)) {
            try {
                result = URLDecoder.decode(value, charset);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return result;
    }

    /**
     * 先base64加密,后urlEncode
     *
     * @param value
     * @return
     */
    public static String base64AndUrlEncode(String value) {
        String base64 = Base64.getEncoder().encodeToString(value.getBytes());
        return encode(base64);
    }

    /**
     * 先urlDecode，后base64解密
     *
     * @param value
     * @return
     */
    public static String urlDecodeAndBase64(String value) {
        if (value != null) {
            String decode = decode(value);
            if (decode == null) {
                return null;
            }
            return new String(Base64.getDecoder().decode(decode.getBytes()));
        }
        return null;
    }

    /**
     * 使用指定的字符集编码请求参数值。
     *
     * @param value   参数值
     * @param charset 字符集
     * @return 编码后的参数值
     */
    public static String encode(String value, String charset) {
        String result = null;
        if (!StringUtils.isEmpty(value)) {
            try {
                result = URLEncoder.encode(value, charset);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return result;
    }

    public static boolean isAjaxRequest(HttpServletRequest request) {
        String xRequestedWithHead = request.getHeader(CommonConstant.HEADER_X_REQUESTED_WITH);
        return CommonConstant.XML_HTTP_REQUEST.equals(xRequestedWithHead);
    }

}
