package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.AfterSaleRecordDetailVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.AfterSaleRecordVo;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MoneyUtils;
import lombok.Data;

import java.util.List;

@Data
public class DrunkhorseRefundFeeBO {

    private String settleAmount;

    private String commisionAmount;

    private String costOfGoodsSold;

    private List<AfterSaleRecordDetailVo> afterSaleRecordDetailList;

    public static DrunkhorseRefundFeeBO build(AfterSaleRecordVo afterSaleRecordVo) {
        DrunkhorseRefundFeeBO refundFeeBO = new DrunkhorseRefundFeeBO();
        refundFeeBO.setSettleAmount(MoneyUtils.centToYuan(afterSaleRecordVo.getSettleAmount()));
        refundFeeBO.setCommisionAmount(MoneyUtils.centToYuan(afterSaleRecordVo.getCommisionAmount()));
        refundFeeBO.setAfterSaleRecordDetailList(afterSaleRecordVo.getAfterSaleRecordDetails());
        return  refundFeeBO;
    }





}
