package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.LoginResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/11/1
 **/
@Data
public class AccountLoginBo {

    private String token;

    private AccountInfoVo accountInfo;

    public AccountLoginBo build(LoginResponse response) {
        this.token = response.getToken();
        this.accountInfo = response.getAccountInfo();
        return this;
    }
}
