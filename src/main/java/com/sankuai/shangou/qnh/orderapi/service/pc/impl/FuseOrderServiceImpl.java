package com.sankuai.shangou.qnh.orderapi.service.pc.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.common.enums.OrderCanOperateItem;
import com.meituan.shangou.saas.common.enums.RefundCanOperateItem;
import com.meituan.shangou.saas.dto.response.ocms.CreateInvoiceResponse;
import com.meituan.shangou.saas.dto.response.ocms.QueryOrderBySelfFetchCodeResponse;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderKey;
import com.meituan.shangou.saas.o2o.dto.request.OCMSCheckSelfFetchCodeRequest;
import com.meituan.shangou.saas.o2o.dto.request.OCMSVerifySelfFetchCodeRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.QueryAfterSaleApplyByAfterSaleIdRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.SearchCombinationProductReq;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListRefundOrderRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderDetailReq;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OrderQueryBySelfFetchRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.model.CombinationProductTO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.*;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.*;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.AfterSaleApply;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderStatusLog;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderAmountInfo;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderRevenueDetailResponse;
import com.meituan.shangou.saas.order.management.client.enums.ChannelType;
import com.meituan.shangou.saas.order.management.client.enums.PriceDisplayType;
import com.meituan.shangou.saas.order.management.client.enums.SortByEnum;
import com.meituan.shangou.saas.order.management.client.enums.SortFieldEnum;
import com.meituan.shangou.saas.order.management.client.enums.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.enums.WaitToAuditRefundGoodsOrderSubTypeEnum;
import com.meituan.shangou.saas.order.management.client.export.dto.request.ExportLimitRequest;
import com.meituan.shangou.saas.order.management.client.export.dto.request.FuseAfsOrderDetailListExportTaskCreateRequest;
import com.meituan.shangou.saas.order.management.client.export.dto.request.FuseOrderListExportTaskCreateRequest;
import com.meituan.shangou.saas.order.management.client.export.dto.request.FuseRefundOrderLisRequest;
import com.meituan.shangou.saas.order.management.client.export.dto.response.ExportFieldResponse;
import com.meituan.shangou.saas.order.management.client.export.dto.response.ExportLimitResponse;
import com.meituan.shangou.saas.order.management.client.export.dto.response.ExportResponse;
import com.meituan.shangou.saas.order.management.client.export.service.ExportFieldThriftService;
import com.meituan.shangou.saas.order.management.client.export.service.FuseOrderListExportThriftService;
import com.meituan.shangou.saas.order.management.client.service.aftersaleapply.AfterSaleApplySearchService;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchService;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.management.client.utils.DesensitizeReceiverInfoUtil;
import com.meituan.shangou.saas.order.management.client.utils.ExchangeUtil;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderLabelModel;
import com.meituan.shangou.saas.order.management.client.utils.OrderUtil;
import com.meituan.shangou.saas.order.management.client.utils.param.DesensitizeReceiverInfoExtParam;
import com.meituan.shangou.saas.order.management.client.utils.result.DesensitizeReceiverInfoResult;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.common.util.OrderItemExtInfoUtil;
import com.meituan.shangou.saas.order.platform.enums.*;
import com.meituan.shangou.saas.order.platform.utils.GsonUtil;
import com.meituan.shangou.saas.service.ocms.OCMSOrderThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ChannelOnlineTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PrintOpLogStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.sankuai.meituan.reco.pickselect.dto.OrderIdentifierDTO;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.PickSelectPrintQueryThriftService;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.dto.OrderPrintQueryDto;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.dto.OrderPrintResultDto;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.request.OrderPrintResultQueryRequest;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.response.OrderPrintResultQueryResponse;
import com.sankuai.meituan.reco.pickselect.query.thrift.fulfill.request.PickDurationRequest;
import com.sankuai.meituan.reco.pickselect.query.thrift.picking.PickingThriftService;
import com.sankuai.meituan.reco.pickselect.query.thrift.picking.response.OrderPickDurationDetailResponse;
import com.sankuai.meituan.reco.pickselect.query.thrift.picking.response.OrderPickDurationResponse;
import com.sankuai.meituan.reco.pickselect.thrift.fulfill.response.ShopPickerResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryOrderType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderCouldOperateItem;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEntityEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.BatchStoreConfigQueryResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TAggDeliveryPlatformConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.DeliveryOperateItem;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.OriginWaybillDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TOrderIdentifier;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.qnh.ofc.transfer.client.thrift.transferorder.request.RecommendTransferSearchReq;
import com.sankuai.qnh.ofc.transfer.client.thrift.transferorder.response.RecommendTransferSearchResponse;
import com.sankuai.qnh.ofc.transfer.client.thrift.transferorder.service.OrderTransferWarehousePlanService;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.request.CreateQnhInvoiceRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryVirtualPhoneRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.CreateQnhInvoiceResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CheckSelfDeliveryCodeResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CommonFuseResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderFuseListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderFuseStatisticsResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderItemFuseListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderItemFuseStatisticsResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderSubStatusCountResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.RefundOrderCountResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.RefundOrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.CompensationVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.InStoreCategoryVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderItemFuseVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.*;
import com.sankuai.shangou.qnh.orderapi.enums.OcmsRefundAuditType;
import com.sankuai.shangou.qnh.orderapi.enums.pc.RefundOrderLabelEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.RefundStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.RefundTypeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.*;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.remote.*;
import com.sankuai.shangou.qnh.orderapi.service.InvoiceService;
import com.sankuai.shangou.qnh.orderapi.service.common.OrderOperateItemsService;
import com.sankuai.shangou.qnh.orderapi.service.pc.AppendDeliveryInfoService;
import com.sankuai.shangou.qnh.orderapi.service.pc.FuseOrderService;
import com.sankuai.shangou.qnh.orderapi.service.pc.TenantService;
import com.sankuai.shangou.qnh.orderapi.service.store.OrderVoService;
import com.sankuai.shangou.qnh.orderapi.utils.CombinationProductUtil;
import com.sankuai.shangou.qnh.orderapi.utils.CommonUsedUtil;
import com.sankuai.shangou.qnh.orderapi.utils.CompensationUtil;
import com.sankuai.shangou.qnh.orderapi.utils.ProductLabelUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConvertUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.DateTimeUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MoneyUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.OrderUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.MultiKeyMap;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.meituan.shangou.saas.order.platform.ExtDataKeyConstants.ExchangeEk.*;
import static com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum.COMMIT;
import static com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum.DRAFT;
import static com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum.FIRST_AUDITED;
import static com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum.FIRST_AUDIT_ING;
import static com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum.FIRST_AUTO_AUDITED;


/**
 * 中台订单服务
 *
 * <AUTHOR>
 * @since 2022/12/8
 */
@Service
@Slf4j
public class FuseOrderServiceImpl implements FuseOrderService {

    @Autowired
    private ChannelOrderRemoteService channelOrderRemoteService;

    @Autowired
    private OrderBizRemoteService orderBizRemoteService;

    @Resource
    private PickSelectPrintQueryThriftService pickSelectPrintQueryThriftService;

    @Resource
    private AppendDeliveryInfoService appendDeliveryInfoService;

    @Resource
    private TmsRemoteService tmsRemoteService;

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    @Resource
    private SacAccountRemoteService sacAccountRemoteService;

    @Resource(name = "queryPickingThriftService")
    private PickingThriftService queryPickingThriftService;

    @Resource(name = "pickingThriftService")
    private com.sankuai.meituan.reco.pickselect.thrift.picking.PickingThriftService pickingThriftService;

    @Resource
    private PickSelectRemoteService pickSelectRemoteService;

    @Resource
    private AfterSaleApplySearchService afterSaleApplySearchService;

    @Resource
    private OcmsOrderSearchService ocmsOrderSearchService;

    @Autowired
    private ExportFieldThriftService exportFieldThriftService;

    @Autowired
    private FuseOrderListExportThriftService fuseOrderListExportThriftService;

    @Autowired
    private OCMSOrderThriftService ocmsOrderThriftService;

    @Autowired
    private TenantService tenantService;

    @Resource
    private TenantConfigRemoteService tenantConfigRemoteService;

    @Resource
    private DeliveryChannelRemoteService deliveryChannelRemoteService;

    @Resource
    private OrderTransferWarehousePlanService orderTransferWarehousePlanService;

    @Resource
    private InvoiceService invoiceService;

    @Autowired
    private FuseOrderService fuseOrderService;

    @Autowired
    private OrderVoService orderVoService;
    @Resource
    private OCMSOrderRemoteService ocmsOrderRemoteService;

    @Resource
    private OcmsChannelRemoteService ocmsChannelRemoteService;

    @Resource
    private OrderLabelRemoteService orderLabelRemoteService;

    private static final int IS_SELF_DELIVERY_NO = 0;

    private static final String UN_KNOW = "未知";

    //履约标签
    private static final int FULFILLMENT_TAG = 1;

    //拣货标准
    private static final int PICK_TAG = 2;

    // TMS 系统，配送无异常的编码
    private static final Integer DELIVERY_NO_EXCEPTION = 0;

    // 系统默认时间
    private static final Integer DEFAULT_TIMESTAMP = 1000;

    private static final List<OrderStatusEnum> NOT_SHOW_DELIVERY_ITEM_ORDER_STATUS = Arrays.asList(OrderStatusEnum.CANCELED,OrderStatusEnum.SUBMIT,OrderStatusEnum.PAYING,OrderStatusEnum.CLOSED);

    static final List<Integer> TO_CHECK_ITEMS = Lists.newArrayList(
            OrderCouldOperateItem.COMPLETE_PICK.getValue(),
            OrderCouldOperateItem.PRINT_RECEIPT.getValue(),
            OrderCouldOperateItem.PART_ORDER_REFUND.getValue(),
            OrderCouldOperateItem.FULL_ORDER_REFUND.getValue(),
            OrderCouldOperateItem.WEIGHT_REFUND.getValue(),
            OrderCouldOperateItem.AFTER_SALE_REFUND.getValue(),
            OrderCouldOperateItem.MONEY_REFUND.getValue()
    );

    private static final Long CUSTOM_PARENT_LABEL_ID = 15L;

    @Override
    @CatTransaction
    public CommonFuseResponse<RefundOrderListResponse> queryRefundOrderList(RefundOrderQueryRequest refundOrderQueryRequest,Long currentStoreId) {
        try {
            checkRefundOrderRequest(refundOrderQueryRequest);

            final OCMSAfterSaleListResponse ocmsAfterSaleListResponse = channelOrderRemoteService.queryRefundOrderList(refundOrderQueryRequest);
            if (ocmsAfterSaleListResponse == null || ocmsAfterSaleListResponse.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
                return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), "系统异常，请稍后重试");
            }

            PageInfoVO pageInfoVO = buildPageInfoVO(refundOrderQueryRequest.getPage(), refundOrderQueryRequest.getPageSize(), ocmsAfterSaleListResponse.getTotalCount());

            RefundOrderListResponse refundOrderListResponse = new RefundOrderListResponse();
            refundOrderListResponse.setPageInfo(pageInfoVO);

            if (CollectionUtils.isEmpty(ocmsAfterSaleListResponse.getOcmsAfterSaleVOList())) {
                return CommonFuseResponse.success(refundOrderListResponse);
            }

            final Map<Long, OCMSOrderVO> orderMap = ocmsAfterSaleListResponse.getOcmsAfterSaleVOList().stream().filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getOrderVO())).map(OCMSAfterSaleVO::getOrderVO).collect(Collectors.toMap(OCMSOrderVO::getOrderId, it -> it, (a, b) -> b));

            OrderFuseListResponse orderFuseListResponse = buildOrderFuseListResponse(new ArrayList<>(orderMap.values()), pageInfoVO,currentStoreId);

            final Map<Long, OrderFuseVO> fuseVOMap = Optional.ofNullable(orderFuseListResponse.getOrderList()).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(OrderFuseVO::getOrderId, it -> it, (a, b) -> b));
            List<OrderLabelModel> refundLabelList = queryOrderShowLabel(refundOrderQueryRequest.getTenantId(), Lists.newArrayList(LabelSourceEnum.COMMON.getValue()), Lists.newArrayList(LabelTypeEnum.REFUND.getValue()));
            final List<RefundApplyRecordBo> collect = ocmsAfterSaleListResponse.getOcmsAfterSaleVOList().stream().filter(item -> Objects.nonNull(item)).map(item -> {
                try {
                    OrderFuseVO orderFuseVO = null;
                    if (!Objects.isNull(item.getOrderId())) {
                        orderFuseVO = fuseVOMap.get(item.getOrderId());
                    }

                    RefundApplyRecordBo refundApplyRecordBo = new RefundApplyRecordBo();
                    setRefundPriceDisplayType4OrderList(Lists.newArrayList(item.getOrderVO()), PriceDisplayType.REFUND_AMOUNT.getCode());
                    //设置订单
                    if (orderFuseVO != null) {
                        orderFuseVO.setOrderTagList(Lists.newArrayList());
                        refundApplyRecordBo.setOrderVO(orderFuseVO);
                        refundApplyRecordBo.setChannelLabelList(ProductLabelUtil.buildChannelLabelVOList(orderFuseVO.getProductList(), item.getAfterSaleApplyDetailVOList()));
                    }
                    refundApplyRecordBo.setServiceId(String.valueOf(item.getServiceId()));
                    refundApplyRecordBo.setAfterSaleId(item.getAfterSaleId());
                    refundApplyRecordBo.setIsAudit(item.getIsAudit());

                    //设置状态和描述
                    setApplyStatusDesc(item, refundApplyRecordBo);
                    refundApplyRecordBo.setStatus(item.getStatus());

                    refundApplyRecordBo.setApplyReason(item.getApplyReason());
                    refundApplyRecordBo.setAfsPattern(item.getAfsPattern());
                    //退单审核之前这个字段可能为0或者负数，展示为【-】,
                    if (item.getRefundAmt() == null || item.getRefundAmt() <= 0) {
                        refundApplyRecordBo.setRefundAmt("-");
                    } else {
                        refundApplyRecordBo.setRefundAmt(item.getRefundAmt().toString());
                    }
                    refundApplyRecordBo.setCreateTime(item.getCreateTime());
                    refundApplyRecordBo.setUpdateTime(item.getUpdateTime());
                    refundApplyRecordBo.setAssignShopId(item.getDispatchShopId());
                    refundApplyRecordBo.setDealTime(item.getDealTime());

                    //仅退款中，通过afsPattern 区分克重退
                    setAfsApplyTypeAndDesc(item, refundApplyRecordBo);

                    //设置申请人类型
                    setWhoAppleType(item, refundApplyRecordBo);
                    refundApplyRecordBo.setRefundProductCount(item.getRefundProductCount());
                    refundApplyRecordBo.setRefundProductCategory(item.getRefundProductCategory());
                    refundApplyRecordBo.setRefundTotalWeight(item.getRefundTotalWeight());
                    refundApplyRecordBo.setAfsPicture(CollectionUtils.isEmpty(item.getRefundPicList()) ? null : item.getRefundPicList());
                    refundApplyRecordBo.setCurrentTime(Instant.now().toEpochMilli());
                    refundApplyRecordBo.setPosStatus(Optional.ofNullable(PosStatusEnum.converterFromOrderPosStatus(item.getPosStatus())).map(PosStatusEnum::getCode).orElse(null));
                    refundApplyRecordBo.setPosErrorMsg(item.getPosErrorMsg());
                    refundApplyRecordBo.setPosCheckTag(item.getPosCheckTag());
                    refundApplyRecordBo.setExistAdjustOrder(item.getExistAdjustOrder());
                    refundApplyRecordBo.setProcessDeadline(item.getProcessDeadline());
                    refundApplyRecordBo.setReturnGoodsStatus(item.getReturnGoodsStatus());
                    refundApplyRecordBo.setChannelExtRefundType(item.getChannelExtRefundType());
                    refundApplyRecordBo.setRefundGoodWay(item.getRefundGoodWay());
                    refundApplyRecordBo.setRefundGoodFreightType(item.getRefundGoodFreightType());
                    if (Objects.nonNull(item.getPickUpRefundGoodsAddress())
                            && !refundApplyRecordBo.getOrderVO().getReceiverAddress().equals(CommonConstant.PRIVACY_PROTECT_ADDRESS)
                            && !item.getPickUpRefundGoodsAddress().equals(refundApplyRecordBo.getOrderVO().getReceiverAddress())) {
                        refundApplyRecordBo.setPickUpRefundGoodsAddress(item.getPickUpRefundGoodsAddress());
                    }
                    //设置超时时间，区分刚创建和一审,需要在setAfsApplyTypeAndDesc后面，以来退款类型
                    setWarnDurtion(item, refundApplyRecordBo);

                    //设置描述
                    setOpContent(item, refundApplyRecordBo);

                    //设置退单日志
                    setAfterSaleLog(item, refundApplyRecordBo);

                    //设置初审时间，根绝退单日志设置
                    setFirstAuditTime(item, refundApplyRecordBo);

                    //设置退单标签
                    List<OrderTagVO> refundOrderTagList = afterSaleVoConvertRefundTag(item.getOrderMark(), refundLabelList);
                    refundApplyRecordBo.setRefundOrderTagList(refundOrderTagList);

                    //设置放心退返回值
                    setWorryFreeReturn(item,refundApplyRecordBo);

                    setReturnDuringDelivery(item,refundApplyRecordBo);
                    //设置退单操作按钮
                    setRefundCouldOperateItems(item,refundApplyRecordBo);
                    return refundApplyRecordBo;
                } catch (Exception e) {
                    log.error("Build response error", e);
                    return null;
                }

            }).filter(Objects::nonNull).collect(Collectors.toList());

            refundOrderListResponse.setRefundApplyRecordVOList(collect);
            return CommonFuseResponse.success(refundOrderListResponse);
        } catch (CommonRuntimeException commonException) {
            log.warn("FuseOrderServiceImpl.queryRefundOrderList commonException error:", commonException);
            return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), commonException.getMessage());
        } catch (Exception e) {
            log.warn("FuseOrderServiceImpl.queryRefundOrderList error:", e);
            return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), "系统异常，请稍后重试");
        }
    }


    private void setReturnDuringDelivery(OCMSAfterSaleVO item, RefundApplyRecordBo refundApplyRecordBo) {
        String extData = item.getExtData();
        if(StringUtils.isNotEmpty(extData)){
            JSONObject jsonObject = JSON.parseObject(extData);

            Boolean isCanReturnGoods = jsonObject.getBoolean("isCanReturnGoods");
            if(isCanReturnGoods!=null){
                refundApplyRecordBo.setIsCanReturnGoods(isCanReturnGoods);
            }
            String returnDuringDeliveryInfoStr = jsonObject.getString("returnDuringDeliveryInfo");
            ChannelReturnDuringDeliveryDTO returnDuringDeliveryInfo = JSON.parseObject(returnDuringDeliveryInfoStr,ChannelReturnDuringDeliveryDTO.class);

            if(returnDuringDeliveryInfo!=null){
                refundApplyRecordBo.setIsReturnGoods(true);
                ReturnGoodsDuringDeliveryInfoVo vo = new ReturnGoodsDuringDeliveryInfoVo();
                vo.setStatus(returnDuringDeliveryInfo.getStatus());
                if(StringUtils.isNotEmpty(returnDuringDeliveryInfo.getDeliveryTime())){
                    vo.setDeliveryTime(Long.parseLong(returnDuringDeliveryInfo.getDeliveryTime()));
                }
                vo.setReason(returnDuringDeliveryInfo.getRejectReason());
                vo.setUTime(Long.parseLong(returnDuringDeliveryInfo.getTime()));
                vo.setPoiCheckResult(returnDuringDeliveryInfo.getPoiCheckResult());
                if(returnDuringDeliveryInfo.getPoiCheckResult()!=0&&StringUtils.isNotEmpty(returnDuringDeliveryInfo.getTime())){
                    vo.setCompleteTime(Long.parseLong(returnDuringDeliveryInfo.getTime()));
                }
                // 驳回原因列表
                List<ReturnGoodsRejectAndCode> rejectReasonList = new ArrayList();
                String refundReasonAndCode = LionUtils.getReturnGoodsRejectReasonAndCode();
                if (StringUtils.isNotBlank(refundReasonAndCode)) {
                    String[] reasonCodePairList = StringUtils.split(refundReasonAndCode, ";");
                    for (String reasonCodePair : reasonCodePairList) {
                        String[] reasonCodePairArray = StringUtils.split(reasonCodePair, ",");
                        if (reasonCodePairArray.length >= 1 && NumberUtils.isCreatable(reasonCodePairArray[0])) {
                            ReturnGoodsRejectAndCode instance = new ReturnGoodsRejectAndCode();
                            instance.setCode(NumberUtils.toInt(reasonCodePairArray[0]));
                            instance.setReason(reasonCodePairArray[1]);
                            rejectReasonList.add(instance);
                        }
                    }
                }

                vo.setRejectReasonList(rejectReasonList);
                refundApplyRecordBo.setReturnGoodsDuringDeliveryInfo(vo);
            }

        }

        }


    private void setWorryFreeReturn(OCMSAfterSaleVO item, RefundApplyRecordBo refundApplyRecordBo) {
        AfterSaleApplyStatusEnum afterSaleApplyStatusEnum = AfterSaleApplyStatusEnum.enumof(item.getStatus());
        AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(item.getAfsApplyType());
        if (afterSaleApplyStatusEnum != null && item.getOrderBizType()== OrderBizTypeEnum.MEITUAN_WAIMAI.getValue()
                && (afterSaleApplyStatusEnum == COMMIT || afterSaleApplyStatusEnum == FIRST_AUDIT_ING)) {
            if (afterSaleTypeEnum != null && afterSaleTypeEnum == AfterSaleTypeEnum.REFUND_GOODS) {
                refundApplyRecordBo.setDirectRefundFlag(1);
            }
        }
        refundApplyRecordBo.setPreReturnFreight(item.getPreReturnFreight());
    }

    private void checkRefundOrderRequest(RefundOrderQueryRequest request) {
        if (StringUtils.isNotEmpty(request.getOrderId()) && request.getOrderId().length() > 4) {
            return;
        }

        int realTimeMonthLimit = LionUtils.getRealTimeOrderQueryLimitOfMonth();
        Long limitTime = DateTimeUtils.getMonthAgoFirstDay(realTimeMonthLimit);
        if (request.getApplyStartTime() != null && request.getApplyEndTime() != null) {
            if (Long.parseLong(request.getApplyStartTime()) < limitTime.longValue() &&
                    Long.parseLong(request.getApplyEndTime()) > limitTime.longValue()) {
                log.warn("申请时间选择跨集群,startTime:{},endTime:{},limitTime:{}", request.getApplyStartTime(), request.getApplyEndTime(), limitTime);
                throw new CommonRuntimeException("近半年的退单无法与半年前的退单一并查询，请重新调整查询时间");
            }
        }

        if (request.getAuditStartTime() != null && request.getAuditEndTime() != null) {
            if (Long.parseLong(request.getAuditStartTime()) < limitTime.longValue() &&
                    Long.parseLong(request.getAuditEndTime()) > limitTime.longValue()) {
                log.warn("审核时间选择跨集群,startTime:{},endTime:{},limitTime:{}", request.getAuditStartTime(), request.getAuditEndTime(), limitTime);
                throw new CommonRuntimeException("近半年的退单无法与半年前的退单一并查询，请重新调整查询时间");
            }
        }

        if (request.getWriteOffStartTime() != null && request.getWriteOffEndTime() != null) {
            if (Long.parseLong(request.getWriteOffStartTime()) < limitTime.longValue() &&
                    Long.parseLong(request.getWriteOffEndTime()) > limitTime.longValue()) {
                log.warn("核销时间选择跨集群,startTime:{},endTime:{},limitTime:{}", request.getWriteOffStartTime(), request.getWriteOffEndTime(), limitTime);
                throw new CommonRuntimeException("近半年的退单无法与半年前的退单一并查询，请重新调整查询时间");
            }
        }

        if (StringUtils.isNotBlank(request.getSkuName()) || StringUtils.isNotBlank(request.getSkuId()) || StringUtils.isNotBlank(request.getUpcCode())) {
            if (DateTimeUtils.compareDateForLower(request.getApplyEndTime(), limitTime)
                    || DateTimeUtils.compareDateForLower(request.getAuditEndTime(), limitTime)
                    || DateTimeUtils.compareDateForLower(request.getWriteOffEndTime(), limitTime)) {
                log.warn("退单列表模糊查询超时间范围");
                throw new CommonRuntimeException("历史退单不支持模糊搜索");
            }
        }
        if (CollectionUtils.isNotEmpty(request.getSkuIdList()) || CollectionUtils.isNotEmpty(request.getUpcCodeList())
                || CollectionUtils.isNotEmpty(request.getErpCodeList())) {
            // 校验租户id是否能根据商品【SKU、条形码、商家ERP编码】查询历史数据
            boolean canHistoricalQuery = LionUtils.checkItemHistoricalQueryTenantId(request.getTenantId());
            if (!canHistoricalQuery) {
                // 校验时间是否满足，和原有的skuId、upcCode保持一致
                if (DateTimeUtils.compareDateForLower(request.getApplyEndTime(), limitTime)
                        || DateTimeUtils.compareDateForLower(request.getAuditEndTime(), limitTime)
                        || DateTimeUtils.compareDateForLower(request.getWriteOffEndTime(), limitTime)) {
                    log.warn("退单列表模糊查询超时间范围");
                    throw new CommonRuntimeException("历史退单不支持模糊搜索");
                }
            }
        }
    }

    public static void afterSaleVoConvertRefundTag(OCMSAfterSaleVO item, RefundApplyRecordBo refundApplyRecordBo) {

        List<OrderTagVO> orderTagVOList = new ArrayList<>();
        if (StringUtils.isNotEmpty(item.getOrderMark())) {
            List<Integer> orderMarkList = Arrays.stream(StringUtils.split(item.getOrderMark(), ","))
                    .map(Integer::parseInt).collect(Collectors.toList());
            for (Integer orderMark : orderMarkList) {
                RefundOrderLabelEnum refundOrderLabelEnum = RefundOrderLabelEnum.getById(orderMark);
                if (Objects.nonNull(refundOrderLabelEnum) && Objects.nonNull(refundOrderLabelEnum.getParentId())) {
                    String desc = refundOrderLabelEnum.getDesc();
                    OrderTagVO orderTagVO = new OrderTagVO();
                    orderTagVO.setType(orderMark);
                    orderTagVO.setName(desc);
                    orderTagVO.setParentType(Math.toIntExact(refundOrderLabelEnum.getParentId()));
                    orderTagVOList.add(orderTagVO);
                }
            }
            refundApplyRecordBo.setRefundOrderTagList(orderTagVOList);
        }
    }

    public List<OrderTagVO> afterSaleVoConvertRefundTag(String orderMark, List<OrderLabelModel> showLabelList) {
        List<OrderTagVO> orderTagVOList = Lists.newArrayList();
        try {
            Map<Long, OrderLabelModel> labelModelMap = showLabelList.stream().collect(Collectors.toMap(OrderLabelModel::getLabelId, Function.identity(), (a, b) -> a));
            if (StringUtils.isNotEmpty(orderMark)) {
                List<Long> orderMarkList = Arrays.stream(StringUtils.split(orderMark, ","))
                        .map(Long::parseLong).collect(Collectors.toList());
                for (Long type : orderMarkList) {
                    OrderLabelModel refundLabelModel = labelModelMap.get(type);
                    if (Objects.nonNull(refundLabelModel)) {
                        OrderTagVO orderTagVO = new OrderTagVO();
                        orderTagVO.setType(Math.toIntExact(refundLabelModel.getLabelId()));
                        orderTagVO.setName(refundLabelModel.getName());
                        orderTagVO.setParentType(Math.toIntExact(refundLabelModel.getParentId()));
                        orderTagVOList.add(orderTagVO);
                    }
                }
            }
        }catch (Exception e){
            log.warn("afterSaleVoConvertRefundTag is error!", e);
        }
        return orderTagVOList;
    }

    //设置一审时间
    private void setFirstAuditTime(OCMSAfterSaleVO ocmsAfterSaleVO, RefundApplyRecordBo refundApplyRecordBo) {
        try {

            final List<Object> firstAuditStatus = Lists.newArrayList(AfterSaleApplyStatusEnum.FIRST_AUDITED.getValue(), AfterSaleApplyStatusEnum.FIRST_AUTO_AUDITED.getValue());
            //状态验证
            if (!firstAuditStatus.contains(ocmsAfterSaleVO.getStatus())) {
                return;
            }

            final List<AfterSaleStatusLogBO> collect = Optional.ofNullable(refundApplyRecordBo.getAfterSaleStatusLogList()).orElse(Lists.newArrayList()).stream().filter(item -> AfterSaleOpEnum.FIRST_AUDIT.getCode() == item.getOperatorType()).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(collect)) {
                refundApplyRecordBo.setFirstAuditTime(Long.valueOf(collect.iterator().next().getOperationTime()));
            }
        } catch (Exception e) {
            log.error("setFirstAuditTime error", e);
        }
    }

    private void setAfterSaleLog(OCMSAfterSaleVO ocmsAfterSaleVO, RefundApplyRecordBo refundApplyRecordBo) {
        if (CollectionUtils.isNotEmpty(ocmsAfterSaleVO.getAfterSaleApplyStatusLogList())) {
            final List<AfterSaleStatusLogBO> afterSaleStatusLogBOList = Lists.newArrayList();

            ocmsAfterSaleVO.getAfterSaleApplyStatusLogList().removeIf(item -> Objects.isNull(item.getTargetStatus()) || Objects.isNull(item.getOperateTime()));

            final List<OCMSAfterSaleStatusLogVO> create = ocmsAfterSaleVO.getAfterSaleApplyStatusLogList().stream().filter(item -> item.getTargetStatus() == COMMIT.getValue()).sorted(Comparator.comparingLong(OCMSAfterSaleStatusLogVO::getOperateTime)).collect(Collectors.toList());
            createAfterSaleStatusLog(create, AfterSaleOpEnum.CREATE, afterSaleStatusLogBOList);

            final List<Object> firstAuditStatus = Lists.newArrayList(AfterSaleApplyStatusEnum.FIRST_AUDITED.getValue(), AfterSaleApplyStatusEnum.FIRST_AUTO_AUDITED.getValue());
            final List<OCMSAfterSaleStatusLogVO> firstAudit = ocmsAfterSaleVO.getAfterSaleApplyStatusLogList().stream().filter(item -> firstAuditStatus.contains(item.getTargetStatus())).sorted(Comparator.comparingLong(OCMSAfterSaleStatusLogVO::getOperateTime)).collect(Collectors.toList());
            createAfterSaleStatusLog(firstAudit, AfterSaleOpEnum.FIRST_AUDIT, afterSaleStatusLogBOList);

            final List<Object> completeStatus = Lists.newArrayList(AfterSaleApplyStatusEnum.AUDITED.getValue(), AfterSaleApplyStatusEnum.FINISH.getValue(), AfterSaleApplyStatusEnum.AUTO_AUDITED.getValue(), AfterSaleApplyStatusEnum.DRAFT_DONE.getValue(), AfterSaleApplyStatusEnum.AUDITED_REJECT.getValue(), AfterSaleApplyStatusEnum.FIRST_AUDITED_REJECT.getValue());
            // 升序排序，取时间最小的
            // 问题，如果驳回后发生申诉，然后再次审核通过过驳回，按什么时间算？
            final List<OCMSAfterSaleStatusLogVO> comlepte = ocmsAfterSaleVO.getAfterSaleApplyStatusLogList().stream().filter(item -> completeStatus.contains(item.getTargetStatus())).sorted(Comparator.comparingLong(OCMSAfterSaleStatusLogVO::getOperateTime)).collect(Collectors.toList());
            createAfterSaleStatusLog(comlepte, AfterSaleOpEnum.COMPLETE, afterSaleStatusLogBOList);

            final List<Object> cancelStatus = Lists.newArrayList(AfterSaleApplyStatusEnum.CANCEL.getValue());
            final List<OCMSAfterSaleStatusLogVO> cancel = ocmsAfterSaleVO.getAfterSaleApplyStatusLogList().stream().filter(item -> cancelStatus.contains(item.getTargetStatus())).sorted(Comparator.comparingLong(OCMSAfterSaleStatusLogVO::getOperateTime)).collect(Collectors.toList());
            createAfterSaleStatusLog(cancel, AfterSaleOpEnum.CANCEL, afterSaleStatusLogBOList);

            if (CollectionUtils.isNotEmpty(afterSaleStatusLogBOList)) {
                refundApplyRecordBo.setAfterSaleStatusLogList(afterSaleStatusLogBOList);
            }

        }

        if (Objects.nonNull(ocmsAfterSaleVO.getPosTime())){
            AfterSaleStatusLogBO afterSaleStatusLogBO = new AfterSaleStatusLogBO();
            afterSaleStatusLogBO.setOperationTime(String.valueOf(ocmsAfterSaleVO.getPosTime()));
            afterSaleStatusLogBO.setOperatorType(AfterSaleOpEnum.WRITEOFF.getCode());
            afterSaleStatusLogBO.setOperatorDesc(AfterSaleOpEnum.WRITEOFF.getDesc());
            if (Objects.isNull(refundApplyRecordBo.getAfterSaleStatusLogList())){
                refundApplyRecordBo.setAfterSaleStatusLogList(Lists.newArrayList());
            }
            refundApplyRecordBo.getAfterSaleStatusLogList().add(afterSaleStatusLogBO);
        }
    }

    private void createAfterSaleStatusLog(List<OCMSAfterSaleStatusLogVO> ocmsAfterSaleStatusLogVOs, AfterSaleOpEnum afterSaleOpEnum, List<AfterSaleStatusLogBO> afterSaleStatusLogBOList) {
        if (CollectionUtils.isNotEmpty(ocmsAfterSaleStatusLogVOs)) {
            final OCMSAfterSaleStatusLogVO ocmsAfterSaleStatusLogVO = ocmsAfterSaleStatusLogVOs.iterator().next();
            AfterSaleStatusLogBO afterSaleStatusLogBO = new AfterSaleStatusLogBO();
            afterSaleStatusLogBO.setOperationTime(ocmsAfterSaleStatusLogVO.getOperateTime() == null ? null : String.valueOf(ocmsAfterSaleStatusLogVO.getOperateTime()));
            afterSaleStatusLogBO.setOperatorType(afterSaleOpEnum.getCode());
            afterSaleStatusLogBO.setOperatorDesc(afterSaleOpEnum.getDesc());
            afterSaleStatusLogBOList.add(afterSaleStatusLogBO);
        }
    }


    private void setOpContent(OCMSAfterSaleVO ocmsAfterSaleVO, RefundApplyRecordBo refundApplyRecordBo) {
        try {
            log.info("whoApplyType:{},channelOrderId:{}", ocmsAfterSaleVO.getWhoApplyType(), ocmsAfterSaleVO.getViewOrderId());
            final OperatorTypeEnum operatorTypeEnum = OperatorTypeEnum.enumOf(ocmsAfterSaleVO.getWhoApplyType());
            if (operatorTypeEnum != null && refundApplyRecordBo.getOrderVO() != null) {
                if (refundApplyRecordBo.getOrderVO().getOrderStatus() == OrderStatusEnum.REFUND_APPLIED.getValue()) {
                    String userName = "";
                    switch (operatorTypeEnum) {
                        case CHANNEL_PLATFORM:
                            userName = "客服介入";
                            if (ocmsAfterSaleVO.getOrderBizType() == OrderBizTypeEnum.JING_DONG.getValue()) {
                                refundApplyRecordBo.setOpContent("系统自动通过");
                            }
                            break;
                        case CALL_CENTER:
                            userName = "客服介入";
                            break;
                        case SYSTEM:
                            if (ocmsAfterSaleVO.getOrderBizType() == OrderBizTypeEnum.JING_DONG.getValue()) {
                                refundApplyRecordBo.setOpContent("系统自动通过");
                            }
                            break;
                        case CUSTOMER:
                            userName = "顾客申请";
                            break;
                        case MERCHANT:
                            userName = "商家发起";
                            break;
                        default:
                            userName = "其他方申请";
                            break;
                    }
                    AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(ocmsAfterSaleVO.getAfsApplyType());

                    switch (afterSaleTypeEnum) {
                        case REFUND:
                            refundApplyRecordBo.setOpContent(userName + "仅退款");
                            break;
                        case REFUND_GOODS:
                            if (Lists.newArrayList(FIRST_AUDITED.getValue(), FIRST_AUTO_AUDITED.getValue()).contains(ocmsAfterSaleVO.getStatus())) {
                                refundApplyRecordBo.setOpContent("商品退回中，请对退货商品进行收货审核");
                            } else {
                                refundApplyRecordBo.setOpContent(userName + "退货退款");
                            }
                            break;
                        case REJECT_BY_CUSTOMER:
                            refundApplyRecordBo.setOpContent("拒收退款");
                            break;
                        case APPEAL:
                            refundApplyRecordBo.setOpContent("申诉处理中，请等待客服处理结果");
                            break;
                        default:
                            break;

                    }
                }
                //待接单，触发极速退款
            } else if (refundApplyRecordBo.getOrderVO().getOrderStatus() == OrderStatusEnum.SUBMIT.getValue()) {
                refundApplyRecordBo.setOpContent("系统自动通过");
            }
        } catch (Exception e) {
            log.error("setOpContent error", e);
        }
    }

    private void setApplyStatusDesc(OCMSAfterSaleVO ocmsAfterSaleVO, RefundApplyRecordBo refundApplyRecordBo) {
        log.info("status:{},channelOrderId:{}", ocmsAfterSaleVO.getStatus(), ocmsAfterSaleVO.getViewOrderId());
        final AfterSaleApplyStatusEnum afterSaleApplyStatusEnum = AfterSaleApplyStatusEnum.enumof(ocmsAfterSaleVO.getStatus());
        if (afterSaleApplyStatusEnum == null || DRAFT.equals(afterSaleApplyStatusEnum)) {
            return;
        }
        switch (afterSaleApplyStatusEnum) {
            case COMMIT:
            case FIRST_AUDITED:
            case FIRST_AUTO_AUDITED:
                refundApplyRecordBo.setStatusDesc(RefundStatusEnum.DEAL_ING.getDesc());
                break;
            case AUDITED:
            case AUTO_AUDITED:
            case FINISH:
            case DRAFT_DONE:
                refundApplyRecordBo.setStatusDesc(RefundStatusEnum.COMPLETE.getDesc());
                break;
            case AUDITED_REJECT:
            case FIRST_AUDITED_REJECT:
                refundApplyRecordBo.setStatusDesc(RefundStatusEnum.REJECT.getDesc());
                break;
            case CANCEL:
                refundApplyRecordBo.setStatusDesc(RefundStatusEnum.CANCEL.getDesc());
                break;
            default:
                log.info("其他无用状态，不返回描述:{}", ocmsAfterSaleVO.getStatus());
                break;
        }

    }


    private void setWhoAppleType(OCMSAfterSaleVO ocmsAfterSaleVO, RefundApplyRecordBo refundApplyRecordBo) {
        log.info("whoApplyType:{},channelOrderId:{}", ocmsAfterSaleVO.getWhoApplyType(), ocmsAfterSaleVO.getViewOrderId());
        final OperatorTypeEnum operatorTypeEnum = OperatorTypeEnum.enumOf(ocmsAfterSaleVO.getWhoApplyType());
        if (operatorTypeEnum == null) {
            return;
        }
        switch (operatorTypeEnum) {
            case CHANNEL_PLATFORM:
            case CALL_CENTER:
                refundApplyRecordBo.setWhoApplyType(ApplyUserEnum.CHANNEL.getCode());
                break;
            case CUSTOMER:
                refundApplyRecordBo.setWhoApplyType(ApplyUserEnum.CUSTOMER.getCode());
                break;
            case MERCHANT:
                refundApplyRecordBo.setWhoApplyType(ApplyUserEnum.MERCHANT.getCode());
                break;
            default:
                refundApplyRecordBo.setWhoApplyType(ApplyUserEnum.OTHER.getCode());
                break;
        }
    }

    private void setAfsApplyTypeAndDesc(OCMSAfterSaleVO ocmsAfterSaleVO, RefundApplyRecordBo refundApplyRecordBo) {
        log.info("AfsApplyType:{},afsPattern:{},channelOrderId:{}", ocmsAfterSaleVO.getAfsApplyType(),
                ocmsAfterSaleVO.getAfsPattern(), ocmsAfterSaleVO.getViewOrderId());
        AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(ocmsAfterSaleVO.getAfsApplyType());
        AfterSalePatternEnum afterSalePatternEnum = AfterSalePatternEnum.enumOf(ocmsAfterSaleVO.getAfsPattern());

        switch (afterSaleTypeEnum) {
            case REFUND:
                //区分克重退，和部分退
                if (AfterSalePatternEnum.WEIGHT.equals(afterSalePatternEnum)) {
                    refundApplyRecordBo.setAfsApplyType(RefundTypeEnum.WEIGHT_REFUND.getCode());
                    refundApplyRecordBo.setAfsApplyTypeDesc(RefundTypeEnum.WEIGHT_REFUND.getDesc());
                } else if (AfterSalePatternEnum.AMOUNT.equals(afterSalePatternEnum)) {
                    refundApplyRecordBo.setAfsApplyType(RefundTypeEnum.AMOUNT_REFUND.getCode());
                    refundApplyRecordBo.setAfsApplyTypeDesc(RefundTypeEnum.AMOUNT_REFUND.getDesc());
                } else if (AfterSalePatternEnum.PART.equals(afterSalePatternEnum)) {
                    refundApplyRecordBo.setAfsApplyType(RefundTypeEnum.PART_REFUND.getCode());
                    refundApplyRecordBo.setAfsApplyTypeDesc(RefundTypeEnum.PART_REFUND.getDesc());
                } else if (AfterSalePatternEnum.ALL.equals(afterSalePatternEnum)) {
                    refundApplyRecordBo.setAfsApplyType(RefundTypeEnum.ALL_REFUND.getCode());
                    refundApplyRecordBo.setAfsApplyTypeDesc(RefundTypeEnum.ALL_REFUND.getDesc());
                } else {
                    refundApplyRecordBo.setAfsApplyType(RefundTypeEnum.REFUND.getCode());
                    refundApplyRecordBo.setAfsApplyTypeDesc(RefundTypeEnum.REFUND.getDesc());
                }
                break;
            case REFUND_GOODS:
                if (AfterSalePatternEnum.AMOUNT.equals(afterSalePatternEnum)) {
                    refundApplyRecordBo.setAfsApplyType(RefundTypeEnum.AMOUNT_REFUND_GOODS.getCode());
                    refundApplyRecordBo.setAfsApplyTypeDesc(RefundTypeEnum.AMOUNT_REFUND_GOODS.getDesc());
                } else if (AfterSalePatternEnum.PART.equals(afterSalePatternEnum)) {
                    refundApplyRecordBo.setAfsApplyType(RefundTypeEnum.PART_REFUND_GOODS.getCode());
                    refundApplyRecordBo.setAfsApplyTypeDesc(RefundTypeEnum.PART_REFUND_GOODS.getDesc());
                } else if (AfterSalePatternEnum.ALL.equals(afterSalePatternEnum)) {
                    refundApplyRecordBo.setAfsApplyType(RefundTypeEnum.ALL_REFUND_GOODS.getCode());
                    refundApplyRecordBo.setAfsApplyTypeDesc(RefundTypeEnum.ALL_REFUND_GOODS.getDesc());
                } else {
                    refundApplyRecordBo.setAfsApplyType(RefundTypeEnum.REFUND_GOODS.getCode());
                    refundApplyRecordBo.setAfsApplyTypeDesc(RefundTypeEnum.REFUND_GOODS.getDesc());
                }
                break;
            case REJECT_BY_CUSTOMER:
                refundApplyRecordBo.setAfsApplyType(RefundTypeEnum.REJECT_BY_CUSTOMER.getCode());
                refundApplyRecordBo.setAfsApplyTypeDesc(RefundTypeEnum.REJECT_BY_CUSTOMER.getDesc());
                break;
            case APPEAL:
                refundApplyRecordBo.setAfsApplyType(RefundTypeEnum.APPEAL.getCode());
                refundApplyRecordBo.setAfsApplyTypeDesc(RefundTypeEnum.APPEAL.getDesc());
                break;
            default:
                log.error("error afterSaleTypeEnum:{}", ocmsAfterSaleVO.getAfsApplyType());
                break;
        }
    }

    private void setWarnDurtion(OCMSAfterSaleVO ocmsAfterSaleVO, RefundApplyRecordBo refundApplyRecordBo) {
        try {
            final String channelTimeOut = LionUtils.getChannelAftersaleTimeout();
            if (StringUtils.isEmpty(channelTimeOut)) {
                return;
            }

            //渠道超时映射
            final Map<String, Map<String, Map<String, String>>> channelTimeOutMap = JacksonUtils.fromJson(channelTimeOut, new TypeReference<Map<String, Map<String, Map<String, String>>>>() {
            });

            //售后类型超时映射
            final Map<String, Map<String, String>> bizTypeMap = channelTimeOutMap.getOrDefault(String.valueOf(ocmsAfterSaleVO.getOrderBizType()), new HashMap<>());

            //状态超时映射，使用状态区分一审二审
            final Map<String, String> afsApplyTypeMap = bizTypeMap.getOrDefault(String.valueOf(refundApplyRecordBo.getAfsApplyType()), new HashMap<>());

            //租户单独配置
            final String tenantDurationTimeout = afsApplyTypeMap.get(ocmsAfterSaleVO.getTenantId() + "-" + ocmsAfterSaleVO.getStatus());

            if (StringUtils.isEmpty(tenantDurationTimeout)) {
                final String defaultWarnDuration = afsApplyTypeMap.get(String.valueOf(ocmsAfterSaleVO.getStatus()));
                refundApplyRecordBo.setWarnDuration(StringUtils.isEmpty(defaultWarnDuration) ? null : Long.valueOf(defaultWarnDuration));
            } else {
                refundApplyRecordBo.setWarnDuration(Long.valueOf(tenantDurationTimeout));
            }
        } catch (Exception e) {
            log.error("set warn duration error", e);
        }
    }


    @Override
    @CatTransaction
    public CommonFuseResponse<OrderFuseListResponse> queryFuseOrders(OrderFuseQueryBO orderFuseQueryBO,Long currentStoreId) {
        //该接口修改，需要在orderbiz的com.meituan.shangou.saas.o2o.service.BizOrderThriftService#queryFuseOrders一起修改
        //该部分涉及PC端的性能优化，详情咨询：lukaixuan02
        OrderFuseListResponse orderListResponse = new OrderFuseListResponse();
        try {
            checkOrderQueryRequest(orderFuseQueryBO);
            // 配送异常数据查询
            if (Objects.nonNull(orderFuseQueryBO.getFuseOrderStatus())
                    && orderFuseQueryBO.getFuseOrderStatus().size() == 1
                    && orderFuseQueryBO.getFuseOrderStatus().get(0) == ChannelOrderNewStatusEnum.SHIPPING_ABNORMAL.getCode()) {
                QueryDeliveryErrorOrderBySubTypeRequest queryDeliveryErrorRequest = new QueryDeliveryErrorOrderBySubTypeRequest();
                queryDeliveryErrorRequest.setSize(orderFuseQueryBO.getPageSize());
                queryDeliveryErrorRequest.setPage(orderFuseQueryBO.getPage());
                queryDeliveryErrorRequest.setTenantId(orderFuseQueryBO.getTenantId());
                queryDeliveryErrorRequest.setViewOrderId(orderFuseQueryBO.getOrderId());
                queryDeliveryErrorRequest.setPoiIdList(orderFuseQueryBO.getPoiIds());
                queryDeliveryErrorRequest.setWarehouseIdList(orderFuseQueryBO.getWarehouseIds());
                return queryDeliveryErrorOrderList(queryDeliveryErrorRequest,currentStoreId);
            }

            OcmsOrderSearchResponseV2 response = channelOrderRemoteService.queryFuseOrders(orderFuseQueryBO);
            Integer responseStatus = Optional.ofNullable(response).map(OcmsOrderSearchResponseV2::getResponseStatus).orElse(-1);
            if (responseStatus != 0) {
                PageInfoVO pageInfoVO = new PageInfoVO();
                pageInfoVO.setPage(orderFuseQueryBO.getPage());
                pageInfoVO.setSize(orderFuseQueryBO.getPageSize());
                pageInfoVO.setTotalPage(0);
                pageInfoVO.setTotalSize(0);
                orderListResponse.setPageInfo(pageInfoVO);
                return CommonFuseResponse.success(orderListResponse);
            }
            PageInfoVo page = response.getPage();
            PageInfoVO pageInfoVO = buildPageInfoVO(page.getPage(), page.getSize(), page.getTotalSize());
            List<OCMSOrderVO> ocmsOrderVOS = Optional.ofNullable(response).map(OcmsOrderSearchResponseV2::getOcmsOrderList)
                    .orElse(Collections.emptyList());

            if (CollectionUtils.isEmpty(ocmsOrderVOS)) {
                return CommonFuseResponse.success(buildOrderFuseListResponse(Collections.emptyList(), pageInfoVO,currentStoreId));
            }

            // 设置退款展示类型
            setRefundPriceDisplayType4OrderList(response.getOcmsOrderList(), PriceDisplayType.REFUND_AMOUNT.getCode());

            // 构造响应结果
            orderListResponse = buildOrderFuseListResponse(orderFuseQueryBO.getTenantId(),
                    ConverterUtils.nonNullConvert(orderFuseQueryBO.getPoiId(), Long::valueOf, 0L),
                    response.getOcmsOrderList(), pageInfoVO,currentStoreId);

            // 设置拣货员信息
            orderVoService.setOrderFuseVOPickerNamesInfo(orderListResponse.getOrderList());

            // 填充打印次数
            orderVoService.fillPrintTimes(orderListResponse.getOrderList());
            //查询操作列表
            if (Objects.nonNull(orderListResponse)) {
                Map<OCMSOrderKey, OrderFuseVO> orderMap = Maps.uniqueIndex(orderListResponse.getOrderList(), order -> new OCMSOrderKey(String.valueOf(order.getChannelOrderId()), order.getChannelId()));

                //操作人权限 10，true/20，false
                Map<Integer, Boolean> accountAuthCodeMap = sacAccountRemoteService.queryAccountCode();

                // order1，10，20，30
                Map<OCMSOrderKey, List<Integer>> orderOperateMap = orderBizRemoteService.queryOrderOperateItems(orderFuseQueryBO.getTenantId(),
                        Lists.newArrayList(orderMap.keySet()), Collections.emptyList());

                Map<OCMSOrderKey, List<Integer>> orderAvailablePermissionCodes = orderOperateMap.entrySet()
                        .stream()
                        .collect(Collectors.toMap(Map.Entry::getKey,
                                entry -> entry.getValue()
                                        .stream()
                                        .filter(item -> !accountAuthCodeMap.containsKey(item) || BooleanUtils.isTrue(accountAuthCodeMap.get(item)))
                                        .collect(Collectors.toList())));

                // 配置可操作按钮
                orderMap.forEach((key, order) -> {
                    List<Integer> canOperateItemList = orderAvailablePermissionCodes.get(key);
                    if(Objects.isNull(canOperateItemList)){
                        canOperateItemList = Lists.newArrayList();
                    }
                    order.setOrderCouldOperateItems(canOperateItemList);
                    if(order.isPlatformDelivery()){
                        order.setOrderCouldOperateItems( order.getOrderCouldOperateItems().stream().filter(e->e!= OrderCanOperateItem.DISPATCH_ORDER.getValue()).collect(Collectors.toList()));
                    }
                    // 【库位指引】按钮
                    if(OrderOperateItemsService.checkRepertoryGuideOp(order.getTenantId(), order.getOrderStatus(), order.getPickStatus())){
                        order.getOrderCouldOperateItems().add(OrderCanOperateItem.REPERTORY_GUIDE.getValue());
                    }
                    if(currentStoreId !=null && order.getDispatchShopId() != null && !Objects.equals(currentStoreId,order.getDispatchShopId())){
                        order.setOrderCouldOperateItems( order.getOrderCouldOperateItems().stream().filter(e->Objects.equals(e,OrderCanOperateItem.PRINT_RECEIPT.getValue())).collect(Collectors.toList()));
                    }
                });
            }

            // 设置订单是否需要转单推荐提示
            setOrderRecommendTransfer(orderFuseQueryBO.getTenantId(), orderListResponse);

            // 添加开票权限
            addCreateInvoiceOp(orderFuseQueryBO.getTenantId(), orderListResponse.getOrderList());

            return CommonFuseResponse.success(orderListResponse);
        } catch (CommonRuntimeException e) {
            log.warn("FuseOrderServiceImpl.queryFuseOrders  error.}", e);
            return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            log.warn("FuseOrderServiceImpl.queryFuseOrders  error.}", e);
            return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), "系统异常，请稍后重试");
        }
    }

    @Override
    public CommonFuseResponse<QueryShopPickerResponse> queryShopPicker(QueryShopPickerRequest request) {
        try{
            QueryShopPickerResponse queryShopPickerResponse = new QueryShopPickerResponse();
            final ShopPickerResponse shopPickerResponse = pickSelectRemoteService.queryShopPicker(request);
            queryShopPickerResponse.setAccountInfoList(shopPickerResponse.getAccountInfoList());
            queryShopPickerResponse.setAutoReceiveMode(shopPickerResponse.getAutoReceiveMode());
            return CommonFuseResponse.success(queryShopPickerResponse);
        }catch (Exception e){
            log.error("FuseOrderServiceImpl.queryShopPicker request:{}", request, e);
            return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @Override
    public CommonFuseResponse<String> transferPicker(TransferOrderRequest request) {
        try {
            if(pickSelectRemoteService.transferPicker(request
                    , ContextHolder.currentUserLoginAppIdFromCookie()
                    , ContextHolder.currentUid()
                    , ContextHolder.currentUserTenantId())){
                return CommonFuseResponse.success("");
            } else {
                return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), "系统异常，请稍后重试");
            }
        } catch (Exception e){
            log.error("FuseOrderServiceImpl.transferPicker request:{}", request, e);
            return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 如果存在开票权限，则添加
     * @param tenantId
     * @param orderList
     */
    private void addCreateInvoiceOp(Long tenantId, List<OrderFuseVO> orderList) {
        try {
            List<Long> createInvoiceShopList = invoiceService.getCreateInvoiceShopList(tenantId);
            Map<String, Map<String, String>> invoiceUrlShopConfig = invoiceService.getInvoiceUrlShopConfig();
            String invoiceUrlForTenant = invoiceService.getInvoiceUrlForTenant(tenantId);
            orderList.forEach(order -> {
                if (invoiceService.checkOrderCreateInvoice(createInvoiceShopList, order.getStoreId(), order.getOrderStatus(), tenantId, invoiceUrlForTenant, invoiceUrlShopConfig))
                    order.getOrderCouldOperateItems().add(OrderCanOperateItem.CREATE_INVOICE.getValue());
            });
        }catch (Exception e){
            log.warn("addCreateInvoiceOp error! --> tenantId={}, orderList={}", tenantId, orderList, e);
        }
    }


    @Override
    public void checkOrderQueryRequest(OrderFuseQueryBO request) {
        int realTimeMonthLimit = LionUtils.getRealTimeOrderQueryLimitOfMonth();
        Long limitTime = DateTimeUtils.getMonthAgoFirstDay(realTimeMonthLimit);
        if (request.getArrivalStartTime() != null && request.getArrivalEndTime() != null) {
            if (Long.parseLong(request.getArrivalStartTime()) < limitTime &&
                    Long.parseLong(request.getArrivalEndTime()) > limitTime) {
                log.warn("预计送达时间选择跨集群,startTime:{},endTime:{},limitTime:{}",request.getArrivalStartTime(),request.getArrivalEndTime(),limitTime);
                throw new CommonRuntimeException("近半年的订单无法与半年前的订单一并查询，请重新调整查询时间");
            }
        }

        if (request.getCompleteTimeStart() != null && request.getCompleteTimeEnd() != null) {
            if (request.getCompleteTimeStart() < limitTime &&
                    request.getCompleteTimeEnd() > limitTime) {
                log.warn("完成时间选择跨集群,startTime:{},endTime:{},limitTime:{}",request.getCompleteTimeStart(),request.getCompleteTimeEnd(),limitTime);
                throw new CommonRuntimeException("近半年的订单无法与半年前的订单一并查询，请重新调整查询时间");
            }
        }

        if (request.getPosTimeStart() != null && request.getPosTimeEnd() != null) {
            if (request.getPosTimeStart() < limitTime && request.getPosTimeEnd() > limitTime) {
                log.warn("核销时间选择跨集群,startTime:{},endTime:{},limitTime:{}",request.getPosTimeStart(),request.getPosTimeEnd(),limitTime);
                throw new CommonRuntimeException("近半年的订单无法与半年前的订单一并查询，请重新调整查询时间");
            }
        }

        if (request.getCreateStartTime() != null && request.getCreateEndTime() != null) {
            if (Long.parseLong(request.getCreateStartTime()) < limitTime &&
                    Long.parseLong(request.getCreateEndTime()) > limitTime) {
                log.warn("下单时间选择跨集群,startTime:{},endTime:{},limitTime:{}",request.getCreateStartTime(),request.getCreateEndTime(),limitTime);
                throw new CommonRuntimeException("近半年的订单无法与半年前的订单一并查询，请重新调整查询时间");
            }
        }

        if (StringUtils.isNotBlank(request.getSkuName())
                || StringUtils.isNotBlank(request.getMemberCard())
                || StringUtils.isNotBlank(request.getReceiverAddress())
                || StringUtils.isNotBlank(request.getReceiverName())
                || (StringUtils.isNotBlank(request.getSmartQuery()) && request.getSmartQuery().length() <= Constants.ORDER_FUZZY_SEARCH_SPLIT_LENGTH)) {
            if (DateTimeUtils.compareDateForLower(String.valueOf(request.getCompleteTimeEnd()), limitTime)
                    ||DateTimeUtils.compareDateForLower(request.getCreateEndTime(), limitTime)
                    || DateTimeUtils.compareDateForLower(request.getArrivalEndTime(), limitTime)
                    || DateTimeUtils.compareDateForLower(String.valueOf(request.getPosTimeEnd()), limitTime)) {
                log.warn("订单列表模糊查询超时间范围");
                throw new CommonRuntimeException("历史订单不支持模糊搜索");
            }
        }
    }

    @Override
    public CommonFuseResponse<CreateQnhInvoiceResponse> createInvoice(CreateQnhInvoiceRequest request) {
        try {
            Long tenantId = ContextHolder.currentUserTenantId();
            if(!request.getOrderToken().equals(OrderUtils.generateMD5Token(request.getChannelOrderId(), request.getChannelId(), tenantId))){
                return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), "参数异常");
            }
            CreateInvoiceResponse invoiceResponse = orderBizRemoteService.createInvoice(tenantId, request.getChannelId(), request.getChannelOrderId());
            if (success(invoiceResponse) && StringUtils.isNotBlank(invoiceResponse.getInvoiceUrl())) {
                return CommonFuseResponse.success(CreateQnhInvoiceResponse.builder()
                        .invoiceUrl(invoiceResponse.getInvoiceUrl())
                        .invoiceSource(invoiceResponse.getInvoiceSource())
                        .invoiceSourceDesc(invoiceResponse.getInvoiceSourceDesc())
                        .build());
            }else if(success(invoiceResponse) && !invoiceResponse.isCanCreateInvoice()){
                return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), "该订单所属渠道暂不支持开发票，可联系牵牛花工作人员咨询。");
            }
            return  CommonFuseResponse.fail(ResultCode.FAIL.getCode(), invoiceResponse.getStatus().getMessage());
        }catch (Exception e){
            log.error("FuseOrderServiceImpl.createInvoice", e);
            return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), "系统异常，请稍后重试");
        }
    }

    @Override
    public List<OrderFuseFinanceDetailBO> queryFinanceDetails(List<Long> serviceIds, Long shopId, Long tenantId, int type) {
        return orderBizRemoteService.queryFinanceDetails(serviceIds, shopId, tenantId, type);
    }

    @Override
    public CommonResponse<Void> returnGoodsAudit(ReturnGoodsAuditRequest request) {

        return orderBizRemoteService.returnGoodsAudit(request.getViewOrderId(), request.getTenantId(), request.getAfterSaleId(), request.getChannelId(),
                request.getAuditResult(), request.getRejectReasonCode(), request.getRejectOtherReason());
    }

    @Override
    public CommonFuseResponse<QueryVirtualPhoneResponse> queryVirtualPhone(QueryVirtualPhoneRequest request) {
        return ocmsChannelRemoteService.queryVirtualPhoneThroughChannel(request);
    }

    @Override
    public CommonResponse<Void> importRefundExchangeInfo(ImportRefundExchangeInfoRequest request) {
        return orderBizRemoteService.importRefundExchangeInfo(request);
    }

    @Override
    public List<UiOptionMultiStage> queryOrderSearchLabel(Long tenantId) {
        try {
            List<OrderLabelModel> orderLabelModelList = orderLabelRemoteService.queryTenantLabel(tenantId, Lists.newArrayList(LabelSourceEnum.COMMON.getValue(), LabelSourceEnum.CUSTOM.getValue()), Lists.newArrayList(LabelTypeEnum.MAIN_ORDER.getValue()), false);
            List<UiOptionMultiStage> uiOptionMultiStages = new ArrayList<>();
            if (CollectionUtils.isEmpty(orderLabelModelList)) {
                return uiOptionMultiStages;
            }
            Map<Long, List<OrderLabelModel>> childLabelMap = orderLabelModelList.stream()
                    .filter(label -> Objects.nonNull(label.getLabelId()) && !Objects.equals(label.getParentId(), label.getLabelId()))
                    .filter(OrderLabelModel::getSupportSearch)
                    .collect(Collectors.groupingBy(OrderLabelModel::getParentId));
            return orderLabelModelList.stream()
                    .filter(label -> Objects.nonNull(label.getLabelId()) && Objects.equals(label.getParentId(), label.getLabelId()))
                    .filter(OrderLabelModel::getSupportSearch)
                    .map(label -> {
                        List<UiChildOption> childOptionList = getChildOptionList(childLabelMap.get(label.getLabelId()), LabelTypeEnum.MAIN_ORDER.getValue());
                        // 自定义标签 如果没有子元素 父元素不展示
                        if(CollectionUtils.isEmpty(childOptionList) && Objects.equals(label.getLabelId(), CUSTOM_PARENT_LABEL_ID)){
                            return null;
                        }
                        return new UiOptionMultiStage(String.valueOf(label.getLabelId()), label.getCode(), label.getName(), childOptionList);
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("FuseOrderServiceImpl.queryOrderSearchLabel error", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<UiOptionMultiStage> queryRefundSearchLabel(Long tenantId) {
        try {
            List<OrderLabelModel> orderLabelModelList = orderLabelRemoteService.queryTenantLabel(tenantId, Lists.newArrayList(LabelSourceEnum.COMMON.getValue(), LabelSourceEnum.CUSTOM.getValue()), Lists.newArrayList(LabelTypeEnum.REFUND.getValue()), false);
            List<UiOptionMultiStage> uiOptionMultiStages = new ArrayList<>();
            if (CollectionUtils.isEmpty(orderLabelModelList)) {
                return uiOptionMultiStages;
            }
            Map<Long, List<OrderLabelModel>> childLabelMap = orderLabelModelList.stream()
                    .filter(label -> Objects.nonNull(label.getLabelId()) && !Objects.equals(label.getParentId(), label.getLabelId()))
                    .filter(OrderLabelModel::getSupportSearch)
                    .collect(Collectors.groupingBy(OrderLabelModel::getParentId));
            return orderLabelModelList.stream()
                    .filter(label -> Objects.nonNull(label.getLabelId()) && Objects.equals(label.getParentId(), label.getLabelId()))
                    .filter(OrderLabelModel::getSupportSearch)
                    .filter(label -> {
                        try {
                            return label.getLabelId() >= Integer.MIN_VALUE && label.getLabelId() <= Integer.MAX_VALUE;
                        } catch (NumberFormatException e) {
                            return false;
                        }
                    })
                    .map(label -> {
                        List<UiChildOption> childOptionList = getChildOptionList(childLabelMap.get(label.getLabelId()), LabelTypeEnum.REFUND.getValue());
                        return new UiOptionMultiStage(Math.toIntExact(label.getLabelId()), label.getCode(), label.getName(), childOptionList);
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("FuseOrderServiceImpl.queryRefundSearchLabel error", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<OrderLabelModel> queryOrderShowLabel(Long tenantId) {
        try {
            if(!com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil.isUseNewOrderMark(tenantId)){
                return new ArrayList<>();
            }
            List<OrderLabelModel> orderLabelModelList = orderLabelRemoteService.queryTenantLabel(tenantId, Lists.newArrayList(LabelSourceEnum.COMMON.getValue(), LabelSourceEnum.CUSTOM.getValue()), Lists.newArrayList(LabelTypeEnum.MAIN_ORDER.getValue()), true);
            if (CollectionUtils.isEmpty(orderLabelModelList)) {
                return new ArrayList<>();
            }
            return orderLabelModelList.stream()
                    .filter(OrderLabelModel::getIsShow)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("FuseOrderServiceImpl.queryOrderShowLabel error", e);
            return new ArrayList<>();
        }
    }

    public List<OrderLabelModel> queryOrderShowLabel(Long tenantId, List<Integer> labelSourceList, List<Integer> labelTypeList) {
        try {
            if(!com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil.isUseNewOrderMark(tenantId)){
                return new ArrayList<>();
            }
            List<OrderLabelModel> orderLabelModelList = orderLabelRemoteService.queryTenantLabel(tenantId, labelSourceList, labelTypeList, true);
            if (CollectionUtils.isEmpty(orderLabelModelList)) {
                return new ArrayList<>();
            }
            return orderLabelModelList.stream()
                    .filter(OrderLabelModel::getIsShow)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("FuseOrderServiceImpl.queryOrderShowLabel error", e);
            return new ArrayList<>();
        }
    }


    public List<UiChildOption> getChildOptionList(List<OrderLabelModel> childLabelList, Integer type) {
        List<UiChildOption> childOptionList = new ArrayList<>();
        if (CollectionUtils.isEmpty(childLabelList)) {
            return childOptionList;
        }
        for (OrderLabelModel childLabel : childLabelList) {
            if (Objects.equals(type, LabelTypeEnum.REFUND.getValue())) {
                childOptionList.add(new UiChildOption(Math.toIntExact(childLabel.getLabelId()), childLabel.getCode(), Math.toIntExact(childLabel.getParentId()), childLabel.getName()));
            }else{
                childOptionList.add(new UiChildOption(String.valueOf(childLabel.getLabelId()), childLabel.getCode(), String.valueOf(childLabel.getParentId()), childLabel.getName()));
            }
        }
        return childOptionList;
    }

    private boolean success(CreateInvoiceResponse response) {
        return response != null && response.getStatus() != null
                && Objects.equals(response.getStatus().getCode(), com.meituan.shangou.saas.dto.StatusCodeEnum.SUCCESS.getCode());
    }

    @Override
    @CatTransaction
    public javafx.util.Pair<OrderFuseDetailBO, Map<Integer, DeliveryChannelDto>> queryDetail(String orderId, Long tenantId, int channelId, Long uid) {
        javafx.util.Pair<OrderFuseDetailBO, Map<Integer, DeliveryChannelDto>> orderFuseDetailBO = channelOrderRemoteService.queryFuseDetail(orderId, tenantId, channelId, uid, true);

        //操作人权限 10，true/20，false
        Map<Integer, Boolean> accountAuthCodeMap = sacAccountRemoteService.queryAccountCode();

        // order1，10，20，30
        Map<OCMSOrderKey, List<Integer>> orderOperateMap = orderBizRemoteService.queryOrderOperateItems(tenantId,
                Collections.singletonList(new OCMSOrderKey(orderId, channelId)), Collections.emptyList());

        Map<OCMSOrderKey, List<Integer>> orderAvailablePermissionCodes = orderOperateMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> entry.getValue()
                                .stream()
                                .filter(item-> !accountAuthCodeMap.containsKey(item) || BooleanUtils.isTrue(accountAuthCodeMap.get(item)))
                                .collect(Collectors.toList())));

        orderFuseDetailBO.getKey().setOrderCouldOperateItems(orderAvailablePermissionCodes.get(new OCMSOrderKey(String.valueOf(orderId), channelId)));

        // 添加开票按钮权限
        addCreateInvoiceOp(tenantId, orderFuseDetailBO.getKey());

        List<OrderLabelModel> showLabelList = queryOrderShowLabel(tenantId);
        if(CollectionUtils.isNotEmpty(showLabelList)){
            orderFuseDetailBO.getKey().setLabelModelList(showLabelList);
        }

        // 配置可操作按钮
        return orderFuseDetailBO;
    }

    private void addCreateInvoiceOp(Long tenantId, OrderFuseDetailBO orderFuseDetailBO){
        try {
            if(tenantId == null || tenantId == 0L ||Objects.isNull(orderFuseDetailBO) || Objects.isNull(orderFuseDetailBO.getBaseInfo())){
                log.warn("addCreateInvoiceOp failed! data is null --> tenantId={}, orderFuseDetailBO={}", tenantId, orderFuseDetailBO);
            }
            List<Long> createInvoiceShopList = invoiceService.getCreateInvoiceShopList(tenantId);
            Map<String, Map<String, String>> invoiceUrlShopConfig = invoiceService.getInvoiceUrlShopConfig();
            String invoiceUrlForTenant = invoiceService.getInvoiceUrlForTenant(tenantId);
            if(invoiceService.checkOrderCreateInvoice(createInvoiceShopList, orderFuseDetailBO.getBaseInfo().getPoiId(), orderFuseDetailBO.getBaseInfo().getFuseOrderStatusCode(), tenantId, invoiceUrlForTenant, invoiceUrlShopConfig)) {
                orderFuseDetailBO.getOrderCouldOperateItems().add(OrderCanOperateItem.CREATE_INVOICE.getValue());
            }
        }catch (Exception e){
            log.warn("addCreateInvoiceOp is error! --> tenantId={}, orderFuseDetailBO={}", tenantId, orderFuseDetailBO, e);
        }
    }

    @Override
    @CatTransaction
    public OrderFuseFinanceDetailBO queryFinanceDetail(Long orderId, Long shopId, Long tenantId, int type) {
        return orderBizRemoteService.queryFinanceDetail(orderId, shopId, tenantId, type);
    }

    @Override
    @CatTransaction
    public List<OrderFuseFinanceDetailBO> queryMultiShopFinanceDetail(List<Long> orderIdList, List<Long> shopIdList, Long tenantId, int type) {
        return orderBizRemoteService.queryMultiShopFinanceDetail(orderIdList, shopIdList, tenantId, type);
    }

    @Override
    @CatTransaction
    public CommonFuseResponse<OrderFuseListResponse> queryDeliveryErrorOrderList(QueryDeliveryErrorOrderBySubTypeRequest request,Long curStoreId) {
        PageInfoVO pageInfoVO;
        List<TOrderIdentifier> tOrderIdentifiers;

        try {
            final Pair<Integer, List<TOrderIdentifier>> integerListPair = tmsRemoteService.queryDeliveryErrorOrders(request);
            pageInfoVO = buildPageInfoVO(request.getPage(), request.getSize(), integerListPair.getLeft());
            tOrderIdentifiers = integerListPair.getRight();
            if (CollectionUtils.isEmpty(tOrderIdentifiers)) {
                OrderFuseListResponse response = new OrderFuseListResponse();
                response.setPageInfo(pageInfoVO);
                response.setOrderList(com.google.common.collect.Lists.newArrayList());
                return CommonFuseResponse.success(response);
            }
        } catch (Exception e) {
            log.error("tmsServiceWrapper queryDeliveryErrorOrders error", e);
            throw new CommonRuntimeException(e);
        }

        // 请求 orderMng 获取订单详情
        try {
            OCMSListViewIdConditionRequest viewIdConditionRequest = buildOCMSListViewIdConditionRequestByTOrderIdentifier(tOrderIdentifiers);
            log.info("OCMSOrderServiceWrapper.queryDeliveryErrorBySubType  ocmsQueryThriftService.queryOrderByViewIdCondition request:{}",
                    viewIdConditionRequest);
            OCMSListViewIdConditionResponse viewIdConditionResponse = ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
            log.info("OCMSOrderServiceWrapper.queryDeliveryErrorBySubType  ocmsQueryThriftService.queryOrderByViewIdCondition response:{}",
                    viewIdConditionResponse);
            if (viewIdConditionResponse.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                log.info("获取订单详情失败, viewOrderId:{}",
                        viewIdConditionRequest.getViewIdConditionList().stream().map(e -> e.getViewOrderId()).collect(Collectors.toList()));
                return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), viewIdConditionResponse.getStatus().getMessage());
            }

            // 检查目标订单数和返回订单数是否相等，若不等则埋点
            checkViewResponseCount(viewIdConditionRequest, viewIdConditionResponse);

            // 设置退款展示类型
            setRefundPriceDisplayType4OrderList(viewIdConditionResponse.getOcmsOrderList(), PriceDisplayType.REFUND_AMOUNT.getCode());
            List<OCMSOrderVO> ocmsOrderVOList=viewIdConditionResponse.getOcmsOrderList().stream().filter(new Predicate<OCMSOrderVO>() {
                @Override
                public boolean test(OCMSOrderVO ocmsOrderVO) {
                    return ocmsOrderVO.getOrderStatus()!=OrderStatusEnum.CANCELED.getValue();
                }
            }).collect(Collectors.toList());
            OrderFuseListResponse orderListResponse = buildOrderFuseListResponse(ocmsOrderVOList, pageInfoVO,curStoreId);
            setDeliveryErrorListSort(orderListResponse);
            setOrderListResponseViewStatusByExceptionSubType(orderListResponse, tOrderIdentifiers);

            // 配送异常 tab，仅保留取消订单
            if(CollectionUtils.isNotEmpty(orderListResponse.getOrderList())){
                Map<OCMSOrderKey, OrderFuseVO> orderMap = Maps.uniqueIndex(orderListResponse.getOrderList(), order -> new OCMSOrderKey(String.valueOf(order.getChannelOrderId()), order.getChannelId()));
                //操作人权限
                Map<Integer, Boolean> accountAuthCodeMap = sacAccountRemoteService.queryAccountCode();
                log.info("查询权限系统获取可操作项 accountAuthCodeMap:{}", accountAuthCodeMap);

                //订单操作项
                Map<OCMSOrderKey, List<Integer>> orderOperateMap = orderBizRemoteService.queryOrderOperateItems(request.getTenantId(),
                        Lists.newArrayList(orderMap.keySet()), Collections.emptyList());

                Map<OCMSOrderKey, List<Integer>> orderAvailablePermissionCodes = orderOperateMap.entrySet()
                        .stream()
                        .collect(Collectors.toMap(Map.Entry::getKey,
                                entry -> entry.getValue()
                                        .stream()
                                        .filter(item-> !accountAuthCodeMap.containsKey(item) || BooleanUtils.isTrue(accountAuthCodeMap.get(item)))
                                        .collect(Collectors.toList())));
                // 配置可操作按钮
                orderMap.forEach((key, order) -> order.setOrderCouldOperateItems(orderAvailablePermissionCodes.get(key)));
                justKeepPartOperateItems(orderListResponse, Lists.newArrayList(OrderCanOperateItem.FULL_ORDER_REFUND,
                        OrderCanOperateItem.WEIGHT_REFUND, OrderCanOperateItem.MONEY_REFUND, OrderCanOperateItem.CREATE_INVOICE, OrderCanOperateItem.SETTING_ORDER_TAG));
            }
            return CommonFuseResponse.success(orderListResponse);
        } catch (Exception e) {
            log.error("OCMSOrderServiceWrapper.queryDeliveryErrorBySubType 调用 ocmsQueryThriftService.queryOrderByViewIdCondition error", e);
            throw new CommonRuntimeException(e);
        }
    }

    @Override
    @CatTransaction
    public CommonFuseResponse<OrderSubStatusCountResponse> queryWaitDeliverySubTypeCount(OrderFuseQueryBO orderFuseQueryBO) {
        try {
            checkOrderQueryRequest(orderFuseQueryBO);
            OrderSubStatusCountResponse response = new OrderSubStatusCountResponse();
            for (ChannelOrderNewStatusEnum statusEnum : ChannelOrderNewStatusEnum.values()) {
                switch (statusEnum) {
                    case PAYED:
                        orderFuseQueryBO.setFuseOrderStatus(Collections.singletonList(ChannelOrderNewStatusEnum.PAYED.getCode()));
                        OcmsOrderSearchResponseV2 waitToTakeOrderResponse = channelOrderRemoteService.queryFuseOrders(orderFuseQueryBO);
                        response.setWaitToTakeOrderCount(getTotalSize(waitToTakeOrderResponse));
                        break;
                    case MERCHANT_CONFIRMED:
                        orderFuseQueryBO.setFuseOrderStatus(Collections.singletonList(ChannelOrderNewStatusEnum.MERCHANT_CONFIRMED.getCode()));
                        OcmsOrderSearchResponseV2 waitToPickResponse = channelOrderRemoteService.queryFuseOrders(orderFuseQueryBO);
                        response.setWaitToPickCount(getTotalSize(waitToPickResponse));
                        break;
                    case PICKED:
                        orderFuseQueryBO.setFuseOrderStatus(Collections.singletonList(ChannelOrderNewStatusEnum.PICKED.getCode()));
                        OcmsOrderSearchResponseV2 pickFinishResponse = channelOrderRemoteService.queryFuseOrders(orderFuseQueryBO);
                        response.setPickFinishCount(getTotalSize(pickFinishResponse));
                        break;
                    case SHIPPING:
                        orderFuseQueryBO.setFuseOrderStatus(Collections.singletonList(ChannelOrderNewStatusEnum.SHIPPING.getCode()));
                        OcmsOrderSearchResponseV2 deliveringCountResponse = channelOrderRemoteService.queryFuseOrders(orderFuseQueryBO);
                        response.setDeliveringCount(getTotalSize(deliveringCountResponse));
                        break;
                    case COMPLETED:
                        orderFuseQueryBO.setFuseOrderStatus(Collections.singletonList(ChannelOrderNewStatusEnum.COMPLETED.getCode()));
                        OcmsOrderSearchResponseV2 orderCompletedResponse = channelOrderRemoteService.queryFuseOrders(orderFuseQueryBO);
                        response.setOrderCompletedCount(getTotalSize(orderCompletedResponse));
                        break;
                    case CANCELED:
                        orderFuseQueryBO.setFuseOrderStatus(Collections.singletonList(ChannelOrderNewStatusEnum.CANCELED.getCode()));
                        OcmsOrderSearchResponseV2 orderCanceledResponse = channelOrderRemoteService.queryFuseOrders(orderFuseQueryBO);
                        response.setOrderCanceledCount(getTotalSize(orderCanceledResponse));
                        break;
                    case CANCELING:
                        orderFuseQueryBO.setFuseOrderStatus(Collections.singletonList(ChannelOrderNewStatusEnum.CANCELING.getCode()));
                        OcmsOrderSearchResponseV2 orderCancelingResponse = channelOrderRemoteService.queryFuseOrders(orderFuseQueryBO);
                        response.setOrderCancelingCount(getTotalSize(orderCancelingResponse));
                        break;
                    case SHIPPING_ABNORMAL:
                        response.setDeliveringErrorCount(tmsRemoteService.queryDeliveryErrorOrdersCount(orderFuseQueryBO));
                        break;
                    case WAIT_SELF_FETCH:
                        orderFuseQueryBO.setFuseOrderStatus(Collections.singletonList(ChannelOrderNewStatusEnum.WAIT_SELF_FETCH.getCode()));
                        OcmsOrderSearchResponseV2 orderWaitSelfFetchResponse = channelOrderRemoteService.queryFuseOrders(orderFuseQueryBO);
                        response.setWaitToSelfFetchCount(getTotalSize(orderWaitSelfFetchResponse));
                        break;
                    default:
                        log.warn("订单的状态不在统计范围内，配送状态:{}.", statusEnum.getCode());
                        break;
                }
            }

            if (tenantConfigRemoteService.isMedicineUnmannedWarehouseTenant(orderFuseQueryBO.getTenantId())) {
                // 仅医药无人仓有待取货，待取货=待拣货+拣货完成
                response.setWaitToTakeGoodsCount(response.getWaitToPickCount() + response.getPickFinishCount());
            }

            response.setAllSubStatuseCount(response.getDeliveringCount() + response.getOrderCompletedCount() + response.getOrderCanceledCount()
                    + response.getWaitToTakeOrderCount() + response.getWaitToPickCount() + response.getPickFinishCount() + response.getOrderCancelingCount()
                    + response.getWaitToSelfFetchCount());

            return CommonFuseResponse.success(response);
        } catch (Exception e) {
            log.error("FuseOrderServiceImpl.queryWaitDeliverySubTypeCount 查询订单子状态数量 error", e);
            OrderSubStatusCountResponse response = new OrderSubStatusCountResponse();
            response.setAllSubStatuseCount(0);
            response.setWaitToTakeOrderCount(0);
            response.setWaitToPickCount(0);
            response.setOrderCancelingCount(0);
            response.setOrderCompletedCount(0);
            response.setDeliveringCount(0);
            response.setPickFinishCount(0);
            response.setOrderCanceledCount(0);
            response.setDeliveringErrorCount(0);
            response.setOrderCancelingCount(0);
            response.setWaitToSelfFetchCount(0);
            response.setWaitToTakeGoodsCount(0);
            return CommonFuseResponse.success(response);
        }

    }

    /**
     * 获取查询总数量
     * @param orderResponse
     * @return
     */
    private Integer getTotalSize(OcmsOrderSearchResponseV2 orderResponse){
        return Objects.nonNull(orderResponse) && Objects.nonNull(orderResponse.getPage()) ? orderResponse.getPage().getTotalSize() : 0;
    }

    @Override
    @CatTransaction
    public CommonFuseResponse<OrderItemFuseListResponse> queryFuseOrderItems(OrderItemFuseQueryBO orderItemFuseQueryBO) {
        try {
            checkFuseOrderItemsRequest(orderItemFuseQueryBO);

            OrderItemFuseSearchResponse response = channelOrderRemoteService.queryFuseOrderItems(orderItemFuseQueryBO);
            PageInfoVo page = response.getPage();
            PageInfoVO pageInfoVO = buildPageInfoVO(page.getPage(), page.getSize(), page.getTotalSize());

            // 构造响应结果
            OrderItemFuseListResponse orderListResponse = buildOrderItemFuseListResponse(response.getOrderItemFuseList(), pageInfoVO);

            // 查询财务数据
            mergeFinanceInfo(orderListResponse, orderItemFuseQueryBO.getTenantId());


            return CommonFuseResponse.success(orderListResponse);
        } catch (CommonRuntimeException commonException) {
            log.warn("FuseOrderServiceImpl.queryFuseOrderItems commonException error:", commonException);
            return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), commonException.getMessage());
        } catch (Exception e) {
            log.warn("FuseOrderServiceImpl.queryFuseOrderItems error:", e);
            return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), "系统异常，请稍后重试");
        }
    }

    private void mergeFinanceInfo(OrderItemFuseListResponse orderListResponse, Long tenantId) {
        try {
            if(CollectionUtils.isEmpty(orderListResponse.getItemList())){
                return;
            }
            List<Long> orderIdList = orderListResponse.getItemList().stream().map(OrderItemFuseVO::getOrderId).collect(Collectors.toList());
            List<Long> shopIdList = orderListResponse.getItemList().stream().map(OrderItemFuseVO::getPoiId).collect(Collectors.toList());
            if(!isContainComposeItem(orderListResponse)){
                return;
            }
            List<OrderFuseFinanceDetailBO> orderFuseFinanceDetailBOList = fuseOrderService.queryMultiShopFinanceDetail(orderIdList, shopIdList, tenantId, 1);
            Map<Long, OrderFuseFinanceDetailBO.ComposeItemFinanceInfo> composeItemFinanceInfoMap = orderFuseFinanceDetailBOList.stream()
                    .flatMap(item -> item.getItemInfos().stream())
                    .filter(item -> Objects.nonNull(item) && CollectionUtils.isNotEmpty(item.getComposeItemFinanceInfos()))
                    .flatMap(item -> item.getComposeItemFinanceInfos().stream())
                    .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getOrderItemId()))
                    .collect(Collectors.toMap(OrderFuseFinanceDetailBO.ComposeItemFinanceInfo::getOrderItemId, Function.identity(), (a, b) -> a));
            orderListResponse.mergeFinanceInfo(composeItemFinanceInfoMap);
        } catch (Exception e) {
            log.warn("FuseOrderServiceImpl.mergeFinanceInfo error", e);
        }
    }

    /**
     * 是否包含组合品
     * @param orderListResponse
     * @return
     */
    private boolean isContainComposeItem(OrderItemFuseListResponse orderListResponse){
        boolean isBNotEmpty = false;
        for (OrderItemFuseVO itemFuseVO : orderListResponse.getItemList()){
            if(Objects.nonNull(itemFuseVO) && CollectionUtils.isNotEmpty(itemFuseVO.getSubProduct())){
                isBNotEmpty = true;
                break;
            }
        }
        return isBNotEmpty;
    }

    @Override
    public void checkFuseOrderItemsRequest(OrderItemFuseQueryBO request) {
        int realTimeMonthLimit = LionUtils.getRealTimeOrderQueryLimitOfMonth();
        Long limitTime = DateTimeUtils.getMonthAgoFirstDay(realTimeMonthLimit);
        if (request.getCreateStartTime() != null && request.getCreateEndTime() != null) {
            if (Long.parseLong(request.getCreateStartTime()) < limitTime.longValue() &&
                    Long.parseLong(request.getCreateEndTime()) > limitTime.longValue()) {
                log.warn("下单时间选择跨集群,startTime:{},endTime:{},limitTime:{}", request.getCreateStartTime(), request.getCreateEndTime(), limitTime);
                throw new CommonRuntimeException("近半年的订单无法与半年前的订单一并查询，请重新调整查询时间");
            }
        }

        if (CollectionUtils.isNotEmpty(request.getSkuIdList()) || CollectionUtils.isNotEmpty(request.getUpcList())
                || CollectionUtils.isNotEmpty(request.getErpCodeList())) {
            boolean canHistoricalQuery = LionUtils.checkItemHistoricalQueryTenantId(request.getTenantId());
            if (!canHistoricalQuery && DateTimeUtils.compareDateForLower(request.getCreateEndTime(), limitTime)) {
                log.warn("订单明细列表模糊查询超时间范围");
                throw new CommonRuntimeException("历史订单不支持模糊搜索");
            }
        }
    }

    @Override
    @CatTransaction
    public CommonFuseResponse<List<ExportFieldsVO>> queryExportProfitLoss(ExportTypeRequest request) {
        try {
            ExportFieldResponse exportFieldResponse = exportFieldThriftService.exportFields(request.getType());
            log.info("FuseOrderServiceImpl.queryExportProfitLoss exportFieldResponse:{}", exportFieldResponse);
            List<ExportFieldsVO> exportFieldsVOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(exportFieldResponse.getExportFieldDetailResponseList())) {
                exportFieldResponse.getExportFieldDetailResponseList().forEach(item -> {
                    exportFieldsVOList.add(ExportFieldsVO.builder()
                                    .label(item.getCellName())
                                    .value(item.getCellKey())
                            .build());
                });
            }

            List<String> skuExportList = Lists.newArrayList("orderItemList", "refundItemList");

            //去掉不需要显示的字段
            exportFieldsVOList.removeIf(item->"batchNo".equals(item.getValue()));
            exportFieldsVOList.removeIf(item->"locationCode".equals(item.getValue()));
            exportFieldsVOList.removeIf(item->"productionDate".equals(item.getValue()));

            //非【超市便利】方向的租户不展示这些字段。
            boolean isSuperMarket = tenantService.isSuperMarketMode(ContextHolder.currentUserTenantId());
            if (!isSuperMarket){
                exportFieldsVOList.removeIf(item -> "erpShopCode".equals(item.getValue()));
                exportFieldsVOList.removeIf(item -> "outShopId".equals(item.getValue()));
                exportFieldsVOList.removeIf(item -> "posStatus".equals(item.getValue()));
                exportFieldsVOList.removeIf(item -> "posErrorMsg".equals(item.getValue()));
            }

            //非ERP商家，不导出商品编码
            Boolean hasErp = tenantService.queryTenantHasErp(ContextHolder.currentUserTenantId());
            if (skuExportList.contains(request.getType()) && !hasErp) {
                exportFieldsVOList.removeIf(item -> "erpItemCode".equals(item.getValue()));
            }
            try {
                // 该租户是否支持订单商品层级分类
                boolean supportItemLevelCategory = com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil.checkSupportOrderItemLevelCategoryErpTenants(ContextHolder.currentUserTenantId());
                if (skuExportList.contains(request.getType())) {
                    // 支持 && ERP租户 --> 不展示【店内分类】
                    if (supportItemLevelCategory && Boolean.TRUE.equals(hasErp)) {
                        exportFieldsVOList.removeIf(item -> "inStoreCategory".equals(item.getValue()));
                    } else {
                        // 不支持 || 非ERP租户 --> 不展示【一级分类，二级分类，辅助分类】
                        exportFieldsVOList.removeIf(item -> "inStoreCategoryFirst".equals(item.getValue()));
                        exportFieldsVOList.removeIf(item -> "inStoreCategorySecond".equals(item.getValue()));
                        exportFieldsVOList.removeIf(item -> "inStoreCategoryAuxiliary".equals(item.getValue()));
                    }
                }
            }catch(Exception e){
                log.warn("排除订单商品层级分类异常！", e);
            }
            //未开启批次管理的商家，不导出批次信息
            boolean isManageBatch = tenantConfigRemoteService.isTenantManageBatchStock(ContextHolder.currentUserTenantId());
            if (skuExportList.contains(request.getType()) && isManageBatch) {
                exportFieldsVOList.add(ExportFieldsVO.builder()
                                .label("出库明细")
                                .value("batchInfo")
                        .build());
            }
            
            // 非医药无人仓租户不展示以下字段：仓名称
            boolean isUnmannedWarehouse = tenantConfigRemoteService.isMedicineUnmannedWarehouseTenant(ContextHolder.currentUserTenantId());
            if (!isUnmannedWarehouse) {
                exportFieldsVOList.removeIf(item->"warehouseName".equals(item.getValue()));
            }

            //非ERP商家，不导出核销信息
            List<String> orderExportList = Lists.newArrayList("orderList", "refundList");
            if (orderExportList.contains(request.getType()) && !hasErp) {
                List<String> notHasErpExcludeFields = Arrays.asList("posTime","posStatus","posErrorMsg","writeOffTime","erpShopCode");
                exportFieldsVOList.removeIf(item -> notHasErpExcludeFields.contains(item.getValue()));
            }
            boolean hasCalcType = LionUtils.isNeedRecordPurchasePriceTenant(ContextHolder.currentUserTenantId());
            if (!hasCalcType){
                exportFieldsVOList.removeIf(item -> "poiPurchasePrice".equals(item.getValue()));
            }
            return CommonFuseResponse.success(exportFieldsVOList);
        } catch (Exception e) {
            log.warn("FuseOrderServiceImpl.queryExportProfitLoss  error.}", e);
            return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), "系统异常，请稍后重试");
        }
    }

    @Override
    @CatTransaction
    public Result<String> exportOrderListTask(OrderFuseQueryBO orderFuseQueryBO) {
        try {
            checkOrderQueryRequest(orderFuseQueryBO);
            // 查询导出限制，判断是否超过导出限制
            checkExportCountLimit(
                    ExportLimitRequest.builder().orderListReq(orderFuseQueryBO.toExportFuseOrderListRequest()).build());
            ExportResponse response = fuseOrderListExportThriftService.exportFuseOrdersList(orderFuseQueryBO.toExportFuseOrderListRequest());
            ResponseHandler.checkResponseAndStatus(response, rep -> rep.getStatus().getCode(), rep -> rep.getStatus().getMessage());
            return ResultBuilder.buildSuccess(response.getToken());
        } catch (CommonRuntimeException commonException) {
            log.warn("FuseOrderServiceImpl.exportOrderListTask commonException error:", commonException);
            return ResultBuilder.buildFailResult(commonException.getMessage());
        } catch (Exception e) {
            log.warn("FuseOrderServiceImpl.exportOrderListTask error:", e);
            return ResultBuilder.buildFailResult(e.getMessage());
        }

    }

    @Override
    @CatTransaction
    public Result<String> exportRefundOrderList(RefundOrderQueryRequest refundOrderQueryRequest) {
        try {
            checkRefundOrderRequest(refundOrderQueryRequest);
            FuseRefundOrderLisRequest refundOrderLisRequest = new FuseRefundOrderLisRequest();
            final OCMSListRefundOrderRequest ocmsListRefundOrderRequest = refundOrderQueryRequest.toOcmsListRefundOrderRequest();
            refundOrderLisRequest.setOcmsListRefundOrderRequest(ocmsListRefundOrderRequest);
            refundOrderLisRequest.setOperatorId(ContextHolder.currentUid());
            refundOrderLisRequest.setExportFieldsList(refundOrderQueryRequest.getExportFields());
            // 查询导出限制，判断是否超过导出限制
            checkExportCountLimit(ExportLimitRequest.builder().refundOrderLisReq(refundOrderLisRequest).build());
            ExportResponse response = fuseOrderListExportThriftService.exportRefundOrderList(refundOrderLisRequest);
            ResponseHandler.checkResponseAndStatus(response, rep -> rep.getStatus().getCode(), rep -> rep.getStatus().getMessage());
            return ResultBuilder.buildSuccess(response.getToken());
        } catch (CommonRuntimeException commonException) {
            log.warn("FuseOrderServiceImpl.exportRefundOrderList commonException error:", commonException);
            return ResultBuilder.buildFailResult(commonException.getMessage());
        } catch (Exception e) {
            log.warn("FuseOrderServiceImpl.exportRefundOrderList error:", e);
            return ResultBuilder.buildFailResult(e.getMessage());
        }
    }

    @Override
    @CatTransaction
    public Result<String> exportRefundOrderDetailList(RefundOrderQueryRequest refundOrderQueryRequest) {
        try {
            checkRefundOrderRequest(refundOrderQueryRequest);

            FuseAfsOrderDetailListExportTaskCreateRequest fuseAfsOrderDetailListExportTaskCreateRequest = new FuseAfsOrderDetailListExportTaskCreateRequest();
            final OCMSListRefundOrderRequest ocmsListRefundOrderRequest = refundOrderQueryRequest.toOcmsListRefundOrderRequest();
            fuseAfsOrderDetailListExportTaskCreateRequest.setOrderItemFuseListReq(ocmsListRefundOrderRequest);
            fuseAfsOrderDetailListExportTaskCreateRequest.setOperatorId(ContextHolder.currentUid());
            fuseAfsOrderDetailListExportTaskCreateRequest.setExportFieldsList(refundOrderQueryRequest.getExportFields());
            // 查询导出限制，判断是否超过导出限制
            checkExportCountLimit(ExportLimitRequest.builder()
                    .afsOrderDetailListReq(fuseAfsOrderDetailListExportTaskCreateRequest).build());

            ExportResponse response = fuseOrderListExportThriftService.exportFuseAfsOrderDetailList(fuseAfsOrderDetailListExportTaskCreateRequest);
            ResponseHandler.checkResponseAndStatus(response, rep -> rep.getStatus().getCode(), rep -> rep.getStatus().getMessage());
            return ResultBuilder.buildSuccess(response.getToken());
        } catch (CommonRuntimeException commonException) {
            log.warn("FuseOrderServiceImpl.exportRefundOrderDetailList commonException error:", commonException);
            return ResultBuilder.buildFailResult(commonException.getMessage());
        } catch (Exception e) {
            log.warn("FuseOrderServiceImpl.exportRefundOrderDetailList error:", e);
            return ResultBuilder.buildFailResult(e.getMessage());
        }
    }

    @Override
    public CommonResponse<DeliveryStatusVO> queryDeliveryDetail(OrderDeliveryDetailRequest request) {

        TOrderIdentifier orderIdentifier = new TOrderIdentifier();
        orderIdentifier.setChannelOrderId(request.getChannelOrderId());
        orderIdentifier.setOrderBizTypeCode(ConvertUtils.convertChannelId2OrderBizType(request.getChannelId()));
        OCMSListViewIdConditionRequest viewIdConditionRequest = buildOCMSListViewIdConditionRequestByTOrderIdentifier(Arrays.asList(orderIdentifier));
        OCMSListViewIdConditionResponse viewIdConditionResponse = ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
        if (viewIdConditionResponse.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue() || CollectionUtils.isEmpty(viewIdConditionResponse.getOcmsOrderList())) {
            log.info("查询订单订单详情为空, request:{}", viewIdConditionRequest);
            MetricHelper.build().name("orderDetailIsNull").count();
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询订单订单详情为空");
        }
        OCMSOrderVO ocmsOrderVO = viewIdConditionResponse.getOcmsOrderList().get(0);
        List<OrderLabelModel> showLabelList = queryOrderShowLabel(ContextHolder.currentUserTenantId());
        OrderFuseVO orderVO =  buildOrderFuseVO(ocmsOrderVO,null,null, null, showLabelList);
        appendDeliveryInfoService.appendDeliveryInfo(Arrays.asList(orderVO));

        if(orderVO == null || orderVO.getDeliveryChannelType()==null){
            return buildDeliveryStatusVO(DeliveryEntityEnum.PLATFORM.getValue());
        }
        return buildDeliveryStatusVO(orderVO.getDeliveryChannelType());
    }


    private CommonResponse<DeliveryStatusVO> buildDeliveryStatusVO(Integer deliveryChannelType) {

        CommonResponse<DeliveryStatusVO> commonResponse = new CommonResponse<DeliveryStatusVO>();
        commonResponse.setCode(0);
        DeliveryStatusVO deliveryStatusVO = new DeliveryStatusVO();
        deliveryStatusVO.setDeliveryChannelType(deliveryChannelType);
        commonResponse.setData(deliveryStatusVO);
        return commonResponse;
    }

    @Override
    @CatTransaction
    public Result verifySelfFetchCode(VerifySelfFetchCodeRequest verifySelfFetchCodeRequest) {
        try {
            DynamicOrderBizType dynamicOrderBizType = DynamicOrderBizType.channelId2OrderBizType(verifySelfFetchCodeRequest.getChannelId());
            // 公域渠道自提码不能为空
            if (ChannelOnlineTypeEnum.isPublic(dynamicOrderBizType.getChannelStandard())
                    && StringUtils.isBlank(verifySelfFetchCodeRequest.getSelfFetchCode())) {
                return ResultBuilder.buildFailResult("公域渠道自提码不能为空");
            }

            OCMSVerifySelfFetchCodeRequest queryBySelfFetchRequest = OCMSVerifySelfFetchCodeRequest.builder()
                    .selfFetchCode(verifySelfFetchCodeRequest.getSelfFetchCode())
                    .tenantId(ContextHolder.currentUserTenantId())
                    .orderBizType(ChannelOrderConvertUtils.convertBizType(verifySelfFetchCodeRequest.getChannelId()))
                    .operatorName(ContextHolder.currentUserName())
                    .viewOrderId(verifySelfFetchCodeRequest.getChannelOrderId())
                    .storeId(Long.valueOf(verifySelfFetchCodeRequest.getStoreId()))
                    .appId(String.valueOf(ContextHolder.currentUserLoginAppId()))
                    .operatorAccountId(ContextHolder.currentUid() != null ? ContextHolder.currentUid() : 0L)
                    .build();

            log.info("OCMSOrderServiceWrapper.verifySelfFetchCode   request:{}", verifySelfFetchCodeRequest);
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse response = ocmsOrderThriftService.verifySelfFetchCode(queryBySelfFetchRequest);
            log.info("OCMSOrderServiceWrapper.verifySelfFetchCode   response:{}", response);
            if (response.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
                return ResultBuilder.buildFailResult(response.getStatus().getMessage());
            }
        } catch (TException e) {
            log.error("OCMSOrderServiceWrapper.weightRefund  ocmsOrderThriftService.tenantWeightRefund TException", e);
            return ResultBuilder.buildFailResult("核验报错");
        }
        return ResultBuilder.buildSuccessResult();
    }

    @Override
    public Result<CheckSelfDeliveryCodeResponse> queryOrderBySelfFetchCode(CheckSelfFetchCodeRequest checkSelfFetchCodeRequest) {
        if (Objects.equals(checkSelfFetchCodeRequest.getChannelId(), ChannelType.YOU_ZAN.getValue())
                || checkSelfFetchCodeRequest.getChannelId() == DynamicChannelType.TAO_XIAN_DA.getChannelId()) {
            log.info("有赞or淘鲜达订单,自提码:{}", checkSelfFetchCodeRequest.getSelfFetchCode());
            return onlineCheckSelfFetchCode(ContextHolder.currentUserTenantId(), checkSelfFetchCodeRequest);
        }
        boolean isToLowercase = LionUtils.getSelfFetchCodeToLowercase();
        String selfFetchCode = isToLowercase ? checkSelfFetchCodeRequest.getSelfFetchCode().toLowerCase() : checkSelfFetchCodeRequest.getSelfFetchCode();
        OrderQueryBySelfFetchRequest queryBySelfFetchRequest = OrderQueryBySelfFetchRequest.builder()
                .selfFetchCode(selfFetchCode)
                .tenantId(ContextHolder.currentUserTenantId())
                .orderBizType(ChannelOrderConvertUtils.convertBizType(checkSelfFetchCodeRequest.getChannelId()))
                .selfFetchStatus(SelfFetchStatusEnum.WAIT_TO_SELF_FETCH.getValue())
                .build();
        SelfFetchCodeConditionResponse ocmsSelfFetchCodeConditionResponse = ocmsQueryThriftService.queryOrderBySelfFetchCode(queryBySelfFetchRequest);
        log.info("根据自提码查询订单详情，request:{}, response:{}", queryBySelfFetchRequest, ocmsSelfFetchCodeConditionResponse);
        if (ocmsSelfFetchCodeConditionResponse.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue() || CollectionUtils.isEmpty(ocmsSelfFetchCodeConditionResponse.getOcmsOrderList())) {
            log.info("根据自提码查询订单详情为空, request:{}", queryBySelfFetchRequest);
            MetricHelper.build().name("orderDetailIsNull").count();
            return ResultBuilder.buildFailResult(ResultCode.FAIL.getCode(), "自提码校验失败，请确认你选择的渠道、输入的自提码是否正确");
        }
        CheckSelfDeliveryCodeResponse response = buildCheckSelfDeliveryCodeResponse(ocmsSelfFetchCodeConditionResponse.getOcmsOrderList().get(0));
        return ResultBuilder.buildSuccess(response);
    }

    @Override
    public OrderExportMaxCountVO queryOrderCount(OrderFuseQueryBO orderFuseQueryBO) {
        FuseOrderListExportTaskCreateRequest exportFuseOrderListRequest = orderFuseQueryBO.toExportFuseOrderListRequest();
        return channelOrderRemoteService.isOverOrderListAndDetailLimit(exportFuseOrderListRequest);
    }

    @Override
    public Result<String> exportOrderListAndDetail(OrderFuseQueryBO orderFuseQueryBO) {
        ExportResponse response = fuseOrderListExportThriftService.exportOrderListAndDetail(orderFuseQueryBO.toExportFuseOrderListRequest());
        ResponseHandler.checkResponseAndStatus(response, rep -> rep.getStatus().getCode(), rep -> rep.getStatus().getMessage());
        return ResultBuilder.buildSuccess(response.getToken());
    }

    @Override
    public CommonFuseResponse<OrderFuseStatisticsResponse> queryFuseOrderStatisticsData(OrderFuseQueryStatisticsDataBO orderFuseQueryStatisticsDataBO) {
        try {
            if(Objects.nonNull(orderFuseQueryStatisticsDataBO.getFuseOrderStatus())
                    && orderFuseQueryStatisticsDataBO.getFuseOrderStatus().size() == 1
                    && orderFuseQueryStatisticsDataBO.getFuseOrderStatus().get(0) == ChannelOrderNewStatusEnum.SHIPPING_ABNORMAL.getCode()){
                return CommonFuseResponse.success(OrderFuseStatisticsResponse.builder().build());
            }
            OrderStatisticsDataSearchResponse response = channelOrderRemoteService.queryOrderFuseStatisticsData(orderFuseQueryStatisticsDataBO);
            Integer responseStatus = Optional.ofNullable(response).map(OrderStatisticsDataSearchResponse::getResponseStatus).orElse(-1);
            if (responseStatus == 0 && Objects.nonNull(response.getOrderStatisticsDataModel())) {
                return CommonFuseResponse.success(OrderFuseStatisticsResponse.builder()
                        .actualPayAmtSum(String.valueOf(response.getOrderStatisticsDataModel().getActualPayAmtSum()))
                        .bizReceiveAmtSum(String.valueOf(response.getOrderStatisticsDataModel().getBizReceiveAmtSum()))
                        .build());
            }
        }catch (Exception e){
            log.error("FuseOrderServiceImpl.queryFuseOrderStatisticsData", e);
        }
        return CommonFuseResponse.success(OrderFuseStatisticsResponse.builder().build());
    }

    @Override
    public CommonFuseResponse<OrderItemFuseStatisticsResponse>
            queryItemFuseOrderStatisticsData(OrderItemFuseQueryStatisticsDataBO orderItemFuseQueryStatisticsDataBO) {
        try {
            OrderItemStatisticsDataSearchResponse response = channelOrderRemoteService
                    .queryOrderItemFuseStatisticsData(orderItemFuseQueryStatisticsDataBO);
            Integer responseStatus = Optional.ofNullable(response)
                    .map(OrderItemStatisticsDataSearchResponse::getResponseStatus).orElse(-1);
            if (responseStatus == 0 && Objects.nonNull(response.getOrderItemStatisticsDataModel())) {
                return CommonFuseResponse.success(OrderItemFuseStatisticsResponse.builder()
                        .quantitySum(String.valueOf(response.getOrderItemStatisticsDataModel().getQuantitySum()))
                        .totalPayAmountSum(
                                String.valueOf(response.getOrderItemStatisticsDataModel().getTotalPayAmountSum()))
                        .build());
            }
        } catch (Exception e) {
            log.error("FuseOrderServiceImpl.queryItemFuseOrderStatisticsData", e);
        }
        return CommonFuseResponse.success(OrderItemFuseStatisticsResponse.builder().build());
    }

    private Result<CheckSelfDeliveryCodeResponse> onlineCheckSelfFetchCode(Long tenantId, CheckSelfFetchCodeRequest checkSelfFetchCodeRequest) {
        OCMSCheckSelfFetchCodeRequest ocmsCheckSelfFetchCodeRequest = new OCMSCheckSelfFetchCodeRequest();
        ocmsCheckSelfFetchCodeRequest.setTenantId(tenantId);
        ocmsCheckSelfFetchCodeRequest.setSelfFetchCode(checkSelfFetchCodeRequest.getSelfFetchCode());
        ocmsCheckSelfFetchCodeRequest.setOrderBizType(ChannelOrderConvertUtils.convertBizType(checkSelfFetchCodeRequest.getChannelId()));
        ocmsCheckSelfFetchCodeRequest.setStoreId(checkSelfFetchCodeRequest.getStoreId());
        try {
            log.info("OCMSOrderServiceWrapper.onlineCheckSelfFetchCode   request:{}", ocmsCheckSelfFetchCodeRequest);
            QueryOrderBySelfFetchCodeResponse response = ocmsOrderThriftService.checkSelfFetchCode(ocmsCheckSelfFetchCodeRequest);
            log.info("OCMSOrderServiceWrapper.onlineCheckSelfFetchCode   response:{}", response);
            if(Objects.nonNull(response) && Objects.equals(StatusCodeEnum.SUCCESS.getCode(), response.getStatus().getCode())){
                OCMSListViewIdConditionRequest viewIdConditionRequest = buildOCMSListViewIdConditionRequest(new ArrayList() {
                    {
                        OrderIdentifierDTO orderIdentifierDTO = new OrderIdentifierDTO();
                        orderIdentifierDTO.setUnifyOrderId(response.getChannelOrderId());
                        orderIdentifierDTO.setSourceCode(ChannelOrderConvertUtils.convertBizType(checkSelfFetchCodeRequest.getChannelId()));
                        add(orderIdentifierDTO);
                    }
                });
                OCMSListViewIdConditionResponse viewIdConditionResponse = ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
                log.info("查询订单详情，request:{}, response:{}", viewIdConditionRequest, viewIdConditionResponse);
                if (viewIdConditionResponse.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue() || CollectionUtils.isEmpty(viewIdConditionResponse.getOcmsOrderList())) {
                    log.info("查询订单订单详情为空, request:{}", viewIdConditionRequest);
                    return ResultBuilder.buildFailResult(ResultCode.FAIL.getCode(), ResultCode.FAIL.getErrorMessage());
                }
                CheckSelfDeliveryCodeResponse checkSelfDeliveryCodeResponse =  buildCheckSelfDeliveryCodeResponse(viewIdConditionResponse.getOcmsOrderList().get(0));
                return ResultBuilder.buildSuccess(checkSelfDeliveryCodeResponse);
            }
        } catch (TException e) {
            log.error("OCMSOrderServiceWrapper.onlineCheckSelfFetchCode  ocmsOrderThriftService.onlineCheckSelfFetchCode error", e);
        }
        return ResultBuilder.buildFailResult(ResultCode.FAIL.getCode(), "自提码校验失败，请确认你选择的订单、输入的自提码是否正确");
    }


    private OCMSListViewIdConditionRequest buildOCMSListViewIdConditionRequest(List<OrderIdentifierDTO> orders) {
        OCMSListViewIdConditionRequest request = new OCMSListViewIdConditionRequest();
        request.setViewIdConditionList(orders.stream().filter(Objects::nonNull).map(this::buildViewIdCondition).collect(Collectors.toList()));
        request.setSortField(SortFieldEnum.ESTIMATED_ARRIVAL_TIME);
        request.setSort(SortByEnum.ASC);
        request.setTenantId(ContextHolder.currentUserTenantId());
        return request;
    }

    private ViewIdCondition buildViewIdCondition(OrderIdentifierDTO orderIdentifierDTO) {
        ViewIdCondition viewIdCondition = new ViewIdCondition();
        viewIdCondition.setOrderBizType(orderIdentifierDTO.getSourceCode());
        viewIdCondition.setViewOrderId(orderIdentifierDTO.getUnifyOrderId());
        return viewIdCondition;
    }

    @Override
    @CatTransaction
    public Result queryAfsDetail(QueryAfsDetailRequest request) {
        OcmsOrderDetailReq req = new OcmsOrderDetailReq(request.getChannelOrderId(), Integer.valueOf(request.getChannelId()),ContextHolder.currentUserTenantId(), ContextHolder.currentUid(), true, false);
        OcmsOrderDetailResponse resp = RpcInvoker.invoke(() -> ocmsOrderSearchService.orderDetail(req));
        if (resp == null || resp.getOrder() == null){
            log.error("订单不存在,{}", request);
            return ResultBuilder.buildBizFailResult("未找到订单",new AfsOrderFuseDetailVO());
        }
        List<AfterSaleApply> afterSaleApplies = afterSaleApplySearchService.queryAfterSaleIdByAfterSaleId(new QueryAfterSaleApplyByAfterSaleIdRequest(Collections.singletonList(request.getChannelOrderId()), Collections.singletonList(request.getChannelAfsId())));
        if (CollectionUtils.isEmpty(afterSaleApplies)){
            log.error("退单不存在,{}", request);
            return ResultBuilder.buildBizFailResult("未找到退单",new AfsOrderFuseDetailVO());
        }
        AfterSaleApply afterSaleApply = afterSaleApplies.get(0);
        AfsOrderFuseDetailBO afsOrderFuseDetailBO = new AfsOrderFuseDetailBO();
        afsOrderFuseDetailBO.init(resp.getOrder(), afterSaleApply);
        OrderFuseFinanceDetailBO orderFuseFinanceDetailBO = queryFinanceDetail(afterSaleApply.getServiceId(), resp.getOrder().getOrderBaseDto().getShopId(),
                ContextHolder.currentUserTenantId(), 2);
        afsOrderFuseDetailBO.mergeFinanceInfo(orderFuseFinanceDetailBO, afterSaleApply, resp.getOrder());
        AfsOrderFuseDetailVO afsOrderFuseDetailVO = afsOrderFuseDetailBO.toAfsOrderFuseDetailVO();
        return ResultBuilder.buildSuccessResult(afsOrderFuseDetailVO);
    }

    @Override
    @CatTransaction
    public CommonFuseResponse<RefundOrderCountResponse> queryRefundOrderCount(RefundOrderQueryRequest refundOrderQueryRequest) {
        RefundOrderCountResponse refundOrderCountResponse = new RefundOrderCountResponse();
        refundOrderQueryRequest.setPage(1);
        refundOrderQueryRequest.setPageSize(1);
        try {
            checkRefundOrderRequest(refundOrderQueryRequest);

            OCMSAfterSaleCountResponse ocmsAfterSaleCountResponse = null;
            for (RefundStatusEnum statusEnum : RefundStatusEnum.values()) {
                switch (statusEnum) {
                    case ALL:
                        refundOrderQueryRequest.setRefundStatusList(Lists.newArrayList(String.valueOf(RefundStatusEnum.ALL.getCode())));
                        ocmsAfterSaleCountResponse = channelOrderRemoteService.queryRefundCount(refundOrderQueryRequest);
                        refundOrderCountResponse.setAll(ocmsAfterSaleCountResponse.getTotalCount());
                        break;
                    case DEAL_ING:
                        refundOrderQueryRequest.setRefundStatusList(Lists.newArrayList(String.valueOf(RefundStatusEnum.DEAL_ING.getCode())));
                        ocmsAfterSaleCountResponse = channelOrderRemoteService.queryRefundCount(refundOrderQueryRequest);
                        refundOrderCountResponse.setWaitDeal(ocmsAfterSaleCountResponse.getTotalCount());
                        break;
                    case COMPLETE:
                        refundOrderQueryRequest.setRefundStatusList(Lists.newArrayList(String.valueOf(RefundStatusEnum.COMPLETE.getCode())));
                        ocmsAfterSaleCountResponse = channelOrderRemoteService.queryRefundCount(refundOrderQueryRequest);
                        refundOrderCountResponse.setComplete(ocmsAfterSaleCountResponse.getTotalCount());
                        break;
                    case CANCEL:
                        refundOrderQueryRequest.setRefundStatusList(Lists.newArrayList(String.valueOf(RefundStatusEnum.CANCEL.getCode())));
                        ocmsAfterSaleCountResponse = channelOrderRemoteService.queryRefundCount(refundOrderQueryRequest);
                        refundOrderCountResponse.setCancel(ocmsAfterSaleCountResponse.getTotalCount());
                        break;
                    case REJECT:
                        refundOrderQueryRequest.setRefundStatusList(Lists.newArrayList(String.valueOf(RefundStatusEnum.REJECT.getCode())));
                        ocmsAfterSaleCountResponse = channelOrderRemoteService.queryRefundCount(refundOrderQueryRequest);
                        refundOrderCountResponse.setReject(ocmsAfterSaleCountResponse.getTotalCount());
                        break;
                    default:
                        log.warn("状态不再对应状态内:{}.", statusEnum.getCode());
                        break;
                }
            }

            return CommonFuseResponse.success(refundOrderCountResponse);
        } catch (CommonRuntimeException commonException) {
            log.warn("FuseOrderServiceImpl.queryRefundOrderCount commonException error:", commonException);
            //如果是业务校验异常，默认返回0
            return CommonFuseResponse.success(refundOrderCountResponse);
        } catch (Exception e) {
            log.error("FuseOrderServiceImpl.queryRefundOrderCount 查询子状态数量 error", e);
            return CommonFuseResponse.fail(ResultCode.FAIL.getCode(), "系统异常，请稍后重试");
        }
    }

    private CheckSelfDeliveryCodeResponse buildCheckSelfDeliveryCodeResponse(OCMSOrderVO ocmsOrderVO) {
        CheckSelfDeliveryCodeResponse resp = new CheckSelfDeliveryCodeResponse();
        resp.setTenantId(ocmsOrderVO.getTenantId());
        Integer channelId = ChannelOrderConvertUtils.sourceBiz2Mid(ocmsOrderVO.getOrderBizType());
        resp.setChannelId(channelId);
        if (Objects.nonNull(DynamicChannelType.findOf(channelId))) {
            //订单页展示渠道简称
            resp.setChannelName(Objects.requireNonNull(DynamicChannelType.findOf(channelId)).getDesc());
        }
        resp.setStoreId(ocmsOrderVO.getShopId());
        resp.setStoreName(ocmsOrderVO.getShopName());
        resp.setChannelOrderId(ocmsOrderVO.getViewOrderId());
        resp.setSelfFetchCode(ocmsOrderVO.getSelfFetchCode());
        return resp;
    }


    /**
     * 仅保留订单的部分可操作按钮.
     *
     * @param orderListResponse  订单列表
     * @param keepedOperateItems 需要保留的操作
     */
    private void justKeepPartOperateItems(OrderFuseListResponse orderListResponse, List<OrderCanOperateItem> keepedOperateItems) {
        List<OrderFuseVO> orderVOs = orderListResponse.getOrderList();
        if (CollectionUtils.isEmpty(orderVOs) || CollectionUtils.isEmpty(keepedOperateItems)) {
            return;
        }
        List<Integer> keepedOperateCodes = keepedOperateItems.stream().map(OrderCanOperateItem::getValue).collect(Collectors.toList());
        orderVOs.forEach(order -> {
            order.getOrderCouldOperateItems().retainAll(keepedOperateCodes);
        });
    }

    private OCMSListViewIdConditionRequest buildOCMSListViewIdConditionRequestByTOrderIdentifier(List<TOrderIdentifier> tOrderIdentifiers) {
        OCMSListViewIdConditionRequest request = new OCMSListViewIdConditionRequest();
        request.setViewIdConditionList(tOrderIdentifiers.stream().filter(Objects::nonNull)
                .map(to -> new ViewIdCondition(to.getOrderBizTypeCode(), to.getChannelOrderId()))
                .collect(Collectors.toList()));
        request.setSortField(SortFieldEnum.ORDER_CREATE_TIME);
        request.setSort(SortByEnum.DESC);
        return request;
    }

    private void checkViewResponseCount(OCMSListViewIdConditionRequest viewIdConditionRequest, OCMSListViewIdConditionResponse viewIdConditionResponse) {
        if (collectionSize(viewIdConditionRequest.getViewIdConditionList()) != collectionSize(viewIdConditionResponse.getOcmsOrderList())){
            MetricHelper.build().name("waitToPickOrderCountNotEqual").count();
            log.info("查询订单详情返回数量不等,viewId:{}, responseOrder:{}",
                    Optional.ofNullable(viewIdConditionRequest.getViewIdConditionList()).map(List::stream).orElse(Stream.empty()).map(e->e.getViewOrderId()).collect(Collectors.toList()),
                    Optional.ofNullable(viewIdConditionResponse.getOcmsOrderList()).map(List::stream).orElse(Stream.empty()).map(e->e.getViewOrderId()).collect(Collectors.toList()));
        }
    }

    private void setDeliveryErrorListSort (OrderFuseListResponse orderListResponse) {
        if (orderListResponse != null && orderListResponse.getOrderList() != null) {
            Collections.sort(orderListResponse.getOrderList(),
                    Comparator.comparing(o -> Optional.ofNullable(o.getEstimateArriveTimeEnd()).orElse(Long.MIN_VALUE)));
        }
    }

    private void setOrderListResponseViewStatusByExceptionSubType(OrderFuseListResponse response, Map<Long, TDeliveryDetail> deliveryDetailMap) {
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getOrderList()) || MapUtils.isEmpty(deliveryDetailMap)) {
            return;
        }
        for (OrderFuseVO each : response.getOrderList()) {
            if (deliveryDetailMap.containsKey(each.getOrderId())) {
                TDeliveryDetail tOrderIdentifier= deliveryDetailMap.get(each.getOrderId());
                // 对接tms展示【三方物流单号】信息
                List<OriginWaybillDto> waybillList = tOrderIdentifier.waybillList;
                if (CollectionUtils.isNotEmpty(waybillList)) {
                    // 根据配送状态过滤，再过滤【三方物流单号】为空的数据
                    each.setOriginWaybillNoList(waybillList.stream()
                            .filter(dto -> com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil
                                    .checkOriginWaybillDeliveryStatus(dto.deliveryStatus)
                                    && StringUtils.isNotBlank(dto.getOriginWaybillNo()))
                            .map(OriginWaybillDto::getOriginWaybillNo).collect(Collectors.toList()));
                }
                if(tOrderIdentifier.status == null){
                    continue;
                }
                DeliveryExceptionSubTypeEnum deliveryExceptionSubTypeEnum = DeliveryExceptionSubTypeEnum.deliveryStatusCodeValueOfWithOutAll(
                        Optional.ofNullable(tOrderIdentifier.deliveryExceptionType).orElse(null),
                        tOrderIdentifier.deliveryExceptionCode,
                        tOrderIdentifier.status);
                Integer viewStatus = ConvertUtils.getOrderViewStatusByExceptionOrderSubType(deliveryExceptionSubTypeEnum);
                DeliveryExceptionStatusDescEnum deliveryExceptionDesc = DeliveryExceptionStatusDescEnum.getByCode(viewStatus);
                each.setViewStatus(viewStatus);
                if(Objects.nonNull(deliveryExceptionDesc)){
                    each.setViewStatusDesc(deliveryExceptionDesc.getDesc());
                    if(tOrderIdentifier.deliveryExceptionCode != null && tOrderIdentifier.deliveryExceptionCode == DeliveryExceptionCodeEnum.RIDER_TAKE_FAIL_AUDITING.getCode()){
                        if(deliveryExceptionDesc == DeliveryExceptionStatusDescEnum.TAKE_EXCEPTION){
                            each.setViewStatusDesc("取货失败待审核");
                        }
                    }
                }

            }
        }
    }

    private void setOrderListResponseViewStatusByExceptionSubType(OrderFuseListResponse response, List<TOrderIdentifier> tOrderIdentifiers) {
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getOrderList()) || CollectionUtils.isEmpty(tOrderIdentifiers)) {
            return;
        }
        Map<String, TOrderIdentifier> tOrderIdentifierMap = tOrderIdentifiers.stream().collect(Collectors.toMap(o -> o.getChannelOrderId() + "_" + o.getOrderBizTypeCode(), o -> o, (f, s) -> f));
        for (OrderFuseVO each : response.getOrderList()) {
            String tOrderIdentifierMapKey = each.getChannelOrderId() + "_" + ChannelOrderConvertUtils.sourceMid2Biz(each.getChannelId());
            if (tOrderIdentifierMap.containsKey(tOrderIdentifierMapKey)) {
                TOrderIdentifier tOrderIdentifier= tOrderIdentifierMap.get(tOrderIdentifierMapKey);
                DeliveryExceptionSubTypeEnum deliveryExceptionSubTypeEnum = DeliveryExceptionSubTypeEnum.deliveryStatusCodeValueOfWithOutAll(
                        Optional.ofNullable(tOrderIdentifier.getExceptionTypeCode()).orElse(null),
                        tOrderIdentifier.getDeliveryExceptionCode(),
                        tOrderIdentifier.getDeliveryStatus().getCode());
                Integer viewStatus = ConvertUtils.getOrderViewStatusByExceptionOrderSubType(deliveryExceptionSubTypeEnum);
                DeliveryExceptionStatusDescEnum deliveryExceptionDesc = DeliveryExceptionStatusDescEnum.getByCode(viewStatus);
                each.setViewStatus(viewStatus);
                if(Objects.nonNull(deliveryExceptionDesc)){
                    each.setViewStatusDesc(deliveryExceptionDesc.getDesc());
                    if(tOrderIdentifier.getExceptionTypeCode() != null && tOrderIdentifier.getDeliveryExceptionCode() == DeliveryExceptionCodeEnum.RIDER_TAKE_FAIL_AUDITING.getCode()){
                        if(deliveryExceptionDesc == DeliveryExceptionStatusDescEnum.TAKE_EXCEPTION){
                            each.setViewStatusDesc("取货失败待审核");
                        }
                    }
                }

            }
        }
    }



    private int collectionSize(Collection collection){
        return collection != null ? collection.size() : 0;
    }


    private PageInfoVO buildPageInfoVO(Integer page, Integer size, Integer totalSize) {
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(page);
        pageInfoVO.setSize(size);
        pageInfoVO.setTotalSize(totalSize);
        pageInfoVO.setTotalPage(totalSize % size == 0 ? totalSize / size : totalSize / size + 1);
        return pageInfoVO;
    }

    private OrderFuseListResponse buildOrderFuseListResponse(List<OCMSOrderVO> ocmsOrderVOList, PageInfoVO pageInfoVO,Long currentStoreId) {
        return buildOrderFuseListResponse(ocmsOrderVOList, Collections.emptyList(), Collections.emptyMap(), Collections.emptyList(), pageInfoVO,currentStoreId);
    }

    private OrderFuseListResponse buildOrderFuseListResponse(long tenantId, long poiId, List<OCMSOrderVO> ocmsOrderVOList, PageInfoVO pageInfoVO,Long curStoreId) {
        List<OrderPrintResultDto> printResult = queryOrderPrintResult(tenantId, poiId, ocmsOrderVOList);
        return buildOrderFuseListResponse(ocmsOrderVOList, printResult, Collections.emptyMap(), Collections.emptyList(), pageInfoVO,curStoreId);
    }

    private OrderFuseListResponse buildOrderFuseListResponse(List<OCMSOrderVO> ocmsOrderVOList,
                                                             List<OrderPrintResultDto> orderPrintResultDtos, Map<String, OrderProfitView> orderProfitMap,
                                                             List<OrderRevenueDetailResponse> orderRevenueDetailResponseList,
                                                             PageInfoVO pageInfoVO,Long curStoreId) {
        OrderFuseListResponse orderListResponse = new OrderFuseListResponse();
        orderListResponse.setPageInfo(pageInfoVO);
        Map<String, OrderRevenueDetailResponse> orderRevenueDetailResponseMap = getOrderRevenueDetailResponseMap(
                orderRevenueDetailResponseList);
        Map<String, OrderPrintResultDto> printResultDtoMap = Optional.ofNullable(orderPrintResultDtos).map(List::stream).orElse(Stream.empty())
                .collect(Collectors.toMap(printOrder -> printOrder.getOrderNo(), v -> v, (f, s) -> f));


        if (CollectionUtils.isNotEmpty(ocmsOrderVOList)) {
            List<OrderLabelModel> showLabelList = queryOrderShowLabel(ocmsOrderVOList.get(0).getTenantId());
            Boolean isErpOrDrunkHorseTenant = ocmsOrderRemoteService.isErpOrDrunkHorseTenant(ocmsOrderVOList.get(0).getTenantId());
            orderListResponse.setOrderList(
                    ocmsOrderVOList.stream()
                            .map(ocmsOrder -> buildOrderFuseVO(ocmsOrder, orderRevenueDetailResponseMap.get(ocmsOrder.getViewOrderId()),
                                    orderProfitMap == null ? null : orderProfitMap.get(ocmsOrder.getViewOrderId()), isErpOrDrunkHorseTenant, showLabelList)).filter(Objects::nonNull)
                            .map(order -> {
                                //设置打印状态
                                OrderPrintResultDto printResultDto = printResultDtoMap.get(order.getChannelOrderId());
                                if (printResultDto != null) {
                                    OrderPrintStatusVO printStatusVo = buildPrintStatusVo(printResultDto);
                                    order.setPrintStatus(printStatusVo);
                                }
                                return order;
                            })
                            .collect(Collectors.toList()));
        }
        //添加实时配送信息，和配送操作按钮
        try {
            javafx.util.Pair<Map<Long, TDeliveryDetail>, Map<Integer, DeliveryChannelDto>> deliveryDetailMap = appendDeliveryInfoService.appendDeliveryInfo(orderListResponse.getOrderList());
            fillOrderMaltDeliveryPlatModule(orderListResponse.getOrderList(), deliveryDetailMap.getKey(), deliveryDetailMap.getValue(),curStoreId);
            fillDeliveryOperateItem(deliveryDetailMap.getKey(),orderListResponse.getOrderList());
            setOrderListResponseViewStatusByExceptionSubType(orderListResponse,deliveryDetailMap.getKey());
        }catch (Exception e){
            log.info("FuseOrderServiceImpl.buildOrderFuseListResponse error:{}", e.getMessage());
        }

        //添加拣货持续时间
        if (CollectionUtils.isNotEmpty(orderListResponse.getOrderList())) {
            appendPickDurationTime(orderListResponse.getOrderList());
        }
        // 订单根据配送方式决定是否展示【贵品取货码】
        isShowExpensiveProductPickupCode(orderListResponse);

        return orderListResponse;

    }

    /**
     * 订单根据配送方式决定是否展示【贵品取货码】
     * @param orderListResponse
     */
    private void isShowExpensiveProductPickupCode(OrderFuseListResponse orderListResponse){
        try {
            // 无数据
            if (Objects.isNull(orderListResponse) || CollectionUtils.isEmpty(orderListResponse.getOrderList())) {
                return;
            }
            orderListResponse.getOrderList().forEach(order -> {
                // 如果不是平台配送，则不返回贵品取货码
                if (StringUtils.isNotEmpty(order.getExpensiveProductPickupCode()) && Objects.nonNull(order.getDeliveryChannelType()) && !Objects.equals(DeliveryEntityEnum.PLATFORM.getValue(), order.getDeliveryChannelType())) {
                    order.setExpensiveProductPickupCode(null);
                }
            });
        }catch (Exception e){
            log.warn("web orderList checkExpensiveProductPickupCode is error");
        }
    }

    // 兼容app端转自送/转聚合配送
    private void fillDeliveryOperateItem(Map<Long, TDeliveryDetail> deliveryDetailMap, List<OrderFuseVO> orderList){
        try {
            log.info("FuseOrderServiceImpl.fillDeliveryOperateItem deliveryDetailMap: {} , orderList: {}", deliveryDetailMap, orderList);
            if(CollectionUtils.isEmpty(orderList)){
                return;
            }

            Long tenantId = orderList.get(0).getTenantId();
            ArrayListMultimap<Long,OrderFuseVO> orderMultimap = ArrayListMultimap.create();
            for (OrderFuseVO vo : orderList){
                if(vo.getDispatchShopId()!=null){
                    orderMultimap.put(vo.getDispatchShopId(),vo);
                }else if(vo.getWarehouseId()!=null){
                    orderMultimap.put(vo.getWarehouseId(),vo);
                }else {
                    orderMultimap.put(vo.getStoreId(),vo);
                }
                TDeliveryDetail tDeliveryDetail = null;
                if(StringUtils.isNotBlank(vo.getEmpowerOrderId())){
                    tDeliveryDetail = deliveryDetailMap.get(Long.parseLong(vo.getEmpowerOrderId()));
                }
                if(tDeliveryDetail==null){
                    if(vo.getOriginalDistributeType()!=null && vo.getOriginalDistributeType() != 25){
                        vo.setPlatformDelivery(true);
                    }
                }
                if(Objects.nonNull(tDeliveryDetail) && tDeliveryDetail.deliveryEntity != null && tDeliveryDetail.deliveryEntity == 0){
                    vo.setPlatformDelivery(true);
                }
            }

            for (Long storeId : orderMultimap.keySet()){
                List<OrderFuseVO> orderVOList = orderMultimap.get(storeId);
                if(CollectionUtils.isEmpty(orderVOList)){
                    continue;
                }
                // 前端控制权限
                Map<Long, DeliveryOperateItem> operateItemMap = tmsRemoteService.queryDeliveryOperateItem(tenantId,storeId,orderVOList.stream().filter(item -> StringUtils.isNotBlank(item.getEmpowerOrderId())).map(item -> {
                    return Long.parseLong(item.getEmpowerOrderId());
                }).collect(Collectors.toList()));

                for (OrderFuseVO orderVO : orderVOList){
                    if(StringUtils.isBlank(orderVO.getEmpowerOrderId())){
                        continue;
                    }
                    Long empowerOrderId = Long.parseLong(orderVO.getEmpowerOrderId());
                    List<Integer> operateList = new ArrayList<>();
                    if(!operateItemMap.containsKey(empowerOrderId)){
                        continue;
                    }
                    DeliveryOperateItem item = operateItemMap.get(empowerOrderId);
                    if(item == null || CollectionUtils.isEmpty(item.getOperateItemList())){
                        continue;
                    }
                    List<com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum> itemEnumList = com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.tmsItemListToOperateItemList(item.getOperateItemList());
                    if(CollectionUtils.isEmpty(itemEnumList)){
                        continue;
                    }
                    if(itemEnumList.contains(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.DELIVERY_TO_SELF)){
                        operateList.add(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.DELIVERY_TO_SELF.type);
                    }
                    if(itemEnumList.contains(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.DELIVERY_TO_MALTFARM)){
                        operateList.add(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.DELIVERY_TO_MALTFARM.type);
                    }
                    if(itemEnumList.contains(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.DELIVERY_TO_DAP)){
                        operateList.add(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.DELIVERY_TO_DAP.type);
                    }
                    if(itemEnumList.contains(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.DOUYIN_RECALL_DELIVERY)){
                        operateList.add(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.DOUYIN_RECALL_DELIVERY.type);
                    }
                    if(itemEnumList.contains(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.DOUYIN_EXCEPTION_RECALL_DELIVERY)){
                        operateList.add(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.DOUYIN_EXCEPTION_RECALL_DELIVERY.type);
                    }
                    if(itemEnumList.contains(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.RECALL_DELIVERY)){
                        operateList.add(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.RECALL_DELIVERY.type);
                    }
                    if (itemEnumList.contains(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.DELIVERY_TO_FOUR_WHEEL)){
                        operateList.add(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.DELIVERY_TO_FOUR_WHEEL.type);
                    }
                    if (itemEnumList.contains(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.APPEND_FOUR_WHEEL_TYPE)){
                        operateList.add(com.sankuai.shangou.qnh.orderapi.enums.app.DeliveryOperateItemEnum.APPEND_FOUR_WHEEL_TYPE.type);
                    }
                    orderVO.setDeliveryOperateItems(operateList);
                }
            }
        }catch (Exception e){
            log.info("FuseOrderServiceImpl.fillDeliveryOperateItem error e= ", e);
        }
    }


    private OrderItemFuseListResponse buildOrderItemFuseListResponse(List<com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderItemFuseVO> ocmsOrderVOList, PageInfoVO pageInfoVO) {
        OrderItemFuseListResponse orderListResponse = new OrderItemFuseListResponse();
        orderListResponse.setPageInfo(pageInfoVO);
        List<OrderItemFuseVO> itemList = new ArrayList<>();
        Map<String, List<ExchangeOrderItemFuseVO>> exchangeGoodsMap = new HashMap();
        if (CollectionUtils.isNotEmpty(ocmsOrderVOList)) {

            Map<Long, List<CombinationChildProductVo>> combinationProductTOMap = getCombinationProductMap(ocmsOrderVOList);

            ocmsOrderVOList.stream().forEach(order->{
                try {
                    OrderItemFuseVO ocmsOrderVO = new OrderItemFuseVO();
                    ocmsOrderVO.setOrderId(order.getOrderId());
                    ocmsOrderVO.setOrderItemId(String.valueOf(order.getOrderItemId()));
                    ocmsOrderVO.setCreateTime(order.getCreateTime());
                    ocmsOrderVO.setFuseOrderStatus(order.getFuseOrderStatus());
                    if(Objects.nonNull(ChannelOrderNewStatusEnum.getByCode(order.getFuseOrderStatus()))){
                        ocmsOrderVO.setFuseOrderStatusDesc(ChannelOrderNewStatusEnum.getByCode(order.getFuseOrderStatus()).getDesc());
                    }
                    ocmsOrderVO.setItemType(order.getItemType());
                    ocmsOrderVO.setChannelId(order.getChannelId());
                    if (Objects.nonNull(DynamicChannelType.findOf(order.getChannelId()))) {
                        //订单页展示渠道简称
                        ocmsOrderVO.setChannelName(Objects.requireNonNull(DynamicChannelType.findOf(order.getChannelId())).getDesc());
                    }
                    ocmsOrderVO.setChannelOrderId(order.getChannelOrderId());
                    ocmsOrderVO.setCurrentPrice(MoneyUtils.convertFenToYuanWithFormat(Long.valueOf(order.getCurrentPrice())));
                    ocmsOrderVO.setCreateTime(order.getCreateTime());
                    ocmsOrderVO.setErpCode(order.getErpCode());
                    ocmsOrderVO.setOriginalPrice(MoneyUtils.convertFenToYuanWithFormat(Long.valueOf(order.getOriginalPrice())));
                    ocmsOrderVO.setPicUrl(order.getPicUrl());
                    ocmsOrderVO.setPoiId(order.getPoiId());
                    ocmsOrderVO.setPoiName(order.getPoiName());
                    ocmsOrderVO.setOutShopId(order.getOutShopId());
                    ocmsOrderVO.setQuantity(order.getQuantity());
                    ocmsOrderVO.setSerialNo(order.getSerialNo());
                    ocmsOrderVO.setSerialNoStr(order.getSerialNoStr());
                    ocmsOrderVO.setSpecification(order.getSpecification());
                    ocmsOrderVO.setSkuId(order.getInstoreSkuId2());
                    ocmsOrderVO.setSkuName(order.getSkuName());
                    ocmsOrderVO.setTenantId(order.getTenantId());
                    ocmsOrderVO.setTotalPayAmount(MoneyUtils.convertFenToYuanWithFormat(Long.valueOf(order.getTotalPayAmount())));
                    ocmsOrderVO.setUpcCode(order.getUpcCode());
                    ocmsOrderVO.setUpdateTime(order.getUpdateTime());
                    ocmsOrderVO.setWarehouseId(order.getWarehouseId());
                    ocmsOrderVO.setWarehouseName(order.getWarehouseName());
                    ocmsOrderVO.setBatchInfo(order.getBatchInfo());
                    ocmsOrderVO.setInStoreCategoryList(buildInStoreCategoryVOList(order.getInStoreCategoryList()));
                    ocmsOrderVO.setDispatchShopId(order.getDispatchShopId());
                    ocmsOrderVO.setDispatchSerialNo(order.getDispatchSerialNo());
                    ocmsOrderVO.setDispatchShopName(order.getDispatchShopName());
                    ocmsOrderVO.setDispatchTenantId(order.getDispatchTenantId());
                    ocmsOrderVO.setDispatchTime(order.getDispatchTime());
                    //处理组合商品
                    ocmsOrderVO.setSubProduct(CombinationProductUtil.buildSubProductVoList(Long.valueOf(ocmsOrderVO.getOrderItemId()), combinationProductTOMap.get(ocmsOrderVO.getOrderId())));
                    ocmsOrderVO.setChannelLabelList(ProductLabelUtil.buildChannelLabelVOList(order.getChannelLabelList()));
                    ocmsOrderVO.setLabelSubDesc(ProductLabelUtil.buildChannelLabelSubDesc(order.getChannelLabelList(), order.getExtData()));
                    // 新增赠品类型
                    ocmsOrderVO.setGiftType(order.getGiftType());

                    try {
                        Map<String, Object> stringObjectMap = GsonUtil.toObjMap(order.getExtData());
                        //过滤换货商品
                        if(stringObjectMap.containsKey(EXE_CHANGE_ORDER_ITEM_ID_FROM_KEY)){
                            log.info("有换货 ocmsOrderVO: {}, stringObjectMap: {}", ocmsOrderVO, stringObjectMap);
                            dealExchangeGoods(ocmsOrderVO, stringObjectMap, exchangeGoodsMap);
                            log.info("有换货 ocmsOrderVO: {}", ocmsOrderVO);
                            return;
                        }
                    }catch (Exception e){
                        log.info("FuseOrderServiceImpl.buildOrderItemFuseListResponse getExtData error e= ", e);
                    }
                    itemList.add(ocmsOrderVO);
                } catch (Exception e) {
                    log.info("FuseOrderServiceImpl.buildOrderItemFuseListResponse parse error:{}", e.getMessage());
                }
            });
        }
        //组合缺货换货商品
        itemList.stream()
                .filter(e->exchangeGoodsMap.containsKey(e.getOrderItemId()))
                .forEach(e->{
                    e.setExchangeItemInfoVOList(exchangeGoodsMap.get(e.getOrderItemId()));
                    e.setExchangeCount(exchangeGoodsMap.get(e.getOrderItemId()).stream().map(ExchangeOrderItemFuseVO::getExchangeFromCnt).reduce(0,Integer::sum));
                    e.setExchangeGoodsCount(exchangeGoodsMap.get(e.getOrderItemId()).stream().map(ExchangeOrderItemFuseVO::getExchangeToCnt).reduce(0,Integer::sum));
                });
        orderListResponse.setItemList(itemList);
        return orderListResponse;
    }

    private void dealExchangeGoods(OrderItemFuseVO ocmsOrderVO, Map<String, Object> stringObjectMap, Map<String, List<ExchangeOrderItemFuseVO>> exchangeGoodsMap) {
        try {
            ExchangeOrderItemFuseVO exchangeItemInfoVO = new ExchangeOrderItemFuseVO();
            setExchangeItemInfoVO(exchangeItemInfoVO, ocmsOrderVO, stringObjectMap);
            setExchangeItemInfoVOPriceAndWeight(exchangeItemInfoVO, stringObjectMap);

            String exchangeSourceOrderItemId = stringObjectMap.get(EXE_CHANGE_ORDER_ITEM_ID_FROM_KEY).toString();
            List<ExchangeOrderItemFuseVO> exchangeItemInfoVOS = exchangeGoodsMap.get(exchangeSourceOrderItemId);
            if(Objects.isNull(exchangeItemInfoVOS)){
                List<ExchangeOrderItemFuseVO> list = new ArrayList<>();
                list.add(exchangeItemInfoVO);
                exchangeGoodsMap.put(exchangeSourceOrderItemId, list);
            }else {
                exchangeItemInfoVOS.add(exchangeItemInfoVO);
            }
        }catch (Exception e){
            log.info("FuseOrderServiceImpl.dealExchangeGoods is error e = ", e);
        }
    }

    private void setExchangeItemInfoVOPriceAndWeight(ExchangeOrderItemFuseVO exchangeItemInfoVO, Map<String, Object> stringObjectMap) {
        //换货商品原/现售价 重量 在extData字段 需要单独赋值 订单明细没有重量
        try {
            Object exchangeOriginPrice = stringObjectMap.get(EXE_CHANGE_ORIGIN_PRICE_KEY);
            Object exchangeSalePrice = stringObjectMap.get(EXE_CHANGE_SALE_PRICE_KEY);
            int originPrice = Integer.parseInt(String.valueOf(Objects.isNull(exchangeOriginPrice) ? "0" : exchangeOriginPrice));
            int salePrice = Integer.parseInt(String.valueOf(Objects.isNull(exchangeSalePrice) ? "0" : exchangeSalePrice));
            OrderItemFuseVO itemInfoVO = exchangeItemInfoVO.getItemInfoVO();
            itemInfoVO.setOriginalPrice(ConverterUtils.formatMoney(originPrice));
            itemInfoVO.setCurrentPrice(ConverterUtils.formatMoney(salePrice));
        }catch (Exception e){
            log.info("FuseOrderServiceImpl.setExchangeItemInfoVOPriceAndWeight is error e= ", e);
        }
    }

    private void setExchangeItemInfoVO(ExchangeOrderItemFuseVO exchangeItemInfoVO, OrderItemFuseVO ocmsOrderVO, Map<String, Object> stringObjectMap) {
        Object exchangeOrderItemCnt = stringObjectMap.get(EXE_CHANGE_ORDER_ITEM_CNT_FROM_KEY);

        exchangeItemInfoVO.setItemInfoVO(ocmsOrderVO.toItemInfoVO());
        exchangeItemInfoVO.setExchangeFromCnt(Integer.parseInt(String.valueOf(exchangeOrderItemCnt)));
        exchangeItemInfoVO.setExchangeToCnt(ocmsOrderVO.getQuantity());

    }

    private Map<Long, List<CombinationChildProductVo>> getCombinationProductMap(List<com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderItemFuseVO> ocmsOrderVOList) {
        Map<Long, List<CombinationChildProductVo>> combinationProductTOMap = new HashMap<>();
        try {
            //根据订单号去批量查询订单对应组合商品
            List<Long> orderIdList = ocmsOrderVOList.stream().map(com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderItemFuseVO::getOrderId).collect(Collectors.toList());
            CombinationProductSearchResponse combinationProduct = channelOrderRemoteService.queryCombinationProductByOrderIds(new SearchCombinationProductReq(orderIdList));
            if(Objects.isNull(combinationProduct) || CollectionUtils.isEmpty(combinationProduct.getCombinationProductTOList())){
                return combinationProductTOMap;
            }
            combinationProductTOMap.putAll(combinationProduct.getCombinationProductTOList().stream().filter(item -> Objects.nonNull(item.getOrderId())).collect(Collectors.toMap(CombinationProductTO::getOrderId, CombinationProductTO::getCombinationProducts)));
        }catch (Exception e){
            log.info("查询订单明细列表组合商品 getCombinationProductMap error e: ",e);
        }
        return combinationProductTOMap;
    }

    private List<InStoreCategoryVO> buildInStoreCategoryVOList(List<com.meituan.shangou.saas.order.management.client.dto.response.online.vo.InStoreCategoryVO> inStoreCategoryBOList) {
        if (CollectionUtils.isEmpty(inStoreCategoryBOList)) {
            return Lists.newArrayList();
        }
        List<InStoreCategoryVO> inStoreCategoryVOList = inStoreCategoryBOList.stream().map(category -> {
            InStoreCategoryVO inStoreCategoryVO = new InStoreCategoryVO();
            inStoreCategoryVO.setStoreCategoryId(category.getStoreCategoryId());
            inStoreCategoryVO.setStoreCategoryName(category.getStoreCategoryName());
            inStoreCategoryVO.setStoreCategoryIdPath(category.getStoreCategoryIdPath());
            inStoreCategoryVO.setStoreCategoryNamePath(category.getStoreCategoryNamePath());
            return inStoreCategoryVO;
        }).collect(Collectors.toList());
        return inStoreCategoryVOList;
    }


    private void appendPickDurationTime(List<OrderFuseVO> orderList) {
        try {
            //tenantId
            Long tenantId = orderList.get(0).getTenantId();

            if(MccConfigUtil.getPickQuerySwitch()){
                final List<PickDurationRequest> pickDurationRequests = orderList.stream().map(item -> new PickDurationRequest(item.getChannelOrderId(), ChannelOrderConvertUtils.convertBizType(item.getChannelId()))).collect(Collectors.toList());

                OrderPickDurationResponse response = queryPickingThriftService.queryPickDuration(pickDurationRequests, tenantId);
                if (response.getStatus().getCode() == ResultCodeEnum.SUCCESS.getValue() && CollectionUtils.isNotEmpty(response.getPickWarnDuration())) {
                    MultiKeyMap<String, OrderPickDurationDetailResponse> map = new MultiKeyMap<>();
                    response.getPickWarnDuration().stream().forEach(item -> map.put(item.getViewOrderId(), String.valueOf(item.getOrderBizType()), item));
                    orderList.stream().forEach(item -> {
                        final String orderBizType = String.valueOf(ChannelOrderConvertUtils.convertBizType(item.getChannelId()));
                        final OrderPickDurationDetailResponse orderPickDurationDetailResponse = map.get(item.getChannelOrderId(), orderBizType);
                        if (Objects.nonNull(orderPickDurationDetailResponse)) {
                            PickWarnDurationVO pickWarnDurationVO = new PickWarnDurationVO();
                            pickWarnDurationVO.setWarnDuration(orderPickDurationDetailResponse.getWarnDuration());
                            pickWarnDurationVO.setCurrentTime(orderPickDurationDetailResponse.getCurrentTime());
                            pickWarnDurationVO.setDeliverTime(orderPickDurationDetailResponse.getDeliverTime());
                            pickWarnDurationVO.setIsReserved(orderPickDurationDetailResponse.getIsReserved());
                            pickWarnDurationVO.setPushTaskTime(orderPickDurationDetailResponse.getPushTaskTime());
                            item.setPickWarnDuration(pickWarnDurationVO);
                        }
                    });
                }
            }else {
                final List<com.sankuai.meituan.reco.pickselect.thrift.fulfill.request.PickDurationRequest> pickDurationRequests = orderList.stream().map(item -> new com.sankuai.meituan.reco.pickselect.thrift.fulfill.request.PickDurationRequest(item.getChannelOrderId(), ChannelOrderConvertUtils.convertBizType(item.getChannelId()))).collect(Collectors.toList());

                com.sankuai.meituan.reco.pickselect.thrift.picking.response.OrderPickDurationResponse response = pickingThriftService.queryPickDuration(pickDurationRequests, tenantId);
                if (response.getStatus().getCode() == ResultCodeEnum.SUCCESS.getValue() && CollectionUtils.isNotEmpty(response.getPickWarnDuration())) {
                    MultiKeyMap<String, com.sankuai.meituan.reco.pickselect.thrift.picking.response.OrderPickDurationDetailResponse> map = new MultiKeyMap<>();
                    response.getPickWarnDuration().stream().forEach(item -> map.put(item.getViewOrderId(), String.valueOf(item.getOrderBizType()), item));
                    orderList.stream().forEach(item -> {
                        final String orderBizType = String.valueOf(ChannelOrderConvertUtils.convertBizType(item.getChannelId()));
                        final com.sankuai.meituan.reco.pickselect.thrift.picking.response.OrderPickDurationDetailResponse orderPickDurationDetailResponse = map.get(item.getChannelOrderId(), orderBizType);
                        if (Objects.nonNull(orderPickDurationDetailResponse)) {
                            PickWarnDurationVO pickWarnDurationVO = new PickWarnDurationVO();
                            pickWarnDurationVO.setWarnDuration(orderPickDurationDetailResponse.getWarnDuration());
                            pickWarnDurationVO.setCurrentTime(orderPickDurationDetailResponse.getCurrentTime());
                            pickWarnDurationVO.setDeliverTime(orderPickDurationDetailResponse.getDeliverTime());
                            pickWarnDurationVO.setIsReserved(orderPickDurationDetailResponse.getIsReserved());
                            pickWarnDurationVO.setPushTaskTime(orderPickDurationDetailResponse.getPushTaskTime());
                            item.setPickWarnDuration(pickWarnDurationVO);
                        }
                    });
                }
            }
        } catch (Exception e) {
            log.info("append pick duration error,errorMessage:{}", e.getMessage());
        }
    }

    private Map<String, OrderRevenueDetailResponse> getOrderRevenueDetailResponseMap(
            List<OrderRevenueDetailResponse> orderRevenueDetailList) {
        return Optional.ofNullable(orderRevenueDetailList)
                .map(List::stream).orElse(Stream.empty())
                .collect(Collectors.toMap(OrderRevenueDetailResponse::getOrderViewId, revenueDetail -> revenueDetail,
                        (before, after) -> before));
    }


    private void fillOrderMaltDeliveryPlatModule(List<OrderFuseVO> orderVOList, Map<Long, TDeliveryDetail> deliveryDetailMap, Map<Integer, DeliveryChannelDto> channelDtoMap,Long curStoreId){
        if(CollectionUtils.isEmpty(orderVOList)){
            return;
        }
        Long tenantId = orderVOList.get(0).getTenantId();
        Set<Long> storeIdSet = new HashSet<>();
        for (OrderFuseVO vo : orderVOList){
            if(vo.getDispatchShopId()!=null){
                storeIdSet.add(vo.getDispatchShopId());
            }else if(vo.getWarehouseId()!=null){
                storeIdSet.add(vo.getWarehouseId());
            }else {
                storeIdSet.add(vo.getStoreId());
            }

            TDeliveryDetail deliveryDetail = deliveryDetailMap.get(Long.parseLong(vo.getEmpowerOrderId()));
            if(deliveryDetail==null){
                if(vo.getOriginalDistributeType() !=null && vo.getOriginalDistributeType() != 25){
                    vo.setPlatformDelivery(true);
                }
            }else if(deliveryDetail.deliveryEntity != null && deliveryDetail.deliveryEntity == 0){
                vo.setPlatformDelivery(true);
            }
        }
        if(tenantId <= 0 || CollectionUtils.isEmpty(storeIdSet)){
            return;
        }
        BatchStoreConfigQueryResponse batchStoreConfigQueryResponse = tmsRemoteService.batchDeliveryStoreConfigSearch(tenantId, new ArrayList<>(storeIdSet));
        if(batchStoreConfigQueryResponse == null || batchStoreConfigQueryResponse.getStatus() == null
                || batchStoreConfigQueryResponse.getStatus().getCode() != 0){
            return;
        }
        List<TStoreConfig> tStoreConfigs = batchStoreConfigQueryResponse.getTStoreConfigs();
        if(CollectionUtils.isEmpty(tStoreConfigs)){
            return;
        }
        List<Integer> platformEnumList=Arrays.asList(AggDeliveryPlatformEnum.MALT_FARM.getCode(),AggDeliveryPlatformEnum.DAP_DELIVERY.getCode());
        Map<Long,Map<Integer,Map<Integer, TAggDeliveryPlatformConfig>>> tStoreConfigMap=new HashMap<>();
        for (TStoreConfig tStoreConfig : tStoreConfigs){
            List<TAggDeliveryPlatformConfig> platformConfigList=tStoreConfig.getAggPlatformConfigs();
            if(CollectionUtils.isEmpty(platformConfigList)){
                continue;
            }
            tStoreConfigMap.put(tStoreConfig.getStoreId(),toPlatformConfigMap(platformConfigList,platformEnumList));
        }

        ArrayListMultimap<Long,Long> dapOrderIdMultiMap=ArrayListMultimap.create();
        ArrayListMultimap<Long,String> dapFulfillOrderIdMultiMap=ArrayListMultimap.create();
        Set<Long> poiIdList = new HashSet<>();
        Map<Integer, Integer> deliveryChannelMap = deliveryChannelRemoteService.tratranslateToChannelIntgerMap(channelDtoMap);
        for (OrderFuseVO vo : orderVOList){

            if(Objects.isNull(vo.getEmpowerOrderId())){
                continue;
            }
            TDeliveryDetail deliveryDetail = deliveryDetailMap.get(Long.parseLong(vo.getEmpowerOrderId()));
            if(deliveryDetail==null||deliveryDetail.deliveryEntity == null|| deliveryDetail.deliveryEntity == 0){
                continue;
            }
            if(!deliveryChannelRemoteService.checkChannel(deliveryDetail.deliveryChannelCode,AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(),vo.getTenantId(),vo.getStoreId(),deliveryChannelMap)){
                continue;
            }
            if(deliveryDetail.platformSource!=null && Objects.equals(deliveryDetail.platformSource, PlatformSourceEnum.OFC.getCode()) ){
                Long shopId = vo.getStoreId();
                if(vo.getDispatchShopId()!=null){
                    shopId = vo.getDispatchShopId();
                }else if(vo.getWarehouseId()!=null){
                    shopId = vo.getWarehouseId();
                }
                poiIdList.add(shopId);
                dapFulfillOrderIdMultiMap.put(shopId, PlatformSourceEnum.OFC.toPrefixOrderId(deliveryDetail.fulfillOrderId+""));
            }else {
                if(vo.getWarehouseId()!=null){
                    dapOrderIdMultiMap.put(vo.getWarehouseId(), Long.parseLong(vo.getEmpowerOrderId()));
                    poiIdList.add(vo.getWarehouseId());
                }else {
                    dapOrderIdMultiMap.put(vo.getStoreId(),Long.parseLong(vo.getEmpowerOrderId()));
                    poiIdList.add(vo.getStoreId());
                }
            }
        }
        Map<String,String> urlMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(poiIdList)){
            for (Long poiId : poiIdList){
                List<Long> orderIdList=dapOrderIdMultiMap.get(poiId);
                try {
                    Map<String,String> url = tmsRemoteService.batchQueryOrderDeliveryUrl(tenantId,poiId,orderIdList,dapFulfillOrderIdMultiMap.get(poiId));
                    if(MapUtils.isEmpty(url)){
                        continue;
                    }
                    urlMap.putAll(url);
                }catch (Exception e){
                    log.error("获取URL失败 orderIdList:{}",orderIdList,e);
                }

            }
        }





        orderVOList.forEach(order -> {
            if(order.getStoreId() == null){
                return;
            }
            TAggDeliveryPlatformConfig tAggDeliveryPlatformConfig = null;
            Long shopId=order.getStoreId();
            if(order.getWarehouseId()!=null){
                shopId=order.getWarehouseId();
            }
            if(order.getDispatchShopId()!=null){
                shopId = order.getDispatchShopId();
            }

            TDeliveryDetail deliveryDetail = deliveryDetailMap.get(Long.parseLong(order.getEmpowerOrderId()));

            if(deliveryDetail == null){
                return;
            }

            Map<Integer,Map<Integer,TAggDeliveryPlatformConfig>> platformConfigMap = tStoreConfigMap.get(shopId);
            if(MapUtils.isNotEmpty(platformConfigMap)){
                Map<Integer,TAggDeliveryPlatformConfig> configMap=new HashMap<>();
                if(platformConfigMap.containsKey(order.getChannelId())){
                    configMap=platformConfigMap.get(order.getChannelId());
                }else {
                    configMap=platformConfigMap.get(ChannelType.MEITUAN.getValue());
                }
                if(MapUtils.isNotEmpty(configMap)){
                    AggDeliveryPlatformEnum platformEnum = AggDeliveryPlatformEnum.MALT_FARM;
                    if(deliveryChannelRemoteService.checkChannel(deliveryDetail.deliveryChannelCode,AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(),order.getTenantId(),order.getStoreId(),deliveryChannelMap)){
                        platformEnum = AggDeliveryPlatformEnum.DAP_DELIVERY;
                    }
                    tAggDeliveryPlatformConfig = configMap.get(platformEnum.getCode());
                }
            }
            if(tAggDeliveryPlatformConfig == null){
                tAggDeliveryPlatformConfig = new TAggDeliveryPlatformConfig();
            }

            if(tAggDeliveryPlatformConfig.getPlatformCode() == null){
                return;
            }

            String oId = order.getEmpowerOrderId();
            if(deliveryDetail.platformSource!=null && Objects.equals(deliveryDetail.platformSource,PlatformSourceEnum.OFC.getCode())){
                oId = PlatformSourceEnum.OFC.toPrefixOrderId(deliveryDetail.fulfillOrderId+"");
            }

            if(deliveryChannelRemoteService.checkChannel(deliveryDetail.deliveryChannelCode,AggDeliveryPlatformEnum.MALT_FARM.getCode(),order.getTenantId(),order.getStoreId(),deliveryChannelMap)){
                order.setDeliveryPlatformCode(AggDeliveryPlatformEnum.MALT_FARM.getCode());
                if (order.getRealDistributeStatus() == null || order.getRealDistributeStatus() <= TmsDeliveryStatusDescEnum.INIT.getCode() ||
                        // 针对麦芽田，配送拒单，也不展示链接
                        order.getRealDistributeStatus() == TmsDeliveryStatusDescEnum.DELIVERY_REJECTED.getCode() ||
                        // 麦芽田转自配送后，不展示链接
                        deliveryDetail.deliveryChannelCode == DeliveryChannelEnum.FARM_DELIVERY_MERCHANT.getCode()) {
                    DeliveryRedirectModuleVO deliveryRedirectModuleVO = new DeliveryRedirectModuleVO();
                    deliveryRedirectModuleVO.setTitle("暂无配送状态");
                    deliveryRedirectModuleVO.setShowButton(false);
                    order.setDeliveryRedirectModule(deliveryRedirectModuleVO);
                    return;
                }
                DeliveryRedirectModuleVO deliveryRedirectModuleVo = AggDeliveryPlatformEnum.MALT_FARM.fillDeliveryRedirectModule(tAggDeliveryPlatformConfig,
                        oId, shopId,
                        Objects.nonNull(order.getDeliveryExceptionType()) && !Objects.equals(order.getDeliveryExceptionType(), DELIVERY_NO_EXCEPTION));
                if(curStoreId!=null && !Objects.equals(curStoreId,shopId)){
                    deliveryRedirectModuleVo.setUrl("");
                    deliveryRedirectModuleVo.setUrlText("");
                    deliveryRedirectModuleVo.setShowButton(false);
                }
                order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
            }else if(deliveryChannelRemoteService.checkChannel(deliveryDetail.deliveryChannelCode,AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(),order.getTenantId(),order.getStoreId(),deliveryChannelMap)){
                order.setDeliveryPlatformCode(AggDeliveryPlatformEnum.DAP_DELIVERY.getCode());
                if (order.getRealDistributeStatus() == null || order.getRealDistributeStatus() <= TmsDeliveryStatusDescEnum.INIT.getCode() ||
                        //配送拒单，也不展示链接
                        order.getRealDistributeStatus() == TmsDeliveryStatusDescEnum.DELIVERY_REJECTED.getCode()) {
                    DeliveryRedirectModuleVO deliveryRedirectModuleVO = new DeliveryRedirectModuleVO();
                    deliveryRedirectModuleVO.setTitle("暂无配送状态");
                    deliveryRedirectModuleVO.setShowButton(false);
                    order.setDeliveryRedirectModule(deliveryRedirectModuleVO);
                    return;
                }
                String url=urlMap.get(oId);
                DeliveryRedirectModuleVO deliveryRedirectModuleVo = AggDeliveryPlatformEnum.DAP_DELIVERY.fillDeliveryRedirectModule(url,
                        Objects.nonNull(order.getDeliveryExceptionType()) && !Objects.equals(order.getDeliveryExceptionType(), DELIVERY_NO_EXCEPTION));
                deliveryRedirectModuleVo.setShowButton(true);
                if(curStoreId!=null && !Objects.equals(curStoreId,shopId)){
                    deliveryRedirectModuleVo.setUrl("");
                    deliveryRedirectModuleVo.setUrlText("");
                    deliveryRedirectModuleVo.setShowButton(false);
                }
                order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
            }
        });
    }

    private Map<Integer,Map<Integer,TAggDeliveryPlatformConfig>> toPlatformConfigMap(List<TAggDeliveryPlatformConfig> aggPlatformConfigs, List<Integer> platformList){
        Map<Integer,Map<Integer,TAggDeliveryPlatformConfig>> platformConfigMap=new HashMap<>();
        ArrayListMultimap<Integer,TAggDeliveryPlatformConfig> platformChannelMultiMap=ArrayListMultimap.create();
        for (TAggDeliveryPlatformConfig config : aggPlatformConfigs){
            if(!platformList.contains(config.getPlatformCode())){
                continue;
            }
            platformChannelMultiMap.put(config.getChannelType()==null ? com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType.MEITUAN.getValue():config.getChannelType(),config);
        }
        if(platformChannelMultiMap.isEmpty()){
            return null;
        }
        for (Integer channelType : platformChannelMultiMap.keySet()){
            List<TAggDeliveryPlatformConfig> configs=platformChannelMultiMap.get(channelType);
            if(CollectionUtils.isEmpty(configs)){
                continue;
            }
            Map<Integer,TAggDeliveryPlatformConfig> configMap=new HashMap<>();
            for (TAggDeliveryPlatformConfig config : configs){
                Integer platformId=AggDeliveryPlatformEnum.MALT_FARM.getCode();
                if(config.getPlatformCode()!=null){
                    platformId = config.getPlatformCode();
                }
                configMap.put(platformId,config);
            }
            platformConfigMap.put(channelType,configMap);
        }
        return platformConfigMap;
    }


    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    public static class OrderProfitView {

        private Long profit;

        private Boolean withDeliveryCost;
    }

    private OrderFuseVO buildOrderFuseVO(OCMSOrderVO ocmsOrderVO, OrderRevenueDetailResponse orderRevenueDetailResponse, OrderProfitView orderProfit, Boolean isErpOrDrunkHorseTenant, List<OrderLabelModel> showLabelList) {
        try {
            List<OCMSOrderItemVO> ocmsOrderItemVOList = ocmsOrderVO.getOcmsOrderItemVOList();
            OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrderVO.getOcmsDeliveryInfoVO();
            OrderFuseVO orderVO = new OrderFuseVO();
            orderVO.setTenantId(ocmsOrderVO.getTenantId());
            orderVO.setOrderId(ocmsOrderVO.getOrderId());
            orderVO.setIsHasCanRefundGoods(ocmsOrderVO.getIsHasCanRefundGoods());
            Integer channelId = ChannelOrderConvertUtils.sourceBiz2Mid(ocmsOrderVO.getOrderBizType());
            orderVO.setChannelId(channelId);
            if (Objects.nonNull(DynamicChannelType.findOf(channelId))) {
                //订单页展示渠道简称
                orderVO.setChannelName(Objects.requireNonNull(DynamicChannelType.findOf(channelId)).getDesc());
            }
            orderVO.setEmpowerOrderId(String.valueOf(ocmsOrderVO.getOrderId()));
            orderVO.setUserId(ocmsOrderVO.getUserId());
            orderVO.setStoreId(ocmsOrderVO.getShopId());
            orderVO.setDispatchShopId(ocmsOrderVO.getDispatchShopId());
            orderVO.setDispatchSerialNo(ocmsOrderVO.getDispatchSerialNo());
            orderVO.setDispatchShopName(ocmsOrderVO.getDispatchShopName());
            orderVO.setDispatchTenantId(ocmsOrderVO.getDispatchTenantId());
            orderVO.setDispatchTime(ocmsOrderVO.getDispatchTime());
            orderVO.setStoreName(ocmsOrderVO.getShopName());
            orderVO.setOutShopId(ocmsOrderVO.getOutShopId());
            orderVO.setWarehouseId(ocmsOrderVO.getWarehouseId());
            orderVO.setWarehouseName(ocmsOrderVO.getWarehouseName());
            orderVO.setChannelOrderId(ocmsOrderVO.getViewOrderId());
            orderVO.setSerialNo(ocmsOrderVO.getOrderSerialNumber());
            orderVO.setSerialNoStr(ocmsOrderVO.getOrderSerialNumberStr());
            orderVO.setOrderSource(ocmsOrderVO.getOrderSource());
            orderVO.setIsBooking(ocmsOrderVO.getIsBooking());
            orderVO.setLastChangeTime(ocmsOrderVO.getLastChangeTime());
            // 支付时间
            orderVO.setPayTime(ocmsOrderVO.getPayTime() != null ? ocmsOrderVO.getPayTime() : ocmsOrderVO.getCreateTime());
            //商品数量为所有商品数量之和

            orderVO.setActualPayAmt(ocmsOrderVO.getActualPayAmt());
            orderVO.setBizReceiveAmt(ocmsOrderVO.getMerchantAmount());
            orderVO.setPosErrorMsg(ocmsOrderVO.getPosErrorMsg());
            orderVO.setPosStatus(Optional.ofNullable(PosStatusEnum.converterFromOrderPosStatus(ocmsOrderVO.getPosStatus())).map(PosStatusEnum::getCode).orElse(null));
            orderVO.setPosCheckTag(ocmsOrderVO.getPosCheckTag());
            if (isDeliveryInfoNotNull(ocmsDeliveryInfoVO)) {
                orderVO.setLockOrderState(ocmsDeliveryInfoVO.getLockOrderState());
                orderVO.setReceiverAddress(ocmsDeliveryInfoVO.getUserAddress());
                orderVO.setDeliveryMethod(ocmsDeliveryInfoVO.getDistributeMethod());
                orderVO.setDeliveryMethodDesc(ocmsDeliveryInfoVO.getDistributeMethodName());
                orderVO.setDeliveryUserName(ocmsDeliveryInfoVO.getRiderName());
                orderVO.setDeliveryUserPhone(ocmsDeliveryInfoVO.getRiderPhone());
                orderVO.setReceiverName(ocmsDeliveryInfoVO.getUserName());
                orderVO.setReceiverPhone(ocmsDeliveryInfoVO.getUserPhone());
                // 美团名酒馆平台配送不展示手机号
                if (BooleanUtils.isTrue(ocmsOrderVO.getIsMtFamousTavern())
                        && Objects.equals(ocmsDeliveryInfoVO.getIsSelfDelivery(), 0)) {
                    orderVO.setReceiverPhone(null);
                }

                // 订单状态已完结 && 非歪马租户 && 租户支持隐藏隐私号
                Long orderStatusTime = OrderUtil.getOrderTimeByLog(ocmsOrderVO.getOrderStatus(), ocmsOrderVO.getOrderStatusLogList());
                if (OrderUtil.isOrderEnd(ocmsOrderVO.getOrderStatus())
                        && !MccConfigUtil.isDrunkHorseTenant(orderVO.getTenantId())
                        && MccDynamicConfigUtil.checkSupportDesensitizeReceiverInfoTenant(orderVO.getTenantId())){
                    // 订单超过时间，需要隐藏隐私号
                    if(OrderUtil.isOverConfigTime(orderStatusTime, MccDynamicConfigUtil.getDesensitizeReceiverInfoTime())){
                        orderVO.setReceiverPhone(null);
                    }
                } else if(orderFinishedOverTwoDays(ocmsOrderVO.getOrderCompleteTime(), ocmsOrderVO.getCreateTime()) || orderCancelOverTwoDays(ocmsOrderVO.getCreateTime(), getCancelTime(ocmsOrderVO))){
                    //隐私号 完成获取取消48小时之后隐藏
                    if (ocmsOrderVO.getOrderStatus() != OrderStatusEnum.REFUND_APPLIED.getValue() || MccConfigUtil.isDrunkHorseTenant(ocmsOrderVO.getTenantId())) {
                        orderVO.setReceiverPhone(null);
                        orderVO.setReceiverAddress(CommonConstant.PRIVACY_PROTECT_ADDRESS);
                    }
                }

                orderVO.setUserPrivacyPhone(ocmsDeliveryInfoVO.getUserPrivacyPhone());

                // 订单收货人信息脱敏处理
                DesensitizeReceiverInfoExtParam receiverInfoExtParam = DesensitizeReceiverInfoExtParam.build(
                                    orderVO.getReceiverName(),orderVO.getReceiverAddress(),orderVO.getReceiverPhone(),
                                    orderVO.getUserPrivacyPhone(), ocmsOrderVO.getTenantId(), ocmsOrderVO.getOrderStatus(),orderStatusTime);
                DesensitizeReceiverInfoResult desensitizeReceiverInfoResult = DesensitizeReceiverInfoUtil.desensitizeReceiverInfo(receiverInfoExtParam);
                if (Objects.nonNull(desensitizeReceiverInfoResult)) {
                    orderVO.setReceiverName(desensitizeReceiverInfoResult.getReceiverName());
                    orderVO.setReceiverAddress(desensitizeReceiverInfoResult.getReceiverAddress());
                    orderVO.setReceiverPhone(desensitizeReceiverInfoResult.getReceiverPhone());
                    orderVO.setUserPrivacyPhone(desensitizeReceiverInfoResult.getReceiverPrivacyPhone());
                }

                // 自提
                boolean selfMention = DistributeMethodEnum.STORE_DELIVERY.getDesc().equals(ocmsDeliveryInfoVO.getDistributeMethodName());
                if (selfMention && orderVO.getChannelId() != null && orderVO.getChannelId().equals(ChannelType.YOU_ZAN.getValue())) {
                    // 有赞渠道自提隐藏用户地址
                    orderVO.setReceiverAddress("到店自取@#到店自取");
                }
                orderVO.setEstimateArriveTimeStart(ocmsDeliveryInfoVO.getArrivalTime());
                orderVO.setEstimateArriveTimeEnd(ocmsDeliveryInfoVO.getArrivalEndTime());
                orderVO.setPickStatus(ocmsDeliveryInfoVO.getDeliveryStatus());
                orderVO.setPickCompleteTime(ocmsDeliveryInfoVO.getCompleteTime());
                orderVO.setDistributeStatus(ocmsDeliveryInfoVO.getDistributeStatus());
                orderVO.setSelfDelivery(ocmsDeliveryInfoVO.getIsSelfDelivery());
                orderVO.setSupportDeliveryUserPrivacyPhone(judgeSupportDeliveryUserPrivacyPhone(ocmsDeliveryInfoVO.getIsSelfDelivery()));
                if (ocmsDeliveryInfoVO.getDeliveryPauseFlag() != null) {
                    orderVO.setDeliveryStatusLocked(ocmsDeliveryInfoVO.getDeliveryPauseFlag());
                }
                orderVO.setOriginalDistributeType(ocmsDeliveryInfoVO.getOriginalDistributeType());
                //添加自提码，和自提状态
                orderVO.setSelfFetchCode(ocmsDeliveryInfoVO.getSelfFetchCode());
                orderVO.setSelfFetchStatus(ocmsDeliveryInfoVO.getSelfFetchStatus());
                orderVO.setDeliveryChannelId(ocmsDeliveryInfoVO.getDeliveryChannelId());
                orderVO.setExpensiveProductPickupCode(ocmsDeliveryInfoVO.getExpensiveProductPickupCode());
            }
            //增加拣货记录按钮
            orderVO.setPickRecordButton(CommonUsedUtil.setPickGoodsRecordButton(orderVO.getChannelId(), orderVO.getPickStatus(), orderVO.getTenantId(), isErpOrDrunkHorseTenant));
            String distributeStatusDesc = Objects.isNull(ocmsDeliveryInfoVO) || Objects.isNull(ocmsDeliveryInfoVO.getDistributeStatus()) || Objects.isNull(DistributeStatusEnum.enumOf(ocmsDeliveryInfoVO.getDistributeStatus()))
                    ? "" : DistributeStatusEnum.enumOf(ocmsDeliveryInfoVO.getDistributeStatus()).getDesc();
            orderVO.setDistributeStatusDesc(distributeStatusDesc.equals(UN_KNOW) ? "" : distributeStatusDesc);
            orderVO.setOrderStatus(ocmsOrderVO.getOrderStatus());
            orderVO.setOrderStatusDesc(OrderStatusEnum.enumOf(ocmsOrderVO.getOrderStatus()).getDesc());

            ChannelOrderNewStatusEnum historyOrderStatus = getHistoryOrderStatus(ocmsOrderVO.getFuseOrderStatus(), ocmsOrderVO.getOrderStatus());
            if(Objects.nonNull(historyOrderStatus)){
                orderVO.setFuseOrderStatus(historyOrderStatus.getCode());
                orderVO.setFuseOrderStatusDesc(historyOrderStatus.getDesc());
            }
            orderVO.setCreateTime(ocmsOrderVO.getCreateTime());
            orderVO.setDeliveryOrderType(ocmsOrderVO.getIsBooking() == 1 ? DeliveryOrderType.DELIVERY_BY_BOOK_TIME.getValue() : DeliveryOrderType.DELIVERY_RIGHT_NOW.getValue());
            orderVO.setDeliveryOrderTypeName(getDeliveryOrderTypeName(orderVO.getDeliveryOrderType()));
            orderVO.setUpdateTime(ocmsOrderVO.getUpdateTime());
            orderVO.setChannelExtraOrderId(ocmsOrderVO.getExtOrderId());
            //备注不为空且不为0才展示
            if (StringUtils.isNotEmpty(ocmsOrderVO.getComments()) && !ocmsOrderVO.getComments().equals("0")) {
                orderVO.setComments(ocmsOrderVO.getComments());
            }
            orderVO.setTotalOfflinePrice(ocmsOrderVO.getTotalOfflinePrice());
            Map<String, OCMSOrderItemVO> orderItemMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(ocmsOrderItemVOList)) {
                orderItemMap = ocmsOrderItemVOList.stream().filter(item -> StringUtils.isNotBlank(item.getInstoreSkuId2())).collect(Collectors.toMap(OCMSOrderItemVO::getInstoreSkuId2, item -> item, (k1, k2) -> k1));
                orderVO.setProductList(ocmsOrderItemVOList.stream().filter(Objects::nonNull).map(this::buildProductVO).filter(Objects::nonNull).collect(Collectors.toList()));
                // 数量
                Integer itemCount = ocmsOrderItemVOList.stream().map(OCMSOrderItemVO::getQuantity).reduce(0, Integer::sum);
                orderVO.setItemCount(itemCount);
                // 种类
                Integer skuCount = Math.toIntExact(ocmsOrderItemVOList.stream().map(v -> v.getInstoreSkuId2() + v.getCustomerSkuId() + v.getSkuName()).distinct().count());
                orderVO.setSkuCount(skuCount);
                Integer orderWeight = ocmsOrderItemVOList.stream().mapToInt(this::calcProductItemWeight).sum();
                orderVO.setWeight(orderWeight);
            } else {
                orderVO.setItemCount(0);
                orderVO.setProductList(ocmsOrderItemVOList.stream().filter(Objects::nonNull).map(this::buildProductVO).collect(Collectors.toList()));
                orderVO.setSkuCount(0);
                orderVO.setWeight(0);
            }
            if (CollectionUtils.isNotEmpty(ocmsOrderVO.getOnlineGiftVOS())) {
                orderVO.setGiftVOList(ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull).map(this::buildGiftVO).collect(Collectors.toList()));
                orderVO.setGiftCount(ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull).mapToInt(OnlineGiftVO::getGiftQuantity).sum());
                addGift2SkuCount(orderVO, ocmsOrderVO.getOnlineGiftVOS(), ocmsOrderItemVOList);
            }
            //build refund info
            if (CollectionUtils.isNotEmpty(ocmsOrderVO.getAfterSaleApplyVOList())) {
                OrderRefundInfo orderRefundInfo = new OrderRefundInfo();
                removeInvalidAfterSaleApply(ocmsOrderVO, ocmsOrderVO.getAfterSaleApplyVOList());
                //只处理用户退款，不处理用户申述
                if (Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), ocmsOrderVO.getOrderStatus())) {
                    OCMSAfterSaleApplyVO afterSaleApplyVO = Optional.ofNullable(ocmsOrderVO.getAfterSaleApplyVOList()).map(List::stream).orElse(Stream.empty())
                            .filter(OCMSAfterSaleApplyVO::isWait2Audit).findFirst().orElse(null);
                    OrderStatusLog orderStatusLog = ocmsOrderVO.getOrderStatusLogList().stream()
                            .filter(e -> Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), e.getTargetStatus()))
                            .max(Comparator.comparingLong(e -> e.getCreateTime()))
                            .orElse(null);
                    if (afterSaleApplyVO != null && orderStatusLog != null) {
                        //设置等待退款的申请，这里的待审批信息会关联订单状态，退差价不会改变订单状态
                        orderRefundInfo.setWaitAuditRefund(buildWaitAuditRefund(orderStatusLog, afterSaleApplyVO, orderItemMap));
                    }

                    // 设置多条待审核售后单
                    List<OCMSAfterSaleApplyVO> afterSaleApplyVOList = Optional.ofNullable(ocmsOrderVO.getAfterSaleApplyVOList()).map(List::stream).orElse(Stream.empty())
                            .filter(OCMSAfterSaleApplyVO::isWait2Audit).collect(Collectors.toList());
                    if (afterSaleApplyVO != null && orderStatusLog != null) {
                        //设置等待退款的申请列表，这里的待审批信息会关联订单状态，退差价不会改变订单状态
                        orderRefundInfo.setWaitAuditRefundList(buildWaitAuditRefundList(orderStatusLog, afterSaleApplyVOList, orderItemMap));
                    }
                }
                //这里的退款信息会关联订单状态，退差价不会改变订单状态
                List<RefundLog> refundLogList = ocmsOrderVO.getOrderStatusLogList().stream().filter(e -> isRefundConcernStatusChange(e))
                        .map(orderStatusLog -> {
                            boolean isAuditRefund = Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), orderStatusLog.getSourceStatus());
                            OCMSAfterSaleApplyVO afterSaleApplyVO = findNearestTimeAfterSale(orderStatusLog.getCreateTime(), ocmsOrderVO.getAfterSaleApplyVOList(), isAuditRefund);
                            //只处理处理完的售后申请, 京东的暂存售后单也不显示给前端
                            if (afterSaleApplyVO != null && !afterSaleApplyVO.isWait2Audit()
                                    && DRAFT.getValue().equals(afterSaleApplyVO.getStatus())
                                    && AfterSaleApplyStatusEnum.DRAFT_DONE.getValue().equals(afterSaleApplyVO.getStatus())) {
                                return RefundLog.buildRefundLog(orderStatusLog, afterSaleApplyVO);
                            }
                            return null;
                        }).filter(Objects::nonNull).sorted(Comparator.comparingLong(RefundLog::getOptTime).reversed()).collect(Collectors.toList());
                //添加退差价信息,重排序
                refundLogList.addAll(addWeightRefundLog(ocmsOrderVO));
                refundLogList = refundLogList.stream().sorted(Comparator.comparingLong(RefundLog::getOptTime).reversed()).collect(Collectors.toList());
                orderRefundInfo.setRefundLogs(refundLogList);
                orderVO.setOrderRefundInfo(orderRefundInfo);
            }
            setOrderRevenueDetailResponse(orderRevenueDetailResponse, ocmsOrderVO, orderProfit, orderVO);

            try {
                if (ChannelOnlineTypeEnum
                        .isPrivate(DynamicOrderBizType.findOf(ocmsOrderVO.getOrderBizType()).getChannelStandard())) {
                    orderVO.setPayments(Optional.ofNullable(ocmsOrderVO.getPayments()).map(List::stream)
                            .orElse(Stream.empty()).map(PaymentVO::new).collect(Collectors.toList()));
                }
            } catch (Exception e) {
                log.info("私域渠道设置支付信息异常", e);
            }
            orderVO.setUserTags(UserTagTypeEnum.getTags(ocmsOrderVO.getTags()));
            orderVO.setIsNeedInvoice(ocmsOrderVO.getIsNeedInvoice());
            orderVO.setActionTagList(ocmsOrderVoConvertTag(ocmsOrderVO));
            orderVO.setOrderOperatorLogList(ocmsOrderVoConvertOperatorLog(ocmsOrderVO));
            orderVO.setOrderTagList(OrderTagVO.convertOrderTagList(ocmsOrderVO,showLabelList));
            orderVO.setMigrateFlag(ocmsOrderVO.getMigrateFlag());
            orderVO.setInvoiceTitle(ocmsOrderVO.getInvoiceTitle());
            orderVO.setInvoiceTaxNo(ocmsOrderVO.getInvoiceTaxNo());
            orderVO.setInvoiceType(ocmsOrderVO.getInvoiceType());
            orderVO.setInvoiceMoney(ocmsOrderVO.getInvoiceMoney());
            orderVO.setToken(OrderUtils.generateMD5Token(ocmsOrderVO.getViewOrderId(), channelId, ocmsOrderVO.getTenantId()));
            orderVO.setIsFastOrder(ocmsOrderVO.getIsFastOrder());
            orderVO.setIsFastToSelfDelivery(ocmsOrderVO.getIsFastToSelfDelivery());
            orderVO.setCompensationModel(buildCompensationModel(ocmsOrderVO.getCompensation()));
            orderVO.setDownFlag(ocmsOrderVO.getDownFlag());
            orderVO.setDegradeModules(ocmsOrderVO.getDegradeModules());
            orderVO.setCompensationModelList(CompensationUtil.buildCompensationModelList(ocmsOrderVO.getCompensation()));
            orderVO.setSuspectSource(ocmsOrderVO.getSuspectSource());
            return orderVO;
        }catch (Exception e){
            log.info("FuseOrderServiceImpl.buildOrderFuseVO error:{}", e.getMessage());
        }
        return null;

    }

    private CompensationVO buildCompensationModel(String compensation){
        try {
            if(StringUtils.isEmpty(compensation)){
                return null;
            }
            return JacksonUtils.fromJson(compensation, CompensationVO.class);
        }catch (Exception e){
            log.error("OrderService.buildCompensationVO error", e);
        }
        return null;
    }

    /**
     * 将赠品数量计算到商品数量中
     * @param orderVO
     * @param onlineGifts
     * @param ocmsOrderItemVOList
     */
    private void addGift2SkuCount(OrderFuseVO orderVO, List<OnlineGiftVO> onlineGifts, List<OCMSOrderItemVO> ocmsOrderItemVOList) {
        try {
            Long tenantId = orderVO.getTenantId();
            TenantBusinessModeEnum businessMode = tenantService.queryTenantBusinessMode(tenantId);

            if (!TenantBusinessModeEnum.MEDICINE_UNMANNED_WAREHOUSE.equals(businessMode)
                    || !com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil.isUmwOrderItemsIncludeGift(tenantId)) {
                return;
            }

            Map<String, Integer> giftSkuKey2Count = new HashMap<>();
            onlineGifts.stream().filter(Objects::nonNull).forEach(onlineGift -> {
                String skuId = onlineGift.getGiftSku();
                String skuName = onlineGift.getGiftName();
                Integer skuQuantity = onlineGift.getGiftQuantity();

                if (StringUtils.isBlank(skuName) || skuQuantity == null || skuQuantity < 1) {
                    return;
                }
                // 如果配置了2条赠送活动 且是同一个sku 则需要累加
                giftSkuKey2Count.compute(assembleSkuKey(skuId, skuName),
                        (k, oldVal) -> oldVal == null ? skuQuantity : oldVal + skuQuantity);
            });

            if (MapUtils.isEmpty(giftSkuKey2Count)) {
                return;
            }

            Set<String> itemExcludeGiftSkuKeys = new HashSet<>();
            if (CollectionUtils.isNotEmpty(ocmsOrderItemVOList)) {
                ocmsOrderItemVOList.forEach(ocmsOrderItemVO -> {
                    String key = assembleSkuKey(ocmsOrderItemVO.getInstoreSkuId2(), ocmsOrderItemVO.getSkuName());

                    if (!giftSkuKey2Count.containsKey(key)) {
                        itemExcludeGiftSkuKeys.add(key);
                    }
                });
            }
            // 重新计算商品种类
            orderVO.setSkuCount(giftSkuKey2Count.size() + itemExcludeGiftSkuKeys.size());
            Integer giftItemCount = giftSkuKey2Count.values().stream().reduce(0, Integer::sum);
            // 增加赠品数量
            orderVO.setItemCount(orderVO.getItemCount() + giftItemCount);
        } catch (Exception e) {
            log.error("[order-list] 统计赠品数量出现异常, tenantId: [{}], 订单id: [{}]", orderVO.getTenantId(), orderVO.getOrderId(), e);
        }
    }

    private String assembleSkuKey(String skuId, String skuName) {
        String tmpName = StringUtils.isBlank(skuId) ? StringUtils.EMPTY : skuId;
        return tmpName + "_" + skuName;
    }

    private boolean orderFinishedOverTwoDays(Long completeTime, Long createTime){
        if (completeTime == null || createTime == null){
            return false;
        }
        if (completeTime < createTime){
            return false;
        }
        return completeTime + 48 * 3600 * 1000 < System.currentTimeMillis();
    }

    private boolean orderCancelOverTwoDays(Long createTime, Long cancelTime) {
        if (cancelTime == null || createTime == null){
            return false;
        }
        if (cancelTime < createTime){
            return false;
        }

        return cancelTime + 48 * 3600 * 1000 < System.currentTimeMillis();
    }


    /**
     * 订单营收信息
     */
    private void setOrderRevenueDetailResponse(OrderRevenueDetailResponse orderRevenueDetailResponse, OCMSOrderVO ocmsOrderVO, OrderProfitView orderProfit, OrderFuseVO orderVO) {
        if (orderRevenueDetailResponse != null && orderRevenueDetailResponse.getOrderAmountInfo() != null) {
            OrderAmountInfo orderAmountInfo = orderRevenueDetailResponse.getOrderAmountInfo();
            RevenueDetailVO revenueDetailVo = new RevenueDetailVO();
            revenueDetailVo.setPromotionInfos(orderRevenueDetailResponse.getPromotionInfos());
            revenueDetailVo.setActualPayAmount(orderAmountInfo.getActualPayAmt());
            if (!Integer.valueOf(OrderSourceEnum.GLORY.getValue()).equals(ocmsOrderVO.getOrderSource())) {
                revenueDetailVo.setBizActivityAmount(orderAmountInfo.getBizCharge());
            }
            revenueDetailVo.setDeliveryAmount(orderAmountInfo.getDeliveryFee());
            revenueDetailVo.setPackageAmount(orderAmountInfo.getPackageAmount());
            revenueDetailVo.setRevenueAmount(orderAmountInfo.getBizReceiveAmount());
            if (orderProfit != null) {
                revenueDetailVo.setNetProfitOnline(orderProfit.getProfit().intValue());
                revenueDetailVo.setWithDeliveryCost(orderProfit.getWithDeliveryCost());
            }
            orderVO.setRevenueDetail(revenueDetailVo);
        }
    }


    private ChannelOrderNewStatusEnum getHistoryOrderStatus(Integer fuseOrderStatus, Integer orderStatus){
        if(Objects.nonNull(ChannelOrderNewStatusEnum.getByCode(fuseOrderStatus))){
            return ChannelOrderNewStatusEnum.getByCode(fuseOrderStatus);
        }

        OrderStatusEnum orderStatusEnum = OrderStatusEnum.enumOf(orderStatus);
        if(Objects.isNull(orderStatusEnum)){
            return null;
        }
        switch (orderStatusEnum){
            case SUBMIT:
            case MERCHANT_CONFIRMED:
            case COMPLETED:
            case REFUND_APPLIED:
            case CANCELED:
                return ChannelOrderNewStatusEnum.getByCode(orderStatusEnum.getValue());
            case PICKING:
                return ChannelOrderNewStatusEnum.SHIPPING;
            default:
                return null;
        }
    }
    /**
     * 获取单条商品的重量
     * @param oCMSOrderItemVO
     * @return
     */
    private Integer calcProductItemWeight(OCMSOrderItemVO oCMSOrderItemVO) {
        if (Objects.isNull(oCMSOrderItemVO.getChannelWeight()) || Objects.isNull(oCMSOrderItemVO.getQuantity())) {
            return 0;
        }
        return oCMSOrderItemVO.getChannelWeight() * oCMSOrderItemVO.getQuantity();
    }

    private boolean isDeliveryInfoNotNull(OCMSDeliveryInfoVO ocmsDeliveryInfoVO) {
        return ObjectUtils.allNotNull(ocmsDeliveryInfoVO,
                ocmsDeliveryInfoVO.getCreateTime(),
                ocmsDeliveryInfoVO.getOrderId(),
                ocmsDeliveryInfoVO.getDeliveryStatus(),
                ocmsDeliveryInfoVO.getTenantId(),
                ocmsDeliveryInfoVO.getShopId());
    }

    /**
     * 判断是否支持拨打骑手隐私号（目前仅平台配送支持查询骑手隐私号）
     *
     * @param isSelfDelivery 是否为商家自配送 1:是  0:否
     * @return 是否支持拨打骑手隐私号
     */
    private static boolean judgeSupportDeliveryUserPrivacyPhone(Integer isSelfDelivery) {
        if (Objects.equals(isSelfDelivery, IS_SELF_DELIVERY_NO)) {
            // 平台配送，支持查询骑手隐私号
            return true;
        }
        return false;
    }

    private String getDeliveryOrderTypeName(Integer deliveryOrderType) {
        DeliveryOrderType deliveryOrderTypeEnum = DeliveryOrderType.findByValue(deliveryOrderType);
        if (Objects.isNull(deliveryOrderTypeEnum)) {
            return "未知";
        }
        switch (deliveryOrderTypeEnum) {
            case DELIVERY_RIGHT_NOW:
                return "立即送达";
            case DELIVERY_BY_BOOK_TIME:
                return "预计送达";
            default:
                return "未知";
        }
    }

    private ProductFuseVO buildProductVO(OCMSOrderItemVO ocmsOrderItemVO) {
        ProductFuseVO productVO = new ProductFuseVO();
        productVO.setOrderItemId(ocmsOrderItemVO.getOrderItemId());
        productVO.setSkuId(ocmsOrderItemVO.getCustomerSkuId());
        productVO.setUpcCode(ocmsOrderItemVO.getSkuCode());
        productVO.setSkuName(ocmsOrderItemVO.getSkuName());
        productVO.setPicUrl(ocmsOrderItemVO.getPicUrl());
        productVO.setSpecification(ocmsOrderItemVO.getSpecification());
        productVO.setSellUnit(ocmsOrderItemVO.getSellUnit());

        ExchangeUtil.ExchangeDO exchangeDO = ExchangeUtil.loadExchangeMetaData(ocmsOrderItemVO.getExtData());
        //如果是换货商品，直接不展示，过滤掉
        if(exchangeDO.getExchangeSourceOrderItemId() > 0){
            return null;
        }
        int quantity = exchangeDO.getOrderQuantity() == 0?ocmsOrderItemVO.getQuantity():exchangeDO.getOrderQuantity();
        productVO.setCount(quantity);
        if (Objects.nonNull(ocmsOrderItemVO.getOriginalPrice())) {
            productVO.setOriginalTotalPrice(ocmsOrderItemVO.getOriginalPrice() * quantity);
        }
        productVO.setTotalPayAmount(ocmsOrderItemVO.getTotalPayAmount());
        productVO.setUnitPrice(ocmsOrderItemVO.getUnitPrice());

        productVO.setBatchInfo(OrderItemExtInfoUtil.extractBatchInfo(ocmsOrderItemVO.getExtData()));
        productVO.setOrderItemOfflinePrice(ocmsOrderItemVO.getOfflinePrice());
        List<TagInfoVO> tagInfoVOS = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVO.getFulfillmentTagList())) {
            for (String fulfillmentTag : ocmsOrderItemVO.getFulfillmentTagList()) {
                TagInfoVO tagInfoVO = new TagInfoVO();
                tagInfoVO.setName(fulfillmentTag);
                tagInfoVO.setType(FULFILLMENT_TAG);
                tagInfoVOS.add(tagInfoVO);
            }
        }
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVO.getPickTagList())) {
            for (String pickTag : ocmsOrderItemVO.getPickTagList()) {
                TagInfoVO tagInfoVO = new TagInfoVO();
                tagInfoVO.setName(pickTag);
                tagInfoVO.setType(PICK_TAG);
                tagInfoVOS.add(tagInfoVO);

            }
        }
        //商品标签(需要排除履约和拣货标签)
        productVO.setTagInfos(Optional.ofNullable(ocmsOrderItemVO.getTagInfos())
                .orElse(Collections.emptyList()).stream().map(tag -> {
                    TagInfoVO tagInfoVO = new TagInfoVO();
                    tagInfoVO.setName(tag.getName());
                    tagInfoVO.setType(tag.getType());
                    return tagInfoVO;
                }).collect(Collectors.toList()));
        productVO.setTagInfoList(tagInfoVOS);
        productVO.setCurrentPrice(ocmsOrderItemVO.getCurrentPrice());

        // extData确认是否为赠品，设置赠品参数 0-平台赠品，1-自定义赠品
        productVO.setGiftType(OrderUtils.getExtDataAsInt("giftType", ocmsOrderItemVO.getExtData()));
        productVO.setChannelLabelList(ProductLabelUtil.buildChannelLabelVOList(ocmsOrderItemVO.getChannelLabelList()));
        productVO.setLabelSubDesc(ProductLabelUtil.buildChannelLabelSubDesc(ocmsOrderItemVO.getChannelLabelList(), ocmsOrderItemVO.getExtData()));
        return productVO;
    }

    private GiftVO buildGiftVO(OnlineGiftVO onlineGiftVO) {
        if (onlineGiftVO != null) {
            GiftVO giftVO = new GiftVO();
            giftVO.setGiftName(onlineGiftVO.getGiftName());
            giftVO.setGiftQuantity(onlineGiftVO.getGiftQuantity());
            return giftVO;
        }
        return null;
    }

    private void removeInvalidAfterSaleApply(OCMSOrderVO ocmsOrderVO, List<OCMSAfterSaleApplyVO> afterSaleApplyVOList) {
        if (CollectionUtils.isNotEmpty(afterSaleApplyVOList)){
            Iterator<OCMSAfterSaleApplyVO> it = afterSaleApplyVOList.iterator();
            while(it.hasNext()){
                OCMSAfterSaleApplyVO applyVO = it.next();
                if (StringUtils.isBlank(applyVO.getAfterSaleId()) || applyVO.getCreateTime() == null || applyVO.getUpdateTime() == null || applyVO.getOrderId() == null){
                    it.remove();
                    MetricHelper.build().name("order.afterSaleRecordInvalid.err").tag("tenantId", String.valueOf(ocmsOrderVO.getTenantId())).tag("storeId", String.valueOf(ocmsOrderVO.getShopId())).count();
                }
            }
        }
    }

    private List<RefundingRecordVO> buildWaitAuditRefundList(OrderStatusLog orderStatusLog, List<OCMSAfterSaleApplyVO> afterSaleApplyVOList, Map<String, OCMSOrderItemVO> orderItemMap) {
        List<RefundingRecordVO> refundingRecordVOList = new ArrayList<>();
        Optional.ofNullable(afterSaleApplyVOList).map(List::stream).orElse(Stream.empty()).forEach(afterSaleApplyVO->{
            RefundingRecordVO refundApplyRecordVO = new RefundingRecordVO();
            refundApplyRecordVO.setServiceId(afterSaleApplyVO.getServiceId());
            refundApplyRecordVO.setAfterSaleId(afterSaleApplyVO.getAfterSaleId());
            refundApplyRecordVO.setIsAudit(afterSaleApplyVO.getIsAudit());
            refundApplyRecordVO.setStatus(afterSaleApplyVO.getStatus());
            refundApplyRecordVO.setApplyReason(afterSaleApplyVO.getApplyReason());
            refundApplyRecordVO.setAfsPattern(afterSaleApplyVO.getAfsPattern());
            refundApplyRecordVO.setCreateTime(afterSaleApplyVO.getCreateTime());
            refundApplyRecordVO.setUpdateTime(afterSaleApplyVO.getUpdateTime());
            refundApplyRecordVO.setAfsApplyType(afterSaleApplyVO.getApplyType());
            refundApplyRecordVO.setWhoApplyType(afterSaleApplyVO.getApplyUserType());
            refundApplyRecordVO.setRefundOptContent(RefundLog.buildRefundContent(orderStatusLog, afterSaleApplyVO));
            if (afterSaleApplyVO.getApplyType() != null) {
                AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(afterSaleApplyVO.getApplyType());
                refundApplyRecordVO.setRefundApplyType(WaitToAuditRefundGoodsOrderSubTypeEnum.getSubTypeCode(afterSaleTypeEnum));
            }
            List<OCMSAfterSaleApplyDetailVO> ocmsAfterSaleApplyDetailVOList = afterSaleApplyVO.getOcmsAfterSaleApplyDetailVOList();
            if (CollectionUtils.isNotEmpty(ocmsAfterSaleApplyDetailVOList)) {
                List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOList = ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).map(this::buildRefundApplyRecordDetailVO).collect(Collectors.toList());
                // 将订单中的商品信息赋值到售后信息中
                if(MapUtils.isNotEmpty(orderItemMap)){
                    refundApplyRecordDetailVOList.stream().forEach(item -> {
                        if(orderItemMap.containsKey(item.getSkuId())){
                            item.setPicUrl(orderItemMap.get(item.getSkuId()).getPicUrl());
                            item.setOrderItemOfflinePrice(orderItemMap.get(item.getSkuId()).getOfflinePrice());
                            item.setTagInfos(orderItemMap.get(item.getSkuId()).getTagInfos());
                        }
                    });
                }
                refundApplyRecordVO.setRefundApplyRecordDetailVOList(refundApplyRecordDetailVOList);
                refundApplyRecordVO.setRefundProductCount(ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).mapToInt(OCMSAfterSaleApplyDetailVO::getCount).sum());
                // 退款总金额计算
                if(CollectionUtils.isNotEmpty(refundApplyRecordVO.getRefundApplyRecordDetailVOList())){
                    refundApplyRecordVO.setRefundAmt(refundApplyRecordVO.getRefundApplyRecordDetailVOList()
                            .stream().filter(Objects::nonNull)
                            .mapToInt(RefundApplyRecordDetailVO::getTotalRefundAmt)
                            .filter(Objects::nonNull).sum());
                }
            }
            refundApplyRecordVO.setRefundPicList(afterSaleApplyVO.getRefundPicList());
            refundingRecordVOList.add(refundApplyRecordVO);
        });
        return refundingRecordVOList;
    }

    private RefundApplyRecordDetailVO buildRefundApplyRecordDetailVO(OCMSAfterSaleApplyDetailVO ocmsAfterSaleApplyDetailVO) {
        return RefundApplyRecordDetailVO.buildRefundApplyRecordDetailVO(ocmsAfterSaleApplyDetailVO);
    }


    private RefundingRecordVO buildWaitAuditRefund(OrderStatusLog orderStatusLog, OCMSAfterSaleApplyVO afterSaleApplyVO, Map<String, OCMSOrderItemVO> orderItemMap) {
        RefundingRecordVO refundApplyRecordVO = new RefundingRecordVO();
        refundApplyRecordVO.setServiceId(afterSaleApplyVO.getServiceId());
        refundApplyRecordVO.setAfterSaleId(afterSaleApplyVO.getAfterSaleId());
        refundApplyRecordVO.setIsAudit(afterSaleApplyVO.getIsAudit());
        refundApplyRecordVO.setStatus(afterSaleApplyVO.getStatus());
        refundApplyRecordVO.setApplyReason(afterSaleApplyVO.getApplyReason());
        refundApplyRecordVO.setAfsPattern(afterSaleApplyVO.getAfsPattern());
        refundApplyRecordVO.setCreateTime(afterSaleApplyVO.getCreateTime());
        refundApplyRecordVO.setUpdateTime(afterSaleApplyVO.getUpdateTime());
        refundApplyRecordVO.setAfsApplyType(afterSaleApplyVO.getApplyType());
        refundApplyRecordVO.setWhoApplyType(afterSaleApplyVO.getApplyUserType());
        refundApplyRecordVO.setRefundOptContent(RefundLog.buildRefundContent(orderStatusLog, afterSaleApplyVO));
        if (afterSaleApplyVO.getApplyType() != null) {
            AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(afterSaleApplyVO.getApplyType());
            refundApplyRecordVO.setRefundApplyType(WaitToAuditRefundGoodsOrderSubTypeEnum.getSubTypeCode(afterSaleTypeEnum));
        }
        List<OCMSAfterSaleApplyDetailVO> ocmsAfterSaleApplyDetailVOList = afterSaleApplyVO.getOcmsAfterSaleApplyDetailVOList();
        if (CollectionUtils.isNotEmpty(ocmsAfterSaleApplyDetailVOList)) {
            List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOList = ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).map(this::buildRefundApplyRecordDetailVO).collect(Collectors.toList());
            // 将订单中的商品信息赋值到售后信息中
            if(MapUtils.isNotEmpty(orderItemMap)){
                refundApplyRecordDetailVOList.stream().forEach(item -> {
                    if(orderItemMap.containsKey(item.getSkuId())){
                        item.setPicUrl(orderItemMap.get(item.getSkuId()).getPicUrl());
                        item.setOrderItemOfflinePrice(orderItemMap.get(item.getSkuId()).getOfflinePrice());
                        item.setTagInfos(orderItemMap.get(item.getSkuId()).getTagInfos());
                    }
                });
            }
            refundApplyRecordVO.setRefundApplyRecordDetailVOList(refundApplyRecordDetailVOList);
            refundApplyRecordVO.setRefundProductCount(ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).mapToInt(OCMSAfterSaleApplyDetailVO::getCount).sum());
            // 退款总金额计算
            if(CollectionUtils.isNotEmpty(refundApplyRecordVO.getRefundApplyRecordDetailVOList())){
                refundApplyRecordVO.setRefundAmt(refundApplyRecordVO.getRefundApplyRecordDetailVOList()
                        .stream().filter(Objects::nonNull)
                        .mapToInt(RefundApplyRecordDetailVO::getTotalRefundAmt)
                        .filter(Objects::nonNull).sum());
            }
        }
        refundApplyRecordVO.setRefundPicList(afterSaleApplyVO.getRefundPicList());
        return refundApplyRecordVO;
    }

    private boolean isRefundConcernStatusChange(OrderStatusLog e) {
        if (e != null) {
            //不展示申述
            return Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), e.getSourceStatus()) || Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), e.getTargetStatus());
        }
        return false;
    }

    private OCMSAfterSaleApplyVO findNearestTimeAfterSale(Long orderStatusCreateTime, List<OCMSAfterSaleApplyVO> afterSaleApplyVOList, boolean isAuditRefund) {
        return afterSaleApplyVOList.stream()
                .filter(e -> !(isAuditRefund && e.isWait2Audit()))//如果是审批类型orderStatusLog，去掉那些还在处于退款审批中的记录
                .min(Comparator.comparingDouble(e -> {
                    long time = isAuditRefund ? e.getUpdateTime() : e.getCreateTime();
                    return Math.abs(time - orderStatusCreateTime);
                }))
                .orElse(null);
    }

    /**
     * 插入退差价到退款日志流
     *
     * @param ocmsOrderVO
     * @return
     */
    private List<RefundLog> addWeightRefundLog(OCMSOrderVO ocmsOrderVO) {
        List<OCMSAfterSaleApplyVO> weightRefundAfterSaleApplyList = ocmsOrderVO.getAfterSaleApplyVOList().stream().filter(afs -> Objects.nonNull(afs.getAfsPattern())&&afs.getAfsPattern()== AfterSalePatternEnum.WEIGHT.getValue() && AfterSaleApplyStatusEnum.AUDITED.getValue().equals(afs.getStatus())).collect(Collectors.toList());
        List<RefundLog> refundLogs = Lists.newArrayList();
        for (OCMSAfterSaleApplyVO applyVO : weightRefundAfterSaleApplyList) {
            OperatorTypeEnum operatorTypeEnum = OperatorTypeEnum.enumOf(applyVO.getApplyUserType());
            RefundLog refundLog = new RefundLog();
            refundLog.setOperator("");
            refundLog.setOptTime(applyVO.getCreateTime());
            refundLog.setOptContent("商家按重量退差价");
            refundLog.setAuditType(OcmsRefundAuditType.WeightRefund.getCode());
            refundLog.setOperatorType(operatorTypeEnum.getValue());
            refundLog.setRefundAmount(applyVO.getRefundAmt());
            refundLog.setOptDesc(applyVO.getApplyReason());
            List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOS = Lists.newArrayList();
            for (OCMSAfterSaleApplyDetailVO ocmsAfterSaleApplyDetailVO : applyVO.getOcmsAfterSaleApplyDetailVOList()) {
                RefundApplyRecordDetailVO recordDetailVO = RefundApplyRecordDetailVO.buildRefundApplyRecordDetailVO(ocmsAfterSaleApplyDetailVO);
                //如果是克重退款展示渠道退款价格，如果是部分退款老逻辑展示线下价格
                recordDetailVO.setRefundAmt(ocmsAfterSaleApplyDetailVO.getRefundAmt());
                recordDetailVO.setTotalRefundAmt(ocmsAfterSaleApplyDetailVO.getRefundAmt());
                refundApplyRecordDetailVOS.add(recordDetailVO);
            }
            refundLog.setRefundApplyRecordDetailVOList(refundApplyRecordDetailVOS);
            refundLog.setRefundPicList(applyVO.getRefundPicList());
            refundLogs.add(refundLog);
        }


        return refundLogs;
    }

    public static List<TagInfoVO> ocmsOrderVoConvertTag(OCMSOrderVO ocmsOrderVO){
        if(ocmsOrderVO==null){
            return Collections.emptyList();
        }
        List<TagInfoVO> tagInfoVOList=new ArrayList<>();
        if(ocmsOrderVO.getIsNeedInvoice()!=null && ocmsOrderVO.getIsNeedInvoice()==1){
            TagInfoVO tagInfoVO=new TagInfoVO();
            tagInfoVO.setName(ActionTagTypeEnum.INVOICE.getTemplate());
            tagInfoVO.setType(ActionTagTypeEnum.INVOICE.getType());
            tagInfoVOList.add(tagInfoVO);
        }
        return tagInfoVOList;
    }


    private static Long getCancelTime(OCMSOrderVO ocmsOrderVO){
        // 订单取消时间
        if(Objects.nonNull(ocmsOrderVO.getOrderStatusLogList())){
            Map<Integer, OrderStatusLog> orderStatusLogMap = ocmsOrderVO.getOrderStatusLogList().stream()
                    .filter(item -> Objects.nonNull(item.getOperateTime()) && OrderStatusEnum.CANCELED.getValue() == item.getTargetStatus())
                    .collect(Collectors.toMap(OrderStatusLog::getTargetStatus, it -> it, (o1, o2) -> o2));
            if(MapUtils.isNotEmpty(orderStatusLogMap)){
                return orderStatusLogMap.get(OrderStatusEnum.CANCELED.getValue()).getOperateTime();
            }
        }
        return null;
    }

    public static List<OrderOperatorLogVO> ocmsOrderVoConvertOperatorLog(OCMSOrderVO ocmsOrderVO) {
        if (ocmsOrderVO == null) {
            return Collections.emptyList();
        }
        List<OrderOperatorLogVO> orderOperatorLogVOList = new ArrayList<>();
        // 下单时间
        setOrderOperatorLogVO(OrderOperatorLogEnum.PLACEORDER.getValue(), ocmsOrderVO.getCreateTime(), orderOperatorLogVOList);
        // 接收时间
        if(Objects.nonNull(ocmsOrderVO.getReceiveTime()) && ocmsOrderVO.getReceiveTime() > DEFAULT_TIMESTAMP){
            setOrderOperatorLogVO(OrderOperatorLogEnum.RECEIVE.getValue(), ocmsOrderVO.getReceiveTime(), orderOperatorLogVOList);
        }
        // 接单时间
        if(Objects.nonNull(ocmsOrderVO.getMerchantTakeOrderTime()) && ocmsOrderVO.getMerchantTakeOrderTime() > DEFAULT_TIMESTAMP){
            setOrderOperatorLogVO(OrderOperatorLogEnum.CONFIRM.getValue(), ocmsOrderVO.getMerchantTakeOrderTime(), orderOperatorLogVOList);
        }

        // 拣货相关时间
        if(Objects.nonNull(ocmsOrderVO.getPickStatusLogList())){
            Map<Integer, PickStatusLogVO> pickStatusLogMap = ocmsOrderVO.getPickStatusLogList().stream().filter(item -> Objects.nonNull(item.getType())).collect(Collectors.toMap(PickStatusLogVO::getType, it -> it, (o1, o2) -> o2));
            if(pickStatusLogMap.containsKey(PickLogTypeEnum.PICKING.getValue())){
                // 开始拣货时间
                setOrderOperatorLogVO(OrderOperatorLogEnum.STARTPICK.getValue(), pickStatusLogMap.get(PickLogTypeEnum.PICKING.getValue()).getUpdateTime(), orderOperatorLogVOList);
            }
            if(pickStatusLogMap.containsKey(PickLogTypeEnum.PICKING_NOT_CONFIRM.getValue())){
                // 备货时间
                setOrderOperatorLogVO(OrderOperatorLogEnum.FINISHPICK.getValue(), pickStatusLogMap.get(PickLogTypeEnum.PICKING_NOT_CONFIRM.getValue()).getUpdateTime(), orderOperatorLogVOList);

            }
        }

        // 配送相关时间
        if(Objects.nonNull(ocmsOrderVO.getDeliveryStatusLogList())){
            Map<Integer, DeliveryStatusLogVo> deliveryStatusLogVoMap = ocmsOrderVO.getDeliveryStatusLogList()
                    .stream().filter(item -> Objects.nonNull(item.getOperateTime()))
                    .collect(Collectors.toMap(DeliveryStatusLogVo::getTargetStatus, it -> it, (o1, o2) -> o2));
            if(deliveryStatusLogVoMap.containsKey(DistributeStatusEnum.RIDER_TAKE_GOODS.getValue())){
                // 配送开始时间
                setOrderOperatorLogVO(OrderOperatorLogEnum.DELIVERY.getValue(), deliveryStatusLogVoMap.get(DistributeStatusEnum.RIDER_TAKE_GOODS.getValue()).getOperateTime(), orderOperatorLogVOList);
            }

            if(deliveryStatusLogVoMap.containsKey(DistributeStatusEnum.RIDER_DELIVERED.getValue())){
                // 骑手送达时间
                setOrderOperatorLogVO(OrderOperatorLogEnum.RIDER_DELIVERED.getValue(), deliveryStatusLogVoMap.get(DistributeStatusEnum.RIDER_DELIVERED.getValue()).getOperateTime(), orderOperatorLogVOList);
            }
        }

        // 自提完成时间
        if(Objects.nonNull(ocmsOrderVO.getSelfFetchCompleteTime()) && ocmsOrderVO.getSelfFetchCompleteTime() > DEFAULT_TIMESTAMP){
            setOrderOperatorLogVO(OrderOperatorLogEnum.SELF_FETCH_FINISH.getValue(), ocmsOrderVO.getSelfFetchCompleteTime(), orderOperatorLogVOList);
        }

        // 完成时间
        if(Objects.nonNull(ocmsOrderVO.getOrderCompleteTime()) && ocmsOrderVO.getOrderCompleteTime() > DEFAULT_TIMESTAMP){
           setOrderOperatorLogVO(OrderOperatorLogEnum.FINISH.getValue(), ocmsOrderVO.getOrderCompleteTime(), orderOperatorLogVOList);
        }

        // 核销时间
        if(Objects.nonNull(ocmsOrderVO.getPosCompleteTime()) && ocmsOrderVO.getPosCompleteTime() > DEFAULT_TIMESTAMP){
            setOrderOperatorLogVO(OrderOperatorLogEnum.POS.getValue(), ocmsOrderVO.getPosCompleteTime(), orderOperatorLogVOList);
        }

        // 订单取消时间
        if(Objects.nonNull(ocmsOrderVO.getCancelTime()) && ocmsOrderVO.getCancelTime() > DEFAULT_TIMESTAMP){
            setOrderOperatorLogVO(OrderOperatorLogEnum.CANCEL.getValue(), ocmsOrderVO.getCancelTime(), orderOperatorLogVOList);
        } else if(Objects.nonNull(ocmsOrderVO.getOrderStatusLogList())){
            Map<Integer, OrderStatusLog> orderStatusLogMap = ocmsOrderVO.getOrderStatusLogList().stream()
                    .filter(item -> Objects.nonNull(item.getOperateTime()) && OrderStatusEnum.CANCELED.getValue() == item.getTargetStatus())
                    .collect(Collectors.toMap(OrderStatusLog::getTargetStatus, it -> it, (o1, o2) -> o2));
            if(MapUtils.isNotEmpty(orderStatusLogMap)){
                // 订单取消时间
                setOrderOperatorLogVO(OrderOperatorLogEnum.CANCEL.getValue(), orderStatusLogMap.get(OrderStatusEnum.CANCELED.getValue()).getOperateTime(), orderOperatorLogVOList);
            }
        }
        return orderOperatorLogVOList;
    }

    public static void setOrderOperatorLogVO(Integer type, Long opeatorTime, List<OrderOperatorLogVO> orderOperatorLogVOList){
        if(Objects.isNull(type) || Objects.isNull(opeatorTime)){
            return ;
        }
        OrderOperatorLogEnum orderOperatorLog = OrderOperatorLogEnum.getByValue(type);
        OrderOperatorLogVO orderOperatorLogVO = new OrderOperatorLogVO();
        orderOperatorLogVO.setOperatorType(orderOperatorLog.getValue());
        orderOperatorLogVO.setOperatorDesc(orderOperatorLog.getDesc());
        orderOperatorLogVO.setOperationTime(String.valueOf(opeatorTime));
        orderOperatorLogVOList.add(orderOperatorLogVO);
    }


    private void setRefundPriceDisplayType4OrderList(List<OCMSOrderVO> ocmsOrderVOS, int type) {
        if(CollectionUtils.isEmpty(ocmsOrderVOS)){
            return;
        }
        ocmsOrderVOS.stream()
                .filter(Objects::nonNull)
                .map(OCMSOrderVO::getAfterSaleApplyVOList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(OCMSAfterSaleApplyVO::getOcmsAfterSaleApplyDetailVOList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .forEach(detailVO -> detailVO.setPriceDisplayType(type));
    }

    private List<OrderPrintResultDto> queryOrderPrintResult(long tenantId, long storeId, List<OCMSOrderVO> orders) {
        if (!CollectionUtils.isNotEmpty(orders)) {
            return Collections.emptyList();
        }

        List<OrderPrintQueryDto> orderPrintQueryDtos = orders.stream()
                .filter(e -> e.getOrderSource() != null && StringUtils.isNotBlank(e.getViewOrderId()))
                .map(e -> {
                    OrderPrintQueryDto orderPrintQueryDto = new OrderPrintQueryDto();
                    orderPrintQueryDto.setOrderNo(e.getViewOrderId());
                    orderPrintQueryDto.setOrderSource(e.getOrderBizType());
                    return orderPrintQueryDto;
                })
                .collect(Collectors.toList());
        OrderPrintResultQueryRequest req = new OrderPrintResultQueryRequest();
        req.setOfflineStoreId(storeId);
        req.setTenantId(tenantId);
        req.setOrderList(orderPrintQueryDtos);
        try {
            OrderPrintResultQueryResponse resp = pickSelectPrintQueryThriftService.queryOrderPrintResult(req);
            log.info("请求履约获取订单打印状态，request: {}, response: {}", req, resp);
            if (CollectionUtils.isEmpty(resp.getPrintResultList())) {
                return Collections.emptyList();
            }
            return resp.getPrintResultList();
        } catch (Exception e) {
            log.error("请求订单打印状态失败,shopId:{}, tenantId:{}", storeId, tenantId, e);
            return Collections.emptyList();
        }
    }

    private OrderPrintStatusVO buildPrintStatusVo(OrderPrintResultDto printResultDto) {
        OrderPrintStatusVO printStatusVo = new OrderPrintStatusVO();
        printStatusVo.setPrintStatus(printResultDto.getPrintStatus());
        printStatusVo.setPrintFailToast(generatePrintToast(printResultDto));
        printStatusVo.setDeviceInfo(printResultDto.getDeviceInfo());
        return printStatusVo;
    }

    private String generatePrintToast(OrderPrintResultDto printResultDto) {
        String toast = null;
        /***
         * 打印状态，40，45，99，999 都归类成“打印失败”
         * **/
        PrintOpLogStatusEnum printStatusEnum = PrintOpLogStatusEnum.enumOf(printResultDto.getPrintStatus());
        if (printStatusEnum != null) {
            switch (printStatusEnum) {
                case PRINT_TIME_OUT:
                    toast = LionUtils.getOrderPrintTimeoutTips();
                    break;
                case SERVICE_NOT_AVAILABLE:
                case PRINTER_OFFLINE:
                case PRINTER_ABNORMAL:
                case UNKNOWN:
                    toast = LionUtils.getOrderPrintFailTips();
                    break;
                case PRINTING:
                case WAITING_FOR_PRINT:
                case PRINT_SUCCESS:
                    toast = null;
                    break;
                default:
                    log.error("未知打印状态码:{}", printStatusEnum);

            }
        }
        return toast;
    }

    private void setOrderRecommendTransfer(Long tenantId, OrderFuseListResponse response) {
        try {
            if (Objects.isNull(tenantId) || Objects.isNull(response) || CollectionUtils.isEmpty(response.getOrderList())) {
                return;
            }

            if (MccConfigUtil.isNotRecommendTransferOrderTenant(tenantId)) {
                return;
            }

            // 限制推荐订单状态范围（待拣货），转单门店存在的无需查询
            List<String> orderIdList = response.getOrderList().stream().
                    filter(orderVO -> StringUtils.isNotBlank(orderVO.getEmpowerOrderId())
                            && Objects.isNull(orderVO.getDispatchShopId())
                            && isShowRecommendTransferOrderViewStatus(orderVO.getFuseOrderStatus()))
                    .map(OrderFuseVO::getEmpowerOrderId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderIdList)) {
                return;
            }
            RecommendTransferSearchReq request = new RecommendTransferSearchReq();
            request.setTenantId(tenantId);
            request.setOrderIdList(orderIdList);
            RecommendTransferSearchResponse result = orderTransferWarehousePlanService.batchSearchRecommendTransferOrder(request);
            if (Objects.isNull(result) || Objects.isNull(result.getCode()) || result.getCode() != 0) {
                log.warn("查询推荐转单失败, request:{}, response:{}", JacksonUtils.toJson(request), JacksonUtils.toJson(result));
                return;
            }

            if (Objects.isNull(result.getData()) || CollectionUtils.isEmpty(result.getData().getRecommendOrderId())) {
                return;
            }

            // 存在转单推荐订单
            for (OrderFuseVO each : response.getOrderList()) {
                if (result.getData().getRecommendOrderId().contains(each.getEmpowerOrderId())) {
                    each.setIsRecommendDispatch(Boolean.TRUE);
                }
            }
        } catch (Exception e) {
            log.error("Error occurred while setting order recommend transfer, tenantId: {}, msg: {}", tenantId, e.getMessage(), e);
        }
    }

    private boolean isShowRecommendTransferOrderViewStatus(Integer fuseOrderStatus) {
        // 状态为待拣货允许展示
        if (Objects.nonNull(fuseOrderStatus)
                && ChannelOrderNewStatusEnum.MERCHANT_CONFIRMED.getCode() == fuseOrderStatus) {
            return true;
        }

        return false;
    }

    /**
     * 校验导出限制
     * @param exportLimitRequest
     */
    private void checkExportCountLimit(ExportLimitRequest exportLimitRequest) {
        // 查询导出限制，判断是否超过导出限制
        ExportLimitResponse limitResponse = fuseOrderListExportThriftService
                .queryExecuteExportLimit(exportLimitRequest);
        log.info("queryExecuteExportLimit limitResponse:{}", limitResponse);
        ResponseHandler.checkResponseAndStatus(limitResponse, rep -> rep.getStatus().getCode(),
                rep -> rep.getStatus().getMessage());
        if (limitResponse.getIsOverLimit()) {
            String msg = "";
            if (Objects.nonNull(exportLimitRequest.getOrderListReq())
                    || Objects.nonNull(exportLimitRequest.getOrderDetailListReq())) {
                msg = "订单";
            }
            if (Objects.nonNull(exportLimitRequest.getRefundOrderLisReq())
                    || Objects.nonNull(exportLimitRequest.getAfsOrderDetailListReq())) {
                msg = "退单";
            }
            throw new CommonRuntimeException("超出导出上限,单次最多导出" + limitResponse.getExportLimitCount() + "条" + msg);
        }
    }

    /**
     * 设置退单列表可操作按钮
     * 
     * @param item
     * @param refundApplyRecordBo
     */
    private void setRefundCouldOperateItems(OCMSAfterSaleVO item, RefundApplyRecordBo refundApplyRecordBo) {
        List<Integer> refundCouldOperateItems = new ArrayList<>();
        // 设置退单补录入操作按钮，只有退单完成时才能进行补录入
        boolean isNotImport = Objects.equals(item.getExchangeStatusType(),
                AfterSaleExchangeTypeEnum.NOT_IMPORT.getValue());
        if (isNotImport && (Objects.equals(item.getStatus(), AfterSaleApplyStatusEnum.FINISH.getValue())
                || Objects.equals(item.getStatus(), AfterSaleApplyStatusEnum.AUTO_AUDITED.getValue())
                || Objects.equals(item.getStatus(), AfterSaleApplyStatusEnum.AUDITED.getValue()))) {
            refundCouldOperateItems.add(RefundCanOperateItem.REFUND_EXCHANGE_PRODUCT_ENTER.getValue());
        }
        refundApplyRecordBo.setRefundCouldOperateItems(refundCouldOperateItems);
        // 录入状态，在审核时进行判断是否需要录入
        refundApplyRecordBo.setIsNotImport(isNotImport);
    }
}

