package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ChannelLabelVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.AfsExchangeProductVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


@Setter
@Getter
@ToString
public class AfsItemInfoVO {
    @FieldDoc(description = "sku编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "sku编码")
    private String sku;

    @FieldDoc(description = "线上渠道sku编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "线上渠道sku编码")
    private String customSkuId;

    @FieldDoc(description = "商品名称", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @FieldDoc(description = "商品线下编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "商品线下编码")
    private String erpItemCode;

    @FieldDoc(description = "线上一级分类", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "线上一级分类")
    private String category;

    @FieldDoc(description = "线上二级分类", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "线上二级分类")
    private String subCategory;

    @FieldDoc(description = "upc编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "upc编码")
    private String upc;

    @FieldDoc(description = "规格", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "规格")
    private String spec;

    @FieldDoc(description = "重量", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "重量")
    private String weight;

    @FieldDoc(description = "单位", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "单位")
    private String unit;

    @FieldDoc(description = "商品金额", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "商品金额")
    private String totalPrice;

    @FieldDoc(description = "取消数量", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "取消数量")
    private String refundCount;

    @FieldDoc(description = "单据原始数量", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "单据原始数量")
    private Long orderItemId;

    @FieldDoc(description = "原价", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "原价")
    private String originPrice;

    @FieldDoc(description = "售价", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "售价")
    private String salePrice;

    @FieldDoc(description = "平台整单优惠", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "平台整单优惠")
    private String platDiscount;

    @FieldDoc(description = "平台单品优惠", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "平台单品优惠")
    private String platItemDiscount;

    @FieldDoc(description = "商家整单优惠", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "商家整单优惠")
    private String poiDiscount;

    @FieldDoc(description = "商家单品优惠", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "商家单品优惠")
    private String poiItemDiscount;

    @FieldDoc(description = "采购价", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "采购价")
    private String purchaseAmt;

    @FieldDoc(description = "采购折扣", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "采购折扣")
    private String purchaseDiscountAmt;

    @FieldDoc(description = "整合营销推广结算(商家）", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "整合营销推广结算(商家）")
    private String poiMarketDiscount;

    @FieldDoc(description = "整合营销推广结算(供应商）", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "整合营销推广结算(供应商）")
    private String supplierMarketDiscount;

    @FieldDoc(description = "图片链接", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "图片链接")
    public String picUrl;

    @FieldDoc(description = "组合商品", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "组合商品")
    public List<SubProductVo> subProduct;

    @FieldDoc(description = "商品标签信息", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "商品标签信息")
    private List<ChannelLabelVO> channelLabelList;

    @FieldDoc(description = "售后商品对应的换货商品信息", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "售后商品对应的换货商品信息")
    private AfsExchangeProductVo afsExchangeProduct;

    @FieldDoc(description = "退款明细数量字段", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "退款明细数量字段")
    private String quantity;

    @FieldDoc(description = "商品标签附加信息", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "商品标签附加信息")
    private String labelSubDesc;

}