package com.sankuai.shangou.qnh.orderapi.controller.app;

import com.alibaba.fastjson.JSONObject;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderDetail;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.RefundTypeEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.exception.ParamInvalidException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.qnh.orderapi.annotation.ValidHeaderParam;
import com.sankuai.shangou.qnh.orderapi.annotation.app.Auth;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.User;
import com.sankuai.shangou.qnh.orderapi.domain.request.CreateQnhInvoiceRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.*;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.CancelOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.ChangedOrderListForAppLocalCacheRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.CheckRefundRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.CheckSelfFetchCodeRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.ConfirmOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.HomePageRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderDeliveryDetailRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderListForAppLocalCacheRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderListRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderMoneyRefundCheckRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderMoneyRefundRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderPickCompleteRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderSearchRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.PartRefundCheckRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.PartRefundRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.PrintReceiptRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryCustomerLatestOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryDeliveryErrorOrderBySubTypeRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryDeliveryErrorOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryMccConfigRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryOrderDetailRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryPrintStatusRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryVirtualPhoneRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryWaitAuditRefundGoodsBySubTypeRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryWaitAuditRefundGoodsSubTypeCountRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryWaitAuditRefundRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryWaitDeliverySubTypeCountRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryWaitToConfirmOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryWaitToDeliveryOrderBySubTypeRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryWaitToDeliveryOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryWaitToPickOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryWaitToSelfFetchOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.RefundApplyAuditRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.RefundByWeightRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.RefundReasonAndCodeRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.VerifySelfFetchCodeRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.WeightRefundCheckRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.WeightRefundUpdateConfigRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.CreateQnhInvoiceResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.GetOrderOperateItemsResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryShopPickerResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.*;
import com.sankuai.shangou.qnh.orderapi.enums.AuditResultEnum;
import com.sankuai.shangou.qnh.orderapi.enums.AuditStageEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.ErrorCodeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.remote.OCMSOrderRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.OcmsChannelRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.app.EmpowerOrderService;
import com.sankuai.shangou.qnh.orderapi.service.app.OrderService;
import com.sankuai.shangou.qnh.orderapi.service.app.SaleReturnService;
import com.sankuai.shangou.qnh.orderapi.service.common.HomePageService;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.RequestContextUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/11/18
 * desc:
 */
@InterfaceDoc(
        displayName = "订单服务",
        type = "restful",
        scenarios = "包含查询各个状态的订单列表",
        description = "包含查询各个状态的订单列表",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "订单服务")
@RestController
@RequestMapping("/pieapi/order")
public class OrderController {

    @Resource
    private OrderService orderService;
    @Resource(name = "appSaleReturnService")
    private SaleReturnService saleReturnService;

    @Resource
    private HomePageService homePageService;

    @Resource
    private EmpowerOrderService empowerOrderService;
    @Resource
    private OcmsChannelRemoteService ocmsChannelRemoteService;
    @Resource
    private OCMSOrderRemoteService ocmsOrderRemoteService;


    @MethodDoc(
            displayName = "订单tab页首页查询接口",
            description = "订单tab页首页查询接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "订单tab页首页查询接口请求",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/homepage",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "订单tab页首页查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/homepage", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<OrderHomePageResponse> homePage(@RequestBody HomePageRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
            return homePageService.appHomePage(request);
        }
        CommonResponse<OrderHomePageResponse> homePageResponse = (identityInfo.isFullStoreMode()
                && orderService.isTenantSupportMultiStoreMode()) ? orderService.homepage4MultiStore(request)
                        : orderService.homePage(request);
        fillAfterSaleCount(request, homePageResponse);
        return homePageResponse;
    }

    private void fillAfterSaleCount(HomePageRequest request,CommonResponse<OrderHomePageResponse> homePageResponse) {
        try{
            Assert.notNull(homePageResponse,"homePageResponse is null");
            Assert.notNull(homePageResponse.getData(),"homePageResponse.getData() is null");
            Assert.notNull(homePageResponse.getData().getOrderPendingTaskVO(),"homePageResponse.getData().getOrderPendingTaskVO() is null");
            OrderPendingTaskVO orderPendingTaskVO = homePageResponse.getData().getOrderPendingTaskVO();
            orderPendingTaskVO.setWaitToAuditRefundGoodsCount(0);

            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            CommonResponse<RefundGoodsSubTypeCountResponse> afterSaleResponse =  orderService.queryWaitAuditRefundGoodsBySubtypeCount(identityInfo.getUser().getTenantId(), identityInfo.getStoreIdList(), request.getEntityType());
            Assert.notNull(afterSaleResponse,"afterSaleResponse is null");
            Assert.notNull(afterSaleResponse.getData(),"afterSaleResponse.getData() is null");
            Assert.notNull(afterSaleResponse.getData().getAllSubTypeCount(),"afterSaleResponse.getData().getAllSubTypeCount() is null");
            orderPendingTaskVO.setWaitToAuditRefundGoodsCount(afterSaleResponse.getData().getAllSubTypeCount());
        } catch (Exception e){
            log.error("homePage fillAfterSaleCount error.",e);
        }
    }

    @MethodDoc(
            displayName = "分页查询待接单订单列表",
            description = "分页查询待接单订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询待接单订单列表请求",
                            type = QueryWaitToConfirmOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/querywaitconfirm",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询待接单订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querywaitconfirm", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @ValidHeaderParam
    public CommonResponse<OrderListResponse> queryWaitConfirmOrder(@Valid @RequestBody QueryWaitToConfirmOrderRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
             return empowerOrderService.queryWaitToConfirmOrder(identityInfo.getUser().getTenantId(), request.getPage(), request.getSize(), request.getEntityType());
        }
        return orderService.queryWaitToConfirmOrder(request.getPage(), request.getSize(), request.getEntityType());
    }

    @MethodDoc(
            displayName = "分页查询待拣货订单列表",
            description = "分页查询待拣货订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询待拣货订单列表请求",
                            type = QueryWaitToPickOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/querywaitpick",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询待拣货订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querywaitpick", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<OrderListResponse> queryWaitPickOrder(@Valid @RequestBody QueryWaitToPickOrderRequest request) {
        if (ApiMethodParamThreadLocal.getIdentityInfo().isFullStoreMode() && orderService.isTenantSupportMultiStoreMode()) {
            return orderService.queryWaitToPickOrder4MultiStore(request);
        }
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
            return empowerOrderService.queryWaitToPickOrder(request.getPage(), request.getSize(), request.getEntityType(), request.getSortType());
        }
        return orderService.queryWaitToPickOrder(request.getPage(), request.getSize(), request.getEntityType(), RequestContextUtils.getHeaderStoreId());
    }


    @MethodDoc(
            displayName = "查询订单详情",
            description = "查询订单详情",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询待拣货订单列表请求",
                            type = QueryWaitToPickOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/queryorderdetail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询订单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/queryorderdetail", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<OrderVO> queryOrderDetail(@Valid @RequestBody QueryOrderDetailRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
//        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
//            return empowerOrderService.queryOrderDetail(request);
//        }
        return orderService.queryOrderDetail(request);
    }


    @MethodDoc(
            displayName = "分页查询待配送订单列表",
            description = "分页查询待配送订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询待配送订单列表请求",
                            type = QueryWaitToDeliveryOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/querywaitdelivery",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询待配送订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querywaitdelivery", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<OrderListResponse> queryWaitDeliveryOrder(@Valid @RequestBody QueryWaitToDeliveryOrderRequest request) {
        return orderService.queryWaitToDeliveryOrder(request,RequestContextUtils.getHeaderStoreId());
    }

    @MethodDoc(
            displayName = "分页查询配送异常的订单列表",
            description = "分页查询配送异常的订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询配送异常的订单列表的请求",
                            type = QueryDeliveryErrorOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/querydeliveryerror",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询配送异常的订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querydeliveryerror", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<OrderListResponse> queryDeliveryErrorOrder(@Valid @RequestBody QueryDeliveryErrorOrderRequest request) {
        return orderService.queryDeliveryErrorOrder(request);
    }

    @MethodDoc(
            displayName = "分页查询退款待审核列表",
            description = "分页查询退款待审核列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询待配送订单列表请求",
                            type = QueryWaitToDeliveryOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/querywaitauditrefund",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询退款待审核列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querywaitauditrefund", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<RefundApplyListResponse> queryWaitAuditRefund(@Valid @RequestBody QueryWaitAuditRefundRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        return orderService.queryWaitAuditRefund(identityInfo.getUser().getTenantId(), identityInfo.getStoreIdList(), request.getPage(), request.getSize(), request.getEntityType());
    }

    @MethodDoc(
            displayName = "分页查询待自提订单列表",
            description = "分页查询待自提订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询待自提订单列表请求",
                            type = QueryWaitToSelfFetchOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/querywaitselffetch",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询待自提订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querywaitselffetch", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @ValidHeaderParam
    public CommonResponse<OrderListResponse> queryWaitSelfFetchOrder(@Valid @RequestBody QueryWaitToSelfFetchOrderRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
            return empowerOrderService.queryWaitToSelfFetchOrder(identityInfo.getUser().getTenantId(), request.getPage(), request.getSize(), request.getEntityType());
        }
        return orderService.queryWaitToSelfFetchOrder(request.getPage(), request.getSize(), request.getEntityType());
    }

    @MethodDoc(
            displayName = "分页查询全部订单列表",
            description = "分页查询全部订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "全部订单列表分页查询请求",
                            type = OrderListRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/orderList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询全部订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/orderList", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<OrderListResponse> orderList(@Valid @RequestBody OrderListRequest request) {
        // 进行参数校验
        String validateResult = request.validate();
        if (validateResult != null) {
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), validateResult);
        }

        // 按是否多门店模式分流
        if (ApiMethodParamThreadLocal.getIdentityInfo().isFullStoreMode() && orderService.isTenantSupportMultiStoreMode()) {
            return orderService.orderList4MultiStore(request);
        }

        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
            return empowerOrderService.orderList(request);
        }
        return orderService.orderList(request, RequestContextUtils.getHeaderStoreId());
    }

    @MethodDoc(
            displayName = "分页查询全部订单列表",
            description = "分页查询全部订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "全部订单列表分页查询请求",
                            type = OrderListRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/orderList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询全部订单列表,app本地缓存专用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/orderList/appcache", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<OrderListResponse> orderListForAppLocalCache(@Valid @RequestBody OrderListForAppLocalCacheRequest request) {
        // 进行参数校验
        String validateResult = request.validate();
        if (validateResult != null) {
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), validateResult);
        }
        if (Objects.isNull(request.getPageNo())){
            request.setPageNo(1);
        }
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
            return empowerOrderService.orderListForAppLocalCache(request);
        }
        return orderService.orderListForAppLocalCache(request,RequestContextUtils.getHeaderStoreId());
    }

    @Auth
    @ApiOperation(value = "分页查询全部订单列表,app本地缓存专用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/changed/orderList/appcache", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<OrderListResponse> changedOrderListForAppLocalCache(@Valid @RequestBody ChangedOrderListForAppLocalCacheRequest request) {
        // 进行参数校验
        String validateResult = request.validate();
        if (Objects.isNull(request.getPageNo())){
            request.setPageNo(1);
        }
        if (validateResult != null) {
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), validateResult);
        }
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
            return empowerOrderService.changedOrderListForAppLocalCache(request);
        }
        return orderService.changedOrderListForAppLocalCache(request, RequestContextUtils.getHeaderStoreId());
    }

    @MethodDoc(
            displayName = "orderDeliveryStatus",
            description = "查询订单配送状态",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询订单配送状态请求",
                            type = OrderDeliveryDetailRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/orderDeliveryStatus",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/orderDeliveryStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<DeliveryStatusVO> orderDeliveryStatus(@Valid @RequestBody OrderDeliveryDetailRequest request) {
        String validateResult = request.validate();
        if (validateResult != null) {
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), validateResult);
        }
        return orderService.queryDeliveryDetail(request);
    }

    @MethodDoc(
            displayName = "确认接单",
            description = "确认接单",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "确认接单请求",
                            type = ConfirmOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/confirmorder",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "确认接单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/confirmorder", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse confirmOrder(@Valid @RequestBody ConfirmOrderRequest request) {
        return orderService.confirmOrder(request);
    }

    @MethodDoc(
            displayName = "订单拣货完成",
            description = "订单拣货完成",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "订单拣货完成请求",
                            type = OrderPickCompleteRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/pickcomplete",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "订单拣货完成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/pickcomplete", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse pickComplete(@Valid @RequestBody OrderPickCompleteRequest request) {
        return orderService.pickComplete(request);
    }


    @MethodDoc(
            displayName = "退款审核",
            description = "退款审核",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "退款审核请求",
                            type = OrderPickCompleteRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/refundaudit",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "退款审核")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/refundaudit", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<RefundApplyAuditResponse> refundAudit(@Valid @RequestBody RefundApplyAuditRequest request) {
        CommonResponse auditResponse = orderService.refundAudit(request);
        String saleReturnOrderNo = null;
        boolean canCreateSaleReturnOrder = false;
        if (auditResponse.getCode() == ResultCodeEnum.SUCCESS.getCode()) {
            canCreateSaleReturnOrder = saleReturnService.checkCanCreateSaleReturnOrder(request);
            // 退货退款终审阶段，查询销退单号
            if (canCreateSaleReturnOrder
                    && request.getAuditStage() != null
                    && request.getAuditStage() == AuditStageEnum.FINAL.getValue()) {
                saleReturnOrderNo = saleReturnService.getSaleReturnOrderNo(request);
            }
            // 退货退款终审阶段，驳回情况下，如果有销退单，关闭销退单
            if(StringUtils.isNotBlank(saleReturnOrderNo)
                    && request.getAuditStage() == AuditStageEnum.FINAL.getValue()
                    && request.getAuditResult() == AuditResultEnum.REJECT.getValue()){
                saleReturnService.closeSaleReturnOrder(request, saleReturnOrderNo);
            }
        } else {
            return CommonResponse.fail(auditResponse.getCode(),auditResponse.getMessage());
        }
        return CommonResponse.success(new RefundApplyAuditResponse(canCreateSaleReturnOrder, saleReturnOrderNo));
    }

    @MethodDoc(
            displayName = "打印小票",
            description = "打印小票，打印指定渠道订单小票",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "打印小票",
                            type = PrintReceiptRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/printreceipt",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "打印小票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/printreceipt", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<PrintReceiptResponse> printReceipt(@Valid @RequestBody PrintReceiptRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return orderService.printReceipt(user.getTenantId(), user.getEmployeeId(), request);
    }

    @MethodDoc(
            displayName = "查询打印状态",
            description = "查询打印状态，根据printId查询对应打印状态。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询打印状态",
                            type = QueryPrintStatusRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/queryprintstatus",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询打印状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/queryprintstatus", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<QueryPrintStatusResponse> queryPrintStatus(@Valid @RequestBody QueryPrintStatusRequest request) {
        return orderService.queryPrintStatus(request);
    }

    @MethodDoc(
            description = "查询虚拟电话",
            displayName = "查询虚拟电话",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询虚拟电话",
                            type = QueryVirtualPhoneRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\\n\" +\n" +
                    "                    \"    \\\"code\\\": 0,\\n\" +\n" +
                    "                    \"    \\\"message\\\": \\\"操作成功\\\",\\n\" +\n" +
                    "                    \"    \\\"data\\\": {\\n\" +\n" +
                    "                    \"        \\\"phoneNo\\\": \\\"13107111899,1425\\\"\\n\" +\n" +
                    "                    \"    }\\n\" +\n" +
                    "                    \"}",
            restExampleUrl = "/pieapi/order/queryvirtualphone",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询虚拟电话")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/queryvirtualphone", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<QueryVirtualPhoneResponse> queryVirtualPhone(@Valid @RequestBody QueryVirtualPhoneRequest request) {
        return orderService.queryVirtualPhone(request);
    }

    @MethodDoc(
            displayName = "部分退款信息查询",
            description = "部分退款信息查询，部分退款页面查询可退数量，商品项金额，预计退款总和",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "检查退款",
                            type = PartRefundCheckRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": -1,\n" +
                    "    \"message\": \"已取消或锁定中的订单不能发起订单取消\",\n" +
                    "    \"data\": {\n" +
                    "        \"refundReasons\": [],\n" +
                    "        \"orderInfo\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/order/partrefundcheck",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/partrefundcheck", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<PartRefundCheckResponse> partRefundCheck(@Valid @RequestBody PartRefundCheckRequest partRefundCheckRequest) {
        return orderService.partRefundCheck(partRefundCheckRequest);
    }


    @MethodDoc(
            displayName = "克重退款信息查询",
            description = "克重退款信息查询，克重退款页面查询可退数量，渠道重量",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "检查退款",
                            type = PartRefundCheckRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": -1,\n" +
                    "    \"message\": \"已取消或锁定中的订单不能发起订单取消\",\n" +
                    "    \"data\": {\n" +
                    "        \"refundReasons\": [],\n" +
                    "        \"orderInfo\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/order/weightrefundcheck",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/weightrefundcheck", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<WeightRefundCheckResponse> weightRefundCheck(@Valid @RequestBody WeightRefundCheckRequest weightRefundCheckRequest) {
        return orderService.weightRefundCheck(weightRefundCheckRequest);
    }

    @MethodDoc(
            displayName = "部分退款",
            description = "部分退款，针对指定渠道订单的指定商品列表进行部分退款。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "部分退款",
                            type = PartRefundRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/partrefund",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/partrefund", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse partRefund(@Valid @RequestBody PartRefundRequest request) {
        return orderService.partRefund(request);
    }

    @MethodDoc(
            displayName = "售后退款",
            description = "售后退款，针对指定渠道订单的指定商品列表进行部分退款。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "售后退款",
                            type = PartRefundRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/afterSaleRefund",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/afterSaleRefund", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse afterSaleRefund(@Valid @RequestBody PartRefundRequest request) {
        return orderService.afterSaleRefund(request);
    }

    @MethodDoc(
            displayName = "退差试算",
            description = "退差试算，针对指定渠道订单的指定商品列表进行退差试算。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "退差试算",
                            type = RefundByWeightRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/weightrefundcalculate",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/weightrefundcalculate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<WeightRefundCalculateResult> weightRefundCalculate(@Valid @RequestBody RefundByWeightRequest request) {
        return orderService.weightRefundCalculate(request);
    }


    @MethodDoc(
            displayName = "取消订单",
            description = "取消订单，即完成指定渠道订单整单退款。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "取消订单",
                            type = CancelOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/cancelorder",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/cancelorder", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse cancelOrder(@Valid @RequestBody CancelOrderRequest request) throws TException {
        return orderService.cancelOrder(request);
    }


    @MethodDoc(
            displayName = "检查退款",
            description = "检查退款，同时会将退款理由列表返回",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "检查退款",
                            type = CheckRefundRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": -1,\n" +
                    "    \"message\": \"已取消或锁定中的订单不能发起订单取消\",\n" +
                    "    \"data\": {\n" +
                    "        \"refundReasons\": [],\n" +
                    "        \"orderInfo\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/order/checkrefund",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/checkrefund", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<CheckRefundResponse> checkRefund(@Valid @RequestBody CheckRefundRequest checkRefundRequest) {
        return orderService.checkRefund(checkRefundRequest);
    }

    @MethodDoc(
            description = "查询用户最近订单",
            displayName = "查询用户最近订单",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询用户最近订单",
                            type = QueryCustomerLatestOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\\n\" +\n" +
                    "                    \"    \\\"code\\\": 0,\\n\" +\n" +
                    "                    \"    \\\"message\\\": \\\"操作成功\\\",\\n\" +\n" +
                    "                    \"    \\\"data\\\": {}\\n\" +\n" +
                    "                    \"}",
            restExampleUrl = "/pieapi/order/queryLatestOrder",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询用户最近订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryLatestOrder", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<CustomerLatestOrderResponse> queryLatestOrder(@Valid @RequestBody QueryCustomerLatestOrderRequest request) {
        return orderService.queryLatestOrder(request);
    }

    @MethodDoc(
            description = "查询用户最近一笔订单（新）",
            displayName = "查询用户最近一笔订单（新）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询用户最近一笔订单（新）",
                            type = QueryUserLatestOrderReq.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\\n\" +\n" +
                    "                    \"    \\\"code\\\": 0,\\n\" +\n" +
                    "                    \"    \\\"message\\\": \\\"操作成功\\\",\\n\" +\n" +
                    "                    \"    \\\"data\\\": {}\\n\" +\n" +
                    "                    \"}",
            restExampleUrl = "/pieapi/order/getUserLatestOrder",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询用户最近一笔订单（新）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryUserLatestOrder", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<CustomerLatestOrderResponse> queryUserLatestOrder(@Valid @RequestBody QueryUserLatestOrderReq request) {
        return orderService.queryUserLatestOrder(request);
    }


    @MethodDoc(
            description = "发起按克重退差价",
            displayName = "发起按克重退差价",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "发起按克重退差价",
                            type = RefundByWeightRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\\n\" +\n" +
                    "                    \"    \\\"code\\\": 0,\\n\" +\n" +
                    "                    \"    \\\"message\\\": \\\"操作成功\\\",\\n\" +\n" +
                    "                    \"    \\\"data\\\": {}\\n\" +\n" +
                    "                    \"}",
            restExampleUrl = "/pieapi/order/weightrefund",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "发起按克重退差价")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/weightrefund", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse weightRefund(@Valid @RequestBody RefundByWeightRequest request) {
        return orderService.weightRefund(request);
    }

    @MethodDoc(
            displayName = "查询退差价联动摊位营收配置",
            description = "查询退差价联动摊位营收配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询退差价联动摊位营收配置",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    ),
                    @ParamDoc(name = "poiId", description = "门店Id ", paramType = ParamType.REQUEST_PARAM),
            },
            restExampleResponseData = "",
            restExamplePostData = "poiId=100011",
            restExampleUrl = "/pieapi/order/weightrefundconfig/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询退差价联动摊位营收配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "poiId", type = ParamDataType.ALL_POI, source = ParamSource.QUERY_PARAM)
    })
    @RequestMapping(value = "/weightrefundconfig/query", method = {RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WeightRefundConfigResponse> queryWeightRefundConfig(@RequestParam(name = "poiId") Long poiId) {

        return orderService.queryWeightRefundConfig(poiId);
    }


    @MethodDoc(
            displayName = "更新退差价联动摊位营收配置",
            description = "更新退差价联动摊位营收配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "更新退差价联动摊位营收配置",
                            type = WeightRefundUpdateConfigRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/weightrefundconfig/save",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "更新退差价联动摊位营收配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "poiId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/weightrefundconfig/save", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse updateWeightRefundConfig(@RequestBody WeightRefundUpdateConfigRequest request) {

        return orderService.updateWeightRefundConfig(request);
    }

    @MethodDoc(
            displayName = "分页查询订单列表",
            description = "分页查询订单列表，分页查询订单概要信息，包含对应商品列表。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询订单列表请求",
                            type = OrderListRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": {\n" +
                    "        \"pageInfo\": {\n" +
                    "            \"page\": 1,\n" +
                    "            \"size\": 2,\n" +
                    "            \"totalPage\": 20,\n" +
                    "            \"totalSize\": 39\n" +
                    "        },\n" +
                    "        \"orderList\": [\n" +
                    "            {\n" +
                    "                \"tenantId\": \"1000011\",\n" +
                    "                \"channelId\": 300,\n" +
                    "                \"channelName\": \"京东到家\",\n" +
                    "                \"storeId\": \"1000017\",\n" +
                    "                \"storeName\": \"中台测试门店(共用饿了么京东渠道)\",\n" +
                    "                \"channelOrderId\": \"916923738000441\",\n" +
                    "                \"serialNo\": \"0\",\n" +
                    "                \"itemCount\": 7,\n" +
                    "                \"actualPayAmt\": 3810,\n" +
                    "                \"bizReceiveAmt\": 3091,\n" +
                    "                \"deliveryMethod\": 1,\n" +
                    "                \"deliveryMethodDesc\": \"达达专送\",\n" +
                    "                \"deliveryUserName\": null,\n" +
                    "                \"deliveryUserPhone\": null,\n" +
                    "                \"receiverName\": \"闻雄\",\n" +
                    "                \"receiverPhone\": \"13107111899,1425\",\n" +
                    "                \"receiveAddress\": \"长沙市岳麓区通程商业广场(麓山店)A座测试1号\",\n" +
                    "                \"channelOrderStatus\": 50,\n" +
                    "                \"channelOrderStatusDesc\": \"订单已取消\",\n" +
                    "                \"createTime\": \"1563195738000\",\n" +
                    "                \"refundAmt\": 0,\n" +
                    "                \"refundTagId\": 100,\n" +
                    "                \"refundTagDesc\": \"无\",\n" +
                    "                \"auditingRefundTagId\": 0,\n" +
                    "                \"auditingRefundTagDesc\": null,\n" +
                    "                \"couldOperateItemList\": [],\n" +
                    "                \"deliveryOrderType\": 0,\n" +
                    "                \"deliveryOrderTypeName\": \"即时订单\",\n" +
                    "                \"estimatedSendArriveTimeStart\": \"1563268500000\",\n" +
                    "                \"estimatedSendArriveTimeEnd\": \"1563268500000\",\n" +
                    "                \"pickupStatus\": 1,\n" +
                    "                \"pickupCompleteTime\": \"1563195817095\",\n" +
                    "                \"distributeStatus\": 1,\n" +
                    "                \"offlineOrderStatus\": 25,\n" +
                    "                \"updateTime\": \"1563246618000\",\n" +
                    "                \"channelExtraOrderId\": null,\n" +
                    "                \"comments\": null,\n" +
                    "                \"productList\": [\n" +
                    "                    {\n" +
                    "                        \"skuId\": \"1145223620684083287\",\n" +
                    "                        \"upcCode\": \"\",\n" +
                    "                        \"skuName\": \"快捷优化00006 无\",\n" +
                    "                        \"picUrl\": null,\n" +
                    "                        \"specification\": \"500g/份\",\n" +
                    "                        \"sellUnit\": \"1\",\n" +
                    "                        \"originalTotalPrice\": null,\n" +
                    "                        \"totalPayAmount\": null,\n" +
                    "                        \"totalDiscountAmount\": null,\n" +
                    "                        \"unitPrice\": 480,\n" +
                    "                        \"count\": 7,\n" +
                    "                        \"isRefund\": 1,\n" +
                    "                        \"refundCount\": 7,\n" +
                    "                        \"boothName\": null,\n" +
                    "                        \"offlinePrice\": 750,\n" +
                    "                        \"stallSettleAmt\": 5250,\n" +
                    "                        \"realQuantity\": null,\n" +
                    "                        \"customerSkuId\": \"1145223620684083287\"\n" +
                    "                    }\n" +
                    "                ]\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"tenantId\": \"1000011\",\n" +
                    "                \"channelId\": 300,\n" +
                    "                \"channelName\": \"京东到家\",\n" +
                    "                \"storeId\": \"1000017\",\n" +
                    "                \"storeName\": \"中台测试门店(共用饿了么京东渠道)\",\n" +
                    "                \"channelOrderId\": \"916921041000242\",\n" +
                    "                \"serialNo\": \"0\",\n" +
                    "                \"itemCount\": 7,\n" +
                    "                \"actualPayAmt\": 3810,\n" +
                    "                \"bizReceiveAmt\": 3091,\n" +
                    "                \"deliveryMethod\": 1,\n" +
                    "                \"deliveryMethodDesc\": \"达达专送\",\n" +
                    "                \"deliveryUserName\": null,\n" +
                    "                \"deliveryUserPhone\": null,\n" +
                    "                \"receiverName\": \"闻雄\",\n" +
                    "                \"receiverPhone\": \"13107216795,1232\",\n" +
                    "                \"receiveAddress\": \"长沙市岳麓区通程商业广场(麓山店)A座测试1号\",\n" +
                    "                \"channelOrderStatus\": 50,\n" +
                    "                \"channelOrderStatusDesc\": \"订单已取消\",\n" +
                    "                \"createTime\": \"1563193202000\",\n" +
                    "                \"refundAmt\": 0,\n" +
                    "                \"refundTagId\": 200,\n" +
                    "                \"refundTagDesc\": \"全部\",\n" +
                    "                \"auditingRefundTagId\": 0,\n" +
                    "                \"auditingRefundTagDesc\": null,\n" +
                    "                \"couldOperateItemList\": [],\n" +
                    "                \"deliveryOrderType\": 0,\n" +
                    "                \"deliveryOrderTypeName\": \"即时订单\",\n" +
                    "                \"estimatedSendArriveTimeStart\": \"1563277500000\",\n" +
                    "                \"estimatedSendArriveTimeEnd\": \"1563277500000\",\n" +
                    "                \"pickupStatus\": 48,\n" +
                    "                \"pickupCompleteTime\": \"1563278626916\",\n" +
                    "                \"distributeStatus\": 48,\n" +
                    "                \"offlineOrderStatus\": 25,\n" +
                    "                \"updateTime\": \"1563332845000\",\n" +
                    "                \"channelExtraOrderId\": null,\n" +
                    "                \"comments\": null,\n" +
                    "                \"productList\": [\n" +
                    "                    {\n" +
                    "                        \"skuId\": \"1145223620684083287\",\n" +
                    "                        \"upcCode\": \"\",\n" +
                    "                        \"skuName\": \"快捷优化00006 无\",\n" +
                    "                        \"picUrl\": null,\n" +
                    "                        \"specification\": \"500g/份\",\n" +
                    "                        \"sellUnit\": \"1\",\n" +
                    "                        \"originalTotalPrice\": null,\n" +
                    "                        \"totalPayAmount\": null,\n" +
                    "                        \"totalDiscountAmount\": null,\n" +
                    "                        \"unitPrice\": 480,\n" +
                    "                        \"count\": 7,\n" +
                    "                        \"isRefund\": 1,\n" +
                    "                        \"refundCount\": 7,\n" +
                    "                        \"boothName\": null,\n" +
                    "                        \"offlinePrice\": 750,\n" +
                    "                        \"stallSettleAmt\": 5250,\n" +
                    "                        \"realQuantity\": null,\n" +
                    "                        \"customerSkuId\": \"1145223620684083287\"\n" +
                    "                    }\n" +
                    "                ]\n" +
                    "            }\n" +
                    "        ]\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/order/search",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @ApiOperation(value = "订单搜索")
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/search", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<OrderListResponse> orderSearch(@Valid @RequestBody OrderSearchRequest request) {
        // 按是否多门店模式分流
        if (ApiMethodParamThreadLocal.getIdentityInfo().isFullStoreMode() && orderService.isTenantSupportMultiStoreMode()) {
            return orderService.orderList4MultiStore(request.build());
        }
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
            return empowerOrderService.orderList(request.build());
        }
        return orderService.orderList(request.build(), RequestContextUtils.getHeaderStoreId());
    }

    @MethodDoc(
            displayName = "查询拣货员列表",
            description = "查询拣货员列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询拣货员列表",
                            type = QueryShopPickerRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"\",\n" +
                    "  \"data\": {\n" +
                    "      \"accountInfoList\":[\n" +
                    "            {\n" +
                    "                \"accountId\":134,\n" +
                    "                \"accountName\":\"14124\",\n" +
                    "                \"account\":\"141\",\n" +
                    "                \"pickedCount\":123,\n" +
                    "                \"waitPickCount\":1243,\n" +
                    "                 \"workStatus\":0\n" +
                    "            }\n" +
                    "      \n" +
                    "      ]\n" +
                    "  }\n" +
                    " }",
            restExampleUrl = "/pieapi/order/queryShopPicker",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @ApiOperation(value = "查询拣货员列表")
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/queryShopPicker", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<QueryShopPickerResponse> queryShopPicker(@Valid @RequestBody QueryShopPickerRequest request) {
        request.selfCheck();
        return orderService.queryShopPicker(request);
    }

    @MethodDoc(
            displayName = "任务分派",
            description = "任务分派",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "任务分派",
                            type = TransferOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/transfer",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @ApiOperation(value = "任务分派(直接下发传递accountId 0,accountName system)")
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/transfer", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<String> transferPicker(@Valid @RequestBody TransferOrderRequest request) {
        request.selfCheck();
        return orderService.transferPicker(request);
    }

    @MethodDoc(
            displayName = "根据子类型分页查询待配送订单列表",
            description = "根据子类型分页查询待配送订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "根据子类型分页查询待配送订单列表请求",
                            type = QueryWaitToDeliveryOrderBySubTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/querywaitdeliverybysubtype",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "根据子类型分页查询待配送订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querywaitdeliverybysubtype", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @ValidHeaderParam
    public CommonResponse<OrderListResponse> queryWaitDeliveryBySubType(@Valid @RequestBody QueryWaitToDeliveryOrderBySubTypeRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
            return empowerOrderService.queryWaitToDeliveryOrderBySubType(request);
        }
        return orderService.queryWaitToDeliveryOrderBySubType(request);
    }

    @MethodDoc(
            displayName = "待配送订单子类型数量查询",
            description = "待配送订单子类型数量查询",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "待配送订单子类型数量查询",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/querywaitdeliverysubtypecount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "待配送订单子类型数量查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querywaitdeliverysubtypecount", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @ValidHeaderParam
    public CommonResponse<WaitToDeliverySubTypeCountResponse> queryWaitDeliverySubTypeCount(@RequestBody QueryWaitDeliverySubTypeCountRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
            return empowerOrderService.queryWaitDeliverySubTypeCount(request);
        }
        return orderService.queryWaitDeliverySubTypeCount(request);
    }

    @MethodDoc(
            displayName = "根据子类型分页查询异常订单列表",
            description = "根据子类型分页查询异常订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "根据子类型分页查询异常订单列表请求",
                            type = QueryDeliveryErrorOrderBySubTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/querydeliveryerrorbysubtype",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "根据子类型分页查询异常订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querydeliveryerrorbysubtype", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<OrderListResponse> queryDeliveryErrorBySubType(@Valid @RequestBody QueryDeliveryErrorOrderBySubTypeRequest request) {
        if (ApiMethodParamThreadLocal.getIdentityInfo().isFullStoreMode() && orderService.isTenantSupportMultiStoreMode()) {
            return orderService.queryDeliveryErrorBySubType4MultiStore(request);
        }
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
            return empowerOrderService.queryDeliveryErrorBySubType(request);
        }
        return orderService.queryDeliveryErrorBySubType(request, RequestContextUtils.getHeaderStoreId());
    }

    @MethodDoc(
            displayName = "异常订单子类型数量查询",
            description = "异常订单子类型数量查询",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "异常订单子类型数量查询",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/querydeliveryerrorsubtypecount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "异常订单子类型数量查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querydeliveryerrorsubtypecount", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<DeliveryErrorSubTypeCountResponse> queryDeliveryErrorSubTypeCount() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
            return empowerOrderService.queryDeliveryErrorSubTypeCount();
        }
        return orderService.queryDeliveryErrorSubTypeCount();
    }

    @MethodDoc(
            displayName = "根据子类型分页查询售后订单列表",
            description = "根据子类型分页查询售后订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "根据子类型分页查询售后订单列表请求",
                            type = QueryWaitToDeliveryOrderBySubTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/querywaitauditrefundgoodsbysubtype",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "根据子类型分页查询售后订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querywaitauditrefundgoodsbysubtype", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @ValidHeaderParam
    public CommonResponse<RefundApplyListResponse> queryWaitAuditRefundGoodsBySubtype(@Valid @RequestBody QueryWaitAuditRefundGoodsBySubTypeRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
            return empowerOrderService.queryWaitToAuditRefundGoodsOrderBySubType(identityInfo.getUser().getTenantId(), identityInfo.getStoreIdList(), request.getPage(), request.getSize(), request.getEntityType(), request.getSubType());
        }
        return orderService.queryWaitToAuditRefundGoodsOrderBySubType(identityInfo.getUser().getTenantId(), identityInfo.getStoreIdList(), request.getPage(), request.getSize(), request.getEntityType(), request.getSubType());
    }

    @MethodDoc(
            displayName = "售后订单子类型数量查询",
            description = "售后订单子类型数量查询",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "售后订单子类型数量查询",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/querywaitauditrefundgoodsbysubtypecount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "售后订单子类型数量查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querywaitauditrefundgoodsbysubtypecount", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @ValidHeaderParam
    public CommonResponse<RefundGoodsSubTypeCountResponse> queryWaitAuditRefundGoodsBySubtypeCount(@RequestBody QueryWaitAuditRefundGoodsSubTypeCountRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (empowerOrderService.queryFromNewOrderService(identityInfo)){
            return empowerOrderService.queryWaitAuditRefundGoodsBySubtypeCount(identityInfo.getUser().getTenantId(), identityInfo.getStoreIdList(), request.getEntityType());
        }
        return orderService.queryWaitAuditRefundGoodsBySubtypeCount(identityInfo.getUser().getTenantId(), identityInfo.getStoreIdList(), request.getEntityType());
    }

    @MethodDoc(
            displayName = "自提码校验接口",
            description = "自提码校验接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自提码校验接口",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/checkselffetchcode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "自提码校验接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/checkselffetchcode", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<CheckSelfDeliveryCodeResponse> checkSelfFetchCode(@Valid @RequestBody CheckSelfFetchCodeRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        return orderService.queryOrderBySelfFetchCode(identityInfo.getUser().getTenantId(), request);
    }

    @MethodDoc(
            displayName = "自提码核验接口",
            description = "自提码核验接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自提码核验接口",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/verifyselffetchcode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "自提码核验接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/verifyselffetchcode", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse verifySelfFetchCode(@Valid @RequestBody VerifySelfFetchCodeRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        return orderService.verifySelfFetchCode(identityInfo.getUser().getTenantId(), request);
    }

    @MethodDoc(
            displayName = "查询金额退可退差商品列表接口",
            description = "查询金额退可退差商品列表接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "金额退页面检查接口"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/orderfuse/moneyRefundCheck",
            restExamplePostData = "{\"channelId\":\"Integer\",\"tenantId\":\"Long\",\"channelOrderId\":\"String\"}",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"string\"}"
    )
    @Auth
    @ApiOperation(value = "查询金额退可退差商品列表接口")
    @MethodLog(logResponse = true, logRequest = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/moneyRefundCheck", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<OrderMoneyRefundCheckResponse> moneyRefundCheck(@Valid @RequestBody OrderMoneyRefundCheckRequest request) {
        try {
            if (request == null) {
                throw new ParamInvalidException("调用OrderController.moneyRefundCheck,OrderMoneyRefundCheckRequest为null");
            }
            if (Objects.equals(request.getChannelId(), DynamicChannelType.MEITUAN.getChannelId())) {
                // 查询订单详情
                OrderDetail orderDetail = ocmsOrderRemoteService.getOrderDetail(request.getChannelOrderId(),
                        request.getChannelId(), ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
                if (Objects.isNull(orderDetail) || Objects.isNull(orderDetail.getOrderBase())) {
                    return CommonResponse.fail(StatusCodeEnum.ORDER_NOT_FOUNT.getCode(),
                            StatusCodeEnum.ORDER_NOT_FOUNT.getMessage());
                }
                if (BooleanUtils.isTrue(orderDetail.getOrderBase().getIsMtFamousTavern())) {
                    OrderMoneyRefundCheckResponse res = new OrderMoneyRefundCheckResponse();
                    res.setShouldGetGatherPhone(true);
                    return CommonResponse.success(res);
                }
            }

            OrderMoneyRefundCheckResponse res = new OrderMoneyRefundCheckResponse();
            List<OrderItemMoneyRefundCheckVO> moneyRefundCheckVOList = orderService.moneyRefundCheck(request);
            if (CollectionUtils.isEmpty(moneyRefundCheckVOList)) {
                return CommonResponse.fail(StatusCodeEnum.FAIL.getCode(), "当前无可金额退的商品，无法发起金额退");
            }
            if (Objects.equals(request.getChannelId(), DynamicChannelType.TAO_XIAN_DA.getChannelId())) {
                List<RefundReasonAndCodeVO> refundReasons = orderService.queryRefundReasonAndCode(RefundReasonAndCodeRequest.builder()
                        .orderBizType(DynamicOrderBizType.channelId2OrderBizTypeValue(request.getChannelId()))
                        .viewOrderId(request.getChannelOrderId())
                        .refundType(RefundTypeEnum.AMOUNT.getValue())
                        .build());
                res.setRefundReasons(refundReasons);
            }
            res.setMoneyRefundCheckVOList(moneyRefundCheckVOList);
            return CommonResponse.success(res);
        }  catch (BizException e) {
            return CommonResponse.fail(StatusCodeEnum.FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("moneyRefundCheck failed, request:{}", JSONObject.toJSONString(request), e);
            return CommonResponse.fail(StatusCodeEnum.FAIL.getCode(), "金额退页面检查失败了！原因：" + e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "商家金额退接口",
            description = "商家金额退接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "商家金额退接口"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/orderfuse/moneyRefund",
            restExamplePostData = "{\"channelId\":\"Integer\",\"tenantId\":\"Long\",\"channelOrderId\":\"String\"}",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"string\"}"
    )
    @Auth
    @ApiOperation(value = "商家金额退接口")
    @MethodLog(logResponse = true, logRequest = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/moneyRefund", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse moneyRefund(@Valid @RequestBody OrderMoneyRefundRequest request) {
        try {
            if (request == null) {
                throw new ParamInvalidException("调用OrderFuseController.moneyRefund,OrderMoneyRefundRequest为null");
            }
            return orderService.moneyRefund(request);
        } catch (Exception e) {
            log.error("moneyRefund failed, request:{}", JSONObject.toJSONString(request), e);
            return CommonResponse.fail(StatusCodeEnum.FAIL.getCode(), "金额退失败！原因：" + e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "开发票",
            description = "开发票",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "商家开发票接口"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/pieapi/order/createInvoice",
            restExamplePostData = "{\"channelId\":\"Integer\",\"tenantId\":\"Long\",\"channelOrderId\":\"String\"}",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"string\"}"
    )
    @Auth
    @ApiOperation(value = "开发票接口")
    @MethodLog(logResponse = true, logRequest = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/createInvoice", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<CreateQnhInvoiceResponse> createInvoice(@Valid @RequestBody CreateQnhInvoiceRequest request) {
        return orderService.createInvoice(request);
    }




    @MethodDoc(
            displayName = "查询订单MCC配置接口",
            description = "查询订单MCC配置接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询订单MCC配置接口",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/queryOrderMccConfig",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询订单MCC配置接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/queryOrderMccConfig", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<QueryMccConfigResponse> queryOrderMccConfig(@RequestBody QueryMccConfigRequest request) {
        try {
            QueryMccConfigResponse resp = orderService.queryMccConfig(request);
            return CommonResponse.success(resp);
        } catch (Exception e) {
            log.error("获取订单mcc配置失败：{}", e.getMessage(), e);
            return CommonResponse.fail(StatusCodeEnum.FAIL.getCode(), "获取订单mcc配置失败！原因：" + e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "查询订单可操作列表", description = "查询订单可操作列表",
            parameters = {@ParamDoc(
                    name = "request", description = "查询订单可操作列表",
                    type = GetOrderOperateItemsRequest.class, paramType = ParamType.REQUEST_BODY,
                    rule = "非空", requiredness = Requiredness.REQUIRED
            )},
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/pieapi/order/getOrderOperateItems",
            restExamplePostData = "{\"orderKeys\":[{\"channelOrderId\":\"String\",\"channelType\":\"Integer\"}],\"checkItems\":[\"Integer\"]}",
            restExampleResponseData = "[{\"channelOrderId\":\"String\",\"channelType\":\"Integer\",\"operateItem\":\"Integer\"}]"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @ApiOperation(value = "查询订单可操作列表")
    @RequestMapping(value = "/getOrderOperateItems", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<List<GetOrderOperateItemsResponse>> getOrderOperateItems(@RequestBody GetOrderOperateItemsRequest request) {
        try {
            if (request == null) {
                throw new ParamInvalidException("调用OrderController.getOrderOperateItems,GetOrderOperateItemsRequest为null");
            }
            request.selfCheck();

            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            List<GetOrderOperateItemsResponse> responseData = orderService.getOrderOperateItems(identityInfo.getUser().getTenantId(),request);
            return CommonResponse.success(responseData);
        } catch (Exception e) {
            log.error("getOrderItemStatisticsData failed, request:{}", JSONObject.toJSONString(request), e);
            return CommonResponse.fail(StatusCodeEnum.FAIL.getCode(), "查询订单可操作列表失败！原因：" + e.getMessage());
        }
    }


    /**
     * 返货审核
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "查询订单可操作列表", description = "查询订单可操作列表",
            parameters = {@ParamDoc(
                    name = "request", description = "查询订单可操作列表",
                    type = GetOrderOperateItemsRequest.class, paramType = ParamType.REQUEST_BODY,
                    rule = "非空", requiredness = Requiredness.REQUIRED
            )},
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/pieapi/order/getOrderOperateItems",
            restExamplePostData = "{\"orderKeys\":[{\"channelOrderId\":\"String\",\"channelType\":\"Integer\"}],\"checkItems\":[\"Integer\"]}",
            restExampleResponseData = "[{\"channelOrderId\":\"String\",\"channelType\":\"Integer\",\"operateItem\":\"Integer\"}]"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @ApiOperation(value = "录入换货售后信息")
    @RequestMapping(value = "/importrefundexchangeinfo", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse<Void> importRefundExchangeInfo(@Valid @RequestBody ImportRefundExchangeInfoRequest request) {
        request.selfCheck();
        request.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        return empowerOrderService.importRefundExchangeInfo(request);
    }

    /**
     * 售后换货信息校验
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "售后换货信息校验", description = "售后换货信息校验",
            parameters = {@ParamDoc(
                    name = "request", description = "查询订单可操作列表",
                    type = GetOrderOperateItemsRequest.class, paramType = ParamType.REQUEST_BODY,
                    rule = "非空", requiredness = Requiredness.REQUIRED
            )},
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/pieapi/order/getOrderOperateItems",
            restExamplePostData = "{\"orderKeys\":[{\"channelOrderId\":\"String\",\"channelType\":\"Integer\"}],\"checkItems\":[\"Integer\"]}",
            restExampleResponseData = "[{\"channelOrderId\":\"String\",\"channelType\":\"Integer\",\"operateItem\":\"Integer\"}]"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @ApiOperation(value = "售后换货信息校验")
    @RequestMapping(value = "/checkRefundExchangeInfo", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<ExchangeRefundImportCheckResponse> checkRefundExchangeInfo(@Valid @RequestBody ExchangeRefundImportCheckReq request) {
        request.selfCheck();
        request.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        return empowerOrderService.checkRefundExchangeInfo(request);
    }

    @MethodDoc(
            displayName = "批量查询订单列表",
            description = "批量查询订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量查询订单列表",
                            type = OrderListRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/selectbyids",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询全部订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/selectbyids", method = RequestMethod.POST)
    public CommonResponse<OrderListQueryResponse> selectListByOrderIds(@Valid @RequestBody OrderListQueryRequest request) {
        return orderService.selectListByOrderIds(request, RequestContextUtils.getHeaderStoreId());
    }
}
