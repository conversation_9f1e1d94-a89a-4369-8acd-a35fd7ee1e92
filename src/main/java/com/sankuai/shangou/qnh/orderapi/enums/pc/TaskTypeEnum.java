package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.sankuai.meituan.reco.store.management.task.constant.AsyncTaskType;
import com.sankuai.meituan.reco.supplychain.purchase.client.common.PurchaseTaskType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.TaskType;
import com.sankuai.meituan.shangou.empower.price.client.enums.task.PriceTaskTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.ProductTaskTypeEnum;
import com.sankuai.meituan.shangou.empower.settlement.constant.ExportTaskTypeEnum;
import com.sankuai.sgfnqnh.promotion.core.client.constant.PromotionTaskType;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.enums.task.PromotionTaskTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批量任务枚举
 *
 * <AUTHOR>
 * Date: 2018/12/29 11:47
 * Description:
 */
@AllArgsConstructor
@Getter
public enum TaskTypeEnum {

    SKU_CREATE("SKU_CREATE", "根据SKU创建商品", 1001),
    UPC_ADD_V2("UPC_ADD_V2", "按UPC新增", 2011),
    SKU_ADD_V2("SKU_ADD_V2", "按SKU新增", 1015),
    UPC_CREATE("UPC_CREATE", "根据UPC创建商品", 2001),
    SKU_EDIT("SKU_EDIT", "根据SKU修改商品", 1002),
    STORE_ITEM("STORE_ITEM", "门店商品批量上线", 1004),
    UPC_EDIT("UPC_EDIT", "根据UPC修改商品", 2002),
    PIC_UPDATE("PIC_UPDATE", "批量上传图片", 1003),
    PRICE_IMPORT("PRICE_IMPORT", "价格上传", 3001),
    BRAND_RELATION_IMPORT("BRAND_RELATION_IMPORT", "品牌关联关系导入", 1006),
    QUICK_SKU_ADD("QUICK_SKU_ADD", "商品快捷上线", 1007),
    QUICK_POI_SKU_ADD("QUICK_POI_SKU_ADD", "门店商品快捷上线", 1008),
    QUICK_SKU_ADD_V2("QUICK_SKU_ADD_V2", "商品快捷上线", 1017),
    QUICK_POI_SKU_ADD_V2("QUICK_POI_SKU_ADD_V2", "门店商品快捷上线", 1018),
    //中台v1.8.6 门店级别增加商品属性后，增加QUICK_SKU_ADD_V3 与 QUICK_POI_SKU_ADD_V3
    QUICK_SKU_ADD_V3("QUICK_SKU_ADD_V3", "商品快捷上线", 1019),
    QUICK_POI_SKU_ADD_V3("QUICK_POI_SKU_ADD_V3", "门店商品快捷上线", 1020),
    BATCH_IMPORT_POI_SKU("BATCH_IMPORT_POI_SKU", "批量导入门店商品", 1028),
    BATCH_UPDATE_POI_SKU("BATCH_UPDATE_POI_SKU", "批量修改门店商品", 1029),
    BATCH_IMPORT_POI_SKU_CDQ("BATCH_IMPORT_POI_CDQ", "批量导入门店商品(菜大全)", 1043),
    BATCH_UPDATE_POI_SKU_CDQ("BATCH_UPDATE_POI_SKU_CDQ", "批量修改门店商品(菜大全)", 1044),
    QUICK_POI_SKU_ADD_CDQ("QUICK_POI_SKU_ADD_CDQ", "门店商品快捷上线(菜大全)", 1042),
    BATCH_PUT_ON_SHELF_SKU("BATCH_PUT_ON_SHELF_SKU", "批量上架商品", 1009),
    BATCH_PULL_OFF_SHELF_SKU("BATCH_PULL_OFF_SHELF_SKU", "批量下架商品", 1010),
    POI_COPY("POI_COPY", "门店复制", 1199),
    POI_COPY_FOR_REGION_SKU("POI_COPY_FOR_REGION_SKU", "区域商品复制门店", 1201),
    POI_COPY_STORE_SPU("POI_COPY_STORE_SPU", "复制门店SPU", 1202),
    FRONT_CATEGORY("FRONT_CATEGORY", "批量设置前台分类", 1013),
    ITEM_INIT("ITEM_INIT", "商品初始化", 1014),
    ITEM_INIT_V2("ITEM_INIT_V2", "商品初始化V2", 1198),
    ASYNC_UPDATE_PRICE("ASYNC_UPDATE_PRICE", "异步更新价格", 6001),
    ASYNC_PULL_CHANNEL_POI("ASYNC_PULL_CHANNEL_POI", "异步拉取渠道门店", 6002),
    TENANT_SKU_ADD("TENANT_SKU_ADD", "TENANT_SKU_ADD", 9011),
    TENANT_SKU_ADD_V2("TENANT_SKU_ADD_V2", "TENANT_SKU_ADD_V2", 9012),
    TENANT_SKU_ADD_MAICAI("TENANT_SKU_ADD_MAICAI", "TENANT_SKU_ADD_MAICAI", 9021),
    TENANT_SKU_ADD_MAICAI_V2("TENANT_SKU_ADD_MAICAI_V2", "TENANT_SKU_ADD_MAICAI_V2", 9022),
    TENANT_PRICE("TENANT_PRICE", "TENANT_PRICE", 9031),
    TENANT_POI_SKU_MAICAI("TENANT_POI_SKU_MAICAI", "TENANT_POI_SKU_MAICAI", 9041),
    TENANT_BRAND_ADD("TENANT_BRAND_ADD", "TENANT_BRAND_ADD", 9051),
    TENANT_CATEGORY_ADD("TENANT_CATEGORY_ADD", "TENANT_CATEGORY_ADD", 9061),
    TENANT_PIC_ADD("TENANT_PIC_ADD", "TENANT_PIC_ADD", 9071),
    TENANT_PIC_ADD_BY_NAME("TENANT_PIC_ADD_BY_NAME", "TENANT_PIC_ADD_BY_NAME", 9081),
    TENANT_BOOTH_CATEGORY("TENANT_BOOTH_CATEGORY", "TENANT_BOOTH_CATEGORY", 9091),
    TENANT_BOOTH_SKU("TENANT_BOOTH_SKU", "TENANT_BOOTH_SKU", 9101),
    TENANT_SKU_ONLINE_SKU("TENANT_SKU_ONLINE_SKU", "TENANT_SKU_ONLINE_SKU", 9111),
    TENANT_SKU_ONLINE_UPC("TENANT_SKU_ONLINE_UPC", "TENANT_SKU_ONLINE_UPC", 9121),
    TENANT_SKU_ONLINE_SPEC("TENANT_SKU_ONLINE_SPEC", "TENANT_SKU_ONLINE_SPEC", 9131),
    TENANT_SKU_UPDATE("TENANT_SKU_UPDATE", "TENANT_SKU_UPDATE", 9141),
    TENANT_SKU_ADD_ONLINE_SKU("TENANT_SKU_ADD_ONLINE_SKU", "TENANT_SKU_ADD_ONLINE_SKU", 9151),
    TENANT_SKU_ADD_4_CDQ("TENANT_SKU_ADD_4_CDQ", "TENANT_SKU_ADD_4_CDQ", 9023),
    TENANT_REGION_SKU_ADD_4_CDQ("TENANT_REGION_SKU_ADD_4_CDQ", "TENANT_REGION_SKU_ADD_4_CDQ", 9024),
    TENANT_REGION_SKU_UPDATE_4_CDQ("TENANT_REGION_SKU_UPDATE_4_CDQ", "TENANT_REGION_SKU_UPDATE_4_CDQ", 9025),
    TENANT_SPU_ADD("TENANT_SPU_ADD", "批量新增总部商品", 9301),
    TENANT_SPU_UPDATE("TENANT_SPU_UPDATE", "批量修改总部商品", 9305),
    TENANT_SPU_PIC_ADD("TENANT_SPU_PIC_ADD", "批量上传图片", 9302),
    TENANT_SPU_PIC_ADD_BY_NAME("TENANT_SPU_PIC_ADD_BY_NAME", "批量上传图片", 9303),
    TENANT_SPU_BOOTH_RELATION_ADD("TENANT_SPU_BOOTH_RELATION_ADD", "批量上传摊位商品关系", 9304),
    TENANT_BONUS_UPDATE("TENANT_BONUS_UPDATE", "TENANT_BONUS_UPDATE", 9182),
    TENANT_BONUS_UPDATE_GOAL("TENANT_BONUS_UPDATE_GOAL", "TENANT_BONUS_UPDATE_GOAL", 9183),
    TENANT_BONUS_UPDATE_SERVICE_STAR("TENANT_BONUS_UPDATE_SERVICE_STAR", "TENANT_BONUS_UPDATE_SERVICE_STAR", 9184),
    TENANT_BONUS_UPDATE_PUNISHMENT("TENANT_BONUS_UPDATE_PUNISHMENT", "TENANT_BONUS_UPDATE_PUNISHMENT", 9185),
    TENANT_BONUS_MEET_CRITERION_APPRAISAL("TENANT_BONUS_MEET_CRITERION_APPRAISAL", "TENANT_BONUS_MEET_CRITERION_APPRAISAL", 9186),

    EXPORT_TENANT_SKU_ASYNC("EXPORT_TENANT_SKU_ASYNC", "商品总库信息列表", 7000),
    EXPORT_POI_SKU_ASYNC("EXPORT_POI_SKU_ASYNC", "门店商品", 7001),
    EXPORT_POI_SKU_ASYNC_CDQ("EXPORT_STORE_SKU_4_CDQ", "门店商品", 9001),
    EXPORT_SKU_ASYNC_CDQ("EXPORT_SKU_4_CDQ", "全国商品", 9002),

    EXPORT_TENANT_SPU_LIST_ASYNC("EXPORT_TENANT_SPU_LIST_ASYNC", "全国商品主档SPU导出", 9401),
    EXPORT_TENANT_SPU_INFO_ASYNC("EXPORT_TENANT_SPU_INFO_ASYNC", "全国商品主档销售规格导出", 9402),
    EXPORT_TENANT_SPU_PURCHASE_SKU_AYSNC("EXPORT_TENANT_SPU_PURCHASE_SKU_AYSNC", "原料规格导出", 100005),
    EXPORT_STORE_SPU_PURCHASE_SKU("EXPORT_STORE_SPU_PURCHASE_SKU", "门店原料规格导出", 9407),
    EXPORT_REGION_SPU_ASYNC("EXPORT_REGION_SPU_ASYNC", "城市商品主档导出", 9403),
    EXPORT_POI_SPU_ASYNC("EXPORT_STORE_SPU_ASYNC", "门店商品导出", 9404),
    EXPORT_BOOTH_SPU_ASYNC("EXPORT_BOOTH_SPU_ASYNC", "摊位商品导出", 9405),
    EXPORT_ON_OFF_SALE_RECORD_ASYNC("EXPORT_ON_OFF_SALE_RECORD_ASYNC", "商品上下架记录导出", 9406),

    //下面六个任务类型不对应中台的类型,只是针对开通了买菜业务的QUICK_SKU_ADD和QUICK_POI_SKU_ADD
    QUICK_SKU_ADD_MAICAI("QUICK_SKU_ADD_MAICAI", "商品快捷上线(买菜)", 99991),
    QUICK_POI_SKU_ADD_MAICAI("QUICK_POI_SKU_ADD_MAICAI", "门店商品快捷上线(买菜)", 99992),
    QUICK_SKU_ADD_MAICAI_V2("QUICK_SKU_ADD_MAICAI_V2", "商品快捷上线(买菜)", 99993),
    QUICK_POI_SKU_ADD_MAICAI_V2("QUICK_POI_SKU_ADD_MAICAI_V2", "门店商品快捷上线(买菜)", 99994),
    QUICK_SKU_ADD_MAICAI_V3("QUICK_SKU_ADD_MAICAI_V3", "商品快捷上线(买菜)", 99995),
    QUICK_POI_SKU_ADD_MAICAI_V3("QUICK_POI_SKU_ADD_MAICAI_V3", "门店商品快捷上线(买菜)", 99996),
    BATCH_IMPORT_POI_SKU_MAICAI("BATCH_IMPORT_POI_SKU_MAICAI", "批量导入门店商品(买菜)", 99997),
    BATCH_UPDATE_POI_SKU_MAICAI("BATCH_UPDATE_POI_SKU_MAICAI", "批量修改门店商品(买菜)", 99998),
    SYNC_CHANNEL_PRICE("SYNC_CHANNEL_PRICE", "同步零售价", 4003),
    BATCH_CHANNEL_SKU_PRICE_STRATEGY_CHANGE("BATCH_CHANNEL_SKU_PRICE_STRATEGY_CHANGE", "批量定单品提价比例", 4004),
    QUICK_MATCH_SPU_BY_UPC("QUICK_MATCH_SPU_BY_UPC", "按UPC匹配标品并上线", 1030),
    MATCH_SPU_BY_UPC("MATCH_SPU_BY_UPC", "按UPC匹配标品", 1031),
    BATCH_CREATE_REGION_SKU("BATCH_CREATE_REGION_SKU", "从总部商品选择批量新增区域商品", 1040),
    COPY_REGION_SKU_FROM_OTHER_REGION("COPY_REGION_SKU_FROM_OTHER_REGION", "从城市复制商品", 1041),

    BATCH_IMPORT_REGION_SPU_ADD_4_CDQ("TENANT_REGION_SPU_ADD_4_CDQ", "批量新增城市商品", 9026),
    BATCH_IMPORT_REGION_SPU_UPDATE_4_CDQ("TENANT_REGION_SPU_UPDATE_4_CDQ", "批量修改城市商品", 9027),
    BATCH_CREATE_REGION_SPU_4_FROM("TENANT_REGION_SPU_ADD_4_CDQ_BY_FROM", "从总部商品选择批量新增区域商品", 9028),

    /**
     * 多规格 门店商品 批量任务类型
     */
    STORE_SPU_BATCH_UPDATE_STATUS("STORE_SPU_BATCH_UPDATE_STATUS", "门店商品批量上下架", 9501),
    STORE_SPU_BATCH_UPDATE_STATUS_BY_FORM("STORE_SPU_BATCH_UPDATE_STATUS_BY_FORM", "门店商品批量上下架", 9502),
    STORE_SPU_BATCH_SET_FRONT_CATEGORY("STORE_SPU_BATCH_SET_FRONT_CATEGORY", "门店商品批量设置店内分类", 9503),
    BATCH_IMPORT_POI_SPU("BATCH_IMPORT_POI_SPU", "批量导入门店商品", 1033),
    BATCH_UPDATE_POI_SPU("BATCH_UPDATE_POI_SPU", "批量修改门店商品", 1034),
    POI_SPU_QUICK_ONLINE("POI_SPU_QUICK_ONLINE", "门店商品快捷上线", 1032),
    STORE_UPC_IMPORT_AND_ONLINE("STORE_UPC_IMPORT_AND_ONLINE", "快捷新增标品", 1035),

    // 租户相关任务
    TENANT_BATCH_IMPORT_DELIVERY_TAKE("TENANT_BATCH_IMPORT_DELIVERY_TAKE", "批量导入配货单", 9161),
    TENANT_BATCH_IMPORT_STORE_AREA("TENANT_BATCH_IMPORT_STORE_AREA", "批量导入库位信息", 9171),
    ADJUST_SETTLE_AMT("ADJUST_SETTLE_AMT", "商品项调整对账金额", 9181),

    //  库存相关任务
    SKU_STOCK_BATCH_UPDATE("SKU_STOCK_BATCH_UPDATE", "批量商品库存设置", 9291),

    // 在Empower内部使用，在该处也定义一份，避免重复
    STOCK_TAKE_PRE_CREATE_SKU("STOCK_TAKE_PRE_CREATE_SKU", "动销盘点预生成SKU列表", AsyncTaskType.BATCH_PRE_CREATE_STOCK_TAKE_SKU.getType()),

    BATCH_UPDATE_STORE_SKU_WARNING_THRESHOLD("BATCH_UPDATE_STORE_SKU_WARNING_THRESHOLD", "批量商品安全库存值设置", 1046),
    BATCH_UPDATE_STORE_SKU_UNLIMITED("BATCH_UPDATE_STORE_UNLIMITED_STOCK", "批量商品库存是否是无限库存", 8796),

    EXPORT_CORE_SKU_STOCK("EXPORT_CORE_SKU_STOCK", "核心品库存导出", TaskType.EXPORT_CORE_SKU_STOCK.getValue()),
    EXPORT_POI_ONLINE_STOCK("EXPORT_POI_ONLINE_STOCK", "门店库存-导出门店线上库存", TaskType.BATCH_EXPORT_POI_ONLINE_STOCK.getValue()),

    // 价格审核相关
    EXPORT_QUOTE_REVIEW("EXPORT_QUOTE_REVIEW", "价格审核记录导出", PriceTaskTypeEnum.EXPORT_QUOTE_REVIEW.getCode()),

    // 价格相关
    EXPORT_PRICE_NOT_EQUAL("EXPORT_PRICE_NOT_EQUAL", "价格不一致数据导出", PriceTaskTypeEnum.EXPORT_PRICE_NOT_EQUAL.getCode()),

    /*saas data 相关任务类型*/
    // 报表下载
    TAB_DOWNLOAD("TAB_DOWNLOAD", "报表导出", com.sankuai.meituan.shangou.saas.crm.data.client.enums.TaskTypeEnum.TAB_DOWNLOAD.getType()),
    FULFILL_DATA_DOWNLOAD("FULFILL_DATA_DOWNLOAD", "履约数据导出", com.sankuai.meituan.shangou.saas.crm.data.client.enums.TaskTypeEnum.FULFILL_DATA_DOWNLOAD.getType()),

    // 市调相关
    BATCH_IMPORT_MR_PRICE("BATCH_IMPORT_MR_PRICE", "批量导入市调价格", TaskType.BATCH_IMPORT_MR_PRICE.getValue()),
    PRICE_WORK_TILE_ADJUST_CHANNEL_PRICE("PRICE_WORK_TILE_ADJUST_CHANNEL_PRICE", "定价工作台-批量调价", PriceTaskTypeEnum.PRICE_WORK_TILE_ADJUST_CHANNEL_PRICE.getCode()),


    //医药任务
    MEDICINE_TENANT_SKU_UPDATE("MEDICINE_TENANT_SKU_UPDATE", "批量修改商品", TaskType.MEDICINE_TENANT_SKU_UPDATE.getValue()),
    EXPORT_MEDICINE_TENANT_SKU("EXPORT_MEDICINE_TENANT_SKU", "商品主档商品导出", TaskType.EXPORT_MEDICINE_TENANT_SKU.getValue()),
    MEDICINE_TENANT_SKU_ADD("MEDICINE_TENANT_SKU_ADD", "医药租户商品批量导入", TaskType.MEDICINE_TENANT_SKU_ADD.getValue()),

    // 医药门店商品相关任务
    EXPORT_MEDICINE_STORE_SKU("EXPORT_MEDICINE_STORE_SKU", "门店商品导出", TaskType.EXPORT_MEDICINE_STORE_SKU.getValue()),
    MEDICINE_STORE_SKU_BATCH_UPDATE_STATUS("MEDICINE_STORE_SKU_BATCH_UPDATE_STATUS", "门店商品批量上下架", TaskType.MEDICINE_STORE_SKU_BATCH_UPDATE_STATUS.getValue()),
    MEDICINE_BATCH_ON_SALE("MEDICINE_BATCH_ON_SALE", "门店商品批量上架", TaskType.MEDICINE_BATCH_ON_SALE.getValue()),
    MEDICINE_BATCH_OFF_SALE("MEDICINE_BATCH_OFF_SALE", "门店商品批量上架", TaskType.MEDICINE_BATCH_OFF_SALE.getValue()),
    EXPORT_APPRAISE_RESULT("EXPORT_APPRAISE_RESULT", "考核结果导出", TaskType.EXPORT_APPRAISE_RESULT.getValue()),
    MEDICINE_POI_SKU_ADD("MEDICINE_POI_SKU_ADD", "医药门店商品批量导入", TaskType.MEDICINE_POI_SKU_ADD.getValue()),

    // 前台分类批量管理
    COPY_STORE_CATEGORY_AND_SORT("COPY_STORE_CATEGORY_AND_SORT", "复制分类及排序", TaskType.COPY_STORE_CATEGORY_AND_SORT_4_CDQ.getValue()),
    COPY_STORE_CATEGORY_ONLY_SORT("COPY_STORE_CATEGORY_ONLY_SORT", "仅复制排序", TaskType.COPY_STORE_ONLY_CATEGORY_SORT_4_CDQ.getValue()),
    COPY_STORE_CATEGORY_AND_SPU("COPY_STORE_CATEGORY_AND_SPU", "新增分类及商品", TaskType.COPY_STORE_CATEGORY_AND_SPU_CREATE_4_CDQ.getValue()),
    DELETE_STORE_CATEGORY_AND_SPU("DELETE_STORE_CATEGORY_AND_SPU", "删除分类及商品", TaskType.COPY_STORE_CATEGORY_AND_SPU_DELETE_4_CDQ.getValue()),

    BATCH_IMPORT_SCM_DELIVERY_TASK("BATCH_IMPORT_SCM_DELIVERY_TASK", "批量导入按单收货单", TaskType.BATCH_IMPORT_SCM_DELIVERY_TASK.getValue()),

    // 订货采购相关。Code范围[160001-169999]
    @Deprecated
    BATCH_UPDATE_STORE_CATEGORY_ORDER_GOODS_DAYS("BATCH_UPDATE_STORE_CATEGORY_ORDER_GOODS_DAYS", "批量更新门店品类订货天数", 1048),

    BATCH_EXPORT_SKU_RELATION("BATCH_EXPORT_SKU_RELATION", "批量导出供货关系",
            PurchaseTaskType.BATCH_EXPORT_SKU_RELATION.getCode()),
    BATCH_EXPORT_SKU_RELATION_V2("BATCH_EXPORT_SKU_RELATION_V2", "批量导出供货关系", PurchaseTaskType.BATCH_EXPORT_SKU_RELATION_V2.getCode()),
    BATCH_EXPORT_SKU_RELATION_BY_ROW("BATCH_EXPORT_SKU_RELATION", "批量导出供货关系-按单行查看",
            PurchaseTaskType.BATCH_EXPORT_SKU_RELATION_BY_ROW.getCode()),
    BATCH_EXPORT_SKU_RELATION_BY_SKU_AGG("BATCH_EXPORT_SKU_RELATION", "批量导出供货关系-按商品查看",
            PurchaseTaskType.BATCH_EXPORT_SKU_RELATION_BY_SKU_AGG.getCode()),
    BATCH_IMPORT_SKU_RELATION("BATCH_IMPORT_SKU_RELATION", "批量新增供货关系", PurchaseTaskType.BATCH_IMPORT_SKU_RELATION.getCode()),
    BATCH_IMPORT_SKU_RELATION_NEW("BATCH_IMPORT_SKU_RELATION", "批量新增供货关系", PurchaseTaskType.BATCH_IMPORT_SKU_RELATION_NEW.getCode()),
    BATCH_UPDATE_SKU_RELATION("BATCH_UPDATE_SKU_RELATION", "批量更新供货关系", PurchaseTaskType.BATCH_UPDATE_SKU_RELATION.getCode()),
    BATCH_UPDATE_SKU_RELATION_NEW("BATCH_UPDATE_SKU_RELATION", "批量更新供货关系", PurchaseTaskType.BATCH_UPDATE_SKU_RELATION_NEW.getCode()),
    BATCH_IMPORT_PURCHASE_PLAN_TASK("BATCH_IMPORT_PURCHASE_PLAN_TASK", "导入新建采购计划单", PurchaseTaskType.BATCH_IMPORT_PURCHASE_PLAN_TASK.getCode()),
    BATCH_IMPORT_ALLOCATION_ORDER_TASK("BATCH_IMPORT_ALLOCATION_ORDER_TASK", "导入调拨单", PurchaseTaskType.BATCH_IMPORT_ALLOCATION_ORDER_TASK.getCode()),
    BATCH_IMPORT_DISTRIBUTION_ORDER_TASK("BATCH_IMPORT_DISTRIBUTION_ORDER_TASK", "导入配销单", PurchaseTaskType.BATCH_IMPORT_DISTRIBUTE_ORDER.getCode()),
    BATCH_UPDATE_PURCHASE_RECEIPT("BATCH_UPDATE_PURCHASE_RECEIPT","批量修改采购单",PurchaseTaskType.BATCH_UPDATE_PURCHASE_RECEIPT.getCode()),
    BATCH_EXPORT_ORDER_REFER("BATCH_EXPORT_ORDER_REFER", "批量导出订货参考", PurchaseTaskType.BATCH_EXPORT_ORDER_REFER.getCode()),
    BATCH_IMPORT_REPLENISH_SETTING("BATCH_IMPORT_REPLENISH_SETTING", "批量导入补货设置", PurchaseTaskType.BATCH_IMPORT_REPLENISH_SETTING.getCode()),
    BATCH_EXPORT_REPLENISH_REFERENCE("BATCH_EXPORT_REPLENISH_REFERENCE", "批量导出补货参考", PurchaseTaskType.BATCH_EXPORT_REPLENISH_REFERENCE.getCode()),
    BATCH_EXPORT_REPLENISH_SETTING("BATCH_EXPORT_REPLENISH_SETTING", "批量导出补货设置", PurchaseTaskType.BATCH_EXPORT_REPLENISH_SETTING.getCode()),
    BATCH_CLONE_SUPPLY_RELATION("BATCH_CLONE_SUPPLY_RELATION", "复制供货关系", PurchaseTaskType.BATCH_CLONE_SUPPLY_RELATION.getCode()),
    BATCH_CLONE_SUPPLY_RELATION_NEW("BATCH_CLONE_SUPPLY_RELATION", "复制供货关系", PurchaseTaskType.BATCH_CLONE_SUPPLY_RELATION_NEW.getCode()),
    EXPORT_ALLOCATION_ORDER_SKU_DETAILS("EXPORT_ALLOCATION_ORDER_SKU_DETAILS", "调拨计划明细导出", com.sankuai.sgxsupply.purchase.client.thrift.task.PurchaseTaskType.EXPORT_ALLOCATION_ORDER_SKU_DETAILS.getCode()),
    BATCH_UPDATE_PURCHASE_SKU_TYPE("BATCH_UPDATE_PURCHASE_SKU_TYPE", "批量修改采购模式", PurchaseTaskType.BATCH_UPDATE_PURCHASE_SKU_TYPE.getCode()),
    BATCH_UPDATE_PURCHASE_SKU_STATUS("BATCH_UPDATE_PURCHASE_SKU_STATUS", "批量修改采购状态", PurchaseTaskType.BATCH_UPDATE_PURCHASE_SKU_STATUS.getCode()),
    BATCH_EXPORT_SUPPLIER("BATCH_EXPORT_SUPPLIER", "批量导出供应商信息", PurchaseTaskType.BATCH_EXPORT_SUPPLIER.getCode()),
    BATCH_UPDATE_SUPPLIER("BATCH_UPDATE_SUPPLIER", "批量更新供应商信息", PurchaseTaskType.BATCH_UPDATE_SUPPLIER.getCode()),
    BATCH_CREATE_SUPPLIER("BATCH_CREATE_SUPPLIER","批量创建供应商", PurchaseTaskType.BATCH_CREATE_SUPPLIER.getCode()),
    BATCH_EXPORT_ORDER_MERCHANDISE_SKU_FLAT("BATCH_EXPORT_ORDER_MERCHANDISE_SKU_FLAT", "要货商品明细导出",
            PurchaseTaskType.BATCH_EXPORT_ORDER_MERCHANDISE_SKU_FLAT.getCode()),
    BATCH_EXPORT_PURCHASE_PLAN_SKU_FLAT("BATCH_EXPORT_PURCHASE_PLAN_SKU_FLAT", "采购商品明细导出",
            PurchaseTaskType.BATCH_EXPORT_PURCHASE_PLAN_SKU_FLAT.getCode()),
    BATCH_EXPORT_ALLOCATION_ORDER_SKU_FLAT("BATCH_EXPORT_ALLOCATION_ORDER_SKU_FLAT", "调拨商品明细导出",
            PurchaseTaskType.BATCH_EXPORT_ALLOCATION_ORDER_SKU_FLAT.getCode()),

    BATCH_EXPORT_DISTIRBUTE_ORDER_SKU_FLAT("BATCH_EXPORT_DISTIRBUTE_ORDER_SKU_FLAT", "配销商品明细导出",
        PurchaseTaskType.BATCH_EXPORT_DISTIRBUTE_ORDER_SKU_FLAT.getCode()),
    BATCH_IMPORT_REPLENISH_SETTING_FOR_REGIONAL_WAREHOUSE("BATCH_IMPORT_REPLENISH_SETTING_FOR_REGIONAL_WAREHOUSE", "批量导入补货设置(中心仓)",
            PurchaseTaskType.BATCH_IMPORT_REPLENISH_SETTING_FOR_REGIONAL_WAREHOUSE.getCode()),
    BATCH_UPDATE_PURCHASE_SKU_STATUS_FOR_REGIONAL_WAREHOUSE("BATCH_UPDATE_PURCHASE_SKU_STATUS_FOR_REGIONAL_WAREHOUSE", "批量修改采购状态(中心仓)",
            PurchaseTaskType.BATCH_UPDATE_PURCHASE_SKU_STATUS_FOR_REGIONAL_WAREHOUSE.getCode()),
    BATCH_EXPORT_REPLENISH_REFERENCE_FOR_REGIONAL_WAREHOUSE("BATCH_EXPORT_REPLENISH_REFERENCE_FOR_REGIONAL_WAREHOUSE", "批量导出补货参考（中心仓）",
            PurchaseTaskType.BATCH_EXPORT_REPLENISH_REFERENCE_FOR_REGIONAL_WAREHOUSE.getCode()),
    BATCH_UPDATE_PURCHASE_SKU_DISTRIBUTE_TYPE("BATCH_UPDATE_PURCHASE_SKU_DISTRIBUTE_TYPE", "批量修改送货方式",
            PurchaseTaskType.BATCH_UPDATE_PURCHASE_SKU_DISTRIBUTE_TYPE.getCode()),

    BATCH_UPSERT_REPLENISH_LIST_SKU_TYPE("BATCH_UPSERT_REPLENISH_LIST_SKU_TYPE", "批量导入补货商品",
            PurchaseTaskType.BATCH_UPSERT_REPLENISH_LIST_SKU.getCode()),

    REPLENISH_LIST_CONFIRM_CREATE_REPLENISH_ORDER("REPLENISH_LIST_CONFIRM_CREATE_REPLENISH_ORDER", "补货清单生成单据",
            PurchaseTaskType.REPLENISH_LIST_CONFIRM_CREATE_REPLENISH_ORDER.getCode()),

    BATCH_EXPORT_REPLENISH_LIST("BATCH_EXPORT_REPLENISH_LIST", "补货清单导出",
            PurchaseTaskType.BATCH_EXPORT_REPLENISH_LIST.getCode()
    ),
    SUPPLY_RELATION_ASYNC_CLONE("SUPPLY_RELATION_ASYNC_CLONE", "同步更新供货关系", PurchaseTaskType.SUPPLY_RELATION_ASYNC_CLONE.getCode()),


    // 门店商品批量复制
    BATCH_STORE_SPU_COPY("BATCH_STORE_SPU_COPY", "门店商品-批量复制", TaskType.BATCH_STORE_SPU_COPY.getValue()),

    // 线上门店商品拉取
    ONLINE_SPU_PULL("PULL_SKU_FROM_ONLINE", "从线上拉取", ProductTaskTypeEnum.ONLINE_SPU_PULL_TASK.getType()),

    //饿了么线上门店商品拉取
    ELM_ONLINE_SPU_PULL("ELM_PULL_SKU_FROM_ONLINE", "饿了么从线上拉取", ProductTaskTypeEnum.ELM_ONLINE_SPU_PULL_TASK.getType()),
    //总部管品-美团线上门店商品拉取
    MT_ONLINE_SPU_PULL("MT_PULL_SKU_FROM_ONLINE", "总部管品-美团从线上拉取", ProductTaskTypeEnum.MT_ONLINE_SPU_PULL_TASK.getType()),
    //商品主档覆盖门店
    SPU_COVER_STORE_TASK("SPU_COVER_STORE_TASK", "从商品主档覆盖到门店", ProductTaskTypeEnum.SPU_COVER_STORE_TASK.getType()),
    PRODUCT_BIZ_BATCH_TASK_QUERY("PRODUCT_BIZ_BATCH_TASK_QUERY", "product biz商品批量任务", -1),
    STORE_COMPOSE_SKU_BATCH("STORE_COMPOSE_SKU_BATCH", "组合商品批量任务", -1),
    COMPOSE_SKU_IMPORT_TASK("COMPOSE_SKU_IMPORT_TASK", "批量导入组合商品", 1000010),
    COMPOSE_SKU_EXPORT_TASK("COMPOSE_SKU_EXPORT_TASK", "组合商品导出", 1000011),

    ERP_GOODS_EXPORT_TASK("ERP_GOODS_EXPORT_TASK", "ERP主档商品导出", ProductTaskTypeEnum.ERP_GOODS_EXPORT_TASK.getType()),


    // 有ERP租户的商品主档销售规格导出
    ERP_TENANT_SPU_EXPORT("ERP_TENANT_SPU_EXPORT", "商品主档-导出商品信息",ProductTaskTypeEnum.ERP_TENANT_SPU_EXPORT.getType()),
    // 导入单位转换系数
    UNIT_CONVERT_FACTOR("UNIT_CONVERT_FACTOR", "导入单位转换系数",ProductTaskTypeEnum.ERP_UNIT_CONVERT_FACTOR_UPDATE.getType()),

    // 有ERP租户的门店商品导出
    STORE_SPU_FROM_EXPORT("STORE_SPU_FROM_EXPORT", "有ERP租户的门店商品导出",ProductTaskTypeEnum.STORE_SPU_FROM_EXPORT.getType()),

    // 有ERP租户的门店商品库存价格导入
    ERP_STORE_SPU_PRICE_AND_STOCK_IMPORT("ERP_STORE_SPU_PRICE_AND_STOCK_IMPORT", "门店商品-Excel设置价格库存",ProductTaskTypeEnum.ERP_STORE_SPU_PRICE_AND_STOCK_IMPORT.getType()),

    ERP_STORE_SPU_PLU_IMPORT("ERP_STORE_SPU_PLU_IMPORT", "门店商品-Excel导入门店PLU码", ProductTaskTypeEnum.ERP_STORE_SPU_PLU_IMPORT.getType()),

    ERP_STORE_SPU_SELL_OUT_ON_SHELF_IMPORT("ERP_STORE_SPU_SELL_OUT_ON_SHELF_IMPORT", "门店商品-Excel设置商品售罄下架状态", ProductTaskTypeEnum.ERP_STORE_SPU_SELL_OUT_ON_SHELF_IMPORT.getType()),

    ERP_STORE_SPU_MANUAL_OFF_SHELF_EXPORT("ERP_STORE_SPU_MANUAL_OFF_SHELF_EXPORT", "商品主档-手工下架记录导出", ProductTaskTypeEnum.ERP_STORE_SPU_MANUAL_OFF_SHELF_EXPORT.getType()),

    ERP_STORE_SPU_CHANNEL_PRICE_ADJUST_RATE("ERP_STORE_SPU_CHANNEL_PRICE_ADJUST_RATE",
            ProductTaskTypeEnum.ERP_STORE_SPU_CHANNEL_PRICE_ADJUST_RATE.getName(),
            ProductTaskTypeEnum.ERP_STORE_SPU_CHANNEL_PRICE_ADJUST_RATE.getType()),
    ERP_STORE_SPU_BUSINESS_CHANNEL("ERP_STORE_SPU_BUSINESS_CHANNEL",  ProductTaskTypeEnum.ERP_STORE_SPU_BUSINESS_CHANNEL.getName(),
            ProductTaskTypeEnum.ERP_STORE_SPU_BUSINESS_CHANNEL.getType()),
    ERP_STORE_SPU_STATUS("ERP_STORE_SPU_STATUS", ProductTaskTypeEnum.ERP_STORE_SPU_STATUS.getName(),
            ProductTaskTypeEnum.ERP_STORE_SPU_STATUS.getType()),

    IMPORT_POI_BAG("IMPORT_POI_BAG",  ProductTaskTypeEnum.IMPORT_POI_BAG.getName(),
            ProductTaskTypeEnum.IMPORT_POI_BAG.getType()),

    EXPORT_POI_BAG("EXPORT_POI_BAG",  ProductTaskTypeEnum.EXPORT_POI_BAG.getName(),
            ProductTaskTypeEnum.EXPORT_POI_BAG.getType()),

    EXCEL_BATCH_CREATE_SPU("EXCEL_BATCH_CREATE_SPU", "excel批量新建商品", 1000021),
    MERCHANT_STORE_CATEGORY_ADJUST_TASK("MERCHANT_STORE_CATEGORY_ADJUST_TASK", "商品主档店内分类调整任务", ProductTaskTypeEnum
            .MERCHANT_STORE_CATEGORY_ADJUST_TASK.getType()),

    STORE_GROUP_CATEGORY_MIGRATE_TASK("STORE_GROUP_CATEGORY_MIGRATE_TASK", "同步分组分类到渠道", ProductTaskTypeEnum.STORE_GROUP_CATEGORY_MIGRATE_TASK.getType()),

    EXPORT_WECHAT_PROMOTION_SUMMARY("EXPORT_WECHAT_PROMOTION_SUMMARY", "微信酒水拉新整体数据", PromotionTaskTypeEnum.EXPORT_WECHAT_PROMOTION_SUMMARY.getType()),
    EXPORT_WECHAT_PROMOTION_DETAIL("EXPORT_WECHAT_PROMOTION_DETAIL", "微信酒水拉新详情数据", PromotionTaskTypeEnum.EXPORT_WECHAT_PROMOTION_DETAIL.getType()),

    //  店仓/库存新任务框架 Code范围[150001-150***] START

    BATCH_IMPORT_STORAGE_BIN(
            "BATCH_IMPORT_STORAGE_BIN", AsyncTaskType.BATCH_IMPORT_STORAGE_BIN.getName(), AsyncTaskType.BATCH_IMPORT_STORAGE_BIN.getType()
    ),
    BATCH_EXPORT_SCM_DELIVERY_ORDERS(
            "BATCH_EXPORT_SCM_DELIVERY_ORDERS", AsyncTaskType.BATCH_EXPORT_SCM_DELIVERY_ORDERS.getName(), AsyncTaskType.BATCH_EXPORT_SCM_DELIVERY_ORDERS.getType()
    ),
    BATCH_EXPORT_STOCK(
            "BATCH_EXPORT_STOCK", AsyncTaskType.BATCH_EXPORT_STOCK.getName(), AsyncTaskType.BATCH_EXPORT_STOCK.getType()
    ),
    BATCH_EXPORT_STOCK_FLOW(
            "BATCH_EXPORT_STOCK_FLOW", AsyncTaskType.BATCH_EXPORT_STOCK_FLOW.getName(), AsyncTaskType.BATCH_EXPORT_STOCK_FLOW.getType()
    ),
    // 批次库存导出
    BATCH_EXPORT_BATCH_STOCK(
            "BATCH_EXPORT_BATCH_STOCK", AsyncTaskType.BATCH_EXPORT_BATCH_STOCK.getName(), AsyncTaskType.BATCH_EXPORT_BATCH_STOCK.getType()
    ),
    // 批次库存流水导出
    BATCH_EXPORT_BATCH_STOCK_FLOW(
            "BATCH_EXPORT_BATCH_STOCK_FLOW", AsyncTaskType.BATCH_EXPORT_BATCH_STOCK_FLOW.getName(), AsyncTaskType.BATCH_EXPORT_BATCH_STOCK_FLOW.getType()
    ),
    ERP_BATCH_EXPORT_STOCK_FLOW(
            "ERP_BATCH_EXPORT_STOCK_FLOW", AsyncTaskType.ERP_BATCH_EXPORT_STOCK_FLOW.getName(), AsyncTaskType.ERP_BATCH_EXPORT_STOCK_FLOW.getType()
    ),
    // 批次效期管理导出
    BATCH_EXPORT_BATCH_STOCK_EXPIRE(
            "BATCH_EXPORT_BATCH_STOCK_EXPIRE", AsyncTaskType.BATCH_EXPORT_BATCH_STOCK_EXPIRE.getName(), AsyncTaskType.BATCH_EXPORT_BATCH_STOCK_EXPIRE.getType()
    ),

    BATCH_EXPORT_DAMAGE_ORDER(
            "BATCH_EXPORT_DAMAGE_ORDER", AsyncTaskType.BATCH_EXPORT_DAMAGE_ORDER.getName(), AsyncTaskType.BATCH_EXPORT_DAMAGE_ORDER.getType()
    ),
    BATCH_EXPORT_STOCK_ADJUST_ORDER(
            "BATCH_EXPORT_STOCK_ADJUST_ORDER", AsyncTaskType.BATCH_EXPORT_STOCK_ADJUST_ORDER.getName(), AsyncTaskType.BATCH_EXPORT_STOCK_ADJUST_ORDER.getType()
    ),

    BATCH_EXPORT_STOCK_ADJUST_ORDER_V2(
            "BATCH_EXPORT_STOCK_ADJUST_ORDER_V2", AsyncTaskType.BATCH_EXPORT_STOCK_ADJUST_ORDER_V2.getName(), AsyncTaskType.BATCH_EXPORT_STOCK_ADJUST_ORDER_V2.getType()
    ),
    BATCH_EXPORT_OUT_WAREHOUSE_ORDER(
            "BATCH_EXPORT_OUT_WAREHOUSE_ORDER", AsyncTaskType.BATCH_EXPORT_OUT_WAREHOUSE_ORDER.getName(), AsyncTaskType.BATCH_EXPORT_OUT_WAREHOUSE_ORDER.getType()
    ),
    BATCH_EXPORT_ALLOT_DIFF_ORDER(
            "BATCH_EXPORT_ALLOT_DIFF_ORDER", AsyncTaskType.BATCH_EXPORT_ALLOT_DIFF_ORDER.getName(), AsyncTaskType.BATCH_EXPORT_ALLOT_DIFF_ORDER.getType()
    ),
    BATCH_EXPORT_ALLOT_DIFF_SKU(
            "BATCH_EXPORT_ALLOT_DIFF_SKU",AsyncTaskType.BATCH_EXPORT_ALLOT_DIFF_SKU.getName(),AsyncTaskType.BATCH_EXPORT_ALLOT_DIFF_SKU.getType()
    ),
    BATCH_EXPORT_COST_ACCOUNTING(
            "BATCH_EXPORT_COST_ACCOUNTING", AsyncTaskType.BATCH_EXPORT_COST_ACCOUNTING.getName(), AsyncTaskType.BATCH_EXPORT_COST_ACCOUNTING.getType()
    ),
    BATCH_EXPORT_OUTBOUND_ORDERS(
        "BATCH_EXPORT_OUTBOUND_ORDERS", AsyncTaskType.BATCH_EXPORT_OUTBOUND_ORDERS.getName(), AsyncTaskType.BATCH_EXPORT_OUTBOUND_ORDERS.getType()
    ),
    BATCH_EXPORT_CT_OUTBOUND_ORDERS(
            "BATCH_EXPORT_CT_OUTBOUND_ORDERS", AsyncTaskType.BATCH_EXPORT_CT_OUTBOUND_ORDERS.getName(), AsyncTaskType.BATCH_EXPORT_CT_OUTBOUND_ORDERS.getType()
    ),
    BATCH_EXPORT_CT_OUTBOUND_SKUS(
            "BATCH_EXPORT_CT_OUTBOUND_SKUS", AsyncTaskType.BATCH_EXPORT_CT_OUTBOUND_SKUS.getName(), AsyncTaskType.BATCH_EXPORT_CT_OUTBOUND_SKUS.getType()
    ),
    BATCH_EXPORT_OUTBOUND_SKUS(
        "BATCH_EXPORT_OUTBOUND_SKUS", AsyncTaskType.BATCH_EXPORT_OUTBOUND_SKUS.getName(), AsyncTaskType.BATCH_EXPORT_OUTBOUND_SKUS.getType()
    ),
    BATCH_EXPORT_ERP_STOCK(
            "BATCH_EXPORT_ERP_STOCK", AsyncTaskType.BATCH_EXPORT_ERP_STOCK.getName(), AsyncTaskType.BATCH_EXPORT_ERP_STOCK.getType()
    ),
    BATCH_IMPORT_STOCK_ADJUSTMENT("BATCH_IMPORT_STOCK_ADJUSTMENT", AsyncTaskType.BATCH_IMPORT_STOCK_ADJUSTMENT.getName(), AsyncTaskType.BATCH_IMPORT_STOCK_ADJUSTMENT.getType()),
    BATCH_IMPORT_STOCK_ADJUSTMENT_V2("BATCH_IMPORT_STOCK_ADJUSTMENT_V2", AsyncTaskType.BATCH_IMPORT_STOCK_ADJUSTMENT_V2.getName(), AsyncTaskType.BATCH_IMPORT_STOCK_ADJUSTMENT_V2.getType()),
    BATCH_IMPORT_LOCATION_SORT("BATCH_IMPORT_LOCATION_SORT", AsyncTaskType.BATCH_IMPORT_LOCATION_SORT.getName(), AsyncTaskType.BATCH_IMPORT_LOCATION_SORT.getType()),
    POI_BATCH_ADD("POI_BATCH_ADD", AsyncTaskType.POI_BATCH_ADD.getName(), AsyncTaskType.POI_BATCH_ADD.getType()),
    POI_BATCH_UPDATE("POI_BATCH_UPDATE", AsyncTaskType.POI_BATCH_UPDATE.getName(), AsyncTaskType.POI_BATCH_UPDATE.getType()),

    ACCOUNT_BATCH_ADD("ACCOUNT_BATCH_ADD", AsyncTaskType.ACCOUNT_BATCH_ADD.getName(),
            AsyncTaskType.ACCOUNT_BATCH_ADD.getType()),
    POI_BATCH_RELATE_CHANNEL("POI_BATCH_RELATE_CHANNEL", AsyncTaskType.POI_BATCH_RELATE_CHANNEL.getName(), AsyncTaskType.POI_BATCH_RELATE_CHANNEL.getType()),
    //  店仓/库存新任务框架 Code范围[150001-150***] END

    // 无人仓相关任务 Code范围[151001-151***]
    UWMS_STANDARD_PRODUCT_EXPORT("UWMS_STANDARD_PRODUCT_EXPORT", "药品三维数据库查询导出", 151001),
    UWMS_STANDARD_PRODUCT_EXPORT_BY_FILE("UWMS_STANDARD_PRODUCT_EXPORT_BY_FILE", "药品三维数据库批量查询", 151002),

    FRANCHISE_ORDER_LIST_WITH_ITEM("FRANCHISE_EXPORT_ORDER_WITH_ITEM","B端订单列表导出", 17),

    FUSE_ORDER_LIST_EXPORT("FUSE_ORDER_LIST_EXPORT","订单列表导出",8),
    FUSE_REFUDN_LIST_EXPORT("FUSE_REFUDN_LIST_EXPORT","退单列表导出",16),
    FUSE_ORDER_ITEM_EXPORT("FUSE_ORDER_ITEM_EXPORT","订单商品明细导出",10),
    FUSE_REFUND_ITEM_EXPORT("FUSE_REFUND_ITEM_EXPORT","退单商品明细导出",15),
    FUSE_ORDER_LIST_AND_DETAIL_EXPORT("FUSE_ORDER_LIST_AND_DETAIL_EXPORT","订单列表+明细导出",18),



    TENANT_MULTI_CLEAN_DATA_EXPORT("TENANT_MULTI_CLEAN_DATA_EXPORT","商品数据生成",
            ProductTaskTypeEnum.CLEAN_TASK_EXPORT_DATA.getType()),
    TENANT_MULTI_CHECK_DATA( "TENANT_MULTI_CHECK_DATA","商品清洗数据检验", ProductTaskTypeEnum.CLEAN_TASK_CHECK_DATA.getType()),


    BATCH_IMPORT_CREATE_SUPPLIER("BATCH_IMPORT_CREATE_SUPPLIER","批量创建供应商", PurchaseTaskType.BATCH_CREATE_SUPPLIER.getCode()),


    //财务相关导出任务
    AGGREGATION_FINANCE_EXPORT_TASK("AGGREGATION_FINANCE_EXPORT_TASK", "核销对帐-汇总-导出", 10010),
    ORDER_FINANCE_EXPORT_TASK("ORDER_FINANCE_EXPORT_TASK", "核销对帐-订单-导出", 10008),
    ORDER_DETAIL_FINANCE_EXPORT_TASK("ORDER_DETAIL_FINANCE_EXPORT_TASK", "核销对帐-订单明细-导出", 10009),
    ACTIVITY_SHARE_FINANCE_EXPORT_TASK("ACTIVITY_SHARE_FINANCE_EXPORT_TASK", "活动对账-导出", 10011),

    // 交易提成相关任务，Code范围[231001-231***]
    QNH_DAILY_COMMISSION_BILL_EXPORT(TaskViewTypeEnum.QNH_DAILY_COMMISSION_BILL_EXPORT.getType(), TaskViewTypeEnum.QNH_DAILY_COMMISSION_BILL_EXPORT.getDesc(), ExportTaskTypeEnum.QNH_DAILY_COMMISSION_BILL_EXPORT.getCode()),
    QNH_DAILY_COMMISSION_BILL_DETAIL_EXPORT(TaskViewTypeEnum.QNH_DAILY_COMMISSION_BILL_DETAIL_EXPORT.getType(), TaskViewTypeEnum.QNH_DAILY_COMMISSION_BILL_DETAIL_EXPORT.getDesc(), ExportTaskTypeEnum.QNH_DAILY_COMMISSION_BILL_DETAIL_EXPORT.getCode()),


    QUERY_ERP_SPU_ERP_ADD_TASK(TaskViewTypeEnum.ERP_SPU_ERP_ADD_TASK.getType(), TaskViewTypeEnum.ERP_SPU_ERP_ADD_TASK.getDesc(), ProductTaskTypeEnum.ERP_SPU_ADD_FROM_STANDARD.getType()),

    QUERY_ERP_SPU_ERP_ADD_BY_UPC_TASK(TaskViewTypeEnum.ERP_SPU_ERP_ADD_BY_UPC_TASK.getType(), TaskViewTypeEnum.ERP_SPU_ERP_ADD_BY_UPC_TASK.getDesc(), ProductTaskTypeEnum.ERP_SPU_ADD_FROM_UPC.getType()),

    QUERY_ERP_SPU_ERP_UPDATE_TASK(TaskViewTypeEnum.ERP_SPU_ERP_UPDATE_TASK.getType(), TaskViewTypeEnum.ERP_SPU_ERP_UPDATE_TASK.getDesc(), ProductTaskTypeEnum.ERP_SPU_UPDATE.getType()),

    QUERY_ERP_SALE_STATUS_UPDATE_TASK(TaskViewTypeEnum.ERP_SALE_STATUS_UPDATE_TASK.getType(), TaskViewTypeEnum.ERP_SALE_STATUS_UPDATE_TASK.getDesc(), ProductTaskTypeEnum.ERP_SPU_ONLINE_STATUS_UPDATE.getType()),

    QUERY_ERP_SPU_PICTURE_TASK(TaskViewTypeEnum.ERP_SPU_PICTURE_TASK.getType(), TaskViewTypeEnum.ERP_SPU_PICTURE_TASK.getDesc(), ProductTaskTypeEnum.ERP_SPU_PIC_UPDATE.getType()),

    QUERY_ERP_SPU_CONTENT_PICTURE_TASK(TaskViewTypeEnum.ERP_SPU_CONTENT_PICTURE_TASK.getType(), TaskViewTypeEnum.ERP_SPU_CONTENT_PICTURE_TASK.getDesc(), ProductTaskTypeEnum.ERP_SPU_CONTENT_PIC_UPDATE.getType()),

    EXPORT_ERP_ITEM_WHITE_TASK(TaskViewTypeEnum.EXPORT_ERP_ITEM_WHITE_TASK.getType(), TaskViewTypeEnum.EXPORT_ERP_ITEM_WHITE_TASK.getDesc(), ProductTaskTypeEnum.CREATE_PRODUCT_FROM_ERP_EXPORT.getType()),

    QUERY_ERP_PIC_ZIP_ADD(TaskViewTypeEnum.ERP_ZIP_PIC_UPDATE.getType(), TaskViewTypeEnum.ERP_ZIP_PIC_UPDATE.getDesc(),
            ProductTaskTypeEnum.ERP_SPU_PIC_UPDATE_FROM_ZIP.getType()),

    SPU_ADD_BY_STANDARD(TaskViewTypeEnum.ERP_SPU_ADD_BY_STANDARD.getType(), TaskViewTypeEnum.ERP_SPU_ADD_BY_STANDARD.getDesc(),
            ProductTaskTypeEnum.PRODUCT_MATCH_STANDARD.getType()),
    SPU_DELETE(TaskViewTypeEnum.ERP_SPU_DELETE.getType(), TaskViewTypeEnum.ERP_SPU_DELETE.getDesc(), ProductTaskTypeEnum.DELETE_SPU.getType()),

    MERCHANT_STORE_CATEGORY_IMPORT_TASK("MERCHANT_STORE_CATEGORY_IMPORT_TASK", "商品主档店内分类新增导入",
            ProductTaskTypeEnum.MERCHANT_STORE_CATEGORY_IMPORT_TASK.getType()),

    MERCHANT_STORE_CATEGORY_EXPORT_TASK("MERCHANT_STORE_CATEGORY_EXPORT_TASK", "商品主档店内分类导出",
            ProductTaskTypeEnum.MERCHANT_STORE_CATEGORY_EXPORT_TASK.getType()),

    MERCHANT_STORE_CATEGORY_IMPORT_TASK4ERP("MERCHANT_STORE_CATEGORY_IMPORT_TASK4ERP", "线上分类设置-Excel导入分类",
            ProductTaskTypeEnum.MERCHANT_STORE_CATEGORY_IMPORT_TASK4ERP.getType()),

    MERCHANT_STORE_CATEGORY_EXPORT_TASK4ERP("MERCHANT_STORE_CATEGORY_EXPORT_TASK4ERP", "线上分类设置-导出",
            ProductTaskTypeEnum.MERCHANT_STORE_CATEGORY_EXPORT_TASK4ERP.getType()),
    BATCH_IMPORT_AUTO_SHELF("BATCH_IMPORT_AUTO_SHELF", ProductTaskTypeEnum.AUTO_SHELF_IMPORT.getName(),
            ProductTaskTypeEnum.AUTO_SHELF_IMPORT.getType()),

    BATCH_EXPORT_AUTO_SHELF("BATCH_EXPORT_AUTO_SHELF", ProductTaskTypeEnum.AUTO_SHELF_EXPORT.getName(),
            ProductTaskTypeEnum.AUTO_SHELF_EXPORT.getType()),

    BATCH_IMPORT_CHANNEL_BLACK("BATCH_IMPORT_CHANNEL_BLACK", "渠道黑名单导入", ProductTaskTypeEnum.CHANNEL_BLACK_IMPORT.getType()),

    BATCH_EXPORT_CHANNEL_BLACK("BATCH_EXPORT_CHANNEL_BLACK", "渠道黑名单导出", ProductTaskTypeEnum.CHANNEL_BLACK_EXPORT.getType()),
    MULTI_CODE_BY_BARCODE_QUERY_EXPORT(TaskViewTypeEnum.MULTI_CODE_BY_BARCODE_QUERY_EXPORT.getType(),TaskViewTypeEnum.MULTI_CODE_BY_BARCODE_QUERY_EXPORT.getDesc(), ProductTaskTypeEnum.MULTI_CODE_BY_BARCODE_QUERY_EXPORT.getType()),
    MULTI_BARCODE_BY_CODE_QUERY_EXPORT(TaskViewTypeEnum.MULTI_BARCODE_BY_CODE_QUERY_EXPORT.getType(),TaskViewTypeEnum.MULTI_BARCODE_BY_CODE_QUERY_EXPORT.getDesc(), ProductTaskTypeEnum.MULTI_BARCODE_BY_CODE_QUERY_EXPORT.getType()),


    // 牵牛花促销相关任务 Code范围 begin：[280001-289999]
    BATCH_IMPORT_ADD_DISCOUNT_ITEM("BATCH_IMPORT_ADD_DISCOUNT_ITEM", "批量导入单品直降活动商品", PromotionTaskType.BATCH_IMPORT_ADD_DISCOUNT_ITEM.getCode()),
    BATCH_IMPORT_ADD_CASH_BACK_ITEM("BATCH_IMPORT_ADD_CASH_BACK_ITEM", "批量导入满减活动商品", PromotionTaskType.BATCH_IMPORT_ADD_CASH_BACK_ITEM.getCode()),
    BATCH_IMPORT_ADD_FLASH_SALE_ITEM("BATCH_IMPORT_ADD_FLASH_SALE_ITEM", "批量导入秒杀活动商品", PromotionTaskType.BATCH_IMPORT_ADD_FLASH_SALE_ITEM.getCode()),
    BATCH_IMPORT_CANCEL_CHANNEL_ITEM("BATCH_IMPORT_CANCEL_CHANNEL_ITEM", "批量导入取消渠道活动商品", PromotionTaskType.BATCH_IMPORT_CANCEL_CHANNEL_ITEM.getCode()),
    BATCH_EXPORT_CHANNEL_ACTIVITY_ITEM("BATCH_EXPORT_CHANNEL_ACTIVITY_ITEM", "批量导出渠道活动商品", PromotionTaskType.BATCH_EXPORT_CHANNEL_ACTIVITY_ITEM.getCode()),
    // 牵牛花促销相关任务 Code范围 end：[280001-289999]

    SAFE_STOCK_IMPORT("SAFE_STOCK_IMPORT", "商品安全库存导入", ProductTaskTypeEnum.SAFE_STOCK_IMPORT.getType()),

    MERCHANT_SPU_BATCH_SYNC_CHANNEL("MERCHANT_SPU_BATCH_SYNC_CHANNEL", "总部商品-批量同步到渠道",
            ProductTaskTypeEnum.MERCHANT_SPU_BATCH_SYNC_CHANNEL.getType()),
    MERCHANT_SPU_SINGLE_SYNC_CHANNEL("MERCHANT_SPU_SINGLE_SYNC_CHANNEL", "总部商品-单个同步到渠道",
            ProductTaskTypeEnum.MERCHANT_SPU_SINGLE_SYNC_CHANNEL.getType()),
    STORE_SPU_BATCH_SYNC_CHANNEL("STORE_SPU_BATCH_SYNC_CHANNEL", "门店商品-批量同步到渠道",
            ProductTaskTypeEnum.STORE_SPU_BATCH_SYNC_CHANNEL.getType()),
    STORE_SPU_SINGLE_SYNC_CHANNEL("STORE_SPU_SINGLE_SYNC_CHANNEL", "门店商品-单个同步到渠道",
            ProductTaskTypeEnum.STORE_SPU_SINGLE_SYNC_CHANNEL.getType()),

    MERCHANT_SPU_BATCH_ADD_WATER_MARK("MERCHANT_SPU_BATCH_ADD_WATER_MARK", "商品主档-批量添加图片水印",
            ProductTaskTypeEnum.ADD_WATER_MARK.getType()),

    DELIVERY_CONFIG_BATCH_IMPORT("DELIVERY_CONFIG_BATCH_IMPORT", AsyncTaskType.DELIVERY_CONFIG_BATCH_IMPORT.getName(),
            AsyncTaskType.DELIVERY_CONFIG_BATCH_IMPORT.getType()),
    LONG_NAME_MERCHANT_SPU("LONG_NAME_MERCHANT_SPU","商品主档-名称过长导出", ProductTaskTypeEnum.LONG_NAME_MERCHANT_SPU.getType()),
    REPEAT_NAME_MERCHANT_SPU("REPEAT_NAME_MERCHANT_SPU","商品主档-名称重复导出",
            ProductTaskTypeEnum.REPEAT_NAME_MERCHANT_SPU.getType()),

    ERP_STORE_STOCK_CONFIG_IMPORT_TASK("ERP_STORE_STOCK_CONFIG_IMPORT_TASK", "门店安全库存导入",
            ProductTaskTypeEnum.ERP_STORE_STOCK_CONFIG_IMPORT_TASK.getType()),
    ERP_STORE_STOCK_CONFIG_EXPORT_TASK("ERP_STORE_STOCK_CONFIG_EXPORT_TASK", "门店安全库存导出",
            ProductTaskTypeEnum.ERP_STORE_STOCK_CONFIG_EXPORT_TASK.getType()),

    /* HU中心导出任务 start */
    EXPORT_HU_LIST(
            TaskViewTypeEnum.EXPORT_HU_LIST.name(), TaskViewTypeEnum.EXPORT_HU_LIST.getDesc(),
            com.sankuai.shangou.logistics.hu.api.task.constant.AsyncTaskType.EXPORT_HU_LIST.getType()
    ),
    /* HU中心导出任务 end */

    ERP_STORE_LOCATION_SKU_RELATION_IMPORT("ERP_STORE_LOCATION_SKU_RELATION_IMPORT", "ERP库位区域任务", 150040)

    ;

    private String type;
    private String desc;
    private int code;

    /**
     * 初始化时校验一下 code 是否重复，避免 getByCode 获取到非预期枚举
     * 注：出现重复时服务会启动失败
     */
    static {
        /*
         * 现有的重复名单，先忽略
         * BATCH_CREATE_SUPPLIER - BATCH_IMPORT_CREATE_SUPPLIER
         * QUERY_ERP_PIC_ZIP_ADD - QUERY_ERP_CONTENT_PIC_ZIP_ADD
         */
        List<Integer> notCheckCodes = Arrays.asList(-1, BATCH_CREATE_SUPPLIER.getCode(), QUERY_ERP_PIC_ZIP_ADD.getCode());
        Map<Integer, TaskTypeEnum> codeMap = new HashMap<>(512);
        for (TaskTypeEnum taskTypeEnum : TaskTypeEnum.values()) {
            Integer curCode = taskTypeEnum.getCode();
            if (!notCheckCodes.contains(curCode) && codeMap.containsKey(curCode)) {
                throw new IllegalStateException("TaskTypeEnum code 出现重复：" + taskTypeEnum + " - " + codeMap.get(curCode));
            }
            codeMap.put(curCode, taskTypeEnum);
        }
    }

    /**
     * @param type type
     * @return enum
     */
    public static TaskTypeEnum getByType(String type) {
        for (TaskTypeEnum e : TaskTypeEnum.values()) {
            if (StringUtils.equalsIgnoreCase(e.getType(), type)) {
                return e;
            }
        }
        return null;
    }

    /**
     * @param code code
     * @return enum
     */
    public static TaskTypeEnum getByCode(int code) {
        for (TaskTypeEnum e : TaskTypeEnum.values()) {
            if (code == e.getCode()) {
                return e;
            }
        }
        return null;
    }
}
