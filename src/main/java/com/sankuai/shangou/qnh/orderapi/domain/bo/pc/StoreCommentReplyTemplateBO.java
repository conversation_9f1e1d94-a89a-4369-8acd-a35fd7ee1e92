package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:门店回复模版BO
 * @author: gong_q<PERSON><PERSON>
 * @date: 2023/9/19
 * @time: 17:10
 * Copyright (C) 2015 Meituan
 * All rights reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class StoreCommentReplyTemplateBO {

    //租户id
    private Long tenantId;

    //门店list
    private List<Long> storeIdList;

    //模版内容
    private String templateContent;

    //当前账号accountId
    private Long operatorUid;

    //模版id
    private Long templateId;
    
}
