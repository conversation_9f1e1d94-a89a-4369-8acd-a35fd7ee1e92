package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 配送配置
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryConfigVO {

    @FieldDoc(
            description = "配送渠道ID"
    )
    @ApiModelProperty(value = "配送渠道ID", required = true)
    private String deliveryChannelId;

    @FieldDoc(
            description = "配送渠道名称"
    )
    @ApiModelProperty(value = "配送渠道名称", required = true)
    private String deliveryChannelName;

    private String appKey;

    private String secret;

    private int enable;
}
