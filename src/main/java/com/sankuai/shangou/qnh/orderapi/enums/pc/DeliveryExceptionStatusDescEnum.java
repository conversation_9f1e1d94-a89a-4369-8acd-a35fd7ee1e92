package com.sankuai.shangou.qnh.orderapi.enums.pc;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <EMAIL>
 * @Date: 2023/1/10 19:54
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum DeliveryExceptionStatusDescEnum {
    WAIT_CONFIRM_ORDER(1, "待商家接单"),
    WAIT_PICK(2, "待拣货"),
    WAIT_LAUNCH_DELIVERY(3, "待发配送"),
    WAITING_TO_ASSIGN_RIDER(4, "待接单"),
    RIDER_ASSIGNED(5, "待到店"),
    RIDER_ARRIVED_SHOP(6, "待取货"),
    RIDER_TAKEN_GOODS(7, "配送中"),
    RIDER_NOT_ASSIGN(8, "骑手未接单"),
    RIDER_NOT_ARRIVED_SHOP(9, "骑手超时未到店"),
    RIDER_NOT_TAKEN_GOODS(10, "骑手超时未取货"),
    DELIVERY_TIMEOUT(11, "骑手配送超时"),
    SYSTEM_ERROR(14, "系统异常"),

    /**
     * 取货失败
     */
    TAKE_EXCEPTION(16, "取货失败"),

    /**
     * 异常上报待处理
     */
    REPORT_EXCEPTION(17, "异常上报待处理"),
    ;

    /**
     * 编码
     */
    private int code;

    /**
     * 描述
     */
    private String desc;


    public static DeliveryExceptionStatusDescEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }

        for (DeliveryExceptionStatusDescEnum e : DeliveryExceptionStatusDescEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }

        return null;
    }
}
