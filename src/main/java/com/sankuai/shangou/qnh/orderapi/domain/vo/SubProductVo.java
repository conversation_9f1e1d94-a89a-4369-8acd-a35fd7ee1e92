package com.sankuai.shangou.qnh.orderapi.domain.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseFinanceDetailBO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * pc app共用dto
 * 部分退 克重退 金额退 组合商品信息
 */
@TypeDoc(
        description = "组合商品"
)
@ApiModel("组合商品")
@Data
public class SubProductVo {

    @FieldDoc(
            description = "商品名", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品名", required = true)
    private String name;
    @FieldDoc(
            description = "skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "skuId", required = true)
    private String skuId;
    @FieldDoc(
            description = "单价（也可能均价）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "单价（也可能均价）")
    private BigDecimal unitPrice;

    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品图片")
    private String picUrl;

    @FieldDoc(
            description = "upcCode", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "upcCode")
    private List<String> upcCode;

    @FieldDoc(
            description = "erpCode", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "erpCode")
    private String erpCode;

    @FieldDoc(
            description = "店内品类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "店内品类")
    private String[] inStoreCategoryList;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "规格")
    private String specification;

    @FieldDoc(
            description = "重量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "重量")
    private String weight;

    @FieldDoc(
            description = "数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "数量")
    private String quantity;

    @FieldDoc(
            description = "原订单明细的数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "原订单明细的数量")
    private String originItemQuantity;

    @FieldDoc(
            description = "多张商品图片URL", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "多张商品图片URL")
    private List<String> multiPicUrl;

    @FieldDoc(
            description = "组合比例下对应的重量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "组合比例下对应的重量")
    private String composeWeight;

    @FieldDoc(
            description = "组合比例下对应的数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "组合比例下对应的数量")
    private String composeQuantity;

    @FieldDoc(
            description = "该商品占所有子商品的百分比", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "该商品占所有子商品的百分比")
    private String proportion;

    @FieldDoc(
            description = "原价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "原价")
    private String originPrice;

    @FieldDoc(
            description = "售价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "售价")
    private String salePrice;

    @FieldDoc(
            description = "总价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品金额/退款金额")
    private String totalPrice;

    @FieldDoc(
            description = "平台整单优惠", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "平台整单优惠")
    private String platDiscount;

    @FieldDoc(
            description = "平台单品优惠", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "平台单品优惠")
    private String platItemDiscount;

    @FieldDoc(
            description = "商家整单优惠", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商家整单优惠")
    private String poiDiscount;

    @FieldDoc(
            description = "商家单品优惠", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商家单品优惠")
    private String poiItemDiscount;

    @FieldDoc(
            description = "记账金额", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "记账金额")
    private String billPrice;

    @FieldDoc(
            description = "记账金额", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "记账金额")
    private String billValue;

    @FieldDoc(
            description = "服务ID,唯一标识", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "服务ID,唯一标识")
    private Long serviceId;

    @FieldDoc(
            description = "原总价  单位:分", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "原总价  单位:分")
    private String originalTotalPrice;


    @FieldDoc(
            description = "实付金额  单位:分", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "实付金额  单位:分")
    private String totalPayAmount;

    @FieldDoc(
            description = "父商品OrderItemId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "父商品OrderItemId")
    private Long composeOrderItemId;

    @FieldDoc(description = "缺货数量", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(name = "缺货数量", required = true)
    private Integer exchangeFromCount;

    @FieldDoc(description = "换货数量", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(name = "换货数量", required = true)
    private Integer exchangeToCount;


    @FieldDoc(
            description = "换货商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "换货商品列表")
    private List<ExchangeProductVo> exchangeProductVoList;

    @FieldDoc(description = "售后子商品对应的换货子商品信息", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "售后子商品对应的换货子商品信息")
    private AfsExchangeProductVo afsExchangeProduct;


    public void mergeFinanceInfo(OrderFuseFinanceDetailBO.ComposeItemFinanceInfo composeItemFinanceInfo){
        if(Objects.isNull(composeItemFinanceInfo)){
            return;
        }
        originPrice = ConverterUtils.formatMoney(composeItemFinanceInfo.getOriginPrice());
        salePrice = ConverterUtils.formatMoney(composeItemFinanceInfo.getSalePrice());
        totalPrice = ConverterUtils.formatMoney(composeItemFinanceInfo.getItemSaleAmt());
        platDiscount = ConverterUtils.formatMoney(composeItemFinanceInfo.getPlatPromotion());
        platItemDiscount = ConverterUtils.formatMoney(composeItemFinanceInfo.getPlatItemPromotion());
        poiDiscount = ConverterUtils.formatMoney(composeItemFinanceInfo.getPoiPromotion());
        poiItemDiscount = ConverterUtils.formatMoney(composeItemFinanceInfo.getPoiItemPromotion());
        poiItemDiscount = ConverterUtils.formatMoney(composeItemFinanceInfo.getPoiItemPromotion());
        totalPayAmount = ConverterUtils.formatMoney(composeItemFinanceInfo.getTotalPayAmount());
    }

}
