package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(
        description = "轨迹详情"
)
public class OrderTrackDetailVO {

    @FieldDoc(
            description = "UUID,用于前端取值"
    )
    @ApiModelProperty(value = "UUID", required = false)
    private String uuid = UUID.randomUUID().toString();

    @FieldDoc(
            description = "轨迹操作类型"
    )
    @ApiModelProperty(value = "轨迹操作类型", required = true)
    private Integer operatorCode;

    @FieldDoc(
            description = "描述"
    )
    @ApiModelProperty(value = "描述", required = true)
    private String operatorDesc;

    @FieldDoc(
            description = "操作人"
    )
    @ApiModelProperty(value = "操作人", required = false)
    private String operatorName;

    @FieldDoc(
            description = "操作时间"
    )
    @ApiModelProperty(value = "操作时间", required = true)
    private Long operationTime;

    @FieldDoc(
            description = "操作类型"
    )
    @ApiModelProperty(value = "操作类型", required = true)
    private Integer operatorType;

    @FieldDoc(
            description = "备注"
    )
    @ApiModelProperty(value = "备注", required = false)
    private String comment;


}
