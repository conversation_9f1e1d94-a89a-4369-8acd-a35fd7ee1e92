package com.sankuai.shangou.qnh.orderapi.enums.pc.infrastructure;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;

import com.sankuai.shangou.qnh.orderapi.remote.ChannelManageRemoteService;
import com.sankuai.shangou.qnh.orderapi.configuration.pc.SpringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author:<EMAIL>
 * @Date: 2019/01/03 下午15:37
 * @Description:渠道枚举类
 */
@AllArgsConstructor
@Getter
@Slf4j
public enum ChannelTypeEnum {
    MEITUAN(ChannelType.MEITUAN.getValue(), "美团外卖"),
    ELEM(ChannelType.ELEM.getValue(), "饿了么"),
    JD2HOME(ChannelType.JD2HOME.getValue(), "京东到家"),
    MT_MEDICINE(ChannelType.MT_MEDICINE.getValue(), "美团医药"),
    YOUZAN(ChannelType.YOU_ZAN.getValue(), "有赞"),
    WEIXIN(ChannelType.WEIXIN.getValue(), "微信"),
    MT_DRUNK_HORSE(ChannelType.MT_DRUNK_HORSE.getValue(), "微商城"),
    QUAN_QIU_WA(ChannelType.QUAN_QIU_WA.getValue(), "全球蛙"),
    SELF_CHANNEL(ChannelType.SELF_CHANNEL.getValue(), "自有平台");

    /**
     * 渠道编码
     */
    private int code;
    /**
     * 渠道名称
     */
    private String desc;

    /**
     * 注：此方法不支持私有渠道，已废弃
     * 判定渠道ID是否正确，可以考虑使用 {@link #getDescByCode} 方法
     */
    @Deprecated
    public static ChannelTypeEnum getByCode(Integer code) {
        for (ChannelTypeEnum e : ChannelTypeEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }
        return null;
    }

    public static List<Integer> getAllChannelIds() {
        return Arrays.asList(ChannelTypeEnum.values()).stream().map(ChannelTypeEnum::getCode).collect(Collectors.toList());
    }

    /**
     * 查询渠道名
     */
    public static String getDescByCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (ChannelTypeEnum e : ChannelTypeEnum.values()) {
            if (e.getCode() == code) {
                return e.getDesc();
            }
        }

        // 走不到时 fallback 为查询
        try {
            log.info("getDescByCode fallback 为远程查询 {}", code);
            ChannelManageRemoteService channelManageRemoteService = SpringUtils.getBean(ChannelManageRemoteService.class);
            return channelManageRemoteService.queryChannelNameByChannelId(code);
        } catch (Throwable e) {
            log.error("getDescByCode 远程查询异常", e);
        }

        return null;
    }
}
