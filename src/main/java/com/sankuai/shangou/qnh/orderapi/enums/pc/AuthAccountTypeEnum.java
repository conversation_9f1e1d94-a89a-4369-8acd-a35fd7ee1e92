package com.sankuai.shangou.qnh.orderapi.enums.pc;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <EMAIL>
 * @Date: 2019/5/16 16:23
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum AuthAccountTypeEnum {

    ORDINARY_USER(1,"普通用户"),
    /**
     * @FieldDoc(
     *     description = "管理账户"
     * )
     */
    ADMIN(2,"管理员"),
    /**
     * @FieldDoc(
     *     description = "摊位账户"
     * )
     */
    BOOTH(3,"摊主");

    private int type;

    private String desc;


    public static AuthAccountTypeEnum getByType(int type) {
        for (AuthAccountTypeEnum e : AuthAccountTypeEnum.values()) {
            if (e.getType() == type) {
                return e;
            }
        }

        return null;
    }

}
