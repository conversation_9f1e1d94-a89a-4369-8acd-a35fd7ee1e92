package com.sankuai.shangou.qnh.orderapi.utils.app;


import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018/9/26
 */
public class TimeUtils {
    private static final long DAY_TIME_STAMP = (long)1000 * 3600 * 24;

    private static final String pattern = "yyyy-MM-dd HH:mm:ss";

    private static final String ymdPattern = "yyyy-MM-dd";

    private static final String mdhmPattern = "MM-dd HH:mm";

    private static final String ymdhmPattern = "yyyy-MM-dd HH:mm";

    private static final String hmPattern = "HH:mm";

    // 搜索启始时间(1970-01-01 08:00:00),所有搜索输入的合法时间不能小于它
    private static final long SEARCH_BEGIN_TIME = 0L;

    private static final int TIME_CONSTANT = 60;

    public static String convertTimeStamp2Str(long timestamp) {

        return DateFormatUtils.format(timestamp, pattern);
    }

    public static String convertTimeStamp2SimpleStr(long timestamp) {

        return DateFormatUtils.format(timestamp, ymdPattern);
    }

    public static long getBeforeDayTimeStamp(int beforeDay) {
        if (beforeDay <= 0) {
            return System.currentTimeMillis();
        }
        return System.currentTimeMillis() - DAY_TIME_STAMP * beforeDay;
    }

    public static String convertTimeStamp2MDHMStr(long timestamp) {

        return DateFormatUtils.format(timestamp, mdhmPattern);
    }

    public static String convertTimeStamp2YMDHMStr(long timestamp) {

        return DateFormatUtils.format(timestamp, ymdhmPattern);
    }

    public static String convertTimeStamp2HMStr(long timestamp) {

        return DateFormatUtils.format(timestamp, hmPattern);
    }

    public static int convertSecond2Minute(int second) {

        //注意，这里秒一定是60的倍数
        return second / TIME_CONSTANT;
    }


    private static boolean isAllEmpty(String[] array) {
        if (ArrayUtils.isEmpty(array)) {
            return true;
        }

        for (String str : array) {
            if (StringUtils.isNotEmpty(str)) {
                return false;
            }
        }
        return true;
    }

    private static long getTime(long initialTime, int month, long minTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(initialTime);
        calendar.add(calendar.MONTH, month);
        long resultTime = calendar.getTimeInMillis();
        if (resultTime < minTime) {
            return minTime;
        }
        return resultTime;
    }

    public static Long localDateToMills(LocalDate localDate){
        if (localDate == null) {
            return null;
        }
        return localDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static Long localDateTimeToMills(LocalDateTime localDateTime){
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static LocalDate stringFormatToLocalDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(ymdPattern));
    }

}

