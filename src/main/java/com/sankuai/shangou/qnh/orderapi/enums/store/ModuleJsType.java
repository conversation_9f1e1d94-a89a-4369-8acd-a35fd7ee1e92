/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.shangou.qnh.orderapi.enums.store;

/**
 * <br><br>
 * Author: l<PERSON><PERSON><PERSON><PERSON> <br>
 * Date: 2019-04-07 Time: 00:41
 */
public enum ModuleJsType {

    MRN(1, "mrn"),
    P<PERSON><PERSON>SO(2, "picasso"),
    H5(3, "h5"),
    DEFAULT(-1, ""),
    ;

    public final int code;
    public final String desc;

    ModuleJsType(int code, String desc) {
        this.code = code;
        this.desc = desc + "Wrapper";
    }

    public static ModuleJsType codeOf(int code) {
        for (ModuleJsType jsType : values()) {
            if (jsType.code == code) {
                return jsType;
            }
        }
        return DEFAULT;
    }
}
