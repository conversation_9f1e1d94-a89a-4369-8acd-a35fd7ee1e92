package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelStoreDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StoreDTO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ChannelStoreInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PoiInfoWithChannelInfoVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2019/3/8 16:35
 * @Description:
 */
@Data
public class PoiInfoWithChannelInfosBO {

    private Long poiId;

    private String poiName;

    private Long tenantId;

    private String outPoiId;

    private String poiAddress;

    private Map<Integer, String> channelPoiNames;

    private Map<Integer, ChannelStoreInfoVO> channelPoiMap;

    public PoiInfoWithChannelInfosBO(StoreDTO storeDTO, List<ChannelStoreDTO> channelStoreDTOS) {
        poiId = storeDTO.getStoreId();
        poiName = storeDTO.getStoreName();
        tenantId = storeDTO.getTenantId();
        outPoiId = storeDTO.getStoreCode();

        if (CollectionUtils.isNotEmpty(channelStoreDTOS)) {
            channelPoiNames = channelStoreDTOS.stream().collect(Collectors.toMap(ChannelStoreDTO::getChannel,
                    a -> a.getChannelPoiName() + " " + a.getChannelPoiCode()));
            channelPoiMap = channelStoreDTOS.stream().collect(Collectors.toMap(ChannelStoreDTO::getChannel, ChannelStoreInfoVO::build));
        }
    }

    public PoiInfoWithChannelInfoVO toPoiResp() {
        PoiInfoWithChannelInfoVO poiInfo = new PoiInfoWithChannelInfoVO();
        poiInfo.setPoiId(String.valueOf(poiId));
        poiInfo.setPoiName(poiName);
        poiInfo.setOutPoiId(outPoiId);
        poiInfo.setAddress(poiAddress);
        poiInfo.setStatus("有效");
        poiInfo.setChannelPois(channelPoiNames);
        poiInfo.setChannelPoiInfo(channelPoiMap);
        return poiInfo;
    }

}
