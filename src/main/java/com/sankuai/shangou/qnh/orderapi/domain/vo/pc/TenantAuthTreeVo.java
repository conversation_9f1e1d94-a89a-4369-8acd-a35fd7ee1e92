package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.sankuai.meituan.shangou.empower.auth.common.tree.AppAuthTree;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2019-12-24
 **/
@Data
public class TenantAuthTreeVo {

    /**
     * 租户ID
     */
    private long tenantId;

    /**
     * 租户权限树
     */
    private List<AppAuthTree> appAuthTree;

    /**
     * 权限树是否可编辑
     */
    private boolean IsEditable;

}
