package com.sankuai.shangou.qnh.orderapi.enums.pc;

import java.util.HashMap;
import java.util.Map;

/**
 * 配送发单模式.
 * 参考 com.sankuai.meituan.shangou.empower.tms.delivery.service.domain.delivery.DeliveryLaunchTypeEnum.
 *
 * <AUTHOR>
 * @since 2021/3/5 14:39
 */
public enum DeliveryLaunchPatternEnum {
    /**
     * 自动发配送
     */
    AUTO_LAUNCH_DELIVERY(1),

    /**
     * 手动发配送
     */
    MANUAL_LAUNCH_DELIVERY(2);

    private static final Map<Integer, DeliveryLaunchPatternEnum> CODE_ENUM_MAP = new HashMap<>();

    static {
        for (DeliveryLaunchPatternEnum each : values()) {
            CODE_ENUM_MAP.put(each.code, each);
        }
    }

    private final int code;

    DeliveryLaunchPatternEnum(int code) {
        this.code = code;
    }

    public static DeliveryLaunchPatternEnum enumOf(Integer code) {
        return CODE_ENUM_MAP.get(code);
    }

    public int getCode() {
        return code;
    }
}
