/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.shangou.qnh.orderapi.enums.store;

/**
 * 权限资源类型枚举
 * <br><br>
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <br>
 * Date: 2019-04-02 Time: 10:11
 */
public enum AuthResourceType {

    MENU(1, "菜单"),
    PAGE(2, "页面"),
    BUTTON(3, "按钮"),
    FIELD(4, "字段"),
    SUB_PAGE(5, "子页面");

    public final int type;
    public final String desc;

    AuthResourceType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
