package com.sankuai.shangou.qnh.orderapi.enums.pc;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/28 19:39
 * @Description: 库存模块错误码
 */
@Getter
@AllArgsConstructor
public enum ChannelStockErrorTypeEnum {
    PART_GOODS_CONFLICT(1001, "部分商品冲突");

    private int code;

    private String desc;


    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static TaskErrorTypeEnum getByCode(int code) {
        for (TaskErrorTypeEnum e : TaskErrorTypeEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }

        return null;
    }
}
