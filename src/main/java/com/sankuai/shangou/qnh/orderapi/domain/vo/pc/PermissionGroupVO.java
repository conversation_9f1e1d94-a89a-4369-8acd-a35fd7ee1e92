package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PermissionGroupVo;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

/**
 * @Author: <EMAIL>
 * @Date: 2018/12/4 下午2:29
 */
@TypeDoc(
        description = "数据权限组列表"
)
@ApiModel("数据权限组列表")
@Getter
public class PermissionGroupVO {
    /**
     * @FieldDoc(
     *     description = "数据权限id"
     * )
     */
    public long id;
    /**
     * @FieldDoc(
     *     description = "租户id"
     * )
     */
    public long tenantId; // required
    /**
     * @FieldDoc(
     *     description = "APP ID"
     * )
     */
    public int appId; // required
    /**
     * @FieldDoc(
     *     description = "权限组编码"
     * )
     */
    public String code; // required
    /**
     * @FieldDoc(
     *     description = "权限组名称"
     * )
     */
    public String name; // required
    /**
     * @FieldDoc(
     *     description = "类型 1-门店，2-数据权限组"
     * )
     */
    public int type; // required


    /**
     * 返回根节点code(用于异步接口)
     */
    public long rootCode;

    /**
     * 返回部门节点code
     */
    public long rootDepartmentCode;

    /**
     * 根节点类型(门店类型/仓库类型)
     */
    public int rootResourceType;

    /**
     * 全路径
     */
    public String fullName;

    /**
     * 数据权限类型和租户组织结构字段值映射
     */
    public int dataAuthDepType;


    public PermissionGroupVO(PermissionGroupVo vo){
        this.id = vo.getId();
        this.tenantId = vo.getId();
        this.appId = vo.getAppId();
        this.code = vo.getCode();
        this.name = vo.getName();
        this.type = vo.getType();
        this.dataAuthDepType = DataAuthKey.dataAuthToDepTypeMap(vo.getType());
    }


    public PermissionGroupVo toPermissionGroupVo(){
        PermissionGroupVo permissionGroupVo = new PermissionGroupVo();

        permissionGroupVo.setAppId(appId);
        permissionGroupVo.setTenantId(ContextHolder.currentUserTenantId());
        permissionGroupVo.setCode(code);
        permissionGroupVo.setName(name);
        permissionGroupVo.setType(type);

        return permissionGroupVo;
    }

}
