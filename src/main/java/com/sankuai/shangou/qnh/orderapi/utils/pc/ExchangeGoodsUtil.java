package com.sankuai.shangou.qnh.orderapi.utils.pc;

import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ExchangeItemInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ItemInfoVO;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;

import static com.meituan.shangou.saas.order.platform.ExtDataKeyConstants.ExchangeEk.*;

@Slf4j
public class ExchangeGoodsUtil {


    /**
     * 处理换货商品原/现单个售价、总重量
     */
    public static void dealSetPriceAndWeight(ExchangeItemInfoVO exchangeItemInfoVO, Map<String, Object> stringObjectMap) {
        try {
            if(Objects.isNull(exchangeItemInfoVO) || Objects.isNull(exchangeItemInfoVO.getItemInfoVO())){
                return;
            }

            Object exchangeOriginPrice = stringObjectMap.get(EXE_CHANGE_ORIGIN_PRICE_KEY);
            Object exchangeSalePrice = stringObjectMap.get(EXE_CHANGE_SALE_PRICE_KEY);
            Object exchangeWeight = stringObjectMap.get(EXE_CHANGE_PRODUCT_WEIGHT_KEY);
            int originPrice = Integer.parseInt(String.valueOf(Objects.isNull(exchangeOriginPrice) ? "0" : exchangeOriginPrice));
            int salePrice = Integer.parseInt(String.valueOf(Objects.isNull(exchangeSalePrice) ? "0" : exchangeSalePrice));
            ItemInfoVO itemInfoVO = exchangeItemInfoVO.getItemInfoVO();
            itemInfoVO.setOriginPrice(ConverterUtils.formatMoney(originPrice));
            itemInfoVO.setSalePrice(ConverterUtils.formatMoney(salePrice));
            itemInfoVO.setWeight(Integer.parseInt(Objects.isNull(exchangeWeight) ? "0" : String.valueOf(exchangeWeight)));
        }catch (Exception e){
            log.info("ExchangeGoodsUtil.dealSetPriceAndWeight is error exchangeItemInfoVO: {}, stringObjectMap: {}", exchangeItemInfoVO, stringObjectMap);
        }
    }
}
