package com.sankuai.shangou.qnh.orderapi.service.app;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.annotation.Degrade;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.Strings;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.dto.request.online.*;
import com.meituan.shangou.saas.order.management.client.dto.request.revenue.MerchantOrderRevenueDetailRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListOrderResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListViewIdConditionResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSQueryOrderQuantityResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsOrderSearchResponseV2;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.*;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderStatusLog;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.MerchantOrderListRevenueDetailResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderAmountInfo;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderRevenueDetailResponse;
import com.meituan.shangou.saas.order.management.client.enums.*;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchThriftServiceV2;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.management.client.utils.DesensitizeReceiverInfoUtil;
import com.meituan.shangou.saas.order.management.client.utils.ExchangeUtil;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderLabelModel;
import com.meituan.shangou.saas.order.management.client.utils.OrderUtil;
import com.meituan.shangou.saas.order.management.client.utils.param.DesensitizeReceiverInfoExtParam;
import com.meituan.shangou.saas.order.management.client.utils.result.DesensitizeReceiverInfoResult;
import com.meituan.shangou.saas.order.platform.enums.*;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PrintOpLogStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.sankuai.meituan.reco.pickselect.dto.OrderIdentifierDTO;
import com.sankuai.meituan.reco.pickselect.logic.common.Status;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.PickSelectPrintQueryThriftService;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.dto.OrderPrintQueryDto;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.dto.OrderPrintResultDto;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.request.OrderPrintResultQueryRequest;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.response.OrderPrintResultQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryOrderType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderCouldOperateItem;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.TPageInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.BatchStoreConfigQueryResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TAggDeliveryPlatformConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.DeliveryOperateItem;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TOrderIdentifier;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.DeliveryExceptionOrderSubTypeCountResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.DeliveryExceptionOrdersBySubTypeAndStoreIdsResponse;
import com.sankuai.shangou.qnh.orderapi.constant.app.CommonConstants;
import com.sankuai.shangou.qnh.orderapi.constant.app.IntegerBooleanConstants;
import com.sankuai.shangou.qnh.orderapi.constant.app.MiniAppConstants;
import com.sankuai.shangou.qnh.orderapi.constant.app.OrderShowStatusConstants;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.converter.app.ActionTagConverter;
import com.sankuai.shangou.qnh.orderapi.converter.app.ChannelOrderConverter;
import com.sankuai.shangou.qnh.orderapi.converter.app.OrderStatusConverter;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.User;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.*;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.*;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderTagVO;
import com.sankuai.shangou.qnh.orderapi.enums.OcmsRefundAuditType;
import com.sankuai.shangou.qnh.orderapi.enums.app.*;
import com.sankuai.shangou.qnh.orderapi.exception.app.CommonLogicException;
import com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.remote.*;
import com.sankuai.shangou.qnh.orderapi.service.pc.FuseOrderService;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.TimeUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum.DRAFT;
import static com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum.DRAFT_DONE;

/**
 * @description:小程序中台服务
 * @author: gong_qisheng
 * @date: 2023/7/3
 * @time: 20:36
 *        Copyright (C) 2015 Meituan
 *        All rights reserved
 */
@Service
@Slf4j
public class MiniAppOrderService {

    @Resource
    AuthRemoteService authThriftWrapper;

    @Resource
    TenantRemoteService tenantWrapper;

    @Resource
    OrderService ocmsOrderServiceWrapper;

    @Resource
    OCMSQueryThriftService ocmsQueryThriftService;

    @Resource
    OcmsOrderSearchThriftServiceV2 orderSearchThriftServiceV2;

    @Resource(name = "appDeliveryInfoService")
    AppendDeliveryInfoService appendDeliveryInfoService;

    @Autowired
    TmsRemoteService tmsServiceWrapper;

    @Resource
    DeliveryChannelRemoteService deliveryChannelWrapper;

    @Resource
    PickSelectPrintQueryThriftService pickSelectPrintQueryThriftService;

    @Autowired
    SaasCrmDataRemoteService saasCrmDataWrapper;

    @Resource
    PoiRemoteService poiRemoteService;

    @Resource
    private FuseOrderService fuseOrderService;

    private static final List<OrderStatusEnum> NOT_SHOW_DELIVERY_ITEM_ORDER_STATUS = Arrays.asList(
            OrderStatusEnum.CANCELED,
            OrderStatusEnum.SUBMIT,
            OrderStatusEnum.PAYING,
            OrderStatusEnum.CLOSED
    );


    /**
     * 获取首页信息，包括权限、规格、待办任务等信息.
     *
     * @param homePageRequest 首页请求参数
     * @return 首页信息响应
     */
    public CommonResponse<OrderHomePageResponse> homePage(HomePageRequest homePageRequest) {
        OrderHomePageModuleVO orderHomePageModuleVO = OrderHomePageModuleVO.homePageInit();
        OrderPendingTaskVO orderPendingTaskVO = OrderPendingTaskVO.homePageInit();
        List<Integer> orderQuantityType = Lists.newArrayList();
        List<TmsDeliveryStatusDescEnum> deliveryQuantityStatuses = Lists.newArrayListWithExpectedSize(3);

        // 查询权限信息并设置权限
        List<String> permissionCodes = queryAuthorizedCodes();
        setPermission(permissionCodes, orderHomePageModuleVO, orderQuantityType, deliveryQuantityStatuses);

        // 设置规格信息
        setAppendSpecToOrderItemWithRpc(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), orderHomePageModuleVO);

        // 根据权限查询订单待办
        if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowOrderTab()) && CollectionUtils.isNotEmpty(orderQuantityType)) {

            // 设置订单基本数量信息
            setOrderPendingTaskVOCountWithRpc(orderPendingTaskVO, homePageRequest, orderQuantityType);
            // 设置异常订单数量
            setDeliveryErrorCountWithRpc(orderHomePageModuleVO, orderPendingTaskVO);
            // 设置待配送订单数量
            setWaitToDeliveryCountWithRpc(orderHomePageModuleVO, orderPendingTaskVO, homePageRequest.getEntityType());

        }
        // 设置退货退款待审核数量
        setWaitToAuditRefundGoodsCountWithRpc(homePageRequest.getEntityType(), orderPendingTaskVO);

        // 组装返回参数
        OrderHomePageResponse orderHomePageResponse = new OrderHomePageResponse();
        orderHomePageResponse.setOrderHomePageModuleVO(orderHomePageModuleVO);
        orderHomePageResponse.setOrderPendingTaskVO(orderPendingTaskVO);

        return CommonResponse.success(orderHomePageResponse);
    }

    /**
     * 分页查询全部订单列表-多门店.
     *
     * @param request 全部订单查询请求
     * @return 订单列表
     */
    @Degrade(rhinoKey = "MiniAppOrderService.orderList",
            fallBackMethod = "orderListFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<OrderListResponse> orderList(OrderListMiniAppRequest request) {
        try {
            // 请求 orderMng 根据多限制条件分页查询订单
            OcmsOrderListReq ocmsOrderListReq = buildOcmsOrderListReq(request);
            log.info("MiniAppOrderService.orderList call OcmsOrderSearchThriftServiceV2.orderList request:{}", ocmsOrderListReq);
            OcmsOrderSearchResponseV2 response = orderSearchThriftServiceV2.orderList(ocmsOrderListReq);
            log.info("MiniAppOrderService.orderList call OcmsOrderSearchThriftServiceV2.orderList response:{}", response);
            Integer responseStatus = Optional.ofNullable(response).map(OcmsOrderSearchResponseV2::getResponseStatus).orElse(-1);
            if (responseStatus != 0) {
                log.warn("MiniAppOrderService.orderList call OcmsOrderSearchThriftServiceV2.orderList fail. request:{}, response:{}",
                        ocmsOrderListReq, response);
                return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(),
                        Optional.ofNullable(response).map(OcmsOrderSearchResponseV2::getMsg).orElse("系统异常，请稍后重试"));
            }
            PageInfoVo page = response.getPage();
            PageInfoVO pageInfoVO = buildPageInfoVO(page.getPage(), page.getSize(), page.getTotalSize());
            List<OCMSOrderVO> ocmsOrderVOS = Optional.of(response).map(OcmsOrderSearchResponseV2::getOcmsOrderList).orElse(Collections.emptyList());

            if (CollectionUtils.isEmpty(ocmsOrderVOS)) {
                OrderListResponse orderListResponse = buildOrderListResponse(Collections.emptyList(), Collections.emptyList(),
                        Collections.emptyMap(), Collections.emptyList(), pageInfoVO);
                return CommonResponse.success(orderListResponse);
            }

            Map<String, Boolean> permissions = authThriftWrapper.isHasPermission(ImmutableList.of(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode(),
                    AuthCodeEnum.MALT_FARM.getAuthCode()));
            // 查询打印结果
            OrderPrintResultQueryResponse orderPrintResponse = queryOrderPrintResult(request.getStoreIds(), ocmsOrderListReq.getTenantId(),
                    Optional.ofNullable(response.getOcmsOrderList()).map(List::stream)
                            .orElse(Stream.empty())
                            .map(e -> new OrderIdentifierDTO(e.getViewOrderId(), e.getOrderBizType()))
                            .collect(Collectors.toList()));
            // 查询订单营收
            boolean showSalePrice = permissions.getOrDefault(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode(), Boolean.FALSE);
            List<OrderRevenueDetailResponse> orderRevenueDetailList = showSalePrice ?
                    getOrderListRevenueDetail4TenantAndViewIds(ocmsOrderListReq.getTenantId(), response.getOcmsOrderList()) :
                    Collections.emptyList();
            Map<String, SaasCrmDataRemoteService.OrderProfitView> orderProfitMap = showSalePrice ?
                    getOrderProfitMap(ocmsOrderListReq.getTenantId(), response.getOcmsOrderList()) : Collections.emptyMap();
            // 设置退款展示类型
            setRefundPriceDisplayType4OrderList(response.getOcmsOrderList(), showSalePrice ?
                    PriceDisplayType.REFUND_AMOUNT.getCode() : PriceDisplayType.OFFLINE_PRICE.getCode());
            // 构造响应结果
            OrderListResponse orderListResponse = buildOrderListResponse(response.getOcmsOrderList(),
                    orderPrintResponse.getPrintResultList(), orderProfitMap, orderRevenueDetailList, pageInfoVO);
            // 针对全部订单列表，订单状态描述做特殊处理
            fixOrdersStatusDesc(orderListResponse);
            // 设置Erp 门店信息
            setErpCode(orderListResponse);
            ocmsOrderServiceWrapper.buildDeliveryCount(orderListResponse);
            return CommonResponse.success(orderListResponse);
        } catch (Exception e) {
            log.warn("MiniAppOrderService.orderList call ocmsOrderSearchService.orderList error.}", e);
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "系统异常，请稍后重试");
        }
    }

    @Degrade(rhinoKey = "MiniAppOrderService.queryWaitToPickOrder",
        fallBackMethod = "queryWaitToPickOrderFallback",
        timeoutInMilliseconds = 5000)
    public CommonResponse<OrderListResponse> queryWaitToPickOrder(QueryWaitToPickStoresOrderRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        if (CollectionUtils.isEmpty(storeIdList)) {
            log.error("There are no stores/warehouses in the request  request:{}", request);
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "请选择门店");
        }
        PageInfoVO pageInfoVO;
        OCMSListOrderResponse ocmsListOrderResponse;
        try {
            //请求拣货获取订单号列表
            OCMSListOrderRequest ocmsListOrderRequest = buildWaitToPickOCMSListOrderRequest(request.getEntityType(), request.getPage(), request.getSize());
            log.info("MiniAppOrderService.queryWaitToPickOrder  ocmsQueryThriftService.listOrder request:{}", ocmsListOrderRequest);
            ocmsListOrderResponse = ocmsQueryThriftService.listOrder(ocmsListOrderRequest);
            log.info("MiniAppOrderService.queryWaitToPickOrder  ocmsQueryThriftService.listOrder response:{}", ocmsListOrderResponse);
            if (ocmsListOrderResponse.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), ocmsListOrderResponse.getStatus().getMessage());
            }
            pageInfoVO = buildPageInfoVO(request.getPage(), request.getSize(), ocmsListOrderResponse.getTotalCount());
            if (CollectionUtils.isEmpty(ocmsListOrderResponse.getOcmsOrderList())) {
                return CommonResponse.success(buildOrderListResponse(Lists.newArrayList(), pageInfoVO));
            }
        } catch (Exception e) {
            log.error("MiniAppOrderService.queryWaitToPickOrder 调用ocmsQueryThriftService.listOrder error", e);
            throw new CommonRuntimeException(e);
        }
        //请求mng获取订单详情
        try {
            OCMSListViewIdConditionRequest viewIdConditionRequest = buildOCMSListViewIdConditionRequestByOCMSOrderVOList(ocmsListOrderResponse.getOcmsOrderList());
            OCMSListViewIdConditionResponse viewIdConditionResponse = ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
            if (viewIdConditionResponse.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
                log.info("获取订单详情失败,viewOrderId:{}", viewIdConditionRequest.getViewIdConditionList().stream().map(
                    ViewIdCondition::getViewOrderId).collect(
                    Collectors.toList()));
                return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), viewIdConditionResponse.getStatus().getMessage());
            }
            //过滤es延迟导致的参数不合法订单
            viewIdConditionResponse.setOcmsOrderList(filterInvalidOrder(viewIdConditionResponse.getOcmsOrderList()));
            checkViewResponseCount(viewIdConditionRequest, viewIdConditionResponse);
            //过滤掉没有接单的订单、已接单的订单才展示在待拣货列表
            List<OCMSOrderVO> orders = Optional.ofNullable(viewIdConditionResponse.getOcmsOrderList()).orElse(
                    Collections.emptyList())
                .stream().filter(order -> order.getOrderStatus() >= OrderStatusEnum.MERCHANT_CONFIRMED.getValue()).collect(Collectors.toList());
            viewIdConditionResponse.setOcmsOrderList(orders);
            Map<String, Boolean> permissions = authThriftWrapper.isHasPermission(
                ImmutableList.of(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode(),
                    AuthCodeEnum.MALT_FARM.getAuthCode()));
            //查询打印结果
            OrderPrintResultQueryResponse orderPrintResultQueryResponse = queryOrderPrintResult(storeIdList, tenantId,
                Optional.ofNullable(viewIdConditionResponse.getOcmsOrderList()).map(List::stream).orElse(Stream.empty())
                    .map(e -> new OrderIdentifierDTO(e.getViewOrderId(), e.getOrderBizType()))
                    .collect(Collectors.toList()));
            boolean showSalePrice = permissions.getOrDefault(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode(), Boolean.FALSE);
            // 查询订单营收
            List<OrderRevenueDetailResponse> orderRevenueDetailList = showSalePrice ?
                getOrderListRevenueDetail4TenantAndViewIds(
                    tenantId, viewIdConditionResponse.getOcmsOrderList()) : Collections.emptyList();
            Map<String, SaasCrmDataRemoteService.OrderProfitView> orderProfitMap = showSalePrice ?
                getOrderProfitMap(tenantId, viewIdConditionResponse.getOcmsOrderList()) : Collections.emptyMap();
            // 设置退款展示类型
            setRefundPriceDisplayType4OrderList(viewIdConditionResponse.getOcmsOrderList(), showSalePrice ?
                PriceDisplayType.REFUND_AMOUNT.getCode() : PriceDisplayType.OFFLINE_PRICE.getCode());

            OrderListResponse orderListResponse = buildOrderListResponse(viewIdConditionResponse.getOcmsOrderList(),
                    orderPrintResultQueryResponse.getPrintResultList(), orderProfitMap, orderRevenueDetailList, pageInfoVO);
            //设置订单展示状态
            setOrderListResponseViewStatus(orderListResponse);
            //设置门店erp_code
            setErpCode(orderListResponse);
            return CommonResponse.success(orderListResponse);
        } catch (Exception e) {
            log.error("MiniAppOrderService.queryWaitToPickOrder 调用ocmsQueryThriftService.queryOrderByViewIdCondition error", e);
            throw new CommonRuntimeException(e);
        }
    }

    @Degrade(rhinoKey = "MiniAppOrderService.queryDeliveryErrorBySubType",
            fallBackMethod = "queryDeliveryErrorBySubTypeFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<OrderListResponse> queryDeliveryErrorBySubType(QueryDeliveryErrorOrderBySubTypeMiniAppRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeAndWarehouseIdList = identityInfo.getStoreAndWarehouseIdList();
        request.setStoreIds(storeAndWarehouseIdList);
        long tenantId = identityInfo.getUser().getTenantId();
        if (CollectionUtils.isEmpty(storeAndWarehouseIdList)) {
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "请选择门店");
        }

        PageInfoVO pageInfoVO;
        List<TOrderIdentifier> tOrderIdentifiers;

        // 请求 TMS 系统获取订单号列表
        try {
            DeliveryExceptionOrdersBySubTypeAndStoreIdsResponse deliveryExceptionResponse = tmsServiceWrapper
                    .queryDeliveryErrorOrdersBySubTypeAndStores(request.getPage(), request.getSize(), request.getSubType(), storeAndWarehouseIdList);
            tOrderIdentifiers = deliveryExceptionResponse.getOrders();
            TPageInfo tPageInfo = deliveryExceptionResponse.getTPageInfo();
            // 下游支持的是全量查询
            pageInfoVO = buildPageInfoVO(tPageInfo.getPageNum(), tPageInfo.getPageSize(), tPageInfo.getTotal());
            if (CollectionUtils.isEmpty(tOrderIdentifiers)) {
                return CommonResponse.success(buildOrderListResponse(Lists.newArrayList(), pageInfoVO));
            }
        } catch (Exception e) {
            log.error("MiniAppOrderService.queryDeliveryErrorBySubType 调用 tmsServiceWrapper.queryDeliveryErrorOrdersBySubTypeAndStores error", e);
            throw new CommonRuntimeException(e);
        }

        // 请求 orderMng 获取订单详情
        try {
            OCMSListViewIdConditionRequest viewIdConditionRequest = buildOCMSListViewIdConditionRequestByTOrderIdentifier(tOrderIdentifiers);
            log.info("MiniAppOrderService.queryDeliveryErrorBySubType  ocmsQueryThriftService.queryOrderByViewIdCondition request:{}",
                    JSONObject.toJSONString(viewIdConditionRequest));
            OCMSListViewIdConditionResponse viewIdConditionResponse = ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
            log.info("MiniAppOrderService.queryDeliveryErrorBySubType  ocmsQueryThriftService.queryOrderByViewIdCondition response:{}",
                    JSONObject.toJSONString(viewIdConditionResponse, SerializerFeature.WriteMapNullValue));
            if (viewIdConditionResponse.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
                log.info("获取订单详情失败, viewOrderId:{}", viewIdConditionRequest.getViewIdConditionList().stream()
                        .map(ViewIdCondition::getViewOrderId)
                        .collect(Collectors.toList()));
                return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), viewIdConditionResponse.getStatus().getMessage());
            }
            // 检查目标订单数和返回订单数是否相等，若不等则埋点
            checkViewResponseCount(viewIdConditionRequest, viewIdConditionResponse);
            Map<String, Boolean> permissions = authThriftWrapper.isHasPermission(ImmutableList.of(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode(),
                    AuthCodeEnum.MALT_FARM.getAuthCode()));
            // 查询打印结果
            OrderPrintResultQueryResponse orderPrintResultQueryResponse = queryOrderPrintResult(storeAndWarehouseIdList, tenantId,
                    Optional.ofNullable(viewIdConditionResponse.getOcmsOrderList()).map(List::stream).orElse(Stream.empty())
                            .map(e -> new OrderIdentifierDTO(e.getViewOrderId(), e.getOrderBizType()))
                            .collect(Collectors.toList()));

            boolean showSalePrice = permissions.getOrDefault(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode(), Boolean.FALSE);

            // 查询订单营收
            List<OrderRevenueDetailResponse> orderRevenueDetailList = showSalePrice ?
                    getOrderListRevenueDetail4TenantAndViewIds(tenantId, viewIdConditionResponse.getOcmsOrderList()) :
                    Collections.emptyList();

            Map<String, SaasCrmDataRemoteService.OrderProfitView> orderProfitMap = showSalePrice ?
                    getOrderProfitMap(tenantId, viewIdConditionResponse.getOcmsOrderList()) : Collections.emptyMap();
            // 设置退款展示类型
            setRefundPriceDisplayType4OrderList(viewIdConditionResponse.getOcmsOrderList(), showSalePrice ?
                    PriceDisplayType.REFUND_AMOUNT.getCode() : PriceDisplayType.OFFLINE_PRICE.getCode());
            List<OrderPrintResultDto> printResultList = orderPrintResultQueryResponse.getPrintResultList();
            OrderListResponse orderListResponse = buildOrderListResponse(viewIdConditionResponse.getOcmsOrderList(), printResultList, orderProfitMap,
                    orderRevenueDetailList, pageInfoVO);
            setDeliveryErrorListSort(orderListResponse);
            setOrderListResponseViewStatusByExceptionSubType(orderListResponse, tOrderIdentifiers);
            // 设置Erp 门店信息
            setErpCode(orderListResponse);
            justKeepPartOperateItems(orderListResponse, Lists.newArrayList(OrderCouldOperateItem.FULL_ORDER_REFUND));
            return CommonResponse.success(orderListResponse);
        } catch (Exception e) {
            log.error("MiniAppOrderService.queryDeliveryErrorBySubType 调用 ocmsQueryThriftService.queryOrderByViewIdCondition error", e);
            throw new CommonRuntimeException(e);
        }
    }

    @Degrade(rhinoKey = "MiniAppOrderService.queryDeliveryErrorSubTypeCount",
            fallBackMethod = "queryDeliveryErrorSubTypeCountFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<DeliveryErrorSubTypeCountResponse> queryDeliveryErrorSubTypeCount() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeAndWarehouseIdList = identityInfo.getStoreAndWarehouseIdList();
        if (CollectionUtils.isEmpty(storeAndWarehouseIdList)) {
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "请选择门店");
        }

        try {
            DeliveryExceptionOrderSubTypeCountResponse response = tmsServiceWrapper.queryDeliveryExceptionOrderSubTypeAndStoresCount(storeAndWarehouseIdList);
            if (response.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), response.getStatus().getMsg());
            }
            return CommonResponse.success(buildDeliveryErrorSubTypeCountResponse(response));
        } catch (Exception e) {
            log.error("MiniAppOrderService.queryDeliveryErrorSubTypeCount 调用 tmsServiceWrapper.queryDeliveryExceptionOrderSubTypeAndStoresCount error", e);
            throw new CommonRuntimeException(e);
        }
    }

    /**
     * 验证多门店数量是否大于50
     * @return
     */
    private boolean validate() {
        int maxStoreSize = MccConfigUtil.getMiniAppOrderMaxStoreSize();
        return ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList().size() > maxStoreSize;
    }

    private OCMSListOrderRequest buildOCMSListOrderRequestByBizMode(Long tenantId, Integer entityType) {
        TenantBusinessModeEnum tenantBizMode = tenantWrapper.getTenantBizMode(tenantId);
        if (TenantBusinessModeEnum.CONVENIENCE_STORE.equals(tenantBizMode)) {
            return buildWaitToDeliveryOCMSListOrderRequest(entityType);
        } else {
            QueryWaitToDeliveryOrderRequest queryWaitToDeliveryOrderRequest = new QueryWaitToDeliveryOrderRequest();
            queryWaitToDeliveryOrderRequest.setPage(MiniAppConstants.ORDER_COUNT_QUERY_PAGE);
            queryWaitToDeliveryOrderRequest.setSize(MiniAppConstants.ORDER_COUNT_QUERY_SIZE);
            queryWaitToDeliveryOrderRequest.setEntityType(entityType);
            return buildOCMSListOrderRequest(queryWaitToDeliveryOrderRequest);
        }
    }

    private OCMSListOrderRequest buildOCMSListOrderRequest(QueryWaitToDeliveryOrderRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        Integer entityType = request.getEntityType();

        OCMSListOrderRequest ocmsListOrderRequest = buildOCMSListOrderRequestBasic(identityInfo.getUser().getTenantId(),
                                                                                   storeIdList, request.getPage(), request.getSize(), entityType);
        // 拣货已完成，且等待分配骑手
        ocmsListOrderRequest
                .setOrderStatusList(Lists.newArrayList(OrderStatusEnum.PICKING.getValue(),
                        OrderStatusEnum.REFUND_APPLIED.getValue(), OrderStatusEnum.APPEAL_APPLIED.getValue()));
        // 已拣货
        ocmsListOrderRequest
                .setPickStatusList(Lists.newArrayList(DeliveryStatusEnum.PICKED.getValue()));
        // 未配送
        ocmsListOrderRequest.setDeliveryStatusList(Lists.newArrayList(
                DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER.getValue(),
                DistributeStatusEnum.DISTRIBUTE_REJECTED.getValue(), DistributeStatusEnum.RIDER_ASSIGNED.getValue(),
                DistributeStatusEnum.RIDER_REACH_SHOP.getValue(),
                DistributeStatusEnum.RIDER_TAKE_GOODS_FAILED.getValue(),
                DistributeStatusEnum.DISTRIBUTE_CANCELED.getValue(), DistributeStatusEnum.UN_KNOWN.getValue(),
                DistributeStatusEnum.DISTRIBUTE_UNKNOWN.getValue()));
        // 订单未完成过，已完成的订单也能发起退款，如果不指定该条件，可能会查出该类订单
        ocmsListOrderRequest.setEndCompletedTime(MiniAppConstants.MIN_VALID_TIMESTAMP);
        ocmsListOrderRequest.setSortField(SortFieldEnum.ESTIMATED_ARRIVAL_TIME);
        ocmsListOrderRequest.setSort(SortByEnum.ASC);
        // 配送到家
        ocmsListOrderRequest.setDistributeMethodList(ImmutableList.of(DistributeMethodEnum.HOME_DELIVERY.getValue()));
        return ocmsListOrderRequest;
    }

    private OCMSListOrderRequest buildOCMSListOrderRequestBasic(Long tenantId, List<Long> storeIdList, Integer page,
                                                                Integer size, Integer entityType) {
        OCMSListOrderRequest ocmsListOrderRequest = new OCMSListOrderRequest();
        ocmsListOrderRequest.setTenantId(tenantId);
        ocmsListOrderRequest.setShopIdList(storeIdList);
        if (Objects.equals(PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code(), entityType)) {
            ocmsListOrderRequest.setShopIdList(Lists.newArrayList());
            ocmsListOrderRequest.setWarehouseIdList(storeIdList);
        }

        // 查询当前时间之前7天的订单
        Integer intValue = MccConfigUtil.getMiniAppOrderListBeginTime();

        ocmsListOrderRequest.setBeginCreateTime(TimeUtils.getBeforeDayTimeStamp(intValue));
        ocmsListOrderRequest.setEndCreateTime(System.currentTimeMillis());
        ocmsListOrderRequest.setPage(page);
        ocmsListOrderRequest.setSize(size);
        return ocmsListOrderRequest;
    }

    private OCMSListOrderRequest buildWaitToDeliveryOCMSListOrderRequest(Integer entityType) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        OCMSListOrderRequest ocmsListOrderRequest = buildOCMSListOrderRequestBasic(identityInfo.getUser().getTenantId(),
                storeIdList, MiniAppConstants.ORDER_COUNT_QUERY_PAGE, MiniAppConstants.ORDER_COUNT_QUERY_SIZE,
                entityType);
        // 拣货已完成，且等待分配骑手 查询退款处理中的订单
        ocmsListOrderRequest.setOrderStatusList(Lists.newArrayList(OrderStatusEnum.PICKING.getValue(),
                OrderStatusEnum.REFUND_APPLIED.getValue(), OrderStatusEnum.APPEAL_APPLIED.getValue()));
        // 已拣货
        ocmsListOrderRequest.setPickStatusList(Lists.newArrayList(DeliveryStatusEnum.PICKED.getValue()));
        // 未配送
        ocmsListOrderRequest.setDeliveryStatusList(Lists.newArrayList(
                DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER.getValue(),
                DistributeStatusEnum.DISTRIBUTE_REJECTED.getValue(), DistributeStatusEnum.RIDER_ASSIGNED.getValue(),
                DistributeStatusEnum.RIDER_REACH_SHOP.getValue(),
                DistributeStatusEnum.RIDER_TAKE_GOODS_FAILED.getValue(),
                DistributeStatusEnum.DISTRIBUTE_CANCELED.getValue(), DistributeStatusEnum.UN_KNOWN.getValue(),
                DistributeStatusEnum.DISTRIBUTE_UNKNOWN.getValue(), DistributeStatusEnum.RIDER_TAKE_GOODS.getValue()));
        // 订单未完成过，已完成的订单也能发起退款，如果不指定该条件，可能会查出该类订单
        ocmsListOrderRequest.setEndCompletedTime(MiniAppConstants.MIN_VALID_TIMESTAMP);
        ocmsListOrderRequest.setSortField(SortFieldEnum.ESTIMATED_ARRIVAL_TIME);
        ocmsListOrderRequest.setSort(SortByEnum.ASC);
        // 配送到家
        ocmsListOrderRequest.setDistributeMethodList(ImmutableList.of(DistributeMethodEnum.HOME_DELIVERY.getValue()));
        return ocmsListOrderRequest;
    }

    private OCMSListOrderRequest buildWaitToPickOCMSListOrderRequest(Integer entityType, Integer page, Integer size) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        OCMSListOrderRequest ocmsListOrderRequest = buildOCMSListOrderRequestBasic(identityInfo.getUser().getTenantId(),
            storeIdList, page, size, entityType);
        // 只查询订单聚合状态，其中拣货过程中发生退款/拣货完成的订单也不展示。
        ocmsListOrderRequest.setFuseOrderStatusList(Lists.newArrayList(FuseOrderStatusEnum.MERCHANT_CONFIRMED.getValue(), FuseOrderStatusEnum.SHIPPING.getValue(), FuseOrderStatusEnum.WAIT_SELF_FETCH.getValue()));
        // 待自提的订单中也可能存在待拣货状态的，因此还是需要在pickStatus和orderStatus上进行约束
        ocmsListOrderRequest.setOrderStatusList(Lists.newArrayList(OrderStatusEnum.PICKING.getValue()));
        ocmsListOrderRequest.setPickStatusList(Lists.newArrayList(DeliveryStatusEnum.PICKING.getValue()));
        ocmsListOrderRequest.setEndCompletedTime(MiniAppConstants.MIN_VALID_TIMESTAMP);
        ocmsListOrderRequest.setSortField(SortFieldEnum.ESTIMATED_ARRIVAL_TIME);
        ocmsListOrderRequest.setSort(SortByEnum.ASC);
        return ocmsListOrderRequest;
    }

    private List<String> queryAuthorizedCodes() {
        // 这个枚举，按照备注的替换方案不全。不进行替换
        List<String> authCodeList = Arrays.asList(PermissionCodeEnum.MINI_APP_ORDER_SEARCH.getCode(),
                                                  PermissionCodeEnum.MINI_APP_ORDER_SUB_TAB.getCode(),
                                                  PermissionCodeEnum.MINI_APP_COMMENT_MANAGEMENT.getCode());
        return authThriftWrapper.queryAuthorizedCodes(authCodeList);
    }

    private void setOrderPendingTaskVOCountWithRpc(OrderPendingTaskVO orderPendingTaskVO, HomePageRequest homePageRequest, List<Integer> orderQuantityType) {

        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        List<Long> storeIdList = identityInfo.getStoreIdList();

        // 请求订单数据源
        OCMSQueryOrderQuantityRequest request = new OCMSQueryOrderQuantityRequest();
        request.setTenantId(tenantId);
        request.setShopIdList(storeIdList);
        request.setOrderBizTypeList(ChannelOrderConverter.ALL_BIZ_TYPE_LIST);
        request.setOrderQuantityTypeList(orderQuantityType);
        // 查询当前时间之前7天的订单
        Integer intValue = MccConfigUtil.getMiniAppOrderListBeginTime();
        request.setBeginCreateTime(TimeUtils.getBeforeDayTimeStamp(intValue));
        request.setEndCreateTime(System.currentTimeMillis());
        if (Objects.equals(homePageRequest.getEntityType(), PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code())) {
            request.setShopIdList(new ArrayList<>());
            request.setWarehouseIdList(storeIdList);
        }

        OCMSQueryOrderQuantityResponse orderQuantityResponse = ocmsQueryThriftService.queryOrderQuantity(request);

        if (orderQuantityResponse.getStatus() != null
                && StatusCodeEnum.SUCCESS.getCode() == orderQuantityResponse.getStatus().getCode()
                && MapUtils.isNotEmpty(orderQuantityResponse.getOrderTypeQuantityMap())) {
            // 设置代办数量
            orderPendingTaskVO
                    .setWaitToTakeOrderCount(MapUtils.getInteger(orderQuantityResponse.getOrderTypeQuantityMap(),
                            QueryOrderTypeQuantityEnum.WAIT_TAKE_ORDER.getValue(), 0));
            orderPendingTaskVO
                    .setWaitToAuditRefundCount(MapUtils.getInteger(orderQuantityResponse.getOrderTypeQuantityMap(),
                            QueryOrderTypeQuantityEnum.WAIT_REFUND_AUDIT.getValue(), 0));
            orderPendingTaskVO
                    .setWaitToAuditRefundGoodsCount(MapUtils.getInteger(orderQuantityResponse.getOrderTypeQuantityMap(),
                            QueryOrderTypeQuantityEnum.WAIT_REFUND_GOODS_AUDIT.getValue(), 0));
            orderPendingTaskVO
                    .setWaitToSelfFetchCount(MapUtils.getInteger(orderQuantityResponse.getOrderTypeQuantityMap(),
                            QueryOrderTypeQuantityEnum.WAIT_SELF_FETCH.getValue(), 0));
            // 待拣货从订单列表获取 - 不再查询拣货接口
            OCMSListOrderRequest ocmsListOrderRequest = buildWaitToPickOCMSListOrderRequest(homePageRequest.getEntityType(), 1, 1);
            OCMSListOrderResponse ocmsListOrderResponse = ocmsQueryThriftService.listOrder(ocmsListOrderRequest);
            if (ocmsListOrderResponse.getStatus().getCode() == com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
                orderPendingTaskVO.setWaitToPickCount(Objects.nonNull(ocmsListOrderResponse.getTotalCount())
                        ? ocmsListOrderResponse.getTotalCount() : 0);
            }
        }
    }

    private void setAppendSpecToOrderItemWithRpc(long tenantId, OrderHomePageModuleVO orderHomePageModuleVO) {
        Map<String, String> tenantSwitchMap = tenantWrapper.queryTenantSwitch(tenantId,
                                                                              Collections.singletonList(CommonConstants.TenantSwitchKey.APPEND_SPEC_TO_ORDER_ITEM));

        Integer appendSpecToOrderItem = "1".equals(tenantSwitchMap.get(CommonConstants.TenantSwitchKey.APPEND_SPEC_TO_ORDER_ITEM))
                        ? IntegerBooleanConstants.BOOLEAN_TRUE : IntegerBooleanConstants.BOOLEAN_FALSE;
        orderHomePageModuleVO.setAppendSpecToOrderItem(appendSpecToOrderItem);
    }

    private void setPermission(List<String> permissionCodes, OrderHomePageModuleVO orderHomePageModuleVO,
            List<Integer> orderQuantityType, List<TmsDeliveryStatusDescEnum> deliveryQuantityStatuses) {

        // 小程序目前只用到三个权限：see PermissionCodeEnum
        for (String permissionCode : permissionCodes) {
            if (StringUtils.equals(permissionCode, PermissionCodeEnum.MINI_APP_ORDER_SUB_TAB.getCode())) {
                orderHomePageModuleVO.setShowOrderTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                orderHomePageModuleVO.setShowWaitToTakeOrderTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                orderHomePageModuleVO.setShowWaitToPickTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                orderHomePageModuleVO.setShowWaitToSelfFetchTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                orderHomePageModuleVO.setShowWaitToDeliveryTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                orderHomePageModuleVO.setShowDeliveryErrorTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                orderHomePageModuleVO.setShowWaitToAuditRefundTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                orderHomePageModuleVO.setShowWaitToAuditAfterSaleTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                orderHomePageModuleVO.setShowOrderTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                orderHomePageModuleVO.setShowPickTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                orderHomePageModuleVO.setShowRiderGetOrderTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                orderHomePageModuleVO.setShowRiderTakeGoodsTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                orderHomePageModuleVO.setShowRiderInDeliveryTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                orderHomePageModuleVO.setShowRiderCompletedTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                //赋值全订单tab
                Arrays.stream(QueryOrderTypeQuantityEnum.values()).map(QueryOrderTypeQuantityEnum::getValue).forEach(orderQuantityType::add);
            }
            if (StringUtils.equals(permissionCode, PermissionCodeEnum.MINI_APP_ORDER_SEARCH.getCode())) {
                orderHomePageModuleVO.setShowOrderSearch(IntegerBooleanConstants.BOOLEAN_TRUE);
            }
            if (StringUtils.equals(permissionCode, PermissionCodeEnum.MINI_APP_COMMENT_MANAGEMENT.getCode())) {
                orderHomePageModuleVO.setShowCommitManagementTab(IntegerBooleanConstants.BOOLEAN_TRUE);
            }
        }
        //小程序默认展示
        deliveryQuantityStatuses.add(TmsDeliveryStatusDescEnum.WAITING_TO_ASSIGN_RIDER);
        deliveryQuantityStatuses.add(TmsDeliveryStatusDescEnum.RIDER_ASSIGNED);
        deliveryQuantityStatuses.add(TmsDeliveryStatusDescEnum.RIDER_TAKEN_GOODS);
    }

    private void setWaitToAuditRefundGoodsCountWithRpc(Integer entityType, OrderPendingTaskVO orderPendingTaskVO) {
        try {
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            CommonResponse<RefundGoodsSubTypeCountResponse> afterSaleResponse = ocmsOrderServiceWrapper
                    .queryWaitAuditRefundGoodsBySubtypeCount(identityInfo.getUser().getTenantId(),
                            identityInfo.getStoreIdList(), entityType);

            if (afterSaleResponse != null && afterSaleResponse.getData() != null) {
                Integer allSubTypeCount = afterSaleResponse.getData().getAllSubTypeCount();
                orderPendingTaskVO.setWaitToAuditRefundGoodsCount(allSubTypeCount == null ? 0 : allSubTypeCount);
            }
        } catch (Exception e) {
            log.error("MiniApp homePage setWaitToAuditRefundGoodsCountWithRpc error", e);
        }
    }

    private void setWaitToDeliveryCountWithRpc(OrderHomePageModuleVO orderHomePageModuleVO,
            OrderPendingTaskVO orderPendingTaskVO, Integer entityType) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowWaitToDeliveryTab())) {
            OCMSListOrderRequest ocmsListOrderRequest = buildOCMSListOrderRequestByBizMode(tenantId, entityType);
            try {
                log.info("MiniAppOCMSOrderServiceWrapper.homePage  调用ocmsQueryThriftService.listOrder request:{}",
                        ocmsListOrderRequest);
                OCMSListOrderResponse ocmsListOrderResponse = ocmsQueryThriftService.listOrder(ocmsListOrderRequest);

                log.info("MiniAppOCMSOrderServiceWrapper.homePage  调用ocmsQueryThriftService.listOrder response:{}",
                        ocmsListOrderResponse);
                if (ocmsListOrderResponse.getStatus().getCode() == com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
                    orderPendingTaskVO.setWaitToDeliveryCount(ocmsListOrderResponse.getTotalCount());
                }
            } catch (Exception e) {
                log.error("MiniAppOCMSOrderServiceWrapper.homePage  调用ocmsQueryThriftService.listOrder error", e);
            }
        }
    }

    private void setDeliveryErrorCountWithRpc(OrderHomePageModuleVO orderHomePageModuleVO, OrderPendingTaskVO orderPendingTaskVO) {
        if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowDeliveryErrorTab())) {
            Integer deliveryErrorOrderNum;
            try {
                IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
                deliveryErrorOrderNum = tmsServiceWrapper.countDeliveryExceptionOrderByStoreIds(identityInfo.getStoreIdList());
                orderPendingTaskVO.setDeliveryErrorCount(deliveryErrorOrderNum);
            } catch (Exception e) {
                log.error("MiniAppOCMSOrderServiceWrapper.homePage  tmsServiceWrapper.countDeliveryExceptionOrderByStoreIds error", e);
            }
        }
    }

    private OcmsOrderListReq buildOcmsOrderListReq(OrderListMiniAppRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OcmsOrderListReq tRequest = new OcmsOrderListReq();
        tRequest.setTenantId(user.getTenantId());
        if (Objects.isNull(request.getEntityType()) || request.getEntityType() == PoiEntityTypeEnum.STORE.code()) {
            tRequest.setShopIdList(request.getStoreIds());
        } else if (request.getEntityType() == PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code()) {
            tRequest.setWarehouseIdList(request.getStoreIds());
        }
        tRequest.setOperator(user.getAccountId());
        // 订单创建时间范围
        tRequest.setBeginCreateTime(LocalDate.parse(request.getBeginCreateDate()).atStartOfDay(ZoneId.systemDefault())
                .toInstant().toEpochMilli());
        tRequest.setEndCreateTime(LocalDate.parse(request.getEndCreateDate()).plusDays(1L).atStartOfDay(ZoneId.systemDefault())
                .toInstant().toEpochMilli());
        // 订单渠道范围
        tRequest.setChannelIdList(request.getChannelIdList());
        // 配送订单类型
        if (Objects.nonNull(request.getDeliveryOrderType())) {
            tRequest.setDeliveryOrderType(request.getDeliveryOrderType());
        }
        // 聚合订单状态
        tRequest.setAggregationOrderStatus(request.getAggregationOrderStatus());

        // 设置搜索关键词
        tRequest.setSmartQuery(request.getKeyword());

        tRequest.setPage(request.getPage());
        tRequest.setPageSize(request.getPageSize());
        // 和deliveryOrderType互斥 如果同时传递，会取deliveryOrderType字段的数据查询
        if (Objects.isNull(request.getDeliveryOrderType()) && CollectionUtils.isNotEmpty(request.getDeliveryOrderTypeList())) {
            tRequest.setDeliveryOrderTypeList(request.getDeliveryOrderTypeList());
        }
        if (CollectionUtils.isNotEmpty(request.getDistributeTypeList())) {
            tRequest.setDistributeTypeList(request.getDistributeTypeList());
        }
        // 小程序需要支持聚合订单状态多选、直接在此处处理转换成订单状态查询底层
        if (Objects.isNull(request.getAggregationOrderStatus())
                && CollectionUtils.isNotEmpty(request.getAggregationOrderStatusList())) {
            tRequest.setChannelOrderStatusList(fixAggregationOrderStatus(request.getAggregationOrderStatusList()));
        }
        return tRequest;
    }

    /**
     * 处理聚合订单状态多选
     */
    private List<Integer> fixAggregationOrderStatus(List<Integer> aggregationOrderStatusList) {
        EnumSet<OrderStatusEnum> channelStatusSet = EnumSet.noneOf(OrderStatusEnum.class);
        aggregationOrderStatusList.forEach(aggregationOrderStatus -> {
            if (aggregationOrderStatus != null && AggregationOrderStatus.findByValue(aggregationOrderStatus) != null) {
                AggregationOrderStatus aggregationOrderStatusEnum = AggregationOrderStatus.findByValue(aggregationOrderStatus);
                switch (aggregationOrderStatusEnum) {
                    case CANCELED:
                        channelStatusSet.add(OrderStatusEnum.CANCELED);
                        break;
                    case DONE:
                        channelStatusSet.add(OrderStatusEnum.COMPLETED);
                        break;
                    case ON_DEAL:
                        channelStatusSet.addAll(EnumSet.complementOf(EnumSet.of(OrderStatusEnum.COMPLETED, OrderStatusEnum.CANCELED, OrderStatusEnum.CLOSED)));
                        break;
                    default:
                        log.error("未处理的订单集合状态:{}", aggregationOrderStatus);
                }
            }
        });
        //订单状态转渠道订单状态
        List<Integer> orderStatusList = channelStatusSet.stream().map(OrderStatusEnum::getValue).distinct().collect(Collectors.toList());
        Map<Integer, Integer> orderStatusChannelOrderStatus = OrderStatusConverter.ORDER_STATUS_CHANNEL_ORDER_STATUS;
        List<Integer> channelOrderStatusList = orderStatusList.stream()
                                                              .map(orderStatus -> orderStatusChannelOrderStatus.get(orderStatus))
                                                              .filter(Objects::nonNull)
                                                              .collect(Collectors.toList());
        return channelOrderStatusList;
    }





    private OrderListResponse buildOrderListResponse(List<OCMSOrderVO> ocmsOrderVOList, PageInfoVO pageInfoVO) {
        return buildOrderListResponse(ocmsOrderVOList, Collections.emptyList(), Collections.emptyMap(), Collections.emptyList(), pageInfoVO);
    }

    private OrderListResponse buildOrderListResponse(List<OCMSOrderVO> ocmsOrderVOList, List<OrderPrintResultDto> orderPrintResultDtos,
                                                     Map<String, SaasCrmDataRemoteService.OrderProfitView> orderProfitMap,
                                                     List<OrderRevenueDetailResponse> orderRevenueDetailResponseList,
                                                     PageInfoVO pageInfoVO) {
        OrderListResponse orderListResponse = new OrderListResponse();
        orderListResponse.setPageInfo(pageInfoVO);
        Map<String, OrderRevenueDetailResponse> orderRevenueDetailResponseMap = getOrderRevenueDetailResponseMap(orderRevenueDetailResponseList);
        Map<String, OrderPrintResultDto> printResultDtoMap = Optional.ofNullable(orderPrintResultDtos).map(List::stream)
                .orElse(Stream.empty()).collect(Collectors.toMap(OrderPrintResultDto::getOrderNo, v -> v, (f, s) -> f));

        if (CollectionUtils.isNotEmpty(ocmsOrderVOList)) {
            List<OrderLabelModel> showLabelList = fuseOrderService.queryOrderShowLabel(ocmsOrderVOList.get(0).getTenantId());
            orderListResponse.setOrderList(ocmsOrderVOList.stream()
                    .map(ocmsOrder -> buildOrderVO(ocmsOrder,
                            orderRevenueDetailResponseMap.get(ocmsOrder.getViewOrderId()),
                            orderProfitMap == null ? null : orderProfitMap.get(ocmsOrder.getViewOrderId()), showLabelList))
                    .peek(order -> {
                        //设置打印状态
                        OrderPrintResultDto printResultDto = printResultDtoMap.get(order.getChannelOrderId());
                        if (printResultDto != null) {
                            OrderPrintStatusVo printStatusVo = buildPrintStatusVo(printResultDto);
                            order.setPrintStatus(printStatusVo);
                        }
                    })
                    .collect(Collectors.toList()));
        }
        //添加实时配送信息，和配送操作按钮
        Pair<Map<Long, TDeliveryDetail>, Map<Integer, DeliveryChannelDto>> deliveryDetailMap = appendDeliveryInfoService.appendDeliveryInfo(orderListResponse.getOrderList());
        fillOrderMaltDeliveryPlatModule(orderListResponse.getOrderList(), deliveryDetailMap.getKey(), deliveryDetailMap.getValue());
        fillDeliveryOperateItem(deliveryDetailMap.getKey(), orderListResponse.getOrderList());
        return orderListResponse;
    }

    private Map<String, OrderRevenueDetailResponse> getOrderRevenueDetailResponseMap(List<OrderRevenueDetailResponse> orderRevenueDetailList) {
        return Optional.ofNullable(orderRevenueDetailList)
                .map(List::stream).orElse(Stream.empty())
                .collect(Collectors.toMap(OrderRevenueDetailResponse::getOrderViewId, revenueDetail -> revenueDetail, (before, after) -> before));
    }

    private OrderVO buildOrderVO(OCMSOrderVO ocmsOrderVO, OrderRevenueDetailResponse orderRevenueDetailResponse, SaasCrmDataRemoteService.OrderProfitView orderProfit, List<OrderLabelModel> showLabelList) {
        List<OCMSOrderItemVO> ocmsOrderItemVOList = ocmsOrderVO.getOcmsOrderItemVOList();
        OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrderVO.getOcmsDeliveryInfoVO();
        OrderVO orderVO = buildOrderVO(ocmsOrderVO, showLabelList);
        buildOrderVOOfDeliveryInfo(orderVO, ocmsDeliveryInfoVO, ocmsOrderVO);

        String distributeStatusDesc = Objects.isNull(ocmsDeliveryInfoVO) || Objects.isNull(ocmsDeliveryInfoVO.getDistributeStatus()) || Objects.isNull(DistributeStatusEnum.enumOf(ocmsDeliveryInfoVO.getDistributeStatus()))
                ? "" : DistributeStatusEnum.enumOf(ocmsDeliveryInfoVO.getDistributeStatus()).getDesc();
        orderVO.setDistributeStatusDesc(distributeStatusDesc.equals(MiniAppConstants.UN_KNOW) ? "" : distributeStatusDesc);

        Map<String, OCMSOrderItemVO> orderItemMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVOList)) {
            orderItemMap = ocmsOrderItemVOList.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getInstoreSkuId2()))
                    .collect(Collectors.toMap(OCMSOrderItemVO::getInstoreSkuId2, item -> item, (oldV, newV) -> oldV));
            orderVO.setProductList(ocmsOrderItemVOList.stream()
                    .filter(Objects::nonNull)
                    .map(this::buildProductVO)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
            Integer itemCount = orderVO.getProductList().stream()
                    .filter(Objects::nonNull)
                    .mapToInt(ProductVO::getCount)
                    .sum();
            orderVO.setItemCount(itemCount);
        } else {
            orderVO.setItemCount(0);
            orderVO.setProductList(ocmsOrderItemVOList.stream()
                    .filter(Objects::nonNull)
                    .map(this::buildProductVO)
                    .collect(Collectors.toList()));
        }
        //build refund info
        OrderRefundInfo orderRefundInfo = buildOrderRefundInfo(ocmsOrderVO, orderItemMap);
        if (orderRefundInfo != null) {
            orderVO.setOrderRefundInfo(orderRefundInfo);
        }

        // 订单营收信息
        RevenueDetailVo revenueDetailVo = buildRevenueDetailVo(ocmsOrderVO, orderRevenueDetailResponse, orderProfit);
        if (revenueDetailVo != null) {
            orderVO.setRevenueDetail(revenueDetailVo);
        }
        return orderVO;
    }

    private OrderVO buildOrderVO(OCMSOrderVO ocmsOrderVO, List<OrderLabelModel> showLabelList) {
        OrderVO orderVO = new OrderVO();
        orderVO.setTenantId(ocmsOrderVO.getTenantId());
        orderVO.setIsHasCanRefundGoods(ocmsOrderVO.getIsHasCanRefundGoods());
        Integer channelId = ChannelOrderConvertUtils.sourceBiz2Mid(ocmsOrderVO.getOrderBizType());
        orderVO.setChannelId(channelId);
        if (Objects.nonNull(ChannelTypeEnum.findByChannelId(channelId))) {
            // 订单页展示渠道简称
            orderVO.setChannelName(Optional.ofNullable(ChannelTypeEnum.findByChannelId(channelId))
                    .map(ChannelTypeEnum::getChannelAbbrName)
                    .orElse(null));
        }
        orderVO.setEmpowerOrderId(ocmsOrderVO.getOrderId());
        orderVO.setUserId(ocmsOrderVO.getUserId());
        orderVO.setStoreId(ocmsOrderVO.getShopId());
        orderVO.setStoreName(ocmsOrderVO.getShopName());
        orderVO.setWarehouseId(ocmsOrderVO.getWarehouseId());
        orderVO.setWarehouseName(ocmsOrderVO.getWarehouseName());
        orderVO.setChannelOrderId(ocmsOrderVO.getViewOrderId());
        orderVO.setSerialNo(ocmsOrderVO.getOrderSerialNumber());
        orderVO.setSerialNoStr(ocmsOrderVO.getOrderSerialNumberStr());
        orderVO.setOrderSource(ocmsOrderVO.getOrderSource());
        // 支付时间
        orderVO.setPayTime(ocmsOrderVO.getPayTime() != null ? ocmsOrderVO.getPayTime() : ocmsOrderVO.getCreateTime());
        //商品数量为所有商品数量之和

        orderVO.setActualPayAmt(ocmsOrderVO.getActualPayAmt());
        orderVO.setBizReceiveAmt(ocmsOrderVO.getMerchantAmount());
        orderVO.setOrderStatus(ocmsOrderVO.getOrderStatus());
        OrderStatusEnum orderStatus = OrderStatusEnum.enumOf(ocmsOrderVO.getOrderStatus());
        orderVO.setOrderStatusDesc(orderStatus == null? "未知状态": orderStatus.getDesc());
        orderVO.setCreateTime(ocmsOrderVO.getCreateTime());
        orderVO.setDeliveryOrderType(Integer.valueOf(1).equals(ocmsOrderVO.getIsBooking()) ? DeliveryOrderType.DELIVERY_BY_BOOK_TIME.getValue() : DeliveryOrderType.DELIVERY_RIGHT_NOW.getValue());
        orderVO.setDeliveryOrderTypeName(getDeliveryOrderTypeName(orderVO.getDeliveryOrderType()));
        orderVO.setUpdateTime(ocmsOrderVO.getUpdateTime());
        orderVO.setChannelExtraOrderId(ocmsOrderVO.getExtOrderId());
        //备注不为空且不为0才展示
        if (StringUtils.isNotEmpty(ocmsOrderVO.getComments()) && !"0".equals(ocmsOrderVO.getComments())) {
            orderVO.setComments(ocmsOrderVO.getComments());
        }
        orderVO.setTotalOfflinePrice(ocmsOrderVO.getTotalOfflinePrice());
        if (CollectionUtils.isNotEmpty(ocmsOrderVO.getOnlineGiftVOS())) {
            orderVO.setGiftVOList(ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull).map(this::buildGiftVO).collect(Collectors.toList()));
            orderVO.setGiftCount(ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull).mapToInt(OnlineGiftVO::getGiftQuantity).sum());
        }
        orderVO.setUserTags(UserTagTypeEnum.getTags(ocmsOrderVO.getTags()));
        orderVO.setIsNeedInvoice(ocmsOrderVO.getIsNeedInvoice());
        orderVO.setActionTagList(ActionTagConverter.ocmsOrderVoConvertTag(ocmsOrderVO));
        orderVO.setOrderUserType(ocmsOrderVO.getUserType());
        orderVO.setOrderTagList(OrderTagVO.convertOrderTagList(ocmsOrderVO, showLabelList));;
        orderVO.setDownFlag(ocmsOrderVO.getDownFlag());
        orderVO.setDegradeModules(ocmsOrderVO.getDegradeModules());
        return orderVO;
    }


    private void buildOrderVOOfDeliveryInfo(OrderVO orderVO, OCMSDeliveryInfoVO ocmsDeliveryInfoVO, OCMSOrderVO ocmsOrderVO) {
        if (!isDeliveryInfoNotNull(ocmsDeliveryInfoVO)){
            return;
        }
        orderVO.setDeliveryMethod(ocmsDeliveryInfoVO.getDistributeMethod());
        orderVO.setDeliveryMethodDesc(ocmsDeliveryInfoVO.getDistributeMethodName());
        orderVO.setDeliveryUserName(ocmsDeliveryInfoVO.getRiderName());
        orderVO.setDeliveryUserPhone(ocmsDeliveryInfoVO.getRiderPhone());
        orderVO.setReceiverName(ocmsDeliveryInfoVO.getUserName());
        orderVO.setReceiverPhone(ocmsDeliveryInfoVO.getUserPhone());
        orderVO.setReceiverAddress(ocmsDeliveryInfoVO.getUserAddress());

        // 订单状态已完结 && 非歪马租户 && 租户支持隐藏隐私号
        Long orderStatusTime = OrderUtil.getOrderTimeByLog(ocmsOrderVO.getOrderStatus(), ocmsOrderVO.getOrderStatusLogList());
        if (OrderUtil.isOrderEnd(orderVO.getOrderStatus())
                && !MccConfigUtil.isDrunkHorseTenant(orderVO.getTenantId())
                && MccDynamicConfigUtil.checkSupportDesensitizeReceiverInfoTenant(orderVO.getTenantId())){
            // 订单超过时间，需要隐藏隐私号
            if(OrderUtil.isOverConfigTime(orderStatusTime, MccDynamicConfigUtil.getDesensitizeReceiverInfoTime())){
                orderVO.setReceiverPhone(null);
            }
        }

        // 订单收货人信息脱敏处理
        DesensitizeReceiverInfoExtParam receiverInfoExtParam = DesensitizeReceiverInfoExtParam.build(
                orderVO.getReceiverName(),orderVO.getReceiverAddress(),orderVO.getReceiverPhone(),
                null, ocmsOrderVO.getTenantId(), ocmsOrderVO.getOrderStatus(), orderStatusTime);
        DesensitizeReceiverInfoResult desensitizeReceiverInfoResult = DesensitizeReceiverInfoUtil.desensitizeReceiverInfo(receiverInfoExtParam);
        if(Objects.nonNull(desensitizeReceiverInfoResult)) {
            orderVO.setReceiverName(desensitizeReceiverInfoResult.getReceiverName());
            orderVO.setReceiverAddress(desensitizeReceiverInfoResult.getReceiverAddress());
            orderVO.setReceiverPhone(desensitizeReceiverInfoResult.getReceiverPhone());
        }

        // 自提
        boolean selfMention = DistributeMethodEnum.STORE_DELIVERY.getDesc().equals(ocmsDeliveryInfoVO.getDistributeMethodName());
        if(selfMention && orderVO.getChannelId() != null && orderVO.getChannelId().equals(ChannelType.YOU_ZAN.getValue())){
            // 有赞渠道自提隐藏用户地址
            orderVO.setReceiverAddress("到店自取@#到店自取");
        }
        orderVO.setEstimateArriveTimeStart(ocmsDeliveryInfoVO.getArrivalTime());
        orderVO.setEstimateArriveTimeEnd(ocmsDeliveryInfoVO.getArrivalEndTime());
        orderVO.setPickStatus(ocmsDeliveryInfoVO.getDeliveryStatus());
        orderVO.setPickCompleteTime(ocmsDeliveryInfoVO.getCompleteTime());
        orderVO.setDistributeStatus(ocmsDeliveryInfoVO.getDistributeStatus());
        orderVO.setSelfDelivery(ocmsDeliveryInfoVO.getIsSelfDelivery());
        orderVO.setSupportDeliveryUserPrivacyPhone(judgeSupportDeliveryUserPrivacyPhone(ocmsDeliveryInfoVO.getIsSelfDelivery()));
        if (ocmsDeliveryInfoVO.getDeliveryPauseFlag() != null){
            orderVO.setDeliveryStatusLocked(ocmsDeliveryInfoVO.getDeliveryPauseFlag());
        }
        if(StringUtils.isNotEmpty(ocmsDeliveryInfoVO.getSelfFetchCode())){
            orderVO.setSelfFetchCode(ocmsDeliveryInfoVO.getSelfFetchCode());
        }
        orderVO.setSelfFetchStatus(ocmsDeliveryInfoVO.getSelfFetchStatus());
        orderVO.setDeliveryChannelId(ocmsDeliveryInfoVO.getDeliveryChannelId());
    }

    private OrderRefundInfo buildOrderRefundInfo(OCMSOrderVO ocmsOrderVO,  Map<String, OCMSOrderItemVO> orderItemMap) {
        if (CollectionUtils.isEmpty(ocmsOrderVO.getAfterSaleApplyVOList())) {
            return null;
        }
        OrderRefundInfo orderRefundInfo = new OrderRefundInfo();
        removeInvalidAfterSaleApply(ocmsOrderVO, ocmsOrderVO.getAfterSaleApplyVOList());
        //只处理用户退款，不处理用户申述
        if (Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), ocmsOrderVO.getOrderStatus())) {
            OCMSAfterSaleApplyVO afterSaleApplyVO = Optional.ofNullable(ocmsOrderVO.getAfterSaleApplyVOList())
                    .map(List::stream).orElse(Stream.empty())
                    .filter(OCMSAfterSaleApplyVO::isWait2Audit).findFirst().orElse(null);
            OrderStatusLog orderStatusLog = ocmsOrderVO.getOrderStatusLogList().stream()
                    .filter(e -> Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), e.getTargetStatus()))
                    .max(Comparator.comparingLong(OrderStatusLog::getCreateTime)).orElse(null);
            if (afterSaleApplyVO != null && orderStatusLog != null) {
                //设置等待退款的申请，这里的待审批信息会关联订单状态，退差价不会改变订单状态
                orderRefundInfo.setWaitAuditRefund(buildWaitAuditRefund(orderStatusLog, afterSaleApplyVO, orderItemMap));
            }

            // 设置多条待审核售后单
            if(MccConfigUtil.isShowWaitAuditRefundList()){
                List<OCMSAfterSaleApplyVO> afterSaleApplyVOList = Optional.ofNullable(ocmsOrderVO.getAfterSaleApplyVOList())
                        .map(List::stream).orElse(Stream.empty())
                        .filter(OCMSAfterSaleApplyVO::isWait2Audit)
                        .collect(Collectors.toList());
                if (afterSaleApplyVO != null && orderStatusLog != null) {
                    //设置等待退款的申请列表，这里的待审批信息会关联订单状态，退差价不会改变订单状态
                    orderRefundInfo.setWaitAuditRefundList(buildWaitAuditRefundList(orderStatusLog, afterSaleApplyVOList, orderItemMap));
                }
            }
        }
        //这里的退款信息会关联订单状态，退差价不会改变订单状态
        List<RefundLog> refundLogList = ocmsOrderVO.getOrderStatusLogList().stream()
                .filter(this::isRefundConcernStatusChange)
                .map(orderStatusLog -> {
                    boolean isAuditRefund = Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), orderStatusLog.getSourceStatus());
                    OCMSAfterSaleApplyVO afterSaleApplyVO = findNearestTimeAfterSale(orderStatusLog.getCreateTime(), ocmsOrderVO.getAfterSaleApplyVOList(), isAuditRefund);
                    //只处理处理完的售后申请, 京东的暂存售后单也不显示给前端
                    if (afterSaleApplyVO != null && !afterSaleApplyVO.isWait2Audit()
                            && AfterSaleApplyStatusEnum.DRAFT.getValue().equals(afterSaleApplyVO.getStatus())
                            && AfterSaleApplyStatusEnum.DRAFT_DONE.getValue().equals(afterSaleApplyVO.getStatus())) {
                        return RefundLog.buildRefundLog(orderStatusLog, afterSaleApplyVO);
                    }
                    return null;})
                .filter(Objects::nonNull)
                .sorted(Comparator.comparingLong(RefundLog::getOptTime).reversed())
                .collect(Collectors.toList());
        //添加退差价信息,重排序
        refundLogList.addAll(addWeightRefundLog(ocmsOrderVO));
        refundLogList = refundLogList.stream()
                .sorted(Comparator.comparingLong(RefundLog::getOptTime).reversed())
                .collect(Collectors.toList());
        orderRefundInfo.setRefundLogs(refundLogList);
        return orderRefundInfo;
    }

    private RevenueDetailVo buildRevenueDetailVo(OCMSOrderVO ocmsOrderVO, OrderRevenueDetailResponse orderRevenueDetailResponse, SaasCrmDataRemoteService.OrderProfitView orderProfit) {
        if (orderRevenueDetailResponse == null || orderRevenueDetailResponse.getOrderAmountInfo() == null) {
            return null;
        }
        OrderAmountInfo orderAmountInfo = orderRevenueDetailResponse.getOrderAmountInfo();
        RevenueDetailVo revenueDetailVo = new RevenueDetailVo();
        revenueDetailVo.setPromotionInfos(orderRevenueDetailResponse.getPromotionInfos());
        revenueDetailVo.setActualPayAmount(orderAmountInfo.getActualPayAmt());
        if (!Integer.valueOf(OrderSourceEnum.GLORY.getValue()).equals(ocmsOrderVO.getOrderSource())) {
            // 牵牛花一期不返回活动分摊信息、二期适配后再放开
            revenueDetailVo.setBizActivityAmount(orderAmountInfo.getBizCharge());
        }
        revenueDetailVo.setDeliveryAmount(orderAmountInfo.getDeliveryFee());
        revenueDetailVo.setPackageAmount(orderAmountInfo.getPackageAmount());
        revenueDetailVo.setRevenueAmount(orderAmountInfo.getBizReceiveAmount());
        if (orderProfit != null) {
            revenueDetailVo.setNetProfitOnline(orderProfit.getProfit().intValue());
            revenueDetailVo.setWithDeliveryCost(orderProfit.getWithDeliveryCost());
        }
        return revenueDetailVo;
    }

    private OrderPrintStatusVo buildPrintStatusVo(OrderPrintResultDto printResultDto) {
        OrderPrintStatusVo printStatusVo = new OrderPrintStatusVo();
        printStatusVo.setPrintStatus(printResultDto.getPrintStatus());
        printStatusVo.setPrintFailToast(generatePrintToast(printResultDto));
        printStatusVo.setDeviceInfo(printResultDto.getDeviceInfo());
        return printStatusVo;
    }

    private void fillOrderMaltDeliveryPlatModule(List<OrderVO> orderVOList, Map<Long, TDeliveryDetail> deliveryDetailMap, Map<Integer, DeliveryChannelDto> channelDtoMap) {
        if (CollectionUtils.isEmpty(orderVOList)) {
            return;
        }
        Long tenantId = orderVOList.get(0).getTenantId();
        List<TStoreConfig> tStoreConfigs = queryDeliveryStoreConfigByOrderList(orderVOList);
        if (CollectionUtils.isEmpty(tStoreConfigs)) {
            return;
        }
        List<Integer> platformEnumList = Arrays.asList(AggDeliveryPlatformEnum.MALT_FARM.getCode(), AggDeliveryPlatformEnum.DAP_DELIVERY.getCode());
        Map<Long, Map<Integer, Map<Integer, TAggDeliveryPlatformConfig>>> tStoreConfigMap = new HashMap<>();
        for (TStoreConfig tStoreConfig : tStoreConfigs) {
            List<TAggDeliveryPlatformConfig> platformConfigList = tStoreConfig.getAggPlatformConfigs();
            if (CollectionUtils.isEmpty(platformConfigList)) {
                continue;
            }
            tStoreConfigMap.put(tStoreConfig.getStoreId(), toPlatformConfigMap(platformConfigList, platformEnumList));
        }

        Map<Integer, Integer> deliveryChannelMap = deliveryChannelWrapper.tratranslateToChannelIntgerMap(channelDtoMap);
        ArrayListMultimap<Long, Long> dapOrderIdMultiMap = ArrayListMultimap.create();
        ArrayListMultimap<Long, String> dapFulfillOrderIdMultiMap = ArrayListMultimap.create();
        for (OrderVO vo : orderVOList) {
            TDeliveryDetail deliveryDetail = deliveryDetailMap.get(vo.getEmpowerOrderId());
            if (deliveryDetail == null) {
                continue;
            }
            if (!deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode, AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(), vo.getTenantId(), vo.getStoreId(), deliveryChannelMap)) {
                continue;
            }
            if(deliveryDetail.platformSource!=null && Objects.equals(deliveryDetail.platformSource, PlatformSourceEnum.OFC.getCode()) ){
                if(vo.getWarehouseId()!=null){
                    dapFulfillOrderIdMultiMap.put(vo.getWarehouseId(), PlatformSourceEnum.OFC.toPrefixOrderId(deliveryDetail.fulfillOrderId+""));
                }else {
                    dapFulfillOrderIdMultiMap.put(vo.getStoreId(),PlatformSourceEnum.OFC.toPrefixOrderId(deliveryDetail.fulfillOrderId+""));
                }
            }else {
                if (vo.getWarehouseId() != null) {
                    dapOrderIdMultiMap.put(vo.getWarehouseId(), vo.getEmpowerOrderId());
                } else {
                    dapOrderIdMultiMap.put(vo.getStoreId(), vo.getEmpowerOrderId());
                }
            }
        }
        Map<String, String> urlMap = new HashMap<>();
        if (!dapOrderIdMultiMap.isEmpty()) {
            for (Long poiId : dapOrderIdMultiMap.keySet()) {
                List<Long> orderIdList = dapOrderIdMultiMap.get(poiId);
                try {
                    Map<String, String> url = tmsServiceWrapper.batchQueryOrderDeliveryUrl(tenantId, poiId, orderIdList,dapFulfillOrderIdMultiMap.get(poiId));
                    if (MapUtils.isEmpty(url)) {
                        continue;
                    }
                    urlMap.putAll(url);
                }catch (Exception e){
                    log.error("获取URL失败 orderIdList:{}",orderIdList,e);
                }

            }
        }
        orderVOList.forEach(order -> setOrderDeliveryPlatform(deliveryDetailMap, order, tStoreConfigMap, deliveryChannelMap, urlMap));
    }

    private void setOrderDeliveryPlatform(Map<Long, TDeliveryDetail> deliveryDetailMap, OrderVO order,
                                          Map<Long, Map<Integer, Map<Integer, TAggDeliveryPlatformConfig>>> tStoreConfigMap,
                                          Map<Integer, Integer> deliveryChannelMap, Map<String, String> urlMap) {
        if (order.getStoreId() == null) {
            return;
        }
        TAggDeliveryPlatformConfig tAggDeliveryPlatformConfig = null;
        Long shopId = order.getStoreId();
        if (order.getWarehouseId() != null) {
            shopId = order.getWarehouseId();
        }
        TDeliveryDetail deliveryDetail = deliveryDetailMap.get(order.getEmpowerOrderId());
        if (deliveryDetail == null) {
            return;
        }
        Map<Integer, Map<Integer, TAggDeliveryPlatformConfig>> platformConfigMap = tStoreConfigMap.get(shopId);
        if (MapUtils.isNotEmpty(platformConfigMap)) {
            Map<Integer, TAggDeliveryPlatformConfig> configMap;
            if (platformConfigMap.containsKey(order.getChannelId())) {
                configMap = platformConfigMap.get(order.getChannelId());
            } else {
                configMap = platformConfigMap.get(ChannelType.MEITUAN.getValue());
            }
            if (MapUtils.isNotEmpty(configMap)) {
                AggDeliveryPlatformEnum platformEnum = AggDeliveryPlatformEnum.MALT_FARM;
                if (deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode, AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(), order.getTenantId(), order.getStoreId(), deliveryChannelMap)) {
                    platformEnum = AggDeliveryPlatformEnum.DAP_DELIVERY;
                }
                tAggDeliveryPlatformConfig = configMap.get(platformEnum.getCode());
            }
        }
        if (tAggDeliveryPlatformConfig == null) {
            tAggDeliveryPlatformConfig = new TAggDeliveryPlatformConfig();
        }

        if (tAggDeliveryPlatformConfig.getPlatformCode() == null) {
            return;
        }
        String oId = order.getEmpowerOrderId().toString();
        if(deliveryDetail.platformSource!=null && Objects.equals(deliveryDetail.platformSource, PlatformSourceEnum.OFC.getCode())){
            oId = PlatformSourceEnum.OFC.toPrefixOrderId(deliveryDetail.fulfillOrderId+"");
        }

        if (deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode, AggDeliveryPlatformEnum.MALT_FARM.getCode(), order.getTenantId(), order.getStoreId(), deliveryChannelMap)) {
            order.setDeliveryPlatformCode(AggDeliveryPlatformEnum.MALT_FARM.getCode());
            if (order.getRealDistributeStatus() == null || order.getRealDistributeStatus() <= TmsDeliveryStatusDescEnum.INIT.getCode() ||
                    // 针对麦芽田，配送拒单，也不展示链接
                    order.getRealDistributeStatus() == TmsDeliveryStatusDescEnum.DELIVERY_REJECTED.getCode() ||
                    // 麦芽田转自配送后，不展示链接
                    deliveryDetail.deliveryChannelCode == DeliveryChannelEnum.FARM_DELIVERY_MERCHANT.getCode()) {
                DeliveryRedirectModuleVo deliveryRedirectModuleVo = new DeliveryRedirectModuleVo();
                deliveryRedirectModuleVo.setTitle("暂无配送状态");
                deliveryRedirectModuleVo.setShowButton(false);
                order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
                return;
            }
            DeliveryRedirectModuleVo deliveryRedirectModuleVo = AggDeliveryPlatformEnum.MALT_FARM.fillDeliveryRedirectModule(tAggDeliveryPlatformConfig,
                    oId, shopId,
                    Objects.nonNull(order.getDeliveryExceptionType()) && !Objects.equals(order.getDeliveryExceptionType(), MiniAppConstants.DELIVERY_NO_EXCEPTION));
            order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
        } else if (deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode, AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(), order.getTenantId(), order.getStoreId(), deliveryChannelMap)) {
            order.setDeliveryPlatformCode(AggDeliveryPlatformEnum.DAP_DELIVERY.getCode());
            if (order.getRealDistributeStatus() == null || order.getRealDistributeStatus() <= TmsDeliveryStatusDescEnum.INIT.getCode() ||
                    //配送拒单，也不展示链接
                    order.getRealDistributeStatus() == TmsDeliveryStatusDescEnum.DELIVERY_REJECTED.getCode()) {
                DeliveryRedirectModuleVo deliveryRedirectModuleVo = new DeliveryRedirectModuleVo();
                deliveryRedirectModuleVo.setTitle("暂无配送状态");
                deliveryRedirectModuleVo.setShowButton(false);
                order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
                return;
            }
            String url = urlMap.get(oId);
            DeliveryRedirectModuleVo deliveryRedirectModuleVo = AggDeliveryPlatformEnum.DAP_DELIVERY.fillDeliveryRedirectModule(url,
                    Objects.nonNull(order.getDeliveryExceptionType()) && !Objects.equals(order.getDeliveryExceptionType(), MiniAppConstants.DELIVERY_NO_EXCEPTION));
            deliveryRedirectModuleVo.setShowButton(true);
            order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
        }
    }

    private void fillDeliveryOperateItem(Map<Long, TDeliveryDetail> deliveryDetailMap, List<OrderVO> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        if (MccConfigUtil.showDeliveryOperateItemSwitch()) {
            showDeliveryOperateItemSwitch(orderList);
            return;
        }
        List<TStoreConfig> tStoreConfigs = queryDeliveryStoreConfigByOrderList(orderList);
        Map<Long, Set<Integer>> tStoreConfigChannelMap = tStoreConfigs.stream()
                .filter(tStoreConfig -> tStoreConfig != null && CollectionUtils.isNotEmpty(tStoreConfig.getAggPlatformConfigs()))
                .collect(Collectors.toMap(TStoreConfig::getStoreId, this::buildPlatformCodeSet, (oldV, newV) -> newV));

        for (OrderVO orderVO : orderList) {
            TDeliveryDetail tDeliveryDetail = deliveryDetailMap.get(orderVO.getEmpowerOrderId());
            List<Integer> deliveryItemList = new ArrayList<>();
            //自提订单，不走配送
            if (orderVO.getDeliveryMethod() != null && orderVO.getDeliveryMethod() == DistributeMethodEnum.STORE_DELIVERY.getValue()) {
                continue;
            }
            OrderStatusEnum orderStatusEnum = OrderStatusEnum.enumOf(orderVO.getOrderStatus());
            if ((orderStatusEnum != null && NOT_SHOW_DELIVERY_ITEM_ORDER_STATUS.contains(orderStatusEnum)) || orderVO.getChannelId() == null) {
                continue;
            }
            DistributeStatusEnum distributeStatusEnum = DistributeStatusEnum.DISTRIBUTE_UNKNOWN;
            if (Objects.nonNull(orderVO.getDistributeStatus())) {
                distributeStatusEnum = DistributeStatusEnum.enumOf(orderVO.getDistributeStatus());
            }
            //已经转单的不容许再次转单
            if (tDeliveryDetail != null && tDeliveryDetail.transType != null && tDeliveryDetail.transType != 0) {
                continue;
            }
            //二次配送不容许转单
            if (tDeliveryDetail != null && tDeliveryDetail.deliveryCount != null && tDeliveryDetail.deliveryCount > 1) {
                continue;
            }

            if (tDeliveryDetail == null || tDeliveryDetail.deliveryEntity == null || tDeliveryDetail.deliveryEntity == 0) {
                //平台配送
                fillPlatformDeliveryOperateItem(tDeliveryDetail, distributeStatusEnum, orderVO, deliveryItemList, tStoreConfigChannelMap);
            } else if (tDeliveryDetail.deliveryEntity == 1) {
                //聚合配送
                if (distributeStatusEnum == DistributeStatusEnum.RIDER_DELIVERED) {
                    continue;
                }
                if (distributeStatusEnum == DistributeStatusEnum.RIDER_TAKE_GOODS &&
                        (tDeliveryDetail.deliveryExceptionCode == null || tDeliveryDetail.deliveryExceptionCode == 0)) {
                    continue;
                }
                deliveryItemList.add(DeliveryOperateItemEnum.DELIVERY_TO_SELF.type);
            } else {
                //商家自己送
                fillSelfDeliveryOperateItem(distributeStatusEnum, tStoreConfigChannelMap, deliveryItemList, orderVO);
            }
            orderVO.setDeliveryOperateItems(deliveryItemList);
        }
    }

    private void showDeliveryOperateItemSwitch(List<OrderVO> orderList) {
        Long tenantId = orderList.get(0).getTenantId();
        ArrayListMultimap<Long, OrderVO> orderMultimap = ArrayListMultimap.create();
        for (OrderVO vo : orderList) {
            if (vo.getWarehouseId() != null) {
                orderMultimap.put(vo.getWarehouseId(), vo);
            } else {
                orderMultimap.put(vo.getStoreId(), vo);
            }
        }

        for (Long storeId : orderMultimap.keySet()) {
            List<OrderVO> orderVOList = orderMultimap.get(storeId);
            if (CollectionUtils.isEmpty(orderVOList)) {
                continue;
            }
            Map<Long, DeliveryOperateItem> operateItemMap = tmsServiceWrapper.queryDeliveryOperateItem(tenantId, storeId, orderVOList.stream().map(OrderVO::getEmpowerOrderId).collect(Collectors.toList()));
            for (OrderVO orderVO : orderVOList) {
                List<Integer> operateList = new ArrayList<>();
                if (!operateItemMap.containsKey(orderVO.getEmpowerOrderId())) {
                    continue;
                }
                DeliveryOperateItem item = operateItemMap.get(orderVO.getEmpowerOrderId());
                if (item == null || CollectionUtils.isEmpty(item.getOperateItemList())) {
                    continue;
                }
                List<DeliveryOperateItemEnum> itemEnumList = DeliveryOperateItemEnum.tmsItemListToOperateItemList(item.getOperateItemList());
                if (CollectionUtils.isEmpty(itemEnumList)) {
                    continue;
                }
                if (itemEnumList.contains(DeliveryOperateItemEnum.DELIVERY_TO_SELF)) {
                    operateList.add(DeliveryOperateItemEnum.DELIVERY_TO_SELF.type);
                }
                if (itemEnumList.contains(DeliveryOperateItemEnum.DELIVERY_TO_MALTFARM)) {
                    operateList.add(DeliveryOperateItemEnum.DELIVERY_TO_MALTFARM.type);
                }
                if (itemEnumList.contains(DeliveryOperateItemEnum.DELIVERY_TO_DAP)) {
                    operateList.add(DeliveryOperateItemEnum.DELIVERY_TO_DAP.type);
                }
                orderVO.setDeliveryOperateItems(operateList);
            }
        }
    }

    private Set<Integer> buildPlatformCodeSet(TStoreConfig tStoreConfig) {
        Set<Integer> platformCodeSet = new HashSet<>();
        tStoreConfig.getAggPlatformConfigs().forEach(config -> {
            AggDeliveryPlatformEnum platformEnum = AggDeliveryPlatformEnum.codeValueOf(config.getPlatformCode());
            if (platformEnum != null && config.getOpenFlag() != null && config.getOpenFlag() == 1) {
                platformCodeSet.add(platformEnum.getCode());
            }
        });
        return platformCodeSet;
    }

    private OrderPrintResultQueryResponse queryOrderPrintResult(List<Long> storeIds, long tenantId, List<OrderIdentifierDTO> orders) {
        if (CollectionUtils.isNotEmpty(orders)) {
            OrderPrintResultQueryRequest resultQueryRequest = new OrderPrintResultQueryRequest();
            // 最底层查询没有用到storeId,防止校验报错，传第一个id
            resultQueryRequest.setOfflineStoreId(storeIds.get(0));
            resultQueryRequest.setTenantId(tenantId);
            resultQueryRequest.setOrderList(orders.stream()
                    .filter(e -> e.getSourceCode() != null && StringUtils.isNotBlank(e.getUnifyOrderId()))
                    .map(e -> new OrderPrintQueryDto(e.getUnifyOrderId(), e.getSourceCode()))
                    .collect(Collectors.toList())
            );
            try {
                OrderPrintResultQueryResponse response = pickSelectPrintQueryThriftService.queryOrderPrintResult(resultQueryRequest);
                log.info("请求履约获取订单打印状态，request:{}, response:{}", resultQueryRequest, response);
                return response;
            } catch (Exception e) {
                log.error("请求订单打印状态失败,shopIds:{}, tenantId:{}", storeIds, tenantId, e);
            }
        }
        return OrderPrintResultQueryResponse.builder().status(Status.FAIL).printResultList(Lists.newArrayList()).build();
    }

    private List<OrderRevenueDetailResponse> getOrderListRevenueDetail4TenantAndViewIds(Long tenantId, List<OCMSOrderVO> orderVOS) {
        if (CollectionUtils.isEmpty(orderVOS)) {
            return Collections.emptyList();
        }
        MerchantOrderRevenueDetailRequest request = new MerchantOrderRevenueDetailRequest();
        request.setTenantId(tenantId);
        List<ViewIdCondition> conditions = orderVOS.stream()
                .map(order -> ViewIdCondition.builder()
                        .orderBizType(order.getOrderBizType())
                        .viewOrderId(order.getViewOrderId())
                        .build())
                .collect(Collectors.toList());
        request.setViewIdConditionList(conditions);

        try {
            MerchantOrderListRevenueDetailResponse revenueDetailResponse = ocmsOrderServiceWrapper.orderListRevenueDetail(request).getData();
            return Optional.ofNullable(revenueDetailResponse)
                    .map(MerchantOrderListRevenueDetailResponse::getOrderListRevenueDetailResponse)
                    .orElse(Collections.emptyList());
        } catch (Exception e) {
            // 查询营收数据异常、返回空、前端不显示订单零售金额相关数据
            log.error("查询营收数据异常: ", e);
            return Collections.emptyList();
        }
    }

    private Map<String, SaasCrmDataRemoteService.OrderProfitView> getOrderProfitMap(Long tenantId, List<OCMSOrderVO> orderVOS) {
        try {
            // 获取租户业务模式，只处理便利店租户
            List<OCMSOrderVO> orders = needOrderProfit(tenantId, orderVOS);
            if (CollectionUtils.isEmpty(orders)) {
                return Collections.emptyMap();
            }

            Map<String, SaasCrmDataRemoteService.OrderProfitView> profitMap = saasCrmDataWrapper.queryNetProfit(orders);
            log.info("查询订单毛利 orders:{},profitMap:{}", Fun.map(orders, OCMSOrderVO::getViewOrderId), profitMap);
            return profitMap;
        } catch (Exception e) {
            log.warn("获取门店预计毛利失败 tenantId:{},orderVOS:{}", tenantId, Fun.map(orderVOS, OCMSOrderVO::getViewOrderId), e);
            return Collections.emptyMap();
        }
    }

    private void setRefundPriceDisplayType4OrderList(List<OCMSOrderVO> ocmsOrderVOS, int priceDisplayType) {
        ocmsOrderVOS.stream()
                    .flatMap(order -> order.getAfterSaleApplyVOList().stream())
                    .flatMap(apply -> apply.getOcmsAfterSaleApplyDetailVOList().stream())
                    .forEach(detail -> detail.setPriceDisplayType(priceDisplayType));
    }

    /**
     * 针对全部订单列表，完善订单状态描述.
     *
     * @param orderListResponse 订单列表
     */
    private void fixOrdersStatusDesc(OrderListResponse orderListResponse) {
        if (CollectionUtils.isEmpty(orderListResponse.getOrderList())) {
            return;
        }
        orderListResponse.getOrderList().forEach(order -> order.setOrderStatusDesc(getOrderOptStatusDesc(order)));
    }

    private PageInfoVO buildPageInfoVO(Integer page, Integer size, Integer totalSize) {
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(page);
        pageInfoVO.setSize(size);
        pageInfoVO.setTotalSize(totalSize);
        pageInfoVO.setTotalPage(totalSize % size == 0 ? totalSize / size : totalSize / size + 1);
        return pageInfoVO;
    }

    private boolean isDeliveryInfoNotNull(OCMSDeliveryInfoVO ocmsDeliveryInfoVO) {
        return ocmsDeliveryInfoVO != null &&
                ocmsDeliveryInfoVO.getCreateTime() != null &&
                ocmsDeliveryInfoVO.getOrderId() != null &&
                ocmsDeliveryInfoVO.getDeliveryStatus() != null &&
                ocmsDeliveryInfoVO.getTenantId() != null &&
                ocmsDeliveryInfoVO.getShopId() != null;
    }

    /**
     * 判断是否支持拨打骑手隐私号（目前仅平台配送支持查询骑手隐私号）
     *
     * @param isSelfDelivery 是否为商家自配送 1:是  0:否
     * @return 是否支持拨打骑手隐私号
     */
    private static boolean judgeSupportDeliveryUserPrivacyPhone(Integer isSelfDelivery) {
        // 平台配送，支持查询骑手隐私号
        return Objects.equals(isSelfDelivery, MiniAppConstants.IS_SELF_DELIVERY_NO);
    }

    private String getDeliveryOrderTypeName(Integer deliveryOrderType) {
        DeliveryOrderType deliveryOrderTypeEnum = DeliveryOrderType.findByValue(deliveryOrderType);
        if (Objects.isNull(deliveryOrderTypeEnum)) {
            return "未知";
        }
        switch (deliveryOrderTypeEnum) {
            case DELIVERY_RIGHT_NOW:
                return "立即送达";
            case DELIVERY_BY_BOOK_TIME:
                return "预订";
            default:
                return "未知";
        }
    }

    private ProductVO buildProductVO(OCMSOrderItemVO ocmsOrderItemVO) {
        ProductVO productVO = new ProductVO();
        productVO.setSkuId(ocmsOrderItemVO.getCustomerSkuId());
        productVO.setUpcCode(ocmsOrderItemVO.getSkuCode());
        productVO.setSkuName(ocmsOrderItemVO.getSkuName());
        productVO.setPicUrl(ocmsOrderItemVO.getPicUrl());
        productVO.setSpecification(ocmsOrderItemVO.getSpecification());
        productVO.setSellUnit(ocmsOrderItemVO.getSellUnit());

        ExchangeUtil.ExchangeDO exchangeDO = ExchangeUtil.loadExchangeMetaData(ocmsOrderItemVO.getExtData());
        //如果是换货商品，直接不展示，过滤掉
        if(exchangeDO.getExchangeSourceOrderItemId() > 0){
            return null;
        }
        int quantity = exchangeDO.getOrderQuantity() == 0?ocmsOrderItemVO.getQuantity():exchangeDO.getOrderQuantity();
        productVO.setCount(quantity);
        if (Objects.nonNull(ocmsOrderItemVO.getOriginalPrice())) {
            productVO.setOriginalTotalPrice(ocmsOrderItemVO.getOriginalPrice() * quantity);
        }
        productVO.setTotalPayAmount(ocmsOrderItemVO.getTotalPayAmount());
        productVO.setUnitPrice(ocmsOrderItemVO.getUnitPrice());

        productVO.setOrderItemOfflinePrice(ocmsOrderItemVO.getOfflinePrice());
        List<TagInfoVO> tagInfoVOS = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVO.getFulfillmentTagList())) {
            for (String fulfillmentTag : ocmsOrderItemVO.getFulfillmentTagList()) {
                TagInfoVO tagInfoVO = new TagInfoVO();
                tagInfoVO.setName(fulfillmentTag);
                tagInfoVO.setType(MiniAppConstants.FULFILLMENT_TAG);
                tagInfoVOS.add(tagInfoVO);
            }
        }
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVO.getPickTagList())) {
            for (String pickTag : ocmsOrderItemVO.getPickTagList()) {
                TagInfoVO tagInfoVO = new TagInfoVO();
                tagInfoVO.setName(pickTag);
                tagInfoVO.setType(MiniAppConstants.PICK_TAG);
                tagInfoVOS.add(tagInfoVO);
            }
        }
        //商品标签(需要排除履约和拣货标签)
        productVO.setTagInfos(Optional.ofNullable(ocmsOrderItemVO.getTagInfos())
                .orElse(Collections.emptyList()).stream().map(tag -> {
                    TagInfoVO tagInfoVO = new TagInfoVO();
                    tagInfoVO.setName(tag.getName());
                    tagInfoVO.setType(tag.getType());
                    return tagInfoVO;
                }).collect(Collectors.toList()));
        productVO.setTagInfoList(tagInfoVOS);
        productVO.setCurrentPrice(ocmsOrderItemVO.getCurrentPrice());
        return productVO;
    }

    private GiftVO buildGiftVO(OnlineGiftVO onlineGiftVO) {
        if (onlineGiftVO != null) {
            GiftVO giftVO = new GiftVO();
            giftVO.setGiftName(onlineGiftVO.getGiftName());
            giftVO.setGiftQuantity(onlineGiftVO.getGiftQuantity());
            return giftVO;
        }
        return null;
    }

    private void removeInvalidAfterSaleApply(OCMSOrderVO ocmsOrderVO, List<OCMSAfterSaleApplyVO> afterSaleApplyVOList) {
        if (CollectionUtils.isEmpty(afterSaleApplyVOList)) {
            return;
        }
        afterSaleApplyVOList.removeIf(applyVO -> {
            boolean isInvalid = StringUtils.isBlank(applyVO.getAfterSaleId())
                    || applyVO.getCreateTime() == null
                    || applyVO.getUpdateTime() == null
                    || applyVO.getOrderId() == null;
            if (isInvalid) {
                log.info("售后索引，缺少必要信息，可能是Es索引写入延迟问题,serviceId:{}, order:{}", applyVO.getServiceId(), ocmsOrderVO.getViewOrderId());
                MetricHelper.build().name("order.afterSaleRecordInvalid.err").tag("tenantId", String.valueOf(ocmsOrderVO.getTenantId())).tag("storeId", String.valueOf(ocmsOrderVO.getShopId())).count();
            }
            return isInvalid;
        });
    }


    private RefundingRecordVO buildWaitAuditRefund(OrderStatusLog orderStatusLog, OCMSAfterSaleApplyVO afterSaleApplyVO, Map<String, OCMSOrderItemVO> orderItemMap) {
        RefundingRecordVO refundApplyRecordVO = new RefundingRecordVO();
        refundApplyRecordVO.setServiceId(afterSaleApplyVO.getServiceId());
        refundApplyRecordVO.setAfterSaleId(afterSaleApplyVO.getAfterSaleId());
        refundApplyRecordVO.setIsAudit(afterSaleApplyVO.getIsAudit());
        refundApplyRecordVO.setStatus(afterSaleApplyVO.getStatus());
        refundApplyRecordVO.setApplyReason(afterSaleApplyVO.getApplyReason());
        refundApplyRecordVO.setAfsPattern(afterSaleApplyVO.getAfsPattern());
        refundApplyRecordVO.setCreateTime(afterSaleApplyVO.getCreateTime());
        refundApplyRecordVO.setUpdateTime(afterSaleApplyVO.getUpdateTime());
        refundApplyRecordVO.setAfsApplyType(afterSaleApplyVO.getApplyType());
        refundApplyRecordVO.setWhoApplyType(afterSaleApplyVO.getApplyUserType());
        refundApplyRecordVO.setRefundOptContent(RefundLog.buildRefundContent(orderStatusLog, afterSaleApplyVO));
        if (afterSaleApplyVO.getApplyType() != null) {
            AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(afterSaleApplyVO.getApplyType());
            refundApplyRecordVO.setRefundApplyType(WaitToAuditRefundGoodsOrderSubTypeEnum.getSubTypeCode(afterSaleTypeEnum));
        }
        List<OCMSAfterSaleApplyDetailVO> ocmsAfterSaleApplyDetailVOList = afterSaleApplyVO.getOcmsAfterSaleApplyDetailVOList();
        if (CollectionUtils.isNotEmpty(ocmsAfterSaleApplyDetailVOList)) {
            List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOList = ocmsAfterSaleApplyDetailVOList.stream()
                    .filter(Objects::nonNull)
                    .map(RefundApplyRecordDetailVO::buildRefundApplyRecordDetailVO)
                    .collect(Collectors.toList());
            // 将订单中的商品信息赋值到售后信息中
            if (MapUtils.isNotEmpty(orderItemMap)) {
                refundApplyRecordDetailVOList.forEach(item -> {
                    if (orderItemMap.containsKey(item.getSkuId())) {
                        item.setPicUrl(orderItemMap.get(item.getSkuId()).getPicUrl());
                        item.setOrderItemOfflinePrice(orderItemMap.get(item.getSkuId()).getOfflinePrice());
                        item.setTagInfos(orderItemMap.get(item.getSkuId()).getTagInfos());
                    }
                });
            }
            refundApplyRecordVO.setRefundApplyRecordDetailVOList(refundApplyRecordDetailVOList);
            refundApplyRecordVO.setRefundProductCount(ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).mapToInt(OCMSAfterSaleApplyDetailVO::getCount).sum());
            // 退款总金额计算
            if(CollectionUtils.isNotEmpty(refundApplyRecordVO.getRefundApplyRecordDetailVOList())){
                refundApplyRecordVO.setRefundAmt(refundApplyRecordVO.getRefundApplyRecordDetailVOList()
                        .stream().filter(Objects::nonNull)
                        .mapToInt(RefundApplyRecordDetailVO::getTotalRefundAmt)
                        .filter(Objects::nonNull).sum());
            }
        }
        refundApplyRecordVO.setRefundPicList(afterSaleApplyVO.getRefundPicList());
        refundApplyRecordVO.setProcessDeadline(afterSaleApplyVO.getProcessDeadline());
        refundApplyRecordVO.setReturnGoodsStatus(afterSaleApplyVO.getReturnGoodsStatus());
        refundApplyRecordVO.setChannelExtRefundType(afterSaleApplyVO.getChannelExtRefundType());
        return refundApplyRecordVO;
    }

    private List<RefundingRecordVO> buildWaitAuditRefundList(OrderStatusLog orderStatusLog, List<OCMSAfterSaleApplyVO> afterSaleApplyVOList, Map<String, OCMSOrderItemVO> orderItemMap) {
        List<RefundingRecordVO> refundingRecordVOList = new ArrayList<>();
        Optional.ofNullable(afterSaleApplyVOList).map(List::stream).orElse(Stream.empty()).forEach(afterSaleApplyVO->{
            RefundingRecordVO refundApplyRecordVO = new RefundingRecordVO();
            refundApplyRecordVO.setServiceId(afterSaleApplyVO.getServiceId());
            refundApplyRecordVO.setAfterSaleId(afterSaleApplyVO.getAfterSaleId());
            refundApplyRecordVO.setIsAudit(afterSaleApplyVO.getIsAudit());
            refundApplyRecordVO.setStatus(afterSaleApplyVO.getStatus());
            refundApplyRecordVO.setApplyReason(afterSaleApplyVO.getApplyReason());
            refundApplyRecordVO.setAfsPattern(afterSaleApplyVO.getAfsPattern());
            refundApplyRecordVO.setCreateTime(afterSaleApplyVO.getCreateTime());
            refundApplyRecordVO.setUpdateTime(afterSaleApplyVO.getUpdateTime());
            refundApplyRecordVO.setAfsApplyType(afterSaleApplyVO.getApplyType());
            refundApplyRecordVO.setWhoApplyType(afterSaleApplyVO.getApplyUserType());
            refundApplyRecordVO.setRefundOptContent(RefundLog.buildRefundContent(orderStatusLog, afterSaleApplyVO));
            if (afterSaleApplyVO.getApplyType() != null) {
                AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(afterSaleApplyVO.getApplyType());
                refundApplyRecordVO.setRefundApplyType(WaitToAuditRefundGoodsOrderSubTypeEnum.getSubTypeCode(afterSaleTypeEnum));
            }
            List<OCMSAfterSaleApplyDetailVO> ocmsAfterSaleApplyDetailVOList = afterSaleApplyVO.getOcmsAfterSaleApplyDetailVOList();
            if (CollectionUtils.isNotEmpty(ocmsAfterSaleApplyDetailVOList)) {
                List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOList = ocmsAfterSaleApplyDetailVOList.stream()
                        .filter(Objects::nonNull)
                        .map(RefundApplyRecordDetailVO::buildRefundApplyRecordDetailVO)
                        .collect(Collectors.toList());
                // 将订单中的商品信息赋值到售后信息中
                if(MapUtils.isNotEmpty(orderItemMap)){
                    refundApplyRecordDetailVOList.forEach(item -> {
                        if(orderItemMap.containsKey(item.getSkuId())){
                            item.setPicUrl(orderItemMap.get(item.getSkuId()).getPicUrl());
                            item.setOrderItemOfflinePrice(orderItemMap.get(item.getSkuId()).getOfflinePrice());
                            item.setTagInfos(orderItemMap.get(item.getSkuId()).getTagInfos());
                        }
                    });
                }
                refundApplyRecordVO.setRefundApplyRecordDetailVOList(refundApplyRecordDetailVOList);
                refundApplyRecordVO.setRefundProductCount(ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).mapToInt(OCMSAfterSaleApplyDetailVO::getCount).sum());
                // 退款总金额计算
                if(CollectionUtils.isNotEmpty(refundApplyRecordVO.getRefundApplyRecordDetailVOList())){
                    refundApplyRecordVO.setRefundAmt(refundApplyRecordVO.getRefundApplyRecordDetailVOList()
                            .stream().filter(Objects::nonNull)
                            .mapToInt(RefundApplyRecordDetailVO::getTotalRefundAmt)
                            .filter(Objects::nonNull).sum());
                }
            }
            refundApplyRecordVO.setRefundPicList(afterSaleApplyVO.getRefundPicList());
            refundApplyRecordVO.setProcessDeadline(afterSaleApplyVO.getProcessDeadline());
            refundApplyRecordVO.setReturnGoodsStatus(afterSaleApplyVO.getReturnGoodsStatus());
            refundApplyRecordVO.setChannelExtRefundType(afterSaleApplyVO.getChannelExtRefundType());
            refundingRecordVOList.add(refundApplyRecordVO);
        });
        return refundingRecordVOList;
    }

    private boolean isRefundConcernStatusChange(OrderStatusLog orderStatusLog) {
        return orderStatusLog != null && (Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), orderStatusLog.getSourceStatus()) || Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), orderStatusLog.getTargetStatus()));
    }

    private OCMSAfterSaleApplyVO findNearestTimeAfterSale(Long orderStatusCreateTime, List<OCMSAfterSaleApplyVO> afterSaleApplyVOList, boolean isAuditRefund) {
        return afterSaleApplyVOList.stream()
                .filter(item -> !(isAuditRefund && item.isWait2Audit())) // 如果是审批类型orderStatusLog，去掉那些还在处于退款审批中的记录
                .min(Comparator.comparingDouble(item -> {
                    long time = isAuditRefund ? item.getUpdateTime() : item.getCreateTime();
                    return Math.abs(time - orderStatusCreateTime);
                })).orElse(null);
    }

    /**
     * 插入退差价到退款日志流
     *
     * @return
     */
    private List<RefundLog> addWeightRefundLog(OCMSOrderVO ocmsOrderVO) {
        List<OCMSAfterSaleApplyVO> weightRefundAfterSaleApplyList = ocmsOrderVO.getAfterSaleApplyVOList().stream()
                                                                               .filter(afs -> Objects.nonNull(afs.getAfsPattern()) && afs.getAfsPattern() == AfterSalePatternEnum.WEIGHT.getValue()
                                                                                       && AfterSaleApplyStatusEnum.AUDITED.getValue().equals(afs.getStatus()))
                                                                               .collect(Collectors.toList());
        List<RefundLog> refundLogs = new ArrayList<>();
        for (OCMSAfterSaleApplyVO applyVO : weightRefundAfterSaleApplyList) {
            RefundLog refundLog = new RefundLog();
            refundLog.setOperator("");
            refundLog.setOptTime(applyVO.getCreateTime());
            refundLog.setOptContent("商家按重量退差价");
            refundLog.setAuditType(OcmsRefundAuditType.WeightRefund.getCode());
            refundLog.setOperatorType(OperatorTypeEnum.enumOf(applyVO.getApplyUserType()).getValue());
            refundLog.setRefundAmount(applyVO.getRefundAmt());
            refundLog.setOptDesc(applyVO.getApplyReason());
            List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOS = Lists.newArrayList();
            for (OCMSAfterSaleApplyDetailVO ocmsAfterSaleApplyDetailVO : applyVO.getOcmsAfterSaleApplyDetailVOList()) {
                RefundApplyRecordDetailVO recordDetailVO = RefundApplyRecordDetailVO.buildRefundApplyRecordDetailVO(ocmsAfterSaleApplyDetailVO);
                // 如果是克重退款展示渠道退款价格，如果是部分退款老逻辑展示线下价格
                recordDetailVO.setRefundAmt(ocmsAfterSaleApplyDetailVO.getRefundAmt());
                recordDetailVO.setTotalRefundAmt(ocmsAfterSaleApplyDetailVO.getRefundAmt());
                refundApplyRecordDetailVOS.add(recordDetailVO);
            }
            refundLog.setRefundApplyRecordDetailVOList(refundApplyRecordDetailVOS);
            refundLog.setRefundPicList(applyVO.getRefundPicList());
            refundLogs.add(refundLog);
        }
        return refundLogs;
    }

    /**
     * 打印状态，40，45，99，999 都归类成“打印失败”
     * @param printResultDto
     * @return
     */
    private String generatePrintToast(OrderPrintResultDto printResultDto) {
        PrintOpLogStatusEnum printStatusEnum = PrintOpLogStatusEnum.enumOf(printResultDto.getPrintStatus());
        String timeoutTips = MccConfigUtil.getOrderPrintTimeoutTips();
        String failTips = MccConfigUtil.getOrderPrintFailTips();
        switch (printStatusEnum) {
            case PRINT_TIME_OUT:
                return timeoutTips;
            case SERVICE_NOT_AVAILABLE:
            case PRINTER_OFFLINE:
            case PRINTER_ABNORMAL:
            case UNKNOWN:
                return failTips;
            case PRINTING:
            case WAITING_FOR_PRINT:
            case PRINT_SUCCESS:
                return null;
            default:
                log.error("未知打印状态码:{}", printStatusEnum);
                return null;
        }
    }

    private List<TStoreConfig> queryDeliveryStoreConfigByOrderList(List<OrderVO> orderVOList) {
        if (CollectionUtils.isEmpty(orderVOList)) {
            return Collections.emptyList();
        }

        Long tenantId = orderVOList.get(0).getTenantId();
        Set<Long> storeIdSet = orderVOList.stream()
                                          .map(orderVO -> orderVO.getWarehouseId() != null ? orderVO.getWarehouseId() : orderVO.getStoreId())
                                          .collect(Collectors.toSet());

        if (tenantId <= 0 || CollectionUtils.isEmpty(storeIdSet)) {
            return Collections.emptyList();
        }

        BatchStoreConfigQueryResponse batchStoreConfigQueryResponse = tmsServiceWrapper.batchDeliveryStoreConfigSearch(tenantId, new ArrayList<>(storeIdSet));
        if (batchStoreConfigQueryResponse == null || batchStoreConfigQueryResponse.getStatus() == null
                || batchStoreConfigQueryResponse.getStatus().getCode() != 0) {
            return Collections.emptyList();
        }

        return Optional.ofNullable(batchStoreConfigQueryResponse)
                       .map(BatchStoreConfigQueryResponse::getTStoreConfigs)
                       .filter(CollectionUtils::isNotEmpty)
                       .orElse(Collections.emptyList());
    }

    private Map<Integer, Map<Integer, TAggDeliveryPlatformConfig>> toPlatformConfigMap(List<TAggDeliveryPlatformConfig> aggPlatformConfigs, List<Integer> platformList) {
        Map<Integer, Map<Integer, TAggDeliveryPlatformConfig>> platformConfigMap = new HashMap<>();
        ArrayListMultimap<Integer, TAggDeliveryPlatformConfig> platformChannelMultiMap = ArrayListMultimap.create();
        for (TAggDeliveryPlatformConfig config : aggPlatformConfigs) {
            if (!platformList.contains(config.getPlatformCode())) {
                continue;
            }
            platformChannelMultiMap.put(config.getChannelType() == null ? com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType.MEITUAN.getValue() : config.getChannelType(), config);
        }
        if (platformChannelMultiMap.isEmpty()) {
            return null;
        }
        for (Integer channelType : platformChannelMultiMap.keySet()) {
            List<TAggDeliveryPlatformConfig> configs = platformChannelMultiMap.get(channelType);
            if (CollectionUtils.isEmpty(configs)) {
                continue;
            }
            Map<Integer, TAggDeliveryPlatformConfig> configMap = new HashMap<>();
            for (TAggDeliveryPlatformConfig config : configs) {
                Integer platformId = AggDeliveryPlatformEnum.MALT_FARM.getCode();
                if (config.getPlatformCode() != null) {
                    platformId = config.getPlatformCode();
                }
                configMap.put(platformId, config);
            }
            platformConfigMap.put(channelType, configMap);
        }
        return platformConfigMap;
    }

    private void fillPlatformDeliveryOperateItem(TDeliveryDetail tDeliveryDetail, DistributeStatusEnum distributeStatusEnum, OrderVO orderVO, List<Integer> deliveryItemList, Map<Long, Set<Integer>> tStoreConfigChannelMap) {
        Long time = null;
        Long now = System.currentTimeMillis();
        Map<Integer, Integer> configMap = MccConfigUtil.getPlatformToSelfConfig();
        Long storeId = orderVO.getStoreId();
        if (orderVO.getWarehouseId() != null) {
            storeId = orderVO.getWarehouseId();
        }
        if (tDeliveryDetail != null && tDeliveryDetail.deliveryEntity != null) {
            time = tDeliveryDetail.createTime;
        }
        if (orderVO.getChannelId() == ChannelTypeEnum.MEITUAN.getChannelId()) {
            if (distributeStatusEnum.getValue() >= DistributeStatusEnum.SELF_DISTRIBUTE.getValue() && distributeStatusEnum.getValue() <= DistributeStatusEnum.RIDER_DELIVERED.getValue()) {
                return;
            }
            if (time == null) {
                // 拿不到时间,取订单创建时间
                time = orderVO.getCreateTime();
            }
            if (configMap.containsKey(orderVO.getChannelId())) {
                long subTime = now - time;
                long showTime = configMap.get(orderVO.getChannelId()) * 60 * 1000L;
                if (subTime < showTime) {
                    return;
                }
            }
        } else if (orderVO.getChannelId() == ChannelTypeEnum.ELEM.getChannelId()) {
            int deliveryExceptionCode = (tDeliveryDetail == null || tDeliveryDetail.deliveryExceptionCode == null) ? 0 : tDeliveryDetail.deliveryExceptionCode;
            Integer deliveryStatus = orderVO.getRealDistributeStatus();
            if (distributeStatusEnum.getValue() >= DistributeStatusEnum.SELF_DISTRIBUTE.getValue() && distributeStatusEnum.getValue() <= DistributeStatusEnum.RIDER_DELIVERED.getValue()) {
                return;
            }
            if (Objects.nonNull(deliveryStatus)) {
                if (deliveryStatus != TmsDeliveryStatusDescEnum.DELIVERY_FAILED.getCode() &&
                        deliveryStatus != TmsDeliveryStatusDescEnum.DELIVERY_CANCELLED.getCode() &&
                        deliveryStatus != TmsDeliveryStatusDescEnum.DELIVERY_REJECTED.getCode()) {
                    return;
                }
                if (deliveryExceptionCode != DeliveryExceptionCodeEnum.SELF_DELIVERY.getCode()) {
                    return;
                }
            } else {
                return;
            }
        } else if (orderVO.getChannelId() == ChannelTypeEnum.JD2HOME.getChannelId()) {
            if (distributeStatusEnum.getValue() != DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER.getValue()) {
                return;
            }
            if (time == null && orderVO.getPickCompleteTime() != null) {
                //取拣货完成时间
                time = orderVO.getPickCompleteTime();
            }
            if (time == null) {
                time = now;
            }
            if (configMap.containsKey(orderVO.getChannelId())) {
                long subTime = now - time;
                long showTime = configMap.get(orderVO.getChannelId()) * 60 * 1000L;
                if (subTime < showTime) {
                    return;
                }
            }
        } else {
            //其他平台不支持转自送
            return;
        }
        deliveryItemList.add(DeliveryOperateItemEnum.DELIVERY_TO_SELF.type);
        Set<Integer> deliveryPlatformSet = new HashSet<>();
        if (tStoreConfigChannelMap.containsKey(storeId)) {
            deliveryPlatformSet = tStoreConfigChannelMap.get(storeId);
        }
        if (deliveryPlatformSet.contains(AggDeliveryPlatformEnum.MALT_FARM.getCode())) {
            deliveryItemList.add(DeliveryOperateItemEnum.DELIVERY_TO_MALTFARM.type);
        }
        if (deliveryPlatformSet.contains(AggDeliveryPlatformEnum.DAP_DELIVERY.getCode())) {
            deliveryItemList.add(DeliveryOperateItemEnum.DELIVERY_TO_DAP.type);
        }
    }

    private void fillSelfDeliveryOperateItem(DistributeStatusEnum distributeStatusEnum, Map<Long, Set<Integer>> tStoreConfigChannelMap, List<Integer> deliveryItemList, OrderVO orderVO) {
        Long storeId = orderVO.getStoreId();
        if (distributeStatusEnum == DistributeStatusEnum.RIDER_DELIVERED) {
            return;
        }
        Set<Integer> deliveryPlatform = new HashSet<>();
        if (tStoreConfigChannelMap.containsKey(storeId)) {
            deliveryPlatform = tStoreConfigChannelMap.get(storeId);
        }
        if (deliveryPlatform.contains(AggDeliveryPlatformEnum.MALT_FARM.getCode())) {
            deliveryItemList.add(DeliveryOperateItemEnum.DELIVERY_TO_MALTFARM.type);
        }
        if (deliveryPlatform.contains(AggDeliveryPlatformEnum.DAP_DELIVERY.getCode())) {
            deliveryItemList.add(DeliveryOperateItemEnum.DELIVERY_TO_DAP.type);
        }
    }

    private List<OCMSOrderVO> needOrderProfit(Long tenantId, List<OCMSOrderVO> orderVOS) {
        List<OCMSOrderVO> orders = Fun.filter(orderVOS, orderVO -> orderVO.getOrderBizType().equals(OrderBizTypeEnum.MEITUAN_WAIMAI.getValue()));
        if (CollectionUtils.isNotEmpty(orders)) {
            TenantBusinessModeEnum tenantBizMode = tenantWrapper.getTenantBizMode(tenantId);
            if (TenantBusinessModeEnum.CONVENIENCE_STORE.equals(tenantBizMode) || TenantBusinessModeEnum.MTSG_FLAGSHIP_STORE.equals(tenantBizMode)) {
                return orders;
            }
        }
        return Collections.emptyList();
    }

    /**
     * 针对全部订单列表中的订单 VO 对象，获取优化的的订单状态描述.
     *
     * @param orderVo 订单 VO 对象
     * @return 优化的订单状态描述
     */
    private String getOrderOptStatusDesc(OrderVO orderVo) {
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.enumOf(orderVo.getOrderStatus());
        TmsDeliveryStatusDescEnum realDeliveryStatusEnum = null;
        if (Objects.nonNull(orderVo.getRealDistributeStatus())) {
            realDeliveryStatusEnum = TmsDeliveryStatusDescEnum.getFromCode(orderVo.getRealDistributeStatus());
        }
        DistributeStatusEnum distributeStatusEnum = null;
        if (Objects.nonNull(orderVo.getDistributeStatus())) {
            distributeStatusEnum = DistributeStatusEnum.enumOf(orderVo.getDistributeStatus());
        }
        // 判断是否在配送中
        boolean isInDelivery = (realDeliveryStatusEnum != null && realDeliveryStatusEnum.getCode() > TmsDeliveryStatusDescEnum.DELIVERY_LAUNCHED.getCode()) ||
                (distributeStatusEnum != null && distributeStatusEnum.getValue() > DistributeStatusEnum.UN_KNOWN.getValue());
        DeliveryStatusEnum pickStatusEnum = null;
        if (Objects.nonNull(orderVo.getPickStatus())) {
            pickStatusEnum = DeliveryStatusEnum.enumOf(orderVo.getPickStatus());
        }
        String desc = "";
        if (orderStatusEnum != null) {
            switch (orderStatusEnum) {
                case SUBMIT:
                case PAYING:
                case PAYED:
                    desc = OrderShowStatusConstants.WAIT_RECEIVE;
                    break;
                case MERCHANT_CONFIRMED:
                    desc = OrderShowStatusConstants.RECEIVED;
                    break;
                case PICKING:
                    desc = OrderShowStatusConstants.PICKING;
                    if (pickStatusEnum != null && pickStatusEnum.getValue() == DeliveryStatusEnum.PICKED.getValue()) {
                        //待自提
                        if (orderVo.getSelfFetchStatus() != null && orderVo.getSelfFetchStatus() == SelfFetchStatusEnum.WAIT_TO_SELF_FETCH.getValue()) {
                            desc = OrderShowStatusConstants.WAIT_SELF_FETCH;
                        } else if (isInDelivery) {
                            desc = orderVo.getDistributeStatusDesc();
                        } else {
                            desc = OrderShowStatusConstants.WAIT_DELIVERY;
                        }
                    }
                    break;
                case COMPLETED:
                    desc = OrderShowStatusConstants.COMPLETED;
                    break;
                case REFUND_APPLIED:
                case APPEAL_APPLIED:
                    desc = OrderShowStatusConstants.REFUNDING;
                    break;
                case CANCELED:
                    desc = OrderShowStatusConstants.CANCELED;
                    break;
                default:
                    // 兜底-取订单状态
                    desc = orderStatusEnum.getDesc();
            }
        }
        return StringUtils.defaultIfBlank(desc, "未知");
    }

    private ViewIdCondition buildViewIdConditionByOCMSOrderVO(OCMSOrderVO ocmsOrderVO) {
        ViewIdCondition viewIdCondition = new ViewIdCondition();
        viewIdCondition.setOrderBizType(ocmsOrderVO.getOrderBizType());
        viewIdCondition.setViewOrderId(ocmsOrderVO.getViewOrderId());
        return viewIdCondition;
    }

    private OCMSListViewIdConditionRequest buildOCMSListViewIdConditionRequestByOCMSOrderVOList(List<OCMSOrderVO> orders) {
        OCMSListViewIdConditionRequest request = new OCMSListViewIdConditionRequest();
        request.setViewIdConditionList(orders.stream().filter(Objects::nonNull)
                .map(this::buildViewIdConditionByOCMSOrderVO).collect(Collectors.toList()));
        request.setSortField(SortFieldEnum.ESTIMATED_ARRIVAL_TIME);
        request.setSort(SortByEnum.ASC);
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (identityInfo.getUser() != null) {
            request.setTenantId(identityInfo.getUser().getTenantId());
        }
        return request;
    }

    private List<OCMSOrderVO> filterInvalidOrder(List<OCMSOrderVO> ocmsOrderList) {
        if (CollectionUtils.isEmpty(ocmsOrderList)) {
            return Collections.emptyList();
        }
        return ocmsOrderList.stream()
                            .filter(ocmsOrderVO -> Objects.nonNull(ocmsOrderVO.getTenantId()) && Objects.nonNull(ocmsOrderVO.getOrderId()))
                            .collect(Collectors.toList());
    }


    private int collectionSize(Collection<?> collection) {
        return Optional.ofNullable(collection).map(Collection::size).orElse(0);
    }

    private void checkViewResponseCount(OCMSListViewIdConditionRequest viewIdConditionRequest, OCMSListViewIdConditionResponse viewIdConditionResponse) {
        if (collectionSize(viewIdConditionRequest.getViewIdConditionList()) != collectionSize(viewIdConditionResponse.getOcmsOrderList())){
            MetricHelper.build().name("waitToPickOrderCountNotEqual").count();
            log.info("查询订单详情返回数量不等,viewId:{}, responseOrder:{}",
                Optional.ofNullable(viewIdConditionRequest.getViewIdConditionList()).map(List::stream).orElse(
                    Stream.empty()).map(ViewIdCondition::getViewOrderId).collect(Collectors.toList()),
                Optional.ofNullable(viewIdConditionResponse.getOcmsOrderList()).map(List::stream).orElse(Stream.empty()).map(
                    OnlineBaseOrderVO::getViewOrderId).collect(Collectors.toList()));
        }
    }

    private void setOrderListResponseViewStatus(OrderListResponse response) {
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getOrderList())) {
            return;
        }
        for (OrderVO each : response.getOrderList()) {
            each.setViewStatus(OrderViewStatusEnum.WAIT_TO_PICK_ORDER.getCode());
        }
    }

    private void setErpCode(OrderListResponse response) {
        if (Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getOrderList())) {
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            List<Long> storeIdList = identityInfo.getStoreIdList();
            Long tenantId = identityInfo.getUser().getTenantId();
            Map<Long, String> shopId2ErpShopCode = poiRemoteService.queryShopId2ErpShopCode(tenantId, storeIdList);
            response.getOrderList().forEach(orderVO -> orderVO.setErpShopCode(StringUtils.isBlank(shopId2ErpShopCode.get(orderVO.getStoreId())) ?
                Strings.EMPTY :
                shopId2ErpShopCode.get(orderVO.getStoreId())));
        }
    }

    private OCMSListViewIdConditionRequest buildOCMSListViewIdConditionRequestByTOrderIdentifier(List<TOrderIdentifier> tOrderIdentifiers) {
        OCMSListViewIdConditionRequest request = new OCMSListViewIdConditionRequest();
        request.setViewIdConditionList(tOrderIdentifiers.stream()
                .filter(Objects::nonNull)
                .map(to -> new ViewIdCondition(to.getOrderBizTypeCode(), to.getChannelOrderId()))
                .collect(Collectors.toList()));
        request.setSortField(SortFieldEnum.ORDER_CREATE_TIME);
        request.setSort(SortByEnum.DESC);
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        request.setTenantId(identityInfo.getUser().getTenantId());
        return request;
    }

    private void setDeliveryErrorListSort(OrderListResponse orderListResponse) {
        if (orderListResponse != null && orderListResponse.getOrderList() != null) {
            orderListResponse.getOrderList().sort(Comparator.comparing(o -> Optional.ofNullable(o.getEstimateArriveTimeEnd()).orElse(Long.MIN_VALUE)));
        }
    }

    private void setOrderListResponseViewStatusByExceptionSubType(OrderListResponse response, List<TOrderIdentifier> tOrderIdentifiers) {
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getOrderList()) || CollectionUtils.isEmpty(tOrderIdentifiers)) {
            return;
        }
        Map<String, TOrderIdentifier> tOrderIdentifierMap = tOrderIdentifiers.stream().collect(Collectors.toMap(o -> o.getChannelOrderId() + "_" + o.getOrderBizTypeCode(), o -> o, (f, s) -> f));
        for (OrderVO each : response.getOrderList()) {
            String tOrderIdentifierMapKey = each.getChannelOrderId() + "_" + ChannelOrderConvertUtils.sourceMid2Biz(each.getChannelId());
            if (tOrderIdentifierMap.containsKey(tOrderIdentifierMapKey)) {
                TOrderIdentifier tOrderIdentifier = tOrderIdentifierMap.get(tOrderIdentifierMapKey);
                DeliveryExceptionSubTypeEnum deliveryExceptionSubTypeEnum = DeliveryExceptionSubTypeEnum.deliveryStatusCodeValueOfWithOutAll(
                        tOrderIdentifier.getExceptionTypeCode(),
                        tOrderIdentifier.getDeliveryExceptionCode(),
                        tOrderIdentifier.getDeliveryStatus().getCode());
                each.setViewStatus(getOrderViewStatusByExceptionOrderSubType(deliveryExceptionSubTypeEnum));
                each.setAllowLatestAuditTime(tOrderIdentifier.getAllowLatestAuditTime());
                each.setExceptionCode(tOrderIdentifier.getDeliveryExceptionCode());
            }
        }
    }

    private Integer getOrderViewStatusByExceptionOrderSubType(DeliveryExceptionSubTypeEnum deliveryExceptionSubTypeEnum) {
        if (deliveryExceptionSubTypeEnum == null) {
            return null;
        }
        int orderViewStatus;
        switch (deliveryExceptionSubTypeEnum) {
            case NO_RIDER_ACCEPT:
                orderViewStatus = OrderViewStatusEnum.NO_RIDER_ACCEPT.getCode();
                break;
            case NO_ARRIVAL_STORE:
                orderViewStatus = OrderViewStatusEnum.NO_ARRIVAL_STORE.getCode();
                break;
            case NO_RIDER_TAKE_GOODS:
                orderViewStatus = OrderViewStatusEnum.NO_RIDER_TAKE_GOODS.getCode();
                break;
            case DELIVERY_TIMEOUT:
                orderViewStatus = OrderViewStatusEnum.DELIVERY_TIMEOUT.getCode();
                break;
            case SYSTEM_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.SYSTEM_EXCEPTION.getCode();
                break;
            case REPORT_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.REPORT_EXCEPTION.getCode();
                break;
            case TAKE_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.TAKE_EXCEPTION.getCode();
                break;
            default:
                return null;
        }
        return orderViewStatus;
    }

    /**
     * 仅保留订单的部分可操作按钮.
     *
     * @param orderListResponse  订单列表
     * @param keepedOperateItems 需要保留的操作
     */
    private void justKeepPartOperateItems(OrderListResponse orderListResponse, List<OrderCouldOperateItem> keepedOperateItems) {
        List<OrderVO> orderVOs = orderListResponse.getOrderList();
        log.info("justKeepPartOperateItems: orderVOs: {}", JSONObject.toJSONString(orderVOs));
        if (CollectionUtils.isEmpty(orderVOs) || CollectionUtils.isEmpty(keepedOperateItems)) {
            return;
        }
        List<Integer> keepOperateCodes = keepedOperateItems.stream().map(OrderCouldOperateItem::getValue).collect(Collectors.toList());
        orderVOs.forEach(order ->{
            if (order.getOrderCouldOperateItems() == null) {
                order.setOrderCouldOperateItems(keepOperateCodes);
                return;
            }
            order.getOrderCouldOperateItems().retainAll(keepOperateCodes);
        });
    }

    private DeliveryErrorSubTypeCountResponse buildDeliveryErrorSubTypeCountResponse(DeliveryExceptionOrderSubTypeCountResponse response) {
        DeliveryErrorSubTypeCountResponse deliveryErrorSubTypeCountResponse = new DeliveryErrorSubTypeCountResponse();
        deliveryErrorSubTypeCountResponse.setAllSubTypeCount(response.getAllSubTypeCount());
        deliveryErrorSubTypeCountResponse.setNoRiderAcceptCount(response.getNoRiderAcceptCount());
        deliveryErrorSubTypeCountResponse.setNoArrivalStoreCount(response.getNoArrivalStoreCount());
        deliveryErrorSubTypeCountResponse.setNoRiderTakeGoodsCount(response.getNoRiderTakeGoodsCount());
        deliveryErrorSubTypeCountResponse.setDeliveryTimeoutCount(response.getDeliveryTimeoutCount());
        deliveryErrorSubTypeCountResponse.setSystemExceptionCount(response.getSystemExceptionCount());
        deliveryErrorSubTypeCountResponse.setTakeGoodsExceptionCount(response.getTakeGoodsExceptionCount());
        deliveryErrorSubTypeCountResponse.setReportExceptionCount(response.getReportExceptionCount());
        return deliveryErrorSubTypeCountResponse;
    }

    public CommonResponse<DeliveryErrorSubTypeCountResponse> queryDeliveryErrorSubTypeCountFallback() {
        log.info("MiniAppOrderService.queryDeliveryErrorSubTypeCount  调用降级方法");
        throw new CommonLogicException(ResultCodeEnum.RETRY_INNER_FAIL);
    }

    public CommonResponse<OrderListResponse> queryWaitToPickOrderFallback(QueryWaitToPickOrderRequest request) {
        log.info("MiniAppOrderService.queryWaitToPickOrder  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCodeEnum.RETRY_INNER_FAIL);
    }

    public CommonResponse<OrderListResponse> queryDeliveryErrorBySubTypeFallback(QueryDeliveryErrorOrderBySubTypeMiniAppRequest request) {
        log.info("MiniAppOrderService.queryDeliveryErrorBySubType  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCodeEnum.RETRY_INNER_FAIL);
    }

    public CommonResponse<OrderListResponse> orderListFallback(OrderListMiniAppRequest request) {
        log.info("MiniAppOrderService.orderList  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCodeEnum.RETRY_INNER_FAIL);
    }

}
