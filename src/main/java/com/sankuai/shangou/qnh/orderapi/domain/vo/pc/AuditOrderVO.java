package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/8 11:15
 * @Description:
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class AuditOrderVO {

    /**
     * 订单ID
     */
    private String orderId;


    /**
     * 渠道编码
     */
    private String channelId;

    /**
     *  渠道名称
     */
    private String channelName;


    /**
     * 门店名称
     */
    private String poiName;


    /**
     * 退款类型
     */
    private String refundType;


    /**
     * 退款金额
     */
    private String refundAmt;


    /**
     * 实付金额
     */
    private String paidAmt;


    /**
     * 订单状态
     */
    private String status;


    /**
     * 发起者
     */
    private String applicant;


    /**
     * 退款类型id
     */
    private String refundTagId;


    /**
     * 订单创建时间
     */
    private String createTime;


    /**
     * 收货人姓名
     */
    private String receiverName;


    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 收货人地址
     */
    private String receiveAddress;

    /**
     * 服务单号
     */
    private String serviceId;

    /**
     * 售后请求状态
     */
    private String afsApplyType;

    /**
     * 申请退款原因
     */
    private String refundReason;

    private String currentAfsType;

    /**
     * 渠道流水号
     */
    private Long orderSerialNumber;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库名
     */
    private String warehouseName;

    /**
     * 是否为美团名酒馆订单，true：是
     */
    private Boolean isMtFamousTavern;

    /**
     * 是否为美团发财酒订单，true：是
     */
    private Boolean isFacaiWine;
}
