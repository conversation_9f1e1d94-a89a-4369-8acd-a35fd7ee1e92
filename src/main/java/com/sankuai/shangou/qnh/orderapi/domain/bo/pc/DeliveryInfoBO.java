package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import lombok.Data;

@Data
public class DeliveryInfoBO {

    private Integer deliveryChannel;

    private Integer status;

    private Boolean activeStatus;

    private Double deliveryFee;

    private Double tipAmount;

    private Integer deliveryCount;

    private String riderName;

    private String riderPhone;

    public static DeliveryInfoBO toDeliveryBO(TDeliveryOrder deliveryOrder){
        if(deliveryOrder==null){
            return null;
        }
        DeliveryInfoBO deliveryInfoBO=new DeliveryInfoBO();
        deliveryInfoBO.setDeliveryFee(deliveryOrder.getDeliveryFee());
        deliveryInfoBO.setDeliveryChannel(deliveryOrder.getDeliveryChannel());
        deliveryInfoBO.setDeliveryCount(deliveryOrder.getDeliveryCount());
        deliveryInfoBO.setStatus(deliveryOrder.getStatus());
        deliveryInfoBO.setActiveStatus(deliveryOrder.getActiveStatus());
        deliveryInfoBO.setTipAmount(deliveryOrder.getTipAmount());
        deliveryInfoBO.setRiderName(deliveryOrder.getRiderName());
        deliveryInfoBO.setRiderPhone(deliveryOrder.getRiderPhone());
        return deliveryInfoBO;
    }


}
