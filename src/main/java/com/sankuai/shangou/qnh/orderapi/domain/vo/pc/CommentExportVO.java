package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.google.common.base.Joiner;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.CommentReplyStatusEnum;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.meituan.shangou.saas.common.file.ExcelFieldAnnotation;
import com.sankuai.sgfulfillment.comment.thrift.common.CommentMatchOrderStatusEnum;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommentBO;
import com.sankuai.shangou.qnh.orderapi.enums.pc.poi.StatusType;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;


@TypeDoc(
        description = "评价导出VO",
        authors = {"hejunliang"}
)
@Data
public class CommentExportVO {

    @ExcelFieldAnnotation(hidden = true)
    private Long poiId;

    @ExcelFieldAnnotation(alias = "渠道")
    private String channelName;

    @ExcelFieldAnnotation(alias = "ERP门店编码")
    private String erpShopCode;

    @ExcelFieldAnnotation(alias = "门店编码")
    private String shopId;

    @ExcelFieldAnnotation(alias = "门店名称")
    private String poiName;

    @ExcelFieldAnnotation(alias = "订单号")
    private String channelOrderId;

    @ExcelFieldAnnotation(alias = "订单是否系统匹配")
    private String isSystemMatchOrder;

    @ExcelFieldAnnotation(alias = "订单评分")
    private String orderScore;

    @ExcelFieldAnnotation(alias = "质量评分")
    private String qualityScore;

    @ExcelFieldAnnotation(alias = "包装评分")
    private String packingScore;

    @ExcelFieldAnnotation(alias = "配送评分")
    private String deliveryScore;

    @ExcelFieldAnnotation(alias = "订单商品")
    private String orderItemList;

    @ExcelFieldAnnotation(alias = "踩商品")
    private String criticItemList;

    @ExcelFieldAnnotation(alias = "赞商品")
    private String praiseItemList;

    @ExcelFieldAnnotation(alias = "用户评价")
    private String commentContent;

    @ExcelFieldAnnotation(alias = "评价时间")
    private String commentTime;

    @ExcelFieldAnnotation(alias = "用户追评")
    private String addCommentContent;

    @ExcelFieldAnnotation(alias = "追评时间")
    private String addCommentTime;

    @ExcelFieldAnnotation(alias = "商家回复")
    private String replyContent;

    @ExcelFieldAnnotation(alias = "图片")
    private String commentPictures;

    @ExcelFieldAnnotation(alias = "评价状态")
    private String isValid;

    /**
     * 评价溯源系统匹配订单状态
     */
    private String matchOrderStatus;

    /**
     * 渠道id
     */
    private Integer channelId; 

    public static CommentExportVO build(CommentBO commentBO) {
        CommentExportVO commentExportVO = new CommentExportVO();
        commentExportVO.setPoiId(commentBO.getStoreId());
        commentExportVO.setShopId(commentBO.getStoreId().toString());
        DynamicChannelType channelTypeEnum = DynamicChannelType.findOf(commentBO.getChannelId());
        commentExportVO.setChannelName(channelTypeEnum != null ?
                channelTypeEnum.getDesc() : StringUtils.EMPTY);
        commentExportVO.setPoiName(null);
        commentExportVO.setOrderScore(commentBO.getOrderScore() != null ?
                String.valueOf(commentBO.getOrderScore()) : StringUtils.EMPTY);
        commentExportVO.setQualityScore(commentBO.getQualityScore() != null ?
                String.valueOf(commentBO.getQualityScore()) : StringUtils.EMPTY);
        commentExportVO.setPackingScore(commentBO.getPackingScore() != null ?
                String.valueOf(commentBO.getPackingScore()) : StringUtils.EMPTY);
        commentExportVO.setDeliveryScore(commentBO.getDeliveryScore() != null ?
                String.valueOf(commentBO.getDeliveryScore()) : StringUtils.EMPTY);
        commentExportVO.setOrderItemList(CollectionUtils.isNotEmpty(commentBO.getOrderItemList()) ?
                Joiner.on(", ").join(commentBO.getOrderItemList()) : StringUtils.EMPTY);
        commentExportVO.setCriticItemList(CollectionUtils.isNotEmpty(commentBO.getCriticItemList()) ?
                Joiner.on(", ").join(commentBO.getCriticItemList()) : StringUtils.EMPTY);
        commentExportVO.setPraiseItemList(CollectionUtils.isNotEmpty(commentBO.getPraiseItemList()) ?
                Joiner.on(", ").join(commentBO.getPraiseItemList()) : StringUtils.EMPTY);
        commentExportVO.setCommentContent(StringUtils.isNotEmpty(commentBO.getCommentContent()) ?
                commentBO.getCommentContent() : StringUtils.EMPTY);
        commentExportVO.setCommentTime(commentBO.getCommentTime() != null ?
                DateUtil.formatLocalDateTime(commentBO.getCommentTime(), Constants.DateFormats.DAY_FORMAT) : StringUtils.EMPTY);
        commentExportVO.setAddCommentContent(StringUtils.isNotEmpty(commentBO.getAddCommentContent()) ?
                commentBO.getAddCommentContent() : StringUtils.EMPTY);
        commentExportVO.setAddCommentTime(commentBO.getAddCommentTime() != null ?
                DateUtil.formatLocalDateTime(commentBO.getAddCommentTime(), Constants.DateFormats.DAY_FORMAT) : StringUtils.EMPTY);
        commentExportVO.setReplyContent(getReplyContent(commentBO));
        commentExportVO.setChannelOrderId(commentBO.getChannelOrderId());
        commentExportVO.setIsValid(commentBO.getIsValid()? StatusType.VALID.getDesc(): StatusType.INVALID.getDesc());

        commentExportVO.setCommentPictures(CollectionUtils.isNotEmpty(commentBO.getCommentPictures()) ?
                Joiner.on(", ").join(commentBO.getCommentPictures()) : StringUtils.EMPTY);
        commentExportVO.setMatchOrderStatus(commentBO.getMatchOrderStatus());
        commentExportVO.setChannelId(commentBO.getChannelId());
        return commentExportVO;
    }

    public HashMap<String, String> convertToCommentExportData(boolean isSuperMarket, boolean exportMatchOrder) {
        HashMap<String, String> dataMap = new LinkedHashMap<>();
        dataMap.put("渠道", this.channelName);
        if (isSuperMarket){
            dataMap.put("ERP门店编码", this.erpShopCode);
        }
        dataMap.put("门店编码", this.shopId);
        dataMap.put("门店名称", this.poiName);
        // 当需要导出时，根据评价溯源状态来导出【订单号】和【订单是否系统匹配】字段
        if (exportMatchOrder) {
            dataMap.put("订单号", this.channelOrderId);
            boolean isMatchOrder = CommentMatchOrderStatusEnum.isSystemMatchOrder(this.matchOrderStatus);
            if (isMatchOrder) {
                dataMap.put("订单是否系统匹配", "是");
            } else {
                // 如果是溯源的渠道(饿了么、美团外卖)，订单号为空且状态【未溯源到订单】,则设置为空，否则为否
                boolean isSystemMatchOrderChannel = DynamicChannelType.MEITUAN
                        .equals(DynamicChannelType.findOf(channelId))
                        || DynamicChannelType.ELEM.equals(DynamicChannelType.findOf(channelId));
                if (isSystemMatchOrderChannel && StringUtils.isBlank(this.channelOrderId)) {
                    dataMap.put("订单是否系统匹配", StringUtils.EMPTY);
                } else {
                    // 不为系统匹配的溯源状态时，当存在订单号或者不为溯源渠道(饿了么、美团外卖)，则设置为【否】
                    dataMap.put("订单是否系统匹配", "否");
                }
            }
        } else {
            // 当不需要导出溯源的订单时，如果订单号是溯源的订单号，则导出时订单号字段为空
            dataMap.put("订单号", CommentMatchOrderStatusEnum.isSystemMatchOrder(this.matchOrderStatus) ? StringUtils.EMPTY
                    : this.channelOrderId);
        }
        dataMap.put("订单评分", this.orderScore);
        dataMap.put("质量评分", this.qualityScore);
        dataMap.put("包装评分", this.packingScore);
        dataMap.put("配送评分", this.deliveryScore);
        dataMap.put("用户评价", this.commentContent);
        dataMap.put("评价时间", this.commentTime);
        dataMap.put("商家回复", this.replyContent);
        dataMap.put("订单商品", this.orderItemList);
        dataMap.put("踩商品", this.criticItemList);
        dataMap.put("赞商品", this.praiseItemList);
        dataMap.put("用户追评", this.addCommentContent);
        dataMap.put("追评时间", this.addCommentTime);
        dataMap.put("评价图片", this.commentPictures);
        dataMap.put("评价状态", this.isValid);
        return dataMap;
    }

    private static String getReplyContent(CommentBO commentBO) {
        if (CommentReplyStatusEnum.REPLIED.name().equals(commentBO.getReplyStatus())) {
            return StringUtils.isNotEmpty(commentBO.getReplyContent()) ? commentBO.getReplyContent() : "商家已通过其他后台回复";
        } else {
            return StringUtils.isNotEmpty(commentBO.getReplyDraft()) ? commentBO.getReplyDraft() : StringUtils.EMPTY;
        }
    }
}
