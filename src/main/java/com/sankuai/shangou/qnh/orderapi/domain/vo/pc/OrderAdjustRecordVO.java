package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2020/4/16
 * @description 订单调整记录
 */
@TypeDoc(
        description = "订单调整记录"
)
@ApiModel("订单调整记录")
@Data
@ToString
public class OrderAdjustRecordVO {

    @FieldDoc(
            description = "调整者"
    )
    @ApiModelProperty(value = "调整者", required = true)
    private String operator;

    @FieldDoc(
            description = "调整时间(格式YYYY.MM.DD hh:mm:ss)"
    )
    @ApiModelProperty(value = "调整时间(格式YYYY.MM.DD hh:mm:ss)", required = true)
    private String updateTime;

    @FieldDoc(
            description = "调整原因"
    )
    @ApiModelProperty(value = "调整原因", required = true)
    @NotNull(message = "调整原因")
    private String comments;


    @FieldDoc(
            description = "调整记录列表"
    )
    @ApiModelProperty(value = "调整记录", required = true)
    private List<OrderAdjustRecord> orderAdjustRecordList;


    @TypeDoc(
            description = "调整记录"
    )
    @ApiModel("调整记录")
    @Data
    public static class OrderAdjustRecord {

        @FieldDoc(
                description = "调整对象id"
        )
        @ApiModelProperty(value = "调整对象id", required = true)
        private String adjustTargetId;

        @FieldDoc(
                description = "调整对象类型(1: 商品维度)"
        )
        @ApiModelProperty(value = "调整对象类型(1: 商品维度)", required = true)
        private String adjustTargetType;

        @FieldDoc(
                description = "调整对象名称"
        )
        @ApiModelProperty(value = "调整对象名称", required = true)
        private String adjustTargetName;


        @FieldDoc(
                description = "具体调整项列表"
        )
        @ApiModelProperty(value = "具体调整项列表", required = true)
        private List<AdjustItem> adjustItemList;
    }


    @TypeDoc(
            description = "具体调整项目"
    )
    @ApiModel("具体调整项目")
    @Data
    public static class AdjustItem {

        @FieldDoc(
                description = "调整项目(1: 摊位id,2: 线下售卖价格)"
        )
        @ApiModelProperty(value = "调整项目(1: 摊位id,2: 线下售卖价格)", required = true)
        private String adjustTargetItem;


        @FieldDoc(
                description = "调整前值"
        )
        @ApiModelProperty(value = "调整前值", required = true)
        private String beforeValue;


        @FieldDoc(
                description = "调整后值"
        )
        @ApiModelProperty(value = "调整后值", required = true)
        private String afterValue;

    }

}
