package com.sankuai.shangou.qnh.orderapi.remote;

import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.infra.osw.api.org.TOrgResourceService;
import com.sankuai.shangou.infra.osw.api.org.TOrgService;
import com.sankuai.shangou.infra.osw.api.org.dto.request.DepartmentDetailRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.response.DepartmentDetailDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.OrgDetailDTO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrgParentBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class OrgRemoteService {

    @Resource
    private TOrgService tOrgService;

    @Resource
    private TOrgResourceService tOrgResourceService;

    public OrgParentBO queryParentOrg(Long tenantId, Long deptId) {
        try {
            DepartmentDetailRequest request = new DepartmentDetailRequest();
            request.setTenantId(tenantId);
            request.setDepartmentId(deptId);
            TResult<OrgDetailDTO> result = tOrgService.queryDepartmentDetail(request);
            if (result != null && result.isSuccess()) {
                OrgDetailDTO orgDetailDTO = result.getData();
                DepartmentDetailDTO departmentDetailDTO = orgDetailDTO != null ? orgDetailDTO.getDepartment() : null;
                if (departmentDetailDTO != null) {
                    return OrgParentBO.builder()
                            .parentId(departmentDetailDTO.getParentId())
                            .parentName(departmentDetailDTO.getParentName())
                            .build();
                }
            }
        } catch (Exception ex) {
            log.error("OrgRemoteService.queryDepartmentDetail error", ex);
        }
        return null;

    }


    public List<Long> queryFranchiseeIdByEmpId(Long tenantId, Long empId) throws TException {
        TResult<List<Long>> result = tOrgResourceService.queryFranchiseeIdByEmpId(tenantId, empId);
        if (result != null && result.isSuccess()) {
            return result.getData();
        }
        return new ArrayList<>();

    }







}
