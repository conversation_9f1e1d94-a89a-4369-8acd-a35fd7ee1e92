package com.sankuai.shangou.qnh.orderapi.enums.store;
// Copyright (C) 2019 Meituan
// All rights reserved

import com.sankuai.shangou.qnh.orderapi.utils.store.ApiMethodParamThreadLocal;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * class: AppAuthIdConstants
 * date: 2019-09-03 17:13:32
 * desc:
 */
@Slf4j
public enum AppAuthIdConstants {

    /**
     * 履约助手
     */
    LV_YUE_ZHU_SHOU(1, 0, "履约助手"),
    SHU_GUO_PAI(5, 0,  "牵牛花APP"),
    PDA_STORE(71, 5,  "pda-门店"),
    PDA_WAREHOUSE_STORE(72, 5,  "pda-共享仓门店"),

    SCALE(7, 0, "称"),

    /**
     * 8、9、11、12、13 为 5 的权限子应用
     */
    AREA_CENTRAL_WAREHOUSE(8, 5, "区域中心仓"),
    SHARED_WAREHOUSE(9, 5, "共享前置仓"),
    STORE(11, 5, "门店"),
    SHARED_WAREHOUSE_STORE(12, 5, "共享前置仓门店"),
    FULL_STORE(13, 5, "全部门店/总部"),
    ;

    private static final Map<Integer, AppAuthIdConstants> CODE_APP_CONSTANTS_MAP = new HashMap<>();
    private final int val;
    private final int parentAppId;
    private final String msg;

    AppAuthIdConstants(int val, int parentAppId, String msg) {
        this.val = val;
        this.parentAppId = parentAppId;
        this.msg = msg;
    }

    static {
        Arrays.stream(values())
                .forEach(it -> CODE_APP_CONSTANTS_MAP.put(it.val(), it));
    }

    public int val() {
        return val;
    }

    public int parentAppId() {
        return parentAppId;
    }

    public String getMsg() {
        return msg;
    }

    public boolean isChildApp() {
        return parentAppId > 0;
    }

    public static AppAuthIdConstants fromCode(String code) {
        if (StringUtils.isBlank(code)) {
            return AppAuthIdConstants.LV_YUE_ZHU_SHOU;
        }

        try {
            return fromCode(Integer.parseInt(code));
        } catch (NumberFormatException e) {
            log.error("format app code failed, code={}, will use LV_YUE_ZHU_SHOU as default", code, e);
            return AppAuthIdConstants.LV_YUE_ZHU_SHOU;
        }
    }

    public static AppAuthIdConstants fromCode(int code) {
        return CODE_APP_CONSTANTS_MAP.getOrDefault(code, AppAuthIdConstants.LV_YUE_ZHU_SHOU);
    }

    /**
     * 获取权限主应用 ID
     *
     * @return 权限主应用 ID
     */
    public static int getAppCode() {
        int authId = Integer.parseInt(ApiMethodParamThreadLocal.getIdentityInfo().getAuthId());
        AppAuthIdConstants appAuthIdConstants = fromCode(authId);
        if (appAuthIdConstants.isChildApp()) {
            return appAuthIdConstants.parentAppId();
        }
        return appAuthIdConstants.val();
    }

}
