package com.sankuai.shangou.qnh.orderapi.enums.store;

public enum FulfillWorkOrderStatusEnum {

    /**
     * 初始状态
     */
    INITIAL(0),

    /**
     * 待接单
     */
    WAIT_TO_ACCEPT(5),

    /**
     * 待下发
     */
    WAIT_TO_PUSH(10),

    /**
     * 已下发
     */
    PUSH_DONE(20),

    /**
     * 待过机，待同步状态到外部系统
     */
    WAIT_TO_SYNC(60),

    /**
     * 拣货订单已完成
     */
    DONE(100),

    /**
     * 已过期
     */
    EXPIRED(80),

    /**
     * 已取消
     */
    CANCELED(90);
    private int code;

    FulfillWorkOrderStatusEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static boolean isFinalStatus(int code) {
        return code == WAIT_TO_SYNC.getCode()
                || code == DONE.getCode()
                || code == EXPIRED.getCode()
                || code == CANCELED.getCode();

    }


}
