package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.meituan.shangou.saas.order.platform.enums.OrderPosStatusEnum;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
public enum PosStatusEnum {

    ALL(-1, "全部"),

    NOT_NEED_POS(30, "无需核销"),

    WAITING_POS(1, "未核销"),

    POS_SUCCESS(10, "已核销"),

    POS_FAIL(20, "核销失败");

    private int code;

    private String desc;

    PosStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PosStatusEnum converterFromOrderPosStatus(Integer orderPostStatus){
        if (Objects.isNull(orderPostStatus)){
            return null;
        }
        if (Objects.equals(orderPostStatus, OrderPosStatusEnum.DOWN.getValue())){
            return WAITING_POS;
        }
        return getByCode(orderPostStatus);
    }



    public static PosStatusEnum getByCode(int code) {
        for (PosStatusEnum e : PosStatusEnum.values()) {
            if (e.getCode() == code && code != 0) {
                return e;
            }
        }
        return null;
    }

    public static List<UiOption> toOptions() {
        return Arrays.asList(PosStatusEnum.values()).stream()
                .map(e -> new UiOption(String.valueOf(e.getCode()), e.getDesc())).collect(Collectors.toList());
    }


}
