package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.shangou.saas.tenant.thrift.dto.booth.BoothInfoDto;
import lombok.Data;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-03-06
 **/
@Data
public class BoothInfoVo {

    /**
     * 摊位Id
     */
    private Long boothId;

    /**
     * 所属部门Id
     */
    private Long departmentId;

    /**
     * 所属门店
     */
    private Long poiId;

    /**
     * 所属租户
     */
    private Long tenantId;

    /**
     * 摊位状态
     */
    private Integer status;

    /**
     * 摊位名称
     */
    private String boothName;


    public BoothInfoVo build(BoothInfoDto boothInfoDto){

        this.boothId = boothInfoDto.getBoothId();
        this.departmentId = boothInfoDto.getDepId();
        this.poiId = boothInfoDto.getPoiId();
        this.tenantId = boothInfoDto.getTenantId();
        this.status = boothInfoDto.getBoothStatus();
        this.boothName = boothInfoDto.getBoothName();
        return this;
    }


}
