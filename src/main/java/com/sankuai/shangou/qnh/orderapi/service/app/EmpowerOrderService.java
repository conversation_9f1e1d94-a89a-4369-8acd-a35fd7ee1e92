package com.sankuai.shangou.qnh.orderapi.service.app;


import com.dianping.cat.util.MetricHelper;
import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.shangou.saas.order.management.client.enums.*;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderListRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.*;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.ImportRefundExchangeInfoRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.*;
import com.sankuai.shangou.qnh.orderapi.enums.app.*;
import com.sankuai.shangou.qnh.orderapi.enums.pda.OrderTabSubTypeEnum;
import com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.remote.OrderBizRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.TenantRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.common.*;
import com.sankuai.shangou.qnh.orderapi.service.common.query.*;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;



/**
 * @program: qnh_order_api
 * @description:
 * @author: jinyi
 * @create: 2023-10-19 19:28
 **/
@Service
@Slf4j
public class EmpowerOrderService {
    @Resource
    private Wait2ConfirmQueryOrderService wait2ConfirmQueryOrderService;
    @Resource
    private Wait2DeliveryQueryOrderService wait2DeliveryQueryOrderService;
    @Resource
    private Wait2PickQueryOrderService wait2PickQueryOrderService;
    @Resource
    private Wait2SelfFetchQueryOrderService wait2SelfFetchQueryOrderService;
    @Resource
    private DeliveryErrorQueryOrderService deliveryErrorQueryOrderService;
    @Resource
    private AppCacheListQueryOrderService appCacheListQueryOrderService;
    @Resource
    private AppCacheChangeQueryOrderService appCacheChangeQueryOrderService;
    @Resource
    private AfterSaleQueryOrderService afterSaleQueryOrderService;
    @Resource
    private OrderListSearchOrderService orderListSearchOrderService;

    @Resource
    private TenantRemoteService tenantWrapper;
    @Autowired
    private OrderBizRemoteService orderBizRemoteService;
    public  boolean queryFromNewOrderService(IdentityInfo identityInfo){
        long tenantId = identityInfo.getUser().getTenantId();
        if (MccConfigUtil.isDrunkHorseTenant(tenantId)){
            return false;
        }
        if (identityInfo.isFullStoreMode()){
            return false;
        }
        try {
            List<String> grayTenantBizModes = MccConfigUtil.getNewOrderServiceNotSupportBizModes();
            if (CollectionUtils.isNotEmpty(grayTenantBizModes)) {
                TenantBusinessModeEnum currentTenantBizMode = tenantWrapper.getTenantBizMode(identityInfo.getUser().getTenantId());
                if (grayTenantBizModes.contains(currentTenantBizMode.getKey())) {
                    return false;
                }
            }
        }catch (Exception e){
            return false;
        }
        return MccConfigUtil.hitNewOrderApiGrayTenant(tenantId);
    }


    @CatTransaction
    public CommonResponse<OrderListResponse> queryWaitToConfirmOrder(Long tenantId, Integer page, Integer size, Integer entityType) {
        try {
            OrderListResponse orderListResponse = wait2ConfirmQueryOrderService.queryOrderList(OrderListRequestContext.buildWaitToAuditRefund(ApiMethodParamThreadLocal.getIdentityInfo(), page, size, entityType));
            return CommonResponse.success(orderListResponse);
        } catch (Exception e) {
            log.error("OrderService.queryWaitToConfirmOrder  调用ocmsQueryThriftService.listOrder error", e);
            MetricHelper.build().name("order.wait2confirm.err").tag("tenantId", String.valueOf(tenantId)).count();
            throw new CommonRuntimeException(e);
        }
    }


    @CatTransaction
    public CommonResponse<OrderListResponse> queryWaitToPickOrder(Integer page, Integer size, Integer entityType, Integer sortType) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        Long tenantId = identityInfo.getUser().getTenantId();
        try {
            OrderListRequestContext orderListRequestContext = OrderListRequestContext.buildWaitToAuditRefund(identityInfo, page, size, entityType);
            orderListRequestContext.setSortType(sortType);
            OrderListResponse orderListResponse = wait2PickQueryOrderService.queryOrderList(orderListRequestContext);
            return CommonResponse.success(orderListResponse);
        } catch (Exception e) {
            MetricHelper.build().name("order.wait2pick.err").tag("tenantId", String.valueOf(tenantId)).tag("storeId", String.valueOf(storeIdList)).count();
            log.error("OrderService.queryWaitToPickOrder 调用ocmsQueryThriftService.queryOrderByViewIdCondition error", e);
            throw new CommonRuntimeException(e);
        }
    }

    private long firstShopId(List<Long> storeIdList) {
        if (CollectionUtils.isNotEmpty(storeIdList)){
            return storeIdList.get(0);
        }
        return 0;
    }


    @CatTransaction
    public CommonResponse<RefundApplyListResponse> queryWaitToAuditRefundGoodsOrderBySubType(Long tenantId, List<Long> storeIdList, Integer page, Integer size, Integer entityType, Integer subType) {
        if (Objects.isNull(WaitToAuditRefundGoodsOrderSubTypeEnum.valueOf(subType))) {
            log.error("售后订单子类型非法：subType={}", subType);
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "售后订单子类型非法");
        }
        try {
            RefundApplyListResponse refundApplyListResponse = afterSaleQueryOrderService.queryAfterSaleOrderList(OrderListRequestContext.buildWaitToAuditRefund(ApiMethodParamThreadLocal.getIdentityInfo(), page, size, entityType, subType));
            return CommonResponse.success(refundApplyListResponse);
        } catch (Exception e) {
            MetricHelper.build().name("order.waitToAuditRefundGoodsOrderBySubType.err").tag("tenantId", String.valueOf(tenantId)).tag("storeId", String.valueOf(firstShopId(storeIdList))).count();
            log.error("OrderService.queryWaitToAuditRefundGoodsOrderBySubType  调用ocmsQueryThriftService.listWaitAuditOrder error", e);
            throw new CommonRuntimeException(e);
        }
    }
    
    @CatTransaction
    public CommonResponse<OrderListResponse> queryWaitToSelfFetchOrder(Long tenantId, Integer page, Integer size, Integer entityType) {
        try {
            OrderListResponse orderListResponse = wait2SelfFetchQueryOrderService.queryOrderList(OrderListRequestContext.buildWaitToAuditRefund(ApiMethodParamThreadLocal.getIdentityInfo(), page, size, entityType));
            return CommonResponse.success(orderListResponse);
        } catch (Exception e) {
            log.error("OrderService.queryWaitToSelfFetchOrder  调用ocmsQueryThriftService.listOrder error", e);
            MetricHelper.build().name("order.wait2confirm.err").tag("tenantId", String.valueOf(tenantId)).count();
            throw new CommonRuntimeException(e);
        }
    }
    
    @CatTransaction
    public CommonResponse<OrderListResponse> orderList(OrderListRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        try {
            OrderListResponse orderListResponse = orderListSearchOrderService.queryOrderList(OrderListRequestContext.buildWaitToAuditRefund(identityInfo, request));
            return CommonResponse.success(orderListResponse);
        } catch (Exception e) {
            log.warn("OrderService.orderList call ocmsOrderSearchService.orderList error.}", e);
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "系统异常，请稍后重试");
        }
    }

    @CatTransaction
    public CommonResponse<OrderListResponse> orderListForAppLocalCache(OrderListForAppLocalCacheRequest request) {
        try {
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            OrderListResponse orderListResponse = appCacheListQueryOrderService.queryOrderList(OrderListRequestContext.buildWaitToAuditRefund(identityInfo, request));
            return CommonResponse.success(orderListResponse);
        } catch (Exception e) {
            log.warn("OrderService.orderList call ocmsOrderSearchService.orderList error.}", e);
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "系统异常，请稍后重试");
        }
    }

    @CatTransaction
    public CommonResponse<OrderListResponse> changedOrderListForAppLocalCache(ChangedOrderListForAppLocalCacheRequest request) {
        try {
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            OrderListResponse orderListResponse = appCacheChangeQueryOrderService.queryOrderList(OrderListRequestContext.buildWaitToAuditRefund(identityInfo, request));
            return CommonResponse.success(orderListResponse);
        } catch (Exception e) {
            log.warn("OrderService.orderList call ocmsOrderSearchService.orderList error.}", e);
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "系统异常，请稍后重试");
        }
    }

    @CatTransaction
    public CommonResponse<OrderListResponse> queryWaitToDeliveryOrderBySubType(QueryWaitToDeliveryOrderBySubTypeRequest request) {
        if (Objects.isNull(WaitToDeliveryOrderSubTypeEnum.valueOf(request.getSubType()))) {
            log.error("待配送订单子类型非法：subType={}", request.getSubType());
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "待配送订单子类型非法");
        }
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();

        try {
            OrderListResponse orderListResponse = wait2DeliveryQueryOrderService.queryOrderList(OrderListRequestContext.buildWaitToAuditRefund(identityInfo, request));
            return CommonResponse.success(orderListResponse);
        } catch (Exception e) {
            MetricHelper.build().name("order.wait2deliveryBySubType.err").tag("tenantId", String.valueOf(identityInfo.getUser().getTenantId())).tag("storeId", String.valueOf(firstShopId(identityInfo.getStoreIdList()))).count();
            log.error("OrderService.queryWaitToDeliveryOrderBySubType  调用ocmsQueryThriftService.listOrder error", e);
            throw new CommonRuntimeException(e);
        }
    }

    @CatTransaction
    public CommonResponse<WaitToDeliverySubTypeCountResponse> queryWaitDeliverySubTypeCount(QueryWaitDeliverySubTypeCountRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        try {
            Map<OrderTabSubTypeEnum, Integer> orderTabSubTypeEnumIntegerMap = wait2DeliveryQueryOrderService.countSubTap(identityInfo.getUser().getTenantId(), identityInfo.getStoreIdList(), request.getEntityType());
            WaitToDeliverySubTypeCountResponse waitToDeliverySubTypeCountResponse = new WaitToDeliverySubTypeCountResponse();
            waitToDeliverySubTypeCountResponse.setAllSubTypeCount(orderTabSubTypeEnumIntegerMap.getOrDefault(OrderTabSubTypeEnum.ALL, 0));
            waitToDeliverySubTypeCountResponse.setDeliveringCount(orderTabSubTypeEnumIntegerMap.getOrDefault(OrderTabSubTypeEnum.DELIVERING, 0));
            waitToDeliverySubTypeCountResponse.setWaitToSendDeliveryCount(orderTabSubTypeEnumIntegerMap.getOrDefault(OrderTabSubTypeEnum.WAIT_TO_SEND_DELIVERY, 0));
            waitToDeliverySubTypeCountResponse.setWaitToArriveShopCount(orderTabSubTypeEnumIntegerMap.getOrDefault(OrderTabSubTypeEnum.WAIT_TO_ARRIVE_SHOP, 0));
            waitToDeliverySubTypeCountResponse.setWaitToRiderAcceptCount(orderTabSubTypeEnumIntegerMap.getOrDefault(OrderTabSubTypeEnum.WAIT_TO_RIDER_ACCEPT, 0));
            waitToDeliverySubTypeCountResponse.setWaitToTakeGoodsCount(orderTabSubTypeEnumIntegerMap.getOrDefault(OrderTabSubTypeEnum.WAIT_TO_TAKE_GOODS, 0));

            return CommonResponse.success(waitToDeliverySubTypeCountResponse);
        } catch (Exception e) {
            MetricHelper.build().name("order.wait2deliverySubTypeCount.err").tag("tenantId", String.valueOf(identityInfo.getUser().getTenantId())).tag("storeId", String.valueOf(firstShopId(identityInfo.getStoreIdList()))).count();
            log.error("OrderService.wait2deliverySubTypeCount  调用ocmsQueryThriftService.queryWaitToDeliveryOrderSubTypeCount error", e);
            throw new CommonRuntimeException(e);
        }
    }


    @CatTransaction
    public CommonResponse<RefundGoodsSubTypeCountResponse> queryWaitAuditRefundGoodsBySubtypeCount(Long tenantId, List<Long> storeIdList, Integer entityType) {
        try {
            Map<WaitToAuditRefundGoodsOrderSubTypeEnum, Integer> map = afterSaleQueryOrderService.countByType(tenantId, storeIdList, entityType);
            RefundGoodsSubTypeCountResponse response = new RefundGoodsSubTypeCountResponse();
            response.setAllSubTypeCount(map.getOrDefault(WaitToAuditRefundGoodsOrderSubTypeEnum.ALL, 0));
            response.setAppealCount(map.getOrDefault(WaitToAuditRefundGoodsOrderSubTypeEnum.APPEAL, 0));
            response.setOnlyRefundCount(map.getOrDefault(WaitToAuditRefundGoodsOrderSubTypeEnum.ONLY_REFUND, 0));
            response.setReturnAndRefundCount(map.getOrDefault(WaitToAuditRefundGoodsOrderSubTypeEnum.RETURN_AND_REFUND, 0));
            response.setRejectByCustomerCount(map.getOrDefault(WaitToAuditRefundGoodsOrderSubTypeEnum.REJECT_BY_CUSTOMER, 0));
            return CommonResponse.success(response);
        } catch (Exception e) {
            MetricHelper.build().name("order.waitAuditRefundGoodsBySubtypeCount.err").tag("tenantId", String.valueOf(tenantId)).tag("storeId", String.valueOf(firstShopId(storeIdList))).count();
            log.error("OrderService.queryWaitAuditRefundGoodsBySubtypeCount  调用ocmsQueryThriftService.queryWaitToRefundGoodsBySubtypeCount error", e);
            throw new CommonRuntimeException(e);
        }
    }


    @CatTransaction
    public CommonResponse<OrderListResponse> queryDeliveryErrorBySubType(QueryDeliveryErrorOrderBySubTypeRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        long tenantId = identityInfo.getUser().getTenantId();
        try {
            OrderListResponse orderListResponse = deliveryErrorQueryOrderService.queryOrderList(OrderListRequestContext.buildWaitToAuditRefund(ApiMethodParamThreadLocal.getIdentityInfo(), request));
            return CommonResponse.success(orderListResponse);
        } catch (Exception e) {
            log.error("OrderService.queryDeliveryErrorBySubType 调用 tmsServiceWrapper.queryDeliveryErrorOrdersBySubType error", e);
            MetricHelper.build().name("order.deliveryErrorBySubType.err").tag("tenantId", String.valueOf(tenantId)).tag("storeId",
                    String.valueOf(firstShopId(storeIdList))).count();
            throw new CommonRuntimeException(e);
        }
    }


    @CatTransaction
    public CommonResponse<DeliveryErrorSubTypeCountResponse> queryDeliveryErrorSubTypeCount() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        long tenantId = identityInfo.getUser().getTenantId();
        if (CollectionUtils.isEmpty(storeIdList) || storeIdList.size() > 1) {
            log.error("订单展示只支持单门店模式：storeIdList={}", storeIdList);
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "只支持单门店模式");
        }
        try {
            Map<DeliveryExceptionSubTypeEnum, Integer> deliveryExceptionSubTypeEnumIntegerMap = deliveryErrorQueryOrderService.countDeliveryErrorBySubType(tenantId, storeIdList);
            DeliveryErrorSubTypeCountResponse subTypeCountResponse=new DeliveryErrorSubTypeCountResponse();
            subTypeCountResponse.setAllSubTypeCount(deliveryExceptionSubTypeEnumIntegerMap.getOrDefault(DeliveryExceptionSubTypeEnum.ALL, 0));
            subTypeCountResponse.setNoRiderAcceptCount(deliveryExceptionSubTypeEnumIntegerMap.getOrDefault(DeliveryExceptionSubTypeEnum.NO_RIDER_ACCEPT, 0));
            subTypeCountResponse.setNoArrivalStoreCount(deliveryExceptionSubTypeEnumIntegerMap.getOrDefault(DeliveryExceptionSubTypeEnum.NO_ARRIVAL_STORE, 0));
            subTypeCountResponse.setNoRiderTakeGoodsCount(deliveryExceptionSubTypeEnumIntegerMap.getOrDefault(DeliveryExceptionSubTypeEnum.NO_RIDER_TAKE_GOODS, 0));
            subTypeCountResponse.setDeliveryTimeoutCount(deliveryExceptionSubTypeEnumIntegerMap.getOrDefault(DeliveryExceptionSubTypeEnum.DELIVERY_TIMEOUT, 0));
            subTypeCountResponse.setReportExceptionCount(deliveryExceptionSubTypeEnumIntegerMap.getOrDefault(DeliveryExceptionSubTypeEnum.REPORT_EXCEPTION, 0));
            subTypeCountResponse.setSystemExceptionCount(deliveryExceptionSubTypeEnumIntegerMap.getOrDefault(DeliveryExceptionSubTypeEnum.SYSTEM_EXCEPTION, 0));
            subTypeCountResponse.setTakeGoodsExceptionCount(deliveryExceptionSubTypeEnumIntegerMap.getOrDefault(DeliveryExceptionSubTypeEnum.TAKE_EXCEPTION, 0));
            return CommonResponse.success(subTypeCountResponse);
        }catch (Exception e){
            log.error("OrderService.queryDeliveryErrorSubTypeCount 调用 tmsServiceWrapper.queryDeliveryExceptionOrderSubTypeOrderList error", e);
            MetricHelper.build().name("order.deliveryErrorSubTypeCount.err").tag("tenantId", String.valueOf(tenantId)).tag("storeId",
                    String.valueOf(firstShopId(storeIdList))).count();
            throw new CommonRuntimeException(e);
        }
    }



    public com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse<Void> importRefundExchangeInfo(ImportRefundExchangeInfoRequest request) {
        return orderBizRemoteService.importRefundExchangeInfo(request);
    }

    public CommonResponse<ExchangeRefundImportCheckResponse> checkRefundExchangeInfo(ExchangeRefundImportCheckReq request) {
        return orderBizRemoteService.checkRefundExchangeInfo(request);
    }
}
