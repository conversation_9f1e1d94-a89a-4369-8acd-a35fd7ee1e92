package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.order.management.client.dto.request.online.OrderItemFuseListReq;
import com.meituan.shangou.saas.order.management.client.export.dto.request.FuseOrderDetailListExportTaskCreateRequest;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.OrderItemFuseQueryExportRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.OrderItemFuseQueryRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2023/1/2 14:30
 * @Description:
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@EqualsAndHashCode
public class OrderItemFuseQueryBO {

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 融合订单状态
     */
    public List<Integer> fuseOrderStatus;

    /**
     * 渠道列表
     */
    private List<Integer> channelIds;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 门店ID集合
     */
    private List<Long> poiIdList;

    /**
     * 仓库id
     */
    private List<Long> warehouseIdList;

    /**
     * 商品skuid
     */
    private String skuId;

    /**
     * 商品条码
     */
    private String upc;

    /**
     * 线上分类
     */
    private String category;


    /**
     * 订单创建开始时间
     */
    private String createStartTime;

    /**
     * 订单创建结束时间
     */
    private String createEndTime;

    /**
     * 商品erp
     */
    private String erpCode;

    /**
     * 店内分类idlist
     */
    private List<String> inStoreCategoryIds;

    /**
     * 租户id
     */
    private Long tenantId;


    /**
     * 商品erp list
     */
    private List<String> erpCodeList;

    /**
     * 商品条码 list
     */
    private List<String> upcList;

    /**
     * SKU list
     */
    private List<String> skuIdList;

    /**
     * 订单标识列表
     */
    public List<String> orderMarkStrList;

    /**
     * 页码
     */
    private int page;

    /**
     * 每页显示记录数
     */
    private int pageSize;

    public OrderItemFuseQueryBO(OrderItemFuseQueryRequest request) {
        if (StringUtils.isNotBlank(request.getCreateStartTime()) && StringUtils.isNotBlank(request.getCreateEndTime())) {
            createStartTime = request.getCreateStartTime();
            createEndTime = request.getCreateEndTime();
        }
        channelIds = CollectionUtils.isNotEmpty(request.getChannelIds()) ?
                request.getChannelIds().stream().map(Integer::parseInt).collect(Collectors.toList())
                : MccDynamicConfigUtil.ignoreOrderBizTypeConditions();
        skuName = request.getSkuName();
        page = request.getPage();
        pageSize = request.getPageSize();

        if (CollectionUtils.isNotEmpty(request.getFuseOrderStatus())) {
            fuseOrderStatus = request.getFuseOrderStatus().stream().filter(item -> !item.equals("0")).map(Integer::parseInt).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(request.getPoiIdList())) {
            poiIdList = request.getPoiIdList();
        }
        if (CollectionUtils.isNotEmpty(request.getWarehouseIdList())) {
            warehouseIdList = request.getWarehouseIdList();
        }
        skuId = request.getSkuId();
        erpCode = request.getErpCode();
        upc = request.getUpc();
        orderId = request.getOrderId();
        inStoreCategoryIds = request.getInStoreCategoryIds();
        if(CollectionUtils.isNotEmpty(request.getErpCodeList())){
            erpCodeList = request.getErpCodeList();
        }
        if(CollectionUtils.isNotEmpty(request.getUpcList())){
            upcList = request.getUpcList();
        }
        if(CollectionUtils.isNotEmpty(request.getSkuIdList())){
            skuIdList = request.getSkuIdList();
        }
        if(CollectionUtils.isNotEmpty(request.getOrderMarkStrList())){
            orderMarkStrList = request.getOrderMarkStrList();
        }
    }

    public OrderItemFuseQueryBO(OrderItemFuseQueryExportRequest request) {
        if (StringUtils.isNotBlank(request.getCreateStartTime()) && StringUtils.isNotBlank(request.getCreateEndTime())) {
            createStartTime = request.getCreateStartTime();
            createEndTime = request.getCreateEndTime();
        }
        channelIds = CollectionUtils.isNotEmpty(request.getChannelIds()) ? request.getChannelIds().stream().map(Integer::parseInt).collect(Collectors.toList())
                : MccDynamicConfigUtil.ignoreOrderBizTypeConditions();
        skuName = request.getSkuName();

        if(CollectionUtils.isNotEmpty(request.getFuseOrderStatus())){
            fuseOrderStatus = request.getFuseOrderStatus().stream().filter(item -> !item.equals("0")).map(Integer::parseInt).collect(Collectors.toList());
        }

        if(CollectionUtils.isNotEmpty(request.getPoiIdList())){
            poiIdList = request.getPoiIdList();
        }
        skuId = request.getSkuId();
        erpCode = request.getErpCode();
        upc = request.getUpc();
        orderId = request.getOrderId();
        inStoreCategoryIds = request.getInStoreCategoryIds();
        if(CollectionUtils.isNotEmpty(request.getErpCodeList())){
            erpCodeList = request.getErpCodeList();
        }
        if(CollectionUtils.isNotEmpty(request.getUpcList())){
            upcList = request.getUpcList();
        }
        if(CollectionUtils.isNotEmpty(request.getSkuIdList())){
            skuIdList = request.getSkuIdList();
        }
        if(CollectionUtils.isNotEmpty(request.getOrderMarkStrList())){
            orderMarkStrList = request.getOrderMarkStrList();
        }
    }


    public OrderItemFuseListReq toOrderItemAllFuseListReq() {
        OrderItemFuseListReq req = new OrderItemFuseListReq();
        req.setTenantId(tenantId);
        if(StringUtils.isNotBlank(orderId)){
            req.setChannelOrderId(orderId);
        }
        req.setBeginCreateTime(ConverterUtils.nonNullConvert(createStartTime, Long::valueOf, 0L));
        req.setEndCreateTime(ConverterUtils.nonNullConvert(createEndTime, Long::valueOf, 0L));
        if(CollectionUtils.isNotEmpty(channelIds)){
            req.setChannelIdList(channelIds);
        }
        if(CollectionUtils.isNotEmpty(poiIdList)){
            req.setShopIdList(poiIdList);
        }
        if(CollectionUtils.isNotEmpty(warehouseIdList)){
            req.setWarehouseIdList(warehouseIdList);
        }
        req.setPage(page);
        req.setPageSize(pageSize);
        if(CollectionUtils.isNotEmpty(fuseOrderStatus)){
            req.setFuseOrderStatus(fuseOrderStatus);
        }
        if(StringUtils.isNotBlank(skuId)){
            req.setSkuId(skuId);
        }
        if(StringUtils.isNotBlank(erpCode)){
            req.setErpCode(erpCode);
        }
        if(StringUtils.isNotBlank(upc)){
            req.setBarCode(upc);
        }
        if (CollectionUtils.isNotEmpty(inStoreCategoryIds)) {
            req.setInStoreCategoryIdList(inStoreCategoryIds);
        }
        if(CollectionUtils.isNotEmpty(skuIdList)){
            req.setSkuIdList(skuIdList);
        }
        if(CollectionUtils.isNotEmpty(erpCodeList)){
            req.setErpCodeList(erpCodeList);
        }
        if(CollectionUtils.isNotEmpty(upcList)){
            req.setUpcList(upcList);
        }
        if(CollectionUtils.isNotEmpty(orderMarkStrList)){
            req.setOrderMarkStrList(orderMarkStrList);
        }
        return req;
    }

    public FuseOrderDetailListExportTaskCreateRequest toFuseOrderDetailListExportTaskCreateRequest() {
        FuseOrderDetailListExportTaskCreateRequest request = new FuseOrderDetailListExportTaskCreateRequest();
        OrderItemFuseListReq req = this.toOrderItemAllFuseListReq();
        request.setOrderItemFuseListReq(req);
        request.setOperatorId(ContextHolder.currentUid());
        return request;
    }
}
