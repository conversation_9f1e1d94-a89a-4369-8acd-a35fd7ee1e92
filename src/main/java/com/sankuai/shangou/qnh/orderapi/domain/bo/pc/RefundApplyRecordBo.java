package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ChannelLabelVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderFuseVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderTagVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ReturnGoodsDuringDeliveryInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "退单列表"
)
@Data
@ApiModel("退单列表信息")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundApplyRecordBo {

    @FieldDoc(
            description = "订单信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单信息", required = true)
    private OrderFuseVO orderVO;

    @FieldDoc(
            description = "退款申请唯一ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款申请唯一ID", required = true)
    private String serviceId;

    @FieldDoc(
            description = "渠道售后id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "渠道售后id", required = false)
    private String afterSaleId;

    @FieldDoc(
            description = "是否需要审核", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否需要审核", required = true)
    private Integer isAudit;


    @FieldDoc(
            description = "售后状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "售后状态", required = true)
    private Integer status;

    @FieldDoc(
            description = "售后状态描述，用于PC显示标签", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "售后状态描述，用于PC显示标签", required = true)
    private String statusDesc;


    @FieldDoc(
            description = "申请原因", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "申请原因", required = true)
    private String applyReason;


    @FieldDoc(
            description = "退款类型, 1-整单退款，2-部分退款", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "退款类型, 1-整单退款，2-部分退款", required = true)
    private Integer afsPattern;

    @FieldDoc(
            description = "退款给顾客金额 单位分", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "退款给顾客金额 单位分", required = false)
    private String refundAmt;

    @FieldDoc(
            description = "创建时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "创建时间", required = true)
    private Long createTime;


    @FieldDoc(
            description = "一审时间，退货退款使用", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "一审时间，退货退款使用", required = true)
    private Long firstAuditTime;


    @FieldDoc(
            description = "更新时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "更新时间", required = true)
    private Long updateTime;

    @FieldDoc(
            description = "售后申请类型  10-仅退款，20-退货退款", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "售后申请类型  10-仅退款，20-退货退款", required = true)
    private Integer afsApplyType;

    @FieldDoc(
            description = "售后申请类型描述", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "售后申请类型描述", required = true)
    private String afsApplyTypeDesc;

    @FieldDoc(
            description = "申请人类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "申请人类型", required = true)
    private Integer whoApplyType;

    @FieldDoc(
            description = "售后商品总数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "售后商品总数量", required = false)
    private Integer refundProductCount;

    @FieldDoc(
            description = "售后商品种类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "售后商品种类", required = true)
    private Integer refundProductCategory;

    @FieldDoc(
            description = "售后商品总重量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "售后商品总数量", required = false)
    private String refundTotalWeight;

    @FieldDoc(
            description = "售后图片", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "售后图片", required = false)
    private List<String> afsPicture;


    @FieldDoc(
            description = "当前时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "当前时间", required = true)
    private Long currentTime;


    @FieldDoc(
            description = "超时时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "超时时间", required = false)
    private Long warnDuration;

    @FieldDoc(
            description = "售后文案", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "售后文案", required = false)
    private String opContent;

    @FieldDoc(
            description = "核销状态", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "核销状态", required = false)
    private Integer posStatus;

    @FieldDoc(
            description = "核销错误描述", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "核销错误描述", required = false)
    private String posErrorMsg;

    @FieldDoc(
            description = "核销检查标签", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "核销检查标签", required = false)
    private Integer posCheckTag;

    @FieldDoc(
            description = "退单日志", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "退单日志", required = false)
    private List<AfterSaleStatusLogBO> afterSaleStatusLogList;

    @FieldDoc(
            description = "退单tag", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "退单tag")
    private List<OrderTagVO> refundOrderTagList;


    @FieldDoc(
            description = "是否存在调整单", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否存在调整单")
    private Integer existAdjustOrder;


    @FieldDoc(
            description = "是否可以直接退款", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否可以直接退款")
    private Integer directRefundFlag;


    @FieldDoc(
            description = "预计退货运费金额", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "预计退货运费金额")
    private Integer preReturnFreight;


    @FieldDoc(
            description = "分配时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分配时间")
    private Long dealTime;

    @FieldDoc(
            description = "处理的转单门店ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "处理的转单门店ID")
    private Long assignShopId;

    @FieldDoc(
            description = "是否支持返货", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否支持返货")
    private Boolean isCanReturnGoods;

    @FieldDoc(
            description = "渠道是否返货", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "渠道是否返货")
    private Boolean isReturnGoods;

    @FieldDoc(
            description = "返货信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "返货信息")
    private ReturnGoodsDuringDeliveryInfoVo returnGoodsDuringDeliveryInfo;

    @FieldDoc(
            description = "商品标签信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品标签信息")
    private List<ChannelLabelVO> channelLabelList;

    @FieldDoc(
            description = "审核截止时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "审核截止时间")
    private Long processDeadline;

    @FieldDoc(
            description = "退货状态", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "退货状态")
    private Integer returnGoodsStatus;

    @FieldDoc(
            description = "退货地址", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "退货地址")
    private String pickUpRefundGoodsAddress;

    @FieldDoc(
            description = "退单可操作列表：详见枚举 RefundCanOperateItem", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "退单可操作列表：详见枚举 RefundCanOperateItem", required = false)
    private List<Integer> refundCouldOperateItems;

    @FieldDoc(
            description = "售后换货信息录入是否未录入，ture：未录入，false：已录入"
    )
    @ApiModelProperty(value = "售后换货信息录入是否未录入，ture：未录入，false：已录入", required = false)
    private Boolean isNotImport;

    @FieldDoc(
            description = "渠道退单额外类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "渠道退单额外类型")
    private Integer channelExtRefundType;


    @FieldDoc(
            description = "退货方式，1:用户自行送回,2:用户呼叫平台骑手送回, 3:商家自行取回"
    )
    @ApiModelProperty(value = "退货方式，1:用户自行送回,2:用户呼叫平台骑手送回, 3:商家自行取回", required = false)
    private Integer refundGoodWay;

    @FieldDoc(
            description = "退货运费承担方", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "退货运费承担方")
    private Integer refundGoodFreightType;
}
