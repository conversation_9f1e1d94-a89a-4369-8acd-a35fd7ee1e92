package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(
        description = "退款模块"
)
@Data
public class AfterSaleVO {

    private Integer afsPattern;

    private Integer applyOperatorType;

    private Long applyTime;

    private String applyReason;

    private Integer afterSaleStatus;

    private Integer auditOperatorType;

    private Long auditTime;

    private String auditReason;

    private String applyOperatorName;

    private String auditOperatorName;


}
