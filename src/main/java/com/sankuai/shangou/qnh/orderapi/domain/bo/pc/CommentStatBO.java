package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;
// Copyright (C) 2019 Meituan
// All rights reserved

import com.google.common.collect.Maps;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentLabelStatDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * @author: <EMAIL>
 * @class: CommentStatBO
 * @date: 2019-07-04 17:06:22
 * @desc:
 */
@Data
@AllArgsConstructor
public class CommentStatBO {

    /**
     * 评价统计标签
     */
    public String commentLabel;

    /**
     * 评价统计标签对应数量
     */
    public int commentLabelCount;

    public CommentStatBO(ChannelCommentLabelStatDTO dto) {
        this.commentLabel = dto.getCommentLabel();
        this.commentLabelCount = dto.getCommentLabelCount();
    }


    public static Map<String, Integer> toStatisticsMap(List<CommentStatBO> boList) {
        Map<String, Integer>  statisticMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(boList)) {
            return statisticMap;
        }
        for (CommentStatBO commentStatBO : boList) {
            statisticMap.put(commentStatBO.getCommentLabel(), commentStatBO.getCommentLabelCount());
        }
        return statisticMap;
    }

    /**
     * 将List<CommentStatBO>转换为label与count的map
     * @param boList
     * @return Map<Label, Count>
     */
    public static Map<String, String> toStatisticsStringMap(List<CommentStatBO> boList) {
        Map<String, String> statisticMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(boList)) {
            return statisticMap;
        }
        for (CommentStatBO commentStatBO : boList) {
            statisticMap.put(commentStatBO.getCommentLabel(), String.valueOf(commentStatBO.getCommentLabelCount()));
        }
        return statisticMap;
    }

}
