package com.sankuai.shangou.qnh.orderapi.enums;

import com.meituan.reco.pickselect.common.domain.orderTrack.ReturnGoodsAuditType;

/**
 * 是否有返货类型码值
 */

public enum IsReturnGoodsTypeEnum {

    //未知
    UN_KNOWN(-1, "未知"),
    NON_RETURN_GOODS(0, "不需要返货"),
    RETURN_GOODS(1, "需要返货"),

    ;


    IsReturnGoodsTypeEnum(int value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    private final int value;

    private final String msg;

    public int getValue() {
        return value;
    }

    public static IsReturnGoodsTypeEnum codeOf(int value) {
        for (IsReturnGoodsTypeEnum typeEnum : values()) {
            if (typeEnum.getValue() == value) {
                return typeEnum;
            }
        }
        return UN_KNOWN;
    }
}
