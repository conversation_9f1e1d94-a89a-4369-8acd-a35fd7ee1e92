package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2022-12-06 14:57
 * @Description:
 */
@TypeDoc(
        description = "赠品信息"
)
@ApiModel("赠品信息")
@Data
public class GiftVO {
    @FieldDoc(
            description = "赠品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "赠品名称",required = true)
    private String giftName;

    @FieldDoc(
            description = "赠品数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赠品数量", required = false)
    private Integer GiftQuantity;
}
