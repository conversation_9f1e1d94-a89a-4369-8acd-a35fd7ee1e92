package com.sankuai.shangou.qnh.orderapi.enums.pc;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/28 19:39
 * @Description: 价格模块错误码
 */
@Getter
@AllArgsConstructor
public enum PriceErrorTypeEnum {

    CREATE_PRICE_ALL_FAIL(2001, "申请创建价格全部失败"),

    SAVE_PRICE_PARAM_ERROR(2002,"保存价格时参数错误"),

    SAVE_PRICE_RAISE_CONFIG_PARAM_VALIDATE_ERROR(2003, "保存渠道或分类提价配置时校验错误");


    private int code;

    private String desc;

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static TaskErrorTypeEnum getByCode(int code) {
        for (TaskErrorTypeEnum e : TaskErrorTypeEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }

        return null;
    }
}
