package com.sankuai.shangou.qnh.orderapi.utils.store;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.RefundRelatedOrderInfoVO;
import com.meituan.shangou.saas.order.management.client.enums.ChannelType;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiShippingModeEnum;
import com.meituan.shangou.sac.infrastructure.lion.SacLionConfig;
import com.sankuai.meituan.reco.store.management.adjustprice.thrift.OrderDetailResponse;
import com.sankuai.meituan.reco.store.management.adjustprice.thrift.RecentPurchasePrice;
import com.sankuai.meituan.reco.store.management.adjustprice.thrift.SearchInfoByKeywordResponse;
import com.sankuai.meituan.reco.store.management.adjustprice.thrift.SkuAdjustBasicInfo;
import com.sankuai.meituan.reco.store.management.adjustprice.thrift.SkuAdjustDetail;
import com.sankuai.meituan.reco.store.management.adjustprice.thrift.SkuAdjustInfo;
import com.sankuai.meituan.reco.store.management.adjustprice.thrift.TQueryAdjustGoodsListResponse;
import com.sankuai.meituan.reco.store.management.adjustprice.thrift.TSkuPriceInfo;
import com.sankuai.meituan.reco.store.management.stock.check.thrift.*;
import com.sankuai.meituan.reco.store.management.thrift.ObtainStoreInfoResult;
import com.sankuai.meituan.reco.store.management.thrift.StoreInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.AppIdEnum;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.AppIdMappingEnum;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.*;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.AdjustGoodVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.AdjustListDeatil;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuErrInfo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.RefundRelatedRevenueInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.RecentPurchasePriceVO;
import com.sankuai.shangou.qnh.orderapi.enums.store.OrderViewStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018/9/25
 */
@Slf4j
public class ConvertUtils {

    public static PlanStockCheckOrderBaseRes convertStockCheckBaseList(StockCheckOrderBaseListPageResult pageResult) {
        PlanStockCheckOrderBaseRes result = new PlanStockCheckOrderBaseRes();
        List<StockCheckOrderBaseInfo> baseInfos = pageResult.pageResult;
        if (CollectionUtils.isEmpty(baseInfos)) {
            return result;
        }
        List<PlanStockCheckOrderBaseRes.PlanStockCheckOrderBaseInfo> transform = Lists.transform(baseInfos,
                new Function<StockCheckOrderBaseInfo, PlanStockCheckOrderBaseRes.PlanStockCheckOrderBaseInfo>() {
            @Nullable
            @Override
            public PlanStockCheckOrderBaseRes.PlanStockCheckOrderBaseInfo apply(@Nullable StockCheckOrderBaseInfo input) {
                return convertStockCheckBase(input);
            }
        });
        result.setHasMore(pageResult.hasMore);
        result.setStockCheckOrderList(transform);
        return result;
    }

    public static PlanStockCheckOrderBaseRes.PlanStockCheckOrderBaseInfo convertStockCheckBase(StockCheckOrderBaseInfo input) {
        if (input == null) {
            return null;
        }
        PlanStockCheckOrderBaseRes.PlanStockCheckOrderBaseInfo res = new PlanStockCheckOrderBaseRes.PlanStockCheckOrderBaseInfo();
        res.setComment(input.comment);
        res.setPlanExecuteTime(TimeUtils.convertTimeStamp2Str(input.planExecuteTime));
        if (input.getExecuteTime() > 0) {
            res.setExecuteTime(TimeUtils.convertTimeStamp2Str(input.getExecuteTime()));
        }
        res.setPlanType(input.planType);
        res.setStatus(input.status);
        res.setStockCheckOrderPlanId(input.stockCheckOrderPlanId);
        return res;
    }


    public static PlanStockCheckOrderDetailRes convertStockCheckDetail(StockCheckOrderDetail orderDetail) {
        if (orderDetail == null) {
            return null;
        }
        PlanStockCheckOrderDetailRes res = new PlanStockCheckOrderDetailRes();
        res.setSubStockCheckOrderList(orderDetail.subStockCheckOrderList == null ? null :
                convertSubStockCheckBaseList(orderDetail.subStockCheckOrderList));
        res.setCategoryIds(orderDetail.getCategoryIds());
        res.setComment(orderDetail.getComment());
        res.setDoneSkuCount(orderDetail.getDoneSkuCount());
        res.setPlanExecuteTime(TimeUtils.convertTimeStamp2Str(orderDetail.getPlanExecuteTime()));
        res.setPlanType(orderDetail.getPlanType());
        res.setSkuCount(orderDetail.getSkuCount());
        res.setStatus(orderDetail.getStatus());
        res.setStockCheckOrderPlanId(orderDetail.getStockCheckOrderPlanId());
        return res;
    }

    private static List<SubStockCheckOrderBase> convertSubStockCheckBaseList(List<SubStockCheckOrderBaseInfo> subStockCheckOrderList) {

        return Lists.transform(subStockCheckOrderList, new Function<SubStockCheckOrderBaseInfo, SubStockCheckOrderBase>() {
            @Nullable
            @Override
            public SubStockCheckOrderBase apply(SubStockCheckOrderBaseInfo input) {
                SubStockCheckOrderBase base = new SubStockCheckOrderBase();
                base.setComment(input.comment);
                base.setSkuCount(input.skuCount);
                base.setSubmitor(input.submitor);
                base.setSubmitTime(TimeUtils.convertTimeStamp2Str(input.submitTime));
                base.setSubStockCheckOrderId(input.getSubStockCheckOrderId());
                return base;
            }
        });
    }

    public static SubStockCheckOrderDetailRes convertSubStockCheckDetail(SubStockCheckOrderDetail subOrderDetail) {

        if (subOrderDetail == null) {
            return null;
        }
        SubStockCheckOrderDetailRes res = new SubStockCheckOrderDetailRes();
        res.setComment(subOrderDetail.getComment());
        res.setSkuCount(subOrderDetail.getSkuCount());
        res.setSubmitor(subOrderDetail.getSubmitor());
        res.setSubmitTime(TimeUtils.convertTimeStamp2Str(subOrderDetail.getSubmitTime()));
        res.setSubStockCheckOrderId(subOrderDetail.subStockCheckOrderId);
        res.setSkuCheckList(subOrderDetail.skuCheckList == null ? null : convertSkuItemList(subOrderDetail.skuCheckList));
        return res;
    }

    public static List<StockCheckSkuItem> convertSkuItemList(List<SkuCheckItem> skuCheckList) {

        if (skuCheckList == null) {
            return null;
        }
        return Lists.transform(skuCheckList, new Function<SkuCheckItem, StockCheckSkuItem>() {
            @Nullable
            @Override
            public StockCheckSkuItem apply(@Nullable SkuCheckItem input) {
                return convertSingleSkuItem(input);
            }
        });
    }

    public static StockCheckSkuItem convertSingleSkuItem(SkuCheckItem input) {
        if (input == null) {
            return null;
        }
        StockCheckSkuItem item = new StockCheckSkuItem();
        item.setComment(input.getComment());
        item.setSkuName(input.getSkuName());
        item.setStock(input.getStock());
        item.setUnit(input.getUnit());
        item.setUnitType(input.getUnitType());
        item.setSkuId(input.getSkuId());
        item.setUpcList(input.getUpcList());
        item.setIsWeight(input.getIsWeight());
        item.setSpec(input.getSpec());
        item.setStockNum(input.getStockNum());
        item.setLocationCode(input.getStockLocationCode());
        item.setIsAvailable(input.getIsAvailable());
        item.setSkuTotalWeight(input.isSetTotalWeight() ? String.valueOf(input.getTotalWeight()) : null);
        item.setWeightType(input.getWeightType());
        return item;
    }

    public static List<SkuCheckItem> convertSkuItemList2Thrift(List<StockCheckSkuItem> skuList) {
        if (skuList == null) {
            return null;
        }
        return Lists.transform(skuList, new Function<StockCheckSkuItem, SkuCheckItem>() {
            @Nullable
            @Override
            public SkuCheckItem apply(@Nullable StockCheckSkuItem input) {
                return convertSingleSkuItem2Thrift(input);
            }
        });
    }


    public static List<SkuAdjustDetail> domainToThriftAdjustOrderList(List<com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuAdjustDetail> domainList) {
        List<SkuAdjustDetail> thriftList = org.assertj.core.util.Lists.newArrayList();
        for (com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuAdjustDetail domainDetail : domainList) {
            SkuAdjustDetail thriftDetail = new SkuAdjustDetail();
            BeanUtils.copyProperties(domainDetail, thriftDetail);
            thriftList.add(thriftDetail);
        }
        return thriftList;
    }

    public static GetSkuPriceInfoResponse thriftToSkuPriceInfoResponse(TSkuPriceInfo tSkuPriceInfo) {
        GetSkuPriceInfoResponse getSkuPriceInfoResponse = new GetSkuPriceInfoResponse();
        BeanUtils.copyProperties(tSkuPriceInfo, getSkuPriceInfoResponse);
        if (tSkuPriceInfo.getLastAdjustTime() != 0) {
            getSkuPriceInfoResponse.setLastAdjustTime(TimeUtils.convertTimeStamp2Str(tSkuPriceInfo.getLastAdjustTime()));
        } else {
            getSkuPriceInfoResponse.setLastAdjustTime("");
        }

        getSkuPriceInfoResponse.setRecentPurchasePriceList(tSkuPriceInfo.getRecentPurchasePrice().stream().map(tRecentPurchasePrice -> {
            RecentPurchasePriceVO vo = new RecentPurchasePriceVO();
            BeanUtils.copyProperties(tRecentPurchasePrice, vo);
            vo.setTime(TimeUtils.convertTimeStamp2Str(tRecentPurchasePrice.getTime()));
            return vo;
        }).collect(Collectors.toList()));
        getSkuPriceInfoResponse.setStorePrice(String.valueOf(tSkuPriceInfo.getStorePrice()));
        getSkuPriceInfoResponse.setStoreMemberPrice(String.valueOf(tSkuPriceInfo.getStoreMemberPrice()));
        getSkuPriceInfoResponse.setMaxPrice(String.valueOf(tSkuPriceInfo.getMaxPrice()));
        getSkuPriceInfoResponse.setMinPrice(String.valueOf(tSkuPriceInfo.getMinPrice()));
        return getSkuPriceInfoResponse;
    }

    private static List<AdjustListDeatil> thriftToAdjustListDomain(List<SkuAdjustBasicInfo> thriftList) {
        List<AdjustListDeatil> resList = Lists.newArrayList();
        for (SkuAdjustBasicInfo thriftDo : thriftList) {
            AdjustListDeatil vo = new AdjustListDeatil();
            BeanUtils.copyProperties(thriftDo, vo);
            vo.setOperatorTime(TimeUtils.convertTimeStamp2Str(thriftDo.getOperatorTime()));
            resList.add(vo);
        }
        return resList;
    }

    public static SearchAdjustListResponse buildAdjustOrderListResponse(SearchInfoByKeywordResponse tResp) {
        SearchAdjustListResponse response = new SearchAdjustListResponse();
        response.setHasMore(tResp.isHasMore());
        response.setTotalCount(tResp.getTotalCount());
        response.setAdjustOrderList(thriftToAdjustListDomain(tResp.getTSkuAdjustBasicList()));
        return response;
    }

    public static GetAdjustDetailResponse thriftToOrderDetailVO(OrderDetailResponse thriftRes) {
        GetAdjustDetailResponse responseVo = new GetAdjustDetailResponse();
        responseVo.setOperatorName(thriftRes.getOperatorName());
        responseVo.setPriceOrderNo(thriftRes.getPriceOrderNo());
        responseVo.setOperatorTime(TimeUtils.convertTimeStamp2Str(thriftRes.getOperatorTime()));

        responseVo.setSkuList(thriftRes.getSkuList().stream().map(tSku -> {
            com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuAdjustDetail vo = new com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuAdjustDetail();
            BeanUtils.copyProperties(tSku, vo);
            vo.setCurStandardPrice(tSku.getCurStandardPrice());
            vo.setCurVipPrice(tSku.getCurVipPrice());
            vo.setPreStandardPrice(tSku.getPreStandardPrice());
            vo.setPreVipPrice(tSku.getPreVipPrice());
            return vo;
        }).collect(Collectors.toList()));
        return responseVo;
    }

    public static SearchAdjustGoodsListResponse convertSearchGoodsList(TQueryAdjustGoodsListResponse thriftRes) {
        SearchAdjustGoodsListResponse response = new SearchAdjustGoodsListResponse();
        response.setHasMore(thriftRes.isHasMore());
        response.setTotalCount(thriftRes.getTotalCount());
        response.setAdjustGoods(thriftRes.getAdjustGoods().stream().map(tAdjustGood -> {
            AdjustGoodVO adjustGoodVO = new AdjustGoodVO();
            BeanUtils.copyProperties(tAdjustGood,adjustGoodVO);
            adjustGoodVO.setStatus(tAdjustGood.getStatus().getValue());
            return adjustGoodVO;
        }).collect(Collectors.toList()));
        return response;
    }

    private static List<com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuAdjustDetail> thriftToSkuAdjustListVO(List<SkuAdjustInfo> thriftList) {
        List<com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuAdjustDetail> voList = Lists.newArrayList();
        for (SkuAdjustInfo skuAdjustInfo : thriftList) {
            com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuAdjustDetail vo = new com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuAdjustDetail();
            BeanUtils.copyProperties(skuAdjustInfo, vo);
            vo.setCurStandardPrice(skuAdjustInfo.getCurStandardPrice());
            vo.setCurVipPrice(skuAdjustInfo.getCurVipPrice());
            vo.setPreStandardPrice(skuAdjustInfo.getPreStandardPrice());
            vo.setPreVipPrice(skuAdjustInfo.getPreStandardPrice());
            voList.add(vo);
        }
        return voList;
    }

    private static List<RecentPurchasePriceVO> thriftToRecentPurchasePriceVO(List<RecentPurchasePrice> thriftList) {
        List<RecentPurchasePriceVO> voList = Lists.newArrayList();
        for (RecentPurchasePrice thriftDo : thriftList) {
            RecentPurchasePriceVO vo = new RecentPurchasePriceVO();
            BeanUtils.copyProperties(thriftDo, vo);
            vo.setTime(TimeUtils.convertTimeStamp2Str(thriftDo.getTime()));
            voList.add(vo);
        }
        return voList;
    }

    private static SkuCheckItem convertSingleSkuItem2Thrift(StockCheckSkuItem input) {
        SkuCheckItem item = new SkuCheckItem();
        item.setComment(input.getComment());
        item.setSkuName(input.getSkuName());
        item.setStock(input.getStock());
        item.setStockNum(input.getStockNum());
        item.setUnit(input.getUnit());
        item.setUnitType(input.getUnitType());
        item.setSkuId(input.getSkuId());
        if (StringUtils.isNotBlank(input.getSkuTotalWeight())) {
            item.setTotalWeight(input.getSkuTotalWeight());
            item.setWeightList(input.getSkuWeightDetail());
        }
        return item;
    }

    public static SubmitSubStockCheckOrderRes convertStockCheckSubmitRes(SubmitSubCheckData submitSubCheckData) {
        SubmitSubStockCheckOrderRes submitSubStockCheckOrderRes = new SubmitSubStockCheckOrderRes();
        submitSubStockCheckOrderRes.setSubCheckOrderId(submitSubCheckData.getSubCheckOrderId());
        List<FailSkuItem> failSkuItemList = submitSubCheckData.getFailSkuItemList();
        if (CollectionUtils.isNotEmpty(failSkuItemList)) {
            submitSubStockCheckOrderRes.setFailSkuItemList(Lists.transform(failSkuItemList, new Function<FailSkuItem, SubmitSubStockCheckOrderRes.FailSkuItem>() {
                @Nullable
                @Override
                public SubmitSubStockCheckOrderRes.FailSkuItem apply(FailSkuItem input) {

                    SubmitSubStockCheckOrderRes.FailSkuItem failSkuItem = new SubmitSubStockCheckOrderRes.FailSkuItem();
                    failSkuItem.setSkuId(input.getSkuId());
                    failSkuItem.setFailText(input.getFailText());
                    return failSkuItem;
                }
            }));
        }
        return submitSubStockCheckOrderRes;

    }

    public static GetStoreByIdsResponse convertObtainStoreInfoResult(ObtainStoreInfoResult result){
        GetStoreByIdsResponse response = new GetStoreByIdsResponse();
        if(result == null || CollectionUtils.isEmpty(result.getStoreInfoList())){
            return response;
        }

        response.setStoreInfoList(Lists.transform(result.getStoreInfoList(), new Function<StoreInfo, StoreInfoItem>() {
            @Nullable
            @Override
            public StoreInfoItem apply(@Nullable StoreInfo storeInfo) {
                StoreInfoItem item = new StoreInfoItem();
                item.setTenantId(storeInfo.getTenantId());
                item.setTenantName(storeInfo.getTenantName());
                item.setPoiId(storeInfo.getPoiId());
                item.setPoiName(storeInfo.getPoiName());
                item.setOutPoiId(storeInfo.getOutPoiId());
                item.setWmPoiId(storeInfo.getWmPoiId());
                item.setPayMerchantId(storeInfo.getPayMerchantId());
                item.setPoiAddress(storeInfo.getPoiAddress());
                item.setPoiStatus(storeInfo.getPoiStatus());
                item.setEntityType(storeInfo.getType());
                item.setEntityId(storeInfo.getEntityId());
                // 透出权限相关信息
                item.setAuthAppId(String.valueOf(mappingAuthAppId(storeInfo)));
                item.setAuthSubAppId(String.valueOf(mappingAuthSubAppId(storeInfo)));
                return item;
            }
        }));

        return response;
    }

    /**
     * 门店信息映射到权限应用 ID，目前直接取主权限应用即可
     */
    private static int mappingAuthAppId(StoreInfo storeInfo) {
        return SessionContext.getCurrentSession().getAuthAppId();
    }

    /**
     * 门店信息映射到权限子应用 ID，这里为比较业务的映射逻辑
     * 如果后期还有更多的映射，再考虑改为配置化方案
     */
    private static int mappingAuthSubAppId(StoreInfo storeInfo) {
        int authAppId = SessionContext.getCurrentSession().getAuthAppId();
        long tenantId = SessionContext.getCurrentSession().getTenantId();
        boolean supportHierarchicalApp = SacLionConfig.isSupportHierarchicalApp(String.valueOf(tenantId));
        // 履约助手无权限子应用概念，使用应用 ID 填充
        if (AppIdMappingEnum.LY_HELPER.getAuthAppId() == authAppId) {
            return authAppId;
        }
        // 主客户端应用
        if (AppIdMappingEnum.SG_PIE.getAuthAppId() == authAppId) {
            // 全部门店
            if (storeInfo.getPoiId() <= 0) {
                return supportHierarchicalApp ? AppIdEnum.APP_13.getAuthAppId() : AppIdEnum.APP_5.getAuthAppId();
            }
            // 共享仓门店
            if (Objects.equals(storeInfo.getType(), PoiEntityTypeEnum.STORE.code())
                    && Objects.equals(storeInfo.getShippingMode(), PoiShippingModeEnum.SHIP_BY_SHAREABLE_WAREHOUSE.code())) {
                return AppIdEnum.APP_12.getAuthAppId();
            }
            // 门店
            if (Objects.equals(storeInfo.getType(), PoiEntityTypeEnum.STORE.code())) {
                return supportHierarchicalApp ? AppIdEnum.APP_11.getAuthAppId() : AppIdEnum.APP_5.getAuthAppId();
            }
            // 中心仓
            if (Objects.equals(storeInfo.getType(), PoiEntityTypeEnum.REGIONAL_WAREHOUSE.code())) {
                return AppIdEnum.APP_8.getAuthAppId();
            }
            // 共享前置仓
            if (Objects.equals(storeInfo.getType(), PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code())) {
                return AppIdEnum.APP_9.getAuthAppId();
            }
            // 否则，为不支持的类型（考虑到上线流程，这里仅记录日志即可）
            log.error("未支持的 POI 类型：{}", storeInfo.getType(), new UnsupportedOperationException());
            return authAppId;
        }
        // 其他情况，也默认返回应用 ID
        log.warn("权限应用 [{}] 未处理子应用信息，功能权限可能异常", authAppId);
        return authAppId;
    }

    public static PullStockCheckSkuDetailRes convertPullStockCheckDetail(PullStockCheckSkuDetailResult pullDetailResult, boolean isConvenienceStore) {

        PullStockCheckSkuDetailRes res = new PullStockCheckSkuDetailRes();
        res.setConvenienceStore(isConvenienceStore);
        res.setPageNo(pullDetailResult.getPageNo());
        res.setPageSize(pullDetailResult.getPageSize());
        res.setTotalCount(pullDetailResult.getTotalCount());
        res.setSkuList(convertSkuItemList(pullDetailResult.getSkuList()));
        return res;
    }

    public static List<SkuErrInfo> convertRefundAndBreadErrMsg(List<com.sankuai.meituan.reco.store.management.thrift.SkuErrInfo> skuErrInfos){
        List<SkuErrInfo> skuErrInfoList = Lists.newArrayList();
        skuErrInfos.forEach(skuErrInfo -> {
            SkuErrInfo skuErr = new SkuErrInfo();
            skuErr.setSkuId(skuErrInfo.skuId);
            skuErr.setErrMsg(skuErrInfo.errMsg);
            skuErr.setStatus(skuErrInfo.status);
            skuErrInfoList.add(skuErr);
        });
        return skuErrInfoList;
    }

    public static StockCheckOrderBaseRes convertStockCheckOrderBaseRes(boolean hasMore, List<StockCheckOrderBriefDTO> dtoList) {
        StockCheckOrderBaseRes baseRes = new StockCheckOrderBaseRes();
        baseRes.setHasMore(hasMore);
        if (CollectionUtils.isNotEmpty(dtoList)) {
            baseRes.setStockCheckOrderList(Lists.transform(dtoList, new Function<StockCheckOrderBriefDTO, StockCheckOrderBaseRes.StockCheckOrderBaseInfo>() {
                @Nullable
                @Override
                public StockCheckOrderBaseRes.StockCheckOrderBaseInfo apply(@Nullable StockCheckOrderBriefDTO input) {
                    StockCheckOrderBaseRes.StockCheckOrderBaseInfo baseInfo = new StockCheckOrderBaseRes.StockCheckOrderBaseInfo();
                    baseInfo.setStockCheckId(input.getStockCheckId());
                    baseInfo.setStatus(input.getStatus());
                    baseInfo.setCreateTime(TimeUtils.convertTimeStamp2Str(input.getCreateTime()));
                    baseInfo.setSkuCount(input.getSkuCount());
                    return baseInfo;
                }
            }));
        }
        return baseRes;
    }

    public static StockCheckOrderDetailRes convertStockCheckOrderDetailRes(StockCheckOrderDetailDTO dto) {
        if (dto == null) {
            return null;
        }
        StockCheckOrderDetailRes detailRes = new StockCheckOrderDetailRes();
        detailRes.setStockCheckId(dto.getStockCheckId());
        detailRes.setCreateTime(TimeUtils.convertTimeStamp2Str(dto.getCheckCreateTime()));
        detailRes.setStockCheckType(dto.getStockCheckType());
        detailRes.setStatus(dto.getStatus());
        if (dto.isSetSubmitTime()) {
            detailRes.setSubmitTime(TimeUtils.convertTimeStamp2Str(dto.getSubmitTime()));
        }
        detailRes.setSubmitter(dto.getSubmitterId());
        detailRes.setSubmitterName(dto.getSubmitterName());
        detailRes.setCheckAreaType(dto.getCheckAreaType());
        if (dto.isSetSkuSize()) {
            detailRes.setSkuCount(dto.getSkuSize());
        }
        return detailRes;
    }


    /**
     * list转换方法
     * @param srcList
     * @param mapFunction
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F,T> List<T> convertList(List<F> srcList, java.util.function.Function<F,T> mapFunction) {
        return Optional.ofNullable(srcList).map(List::stream).orElse(Stream.empty())
                .filter(Objects::nonNull).map(mapFunction).collect(Collectors.toList());
    }


    /**
     * 类型转换(防止NPE),如果接收对象为基本类型,请指定基本类型的默认值
     * @param src
     * @param mapFunction
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F,T> T nonNullConvert(F src, java.util.function.Function<F,T> mapFunction) {
        return nonNullConvert(src, mapFunction,null);
    }



    /**
     * 类型转换(防止NPE),如果接收对象为基本类型,请指定基本类型的默认值
     * @param src
     * @param mapFunction
     * @param defaultValue
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F,T> T nonNullConvert(F src, java.util.function.Function<F,T> mapFunction, T defaultValue) {
        try {
            return Optional.ofNullable(src).map(mapFunction).orElse(defaultValue);
        } catch (Exception e) {
            log.warn("convert error", e);
            return defaultValue;
        }

    }


    public static RefundRelatedRevenueInfoVO generateRefundRelatedRevenueInfoVO(RefundRelatedOrderInfoVO refundRelatedOrderInfoVO){
        RefundRelatedRevenueInfoVO relatedRevenueInfoVO = new RefundRelatedRevenueInfoVO();
        relatedRevenueInfoVO.setChannelId(OCMSUtils.convertOrderBizType2ChannelId(refundRelatedOrderInfoVO.getOrderBizType()));
        relatedRevenueInfoVO.setViewOrderId(refundRelatedOrderInfoVO.getViewOrderId());
        relatedRevenueInfoVO.setBoothId(refundRelatedOrderInfoVO.getBoothId());
        relatedRevenueInfoVO.setBenefitUnifyCode(refundRelatedOrderInfoVO.getBenefitUnifyCode());
        return relatedRevenueInfoVO;
    }


    public static Integer getOrderViewStatusByExceptionOrderSubType(DeliveryExceptionSubTypeEnum deliveryExceptionSubTypeEnum){
        if (deliveryExceptionSubTypeEnum == null) {
            return null;
        }
        Integer orderViewStatus = null;
        switch (deliveryExceptionSubTypeEnum) {
            case NO_RIDER_ACCEPT:
                orderViewStatus = OrderViewStatusEnum.NO_RIDER_ACCEPT.getCode();
                break;
            case NO_ARRIVAL_STORE:
                orderViewStatus = OrderViewStatusEnum.NO_ARRIVAL_STORE.getCode();
                break;
            case NO_RIDER_TAKE_GOODS:
                orderViewStatus = OrderViewStatusEnum.NO_RIDER_TAKE_GOODS.getCode();
                break;
            case DELIVERY_TIMEOUT:
                orderViewStatus = OrderViewStatusEnum.DELIVERY_TIMEOUT.getCode();
                break;
            case SYSTEM_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.SYSTEM_EXCEPTION.getCode();
                break;
            case REPORT_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.REPORT_EXCEPTION.getCode();
                break;
            case TAKE_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.TAKE_EXCEPTION.getCode();
                break;
            default:
                return null;
        }
        return orderViewStatus;
    }


    public static Integer pickOrderSourceConvertChannelId(Integer orderSource) {
        if(orderSource == null){
            return null;
        }
        switch (orderSource) {
            case 0:
            case 101:
                return ChannelType.MEITUAN.getValue();
            case 151:
                return ChannelType.ELEM.getValue();
            case 201:
                return ChannelType.JD2HOME.getValue();
            case 501:
                return ChannelType.YOU_ZAN.getValue();
            default:
                return null;
        }
    }

}
