package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.utils.GsonUtil;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.CommentReplyStatusEnum;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.shangou.qnh.orderapi.enums.pc.infrastructure.ChannelTypeEnum;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.enums.pc.poi.StatusType;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommentBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommentItemTagBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@TypeDoc(
        description = "评价",
        authors = {"hejunliang"}
)
@Data
public class CommentVO {

    @FieldDoc(
            description = "门店id"
    )
    private Long poiId;

    @FieldDoc(
            description = "门店名称"
    )
    private String poiName;

    @FieldDoc(
            description = "渠道id"
    )
    private String channelId;

    @FieldDoc(
            description = "渠道名称"
    )
    private String channelName;

    @FieldDoc(
            description = "中台评价id"
    )
    private String commentId;

    @FieldDoc(
            description = "评价内容"
    )
    private String commentContent;

    @FieldDoc(
            description = "评价时间",
            rule = "yyyy-MM-dd"
    )
    private String commentTime;

    @FieldDoc(
            description = "追评内容"
    )
    private String addCommentContent;

    @FieldDoc(
            description = "追评时间",
            rule = "yyyy-MM-dd"
    )
    private String addCommentTime;

    @FieldDoc(
            description = "评价级别"

    )
    private String commentLevel;

    @FieldDoc(
            description = "订单评分"
    )
    private Integer orderScore;

    @FieldDoc(
            description = "质量评分"
    )
    private Integer qualityScore;

    @FieldDoc(
            description = "包装评分"
    )
    private Integer packingScore;

    @FieldDoc(
            description = "配送评分"
    )
    private Integer deliveryScore;

    @FieldDoc(
            description = "图片地址"

    )
    private List<String> commentPictures;

    @FieldDoc(
            description = "配送标签"
    )
    private List<String> deliveryCommentLabels;

    @FieldDoc(
            description = "订单商品列表"
    )
    private List<String> orderItemList;

    @FieldDoc(
            description = "赞商品列表"
    )
    private List<String> praiseItemList;

    @FieldDoc(
            description = "踩商品列表"
    )
    private List<String> criticItemList;

    @FieldDoc(
            description = "评论回复内容"
    )
    private String replyContent;

    @FieldDoc(
            description = "回复状态",
            rule = "NOT_REPLY:未回复, REPLYING:审核中, REPLY_FAILED:审核失败, REPLIED:已回复"
    )
    private String replyStatus;

    @FieldDoc(
            description = "回复时间",
            rule = "yyyy-MM-dd HH:mm:ss"
    )
    private String replyTime;

    @FieldDoc(
            description = "评论回复过期分钟数(commentReplyExpireInMinute分钟以内过期)"
    )
    private Integer commentReplyExpireInMinute;

    @FieldDoc(
            description = "是否可以回复"
    )
    private Boolean canReply;

    @FieldDoc(
            description = "追平评论列表，目前仅有赞渠道使用"
    )
    private List<CommentContentVO> addCommentList;

    @FieldDoc(
            description = "商家回复评论列表，目前仅有赞渠道使用"
    )
    private List<CommentContentVO> replyCommentList;

    @FieldDoc(
            description = "渠道订单编号"
    )
    private String channelOrderId;

    @FieldDoc(
            description = "商家回复评论列表，目前仅有赞渠道使用"
    )
    private List<CommentItemTagVO> itemTagList;

    @FieldDoc(
            description = "评价状态"
    )
    private Boolean isValid;
    
    @FieldDoc(
            description = "评价溯源系统匹配订单状态"
    )
    private String matchOrderStatus;

    @FieldDoc(
            description = "评价溯源系统匹配订单id列表"
    )
    private List<String> matchChannelOrderIds;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CommentContentVO{

        public String commentId; // optional

        public String commentTime; // required

        public String commentContent; // required

        public List<String> pictureList; // optional

        public CommentContentVO(String commentContent){
            this.commentContent = commentContent;
            this.commentTime = DateUtil.format(new Date(),DateUtil.YYYY_MM_DD_HH_MM_SS);
        }
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class CommentItemTagVO {
        /**
         * 渠道商品id
         */
        private Long customerSkuId;

        /**
         * 渠道商品name
         */
        private String customerSkuName;

        /**
         * 商品标签
         */
        private List<String> itemTags;
    }

    public static CommentVO build(CommentBO commentBO) {
        if (commentBO == null) {
            return null;
        }
        CommentVO commentVO = new CommentVO();
        commentVO.setPoiId(commentBO.getStoreId());
        commentVO.setChannelId(String.valueOf(commentBO.getChannelId()));
        DynamicChannelType channelTypeEnum = DynamicChannelType.findOf(commentBO.getChannelId());
        commentVO.setChannelName(channelTypeEnum != null ? channelTypeEnum.getDesc() : null);
        commentVO.setCommentId(commentBO.getCommentId());
        commentVO.setCommentContent(commentBO.getCommentContent());
        commentVO.setCommentTime(commentBO.getCommentTime() != null ?
                DateUtil.formatLocalDateTime(commentBO.getCommentTime(), Constants.DateFormats.DAY_FORMAT) : null);

        commentVO.setAddCommentTime(commentBO.getAddCommentTime() != null ?
                DateUtil.formatLocalDateTime(commentBO.getAddCommentTime(), Constants.DateFormats.DAY_FORMAT) : null);
        commentVO.setCommentLevel(commentBO.getCommentLevel());
        commentVO.setOrderScore(commentBO.getOrderScore());
        commentVO.setQualityScore(commentBO.getQualityScore());
        commentVO.setPackingScore(commentBO.getPackingScore());
        commentVO.setDeliveryScore(commentBO.getDeliveryScore());
        commentVO.setCommentPictures(CollectionUtils.isNotEmpty(commentBO.getCommentPictures()) ?
                commentBO.getCommentPictures() : Collections.emptyList());
        commentVO.setDeliveryCommentLabels(CollectionUtils.isNotEmpty(commentBO.getDeliveryCommentLabels()) ?
                commentBO.getDeliveryCommentLabels() : Collections.emptyList());
        commentVO.setOrderItemList(CollectionUtils.isNotEmpty(commentBO.getOrderItemList()) ?
                commentBO.getOrderItemList() : Collections.emptyList());
        commentVO.setPraiseItemList(CollectionUtils.isNotEmpty(commentBO.getPraiseItemList()) ?
                commentBO.getPraiseItemList() : Collections.emptyList());
        commentVO.setCriticItemList(CollectionUtils.isNotEmpty(commentBO.getCriticItemList()) ?
                commentBO.getCriticItemList() : Collections.emptyList());

        if(commentBO.getChannelId()!=null&&commentBO.getChannelId() == ChannelTypeEnum.YOUZAN.getCode()){
            commentVO.setReplyCommentList(calculateReplyContentList(commentBO.getReplyStatus(), commentBO.getReplyContent(), commentBO.getReplyDraft()));
            commentVO.setAddCommentList(buildCommentContentVOList(commentBO.getAddCommentContent()));
        }else{
            commentVO.setReplyContent(calculateReplyContent(commentBO.getReplyStatus(), commentBO.getReplyContent(), commentBO.getReplyDraft()));
            commentVO.setAddCommentContent(commentBO.getAddCommentContent());
        }
        commentVO.setReplyStatus(commentBO.getReplyStatus());
        commentVO.setReplyTime(calculateReplyTime(commentBO.getReplyStatus(), commentBO.getReplyTime(), commentBO.getReplyDraftTime()));
        commentVO.setCommentReplyExpireInMinute(commentBO.getCommentReplyExpireInMinute());
        commentVO.setCanReply(commentBO.getCanReply());
        commentVO.setChannelOrderId(commentBO.getChannelOrderId());
        commentVO.setItemTagList(ConverterUtils.convertList(commentBO.getItemTagList(), CommentVO::convertToCommentItemTagVO));
        commentVO.setIsValid(commentBO.getIsValid());
        commentVO.setMatchOrderStatus(commentBO.getMatchOrderStatus());
        commentVO.setMatchChannelOrderIds(commentBO.getMatchChannelOrderIds());
        return commentVO;
    }

    private static CommentItemTagVO convertToCommentItemTagVO(CommentItemTagBO itemTagBO) {
        CommentItemTagVO itemTagVO = new CommentItemTagVO();
        itemTagVO.setCustomerSkuId(itemTagBO.getCustomerSkuId());
        itemTagVO.setCustomerSkuName(itemTagBO.getCustomerSkuName());
        itemTagVO.setItemTags(itemTagBO.getItemTags());
        return itemTagVO;
    }

    private static String calculateReplyContent(String replyStatus, String replyContent, String replyDraft) {
        if (CommentReplyStatusEnum.REPLIED.name().equals(replyStatus)) {
            return StringUtils.isNotEmpty(replyContent) ? replyContent : Constants.Comment.COMMENT_REPLY_BY_OTHER_PLATFORM_SHOW_CONTENT;
        } else {
            return replyDraft;
        }
    }

    private static String calculateReplyTime(String replyStatus, LocalDateTime replyTime, LocalDateTime replyDraftTime) {
        if (CommentReplyStatusEnum.REPLIED.name().equals(replyStatus)) {
            return replyTime != null ? DateUtil.formatLocalDateTime(replyTime, DateUtil.YYYY_MM_DD_HH_MM_SS) : null;
        } else {
            return replyDraftTime != null ? DateUtil.formatLocalDateTime(replyDraftTime, DateUtil.YYYY_MM_DD_HH_MM_SS) : null;
        }
    }
    /**
     * 有赞渠道新增字段，为了兼容多条回复与追评
     * @param replyStatus
     * @param replyContent
     * @param replyDraft
     * @return
     */
    private static List<CommentContentVO> calculateReplyContentList(String replyStatus, String replyContent, String replyDraft) {
        if (CommentReplyStatusEnum.REPLIED.name().equals(replyStatus)) {
            return StringUtils.isNotEmpty(replyContent) ? buildCommentContentVOList(replyContent): Lists.newArrayList(new CommentContentVO(Constants.Comment.COMMENT_REPLY_BY_OTHER_PLATFORM_SHOW_CONTENT));
        } else {
            return Lists.newArrayList(new CommentContentVO(replyDraft));
        }
    }

    private static List<CommentContentVO> buildCommentContentVOList(String content) {
        if(StringUtils.isNotBlank(content)){
            return GsonUtil.fromJSonList(content,CommentContentVO.class);
        }
        return null;
    }
}
