package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;
// Copyright (C) 2019 Meituan
// All rights reserved

import lombok.Data;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentStatChannelDTO;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentStatStoreDTO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @class: CommentStoreStatBO
 * @date: 2019-07-08 16:09:44
 * @desc:
 */
@Data
public class CommentStoreStatBO {
    /**
     * 门店Id
     */
    private Long poiId;

    /**
     * channel2Static
     */
    private Map<Integer, List<CommentStatBO>> statistics = new HashMap<>();


    public CommentStoreStatBO(ChannelCommentStatStoreDTO dto) {
        this.poiId = dto.getStoreId();
        for (ChannelCommentStatChannelDTO detailDto : dto.getCommentStatChannelDTOList()) {
            this.statistics.put(detailDto.getChannelId(),
                    detailDto.getCommentLabelStatDTOList().stream().map(CommentStatBO::new).collect(Collectors.toList()));
        }
    }

}
