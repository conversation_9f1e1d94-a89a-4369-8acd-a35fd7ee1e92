package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.meituan.shangou.empower.auth.thrift.vo.*;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/24
 **/
@Data
@Builder
public class AccountPageListBo {

    /**
     * 分页信息
     */
    private PageInfoVo pageInfo;

    /**
     * 账号列表
     */
    @Deprecated
    private List<AccountInfoVo> accountInfoList;
    /**
     * 新的账号列表返回值
     */
    private List<AccountPageInfoVO> accountPageInfoVOList;


    public static AccountPageListBo build(QueryAccountInfoListResponse response) {
        return builder()
                .pageInfo(response.getPageInfo())
                .accountInfoList(response.getAccountInfoList())
                .build();
    }


    public static AccountPageListBo build(QueryAccountPageInfoResponse response) {
        return AccountPageListBo.builder()
                .pageInfo(response.getPageInfo())
                .accountPageInfoVOList(response.getAccountPageInfoVOs())
                .build();
    }
}
