package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import lombok.Data;

import java.util.Objects;

/**
 * DeliveryOperationVo
 *
 * <AUTHOR>
 * @since 2023/3/6
 */
@Data
public class DeliveryLogVo implements Comparable<DeliveryLogVo> {

    /**
     * 被记录者名称
     */
    private String recordedName;

    /**
     * 时间
     */
    private Long time;

    /**
     * 描述
     */
    private String description;

    /**
     * 分类
     */
    private Integer category;

    public DeliveryLogVo(String recordedName, String description, int category, long time) {
        this.recordedName = recordedName;
        this.description = description;
        this.category = category;
        this.time = time;
    }

    @Override
    public int compareTo(DeliveryLogVo o) {
        Objects.requireNonNull(o, "Comparing object must not be null");
        return Long.compare(time, o.getTime());
    }

    public interface DeliveryLogCategory {

        /**
         * 状态日志
         */
        int STATUS_LOG = 1;

        /**
         * 操作日志
         */
        int OPERATION_LOG = 2;

    }

}
