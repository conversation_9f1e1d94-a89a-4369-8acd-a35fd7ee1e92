package com.sankuai.shangou.qnh.orderapi.utils;

import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.ProductLabelVO;
import com.meituan.shangou.saas.order.platform.enums.OrderProductLabelEnum;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ChannelLabelVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.CompensationVO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MoneyUtils;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.JsonNode;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CompensationUtil {

    private static final ObjectMapper objMapper = new ObjectMapper();

    /**
     * 构建赔付模型列表
     * @param compensation
     * @return
     */
    public static List<CompensationVO> buildCompensationModelList(String compensation){
        try {
            if(StringUtils.isEmpty(compensation)){
                return new ArrayList<>();
            }
            JsonNode jsonNode = objMapper.readTree(compensation);
            if(jsonNode.isObject()){
                return Collections.singletonList(buildCompensationModel(compensation));
            }
            if(!jsonNode.isArray()){
                return new ArrayList<>();
            }
            return JacksonUtils.fromJson(compensation, new TypeReference<List<CompensationVO>>() {});
        }catch (Exception e){
            return new ArrayList<>();
        }
    }

    /**
     * 构建赔付模型
     * @param compensation
     * @return
     */
    public static CompensationVO buildCompensationModel(String compensation){
        try {
            if(StringUtils.isEmpty(compensation)){
                return new CompensationVO();
            }
            JsonNode jsonNode = objMapper.readTree(compensation);
            if(!jsonNode.isObject()){
                return new CompensationVO();
            }
            CompensationVO compensationVO = JacksonUtils.fromJson(compensation, CompensationVO.class);
            // 兼容历史数据，历史数据单位为元
            compensationVO.setAmount(MoneyUtils.yuanToCentInt(compensationVO.getAmount()));
            return compensationVO;
        }catch (Exception e){
            return new CompensationVO();
        }
    }
}
