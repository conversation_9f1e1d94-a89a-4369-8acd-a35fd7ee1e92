package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.sankuai.shangou.qnh.orderapi.domain.result.pc.MultiStageOptionable;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiChildOption;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOptionMultiStage;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2022/12/03 19:54
 * @Description:配送类型二级
 */
@Getter
public enum DeliveryTypeEnum implements MultiStageOptionable {

    /* --------- 订单平台配送 --------- */
    ORDER_PLATFORM_DELIVERY(1L, "platformDelivery", "平台配送"),
    UN_KNOWN(0L, 1L, "unKnown", "未知"),
    ZHUAN_SONG(5L, 1L, "zhuanSong", "专送"),
    KUAI_SONG(10L, 1L, "kuaiSong", "快送"),
    COMBO(15L, 1L, "combo", "混合送"),
    ZONG_BAO(20L, 1L, "zongBao", "众包"),
    SELF_DELIVERY(25L, 1L, "selfDelivery", "商家自配送"),
    QIKE(30L, 1L, "qike", "企客配送"),

    JDDJ_DADA_ZHAUN_SONG(35L, 1L, "jddjDadaZhaunSong", "京东到家达达专送"),
    JDDJ_TONG_CHENG(40L, 1L, "jddjTongCheng", "京东到家同城快递"),

    ELEM_ZONG_BAO(45L, 1L, "elemZongBao", "蜂鸟众包"),
    ELEM_PLATFORM(50L, 1L, "elemPlatform", "蜂鸟平台配送"),
    ELEM_DELIVERY(55L, 1L, "elemDelivery", "快递配送"),
    ELEM_HALF_DAY(60L, 1L, "elemHalfDay", "半日达"),
    ELEM_HOUR(65L, 1L, "elemHour", "鸟潮小时达"),
    YOUZAN_SAME_CITY(70L, 1L, "youZanSameCity", "有赞同城送"),
    DOUYIN_HOUR(75L, 1L, "douYinHour", "抖音小时达"),
    OPEN_API_DELIVERY(80L,1L,"openApiDelivery","渠道管理配送"),
    TXD_DELIVERY(85L,1L,"txdDelivery","淘鲜达平台配送"),

    /* --------- 商家自行配送 --------- */
    MERCHANT_DELIVERY(2L,"merchantDelivery","自配送"),
    MERCHANT_SELF_DELIVERY(2L, 2L,"merchantSelfDelivery","商家自送"),

    /* --------- 自建平台 --------- */
    HAI_KUI_DELIVERY(900L,2L,"haiKuiDelivery","自建平台-美团海葵"),

    FENG_NIAO_DELIVERY(800L,2L,"fengNiaoDelivery","自建平台-蜂鸟即配"),

    /* --------- 聚合运力平台 --------- */
    AGGREGATION_DELIVERY(-1L,2L,"aggregationDelivery","聚合运力配送"),

    AGGREGATE_DELIVERY_MEITUAN_TUAN(10000L,2L,"aggregateDeliveryMeituanTuan","聚合运力-美团海葵"),

    AGGREGATE_DELIVERY_SHUNFENG(10001L,2L,"aggregateDeliveryShunfeng","聚合运力-顺丰同城"),

    AGGREGATE_DELIVERY_DADA(10002L,2L,"aggregateDeliveryDada","聚合运力-达达快送"),

    AGGREGATE_DELIVERY_FENGNIAO(10004L,2L,"aggregateDeliveryFengniao","聚合运力-蜂鸟即配"),

    /* --------- 麦芽田平台 --------- */
    MALT_FARM(-2L,2L,"maltFarm","麦芽田配送-暂未接单"),

    FARM_DELIVERY_MEITUAN(20000L,2L,"farmDeliveryMeituan","麦芽田-美团配送"),

    FARM_DELIVERY_SHUNFENG(20001L,2L,"farmDeliveryShunfeng","麦芽田-顺丰同城"),

    FARM_DELIVERY_DADA(20002L,2L,"farmDeliveryDada","麦芽田-达达快送"),

    FARM_DELIVERY_SHANSONG(20003L,2L,"farmDeliveryShansong","麦芽田-闪送"),

    FARM_DELIVERY_UU(20004L,2L,"farmDeliveryUu","麦芽田-UU跑腿"),

    FARM_DELIVERY_DIANWODA(20005L,2L,"farmDeliveryDianwoda","麦芽田-点我达"),

    FARM_DELIVERY_FENGNIAO(20006L,2L,"farmDeliveryFengniao","麦芽田-蜂鸟跑腿"),

    FARM_DELIVERY_FENGNIAO_KA(20023L,2L,"farmDeliveryFengniaoKa","麦芽田-蜂鸟配送"),

    FARM_DELIVERY_PAO_TUI(20024L,2L,"farmDeliveryPaoTui","麦芽田-美团跑腿"),

    FARM_DELIVERY_365(20025L,2L,"farmDelivery365","麦芽田-365跑腿"),

    FARM_DELIVERY_GUOXIAODI(20026L,2L,"farmDeliveryGuoxiaodi","麦芽田-裹小递"),

    FARM_DELIVERY_CAOSONG(20027L,2L,"farmDeliveryCaosong","麦芽田-曹操送"),

    FARM_DELIVERY_KELOOP(20028L,2L,"farmDeliveryKeloop","麦芽田-快跑者"),

    FARM_DELIVERY_BANGLA(20029L,2L,"farmDeliveryBangla","麦芽田-帮啦跑腿"),

    FARM_DELIVERY_CAOCAO(20030L,2L,"farmDeliveryCaocao","麦芽田-曹操跑腿"),

    FARM_DELIVERY_FUWU(20031L,2L,"farmDeliveryFuwu","麦芽田-快服务"),

    FARM_DELIVERY_IPAOTUI(20032L,2L,"farmDeliveryIpaotui","麦芽田-爱跑腿"),

    FARM_DELIVERY_WUKONG(20033L,2L,"farmDeliveryWukong","麦芽田-悟空快跑"),

    FARM_DELIVERY_HAOJI(20034L,2L,"farmDeliveryHaoji","麦芽田-好急"),

    FARM_DELIVERY_MTBS(20035L,2L,"farmDeliveryMtbs","麦芽田-美团跑腿帮送"),

    FARM_DELIVERY_SHUNFENGC(20036L,2L,"farmDeliveryShunfengc","麦芽田-顺丰C"),

    FARM_DELIVERY_LAI_DA(20037L,2L,"farmDeliveryLaida","麦芽田-来答配送"),

    FARM_DELIVERY_SONGDONGXI(20038L,2L,"farmDeliverySongDongXi","麦芽田-送个东西"),

    FARM_DELIVERY_ZHONGLIBAN(20039L,2L,"farmDeliveryZhongLiBan","麦芽田-中里办"),

    FARM_DELIVERY_HANG_YOU_DA(20040L,2L,"farmDeliveryHangYouDa","麦芽田-行优达"),

    FARM_DELIVERY_TT_KUAI_SONG(20041L,2L,"farmDeliveryTuiTuiKuaiSong","麦芽田-腿腿快送"),

    FARM_DELIVERY_KAO_PU_SONG(20042L,2L,"farmDeliveryKaoPuSong","麦芽田-靠谱送"),

    FARM_DELIVERY_KUAI_NAN_PT(20043L,2L,"farmDeliveryKuaiNanPT","麦芽田-快男跑腿"),

    FARM_DELIVERY_NA_DOU_DA(20044L,2L,"farmDeliveryNaDouDa","麦芽田-哪都达"),

    FARM_DELIVERY_SHENG_DIAN_SONG(20045L,2L,"farmDeliveryShengDianSong","麦芽田-省点送"),

    FARM_DELIVERY_QU_LAI_DA(20046L,2L,"farmDeliveryQuLaiDa","麦芽田-趣来达"),

    FARM_DELIVERY_FENG_QI(20047L,2L,"farmDeliveryFengQi","麦芽田-蜂骑快送"),

    FARM_DELIVERY_JIAO_DIAN(20048L,2L,"farmDeliveryJiaoDian","麦芽田-交点配送"),

    FARM_DELIVERY_SONG_DAO(20049L,2L,"farmDeliverySongDao","麦芽田-送道"),

    FARM_DELIVERY_HU_LAN_BANG(20050L,2L,"farmDeliveryHuLanBang","麦芽田-呼兰闪送帮"),

    FARM_DELIVERY_FU_WU_JIA(20051L,2L,"farmDeliveryFuWuJia","麦芽田-服务之家"),

    FARM_DELIVERY_XIA_KE_SONG(20052L,2L,"farmDeliveryXiaKeSong","麦芽田-同达快送"),

    FARM_DELIVERY_SJS(20053L,2L,"farmDeliverySJS","麦芽田-闪急送"),

    FARM_DELIVERY_TJPS(20054L,2L,"farmDeliveryTJPS","麦芽田-同极配送"),

    FARM_DELIVERY_HUO_LA_LA(20055L,2L,"farmDeliveryHuoLaLa","麦芽田-货拉拉"),

    FARM_DELIVERY_HUOLL_PT(20056L,2L,"farmDeliveryHuoLLPT","麦芽田-货拉拉跑腿"),

    FARM_DELIVERY_SBPS(20057L,2L,"farmDeliverySBPS","麦芽田-神兵跑腿"),

    FARM_DELIVERY_WLDS(20058L,2L,"farmDeliveryWLDS","麦芽田-抖送"),

    FARM_DELIVERY_SONG_JIA(20059L,2L,"farmDeliverySongJia","麦芽田-送佳"),

    FARM_DELIVERY_SU_SONG(20060L,2L,"farmDeliverySuSong","麦芽田-速送"),

    FARM_DELIVERY_QING_YUN(20061L,2L,"farmDeliveryQingYun","麦芽田-轻云送"),

    FARM_DELIVERY_KUAI_DA(20062L,2L,"farmDeliveryKuaiDa","麦芽田-快达配送"),

    FARM_DELIVERY_DDKS_PT(20063L,2L,"farmDeliveryDDKSPT","麦芽田-滴滴跑腿特惠"),

    FARM_DELIVERY_DDKS_ZS(20064L,2L,"farmDeliveryDDKSZS","麦芽田-滴滴跑腿直送"),

    FARM_DELIVERY_DDKS_QC(20065L,2L,"farmDeliveryDDKSQC","麦芽田-滴滴汽车送"),

    FARM_DELIVERY_DDKS(20066L,2L,"farmDeliveryDDKS","麦芽田-滴滴快送"),

    FARM_DELIVERY_TDKS(20067L,2L,"farmDeliveryTDKS","麦芽田-同达快送(自主)"),

    FARM_DELIVERY_SHAN_PAO(20068L,2L,"farmDeliveryShanPao","麦芽田-闪跑侠配送"),

    FARM_DELIVERY_KUAI_MA(20069L,2L,"farmDeliveryKuaiMa","麦芽田-快马跑腿"),

    FARM_DELIVERY_HEI_QI(20070L,2L,"farmDeliveryHeiQi","麦芽田-黑骑配送"),

    FARM_DELIVERY_SHU_GUO_PAI_MERCHANT(20071L,2L,"farmDeliveryShuGuoPaiMerchant","麦芽田-美团跑腿（商）"),

    FARM_DELIVERY_MERCHANT(29998L,2L,"farmDeliveryMerchant","麦芽田-商家自配"),

    FARM_DELIVERY_UNKNOW(29999L,2L,"FARM_DELIVERY_UNKNOW","麦芽田-未知配送商"),

    DAP_DELIVERY(-4L,2L,"dapDelivery","青云聚信平台"),

    DAP_DELIVERY_PAO_TUI(40000L,2L,"dapDeliveryPaoTui","青云聚信平台-美团跑腿"),

    DAP_DELIVERY_SHUNFENG(40001L,2L,"dapDeliveryShunfeng","青云聚信平台-顺丰同城"),

    DAP_DELIVERY_DADA(40002L,2L,"dapDeliveryDada","青云聚信平台-达达快送"),

    DAP_DELIVERY_SHANSONG(40003L,2L,"dapDeliveryShansong","青云聚信平台-闪送"),

    DAP_DELIVERY_GUOXIAODI(40004L,2L,"dapDeliveryQuoxiaodi","青云聚信平台-裹小递"),

    DAP_DELIVERY_UU(40005L,2L,"dapDeliveryUu","青云聚信平台-UU跑腿"),

    DAP_DELIVERY_MEI_TUAN(40006L,2L,"dapDeliveryMeiTuan","青云聚信平台-美团跑腿零售"),

    DAP_DELIVERY_SHIPAIS(40007L,2L,"dapDeliveryShiPaiS","青云聚信平台-上海食派士商贸发展有限公司"),

    DAP_DELIVERY_FAST(40008L,2L,"dapDeliveryFast","青云聚信平台-快服务"),

    DAP_DELIVERY_CAO_CAO(40009L,2L,"dapDeliveryCaoCao","青云聚信平台-曹操跑腿"),

    DAP_DELIVERY_AI_PAO(40010L,2L,"dapDeliveryAiPao","青云聚信平台-爱跑腿"),

    DAP_DELIVERY_KUAI_PAO(40011L,2L,"dapDeliveryKuaiPao","青云聚信平台-快跑者"),

    DAP_DELIVERY_DA_YOU(40012L,2L,"dapDeliveryDaYou","青云聚信平台-大有配送"),

    DAP_DELIVERY_SONG_DX(40013L,2L,"dapDeliverySongDongxi","青云聚信平台-送个东西"),

    DAP_DELIVERY_KAO_PU(40014L,2L,"dapDeliveryKaoPu","青云聚信平台-靠谱送"),

    DAP_DELIVERY_LAN_ZHU(40015L,2L,"dapDeliveryLanZhu","青云聚信平台-懒猪快送"),

    DAP_DELIVERY_HAO_JI(40016L,2L,"dapDeliveryHaoJi","青云聚信平台-好急"),

    DAP_DELIVERY_MT_Pei(40017L,2L,"dapDeliverymtPei","青云聚信平台-美团配送"),

    DAP_DELIVERY_KUAI_GOU(40018L,2L,"dapDeliveryKuaiGou","青云聚信平台-快狗打车"),

    DAP_DELIVERY_KUAI_NAN(40019L,2L,"dapDeliveryKuaiNan","青云聚信平台-快男跑腿"),

    DAP_DELIVERY_MT_PAO(40020L,2L,"dapDeliveryNaDouDa","青云聚信平台-哪都达"),

    DAP_DELIVERY_KSH(40021L,2L,"dapDeliveryKaiShiSong","青云聚信平台-开始送"),

    DAP_DELIVERY_BL(40022L,2L,"dapDeliveryBangLa","青云聚信平台-帮啦"),

    DAP_DELIVERY_CP_CITY(40023L,2L,"dapDeliveryChaoPaoTongCheng","青云聚信平台-超跑同城"),

    DAP_DELIVERY_PT_365(40024L,2L,"dapDelivery365PaoTui","青云聚信平台-365跑腿"),

    DAP_DELIVERY_QB_SHANSONG(40025L,2L,"dapDeliveryQiBangShanSong","青云聚信平台-骑帮闪送"),

    DAP_DELIVERY_YB_TONGCHENG(40026L,2L,"dapDeliveryYiBuTongcheng","青云聚信平台-一步同城"),

    DAP_DELIVERY_PT_UNION(40027L,2L,"dapDeliveryPaoTuiLianMeng","青云聚信平台-跑腿联盟"),

    DAP_DELIVERY_WUKONG_KUAIPAO(40028L,2L,"dapDeliveryWuKongKuaiPao","青云聚信平台-悟空快跑"),

    DAP_DELIVERY_SONG_XIAO_XIAN(40029L,2L,"dapDeliverySongXiaoXian","青云聚信平台-送小闲"),

    DAP_DELIVERY_CAO_CAO_SONG(40030L,2L,"dapDeliveryCaoCaoSong","青云聚信平台-曹操送"),

    DAP_DELIVERY_DA_SHENG_JI_SONG(40031L,2L,"dapDeliveryDaShengJiSong","青云聚信平台-大圣急送"),

    DAP_DELIVERY_QU_LAI_DA(40032L,2L,"dapDeliveryQuLaiDa","青云聚信平台-趣来达"),

    DAP_DELIVERY_LIANG_PAO_PAO(40033L,2L,"dapDeliveryLiangPaoPao","青云聚信平台-梁跑跑"),

    DAP_DELIVERY_FENG_QI_KUAI_SONG(40034L,2L,"dapDeliveryFengQiKuaiSong","青云聚信平台-蜂骑快送"),

    DAP_DELIVERY_DOU_SONG(40035L,2L,"dapDeliveryDouSong","青云聚信平台-抖送"),

    DAP_DELIVERY_ZHONG_LI_BAN(40036L,2L,"dapDeliveryZhongLiBan","青云聚信平台-中里办"),

    DAP_DELIVERY_YUN_XIAO_PAO(40037L,2L,"dapDeliveryYunXiaoPao","青云聚信平台-云小跑"),

    DAP_DELIVERY_HANG_YOU_DA(40038L,2L,"dapDeliveryHangYouDa","青云聚信平台-行优达"),

    DAP_DELIVERY_U_JI_SONG(40039L,2L,"dapDeliveryXiaoUJiSong","青云聚信平台-小U急送"),

    DAP_DELIVERY_TT_KUAI_SONG(40040L,2L,"dapDeliveryTuiTuiKuaiSong","青云聚信平台-腿腿快送"),
    // 青云侧承运商code 100046
    DAP_DELIVERY_SCHOOL_ZHUAN_SONG(40041L,2L,"dapDeliveryXiaoNeiZhuanSong","青云聚信平台-校内专送"),

    DAP_DELIVERY_FLASH_MAN(40042L,2L,"dapDeliveryFlashMan","青云聚信平台-闪跑侠"),

    DAP_DELIVERY_SCHOOL_WM(40043L,2L,"dapDeliverySchoolWaiMaiGou","青云聚信平台-校内外卖购"),

    DAP_DELIVERY_CITY_SLACKER(40044L,2L,"dapDeliveryCitySlacker","青云聚信平台-同城懒人"),

    DAP_DELIVERY_HLL_ENTERPRISE(40045L,2L,"dapDeliveryHuoLaLaENTERPRISE","青云聚信平台-货拉拉企业版"),

    DAP_DELIVERY_DD_FREIGHT(40046L,2L,"dapDeliveryDiDiFreight","青云聚信平台-滴滴货运"),

    DAP_DELIVERY_KUAI_DA(40047L,2L,"dapDeliveryKuaiDa","青云聚信平台-快达"),

    DAP_DELIVERY_XIA_KE_SONG(40048L,2L,"dapDeliveryXiaKeSong","青云聚信平台-同达快送"),

    DAP_DELIVERY_DMD(40050L,2L,"dapDeliveryDMD","青云聚信平台-单满多"),

    DAP_DELIVERY_YI_MAO(40051L,2L,"dapDeliveryYiMao","青云聚信平台-逸猫快送"),

    DAP_DELIVERY_KUAI_TUO(40052L,2L,"dapDeliveryKuaiTuo","青云聚信平台-快驼"),

    DAP_DELIVERY_WD_PAO_TUI(40054L,2L,"dapDeliveryWdPaoTui","青云聚信平台-微顶跑腿"),

    DAP_DELIVERY_RONG_CHEN(40055L,2L,"dapDeliveryRongChen","青云聚信平台-荣宸快送"),

    DAP_DELIVERY_JIN_QI(40056L,2L,"dapDeliveryJinQi","青云聚信平台-金骑配送"),

    DAP_DELIVERY_DB_PAO_TUI(40057L,2L,"dapDeliveryDbPaoTui","青云聚信平台-代帮跑腿"),

    DAP_DELIVERY_LL_PAO_TUI(40058L,2L,"dapDeliveryLlPaoTui","青云聚信平台-来啦跑腿"),

    DAP_DELIVERY_XIAO_LING(40059L,2L,"dapDeliveryXiaoLing","青云聚信平台-小羚配送"),

    DAP_DELIVERY_BSXY(40060L,2L,"dapDeliveryBSXY","青云聚信平台-必宿校园"),

    DAP_DELIVERY_RONG_BANG(40061L,2L,"dapDeliveryRongBang","青云聚信平台-荣帮"),

    DAP_DELIVERY_TY_PAO_TUI(40062L,2L,"dapDeliveryTyPaoTui","青云聚信平台-同用跑腿"),

    DAP_DELIVERY_XUN_DA(40063L,2L,"dapDeliveryXunDa","青云聚信平台-迅达配送"),

    DAP_DELIVERY_LANLING_PAO_TUI(40064L,2L,"dapDeliveryLanLingPaoTui","青云聚信平台-兰陵跑腿"),

    DAP_DELIVERY_ML_PAO_TUI(40066L,2L,"dapDeliveryMLPaoTui","青云聚信平台-麻利跑腿"),

    DAP_DELIVERY_WEN_DAI(40067L,2L,"dapDeliveryWenDai","青云聚信平台-温代配送"),

    DAP_DELIVERY_FENGNIAO_KA(40068L,2L,"dapDeliveryFengNiaoKa","青云聚信平台-蜂鸟配送"),

    DAP_DELIVERY_XSQC(40069L,2L,"dapDeliveryXSQC","青云聚信平台-响送全成"),

    DAP_DELIVERY_DD_QS(40070L,2L,"dapDeliveryDDQS","青云聚信平台-DD骑士"),

    DAP_DELIVERY_LWM(40071L,2L,"dapDeliveryLWM","青云聚信平台-乐外卖"),

    DAP_DELIVERY_SJS(40072L,2L,"dapDeliverySJS","青云聚信平台-闪急送"),

    DAP_DELIVERY_HEI_QI(40073L,2L,"dapDeliveryHeiQi","青云聚信平台-黑骑配送"),

    DAP_DELIVERY_SB_PAO_TUI(40074L,2L,"dapDeliverySbPaoTui","青云聚信平台-神兵跑腿"),

    // 青云侧承运商code 100078
    DAP_DELIVERY_SCHOOL_ZS(40075L,2L,"dapDeliverySchoolZS","青云聚信平台-校内专送"),

    DAP_DELIVERY_FHTC(40076L,2L,"dapDeliveryFHTC","青云聚信平台-丰和同城"),

    DAP_DELIVERY_CHAO_FENG(40077L,2L,"dapDeliveryChaoFeng","青云聚信平台-超风速送"),

    DAP_DELIVERY_SPSDX(40079L,2L,"dapDeliverySPSDX","青云聚信平台-上铺闪电侠"),

    DAP_DELIVERY_HL_PAO_TUI(40080L,2L,"dapDeliveryHlPaoTui","青云聚信平台-呼拉跑腿"),

    DAP_DELIVERY_WSKD(40081L,2L,"dapDeliveryWSKD","青云聚信平台-微刷快点"),

    DAP_DELIVERY_YKM(40082L,2L,"dapDeliveryYKM","青云聚信平台-云快卖"),

    DAP_DELIVERY_BJ_PAO_TUI(40083L,2L,"dapDeliveryBjPaoTui","青云聚信平台-八戒跑腿"),

    DAP_DELIVERY_SYTC(40084L,2L,"dapDeliverySYTCJS","青云聚信平台-思雨同城急送"),

    DAP_DELIVERY_BJKJ(40085L,2L,"dapDeliveryBJKJ","青云聚信平台-北京快桔安运"),

    DAP_DELIVERY_25DU(40086L,2L,"dapDelivery25Du","青云聚信平台-25度"),

    DAP_DELIVERY_JTKJ(40087L,2L,"dapDeliveryJTKJ","青云聚信平台-九筒科技"),

    DAP_DELIVERY_XSD(40088L,2L,"dapDeliveryXSD","青云聚信平台-信速达"),

    DAP_DELIVERY_SONG_DAO(40089L,2L,"dapDeliverySongDao","青云聚信平台-送道"),

    DAP_DELIVERY_DONG_HAN(40090L,2L,"dapDeliveryDongHan","青云聚信平台-东海速送"),

    DAP_DELIVERY_DDFL(40091L,2L,"dapDeliveryDDFL","青云聚信平台-达达分流"),

    DAP_DELIVERY_PAO_TUI_MERCHANT(40092L,2L,"dapDeliveryPaoTuiMerchant","青云聚信平台-美团跑腿（商）"),

    DAP_DELIVERY_UNKNOW(49999L,2L,"dapDeliveryUnknow","青云聚信平台-未知配送商"),

    OTHER_SELF_DELIVERY_MNG(60000L, 2L, "otherSelfDeliveryMng", "非牵牛花管理配送"),

    STORE_DELIVERY_PARENT(3L,"storeDeliveryParent","到店自提"),
    STORE_DELIVERY(3L,3L, "storeDelivery","到店自提");

    DeliveryTypeEnum(Long id, Long parentId, String code, String desc) {
        this.id = id;
        this.parentId = parentId;
        this.code = code;
        this.desc = desc;
    }

    public static DeliveryTypeEnum getByCode(String code) {
        for (DeliveryTypeEnum label : DeliveryTypeEnum.values()) {
            if (Objects.equals(label.code, code)) {
                return label;
            }
        }
        return null;
    }

    public static DeliveryTypeEnum getByParentId(Long id) {
        for (DeliveryTypeEnum label : DeliveryTypeEnum.values()) {
            if (Objects.equals(label.id, id) && label.parentId == null) {
                return label;
            }
        }
        return null;
    }

    public static DeliveryTypeEnum getByChildId(Long id) {
        for (DeliveryTypeEnum label : DeliveryTypeEnum.values()) {
            if (Objects.equals(label.id, id) && label.parentId != null) {
                return label;
            }
        }
        return null;
    }


    DeliveryTypeEnum(Long id, String code, String desc) {
        this(id, null, code, desc);
    }

    private Long id;

    private Long parentId;

    private String code;

    private String desc;

    @Override
    public List<UiOptionMultiStage> toOptions() {
        return Arrays.asList(DeliveryTypeEnum.values()).stream().filter(item -> Objects.isNull(item.getParentId()))
                .map(e -> new UiOptionMultiStage(Math.toIntExact(e.getId()), e.getCode(), e.getDesc(), getChildOptionList(e.getId()))).collect(Collectors.toList());
    }

    public List<UiChildOption> getChildOptionList(Long parentId) {
        List<UiChildOption> childOptionList = new ArrayList<>();
        for (DeliveryTypeEnum label : DeliveryTypeEnum.values()) {
            if (Objects.nonNull(label.getParentId()) && parentId.equals(label.getParentId())) {
                childOptionList.add(new UiChildOption(Math.toIntExact(label.getId()), label.getCode(), Math.toIntExact(label.getParentId()), label.getDesc()));
            }
        }
        return childOptionList;
    }

    }

