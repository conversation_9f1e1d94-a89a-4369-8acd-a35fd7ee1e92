package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.tenant.thrift.dto.config.BizInfoDto;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.BizInfoVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: <EMAIL>
 * @Date: 2019/6/23 17:49
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BizInfoBO {

    private Integer bizId;

    private String bizName;

    public BizInfoBO(BizInfoDto bizInfoDto) {
        this.bizId = bizInfoDto.getBizId();
        this.bizName = bizInfoDto.getBizName();
    }

    public BizInfoVO toBizInfoVO() {
        BizInfoVO bizInfoVO = new BizInfoVO();

        bizInfoVO.setBizId(String.valueOf(bizId));
        bizInfoVO.setBizName(bizName);

        return bizInfoVO;
    }

}
