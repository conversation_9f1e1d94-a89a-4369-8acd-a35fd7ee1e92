package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelCategoryWithPathDTO;
import com.sankuai.shangou.qnh.orderapi.enums.pc.infrastructure.ChannelTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/4/12 15:36
 * @Description:
 */
@TypeDoc(
        name = "渠道后台分类VO对象",
        description = "渠道后台分类VO对象"
)
@Data
@ToString
@EqualsAndHashCode
public class ChannelCategoryVO {

    @FieldDoc(
            description = "渠道ID"
    )
    private String channelId;

    @FieldDoc(
            description = "渠道名称"
    )
    private String channelName;

    @FieldDoc(
            description = "渠道类目编码"
    )
    private String categoryId;

    @FieldDoc(
            description = "渠道类目名称"
    )
    private String categoryName;

    @FieldDoc(
            description = "渠道类目编码全路径"
    )
    private String idPath;

    @FieldDoc(
            description = "渠道类目名称全路径"
    )
    private String namePath;

    @FieldDoc(
            description = "渠道类目动态信息,单渠道必填",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目动态信息")
    private List<ChannelDynamicInfoVO> channelDynamicInfoVOList;

    @FieldDoc(
            description = "是否校验upc (0:不校验,1:校验，注意，只有三级分类该属性才有效)"
    )
    @ApiModelProperty(name = "是否校验upc (0:不校验,1:校验，注意，只有三级分类该属性才有效)")
    private Integer checkUpcStatus;

    @FieldDoc(
            description = "该类目的商品条形码（UPC）是否必填，0-必填，1-选填 (只有三级分类该属性才有效)"
    )
    @ApiModelProperty(name = "该类目的商品条形码（UPC）是否必填，0-必填，1-选填 (只有三级分类该属性才有效)")
    private Integer upcRequired;

    public static ChannelCategoryVO of(ChannelCategoryDTO channelCategory) {
        if (channelCategory == null) {
            return null;
        }

        ChannelCategoryVO categoryVO = new ChannelCategoryVO();

        categoryVO.setCategoryId(channelCategory.getChannelCategoryCode());
        categoryVO.setCategoryName(channelCategory.getChannelCategoryName());
        categoryVO.setIdPath(channelCategory.getChannelCategoryCodePath());
        categoryVO.setNamePath(channelCategory.getChannelCategoryNamePath());
        categoryVO.setChannelDynamicInfoVOList(ChannelDynamicInfoVO.ofOcmsDTOList(channelCategory.getChannelDynamicInfoDTOList()));
        categoryVO.setCheckUpcStatus(channelCategory.getCheckUpcStatus());
        categoryVO.setUpcRequired(channelCategory.getUpcRequired());
        return categoryVO;
    }

    public static ChannelCategoryVO of(ChannelCategoryWithPathDTO channelCategory) {
        if (channelCategory == null) {
            return null;
        }

        ChannelCategoryVO categoryVO = new ChannelCategoryVO();

        categoryVO.setCategoryId(channelCategory.getChannelCategoryCode());
        categoryVO.setCategoryName(channelCategory.getChannelCategoryName());
        categoryVO.setIdPath(channelCategory.getChannelCategoryCodePath());
        categoryVO.setNamePath(channelCategory.getChannelCategoryNamePath());

        return categoryVO;
    }

    public static ChannelCategoryDTO to(ChannelCategoryVO channelCategory) {
        if (channelCategory == null) {
            return null;
        }

        ChannelCategoryDTO categoryDTO = new ChannelCategoryDTO();

        categoryDTO.setChannelCategoryCode(channelCategory.getCategoryId());
        categoryDTO.setChannelCategoryCodePath(channelCategory.getIdPath());
        categoryDTO.setChannelCategoryName(channelCategory.getCategoryName());
        categoryDTO.setChannelCategoryNamePath(channelCategory.getNamePath());

        return categoryDTO;
    }

    public static ChannelCategoryVO of(com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO channelCategoryDTO) {
        ChannelCategoryVO categoryVO = new ChannelCategoryVO();
        categoryVO.setChannelId(String.valueOf(channelCategoryDTO.getChannelId()));
        categoryVO.setCategoryId(channelCategoryDTO.getChannelCategoryCode());
        categoryVO.setCategoryName(channelCategoryDTO.getChannelCategoryName());
        categoryVO.setIdPath(channelCategoryDTO.getChannelCategoryCodePath());
        categoryVO.setNamePath(channelCategoryDTO.getChannelCategoryNamePath());
        categoryVO.setChannelName(ChannelTypeEnum.getDescByCode(channelCategoryDTO.getChannelId()));
        categoryVO.setChannelDynamicInfoVOList(ChannelDynamicInfoVO.ofBizDTOList(channelCategoryDTO.getChannelDynamicInfoDTOList()));
        categoryVO.setCheckUpcStatus(channelCategoryDTO.getCheckUpcStatus());
        categoryVO.setUpcRequired(channelCategoryDTO.getUpcRequired());
        return categoryVO;
    }

    public static com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO toDTO(ChannelCategoryVO categoryVO) {
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO channelCategoryDTO = new com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO();
        channelCategoryDTO.setChannelId(Integer.valueOf(categoryVO.getChannelId()));
        channelCategoryDTO.setChannelCategoryCode(categoryVO.getCategoryId());
        channelCategoryDTO.setChannelCategoryName(categoryVO.getCategoryName());
        channelCategoryDTO.setChannelCategoryCodePath(categoryVO.getIdPath());
        channelCategoryDTO.setChannelCategoryNamePath(categoryVO.getNamePath());
        return channelCategoryDTO;
    }
}
