package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.shangou.saas.tenant.thrift.dto.dict.DictTypeDto;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Data
public class DictVo {

    @FieldDoc(
            description = "字典/枚举类型"
    )
    private String dictType;

    @FieldDoc(
            description = "是否为树形字典"
    )
    private boolean treeDict;

    @FieldDoc(
            description = "枚举项集合"
    )
    private List<DictItemVo> dictItems;

    public static DictVo of(DictTypeDto dto) {
        DictVo dictVo = new DictVo();
        dictVo.setDictType(dto.getDictType());
        dictVo.setTreeDict(dto.isTreeDict());
        dictVo.setDictItems(Fun.map(dto.getDictItems(), DictItemVo::of));
        return dictVo;
    }

}
