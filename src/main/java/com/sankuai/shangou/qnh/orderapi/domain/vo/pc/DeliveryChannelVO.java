package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 配送渠道
 */
@Setter
@Getter
public class DeliveryChannelVO {

    @FieldDoc(
            description = "配送渠道ID"
    )
    @ApiModelProperty(value = "配送渠道ID")
    private String deliveryChannelId;

    @FieldDoc(
            description = "配送渠道名称"
    )
    @ApiModelProperty(value = "配送渠道名称")
    private String deliveryChannelName;

    @FieldDoc(
            description = "配送服务包列表"
    )
    @ApiModelProperty(value = "配送服务包列表")
    List<DeliveryServiceVO> deliveryServiceList;
}
