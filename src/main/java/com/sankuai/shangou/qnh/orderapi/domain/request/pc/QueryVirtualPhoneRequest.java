package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class QueryVirtualPhoneRequest {
    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道订单号")
    @NotNull
    private String channelOrderId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道ID")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "查询电话类型  1-用户 2-骑手", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "查询电话类型  1-用户 2-骑手")
    @NotNull
    private Integer phoneType;


    @FieldDoc(
            description = "门店", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

    @FieldDoc(
            description = "订单类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    @FieldDoc(
            description = "退单号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "退单号")
    private String afterSaleId;
}
