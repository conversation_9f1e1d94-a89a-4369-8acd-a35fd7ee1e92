package com.sankuai.shangou.qnh.orderapi.utils.app;

import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;

import java.util.List;

/**
 * 分页工具
 * <p>
 * Created by qianxg on 2024/6/18
 */
public class PageUtils {


    /**
     * 【内存分页】获取指定分页数据
     *
     * @param allEntries 所有数据列表
     * @param pageNo     页码
     * @param pageSize   页长
     * @return 指定分页数据
     * @param <T> 数据类型
     */
    public static <T> List<T> getPageOrder(List<T> allEntries, int pageNo, int pageSize) {
        if (CollectionUtils.isEmpty(allEntries)) {
            return Lists.newArrayList();
        }
        int totalCount = allEntries.size();
        int offset = (pageNo - 1) * pageSize;
        if (offset < totalCount) {
            return allEntries.subList(offset, Math.min(totalCount, offset + pageSize));
        }
        return Lists.newArrayList();
    }
}
