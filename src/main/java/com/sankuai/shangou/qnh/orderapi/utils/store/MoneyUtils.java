package com.sankuai.shangou.qnh.orderapi.utils.store;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @createTime 2019/11/29
 * @description
 */
public class MoneyUtils {

    /**
     * 分转换成元,保留两位小数
     * @param cent
     */
    public static String centToYuan(Integer cent) {
        if (cent == null) {
            return "";
        }
        return BigDecimal.valueOf(cent).divide(BigDecimal.valueOf(100),2, RoundingMode.CEILING).toString();
    }

    public static long yuanToCent(double yuan) {
        return BigDecimal.valueOf(yuan).multiply(BigDecimal.valueOf(100)).intValue();
    }

    public static double centToYuan(int cent) {
        return BigDecimal.valueOf(cent).divide(BigDecimal.valueOf(100),2, RoundingMode.CEILING).doubleValue();
    }

    /**
     * 分转换成元,保留两位小数
     * @param cent
     */
    public static Double centToYuan(Long cent) {
        if (cent == null) {
            return 0D;
        }
        return BigDecimal.valueOf(cent).divide(BigDecimal.valueOf(100),2, RoundingMode.CEILING).doubleValue();
    }

    public static Double centToYuanByDown(Long cent){
        return BigDecimal.valueOf(cent).divide(BigDecimal.valueOf(100),2, RoundingMode.DOWN).doubleValue();
    }
}
