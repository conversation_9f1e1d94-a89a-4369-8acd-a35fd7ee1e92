package com.sankuai.shangou.qnh.orderapi.utils.pc;

import com.dianping.lion.client.Lion;
import com.google.common.base.Splitter;
import com.sankuai.meituan.common.json.JSONUtil;
import com.sankuai.shangou.qnh.orderapi.constant.pc.ConfigKeyConstant;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-02-23
 * @email <EMAIL>
 */
@Slf4j
public class LionUtils {

    private LionUtils() {

    }

    //是否需要记录商品采购价格
    public static boolean isNeedRecordPurchasePriceTenant(Long tenantId){
        String tenantIdStr = Lion.getConfigRepository("com.sankuai.shangou.empower.orderbiz")
                .get("record.purchase.price.tenant.id.list", StringUtils.EMPTY);
        return Splitter.on(",").splitToList(tenantIdStr).stream().map(s -> Long.parseLong(s)).collect(Collectors.toList()).contains(tenantId);
    }

    public static int getRealTimeOrderQueryLimitOfMonth() {
        return Lion.getConfigRepository("com.sankuai.shangou.empower.ordermng").getIntValue("real_time_order_query_limit_of_month", 6);
    }

    public static int getCommentExportMaxRowsLimit() {
        return Lion.getConfigRepository().getIntValue(ConfigKeyConstant.COMMENT_EXPORT_MAX_ROWS_LIMIT,
                Constants.Comment.COMMENT_EXPORT_MAX_ROWS_LIMIT);
    }

    public static int getCommentExportMaxPageLimit() {
        return Lion.getConfigRepository().getIntValue(ConfigKeyConstant.COMMENT_EXPORT_QUERY_PAGE_SIZE,
                Constants.Comment.COMMENT_EXPORT_QUERY_PAGE_SIZE);
    }

    public static String getOrderOcmsMigrationShopId() {
        return Lion.getConfigRepository().get("order.ocms.migration.shopId");
    }

    public static String getOrderOcmsMigrationTenantId() {
        return Lion.getConfigRepository().get("order.ocms.migration.tenantId");
    }

    public static boolean getOrderOcmsMigrationSwitch() {
        return Lion.getConfigRepository().getBooleanValue("order.ocms.migration.switch", false);
    }

    public static boolean getCommentProcessRecordUrlFallback() {
        return Lion.getConfigRepository().getBooleanValue("comment.process.record.url.fallback", false);
    }

    public static boolean getDeliveryShiftQueryTenantDeliveryConfig() {
        return Lion.getConfigRepository().getBooleanValue("delivery_shift_query_tenant_delivery_config", false);
    }

    public static boolean getDeliveryShiftUpdateTenantDeliveryConfig() {
        return Lion.getConfigRepository().getBooleanValue("delivery_shift_update_tenant_delivery_config", false);
    }

    public static boolean getDeliveryShiftSyncDeliveryRange() {
        return Lion.getConfigRepository().getBooleanValue("delivery_shift_sync_delivery_range", false);
    }

    public static String getOrderOperatorAuthCodes() {
        return Lion.getConfigRepository().get("order.operator.auth.codes");
    }

    public static boolean getOrderTabAppendTmsDeliveryInfoSwitch() {
        return Lion.getConfigRepository().getBooleanValue("order.tab.append.tmsDeliveryInfo.switch", true);
    }

    public static boolean getDeliveryOperateItemRetry() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.retry", true);
    }

    public static boolean getDeliveryOperateItemSelfDelivery() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.selfDelivery", true);
    }

    public static boolean getDeliveryOperateItemExceptionToThird() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.exceptionToThird", true);
    }

    public static boolean getDeliveryOperateItemManualThird() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.manualThird", true);
    }

    public static boolean getDeliveryOperateItemCanCancel() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.canCancel", true);
    }

    public static boolean getDeliveryOperateItemCanceling() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.canceling", true);
    }

    public static boolean getDeliveryOperateItemRetryByMaltfarm() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.retryByMaltfarm", true);
    }

    public static boolean getDeliveryOperateItemRetryByHaiKui() {
        return Lion.getConfigRepository().getBooleanValue("delivery.operate.item.retryByHaiKui", true);
    }

    public static Map<String, String> getOrderStatusOperateMsm() {
        return Lion.getConfigRepository().getMap("order.status.operateMsm");
    }

    public static String getAppidMap() {
        return Lion.getConfigRepository().get("appid.map");
    }

    public static String getChannelAftersaleTimeout() {
        return Lion.getConfigRepository().get("channel.aftersale.timeout");
    }

    public static boolean getSelfFetchCodeToLowercase() {
        return Lion.getConfigRepository().getBooleanValue("self_fetch_code_to_lowercase", false);
    }

    public static String getOrderPrintTimeoutTips() {
        return Lion.getConfigRepository().get("order.print.timeout.tips", "打印超时，若未出票请检查打印机状态后重试");
    }

    public static String getOrderPrintFailTips() {
        return Lion.getConfigRepository().get("order.print.fail.tips", "打印失败，请检查打印机状态后重试");
    }

    /**
     * 订单评价查询状态开关
     * @return 默认返回true开启，表示只查询正常状态评价，false表示根据前端传参查询
     */
    public static boolean getOrderCommentStatusConfig() {
        return Lion.getConfigRepository().getBooleanValue("order.comment.status", Boolean.TRUE);
    }

    /**
     * 订单退款数量显示逻辑配置,false为以老逻辑显示,true以新逻辑显示orderFinance为准
     * @return
     */
    public static boolean getRefundCountDisplayConfig() {
        return Lion.getConfigRepository().getBooleanValue("refund.count.display", Boolean.TRUE);
    }

    /**
     * 返货驳回原因&code
     */
    public static String getReturnGoodsRejectReasonAndCode() {
        return Lion.getConfigRepository().get("return_goods_reject_reason_and_code");
    }

    /**
     * 地址变更费注释 美团
     */
    public static String getAddressChangeFeeNotesWeb() {
        return Lion.getConfigRepository().get("address_change_fee_notes_web","指美团渠道商家自配送订单，用户为下单后修改地址所支付的费用，该笔费用将全部给到商家，并计入【商家运费收入】字段");
    }

    /**
     * 地址变更退费注释 美团
     */
    public static String getAddressChangeFeeNotesApp() {
        return Lion.getConfigRepository().get("address_change_fee_notes_app","指美团渠道商家自配送订单，用户为下单后修改地址所支付的费用，该笔费用将全部给到商家");
    }

    /**
     * 地址变更费注释web 饿了么
     */
    public static String getAddressChangeFeeNotesWebEleM() {
        return Lion.getConfigRepository().get("address_change_fee_notes_web_elm","指饿了么渠道商家自配送订单，用户为下单后修改地址所支付的费用，该笔费用将全部给到商家，并计入【商家运费收入】字段");
    }

    /**
     * 地址变更退费注释app 饿了么
     */
    public static String getAddressChangeFeeNotesAppEleM() {
        return Lion.getConfigRepository().get("address_change_fee_notes_app_elm","指饿了么渠道商家自配送订单，用户为下单后修改地址所支付的费用，该笔费用将全部给到商家");
    }

    /**
     * 订单请求参数长度限制配置
     */
    public static Integer getOrderParamsLengthLimitConfig() {
        try {
            return Lion.getConfigRepository().getIntValue("order_params_length_limit_config", 100);
        }catch (Exception e){
            log.error("getOrderParamsLengthLimitConfig error", e);
            return 100;
        }
    }

    /**
     * 校验租户id是否能根据商品【SKU、条形码、商家ERP编码】查询历史数据
     *
     * @param tenantId
     * @return
     */
    public static boolean checkItemHistoricalQueryTenantId(long tenantId) {
        List<Long> tenantIdList = Lion.getConfigRepository().getList("orderItems.historicalQuery.tenantIds", Long.class,
                Collections.emptyList());
        // 为空，默认不能进行商品【SKU、条形码、商家ERP编码】查询历史数据
        if (CollectionUtils.isEmpty(tenantIdList)) {
            return false;
        }
        // 包含0时，表示所有租户都可以进行商品【SKU、条形码、商家ERP编码】查询历史数据
        if (tenantIdList.contains(0L)) {
            return true;
        }
        return tenantIdList.contains(tenantId);
    }
}
