package com.sankuai.shangou.qnh.orderapi.utils.app;

import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.constant.app.CommonConstants;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException;

import java.util.function.Function;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/16 19:12
 * @Description: 处理返回结果
 */
public class ResponseHandler {

    /**
     * 检查响应结果
     *
     * @param response
     * @param codeSupplier
     * @param msgSupplier
     */
    public static <T> void checkResponseAndStatus(T response, Function<T, Integer> codeSupplier,
                                                  Function<T, String> msgSupplier, ResultCodeEnum resultCodeEnum) {
        if (response == null) {
            throw new CommonRuntimeException(CommonConstants.ErrorMsg.UNKNOWN_ERROR, ResultCodeEnum.FAIL);
        }

        if (codeSupplier.apply(response) != ResultCodeEnum.SUCCESS.getCode()) {
            throw new CommonRuntimeException(msgSupplier.apply(response), resultCodeEnum);
        }

    }

    /**
     * 检查响应结果
     * @param response
     * @param codeSupplier
     * @param msgSupplier
     */
    public  static <T> void  checkResponseAndStatus(T response, Function<T,Integer> codeSupplier, Function<T,String> msgSupplier) {
        if (response == null) {
            throw new BizException(com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.FAIL.getValue(), CommonConstants.ErrorMsg.UNKNOWN_ERROR);
        }

        if (codeSupplier.apply(response) != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(codeSupplier.apply(response), msgSupplier.apply(response));
        }

    }

}
