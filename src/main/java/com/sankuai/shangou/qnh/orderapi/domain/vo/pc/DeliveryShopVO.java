package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 配送渠道门店
 */
@Setter
@Getter
public class DeliveryShopVO {

    @FieldDoc(
            description = "配送渠道ID"
    )
    @ApiModelProperty(value = "配送渠道ID")
    private String deliveryChannelId;

    @FieldDoc(
            description = "配送渠道名称"
    )
    @ApiModelProperty(value = "配送渠道名称")
    private String deliveryChannelName;

    @FieldDoc(
            description = "配送门店ID"
    )
    @ApiModelProperty(value = "配送门店ID")
    private String deliveryShopId;

    @FieldDoc(
            description = "配送门店名称"
    )
    @ApiModelProperty(value = "配送门店名称")
    private String deliveryShopName;
}
