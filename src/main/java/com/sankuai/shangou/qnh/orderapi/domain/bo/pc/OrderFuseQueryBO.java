package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderFuseListReq;
import com.meituan.shangou.saas.order.management.client.export.dto.request.FuseOrderListExportTaskCreateRequest;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.enums.OrderPosStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderUserType;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.OrderFuseQueryRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.OrderPCFuseQueryRequest;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelOrderNewStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.PosStatusEnum;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.DateTimeUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/2 14:30
 * @Description:
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@EqualsAndHashCode
public class OrderFuseQueryBO {

    private Long tenantId;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人地址
     */
    private String receiverAddress;

    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 收货人电话后四位
     */
    private String receiverPrivacyPhoneLastFourDigits;

    /**
     * 门店名称
     */
    private String poiName;

    /**
     * 门店编码
     */
    private String poiId;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 开始时间
     */
    private String createStartTime;


    /**
     * 结束时间
     */
    private String createEndTime;

    /**
     * 预计送达开始时间
     */
    private String arrivalStartTime;

    /**
     * 预计送达结束时间
     */
    private String arrivalEndTime;

    /**
     * 订单完成时间开始点，毫秒
     */
    private Long completeTimeStart;

    /**
     * 订单完成时间结束点，毫秒
     */
    private Long completeTimeEnd;

    /**
     * 核销时间开始点，毫秒
     */
    private Long posTimeStart;

    /**
     * 核销时间结束点，毫秒
     */
    private Long posTimeEnd;

    /**
     * 状态
     */
    private List<Integer> status;


    /**
     * 退款状态
     */
    private List<String> refundTypes;


    /**
     * 渠道列表
     */
    private List<Integer> channelIds;


    private List<Long> poiIds;


    private String skuName;

    private int orderType;

    private int hasMemberCard;

    private String memberCard;

    /**
     * 流水号
     */
    private Long orderSerialNumber;

    /**
     * 订单序号
     */
    private String orderSerialNumberStr;

    /**
     * 摊位ID
     */
    private List<Long> boothIds;

    /**
     * 问题订单(1进货价为空)
     */
    private Integer orderProblemType;


    /**
     * 页码
     */
    private int page;


    /**
     * 每页显示记录数
     */
    private int pageSize;

    /**
     * 是否要针对当前用户隐藏订单收货地址
     */
    private Boolean hideOrderRecvAddress;

    /**
     * 仓库id
     */
    private List<Long> warehouseIds;

    /**
     * 核销状态列表
     */
    public List<Integer> writeOffStatus;

    /**
     * 订单标识列表
     */
    public List<Integer> orderMarks;

    /**
     * 自配送类型列表
     */
    private List<Integer> deliveryChannelIds;

    /**
     * 平台配送类型列表
     */
    private List<Integer> originalDistributeTypes;

    /**
     * 融合订单状态
     */
    public List<Integer> fuseOrderStatus;

    /**
     * 配送方式
     */
    private Integer distributeMethod;

    /**
     * 导出字段
     */
    public List<String> exportFieldsList;

    /**
     * 订单号 支持后模糊匹配
     */
    public String viewOrderIdFuzzy;

    /**
     * 模糊查询关键字
     */
    public String smartQuery;


    /**
     * 渠道订单ID
     */
    private List<String> viewOrderIdList;

    /**
     * 订单标识字符串列表
     */
    public List<String> orderMarkStrList;

    public OrderFuseQueryBO(OrderFuseQueryRequest request) {
        if(StringUtils.isNotEmpty(request.getOrderId()) && request.getOrderId().length() > Constants.ORDER_FUZZY_SEARCH_SPLIT_LENGTH){
            orderId = request.getOrderId();
            page = request.getPage();
            pageSize = request.getPageSize();
            if(CollectionUtils.isNotEmpty(request.getExportFields())){
                exportFieldsList = request.getExportFields();
            }
            if(CollectionUtils.isNotEmpty(request.getPoiIdList())){
                poiIds = request.getPoiIdList();
            }
            if(CollectionUtils.isNotEmpty(request.getWarehouseIdList())){
                warehouseIds = request.getWarehouseIdList();
            }
        }else{
            if(StringUtils.isNotEmpty(request.getOrderId())){
                viewOrderIdFuzzy = request.getOrderId();
            }
            receiverName = request.getReceiverName();
            receiverPrivacyPhoneLastFourDigits = request.getReceiverPhone();
            receiverAddress = request.getReceiverAddress();
            if (StringUtils.isNotBlank(request.getCreateStartTime()) && StringUtils.isNotBlank(request.getCreateEndTime())) {
                createStartTime = request.getCreateStartTime();
                createEndTime = request.getCreateEndTime();
            }

            //预计送达时间、完成时间、核销时间查询时，转换订单下单时间逻辑：
            //1.下单开始时间往前推30天，如果往前推后，超过ES线上索引指定时间范围，则置为上限时间
            //2.下单结束时间直接赋值
            if (StringUtils.isNotBlank(request.getArrivalStartTime()) && StringUtils.isNotBlank(request.getArrivalEndTime())) {
                arrivalStartTime = request.getArrivalStartTime();
                arrivalEndTime = request.getArrivalEndTime();
                createStartTime = String.valueOf(getAdjustedTimestamp(Long.parseLong(arrivalStartTime), Long.parseLong(arrivalEndTime)));
                createEndTime = arrivalEndTime;
            }

            this.completeTimeStart = request.getCompleteTimeStart();
            this.completeTimeEnd = request.getCompleteTimeEnd();
            if (this.completeTimeStart != null) {
                createStartTime = String.valueOf(getAdjustedTimestamp(completeTimeStart,completeTimeEnd));
            }
            if (this.completeTimeEnd != null) {
                createEndTime = String.valueOf(completeTimeEnd);
            }

            this.posTimeStart = request.getPosTimeStart();
            this.posTimeEnd = request.getPosTimeEnd();
            if (this.posTimeStart != null) {
                createStartTime = String.valueOf(getAdjustedTimestamp(posTimeStart,posTimeEnd));
            }
            if (this.posTimeEnd != null) {
                createEndTime = String.valueOf(posTimeEnd);
            }

            this.viewOrderIdList = request.getViewOrderIdList();

            channelIds = CollectionUtils.isNotEmpty(request.getChannelIds()) ? ConverterUtils.convertList(request.getChannelIds(), Integer::valueOf)
                    : MccDynamicConfigUtil.ignoreOrderBizTypeConditions();

            status =  request.getStatus();
            refundTypes = request.getRefundTypes();
            skuName = request.getSkuName();
            orderType = ConverterUtils.nonNullConvert(request.getOrderType(), Integer::valueOf, 0);
            hasMemberCard = ConverterUtils.nonNullConvert(request.getHasMemberCard(), Integer::valueOf, 0);
            memberCard = request.getMemberCard();
            orderProblemType = request.getOrderProblemType();
            boothIds = request.getBoothIds();
            if (Strings.isNotEmpty(request.getOrderSerialNumber())) {
                if (StringUtils.isNumeric(request.getOrderSerialNumber())) {
                    orderSerialNumber = ConverterUtils.nonNullConvert(request.getOrderSerialNumber(), Long::valueOf,
                            -1L);
                } else {
                    orderSerialNumber = -1L;
                }
                orderSerialNumberStr = request.getOrderSerialNumber();
            }
            page = request.getPage();
            pageSize = request.getPageSize();
            if(CollectionUtils.isNotEmpty(request.getWriteOffStatus())){
                writeOffStatus = request.getWriteOffStatus().stream().filter(item -> !item.equals("-1")).map(Integer::parseInt).collect(Collectors.toList());
            }
            orderMarks = request.getOrderMarks();
            deliveryChannelIds = request.getDeliveryChannelIds();
            originalDistributeTypes =  request.getOriginalDistributeTypes();
            distributeMethod = request.getDistributeMethod();

            if(CollectionUtils.isNotEmpty(request.getFuseOrderStatus())){
                fuseOrderStatus = request.getFuseOrderStatus().stream().filter(item -> !item.equals("0")).map(Integer::parseInt).collect(Collectors.toList());
            }

            if(CollectionUtils.isNotEmpty(request.getPoiIdList())){
                poiIds = request.getPoiIdList();
            }
            if(CollectionUtils.isNotEmpty(request.getWarehouseIdList())){
                warehouseIds = request.getWarehouseIdList();
            }
            if(CollectionUtils.isNotEmpty(request.getExportFields())){
                exportFieldsList = request.getExportFields();
            }
            if(CollectionUtils.isNotEmpty(request.getOrderMarkStrList())){
                orderMarkStrList = request.getOrderMarkStrList();
            }
        }

    }


    public OrderFuseQueryBO(OrderPCFuseQueryRequest request) {
        smartQuery = request.getSmartQuery();
        //模糊搜索大于等于4位，忽略其他查询条件
        if (StringUtils.isNotEmpty(smartQuery) && smartQuery.length() >= Constants.ORDER_FUZZY_SEARCH_SPLIT_LENGTH) {
            //大于9位，则位订单号搜索，赋值orderId，后续有配送异常数量查tms需要使用
            if (smartQuery.length() > Constants.PC_ORDER_FUZZY_SEARCH_SPLIT_LENGTH) {
                orderId = smartQuery;
            } else {
                createStartTime = request.getCreateStartTime() == null ? String.valueOf(DateTimeUtils.getMonthAgoFirstDay(3)) : request.getCreateStartTime();
                createEndTime = request.getCreateEndTime() == null ? String.valueOf(Instant.now().toEpochMilli()) : request.getCreateEndTime();
                //预计送达时间、完成时间、核销时间查询时，转换订单下单时间逻辑：
                //1.下单开始时间往前推30天，如果往前推后，超过ES线上索引指定时间范围，则置为上限时间
                //2.下单结束时间直接赋值
                if (StringUtils.isNotBlank(request.getArrivalStartTime()) && StringUtils.isNotBlank(request.getArrivalEndTime())) {
                    arrivalStartTime = request.getArrivalStartTime();
                    arrivalEndTime = request.getArrivalEndTime();
                    createStartTime = String.valueOf(getAdjustedTimestamp(Long.parseLong(arrivalStartTime), Long.parseLong(arrivalEndTime)));
                    createEndTime = arrivalEndTime;
                }

                this.completeTimeStart = request.getCompleteTimeStart();
                this.completeTimeEnd = request.getCompleteTimeEnd();
                if (this.completeTimeStart != null) {
                    createStartTime = String.valueOf(getAdjustedTimestamp(completeTimeStart, completeTimeEnd));
                }
                if (this.completeTimeEnd != null) {
                    createEndTime = String.valueOf(completeTimeEnd);
                }

                this.posTimeStart = request.getPosTimeStart();
                this.posTimeEnd = request.getPosTimeEnd();
                if (this.posTimeStart != null) {
                    createStartTime = String.valueOf(getAdjustedTimestamp(posTimeStart, posTimeEnd));
                }
                if (this.posTimeEnd != null) {
                    createEndTime = String.valueOf(posTimeEnd);
                }
            }

            page = request.getPage();
            pageSize = request.getPageSize();
            if (CollectionUtils.isNotEmpty(request.getExportFields())) {
                exportFieldsList = request.getExportFields();
            }
            if (CollectionUtils.isNotEmpty(request.getPoiIdList())) {
                poiIds = request.getPoiIdList();
            }
            if (CollectionUtils.isNotEmpty(request.getWarehouseIdList())) {
                warehouseIds = request.getWarehouseIdList();
            }

        } else {

            receiverName = request.getReceiverName();
            receiverAddress = request.getReceiverAddress();

            if (StringUtils.isNotBlank(request.getCreateStartTime()) && StringUtils.isNotBlank(request.getCreateEndTime())) {
                createStartTime = request.getCreateStartTime();
                createEndTime = request.getCreateEndTime();
            }
            //预计送达时间、完成时间、核销时间查询时，转换订单下单时间逻辑：
            //1.下单开始时间往前推30天，如果往前推后，超过ES线上索引指定时间范围，则置为上限时间
            //2.下单结束时间直接赋值
            if (StringUtils.isNotBlank(request.getArrivalStartTime()) && StringUtils.isNotBlank(request.getArrivalEndTime())) {
                arrivalStartTime = request.getArrivalStartTime();
                arrivalEndTime = request.getArrivalEndTime();
                createStartTime = String.valueOf(getAdjustedTimestamp(Long.parseLong(arrivalStartTime), Long.parseLong(arrivalEndTime)));
                createEndTime = arrivalEndTime;
            }

            this.completeTimeStart = request.getCompleteTimeStart();
            this.completeTimeEnd = request.getCompleteTimeEnd();
            if (this.completeTimeStart != null) {
                createStartTime = String.valueOf(getAdjustedTimestamp(completeTimeStart, completeTimeEnd));
            }
            if (this.completeTimeEnd != null) {
                createEndTime = String.valueOf(completeTimeEnd);
            }

            this.posTimeStart = request.getPosTimeStart();
            this.posTimeEnd = request.getPosTimeEnd();
            if (this.posTimeStart != null) {
                createStartTime = String.valueOf(getAdjustedTimestamp(posTimeStart, posTimeEnd));
            }
            if (this.posTimeEnd != null) {
                createEndTime = String.valueOf(posTimeEnd);
            }


            channelIds = CollectionUtils.isNotEmpty(request.getChannelIds())
                    ? ConverterUtils.convertList(request.getChannelIds(), Integer::valueOf)
                    : MccDynamicConfigUtil.ignoreOrderBizTypeConditions(); 

            status = request.getStatus();
            refundTypes = request.getRefundTypes();
            skuName = request.getSkuName();
            orderType = ConverterUtils.nonNullConvert(request.getOrderType(), Integer::valueOf, 0);
            hasMemberCard = ConverterUtils.nonNullConvert(request.getHasMemberCard(), Integer::valueOf, 0);
            memberCard = request.getMemberCard();
            orderProblemType = request.getOrderProblemType();
            boothIds = request.getBoothIds();
            page = request.getPage();
            pageSize = request.getPageSize();
            if (CollectionUtils.isNotEmpty(request.getWriteOffStatus())) {
                writeOffStatus = request.getWriteOffStatus().stream().filter(item -> !item.equals("-1")).map(Integer::parseInt).collect(Collectors.toList());
            }
            orderMarks = request.getOrderMarks();
            deliveryChannelIds = request.getDeliveryChannelIds();
            originalDistributeTypes = request.getOriginalDistributeTypes();
            distributeMethod = request.getDistributeMethod();

            if (CollectionUtils.isNotEmpty(request.getFuseOrderStatus())) {
                fuseOrderStatus = request.getFuseOrderStatus().stream().filter(item -> !item.equals("0")).map(Integer::parseInt).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(request.getPoiIdList())) {
                poiIds = request.getPoiIdList();
            }
            if (CollectionUtils.isNotEmpty(request.getWarehouseIdList())) {
                warehouseIds = request.getWarehouseIdList();
            }
            if (CollectionUtils.isNotEmpty(request.getExportFields())) {
                exportFieldsList = request.getExportFields();
            }
            if(CollectionUtils.isNotEmpty(request.getOrderMarkStrList())){
                orderMarkStrList = request.getOrderMarkStrList();
            }
        }
    }

    private long getAdjustedTimestamp(long startTime,long endTime) {
        int limitMonth = LionUtils.getRealTimeOrderQueryLimitOfMonth();
        long thirtyDaysAgo = startTime - 30L * 24 * 60 * 60 * 1000;
        long limitMonthAgo = DateTimeUtils.getMonthAgoFirstDay(limitMonth);
        return endTime > limitMonthAgo ? Math.max(thirtyDaysAgo, limitMonthAgo) : thirtyDaysAgo;
    }

    public OcmsOrderFuseListReq toOcmsOrderAllFuseListReq() {
        OcmsOrderFuseListReq req = new OcmsOrderFuseListReq();
        req.setTenantId(tenantId);
        if(StringUtils.isNotEmpty(smartQuery)){
            req.setSmartQuery(smartQuery);
        }
        if(StringUtils.isNotEmpty(receiverName)){
            req.setReceiverName(receiverName);
        }
        if(StringUtils.isNotEmpty(receiverPhone)){
            req.setReceiverPhone(receiverPhone);
        }
        req.setReceiverPhoneLastFourDigits(receiverPrivacyPhoneLastFourDigits);
        if(StringUtils.isNotEmpty(receiverAddress)){
            req.setReceiverAddress(receiverAddress);
        }
        req.setShopId(ConverterUtils.nonNullConvert(poiId, Long::valueOf, 0L));
        if(CollectionUtils.isNotEmpty(poiIds)){
            req.setShopIdList(poiIds);
        }
        if(CollectionUtils.isNotEmpty(warehouseIds)){
            req.setWarehouseIdList(warehouseIds);
        }
        req.setShopName(poiName);
        req.setChannelOrderId(orderId);
        req.setViewOrderIdList(viewOrderIdList);
        req.setViewOrderIdFuzzy(viewOrderIdFuzzy);
        req.setBeginCreateTime(ConverterUtils.nonNullConvert(createStartTime, Long::valueOf, 0L));
        req.setEndCreateTime(ConverterUtils.nonNullConvert(createEndTime, Long::valueOf, 0L));

        req.setArrivalStartTime(ConverterUtils.nonNullConvert(arrivalStartTime, Long::valueOf, 0L));
        req.setArrivalEndTime(ConverterUtils.nonNullConvert(arrivalEndTime, Long::valueOf, 0L));

        req.setCompleteTimeStart(completeTimeStart);
        req.setCompleteTimeEnd(completeTimeEnd);
        req.setPosTimeStart(posTimeStart);
        req.setPosTimeEnd(posTimeEnd);

        req.setChannelIdList(channelIds);

        req.setRefundTagIdList(ConverterUtils.convertList(refundTypes, Integer::valueOf));
        if(StringUtils.isNotEmpty(skuName)){
            req.setGoodsNameFuzzy(skuName);
        }
        if(StringUtils.isNotEmpty(memberCard)){
            req.setMemberCardNumFuzzy(memberCard);
        }
        if (hasMemberCard == 0) {
            req.setOrderUserTypeList(Arrays.asList(OrderUserType.COMMON.getValue(), OrderUserType.MEMBER.getValue()));
        } else {
            req.setOrderUserTypeList(Arrays.asList(hasMemberCard == 1 ? OrderUserType.MEMBER.getValue() : OrderUserType.COMMON.getValue()));
        }

        if (orderType == 0) {
            req.setOrderBookingTypeList(Arrays.asList(0, 1));
        } else {
            req.setOrderBookingTypeList(Arrays.asList(orderType == 1 ? 0 : 1));
        }
        req.setPage(page);
        req.setPageSize(pageSize);

        if(CollectionUtils.isNotEmpty(fuseOrderStatus)){
            List<Integer> orderStatusList = new ArrayList<>();
                for (Integer status: fuseOrderStatus){
                    // 拣货中和
                    if(!ChannelOrderNewStatusEnum.isNotNeedQueryOldStatus(status)){
                        orderStatusList.add(status);
                    }
                }
                req.setFuseOrderStatusList(fuseOrderStatus);
                req.setChannelOrderStatusList(orderStatusList);
        }

        req.setDistributeMethod(distributeMethod);
        req.setOriginalDistributeTypeList(originalDistributeTypes);
        req.setDeliveryChannelIdList(deliveryChannelIds);
        req.setOrderMarkList(orderMarks);
        if (CollectionUtils.isNotEmpty(writeOffStatus)){
            if (writeOffStatus.contains(OrderPosStatusEnum.NEW.getValue())){
                writeOffStatus.add(OrderPosStatusEnum.DOWN.getValue());
            }
            req.setWriteOffStatusList(writeOffStatus);
            for (Integer status: writeOffStatus){
                if(Objects.equals(status, PosStatusEnum.ALL.getCode())){
                    req.setWriteOffStatusList(new ArrayList<>());
                    break;
                }
            }
        }
        if(Objects.nonNull(orderSerialNumber) && orderSerialNumber > 0){
            req.setOrderSerialNumber(orderSerialNumber);
        }
        if (StringUtils.isNotBlank(orderSerialNumberStr)) {
            req.setOrderSerialNumberStr(orderSerialNumberStr);
        }
        if(CollectionUtils.isNotEmpty(orderMarkStrList)){
            req.setOrderMarkStrList(orderMarkStrList);
        }
        return req;
    }



    public FuseOrderListExportTaskCreateRequest toExportFuseOrderListRequest() {
        FuseOrderListExportTaskCreateRequest exportReq = new FuseOrderListExportTaskCreateRequest();
        exportReq.setOperatorId(ContextHolder.currentUid());
        exportReq.setExportFieldsList(exportFieldsList);

        OcmsOrderFuseListReq req = new OcmsOrderFuseListReq();
        req.setTenantId(ContextHolder.currentUserTenantId());
        if(StringUtils.isNotEmpty(receiverName)){
            req.setReceiverName(receiverName);
        }
        if(StringUtils.isNotEmpty(receiverPhone)){
            req.setReceiverPhone(receiverPhone);
        }
        req.setReceiverPhoneLastFourDigits(receiverPrivacyPhoneLastFourDigits);
        if(StringUtils.isNotEmpty(receiverAddress)){
            req.setReceiverAddress(receiverAddress);
        }
        req.setShopId(ConverterUtils.nonNullConvert(poiId, Long::valueOf, 0L));
        if(CollectionUtils.isNotEmpty(poiIds)){
            req.setShopIdList(poiIds);
        }
        if(CollectionUtils.isNotEmpty(warehouseIds)){
            req.setWarehouseIdList(warehouseIds);
        }
        req.setShopName(poiName);
        req.setChannelOrderId(orderId);
        req.setViewOrderIdFuzzy(viewOrderIdFuzzy);
        req.setBeginCreateTime(ConverterUtils.nonNullConvert(createStartTime, Long::valueOf, 0L));
        req.setEndCreateTime(ConverterUtils.nonNullConvert(createEndTime, Long::valueOf, 0L));

        req.setArrivalStartTime(ConverterUtils.nonNullConvert(arrivalStartTime, Long::valueOf, 0L));
        req.setArrivalEndTime(ConverterUtils.nonNullConvert(arrivalEndTime, Long::valueOf, 0L));

        req.setCompleteTimeStart(completeTimeStart);
        req.setCompleteTimeEnd(completeTimeEnd);

        req.setPosTimeStart(posTimeStart);
        req.setPosTimeEnd(posTimeEnd);

        req.setChannelIdList(channelIds);

        req.setRefundTagIdList(ConverterUtils.convertList(refundTypes, Integer::valueOf));
        if(StringUtils.isNotEmpty(skuName)){
            req.setGoodsNameFuzzy(skuName);
        }
        if(StringUtils.isNotEmpty(memberCard)){
            req.setMemberCardNumFuzzy(memberCard);
        }
        if (hasMemberCard == 0) {
            req.setOrderUserTypeList(Arrays.asList(OrderUserType.COMMON.getValue(), OrderUserType.MEMBER.getValue()));
        } else {
            req.setOrderUserTypeList(Arrays.asList(hasMemberCard == 1 ? OrderUserType.MEMBER.getValue() : OrderUserType.COMMON.getValue()));
        }

        if (orderType == 0) {
            req.setOrderBookingTypeList(Arrays.asList(0, 1));
        } else {
            req.setOrderBookingTypeList(Arrays.asList(orderType == 1 ? 0 : 1));
        }
        req.setPage(page);
        req.setPageSize(pageSize);

        if(CollectionUtils.isNotEmpty(fuseOrderStatus)){
            List<Integer> orderStatusList = new ArrayList<>();
            for (Integer status: fuseOrderStatus){
                // 拣货中和
                if(!ChannelOrderNewStatusEnum.isNotNeedQueryOldStatus(status)){
                    orderStatusList.add(status);
                }
            }
            req.setFuseOrderStatusList(fuseOrderStatus);
            req.setChannelOrderStatusList(orderStatusList);
        }

        req.setDistributeMethod(distributeMethod);
        req.setOriginalDistributeTypeList(originalDistributeTypes);
        req.setDeliveryChannelIdList(deliveryChannelIds);
        req.setOrderMarkList(orderMarks);
        if (CollectionUtils.isNotEmpty(writeOffStatus)){
            if (writeOffStatus.contains(OrderPosStatusEnum.NEW.getValue())){
                writeOffStatus.add(OrderPosStatusEnum.DOWN.getValue());
            }
            req.setWriteOffStatusList(writeOffStatus);
            for (Integer status: writeOffStatus){
                if(Objects.equals(status, PosStatusEnum.ALL.getCode())){
                    req.setWriteOffStatusList(new ArrayList<>());
                    break;
                }
            }
        }
        if(Objects.nonNull(orderSerialNumber) && orderSerialNumber > 0){
            req.setOrderSerialNumber(orderSerialNumber);
        }
        if (StringUtils.isNotBlank(orderSerialNumberStr)) {
            req.setOrderSerialNumberStr(orderSerialNumberStr);
        }
        if(CollectionUtils.isNotEmpty(orderMarkStrList)){
            req.setOrderMarkStrList(orderMarkStrList);
        }
        exportReq.setOcmsOrderFuseListReq(req);
        return exportReq;
    }
}
