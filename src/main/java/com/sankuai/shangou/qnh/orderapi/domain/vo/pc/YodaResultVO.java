package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;

@TypeDoc(
        description = "yoda info接口响应内容"
)
@Data
@AllArgsConstructor
public class YodaResultVO {

    @FieldDoc(
            description = "yoda响应requestCode", requiredness = Requiredness.REQUIRED
    )
    private String requestCode;
}
