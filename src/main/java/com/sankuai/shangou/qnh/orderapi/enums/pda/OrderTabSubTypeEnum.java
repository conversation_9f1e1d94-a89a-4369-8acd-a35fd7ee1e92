package com.sankuai.shangou.qnh.orderapi.enums.pda;

import com.meituan.shangou.saas.order.management.client.enums.WaitToAuditRefundGoodsOrderSubTypeEnum;
import com.meituan.shangou.saas.order.management.client.enums.WaitToDeliveryOrderSubTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/7/10
 **/
public enum OrderTabSubTypeEnum {
    ALL(0, "全部", OrderTabTypeEnum.UNKOWN),
    WAIT_TO_SEND_DELIVERY(WaitToDeliveryOrderSubTypeEnum.DELIVERING.getCode(), "待发单", OrderTabTypeEnum.WAIT_TO_DELIVERY),
    WAIT_TO_RIDER_ACCEPT(WaitToDeliveryOrderSubTypeEnum.WAIT_TO_RIDER_ACCEPT.getCode(), "待接单", OrderTabTypeEnum.WAIT_TO_DELIVERY),
    WAIT_TO_ARRIVE_SHOP(WaitToDeliveryOrderSubTypeEnum.WAIT_TO_ARRIVE_SHOP.getCode(), "待到店", OrderTabTypeEnum.WAIT_TO_DELIVERY),
    WAIT_TO_TAKE_GOODS(WaitToDeliveryOrderSubTypeEnum.WAIT_TO_ARRIVE_SHOP.getCode(), "待取货", OrderTabTypeEnum.WAIT_TO_DELIVERY),
    DELIVERING(WaitToDeliveryOrderSubTypeEnum.DELIVERING.getCode(), "配送中", OrderTabTypeEnum.WAIT_TO_DELIVERY),

    REFUND(WaitToAuditRefundGoodsOrderSubTypeEnum.ONLY_REFUND.getCode(), "仅退款", OrderTabTypeEnum.AFTER_SALE),
    REFUND_GOOD(WaitToAuditRefundGoodsOrderSubTypeEnum.RETURN_AND_REFUND.getCode(), "仅退货退款", OrderTabTypeEnum.AFTER_SALE),
    REJECT_BY_CUSTOMER(WaitToAuditRefundGoodsOrderSubTypeEnum.REJECT_BY_CUSTOMER.getCode(), "用户拒收", OrderTabTypeEnum.AFTER_SALE),
    APPEAL(WaitToAuditRefundGoodsOrderSubTypeEnum.APPEAL.getCode(), "申诉", OrderTabTypeEnum.AFTER_SALE),

    NO_RIDER_ACCEPT(DeliveryExceptionSubTypeEnum.NO_RIDER_ACCEPT.getCode(), "未接单", OrderTabTypeEnum.DELIVERY_EXCEPTION),
    NO_ARRIVAL_STORE(DeliveryExceptionSubTypeEnum.NO_ARRIVAL_STORE.getCode(), "未到店", OrderTabTypeEnum.DELIVERY_EXCEPTION),
    NO_RIDER_TAKE_GOODS(DeliveryExceptionSubTypeEnum.NO_RIDER_TAKE_GOODS.getCode(), "未取货", OrderTabTypeEnum.DELIVERY_EXCEPTION),
    DELIVERY_TIMEOUT(DeliveryExceptionSubTypeEnum.DELIVERY_TIMEOUT.getCode(), "配送超时", OrderTabTypeEnum.DELIVERY_EXCEPTION),
    SYSTEM_EXCEPTION(DeliveryExceptionSubTypeEnum.SYSTEM_EXCEPTION.getCode(), "系统异常", OrderTabTypeEnum.DELIVERY_EXCEPTION),
    REPORT_EXCEPTION(DeliveryExceptionSubTypeEnum.REPORT_EXCEPTION.getCode(), "上报异常", OrderTabTypeEnum.DELIVERY_EXCEPTION),
    TAKE_EXCEPTION(DeliveryExceptionSubTypeEnum.TAKE_EXCEPTION.getCode(), "取货异常", OrderTabTypeEnum.DELIVERY_EXCEPTION),



    ;

    private int code;
    private String desc;
    private OrderTabTypeEnum parent;

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public OrderTabTypeEnum getParent(){
        return this.parent;
    }

    public static OrderTabSubTypeEnum enumOf(int code) {
        for (OrderTabSubTypeEnum status : OrderTabSubTypeEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    OrderTabSubTypeEnum(int code, String desc, OrderTabTypeEnum parent) {
        this.code = code;
        this.desc = desc;
        this.parent = parent;
    }
}
