package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.dto.request.OrderSearchRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderAllListReq;
import com.meituan.shangou.saas.order.management.client.export.dto.request.ExportChannelOrderDetailRequest;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderAllListReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderUserType;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.converter.pc.ChannelOrderConverterV2;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.ChannelOrderDownloadRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.ChannelOrderQueryRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ChannelOrderConverter;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.DeliveryChannelUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/2 14:30
 * @Description:
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@EqualsAndHashCode
public class ChannelOrderQueryBO {

    private Long tenantId;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人地址
     */
    private String receiverAddress;

    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 收货人电话后四位
     */
    private String receiverPrivacyPhoneLastFourDigits;

    /**
     * 门店名称
     */
    private String poiName;

    /**
     * 门店编码
     */
    private String poiId;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 开始时间
     */
    private Date createStartTime;


    /**
     * 结束时间
     */
    private Date createEndTime;

    /**
     * 预计送达开始时间
     */
    private Date arrivalStartTime;

    /**
     * 预计送达结束时间
     */
    private Date arrivalEndTime;


    /**
     * 状态
     */
    private String status;


    /**
     * 退款状态
     */
    private List<String> refundTypes;


    /**
     * 渠道列表
     */
    private List<Integer> channelIds;


    private List<Long> poiIds;


    private String skuName;

    private int orderType;

    private int hasMemberCard;

    private String memberCard;

    /**
     * 流水号
     */
    private Long orderSerialNumber;

    /**
     * 摊位ID
     */
    private List<Long> boothIds;

    /**
     * 问题订单(1进货价为空)
     */
    private Integer orderProblemType;


    /**
     * 页码
     */
    private int page;


    /**
     * 每页显示记录数
     */
    private int pageSize;

    /**
     * 是否要针对当前用户隐藏订单收货地址
     */
    private Boolean hideOrderRecvAddress;

    /**
     * 仓库id
     */
    private List<Long> warehouseIds;

    /**
     * 订单用户类型， 后续需弃用 hasMemberCard.
     */
    private Integer orderUserType;

    /**
     * 配送方式筛选 2 自营配送  -4/4000+ 青云配送
     */
    private List<Integer> deliveryChannelTypeList;


    /**
     * 完成配送，筛选起始时间
     */
    private Date finishDeliveryStartTime;

    /**
     * 完成配送，筛选结束时间
     */
    private Date finishDeliveryEndTime;


    /**
     * 基础skuId
     */
    private String materialSkuId;

    /**
     * 骑手账户id
     */
    private String riderAccountName;

    /**
     * 客户端来源
     */
    private String clientType;

    /**
     * 订单标识列表
     */
    public List<Integer> orderMarks;



    public ChannelOrderQueryBO(ChannelOrderQueryRequest request) {

        receiverName = request.getReceiverName();
        receiverPrivacyPhoneLastFourDigits = request.getReceiverPhone();
        poiId = request.getPoiId();
        poiIds = ConverterUtils.nonNullConvert(request.getPoiId(), p -> Arrays.asList(Long.valueOf(p)));
        poiName = request.getPoiName();
        orderId = request.getOrderId();
        receiverAddress = request.getReceiverAddress();
        // 查询的开始时间精确到秒
        createStartTime = Optional.ofNullable(request.getCreateStartTime())
                .map(val -> val.length() == Constants.TIME_STRING_SECONDS_LENGTH ? Constants.Functions.DATE_PARSE_FUNCTION.apply(val.concat("00")) : null)
                .orElse(null);
        // 查询的结束时间精确到秒
        createEndTime = Optional.ofNullable(request.getCreateEndTime())
                .map(val -> val.length() == Constants.TIME_STRING_SECONDS_LENGTH ? Constants.Functions.END_DATE_PARSE_FUNCTION.apply(val.concat("59")) : null)
                .orElse(null);
        if (StringUtils.isNotBlank(request.getArrivalStartTime()) && StringUtils.isNotBlank(request.getArrivalEndTime())) {
            arrivalStartTime = Constants.Functions.DATE_PARSE_UN_SS_FUNCTION.apply(request.getArrivalStartTime());
            arrivalEndTime = Constants.Functions.END_DATE_PARSE_UN_SS_FUNCTION.apply(request.getArrivalEndTime());
        }


        channelIds = CollectionUtils.isNotEmpty(request.getChannelIds()) ? ConverterUtils.convertList(request.getChannelIds(), Integer::valueOf)
                : DynamicChannelType.getAllChannelIds();

        status = StringUtils.equalsIgnoreCase("0", request.getStatus()) ? null : request.getStatus();
        refundTypes = request.getRefundTypes();
        skuName = request.getSkuName();
        orderType = ConverterUtils.nonNullConvert(request.getOrderType(), Integer::valueOf, 0);
        hasMemberCard = ConverterUtils.nonNullConvert(request.getHasMemberCard(), Integer::valueOf, 0);
        // 订单用户类型
        orderUserType = request.getOrderUserType();
        memberCard = request.getMemberCard();
        orderProblemType = request.getOrderProblemType();
        boothIds = request.getBoothIds();
        if (Strings.isNotEmpty(request.getOrderSerialNumber())) {
            orderSerialNumber = ConverterUtils.nonNullConvert(request.getOrderSerialNumber(), Long::valueOf, -1L);
        }
        page = request.getPage();
        pageSize = request.getPageSize();
        if(Objects.nonNull(request.getDeliveryChannelType())) {
            deliveryChannelTypeList = DeliveryChannelUtils.getDeliveryChannelIdList(request.getDeliveryChannelType());
        }
        if (StringUtils.isNotBlank(request.getFinishDeliveryStartTime()) && StringUtils.isNotBlank(request.getFinishDeliveryEndTime())) {
            finishDeliveryStartTime =  Constants.Functions.DATE_PARSE_UN_SS_FUNCTION.apply(request.getFinishDeliveryStartTime());
            finishDeliveryEndTime = Constants.Functions.END_DATE_PARSE_UN_SS_FUNCTION.apply(request.getFinishDeliveryEndTime());
        }
        materialSkuId = request.getSkuId();
        riderAccountName = request.getRiderAccount();
        orderMarks = request.getOrderMarks();
    }


    public ChannelOrderQueryBO(ChannelOrderDownloadRequest request) {

        receiverName = request.getReceiverName();
        receiverPrivacyPhoneLastFourDigits = request.getReceiverPhone();
        poiId = request.getPoiId();
        poiIds = ConverterUtils.nonNullConvert(request.getPoiId(), p -> Arrays.asList(Long.valueOf(p)));
        poiName = request.getPoiName();
        orderId = request.getOrderId();
        receiverAddress = request.getReceiverAddress();

        // 查询的开始时间精确到秒
        createStartTime = Optional.ofNullable(request.getCreateStartTime())
                .map(val -> val.length() == Constants.TIME_STRING_SECONDS_LENGTH ? Constants.Functions.DATE_PARSE_FUNCTION.apply(val.concat("00")) : Constants.Functions.DATE_PARSE_UN_SS_FUNCTION.apply(val))
                .orElse(null);
        // 查询的结束时间精确到秒
        createEndTime = Optional.ofNullable(request.getCreateEndTime())
                .map(val -> val.length() == Constants.TIME_STRING_SECONDS_LENGTH ? Constants.Functions.END_DATE_PARSE_FUNCTION.apply(val.concat("59")) : Constants.Functions.DATE_PARSE_UN_SS_FUNCTION.apply(val))
                .orElse(null);

        channelIds = StringUtils.isNotEmpty(request.getChannelIds()) ?
                ConverterUtils.convertList(Arrays.asList(StringUtils.split(request.getChannelIds(), ",")), Integer::valueOf)
                : DynamicChannelType.getAllChannelIds();

        status = StringUtils.equalsIgnoreCase("0", request.getStatus()) ? null : request.getStatus();
        refundTypes = ConverterUtils.nonNullConvert(request.getRefundTypes(), s -> Arrays.asList(StringUtils.split(s, ",")));
        orderType = ConverterUtils.nonNullConvert(request.getOrderType(), Integer::valueOf, 0);
        hasMemberCard = ConverterUtils.nonNullConvert(request.getHasMemberCard(), Integer::valueOf, 0);
        memberCard = request.getMemberCard();
        skuName = request.getSkuName();
        if (StringUtils.isNotBlank(request.getArrivalStartTime()) && StringUtils.isNotBlank(request.getArrivalEndTime())) {
            arrivalStartTime = Constants.Functions.DATE_PARSE_UN_SS_FUNCTION.apply(request.getArrivalStartTime());
            arrivalEndTime = Constants.Functions.END_DATE_PARSE_UN_SS_FUNCTION.apply(request.getArrivalEndTime());
        }
        boothIds = request.getBoothIds();
        if (Strings.isNotEmpty(request.getOrderSerialNumber())) {
            orderSerialNumber = ConverterUtils.nonNullConvert(request.getOrderSerialNumber(), Long::valueOf, -1L);
        }
        orderProblemType = request.getOrderProblemType();
        page = request.getPage();
        pageSize = request.getPageSize();
        warehouseIds = request.getWarehouseIdList();
        if(Objects.nonNull(request.getDeliveryChannelType())) {
            deliveryChannelTypeList = DeliveryChannelUtils.getDeliveryChannelIdList(request.getDeliveryChannelType());
        }
        if (StringUtils.isNotBlank(request.getFinishDeliveryStartTime()) && StringUtils.isNotBlank(request.getFinishDeliveryEndTime())) {
            finishDeliveryStartTime =  Constants.Functions.DATE_PARSE_UN_SS_FUNCTION.apply(request.getFinishDeliveryStartTime());
            finishDeliveryEndTime = Constants.Functions.END_DATE_PARSE_UN_SS_FUNCTION.apply(request.getFinishDeliveryEndTime());
        }
        materialSkuId = request.getSkuId();
        riderAccountName = request.getRiderAccount();
        clientType = request.getClientType();
        if (StringUtils.isNotBlank(request.getOrderMarks())) {
            orderMarks = ConverterUtils.convertList(Arrays.asList(StringUtils.split(request.getOrderMarks(), ",")), Integer::valueOf);
        }
    }


    public OrderSearchRequest toOrderListRequest() {
        OrderSearchRequest req = new OrderSearchRequest();
        req.setTenantId(tenantId);

        req.setReceiverName(receiverName);
        req.setReceiverPhone(receiverPhone);
        req.setReceiverPrivacyPhoneLastFourDigits(receiverPrivacyPhoneLastFourDigits);
        req.setShopIdList(poiIds);
        req.setShopName(poiName);
        req.setViewOrderIdFuzzy(orderId);
        req.setReceiverAddress(receiverAddress);

        req.setBeginCreateTime(ConverterUtils.nonNullConvert(createStartTime, Date::getTime, DateUtils.addYears(new Date(), -1).getTime()));
        req.setEndCreateTime(ConverterUtils.nonNullConvert(createEndTime, Date::getTime, System.currentTimeMillis()));
        if (CollectionUtils.isNotEmpty(channelIds)) {
            req.setOrderBizTypeList(channelIds.stream().map(ChannelOrderConverter::sourceMid2Biz).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        req.setOrderSourceList(Lists.newArrayList(OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), OrderSourceEnum.GLORY.getValue()
        ));
        req.setStatusList(ConverterUtils.nonNullConvert(status, s -> Arrays.asList(ChannelOrderConverter.orderStatusMid2Biz(Integer.valueOf(s)))));
        req.setRefundTypeList(ChannelOrderConverter.refundTypeMid2BizList(ConverterUtils.convertList(refundTypes, Integer::valueOf)));

        req.setGoodsNameFuzzy(skuName);
        req.setMemberCardNumFuzzy(memberCard);
        // 如果用户类型为null 或为0  ，则表示走原逻辑
        if (orderUserType == null || orderUserType == 0) {
            if (hasMemberCard == 0) {
                req.setOrderUserTypeList(Arrays.asList(OrderUserType.COMMON.getValue(), OrderUserType.MEMBER.getValue()));
            } else {
                req.setOrderUserTypeList(Arrays.asList(hasMemberCard == 1 ? OrderUserType.MEMBER.getValue() : OrderUserType.COMMON.getValue()));
            }
        } else {
            req.setOrderUserTypeList(Arrays.asList(orderUserType));
        }

        if (orderType == 0) {
            req.setOrderBookingList(Arrays.asList(0, 1));
        } else {
            req.setOrderBookingList(Arrays.asList(orderType == 1 ? 0 : 1));
        }
        if (arrivalStartTime != null && arrivalEndTime != null) {
            req.setBeginArrivalTime(arrivalStartTime.getTime());
            req.setEndArrivalTime(arrivalEndTime.getTime());
        }
        req.setBoothIds(boothIds);
        req.setOrderSerialNumber(orderSerialNumber);
        req.setOrderProblemType(orderProblemType);
        req.setPage(page);
        req.setSize(pageSize);
        req.setHideOrderRecvAddress(hideOrderRecvAddress);
        req.setWarehouseIdList(warehouseIds);
        req.setDeliveryChannelTypeList(deliveryChannelTypeList);
        if(finishDeliveryStartTime != null && finishDeliveryEndTime != null) {
            req.setDeliveryStartTime(finishDeliveryStartTime.getTime());
            req.setDeliveryEndTime(finishDeliveryEndTime.getTime());
            req.setDistributeStatus(DistributeStatusEnum.RIDER_DELIVERED.getValue());
        }
        req.setMaterialSkuId(materialSkuId);
        req.setRiderAccountName(riderAccountName);
        req.setClientType(clientType);
        req.setOrderMarkList(orderMarks);
        return req;
    }


    public OrderAllListReq toOrderAllListReq() {
        OrderAllListReq req = new OrderAllListReq();
        req.setTenantId(tenantId);

        req.setReceiverName(receiverName);
        req.setReceiverPhone(receiverPhone);
        req.setReceiverPhoneLastFourDigits(receiverPrivacyPhoneLastFourDigits);
        req.setReceiverAddress(receiverAddress);
        req.setShopId(ConverterUtils.nonNullConvert(poiId, Long::valueOf, 0L));
        req.setShopName(poiName);
        req.setChannelOrderId(orderId);

        req.setBeginCreateTime(ConverterUtils.nonNullConvert(createStartTime, Date::getTime, DateUtils.addYears(new Date(), -1).getTime()));
        req.setEndCreateTime(ConverterUtils.nonNullConvert(createEndTime, Date::getTime, System.currentTimeMillis()));
        req.setChannelIdList(channelIds);

        req.setChannelOrderStatusList(ConverterUtils.nonNullConvert(status, s -> Arrays.asList(Integer.valueOf(s))));
        req.setRefundTagIdList(ConverterUtils.convertList(refundTypes, Integer::valueOf));

        req.setGoodsNameFuzzy(skuName);
        req.setMemberCardNumFuzzy(memberCard);
        if (hasMemberCard == 0) {
            req.setOrderUserTypeList(Arrays.asList(OrderUserType.COMMON.getValue(), OrderUserType.MEMBER.getValue()));
        } else {
            req.setOrderUserTypeList(Arrays.asList(hasMemberCard == 1 ? OrderUserType.MEMBER.getValue() : OrderUserType.COMMON.getValue()));
        }

        if (orderType == 0) {
            req.setOrderBookingTypeList(Arrays.asList(0, 1));
        } else {
            req.setOrderBookingTypeList(Arrays.asList(orderType == 1 ? 0 : 1));
        }
        return req;
    }

    public OcmsOrderAllListReq toOcmsOrderAllListReq() {
        OcmsOrderAllListReq req = new OcmsOrderAllListReq();
        req.setTenantId(tenantId);

        req.setReceiverName(receiverName);
        req.setReceiverPhone(receiverPhone);
        req.setReceiverPhoneLastFourDigits(receiverPrivacyPhoneLastFourDigits);
        req.setReceiverAddress(receiverAddress);
        req.setShopId(ConverterUtils.nonNullConvert(poiId, Long::valueOf, 0L));
        req.setShopName(poiName);
        req.setChannelOrderId(orderId);

        req.setBeginCreateTime(ConverterUtils.nonNullConvert(createStartTime, Date::getTime, DateUtils.addYears(new Date(), -1).getTime()));
        req.setEndCreateTime(ConverterUtils.nonNullConvert(createEndTime, Date::getTime, System.currentTimeMillis()));
        req.setChannelIdList(channelIds);

        req.setChannelOrderStatusList(ConverterUtils.nonNullConvert(status, s -> Arrays.asList(Integer.valueOf(s))));
        req.setRefundTagIdList(ConverterUtils.convertList(refundTypes, Integer::valueOf));

        req.setGoodsNameFuzzy(skuName);
        req.setMemberCardNumFuzzy(memberCard);
        if (hasMemberCard == 0) {
            req.setOrderUserTypeList(Arrays.asList(OrderUserType.COMMON.getValue(), OrderUserType.MEMBER.getValue()));
        } else {
            req.setOrderUserTypeList(Arrays.asList(hasMemberCard == 1 ? OrderUserType.MEMBER.getValue() : OrderUserType.COMMON.getValue()));
        }

        if (orderType == 0) {
            req.setOrderBookingTypeList(Arrays.asList(0, 1));
        } else {
            req.setOrderBookingTypeList(Arrays.asList(orderType == 1 ? 0 : 1));
        }
        return req;
    }

    public ExportChannelOrderDetailRequest toExportChannelOrderDetailRequest() {
        int selectAll = 0;
        ExportChannelOrderDetailRequest req = new ExportChannelOrderDetailRequest();
        req.setTenantId(ContextHolder.currentUserTenantId());
        req.setOperatorId(ContextHolder.currentUid());
        req.setReceiverName(receiverName);
        req.setReceiverAddress(receiverAddress);
        req.setReceiverPrivacyPhoneLastFourDigits(receiverPrivacyPhoneLastFourDigits);
        req.setShopName(poiName);
        req.setShopId(StringUtils.isBlank(poiId) || Long.valueOf(poiId) == 0L ? null : Long.valueOf(poiId));
        req.setChannelOrderId(orderId);
        req.setBeginCreateTime(ConverterUtils.nonNullConvert(createStartTime, Date::getTime, DateUtils.addYears(new Date(), -1).getTime()));
        req.setEndCreateTime(ConverterUtils.nonNullConvert(createEndTime, Date::getTime, System.currentTimeMillis()));
        if (arrivalStartTime != null && arrivalEndTime != null) {
            req.setBeginArrivalTime(arrivalStartTime.getTime());
            req.setEndArrivalTime(arrivalEndTime.getTime());
        }
        req.setOrderStatusList(ChannelOrderConvertUtils.orderStatusMid2BizList(ConverterUtils.nonNullConvert(status, s -> Arrays.asList(Integer.valueOf(s)))));
        req.setOrderBizTypeList(ChannelOrderConvertUtils.sourceMid2BizList(channelIds));
        req.setRefundTagIdList(ChannelOrderConvertUtils.refundTypeMid2BizList(ConverterUtils.convertList(refundTypes, Integer::valueOf)));

        if (orderType == selectAll) {
            req.setOrderBookingTypeList(Arrays.asList(0, 1));
        } else {
            req.setOrderBookingTypeList(Arrays.asList(orderType == 1 ? 0 : 1));
        }
        if (hasMemberCard == selectAll) {
            req.setOrderUserTypeList(Arrays.asList(OrderUserType.COMMON.getValue(), OrderUserType.MEMBER.getValue()));
        } else {
            req.setOrderUserTypeList(Arrays.asList(hasMemberCard == 1 ? OrderUserType.MEMBER.getValue() : OrderUserType.COMMON.getValue()));
        }
        req.setDeliveryChannelTypeList(deliveryChannelTypeList);

        req.setGoodsNameFuzzy(skuName);
        req.setMemberCardNumFuzzy(memberCard);
        req.setBoothIds(boothIds);
        req.setOrderSerialNumber(orderSerialNumber);
        req.setOrderProblemType(orderProblemType);
        req.setWarehouseIdList(warehouseIds);
        if(finishDeliveryStartTime != null && finishDeliveryEndTime != null) {
            req.setDeliveryStartTime(finishDeliveryStartTime.getTime());
            req.setDeliveryEndTime(finishDeliveryEndTime.getTime());
            req.setDistributeStatus(DistributeStatusEnum.RIDER_DELIVERED.getValue());
        }
        req.setMaterialSkuId(materialSkuId);
        req.setRiderAccountName(riderAccountName);
        req.setClientType(clientType);
        req.setOrderMarkList(orderMarks);

        return req;
    }
}
