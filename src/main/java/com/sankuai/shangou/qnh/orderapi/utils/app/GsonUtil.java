package com.sankuai.shangou.qnh.orderapi.utils.app;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2020-06-12 12:18
 * @Description:
 */
@Slf4j
public class GsonUtil {
    //启用 复杂mapkey 序列化
    private static final Gson gson = new GsonBuilder().enableComplexMapKeySerialization().create();


    /**
     * 转换为java bean
     *
     * @param jsonStr   json String
     * @param beanClass 被转换bean 的类型
     * @param <T>       返回值类型 泛型
     * @return 返回值
     */
    public static <T> T toJavaBean(String jsonStr, Class<T> beanClass) {
        try {
            if (StringUtils.isBlank(jsonStr)) {
                log.warn("GsonUtil.toJavaBean jsonStr is null or empty");
                return null;
            }
            return gson.fromJson(jsonStr, beanClass);
        } catch (Exception e) {
            log.error("GsonUtil.toJavaBean exception jsonStr:{}", jsonStr, e);
        }
        return null;
    }

    public static Map<Long, Integer> toLongIntegerMap(String jsonStr) {
        Gson gson = new Gson();
        Type mapType = new TypeToken<Map<Long, Integer>>() {
        }.getType();
        return gson.fromJson(jsonStr, mapType);
    }

    /**
     * obj 转 jsonString
     *
     * @param obj 对象
     * @return jsonStr
     */
    public static String toJsonString(Object obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        return gson.toJson(obj);
    }


    /**
     * 转 beanList
     *
     * @param jsonStr  beanList jsonString
     * @param beanType bean类型
     * @param <T>      List 泛型
     * @return List<T>
     */
    public static <T> List<T> toJavaBeanList(String jsonStr, Class<T> beanType) {
        try {
            if (StringUtils.isBlank(jsonStr)) {
                log.warn("GsonUtil.toJavaBeanList jsonStr is null or empty");
                return null;
            }
            Type collectionType = TypeToken.getParameterized(List.class, beanType).getType();
            return gson.fromJson(jsonStr, collectionType);
        } catch (Exception e) {
            log.error("GsonUtil.toJavaBeanList exception jsonStr:{}", jsonStr, e);
        }
        return null;
    }


    /**
     * 转jsonObject
     *
     * @param json jsonString
     * @return gson jsonObj
     */
    public static JsonObject toJsonObject(String json) {
        try {
            if (StringUtils.isBlank(json)) {
                log.warn("GsonUtil.toJsonObject jsonStr is null or empty");
                return null;
            }
            JsonElement jsonElement = toJsonElement(json);
            if (Objects.nonNull(jsonElement)) {
                return jsonElement.getAsJsonObject();
            }
        } catch (Exception e) {
            log.error("GsonUtil.toJsonObject exception jsonStr:{}", json, e);
        }
        return null;
    }


    /**
     * toJSON element
     *
     * @param json jsonString
     * @return gson jsonElement
     */
    public static JsonElement toJsonElement(String json) {
        try {
            if (StringUtils.isBlank(json)) {
                log.warn("GsonUtil.toJsonElement jsonStr is null or empty");
                return null;
            }
            return JsonParser.parseString(json);
        } catch (Exception e) {
            log.error("GsonUtil.toJsonElement exception jsonStr:{}", json, e);
        }
        return null;
    }

    /**
     * jsonString 转map
     *
     * @param json      jsonString
     * @param keyType   key 泛型的类型
     * @param valueType value 泛型的类型
     * @return map<K, V>
     */
    public static <K, V> Map<K, V> toMap(String json, Class<K> keyType, Class<V> valueType) {
        try {
            Type collectionType = TypeToken.getParameterized(Map.class, keyType, valueType).getType();
            return gson.fromJson(json, collectionType);
        } catch (Exception e) {
            log.error("GsonUtil.toMap exception jsonStr:{}", json, e);
        }
        return null;
    }

    /**
     * json obj 获取指定Long类型
     * @param jsonObject
     * @param memberName
     * @return
     */
    public static Long getLong(JsonObject jsonObject, String memberName) {
        try {
            if (Objects.isNull(jsonObject)) {
                log.warn("GsonUtil.getLong jsonObject is null ");
                return null;
            }
            JsonElement jsonElement = jsonObject.get(memberName);
            if (Objects.isNull(jsonElement)) {
                log.warn("GsonUtil.getLong jsonElement is null ");
                return null;
            }
            return jsonElement.getAsLong();
        } catch (Exception e) {
            log.error("GsonUtil.getLong exception jsonStr:{}", jsonObject, e);
        }
        return null;
    }

    /**
     * json obj 获取指定Integer类型
     * @param jsonObject
     * @param memberName
     * @return
     */
    public static Integer getInteger(JsonObject jsonObject, String memberName) {
        try {
            if (Objects.isNull(jsonObject)) {
                log.warn("GsonUtil.getInteger jsonObject is null ");
                return null;
            }
            JsonElement jsonElement = jsonObject.get(memberName);
            if (Objects.isNull(jsonElement)) {
                log.warn("GsonUtil.getInteger jsonElement is null ");
                return null;
            }
            return jsonElement.getAsInt();
        } catch (Exception e) {
            log.error("GsonUtil.getInteger exception jsonStr:{}", jsonObject, e);
        }
        return null;
    }

    /**
     * json obj 获取指定String类型
     * @param jsonObject
     * @param memberName
     * @return
     */
    public static String getString(JsonObject jsonObject, String memberName) {
        try {
            if (Objects.isNull(jsonObject)) {
                log.warn("GsonUtil.getString jsonObject is null ");
                return null;
            }
            JsonElement jsonElement = jsonObject.get(memberName);
            if (Objects.isNull(jsonElement)) {
                log.warn("GsonUtil.getString jsonElement is null ");
                return null;
            }
            return jsonElement.getAsString();
        } catch (Exception e) {
            log.error("GsonUtil.getString exception jsonStr:{}", jsonObject, e);
        }
        return null;
    }

    public static Double getDouble(JsonObject jsonObject, String memberName) {
        try {
            if (Objects.isNull(jsonObject)) {
                log.warn("GsonUtil.getDouble jsonObject is null ");
                return null;
            }
            JsonElement jsonElement = jsonObject.get(memberName);
            if (Objects.isNull(jsonElement)) {
                log.warn("GsonUtil.getDouble jsonElement is null ");
                return null;
            }
            return jsonElement.getAsDouble();
        } catch (Exception e) {
            log.error("GsonUtil.getDouble exception jsonStr:{}", jsonObject, e);
        }
        return null;
    }


}
