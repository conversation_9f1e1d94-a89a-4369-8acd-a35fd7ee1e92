package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AfterSaleVO;
import lombok.Data;

@Data
public class AfterSaleBO {

    private Integer afsPattern;

    private Integer applyOperatorType;

    private Long applyTime;

    private String applyReason;

    private Integer afterSaleStatus;

    private Integer auditType;

    private Long auditTime;

    private String auditReason;

    private String applyOperatorName;

    private String auditOperatorName;

    public AfterSaleVO toBuildAfterSaleVO() {
        AfterSaleVO afterSaleVO = new AfterSaleVO();
        afterSaleVO.setAfsPattern(afsPattern);
        afterSaleVO.setApplyOperatorType(applyOperatorType);
        afterSaleVO.setApplyTime(applyTime);
        afterSaleVO.setApplyReason(applyReason);
        afterSaleVO.setAfterSaleStatus(afterSaleStatus);
        afterSaleVO.setAuditOperatorType(auditType);
        afterSaleVO.setAuditTime(auditTime);
        afterSaleVO.setAuditReason(auditReason);
        afterSaleVO.setApplyOperatorName(applyOperatorName);
        afterSaleVO.setAuditOperatorName(auditOperatorName);
        return afterSaleVO;

    }


}
