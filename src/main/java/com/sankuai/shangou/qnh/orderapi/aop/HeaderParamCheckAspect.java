package com.sankuai.shangou.qnh.orderapi.aop;

import com.meituan.reco.pickselect.common.exception.ParamException;
import com.sankuai.meituan.shangou.saas.common.exception.ParamInvalidException;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

@Component
@Aspect
public class HeaderParamCheckAspect {

    @Before("@annotation(com.sankuai.shangou.qnh.orderapi.annotation.ValidHeaderParam)")
    public void doAround (JoinPoint point) throws ParamInvalidException {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        int maxStoreSize = MccConfigUtil.getMiniAppOrderMaxStoreSize();
        if (identityInfo.isMiniApp() && identityInfo.isFullStoreMode() && identityInfo.getStoreIdList().size() > maxStoreSize) {
            throw new ParamException("所选门店数量大于" + maxStoreSize + ",请重新选择");
        }
    }
}
