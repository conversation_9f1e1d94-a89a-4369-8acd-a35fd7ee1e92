package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.sankuai.shangou.qnh.orderapi.domain.result.pc.MultiStageOptionable;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiChildOption;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOptionMultiStage;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 * @Author: <EMAIL>
 * @Date: 2023/6/03 19:54
 * @Description:tag类型
 */
@Getter
public enum RefundOrderLabelEnum implements MultiStageOptionable {
    POS(2L,"pos","核销"),
    OVER_SOLD(201L,2L,"overSold","超卖"),
    SUB_CHANNEL(4L,"subBusinessChannel","子渠道"),
    WJTZ(401L,4L,"fitSurvival","物竞天择"),
    TX(402L,4L,"selection","天选"),
    CW(50L,"settlement","核销相关"),
    TZD(5001L,50L,"adjust","调整单"),
    SPECIAL_ORDER_TYPE(5L, "specialOrderType","特殊订单类型"),
    INTEGRATED_MARKETING(504L, 5L, "integratedMarketing", "整合营销"),

    DRUNK_HORSE_REFUND_LABLE(6L,"drunkhorseRefundLable","歪马退单标签"),
    MT_FAMOUS_TAVERN(509L, 6L, "mtFamousTavern", "美团名酒馆"),
    MT_FACAI_WINE(510L, 6L, "FA_CAI_WINE", "发财酒订单"),
    ;

    RefundOrderLabelEnum(Long id, Long parentId, String code, String desc){
        this.id = id;
        this.parentId = parentId;
        this.code = code;
        this.desc = desc;
    }

    public static RefundOrderLabelEnum getByCode(String code){
        for (RefundOrderLabelEnum label: RefundOrderLabelEnum.values()){
            if (Objects.equals(label.code, code)){
                return label;
            }
        }
        return null;
    }

    public static RefundOrderLabelEnum getById(Integer id){
        for (RefundOrderLabelEnum label: RefundOrderLabelEnum.values()){
            if (label.getId().intValue() == id){
                return label;
            }
        }
        return null;
    }

    RefundOrderLabelEnum(Long id, String code, String desc){
        this(id, null, code, desc);
    }

    private Long id;

    private Long parentId;

    private String code;

    private String desc;


    @Override
    public List<UiOptionMultiStage> toOptions() {
        return Arrays.asList(RefundOrderLabelEnum.values()).stream().filter(item -> Objects.isNull(item.getParentId()))
                .map(e -> new UiOptionMultiStage(Math.toIntExact(e.getId()), e.getCode(), e.getDesc(), getChildOptionList(e.getId()))).collect(Collectors.toList());
    }

    public List<UiChildOption> getChildOptionList(Long parentId){
        List<UiChildOption> childOptionList = new ArrayList<>();
        for (RefundOrderLabelEnum label: RefundOrderLabelEnum.values()){
            if(Objects.nonNull(label.getParentId()) && parentId.equals(label.getParentId())){
                childOptionList.add(new UiChildOption(Math.toIntExact(label.getId()), label.getCode(), Math.toIntExact(label.getParentId()), label.getDesc()));
            }
        }
        return childOptionList;
    }

    @Override
    public List<UiOptionMultiStage> toOptions(List<MultiStageOptionable> refundOrderLabelEnumList) {
        if (CollectionUtils.isEmpty(refundOrderLabelEnumList)) {
            return null;
        }
        return refundOrderLabelEnumList.stream().filter(item -> item instanceof RefundOrderLabelEnum)
                .map(item -> (RefundOrderLabelEnum)item).map(e -> new UiOptionMultiStage(Math.toIntExact(e.getId()),
                        e.getCode(), e.getDesc(), getChildOptionList(e.getId())))
                .collect(Collectors.toList());
    }

}

