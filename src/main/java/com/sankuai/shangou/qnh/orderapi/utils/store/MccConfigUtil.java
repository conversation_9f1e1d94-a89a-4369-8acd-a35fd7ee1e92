package com.sankuai.shangou.qnh.orderapi.utils.store;

import com.dianping.lion.client.Lion;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.sankuai.shangou.qnh.orderapi.constant.pc.ConfigKeyConstant;
import com.sankuai.shangou.qnh.orderapi.constant.store.ExternalConfigConstants;
import com.sankuai.shangou.qnh.orderapi.enums.store.MccKeyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


/***
 * author : <EMAIL> 
 * date : 2023/7/10 
 * time : 3:46 PM
 * 描述 :
 **/
@Slf4j
public class MccConfigUtil {

    public static boolean isDrunkHorseTenant(Long tenantId){
        String tenantIdStr = Lion.getConfigRepository("com.sankuai.shangou.empower.orderbiz").get("drunk_horse_tenant");
        if (StringUtils.isBlank(tenantIdStr) || tenantId == null) {
            return false;
        }
        Set<Long> tenantIdSet = Splitter.on(",")
                .trimResults()
                .splitToList(tenantIdStr).stream()
                .map(Long::parseLong)
                .collect(Collectors.toSet());
        return tenantIdSet.contains(tenantId);
    }

    public static String getOrderOcmsMigrationShopId() {
        return Lion.getConfigRepository().get("order.ocms.migration.shopId");
    }

    public static String getOrderOcmsMigrationTenantId() {
        return Lion.getConfigRepository().get("order.ocms.migration.tenantId");
    }

    public static boolean getOrderOcmsMigrationSwitch() {
        return Lion.getConfigRepository().getBooleanValue("order.ocms.migration.switch", false);
    }

    public static boolean getCommentFilterChannel() {
        return Lion.getConfigRepository().getBooleanValue("comment.filter.channel", true);
    }

    public static String getOrderOperatorAuthCodes() {
        return Lion.getConfigRepository().get("store.order.operator.auth.codes");
    }

    public static Map<String, String> getOrderStatusOperateMsm() {
        return Lion.getConfigRepository().getMap("order.status.operateMsm");
    }

    public static int getTokenExpireTimeInSeconds() {
        return Lion.getConfigRepository().getIntValue(ExternalConfigConstants.TOKEN_EXPIRE_TIME_IN_SECONDS, 7 * 24 * 3600);
    }

    public static int getDrunkhorsePrivatePhoneShowHour() {
        return Lion.getConfigRepository().getIntValue("drunkhorse.private.phone.show.hour", 24);
    }

    public static String getOrderExcludeChannelCodes() {
        return Lion.getConfigRepository().get("order.exclude.channel.codes", StringUtils.EMPTY);
    }

    public static String getChannelAbbreviation() {
        return Lion.getConfigRepository().get(MccKeyEnum.CHANNEL_ABBREVIATION.key,
                "{\"100\":\"美团\",\"200\":\"饿了么\",\"300\":\"京东\",\"500\",\"有赞\",\"800\":\"全球蛙\",\"900\":\"自建渠道\"}");
    }

    public static boolean getCatEventStatisticSwitch() {
        return Lion.getConfigRepository().getBooleanValue("cat.event.statistic.switch", true);
    }

    public static Boolean getPickSelectQueryJoinSwitch(Long tenantId){
        if(tenantId == null || tenantId<=0){
            log.error("getPickSelectQueryJoinSwitch tenantId:{}",tenantId);
            return false;
        }
        return Lion.getConfigRepository("com.sankuai.waimai.sc.pickselectservice").getBooleanValue("pickselect.query.join.switch", true);
    }

    public static boolean hasHideAddress(String deliveryAddress){
        List<String> hideAddressList = Lion.getConfigRepository().getList("order.hide.address.list", String.class, Lists.newArrayList("地址已隐藏"));
        if (CollectionUtils.isEmpty(hideAddressList)){
            return false;
        }else if (StringUtils.isBlank(deliveryAddress)){
            return false;
        }
        return hideAddressList.stream().anyMatch(addressSubStr -> StringUtils.contains(deliveryAddress, addressSubStr));
    }

    /**
     * 是否从渠道获取虚拟电话
     *
     * @return
     */
    public static boolean isQueryVirtualPhoneFromChannel(Long tenantId) {
        List<Long> tenantIdList = Lion.getConfigRepository().getList("query.virtual.phone.from.channel", Long.class, Lists.newArrayList());
        return tenantIdList.contains(-1L) || tenantIdList.contains(tenantId);
    }


    /**
     * 判断租户是否为一门店多摊位租户
     */
    public static boolean checkIsOnePoiWithSeveralBoothTenant(Long tenantId){
        try {
            if(Objects.isNull(tenantId)){
                log.info("checkIsOnePoiWithSeveralBoothTenant tenantId is null");
                return false;
            }
            String tenantIdStr = Lion.getConfigRepository("com.sankuai.waimai.sc.saascrmeapi").get(ConfigKeyConstant.ONE_POI_WITH_SEVERAL_BOOTH_TENANT_LIST);
            log.info("checkIsOnePoiWithSeveralBoothTenant tenantIdStr: {}", tenantIdStr);
            if (StringUtils.isBlank(tenantIdStr)) {
                return false;
            }
            String[] tenantArray = tenantIdStr.split(",");
            if(tenantArray.length == 0){
                return false;
            }
            return Arrays.asList(tenantArray).contains(String.valueOf(tenantId));

        } catch (Exception e) {
            log.info("checkIsOnePoiWithSeveralBoothTenant is error tenantId: {}, e= ", tenantId, e);
        }
        return false;
    }

}
