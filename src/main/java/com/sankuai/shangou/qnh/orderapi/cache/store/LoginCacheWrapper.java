package com.sankuai.shangou.qnh.orderapi.cache.store;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Lists;
import com.meituan.mtrace.Tracer;
import com.meituan.reco.pickselect.common.exception.FallbackException;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.reco.pickselect.common.utils.ParamCheckUtils;
import com.sankuai.meituan.reco.store.management.thrift.EmpowerTaskLogicException;
import com.sankuai.meituan.reco.store.management.thrift.Power;
import com.sankuai.meituan.reco.store.management.thrift.StorePower;
import com.sankuai.meituan.reco.store.management.thrift.UserInfo;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import com.sankuai.shangou.qnh.orderapi.constant.store.ExternalConfigConstants;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccConfigUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

@Service
@Slf4j
@Rhino
public class LoginCacheWrapper {
    private static final String STORE_MANAGEMENT_SESSION = "store_management_session";

    @Resource
    private RedisStoreClient redisStoreClient;

    @Data
    private static final class CachedUser implements Serializable {
        private static final long serialVersionUID = 1L;

        private long tenantId;

        private String userId;

        private String username;

        private String tenantToken;

        private String token;

        private List<CachedStorePower> cachedStorePowerList;

        public CachedUser(UserInfo userInfo) {
            ParamCheckUtils.nullCheck(userInfo, "userInfo cannot be null");

            this.tenantId = userInfo.getTenantId();
            this.userId = userInfo.getUserId();
            this.username = userInfo.getUsername();
            this.token = userInfo.getToken();
            this.tenantToken = userInfo.getTenantToken();

            if (CollectionUtils.isNotEmpty(userInfo.getStorePowerList())) {
                cachedStorePowerList = Lists.transform(userInfo.getStorePowerList(), CachedStorePower::new);
            }
        }

        // 给hessian使用
        public CachedUser() {

        }

        public UserInfo toUserInfo() {
            UserInfo userInfo = new UserInfo();

            userInfo.setTenantId(tenantId);
            userInfo.setUserId(userId);
            userInfo.setUsername(username);
            userInfo.setToken(token);
            userInfo.setTenantToken(tenantToken);
            if (CollectionUtils.isNotEmpty(cachedStorePowerList)) {
                userInfo.setStorePowerList(Lists.transform(cachedStorePowerList, CachedStorePower::toStorePower));
            }

            return userInfo;
        }
    }

    @Data
    private static final class CachedStorePower implements Serializable {
        private static final long serialVersionUID = 1L;

        private String storeName;

        private long storeId;

        private List<CachedPower> cachedPowerList;

        public CachedStorePower(StorePower storePower) {
            ParamCheckUtils.nullCheck(storePower, "storePower cannot be null");

            this.storeId = storePower.getStoreId();
            this.storeName = storePower.getStoreName();
            if (CollectionUtils.isNotEmpty(storePower.getPowerList())) {
                this.cachedPowerList = Lists.transform(storePower.getPowerList(), CachedPower::new);
            }
        }

        // 给hessian使用
        public CachedStorePower() {

        }

        public StorePower toStorePower() {
            StorePower storePower = new StorePower();
            storePower.setStoreName(storeName);
            storePower.setStoreId(storeId);
            if (cachedPowerList != null) {
                storePower.setPowerList(Lists.transform(cachedPowerList, CachedPower::toPower));
            }

            return storePower;
        }
    }

    @Data
    private static final class CachedPower implements Serializable {
        private static final long serialVersionUID = 1L;

        private int appType;

        private List<Integer> functionPowerList;

        private List<String> dataGroupPowerList;

        public CachedPower(Power power) {
            ParamCheckUtils.nullCheck(power, "power cannot be null");

            this.appType = power.appType;
            this.functionPowerList = power.getFunctionPowerList();
            this.dataGroupPowerList = power.getDataGroupPowerList();
        }

        // 给hessian使用
        public CachedPower() {

        }

        public Power toPower() {
            Power power = new Power();

            power.setAppType(appType);
            power.setFunctionPowerList(functionPowerList);
            power.setDataGroupPowerList(dataGroupPowerList);

            return power;
        }
    }


    @Degrade(rhinoKey = "LoginCacheWrapper.cacheUserInfo",
            fallBackMethod = "cacheUserInfoFallback",
            timeoutInMilliseconds = 200)
    @CommonMonitorTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public boolean cacheUserInfo(UserInfo userInfo, String key, int expireTimeInSec) throws EmpowerTaskLogicException {
        if (userInfo == null || StringUtils.isEmpty(key)) {
            return false;
        }

        StoreKey storeKey = new StoreKey(STORE_MANAGEMENT_SESSION, key);
        Boolean ret = redisStoreClient.set(storeKey, JacksonUtils.toJson(new CachedUser(userInfo)),
                expireTimeInSec <= 0 ? getTokenExpireTimeInSec() : expireTimeInSec);

        log.info("cache userInfo, key = {}, ret = {}, userInfo = {}", key, ret, userInfo);

        return ret;
    }

    private boolean cacheUserInfoFallback(UserInfo userInfo, String key, int expireTimeInSec) throws EmpowerTaskLogicException {
        log.error("cacheUserInfo fallback, cannot save userInfo = {}, key = {}, expireTimeInSec = {}", userInfo, key, expireTimeInSec);
        throw new FallbackException("保存用户信息username  = " + userInfo.getUsername() + "失败");
    }


    @Degrade(rhinoKey = "LoginCacheWrapper.getUserInfo",
            fallBackMethod = "getUserInfoFallback",
            timeoutInMilliseconds = 200)
    @CommonMonitorTransaction
    @MethodLog(logRequest = true)
    public UserInfo getUserInfo(String key) throws InterruptedException {
        ParamCheckUtils.emptyCheck(key, "key cannot be empty");
        long startTime = System.currentTimeMillis();
        Object obj = redisStoreClient.get(new StoreKey(STORE_MANAGEMENT_SESSION, key));
        long endTime = System.currentTimeMillis();
        if (endTime - startTime > 100) {
            Transaction transaction = Cat.newTransaction("GET_USER_INFO_LONG_CACHE", "getUserInfo_"+ Tracer.id());
            transaction.setStatus("0");
            transaction.complete();
            log.info("get userInfo from cache time is { },and key = { }", endTime - startTime, key);
        }
        CachedUser cachedUser = null;
        if (obj instanceof CachedUser) {
            cachedUser = (CachedUser) obj;
        } else if (obj instanceof String) {
            cachedUser = JacksonUtils.fromJson(obj.toString(), CachedUser.class);
        }

        if (cachedUser == null) {
            return null;
        }

        return cachedUser.toUserInfo();
    }

    public UserInfo getUserInfoFallback(String key) {
        throw new FallbackException("解析key = " + key + "失败");
    }

    private int getTokenExpireTimeInSec() {
        // 默认一周
        return MccConfigUtil.getTokenExpireTimeInSeconds();
    }
}
