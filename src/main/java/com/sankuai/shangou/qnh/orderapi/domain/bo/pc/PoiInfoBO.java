package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StoreDTO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PoiInfoForSelectOptionVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PoiInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PoiInfoWithHierarchyVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/16 11:18
 * @Description:
 */
@Setter
@Getter
@NoArgsConstructor
@ToString
@EqualsAndHashCode(of = {"poiId"})
public class PoiInfoBO {

    private Long poiId;

    private String poiName;

    private Long tenantId;

    private Long wmPoiId;

    private String outPoiId;

    private String payMerchantId;

    private String poiAddress;

    private Integer poiStatus;

    private AreaBO areaInfo;

    private Long departmentId;

    private Date exchangeTime;

    private Long commercialAgentId;

    private Integer entityType;

    private String mobile;

    private Integer shippingMode;

    private Double longitude;

    private Double latitude;

    private Long shareableWarehouseId;

    private String shareableWarehouseName;

    // 运营模式（如加盟/自营） 1：自营 2：加盟
    public Integer operationMode;

    /**
     * 门店配置，配置ID-配置内容JSON
     */
    private Map<Integer, String> poiConfigMap;

    public PoiInfoBO(PoiInfoDto poiInfoDto) {
        poiId             = poiInfoDto.getPoiId();
        poiName           = poiInfoDto.getPoiName();
        tenantId          = poiInfoDto.getTenantId();
        wmPoiId           = poiInfoDto.getWmPoiId();
        outPoiId          = poiInfoDto.getOutPoiId();
        payMerchantId     = poiInfoDto.getPayMerchantId();
        poiAddress        = poiInfoDto.getPoiAddress();
        poiStatus         = poiInfoDto.getPoiStatus();
        areaInfo          = ConverterUtils.nonNullConvert(poiInfoDto.getDistrict(), AreaBO::new);
        departmentId      = poiInfoDto.getDepartmentId();
        commercialAgentId = poiInfoDto.getBindingCommercialAgentId();
        entityType = poiInfoDto.getEntityType();
        mobile = poiInfoDto.getMobile();
        shippingMode = poiInfoDto.getShippingMode();
        longitude = poiInfoDto.getLongitude();
        latitude = poiInfoDto.getLatitude();
        shareableWarehouseId = poiInfoDto.getShareableWarehouseId();
        shareableWarehouseName = poiInfoDto.getShareableWarehouseName();

        if (poiInfoDto.getPoiExtendContentDto() != null) {
            exchangeTime = ConverterUtils.nonNullConvert(poiInfoDto.getPoiExtendContentDto().getExchangeTime(), Date::new);
            operationMode = poiInfoDto.getPoiExtendContentDto().getOperationMode();
        }
    }

    public PoiInfoDto toPoiInfoDto(){
        return PoiInfoDto.builder()
                .poiId(poiId)
                .poiName(poiName)
                .tenantId(tenantId)
                .wmPoiId(wmPoiId)
                .outPoiId(outPoiId)
                .payMerchantId(payMerchantId)
                .poiAddress(poiAddress)
                .poiStatus(poiStatus)
                .entityType(entityType)
                .departmentId(departmentId)
                .mobile(mobile)
                .shippingMode(shippingMode)
                .longitude(longitude)
                .latitude(latitude)
                .shareableWarehouseId(shareableWarehouseId)
                .shareableWarehouseName(shareableWarehouseName)
                .build();

    }


    public PoiInfoBO(StoreDTO storeDTO) {
        poiId = storeDTO.getStoreId();
        poiName = storeDTO.getStoreName();
        tenantId = storeDTO.getTenantId();
        outPoiId = storeDTO.getStoreCode();
    }


    public PoiInfoVO toPoiResp() {
        PoiInfoVO resp = new PoiInfoVO();
        resp.setPoiId(poiId);
        resp.setPoiName(poiName);
        resp.setAddress(poiAddress);
        resp.setOutPoiId(outPoiId);
        resp.setStatus(Integer.valueOf(1).equals(poiStatus) ? "有效":"无效");
        resp.setStatusCode(String.valueOf(poiStatus));
        resp.setCityName(ConverterUtils.nonNullConvert(areaInfo, AreaBO::getCityName));
        resp.setAreaCode(ConverterUtils.nonNullConvert(areaInfo, a -> a.getProvinceCode()+"_"+a.getCityCode()));
        resp.setEntityType(entityType);
        resp.setShippingMode(shippingMode);
        resp.setOperationMode(operationMode);
        resp.setPoiConfigMap(poiConfigMap);
        return resp;
    }

    public PoiInfoForSelectOptionVO toPoiInfoForSelectOptionVO() {
        PoiInfoForSelectOptionVO vo = new PoiInfoForSelectOptionVO();
        vo.setPoiId(poiId);
        vo.setPoiName(poiName);
        vo.setEntityType(entityType);
        return vo;
    }

    public List<PoiInfoWithHierarchyVO> toPoiInfoWithHierarchy(Map<Long, List<PoiInfoBO>> hierarchyInfo, List<Long> originalPoiIds, Boolean global) {
        ArrayList<PoiInfoWithHierarchyVO> result = new ArrayList<>();
        PoiInfoWithHierarchyVO vo = new PoiInfoWithHierarchyVO();
        vo.setPoiId(poiId);
        vo.setAddress(poiAddress);
        vo.setOutPoiId(outPoiId);
        vo.setStatus(Integer.valueOf(1).equals(poiStatus) ? "有效":"无效");
        vo.setStatusCode(String.valueOf(poiStatus));
        vo.setCityName(ConverterUtils.nonNullConvert(areaInfo, AreaBO::getCityName));
        vo.setAreaCode(ConverterUtils.nonNullConvert(areaInfo, a -> a.getProvinceCode()+"_"+a.getCityCode()));
        vo.setEntityType(entityType);
        vo.setShippingMode(shippingMode);
        vo.setGlobal(false);
        if (entityType.equals(PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code()) && global) {
            vo.setPoiName(poiName + "-分门店");
            // 原始包含，才能包含
            if (originalPoiIds.contains(poiId)) {
                PoiInfoWithHierarchyVO globalVo = new PoiInfoWithHierarchyVO();
                BeanUtils.copyProperties(vo, globalVo);
                globalVo.setGlobal(true);
                globalVo.setPoiName(poiName + "-整体");
                result.add(globalVo);
            }
        } else {
            vo.setPoiName(poiName);
        }
        vo.setChildren(hierarchyInfo.getOrDefault(poiId, new ArrayList<>()).stream().map(PoiInfoBO::toPoiResp)
                .collect(Collectors.toList()));
        result.add(vo);
        return result;
    }
}
