package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.google.common.collect.Lists;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PoiBoothVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/4/30 17:22
 * @Description:
 */
@Data
public class PoiBoothBO {

    /**
     * 门店ID
     */
    private Long poiId;

    private String outPoiId;

    /**
     * 门店名称
     */
    private String poiName;

    /**
     * 关联部门ID
     */
    private Long depId;

    /**
     * 摊位列表
     */
    private List<BoothBO> booths;

    public PoiBoothVO toPoiBoothVO() {
        PoiBoothVO poiBoothVO = new PoiBoothVO();

        poiBoothVO.setId(String.valueOf(poiId));
        poiBoothVO.setTitle(poiName);
        poiBoothVO.setDepId(String.valueOf(depId));

        if (CollectionUtils.isNotEmpty(booths)) {
            List<PoiBoothVO> children = Lists.newArrayListWithCapacity(booths.size());
            for (BoothBO booth : booths) {
                PoiBoothVO pb = new PoiBoothVO();
                pb.setId(String.valueOf(booth.getBoothId()));
                pb.setTitle(booth.getBoothName());
                pb.setDepId(String.valueOf(booth.getDepId()));
                pb.setOpHave(booth.isOpHave());
                children.add(pb);
            }

            poiBoothVO.setChildren(children);
        }

        return poiBoothVO;
    }

}
