package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2020/4/22
 * @description
 */
@Data
public class ChannelOrderAdjustBO {

    /**
     * 请求id
     */
    private String requestId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 渠道订单号
     */
    private String orderId;

    /**
     * 渠道编码
     */
    private Integer channelId;

    /**
     * 调整商品列表
     */
    private List<OrderItemAdjustBO> orderItemAdjustBOList;

    /**
     * 调整原因
     */
    private String comments;

    /**
     * 操作人账号
     */
    private String operatorAccount;

    /**
     * 操作人名称
     */
    private String operatorUserName;


    /**
     * 商品项调整信息
     */
    @Data
    public static class OrderItemAdjustBO {

        /**
         * 商品项id
         */
        private Long orderItemId;

        /**
         * 摊位id
         */
        private Long boothId;

        /**
         * 商品线下售价(分)
         */
        private Integer offlinePrice;

        /**
         * 原始摊位id
         */
        private Long originalBoothId;

        /**
         * 原始商品线下售价(分)
         */
        private Integer originalOfflinePrice;

        /**
         * 上次更新时间
         */
        private Long lastUpdateTime;

    }
}
