package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSAfterSaleApplyVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderLabelModel;
import com.meituan.shangou.saas.order.platform.enums.OrderLabelEnum;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum.DRAFT;
import static com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum.DRAFT_DONE;

/**
 * @Author: <EMAIL>
 * @Date: 2022-12-09 11:08
 * @Description:
 */
@TypeDoc(
        description = "订单tag信息"
)
@ApiModel("订单tag信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderTagVO {
    @FieldDoc(
            description = "标签类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "标签类型", required = true)
    private Integer type;

    @FieldDoc(
            description = "标签父类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "标签父类型", required = false)
    private Integer parentType;

    @FieldDoc(
            description = "标签类型字符串", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "标签类型字符串", required = true)
    private String typeStr;

    @FieldDoc(
            description = "标签父类型字符串", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "标签父类型字符串", required = false)
    private String parentTypeStr;

    @FieldDoc(
            description = "标签名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "标签名称", required = true)
    private String name;

    @FieldDoc(
            description = "是否是自定义标签", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否是自定义标签", required = true)
    private boolean customLabel;


    private final static Long REFUND_LABEL_ID = 301L;
    private final static Long ORDER_INFO_CHANGE_ID = 505L;
    private final static Long FAST_DELIVERY_ID = 507L;
    private final static Long CUSTOM_LABEL_PARENT_ID = 15L;

    public OrderTagVO(Integer type, Integer parentType, String name){
        this.type = type;
        this.parentType = parentType;
        this.name = name;
    }

    public static List<OrderTagVO> convertOrderTagList(OCMSOrderVO ocmsOrderVO, List<OrderLabelModel> showLabelList) {
        if (ocmsOrderVO == null) {
            return Collections.emptyList();
        }
        if(CollectionUtils.isNotEmpty(showLabelList)){
            return convertOrderTagListV2(ocmsOrderVO, showLabelList);
        }
        List<OCMSAfterSaleApplyVO> afterSaleApplyVOList = ocmsOrderVO.getAfterSaleApplyVOList();
        List<OCMSAfterSaleApplyVO> afterSaleApplyCollect = Optional.ofNullable(afterSaleApplyVOList)
                .map(Collection::stream).orElse(Stream.empty())
                .filter(item -> !item.getStatus().equals(DRAFT.getValue()) && !item.getStatus().equals(DRAFT_DONE.getValue()))
                .collect(Collectors.toList());

        List<OrderTagVO> orderTagVOList = new ArrayList<>();
        if (StringUtils.isNotEmpty(ocmsOrderVO.getOrderMark())) {
            List<Integer> orderMarkList = Arrays.stream(StringUtils.split(ocmsOrderVO.getOrderMark(), ","))
                    .filter(s -> {
                        try {
                            long value = Long.parseLong(s);
                            return value >= Integer.MIN_VALUE && value <= Integer.MAX_VALUE;
                        } catch (NumberFormatException e) {
                            return false;
                        }
                    })
                    .map(Integer::parseInt).collect(Collectors.toList());
            for (Integer orderMark : orderMarkList) {
                if (OrderLabelEnum.getById(orderMark) != null) {
                    OrderLabelEnum orderLabelEnum = OrderLabelEnum.getById(orderMark);
                    if (Objects.isNull(orderLabelEnum.getParentId())) {
                        continue;
                    }
                    String desc = orderLabelEnum.getDesc();
                    if (OrderLabelEnum.REFUND.getId().equals(orderMark)  && afterSaleApplyCollect.size() > 0) {
                        desc = "退单" + afterSaleApplyCollect.size() + "笔";
                    }else if(needHideTag(orderMark)){
                        continue;
                    }else if(OrderLabelEnum.ORDER_INFO_CHANGE.getId().equals(orderMark)){
                        desc = "用户修改了订单信息";
                    }else if(OrderLabelEnum.FAST_DELIVERY.getId().equals(orderMark)){
                        desc = "闪电送";
                    }
                    OrderTagVO orderTagVO = new OrderTagVO();
                    orderTagVO.setType(orderMark);
                    orderTagVO.setName(desc);
                    orderTagVO.setParentType(orderLabelEnum.getParentId());
                    orderTagVOList.add(orderTagVO);
                }
            }
        }
        return orderTagVOList;
    }

    private static boolean needHideTag(Integer orderMark){
        if (orderMark == null) {
            return false;
        }
        List<OrderLabelEnum> needHideList = Arrays.asList(
                OrderLabelEnum.REFUND,
                OrderLabelEnum.CONTAIN_COMPOSE_PRODUCT,
                OrderLabelEnum.SEVEN_RETURN,
                OrderLabelEnum.COMPENSATION_FOR_NOT_ICE,
                OrderLabelEnum.COMPENSATION_FOR_THAW,
                OrderLabelEnum.COMPENSATION_FOR_BROKEN,
                OrderLabelEnum.PRIVACY_GOODS,
                OrderLabelEnum.DISPATCH_ORDER,
                OrderLabelEnum.PART_REFUND,
                OrderLabelEnum.NO_REFUND,
                OrderLabelEnum.VALID_ORDER,
                OrderLabelEnum.ALL_REFUND
                );
        return needHideList.stream().anyMatch(item -> item.getId().equals(orderMark));
    }

    public static List<OrderTagVO> convertOrderTagListV2(String orderMark, List<OrderLabelModel> showLabelList) {
        try {
            if (StringUtils.isEmpty(orderMark)) {
                return Collections.emptyList();
            }
            List<Long> orderMarkList = Arrays.stream(StringUtils.split(orderMark, ",")).map(Long::parseLong).collect(Collectors.toList());
            Map<Long, OrderLabelModel> labelModelMap = showLabelList.stream().collect(Collectors.toMap(OrderLabelModel::getLabelId, Function.identity(), (a, b) -> a));
            List<OrderTagVO> orderTagVOList = new ArrayList<>();
            for (Long label : orderMarkList) {
                OrderLabelModel labelModel = labelModelMap.get(label);
                if (labelModel == null) {
                    continue;
                }
                OrderTagVO orderTagVO = new OrderTagVO();
                String desc = labelModel.getName();
                if (CUSTOM_LABEL_PARENT_ID.equals(labelModel.getParentId())) {
                    orderTagVO.setCustomLabel(true);
                } else if(label >= Integer.MIN_VALUE && label <= Integer.MAX_VALUE) {
                    orderTagVO.setType(Math.toIntExact(label));
                    orderTagVO.setParentType(Math.toIntExact(labelModel.getParentId()));
                    orderTagVO.setCustomLabel(false);
                }
                orderTagVO.setTypeStr(String.valueOf(label));
                orderTagVO.setParentTypeStr(labelModel.getParentId().toString());
                orderTagVO.setName(desc);
                orderTagVOList.add(orderTagVO);
            }
            return orderTagVOList;
        } catch (Exception e) {
            return Collections.emptyList();
        }

    }

    public static List<OrderTagVO> convertOrderTagListV2(OCMSOrderVO ocmsOrderVO, List<OrderLabelModel> showLabelList) {
        try {
            List<OCMSAfterSaleApplyVO> afterSaleApplyVOList = ocmsOrderVO.getAfterSaleApplyVOList();
            List<OCMSAfterSaleApplyVO> afterSaleApplyCollect = Optional.ofNullable(afterSaleApplyVOList)
                    .map(Collection::stream).orElse(Stream.empty())
                    .filter(item -> !item.getStatus().equals(DRAFT.getValue()) && !item.getStatus().equals(DRAFT_DONE.getValue()))
                    .collect(Collectors.toList());
            List<OrderTagVO> orderTagVOList = new ArrayList<>();
            if (StringUtils.isNotEmpty(ocmsOrderVO.getOrderMark())) {
                List<Long> orderMarkList = Arrays.stream(StringUtils.split(ocmsOrderVO.getOrderMark(), ",")).map(Long::parseLong).collect(Collectors.toList());
                Map<Long, OrderLabelModel> labelModelMap = showLabelList.stream().collect(Collectors.toMap(OrderLabelModel::getLabelId, Function.identity(), (a, b) -> a));
                for (Long orderMark : orderMarkList) {
                    OrderLabelModel labelModel = labelModelMap.get(orderMark);
                    if (labelModel == null) {
                        continue;
                    }
                    String desc = labelModel.getName();
                    if (REFUND_LABEL_ID.equals(orderMark) && afterSaleApplyCollect.size() > 0) {
                        desc = "退单" + afterSaleApplyCollect.size() + "笔";
                    } else if (ORDER_INFO_CHANGE_ID.equals(orderMark)) {
                        desc = "用户修改了订单信息";
                    } else if (FAST_DELIVERY_ID.equals(orderMark)) {
                        desc = "闪电送";
                    }
                    OrderTagVO orderTagVO = new OrderTagVO();
                    if (CUSTOM_LABEL_PARENT_ID.equals(labelModel.getParentId())) {
                        orderTagVO.setCustomLabel(true);
                    } else if(orderMark >= Integer.MIN_VALUE && orderMark <= Integer.MAX_VALUE) {
                        orderTagVO.setType(Math.toIntExact(orderMark));
                        orderTagVO.setParentType(Math.toIntExact(labelModel.getParentId()));
                        orderTagVO.setCustomLabel(false);
                    }
                    orderTagVO.setTypeStr(String.valueOf(orderMark));
                    orderTagVO.setParentTypeStr(labelModel.getParentId().toString());
                    orderTagVO.setName(desc);
                    orderTagVOList.add(orderTagVO);
                }
            }
            return orderTagVOList;
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    public static List<OrderTagVO> convertOrderTagList(List<Long> labelIdList, List<OrderLabelModel> showLabelList){
        if(CollectionUtils.isEmpty(labelIdList)){
            return Collections.emptyList();
        }
        Map<Long, OrderLabelModel> labelModelMap = showLabelList.stream().collect(Collectors.toMap(OrderLabelModel::getLabelId, Function.identity(), (a, b) -> a));
        return labelIdList.stream().map(labelId -> convertOrderTag(labelId, labelModelMap)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static OrderTagVO convertOrderTag(Long labelId, Map<Long, OrderLabelModel> labelModelMap){
        if(labelId == null){
            return null;
        }
        OrderLabelModel labelModel = labelModelMap.get(labelId);
        if (labelModel == null) {
            return null;
        }
        String desc = labelModel.getName();
        OrderTagVO orderTagVO = new OrderTagVO();
        if (CUSTOM_LABEL_PARENT_ID.equals(labelModel.getParentId())) {
            orderTagVO.setCustomLabel(true);
        } else if(labelId >= Integer.MIN_VALUE && labelId <= Integer.MAX_VALUE) {
            orderTagVO.setType(Math.toIntExact(labelId));
            orderTagVO.setParentType(Math.toIntExact(labelModel.getParentId()));
        }
        orderTagVO.setTypeStr(String.valueOf(labelId));
        orderTagVO.setParentTypeStr(labelModel.getParentId().toString());
        orderTagVO.setName(desc);
        return orderTagVO;
    }


    }
