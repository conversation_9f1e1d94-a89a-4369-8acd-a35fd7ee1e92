package com.sankuai.shangou.qnh.orderapi.enums.pda;

import com.sankuai.shangou.qnh.orderapi.enums.pc.AdjustTargetItemEnum;

/**
 * <AUTHOR>
 * @since 2024/7/10
 **/
public enum OrderTabTypeEnum {
    UNKOWN(0, "未知"),
    WAIT_TO_CONFIRM(10, "待接单"),
    WAIT_TO_PICK(20, "待发货"),
    WAIT_TO_DELIVERY(30, "待配送"),
    WAIT_TO_SELF_FETCH(40, "待自提"),
    DELIVERY_EXCEPTION(50, "配送异常"),
    AFTER_SALE(60, "售后"),

    ;

    private int code;
    private String desc;

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static OrderTabTypeEnum enumOf(int code) {
        for (OrderTabTypeEnum status : OrderTabTypeEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
    OrderTabTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
