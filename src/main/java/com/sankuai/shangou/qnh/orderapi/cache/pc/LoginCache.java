package com.sankuai.shangou.qnh.orderapi.cache.pc;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.meituan.shangou.saas.common.json.GsonUtils;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CacheConstant;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.LoginTicket;
import com.taobao.tair3.client.util.SerializableUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 登录缓存
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class LoginCache {

    @Autowired
    private RedisStoreClient redisStoreClient;

    private static final String CATEGORY = "saas_eapi";

    public void addLoginTicket(String ticket, LoginTicket loginTicket, int expireTimeInSeconds) {
        String key = getKey(ticket);
        try {
            byte[] serialized = SerializableUtil.serialize(loginTicket);
            StoreKey storeKey = new StoreKey(CATEGORY, new Object[]{key});
            redisStoreClient.set(storeKey, serialized, expireTimeInSeconds);
        } catch (Exception e) {
            log.error("squirrel put error: key:{}, value:{}, msg:{}", new Object[]{key, GsonUtils.gson().toJson(loginTicket), e.getMessage(), e});
        }
    }

    public LoginTicket getLoginTicket(String ticket) {
        String key = getKey(ticket);
        byte[] result = redisStoreClient.get(new StoreKey(CATEGORY, new Object[]{key}));
        if (result != null) {
            return SerializableUtil.deserialize(result, LoginTicket.class);
        } else {
            log.info("获取缓存数据为空, key:{}", key);
            return null;
        }
    }

    public void refreshLoginTicket(String ticket, int expireTimeInSeconds) {
        addLoginTicket(ticket, getLoginTicket(ticket), expireTimeInSeconds);
    }

    public void removeLoginTicket(String ticket) {
        String key = getKey(ticket);
        redisStoreClient.delete(new StoreKey(CATEGORY, new Object[]{key}));
    }

    private String getKey(String ticket) {
        return CacheConstant.LOGIN_TICKET_KEY_PREFIX + ticket;
    }
}
