package com.sankuai.shangou.qnh.orderapi.enums;

public enum ProductLabelEnum {

    SUPPORT_SEVEN_DAYS_RETURN(1, "7天无理由退货"),
    COMPENSATION_FOR_NOT_ICE(2, "不冰必赔"),
    COMPENSATION_FOR_THAW(3, "融化必赔"),
    COMPENSATION_FOR_BROKEN(4, "坏必赔");


    private int value;
    private String desc;

    ProductLabelEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
