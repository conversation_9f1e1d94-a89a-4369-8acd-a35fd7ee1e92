package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelStoreDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Author: <EMAIL>
 * @Date: 2019/4/3 15:11
 * @Description:
 */
@TypeDoc(
        name = "渠道门店信息",
        description = "渠道门店信息"
)
@Data
@ToString
@EqualsAndHashCode
public class ChannelStoreInfoVO {

    @FieldDoc(
            description = "门店编码"
    )
    private String poiId;

    @FieldDoc(
            description = "渠道门店编码"
    )
    private String channelPoiId;

    @FieldDoc(
            description = "渠道门店名称"
    )
    private String channelPoiName;

    @FieldDoc(
            description = "渠道门店状态"
    )
    private int channelPoiStatus;


    public static ChannelStoreInfoVO build(ChannelStoreDTO dto) {
        ChannelStoreInfoVO vo = new ChannelStoreInfoVO();
        vo.poiId = dto.getStoreId() + "";
        vo.channelPoiId = dto.getChannelPoiCode();
        vo.channelPoiName = dto.getChannelPoiName();
        vo.channelPoiStatus = dto.getChannelStatus();
        return vo;
    }
}
