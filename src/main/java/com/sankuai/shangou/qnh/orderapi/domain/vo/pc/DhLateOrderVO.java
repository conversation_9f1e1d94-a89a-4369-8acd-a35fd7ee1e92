package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.dto.OverTimeOrderVO;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.enums.pc.infrastructure.ChannelTypeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelOrderStatusEnum;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MoneyUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2021-10-08 17:54
 * @Description:
 */
@Data
@ToString
@TypeDoc(
        description = "超时订单对象"
)
public class DhLateOrderVO {


    @FieldDoc(
            description = "订单号"
    )
    @ApiModelProperty(value = "订单号", required = true)
    private String orderId;

    @FieldDoc(
            description = "门店名称"
    )
    @ApiModelProperty(value = "门店名称", required = true)
    private String poiName;

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long poiId;

    @FieldDoc(
            description = "渠道id"
    )
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "渠道名称"
    )
    @ApiModelProperty(value = "渠道名称", required = true)
    private String channelName;

    @FieldDoc(
            description = "实付金额，单位：元"
    )
    @ApiModelProperty(value = "实付金额，单位：元", required = true)
    private String actualPayAmount;


    @FieldDoc(
            description = "订单状态，列表接口返回导出不返回"
    )
    @ApiModelProperty(value = "订单状态", required = true)
    private String orderStatus;


    @FieldDoc(
            description = "配送状态，列表接口返回导出不返回"
    )
    @ApiModelProperty(value = "配送状态", required = true)
    private String deliveryStatus;


    @FieldDoc(
            description = "是否超时发券"
    )
    @ApiModelProperty(value = "是否超时发券", required = true)
    private Boolean hasCompensated;

    @FieldDoc(
            description = "订单类型,实时单，预约单"
    )
    @ApiModelProperty(value = "订单类型", required = true)
    private String orderType;


    @FieldDoc(
            description = "下单时间"
    )
    @ApiModelProperty(value = "下单时间", required = true)
    private String createTime;


    @FieldDoc(
            description = "预计送达时间"
    )
    @ApiModelProperty(value = "预计送达时间", required = true)
    private String estimateArrivalTime;


    @FieldDoc(
            description = "骑手送达时间"
    )
    @ApiModelProperty(value = "骑手送达时间", required = true)
    private String actualArrivalTime;

    @FieldDoc(
            description = "用户id"
    )
    @ApiModelProperty(value = "用户id", required = true)
    private Long userId;


    @FieldDoc(
            description = "超时时长：（分钟）"
    )
    @ApiModelProperty(value = "超时时长：（分钟）", required = true)
    private String overTime;

    @Deprecated
    @FieldDoc(
            description = "优惠券金额：（元）"
    )
    @ApiModelProperty(value = "优惠券金额：（元）", required = true)
    private String couponAmt;

    @FieldDoc(
            description = "优惠券金额列表"
    )
    @ApiModelProperty(value = "优惠券金额列表", required = true)
    private List<String> couponAmtList;

    @FieldDoc(
            description = "外卖门店id"
    )
    @ApiModelProperty(value = "外卖门店id", required = true)
    private Long wmPoiId;
    @FieldDoc(
            description = "发券方式"
    )
    @ApiModelProperty(value = "发券方式", required = true)
    private String couponSendType;

    @FieldDoc(
            description = "是否发财酒"
    )
    @ApiModelProperty(value = "是否发财酒", required = true)
    private Boolean isFacaiWine;

    public HashMap<String, String> toExcelMap() {

        HashMap<String, String> map = new HashMap<>(20);

        map.put("订单号", orderId);
        map.put("门店名称", poiName);
        map.put("渠道", channelName);
        map.put("实付金额", actualPayAmount);
        map.put("是否超时发券", hasCompensated ? "是" : "否");
        map.put("优惠券金额", couponAmt );
        map.put("订单类型", orderType);
        map.put("创建时间", createTime);
        map.put("预计送达时间", estimateArrivalTime);
        return map;

    }

    public static DhLateOrderVO convertVO(OverTimeOrderVO lateOrderVO) {
        DhLateOrderVO vo = new DhLateOrderVO();
        vo.setOrderId(lateOrderVO.getOrderId());
        vo.setPoiId(lateOrderVO.getStoreId());
        vo.setPoiName(lateOrderVO.getPoiName());
        vo.setChannelId(lateOrderVO.getChannelId());
        vo.setChannelName(ChannelTypeEnum.getDescByCode(lateOrderVO.getChannelId()));
        if (Objects.nonNull(lateOrderVO.getActualPayAmount())) {
            vo.setActualPayAmount(MoneyUtils.centToYuanStr(lateOrderVO.getActualPayAmount()));
        }
        if (Objects.nonNull(lateOrderVO.getCreateTime())) {
            vo.setCreateTime(DateFormatUtils.format(lateOrderVO.getCreateTime(), Constants.DateFormats.SHOW_FORMAT));
        }
        if (Objects.nonNull(lateOrderVO.getEstimateArrivalTime())) {
            vo.setEstimateArrivalTime(DateFormatUtils.format(lateOrderVO.getEstimateArrivalTime(), Constants.DateFormats.SHOW_FORMAT));
        }
        vo.setOrderType(lateOrderVO.getOrderType() == 0 ? "实时单" : "预约单");
        vo.setUserId(lateOrderVO.getUserId());
        vo.setHasCompensated(lateOrderVO.isHasCompensated());
        if (Objects.nonNull(lateOrderVO.getOverTime())) {
            BigDecimal ot = new BigDecimal(lateOrderVO.getOverTime()).divide(new BigDecimal(1000L), 0, BigDecimal.ROUND_HALF_UP);
            // 如果超过一分钟则展示单位为分钟，否则为秒
            if (ot.compareTo(BigDecimal.valueOf(60L)) >= 0) {
                vo.setOverTime(ot.divide(new BigDecimal(60L), 2, BigDecimal.ROUND_HALF_UP).toString() + "分钟");
            } else {
                vo.setOverTime(ot.toString() + "秒");
            }
        }
        if (Objects.nonNull(lateOrderVO.getCouponAmt())) {
            vo.setCouponAmt(MoneyUtils.centToYuanStr(lateOrderVO.getCouponAmt()));
        }
        if (Objects.nonNull(lateOrderVO.getOrderStatus())) {
            vo.setOrderStatus(ConverterUtils.nonNullConvert(ChannelOrderStatusEnum.getByCode(lateOrderVO.getOrderStatus())
                    , s -> s.getDesc(), lateOrderVO.getChannelOrderStatusDesc()));
        }
        //特殊逻辑，列表页既要展示订单状态又要展示配送状态所以配送中替换为履约中
        if (StringUtils.isNotBlank(vo.getOrderStatus())&&vo.getOrderStatus().equals(ChannelOrderStatusEnum.PULFILLMENT.getDesc())){
            vo.setOrderStatus("履约中");
        }
        if (Objects.nonNull(lateOrderVO.getDeliveryStatus())) {
            vo.setDeliveryStatus(ConverterUtils.nonNullConvert(DistributeStatusEnum.enumOf(lateOrderVO.getDeliveryStatus()), s -> s.getDesc()));
        }
        if (Objects.nonNull(lateOrderVO.getActualArrivalTime())) {
            vo.setActualArrivalTime(DateFormatUtils.format(lateOrderVO.getActualArrivalTime(), Constants.DateFormats.SHOW_FORMAT));
        }
        if (lateOrderVO.getCouponAmtDescList() != null) {
            vo.setCouponAmtList(lateOrderVO.getCouponAmtDescList());
        }
        if (lateOrderVO.getCouponSendType() != null) {
            vo.setCouponSendType(lateOrderVO.getCouponSendType().getDesc());
        }
        vo.setWmPoiId(lateOrderVO.getWmPoiId());
        vo.setIsFacaiWine(lateOrderVO.getFaCaiWine());
        return vo;
    }
}
