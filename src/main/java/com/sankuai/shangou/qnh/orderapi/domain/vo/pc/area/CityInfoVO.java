package com.sankuai.shangou.qnh.orderapi.domain.vo.pc.area;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.tenant.thrift.dto.DistrictSimDto;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import lombok.*;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/14 14:32
 * @Description:
 */
@TypeDoc(
        name = "查询城市列表VO对象",
        description = "查询城市列表VO对象"
)
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CityInfoVO {

    /**
     * 编码
     */
    private String code;


    /**
     * 名称
     */
    private String name;


    public static CityInfoVO build(DistrictSimDto dto) {
        return builder()
                .code(ConverterUtils.nonNullConvert(dto.getDistrictCode(), String::valueOf))
                .name(dto.getDistrictName())
                .build();

    }

}
