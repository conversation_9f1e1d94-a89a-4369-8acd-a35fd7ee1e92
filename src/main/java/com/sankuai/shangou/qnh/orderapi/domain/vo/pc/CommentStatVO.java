package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommentChannelStatBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommentStatBO;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: <EMAIL>
 * @class: CommentStatVO
 * @date: 2019-08-05 21:27:03
 * @desc:
 */
@Data
public class CommentStatVO {

    /**
     * 分渠道统计评价数据
     * @param bos
     * @return Map<label, Map<渠道id, count>>
     */
    public static Map<String, Map<String, Integer>> calcDetailStatistics(List<CommentChannelStatBO> bos) {
        Map<String, Map<String, Integer>> detailStatistics = new HashMap<>();
        for (CommentChannelStatBO bo : bos) {
            for (CommentStatBO commentStatBO : bo.getChannelCommentStatBOS()) {
                if (detailStatistics.containsKey(commentStatBO.getCommentLabel())) {
                    Map<String, Integer> channel2Count = detailStatistics.get(commentStatBO.getCommentLabel());
                    channel2Count.put(String.valueOf(bo.getChannelId()), commentStatBO.getCommentLabelCount());
                } else {
                    Map<String, Integer> channel2Count = new HashMap<>();
                    channel2Count.put(String.valueOf(bo.getChannelId()), commentStatBO.getCommentLabelCount());
                    detailStatistics.put(commentStatBO.getCommentLabel(), channel2Count);
                }
            }
        }
        return detailStatistics;
    }

    /**
     * 汇总统计评价数据
     * @param detailStatistics
     * @return
     */
    public static Map<String, Integer> calcTotalStatistics(Map<String, Map<String, Integer>> detailStatistics) {
        Map<String, Integer> totalStatisticsMap = new HashMap<>();
        if (MapUtils.isNotEmpty(detailStatistics)) {
            for (Map.Entry<String, Map<String, Integer>> labelInfo : detailStatistics.entrySet()) {
                int labelCount = 0;
                for (Map.Entry<String, Integer> channelInfo : labelInfo.getValue().entrySet()) {
                    labelCount += channelInfo.getValue();
                }
                totalStatisticsMap.put(labelInfo.getKey(), labelCount);
            }
        }
        return totalStatisticsMap;
    }


    public static Map<String, Object> toMap(List<CommentChannelStatBO> bos) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Map<String, Integer>> detailStatistics = calcDetailStatistics(bos);
        Map<String, Integer> totalStatistics = calcTotalStatistics(detailStatistics);
        resultMap.putAll(totalStatistics);
        resultMap.put("detailStatistics", detailStatistics);
        return resultMap;
    }
}
