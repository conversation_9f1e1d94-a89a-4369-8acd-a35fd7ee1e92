package com.sankuai.shangou.qnh.orderapi.enums.pc;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 错误类型枚举
 * @Author: <EMAIL>
 * @Date: 2019/1/25 16:44
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum TaskErrorTypeEnum {

    SKU_NOT_EXIST(4000,"sku不存在"),

    SKU_FIELD_LACK(4001,"SKU字段缺失"),

    SKU_CATEGORY_NOT_EXIST(4002,"前台分类不存在"),

    SKU_PRICE_NOT_EXIST(4003,"未设置价格"),

    SKU_REPETITION_DATA(4004,"重复数据"),

    STORE_NOT_EXIST(5000,"门店不存在"),

    STORE_NOT_OPENED(5001,"门店未开通"),

    PRICE_FORMAT_ERROR(6000,"价格格式错误"),

    PIC_NAME_ERROR(7000,"图片名称错误"),

    PIC_QUANTITY_EXCEEDED(7001,"图片数量错误"),

    CHANNEL_NOT_EXIST(8000,"渠道不存在"),

    CHANNEL_NOT_OPENED(8001,"渠道未开通");

    private int code;

    private String desc;


    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static TaskErrorTypeEnum getByCode(int code) {
        for (TaskErrorTypeEnum e : TaskErrorTypeEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }

        return null;
    }

}
