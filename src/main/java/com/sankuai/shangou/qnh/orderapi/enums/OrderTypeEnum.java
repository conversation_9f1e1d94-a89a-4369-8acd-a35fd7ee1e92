package com.sankuai.shangou.qnh.orderapi.enums;

public enum OrderTypeEnum {


    ORDER(0, "订单"),
    AFTER_SALE_ORDER(1, "售后单");

    OrderTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final int code;
    /**
     * 渠道名称
     */
    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
