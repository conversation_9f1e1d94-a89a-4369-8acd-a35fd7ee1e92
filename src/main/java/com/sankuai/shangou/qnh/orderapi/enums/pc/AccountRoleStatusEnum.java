package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/24
 * 账号角色的账号枚举
 **/
public enum AccountRoleStatusEnum {

    //enum
    ALL(0, "全部"),
    ONLINE(1, "启用"),
    OFFLINE(2, "停用");

    public static Map<Integer, Integer> statusValidMap = Maps.newHashMap();

    public static Map<Integer, Integer> validStatusMap = Maps.newHashMap();

    static {
        // valid定义-1全部0停用1有效
        statusValidMap.put(ALL.getCode(), -1);
        statusValidMap.put(OFFLINE.getCode(), 0);
        statusValidMap.put(ONLINE.getCode(), 1);

        validStatusMap.put(0, OFFLINE.getCode());
        validStatusMap.put(1, ONLINE.getCode());


    }


    private int code;

    private String desc;

    AccountRoleStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    /**
     * 根据状态查valid
     *
     * @param status
     * @return
     */
    public static Integer getValidByStatus(int status) {
        return statusValidMap.get(status);
    }

    /**
     * 根据valid查状态
     *
     * @param valid
     * @return
     */
    public static Integer getStatusByValid(Integer valid) {
        return validStatusMap.get(valid);
    }

    public static AccountRoleStatusEnum enumOf(int code) {
        for (AccountRoleStatusEnum status : AccountRoleStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

}
