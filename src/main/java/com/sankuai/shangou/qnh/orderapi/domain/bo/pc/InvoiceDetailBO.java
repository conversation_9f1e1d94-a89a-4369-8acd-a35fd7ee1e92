package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ChannelOrderDetailVO;
import lombok.Data;

import java.text.DecimalFormat;
import java.util.Objects;

@Data
public class InvoiceDetailBO {

    //1 线上 2 线下
    private Integer InvoiceChannel;

    //申请时间
    private String applyInvoiceTime;

    //发票抬头
    private String title;

    //税号
    private String taxpayerNo;

    //开票金额
    private String InvoiceAmount;

    //发票状态
    private Integer InvoiceStatus;

    //开票完成时间
    private String completeInvoiceTime;

    //发票号码
    private String invoiceNumber;

    //发票下载链接
    private String invoiceDownloadUrl;

    private static final Integer ON_LINE_CHANEL = 1;

    public ChannelOrderDetailVO.InvoiceDetailVO buildInvoiceVO() {
        ChannelOrderDetailVO.InvoiceDetailVO invoiceDetailVO = new ChannelOrderDetailVO.InvoiceDetailVO();
        invoiceDetailVO.setInvoiceChannel(InvoiceChannel);
        invoiceDetailVO.setApplyInvoiceTime(applyInvoiceTime);
        invoiceDetailVO.setTitle(title);
        invoiceDetailVO.setTaxpayerNo(taxpayerNo);
        invoiceDetailVO.setInvoiceAmount(InvoiceAmount);
        invoiceDetailVO.setInvoiceStatus(InvoiceStatus);
        invoiceDetailVO.setCompleteInvoiceTime(completeInvoiceTime);
        invoiceDetailVO.setInvoiceNumber(invoiceNumber);
        invoiceDetailVO.setInvoiceDownloadUrl(invoiceDownloadUrl);
        return invoiceDetailVO;


    }

    public static InvoiceDetailBO buildInvoiceDetailBO(InvoiceApplyRecordBO invoiceApplyRecordBO) {
        if (Objects.isNull(invoiceApplyRecordBO)) {
            return null;
        }
        InvoiceDetailBO invoiceDetailBO = new InvoiceDetailBO();
        invoiceDetailBO.setInvoiceChannel(ON_LINE_CHANEL);
        invoiceDetailBO.setApplyInvoiceTime(invoiceApplyRecordBO.getApplyInvoiceTime());
        invoiceDetailBO.setTitle(invoiceApplyRecordBO.getTitle());
        invoiceDetailBO.setTaxpayerNo(invoiceApplyRecordBO.getTaxpayerNo());
        //金额保留两位小数处理
        Double invoiceAmountValue = invoiceApplyRecordBO.getInvoiceAmount() != null ? Double.parseDouble(invoiceApplyRecordBO.getInvoiceAmount()) : null;
        DecimalFormat df = new DecimalFormat("0.00");
        invoiceDetailBO.setInvoiceAmount(invoiceAmountValue != null ? df.format(invoiceAmountValue) : null);
        invoiceDetailBO.setInvoiceStatus(invoiceApplyRecordBO.getInvoiceStatus());
        invoiceDetailBO.setCompleteInvoiceTime(invoiceApplyRecordBO.getCompleteInvoiceTime());
        invoiceDetailBO.setInvoiceNumber(invoiceApplyRecordBO.getInvoiceNumber());
        invoiceDetailBO.setInvoiceDownloadUrl(invoiceApplyRecordBO.getInvoiceDownloadUrl());
        return invoiceDetailBO;

    }



}
