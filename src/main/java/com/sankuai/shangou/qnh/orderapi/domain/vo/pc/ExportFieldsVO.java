package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * desc: 导出字段响应
 */
@TypeDoc(
        description = "导出字段响应"
)
@ApiModel("导出字段响应")
@Data
@Builder
public class ExportFieldsVO {

    @FieldDoc(
            description = "导出字段文案", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "导出字段文案", required = true)
    private String label;

    @FieldDoc(
            description = "导出字段", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "导出字段", required = true)
    private String value;
}
