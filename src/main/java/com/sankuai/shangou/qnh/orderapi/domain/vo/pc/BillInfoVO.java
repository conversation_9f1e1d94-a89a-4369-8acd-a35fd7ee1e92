package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class BillInfoVO {

    @FieldDoc(description = "订单原总金额(单位:分)", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "订单原总金额(单位:分)", required = true)
    private String originalAmt;

    @FieldDoc(description = "商家收入金额", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "商家收入金额", required = true)
    private String bizReceiveAmt;

    @FieldDoc(description = "支付总金额", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "支付总金额", required = true)
    private String actualPayAmt;

    @FieldDoc(description = "商品原价总金额", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "商品原价总金额", required = true)
    private String itemOriginalAmt;

    @FieldDoc(description = "商品售价总金额", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "商品售价总金额", required = true)
    private String itemSaleAmt;

    @FieldDoc(description = "总的佣金", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "总的佣金", required = true)
    private String totalCommission;

    @FieldDoc(description = "商品佣金", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "商品佣金", required = true)
    private String commission;

    @FieldDoc(description = "运费佣金", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "运费佣金", required = true)
    private String logisticsCommission;

    @FieldDoc(description = "美团店铺环保捐赠", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "美团店铺环保捐赠", required = true)
    private String donationAmt;

    @FieldDoc(description = "积分抵扣金额", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "积分抵扣金额", required = true)
    private String scoreDeduction;

    @FieldDoc(description = "履约服务费", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "履约服务费", required = true)
    private String performService;

    @FieldDoc(description = "自提服务费", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "自提服务费", required = true)
    private String selfPickService;

    @FieldDoc(description = "总的包装费", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "总的包装费", required = true)
    private String totalPackage;

    @FieldDoc(description = "实付包装费", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "实付包装费", required = true)
    private String actualPayPackage;

    @FieldDoc(description = "平台包装费优惠", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "平台包装费优惠", required = true)
    private String platPackageDiscount;

    @FieldDoc(description = "商家包装费优惠", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "商家包装费优惠", required = true)
    private String poiPackageDiscount;

    @FieldDoc(description = "包装费优惠", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "包装费优惠", required = true)
    private String packageDiscount;

    @FieldDoc(description = "平台包装费收入", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "平台包装费收入", required = true)
    private String platPackage;

    @FieldDoc(description = "商家包装费收入", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "商家包装费收入", required = true)
    private String poiPackage;

    @FieldDoc(description = "整单总优惠", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "整单总优惠", required = true)
    private String totalDiscount;

    @FieldDoc(description = "单品总优惠", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "单品总优惠", required = true)
    private String totalItemDiscount;

    @FieldDoc(description = "平台整单优惠", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "平台整单优惠", required = true)
    private String platDiscount;

    @FieldDoc(description = "平台单品优惠", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "平台单品优惠", required = true)
    private String platItemDiscount;

    @FieldDoc(description = "商家整单优惠", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "商家整单优惠", required = true)
    private String poiDiscount;

    @FieldDoc(description = "商家单品优惠", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "商家单品优惠", required = true)
    private String poiItemDiscount;

    @FieldDoc(description = "平台运费优惠", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "平台运费优惠", required = true)
    private String platLogisticsDiscount;

    @FieldDoc(description = "商家运费优惠", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "商家运费优惠", required = true)
    private String poiLogisticsDiscount;

    @FieldDoc(description = "运费优惠", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "运费优惠", required = true)
    private String logisticsDiscount;

    @FieldDoc(description = "运费（骑手）小费（商家+用户）", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "运费（骑手）小费（商家+用户）", required = true)
    private String totalLogisticsTips;

    @FieldDoc(description = "商家运费小费", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "商家运费小费", required = true)
    private String poiLogisticsTips;

    @FieldDoc(description = "用户运费小费", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "用户运费小费", required = true)
    private String customerLogisticsTips;

    @FieldDoc(description = "总运费", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "总运费", required = true)
    private String originalLogisticsAmt;

    @FieldDoc(description = "支付运费", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "支付运费", required = true)
    private String payLogisticsAmt;

    @FieldDoc(description = "商家运费收入", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "商家运费收入", required = true)
    private String poiLogisticsIncome;

    @FieldDoc(description = "基础运费", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "基础运费")
    private String baseFreight;

    @FieldDoc(description = "重量运费", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "重量运费")
    private String weightFreight;

    @FieldDoc(description = "距离运费", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "距离运费")
    private String distanceFreight;

    @FieldDoc(description = "时段运费", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "时段运费")
    private String timeFrameFreight;

    @FieldDoc(description = "商家支付远距离运费", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "商家支付远距离运费")
    private String poiFarDistanceFreight;

    @FieldDoc(description = "整合营销推广结算(商家)", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "整合营销推广结算(商家)", required = true)
    private String poiMarketDiscount;

    @FieldDoc(description = "整合营销推广结算(供应商)", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "整合营销推广结算(供应商)", required = true)
    private String supplierMarketDiscount;

    @FieldDoc(description = "采购金额", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "采购金额", required = true)
    private String purchaseAmt;

    @FieldDoc(description = "采购折扣", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "采购折扣", required = true)
    private String purchaseDiscountAmt;

    @FieldDoc(description = "餐盒费", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "餐盒费", required = true)
    private String boxAmt;

    @FieldDoc(description = "退货运费", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "退货运费", required = true)
    private String returnFreight;

    // 使用购物卡总金额
    @FieldDoc(description = "使用购物卡总金额", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "使用购物卡总金额", required = true)
    public String shopCardTotalFee;

    //本金金额
    @FieldDoc(description = "本金金额", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "本金金额", required = true)
    public String shopCardBaseFee;

    // 赠金金额
    @FieldDoc(description = "赠金金额", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "赠金金额", required = true)
    public String shopCardGiveFee;

    // @FieldDoc(description = "赠金平台分摊")
    @FieldDoc(description = "赠金平台分摊", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "赠金平台分摊", required = true)
    public String shopCardPlatformGiveFeeShare;

    //  @FieldDoc(description = "赠金商家分摊")
    @FieldDoc(description = "赠金商家分摊", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "赠金商家分摊", required = true)
    public String shopCardShopGiveFeeShare;

    @FieldDoc(description = "闪电送费用", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "闪电送费用", required = true)
    public String fastDeliveryAmt;

    @FieldDoc(description = "地址变更费", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "地址变更费", required = false)
    private String addressChangeFee;

    @FieldDoc(description = "地址变更费注释", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "地址变更费注释", required = false)
    private String addressChangeFeeNotes;


    @FieldDoc(description = "红包支付总金额", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "红包支付总金额", required = true)
    private String redpackAmountTotal;

    @FieldDoc(description = "红包支付金额平台承担", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "红包支付金额平台承担", required = true)
    private String redpackAmountPlatform;

    @FieldDoc(description = "红包支付金额商家承担", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "红包支付金额商家承担", required = true)
    private String redpackAmountMerchant;

    @FieldDoc(description = "基础服务费", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "基础服务费", required = false)
    private String baseServiceFee;

}