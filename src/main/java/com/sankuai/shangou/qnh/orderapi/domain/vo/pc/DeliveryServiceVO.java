package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 配送服务包
 */
@Setter
@Getter
public class DeliveryServiceVO {

    @FieldDoc(
            description = "配送服务包编码"
    )
    @ApiModelProperty(value = "配送服务包编码")
    private String deliveryServiceCode;

    @FieldDoc(
            description = "配送服务包名称"
    )
    @ApiModelProperty(value = "配送服务包名称")
    private String deliveryServiceDescription;


}
