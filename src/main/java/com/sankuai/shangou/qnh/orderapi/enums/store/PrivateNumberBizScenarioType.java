package com.sankuai.shangou.qnh.orderapi.enums.store;

/**
 * <AUTHOR>
 * @date 2022/8/1 3:11 PM
 */
public enum PrivateNumberBizScenarioType {

    WAIMAI_CUSTOMER_CALL_MERCHANT(11, "用户拨打商家"),
    WAIMAI_CUSTOMER_CALL_RIDER(12, "用户拨打骑手"),
    WAIMAI_RIDER_CALL_CUSTOMER(13, "骑手拨打用户"),
    WAIMAI_USER_CALLED(14, "用户拨打"),
    WAIMAI_RIDER_CALLED(21, "骑手拨打"),
    WAIMAI_USER_TO_USER(15, "用户拨打用户"),
    WAIMAI_MERCHANT_CALL_CUSTOMER(16, "商家拨打用户"),
    WAIMAI_AFTER_SALE_MERCHANT_CALL_CUSTOMER(17, "售后商家拨打用户");

    public final int code;

    public final String desc;

    PrivateNumberBizScenarioType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
