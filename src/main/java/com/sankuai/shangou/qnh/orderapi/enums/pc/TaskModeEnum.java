package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 区分任务类型
 *
 * <AUTHOR>
 * @date 2022/6/1
 */
@Getter
@AllArgsConstructor
public enum TaskModeEnum {
    /**
     * 默认类型
     */
    DEFAULT("default", Arrays.stream(TenantBusinessModeEnum.values())
            .filter(bizMode -> bizMode != TenantBusinessModeEnum.UNKNOWN)
            .filter(bizMode -> bizMode != TenantBusinessModeEnum.NOT_EXIST)
            .map(TenantBusinessModeEnum::getKey)
            .collect(Collectors.toList())),

    /**
     * 医药类型（专用）
     */
    HEALTH("health", Collections.singletonList(TenantBusinessModeEnum.UNMANNED_MICRO_WAREHOUSE.getKey())),
    ;

    /**
     * 类型
     */
    final String value;
    /**
     * 当前任务类型默认支持的业务线（租户域）
     */
    final List<String> defaultSupportedBizModes;

    public static TaskModeEnum fromValue(String mode) {
        if (StringUtils.isBlank(mode)) {
            return DEFAULT;
        }
        return Arrays.stream(TaskModeEnum.values())
                .filter(taskModeEnum -> taskModeEnum.getValue().equals(mode))
                .findAny().orElse(DEFAULT);
    }
}
