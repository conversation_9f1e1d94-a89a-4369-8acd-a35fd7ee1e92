package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.tenant.thrift.dto.DistrictDto;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2019/2/18 16:21
 * @Description:
 */
@TypeDoc(description = "租户门店基本信息")
@Setter
@Getter
@ToString
@EqualsAndHashCode
public class PoiInfoVO {

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long poiId;

    @FieldDoc(
            description = "门店名称"
    )
    @ApiModelProperty(value = "门店名称", required = true)
    private String poiName;

    @FieldDoc(
            description = "门店地址"
    )
    @ApiModelProperty(value = "门店地址", required = true)
    private String address;

    @FieldDoc(
            description = "门店状态"
    )
    @ApiModelProperty(value = "门店状态", required = true)
    private String status;

    @FieldDoc(
            description = "门店状态编码"
    )
    @ApiModelProperty(value = "门店状态编码")
    private String statusCode;


    /**
     * 城市名称
     */
    private String cityName;


    private String areaCode;


    @FieldDoc(
            description = "外部门店编号"
    )
    @ApiModelProperty(value = "外部门店编号", required = true)
    private String outPoiId;


    private Integer entityType;

    private Integer shippingMode;

    @FieldDoc(
            description = "运营模式（如加盟/自营） 1：自营 2：加盟"
    )
    @ApiModelProperty(value = "运营模式")
    public Integer operationMode;

    @FieldDoc(
            description = "门店配置，配置ID-配置内容JSON"
    )
    @ApiModelProperty(value = "门店配置，配置ID-配置内容JSON")
    private Map<Integer, String> poiConfigMap;



    public static PoiInfoVO build(PoiDetailInfoDTO dto){
        PoiInfoVO vo = new PoiInfoVO();
        vo.setPoiId(dto.getPoiId());
        vo.setPoiName(dto.getPoiName());
        vo.setAddress(dto.getPoiAddress());
        vo.setOutPoiId(dto.getOutPoiId());
        vo.setStatus(Integer.valueOf(1).equals(dto.getPoiStatus()) ? "有效":"无效");
        vo.setStatusCode(String.valueOf(dto.getPoiStatus()));
        vo.setCityName(ConverterUtils.nonNullConvert(dto.getDistrict(), DistrictDto::getCityName));
        vo.setAreaCode(ConverterUtils.nonNullConvert(dto.getDistrict(), a -> a.getProvinceId()+"_"+a.getCityId()));
        vo.setEntityType(dto.getEntityType());
        vo.setShippingMode(dto.getShippingMode());
        return vo;
    }
}
