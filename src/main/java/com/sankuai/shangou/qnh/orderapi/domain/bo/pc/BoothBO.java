package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.tenant.thrift.dto.booth.BoothInfoDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: <EMAIL>
 * @Date: 2019/4/30 17:23
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BoothBO {

    /**
     * 摊位ID
     */
    private Long boothId;

    /**
     * 摊位名称
     */
    private String boothName;


    /**
     * 关联部门ID
     */
    private Long depId;


    /**
     * 门店ID
     */
    private Long poiId;


    /**
     * 摊位状态
     */
    private Integer boothStatus;

    private boolean opHave;


    public BoothBO(BoothInfoDto boothInfoDto) {
        boothId = boothInfoDto.getBoothId();
        boothName = boothInfoDto.getBoothName();
        poiId = boothInfoDto.getPoiId();
        boothStatus = boothInfoDto.getBoothStatus();
        depId = boothInfoDto.getDepId();
    }

}
