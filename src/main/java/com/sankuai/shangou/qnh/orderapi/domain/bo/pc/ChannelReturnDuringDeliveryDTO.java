package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import lombok.Data;

@Data
public class ChannelReturnDuringDeliveryDTO {

    /**
     * @FieldDoc(
     *     description = "渠道返货单Id",
     *     example = "1928374857328384738"
     * )
     */
    public String dispatchOrderId; // optional
    /**
     * @FieldDoc(
     *     description = "预计送达时间",
     *     example = "100"
     * )
     */
    public String deliveryTime; // optional
    /**
     * @FieldDoc(
     *     description = "退款id，当前返货配送单是因为用户申请退款时有值。",
     *     example = "200"
     * )
     */
    public String refundId; // optional
    /**
     * @FieldDoc(
     *     description = "返货配送单状态 参考值： 31：返货中 32：返货成功 33：返货失败",
     *     example = "31"
     * )
     */
    public int status; // optional
    /**
     * @FieldDoc(
     *     description = "状态变更时间",
     *     example = "1704353664"
     * )
     */
    public String time; // optional
    /**
     * @FieldDoc(
     *     description = "返货发起方 参考值： 1：用户 2：骑手",
     *     example = "1"
     * )
     */
    public Integer opRole; // optional
    /**
     * @FieldDoc(
     *     description = "商家验货结果，当商家收到货并发起验货之后有值 参考值： 1：商家验收通过 2：商家验收不通过 3：系统自动验收通过",
     *     example = "1"
     * )
     */
    public Integer poiCheckResult; // optional
    /**
     * @FieldDoc(
     *     description = "商家验货不通过时填写的原因",
     *     example = "货物损坏"
     * )
     */
    public String rejectReason; // optional
}
