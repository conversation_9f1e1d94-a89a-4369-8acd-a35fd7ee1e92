package com.sankuai.shangou.qnh.orderapi.utils.pc;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @author: <EMAIL>
 * @class: GsonUtils
 * @date: 2020-06-02 15:22:47
 * @desc: gson的instance, 提供单例来处理缓存typeAdapter, 并且通用处理日期
 *
 * 示例
 * {"date":"2020-06-03 11:20:19","localDateTime":"2020-06-03 11:20:19","localDate":"2020-06-03","test":"test","num":1,"along":5,"aDouble":5.5,"decimal":5.67}
 *
 */
public class GsonUtils {

    private static Gson instance ;

    static{
        instance = new GsonBuilder().registerTypeAdapter(Date.class, new TypeAdapter<Date>() {
            @Override
            public void write(JsonWriter jsonWriter, Date date) throws IOException {
                if (date == null) {
                    jsonWriter.nullValue();
                    return;
                }
                jsonWriter.value(date.getTime());
            }

            @Override
            public Date read(JsonReader jsonReader) throws IOException {
                if (jsonReader.peek() == JsonToken.NULL) {
                    jsonReader.nextNull();
                    return null;
                }
                return new Date(jsonReader.nextLong());
            }
        }).registerTypeAdapter(LocalDateTime.class, new TypeAdapter<LocalDateTime>() {
            @Override
            public void write(JsonWriter jsonWriter, LocalDateTime date) throws IOException {
                if (date == null) {
                    jsonWriter.nullValue();
                    return;
                }
                jsonWriter.value(date.toString());
            }

            @Override
            public LocalDateTime read(JsonReader jsonReader) throws IOException {
                if (jsonReader.peek() == JsonToken.NULL) {
                    jsonReader.nextNull();
                    return null;
                }
                return LocalDateTime.parse(jsonReader.nextString());
            }
        }).registerTypeAdapter(LocalDate.class, new TypeAdapter<LocalDate>() {
            @Override
            public void write(JsonWriter jsonWriter, LocalDate date) throws IOException {
                if (date == null) {
                    jsonWriter.nullValue();
                    return;
                }
                jsonWriter.value(date.toString());
            }

            @Override
            public LocalDate read(JsonReader jsonReader) throws IOException {
                if (jsonReader.peek() == JsonToken.NULL) {
                    jsonReader.nextNull();
                    return null;
                }
                return LocalDate.parse(jsonReader.nextString());
            }
        }).create();
    }

    private GsonUtils() {
    }



    public static String toJSONString(Object o) {
        return instance.toJson(o);
    }

    public static Gson gson(){
        return instance;
    }

    public static <T> T toJavaBean(String jsonStr, Class<T> beanClass) {
        return instance.fromJson(jsonStr, beanClass);
    }




}
