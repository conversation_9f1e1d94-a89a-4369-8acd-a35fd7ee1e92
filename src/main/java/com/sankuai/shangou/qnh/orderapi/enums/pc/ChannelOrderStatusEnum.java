package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Optionable;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * @Author: <EMAIL>
 * @Date: 2019/1/17 19:54
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum ChannelOrderStatusEnum implements Optionable {

    ALL(0,"全部"),
    NEW_ORDER(10,"已创建"),
    BIZ_CONFIRMED(20,"已接单"),
    PULFILLMENT(25,"配送中"),
    FINISHED(30,"已完成"),
    CANCELING(40,"取消中"),
    CANCELED(50,"已取消")
    ;

    /**
     * 编码
     */
    private int code;

    /**
     * 描述
     */
    private String desc;


    @Override
    public List<UiOption> toOptions() {
        return Arrays.asList(ChannelOrderStatusEnum.values()).stream()
                .map(e -> new UiOption(String.valueOf(e.getCode()),e.getDesc())).collect(Collectors.toList());
    }


    public static ChannelOrderStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }

        for (ChannelOrderStatusEnum e : ChannelOrderStatusEnum.values()) {
            if (e.getCode() == code && code != 0) {
                return e;
            }
        }

        return null;
    }
}
