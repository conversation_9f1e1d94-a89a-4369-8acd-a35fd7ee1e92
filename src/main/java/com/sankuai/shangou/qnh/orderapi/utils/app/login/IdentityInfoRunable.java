package com.sankuai.shangou.qnh.orderapi.utils.app.login;

import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;

public abstract class IdentityInfoRunable implements Runnable {

    private IdentityInfo identityInfo;



    public IdentityInfoRunable(IdentityInfo identityInfo) {
        this.identityInfo = identityInfo;
    }

    public IdentityInfoRunable() {
        this.identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
    }

    @Override
    public void run() {
        ApiMethodParamThreadLocal.getInstance().set(this.identityInfo);
        doRun();
    }

    protected abstract void doRun();

}
