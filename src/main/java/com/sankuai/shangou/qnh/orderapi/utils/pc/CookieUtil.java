package com.sankuai.shangou.qnh.orderapi.utils.pc;

import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.http.DateGenerator;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * Cookie工具类
 */
public class CookieUtil {

    /**
     * 得到当前request请求的所有cookie
     *
     * @param request cookie数组
     * @return
     */
    public static Cookie[] getCookies(HttpServletRequest request) {
        return request == null ? null : request.getCookies();
    }

    /**
     * 根据cookie名字取得cookie
     *
     * @param request
     * @param name
     * @return cookie对象
     */
    public static Cookie getCookie(HttpServletRequest request, String name) {
        Cookie[] cookies = getCookies(request);
        if (cookies != null && cookies.length > 0) {
            for (int i = 0; i < cookies.length; i++) {
                Cookie cookie = cookies[i];
                String cookName = cookie.getName();
                if (cookName != null && cookName.equals(name)) {
                    return cookie;
                }
            }
        }
        return null;
    }

    /**
     * 根据cookie名字取得cookie的值
     *
     * @param request
     * @param name
     * @return cookie的值
     */
    public static String getCookieValue(HttpServletRequest request, String name) {
        Cookie cookie = getCookie(request, name);
        if (cookie != null) {
            return cookie.getValue();
        }
        return null;
    }


    /**
     * 将cookie写入客户端
     *
     * @param response
     * @param name
     * @param value
     * @param domain
     * @param path
     * @param maxAge
     */
    public static void addCookie(HttpServletResponse response, String name, String value,
                                 String domain, String path, int maxAge) {
        Cookie cookie = new Cookie(name, value);
        if (StringUtils.isNotBlank(domain)) {
            cookie.setDomain(domain);
        }
        if (StringUtils.isNotBlank(path)) {
            cookie.setPath(path);
        }
        cookie.setMaxAge(maxAge);
        cookie.setHttpOnly(true);
        response.addCookie(cookie);
    }



    public static void addCookieWithSameSite(HttpServletResponse response, String name, String value,
                                 String domain, String path, int maxAge) {
        String cookieStr = buildCookieStr(name, value, domain, path, maxAge);
        response.addHeader("Set-Cookie", cookieStr);
    }

    private static String buildCookieStr(String name, String value, String domain, String path, int maxAge) {
        StringBuilder cookieStrBuilder = new StringBuilder();
        cookieStrBuilder.append(name).append("=").append(value);
        // Append path
        if (path!=null && path.length()>0)
            cookieStrBuilder.append(";Path=").append(path);

        // Append domain
        if (domain!=null && domain.length()>0)
            cookieStrBuilder.append(";Domain=").append(domain);

        // Handle max-age and/or expires
        if (maxAge >= 0)
        {
            // Always use expires
            // This is required as some browser (M$ this means you!) don't handle max-age even with v1 cookies
            cookieStrBuilder.append(";Expires=");
            if (maxAge == 0) {
                cookieStrBuilder.append(DateGenerator.formatCookieDate(0).trim());
            }
            else {
                DateGenerator.formatCookieDate(cookieStrBuilder, System.currentTimeMillis() + 1000L * maxAge);
            }

            cookieStrBuilder.append(";Max-Age=");
            cookieStrBuilder.append(maxAge);
        }

        // add the other fields
        cookieStrBuilder.append(";Secure");
        cookieStrBuilder.append(";SameSite=None");
        cookieStrBuilder.append(";HttpOnly");


        return cookieStrBuilder.toString();

    }

    /**
     * 删除Cookie
     *
     * @param request
     * @param response
     * @param name
     * @param domain
     * @param path
     */
    public static void removeCookie(HttpServletRequest request, HttpServletResponse response
            , String name, String domain, String path) {
        Cookie[] cookies = getCookies(request);
        if (cookies == null || cookies.length <= 0) {
            return;
        }
        for (Cookie cookie : cookies) {
            String cookName = cookie.getName();
            if (StringUtils.isBlank(cookName)) {
                continue;
            }
            if (Objects.equals(cookName, name)) {
                if (StringUtils.isNotBlank(domain)) {
                    cookie.setDomain(domain);
                }
                if (StringUtils.isNotBlank(path)) {
                    cookie.setPath(path);
                }
                removeCookie(response, cookie);
            }
        }
    }

    /**
     * 删除cookie
     *
     * @param response
     * @param cookie   cookie对象
     */
    public static void removeCookie(HttpServletResponse response, Cookie cookie) {
        if (cookie != null) {
            cookie.setMaxAge(0);
            response.addCookie(cookie);
        }
    }


}
