package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Optionable;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * @Author: <EMAIL>
 * @Date: 2022/12/03 19:54
 * @Description:是否是会员
 */
@Getter
@AllArgsConstructor
public enum MemberCardEnum implements Optionable {

    ALL(0,"全部"),
    YES(1, "是"),
    NO(2, "否");

    /**
     * 编码
     */
    private int code;

    /**
     * 描述
     */
    private String desc;

    @Override
    public List<UiOption> toOptions() {
        return Arrays.asList(MemberCardEnum.values()).stream()
                .map(e -> new UiOption(String.valueOf(e.getCode()),e.getDesc())).collect(Collectors.toList());
    }


    public static MemberCardEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }

        for (MemberCardEnum e : MemberCardEnum.values()) {
            if (e.getCode() == code && code != 0) {
                return e;
            }
        }

        return null;
    }

}
