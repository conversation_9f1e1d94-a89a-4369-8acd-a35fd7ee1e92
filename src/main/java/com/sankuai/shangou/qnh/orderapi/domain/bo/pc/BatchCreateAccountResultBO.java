package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.response.BatchCreateAccountResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.response.CreateAccountResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/3/31
 * @desc 批量创建权限
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchCreateAccountResultBO {

    private int successNum;
    private int failNum;

    private List<AccountCreateResult> accountCreateResultList;


    public static BatchCreateAccountResultBO buildSuccessResult(BatchCreateAccountResponse batchCreateAccountResponse, Map<Long, String> poiIdPoiNameKVMap) {
        BatchCreateAccountResultBO batchCreateAccountResultBO = new BatchCreateAccountResultBO();
        batchCreateAccountResultBO.setSuccessNum(batchCreateAccountResponse.getSuccessNum());
        batchCreateAccountResultBO.setFailNum(batchCreateAccountResponse.getFailedNum());
        List<CreateAccountResult> createAccountResult = batchCreateAccountResponse.getCreateAccountResult();
        batchCreateAccountResultBO.setAccountCreateResultList(createAccountResult.stream().map(cr-> AccountCreateResult.buildThis(cr,poiIdPoiNameKVMap.get(cr.getPoiId()))).collect(Collectors.toList()));
        return batchCreateAccountResultBO;
    }

    public static BatchCreateAccountResultBO buildFailResult(List<BatchCreateAccountBO> batchCreateAccountBOList,String msg) {
        BatchCreateAccountResultBO batchCreateAccountResultBO = new BatchCreateAccountResultBO();
        batchCreateAccountResultBO.setFailNum(batchCreateAccountBOList.size());
        batchCreateAccountResultBO.setAccountCreateResultList(batchCreateAccountBOList.stream().map(batch-> AccountCreateResult.buildByBatchCreateRequest(batch,msg)).collect(Collectors.toList()));
        return batchCreateAccountResultBO;
    }

    @Data
    public static class AccountCreateResult {
        //手机号
        public String mobile;
        //门店id
        public long poiId;

        public String poiName;
        //账号名称
        public String accountName;
        //员工ID
        public Long staffId;
        //原因
        public String remark;
        //创建状态描述
        public String createStatusDesc;
        //操作指南
        public String operatorGuide;
        //账号创建结果
        public int createStatus;


        public static AccountCreateResult buildThis(CreateAccountResult createAccountResult,String poiName) {
            AccountCreateResult accountCreateResult = new AccountCreateResult();
            accountCreateResult.setMobile(createAccountResult.getMobile());
            accountCreateResult.setStaffId(createAccountResult.getStaffId());
            accountCreateResult.setPoiId(createAccountResult.getPoiId());
            accountCreateResult.setPoiName(poiName);
            accountCreateResult.setAccountName("/");
            accountCreateResult.setRemark(createAccountResult.getRemark());
            accountCreateResult.setCreateStatus(createAccountResult.getCreateStatus());
            switch (ResultCodeEnum.findByValue(createAccountResult.getCreateStatus())) {
                case SUCCESS:
                    accountCreateResult.setAccountName(createAccountResult.getAccountName());
                    accountCreateResult.setCreateStatusDesc("成功");
                    accountCreateResult.setRemark(null);
                    break;
                case PERMISSIONS_ASSIGN_FAIL:
                    accountCreateResult.setCreateStatusDesc("失败");
                    accountCreateResult.setOperatorGuide("请手动分配");
                    break;
                default:
                    accountCreateResult.setCreateStatusDesc("失败");
                    accountCreateResult.setOperatorGuide("请手动创建");
                    break;
            }
            return accountCreateResult;

        }

        public static AccountCreateResult buildByBatchCreateRequest(BatchCreateAccountBO batchCreateAccountBO, String msg) {
            AccountCreateResult accountCreateResult = new AccountCreateResult();
            accountCreateResult.setMobile(batchCreateAccountBO.getMobile());
            accountCreateResult.setStaffId(batchCreateAccountBO.getEmpId());
            accountCreateResult.setPoiId(batchCreateAccountBO.getPoiId());
            accountCreateResult.setPoiName(batchCreateAccountBO.getPoiName());
            accountCreateResult.setRemark(msg);
            accountCreateResult.setCreateStatusDesc("失败");
            accountCreateResult.setOperatorGuide("请手动分配");
            return accountCreateResult;
        }
    }


}
