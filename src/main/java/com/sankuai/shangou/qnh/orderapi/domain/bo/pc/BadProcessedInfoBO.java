package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.sgfulfillment.comment.thrift.dto.model.BadProcessedInfoDTO;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentDTO;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.CommentRecordDTO;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2020/11/18
 * @email jianglilin02@meituan
 */
@Data
public class BadProcessedInfoBO extends CommentBO {
    public String recordId;
    public String merchantPhone;
    public long duration;
    public String beginTime;
    public String connectTime;
    public String releaseTime;
    public boolean isMerchantCall;
    public String recordUrl;
    public int callResultEnum;
    private int listenNum;
    private boolean valid;

    public static BadProcessedInfoBO convertToBO(BadProcessedInfoDTO dto) {
        ChannelCommentDTO channelCommentDTO = dto.getChannelCommentDTO();
        CommentBO commentBO = CommentBO.build(channelCommentDTO);
        BadProcessedInfoBO bo = new BadProcessedInfoBO();
        BeanUtils.copyProperties(commentBO, bo);

        CommentRecordDTO recordDTO = dto.getRecordDTO();
        bo.setRecordId(recordDTO.getRecordId());
        bo.setMerchantPhone(recordDTO.getMerchantPhone());
        bo.setDuration(recordDTO.getDuration());
        bo.setBeginTime(recordDTO.getBeginTime());
        bo.setConnectTime(recordDTO.getConnectTime());
        bo.setReleaseTime(recordDTO.getReleaseTime());
        bo.setMerchantCall(recordDTO.getIsMerchantCall());
        bo.setRecordUrl(recordDTO.getRecordUrl());
        bo.setCallResultEnum(recordDTO.getCallResultEnum());
        bo.setListenNum(recordDTO.getListenNum());
        bo.setValid(dto.getChannelCommentDTO().getValid());
        return bo;
    }

}
