package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.shangou.saas.tenant.thrift.dto.dict.DictItemDto;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Data
public class DictItemVo {

    @FieldDoc(
            description = "枚举编码"
    )
    private String code;

    @FieldDoc(
            description = "枚举名称"
    )
    private String desc;

    @FieldDoc(
            description = "业务线（可选项）"
    )
    private String bizMode;

    @FieldDoc(
            description = "父级编码（树形字典时有值）"
    )
    private String parentCode;


    public static DictItemVo of(DictItemDto dto) {
        DictItemVo dictItemVo = new DictItemVo();
        dictItemVo.setCode(dto.getCode());
        dictItemVo.setDesc(dto.getDesc());
        dictItemVo.setBizMode(dto.getBizMode());
        dictItemVo.setParentCode(dto.getParentCode());
        return dictItemVo;
    }

}
