package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2022-12-09 11:08
 * @Description:
 */
@TypeDoc(
        description = "操作日志"
)
@ApiModel("操作日志")
@Data
public class OrderOperatorLogVO {

    @FieldDoc(
            description = "操作类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "操作类型", required = true)
    private Integer operatorType;

    @FieldDoc(
            description = "操作描述", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "操作描述", required = false)
    private String operatorDesc;

    @FieldDoc(
            description = "操作时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "操作时间", required = false)
    private String operationTime;
}
