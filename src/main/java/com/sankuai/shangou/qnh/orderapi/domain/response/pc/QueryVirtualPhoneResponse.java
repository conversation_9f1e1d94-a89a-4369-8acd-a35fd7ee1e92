package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class QueryVirtualPhoneResponse {
    @FieldDoc(
            description = "收货号码-隐私号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "收货号码-隐私号", required = true)
    private String phoneNo;
}
