package com.sankuai.shangou.qnh.orderapi.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.gson.*;
import com.meituan.shangou.saas.o2o.dto.model.CombinationProductModel;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.AfterSaleRecordDetailVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.CombinationChildProductVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSAfterSaleApplyDetailVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.ProductInfoVo;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.AfterSaleApplyDetail;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderItem;
import com.meituan.shangou.saas.order.management.client.utils.ExchangeUtil;
import com.meituan.shangou.saas.order.platform.common.model.OrderItemExchangeModel;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseFinanceDetailBO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;

import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ProductVOForRefund;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.AfterSaleRecordVO;
import com.sankuai.shangou.qnh.orderapi.enums.ComposeTypeEnum;
import com.sankuai.shangou.qnh.orderapi.service.common.ExchangeItemUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.assertj.core.util.Lists;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组合商品Util
 */

@Slf4j
public class CombinationProductUtil {

    private static final String SYMBOL_COMMA = ",";
    private static final String STRING_ZERO = "0";
    private static final String STRING_ONE_DECIMAL = "0.0";
    private static final String STRING_TWO_DECIMAL = "0.00";
    private static final String STRING_THREE_DECIMAL = "0.000";

    /**
     * 部分退/金额退/克重退需要转换的
     */
    public static List<SubProductVo> buildSubProductByModel(List<CombinationProductModel> combinationProductModelList) {
        List<SubProductVo> list = new ArrayList<>();
        try {
            if (CollectionUtils.isNotEmpty(combinationProductModelList)) {
                // 过滤组合品中的换货商品，保持原有逻辑不变
                List<CombinationProductModel> originCombinationProductModelList = combinationProductModelList.stream()
                        .filter(item -> {
                            ExchangeUtil.ExchangeDO exchangeDO = ExchangeUtil.loadExchangeMetaData(item.getExtData());
                            return !(exchangeDO.getExchangeSourceOrderItemId() > 0);
                        }).collect(Collectors.toList());
                // 计算换货前原本组合品所有子商品的总售价
                BigDecimal childrenAllSalePrice = getComposeChildAllSalePrice(originCombinationProductModelList);

                for (CombinationProductModel item : originCombinationProductModelList){
                    SubProductVo vo = new SubProductVo();
                    vo.setName(item.getName());
                    vo.setSkuId(item.getSkuId());
                    vo.setUnitPrice(StringUtils.isNotBlank(item.getSalePrice()) ? new BigDecimal(item.getSalePrice()) : BigDecimal.ZERO);
                    vo.setQuantity(formatThreeDecimal(item.getQuantity()));
                    //组合关系数量和重量
                    vo.setComposeQuantity(formatThreeDecimal(item.getComposeQuantity()));
                    vo.setComposeWeight(formatThreeDecimal(item.getComposeWeight()));
                    //换算比例
                    vo.setProportion(countProportion(item.getSalePrice(), item.getQuantity(), childrenAllSalePrice));
                    vo.setServiceId(item.getServiceId());
                    OrderItemExchangeModel exchangeModel = OrderItemExchangeModel.build(item.getExtData());
                    vo.setExchangeFromCount(exchangeModel.getExchangeSourceOrderItemCnt());
                    vo.setExchangeToCount(exchangeModel.getExchangeOrderItemToCnt());
                    //组合品的换货商品处理
                    vo.setExchangeProductVoList(ExchangeItemUtil.getChildExchangeProductList(item, combinationProductModelList));
                    list.add(vo);
                }
            }
        }catch (Exception e){
          log.info("CombinationProductUtil.buildSubProductByModel error e: ", e);
        }
        return list;
    }

    private static String countProportion(String salePrice, String quantity, BigDecimal childrenAllSalePrice) {
        try {
            if(BigDecimal.ZERO.compareTo(childrenAllSalePrice) < 0){
                BigDecimal oneChildrenAllSalePrice = new BigDecimal(StringUtils.isNotBlank(salePrice) ? salePrice : STRING_ZERO).multiply(new BigDecimal(StringUtils.isNotBlank(quantity) ? quantity : STRING_ZERO));
                return oneChildrenAllSalePrice.divide(childrenAllSalePrice,2,BigDecimal.ROUND_HALF_UP).toString();
            }
        }catch (Exception e){
            log.info("CombinationProductUtil.countProportion is error e= ", e);
        }
        return STRING_TWO_DECIMAL;
    }

    /**
     * 转换
     */
    public static List<SubProductVo> buildSubProduct(List<CombinationChildProductVo> combinationChildProductVoList, Integer count) {
        List<SubProductVo> list = new ArrayList<>();
        try {
            if (CollectionUtils.isNotEmpty(combinationChildProductVoList)) {
                // 组合平所有子商品
                // 过滤组合品中的换货商品，保持原有逻辑不变
                List<CombinationChildProductVo> originCombinationChildProductVoList = combinationChildProductVoList
                        .stream().filter(item -> {
                            ExchangeUtil.ExchangeDO exchangeDO = ExchangeUtil.loadExchangeMetaData(item.getExtData());
                            return !(exchangeDO.getExchangeSourceOrderItemId() > 0);
                        }).collect(Collectors.toList());
                // 计算换货前原本组合品所有子商品的总售价   
                BigDecimal childrenAllSalePrice = getComposeChildrenAllSalePrice(originCombinationChildProductVoList);

                for (CombinationChildProductVo item : originCombinationChildProductVoList){
                    SubProductVo vo = new SubProductVo();
                    vo.setName(item.getName());
                    vo.setSkuId(item.getSkuId());
                    //组合品的换货商品处理
                    vo.setExchangeProductVoList(ExchangeItemUtil.getChildExchangeProductList(item, combinationChildProductVoList));
                    OrderItemExchangeModel exchangeModel = OrderItemExchangeModel.build(item.getExtData());
                    vo.setExchangeFromCount(exchangeModel.getExchangeSourceOrderItemCnt());
                    vo.setExchangeToCount(exchangeModel.getExchangeOrderItemToCnt());
                    //todo 新加内容 还有订单正单的数据

                    //处理新增组合比例 单个组合品对应的子商品数量
                    dealCombinationRelationshipRatio(vo, item, childrenAllSalePrice);
                    vo.setQuantity(formatThreeDecimal(item.getQuantity()));

                    log.info("CombinationProductUtil.buildSubProduct item: {}", item);
                    //虚拟套装还是多包装单元
                    if(StringUtils.isBlank(item.getSalePrice())){
                        vo.setUnitPrice(null);
                        list.add(vo);
                        continue;
                    }
                    int composeType = Optional.ofNullable(item.getComposeType()).orElse(0);
                    if(ComposeTypeEnum.VIRTUAL_SUITE.getValue() == composeType){
                        vo.setUnitPrice(new BigDecimal(StringUtils.isNotBlank(item.getSalePrice()) ? item.getSalePrice() : "0"));
                        list.add(vo);
                        continue;
                    }
                    if(Objects.isNull(count) || StringUtils.isBlank(item.getQuantity())){
                        vo.setUnitPrice(new BigDecimal(StringUtils.isNotBlank(item.getSalePrice()) ? item.getSalePrice() : "0"));
                        list.add(vo);
                        continue;
                    }
                    vo.setUnitPrice(new BigDecimal(StringUtils.isNotBlank(item.getQuantity()) ? item.getQuantity() : "0").multiply(new BigDecimal(StringUtils.isNotBlank(item.getSalePrice()) ? item.getSalePrice() : "0")).divide(BigDecimal.valueOf(count),0,BigDecimal.ROUND_HALF_UP));
                    list.add(vo);
                }
            }
        }catch (Exception e){
            log.info("CombinationProductUtil.buildSubProduct error e: ", e);
        }
        return list;
    }

    private static void dealCombinationRelationshipRatio(SubProductVo vo, CombinationChildProductVo item, BigDecimal childrenAllSalePrice) {
        // 单个组合品对应的数量，正单会传递 后续添加
        vo.setComposeQuantity(formatThreeDecimal(item.getComposeQuantity()));
        // 单个组合品对应的重量 正单后续会添加进去
        vo.setComposeWeight(formatThreeDecimal(item.getComposeWeight()));
        vo.setProportion(countProportion(item.getSalePrice(), item.getQuantity(), childrenAllSalePrice));
    }

    public static List<SubProductVo> getSubProductVoList(List<CombinationChildProductVo> combinationChildProductVoList) {
        List<SubProductVo> list = new ArrayList<>();
        try {
            if (CollectionUtils.isNotEmpty(combinationChildProductVoList)) {
                combinationChildProductVoList.forEach(item -> {
                    ExchangeUtil.ExchangeDO exchangeDO = ExchangeUtil.loadExchangeMetaData(item.getExtData());
                    //如果是换货商品，直接不展示，过滤掉
                    if(exchangeDO.getExchangeSourceOrderItemId() > 0){
                        return;
                    }
                    list.add(buildSubProduct(item, combinationChildProductVoList));
                });
            }
        }catch (Exception e){
            log.info("CombinationProductUtil.getSubProductVoList error e: ", e);
        }
        return list;
    }

    public static SubProductVo buildSubProduct(CombinationChildProductVo item, List<CombinationChildProductVo> allChildItem) {

        SubProductVo vo = new SubProductVo();
        try {
            log.info("CombinationProductUtil.buildSubProduct app item: {}", item);
            vo.setName(item.getName());
            vo.setPicUrl(item.getPicUrl());
            vo.setMultiPicUrl(processMultiPicUrl(item.getPicUrl()));
            vo.setUpcCode(StringUtils.isNotEmpty(item.getUpcCode()) ? Splitter.on(",").splitToList(item.getUpcCode()) : new ArrayList<>());
            vo.setSkuId(item.getSkuId());
            vo.setErpCode(item.getErpCode());
            vo.setUnitPrice(StringUtils.isNotBlank(item.getSalePrice()) ? new BigDecimal(item.getSalePrice()) : BigDecimal.ZERO);
            vo.setWeight(formatThreeDecimal(item.getWeight()));
            vo.setQuantity(formatThreeDecimal(item.getQuantity()));
            vo.setSpecification(item.getSpec());
            vo.setServiceId(item.getServiceId());
            OrderItemExchangeModel exchangeModel = OrderItemExchangeModel.build(item.getExtData());
            vo.setExchangeFromCount(exchangeModel.getExchangeSourceOrderItemCnt());
            vo.setExchangeToCount(exchangeModel.getExchangeOrderItemToCnt());
            vo.setExchangeProductVoList(ExchangeItemUtil.getChildExchangeProductList(item, allChildItem));
            vo.setComposeOrderItemId(StringUtils.isBlank(item.getCombinationItemId()) ? null : Long.parseLong(item.getCombinationItemId()));
        } catch (Exception e) {
            log.info("CombinationProductUtil.buildSubProduct error item: {} ,error = ", item, e);
        }
        return vo;
    }

    public static List<SubProductVo> buildCombinationProduct(AfterSaleApplyDetail afterSaleApplyDetail, ProductInfoVo productInfo) {
        List<SubProductVo> list = new ArrayList<>();
        try {
            if (StringUtils.isBlank(afterSaleApplyDetail.getComposeProduct())) {
                return list;
            }
            JSONArray array = JSONObject.parseArray(afterSaleApplyDetail.getComposeProduct());
            if (Objects.isNull(array) || array.size() == 0) {
                return list;
            }
            // 商品正单明细取图片
            Map<Long, CombinationChildProductVo> productVoMap = getProductMap(productInfo);
            //兼容历史数据
            Map<String, CombinationChildProductVo> productVoSkuIdMap = getProductSkuIdMap(productInfo);
            //计算组合品下所有子商品总售价
            BigDecimal composeChildrenAllSalePrice = getComposeChildrenAllSalePrice(productInfo.getCombinationChildProductVoList());

            log.info("buildCombinationProduct array: {} ,productVoMap: {}, composeChildrenAllSalePrice: {}", JSON.toJSONString(array),JSON.toJSONString(productVoMap), composeChildrenAllSalePrice);
            for (int i = 0; i < array.size(); i++) {
                JSONObject obj = array.getJSONObject(i);
                if (Objects.isNull(obj)) {
                    continue;
                }
                SubProductVo vo = new SubProductVo();
                vo.setQuantity(formatThreeDecimal(obj.getString("quantity")));
                vo.setWeight(formatThreeDecimal(obj.getString("weight")));
                //退单明细采用改字段 退款金额
                vo.setTotalPrice(String.valueOf(divideHundred(obj.getBigDecimal("refundAmt"))));
                Long orderItemId = obj.getLong("orderItemId");
                String skuId = obj.getString("skuId");
                //默认给0
                vo.setProportion(STRING_TWO_DECIMAL);
                // 图片需要单独从正单取
                CombinationChildProductVo dueComposeProductDetail = null;
                if(Objects.nonNull(orderItemId) && Objects.nonNull(productVoMap.get(orderItemId))){
                    dueComposeProductDetail = productVoMap.get(orderItemId);
                }else if(Objects.isNull(orderItemId) && StringUtils.isNotBlank(skuId) && Objects.nonNull(productVoSkuIdMap.get(skuId))){
                    dueComposeProductDetail = productVoSkuIdMap.get(skuId);
                }else {
                    log.info("CombinationProductUtil.buildCombinationProduct has no dueOrderCompose ,error composeProductDetail: {}", obj);
                }
                dealRefundAfterSaleDateFromDueOrder(dueComposeProductDetail, vo);
                // 处理组合品换货信息
                vo.setAfsExchangeProduct(ExchangeItemUtil.buildAfsCombinationProduct(afterSaleApplyDetail.getAfsItemExchangeModelList(), productVoMap,
                        obj.getLong("orderItemId")));
                list.add(vo);
            }

        } catch (Exception e) {
            log.info("CombinationProductUtil.buildCombinationProduct error afterSaleApplyDetail: {} ,error = ", afterSaleApplyDetail, e);

        }
        return list;
    }

    private static void dealRefundAfterSaleDateFromDueOrder(CombinationChildProductVo dueComposeProductDetail, SubProductVo vo) {
        if(Objects.isNull(dueComposeProductDetail)){
            return;
        }
        vo.setPicUrl(dueComposeProductDetail.getPicUrl());
        vo.setMultiPicUrl(processMultiPicUrl(dueComposeProductDetail.getPicUrl()));
        vo.setErpCode(dueComposeProductDetail.getErpCode());
        vo.setSkuId(dueComposeProductDetail.getSkuId());
        vo.setUpcCode(StringUtils.isNotEmpty(dueComposeProductDetail.getUpcCode()) ? Splitter.on(",").splitToList(dueComposeProductDetail.getUpcCode()) : new ArrayList<>());
        vo.setName(dueComposeProductDetail.getName());
        vo.setSpecification(dueComposeProductDetail.getSpec());
        vo.setUnitPrice(BigDecimal.valueOf(divideHundred(dueComposeProductDetail.getSalePrice())));
        vo.setOriginItemQuantity(formatThreeDecimal(dueComposeProductDetail.getQuantity()));
    }

    private static Map<String, CombinationChildProductVo> getProductSkuIdMap(ProductInfoVo productInfo) {
        Map<String, CombinationChildProductVo> productVoMap = new HashMap<>();
        try {
            if(CollectionUtils.isNotEmpty(productInfo.getCombinationChildProductVoList())){
                productVoMap = productInfo.getCombinationChildProductVoList().stream().filter(item -> Objects.nonNull(item.getSkuId())).collect(Collectors.toMap(CombinationChildProductVo::getSkuId, v -> v,(k1, k2) -> k2));
            }
        }catch (Exception e){
            log.info("CombinationProductUtil.getProductSkuIdMap error orderItemId: {}, error = ", productInfo.getOrderItemId(), e);
        }
        return productVoMap;

    }

    private static BigDecimal getComposeChildrenAllSalePrice(List<CombinationChildProductVo> combinationChildProductVoList) {
        BigDecimal childrenAllSalePrice = new BigDecimal(STRING_ZERO);
        if(CollectionUtils.isEmpty(combinationChildProductVoList)){
            return childrenAllSalePrice;
        }
        try {
            for (CombinationChildProductVo item : combinationChildProductVoList){
                // 排除组合品换货的商品信息
                ExchangeUtil.ExchangeDO exchangeDO = ExchangeUtil.loadExchangeMetaData(item.getExtData());
                if (exchangeDO.getExchangeSourceOrderItemId() > 0) {
                    continue;
                }
                BigDecimal salePrice = new BigDecimal(StringUtils.isNotBlank(item.getSalePrice()) ? item.getSalePrice() : STRING_ZERO);
                childrenAllSalePrice = childrenAllSalePrice.add(salePrice.multiply(new BigDecimal(StringUtils.isNotBlank(item.getQuantity()) ? item.getQuantity() : STRING_ZERO)));
            }
        }catch (Exception e){
           log.info("getComposeChildrenAllSalePrice is error");
        }
        log.info("CombinationProductUtil.getComposeChildrenAllSalePrice childrenAllSalePrice: {}", childrenAllSalePrice);
        return childrenAllSalePrice;
    }

    private static BigDecimal getComposeChildAllSalePrice(List<CombinationProductModel> combinationProductModelList) {
        BigDecimal childrenAllSalePrice = new BigDecimal(STRING_ZERO);
        if(CollectionUtils.isEmpty(combinationProductModelList)){
            return childrenAllSalePrice;
        }
        try {
            for (CombinationProductModel item : combinationProductModelList){
                BigDecimal salePrice = new BigDecimal(StringUtils.isNotBlank(item.getSalePrice()) ? item.getSalePrice() : STRING_ZERO);
                childrenAllSalePrice = childrenAllSalePrice.add(salePrice.multiply(new BigDecimal(StringUtils.isNotBlank(item.getQuantity()) ? item.getQuantity() : STRING_ZERO)));
            }
        }catch (Exception e){
            log.info("getComposeChildAllSalePrice is error e= ", e);
        }
        log.info("CombinationProductUtil.getComposeChildAllSalePrice childrenAllSalePrice: {}", childrenAllSalePrice);
        return childrenAllSalePrice;
    }

    private static Map<Long, CombinationChildProductVo> getProductMap(ProductInfoVo productInfo) {
        Map<Long, CombinationChildProductVo> productVoMap = new HashMap<>();
        try {
            if(CollectionUtils.isNotEmpty(productInfo.getCombinationChildProductVoList())){
                productVoMap = productInfo.getCombinationChildProductVoList().stream().filter(item -> Objects.nonNull(item.getServiceId())).collect(Collectors.toMap(CombinationChildProductVo::getServiceId, v -> v));
            }
        }catch (Exception e){
            log.info("CombinationProductUtil.getProductMap error orderItemId: {}, error = ", productInfo.getOrderItemId(), e);
        }
        return productVoMap;
    }


    public static List<SubProductVo> buildSubProductVoList(Long orderItemId, List<CombinationChildProductVo> combinationChildProductVoList) {
        List<SubProductVo> subProductVoList = new ArrayList<>();
        try{
            log.info("CombinationProductUtil.buildSubProductVoList orderItemId: {}, combinationChildProductVoList: {}", orderItemId, combinationChildProductVoList);
            if (CollectionUtils.isEmpty(combinationChildProductVoList)) {
                return subProductVoList;
            }
            List<CombinationChildProductVo> combinationChildProductVoExcludeExchangeList  = Lists.list();
            for (CombinationChildProductVo item : combinationChildProductVoList){
                ExchangeUtil.ExchangeDO exchangeDO = ExchangeUtil.loadExchangeMetaData(item.getExtData());
                if(exchangeDO.getExchangeSourceOrderItemId() > 0){
                    continue;
                }
                combinationChildProductVoExcludeExchangeList.add(item);
            }
            //计算总售价
            if(Objects.isNull(orderItemId)){
                log.info("CombinationProductUtil.buildSubProductVoList orderItemId is null error");
                return subProductVoList;
            }
            BigDecimal childrenAllSalePrice = getComposeChildrenAllSalePrice(combinationChildProductVoExcludeExchangeList);

            for (CombinationChildProductVo productVo : combinationChildProductVoExcludeExchangeList) {
                if(StringUtils.isNotBlank(productVo.getCombinationItemId()) && productVo.getCombinationItemId().equals(orderItemId.toString())){
                    subProductVoList.add(buildSubProductVo(productVo, childrenAllSalePrice, combinationChildProductVoList));
                }
            }
        }catch (Exception e){
            log.error("CombinationProductUtil.buildSubProductVoList error ", e);
        }

        return subProductVoList;
    }


    private static SubProductVo buildSubProductVo(CombinationChildProductVo combinationChildProductVo, BigDecimal childrenAllSalePrice, List<CombinationChildProductVo> allChildItem) {
        SubProductVo subProductVo = new SubProductVo();
        subProductVo.setName(combinationChildProductVo.getName());
        subProductVo.setSkuId(combinationChildProductVo.getSkuId());
        subProductVo.setUnitPrice(StringUtils.isNotEmpty(combinationChildProductVo.getSalePrice()) ? new BigDecimal(combinationChildProductVo.getSalePrice()) : BigDecimal.ZERO);
        subProductVo.setPicUrl(combinationChildProductVo.getPicUrl());
        subProductVo.setUpcCode(StringUtils.isNotEmpty(combinationChildProductVo.getUpcCode()) ? Splitter.on(",").splitToList(combinationChildProductVo.getUpcCode()) : new ArrayList<>());
        subProductVo.setErpCode(combinationChildProductVo.getErpCode());
        subProductVo.setSpecification(combinationChildProductVo.getSpec());
        subProductVo.setMultiPicUrl(processMultiPicUrl(combinationChildProductVo.getPicUrl()));
        subProductVo.setWeight(formatThreeDecimal(combinationChildProductVo.getWeight()));
        subProductVo.setQuantity(formatThreeDecimal(combinationChildProductVo.getQuantity()));
        //组合关系数量和重量
        subProductVo.setComposeQuantity(formatThreeDecimal(combinationChildProductVo.getComposeQuantity()));
        subProductVo.setComposeWeight(formatThreeDecimal(combinationChildProductVo.getComposeWeight()));
        //换算比例
        subProductVo.setProportion(countProportion(combinationChildProductVo.getSalePrice(), combinationChildProductVo.getQuantity(), childrenAllSalePrice));
        subProductVo.setServiceId(combinationChildProductVo.getServiceId());
        String combinationItemId = combinationChildProductVo.getCombinationItemId();
        subProductVo.setComposeOrderItemId(StringUtils.isNotBlank(combinationItemId) ? Long.parseLong(combinationItemId) : null);
        subProductVo.setExchangeProductVoList(ExchangeItemUtil.getChildExchangeProductList(combinationChildProductVo, allChildItem));
        OrderItemExchangeModel exchangeModel = OrderItemExchangeModel.build(combinationChildProductVo.getExtData());
        subProductVo.setExchangeFromCount(exchangeModel.getExchangeSourceOrderItemCnt());
        subProductVo.setExchangeToCount(exchangeModel.getExchangeOrderItemToCnt());
        return subProductVo;
    }


    public static List<SubProductVo> buildSubProductVoList(Long orderItemId, OrderDetailVO orderDetail){
        List<SubProductVo> subProductVoList = new ArrayList<>();
        Map<Long, List<ProductVOForRefund>> productVOForRefundMap = new HashMap<>();
        try{

            if(CollectionUtils.isNotEmpty(orderDetail.getProductList())){
                List<ProductVOForRefund> productList = orderDetail.getProductList();
                productVOForRefundMap = Optional.ofNullable(productList).orElse(new ArrayList<>()).stream().collect(Collectors.groupingBy(ProductVOForRefund::getOrderItemId));
            }

            if(MapUtils.isEmpty(productVOForRefundMap)){
                return subProductVoList;
            }
            List<ProductVOForRefund> productVOForRefunds = productVOForRefundMap.get(orderItemId);
            if(CollectionUtils.isNotEmpty(productVOForRefunds)){
                productVOForRefunds.stream().forEach(productVOForRefund ->{
                    subProductVoList.addAll(productVOForRefund.getSubProductVoList());
                });
            }
        }catch (Exception e){
            log.error("buildSubProductVoList error ", e);
        }
        return subProductVoList;
    }


    public static List<SubProductVo> getAfsDetailComposeProductList(String afterSaleApplyDetail) {
        List<SubProductVo> subProductVoList = new ArrayList<>();
        try {
            if(StringUtils.isBlank(afterSaleApplyDetail)){
                return subProductVoList;
            }
            log.info("CombinationProductUtil.getAfsDetailComposeProductList afterSaleApplyDetail: {}", afterSaleApplyDetail);
            JsonElement jsonElement = JsonParser.parseString(afterSaleApplyDetail);
            if(jsonElement.isJsonArray()) {
                JsonArray jsonArray = jsonElement.getAsJsonArray();
                if (jsonArray.size() == 0) {
                    return subProductVoList;
                }
                for (int i = 0; i < jsonArray.size(); i++) {
                    JsonObject asJsonObject = jsonArray.get(i).getAsJsonObject();
                    if (asJsonObject == null) {
                        continue;
                    }
                    SubProductVo subProductVo = new SubProductVo();
                    subProductVo.setQuantity(formatThreeDecimal(putWithDefault(asJsonObject, "quantity")));
                    subProductVo.setErpCode(putWithDefault(asJsonObject, "erpCode"));
                    subProductVo.setSkuId(putWithDefault(asJsonObject, "skuId"));
                    subProductVo.setWeight(formatThreeDecimal(putWithDefault(asJsonObject, "weight")));
                    subProductVo.setUpcCode(buildUpcCodeList(asJsonObject));
                    subProductVo.setName(putWithDefault(asJsonObject, "name"));
                    subProductVo.setSpecification(putWithDefault(asJsonObject, "spec"));
                    subProductVo.setTotalPrice(formatTWoDecimalAmt(putWithDefault(asJsonObject, "refundAmt")));
                    //app 订单详情,售后详情需要使用父商品OrderItemId
                    String composeOrderItemId = putWithDefault(asJsonObject, "combinationItemId");
                    subProductVo.setComposeOrderItemId(StringUtils.isNotBlank(composeOrderItemId) ? Long.parseLong(composeOrderItemId) : null);
                    String orderItemId = putWithDefault(asJsonObject, "orderItemId");
                    subProductVo.setServiceId(StringUtils.isNotBlank(orderItemId) ? Long.parseLong(orderItemId) : null);
                    subProductVoList.add(subProductVo);
                }
            }
        } catch (Exception e) {
            log.error("CombinationProductUtil.getAfsDetailComposeProductList error e= ", e);
        }
        return subProductVoList;
    }

    public static List<SubProductVo> getAfsDetailComposeProductList(AfterSaleRecordDetailVo afterSaleRecordDetailVo, Map<Long, CombinationChildProductVo> combinationProductMap) {
        List<SubProductVo> subProductVoList = new ArrayList<>();
        try {
            String afterSaleApplyDetail = afterSaleRecordDetailVo.getComposeProduct();
            if (StringUtils.isBlank(afterSaleApplyDetail)) {
                return subProductVoList;
            }
            log.info("CombinationProductUtil.getAfsDetailComposeProductList afterSaleApplyDetail: {}",
                    afterSaleApplyDetail);
            JsonElement jsonElement = JsonParser.parseString(afterSaleApplyDetail);
            if (jsonElement.isJsonArray()) {
                JsonArray jsonArray = jsonElement.getAsJsonArray();
                if (jsonArray.size() == 0) {
                    return subProductVoList;
                }
                for (int i = 0; i < jsonArray.size(); i++) {
                    JsonObject asJsonObject = jsonArray.get(i).getAsJsonObject();
                    if (asJsonObject == null) {
                        continue;
                    }
                    SubProductVo subProductVo = new SubProductVo();
                    subProductVo.setQuantity(formatThreeDecimal(putWithDefault(asJsonObject, "quantity")));
                    subProductVo.setErpCode(putWithDefault(asJsonObject, "erpCode"));
                    subProductVo.setSkuId(putWithDefault(asJsonObject, "skuId"));
                    subProductVo.setWeight(formatThreeDecimal(putWithDefault(asJsonObject, "weight")));
                    subProductVo.setUpcCode(buildUpcCodeList(asJsonObject));
                    subProductVo.setName(putWithDefault(asJsonObject, "name"));
                    subProductVo.setSpecification(putWithDefault(asJsonObject, "spec"));
                    subProductVo.setTotalPrice(formatTWoDecimalAmt(putWithDefault(asJsonObject, "refundAmt")));
                    // app 订单详情,售后详情需要使用父商品OrderItemId
                    String composeOrderItemId = putWithDefault(asJsonObject, "combinationItemId");
                    subProductVo.setComposeOrderItemId(
                            StringUtils.isNotBlank(composeOrderItemId) ? Long.parseLong(composeOrderItemId) : null);
                    String orderItemId = putWithDefault(asJsonObject, "orderItemId");
                    subProductVo.setServiceId(StringUtils.isNotBlank(orderItemId) ? Long.parseLong(orderItemId) : null);
                    String serviceId = putWithDefault(asJsonObject, "orderItemId");
                    subProductVo.setAfsExchangeProduct(
                            ExchangeItemUtil.buildAfsCombinationProduct(afterSaleRecordDetailVo.getAfsItemExchangeModelList(), combinationProductMap,
                                    StringUtils.isNotBlank(serviceId) ? Long.valueOf(serviceId) : null));
                    subProductVoList.add(subProductVo);
                }
            }
        } catch (Exception e) {
            log.error("CombinationProductUtil.getAfsDetailComposeProductList error e= ", e);
        }
        return subProductVoList;
    }

    public static List<SubProductVo> getAfsDetailComposeProductList(OCMSAfterSaleApplyDetailVO ocmsAfterSaleApplyDetailVO, Map<Long, CombinationChildProductVo> combinationProductMap) {
        List<SubProductVo> subProductVoList = new ArrayList<>();
        try {
            String afterSaleApplyDetail = ocmsAfterSaleApplyDetailVO.getComposeProduct();
            if (StringUtils.isBlank(afterSaleApplyDetail)) {
                return subProductVoList;
            }
            log.info("CombinationProductUtil.getAfsDetailComposeProductList afterSaleApplyDetail: {}",
                    afterSaleApplyDetail);
            JsonElement jsonElement = JsonParser.parseString(afterSaleApplyDetail);
            if (jsonElement.isJsonArray()) {
                JsonArray jsonArray = jsonElement.getAsJsonArray();
                if (jsonArray.size() == 0) {
                    return subProductVoList;
                }
                for (int i = 0; i < jsonArray.size(); i++) {
                    JsonObject asJsonObject = jsonArray.get(i).getAsJsonObject();
                    if (asJsonObject == null) {
                        continue;
                    }
                    SubProductVo subProductVo = new SubProductVo();
                    subProductVo.setQuantity(formatThreeDecimal(putWithDefault(asJsonObject, "quantity")));
                    subProductVo.setErpCode(putWithDefault(asJsonObject, "erpCode"));
                    subProductVo.setSkuId(putWithDefault(asJsonObject, "skuId"));
                    subProductVo.setWeight(formatThreeDecimal(putWithDefault(asJsonObject, "weight")));
                    subProductVo.setUpcCode(buildUpcCodeList(asJsonObject));
                    subProductVo.setName(putWithDefault(asJsonObject, "name"));
                    subProductVo.setSpecification(putWithDefault(asJsonObject, "spec"));
                    subProductVo.setTotalPrice(formatTWoDecimalAmt(putWithDefault(asJsonObject, "refundAmt")));
                    // app 订单详情,售后详情需要使用父商品OrderItemId
                    String composeOrderItemId = putWithDefault(asJsonObject, "combinationItemId");
                    subProductVo.setComposeOrderItemId(
                            StringUtils.isNotBlank(composeOrderItemId) ? Long.parseLong(composeOrderItemId) : null);
                    String orderItemId = putWithDefault(asJsonObject, "orderItemId");
                    subProductVo.setServiceId(StringUtils.isNotBlank(orderItemId) ? Long.parseLong(orderItemId) : null);
                    String serviceId = putWithDefault(asJsonObject, "orderItemId");
                    subProductVo.setAfsExchangeProduct(ExchangeItemUtil.buildAfsCombinationProduct(
                            ocmsAfterSaleApplyDetailVO.getAfsItemExchangeModelList(), combinationProductMap,
                            StringUtils.isNotBlank(serviceId) ? Long.valueOf(serviceId) : null));
                    subProductVoList.add(subProductVo);
                }
            }
        } catch (Exception e) {
            log.error("CombinationProductUtil.getAfsDetailComposeProductList error e= ", e);
        }
        return subProductVoList;
    }


    private static String putWithDefault(JsonObject asJsonObject, String key){
        if (!(asJsonObject.get(key) instanceof JsonNull) && asJsonObject.get(key) != null) {
            return asJsonObject.get(key).getAsString();
        }
        return StringUtils.EMPTY;
    }

    private static List<String> buildUpcCodeList(JsonObject asJsonObject){
        String upcCode = putWithDefault(asJsonObject, "upcCode");
        return StringUtils.isNotBlank(upcCode) ? Splitter.on(",").splitToList(upcCode) : new ArrayList<>();
    }

    private static List<String> processMultiPicUrl(String picUrls) {
        if (Strings.isEmpty(picUrls)) {
            return new ArrayList<String>();
        }
        return Arrays.asList(picUrls.split(SYMBOL_COMMA));
    }

    /**
     * 根据settlement 组合子商品信息返回（退单明细用）
     */
    public static List<SubProductVo> getFinanceComposeDetails(List<OrderFuseFinanceDetailBO.ComposeItemFinanceInfo> composeItemFinanceInfos, ProductInfoVo productInfo, AfterSaleApplyDetail afterSaleApplyDetail) {
        List<SubProductVo> subProductVoList = new ArrayList<>();
        try {
            Map<Long, CombinationChildProductVo> productVoMap = getProductMap(productInfo);
            BigDecimal composeChildrenAllSalePrice = getComposeChildrenAllSalePrice(productInfo.getCombinationChildProductVoList());
            log.info("CombinationProductUtil.getFinanceComposeDetails composeItemFinanceInfos: {}, productVoMap: {}", JSON.toJSONString(composeItemFinanceInfos), productVoMap);
            composeItemFinanceInfos.forEach(financeComposeDetail -> {

                SubProductVo vo = new SubProductVo();
                vo.setQuantity(formatThreeDecimal(financeComposeDetail.getQuantity()));
                vo.setErpCode(financeComposeDetail.getErpItemCode());
                vo.setSkuId(financeComposeDetail.getSkuCode());
                vo.setWeight(formatThreeDecimal(financeComposeDetail.getWeight()));
                vo.setName(financeComposeDetail.getSkuName());
                vo.setUnitPrice(BigDecimal.valueOf(Objects.nonNull(financeComposeDetail.getSalePrice()) ? financeComposeDetail.getSalePrice() : Double.NaN));
                vo.setTotalPrice(formatTWoDecimalAmt(financeComposeDetail.getItemSaleAmt()));
                //优惠信息
                vo.setPlatDiscount(formatTWoDecimalAmt(financeComposeDetail.getPlatPromotion()));
                vo.setPlatItemDiscount(formatTWoDecimalAmt(financeComposeDetail.getPlatItemPromotion()));
                vo.setPoiDiscount(formatTWoDecimalAmt(financeComposeDetail.getPoiPromotion()));
                vo.setPoiItemDiscount(formatTWoDecimalAmt(financeComposeDetail.getPoiItemPromotion()));

                //原价 售价
                vo.setSalePrice(formatTWoDecimalAmt(financeComposeDetail.getSalePrice()));
                vo.setOriginPrice(formatTWoDecimalAmt(financeComposeDetail.getOriginPrice()));
                // 需要单独从正单取值的有
                CombinationChildProductVo dueCompose = null;
                if(Objects.nonNull(financeComposeDetail.getOrderItemId())){
                    dueCompose = productVoMap.get(financeComposeDetail.getOrderItemId());
                }
                vo.setProportion(STRING_TWO_DECIMAL);
                if(Objects.nonNull(dueCompose)){
                    vo.setPicUrl(dueCompose.getPicUrl());
                    vo.setMultiPicUrl(processMultiPicUrl(dueCompose.getPicUrl()));
                    vo.setUpcCode(StringUtils.isNotEmpty(dueCompose.getUpcCode()) ? Splitter.on(",").splitToList(dueCompose.getUpcCode()) : new ArrayList<>());
                    vo.setSpecification(dueCompose.getSpec());
                    vo.setName(dueCompose.getName());
                    vo.setProportion(countProportion(dueCompose.getSalePrice(), dueCompose.getQuantity(), composeChildrenAllSalePrice));
                    vo.setOriginItemQuantity(formatThreeDecimal(dueCompose.getQuantity()));
                }
                // 处理组合品换货信息
                if (Objects.nonNull(afterSaleApplyDetail)) {
                    vo.setAfsExchangeProduct(ExchangeItemUtil.buildAfsCombinationProduct(
                            afterSaleApplyDetail.getAfsItemExchangeModelList(), productVoMap,
                            financeComposeDetail.getServiceId()));
                }
                subProductVoList.add(vo);
            });

        }catch (Exception e){

            log.info("CombinationProductUtil.getFinanceComposeDetails is error composeItemFinanceInfos: {}, e= ", composeItemFinanceInfos, e);
        }
        return subProductVoList;
    }

    public static double divideHundred(Integer value){
        if (value == null){
            return 0D;
        }
        return BigDecimal.valueOf(value).divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP).doubleValue();
    }

    public static double divideHundred(BigDecimal value){
        if (value == null){
            return 0D;
        }
        return value.divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP).doubleValue();
    }

    public static double divideHundred(String value){
        if (StringUtils.isBlank(value)){
            return 0D;
        }
        return new BigDecimal(value).divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP).doubleValue();
    }

    //金额退需要单独处理子商品图片 还有组合品精简字段需从正单取
    public static void separateDealPicUrl(List<SubProductVo> subProduct, OrderItem orderItem) {
        try {
            log.info("CombinationProductUtil.separateDealPicUrl subProduct: {}, orderItem: {}", JSON.toJSONString(subProduct), orderItem);
            if(CollectionUtils.isEmpty(subProduct) || CollectionUtils.isEmpty(orderItem.getCombinationChildProductList())){
                return;
            }
            Map<Long, CombinationChildProductVo> dueComposeMap = orderItem.getCombinationChildProductList().stream().filter(item -> Objects.nonNull(item.getServiceId())).collect(Collectors.toMap(CombinationChildProductVo::getServiceId, value -> value));
            subProduct.forEach(item ->{
                if(Objects.isNull(item.getServiceId()) || Objects.isNull(dueComposeMap.get(item.getServiceId()))){
                    log.info("正单没有对应的组合品子商品 item serviceId: {}", item.getServiceId());
                    return;
                }
                CombinationChildProductVo vo = dueComposeMap.get(item.getServiceId());
                item.setPicUrl(vo.getPicUrl());
                item.setMultiPicUrl(processMultiPicUrl(vo.getPicUrl()));
                item.setUpcCode(StringUtils.isNotEmpty(vo.getUpcCode()) ? Splitter.on(",").splitToList(vo.getUpcCode()) : new ArrayList<>());
                item.setSpecification(vo.getSpec());
                //补充精简字段
                item.setSkuId(vo.getSkuId());
                item.setName(vo.getName());
                item.setErpCode(vo.getErpCode());
            });
        }catch (Exception e){
            log.info("CombinationProductUtil.separateDealPicUrl is error orderId {}, orderItemId: {}, e= ", orderItem.getOrderId(), orderItem.getOrderItemId(), e);
        }

    }

    public static void dealAppOrderDetailAfterSaleCompose(AfterSaleRecordVO afterSaleRecordVO, OrderFuseFinanceDetailBO orderFuseFinanceDetailBO, Map<Long, SubProductVo> dueSubProductVoMap) {
        try {
            log.info("CombinationProductUtil.dealAppOrderDetailAfterSaleCompose afterSaleRecordVO: {}, orderFuseFinanceDetailBO: {}, dueSubProductVoMap: {}", afterSaleRecordVO, orderFuseFinanceDetailBO, JSON.toJSONString(dueSubProductVoMap));
            Map<Long, OrderFuseFinanceDetailBO.ItemFinanceInfo> OrderFuseItemFinanceInfoMap = new HashMap<>();
            orderFuseFinanceDetailBO.getItemInfos().forEach(itemFinanceInfo -> {
                if(Objects.isNull(itemFinanceInfo.getOrderItemId())){
                    return;
                }
                OrderFuseItemFinanceInfoMap.put(itemFinanceInfo.getOrderItemId(), itemFinanceInfo);
            });
            afterSaleRecordVO.getAfterSaleRecordDetailList().forEach(afterDetail -> {
                if(CollectionUtils.isEmpty(afterDetail.getSubProductVoList())){
                    return;
                }
                SubProductVo subProductVo = afterDetail.getSubProductVoList().get(0);
                if(Objects.isNull(subProductVo.getComposeOrderItemId())){
                    return;
                }
                OrderFuseFinanceDetailBO.ItemFinanceInfo itemFinanceInfo = OrderFuseItemFinanceInfoMap.get(subProductVo.getComposeOrderItemId());
                if(Objects.isNull(itemFinanceInfo)){
                    return;
                }
                Map<Long, SubProductVo> skuFinanceComposeDetailMap = getSkuFinanceComposeDetailMap(itemFinanceInfo.getComposeItemFinanceInfos());
                afterDetail.getSubProductVoList().forEach(compose -> {
                    Long serviceId = compose.getServiceId();
                    if(Objects.isNull(serviceId)){
                        log.info("CombinationProductUtil.dealAppOrderDetailAfterSaleCompose serviceId is null compose: {}", compose);
                        return;
                    }
                    // 正单数据
                    SubProductVo dueSubProductVo = dueSubProductVoMap.get(serviceId);
                    if(Objects.nonNull(dueSubProductVo)){
                        compose.setName(dueSubProductVo.getName());
                        compose.setSpecification(dueSubProductVo.getSpecification());
                        compose.setErpCode(dueSubProductVo.getErpCode());
                        compose.setSkuId(dueSubProductVo.getSkuId());
                    }
                    SubProductVo financeSubProductVo = skuFinanceComposeDetailMap.get(serviceId);
                    if(Objects.isNull(financeSubProductVo)){
                        log.info("CombinationProductUtil.dealAppOrderDetailAfterSaleCompose financeSubProductVo is null");
                        return;
                    }
                    if(StringUtils.isBlank(compose.getName())){
                        compose.setName(financeSubProductVo.getName());
                    }
                    if(StringUtils.isBlank(compose.getSkuId())){
                        compose.setSkuId(financeSubProductVo.getSkuId());
                    }
                    if(StringUtils.isBlank(compose.getErpCode())){
                        compose.setErpCode(financeSubProductVo.getErpCode());
                    }
                    dealSetFinanceComposeData(compose, financeSubProductVo.getTotalPrice(), financeSubProductVo.getSalePrice(), financeSubProductVo.getQuantity());
                });

            });
        }catch (Exception e){
            log.info("CombinationProductUtil.dealAppOrderDetailAfterSaleCompose is error orderId: {},  e= ", orderFuseFinanceDetailBO.getOrderId(), e);
        }
    }

    private static Map<Long, SubProductVo> getSkuFinanceComposeDetailMap(List<OrderFuseFinanceDetailBO.ComposeItemFinanceInfo> composeItemFinanceInfos) {
        Map<Long, SubProductVo> map = new HashMap<>();
        try {
            if(CollectionUtils.isEmpty(composeItemFinanceInfos)){
                return map;
            }
            composeItemFinanceInfos.forEach(item -> {
                Long orderItemId = item.getOrderItemId();
                if(Objects.isNull(orderItemId)){
                    log.info("getSkuFinanceComposeDetailMap orderItemId is null");
                    return;
                }
                SubProductVo vo = new SubProductVo();
                // 接数据已处理过
                vo.setQuantity(formatThreeDecimal(item.getQuantity()));
                vo.setSalePrice(formatTWoDecimalAmt(item.getSalePrice()));
                vo.setTotalPrice(formatTWoDecimalAmt(item.getItemSaleAmt()));
                vo.setWeight(formatThreeDecimal(item.getWeight()));
                vo.setSkuId(item.getSkuCode());
                vo.setName(item.getSkuName());
                vo.setErpCode(item.getErpItemCode());
                vo.setServiceId(orderItemId);
                map.put(orderItemId, vo);
            });
        }catch (Exception e){
            log.info("CombinationProductUtil.getSkuFinanceComposeDetailMap is error composeItemFinanceInfos: {},  e= ", composeItemFinanceInfos, e);
        }
        return map;
    }

    // 统一管理财务需要赋值的字段
    private static void dealSetFinanceComposeData(SubProductVo compose, String itemSaleAmt, String salePrice, String refundQuantity) {
        compose.setTotalPrice(formatTWoDecimalAmt(itemSaleAmt));
        compose.setSalePrice(formatTWoDecimalAmt(salePrice));
        compose.setQuantity(formatThreeDecimal(refundQuantity));
    }

    /**
     * 处理App售后列表 增加组合品明细
     * @param subProductVoList 退单记录组合子商品
     * @param dueSubProductVoList 正单组合品子商品
     * @param composeItemFinanceInfos settlement组合品子商品
     */
    public static void dealAppRefundSaleAfterRecordCompose(List<SubProductVo> subProductVoList, List<SubProductVo> dueSubProductVoList, List<OrderFuseFinanceDetailBO.ComposeItemFinanceInfo> composeItemFinanceInfos) {
        try {
            log.info("CombinationProductUtil.dealAppRefundSaleAfterRecordCompose subProductVoList: {}, dueSubProductVoList: {}, composeItemFinanceInfos: {}", subProductVoList, dueSubProductVoList, JSON.toJSONString(composeItemFinanceInfos));
            if(CollectionUtils.isEmpty(dueSubProductVoList) || CollectionUtils.isEmpty(composeItemFinanceInfos)){
                return;
            }
            Map<Long, SubProductVo> dueSubProductVoMap = dueSubProductVoList.stream().filter(item -> Objects.nonNull(item.getServiceId())).collect(Collectors.toMap(SubProductVo::getServiceId, value -> value));
            Map<Long, SubProductVo> financeSubProductVoMap = getSkuFinanceComposeDetailMap(composeItemFinanceInfos);
            subProductVoList.forEach(subProductVo -> {
                Long serviceId = subProductVo.getServiceId();
                if(Objects.isNull(serviceId) || serviceId==0){
                    log.info("CombinationProductUtil.dealAppRefundSaleAfterRecordCompose subProductVo serviceId is null or serviceId = 0");
                    return;
                }
                SubProductVo dueSubProductVo = dueSubProductVoMap.get(serviceId);
                //正单取值数据
                if(Objects.nonNull(dueSubProductVo)){
                    subProductVo.setSpecification(dueSubProductVo.getSpecification());
                    subProductVo.setName(dueSubProductVo.getName());
                    subProductVo.setErpCode(dueSubProductVo.getErpCode());
                    subProductVo.setSkuId(dueSubProductVo.getSkuId());
                    subProductVo.setPicUrl(dueSubProductVo.getPicUrl());
                    subProductVo.setMultiPicUrl(dueSubProductVo.getMultiPicUrl());
                }
                SubProductVo financeSubProductVo = financeSubProductVoMap.get(serviceId);
                //赋值财务数据
                subProductVo.setQuantity(STRING_ZERO);
                subProductVo.setTotalPrice(STRING_TWO_DECIMAL);
                if(Objects.nonNull(financeSubProductVo)){
                    if(StringUtils.isBlank(subProductVo.getName())){
                        subProductVo.setName(financeSubProductVo.getName());
                    }
                    if(StringUtils.isBlank(subProductVo.getSkuId())){
                        subProductVo.setSkuId(financeSubProductVo.getSkuId());
                    }
                    if(StringUtils.isBlank(subProductVo.getErpCode())){
                        subProductVo.setErpCode(financeSubProductVo.getErpCode());
                    }
                    dealSetFinanceComposeData(subProductVo, financeSubProductVo.getTotalPrice(), financeSubProductVo.getSalePrice(), financeSubProductVo.getQuantity());
                }
            });
        }catch (Exception e){
            log.info("CombinationProductUtil.dealAppRefundSaleAfterRecordCompose is error subProductVoList: {}, e= ",JSON.toJSONString(subProductVoList), e);
        }

    }

    //转换三位小数 重量数量用 小数有零抹掉
    public static String formatThreeDecimal(Object value) {
        try {
            if(value instanceof String){
                String v = (String) value;
                return new DecimalFormat("0.###").format(Double.parseDouble(v));
            }

            if(value instanceof Double) {
                Double v = (Double) value;
                return new DecimalFormat("0.###").format(v);
            }

            if(value instanceof Integer){
                int v = (Integer) value;
                double dou = v;
                return new DecimalFormat("0.###").format(dou);
            }

            if(value instanceof BigDecimal){
                BigDecimal v = (BigDecimal) value;
                return new DecimalFormat("0.###").format(v.doubleValue());
            }

        }catch (Exception e){
            return "0";
        }
        return "0";
    }

    //转换两位小数 不够0填充 金额用
    public static String formatTWoDecimalAmt(Object value) {
        try {
            if(value instanceof String){
                String v = (String)value;
                return new DecimalFormat("0.00").format(Double.parseDouble(v));
            }

            if(value instanceof Double){
                Double v = (Double) value;
                return new DecimalFormat("0.00").format(value);
            }

            if(value instanceof Integer){
                Integer v = (Integer) value;
                return new DecimalFormat("0.00").format(BigDecimal.valueOf(v).doubleValue());
            }
        }catch (Exception e){
            return "0.00";
        }
        return "0.00";
    }
}
