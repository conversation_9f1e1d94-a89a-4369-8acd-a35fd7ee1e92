package com.sankuai.shangou.qnh.orderapi.utils.app.login;

import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;

import java.util.concurrent.Callable;

public abstract class IdentityInfoCallable<T> implements Callable<T> {

    private IdentityInfo identityInfo;

    private SessionInfo sessionInfo;



    public IdentityInfoCallable() {
        this.identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
    }

    public IdentityInfoCallable(IdentityInfo identityInfo, SessionInfo sessionInfo) {
        this.identityInfo = identityInfo;
        this.sessionInfo = sessionInfo;
    }

    @Override
    public T call() throws Exception {
        ApiMethodParamThreadLocal.getInstance().set(this.identityInfo);
        SessionContext.init(sessionInfo);
        return doCall();
    }

    protected abstract T doCall();

}
