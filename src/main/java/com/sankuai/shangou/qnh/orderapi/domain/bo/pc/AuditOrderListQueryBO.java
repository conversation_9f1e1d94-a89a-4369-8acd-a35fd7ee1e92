package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsCancelAuditOrderListReq;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.CancelAuditOrderListReq;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.AuditOrderQueryRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.MultiShopAuditOrderQueryRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/8 11:12
 * @Description:
 */
@Setter
@Getter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
public class AuditOrderListQueryBO {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 收货人姓名
     */
    private String receiveName;


    /**
     * 收货人电话
     */
    private String receivePhone;

    /**
     * 收货人电话后四位
     */
    private String receiverPrivacyPhoneLastFourDigits;


    /**
     * 门店名称
     */
    private String poiName;


    /**
     * 门店编码
     */
    private String poiId;


    private List<Long> poiIds;

    /**
     * 退款标识
     */
    private String refundType;


    /**
     * 开始时间
     */
    private Date createStartTime;


    /**
     * 结束时间
     */
    private Date createEndTime;


    /**
     *  申请取消的开始时间
     */
    private Date cancelStartTime;


    /**
     *  申请取消的结束时间
     */
    private Date cancelEndTime;


    /**
     * 渠道列表
     */
    private List<Integer> channelIds;

    /**
     * 申请人
     */
    private int applicant;


    /**
     *
     */
    private int page;

    /**
     *
     */
    private int pageSize;

    /**
     * 渠道流水号
     */
    private String orderSerialNumber;

    /**
     * 仓库id
     */
    private List<Long> warehouseIds;

    /**
     * 客户端来源
     */
    private String clientType;

    /**
     * 订单标识列表
     */
    private List<Integer> orderMarks;



    public AuditOrderListQueryBO(AuditOrderQueryRequest request) {

        createStartTime = ConverterUtils.parseStartDate(request.getCreateStartTime());
        createEndTime = ConverterUtils.parseEndDate(request.getCreateEndTime());

        cancelStartTime = ConverterUtils.parseStartDate(request.getCancelStartTime());
        cancelEndTime = ConverterUtils.parseEndDate(request.getCancelEndTime());

        receiveName = request.getReceiverName();
        receiverPrivacyPhoneLastFourDigits = request.getReceiverPhone();

        poiIds = ConverterUtils.nonNullConvert(request.getPoiId(), p -> Arrays.asList(Long.valueOf(p)));
        poiName = request.getPoiName();

        refundType = request.getRefundType();

        channelIds = CollectionUtils.isNotEmpty(request.getChannelIds()) ? ConverterUtils.convertList(request.getChannelIds(), Integer::valueOf)
                : DynamicChannelType.getAllChannelIds();

        applicant = ConverterUtils.nonNullConvert(request.getApplicant(), Integer::valueOf, 0);

        page = request.getPage();
        pageSize = request.getPageSize();

        orderSerialNumber = request.getOrderSerialNumber();
    }

    public AuditOrderListQueryBO(MultiShopAuditOrderQueryRequest request) {

        createStartTime = ConverterUtils.parseStartDate(request.getCreateStartTime());
        createEndTime = ConverterUtils.parseEndDate(request.getCreateEndTime());

        cancelStartTime = ConverterUtils.parseStartDate(request.getCancelStartTime());
        cancelEndTime = ConverterUtils.parseEndDate(request.getCancelEndTime());

        receiveName = request.getReceiverName();
        receiverPrivacyPhoneLastFourDigits = request.getReceiverPhone();

        poiIds = ConverterUtils.convertList(request.getPoiIdList(), Long::valueOf);
        poiName = request.getPoiName();

        refundType = request.getRefundType();

        channelIds = CollectionUtils.isNotEmpty(request.getChannelIds()) ? ConverterUtils.convertList(request.getChannelIds(), Integer::valueOf)
                : DynamicChannelType.getAllChannelIds();

        applicant = ConverterUtils.nonNullConvert(request.getApplicant(), Integer::valueOf, 0);

        page = request.getPage();
        pageSize = request.getPageSize();

        orderSerialNumber = request.getOrderSerialNumber();
        orderMarks = request.getOrderMarks();
    }


    /**
     * 转换为三方请求对象
     * @return
     */
    public OcmsCancelAuditOrderListReq toOcmsCancelAuditReq() {
        OcmsCancelAuditOrderListReq req = new OcmsCancelAuditOrderListReq();

        req.setBeginCreateTime(ConverterUtils.nonNullConvert(createStartTime, Date::getTime,DateUtils.addYears(new Date(),-1).getTime()));
        req.setEndCreateTime(ConverterUtils.nonNullConvert(createEndTime, Date::getTime, System.currentTimeMillis()));

        req.setCancelBeginTime(ConverterUtils.nonNullConvert(cancelStartTime, Date::getTime, DateUtils.addYears(new Date(),-1).getTime()));
        req.setCancelEndTime(ConverterUtils.nonNullConvert(cancelEndTime, Date::getTime, System.currentTimeMillis()));

        req.setChannelIdList(channelIds);
        req.setRefundTagIdList(ConverterUtils.nonNullConvert(refundType, r -> Arrays.asList(Integer.valueOf(r))));
        req.setReceiverName(receiveName);
        req.setReceiverPhone(receivePhone);
        req.setReceiverPhoneLastFourDigits(receiverPrivacyPhoneLastFourDigits);
        req.setShopName(poiName);
        req.setShopIdList(poiIds);

        if (applicant != 0) {
            req.setWhoApplyTypeList(Arrays.asList(applicant));
        }


        req.setTenantId(tenantId);

        req.setPage(page);
        req.setPageSize(pageSize);
        req.setOrderSerialNumber(orderSerialNumber);
        req.setWarehouseIdList(warehouseIds);
        req.setClientType(clientType);
        req.setOrderMarkList(orderMarks);
        return req;
    }

    /**
     * 转换为三方请求对象
     * @return
     */
    public CancelAuditOrderListReq toCancelAuditReq() {
        CancelAuditOrderListReq req = new CancelAuditOrderListReq();

        req.setBeginCreateTime(ConverterUtils.nonNullConvert(createStartTime, Date::getTime,DateUtils.addYears(new Date(),-1).getTime()));
        req.setEndCreateTime(ConverterUtils.nonNullConvert(createEndTime, Date::getTime, System.currentTimeMillis()));

        req.setCancelBeginTime(ConverterUtils.nonNullConvert(cancelStartTime, Date::getTime, DateUtils.addYears(new Date(),-1).getTime()));
        req.setCancelEndTime(ConverterUtils.nonNullConvert(cancelEndTime, Date::getTime, System.currentTimeMillis()));

        req.setChannelIdList(channelIds);
        req.setRefundTagIdList(ConverterUtils.nonNullConvert(refundType, r -> Arrays.asList(Integer.valueOf(r))));
        req.setReceiverName(receiveName);
        req.setReceiverPhone(receivePhone);
        req.setReceiverPhoneLastFourDigits(receiverPrivacyPhoneLastFourDigits);
        req.setShopName(poiName);
        req.setShopIdList(poiIds);

        if (applicant != 0) {
            req.setWhoApplyTypeList(Arrays.asList(applicant));
        }


        req.setTenantId(tenantId);

        req.setPage(page);
        req.setPageSize(pageSize);
        req.setOrderSerialNumber(orderSerialNumber);
        return req;
    }


}
