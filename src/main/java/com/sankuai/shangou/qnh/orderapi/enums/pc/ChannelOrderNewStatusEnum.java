package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Optionable;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 * @Author: <EMAIL>
 * @Date: 2022/12/03 19:54
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum ChannelOrderNewStatusEnum implements Optionable {

    ALL(0,"全部"),
    PAYED(1, "待接单"),
    MERCHANT_CONFIRMED(11, "待拣货"),
    PICKED(13, "拣货完成"),
    WAIT_SELF_FETCH(35, "待自提"),
    SHIPPING(14, "配送中"),
    SHIPPING_ABNORMAL(99, "配送异常"),
    COMPLETED(15, "已完成"),
    CANCELING(22, "退单待审核"),
    CANCELED(25, "已取消"),
    ;

    /**
     * 编码
     */
    private int code;

    /**
     * 描述
     */
    private String desc;


    @Override
    public List<UiOption> toOptions() {
        return Arrays.asList(ChannelOrderNewStatusEnum.values()).stream()
                .map(e -> new UiOption(String.valueOf(e.getCode()),e.getDesc())).collect(Collectors.toList());
    }


    public static ChannelOrderNewStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }

        for (ChannelOrderNewStatusEnum e : ChannelOrderNewStatusEnum.values()) {
            if (e.getCode() == code && code != 0) {
                return e;
            }
        }

        return null;
    }

    public static boolean isNotNeedQueryOldStatus(Integer code){
        Set<Integer> needQueryStatusSet = Sets.newHashSet();
        needQueryStatusSet.add(ChannelOrderNewStatusEnum.PICKED.getCode());
        needQueryStatusSet.add(ChannelOrderNewStatusEnum.SHIPPING.getCode());
        needQueryStatusSet.add(ChannelOrderNewStatusEnum.SHIPPING_ABNORMAL.getCode());
        needQueryStatusSet.add(ChannelOrderNewStatusEnum.WAIT_SELF_FETCH.getCode());
        return needQueryStatusSet.contains(code);
    }


    public static List<Integer> getShippingAbnormalStatus() {
        return Lists.newArrayList(SHIPPING_ABNORMAL.getCode());
    }

}
