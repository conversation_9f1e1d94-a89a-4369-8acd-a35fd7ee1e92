package com.sankuai.shangou.qnh.orderapi.utils.store;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 日期工具类
 * <AUTHOR>
 *
 **/
@Slf4j
public class DateUtils {

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss SSS";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYYMMDD = "yyyyMMdd";
    public static final String MM_DD_BY_DOT = "MM.dd";
    public static final String START_TIME_SUFFIX = " 00:00:00";
    public static final String END_TIME_SUFFIX = " 23:59:59";


    /**
     * 将时间字符串转为Date
     *
     * @param dateStr 日期字符串
     *
     * @param format 日期格式
     * @return 给定字符串描述的日期对象。
     */
    public static Date parse(String dateStr, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date date = null;
        try {
            date = sdf.parse(dateStr);
        } catch (ParseException e) {
            log.error("date format error, dateStr:{}, format:{}", dateStr, format, e);
        }
        return date;
    }


    /**
     * 根据给定的格式与时间(Date类型的)，返回时间字符串
     *
     * @param date 指定的日期
     * @param format 日期格式字符串
     * @return String 指定格式的日期字符串.
     */
    public static String format(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    /**
     * 获取日期开始时间 当日00:00:00.000 对应时间戳
     * @param date
     * @return
     */
    public static Date getDayBeginTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取日期结束时间 当日23:59:59.999 对应时间戳
     * @param date
     * @return
     */
    public static Date getDayEndTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 校验日期格式
     * @param format
     */
    public static boolean isValidDate(String str, String format) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        boolean convertSuccess = true;
        FastDateFormat fdf = FastDateFormat.getInstance(format);
        try {
            fdf.parse(str);
        } catch (ParseException e) {
            convertSuccess=false;
        }
        return convertSuccess;
    }

    /**
     * 格式化日期
     * @param date 日期
     * @param format 格式
     * @return 日期字符串
     */
    public static String format(LocalDate date, String format) {

        if (date == null) {
            return null;
        }

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        return dateTimeFormatter.format(date);
    }

    /**
     * 获取指定格式日期字符串
     * @param dateStr
     * @param originalFormat
     * @param targetFormat
     * @return
     */
    public static String format(String dateStr, String originalFormat, String targetFormat) {

        if (StringUtils.isEmpty(dateStr)) {
            return StringUtils.EMPTY;
        }

        DateTimeFormatter originalFormatter = DateTimeFormatter.ofPattern(originalFormat);
        DateTimeFormatter targetFormatter = DateTimeFormatter.ofPattern(targetFormat);

        LocalDate originalDate = LocalDate.parse(dateStr, originalFormatter);
        return targetFormatter.format(originalDate);
    }

    /**
     * 获取两个日期之间的所有日期
     * @param beginDateStr 开始日期
     * @param endDateStr 结束日期
     * @param format 格式
     * @return 间隔天数
     *
     * 举例: beginDateStr = "2020-03-01" , endDateStr = "2020-03-02"
     * 返回结果: [2020-02-27, 2020-02-28, 2020-02-29, 2020-03-01]
     */
    public static List<String> getBetweenDateList(String beginDateStr, String endDateStr, String format) {
        List<String> list = new ArrayList<>();
        LocalDate beginDate;
        LocalDate endDate;
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        try {
            beginDate = LocalDate.parse(beginDateStr, dateTimeFormatter);
            endDate = LocalDate.parse(endDateStr, dateTimeFormatter);
        } catch (Exception e) {
            throw new IllegalArgumentException("日期格式错误");
        }

        if (beginDate.compareTo(endDate) > 0) {
            throw new IllegalArgumentException("开始日期不能大于结束日期");
        }

        long betweenDays = ChronoUnit.DAYS.between(beginDate, endDate);
        for (long i = 0; i <= betweenDays; i++) {
            LocalDate localDate = beginDate.plusDays(i);
            list.add(dateTimeFormatter.format(localDate));
        }
        return list;
    }
}

