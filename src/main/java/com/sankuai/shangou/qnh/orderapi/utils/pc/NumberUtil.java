package com.sankuai.shangou.qnh.orderapi.utils.pc;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/12/18
 **/
public class NumberUtil {

    public static Double add(Double first, Double second){
        return BigDecimal.valueOf(Optional.ofNullable(first).orElse(0D) + Optional.ofNullable(second).orElse(0D))
                .setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    public static Double subtract(Double first, Double second){
        return BigDecimal.valueOf(Optional.ofNullable(first).orElse(0D) - Optional.ofNullable(second).orElse(0D))
                .setScale(2, RoundingMode.HALF_UP).doubleValue();
    }
}
