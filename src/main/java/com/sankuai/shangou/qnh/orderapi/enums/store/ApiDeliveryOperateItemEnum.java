package com.sankuai.shangou.qnh.orderapi.enums.store;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryOperateItemEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public enum ApiDeliveryOperateItemEnum {

    //1取消2转自配送3已送出4联系骑手
    MANUAL_THIRD_PART(101, "手动发三方配送", "发三方配送"),
    RETRY_LAUNCH(102, "重发配送", "重发配送"),
    EXCEPTION_TO_THIRD_PART(103, "平台配送异常转三方配送", "发三方配送"),
    EXCEPTION_TO_SELF(104, "平台配送异常转商家自配送", "自己送"),

    RETRY_LAUNCH_BY_MALTFARM(105, "麦芽田重发配送", "重发配送"),
    RETRY_LAUNCH_BY_HAIKUI(106, "海葵重发配送", "重发配送"),

    DELIVERY_TO_SELF(107,"转自配送","转自配送"),

    DELIVERY_TO_MALTFARM(108,"转麦芽田","转麦芽田"),

    DELIVERY_TO_DAP(109,"转青云智送","转青云智送"),


    ADD_TIP_FEE(110,"加小费","加小费"),
    PAOTUI_CANCEL_DELIVERY(111,"取消配送","取消配送"),
    RECALL_DELIVERY(112,"再次呼叫","再次呼叫"),
    REPORT_EXCEPTION(113,"异常上报","异常上报"),
    AUDIT_EXCEPTION(114,"异常审核","异常审核"),



    DELIVERY_TO_FOUR_WHEEL(117,"转汽车配送","转汽车配送"),
    APPEND_FOUR_WHEEL_TYPE(118,"追加车型","追加汽车配送车型"),
    CANCELING(900, "取消中", "取消中"),
    CANCEL_DELIVERY(901, "取消配送", "取消配送"),


    ;


    /**
     * code
     */
    public final Integer type;
    /**
     * 实际的场景
     */
    public final String scene;
    /**
     * 按钮描述
     */
    public final String iconDesc;

    ApiDeliveryOperateItemEnum(Integer type, String scene, String iconDesc) {
        this.type = type;
        this.scene = scene;
        this.iconDesc = iconDesc;
    }

    public static ApiDeliveryOperateItemEnum tmsItemToOperateItem(Integer code){
        if(code==null){
            return null;
        }
        DeliveryOperateItemEnum codeEnum = DeliveryOperateItemEnum.TRANS_SELF_DELIVERY.codeToEnum(code);
        if(codeEnum ==null){
            return null;
        }
        switch (codeEnum){
            case TRANS_SELF_DELIVERY:
                return ApiDeliveryOperateItemEnum.DELIVERY_TO_SELF;
            case TRANS_MALT_FARM:
                return ApiDeliveryOperateItemEnum.DELIVERY_TO_MALTFARM;
            case TRANS_DAP_DELIVERY:
                return ApiDeliveryOperateItemEnum.DELIVERY_TO_DAP;
            case ADD_TIP_FEE:
                return ApiDeliveryOperateItemEnum.ADD_TIP_FEE;
            case CANCEL_DELIVERY:
                return ApiDeliveryOperateItemEnum.PAOTUI_CANCEL_DELIVERY;
            case RECALL_DELIVERY:
                return ApiDeliveryOperateItemEnum.RECALL_DELIVERY;
            case REPORT_EXCEPTION:
                return ApiDeliveryOperateItemEnum.REPORT_EXCEPTION;
            case AUDIT_EXCEPTION:
                return ApiDeliveryOperateItemEnum.AUDIT_EXCEPTION;
            case TRANS_FOUR_WHEEL_DELIVERY:
                return ApiDeliveryOperateItemEnum.DELIVERY_TO_FOUR_WHEEL;
            case APPEND_FOUR_WHEEL_DELIVERY_TYPE:
                return ApiDeliveryOperateItemEnum.APPEND_FOUR_WHEEL_TYPE;
            default:
                return null;
        }
    }

    public static List<ApiDeliveryOperateItemEnum> tmsItemListToOperateItemList(List<Integer> codeList){
        if(CollectionUtils.isEmpty(codeList)){
            return Collections.emptyList();
        }
        List<ApiDeliveryOperateItemEnum> itemEnumList = new ArrayList<>();
        for (Integer code : codeList){
            ApiDeliveryOperateItemEnum itemEnum = tmsItemToOperateItem(code);
            if(itemEnum!=null){
                itemEnumList.add(itemEnum);
            }
        }
        return itemEnumList;
    }

    public static List<Integer> tmsItemListToOperateItemCodeList(List<Integer> codeList){
        if(CollectionUtils.isEmpty(codeList)){
            return Collections.emptyList();
        }
        List<Integer> itemCodeList = new ArrayList<>();
        for (Integer code : codeList){
            ApiDeliveryOperateItemEnum itemEnum = tmsItemToOperateItem(code);
            if(itemEnum!=null){
                itemCodeList.add(itemEnum.getType());
            }
        }
        return itemCodeList;
    }

    public Integer getType() {
        return type;
    }
}
