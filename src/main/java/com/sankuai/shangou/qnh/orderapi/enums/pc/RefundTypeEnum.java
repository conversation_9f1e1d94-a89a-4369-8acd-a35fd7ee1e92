package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.google.common.collect.Lists;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Optionable;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import lombok.AllArgsConstructor;
import lombok.Getter;


import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 退单类型
 *
 */
@AllArgsConstructor
@Getter
public enum RefundTypeEnum implements Optionable {

    REFUND(10, "仅退款", 0),
    WEIGHT_REFUND(20, "仅退款-克重退差", 3),
    AMOUNT_REFUND(30, "仅退款-按金额退", 4),
    REFUND_GOODS(40, "退货退款", 0),
    REJECT_BY_CUSTOMER(50, "用户拒收", 8),
    APPEAL(101, "申诉单", 9),
    PART_REFUND(110, "仅退款-部分退", 1),
    ALL_REFUND(120, "仅退款-整单退", 2),
    PART_REFUND_GOODS(130, "退货退款-部分退", 5),
    ALL_REFUND_GOODS(140, "退货退款-整单退", 6),
    AMOUNT_REFUND_GOODS(150, "退货退款-按金额退", 7);

    private int code;
    private String desc;
    private int sort;

    public static RefundTypeEnum getByCode(Integer code) {

        if (Objects.isNull(code)) {
            return null;
        }

        for (RefundTypeEnum e : RefundTypeEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }

        return null;
    }

    //后台不展示这两个枚举选项
    private static final List<RefundTypeEnum> noDisplayRefundTypeInSelect = Arrays.asList(RefundTypeEnum.REFUND, RefundTypeEnum.REFUND_GOODS);

    @Override
    public List<UiOption> toOptions() {
        return Arrays.asList(RefundTypeEnum.values()).stream().filter(e -> !noDisplayRefundTypeInSelect.contains(e))
                .sorted(Comparator.comparing(RefundTypeEnum::getSort))
                .map(e -> new UiOption(String.valueOf(e.getCode()), e.getDesc())).collect(Collectors.toList());
    }

    public static RefundTypeEnum enumOf(Integer code) {
        if (code == null) {
            return RefundTypeEnum.REFUND;
        }
        for (RefundTypeEnum thisEnum : RefundTypeEnum.values()) {
            if (thisEnum.getCode() == code) {
                return thisEnum;
            }
        }
        return RefundTypeEnum.REFUND;
    }

    public static List<Integer> getAmountRefundList() {
        return Lists.newArrayList(REFUND.getCode(), WEIGHT_REFUND.getCode(), AMOUNT_REFUND.getCode(), PART_REFUND.getCode(), ALL_REFUND.getCode());
    }

    public static List<Integer> getGoodsRefundList() {
        return Lists.newArrayList(REFUND_GOODS.getCode(), PART_REFUND_GOODS.getCode(), ALL_REFUND_GOODS.getCode(), AMOUNT_REFUND_GOODS.getCode());
    }
}
