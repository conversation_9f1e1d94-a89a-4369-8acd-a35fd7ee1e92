package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.shangou.empower.uwmsplatform.enums.OrderBizType;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderBaseVo;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.Order;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderBase;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderExtend;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.management.client.utils.DesensitizeReceiverInfoUtil;
import com.meituan.shangou.saas.order.management.client.utils.OrderUtil;
import com.meituan.shangou.saas.order.management.client.utils.param.DesensitizeReceiverInfoExtParam;
import com.meituan.shangou.saas.order.management.client.utils.result.DesensitizeReceiverInfoResult;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.RefundTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderBaseDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.AfsApplyType;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.converter.pc.ChannelOrderConverterV2;
import com.sankuai.shangou.qnh.orderapi.converter.pc.UwmsOrderChannelConvert;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ChannelOrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.TagInfoVO;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelOrderRefundTypeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelOrderStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.store.UserTagTypeEnum;
import com.sankuai.shangou.qnh.orderapi.utils.pc.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/2 14:31
 * @Description:
 */
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Slf4j
public class ChannelOrderBO {

    private Long tenantId;

    /**
     * 订单ID
     */
    private String orderId;


    /**
     * 渠道编码
     */
    private int channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     *
     */
    private Long poiId;

    /**
     * 门店名称
     */
    private String poiName;


    /**
     * 配送方式
     */
    private String deliveryMethod;


    /**
     * 数量
     */
    private int quantity;


    /**
     * 订单金额
     */
    private double orderAmt;


    /**
     * 付款金额
     */
    private double paidAmount;


    /**
     * 商家实收金额
     */
    private double merchantAmount;

    /**
     * 配送费
     */
    private double deliveryAmt;

    /**
     * 打包费
     */
    private double packageAmt;

    /**
     * 平台服务费
     */
    private double platformAmt;

    /**
     * 配送员姓名
     */
    private String riderName;

    /**
     * 配送员电话
     */
    private String riderPhone;

    /**
     * 收货人名称
     */
    private String receiverName;

    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 收费人地址
     */
    private String receiverAddress;


    /**
     * 订单状态
     */
    private String status;

    /**
     * 订单状态-OrderBase
     */
    private Integer orderStatus;


    /**
     * 支付方式
     */
    private int payMethod;


    /**
     * 退款类型
     */
    private String refundType;


    /**
     * 创建时间
     */
    private Date createTime;

    private int currentAfsType;

    private String orderType;

    private String memberCard;

    /**
     * 订单流水号
     */
    private long orderSerialNumber;

    private List<Integer> couldOperateItemList;

    /**
     * 商家活动支出
     */
    private Double bizActivityAmt;

    /**
     * 线下总价
     */
    private double offlineTotalAmt;
    /**
     * 销售利润
     */
    private double saleBenefit;

    /**
     * 预计送达时间
     */
    private Date arrivalTime;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 订单用户类型
     */
    private Integer orderUserType;

    /**
     * 订单附加信息，对应extData中的一部分数据
     */
    private OrderExtend orderExtend;

    private Integer deliveryChannelId;


    /**
     * 门店经营模式 1 直营 2 加盟
     */
    private Integer manageMode;

    /**
     * 地推单 1 推广现提  0/null 空
     */
    private Integer pullNewOrder;

    /**
     * 用户身份tag
     */
    private String userTag;

    /**
     * 实际送达时间
     */
    private String finishDeliveryTime;

    /**
     * 骑手牵牛花账号名
     */
    private String riderAccount;


    /**
     * 组织结构二级
     */
    private String parentOrgName;

    /**
     * 组织结构三级
     */
    private String grandParentOrgName;

    /**
     * 订单重量
     */
    private Long deliveryWeight;

    /**
     * 配送导航距离
     */
    private Long deliveryDistance;

    /**
     * 商家实收(加盟）
     */
    private String franchiseeMerchantAmount;

    /**
     * 加盟且灰度门店且未计算完成时返回false
     */
    private Boolean isMerchantAmountCalculated = true;

    /**
     * 剔除代销品的商家活动支出
     */
    private Integer bizActivityAmtWithoutConsignment;

    /**
     * 是否为美团名酒馆订单，true：是
     */
    private Boolean isMtFamousTavern;

    /**
     * 是否为美团发财酒订单，true：是
     */
    private Boolean isFacaiWine;

    private static final Integer IS_CONSIGNMENT = 1;




    @Deprecated
    public ChannelOrderBO(OrderInfoDTO orderListDetail) {
        tenantId = orderListDetail.getTenantId();
        orderId = orderListDetail.getChannelOrderId();
        channelId = orderListDetail.getChannelId();
        channelName = orderListDetail.getChannelName();
        poiId = orderListDetail.getShopId();
        poiName = orderListDetail.getShopName();
        quantity = orderListDetail.getItemCount();
        paidAmount = orderListDetail.getActualPayAmt() / 100.0;
        merchantAmount = orderListDetail.getBizReceiveAmt() / 100.0;
        bizActivityAmt = new BigDecimal(String.valueOf(orderListDetail.getBizActivityAmt())).divide(new BigDecimal("100")).doubleValue();
        offlineTotalAmt = new BigDecimal(String.valueOf(orderListDetail.getOfflineTotalAmt())).divide(new BigDecimal("100")).doubleValue();
        saleBenefit = new BigDecimal(String.valueOf(orderListDetail.getSaleBenefit())).divide(new BigDecimal("100")).doubleValue();
        deliveryMethod = orderListDetail.getDeliveryName();
        receiverName = orderListDetail.getReceiverName();
        receiverPhone = orderListDetail.getReceiverPhone();
        receiverAddress = orderListDetail.getReceiveAddress();
        status = ConverterUtils.nonNullConvert(ChannelOrderStatusEnum.getByCode(orderListDetail.getChannelOrderStatus()), s -> s.getDesc(), orderListDetail.getChannelOrderStatusDesc());
        createTime = new Date(orderListDetail.getCreateTime());
        refundType = ConverterUtils.nonNullConvert(ChannelOrderRefundTypeEnum.getByCode(orderListDetail.getRefundTagId()), s -> s.getDesc(), orderListDetail.getRefundTagDesc());
        couldOperateItemList = orderListDetail.getCouldOperateItemList();
        currentAfsType = orderListDetail.getCurrentAfsType();
        orderType = orderListDetail.getOrderBookingType() == 0 ? "实时订单" : "预约订单";
        memberCard = StringUtils.isNotBlank(orderListDetail.getMemberCardNum()) ? orderListDetail.getMemberCardNum() : "非会员";
        orderSerialNumber = orderListDetail.getOrderSerialNumber();
        arrivalTime = new Date(orderListDetail.getEstimatedSendArriveTimeEnd());
    }

    public ChannelOrderBO(Order order) {
        OrderBase base = order.getOrderBase();
        tenantId = order.getOrderBase().getTenantId();
        orderId = base.getViewOrderId();
        channelId = ChannelOrderConvertUtils.sourceBiz2Mid(base.getOrderBizType());
        channelName = ChannelOrderConvertUtils.getChannelNameByChannelId(channelId);
        poiId = base.getShopId();
        poiName = base.getShopName();
        quantity = base.getItemCount();
        paidAmount = base.getActualPayAmt() / 100.0;
        merchantAmount = base.getBizReceiveAmt() / 100.0;
        if(OrderSourceEnum.GLORY.getValue() != base.getOrderSource()){
            bizActivityAmt = new BigDecimal(String.valueOf(base.getBizActivityAmt())).divide(new BigDecimal("100")).doubleValue();
            if(base.getBizActivityAmtWithoutConsignment() != null) {
                bizActivityAmt = new BigDecimal(String.valueOf(base.getBizActivityAmtWithoutConsignment())).divide(new BigDecimal("100")).doubleValue();
            }
        }
        offlineTotalAmt = new BigDecimal(String.valueOf(base.getOfflineTotalAmt())).divide(new BigDecimal("100")).doubleValue();
        saleBenefit = new BigDecimal(String.valueOf(base.getSaleBenefit())).divide(new BigDecimal("100")).doubleValue();
        deliveryMethod = base.getDistributeMethodName();
        receiverName = base.getReceiverName();
        receiverPhone = base.getReceiverPhone();
        //美团名酒馆商家配送不展示隐私号
        if (BooleanUtils.isTrue(base.getIsMtFamousTavern()) && Objects.equals(0, base.getIsSelfDelivery())) {
            receiverPhone = null;
        }
        // 若使用了隐私号 && 是歪马渠道 && 隐私号不为空
        if (NumberUtils.INTEGER_ONE.equals(base.getUsePrivacyPhone())
                && base.getOrderBizType().equals(OrderBizTypeEnum.MEITUAN_DRUNK_HOURSE.getValue())
                && StringUtils.isNotBlank(base.getReceiverPrivacyPhone())) {
            receiverPhone = base.getReceiverPrivacyPhone();
        }
        receiverAddress = base.getReceiveAddress();

        // 脱敏收货人信息
        desensitizeReceiverInfo(base);

        Integer channelOrderStatus = ChannelOrderConverter.orderStatusBiz2Mid(base.getOrderStatus());
        status = ConverterUtils.nonNullConvert(ChannelOrderStatusEnum.getByCode(channelOrderStatus), s -> s.getDesc(), generateOrderStatusDesc(base));
        orderStatus = base.getOrderStatus();
        createTime = new Date(base.getCreateTime());
        Integer refundTagId = ChannelOrderConverter.refundTypeBiz2Mid(base.getRefundType());
        String refundTagDesc = "";
        if (refundTagId != null) {
            RefundTypeEnum refundTypeEnum = RefundTypeEnum.enumOf(base.getRefundType());
            refundTagDesc = refundTypeEnum != null ? refundTypeEnum.getDesc() : "";
        }
        if (refundTagId != null) {
            refundType = ConverterUtils.nonNullConvert(ChannelOrderRefundTypeEnum.getByCode(refundTagId), s -> s.getDesc(), refundTagDesc);
        }

        currentAfsType = OfflineOrderConstantsHelper.isOnSale(base.getOrderStatus(), base.getDistributeStatus()) ? AfsApplyType.ON_SALE.getValue() : AfsApplyType.AFTER_SALE.getValue();
        orderType = base.getIsBooking() == 0 ? "实时订单" : "预约订单";
        memberCard = StringUtils.isNotBlank(base.getMemberCardNum()) ? base.getMemberCardNum() : "非会员";
        orderSerialNumber = base.getOrderSerialNumber();
        arrivalTime = ConverterUtils.nonNullConvert(base.getArrivalEndTime(), Date::new);
        warehouseId = base.getWarehouseId();
        warehouseName = base.getWarehouseName();
        orderUserType = base.getUserType();
        orderExtend = base.getOrderExtend();
        deliveryChannelId = base.getDeliveryChannelId();
        if (CollectionUtils.isNotEmpty(base.getTags())) {
            TagInfoVO tagInfoVO = CollectionUtils.isNotEmpty(UserTagTypeEnum.getTagList(base.getTags())) ? UserTagTypeEnum.getTagList(base.getTags()).get(0) : null;
            userTag = tagInfoVO != null ? tagInfoVO.getName() : null;
        }
        riderName = base.getRiderName();
        if(base.getOrderExtend() != null) {
            pullNewOrder = Objects.equals(base.getOrderExtend().getSelfPickPullNewOrder(), true) ? 1 : 0;
        }
        if (base.getFinishDeliveryTime() != null) {
            finishDeliveryTime = DateTimeUtils.getDateStrFromLong(base.getFinishDeliveryTime());
        }
        riderAccount = base.getRiderAccountName();
        isMtFamousTavern = base.getIsMtFamousTavern();
        isFacaiWine = base.getIsFacaiWine();
        if(MccConfigUtil.checkIsFranchiseeFeePoi(tenantId, poiId) && Objects.equals(base.getFranchiseeOrder(), CommonConstant.FRANCHISEE_ORDER)) {
            if(base.getSettleAmount() == null) {
                isMerchantAmountCalculated = false;
            } else {
                franchiseeMerchantAmount = MoneyUtils.centToYuan(base.getSettleAmount());

            }
            fixHistoryFeeShow(base);

        }
    }

    private void desensitizeReceiverInfo(OrderBase base){
        try {
            // 订单状态已完结 && 非歪马租户 && 租户支持隐藏隐私号
            if (OrderUtil.isOrderEnd(base.getOrderStatus())
                    && !MccConfigUtil.isDhTenant(tenantId)
                    && DesensitizeReceiverInfoUtil.checkSupportDesensitizeReceiverInfoTenant(tenantId)) {
                // 订单超过时间，需要隐藏隐私号
                if (OrderUtil.isOverConfigTime(base.getFinalOrderEndTime(), DesensitizeReceiverInfoUtil.getDesensitizeReceiverInfoTime())) {
                    receiverPhone = null;
                }
            }

            // 订单收货人信息脱敏处理
            DesensitizeReceiverInfoExtParam receiverInfoExtParam = DesensitizeReceiverInfoExtParam.build(
                    receiverName, receiverAddress, receiverPhone,
                    null, tenantId, base.getOrderStatus(), base.getFinalOrderEndTime());
            DesensitizeReceiverInfoResult desensitizeReceiverInfoResult = DesensitizeReceiverInfoUtil.desensitizeReceiverInfo(receiverInfoExtParam);
            if(Objects.nonNull(desensitizeReceiverInfoResult)) {
                receiverName = desensitizeReceiverInfoResult.getReceiverName();
                receiverAddress = desensitizeReceiverInfoResult.getReceiverAddress();
                receiverPhone = desensitizeReceiverInfoResult.getReceiverPhone();
            }
        }catch (Exception e){
            log.warn("ChannelOrderBO订单收货人信息脱敏处理失败！base: {}", JSON.toJSONString(base), e);
        }
    }



    /**
     * 2024-08-08 01:00:00全量前的加盟标识订单，若无新佣金实收字段则读老字段
     **/
    public void fixHistoryFeeShow(OrderBase base) {
        try {
            if(base == null) {
                return;
            }
            String targetDateTimeStr = "2024-08-08 01:00:00";
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date targetDateTime = sdf.parse(targetDateTimeStr);
            boolean isBefore = createTime.before(targetDateTime);
            if(isBefore && base.getSettleAmount() == null){
                isMerchantAmountCalculated = true;
            }
        } catch (Exception e) {
            log.error("fixHistoryFeeShow error", e);
        }
    }


    private String generateOrderStatusDesc(OrderBase orderBase) {
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.enumOf(orderBase.getOrderStatus());
        DistributeStatusEnum distributeStatusEnum = null;
        if (Objects.nonNull(orderBase.getDistributeStatus())) {
            distributeStatusEnum = DistributeStatusEnum.enumOf(orderBase.getDistributeStatus());
        }
        String desc = "";
        if (orderStatusEnum != null) {
            switch (orderStatusEnum) {
                case SUBMIT:
                    desc = orderStatusEnum.getDesc();
                    break;
                case PICKING:
                case MERCHANT_CONFIRMED:
                    if (distributeStatusEnum != null && distributeStatusEnum.getValue() > DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER.getValue()) {
                        desc = distributeStatusEnum.getDesc();
                    }
                    break;
                default:
                    desc = orderStatusEnum.getDesc();

            }
            //兜底-取订单状态
            if (org.apache.commons.lang.StringUtils.isBlank(desc)) {
                desc = orderStatusEnum.getDesc();
            }
        }
        return org.apache.commons.lang.StringUtils.defaultIfBlank(desc, "未知");
    }

    public static boolean isOnSale(int offlineOrderStatus, int distributeStatus) {
        return offlineOrderStatus <= OfflineOrderConstantsHelper.OfflineOrderStatus.FULFILLMENT && distributeStatus < DistributeStatusEnum.RIDER_TAKE_GOODS.getValue();
    }


    public ChannelOrderBO(OrderBaseDTO orderBase) {
        orderId = orderBase.getChannelOrderId();
        channelName = orderBase.getChannelName();
        poiName = orderBase.getShopName();

        orderAmt = orderBase.getOriginalAmt() / 100.0;
        paidAmount = orderBase.getActualPayAmt() / 100.0;
        merchantAmount = orderBase.getBizReceiveAmt() / 100.0;
        platformAmt = orderBase.getPlatformFee() / 100.0;
        deliveryAmt = orderBase.getFreight() / 100.0;
        packageAmt = orderBase.getPackageAmt() / 100.0;

        deliveryMethod = orderBase.getDeliveryMethodName();
        payMethod = orderBase.getPayMethod();
        receiverName = orderBase.getReceiverName();
        receiverPhone = orderBase.getReceiverPhone();
        receiverAddress = orderBase.getReceiveAddress();
        status = ConverterUtils.nonNullConvert(ChannelOrderStatusEnum.getByCode(orderBase.getChannelOrderStatus()), s -> s.getDesc(), orderBase.getChannelOrderStatusDesc());
        createTime = new Date(orderBase.getCreateTime());
        riderName = orderBase.getDeliveryUserName();
        riderPhone = orderBase.getDeliveryUserPhone();
        refundType = ConverterUtils.nonNullConvert(ChannelOrderRefundTypeEnum.getByCode(orderBase.getLastAfterSaleApplyRefundTagId()), s -> s.getDesc(), "");
        currentAfsType = orderBase.getCurrentAfsType();
        orderType = orderBase.getOrderBookingType() == 0 ? "实时订单" : "预约订单";
        memberCard = StringUtils.isNotBlank(orderBase.getMemberCardNum()) ? orderBase.getMemberCardNum() : "非会员";
        quantity = orderBase.getItemCount();

    }

    public ChannelOrderBO(OrderBaseVo orderBase) {
        orderId = orderBase.getChannelOrderId();
        channelName = orderBase.getChannelName();
        poiName = orderBase.getShopName();
        channelId = orderBase.getChannelId();
        orderAmt = orderBase.getOriginalAmt() / 100.0;
        paidAmount = orderBase.getActualPayAmt() / 100.0;
        merchantAmount = orderBase.getBizReceiveAmt() / 100.0;
        platformAmt = orderBase.getPlatformFee() / 100.0;
        deliveryAmt = orderBase.getFreight() / 100.0;
        packageAmt = orderBase.getPackageAmt() / 100.0;
        orderSerialNumber = orderBase.getOrderSerialNumber();
        deliveryMethod = orderBase.getDeliveryMethodName();
        payMethod = orderBase.getPayMethod();
        receiverName = orderBase.getReceiverName();
        receiverPhone = orderBase.getReceiverPhone();
        receiverAddress = orderBase.getReceiveAddress();
        status = ConverterUtils.nonNullConvert(ChannelOrderStatusEnum.getByCode(orderBase.getChannelOrderStatus()), s -> s.getDesc(), orderBase.getChannelOrderStatusDesc());
        createTime = new Date(orderBase.getCreateTime());
        riderName = orderBase.getDeliveryUserName();
        riderPhone = orderBase.getDeliveryUserPhone();
        refundType = ConverterUtils.nonNullConvert(ChannelOrderRefundTypeEnum.getByCode(orderBase.getLastAfterSaleApplyRefundTagId()), s -> s.getDesc(), "");
        currentAfsType = orderBase.getCurrentAfsType();
        orderType = orderBase.getOrderBookingType() == 0 ? "实时订单" : "预约订单";
        memberCard = StringUtils.isNotBlank(orderBase.getMemberCardNum()) ? orderBase.getMemberCardNum() : "非会员";
        quantity = orderBase.getItemCount();
        warehouseId = orderBase.getWarehouseId();
        warehouseName = orderBase.getWarehouseName();
    }


    public ChannelOrderVO toChannelOrderVO() {
        ChannelOrderVO channelOrderVO = new ChannelOrderVO();
        channelOrderVO.setChannelName(channelName);
        channelOrderVO.setChannelId(String.valueOf(channelId));
        channelOrderVO.setOrderId(orderId);
        channelOrderVO.setPoiId(poiId);
        channelOrderVO.setPoiName(poiName);
        channelOrderVO.setPaidAmount(ConverterUtils.formatMoney(paidAmount));
        channelOrderVO.setMerchantAmount(ConverterUtils.formatMoney(merchantAmount));
        channelOrderVO.setReceiverName(receiverName);
        channelOrderVO.setReceiverPhone(receiverPhone);
        channelOrderVO.setReceiverAddress(receiverAddress);
        channelOrderVO.setQuantity(quantity);
        channelOrderVO.setStatus(status);
        channelOrderVO.setCreateTime(DateFormatUtils.format(createTime, Constants.DateFormats.SHOW_FORMAT));
        channelOrderVO.setDeliveryMethod(deliveryMethod);
        if(MccConfigUtil.isDhTenant(tenantId) && DeliveryChannelUtils.isDapDeliveryChannel(deliveryChannelId)) {
            channelOrderVO.setDeliveryMethod("青云配送");
        }
        if(MccConfigUtil.isDhTenant(tenantId) && (deliveryChannelId == null || DeliveryChannelUtils.isMerchantDeliveryChannel(deliveryChannelId) ) ) {
            channelOrderVO.setDeliveryMethod("自营配送");
        }
        channelOrderVO.setRefundType(refundType);
        channelOrderVO.setCouldOperateItemList(ConverterUtils.convertList(couldOperateItemList, String::valueOf));
        channelOrderVO.setCurrentAfsType(String.valueOf(currentAfsType));
        channelOrderVO.setOrderType(orderType);
        channelOrderVO.setMemberCard(memberCard);
        channelOrderVO.setOrderSerialNumber(orderSerialNumber);
        channelOrderVO.setBizActivityAmt(bizActivityAmt == null ? "-" : ConverterUtils.formatMoney(bizActivityAmt));
        channelOrderVO.setOfflineTotalAmt(ConverterUtils.formatMoney(offlineTotalAmt));
        channelOrderVO.setSaleBenefit(ConverterUtils.formatMoney(saleBenefit));
        channelOrderVO.setArrivalTime(ConverterUtils.nonNullConvert(arrivalTime, t -> DateFormatUtils.format(t, Constants.DateFormats.SHOW_FORMAT)));
        channelOrderVO.setWarehouseId(warehouseId);
        channelOrderVO.setWarehouseName(warehouseName);
        channelOrderVO.setOrderUserType(orderUserType);
        channelOrderVO.setManageMode(manageMode);
        channelOrderVO.setPullNewOrder(pullNewOrder);
        channelOrderVO.setUserTag(userTag);
        channelOrderVO.setFinishDeliveryTime(finishDeliveryTime);
        channelOrderVO.setRiderAccount(riderAccount);
        channelOrderVO.setRiderName(riderName);
        channelOrderVO.setParentOrgName(parentOrgName);
        channelOrderVO.setGrandParentOrgName(grandParentOrgName);
        channelOrderVO.setDeliveryDistance(deliveryDistance);
        channelOrderVO.setDeliveryWeight(deliveryWeight);
        // 医药特有逻辑，将渠道信息重新映射到真实的渠道上面
        remapMedicineChannelInfo(channelOrderVO);
        channelOrderVO.setIsMerchantAmountCalculated(isMerchantAmountCalculated);
        if(franchiseeMerchantAmount != null || isMerchantAmountCalculated == false) {  //加盟实收有值或加盟实收为计算中，则实收取加盟实收
            channelOrderVO.setMerchantAmount(franchiseeMerchantAmount);
        }
        channelOrderVO.setIsMerchantAmountCalculated(isMerchantAmountCalculated);
        channelOrderVO.setIsMtFamousTavern(isMtFamousTavern);
        channelOrderVO.setIsFacaiWine(isFacaiWine);
        //设置密钥，开发票的时候使用
        channelOrderVO.setToken(OrderUtils.generateMD5Token(orderId, channelId, tenantId));
        return channelOrderVO;
    }

    private void remapMedicineChannelInfo(ChannelOrderVO channelOrderVO) {
        if (this.orderExtend == null || this.orderExtend.medicineRealOrderChannel == null || this.orderExtend.getMedicineRealOrderChannel() <= 0) {
            return;
        }

        OrderBizType realOrderBizType = UwmsOrderChannelConvert.INSTANCE.convertUwmsOrderChannelCode2OrderBizType(this.orderExtend.getMedicineRealOrderChannel());

        if (Objects.equals(OrderBizType.UN_KNOW, realOrderBizType)) {
            return;
        }

        OrderBizTypeEnum centerOrderTypeEnum = OrderBizTypeEnum.enumOf(realOrderBizType.getCode());

        if (centerOrderTypeEnum == null || Objects.equals(OrderBizTypeEnum.UN_KNOW, centerOrderTypeEnum)) {
            return;
        }

        Integer channelId = ChannelOrderConvertUtils.sourceBiz2Mid(centerOrderTypeEnum.getValue());
        String channelName = ChannelOrderConvertUtils.getChannelNameByChannelId(channelId);
        channelOrderVO.setRealMedicineOrderChannelId(String.valueOf(channelId));
        channelOrderVO.setRealMedicineOrderChannelName(channelName);
    }


    /**
     * 转换成导出Excel用的map
     *
     * @return
     */
    public HashMap<String, String> toExcelMap() {

        HashMap<String, String> map = new HashMap<>(20);

        map.put("订单号", String.valueOf(orderId));
        map.put("门店", poiName);
        map.put("渠道", channelName);
        map.put("收货人姓名", receiverName);
        map.put("收货人地址", receiverAddress);
        map.put("收货人电话", receiverPhone);
        map.put("订单总金额", ConverterUtils.formatMoney(orderAmt));
        map.put("订单实付金额", ConverterUtils.formatMoney(paidAmount));
        map.put("商家实收金额", ConverterUtils.formatMoney(merchantAmount));
        map.put("配送费", ConverterUtils.formatMoney(deliveryAmt));
        map.put("餐盒费", ConverterUtils.formatMoney(packageAmt));
        map.put("平台服务费", ConverterUtils.formatMoney(platformAmt));
        map.put("配送员姓名", riderName);
        map.put("配送员手机号", riderPhone);
        map.put("会员卡号", memberCard);
        map.put("订单类型", orderType);
        map.put("是否在线支付", ChannelOrderDetailBO.ONLINE_PAYMETHODS.contains(payMethod) ? "是" : "否");
        map.put("订单状态", status);
        map.put("订单创建时间", DateFormatUtils.format(createTime, Constants.DateFormats.SHOW_FORMAT));
        map.put("商品数量", String.valueOf(quantity));
        return map;

    }


}
