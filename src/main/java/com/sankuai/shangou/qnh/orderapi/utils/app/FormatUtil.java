package com.sankuai.shangou.qnh.orderapi.utils.app;

import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/13
 */
public class FormatUtil {
    public static String formatViewOrderIds(List<String> viewOrderIds) {
        if(CollectionUtils.isEmpty(viewOrderIds)) {
            return "";
        }
        StringBuilder viewOrderIdStringBuilder = new StringBuilder();
        for (String viewOrderId : viewOrderIds) {
            viewOrderIdStringBuilder.append("'");
            viewOrderIdStringBuilder.append(viewOrderId);
            viewOrderIdStringBuilder.append("',");
        }
        return viewOrderIdStringBuilder.deleteCharAt(viewOrderIdStringBuilder.length() - 1).toString();
    }

    /**
     * 获取末尾num位的字符
     * @param str
     * @param num
     * @return
     */
    public static String getLastNumCharacters(String str, Integer num) {
        // 确保字符串不为空且长度至少为4
        if (str != null && str.length() >= num) {
            return str.substring(str.length() - num);
        } else {
            // 如果字符串长度小于4，或者是空，可以返回整个字符串或者null/空字符串
            // 这取决于你希望如何处理这种情况
            return str;
        }
    }
}
