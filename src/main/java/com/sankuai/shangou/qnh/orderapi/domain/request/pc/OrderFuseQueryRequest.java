package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2023/1/2 11:38
 * @Description:
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(
        description = "订单分页查询请求"
)
public class OrderFuseQueryRequest extends PageRequest implements BaseRequest {

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人地址
     */
    private String receiverAddress;

    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 订单创建开始时间
     */
    private String createStartTime;

    /**
     * 订单创建结束时间
     */
    private String createEndTime;

    /**
     * 预计送达开始时间
     */
    private String arrivalStartTime;

    /**
     * 预计送达结束时间
     */
    private String arrivalEndTime;

    /**
     * 订单完成时间开始点，毫秒
     */
    private Long completeTimeStart;

    /**
     * 订单完成时间结束点，毫秒
     */
    private Long completeTimeEnd;

    /**
     * 核销时间开始点，毫秒
     */
    private Long posTimeStart;

    /**
     * 核销时间结束点，毫秒
     */
    private Long posTimeEnd;

    /**
     * 状态
     */
    private List<Integer> status;


    /**
     * 渠道列表
     */
    private List<String> channelIds;


    /**
     * 退款标识
     */
    private List<String> refundTypes;


    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 是否是会员
     */
    private String hasMemberCard;

    /**
     * 会员卡号
     */
    private String memberCard;

    /**
     * 流水号
     */
    @FieldDoc(
            description = "流水号"
    )
    private String orderSerialNumber;

    /**
     * 摊位ID
     */
    @FieldDoc(
            description = "摊位ID集合"
    )
    private List<Long> boothIds;

    /**
     * 问题订单(1进货价为空)
     */
    @FieldDoc(
            description = "问题订单(1进货价为空)"
    )
    private Integer orderProblemType;

    /**
     * 门店ID集合
     */
    private List<Long> poiIdList;

    /**
     * 仓库id
     */
    private List<Long> warehouseIdList;

    /**
     * 核销状态列表
     */
    public List<String> writeOffStatus;

    /**
     * 订单标识列表
     */
    public List<Integer> orderMarks;

    /**
     * 自配送类型列表
     */
    private List<Integer> deliveryChannelIds;

    /**
     * 平台配送类型列表
     */
    private List<Integer> originalDistributeTypes;

    /**
     * 融合订单状态
     */
    public List<String> fuseOrderStatus;

    /**
     * 配送方式
     */
    private Integer distributeMethod;

    /**
     * 导出字段
     */
    public List<String> exportFields;


    /**
     * 渠道订单ID
     */
    private List<String> viewOrderIdList;

    /**
     * 订单标识字符串列表
     */
    public List<String> orderMarkStrList;




    @Override
    public void selfCheck() {
        if (StringUtils.isNotEmpty(orderId)) {
            Integer limitLength = LionUtils.getOrderParamsLengthLimitConfig();
            AssertUtil.isTrue(orderId.length() <= limitLength && orderId.length() >= 4, "订单号最多输入" + limitLength + "位，最少4位字符串");
        }

        if (StringUtils.isNotEmpty(orderSerialNumber)) {
            AssertUtil.isTrue(orderSerialNumber.length() <= 10, "流水号最多输入10个数字");
        }

        if (StringUtils.isNotEmpty(receiverPhone)) {
            AssertUtil.isTrue(receiverPhone.length() <= 25, "顾客手机号最多输入25个数字");
        }

        if (StringUtils.isNotEmpty(skuName)) {
            AssertUtil.isTrue(skuName.length() <= 25, "商品名称最多输入25个字符");
        }

        if (StringUtils.isNotEmpty(receiverName)) {
            AssertUtil.isTrue(receiverName.length() <= 10, "顾客名称最多输入10个字符");
        }


        if (StringUtils.isNotEmpty(receiverAddress)) {
            AssertUtil.isTrue(receiverAddress.length() <= 25, "顾客地址最多输入25个字符");
        }

        if (StringUtils.isNotEmpty(memberCard)) {
            AssertUtil.isTrue(memberCard.length() <= 25, "会员卡号最多输入25位数字");
        }

    }
}
