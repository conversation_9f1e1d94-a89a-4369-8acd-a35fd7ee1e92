package com.sankuai.shangou.qnh.orderapi.enums.pc;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: <EMAIL>
 * @class: ChannelCommentStatEnum
 * @date: 2019-08-06 17:16:56
 * @desc:
 */
@Getter
@AllArgsConstructor
public enum ChannelCommentStatEnum {
    GOOD_COMMENT_COUNT("好评总数"),              //好评总数
    GOOD_COMMENT_RATE("好评率"),               //好评率=好评总数/评价总数
    BAD_COMMENT_COUNT("差评总数"),              //差评总数
    BAD_COMMENT_RATE("差评率"),               //差评率=差评总数/评价总数
    NOT_REPLY_BAD_COMMENT_COUNT("未回复差评数"),    //未回复差评数
    NOT_REPLY_BAD_COMMENT_RATE("未回复差评率"),     //未回复差评率=未回复差评数/差评数
    TOTAL_COMMENT_COUNT("评价总数"),            //评价总数
    NOT_REPLY_COMMENT_COUNT("未回复评价数");         //未回复评价数

    private String desc;

}
