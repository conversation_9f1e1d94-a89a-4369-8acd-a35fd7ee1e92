package com.sankuai.shangou.qnh.orderapi.enums.pc;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/24
 * 调整类型枚举
 **/
public enum AdjustTargetItemEnum {

    //enum
    BOOTH_ID(1, "摊位id"),
    OFFLINE_PRICE(2, "商品进货价");


    private int code;

    private String desc;

    AdjustTargetItemEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static AdjustTargetItemEnum enumOf(int code) {
        for (AdjustTargetItemEnum status : AdjustTargetItemEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

}
