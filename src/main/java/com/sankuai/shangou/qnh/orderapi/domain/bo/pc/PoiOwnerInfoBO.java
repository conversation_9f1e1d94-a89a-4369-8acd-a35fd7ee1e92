package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiOwnerDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/11/25 17:05
 * Description: 门店责任人信息BO
 */
@Data
public class PoiOwnerInfoBO {

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 门店Id
     */
    private Long poiId;

    /**
     * 责任人Uid
     */
    private Long ownerUid;

    /**
     * 责任人姓名
     */
    private String ownerName;

    /**
     * 责任人mis号
     */
    private String ownerMis;

    public static PoiOwnerInfoBO buildFromPoiOwnerDTO(PoiOwnerDTO ownerDTO) {
        PoiOwnerInfoBO poiOwnerBO = new PoiOwnerInfoBO();
        poiOwnerBO.setTenantId(ownerDTO.getTenantId());
        poiOwnerBO.setPoiId(ownerDTO.getPoiId());
        poiOwnerBO.setOwnerUid(ownerDTO.getOwnerUid());
        poiOwnerBO.setOwnerName(ownerDTO.getOwnerName());
        poiOwnerBO.setOwnerMis(ownerDTO.getOwnerMis());
        return poiOwnerBO;
    }
}
