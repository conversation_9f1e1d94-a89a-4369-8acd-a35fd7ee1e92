package com.sankuai.shangou.qnh.orderapi.enums.pc;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/24
 * 权限类型枚举
 **/
public enum OptTypeEnum {


    //enum
    OFFLINE(1, "停用"),

    ONLINE(2, "启用"),

    DELETE(3, "删除");


    private static Map<Integer, Integer> statusOptMapping = Maps.newHashMap();

    static {
        statusOptMapping.put(AccountRoleStatusEnum.ONLINE.getCode(), ONLINE.getCode());
        statusOptMapping.put(AccountRoleStatusEnum.OFFLINE.getCode(), OFFLINE.getCode());
    }


    /**
     * 获取状态对应的操作
     *
     * @param status
     * @return
     */
    public static Integer getOpt(Integer status) {
        return statusOptMapping.get(status);
    }

    private int code;

    private String desc;

    OptTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }


}
