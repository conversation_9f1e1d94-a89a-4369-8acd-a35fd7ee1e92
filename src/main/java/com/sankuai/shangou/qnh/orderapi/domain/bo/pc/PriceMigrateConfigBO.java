package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * author: guo<PERSON><PERSON>@meituan.com
 * date: 2020-02-13 16:14:21
 *
 * desc: 价格迁移灰度MCC配置对象
 */
@Data
@NoArgsConstructor
public class PriceMigrateConfigBO {

    /**
     * 价格模块迁移开关配置，若迁移模块未设置开关，则表示所有模块走新接口
     */
    private Map<String, PriceMigrateConfigDetailBO> priceModelSwitchMap;

}
