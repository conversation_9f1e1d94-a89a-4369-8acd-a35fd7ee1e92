package com.sankuai.shangou.qnh.orderapi.remote;

import org.apache.commons.collections4.CollectionUtils;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiRelationThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.constants.PoiFields;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiRelationTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiListQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiRelationQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiSearchRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiRelationSimpleMapResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiSearchResponse;
import com.sankuai.meituan.reco.store.management.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.PoiResp;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：<EMAIL>
 * @Date: 2018/9/30 下午5:20
 */
@Component
@Slf4j
public class PoiRemoteService {

    @Autowired
    private PoiThriftService poiThriftService;

    @Resource
    private ConfigThriftService configThriftService;

    @Autowired
    private PoiRelationThriftService poiRelationThriftService;

    public HashMap<Long, PoiResp> batchQuery(List<Long> poiIds) {
        if (poiIds == null || poiIds.size() == 0) {
            return new HashMap<>();
        }
        PoiMapResponse response = poiThriftService.queryTenantPoiInfoMapByPoiIds(poiIds, ContextHolder.currentUserTenantId());
        log.info("poiIds:{},result:{}", poiIds, response);
        if (response.status.code != 0) {
            throw new BizException(response.status.getMessage());
        }
        HashMap<Long, PoiResp> map = Maps.newHashMap();
        if (!response.poiInfoMap.isEmpty()) {
            for (Long poiId : response.poiInfoMap.keySet()) {
                PoiResp poi = new PoiResp();
                poi.setPoiId(poiId);
                poi.setPoiName(response.poiInfoMap.get(poiId).getPoiName());
                poi.setMerChantCode(response.poiInfoMap.get(poiId).getPayMerchantId());
                poi.setDepId(response.poiInfoMap.get(poiId).getDepartmentId());
                poi.setOutPoiId(response.getPoiInfoMap().get(poiId).getOutPoiId());
                map.put(poiId, poi);
            }
        }

        return map;
    }

    public Map<Long, PoiInfoDto> queryPoiByIds(Long tenantId, List<Long> poiIds) {
        if (CollectionUtils.isEmpty(poiIds)) {
            return new HashMap<>();
        }

        PoiMapResponse poiListResponse = RpcInvoker.invoke(() -> poiThriftService.queryTenantPoiInfoMapByPoiIds(poiIds, tenantId));
        ResponseHandler.checkResponseAndStatus(poiListResponse, r -> r.getStatus().getCode(), r -> r.getStatus().getMessage());

        return poiListResponse.getPoiInfoMap();
    }

    /**
     * 综合条件查询 POI 信息
     * 可通过 entityTypes 参数过滤需要的 poi 类型， entityTypes 为空时，默认只查询门店（entityType = 3）
     * 可通过 excludeShareableWarehouseBindingStore 参数 排除绑定共享前置仓的门店
     *
     * @param request PoiListQueryRequest
     * @return List<PoiInfoDto>
     */
    public List<PoiInfoDto> queryPoiListWithCondition(PoiListQueryRequest request) {
        PoiListResponse poiListResponse = poiThriftService.queryPoiInfoListByCondition(request);

        ResponseHandler.checkResponseAndStatus(poiListResponse, r -> r.getStatus().getCode(), r -> r.getStatus().getMessage());

        return poiListResponse.getPoiList();

    }

    /**
     * 批量查询 POI 关联关系，仅返回关联的 poi id
     *
     * @param tenantId        租户 id
     * @param relationType    关系类型 {@link PoiRelationTypeEnum}
     * @param poiIds          poi id 列表
     * @param reverseRelation 是否反转关系查询
     *                        例如 {@link PoiRelationTypeEnum#STORE_SHAREABLE_WAREHOUSE_RELATION} 反向表示传入共享前置仓查询关联的门店
     * @return List<PoiRelationDto>
     */
    public Map<Long, List<Long>> queryPoiRelationMapByPoiIds(long tenantId, PoiRelationTypeEnum relationType, List<Long> poiIds, Boolean reverseRelation) {
        if (CollectionUtils.isEmpty(poiIds)) {
            return Collections.emptyMap();
        }

        PoiRelationQueryRequest relationQueryRequest = new PoiRelationQueryRequest();
        relationQueryRequest.setTenantId(tenantId);
        relationQueryRequest.setPoiIdList(poiIds);
        relationQueryRequest.setRelationType(relationType.code());
        relationQueryRequest.setReverseRelation(reverseRelation);

        PoiRelationSimpleMapResponse response = RpcInvoker.invoke(() -> poiRelationThriftService.batchQueryRelationMapByPoiIds(relationQueryRequest));
        ResponseHandler.checkResponseAndStatus(response, r2 -> r2.getStatus().getCode(), r2 -> r2.getStatus().getMessage());

        return response.getPoiRelationMap();
    }

    /**
     * 查询共享前置仓关联的门店 poi id
     *
     * @param tenantId 租户 id
     * @param poiIds   共享前置仓 poi id
     * @return List<PoiRelationDto>
     */
    public Map<Long, List<Long>> queryShareableWarehouseRelatedStoreId(long tenantId, List<Long> poiIds) {
        return queryPoiRelationMapByPoiIds(tenantId, PoiRelationTypeEnum.STORE_SHAREABLE_WAREHOUSE_RELATION, poiIds, Boolean.TRUE);
    }

    public Map<Long, String> queryShopId2ErpShopCode(Long tenantId, Long shopId) {
        if (Objects.isNull(shopId) || Objects.isNull(tenantId)) {
            return Maps.newHashMap();
        }
        boolean hasErp = isErpStore(tenantId);
        log.info("tenantId :{}, hasErp:{}", tenantId, hasErp);
        if(!hasErp){
            return Maps.newHashMap();
        }
        PoiSearchRequest request = new PoiSearchRequest();
        request.setTenantId(tenantId);
        request.setFields(new HashSet<>(Arrays.asList(PoiFields.POI_ID, PoiFields.OUT_POI_ID)));
        request.setPoiIdList(Arrays.asList(shopId));
        PoiSearchResponse response = poiThriftService.search(request);
        if (Objects.isNull(response) || org.apache.commons.collections.CollectionUtils.isEmpty(response.getPoiList())) {
            return Maps.newHashMap();
        }
        return response.getPoiList().stream().collect(Collectors.toMap(PoiInfoDto::getPoiId, PoiInfoDto::getOutPoiId, (v1, v2) -> v2));
    }

    public Map<Long, String> queryShopId2ErpShopCodes(Long tenantId, List<Long> shopIdList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(shopIdList) || Objects.isNull(tenantId)) {
            return Maps.newHashMap();
        }
        boolean hasErp = isErpStore(tenantId);
        log.info("tenantId :{}, hasErp:{}", tenantId, hasErp);
        if(!hasErp){
            return Maps.newHashMap();
        }
        PoiSearchRequest request = new PoiSearchRequest();
        request.setTenantId(tenantId);
        request.setFields(new HashSet<>(Arrays.asList(PoiFields.POI_ID, PoiFields.OUT_POI_ID)));
        request.setPoiIdList(shopIdList);
        PoiSearchResponse response = poiThriftService.search(request);
        if (Objects.isNull(response) || org.apache.commons.collections.CollectionUtils.isEmpty(response.getPoiList())) {
            return Maps.newHashMap();
        }
        return response.getPoiList().stream().collect(Collectors.toMap(PoiInfoDto::getPoiId, PoiInfoDto::getOutPoiId, (v1, v2) -> v2));
    }

    public boolean isErpStore(Long tenantId) {
        ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
        configQueryRequest.setTenantId(tenantId);
        configQueryRequest.setSubjectId(tenantId);
        configQueryRequest.setConfigId(ConfigItemEnum.HAS_ERP.getKey());
        TenantConfigResponse tenantConfigResponse = configThriftService.queryTenantConfig(configQueryRequest);
        log.info("queryTenantConfig tenantId:{},resp:{}", tenantId, tenantConfigResponse);
        if (Objects.nonNull(tenantConfigResponse)
                && Objects.nonNull(tenantConfigResponse.getStatus())
                && ResultCodeEnum.SUCCESS.getCode() == tenantConfigResponse.getStatus().getCode()) {
            ConfigDto config = tenantConfigResponse.getConfig();
            if (Objects.nonNull(config) && org.apache.commons.lang3.StringUtils.isNotBlank(config.getConfigContent())) {
                String configContent = config.getConfigContent();
                return ConfigItemEnum.HAS_ERP.isMainConfigYesStr(configContent);
            }
        }
        return Boolean.FALSE;
    }

    public Map<Long, String> queryShopId2ErpShopCode(Long tenantId, List<Long> shopIdList) {
        if(org.apache.commons.collections.CollectionUtils.isEmpty(shopIdList) || Objects.isNull(tenantId)){
            return Maps.newHashMap();
        }
        boolean hasErp = isErpStore(tenantId);
        log.info("tenantId :{}, hasErp:{}", tenantId, hasErp);
        if(!hasErp){
            return Maps.newHashMap();
        }
        PoiSearchRequest request = new PoiSearchRequest();
        request.setTenantId(tenantId);
        request.setFields(new HashSet<>(Arrays.asList(PoiFields.POI_ID, PoiFields.OUT_POI_ID)));
        request.setPoiIdList(shopIdList);
        try {
            PoiSearchResponse response = poiThriftService.search(request);
            if (Objects.isNull(response) || org.apache.commons.collections.CollectionUtils.isEmpty(response.getPoiList())) {
                return Maps.newHashMap();
            }
            return response.getPoiList().stream().collect(Collectors.toMap(PoiInfoDto::getPoiId, PoiInfoDto::getOutPoiId, (v1, v2) -> v2));
        } catch (Exception e) {
            return Maps.newHashMap();
        }
    }

    @MethodLog(logRequest = true, logResponse = true)
    public Map<Long, PoiInfoDto> queryPoiInfosByPoiIds(Set<Long> poiIds) {
        if (CollectionUtils.isEmpty(poiIds)) {
            return Collections.emptyMap();
        }
        PoiMapResponse response = poiThriftService.queryPoiInfoMapByPoiIds(new ArrayList<>(poiIds));
        if (response == null || response.getStatus() == null || !response.getStatus().isSuccess()) {
            throw new IllegalArgumentException("illegal poiIds");
        }

        if (CollectionUtils.isNotEmpty(poiIds.stream().filter(poiId -> !response.getPoiInfoMap().containsKey(poiId)).collect(Collectors.toList()))) {
            throw new IllegalArgumentException("illegal poiIds");
        }
        return response.getPoiInfoMap();
    }
}
