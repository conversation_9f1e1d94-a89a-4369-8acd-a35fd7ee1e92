package com.sankuai.shangou.qnh.orderapi.enums.pc;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 退单日志枚举
 */
@AllArgsConstructor
@Getter
public enum AfterSaleOpEnum {


    CREATE(1, "生成退单时间"),
    FIRST_AUDIT(10, "退单初审时间"),
    COMPLETE(30, "退款完成时间"),
    CANCEL(40, "退款取消时间"),
    WRITEOFF(50, "核销完成时间"),
    ;


    private int code;

    private String desc;

    public static AfterSaleOpEnum getByCode(Integer code) {

        if (Objects.isNull(code)) {
            return null;
        }

        for (AfterSaleOpEnum e : AfterSaleOpEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }

        return null;
    }


}
