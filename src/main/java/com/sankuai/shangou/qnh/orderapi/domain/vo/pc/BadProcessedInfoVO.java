package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.BadProcessedInfoBO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2020/11/18
 * @email jianglilin02@meituan
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class BadProcessedInfoVO extends CommentVO {
    public String recordId;
    public String merchantPhone;
    public long callDuration;
    public String beginTime;
    public String connectTime;
    public String releaseTime;
    public boolean isMerchantCall;
    public String recordUrl;
    public int callResultEnum;
    private int listenNum;
    private Boolean isValid = true;




    public static BadProcessedInfoVO convertToVO(BadProcessedInfoBO badProcessedInfoBO) {
        CommentVO commentVO = CommentVO.build(badProcessedInfoBO);
        BadProcessedInfoVO vo = new BadProcessedInfoVO();
        BeanUtils.copyProperties(commentVO,vo);
        vo.setRecordId(badProcessedInfoBO.getRecordId());
        vo.setMerchantPhone(badProcessedInfoBO.getMerchantPhone());
        vo.setCallDuration(badProcessedInfoBO.getDuration());
        vo.setBeginTime(badProcessedInfoBO.getBeginTime());
        vo.setConnectTime(badProcessedInfoBO.getConnectTime());
        vo.setReleaseTime(badProcessedInfoBO.getReleaseTime());
        vo.setMerchantCall(badProcessedInfoBO.isMerchantCall());
        vo.setRecordUrl(replaceWithHttp(badProcessedInfoBO.getRecordUrl()));
        vo.setCallResultEnum(badProcessedInfoBO.getCallResultEnum());
        vo.setListenNum(badProcessedInfoBO.getListenNum());
        vo.setIsValid(badProcessedInfoBO.isValid());
        return vo;
    }


    //chrome后续版本会屏蔽https调http的地址
    //没有必要在数据库层面改，数据库保留原始回调的recordUrl，方便降级和追查。
    private static String replaceWithHttp(String rawRecordUrl) {
        if (LionUtils.getCommentProcessRecordUrlFallback()) {
            return rawRecordUrl;
        }

        if (rawRecordUrl.startsWith("https")) {
            return rawRecordUrl;
        }

        return rawRecordUrl.replace("http", "https");

    }
}
