package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.tenant.thrift.dto.channel.ChannelBaseDto;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.ChannelDetailDto;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.ChannelInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigContentDto;
import lombok.Data;

import java.util.List;

@Data
public class ChannelInfoBo {

    /**
     * 渠道ID
     */
    public Integer channelId;

    /**
     * 渠道编码
     */
    public String channelCode;

    /**
     * 渠道名称
     */
    public String channelName;

    /**
     * 渠道类型 1-公有渠道 0-私有渠道 -1 - POS渠道
     */
    public Integer standard;

    public String logo;

    /**
     * 颜色
     */
    public String color;

    /**
     * 渠道状态 0-停用 1-启用
     */
    public Integer status;

    /**
     * 渠道配置
     */
    public List<ConfigContentDto> configs;

    /**
     * 订单域类型
     */
    public Integer orderBizType;

    /**
     * 渠道缩写
     */
    public String channelAbbr;

    /**
     * 渠道打印logo
     */
    public String channelPrintLogo;


    public static ChannelInfoBo fromChannelBaseDto(ChannelBaseDto channelBaseDto) {
        ChannelInfoBo channelInfoBo = new ChannelInfoBo();
        channelInfoBo.setChannelId(channelBaseDto.getChannelId());
        channelInfoBo.setChannelCode(channelBaseDto.getChannelCode());
        channelInfoBo.setChannelName(channelBaseDto.getChannelName());
        channelInfoBo.setStandard(channelBaseDto.getStandard());
        return channelInfoBo;
    }

    public static ChannelInfoBo fromChannelInfoDto(ChannelInfoDto channelInfoDto) {
        ChannelInfoBo channelInfoBo = new ChannelInfoBo();
        channelInfoBo.setChannelId(channelInfoDto.getChannelId());
        channelInfoBo.setChannelCode(channelInfoDto.getChannelCode());
        channelInfoBo.setChannelName(channelInfoDto.getChannelName());
        channelInfoBo.setStandard(channelInfoDto.getStandard());
        channelInfoBo.setLogo(channelInfoDto.getLogo());
        channelInfoBo.setColor(channelInfoDto.getColor());
        channelInfoBo.setStatus(channelInfoDto.getStatus());
        channelInfoBo.setOrderBizType(channelInfoDto.getOrderBizType());
        channelInfoBo.setChannelAbbr(channelInfoDto.getChannelAbbr());
        channelInfoBo.setChannelPrintLogo(channelInfoDto.getChannelPrintLogo());
        return channelInfoBo;
    }

    public static ChannelInfoBo fromChannelDetailDto(ChannelDetailDto channelDetailDto) {
        ChannelInfoBo channelInfoBo = new ChannelInfoBo();
        channelInfoBo.setChannelId(channelDetailDto.getChannelId());
        channelInfoBo.setChannelCode(channelDetailDto.getChannelCode());
        channelInfoBo.setChannelName(channelDetailDto.getChannelName());
        channelInfoBo.setStandard(channelDetailDto.getStandard());
        channelInfoBo.setLogo(channelDetailDto.getLogo());
        channelInfoBo.setColor(channelDetailDto.getColor());
        channelInfoBo.setStatus(channelDetailDto.getStatus());
        channelInfoBo.setConfigs(channelDetailDto.getConfigs());
        channelInfoBo.setOrderBizType(channelDetailDto.getOrderBizType());
        channelInfoBo.setChannelAbbr(channelDetailDto.getChannelAbbr());
        channelInfoBo.setChannelPrintLogo(channelDetailDto.getChannelPrintLogo());
        return channelInfoBo;
    }
}
