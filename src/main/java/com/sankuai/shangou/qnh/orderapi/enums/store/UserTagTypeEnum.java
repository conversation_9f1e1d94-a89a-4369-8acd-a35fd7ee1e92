package com.sankuai.shangou.qnh.orderapi.enums.store;

import java.text.MessageFormat;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.meituan.shangou.saas.o2o.enums.TagModelType;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.TagInfoVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderTagVo;
import com.meituan.shangou.saas.order.platform.enums.OrderTagEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderTagDTO;

/**
 * 标签类型
 *
 * <AUTHOR>
 * @since 2020/12/25 18:20
 */
public enum UserTagTypeEnum {


    /**
     * 收藏
     */
    FAVORITES(OrderTagEnum.FAVORITES.getCode(), "收藏店铺", 1) {
        @Override
        public String getName(String value) {
            if (String.valueOf(1).equals(value)) {
                return this.getTemplate();
            }
            return null;
        }
    },

    /**
     * 订单次数
     */
    ORDER_COUNT(OrderTagEnum.ORDER_COUNT.getCode(), "下单{0}次", 0) {
        @Override
        public String getName(String value) {
            // 订单中心获取的次数是已完成的订单、value实际为已支付状态对应的订单、已完成订单不包含当前订单
            int count = NumberUtils.toInt(value, 0);
            return count < 1 ? "门店新客" : MessageFormat.format(this.getTemplate(), count + 1);
        }
    },
    /**
     * 微商城新客
     */
    DRUNK_HORSE_NEW_USER(TagModelType.DRUNK_HORSE_NEW_CUSTOMER.getCode(), "微商城新客", 2) {
        @Override
        public String getName(String value) {
            if (String.valueOf(1).equals(value)) {
                return this.getTemplate();
            }
            return null;
        }
    };

    /**
     * 根据sort排序
     */
    private static List<UserTagTypeEnum> userTagTypeEnums = Arrays.stream(UserTagTypeEnum.values())
            .sorted(Comparator.comparingInt(UserTagTypeEnum::getSort)).collect(Collectors.toList());

    private int code;

    private int sort;

    private String template;


    UserTagTypeEnum(int code, String template, int sort) {
        this.code = code;
        this.template = template;
        this.sort = sort;
    }


    public int getCode() {
        return code;
    }

    public String getTemplate() {
        return template;
    }

    public int getSort() {
        return sort;
    }

    public String getName(String value) {
        return this.template;
    }

    public static List<TagInfoVO> getTags(List<OrderTagDTO> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return null;
        }
        Map<Integer, OrderTagDTO> tagMap = tags.stream()
                .collect(Collectors.toMap(OrderTagDTO::getType, tag -> tag, (old, current) -> old));
        return userTagTypeEnums.stream()
                .map(userTagTypeEnum -> {
                    OrderTagDTO tag = tagMap.get(userTagTypeEnum.getCode());
                    if (tag == null) {
                        return null;
                    }
                    String name = userTagTypeEnum.getName(tag.getValue());
                    if (StringUtils.isEmpty(name)) {
                        return null;
                    }
                    TagInfoVO tagInfoVO = new TagInfoVO();
                    tagInfoVO.setType(tag.getType());
                    tagInfoVO.setName(name);
                    return tagInfoVO;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static List<TagInfoVO> getTagList(List<OrderTagVo> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return null;
        }
        Map<Integer, OrderTagVo> tagMap = tags.stream()
                .collect(Collectors.toMap(OrderTagVo::getType, tag -> tag, (old, current) -> old));
        return userTagTypeEnums.stream()
                .map(userTagTypeEnum -> {
                    OrderTagVo tag = tagMap.get(userTagTypeEnum.getCode());
                    if (tag == null) {
                        return null;
                    }
                    String name = userTagTypeEnum.getName(tag.getValue());
                    if (StringUtils.isEmpty(name)) {
                        return null;
                    }
                    TagInfoVO tagInfoVO = new TagInfoVO();
                    tagInfoVO.setType(tag.getType());
                    tagInfoVO.setName(name);
                    return tagInfoVO;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
