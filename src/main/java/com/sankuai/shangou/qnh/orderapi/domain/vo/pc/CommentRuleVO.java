package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommentRuleBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 评价规则VO
 *
 * <AUTHOR>
 */
@TypeDoc(
        name = "评价规则VO",
        description = "评价规则VO"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CommentRuleVO {

    @FieldDoc(
            description = "评分规则"
    )
    private List<String> commentLevelRules;

    public static CommentRuleVO build(CommentRuleBO commentRuleBO) {

        List<String> commentLevelRules = Collections.emptyList();

        if (commentRuleBO != null && CollectionUtils.isNotEmpty(commentRuleBO.getCommentLevelRules())) {
            commentLevelRules = commentRuleBO.getCommentLevelRules();
        }

        return new CommentRuleVO(commentLevelRules);
    }
}
