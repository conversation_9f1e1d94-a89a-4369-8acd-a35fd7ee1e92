package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.meituan.shangou.saas.tenant.thrift.common.enums.DepTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.sac.dto.model.SacDataAuthDTO;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.DataAuthKeyDto;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.DataAuthKeyVO;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PermissionGroupVo;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;

import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.DepartmentBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 数据权限key
 * @author: WangSukuan
 * @create: 2020-02-20
 **/
@Data
public class DataAuthKey  {

    @ApiModelProperty(value = "数据权限code", required = true)
    private String dataAuthCode;

    @ApiModelProperty(value = "数据权限类型", required = true)
    private Integer dataAuthType;

    @ApiModelProperty(value = "数据权限名称", required = true)
    private String dataAuthName;

    @ApiModelProperty(value = "所属部门Id")
    private Long dataAuthDepId;

    @ApiModelProperty(value = "数据权限类型和租户组织结构字段值映射")
    private Integer dataAuthDepType;

    /**
     * 租户全部数据权限名称
     */
    public static final String TENANT_DATA_AUTH_NAME = "租户全部";

    /**
     * 租户全部数据权限code
     */
    public static final String TENANT_DATA_AUTH_CODE = "-1";

    /**
     * 租户全部数据权限转换
     */
    public static final String TENANT_DATA_AUTH_CODE_CONVERT = "0";

    public DataAuthKey buildDataAuthKey(PermissionGroupVo permissionGroupVo) {
        this.dataAuthCode = permissionGroupVo.getCode();
        this.dataAuthType = permissionGroupVo.getType();
        this.dataAuthName = permissionGroupVo.getName();
        this.dataAuthDepType = dataAuthToDepTypeMap(permissionGroupVo.getType());
        return this;
    }

    public DataAuthKey buildDataAuthKeyByDataAuthKeyVO(DataAuthKeyVO dataAuthKeyVO) {
        this.dataAuthCode = dataAuthKeyVO.getDataAuthCode();
        this.dataAuthType = dataAuthKeyVO.getDataAuthType();
        this.dataAuthName = dataAuthKeyVO.getDataAuthName();
        this.dataAuthDepType = dataAuthToDepTypeMap(dataAuthKeyVO.getDataAuthType());
        return this;
    }

    public DataAuthKey buildDataAuthKey(PoiInfoDto poiInfoDto) {
        this.dataAuthCode = String.valueOf(poiInfoDto.getPoiId());
        this.dataAuthType = permissionGroupTypeFormEntityType(poiInfoDto.getEntityType()).getValue();
        this.dataAuthName = poiInfoDto.getPoiName();
        this.dataAuthDepId = poiInfoDto.getDepartmentId();
        this.dataAuthDepType = dataAuthToDepTypeMap(DepTypeEnum.POI.getKey());
        return this;
    }

    public DataAuthKey buildDataAuthKey(DepartmentBo departmentBo) {
        this.dataAuthCode = String.valueOf(departmentBo.getDepartmentId());
        this.dataAuthType = PermissionGroupTypeEnum.POI_GROUP.getValue();
        this.dataAuthName = departmentBo.getDepartmentName();
        this.dataAuthDepId = departmentBo.getDepartmentId();
        this.dataAuthDepType = dataAuthToDepTypeMap(DepTypeEnum.POI.getKey());
        return this;
    }

    public DataAuthKey buildTenantDataAuthKey(){

        this.setDataAuthName(TENANT_DATA_AUTH_NAME);
        this.setDataAuthCode(TENANT_DATA_AUTH_CODE);
        this.setDataAuthType(PermissionGroupTypeEnum.POI_GROUP.getValue());
        return this;
    }

    public boolean isPoiType() {
        return Objects.equals(PermissionGroupTypeEnum.POI.getValue(), dataAuthType)
                || Objects.equals(PermissionGroupTypeEnum.SHAREABLE_WAREHOUSE.getValue(), dataAuthType);
    }

    /**
     * 将权限提供的数据权限类型映射到租户提供的部门类型
     * 注意：当新增类型时，这里的映射也需要更新
     *
     * @return DepTypeEnum 部门类型值
     * @see PermissionGroupTypeEnum
     * @see DepTypeEnum
     */
    public static int dataAuthToDepTypeMap(int authPermissionGroup){
        if (Objects.equals(authPermissionGroup, PermissionGroupTypeEnum.POI.getValue())) {
            return DepTypeEnum.POI.getKey();
        } else if (Objects.equals(authPermissionGroup, PermissionGroupTypeEnum.WAREHOUSE.getValue())) {
            return DepTypeEnum.REPO.getKey();
        } else if (Objects.equals(authPermissionGroup, PermissionGroupTypeEnum.BOOTH.getValue())) {
            return DepTypeEnum.BOOTH.getKey();
        } else if (Objects.equals(authPermissionGroup, PermissionGroupTypeEnum.DATA_PERMISSION_GROUP.getValue())
                || Objects.equals(authPermissionGroup, PermissionGroupTypeEnum.POI_GROUP.getValue())) {
            return DepTypeEnum.DEPARTMENT.getKey();
        } else if (Objects.equals(authPermissionGroup, PermissionGroupTypeEnum.SHAREABLE_WAREHOUSE.getValue())) {
            return DepTypeEnum.SHAREABLE_WAREHOUSE.getKey();
        } else {
            // 库区、分组字段值不映射
            return authPermissionGroup;
        }
    }

    /**
     * 从 POI 实体类型得到权限分组
     * 注意：新增类型时这里可能需要补充分支
     *
     * @param entityType POI 实体类型
     * @return 权限分组
     */
    public static PermissionGroupTypeEnum permissionGroupTypeFormEntityType(Integer entityType) {
        if (Objects.equals(entityType, PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code())) {
            // 共享前置仓有单独的权限分组
            return PermissionGroupTypeEnum.SHAREABLE_WAREHOUSE;
        } else if (Objects.equals(entityType, PoiEntityTypeEnum.STORE.code())
                || Objects.equals(entityType, PoiEntityTypeEnum.REGIONAL_WAREHOUSE.code())) {
            // 门店、中心仓都对应到门店
            return PermissionGroupTypeEnum.POI;
        } else {
            throw new BizException("不支持的实体类型 entityType = " + entityType);
        }
    }

    public static List<SacDataAuthDTO> buildSacDataAuthDtosByDataAuthKeyList(List<DataAuthKey> dataAuthKeys){

        List<SacDataAuthDTO> sacDataAuthDtos = new ArrayList<>();
        for (DataAuthKey dataAuthKey : dataAuthKeys){
            SacDataAuthDTO sacDataAuthDTO = buildSacDataAuthDtoByDataAuthKey(dataAuthKey);
            sacDataAuthDtos.add(sacDataAuthDTO);
        }
        return sacDataAuthDtos;

    }

    private static SacDataAuthDTO buildSacDataAuthDtoByDataAuthKey(DataAuthKey dataAuthKey){

        SacDataAuthDTO sacDataAuthDTO = new SacDataAuthDTO();
        sacDataAuthDTO.setSacDataAuthCode(dataAuthKey.getDataAuthCode());
        sacDataAuthDTO.setSacDataAuthName(dataAuthKey.getDataAuthName());
        sacDataAuthDTO.setSacDataAuthType(dataAuthKey.getDataAuthType());
        return sacDataAuthDTO;

    }

    public SacDataAuthDTO buildSacDataAuthDTO() {
        SacDataAuthDTO sacDataAuthDTO = new SacDataAuthDTO();
        sacDataAuthDTO.setSacDataAuthCode(this.getDataAuthCode());
        sacDataAuthDTO.setSacDataAuthType(this.getDataAuthType());
        sacDataAuthDTO.setSacDataAuthName(this.getDataAuthName());
        return sacDataAuthDTO;
    }

    public DataAuthKeyDto convert2DataAuthKeyDto(){
        DataAuthKeyDto dataAuthKeyDto = new DataAuthKeyDto();
        dataAuthKeyDto.setDataAuthCode(this.getDataAuthCode());
        dataAuthKeyDto.setDataAuthName(this.getDataAuthName());
        dataAuthKeyDto.setDataAuthType(this.getDataAuthType());
        return dataAuthKeyDto;
    }

}
