package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.meituan.shangou.saas.order.management.client.dto.request.OrderItemFuseStatisticsDataReq;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.OrderItemFuseStatisticsDataQueryRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: reco_store_saas_e_api
 * @description:
 * @author: jinyi
 * @create: 2023-08-17 19:31
 **/
@Setter
@Getter
@ToString
@NoArgsConstructor
@EqualsAndHashCode
public class OrderItemFuseQueryStatisticsDataBO {

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 融合订单状态
     */
    public List<Integer> fuseOrderStatus;

    /**
     * 渠道列表
     */
    private List<Integer> channelIds;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 门店ID集合
     */
    private List<Long> poiIdList;

    /**
     * 仓库id
     */
    private List<Long> warehouseIdList;

    /**
     * 商品skuid
     */
    private List<String> skuIdList;

    /**
     * 商品条码
     */
    private List<String> upcList;

    /**
     * 线上分类（店内分类）
     */
    private List<String> inStoreCategoryIds;

    /**
     * 订单创建开始时间
     */
    private String createStartTime;

    /**
     * 订单创建结束时间
     */
    private String createEndTime;

    /**
     * 商品erp
     */
    private List<String> erpCodeList;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 页码
     */
    private int page;

    /**
     * 每页显示记录数
     */
    private int pageSize;

    /**
     * 订单标识字符串列表
     */
    private List<String> orderMarkStrList;

    public OrderItemFuseQueryStatisticsDataBO(OrderItemFuseStatisticsDataQueryRequest request) {
        if (StringUtils.isNotBlank(request.getCreateStartTime())
                && StringUtils.isNotBlank(request.getCreateEndTime())) {
            createStartTime = request.getCreateStartTime();
            createEndTime = request.getCreateEndTime();
        }
        channelIds = CollectionUtils.isNotEmpty(request.getChannelIds())
                ? request.getChannelIds().stream().map(Integer::parseInt).collect(Collectors.toList())
                : MccDynamicConfigUtil.ignoreOrderBizTypeConditions();
        skuName = request.getSkuName();
        page = request.getPage();
        pageSize = request.getPageSize();

        if (CollectionUtils.isNotEmpty(request.getFuseOrderStatus())) {
            fuseOrderStatus = request.getFuseOrderStatus().stream().filter(item -> !item.equals("0"))
                    .map(Integer::parseInt).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(request.getPoiIdList())) {
            poiIdList = request.getPoiIdList();
        }
        if (CollectionUtils.isNotEmpty(request.getWarehouseIdList())) {
            warehouseIdList = request.getWarehouseIdList();
        }
        if (CollectionUtils.isNotEmpty(request.getSkuIdList())) {
            skuIdList = request.getSkuIdList();
        }
        if (CollectionUtils.isNotEmpty(request.getErpCodeList())) {
            erpCodeList = request.getErpCodeList();
        }
        if (CollectionUtils.isNotEmpty(request.getUpcList())) {
            upcList = request.getUpcList();
        }
        if (CollectionUtils.isNotEmpty(request.getInStoreCategoryIds())) {
            inStoreCategoryIds = request.getInStoreCategoryIds();
        }
        orderId = request.getOrderId();
        if(CollectionUtils.isNotEmpty(request.getOrderMarkStrList())){
            orderMarkStrList = request.getOrderMarkStrList();
        }
    }

    public OrderItemFuseStatisticsDataReq toOrderItemFuseStatisticsDataReq() {
        OrderItemFuseStatisticsDataReq req = new OrderItemFuseStatisticsDataReq();
        req.setTenantId(tenantId);
        if (StringUtils.isNotBlank(orderId)) {
            req.setViewOrderId(orderId);
        }
        req.setBeginCreateTime(ConverterUtils.nonNullConvert(createStartTime, Long::valueOf, 0L));
        req.setEndCreateTime(ConverterUtils.nonNullConvert(createEndTime, Long::valueOf, 0L));
        if (CollectionUtils.isNotEmpty(channelIds)) {
            req.setOrderBizTypeList(ChannelOrderConvertUtils.sourceMid2BizList(channelIds));
        }
        if (CollectionUtils.isNotEmpty(poiIdList)) {
            req.setShopIdList(poiIdList);
        }
        if (CollectionUtils.isNotEmpty(warehouseIdList)) {
            req.setWarehouseIdList(warehouseIdList);
        }
        if (StringUtils.isNotBlank(skuName)) {
            req.setSkuName(skuName);
        }
        req.setPage(page);
        req.setPageSize(pageSize);
        if (CollectionUtils.isNotEmpty(fuseOrderStatus)) {
            req.setFuseOrderStatusList(fuseOrderStatus);
        }
        if (CollectionUtils.isNotEmpty(skuIdList)) {
            req.setSkuIdList(skuIdList);
        }
        if (CollectionUtils.isNotEmpty(erpCodeList)) {
            req.setErpCodeList(erpCodeList);
        }
        if (CollectionUtils.isNotEmpty(upcList)) {
            req.setBarCodeList(upcList);
        }
        if (CollectionUtils.isNotEmpty(inStoreCategoryIds)) {
            req.setInStoreCategoryIdList(inStoreCategoryIds);
        }
        if(CollectionUtils.isNotEmpty(orderMarkStrList)){
            req.setOrderMarkStrList(orderMarkStrList);
        }
        return req;
    }
}
