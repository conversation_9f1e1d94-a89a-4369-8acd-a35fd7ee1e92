package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@TypeDoc(
        description = "优惠活动信息"
)
@ApiModel("优惠活动信息")
@Data
public class PromotionVO {

    @FieldDoc(
            description = "活动说明", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "活动说明", required = true)
    private String description;

    @FieldDoc(
            description = "活动优惠金额 单位:元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "活动优惠金额 单位:元", required = true)
    private Integer discount;

    @FieldDoc(
            description = "平台承担金额 单位:元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "平台承担金额 单位:元", required = true)
    private Integer platformBearFee;

    @FieldDoc(
            description = "商家承担金额 单位:元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商家承担金额 单位:元", required = true)
    private Integer bizBearFee;

    @FieldDoc(
            description = "代理商承担金额 单位:元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "代理商承担金额 单位:元", required = true)
    private Integer agentBearFee;

    @FieldDoc(
            description = "物流承担金额 单位:元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "物流承担金额 单位:元", required = true)
    private Integer logisticsBearCharge; // required
}
