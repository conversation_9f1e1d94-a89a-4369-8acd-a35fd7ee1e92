package com.sankuai.shangou.qnh.orderapi.utils.pc;

import com.meituan.shangou.saas.tenant.thrift.common.Status;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.sac.dto.response.SacStatus;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.Result;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.CommonResponse;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/26
 * 返回结果处理器
 **/
@Slf4j
public class ResponseHandler {


    public static void handleResult(Result result) {
        if (result.getCode() != ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(result.getCode(), result.getMsg());
        }
    }

    public static void handleSacStatus(SacStatus sacStatus) {
        if (sacStatus.getCode() != ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(sacStatus.getCode(), sacStatus.getMessage());
        }
    }

    public static void handleStatus(Status status) {
        if (status.getCode() != StatusCodeEnum.SUCCESS.getCode()) {
            throw new BizException(status.getCode(), status.getMessage());
        }
    }

    public static void handleStatus(Integer status, String msg) {
        if (status != StatusCodeEnum.SUCCESS.getCode()) {
            throw new BizException(status, msg);
        }
    }

    /**
     * 检查响应结果
     *
     * @param response
     * @param codeSupplier
     * @param msgSupplier
     */
    public static <T> void checkResponseAndStatus(T response, Function<T, Integer> codeSupplier, Function<T, String> msgSupplier) {
        log.info("rpc result:{}", JacksonUtils.toJson(response));
        if (response == null) {
            throw new BizException(ResultCodeEnum.FAIL.getValue(), Constants.ErrorMsg.UNKNOWN_ERROR);
        }

        if (codeSupplier.apply(response) != ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(codeSupplier.apply(response), msgSupplier.apply(response));
        }

    }

    public static void checkResponseAndStatus(CommonResponse response) {
        if (response == null || response.getStatus() == null) {
            throw new BizException(ResultCodeEnum.FAIL.getValue(), Constants.ErrorMsg.UNKNOWN_ERROR);
        }
        if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(response.getStatus().getCode(), response.getStatus().getMsg());
        }
    }

    public static <T> void checkResponseAndStatus(T response, Supplier<Integer> codeSupplier, Supplier<String> msgSupplier) {
        if (response == null) {
            throw new BizException(ResultCodeEnum.FAIL.getValue(), Constants.ErrorMsg.UNKNOWN_ERROR);
        }

        if (codeSupplier.get() != ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(codeSupplier.get(), msgSupplier.get());
        }

    }

    /**
     * 检查响应结果，且返回某些code码时不抛出异常
     *
     * @param response
     * @param codeSupplier
     * @param msgSupplier
     */
    public static <T> void checkResponseAndStatusExcludeSepCodes(T response, Function<T, Integer> codeSupplier, Function<T, String> msgSupplier, List<Integer> sepCodes) {
        if (response == null) {
            throw new BizException(ResultCodeEnum.FAIL.getValue(), Constants.ErrorMsg.UNKNOWN_ERROR);
        }

        if (codeSupplier.apply(response) != ResultCodeEnum.SUCCESS.getValue() && !sepCodes.contains(codeSupplier.apply(response))) {
            throw new BizException(codeSupplier.apply(response), msgSupplier.apply(response));
        }
    }

    /**
     * 检查响应结果是否为空
     *
     * @param response
     */
    public static <T> void checkNullResponse(T response) {
        if (response == null) {
            throw new BizException(ResultCodeEnum.FAIL.getValue(), Constants.ErrorMsg.UNKNOWN_ERROR);
        }
    }

}
