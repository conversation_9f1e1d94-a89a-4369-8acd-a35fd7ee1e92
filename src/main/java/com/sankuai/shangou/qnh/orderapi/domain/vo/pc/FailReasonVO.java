package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020/4/16
 * desc: 失败原因
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class FailReasonVO {

    @FieldDoc(
            description = "渠道ID"
    )
    @ApiModelProperty(value = "渠道ID")
    private Integer channelId;

    @FieldDoc(
            description = "失败原因描述"
    )
    @ApiModelProperty(value = "失败原因描述")
    private String failReasonDescription;
}
