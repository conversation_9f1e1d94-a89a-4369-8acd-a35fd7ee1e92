package com.sankuai.shangou.qnh.orderapi.domain.bo.pc;

import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentDTO;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentItemTagDTO;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.shangou.qnh.orderapi.enums.pc.poi.StatusType;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评价BO
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
public class CommentBO {
    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 中台评论id
     */
    private String commentId;

    /**
     * 渠道评论id
     */
    private String channelCommentId;

    /**
     * 评论内容
     */
    private String commentContent;

    /**
     * 评论时间
     */
    private LocalDateTime commentTime;

    /**
     * 追评内容
     */
    private String addCommentContent;

    /**
     * 追评时间
     */
    private LocalDateTime addCommentTime;

    /**
     * 评论级别
     */
    private String commentLevel;

    /**
     * 订单评分
     */
    private Integer orderScore;

    /**
     * 质量评分
     */
    private Integer qualityScore;

    /**
     * 包装评分
     */
    private Integer packingScore;

    /**
     * 配送评分
     */
    private Integer deliveryScore;

    /**
     * 图片地址
     */
    private List<String> commentPictures;

    /**
     * 配送标签
     */
    private List<String> deliveryCommentLabels;

    /**
     * 配送标签
     */
    private List<String> orderItemList;

    /**
     * 赞商品列表
     */
    private List<String> praiseItemList;

    /**
     * 踩商品列表
     */
    private List<String> criticItemList;

    /**
     * 中台提交评价回复内容
     */
    private String replyDraft;

    /**
     * 中台提交评价回复内容时间
     */
    private LocalDateTime replyDraftTime;

    /**
     * 中台提交评价回复内容操作人id
     */
    private Long replyDraftUid;

    /**
     * 中台提交评价回复内容操作来源
     */
    private String replyDraftSource;

    /**
     * 评论回复内容
     */
    private String replyContent;

    /**
     * 回复时间
     */
    private LocalDateTime replyTime;

    /**
     * 回复状态
     */
    private String replyStatus;

    /**
     * 评论回复过期分钟数
     */
    private Integer commentReplyExpireInMinute;

    /**
     * 是否可以回复
     */
    private Boolean canReply;

    /**
     * 渠道订单编号
     */
    private String channelOrderId;

    /**
     * 商品标签列表
     */
    private List<CommentItemTagBO> itemTagList;

    /**
     * 评价状态
     */
    private Boolean isValid;

    /**
     * 评价溯源系统匹配订单状态
     */
    private String matchOrderStatus;

    /**
     * 评价溯源系统匹配订单id列表
     */
    private List<String> matchChannelOrderIds;

    public static CommentBO build(ChannelCommentDTO commentDTO) {
        if (commentDTO == null) {
            return null;
        }
        CommentBO commentBO = new CommentBO();
        commentBO.setTenantId(commentDTO.getTenantId());
        commentBO.setStoreId(commentDTO.getStoreId());
        commentBO.setChannelId(commentDTO.getChannelId());
        commentBO.setChannelCommentId(commentDTO.getChannelCommentId());
        commentBO.setCommentId(commentDTO.getCommentId());
        commentBO.setCommentContent(commentDTO.getCommentContent());
        commentBO.setCommentTime(StringUtils.isNotEmpty(commentDTO.getCommentTime()) ?
                DateUtil.toLocalDateTime(commentDTO.getCommentTime(), DateUtil.YYYY_MM_DD_HH_MM_SS) : null);
        commentBO.setAddCommentContent(commentDTO.getAddCommentContent());
        commentBO.setAddCommentTime(StringUtils.isNotEmpty(commentDTO.getAddCommentTime()) ?
                DateUtil.toLocalDateTime(commentDTO.getAddCommentTime(), DateUtil.YYYY_MM_DD_HH_MM_SS) : null);
        commentBO.setCommentLevel(commentDTO.getCommentLevel());
        commentBO.setOrderScore(commentDTO.getOrderScore() != -1 ? commentDTO.getOrderScore() : null);
        commentBO.setQualityScore(commentDTO.getQualityScore() != -1 ? commentDTO.getQualityScore() : null);
        commentBO.setPackingScore(commentDTO.getPackingScore() != -1 ? commentDTO.getPackingScore() : null);
        commentBO.setDeliveryScore(commentDTO.getDeliveryScore() != -1 ? commentDTO.getDeliveryScore() : null);
        commentBO.setDeliveryCommentLabels(commentDTO.getDeliveryCommentLabels());
        commentBO.setCommentPictures(commentDTO.getCommentPictures());
        commentBO.setOrderItemList(commentDTO.getOrderItemList());
        commentBO.setPraiseItemList(commentDTO.getPraiseItemList());
        commentBO.setCriticItemList(commentDTO.getCriticItemList());
        commentBO.setReplyDraft(commentDTO.getReplyDraft());
        commentBO.setReplyDraftUid(commentDTO.getReplyDraftUid() != -1 ? commentDTO.getReplyDraftUid() : null);
        commentBO.setReplyDraftTime(StringUtils.isNotEmpty(commentDTO.getReplyDraftTime()) ?
                DateUtil.toLocalDateTime(commentDTO.getReplyDraftTime(), DateUtil.YYYY_MM_DD_HH_MM_SS) : null);
        commentBO.setReplyDraftSource(commentDTO.getReplyDraftSource());
        commentBO.setReplyContent(commentDTO.getReplyContent());
        commentBO.setReplyStatus(commentDTO.getReplyStatus());
        commentBO.setReplyTime(StringUtils.isNotEmpty(commentDTO.getReplyTime()) ?
                DateUtil.toLocalDateTime(commentDTO.getReplyTime(), DateUtil.YYYY_MM_DD_HH_MM_SS) : null);
        commentBO.setCommentReplyExpireInMinute(commentDTO.getCommentReplyExpireInMinute());
        commentBO.setCanReply(commentDTO.getCanReply());
        commentBO.setChannelOrderId(commentDTO.getChannelOrderId());
        commentBO.setItemTagList(ConverterUtils.convertList(commentDTO.getItemTagList(), CommentBO::convertToCommentItemTagBO));
        commentBO.setIsValid(commentDTO.getValid());
        commentBO.setMatchOrderStatus(commentDTO.getMatchOrderStatus());
        commentBO.setMatchChannelOrderIds(commentDTO.getMatchChannelOrderIds());
        return commentBO;
    }

    private static CommentItemTagBO convertToCommentItemTagBO(ChannelCommentItemTagDTO itemTagDTO) {
        CommentItemTagBO itemTagBO = new CommentItemTagBO();
        itemTagBO.setCustomerSkuId(itemTagDTO.getCustomerSkuId());
        itemTagBO.setCustomerSkuName(itemTagDTO.getCustomerSkuName());
        itemTagBO.setItemTags(itemTagDTO.getItemTags());
        return itemTagBO;
    }

}
