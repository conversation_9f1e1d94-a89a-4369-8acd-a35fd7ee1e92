package com.sankuai.shangou.qnh.orderapi.service.common;

import com.dianping.cat.Cat;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSQueryOrderQuantityRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSQueryOrderQuantityResponse;
import com.meituan.shangou.saas.order.management.client.enums.QueryOrderTypeQuantityEnum;
import com.meituan.shangou.saas.order.management.client.enums.WaitToAuditRefundGoodsOrderSubTypeEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.QueryActivateDeliveryOrderCntDetailResponse;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import com.sankuai.shangou.qnh.orderapi.annotation.app.BindAuthCode;
import com.sankuai.shangou.qnh.orderapi.constant.app.CommonConstants;
import com.sankuai.shangou.qnh.orderapi.constant.app.IntegerBooleanConstants;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.DataAuthResourceRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.HomePageRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderHomePageResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.ThirdWaitToDeliverySubTypeCountResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pda.PdaOrderHomePageResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderHomePageModuleVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderPendingTaskVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ThriftPartyModuleVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ThriftPartyTaskVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pda.PdaOrderHomePageModuleVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pda.PdaOrderPendingTaskVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.AuthCodeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.TmsDeliveryStatusDescEnum;
import com.sankuai.shangou.qnh.orderapi.remote.*;
import com.sankuai.shangou.qnh.orderapi.service.common.query.AfterSaleQueryOrderService;
import com.sankuai.shangou.qnh.orderapi.service.common.query.DeliveryErrorQueryOrderService;
import com.sankuai.shangou.qnh.orderapi.service.common.query.Wait2DeliveryQueryOrderService;
import com.sankuai.shangou.qnh.orderapi.service.common.query.Wait2PickQueryOrderService;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.TimeUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.sankuai.shangou.qnh.orderapi.constant.app.IntegerBooleanConstants.BOOLEAN_TRUE;

/**
 * <AUTHOR>
 * @since 2024/7/25
 **/
@Slf4j
@Service
public class HomePageService {
    @Resource
    private AuthRemoteService authThriftWrapper;
    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;
    @Resource
    private TenantBusinessConfigRemoteService tenantBusinessConfigWrapper;
    @Resource
    private TenantRemoteService tenantWrapper;
    @Resource
    private DHThirdDeliveryRemoteService dhThirdDeliveryService;
    @Resource
    private AbnOrderRemoteService abnOrderServiceWrapper;
    @Resource
    private RiderDeliveryRemoteService riderDeliveryRemoteService;
    @Resource
    private Wait2PickQueryOrderService wait2PickQueryOrderService;
    @Resource
    private Wait2DeliveryQueryOrderService wait2DeliveryQueryOrderService;
    @Resource
    private DeliveryErrorQueryOrderService deliveryErrorQueryOrderService;
    @Resource
    private AfterSaleQueryOrderService afterSaleQueryOrderService;

    private final static List<String> authCodeList = Arrays.asList(AuthCodeEnum.ORDER_WAIT_TO_CONFIRM.getAuthCode(),
            AuthCodeEnum.ORDER_WAIT_TO_PICK.getAuthCode(), AuthCodeEnum.WAIT_TO_SELF_FETCH.getAuthCode(), AuthCodeEnum.ORDER_WAIT_TO_DELIVERY.getAuthCode(),
            AuthCodeEnum.ORDER_DELIVERY_ERROR.getAuthCode(), AuthCodeEnum.ORDER_REFUND_AUDIT.getAuthCode(),
            AuthCodeEnum.ORDER_SUB_TAB.getAuthCode(), AuthCodeEnum.PICK_SUB_TAB.getAuthCode(), AuthCodeEnum.ORDER_SEARCH.getAuthCode(),
            AuthCodeEnum.SELF_RIDER_WAIT_TO_GET.getAuthCode(), AuthCodeEnum.SELF_RIDER_WAIT_TO_TAKEGOODS.getAuthCode(),
            AuthCodeEnum.SELF_RIDER_IN_DELIVERY.getAuthCode(), AuthCodeEnum.SELF_RIDER_COMPLETED.getAuthCode(),
            AuthCodeEnum.ORDER_TAB_AFTER_SALES.getAuthCode(), AuthCodeEnum.SELF_RIDER_SELF_PICK_PROMOTE_ORDER.getAuthCode(),
            AuthCodeEnum.THIRD_PARTY_DELIVERY.getAuthCode(), AuthCodeEnum.THIRD_PARTY_WAIT_TO_DELIVERY_AND_EXCEPTION.getAuthCode(),
            AuthCodeEnum.DH_EXCEPTION.getAuthCode(), AuthCodeEnum.WORKBENCH_NEW_TASK_ONLY_DELIVERY.getAuthCode(),  AuthCodeEnum.WORKBENCH_WAIT_TAKE_DELIVERY_TASK.getAuthCode(),
            AuthCodeEnum.WORKBENCH_SELF_RIDER_IN_DELIVERY.getAuthCode(),AuthCodeEnum.WORKBENCH_RIDER_DONE_DELIVERY.getAuthCode(), AuthCodeEnum.WORKBENCH_DELIVERY_SELF_NEW_PICK_DELIVERY.getAuthCode()
    );

    private final static List<String> pdaAuthCodeList = Arrays.asList(AuthCodeEnum.ORDER_WAIT_TO_CONFIRM.getAuthCode(),
            AuthCodeEnum.ORDER_WAIT_TO_PICK.getAuthCode(), AuthCodeEnum.WAIT_TO_SELF_FETCH.getAuthCode(), AuthCodeEnum.ORDER_WAIT_TO_DELIVERY.getAuthCode(),
            AuthCodeEnum.ORDER_DELIVERY_ERROR.getAuthCode(), AuthCodeEnum.ORDER_REFUND_AUDIT.getAuthCode(),
            AuthCodeEnum.ORDER_SUB_TAB.getAuthCode(), AuthCodeEnum.PICK_SUB_TAB.getAuthCode(), AuthCodeEnum.ORDER_SEARCH.getAuthCode(),
            AuthCodeEnum.ORDER_TAB_AFTER_SALES.getAuthCode());

    private final static List<String> SELF_RIDER_AUTH_CODE_LIST = com.google.common.collect.Lists.newArrayList(
            AuthCodeEnum.SELF_RIDER_WAIT_TO_GET.getAuthCode(), AuthCodeEnum.SELF_RIDER_IN_DELIVERY.getAuthCode(), AuthCodeEnum.SELF_RIDER_WAIT_TO_TAKEGOODS.getAuthCode(),
            AuthCodeEnum.NEW_TASK_ONLY_DELIVERY.getAuthCode(), AuthCodeEnum.WAIT_TAKE_DELIVERY_TASK.getAuthCode(), AuthCodeEnum.SELF_RIDER_COMPLETED.getAuthCode(),
            AuthCodeEnum.WORKBENCH_SELF_RIDER_IN_DELIVERY.getAuthCode(), AuthCodeEnum.WORKBENCH_NEW_TASK_ONLY_DELIVERY.getAuthCode(), AuthCodeEnum.WORKBENCH_WAIT_TAKE_DELIVERY_TASK.getAuthCode(), AuthCodeEnum.WORKBENCH_RIDER_DONE_DELIVERY.getAuthCode()
    );


    private List<String> getOrderPermissionCodes(Long tenantId, Long storeId, boolean isApp){
        DataAuthResourceRequest dataAuthResourceRequest = new DataAuthResourceRequest();
        dataAuthResourceRequest.setTenantId(tenantId);
        dataAuthResourceRequest.setStoreId(storeId);
        dataAuthResourceRequest.setCode(AuthCodeEnum.ORDER_TAB.getAuthCode());
        dataAuthResourceRequest.setType(0);
        return authThriftWrapper.queryAuthorizedCodes(isApp ? authCodeList : pdaAuthCodeList);
    }

    /**
     * 查询订单tab首页信息
     *
     * @param
     * @return
     */
    @CatTransaction
    public CommonResponse<OrderHomePageResponse> appHomePage(HomePageRequest homePageRequest) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        Long storeId = identityInfo.getStoreId();

        OrderHomePageResponse orderHomePageResponse = new OrderHomePageResponse();
        OrderHomePageModuleVO orderHomePageModuleVO = OrderHomePageModuleVO.appHomePageInit();
        // 规格信息
        if(MccDynamicConfigUtil.getWmsjTenantIds().contains(tenantId)) {
            //歪马走灰度移除
            orderHomePageModuleVO.setAppendSpecToOrderItem(isDhAppendSpecToOrderItem(tenantId, storeId));
        } else {
            orderHomePageModuleVO.setAppendSpecToOrderItem(isAppendSpecToOrderItem(tenantId));
        }
        List<String> permissionCodes = getOrderPermissionCodes(tenantId, storeId, true);
        //开通模块信息
        fillModulePermission(permissionCodes, orderHomePageModuleVO, homePageRequest.isIgnoreOrderSubTabAuth());
        OrderPendingTaskVO orderPendingTaskVO = OrderPendingTaskVO.homePageInit();
        //数量统计信息
        fillModuleCount(permissionCodes, orderHomePageModuleVO, orderPendingTaskVO, homePageRequest.getEntityType());

        // 查询歪马异常单
        if(IntegerBooleanConstants.BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowDhExceptionTab())) {
            try {
                orderPendingTaskVO.setDhExceptionCount(abnOrderServiceWrapper.getUnprocessedAbnOrderCount(storeId));
            } catch (Exception e) {
                log.error("abnOrderServiceWrapper.getUnprocessedAbnOrderCount error", e);
            }
        }
        //处理“三方配送”TAB
        if (MccConfigUtil.isDHAggDeliveryGrayStore(storeId)) {
            ThriftPartyModuleVO thriftPartyModuleVO = getThriftPartyModuleVO(permissionCodes);
            ThriftPartyTaskVO thriftPartyTaskVO = getThriftPartyTaskVO(thriftPartyModuleVO);
            orderHomePageResponse.setThriftPartyModuleVO(thriftPartyModuleVO);
            orderHomePageResponse.setThriftPartyTaskVO(thriftPartyTaskVO);
        }
        // 组装返回参数
        orderHomePageResponse.setOrderHomePageModuleVO(orderHomePageModuleVO);
        orderHomePageResponse.setOrderPendingTaskVO(orderPendingTaskVO);
        return CommonResponse.success(orderHomePageResponse);
    }

    @CatTransaction
    public CommonResponse<PdaOrderHomePageResponse> pdaHomePage(HomePageRequest homePageRequest) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        Long storeId = identityInfo.getStoreId();
        PdaOrderHomePageResponse orderHomePageResponse = new PdaOrderHomePageResponse();
        PdaOrderHomePageModuleVO orderHomePageModuleVO = new PdaOrderHomePageModuleVO();
        // 规格信息
        orderHomePageModuleVO.setAppendSpecToOrderItem(isAppendSpecToOrderItem(tenantId));
        List<String> permissionCodes = getOrderPermissionCodes(tenantId, storeId, false);
        //开通模块信息
        fillPermission(permissionCodes, orderHomePageModuleVO);
        fillShowWaitToTakeOrderTab(permissionCodes, orderHomePageModuleVO::setShowWaitToTakeOrderTab);
        PdaOrderPendingTaskVO orderPendingTaskVO = PdaOrderPendingTaskVO.homePageInit();
        //数量统计信息
        fillModuleCount(permissionCodes, orderHomePageModuleVO, orderPendingTaskVO, homePageRequest.getEntityType());
        // 组装返回参数
        orderHomePageResponse.setOrderHomePageModuleVO(orderHomePageModuleVO);
        orderHomePageResponse.setOrderPendingTaskVO(orderPendingTaskVO);
        return CommonResponse.success(orderHomePageResponse);
    }

    public OCMSQueryOrderQuantityRequest buildOCMSQueryOrderQuantityRequest(Long tenantId, Long storeId, Integer entityType, List<String> permissionCodes) {
        OCMSQueryOrderQuantityRequest request = new OCMSQueryOrderQuantityRequest();
        request.setTenantId(tenantId);
        request.setShopIdList(Lists.newArrayList(storeId));
        request.setOrderQuantityTypeList(getOrderQuantityType(permissionCodes));
        //查询当前时间之前7天的订单
        request.setBeginCreateTime(TimeUtils.getBeforeDayTimeStamp(MccConfigUtil.queryOrderCreateTimeBefore()));
        request.setAfterSaleOrderBeginCreateTime(AfterSaleQueryOrderService.getAfterSaleQueryStartTime());
        request.setEndCreateTime(System.currentTimeMillis());
        if(Objects.equals(entityType, PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code())){
            request.setShopIdList(new ArrayList<>());
            request.setWarehouseIdList(Lists.newArrayList(storeId));
        }
        return request;
    }

    private List<Integer> getOrderQuantityType(List<String> permissionCodes) {
        Set<Integer> orderQuantityType = new HashSet<>();
        if(permissionCodes.contains(AuthCodeEnum.ORDER_WAIT_TO_CONFIRM.getAuthCode())){
            orderQuantityType.add(QueryOrderTypeQuantityEnum.WAIT_TAKE_ORDER.getValue());
        }
        if(permissionCodes.contains(AuthCodeEnum.WAIT_TO_SELF_FETCH.getAuthCode())){
            orderQuantityType.add(QueryOrderTypeQuantityEnum.WAIT_SELF_FETCH.getValue());
        }
        if(permissionCodes.contains(AuthCodeEnum.ORDER_REFUND_AUDIT.getAuthCode())){
            orderQuantityType.add(QueryOrderTypeQuantityEnum.WAIT_REFUND_AUDIT.getValue());

        }
        if(permissionCodes.contains(AuthCodeEnum.ORDER_TAB_AFTER_SALES.getAuthCode())){
            orderQuantityType.add(QueryOrderTypeQuantityEnum.WAIT_REFUND_AUDIT.getValue());
        }
        return new ArrayList<>(orderQuantityType);
    }

    private void fillModuleCount(List<String> permissionCodes, OrderHomePageModuleVO orderHomePageModuleVO, OrderPendingTaskVO orderPendingTaskVO, Integer entityType) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        Long storeId = identityInfo.getStoreId();
        //订单【待接单、售后、待自提】
        if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowOrderTab())){
            //请求订单数据源
            OCMSQueryOrderQuantityResponse orderQuantityResponse = ocmsQueryThriftService.queryOrderQuantityV2(buildOCMSQueryOrderQuantityRequest(tenantId, storeId, entityType, permissionCodes));
            if (orderQuantityResponse.getStatus() != null
                    && StatusCodeEnum.SUCCESS.getCode() == orderQuantityResponse.getStatus().getCode()
                    && MapUtils.isNotEmpty(orderQuantityResponse.getOrderTypeQuantityMap())) {
                // 设置代办数量
                orderPendingTaskVO.setWaitToTakeOrderCount(MapUtils.getInteger(orderQuantityResponse.getOrderTypeQuantityMap(), QueryOrderTypeQuantityEnum.WAIT_TAKE_ORDER.getValue(), 0));
                orderPendingTaskVO.setWaitToAuditRefundCount(MapUtils.getInteger(orderQuantityResponse.getOrderTypeQuantityMap(), QueryOrderTypeQuantityEnum.WAIT_REFUND_AUDIT.getValue(), 0));
                orderPendingTaskVO.setWaitToSelfFetchCount(MapUtils.getInteger(orderQuantityResponse.getOrderTypeQuantityMap(), QueryOrderTypeQuantityEnum.WAIT_SELF_FETCH.getValue(), 0));
            }
        }
        Map<WaitToAuditRefundGoodsOrderSubTypeEnum, Integer> waitToAuditRefundGoodsOrderSubTypeEnumIntegerMap = afterSaleQueryOrderService.countByType(tenantId, Lists.newArrayList(storeId), entityType);
        orderPendingTaskVO.setWaitToAuditRefundGoodsCount(waitToAuditRefundGoodsOrderSubTypeEnumIntegerMap.getOrDefault(WaitToAuditRefundGoodsOrderSubTypeEnum.ALL, 0));
        //拣货
        if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowWaitToPickTab())) {
            orderPendingTaskVO.setWaitToPickCount(wait2PickQueryOrderService.countAll(tenantId, Lists.newArrayList(storeId), entityType));
        }
        //配送异常
        if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowDeliveryErrorTab())) {
            orderPendingTaskVO.setDeliveryErrorCount(deliveryErrorQueryOrderService.countAll(tenantId, Lists.newArrayList(storeId), entityType));
        }
        // 根据权限查询自营骑手待办
        List<TmsDeliveryStatusDescEnum> deliveryQuantityStatuses = getDeliveryQuantityStatuses(permissionCodes);
        if (MccConfigUtil.getFusionGaryV1(tenantId, storeId)) {
            if (!Collections.disjoint(permissionCodes, SELF_RIDER_AUTH_CODE_LIST)) {
                try {
                    Set<QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey> waitGetOrderKeySet = new HashSet<>();
                    Set<QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey> waitTakeOrderKeySet = new HashSet<>();

                    Optional<QueryActivateDeliveryOrderCntDetailResponse> cntDetailResponseOpt = riderDeliveryRemoteService.queryActivateDeliveryOrderCntDetail();
                    if (cntDetailResponseOpt.isPresent()) {
                        QueryActivateDeliveryOrderCntDetailResponse cntDetailResponse = cntDetailResponseOpt.get();

                        if (BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowNewJustDeliveryTaskTab())) {
                            orderPendingTaskVO.setNewJustDeliveryTaskCount(cntDetailResponse.getNewJustDeliveryOrderCnt());
                            waitGetOrderKeySet.addAll(cntDetailResponse.getNewJustDeliveryOrderKeyList());
                        }

                        if (BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowWaitTakeDeliveryTaskTab())) {
                            orderPendingTaskVO.setWaitTakeDeliveryTaskCount(cntDetailResponse.getWaitTakeDeliveryOrderCount());
                            waitTakeOrderKeySet.addAll(cntDetailResponse.getWaitTakeDeliveryOrderKeyList());
                        }

                        if (BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowRiderInDeliveryTab())) {
                            orderPendingTaskVO.setRiderInDeliveryCount(cntDetailResponse.getInDeliveryOrderCount());
                        }

                        if (BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowNewPickDeliveryTaskTab())) {
                            orderPendingTaskVO.setNewPickDeliveryTaskCount(cntDetailResponse.getNewPickDeliveryOrderCnt());
                        }

                    }
                    orderPendingTaskVO.setRiderWaitToGetOrderCount(waitGetOrderKeySet.size());
                    orderPendingTaskVO.setRiderWaitToTakeGoodsCount(waitTakeOrderKeySet.size());
                } catch (Exception e) {
                    log.error("query drunk horse tab red count fail", e);
                    Cat.logEvent("HOME_PAGE", "QUERY_TAB_RED_COUNT_FAIL");
                }
            }
        } else {
            if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowOrderTab())
                    && CollectionUtils.isNotEmpty(deliveryQuantityStatuses)) {
                Map<Integer, Integer> deliveryStatusQuantityMap = riderDeliveryRemoteService.queryDeliveryOrderQuantity(deliveryQuantityStatuses);
                orderPendingTaskVO.setRiderWaitToGetOrderCount(
                        deliveryStatusQuantityMap.getOrDefault(TmsDeliveryStatusDescEnum.WAITING_TO_ASSIGN_RIDER.getCode(), 0));
                orderPendingTaskVO.setRiderWaitToTakeGoodsCount(
                        deliveryStatusQuantityMap.getOrDefault(TmsDeliveryStatusDescEnum.RIDER_ASSIGNED.getCode(), 0));
                orderPendingTaskVO.setRiderInDeliveryCount(
                        deliveryStatusQuantityMap.getOrDefault(TmsDeliveryStatusDescEnum.RIDER_TAKEN_GOODS.getCode(), 0)
                );
                //查询推广自提，暂时不展示小红点
                orderPendingTaskVO.setWaitToFinishSelfPickPromoteOrderCount(0);
            }
        }

        //配送
        if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowWaitToDeliveryTab())) {
            orderPendingTaskVO.setWaitToDeliveryCount(wait2DeliveryQueryOrderService.countAll(tenantId, Lists.newArrayList(storeId), entityType));
        }
    }


    private void fillModuleCount(List<String> permissionCodes, PdaOrderHomePageModuleVO orderHomePageModuleVO, PdaOrderPendingTaskVO orderPendingTaskVO, Integer entityType) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        Long storeId = identityInfo.getStoreId();
        //订单【待接单、售后、待自提】
        if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowOrderTab())){
            //请求订单数据源
            OCMSQueryOrderQuantityResponse orderQuantityResponse = ocmsQueryThriftService.queryOrderQuantityV2(buildOCMSQueryOrderQuantityRequest(tenantId, storeId, entityType, permissionCodes));
            if (orderQuantityResponse.getStatus() != null
                    && StatusCodeEnum.SUCCESS.getCode() == orderQuantityResponse.getStatus().getCode()
                    && MapUtils.isNotEmpty(orderQuantityResponse.getOrderTypeQuantityMap())) {
                // 设置代办数量
                orderPendingTaskVO.setWaitToTakeOrderCount(MapUtils.getInteger(orderQuantityResponse.getOrderTypeQuantityMap(), QueryOrderTypeQuantityEnum.WAIT_TAKE_ORDER.getValue(), 0));
                orderPendingTaskVO.setWaitToAuditRefundCount(MapUtils.getInteger(orderQuantityResponse.getOrderTypeQuantityMap(), QueryOrderTypeQuantityEnum.WAIT_REFUND_AUDIT.getValue(), 0));
                orderPendingTaskVO.setWaitToSelfFetchCount(MapUtils.getInteger(orderQuantityResponse.getOrderTypeQuantityMap(), QueryOrderTypeQuantityEnum.WAIT_SELF_FETCH.getValue(), 0));
            }
        }
        Map<WaitToAuditRefundGoodsOrderSubTypeEnum, Integer> waitToAuditRefundGoodsOrderSubTypeEnumIntegerMap = afterSaleQueryOrderService.countByType(tenantId, Lists.newArrayList(storeId), entityType);
        orderPendingTaskVO.setWaitToAuditRefundGoodsCount(waitToAuditRefundGoodsOrderSubTypeEnumIntegerMap.getOrDefault(WaitToAuditRefundGoodsOrderSubTypeEnum.ALL, 0));
        //拣货
        if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowWaitToPickTab())) {
            orderPendingTaskVO.setWaitToPickCount(wait2PickQueryOrderService.countAll(tenantId, Lists.newArrayList(storeId), entityType));
        }
        //配送异常
        if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowDeliveryErrorTab())) {
            orderPendingTaskVO.setDeliveryErrorCount(deliveryErrorQueryOrderService.countAll(tenantId, Lists.newArrayList(storeId), entityType));
        }
        //配送
        if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowWaitToDeliveryTab())) {
            orderPendingTaskVO.setWaitToDeliveryCount(wait2DeliveryQueryOrderService.countAll(tenantId, Lists.newArrayList(storeId), entityType));
        }

    }

    private List<TmsDeliveryStatusDescEnum> getDeliveryQuantityStatuses(List<String> permissionCodes) {
        List<TmsDeliveryStatusDescEnum> deliveryQuantityStatuses = new ArrayList<>();
        if (permissionCodes.contains(AuthCodeEnum.SELF_RIDER_WAIT_TO_GET.getAuthCode())){
            deliveryQuantityStatuses.add(TmsDeliveryStatusDescEnum.WAITING_TO_ASSIGN_RIDER);
        }
        if (permissionCodes.contains(AuthCodeEnum.SELF_RIDER_WAIT_TO_TAKEGOODS.getAuthCode())){
            deliveryQuantityStatuses.add(TmsDeliveryStatusDescEnum.RIDER_ASSIGNED);
        }
        if (permissionCodes.contains(AuthCodeEnum.SELF_RIDER_IN_DELIVERY.getAuthCode())){
            deliveryQuantityStatuses.add(TmsDeliveryStatusDescEnum.RIDER_TAKEN_GOODS);
        }
        return deliveryQuantityStatuses;
    }

    private void fillModulePermission(List<String> permissionCodes, OrderHomePageModuleVO orderHomePageModuleVO, boolean ignoreOrderSubTabAuth) {
        fillPermission(permissionCodes, orderHomePageModuleVO);
        fillShowWaitToTakeOrderTab(permissionCodes, orderHomePageModuleVO::setShowWaitToTakeOrderTab);
        if (MccConfigUtil.getFusionGaryV1(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),ApiMethodParamThreadLocal.getIdentityInfo().getStoreId())) {
            fillPermissionForMultiBindAuthCode(permissionCodes, orderHomePageModuleVO,  ignoreOrderSubTabAuth);
        }
    }

    private ThriftPartyTaskVO getThriftPartyTaskVO(ThriftPartyModuleVO thriftPartyModuleVO) {
        ThriftPartyTaskVO thriftPartyTaskVO = ThriftPartyTaskVO.thriftPartyInit();
        try {
            if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(thriftPartyModuleVO.getShowThirdPartTab())) {
                //不判断成功与否，失败直接返回0咯
                ThirdWaitToDeliverySubTypeCountResponse response = dhThirdDeliveryService.queryWaitDeliverySubTypeCount();
                Integer waitToTakeCount = 0;
                Integer waitToPickCount = 0;
                if (MccDynamicConfigUtil.isNewPickGrayStore(ApiMethodParamThreadLocal.getIdentityInfo().getStoreId())) {
                    Pair<Integer, Integer> pair = dhThirdDeliveryService.queryUnAcceptedPickOrderCountAndWaitPickCount();
                    waitToTakeCount = pair.getKey();
                    waitToPickCount = pair.getValue();
                } else {
                    waitToTakeCount = dhThirdDeliveryService.queryStoreUnAcceptedPickOrderCount();
                    waitToPickCount = dhThirdDeliveryService.queryWaitPickOrderCount();
                }
                if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(thriftPartyModuleVO.getShowThirdWaitToTakeOrderTab())) {
                    //三方待领取数量
                    thriftPartyTaskVO.setThirdWaitToTakeCount(waitToTakeCount);
                }
                if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(thriftPartyModuleVO.getShowThirdWaitToPickTab())) {
                    //三方待拣货数量
                    thriftPartyTaskVO.setThirdWaitToPickCount(waitToPickCount);
                }
                if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(thriftPartyModuleVO.getShowThirdWaitToDeliveryTab())) {
                    //三方待发配送数量
                    thriftPartyTaskVO.setThirdWaitToLaunchDeliveryCount(response.getWaitToLaunchDeliveryCount());
                    //三方待接单数量
                    thriftPartyTaskVO.setThirdWaitToAcceptDeliveryCount(response.getWaitToRiderAcceptCount());
                    //三方待取货数量
                    thriftPartyTaskVO.setThirdWaitToPickUpDeliveryCount(response.getWaitToTakeGoodsCount());
                    //三方配送中数量
                    thriftPartyTaskVO.setThirdOnDeliveryCount(response.getDeliveringCount());
                }
                if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(thriftPartyModuleVO.getShowThirdExceptionTab())) {
                    //三方异常配送数量
                    thriftPartyTaskVO.setThirdExceptionCount(response.getExceptionCount());
                }
            }
        } catch (Exception e) {
            log.error("getThriftPartyTaskVO error", e);
        }
        return thriftPartyTaskVO;
    }

    private ThriftPartyModuleVO getThriftPartyModuleVO(List<String> permissionCodes) {
        ThriftPartyModuleVO thriftPartyModuleVO = ThriftPartyModuleVO.initThriftPartyModule();
        fillThirdPartyPermission(permissionCodes, thriftPartyModuleVO);
        return thriftPartyModuleVO;
    }

    private void fillThirdPartyPermission(List<String> permissionCodes, ThriftPartyModuleVO thriftPartyModuleVO) {
        // 没有配置默认都没有权限
        for (String permissionCode : permissionCodes) {
            // 待接单tab页鉴权
            if (StringUtils.equals(permissionCode, AuthCodeEnum.THIRD_PARTY_DELIVERY.getAuthCode())) {
                thriftPartyModuleVO.setShowThirdPartTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                thriftPartyModuleVO.setShowThirdWaitToTakeOrderTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                thriftPartyModuleVO.setShowThirdWaitToPickTab(IntegerBooleanConstants.BOOLEAN_TRUE);
            } else if (StringUtils.equals(permissionCode, AuthCodeEnum.THIRD_PARTY_WAIT_TO_DELIVERY_AND_EXCEPTION.getAuthCode())) {
                thriftPartyModuleVO.setShowThirdWaitToDeliveryTab(IntegerBooleanConstants.BOOLEAN_TRUE);
                // 配送异常tab页鉴权
                thriftPartyModuleVO.setShowThirdExceptionTab(IntegerBooleanConstants.BOOLEAN_TRUE);
            }
        }
    }

    private void fillPermission(List<String> permissionCodes, Object orderHomePageModuleVO){
        Class<?> clazz = orderHomePageModuleVO.getClass();
        Field[] declaredFields = clazz.getDeclaredFields();
        for (Field field : declaredFields) {
            if (!field.isAnnotationPresent(BindAuthCode.class)) {
                continue;
            }
            BindAuthCode annotation = field.getAnnotation(BindAuthCode.class);
            if (permissionCodes.contains(annotation.value().getAuthCode())) {
                field.setAccessible(true);
                try {
                    field.set(orderHomePageModuleVO, IntegerBooleanConstants.BOOLEAN_TRUE);
                } catch (IllegalAccessException e) {
                    log.error("fillPermission error", e);
                }
            }
        }
    }

    private void fillPermissionForMultiBindAuthCode(List<String> permissionCodes, OrderHomePageModuleVO orderHomePageModuleVO, boolean ignoreOrderSubTabAuth){

        boolean anySelfDeliveryAuth = false;
        for (String permissionCode : permissionCodes) {
            if(StringUtils.equals(permissionCode, AuthCodeEnum.NEW_TASK_ONLY_DELIVERY.getAuthCode()) || StringUtils.equals(permissionCode, AuthCodeEnum.WORKBENCH_NEW_TASK_ONLY_DELIVERY.getAuthCode())) {
                orderHomePageModuleVO.setShowNewJustDeliveryTaskTab(BOOLEAN_TRUE);
                //二级tab
                orderHomePageModuleVO.setShowRiderGetOrderTab(BOOLEAN_TRUE);
                anySelfDeliveryAuth = true;
            } else if(StringUtils.equals(permissionCode, AuthCodeEnum.WAIT_TAKE_DELIVERY_TASK.getAuthCode()) || StringUtils.equals(permissionCode, AuthCodeEnum.WORKBENCH_WAIT_TAKE_DELIVERY_TASK.getAuthCode())) {
                orderHomePageModuleVO.setShowWaitTakeDeliveryTaskTab(BOOLEAN_TRUE);
                //二级tab
                orderHomePageModuleVO.setShowRiderTakeGoodsTab(BOOLEAN_TRUE);
                anySelfDeliveryAuth = true;
            } else if(StringUtils.equals(permissionCode, AuthCodeEnum.RIDER_DONE_DELIVERY.getAuthCode()) || StringUtils.equals(permissionCode, AuthCodeEnum.WORKBENCH_RIDER_DONE_DELIVERY.getAuthCode()) ) {
                orderHomePageModuleVO.setShowCompletedDeliveryTaskTab(BOOLEAN_TRUE);
                //二级tab
                orderHomePageModuleVO.setShowRiderCompletedTab(BOOLEAN_TRUE);
                anySelfDeliveryAuth = true;
            } else if (StringUtils.equals(permissionCode, AuthCodeEnum.SELF_RIDER_IN_DELIVERY.getAuthCode()) || StringUtils.equals(permissionCode, AuthCodeEnum.WORKBENCH_SELF_RIDER_IN_DELIVERY.getAuthCode())) {
                orderHomePageModuleVO.setShowRiderInDeliveryTab(BOOLEAN_TRUE);
                anySelfDeliveryAuth = true;
            } else if (StringUtils.equals(permissionCode, AuthCodeEnum.NEW_TASK_PICK_DELIVERY.getAuthCode()) || StringUtils.equals(permissionCode, AuthCodeEnum.WORKBENCH_DELIVERY_SELF_NEW_PICK_DELIVERY.getAuthCode())) {
                orderHomePageModuleVO.setShowNewPickDeliveryTaskTab(BOOLEAN_TRUE);
                //二级tab
                orderHomePageModuleVO.setShowRiderGetOrderTab(BOOLEAN_TRUE);
                anySelfDeliveryAuth = true;
            }
        }
        //一级tab
        if (anySelfDeliveryAuth && ignoreOrderSubTabAuth) {
            orderHomePageModuleVO.setShowOrderTab(BOOLEAN_TRUE);
        }

    }

    private void fillShowWaitToTakeOrderTab(List<String> permissionCodes, Consumer<Integer> consumer) {
        if (!permissionCodes.contains(AuthCodeEnum.ORDER_WAIT_TO_CONFIRM.getAuthCode())) {
            return;
        }
        // 待接单tab页，除了权限控制外，还需要判断商户是否自动接单，如果是自动接单不需要展示待接单tab
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        boolean isManualTake = tenantBusinessConfigWrapper.isManualTakeOrder(identityInfo.getUser().getTenantId(), identityInfo.getStoreId());
        consumer.accept(isManualTake ? IntegerBooleanConstants.BOOLEAN_TRUE : IntegerBooleanConstants.BOOLEAN_FALSE);
    }


    private Integer isAppendSpecToOrderItem(long tenantId) {
        Map<String, String> tenantSwitchMap = tenantWrapper.queryTenantSwitch(tenantId, Arrays.asList(CommonConstants.TenantSwitchKey.APPEND_SPEC_TO_ORDER_ITEM));
        return "1".equals(tenantSwitchMap.get(CommonConstants.TenantSwitchKey.APPEND_SPEC_TO_ORDER_ITEM))
                ? IntegerBooleanConstants.BOOLEAN_TRUE : IntegerBooleanConstants.BOOLEAN_FALSE;
    }

    private Integer isDhAppendSpecToOrderItem(long tenantId, Long storeId) {
        try {
            if (MccDynamicConfigUtil.isNewPickGrayStore(storeId)) {
                //歪马新流程不需要把规格贴到商品名后面了，单独展示
                return IntegerBooleanConstants.BOOLEAN_FALSE;
            } else {
                return isAppendSpecToOrderItem(tenantId);
            }
        } catch (Exception e) {
            log.error("isDhAppendSpecToOrderItem error", e);
            //降级展示
            return IntegerBooleanConstants.BOOLEAN_TRUE;
        }
    }
}
