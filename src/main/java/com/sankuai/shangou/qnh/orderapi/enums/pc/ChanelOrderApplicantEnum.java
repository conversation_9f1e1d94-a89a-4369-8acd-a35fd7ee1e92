package com.sankuai.shangou.qnh.orderapi.enums.pc;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <EMAIL>
 * @Date: 2019/6/4 17:03
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum ChanelOrderApplicantEnum {

    USER(1,"用户发起"),
    MERCHANT(2,"商家"),
    CHANNEL(3,"渠道");

    private int code;

    private String desc;


    public static ChanelOrderApplicantEnum getByCode(int code) {

        for (ChanelOrderApplicantEnum e : ChanelOrderApplicantEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }
        return null;
    }

}
