package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * ERP门店商品SPU分页查询请求参数
 *
 * <AUTHOR>
 * @since 2023/05/11
 */
@TypeDoc(
        description = "ERP门店商品SPU分页查询请求参数"
)
@ApiModel("ERP门店商品SPU分页查询请求参数")
@Getter
@Setter
@ToString(callSuper = true)
public class ErpStoreSpuPageQueryRequest extends ErpStoreSpuQueryRequest{

    @FieldDoc(description = "页码", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(name = "页码", required = true)
    private Integer page = 1;

    @FieldDoc(description = "每页数量", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(name = "每页数量", required = true)
    private Integer size = 20;

    public void validate() {
        super.validate();

        if (this.page == null || this.page <= 0) {
            throw new ParamException("页码非法");
        }
        if (this.size == null || this.size <= 0 || this.size > 50) {
            throw new ParamException("分页大小非法");
        }
    }

}
