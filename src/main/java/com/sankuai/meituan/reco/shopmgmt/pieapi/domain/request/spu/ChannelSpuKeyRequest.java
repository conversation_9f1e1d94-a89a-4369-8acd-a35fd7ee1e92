// Copyright (C) 2021 Meituan
// All rights reserved
package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.price.client.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.ChannelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2021/2/25 下午2:18
 **/
@TypeDoc(
        name = "渠道spu key",
        description = "渠道spu key"
)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ChannelSpuKeyRequest {
    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id", required = false)
    private Long storeId;
    @FieldDoc(
            description = "渠道id"
    )
    @ApiModelProperty(value = "渠道id", required = false)
    private Integer channelId;
    @FieldDoc(
            description = "spuid"
    )
    @ApiModelProperty(value = "spuid", required = false)
    private String spuId;

    public void validate() {
        Preconditions.checkArgument(Objects.nonNull(storeId), "门店ID不能为空");
        Preconditions.checkArgument(Objects.nonNull(channelId)
                && Objects.nonNull(ChannelType.findByValue(channelId)), "渠道ID不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(spuId), "spuId不能为空");
    }
}
