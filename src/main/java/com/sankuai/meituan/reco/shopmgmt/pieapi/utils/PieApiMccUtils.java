package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.MccKeyEnum;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/16 17:19
 * @Description:
 */
@Slf4j
public class PieApiMccUtils {

    /**
     * 获取租户的门店管理员称呼，如 "店长"
     * @return
     */
    public static List<String> getStoreManagerTitleListByTargetKey(String targetKey) {
        String storeManagerTitles = ConfigUtilAdapter.getString(MccKeyEnum.STORE_MANAGER_TITLE.key);
        if (StringUtils.isBlank(storeManagerTitles)) {
            return Lists.newArrayList();
        }

        Map<String, String> targetKey2StoreManagerTitleMap = JSONObject.parseObject(storeManagerTitles, Map.class);
        if (MapUtils.isNotEmpty(targetKey2StoreManagerTitleMap) && StringUtils.isNotBlank(targetKey2StoreManagerTitleMap.get(targetKey))) {
            List<String> storeManagerTitleList = Arrays.asList(targetKey2StoreManagerTitleMap.get(targetKey).split(","));
            return storeManagerTitleList;
        }

        return Lists.newArrayList();
    }

    /**
     * 获取超时新供给业务类型id
     * @return
     */
    public static int getSupermarketNewSupplyBizType() {
        return ConfigUtilAdapter.getInt("supermarket.new.supply.biz.type");
    }

}
