// Copyright (C) 2021 Meituan
// All rights reserved
package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryProblemSpuRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2021/2/22 下午2:23
 **/
@TypeDoc(
        name = "不一致商品查询请求",
        description = "不一致商品查询请求"
)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProblemSpuQueryRequest {
    @FieldDoc(
            description = "门店id列表"
    )
    @ApiModelProperty(value = "门店id", required = false)
    private Long storeId;
    @FieldDoc(
            description = "商品名称"
    )
    @ApiModelProperty(value = "商品名称", required = false)
    private String spuName;

    @FieldDoc(
            description = "spuid"
    )
    @ApiModelProperty(value = "spuid", required = false)
    private String spuId;

    @FieldDoc(
            description = "page"
    )
    @ApiModelProperty(value = "page", required = false)
    private int page;
    @FieldDoc(
            description = "pageSize"
    )
    @ApiModelProperty(value = "pageSize", required = false)
    private int pageSize;

    public void validate() {
        Preconditions.checkArgument(Objects.nonNull(storeId) && storeId.compareTo(0L) > 0, "门店ID非法");
        Preconditions.checkArgument(page > 0, "当前查询页码非法");
        Preconditions.checkArgument(pageSize > 0 && pageSize <= 50, "每页查询数量小于50");
    }

    public QueryProblemSpuRequest to(Long tenantId) {
        return QueryProblemSpuRequest.builder()
                .tenantId(tenantId)
                .storeIds(Lists.newArrayList(storeId))
                .spuId(spuId)
                .spuName(spuName)
                .page(page)
                .pageSize(pageSize)
                .build();
    }
}
