package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreCategoryChangeRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/6/23 8:36 下午
 **/
@TypeDoc(
        description = "修改店内分类名称",
        authors = {"zhouyan32"}
)
@Data
@ApiModel("修改店内分类名称")
public class ModifyFrontCategoryRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId = 100;

    @FieldDoc(
            description = "分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分类ID", required = true)
    private Long categoryId;

    @FieldDoc(
            description = "修改后的分类名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "修改后的分类名称", required = true)
    private String name;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.channelId == null) {
            throw new CommonLogicException("渠道id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.categoryId == null) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (StringUtils.isBlank(name)) {
            throw new CommonLogicException("店内分类名称不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }

    public ChannelStoreCategoryChangeRequest to(User user) {
        ChannelStoreCategoryChangeRequest channelStoreCategoryChangeRequest = new ChannelStoreCategoryChangeRequest();
        channelStoreCategoryChangeRequest.setTenantId(user.getTenantId());
        channelStoreCategoryChangeRequest.setStoreId(storeId);
        channelStoreCategoryChangeRequest.setChannelId(channelId);
        channelStoreCategoryChangeRequest.setCategoryId(categoryId);
        channelStoreCategoryChangeRequest.setName(name);
        channelStoreCategoryChangeRequest.setOperatorId(user.getAccountId());
        channelStoreCategoryChangeRequest.setOperator(user.getOperatorName());
        return channelStoreCategoryChangeRequest;
    }
}
