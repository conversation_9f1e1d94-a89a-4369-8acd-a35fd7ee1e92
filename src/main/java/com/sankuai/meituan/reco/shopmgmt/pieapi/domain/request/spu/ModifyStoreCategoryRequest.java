package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreCategoryChangeRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.enums.StoreCategorySceneIdentityEnum;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category.UpdateMerchantStoreCategoryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */

@Data
@ApiModel("修改门店店内分类名称")
public class ModifyStoreCategoryRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分类ID", required = true)
    private String categoryId;

    @FieldDoc(
            description = "修改后的分类名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "修改后的分类名称", required = true)
    private String name;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.categoryId == null) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (StringUtils.isBlank(name)) {
            throw new CommonLogicException("店内分类名称不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }

    public UpdateMerchantStoreCategoryRequest toPoiRpcReq(User user) {
        UpdateMerchantStoreCategoryRequest rpcReq = new UpdateMerchantStoreCategoryRequest();
        rpcReq.setUpdateType(1);
        rpcReq.setCategoryName(StringUtils.trim(name));
        rpcReq.setCategoryId(Long.valueOf(categoryId));
        rpcReq.setMerchantId(user.getTenantId());
        rpcReq.setNewStoreGroupId(storeId);
        rpcReq.setOperatorId(user.getAccountId());
        rpcReq.setOperatorName(user.getOperatorName());
        rpcReq.setOperatorAccount(user.getAccountName());
        rpcReq.setSceneIdentity(StoreCategorySceneIdentityEnum.POI.getCode());
        rpcReq.setPushChannel(true);
        return rpcReq;
    }
}
