package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.DeleteReasonTemplateRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 删除审核拒绝原因模板
 * @author: WangSukuan
 * @create: 2020-03-11
 **/
@TypeDoc(
        description = "删除审核拒绝原因模板"
)
@Data
@ApiModel("删除审核拒绝原因模板")
public class ReasonTemplateDeleteRequest {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "模板原因id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "模板原因id", required = true)
    @NotNull
    private Long reasonId;


    public DeleteReasonTemplateRequest convertDeleteReasonTemplateRequest(User user){

        DeleteReasonTemplateRequest deleteReasonTemplateRequest = new DeleteReasonTemplateRequest();
        deleteReasonTemplateRequest.setTenantId(this.getTenantId());
        deleteReasonTemplateRequest.setOperateId(user.getAccountId());
        deleteReasonTemplateRequest.setOperateName(user.getOperatorName());
        deleteReasonTemplateRequest.setReasonId(this.getReasonId());

        return deleteReasonTemplateRequest;

    }


}
