package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.FrontCategoryWithPathDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.FrontCategorySimpleDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreCategoryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 门店商品多分类
 * @author: WangSukuan
 * @create: 2020-03-03
 **/
@TypeDoc(
        description = "门店商品多分类"
)
@Data
@ApiModel("门店商品多分类")
@NoArgsConstructor
public class FrontCategorySimpleVO {

    @FieldDoc(
            description = "前台分类编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "前台分类编码", required = true)
    private String frontCategoryCode;

    @FieldDoc(
            description = "前台分类名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "前台分类名称", required = true)
    private String frontCategoryName;

    @FieldDoc(
            description = "前台分类编码全路径, \">\"拼接", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "前台分类编码全路径, \">\"拼接", required = true)
    private String frontCategoryCodePath;

    @FieldDoc(
            description = "前台分类名称全路径,\">\"拼接", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "前台分类名称全路径,\">\"拼接", required = true)
    private String frontCategoryNamePath;


    public FrontCategorySimpleVO(FrontCategorySimpleDTO frontCategorySimpleDTO) {
        this.setFrontCategoryCode(frontCategorySimpleDTO.getFrontCategoryCode());
        this.setFrontCategoryName(frontCategorySimpleDTO.getFrontCategoryName());
        this.setFrontCategoryCodePath(frontCategorySimpleDTO.getFrontCategoryCodePath());
        this.setFrontCategoryNamePath(frontCategorySimpleDTO.getFrontCategoryNamePath());
    }

    public FrontCategorySimpleVO(StoreCategoryDTO frontCategorySimpleDTO) {
        this.setFrontCategoryCode(frontCategorySimpleDTO.getStoreCategoryCode());
        this.setFrontCategoryName(frontCategorySimpleDTO.getStoreCategoryName());
        this.setFrontCategoryCodePath(frontCategorySimpleDTO.getStoreCategoryCodePath());
        this.setFrontCategoryNamePath(frontCategorySimpleDTO.getStoreCategoryNamePath());
    }

    public FrontCategorySimpleDTO buildFrontCategorySimpleDTO(){
        FrontCategorySimpleDTO frontCategorySimpleDTO = new FrontCategorySimpleDTO();
        frontCategorySimpleDTO.setFrontCategoryCode(this.getFrontCategoryCode());
        frontCategorySimpleDTO.setFrontCategoryName(this.getFrontCategoryName());
        frontCategorySimpleDTO.setFrontCategoryCodePath(this.getFrontCategoryCodePath());
        frontCategorySimpleDTO.setFrontCategoryNamePath(this.getFrontCategoryNamePath());
        return frontCategorySimpleDTO;
    }

    public FrontCategorySimpleVO(FrontCategoryWithPathDTO frontCategoryWithPathDTO) {
        this.setFrontCategoryCode(frontCategoryWithPathDTO.getFrontCategoryCode());
        this.setFrontCategoryName(frontCategoryWithPathDTO.getFrontCategoryName());
        this.setFrontCategoryCodePath(frontCategoryWithPathDTO.getFrontCategoryCodePath());
        this.setFrontCategoryNamePath(frontCategoryWithPathDTO.getFrontCategoryNamePath());
    }

    public FrontCategoryWithPathDTO buildFrontCategoryWithPathDTO(){
        FrontCategoryWithPathDTO frontCategoryWithPathDTO = new FrontCategoryWithPathDTO();
        frontCategoryWithPathDTO.setFrontCategoryCode(this.getFrontCategoryCode());
        frontCategoryWithPathDTO.setFrontCategoryName(this.getFrontCategoryName());
        frontCategoryWithPathDTO.setFrontCategoryCodePath(this.getFrontCategoryCodePath());
        frontCategoryWithPathDTO.setFrontCategoryNamePath(this.getFrontCategoryNamePath());
        return frontCategoryWithPathDTO;
    }

    public static StoreCategoryDTO convertToStoreCategoryDTO(FrontCategorySimpleVO storeCategory){
        StoreCategoryDTO storeCategoryDTO = new StoreCategoryDTO();
        storeCategoryDTO.setStoreCategoryCode(storeCategory.getFrontCategoryCode());
        storeCategoryDTO.setStoreCategoryName(storeCategory.getFrontCategoryName());
        storeCategoryDTO.setStoreCategoryCodePath(storeCategory.getFrontCategoryCodePath());
        storeCategoryDTO.setStoreCategoryNamePath(storeCategory.getFrontCategoryNamePath());
        return storeCategoryDTO;
    }
}
