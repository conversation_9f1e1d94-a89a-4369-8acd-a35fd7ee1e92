package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.dianping.pigeon.util.CollectionUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelStoreSpuParamDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.assertj.core.util.Lists;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 门店商品上架参数
 */
@TypeDoc(
        description = "门店商品上架参数"
)
@Data
public class StoreSpuOnlineParamVO {

    @FieldDoc(
            description = "SPU编码"
    )
    private String spuId;

    /**
     * 只会使用： skuId、 ChannelSyncStrategyVO#channelId、ChannelSyncStrategyVO#syncStrategyType、ChannelSyncStrategyVO#fixedPrice
     * 租户在手动定价模式下，用于保存SKU零售价
     */
    @FieldDoc(
            description = "sku价格信息(复用调价实体)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "sku价格信息(复用调价实体)", required = true)
    @NotNull
    private List<SkuAdjustPriceVO> skuAdjustPriceVOS;

    public static List<ChannelStoreSpuParamDTO> to(List<StoreSpuParamVO> storeSpuList, List<ChannelParamVO> channelList) {
        if (CollectionUtils.isEmpty(storeSpuList)) {
            return Lists.newArrayList();
        }

        if (CollectionUtils.isEmpty(channelList)) {
            return StoreSpuParamVO.to(storeSpuList);
        }

        return channelList.stream().map(channel -> StoreSpuParamVO.to(storeSpuList, channel.getChannelId())).flatMap(List::stream).collect(Collectors.toList());
    }

    public static List<ChannelStoreSpuParamDTO> to(List<StoreSpuParamVO> storeSpuList, Integer channelId) {
        if (CollectionUtils.isEmpty(storeSpuList)) {
            return Lists.newArrayList();
        }

        return storeSpuList.stream().map(spu -> StoreSpuParamVO.to(spu, channelId)).collect(Collectors.toList());
    }

    public static List<ChannelStoreSpuParamDTO> to(List<StoreSpuParamVO> storeSpuList) {
        if (CollectionUtils.isEmpty(storeSpuList)) {
            return Lists.newArrayList();
        }

        return storeSpuList.stream().map(StoreSpuParamVO::to).collect(Collectors.toList());
    }

    public static ChannelStoreSpuParamDTO to(StoreSpuParamVO storeSpu, Integer channelId) {
        ChannelStoreSpuParamDTO channelStoreSpuParamDTO = StoreSpuParamVO.to(storeSpu);
        if (channelStoreSpuParamDTO != null) {
            channelStoreSpuParamDTO.setChannelId(channelId);
        }
        return channelStoreSpuParamDTO;
    }

    public static ChannelStoreSpuParamDTO to(StoreSpuParamVO storeSpu) {
        if (storeSpu == null) {
            return null;
        }

        ChannelStoreSpuParamDTO channelStoreSpuParamDTO = new ChannelStoreSpuParamDTO();
        channelStoreSpuParamDTO.setSpuId(storeSpu.getSpuId());
        channelStoreSpuParamDTO.setStoreId(storeSpu.getPoiId());

        return channelStoreSpuParamDTO;
    }
}
