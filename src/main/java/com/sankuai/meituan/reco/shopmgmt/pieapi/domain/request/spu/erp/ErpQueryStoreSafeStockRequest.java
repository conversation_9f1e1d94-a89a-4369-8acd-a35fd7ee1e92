package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp;

import org.apache.commons.lang3.StringUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * ERP门店商品安全库存查询请求参数
 *
 * <AUTHOR>
 * @since 2024/09/27
 */
@TypeDoc(
        description = "ERP门店商品安全库存查询请求参数"
)
@ApiModel("ERP门店商品安全库存查询请求参数")
@Getter
@Setter
@ToString
public class ErpQueryStoreSafeStockRequest {

    @FieldDoc(description = "门店ID", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(name = "门店ID")
    private Long storeId;

    @FieldDoc(description = "SKU主键", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "SKU主键")
    private String skuId;


    public void validate() {
        if (this.storeId == null) {
            throw new ParamException("门店ID不能为空");
        }
        if (StringUtils.isBlank(skuId)) {
            throw new ParamException("规格不能为空");
        }
    }
}
