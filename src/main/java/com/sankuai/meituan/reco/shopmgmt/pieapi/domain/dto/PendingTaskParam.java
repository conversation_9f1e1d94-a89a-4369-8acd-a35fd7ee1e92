/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto;

import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <br><br>
 * Author: l<PERSON><PERSON><PERSON><PERSON> <br>
 * Date: 2019-03-27 Time: 15:29
 * @since 2.1 权限迁移版本
 */
@Builder
@Data
public class PendingTaskParam {

    private long tenantId;
    private long entityId;
    private int entityType;
    private List<Long> storeIds;
    private User user;
    private Set<String> authCodes;
}
