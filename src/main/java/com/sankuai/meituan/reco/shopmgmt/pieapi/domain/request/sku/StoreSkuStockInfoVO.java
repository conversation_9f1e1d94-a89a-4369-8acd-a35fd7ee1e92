package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Version 1.0
 * @Date 2022/5/10 8:46 下午
 */
@TypeDoc(
        description = "门店商品库存信息"
)
@Data
@ApiModel("门店商品库存信息")
public class StoreSkuStockInfoVO {

    @FieldDoc(
            description = "商品库存信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品库存信息列表")
    private List<SkuStockInfo> skuStockInfoList = Lists.newArrayList();

    @Data
    @Builder
    @ApiModel("库存信息")
    public static class SkuStockInfo{
        @FieldDoc(
                description = "商品ID", requiredness = Requiredness.REQUIRED
        )
        @ApiModelProperty(name = "商品ID")
        private String skuId;

        @FieldDoc(
                description = "总库存", requiredness = Requiredness.REQUIRED
        )
        @ApiModelProperty(name = "总库存")
        private Double quantity;

        @FieldDoc(
                description = "可用库存", requiredness = Requiredness.REQUIRED
        )
        @ApiModelProperty(name = "可用库存")
        private Double validQuantity;

        @FieldDoc(
                description = "锁定库存", requiredness = Requiredness.REQUIRED
        )
        @ApiModelProperty(name = "锁定库存")
        private Double lockedQuantity;

        @FieldDoc(
                description = "冻结库存", requiredness = Requiredness.REQUIRED
        )
        @ApiModelProperty(name = "冻结库存")
        private Double frozenQuantity;

        @FieldDoc(
                description = "下架库存", requiredness = Requiredness.REQUIRED
        )
        @ApiModelProperty(name = "下架库存")
        private Double offShelveQuantity;
    }
}
