package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreCategoryQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/6/23 8:10 下午
 **/
@TypeDoc(
        description = "查询门店前台分类请求参数",
        authors = {"zhouyan32"}
)
@Data
@ApiModel("查询门店前台分类请求参数")
public class QueryFrontCategoryByPoiRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId = 100;

    @FieldDoc(
            description = "父分类id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "父分类id")
    private String parentCode;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.channelId == null) {
            throw new CommonLogicException("渠道id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }

    public ChannelStoreCategoryQueryRequest to(User user) {

        ChannelStoreCategoryQueryRequest channelStoreCategoryQueryRequest = new ChannelStoreCategoryQueryRequest();
        channelStoreCategoryQueryRequest.setTenantId(user.getTenantId());
        channelStoreCategoryQueryRequest.setStoreId(storeId);
        channelStoreCategoryQueryRequest.setChannelId(channelId);
        channelStoreCategoryQueryRequest.setQueryAll(true);
        if (StringUtils.isNotBlank(this.parentCode)) {
            channelStoreCategoryQueryRequest.setQueryAll(false);
            channelStoreCategoryQueryRequest.setParentCategoryId(Long.parseLong(this.parentCode));
        }
        channelStoreCategoryQueryRequest.setOperatorId(user.getAccountId());
        channelStoreCategoryQueryRequest.setOperator(user.getOperatorName());
        return channelStoreCategoryQueryRequest;
    }
}
