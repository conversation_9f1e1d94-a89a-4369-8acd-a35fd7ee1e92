package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * 快捷建品商品搜索入参
 *
 * <AUTHOR>
 * @date 2021-09-26 18:36
 */
@TypeDoc(
        description = "快捷建品商品搜索入参",
        authors = {"maliang16"}
)
@Data
@ApiModel("快捷建品商品搜索入参")
public class ProductQuery4FastCreateSpuRequest {
    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称")
    @NotBlank(message = "搜索内容不能为空")
    private String keyword;
    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id")
    @NotNull(message = "门店id不能为空")
    private Long storeId;

    @FieldDoc(
            description = "商品类别，主档商品-TENANT，闪购标品-RETAIL，医药标品-MEDICINE", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品类别，主档商品-TENANT，闪购标品-RETAIL，医药标品-MEDICINE")
    private String merchandiseCategory;
}
