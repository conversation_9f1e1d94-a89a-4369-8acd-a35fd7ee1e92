package com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018/3/22
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface MethodLog {
    boolean logRequest() default false;

    boolean logResponse() default false;

    boolean logException() default true;

    String logger() default "";
}
