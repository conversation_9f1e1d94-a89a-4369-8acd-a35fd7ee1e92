package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.FranchisorPurchasePlatformInfoVO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSupplierSkuDTO;
import lombok.Data;

import java.util.List;

/**
 * 控品商品结果
 *
 * <AUTHOR>
 * @since 2023/10/24
 */
@TypeDoc(
        description = "供货规格详情结果"
)
@Data
public class SupplierSkuInfoVO {

    @FieldDoc(
            description = "供货商品ID"
    )
    private String supplierSpuId;
    @FieldDoc(
            description = "供货商品名称"
    )
    private String supplierSpuName;
    @FieldDoc(
            description = "供货商品规格ID"
    )
    private String supplierSkuId;
    @FieldDoc(
            description = "供货商品规格"
    )
    private String supplierSpec;
    @FieldDoc(
            description = "供货商品图片"
    )
    private List<String> supplierPic;
    @FieldDoc(
            description = "供货商品名称"
    )
    private List<String> supplierUpc;
    @FieldDoc(
            description = "采购平台编码"
    )
    private List<FranchisorPurchasePlatformInfoVO> platformCodeInfoList;

    public static SupplierSkuInfoVO build(TenantSupplierSkuDTO tenantSupplierSku) {
        if (tenantSupplierSku == null) {
            return null;
        }
        SupplierSkuInfoVO vo = new SupplierSkuInfoVO();
        vo.setSupplierSpuId(tenantSupplierSku.getSupplierSpuId());
        vo.setSupplierSpuName(tenantSupplierSku.getSupplierName());
        vo.setSupplierSkuId(tenantSupplierSku.getSupplierSkuId());
        vo.setSupplierSpec(tenantSupplierSku.getSupplierSpec());
        vo.setSupplierPic(tenantSupplierSku.getSupplierPic());
        vo.setSupplierUpc(tenantSupplierSku.getSupplierUpc());
        vo.setPlatformCodeInfoList(Fun.map(tenantSupplierSku.getPurchasePlatformInfoDTOS(), FranchisorPurchasePlatformInfoVO::of));
        return vo;
    }


}
