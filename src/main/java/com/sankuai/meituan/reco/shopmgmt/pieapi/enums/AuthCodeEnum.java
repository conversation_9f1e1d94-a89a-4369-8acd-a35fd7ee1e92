/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import lombok.Getter;

/**
 * <br><br>
 * Author: <PERSON><PERSON><PERSON><PERSON> <br>
 * Date: 2019--27 Time: 15:49
 * <p>
 * 废弃：菜单code和权限code存在重复，需要拆分
 * 菜单code ——> MenuCodeEnum，权限code ——> PermissionCodeEnum
 */
@Getter
@Deprecated
public enum AuthCodeEnum {

    ORDER_TAB("ORDER_TAB", "底部订单TAB"),
    ORDER_SUB_TAB("ORDER_SUB_TAB", "订单子TAB"),
    ORDER_WAIT_TO_CONFIRM("ORDER_WAIT_TO_CONFIRM", "待接单"),
    ORDER_WAIT_TO_PICK("ORDER_WAIT_TO_PICK", "待拣货"),
    WAIT_TO_SELF_FETCH("WAIT_TO_SELF_FETCH", "待自提"),
    ORDER_WAIT_TO_DELIVERY("ORDER_WAIT_TO_DELIVERY", "待配送"),
    ORDER_DELIVERY_ERROR("ORDER_DELIVERY_ERROR", "配送异常"),
    DH_EXCEPTION("WAIMA_DH_EXCEPTION", "配送异常TAB"),
    ORDER_REFUND_AUDIT("ORDER_REFUND_AUDIT", "退款审核"),
    ORDER_TAB_AFTER_SALES("ORDER_TAB_AFTER_SALES", "售后审核"),
    SELF_RIDER_WAIT_TO_GET("SELF_RIDER_WAIT_TO_GET", "自营配送骑手待领取任务"),
    SELF_RIDER_WAIT_TO_TAKEGOODS("SELF_RIDER_WAIT_TO_TAKEGOODS", "自营配送骑手待取货"),
    SELF_RIDER_IN_DELIVERY("SELF_RIDER_IN_DELIVERY", "自营配送骑手配送中"),
    SELF_RIDER_COMPLETED("SELF_RIDER_COMPLETED", "自营配送骑手已完成"),
    SELF_RIDER_SELF_PICK_PROMOTE_ORDER("DH-SELF-PICKUP-ORDER", "自营配送骑手自提推广订单"),
    PICK_SUB_TAB("PICK_SUB_TAB", "拣货子TAB"),
    FULFILL_STATISTICS("FULFILL_STATISTICS", "履约数据统计"),
    FULFILL_STATISTICS_COMPASS_NEW("FULFILL_STATISTICS_COMPASS_NEW", "履约数据罗盘"),
    FULFILL_LACK_STATISTICS("FULFILL_LACK_STATISTICS", "缺货统计"),
    FULFILL_PREPARE_STATISTICS("FULFILL_PREPARE_STATISTICS", "备货统计"),
    FULFILL_PICK_STATISTICS("FULFILL_PICK_STATISTICS", "拣货统计"),
    FULFILL_MERGE_STATISTICS("FULFILL_MERGE_STATISTICS", "合流统计"),
    OUTSIDE_SETTLE("OUTSIDE_SETTLE", "拣货结算"),
    ORDER_SEARCH("ORDER_SEARCH", "订单查询"),
    MANAGEMENT_TAB("MANAGEMENT_TAB", "经营TAB"),
    MANAGEMENT_STATISTICS("MANAGEMENT_STATISTICS", "经营统计数据"),
    MANAGEMENT_PRODUCT("MANAGEMENT_PRODUCT", "经营-商品"),
    STORE_PRODUCT("STORE_PRODUCT", "门店商品"),
    PRICE_CHECK("PRICE_CHECK", "报价审核"),
    PRICE_RECORD("PRICE_RECORD", "报价记录"),
    TENANT_SKU_REVIEW("MERCHANDISE-APPLY-AUDIT", "商品审核"),
    RETAIL_PRICE_VERIFY("PRICE-RETAIL-VERIFY", "零售价审核"),
    MANAGEMENT_AFTER_SALE("MANAGEMENT_AFTER_SALE", "经营-售后"),
    REFUND_AUDIT("REFUND_AUDIT", "退款审核"),
    ORDER_DELIVERY_EXCEPTION("DELIVERY_ABNORMAL", "配送异常"),
    LACK_DEAL("LACK_DEAL", "缺货处理"),
    COMMENT_MANAGEMENT("COMMENT_MANAGEMENT", "评价管理"),
    MANAGEMENT_WAREHOUSE("MANAGEMENT_WAREHOUSE", "经营-仓库"),
    NO_BILL_RECEIVE("NO_BILL_RECEIVE", "无单收货"),
    WITH_BILL_RECEIVE("WITH_BILL_RECEIVE", "有单收货"),
    SUPPLEMENT("SUPPLEMENT", "补货"),
    PROCESS_ORDER("PROCESS_ORDER", "加工"),
    BREAKAGE_REPORT("BREAKAGE_REPORT", "报损"),
    STOCK_CHECK("STOCK_CHECK", "盘点任务"),
    STOCK_QUERY("STOCK_QUERY", "库存查询"),
    OUTBOUND_ORDER_APP("OUTBOUND_ORDER_APP", "出库"),
    DUTY_CENTER("DH-DIFFERENCE-JUDGMENT", "定责中心"),

    WMS_PENDING_PUTAWAY_TASK("WMS_PENDING_PUTAWAY_TASK", "待上架"),
    WAREHOUSE_SUPPLY_TASK("REPLENISHMENT-FUSION", "仓内补货-融合"),

    LOCATION_MANAGEMENT("LOCATION_MANAGEMENT", "库位管理"),
    BATCH_STOCK_WARNING("EXPIRE_ALERT", "批次库存预警"),
    DH_TASK_PICKING("DH-TASK-PICKING", "拣货任务"),
    DH_TASK_RECHECK("DH-TASK-RECHECK", "复核单"),
    DH_DELIVERY_SIGN_IN("DH-DELIVERY-SIGN-IN", "到货签到"),
    DELIVERY_SIGN_IN("DELIVERY_SIGN_IN", "到货签到"),
    FUSE_APPOINTMENT_SIGN_IN("FUSE-APPOINTMENT-SIGN-IN", "到货签到融合"),
    BORROW_RETURN("DH-MATERIAL-IO", "领用/归还"),
    DH_TAKE_DELIVERY_BY_ORDER("DH-TAKE-DELIVERY-BY-ORDER", "歪马收货"),
    DH_LOSS("DH-LOSS", "歪马报损"),
    DH_STOCK_CHECK("DH_STOCK_CHECK", "歪马盘点任务"),
    DH_TASK_CENTER("DH-TASK-CENTER", "任务管理"),
    DH_REPLENISHMENT("DH-REPLENISHMENT", "仓内补货"),
    DH_AUDIT_COUNT("DH-AUDIT-STOCK", "稽核盘点"),
    THIRD_PARTY_DELIVERY("THIRD_ORDER_QUERYABLE", "三方配送TAB"),
    THIRD_PARTY_WAIT_TO_DELIVERY_AND_EXCEPTION("THIRD_ORDER_EXCEPTION_QUERYABLE", "三方待配送和异常TAB"),
    THIRD_PARTY_ACTION_DELIVERY_DETAIL("THIRD_ORDER_ACTION_DELIVERY_DETAIL", "三方配送详情"),

    DELIVERY_MENU("DELIVERY_MENU", "经营-配送"),
    MANAGEMENT_EMPLOYEE("MANAGEMENT_EMPLOYEE", "经营-员工管理"),
    PREPARATIONS("PREPARATIONS", "经营-筹建"),
    DH_SEARCH_TASK_MANAGE("DH-SEARCH-TASK-MANAGE", "寻仓管理"),
    DH_SEARCH_TASK("DH-SEARCH-TASK", "寻仓任务"),
    MESSAGE_TAB("MESSAGE_TAB", "消息底部TAB"),
    MINE_TAB("MINE_TAB", "我的"),
    PRINT_SETTING("PRINT_SETTING", "打印设置"),
    PHONE_CALL_SETTING("PHONE_CALL_SETTING", "电话设置"),
    POI_MGR("POI_MGR", "门店管理"),
    POI_SEC_DEPOSIT("POI_SEC_DEPOSIT", "门店保证金"),
    SHOW_SALE_PRICE("SHOW_SALE_PRICE", "订单零售价&营收数据"),
    MALT_FARM("MAIYATIAN", "麦芽田权限code"),
    WAIMA_WECHAT_PULL_NEW("WINE_PROMOTE_CODE", "歪马送酒微信拉新"),
    ASSISTANT_OFF_SALE_IN_STOCK("ASSISTANT_OFF_SALE_IN_STOCK", "店长助手(有库存待上架)"),

    DH_INVENTORY_WARNING("DH-INVENTORY-WARNING", "库区库位预警"),
    DH_ONBOARD_AND_RESIGN("wm-board-resign", "歪马员工入离职"),
    TURN_AGG_DELIVERY("ORDER_CHANGE_TO_THIRD_DELIVERY_BUTTON", "歪马转三方配送"),

    TURN_SELF_DELIVERY("WAIMA_ORDER_CHANGE_TO_SELF_DELIVERY_BUTTON", "歪马转自配送"),
    PUNISH_MINE("PUNISH-MINE", "我的违规"),
    STALL("STREET-PROMOTION", "地摊推广"),
    EQUIPMENT_MANAGE("EQUIPMENT-MANAGE", "装备管理"),
    MY_EQUIPMENT("MY-EQUIPMENT", "骑手装备"),
    RETURN_SEAL_CONTAINER("RETURN_SEAL_CONTAINER", "归还容具"),
    SMILE_ACT("SMILE_ACT", "微笑行动"),

    SUPPLY_CHAIN_ORDER("SUPPLY_CHAIN_ORDER", "要货"),
    ;

    private final String authCode;
    private final String desc;

    AuthCodeEnum(String auth, String desc) {
        this.authCode = auth;
        this.desc = desc;
    }

    public static AuthCodeEnum authOf(String authCode) {
        for (AuthCodeEnum moduleEnum : AuthCodeEnum.values()) {
            if (moduleEnum.authCode.equals(authCode)) {
                return moduleEnum;
            }
        }
        return null;
    }
}
