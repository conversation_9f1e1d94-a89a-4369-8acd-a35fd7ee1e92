package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderItemVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.TConsumableMaterialInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/12/13 15:19
 **/
@Slf4j
public class ConsumableMaterialParseUtils {
    public static List<TConsumableMaterialInfo> parseConsumableMaterialInfo(OCMSOrderVO ocmsOrderVO) {
        try {
            log.info("开始解析耗材信息, storeID:{}, viewOrderId: {}", ocmsOrderVO.getShopId(), ocmsOrderVO.getViewOrderId() );
            List<TConsumableMaterialInfo> consumableMaterialInfos = ConsumableMaterialParseUtils.parseConsumableInfoFromBizOrderItemModel(ocmsOrderVO.getOcmsOrderItemVOList());
            log.info("结束解析耗材信息, consumableMaterialInfos:{}", consumableMaterialInfos);
            return consumableMaterialInfos;
        } catch (Exception e) {
            Cat.logEvent("CONSUME_CHECK", "PARSE_CONSUMABLE_MATERIAL_INFO_FAIL");
            log.error("解析耗材数量失败, viewOrderId: {}", ocmsOrderVO.getViewOrderId(), e);
            return Collections.emptyList();
        }
    }


    public static List<TConsumableMaterialInfo> parseConsumableInfoFromBizOrderItemModel(List<OCMSOrderItemVO> ocmsOrderItemVOS) {
        List<ConsumableMaterialAttributeDescInfo> consumableAttrDescInfoList = getConsumableAttrDescInfoList();

        if(CollectionUtils.isEmpty(consumableAttrDescInfoList)) {
            return Collections.emptyList();
        }

        List<TConsumableMaterialInfo> consumableMaterialInfos = ocmsOrderItemVOS.stream()
                .map(item -> parseConsumableMaterialInfo(item.getQuantity(), item.getPropertyList(), consumableAttrDescInfoList))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        return aggregateConsumableMaterial(consumableMaterialInfos);
    }

    public static List<TConsumableMaterialInfo> parseConsumableMaterialInfo(Integer quantity, List<String> properties,
                                                                           List<ConsumableMaterialAttributeDescInfo> consumableAttrDescInfoList) {

        Set<String> allConsumableAttrDescSet = consumableAttrDescInfoList.stream()
                .map(ConsumableMaterialAttributeDescInfo::getAttributeDescMap)
                .map(Map::keySet)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());

        Set<String> productConsumableAtrrSet = properties.stream()
                .filter(allConsumableAttrDescSet::contains)
                .collect(Collectors.toSet());


        List<TConsumableMaterialInfo> consumableMaterialInfos = new ArrayList<>();
        productConsumableAtrrSet.forEach(attr ->
                consumableAttrDescInfoList.forEach(info -> {
                    if (info.getAttributeDescMap().containsKey(attr)) {
                        TConsumableMaterialInfo consumableMaterialInfo = new TConsumableMaterialInfo();
                        consumableMaterialInfo.setCount(info.getAttributeDescMap().get(attr) * quantity);
                        consumableMaterialInfo.setSkuId(info.getSkuId());
                        consumableMaterialInfos.add(consumableMaterialInfo);
                    }
                }));

        return consumableMaterialInfos;
    }

    private static List<TConsumableMaterialInfo> aggregateConsumableMaterial(List<TConsumableMaterialInfo> consumableMaterialInfos) {
        Map<String, TConsumableMaterialInfo> materialMap = consumableMaterialInfos.stream()
                .collect(Collectors.toMap(TConsumableMaterialInfo::getSkuId, Function.identity(),
                        (material1, material2) -> {
                            material1.setCount(material1.getCount() + material2.getCount());
                            return material1;
                        }));

        return new ArrayList<>(materialMap.values());
    }

    //兼容策略：从拣货的lion出
    private static List<ConsumableMaterialAttributeDescInfo> getConsumableAttrDescInfoList() {
        return Lion.getConfigRepository("com.sankuai.waimai.sc.pickselectservice").getList("consumable.material.attribute.desc.list",
                ConsumableMaterialAttributeDescInfo.class, Collections.emptyList());
    }

    @Data
    public static class ConsumableMaterialAttributeDescInfo {
        private String skuId;

        private Map<String/*attributeDesc*/, Integer/*count*/> attributeDescMap;
    }

}
