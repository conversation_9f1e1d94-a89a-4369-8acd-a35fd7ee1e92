package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.google.common.collect.ImmutableMap;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.DxSSOConstants;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;

import static org.apache.commons.lang.CharEncoding.UTF_8;

@Slf4j
public class DxUtils {

    public static String buildSSORedirectUrl(String dxRedirect) {
        HostEnv hostEnv = Optional.ofNullable(ProcessInfoUtil.getHostEnv()).orElse(HostEnv.PROD);
        String swimlane = ProcessInfoUtil.getSwimlane();
        String ePassPortUrl = DxSSOConstants.EPASSPORT_ENV_URLS.getOrDefault(hostEnv,
                DxSSOConstants.EPASSPORT_ENV_URLS.get(HostEnv.PROD));

        String pieApiUrl = DxSSOConstants.PIEAPI_ENV_URLS.getOrDefault(hostEnv,
                DxSSOConstants.PIEAPI_ENV_URLS.get(HostEnv.PROD));
        boolean stAndOnline = HostEnv.PROD.equals(hostEnv) || HostEnv.STAGING.equals(hostEnv);
        if (!ProcessInfoUtil.isLocalHostOnline() && StringUtils.isNotEmpty(swimlane) && !stAndOnline) {
            pieApiUrl = String.format(DxSSOConstants.PIEAPI_SWIM_URLS, swimlane);
        }
        return String.format(DxSSOConstants.SSO_LOGIN_URL, ePassPortUrl,
                encodeUri(String.format(DxSSOConstants.SSO_CALLBACK_URL, pieApiUrl,
                        encodeUri(dxRedirect))));
    }

    public static String encodeUri(String uri) {
        try {
            return URLEncoder.encode(uri, DxSSOConstants.CHARSET);
        } catch (UnsupportedEncodingException e) {
            return uri;
        }
    }

    /**
     * md5签名
     *
     * @param data 签名数据
     */
    public static String md5(String data) {
        if (StringUtils.isEmpty(data)) {
            return data;
        }

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(data.getBytes());
            byte[] digest = md.digest();

            StringBuilder res = new StringBuilder();
            for (byte b : digest) {
                char ch = Character.forDigit((b >> 4) & 0xF, 16);
                res.append(ch);
                ch = Character.forDigit(b & 0xF, 16);
                res.append(ch);
            }
            return res.toString();
        } catch (NoSuchAlgorithmException ex) {
            throw new RuntimeException("md5 digest failed", ex);
        }
    }

    public static String buildErrorInfo(String msg) {
        return GsonUtil.toJsonString(ImmutableMap.of("code", "-1", "message", StringUtils.isBlank(msg) ? "系统异常登录失败" : msg));
    }

    public static void writeData2Response(HttpServletResponse response) {
        writeData2Response(response, null);
    }

    public static void writeData2Response(HttpServletResponse response, String msg) {
        if (Objects.isNull(response)) {
            return;
        }
        response.setContentType(DxSSOConstants.CONTENT_TYPE_APPLICATION_JSON);
        response.setCharacterEncoding(UTF_8);
        try (PrintWriter writer = response.getWriter()) {
            writer.write(DxUtils.buildErrorInfo(msg));

        } catch (IOException ioEx) {
            log.error("sso auth error, exception->", ioEx);
        }
    }

    public static void setToken2Cookie(HttpServletRequest request, HttpServletResponse response, String token) {
        Cookie cookie = new Cookie(DxSSOConstants.COOKIE_KEY, token);
        cookie.setDomain(request.getServerName());
        cookie.setPath(DxSSOConstants.COOKIE_PATH);
        cookie.setMaxAge(DxSSOConstants.MAX_AGE);
        cookie.setHttpOnly(true);
        response.addCookie(cookie);
        return;
    }

    public static Cookie getTokenCookie(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (ArrayUtils.isEmpty(cookies)) {
            return null;
        }
        return Arrays.stream(cookies).filter(Objects::nonNull)
                .filter(cookie -> DxSSOConstants.COOKIE_KEY.equals(cookie.getName())).findAny().orElse(null);
    }

    public static void removeToken4Cookie(HttpServletRequest request, HttpServletResponse response) {
        removeCookie(request, response, getTokenCookie(request));
    }

    public static void removeCookie(HttpServletRequest request, HttpServletResponse response, Cookie cookie) {
        if (Objects.isNull(cookie)) {
            return;
        }
        cookie.setDomain(request.getServerName());
        cookie.setPath(DxSSOConstants.COOKIE_PATH);
        cookie.setMaxAge(BigInteger.ZERO.intValue());
        response.addCookie(cookie);
    }
}
