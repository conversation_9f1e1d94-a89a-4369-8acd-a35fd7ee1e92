package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import com.meituan.linz.boot.enums.EnumBase;
import com.meituan.linz.boot.util.Assert;

/**
 * 业态枚举
 *
 * <AUTHOR>
 * @since 2022/12/27
 */
public enum ModeEnum implements EnumBase {

    B(0, "b+", "B+版"),
    MARKET(1, "supermarket", "超市版"),
    ;

    private int code;
    private String strCode;
    private String message;

    ModeEnum(int code, String strCode, String message) {
        this.code = code;
        this.strCode = strCode;
        this.message = message;
    }

    public String getStrCode() {
        return strCode;
    }

    public static ModeEnum ofStrCodeNullable(String strCode) {
        for (ModeEnum dateType : ModeEnum.values()) {
            if (dateType.getStrCode().equals(strCode)) {
                return dateType;
            }
        }
        return null;
    }



    public static ModeEnum ofStrCode(String strCode) {
        ModeEnum obj = ModeEnum.ofStrCodeNullable(strCode);
        Assert.throwIfNull(obj, "invalid code of BizModeEnum, strCode: {}", strCode);
        return obj;
    }
    @Override
    public int code() {
        return this.code;
    }

    @Override
    public String message() {
        return this.message;
    }
}
