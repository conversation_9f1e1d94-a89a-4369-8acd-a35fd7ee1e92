package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryPoiRelationStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryStoreCategoryByConditionRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.enums.StoreCategorySceneIdentityEnum;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category.DeleteMerchantStoreCategoryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "查询牵牛花门店店内分类请求"
)
@Data
@ApiModel("查询牵牛花门店店内分类请求")
public class QueryPoiStoreCategoryRequest {

    @FieldDoc(
            description = "门店ID",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    private Long storeId;

    @FieldDoc(
            description = "搜索关键词(分类名称)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "搜索关键词(分类名称)", required = true)
    private String keyWord;

    public QueryStoreCategoryByConditionRequest to(User user, Integer sceneIdentity) {
        QueryStoreCategoryByConditionRequest rpcReq = new QueryStoreCategoryByConditionRequest();
        rpcReq.setTenantId(user.getTenantId());
        rpcReq.setStoreId(storeId);
        rpcReq.setSceneIdentity(sceneIdentity);
        rpcReq.setKeyWord(keyWord);
        return rpcReq;
    }


    public QueryPoiRelationStoreCategoryRequest toRelationRequest(User user) {
        QueryPoiRelationStoreCategoryRequest rpcReq = new QueryPoiRelationStoreCategoryRequest();
        rpcReq.setTenantId(user.getTenantId());
        rpcReq.setStoreId(storeId);
        rpcReq.setKeyWord(keyWord);
        return rpcReq;
    }

}
