package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.StoreSpuOnlineParamVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: wanghongzhen
 * @date: 2020-05-11 17:02
 */
@TypeDoc(
        description = "商品批量上下架请求"
)
@Data
@ApiModel("商品批量上下架请求")
public class BatchChangeSpuStatusRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商品编码列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品编码列表", required = true)
    private List<String> spuIdList;

    @FieldDoc(
            description = "商品编码列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品编码列表(目前单个SPU从未上线状态点击上架时(且租户为手动定价)，前端强制要求输入渠道价格, 使用spuList，其余场景使用spuIdList)", required = true)
    private List<StoreSpuOnlineParamVO> spuList;

    @FieldDoc(
            description = "sku状态 1-上架 2-下架", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "sku状态 1-上架 2-下架", required = true)
    @NotNull
    private Integer spuStatus;

    @FieldDoc(
            description = "渠道列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道列表", required = true)
    private List<Integer> channelIds;

    @FieldDoc(
            description = "是否自动上架 1-是 0-否", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否自动上架 1-是 0-否")
    private Integer autoOnSale;

    @FieldDoc(
            description = "分类名称列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类名称列表")
    private List<Long> storeCategorys;

    @FieldDoc(
            description = "是否验证存在任一待审核状态的sku （true-校验，false-不校验）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否验证存在任一待审核状态的sku")
    private boolean checkPermit = false;

    @FieldDoc(
            description = "上架校验 （true-校验，false-不校验）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "上架校验")
    private boolean checkOnSale = false;
}
