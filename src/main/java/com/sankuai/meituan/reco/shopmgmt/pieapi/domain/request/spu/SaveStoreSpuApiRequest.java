package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.CreateStockAdjustRequestVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuCreateVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.OperateSourceTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.SaveStoreSpuRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OperateSourceEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.SaveTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.CreateStoreSpuAndOnlineRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.UpdateStoreSpuAndOnlineRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.constraints.NotNull;

import java.util.List;
import java.util.Objects;

/**
 * @Title: SaveStoreSpuApiRequest
 * @Description: 保存门店商品并上线请求参数
 * @Author: zhaolei12
 * @Date: 2020/5/16 8:21 下午
 */
@TypeDoc(
        description = "保存门店商品并上线请求参数"
)
@Data
@ApiModel("保存门店商品并上线请求参数")
public class SaveStoreSpuApiRequest {

    @FieldDoc(
            description = "1-新建，2-更新", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "1-新建，2-更新", required = true)
    @NotNull
    private Integer saveType;

    @FieldDoc(
            description = "租户id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户id")
    private Long tenantId;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "门店商品信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店商品信息", required = true)
    @NotNull
    private StoreSpuCreateVO storeSpu;

    @FieldDoc(
            description = "是否无限库存配置下提交（仅App使用）"
    )
    @ApiModelProperty(name = "无限库存的标志位")
    private Boolean infiniteInventory;

    @FieldDoc(
            description = "创建门店商品是否新建报价（true-报价，false-不报价）"
    )
    @ApiModelProperty(name = "创建门店商品是否新建报价")
    private boolean quoteForCreate = false;

    @FieldDoc(
            description = "是否作废待审核报价（true-删除，false-不删除）"
    )
    @ApiModelProperty(name = "是否作废待审核报价")
    private boolean deleteToReviewQuote = false;

    @FieldDoc(
            description = "0为初始化非手动定价，1为手动定价"
    )
    @ApiModelProperty(name = "定价方式")
    private Integer priceInitType;

    @FieldDoc(description = "是否使用新库存调整链路")
    @ApiModelProperty(value = "是否使用新库存调整链路")
    private boolean useNewAdjustStock;

    @FieldDoc(description = "库存调整请求")
    @ApiModelProperty(value = "库存调整请求")
    private CreateStockAdjustRequestVo stockAdjustRequestVo;
    public static SaveStoreSpuRequest toRpcRequest(SaveStoreSpuApiRequest request, User user) {
        SaveStoreSpuRequest saveStoreSpuRequest = new SaveStoreSpuRequest();
        saveStoreSpuRequest.setTenantId(user.getTenantId());
        saveStoreSpuRequest.setOperatorId(user.getAccountId());
        saveStoreSpuRequest.setOperatorName(user.getOperatorName());
        saveStoreSpuRequest.setOperateSource(OperateSourceEnum.EMPOWER_ASSISTENT_APP);
        saveStoreSpuRequest.setStoreId(request.getStoreId());
        saveStoreSpuRequest.setSaveType(request.getSaveType());
        saveStoreSpuRequest.setInfiniteInventory(request.getInfiniteInventory());
        saveStoreSpuRequest.setUseNewAdjustStock(request.isUseNewAdjustStock());

        saveStoreSpuRequest.setStoreSpu(StoreSpuCreateVO.toDTO(request.getTenantId(), request.getStoreId(), request.getStoreSpu()));

        // 新增时，app端没有传上下架状态，需要给默认值2(下架状态)。
        if (request.getSaveType() == SaveTypeEnum.CREATE.getValue() &&
                CollectionUtils.isNotEmpty(saveStoreSpuRequest.getStoreSpu().getChannelSpuList())) {
            saveStoreSpuRequest.getStoreSpu().getChannelSpuList().forEach(channelSpuDTO -> {
                // 蔬果派管理模式下，app建品会传入上下架状态; 空才会进入旧逻辑  https://ones.sankuai.com/ones/product/8969/workItem/requirement/detail/64955025
                if (Objects.isNull(channelSpuDTO.getSpuStatus())) {
                    // 旧逻辑，默认下架 （目前有存量版本单规格会走这里）
                    channelSpuDTO.setSpuStatus(2);
                }
                channelSpuDTO.setTenantId(user.getTenantId());
                channelSpuDTO.setStoreId(request.getStoreId());
            });
        }

        return saveStoreSpuRequest;
    }

    public static CreateStoreSpuAndOnlineRequest toCreateStoreSpuAndOnlineRequest(SaveStoreSpuApiRequest request, User user,
                                                                                  boolean notMerchantChargeGray,
                                                                                  boolean isStoreManagementTenant,
                                                                                  boolean merchantCharge) {
        CreateStoreSpuAndOnlineRequest createStoreSpuAndOnlineRequest = new CreateStoreSpuAndOnlineRequest();
        createStoreSpuAndOnlineRequest.setTenantId(user.getTenantId());
        createStoreSpuAndOnlineRequest.setOperatorId(user.getAccountId());
        createStoreSpuAndOnlineRequest.setOperatorName(user.getOperatorName());

        createStoreSpuAndOnlineRequest.setOperateSource(OperateSourceTypeEnum.APP.getCode());
        createStoreSpuAndOnlineRequest.setStoreId(request.getStoreId());

        createStoreSpuAndOnlineRequest.setStoreSpu(StoreSpuCreateVO.toStoreSpuOnlineDTO(request.getTenantId(),
                request.getStoreId(), request.getStoreSpu(), notMerchantChargeGray, isStoreManagementTenant, merchantCharge));
        createStoreSpuAndOnlineRequest.setUseNewAdjustStock(request.isUseNewAdjustStock());

        return createStoreSpuAndOnlineRequest;
    }

    public static UpdateStoreSpuAndOnlineRequest toUpdateStoreSpuAndOnlineRequest(SaveStoreSpuApiRequest request, User user,
                                                                                  boolean notMerchantChargeGray,
                                                                                  boolean isStoreManagementTenant,
                                                                                  boolean merchantCharge) {
        UpdateStoreSpuAndOnlineRequest updateStoreSpuAndOnlineRequest = new UpdateStoreSpuAndOnlineRequest();

        updateStoreSpuAndOnlineRequest.setTenantId(user.getTenantId());
        updateStoreSpuAndOnlineRequest.setStoreId(request.getStoreId());
        updateStoreSpuAndOnlineRequest.setOperatorId(user.getAccountId());
        updateStoreSpuAndOnlineRequest.setOperatorName(user.getOperatorName());
        updateStoreSpuAndOnlineRequest.setOperatorAccount(user.getAccountName());
        updateStoreSpuAndOnlineRequest.setSource(OperateSourceTypeEnum.APP.getCode());
        updateStoreSpuAndOnlineRequest.setStoreSpuOnlineDTO(StoreSpuCreateVO.toStoreSpuOnlineDTO(request.getTenantId(),
                request.getStoreId(), request.getStoreSpu(), notMerchantChargeGray, isStoreManagementTenant, merchantCharge));
        updateStoreSpuAndOnlineRequest.setUseNewAdjustStock(request.isUseNewAdjustStock());

        return updateStoreSpuAndOnlineRequest;
    }


}
