package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StoreSkuPropertyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.ChannelBrandRelationVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.container.SpuContainer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.AiRecommendVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSaleAttrInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.MedicalDeviceQuaInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.NameSupplementInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.SpecialPictureVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreGroupCategoryCodeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSpecialPictureVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.SaleAttrCompatUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSaleAttributeDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ProductPropertyDTO;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelDynamicInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.VideoInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.StanderTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.WeightTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.BrandDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.CategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SaleAttributeDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.TenantSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.TenantStoreCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.UpdateTenantSpuRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.OperateInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.PoiGroupInfoDto;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuChannelSaleAttributeDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuSaleAttributeDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuPropertyDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSpuBizDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantStoreCategoryBizDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.OperateTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.BatchUpdateTenantSpuRequest;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.enums.ProductOperateSourceEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Title: TenantSpuAddOrUpdateRequest
 * @Description: 新增或更新租户商品请求参数
 * @Author: wuyongjiang
 * @Date: 2022/8/29 11:02
 */
@EqualsAndHashCode(callSuper = true)
@TypeDoc(
        description = "新增或更新租户商品请求参数"
)
@Data
@ApiModel("新增或更新租户商品请求参数")
public class TenantSpuAddOrUpdateApiRequest extends SpuContainer {
    @FieldDoc(
            description = "保存类型，edit, create ", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "保存类型", required = true)
    private String saveType;

    @FieldDoc(
            description = "spu", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "spu", required = true)
    private String spu;

    @FieldDoc(
            description = "spu名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "spu名称", required = true)
    private String name;

    @FieldDoc(
            description = "商品类目"
    )
    @ApiModelProperty(value = "商品类目")
    private String categoryId;

    @FieldDoc(
            description = "品牌"
    )
    @ApiModelProperty(value = "品牌")
    private String brandId;

    @FieldDoc(
            description = "产地 "
    )
    @ApiModelProperty(value = "产地")
    private String origin;

    @FieldDoc(
            description = "图片 ",requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "图片",required = true)
    private List<String> picUrlList;

    @FieldDoc(
            description = "图片详情 "
    )
    @ApiModelProperty(value = "图片详情")
    private List<String> pictureContents;

    @FieldDoc(
            description = "视频信息"
    )
    @ApiModelProperty(value = "视频")
    private VideoInfoVO videoInfo;

    @FieldDoc(
            description = "是否标品 "
    )
    @ApiModelProperty(value = "是否标品")
    private int standerType;

    @FieldDoc(
            description = "spu下的sku",requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "spu下的sku",required = true)
    private List<TenantSkuVO> skus;
    
    @FieldDoc(
            description = "美团渠道类目 ",requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "美团渠道类目",required = true)
    private String mtCategoryId;

    @FieldDoc(
            description = "抖音渠道类目 ",requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "抖音渠道类目",required = true)
    private String douyinCategoryId;

    @FieldDoc(
            description = "是否允许自定义规格描述 "
    )
    @ApiModelProperty(value = "是否允许自定义规格描述")
    private Boolean canCustomizeSpec;

    @FieldDoc(
            description = "饿了么渠道类目",requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "饿了么渠道类目",required = true)
    private String elmCategoryId;

    @FieldDoc(
            description = "规格类型"
    )
    @ApiModelProperty(value = "规格类型(1单规格2多规格")
    private Integer specType;

    @FieldDoc(
            description = "商品属性 不超过十个属性"
    )
    @ApiModelProperty(name = "商品属性")
    private List<StoreSkuPropertyVO> properties;

    @FieldDoc(
            description = "商品卖点"
    )
    @ApiModelProperty(value = "商品卖点")
    private String sellingPoint;

    @FieldDoc(
            description = "商品描述"
    )
    @ApiModelProperty(value = "商品描述")
    private String description;

    @FieldDoc(
            description = "店内分类列表"
    )
    @ApiModelProperty(value = "店内分类列表")
    private List<String> storeCategoryCodes;

    @FieldDoc(
            description = "美团渠道类目动态信息"
    )
    @ApiModelProperty(name = "美团渠道类目动态信息")
    private List<ChannelDynamicInfoVO> channelDynamicInfoVOList;

    @FieldDoc(
            description = "饿了么类目属性"
    )
    private List<ChannelDynamicInfoVO> elmChannelDynamicInfoVOList;

    @FieldDoc(
            description = "抖音渠道类目动态信息"
    )
    @ApiModelProperty(name = "抖音渠道类目动态信息")
    private List<ChannelDynamicInfoVO> douyinChannelDynamicInfoVOList;


    @FieldDoc(
            description = "抖音售后服务"
    )
    @ApiModelProperty(name = "抖音售后服务")
    private String douyinAfterSaleServiceType;

    @FieldDoc(
            description = "是否控品，1是，0否"
    )
    @ApiModelProperty("是否控品")
    private Integer controlProduct;

    @FieldDoc(
            description = "加盟区域id列表"
    )
    @ApiModelProperty("加盟区域id列表")
    private List<Integer> joinAreaIds;

    @FieldDoc(
            description = "上下架状态1上架，2下架"
    )
    @ApiModelProperty("上下架状态1上架，2下架")
    private Integer saleStatus;

    @FieldDoc(
            description = "加盟主商品id"
    )
    @ApiModelProperty("加盟主商品id")
    private String franchisorControlProductSpuId;

    @FieldDoc(
            description = "京东渠道类目ID"
    )
    @ApiModelProperty("京东渠道类目ID")
    private String jdCategoryId;

    @FieldDoc(
            description = "店内分类分组列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "店内分类分组列表")
    private List<StoreGroupCategoryCodeVO> storeGroupCategoryCodes;

    @FieldDoc(
            description = "京东渠道配送要求", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东渠道配送要求")
    private Integer deliveryRequirement;

    @FieldDoc(
            description = "京东渠道品牌", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东渠道品牌")
    private List<ChannelBrandRelationVO> channelBrand;

    @FieldDoc(
            description = "京东渠道销售属性", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东渠道销售属性")
    private List<SaleAttrVo> jdSaleAttrList;

    @FieldDoc(
            description = "需要推送的字段列表，参考com.sankuai.meituan.shangou.empower.productbiz.enums.PushFieldEnum"
    )
    private List<String> needPushFieldList;

    @FieldDoc(
            description = "医疗器械资质相关信息",
            rule = "渠道类目为部分医疗器械分类时，该字段必填",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty("医疗器械资质相关信息")
    private MedicalDeviceQuaInfoVO medicalDeviceQuaInfo;

    @FieldDoc(
            description = "美团售后服务", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty("美团售后服务")
    private String mtAfterSaleServiceType;

    @FieldDoc(
            description = "货盘归属：1-总部货盘，2-门店货盘"
    )
    @ApiModelProperty(value = "货盘归属：1-总部货盘，2-门店货盘")
    private Integer palletSrc;
    @FieldDoc(
            description = "门店分组Id列表"
    )
    @ApiModelProperty(value = "门店分组Id列表")
    private List<Integer>poiGroupIdList;

    @FieldDoc(
            description = "特殊管控商品资质"
    )
    @ApiModelProperty("特殊管控商品资质")
    private List<String> controlQuaPicUrl;

    @FieldDoc(
            description = "AI推荐信息"
    )
    @ApiModelProperty(value = "AI推荐信息")
    private AiRecommendVO aiRecommendVO;

    @FieldDoc(
            description = "商品名称补充语信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品名称补充语信息")
    private NameSupplementInfoVO nameSupplementInfo;

    @FieldDoc(
            description = "美团类目特殊图"
    )
    @ApiModelProperty(value = "美团类目特殊图")
    private List<TenantSpecialPictureVO> mtSpecialPictureList;

    public static UpdateTenantSpuRequest toRpcRequest(TenantSpuAddOrUpdateApiRequest request, User user) {
        UpdateTenantSpuRequest updateTenantSpuRequest = new UpdateTenantSpuRequest();
        updateTenantSpuRequest.setOperateSource(3);
        updateTenantSpuRequest.setOperatorId(user.getAccountId());
        updateTenantSpuRequest.setOperatorName(user.getOperatorName());

        TenantSpuDTO tenantSpuDTO = new TenantSpuDTO();
        tenantSpuDTO.setTenantId(user.getTenantId());
        tenantSpuDTO.setBrand(request.fetchBrandDto());
        tenantSpuDTO.setSpuId(request.getSpu());
        tenantSpuDTO.setName(request.getName());
        tenantSpuDTO.setCategory(request.fetchCategoryDto());
        tenantSpuDTO.setProducingPlace(request.getOrigin());
        tenantSpuDTO.setImageUrls(request.getPicUrlList());
        tenantSpuDTO.setPictureContents(request.getPictureContents());
        if(request.getVideoInfo()!=null){
            tenantSpuDTO.setVideoInfo(request.getVideoInfo().toVideoInfoDTO());
        }
        tenantSpuDTO.setWeightType(request.fetchWeightType());
        tenantSpuDTO.setSpecType(request.fetchSpecType());
        tenantSpuDTO.setTenantSkuDTOList(Optional.ofNullable(request.getSkus()).map(List::stream).orElse(Stream.empty())
                        .filter(Objects::nonNull).map(TenantSkuVO::toDTO).collect(Collectors.toList()));
        tenantSpuDTO.setAllowCustomizeSpec(request.getCanCustomizeSpec());
        tenantSpuDTO.setProperties(fetchProductPropertyDTO(request.getProperties()));
        tenantSpuDTO.setSellingPoint(request.getSellingPoint());
        tenantSpuDTO.setDescription(request.getDescription());
        tenantSpuDTO.setStoreCategoryList(request.fetchTenantCategoryDto());

        ChannelCategoryDTO mtChannelCategoryDTO = new ChannelCategoryDTO();
        mtChannelCategoryDTO.setChannelCategoryCode(request.getMtCategoryId());
        if(CollectionUtils.isNotEmpty(request.getChannelDynamicInfoVOList())){
            mtChannelCategoryDTO.setChannelDynamicInfoDTOList(ChannelDynamicInfoVO.toDTOList(request.getChannelDynamicInfoVOList()));
        }
        mtChannelCategoryDTO.setSaleAttributes(request.fetchSaleAttrsForOcms(EnhanceChannelType.MT.getChannelId()));
        tenantSpuDTO.setMtChannelCategory(mtChannelCategoryDTO);

        ChannelCategoryDTO elmChannelCategoryDTO = new ChannelCategoryDTO();
        elmChannelCategoryDTO.setChannelCategoryCode(request.getElmCategoryId());
        if(CollectionUtils.isNotEmpty(request.getElmChannelDynamicInfoVOList())){
            elmChannelCategoryDTO.setChannelDynamicInfoDTOList(ChannelDynamicInfoVO.toDTOList(request.getElmChannelDynamicInfoVOList()));
        }
        elmChannelCategoryDTO.setSaleAttributes(request.fetchSaleAttrsForOcms(EnhanceChannelType.ELEM.getChannelId()));
        tenantSpuDTO.setElmChannelCategory(elmChannelCategoryDTO);

        ChannelCategoryDTO douyinChannelCategoryDTO = new ChannelCategoryDTO();
        douyinChannelCategoryDTO.setChannelCategoryCode(request.getDouyinCategoryId());
        if(CollectionUtils.isNotEmpty(request.getDouyinChannelDynamicInfoVOList())){
            douyinChannelCategoryDTO.setChannelDynamicInfoDTOList(ChannelDynamicInfoVO.toDTOList(request.getDouyinChannelDynamicInfoVOList()));
        }
        douyinChannelCategoryDTO.setSaleAttributes(request.fetchSaleAttrsForOcms(EnhanceChannelType.DY.getChannelId()));
        tenantSpuDTO.setDouyinChannelCategoryInfo(douyinChannelCategoryDTO);
        tenantSpuDTO.setDouyinAfterSaleServiceType(request.getDouyinAfterSaleServiceType());
        tenantSpuDTO.setMtAfterSaleServiceType(request.getMtAfterSaleServiceType());
        if (Objects.nonNull(request.getJdCategoryId())) {
            ChannelCategoryDTO jdChannelCategoryDTO = new ChannelCategoryDTO();
            jdChannelCategoryDTO.setChannelCategoryCode(request.getJdCategoryId());
            jdChannelCategoryDTO.setSaleAttributes(request.fetchSaleAttrsForOcms(EnhanceChannelType.JDDJ.getChannelId()));
            jdChannelCategoryDTO.setSkuImageSetting(request.fetchSkuImageSetting(EnhanceChannelType.JDDJ.getChannelId()));
            tenantSpuDTO.setJdChannelCategory(jdChannelCategoryDTO);
        }
        if (CollectionUtils.isNotEmpty(request.getStoreGroupCategoryCodes())) {
            tenantSpuDTO.setStoreGroupCategoryList(Fun.map(request.getStoreGroupCategoryCodes(),
                    StoreGroupCategoryCodeVO::toDTO));
        }
        tenantSpuDTO.setDeliveryRequirement(request.getDeliveryRequirement());
        if (CollectionUtils.isNotEmpty(request.getChannelBrand())) {
            tenantSpuDTO.setChannelBrands(Fun.map(request.getChannelBrand(), ChannelBrandRelationVO::toDTO));
        }
        if (CollectionUtils.isNotEmpty(request.getJdSaleAttrList())) {
            tenantSpuDTO.setJdSaleAttributeList(Fun.map(request.getJdSaleAttrList(), SaleAttrVo::toOcmsDTO));
        }
        tenantSpuDTO.setControlProduct(request.getControlProduct());
        tenantSpuDTO.setJoinAreaIds(request.getJoinAreaIds());
        tenantSpuDTO.setSaleStatus(request.getSaleStatus());
        tenantSpuDTO.setFranchisorControlProductSpuId(request.getFranchisorControlProductSpuId());
        if (request.getMedicalDeviceQuaInfo() != null) {
            tenantSpuDTO.setMedicalDeviceQuaInfo(request.getMedicalDeviceQuaInfo().toOcmsMedicalDeviceQuaInfoDTO());
        }
        tenantSpuDTO.setPalletSrc(request.getPalletSrc());
        tenantSpuDTO.setPoiGroupIdList(request.getPoiGroupIdList());
        tenantSpuDTO.setControlQuaPicUrl(request.getControlQuaPicUrl());

        tenantSpuDTO.setAiRecommendDTO(AiRecommendVO.convertAiRecommendOCMSDTO(request.getAiRecommendVO()));
        tenantSpuDTO.setNameSupplementInfo(NameSupplementInfoVO.convertToDTO(request.getNameSupplementInfo()));
        if(CollectionUtils.isNotEmpty(request.getMtSpecialPictureList())) {
            tenantSpuDTO.setMtSpecialPictureList(Fun.map(request.getMtSpecialPictureList(), TenantSpecialPictureVO::toMtSpecialPictureDTO));
        }
        updateTenantSpuRequest.setTenantSpu(tenantSpuDTO);
        return SaleAttrCompatUtils.compat(updateTenantSpuRequest);
    }

    public BrandDTO fetchBrandDto(){
        BrandDTO brandDTO = new BrandDTO();
        brandDTO.setBrandCode(brandId);
        return brandDTO;
    }
    public CategoryDTO fetchCategoryDto(){
        CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setCategoryCode(categoryId);
        return categoryDTO;
    }

    public int fetchWeightType() {
        StanderTypeEnum standerTypeEnum = StanderTypeEnum.findByCode(standerType);
        Preconditions.checkArgument(standerTypeEnum != null, "standerType非法");
        if (standerTypeEnum == StanderTypeEnum.STANDER) {
            return WeightTypeEnum.NONE.getCode();
        } else {
            return WeightTypeEnum.WEIGHT.getCode();
        }
    }

    public Integer fetchSpecType() {
        if (Objects.isNull(specType)) {
            specType = 2;
        }
        return specType;
    }

    public List<TenantStoreCategoryDTO> fetchTenantCategoryDto() {
        if (CollectionUtils.isEmpty(storeCategoryCodes)) {
            return Lists.newArrayList();
        }
        return storeCategoryCodes.stream().map(storeCategoryCode -> {
            TenantStoreCategoryDTO tenantStoreCategoryDTO = new TenantStoreCategoryDTO();
            tenantStoreCategoryDTO.setCategoryId(Long.valueOf(storeCategoryCode));
            return tenantStoreCategoryDTO;
        }).collect(Collectors.toList());
    }

    public static List<ProductPropertyDTO> fetchProductPropertyDTO(List<StoreSkuPropertyVO> storeSkuPropertyVOList) {
        List<ProductPropertyDTO> productPropertyDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(storeSkuPropertyVOList)) {
            return productPropertyDTOList;
        }

        if (CollectionUtils.isNotEmpty(storeSkuPropertyVOList)) {
            for (StoreSkuPropertyVO storeSkuPropertyVO : storeSkuPropertyVOList) {
                if (storeSkuPropertyVO != null) {
                    productPropertyDTOList.add(storeSkuPropertyVO.buildProductPropertyDTO());
                }
            }
        }
        return productPropertyDTOList;
    }

    public ChannelSaleAttrInfoVO getChannelSaleAttrInfo(int channelId) {
        if (getChannelSaleAttrInfoList() == null) {
            return null;
        }
        return getChannelSaleAttrInfoList().stream()
                .filter(channelSaleAttrInfo -> channelSaleAttrInfo.getChannelId() == channelId)
                .findFirst()
                .orElse(null);
    }

    public List<SaleAttributeDTO> fetchSaleAttrsForOcms(int channelId) {
        return Optional.ofNullable(getChannelSaleAttrInfo(channelId))
                .map(ChannelSaleAttrInfoVO::toOcmsDTO)
                .map(ChannelSaleAttributeDTO::getSaleAttributes)
                .orElse(Collections.emptyList());
    }

    public Boolean fetchSkuImageSetting(int channelId) {
        return Optional.ofNullable(getChannelSaleAttrInfo(channelId))
                .map(ChannelSaleAttrInfoVO::getSkuImageSetting)
                .orElse(null);
    }

    public static BatchUpdateTenantSpuRequest toBizRpcRequest(TenantSpuAddOrUpdateApiRequest request, User user) {
        BatchUpdateTenantSpuRequest updateTenantSpuRequest = new BatchUpdateTenantSpuRequest();
        updateTenantSpuRequest.setTenantId(user.getTenantId());
        updateTenantSpuRequest.setOperateInfo(buildOperateInfoDTO(user));

        TenantSpuBizDTO tenantSpuDTO = new TenantSpuBizDTO();
        tenantSpuDTO.setTenantId(user.getTenantId());
        tenantSpuDTO.setBrand(request.fetchBizBrandDto());
        tenantSpuDTO.setSpuId(request.getSpu());
        tenantSpuDTO.setName(request.getName());
        tenantSpuDTO.setCategory(request.fetchBizCategoryDto());
        tenantSpuDTO.setProducingPlace(request.getOrigin());
        tenantSpuDTO.setImageUrls(request.getPicUrlList());
        tenantSpuDTO.setPictureContents(request.getPictureContents());
        if (request.getVideoInfo()!=null) {
            tenantSpuDTO.setVideoInfo(request.getVideoInfo().toBizVideoInfoDTO());
        }
        tenantSpuDTO.setWeightType(request.fetchWeightType());
        tenantSpuDTO.setSpecType(request.fetchSpecType());
        tenantSpuDTO.setTenantSkuDTOList(Optional.ofNullable(request.getSkus()).map(List::stream).orElse(Stream.empty())
                .filter(Objects::nonNull).map(TenantSkuVO::toBizDTO).collect(Collectors.toList()));
        tenantSpuDTO.setAllowCustomizeSpec(request.getCanCustomizeSpec());
        tenantSpuDTO.setProperties(fetchBizProductPropertyDTO(request.getProperties()));
        tenantSpuDTO.setSellingPoint(request.getSellingPoint());
        tenantSpuDTO.setDescription(request.getDescription());
        tenantSpuDTO.setStoreCategoryList(request.fetchBizTenantCategoryDto());

        com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO mtChannelCategoryDTO = new com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO();
        mtChannelCategoryDTO.setChannelCategoryCode(request.getMtCategoryId());
        if(CollectionUtils.isNotEmpty(request.getChannelDynamicInfoVOList())){
            mtChannelCategoryDTO.setChannelDynamicInfoDTOList(ChannelDynamicInfoVO.toBizDTOList(request.getChannelDynamicInfoVOList()));
        }
        mtChannelCategoryDTO.setSaleAttrList(request.fetchSaleAttrsForBiz(EnhanceChannelType.MT.getChannelId()));
        tenantSpuDTO.setMtChannelCategory(mtChannelCategoryDTO);

        com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO elmChannelCategoryDTO = new com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO();
        elmChannelCategoryDTO.setChannelCategoryCode(request.getElmCategoryId());
        if(CollectionUtils.isNotEmpty(request.getElmChannelDynamicInfoVOList())){
            elmChannelCategoryDTO.setChannelDynamicInfoDTOList(ChannelDynamicInfoVO.toBizDTOList(request.getElmChannelDynamicInfoVOList()));
        }
        elmChannelCategoryDTO.setSaleAttrList(request.fetchSaleAttrsForBiz(EnhanceChannelType.ELEM.getChannelId()));
        tenantSpuDTO.setElmChannelCategory(elmChannelCategoryDTO);

        com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO douyinChannelCategoryDTO = new com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO();
        douyinChannelCategoryDTO.setChannelCategoryCode(request.getDouyinCategoryId());
        if(CollectionUtils.isNotEmpty(request.getDouyinChannelDynamicInfoVOList())){
            douyinChannelCategoryDTO.setChannelDynamicInfoDTOList(ChannelDynamicInfoVO.toBizDTOList(request.getDouyinChannelDynamicInfoVOList()));
        }
        douyinChannelCategoryDTO.setSaleAttrList(request.fetchSaleAttrsForBiz(EnhanceChannelType.DY.getChannelId()));
        tenantSpuDTO.setDouyinChannelCategory(douyinChannelCategoryDTO);
        tenantSpuDTO.setDouyinAfterSaleServiceType(request.getDouyinAfterSaleServiceType());
        tenantSpuDTO.setMtAfterSaleServiceType(request.getMtAfterSaleServiceType());
        if (Objects.nonNull(request.getJdCategoryId())) {
            com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO jdChannelCategoryDTO = new com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO();
            jdChannelCategoryDTO.setChannelCategoryCode(request.getJdCategoryId());
            jdChannelCategoryDTO.setSaleAttrList(request.fetchSaleAttrsForBiz(EnhanceChannelType.JDDJ.getChannelId()));
            tenantSpuDTO.setJdChannelCategory(jdChannelCategoryDTO);
        }
        if (CollectionUtils.isNotEmpty(request.getStoreGroupCategoryCodes())) {
            tenantSpuDTO.setStoreGroupCategoryList(Fun.map(request.getStoreGroupCategoryCodes(),
                    StoreGroupCategoryCodeVO::toBizDTO));
        }
        tenantSpuDTO.setDeliveryRequirement(request.getDeliveryRequirement());
        if (CollectionUtils.isNotEmpty(request.getChannelBrand())) {
            tenantSpuDTO.setChannelBrandRelationDTOS(Fun.map(request.getChannelBrand(), ChannelBrandRelationVO::toBizDTO));
        }
        if (CollectionUtils.isNotEmpty(request.getJdSaleAttrList())) {
            tenantSpuDTO.setJdSaleAttributeList(Fun.map(request.getJdSaleAttrList(), SaleAttrVo::toBizDTO));
        }
        tenantSpuDTO.setControlProduct(request.getControlProduct());
        tenantSpuDTO.setJoinAreaIds(request.getJoinAreaIds());
        tenantSpuDTO.setSaleStatus(request.getSaleStatus());
        tenantSpuDTO.setFranchisorControlProductSpuId(request.getFranchisorControlProductSpuId());
        if (request.getMedicalDeviceQuaInfo() != null) {
            tenantSpuDTO.setMedicalDeviceQuaInfo(request.getMedicalDeviceQuaInfo().toMedicalDeviceQuaInfoDTO());
        }
        tenantSpuDTO.setPalletSrc(request.getPalletSrc());
        tenantSpuDTO.setPoiGroupInfoDtoList(convertPoiGroupInfoList(request.getPoiGroupIdList()));
        tenantSpuDTO.setAiRecommendInfo(AiRecommendVO.convertAiRecommendBizDTO(request.getAiRecommendVO()));
        tenantSpuDTO.setNameSupplementInfo(NameSupplementInfoVO.convertToBizDTO(request.getNameSupplementInfo()));
        tenantSpuDTO.setMtSpecialPictureList(Fun.map(request.getMtSpecialPictureList(), TenantSpecialPictureVO::toDTO));
        updateTenantSpuRequest.setTenantSpus(Collections.singletonList(tenantSpuDTO));
        return SaleAttrCompatUtils.compat(updateTenantSpuRequest);
    }

    private static OperateInfoDTO buildOperateInfoDTO(User user) {
        OperateInfoDTO operateInfoDTO = new OperateInfoDTO();
        operateInfoDTO.setOperateId(user.getAccountId());
        operateInfoDTO.setOperateName(user.getOperatorName());
        operateInfoDTO.setSourceType(ProductOperateSourceEnum.APP.getCode());
        operateInfoDTO.setOperateType(OperateTypeEnum.UPDATE.getType());
        operateInfoDTO.setScenes("APP编辑商品池商品");
        return operateInfoDTO;
    }

    private List<SpuSaleAttributeDTO> fetchSaleAttrsForBiz(int channelId) {
        return Optional.ofNullable(getChannelSaleAttrInfo(channelId))
                .map(ChannelSaleAttrInfoVO::toBizDTO)
                .map(SpuChannelSaleAttributeDTO::getSpuSaleAttributes)
                .orElse(Collections.emptyList());
    }

    private com.sankuai.meituan.shangou.empower.productbiz.client.dto.BrandDTO fetchBizBrandDto() {
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.BrandDTO brandDTO = new com.sankuai.meituan.shangou.empower.productbiz.client.dto.BrandDTO();
        brandDTO.setBrandCode(brandId);
        return brandDTO;
    }

    private com.sankuai.meituan.shangou.empower.productbiz.client.dto.CategoryDTO fetchBizCategoryDto(){
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.CategoryDTO categoryDTO = new com.sankuai.meituan.shangou.empower.productbiz.client.dto.CategoryDTO();
        categoryDTO.setCategoryCode(categoryId);
        return categoryDTO;
    }

    private static List<StoreSkuPropertyDTO> fetchBizProductPropertyDTO(List<StoreSkuPropertyVO> storeSkuPropertyVOList) {
        List<StoreSkuPropertyDTO> productPropertyDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(storeSkuPropertyVOList)) {
            return productPropertyDTOList;
        }

        if (CollectionUtils.isNotEmpty(storeSkuPropertyVOList)) {
            for (StoreSkuPropertyVO storeSkuPropertyVO : storeSkuPropertyVOList) {
                if (storeSkuPropertyVO != null) {
                    productPropertyDTOList.add(storeSkuPropertyVO.buildProductSkutPropertyDTO());
                }
            }
        }
        return productPropertyDTOList;
    }

    private List<TenantStoreCategoryBizDTO> fetchBizTenantCategoryDto() {
        if (CollectionUtils.isEmpty(storeCategoryCodes)) {
            return Lists.newArrayList();
        }
        return storeCategoryCodes.stream().map(storeCategoryCode -> {
            TenantStoreCategoryBizDTO tenantStoreCategoryBizDTO = new TenantStoreCategoryBizDTO();
            tenantStoreCategoryBizDTO.setCategoryId(Long.valueOf(storeCategoryCode));
            return tenantStoreCategoryBizDTO;
        }).collect(Collectors.toList());
    }

    private static List<PoiGroupInfoDto> convertPoiGroupInfoList(List<Integer> poiGroupIdList){
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(poiGroupIdList)) {
            return null;
        }

        return Fun.map(poiGroupIdList, groupId -> {
            PoiGroupInfoDto poiGroupInfoDto = new PoiGroupInfoDto();
            poiGroupInfoDto.setPoiGroupId(groupId);
            return poiGroupInfoDto;
        });

    }
}
