package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.config;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * app 待办 商品特殊菜单配置项
 * <AUTHOR>
 * @since 2024/8/12
 */
@Getter
@Setter
@ToString
public class GoodsSpecialMenuConfig {

    /**
     * 菜单权限code
     */
    private String menuCode;

    /**
     * 菜单标题
     */
    private String mainTitle;

    /**
     * 裂变渠道集合
     */
    private List<FissionChannel> fissionChannels;

    @Getter
    @Setter
    @ToString
    public static class FissionChannel {
        private Integer channelId;

        private String channelName;

        private Integer sort;

        private String jumpUrl;
    }
}
