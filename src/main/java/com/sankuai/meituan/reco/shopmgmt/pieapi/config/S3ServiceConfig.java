package com.sankuai.meituan.reco.shopmgmt.pieapi.config;

import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.meituan.service.inf.kms.value.KMSStringValue;
import com.sankuai.meituan.shangou.saas.common.storage.StorageService;
import com.sankuai.meituan.shangou.saas.common.storage.mss.MtCloudS3StorageService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2022/7/22
 */
@Configuration
public class S3ServiceConfig {




    @Value("${mts3.bucketName}")
    private String bucketName;
    @Value("${mts3.expireSeconds}")
    private int expireSeconds;
    @Value("${mts3.url}")
    private String url;



    @Bean
    public StorageService s3StorageService() throws Exception{
        MtCloudS3StorageService proxy = new MtCloudS3StorageService();
        proxy.setAppKey("com.sankuai.sgshopmgmt.empower.pieapi");
        proxy.setBucketName(bucketName);
        proxy.setExpireSeconds(expireSeconds);
        proxy.setUrl(url);
        proxy.init();
        return proxy;
    }




}
