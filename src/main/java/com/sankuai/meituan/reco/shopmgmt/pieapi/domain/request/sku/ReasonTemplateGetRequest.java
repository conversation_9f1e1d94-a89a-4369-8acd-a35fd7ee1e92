package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.GetReasonTemplateRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 获取审核拒绝原因模板请求
 * @author: WangSukuan
 * @create: 2020-03-11
 **/
@TypeDoc(
        description = "获取审核拒绝原因模板请求"
)
@Data
@ApiModel("获取审核拒绝原因模板请求")
public class ReasonTemplateGetRequest {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "模板原因id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "模板原因id", required = true)
    @NotNull
    private Long reasonId;


    public GetReasonTemplateRequest convertGetReasonTemplateRequest(User user){

        GetReasonTemplateRequest getReasonTemplateRequest = new GetReasonTemplateRequest();
        getReasonTemplateRequest.setTenantId(this.getTenantId());
        getReasonTemplateRequest.setReasonId(this.getReasonId());
        getReasonTemplateRequest.setOperateId(user.getAccountId());
        getReasonTemplateRequest.setOperateName(user.getOperatorName());
        return getReasonTemplateRequest;

    }


}
