package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NonNull;

/**
 * @Author: <EMAIL>
 * @Date: 2021/2/20 09:20
 * @Description:
 */
@Data
@ApiModel("增加\"易差评\"标签请求参数")
public class AddBadCaseTagRequest {

    @FieldDoc(
            description = "SPU编码"
    )
    @ApiModelProperty(value = "SPU编码")
    @NonNull
    private List<String> spuIds;

}
