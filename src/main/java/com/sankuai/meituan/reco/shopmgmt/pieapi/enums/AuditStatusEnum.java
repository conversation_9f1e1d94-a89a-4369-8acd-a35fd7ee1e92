package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/3/4 4:10 下午
 **/
@AllArgsConstructor
@Getter
@Deprecated
public enum AuditStatusEnum {

    //0:未送审,1:先审后发审核中,2:审核通过,3:审核驳回,4:纠错驳回,5:审核撤销,6:先发后审审核中
    UN_AUDIT(0, "未送审"),
    UN_PUBLISH_AUDITING(1, "先审后发审核中"),
    AUDITED(2,  "审核通过"),
    AUDIT_FAIL(3,  "审核驳回"),
    MISTAKEN_FAIL(4, "纠错驳回"),
    AUDIT_CANCEL(5, "审核撤销"),
    PUBLISHED_AUDITING(6, "先发后审审核中");

    private Integer code;

    private String desc;
}
