package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.FrontCategorySimpleDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pricetrend.ChannelSkuPriceTrendVO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelSkuDetailInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 线上商品信息
 * @author: liyu44
 * @create: 2020-02-03
 **/
@TypeDoc(
        description = "线上商品信息"
)
@Data
@ApiModel("线上商品信息")
public class ChannelSkuDetailInfoVo {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    @NotNull
    private Integer channelId;
    @FieldDoc(
            description = "商品状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品状态", required = true)
    @NotNull
    private Integer skuStatus;
    @FieldDoc(
            description = "线上售价 单位：元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "线上售价", required = true)
    @NotNull
    private Double price;
    @FieldDoc(
            description = "线上库存", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "线上库存")
    private Integer stock;
    @FieldDoc(
            description = "前台分类编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "前台分类编码", required = true)
    @NotNull
    private String frontCategoryCode;
    @FieldDoc(
            description = "前台分类名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "前台分类名称")
    private String frontCategoryName;
    @FieldDoc(
            description = "前台分类编码全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "前台分类编码全路径")
    private String frontCategoryCodePath;
    @FieldDoc(
            description = "前台分类名称全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "前台分类名称全路径")
    private String frontCategoryNamePath;
    @FieldDoc(
            description = "渠道类目编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目编码")
    private String channelCategoryCode;
    @FieldDoc(
            description = "渠道类目名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目名称")
    private String channelCategoryName;
    @FieldDoc(
            description = "渠道类目编码全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目编码全路径")
    private String channelCategoryCodePath;
    @FieldDoc(
            description = "渠道类目名称全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目名称全路径")
    private String channelCategoryNamePath;
    @FieldDoc(
            description = "渠道品牌编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道品牌编码")
    private String channelBrandCode;
    @FieldDoc(
            description = "渠道品牌名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道品牌名称")
    private String channelBrandName;
    @FieldDoc(
            description = "最小购买数量，菜大全web保存商品必填，app端传空", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "最小购买数量")
    private String minNum;
    @FieldDoc(
            description = "包装盒价格，单位：元，菜大全web保存商品必填，app端传空", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "包装盒价格")
    private String boxPrice;
    @FieldDoc(
            description = "包装盒数量，菜大全web保存商品必填，app端传空", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "包装盒数量")
    private String boxNum;
    @FieldDoc(
            description = "价格来源(定价方式)，价格来源(定价方式), 0-全部 1-手动定价 2-按提价策略定价 3-等于提报价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "价格来源(定价方式)，价格来源(定价方式), 0-全部 1-手动定价 2-按提价策略定价 3-等于提报价")
    private Integer priceSource;

    @FieldDoc(
            description = "门店商品多分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品多分类")
    private List<FrontCategorySimpleVO> frontCategories = new ArrayList<>();


    @FieldDoc(
            description = "市斤价, 单位:元", requiredness = Requiredness.NONE
    )
    @ApiModelProperty(name = "市斤价")
    private Double pricePer500g;

    @FieldDoc(
            description = "显示价格趋势图标 true-显示 false-不展示", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "显示价格趋势图标 true-显示 false-不展示", required = true)
    private Boolean showPriceTrendIcon;

    @FieldDoc(
            description = "价格趋势", requiredness = Requiredness.NONE
    )
    @ApiModelProperty(name = "价格趋势", required = true)
    private ChannelSkuPriceTrendVO priceTrend;

    public ChannelSkuDetailInfoVo() {
    }

    public ChannelSkuDetailInfoVo(ChannelSkuDetailInfoDTO channelSkuDetailInfoDTO) {

        this.setChannelId(channelSkuDetailInfoDTO.getChannelId());
        this.setSkuStatus(skuStatusNew2Old(channelSkuDetailInfoDTO.getSkuStatus()));
        this.setPrice(channelSkuDetailInfoDTO.getPrice());
        this.setFrontCategoryCode(channelSkuDetailInfoDTO.getFrontCategoryCode());
        this.setFrontCategoryName(channelSkuDetailInfoDTO.getFrontCategoryName());
        this.setFrontCategoryCodePath(channelSkuDetailInfoDTO.getFrontCategoryCodePath());
        this.setFrontCategoryNamePath(channelSkuDetailInfoDTO.getFrontCategoryNamePath());
        this.setMinNum(channelSkuDetailInfoDTO.getMinNum());
        this.setBoxPrice(channelSkuDetailInfoDTO.getBoxPrice());
        this.setBoxNum(channelSkuDetailInfoDTO.getBoxNum());
        this.setPriceSource(channelSkuDetailInfoDTO.getPriceSource());

        this.setStock(channelSkuDetailInfoDTO.getStock());
        this.setChannelCategoryCode(channelSkuDetailInfoDTO.getChannelCategoryCode());
        this.setChannelCategoryName(channelSkuDetailInfoDTO.getChannelCategoryName());
        this.setChannelCategoryCodePath(channelSkuDetailInfoDTO.getChannelCategoryCodePath());
        this.setChannelCategoryNamePath(channelSkuDetailInfoDTO.getChannelCategoryNamePath());
        this.setChannelBrandCode(channelSkuDetailInfoDTO.getChannelBrandCode());
        this.setChannelBrandName(channelSkuDetailInfoDTO.getChannelBrandName());

        List<FrontCategorySimpleDTO> frontCategorySimpleDTOS = channelSkuDetailInfoDTO.getFrontCategories();
        if (CollectionUtils.isNotEmpty(frontCategorySimpleDTOS)){
            for (FrontCategorySimpleDTO frontCategorySimpleDTO : frontCategorySimpleDTOS){
                FrontCategorySimpleVO frontCategorySimpleVO = new FrontCategorySimpleVO(frontCategorySimpleDTO);
                this.frontCategories.add(frontCategorySimpleVO);
            }
        }

    }


    private static int skuStatusNew2Old(int skuStatus) {
        switch (skuStatus) {
            //未上线
            case -1:
                return 3;
            //已上架
            case 1:
                return 1;
            //已下架
            case 2:
                return 2;
            default:
                return skuStatus;
        }
    }

    public ChannelSkuDetailInfoDTO buildChannelSkuDetailInfoDTO(){

        ChannelSkuDetailInfoDTO channelSkuDetailInfoDTO = new ChannelSkuDetailInfoDTO();

        channelSkuDetailInfoDTO.setChannelId(this.getChannelId() == null ? 0 : this.getChannelId());
        channelSkuDetailInfoDTO.setSkuStatus(this.getSkuStatus() == null ? 0 : this.getSkuStatus());
        channelSkuDetailInfoDTO.setPrice(this.getPrice() == null ? 0 : this.getPrice());

        channelSkuDetailInfoDTO.setFrontCategoryCode(this.getFrontCategoryCode());
        channelSkuDetailInfoDTO.setFrontCategoryName(this.getFrontCategoryName());
        channelSkuDetailInfoDTO.setFrontCategoryCodePath(this.getFrontCategoryCodePath());
        channelSkuDetailInfoDTO.setFrontCategoryNamePath(this.getFrontCategoryNamePath());
        channelSkuDetailInfoDTO.setMinNum(this.getMinNum());
        channelSkuDetailInfoDTO.setBoxPrice(this.getBoxPrice());
        channelSkuDetailInfoDTO.setBoxNum(this.getBoxNum());

        channelSkuDetailInfoDTO.setPriceSource(this.getPriceSource() == null ? 0 : this.getPriceSource());
        channelSkuDetailInfoDTO.setStock(this.getStock() == null ? 0 : this.getStock());

        channelSkuDetailInfoDTO.setChannelCategoryCode(this.getChannelCategoryCode());
        channelSkuDetailInfoDTO.setChannelCategoryName(this.getChannelCategoryName());
        channelSkuDetailInfoDTO.setChannelCategoryCodePath(this.getChannelCategoryCodePath());
        channelSkuDetailInfoDTO.setChannelCategoryNamePath(this.getChannelCategoryNamePath());
        channelSkuDetailInfoDTO.setChannelBrandCode(this.getChannelBrandCode());
        channelSkuDetailInfoDTO.setChannelBrandName(this.getChannelBrandName());

        List<FrontCategorySimpleDTO> frontCategorySimpleDTOS = new ArrayList<>();
        for (FrontCategorySimpleVO frontCategorySimpleVO : this.frontCategories){
            frontCategorySimpleDTOS.add(frontCategorySimpleVO.buildFrontCategorySimpleDTO());
        }
        channelSkuDetailInfoDTO.setFrontCategories(frontCategorySimpleDTOS);

        return channelSkuDetailInfoDTO;

    }
}
