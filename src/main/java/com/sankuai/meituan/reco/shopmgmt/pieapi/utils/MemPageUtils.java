package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/11/21
 * @email jianglilin02@meituan
 */
public class MemPageUtils {
    /**
     * 内存分页工具
     *
     * @param sortList  需要分页的列表
     * @param pageNo    上次分页的最后一个unique key
     * @param pageSize  分页大小
     * @return 分页后的PageInfo
     */
    public static <T> PageInfo<T> pagingList(List<T> sortList, int pageNo, int pageSize) {

        if (CollectionUtils.isEmpty(sortList)) {
            return new PageInfo<>(false, Lists.newArrayList());
        }

        List<List<T>> partition = Lists.partition(sortList, pageSize);
        if (pageNo > partition.size()) {
            return new PageInfo<>(false, Lists.newArrayList());
        }
        boolean hasMore = (pageNo < partition.size());
        return new PageInfo<>(hasMore, partition.get(pageNo - 1));

    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PageInfo<T> {


        private boolean hasMore;

        private List<T> pagedList;

    }
}
