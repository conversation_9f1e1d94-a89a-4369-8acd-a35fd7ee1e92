package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/16 16:39
 * @Description:
 */
@Slf4j
public class ConverterUtils {


    /**
     * 包装boolean类型,供前端使用
     * @param b
     * @return
     */
    public static int wrap(boolean b) {
        return b ? 1 : 0;
    }

    /**
     * 类型转换(防止NPE),如果接收对象为基本类型,请指定基本类型的默认值
     * @param src
     * @param mapFunction
     * @param defaultValue
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F,T> T nonNullConvert(F src, Function<F,T> mapFunction, T defaultValue) {
        try {
            return Optional.ofNullable(src).map(mapFunction).orElse(defaultValue);
        } catch (Exception e) {
            log.error("convert error", e);
            return defaultValue;
        }

    }


    public static <T> T nonNullConvert(String src, Function<String,T> mapFunction, T defaultValue) {
        try {
            return Optional.ofNullable(src).filter(StringUtils::isNotEmpty).map(mapFunction).orElse(defaultValue);
        } catch (Exception e) {
            log.error("convert error", e);
            return defaultValue;
        }

    }

    /**
     * List类型转换
     *
     * @param srcList
     * @param mapFunction
     * @param <S>         source
     * @param <T>         target
     * @return
     */
    public static <S, T, K> Map<K, T> listStreamMapToMap(List<S> srcList, Function<S, K> keyFunction, Function<S, T> mapFunction) {
        return Optional.ofNullable(srcList).map(List::stream).orElse(Stream.empty())
                .filter(Objects::nonNull).collect(Collectors.toMap(keyFunction, mapFunction, (k1, k2) -> k2));
    }


    /**
     * 类型转换(防止NPE),如果接收对象为基本类型,请指定基本类型的默认值
     * @param src
     * @param mapFunction
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F,T> T nonNullConvert(F src, Function<F,T> mapFunction) {
        return nonNullConvert(src, mapFunction,null);
    }


    public static <T> T nonNullConvert(String src, Function<String,T> mapFunction) {
        return nonNullConvert(src, mapFunction,null);
    }


    /**
     * List类型转换
     * @param srcList
     * @param mapFunction
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F,T> List<T> convertList(List<F> srcList, Function<F,T> mapFunction) {
        return Optional.ofNullable(srcList).map(List::stream).orElse(Stream.empty())
                .filter(Objects::nonNull).map(mapFunction).collect(Collectors.toList());
    }



    public static String convert(int i) {
        return i > 0 ? String.valueOf(i): null;
    }


    public static String convert(double d) {
        return d > 0.0D ? String.valueOf(d): null;
    }

    public static String convert(long l) {
        return l > 0L ? String.valueOf(l): null;
    }

    public static String convertNullString(String value, String defaultValue) {
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        return value;
    }

    public static<F> List<F> convertNullList(List<F> list) {
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 格式化百分比
     * @param ratio
     * @return
     */
    public static String formatRatio(double ratio) {
        return new DecimalFormat("0.00").format(ratio);
    }


    public static String formatMoney(int fen) {
        return new DecimalFormat("0.00").format(fen / 100.0);
    }

    public static Integer StringToInteger(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        try {
            return Integer.valueOf(str);
        } catch (Exception e) {
            log.error("StringToInteger error", e);
            return null;
        }
    }

}