package com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ocms;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.CdqStoreSkuVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.CdqStoreSkuWithChannelInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChangeChannelSkuFrontCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChangeMultiChannelSkuStatusRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChannelSkuDetailInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChannelStatusChangeParamVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChannelStoreSkuFrontCategoryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChgChannelSkuStatusForAppRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.FrontCategorySimpleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.PriceChangePreviewRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.QueryCdqStoreSkuDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SaveCdqStoreSkuRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuListPageByDayAndOpTypeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuListPageForAppRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuPriceAndStock;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StoreSkuStockVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.UpdatePriceAndStockForAppRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.BatchChangeSpuFrontCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.BatchChangeSpuStatusRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryTenantRegionSpuByNameRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.UpdateStockRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.ChannelStoreSpuFrontCategoryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.ChannelSyncStrategyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SkuAdjustPriceVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.StoreSpuOnlineParamVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChangeChannelSkuFrontCategoryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChangeChannelSkuStatusForAppResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChangeMultiChannelSkuStatusResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelCategoryRelationVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelPriceAndStockVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelSkuForAppVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ErrorRecordVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.FailedRecordVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.QueryStoreOnlineSkuDetailResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuChannelStatusChangeResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuListPageForAppResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuTagCategoryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuTagSimpleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.UpdateFailedRecordVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.UpdatePriceStockResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.common.dto.ChannelStoreFailDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.common.response.GeneralResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.enums.quote.QueryQuoteModeEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.price.enums.quote.ReviewClientEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.price.enums.quote.ReviewOperateEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.price.enums.quote.ReviewStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.quote.BatchQuoteReviewRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.quote.QueryQuoteReviewsRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.quote.QuoteCommitRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.quote.QuoteDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.AllowSaleEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelNormAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.BatchChangeStoreSpuFrontCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.BatchUpdateStoreSpuStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.PageQueryTenantRegionSpuByNameRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.PageQueryTenantRegionSpuByNameResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.dto.sku.CdqStoreSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.dto.sku.CdqStoreSkuWithChannelInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.request.sku.CdqStoreSkuDetailQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.request.sku.CdqStoreSkuSaveRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.response.sku.CdqStoreSkuDetailQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.BatchUpdateStoreStockRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.CategoryBindChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChangeChannelSkuStatusForAppRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChangeStoreSkuChannelFrontCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelCategoryWithPathDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelPriceAndStockDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelSkuDetailInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelSkuForAppDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelStoreSkuForAppDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelStoreSkuFrontCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.FailedRecordDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.PageInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryChannelCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QuerySkuByDayAndOpTypeRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.Response;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SaveOnlinePriceAndStockRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuChannelStatusChangeParam;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuChannelStatusChangeResult;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuListPageForAppResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuOnlinePriceAndStockDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuPageQueryForAppRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuTagDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StoreSkuSaleStockDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.TaskResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.UpdateFailedRecordDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.UpdatePriceStockResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.BizTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeviceTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OperateSourceEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.SkuOperateSourceType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.reponse.sku.SkuChannelStatusChangeResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.request.sku.SkuChannelStatusChangeRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.TenantFullTagCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.TenantTagDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.TenantTagResponse;
import com.sankuai.meituan.shangou.empower.price.client.dto.config.StoreSkuPriceInfoDTO;
import com.sankuai.meituan.shangou.empower.price.client.request.config.SkuPriceChangeByWeightReqeust;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/4/15
 * desc: 中台工具类
 */
@Slf4j
public class OCMSUtils {

    private static final int AUTO_ON_SALE_NEXT_DAY_YES = 1;
    private static final int AUTO_ON_SALE_YES = 1;
    private static final int SKU_STATUS_ONLINE = 1;
    private static final String ANDROID = "android";
    private static final String IOS = "ios";

    private static final String PHONE_MUSK = "**";

    //门店打标  0-未知，1-门店未添加，2-门店已添加
    private static final Integer STORE_ON_SALE_ADD = 2;

    private static final int TENANT_CONFIG_ID_INFINITE_SKU = 4;

    //创建门店商品请求来源-APP
    private static final int CREATE_STORE_SKU_REQUEST_SOURCE_APP = 3;

    private OCMSUtils() {

    }

    public static SaveOnlinePriceAndStockRequest convertRequest(UpdatePriceAndStockForAppRequest request, User user) {
        SaveOnlinePriceAndStockRequest saveOnlinePriceAndStockRequest = new SaveOnlinePriceAndStockRequest();
        saveOnlinePriceAndStockRequest.setTenantId(user.getTenantId());
        saveOnlinePriceAndStockRequest.setOperatorId(user.getAccountId());
        saveOnlinePriceAndStockRequest.setOperatorName(user.getOperatorName());
        saveOnlinePriceAndStockRequest.setAccountType(user.getAccountType());
        saveOnlinePriceAndStockRequest.setSkuOnlinePriceAndStocks(convertSkuOnlinePriceAndStockDTOList(request.getStoreId(), request.getSkuPriceAndStocks()));
        if (ANDROID.equalsIgnoreCase(ApiMethodParamThreadLocal.getIdentityInfo().getOs())) {
            saveOnlinePriceAndStockRequest.setSource(DeviceTypeEnum.ANDROID);
        } else if (IOS.equalsIgnoreCase(ApiMethodParamThreadLocal.getIdentityInfo().getOs())) {
            saveOnlinePriceAndStockRequest.setSource(DeviceTypeEnum.IOS);
        }

        return saveOnlinePriceAndStockRequest;
    }

    public static SkuChannelStatusChangeRequest convertRequest(ChangeMultiChannelSkuStatusRequest request, User user) {
        SkuChannelStatusChangeRequest skuChannelStatusChangeRequest = new SkuChannelStatusChangeRequest();
        skuChannelStatusChangeRequest.setTenantId(user.getTenantId());
        skuChannelStatusChangeRequest.setStoreId(request.getStoreId());
        skuChannelStatusChangeRequest.setSkuId(request.getSkuId());
        skuChannelStatusChangeRequest.setChannelStatusChangeParamList(convertSkuChannelStatusChangeParamList(request.getChannelStatusChangeParamList()));
        skuChannelStatusChangeRequest.setOperator(user.getOperatorName());
        skuChannelStatusChangeRequest.setOperatorId(user.getAccountId());
        if (Objects.nonNull(request.getBizType()) && Objects.nonNull(BizTypeEnum.findByValue(request.getBizType()))) {
            skuChannelStatusChangeRequest.setBizType(BizTypeEnum.findByValue(request.getBizType()));
        }
        skuChannelStatusChangeRequest.setCheckPermit(request.isCheckPermit());
        return skuChannelStatusChangeRequest;
    }

    public static ChangeChannelSkuStatusForAppRequest convertRequest(ChgChannelSkuStatusForAppRequest request, User user) {
        ChangeChannelSkuStatusForAppRequest changeChannelSkuStatusForAppRequest = new ChangeChannelSkuStatusForAppRequest();
        changeChannelSkuStatusForAppRequest.setTenantId(user.getTenantId());
        changeChannelSkuStatusForAppRequest.setStoreId(request.getStoreId());
        changeChannelSkuStatusForAppRequest.setOperator(user.getOperatorName());
        changeChannelSkuStatusForAppRequest.setOperatorId(user.getAccountId());
        changeChannelSkuStatusForAppRequest.setChannelSkuList(convertChannelStoreSkuForAppDTOList(request.getStoreId(), request.getSkuList()));
        changeChannelSkuStatusForAppRequest.setSkuStatus(request.getSkuStatus());
        if (CollectionUtils.isNotEmpty(request.getStoreCategorys())) {
            changeChannelSkuStatusForAppRequest.setStoreCategorys(request.getStoreCategorys());
        }
        if (Objects.nonNull(request.getAutoOnSale())) {
            changeChannelSkuStatusForAppRequest.setAutoOnSale(request.getAutoOnSale() == AUTO_ON_SALE_YES);
        } else {
            changeChannelSkuStatusForAppRequest.setAutoOnSale(false);
        }
        changeChannelSkuStatusForAppRequest.setChannelIds(request.getChannelIds());
        changeChannelSkuStatusForAppRequest.setCheckPermit(request.isCheckPermit());
        return changeChannelSkuStatusForAppRequest;
    }

    public static SkuPageQueryForAppRequest convertRequest(SkuListPageForAppRequest request, User user, String boothId) {
        SkuPageQueryForAppRequest skuPageQueryForAppRequest = new SkuPageQueryForAppRequest();
        skuPageQueryForAppRequest.setPage(request.getPage());
        skuPageQueryForAppRequest.setSize(request.getSize());
        skuPageQueryForAppRequest.setKeyword(request.getKeyword());
        skuPageQueryForAppRequest.setStoreId(request.getStoreId());
        skuPageQueryForAppRequest.setCategories(request.getCategories());
        skuPageQueryForAppRequest.setTenantId(user.getTenantId());
        skuPageQueryForAppRequest.setChannelStoreCategoryMap(request.getChannelStoreCategoryMap());
        if (Objects.nonNull(request.getHasStoreCategory())) {
            skuPageQueryForAppRequest.setHasStoreCategory(request.getHasStoreCategory());
        }
        if (Strings.isNotEmpty(boothId)) {
            skuPageQueryForAppRequest.setBoothIds(Arrays.asList(Long.valueOf(boothId)));
        }
        if (Objects.nonNull(request.getSkuStatus())) {
            skuPageQueryForAppRequest.setSkuStatus(request.getSkuStatus());
        }
        skuPageQueryForAppRequest.setChannelIds(request.getChannelIds());
        skuPageQueryForAppRequest.setTagIds(request.getTagIds());
        return skuPageQueryForAppRequest;
    }


    public static QuerySkuByDayAndOpTypeRequest convertRequest(SkuListPageByDayAndOpTypeRequest request, User user) {
        QuerySkuByDayAndOpTypeRequest querySkuByDayAndOpTypeRequest = new QuerySkuByDayAndOpTypeRequest();
        querySkuByDayAndOpTypeRequest.setPageNum(request.getPage());
        querySkuByDayAndOpTypeRequest.setPageSize(request.getSize());
        if (Objects.nonNull(request.getDays())) {
            querySkuByDayAndOpTypeRequest.setDays(request.getDays());
        }
        if (Objects.nonNull(request.getHasStoreCategory())) {
            querySkuByDayAndOpTypeRequest.setHasStoreCategory(request.getHasStoreCategory());
        }
        querySkuByDayAndOpTypeRequest.setOpType(request.getOpType());
        querySkuByDayAndOpTypeRequest.setStoreId(request.getStoreId());
        querySkuByDayAndOpTypeRequest.setTenantId(user.getTenantId());
        querySkuByDayAndOpTypeRequest.setStoreCategory(request.getStoreCategory());
        return querySkuByDayAndOpTypeRequest;
    }

    private static List<SkuOnlinePriceAndStockDTO> convertSkuOnlinePriceAndStockDTOList(Long storeId, List<SkuPriceAndStock> skuPriceAndStockList) {
        if (Objects.isNull(skuPriceAndStockList)) {
            return Lists.newArrayList();
        }

        List<SkuOnlinePriceAndStockDTO> skuOnlinePriceAndStockDTOList = Lists.newArrayList();
        for (SkuPriceAndStock skuPriceAndStock : skuPriceAndStockList) {
            SkuOnlinePriceAndStockDTO skuOnlinePriceAndStockDTO = new SkuOnlinePriceAndStockDTO();
            skuOnlinePriceAndStockDTO.setStoreId(storeId);
            skuOnlinePriceAndStockDTO.setSkuId(skuPriceAndStock.getSkuId());
            if (Objects.nonNull(skuPriceAndStock.getPrice())) {
                skuOnlinePriceAndStockDTO.setPrice(skuPriceAndStock.getPrice());
            }
            if (Objects.nonNull(skuPriceAndStock.getStock())) {
                skuOnlinePriceAndStockDTO.setStock(skuPriceAndStock.getStock());
            }
            skuOnlinePriceAndStockDTO.setChannelId(skuPriceAndStock.getChannelId());
            skuOnlinePriceAndStockDTO.setAutoResumeInfiniteStock(skuPriceAndStock.getAutoResumeInfiniteStock());
            skuOnlinePriceAndStockDTO.setCustomizeStockFlag(skuPriceAndStock.getCustomizeStockFlag());
            skuOnlinePriceAndStockDTOList.add(skuOnlinePriceAndStockDTO);

        }

        return skuOnlinePriceAndStockDTOList;
    }

    public static UpdatePriceStockResponseVO convertResponse(UpdatePriceStockResponse response) {
        UpdatePriceStockResponseVO updatePriceStockResponseVO = new UpdatePriceStockResponseVO();
        updatePriceStockResponseVO.setErrorCode(response.getCode());
        updatePriceStockResponseVO.setErrorRecordList(convertUpdateFailedRecordVOList(response.getErrorRecordList()));
        updatePriceStockResponseVO.setComment(response.getComment());
        updatePriceStockResponseVO.setNeedReview(response.isNeedReview() ? 1 : 0);
        return updatePriceStockResponseVO;
    }

    public static ChangeMultiChannelSkuStatusResponse convertResponse(SkuChannelStatusChangeResponse skuChannelStatusChangeResponse) {
        ChangeMultiChannelSkuStatusResponse response = new ChangeMultiChannelSkuStatusResponse();
        response.setChannelStatusChangeResults(convertSkuChannelStatusChangeResultVOList(skuChannelStatusChangeResponse.getChannelStatusChangeResults()));
        return response;
    }

    public static ChangeChannelSkuStatusForAppResponseVO convertResponse(Response response) {
        ChangeChannelSkuStatusForAppResponseVO changeChannelSkuStatusForAppResponseVO = new ChangeChannelSkuStatusForAppResponseVO();
        changeChannelSkuStatusForAppResponseVO.setErrorCode(response.getCode());
        changeChannelSkuStatusForAppResponseVO.setErrorRecordList(convertErrorRecordVOList(response.getErrorRecordList()));
        return changeChannelSkuStatusForAppResponseVO;
    }

    public static SkuListPageForAppResponseVO convertResponse(SkuListPageForAppResponse response) {
        SkuListPageForAppResponseVO skuListPageForAppResponseVO = new SkuListPageForAppResponseVO();
        skuListPageForAppResponseVO.setSkuInfoList(convertChannelSkuForAppVOList(response.getSkuInfoList()));
        skuListPageForAppResponseVO.setPageInfo(convert(response.getPageInfo()));
        return skuListPageForAppResponseVO;
    }

    private static List<UpdateFailedRecordVO> convertUpdateFailedRecordVOList(List<UpdateFailedRecordDTO> updateFailedRecordDTOList) {
        if (Objects.isNull(updateFailedRecordDTOList)) {
            return Lists.newArrayList();
        }

        List<UpdateFailedRecordVO> updateFailedRecordVOList = Lists.newArrayList();

        for (UpdateFailedRecordDTO updateFailedRecordDTO : updateFailedRecordDTOList) {
            UpdateFailedRecordVO updateFailedRecordVO = new UpdateFailedRecordVO();
            updateFailedRecordVO.setStoreId(updateFailedRecordDTO.getStoreId());
            updateFailedRecordVO.setSkuId(updateFailedRecordDTO.getSkuId());
            updateFailedRecordVO.setErrorType(updateFailedRecordDTO.getErrorType());
            updateFailedRecordVO.setErrorMsg(updateFailedRecordDTO.getErrorMsg());
            updateFailedRecordVO.setChannelId(updateFailedRecordDTO.getChannelId());
            updateFailedRecordVOList.add(updateFailedRecordVO);
        }

        return updateFailedRecordVOList;
    }

    private static List<SkuChannelStatusChangeParam> convertSkuChannelStatusChangeParamList(List<ChannelStatusChangeParamVO> channelStatusChangeParamVOList) {
        if (Objects.isNull(channelStatusChangeParamVOList)) {
            return Lists.newArrayList();
        }

        List<SkuChannelStatusChangeParam> skuChannelStatusChangeParamList = Lists.newArrayList();
        for (ChannelStatusChangeParamVO channelStatusChangeParamVO : channelStatusChangeParamVOList) {
            SkuChannelStatusChangeParam skuChannelStatusChangeParam = new SkuChannelStatusChangeParam();
            skuChannelStatusChangeParam.setChannelId(channelStatusChangeParamVO.getChannelId());
            skuChannelStatusChangeParam.setFrontCategoryCode(channelStatusChangeParamVO.getFrontCategoryId());
            skuChannelStatusChangeParam.setSkuChannelStatus(channelStatusChangeParamVO.getSkuChannelStatus());
            skuChannelStatusChangeParamList.add(skuChannelStatusChangeParam);
        }
        return skuChannelStatusChangeParamList;
    }

    private static List<SkuChannelStatusChangeResultVO> convertSkuChannelStatusChangeResultVOList(List<SkuChannelStatusChangeResult> skuChannelStatusChangeResultList) {
        if (Objects.isNull(skuChannelStatusChangeResultList)) {
            return Lists.newArrayList();
        }

        List<SkuChannelStatusChangeResultVO> skuChannelStatusChangeResultVOList = Lists.newArrayList();
        for (SkuChannelStatusChangeResult skuChannelStatusChangeResult : skuChannelStatusChangeResultList) {
            SkuChannelStatusChangeResultVO skuChannelStatusChangeResultVO = new SkuChannelStatusChangeResultVO();
            skuChannelStatusChangeResultVO.setChannelId(skuChannelStatusChangeResult.getChannelId());
            skuChannelStatusChangeResultVO.setSkuChannelStatus(skuChannelStatusChangeResult.getSkuChannelStatus());
            skuChannelStatusChangeResultVO.setSuccess(skuChannelStatusChangeResult.isSuccess() ? 1 : 0);
            skuChannelStatusChangeResultVO.setFailReason(skuChannelStatusChangeResult.getFailReason());
            skuChannelStatusChangeResultVO.setCode(skuChannelStatusChangeResult.getCode());
            skuChannelStatusChangeResultVOList.add(skuChannelStatusChangeResultVO);
        }
        return skuChannelStatusChangeResultVOList;
    }

    private static List<ChannelStoreSkuForAppDTO> convertChannelStoreSkuForAppDTOList(Long storeId, List<String> skuList) {
        if (Objects.isNull(skuList)) {
            return Lists.newArrayList();
        }

        List<ChannelStoreSkuForAppDTO> channelStoreSkuForAppDTOList = Lists.newArrayList();
        for (String sku : skuList) {
            ChannelStoreSkuForAppDTO channelStoreSkuForAppDTO = new ChannelStoreSkuForAppDTO();
            channelStoreSkuForAppDTO.setSku(sku);
            channelStoreSkuForAppDTO.setStoreId(storeId);
            channelStoreSkuForAppDTOList.add(channelStoreSkuForAppDTO);
        }
        return channelStoreSkuForAppDTOList;
    }

    public static List<ErrorRecordVO> convertErrorRecordVOList(List<FailedRecordDTO> failedRecordDTOS) {
        if (Objects.isNull(failedRecordDTOS)) {
            return Lists.newArrayList();
        }

        List<ErrorRecordVO> errorRecordVOList = Lists.newArrayList();
        for (FailedRecordDTO failedRecordDTO : failedRecordDTOS) {
            ErrorRecordVO errorRecordVO = new ErrorRecordVO();
            errorRecordVO.setSku(failedRecordDTO.getSkuId());
            errorRecordVO.setStoreId(failedRecordDTO.getStoreId());
            errorRecordVO.setChannelId(failedRecordDTO.getChannelId());
            errorRecordVO.setErrorMsg(failedRecordDTO.getErrorMsg());
            errorRecordVO.setErrorType(failedRecordDTO.getErrorCode());
            errorRecordVOList.add(errorRecordVO);
        }

        return errorRecordVOList;
    }

    private static List<ChannelSkuForAppVO> convertChannelSkuForAppVOList(List<ChannelSkuForAppDTO> channelSkuForAppDTOList) {
        if (Objects.isNull(channelSkuForAppDTOList)) {
            return Lists.newArrayList();
        }

        List<ChannelSkuForAppVO> channelSkuForAppVOList = Lists.newArrayList();

        for (ChannelSkuForAppDTO channelSkuForAppDTO : channelSkuForAppDTOList) {
            ChannelSkuForAppVO channelSkuForAppVO = new ChannelSkuForAppVO();
            channelSkuForAppVO.setTenantId(channelSkuForAppDTO.getTenantId());
            channelSkuForAppVO.setSku(channelSkuForAppDTO.getSku());
            channelSkuForAppVO.setName(channelSkuForAppDTO.getName());
            channelSkuForAppVO.setStoreId(channelSkuForAppDTO.getStoreId());
            channelSkuForAppVO.setImages(channelSkuForAppDTO.getImages());
            channelSkuForAppVO.setSpec(channelSkuForAppDTO.getSpec());
            channelSkuForAppVO.setWeight(channelSkuForAppDTO.getWeight());
            channelSkuForAppVO.setWeightType(channelSkuForAppDTO.getWeightType());
            channelSkuForAppVO.setBasicUnit(channelSkuForAppDTO.getBasicUnit());
            channelSkuForAppVO.setUpcInfo(channelSkuForAppDTO.getUpcInfo());
            channelSkuForAppVO.setChannels(convertChannelPriceAndStockVOList(channelSkuForAppDTO.getChannelPriceAndStocks()));
            channelSkuForAppVO.setCustomizeStockFlag(channelSkuForAppDTO.getCustomizeStockFlag());
            channelSkuForAppVO.setAutoResumeInfiniteStock(channelSkuForAppDTO.getAutoResumeInfiniteStock());
            channelSkuForAppVO.setCustomizeStockQuantity(channelSkuForAppDTO.getCustomizeStockQuantity());
            channelSkuForAppVO.setChannelId2FirstFrontCategoryNameMap(channelSkuForAppDTO.getChannelId2FirstFrontCategoryNameMap());
            channelSkuForAppVO.setMonthSaleAmount(channelSkuForAppDTO.getMonthSaleAmount());
            channelSkuForAppVO.setReviewStatus(channelSkuForAppDTO.getQuoteStatus());
            channelSkuForAppVO.setSpecialty(channelSkuForAppDTO.getSpecialty());
            channelSkuForAppVO.setQuotePrice(channelSkuForAppDTO.getQuotePrice());
            //标签信息转换
            channelSkuForAppVO.setSkuTagSimpleVOList(convertSkuTagInfo2VOList(channelSkuForAppDTO.getTags()));

            channelSkuForAppVOList.add(channelSkuForAppVO);
        }

        return channelSkuForAppVOList;
    }

    /**
     * 将标签信息DTO转换成VO
     *
     * @param skuTagDTOList
     * @return
     */
    private static List<SkuTagSimpleVO> convertSkuTagInfo2VOList(List<SkuTagDTO> skuTagDTOList) {
        if (CollectionUtils.isEmpty(skuTagDTOList)) {
            return Lists.newArrayList();
        }

        List<SkuTagSimpleVO> skuTagSimpleVOList = Lists.newArrayList();
        for (SkuTagDTO skuTagDTO : skuTagDTOList) {
            SkuTagSimpleVO skuTagSimpleVO = new SkuTagSimpleVO();
            skuTagSimpleVO.setTagId(skuTagDTO.getTagId());
            skuTagSimpleVO.setTagName(skuTagDTO.getTagName());
            skuTagSimpleVOList.add(skuTagSimpleVO);
        }

        return skuTagSimpleVOList;
    }

    private static List<ChannelPriceAndStockVO> convertChannelPriceAndStockVOList(List<ChannelPriceAndStockDTO> channelPriceAndStockDTOList) {
        if (Objects.isNull(channelPriceAndStockDTOList)) {
            return Lists.newArrayList();
        }

        List<ChannelPriceAndStockVO> channelPriceAndStockVOList = Lists.newArrayList();
        for (ChannelPriceAndStockDTO channelPriceAndStockDTO : channelPriceAndStockDTOList) {
            ChannelPriceAndStockVO channelPriceAndStockVO = new ChannelPriceAndStockVO();
            channelPriceAndStockVO.setChannelId(channelPriceAndStockDTO.getChannelId());
            channelPriceAndStockVO.setPrice(channelPriceAndStockDTO.getPrice());
            channelPriceAndStockVO.setStock(channelPriceAndStockDTO.getStock());
            channelPriceAndStockVO.setStatus(channelPriceAndStockDTO.getStatus());
            channelPriceAndStockVOList.add(channelPriceAndStockVO);
        }

        return channelPriceAndStockVOList;
    }

    private static PageInfoVO convert(PageInfoDTO pageInfoDTO) {
        if (Objects.isNull(pageInfoDTO)) {
            return null;
        }
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(pageInfoDTO.getPage());
        pageInfoVO.setSize(pageInfoDTO.getSize());
        pageInfoVO.setTotalPage(pageInfoDTO.getTotalPage());
        pageInfoVO.setTotalSize(pageInfoDTO.getTotalSize());
        return pageInfoVO;
    }

    public static CdqStoreSkuSaveRequest convertRequest(SaveCdqStoreSkuRequest saveCdqStoreSkuRequest, User user) throws Exception {
        CdqStoreSkuSaveRequest request = new CdqStoreSkuSaveRequest();
        request.setSaveType(saveCdqStoreSkuRequest.getSaveType());
        request.setStoreId(saveCdqStoreSkuRequest.getStoreId());
        request.setTenantId(saveCdqStoreSkuRequest.getTenantId());
        request.setOperatorId(user.getAccountId());
        request.setOperatorName(user.getOperatorName());
        request.setOperateSource(OperateSourceEnum.EMPOWER_ASSISTENT_APP);
        if (saveCdqStoreSkuRequest.getInfiniteInventory() != null) {
            request.setInfiniteInventory(saveCdqStoreSkuRequest.getInfiniteInventory());
        }
        request.setUpdateStoreSkuPriceForUpdate(saveCdqStoreSkuRequest.isUpdateStoreSkuPriceForUpdate());

        CdqStoreSkuWithChannelInfoDTO cdqStoreSkuWithChannelInfoDTO = new CdqStoreSkuWithChannelInfoDTO();

        CdqStoreSkuWithChannelInfoVo cdqStoreSkuWithChannelInfoVo = saveCdqStoreSkuRequest.getCdqStoreSkuWithChannelInfoVo();
        CdqStoreSkuVo cdqStoreSkuVo = cdqStoreSkuWithChannelInfoVo.getCdqStoreSkuVo();
        List<ChannelSkuDetailInfoVo> channelSkuDetailInfoVoList = cdqStoreSkuWithChannelInfoVo.getChannelSkuDetailInfoVos();

        CdqStoreSkuDTO cdqStoreSkuDTO = cdqStoreSkuVo.buildCdqStoreSkuDTO();
        cdqStoreSkuDTO.setStorePrice(cdqStoreSkuVo.getStorePrice() == null ? 0 : cdqStoreSkuVo.getStorePrice());
        cdqStoreSkuDTO.setTenantId(request.getTenantId());
        cdqStoreSkuDTO.setStoreId(request.getStoreId());
        List<ChannelSkuDetailInfoDTO> channelSkuDetailInfoDTOList = convertChannelSkuDetailInfoDTOList(channelSkuDetailInfoVoList);

        cdqStoreSkuWithChannelInfoDTO.setStoreSkuInfo(cdqStoreSkuDTO);
        cdqStoreSkuWithChannelInfoDTO.setChannelSkuInfoList(channelSkuDetailInfoDTOList);
        request.setStoreSkuWithChannelInfo(cdqStoreSkuWithChannelInfoDTO);

        return request;

    }

    private static List<ChannelSkuDetailInfoDTO> convertChannelSkuDetailInfoDTOList(List<ChannelSkuDetailInfoVo> channelSkuDetailInfoVoList) {

        List<ChannelSkuDetailInfoDTO> channelSkuDetailInfoDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(channelSkuDetailInfoVoList)) {
            return channelSkuDetailInfoDTOList;
        }
        for (ChannelSkuDetailInfoVo channelSkuDetailInfoVo : channelSkuDetailInfoVoList) {
            channelSkuDetailInfoDTOList.add(channelSkuDetailInfoVo.buildChannelSkuDetailInfoDTO());
        }
        return channelSkuDetailInfoDTOList;
    }

    public static CdqStoreSkuDetailQueryRequest convertRequest(QueryCdqStoreSkuDetailRequest queryCdqStoreSkuDetailRequest, User user) {
        CdqStoreSkuDetailQueryRequest request = new CdqStoreSkuDetailQueryRequest();
        request.setTenantId(user.getTenantId());
        request.setStoreId(queryCdqStoreSkuDetailRequest.getStoreId());
        request.setSkuId(queryCdqStoreSkuDetailRequest.getStoreSkuId());
        return request;
    }

    public static QueryStoreOnlineSkuDetailResponse convertQueryStoreOnlineSkuDetailResponse(CdqStoreSkuDetailQueryResponse response) {

        QueryStoreOnlineSkuDetailResponse queryStoreOnlineSkuDetailResponse = new QueryStoreOnlineSkuDetailResponse();
        if (response.getStoreSku() == null) {
            return queryStoreOnlineSkuDetailResponse;
        }
        CdqStoreSkuWithChannelInfoDTO cdqStoreSkuWithChannelInfoDTO = response.getStoreSku();

        CdqStoreSkuDTO cdqStoreSkuDTO = cdqStoreSkuWithChannelInfoDTO.getStoreSkuInfo();
        CdqStoreSkuVo cdqStoreSkuVo = new CdqStoreSkuVo(cdqStoreSkuDTO);

        List<ChannelSkuDetailInfoDTO> channelSkuDetailInfoDTOList = cdqStoreSkuWithChannelInfoDTO.getChannelSkuInfoList();
        List<ChannelSkuDetailInfoVo> channelSkuDetailInfoVoList = convertChannelSkuDetailInfoVoList(channelSkuDetailInfoDTOList);

        CdqStoreSkuWithChannelInfoVo cdqStoreSkuWithChannelInfoVo = new CdqStoreSkuWithChannelInfoVo();
        cdqStoreSkuWithChannelInfoVo.setCdqStoreSkuVo(cdqStoreSkuVo);
        cdqStoreSkuWithChannelInfoVo.setChannelSkuDetailInfoVos(channelSkuDetailInfoVoList);

        queryStoreOnlineSkuDetailResponse.setStoreSku(cdqStoreSkuWithChannelInfoVo);
        return queryStoreOnlineSkuDetailResponse;
    }

    private static List<ChannelSkuDetailInfoVo> convertChannelSkuDetailInfoVoList(List<ChannelSkuDetailInfoDTO> channelSkuDetailInfoDTOList) {

        List<ChannelSkuDetailInfoVo> channelSkuDetailInfoVoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(channelSkuDetailInfoDTOList)) {
            return channelSkuDetailInfoVoList;
        }
        for (ChannelSkuDetailInfoDTO channelSkuDetailInfoDTO : channelSkuDetailInfoDTOList) {
            channelSkuDetailInfoVoList.add(new ChannelSkuDetailInfoVo(channelSkuDetailInfoDTO));
        }
        return channelSkuDetailInfoVoList;
    }

    public static ChangeStoreSkuChannelFrontCategoryRequest convertRequest(ChangeChannelSkuFrontCategoryRequest request, User user) {
        ChangeStoreSkuChannelFrontCategoryRequest changeStoreSkuChannelFrontCategoryRequest = new ChangeStoreSkuChannelFrontCategoryRequest();
        changeStoreSkuChannelFrontCategoryRequest.setTenantId(user.getTenantId());
        changeStoreSkuChannelFrontCategoryRequest.setOperatorId(user.getAccountId());
        changeStoreSkuChannelFrontCategoryRequest.setOperator(user.getOperatorName());
        List<ChannelStoreSkuFrontCategoryDTO> channelStoreSkuFrontCategoryDTOList = request.getChannelStoreSkuFrontCategoryList().stream()
                .filter(Objects::nonNull)
                .map(e -> buildChannelStoreSkuFrontCategoryDTO(e, request.getStoreId()))
                .collect(Collectors.toList());
        changeStoreSkuChannelFrontCategoryRequest.setChannelStoreSkuFrontCategoryList(channelStoreSkuFrontCategoryDTOList);
        return changeStoreSkuChannelFrontCategoryRequest;
    }

    public static ChangeChannelSkuFrontCategoryResponse convertChangeChannelSkuFrontCategoryResponse(TaskResponse response) {
        ChangeChannelSkuFrontCategoryResponse changeChannelSkuFrontCategoryResponse = new ChangeChannelSkuFrontCategoryResponse();
        changeChannelSkuFrontCategoryResponse.setTaskId(response.getTaskId());
        if (CollectionUtils.isNotEmpty(response.getErrorRecordList())) {
            List<FailedRecordVO> errorRecordList = response.getErrorRecordList().stream().filter(Objects::nonNull)
                    .map(FailedRecordVO::new)
                    .collect(Collectors.toList());
            changeChannelSkuFrontCategoryResponse.setErrorRecordList(errorRecordList);
        }
        return changeChannelSkuFrontCategoryResponse;
    }

    private static ChannelStoreSkuFrontCategoryDTO buildChannelStoreSkuFrontCategoryDTO(ChannelStoreSkuFrontCategoryVO channelStoreSkuFrontCategoryVO, Long storeId) {
        ChannelStoreSkuFrontCategoryDTO channelStoreSkuFrontCategoryDTO = new ChannelStoreSkuFrontCategoryDTO();
        channelStoreSkuFrontCategoryDTO.setSkuId(channelStoreSkuFrontCategoryVO.getSkuId());
        channelStoreSkuFrontCategoryDTO.setStoreId(storeId);
        channelStoreSkuFrontCategoryDTO.setChannelId(channelStoreSkuFrontCategoryVO.getChannelId());
        channelStoreSkuFrontCategoryDTO.setFrontCategoryId(channelStoreSkuFrontCategoryVO.getFrontCategoryId());
        return channelStoreSkuFrontCategoryDTO;
    }

    /**
     * 将ocms商品标签列表(带分类)转换成VO对象
     *
     * @param response
     * @return
     */
    public static List<SkuTagCategoryVO> convertTenantTagResponse(TenantTagResponse response) {
        List<SkuTagCategoryVO> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(response.getData())) {
            List<TenantFullTagCategoryDTO> tenantFullTagCategoryDTOList = response.getData().stream().filter(Objects::nonNull)
                    .collect(Collectors.toList());
            for (TenantFullTagCategoryDTO tenantFullTagCategoryDTO : tenantFullTagCategoryDTOList) {
                //标签分类信息赋值
                SkuTagCategoryVO skuTagCategoryVO = new SkuTagCategoryVO();
                skuTagCategoryVO.setTagCategoryId(tenantFullTagCategoryDTO.getId());
                skuTagCategoryVO.setCategoryName(tenantFullTagCategoryDTO.getName());
                List<SkuTagSimpleVO> skuTagSimpleVOList = Lists.newArrayList();
                //分类下标签列表赋值
                if (CollectionUtils.isNotEmpty(tenantFullTagCategoryDTO.getTagList())) {
                    for (TenantTagDTO tenantTagDTO : tenantFullTagCategoryDTO.getTagList()) {
                        SkuTagSimpleVO skuTagSimpleVO = new SkuTagSimpleVO();
                        skuTagSimpleVO.setTagId(tenantTagDTO.getId());
                        skuTagSimpleVO.setTagName(tenantTagDTO.getName());
                        skuTagSimpleVOList.add(skuTagSimpleVO);
                    }
                }
                skuTagCategoryVO.setTags(skuTagSimpleVOList);
                result.add(skuTagCategoryVO);
            }
        }
        return result;
    }

    public static QuoteCommitRequest convert2QuoteCommitRequest(User user, SaveCdqStoreSkuRequest request, String os) {
        QuoteCommitRequest quoteCommitRequest = new QuoteCommitRequest();
        quoteCommitRequest.setTenantId(request.getTenantId());
        quoteCommitRequest.setStoreId(request.getStoreId());
        quoteCommitRequest.setSkuId(request.getCdqStoreSkuWithChannelInfoVo().getCdqStoreSkuVo().getSkuId());
        quoteCommitRequest.setQuoterId(user.getAccountId());
        quoteCommitRequest.setQuoterName(user.getOperatorName());
        if (ANDROID.equalsIgnoreCase(os)) {
            quoteCommitRequest.setSource(com.sankuai.meituan.shangou.empower.ocms.client.price.enums.quote.DeviceTypeEnum.ANDROID);
        } else if (IOS.equalsIgnoreCase(ApiMethodParamThreadLocal.getIdentityInfo().getOs())) {
            quoteCommitRequest.setSource(com.sankuai.meituan.shangou.empower.ocms.client.price.enums.quote.DeviceTypeEnum.IOS);
        }
        quoteCommitRequest.setQuotePrice(MoneyUtils.yuanToCent(request.getCdqStoreSkuWithChannelInfoVo().getCdqStoreSkuVo().getStorePrice()));
        return quoteCommitRequest;
    }

    public static QueryQuoteReviewsRequest convert2ToReviewQuoteQueryRequest(long tenantId, long storeId, String skuId) {
        QueryQuoteReviewsRequest queryQuoteReviewsRequest = new QueryQuoteReviewsRequest();
        queryQuoteReviewsRequest.setTenantId(tenantId);
        queryQuoteReviewsRequest.setStoreIds(Lists.newArrayList(storeId));
        queryQuoteReviewsRequest.setSkuIds(Lists.newArrayList(skuId));
        queryQuoteReviewsRequest.setPageNum(1);
        queryQuoteReviewsRequest.setPageSize(1);
        queryQuoteReviewsRequest.setReviewStatus(Lists.newArrayList(ReviewStatusEnum.TO_REVIEW));
        queryQuoteReviewsRequest.setQueryQuoteMode(QueryQuoteModeEnum.ALL);
        return queryQuoteReviewsRequest;
    }

    public static QuoteDeleteRequest convert2QuoteDeleteRequest(long tenantId, long quoteRecordId) {
        QuoteDeleteRequest quoteDeleteRequest = new QuoteDeleteRequest();
        quoteDeleteRequest.setTenantId(tenantId);
        quoteDeleteRequest.setQuoteRecordId(Lists.newArrayList(quoteRecordId));
        return quoteDeleteRequest;
    }

    public static SkuPriceChangeByWeightReqeust convert2SkuPriceChangeByWeightRequest(PriceChangePreviewRequest request) {
        SkuPriceChangeByWeightReqeust skuPriceChangeByWeightReqeust = new SkuPriceChangeByWeightReqeust();
        skuPriceChangeByWeightReqeust.setTenantId(request.getTenantId());
        List<StoreSkuPriceInfoDTO> skuPriceInfos = Lists.newArrayList();
        StoreSkuPriceInfoDTO storeSkuPriceInfoDTO = new StoreSkuPriceInfoDTO();
        storeSkuPriceInfoDTO.setStoreId(request.getStoreId());
        storeSkuPriceInfoDTO.setSkuId(request.getSkuId());
        storeSkuPriceInfoDTO.setWeight(request.getWeight());
        skuPriceInfos.add(storeSkuPriceInfoDTO);
        skuPriceChangeByWeightReqeust.setSkuPriceInfos(skuPriceInfos);
        return skuPriceChangeByWeightReqeust;
    }

    /*
    接口更换为审核接口
    */
    public static BatchQuoteReviewRequest convert2BatchQuoteReviewedRequest(User user, long quoteRecordId) {
        BatchQuoteReviewRequest batchQuoteReviewRequest = new BatchQuoteReviewRequest();
        batchQuoteReviewRequest.setTenantId(user.getTenantId());
        batchQuoteReviewRequest.setQuoteIds(Lists.newArrayList(quoteRecordId));
        batchQuoteReviewRequest.setReviewOperate(ReviewOperateEnum.DISABLE);
        batchQuoteReviewRequest.setReviewClientType(ReviewClientEnum.APP);
        batchQuoteReviewRequest.setReviewerId(user.getAccountId());
        batchQuoteReviewRequest.setReviewerName(user.getOperatorName());
        return batchQuoteReviewRequest;
    }

    public static PageQueryTenantRegionSpuByNameRequest convertRequest(QueryTenantRegionSpuByNameRequest regionSpuByNmeRequest, User user) {
        if (regionSpuByNmeRequest == null) {
            return null;
        }
        PageQueryTenantRegionSpuByNameRequest request = new PageQueryTenantRegionSpuByNameRequest();
        request.setTenantId(user.getTenantId());
        request.setKeywords(regionSpuByNmeRequest.getKeyword());
        request.setTargetStoreId(regionSpuByNmeRequest.getStoreId());
        request.setPage(regionSpuByNmeRequest.getPage());
        request.setPageSize(regionSpuByNmeRequest.getSize());
        request.setOffset(regionSpuByNmeRequest.getScrollId());
        return request;

    }

    public static QueryTenantRegionSpuByNameResponseVO convertSuggestStoreSpuListResponse(PageQueryTenantRegionSpuByNameResponse regionSpuByNameResponse) {

        QueryTenantRegionSpuByNameResponseVO response = new QueryTenantRegionSpuByNameResponseVO();
        List<SuggestSpuVO> suggestSpuVOS = new ArrayList<>();
        if (ResultCodeEnum.SUCCESS.getValue() == regionSpuByNameResponse.getCode() && CollectionUtils.isNotEmpty(regionSpuByNameResponse.getSuggestStoreSpuList())) {
            for (SuggestStoreSpuDTO spuDTO : regionSpuByNameResponse.getSuggestStoreSpuList()) {
                suggestSpuVOS.add(new SuggestSpuVO(spuDTO));
            }
        }
        response.setSuggestSpuList(suggestSpuVOS);
        PageInfoVO pageInfoVO = new PageInfoVO();

        if (regionSpuByNameResponse.getPageInfo() != null) {
            pageInfoVO.setPage(regionSpuByNameResponse.getPageInfo().getPage());
            pageInfoVO.setSize(regionSpuByNameResponse.getPageInfo().getSize());
            pageInfoVO.setTotalPage(regionSpuByNameResponse.getPageInfo().getTotalPage());
            pageInfoVO.setTotalSize(regionSpuByNameResponse.getPageInfo().getTotal());
            pageInfoVO.setScrollId(regionSpuByNameResponse.getPageInfo().getOffset());
        }

        response.setPageInfo(pageInfoVO);
        return response;
    }

    public static BatchUpdateStoreSpuStatusRequest convertRequest(BatchChangeSpuStatusRequest request, User user) {
        BatchUpdateStoreSpuStatusRequest batchUpdateStoreSpuStatusRequest = new BatchUpdateStoreSpuStatusRequest();
        batchUpdateStoreSpuStatusRequest.setTenantId(user.getTenantId());
        batchUpdateStoreSpuStatusRequest.setOperatorName(user.getOperatorName());
        batchUpdateStoreSpuStatusRequest.setOperatorAccount(user.getAccountName());
        batchUpdateStoreSpuStatusRequest.setOperatorId(user.getAccountId());
        batchUpdateStoreSpuStatusRequest.setStoreId(request.getStoreId());
       // batchUpdateStoreSpuStatusRequest.setSpuList(convertChannelStoreSpuParamDTOList(request.getStoreId(), request.getSpuIdList(), request.getChannelIds()));
        batchUpdateStoreSpuStatusRequest.setSpuList(getChannelStoreSpuList(request));
        batchUpdateStoreSpuStatusRequest.setSellStatus(request.getSpuStatus());
        if (CollectionUtils.isNotEmpty(request.getStoreCategorys())) {
            batchUpdateStoreSpuStatusRequest.setStoreCategoryIds(request.getStoreCategorys());
        }
        if (Objects.nonNull(request.getAutoOnSale())) {
            batchUpdateStoreSpuStatusRequest.setAutoOnSale(request.getAutoOnSale() == AUTO_ON_SALE_YES);
        } else {
            batchUpdateStoreSpuStatusRequest.setAutoOnSale(false);
        }
        batchUpdateStoreSpuStatusRequest.setCheckPermit(request.isCheckPermit());
        batchUpdateStoreSpuStatusRequest.setOperateSourceType(SkuOperateSourceType.APP.getValue());
        batchUpdateStoreSpuStatusRequest.setCheckOnSale(request.isCheckOnSale());
        return batchUpdateStoreSpuStatusRequest;
    }

    /**
     * 前端请求中spuIdList非空 走这里
     * @param storeId
     * @param spuIdList
     * @param channelIdList
     * @return
     */
    private static List<ChannelStoreSpuParamDTO> convertChannelStoreSpuParamDTOList(Long storeId, List<String> spuIdList,
                                                                                    List<Integer> channelIdList) {
        if (CollectionUtils.isEmpty(spuIdList)) {
            return Lists.newArrayList();
        }

        List<ChannelStoreSpuParamDTO> channelStoreSpuParamDTOList = Lists.newArrayList();
        for (String spuId : spuIdList) {
            for (Integer channelId : channelIdList) {
                ChannelStoreSpuParamDTO channelStoreSpuParamDTO = new ChannelStoreSpuParamDTO();
                channelStoreSpuParamDTO.setSpuId(spuId);
                channelStoreSpuParamDTO.setStoreId(storeId);
                channelStoreSpuParamDTO.setChannelId(channelId);
                channelStoreSpuParamDTOList.add(channelStoreSpuParamDTO);
            }
        }

        return channelStoreSpuParamDTOList;
    }

    /**
     * 前端请求中spuList不为空在走这里（携带零售价）
     * @param request
     * @return
     */
    public static List<ChannelStoreSpuParamDTO> getChannelStoreSpuList(BatchChangeSpuStatusRequest request) {
        List<StoreSpuOnlineParamVO> storeSpuList = request.getSpuList();
        Long storeId  = request.getStoreId();
        List<Integer> channelList = request.getChannelIds();

        if (CollectionUtils.isEmpty(storeSpuList)) {
            return convertChannelStoreSpuParamDTOList(storeId, request.getSpuIdList(), channelList);
        }

        // 当前走这里场景：租户为手动定价模式 &  app端单条SPU上架 & 该SPU之前未上线，前端强制要求填写渠道价格，渠道价格会填充
        List<ChannelStoreSpuParamDTO> channelStoreSpuParamDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(channelList)) {
            // 老逻辑存在遍历channelList场景，仍保留该逻辑
            for (Integer channelId : channelList) {
                for (StoreSpuOnlineParamVO storeSpuParamVO : storeSpuList) {
                    if (storeSpuParamVO == null || org.apache.commons.lang3.StringUtils.isBlank(storeSpuParamVO.getSpuId()) || storeId < 0L) {
                        continue;
                    }
                    ChannelStoreSpuParamDTO channelStoreSpuParamDTO = new ChannelStoreSpuParamDTO();
                    channelStoreSpuParamDTO.setChannelId(channelId);
                    channelStoreSpuParamDTO.setSpuId(storeSpuParamVO.getSpuId());
                    channelStoreSpuParamDTO.setStoreId(storeId);

                    // 如果有指定零售价, 则组装零售价参数
                    List<SkuChannelPriceDTO> skuChannelPriceDTOS = getSkuChannelPriceDtoList(storeSpuParamVO.getSkuAdjustPriceVOS(), channelId);

                    // 有指定渠道零售价，才填入； 兼容没有指定零售价的链路
                    if (CollectionUtils.isNotEmpty(skuChannelPriceDTOS)) {
                        channelStoreSpuParamDTO.setSkuChannelPriceDTOS(skuChannelPriceDTOS);
                    }

                    channelStoreSpuParamDTOS.add(channelStoreSpuParamDTO);
                }
            }
        } else {
            for (StoreSpuOnlineParamVO storeSpuParamVO : storeSpuList) {
                if (storeSpuParamVO == null || org.apache.commons.lang3.StringUtils.isBlank(storeSpuParamVO.getSpuId()) || storeId < 0L) {
                    continue;
                }
                ChannelStoreSpuParamDTO channelStoreSpuParamDTO = new ChannelStoreSpuParamDTO();
                channelStoreSpuParamDTO.setSpuId(storeSpuParamVO.getSpuId());
                channelStoreSpuParamDTO.setStoreId(storeId);

                // 如果有指定零售价则组装
                List<SkuChannelPriceDTO> skuChannelPriceDTOS = getSkuChannelPriceDtoList(storeSpuParamVO.getSkuAdjustPriceVOS(), null);
                // 有指定渠道零售价，才填入； 兼容没有指定零售价的链路
                if (CollectionUtils.isNotEmpty(skuChannelPriceDTOS)) {
                    channelStoreSpuParamDTO.setSkuChannelPriceDTOS(skuChannelPriceDTOS);
                }

                channelStoreSpuParamDTOS.add(channelStoreSpuParamDTO);
            }
        }
        return channelStoreSpuParamDTOS;
    }

    /**
     * 构建手动定价模式下，SKU各渠道零售价格
     * @param skuAdjustPriceVOS
     * @param channelId 若指定了渠道id，则只返回SKU对应渠道的价格
     * @return
     */
    public static List<SkuChannelPriceDTO> getSkuChannelPriceDtoList(List<SkuAdjustPriceVO> skuAdjustPriceVOS, Integer channelId) {
        if (CollectionUtils.isEmpty(skuAdjustPriceVOS)) {
            return  Collections.emptyList();
        }

        List<SkuChannelPriceDTO> skuChannelPriceDTOS = new ArrayList<>(skuAdjustPriceVOS.size());

        for (SkuAdjustPriceVO skuAdjustPriceVO : skuAdjustPriceVOS) {
            // 单个SKU价格信息
            SkuChannelPriceDTO skuChannelPriceDTO = new SkuChannelPriceDTO();

            skuChannelPriceDTO.setSkuId(skuAdjustPriceVO.getSkuId());

            List<ChannelSyncStrategyVO> skuPriceStrategies = skuAdjustPriceVO.getSkuPriceStrategies();
            if (CollectionUtils.isEmpty(skuPriceStrategies)) {
                continue;
            }

            // 单个SKU下的各渠道价格
            List<ChannelPriceDTO> channelPriceDTOS = new ArrayList<>(skuPriceStrategies.size());
            for (ChannelSyncStrategyVO channelSyncStrategyVO : skuPriceStrategies) {
                // 如果指定渠道，则只能填充对应渠道零售价
                if (Objects.nonNull(channelId) && !Objects.equals(channelId, channelSyncStrategyVO.getChannelId())) {
                    continue;
                }
                ChannelPriceDTO channelPriceDTO = new ChannelPriceDTO();
                channelPriceDTO.setChannelId(channelSyncStrategyVO.getChannelId());
                channelPriceDTO.setFixedPrice(channelSyncStrategyVO.getFixedPrice());

                channelPriceDTOS.add(channelPriceDTO);
            }

            // 没有任何渠道零售价
            if (CollectionUtils.isEmpty(channelPriceDTOS)) {
                continue;
            }

            skuChannelPriceDTO.setChannelPriceDTOS(channelPriceDTOS);

            skuChannelPriceDTOS.add(skuChannelPriceDTO);
        }

        return skuChannelPriceDTOS;
    }

    public static BatchChangeSpuStatusResponseVO convertResponse(GeneralResponse response) {
        BatchChangeSpuStatusResponseVO responseVO = new BatchChangeSpuStatusResponseVO();
        responseVO.setCode(response.getCode());
        responseVO.setMsg(response.getMsg());
        responseVO.setErrorRecordList(convertChannelSpuFailRecordVOList(response.getFailedList()));
        return responseVO;
    }

    public static BatchChangeSpuStatusResponseVO convertResponse(com.sankuai.meituan.shangou.empower.productbiz.client.response.CommonResponse response) {
        BatchChangeSpuStatusResponseVO responseVO = new BatchChangeSpuStatusResponseVO();
        responseVO.setCode(response.getStatus().getCode());
        responseVO.setMsg(response.getStatus().getMsg());
        responseVO.setErrorRecordList(Collections.emptyList());
        return responseVO;
    }

    public static List<ChannelSpuFailRecordVO> convertChannelSpuFailRecordVOList(List<ChannelStoreFailDTO> failedRecordDTOS) {
        if (Objects.isNull(failedRecordDTOS)) {
            return Lists.newArrayList();
        }

        List<ChannelSpuFailRecordVO> errorRecordVOList = Lists.newArrayList();
        for (ChannelStoreFailDTO failedRecordDTO : failedRecordDTOS) {
            ChannelSpuFailRecordVO errorRecordVO = new ChannelSpuFailRecordVO();
            errorRecordVO.setSpuId(failedRecordDTO.getSpuId());
            errorRecordVO.setStoreId(failedRecordDTO.getStoreId());
            errorRecordVO.setChannelId(failedRecordDTO.getChannelId());
            errorRecordVO.setHasRetailPriceToReview(failedRecordDTO.isHasRetailPriceToReview());
            errorRecordVO.setHasOfflinePriceToReview(failedRecordDTO.isHasOfflinePriceToReview());
            errorRecordVO.setHasZeroRetailPrice(failedRecordDTO.isHasZeroRetailPrice());
            errorRecordVO.setHasZeroOfflinePrice(failedRecordDTO.isHasZeroOfflinePrice());
            errorRecordVO.setErrorMsg(failedRecordDTO.getErrorMsg());
            errorRecordVO.setErrorCode(failedRecordDTO.getErrorCode());
            errorRecordVOList.add(errorRecordVO);
        }

        return errorRecordVOList;
    }

    public static BatchChangeStoreSpuFrontCategoryRequest convertRequest(BatchChangeSpuFrontCategoryRequest request, User user) {
        BatchChangeStoreSpuFrontCategoryRequest batchChangeStoreSpuFrontCategoryRequest = new BatchChangeStoreSpuFrontCategoryRequest();
        batchChangeStoreSpuFrontCategoryRequest.setTenantId(user.getTenantId());
        batchChangeStoreSpuFrontCategoryRequest.setOperatorId(user.getAccountId());
        batchChangeStoreSpuFrontCategoryRequest.setOperatorName(user.getOperatorName());
        batchChangeStoreSpuFrontCategoryRequest.setOperateSourceType(SkuOperateSourceType.APP.getValue());
        List<ChannelStoreSpuFrontCategoryDTO> channelStoreSpuFrontCategoryDTOList = request.getChannelSpuFrontCategoryList().stream()
                .filter(Objects::nonNull)
                .map(e -> buildChannelStoreSpuFrontCategoryDTO(e, request.getStoreId()))
                .collect(Collectors.toList());
        batchChangeStoreSpuFrontCategoryRequest.setSpuList(channelStoreSpuFrontCategoryDTOList);
        return batchChangeStoreSpuFrontCategoryRequest;
    }

    private static ChannelStoreSpuFrontCategoryDTO buildChannelStoreSpuFrontCategoryDTO(ChannelStoreSpuFrontCategoryVO channelStoreSkuFrontCategoryVO, Long storeId) {
        ChannelStoreSpuFrontCategoryDTO channelStoreSpuFrontCategoryDTO = new ChannelStoreSpuFrontCategoryDTO();
        channelStoreSpuFrontCategoryDTO.setSpuId(channelStoreSkuFrontCategoryVO.getSpuId());
        channelStoreSpuFrontCategoryDTO.setStoreId(storeId);
        channelStoreSpuFrontCategoryDTO.setChannelId(channelStoreSkuFrontCategoryVO.getChannelId());
        channelStoreSpuFrontCategoryDTO.setFrontCategoryIds(channelStoreSkuFrontCategoryVO.getFrontCategoryCodeList());
        return channelStoreSpuFrontCategoryDTO;
    }

    public static BatchChangeSpuFrontCategoryResponseVO convertChangeChannelSpuFrontCategoryResponse(GeneralResponse response) {
        BatchChangeSpuFrontCategoryResponseVO batchChangeSpuFrontCategoryResponseVO = new BatchChangeSpuFrontCategoryResponseVO();
        batchChangeSpuFrontCategoryResponseVO.setTaskId(response.getTaskId());
        if (CollectionUtils.isNotEmpty(response.getFailedList())) {
            List<ChannelSpuFailRecordVO> errorRecordList = response.getFailedList().stream().filter(Objects::nonNull)
                    .map(OCMSUtils::buildChannelSpuFailRecordVO)
                    .collect(Collectors.toList());
            batchChangeSpuFrontCategoryResponseVO.setErrorRecordList(errorRecordList);
        }
        return batchChangeSpuFrontCategoryResponseVO;
    }

    private static ChannelSpuFailRecordVO buildChannelSpuFailRecordVO(ChannelStoreFailDTO dto) {
        ChannelSpuFailRecordVO channelSpuFailRecordVO = new ChannelSpuFailRecordVO();
        channelSpuFailRecordVO.setSpuId(dto.getSpuId());
        channelSpuFailRecordVO.setStoreId(dto.getStoreId());
        channelSpuFailRecordVO.setChannelId(dto.getChannelId());
        channelSpuFailRecordVO.setErrorCode(dto.getErrorCode());
        channelSpuFailRecordVO.setErrorMsg(dto.getErrorMsg());
        return channelSpuFailRecordVO;
    }

    public static BatchUpdateStoreStockRequest convertRequest(UpdateStockRequest request, User user) {
        BatchUpdateStoreStockRequest batchUpdateStoreStockRequest = new BatchUpdateStoreStockRequest();
        batchUpdateStoreStockRequest.setTenantId(user.getTenantId());
        batchUpdateStoreStockRequest.setStoreSkuSaleStockList(convertStoreSkuSaleStockDTOList(request.getSkuStockList(), request.getStoreId()));
        batchUpdateStoreStockRequest.setOperatorId(user.getEmployeeId());
        batchUpdateStoreStockRequest.setOperatorName(user.getOperatorName());
        return batchUpdateStoreStockRequest;
    }

    private static List<StoreSkuSaleStockDTO> convertStoreSkuSaleStockDTOList(List<StoreSkuStockVO> skuStockList, Long storeId) {
        if (Objects.isNull(skuStockList))
            return Lists.newArrayList();
        List<StoreSkuSaleStockDTO> storeSkuSaleStockDTOList = Lists.newArrayList();
        for (StoreSkuStockVO storeSkuStockVO : skuStockList) {
            StoreSkuSaleStockDTO storeSkuSaleStockDTO = new StoreSkuSaleStockDTO();
            storeSkuSaleStockDTO.setStoreId(storeId);
            storeSkuSaleStockDTO.setQuantity(storeSkuStockVO.getStock());
            storeSkuSaleStockDTO.setSkuCode(storeSkuStockVO.getSkuId());
            storeSkuSaleStockDTO.setCustomizeStockFlag(storeSkuStockVO.getCustomizeStockFlag());
            storeSkuSaleStockDTO.setAutoResumeInfiniteStock(storeSkuStockVO.getAutoResumeInfiniteStock());
            storeSkuSaleStockDTOList.add(storeSkuSaleStockDTO);
        }
        return storeSkuSaleStockDTOList;
    }

    public static List<FrontCategoryWithPathDTO> convert2FrontCategoryWithPathDTO(List<FrontCategorySimpleVO> frontCategorySimpleVOList) {
        if (CollectionUtils.isEmpty(frontCategorySimpleVOList)) {
            return org.assertj.core.util.Lists.newArrayList();
        }
        List<FrontCategoryWithPathDTO> frontCategoryWithPathDTOList = org.assertj.core.util.Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(frontCategorySimpleVOList)) {
            for (FrontCategorySimpleVO frontCategorySimpleVO : frontCategorySimpleVOList) {
                frontCategoryWithPathDTOList.add(frontCategorySimpleVO.buildFrontCategoryWithPathDTO());
            }
        }
        return frontCategoryWithPathDTOList;
    }

    public static com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelCategoryDTO convert2ChannelCategoryDTO(ChannelCategoryVO channelCategoryVO) {
        com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelCategoryDTO categoryDTO = new com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelCategoryDTO();
        if (channelCategoryVO == null) {
            return categoryDTO;
        }
        categoryDTO.setChannelCategoryCode(channelCategoryVO.getChannelCategoryCode());
        categoryDTO.setChannelCategoryName(channelCategoryVO.getChannelCategoryName());
        categoryDTO.setChannelCategoryCodePath(channelCategoryVO.getChannelCategoryCodePath());
        categoryDTO.setChannelCategoryNamePath(categoryDTO.getChannelCategoryNamePath());
        categoryDTO.setChannelDynamicInfoDTOList(ChannelDynamicInfoVO.toDTOList(channelCategoryVO.getChannelDynamicInfoVOList()));
        return categoryDTO;
    }

    public static List<Integer> toAuditStatusList(Integer mtAllowSale, Integer hasAuditRejected, Integer hasAuditing) {
        //便利店模式单独传递审核驳回和审核中状态，其他模式传递不可售状态
        if (mtAllowSale > AllowSaleEnum.ALL.getCode()) {//是否可售
            return ChannelAuditStatusEnum.ofAllowSale(mtAllowSale).stream()
                    .map(ChannelAuditStatusEnum::getCode).collect(Collectors.toList());
        } else if (hasAuditRejected == 1 && hasAuditing == 0) {//审核驳回
            return ChannelAuditStatusEnum.getAllRejectStatusCodes();
        } else if (hasAuditRejected == 0 && hasAuditing == 1) {//审核中
            return Lists.newArrayList(ChannelAuditStatusEnum.AUDITING_FOR_BEFORE_RELEASE.getCode());
        } else if (hasAuditRejected == 1 && hasAuditing == 1) {//同时选择审核驳回和审核中
            List<Integer> rejectedCodes = ChannelAuditStatusEnum.getAllRejectStatusCodes();
            rejectedCodes.add(ChannelAuditStatusEnum.AUDITING_FOR_BEFORE_RELEASE.getCode());
            return rejectedCodes;
        } else {
            return Collections.emptyList();
        }
    }

    public static List<Integer> toNormAuditStatusList(Integer mtAllowSale, Integer hasAuditRejected, Integer hasAuditing) {
        //便利店模式单独传递审核驳回和审核中状态，其他模式传递不可售状态
        if (mtAllowSale > AllowSaleEnum.ALL.getCode()) {//是否可售
            return ChannelNormAuditStatusEnum.ofAllowSale(mtAllowSale).stream()
                    .map(ChannelNormAuditStatusEnum::getCode).collect(Collectors.toList());
        } else if (hasAuditRejected == 1 && hasAuditing == 0) {//审核驳回
            return ChannelNormAuditStatusEnum.getAllRejectStatusCodes();
        } else if (hasAuditRejected == 0 && hasAuditing == 1) {//审核中
            return Lists.newArrayList(ChannelNormAuditStatusEnum.AUDITING_FOR_BEFORE_RELEASE.getCode());
        } else if (hasAuditRejected == 1 && hasAuditing == 1) {//同时选择审核驳回和审核中
            List<Integer> allNormStatusList = ChannelNormAuditStatusEnum.getAllRejectStatusCodes();
            allNormStatusList.add(ChannelNormAuditStatusEnum.AUDITING_FOR_BEFORE_RELEASE.getCode());
            return allNormStatusList;
        } else {
            return Collections.emptyList();
        }
    }


    public static ChannelCategoryRelationVO convertQueryChannelCategoryResponse(QueryChannelCategoryResponse response) {
        ChannelCategoryRelationVO relationVO = new ChannelCategoryRelationVO();
        List<ChannelCategoryVO> categoryVOList = Lists.newArrayList();

        if (ResultCodeEnum.SUCCESS.getValue() == response.getCode() && Objects.nonNull(response.getChannelCategoryInfo())) {
            CategoryBindChannelCategoryDTO channelCategoryInfo = response.getChannelCategoryInfo();
            Map<Integer, ChannelCategoryWithPathDTO> channelCategoryMap = channelCategoryInfo.getChannelCategoryMap();
            if (!channelCategoryMap.isEmpty()) {
                channelCategoryMap.entrySet().forEach(entry -> {
                    Integer channelId = entry.getKey();
                    ChannelCategoryWithPathDTO value = entry.getValue();
                    ChannelCategoryVO vo = new ChannelCategoryVO();
                    vo.setChannelId(channelId);
                    vo.setChannelCategoryCode(value.getChannelCategoryCode());
                    vo.setChannelCategoryName(value.getChannelCategoryName());
                    vo.setChannelCategoryCodePath(value.getChannelCategoryCodePath());
                    vo.setChannelCategoryNamePath(value.getChannelCategoryNamePath());
                    categoryVOList.add(vo);
                });
            }
        }
        relationVO.setCategoryVOList(categoryVOList);

        return relationVO;
    }

    public static boolean isPhoneHasMusk(String phoneNo) {
        return StringUtils.contains(phoneNo, PHONE_MUSK);
    }

}
