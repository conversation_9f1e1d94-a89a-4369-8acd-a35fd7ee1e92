package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSkuSimpleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NonNull;

import java.util.List;

/**
 * @Title: AddTenantSkuApiRequest
 * @Description: 新增租户SKU（新增规格）请求参数
 * @Author: zhaolei12
 * @Date: 2020/5/24 9:54 上午
 */
@TypeDoc(
        description = "新增租户SKU（新增规格）请求参数"
)
@Data
@ApiModel("新增租户SKU（新增规格）请求参数")
public class AddTenantSkuApiRequest {

    @FieldDoc(
            description = "SPU编码"
    )
    @ApiModelProperty(value = "SPU编码")
    @NonNull
    private String spuId;

    @FieldDoc(
            description = "租户商品SKU集合"
    )
    @ApiModelProperty(value = "租户商品新增规格集合")
    @NonNull
    private List<TenantSkuSimpleVO> skuList;

}
