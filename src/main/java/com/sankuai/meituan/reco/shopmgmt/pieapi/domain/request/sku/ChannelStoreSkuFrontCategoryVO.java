package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelStoreSkuFrontCategoryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 渠道门店商品前台分类参数集合
 * @author: liyu44
 * @create: 2020-02-04
 **/
@TypeDoc(
        description = "渠道门店商品前台分类参数集合"
)
@Data
@ApiModel("渠道门店商品前台分类参数集合")
public class ChannelStoreSkuFrontCategoryVO {

    @FieldDoc(
            description = "商品ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品ID", required = true)
    @NotNull
    private String skuId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "店内分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "店内分类ID", required = true)
    @NotNull
    private Long frontCategoryId;

}
