package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuReviewBatchRecallApplyRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.review.BatchRecallProductReviewRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

/**
 * @description: 撤回提交申请
 * @author: WangSukuan
 * @create: 2020-03-10
 **/
@TypeDoc(
        description = "撤回提交申请"
)
@Data
@ApiModel("撤回提交申请")
public class BatchSkuReviewRecallApplyRequest {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "商品审核id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品审核id", required = true)
    @NotNull
    private Set<Long> skuReviewIdSet = new HashSet<>();


    public SkuReviewBatchRecallApplyRequest convertSkuReviewBatchRecallApplyRequest(User user){

        SkuReviewBatchRecallApplyRequest skuReviewBatchRecallApplyRequest = new SkuReviewBatchRecallApplyRequest();
        skuReviewBatchRecallApplyRequest.setTenantId(user.getTenantId());
        skuReviewBatchRecallApplyRequest.setSkuReviewIdSet(this.getSkuReviewIdSet());
        skuReviewBatchRecallApplyRequest.setOperateId(user.getAccountId());
        skuReviewBatchRecallApplyRequest.setOperateName(user.getOperatorName());
        return skuReviewBatchRecallApplyRequest;
    }

    public BatchRecallProductReviewRequest convertToBatchRecallProductReviewRequest(User user){
        BatchRecallProductReviewRequest request = new BatchRecallProductReviewRequest();
        request.setTenantId(user.getTenantId());
        request.setReviewIds(new ArrayList<>(this.getSkuReviewIdSet()));
        request.setOperateId(user.getAccountId());
        request.setOperateName(user.getOperatorName());
        return request;
    }

}
