package com.sankuai.meituan.reco.shopmgmt.pieapi.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.meituan.mtrace.http.TraceMethodInterceptor;
import com.sankuai.meituan.reco.shopmgmt.pieapi.config.exception.CustomHandlerExceptionResolver;
import com.sankuai.meituan.reco.shopmgmt.pieapi.config.interceptors.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurityInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Configuration
@ComponentScan("com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity")
public class WebMvcConfigurer extends WebMvcConfigurerAdapter {

	/**
	 * 数据鉴权拦截器
	 */
	@Resource
	private DataSecurityInterceptor dataSecurityInterceptor;

	@Override
	public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
		configurer.enable();
		super.configureDefaultServletHandling(configurer);
	}

	@Bean
	public ApiMethodParamInterceptor apiMethodParamInterceptor() {
		return new ApiMethodParamInterceptor();
	}

	@Bean
	public AuthInterceptor authInterceptor() {
		return new AuthInterceptor();
	}

	@Bean
	public ApiMethodStatisticsInterceptor apiMethodStatisticsInterceptor() {
		return new ApiMethodStatisticsInterceptor();
	}

	@Bean
	public StandardSpecInterceptor standardSpecInterceptor(){return new StandardSpecInterceptor();}

	@Bean
	public SpuGrayInterceptor spuGrayInterceptor(){return new SpuGrayInterceptor();}

	@Bean
	public OrderGrayInterceptor orderGrayInterceptor(){return new OrderGrayInterceptor();}

	@Override
	public void addInterceptors(InterceptorRegistry registry) {


		// https://km.sankuai.com/collabpage/28247159
		// 如果曾经配置过老版的TraceHandlerInterceptor，请替换成TraceFilter+TraceMethodInterceptor组合。老版TraceHandlerInterceptor有缺陷，无法覆盖其他ServletFilter中的对外调用。
		TraceMethodInterceptor traceInterceptor = new TraceMethodInterceptor();
		registry.addInterceptor(traceInterceptor).addPathPatterns("/**");

		// 数据鉴权拦截器
		registry.addInterceptor(dataSecurityInterceptor)
				.addPathPatterns("/**")
		;

		/**
		 * 拦截通用参数
		 */
		registry.addInterceptor(apiMethodParamInterceptor())
				.addPathPatterns("/**")
				.excludePathPatterns("/monitor/alive")
				.excludePathPatterns("/static/**")
				.excludePathPatterns("/webjars/**")
				.excludePathPatterns("/swagger-resources/**")
				.excludePathPatterns("/v2/api-docs/**")
				.excludePathPatterns("/META-INF/**")
				.excludePathPatterns("/configuration/**")
				.excludePathPatterns("swagger-ui.html");

		registry.addInterceptor(authInterceptor())
				.addPathPatterns("/**")
				.excludePathPatterns("/monitor/alive")
				.excludePathPatterns("/static/**")
				.excludePathPatterns("/webjars/**")
				.excludePathPatterns("/swagger-resources/**")
				.excludePathPatterns("/v2/api-docs/**")
				.excludePathPatterns("/META-INF/**")
				.excludePathPatterns("/configuration/**")
				.excludePathPatterns("swagger-ui.html");

		registry.addInterceptor(apiMethodStatisticsInterceptor())
				.addPathPatterns("/**")
				.excludePathPatterns("/monitor/alive")
				.excludePathPatterns("/static/**")
				.excludePathPatterns("/webjars/**")
				.excludePathPatterns("/swagger-resources/**")
				.excludePathPatterns("/v2/api-docs/**")
				.excludePathPatterns("/META-INF/**")
				.excludePathPatterns("/configuration/**")
				.excludePathPatterns("swagger-ui.html");
		/*
		配置通用参数就可以
		 */
		registry.addInterceptor(standardSpecInterceptor())
				.addPathPatterns("/**")
				.excludePathPatterns("/monitor/alive")
				.excludePathPatterns("/static/**")
				.excludePathPatterns("/webjars/**")
				.excludePathPatterns("/swagger-resources/**")
				.excludePathPatterns("/v2/api-docs/**")
				.excludePathPatterns("/META-INF/**")
				.excludePathPatterns("/configuration/**")
				.excludePathPatterns("swagger-ui.html");

		/*
		spu多规格建设，访问sku老接口将被拦截。审核相关接口放开。
		 */
		registry.addInterceptor(spuGrayInterceptor())
				.addPathPatterns("/pieapi/ocms/skuAndStock/**")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/saveSkuReviewApply")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/querySkuReviewList")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/getSkuReviewInfo")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/batchRecallApply")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/saveReasonTemplate")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/batchSaveReasonTemplate")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/deleteReasonTemplate")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/batchDeleteReasonTemplate")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/getReasonTemplate")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/queryReasonTemplateList")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/batchReview")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/querySkuReviewApplyList")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/listSkuTags")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/queryChannelCategory")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/queryChannelCategoryByErpCategory")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/querySkusForPickAndOutwardSourcing")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/getStoreToReviewCount")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/querySp")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/queryCartonMeasure")
				.excludePathPatterns("/pieapi/ocms/skuAndStock/queryChannelCategoryByChannelId")
                .excludePathPatterns("/pieapi/ocms/skuAndStock/getQualification")
                .excludePathPatterns("/pieapi/ocms/skuAndStock/queryAfterSale");


        registry.addInterceptor(orderGrayInterceptor())
				.addPathPatterns("/**")
				.excludePathPatterns("/monitor/alive")
				.excludePathPatterns("/static/**")
				.excludePathPatterns("/webjars/**")
				.excludePathPatterns("/swagger-resources/**")
				.excludePathPatterns("/v2/api-docs/**")
				.excludePathPatterns("/META-INF/**")
				.excludePathPatterns("/configuration/**")
                .excludePathPatterns("swagger-ui.html");



		super.addInterceptors(registry);
	}

	@Override
	public void addResourceHandlers(ResourceHandlerRegistry registry) {
		registry.addResourceHandler("/**").addResourceLocations("classpath:/static/");
		//屏蔽swagger
//		registry.addResourceHandler("swagger-ui.html")
//				.addResourceLocations("classpath:/META-INF/resources/");
		registry.addResourceHandler("/webjars/**")
				.addResourceLocations("classpath:/META-INF/resources/webjars/");
		super.addResourceHandlers(registry);
	}

	@Bean
	public HandlerExceptionResolver customHandlerExceptionResolver() {
		return new CustomHandlerExceptionResolver();
	}

	@Override
	public void configureHandlerExceptionResolvers(
			List<HandlerExceptionResolver> exceptionResolvers) {
		exceptionResolvers.add(customHandlerExceptionResolver());
	}

	@Bean
	public MappingJackson2HttpMessageConverter customJackson2HttpMessageConverter() {
		MappingJackson2HttpMessageConverter jsonConverter = new MappingJackson2HttpMessageConverter();

		ObjectMapper objectMapper = new ObjectMapper();
		/**
		 * 序列换成json时,将所有的long变成string
		 * 因为js中得数字类型不能包含所有的java long值
		 */
		SimpleModule simpleModule = new SimpleModule();
		simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
		simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
		objectMapper.registerModule(simpleModule);

		//设置日期格式
		SimpleDateFormat smt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		objectMapper.setDateFormat(smt);
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		jsonConverter.setObjectMapper(objectMapper);

		//设置中文编码格式
		List<MediaType> list = new ArrayList<MediaType>();
		list.add(MediaType.APPLICATION_JSON_UTF8);
		jsonConverter.setSupportedMediaTypes(list);

		return jsonConverter;
	}

	// 使用阿里 FastJson 作为JSON MessageConverter
	@Override
	public void configureMessageConverters(
			List<HttpMessageConverter<?>> converters) {
		super.configureMessageConverters(converters);
		converters.add(customJackson2HttpMessageConverter());
	}
}
