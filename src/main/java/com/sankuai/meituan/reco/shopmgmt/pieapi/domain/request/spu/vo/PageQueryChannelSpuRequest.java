// Copyright (C) 2020 Meituan
// All rights reserved
package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.request.ChannelProductPageQueryByStoreCategoryIdsRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2020/11/26 下午12:00
 **/
@Data
public class PageQueryChannelSpuRequest {
    private Integer page;
    private Integer pageSize;
    private Long storeId;
    private Integer channelId;
    private List<Long> frontCategoryIds;
    public boolean validate() {
        if (page < 1) {
            return false;
        }
        if (pageSize < 1 || pageSize > 50) {
            return false;
        }
        if (storeId <= 0) {
            return false;
        }
        if (channelId != 100) {
            return false;
        }
        return true;
    }
    public ChannelProductPageQueryByStoreCategoryIdsRequest toChannelProductPageQueryByStoreCategoryIdsRequest() {
        ChannelProductPageQueryByStoreCategoryIdsRequest request = new ChannelProductPageQueryByStoreCategoryIdsRequest();
        request.setChannelId(this.channelId);
        request.setPageNum(page);
        request.setPageSize(pageSize);
        request.setStoreId(this.storeId);
        request.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        request.setStoreCategoryIds(this.frontCategoryIds);
        return request;
    }
}
