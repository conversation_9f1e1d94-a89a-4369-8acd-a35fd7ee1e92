package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/4/30
 */
@TypeDoc(
        description = "查询税率请求"
)
@Data
public class TaxRateOptionQueryRequest {

    @FieldDoc(
            description = "税率查询类型，进项税率传1，销项税率传2"
    )
    @ApiModelProperty(value = "税率查询类型")
    private Integer queryType;

}
