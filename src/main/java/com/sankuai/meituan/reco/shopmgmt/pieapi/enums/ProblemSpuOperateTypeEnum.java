package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 问题商品操作类型
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ProblemSpuOperateTypeEnum {

    // 1从商家端删除;2从蔬果派删除;3向商家端新增;4向蔬果派新增;6信息与商家端一致;7信息与蔬果派一致;9价格与商家端一致;10价格与蔬果派一致
    DELETE_FROM_WAIMAI(1 ,"从商家端删除"),
    DELETE_FROM_SGP(2, "从牵牛花删除"),
    CREATE_IN_WAIMAI(3, "向商家端新增"),
    CREATE_IN_SGP(4, "向牵牛花新增"),
    REPAIR_BASE_INFO_BY_WAIMAI(6, "信息与商家端一致"),
    REPAIR_BASE_INFO_BY_SGP(7, "信息与牵牛花一致"),
    REPAIR_DETAIL_PRICE_BY_WAIMAI(9, "价格与商家端一致"),
    REPAIR_DETAIL_PRICE_BY_SGP(10, "价格与牵牛花一致");

    private int code;
    private String desc;

    public static ProblemSpuOperateTypeEnum findByCode(Integer code) {

        if (code == null) {
            return null;
        }

        for (ProblemSpuOperateTypeEnum typeEnum : ProblemSpuOperateTypeEnum.values()) {
            if (typeEnum.getCode() == code) {
                return typeEnum;
            }
        }

        return null;
    }
}