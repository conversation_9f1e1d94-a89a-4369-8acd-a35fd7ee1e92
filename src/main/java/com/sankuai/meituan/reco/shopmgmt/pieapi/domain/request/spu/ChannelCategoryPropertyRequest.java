package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(
        name = "渠道类目属性请求",
        description = "渠道类目属性请求"
)
@Setter
@Getter
@EqualsAndHashCode
@ToString
public class ChannelCategoryPropertyRequest{

    @FieldDoc(
            description = "渠道编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道编码")
    private Integer channelId;

    @FieldDoc(
            description = "后台类目编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "后台类目编码")
    private String channelCategoryId;

    public boolean validate() {
        if (this.channelId == null) {
            return false;        }
        if (StringUtils.isBlank(channelCategoryId)) {
            return false;
        }
        return true;
    }
}
