package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <EMAIL>
 * @Date: 2021/2/23 11:47
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum ContractShowStatus {
    CANRENEW(-1, "可以续签"),
    UNSIGNED(1, "待签署"),
    NONEEDTOSIGN(2, "无需签署"),
    SIGNEDOFFLINE(3, "已签署（纸质）"),
    SIGNEDONLINE(4, "已签署（电子）");

    private int code;
    private String desc;

    public static ContractShowStatus getByCode(int code) {
        for (ContractShowStatus s : ContractShowStatus.values()) {
            if (s.getCode() == code) {
                return s;
            }
        }

        return null;
    }
}
