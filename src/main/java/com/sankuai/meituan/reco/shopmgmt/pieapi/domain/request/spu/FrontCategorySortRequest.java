package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelFrontCategorySortRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreCategorySortDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/6/23 8:48 下午
 **/
@TypeDoc(
        description = "店内分类基本请求参数",
        authors = {"zhouyan32"}
)
@Data
@ApiModel("店内分类基本请求参数")
public class FrontCategorySortRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId = 100;

    @FieldDoc(
            description = "排序后的ID列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "排序后的ID列表", required = true)
    private List<String> categoryIds;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.channelId == null) {
            throw new CommonLogicException("渠道id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (CollectionUtils.isEmpty(categoryIds)) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }

    public ChannelFrontCategorySortRequest to(User user) {
        ChannelFrontCategorySortRequest channelFrontCategorySortRequest = new ChannelFrontCategorySortRequest();
        channelFrontCategorySortRequest.setTenantId(user.getTenantId());
        channelFrontCategorySortRequest.setStoreId(storeId);
        channelFrontCategorySortRequest.setChannelId(channelId);
        channelFrontCategorySortRequest.setOperatorId(user.getAccountId());
        channelFrontCategorySortRequest.setOperator(user.getOperatorName());
        channelFrontCategorySortRequest.setChannelStoreCategorySortList(convertSortDto(this.categoryIds));
        return channelFrontCategorySortRequest;
    }

    private List<ChannelStoreCategorySortDTO> convertSortDto(List<String> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.emptyList();
        }

        List<ChannelStoreCategorySortDTO> categoryDtos = Lists.newArrayList();

        for (int i = 0; i < categoryIds.size(); i++) {
            ChannelStoreCategorySortDTO storeCategorySortDTO = new ChannelStoreCategorySortDTO();
            storeCategorySortDTO.setCategoryId(ConverterUtils.nonNullConvert(categoryIds.get(i), Long::valueOf, 0L));
            storeCategorySortDTO.setSort(i + 1);
            categoryDtos.add(storeCategorySortDTO);
        }

        return categoryDtos;

    }

}
