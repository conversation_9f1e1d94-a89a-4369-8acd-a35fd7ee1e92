package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Title: TenantSpuInfoApiRequest
 * @Description: 获取租户商品详情请求参数
 * @Author: wuyongjiang
 * @Date: 2022/8/25 18:55
 */
@TypeDoc(
        description = "获取租户商品详情请求参数"
)
@Data
@ApiModel("获取租户商品详情请求参数")
public class TenantSpuInfoApiRequest {
    @FieldDoc(
            description = "spu", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "spu",required = true)
    @NotNull
    private String spu;
    @FieldDoc(
            description = "spuName", requiredness = Requiredness.OPTIONAL
    )
    private String spuName;
}
