// Copyright (C) 2021 Meituan
// All rights reserved
package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2021/6/1 下午4:16
 **/
public enum ProductManagementTypeEnum {
    BY_SGP("通过牵牛花管理", 1, "BY_SGP"),
    BY_WAIMAI("通过商家端管理", 2, "BY_WAIMAI");
    private String desc;
    private Integer code;
    private String configValue;

    private ProductManagementTypeEnum(String desc, Integer code, String configValue) {
        this.desc = desc;
        this.code = code;
        this.configValue = configValue;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getConfigValue() {
        return configValue;
    }

    public static ProductManagementTypeEnum ofConfigValue(String configValue) {
        if (StringUtils.isBlank(configValue)) {
            return null;
        }

        return Arrays.stream(ProductManagementTypeEnum.values())
                .filter(value -> configValue.equals(value.getConfigValue()))
                .findFirst()
                .orElse(null);
    }
}
