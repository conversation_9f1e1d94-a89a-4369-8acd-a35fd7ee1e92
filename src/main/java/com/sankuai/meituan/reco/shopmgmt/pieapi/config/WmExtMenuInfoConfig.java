package com.sankuai.meituan.reco.shopmgmt.pieapi.config;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.dianping.lion.client.Lion;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 歪马待办扩展配置信息
 * @author: hezhengyu
 * @create: 2024-03-25 14:49
 */
@Data
@Slf4j
@ToString
public class WmExtMenuInfoConfig {


    /**
     * etops对应的待办菜单对应的code
     */
    private String menuCode;

    /**
     * 描述.
     */
    private String desc;

    /**
     * 是否支持多门店查询，等后续接入方都支持多门店后删除
     */
    private boolean supportMultiPoi;

    /**
     * 泛化调用配置
     */
    private String remoteAppkey;

    private String genericServiceName;

    private String methodName;

    public static final String CONFIG_KEY = "waima.todo.ext.menu.info";

    public static List<WmExtMenuInfoConfig> getConfigList() {
        String s = Lion.getConfigRepository().get(CONFIG_KEY);
        if (StringUtils.isBlank(s)) {
            log.info("getWmExtMenuInfoConfigList is null");
            return Lists.newArrayList();
        }
        return JSON.parseArray(s, WmExtMenuInfoConfig.class);
    }

    public static Map<String, WmExtMenuInfoConfig> getConfigMap() {
        return ListUtils.emptyIfNull(getConfigList()).stream().collect(Collectors.toMap(WmExtMenuInfoConfig::getMenuCode, Function.identity(), (o1, o2) -> o1));
    }

    public static Integer getExpiredTime(){
       return Lion.getConfigRepository().getIntValue("waima.todo.expired.time", 2);

    }
}
