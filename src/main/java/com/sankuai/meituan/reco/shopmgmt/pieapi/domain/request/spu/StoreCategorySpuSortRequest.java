package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.FieldSortDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.FieldSort;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.MerchantStoreCategorySpuSequenceUpdateRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.enums.StoreCategorySceneIdentityEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */

@Data
@ApiModel("分类下商品排序请求参数")
public class StoreCategorySpuSortRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "当前分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "当前分类ID", required = true)
    private String categoryId;

    @FieldDoc(
            description = "商品排序map", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品排序map")
    public Map<String, Integer> sequenceMap;

    @FieldDoc(
            description = "排序字段信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "排序字段信息")
    private FieldSortDTO fieldSort;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (StringUtils.isEmpty(this.categoryId)) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }

    public MerchantStoreCategorySpuSequenceUpdateRequest to(User user) {
        MerchantStoreCategorySpuSequenceUpdateRequest request = new MerchantStoreCategorySpuSequenceUpdateRequest();
        request.setTenantId(user.getTenantId());
        request.setNewStoreGroupId(this.storeId);
        request.setCategoryId(Long.valueOf(this.categoryId));
        request.setSequenceMap(this.sequenceMap);
        if(this.fieldSort != null){
            FieldSort sort = new FieldSort();
            sort.setSortType(this.fieldSort.getSortType());
            sort.setField(this.fieldSort.getField());
            sort.setChannelId(this.fieldSort.getChannelId());
            request.setFieldSort(sort);
        }
        request.setSceneIdentity(StoreCategorySceneIdentityEnum.POI.getCode());
        request.setOperatorId(user.getAccountId());
        request.setOperatorName(user.getAccountName());
        return request;
    }

}
