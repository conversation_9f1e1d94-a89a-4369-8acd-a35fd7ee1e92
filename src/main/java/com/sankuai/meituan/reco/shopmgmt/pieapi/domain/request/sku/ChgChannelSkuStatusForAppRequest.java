package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "商品批量上下架请求"
)
@Data
@ApiModel("商品批量上下架请求")
public class ChgChannelSkuStatusForAppRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商品编码列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品编码列表", required = true)
    private List<String> skuList;

    @FieldDoc(
            description = "sku状态 1-上架 2-下架", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "sku状态 1-上架 2-下架", required = true)
    @NotNull
    private Integer skuStatus;

    @FieldDoc(
            description = "渠道列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道列表", required = true)
    @NotNull
    private List<Integer> channelIds;

    @FieldDoc(
            description = "是否自动上架 1-是 0-否", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否自动上架 1-是 0-否")
    private Integer autoOnSale;

    @FieldDoc(
            description = "分类名称列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类名称列表")
    private List<String> storeCategorys;

    @FieldDoc(
            description = "校验是否允许上架 （true-校验，false-不校验）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "校验是否允许上架")
    private boolean checkPermit = false;
}

