package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 创建门店商品并上线
 * @author: liyu44
 * @create: 2020-02-03
 **/
@TypeDoc(
        description = "(创建或更新)门店商品并上线请求"
)
@Data
@ApiModel("(创建或更新)门店商品并上线请求")
public class SaveCdqStoreSkuRequest {

    @FieldDoc(
            description = "上线门店商品信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "上线门店商品信息", required = true)
    @NotNull
    private CdqStoreSkuWithChannelInfoVo cdqStoreSkuWithChannelInfoVo;

    @FieldDoc(
            description = "1-新建，2-更新", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "1-新建，2-更新", required = true)
    @NotNull
    private Integer saveType;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id", required = true)
    @NotNull
    private Long storeId;
    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    @NotNull
    private Long tenantId;
    @FieldDoc(
            description = "是否无限库存配置下提交（仅App使用）"
    )
    @ApiModelProperty(name = "无限库存的标志位")
    private Boolean infiniteInventory;

    @FieldDoc(
            description = "创建门店商品是否新建报价（true-报价，false-不报价）"
    )
    @ApiModelProperty(name = "创建门店商品是否新建报价")
    private boolean quoteForCreate = false;

    @FieldDoc(
            description = "编辑门店商品是否更新门店价格（true-更新价，false-不更新）"
    )
    @ApiModelProperty(name = "编辑门店商品是否更新门店价格")
    private boolean updateStoreSkuPriceForUpdate = false;

    @FieldDoc(
            description = "是否作废待审核报价（true-删除，false-不删除）"
    )
    @ApiModelProperty(name = "是否作废待审核报价")
    private boolean deleteToReviewQuote = false;
}
