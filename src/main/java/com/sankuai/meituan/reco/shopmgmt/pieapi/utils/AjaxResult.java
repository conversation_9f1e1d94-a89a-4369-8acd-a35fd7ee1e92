package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * Author: <PERSON><PERSON><PERSON>of<PERSON>
 * Date: 2017-11-22
 * Time: 下午6:57
 */
public class AjaxResult {

    public static Map<String, Object> createSuccessMap(int code, String msg) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("msg", msg);
        return result;
    }

    public static Map<String, Object> createSuccessMap(int code, String msg, Object data) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("msg", msg);
        result.put("data", data);
        return result;
    }

}
