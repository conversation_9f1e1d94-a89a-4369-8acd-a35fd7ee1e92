package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.config;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-10-31
 */
@TypeDoc(
        description = "门店履约展示配置信息"
)
@ApiModel("门店履约展示配置信息")
@Data
public class StoreFulfillShowConfigVO {
    @FieldDoc(
            description = "待拣货页面展示订单信息：0不展示，1展示", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待拣货页面展示订单信息：0不展示，1展示", required = true)
    public Integer waitPickShowOrderInfo;

    @FieldDoc(
            description = "已拣货页面展示订单信息：0不展示，1展示", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "已拣货页面展示订单信息：0不展示，1展示", required = true)
    public Integer pickedShowOrderInfo;
}
