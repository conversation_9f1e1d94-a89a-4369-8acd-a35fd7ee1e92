package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;
import java.util.List;

/**
 * @author: zhangbo
 * @date: 2020-05-15 17:02
 */
@TypeDoc(
        description = "查询已上架已下架商品数请求"
)
@Data
@ApiModel("查询已上架已下架商品数")
public class QueryOnSaleAndOffSaleCountRequest {

    @FieldDoc(
            description = "搜索关键字", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "搜索关键字", required = true)
    private String keyword;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "渠道Id列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道Id列表", required = true)
    private List<Integer> channelIds;


    @FieldDoc(
            description = "分渠道门店分类信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分渠道门店分类信息")
    private Map<Integer,String> channelStoreCategoryMap;

    @FieldDoc(
            description = "分类状态 1-未分类 2-有分类", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分类状态 1-未分类 2-有分类", required = true)
    private Integer hasStoreCategory;

    @FieldDoc(
            description = "渠道范围 0-指定渠道 1-任一渠道 2- 全部渠道", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道范围 0-指定渠道 1-任一渠道 2- 全部渠道")
    private Integer scope;
}
