package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-01-07 11:24
 * @Description:
 */
@TypeDoc(
        description = "商品价格库存设置请求"
)
@Data
@ApiModel("商品价格库存设置请求")
public class UpdatePriceAndStockForAppRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商品价格库存信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品价格库存信息列表", required = true)
    @NotNull
    private List<SkuPriceAndStock> skuPriceAndStocks;
}
