package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreCategoryDegradeRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreCategoryUpgradeRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/6/23 8:40 下午
 **/
@TypeDoc(
        description = "修改店内分类等级请求",
        authors = {"zhouyan32"}
)
@Data
@ApiModel("修改店内分类等级请求")
public class ModifyFrontCategoryLevelRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId = 100;
    @FieldDoc(
            description = "分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分类ID", required = true)
    private Long categoryId;
    @FieldDoc(
            description = "层级，1一级分类，2二级分类", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "层级，1一级分类，2二级分类", required = true)
    private int level;

    @FieldDoc(
            description = "父分类Id,层级为2时必填", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "父分类Id,层级为2时必填")
    private String parentCode;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.channelId == null) {
            throw new CommonLogicException("渠道id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.categoryId == null) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (level == 2 && StringUtils.isBlank(parentCode)) {
            throw new CommonLogicException("降级操作必须传入父类id", ResultCode.CHECK_PARAM_ERR);
        }
    }

    public ChannelStoreCategoryUpgradeRequest toUpgrade(User user) {
        ChannelStoreCategoryUpgradeRequest channelStoreCategoryUpgradeRequest = new ChannelStoreCategoryUpgradeRequest();
        channelStoreCategoryUpgradeRequest.setTenantId(user.getTenantId());
        channelStoreCategoryUpgradeRequest.setStoreId(this.storeId);
        channelStoreCategoryUpgradeRequest.setChannelId(this.channelId);
        channelStoreCategoryUpgradeRequest.setCategoryId(this.categoryId);
        channelStoreCategoryUpgradeRequest.setOperatorId(user.getAccountId());
        channelStoreCategoryUpgradeRequest.setOperator(user.getOperatorName());

        return channelStoreCategoryUpgradeRequest;
    }

    public ChannelStoreCategoryDegradeRequest toDegrade(User user) {
        ChannelStoreCategoryDegradeRequest channelStoreCategoryDegradeRequest = new ChannelStoreCategoryDegradeRequest();
        channelStoreCategoryDegradeRequest.setTenantId(user.getTenantId());
        channelStoreCategoryDegradeRequest.setStoreId(this.storeId);
        channelStoreCategoryDegradeRequest.setChannelId(this.channelId);
        channelStoreCategoryDegradeRequest.setCategoryId(this.categoryId);
        channelStoreCategoryDegradeRequest.setParentCategoryId(Long.parseLong(this.parentCode));
        channelStoreCategoryDegradeRequest.setOperatorId(user.getAccountId());
        channelStoreCategoryDegradeRequest.setOperator(user.getOperatorName());
        return channelStoreCategoryDegradeRequest;

    }
}
