package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import java.util.List;

import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSaleAttributeValueDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SaleAttributeValueDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SkuChannelSaleAttributeValueDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SkuSaleAttributeValueDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-12-26
 */
@TypeDoc(
        description = "渠道销售属性值"
)
@Data
@ApiModel("渠道销售属性值")
public class ChannelSaleAttrValueInfoVO {
    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID")
    private int channelId;

    @FieldDoc(
            description = "销售属性值", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "销售属性值")
    private List<SaleAttrValueInfoVO> saleAttrValueInfoList;

    public static ChannelSaleAttrValueInfoVO of(ChannelSaleAttributeValueDTO channelSaleAttributeValueDTO) {
        ChannelSaleAttrValueInfoVO channelSaleAttrValueInfoVO = new ChannelSaleAttrValueInfoVO();
        channelSaleAttrValueInfoVO.setChannelId(channelSaleAttributeValueDTO.getChannelId());
        channelSaleAttrValueInfoVO.setSaleAttrValueInfoList(Fun.map(channelSaleAttributeValueDTO.getSaleAttributeValues(), SaleAttrValueInfoVO::ofDTO));
        return channelSaleAttrValueInfoVO;
    }

    public static ChannelSaleAttrValueInfoVO of(SkuChannelSaleAttributeValueDTO skuChannelSaleAttributeValueDTO) {
        ChannelSaleAttrValueInfoVO channelSaleAttrValueInfoVO = new ChannelSaleAttrValueInfoVO();
        channelSaleAttrValueInfoVO.setChannelId(skuChannelSaleAttributeValueDTO.getChannelId());
        channelSaleAttrValueInfoVO.setSaleAttrValueInfoList(Fun.map(skuChannelSaleAttributeValueDTO.getSkuSaleAttributeValues(), SaleAttrValueInfoVO::ofBizDTO));
        return channelSaleAttrValueInfoVO;
    }

    public SkuChannelSaleAttributeValueDTO toBizDTO(SkuImageInfo skuImageInfo) {
        SkuChannelSaleAttributeValueDTO dto = new SkuChannelSaleAttributeValueDTO();
        dto.setChannelId(channelId);
        dto.setSkuSaleAttributeValues(Fun.map(saleAttrValueInfoList, SaleAttrValueInfoVO::toBizDTO));
        if (skuImageInfo == null) {
            return dto;
        }
        if (channelId == EnhanceChannelType.JDDJ.getChannelId()) {
            dto.setSkuImageUrls(skuImageInfo.getJdImageUrls());
        }
        else {
            dto.getSkuSaleAttributeValues().stream()
                    .filter(SkuSaleAttributeValueDTO::isImageRelated)
                    .findAny()
                    .ifPresent(skuSaleAttributeValueDTO -> skuSaleAttributeValueDTO.setRelatedImageUrls(skuImageInfo.getImageUrls()));
        }
        return dto;
    }

    public ChannelSaleAttributeValueDTO toOcmsDTO(SkuImageInfo skuImageInfo) {
        ChannelSaleAttributeValueDTO dto = new ChannelSaleAttributeValueDTO();
        dto.setChannelId(channelId);
        dto.setSaleAttributeValues(Fun.map(saleAttrValueInfoList, SaleAttrValueInfoVO::toDTO));
        if (skuImageInfo == null) {
            return dto;
        }
        if (channelId == EnhanceChannelType.JDDJ.getChannelId()) {
            dto.setSkuImageUrls(skuImageInfo.getJdImageUrls());
        }
        else {
            dto.getSaleAttributeValues().stream()
                    .filter(SaleAttributeValueDTO::isImageRelated)
                    .findAny()
                    .ifPresent(saleAttributeValueDTO -> saleAttributeValueDTO.setRelatedImageUrls(skuImageInfo.getImageUrls()));
        }
        return dto;
    }
}
