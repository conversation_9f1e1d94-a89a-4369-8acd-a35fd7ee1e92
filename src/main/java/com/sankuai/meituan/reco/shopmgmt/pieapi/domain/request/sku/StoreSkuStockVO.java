package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: zhangbo
 * @date: 2020-05-15 17:03
 */
@TypeDoc(
        description = "查询已上架已下架商品数响应"
)
@Data
@ApiModel("设置库存信息")
public class StoreSkuStockVO {
    @FieldDoc(
            description = "SKU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SKU编码", required = true)
    @NotNull
    private String skuId;

    @FieldDoc(
            description = "库存", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "库存", required = true)
    @NotNull
    private Long stock;

    @FieldDoc(
            description = "自定义库存标记", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "自定义库存标记", required = true)
    private Integer customizeStockFlag;

    @FieldDoc(
            description = "第二天是否自动恢复无限库存", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "第二天是否自动恢复无限库存", required = true)
    private Integer autoResumeInfiniteStock;
}
