package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/3/31 2:28 下午
 **/
@TypeDoc(
        name = "不一致商品问题列表和商品信息",
        description = "不一致商品问题列表和商品信息"
)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class SpuProblemListRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;


    @FieldDoc(
            description = "门店spuId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店spuId", required = true)
    private String spuId;

    public void validate() {
        Preconditions.checkArgument(Objects.nonNull(storeId), "门店id不能为空");
        Preconditions.checkArgument(StringUtils.isNotEmpty(spuId), "商品spuId不能为空");
    }

    public StoreSpuDetailApiRequest toDetail() {
        StoreSpuDetailApiRequest request = new StoreSpuDetailApiRequest();
        request.setStoreId(this.storeId);
        request.setSpuId(this.spuId);
        return request;
    }
}
