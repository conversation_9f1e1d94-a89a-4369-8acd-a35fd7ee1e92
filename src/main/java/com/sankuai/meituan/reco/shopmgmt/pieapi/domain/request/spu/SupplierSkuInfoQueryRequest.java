package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@TypeDoc(
        description = "供货商规格查询请求"
)
@Data
public class SupplierSkuInfoQueryRequest {

    @FieldDoc(
            description = "供货商规格ID"
    )
    @ApiModelProperty(value = "供货商规格ID", required = true)
    private List<String> supplierSkuIds;

    @FieldDoc(
            description = "加盟区域ID列表"
    )
    @ApiModelProperty(value = "文件url", required = true)
    private List<Integer> joinAreaIds;

    public void selfCheck() {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(supplierSkuIds), "供货商规格ID不能为空");
    }
}
