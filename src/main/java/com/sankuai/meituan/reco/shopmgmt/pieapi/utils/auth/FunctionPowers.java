package com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth;

public enum FunctionPowers {
	ORDER_GOODS(10),          // 要货
	RECEIVING_GOODS(20),      // 收货
	RETURN_GOODS(30),         // 退货
	BREAKAGE_REPORT(40),      // 报损
	ADJUST_PRICE(50),         // 调价
	CHECK_GOODS(60),          // 盘点
	QUERY_GOODS(70),          // 查询商品
	ORDER(80)                 // 订单
	;

	private final int code;

	FunctionPowers(int code) {
		this.code = code;
	}

	public int code() {
		return code;
	}
}
