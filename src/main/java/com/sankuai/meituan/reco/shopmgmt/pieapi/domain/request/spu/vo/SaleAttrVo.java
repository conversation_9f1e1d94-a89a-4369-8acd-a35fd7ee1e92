package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SaleAttributeDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuSaleAttributeDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelSaleAttrDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/9/6
 * 销售属性
 **/
@TypeDoc(
        description = "销售属性"
)
@Data
@ApiModel("销售属性")
public class SaleAttrVo {

    @FieldDoc(
            description = "属性ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "属性ID")
    @NotNull
    private String attrId;

    @FieldDoc(
            description = "属性名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "属性名称")
    @NotNull
    private String attrName;

    @FieldDoc(
            description = "是否关联图片"
    )
    @ApiModelProperty("是否关联图片")
    private boolean imageRelated;

    public static SpuSaleAttributeDTO convertBizDTO (SaleAttrVo saleAttrInfoVO){

        SpuSaleAttributeDTO saleAttributeDTO = new SpuSaleAttributeDTO();
        Optional.ofNullable(saleAttrInfoVO.getAttrId()).ifPresent(id -> saleAttributeDTO.setAttrId(Long.valueOf(id)));
        saleAttributeDTO.setAttrName(saleAttrInfoVO.getAttrName());
        saleAttributeDTO.setImageRelated(saleAttrInfoVO.isImageRelated());
        return saleAttributeDTO;
    }

    public static SaleAttrVo fromDTO (SaleAttributeDTO saleAttributeDTO){

        SaleAttrVo saleAttrInfoVO = new SaleAttrVo();
        Optional.ofNullable(saleAttributeDTO.getAttrId()).ifPresent(id -> saleAttrInfoVO.setAttrId(id.toString()));
        saleAttrInfoVO.setAttrName(saleAttributeDTO.getAttrName());
        saleAttrInfoVO.setImageRelated(saleAttributeDTO.isImageRelated());
        return saleAttrInfoVO;
    }

    public static SaleAttrVo fromDTO(SpuSaleAttributeDTO saleAttributeDTO){

        SaleAttrVo saleAttrInfoVO = new SaleAttrVo();
        Optional.ofNullable(saleAttributeDTO.getAttrId()).ifPresent(id -> saleAttrInfoVO.setAttrId(id.toString()));
        saleAttrInfoVO.setAttrName(saleAttributeDTO.getAttrName());
        saleAttrInfoVO.setImageRelated(saleAttributeDTO.isImageRelated());
        return saleAttrInfoVO;
    }

    public static SaleAttrVo ofDTO (ChannelSaleAttrDTO saleAttributeDTO){
        SaleAttrVo saleAttrInfoVO = new SaleAttrVo();
        saleAttrInfoVO.setAttrId(saleAttributeDTO.getAttrId());
        saleAttrInfoVO.setAttrName(saleAttributeDTO.getAttrName());
        return saleAttrInfoVO;
    }

    public SaleAttributeDTO toOcmsDTO() {
        SaleAttributeDTO saleAttributeDTO = new SaleAttributeDTO();
        saleAttributeDTO.setAttrId(Optional.ofNullable(attrId).map(Long::valueOf).orElse(null));
        saleAttributeDTO.setAttrName(attrName);
        saleAttributeDTO.setImageRelated(imageRelated);
        return saleAttributeDTO;
    }

    public SpuSaleAttributeDTO toBizDTO() {
        SpuSaleAttributeDTO spuSaleAttributeDTO = new SpuSaleAttributeDTO();
        spuSaleAttributeDTO.setAttrId(Optional.ofNullable(attrId).map(Long::valueOf).orElse(null));
        spuSaleAttributeDTO.setAttrName(attrName);
        spuSaleAttributeDTO.setImageRelated(imageRelated);
        return spuSaleAttributeDTO;
    }
}
