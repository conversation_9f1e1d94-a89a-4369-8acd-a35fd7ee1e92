package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: <EMAIL>
 * @Date: 2020-12-01 11:13
 * @Description:
 */
@TypeDoc(
        description = "根据SPUID和租户及重量查询相似的spu请求",
        authors = {"liuxun05"}
)
@Data
@ApiModel("根据SPUID和重量查询租户商品信息，按照覆盖门店数倒序排列")
public class FindTenantSimilarSkuRequest {

    @FieldDoc(
            description = "商品spu编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品spu编码")
    @NotNull
    private String spuId;

    @FieldDoc(
            description = "相似规格需要重量相同", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "重量")
    @NotNull
    private String maxWeight;

    @FieldDoc(
            description = "返回条数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "条数")
    @NotNull
    private Integer size;

}
