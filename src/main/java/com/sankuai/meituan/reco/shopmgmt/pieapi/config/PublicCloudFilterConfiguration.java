package com.sankuai.meituan.reco.shopmgmt.pieapi.config;

import com.dianping.cat.servlet.CatFilter;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.stereotypes.publiccloud.PublicCloudConfig;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;

/**
 * 通过此类来配置Filter
 * Author: yangshaosong
 * Date: 2018-01-02
 * Time: 下午12:37
 */
@PublicCloudConfig
public class PublicCloudFilterConfiguration {

    private static final int CAT_FILTER_ORDER = 1;

    @Bean
    public FilterRegistrationBean catFilter() {
        CatFilter filter = new CatFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/*");
        registration.setName("cat-filter");
        registration.setOrder(CAT_FILTER_ORDER);
        return registration;
    }

}
