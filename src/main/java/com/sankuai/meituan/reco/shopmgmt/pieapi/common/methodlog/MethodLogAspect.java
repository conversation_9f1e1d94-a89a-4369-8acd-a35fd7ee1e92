package com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog;

import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018/3/22
 */
@Aspect
@Component
@Order(1)
public class MethodLogAspect {

    private static final Logger LOGGER = LoggerFactory.getLogger(MethodLogAspect .class);

    private Map<String, Logger> loggerMap = new ConcurrentHashMap<>();

    @Around(value = "@annotation(com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog)")
    public Object aroundInvoke(ProceedingJoinPoint joinPoint) throws Throwable {

        String className = joinPoint.getTarget().getClass().getSimpleName();

        final String methodName = joinPoint.getSignature().getName();
        final MethodSignature methodSignature = (MethodSignature) joinPoint
                .getSignature();
        Method method = methodSignature.getMethod();
        MethodLog annotation = AnnotationUtils.findAnnotation(method, MethodLog.class);
        if (annotation == null) {
            return joinPoint.proceed();
        } else {

            String paramText = buildParamText(joinPoint, method);
            if (annotation.logRequest()) {
                getLogger(annotation).info("start monitor method:{} , class: {}, param: {}", methodName, className, paramText);
            }
            try {
                Long start = System.currentTimeMillis();
                Object result = joinPoint.proceed();
                if (annotation.logResponse()) {
                    getLogger(annotation).info("end monitor proceed , class:{}, method {}, cost {}, param {}, result {}", className, methodName, (System.currentTimeMillis() - start),paramText, JacksonUtils.toJson(result));
                }
                return result;
            } catch (Exception ex) {
                if (annotation.logException()) {
                    getLogger(annotation).error("end monitor proceed exception,class:{},method {} ,param {}, exception= {}", className, methodName, paramText, ex);
                }
                throw ex;
            }
        }

    }

    private Logger getLogger(MethodLog methodLog) {
        if (methodLog == null || StringUtils.isEmpty(methodLog.logger())) {
            return LOGGER;
        } else {
            String loggerName = methodLog.logger();
            if (!loggerMap.containsKey(loggerName)) {
                Logger logger = LoggerFactory.getLogger(loggerName);
                loggerMap.putIfAbsent(loggerName, logger);
            }
            return loggerMap.get(loggerName);
        }
    }

    private String buildParamText(ProceedingJoinPoint joinPoint, Method method) {

        Object[] joinPointArgs = joinPoint.getArgs();
        if (joinPointArgs == null || joinPointArgs.length == 0) {
            return "empty";
        }

        String[] parameterNames = new DefaultParameterNameDiscoverer().getParameterNames(method);

        StringBuilder builder = new StringBuilder();
        int i = 0;
        for (Object arg : joinPointArgs) {

            builder.append(parameterNames[i++]);
            builder.append(":");
            if (arg instanceof Number) {
                builder.append(arg);
                builder.append(",");
                continue;
            }
            String argString = JacksonUtils.toJson(arg);
            if (argString == null) {
                builder.append("null");
            /*} else if (argString.length() > 500) {
                builder.append(argString.substring(0, 250));
                builder.append("...");
                builder.append(argString.substring(argString.length() - 250, argString.length()));
            } else {*/
            } else {
                builder.append(argString);
            }
            builder.append(",");

        }
        return builder.toString();

    }
}
