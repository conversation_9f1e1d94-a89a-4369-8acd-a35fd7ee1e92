package com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login;

import java.util.concurrent.Callable;

public abstract class IdentityInfoRunable implements Runnable {

    private IdentityInfo identityInfo;



    public IdentityInfoRunable(IdentityInfo identityInfo) {
        this.identityInfo = identityInfo;
    }

    public IdentityInfoRunable() {
        this.identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
    }

    @Override
    public void run() {
        ApiMethodParamThreadLocal.getInstance().set(this.identityInfo);
        doRun();
    }

    protected abstract void doRun();

}
