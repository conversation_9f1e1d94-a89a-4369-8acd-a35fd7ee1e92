package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryMedicalStandardProductRequest;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 查询医药标品库商品请求
 *
 * <AUTHOR>
 * @since 2025/02/11
 */
@Getter
@Setter
@ToString
public class MedicalStandardSpuQueryRequest {
    @FieldDoc(
            description = "条码列表"
    )
    @ApiModelProperty(value = "条码列表")
    private List<String> upcs;

    public QueryMedicalStandardProductRequest convertToBizRequest(User user) {
        QueryMedicalStandardProductRequest bizReq = new QueryMedicalStandardProductRequest();
        bizReq.setTenantId(user.getTenantId());
        bizReq.setUpcList(this.getUpcs());
        return bizReq;
    }
}
