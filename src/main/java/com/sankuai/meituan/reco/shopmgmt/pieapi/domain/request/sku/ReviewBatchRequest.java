package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.BatchReviewRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ReviewClientTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.review.BatchReviewTenantProductRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description: 批量审核请求
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-03-11
 **/
@TypeDoc(
        description = "批量审核请求"
)
@Data
@ApiModel("批量审核请求")
public class ReviewBatchRequest {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "审核业务记录id列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审核业务记录id列表", required = true)
    @NotNull
    private List<Long> reviewBizIds;

    @FieldDoc(
            description = "审核类型：1-报价审核 2-商品提报审核", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审核类型：1-报价审核 2-商品提报审核", required = true)
    @NotNull
    private Integer reviewBizType;

    @FieldDoc(
            description = "审核操作：1-通过 2-驳回", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审核操作：1-通过 2-驳回", required = true)
    @NotNull
    private Integer reviewOperate;

    @FieldDoc(
            description = "驳回原因", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "驳回原因")
    private String rejectReason;

    @FieldDoc(
            description = "审核人id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审核人id", required = true)
    @NotNull
    private Long reviewerId;

    @FieldDoc(
            description = "审核人用户名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审核人用户名", required = true)
    @NotNull
    private String reviewerName;

    @FieldDoc(
            description = "审核客户端类型(1-WEB端审核;2-APP端审核)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审核客户端类型(1-WEB端审核;2-APP端审核)", required = true)
    @NotNull
    private Integer reviewClientType;

    @FieldDoc(
            description = "审核备注，门店提报审核使用", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "审核备注，门店提报审核使用")
    private String reviewRemark;

    public BatchReviewRequest convertBatchReviewRequest(){

        BatchReviewRequest batchReviewRequest = new BatchReviewRequest();
        batchReviewRequest.setTenantId(this.getTenantId());
        batchReviewRequest.setReviewBizIds(this.getReviewBizIds());
        batchReviewRequest.setReviewBizType(this.getReviewBizType());
        batchReviewRequest.setReviewOperate(this.getReviewOperate());
        batchReviewRequest.setRejectReason(this.getRejectReason());
        batchReviewRequest.setReviewerId(this.getReviewerId());
        batchReviewRequest.setReviewerName(this.getReviewerName());
        batchReviewRequest.setReviewClientType(ReviewClientTypeEnum.findByValue(reviewClientType));
        return batchReviewRequest;

    }

    public BatchReviewTenantProductRequest convertTenantBatchReviewRequest(User user) {
        BatchReviewTenantProductRequest batchReviewRequest = new BatchReviewTenantProductRequest();
        batchReviewRequest.setTenantId(user.getTenantId());
        batchReviewRequest.setReviewIds(this.getReviewBizIds());
        batchReviewRequest.setReviewOperate(this.getReviewOperate());
        batchReviewRequest.setRejectReason(this.getRejectReason());
        // 注意：提报历史逻辑的审核人使用的employeeId,但提报人使用的accountId
        batchReviewRequest.setReviewerId(user.getEmployeeId());
        batchReviewRequest.setReviewerName(user.getOperatorName());
        batchReviewRequest.setReviewClientType(this.getReviewClientType());
        batchReviewRequest.setReviewRemark(this.getReviewRemark());
        return batchReviewRequest;
    }


}
