package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

/**
 * @Author: wangyihao04
 * @Date: 2021-12-23 20:29
 * @Mail: <EMAIL>
 */
public enum  TenantPriceType {
    MANUAL(1, "手动定价"),
    COMMON(2, "通用提价");
    private int code;
    private String desc;

    TenantPriceType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static TenantPriceType findByCode(Integer code){
        for (TenantPriceType it : values()){
            if (it.getCode() == code){
                return it;
            }
        }
        return null;
    }
}
