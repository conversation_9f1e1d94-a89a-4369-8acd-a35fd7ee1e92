package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2020-03-25 15:03
 * @Description:
 */
@TypeDoc(
        description = "退差价联动摊位营收(提报价模式)"
)
@ApiModel("退差价联动摊位营收(提报价模式)")
@Data
public class WeightRefundRevenueConfig {
    @FieldDoc(
            description = "退差价联动摊位营收,0-否,1-是,null表示不设置该值", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退差价联动摊位营收,0-否,1-是,null表示不设置该值", required = true)
    private Integer isLinked;
}
