package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import org.apache.commons.lang3.StringUtils;

public final class DaySeqNumUtil {
    private DaySeqNumUtil() {
    }

    /**
     * 返回订单流水号
     */
    public static String getDaySeqNum(Integer oldValue, String newValue) {
        if (MccConfigUtil.getDaySeqNumSwitch() && StringUtils.isNotEmpty(newValue)) {
            return newValue;
        }
        return oldValue == null ? null : String.valueOf(oldValue);
    }

    public static String getDaySeqNum(Long oldValue, String newValue) {
        return getDaySeqNum(oldValue == null ? null : oldValue.intValue(), newValue);
    }
}