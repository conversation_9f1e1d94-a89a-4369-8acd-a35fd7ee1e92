package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.platform.empower.product.client.enums.StoreCategorySceneIdentityEnum;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category.UpdateMerchantStoreCategoryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */

@Data
@ApiModel("店内分类置顶请求")
public class StoreCategoryTopRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分类ID", required = true)
    private String categoryId;

    @FieldDoc(
            description = "限时置顶开关", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "限时置顶开关", required = true)
    private Boolean topFlag;

    @FieldDoc(
            description = "置顶周期：1,2,3,4,5,6,7分别表示周一至周日", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "置顶周期：1,2,3,4,5,6,7分别表示周一至周日")
    private String weeksTime;

    @FieldDoc(
            description = "置顶时段：00:00-09:00，10:00-11:00", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "置顶时段：00:00-09:00，10:00-11:00")
    private List<String> period;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.categoryId == null) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.topFlag == null) {
            throw new CommonLogicException("置顶开关不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.topFlag) {
            if (this.weeksTime == null) {
                throw new CommonLogicException("置顶周期不能为空", ResultCode.CHECK_PARAM_ERR);
            }
            if (this.period == null) {
                throw new CommonLogicException("置顶周期时段不能为空", ResultCode.CHECK_PARAM_ERR);
            }
        }
    }

    public UpdateMerchantStoreCategoryRequest toPoiRpcReq(User user) {
        UpdateMerchantStoreCategoryRequest storeCategoryTopRequest = new UpdateMerchantStoreCategoryRequest();
        storeCategoryTopRequest.setMerchantId(user.getTenantId());
        storeCategoryTopRequest.setNewStoreGroupId(this.storeId);
        storeCategoryTopRequest.setUpdateType(6);
        storeCategoryTopRequest.setSceneIdentity(StoreCategorySceneIdentityEnum.POI.getCode());
        storeCategoryTopRequest.setCategoryId(Long.valueOf(this.categoryId));
        storeCategoryTopRequest.setTopFlag(this.topFlag);
        storeCategoryTopRequest.setPeriod(this.period);
        storeCategoryTopRequest.setWeeksTime(this.weeksTime);
        storeCategoryTopRequest.setOperatorId(user.getAccountId());
        storeCategoryTopRequest.setOperatorName(user.getOperatorName());
        return storeCategoryTopRequest;
    }
}
