package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;

/**
 * 基于Jackson 2.x版本。
 * 1.x版本不支持泛型序列
 *
 * <AUTHOR>
 * @date 2021-09-28 16:15
 */
@Slf4j
public class JacksonUtilsV2 {
    /**
     * 对象映射
     */
    private static final ObjectMapper objMapper = new ObjectMapper();

    static {
        objMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        objMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    public static <T> T fromJson(String json, TypeReference<T> typeRef) {
        try {
            return objMapper.readValue(json, typeRef);
        } catch (Exception e) {
            log.error("Json串转换成对象出错：{}", json);
            throw new RuntimeException("Json串转换成对象出错!", e);
        }
    }
}
