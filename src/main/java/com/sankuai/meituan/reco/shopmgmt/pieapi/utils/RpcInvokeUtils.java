package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.dianping.lion.common.util.JsonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2023/8/28 11:26
 **/
@Slf4j
public class RpcInvokeUtils {

    public static <T>  T rpcInvokeTemplate(Supplier<T> rpcMethod, String errorMsg,
                                           Function<T, Integer> parseCodeMethod,
                                           Function<T, String> parseMsgMethod) {
        try {
            T response = rpcMethod.get();
            log.info("end invoke rpc method, response: {}", JsonUtils.toJson(response));
            checkResponse(response, errorMsg, parseCodeMethod, parseMsgMethod);
            return response;
        } catch (Exception e) {
            log.error("远程调用失败, error: ", e);
            throw new BizException(errorMsg);
        }
    }

    public static <T> void checkResponse(T response, String errorMsg, Function<T, Integer> parseCodeMethod, Function<T, String> parseMsgMethod) {
        if (response == null) {
            log.error(errorMsg + ", "+ "response: {}", JsonUtils.toJson(response));
            throw new CommonRuntimeException(errorMsg);
        }

        Integer code = null;
        String msg = null;
        try {
            code = parseCodeMethod.apply(response);
            msg = parseMsgMethod.apply(response);
        } catch (Exception e) {
            log.error("解析code和msg失败, response: {}", JsonUtils.toJson(response), e);
            throw new CommonRuntimeException("解析code和msg失败");
        }

        if (!Objects.equals(code, 0)) {
            if (StringUtils.isNotBlank(msg)) {
                log.error(errorMsg + ", response: {}", JsonUtils.toJson(response));
                throw new BizException(errorMsg + ": " + msg);
            } else {
                log.error(errorMsg + ", response: {}", JsonUtils.toJson(response));
                throw new BizException(errorMsg);
            }
        }
    }
}
