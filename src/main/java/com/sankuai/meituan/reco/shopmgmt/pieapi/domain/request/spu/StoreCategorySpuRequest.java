package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.MerchantStoreCategorySpuQueryRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.enums.StoreCategorySceneIdentityEnum;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category.DeleteMerchantStoreCategoryRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category.PoiStoreCategoryQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */

@Data
@ApiModel("店内分类商品分页查询请求")
public class StoreCategorySpuRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分类ID", required = true)
    private String categoryId;

    @FieldDoc(
            description = "页数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "页数", required = true)
    private Integer page;

    @FieldDoc(
            description = "分页大小", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页大小", required = true)
    private Integer pageSize;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.categoryId == null) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.page == null) {
            throw new CommonLogicException("查询页数不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.pageSize == null) {
            throw new CommonLogicException("分页大小不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }

    public MerchantStoreCategorySpuQueryRequest to(User user) {
        MerchantStoreCategorySpuQueryRequest rpcReq = new MerchantStoreCategorySpuQueryRequest();
        rpcReq.setTenantId(user.getTenantId());
        rpcReq.setStoreGroupId(this.storeId);
        rpcReq.setCategoryId(Long.valueOf(this.categoryId));
        rpcReq.setPage(this.page);
        rpcReq.setPageSize(this.pageSize);
        return rpcReq;
    }

}
