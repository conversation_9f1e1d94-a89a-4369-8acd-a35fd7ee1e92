package com.sankuai.meituan.reco.shopmgmt.pieapi.config;

import com.meituan.shangou.saas.order.management.client.utils.BusinessIdTracer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@Aspect
@Component
@Slf4j
public class ControllerBusinessIdAspect {



    @Around("execution(* com.sankuai.meituan.reco.shopmgmt.pieapi.controller..*.*(..))")
    public Object aroundRiderController(ProceedingJoinPoint pjp) throws Throwable {
        putTraceTenantId(pjp);
        return pjp.proceed();
    }

    private void putTraceTenantId(ProceedingJoinPoint pjp) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        User user = identityInfo.getUser();
        if (user != null && user.getTenantId() > 0){
            BusinessIdTracer.putTenantId(user.getTenantId());
        }
    }


}