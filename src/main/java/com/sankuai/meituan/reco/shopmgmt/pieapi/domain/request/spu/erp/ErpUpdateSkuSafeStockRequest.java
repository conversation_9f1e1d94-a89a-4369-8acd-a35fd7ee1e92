package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp;

import org.apache.commons.lang3.StringUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.UpdateSkuSafeStockRequest;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.enums.ProductOperateSourceEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * ERP门店商品修改SKU安全库存请求参数
 *
 * <AUTHOR>
 * @since 2024/09/29
 */
@TypeDoc(
        description = "ERP门店商品修改SKU安全库存请求参数"
)
@ApiModel("ERP门店商品修改SKU安全库存请求参数")
@Getter
@Setter
@ToString
public class ErpUpdateSkuSafeStockRequest {

    @FieldDoc(description = "门店ID", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(name = "门店ID")
    private Long storeId;

    @FieldDoc(description = "SKU主键", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "SKU主键")
    private String skuId;

    @FieldDoc(description = "安全库存", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "安全库存")
    private Integer onlineSafeStock;


    public void validate() {
        if (this.storeId == null) {
            throw new ParamException("门店ID不能为空");
        }
        if (StringUtils.isBlank(skuId)) {
            throw new ParamException("规格不能为空");
        }
    }

    public UpdateSkuSafeStockRequest toUpdateSkuSafeStockRequest(User user) {
        UpdateSkuSafeStockRequest updateSkuSafeStockRequest = new UpdateSkuSafeStockRequest();
        updateSkuSafeStockRequest.setTenantId(user.getTenantId());
        updateSkuSafeStockRequest.setStoreId(getStoreId());

        updateSkuSafeStockRequest.setOperatorAccount(user.getAccountName());
        updateSkuSafeStockRequest.setOperatorId(user.getAccountId());
        updateSkuSafeStockRequest.setOperatorName(user.getOperatorName());

        updateSkuSafeStockRequest.setSource(ProductOperateSourceEnum.APP.getCode());

        return updateSkuSafeStockRequest;
    }
}
