package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/30
 */
@TypeDoc(
        description = "税率查询结果VO"
)
@Data
public class TaxRateOptionQueryVO {

    @FieldDoc(
            description = "税率选项列表"
    )
    private List<Integer> taxRateOptionList;

}
