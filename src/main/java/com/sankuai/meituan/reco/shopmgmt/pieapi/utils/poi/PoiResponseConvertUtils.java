package com.sankuai.meituan.reco.shopmgmt.pieapi.utils.poi;

import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.CityWithPoiListVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.PoiInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.area.bo.AreaBO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.poi.bo.PoiInfoBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/9/7 8:34 下午
 * Description
 */
@Slf4j
public class PoiResponseConvertUtils {

    public static List<CityWithPoiListVO> constructCityPoiList(List<PoiInfoBO> poiInfoBOs) {
        List<CityWithPoiListVO> cityWithPoiLists = new ArrayList<>();
        List<AreaBO> areaList = convertList(poiInfoBOs, PoiInfoBO::getAreaInfo).stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areaList)) {
            return cityWithPoiLists;
        }
        areaList = areaList.stream().distinct().collect(Collectors.toList());
        Map<String, AreaBO> cityMaps = areaList.stream().collect(Collectors.toMap(AreaBO::getCityCode
                , v -> v, (oValue, nValue) -> nValue));
        Map<String, List<PoiInfoVO>> poiInfoVOMap = new HashMap<>();
        for (PoiInfoBO poiInfoBO : poiInfoBOs) {
            if (poiInfoBO.getAreaInfo() != null && StringUtils.isNotBlank(poiInfoBO.getAreaInfo().getCityCode())
                    && StringUtils.isNotBlank(poiInfoBO.getAreaInfo().getCityName())) {
                if (poiInfoVOMap.get(poiInfoBO.getAreaInfo().getCityCode()) != null) {
                    List<PoiInfoVO> poiInfoVOList = poiInfoVOMap.get(poiInfoBO.getAreaInfo().getCityCode());
                    poiInfoVOList.add(poiInfoBO.toPoiResp());
                    poiInfoVOMap.put(poiInfoBO.getAreaInfo().getCityCode(), poiInfoVOList);
                } else {
                    List<PoiInfoVO> poiInfoVOList = new ArrayList<>();
                    poiInfoVOList.add(poiInfoBO.toPoiResp());
                    poiInfoVOMap.put(poiInfoBO.getAreaInfo().getCityCode(), poiInfoVOList);
                }
            }
        }

        for (Map.Entry<String, AreaBO> entry : cityMaps.entrySet()) {
            String cityCode = entry.getKey();
            AreaBO areaBO = entry.getValue();
            if (poiInfoVOMap.get(cityCode) != null) {
                CityWithPoiListVO cityWithPoiListVO = new CityWithPoiListVO();
                cityWithPoiListVO.setCityCode(areaBO.getCityCode());
                cityWithPoiListVO.setCityName(areaBO.getCityName());
                cityWithPoiListVO.setPoiList(poiInfoVOMap.get(areaBO.getCityCode()));
                cityWithPoiLists.add(cityWithPoiListVO);
            }
        }
        return cityWithPoiLists;
    }

    /**
     * List类型转换
     * @param srcList
     * @param mapFunction
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F,T> List<T> convertList(List<F> srcList, Function<F,T> mapFunction) {
        return Optional.ofNullable(srcList).map(List::stream).orElse(Stream.empty())
                .filter(Objects::nonNull).map(mapFunction).collect(Collectors.toList());
    }

}
