package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ERP门店商品批量设置接口请求参数
 *
 * <AUTHOR>
 * @since 2023/05/12
 */
@TypeDoc(
        description = "ERP门店商品批量设置接口请求参数"
)
@Data
@ApiModel("ERP门店商品批量设置接口请求参数")
public class ErpStoreSpuBatchUpdateRequest {
    
    @FieldDoc(description = "门店SPU参数", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(name = "门店SPU参数")
    private List<StoreSpuUpdateParamVO> storeSpuList;

    @FieldDoc(description = "上下线状态 1-上线 2-下线 3-手动下线", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "上下线状态 1-上线 2-下线 3-手动下线")
    private Integer onlineStatus;

    @FieldDoc(description = "上下架状态 1-上架 2-下架 3-手动下架", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "上下架状态 1-上架 2-下架 3-手动下架")
    private Integer saleStatus;

    @FieldDoc(description = "价格状态 1-接 -1-停", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "价格状态 1-接 -1-停")
    private Integer priceStatus;

    @FieldDoc(description = "库存状态 1-接 -1-停", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "库存状态 1-接 -1-停")
    private Integer stockStatus;

    @FieldDoc(description = "库存", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "库存")
    private String stock;

    @FieldDoc(description = "售价", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "售价")
    private String salePrice;

    @FieldDoc(description = "原价", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "原价")
    private String originPrice;

    @FieldDoc(
            description = "操作类型：" +
                    "1：（批量）修改上下线" +
                    "2：（批量）修改上下架" +
                    "3：（批量）修改接停价格" +
                    "4：（批量）修改接停库存" +
                    "5：（批量）修改售价" +
                    "6：（批量）修改原价" +
                    "7：（批量）修改库存" +
                    "8：（批量）修改价格状态 + 修改售价/原价" +
                    "9：（批量）修改库存状态 + 修改库存"
            , requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "操作类型")
    private String opType;

    @FieldDoc(
            description = "商品参与活动时的处理方式，1-保留活动，更新失败；2-下线活动，更新成功", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品参与活动时的处理方式")
    private Integer actCheckType;

    @TypeDoc(
            description = "门店SPU参数"
    )
    @Data
    @ApiModel("门店SPU参数")
    public static final class StoreSpuUpdateParamVO {
        @FieldDoc(description = "门店ID", requiredness = Requiredness.REQUIRED)
        @ApiModelProperty(name = "门店ID")
        private Long storeId;

        @FieldDoc(description = "SPU编码", requiredness = Requiredness.REQUIRED)
        @ApiModelProperty(name = "SPU编码")
        private String spuId;

        @FieldDoc(description = "erp商品编码", requiredness = Requiredness.REQUIRED)
        @ApiModelProperty(name = "erp商品编码")
        private String erpCode;

        @FieldDoc(description = "upc编码列表", requiredness = Requiredness.REQUIRED)
        @ApiModelProperty(name = "upc编码列表")
        private List<String> upc;

        public void validate() {
            if (storeId == null) {
                throw new ParamException("门店ID不能为空");
            }
            if (StringUtils.isBlank(spuId)) {
                throw new ParamException("SPU编码不能为空");
            }
            if (StringUtils.isBlank(erpCode)) {
                throw new ParamException("erp商品编码不能为空");
            }
            if (CollectionUtils.isEmpty(upc)) {
                throw new ParamException("upc编码列表不能为空");
            }
        }

    }

    public void validate() {
        if (CollectionUtils.isEmpty(storeSpuList)) {
            throw new ParamException("门店SPU参数不能为空");
        }
        for (StoreSpuUpdateParamVO storeSpuUpdateParamVO : storeSpuList) {
            storeSpuUpdateParamVO.validate();
        }
        if (StringUtils.isBlank(opType)) {
            throw new ParamException("操作类型不能为空");
        }
    }
}
