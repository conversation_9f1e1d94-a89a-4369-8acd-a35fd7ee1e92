package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;

import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSaleAttributeValueDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SaleAttributeDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SaleAttributeValueDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.TenantSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.TenantSpuAddSpecDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.TenantSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.TenantSpuAddSpecRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.UpdateTenantSpuRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SkuChannelSaleAttributeValueDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SkuSaleAttributeValueDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuChannelSaleAttributeDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuSaleAttributeDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSpuBizDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.BatchUpdateTenantSpuRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.StoreSpuFastCreateRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.review.SubmitTenantProductReviewRequest;
import com.sankuai.meituan.shangou.empower.productplatform.util.SwitchUtils;

/**
 * 销售属性兼容工具类，因为前端会直接使用新字段，不再使用老字段，后端需要保证数据向前兼容
 * <AUTHOR>
 * @since 2025-02-19
 */
public class SaleAttrCompatUtils {
    private SaleAttrCompatUtils() {
    }

    /**
     * 新建规格兼容
     */
    public static void compat(TenantSpuAddSpecRequest request) {
        if (request == null) {
            return;
        }

        request.getTenantSpuAddSpecDTOList().forEach(dto -> {
            writeNewToOld(dto);
            if (SwitchUtils.useSaleAttr(request.getTenantId())) {
                writeOldToNew(dto);
            }
        });
    }

    /**
     * 新客户端兼容（新客户端，但是未开启销售属性功能）
     */
    private static void writeNewToOld(TenantSpuAddSpecDTO dto) {
        List<ChannelSaleAttributeValueDTO> channelSaleAttributeValueDTOList = dto.getChannelSaleAttributeValues();
        if (CollectionUtils.isEmpty(channelSaleAttributeValueDTOList)) {
            return;
        }
        // 新字段
        ChannelSaleAttributeValueDTO jdChannelSaleAttrValue = channelSaleAttributeValueDTOList.stream()
                .filter(attr -> Objects.equals(attr.getChannelId(), EnhanceChannelType.JDDJ.getChannelId()))
                .findFirst().orElse(null);
        if (jdChannelSaleAttrValue == null) {
            return;
        }

        // 回写老字段
        if (CollectionUtils.isEmpty(dto.getSaleAttributeValueDTOS())) {
            dto.setSaleAttributeValueDTOS(jdChannelSaleAttrValue.getSaleAttributeValues());
        }
    }

    /**
     * 老客户端兼容（老客户端，但是开启了销售属性功能）
     */
    private static void writeOldToNew(TenantSpuAddSpecDTO dto) {
        // 老字段
        List<SaleAttributeValueDTO> jdSaleAttrValues = dto.getSaleAttributeValueDTOS();
        if (CollectionUtils.isEmpty(jdSaleAttrValues)) {
            return;
        }

        // 回写新字段
        if (CollectionUtils.isNotEmpty(dto.getChannelSaleAttributeValues())) {
            return;
        }
        ChannelSaleAttributeValueDTO jdChannelSaleAttrValue = new ChannelSaleAttributeValueDTO();
        jdChannelSaleAttrValue.setChannelId(EnhanceChannelType.JDDJ.getChannelId());
        jdChannelSaleAttrValue.setSaleAttributeValues(jdSaleAttrValues);
        dto.setChannelSaleAttributeValues(Collections.singletonList(jdChannelSaleAttrValue));
    }

    /**
     * 更新商品兼容
     */
    public static UpdateTenantSpuRequest compat(UpdateTenantSpuRequest request) {
        if (request == null) {
            return null;
        }
        fillJdSaleAttrCompat(request.getTenantSpu());
        return request;
    }

    /**
     * 批量更新商品兼容
     */
    public static BatchUpdateTenantSpuRequest compat(BatchUpdateTenantSpuRequest request) {
        if (request == null || request.getTenantSpus() == null) {
            return request;
        }
        request.getTenantSpus().forEach(SaleAttrCompatUtils::fillJdSaleAttrCompat);
        return request;
    }

    /**
     * 仅当老字段为空的时候覆盖老的字段
     */
    private static void fillJdSaleAttrCompat(TenantSpuDTO tenantSpu) {
        writeNewToOld(tenantSpu);
        if (SwitchUtils.useSaleAttr(tenantSpu.getTenantId())) {
            writeOldToNew(tenantSpu);
        }
    }

    private static void writeNewToOld(TenantSpuDTO tenantSpu) {
        // 新字段
        List<SaleAttributeDTO> jdSaleAttr = Optional.of(tenantSpu)
                .map(TenantSpuDTO::getJdChannelCategory)
                .map(ChannelCategoryDTO::getSaleAttributes)
                .orElse(null);
        if (CollectionUtils.isEmpty(jdSaleAttr)) {
            return;
        }
        // 回写老字段
        if (CollectionUtils.isEmpty(tenantSpu.getJdSaleAttributeList())) {
            tenantSpu.setJdSaleAttributeList(jdSaleAttr);
        }

        for (TenantSkuDTO tenantSkuDTO : tenantSpu.getTenantSkuDTOList()) {
            // 新字段
            List<SaleAttributeValueDTO> jdSaleAttrValue = Optional.ofNullable(tenantSkuDTO.getChannelSaleAttributeValues())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(channelSaleAttributeValueDTO -> Objects.equals(channelSaleAttributeValueDTO.getChannelId(), EnhanceChannelType.JDDJ.getChannelId()))
                    .map(ChannelSaleAttributeValueDTO::getSaleAttributeValues)
                    .findFirst()
                    .orElse(Collections.emptyList());

            if (CollectionUtils.isEmpty(jdSaleAttrValue)) {
                continue;
            }

            // 回写老字段
            if (CollectionUtils.isEmpty(tenantSkuDTO.getJdAttrValueList())) {
                tenantSkuDTO.setJdAttrValueList(jdSaleAttrValue);
            }
        }
    }

    private static void writeOldToNew(TenantSpuDTO tenantSpu) {
        // 老字段
        List<SaleAttributeDTO> jdSaleAttributeList = tenantSpu.getJdSaleAttributeList();
        if (CollectionUtils.isEmpty(jdSaleAttributeList)) {
            return;
        }
        // 回写新字段
        if (tenantSpu.getJdChannelCategory() == null || CollectionUtils.isNotEmpty(tenantSpu.getJdChannelCategory().getSaleAttributes())) {
            return;
        }
        tenantSpu.getJdChannelCategory().setSaleAttributes(jdSaleAttributeList);


        Map<String, Long> name2Id = new HashMap<>();
        for (SaleAttributeDTO saleAttributeDTO : jdSaleAttributeList) {
            name2Id.put(saleAttributeDTO.getAttrName(), saleAttributeDTO.getAttrId());
        }

        for (TenantSkuDTO tenantSkuDTO : tenantSpu.getTenantSkuDTOList()) {
            // 老字段
            List<SaleAttributeValueDTO> jdSaleAttrValues = tenantSkuDTO.getJdAttrValueList();
            if (CollectionUtils.isEmpty(jdSaleAttrValues)) {
                continue;
            }

            // Attention：处理spu时没有做返回，说明SPU上存在老字段且没有新字段，是老版本APP，此时覆盖SKU上返回的新销售属性字段
//            if (CollectionUtils.isNotEmpty(tenantSkuDTO.getChannelSaleAttributeValues())) {
//                continue;
//            }
            jdSaleAttrValues.stream().filter(jdSaleAttrValue -> jdSaleAttrValue.getAttrId() == null)
                    .forEach(jdSaleAttrValue -> jdSaleAttrValue.setAttrId(name2Id.get(jdSaleAttrValue.getAttrName())));

            // 回写新字段
            ChannelSaleAttributeValueDTO jdChannelSaleAttrValue = new ChannelSaleAttributeValueDTO();
            jdChannelSaleAttrValue.setChannelId(EnhanceChannelType.JDDJ.getChannelId());
            jdChannelSaleAttrValue.setSaleAttributeValues(jdSaleAttrValues);
            tenantSkuDTO.setChannelSaleAttributeValues(Collections.singletonList(jdChannelSaleAttrValue));
        }
    }

    private static void fillJdSaleAttrCompat(TenantSpuBizDTO tenantSpu) {
        writeNewToOld(tenantSpu);
        if (SwitchUtils.useSaleAttr(tenantSpu.getTenantId())) {
            writeOldToNew(tenantSpu);
        }
    }

    private static void writeNewToOld(TenantSpuBizDTO tenantSpu) {
        // 新字段
        List<SpuSaleAttributeDTO> jdSaleAttr = Optional.ofNullable(tenantSpu)
                .map(TenantSpuBizDTO::getJdChannelCategory)
                .map(com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO::getSaleAttrList)
                .orElse(null);
        if (CollectionUtils.isEmpty(jdSaleAttr)) {
            return;
        }
        // 回写老字段
        if (CollectionUtils.isEmpty(tenantSpu.getJdSaleAttributeList())) {
            tenantSpu.setJdSaleAttributeList(jdSaleAttr);
        }

        for (com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSkuDTO tenantSkuDTO : tenantSpu.getTenantSkuDTOList()) {
            // 新字段
            List<SkuSaleAttributeValueDTO> jdSaleAttrValue = Optional.ofNullable(tenantSkuDTO.getChannelSaleAttrValueList())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(channelSaleAttributeValueDTO -> Objects.equals(channelSaleAttributeValueDTO.getChannelId(), EnhanceChannelType.JDDJ.getChannelId()))
                    .map(SkuChannelSaleAttributeValueDTO::getSkuSaleAttributeValues)
                    .findFirst()
                    .orElse(Collections.emptyList());

            if (CollectionUtils.isEmpty(jdSaleAttrValue)) {
                continue;
            }

            // 回写老字段
            if (CollectionUtils.isEmpty(tenantSkuDTO.getJdAttrValueList())) {
                tenantSkuDTO.setJdAttrValueList(jdSaleAttrValue);
            }
        }
    }

    private static void writeOldToNew(TenantSpuBizDTO tenantSpu) {
        // 老字段
        List<SpuSaleAttributeDTO> jdSaleAttrList = tenantSpu.getJdSaleAttributeList();
        if (CollectionUtils.isEmpty(jdSaleAttrList)) {
            return;
        }
        // 回写新字段
        if (tenantSpu.getJdChannelCategory() == null || CollectionUtils.isNotEmpty(tenantSpu.getJdChannelCategory().getSaleAttrList())) {
            return;
        }
        tenantSpu.getJdChannelCategory().setSaleAttrList(jdSaleAttrList);

        Map<String, Long> name2Id = new HashMap<>();
        for (SpuSaleAttributeDTO saleAttributeDTO : jdSaleAttrList) {
            name2Id.put(saleAttributeDTO.getAttrName(), saleAttributeDTO.getAttrId());
        }

        for (com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSkuDTO tenantSkuDTO : tenantSpu.getTenantSkuDTOList()) {
            // 老字段
            List<SkuSaleAttributeValueDTO> jdSaleAttrValues = tenantSkuDTO.getJdAttrValueList();
            if (CollectionUtils.isEmpty(jdSaleAttrValues)) {
                continue;
            }

            // Attention：处理spu时没有做返回，说明SPU上存在老字段且没有新字段，是老版本APP，此时覆盖SKU上返回的新销售属性字段
//            if (CollectionUtils.isNotEmpty(tenantSkuDTO.getChannelSaleAttrValueList())) {
//                continue;
//            }
            jdSaleAttrValues.stream().filter(saleAttrValue -> saleAttrValue.getAttrId() == null)
                    .forEach(saleAttrValue -> saleAttrValue.setAttrId(name2Id.get(saleAttrValue.getAttrName())));

            // 回写新字段
            SkuChannelSaleAttributeValueDTO jdChannelSaleAttrValue = new SkuChannelSaleAttributeValueDTO();
            jdChannelSaleAttrValue.setChannelId(EnhanceChannelType.JDDJ.getChannelId());
            jdChannelSaleAttrValue.setSkuSaleAttributeValues(jdSaleAttrValues);
            tenantSkuDTO.setChannelSaleAttrValueList(Collections.singletonList(jdChannelSaleAttrValue));
        }
    }

    public static StoreSpuFastCreateRequest compat(StoreSpuFastCreateRequest storeSpuFastCreateRequest) {
        writeNewToOld(storeSpuFastCreateRequest);
        if (SwitchUtils.useSaleAttr(storeSpuFastCreateRequest.getTenantId())) {
            writeOldToNew(storeSpuFastCreateRequest);
        }
        return storeSpuFastCreateRequest;
    }

    private static void writeNewToOld(StoreSpuFastCreateRequest storeSpuFastCreateRequest) {
        // 新字段
        List<SpuSaleAttributeDTO> jdSaleAttr = storeSpuFastCreateRequest.getChannelSaleAttributes().stream()
                .filter(saleAttr -> Objects.equals(saleAttr.getChannelId(), EnhanceChannelType.JDDJ.getChannelId()))
                .map(SpuChannelSaleAttributeDTO::getSpuSaleAttributes)
                .findAny()
                .orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(jdSaleAttr)) {
            return;
        }
        // 回写老字段
        if (CollectionUtils.isEmpty(storeSpuFastCreateRequest.getJdSaleAttributeList())) {
            storeSpuFastCreateRequest.setJdSaleAttributeList(jdSaleAttr);
        }

        for (StoreSkuDTO storeSkuDTO : storeSpuFastCreateRequest.getStoreSkuList()) {
            // 新字段
            List<SkuSaleAttributeValueDTO> jdSaleAttrValue = Optional.ofNullable(storeSkuDTO.getChannelSaleAttributeValues())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(channelSaleAttributeValueDTO -> Objects.equals(channelSaleAttributeValueDTO.getChannelId(), EnhanceChannelType.JDDJ.getChannelId()))
                    .map(SkuChannelSaleAttributeValueDTO::getSkuSaleAttributeValues)
                    .findFirst()
                    .orElse(Collections.emptyList());

            if (CollectionUtils.isEmpty(jdSaleAttrValue)) {
                continue;
            }

            // 回写老字段
            if (CollectionUtils.isEmpty(storeSkuDTO.getJdSaleAttributeValueDTOS())) {
                storeSkuDTO.setJdSaleAttributeValueDTOS(jdSaleAttrValue);
            }
        }
    }

    private static void writeOldToNew(StoreSpuFastCreateRequest storeSpuFastCreateRequest) {
        // 老字段
        List<SpuSaleAttributeDTO> jdSaleAttributeList = storeSpuFastCreateRequest.getJdSaleAttributeList();
        if (CollectionUtils.isEmpty(jdSaleAttributeList)) {
            return;
        }

        Map<String, Long> name2Id = new HashMap<>();
        for (SpuSaleAttributeDTO saleAttributeDTO : jdSaleAttributeList) {
            name2Id.put(saleAttributeDTO.getAttrName(), saleAttributeDTO.getAttrId());
        }

        // 回写新字段
        if (CollectionUtils.isNotEmpty(storeSpuFastCreateRequest.getChannelSaleAttributes())) {
            return;
        }
        SpuChannelSaleAttributeDTO jdChannelSaleAttribute = new SpuChannelSaleAttributeDTO();
        jdChannelSaleAttribute.setChannelId(EnhanceChannelType.JDDJ.getChannelId());
        jdChannelSaleAttribute.setSpuSaleAttributes(jdSaleAttributeList);
        storeSpuFastCreateRequest.setChannelSaleAttributes(Collections.singletonList(jdChannelSaleAttribute));

        for (StoreSkuDTO storeSkuDTO : storeSpuFastCreateRequest.getStoreSkuList()) {
            // 老字段
            List<SkuSaleAttributeValueDTO> jdSaleAttrValues = storeSkuDTO.getJdSaleAttributeValueDTOS();
            if (CollectionUtils.isEmpty(jdSaleAttrValues)) {
                continue;
            }

            if (CollectionUtils.isNotEmpty(storeSkuDTO.getChannelSaleAttributeValues())) {
                continue;
            }

            jdSaleAttrValues.stream().filter(jdSaleAttrValue -> jdSaleAttrValue.getAttrId() == null)
                    .forEach(jdSaleAttrValue -> {
                        jdSaleAttrValue.setAttrId(name2Id.get(jdSaleAttrValue.getAttrName()));
                    });

            // 回写新字段
            SkuChannelSaleAttributeValueDTO jdChannelSaleAttrValue = new SkuChannelSaleAttributeValueDTO();
            jdChannelSaleAttrValue.setChannelId(EnhanceChannelType.JDDJ.getChannelId());
            jdChannelSaleAttrValue.setSkuSaleAttributeValues(jdSaleAttrValues);
            storeSkuDTO.setChannelSaleAttributeValues(Collections.singletonList(jdChannelSaleAttrValue));
        }
    }

    public static SubmitTenantProductReviewRequest compat(SubmitTenantProductReviewRequest submitTenantProductReviewRequest) {
        writeNewToOld(submitTenantProductReviewRequest);
        if (SwitchUtils.useSaleAttr(submitTenantProductReviewRequest.getTenantId())) {
            writeOldToNew(submitTenantProductReviewRequest);
        }
        return submitTenantProductReviewRequest;
    }

    private static void writeNewToOld(SubmitTenantProductReviewRequest submitTenantProductReviewRequest) {
        // 新字段
        List<SpuSaleAttributeDTO> jdSaleAttr = submitTenantProductReviewRequest.getChannelSaleAttributeList().stream()
                .filter(saleAttr -> Objects.equals(saleAttr.getChannelId(), EnhanceChannelType.JDDJ.getChannelId()))
                .map(SpuChannelSaleAttributeDTO::getSpuSaleAttributes)
                .findAny()
                .orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(jdSaleAttr)) {
            return;
        }
        // 回写老字段
        if (CollectionUtils.isEmpty(submitTenantProductReviewRequest.getJdSaleAttrList())) {
            submitTenantProductReviewRequest.setJdSaleAttrList(jdSaleAttr);
        }

        for (SubmitTenantProductReviewRequest.SkuInfo skuInfo : submitTenantProductReviewRequest.getSkuList()) {
            // 新字段
            List<SkuSaleAttributeValueDTO> jdSaleAttrValue = Optional.ofNullable(skuInfo.getChannelSaleAttributeList())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(channelSaleAttributeValueDTO -> Objects.equals(channelSaleAttributeValueDTO.getChannelId(), EnhanceChannelType.JDDJ.getChannelId()))
                    .map(SkuChannelSaleAttributeValueDTO::getSkuSaleAttributeValues)
                    .findFirst()
                    .orElse(Collections.emptyList());

            if (CollectionUtils.isEmpty(jdSaleAttrValue)) {
                continue;
            }

            // 回写老字段
            if (CollectionUtils.isEmpty(skuInfo.getJdSaleAttrValueList())) {
                skuInfo.setJdSaleAttrValueList(jdSaleAttrValue);
            }
        }
    }

    private static void writeOldToNew(SubmitTenantProductReviewRequest submitTenantProductReviewRequest) {
        // 老字段
        List<SpuSaleAttributeDTO> jdSaleAttributeList = submitTenantProductReviewRequest.getJdSaleAttrList();
        if (CollectionUtils.isEmpty(jdSaleAttributeList)) {
            return;
        }

        // 回写新字段
        if (CollectionUtils.isNotEmpty(submitTenantProductReviewRequest.getChannelSaleAttributeList())) {
            return;
        }

        Map<String, Long> name2Id = new HashMap<>();
        for (SpuSaleAttributeDTO saleAttributeDTO : jdSaleAttributeList) {
            name2Id.put(saleAttributeDTO.getAttrName(), saleAttributeDTO.getAttrId());
        }

        SpuChannelSaleAttributeDTO jdChannelSaleAttribute = new SpuChannelSaleAttributeDTO();
        jdChannelSaleAttribute.setChannelId(EnhanceChannelType.JDDJ.getChannelId());
        jdChannelSaleAttribute.setSpuSaleAttributes(jdSaleAttributeList);
        submitTenantProductReviewRequest.setChannelSaleAttributeList(Collections.singletonList(jdChannelSaleAttribute));

        for (SubmitTenantProductReviewRequest.SkuInfo skuInfo : submitTenantProductReviewRequest.getSkuList()) {
            // 老字段
            List<SkuSaleAttributeValueDTO> jdSaleAttrValues = skuInfo.getJdSaleAttrValueList();
            if (CollectionUtils.isEmpty(jdSaleAttrValues)) {
                continue;
            }

            // 回写新字段
            if (CollectionUtils.isNotEmpty(skuInfo.getChannelSaleAttributeList())) {
                continue;
            }
            jdSaleAttrValues.stream().filter(attrValue -> attrValue.getAttrId() == null)
                    .forEach(jdSaleAttrValue -> jdSaleAttrValue.setAttrId(name2Id.get(jdSaleAttrValue.getAttrName())));
            SkuChannelSaleAttributeValueDTO jdChannelSaleAttrValue = new SkuChannelSaleAttributeValueDTO();
            jdChannelSaleAttrValue.setChannelId(EnhanceChannelType.JDDJ.getChannelId());
            jdChannelSaleAttrValue.setSkuSaleAttributeValues(jdSaleAttrValues);
            skuInfo.setChannelSaleAttributeList(Collections.singletonList(jdChannelSaleAttrValue));
        }
    }
}
