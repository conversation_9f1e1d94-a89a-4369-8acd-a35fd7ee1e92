package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.ChannelBrandRelationVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrValueVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.container.SpuContainer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.AiRecommendVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelSkuAttrValueInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.CartonMeasureConvertFactorVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelDynamicInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSaleAttrInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.MedicalDeviceQuaInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.NameSupplementInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.SpecialPictureVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreGroupCategoryCodeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.VideoInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.SaleAttrCompatUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.AiRecommendDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SkuSaleAttributeValueDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuSaleAttributeDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.sku.CombineChildSkuDto;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.review.SubmitTenantProductReviewRequest;
import com.sankuai.meituan.shangou.saas.common.money.PriceUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 保存商品提报信息请求
 * @author: WangSukuan
 * @create: 2020-03-10
 **/
@EqualsAndHashCode(callSuper = true)
@TypeDoc(
        description = "保存商品提报信息请求"
)
@Data
@ApiModel("保存商品提报信息请求")
public class SkuReviewApplySaveRequest extends SpuContainer {

    private static final Integer SUPPORT_MULTI_SPEC_YES = 1;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    @NotNull
    private String skuName;

    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片", required = true)
    @NotNull
    private List<String> imageList;

    @FieldDoc(
            description = "商品类目编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品类目编码")
    private String categoryCode;

    @FieldDoc(
            description = "商品品牌编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品品牌编码")
    private String brandCode;

    @FieldDoc(
            description = "产地", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "产地")
    private String productionArea;

    @FieldDoc(
            description = "upc", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "upc")
    @Deprecated
    private String upc;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售卖单位", required = true)
    @Deprecated
    private String baseUnit;

    @FieldDoc(
            description = "称重类型(1:称重计重 3:非称重/标品)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "称重类型", required = true)
    @NotNull
    private Integer weightType;

    @FieldDoc(
            description = "提报方式(0:普通提报 1:标品库匹配提报)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "提报方式")
    @NotNull
    private Integer applyType = 0;

    @FieldDoc(
            description = "商品重量(g)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品重量")
    @Deprecated
    private Integer weight = 0;

    @FieldDoc(
            description = "带单位的重量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "带单位的重量")
    @Deprecated
    private String weightForUnit = "0";

    @FieldDoc(
            description = "重量单位", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "重量单位")
    @Deprecated
    private String weightUnit = "克(g)";

    @FieldDoc(
            description = "渠道类目编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目编码")
    private String channelCategoryCode;

    @FieldDoc(
            description = "渠道类目动态信息", requiredness = Requiredness.OPTIONAL
    )
    private List<ChannelDynamicInfoVO> channelDynamicInfoVOList;

    @FieldDoc(
            description = "商品视频信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品视频信息")
    private VideoInfoVO video;

    @FieldDoc(
            description = "商品视频链接", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品视频链接")
    private List<String> pictureContents;

    @FieldDoc(
            description = "店内分类编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "店内分类编码")
    private List<String> storeCategoryIds;

    @FieldDoc(
            description = "饿了么渠道类目编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "饿了么渠道类目编码")
    private String elemChannelCategoryCode;

    @FieldDoc(
            description = "饿了么渠道类目动态信息", requiredness = Requiredness.OPTIONAL
    )
    private List<ChannelDynamicInfoVO> elemChannelDynamicInfoVOList;

    @FieldDoc(
            description = "商品规格类型 1单规格 2多规格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品规格类型 1单规格 2多规格")
    private Integer specType;


    @FieldDoc(
            description = "京东渠道类目编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东渠道类目编码")
    private String jdCategoryId;


    @FieldDoc(
            description = "京东到家渠道类目属性列表，多规格传入", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东到家渠道类目属性列表，多规格传入")
    private List<SaleAttrVo> jdSaleAttrList;


    @FieldDoc(
            description = "京东类目属性值，多规格传入", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东类目属性值")
    @Deprecated
    private List<SaleAttrValueVo> jdAttrValues;


    @FieldDoc(
            description = "建议零售价单位元，开通京东渠道传入", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "建议零售价单位元")
    @Deprecated
    private Double suggestPrice;

    @FieldDoc(
            description = "商品规格名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品规格名称")
    @Deprecated
    private String specName;

    @FieldDoc(
            description = "店内分类分组列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "店内分类分组列表")
    private List<StoreGroupCategoryCodeVO> storeGroupCategoryCodes;

    @FieldDoc(
            description = "是否开启效期检查，0-不检查 1-检查"
    )
    @ApiModelProperty(value = "是否开启效期检查，0-不检查 1-检查")
    @Deprecated
    private Integer enableExpiredCheck;

    @FieldDoc(
            description = "产地 1-国产 2-进口"
    )
    @ApiModelProperty(value = "产地 1-国产 2-进口")
    @Deprecated
    private Integer originPlace;

    @FieldDoc(
            description = "保质期天数"
    )
    @ApiModelProperty(value = "保质期天数")
    @Deprecated
    private Integer expirationDays;

    @FieldDoc(
            description = "渠道品牌列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道品牌列表")
    @Valid
    private List<ChannelBrandRelationVO> channelBrand;

    @FieldDoc(
            description = "外部编码"
    )
    @ApiModelProperty(name = "外部编码")
    @Deprecated
    private String externalCode;

    @FieldDoc(
            description = "加盟商总部编码"
    )
    @ApiModelProperty(value = "加盟商总部编码")
    @Deprecated
    private String franchiseeHeadquartersCode;

    @FieldDoc(
            description = "采购平台编码"
    )
    @ApiModelProperty(value = "采购平台编码")
    @Deprecated
    private String purchasePlatformCode;


    @FieldDoc(
            description = "箱规单位转换系数"
    )
    @ApiModelProperty(value = "箱规单位转换系数")
    @Deprecated
    private List<CartonMeasureConvertFactorVO> cartonMeasureConvertFactorList;

    @FieldDoc(
            description = "是否自定义无upc编码  0-否，1-是"
    )
    @ApiModelProperty(value = "是否自定义无upc编码  0-否，1-是")
    @Deprecated
    private Integer customizeNoUpcCode;

    @FieldDoc(
            description = "抖音渠道类目编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "抖音渠道类目编码")
    private String douyinCategoryId;

    @FieldDoc(
            description = "抖音渠道类目动态信息", requiredness = Requiredness.OPTIONAL
    )
    private List<ChannelDynamicInfoVO> douyinChannelDynamicInfoVOList;

    @FieldDoc(
            description = "抖音售后服务类型", requiredness = Requiredness.OPTIONAL
    )
    private String douyinAfterSaleServiceType;

    @FieldDoc(
            description = "商品卖点"
    )
    @ApiModelProperty(value = "商品卖点")
    private String sellingPoint;

    @FieldDoc(
            description = "商品描述"
    )
    @ApiModelProperty(value = "商品描述")
    private String description;

    @FieldDoc(
            description = "商品属性 不超过十个属性", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品属性")
    private List<StoreSkuPropertyVO> properties;

    @FieldDoc(
            description = "医药器械资质图信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "医药器械资质图信息")
    private MedicalDeviceQuaInfoVO medicalDeviceQuaInfo;

    @FieldDoc(
            description = "是否门店自定义资质图信息标识", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否门店自定义资质图信息标识")
    private Boolean customizedMedicalDeviceQuaInfo;

    @FieldDoc(
            description = "美团售后服务类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "美团售后服务类型")
    private String mtAfterSaleServiceType;

    @FieldDoc(
            description = "是否门店自定义美团售后服务类型标识", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否门店自定义美团售后服务类型标识")
    private Boolean customizedMtAfterSaleServiceType;

    @FieldDoc(
            description = "客户端是否支持多规格，1-支持，2-不支持，默认不支持"
    )
    @ApiModelProperty(value = "客户端是否支持多规格，1-支持，2-不支持，默认不支持")
    private Integer supportMultiSpec;

    @FieldDoc(
            description = "规格信息，supportMultiSpec为1时必传"
    )
    @ApiModelProperty(value = "规格信息，supportMultiSpec为1时必传")
    private List<ReviewSkuVO> skuList;

    @FieldDoc(
            description = "提报类型"
    )
    @ApiModelProperty(value = "提报类型")
    private String submitType;

    @FieldDoc(
            description = "提报备注"
    )
    @ApiModelProperty(value = "提报备注")
    private String submitRemark;

    @FieldDoc(
            description = "同步渠道分组"
    )
    @ApiModelProperty(value = "同步渠道分组")
    private List<Integer> poiGroupIdList;

    @FieldDoc(
            description = "特殊管控商品资质"
    )
    @ApiModelProperty("特殊管控商品资质")
    private List<String> controlQuaPicUrl;

    @FieldDoc(
            description = "AI推荐信息"
    )
    @ApiModelProperty(value = "AI推荐信息")
    private AiRecommendVO aiRecommendVO;

    @FieldDoc(
            description = "渠道sku属性值信息"
    )
    @ApiModelProperty(value = "渠道sku属性值信息")
    private List<ChannelSkuAttrValueInfoVo> channelSkuAttrValueInfoList;

    @FieldDoc(
            description = "商品名称补充语信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品名称补充语信息")
    private NameSupplementInfoVO nameSupplementInfo;

    @FieldDoc(
            description = "类目特殊图"
    )
    @ApiModelProperty(value = "类目特殊图")
    private List<SpecialPictureVO> mtSpecialPictureList;

    public void selfCheck() {
        // 旧不支持多规格的客户端需要校验单位（因为移除了jsr校验注解，在此手工实现)
        if (!SUPPORT_MULTI_SPEC_YES.equals(this.getSupportMultiSpec())) {
            Preconditions.checkArgument(baseUnit != null, "售卖单位不能为空");
        }
    }

    /**
     * @see SkuReviewApplySaveRequest#convertBizRequest(User)
     * @param user
     * @return
     */
    @Deprecated
    public SaveSkuReviewApplyRequest convertSaveSkuReviewApplyRequest(User user) {

        SaveSkuReviewApplyRequest saveSkuReviewApplyRequest = new SaveSkuReviewApplyRequest();

        saveSkuReviewApplyRequest.setTenantId(user.getTenantId());
        saveSkuReviewApplyRequest.setStoreId(this.getStoreId());
        saveSkuReviewApplyRequest.setSkuName(this.getSkuName().trim());
        saveSkuReviewApplyRequest.setImageList(this.getImageList());
        saveSkuReviewApplyRequest.setCategoryCode(this.getCategoryCode());
        saveSkuReviewApplyRequest.setBrandCode(this.getBrandCode());
        if (StringUtils.isNotEmpty(this.getProductionArea())) {
            saveSkuReviewApplyRequest.setProductionArea(this.getProductionArea().trim());
        }
        if (StringUtils.isNotEmpty(this.getUpc())) {
            saveSkuReviewApplyRequest.setUpc(this.getUpc().trim());
        }
        saveSkuReviewApplyRequest.setBaseUnit(this.getBaseUnit().trim());
        saveSkuReviewApplyRequest.setOperateId(user.getAccountId());
        saveSkuReviewApplyRequest.setOperateName(user.getOperatorName());
        saveSkuReviewApplyRequest.setWeightType(this.getWeightType());
        saveSkuReviewApplyRequest.setApplyType(this.getApplyType());

        saveSkuReviewApplyRequest.setWeight(this.getWeight());
        saveSkuReviewApplyRequest.setWeightForUnit(this.getWeightForUnit());
        saveSkuReviewApplyRequest.setWeightUnit(this.getWeightUnit());
        saveSkuReviewApplyRequest.setChannelCategoryCode(this.getChannelCategoryCode());
        if (Objects.nonNull(this.getVideo())) {
            saveSkuReviewApplyRequest.setVideo(this.getVideo().toDTO());
        }
        saveSkuReviewApplyRequest.setPictureContents(this.getPictureContents());
        if (CollectionUtils.isNotEmpty(this.getStoreCategoryIds())) {
            saveSkuReviewApplyRequest.setStoreCategoryIds(ConverterUtils.convertList(this.getStoreCategoryIds(), Long::valueOf));
        }
        saveSkuReviewApplyRequest.setElemChannelCategoryCode(this.getElemChannelCategoryCode());
        if (CollectionUtils.isNotEmpty(this.getElemChannelDynamicInfoVOList())) {
            saveSkuReviewApplyRequest.setElemCategoryProperties(
                    ChannelDynamicInfoVO.toCategoryProperties(this.getElemChannelDynamicInfoVOList()));
        }
        if (this.getSpecType() != null && this.getSpecType() > 0) {
            saveSkuReviewApplyRequest.setSpecType(this.getSpecType());
        } else {
            saveSkuReviewApplyRequest.setSpecType(2);
        }
        saveSkuReviewApplyRequest.setJdChannelCategoryCode(this.jdCategoryId);
        if (CollectionUtils.isNotEmpty(this.jdSaleAttrList)) {
            saveSkuReviewApplyRequest.setJdSaleAttributeList(this.jdSaleAttrList.stream()
                    .map(e -> new SaleAttributeDTO().setAttrId(e.getAttrId()).setAttrName(e.getAttrName()))
                    .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(this.jdAttrValues)) {
            saveSkuReviewApplyRequest.setJdSaleAttributeValueList(this.jdAttrValues.stream()
                    .map(e -> new SaleAttributeValueDTO().setAttrName(e.getAttrName()).setAttrValue(e.getAttrValue()))
                    .collect(Collectors.toList()));
        }
        saveSkuReviewApplyRequest.setSuggestPrice(this.suggestPrice != null ? PriceUtils.yuan2Fen(this.suggestPrice) : 0L);

        if (CollectionUtils.isNotEmpty(this.getStoreGroupCategoryCodes())) {
            List<StoreGroupCategoryCodeDTO> storeGroupCategoryDTOList = Lists.newArrayList();
            this.getStoreGroupCategoryCodes().forEach(storeGroupCategoryCodeVO -> {
                StoreGroupCategoryCodeDTO dto = new StoreGroupCategoryCodeDTO();
                dto.setStoreGroupId(storeGroupCategoryCodeVO.getStoreGroupId());
                dto.setCategoryCodes(storeGroupCategoryCodeVO.getCategoryCodes());
                storeGroupCategoryDTOList.add(dto);
            });
            saveSkuReviewApplyRequest.setStoreGroupCagegoryCodes(storeGroupCategoryDTOList);
        }
        saveSkuReviewApplyRequest.setSpecName(this.getSpecName());
        saveSkuReviewApplyRequest.setCategoryProperties(ChannelDynamicInfoVO.toCategoryProperties(this.getChannelDynamicInfoVOList()));
        if (this.getEnableExpiredCheck() != null) {
            saveSkuReviewApplyRequest.setEnableExpiredCheck(this.getEnableExpiredCheck());
        }
        if (this.getOriginPlace() != null) {
            saveSkuReviewApplyRequest.setOriginPlace(this.getOriginPlace());
        }
        if (this.getExpirationDays() != null) {
            saveSkuReviewApplyRequest.setExpirationDays(this.getExpirationDays());
        }
        saveSkuReviewApplyRequest.setExternalCode(this.getExternalCode());
        saveSkuReviewApplyRequest.setFranchiseeHeadquartersCode(this.getFranchiseeHeadquartersCode());
        saveSkuReviewApplyRequest.setPurchasePlatformCode(this.getPurchasePlatformCode());
        saveSkuReviewApplyRequest.setCategoryProperties(ChannelDynamicInfoVO.toCategoryProperties(this.getChannelDynamicInfoVOList()));
        if(CollectionUtils.isNotEmpty(this.channelBrand)) {
            saveSkuReviewApplyRequest.setChannelBrands(this.channelBrand.stream().map(c->{
                ChannelBrandRelationDTO channelBrandRelationDTO = new ChannelBrandRelationDTO();
                channelBrandRelationDTO.setChannelId(c.getChannelId());
                channelBrandRelationDTO.setBrandCode(c.getBrandCode());
                return channelBrandRelationDTO;
            }).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(this.getCartonMeasureConvertFactorList())) {
            saveSkuReviewApplyRequest.setCartonMeasureList(Fun.map(this.getCartonMeasureConvertFactorList(), CartonMeasureConvertFactorVO::toCartonMeasureForIDLDTO));
        }
        if (this.customizeNoUpcCode != null) {
            saveSkuReviewApplyRequest.setCustomizeNoUpcCode(this.customizeNoUpcCode);
        } else {
            saveSkuReviewApplyRequest.setCustomizeNoUpcCode(0);
        }
        saveSkuReviewApplyRequest.setDouyinChannelCategoryCode(this.getDouyinCategoryId());
        if (CollectionUtils.isNotEmpty(this.getDouyinChannelDynamicInfoVOList())) {
            saveSkuReviewApplyRequest.setDouyinCategoryProperties(ChannelDynamicInfoVO.toCategoryProperties(this.getDouyinChannelDynamicInfoVOList()));
        }
        saveSkuReviewApplyRequest.setDouyinAfterSaleServiceType(this.getDouyinAfterSaleServiceType());

        // 提报增加商品描述、卖点和属性字段
        if (StringUtils.isNotEmpty(this.description)) {
            saveSkuReviewApplyRequest.setDescription(this.description);
        }
        if (StringUtils.isNotEmpty(this.sellingPoint)) {
            saveSkuReviewApplyRequest.setSellPoint(this.sellingPoint);
        }
        if (CollectionUtils.isNotEmpty(this.properties)) {
            saveSkuReviewApplyRequest.setProperties(ConverterUtils.convertList(this.properties,
                    StoreSkuPropertyVO::buildStoreSkuPropertyDTO));
        }
        if (this.medicalDeviceQuaInfo != null) {
            saveSkuReviewApplyRequest.setMedicalDeviceQuaInfo(this.medicalDeviceQuaInfo.toOcmsMedicalDeviceQuaInfoIdl());
        }
        saveSkuReviewApplyRequest.setMtAfterSaleServiceType(this.mtAfterSaleServiceType);
        return saveSkuReviewApplyRequest;
    }

    public SubmitTenantProductReviewRequest convertBizRequest(User user) {
        SubmitTenantProductReviewRequest rpcReq = new SubmitTenantProductReviewRequest();
        rpcReq.setTenantId(user.getTenantId());
        rpcReq.setStoreId(this.getStoreId());
        rpcReq.setName(this.getSkuName().trim());
        rpcReq.setPictures(this.getImageList());
        rpcReq.setCategoryCode(this.getCategoryCode());
        rpcReq.setBrandCode(this.getBrandCode());
        if (StringUtils.isNotEmpty(this.getProductionArea())) {
            rpcReq.setProductionArea(this.getProductionArea().trim());
        }
        rpcReq.setOperatorId(user.getAccountId());
        rpcReq.setOperator(user.getOperatorName());
        rpcReq.setWeightType(this.getWeightType());
        rpcReq.setApplyType(this.getApplyType());

        rpcReq.setChannelCategoryCode(this.getChannelCategoryCode());
        if (Objects.nonNull(this.getVideo())) {
            rpcReq.setVideo(this.getVideo().toBizVideoInfoDTO());
        }
        rpcReq.setPictureContents(this.getPictureContents());
        if (CollectionUtils.isNotEmpty(this.getStoreCategoryIds())) {
            rpcReq.setStoreCategoryIds(ConverterUtils.convertList(this.getStoreCategoryIds(), Long::valueOf));
        }
        rpcReq.setElemChannelCategoryCode(this.getElemChannelCategoryCode());
        if (CollectionUtils.isNotEmpty(this.getElemChannelDynamicInfoVOList())) {
            rpcReq.setElemCategoryProperties(ChannelDynamicInfoVO.toCategoryProperties(this.getElemChannelDynamicInfoVOList()));
        }
        if (this.getSpecType() != null && this.getSpecType() > 0) {
            rpcReq.setSpecType(this.getSpecType());
        } else {
            rpcReq.setSpecType(2);
        }
        rpcReq.setJdChannelCategoryCode(this.jdCategoryId);
        if (CollectionUtils.isNotEmpty(this.jdSaleAttrList)) {
            rpcReq.setJdSaleAttrList(Fun.map(this.jdSaleAttrList, SaleAttrVo::convertBizDTO));
        }
        rpcReq.setChannelSaleAttributeList(Fun.map(this.getChannelSaleAttrInfoList(), ChannelSaleAttrInfoVO::toBizDTO));

        if (CollectionUtils.isNotEmpty(this.getStoreGroupCategoryCodes())) {
            List<com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreGroupCategoryCodeDTO > storeGroupCategoryDTOList = new ArrayList<>();
            this.getStoreGroupCategoryCodes().forEach(storeGroupCategoryCodeVO -> {
                com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreGroupCategoryCodeDTO dto = new com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreGroupCategoryCodeDTO ();
                dto.setStoreGroupId(storeGroupCategoryCodeVO.getStoreGroupId());
                dto.setCategoryCodes(storeGroupCategoryCodeVO.getCategoryCodes());
                storeGroupCategoryDTOList.add(dto);
            });
            rpcReq.setStoreGroupCategoryCodes(storeGroupCategoryDTOList);
        }

        rpcReq.setCategoryProperties(ChannelDynamicInfoVO.toCategoryProperties(this.getChannelDynamicInfoVOList()));
        if(CollectionUtils.isNotEmpty(this.channelBrand)) {
            rpcReq.setChannelBrands(this.channelBrand.stream().map(c->{
                com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelBrandRelationDTO channelBrandRelationDTO = new com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelBrandRelationDTO();
                channelBrandRelationDTO.setChannelId(c.getChannelId());
                channelBrandRelationDTO.setBrandCode(c.getBrandCode());
                return channelBrandRelationDTO;
            }).collect(Collectors.toList()));
        }
        rpcReq.setDouyinChannelCategoryCode(this.getDouyinCategoryId());
        if (CollectionUtils.isNotEmpty(this.getDouyinChannelDynamicInfoVOList())) {
            rpcReq.setDouyinCategoryProperties(ChannelDynamicInfoVO.toCategoryProperties(this.getDouyinChannelDynamicInfoVOList()));
        }
        rpcReq.setDouyinAfterSaleServiceType(this.getDouyinAfterSaleServiceType());

        // 提报增加商品描述、卖点和属性字段
        if (StringUtils.isNotEmpty(this.description)) {
            rpcReq.setDescription(this.description);
        }
        if (StringUtils.isNotEmpty(this.sellingPoint)) {
            rpcReq.setSellingPoint(this.sellingPoint);
        }
        if (CollectionUtils.isNotEmpty(this.properties)) {
            rpcReq.setProperties(ConverterUtils.convertList(this.properties,
                    StoreSkuPropertyVO::buildProductSkutPropertyDTO));
        }
        if (this.medicalDeviceQuaInfo != null) {
            rpcReq.setMedicalDeviceQuaInfo(this.medicalDeviceQuaInfo.toMedicalDeviceQuaInfoDTO());
        }
        rpcReq.setMtAfterSaleServiceType(this.mtAfterSaleServiceType);

        boolean clientSupportMultiSpecAndStoreInfo = Objects.equals(SUPPORT_MULTI_SPEC_YES, this.getSupportMultiSpec());
        rpcReq.setClientSupportMultiSpecAndStoreInfo(clientSupportMultiSpecAndStoreInfo);
        if (clientSupportMultiSpecAndStoreInfo) {
            rpcReq.setSubmitType(this.getSubmitType());
            rpcReq.setSubmitRemark(this.getSubmitRemark());
            rpcReq.setSkuList(parseSkuFromNewClient());
        }
        else {
            rpcReq.setSkuList(parseSkuFromOldClient());
        }
        if(CollectionUtils.isNotEmpty(this.poiGroupIdList)){
            rpcReq.setPoiGroupIdList(this.poiGroupIdList);
        }

        rpcReq.setNameSupplementInfo(NameSupplementInfoVO.convertToBizDTO(this.getNameSupplementInfo()));

        rpcReq.setMtSpecialPictureList(ConverterUtils.convertList(this.getMtSpecialPictureList(),SpecialPictureVO::toDTO));
        return SaleAttrCompatUtils.compat(rpcReq);
    }

    private List<SubmitTenantProductReviewRequest.SkuInfo> parseSkuFromNewClient() {
        return Fun.map(this.getSkuList(), httpSku -> {
            SubmitTenantProductReviewRequest.SkuInfo rpcSkuInfo = new SubmitTenantProductReviewRequest.SkuInfo();
            rpcSkuInfo.setUpcList(Fun.map(httpSku.getUpcList(), StringUtils::trim));
            rpcSkuInfo.setBaseUnit(StringUtils.trim(httpSku.getBaseUnit()));
            rpcSkuInfo.setWeight(httpSku.getWeight() != null ? httpSku.getWeight() : 0);
            rpcSkuInfo.setWeightForUnit(httpSku.getWeightForUnit() != null ? httpSku.getWeightForUnit() : "0");
            rpcSkuInfo.setWeightUnit(httpSku.getWeightUnit() != null ? httpSku.getWeightUnit() : "克(g)");

            if (CollectionUtils.isNotEmpty(httpSku.getJdAttrValues())) {
                rpcSkuInfo.setJdSaleAttrValueList(httpSku.getJdAttrValues().stream()
                        .map(SaleAttrValueVo::convertBizDTO)
                        .collect(Collectors.toList()));
            }

            rpcSkuInfo.setSuggestPrice(httpSku.getSuggestPrice() != null ? PriceUtils.yuan2Fen(httpSku.getSuggestPrice()) : 0L);
            rpcSkuInfo.setSpecName(httpSku.getSpecName());
            if (httpSku.getEnableExpiredCheck() != null) {
                rpcSkuInfo.setEnableExpiredCheck(httpSku.getEnableExpiredCheck());
            }
            if (httpSku.getOriginPlace() != null) {
                rpcSkuInfo.setOriginPlace(httpSku.getOriginPlace());
            }
            if (httpSku.getExpirationDays() != null) {
                rpcSkuInfo.setExpirationDays(httpSku.getExpirationDays());
            }
            rpcSkuInfo.setExternalCode(httpSku.getExternalCode());
            rpcSkuInfo.setFranchiseeHeadquartersCode(httpSku.getFranchiseeHeadquartersCode());
            rpcSkuInfo.setPurchasePlatformCode(httpSku.getPurchasePlatformCode());
            if (CollectionUtils.isNotEmpty(httpSku.getCartonMeasureConvertFactorList())) {
                rpcSkuInfo.setCartonMeasureList(Fun.map(httpSku.getCartonMeasureConvertFactorList(), CartonMeasureConvertFactorVO::toBizDTO));
            }
            if (httpSku.getCustomizeNoUpcCode() != null) {
                rpcSkuInfo.setCustomizeNoUpcCode(httpSku.getCustomizeNoUpcCode());
            }
            else {
                rpcSkuInfo.setCustomizeNoUpcCode(0);
            }

            if (httpSku.getPurchaseInfo() != null) {
                rpcSkuInfo.setSupplyPurchase(httpSku.getPurchaseInfo().toDTO());
            }
            rpcSkuInfo.setUseSkuAsUpc(httpSku.getUseSkuAsUpc());
            rpcSkuInfo.setChannelSaleAttributeList(Fun.map(httpSku.getChannelSaleAttrValueInfoList(),
                    channelSaleAttrValueInfoVO -> channelSaleAttrValueInfoVO.toBizDTO(httpSku.getImageInfo())));

            rpcSkuInfo.setChannelSkuAttrValueInfoList(ChannelSkuAttrValueInfoVo.toDtoList(httpSku.getChannelSkuAttrValueInfoList()));
            rpcSkuInfo.setSkuSaleType(httpSku.getSkuSaleType());
            if (CollectionUtils.isNotEmpty(httpSku.getChildSkuList())) {
                rpcSkuInfo.setChildSkuList(JacksonUtils.convertList(httpSku.getChildSkuList(), CombineChildSkuDto.class));
            }
            return rpcSkuInfo;
        });
    }

    private List<SubmitTenantProductReviewRequest.SkuInfo> parseSkuFromOldClient() {
        SubmitTenantProductReviewRequest.SkuInfo rpcSkuInfo = new SubmitTenantProductReviewRequest.SkuInfo();
        if (StringUtils.isNotEmpty(this.getUpc())) {
            rpcSkuInfo.setUpcList(Collections.singletonList(this.getUpc().trim()));
        }
        rpcSkuInfo.setBaseUnit(this.getBaseUnit().trim());
        rpcSkuInfo.setWeight(this.getWeight());
        rpcSkuInfo.setWeightForUnit(this.getWeightForUnit());
        rpcSkuInfo.setWeightUnit(this.getWeightUnit());
        if (CollectionUtils.isNotEmpty(this.jdAttrValues)) {
            rpcSkuInfo.setJdSaleAttrValueList(this.jdAttrValues.stream()
                    .map(SaleAttrValueVo::convertBizDTO)
                    .collect(Collectors.toList()));
        }
        rpcSkuInfo.setSuggestPrice(this.suggestPrice != null ? PriceUtils.yuan2Fen(this.suggestPrice) : 0L);
        rpcSkuInfo.setSpecName(this.getSpecName());
        if (this.getEnableExpiredCheck() != null) {
            rpcSkuInfo.setEnableExpiredCheck(this.getEnableExpiredCheck());
        }
        if (this.getOriginPlace() != null) {
            rpcSkuInfo.setOriginPlace(this.getOriginPlace());
        }
        if (this.getExpirationDays() != null) {
            rpcSkuInfo.setExpirationDays(this.getExpirationDays());
        }
        rpcSkuInfo.setExternalCode(this.getExternalCode());
        rpcSkuInfo.setFranchiseeHeadquartersCode(this.getFranchiseeHeadquartersCode());
        rpcSkuInfo.setPurchasePlatformCode(this.getPurchasePlatformCode());
        if (CollectionUtils.isNotEmpty(this.getCartonMeasureConvertFactorList())) {
            rpcSkuInfo.setCartonMeasureList(Fun.map(this.getCartonMeasureConvertFactorList(), CartonMeasureConvertFactorVO::toBizDTO));
        }
        if (this.customizeNoUpcCode != null) {
            rpcSkuInfo.setCustomizeNoUpcCode(this.customizeNoUpcCode);
        }
        else {
            rpcSkuInfo.setCustomizeNoUpcCode(0);
        }

        rpcSkuInfo.setChannelSkuAttrValueInfoList(ChannelSkuAttrValueInfoVo.toDtoList(this.getChannelSkuAttrValueInfoList()));

        return Collections.singletonList(rpcSkuInfo);
    }
}