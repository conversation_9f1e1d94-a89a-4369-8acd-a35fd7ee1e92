package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: liujianghua02
 * @date: 2020-07-22 15:46
 */
@TypeDoc(
        description = "根据SPUID查询租户商品信息请求参数",
        authors = {"liujianghua02"}
)
@Data
@ApiModel("根据SPUID查询租户商品信息")
public class GetTenantSpuBySpuIdRequest {
    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商品spu编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品spu编码")
    private String spuId;
}
