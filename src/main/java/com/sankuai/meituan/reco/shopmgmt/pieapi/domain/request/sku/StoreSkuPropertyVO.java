package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ProductPropertyDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StoreSkuPropertyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "商品属性"
)
@Data
@ApiModel("商品属性")
@NoArgsConstructor

public class StoreSkuPropertyVO {

    @FieldDoc(
            description = "属性名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "属性名称", required = true)
    private String propertyName;

    @FieldDoc(
            description = "属性值，不超过十个", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "属性值", required = true)
    private List<String> propertyValues;

    public StoreSkuPropertyDTO buildStoreSkuPropertyDTO(){
        StoreSkuPropertyDTO storeSkuPropertyDTO = new StoreSkuPropertyDTO();
        storeSkuPropertyDTO.setPropertyName(this.getPropertyName());
        storeSkuPropertyDTO.setPropertyValues(this.getPropertyValues());
        return storeSkuPropertyDTO;
    }

    public StoreSkuPropertyVO(StoreSkuPropertyDTO storeSkuPropertyDTO){
        this.propertyName = storeSkuPropertyDTO.getPropertyName();
        this.propertyValues = storeSkuPropertyDTO.getPropertyValues();
    }

    public StoreSkuPropertyVO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuPropertyDTO storeSkuPropertyDTO) {
        this.propertyName = storeSkuPropertyDTO.getPropertyName();
        this.propertyValues = storeSkuPropertyDTO.getPropertyValues();
    }

    public StoreSkuPropertyVO(ProductPropertyDTO productPropertyDTO){
        this.propertyName = productPropertyDTO.getPropertyName();
        this.propertyValues = productPropertyDTO.getPropertyValues();
    }

    public ProductPropertyDTO buildProductPropertyDTO(){
        ProductPropertyDTO productPropertyDTO = new ProductPropertyDTO();
        productPropertyDTO.setPropertyName(this.getPropertyName());
        productPropertyDTO.setPropertyValues(this.getPropertyValues());
        return productPropertyDTO;
    }

    public com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuPropertyDTO buildProductSkutPropertyDTO() {
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuPropertyDTO storeSkuPropertyDTO =
                new com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuPropertyDTO();
        storeSkuPropertyDTO.setPropertyName(this.getPropertyName());
        storeSkuPropertyDTO.setPropertyValues(this.getPropertyValues());
        return storeSkuPropertyDTO;

    }
}
