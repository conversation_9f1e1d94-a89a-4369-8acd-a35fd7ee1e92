package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;


@TypeDoc(
        description = "查询渠道推荐品牌输入参数"
)
@Data
@ApiModel("查询渠道推荐品牌输入参数")
@ToString
public class RecommendBrandRequest {
    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道id")
    private List<Integer> channelIds;

    @FieldDoc(
            description = "商品名称)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称")
    private String name;

}
