package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/2/4
 */
@TypeDoc(
        description = "拣货缺货处理配置"
)
@ApiModel("拣货缺货处理配置")
@Data
public class ActionAfterLackConfig {
    @FieldDoc(
            description = "缺货自动退款,0-否,1-是,null表示不设置该值", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "缺货自动退款,0-否,1-是,null表示不设置该值", required = true)
    private Integer lackAutoRefund;
    @FieldDoc(
            description = "缺货自动售罄,0-否,1-是,null表示不设置该值", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "缺货自动售罄,0-否,1-是,null表示不设置该值", required = true)
    private Integer lackAutoSoldOut;
}
