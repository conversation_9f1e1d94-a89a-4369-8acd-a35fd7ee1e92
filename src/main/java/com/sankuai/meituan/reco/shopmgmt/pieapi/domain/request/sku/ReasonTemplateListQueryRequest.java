package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryReasonTemplateListRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 获取审核拒绝/通过原因模板列表
 * @author: WangSukuan
 * @create: 2020-03-11
 **/
@TypeDoc(
        description = "获取审核拒绝/通过原因模板请求"
)
@Data
@ApiModel("获取审核原因模板请求")
public class ReasonTemplateListQueryRequest {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "原因模版类型：1-驳回，2-通过", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "原因模版类型")
    private Integer reasonType;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "页码")
    private Integer pageNum = 1;

    @FieldDoc(
            description = "每页大小", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "每页大小")
    private Integer pageSize = 10;



    public QueryReasonTemplateListRequest convertQueryReasonTemplateListRequest(User user){

        QueryReasonTemplateListRequest queryReasonTemplateListRequest = new QueryReasonTemplateListRequest();
        queryReasonTemplateListRequest.setTenantId(this.getTenantId());
        queryReasonTemplateListRequest.setReasonType(this.getReasonType() == null ? 1 : this.getReasonType());
        queryReasonTemplateListRequest.setPageNum(this.getPageNum());
        queryReasonTemplateListRequest.setPageSize(this.getPageSize());
        queryReasonTemplateListRequest.setOperateId(user.getAccountId());
        queryReasonTemplateListRequest.setOperateName(user.getOperatorName());
        return queryReasonTemplateListRequest;

    }

}
