package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp;

import org.apache.commons.lang3.StringUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ERP门店商品相关属性同步请求参数
 *
 * <AUTHOR>
 * @since 2023/05/12
 */
@TypeDoc(description = "ERP门店商品相关属性同步请求参数")
@Data
@ApiModel(description = "ERP门店商品相关属性同步请求参数")
public class ErpStoreSpuSyncStatusRequest {

    @FieldDoc(description = "门店ID", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(name = "门店ID")
    private Long storeId;

    @FieldDoc(description = "SPU编码", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(name = "SPU编码")
    private String spuId;

    public void validate() {
        if (this.storeId == null) {
            throw new ParamException("门店ID不能为空");
        }
        if (StringUtils.isBlank(this.spuId)) {
            throw new ParamException("SPU编码不能为空");
        }
    }
}
