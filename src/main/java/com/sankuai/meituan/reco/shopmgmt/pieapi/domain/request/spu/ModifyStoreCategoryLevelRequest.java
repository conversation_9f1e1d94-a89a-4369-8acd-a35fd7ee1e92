package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreCategoryDegradeRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreCategoryUpgradeRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.enums.StoreCategorySceneIdentityEnum;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category.UpdateMerchantStoreCategoryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */

@Data
@ApiModel("修改门店店内分类等级请求")
public class ModifyStoreCategoryLevelRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分类ID", required = true)
    private String categoryId;

    @FieldDoc(
            description = "层级，1一级分类，2二级分类", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "层级，1一级分类，2二级分类", required = true)
    private Integer level;

    @FieldDoc(
            description = "父分类id，调整为1级分类时传0", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "父分类id，调整为1级分类时传0", required = true)
    private String parentCategoryId;

    public void validate() {
        Preconditions.checkNotNull(level);
        Preconditions.checkArgument(StringUtils.isNotBlank(categoryId) && StringUtils.isNumeric(categoryId)
                && Long.parseLong(categoryId) > 0);
        if (level == 1) {
            Preconditions.checkArgument(StringUtils.isNotBlank(parentCategoryId)
                    && StringUtils.isNumeric(parentCategoryId) && Long.parseLong(parentCategoryId) == 0);
        } else if (level == 2) {
            Preconditions.checkArgument(StringUtils.isNotBlank(parentCategoryId)
                    && StringUtils.isNumeric(parentCategoryId) && Long.parseLong(parentCategoryId) > 0);
        } else {
            throw new IllegalArgumentException("不支持的层级调整");
        }
    }

    public UpdateMerchantStoreCategoryRequest toPoiRpcReq(User user) {
        UpdateMerchantStoreCategoryRequest rpcReq = new UpdateMerchantStoreCategoryRequest();
        if (level == 1) {
            rpcReq.setUpdateType(3);
        } else {
            rpcReq.setUpdateType(2);
        }
        rpcReq.setParentCategoryId(Long.valueOf(parentCategoryId));
        rpcReq.setCategoryId(Long.valueOf(categoryId));
        rpcReq.setMerchantId(user.getTenantId());
        rpcReq.setNewStoreGroupId(storeId);
        rpcReq.setOperatorId(user.getAccountId());
        rpcReq.setOperatorAccount(user.getAccountName());
        rpcReq.setSceneIdentity(StoreCategorySceneIdentityEnum.POI.getCode());
        rpcReq.setPushChannel(true);
        return rpcReq;
    }
}
