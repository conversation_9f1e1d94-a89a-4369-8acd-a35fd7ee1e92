package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2025-02-18
 */
@Slf4j
@Data
public class TenantSpuRouteConfig {
    /**
     * 白名单
     */
    private List<String> whiteList = new ArrayList<>();
    /**
     * 黑名单
     */
    private List<String> blackList = new ArrayList<>();

    public boolean routeToBiz(Long tenantId) {
        String tenantIdStr = String.valueOf(tenantId);
        if (whiteList.contains(tenantIdStr)) {
            return true;
        }
        if (blackList.contains(tenantIdStr)) {
            return false;
        }
        return whiteList.contains("*");
    }
}
