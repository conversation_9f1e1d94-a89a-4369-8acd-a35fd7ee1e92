package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import lombok.AllArgsConstructor;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/31 17:32
 **/
@AllArgsConstructor
public enum DrunkHorseWaitToDeliverySubTypeEnum {
    ALL(0, Arrays.asList(DeliveryStatusEnum.INIT, DeliveryStatusEnum.DELIVERY_LAUNCHED, DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER,
            DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_ARRIVED_SHOP, DeliveryStatusEnum.RIDER_TAKEN_GOODS), ""),
    WAIT_TO_SEND_DELIVERY(10, Arrays.asList(DeliveryStatusEnum.DELIVERY_LAUNCHED, DeliveryStatusEnum.INIT), "待发配送"),
    WAIT_TO_RIDER_ACCEPT(20, Collections.singletonList(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER), "待接单"),
    WAIT_TO_TAKE_GOODS(30, Arrays.asList(DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_ARRIVED_SHOP), "待取货"),
    DELIVERING(40, Collections.singletonList(DeliveryStatusEnum.RIDER_TAKEN_GOODS), "配送中");

    private int code;
    private List<DeliveryStatusEnum> deliveryStatusEnumList;

    private String desc;

    public int getCode() {
        return code;
    }

    public List<DeliveryStatusEnum> getDeliveryStatusEnumList() {
        return deliveryStatusEnumList;
    }

    public String getDesc() {
        return desc;
    }

    public static DrunkHorseWaitToDeliverySubTypeEnum enumOf(int subType) {
        for (DrunkHorseWaitToDeliverySubTypeEnum val: values()) {
            if (val.getCode() == subType) {
                return val;
            }
        }

        throw new IllegalArgumentException("非法的subType");
    }
}
