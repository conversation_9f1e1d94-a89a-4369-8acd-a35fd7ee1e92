package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: wanghongzhen
 * @date: 2020-05-11 15:04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchOperateSpuFailInfoVO {
    /**
     * 商品名称
     */
    private String spuName;
    /**
     * 门店名称
     */
    private String poiName;
    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道编码
     */
    private String channelId;

    /**
     * 存在零售价待审核记录
     */
    private boolean hasRetailPriceToReview;

    /**
     * 存在进货价待审核记录
     */
    private boolean hasOfflinePriceToReview;

    /**
     * 存在零售价为0的sku
     */
    private boolean hasZeroRetailPrice;

    /**
     * 存在进货价价为0的sku
     */
    private boolean hasZeroOfflinePrice;

    /**
     * 失败原因
     */
    private String errorMsg;

    /**
     * spuId
     */
    private String spuId;
}
