package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/7/17 5:13 PM
 */
public class MaskUtils {

    /**
     * 手机号显示首3末4位，中间用*号隐藏代替，如：188****5593
     *
     * @param mobile
     * @return
     */
    public static String maskMobile(String mobile) {
        if (StringUtils.isBlank(mobile) || mobile.length() <= 8) {
            return mobile;
        }
        return wordMask(mobile, 3, 4, "*");
    }

    /**
     * 对字符串进行脱敏处理 --
     *
     * @param word        被脱敏的字符
     * @param startLength 被保留的开始长度 前余n位
     * @param endLength   被保留的结束长度 后余n位
     * @param pad         填充字符
     */
    public static String wordMask(String word, int startLength, int endLength, String pad) {
        if (startLength + endLength > word.length()) {
            return StringUtils.leftPad("", word.length() - 1, pad);
        }
        String startStr = word.substring(0, startLength);
        String endStr = word.substring(word.length() - endLength, word.length());
        return startStr + StringUtils.leftPad("", word.length() - startLength - endLength, pad) + endStr;
    }

}
