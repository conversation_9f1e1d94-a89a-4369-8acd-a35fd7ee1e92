package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;

import com.meituan.linz.product.channel.EnhanceChannelType;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSaleAttributeValueDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.TenantSkuDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ErpStoreSkuDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SkuChannelSaleAttributeValueDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuDetailDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSkuBizDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.review.ReviewTenantSkuDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-12-26
 */
@TypeDoc(
        description = "SKU图片信息"
)
@Data
@ApiModel("SKU图片信息")
public class SkuImageInfo {
    @FieldDoc(
            description = "非京东规格图片URL", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "非京东规格图片URL")
    private List<String> imageUrls;
    @FieldDoc(
            description = "京东规格图片URL", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "京东规格图片URL")
    private List<String> jdImageUrls;

    public static SkuImageInfo of(TenantSkuDTO tenantSkuDTO) {
        SkuImageInfo skuImageInfo = new SkuImageInfo();

        if (tenantSkuDTO.getChannelSaleAttributeValues() == null) {
            return skuImageInfo;
        }

        buildFromOcms(tenantSkuDTO.getChannelSaleAttributeValues(), skuImageInfo);

        return skuImageInfo;
    }

    private static void buildFromOcms(List<ChannelSaleAttributeValueDTO> channelSaleAttributeValues, SkuImageInfo skuImageInfo) {
        channelSaleAttributeValues.stream()
                .filter(channelSaleAttributeValueDTO -> channelSaleAttributeValueDTO.getChannelId() != EnhanceChannelType.JDDJ.getChannelId())
                .map(ChannelSaleAttributeValueDTO::getSaleAttributeValues)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(saleAttributeValueDTO -> CollectionUtils.isNotEmpty(saleAttributeValueDTO.getRelatedImageUrls()))
                .findAny()
                .ifPresent(saleAttributeValueDTO -> skuImageInfo.setImageUrls(saleAttributeValueDTO.getRelatedImageUrls()));

        channelSaleAttributeValues.stream()
                .filter(channelSaleAttributeValueDTO -> channelSaleAttributeValueDTO.getChannelId() == EnhanceChannelType.JDDJ.getChannelId())
                .findAny()
                .ifPresent(channelSaleAttributeValueDTO -> skuImageInfo.setJdImageUrls(channelSaleAttributeValueDTO.getSkuImageUrls()));

    }

    public static SkuImageInfo of(com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSkuDTO tenantSkuDTO) {
        SkuImageInfo skuImageInfo = new SkuImageInfo();

        if (tenantSkuDTO.getChannelSaleAttrValueList() == null) {
            return skuImageInfo;
        }

        buildFromBiz(tenantSkuDTO.getChannelSaleAttrValueList(), skuImageInfo);
        return skuImageInfo;
    }

    public static SkuImageInfo of(TenantSkuBizDTO tenantSkuBizDTO) {
        SkuImageInfo skuImageInfo = new SkuImageInfo();

        if (tenantSkuBizDTO.getChannelSaleAttrValueList() == null) {
            return skuImageInfo;
        }

        buildFromBiz(tenantSkuBizDTO.getChannelSaleAttrValueList(), skuImageInfo);
        return skuImageInfo;
    }

    private static void buildFromBiz(List<SkuChannelSaleAttributeValueDTO> channelSaleAttributeValues, SkuImageInfo skuImageInfo) {
        channelSaleAttributeValues.stream()
                .filter(channelSaleAttributeValueDTO -> channelSaleAttributeValueDTO.getChannelId() != EnhanceChannelType.JDDJ.getChannelId())
                .map(SkuChannelSaleAttributeValueDTO::getSkuSaleAttributeValues)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(saleAttributeValueDTO -> CollectionUtils.isNotEmpty(saleAttributeValueDTO.getRelatedImageUrls()))
                .findAny()
                .ifPresent(saleAttributeValueDTO -> skuImageInfo.setImageUrls(saleAttributeValueDTO.getRelatedImageUrls()));

        channelSaleAttributeValues.stream()
                .filter(channelSaleAttributeValueDTO -> channelSaleAttributeValueDTO.getChannelId() == EnhanceChannelType.JDDJ.getChannelId())
                .findAny()
                .ifPresent(channelSaleAttributeValueDTO -> skuImageInfo.setJdImageUrls(channelSaleAttributeValueDTO.getSkuImageUrls()));
    }

    public static SkuImageInfo of(StoreSkuDTO storeSkuDTO) {
        SkuImageInfo skuImageInfo = new SkuImageInfo();

        if (storeSkuDTO.getChannelSaleAttributeValues() == null) {
            return skuImageInfo;
        }

        buildFromBiz(storeSkuDTO.getChannelSaleAttributeValues(), skuImageInfo);
        return skuImageInfo;
    }

    public static SkuImageInfo of(com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreSkuDTO storeSkuDTO) {
        SkuImageInfo skuImageInfo = new SkuImageInfo();

        if (storeSkuDTO.getChannelSaleAttributeValues() == null) {
            return skuImageInfo;
        }

        buildFromOcms(storeSkuDTO.getChannelSaleAttributeValues(), skuImageInfo);
        return skuImageInfo;
    }

    public static SkuImageInfo of(ErpStoreSkuDTO erpStoreSkuDTO) {
        SkuImageInfo skuImageInfo = new SkuImageInfo();
        if (CollectionUtils.isEmpty(erpStoreSkuDTO.getChannelSaleAttributeValues())) {
            return skuImageInfo;
        }
        buildFromBiz(erpStoreSkuDTO.getChannelSaleAttributeValues(), skuImageInfo);
        return skuImageInfo;

    }

    public static SkuImageInfo of(StoreSkuDetailDTO storeSkuDetailDTO) {
        SkuImageInfo skuImageInfo = new SkuImageInfo();
        if (CollectionUtils.isEmpty(storeSkuDetailDTO.getSkuChannelSaleAttributeValues())) {
            return skuImageInfo;
        }
        buildFromBiz(storeSkuDetailDTO.getSkuChannelSaleAttributeValues(), skuImageInfo);
        return skuImageInfo;
    }

    public static SkuImageInfo of(ReviewTenantSkuDTO reviewTenantSkuDTO) {
        SkuImageInfo skuImageInfo = new SkuImageInfo();
        if (CollectionUtils.isEmpty(reviewTenantSkuDTO.getChannelSaleAttributeValues())) {
            return skuImageInfo;
        }
        buildFromBiz(reviewTenantSkuDTO.getChannelSaleAttributeValues(), skuImageInfo);
        return skuImageInfo;
    }
}
