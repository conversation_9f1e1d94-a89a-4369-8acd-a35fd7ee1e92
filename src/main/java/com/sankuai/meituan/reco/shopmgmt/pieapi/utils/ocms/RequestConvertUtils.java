package com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ocms;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SupplyRelationAndPurchaseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.client.common.dto.PageQueryDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelStoreFrontCategoryFactor;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreSpuQueryByStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.StoreSpuPageQueryByStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.StoreSpuCountRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/5/15
 * desc: 中台request转换工具类
 */
@Slf4j
public class RequestConvertUtils {

    //未上架
    private static final int statusOfOffSale = 1;
    //未报价
    private static final int statusOfUnquoted = 2;

    //未报价
    private static final int statusOfOCMSUnquoted = 3;
    //未上架
    private static final int StatusOfOCMSOffLine = 2;

    public static BatchUpdateStoreStockRequest convertRequest(UpdateStockRequest request, User user, boolean enableMultiLocation, Map<String, Boolean> skuBatchEnableSwitchMap) {
        BatchUpdateStoreStockRequest batchUpdateStoreStockRequest = new BatchUpdateStoreStockRequest();
        if(Objects.nonNull(user.getTenantId())){
            batchUpdateStoreStockRequest.setTenantId(user.getTenantId());
        }
        batchUpdateStoreStockRequest.setStoreSkuSaleStockList(convertStoreSkuSaleStockDTOList(request.getSkuStockList(),request.getStoreId(), enableMultiLocation, skuBatchEnableSwitchMap));
        if(Objects.nonNull(user.getEmployeeId())){
            batchUpdateStoreStockRequest.setOperatorId(user.getEmployeeId());
        }
        if(Objects.nonNull(user.getOperatorName())){
            batchUpdateStoreStockRequest.setOperatorName(user.getOperatorName());
        }
        batchUpdateStoreStockRequest.setComment(request.getComment());

        if(Objects.nonNull(request.getAdjustType())){
            batchUpdateStoreStockRequest.setAdjustType(request.getAdjustType());
        }
        return batchUpdateStoreStockRequest;
    }

    public static StockSyncChannelRequest convertToStockSyncRequest(UpdateStockRequest request, User user) {
        StockSyncChannelRequest stockSyncChannelRequest = new StockSyncChannelRequest();
        stockSyncChannelRequest.setTenantId(user.getTenantId());
        stockSyncChannelRequest.setOperatorId(user.getEmployeeId());
        stockSyncChannelRequest.setOperatorName(user.getOperatorName());
        stockSyncChannelRequest.setStoreId(request.getStoreId());
        stockSyncChannelRequest.setSkuIds(ConverterUtils.convertList(request.getSkuStockList(), StoreSkuStockVO::getSkuId));
        return stockSyncChannelRequest;
    }

    private static List<StoreSkuSaleStockDTO> convertStoreSkuSaleStockDTOList(List<StoreSkuStockVO> skuStockList, Long storeId, boolean enableMultiLocation, Map<String, Boolean> skuBatchEnableSwitchMap){
        if(Objects.isNull(skuStockList))
            return Lists.newArrayList();
        List<StoreSkuSaleStockDTO> storeSkuSaleStockDTOList = Lists.newArrayList();
        for(StoreSkuStockVO storeSkuStockVO:skuStockList) {
            StoreSkuSaleStockDTO storeSkuSaleStockDTO = new StoreSkuSaleStockDTO();
            if(Objects.nonNull(storeId)){
                storeSkuSaleStockDTO.setStoreId(storeId);
            }
            storeSkuSaleStockDTO.setQuantity(storeSkuStockVO.getStock());
            storeSkuSaleStockDTO.setSkuCode(storeSkuStockVO.getSkuId());
            if(Objects.nonNull(storeSkuStockVO.getCustomizeStockFlag())){
                storeSkuSaleStockDTO.setCustomizeStockFlag(storeSkuStockVO.getCustomizeStockFlag());
            }
            if(Objects.nonNull(storeSkuStockVO.getAutoResumeInfiniteStock())){
                storeSkuSaleStockDTO.setAutoResumeInfiniteStock(storeSkuStockVO.getAutoResumeInfiniteStock());
            }
            // 一品多位或批次,不调整库存
            if (enableMultiLocation || skuBatchEnableSwitchMap.getOrDefault(storeSkuStockVO.getSkuId(), false)) {
                storeSkuSaleStockDTO.setIsAdjustStockQuantity(false);
            }
            storeSkuSaleStockDTOList.add(storeSkuSaleStockDTO);
        }
        return storeSkuSaleStockDTOList;
    }
    //查询已上架已下架商品数
    public static StoreSpuCountRequest convertStoreSpuCountRequest(QueryOnSaleAndOffSaleCountRequest request,User user, String boothId){
        StoreSpuCountRequest storeSpuCountRequest = new StoreSpuCountRequest();
        StoreSpuQueryByStatusDTO storeSpuQueryByStatusDTO = new StoreSpuQueryByStatusDTO();
        storeSpuQueryByStatusDTO.setTenantId(user.getTenantId());
        storeSpuQueryByStatusDTO.setStoreId(request.getStoreId());
        if(Objects.nonNull(request.getScope())){
            storeSpuQueryByStatusDTO.setScope(request.getScope());
        }
        if(Objects.nonNull(request.getHasStoreCategory())){
            storeSpuQueryByStatusDTO.setHasStoreCategory(request.getHasStoreCategory());
        }
        if (MapUtils.isNotEmpty(request.getChannelStoreCategoryMap())) {
            List<ChannelStoreFrontCategoryFactor> frontCategoryFactorList = Lists.newArrayList();
            request.getChannelStoreCategoryMap().forEach((channelId, frontCategory)->{
                ChannelStoreFrontCategoryFactor channelStoreFrontCategoryFactor = new ChannelStoreFrontCategoryFactor();
                channelStoreFrontCategoryFactor.setStoreId(request.getStoreId());
                channelStoreFrontCategoryFactor.setChannelId(channelId);
                channelStoreFrontCategoryFactor.setFrontCategoryId(Long.parseLong(frontCategory));

                frontCategoryFactorList.add(channelStoreFrontCategoryFactor);
            });
            storeSpuQueryByStatusDTO.setFrontCategoryList(frontCategoryFactorList);
        }
        if (Strings.isNotEmpty(boothId)) {
            storeSpuQueryByStatusDTO.setBoothIds(Arrays.asList(Long.valueOf(boothId)));
        }
        //DTO中状态信息，具体请求时在赋值
        storeSpuCountRequest.setStoreSpuQueryByStatusDTO(storeSpuQueryByStatusDTO);
        return storeSpuCountRequest;
    }
    //按天查未报价/未上架商品request转换
    public static StoreSpuPageQueryByStatusRequest convertStoreSpuPageQueryByStatus(PageQueryOffSaleAndUnquotedRequest request,User user){
        StoreSpuPageQueryByStatusRequest storeSpuPageQueryByStatusRequest = new StoreSpuPageQueryByStatusRequest();
        StoreSpuQueryByStatusDTO storeSpuQueryByStatusDTO = new StoreSpuQueryByStatusDTO();
        PageQueryDTO pageQueryDTO = new PageQueryDTO();
        storeSpuQueryByStatusDTO.setTenantId(user.getTenantId());
        storeSpuQueryByStatusDTO.setStoreId(request.getStoreId());
        storeSpuQueryByStatusDTO.setHasStoreCategory(request.getHasStoreCategory());
        if(Objects.nonNull(request.getQueryType())){
            if(request.getQueryType()==statusOfOffSale)
            storeSpuQueryByStatusDTO.setStatus(StatusOfOCMSOffLine);
        }
        if (MapUtils.isNotEmpty(request.getChannelStoreCategoryMap())) {
            List<ChannelStoreFrontCategoryFactor> frontCategoryFactorList = Lists.newArrayList();
            request.getChannelStoreCategoryMap().forEach((channelId, frontCategory)->{
                ChannelStoreFrontCategoryFactor channelStoreFrontCategoryFactor = new ChannelStoreFrontCategoryFactor();
                channelStoreFrontCategoryFactor.setStoreId(request.getStoreId());
                channelStoreFrontCategoryFactor.setChannelId(channelId);
                channelStoreFrontCategoryFactor.setFrontCategoryId(Long.parseLong(frontCategory));

                frontCategoryFactorList.add(channelStoreFrontCategoryFactor);
            });
            storeSpuQueryByStatusDTO.setFrontCategoryList(frontCategoryFactorList);
        }

        if(Objects.nonNull(request.getPage())){
            pageQueryDTO.setPageNum(request.getPage());
        }
        if(Objects.nonNull(request.getSize())){
            pageQueryDTO.setPageSize(request.getSize());
        }
        if(Objects.nonNull(request.getDays())){
            storeSpuPageQueryByStatusRequest.setDays(request.getDays());
        }
        storeSpuPageQueryByStatusRequest.setPageQueryDTO(pageQueryDTO);
        storeSpuPageQueryByStatusRequest.setStoreSpuQueryByStatusDTO(storeSpuQueryByStatusDTO);
        return storeSpuPageQueryByStatusRequest;
    }

    //按天查未报价/未上架商品总数request转换
    public static StoreSpuCountRequest convertStoreSpuCountRequest(QueryOffSaleAndUnquotedCountRequest request,User user){
        StoreSpuCountRequest storeSpuCountRequest = new StoreSpuCountRequest();
        if(Objects.nonNull(request.getDays())){
            storeSpuCountRequest.setDays(request.getDays());
        }
        storeSpuCountRequest.setStoreSpuQueryByStatusDTO(convertStoreSpuQueryByStatusDTO(request,user));
        return storeSpuCountRequest;
    }

    public static StoreSpuQueryByStatusDTO convertStoreSpuQueryByStatusDTO(QueryOffSaleAndUnquotedCountRequest request,User user){
        StoreSpuQueryByStatusDTO storeSpuQueryByStatusDTO = new StoreSpuQueryByStatusDTO();
        storeSpuQueryByStatusDTO.setTenantId(user.getTenantId());
        storeSpuQueryByStatusDTO.setStoreId(request.getStoreId());
        if(Objects.nonNull(request.getQueryType())){
            switch (request.getQueryType()){
                case statusOfOffSale://未上架-对应ocms中已下架或未上架（code：2）
                    storeSpuQueryByStatusDTO.setStatus(StatusOfOCMSOffLine);
                    break;
                default://未报价——对应ocms中未报价（code：3） //暂时废弃，走sku接口
                    storeSpuQueryByStatusDTO.setStatus(statusOfOCMSUnquoted);
                    break;
            }
        }
        return storeSpuQueryByStatusDTO;
    }

    public static SupplyRelationAndPurchaseSkuReq convertToSupplyRelationAndPurchaseSkuReq(Long storeId, String spuId,
                                                                                    List<SupplyRelationAndPurchaseVO> supplyRelationAndPurchaseVOS) {
        SupplyRelationAndPurchaseSkuReq model = new SupplyRelationAndPurchaseSkuReq();
        model.setPoiId(storeId);
        model.setSpuId(spuId);
        model.instantiateSupplyRelationSetVoList(supplyRelationAndPurchaseVOS);
        model.instantiatePurchaseSkuSetPropertyVos(supplyRelationAndPurchaseVOS);
        return model;
    }
}
