package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "渠道上下架信息"
)
@Data
@ApiModel("渠道上下架信息")
public class ChannelStatusChangeParamVO {

    @FieldDoc(
            description = "渠道ID  -1-线下 100-美团 200-饿了么 300-京东", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "绑定渠道前台分类末级Id   该参数废弃", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "绑定渠道前台分类末级Id")
    private String frontCategoryId;

    @FieldDoc(
            description = "商品状态 1-上架，2-下架", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品状态", required = true)
    @NotNull
    private Integer skuChannelStatus;
}
