package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 非白底图片转白底图片请求入参
 *
 * <AUTHOR>
 * @date 2021-10-18 19:08
 */
@Data
@TypeDoc(
        description = "转白底图片请求入参"
)
@ApiModel("转白底图片请求入参")
public class WhiteBackgroundImageGenRequest {
    @FieldDoc(
            description = "图片地址", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "图片地址", required = true)
    @NotNull(message = "图片地址不能为空")
    private String imgUrl;
    @FieldDoc(
            description = "期望输出图片高度", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "期望输出图片高度", required = true)
    @NotNull(message = "图片高度不能为空")
    private Integer height;
    @FieldDoc(
            description = "期望宽度图片高度", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "期望宽度图片高度", required = true)
    @NotNull(message = "图片宽度不能为空")
    private Integer width;
}
