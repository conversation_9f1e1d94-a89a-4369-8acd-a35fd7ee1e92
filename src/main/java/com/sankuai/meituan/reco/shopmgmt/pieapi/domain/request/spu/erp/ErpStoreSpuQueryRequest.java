package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * ERP门店商品SPU查询请求参数
 *
 * <AUTHOR>
 * @since 2023/05/11
 */
@TypeDoc(
        description = "ERP门店商品SPU查询请求参数"
)
@ApiModel("ERP门店商品SPU查询请求参数")
@Getter
@Setter
@ToString
public class ErpStoreSpuQueryRequest {

    @FieldDoc(description = "门店ID", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(name = "门店ID")
    private Long storeId;

    @FieldDoc(description = "SPU编码列表", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "SPU编码列表")
    private List<String> spuIdList;

    @FieldDoc(description = "关键字搜索参数", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "关键字搜索参数")
    private String keyword;

    @FieldDoc(description = "上下架状态：1-上架 2-下架，3-手动下架", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "上下架状态")
    public List<Integer> saleStatusList;

    @FieldDoc(description = "售卖状态(0-全部 -1-未上线 1-已上架 2-已下架 3-已上线(已上架+已下架)，默认0)", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "售卖状态(0-全部 -1-未上线 1-已上架 2-已下架 3-已上线(已上架+已下架)，默认0)")
    private Integer onlineStatus;

    @FieldDoc(description = "指定渠道编码和onlineStatus组合使用", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "指定渠道编码")
    private List<Integer> channelIdList;

    @FieldDoc(description = "渠道库存  1-售罄 -1-未售罄", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "渠道库存  1-售罄 -1-未售罄")
    private List<Integer> channelStockList;

    @FieldDoc(description = "价格范围，最小价格", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "价格范围，最小价格")
    private String minPrice;

    @FieldDoc(description = "价格范围，最大价格", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "价格范围，最大价格")
    private String maxPrice;

    @FieldDoc(description = "店内分类编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "店内分类编码")
    private List<String> frontCategoryIds;

    @FieldDoc(description = "无店内分类", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "无店内分类")
    private Integer noFrontCategory;

    @FieldDoc(description = "是否需要门店商品美团渠道spu编码: true-需要, false-不需要，默认不需要", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "是否需要门店商品美团渠道spu编码")
    private Boolean needMtChannelSpuId;

    @FieldDoc(description = "是否需要门店商品美团渠道线上价格: true-需要, false-不需要，默认不需要", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "是否需要门店商品美团渠道线上价格")
    private Boolean needMtChannelPrice;

    @FieldDoc(description = "1-筛选全部 2-筛选售卖 3-筛选停售", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "1-筛选全部 2-筛选售卖 3-筛选停售")
    private Integer tabStatus;

    @FieldDoc(
            description = "库存状态" +
                    "1-接库存，使用ERP库存" +
                    "-1-停库存，使用自定义库存", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "1-接库存 -1-停库存")
    private Integer stockStatus;

    public List<Integer> fetchChannelIds() {
        if (CollectionUtils.isEmpty(channelIdList)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(channelIdList);
    }

    public void validate() {
        if (this.storeId == null) {
            throw new ParamException("门店ID不能为空");
        }
    }
}
