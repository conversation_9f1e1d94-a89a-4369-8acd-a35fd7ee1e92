package com.sankuai.meituan.reco.shopmgmt.pieapi.config;

import com.dianping.squirrel.client.impl.redis.RedisClientBuilder;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/7/27 21:05
 **/

@Configuration
public class SquirrelConfigurer {

    @Value("${squirrel.clusterName}")
    private String squirrelClusterName;

    @Value("${squirrel.fnCluster}")
    private String fnClusterName;

    @Bean("wmRedisStoreClient")
    public RedisStoreClient redisStoreClientBean() {
        return new RedisClientBuilder(squirrelClusterName)
                .readTimeout(1000)
                .build();
    }

    @Bean("fnRedisClient")
    public RedisStoreClient fnRedisClientBean() {
        return new RedisClientBuilder(fnClusterName)
                .readTimeout(1000)
                .build();
    }
}
