package com.sankuai.meituan.reco.shopmgmt.pieapi.config;

import com.dianping.cat.Cat;
import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.assertj.core.util.Maps;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;


@Aspect
@Component
@Slf4j
public class ControllerAspect {

    @Resource
    private HttpServletRequest request;

    public Object logResponseCode(ProceedingJoinPoint pjp) throws Throwable {
        Object ret = pjp.proceed();
        if (Objects.isNull(ret)) {
            logResponseCode(request.getRequestURI(), null);
        } else {
            if (ret instanceof CommonResponse) {
                CommonResponse<?> response = (CommonResponse<?>) ret;
                logResponseCode(request.getRequestURI(), response);
            }
        }

        return ret;
    }

    @Around("execution(* com.sankuai.meituan.reco.shopmgmt.pieapi.controller.rider.*Controller.*(..))")
    public Object aroundRiderController(ProceedingJoinPoint pjp) throws Throwable {
        return logResponseCode(pjp);
    }

    public static void logResponseCode(String url, CommonResponse<?> response) {
        if (!StringUtils.startsWith(url, "/pieapi/rider") && !StringUtils.startsWith(url, "/pieapi/delivery/dh/third")) {
            return;
        }
        String code;
        String msg;
        if (response == null) {
            code = "NullResponse";
            msg = "";
        } else {
            code = String.valueOf(response.getCode());
            msg = response.getMessage();
        }

        if (!"0".equals(code)) {
            log.warn("logResponseCode url {} code {} msg {} trace {}", url, code, msg, Tracer.id());
            Cat.logMetricForCount("ErrRespCode_" + url, Maps.newHashMap("code", code));
        } else {
            Cat.logMetricForCount("RespCode_" + url, Maps.newHashMap("code", code));
        }
    }

}