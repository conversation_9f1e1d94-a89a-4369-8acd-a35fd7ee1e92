package com.sankuai.meituan.reco.shopmgmt.pieapi.config;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.DispatcherType;

import com.sankuai.oceanus.http.filter.InfFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.filter.CharacterEncodingFilter;
import org.springframework.web.filter.DelegatingFilterProxy;

import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurityPrepareFilter;
import com.sankuai.meituan.shangou.empower.auth.sdk.filter.AuthFilterFactoryBean;
import com.sankuai.meituan.shangou.empower.auth.sdk.filter.LoginFilterFactoryBean;

/**
 * Created by l<PERSON>jia on 2018/8/15.
 */
@Configuration
public class FilterConfiguration {
    private static final int CHARACTER_ENCODING_FILTER_ORDER = 2;

    /**
     * 确保加载顺序
     * TraceFilter 注意要放到第一个
     */
    private static final int MTRACE_FILTER_ORDER = Ordered.HIGHEST_PRECEDENCE;

    private static final int INF_FILTER = MTRACE_FILTER_ORDER + 1;
    private static final int JMONITOR_LISTENER_ORDER = INF_FILTER + 1;
    private static final int MT_CONTEXT_LISTENER_ORDER = JMONITOR_LISTENER_ORDER + 1;
    private static final int HTTP_MONITOR_FILTER_ORDER = MT_CONTEXT_LISTENER_ORDER + 1;
    private static final int CROS_MONITOR_FILTER_ORDER = HTTP_MONITOR_FILTER_ORDER + 1;
    private static final int LOGIN_FILTER_ORDER = CROS_MONITOR_FILTER_ORDER + 1;
    private static final int CAT_MONITOR_FILTER_ORDER = LOGIN_FILTER_ORDER + 1;
    private static final int ENCODING_MONITOR_FILTER_ORDER = CAT_MONITOR_FILTER_ORDER + 1;
    private static final int AUTH_FILTER_ORDER = ENCODING_MONITOR_FILTER_ORDER + 1;
    private static final int CORS_FILTER_ORDER = AUTH_FILTER_ORDER + 1;

    // 数据鉴权准备 filter，这里放最后面
    private static final int DATA_SECURITY_PREPARE_FILTER_ORDER = 100;

    @Bean
    public FilterRegistrationBean mTraceFilter() {
        // mtrace埋点，收集耗时等信息。
        com.meituan.mtrace.http.TraceFilter filter = new com.meituan.mtrace.http.TraceFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/*");
        registration.setName("mtrace-filter");
        registration.setOrder(MTRACE_FILTER_ORDER);
        return registration;
    }

    @Bean
    public FilterRegistrationBean characterEncodingFilter() {
        CharacterEncodingFilter filter = new CharacterEncodingFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/*");
        registration.setName("SetCharacterEncoding");
        Map<String, String> initParameters = new HashMap<>();
        initParameters.put("encoding", "UTF-8");
        initParameters.put("forceEncoding", "true");
        registration.setInitParameters(initParameters);
        registration.setOrder(CHARACTER_ENCODING_FILTER_ORDER);
        return registration;
    }

    //登录校验filter
    @Bean
    public FilterRegistrationBean loginFilter(){
        DelegatingFilterProxy filter = new DelegatingFilterProxy();
        FilterRegistrationBean registration = new FilterRegistrationBean();
        filter.setTargetBeanName("loginFilterBean");
        filter.setTargetFilterLifecycle(true);

        registration.setFilter(filter);
        registration.addUrlPatterns("/pieapi/account/homepage");
        registration.addUrlPatterns("/pieapi/account/queryconfig");
        registration.addUrlPatterns("/pieapi/account/setconfig");
        registration.addUrlPatterns("/pieapi/account/mobile/sendBindVerificationCode");
        registration.addUrlPatterns("/pieapi/account/mobile/verifyAndBind");
        registration.addUrlPatterns("/pieapi/account/mobile/interface/sendBindVerificationCode");
        registration.addUrlPatterns("/pieapi/account/mobile/interface/verifyAndBind");
        registration.addUrlPatterns("/pieapi/account/deactivationAccount");
        registration.addUrlPatterns("/pieapi/auth/*");
        registration.addUrlPatterns("/pieapi/management/*");
        registration.addUrlPatterns("/pieapi/order/*");
        registration.addUrlPatterns("/pieapi/miniapp/order/*");
        registration.addUrlPatterns("/pieapi/pendingtask/*");
        registration.addUrlPatterns("/pieapi/pick/*");
        registration.addUrlPatterns("/pieapi/securitydeposit/*");
        registration.addUrlPatterns("/pieapi/employee/*");
        registration.addUrlPatterns("/pieapi/ocms/skuAndStock/*");
        registration.addUrlPatterns("/pieapi/push/config/uuid");
        registration.addUrlPatterns("/pieapi/messagecenter/*");
        registration.addUrlPatterns("/pieapi/contract/*");
        registration.addUrlPatterns("/order/settlement/history/booth/*");
        registration.addUrlPatterns("/pieapi/priceservice/*");
        registration.addUrlPatterns("/pieapi/priceTrend/*");
        registration.addUrlPatterns("/pieapi/ocms/store/spu/*");
        registration.addUrlPatterns("/pieapi/ocms/problem/spu/*");
        registration.addUrlPatterns("/pieapi/ocms/spu/region/*");
        registration.addUrlPatterns("/pieapi/ocms/channel/spu/*");
        registration.addUrlPatterns("/pieapi/ocms/tenant/spu/*");
        registration.addUrlPatterns("/pieapi/store/spu/erp/*");
        registration.addUrlPatterns("/pieapi/booth/*");
        registration.addUrlPatterns("/pieapi/productintelligent/*");
        registration.addUrlPatterns("/pieapi/ocms/channel/frontCategorySpu/*");
        registration.addUrlPatterns("/pieapi/price/*");
        registration.addUrlPatterns("/pieapi/realtime/*");
        registration.addUrlPatterns("/pieapi/assistant/task/*");
        registration.addUrlPatterns("/pieapi/delivery/*");
        registration.addUrlPatterns("/pieapi/my/deliverymanagement/*");
        registration.addUrlPatterns("/pieapi/backend-category/*");
        registration.addUrlPatterns("/pieapi/pullnew/*");
        registration.addUrlPatterns("/pieapi/rider/delivery/*");
        registration.addUrlPatterns("/pieapi/rider/manage/*");
        registration.addUrlPatterns("/pieapi/rider/picking/*");
        registration.addUrlPatterns("/pieapi/appmodel/*");
        registration.addUrlPatterns("/pieapi/ocms/brand/*");
        // /pieapi/tenant/* 下有非登录能使用的接口，这里单独进行配置
        registration.addUrlPatterns("/pieapi/tenant/bizMode");
        registration.addUrlPatterns("/pieapi/tenant/chainRelation");
        registration.addUrlPatterns("/pieapi/tenant/hsPurchaseShopInfo");
        registration.addUrlPatterns("/pieapi/tenant/queryPoiByWarehouseId");
        registration.addUrlPatterns("/pieapi/tenant/batchQueryTenantConfig");
        registration.addUrlPatterns("/pieapi/tenant/batchQueryPoiConfig");
        registration.addUrlPatterns("/pieapi/tenant/batchQueryTenantChannelConfig");
        registration.addUrlPatterns("/pieapi/tenant/batchQueryPoiChannelConfig");
        registration.addUrlPatterns("/pieapi/tenant/aggTenantLevelConfig");
        registration.addUrlPatterns("/pieapi/tenant/getTenantType");
        registration.addUrlPatterns("/pieapi/tenant/suggestPriceCoverageStoreSwitch");
        registration.addUrlPatterns("/pieapi/tenant/queryTenantStoreSelfPropertiesConfig");
        registration.addUrlPatterns("/pieapi/tenant/channels/*");
        registration.addUrlPatterns("/pieapi/rider/locatingLog/postLocatingException");
        registration.addUrlPatterns("/pieapi/merchant/storeCategory/*");
        registration.addUrlPatterns("/pieapi/image/*");
        registration.addUrlPatterns("/pieapi/poi/*");
        registration.addUrlPatterns("/pieapi/store/frontCategory/*");
        registration.addUrlPatterns("/pieapi/common/area/*");
        registration.addUrlPatterns("/pieapi/warehouse/pick/*");
        registration.addUrlPatterns("/pieapi/rider/delivery-statistic/*");
        registration.addUrlPatterns("/pieapi/labor/*");
        registration.addUrlPatterns("/pieapi/consumable/*");
        registration.addUrlPatterns("/pieapi/regionselect/*");
        registration.addUrlPatterns("/pieapi/department/*");
        registration.addUrlPatterns("/pieapi/notice/*");
        registration.addUrlPatterns("/pieapi/customer/*");
        registration.addUrlPatterns("/pieapi/common/sn/*");
        registration.addUrlPatterns("/pieapi/tenantbill/*");

        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setName("loginFilter");
        registration.addInitParameter("excludedUris","/pieapi/contract/storeServicePeriod/download");//需要排除的uri
        registration.setOrder(LOGIN_FILTER_ORDER);
        return registration;
    }

    @Bean
    public FilterRegistrationBean corsFilter() {
        CorsFilter filter = new CorsFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/*");
        registration.setName("cors-filter");
        registration.setOrder(CORS_FILTER_ORDER);
        return registration;
    }

    //登录factoryBean 和相关配置
    @Bean
    public LoginFilterFactoryBean loginFilterBean(){
        LoginFilterFactoryBean loginFilterFactoryBean = new LoginFilterFactoryBean();
        loginFilterFactoryBean.setSecret("");//账号系统分配
        loginFilterFactoryBean.setLogEnable(true);//info日志打印
        loginFilterFactoryBean.setExcludedUriList("/api/account/**,/pieapi/contract/storeServicePeriod/download");//不校验配置urlList（ANT风格） 逗号分隔
        loginFilterFactoryBean.setIncludedUriList("");//校验配置urlList（ANT风格） 逗号分隔,优先级高于exludedUriList，同时配置则只有include生效
        return loginFilterFactoryBean;
    }

    //鉴权filter
    @Bean
    public FilterRegistrationBean authFilter(){
        DelegatingFilterProxy filter = new DelegatingFilterProxy();
        FilterRegistrationBean registration = new FilterRegistrationBean();
        filter.setTargetBeanName("authFilterBean");
        filter.setTargetFilterLifecycle(true);

        registration.setFilter(filter);
        // 接口鉴权 url
        // 注：自 2024.03 月后，开启新的鉴权模式，新增接口默认需要鉴权，所有接口都需要走鉴权拦截器。拦截器内部会处理无需鉴权的接口
        registration.addUrlPatterns("/*");
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setName("authFilter");

        registration.setOrder(AUTH_FILTER_ORDER);
        return registration;
    }

    //鉴权factorybean 和相关配置
    @Bean
    public AuthFilterFactoryBean authFilterBean(){
        AuthFilterFactoryBean authFilterFactoryBean = new AuthFilterFactoryBean();

        // 账号相关的接口不需要做功能鉴权。此为老模式下的配置，勿动，开启新模式后不再推荐使用
        String excludedUriList = "/monitor/alive,/pieapi/contract/storeServicePeriod/download";
        authFilterFactoryBean.setExcludedUriList(excludedUriList);//不校验配置urlList（ANT风格） 逗号分隔
        authFilterFactoryBean.setLogEnable(true);//info日志打印
        authFilterFactoryBean.setAuthFailedResponse("{\"code\": 402,\"message\": \"认证不通过\"}");//校验不通过给前端返回信息,

        // 开启新的鉴权模式：新增的API的会进行鉴权
        authFilterFactoryBean.setEnableNewMode(Boolean.TRUE);
        // 截止2024.03月，历史已经在进行鉴权的URL配置。注：此部分配置用来在新的鉴权模式下进行兼容性处理，历史开启鉴权的，新模式下仍然开启鉴权
        authFilterFactoryBean.addPathPatterns("/pieapi/account/deactivationAccount");
        authFilterFactoryBean.addPathPatterns("/pieapi/order/*");
        authFilterFactoryBean.addPathPatterns("/pieapi/pendingtask/*");
        authFilterFactoryBean.addPathPatterns("/pieapi/pick/*");
        authFilterFactoryBean.addPathPatterns("/pieapi/employee/*");
        authFilterFactoryBean.addPathPatterns("/pieapi/securitydeposit/list");
        authFilterFactoryBean.addPathPatterns("/pieapi/push/config/uuid");
        authFilterFactoryBean.addPathPatterns("/pieapi/priceservice/*");
        authFilterFactoryBean.addPathPatterns("/pieapi/price/*");
        authFilterFactoryBean.addPathPatterns("/pieapi/realtime/*");

        return authFilterFactoryBean;
    }


    @Bean
    public FilterRegistrationBean dataSecurityPrepareFilter() {
        DataSecurityPrepareFilter filter = new DataSecurityPrepareFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/*");
        registration.setName("data-security-prepare-filter");
        registration.setOrder(DATA_SECURITY_PREPARE_FILTER_ORDER);
        return registration;
    }

    @Bean
    public FilterRegistrationBean oceanusFilterRegistrationBean() {
        // 1. http 限流接入oceanus-http 创建过滤器注册Bean
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();

        // 2. 创建并设置InfFilter到注册Bean
        InfFilter filter = new InfFilter();
        filterRegistrationBean.setFilter(filter);

        // 3. 设置需要过滤的url
        List<String> urlPatterns = new ArrayList<>();
        urlPatterns.add("/pieapi/*");
        urlPatterns.add("/order/*");
        filterRegistrationBean.setUrlPatterns(urlPatterns);

        // 4. 设置fileter参数
        // limit参数配置是否打开限流功能
        filterRegistrationBean.addInitParameter("limit","true");
        // oceanus-http.inffilter.limit-exclude-uri参数配置限流功能排除的uri, 避免健康监测url被限流，导致被OCTO摘掉机器流量
        filterRegistrationBean.addInitParameter("oceanus-http.inffilter.limit-exclude-uri", "/monitor/alive");
        filterRegistrationBean.setOrder(INF_FILTER);

        return filterRegistrationBean;
    }

}
