package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/28.
 */
public class AopUtils {
    /**
     * 获取连接点的方法名
     * @param joinPoint 连接点
     * @return  方法名
     */
    public static String getMethodName(ProceedingJoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        if (signature instanceof MethodSignature) {
            return ((MethodSignature)signature).getMethod().getName();
        } else {
            return signature.getName();
        }
    }

    /**
     * 获取连接点的方法返回值类型
     * @param joinPoint 连接点
     * @return  方法返回值类型
     */
    public static Class<?> getMethodReturnType(ProceedingJoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        if (signature instanceof MethodSignature) {
            return ((MethodSignature)signature).getMethod().getReturnType();
        } else {
            return signature.getDeclaringType();
        }
    }

    /**
     * 获取方法上的指定注解
     * @param joinPoint         连接点
     * @param annotationClass   要获取的注解的类
     * @return  若存在指定注解则返回, 没有返回null
     */
    public static <A extends Annotation> A getMethodAnnotation(ProceedingJoinPoint joinPoint,
                                                               Class<A> annotationClass) {
        Signature signature = joinPoint.getSignature();
        if (signature instanceof MethodSignature) {
            Method method = ((MethodSignature)signature).getMethod();
            return method.getAnnotation(annotationClass);
        } else {
            return null;
        }
    }

    public static Method getMethod(ProceedingJoinPoint joinPoint){
        Signature signature = joinPoint.getSignature();
        if (signature instanceof MethodSignature) {
            return ((MethodSignature)signature).getMethod();
        } else {
            return null;
        }
    }
}
