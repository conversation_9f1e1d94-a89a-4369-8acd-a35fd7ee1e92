package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "查询总部店内分类请求"
)
@Data
@ApiModel("查询总部店内分类请求")
public class QueryMerchantStoreCategoryRequest {

    @FieldDoc(
            description = "门店分组id，默认分组传0",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店分组id，默认分组传0")
    private Integer storeGroupId;

}
