package com.sankuai.meituan.reco.shopmgmt.pieapi.mq;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class ConsumableOutMsg implements Serializable {
    @FieldDoc(description = "租户ID")
    private Long tenantId;

    @FieldDoc(description = "仓ID")
    private Long repositoryId;

    @FieldDoc(description = "订单viewID")
    private String viewOrderId;

    @FieldDoc(description = "操作人employeeID")
    private Long operatorEmployeeId;

    @FieldDoc(description = "操作人账号ID")
    private String operatorAccountId;

    @FieldDoc(description = "操作人名称")
    private String operatorName;

    @FieldDoc(description = "拣货时间")
    private Long pickTime;

    @FieldDoc(description = "耗材明细列表")
    private List<ConsumableItem> items;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConsumableItem implements Serializable {

        private String skuId;

        private BigDecimal qty;
    }
}
