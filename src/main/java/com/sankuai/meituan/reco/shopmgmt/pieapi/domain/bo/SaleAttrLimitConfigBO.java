package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-02-12
 */
@Data
public class SaleAttrLimitConfigBO {
    public static final int MAX_SALE_ATTR_NAME_LENGTH = 64;
    public static final int MAX_SALE_ATTR_VALUE_LENGTH = 128;

    /**
     * 渠道属性限制配置
     */
    private List<ChannelAttrLimitConfig> channelAttrLimitConfigs;

    @Data
    public static final class ChannelAttrLimitConfig {
        /**
         * 渠道ID
         */
        private Integer channelId;
        /**
         * 属性名称最大长度
         */
        private Integer attrNameMaxLength = MAX_SALE_ATTR_NAME_LENGTH;
        /**
         * 属性值最大长度
         */
        private Integer attrValueMaxLength = MAX_SALE_ATTR_VALUE_LENGTH;
    }

    public int getAttrNameMaxLength(Integer channelId) {
        if (CollectionUtils.isEmpty(channelAttrLimitConfigs)) {
            return MAX_SALE_ATTR_NAME_LENGTH;
        }
        return channelAttrLimitConfigs.stream()
                .filter(config -> Objects.equals(channelId, config.getChannelId()))
                .findFirst()
                .map(ChannelAttrLimitConfig::getAttrNameMaxLength)
                .orElse(MAX_SALE_ATTR_NAME_LENGTH);
    }

    public int getAttrValueMaxLength(Integer channelId) {
        if (CollectionUtils.isEmpty(channelAttrLimitConfigs)) {
            return MAX_SALE_ATTR_VALUE_LENGTH;
        }
        return channelAttrLimitConfigs.stream()
                .filter(config -> Objects.equals(channelId, config.getChannelId()))
                .findFirst()
                .map(ChannelAttrLimitConfig::getAttrValueMaxLength)
                .orElse(MAX_SALE_ATTR_VALUE_LENGTH);
    }
}
