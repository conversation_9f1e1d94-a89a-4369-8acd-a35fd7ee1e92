package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;


import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.CompareSpuTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryCompareRecordListRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/2/22 下午2:18
 **/
@TypeDoc(
        name = "不一致商品查询请求",
        description = "不一致商品查询请求"
)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProblemSpuTypeQueryRequest {
    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @FieldDoc(description = "商品名称", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "spuName", required = false)
    private String spuName;

    @FieldDoc(
            description = "上架状态1-上架 2-下架", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "type")
    private Integer onSaleStatus;

    @FieldDoc(
            description = "类型（101商家端商品缺失;102蔬果派商品缺失;201商家端规格缺失;202蔬果派规格缺失;301基本信息不一致;401价格不一致) ", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "type", required = true)
    private Integer type;

    @FieldDoc(
            description = "page", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "page", required = true)
    private Integer page;

    @FieldDoc(
            description = "pageSize", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "pageSize", required = true)
    private Integer pageSize;

    public void validate() {
        Preconditions.checkArgument(Objects.nonNull(type) , "不一致类型不能为空");
        CompareSpuTypeEnum compareSpuTypeEnum = CompareSpuTypeEnum.findByCode(type);
        Preconditions.checkArgument(Objects.nonNull(compareSpuTypeEnum) && compareSpuTypeEnum.getWebCode()!=0, "不一致类型非法");
        Preconditions.checkArgument(Objects.nonNull(page) && page > 0, "当前查询页码非法");
        Preconditions.checkArgument(Objects.nonNull(pageSize) && pageSize > 0 && pageSize <= 50, "每页查询数量小于50");
    }

    public QueryCompareRecordListRequest to(Long tenantId) {
        return   QueryCompareRecordListRequest.builder()
                .tenantId(tenantId)
                .storeIds(Lists.newArrayList(storeId))
                .spuName(spuName)
                .onSaleStatus(onSaleStatus)
                .compareType(Objects.requireNonNull(CompareSpuTypeEnum.findByCode(type)).getWebCode())
                .page(page)
                .pageSize(pageSize)
                .build();


    }

}
