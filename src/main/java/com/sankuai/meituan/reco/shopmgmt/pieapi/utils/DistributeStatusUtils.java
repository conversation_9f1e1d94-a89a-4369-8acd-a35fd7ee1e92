package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.dianping.cat.util.StringUtils;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OnlineOrderVO;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import static com.sankuai.meituan.reco.shopmgmt.pieapi.constants.DeliveryExceptionOperateType.*;

import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @version V1.0
 * @date 2019/11/22
 * 配送状态工具类
 **/
public class DistributeStatusUtils {

    private static final int IS_SELF_DELIVERY_YES = 1;

    /**
     * 映射为配送异常状态描述
     *
     * @param distributeStatus
     * @return
     */
    public static String getDistributeStatusName(Integer distributeStatus) {
        DistributeStatusEnum status = DistributeStatusEnum.enumOf(distributeStatus);
        if (status == null) {
            return "";
        }
        switch (status) {
            case WAIT_FOR_ASSIGN_RIDER:
                return "无骑手接单";
            case DISTRIBUTE_REJECTED:
                return "配送拒单";
            case DISTRIBUTE_FAILED:
                return "配送中异常";
            default:
                return status.getDesc();
        }
    }

    /**
     * 待处理订单获取状态对应的操作列表
     */
    public static List<Integer> getOpenOperationList(OnlineOrderVO order) {
        DistributeStatusEnum status = DistributeStatusEnum.enumOf(order.getDeliveryInfoModel().getDistributeStatus());
        if (status == null) {
            return Lists.newArrayList();
        }
        switch (status) {
            case WAIT_FOR_ASSIGN_RIDER:
                return Lists.newArrayList(CANCEL.type, SELF_DELIVERY.type);
            case DISTRIBUTE_REJECTED:
                if (isSelfDelivery(order)) {
                    return Lists.newArrayList(CANCEL.type);
                } else {
                    return Lists.newArrayList(CANCEL.type, SELF_DELIVERY.type);
                }
            case SELF_DISTRIBUTE:
                return Lists.newArrayList(CANCEL.type);
            case RIDER_TAKE_GOODS:
                return Lists.newArrayList(CANCEL.type);
            case DISTRIBUTE_FAILED:
                return Lists.newArrayList(CONTACT_RIDER.type);
            case DISTRIBUTE_CANCELED:
                if (isisThirdDelivery(order)) {
                    return Lists.newArrayList(CANCEL.type, RETRY_CREATE_DELIVERY.type);
                } else {
                    return Lists.newArrayList(CANCEL.type, SELF_DELIVERY.type);
                }
            default:
                return Lists.newArrayList();
        }
    }

    /**
     * 订单是否为第三方配送
     * @param order
     * @return
     */
    private static boolean isisThirdDelivery(OnlineOrderVO order) {
        return Objects.nonNull(order.getDeliveryInfoModel().getDeliveryChannelId()) && StringUtils.isNotEmpty(order.getDeliveryInfoModel().getChannelDeliveryId());
    }

    /**
     * 订单是否为自配送
     * @param order
     * @return
     */
    private static boolean isSelfDelivery(OnlineOrderVO order) {
        return IS_SELF_DELIVERY_YES == order.getDeliveryInfoModel().getIsSelfDelivery();
    }


    /**
     * 已处理获取状态对应的操作列表(1取消2转自配送3已送出4联系骑手5知道了)
     */
    public static List<Integer> getClosedOperationList(Integer distributeStatus) {
        DistributeStatusEnum status = DistributeStatusEnum.enumOf(distributeStatus);
        if (status == null) {
            return Lists.newArrayList();
        }
        switch (status) {
            case WAIT_FOR_ASSIGN_RIDER:
                return Lists.newArrayList(CANCEL.type);
            case DISTRIBUTE_REJECTED:
                return Lists.newArrayList(CANCEL.type);
            case SELF_DISTRIBUTE:
                return Lists.newArrayList(CANCEL.type);
            case RIDER_TAKE_GOODS:
                return Lists.newArrayList(CANCEL.type);
            case DISTRIBUTE_FAILED:
                return Lists.newArrayList(CONTACT_RIDER.type);
            default:
                return Lists.newArrayList();
        }

    }


}
