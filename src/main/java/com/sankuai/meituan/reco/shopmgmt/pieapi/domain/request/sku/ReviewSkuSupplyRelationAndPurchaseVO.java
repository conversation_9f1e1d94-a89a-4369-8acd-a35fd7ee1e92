package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import java.util.List;
import java.util.Objects;

import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SupplyRelationAndPurchaseVO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.review.ReviewSubmitSupplyPurchaseDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.review.ReviewSupplyPurchaseDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.review.ReviewSupplyRelationDTO;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 审核提交的采购信息
 *
 * <AUTHOR>
 * @since 2024/08/20
 */
@Getter
@Setter
@ToString
public class ReviewSkuSupplyRelationAndPurchaseVO {
    /***
     * 采购状态：1正常、2停止
     */
    private Integer purchaseStatus;

    /***
     * 采购类型：1统采、2地采
     */
    private Integer purchaseType;

    /***
     * 供应商绑定
     */
    private List<SupplyRelationAndPurchaseVO.SkuSupplyRelationVO> supplyRelations;

    public ReviewSubmitSupplyPurchaseDTO toDTO() {
        ReviewSubmitSupplyPurchaseDTO rpcDTO = new ReviewSubmitSupplyPurchaseDTO();
        rpcDTO.setPurchaseStatus(purchaseStatus);
        rpcDTO.setPurchaseType(purchaseType);
        if (supplyRelations != null) {
            rpcDTO.setSupplyRelations(Fun.map(supplyRelations, relation ->
                    ReviewSupplyRelationDTO.builder()
                            .supplierId(relation.getSupplierId())
                            .supplyUnit(relation.getSupplyUnit())
                            .supplyUnitRatio(relation.getSupplyUnitRatio())
                            .supplyUnitPrice(relation.getSupplyUnitPrice())
                            .minOrderQuantity(relation.getMinOrderQuantity())
                            .masterFlag(relation.getMasterFlag())
                            .build()));
        }
        return rpcDTO;
    }

    public static ReviewSkuSupplyRelationAndPurchaseVO fromDTO(ReviewSupplyPurchaseDTO reviewSupplyPurchaseDTO) {
        if (Objects.isNull(reviewSupplyPurchaseDTO)) {
            return null;
        }

        ReviewSkuSupplyRelationAndPurchaseVO vo = new ReviewSkuSupplyRelationAndPurchaseVO();
        vo.setPurchaseStatus(reviewSupplyPurchaseDTO.getPurchaseStatus());
        vo.setPurchaseType(reviewSupplyPurchaseDTO.getPurchaseType());
        vo.setSupplyRelations(Fun.map(reviewSupplyPurchaseDTO.getSupplyRelations(), reviewSupplyRelationDTO -> {
            SupplyRelationAndPurchaseVO.SkuSupplyRelationVO skuSupplyRelationVO = new SupplyRelationAndPurchaseVO.SkuSupplyRelationVO();
            skuSupplyRelationVO.setSupplierId(reviewSupplyRelationDTO.getSupplierId());
            skuSupplyRelationVO.setSupplyUnit(reviewSupplyRelationDTO.getSupplyUnit());
            skuSupplyRelationVO.setSupplyUnitRatio(reviewSupplyRelationDTO.getSupplyUnitRatio());
            skuSupplyRelationVO.setSupplyUnitPrice(reviewSupplyRelationDTO.getSupplyUnitPrice());
            skuSupplyRelationVO.setMinOrderQuantity(reviewSupplyRelationDTO.getMinOrderQuantity());
            skuSupplyRelationVO.setMasterFlag(reviewSupplyRelationDTO.getMasterFlag());
            return skuSupplyRelationVO;
        }));

        return vo;
    }

}
