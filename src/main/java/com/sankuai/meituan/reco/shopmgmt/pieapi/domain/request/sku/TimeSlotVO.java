package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ProductTimeSlotDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.TimeSlotDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TimeFragmentDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "时间段"
)
@Data
@ApiModel("时间段")
@NoArgsConstructor
public class TimeSlotVO {

    @FieldDoc(
            description = "开始时间，参考\"09:00\"", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "开始时间", required = true)
    private String startTime;

    @FieldDoc(
            description = "结束时间，参考\"09:30\"", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "结束时间", required = true)
    private String endTime;

    public TimeSlotDTO buildTimeSlotDTO(){
        TimeSlotDTO timeSlotDTO = new TimeSlotDTO();
        timeSlotDTO.setStartTime(this.getStartTime());
        timeSlotDTO.setEndTime(this.getEndTime());
        return timeSlotDTO;
    }

    public TimeSlotVO(TimeSlotDTO timeSlotDTO){
        this.startTime = timeSlotDTO.getStartTime();
        this.endTime = timeSlotDTO.getEndTime();
    }

    public TimeSlotVO(TimeFragmentDTO timeSlotDTO) {
        this.startTime = timeSlotDTO.getStartTime();
        this.endTime = timeSlotDTO.getEndTime();
    }

    public TimeSlotVO(ProductTimeSlotDTO timeSlotDTO){
        this.startTime = timeSlotDTO.getStartTime();
        this.endTime = timeSlotDTO.getEndTime();
    }

    public ProductTimeSlotDTO buildProductTimeSlotDTO(){
        ProductTimeSlotDTO productTimeSlotDTO = new ProductTimeSlotDTO();
        productTimeSlotDTO.setStartTime(this.getStartTime());
        productTimeSlotDTO.setEndTime(this.getEndTime());
        return productTimeSlotDTO;
    }

    public TimeFragmentDTO buildTimeFragmentDTO() {
        TimeFragmentDTO timeFragmentDTO = new TimeFragmentDTO();
        timeFragmentDTO.setStartTime(this.getStartTime());
        timeFragmentDTO.setEndTime(this.getEndTime());
        return timeFragmentDTO;
    }
}
