package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;


@TypeDoc(
        description = "查询渠道品牌列表输入参数"
)
@Data
@ApiModel("查询渠道品牌列表输入参数")
@ToString
public class QueryBrandRequest {
    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "渠道品牌过滤关键字)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道品牌过滤关键字")
    private String searchBrandName;

}
