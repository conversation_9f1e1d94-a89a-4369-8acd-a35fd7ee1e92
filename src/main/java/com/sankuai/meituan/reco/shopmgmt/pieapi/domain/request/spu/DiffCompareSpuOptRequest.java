package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.ChannelSpuKeyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.MultiChannelSpuKeyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.diffcompare.MultiChannelStoreCustomSpuKeyDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.DiffCompareSpuOperateTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.DiffCompareTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.diffcompare.DiffCompareSpuRepairOptRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Sets;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@TypeDoc(
        name = "不一致商品操作请求",
        description = "不一致商品操作请求"
)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DiffCompareSpuOptRequest {

    @FieldDoc(
            description = "门店id"
    )
    private Long storeId;

    @FieldDoc(
            description = "门店渠道商品集合"
    )
    private List<MultiChannelSpuKeyVO> customSpuKeys;

    @FieldDoc(
            description = "操作类型, @see DiffCompareSpuOperateTypeEnum"
    )
    @ApiModelProperty(value = "修复操作类型", required = true)
    private Integer optType;

    @FieldDoc(
            description = "不一致类型,  @see SpuDiffCompareTypeEnum"
    )
    @ApiModelProperty(value = "不一致类型", required = false)
    private Integer compareType;

    public void validate() {
        Preconditions.checkArgument(Objects.nonNull(this.optType), "修复操作类型不能为空");
        Preconditions.checkArgument(Objects.nonNull(DiffCompareSpuOperateTypeEnum.findByCode(this.optType)), "修复操作类型不合法");
        Preconditions.checkArgument(Objects.nonNull(this.compareType), "需要修复的不一致类型不能为空");
        Preconditions.checkArgument(Objects.nonNull(DiffCompareTypeEnum.enumOf(this.compareType)), "不一致类型不合法");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(this.customSpuKeys), "商品列表不能为空");
        Preconditions.checkArgument(Objects.nonNull(this.storeId), "门店id不能为空");
        this.customSpuKeys.forEach(customSpuKey -> {
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(customSpuKey.getChannelIds()), "渠道id不能为空");
            Preconditions.checkArgument(StringUtils.isNotEmpty(customSpuKey.getCustomSpuId()), "商品编码不能为空");
        });

        int spuRepairMaxSize = MccConfigUtil.getProblemSpuRepairMaxSize();
        Preconditions.checkArgument(customSpuKeys.size() <= spuRepairMaxSize, "一次最多%s个商品", spuRepairMaxSize);

        List<ChannelSpuKeyVO> keyList = customSpuKeys.stream()
                .map(spuKey -> spuKey.getChannelIds()
                        .stream()
                        .map(channelId -> ChannelSpuKeyVO.of(channelId, spuKey.getCustomSpuId()))
                        .collect(Collectors.toList()))
                .flatMap(List::stream)
                .collect(Collectors.toList());
        Set<ChannelSpuKeyVO> keySet = Sets.newHashSet(keyList);
        Preconditions.checkArgument(keyList.size() == keySet.size(), "商品列表中存在重复商品");
    }

    public DiffCompareSpuRepairOptRequest convert2Request(User user) {
        return DiffCompareSpuRepairOptRequest.builder()
                .tenantId(user.getTenantId())
                .customSpuKeys(customSpuKeys.stream()
                        .map(key -> MultiChannelStoreCustomSpuKeyDTO.builder()
                                .storeId(storeId)
                                .channelIds(key.getChannelIds())
                                .customSpuId(key.getCustomSpuId())
                                .build())
                        .collect(Collectors.toList()))
                .optType(DiffCompareSpuOperateTypeEnum.findByCode(optType))
                .compareType(DiffCompareTypeEnum.enumOf(compareType))
                .operatorId(user.getAccountId())
                .operatorName(user.getAccountName())
                .build();
    }
}
