package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSpuV2DTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSpuBaseDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.ChannelSpuRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.assertj.core.util.Lists;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@TypeDoc(
        name = "创建渠道商品请求",
        description = "创建渠道商品请求"
)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CreateChannelSpuRequest {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(name = "门店ID")
    private Long storeId;

    @FieldDoc(
            description = "SPU编码"
    )
    @ApiModelProperty(name = "SPU编码", required = true)
    private String spuId;

    @FieldDoc(
            description = "渠道ID"
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    @NotNull
    private Integer channelId;

    public boolean validate() {
        if (storeId == null) {
            return false;
        }
        if (spuId == null) {
            return false;
        }
        if (channelId == null) {
            return false;
        }
        return true;
    }

    public ChannelSpuRequest toBatchCreateChannelSpu() {
        ChannelSpuRequest channelSpuRequest = new ChannelSpuRequest();
        channelSpuRequest.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        channelSpuRequest.setStoreId(this.getStoreId());
        channelSpuRequest.setChannelId(this.getChannelId());
        channelSpuRequest.setOperatorId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
        channelSpuRequest.setOperatorName(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountName());

        ChannelSpuV2DTO spuV2DTO = new ChannelSpuV2DTO();
        StoreSpuBaseDTO spuBaseDTO = new StoreSpuBaseDTO();
        spuBaseDTO.setSpuId(this.getSpuId());
        spuV2DTO.setStoreSpuBase(spuBaseDTO);
        channelSpuRequest.setChannelSpuV2DTOList(Lists.newArrayList(spuV2DTO));
        return channelSpuRequest;
    }

}
