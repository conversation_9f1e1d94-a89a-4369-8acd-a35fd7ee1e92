/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.HttpPost;

/**
 * <br><br>
 * Author: lin<PERSON><PERSON><PERSON> <br>
 * Date: 2019-04-08 Time: 19:13
 */
@Slf4j
public class HttpUtils {
    public static <T> T post(String url, String json, Class<T> clazz) {
        try {
            com.dianping.pigeon.util.HttpUtils.HttpResult result = com.dianping.pigeon.util.HttpUtils.post(url, json);
            return !result.isSuccess() ? null : JSON.parseObject(result.getBody(), clazz);
        } catch (Exception e) {
            log.warn("http request error, method:{}", HttpPost.METHOD_NAME, e);
            return null;
        }
    }
}
