package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.bo.ChannelSpuBO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.ChannelParamVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.client.common.enums.ClientTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelStoreSpuParamDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.BatchDeleteStoreSpuRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;


@TypeDoc(
        description = "批量删除门店商品接口请求参数"
)
@Data
@Slf4j
public class BatchDeleteStoreSpuApiRequest {
    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "操作类型 1-删除渠道商品，2-删除门店商品&渠道商品"
    )
    @ApiModelProperty(value = "操作类型", required = true)
    private Integer delType;

    @FieldDoc(
            description = "门店SPU_ID列表"
    )
    @ApiModelProperty(value = "门店SPU_ID列表", required = true)
    private List<String> storeSpuList;

    @FieldDoc(
            description = "渠道参数"
    )
    @ApiModelProperty(value = "渠道参数", required = true)
    private List<ChannelParamVO> channelList;

    public BatchDeleteStoreSpuRequest toRpcRequest(User user) {
        BatchDeleteStoreSpuRequest request = new BatchDeleteStoreSpuRequest();
        request.setTenantId(user.getTenantId());
        request.setDelType(this.getDelType());
        request.setOperatorId(user.getAccountId());
        request.setOperatorName(user.getOperatorName());

        List<ChannelStoreSpuParamDTO> spuList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(storeSpuList) && CollectionUtils.isNotEmpty(channelList)) {
            for (ChannelParamVO channelParamVO : channelList) {
                for (String spuId : storeSpuList) {
                    ChannelStoreSpuParamDTO channelStoreSpuParamDTO = new ChannelStoreSpuParamDTO();
                    channelStoreSpuParamDTO.setSpuId(spuId);
                    channelStoreSpuParamDTO.setStoreId(storeId);
                    channelStoreSpuParamDTO.setChannelId(channelParamVO.getChannelId());

                    spuList.add(channelStoreSpuParamDTO);
                }
            }
        }

        request.setSpuList(spuList);
        request.setClientType(ClientTypeEnum.APP.getCode());
        return request;
    }

    public List<ChannelSpuBO> toChannelSkuBOList() {
        List<ChannelSpuBO> channelSpuBOS = new ArrayList<>();
        for (ChannelParamVO channelInfoBO : channelList) {
            if (channelInfoBO == null) {
                continue;
            }
            for (String spuId : storeSpuList) {
                ChannelSpuBO channelSpuBO = new ChannelSpuBO();
                channelSpuBO.setChannelId(channelInfoBO.getChannelId());
                channelSpuBO.setChannelName(channelInfoBO.getChannelName());
                channelSpuBO.setSpuId(spuId);
                channelSpuBO.setPoiId(storeId);
                channelSpuBOS.add(channelSpuBO);
            }
        }
        return channelSpuBOS;
    }
}
