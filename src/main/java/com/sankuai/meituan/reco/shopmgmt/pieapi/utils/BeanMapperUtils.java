package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import lombok.SneakyThrows;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

public class BeanMapperUtils extends BeanUtils {

    @SneakyThrows
    public static  <T> T beanMapping(Object source,Class<T> target) {
        if(null == source){
            return null;
        }

        T t = target.newInstance();
        copyProperties(source,t);
        return t;
    }

    public static  <T> T beanMapping(Object source, Class<T> target, Consumer<T> consumer) {
        T t =  beanMapping(source,target);
        consumer.accept(t);
        return t;
    }

    public static <T> List<T> beanMappingList(List<?> sourceList, Class<T> target){
        if(null == sourceList){
            return null;
        }
        return sourceList.stream().map(source -> beanMapping(source,target)).collect(Collectors.toList());
    };
}
