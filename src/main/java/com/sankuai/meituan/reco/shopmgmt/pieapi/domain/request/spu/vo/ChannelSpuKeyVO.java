package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@TypeDoc(
        name = "门店渠道商品key",
        description = "门店渠道商品key"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ChannelSpuKeyVO {

    @FieldDoc(
            description = "渠道id"
    )
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "商品编码"
    )
    @ApiModelProperty(value = "商品编码", required = true)
    private String customSpuId;

    public static ChannelSpuKeyVO of(Integer channelId, String customSpuId) {

        ChannelSpuKeyVO customSpuKey = new ChannelSpuKeyVO();

        customSpuKey.setChannelId(channelId);
        customSpuKey.setCustomSpuId(customSpuId);
        return customSpuKey;
    }
}