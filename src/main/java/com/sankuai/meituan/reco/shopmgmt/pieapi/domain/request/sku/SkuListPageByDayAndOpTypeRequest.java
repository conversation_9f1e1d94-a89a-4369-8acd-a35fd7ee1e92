package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: <EMAIL>
 * @Date: 2020-01-07 16:15
 * @Description:
 */
@TypeDoc(
        description = "分页查询商品请求"
)
@Data
@ApiModel("根据天数分页查询商品请求")
public class SkuListPageByDayAndOpTypeRequest {
    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    @NotNull
    private Integer size;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "查询类型，1未上架，2未报价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "查询类型")
    @NotNull
    private Integer opType;

    @FieldDoc(
            description = "查询天数,不传代表所有", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "查询天数")
    public Integer days;

    @FieldDoc(
            description = "店内分类过滤。不传不过滤", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "店内分类过滤")
    public String storeCategory;

    @FieldDoc(
            description = "分类状态  1-未分类（未上线+分类=未分类 2-有分类(已上线-分类=未分类）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类状态  1-未分类（未上线+分类=未分类 2-有分类(已上线-分类=未分类）")
    private Integer hasStoreCategory;
}
