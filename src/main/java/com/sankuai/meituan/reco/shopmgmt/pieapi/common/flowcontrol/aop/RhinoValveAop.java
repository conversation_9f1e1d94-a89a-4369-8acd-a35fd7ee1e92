package com.sankuai.meituan.reco.shopmgmt.pieapi.common.flowcontrol.aop;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.onelimiter.LimitResult;
import com.dianping.rhino.onelimiter.OneLimiter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.flowcontrol.annotation.RhinoValve;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FlowValveException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.AopUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by zengzijian on 2018/6/5.
 */
@Aspect
@Component
public class RhinoValveAop {
    @Value("${app.name}")
    private String appKey;

    private static final Logger LOGGER = LoggerFactory.getLogger(RhinoValveAop.class);
    private OneLimiter oneLimiter = Rhino.newOneLimiter();

    @Around(value = "@annotation(com.sankuai.meituan.reco.shopmgmt.pieapi.common.flowcontrol.annotation.RhinoValve)")
    public Object valve(ProceedingJoinPoint joinPoint) throws Throwable {
        Method method = AopUtils.getMethod(joinPoint);
        if (method != null) {
            RhinoValve rhinoValve = AnnotationUtils.findAnnotation(method, RhinoValve.class);
            if (rhinoValve == null) {
                return joinPoint.proceed();
            }
            Map<String, String> params = new HashMap<>();
            params.put("appkey", appKey);
            LimitResult result = oneLimiter.run(rhinoValve.entrance(), params);

            if (result.isReject()){
                LOGGER.info("rhino valve reject, entrance:" + rhinoValve.entrance() + ", appkey:" + appKey);
                throw new FlowValveException(ResultCode.VALVE_EFFECT.getCode(), result.getMsg());
            }

            return joinPoint.proceed();
        }

        return joinPoint.proceed();
    }
}
