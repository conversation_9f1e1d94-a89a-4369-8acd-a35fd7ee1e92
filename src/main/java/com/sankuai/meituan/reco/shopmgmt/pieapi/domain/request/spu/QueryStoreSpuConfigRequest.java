package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import java.util.Objects;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "商品门店配置查询请求"
)
@Data
public class QueryStoreSpuConfigRequest {
    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @FieldDoc(
            description = "配置页面类型"
    )
    @ApiModelProperty(value = "配置页面类型")
    private String configPageType;

    public void selfCheck(){
        Preconditions.checkArgument(Objects.nonNull(storeId), "门店id不能为空");
        Preconditions.checkArgument(Objects.nonNull(configPageType), "配置类型不能为空");
    }
}
