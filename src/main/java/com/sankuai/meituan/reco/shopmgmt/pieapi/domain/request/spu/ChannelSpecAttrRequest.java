package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelSpecialAttrEnum;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/7/1 17:08
 **/
@TypeDoc(
        name = "渠道特殊属性请求",
        description = "渠道特殊属性请求"
)
@Data
@EqualsAndHashCode
@ToString
public class ChannelSpecAttrRequest {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    public Long tenantId;

    @FieldDoc(
            name = "渠道ID",
            description = "渠道ID",
            requiredness = Requiredness.REQUIRED
    )
    public Integer channelId;

    @FieldDoc(
            name = "属性id",
            description = "属性id",
            requiredness = Requiredness.REQUIRED
    )
    public Long attrId;

    @FieldDoc(
            name = "关键词",
            description = "关键词",
            requiredness = Requiredness.REQUIRED
    )
    public String keyword;

    @FieldDoc(
            description = "页码",
            requiredness = Requiredness.REQUIRED
    )
    public Integer page=1;

    @FieldDoc(
            description = "每页记录数",
            requiredness = Requiredness.OPTIONAL
    )
    public Integer pageSize=20;

    @FieldDoc(
            description = "类目id",
            requiredness = Requiredness.OPTIONAL
    )
    public String categoryId;

    public boolean validate(){
        if(StringUtils.isBlank(keyword)){
            return false;
        }
        if(!ChannelSpecialAttrEnum.isSpecialAttrEnum(attrId)){
            return false;
        }
        return true;
    }
}
