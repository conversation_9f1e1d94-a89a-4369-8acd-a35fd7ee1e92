package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: wangyihao04
 * @Date: 2020-11-12 17:42
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "sku调价信息集"
)
@Data
@ApiModel("sku调价信息集")
public class SkuAdjustPriceVO {
    @FieldDoc(
            description = "商品ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品ID")
    @NotNull
    private String skuId;

    @FieldDoc(
            description = "渠道定价策略", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道定价策略")
    private List<ChannelSyncStrategyVO> skuPriceStrategies;

    public void selfCheck() {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(skuPriceStrategies), "商品定价策略不能为空");
        skuPriceStrategies.forEach(priceStrategy -> priceStrategy.selfCheck());
    }
}
