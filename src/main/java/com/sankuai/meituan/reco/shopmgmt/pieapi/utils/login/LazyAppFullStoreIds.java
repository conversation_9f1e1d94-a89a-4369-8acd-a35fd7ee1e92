package com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login;

import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * APP 全部门店ID懒加载对象
 *
 * <AUTHOR>
 * @date 2023/9/21
 */
@Slf4j
public class LazyAppFullStoreIds {

    private List<Long> storeIds;

    private final Supplier<List<Long>> fullStoreIdSupplier;

    public LazyAppFullStoreIds(Supplier<List<Long>> fullStoreIdSupplier) {
        this.fullStoreIdSupplier = fullStoreIdSupplier;
    }

    /**
     * 获取全部门店ID
     * 从使用方式来说，这里暂不考虑并发问题
     *
     * @return 全部门店ID列表
     */
    @NotNull
    public List<Long> fetchStoreIds() {
        if (Objects.isNull(storeIds)) {
            try {
                storeIds = fullStoreIdSupplier.get();
                log.info("懒加载获取全部门店ID列表为：{}", storeIds);
            } catch (Exception e) {
                log.error("懒加载获取全部门店ID列表异常", e);
                throw new CommonRuntimeException("获取全部门店ID列表异常，请重置", e);
            }
        }
        if (Objects.isNull(storeIds)) {
            return Collections.emptyList();
        }
        return storeIds;
    }

}
