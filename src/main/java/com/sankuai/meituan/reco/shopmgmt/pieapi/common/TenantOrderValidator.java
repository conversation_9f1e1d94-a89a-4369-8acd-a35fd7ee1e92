package com.sankuai.meituan.reco.shopmgmt.pieapi.common;

import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.OrderBizFacade;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataAuthException;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurityContext;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ValidatorConfig;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.validator.AbstractSingleDataAuthValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * OrderValidator
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
@Slf4j
@Component
@ValidatorConfig(dataType = ParamDataType.CUSTOM)
public class TenantOrderValidator extends AbstractSingleDataAuthValidator {

    @Autowired
    private OrderBizFacade orderBizFacade;

    @Override
    public boolean hasDataAuth(DataSecurityContext context, Long orderId) throws DataAuthException {
        if (orderId == null) {
            return true;
        }

        BizOrderModel bizOrder;
        try {
            bizOrder = orderBizFacade.queryOrderModelByOrderId(orderId);
        } catch (Exception e) {
            log.error("Validating order error: {}, value: {}", e.getMessage(), orderId, e);
            throw new DataAuthException(e);
        }
        
        if (bizOrder == null) {
            log.warn("deliverOrders is empty");
            return false;
        }
        return Objects.equals(context.getTenantId(), bizOrder.getTenantId());
    }

}
