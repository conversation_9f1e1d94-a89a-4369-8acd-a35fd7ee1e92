package com.sankuai.meituan.reco.shopmgmt.pieapi.common.flowcontrol.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/6/5.
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface RhinoValve {
    String entrance();
}
