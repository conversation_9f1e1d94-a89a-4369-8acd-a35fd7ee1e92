package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.FrontCategorySimpleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.CreateStockAdjustRequestVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrValueVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.AiRecommendVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelSkuAttrValueInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelCategoryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelDynamicInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelPriceVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSaleAttrInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSpuCreateVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.NameSupplementInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.SpecialPictureVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuSimpleCreateVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.SaleAttrCompatUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.CartonMeasureDto;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelBrandRelationDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSpuDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.KeyValueDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreCategoryDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreGroupCategoryCodeDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TimeFragmentDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.VideoInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.sku.CombineChildSkuDto;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.StoreSpuFastCreateRequest;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.constant.SystemSourceType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2021-03-01 15:44
 * @Description:
 */
@TypeDoc(
        description = "新建门店商品（新）并上线请求参数"
)
@Data
@ApiModel("新建门店商品（新）并上线请求参数")
public class CreateStoreSpuApiRequest {

    @FieldDoc(
            description = "租户id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户id")
    private Long tenantId;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "门店商品信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店商品信息", required = true)
    @NotNull
    private StoreSpuSimpleCreateVO storeSpu;

    @FieldDoc(
            description = "是否无限库存配置下提交（仅App使用）"
    )
    @ApiModelProperty(name = "无限库存的标志位")
    private Boolean infiniteInventory;

    @FieldDoc(
            description = "0为初始化非手动定价，1为手动定价"
    )
    @ApiModelProperty(name = "定价方式")
    private Integer priceInitType;
    @FieldDoc(
            description = "是否手动创建"
    )
    @ApiModelProperty(name = "是否手动创建")
    private Boolean manualCreate;


    @FieldDoc(description = "是否使用新库存调整链路")
    @ApiModelProperty(value = "是否使用新库存调整链路")
    private boolean useNewAdjustStock;

    @FieldDoc(description = "库存调整请求")
    @ApiModelProperty(value = "库存调整请求")
    private CreateStockAdjustRequestVo stockAdjustRequestVo;

    public StoreSpuFastCreateRequest toRpcRequest(User user) {
        StoreSpuFastCreateRequest rpcRequest = new StoreSpuFastCreateRequest();
        rpcRequest.setStoreId(storeId);
        rpcRequest.setTenantId(tenantId);
        rpcRequest.setInfiniteInventory(infiniteInventory);
        rpcRequest.setPriceType(priceInitType);
        rpcRequest.setSellPoint(storeSpu.getSellPoint());
        rpcRequest.setBoothId(storeSpu.getBoothId());
        rpcRequest.setDescription(storeSpu.getDescription());
        rpcRequest.setWeightType(storeSpu.getWeightType());
        rpcRequest.setBrandCode(storeSpu.getBrandCode());
        rpcRequest.setProducingArea(storeSpu.getProducingArea());
        rpcRequest.setSpuName(storeSpu.getSpuName());
        rpcRequest.setCategoryCode(storeSpu.getCategoryCode());
        rpcRequest.setPictureContents(storeSpu.getPictureContents());
        if (storeSpu.getVideo() != null) {
            rpcRequest.setVideo(new VideoInfoDTO(storeSpu.getVideo().getVideoUrl(),
                    storeSpu.getVideo().getCoverImageUrl()));
        }

        final List<KeyValueDTO> properties = new ArrayList<>();
        if (storeSpu.getProperties() != null) {
            storeSpu.getProperties().forEach(vo -> {
                properties.add(new KeyValueDTO(vo.getPropertyName(), vo.getPropertyValues()));
            });
        }
        rpcRequest.setProperties(properties);

        final List<StoreSkuDTO> storeSkuList = new ArrayList<>();

        if (storeSpu.getStoreSkuList() != null) {
            storeSpu.getStoreSkuList().forEach(sku -> {
                StoreSkuDTO dto = new StoreSkuDTO();
                dto.setAutoResumeInfiniteStock(sku.getAutoResumeInfiniteStock());
                dto.setSkuId(sku.getSkuId());
                dto.setOnlinePrice(sku.getOnlinePrice());
                dto.setStock(sku.getStock());
                dto.setCustomizedStockFlag(sku.getCustomizeStockFlag());
                dto.setUpc(sku.getUpc());
                dto.setUpcList(sku.getUpcList());
                dto.setWeight(sku.getWeight());
                dto.setWeightForUnit(sku.getWeightForUnit());
                dto.setWeightUnit(sku.getWeightUnit());
                dto.setSpec(sku.getSpec());
                dto.setSaleUnit(sku.getSaleUnit());
                dto.setMinOrderCount(sku.getMinNum());
                dto.setBoxNum(sku.getBoxNum());
                dto.setBoxPrice(sku.getBoxPrice());
                dto.setElemOnlinePrice(sku.getElemOnlinePrice());
                dto.setJdOnlinePrice(sku.getJdOnlinePrice());
                dto.setYzOnlinePrice(sku.getYzOnlinePrice());
                dto.setSuggestPrice(sku.getSuggestPrice());
                dto.setExternalCode(sku.getExternalCode());
                dto.setFranchiseeHeadquartersCode(sku.getFranchiseeHeadquartersCode());
                dto.setPurchasePlatformCode(sku.getPurchasePlatformCode());
                dto.setCustomizeNoUpcCode(sku.getCustomizeNoUpcCode());
                // 进货价
                if (StringUtils.isNotBlank(sku.getStorePrice())) {
                    dto.setStorePrice(MoneyUtils.yuanToCent(sku.getStorePrice()));
                }
                dto.setUseSkuAsUpc(sku.getUseSkuAsUpc());
                if (storeSpu.getSpecType() == 2 && CollectionUtils.isNotEmpty(sku.getJdAttrValues())) {
                    dto.setJdSaleAttributeValueDTOS(ConverterUtils.convertList(sku.getJdAttrValues(), SaleAttrValueVo::convertBizDTO));
                }
                if (CollectionUtils.isNotEmpty(sku.getCartonMeasureConvertFactorList())) {
                    dto.setCartonMeasureList(sku.getCartonMeasureConvertFactorList().stream()
                            .map(item -> {
                                CartonMeasureDto cartonMeasureDto = new CartonMeasureDto();
                                cartonMeasureDto.setCartonMeasureCode(item.getCartonMeasureCode());
                                cartonMeasureDto.setCartonMeasureName(item.getCartonMeasureName());
                                cartonMeasureDto.setBasicUnitConvertFactor(item.getBasicUnitConvertFactor());
                                return cartonMeasureDto;
                            })
                            .collect(Collectors.toList()));
                }

                //后续渠道价格都通过该参数传递。不再使用onlinePrice、elemOnlinePrice、jdOnlinePrice等专用字段传递价格参数
                if (CollectionUtils.isNotEmpty(sku.getChannelOnlinePriceList())) {
                    dto.setChannelPriceList(Fun.map(sku.getChannelOnlinePriceList(), ChannelPriceVO::toDto));
                }
                dto.setChannelSaleAttributeValues(Fun.map(sku.getChannelSaleAttrValueInfoList(),
                        channelSaleAttrValueInfoVO -> channelSaleAttrValueInfoVO.toBizDTO(sku.getImageInfo())));

                dto.setChannelSkuAttrValueInfoList(ChannelSkuAttrValueInfoVo.toDtoList(sku.getChannelSkuAttrValueInfoList()));
                dto.setSkuSaleType(sku.getSkuSaleType());
                if (CollectionUtils.isNotEmpty(sku.getChildSkuList())) {
                    dto.setChildSkuList(JacksonUtils.convertList(sku.getChildSkuList(), CombineChildSkuDto.class));
                }
                storeSkuList.add(dto);
            });
        }
        rpcRequest.setStoreSkuList(storeSkuList);

        final Map<Integer, List<TimeFragmentDTO>> time = new HashMap<>();
        if (storeSpu.getAvailableTimes() != null) {
            storeSpu.getAvailableTimes().forEach((k, v) -> {
                time.put(k, v.stream().map(t -> new TimeFragmentDTO(t.getStartTime(), t.getEndTime())).collect(Collectors
                        .toList()));
            });
        }
        rpcRequest.setAvailableTimes(time);

        List<ChannelSpuCreateVO> channelSpuCreateVOS = storeSpu.getChannelSpuList();
        if (channelSpuCreateVOS != null && !channelSpuCreateVOS.isEmpty()) {
            ChannelSpuDTO channelSpuDTO = new ChannelSpuDTO();
            ChannelSpuCreateVO vo = channelSpuCreateVOS.get(0);
            channelSpuDTO.setSpuId(vo.getSpuId());
            channelSpuDTO.setChannelId(vo.getChannelId());

            ChannelCategoryVO channelCategoryVO = vo.getChannelCategory();
            if (channelCategoryVO != null) {
                ChannelCategoryDTO channelCategoryDTO = new ChannelCategoryDTO();
                channelCategoryDTO.setChannelCategoryCode(channelCategoryVO.getChannelCategoryCode());
                channelCategoryDTO.setChannelCategoryCodePath(channelCategoryVO.getChannelCategoryCodePath());
                channelCategoryDTO.setChannelCategoryName(channelCategoryVO.getChannelCategoryName());
                channelCategoryDTO.setChannelCategoryNamePath(channelCategoryVO.getChannelCategoryNamePath());
                channelCategoryDTO.setChannelDynamicInfoDTOList(ChannelDynamicInfoVO.toBizDTOList(channelCategoryVO
                        .getChannelDynamicInfoVOList()));
                channelSpuDTO.setChannelCategory(channelCategoryDTO);
            }

            List<FrontCategorySimpleVO> storeCategories = vo.getFrontCategories();
            if (CollectionUtils.isNotEmpty(storeCategories)) {
                final List<StoreCategoryDTO> storeCategoryList = new ArrayList<>();
                storeCategories.forEach(c -> {
                    StoreCategoryDTO dto = new StoreCategoryDTO();
                    dto.setStoreCategoryCode(c.getFrontCategoryCode());
                    dto.setStoreCategoryCodePath(c.getFrontCategoryCodePath());
                    dto.setStoreCategoryName(c.getFrontCategoryName());
                    dto.setStoreCategoryNamePath(c.getFrontCategoryNamePath());
                    storeCategoryList.add(dto);
                });
                channelSpuDTO.setStoreCategoryList(storeCategoryList);
            }

            channelSpuDTO.setSpuStatus(vo.getSpuStatus());

            rpcRequest.setChannelSpu(channelSpuDTO);
        }
        //设置门店店内分类
        rpcRequest.setStoreCategoryCodes(storeSpu.getStoreCategoryCodes());
        rpcRequest.setMerchantStoreCategoryCodes(storeSpu.getMerchantStoreCategoryCodes());
        rpcRequest.setPoiStoreCategoryCodes(storeSpu.getPoiStoreCategoryCodes());
        rpcRequest.setImageUrls(storeSpu.getImagesUrls());
        rpcRequest.setOperatorId(user.getAccountId());
        rpcRequest.setOperatorName(user.getOperatorName());
        rpcRequest.setSourceType(SystemSourceType.APP.getValue());
        rpcRequest.setSpecialty(storeSpu.getSpecialty());
        rpcRequest.setProducingArea(storeSpu.getProducingArea());
        rpcRequest.setBrandCode(storeSpu.getBrandCode());
        rpcRequest.setElemChannelCategoryCode(storeSpu.getElemChannelCategoryCode());
        if (CollectionUtils.isNotEmpty(storeSpu.getElemChannelDynamicInfoVOList())) {
            rpcRequest.setElemChannelDynamicInfo(ChannelDynamicInfoVO.toBizDTOList(storeSpu.getElemChannelDynamicInfoVOList()));
        }
        rpcRequest.setJdChannelCategoryCode(storeSpu.getJdCategoryId());
        //设置京东规格属性
        if (CollectionUtils.isNotEmpty(storeSpu.getJdSaleAttrList())) {
            rpcRequest.setJdSaleAttributeList(ConverterUtils.convertList(storeSpu.getJdSaleAttrList(), SaleAttrVo::convertBizDTO));
        }
        rpcRequest.setChannelSaleAttributes(ConverterUtils.convertList(storeSpu.getChannelSaleAttrInfoList(), ChannelSaleAttrInfoVO::toBizDTO));
        rpcRequest.setSpecType(storeSpu.getSpecType());
        if (CollectionUtils.isNotEmpty(storeSpu.getStoreGroupCategoryCodes())) {
            List<StoreGroupCategoryCodeDTO> storeGroupCategoryDTOList = Lists.newArrayList();
            storeSpu.getStoreGroupCategoryCodes().forEach(storeGroupCategoryVO -> {
                StoreGroupCategoryCodeDTO dto = new StoreGroupCategoryCodeDTO();
                dto.setStoreGroupId(storeGroupCategoryVO.getStoreGroupId());
                dto.setCategoryCodes(storeGroupCategoryVO.getCategoryCodes());
                storeGroupCategoryDTOList.add(dto);
            });
            rpcRequest.setStoreGroupCategoryCodes(storeGroupCategoryDTOList);
        }
        if(CollectionUtils.isNotEmpty(storeSpu.getChannelBrand())){
            rpcRequest.setChannelBrands(storeSpu.getChannelBrand().stream().map(c ->{
                ChannelBrandRelationDTO channelBrandRelationDTO = new ChannelBrandRelationDTO();
                channelBrandRelationDTO.setChannelId(c.getChannelId());
                channelBrandRelationDTO.setBrandCode(c.getBrandCode());
                return channelBrandRelationDTO;
            }).collect(Collectors.toList()));
        }
        rpcRequest.setDouyinChannelCategoryCode(storeSpu.getDouyinCategoryId());
        if (CollectionUtils.isNotEmpty(storeSpu.getDouyinChannelDynamicInfoVOList())) {
            rpcRequest.setDouyinChannelDynamicInfo(ChannelDynamicInfoVO.toBizDTOList(storeSpu.getDouyinChannelDynamicInfoVOList()));
        }
        rpcRequest.setDouyinAfterSaleServiceType(storeSpu.getDouyinAfterSaleServiceType());
        if (storeSpu.getMedicalDeviceQuaInfo() != null) {
            rpcRequest.setMedicalDeviceQuaInfo(storeSpu.getMedicalDeviceQuaInfo().toMedicalDeviceQuaInfoDTO());
        }
        rpcRequest.setMtAfterSaleServiceType(storeSpu.getMtAfterSaleServiceType());
        rpcRequest.setUseNewAdjustStock(useNewAdjustStock);
        rpcRequest.setPoiGroupIdList(storeSpu.getPoiGroupIdList());
        rpcRequest.setControlQuaPicUrl(storeSpu.getControlQuaPicUrl());
        rpcRequest.setAiRecommendInfo(AiRecommendVO.convertAiRecommendBizDTO(storeSpu.getAiRecommendVO()));
        rpcRequest.setNameSupplementInfo(NameSupplementInfoVO.convertToBizDTO(storeSpu.getNameSupplementInfo()));
        rpcRequest.setMtSpecialPictureList(ConverterUtils.convertList(storeSpu.getMtSpecialPictureList(), SpecialPictureVO::toDTO));
        return SaleAttrCompatUtils.compat(rpcRequest);
    }

}
