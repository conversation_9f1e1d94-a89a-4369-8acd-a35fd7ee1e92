package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.DiffCompareTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.diffcompare.QueryDiffCompareSpuRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

@TypeDoc(
        name = "不一致商品查询请求",
        description = "不一致商品查询请求"
)
@Data
@Builder
public class DiffCompareSpuQueryRequest {
    @FieldDoc(
            description = "门店id列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "门店id列表")
    private Long storeId;

    @FieldDoc(description = "商品Id", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "customSpuId", required = false)
    private String customSpuId;

    @FieldDoc(description = "商品名称", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "spuName", required = false)
    private String spuName;

    @FieldDoc(
            description = "上架状态1-上架 2-下架", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "type")
    private Integer onSaleStatus;

    @FieldDoc(
            description = "类型: 1-渠道商品缺失; 2-牵牛花商品缺失; 3-商品规格缺失; 4-基础信息不一致; 5-销售信息不一致; 6-价格不一致)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "type", required = true)
    private Integer type;

    @FieldDoc(
            description = "类型: 1-渠道商品缺失; 2-牵牛花商品缺失; 3-商品规格缺失; 4-基础信息不一致; 5-销售信息不一致; 6-价格不一致;15-渠道商家端规格缺失；16-牵牛花规格缺失；17-规格销售信息不一致)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "type", required = true)
    private List<Integer> typeList;

    @FieldDoc(
            description = "查询美团补贴活动品，1-仅美补活动品，2-仅非美补活动品，0/null-查询全部", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "查询美团补贴活动品，1-仅美补活动品，2-仅非美补活动品，0/null-查询全部")
    private Integer queryMtSubsidyActivitySpu;

    @FieldDoc(
            description = "page", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "page", required = true)
    private Integer page;

    @FieldDoc(
            description = "pageSize", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "pageSize", required = true)
    private Integer pageSize;
    public void validate() {
        List<DiffCompareTypeEnum>typeEnumList = CollectionUtils.isNotEmpty(this.typeList)? this.typeList.stream()
                .map(DiffCompareTypeEnum::enumOf).filter(Objects::nonNull).collect(Collectors.toList()) : null;
        Preconditions.checkArgument(Objects.nonNull(DiffCompareTypeEnum.enumOf(type))|| CollectionUtils.isNotEmpty(typeEnumList) , "不一致类型不能为空");
        Preconditions.checkArgument(Objects.nonNull(page) && page > 0, "当前查询页码非法");
        Preconditions.checkArgument(Objects.nonNull(pageSize) && pageSize > 0 && pageSize <= 50, "每页查询数量小于1 或 大于50");
    }

    public QueryDiffCompareSpuRequest to(Long tenantId) {
        return   QueryDiffCompareSpuRequest.builder()
                .tenantId(tenantId)
                .storeIds(Lists.newArrayList(storeId))
                .customSpuId(customSpuId)
                .spuNameKey(spuName)
                .onSaleStatus(onSaleStatus)
                .queryMtSubsidyActivitySpu(queryMtSubsidyActivitySpu)
                .compareType(type)
                .page(page)
                .pageSize(pageSize)
                .compareTypeList(typeList)
                .build();
    }
}
