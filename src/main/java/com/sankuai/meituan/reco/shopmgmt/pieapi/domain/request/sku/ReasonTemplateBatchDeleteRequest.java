package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.google.common.base.Preconditions;
import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.DeleteReasonTemplateRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@TypeDoc(
        description = "批量删除审核拒绝原因模板"
)
@ApiModel("批量删除审核拒绝原因模板")
@Getter
@Setter
@ToString
public class ReasonTemplateBatchDeleteRequest {

    @FieldDoc(
            description = "模板原因id列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "模板原因id列表", required = true)
    private List<Long> reasonIdList;

    public void selfCheck() {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(reasonIdList) && reasonIdList.size() <= 30, "模板原因id列表不可为空且上限30");
    }

    public List<DeleteReasonTemplateRequest> toDeleteReasonTemplateRequestList(User user) {
        return Fun.map(reasonIdList, reasonId -> {
            DeleteReasonTemplateRequest deleteReasonTemplateRequest = new DeleteReasonTemplateRequest();
            deleteReasonTemplateRequest.setTenantId(user.getTenantId());
            deleteReasonTemplateRequest.setOperateId(user.getAccountId());
            deleteReasonTemplateRequest.setOperateName(user.getOperatorName());
            deleteReasonTemplateRequest.setReasonId(reasonId);
            return deleteReasonTemplateRequest;
        });
    }

}
