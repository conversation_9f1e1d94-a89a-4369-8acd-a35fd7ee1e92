package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询商品主档异常详情请求
 *
 * <AUTHOR>
 * @since 2023/7/6
 */
@TypeDoc(
        name = "查询异常类型请求",
        description = "查询异常类型请求）"
)
@Data
public class QueryAllAbnormalTypeListRequest {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID")
    private Long storeId;


}
