package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSkuCreateVO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.StoreSpuDetailRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "更新门店商品规格请求参数"
)
@Data
@ApiModel("更新门店商品规格请求参数")
public class StoreSpuSpecListUpdateRequest {
    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户id")
    @Min(value = 1, message = "无效的租户id")
    private long tenantId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id")
    @Min(value = 1, message = "无效的门店id")
    private long storeId;

    @FieldDoc(
            description = "spu编码",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "spu编码")
    @NotNull(message = "spu编码为空")
    private String spuId;

    @FieldDoc(
            description = "原sku列表",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "原sku列表")
    @NotNull(message = "原sku列表为空")
    private List<StoreSkuCreateVO> orgSkuList;

    @FieldDoc(
            description = "sku列表",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "sku列表")
    @NotNull(message = "sku列表为空")
    private List<StoreSkuCreateVO> skuList;

    @FieldDoc(
            description = "是否无限库存配置下提交（仅App使用）"
    )
    @ApiModelProperty(name = "无限库存的标志位")
    private Boolean infiniteInventory;

    public StoreSpuDetailRequest toStoreSpuDetailRequest() {
        StoreSpuDetailRequest storeSpuDetailRequest = new StoreSpuDetailRequest();
        storeSpuDetailRequest.setTenantId(tenantId);
        storeSpuDetailRequest.setStoreId(storeId);
        storeSpuDetailRequest.setSpuId(spuId);
        return storeSpuDetailRequest;
    }
}
