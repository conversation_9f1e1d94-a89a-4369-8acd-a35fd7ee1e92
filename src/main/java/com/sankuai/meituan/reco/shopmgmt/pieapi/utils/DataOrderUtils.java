package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import com.meituan.linz.boot.util.Assert;

/**
 * <AUTHOR>
 * @since 2021/12/28
 */
public class DataOrderUtils {

    private DataOrderUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static boolean isRealTime(String startDate, String endDate) {
        LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ISO_DATE);
        LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ISO_DATE);
        return isRealTime(start, end);
    }

    public static boolean isRealTime(LocalDate start, LocalDate end) {
        LocalDate now = LocalDate.now();
        Assert.throwIfTrue(start.isAfter(now) || end.isAfter(now), "开始或结束时间不能大于当前时间");
        Assert.throwIfTrue(start.isAfter(end), "开始时间不能大于结束时间");
        if (start.isAfter(now.minusDays(2l))) {
            return true;
        }
        Assert.throwIfTrue(end.isAfter(now.minusDays(2l)), "所选时间段存在跨实时与离线数据");
        return false;
    }
}
