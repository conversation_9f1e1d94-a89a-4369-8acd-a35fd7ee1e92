package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdjustContainerBatchStockRequestInfo {

    @FieldDoc(
            description = "库位code"
    )
    @ApiModelProperty(value = "", required = true)
    private String originLocationCode;

    @FieldDoc(
            description = "库位ID"
    )
    @ApiModelProperty(value = "", required = true)
    private Long originLocationId;

    @FieldDoc(
            description = "库位type"
    )
    @ApiModelProperty(value = "")
    private Integer originLocationType;

    @FieldDoc(
            description = "调整前库存数量"
    )
    @ApiModelProperty(value = "调整前库存数量", required = true)
    private Integer oldQuantity;

    @FieldDoc(
            description = "调整后库存数量"
    )
    @ApiModelProperty(value = "调整后库存数量", required = true)
    private Integer newQuantity;

}
