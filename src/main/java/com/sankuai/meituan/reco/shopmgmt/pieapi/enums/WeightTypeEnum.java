package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum WeightTypeEnum {

    WEIGHT(1, "称重计量"),
    PIECE(2, "称重计件"),
    NONE(3, "非称重");


    private int code;

    private String desc;



    public static WeightTypeEnum getByCode(int code) {
        for (WeightTypeEnum e : WeightTypeEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }

        return null;
    }



    public static WeightTypeEnum getByDesc(String desc) {
        for (WeightTypeEnum e : WeightTypeEnum.values()) {
            if (StringUtils.equals(e.getDesc(), desc)) {
                return e;
            }
        }

        return null;
    }

    public StanderTypeEnum toStanderType(){
        if (code == NONE.code){
            return StanderTypeEnum.STANDER;
        }
        return StanderTypeEnum.NO_STANDER;
    }
}
