package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.CombineChildSkuVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdjustSkuRequestInfo {
    @FieldDoc(
            description = "sku id"
    )
    @ApiModelProperty(value = "sku id", required = true)
    private String skuId;

    @FieldDoc(
            description = "商品名称"
    )
    @ApiModelProperty(value = "商品名称", required = true)
    private String skuName;


    @FieldDoc(
            description = "规格名称"
    )
    @ApiModelProperty(value = "规格名称", required = true)
    private String specName;

    @FieldDoc(
            description = "单位"
    )
    @ApiModelProperty(value = "单位", required = true)
    private String unit;

    @FieldDoc(
            description = "备注"
    )
    @ApiModelProperty(value = "备注", required = true)
    private String comment;

    @FieldDoc(
            description = "调整前库存"
    )
    @ApiModelProperty(value = "调整前库存", required = true)
    private String oldQuantity;

    @FieldDoc(
            description = "调整后库存"
    )
    @ApiModelProperty(value = "调整后库存", required = true)
    private String newQuantity;

    @FieldDoc(
            description = "是否为批次商品"
    )
    @ApiModelProperty(value = "是否为批次商品", required = true)
    private Boolean enableExpiredCheck;

    @FieldDoc(description = "门店商品 可用库存类型标识")
    @ApiModelProperty(value = "门店商品 可用库存类型标识")
    private Integer customizeStockFlag;

    @FieldDoc(description = "自动恢复无限库存")
    @ApiModelProperty(value = "自动恢复无限库存")
    private Integer autoResumeInfiniteStock;

    @FieldDoc(
            description = "库位/批次库存列表"
    )
    @ApiModelProperty(value = "库位/批次库存列表", required = true)
    private List<AdjustContainerBatchStockRequestInfo> batchContainerInfoList;

    @FieldDoc(
            description = "sku类型 1单品 2组合品"
    )
    @ApiModelProperty(name = "sku类型 1单品 2组合品")
    private Integer skuSaleType;

    @FieldDoc(
            description = "组合品的子sku信息"
    )
    @ApiModelProperty(name = "组合品的子sku信息")
    private List<CombineChildSkuVo> childSkuList;

}
