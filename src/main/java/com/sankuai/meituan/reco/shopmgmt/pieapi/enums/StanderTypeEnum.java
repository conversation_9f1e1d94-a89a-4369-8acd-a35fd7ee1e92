package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import lombok.Getter;

@Getter
public enum StanderTypeEnum {

    STANDER(1, "标品"),
    NO_STANDER(0, "非标品");

    StanderTypeEnum(int code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private final  int code;
    private final  String desc;

    public static StanderTypeEnum findByCode(int code){
        for (StanderTypeEnum standerType : values()) {
            if (standerType.getCode() == code){
                return standerType;
            }
        }
        return null;
    }
}
