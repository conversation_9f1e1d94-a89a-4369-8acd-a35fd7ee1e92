package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;


@TypeDoc(
        description = "分页查询SPU信息, 门店未上线/未上架SPU, 城市在门店未添加SPU"
)
@Data
@ApiModel("分页查询SPU信息, 门店未上线/未上架SPU, 城市在门店未添加SPU")
public class PageTaggedSpuByQueryRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "标签id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标签id", required = true)
    @NotNull
    private Long tagId;


    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第几页", required = true)
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    private Integer pageSize;
}
