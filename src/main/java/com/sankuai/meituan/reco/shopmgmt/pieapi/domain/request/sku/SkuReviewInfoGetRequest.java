package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.GetSkuReviewInfoRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 获取单个商品审核信息
 * @author: WangSukuan
 * @create: 2020-03-10
 **/
@TypeDoc(
        description = "获取单个商品审核信息"
)
@Data
@ApiModel("获取单个商品审核信息")
public class SkuReviewInfoGetRequest {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商品审核id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品审核id", required = true)
    @NotNull
    private Long skuReviewId;

    public GetSkuReviewInfoRequest convertGetSkuReviewInfoRequest(User user){

        GetSkuReviewInfoRequest request = new GetSkuReviewInfoRequest();
        request.setTenantId(user.getTenantId());
        request.setStoreId(this.getStoreId());
        request.setSkuReviewId(this.getSkuReviewId());
        request.setOperateId(user.getAccountId());
        request.setOperateName(user.getOperatorName());

        return request;

    }


}
