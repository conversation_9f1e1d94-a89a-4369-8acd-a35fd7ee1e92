package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SaleAttributeValueDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SkuSaleAttributeValueDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/9/6
 * 销售属性值
 **/
@TypeDoc(
        description = "销售属性值"
)
@Data
@ApiModel("销售属性值")
public class SaleAttrValueVo {

    @FieldDoc(
            description = "属性名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "属性名称")
    @NotNull
    private String attrName;

    @FieldDoc(
            description = "属性值", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "属性值")
    @NotNull
    private String attrValue;

    public static SkuSaleAttributeValueDTO convertBizDTO (SaleAttrValueVo saleAttrValueInfoVO){

        SkuSaleAttributeValueDTO saleAttributeValueDTO = new SkuSaleAttributeValueDTO();
        saleAttributeValueDTO.setAttrValue(saleAttrValueInfoVO.getAttrValue());
        saleAttributeValueDTO.setAttrName(saleAttrValueInfoVO.getAttrName());
        return saleAttributeValueDTO;
    }

    public static SaleAttrValueVo ofDTO (SaleAttributeValueDTO saleAttributeValueDTO){

        SaleAttrValueVo saleAttrValueVo = new SaleAttrValueVo();
        saleAttrValueVo.setAttrValue(saleAttributeValueDTO.getAttrValue());
        saleAttrValueVo.setAttrName(saleAttributeValueDTO.getAttrName());
        return saleAttrValueVo;
    }

    public static SaleAttrValueVo ofDTO(SkuSaleAttributeValueDTO saleAttributeValueDTO) {
        SaleAttrValueVo saleAttrValueVo = new SaleAttrValueVo();
        saleAttrValueVo.setAttrValue(saleAttributeValueDTO.getAttrValue());
        saleAttrValueVo.setAttrName(saleAttributeValueDTO.getAttrName());
        return saleAttrValueVo;
    }
}
