package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-12-01 11:34
 * @Description:
 */
@TypeDoc(
        description = "查询租户spu预览数据",
        authors = {"liuxun05"}
)
@Data
@ApiModel("查询租户spu预览数据")
public class TenantSpuPreviewRequest {

    @FieldDoc(
            description = "预览类型列表，1-数量单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "预览类型列表")
    @NotNull
    private List<Integer> typeList;

    @FieldDoc(
            description = "商品spu编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品spu编码")
    @NotNull
    private String spuId;


}
