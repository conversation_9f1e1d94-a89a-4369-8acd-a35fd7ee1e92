package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.sankuai.meituan.waimai.service.order.common.util.Skip32;

public class Skip32Wrapper {
    static byte[] key = new byte[]{82, 27, 101, -80, 78, 14, 123, -128, 83, 22};
    public static long encViewIdWithRange(byte version, int datetime, int poiId) {
        int versionAndDatetime = version << 16 | datetime;
        long datetimeAndVersionFirstPart = (long) (versionAndDatetime & 983040);
        long datetimeAndVersionSecondPart = (long) (versionAndDatetime & '\uffff');
        int poiIdSkip32 = Skip32.encrypt(poiId, key);
        long poiIdFirstPart = (long) poiIdSkip32 & 4294901760L;
        long poiIdSecondPart = (long) poiIdSkip32 & 65535L;
        long viewId = datetimeAndVersionFirstPart << 32 | poiIdFirstPart << 16 | datetimeAndVersionSecondPart << 16 | poiIdSecondPart;
        return viewId;
    }

}
