package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryTenantSkuCartonMeasureRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/11
 */
@TypeDoc(
        description = "sku箱规转换列表查询参数"
)
@Data
public class QueryCartonMeasureRequest {
    @FieldDoc(
            description = "sku列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "sku列表", required = true)
    public List<String> skuIdList;

    public QueryTenantSkuCartonMeasureRequest toQueryTenantSkuCartonMeasure(Long tenantId){
        return new QueryTenantSkuCartonMeasureRequest(tenantId, this.getSkuIdList());
    }
}
