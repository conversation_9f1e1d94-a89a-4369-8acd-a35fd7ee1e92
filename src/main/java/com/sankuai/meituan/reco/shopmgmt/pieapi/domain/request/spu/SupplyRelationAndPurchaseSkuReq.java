package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.linz.boot.util.Assert;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SupplyRelationAndPurchaseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.SupplyRelationUtils;
import com.sankuai.meituan.shangou.saas.common.utils.AssertUtil;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022/07/05
 */
@Getter
@Setter
public class SupplyRelationAndPurchaseSkuReq {
    @FieldDoc(description = "门店id")
    private Long poiId;

    @FieldDoc(description = "spuId")
    @ApiModelProperty(value = "spuId", required = true)
    private String spuId;

    @FieldDoc(description = "供货关系设置列表")
    @ApiModelProperty(value = "供货关系设置列表")
    private List<SupplyRelationSetVo> supplyRelationSetVoList;

    @FieldDoc(description = "采购商品设置属性列表")
    @ApiModelProperty(value = "采购商品设置属性列表")
    private List<PurchaseSkuSetPropertyVo> purchaseSkuSetPropertyVos;

    public void selfCheck() {
        AssertUtil.isTrue(poiId != null && poiId != 0, "门店编号不能为空");
        AssertUtil.notBlank(spuId, "商品spuId不能为空");
    }

    @Getter
    @Setter
    public class PurchaseSkuSetPropertyVo {

        @FieldDoc(description = "商品id")
        private String skuId;

        @FieldDoc(description = "采购状态 1:正常采购  2:停止采购")
        private Integer purchaseStatus;

        @FieldDoc(description = "采购模式 1:统采 2:地采")
        private Integer purchaseType;

        public void selfCheck() {
            Assert.throwIfTrue(StringUtils.isBlank(skuId), "商品id不合法");
            Assert.throwIfTrue(Objects.isNull(purchaseStatus), "采购状态不合法");
            Assert.throwIfTrue(Objects.isNull(purchaseType), "采购模式不合法");
        }
    }

    @Getter
    @Setter
    public class SupplyRelationSetVo {

        @FieldDoc(description = "供货方id")
        @ApiModelProperty(value = "供货商id", required = true)
        private Long supplierId;

        @FieldDoc(description = "采购商品skuId")
        @ApiModelProperty(value = "采购商品skuId", required = true)
        private String skuId;

        @FieldDoc(description = "采购单位")
        @ApiModelProperty(value = "采购单位", required = true)
        private String supplyUnit;

        @FieldDoc(description = "采购单位与基本单位的转换比例")
        @ApiModelProperty(value = "采购单位与基本单位的转换比例", required = true)
        private Double supplyUnitRatio;

        @FieldDoc(description = "采购单价，元 可支持6位小数")
        @ApiModelProperty(value = "采购单价", required = true)
        private Double supplyUnitPrice;

        @FieldDoc(description = "最小起订量")
        @ApiModelProperty(value = "最小起订量", required = true)
        private Integer minOrderQuantity;

        @FieldDoc(description = "是否设置为主供 1-是 0-否")
        private Integer masterFlag;

        @FieldDoc(description = "箱规编码")
        private String supplyUnitCode;

        @FieldDoc(description = "订货倍数")
        private Integer orderMultiple;

        public void selfCheck() {
            AssertUtil.isTrue(poiId != null && poiId != 0, "门店编号不能为空");
            AssertUtil.isTrue(supplierId != null && supplierId != 0, "供货商不能为空");
            AssertUtil.notBlank(skuId, "采购规格商品skuId不能为空");
            AssertUtil.notBlank(supplyUnit, "采购单位不能为空");
            AssertUtil.betweenClose(supplyUnitRatio, 0, 99999, "采购单位比例不合法");
            AssertUtil.betweenClose(minOrderQuantity, 1, 99999, "最小起订量不合法");

            supplyUnitPrice = SupplyRelationUtils.checkAndRoundSupplyUnitPrice(supplyUnitPrice);
        }
    }

    public void instantiateSupplyRelationSetVoList(List<SupplyRelationAndPurchaseVO> supplyRelationAndPurchaseVOS) {
        this.supplyRelationSetVoList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(supplyRelationAndPurchaseVOS)) {
            return;
        }
        supplyRelationAndPurchaseVOS.stream()
                .filter(item -> item.getSkuId() != null)
                .filter(item -> CollectionUtils.isNotEmpty(item.getSupplyRelations()))
                .forEach(item ->
                {
                    for (SupplyRelationAndPurchaseVO.SkuSupplyRelationVO supplyRelationVO : item.getSupplyRelations()) {
                        SupplyRelationSetVo supplyRelationSetVo = new SupplyRelationSetVo();
                        supplyRelationSetVo.setOrderMultiple(supplyRelationVO.getOrderMultiple());
                        supplyRelationSetVo.setSkuId(item.getSkuId());
                        supplyRelationSetVo.setSupplierId(supplyRelationVO.getSupplierId());
                        supplyRelationSetVo.setSupplyUnitPrice(supplyRelationVO.getSupplyUnitPrice());
                        supplyRelationSetVo.setSupplyUnitRatio(supplyRelationVO.getSupplyUnitRatio());
                        supplyRelationSetVo.setSupplyUnit(supplyRelationVO.getSupplyUnit());
                        supplyRelationSetVo.setSupplyUnitCode(supplyRelationVO.getSupplyUnitCode());
                        supplyRelationSetVo.setMinOrderQuantity(supplyRelationVO.getMinOrderQuantity());
                        supplyRelationSetVo.setMasterFlag(supplyRelationVO.getMasterFlag());
                        supplyRelationSetVoList.add(supplyRelationSetVo);
                    }
                });
    }

    public void instantiatePurchaseSkuSetPropertyVos(List<SupplyRelationAndPurchaseVO> supplyRelationAndPurchaseVOS) {
        this.purchaseSkuSetPropertyVos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(purchaseSkuSetPropertyVos)) {
            return;
        }

        supplyRelationAndPurchaseVOS.stream()
                .filter(item -> item.getSkuId() != null)
                .filter(item -> item.getPurchaseStatus() != null && item.getPurchaseType() != null)
                .forEach(item -> {
                    PurchaseSkuSetPropertyVo purchaseSkuSetPropertyVo = new PurchaseSkuSetPropertyVo();
                    purchaseSkuSetPropertyVo.setSkuId(item.getSkuId());
                    purchaseSkuSetPropertyVo.setPurchaseType(item.getPurchaseType());
                    purchaseSkuSetPropertyVo.setPurchaseStatus(item.getPurchaseStatus());
                    purchaseSkuSetPropertyVos.add(purchaseSkuSetPropertyVo);
                });
    }


}
