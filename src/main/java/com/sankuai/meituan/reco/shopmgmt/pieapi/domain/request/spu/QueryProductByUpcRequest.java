package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: <EMAIL>
 * @Date: 2021-03-01 14:33
 * @Description:
 */
@TypeDoc(
        description = "查询标品库、租户商品池、门店商品请求参数"
)
@Data
@ApiModel("查询标品库、租户商品池、门店商品请求参数")
public class QueryProductByUpcRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "upc", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "upc")
    @NotNull
    private String upc;

    @FieldDoc(
            description = "是否需要查询标品库，扫码查询true,搜索查询false", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否需要查询标品库")
    @NotNull
    private Boolean needQuerySP;


    @FieldDoc(
            description = "商品名称，", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品名称")
    private String name;

    @FieldDoc(
            description = "租户是否开启审核", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户是否开启审核")
    @NotNull
    private Boolean needTenantAudit;

}
