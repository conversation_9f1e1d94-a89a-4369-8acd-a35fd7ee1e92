package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.config;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2019/11/20
 * @description 账号履约相关的配置
 */
@TypeDoc(
        description = "账号门店履约配置信息"
)
@ApiModel("账号门店履约配置信息")
@Data
public class AccountStoreFulfillConfigVO {

    @FieldDoc(
            description = "门店ID，对应中台门店ID，对应履约系统内部是offlineStoreId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店ID，对应中台门店ID，对应履约系统内部是offlineStoreId", required = true)
    public long storeId;

    @FieldDoc(
            description = "接单模式, 0:没有接单阶段，1:手动接单，2:自动接单", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "接单模式, 0:没有接单阶段，1:手动接单，2:自动接单", required = true)
    public int orderAcceptMode;

    @FieldDoc(
            description = "领取模式, 0:不需要领取，1:需要领取", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "领取模式, 0:不需要领取，1:需要领取", required = true)
    public int pickTaskReceiveMode;

    @FieldDoc(
            description = "拣货模式, 0:按区域拣货，1:按单拣货", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货模式, 0:按区域拣货，1:按单拣货", required = true)
    public int pickTaskMode;

    @FieldDoc(
            description = "合流模式, 0:不需要合流，1:需要合流", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "合流模式, 0:不需要合流，1:需要合流", required = true)
    public int mergeTaskMode;



}
