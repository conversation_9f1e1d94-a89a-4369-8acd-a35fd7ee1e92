package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description: 修改线上商品前台分类请求
 * @author: liyu44
 * @create: 2020-02-04
 **/
@TypeDoc(
        description = "修改线上商品前台分类请求"
)
@Data
@ApiModel("修改线上商品前台分类请求")

public class ChangeChannelSkuFrontCategoryRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "渠道门店商品前台分类参数集合", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道门店商品前台分类参数集合", required = true)
    @NotNull
    private List<ChannelStoreSkuFrontCategoryVO> channelStoreSkuFrontCategoryList;
}
