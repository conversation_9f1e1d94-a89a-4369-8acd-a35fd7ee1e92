package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreCategoryCreateRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.List;

@TypeDoc(
        description = "创建店内分类参数",
        authors = {"zhouyan32"}
)
@Data
@ApiModel("创建店内分类参数")
public class CreateFrontCategoryRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId = 100;


    @FieldDoc(
            description = "分类名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分类名称", required = true)
    private String name;

    @FieldDoc(
            description = "父分类Id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "父分类Id")
    private String parentCode;


    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.channelId == null) {
            throw new CommonLogicException("渠道id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }

    public ChannelStoreCategoryCreateRequest to(User user) {
        ChannelStoreCategoryCreateRequest channelStoreCategoryCreateRequest = new ChannelStoreCategoryCreateRequest();
        channelStoreCategoryCreateRequest.setTenantId(user.getTenantId());
        channelStoreCategoryCreateRequest.setStoreId(this.storeId);
        channelStoreCategoryCreateRequest.setChannelId(this.channelId);
        channelStoreCategoryCreateRequest.setName(this.name);
        if (StringUtils.isNotBlank(this.parentCode)) {
            channelStoreCategoryCreateRequest.setParentCategoryId(Long.parseLong(this.parentCode));
        }
        channelStoreCategoryCreateRequest.setOperatorId(user.getAccountId());
        channelStoreCategoryCreateRequest.setOperator(user.getOperatorName());
       return channelStoreCategoryCreateRequest;
    }
}