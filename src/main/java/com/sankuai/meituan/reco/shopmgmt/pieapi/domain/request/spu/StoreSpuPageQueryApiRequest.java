package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.OperateSourceTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ocms.OCMSUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelSpuStopSellingStatusEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelStoreFrontCategoryFactorDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.NonErpStoreSpuPageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Title: StoreSpuPageQueryApiRequest
 * @Description: 门店商品SPU分页查询请求参数
 * @Author: zhaolei12
 * @Date: 2020/4/17 6:21 下午
 */
@TypeDoc(
        description = "门店商品SPU分页查询请求参数",
        authors = {"zhaolei12"}
)
@Data
@ApiModel("门店商品SPU分页查询请求参数")
public class StoreSpuPageQueryApiRequest {

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第几页", required = true)
    @NotNull
    private Integer page = 1;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    @NotNull
    private Integer size = 20;

    @FieldDoc(
            description = "搜索参数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "搜索参数")
    private String keyword;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "上下线状态 -1-未上传 1-已上架 2-未上架", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "上下线状态  -1-未上传 1-已上架 2-未上架")
    private Integer spuStatus = 0;

    @FieldDoc(
            description = "商品状态列表 0-未上线 1-已上架 2-未上架", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品状态列表  0-未上线 1-已上架 2-未上架")
    private  List<Integer> spuStatusList;

    @FieldDoc(
            description = "渠道范围 0-指定渠道 1-任一渠道 2-全部渠道", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道范围 0-指定渠道 1-任一渠道 2-全部渠道")
    private Integer scope = 2;

    @FieldDoc(
            description = "渠道id列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道id列表")
    private List<Integer> channelIds;

    @FieldDoc(
            description = "商家类目编号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商家类目编号")
    private List<String> categories;

    @FieldDoc(
            description = "分渠道门店分类信息 channelId -> storeCategoryId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分渠道门店分类信息")
    private Map<Integer, String> channelStoreCategoryMap;

    @FieldDoc(
            description = "门店店内分类，仅非总部管品业态使用，单渠道不用", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店店内分类")
    private List<String> storeCategoryIdList;

    @FieldDoc(
            description = "分类状态  1-未分类（未上线+分类=未分类 2-有分类(已上线-分类=未分类）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类状态  1-未分类（未上线+分类=未分类 2-有分类(已上线-分类=未分类）")
    private Integer hasStoreCategory = 0;

    @FieldDoc(
            description = "标签id列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "标签id列表")
    private List<Long> tagIds;

    @FieldDoc(description = "美团渠道商品是否可售,0-全部 1-可售 2-不可售")
    @ApiModelProperty(value = "美团渠道商品是否可售")
    private Integer mtAllowSale = 0;

    @FieldDoc(
            description = "是否设置力荐： 0 不过滤，1 设置，2 未设置"
    )
    @ApiModelProperty(value = "是否设置力荐")
    public int hasSpecialty;

    @FieldDoc(
            description = "是否设置可售时间： 0 不过滤，1 设置，2 未设置"
    )
    @ApiModelProperty(value = "是否设置可售时间")
    public int hasAvailableTimes;

    @FieldDoc(
            description = "是否设置卖点： 0 不过滤，1 设置，2 未设置"
    )
    @ApiModelProperty(value = "是否设置卖点")
    public int hasSellPoint;

    @FieldDoc(
            description = "是否设置售罄： 0 不过滤，1 设置，2 未设置"
    )
    @ApiModelProperty(value = "是否设置售罄")
    public int hasSellOut;

    @FieldDoc(
            description = "后台类为未分类，1是，其他否", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "后台类为未分类，1是，其他否")
    private Integer hasNoBackendCategory = 0;

    @FieldDoc(
            description = "是否设置平台停售： 0 不过滤，1 设置，2 未设置"
    )
    @ApiModelProperty(value = "是否设置平台停售")
    public int hasStopSelling;

    @FieldDoc(
            description = "是否设置审核驳回： 0 不过滤，1 设置，2 未设置"
    )
    @ApiModelProperty(value = "是否设置审核驳回")
    public int hasAuditRejected;

    @FieldDoc(
            description = "是否设置审核中： 0 不过滤，1 设置，2 未设置"
    )
    @ApiModelProperty(value = "是否设置审核中")
    public int hasAuditing;

    @FieldDoc(
            description = "是否查询平台下架商品：true-查询平台下架商品，false-不行"
    )
    @ApiModelProperty(value = "是否查询平台下架商品")
    public Boolean platformSoldOut;

    @FieldDoc(
            description = "是否需要查询渠道商品id, channelSpuId表示渠道侧商品的实体id"
    )
    @ApiModelProperty(value = "是否需要查询渠道商品id")
    public Boolean needChannelSpuId;

    @FieldDoc(
            description = "是否异步查询商品活动信息"
    )
    @ApiModelProperty(value = "是否异步查询商品活动信息")
    public Boolean asyncQueryPromotion;

    @FieldDoc(
            description = "库存状态：0-全部，1-已售罄，2-未售罄，3-库存不足"
    )
    @ApiModelProperty(value = "库存状态：0-全部，1-已售罄，2-未售罄，3-库存不足")
    private List<Integer> stockStatusList;

    @FieldDoc(
            description = "审核渠道id"
    )
    @ApiModelProperty(value = "审核渠道id")
    private Integer auditChannelId;

    @FieldDoc(
            description = "审核状态，与审核渠道id共同生效"
    )
    @ApiModelProperty(value = "审核状态，与审核渠道id共同生效")
    private Integer auditStatus;

    @FieldDoc(
            description = "商品异常码"
    )
    @ApiModelProperty(value = "商品异常码")
    private List<String> abnormalCodes;

    @FieldDoc(
            description = "是否存在不可售异常筛选条件"
    )
    @ApiModelProperty(value = "是否存在不可售异常筛选条件")
    private Boolean hasNoSaleAbnormal;

    @FieldDoc(
            description = "主档商品创建来源"
    )
    @ApiModelProperty(value = "主档商品创建来源")
    private Integer tenantCreateSource;

    @FieldDoc(
            description = "商品类型，1-常规品，2-套装"
    )
    @ApiModelProperty(value = "商品类型，1-常规品，2-套装")
    private String skuSaleType;

    @FieldDoc(
            description = "子商品列表"
    )
    @ApiModelProperty(value = "子商品列表")
    private List<String> childSkuIdList;

    public static NonErpStoreSpuPageQueryRequest toBizRpcRequest(StoreSpuPageQueryApiRequest request,
                                                                 List<String> lastAbnormalCodes,
                                                                 User user,
                                                                 String boothId,
                                                                 boolean isStoreManagementTenant,
                                                                 boolean merchantChargeSpu,
                                                                 boolean isNotMerchantCharge) {
        NonErpStoreSpuPageQueryRequest storeSpuPageQueryRequest = new NonErpStoreSpuPageQueryRequest();
        storeSpuPageQueryRequest.setTenantId(user.getTenantId());
        storeSpuPageQueryRequest.setStoreIds(Lists.newArrayList(request.getStoreId()));
        storeSpuPageQueryRequest.setPage(request.getPage());
        storeSpuPageQueryRequest.setPageSize(request.getSize());
        storeSpuPageQueryRequest.setKeywords(request.getKeyword());
        storeSpuPageQueryRequest.setCategoryCodeList(request.getCategories());

        List<ChannelStoreFrontCategoryFactorDTO> frontCategoryFactors = Lists.newArrayList();
        if (MapUtils.isNotEmpty(request.getChannelStoreCategoryMap())) {
            request.getChannelStoreCategoryMap().forEach((key, value) -> {
                ChannelStoreFrontCategoryFactorDTO frontCategoryFactor = new ChannelStoreFrontCategoryFactorDTO();
                frontCategoryFactor.setChannelId(key);
                frontCategoryFactor.setFrontCategoryId(ConverterUtils.nonNullConvert(value, Long::parseLong));
                frontCategoryFactor.setStoreId(request.getStoreId());

                frontCategoryFactors.add(frontCategoryFactor);
            });
        }

        if (merchantChargeSpu || isNotMerchantCharge || isStoreManagementTenant) {
            if(CollectionUtils.isNotEmpty(request.getStoreCategoryIdList())){
                //老版本中前端进入门店商品列表页传的是full_category，如果用户不杀掉APP重进，仍然会使用老版本，
                // 避免后续全量出现较多报错，这里特殊兼容一下
                String categoryId = request.getStoreCategoryIdList().get(0);
                if (Objects.equals(categoryId, "full_category")){
                    storeSpuPageQueryRequest.setStoreCategoryIds(new ArrayList<>());
                    Cat.logEvent("full_category", String.valueOf(user.getTenantId()));
                }
                //正常逻辑
                else {
                    storeSpuPageQueryRequest.setStoreCategoryIds(request.getStoreCategoryIdList());
                }
            }
        } else {
            storeSpuPageQueryRequest.setFrontCategoryList(frontCategoryFactors);
        }


        if (Strings.isNotEmpty(boothId)) {
            storeSpuPageQueryRequest.setBoothIds(Collections.singletonList(Long.valueOf(boothId)));
        }
        if (Objects.nonNull(request.getSpuStatus())) {
            storeSpuPageQueryRequest.setSpuStatus(request.getSpuStatus());
        }
        if(CollectionUtils.isNotEmpty(request.getSpuStatusList())){
            storeSpuPageQueryRequest.setSpuStatusList(request.getSpuStatusList());
        }
        storeSpuPageQueryRequest.setMode(request.getScope());
        storeSpuPageQueryRequest.setChannelIds(request.getChannelIds());
        storeSpuPageQueryRequest.setTagIds(request.getTagIds());
        storeSpuPageQueryRequest.setHasStoreCategory(request.getHasStoreCategory());
        storeSpuPageQueryRequest.setHasAvailableTimes(request.getHasAvailableTimes());
        storeSpuPageQueryRequest.setHasSpecialty(request.getHasSpecialty());
        storeSpuPageQueryRequest.setHasSellPoint(request.getHasSellPoint());
        storeSpuPageQueryRequest.setHasSellOut(request.getHasSellOut());
        storeSpuPageQueryRequest.setStockStatusList(request.getStockStatusList());
        storeSpuPageQueryRequest.setHasStopSelling(request.getHasStopSelling());
        storeSpuPageQueryRequest.setOperateSource(OperateSourceTypeEnum.APP.getCode());
        storeSpuPageQueryRequest.setStopSellingStatus(ChannelSpuStopSellingStatusEnum.getStopSellingStatusList(request.getHasStopSelling()));
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.ExtendedAttributesDTO extendedAttributesDTO =
                new com.sankuai.meituan.shangou.empower.productbiz.client.dto.ExtendedAttributesDTO();
        extendedAttributesDTO.setSkuSale(false);
        storeSpuPageQueryRequest.setExtendedAttributes(extendedAttributesDTO);
        // 美团渠道商品审核状态
        storeSpuPageQueryRequest.setAuditStatusList(OCMSUtils.toAuditStatusList(request.getMtAllowSale(),
                request.getHasAuditRejected(),request.getHasAuditing()));
        storeSpuPageQueryRequest.setNormAuditStatusList(OCMSUtils.toNormAuditStatusList(request.getMtAllowSale(),
                request.getHasAuditRejected(),request.getHasAuditing()));
        storeSpuPageQueryRequest.setHasNoBackendCategory(request.getHasNoBackendCategory());
        storeSpuPageQueryRequest.setPlatformSoldOutFlag(request.getPlatformSoldOut());
        storeSpuPageQueryRequest.setNeedChannelSpuId(request.getNeedChannelSpuId());
        storeSpuPageQueryRequest.setAuditChannelId(request.getAuditChannelId());
        storeSpuPageQueryRequest.setMergedAuditStatus(request.getAuditStatus());
        storeSpuPageQueryRequest.setAbnormalCodes(lastAbnormalCodes);
        storeSpuPageQueryRequest.setHasNoSaleAbnormal(request.getHasNoSaleAbnormal());
        storeSpuPageQueryRequest.setTenantCreateSource(request.getTenantCreateSource());
        if(StringUtils.isNotBlank(request.getSkuSaleType())){
            storeSpuPageQueryRequest.setSkuSaleTypeList(Lists.newArrayList(request.getSkuSaleType()));
        }
        storeSpuPageQueryRequest.setChildSkuIdList(request.getChildSkuIdList());
        storeSpuPageQueryRequest.setOperatorId(user.getAccountId());
        return storeSpuPageQueryRequest;
    }
}
