package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ReviewOperateRecordVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ReviewTenantSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.AiRecommendVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelCategorySimpleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSaleAttrInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.NameSupplementInfoVO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ReviewOperateRecordDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuReviewDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.review.ReviewTenantSpuDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description: 提报商品对象
 * @author: WangSukuan
 * @create: 2020-03-10
 **/
@TypeDoc(
        description = "提报商品对象"
)
@Data
@ApiModel("提报商品对象")
@NoArgsConstructor
public class SkuReviewVO {

    @FieldDoc(description = "租户ID")
    @ApiModelProperty(name = "租户ID")
    private Long tenantId;

    @FieldDoc(description = "门店id")
    @ApiModelProperty(name = "门店id")
    private Long storeId;

    @FieldDoc(description = "门店名称")
    @ApiModelProperty(name = "门店名称")
    private String storeName;

    @FieldDoc(description = "商品名称")
    @ApiModelProperty(name = "商品名称")
    private String skuName;

    @FieldDoc(description = "商品sku")
    @ApiModelProperty(name = "商品sku")
    private String skuId;

    @FieldDoc(description = "商品图片")
    @ApiModelProperty(name = "商品图片")
    private List<String> imageUrls;

    @FieldDoc(description = "分类编码")
    @ApiModelProperty(name = "分类编码")
    private String categoryCode;

    @FieldDoc(description = "分类编码路径")
    @ApiModelProperty(name = "分类编码路径")
    private String categoryCodePath;

    @FieldDoc(description = "分类名称")
    @ApiModelProperty(name = "分类名称")

    private String categoryName;

    @FieldDoc(description = "分类名称路径")
    @ApiModelProperty(name = "分类名称路径")
    private String categoryNamePath;

    @FieldDoc(description = "品牌编码")
    @ApiModelProperty(name = "品牌编码")
    private String brandCode;

    @FieldDoc(description = "品牌名称")
    @ApiModelProperty(name = "品牌名称")
    private String brandName;

    @FieldDoc(description = "品牌编码路径")
    @ApiModelProperty(name = "品牌编码路径")
    private String brandCodePath;

    @FieldDoc(description = "品牌名称路径")
    @ApiModelProperty(name = "品牌名称路径")
    private String brandNamePath;

    @FieldDoc(description = "产地")
    @ApiModelProperty(name = "产地")
    private String productionArea;

    @FieldDoc(description = "upc")
    @ApiModelProperty(name = "upc")
    private List<String> upcList;

    @FieldDoc(description = "申请人ID")
    @ApiModelProperty(name = "申请人ID")
    private Long applicantId;

    @FieldDoc(description = "申请人名称")
    @ApiModelProperty(name = "申请人名称")
    private String applicantName;

    @FieldDoc(description = "申请人手机号")
    @ApiModelProperty(name = "申请人手机号")
    private String applicantPhone;

    @FieldDoc(description = "售卖单位")
    @ApiModelProperty(name = "售卖单位")
    private String baseUnit;

    @FieldDoc(description = "售卖单位")
    @ApiModelProperty(name = "售卖单位")
    private List<ReviewOperateRecordVO> reviewOperateRecordVOS;

    @FieldDoc(description = "提报记录id")
    @ApiModelProperty(name = "提报记录id")
    private Long recordId;

    @FieldDoc(description = "审核状态")
    @ApiModelProperty(name = "审核状态")
    private Integer reviewStatus;

    @FieldDoc(description = "拒绝原因")
    @ApiModelProperty(name = "拒绝原因")
    private String rejectReason;

    @FieldDoc(description = "称重类型")
    @ApiModelProperty(name = "称重类型(1:称重计重 3:非称重/标品)")
    private Integer weightType;

    @FieldDoc(description = "提报方式")
    @ApiModelProperty(name = "提报方式(0:普通提报 1:标品匹配提报)")
    private Integer applyType;

    @FieldDoc(description = "以克(g)为单位的商品重量")
    @ApiModelProperty(name = "以克(g)为单位的商品重量")
    private Integer weight;

    @FieldDoc(description = "带单位的商品重量")
    @ApiModelProperty(name = "带单位的商品重量")
    private String weightForUnit;

    @FieldDoc(description = "重量单位")
    @ApiModelProperty(name = "重量单位")
    private String weightUnit;

    @FieldDoc(description = "美团渠道类目末级编码")
    @ApiModelProperty(name = "美团渠道类目末级编码")
    private String channelCategoryCode;

    @FieldDoc(description = "美团渠道类目末级名称")
    @ApiModelProperty(name = "美团渠道类目末级名称")
    private String channelCategoryName;

    @FieldDoc(description = "美团渠道类目编码路径")
    @ApiModelProperty(name = "美团渠道类目编码路径")
    private String channelCategoryCodePath;

    @FieldDoc(description = "美团渠道类目名称路径")
    @ApiModelProperty(name = "美团渠道类目名称路径")
    private String channelCategoryNamePath;

    @FieldDoc(description = "总部零售价")
    @ApiModelProperty(name = "总部零售价")
    private Double suggestPrice;

    @FieldDoc(description = "提报商品sku")
    @ApiModelProperty(name = "提报商品sku")
    private List<ReviewTenantSkuVO> skuList;

    @FieldDoc(description = "规格类型")
    @ApiModelProperty(name = "规格类型")
    private Integer specType;

    @FieldDoc(description = "审核备注")
    @ApiModelProperty(name = "审核备注")
    private String reviewRemark;

    @FieldDoc(description = "提报类型")
    @ApiModelProperty(name = "提报类型")
    private String submitType;

    @FieldDoc(description = "提报类型名称")
    @ApiModelProperty(name = "提报类型名称")
    private String submitTypeName;

    @FieldDoc(description = "提报备注")
    @ApiModelProperty(name = "提报备注")
    private String submitRemark;

    @FieldDoc(description = "商品名称")
    @ApiModelProperty(name = "商品名称")
    private String name;

    @FieldDoc(description = "商品spu")
    @ApiModelProperty(name = "商品spu")
    private String spuId;

    @FieldDoc(description = "是否已建品：1-已建品（已创建门店商品），0-未建品")
    @ApiModelProperty(name = "是否已建品：1-已建品（已创建门店商品），0-未建品")
    private Integer alreadyCreatePoiSpu;

    @FieldDoc(description = "是否已上线任意渠道：1-是，0-否")
    @ApiModelProperty(name = "是否已上线任意渠道：1-是，0-否")
    private Integer onlineAnyChannel;

    @FieldDoc(description = "渠道类目")
    @ApiModelProperty(name = "渠道类目")
    private List<ChannelCategorySimpleVO> channelCategories;

    @FieldDoc(description = "京东到家渠道销售列表，多规格传入")
    @ApiModelProperty(name = "京东到家渠道销售列表，多规格传入")
    private List<SaleAttrVo> jdSaleAttrList;

    @FieldDoc(description = "销售属性信息")
    @ApiModelProperty(name = "销售属性信息")
    private List<ChannelSaleAttrInfoVO> channelSaleAttrInfoList;

    @FieldDoc(description = "AI推荐信息")
    @ApiModelProperty(name = "AI推荐信息")
    private AiRecommendVO aiRecommendVO;
    @FieldDoc(
            description = "商品卖点"
    )
    @ApiModelProperty(value = "商品卖点")
    private String sellingPoint;

    @FieldDoc(description = "审批中心审核单号")
    @ApiModelProperty(name = "审批中心审核单号")
    private String reviewNo;

    @FieldDoc(description = "待审核人- 权限使用")
    @ApiModelProperty(name = "待审核人- 权限使用")
    private List<String> pendingReviewUsers;
    @FieldDoc(description = "待审核人 - 展示使用")
    @ApiModelProperty(name = "待审核人 - 展示使用")
    private List<String> pendingQnhDisplayName;

    @FieldDoc(description = "商品名称补充语信息")
    @ApiModelProperty(value = "商品名称补充语信息")
    private NameSupplementInfoVO nameSupplementInfo;

    public SkuReviewVO buildSkuReviewVO(SkuReviewDTO skuReviewDTO){

        this.setTenantId(skuReviewDTO.getTenantId());
        this.setStoreId(skuReviewDTO.getStoreId());
        this.setStoreName(skuReviewDTO.getStoreName());
        if (StringUtils.isNotEmpty(skuReviewDTO.getSkuName())){
            this.setSkuName(skuReviewDTO.getSkuName().trim());
        }
        this.setSkuId(skuReviewDTO.getSkuId());
        this.setImageUrls(skuReviewDTO.getImageUrls() == null ? Lists.newArrayList() : skuReviewDTO.getImageUrls());

        this.setCategoryCode(skuReviewDTO.getCategoryCode());
        this.setCategoryCodePath(skuReviewDTO.getCategoryCodePath());
        this.setCategoryName(skuReviewDTO.getCategoryName());
        this.setCategoryNamePath(skuReviewDTO.getCategoryNamePath());

        this.setBrandCode(skuReviewDTO.getBrandCode());
        this.setBrandCodePath(skuReviewDTO.getBrandCodePath());
        this.setBrandName(skuReviewDTO.getBrandName());
        this.setBrandNamePath(skuReviewDTO.getBrandNamePath());

        if (StringUtils.isNotEmpty(skuReviewDTO.getProductionArea())){
            this.setProductionArea(skuReviewDTO.getProductionArea().trim());
        }
        if (CollectionUtils.isNotEmpty(skuReviewDTO.getUpcList())){
            skuReviewDTO.getUpcList().forEach(String::trim);
        }
        this.setUpcList(skuReviewDTO.getUpcList() == null ? Lists.newArrayList() : skuReviewDTO.getUpcList());
        this.setApplicantId(skuReviewDTO.getApplicantId());
        this.setApplicantName(skuReviewDTO.getApplicantName());
        this.setApplicantPhone(skuReviewDTO.getApplicantPhone());
        if (StringUtils.isNotEmpty(skuReviewDTO.getBaseUnit())){
            this.setBaseUnit(skuReviewDTO.getBaseUnit().trim());
        }
        this.setRecordId(skuReviewDTO.getRecordId());

        List<ReviewOperateRecordVO> reviewOperateRecordVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuReviewDTO.getOperateRecords())){
            this.setReviewOperateRecordVOS(reviewOperateRecordVOList);
            return this;
        }
        for (ReviewOperateRecordDTO reviewOperateRecordDTO : skuReviewDTO.getOperateRecords()){
            ReviewOperateRecordVO reviewOperateRecordVO = new ReviewOperateRecordVO().buildReviewOperateRecordVO(reviewOperateRecordDTO);
            reviewOperateRecordVOList.add(reviewOperateRecordVO);
        }
        this.setReviewOperateRecordVOS(reviewOperateRecordVOList);
        this.setReviewStatus(skuReviewDTO.getReviewStatus());
        this.setRejectReason(skuReviewDTO.getRejectReason());
        this.setWeightType(skuReviewDTO.getWeightType());
        this.setApplyType(skuReviewDTO.getApplyType());

        // 适配平台商品审核新增
        this.setWeight(skuReviewDTO.getWeight());
        this.setWeightForUnit(skuReviewDTO.getWeightForUnit());
        this.setWeightUnit(skuReviewDTO.getWeightUnit());
        this.setChannelCategoryCode(skuReviewDTO.getChannelCategoryCode());
        this.setChannelCategoryCodePath(skuReviewDTO.getChannelCategoryCodePath());
        this.setChannelCategoryName(skuReviewDTO.getChannelCategoryName());
        this.setChannelCategoryNamePath(skuReviewDTO.getChannelCategoryNamePath());

        // 主档零售价
        this.setSuggestPrice(skuReviewDTO.getSuggestPrice());

        return this;

    }

    public SkuReviewVO buildSkuReviewVO(ReviewTenantSpuDTO reviewTenantSpuDTO) {

        this.setTenantId(reviewTenantSpuDTO.getTenantId());
        this.setStoreId(reviewTenantSpuDTO.getStoreId());
        this.setStoreName(reviewTenantSpuDTO.getStoreName());

        this.setName(Optional.ofNullable(reviewTenantSpuDTO.getName()).map(StringUtils::trim).orElse(null));
        // 兼容历史字段，其实是spuName
        this.setSkuName(Optional.ofNullable(reviewTenantSpuDTO.getName()).map(StringUtils::trim).orElse(null));

        this.setSpuId(reviewTenantSpuDTO.getSpuId());
        // 兼容历史字段，其实是spuId
        this.setSkuId(reviewTenantSpuDTO.getSpuId());

        this.setImageUrls(reviewTenantSpuDTO.getImageUrls() == null ? Collections.emptyList() : reviewTenantSpuDTO.getImageUrls());

        this.setCategoryCode(reviewTenantSpuDTO.getCategoryCode());
        this.setCategoryCodePath(reviewTenantSpuDTO.getCategoryCodePath());
        this.setCategoryName(reviewTenantSpuDTO.getCategoryName());
        this.setCategoryNamePath(reviewTenantSpuDTO.getCategoryNamePath());

        this.setBrandCode(reviewTenantSpuDTO.getBrandCode());
        this.setBrandCodePath(reviewTenantSpuDTO.getBrandCodePath());
        this.setBrandName(reviewTenantSpuDTO.getBrandName());
        this.setBrandNamePath(reviewTenantSpuDTO.getBrandNamePath());

        if (StringUtils.isNotEmpty(reviewTenantSpuDTO.getProductionArea())) {
            this.setProductionArea(reviewTenantSpuDTO.getProductionArea().trim());
        }

        this.setApplicantId(reviewTenantSpuDTO.getApplicantId());
        this.setApplicantName(reviewTenantSpuDTO.getApplicantName());
        this.setApplicantPhone(reviewTenantSpuDTO.getApplicantPhone());

        this.setRecordId(reviewTenantSpuDTO.getRecordId());
        List<ReviewOperateRecordVO> reviewOperateRecordVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(reviewTenantSpuDTO.getOperateRecords())) {
            for (com.sankuai.meituan.shangou.empower.productbiz.client.dto.review.ReviewOperateRecordDTO reviewOperateRecordDTO : reviewTenantSpuDTO.getOperateRecords()) {
                ReviewOperateRecordVO reviewOperateRecordVO = new ReviewOperateRecordVO().buildReviewOperateRecordVO(reviewOperateRecordDTO);
                reviewOperateRecordVOList.add(reviewOperateRecordVO);
            }
            this.setReviewOperateRecordVOS(reviewOperateRecordVOList);
        }

        this.setSubmitType(reviewTenantSpuDTO.getSubmitType());
        this.setSubmitTypeName(reviewTenantSpuDTO.getSubmitTypeName());
        this.setSubmitRemark(reviewTenantSpuDTO.getSubmitRemark());

        this.setReviewStatus(reviewTenantSpuDTO.getReviewStatus());
        this.setRejectReason(reviewTenantSpuDTO.getRejectReason());
        this.setReviewRemark(reviewTenantSpuDTO.getReviewRemark());

        this.setWeightType(reviewTenantSpuDTO.getWeightType());
        this.setApplyType(reviewTenantSpuDTO.getApplyType());

        // 兼容旧字段，填充美团渠道类目
        ListUtils.emptyIfNull(reviewTenantSpuDTO.getChannelCategoryDTOS())
                .stream()
                .filter(channelCategoryDTO -> Objects.equals(channelCategoryDTO.getChannelId(), EnhanceChannelType.MT.getChannelId()))
                .findFirst()
                .ifPresent(channelCategoryDTO -> {
                    this.setChannelCategoryCode(channelCategoryDTO.getChannelCategoryCode());
                    this.setChannelCategoryCodePath(channelCategoryDTO.getChannelCategoryCodePath());
                    this.setChannelCategoryName(channelCategoryDTO.getChannelCategoryName());
                    this.setChannelCategoryNamePath(channelCategoryDTO.getChannelCategoryNamePath());
                });

        this.setAlreadyCreatePoiSpu(reviewTenantSpuDTO.getAlreadyCreatePoiSpu());
        this.setOnlineAnyChannel(reviewTenantSpuDTO.getOnlineAnyChannel());

        // 原来扁平模型上sku维度的字段,在适配多规格后,取第一个sku的兼容展示
        ListUtils.emptyIfNull(reviewTenantSpuDTO.getSkuList())
                .stream()
                .findFirst()
                .ifPresent(firstSku -> {
                    if (CollectionUtils.isNotEmpty(firstSku.getUpcList())) {
                        this.setUpcList(firstSku.getUpcList()
                                .stream()
                                .map(StringUtils::trim)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList()));
                    }

                    if (StringUtils.isNotEmpty(firstSku.getBaseUnit())) {
                        this.setBaseUnit(firstSku.getBaseUnit().trim());
                    }

                    // 总部零售价分转元
                    this.setSuggestPrice(firstSku.getSuggestPrice());

                    this.setWeight(firstSku.getWeight());
                    this.setWeightForUnit(firstSku.getWeightForUnit());
                    this.setWeightUnit(firstSku.getWeightUnit());
                });

        // skuList
        this.setSkuList(Fun.map(reviewTenantSpuDTO.getSkuList(), ReviewTenantSkuVO::fromReviewTenantSkuDTO));
        this.setSpecType(reviewTenantSpuDTO.getSpecType());

        // 渠道类目，其中类目名称以外的字段主要用于重新提报场景
        this.setChannelCategories(Fun.map(reviewTenantSpuDTO.getChannelCategoryDTOS(), ChannelCategorySimpleVO::fromChannelCategoryDTO));

        // 提报的京东类目属性
        this.setJdSaleAttrList(Fun.map(reviewTenantSpuDTO.getJdSaleAttrList(), SaleAttrVo::fromDTO));
        this.setChannelSaleAttrInfoList(Fun.map(reviewTenantSpuDTO.getChannelSaleAttributes(), ChannelSaleAttrInfoVO::of));
        this.setSellingPoint(reviewTenantSpuDTO.getSellingPoint());
        this.setAiRecommendVO(AiRecommendVO.fetchAiRecommendBizDTO(reviewTenantSpuDTO.getAiRecommendInfo()));
        this.setSellingPoint(reviewTenantSpuDTO.getSellingPoint());
        this.setNameSupplementInfo(NameSupplementInfoVO.fromBizDTO(reviewTenantSpuDTO.getNameSupplementInfo()));

        //审批中心关联的审核单号，下一个节点待审核人
        this.setReviewNo(reviewTenantSpuDTO.getReviewNo());
        this.setPendingReviewUsers(reviewTenantSpuDTO.getPendingReviewUsers());
        this.setPendingQnhDisplayName(reviewTenantSpuDTO.getPendingQnhDisplayName());
        return this;
    }
}
