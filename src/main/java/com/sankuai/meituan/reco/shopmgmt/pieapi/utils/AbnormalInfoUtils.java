package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.meituan.shangou.saas.tenant.thrift.common.enums.YesNoEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSpuVO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelNormAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelSpuStopSellingStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.HasStopSellingEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.AbnormalProductInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSpuBizDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.ChannelSpuAbnormalEnum;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/7
 * @Description
 */
public class AbnormalInfoUtils {
    public static List<AbnormalProductInfoDTO> buildAbnormalProductInfoList(List<Integer> mtAuditStatusList, List<Integer> mtNormAuditStatusList,
                                                                      List<Integer> mtStopSellingStatusList, List<Integer> mtSoldOutFlagList) {
        List<AbnormalProductInfoDTO> abnormalProductInfos = new ArrayList<>();
        // 审核状态
        if (CollectionUtils.isNotEmpty(mtAuditStatusList)){
            Set<Integer> allAuditingCodeSet = new HashSet<>(ChannelAuditStatusEnum.getAllAuditingStatusCodes());
            Set<Integer> allRejectCodeSet = new HashSet<>(ChannelAuditStatusEnum.getAllRejectStatusCodes());
            addAbnormalProductInfo(mtAuditStatusList, allAuditingCodeSet, abnormalProductInfos, allRejectCodeSet);
        }
        if (CollectionUtils.isNotEmpty(mtNormAuditStatusList)){
            Set<Integer> allNormAuditingCodeSet = new HashSet<>(ChannelNormAuditStatusEnum.getAllAuditingStatusCodes());
            Set<Integer> allNormRejectCodeSet = new HashSet<>(ChannelNormAuditStatusEnum.getAllRejectStatusCodes());
            addAbnormalProductInfo(mtNormAuditStatusList, allNormAuditingCodeSet, abnormalProductInfos, allNormRejectCodeSet);
        }

        // 停售状态
        if (CollectionUtils.isNotEmpty(mtStopSellingStatusList)){
            Set<Integer> allStopSellingCodeSet = new HashSet<>(ChannelSpuStopSellingStatusEnum.getStopSellingStatusList(HasStopSellingEnum.STOPSELLING.getCode()));
            if (!CollectionUtils.intersection(allStopSellingCodeSet, mtStopSellingStatusList).isEmpty()){
                addAbnormalProductInfo(ChannelSpuAbnormalEnum.MT_STOP_SELLING, abnormalProductInfos);
            }
        }

        // 下架状态
        if (Optional.ofNullable(mtSoldOutFlagList).orElse(new ArrayList<>())
                .stream().anyMatch(YesNoEnum::isYes)){
            addAbnormalProductInfo(ChannelSpuAbnormalEnum.MT_SOLD_OUT, abnormalProductInfos);
        }
        return abnormalProductInfos;
    }

    public static  void addAbnormalProductInfo(List<Integer> auditStatusList, Set<Integer> allAuditingCodeSet, List<AbnormalProductInfoDTO> abnormalProductInfos, Set<Integer> allRejectCodeSet) {
        if (!CollectionUtils.intersection(allAuditingCodeSet, auditStatusList).isEmpty()){
            addAbnormalProductInfo(ChannelSpuAbnormalEnum.MT_AUDITING, abnormalProductInfos);
        }
        if (!CollectionUtils.intersection(allRejectCodeSet, auditStatusList).isEmpty()){
            addAbnormalProductInfo(ChannelSpuAbnormalEnum.MT_AUDIT_REJECT, abnormalProductInfos);
        }
    }

    public static  void addAbnormalProductInfo(ChannelSpuAbnormalEnum mtAuditing, List<AbnormalProductInfoDTO> abnormalProductInfos) {
        AbnormalProductInfoDTO abnormalProductInfoDTO = new AbnormalProductInfoDTO();
        abnormalProductInfoDTO.setChannelId(mtAuditing.getChannelId());
        abnormalProductInfoDTO.setAbnormalCode(mtAuditing.getCode());
        abnormalProductInfoDTO.setAbnormalDesc(mtAuditing.getDefaultDesc());
        abnormalProductInfoDTO.setAbnormalHandleMsg("");
        abnormalProductInfos.add(abnormalProductInfoDTO);
    }

    public static void fillFranchiseAbnormalInfoForBiz(TenantSpuBizDTO tenantSpuDTO, TenantSpuVO vo) {
        List<AbnormalProductInfoDTO> abnormalProductInfos = new ArrayList<>();

        // 加盟门店
        abnormalProductInfos.addAll(buildAbnormalProductInfoList(tenantSpuDTO.getFranchiseeMtAuditStatusList(),
                tenantSpuDTO.getFranchiseeMtNormAuditStatusList(), tenantSpuDTO.getFranchiseeMtStopSellingStatusList(),
                tenantSpuDTO.getFranchiseeMtSoldOutFlagList()));

        // 自营门店
        abnormalProductInfos.addAll(buildAbnormalProductInfoList(tenantSpuDTO.getAuditStatusList(), tenantSpuDTO.getNormAuditStatusList(), null, null));

        // 去重
        abnormalProductInfos = new ArrayList<>(abnormalProductInfos.stream()
                .collect(Collectors.toMap(AbnormalProductInfoDTO::getAbnormalCode, item->item, (k1,k2)->k1)).values());

        vo.setAbnormalCodes(abnormalProductInfos.stream().map(AbnormalProductInfoDTO::getAbnormalCode).collect(Collectors.toList()));
        vo.setAbnormalMarkMsg(getAbnormalMarkMsg(abnormalProductInfos));
    }

    public static String getAbnormalMarkMsg(List<AbnormalProductInfoDTO> abnormalProductInfos) {
        if (CollectionUtils.isEmpty(abnormalProductInfos)) {
            return null;
        }
        AbnormalProductInfoDTO firstAbnormalProduct = abnormalProductInfos.get(0);
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.findByChannelId(firstAbnormalProduct.getChannelId());
        String channelName = channelTypeEnum == null ? "" : channelTypeEnum.getChannelName();
        String abnormalMarkMsg = channelName + firstAbnormalProduct.getAbnormalDesc();
        if (abnormalProductInfos.size() == 1) {
            return abnormalMarkMsg;
        } else {
            return abnormalProductInfos.size() + "项问题";
        }
    }
}
