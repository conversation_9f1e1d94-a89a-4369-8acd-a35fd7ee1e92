package com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ocms;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StoreSkuStockVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.StoreVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.common.dto.PageInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.StoreSpuCountResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.StoreSpuPageQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StoreSkuSaleStockDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/5/15
 * desc: 中台request转换工具类
 */
@Slf4j
public class ResponseConvertUtils {

    //查询已上架已下架商品数，分两次查询，一次查询已上架，一次查询已下架
    public static QueryOnSaleAndOffSaleCountResponseVO convertQueryOnSaleAndOffSaleCount(StoreSpuCountResponse response1
            ,StoreSpuCountResponse response2, StoreSpuCountResponse response3) {
        QueryOnSaleAndOffSaleCountResponseVO queryOnSaleAndOffSaleCountResponseVO = new QueryOnSaleAndOffSaleCountResponseVO();
        queryOnSaleAndOffSaleCountResponseVO.setOnSaleCount(response1.getCount());
        queryOnSaleAndOffSaleCountResponseVO.setOffSaleCount(response2.getCount());
        queryOnSaleAndOffSaleCountResponseVO.setNotOnlineCount(response3.getCount());
        return queryOnSaleAndOffSaleCountResponseVO;
    }

    private static List<StoreSkuSaleStockDTO> convertStoreSkuSaleStockDTOList(List<StoreSkuStockVO>skuStockList, Long storeId){
        if(Objects.isNull(skuStockList))
            return Lists.newArrayList();
        List<StoreSkuSaleStockDTO> storeSkuSaleStockDTOList = Lists.newArrayList();
        for(StoreSkuStockVO storeSkuStockVO:skuStockList)
        {
            StoreSkuSaleStockDTO storeSkuSaleStockDTO = new StoreSkuSaleStockDTO();
            storeSkuSaleStockDTO.setStoreId(storeId);
            storeSkuSaleStockDTO.setQuantity(storeSkuStockVO.getStock());
            storeSkuSaleStockDTO.setSkuCode(storeSkuStockVO.getSkuId());
            storeSkuSaleStockDTO.setCustomizeStockFlag(storeSkuStockVO.getCustomizeStockFlag());
            storeSkuSaleStockDTO.setAutoResumeInfiniteStock(storeSkuStockVO.getAutoResumeInfiniteStock());
            storeSkuSaleStockDTOList.add(storeSkuSaleStockDTO);
        }
        return storeSkuSaleStockDTOList;
    }
    //按天查未报价/未上架商品
    public static StoreSpuPageQueryResponseVO convertStoreSpuPageQuery(StoreSpuPageQueryResponse response){
        StoreSpuPageQueryResponseVO storeSpuPageQueryResponseVO = new StoreSpuPageQueryResponseVO();
        //pageInfoVO赋值
        storeSpuPageQueryResponseVO.setPageInfo(convertpageInfoVO(response.getPageInfoDTO()));
        //StoreSpuVO赋值
        storeSpuPageQueryResponseVO.setStoreSpuList(convertStoreSpuVO(response.getStoreSpuList()));
        return storeSpuPageQueryResponseVO;
    }
    //PageInfoVO转换
    public static PageInfoVO convertpageInfoVO(PageInfoDTO pageInfoDTO){
        if(Objects.isNull(pageInfoDTO)){
            return null;
        }
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(pageInfoDTO.getPage());
        pageInfoVO.setSize(pageInfoDTO.getSize());
        pageInfoVO.setTotalPage(pageInfoDTO.getTotalPage());
        pageInfoVO.setTotalSize(pageInfoDTO.getTotal());
        return pageInfoVO;
    }

    ////StoreSpuVO赋值
    public static List<StoreSpuVO> convertStoreSpuVO(List<StoreSpuDTO> storeSpuList){
        if(Objects.isNull(storeSpuList))
            return Lists.newArrayList();
        List<StoreSpuVO> storeSpuVOList = Lists.newArrayList();
        for(StoreSpuDTO storeSpuDTO : storeSpuList){
            StoreSpuVO storeSpuVO = new StoreSpuVO();
            storeSpuVO.setTenantId(storeSpuDTO.getTenantId());
            storeSpuVO.setSpuId(storeSpuDTO.getSpuId());
            storeSpuVO.setName(storeSpuDTO.getName());
            storeSpuVO.setImages(storeSpuDTO.getImageUrls());
            storeSpuVO.setWeightType(storeSpuDTO.getWeightType());
            if(Objects.nonNull(storeSpuDTO.getSpecialty())){
                storeSpuVO.setSpecialty(storeSpuDTO.getSpecialty());
            }
            storeSpuVO.setChannelId2FirstFrontCategoryNameMap(storeSpuDTO.getChannelId2FirstFrontCategoryNameMap());
            //设置门店信息storevo
            storeSpuVO.setStore(convertStoreSpuDTOToStoreVO(storeSpuDTO.getStore()));
            //设置商品标签
            storeSpuVO.setTagList(convertTagList(storeSpuDTO.getTagList()));
            //设置门店商品sku列表
            storeSpuVO.setStoreSkuList(convertStoreSkuDTOToStoreSkuVO(storeSpuDTO));
            //设置渠道商品sku列表
            storeSpuVO.setChannelSpuList(convertStoreSkuDTOToChannelSpuVOList(storeSpuDTO.getChannelSpuList()));
            storeSpuVOList.add(storeSpuVO);
        }
        return storeSpuVOList;
    }
    //设置门店信息storevo
    public static StoreVO convertStoreSpuDTOToStoreVO(StoreDTO storeDTO) {
        StoreVO storeVO = new StoreVO();
        storeVO.setStoreName(storeDTO.getName());
        storeVO.setStoreId(storeDTO.getStoreId());
        return storeVO;
    }
    //设置商品标签,待完善
    public static List<SpuTagVO> convertTagList(List<SpuTagDTO> tagList){
        if(Objects.isNull(tagList)){
            return Lists.newArrayList();
        }
        List<SpuTagVO> spuTagVOList = Lists.newArrayList();
        for(SpuTagDTO spuTagDTO :tagList){
            SpuTagVO spuTagVO = new SpuTagVO();
            spuTagVO.setTagId(spuTagDTO.getTagId());
            spuTagVO.setTagName(spuTagDTO.getTagName());
            spuTagVOList.add(spuTagVO);
        }
        return spuTagVOList;
    }

    //设置门店商品sku列表
    public static List<StoreSkuVO> convertStoreSkuDTOToStoreSkuVO(StoreSpuDTO storeSpuDTO){
        if(Objects.isNull(storeSpuDTO.getStoreSkuList())){
            return Lists.newArrayList();
        }
        List<StoreSkuDTO> storeSkuDTOList = storeSpuDTO.getStoreSkuList();
        List<StoreSkuVO> storeSkuVOList = Lists.newArrayList();
        for(StoreSkuDTO storeSkuDTO : storeSkuDTOList){
            StoreSkuVO storeSkuVO = new StoreSkuVO();
            storeSkuVO.setSkuId(storeSkuDTO.getSkuId());
            storeSkuVO.setUpcInfo(storeSkuDTO.getUpcList());
            storeSkuVO.setSpec(storeSkuDTO.getSpec());
            storeSkuVO.setWeight(storeSkuDTO.getWeight());
            if (storeSkuDTO.getStorePrice() != null) {
                storeSkuVO.setStorePrice(MoneyUtils.yuanToCent(storeSkuDTO.getStorePrice()));
            }
            storeSkuVO.setSaleUnit(storeSkuDTO.getUnit());
            storeSkuVO.setCustomizeStockFlag(storeSkuDTO.getCustomizeStockFlag());
            storeSkuVO.setCustomizeStockQuantity(storeSkuDTO.getCustomizeStockQuantity());
            storeSkuVO.setAutoResumeInfiniteStock(storeSkuDTO.getAutoResumeInfiniteStock());
            storeSkuVO.setMonthSaleAmount(storeSpuDTO.getMonthSaleAmount());
            storeSkuVO.setReviewStatus(storeSpuDTO.getReviewStatus());
            storeSkuVOList.add(storeSkuVO);
        }
        return storeSkuVOList;
    }
    //设置渠道商品sku列表
    public static List<ChannelSpuVO> convertStoreSkuDTOToChannelSpuVOList(List<ChannelSpuDTO> channelSpuList){
        if(Objects.isNull(channelSpuList)){
            return Lists.newArrayList();
        }
        List<ChannelSpuVO> ChannelSpuVOList = Lists.newArrayList();
        for(ChannelSpuDTO channelSpuDTO : channelSpuList){
            ChannelSpuVO channelSpuVO = new ChannelSpuVO();
            channelSpuVO.setChannelId(channelSpuDTO.getChannelId());
            channelSpuVO.setOnline(channelSpuDTO.getOnline());
            channelSpuVO.setSpuStatus(channelSpuDTO.getSpuStatus());
            List<ChannelSkuVO> channelSkuVOList = Lists.newArrayList();
            for(ChannelSkuDTO channelSkuDTO :channelSpuDTO.getChannelSkuList()){
                ChannelSkuVO channelSkuVO = new ChannelSkuVO();
                if (channelSkuDTO.getPrice() != null) {
                    channelSkuVO.setPrice(MoneyUtils.yuanToCent(channelSkuDTO.getPrice()));
                }
                if (channelSkuDTO.getStock() != null) {
                    channelSkuVO.setStock(channelSkuDTO.getStock().longValue());
                }
                channelSkuVOList.add(channelSkuVO);
            }
            channelSpuVO.setChannelSkuList(channelSkuVOList); //将DTP转化为VO
        }
        return ChannelSpuVOList;
    }

    public static QueryOffSaleAndUnquotedCountResponseVO convertQueryOffSaleAndUnquotedCount(StoreSpuCountResponse storeSpuCountResponse,Integer day){
        QueryOffSaleAndUnquotedCountResponseVO queryOffSaleAndUnquotedCountResponseVO = new QueryOffSaleAndUnquotedCountResponseVO();
        Map<Integer, Integer> dayCountMap = new HashMap<>();
        dayCountMap.put(day,storeSpuCountResponse.getCount());
        queryOffSaleAndUnquotedCountResponseVO.setDayCountMap(dayCountMap);
        return queryOffSaleAndUnquotedCountResponseVO;
    }
}
