package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 问题商品操作类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CompareSpuTypeEnum {

    SPU_MISS(100, 0, "商品缺失", 0),
    WAIMAI_SPU_MISS(101, 2, "商家端商品缺失", 1),
    SGP_SPU_MISS(102, 3, "牵牛花商品缺失", 0),

    SPEC_MISS(200, 0, "规格缺失", 0),
    WAIMAI_SPEC_MISS(201, 4, "商家端规格缺失", 2),
    SGP_SPEC_MISS(202, 5, "牵牛花规格缺失", 3),

    TOP_BASE_DIFF(300, 0, "基础信息不一致", 0),
    BASE_INFO_DIFF(301, 7, "基础信息不一致", 4),
    SALSE_INFO_DIFF(302, 8, "销售信息不一致", 5),

    TOP_PRICE_DIFF(400, 0, "价格不一致", 0),
    PRICE_DIFF(401, 6, "价格不一致", 6);

    /**
     * app端使用的大类编号
     */
    private Integer code;
    
    /**
     * web端使用的不一致对比类型编号，@see SpuCompareTypeEnum
     */
    private Integer webCode;

    private String desc;

    /**
     * 在app端门店商品列表页，问题商品会带有"商品存在x项问题"，点击该提示后只能提示一项最重要的不一致
     * 该项不一致是子类不一致, 当前展示优先级:
     * 蔬果派商品缺失(102) > 商家端商品缺失(101) > 商家端规格缺失(201) > 蔬果派规格缺失(202) > 基础信息不一致(301) > 销售信息不一致(302) > 价格不一致(401)
     */
    private Integer sort;

    public static CompareSpuTypeEnum findByCode(int code) {
        for (CompareSpuTypeEnum compareSpuTypeEnum : CompareSpuTypeEnum.values()) {
            if (compareSpuTypeEnum.getCode() == code) {
                return compareSpuTypeEnum;
            }
        }
        return null;
    }

    public static CompareSpuTypeEnum findByWebCode(int webCode) {
        for (CompareSpuTypeEnum compareSpuTypeEnum : CompareSpuTypeEnum.values()) {
            if (compareSpuTypeEnum.getWebCode() == webCode) {
                return compareSpuTypeEnum;
            }
        }
        return null;
    }

}
