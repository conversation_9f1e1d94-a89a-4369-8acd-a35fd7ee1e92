package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.QualificationPicturesRule;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 特殊管控商品资质，仅美团渠道生效
 */
@Data
public class ControlQuaTipVo {
    private String imageTitle;

    private Long imgTitleId;

    private String url;

    private String imgDesc;


    public static List<ControlQuaTipVo> ofQualificationPicturesRule(QualificationPicturesRule qualificationPicturesRule) {
        if (qualificationPicturesRule == null || CollectionUtils.isEmpty(qualificationPicturesRule.getQualificationPicturesInfos())) {
            return Collections.emptyList();
        }

        return qualificationPicturesRule.getQualificationPicturesInfos().stream().map(item -> {
            ControlQuaTipVo controlQuaTipVo = new ControlQuaTipVo();
            controlQuaTipVo.setImageTitle(item.getTitle());
            controlQuaTipVo.setUrl(item.getExample());
            controlQuaTipVo.setImgDesc(item.getDescription());
            return controlQuaTipVo;
        }).collect(Collectors.toList());
    }
}
