package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;


import org.apache.commons.lang3.StringUtils;

public enum TenantBizModeEnum {
    /**
     * 三方代运营
     */
    VEGETABLE_MARKET("vegetable_market", "三方代运营"),

    CONVENIENCE_STORE("convenience_store", "便利店"),

    SUPER_MARKET("supermarket", "商超"),

    CAI_DA_QUAN("caidaquan", "菜大全"),

    DRUNK_HORSE("waima", "歪马送酒"),

    FLAGSHIP_STORE("MTSG_FlagshipStore", "美团旗舰店"),

    UNMANNED_MICRO_WAREHOUSE("UnmannedMicroWarehouse", "无人微仓"),

    MEDICINE_UNMANNED_WAREHOUSE("MedicineUnmannedWarehouse", "医药无人仓"),

    UNKNOWN(StringUtils.EMPTY, "未知");

    private String code;
    private String desc;

    TenantBizModeEnum(String key, String desc) {
        this.code = key;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static TenantBizModeEnum codeOf(String code) {
        if (StringUtils.isEmpty(code)) {
            return UNKNOWN;
        }
        for (TenantBizModeEnum modeEnum : values()) {
            if (modeEnum.getCode().equals(code)) {
                return modeEnum;
            }
        }
        return UNKNOWN;
    }
}
