package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Title: PicAuditApiRequest
 * @Description: 商品图片白底检测接口请求参数
 * @Author: zhaolei12
 * @Date: 2020/7/27 2:36 下午
 */
@TypeDoc(
        description = "商品图片白底检测接口请求参数"
)
@Data
@ApiModel("商品图片白底检测接口请求参数")
public class PicAuditApiRequest {

    @FieldDoc(
            description = "图片地址", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "图片地址", required = true)
    @NotNull
    private String imgUrl;
}
