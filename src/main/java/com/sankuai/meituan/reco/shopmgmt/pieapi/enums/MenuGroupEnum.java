package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * MenuGroupEnum
 *
 * <AUTHOR>
 * @since 2022-11-01 3:01 PM
 *  todo group info include description, sub-authCode and color info used by front end to display
 */
@Getter
@Slf4j
@AllArgsConstructor
public enum MenuGroupEnum {
    // The order is used as rank sequence, so do not change
    ALL("待办", ""),
    ORDER("履约", "['#F2FAFF','#ECF7FF']"),
    INVENTORY("库存", "['#F5F2FF','#F1ECFF']"),
    GOODS("商品", "['#FAF9EB','#FAF3D4']"),
    PURCHASE("采购", "['#F0FFF2','#E1FAE4']"),
    FINANCE("财务", "['#FFF2EB','#FFE6D9']"),
    WAIMA("歪马", "['#F2FAFF', '#ECF7FF']"),
    SMILE("微笑行动", "['#FFF2EB', '#FFE6D9']"),
    ;

    private final String desc;
    private final String color;

    public static MenuGroupEnum ofGroupDesc(String groupDesc) {
        for (MenuGroupEnum moduleEnum : MenuGroupEnum.values()) {
            if (Objects.nonNull(moduleEnum.getDesc()) && moduleEnum.getDesc().equals(groupDesc)) {
                return moduleEnum;
            }
        }
        log.warn("工作台未找到任务类型对应的Group, groupDesc:{}", groupDesc);
        return null;
    }
}
