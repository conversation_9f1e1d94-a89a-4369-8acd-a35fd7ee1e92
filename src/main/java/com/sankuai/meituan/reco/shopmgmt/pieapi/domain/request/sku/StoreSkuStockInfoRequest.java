package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Version 1.0
 * @Date 2022/5/10 8:41 下午
 */
@Data
@Slf4j
public class StoreSkuStockInfoRequest {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID")
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "实体ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "实体ID")
    @NotNull
    private Long entityId;

    @FieldDoc(
            description = "实体类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "实体类型")
    @NotNull
    private Integer entityType;

    @FieldDoc(
            description = "商品ID列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品ID列表")
    @NotNull
    private List<String> skuIds;

    public void validate() {
        if (this.tenantId == null) {
            throw new ParamException("租户id不能为空");
        }
        if (this.entityId == null) {
            throw new ParamException("实体id不能为空");
        }
        if (this.entityType == null) {
            throw new ParamException("实体类型不能为空");
        }
        if (CollectionUtils.isEmpty(skuIds)) {
            throw new ParamException("商品id列表不能为空");
        }
        if (skuIds.size() > 100) {
            throw new ParamException("商品id数量不能大于100");
        }
    }
}
