package com.sankuai.meituan.reco.shopmgmt.pieapi.config.interceptors;

import com.meituan.mtrace.Tracer;
import com.meituan.shangou.saas.order.management.client.utils.BusinessIdTracer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.LazyAppFullStoreIds;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.LoginWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.reco.store.management.thrift.UserInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
public class ApiMethodParamInterceptor extends HandlerInterceptorAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiMethodParamInterceptor.class);
    @Resource
    private TenantWrapper tenantWrapper;

    @Resource
    private LoginWrapper loginWrapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {

            String token = request.getHeader(ProjectConstants.TOKEN);
            String eToken = request.getHeader(ProjectConstants.E_TOKEN);
            if (StringUtils.isNotBlank(eToken)){
                token = eToken;
            }
            String uuid = request.getHeader(ProjectConstants.UUID);
            String appId = request.getHeader(ProjectConstants.APPID);
            String appVersion = request.getHeader(ProjectConstants.APPVERSION);
            String os = request.getHeader(ProjectConstants.OS);
            String jsVersion = request.getHeader(ProjectConstants.JSVERSION);
            String mrnApp = request.getHeader(ProjectConstants.MRN_APP);
            String mrnVersion = request.getHeader(ProjectConstants.MRN_VERSION);
            String storeIds = request.getHeader(ProjectConstants.STORE_ID);
            String appChannel = request.getHeader(ProjectConstants.APP_CHANNEL);
            String authId = request.getHeader(ProjectConstants.AUTH_ID);
            String subAuthId = request.getHeader(ProjectConstants.SUB_AUTH_ID);

            ApiMethodParamThreadLocal.setAppId(appId);
            ApiMethodParamThreadLocal.setUuid(uuid);
            ApiMethodParamThreadLocal.setAppVersion(appVersion);
            ApiMethodParamThreadLocal.setOs(os);
            ApiMethodParamThreadLocal.setJsVersion(jsVersion);
            ApiMethodParamThreadLocal.setToken(token);
            ApiMethodParamThreadLocal.setMrnApp(mrnApp);
            ApiMethodParamThreadLocal.setMrnVersion(mrnVersion);
            ApiMethodParamThreadLocal.setStoreIds(storeIds);
            ApiMethodParamThreadLocal.setLazyAppFullStoreIds(new LazyAppFullStoreIds(loginWrapper::getCurrentUserFullStoreList));
            ApiMethodParamThreadLocal.setAppChannel(appChannel);
            ApiMethodParamThreadLocal.setAuthId(resolveAuthId(authId, subAuthId));


            boolean needNewest = false;
            if (handler instanceof HandlerMethod) {
                // 只处理Controller
                HandlerMethod handlerMethod = (HandlerMethod) handler;
                Class<?> type = handlerMethod.getBeanType();
                AuthInfo authInfo = new AuthInfo(type.getDeclaredAnnotation(Auth.class),
                        handlerMethod.getMethodAnnotation(Auth.class));
                // 如果未找到@Auth注解,不进行后续操作
                if (!authInfo.needAuth()) {
                    return true;
                }
            }

            SessionInfo sessionInfo = SessionContext.getCurrentSession();
            if (sessionInfo == null || sessionInfo.getAccountId() <= 0 || sessionInfo.getStaffId() <= 0) {
                LOGGER.error("SessionContext.getCurrentSession not value, sessionInfo = {}", sessionInfo);
                return true;
            }

            if (StringUtils.isNotBlank(token)) {
                TenantWrapper.EmployeeBaseInfo operator = tenantWrapper.getEmployeeInfo(sessionInfo.getTenantId(), sessionInfo.getStaffId());

                ApiMethodParamThreadLocal.setUserInfo(new User(operator.getEmployeeName(),sessionInfo.getTenantId(), sessionInfo.getStaffId(),
                        sessionInfo.getAccountId(), sessionInfo.getAccountType(), sessionInfo.getAccountName(), sessionInfo.getEpAccountId(), operator.getEmployeePhone()));
            } else {
                LOGGER.error("ApiMethodParamInterceptor.preHandle user not login,token={}", token);
            }

            log.info("pre handle api method, url={}, common token={}, request ={}", request.getRequestURI(), token, ApiMethodParamThreadLocal.getIdentityInfo());

        } catch (CommonLogicException e) {
            log.error("obtain userInfo error", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("ApiMethodParamInterceptor.preHandle set threadLocal wrong", e);

        }
        return true;
    }

    private Integer resolveAuthId(String authId, String subAuthId) {
        if (StringUtils.isNotBlank(subAuthId)) {
            return Integer.valueOf(subAuthId);
        }

        if (StringUtils.isNotBlank(authId)) {
            return Integer.valueOf(authId);
        }

        return null;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) {
        try {
            super.afterCompletion(request, response, handler, ex);
        } catch (Exception e) {
            LOGGER.error("ApiMethodParamInterceptor.afterCompletion remove thread local error request:{}", request, e);
        } finally {
            ApiMethodParamThreadLocal.getInstance().remove();
            SessionContext.destroy();
        }
    }

    private static final class AuthInfo {
        private final boolean needAuth;

        private AuthInfo(Auth authOnClass, Auth authOnMethod) {
            if (authOnClass == null && authOnMethod == null) {
                needAuth = false;
            } else if (authOnMethod != null) {
                needAuth = true;
            } else {
                needAuth = true;
            }
        }

        @SuppressWarnings("WeakerAccess")
        public boolean needAuth() {
            return needAuth;
        }
    }
}
