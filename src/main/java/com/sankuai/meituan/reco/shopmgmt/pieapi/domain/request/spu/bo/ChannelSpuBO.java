package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.bo;

import lombok.*;

/**
 * @author: wang<PERSON>zhen
 * @date: 2020-05-11 14:31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelSpuBO {
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 商品编码
     */
    private String spuId;

    /**
     * 商品名称
     */
    private String spuName;

    /**
     * 门店编码
     */
    private Long poiId;


    /**
     * 门店名称
     */
    private String poiName;

    /**
     * 渠道编码
     */
    private int channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    public ChanelSpuPoiTuple toTuple() {
        return new ChanelSpuPoiTuple(spuId, poiId, channelId);
    }



    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    @EqualsAndHashCode
    @ToString
    public static class ChanelSpuPoiTuple {
        private String spuId;
        private Long poi;
        private int channelId;
    }
}
