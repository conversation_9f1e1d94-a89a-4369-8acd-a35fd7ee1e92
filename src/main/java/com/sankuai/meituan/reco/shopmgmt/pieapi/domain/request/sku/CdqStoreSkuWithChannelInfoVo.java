package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.priceconfig.StoreSkuPriceFilter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pricetrend.ChannelSkuPriceTrendVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pricetrend.StoreSkuWithChannelPriceTrendVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.saas.common.money.PriceUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @description: 门店商品明细
 * @author: liyu44
 * @create: 2020-02-03
 **/
@TypeDoc(
        description = "门店商品明细"
)
@Data
@ApiModel("门店商品明细")

public class CdqStoreSkuWithChannelInfoVo {

    @FieldDoc(
            description = "门店商品信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店商品信息", required = true)
    @NotNull
    private CdqStoreSkuVo cdqStoreSkuVo;

    @FieldDoc(
            description = "线上商品信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "线上商品信息", required = true)
    @NotNull
    private List<ChannelSkuDetailInfoVo> channelSkuDetailInfoVos;


    /**
     * 设置门店商品价格趋势信息
     * @param storeSkuPriceFilter 门店商品价格过滤器
     * @param storeSkuWithChannelPriceTrendVO 门店商品价格趋势
     * @param priceTrendPermissionMap 用户价格趋势权限
     */
    public void fillStoreSkuWithChannelPriceTrendInfo(
            StoreSkuPriceFilter storeSkuPriceFilter,
            StoreSkuWithChannelPriceTrendVO storeSkuWithChannelPriceTrendVO,
            Map<String, Boolean> priceTrendPermissionMap) {

        if (CollectionUtils.isEmpty(this.channelSkuDetailInfoVos)) {
            return;
        }

        this.channelSkuDetailInfoVos.forEach(channelSkuInfoVO -> {

            Integer channelId = channelSkuInfoVO.getChannelId();

            // 计算市斤价
            if (storeSkuPriceFilter.getCanCalculatePriceByWeight() && channelSkuInfoVO.getPrice() != null
                    && this.cdqStoreSkuVo.getWeight() != null) {
                long priceByFen = PriceUtils.yuan2Fen(channelSkuInfoVO.getPrice());
                long pricePer500gByFen = PriceUtils.calculatePriceByWeight(priceByFen, this.cdqStoreSkuVo.getWeight(), ProjectConstants.WEIGHT_500G);
                channelSkuInfoVO.setPricePer500g(PriceUtils.fen2Yuan(pricePer500gByFen));
            }

            // 判断是否显示价格趋势图标
            // 计算是否展示价格趋势图标
            // 若降级到直接展示价格趋势图标, 直接返回可以展示图标; 反之, 则需要判断是否有价格趋势数据
            // 备注：趋势图标展示不判断权限, 只判断是否有数据
            boolean priceTrendIconDirectShow = MccConfigUtil.isPriceTrendIconDirectShow();
            boolean hasPriceTrend = storeSkuWithChannelPriceTrendVO != null
                    && storeSkuWithChannelPriceTrendVO.isHasPriceTrend(channelSkuInfoVO.getChannelId());
            boolean showPriceTrendIcon = priceTrendIconDirectShow || hasPriceTrend;
            channelSkuInfoVO.setShowPriceTrendIcon(showPriceTrendIcon);

            // 设置渠道价格趋势, 需要根据权限过滤价格趋势数据
            ChannelSkuPriceTrendVO priceTrend = null;
            if (storeSkuWithChannelPriceTrendVO != null) {
                priceTrend = storeSkuWithChannelPriceTrendVO.generateChannelSkuPriceTrendVO(
                        channelId, priceTrendPermissionMap);
            }
            if (priceTrend == null) {
                priceTrend = ChannelSkuPriceTrendVO.empty();
            }
            channelSkuInfoVO.setPriceTrend(priceTrend);
        });
    }
}
