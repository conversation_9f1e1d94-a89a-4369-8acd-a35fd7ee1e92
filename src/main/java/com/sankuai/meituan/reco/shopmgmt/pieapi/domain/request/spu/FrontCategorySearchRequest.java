package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.StoreCategorySearchRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/6/23 8:49 下午
 **/
@TypeDoc(
        description = "店内分类搜索接口",
        authors = {"zhouyan32"}
)
@Data
@ApiModel("店内分类搜索接口")
public class FrontCategorySearchRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId = 100;

    @FieldDoc(
            description = "搜索关键词", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "搜索关键词", required = true)
    private String keyWord;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.channelId == null) {
            throw new CommonLogicException("渠道id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (StringUtils.isBlank(this.keyWord)) {
            throw new CommonLogicException("关键词不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }

    public StoreCategorySearchRequest to(User user) {
        StoreCategorySearchRequest storeCategorySearchRequest = new StoreCategorySearchRequest();
        storeCategorySearchRequest.setTenantId(user.getTenantId());
        storeCategorySearchRequest.setStoreId(this.storeId);
        storeCategorySearchRequest.setChannelId(this.channelId);
        storeCategorySearchRequest.setKeyWord(this.keyWord);

        return storeCategorySearchRequest;
    }
}
