package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Title: QueryTenantSkuBySpuIdApiRequest
 * @Description: 租户商品规格查询接口请求参数
 * @Author: zhaolei12
 * @Date: 2020/5/24 9:27 上午
 */
@TypeDoc(
        description = "租户商品规格查询接口请求参数"
)
@Data
@ApiModel("租户商品规格查询接口请求参数")
public class QueryTenantSkuBySpuIdApiRequest {

    @FieldDoc(
            description = "SPUID"
    )
    @ApiModelProperty(value = "SPUID")
    private String spuId;

    /**
     * 门店建品选择规格时会调用该接口，需求要求如果该门店对应的区域商品有自定义，需要返回自定义信息，因此需要
     * 传递门店ID，用于后端查询对应的区域商品信息进行填充
     */
    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID")
    private Long storeId;

}
