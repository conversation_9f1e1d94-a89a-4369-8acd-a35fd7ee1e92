package com.sankuai.meituan.reco.shopmgmt.pieapi.config;

import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.meituan.service.inf.kms.value.KMSStringValue;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.meituan.shangou.saas.common.storage.StorageService;
import com.sankuai.meituan.shangou.saas.common.storage.mss.MtCloudS3StorageService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2022/7/22
 */
@Configuration
public class KmsConfig {


    private static final String S3_ACCESSTOKEN = "s3_accessToken";


    @Value("${app.name}")
    private String appKey;


    @Bean
    public String s3AccessToken() throws KmsResultNullException {
        KMSStringValue kmsStringValue = new KMSStringValue();
        kmsStringValue.setAppKey(appKey);
        kmsStringValue.setName(S3_ACCESSTOKEN);
        kmsStringValue.setRetryCount(5);
        return kmsStringValue.get();
    }


    @Bean("lionPassword")
    public String lionPassword() throws KmsResultNullException {
        return Kms.getByName(appKey, "lion.password");
    }

}
