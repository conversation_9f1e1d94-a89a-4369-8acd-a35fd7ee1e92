package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "商品线上价格库存信息"
)
@Data
@ApiModel("商品线上价格库存信息")
public class  SkuPriceAndStock {

    @FieldDoc(
            description = "商品编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品编码", required = true)
    @NotNull
    private String skuId;

    @FieldDoc(
            description = "价格"
    )
    @ApiModelProperty(name = "价格")
    private Double price;

    @FieldDoc(
            description = "库存"
    )
    @ApiModelProperty(name = "库存")
    private Long stock;

    @FieldDoc(
            description = "渠道ID  -1-线下 100-美团 200-饿了么 300-京东", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "自定义库存标记 0：非自定义库存，1：自定义库存, 非无限库存模式下传0 默认为0"
    )
    @ApiModelProperty(name = "自定义库存标记")
    private Integer customizeStockFlag = 0;

    @FieldDoc(
            description = "第二天是否自动恢复无限库存 0-不自动恢复 1-自动恢复  默认为0"
    )
    @ApiModelProperty(name = "第二天是否自动恢复无限库存")
    private Integer autoResumeInfiniteStock = 0;
}

