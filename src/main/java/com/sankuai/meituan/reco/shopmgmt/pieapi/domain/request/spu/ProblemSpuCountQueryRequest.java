package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelNormAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelSpuStopSellingStatusEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryCompareTypeCountRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.diffcompare.QueryDiffCompareSpuRequest;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.PageQueryPoiSpuCommand;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;
import java.util.Objects;


@TypeDoc(
        name = "不一致商品统计查询请求",
        description = "不一致商品统计查询请求"
)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProblemSpuCountQueryRequest {

    @FieldDoc(description = "门店id", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "门店id")
    private Long storeId;

    public void validate() {
        Preconditions.checkArgument(Objects.nonNull(storeId) && storeId.compareTo(0L) > 0, "门店ID非法");
    }

    public QueryCompareTypeCountRequest to(long tenantId) {
        return QueryCompareTypeCountRequest.builder()
                .tenantId(tenantId)
                .storeIds(Lists.newArrayList(storeId))
                .build();

    }

    public PageQueryPoiSpuCommand toRequest(long tenantId) {
        PageQueryPoiSpuCommand spuCommand = new PageQueryPoiSpuCommand();
        spuCommand.setMerchantId(tenantId);
        spuCommand.setPoiIds(Lists.newArrayList(storeId));
        spuCommand.setAuditStatusList(ChannelAuditStatusEnum.getAllRejectStatusCodes());
        spuCommand.setNormAuditStatusList(ChannelNormAuditStatusEnum.getAllRejectStatusCodes());
        spuCommand.setPageSize(1);
        spuCommand.setPageNo(1);

        return spuCommand;
    }

    public PageQueryPoiSpuCommand toRequest(long tenantId, List<String> lastAbnormalCodes) {
        PageQueryPoiSpuCommand spuCommand = new PageQueryPoiSpuCommand();
        spuCommand.setMerchantId(tenantId);
        spuCommand.setPoiIds(Lists.newArrayList(storeId));
        spuCommand.setAbnormalCodes(lastAbnormalCodes);
        spuCommand.setPageSize(1);
        spuCommand.setPageNo(1);

        return spuCommand;
    }


    public PageQueryPoiSpuCommand toAuditingRequest(long tenantId) {
        PageQueryPoiSpuCommand spuCommand = new PageQueryPoiSpuCommand();
        spuCommand.setMerchantId(tenantId);
        spuCommand.setPoiIds(Lists.newArrayList(storeId));
        spuCommand.setAuditStatusList(Lists.newArrayList(ChannelAuditStatusEnum.AUDITING_FOR_BEFORE_RELEASE.getCode()));
        spuCommand.setNormAuditStatusList(Lists.newArrayList(ChannelNormAuditStatusEnum.AUDITING_FOR_BEFORE_RELEASE.getCode()));
        spuCommand.setPageSize(1);
        spuCommand.setPageNo(1);

        return spuCommand;
    }

    public PageQueryPoiSpuCommand toStopSellRequest(long tenantId) {
        PageQueryPoiSpuCommand spuCommand = new PageQueryPoiSpuCommand();
        spuCommand.setMerchantId(tenantId);
        spuCommand.setPoiIds(Lists.newArrayList(storeId));
        spuCommand.setStopSellingStatus(Lists.newArrayList(ChannelSpuStopSellingStatusEnum.INCOMPLETE.getCode()
                ,ChannelSpuStopSellingStatusEnum.UNQUALIFIED.getCode(),
                ChannelSpuStopSellingStatusEnum.INCOMPLETEANDUNQUALIFIED.getCode()));
        spuCommand.setPageSize(1);
        spuCommand.setPageNo(1);

        return spuCommand;
    }

    public PageQueryPoiSpuCommand toPlatformSoldOutRequest(long tenantId) {
        PageQueryPoiSpuCommand spuCommand = new PageQueryPoiSpuCommand();
        spuCommand.setMerchantId(tenantId);
        spuCommand.setPoiIds(Lists.newArrayList(storeId));
        spuCommand.setPlatformSoldOut(true);
        spuCommand.setPageSize(1);
        spuCommand.setPageNo(1);

        return spuCommand;
    }

    public QueryDiffCompareSpuRequest toDiffCompareSpuRequest(long tenantId) {
        return QueryDiffCompareSpuRequest.builder()
                .tenantId(tenantId)
                .storeIds(Lists.newArrayList(storeId))
                .build();
    }
}