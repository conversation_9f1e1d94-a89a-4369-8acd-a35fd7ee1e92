package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.AreaInfo;
import com.sankuai.meituan.waimai.thrift.domain.WmProductLibOrigin;
import lombok.Data;

/**
 * 单一层级地区
 *
 * <AUTHOR>
 * @since 2024/10/31
 */
@Data
public class SingleAreaBo {

    /**
     * 地区id
     */
    private Long areaId;

    /**
     * 地区名
     */
    private String areaName;

    /**
     * 地区类型 0-位置，1-国内，2-国外
     */
    private Integer areaType;

    /**
     * 父地区id
     */
    private Long parentId;

    /**
     * 地区id路径
     */
    private String areaIdPath;

    /**
     * 地区名路径
     */
    private String areaNamePath;

    /**
     * 地区级别 1-国家，2-省份，3-地市，4-区县
     */
    private Integer level;

    /**
     * 叶子结点 1-是，2-否
     */
    private Integer leafNode;

    public static SingleAreaBo of(AreaInfo areaInfo) {
        SingleAreaBo singleAreaBO = new SingleAreaBo();
        singleAreaBO.setAreaId(Long.parseLong(areaInfo.getAreaId()));
        singleAreaBO.setAreaName(areaInfo.getAreaName());
        singleAreaBO.setAreaIdPath(areaInfo.getAreaIdPath());
        singleAreaBO.setAreaNamePath(areaInfo.getAreaNamePath());
        singleAreaBO.setParentId(Long.parseLong(areaInfo.getParentId()));
        singleAreaBO.setLevel(Integer.valueOf(areaInfo.getLevel()));
        return singleAreaBO;
    }

    public static SingleAreaBo of(WmProductLibOrigin wmProductLibOrigin) {
        SingleAreaBo singleAreaBO = new SingleAreaBo();
        singleAreaBO.setAreaId((long) wmProductLibOrigin.getId());
        singleAreaBO.setAreaName(wmProductLibOrigin.getOriginName());
        singleAreaBO.setAreaIdPath(wmProductLibOrigin.getIdPath());
        singleAreaBO.setAreaNamePath(wmProductLibOrigin.getOriginNamePath());
        singleAreaBO.setAreaType(wmProductLibOrigin.getOriginType());
        singleAreaBO.setParentId(wmProductLibOrigin.getParentId());
        singleAreaBO.setLevel(wmProductLibOrigin.getLevel());
        singleAreaBO.setLeafNode(wmProductLibOrigin.getLeaf());
        return singleAreaBO;
    }

}