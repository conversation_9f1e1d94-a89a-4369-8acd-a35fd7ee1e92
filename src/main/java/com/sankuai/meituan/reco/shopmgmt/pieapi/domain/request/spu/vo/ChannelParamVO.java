package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @Title: ChannelParamVo
 * @Description: 渠道参数
 * @Author: zhaolei12
 * @Date: 2020/4/21 9:49 下午
 */
@TypeDoc(
        description = "渠道参数"
)
@Data
public class ChannelParamVO {

    @FieldDoc(
            description = "渠道ID"
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道名称"
    )
    private String channelName;
}
