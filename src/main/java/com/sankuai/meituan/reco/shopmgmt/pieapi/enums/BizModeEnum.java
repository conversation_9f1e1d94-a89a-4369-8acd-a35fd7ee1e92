package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import com.meituan.linz.boot.enums.EnumBase;
import com.meituan.linz.boot.util.Assert;

/**
 * 业态枚举
 *
 * <AUTHOR>
 * @since 2022/12/27
 */
public enum BizModeEnum implements EnumBase {

    VEGETABLE_MARKET(0, "vegetable_market", "三方代运营", ModeEnum.B),
    CONVENIENCE_STORE(1, "convenience_store", "便利店", ModeEnum.B),
    SUPERMARKET(2, "supermarket", "商超", ModeEnum.MARKET),
    CAIDAQUAN(3, "caidaquan", "菜大全", ModeEnum.B),
    WAIMA(4, "waima", "歪马送酒", ModeEnum.B)
    ;

    private int code;
    private String strCode;
    private String message;
    private ModeEnum mode;

    BizModeEnum(int code, String strCode, String message, ModeEnum mode) {
        this.code = code;
        this.strCode = strCode;
        this.message = message;
        this.mode = mode;
    }

    public String getStrCode() {
        return strCode;
    }

    public ModeEnum getMode() {
        return mode;
    }

    public static BizModeEnum ofStrCodeNullable(String strCode) {
        for (BizModeEnum dateType : BizModeEnum.values()) {
            if (dateType.getStrCode().equals(strCode)) {
                return dateType;
            }
        }
        return null;
    }



    public static BizModeEnum ofStrCode(String strCode) {
        BizModeEnum obj = BizModeEnum.ofStrCodeNullable(strCode);
        Assert.throwIfNull(obj, "invalid code of BizModeEnum, strCode: {}", strCode);
        return obj;
    }
    @Override
    public int code() {
        return this.code;
    }

    @Override
    public String message() {
        return this.message;
    }
}
