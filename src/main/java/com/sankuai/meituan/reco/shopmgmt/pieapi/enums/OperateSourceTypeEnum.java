package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

/**
 * @Author: luokai14
 * @Date: 2022/3/7 3:29 下午
 * @Mail: <EMAIL>
 */
public enum OperateSourceTypeEnum {
    DEVELOPER(1, "开放平台"),
    OCMS(2, "牵牛花web"),
    APP(3, "app");

    private int code;
    private String desc;

    OperateSourceTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OperateSourceTypeEnum findByCode(Integer code) {
        for (OperateSourceTypeEnum it : values()) {
            if (it.getCode() == code) {
                return it;
            }
        }
        return null;
    }
}
