package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

/**
 * @Author：<EMAIL>
 * @Date: 2022/5/11 17:01 PM
 */
public enum PoiTypeEnum {

    // 3 门店, 6 共享仓
    STORE(3, "门店"),
    SHARE_WAREHOUSE(6, "共享仓");

    private final int code;
    private final String msg;

    PoiTypeEnum(int code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static boolean isStore(int code){
        return STORE.code == code;
    }

    public static boolean isShareWareHouse(int code){
        return SHARE_WAREHOUSE.code == code;
    }

    public static boolean isPoiTypeEnum(Integer code){
        if(null == code){
            return false;
        }
        for(PoiTypeEnum typeEnum : PoiTypeEnum.values()){
            if(code == typeEnum.code){
                return true;
            }
        }
        return false;
    }
}
