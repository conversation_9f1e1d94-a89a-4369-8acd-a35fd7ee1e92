package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.google.common.base.Preconditions;
import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SaveReasonTemplateRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OperateTypeEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


@TypeDoc(
        description = "批量保存审核原因模板"
)
@ApiModel("批量保存审核原因模板")
@Getter
@Setter
@ToString
public class ReasonTemplateBatchSaveRequest {

    @FieldDoc(
            description = "原因模版类型：1-驳回，2-通过", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "原因模版类型")
    private Integer reasonType;

    @FieldDoc(
            description = "明细列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "明细列表")
    private List<Item> list;

    @TypeDoc(
            description = "明细"
    )
    @ApiModel("明细")
    @Getter
    @Setter
    @ToString
    public static class Item {

        @FieldDoc(
                description = "拒绝/通过原因", requiredness = Requiredness.REQUIRED
        )
        @ApiModelProperty(name = "拒绝/通过原因", required = true)
        private String reason;

        @FieldDoc(
                description = "模板原因id", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "模板原因id")
        private Long reasonId;

        @FieldDoc(
                description = "操作类型(1:新增;2-修改)", requiredness = Requiredness.REQUIRED
        )
        @ApiModelProperty(name = "操作类型(1:新增;2-修改)", required = true)
        private Integer operateType;

        public void selfCheck() {
            Preconditions.checkArgument(reason != null, "模板文本不允许为空");
            Preconditions.checkArgument(operateType != null, "模板操作类型不允许为空");
            if (operateType == 2) {
                Preconditions.checkArgument(reasonId != null, "修改场景，id必填");
            }
        }
    }


    public void selfCheck() {
        Preconditions.checkArgument(reasonType != null, "原因模板类型不能为空");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(list) && list.size() <= 30, "模板列表不可为空且上限30");
        for (Item item : list) {
            Preconditions.checkArgument(item != null, "模板列表不允许有空元素");
            item.selfCheck();
        }
        Preconditions.checkArgument(Fun.distinct(list, Item::getReason).size() == list.size(), "模板列表存在相同文本");
    }

    public List<SaveReasonTemplateRequest> toSaveReasonTemplateRequestList(User user) {
        return Fun.map(list, item -> {
            SaveReasonTemplateRequest saveReasonTemplateRequest = new SaveReasonTemplateRequest();
            saveReasonTemplateRequest.setTenantId(user.getTenantId());
            saveReasonTemplateRequest.setOperateId(user.getAccountId());
            saveReasonTemplateRequest.setOperateName(user.getOperatorName());
            saveReasonTemplateRequest.setReason(item.getReason().trim());
            // 新增时设置默认值，修改时不处理
            if (item.getOperateType() == 1) {
                saveReasonTemplateRequest.setReasonType(this.getReasonType() != null ? this.getReasonType() : 1);
            }
            if (item.getReasonId() != null) {
                saveReasonTemplateRequest.setReasonId(item.getReasonId());
            }
            saveReasonTemplateRequest.setOperateType(OperateTypeEnum.findByValue(item.getOperateType()));
            return saveReasonTemplateRequest;
        });
    }

}
