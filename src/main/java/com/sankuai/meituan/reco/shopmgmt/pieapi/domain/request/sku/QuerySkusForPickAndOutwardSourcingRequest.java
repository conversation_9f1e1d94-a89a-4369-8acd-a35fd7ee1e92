package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 查询待拣货及外采商品请求
 * @author: luokun04
 * @create: 2020-10-14
 **/
@TypeDoc(
        description = "查询待外采商品请求"
)
@Data
@ApiModel("查询待拣货及外采商品请求")
public class QuerySkusForPickAndOutwardSourcingRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id", required = true)
    @NotNull
    private Long storeId;
}