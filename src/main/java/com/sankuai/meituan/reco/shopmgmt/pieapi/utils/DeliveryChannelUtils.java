package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;

import java.util.*;

public class DeliveryChannelUtils {

    //青云配送枚举map,<Integer(平台code/承运商code), String(承运商名)>
    private static Map<Integer, String> dapDeliveryMap = new HashMap<>();

    //自营配送枚举map
    private static Map<Integer, String>  merchantDeliveryMap = new HashMap<>();

    static {
        for(DeliveryChannelEnum each : DeliveryChannelEnum.values()) {
            if(each.isDapDelivery()) {
                dapDeliveryMap.put(each.getCode(), each.getName());
            }
            else if(each.isMerchantSelfDelivery() ) {
                merchantDeliveryMap.put(each.getCode(), each.getName());
            }
        }

        dapDeliveryMap.put(DeliveryChannelEnum.DAP_DELIVERY.getCode(), null); //青云渠道配送，code为-4是平台code, 此时骑手未接单，无承运商传null
        merchantDeliveryMap.put(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode(), "歪马"); //自营配送渠道,承运商展示“歪马”

    }

    //根据配送渠道类别返回deliveryChannelId列表，用于筛选不同配送渠道订单
    public static List<Integer> getDeliveryChannelIdList(Integer DeliveryChannelType) {

        if(DeliveryChannelType.equals(DeliveryChannelEnum.DAP_DELIVERY.getCode())) {
            return new ArrayList<>(dapDeliveryMap.keySet());

        } else if(DeliveryChannelType.equals(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())) {
            return new ArrayList<>(merchantDeliveryMap.keySet());

        } else {
            return null;
        }
    }

    //是否属于青云配送
    public static boolean isDapDeliveryChannel(Integer deliveryChannelId) {
        if(dapDeliveryMap.containsKey(deliveryChannelId)) {
            return true;
        }  else {
            return false;
        }
    }

    //是否属于自营配送
    public static boolean isMerchantDeliveryChannel(Integer deliveryChannelId) {
        if(merchantDeliveryMap.containsKey(deliveryChannelId)) {
            return true;
        }  else {
            return false;
        }
    }

    //用于根据配送渠道id查询对应的承运商
    public static String getDeliveryPlatform(Integer deliveryChannelId, Integer deliveryStatus) {
        if(dapDeliveryMap.containsKey(deliveryChannelId)) {
            return dapDeliveryMap.get(deliveryChannelId);
        } else if(merchantDeliveryMap.containsKey(deliveryChannelId)) {
            if(deliveryStatus.equals(DeliveryStatusEnum.INIT.getCode()) || deliveryStatus.equals(DeliveryStatusEnum.DELIVERY_LAUNCHED.getCode())) {
                return null; //等待发起配送、等待配送平台接单 这两阶段无承运商，不展示“歪马”承运商
            }
            return merchantDeliveryMap.get(deliveryChannelId);
        } else {
            return null;
        }
    }







}
