package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.CommonConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;

import java.util.function.Function;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/16 19:12
 * @Description: 处理返回结果
 */
public class ResponseHandler {

    /**
     * 检查响应结果
     *
     * @param response
     * @param codeSupplier
     * @param msgSupplier
     */
    public static <T> void checkResponseAndStatus(T response, Function<T, Integer> codeSupplier,
                                                  Function<T, String> msgSupplier, ResultCode resultCode) {
        if (response == null) {
            throw new CommonRuntimeException(CommonConstants.ErrorMsg.UNKNOWN_ERROR, ResultCode.FAIL);
        }

        if (codeSupplier.apply(response) != ResultCode.SUCCESS.getCode()) {
            throw new CommonRuntimeException(msgSupplier.apply(response), resultCode);
        }

    }

    /**
     * 检查响应结果
     * @param response
     * @param codeSupplier
     * @param msgSupplier
     */
    public  static <T> void  checkResponseAndStatus(T response, Function<T,Integer> codeSupplier, Function<T,String> msgSupplier) {
        if (response == null) {
            throw new BizException(ResultCodeEnum.FAIL.getValue(), CommonConstants.ErrorMsg.UNKNOWN_ERROR);
        }

        if (codeSupplier.apply(response) != ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(codeSupplier.apply(response), msgSupplier.apply(response));
        }

    }

}
