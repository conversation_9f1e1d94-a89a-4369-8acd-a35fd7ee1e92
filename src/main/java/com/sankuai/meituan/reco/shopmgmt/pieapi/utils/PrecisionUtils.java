package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import java.math.BigDecimal;

public abstract class PrecisionUtils {
	private PrecisionUtils() {

	}

	public static String moneyPrecisionConvert(Double value) {
		if(value == null) {
			return null;
		}

		return BigDecimal.valueOf(value).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
	}

	public static String stockPrecisionConvert(Double value, boolean needWeight) {
		if(value == null) {
			return null;
		}

		if(needWeight) {
			return BigDecimal.valueOf(value).setScale(3, BigDecimal.ROUND_HALF_UP).toString();
		} else {
			return BigDecimal.valueOf(value).setScale(0, BigDecimal.ROUND_HALF_UP).toString();
		}
	}
}
