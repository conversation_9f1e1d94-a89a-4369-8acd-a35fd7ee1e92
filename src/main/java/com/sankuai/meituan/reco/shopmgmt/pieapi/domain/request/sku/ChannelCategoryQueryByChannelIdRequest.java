package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@TypeDoc(
        name = "根据渠道id查询渠道类目请求",
        description = "根据渠道id查询渠道类目请求"
)
@Setter
@Getter
@EqualsAndHashCode
@ToString
public class ChannelCategoryQueryByChannelIdRequest {

    @FieldDoc(
            description = "渠道编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道编码")
    @NotNull(message = "渠道编码不能为空")
    private Integer channelId;
}
