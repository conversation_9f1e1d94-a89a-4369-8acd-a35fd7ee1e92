package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2022/1/13
 */
public class FormatUtil {
    public static String formatViewOrderIds(List<String> viewOrderIds) {
        if(CollectionUtils.isEmpty(viewOrderIds)) {
            return "";
        }
        StringBuilder viewOrderIdStringBuilder = new StringBuilder();
        for (String viewOrderId : viewOrderIds) {
            viewOrderIdStringBuilder.append("'");
            viewOrderIdStringBuilder.append(viewOrderId);
            viewOrderIdStringBuilder.append("',");
        }
        return viewOrderIdStringBuilder.deleteCharAt(viewOrderIdStringBuilder.length() - 1).toString();
    }
}
