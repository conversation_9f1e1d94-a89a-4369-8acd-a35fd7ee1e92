package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sn;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/9/14 15:42
 **/
@Data
public class CheckSnCodeRequest {
    private String snCode;

    private String skuId;

    public String validate() {
        if (StringUtils.isBlank(snCode)) {
            return "sn码不能为空";
        }

        if (StringUtils.isBlank(skuId)) {
            return "skuId不能为空";
        }

        return null;
    }
}
