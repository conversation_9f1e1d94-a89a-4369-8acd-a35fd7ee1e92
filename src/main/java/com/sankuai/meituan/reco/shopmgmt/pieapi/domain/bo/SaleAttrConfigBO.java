package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
public class SaleAttrConfigBO {
    private static final int SALE_ATTR_OFF = 0;
    private static final int SALE_ATTR_ON = 1;
    private static final int SALE_ATTR_ON_SKU_IMAGE = 2;
    /**
     * 使用销售属性的租户id
     */
    private Set<String> useSaleAttrTenantIds;
    /**
     * 使用SKU图片的租户id
     */
    private Set<String> useSkuImageTenantIds;

    /**
     * 获取销售属性开启情况
     * @param tenantId 租户ID
     * @return 0-未开启，1-开启，2-开启SKU图片
     */
    public int getTenantSaleAttrConfig(long tenantId) {
        List<String> tenantIdStrList = Arrays.asList(String.valueOf(tenantId), "*");

        if (CollectionUtils.containsAny(Optional.ofNullable(useSaleAttrTenantIds).orElse(Collections.emptySet()), tenantIdStrList)) {
            return SALE_ATTR_ON;
        }
        if (CollectionUtils.containsAny(Optional.ofNullable(useSkuImageTenantIds).orElse(Collections.emptySet()), tenantIdStrList)) {
            return SALE_ATTR_ON_SKU_IMAGE;
        }

        return SALE_ATTR_OFF;
    }
}
