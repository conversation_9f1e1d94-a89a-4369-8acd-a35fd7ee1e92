package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.CompareSpuTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryStoreSpuProblemDetailRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/2/22 下午2:18
 **/
@TypeDoc(
        name = "不一致商品详情请求",
        description = "不一致商品详情请求"
)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class StoreSpuProblemInfoRequest {
    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;


    @FieldDoc(
            description = "门店spuId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店spuId", required = true)
    private String spuId;

    @FieldDoc(
            description = "二级不一致类型（101商家端商品缺失;102蔬果派商品缺失;201商家端规格缺失;202蔬果派规格缺失;301基本信息不一致;401价格不一致)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "二级不一致类型", required = true)
    private Integer type;

    public void validate() {
        Preconditions.checkArgument(Objects.nonNull(storeId), "门店id不能为空");
        Preconditions.checkArgument(StringUtils.isNotEmpty(spuId), "商品spuId不能为空");
        Preconditions.checkArgument(Objects.nonNull(type), "不一致类型不能为空");
        CompareSpuTypeEnum compareSpuTypeEnum = CompareSpuTypeEnum.findByCode(type);
        Preconditions.checkArgument(Objects.nonNull(compareSpuTypeEnum)
                && compareSpuTypeEnum.getWebCode()!=0, "不一致类型非法");
    }


    public QueryStoreSpuProblemDetailRequest to(User user) {
        return QueryStoreSpuProblemDetailRequest.builder()
                .tenantId(user.getTenantId())
                .storeId(storeId)
                .spuId(spuId)
                .problemType(Objects.requireNonNull(CompareSpuTypeEnum.findByCode(type)).getWebCode())
                .build();
    }
}