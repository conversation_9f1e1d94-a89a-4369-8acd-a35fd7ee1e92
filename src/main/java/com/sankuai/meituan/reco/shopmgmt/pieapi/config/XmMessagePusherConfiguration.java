package com.sankuai.meituan.reco.shopmgmt.pieapi.config;

import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KMSStringUtils;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.xm.pubapi.thrift.PusherInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/7/18 20:25
 **/
@Configuration
public class XmMessagePusherConfiguration {
    @Value("${app.key}")
    private String appKey;

    @Bean("fulfillWarningMessagePusher")
    public PusherInfo fulfillWarningMessagePusher() throws KmsResultNullException {
        PusherInfo pusherInfo = new PusherInfo();
        pusherInfo.setAppkey(Kms.getByName(appKey, "fulfill.warning.pusher.key"));
        pusherInfo.setFromUid(Long.parseLong(Kms.getByName(appKey, "fulfill.warning.pusher.pubId")));
        pusherInfo.setToken(Kms.getByName(appKey, "fulfill.warning.pusher.token"));
        pusherInfo.setAppId((short) 1); //// 大象APP_ID=1，若不配置该成员，则自动配置公众所属的APP_ID
        return pusherInfo;
    }
}
