package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelBrandBindingDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelBrandRelationDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2023/2/17
 * 渠道与品牌关联关系
 **/
@TypeDoc(
        description = "渠道与品牌关联关系"
)
@Data
@ApiModel("渠道与品牌关联关系")
public class ChannelBrandRelationVO {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "品牌编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "品牌编码")
    @NotNull
    private String brandCode;

    public ChannelBrandBindingDTO toDTO() {
        ChannelBrandBindingDTO channelBrandBindingDTO = new ChannelBrandBindingDTO();
        channelBrandBindingDTO.setChannelId(channelId);
        channelBrandBindingDTO.setBrandCode(brandCode);
        return channelBrandBindingDTO;
    }

    public ChannelBrandRelationDTO toBizDTO() {
        ChannelBrandRelationDTO channelBrandRelationDTO = new ChannelBrandRelationDTO();
        channelBrandRelationDTO.setChannelId(channelId);
        channelBrandRelationDTO.setBrandCode(brandCode);
        return channelBrandRelationDTO;
    }

}
