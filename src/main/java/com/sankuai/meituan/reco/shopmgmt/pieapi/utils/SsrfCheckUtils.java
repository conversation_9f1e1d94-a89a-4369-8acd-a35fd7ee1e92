package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.dianping.cat.util.MetricHelper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;
import com.sankuai.security.sdk.SecSdk;
import com.sankuai.security.sdk.core.ssrf.SSRFConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Slf4j
public class SsrfCheckUtils {

    public static void checkUrl(String imageUrl) {
        if (StringUtils.isEmpty(imageUrl) || !imageUrl.startsWith("http")) {
            return;
        }
        if (!MccConfigUtil.ssrfCheckUrlSwitch()) {
            return;
        }
        List<String> acceptedDomains = MccConfigUtil.getUrlWhileList();
        SSRFConfig ssrfConfig = new SSRFConfig();
        ssrfConfig.addAcceptedDomains(acceptedDomains);
        ssrfConfig.setAddressCheckIfNotMatchDomains(false);
        if (!SecSdk.checkSSRF(imageUrl, ssrfConfig)) {
            log.warn("Check url ssrf NOT PASS, imageUrl:{}.", imageUrl);
            MetricHelper.build().name("check_url_ssrf").tag("result", "not_pass").count();
            if (MccConfigUtil.interruptUrlSsrf()) {
                throw new ParamException("上传资源链接不符合安全要求，校验不通过");
            }
        } else {
            MetricHelper.build().name("check_url_ssrf").tag("result", "pass").count();
        }
    }

    public static void checkInnerUrl(String imageUrl) {
        if (StringUtils.isEmpty(imageUrl) || !imageUrl.startsWith("http")) {
            return;
        }
        if (!MccConfigUtil.ssrfCheckUrlSwitch()) {
            return;
        }
        List<String> acceptedDomains = MccConfigUtil.getUrlWhileList();
        if (!SecSdk.securitySSRF(imageUrl, acceptedDomains)) {
            // 危险，可能是SSRF攻击请求，
            throw new ParamException("上传资源链接不符合安全要求，校验不通过");
        }
    }

}