package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sn;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/9/14 15:54
 **/
@Data
public class FuzzySearchSnCodeRequest {
    private String snCodeKeyWord;

    private String skuId;

    public String validate() {
        if (StringUtils.isBlank(snCodeKeyWord)) {
            return "sn码关键词不能为空";
        }

        if (StringUtils.isBlank(skuId)) {
            return "skuId不能为空";
        }

        return null;
    }
}
