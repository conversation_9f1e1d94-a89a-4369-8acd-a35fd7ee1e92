package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.StoreCategoryTopRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/6/23 8:53 下午
 **/
@TypeDoc(
        description = "店内分类置顶请求",
        authors = {"zhouyan32"}
)
@Data
@ApiModel("店内分类置顶请求")
public class FrontCategoryTopRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId = 100;
    @FieldDoc(
            description = "分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分类ID", required = true)
    private Long categoryId;

    @FieldDoc(
            description = "开启关闭置顶, 0-关闭，1-开启", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "开启关闭置顶, 0-关闭，1-开启", required = true)
    private Integer topFlag;

    @FieldDoc(
            description = "置顶周期：1,2,3,4,5,6,7分别表示周一至周日", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "置顶周期：1,2,3,4,5,6,7分别表示周一至周日")
    private String weeksTime;

    @FieldDoc(
            description = "置顶时段：00:00-09:00，10:00-11:00", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "置顶时段：00:00-09:00，10:00-11:00")
    private List<String> period;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.channelId == null) {
            throw new CommonLogicException("渠道id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.categoryId == null) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.topFlag == null) {
            throw new CommonLogicException("置顶开关不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.topFlag == 1) {
            if (this.weeksTime == null) {
                throw new CommonLogicException("置顶周期不能为空", ResultCode.CHECK_PARAM_ERR);
            }
            if (this.period == null) {
                throw new CommonLogicException("置顶周期时段不能为空", ResultCode.CHECK_PARAM_ERR);
            }
        }
    }

    public StoreCategoryTopRequest to(User user) {
        StoreCategoryTopRequest storeCategoryTopRequest = new StoreCategoryTopRequest();
        storeCategoryTopRequest.setTenantId(user.getTenantId());
        storeCategoryTopRequest.setStoreId(this.storeId);
        storeCategoryTopRequest.setChannelId(this.channelId);
        storeCategoryTopRequest.setStoreCategoryId(this.categoryId);
        storeCategoryTopRequest.setTopFlag(this.topFlag);
        storeCategoryTopRequest.setPeriod(this.period);
        storeCategoryTopRequest.setWeeksTime(this.weeksTime);
        storeCategoryTopRequest.setOperatorId(user.getAccountId());
        storeCategoryTopRequest.setOperatorName(user.getOperatorName());
        return storeCategoryTopRequest;
    }
}
