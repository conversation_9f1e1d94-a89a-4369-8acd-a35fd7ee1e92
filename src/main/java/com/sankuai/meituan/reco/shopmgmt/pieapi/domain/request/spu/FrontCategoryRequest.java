package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreCategoryDeleteRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.StoreCategoryQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/6/23 8:30 下午
 **/
@TypeDoc(
        description = "店内分类基本请求参数",
        authors = {"zhouyan32"}
)
@Data
@ApiModel("店内分类基本请求参数")
public class FrontCategoryRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId = 100;

    @FieldDoc(
            description = "分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分类ID", required = true)
    private Long categoryId;


    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.channelId == null) {
            throw new CommonLogicException("渠道id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.categoryId == null) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }

    public ChannelStoreCategoryDeleteRequest to(User user) {
        ChannelStoreCategoryDeleteRequest channelStoreCategoryDeleteRequest = new ChannelStoreCategoryDeleteRequest();
        channelStoreCategoryDeleteRequest.setTenantId(user.getTenantId());
        channelStoreCategoryDeleteRequest.setStoreId(this.storeId);
        channelStoreCategoryDeleteRequest.setChannelId(this.channelId);
        channelStoreCategoryDeleteRequest.setCategoryId(this.categoryId);
        channelStoreCategoryDeleteRequest.setOperatorId(user.getAccountId());
        channelStoreCategoryDeleteRequest.setOperator(user.getOperatorName());
        return channelStoreCategoryDeleteRequest;
    }

    public StoreCategoryQueryRequest toQuery(User user) {
        StoreCategoryQueryRequest storeCategoryQueryRequest = new StoreCategoryQueryRequest();
        storeCategoryQueryRequest.setTenantId(user.getTenantId());
        storeCategoryQueryRequest.setStoreId(this.storeId);
        storeCategoryQueryRequest.setChannelId(this.channelId);
        storeCategoryQueryRequest.setStoreCategoryId(this.categoryId);
        return storeCategoryQueryRequest;
    }
}
