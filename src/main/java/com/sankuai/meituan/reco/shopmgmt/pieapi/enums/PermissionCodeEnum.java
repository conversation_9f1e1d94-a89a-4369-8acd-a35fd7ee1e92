/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import lombok.Getter;

/**
 * 功能权限code
 *
 * <AUTHOR>
 * @since 2021/7/21
 */
@Getter
public enum PermissionCodeEnum {

    ASSISTANT_OFF_SALE_IN_STOCK("ASSISTANT_OFF_SALE_IN_STOCK", "店长助手(有库存待上架)"),
    SALE_RETURN_TASK("SALE_RETURN_TASK", "销售退货回库"),

    MINI_APP_ORDER_SUB_TAB("ORDER_SUB_TAB","小程序进行中订单列表"),
    MINI_APP_ORDER_SEARCH("ORDER_SEARCH","小程序订单全部订单"),
    MINI_APP_COMMENT_MANAGEMENT("COMMENT_MANAGEMENT","小程序评价"),
    ;

    private final String code;
    private final String desc;

    PermissionCodeEnum(String auth, String desc) {
        this.code = auth;
        this.desc = desc;
    }

    public static PermissionCodeEnum authOf(String authCode) {
        for (PermissionCodeEnum moduleEnum : PermissionCodeEnum.values()) {
            if (moduleEnum.code.equals(authCode)) {
                return moduleEnum;
            }
        }
        return null;
    }
}
