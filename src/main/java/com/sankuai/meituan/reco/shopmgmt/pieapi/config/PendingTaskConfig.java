package com.sankuai.meituan.reco.shopmgmt.pieapi.config;

import com.beust.jcommander.internal.Lists;
import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.commons.utils.json.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/3/28 14:33
 */
@Data
@Slf4j
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class PendingTaskConfig {

    /**
     * 工作台菜单对应的code(与前端预定)
     */
    private String authCode;

    /**
     * 描述.
     */
    private String desc;

    /**
     * 是否支持多门店查询
     */
    private boolean supportMultiPoi = false;

    /**
     * 泛化调用配置
     */
    private String remoteAppkey;

    private String genericServiceName;

    private String methodName;

    public static final String CONFIG_KEY = "menu.pending.count.thrift.info";

    public static List<PendingTaskConfig> getConfigList() {
        String s = Lion.getConfigRepository().get(CONFIG_KEY);
        if (StringUtils.isBlank(s)) {
            log.info("getPendingTaskConfigList is null");
            return Lists.newArrayList();
        }
        return JsonUtils.toObject(s, new TypeReference<List<PendingTaskConfig>>() {
        });
    }

}
