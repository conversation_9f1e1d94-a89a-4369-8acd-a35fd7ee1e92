package com.sankuai.meituan.reco.shopmgmt.pieapi.config.interceptors;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.FunctionPowers;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
public class AuthInterceptor extends HandlerInterceptorAdapter {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {

        if (handler instanceof HandlerMethod) {
            // 只处理Controller
            HandlerMethod handlerMethod = (HandlerMethod) handler;

            Class<?> type = handlerMethod.getBeanType();

            AuthInfo authInfo = new AuthInfo(type.getDeclaredAnnotation(Auth.class),
                    handlerMethod.getMethodAnnotation(Auth.class));

            if (!authInfo.authenticate) {
                return true;
            }

            if (ApiMethodParamThreadLocal.getIdentityInfo().getUser() == null) {
                throw new CommonLogicException("获取用户数据失败", ResultCode.FAIL);
            }
            return true;
        } else {
            return true;
        }

    }

    @SuppressWarnings("unused")
    private static final class AuthInfo {
        private final boolean authenticate;

        private AuthInfo(Auth authOnClass, Auth authOnMethod) {
            FunctionPowers[] permission;

            if (authOnClass == null && authOnMethod == null) {
                authenticate = false;
            } else if (authOnMethod != null) {
                authenticate = authOnMethod.authenticate();
            } else {
                authenticate = authOnClass.authenticate();
            }
        }

        public boolean authenticate() {
            return authenticate;
        }
    }
}
