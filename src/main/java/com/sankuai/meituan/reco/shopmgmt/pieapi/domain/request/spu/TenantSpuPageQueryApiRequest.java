package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.StanderTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.WeightTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.client.common.dto.OperatorDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelNormAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.MtViolationStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.TenantSpuPageQueryRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.OperateInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title: TenantSpuPageQueryApiRequest
 * @Description: 租户商品spu分页查询请求参数
 * @Author: wuyongjiang
 * @Date: 2022/8/23 17:16
 */
@TypeDoc(
        description = "租户商品spu分页查询请求参数"
)
@Data
@ApiModel("租户商品spu分页查询请求参数")
public class TenantSpuPageQueryApiRequest {
    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第几页", required = true)
    @NotNull
    private Integer page = 1;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    @NotNull
    private Integer size = 20;

    @FieldDoc(
            description = "搜索参数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "搜索参数")
    private String keyword;

    @FieldDoc(
            description = "商品类目", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品类目")
    private List<String> categoryIdList;


    @FieldDoc(
            description = "品牌", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "品牌")
    private String brandName;


    @FieldDoc(
            description = "skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "sku")
    private String sku;

    @FieldDoc(
            description = "查无图，1：查无图商品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否无图")
    private Integer noPic;


    @FieldDoc(
            description = "是否标品，1：标品， 0：非标品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否标品")
    private Integer standerType;

    @FieldDoc(
            description = "导出类型; 1 spu信息 2 商品信息 3导出采购规格信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "导出类型; 1 spu信息 2 商品信息 3导出采购规格信息")
    private int exportType;

    @FieldDoc(
            description = "是否可售; 0 全部 1 可售 2 不可售", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否可售; 0 全部 1 可售 2 不可售")
    private int mtAllowSale;

    @FieldDoc(
            description = "加盟门店审核状态; 0 全部 1审核驳回，2审核中 用于查询加盟门店审核状态"
    )
    @ApiModelProperty(value = "加盟门店审核状态; 0 全部 1审核驳回，2审核中")
    private int mtAllowSaleStatus;

    @FieldDoc(
            description = "加盟门店违规状态; 0全部 1美团平台停售 2美团平台下架"
    )
    @ApiModelProperty(value = "加盟门店违规状态; 0全部 1美团平台停售 2美团平台下架")
    private int mtViolationStatus;

    @FieldDoc(
            description = "商品店内分类id列表",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品店内分类id列表")
    private List<Long> storeCategoryIdList;

    @FieldDoc(
            description = "查无动态信息，默认0，查询1",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "查无动态信息")
    private int noDynamicInfo;

    @FieldDoc(
            description = "查无饿了么类目属性；默认0；需要查1",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "查无饿了么类目属性")
    private int noEleCategoryProperties;

    @FieldDoc(
            description = "是否筛选不可售异常",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否筛选不可售异常")
    private Boolean hasNoSaleAbnormal;

    public static TenantSpuPageQueryRequest toRpcRequest(TenantSpuPageQueryApiRequest request, User user) {
        TenantSpuPageQueryRequest rpcRequest = new TenantSpuPageQueryRequest();

        rpcRequest.setPage(request.getPage());
        rpcRequest.setPageSize(request.getSize());
        rpcRequest.setTenantId(user.getTenantId());
        rpcRequest.setKeywords(request.getKeyword());
        rpcRequest.setCategoryIdList(request.getCategoryIdList());
        rpcRequest.setBrandName(request.getBrandName());
        rpcRequest.setSku(request.getSku());
        rpcRequest.setExportType(request.getExportType());
        rpcRequest.setNoPic(request.getNoPic());
        rpcRequest.setWeightTypeList(request.toWeightTypeList());
        rpcRequest.setNeedStatisticsCoverage(true);
        rpcRequest.setAuditStatusList(ChannelAuditStatusEnum.ofAllowSale(request.getMtAllowSale()).stream().map(ChannelAuditStatusEnum::getCode).collect(Collectors.toList()));
        rpcRequest.setNormAuditStatusList(ChannelNormAuditStatusEnum.ofAllowSale(request.getMtAllowSale()).stream().map(ChannelNormAuditStatusEnum::getCode).collect(Collectors.toList()));
        rpcRequest.setFranchiseeAuditStatusList(ChannelAuditStatusEnum.ofAllowSaleStatus(request.getMtAllowSaleStatus()).stream().map(ChannelAuditStatusEnum::getCode).collect(Collectors.toList()));
        rpcRequest.setFranchiseeNormAuditStatusList(ChannelNormAuditStatusEnum.ofAllowSaleStatus(request.getMtAllowSaleStatus()).stream().map(ChannelNormAuditStatusEnum::getCode).collect(Collectors.toList()));
        rpcRequest.setFranchiseeHasStopSelling(request.toFranchiseeHasStopSelling());
        rpcRequest.setFranchiseePlatformSoldOut(request.toFranchiseePlatformSoldOut());
        rpcRequest.setNoDynamicInfo(request.getNoDynamicInfo());
        rpcRequest.setNoEleCategoryProperties(request.getNoEleCategoryProperties());

        OperatorDTO operator = new OperatorDTO();
        operator.setOperatorId(user.getAccountId());
        operator.setOperatorName(user.getOperatorName());
        rpcRequest.setOperator(operator);
        return rpcRequest;
    }

    public static com.sankuai.meituan.shangou.empower.productbiz.client.request.TenantSpuPageQueryRequest toBizRpcRequest(TenantSpuPageQueryApiRequest request, User user) {
        com.sankuai.meituan.shangou.empower.productbiz.client.request.TenantSpuPageQueryRequest rpcRequest = new com.sankuai.meituan.shangou.empower.productbiz.client.request.TenantSpuPageQueryRequest();

        rpcRequest.setPage(request.getPage());
        rpcRequest.setPageSize(request.getSize());
        rpcRequest.setTenantId(user.getTenantId());
        rpcRequest.setKeywords(request.getKeyword());
        rpcRequest.setCategoryIdList(request.getCategoryIdList());
        rpcRequest.setBrandName(request.getBrandName());
        rpcRequest.setSku(request.getSku());
        rpcRequest.setExportType(request.getExportType());
        rpcRequest.setNoPic(request.getNoPic());
        rpcRequest.setWeightTypeList(request.toWeightTypeList());
        rpcRequest.setNeedStatisticsCoverage(true);
        rpcRequest.setAuditStatusList(ChannelAuditStatusEnum.ofAllowSale(request.getMtAllowSale()).stream().map(ChannelAuditStatusEnum::getCode).collect(Collectors.toList()));
        rpcRequest.setNormAuditStatusList(ChannelNormAuditStatusEnum.ofAllowSale(request.getMtAllowSale()).stream().map(ChannelNormAuditStatusEnum::getCode).collect(Collectors.toList()));
        rpcRequest.setFranchiseeAuditStatusList(ChannelAuditStatusEnum.ofAllowSaleStatus(request.getMtAllowSaleStatus()).stream().map(ChannelAuditStatusEnum::getCode).collect(Collectors.toList()));
        rpcRequest.setFranchiseeNormAuditStatusList(ChannelNormAuditStatusEnum.ofAllowSaleStatus(request.getMtAllowSaleStatus()).stream().map(ChannelNormAuditStatusEnum::getCode).collect(Collectors.toList()));
        rpcRequest.setFranchiseeHasStopSelling(request.toFranchiseeHasStopSelling());
        rpcRequest.setFranchiseePlatformSoldOut(request.toFranchiseePlatformSoldOut());
        rpcRequest.setNoDynamicInfo(request.getNoDynamicInfo());
        if (BooleanUtils.isTrue(request.getHasNoSaleAbnormal())) {
            rpcRequest.setAbnormalCodes(MccConfigUtil.noSaleAbnormalCodes());
        }
        rpcRequest.setNoEleCategoryProperties(request.getNoEleCategoryProperties());

        OperateInfoDTO operator = new OperateInfoDTO();
        operator.setOperateId(user.getAccountId());
        operator.setOperateName(user.getOperatorName());
        rpcRequest.setOperator(operator);
        return rpcRequest;
    }

    public List<Integer> toWeightTypeList(){
        if (Objects.equals(StanderTypeEnum.STANDER.getCode(), standerType)){
            return Lists.newArrayList(WeightTypeEnum.NONE.getCode());
        }else if (Objects.equals(StanderTypeEnum.NO_STANDER.getCode(), standerType)){
            return Lists.newArrayList(WeightTypeEnum.WEIGHT.getCode(), WeightTypeEnum.PIECE.getCode());
        }
        return null;
    }

    public Integer toFranchiseeHasStopSelling() {
        if (Objects.equals(MtViolationStatusEnum.STOP_SELLING.getCode(), mtViolationStatus)){
            return 1;
        } else if (Objects.equals(MtViolationStatusEnum.SOLD_OUT.getCode(), mtViolationStatus)){
            return 0;
        }

        return null;
    }

    public Integer toFranchiseePlatformSoldOut() {
        if (Objects.equals(MtViolationStatusEnum.SOLD_OUT.getCode(), mtViolationStatus)){
            return 1;
        } else if (Objects.equals(MtViolationStatusEnum.STOP_SELLING.getCode(), mtViolationStatus)){
            return 0;
        }

        return null;
    }
}
