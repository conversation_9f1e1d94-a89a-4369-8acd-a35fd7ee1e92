package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.ProblemSpuCountRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/2/25 3:40 下午
 **/
@TypeDoc(
        name = "StoreSpuProblemCountRequest",
        description = "不一致商品数量统计请求"
)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class StoreSpuProblemCountRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    public void validate() {
        Preconditions.checkArgument(Objects.nonNull(storeId) && storeId.compareTo(0L) > 0, "门店ID非法");
    }

    public ProblemSpuCountRequest to(Long tenantId) {
        return ProblemSpuCountRequest.builder()
                .tenantId(tenantId)
                .storeIds(Lists.newArrayList(storeId))
                .build();
    }
}
