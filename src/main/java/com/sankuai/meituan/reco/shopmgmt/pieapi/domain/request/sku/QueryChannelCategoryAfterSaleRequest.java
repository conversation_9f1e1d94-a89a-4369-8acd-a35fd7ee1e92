package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/1/11
 */

@TypeDoc(
        description = "根据商品类目查询渠道类目请求"
)
@Data
@ApiModel("根据商品类目查询渠道类目请求")
public class QueryChannelCategoryAfterSaleRequest {
    @FieldDoc(
            description = "channelId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "channelId", required = true)
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "categoryId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "categoryId", required = true)
    @NotNull
    private String categoryId;


    private String brandId;

    private String spuId;
}
