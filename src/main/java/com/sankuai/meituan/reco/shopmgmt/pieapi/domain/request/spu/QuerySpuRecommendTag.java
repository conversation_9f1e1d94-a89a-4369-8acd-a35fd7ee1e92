package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;


@TypeDoc(
        description = "获取商品渠道推荐类目"
)
@Data
@ApiModel("获取商品渠道推荐类目")
@ToString
public class QuerySpuRecommendTag {
    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "商品名称(商品名称和UPC只能填一个)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品名称")
    private String name;

    @FieldDoc(
            description = "UPC(商品名称和UPC只能填一个)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "UPC")
    private String upcCode;
}
