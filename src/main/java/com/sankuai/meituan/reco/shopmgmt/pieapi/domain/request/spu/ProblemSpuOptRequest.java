// Copyright (C) 2021 Meituan
// All rights reserved
package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.ChannelSpuKeyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.ProblemSpuOperateTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Sets;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/2/23 上午11:18
 **/
@TypeDoc(
        name = "不一致商品操作请求",
        description = "不一致商品操作请求"
)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProblemSpuOptRequest {


    @FieldDoc(
            description = "门店id"
    )
    private Long storeId;

    @FieldDoc(
            description = "门店渠道商品集合"
    )
    private List<ChannelSpuKeyVO> customSpuKeys;

    @FieldDoc(
            description = "操作类型(1从商家端删除;2从蔬果派删除;3向商家端新增;4向蔬果派新增;6信息与商家端一致;7信息与蔬果派一致;9价格与商家端一致;10价格与蔬果派一致)"
    )
    @ApiModelProperty(value = "修复操作类型", required = true)
    private Integer optType;

    @FieldDoc(
            description = "不一致类型,基础信息和销售信息修复时必传，其余场景暂时可为空(2 商家端商品缺失; 3 蔬果派商品缺失; 4 商家端规格缺失; 5 蔬果派规格缺失; 6 价格不一致; 7 基础信息不一致; 8 销售信息不一致) @see SpuCompareTypeEnum"
    )
    @ApiModelProperty(value = "不一致类型", required = false)
    private Integer compareType;

    @FieldDoc(
            description = "无UPC是否创建商品(optType=3向蔬果派新增时必传)"
    )
    @ApiModelProperty(value = "无UPC是否创建商品", required = false)
    private Integer isCreateWithOutUpc;

    public void validate() {
        Preconditions.checkArgument(Objects.nonNull(this.optType), "修复操作类型不能为空");
        Preconditions.checkArgument(Objects.nonNull(ProblemSpuOperateTypeEnum.findByCode(this.optType)), "修复操作类型不合法");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(this.customSpuKeys), "商品列表不能为空");

        int problemSpuRepairMaxSize = MccConfigUtil.getProblemSpuRepairMaxSize();
        Preconditions.checkArgument(this.customSpuKeys.size() <= problemSpuRepairMaxSize,
                "一次最多%s个商品", problemSpuRepairMaxSize);

        this.customSpuKeys.forEach(customSpuKey -> {
            Preconditions.checkArgument(Objects.nonNull(this.storeId), "门店id不能为空");
            Preconditions.checkArgument(Objects.nonNull(customSpuKey.getChannelId()), "渠道id不能为空");
            Preconditions.checkArgument(StringUtils.isNotEmpty(customSpuKey.getCustomSpuId()), "商品编码不能为空");
        });

        Set<ChannelSpuKeyVO> customSpuKeySet = Sets.newHashSet(this.customSpuKeys);
        Preconditions.checkArgument(customSpuKeySet.size() == this.customSpuKeys.size(), "商品列表不能为空");
    }
}
