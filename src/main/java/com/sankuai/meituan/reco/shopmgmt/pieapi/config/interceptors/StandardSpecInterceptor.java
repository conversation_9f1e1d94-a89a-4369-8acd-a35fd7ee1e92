package com.sankuai.meituan.reco.shopmgmt.pieapi.config.interceptors;

import com.meituan.shangou.saas.tenant.thrift.dto.config.request.TenantSwitchGetRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantSwitchGetResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.FunctionPowers;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
/**
 * Create by zhangbo86 on 2020/5/9.
 */
@Slf4j
public class StandardSpecInterceptor extends HandlerInterceptorAdapter {
    @Resource
    private ConfigThriftService configThriftService;
    private static final String StandeSpecSwitch = "1";//灰度开关
    private static final String standardSpecPara = "SPEC_STANDARD";  //查询灰度开启的参数

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;

            Class<?> type = handlerMethod.getBeanType();

            AuthInfo authInfo = new AuthInfo(type.getDeclaredAnnotation(Auth.class),
                    handlerMethod.getMethodAnnotation(Auth.class));

            if (!authInfo.authenticate()) {
                return true;
            }
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            if (user == null) {
                return true;
            }
            TenantSwitchGetRequest tenantSwitchGetRequest =new TenantSwitchGetRequest();
            List<String> switchKey = new ArrayList<>();
            tenantSwitchGetRequest.setTenantId(user.getTenantId());
            switchKey.add(standardSpecPara);
            tenantSwitchGetRequest.setSwitchKey(switchKey);
            TenantSwitchGetResponse switchGetResponse = configThriftService.getTenantSwitch(tenantSwitchGetRequest);
            Map<String, String> switchValue = switchGetResponse.getSwitchValue();
            log.info("pre handle api method, switchValue={}", switchValue.get(standardSpecPara));
            if(MccConfigUtil.getinterceptor_enable()){
                if (switchValue.get(standardSpecPara) !=null && switchValue.get(standardSpecPara).equals(StandeSpecSwitch)){
                    log.info("pre handle api method, url={}, switch={}, request ={}", request.getRequestURI(), switchValue.get(standardSpecPara), ApiMethodParamThreadLocal.getIdentityInfo());
                    throw new CommonLogicException(MccConfigUtil.getErrorInformation(),ResultCode.FAIL);
                }
            }
        }
        return true;
    }

    private static final class AuthInfo {
        private final boolean authenticate;

        private AuthInfo(Auth authOnClass, Auth authOnMethod) {
            FunctionPowers[] permission;

            if (authOnClass == null && authOnMethod == null) {
                authenticate = false;
            } else if (authOnMethod != null) {
                authenticate = authOnMethod.authenticate();
            } else {
                authenticate = authOnClass.authenticate();
            }
        }

        public boolean authenticate() {
            return authenticate;
        }
    }
}
