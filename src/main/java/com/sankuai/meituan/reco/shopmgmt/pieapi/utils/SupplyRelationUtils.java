package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.sankuai.meituan.shangou.saas.common.exception.ParamInvalidException;
import com.sankuai.meituan.shangou.saas.common.utils.AssertUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @since 2021/09/16
 */
public class SupplyRelationUtils {
    private static final int SUPPLY_UNIT_PRICE_SCALE = 2;

    private static final double MAX_SUPPLY_UNIT_PRICE = 30000;

    private SupplyRelationUtils(){
    }

    /**
     * 校验并将采购单价价格保留到2位小数。采购单位的取值范围 0 ~ {@link #SUPPLY_UNIT_PRICE_SCALE}
     * <p>注意：设置后并不能保证浮点数精度问题，后续的操作仍要注意精度问题</p>
     */
    public static double checkAndRoundSupplyUnitPrice(Double supplyUnitPrice) {
        AssertUtil.notNull(supplyUnitPrice, "采购单价不能为空");

        BigDecimal priceDecimal = BigDecimal.valueOf(supplyUnitPrice).setScale(SUPPLY_UNIT_PRICE_SCALE, RoundingMode.HALF_UP);

        if (priceDecimal.compareTo(BigDecimal.ZERO) < 0) {
            throw new ParamInvalidException("采购单价不能小于0");
        }

        if (priceDecimal.compareTo(BigDecimal.valueOf(MAX_SUPPLY_UNIT_PRICE)) > 0) {
            throw new ParamInvalidException("采购单价不能超过"+ BigDecimal.valueOf(MAX_SUPPLY_UNIT_PRICE)
                    .setScale(SUPPLY_UNIT_PRICE_SCALE, RoundingMode.HALF_UP).toPlainString());
        }

        return priceDecimal.doubleValue();
    }
}
