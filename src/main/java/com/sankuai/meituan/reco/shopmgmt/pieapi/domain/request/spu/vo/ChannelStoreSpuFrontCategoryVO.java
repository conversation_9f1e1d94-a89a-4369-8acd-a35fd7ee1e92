package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2020/5/19 2:35 下午
 */
@TypeDoc(
        description = "渠道门店商品前台分类参数集合"
)
@Data
@ApiModel("渠道门店商品前台分类参数集合")
public class ChannelStoreSpuFrontCategoryVO {

    @FieldDoc(
            description = "商品ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品ID", required = true)
    @NotNull
    private String spuId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "店内分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "店内分类ID", required = true)
    @NotNull
    private List<Long> frontCategoryCodeList;
}
