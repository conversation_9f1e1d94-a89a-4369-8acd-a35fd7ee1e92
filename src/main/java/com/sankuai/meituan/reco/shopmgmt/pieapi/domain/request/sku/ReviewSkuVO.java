package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import java.util.List;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrValueVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.container.SkuContainer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelSkuAttrValueInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.CartonMeasureConvertFactorVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 审核提交的sku信息
 *
 * <AUTHOR>
 * @since 2024/08/20
 */
@Getter
@Setter
@ToString
public class ReviewSkuVO extends SkuContainer {

    @FieldDoc(
            description = "upcList", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "upcList")
    private List<String> upcList;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "售卖单位")
    private String baseUnit;

    @FieldDoc(
            description = "商品重量(g)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品重量")
    private Integer weight;

    @FieldDoc(
            description = "带单位的重量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "带单位的重量")
    private String weightForUnit;

    @FieldDoc(
            description = "重量单位", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "重量单位")
    private String weightUnit;

    @FieldDoc(
            description = "京东类目属性值，多规格传入", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东类目属性值")
    private List<SaleAttrValueVo> jdAttrValues;

    @FieldDoc(
            description = "建议零售价单位元，开通京东渠道传入", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "建议零售价单位元")
    private Double suggestPrice;

    @FieldDoc(
            description = "商品规格名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品规格名称")
    private String specName;

    @FieldDoc(
            description = "是否开启效期检查，0-不检查 1-检查"
    )
    @ApiModelProperty(value = "是否开启效期检查，0-不检查 1-检查")
    private Integer enableExpiredCheck;

    @FieldDoc(
            description = "产地 1-国产 2-进口"
    )
    @ApiModelProperty(value = "产地 1-国产 2-进口")
    private Integer originPlace;

    @FieldDoc(
            description = "保质期天数"
    )
    @ApiModelProperty(value = "保质期天数")
    private Integer expirationDays;

    @FieldDoc(
            description = "外部编码"
    )
    @ApiModelProperty(name = "外部编码")
    private String externalCode;

    @FieldDoc(
            description = "加盟商总部编码"
    )
    @ApiModelProperty(value = "加盟商总部编码")
    private String franchiseeHeadquartersCode;

    @FieldDoc(
            description = "采购平台编码"
    )
    @ApiModelProperty(value = "采购平台编码")
    private String purchasePlatformCode;

    @FieldDoc(
            description = "箱规单位转换系数"
    )
    @ApiModelProperty(value = "箱规单位转换系数")
    private List<CartonMeasureConvertFactorVO> cartonMeasureConvertFactorList;

    @FieldDoc(
            description = "是否自定义无upc编码  0-否，1-是"
    )
    @ApiModelProperty(value = "是否自定义无upc编码  0-否，1-是")
    private Integer customizeNoUpcCode;

    @FieldDoc(
            description = "是否使用SKU填充UPC 0-否，1-是"
    )
    @ApiModelProperty(value = "是否使用SKU填充UPC  0-否，1-是")
    private Integer useSkuAsUpc;

    @FieldDoc(
            description = "绑定采购供应商"
    )
    @ApiModelProperty(name = "绑定采购供应商")
    private ReviewSkuSupplyRelationAndPurchaseVO purchaseInfo;

    @FieldDoc(
            description = "渠道sku属性值信息"
    )
    @ApiModelProperty(value = "渠道sku属性值信息")
    private List<ChannelSkuAttrValueInfoVo> channelSkuAttrValueInfoList;

    @FieldDoc(
            description = "sku类型 1单品 2组合品"
    )
    @ApiModelProperty(name = "sku类型 1单品 2组合品")
    private Integer skuSaleType;

    @FieldDoc(
            description = "组合品的子sku信息"
    )
    @ApiModelProperty(name = "组合品的子sku信息")
    private List<CombineChildSkuVo> childSkuList;
}
