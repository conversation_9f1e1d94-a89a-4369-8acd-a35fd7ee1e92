package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.meituan.mtrace.thread.pool.TraceExecutors;

import java.util.concurrent.*;

/**
 * @Author: <EMAIL>
 * @Date: 2020-01-15 10:30
 * @Description:延迟加载线程池
 */
public class CommonThreadPoolHolder {
    public static ExecutorService commonThreadPool;
    private final static int CORE_POOL_SIZE = 5;
    private final static int MAX_POOL_SIZE = 10;
    private final static long KEEP_ALIVE_TIME = 60;
    private final static int MAX_QUEUE_SIZE = 400;

    public static ExecutorService getInstance() {
        if (null == commonThreadPool) {
            ThreadFactory namedThreadFactory = (Runnable r) -> new Thread(r, "Common_thread_pool_" + r.hashCode());
            commonThreadPool = TraceExecutors.getTraceExecutorService(new ThreadPoolExecutor(CORE_POOL_SIZE, MAX_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS, new ArrayBlockingQueue<>(MAX_QUEUE_SIZE), namedThreadFactory));
        }
        return commonThreadPool;
    }

    private static ExecutorService concurrentRpcInvokeExecutor = new ExecutorServiceTraceWrapper(
            new ThreadPoolExecutor(
                    10,
                    20,
                    3,
                    TimeUnit.MINUTES,
                    new SynchronousQueue<>(),
                    new ThreadFactoryBuilder().setNameFormat("concurrentRpcInvokeExecutor" ).build(),
                    new ThreadPoolExecutor.CallerRunsPolicy()
            )
    );

    public static ExecutorService getConcurrentRpcInvokeExecutor() {
        return concurrentRpcInvokeExecutor;
    }

}
