package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.platform.empower.product.client.enums.StoreCategorySceneIdentityEnum;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category.DeleteMerchantStoreCategoryRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category.PoiStoreCategoryQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */

@Data
@ApiModel("门店店内分类基本请求参数")
public class StoreCategoryRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分类ID", required = true)
    private String categoryId;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.categoryId == null) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }

    public DeleteMerchantStoreCategoryRequest toPoiRpcReq(User user) {
        DeleteMerchantStoreCategoryRequest rpcReq = new DeleteMerchantStoreCategoryRequest();
        rpcReq.setCategoryId(Long.valueOf(categoryId));
        rpcReq.setMerchantId(user.getTenantId());
        rpcReq.setNewStoreGroupId(storeId);
        rpcReq.setOperatorId(user.getAccountId());
        rpcReq.setOperatorAccount(user.getAccountName());
        rpcReq.setSceneIdentity(StoreCategorySceneIdentityEnum.POI.getCode());
        return rpcReq;
    }

    public PoiStoreCategoryQueryRequest toSimpleQueryReq(User user) {
        PoiStoreCategoryQueryRequest rpcReq = new PoiStoreCategoryQueryRequest();
        rpcReq.setMerchantId(user.getTenantId());
        rpcReq.setStoreId(storeId);
        rpcReq.setCategoryIdList(Lists.newArrayList(Long.valueOf(categoryId)));
        return rpcReq;
    }
}
