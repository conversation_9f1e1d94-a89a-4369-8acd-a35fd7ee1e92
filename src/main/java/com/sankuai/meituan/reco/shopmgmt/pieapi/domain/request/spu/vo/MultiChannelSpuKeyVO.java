package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        name = "门店多渠道商品key",
        description = "门店多渠道商品key"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MultiChannelSpuKeyVO {
    @FieldDoc(
            description = "渠道id"
    )
    @ApiModelProperty(value = "渠道id", required = true)
    private List<Integer> channelIds;

    @FieldDoc(
            description = "商品编码"
    )
    @ApiModelProperty(value = "商品编码", required = true)
    private String customSpuId;
}
