package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/2/7 16:40
 **/
@ApiModel("门店当前有排班的员工VO")
@TypeDoc(name = "门店当前有排班的员工VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScheduledEmployeeVO {
    @ApiModelProperty("账号id")
    @FieldDoc(name = "账号id")
    private Long accountId;

    @ApiModelProperty("员工id")
    @FieldDoc(name = "员工id")
    private Long employeeId;

    @ApiModelProperty("员工姓名")
    @FieldDoc(name = "员工姓名")
    private String employeeName;

    @ApiModelProperty("班次id")
    @FieldDoc(name = "班次id")
    private Long shiftId;
}