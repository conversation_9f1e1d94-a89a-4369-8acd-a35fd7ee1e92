package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.consumable.ConsumableItemDetail;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.SaleAttrConfigBO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.SaleAttrLimitConfigBO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.TenantSpuRouteConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.config.GoodsSpecialMenuConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.delivery.DeliveryCompleteConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.picking.PickConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ProblemSpuCountDetailVO;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.warehouse.TradeShippingGrayService;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.TimeUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PriceMigrateConfigDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PriceMigrateGrayMccConfigVO;
import com.sankuai.meituan.util.ConfigUtilAdapter;

import lombok.extern.slf4j.Slf4j;

import static com.dianping.lion.client.Lion.getConfigRepository;

/**
 * MCC配置工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MccConfigUtil {

    private static final String BOOTH_REVENUE_FROM_SETTLEMENT_ALL = "all";

    private static final String ORDER_API_APPKEY = "com.sankuai.shangou.qnh.orderapi";

    private static final long ONE_DAY_SECONDS = 24 * 60 * 60L;


    /**
     * 价格图标是否直接展示
     *
     * @return
     */
    public static boolean isPriceTrendIconDirectShow() {
        return ConfigUtilAdapter.getBoolean("price.trend.icon.direct.show");
    }

    /**
     * 一个订单是否展示多条售后
     *
     * @return
     */
    public static boolean isShowWaitAuditRefundList() {
        return ConfigUtilAdapter.getBoolean("show.wait.audit.refund.list", false);
    }

    /**
     * 报错文案配置
     *
     * @return
     */
    public static String getErrorInformation() {
        return ConfigUtilAdapter.getString("errmsg_standard_config", "系统维护中，请22:00点之后再进行操作，如有疑问请咨询您的渠道经理。");
    }

    public static String getSysUpdateInformation() {
        return ConfigUtilAdapter.getString("errmsg_system_update", "系统已升级，请返回首页重新进入后再使用。");
    }

    public static boolean getinterceptor_enable() {
        return ConfigUtilAdapter.getBoolean("interceptor_enable", true);
    }

    public static String getCoreTagName() {
        return ConfigUtilAdapter.getString("core_tag_name", "核心");
    }

    public static String getCommonTagName() {
        return ConfigUtilAdapter.getString("common_tag_name", "常规");
    }

    public static boolean isPriceMigrateGraySwitch(String priceModule, Long tenantId, Long storeId) {

        // 配置规则示例：{"priceModelSwitchMap":{"module":{"switchStatus":true,"crayTenantStoreMap":{"1000094":["4987271"]}}}}
        String priceSwitchConfig = ConfigUtilAdapter.getString("price.migrate.switch", "");
        if (org.apache.commons.lang.StringUtils.isBlank(priceSwitchConfig) || StringUtils.isBlank(priceModule)) {
            return true;
        }

        try {
            PriceMigrateGrayMccConfigVO migrateGrayMccConfigBO = JacksonUtils.fromJson(priceSwitchConfig,
                    PriceMigrateGrayMccConfigVO.class);

            Map<String, PriceMigrateConfigDetailVO> switchMap = migrateGrayMccConfigBO.getPriceModelSwitchMap();

            // 1.若当前功能模块未设置配置，则表明走新模块
            if (MapUtils.isEmpty(switchMap) || !switchMap.containsKey(priceModule)) {
                return true;
            }

            // 2.若当前模块配置为关闭，或不在灰度列表中，则直接走原流程
            Map<Long, List<Long>> crayTenantStoreMap = switchMap.get(priceModule).getCrayTenantStoreMap();
            if (BooleanUtils.isFalse(switchMap.get(priceModule).getSwitchStatus())
                    || (MapUtils.isNotEmpty(crayTenantStoreMap) && !crayTenantStoreMap.containsKey(tenantId))) {
                return false;
            }

            // 3. 在功能模块开关开启下，若灰度租户列表为空，或当前租户的门店列表为空，则表示走新接口
            if (MapUtils.isEmpty(crayTenantStoreMap) || CollectionUtils.isEmpty(crayTenantStoreMap.get(tenantId))) {
                return true;
            }

            // 4.若包含当前门店，则走新接口；不包含当前门店，则走老接口
            return Objects.isNull(storeId) || crayTenantStoreMap.get(tenantId).contains(storeId);
        } catch (Exception e) {
            log.warn("price migrate gray switch config is error, config [{}].", priceSwitchConfig);
        }

        return true;
    }

    public static String getLowestSecDepRemainAmountRatioByTargetKey(String targetKey) {
        String lowestRatioStr = ConfigUtilAdapter.getString("sec.dep.lowest.remain.amount.ratio", "{\"1000061\":\"20\"}");
        if (StringUtils.isBlank(lowestRatioStr)) {
            return null;
        }
        try {
            Map<String, String> ratioMap = JSONObject.parseObject(lowestRatioStr, Map.class);
            if (MapUtils.isNotEmpty(ratioMap)) {
                return ratioMap.get(targetKey);
            }
        } catch (Exception e) {
            log.error("租户保证金余额最低比例map解析失败", e);
        }
        return null;
    }


    public static String getPlanIntroducePictureUrl() {
        return ConfigUtilAdapter.getString("appraise.plan.introduce");
    }

    /**
     * 获取新营收逻辑的租户
     *
     * @return
     */
    public static List<String> getNewRevenueTenantIds() {
        String newRevenueTenant = ConfigUtilAdapter.getString("newRevenueTenant", "");
        return Lists.newArrayList(StringUtils.split(newRevenueTenant, ","));
    }



    public static String getAssistantTaskJumpUrl(Integer taskType) {
        String jumpUrlMapStr = ConfigUtilAdapter.getString("assistant.task.jumpUrl", "");
        Map<String, String> jumpUrlMap = com.meituan.linz.boot.util.JacksonUtils.parseMap(jumpUrlMapStr, String.class, String.class);
        return jumpUrlMap.get(String.valueOf(taskType));
    }

    public static String getBadCaseTagName() {
        return ConfigUtilAdapter.getString("badcase.tagname", "易差评");
    }

    public static boolean useNewAppraiseService() {
        return ConfigUtilAdapter.getBoolean("useNewAppraiseService", false);
    }


    /**
     * TODO 可以移除此开关
     *
     * @return
     */
    public static boolean useSacAuthentication(){
        return ConfigUtilAdapter.getBoolean("useSacAuthentication", false);
    }


    public static boolean useSacAuthenticationV2() {
        return ConfigUtilAdapter.getBoolean("useSacAuthentication2", false);
    }

    /**
     * 动态经营模块配置
     *
     * @return 菜单 CODE 数组
     */
    public static List<String> dynamicRevisionHomepageMenuCodes() {
        try {
            String codes = ConfigUtilAdapter.getString("dynamic_revision_homepage_menu_codes", "[]");
            return JSON.parseArray(codes, String.class);
        } catch (Exception e) {
            log.error("解析配置 dynamic_revision_homepage_menu_codes 异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 摊位营收/账单灰度门店
     * @param shopId
     * @return
     */
    public static boolean isBoothRevenueFromSettlement(Long shopId) {
        String boothFromSettlementShopsConfig = ConfigUtilAdapter.getString("booth_revenue_from_settlement_shops", "");

        //如果配置的是all全量切换
        if (BOOTH_REVENUE_FROM_SETTLEMENT_ALL.equals(boothFromSettlementShopsConfig)) {
            return true;
        }

        try {
            if (StringUtils.isBlank(boothFromSettlementShopsConfig)) {
                return false;
            }
            else {
                List<Long> boothFromSettlementShops = Splitter.on(",").trimResults().splitToList(boothFromSettlementShopsConfig).stream()
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                return boothFromSettlementShops.contains(shopId);
            }
        }
        catch (Exception e) {
            log.error("摊位营收查询结算系统配置格式异常，配置值:{}, 降级返回false", boothFromSettlementShopsConfig);
            return false;
        }
    }

    /**
     * 歪马送酒租户id
     * @return
     */
    public static List<String> getDHTenantIdList(){
        String tenantIdStr=ConfigUtilAdapter.getString("drunk.horse.pieapi.tenant.list","1000395");
        if(org.apache.commons.lang3.StringUtils.isEmpty(tenantIdStr)){
            return Collections.emptyList();
        }
        return Splitter.on(",").splitToList(tenantIdStr);
    }

    /**
     * 是否是歪马租户
     */
    public static Boolean checkIsDHTenant(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            return false;
        }
        List<String> dhTenantIdList = getDHTenantIdList();
        return dhTenantIdList.contains(tenantId.toString());
    }

    public static Date getConfirmLetterOnlineDate() {
        String defaultConfirmLetterOnlineDate = "2012-12-30 00:00:00";
        try {
            String confirmLetterOnlineDate = ConfigUtilAdapter.getString("contract.confirm.letter.online.date");
            if (StringUtils.isBlank(confirmLetterOnlineDate)) {
                confirmLetterOnlineDate = defaultConfirmLetterOnlineDate;
            }
            return TimeUtils.parseAsDate(confirmLetterOnlineDate, TimeUtils.DATE_TIME);
        }
        catch (Exception e) {
            log.error("解析确认函上线日期异常", e);
            return TimeUtils.parseAsDate(defaultConfirmLetterOnlineDate, TimeUtils.DATE_TIME);
        }
    }

    public static boolean isAppAddBoxAndPriceStockSwitchOn(){
        return ConfigUtilAdapter.getBoolean("is_app_add_box_and_price_stock_switch_on", false);
    }

    /**
     * 通过配置判断是否是牵牛花请求
     */
    public static boolean isQnhRequest(Long tenantId, Long storeId) {
        String qnhTenantIds = ConfigUtilAdapter.getString("qnh_tenant_ids", "");
        if (StringUtils.isEmpty(qnhTenantIds)) {
            return false;
        }

        if (!qnhTenantIds.contains(String.valueOf(tenantId))) {
            return false;
        }

        String qnhStoreIds = ConfigUtilAdapter.getString("qnh_store_ids", "");
        if (StringUtils.isEmpty(qnhStoreIds)) {
            return false;
        }

        return "*".equals(qnhStoreIds) || qnhStoreIds.contains(String.valueOf(storeId));

    }

    public static int getProblemSpuRepairMaxSize() {
        return ConfigUtilAdapter.getInt("problem.spu.repair.count.max", 10);
    }
    /**
     * 歪马预订单立即发配送功能开关
     */
    public static boolean immediatelyDeliveryGerySwitch() {
        return Lion.getConfigRepository().getBooleanValue("drunk.horse.immediately.delivery.gery.switch", true);
    }

    public static List<ProblemSpuCountDetailVO> getProblemSpuCountDetailList() {
        String problemSpuCountDetails = ConfigUtilAdapter.getString("problem.spu.count.detail", "");
        if (StringUtils.isBlank(problemSpuCountDetails)) {
            return null;
        }
        return JSON.parseArray(problemSpuCountDetails, ProblemSpuCountDetailVO.class);
    }

    public static List<ProblemSpuCountDetailVO> getProblemSpuCountNewDetailList() {
        String problemSpuCountDetails = ConfigUtilAdapter.getString("problem.spu.count.newDetail", "");
        if (StringUtils.isBlank(problemSpuCountDetails)) {
            return null;
        }
        return JSON.parseArray(problemSpuCountDetails, ProblemSpuCountDetailVO.class);
    }

    /**
     * 是否无erp新异常查询灰度
     * @param tenantId 租户id
     * @return
     */
    public static boolean isNoErpNewQueryAbnormalGray(Long tenantId){
        try{
            String noErpNewAbnormalQueryTenantIdsStr = ConfigUtilAdapter.getString("assistant.noErp.newAbnormalQuery.tenantIds", "[]");
            if (StringUtils.isBlank(noErpNewAbnormalQueryTenantIdsStr)){
                return false;
            }

            // 全量配置
            if (noErpNewAbnormalQueryTenantIdsStr.contains("*")){
                return true;
            }

            List<Long> tenantIds = JSONObject.parseArray(noErpNewAbnormalQueryTenantIdsStr, Long.class);
            if (CollectionUtils.isEmpty(tenantIds)){
                return false;
            }

            return tenantIds.contains(tenantId);
        }catch (Exception e){
            log.warn("无erp新异常查询灰度租户列表字符串解析异常", e);
            return false;
        }
    }

    /**
     * 是否erp新异常查询灰度
     * @param tenantId 租户id
     * @return
     */
    public static boolean isErpNewQueryAbnormalGray(Long tenantId){
        try{
            String erpNewAbnormalQueryTenantIdsStr = ConfigUtilAdapter.getString("assistant.erp.newAbnormalQuery.tenantIds", "[]");
            if (StringUtils.isBlank(erpNewAbnormalQueryTenantIdsStr)){
                return false;
            }

            // 全量配置
            if (erpNewAbnormalQueryTenantIdsStr.contains("*")){
                return true;
            }

            List<Long> tenantIds = JSONObject.parseArray(erpNewAbnormalQueryTenantIdsStr, Long.class);
            if (CollectionUtils.isEmpty(tenantIds)){
                return false;
            }

            return tenantIds.contains(tenantId);
        }catch (Exception e){
            log.warn("erp新异常查询灰度租户列表字符串解析异常", e);
            return false;
        }
    }

    public static boolean deliveryErrorSubTypeSwitch(){
        return Lion.getConfigRepository().getBooleanValue("delivery.error.sub.type.switch", false);
    }

    public static boolean ssrfCheckUrlSwitch(){
        return ConfigUtilAdapter.getBoolean("ssrf_check_url_switch", false);
    }

    public static Boolean interruptUrlSsrf() {
        return ConfigUtilAdapter.getBoolean("interrupt_url_ssrf", false);
    }

    public static List<String> getUrlWhileList() {
        String urls = ConfigUtilAdapter.getString("url_white_list", "[]");
        try {
            return JSON.parseArray(urls, String.class);
        } catch (Exception e) {
            log.warn("getUrlWhileList [{}] mcc config exception. ", urls, e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取过滤工作台经营模块配置，外部库存对接的门店需过滤库存子模块： ["moduleCode1","moduleCode2"]
     * @return
     */
    public static List<String> getStockDockingFilterModuleConfig() {
        String configValue = ConfigUtilAdapter.getString("stock.docking.management.module.blacklist", "[]");
        try {
            return JSON.parseArray(configValue, String.class);
        } catch (Exception e) {
            log.warn("getStockDockingFilterModuleConfig [{}] mcc config exception. ", configValue, e);
        }

        return Collections.emptyList();
    }

    /**
     * 获取过滤工作台库存待办权限配置，外部库存对接的门店需过滤库存待办子模块 ["TODO1","TODO2"]
     * @return
     */
    public static List<String> getStockDockingFilterWorkbenchConfig() {
        String configValue = ConfigUtilAdapter.getString("stock.docking.workbench.module.blacklist", "[]");
        try {
            return JSON.parseArray(configValue, String.class);
        } catch (Exception e) {
            log.warn("getStockDockingFilterWorkbenchConfig [{}] mcc config exception. ", configValue, e);
        }

        return Collections.emptyList();
    }

    /**
     * 歪马送酒租户 扫码出库
     * 按照温度属性拆分拣货项 灰度门店
     */
    public static boolean isGrayStore4SplitByTemperature(Long storeId) {
        String configValue = Lion.getString("com.sankuai.waimai.sc.pickselectservice",
                "split.pick.task.by.temperature.store.list","");
        if (org.apache.commons.lang3.StringUtils.isBlank(configValue)) {
            return false;
        }
        if ("-1".equals(configValue)) {
            return true;
        }
        return Splitter.on(",").splitToList(configValue).contains(String.valueOf(storeId));
    }


    /**
     * 获取axb隐私号灰度门店wmPoiId -- 默认测试门店
     *
     * @return 灰度门店列表
     */
    public static Set<String> getAxBPrivacyPhonePoiIds() {
        String configStr = ConfigUtilAdapter.getString("axb.privacy.phone.poiIds", "730620");
        if (StringUtils.isEmpty(configStr)) {
            return new HashSet<>();
        }
        String[] poiIds = configStr.split(",");
        return new HashSet<>(Arrays.asList(poiIds));
    }

    /**
     * 是否全部使用axb隐私号 -- 默认false
     *
     * @return .
     */
    public static boolean isAllUserAxBPrivacyPhone() {
        return ConfigUtilAdapter.getBoolean("axb.privacy.phone.switch", false);
    }

    /**
     * AXB 隐私号有效时长 -- 默认三小时:单位秒
     *
     * @return .
     */
    public static Integer axbPrivacyPhoneValidTime() {
        return ConfigUtilAdapter.getInt("axb.privacy.phone.validTime", 10800);
    }

    /**
     * 骑手端联系用户入口有效时长 -- 默认7天: 单位秒
     *
     * @return .
     */
    public static Long contactUserDuration() {
        return ConfigUtilAdapter.getLong("contact.user.duration", 7 * 24 * 60 * 60);
    }


    /**
     * 获取使用AXB隐私号的租户ID
     *
     * @return .
     */
    public static Set<String> getAxbPrivacyPhoneTenantIds() {
        String configStr = ConfigUtilAdapter.getString("axb.privacy.phone.tenantIds", "1001197");
        if (StringUtils.isEmpty(configStr)) {
            return new HashSet<>();
        }
        String[] tenantIds = configStr.split(",");
        return new HashSet<>(Arrays.asList(tenantIds));
    }

    /**
     * 歪马临时骑手的角色id
     */
    public static List<String> tempRiderRoleIds() {
        String roleIds = ConfigUtilAdapter.getString("temp.rider.role.ids","");
        if (StringUtils.isBlank(roleIds)) {
            return Collections.emptyList();
        }
        return Splitter.on(",").splitToList(roleIds);
    }
    public static String getImGoodsConfig() {
        return ConfigUtilAdapter.getString("im.goods.config");
    }


    // 领用对应的其他出入库类型：物料领用、装备领用
    public static List<Long> borrowOtherTaskTypeIds() {
        try {
            return Splitter.on(",")
                    .trimResults()
                    .splitToList(Lion.getConfigRepository("com.sankuai.drunkhorsemgmt.wmsapi").get("borrow_other_task_type_ids", "")).stream()
                    .filter(StringUtils::isNotBlank)
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("borrow_other_task_type_ids config error ", e);
            return Collections.emptyList();
        }
    }

    // 归还对应的其他出入库类型：物料归还、装备归还
    public static List<Long> returnOtherTaskTypeIds() {
        try {
            return Splitter.on(",")
                    .trimResults()
                    .splitToList(Lion.getConfigRepository("com.sankuai.drunkhorsemgmt.wmsapi").get("return_other_task_type_ids", "")).stream()
                    .filter(StringUtils::isNotBlank)
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("return_other_task_type_ids config error ", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取退款审核渠道配置
     * @return
     */
    public static List<Integer> getRefundOrderbizTypeList(){
        String channelStr = ConfigUtilAdapter.getString("query.refund.orderbizType.list","101,151,501,103,601,301");
        if(org.apache.commons.lang3.StringUtils.isEmpty(channelStr)){
            return Collections.emptyList();
        }
        return Splitter.on(",").splitToList(channelStr).stream().map(NumberUtils::toInt).collect(Collectors.toList());
    }

    /**
     * 区域规划租户id
     * @return
     */
    public static Long getRegionSelectTenantId() {
        return Lion.getLong("com.sankuai.sgxsupply.wxmall.eapi", "region.select.drunk.horse.tenant.id", -3L);
    }


    public static boolean isDrunkHorseTenant(Long tenantId){
        String tenantIdStr = Lion.getConfigRepository("com.sankuai.shangou.empower.orderbiz").get("drunk_horse_tenant");
        if (StringUtils.isBlank(tenantIdStr) || tenantId == null) {
            return false;
        }
        Set<Long> tenantIdSet = Splitter.on(",")
                .trimResults()
                .splitToList(tenantIdStr).stream()
                .map(Long::parseLong)
                .collect(Collectors.toSet());
        return tenantIdSet.contains(tenantId);
    }

    public static boolean isWatermarkSwitchGrayStore(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository().getList("watermark.gray.store.ids", Long.class, Collections.emptyList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(-1L, grayStoreIds.get(0))) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    /**
     * 修改库存时是否同步推送渠道开关
     * @return 是否同步推送渠道
     * TRUE：修改库存后同步推送渠道
     * FALSE：修改库存后不同步推送渠道，由库存系统异步推送
     */
    public static boolean isPushChannelSynchronouslyWhenUpdateStock() {
        return ConfigUtilAdapter.getBoolean("is_push_channel_synchronously_when_update_stock", true);
    }

    public static Map<Integer,Integer> getPlatformToSelfConfig(){
        String content = ConfigUtilAdapter.getString("delivery_platform_to_self_config", "100=15|300=10");
        if(StringUtils.isEmpty(content)){
            return Collections.emptyMap();
        }
        List<String> strList=Splitter.on("|").splitToList(content);
        if(CollectionUtils.isEmpty(strList)){
            return Collections.emptyMap();
        }
        Map<Integer,Integer> configMap = new HashMap<>();
        strList.forEach(str->{
            List<String> dataList = Splitter.on("=").splitToList(str);
            if(CollectionUtils.isEmpty(dataList) || dataList.size()!=2){
                return;
            }
            configMap.put(Integer.parseInt(dataList.get(0)),Integer.parseInt(dataList.get(1)));
        });
        return configMap;
    }

    public static boolean isShowDeliveryOperateItem() {
        return ConfigUtilAdapter.getBoolean("is_show_delivery_operate_item", false);
    }


    public static boolean isDeliveryChannelConfigStore(Long storeId) {
        try {
            String storeIdListStr = Lion.getConfigRepository("com.sankuai.sgfulfillment.tms")
                    .get("delivery.channel.config.storeId.list", org.apache.commons.lang3.StringUtils.EMPTY);
            // 如果开关对应的字符串为空，全量放开
            if(org.apache.commons.lang3.StringUtils.isEmpty(storeIdListStr)){
                return true;
            }
            return Splitter.on(",").splitToList(storeIdListStr).contains(String.valueOf(storeId));
        } catch (Exception exception) {
            log.error("get deliveryChannelConfigStoreIds error", exception);
            return false;
        }
    }

    public static boolean isDeliveryChannelQuery4Enum() {
        try {
            return Lion.getConfigRepository("com.sankuai.sgfulfillment.tms").getBooleanValue("delivery.channel.query.degrade.switch", false);
        } catch (Exception exception) {
            log.error("get isDeliveryChannelQuery4Enum get(delivery.channel.query.degrade.switch) error", exception);
            return false;
        }
    }

    public static boolean showDeliveryOperateItemSwitch() {
        return ConfigUtilAdapter.getBoolean("show_delivery_operate_item", true);
    }

    public static Integer postDeliveryProofPhotoTimeLimit() {
        return Lion.getConfigRepository().getIntValue("post.delivery.proof.photo.time.limit", 2 * 60);
    }

    // 配销需求的回滚开关，若关闭，把配销改动影响到老流程的部分降级
    public static boolean isDistributionOrderEnable() {
        return ConfigUtilAdapter.getBoolean("outbound.distribution.order.enable", true);
    }

    public static boolean isPurchaseOrderEnable() {
        return ConfigUtilAdapter.getBoolean("outbound.purchase.order.enable", true);
    }

    public static List<Long> getFulfillWarningMessageNotifyGroupIds() {
        return Lion.getConfigRepository().getList("fulfill.warning.message.notify.group.ids", Long.class);
    }

    public static String getUploadImageDegradeNotifyMessageTemplate() {
        return Lion.getConfigRepository().get("upload.image.degrade.notify.message.template", "歪马-履约-故障通知（已执行降级方案）\n" +
                "【故障表现】：扫码出库图片上传多次失败\n" +
                "【故障时间】：%s，暂未恢复，等待通知\n" +
                "【故障影响】：扫码出库、送达拍照等上传图片功能失败\n" +
                "【降级方案】：扫码出库上传图片功能已自动降级为不强制上传\n" +
                "【故障原因】：集团图片服务异常");
    }

    public static String getUploadImageRecoverNotifyMessageTemplate() {
        return Lion.getConfigRepository().get("upload.image.recover.notify.message.template", "歪马-履约-故障恢复通知（降级方案已撤回）\n" +
                "【故障表现】：扫码出库图片上传多次失败\n" +
                "【故障恢复时间】：%s \n" +
                "【故障影响】：扫码出库、送达拍照等上传图片功能失败\n" +
                "【故障原因】：集团图片服务异常\n" +
                "【降级方案撤回】：扫码出库上传图片功能已恢复强制上传");
    }

    public static Integer getQueryOrderListBatchSize() {
        return Lion.getConfigRepository().getIntValue("query.order.list.batch.size", 30);
    }

    public static Integer getMaxActualPayAmtForThirdDelivery() {
        return Lion.getConfigRepository().getIntValue("max.actual.pay.third.delivery", 150 * 100);
    }

    public static boolean isDHAggDeliveryGrayStore(long poiId) {
        try {
            List<Long> grayPoiList = Lion.getConfigRepository("com.sankuai.sgfulfillment.tms").getList("dh.agg.delivery.gray", Long.class);
            //-1则全量
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(grayPoiList) && grayPoiList.size() == 1 && grayPoiList.get(0).equals(-1L)) {
                return true;
            }
            return Optional.ofNullable(grayPoiList).orElse(Lists.newArrayList()).contains(poiId);
        } catch (Exception e) {
            log.error("isDHAggDeliveryGrayStore error", e);
            return false;
        }

    }

    public static boolean needBlockAllotOutOperation(Long tenantId){
        try {
            List<Long> blockedTenantIds = Lion.getConfigRepository("com.sankuai.waimai.sc.pickselectservice")
                    .getList("allot.outbound.block.tenant", Long.class, new ArrayList<>());

            return blockedTenantIds.contains(tenantId);
        } catch (Exception e) {
            log.error("查询调拨出库单阻断开关异常，将兜底为不阻断", e);
            return false;
        }
    }

    public static Long getWaimaTenantId() {
       return Lion.getConfigRepository("com.sankuai.shangou.waima.support").getLongValue("waima.tenant.id", 1000395L);
    }

    public static boolean queryQuestionnaireSwitch() {
        return Lion.getConfigRepository().getBooleanValue("query.questionnaire.switch", true);
    }

    public static boolean isNewPickGrayStore(long storeId) {
        return true;
    }

    public static boolean isGrayWiderShippingAreaStores(long storeId) {
        List<Long> grayStores = getConfigRepository("com.sankuai.shangou.supplychain.ofapp").getList("wider.shipping.area.stores", Long.class);
        if (CollectionUtils.isNotEmpty(grayStores) && grayStores.size() == 1 && grayStores.get(0).equals(-1L)) {
            return true;
        }
        return grayStores.contains(storeId);
    }

    public static String getTagHint(String lionKey, String defaultValue) {
        return getConfigRepository("com.sankuai.shangou.supplychain.ofapp").get(lionKey, defaultValue);
    }

    /**
     * sn弱校验门店名单
     */
    public static boolean checkIsSnWeekCheckStore(Long storeId) {
        try {
            if (Objects.isNull(storeId)) {
                return false;
            }

            List<Long> storeIds = Lion.getConfigRepository("com.sankuai.waimai.sc.pickselectservice").getList("sn.weak.check.store.ids", Long.class);

            if (CollectionUtils.isEmpty(storeIds)) {
                return false;
            }

            if (storeIds.size() == 1 && Long.valueOf(-1L).equals(storeIds.get(0))) {
                return true;
            }

            return storeIds.contains(storeId);
        } catch (Exception e) {
            log.error("checkIsSnWeekCheckStore error", e);
            Cat.logEvent("SN_WEAK_CHECK", "GET_SN_WEAK_CHECK_SWITCH_ERROR");
            return true;
        }

    }

    /**
     * 获取歪马商品侧的温度属性与拣货侧的温度属性映射关系
     * 拣货的配置，统一存放在OIO里
     */
    public static Map<String, String> getPropertyColorMap() {
        Map<String, String> defaultMap = new HashMap<>();
        defaultMap.put("冰冻","#198CFF");
        defaultMap.put("冷藏","#198CFF");
        defaultMap.put("冰镇","#198CFF");
        defaultMap.put("常温","#FF6A00");
        defaultMap.put("一半冰一半不冰","#198CFF");
        defaultMap.put("自定义：备注","#999999");
        defaultMap.put("需要红酒开瓶器","#FF192D");

        return Lion.getConfigRepository("com.sankuai.shangou.logistics.oio").getMap("drunk.horse.property.color.map", String.class, defaultMap);
    }

    public static boolean getJudgeTrusteeshipSwitch(){
        return Lion.getConfigRepository().getBooleanValue("judge.trusteeship.switch", true);
    }

    public static boolean getPickQuerySwitch(){
        return Lion.getConfigRepository().getBooleanValue("pickselect.query.switch", true);
    }

    public static Integer getShortExpirationThreshold() {
       return Lion.getConfigRepository().getIntValue("short.expiration.threshold", 90);
    }

    public static Boolean isPickPhotoConfigurableGrayStore(Long storeId) {
        if (storeId == null) {
            return false;
        }
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.waimai.sc.pickselectservice").getList("pick.photo.configurable.gray.store", Long.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && grayStoreIds.contains(-1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static Boolean isDeliveryPhotoConfigurableGrayStore(Long storeId) {
        if (storeId == null) {
            return false;
        }
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.sgfulfillment.tms")
                .getList("delivery.photo.configurable.gray.store", Long.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && grayStoreIds.contains(-1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static List<DeliveryCompleteConfigVO> getDeliveryCompleteConfig() {
        String deliveryCompleteConfigStr = Lion.getConfigRepository().get("drunk.horse.delivery.complete.config");
        return JsonUtil.fromJson(deliveryCompleteConfigStr, new TypeReference<List<DeliveryCompleteConfigVO>>() {});

    }

    public static PickConfigVO getPickingConfig() {
        String pickConfigJsonStr = Lion.getConfigRepository().get("drunk.horse.pick.config");
        return JsonUtil.fromJson(pickConfigJsonStr, PickConfigVO.class);
    }

    public static boolean isReadGoodsGrayStore(Long offlineStoreId) {
        if (offlineStoreId == null) {
            log.warn("offlineStoreId is null");
            return false;
        }

        List<Long> storeList = getConfigRepository("com.sankuai.waimai.sc.pickselectservice")
                .getList("read.goods.gray.store", Long.class, Collections.emptyList());

        if (CollectionUtils.isEmpty(storeList)) {
            return false;
        }

        if (storeList.size() == 1 && Objects.equals(storeList.get(0), -1L)) {
            return true;
        }

        return storeList.contains(offlineStoreId);
    }

    public static HighPriceTagConfig getHighPriceTagConfig() {
        String jsonStr = getConfigRepository().get("high.price.tag.pair");
        return JSON.parseObject(jsonStr, HighPriceTagConfig.class);
    }

    public static Boolean isHighPriceTagGrayStore(Long storeId) {
        if (storeId == null) {
            return false;
        }
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.sgshopmgmt.empower.pieapi")
                .getList("high.price.tag.gray.store", Long.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && grayStoreIds.contains(-1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static int queryOrderCreateTimeBefore() {
        return getConfigRepository("com.sankuai.shangou.qnh.orderapi").getIntValue("query.order.create.time.before",7);
    }

    @Data
    public static class HighPriceTagConfig {
        private String tagCode;

        private String tagValueCode;
    }

    public static Boolean isTrainingGrayStore(Long storeId) {
        if (storeId == null) {
            return false;
        }

        List<Long> grayStoreList = getConfigRepository("com.sankuai.shangou.bizmng.labor")
                .getList("training.gray.store.ids", Long.class, Collections.emptyList());

        if (grayStoreList.size() == 1 && Objects.equals(grayStoreList.get(0), -1L)) {
            return true;
        }

        return grayStoreList.contains(storeId);
    }

    public static boolean isDisplayMtMedicineCategoryTree(Long tenantId) {
        String config = ConfigUtilAdapter.getString("is_display_mt_medicine_category_tree", "");

        if (StringUtils.isEmpty(config)) {
            return false;
        }

        // 配置的租户列表包含*, 表示全量
        Set<String> configTenantIds = Sets.newHashSet(StringUtils.split(config, ","));
        if (configTenantIds.contains("*")) {
            return true;
        }

        // 配置的租户列表包含当前租户, 表示当前租户执行灰度逻辑
        return configTenantIds.contains(String.valueOf(tenantId));
    }

    public static Boolean isDhScenePoi(Long storeId) {
        try {
            List<Long> stores = Lion.getConfigRepository(ORDER_API_APPKEY).getList("dh_scene_poi_switch", Long.class, org.assertj.core.util.Lists.newArrayList());
            //全量逻辑
            if (stores.size() == 1 && stores.get(0).equals(-1L)) {
                return true;
            }
            return stores.contains(storeId);
        } catch (Exception e) {
            log.error("isDhScenePoi error", e);
            return false;
        }
    }

    public static List<String> getDhTurnAggLimitSceneList() {
        try {
            return getConfigRepository(ORDER_API_APPKEY).getList("dh.turn.agg.limit.scene", String.class, Lists.newArrayList("餐馆"));
        } catch (Exception e) {
            log.error("isDhScenePoi error", e);
            return Lists.newArrayList();
        }
    }


    public static boolean queryDeliveryExceptionByTmsApi(Long tenantId) {
        try {
            if (isDrunkHorseTenant(tenantId)) {
                return false;
            }
            List<Long> tenantIds = getConfigRepository(ORDER_API_APPKEY).getList("delivery.exception.count.from.tms.tanant", Long.class, Lists.newArrayList());
            return tenantIds.contains(tenantId) || tenantIds.contains(-1L);
        } catch (Exception e) {
            log.error("isDhScenePoi error", e);
            return false;
        }
    }

    /**
     * 新订单流水号开关
     */
    public static boolean getDaySeqNumSwitch(){
        return Lion.getConfigRepository("com.sankuai.sgfulfillment.tms")
                .getBooleanValue("day.seq.num.switch", true);
    }

    public static List<ConsumableItemDetail> getNeedManageStockConsumableItem() {
        return Lion.getConfigRepository("com.sankuai.shangou.logistics.oio").getList("consumable.goods.list", ConsumableItemDetail.class, Collections.emptyList());

    }

    public static Map<String/*sku*/, String/*warnQty*/> getManageStockConsumableItemWarnQtyMap() {
        return Lion.getConfigRepository("com.sankuai.shangou.logistics.oio").getMap("consumable.goods.warn.qty", String.class, Maps.newHashMap());

    }

    public static Long getBigSquirrelTenantId(){
        return ConfigUtilAdapter.getLong("big_squirrel_tenant_id", 1002114L);
    }

    private static final String OFAPP_APPKEY = "com.sankuai.shangou.supplychain.ofapp";
    public static boolean isNewPreOrderAssessGrayStore(long storeId) {
        List<Long> grayStoreIds = getConfigRepository(OFAPP_APPKEY).getList("new.pre.order.stores", Long.class, Lists.newArrayList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }
        if (grayStoreIds.size() == 1 && grayStoreIds.get(0).equals(-1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static Long preOrderAssessTimePlusMills() {
        return Lion.getConfigRepository(OFAPP_APPKEY).getLongValue("pre.order.plus.mills", 0L);
    }

    public static boolean isSealContainerReturnTimeoutNotifyGrayStore(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.sgfulfillment.tms").getList("seal.container.return.timeout.notify.gray.storeIds", Long.class, Collections.singletonList(-1L));
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(grayStoreIds.get(0), -1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static boolean isNewTransferRideStore(Long storeId) {
        try {
            List<Long> stores = Lion.getConfigRepository().getList("dh_new_rider_transfer_switch", Long.class, Lists.newArrayList());
            //全量逻辑
            if (stores.size() == 1 && stores.get(0).equals(-1L)) {
                return true;
            }
            return stores.contains(storeId);
        } catch (Exception e) {
            log.error("isDhScenePoi error", e);
            return false;
        }

    }

    public static boolean isSwitchBatchStockGrayStore(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.shangou.logistics.oio").getList("switch.batch.stock.gray.store.list", Long.class, Collections.emptyList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(-1L, grayStoreIds.get(0))) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    /**
     * app 待办 商品特殊菜单配置项
     *
     * @return
     */
    public static Map<String, GoodsSpecialMenuConfig> goodsSpecialMenuConfigMap() {
        try {
            String goodsSpecialMenuConfigStr = ConfigUtilAdapter.getString("goods.special.menu.config", "");
            if (StringUtils.isBlank(goodsSpecialMenuConfigStr)){
                return Collections.emptyMap();
            }
            List<GoodsSpecialMenuConfig> menuConfigs = JSONObject.parseArray(goodsSpecialMenuConfigStr, GoodsSpecialMenuConfig.class);
            return Fun.toMapQuietly(menuConfigs, GoodsSpecialMenuConfig::getMenuCode);
        }catch (Exception e){
            log.error("获取商品特殊菜单配置项错误", e);
            return Collections.emptyMap();
        }
    }
    /**
     *
     * 查询租户是否可以查询美团渠道活动信息
     */
    public static boolean isQueryStoreChannelSpuActivity(Long tenantId) {
        String config = ConfigUtilAdapter.getString("is_query_store_channel_spu_activity", "");
        if (StringUtils.isEmpty(config)) {
            return false;
        }

        // 配置的租户列表包含*, 表示全量
        Set<String> configTenantIds = Sets.newHashSet(StringUtils.split(config, ","));
        if (configTenantIds.contains("*")) {
            return true;
        }
        // 配置的租户列表包含当前租户, 表示当前租户执行灰度逻辑
        return configTenantIds.contains(String.valueOf(tenantId));
    }

    public static boolean storeMessageUpdate() {
        return ConfigUtilAdapter.getBoolean("store.message.update", true);
    }

    /**
     * 获取不可售异常code集合
     *
     * @return
     */
    public static List<String> noSaleAbnormalCodes() {
        try {
            String noSaleAbnormalCodesStr = ConfigUtilAdapter.getString("no.sale.abnormal.codes", "[]");
            if (StringUtils.isBlank(noSaleAbnormalCodesStr)){
                return Collections.emptyList();
            }
            return JSONObject.parseArray(noSaleAbnormalCodesStr, String.class);
        }catch (Exception e){
            log.error("获取不可售异常code集合配置项错误", e);
            return Collections.emptyList();
        }
    }

    public static Boolean acceptPickOrderSwitch(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository().getList("accept.pick.order.gray.store.list", Long.class, Collections.emptyList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(-1L, grayStoreIds.get(0))) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    /**
     * 判断入参门店ID是否从productbiz保存门店商品
     * 此开关用于将菜大全门店商品保存从ocms切换到productbiz
     * 由于只涉及一个租户，因此灰度使用门店维度
     */
    public static boolean isCdqSaveStoreSpuFromProductBiz(Long storeId) {
        String config = ConfigUtilAdapter.getString("is_cdq_save_store_spu_from_productbiz", "");
        if (StringUtils.isEmpty(config)) {
            return false;
        }

        // 配置的门店列表包含*, 表示全量
        Set<String> configStoreIds = Sets.newHashSet(StringUtils.split(config, ","));
        if (configStoreIds.contains("*")) {
            return true;
        }
        // 配置的门店列表包含当前门店, 表示当前门店执行灰度逻辑
        return configStoreIds.contains(String.valueOf(storeId));
    }

    /**
     * 是否直接从eapi调用库存接口，
     * true 是，库存交互不走商品
     * false 否，库存交互走商品
     * @param tenantId
     * @return
     */
    public static boolean isSaveSkuStockFromEApi(Long tenantId) {
        String config = Lion.getString("com.sankuai.waimai.sc.saascrmeapi", "is_save_sku_stock_from_eApi");
        if (StringUtils.isEmpty(config)) {
            return false;
        }

        // 配置的租户列表包含*, 表示全量
        Set<String> configTenantIds = Sets.newHashSet(StringUtils.split(config, ","));
        if (configTenantIds.contains("*")) {
            return true;
        }
        // 配置的租户列表包含当前租户, 表示当前租户执行灰度逻辑
        return configTenantIds.contains(String.valueOf(tenantId));

    }

    /**
     * 是否使用销售属性
     * @deprecated {@link com.sankuai.meituan.shangou.empower.productplatform.util.SwitchUtils#useSaleAttr(java.lang.Long)}
     */
    @Deprecated
    public static int saleAttrConfig(long tenantId) {
        try {
            String config = Lion.getString("com.sankuai.sgshopmgmt.productplatform", "sale_attr_config", "{}");
            SaleAttrConfigBO saleAttrConfig = com.meituan.linz.boot.util.JacksonUtils.parse(config, SaleAttrConfigBO.class);

            return saleAttrConfig.getTenantSaleAttrConfig(tenantId);
        }
        catch (Exception e) {
            log.error("获取sale_attr_config配置异常", e);
            return 0;
        }
    }

    public static SaleAttrLimitConfigBO getSaleAttrLimitConfig() {
        try {
            String config = Lion.getString("com.sankuai.sgshopmgmt.productbiz", "sale_attr_limit_config", "{\"channelAttrLimitConfigs\":[{\"channelId\":100,\"attrValueMaxLength\":25},{\"channelId\":200,\"attrValueMaxLength\":128},{\"channelId\":300,\"attrValueMaxLength\":30},{\"channelId\":2300,\"attrNameMaxLength\":64,\"attrValueMaxLength\":64}]}");
            return com.meituan.linz.boot.util.JacksonUtils.parse(config, SaleAttrLimitConfigBO.class);
        }
        catch (Exception e) {
            log.error("获取sale_attr_limit_config配置异常", e);
            return new SaleAttrLimitConfigBO();
        }
    }

    /**
     * 计算零售价时是否添加分位规则处理逻辑
     */
    public static boolean isCalPriceWithCentMode(Long tenantId) {
        String config = ConfigUtilAdapter.getString("calculate_price_with_cent_mode_tenants", "");
        if (StringUtils.isEmpty(config)) {
            return false;
        }

        // 配置的租户列表包含*, 表示全量
        Set<String> configTenantIds = Sets.newHashSet(StringUtils.split(config, ","));
        if (configTenantIds.contains("*")) {
            return true;
        }
        // 配置的租户列表包含当前租户, 表示当前租户执行灰度逻辑
        return configTenantIds.contains(String.valueOf(tenantId));
    }


    /**
     * 全部门店时是否展示地推拉新链接
     */
    public static boolean allStoreShowPullNewSwitch() {
        return ConfigUtilAdapter.getBoolean("all_store_show_pull_new_switch", false);
    }

    public static boolean isShowGoodsItemListGrayStore(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.shangou.supplychain.ofapp").getList("show.goods.item.list.gray.store.ids", Long.class, Collections.emptyList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(-1L, grayStoreIds.get(0))) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static List<String> getWineBottleOpenerSkuIds() {
        return Lion.getConfigRepository("com.sankuai.shangou.supplychain.ofapp").getList("wine.bottle.opener.sku.ids", String.class, Collections.singletonList("70013100302"));
    }

    public static Integer getQueryReviewSpuRemoveStoreIdsThreshold() {
        return Lion.getInt("com.sankuai.waimai.sc.saascrmeapi", "page_query_review_spu_remove_store_threshold", 2000);
    }

    /**
     * 青云配送订单详情无授权链接开关
     */
    public static boolean dapOrderLinkNoAuthSwitch() {
        return Lion.getConfigRepository().getBooleanValue("dap.order.link.no.auth.switch", true);
    }

    public static boolean updateTenantSpuToBiz(Long tenantId) {
        try {
            String configStr = ConfigUtilAdapter.getString("update_tenant_spu_to_biz", "{}");
            TenantSpuRouteConfig routeConfig = com.meituan.linz.boot.util.JacksonUtils.parse(configStr, TenantSpuRouteConfig.class);
            return routeConfig.routeToBiz(tenantId);
        }
        catch (Exception e) {
            log.error("获取updateTenantSpuToBiz配置异常", e);
            return false;
        }
    }

    /**
     * 是否是支持脱敏收货人信息的租户
     * @param tenantId 租户ID
     * @return true 是  false否
     */
    public static boolean isSupportDesensitizeReceiverInfoTenants(Long tenantId) {
        String configTenantIds = getConfigRepository(ORDER_API_APPKEY).get("support_desensitize_receiver_info_tenants", "");
        // 配置的租户列表包含ALL, 表示全量
        List<String> tenantIds = Lists.newArrayList(StringUtils.split(configTenantIds, ","));
        if (CollectionUtils.isNotEmpty(tenantIds) && tenantIds.get(0).equalsIgnoreCase("ALL")) {
            return true;
        }
        // 配置的租户列表包含当前租户, 表示当前租户执行灰度逻辑
        return tenantIds.contains(String.valueOf(tenantId));
    }

    /**
     * 脱敏收货人信息的时长配置
     * @return  脱敏时长  单位秒
     */
    public static Long getDesensitizeReceiverInfoTime() {
        return getConfigRepository(ORDER_API_APPKEY).getLongValue("desensitize_receiver_info_time", ONE_DAY_SECONDS);
    }

    public static List<String> getFilterMunichSubMenu() {
        try {
            return Lion.getConfigRepository().getList("filter.munich.sub.menu", String.class, Collections.singletonList("SMILE_ACT_WAIT_COLLECT"));
        } catch (Exception e) {
            log.error("getFilterMunichSubMenu error", e);
            return Collections.singletonList("SMILE_ACT_WAIT_COLLECT");
        }
    }

    /**
     * 是否歪马融合灰度租户
     */
    public static boolean isWaimaMergedTenant(Long tenantId) {
        String tenantList = Lion.getString("com.sankuai.shangou.platform.emproduct", "waima_merged_tenant_list", "");
        String tenantBlackList = ConfigUtilAdapter.getString("waima_merged_tenant_black_list", "");
        return isInTenantConfig(tenantList, tenantId) && !isInTenantConfig(tenantBlackList, tenantId);
    }

    private static boolean isInTenantConfig(String tenantConfig, Long tenantId) {
        if (org.apache.commons.lang3.StringUtils.isBlank(tenantConfig)) {
            return false;
        }
        Set<String> tenantIdSet = Sets.newHashSet(tenantConfig.split(","));
        return tenantIdSet.contains("*") || tenantIdSet.contains(String.valueOf(tenantId));
    }

    public static Boolean getLaunchPointForceSyncConfig(Long tenantId) {
        Map<String, Boolean> configMap = getConfigRepository("com.sankuai.shangou.logistics.dmp").getMap("launch_point_force_sync", Boolean.class, Maps.newHashMap());
        for (Map.Entry<String, Boolean> entry : configMap.entrySet()) {
            if ("-1".equals(entry.getKey())) {
                return true;
            }
            if (entry.getKey().contains(String.valueOf(tenantId))) {
                return entry.getValue();
            }
        }
        return false;
    }
}
