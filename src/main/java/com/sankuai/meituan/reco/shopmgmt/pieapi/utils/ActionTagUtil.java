package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.TagInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.ActionTagTypeEnum;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ActionTagUtil {

    public static List<TagInfoVO> ocmsOrderVoConvertTag(OCMSOrderVO ocmsOrderVO){
        if(ocmsOrderVO==null){
            return Collections.emptyList();
        }
        List<TagInfoVO> tagInfoVOList=new ArrayList<>();
        if(ocmsOrderVO.getIsNeedInvoice()!=null && ocmsOrderVO.getIsNeedInvoice()==1){
            TagInfoVO tagInfoVO=new TagInfoVO();
            tagInfoVO.setName(ActionTagTypeEnum.INVOICE.getTemplate());
            tagInfoVO.setType(ActionTagTypeEnum.INVOICE.getType());
            tagInfoVOList.add(tagInfoVO);
        }
        return tagInfoVOList;
    }

}
