package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.dianping.rhino.cluster.common.util.AssertUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2024/9/12
 */
@TypeDoc(
        description = "查询门店商品活动信息"
)
@Data
@ApiModel("查询门店商品活动信息")
@ToString
public class StoreSpuActivityQueryRequest {
    @FieldDoc(
            description = "spuId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "spuId")
    private String spuId;
    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id")
    private Integer channelId;
    public void selfCheck() {
        AssertUtil.notNull(channelId, "渠道Id不能为空");
        AssertUtil.notNull(spuId, "spuId不能为空");}
}
