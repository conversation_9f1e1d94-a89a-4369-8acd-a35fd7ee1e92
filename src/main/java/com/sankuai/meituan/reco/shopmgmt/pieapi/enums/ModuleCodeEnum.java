package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

/**
 * Created by yang<PERSON> on 19/1/23.
 */
public enum ModuleCodeEnum {
    PURCHASE_CATEGORY("10", "/storemanagement/purchase/category", "要货"),
    PURCHASE_COMMON_LIST("100100", "/storemanagement/purchase/commonlist", "要货"),
    PURCHASE_DETAIL("100101", "/storemanagement/purchase/detail", "要货"),
    PURCHASE_GET_SKU_BY_CATE("100102", "/storemanagement/purchase/getskubycate", "要货"),
    PURCHASE_LIST("100103", "/storemanagement/purchase/list", "要货"),
    PURCHASE_SEARCH_SKU("100104", "/storemanagement/purchase/searchsku", "要货"),
    PURCHASE_SEARCH_SKU_BY_UPC("100105", "/storemanagement/purchase/searchskubyupc", "要货"),
    PURCHASE_SUBMIT("100106", "/storemanagement/purchase/submit", "要货"),

    DELIVERY_LIST("20", "/storemanagement/delivery/list", "收货"),
    DELIVERY_CONFIRM("200100", "/storemanagement/delivery/confirm", "收货"),
    DELIVERY_DELIVERY_LIST("200101", "/storemanagement/delivery/deliverylist", "收货"),
    DELIVERY_DETAIL("200102", "/storemanagement/delivery/detail", "收货"),
    DELIVERY_NO_DELIVERY_ORDER_LIST("200103", "/storemanagement/delivery/nodeliveryorderlist", "收货"),
    DELIVERY_ORDER_DETAIL("200104", "/storemanagement/delivery/orderdetail", "收货"),
    DELIVERY_SEARCH_SKU("200105", "/storemanagement/delivery/searchsku", "收货"),
    DELIVERY_SEARCH_SKU_BY_UPC("200106", "/storemanagement/delivery/searchskubyupc", "收货"),
    DELIVERY_SUB_DELIVERY_LIST("200107", "/storemanagement/delivery/subdeliverylist", "收货"),
    DELIVERY_SUBMIT("200108", "/storemanagement/delivery/submit", "收货"),
    DELIVERY_UPDATE_NOTIFY_ORDER_STATUS("200109", "/storemanagement/delivery/updatenotifyorderstatus", "收货"),
    DELIVERY_WAIT_LIST("200110", "/storemanagement/delivery/waitlist", "收货"),

    RETURN_QUERY_RETURN_ORDER("30", "/storemanagement/return/queryReturnOrder", "退货"),
    RETURN_QUERY_REFUND_ORDER_SKU_DETAILS("300100", "/storemanagement/return/queryRefundOrderSkuDetails", "退货"),
    RETURN_QUERY_RETURN_ORDER_DETAIL("300101", "/storemanagement/return/queryReturnOrderDetail", "退货"),
    RETURN_SUBMIT_RETURN_ORDER("300102", "/storemanagement/return/submitReturnOrder", "退货"),
    REFUND_QUERY_CONTAINER_TYPE("300103", "/storemanagement/refund/queryContainerType", "退货"),
    REFUND_QUERY_SKU("300104", "/storemanagement/refund/querySku", "退货"),
    REFUND_SCAN_UPC("300105", "/storemanagement/refund/scanUpc", "退货"),
    REFUND_SKU_STOCK_DETAIL("300106", "/storemanagement/refund/skuStockDetail", "退货"),
    REFUND_UPDATE_ORDER_STATUS("300107", "/storemanagement/refund/updateOrderStatus", "退货"),

    BREAK_QUERY_BREAK_ORDER("40", "/storemanagement/break/queryBreakOrder", "报损"),
    BREAK_QUERY_BREAK_ORDER_DETAIL("400100", "/storemanagement/break/queryBreakOrderDetail", "报损"),
    BREAK_QUERY_BREAK_ORDER_SKU_DETAILS("400101", "/storemanagement/break/queryBreakOrderSkuDetails", "报损"),
    BREAK_SUBMIT_BREAK_ORDER("400102", "/storemanagement/break/submitBreakOrder", "报损"),
    BREAK_UPLOAD_PIC("400103", "/storemanagement/break/uploadPic", "报损"),

    ADJUST_LIST("50", "/storemanagement/adjust/list", "调价"),
    ADJUST_GOODS("500100", "/storemanagement/adjust/goods", "调价"),
    ADJUST_INFO("500101", "/storemanagement/adjust/info", "调价"),
    ADJUST_SAVE("500102", "/storemanagement/adjust/save", "调价"),
    ADJUST_SKU("500103", "/storemanagement/adjust/sku", "调价"),
    ADJUST_UPC_SKU("500104", "/storemanagement/adjust/upc/sku", "调价"),

    STOCKCHECK_QUERY_ORDER_LIST("60", "/storemanagement/stockcheck/queryOrderList", "盘点"),
    STOCKCHECK_ORDER_DETAIL("600100", "/storemanagement/stockcheck/orderDetail", "盘点"),
    STOCKCHECK_ORDER_LIST("600101", "/storemanagement/stockcheck/orderList", "盘点"),
    STOCKCHECK_ORDER_SUBMIT("600102", "/storemanagement/stockcheck/orderSubmit", "盘点"),
    STOCKCHECK_PULL_STOCK_CHECK_ORDER_SKU_DETAIL("600103", "/storemanagement/stockcheck/pullStockCheckOrderSkuDetail", "盘点"),
    STOCKCHECK_QUERY_ORDER_DETAIL("600104", "/storemanagement/stockcheck/queryOrderDetail", "盘点"),
    STOCKCHECK_QUERY_SUB_ORDER_DETAIL("600105", "/storemanagement/stockcheck/querySubOrderDetail", "盘点"),
    STOCKCHECK_SKU("600107", "/storemanagement/stockcheck/sku", "盘点"),
    STOCKCHECK_SUBMIT_SUB_STOCK_CHECK_ORDER("600108", "/storemanagement/stockcheck/submitSubStockCheckOrder", "盘点"),
    STOCKCHECK_UPC("600109", "/storemanagement/stockcheck/upc", ""),

    GOODS_LIST("70", "/storemanagement/goods/list", "商品查询"),
    GOODS_DETAIL("700100", "/storemanagement/goods/detail", "商品查询"),
    GOODS_DETAIL_BY_UPC("700101", "/storemanagement/goods/detailbyupc", "商品查询"),
    GOODS_MATERIAL_TRANSFER_GOODS_INFO_BY_UPC("700102", "/storemanagement/goods/materialTransferGoodsInfoByUpc", "商品查询"),
    GOODS_PROCESS_GOODS_INFO_BY_UPC("700103", "/storemanagement/goods/processGoodsInfoByUpc", "商品查询"),
    GOODS_QUERY_SUPPLIERS("700104", "/storemanagement/goods/querySuppliers", ""),

    ORDER_ALL("80", "/storemanagement/order/all", "订单"),
    ORDER_DETAIL("800100", "/storemanagement/order/detail", "订单"),
    ORDER_SEARCH("800101", "/storemanagement/order/search", "订单"),
    ORDER_SUBMIT("800102", "/storemanagement/order/submit", "订单"),

    HOMEPAGE_DATA("90", "/storemanagement/homepage/data", "首页"),
    HOMEPAGE_PENDING("900100", "/storemanagement/homepage/pending", "首页"),
    HOMEPAGE_PUSH_OPERATE("900101", "/storemanagement/homepage/pushOperate", "首页"),
    HOMEPAGE_PUSH_STATUS("900102", "/storemanagement/homepage/pushStatus", "首页"),

    TRANSFER_ORDERS("110", "/storemanagement/transfer/orders", ""),
    TRANSFER_ORDER("110100", "/storemanagement/transfer/order", ""),
    TRANSFER_ORDER_DETAIL("110101", "/storemanagement/transfer/order/detail", ""),

    PROCESS_ORDERS("120", "/storemanagement/process/orders", "加工"),
    PROCESS_ORDER("120100", "/storemanagement/process/order", "加工"),
    PROCESS_DETAIL("120101", "/storemanagement/process/order/detail", "加工"),

    LOCATION_QUERY_LOCATION_LIST("130", "/storemanagement/location/queryLocationList", "库位管理"),
    LOCATION_BATCH_BIND_LOCATION("130100", "/storemanagement/location/batchBindLocation", "库位管理"),
    LOCATION_BATCH_UNBIND_LOCATION("130101", "/storemanagement/location/batchUnbindLocation", "库位管理"),
    LOCATION_BIND_LOCATION("130102", "/storemanagement/location/bindLocation", "库位管理"),
    LOCATION_MOVE_LOCATION("130103", "/storemanagement/location/moveLocation", "库位管理"),
    LOCATION_QUERY_LOCATION_DETAIL("130104", "/storemanagement/location/queryLocationDetail", "库位管理"),

    PUSH_CONFIG_CONFIRM("999100", "/storemanagement/push/config/confirm", ""),
    PUSH_CONFIG_UUID("999101", "/storemanagement/push/config/uuid", ""),

    STORE_LIST("999102", "/storemanagement/store/list", ""),

    EMPLOYEE_DEVICE_CONFIRM("999103", "/storemanagement/employee/device/confirm", ""),
    EMPLOYEE_DEVICE_CANCEL("999104", "/storemanagement/employee/device/cancel", ""),

    ;

    private String code;
    private String url;
    private String desc;

    ModuleCodeEnum(String code, String url, String desc) {
        this.code = code;
        this.url = url;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getUrl() {
        return url;
    }

    public String getDesc() {
        return desc;
    }
}
