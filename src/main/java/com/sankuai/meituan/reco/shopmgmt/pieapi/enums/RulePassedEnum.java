package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <EMAIL>
 * @Date: 2020/9/16 15:37
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum  RulePassedEnum {

    UNKNOWN(-1, "未知"),
    NOT_PASSED(0, "不通过"),
    PASSED(1, "通过"),
    NOT_FIXED(2, "未订正");

    private int code;

    private String desc;


    public static RulePassedEnum getByCode(int code) {
        for (RulePassedEnum e : RulePassedEnum.values()) {
            if (e.getCode() == code ){
                return e;
            }
        }

        return null;
    }

}
