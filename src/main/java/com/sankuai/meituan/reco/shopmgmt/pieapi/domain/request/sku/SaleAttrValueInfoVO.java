package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SaleAttributeValueDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SkuSaleAttributeValueDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;

/**
 * @Author: luokai14
 * @Date: 2022/9/13 8:29 下午
 * @Mail: <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleAttrValueInfoVO {

    @FieldDoc(
            description = "属性规格Id"
    )
    @ApiModelProperty("属性规格Id")
    private String attrId;

    @FieldDoc(
            description = "属性规格Id"
    )
    @ApiModelProperty("属性规格值")
    private String attrValue;

    @FieldDoc(
            description = "规格属性名"
    )
    @ApiModelProperty("属性规格Id")
    private String attrName;

    @FieldDoc(
            description = "销售属性值ID"
    )
    @ApiModelProperty("销售属性值ID")
    private String attrValueId;

    @FieldDoc(
            description = "销售属性值关联的图片"
    )
    @ApiModelProperty("销售属性值关联的图片")
    private List<String> relatedImageUrls;

    @FieldDoc(
            description = "是否关联图片"
    )
    @ApiModelProperty("是否关联图片")
    private boolean imageRelated;


    public static SaleAttrValueInfoVO ofDTO (SaleAttributeValueDTO saleAttributeValueDTO){

        SaleAttrValueInfoVO saleAttrValueInfoVO = new SaleAttrValueInfoVO();
        Optional.ofNullable(saleAttributeValueDTO.getAttrId()).ifPresent(attrId -> saleAttrValueInfoVO.setAttrId(String.valueOf(attrId)));
        saleAttrValueInfoVO.setAttrValue(saleAttributeValueDTO.getAttrValue());
        saleAttrValueInfoVO.setAttrName(saleAttributeValueDTO.getAttrName());
        saleAttrValueInfoVO.setAttrValueId(saleAttributeValueDTO.getAttrValueId());
        saleAttrValueInfoVO.setRelatedImageUrls(saleAttributeValueDTO.getRelatedImageUrls());
        saleAttrValueInfoVO.setImageRelated(saleAttributeValueDTO.isImageRelated());
        return saleAttrValueInfoVO;
    }

    public static SaleAttrValueInfoVO ofBizDTO(SkuSaleAttributeValueDTO saleAttributeValueDTO) {
        SaleAttrValueInfoVO saleAttrValueInfoVO = new SaleAttrValueInfoVO();
        Optional.ofNullable(saleAttributeValueDTO.getAttrId()).ifPresent(attrId -> saleAttrValueInfoVO.setAttrId(String.valueOf(attrId)));
        saleAttrValueInfoVO.setAttrValue(saleAttributeValueDTO.getAttrValue());
        saleAttrValueInfoVO.setAttrName(saleAttributeValueDTO.getAttrName());
        saleAttrValueInfoVO.setAttrValueId(saleAttributeValueDTO.getAttrValueId());
        saleAttrValueInfoVO.setRelatedImageUrls(saleAttributeValueDTO.getRelatedImageUrls());
        saleAttrValueInfoVO.setImageRelated(saleAttributeValueDTO.isImageRelated());
        return saleAttrValueInfoVO;
    }

    public SaleAttributeValueDTO toDTO() {
        SaleAttributeValueDTO saleAttributeValueDTO = new SaleAttributeValueDTO();
        saleAttributeValueDTO.setAttrId(Optional.ofNullable(attrId).map(Long::valueOf).orElse(null));
        saleAttributeValueDTO.setAttrName(attrName);
        saleAttributeValueDTO.setAttrValue(attrValue);
        saleAttributeValueDTO.setAttrValueId(attrValueId);
        saleAttributeValueDTO.setRelatedImageUrls(relatedImageUrls);
        saleAttributeValueDTO.setImageRelated(imageRelated);
        return saleAttributeValueDTO;
    }

    public SkuSaleAttributeValueDTO toBizDTO() {
        SkuSaleAttributeValueDTO saleAttributeValueDTO = new SkuSaleAttributeValueDTO();
        saleAttributeValueDTO.setAttrId(Optional.ofNullable(attrId).map(Long::valueOf).orElse(null));
        saleAttributeValueDTO.setAttrName(attrName);
        saleAttributeValueDTO.setAttrValue(attrValue);
        saleAttributeValueDTO.setAttrValueId(attrValueId);
        saleAttributeValueDTO.setRelatedImageUrls(relatedImageUrls);
        saleAttributeValueDTO.setImageRelated(imageRelated);
        return saleAttributeValueDTO;
    }
}
