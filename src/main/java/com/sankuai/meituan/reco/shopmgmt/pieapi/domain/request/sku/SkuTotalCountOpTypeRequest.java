package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-01-10 15:30
 * @Description:
 */
@TypeDoc(
        description = "查分类下商品总数请求"
)
@Data
@ApiModel("查分类下商品总数请求")
public class SkuTotalCountOpTypeRequest {
    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "查询类型，1未上架，2未报价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "查询类型")
    @NotNull
    private Integer opType;

    @FieldDoc(
            description = "查询天数列表，0代表所有天", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "查询天数")
    @NotNull
    public List<Integer> days;

    @FieldDoc(
            description = "店内分类过滤。不传不过滤", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "店内分类过滤")
    public String storeCategory;

    @FieldDoc(
            description = "分类状态  1-未分类（未上线+分类=未分类 2-有分类(已上线-分类=未分类）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类状态  1-未分类（未上线+分类=未分类 2-有分类(已上线-分类=未分类）")
    private Integer hasStoreCategory;
}
