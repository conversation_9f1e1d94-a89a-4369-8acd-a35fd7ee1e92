package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto;

import lombok.Data;

/**
 * 门店数仓实时数据
 *
 * <AUTHOR>
 * @since 2021/9/3
 */
@Data
public class PoiSgRealTimeDataDto {

    /**
     * 有效订单量
     */
    private Integer finOrdNum;

    /**
     * 有效订单量-环比率
     */
    private Double finOrdNumCr;

    /**
     * 总营业额（单位：元）
     */
    private Double openAmtGtv;

    /**
     * 总营业额-环比率
     */
    private Double openAmtGtvCr;

    /**
     * 商品销售额（单位：元）
     */
    private Double prodAmt;

    /**
     * 商品销售额-环比率
     */
    private Double prodAmtCr;

    /**
     * 销售收入（单位：元）
     */
    private Double ordAmt;

    /**
     * 销售收入-环比率
     */
    private Double ordAmtCr;

    /**
     * 预计收入（单位：元）
     */
    private Double toPredictIncome;

    /**
     * 预计收入-环比率
     */
    private Double toPredictIncomeCr;

    /**
     * 履约超时率
     */
    private Double performanceOvertimeRate;

    /**
     * 履约超时率-环比率
     */
    private Double performanceOvertimeRateCr;
}