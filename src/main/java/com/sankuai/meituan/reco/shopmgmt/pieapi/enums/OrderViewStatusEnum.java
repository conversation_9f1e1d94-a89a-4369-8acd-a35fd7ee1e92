package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import org.codehaus.jackson.annotate.JsonValue;

/**
 * @Description 订单展示状态
 * @ClassName OrderViewStatus
 * <AUTHOR>
 * @Version 1.0
 * @Date 2022/1/26 3:02 下午
 */
public enum OrderViewStatusEnum {
    /**
     * 待商家接单
     */
    WAIT_TO_MERCHANT_ACCEPT(1, "待商家接单"),

    /**
     * 待拣货
     */
    WAIT_TO_PICK_ORDER(2, "待拣货"),

    /**
     * 待发单
     */
    WAIT_TO_SEND_DELIVERY(3, "待发单"),

    /**
     * 待接单
     */
    WAIT_TO_RIDER_ACCEPT(4, "待接单"),

    /**
     * 待到店
     */
    WAIT_TO_ARRIVE_SHOP(5, "待到店"),

    /**
     * 待取货
     */
    WAIT_TO_TAKE_GOODS(6, "待取货"),

    /**
     * 配送中
     */
    DELIVERING(7, "配送中"),

    /**
     * 未接单
     */
    NO_RIDER_ACCEPT(8, "未接单"),

    /**
     * 未到店
     */
    NO_ARRIVAL_STORE(9, "未到店"),

    /**
     * 未取货
     */
    NO_RIDER_TAKE_GOODS(10, "未取货"),

    /**
     * 配送超时
     */
    DELIVERY_TIMEOUT(11, "配送超时"),

    /**
     * 顾客申请全部退款
     */
    ALL_REFUND(12, "顾客申请全部退款"),

    /**
     * 顾客申请部分退款
     */
    PART_REFUND(13, "顾客申请部分退款"),

    /**
     * 系统异常
     */
    SYSTEM_EXCEPTION(14, "系统异常"),

    /**
    * 待自提
     */
    WAIT_TO_SELF_FETCH(15, "待自提"),

    /**
     * 取货失败
     */
    TAKE_EXCEPTION(16, "取货失败"),

    /**
     * 异常上报待处理
     */
    REPORT_EXCEPTION(17, "异常上报待处理"),

    ;

    private final int code;
    private final String desc;

    OrderViewStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
