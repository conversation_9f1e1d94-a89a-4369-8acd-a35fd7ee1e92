package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.google.common.collect.Sets;
import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QuerySkuReviewListRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ReviewStatusEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.review.PageQueryReviewProductRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * @description: 获取商品提报审核列表请求
 * @author: WangSukuan
 * @create: 2020-03-10
 **/
@TypeDoc(
        description = "获取商品提报审核列表请求"
)
@Data
@ApiModel("获取商品提报审核列表请求")
public class SkuReviewListQueryRequest {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "门店id列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id列表", required = true)
    @NotNull
    private Set<Long> storeIdSet;

    @FieldDoc(
                    description = "审核状态（1.提报人：提报记录列表，传入\"待审核\"\n" +
                                    "\n" +
                                    "2.审核人：审核记录列表，待审核，传入\"待审核\"\n" +
                                    "\n" +
                                    "3.审核人：审核记录列表，已审核，传入\"通过、驳回\"\n" +
                                    "\n" +
                                    "4.提报记录搜索：传入要搜索的类型）",
                    requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(
                    name = "审核状态（1.提报人：提报记录列表，传入\"待审核\"\n" +
                                    "\n" +
                                    "2.审核人：审核记录列表，待审核，传入\"待审核\"\n" +
                                    "\n" +
                                    "3.审核人：审核记录列表，已审核，传入\"通过、驳回\"\n" +
                                    "\n" +
                                    "4.提报记录搜索：传入要搜索的类型）",
                    required = true)
    @NotNull
    private List<Integer> reviewStatusList;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品名称")
    private String skuName;

    @FieldDoc(
            description = "申请人手机号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "申请人手机号")
    private String applicantPhone;

    @FieldDoc(
            description = "申请人姓名", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "申请人姓名")
    private String applicantName;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "页码")
    private Integer pageNum = 1;

    @FieldDoc(
            description = "每页大小", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "每页大小")
    private Integer pageSize = 10;

    @FieldDoc(
            description = "审核时间倒序", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "审核时间倒序")
    private Boolean orderByReviewTimeDesc;

    @FieldDoc(
            description = "待审核人", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "待审核人")
    private String pendingReviewUser;

    public QuerySkuReviewListRequest convertQuerySkuReviewListRequest(User user){

        QuerySkuReviewListRequest querySkuReviewListRequest = new QuerySkuReviewListRequest();

        querySkuReviewListRequest.setTenantId(user.getTenantId());
        querySkuReviewListRequest.setStoreIdSet(this.getStoreIdSet() == null ? Sets.newHashSet() : this.getStoreIdSet());
        querySkuReviewListRequest.setReviewStatusList(getReviewStatusEnumList());
        if (StringUtils.isNotEmpty(this.getSkuName())){
            querySkuReviewListRequest.setSkuName(this.getSkuName().trim());
        }
        querySkuReviewListRequest.setApplicantName(this.getApplicantName());
        querySkuReviewListRequest.setApplicantPhone(this.getApplicantPhone());
        querySkuReviewListRequest.setOperateId(user.getAccountId());
        querySkuReviewListRequest.setOperateName(user.getOperatorName());
        querySkuReviewListRequest.setPageNum(this.getPageNum());
        querySkuReviewListRequest.setPageSize(this.getPageSize());

        return querySkuReviewListRequest;

    }

    public PageQueryReviewProductRequest convertToQueryReviewProductRequest(User user){

        PageQueryReviewProductRequest request = new PageQueryReviewProductRequest();

        request.setPage(this.getPageNum());
        request.setPageSize(this.getPageSize());

        request.setTenantId(user.getTenantId());
        request.setStoreIds(this.getStoreIdSet() == null ? Collections.emptyList() : new ArrayList<>(this.getStoreIdSet()));
        request.setReviewStatusList(Fun.map(this.getReviewStatusEnumList(), ReviewStatusEnum::getValue));
        if (StringUtils.isNotEmpty(this.getSkuName())){
            request.setName(this.getSkuName().trim());
        }
        request.setReporterName(this.getApplicantName());
        request.setReporterPhone(this.getApplicantPhone());
        request.setOperateId(user.getAccountId());
        request.setOperateName(user.getOperatorName());

        request.setNeedReportTotalCount(false);
        request.setNeedCreateAndOnlineStatus(false);
        request.setReporterPerspective(false);

        request.setOrderByReviewTimeDesc(this.getOrderByReviewTimeDesc());
        request.setPendingReviewUser(this.getPendingReviewUser());
        return request;

    }

    private List<ReviewStatusEnum> getReviewStatusEnumList(){

        List<ReviewStatusEnum> reviewStatusEnumList = new ArrayList<>();
        if (CollectionUtils.isEmpty(this.getReviewStatusList())){
            return reviewStatusEnumList;
        }
        for (Integer integer : this.getReviewStatusList()){
            ReviewStatusEnum reviewStatusEnum = ReviewStatusEnum.findByValue(integer);
            reviewStatusEnumList.add(reviewStatusEnum);
        }
        return reviewStatusEnumList;
    }


}
