package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.price.enums.strategy.SyncStrategyTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;

/**
 * @Author: wangyihao04
 * @Date: 2020-11-12 17:44
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "渠道策略信息"
)
@Data
@ApiModel("渠道策略信息")
public class ChannelSyncStrategyVO {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID")
    @NotNull
    private int channelId;

    @FieldDoc(
            description = "定价策略：1-手动定价，2-通用策略，4-单品提价策略", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "定价策略")
    @NotNull
    private int syncStrategyType;

    @FieldDoc(
            description = "固定金额(单位元)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "固定金额(单位元)")
    private String fixedPrice;

    public void selfCheck() {
        Preconditions.checkArgument(SyncStrategyTypeEnum.FIX_PRICE.getValue() == this.syncStrategyType ||
                SyncStrategyTypeEnum.RAISE_STORE_PRICE.getValue() == this.syncStrategyType ||
                SyncStrategyTypeEnum.SINGLE_SKU_FIX_STRATEGY_PRICE.getValue() == this.syncStrategyType, "策略ID非法");


        if (this.syncStrategyType == SyncStrategyTypeEnum.FIX_PRICE.getValue() && StringUtils.isBlank(fixedPrice)) {
            throw new IllegalArgumentException("固定金额非法");
        }
    }
}