package com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login;

public abstract class ApiMethodParamThreadLocal {
    private ApiMethodParamThreadLocal() {
    }

    private static ThreadLocal<IdentityInfo> methodParamThreadLocal = ThreadLocal
            .withInitial(IdentityInfo::new);

    public static ThreadLocal<IdentityInfo> getInstance() {
        return methodParamThreadLocal;
    }

    public static IdentityInfo getIdentityInfo() {
        return getInstance().get();
    }

    @SuppressWarnings("unused")
    public static IdentityInfo getIdentityInfo(long storeId) {
        return getInstance().get();
    }

    public static void setUserInfo(User user) {
        getInstance().get().setUser(user);
    }

    public static void setUuid(String uuid) {
        getInstance().get().setUuid(uuid);
    }

    public static void setToken(String token) {
        getInstance().get().setToken(token);
    }

    public static void setOs(String os) {
        getInstance().get().setOs(os);
    }

    public static void setJsVersion(String version) {
        getInstance().get().setJsVersion(version);
    }

    public static void setAppVersion(String appVersion) {
        getInstance().get().setAppVersion(appVersion);
    }

    public static void setMrnApp(String mrnApp) {
        getInstance().get().setMrnApp(mrnApp);
    }

    public static void setMrnVersion(String mrnVersion) {
        getInstance().get().setMrnVersion(mrnVersion);
    }

    public static void setAppId(String appId) {
        getInstance().get().setAppId(appId);
    }

    public static void setStoreIds(String storeIds) {
        getInstance().get().setStoreIds(storeIds);
    }

    public static void setLazyAppFullStoreIds(LazyAppFullStoreIds lazyAppFullStoreIds) {
        getInstance().get().setLazyAppFullStoreIds(lazyAppFullStoreIds);
    }

    public static void setAppChannel(String appChannel) {
        getInstance().get().setAppChannel(appChannel);
    }

    public static void setAuthId(Integer authId) {
        getInstance().get().setAuthId(authId);
    }

}
