package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SaveReasonTemplateRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OperateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 保存审核拒绝原因模板
 * @author: WangSukuan
 * @create: 2020-03-11
 **/
@TypeDoc(
        description = "保存审核拒绝原因模板"
)
@Data
@ApiModel("保存审核拒绝/通过原因模板")
public class ReasonTemplateSaveRequest {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "拒绝/通过原因", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "拒绝/通过原因", required = true)
    @NotNull
    private String reason;

    @FieldDoc(
            description = "原因模版类型：1-驳回，2-通过", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "原因模版类型")
    private Integer reasonType;

    @FieldDoc(
            description = "模板原因id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "模板原因id")
    private Long reasonId;

    @FieldDoc(
            description = "操作类型(1:新增;2-修改)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "操作类型(1:新增;2-修改)", required = true)
    @NotNull
    private Integer operateType;



    public SaveReasonTemplateRequest convertSaveReasonTemplateRequest(User user){

        SaveReasonTemplateRequest saveReasonTemplateRequest = new SaveReasonTemplateRequest();
        saveReasonTemplateRequest.setTenantId(user.getTenantId());
        saveReasonTemplateRequest.setOperateId(user.getAccountId());
        saveReasonTemplateRequest.setOperateName(user.getOperatorName());
        saveReasonTemplateRequest.setReason(this.getReason().trim());
        // 新增时设置默认值，修改时不处理
        if (this.operateType == 1) {
            saveReasonTemplateRequest.setReasonType(this.getReasonType() != null ? this.getReasonType() : 1);
        }
        if (this.getReasonId() != null) {
            saveReasonTemplateRequest.setReasonId(this.getReasonId());
        }
        saveReasonTemplateRequest.setOperateType(OperateTypeEnum.findByValue(operateType));
        return saveReasonTemplateRequest;

    }


}
