package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.MerchantSpuIdListCommand;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Collections;

/**
 * <AUTHOR>
 * @Date 2024/3/7
 * @Description
 */
@TypeDoc(
        name = "查询商品主档异常详情请求",
        description = "查询商品主档异常详情请求）"
)
@Getter
@Setter
@ToString
public class QueryTenantSpuAbnormalRequest {

    @FieldDoc(
            description = "商品spuId"
    )
    @ApiModelProperty(value = "商品spuId")
    @NotNull
    private String spuId;

    public MerchantSpuIdListCommand toMerchantSpuIdListCommand(QueryTenantSpuAbnormalRequest request, long tenantId) {
        MerchantSpuIdListCommand command = new MerchantSpuIdListCommand();
        command.setMerchantId(tenantId);
        command.setSpuIds(Collections.singletonList(request.getSpuId()));
        return command;
    }
}