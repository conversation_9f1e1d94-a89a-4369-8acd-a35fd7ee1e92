package com.sankuai.meituan.reco.shopmgmt.pieapi.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPoolConfig {
    @Bean("queryStatusCountAsyncThreadPool")
    public ExecutorService queryStatusCountAsyncThreadPool(){
        ExecutorService executorService = TraceExecutors.getTraceExecutorService(new ThreadPoolExecutor(10,10,60L,
                TimeUnit.SECONDS,new LinkedBlockingQueue<>(100),
                new ThreadFactoryBuilder().setNameFormat("queryStatusCountAsyncThreadPool-%d").build()
        ));
        return executorService;
    }

    @Bean("createSupplyRelationAfterSpuCreateThreadPool")
    public ExecutorService createSupplyRelationAfterSpuCreate() {
        return TraceExecutors.getTraceExecutorService(new ThreadPoolExecutor(5, 15, 5,
                TimeUnit.MINUTES, new SynchronousQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("createSupplyRelationAfterSpuCreate-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        ));
    }

    @Bean("storeSpuQueryPurchaseThreadPool")
    public ExecutorService storeSpuQueryPurchaseThreadPool() {
        return new ExecutorServiceTraceWrapper(
                new ThreadPoolExecutor(5, 20, 5,
                        TimeUnit.MINUTES, new SynchronousQueue<>(),
                        new ThreadFactoryBuilder().setNameFormat("store-spu-query-purchase-thread-pool-%d").build(),
                        new ThreadPoolExecutor.CallerRunsPolicy()));
    }
}
