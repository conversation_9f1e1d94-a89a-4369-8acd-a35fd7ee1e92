/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import org.assertj.core.util.Sets;

import java.util.Set;

import static com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthModuleEnum.Type.OCMS;
import static com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthModuleEnum.Type.PICKTASK;
import static com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthModuleEnum.Type.STORE;
import static com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthModuleEnum.Type.KPH;

/**
 * <br><br>
 * Author: linjianyu <br>
 * Date: 2019-03-27 Time: 15:49
 */
@Deprecated
public enum AuthModuleEnum {

    KPH_PURCHASE("10", "康品汇要货任务", KPH),
    KPH_DELIVERY("20", "康品汇收货任务", KPH),
    KPH_REFUND("30", "康品汇退款任务", KPH),
    KPH_BREAK("40", "康品汇报损任务", KPH),
    KPH_ADJUST("50", "康品汇调价任务", KPH),
    KPH_STOCK_CHECK("60", "康品汇盘点任务", KPH),
    KPH_GOODS("70", "康品汇商品查询任务", KPH),
    KPH_ORDER("80", "康品汇订单任务", KPH),
    KPH_HOMEPAGE("90", "康品汇首页数据", KPH),

    DELIVERY("140", "有单收货"),
    REFUND("150", "退货"),
    BREAK("160", "报损"),
    STOCK_CHECK("170", "在线盘点"),
    STOCK_TAKE("290","盘点任务"),

    PICKTASK_TODAY_FULFILLMENT("51432", "拣货任务", PICKTASK),
    PICKTASK_TODAY_FULFILLMENT_FOR_FOOD_MARKET("623455", "菜场拣货任务", PICKTASK),
    PICKTASK_REFUND("223123", "拣货退款", PICKTASK),
    PICKTASK_MERGE_TASK("123002", "拣货合流任务", PICKTASK),
    PICKTASK_LACK_STOCK("123008", "拣货缺货", PICKTASK),

    PRICE_CHECK("9b1f9064-93af-4518-880d-7e47ae1735c1", "报价审核", OCMS),;
    public final String auth;
    public final String desc;
    public final Type type;

    AuthModuleEnum(String auth, String desc) {
        this.auth = auth;
        this.desc = desc;
        this.type = STORE;
    }

    AuthModuleEnum(String auth, String desc, Type type) {
        this.auth = auth;
        this.desc = desc;
        this.type = type;
    }

    public static AuthModuleEnum authOf(String value) {
        for (AuthModuleEnum moduleEnum : AuthModuleEnum.values()) {
            if (moduleEnum.auth.equals(value)) {
                return moduleEnum;
            }
        }
        return null;
    }

    public static Set<AuthModuleEnum> typeOf(Type type) {
        Set<AuthModuleEnum> moduleSet = Sets.newHashSet();
        for (AuthModuleEnum module : values()) {
            if (module.type == type) {
                moduleSet.add(module);
            }
        }
        return moduleSet;
    }

    public enum Type {
        KPH("康品汇批量"),
        PICKTASK("拣货批量"),
        STORE("门店"),
        OCMS("中台"),
        ;

        public final String desc;

        Type(String desc) {
            this.desc = desc;
        }
    }
}
