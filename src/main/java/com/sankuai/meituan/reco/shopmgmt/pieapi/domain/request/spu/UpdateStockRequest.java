package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: wanghongzhen
 * @date: 2020-05-11 17:02
 */
@TypeDoc(
        description = "设置库存请求"
)
@Data
@ApiModel("设置库存请求")
public class UpdateStockRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "SPU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SPU编码", required = true)
    @NotNull
    private String spuId;

    @FieldDoc(
            description = "渠道商品SKU库存参数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道商品SKU库存参数", required = true)
    @NotNull
    private List<StoreSkuStockVO> skuStockList;

    @FieldDoc(
            description = "调整库存说明", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "调整库存说明")
    private String comment;

    @FieldDoc(
            description = "调整库存类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "调整库存类型")
    private Integer adjustType;

}
