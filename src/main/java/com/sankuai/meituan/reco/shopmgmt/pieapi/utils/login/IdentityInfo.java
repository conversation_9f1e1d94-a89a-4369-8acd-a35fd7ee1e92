package com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.reco.store.management.thrift.Power;
import com.sankuai.meituan.reco.store.management.thrift.StorePower;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.AppIdEnum;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by zengzijian on 2018/5/10.
 */
@Data
public class IdentityInfo {

    private User user;

    // 与前端交互的token
    private String token;

    private String uuid;

    private String appId;

    private String os;

    private String appVersion;//大版本号

    private String jsVersion;//小版本号

    private String mrnApp;      //  js checkUpdateV2 需要app参数

    private String mrnVersion;  //  js checkUpdateV2 需要version参数

    /**
     * 门店id(多门店以逗号分隔)
     */
    @Getter(value = AccessLevel.PRIVATE)
    private String storeIds;

    /**
     * APP 全部门店ID懒加载对象
     */
    @Getter(value = AccessLevel.PRIVATE)
    private LazyAppFullStoreIds lazyAppFullStoreIds;

    /**
     * app渠道号, 分企业版和appStore版
     */
    private String appChannel;

    /**
     * 权限侧的应用id
     * 有子应用取子应用，否则取父应用，即按 sutAuthId、authId 顺序取值
     */
    private Integer authId;

    /**
     * @see com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User#tenantId
     * @deprecated ApiMethodParamInterceptor没有赋值，用com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User#tenantId
     */
    @Deprecated
    private long tenantId;

    /**
     * @see com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User#operatorName
     * @deprecated ApiMethodParamInterceptor没有赋值，用com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User#operatorName
     */
    @Deprecated
    private String employeeName;

    /**
     * @see com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User#accountName
     * @deprecated ApiMethodParamInterceptor没有赋值，用com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User#accountName
     */
    @Deprecated
    private String username;

    // 业务交互token，当前是一个与康品会约定的固定值
    private String tenantToken;

    private List<AppStorePower> storePowerList;

    /**
     * @see com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User#employeeId
     * @deprecated ApiMethodParamInterceptor没有赋值，用com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User#employeeId
     */
    @Deprecated
    private long employeeId;

    /**
     * 当前应用是否为全部门店模式
     * 对于支持层级权限的租户，使用 authId 判定即可。此时拥有一个门店权限也能进入全部门店模式
     * 对于不支持层级权限的租户，使用请求 Header 中 storeId 等于 -1，或者包含的门店数大于 1 来进行判定。
     *
     * @return boolean
     */
    public boolean isFullStoreMode() {
        return Objects.equals(authId, AppIdEnum.APP_13.getAuthAppId())
                || isFullStoreId()
                || getStoreIdList().size() > 1;
    }

    /**
     * 全门店模式下获取门店id列表
     *
     * @return 门店id列表
     */
    public List<Long> getStoreIdList() {
        if (StringUtils.isBlank(storeIds)) {
            return Lists.newArrayList();
        }

        // 如果为 APP 客户端，且请求 Header 中 storeId 为 -1，则进行替换
        // 注：目前仅处理 APP 端全部门店逻辑，其他端推荐使用 isFullStoreMode 判定在全部门店下后，按需查询相关数据权限
        // https://km.sankuai.com/collabpage/1873728621
        boolean isApp = Optional.ofNullable(SessionContext.getCurrentSession())
                .map(SessionInfo::getAuthAppId)
                .map(authAppId -> Objects.equals(authAppId, AppIdEnum.APP_5.getAuthAppId()))
                .orElse(Boolean.FALSE);
        boolean isFullStoreId = isFullStoreId();
        if (isApp && isFullStoreId && Objects.nonNull(lazyAppFullStoreIds)) {
            return lazyAppFullStoreIds.fetchStoreIds();
        }

        return Arrays.stream(storeIds.split(","))
                .distinct()
                .filter(e -> !e.isEmpty())
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    /**
     * 单门店模式下，获取当前用户选择门店ID
     * 由于可能抛出异常，设置下 JsonIgnore，避免在 log 时抛出异常
     *
     * @return Long
     * @throws CommonLogicException 全部门店模式下，使用此方法会抛出异常：当前功能模式不支持全门店模式
     */
    @JsonIgnore
    public Long getStoreId() {
        if (isFullStoreMode()) {
            // 不支持全门店模式
            throw new CommonLogicException(ResultCode.NOT_SUPPORT_FULL_STORE_MODE);
        }
        try {
            return Long.parseLong(storeIds);
        } catch (NumberFormatException ex) {
            // 不支持全门店模式
            throw new CommonLogicException(ResultCode.NOT_SUPPORT_FULL_STORE_MODE);
        }
    }

    /**
     * 请求 Header 中 storeId 是否为 -1
     *
     * @return boolean
     */
    private boolean isFullStoreId() {
        return ProjectConstants.FULL_STORE_ID.equals(storeIds);
    }

    @Getter
    public static class AppStorePower {

        private long storeId;

        private String storeName;

        private int type;

        private List<Integer> functionPowerList;

        private List<String> dataGroupList;

        AppStorePower(StorePower storePower) {
            this.storeId = storePower.getStoreId();
            this.storeName = storePower.getStoreName();
            this.type = storePower.getType();

            if (CollectionUtils.isNotEmpty(storePower.getPowerList())) {
                for (Power power : storePower.getPowerList()) {
                    if (power.getAppType() == ProjectConstants.APP_TYPE) {
                        this.functionPowerList = power.getFunctionPowerList();
                        this.dataGroupList = power.getDataGroupPowerList();
                        break;
                    }
                }
            }
        }

    }

}
