package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "创建库存调整请求",
        authors = "lijiajun20",
        version = "1.0"
)
@ApiModel("创建库存调整请求")
@Data
public class CreateStockAdjustRequestVo {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private Long entityId;

    @FieldDoc(
            description = "门店类型"
    )
    @ApiModelProperty(value = "门店类型", required = true)
    private Integer entityType;

    @FieldDoc(description = "评论")
    @ApiModelProperty(value = "评论", required = true)
    private String comment;

    @FieldDoc(description = "sku 列表")
    @ApiModelProperty(value = "sku 列表",required = true)
    private List<AdjustSkuRequestInfo> skuList;
}
