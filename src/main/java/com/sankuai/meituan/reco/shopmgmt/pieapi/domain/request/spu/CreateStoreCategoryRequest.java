package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.platform.empower.product.client.enums.StoreCategorySceneIdentityEnum;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category.CreateMerchantStoreCategoryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */

@Data
@ApiModel("创建门店店内分类参数")
public class CreateStoreCategoryRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "分类名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分类名称", required = true)
    private String name;

    @FieldDoc(
            description = "父分类Id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "父分类Id")
    private String parentCategoryId;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (StringUtils.isBlank(name)) {
            throw new CommonLogicException("分类名称不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (StringUtils.isBlank(parentCategoryId) || !StringUtils.isNumeric(parentCategoryId)) {
            throw new CommonLogicException("父分类Id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }

    public CreateMerchantStoreCategoryRequest toPoiRpcReq(User user) {
        CreateMerchantStoreCategoryRequest rpcReq = new CreateMerchantStoreCategoryRequest();
        rpcReq.setMerchantId(user.getTenantId());
        rpcReq.setNewStoreGroupId(this.storeId);
        rpcReq.setCategoryName(this.name);
        rpcReq.setParentCategoryId(Long.valueOf(this.parentCategoryId));
        rpcReq.setOperatorId(user.getAccountId());
        rpcReq.setOperatorName(user.getOperatorName());
        rpcReq.setOperatorAccount(user.getAccountName());
        rpcReq.setSceneIdentity(StoreCategorySceneIdentityEnum.POI.getCode());
        rpcReq.setPushChannel(true);
       return rpcReq;
    }
}