package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/23 10:55
 * @Description:
 */
@TypeDoc(
        name = "ChannelCategoryRequest",
        description = "渠道类目请求"
)
@Setter
@Getter
@EqualsAndHashCode
@ToString
public class ChannelCategoryRequest {

    @FieldDoc(
            description = "渠道编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道编码")
    @NotNull
    private int channelId;

    @FieldDoc(
            description = "父级渠道类目编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "父级渠道类目编码")
    @NotNull
    private String categoryCode;

    @FieldDoc(
            description = "渠道类目层级", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道类目层级")
    @NotNull
    private int level;

}
