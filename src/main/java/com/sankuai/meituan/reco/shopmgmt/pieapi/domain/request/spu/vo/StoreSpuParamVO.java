package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.dianping.pigeon.util.CollectionUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelStoreSpuParamDTO;
import lombok.Data;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Title: StoreSpuParamVO
 * @Description: 门店SPU参数信息
 * @Author: zhaolei12
 * @Date: 2020/4/21 9:48 下午
 */
@TypeDoc(
        description = "门店SPU参数信息"
)
@Data
public class StoreSpuParamVO {

    @FieldDoc(
            description = "门店ID"
    )
    private Long poiId;

    @FieldDoc(
            description = "门店名称"
    )
    private String poiName;

    @FieldDoc(
            description = "SPU编码"
    )
    private String spuId;

    @FieldDoc(
            description = "商品名称"
    )
    private String name;

    public static List<ChannelStoreSpuParamDTO> to(List<StoreSpuParamVO> storeSpuList, List<ChannelParamVO> channelList) {
        if (CollectionUtils.isEmpty(storeSpuList)) {
            return Lists.newArrayList();
        }

        if (CollectionUtils.isEmpty(channelList)) {
            return StoreSpuParamVO.to(storeSpuList);
        }

        return channelList.stream().map(channel -> StoreSpuParamVO.to(storeSpuList, channel.getChannelId())).flatMap(List::stream).collect(Collectors.toList());
    }

    public static List<ChannelStoreSpuParamDTO> to(List<StoreSpuParamVO> storeSpuList, Integer channelId) {
        if (CollectionUtils.isEmpty(storeSpuList)) {
            return Lists.newArrayList();
        }

        return storeSpuList.stream().map(spu -> StoreSpuParamVO.to(spu, channelId)).collect(Collectors.toList());
    }

    public static List<ChannelStoreSpuParamDTO> to(List<StoreSpuParamVO> storeSpuList) {
        if (CollectionUtils.isEmpty(storeSpuList)) {
            return Lists.newArrayList();
        }

        return storeSpuList.stream().map(StoreSpuParamVO::to).collect(Collectors.toList());
    }

    public static ChannelStoreSpuParamDTO to(StoreSpuParamVO storeSpu, Integer channelId) {
        ChannelStoreSpuParamDTO channelStoreSpuParamDTO = StoreSpuParamVO.to(storeSpu);
        if (channelStoreSpuParamDTO != null) {
            channelStoreSpuParamDTO.setChannelId(channelId);
        }
        return channelStoreSpuParamDTO;
    }

    public static ChannelStoreSpuParamDTO to(StoreSpuParamVO storeSpu) {
        if (storeSpu == null) {
            return null;
        }

        ChannelStoreSpuParamDTO channelStoreSpuParamDTO = new ChannelStoreSpuParamDTO();
        channelStoreSpuParamDTO.setSpuId(storeSpu.getSpuId());
        channelStoreSpuParamDTO.setStoreId(storeSpu.getPoiId());

        return channelStoreSpuParamDTO;
    }
}
