package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.sku.CombineChildSkuDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/3/10
 */
@TypeDoc(
        description = "组合品的子商品模型"
)
@Data
public class CombineChildSkuVo {

    @FieldDoc(
            description = "子商品skuId"
    )
    @ApiModelProperty(value = "子商品skuId")
    private String skuId;

    @FieldDoc(
            description = "子商品spuId"
    )
    @ApiModelProperty(value = "子商品spuId")
    private String spuId;

    @FieldDoc(
            description = "子商品名称"
    )
    @ApiModelProperty(value = "子商品名称")
    private String spuName;

    @FieldDoc(
            description = "子商品规格描述"
    )
    @ApiModelProperty(value = "子商品规格描述")
    private String skuSpec;

    @FieldDoc(
            description = "子商品数量"
    )
    @ApiModelProperty(value = "子商品数量")
    private Double amount;

    @FieldDoc(
            description = "重量"
    )
    @ApiModelProperty(value = "重量")
    private String weightForUnit;

    @FieldDoc(
            description = "重量单位"
    )
    @ApiModelProperty(value = "重量单位")
    private String weightUnit;

    public static CombineChildSkuDto convertDto(CombineChildSkuVo vo) {
        com.sankuai.meituan.shangou.empower.ocms.client.product.dto.sku.CombineChildSkuDto dto = new CombineChildSkuDto();
        dto.setSkuId(vo.getSkuId());
        dto.setSpuId(vo.getSpuId());
        dto.setSpuName(vo.getSpuName());
        dto.setSkuSpec(vo.getSkuSpec());
        dto.setAmount(vo.getAmount());
        dto.setWeightForUnit(vo.getWeightForUnit());
        dto.setWeightUnit(vo.getWeightUnit());
        return dto;
    }

}
