package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelFrontCategorySortRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreCategorySortDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.MerchantStoreCategorySortRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.enums.StoreCategorySceneIdentityEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */

@Data
@ApiModel("门店店内分类排序请求参数")
public class StoreCategorySortRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "分类ID序列集", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分类ID序列集", required = true)
    private List<String> categoryIds;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (CollectionUtils.isEmpty(categoryIds)) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }

    public MerchantStoreCategorySortRequest toPoiRpcReq(User user) {
        MerchantStoreCategorySortRequest request = new MerchantStoreCategorySortRequest();
        request.setTenantId(user.getTenantId());
        request.setCategoryIds(ConverterUtils.convertList(categoryIds, Long::parseLong));
        request.setOperatorId(user.getAccountId());
        request.setOperatorName(user.getOperatorName());
        request.setNewStoreGroupId(storeId);
        request.setOperatorAccount(user.getAccountName());
        request.setSceneIdentity(StoreCategorySceneIdentityEnum.POI.getCode());
        return request;
    }

}
