package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.DeleteTenantSpuRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Title: TenantSpuDeleteApiRequest
 * @Description: 删除租户商品请求参数
 * @Author: wuyongjiang
 * @Date: 2022/8/30 18:55
 */
@TypeDoc(
        description = "删除租户商品请求参数"
)
@Data
@ApiModel("删除租户商品请求参数")
public class TenantSpuDeleteApiRequest {
    @FieldDoc(
            description = "spu", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "spu",required = true)
    @NotNull
    private String spu;

    public static DeleteTenantSpuRequest toRpcRequest(TenantSpuDeleteApiRequest request, User user){
        DeleteTenantSpuRequest deleteTenantSpuRequest = new DeleteTenantSpuRequest();
        deleteTenantSpuRequest.setTenantId(user.getTenantId());
        deleteTenantSpuRequest.setSpuId(request.getSpu());
        deleteTenantSpuRequest.setOperatorId(user.getAccountId());
        deleteTenantSpuRequest.setOperatorName(user.getOperatorName());
        deleteTenantSpuRequest.setOperateSource(3);

        return deleteTenantSpuRequest;
    }
}
