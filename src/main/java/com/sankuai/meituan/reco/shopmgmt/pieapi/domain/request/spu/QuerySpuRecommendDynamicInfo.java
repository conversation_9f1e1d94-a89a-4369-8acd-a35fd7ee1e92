package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Objects;


@TypeDoc(
        description = "获取商品渠道推荐动态信息"
)
@Data
@ApiModel("获取商品渠道推荐动态信息")
@ToString
public class QuerySpuRecommendDynamicInfo {

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称")
    private String spuName;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "渠道类目id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道类目id")
    private String channelCategoryId;

    public void selfCheck(){
        Preconditions.checkArgument(StringUtils.isNotBlank(spuName), "商品名称不能为空");
        Preconditions.checkArgument(Objects.nonNull(channelId), "渠道id不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(channelCategoryId), "渠道类目id不能为空");
    }
}
