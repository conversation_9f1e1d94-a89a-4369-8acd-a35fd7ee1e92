package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ExtendedAttributesDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.StoreSpuDetailRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;
import java.util.Optional;

/**
 * @Title: StoreSpuDetailApiRequest
 * @Description:
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:02 下午
 */
@Data
@Slf4j
public class StoreSpuDetailApiRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "SPU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SPU编码")
    @NotNull
    private String spuId;

    @FieldDoc(
            description = "true必须返回渠道类目信息(上线则以推送渠道类目为准，未上线以总部商品池类目为准); 除了门店商品编辑页面，其余场景默认false", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "true必须返回渠道类目信息(上线则以推送渠道类目为准，未上线以总部商品池类目为准); 除了门店商品编辑页面，其余场景默认false")
    private Boolean channelCategoryExtend;

    @FieldDoc(
            description = "是否查询商品促销信息,默认查询"
    )
    @ApiModelProperty(name = "是否查询商品促销信息,默认查询")
    private Boolean querySkuPromotion = true;

    public static StoreSpuDetailRequest toRpcRequest(StoreSpuDetailApiRequest request, User user) {
        StoreSpuDetailRequest rpcRequest = new StoreSpuDetailRequest();
        rpcRequest.setTenantId(user.getTenantId());
        rpcRequest.setStoreId(request.getStoreId());
        rpcRequest.setSpuId(request.getSpuId());
        ExtendedAttributesDTO extendedAttributesDTO = new ExtendedAttributesDTO();
        extendedAttributesDTO.setSpuSale(true);
        // true：必须返回渠道类目信息（上线则以推送渠道类目为准，未上线以总部商品池类目为准）; 默认false（不需要商品渠道类目信息）
        extendedAttributesDTO.setChannelCategoryExtend(Optional.ofNullable(request.getChannelCategoryExtend()).orElse(false));
        rpcRequest.setExtendedAttributes(extendedAttributesDTO);
        return rpcRequest;
    }

}
