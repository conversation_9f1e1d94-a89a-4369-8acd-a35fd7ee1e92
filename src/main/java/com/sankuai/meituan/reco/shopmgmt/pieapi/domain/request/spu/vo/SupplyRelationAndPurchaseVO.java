package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/6 8:22 PM
 */
@Getter
@Setter
public class SupplyRelationAndPurchaseVO {
    /***
     * skuId，可绑定多个供应商
     */
    private String skuId;

    /***
     * 规格名称：用于手动创建sku时，skuId为null的情况进行查询匹配
     */
    private String spec;

    /***
     * 采购状态：1正常、2停止
     */
    private Integer purchaseStatus;

    /***
     * 采购类型：1统采、2地采
     */
    private Integer purchaseType;

    /***
     * 供应商绑定
     */
    private List<SkuSupplyRelationVO> supplyRelations;

    @Getter
    @Setter
    public static class SkuSupplyRelationVO {

        private Long supplierId;

        private String supplyUnit;

        private Double supplyUnitRatio;

        private Double supplyUnitPrice;

        private Integer minOrderQuantity;

        private Integer masterFlag;
        /**
         * 箱规编码
         */
        private String supplyUnitCode;

        private Integer orderMultiple;
    }

}
