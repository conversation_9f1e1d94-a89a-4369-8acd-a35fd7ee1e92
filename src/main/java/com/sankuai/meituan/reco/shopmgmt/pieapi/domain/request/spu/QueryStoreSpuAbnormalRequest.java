package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 查询门店商品异常详情请求
 *
 * <AUTHOR>
 * @since 2023/5/23
 */
@TypeDoc(
        name = "查询门店商品异常详情请求",
        description = "查询门店商品异常详情请求"
)
@Getter
@Setter
@ToString
public class QueryStoreSpuAbnormalRequest {

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id")
    private Long storeId;
    @FieldDoc(
            description = "商品spuId"
    )
    @ApiModelProperty(value = "商品spuId")
    private String spuId;

    @FieldDoc(
            description = "渠道id，不传查所有渠道"
    )
    @ApiModelProperty(value = "渠道id，不传查所有渠道")
    private Integer channelId;

    @FieldDoc(
            description = "是否查询商品质量信息渠道实时文案, true则查询渠道实时文案"
    )
    @ApiModelProperty(value = "是否查询商品质量信息渠道实时文案, true则查询渠道实时文案")
    private Boolean originQualityProblem;

    @FieldDoc(
            description = "是否需要对比一致信息"
    )
    @ApiModelProperty(value = "是否需要对比一致信息")
    private Boolean needDiffInfo;

}
