package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QuerySkuReviewApplyListRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ReviewStatusEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.review.PageQueryReviewProductRequest;
import com.sankuai.meituan.shangou.saas.common.enums.EnumUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.EnumerationUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.enums.EnumUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @description: 查询商品审核提报信息列表请求
 * @author: WangSukuan
 * @create: 2020-03-17
 **/
@TypeDoc(
        description = "保存商品提报信息请求"
)
@Data
@ApiModel("保存商品提报信息请求")
public class SkuReviewApplyListQueryRequest {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品名称")
    private String skuName;

    @FieldDoc(
            description = "要搜索的类型（1-待审核；5-无效；6-撤回；10-审核通过；11-审核驳回）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "要搜索的类型（1-待审核；5-无效；6-撤回；10-审核通过；11-审核驳回）")
    private List<Integer> reviewStatusList;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "页码")
    private Integer pageNum = 1;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "每页行数")
    private Integer pageSize = 10;



    public QuerySkuReviewApplyListRequest convertQuerySkuReviewApplyListRequest(User user){

        QuerySkuReviewApplyListRequest request = new QuerySkuReviewApplyListRequest();
        request.setTenantId(this.getTenantId());
        request.setStoreId(this.getStoreId());
        if (StringUtils.isNotEmpty(this.getSkuName())){
            request.setSkuName(this.getSkuName().trim());
        }
        request.setPageNum(this.getPageNum());
        request.setPageSize(this.getPageSize());
        request.setReviewStatusList(getReviewStatusEnumList());
        request.setOperateId(user.getAccountId());
        request.setOperateName(user.getOperatorName());
        return request;

    }

    public PageQueryReviewProductRequest convertToQueryReviewProductRequest(User user){
        PageQueryReviewProductRequest request = new PageQueryReviewProductRequest();

        request.setPage(this.getPageNum());
        request.setPageSize(this.getPageSize());

        request.setTenantId(this.getTenantId());
        request.setStoreIds(Objects.nonNull(this.getStoreId()) ? Collections.singletonList(this.getStoreId()) : Collections.emptyList());
        if (StringUtils.isNotBlank(this.getSkuName())){
            request.setName(this.getSkuName().trim());
        }
        request.setReviewStatusList(Fun.map(this.getReviewStatusEnumList(), ReviewStatusEnum::getValue));
        request.setOperateId(user.getAccountId());
        request.setOperateName(user.getOperatorName());

        request.setNeedReportTotalCount(false);
        request.setNeedCreateAndOnlineStatus(true);
        request.setReporterPerspective(true);

        return request;
    }

    private List<ReviewStatusEnum> getReviewStatusEnumList(){

        List<ReviewStatusEnum> reviewStatusEnums = new ArrayList<>();
        if (CollectionUtils.isEmpty(this.getReviewStatusList())){
            return new ArrayList<>();
        }
        for (Integer integer : this.getReviewStatusList()){
            ReviewStatusEnum reviewStatusEnum = ReviewStatusEnum.findByValue(integer);
            if (null != reviewStatusEnum){
                reviewStatusEnums.add(reviewStatusEnum);
            }
        }
        return reviewStatusEnums;
    }

}
