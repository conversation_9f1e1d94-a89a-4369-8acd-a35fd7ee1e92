package com.sankuai.meituan.reco.shopmgmt.pieapi.config;

import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.stereotypes.publiccloud.PublicCloudConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

/**
 * 通过此类来配置WebMvc的配置
 * Author: yangshaosong
 * Date: 2018-01-02
 * Time: 下午2:17
 */
@PublicCloudConfig
public class PublicCloudWebMvcConfigurer extends WebMvcConfigurerAdapter {
    @Value("${server.port}")
    private Integer port;

    @Value("${app.name}")
    private String appKey;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 多个拦截器组成一个拦截器链
        // addPathPatterns 用于添加拦截规则
        // excludePathPatterns 用户排除拦截
        /* https://km.sankuai.com/collabpage/28247159
        // 如果曾经配置过老版的TraceHandlerInterceptor，请替换成TraceFilter+TraceMethodInterceptor组合。老版TraceHandlerInterceptor有缺陷，无法覆盖其他ServletFilter中的对外调用。
        TraceHandlerInterceptor traceHandlerInterceptor = new TraceHandlerInterceptor();
        traceHandlerInterceptor.setAppkey(appKey);
        traceHandlerInterceptor.setPort(port);
        registry.addInterceptor(traceHandlerInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/api/monitor/alive")
                .excludePathPatterns("/static/**")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/v2/api-docs/**")
                .excludePathPatterns("/META-INF/**")
                .excludePathPatterns("/configuration/**")
                .excludePathPatterns("/hystrix/**")
                .excludePathPatterns("swagger-ui.html");
         */
    }

}
