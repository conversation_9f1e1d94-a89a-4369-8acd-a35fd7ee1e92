package com.sankuai.meituan.reco.shopmgmt.pieapi.utils;

import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018/10/17
 */
public class PageUtils {
    public static final int DEFAULT_PAGE_SIZE = 20;

    public static int getDefaultPageSize() {
        return DEFAULT_PAGE_SIZE;
    }

    public static boolean checkHasMore(int totalCount, int pageSize, int fetchSize, int pageNo) {

        if (fetchSize < pageSize) {
            return false;
        } else {
            return totalCount > pageNo * pageSize;
        }

    }

    public static boolean checkHasMore(List<?> list, int pageSize) {
        return !CollectionUtils.isEmpty(list) && list.size() >= pageSize;
    }

    public static boolean checkHasMore(int totalCount, int pageSize, int pageNo) {
        return totalCount > pageNo * pageSize;
    }

}
