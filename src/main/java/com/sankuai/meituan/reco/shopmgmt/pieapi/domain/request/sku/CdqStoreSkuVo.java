package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuTagSimpleVO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.dto.sku.CdqStoreSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuTagDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StoreSkuPropertyDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.TimeSlotDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 门店商品信息
 * @author: liyu44
 * @create: 2020-02-03
 **/

@TypeDoc(
        description = "门店商品信息"
)
@Data
@ApiModel("门店商品信息")
public class CdqStoreSkuVo {

    @FieldDoc(
            description = "租户id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户id")
    private Long tenantId;
    @FieldDoc(
            description = "城市编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "城市编码")
    @NotNull
    private Integer regionCode;
    @FieldDoc(
            description = "门店id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店id")
    private Long storeId;
    @FieldDoc(
            description = "总部商品编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "总部商品编码", required = true)
    @NotNull
    private String tenantSkuId;
    @FieldDoc(
            description = "门店商品编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店商品编码", required = true)
    @NotNull
    private String skuId;
    @FieldDoc(
            description = "UPC编码列表，最多6个", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "UPC编码列表，最多6个")
    private List<String> upcList;
    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    @NotNull
    private String skuName;
    @FieldDoc(
            description = "商品类目code", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品类目code")
    private String categoryCode;
    @FieldDoc(
            description = "商品类目名称路径（创建更新商品时无需传入，用于详情返回）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品类目名称路径（创建更新商品时无需传入，用于详情返回）")
    private String categoryCodePath;
    @FieldDoc(
            description = "商品类目名称（创建更新商品时无需传入，用于详情返回）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品类目名称（创建更新商品时无需传入，用于详情返回）")
    private String categoryName;
    @FieldDoc(
            description = "商品类目名称路径（创建更新商品时无需传入，用于详情返回）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品类目名称路径（创建更新商品时无需传入，用于详情返回）")
    private String categoryNamePath;
    @FieldDoc(
            description = "品牌code", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌code")
    private String brandCode;
    @FieldDoc(
            description = "品牌名称（创建更新商品时无需传入，用于详情返回）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌名称（创建更新商品时无需传入，用于详情返回）")
    private String brandName;
    @FieldDoc(
            description = "产地", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "产地")
    private String producingPlace;
    @FieldDoc(
            description = "规格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "规格")
    private String spec;
    @FieldDoc(
            description = "重量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "重量")
    private Integer weight;

    @FieldDoc(
            description = "称重类型 1-称重计量 2-称重计件 3-非称重"
    )
    @ApiModelProperty(name = "称重类型")
    private Integer weightType;
    @FieldDoc(
            description = "图片url列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "图片url列表", required = true)
    @NotNull
    private List<String> images;
    @FieldDoc(
            description = "门店商品价格（单位-元）,创建时必传", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品价格（单位-元）")
    @NotNull
    private Double storePrice;
    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售卖单位", required = true)
    @NotNull
    private String saleUnit;
    @FieldDoc(
            description = "库存", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "库存")
    private Integer stock;
    @FieldDoc(
            description = "摊位id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "摊位id")
    private Long boothId;
    @FieldDoc(
            description = "原摊位id（编辑时有摊位关联需要传入，创建商品和获取商品详情时此字段无用）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "原摊位id（编辑时有摊位关联需要传入，创建商品和获取商品详情时此字段无用）")
    private Long oldBoothId;
    @FieldDoc(
            description = "可售时间，如果为无限，此字段为空。\n" +
                    "\n" +
                    "key:工作日 见@Enum WeekDayEnum\n" +
                    "* value:时间段 时间段不允许有交集，个数不超过5个\n" +
                    "* {\"09:00-09:30\"},{\"13:30-15:00\"},{\"20:00-21:00\"}", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "可售时间")
    private Map<Integer,List<TimeSlotVO>> availableTimes;
    @FieldDoc(
            description = "是否为“力荐”商品，字段取值范围：0-否， 1-是", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否为“力荐”商品")
    private Integer specialty;
    @FieldDoc(
            description = "商品描述 200字以内", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品描述")
    private String description;
    @FieldDoc(
            description = "商品属性 不超过十个属性", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品属性")
    private List<StoreSkuPropertyVO> properties;
    @FieldDoc(
            description = "(详情返回)自定义库存标记:0-非自定义库存 1-自定义库存", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "自定义库存标记")
    private Integer customizeStockFlag;
    @FieldDoc(
            description = "(详情返回)自定义库存数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "自定义库存数量")
    private Integer customizeStockQuantity;
    @FieldDoc(
            description = "(详情返回)第二天是否自动恢复无限库存：0-不自动恢复 1-自动恢复", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "第二天是否自动恢复无限库存")
    private Integer autoResumeInfiniteStock;

    @FieldDoc(
            description = "摊位名称：来自租户服务", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "摊位名称：来自租户服务")
    private String boothName;

    @FieldDoc(
            description = "标签列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "标签列表")
    private List<SkuTagSimpleVO> skuTagSimpleVOList;

    public CdqStoreSkuVo() {
    }

    public CdqStoreSkuVo(CdqStoreSkuDTO cdqStoreSkuDTO){

        this.setTenantId(cdqStoreSkuDTO.getTenantId());
        this.setRegionCode(cdqStoreSkuDTO.getRegionCode());
        this.setStoreId(cdqStoreSkuDTO.getStoreId());
        this.setTenantSkuId(cdqStoreSkuDTO.getTenantSkuId());
        this.setSkuId(cdqStoreSkuDTO.getSkuId());
        this.setSkuName(cdqStoreSkuDTO.getSkuName());
        this.setImages(cdqStoreSkuDTO.getImages());
        this.setStorePrice(cdqStoreSkuDTO.getStorePrice());
        this.setSaleUnit(cdqStoreSkuDTO.getSaleUnit());

        this.setUpcList(cdqStoreSkuDTO.getUpcList());
        this.setCategoryCode(cdqStoreSkuDTO.getCategoryCode());
        this.setCategoryCodePath(cdqStoreSkuDTO.getCategoryCodePath());
        this.setCategoryName(cdqStoreSkuDTO.getCategoryName());
        this.setCategoryNamePath(cdqStoreSkuDTO.getCategoryNamePath());
        this.setBrandCode(cdqStoreSkuDTO.getBrandCode());
        this.setBrandName(cdqStoreSkuDTO.getBrandName());
        this.setProducingPlace(cdqStoreSkuDTO.getProducingPlace());
        this.setSpec(cdqStoreSkuDTO.getSpec());
        this.setWeight(cdqStoreSkuDTO.getWeight());
        this.setWeightType(cdqStoreSkuDTO.getWeightType());
        this.setStock(cdqStoreSkuDTO.getStock());
        this.setBoothId(cdqStoreSkuDTO.getBoothId());
        this.setOldBoothId(cdqStoreSkuDTO.getOldBoothId());
        this.setSpecialty(cdqStoreSkuDTO.getSpecialty());
        this.setDescription(cdqStoreSkuDTO.getDescription());
        this.setCustomizeStockFlag(cdqStoreSkuDTO.getCustomizeStockFlag());
        this.setCustomizeStockQuantity(cdqStoreSkuDTO.getCustomizeStockQuantity());
        this.setAutoResumeInfiniteStock(cdqStoreSkuDTO.getAutoResumeInfiniteStock());
        this.setBoothName(cdqStoreSkuDTO.getBoothName());

        Map<Integer,List<TimeSlotDTO>> availableTimeDtoMap = cdqStoreSkuDTO.getAvailableTimes();
        if (availableTimeDtoMap != null && availableTimeDtoMap.size() > 0){
            Map<Integer,List<TimeSlotVO>> availableTimeVoMap = new HashMap<>();
            for (Map.Entry<Integer, List<TimeSlotDTO>> entry : availableTimeDtoMap.entrySet()) {
                List<TimeSlotVO> timeSlotVOList = new ArrayList<>();
                if (CollectionUtils.isEmpty(entry.getValue())){
                    continue;
                }
                for (TimeSlotDTO timeSlotDTO : entry.getValue()) {
                    if (null == entry.getValue()){
                        continue;
                    }
                    timeSlotVOList.add(new TimeSlotVO(timeSlotDTO));
                }
                availableTimeVoMap.put(entry.getKey(), timeSlotVOList);
            }
            this.setAvailableTimes(availableTimeVoMap);
        }

        List<StoreSkuPropertyDTO> storeSkuPropertyDTOList = cdqStoreSkuDTO.getProperties();
        List<StoreSkuPropertyVO> storeSkuPropertyVOList =  new ArrayList<>();
        if (CollectionUtils.isNotEmpty(storeSkuPropertyDTOList)){
            for (StoreSkuPropertyDTO storeSkuPropertyDTO : storeSkuPropertyDTOList) {
                if (storeSkuPropertyDTO != null){
                    storeSkuPropertyVOList.add(new StoreSkuPropertyVO(storeSkuPropertyDTO));
                }
            }
        }
        this.setProperties(storeSkuPropertyVOList);

        //商品标签数据赋值
        List<SkuTagDTO> skuTagDTOList = cdqStoreSkuDTO.getTags();
        List<SkuTagSimpleVO> skuTagSimpleVOList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(skuTagDTOList)){
            for(SkuTagDTO skuTagDTO:skuTagDTOList){
                SkuTagSimpleVO skuTagSimpleVO = new SkuTagSimpleVO();
                skuTagSimpleVO.setTagId(skuTagDTO.getTagId());
                skuTagSimpleVO.setTagName(skuTagDTO.getTagName());
                skuTagSimpleVOList.add(skuTagSimpleVO);
            }
        }
        this.setSkuTagSimpleVOList(skuTagSimpleVOList);
    }

    public CdqStoreSkuDTO buildCdqStoreSkuDTO(){

        CdqStoreSkuDTO cdqStoreSkuDTO = new CdqStoreSkuDTO();

        cdqStoreSkuDTO.setRegionCode(this.getRegionCode() == null ? 0 : this.getRegionCode());

        cdqStoreSkuDTO.setTenantSkuId(this.getTenantSkuId());
        cdqStoreSkuDTO.setSkuId(this.getSkuId());
        cdqStoreSkuDTO.setSkuName(this.getSkuName());
        cdqStoreSkuDTO.setImages(this.getImages());
        cdqStoreSkuDTO.setSaleUnit(this.getSaleUnit());
        cdqStoreSkuDTO.setDescription(this.getDescription());

        cdqStoreSkuDTO.setUpcList(this.getUpcList());
        cdqStoreSkuDTO.setCategoryCode(this.getCategoryCode());
        cdqStoreSkuDTO.setCategoryCodePath(this.getCategoryCodePath());
        cdqStoreSkuDTO.setCategoryName(this.getCategoryName());
        cdqStoreSkuDTO.setCategoryNamePath(this.getCategoryNamePath());
        cdqStoreSkuDTO.setBrandCode(this.getBrandCode());
        cdqStoreSkuDTO.setBrandName(this.getBrandName());
        cdqStoreSkuDTO.setProducingPlace(this.getProducingPlace());
        cdqStoreSkuDTO.setSpec(this.getSpec());

        cdqStoreSkuDTO.setWeight(this.getWeight() == null ? 0 : this.getWeight());
        cdqStoreSkuDTO.setStock(this.getStock() == null ? 0 : this.getStock());

        Map<Integer, List<TimeSlotDTO>> availableTimesDtoMap = new HashMap<>();
        Map<Integer, List<TimeSlotVO>> availableTimesVoMap = this.getAvailableTimes();
        if (availableTimesVoMap != null && availableTimesVoMap.size() > 0){
            for (Map.Entry<Integer, List<TimeSlotVO>> entry : availableTimesVoMap.entrySet()){
                List<TimeSlotDTO> timeSlotDtoList = new ArrayList<>();
                for (TimeSlotVO timeSlotVO : entry.getValue()){
                    timeSlotDtoList.add(timeSlotVO.buildTimeSlotDTO());
                }
                availableTimesDtoMap.put(entry.getKey(), timeSlotDtoList);
            }
            cdqStoreSkuDTO.setAvailableTimes(availableTimesDtoMap);
        }
        List<StoreSkuPropertyDTO> storeSkuPropertyDtoList = new ArrayList<>();
        List<StoreSkuPropertyVO> storeSkuPropertyVOList = this.getProperties();
        if (CollectionUtils.isNotEmpty(storeSkuPropertyVOList)){
            for (StoreSkuPropertyVO storeSkuPropertyVO : this.getProperties()){
                if (storeSkuPropertyVO == null){
                    continue;
                }
                storeSkuPropertyDtoList.add(storeSkuPropertyVO.buildStoreSkuPropertyDTO());
            }
            cdqStoreSkuDTO.setProperties(storeSkuPropertyDtoList);
        }

        cdqStoreSkuDTO.setBoothId(this.getBoothId() == null ? 0 : this.getBoothId());
        cdqStoreSkuDTO.setOldBoothId(this.getOldBoothId() == null ? 0: this.getOldBoothId());
        cdqStoreSkuDTO.setSpecialty(this.getSpecialty() == null ? 0 : this.getSpecialty());
        cdqStoreSkuDTO.setCustomizeStockFlag(this.getCustomizeStockFlag() == null ? 0: this.getCustomizeStockFlag());
        cdqStoreSkuDTO.setCustomizeStockQuantity(this.getCustomizeStockQuantity() == null ? 0 : this.getCustomizeStockQuantity());
        cdqStoreSkuDTO.setAutoResumeInfiniteStock(this.getAutoResumeInfiniteStock() == null ? 0 : this.getCustomizeStockQuantity());
        cdqStoreSkuDTO.setBoothName(this.getBoothName());
        return cdqStoreSkuDTO;

    }
}
