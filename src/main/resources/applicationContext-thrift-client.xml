<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <bean id="clientOctoAppKey" class="com.sankuai.meituan.util.ConfigUtilAdapter"
          factory-method="getString" lazy-init="true">
        <constructor-arg index="0" value="octo.appkey"/>
        <constructor-arg index="1">
            <bean class="com.sankuai.meituan.util.ConfigUtilAdapter" factory-method="getString">
                <constructor-arg index="0" value="app.key"/>
            </bean>
        </constructor-arg>
    </bean>

    <!-- thrift客户端线程池配置 -->
    <bean id="mtThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('freego_bapi_thrift_client_pool_max-active', '100')}"/>
        <property name="maxIdle" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('freego_bapi_thrift_client_pool_max-idle', '20')}"/>
        <property name="minIdle" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('freego_bapi_thrift_client_pool_min-idle', '10')}"/>
        <property name="maxWait" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('freego_bapi_thrift_client_pool_max-wait', '1000')}"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <!-- tms渠道配置化thrift客户端线程池配置 -->
    <bean id="tmsThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="5000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="printThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="5000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="qnhOrderApiThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_thrift_client_pool_max-active', '100')}"/>
        <property name="maxIdle"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_thrift_client_pool_max-idle', '20')}"/>
        <property name="minIdle"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_thrift_client_pool_min-idle', '10')}"/>
        <property name="maxWait"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_thrift_client_pool_max-wait', '1000')}"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="fuseOrderListExportThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.order.management.client.export.service.FuseOrderListExportThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ordermng"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="deliveryChannelThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.DeliveryChannelThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="authenticateService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.sac.thrift.authenticate.AuthenticateService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="ocmsOrderSearchService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ordermng"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="openPickThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.thrift.OpenPickThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <!-- 订单系统线上订单业务接口 -->
    <bean id="bizOrderThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.o2o.service.BizOrderThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="orderTransferWarehousePlanService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.qnh.ofc.transfer.client.thrift.transferorder.service.OrderTransferWarehousePlanService"/> <!-- service接口名 -->
        <property name="timeout" value="1000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.ofc.transfer"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="ocmsOrderOperateThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.service.ocms.OCMSOrderOperateThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <!-- 订单系统OCMS业务接口 -->
    <bean id="ocmsOrderThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.service.ocms.OCMSOrderThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="orderFinanceThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.finance.service.OrderFinanceThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.empower.settlement"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="deliveryConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.delivery.thrift.service.DeliveryConfigThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getInt('freego.product.thrift.service.timeout',15000)}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="deliveryConfigurationThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tms_delivery_thrift_timeout','10000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="channelPoiManageThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="queryDeliveryInfoThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.QueryDeliveryInfoThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="settlementVoucherThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.settlement.services.SettlementVoucherThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.empower.settlement"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="orderSearchService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.order.management.client.service.OrderSearchService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ordermng"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="ocmsOrderSearchThriftServiceV2" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchThriftServiceV2"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ordermng"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="orderItemFuseSearchThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.saas.order.management.client.service.online.OrderItemFuseSearchThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ordermng"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="ocmsQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ordermng"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="pickSelectPrintQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.logic.thrift.print.PickSelectPrintQueryThriftService"/>
        <property name="timeout" value="1500"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectlogic"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="pickingThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.thrift.picking.PickingThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="queryPickingThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.query.thrift.picking.PickingThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.fulfill.pickquery"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="afterSaleApplySearchService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.order.management.client.service.aftersaleapply.AfterSaleApplySearchService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ordermng"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="exportFieldThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.saas.order.management.client.export.service.ExportFieldThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ordermng"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="deliveryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.delivery.thrift.service.DeliveryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getInt('freego.product.thrift.service.timeout',5000)}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="filterByServiceName" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="channelOrderTenantThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelOrderTenantThriftService"/>
        <property name="timeout" value="5600"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocms"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="channelThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocms"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
        <property name="remoteUniProto" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="channelDeliveryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelDeliveryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="channelSkuForAppThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelSkuForAppThriftService"/>
        <property name="timeout" value="5600"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocms"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
        <property name="remoteUniProto" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="stockSyncConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.thrift.service.StockSyncConfigThriftService"/>
        <property name="timeout" value="5600"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocms"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
        <property name="remoteUniProto" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="configThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.ConfigThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="filterByServiceName" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="storeLoginThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" lazy-init="false">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.store.management.thrift.LoginThriftService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.storeempower"/>
        <property name="filterByServiceName" value="true"/>
        <property name="timeout" value="3000"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="loginThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.auth.thrift.service.LoginThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_login_thrift_timeout','1500')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="boothThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.BoothThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="filterByServiceName" value="true"/>
        <property name="filters">
            <list>
                <ref bean="thriftInvokerCatEventFilter"/>
            </list>
        </property>
    </bean>

    <bean id="privacyPhoneThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.service.PrivacyPhoneThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="mtUserThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.service.MtUserThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="sacAccountSearchThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_timeout','1500')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="saleReturnOrderThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.store.management.thrift.scmdelivery.SaleReturnOrderThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saleReturnOrderThriftService_thrift_timeout','10000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.storeempower"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="deliveryOperationThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tms_delivery_thrift_timeout','10000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="channelCommentThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.sgfulfillment.comment.thrift.ChannelCommentThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.comment"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="commentContactThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.sgfulfillment.comment.thrift.CommentContactThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.comment"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <!-- 牵牛花订单 -->
    <bean id="qnhOrderThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.QnhOrderDockingThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('qnh_order_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="poiThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.PoiThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="riderPickingThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.thrift.picking.rider.RiderPickingThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="riderQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="employThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.EmployeeThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="channelOrderDockingThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderDockingThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_order_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="openPrintThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.thrift.print.OpenPrintThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <!--开始手动配置-->
    <bean id="authThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_timeout','1500')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="userThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.UserThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="skuStoreStockThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.thrift.service.SkuStoreStockThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ocms_channel_store_sku_service_time_out','2000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocms"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
        <property name="remoteUniProto" value="true"/>
    </bean>

    <bean id="poiRelationThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.PoiRelationThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="channelManageThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.ChannelManageThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="authAccountThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.auth.thrift.service.AccountThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_timeout','1500')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="businessConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.BusinessConfigThriftService"/>
        <property name="timeout" value="2000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="fulfillThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.thrift.fulfill.FulfillThriftService"/>
        <property name="timeout" value="1500"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="printThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.query.thrift.print.PrintThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.fulfill.pickquery"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="orderThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.query.thrift.order.OrderThriftService"/>
        <property name="timeout" value="1500"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.fulfill.pickquery"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="queryFulfillThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.query.thrift.fulfill.FulfillThriftService"/>
        <property name="timeout" value="1500"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.fulfill.pickquery"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="merchantRevenueQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.order.management.client.service.revenue.MerChantRevenueQueryService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getInt('order.mng.thrift.service.timeout',5000)}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ordermng"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="dataQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.sgdata.query.thrift.DataQueryThriftService"/>
        <property name="timeout" value="20000"/>
        <property name="remoteAppkey" value="com.sankuai.sgdata.queryapi"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="selfDeliveryPoiConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.dmp"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="empowerMerchantSpuThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerMerchantSpuThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getInt('shangou.empower.product.thrift.service.timeout',5000)}"/>
        <property name="appKey" ref="clientOctoAppKey"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.platform.emproduct"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="orderFulfillmentReportThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.order.platform.client.service.report.OrderFulfillmentReportThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" ref="clientOctoAppKey"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderplatform"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="qnhBizThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.productbiz.client.service.QnhBizThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.productbiz"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="crmOrderFinanceThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.saas.crm.data.client.service.OrderFinanceThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('orderFinance_thriftService_timeout','2000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasdata"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="storeSpuBizThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.productbiz.client.service.StoreSpuBizThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.productbiz"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="abnOrderService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.AbnOrderService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="depotGoodsThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.shangou.goodscenter.thrift.DepotGoodsThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.goodscenter"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="departmentV2ThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.DepartmentV2ThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8892"/>
    </bean>

    <bean id="tradeShippingGrayService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.TradeShippingGrayService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tradeShippingCountService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.TradeShippingCountService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tradeShippingOrderService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="invoiceQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.sgfnqnh.finance.tax.thrift.service.InvoiceQueryThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="remoteServerPort" value="8094"/>
        <property name="remoteAppkey" value="com.sankuai.sgfnqnh.finance.tax"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO -->
    </bean>

    <bean id="tagThriftApi" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.xsupply.product.client.service.api.TagThriftApi"/>
        <property name="timeout" value="3000"/>
        <property name="remoteAppkey" value="com.sankuai.sgxsupply.product.management"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="wmsConfigurationThriftService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.store.management.wms.api.config.WmsConfigurationThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('wmsConfigurationThriftService_thrift_timeout','10000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.storeempower"/>
        <property name="remoteServerPort" value="8888"/>
    </bean>

    <bean id="fulfillmentOrderSearchThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.ofc.ofw"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="newPrintLogThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="printThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.qnh.fulfill.print.client.thrift.PrintLogThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.fulfill.print"/>
        <property name="remoteServerPort" value="8870"/>
    </bean>

    <bean id="imInfoThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.empower.infrastructure.client.ImInfoThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfnqnh.infrastructure.im"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="orderLabelThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="qnhOrderApiThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.order.platform.client.service.OrderLabelThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" ref="clientOctoAppKey"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderplatform"/>
        <property name="remoteServerPort" value="9028"/>
    </bean>

    <bean id="drunkHorseOrderThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.sgxsupply.wxmall.bizmanagement.client.thrift.DrunkHorseOrderThriftService"/>
        <property name="timeout" value="8000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="nettyIO" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgxsupply.wxmall.bizmanagement"/>
        <property name="remoteServerPort" value="8001"/>
    </bean>

    <bean id="goodsSkuRelationThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.goodscenter.thrift.GoodsSkuRelationThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.goodscenter"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="InvoiceRecordQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.sgxsupply.wxmall.bizmanagement.client.thrift.InvoiceRecordQueryThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="remoteAppkey" value="com.sankuai.sgxsupply.wxmall.bizmanagement"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="orderTagThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.order.platform.client.service.OrderTagThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderplatform"/>
        <property name="remoteServerPort" value="9025"/>
    </bean>

    <bean id="tOrgService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.infra.osw.api.org.TOrgService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.infra.osw"/>
        <property name="timeout" value="2000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tOrgResourceService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.infra.osw.api.org.TOrgResourceService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.infra.osw"/>
        <property name="timeout" value="2000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tEmployeeService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.infra.osw.api.org.TEmployeeService"/>
        <property name="timeout" value="3000"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.infra.osw"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="basicSkuWacPriceQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.xsupply.price.client.wac.price.query.BasicSkuWacPriceQueryThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="remoteAppkey" value="com.sankuai.sgxsupply.price"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="channelOrderDetailExportThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.saas.order.management.client.export.service.ChannelOrderDetailExportThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ordermng"/>
        <property name="remoteServerPort" value="8026"/>
    </bean>

    <!-- 打印关系 -->
    <import resource="classpath*:saas-shangou-tenant-print-thrift-client.xml"/>
</beans>
