package com.sankuai.shangou.logistics.delivery.dx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaProducer;
import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.shangou.logistics.delivery.xm.enums.CallBackEnum;
import com.sankuai.xm.openplatform.api.entity.CardCallBackReq;
import com.sankuai.xm.openplatform.api.entity.CardCallBackResp;
import com.sankuai.xm.openplatform.api.entity.PullCardReq;
import com.sankuai.xm.openplatform.api.entity.PullCardResp;
import com.sankuai.xm.openplatform.api.service.sdk.OpenCardSeviceSI;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;

/**
 * <AUTHOR>
 * @description 大象卡片回调服务
 * @date 2025-05-15
 */
@Slf4j
@ThriftServerPublisher
public class XmCardCallBackServiceImpl implements OpenCardSeviceSI.Iface {

    @MafkaProducer(namespace = "com.sankuai.mafka.castle.daojiacommon", topic = "xm_call_back_message_topic")
    private IProducerProcessor<Object, String> xmCallbackMessageProducer;


    @Override
    public CardCallBackResp cardCallBack(CardCallBackReq req) {
        try {
            log.info("cardCallBack req:{}", req);
            long empId = req.getOperator().getEmpId();
            String requestId = req.getRequestParam().getRequestId();
            String interactiveParam = req.getInteractiveParam();
            JSONObject jsonObject = JSON.parseObject(interactiveParam);
            jsonObject.put("empId", empId);
            jsonObject.put("requestId", requestId);
            jsonObject.put("callBackType", CallBackEnum.CARD_CALLBACK.getType());
            ProducerResult result = xmCallbackMessageProducer.sendMessage(jsonObject.toJSONString());
            log.info("cardCallBack sendMessage success messageID={}", result.getMessageID());
        } catch (Exception e) {
            log.error("cardCallBack error", e);
        }
        return new CardCallBackResp();
    }

    @Override
    public PullCardResp pullCard(PullCardReq req) throws TException {
        log.info("pullCard req:{}", req);
        return new PullCardResp();
    }
}
