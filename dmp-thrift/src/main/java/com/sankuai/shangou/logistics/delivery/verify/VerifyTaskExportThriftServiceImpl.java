package com.sankuai.shangou.logistics.delivery.verify;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.verify.dto.TPageDTO;
import com.sankuai.shangou.logistics.delivery.verify.dto.VerifyTaskViewAndExportDTO;
import com.sankuai.shangou.logistics.delivery.verify.request.VerifyTaskExportRequest;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/16 11:22
 **/
@ShangouThriftServer
public class VerifyTaskExportThriftServiceImpl implements VerifyTaskExportThriftService {
    @Resource
    private VerifyTaskService verifyTaskService;
    @Override
    public TResult<TPageDTO<VerifyTaskViewAndExportDTO>> exportVerifyTaskList(VerifyTaskExportRequest request) {
        return TResult.buildSuccess(verifyTaskService.searchVerifyTask(request));
    }
}
