package com.sankuai.shangou.logistics.delivery.offlineboard;

import com.google.common.collect.Lists;
import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.response.PaginationList;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.config.LionConfigUtils;
import com.sankuai.shangou.logistics.delivery.offlineboard.enums.BizCalculatorEnum;
import com.sankuai.shangou.logistics.delivery.offlineboard.enums.OfflineOrgDataGroupType;
import com.sankuai.shangou.logistics.delivery.offlineboard.enums.PowerApiApiCodeEnum;
import com.sankuai.shangou.logistics.delivery.offlineboard.enums.SortTypeEnum;
import com.sankuai.shangou.logistics.delivery.offlineboard.request.OfflineDataQueryBaseRequest;
import com.sankuai.shangou.logistics.delivery.offlineboard.request.OfflineDataQueryGroupRequest;
import com.sankuai.shangou.logistics.delivery.offlineboard.response.DynamicExportOfflineBoardDataResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024-06-04
 * @email <EMAIL>
 */
@Slf4j
@ShangouThriftServer
public class OfflineBoardThriftServiceImpl implements OfflineBoardThriftService {

    @Resource
    private OfflineBoardService offlineBoardService;


    @Override
    public TResult<DynamicExportOfflineBoardDataResponse> exportOneOrgOfflineBoardData(OfflineDataQueryGroupRequest request) {
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException("Illegal argument, value = " + validate.get());
        }

        //默认排序逻辑
        if (Objects.isNull(request.getSortField())) {
            request.setSortField("groupColumn");
            request.setSortType(SortTypeEnum.ASC.getCode());
        }
        OfflineOrgDataGroupType offlineOrgDataGroupType = OfflineOrgDataGroupType.valueOfType(2);
        assert offlineOrgDataGroupType != null;
        request.setGroupColumn(offlineOrgDataGroupType.getPowerApiGroupKey());
        request.setGroupColumnName(offlineOrgDataGroupType.getPowerApiGroupNameKey());

        return processOfflineDataExportQuery(request, BizCalculatorEnum.ORG_PERFORMANCE_DATA,
                PowerApiApiCodeEnum.FULFILL_OFFLINE_ORG_DATA, "_1" + parseVersion(request.getVersion()));
    }

    @Override
    public TResult<DynamicExportOfflineBoardDataResponse> exportTwoOrgOfflineBoardData(OfflineDataQueryGroupRequest request) {
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException("Illegal argument, value = " + validate.get());
        }

        //默认排序逻辑
        if (Objects.isNull(request.getSortField())) {
            request.setSortField("groupColumn");
            request.setSortType(SortTypeEnum.ASC.getCode());
        }
        OfflineOrgDataGroupType offlineOrgDataGroupType = OfflineOrgDataGroupType.valueOfType(3);
        assert offlineOrgDataGroupType != null;
        request.setGroupColumn(offlineOrgDataGroupType.getPowerApiGroupKey());
        request.setGroupColumnName(offlineOrgDataGroupType.getPowerApiGroupNameKey());

        return processOfflineDataExportQuery(request, BizCalculatorEnum.ORG_PERFORMANCE_DATA,
                PowerApiApiCodeEnum.FULFILL_OFFLINE_ORG_DATA, "_2" + parseVersion(request.getVersion()));
    }

    @Override
    public TResult<DynamicExportOfflineBoardDataResponse> exportStoreOfflineBoardData(OfflineDataQueryBaseRequest request) {
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException("Illegal argument, value = " + validate.get());
        }

        //默认排序逻辑
        if (Objects.isNull(request.getSortField())) {
            request.setSortField("storeName");
            request.setSortType(SortTypeEnum.ASC.getCode());
        }

        return processOfflineDataExportQuery(request, BizCalculatorEnum.OFFLINE_STORE_FULFILL_DATA,
                PowerApiApiCodeEnum.FULFILL_OFFLINE_STORE_DATA, StringUtils.EMPTY + parseVersion(request.getVersion()));
    }

    @Override
    public TResult<DynamicExportOfflineBoardDataResponse> exportRiderOfflineBoardData(OfflineDataQueryBaseRequest request) {
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException("Illegal argument, value = " + validate.get());
        }

        //默认排序逻辑
        // https://fsd.sankuai.com/ones/requirement/detail/85520625 列表排序改为默认按已送达订单量排序，高的排在上面
        if (Objects.isNull(request.getSortField())) {
            request.setSortField("deliveredOrderNum");
            request.setSortType(SortTypeEnum.DESC.getCode());
        }

        // 再添加员工id排序  如果只有按已送达订单量排序 可能存在分页时有重复数据的问题
        request.setSortRuleList(
                Lists.newArrayList(new OfflineDataQueryBaseRequest.SortRule("employeeId", SortTypeEnum.ASC.getCode())));


        return processOfflineDataExportQuery(request, BizCalculatorEnum.OFFLINE_RIDER_FULFILL_DATA,
                PowerApiApiCodeEnum.FULFILL_OFFLINE_RIDER_DATA, StringUtils.EMPTY + parseVersion(request.getVersion()));
    }

    @Override
    public TResult<DynamicExportOfflineBoardDataResponse> exportAbnormalOfflineBoardData(OfflineDataQueryBaseRequest request) {
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException("Illegal argument, value = " + validate.get());
        }

        //默认排序逻辑
        if (Objects.isNull(request.getSortField())) {
            request.setSortField("employeeName");
            request.setSortType(SortTypeEnum.ASC.getCode());
        }

        return processOfflineDataExportQuery(request,BizCalculatorEnum.OFFLINE_ABNORMAL_ORDER_FULFILL_DATA,
                PowerApiApiCodeEnum.FULFILL_OFFLINE_ABNORMAL_ORDER_V4, StringUtils.EMPTY + parseVersion(request.getVersion()));
    }

    @Override
    public TResult<DynamicExportOfflineBoardDataResponse> exportDeliveryChannelOfflineBoardData(OfflineDataQueryBaseRequest request) {
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException("Illegal argument, value = " + validate.get());
        }

        //默认排序逻辑
        if (Objects.isNull(request.getSortField())) {
            request.setSortField("poiName");
            request.setSortType(SortTypeEnum.ASC.getCode());
        }
        request.setSortRuleList(
                Lists.newArrayList(new OfflineDataQueryBaseRequest.SortRule("carrierName", SortTypeEnum.ASC.getCode()))
        );


        return processOfflineDataExportQuery(request, BizCalculatorEnum.OFFLINE_DELIVERY_CHANNEL_DATA,
                PowerApiApiCodeEnum.FULFILL_OFFLINE_THIRD_PERFORMANCE_DATA, StringUtils.EMPTY + parseVersion(request.getVersion()));
    }

    private <R extends OfflineDataQueryBaseRequest> TResult<DynamicExportOfflineBoardDataResponse> processOfflineDataExportQuery(R request, BizCalculatorEnum bizCalculatorEnum, PowerApiApiCodeEnum powerApiApiCodeEnum, String identify) {
        PaginationList<HashMap<String, Object>> paginationList = offlineBoardService.pageQueryOfflineDataTemplate(
                bizCalculatorEnum,
                powerApiApiCodeEnum,
                request
        );

        Map<String/*keyName*/, String/*headerName*/> excelHeaderMap = LionConfigUtils.getExcelHeaderMap(bizCalculatorEnum, identify);

        List<List<String>> result = new ArrayList<>();
        for (HashMap<String, Object> dataMap : paginationList.getList()) {
            // 创建一个List来存放当前HashMap按orderMap的key排序后的值
            List<String> sortedValues = new ArrayList<>();

            // 按照orderMap的key顺序遍历并提取值
            for (String key : excelHeaderMap.keySet()) {
                // 如果key不存在，则添加null
                sortedValues.add(
                        Objects.isNull(dataMap.get(key))? "-" : String.valueOf(dataMap.get(key))
                );
            }

            // 将排序并提取后的值的List加入到结果List中
            result.add(sortedValues);
        }


        DynamicExportOfflineBoardDataResponse response = new DynamicExportOfflineBoardDataResponse();
        response.setResultPaginationList(
                new PaginationList<>(
                        paginationList.getPage(), paginationList.getPage(),
                        paginationList.getTotal(), result
                )
        );
        response.setHeadList(Lists.newArrayList(excelHeaderMap.values()));
        return TResult.buildSuccess(response);
    }

    private String parseVersion(String version) {
        return StringUtils.isBlank(version) ? "" : version;
    }
}
