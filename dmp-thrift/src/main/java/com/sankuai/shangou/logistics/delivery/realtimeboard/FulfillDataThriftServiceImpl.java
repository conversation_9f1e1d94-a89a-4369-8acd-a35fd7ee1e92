package com.sankuai.shangou.logistics.delivery.realtimeboard;

import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.response.TPageResult;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.fulfill.FulfillDataThriftService;
import com.sankuai.shangou.logistics.delivery.fulfill.dto.*;
import com.sankuai.shangou.logistics.delivery.fulfill.request.DepartmentFulfillDataQueryRequest;
import com.sankuai.shangou.logistics.delivery.fulfill.request.PageFulfillDataQueryRequest;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.*;


@Slf4j
@ShangouThriftServer
public class FulfillDataThriftServiceImpl implements FulfillDataThriftService {
    @Resource
    private FulfillDataSearchService fulfillDataSearchService;

    @Override
    @MethodLog(logRequest = true,logResponse = true)
    public TPageResult<FulfillCarrierStatisticsDTO> queryCarrierStatistic(FulfillDataPageQueryDTO req) {
        return fulfillDataSearchService.queryCarrierStatistic(req);
    }


    @Override
    @MethodLog(logRequest = true,logResponse = true)
    public TPageResult<FulfillRiderStatisticsDTO> queryRiderStatistic(PageFulfillDataQueryRequest request) {
        return fulfillDataSearchService.queryRiderStatistics(request);
    }

    @Override
    @MethodLog(logRequest = true,logResponse = true)
    public TPageResult<FulfillStoreStatisticsDTO> queryStoreStatistic(PageFulfillDataQueryRequest request) {
        return fulfillDataSearchService.queryStoreStatistics(request);
    }

    @Override
    @MethodLog(logRequest = true,logResponse = true)
    public TPageResult<DepartmentFulfillDataDTO> queryDepartmentLevelStatistic(DepartmentFulfillDataQueryRequest request) {
        return fulfillDataSearchService.queryDepartmentLevelStatistic(request);
    }

    @Override
    @MethodLog(logRequest = true,logResponse = true)
    public TPageResult<AbnormalOrderDTO> queryAbnormalOrderList(PageFulfillDataQueryRequest request) {
        return fulfillDataSearchService.queryAbnormalOrderList(request);
    }


}
