package com.sankuai.shangou.logistics.delivery.dx;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.xm.openplatform.callback.api.XmOpenCallbackServiceI;
import com.sankuai.xm.openplatform.common.entity.EmptyResp;
import com.sankuai.xm.openplatform.common.entity.RespStatus;
import com.sankuai.xm.openplatform.common.enums.ResCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;

/**
 * <AUTHOR>
 * @description 大象开放平台消息回调
 * @date 2025-05-15
 */
@Slf4j
@ThriftServerPublisher
public class XmOpenCallbackServiceImpl implements XmOpenCallbackServiceI.Iface {
    @Override
    public EmptyResp eventCallback(int eventType, String jsonEvent) throws TException {
        log.info("大象回调，eventType:{}, jsonEvent:{}", eventType, jsonEvent);
        EmptyResp resp = new EmptyResp();
        RespStatus status = new RespStatus();
        status.setCode(ResCodeEnum.SUCCESS.getCode());
        status.setMsg(ResCodeEnum.SUCCESS.getMsg());
        resp.setStatus(status);
        return resp;
    }
}
