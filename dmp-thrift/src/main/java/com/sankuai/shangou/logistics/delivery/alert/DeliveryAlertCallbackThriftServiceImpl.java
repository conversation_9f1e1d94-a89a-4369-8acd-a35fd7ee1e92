package com.sankuai.shangou.logistics.delivery.alert;

import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaProducer;
import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.logistics.delivery.alert.enums.DeliveryAlertCallbackEnum;
import com.sankuai.shangou.logistics.delivery.alert.mq.msg.DeliveryAlertCallbackMessage;
import com.sankuai.shangou.logistics.delivery.alert.request.DeliveryAlertCallbackRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description 配送告警回调
 * @date 2025-05-15
 */
@Slf4j
@ShangouThriftServer
public class DeliveryAlertCallbackThriftServiceImpl implements DeliveryAlertCallbackThriftService {

    @MafkaProducer(namespace = "com.sankuai.mafka.castle.daojiacommon", topic = "delivery_alert_callback_message_topic")
    private IProducerProcessor<Object, String> deliveryAlertCallbackMessageProducer;

    @Override
    public Boolean callback(DeliveryAlertCallbackRequest request) {
        log.info("配送告警回调, request:{}", request);
        if (request == null || StringUtils.isBlank(request.getType())) {
            return false;
        }
        DeliveryAlertCallbackEnum callbackEnum = DeliveryAlertCallbackEnum.ofType(request.getType());
        if (callbackEnum == null) {
            return false;
        }
        DeliveryAlertCallbackMessage message = new DeliveryAlertCallbackMessage();
        message.setType(callbackEnum.getType());
        try {
            ProducerResult sentResult = deliveryAlertCallbackMessageProducer.sendMessage(message.toString());
            log.info("配送告警回调消息发送成功 messageID={}", sentResult.getMessageID());
        } catch (Exception e) {
            log.error("配送告警回调消息发送失败, message:{}", message, e);
            return false;
        }
        return true;
    }
}
