package com.sankuai.shangou.logistics.delivery.grayconfig;

import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.request.UserContext;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.gray.GrayManagementThriftService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/7 16:33
 **/
@Service("grayManagementServiceProcessor")
public class GrayManagementThriftServiceImpl implements GrayManagementThriftService {
    @Resource
    private GrayConfigService grayConfigService;

    @Override
    public Boolean checkIsGrayStore(String grayKey, Long tenantId, Long storeId) {
        checkParam(grayKey, tenantId, storeId);
        return grayConfigService.judgeIsGrayStore(grayKey, tenantId, storeId);
    }

    @Override
    public TResult<Boolean> checkIsGrayStoreV2(UserContext userContext, String grayKey, Long storeId) {
        checkParam(grayKey, userContext.getTenantId(), storeId);
        return TResult.buildSuccess(grayConfigService.judgeIsGrayStore(grayKey, userContext.getTenantId(), storeId));
    }

    @Override
    public List<Long> batchJudgeIsGrayStore(String grayKey, Long tenantId, List<Long> storeIds) {
        checkParam(grayKey, tenantId, storeIds);
        return grayConfigService.batchJudgeIsGrayStore(grayKey, tenantId, storeIds);
    }

    private void checkParam(String grayKey, Long tenantId, Long storeId) {
        if (StringUtils.isBlank(grayKey)) {
            throw new IllegalArgumentException("灰度key不能为空");
        }

        if (tenantId == null || tenantId <= 0L) {
            throw new IllegalArgumentException("租户id不合法");
        }

        if (storeId == null || storeId <= 0L) {
            throw new IllegalArgumentException("门店id不合法");
        }
    }

    private void checkParam(String grayKey, Long tenantId, List<Long> storeIds) {
        if (StringUtils.isBlank(grayKey)) {
            throw new IllegalArgumentException("灰度key不能为空");
        }

        if (tenantId == null || tenantId <= 0L) {
            throw new IllegalArgumentException("租户id不合法");
        }

        if (CollectionUtils.isNotEmpty(storeIds) && storeIds.size() > 1000) {
            throw new IllegalArgumentException("门店id个数不能超过1000");
        }

        if (CollectionUtils.isNotEmpty(storeIds) && storeIds.stream().anyMatch(storeId -> storeId == null || storeId <= 0L)) {
            throw new IllegalArgumentException("门店id不合法");
        }
    }
}
