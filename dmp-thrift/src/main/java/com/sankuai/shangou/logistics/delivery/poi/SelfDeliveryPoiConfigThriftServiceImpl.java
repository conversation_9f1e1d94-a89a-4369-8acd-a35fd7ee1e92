package com.sankuai.shangou.logistics.delivery.poi;

import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.logistics.delivery.common.TOperator;
import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPO;
import com.sankuai.shangou.logistics.delivery.poi.dto.SelfDeliveryPoiConfigDTO;
import com.sankuai.shangou.logistics.delivery.poi.dto.TVoid;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-09-01
 * @email <EMAIL>
 */
@Slf4j
@ShangouThriftServer
public class SelfDeliveryPoiConfigThriftServiceImpl implements SelfDeliveryPoiConfigThriftService {

    @Resource
    private SelfDeliveryPoiConfigService selfDeliveryPoiConfigService;


    @Override
    public TResult<SelfDeliveryPoiConfigDTO> querySelfDeliveryConfig(Long tenantId, Long poiId) {
        SelfDeliveryPoiConfigPO selfDeliveryPoiConfigPO = selfDeliveryPoiConfigService.querySelfDeliveryConfig(tenantId, poiId);
        return new TResult<SelfDeliveryPoiConfigDTO>().success(convertToDTO(selfDeliveryPoiConfigPO));
    }

    @Override
    public TResult<TVoid> initSelfDeliveryConfig(Long tenantId, Long poiId) {
        selfDeliveryPoiConfigService.initConfig(tenantId, poiId);
        return new TResult<TVoid>().success();
    }

    private SelfDeliveryPoiConfigDTO convertToDTO(SelfDeliveryPoiConfigPO selfDeliveryPoiConfigPO) {
        if(Objects.isNull(selfDeliveryPoiConfigPO)) {
            return null;
        }
        SelfDeliveryPoiConfigDTO dto = new SelfDeliveryPoiConfigDTO();
        dto.setId(selfDeliveryPoiConfigPO.getId());
        dto.setTenantId(selfDeliveryPoiConfigPO.getTenantId());
        dto.setPoiId(selfDeliveryPoiConfigPO.getPoiId());
        dto.setEnableTurnDelivery(selfDeliveryPoiConfigPO.getEnableTurnDelivery());
        dto.setTOperator(new TOperator(
                        selfDeliveryPoiConfigPO.getLastOperatorId(),
                        selfDeliveryPoiConfigPO.getLastOperatorName(),
                TimeUtils.toMilliSeconds(selfDeliveryPoiConfigPO.getLastOperateTime())
                )
        );
        dto.setEnablePickDeliverySplit(selfDeliveryPoiConfigPO.getEnablePickDeliverySplit());
        return dto;
    }
}
