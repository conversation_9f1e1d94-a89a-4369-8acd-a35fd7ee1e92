package com.sankuai.shangou.logistics.delivery.questionnaire;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDO;

import com.sankuai.shangou.logistics.delivery.questionnaire.dto.DeliveryQuestionnaireDTO;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/15 15:54
 **/

@Slf4j
@ShangouThriftServer
public class DeliveryQuestionnaireThriftServiceImpl implements DeliveryQuestionnaireThriftService {
    @Resource
    private DeliveryQuestionnaireService deliveryQuestionnaireService;

    @Override
    @MethodLog(logResponse = true, logRequest = true)
    @CatTransaction
    public TResult<List<DeliveryQuestionnaireDTO>> queryQuestionnaireByDeliveryOrderIds(List<Long> deliveryOrderIds) {
        List<DeliveryQuestionnaireDO> deliveryQuestionnaireDOList = deliveryQuestionnaireService.queryQuestionnaireByDeliveryOrderIds(deliveryOrderIds);

        List<DeliveryQuestionnaireDTO> questionnaireDTOList = deliveryQuestionnaireDOList.stream()
                .map(this::transform2DTO)
                .collect(Collectors.toList());

        return new TResult<List<DeliveryQuestionnaireDTO>>().success(questionnaireDTOList);
    }

    private DeliveryQuestionnaireDTO transform2DTO(DeliveryQuestionnaireDO deliveryQuestionnaireDO) {
        DeliveryQuestionnaireDTO dto = new DeliveryQuestionnaireDTO();
        dto.setAnswer(deliveryQuestionnaireDO.getAnswer());
        dto.setId(deliveryQuestionnaireDO.getId());
        dto.setCityId(deliveryQuestionnaireDO.getCityId());
        dto.setAnswerOptList(JsonUtil.fromJson(deliveryQuestionnaireDO.getAnswerOptionsSnapshot(), new TypeReference<List<String>>() {}));
        dto.setRiderAccountId(deliveryQuestionnaireDO.getRiderAccountId());
        dto.setOrderId(deliveryQuestionnaireDO.getOrderId());
        dto.setQuestion(deliveryQuestionnaireDO.getQuestion());
        dto.setStoreId(deliveryQuestionnaireDO.getStoreId());
        dto.setDeliveryOrderId(deliveryQuestionnaireDO.getDeliveryOrderId());

        return dto;
    }
}
