package com.sankuai.shangou.logistics.delivery.shippingarea;

import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.shippingarea.request.ResetShippingAreaRequest;
import com.sankuai.shangou.logistics.delivery.shippingarea.service.ShippingAreaService;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/6/11 15:51
 **/
@ShangouThriftServer
public class ShippingAreaOperateThriftServiceImpl implements ShippingAreaOperateThriftService {
    @Resource
    private ShippingAreaService shippingAreaService;

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    public TResult<Void> reset(ResetShippingAreaRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }
        shippingAreaService.resetChannelPoiShippingArea(request);
        return TResult.buildSuccess(null);
    }
}
