package com.sankuai.shangou.logistics.delivery.config;

import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.request.UserContext;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.gray.GrayManagementThriftService;
import com.sankuai.shangou.logistics.delivery.grayconfig.GrayConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/9 16:33
 **/
@Slf4j
@ShangouThriftServer
public class ConfigManagementThriftServiceImpl implements ConfigManagementThriftService {
    @Resource
    private GrayConfigService grayConfigService;

    @Override
    public Boolean queryTenantDeliveryConfig(String grayKey, Long tenantId) {
        return LionConfigUtils.getConfig(grayKey, tenantId);
    }
}
