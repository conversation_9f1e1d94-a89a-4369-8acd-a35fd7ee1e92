package com.sankuai.shangou.logistics.delivery;

import com.sankuai.shangou.infra.osw.api.org.dto.response.DepartmentDTO;
import com.sankuai.shangou.logistics.delivery.realtimeboard.FulfillDataSearchService;
import org.apache.shiro.util.Assert;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class FulfillDataThriftServiceTest {
    @Test
    public void testConvertStoreData2DepartmentName() {
        DepartmentDTO storeInfo = new DepartmentDTO();
        storeInfo.setDepartmentId(1046684L);
        storeInfo.setDepartmentName("歪马送酒（大观公园店）");
        storeInfo.setParentId(1055907L);
        DepartmentDTO level3Info = new DepartmentDTO();
        level3Info.setDepartmentId(1055907L);
        level3Info.setDepartmentName("广州二区");
        level3Info.setParentId(1045101L);
        DepartmentDTO level2Info = new DepartmentDTO();
        level2Info.setDepartmentId(1045101L);
        level2Info.setDepartmentName("广州");
        level2Info.setParentId(1030244L);
        Map<Long, String> result = FulfillDataSearchService.convertToStoreId2DepartmentName(new HashMap<Long, Long>() {{
            put(1046684L, 1033573L);
        }}, Collections.singletonList(storeInfo), Collections.singletonList(level3Info), Collections.singletonList(level2Info));
        Assert.isTrue(result.get(1033573L).equals(level2Info.getDepartmentName()));
        result = FulfillDataSearchService.convertToStoreId2DepartmentName(new HashMap<Long, Long>() {{
            put(1046684L, 1033573L);
        }}, Collections.singletonList(storeInfo), Collections.singletonList(level3Info));

        Assert.isTrue(result.get(1033573L).equals(level3Info.getDepartmentName()));
    }
}
