<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sankuai.shangou</groupId>
        <artifactId>logistics-dmp</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dmp-thrift</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>zebra-xframe-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>dmp-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>dmp-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-thrift-publisher</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-common-lang</artifactId>
        </dependency>
    </dependencies>

</project>