package com.sankuai.shangou.logistics.sdms.sdk.verify.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/7/22 11:55
 **/
@Data
@ThriftStruct
public class TVerifyTaskDTO {
    @ThriftField(1)
    public Long taskId;

    @ThriftField(2)
    public List<Integer> collectContents;

    @ThriftField(3)
    public Integer collectMode;

    @ThriftField(4)
    public Integer taskStatus;

    @ThriftField(5)
    public LocalDateTime taskPushTime;

    @ThriftField(6)
    public LocalDateTime taskCompleteTime;

    @ThriftField(7)
    public Long riderAccountId;

    @ThriftField(8)
    public Long storeId;

    @ThriftField(9)
    public String channelOrderId;

    @ThriftField(10)
    public Boolean faceIdentifyResult;

    @ThriftField(11)
    public Boolean helmetIdentifyResult;

    @ThriftField(12)
    public Boolean dressingIdentifyResult;

    @ThriftField(13)
    public String callbackCode;

    @ThriftField(14)
    public String callbackMsg;

    @ThriftField(15)
    public LocalDateTime expireTime;

    @ThriftField(16)
    public String requestCode;

    @ThriftField(17)
    public String uuid;

    @ThriftField(18)
    public Map<String, Integer> punishInfoMap;

}
