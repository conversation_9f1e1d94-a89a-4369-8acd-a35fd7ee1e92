package com.sankuai.shangou.logistics.sdms.sdk.verify.enums;

/**
 * <AUTHOR>
 * @since 2024/10/15 16:19
 **/
public enum CollectionModeEnum {
    PROACTIVE_COLLECTION(1, "主动采集"),
    PASSIVE_COLLECTION(2, "静默采集");

    private int code;

    private String desc;

    CollectionModeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static CollectionModeEnum valueOfCode(int code) {
        for (CollectionModeEnum obj : CollectionModeEnum.values()) {
            if (java.util.Objects.equals(obj.code, code)) {
                return obj;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
