package com.sankuai.shangou.logistics.sdms.sdk.common;

import com.facebook.swift.codec.ThriftField;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.exception.ShangouBizException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @email <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TContextInfo {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店列表字符串，多门店模式时用逗号分割",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String poiIdStr;

    @FieldDoc(
            description = "操作人员工id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Long employeeId;

    @FieldDoc(
            description = "操作人姓名",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public String employeeName;

    @FieldDoc(
            description = "账号id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public Long accountId;

    @FieldDoc(
            description = "账号名",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    public String accountName;

    public long getPoiId() {
        if (StringUtils.isBlank(poiIdStr) || !NumberUtils.isNumber(poiIdStr)) {
            throw new ShangouBizException("非单门店模式");
        }
        return Long.parseLong(poiIdStr);
    }

    /**
     * 兼容全部门店模式，单门店模式会返回一个
     */
    public List<Long> getPoiIdList() {
        if (StringUtils.isBlank(poiIdStr)) {
            throw new ShangouBizException("未获取到门店ID");
        }
        if(poiIdStr.contains(",")) {
            return Arrays.stream(poiIdStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
        } else if (NumberUtils.isNumber(poiIdStr)) {
            return Lists.newArrayList(Long.parseLong(poiIdStr));
        } else {
            throw new ShangouBizException("解析门店失败");
        }

    }
}
