package com.sankuai.shangou.logistics.sdms.sdk.limit.adapter;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/10/30 14:42
 **/
public interface LimitItemClientAdapter {
    @MethodDoc(
            displayName = "查询限制项信息",
            description = "查询限制项信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询限制项信息请求",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "限制项信息",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    TResult<LimitItemDTO> queryLimitItem(Long tenantId, Long accountId, Integer limitType);

    @MethodDoc(
            displayName = "批量查询限制项信息",
            description = "批量查询限制项信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量查询限制项信息请求",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "批量限制项信息",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    TResult<List<LimitItemDTO>> batchQueryLimitItemList(Long tenantId, List<Long> accountIdList, Integer limitType);
}
