package com.sankuai.shangou.logistics.sdms.sdk.limit.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/11/6 17:37
 **/
@Getter
public enum LimitTypeEnum {
    SAFETY_TRAINING(1, "安全培训"),
    NEW_EMPLOYEE_TRAINING(2, "新人培训"),
    EQUIPMENT_UN_GET(3, "装备未领取"),
    ;

    private final int code;

    private final String desc;

    LimitTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LimitTypeEnum enumOf(int code) {
        for (LimitTypeEnum val : LimitTypeEnum.values()) {
            if (val.getCode() == code) {
                return val;
            }
        }

        throw new IllegalArgumentException("不识别的枚举类型");
    }
}
