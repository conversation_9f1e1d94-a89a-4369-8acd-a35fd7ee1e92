package com.sankuai.shangou.logistics.sdms.sdk.limit.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/10/30 14:45
 **/
@SuppressWarnings("unused")
@ThriftStruct
@Data
public class LimitItemDTO {
    @FieldDoc(description = "账号id")
    @ThriftField(1)
    private Long accountId;

    @FieldDoc(description = "限制原因")
    @ThriftField(2)
    private String reason;

    @FieldDoc(description = "跳转链接")
    @ThriftField(3)
    private String skipUrl;

    @FieldDoc(description = "跳转链接类型, 参考com.sankuai.shangou.logistics.sdms.sdk.limit.constant.SkipUrlTypeEnum")
    @ThriftField(4)
    private Integer skipUrlType;

    @FieldDoc(description = "跳转按钮文案")
    @ThriftField(5)
    private String skipButtonText;

    @FieldDoc(description = "解除限制说明文案")
    @ThriftField(6)
    private String releaseLimitText;

    @FieldDoc(description = "限制接单类型，1-直接限制，2-先提示后限制")
    @ThriftField(7)
    private Integer limitMode;

    @FieldDoc(description = "限制开始时间，配合limitMode=2使用")
    @ThriftField(8)
    private LocalDateTime limitStartDate;

    @FieldDoc(description = "限制装备skuId，当限制是装备未领取的时候有值")
    @ThriftField(9)
    private Map<String, Integer> limitSkuIdNumMap;

    @FieldDoc(description = "是否灰度门店")
    @ThriftField(10)
    private Boolean isGrayStore;

    /**
     * 兼容旧数据直接返回直接限制
     */
    public Integer getLimitMode() {
        return Objects.isNull(limitMode) ? 1 : limitMode;
    }
}
