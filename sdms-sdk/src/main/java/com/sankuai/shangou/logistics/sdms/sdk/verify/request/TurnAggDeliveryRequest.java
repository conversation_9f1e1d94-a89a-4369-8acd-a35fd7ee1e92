package com.sankuai.shangou.logistics.sdms.sdk.verify.request;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-31
 * @email <EMAIL>
 */
@Data
public class TurnAggDeliveryRequest {

    @FieldDoc(
            description = "订单id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String channelOrderId;

    @FieldDoc(
            description = "订单id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Integer channelId;
}
