package com.sankuai.shangou.logistics.sdms.sdk.verify.dto;

import com.sankuai.shangou.commons.utils.json.JsonUtils;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-03-31
 */
@Data
public class SmilePendingTaskInfo {

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 待办详细
     */
    private List<Detail> detailList;

    @Data
    public static class Detail {

        /**
         * 任务数
         */
        private Integer taskCount;

        /**
         * 延时任务数
         */
        private Integer delayTaskCount;

        /**
         * 临期任务数
         */
        private Integer nearExpirationTaskCount;

        /**
         * 门店id
         */
        private Long poiId;
    }

    public String toJsonStr(){
        return JsonUtils.toJson(this);
    }
}
