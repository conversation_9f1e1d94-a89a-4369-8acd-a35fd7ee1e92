package com.sankuai.shangou.logistics.sdms.sdk.order;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.sdms.sdk.common.TContextInfo;
import com.sankuai.shangou.logistics.sdms.sdk.config.dto.request.QuerySelfDeliveryConfigListReq;
import com.sankuai.shangou.logistics.sdms.sdk.verify.request.TurnAggDeliveryRequest;

/**
 * <AUTHOR>
 * @date 2023-08-31
 * @email <EMAIL>
 */
@InterfaceDoc(
        displayName = "配送操作接口",
        type = "octo.thrift.annotation",
        scenarios = "运单统计数据查询接口",
        description = "运单统计数据查询接口",
        authors = {
                "yujing10"
        }
)
@ThriftService
public interface DeliveryOperationThriftService {

    @MethodDoc(
            displayName = "转三方配送",
            description = "转三方配送",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "转三方配送请求",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询骑手维度运单统计信息返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<Void> turnToAggDelivery(TContextInfo tContextInfo, TurnAggDeliveryRequest req);


}
