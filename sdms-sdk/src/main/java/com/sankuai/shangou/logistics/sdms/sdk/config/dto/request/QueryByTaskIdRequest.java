package com.sankuai.shangou.logistics.sdms.sdk.config.dto.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/10/11 19:19
 **/
@ThriftStruct
@Data
public class QueryByTaskIdRequest {

    @ThriftField(1)
    @FieldDoc(description = "采集任务id", requiredness = Requiredness.REQUIRED)
    private Long taskId;
}
