package com.sankuai.shangou.logistics.sdms.sdk.verify.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/10 10:44
 **/

public enum VerifyTaskStatusEnum {
    INIT(10, "初始化"),

    EXPIRE(80, "已过期"),

    CANCEL(90, "已取消"),

    COMPLETED(100, "已完成");

    private int code;

    private String desc;

    VerifyTaskStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static VerifyTaskStatusEnum enumOf(int code) {
        for (VerifyTaskStatusEnum val : values()) {
            if (code == val.getCode()) {
                return val;
            }
        }

        throw new IllegalArgumentException("不识别的任务状态");
    }

    /**
     * 获取进行中的任务状态
     */
    public static List<Integer> getOngoingStatus() {
        return Collections.singletonList(INIT.getCode());
    }

    public static List<Integer> getEndStatus() {
        return Arrays.asList(COMPLETED.getCode(), EXPIRE.getCode());
    }
}
