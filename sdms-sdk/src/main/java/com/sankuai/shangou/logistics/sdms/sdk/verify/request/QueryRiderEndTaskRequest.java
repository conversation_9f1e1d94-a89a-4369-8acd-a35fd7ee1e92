package com.sankuai.shangou.logistics.sdms.sdk.verify.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2025-03-26
 */
@ThriftStruct
@Data
public class QueryRiderEndTaskRequest {

    @FieldDoc(description = "分页页码")
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    private Integer page;

    @FieldDoc(description = "分页大小")
    @ThriftField(value = 2, requiredness = ThriftField.Requiredness.REQUIRED)
    private Integer pageSize;

    @FieldDoc(description = "骑手账号id")
    @ThriftField(value = 3, requiredness = ThriftField.Requiredness.REQUIRED)
    private Long riderAccountId;

    @FieldDoc(description = "开始时间")
    @ThriftField(value = 4, requiredness = ThriftField.Requiredness.REQUIRED)
    private LocalDateTime beginTime;

    @FieldDoc(description = "结束时间")
    @ThriftField(value = 5, requiredness = ThriftField.Requiredness.REQUIRED)
    private LocalDateTime endTime;

    @FieldDoc(description = "任务状态")
    @ThriftField(value = 6, requiredness = ThriftField.Requiredness.OPTIONAL)
    private Integer taskStatus;

    @FieldDoc(description = "审核状态")
    @ThriftField(value = 7, requiredness = ThriftField.Requiredness.OPTIONAL)
    private Integer verifyResult;
}
