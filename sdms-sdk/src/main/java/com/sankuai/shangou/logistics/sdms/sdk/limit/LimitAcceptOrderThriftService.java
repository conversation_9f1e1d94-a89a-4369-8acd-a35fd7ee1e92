package com.sankuai.shangou.logistics.sdms.sdk.limit;

import com.facebook.swift.service.ThriftMethod;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/10/30 15:45
 **/
@ShangouThriftServer
public interface LimitAcceptOrderThriftService {
    @MethodDoc(
            displayName = "查询单个骑手限制项信息",
            description = "查询单个骑手限制项信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询单个骑手限制项信息请求",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询单个骑手限制项信息请求",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<List<LimitItemDTO>> queryLimitItemListByAccountIdNew(Long tenantId, Long accountId, Long storeId);


    @MethodDoc(
            displayName = "查询多个骑手限制项信息",
            description = "查询多个骑手限制项信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询多个骑手限制项信息请求",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询多个骑手限制项信息",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<Map<Long, List<LimitItemDTO>>> queryLimitItemListByAccountIdListNew(Long tenantId, List<Long> accountIdList, Long storeId);

    @MethodDoc(
            displayName = "查询单个骑手限制项信息",
            description = "查询单个骑手限制项信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询单个骑手限制项信息请求",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询单个骑手限制项信息请求",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<List<LimitItemDTO>> queryLimitItemListByAccountId(Long tenantId, Long accountId);


    @MethodDoc(
            displayName = "查询多个骑手限制项信息",
            description = "查询多个骑手限制项信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询多个骑手限制项信息请求",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询多个骑手限制项信息",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<Map<Long, List<LimitItemDTO>>> queryLimitItemListByAccountIdList(Long tenantId, List<Long> accountIdList);
}
