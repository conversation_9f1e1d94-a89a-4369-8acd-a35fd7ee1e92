package com.sankuai.shangou.logistics.sdms.sdk.verify.enums;

/**
 * <AUTHOR>
 * @date 2024-09-23
 * @email <EMAIL>
 */
public enum CollectContentTypeEnum {

    FACE(1 ,"人脸"),
    HELMET(2, "戴盔"),
    DRESS(3, "着装"),

    EXPIRE(99, "超时")

    ;
    private int code;

    private String desc;

    CollectContentTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static CollectContentTypeEnum valueOfCode(int code) {
        for (CollectContentTypeEnum obj : CollectContentTypeEnum.values()) {
            if (java.util.Objects.equals(obj.code, code)) {
                return obj;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
    
    
}
