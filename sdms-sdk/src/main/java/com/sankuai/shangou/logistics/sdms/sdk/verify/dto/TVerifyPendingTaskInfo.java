package com.sankuai.shangou.logistics.sdms.sdk.verify.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2025-04-09
 */
@ThriftStruct
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TVerifyPendingTaskInfo {
    @ThriftField(1)
    public Long id;

    /**
     * 提醒时间(秒)
     */
    @ThriftField(2)
    private Long remindTime;

    @ThriftField(3)
    private Integer taskStatus;

    @ThriftField(4)
    private Long riderAccountId;
    /**
     * 任务下发时间
     */
    @ThriftField(5)
    private Long pushTaskTime;
    /**
     * 采集模式
     */
    @ThriftField(6)
    private Integer collectMode;

    /**
     * 临期时间(秒)
     */
    @ThriftField(7)
    private Integer nearExpirationTime;
}
