package com.sankuai.shangou.logistics.sdms.sdk.verify.enums;

/**
 * <AUTHOR>
 * @date 2024-09-23
 * @email <EMAIL>
 */
public enum PunishTypeEnum {

    NONE(0, "不落罚"),
    BY_PUNISH_SYSTEM(1, "通过落罚系统"),
    BY_MANUAL(2, "通过人工"),


    ;
    private int code;

    private String desc;

    PunishTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static PunishTypeEnum valueOfCode(int code) {
        for (PunishTypeEnum obj : PunishTypeEnum.values()) {
            if (java.util.Objects.equals(obj.code, code)) {
                return obj;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
    
    
}
