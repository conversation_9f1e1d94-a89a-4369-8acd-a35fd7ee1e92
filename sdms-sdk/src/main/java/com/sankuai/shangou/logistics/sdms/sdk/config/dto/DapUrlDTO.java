package com.sankuai.shangou.logistics.sdms.sdk.config.dto;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-08-29
 * @email <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DapUrlDTO {

    @FieldDoc(
            description = "设置url",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String configUrl;

}
