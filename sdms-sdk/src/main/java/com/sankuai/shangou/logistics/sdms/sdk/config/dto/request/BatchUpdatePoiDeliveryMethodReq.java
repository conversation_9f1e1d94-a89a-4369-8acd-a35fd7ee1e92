package com.sankuai.shangou.logistics.sdms.sdk.config.dto.request;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @email <EMAIL>
 */
@Data
public class BatchUpdatePoiDeliveryMethodReq {

    @FieldDoc(
            description = "操作的门店列表",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public List<Long> poiIds;

    @FieldDoc(
            description = "是否开启了转单：1-开启，0-未开启",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(value = 2)
    public Integer enableTurnDelivery;

}
