package com.sankuai.shangou.logistics.sdms.sdk.config;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.response.TPageResult;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.sdms.sdk.common.TContextInfo;
import com.sankuai.shangou.logistics.sdms.sdk.config.dto.DapUrlDTO;
import com.sankuai.shangou.logistics.sdms.sdk.config.dto.SelfDeliveryPoiConfigDTO;
import com.sankuai.shangou.logistics.sdms.sdk.config.dto.request.BatchUpdatePoiDeliveryMethodReq;
import com.sankuai.shangou.logistics.sdms.sdk.config.dto.request.QuerySelfDeliveryConfigListReq;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @email <EMAIL>
 */
@InterfaceDoc(
        displayName = "运单统计数据查询接口",
        type = "octo.thrift.annotation",
        scenarios = "运单统计数据查询接口",
        description = "运单统计数据查询接口",
        authors = {
                "yujing10"
        }
)
@ThriftService
public interface SelfDeliveryPoiConfigThriftService {

    @MethodDoc(
            displayName = "查询自配门店配置",
            description = "查询自配门店配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询自配门店配置",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询骑手维度运单统计信息返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TPageResult<SelfDeliveryPoiConfigDTO> querySelfDeliveryConfigList(TContextInfo tContextInfo, QuerySelfDeliveryConfigListReq req);

    @MethodDoc(
            displayName = "查询自配门店配置",
            description = "查询自配门店配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询自配门店配置",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询骑手维度运单统计信息返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<SelfDeliveryPoiConfigDTO> querySelfDeliveryConfig(@ThriftField(1) Long tenantId, @ThriftField(2) Long poiId);


    @MethodDoc(
            displayName = "查询自配门店配置",
            description = "查询自配门店配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询自配门店配置",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询骑手维度运单统计信息返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<DapUrlDTO> queryDapStoreConfigUrl(@ThriftField(1) Long tenantId, @ThriftField(2) Long poiId);


    @MethodDoc(
            displayName = "批量修改门店配送方式",
            description = "批量修改门店配送方式",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量修改门店配送方式",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询骑手维度运单统计信息返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<Void> batchUpdatePoiDeliveryMethod(TContextInfo tContextInfo, BatchUpdatePoiDeliveryMethodReq req);
}
