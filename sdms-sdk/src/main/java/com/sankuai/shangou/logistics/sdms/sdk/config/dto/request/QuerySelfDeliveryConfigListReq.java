package com.sankuai.shangou.logistics.sdms.sdk.config.dto.request;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.request.TPageRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @email <EMAIL>
 */
@Data
public class QuerySelfDeliveryConfigListReq {

    @FieldDoc(
            description = "是否开启了向青云转单：1-开启，0-未开启",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.OPTIONAL)
    public Integer enableTurnToDap;

    @FieldDoc(
            description = "组织架构ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(value = 2, requiredness = ThriftField.Requiredness.OPTIONAL)
    public List<Long> depIdList;

    @FieldDoc(
            description = "门店id列表",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(value = 3, requiredness = ThriftField.Requiredness.OPTIONAL)
    public List<Long> filterPoiList;

    @FieldDoc(
            description = "分页请求",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public TPageRequest tPageRequest;

}
