package com.sankuai.shangou.logistics.sdms.sdk.verify.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/7/8 19:53
 **/
@Data
@ThriftStruct
public class QueryVerifyTaskRequest {
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long riderAccountId;

    public String validate() {

        if (this.getRiderAccountId() == null) {
            return "骑手账号id为空";
        }

        return null;
    }

}
