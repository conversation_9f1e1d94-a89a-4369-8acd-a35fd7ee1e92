package com.sankuai.shangou.logistics.sdms.sdk.verify.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/22 15:03
 **/
@ThriftStruct
@Data
public class SearchVerifyTaskRequest {
    @ThriftField(1)
    public List<Long> storeIdList;

    @ThriftField(2)
    public Long riderAccountId;

    @ThriftField(3)
    public LocalDateTime pushTimeBegin;

    @ThriftField(4)
    public LocalDateTime pushTimeEnd;

    @ThriftField(5)
    public List<Integer> statusList;

    @ThriftField(6)
    public Long tenantId;

    @ThriftField(7)
    public Integer page;

    @ThriftField(8)
    public Integer pageSize;

    @ThriftField(9)
    public Long taskId;

    @ThriftField(10)
    public Boolean faceIdentifyResult;

    @ThriftField(11)
    public Boolean helmetIdentifyResult;

    @ThriftField(12)
    public Boolean dressingIdentifyResult;


    public String validate() {
        if (pushTimeBegin == null || pushTimeEnd == null) {
            return "下发时间范围不能为空";
        }

        if (tenantId == null) {
            return "租户id不能为空";
        }

        if (page == null || pageSize == null) {
            return "分页参数为空";
        }

        if (CollectionUtils.isEmpty(storeIdList)) {
            return "门店列表不能为空";
        }

        if (pushTimeBegin.plusMonths(3).isBefore(pushTimeEnd)) {
            return "下发时间范围不能超过3个月";
        }

        return null;
    }
}
