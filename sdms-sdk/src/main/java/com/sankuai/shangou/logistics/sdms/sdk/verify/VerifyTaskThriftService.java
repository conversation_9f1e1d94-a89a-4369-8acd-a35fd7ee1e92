package com.sankuai.shangou.logistics.sdms.sdk.verify;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.response.TPageResult;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.todo.PoiAccountInfo;
import com.sankuai.shangou.logistics.sdms.sdk.config.dto.request.QueryByTaskIdRequest;
import com.sankuai.shangou.logistics.sdms.sdk.config.dto.request.QuerySelfDeliveryConfigListReq;
import com.sankuai.shangou.logistics.sdms.sdk.config.dto.request.QueryVerifyImageRequest;
import com.sankuai.shangou.logistics.sdms.sdk.verify.dto.*;
import com.sankuai.shangou.logistics.sdms.sdk.verify.request.AssignVerifyTaskRequest;
import com.sankuai.shangou.logistics.sdms.sdk.verify.request.GetAuthorizeCodeRequest;
import com.sankuai.shangou.logistics.sdms.sdk.verify.request.PostVerifyFailResultRequest;
import com.sankuai.shangou.logistics.sdms.sdk.verify.request.PostVerifySuccessResultRequest;
import com.sankuai.shangou.logistics.sdms.sdk.verify.request.QueryRiderEndTaskRequest;
import com.sankuai.shangou.logistics.sdms.sdk.verify.request.QueryVerifyTaskRequest;
import com.sankuai.shangou.logistics.sdms.sdk.verify.request.SearchVerifyTaskRequest;

/**
 * <AUTHOR>
 * @since 2024/7/11 21:17
 **/
@ThriftService
public interface VerifyTaskThriftService {
    @MethodDoc(
            displayName = "尝试下发验证任务",
            description = "尝试下发验证任务",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "尝试下发验证任务请求",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "尝试下发验证任务结果",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<TVerifyTaskInfo> tryToAssignVerifyTask(AssignVerifyTaskRequest request);


    @MethodDoc(
            displayName = "查询骑手进行中的验证任务",
            description = "查询骑手进行中的验证任务",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询骑手进行中的验证任务",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询骑手进行中的验证任务",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<TVerifyTaskInfo> queryTaskInfo(QueryVerifyTaskRequest request);

    @MethodDoc(
            displayName = "获取权限code",
            description = "获取权限code",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "获取权限code",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "获取权限code",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<AuthorizeCodeInfo> getAuthorizeCode(GetAuthorizeCodeRequest request);


    @MethodDoc(
            displayName = "上报识别结果",
            description = "上报识别结果",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "上报识别结果",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "上报识别结果",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<Void> postVerifySuccessResult(PostVerifySuccessResultRequest request);

    @MethodDoc(
            displayName = "上报识别结果",
            description = "上报识别结果",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "上报识别结果",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "上报识别结果",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<PostVerifyFailResponse> postVerifyFailResult(PostVerifyFailResultRequest request);


    @MethodDoc(
            displayName = "查询识别任务",
            description = "查询识别任务",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询识别任务",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询识别任务",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TPageResult<TVerifyTaskDTO> searchVerifyTask(SearchVerifyTaskRequest request);


    @MethodDoc(
            displayName = "查询验证图片,返回结果为base64格式的图片数据",
            description = "查询验证图片,返回结果为base64格式的图片数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询验证图片",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询识别任务",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<String> queryVerifyImageBase64(QueryVerifyImageRequest request);

    @MethodDoc(
            displayName = "通过任务id查询采集任务",
            description = "通过任务id查询采集任务",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "通过任务id查询采集任务",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "通过任务id查询采集任务",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<TVerifyTaskDTO> queryByTaskId(QueryByTaskIdRequest request);

    @MethodDoc(
            displayName = "查询骑手已结束的任务",
            description = "查询骑手已结束的任务",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询骑手已结束的任务",
                            type = QueryRiderEndTaskRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询骑手已结束的任务",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TPageResult<TVerifyEndTaskDTO> queryRiderEndTask(QueryRiderEndTaskRequest request);

    @MethodDoc(
            displayName = "查询骑手待采集任务数量",
            description = "查询骑手待采集任务数量",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询骑手待采集任务数量",
                            type = QueryVerifyTaskRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询骑手待采集任务数量",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<Integer> queryRiderDoingTaskCount(QueryVerifyTaskRequest request);

    @MethodDoc(
            displayName = "查询骑手待采集任务待办信息",
            description = "查询骑手待采集任务待办信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询骑手待采集任务待办信息",
                            type = PoiAccountInfo.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询骑手待采集任务待办信息",
            restExampleResponseData = "{\"taskType\":\"WM_DECORATION_TASK\",\"detailList\":[{\"taskCount\":4,\"delayTaskCount\":0,\"poiId\":********}]}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<String> queryRiderPendingTaskInfo(PoiAccountInfo poiAccountInfo);

    @MethodDoc(
            displayName = "查询骑手待采集的验证任务",
            description = "查询骑手待采集的验证任务",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询骑手待采集的验证任务",
                            type = QueryVerifyTaskRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询骑手进行中的验证任务",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<TVerifyPendingTaskInfo> queryPendingTaskInfo(QueryVerifyTaskRequest request);
}
