package com.sankuai.shangou.logistics.sdms.sdk.verify.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-03-26
 */
@Data
@ThriftStruct
public class TVerifyEndTaskDTO {
    @ThriftField(1)
    private Long taskId;

    @FieldDoc(description = "任务状态")
    @ThriftField(2)
    private Integer taskStatus;

    @FieldDoc(description = "下发时间")
    @ThriftField(3)
    private Long pushTaskTime;

    @FieldDoc(description = "过期时间")
    @ThriftField(4)
    private Long expireTime;

    @FieldDoc(description = "完成时间")
    @ThriftField(5)
    private Long completeTime;

    @FieldDoc(description = "采集模式")
    @ThriftField(6)
    private Integer collectMode;

    @FieldDoc(description = "人脸识别结果")
    @ThriftField(7)
    private Boolean faceIdentifyResult;

    @FieldDoc(description = "头盔识别结果")
    @ThriftField(8)
    private Boolean helmetIdentifyResult;

    @FieldDoc(description = "着装识别结果")
    @ThriftField(9)
    private Boolean dressingIdentifyResult;

    @FieldDoc(description = "审核状态")
    @ThriftField(10)
    private Integer verifyStatus;

    @FieldDoc(description = "标签列表")
    @ThriftField(11)
    private List<TLabelDTO> labels;
}
