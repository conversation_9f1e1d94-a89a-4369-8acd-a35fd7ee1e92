package com.sankuai.shangou.logistics.sdms.sdk.config;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.sdms.sdk.config.dto.request.QuerySelfDeliveryConfigListReq;

/**
 * <AUTHOR>
 * @date 2024-07-08
 * @email <EMAIL>
 */
@InterfaceDoc(
        displayName = "配送操作接口",
        type = "octo.thrift.annotation",
        scenarios = "运单统计数据查询接口",
        description = "运单统计数据查询接口",
        authors = {
                "yujing10"
        }
)
@ThriftService
public interface SdmsStoreConfigService {

    @MethodDoc(
            displayName = "查询考核配送时长",
            description = "查询考核配送时长",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询考核配送时长",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询考核配送时长",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<Integer> calcAssessDeliveryDuration(Long merchantId, Long warehouseId, String tradeOrderNo, Integer orderBizType, Long deliveryDistance);

    @MethodDoc(
            displayName = "查询考核配送时长",
            description = "查询考核配送时长",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询考核配送时长",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询考核配送时长",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<Integer> calcAssessDeliveryDurationBySeconds(Long merchantId, Long warehouseId, String tradeOrderNo, Integer orderBizType, Long deliveryDistance);


    @MethodDoc(
            displayName = "查询预定单下发时间",
            description = "查询预定单下发时间",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询考核配送时长",
                            type = QuerySelfDeliveryConfigListReq.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询考核配送时长",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<Long> calcPushDownTimestamp(Long merchantId, Long warehouseId, String tradeOrderNo, Integer orderBizType, Long deliveryDistance);


}
