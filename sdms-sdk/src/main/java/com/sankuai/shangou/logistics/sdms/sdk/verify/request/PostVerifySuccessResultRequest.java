package com.sankuai.shangou.logistics.sdms.sdk.verify.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/7/14 21:19
 **/
@ThriftStruct
@Data
public class PostVerifySuccessResultRequest {
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long taskId;

    @ThriftField(value = 2, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long tenantId;

    @ThriftField(value = 3, requiredness = ThriftField.Requiredness.REQUIRED)
    public String requestCode;

    @ThriftField(value = 4, requiredness = ThriftField.Requiredness.REQUIRED)
    public String responseCode;

    @ThriftField(value = 5, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long riderAccountId;

    @ThriftField(value = 6, requiredness = ThriftField.Requiredness.OPTIONAL)
    public String uuid;

    public String validate() {
        if (taskId == null) {
            return "任务id为空";
        }

        if(tenantId == null) {
            return "租户id为空";
        }

        if (StringUtils.isBlank(requestCode)) {
            return "requestCode为空";
        }

        if (StringUtils.isBlank(responseCode)) {
            return "responseCode为空";
        }

        return null;
    }
}
