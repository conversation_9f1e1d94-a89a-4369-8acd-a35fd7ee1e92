package com.sankuai.shangou.logistics.sdms.sdk.config.dto;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.logistics.sdms.sdk.common.TContextInfo;
import com.sankuai.shangou.logistics.sdms.sdk.common.TOperator;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @email <EMAIL>
 */
@Data
public class SelfDeliveryPoiConfigDTO {

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long poiId;

    @FieldDoc(
            description = "门店名称",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String poiName;

    @FieldDoc(
            description = "是否支持转青云",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Integer enableTurnDelivery;

    @FieldDoc(
            description = "操作人信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public TOperator tOperator;

}
