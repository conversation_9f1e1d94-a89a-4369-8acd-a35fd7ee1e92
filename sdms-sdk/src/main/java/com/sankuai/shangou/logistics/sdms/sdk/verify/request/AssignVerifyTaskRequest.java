package com.sankuai.shangou.logistics.sdms.sdk.verify.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/7/8 19:53
 **/
@Data
@ThriftStruct
public class AssignVerifyTaskRequest {
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    private Long tenantId;

    @ThriftField(value = 2, requiredness = ThriftField.Requiredness.REQUIRED)
    private Long storeId;

    @ThriftField(value = 3, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long deliveryOrderId;

    @ThriftField(value = 4, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long riderAccountId;

    @ThriftField(value = 5, requiredness = ThriftField.Requiredness.REQUIRED)
    public String viewOrderId;

    @ThriftField(value = 6, requiredness = ThriftField.Requiredness.REQUIRED)
    public Integer orderBizType;

    @ThriftField(value = 7, requiredness = ThriftField.Requiredness.OPTIONAL)
    public Long riderEmpId;

    public String validate() {
        if (this.getTenantId() == null) {
            return "租户id为空";
        }

        if (this.getStoreId() == null) {
            return "门店id为空";
        }

        if (this.getDeliveryOrderId() == null) {
            return "运单id为空";
        }

        if (this.getRiderAccountId() == null) {
            return "骑手账号id为空";
        }

        if (this.getViewOrderId() == null) {
            return "订单id为空";
        }

        if (this.getOrderBizType() == null) {
            return "订单业务类型为空";
        }

        return null;
    }
}
