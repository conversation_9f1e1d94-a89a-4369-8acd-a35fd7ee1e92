package com.sankuai.shangou.logistics.sdms.sdk.verify.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/7/14 21:19
 **/
@ThriftStruct
@Data
public class PostVerifyFailResultRequest {
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long taskId;

    @ThriftField(value = 2, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long tenantId;

    @ThriftField(value = 3, requiredness = ThriftField.Requiredness.REQUIRED)
    public String requestCode;

    @ThriftField(value = 4, requiredness = ThriftField.Requiredness.REQUIRED)
    public String userName;

    @ThriftField(value = 5, requiredness = ThriftField.Requiredness.REQUIRED)
    public String errCode;

    @ThriftField(value = 6, requiredness = ThriftField.Requiredness.REQUIRED)
    public String errMsg;

    @ThriftField(value = 7, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long riderAccountId;

    @ThriftField(value = 8, requiredness = ThriftField.Requiredness.REQUIRED)
    public Long employeeId;

    @ThriftField(value = 9, requiredness = ThriftField.Requiredness.REQUIRED)
    public String userAgent ;

    @ThriftField(value = 10, requiredness = ThriftField.Requiredness.REQUIRED)
    public String uuid ;


    @ThriftField(value = 11, requiredness = ThriftField.Requiredness.REQUIRED)
    public String ip;

    public String validate() {
        if (this.getTenantId() == null) {
            return "租户id为空";
        }

        if (this.getTaskId() == null) {
            return "门店id为空";
        }

        if (this.getIp() == null) {
            return "ip地址为空";
        }

        if (this.getRiderAccountId() == null) {
            return "骑手账号id为空";
        }

        if (StringUtils.isBlank(this.getUuid())) {
            return "uuid为空";
        }

        if (this.getEmployeeId() == null) {
            return "员工id为空";
        }

        if (StringUtils.isBlank(this.getUserName())) {
            return "用工名为空";
        }

        if (StringUtils.isBlank(this.getUserAgent())) {
            return "userAgent为空";
        }

        if (StringUtils.isBlank(errCode)) {
            return "错误码为空";
        }

        return null;
    }
}
