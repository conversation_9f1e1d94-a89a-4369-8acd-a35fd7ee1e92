package com.sankuai.shangou.logistics.delivery.monitoring.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 骑手的用于监控的运单信息 VO.
 *
 * <AUTHOR>
 * @since 2021/12/3 18:28
 */
@TypeDoc(
        description = "骑手监控大屏-运单信息"
)
@ApiModel("骑手监控大屏-运单信息")
@Data
@Builder
public class RiderDeliveryOrderMonitoringVO {

    @FieldDoc(
            description = "运单号"
    )
    @ApiModelProperty(value = "运单号", required = true)
    private String deliveryOrderId;

    @FieldDoc(
            description = "赋能订单号"
    )
    @ApiModelProperty(value = "赋能订单号", required = true)
    private String orderId;

    @FieldDoc(
            description = "运单状态 30-待领取 40-拣货中 50-配送中"
    )
    @ApiModelProperty(value = "运单状态", required = true)
    private Integer orderShowStatus;

    @FieldDoc(
            description = "运单状态描述"
    )
    @ApiModelProperty(value = "运单状态描述", required = true)
    private String orderShowStatusDesc;

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "租户ID", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private Long shopId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道名称", required = true)
    private String channelName;

    @FieldDoc(
            description = "订单流水", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单流水", required = true)
    private String serialNo;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道订单号", required = true)
    private String channelOrderId;

    @FieldDoc(
            description = "预计送达时间开始时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "预计送达时间开始时间", required = true)
    private Long estimateArriveTimeStart;

    @FieldDoc(
            description = "预计送达时间截止时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "预计送达时间截止时间", required = true)
    private Long estimateArriveTimeEnd;

    @FieldDoc(
            description = "运单创建时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "运单创建时间戳", required = true)
    private Long createTime;

    @FieldDoc(
            description = "运单当前状态超时时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "运单当前状态超时时间戳", required = true)
    private Long currentStatusTimeoutStamp;

    @FieldDoc(
            description = "运单整单超时时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "运单整单超时时间戳", required = true)
    private Long entireOrderTimeoutStamp;

    // 收货人信息 start

    @FieldDoc(
            description = "收货人姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人姓名", required = true)
    private String receiverName;

    @FieldDoc(
            description = "收货人定位经纬度", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人定位经纬度", required = true)
    private DeliveryCoordinateVO receiverLocation;

    // 收货人信息 end

    @FieldDoc(
            description = "渠道code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道code", required = true)
    public Integer deliveryPlatformCode;

    @FieldDoc(
            description = "承运商名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "承运商名称", required = true)
    public String deliveryChannelName;

    @FieldDoc(
            description = "", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "承运商名称", required = true)
    public Integer thirdPartDeliveryStatus;

    @FieldDoc(
            description = "考核送达截止时间", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveDeadline;

    @FieldDoc(
            description = "考核送达剩余时间", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveLeftTime;

    @FieldDoc(
            description = "考核送达超时时间", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveTimeout;

    @FieldDoc(
            description = "是否拣配分离", requiredness = Requiredness.OPTIONAL
    )
    private Boolean isPickDeliverySplit;
}
