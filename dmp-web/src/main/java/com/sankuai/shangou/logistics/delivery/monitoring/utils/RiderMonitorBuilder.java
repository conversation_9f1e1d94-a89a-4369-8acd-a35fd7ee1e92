package com.sankuai.shangou.logistics.delivery.monitoring.utils;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderItemVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OnlineGiftVO;
import com.meituan.shangou.saas.order.management.client.enums.ChannelType;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.utils.AddressSceneConvertUtils;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TReceiver;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrderMonitorInfo;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderMonitorInfo;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TStaffRider;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.config.LionConfigUtils;
import com.sankuai.shangou.logistics.delivery.monitoring.enums.DeliveryMonitoringOrderTagEnum;
import com.sankuai.shangou.logistics.delivery.monitoring.enums.OrderShowStatusEnum;
import com.sankuai.shangou.logistics.delivery.monitoring.enums.RiderLocatingExceptionVOEnum;
import com.sankuai.shangou.logistics.delivery.monitoring.enums.RiderShowStatusEnum;
import com.sankuai.shangou.logistics.delivery.monitoring.vo.*;
import com.sankuai.shangou.logistics.delivery.monitoring.wrapper.OfwClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 骑手大屏 VO 构造器.
 *
 * <AUTHOR>
 * @since 2021/12/7 19:36
 */
@Slf4j
public class RiderMonitorBuilder {

    /**
     * 经纬度坐标的精度
     */
    private static final int COORDINATE_PRECISION = 6;
    /**
     * 默认字符串
     */
    private static final String DEFAULT_STRING = "-";

    /**
     * 会员订单类型
     */
    private static final Integer MEMBER_USER_TYPE = 15;

    /**
     * 预定单
     */
    private static final int BOOKING_ORDER = 1;


    /**
     * 组装门店骑手监控信息响应.
     *
     * @param orderList 运单列表
     * @param riderList 骑手列表
     * @return 门店监控信息响应
     */
    public static QueryShopDeliveryMonitoringResponse buildShopDeliveryMonitoringResp(List<TRiderDeliveryOrderMonitorInfo> orderList,
                                                                                      List<TRiderMonitorInfo> riderList,
                                                                                      List<OCMSOrderVO> ocmsOrderVOList,
                                                                                      boolean filterThirdDelivery,
                                                                                      Map<OfwClient.OrderKey, Boolean> sealTagMap) {
        QueryShopDeliveryMonitoringResponse response = new QueryShopDeliveryMonitoringResponse();

        Map<Integer, List<RiderDeliveryOrderMonitoringVO>> orderListMap = transformDeliveryOrderList(orderList, filterThirdDelivery, ocmsOrderVOList);
        Map<Integer, List<RiderDeliveryInfoVO>> riderListMap = transformRiderList(riderList, filterThirdDelivery, ocmsOrderVOList);
        List<OrderInfoVO> orderInfo = transformOCMSOrderList(ocmsOrderVOList, sealTagMap);

        response.setOrderStatus(orderListMap);
        response.setRiderStatus(riderListMap);
        response.setOrderInfo(orderInfo);
        
        return response;
    }

    /**
     * 取订单信息
     *
     */
    private static List<OrderInfoVO> transformOCMSOrderList(List<OCMSOrderVO> ocmsOrderVOList,
                                                            Map<OfwClient.OrderKey, Boolean> sealTagMap) {
        return Optional.ofNullable(ocmsOrderVOList).orElse(Collections.emptyList())
                .stream()
                .filter(OrderInfoVO -> Objects.nonNull(OrderInfoVO.getOrderId()))
                .map(
                        ocmsOrderVO -> {
                            OrderInfoVO orderInfoVO = new OrderInfoVO();
                            orderInfoVO.setOrderUserType(ocmsOrderVO.getUserType());
                            orderInfoVO.setOrderId(String.valueOf(ocmsOrderVO.getOrderId()));
                            orderInfoVO.setGifts(transformProductsFromGift(ocmsOrderVO.getOnlineGiftVOS()));
                            orderInfoVO.setProducts(transformProductsFromItem(ocmsOrderVO.getOcmsOrderItemVOList()));
                            orderInfoVO.setOrderTagList(transformOrderTagEnum(ocmsOrderVO, sealTagMap));
                            orderInfoVO.setSortedTagList(SortedOrderTagVO.buildListOfMng(ocmsOrderVO.getSortedTagList()));
                            return orderInfoVO;
                        }
                ).collect(Collectors.toList());
    }

    private static List<ProductVO> transformProductsFromGift(List<OnlineGiftVO> giftVOList) {
        return Optional.ofNullable(giftVOList).orElse(Collections.emptyList())
                .stream()
                .map(giftVO -> new ProductVO(giftVO.getGiftName(), null, null, giftVO.getGiftQuantity())
                ).collect(Collectors.toList());
    }

    private static List<Integer> transformOrderTagEnum(OCMSOrderVO ocmsOrderVO,
                                                       Map<OfwClient.OrderKey, Boolean> sealTagMap) {

        List<Integer> orderTagList = new ArrayList<>();

        //会员标签
        if (Objects.equals(ocmsOrderVO.getUserType(), MEMBER_USER_TYPE)) {
            orderTagList.add(DeliveryMonitoringOrderTagEnum.MEMBER.getCode());
        }

        //封签标签
        OfwClient.OrderKey orderKey = new OfwClient.OrderKey(ocmsOrderVO.getViewOrderId(), ocmsOrderVO.getOrderBizType());
        if (sealTagMap.containsKey(orderKey) && Objects.equals(sealTagMap.get(orderKey), true)) {
            orderTagList.add(DeliveryMonitoringOrderTagEnum.SEAL.getCode());
        }

        //餐馆标签
        if (Objects.nonNull(ocmsOrderVO.getOcmsDeliveryInfoVO())
                && StringUtils.isNotBlank(ocmsOrderVO.getOcmsDeliveryInfoVO().getCategory())
                && MccUtils.isDhScenePoi(ocmsOrderVO.getShopId())
                && isRestaurant(buildScene(ocmsOrderVO.getOcmsDeliveryInfoVO().getCategory()))) {
            orderTagList.add(DeliveryMonitoringOrderTagEnum.RESTAURANT.getCode());
        }

        if (Objects.nonNull(ocmsOrderVO.getIsFacaiWine()) && ocmsOrderVO.getIsFacaiWine()) {
            orderTagList.add(DeliveryMonitoringOrderTagEnum.FACAI.getCode());
        }

        return orderTagList;
    }

    private static List<ProductVO> transformProductsFromItem(List<OCMSOrderItemVO> ocmsOrderItemVOList) {
        return Optional.ofNullable(ocmsOrderItemVOList).orElse(Collections.emptyList())
                .stream().
                map(orderItem -> new ProductVO(orderItem.getSkuName(),
                        orderItem.getCustomerSkuId(),
                        orderItem.getSpecification(),
                        orderItem.getQuantity())
                ).collect(Collectors.toList());
    }

    /**
     * 将运单拆分为三组: 待领取/捡货中/配送中.
     *
     * @param orderList 门店所有活跃运单列表
     * @return 分组后的运单列表
     */
    private static Map<Integer, List<RiderDeliveryOrderMonitoringVO>> transformDeliveryOrderList(List<TRiderDeliveryOrderMonitorInfo> orderList, boolean filterThirdDelivery, List<OCMSOrderVO> ocmsOrderVOList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return new HashMap<>();
        }
        Map<String, OCMSOrderVO> ocmsOrdermap = IListUtils.nullSafeAndOverrideCollectToMap(ocmsOrderVOList, OCMSOrderVO::getViewOrderId, Function.identity());
        HashMap<Integer, List<RiderDeliveryOrderMonitoringVO>> orderStatusMap = new HashMap<>();

        orderStatusMap.put(OrderShowStatusEnum.WAIT_TAKE.getCode(),
                getOrderMonitoringVOList(orderList, OrderShowStatusEnum.WAIT_TAKE, ocmsOrdermap));
        orderStatusMap.put(OrderShowStatusEnum.PICKING.getCode(),
                getOrderMonitoringVOList(orderList, OrderShowStatusEnum.PICKING, ocmsOrdermap));
        orderStatusMap.put(OrderShowStatusEnum.IN_DELIVERY.getCode(),
                getOrderMonitoringVOList(orderList, OrderShowStatusEnum.IN_DELIVERY, ocmsOrdermap));
        if (!filterThirdDelivery) {
            orderStatusMap.getOrDefault(OrderShowStatusEnum.WAIT_TAKE.getCode(), Lists.newArrayList())
                    .addAll(getThirdOrderMonitoringVOList(orderList, OrderShowStatusEnum.WAIT_TAKE, ocmsOrdermap));
            orderStatusMap.getOrDefault(OrderShowStatusEnum.PICKING.getCode(), Lists.newArrayList())
                    .addAll(getThirdOrderMonitoringVOList(orderList, OrderShowStatusEnum.PICKING, ocmsOrdermap));
            orderStatusMap.getOrDefault(OrderShowStatusEnum.IN_DELIVERY.getCode(), Lists.newArrayList())
                    .addAll(getThirdOrderMonitoringVOList(orderList, OrderShowStatusEnum.IN_DELIVERY, ocmsOrdermap));
        }

        return orderStatusMap;

    }

    /**
     * 获取运单展示状态对应的运单VO列表
     *
     * @param orderList           运单列表
     * @param orderShowStatusEnum 运单展示状态枚举
     * @return 运单展示状态对应的运单VO列表
     */
    private static List<RiderDeliveryOrderMonitoringVO> getOrderMonitoringVOList(List<TRiderDeliveryOrderMonitorInfo> orderList,
                                                                                 OrderShowStatusEnum orderShowStatusEnum,
                                                                                 Map<String, OCMSOrderVO> ocmsOrdermap) {
        return orderList.stream()
                .filter(Objects::nonNull)
                .filter(item -> Objects.equals(item.getDeliveryPlatformCode(), DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode()))
                .filter(item -> mapOrderStatus(item).getCode() == orderShowStatusEnum.getCode())
                .map(tRiderDeliveryOrderMonitorInfo -> transform(tRiderDeliveryOrderMonitorInfo, ocmsOrdermap.get(tRiderDeliveryOrderMonitorInfo.getChannelOrderId())))
                .sorted((o1, o2) -> {
                    try {
                        //都用期望的*送达时间*来比较
                        long compareKey1 = Objects.nonNull(o1.getEvaluateArriveDeadline()) ? o1.getEvaluateArriveDeadline() : o1.getEstimateArriveTimeStart();
                        long compareKey2 = Objects.nonNull(o2.getEvaluateArriveDeadline()) ? o2.getEvaluateArriveDeadline() : o2.getEstimateArriveTimeStart();
                        return (int) (compareKey1 - compareKey2);
                    } catch (Exception e) {
                        log.error("delivery order compare error", e);
                        Cat.logEvent("DELIVERY_ORDER", "COMPARE_ERROR");
                        return (int) (o1.getEstimateArriveTimeStart() - o2.getEstimateArriveTimeStart());
                    }
                })
                .collect(Collectors.toList());
    }


    /**
     * 获取运单展示状态对应的运单VO列表
     *
     * @param orderList           运单列表
     * @param orderShowStatusEnum 运单展示状态枚举
     * @return 运单展示状态对应的运单VO列表
     */
    private static List<RiderDeliveryOrderMonitoringVO> getThirdOrderMonitoringVOList(List<TRiderDeliveryOrderMonitorInfo> orderList,
                                                                                      OrderShowStatusEnum orderShowStatusEnum,
                                                                                      Map<String, OCMSOrderVO> ocmsOrdermap) {
        return orderList.stream()
                .filter(Objects::nonNull)
                .filter(item -> !Objects.equals(item.getDeliveryPlatformCode(), DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode()))
                .filter(item -> mapThirdOrderStatus(item).getCode() == orderShowStatusEnum.getCode())
                .map(tRiderDeliveryOrderMonitorInfo -> transform(tRiderDeliveryOrderMonitorInfo, ocmsOrdermap.get(tRiderDeliveryOrderMonitorInfo.getChannelOrderId())))
                .sorted(Comparator.comparingLong(RiderDeliveryOrderMonitoringVO::getEstimateArriveTimeStart))
                .collect(Collectors.toList());
    }

    /**
     * 将骑手拆分为两组:履约中/空闲中.
     *
     * @param riderList 骑手列表
     * @return 履约中/空闲中 的骑手列表
     */
    private static Map<Integer, List<RiderDeliveryInfoVO>> transformRiderList(List<TRiderMonitorInfo> riderList, boolean filterThirdDelivery, List<OCMSOrderVO> ocmsOrderVOList) {
        if (CollectionUtils.isEmpty(riderList)) {
            return new HashMap<>();
        }
        Map<String, OCMSOrderVO> ocmsOrdermap = IListUtils.nullSafeAndOverrideCollectToMap(ocmsOrderVOList, OCMSOrderVO::getViewOrderId, Function.identity());
        HashMap<Integer, List<RiderDeliveryInfoVO>> riderStatusMap = new HashMap<>();
        List<RiderDeliveryInfoVO> idleRiderList = riderList.stream()
                .filter(item -> CollectionUtils.isEmpty(item.getOrderInfoList()))
                .map(tRiderMonitorInfo -> transform(tRiderMonitorInfo, ocmsOrdermap))
                .filter(Objects::nonNull)
                .sorted(RiderMonitorBuilder::compare)
                .collect(Collectors.toList());

        List<RiderDeliveryInfoVO> busyRiderList = riderList.stream()
                .filter(item -> CollectionUtils.isNotEmpty(item.getOrderInfoList()))
                .filter(item -> Objects.isNull(item.getOrderInfoList().get(0).getDeliveryPlatformCode())
                        || Objects.equals(item.getOrderInfoList().get(0).getDeliveryPlatformCode(), DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode()))
                .map(tRiderMonitorInfo -> transform(tRiderMonitorInfo, ocmsOrdermap))
                .filter(Objects::nonNull)
                .sorted(RiderMonitorBuilder::compare)
                .collect(Collectors.toList());

        List<RiderDeliveryInfoVO> thirdBusyList = Lists.newArrayList();
        if (!filterThirdDelivery) {
            thirdBusyList = riderList.stream()
                    .filter(item -> CollectionUtils.isNotEmpty(item.getOrderInfoList()))
                    .filter(item -> Objects.nonNull(item.getOrderInfoList().get(0).getDeliveryPlatformCode())
                            && !Objects.equals(item.getOrderInfoList().get(0).getDeliveryPlatformCode(), DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode()))
                    .map(tRiderMonitorInfo -> transform(tRiderMonitorInfo, ocmsOrdermap))
                    .filter(Objects::nonNull)
                    .sorted(RiderMonitorBuilder::compare)
                    .collect(Collectors.toList());
        }

        riderStatusMap.put(RiderShowStatusEnum.IN_IDLE.getCode(), idleRiderList);
        riderStatusMap.put(RiderShowStatusEnum.IN_BUSY.getCode(), busyRiderList);
        riderStatusMap.put(RiderShowStatusEnum.THIRD_PARTY.getCode(), thirdBusyList);
        return riderStatusMap;

    }

    /**
     * TRiderMonitorInfo 转 RiderDeliveryInfoVO.
     *
     * @param tRiderMonitorInfo 骑手监控信息
     * @return RiderDeliveryInfoVO
     */
    private static RiderDeliveryInfoVO transform(TRiderMonitorInfo tRiderMonitorInfo, Map<String, OCMSOrderVO> ocmsOrdermap) {
        if (Objects.isNull(tRiderMonitorInfo)) {
            return null;
        }
        RiderShowStatusEnum statusEnum;
        List<RiderDeliveryOrderMonitoringVO> relationOrderList;
        if (CollectionUtils.isNotEmpty(tRiderMonitorInfo.getOrderInfoList())) {
            relationOrderList = tRiderMonitorInfo.getOrderInfoList()
                    .stream()
                    .map(tRiderDeliveryOrderMonitorInfo -> transform(tRiderDeliveryOrderMonitorInfo, ocmsOrdermap.get(tRiderDeliveryOrderMonitorInfo.getChannelOrderId())))
                    .collect(Collectors.toList());

            statusEnum = RiderShowStatusEnum.IN_BUSY;
        } else {
            relationOrderList = new ArrayList<>();
            statusEnum = RiderShowStatusEnum.IN_IDLE;
        }

        DeliveryCoordinateVO location = new DeliveryCoordinateVO();
        if (Objects.nonNull(tRiderMonitorInfo.getLocation())) {
            location.setLatitude(formatCoordinate(tRiderMonitorInfo.getLocation().getLatitude()));
            location.setLongitude(formatCoordinate(tRiderMonitorInfo.getLocation().getLongitude()));
        }

        return RiderDeliveryInfoVO.builder()
                .riderName(tRiderMonitorInfo.getStaffRider().getName())
                .riderPhone(tRiderMonitorInfo.getStaffRider().getPhone())
                .riderAccountId(tRiderMonitorInfo.getStaffRider().getAccountId())
                .riderStatus(statusEnum.getCode())
                .riderStatusDesc(statusEnum.getDesc())
                .orderInfoList(relationOrderList)
                .riderLocation(location)
                .riderLocatingExceptionVO(transform(tRiderMonitorInfo.getLocatingExceptionType()))
                .tempRiderFlag(isTempRider(tRiderMonitorInfo.getStaffRider()))
                .build();
    }

    /**
     * TRiderDeliveryOrderMonitorInfo 转 RiderDeliveryOrderMonitoringVO.
     *
     * @param tRiderDeliveryOrder 运单监控信息
     * @return RiderDeliveryOrderMonitoringVO
     */
    private static RiderDeliveryOrderMonitoringVO transform(TRiderDeliveryOrderMonitorInfo tRiderDeliveryOrder, OCMSOrderVO ocmsOrderVO) {
        if (tRiderDeliveryOrder == null) {
            return null;
        }
        Integer channelId = ChannelOrderConvertUtils.sourceBiz2Mid(tRiderDeliveryOrder.getOrderBizTypeCode());
        String channelName = getChannelNameByChannelId(channelId);

        TReceiver receiver = tRiderDeliveryOrder.getReceiver();
        DeliveryCoordinateVO receiverLocation = null;
        String receiverName = DEFAULT_STRING;
        if (Objects.nonNull(receiver)) {
            receiverLocation = new DeliveryCoordinateVO();
            receiverLocation.setLatitude(formatCoordinate(receiver.getLatitude()));
            receiverLocation.setLongitude(formatCoordinate(receiver.getLongitude()));
            receiverName = receiver.getReceiverName();
        }

        String serialNo = DEFAULT_STRING + DEFAULT_STRING;
        OrderShowStatusEnum orderShowStatusEnum;
        if (Objects.equals(tRiderDeliveryOrder.getDeliveryPlatformCode(), DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode())) {
            orderShowStatusEnum = OrderShowStatusEnum.mapRiderDeliveryStatusEnum(tRiderDeliveryOrder.getDeliveryStatus());
        } else {
            orderShowStatusEnum = OrderShowStatusEnum.mapThirdRiderDeliveryStatusEnum(DeliveryStatusEnum.valueOf(tRiderDeliveryOrder.getDeliveryStatus()));
        }
        if (Objects.nonNull(tRiderDeliveryOrder.getDaySeq()) && tRiderDeliveryOrder.getDaySeq() > 0 ) {
            serialNo = tRiderDeliveryOrder.getDaySeq().toString();
        }
        Long evaluateArriveDeadline = null;
        Long evaluateArriveLeftTime = null;
        Long evaluateArriveTimeout = null;
        if (Objects.nonNull(ocmsOrderVO)) {
            if (Objects.nonNull(tRiderDeliveryOrder.getAssessDeliveryTime())) {
                //新逻辑，考核时间与eta脱钩
                evaluateArriveDeadline = tRiderDeliveryOrder.getAssessDeliveryTime();
            } else {
                //原逻辑
                if (Objects.equals(ocmsOrderVO.getIsBooking(), BOOKING_ORDER)) {
                    if (LionConfigUtils.isNewPreOrderAssessGrayStore(tRiderDeliveryOrder.getStoreId())) {
                        // 预订单：考核时间=预计送达时间+5分钟
                        evaluateArriveDeadline = tRiderDeliveryOrder.getEstimatedDeliveryEndTime() +  LionConfigUtils.preOrderAssessTimePlusMills();
                    } else {
                        // 预订单：考核时间=预计送达时间+5分钟
                        evaluateArriveDeadline = tRiderDeliveryOrder.getEstimatedDeliveryEndTime() + (5 * 60 * 1000);
                    }
                } else {
                    // 实时单：考核时间=支付时间+25分钟
                    evaluateArriveDeadline = ocmsOrderVO.getPayTime() + (25 * 60 * 1000);
                }
            }
            long tsNow = System.currentTimeMillis();
            if (tsNow > evaluateArriveDeadline) {
                // 配送中，已经超时
                evaluateArriveLeftTime = 0L;
                evaluateArriveTimeout = (tsNow - evaluateArriveDeadline);
            } else {
                // 配送中，还未超时
                evaluateArriveLeftTime = (evaluateArriveDeadline - tsNow);
                evaluateArriveTimeout = 0L;
            }
        }

        return RiderDeliveryOrderMonitoringVO.builder()
                .deliveryOrderId(tRiderDeliveryOrder.getDeliveryOrderId().toString())
                .orderId(tRiderDeliveryOrder.getOrderId().toString())
                .tenantId(tRiderDeliveryOrder.getTenantId())
                .shopId(tRiderDeliveryOrder.getStoreId())
                .channelId(channelId)
                .channelName(channelName)
                .channelOrderId(tRiderDeliveryOrder.getChannelOrderId())
                .estimateArriveTimeEnd(tRiderDeliveryOrder.getEstimatedDeliveryEndTime())
                .estimateArriveTimeStart(tRiderDeliveryOrder.getEstimatedDeliveryTime())
                .receiverName(receiverName)
                .receiverLocation(receiverLocation)
                .createTime(tRiderDeliveryOrder.getCreateTime())
                .orderShowStatus(orderShowStatusEnum.getCode())
                .orderShowStatusDesc(orderShowStatusEnum.getDesc())
                .serialNo(serialNo)
                .currentStatusTimeoutStamp(tRiderDeliveryOrder.getCurrentStatusTimeoutStamp())
                .entireOrderTimeoutStamp(tRiderDeliveryOrder.getEntireOrderTimeoutStamp())
                .deliveryPlatformCode(tRiderDeliveryOrder.getDeliveryPlatformCode())
                .deliveryChannelName(tRiderDeliveryOrder.getDeliveryChannelName())
                .thirdPartDeliveryStatus(tRiderDeliveryOrder.getDeliveryStatus())
                .evaluateArriveDeadline(evaluateArriveDeadline)
                .evaluateArriveLeftTime(evaluateArriveLeftTime)
                .evaluateArriveTimeout(evaluateArriveTimeout)
                .isPickDeliverySplit(tRiderDeliveryOrder.getPickDeliverySplitTag())

                .build();

    }

    /**
     * 映射运单状态
     *
     * @param tRiderDeliveryOrder 运单信息
     * @return OrderShowStatusEnum 运单状态展示枚举
     */
    private static OrderShowStatusEnum mapOrderStatus(TRiderDeliveryOrderMonitorInfo tRiderDeliveryOrder) {

        RiderDeliveryStatusEnum riderDeliveryStatusEnum =
                RiderDeliveryStatusEnum.enumOf(tRiderDeliveryOrder.getDeliveryStatus());

        return OrderShowStatusEnum.mapRiderDeliveryStatusEnum(riderDeliveryStatusEnum);
    }

    private static OrderShowStatusEnum mapThirdOrderStatus(TRiderDeliveryOrderMonitorInfo tRiderDeliveryOrder) {


        DeliveryStatusEnum deliveryStatusEnum = DeliveryStatusEnum.valueOf(tRiderDeliveryOrder.getDeliveryStatus());

        return OrderShowStatusEnum.mapThirdRiderDeliveryStatusEnum(deliveryStatusEnum);
    }

    private static String formatCoordinate(String source) {
        if (StringUtils.isBlank(source)) {
            return null;
        }

        return new BigDecimal(source)
                .setScale(COORDINATE_PRECISION, RoundingMode.HALF_UP)
                .toString();
    }

    private static RiderLocatingExceptionVO transform(Integer riderLocatingExceptionType){
        if (riderLocatingExceptionType == null) {
            return null;
        }

        RiderLocatingExceptionVOEnum exceptionVOEnum = RiderLocatingExceptionVOEnum.enumOf(riderLocatingExceptionType);

        if (exceptionVOEnum == null) {
            return null;
        }

        return new RiderLocatingExceptionVO(exceptionVOEnum.getValue(), exceptionVOEnum.getDesc1(), exceptionVOEnum.getDesc2());

    }

    /**
     * 通过渠道id得到渠道名称
     *
     * @param channelId 渠道id
     * @return 渠道名称
     */
    private static String getChannelNameByChannelId(Integer channelId) {
        if (channelId != null) {
            if (channelId.equals(ChannelType.ELEM.getValue())) {
                return "饿了么";
            }
            if (channelId.equals(ChannelType.JD2HOME.getValue())) {
                return "京东到家";
            }
            if (channelId.equals(ChannelType.MEITUAN.getValue())) {
                return "美团";
            }
            if (channelId.equals(ChannelType.MT_MEDICINE.getValue())) {
                return "美团医药";
            }
            if (channelId.equals(ChannelType.YOU_ZAN.getValue())) {
                return "有赞";
            }
            if (channelId.equals(ChannelType.MT_DRUNK_HORSE.getValue())) {
                return "微商城";
            }
        }

        return DEFAULT_STRING;
    }

    /**
     *  根据骑手定位状态排序:
     *  1.有定位的骑手优先于无定位的骑手;
     *  2.无定位的骑手内部:操作不规范>网络不佳>非履约时段 （操作不规范内部无特殊优先级）
     */
    private static int compare(RiderDeliveryInfoVO rider1, RiderDeliveryInfoVO rider2) {
        if (rider1 == null || rider2 == null) {
            return rider1 == null ? 1 : -1;
        }

        if (locationNonNull(rider1.getRiderLocation()) || locationNonNull(rider2.getRiderLocation())) {
            return compareByLocation(rider1,rider2);
        }

        return compareByException(rider1,rider2);
    }

    private static int compareByLocation(RiderDeliveryInfoVO rider1, RiderDeliveryInfoVO rider2) {
        if (locationNonNull(rider1.getRiderLocation()) && locationNonNull(rider2.getRiderLocation())) {
            return 0;
        }
        return locationNonNull(rider1.getRiderLocation())? -1 : 1;
    }

    private static int compareByException(RiderDeliveryInfoVO rider1, RiderDeliveryInfoVO rider2) {
        if (Objects.isNull(rider1.getRiderLocatingExceptionVO()) || Objects.isNull(rider2.getRiderLocatingExceptionVO())) {
            return 0;
        }

        if (StringUtils.equals(rider1.getRiderLocatingExceptionVO().getExceptionDesc1(),rider2.getRiderLocatingExceptionVO().getExceptionDesc1())) {
            return 0;
        }

        if (rider1.getRiderLocatingExceptionVO().getExceptionDesc1().equals("操作不规范") || rider2.getRiderLocatingExceptionVO().getExceptionDesc1().equals("操作不规范")){
            return rider1.getRiderLocatingExceptionVO().getExceptionDesc1().equals("操作不规范") ? -1 : 1;
        }

        if (rider1.getRiderLocatingExceptionVO().getExceptionDesc1().equals("网络不佳") || rider2.getRiderLocatingExceptionVO().getExceptionDesc1().equals("网络不佳")) {
            return rider1.getRiderLocatingExceptionVO().getExceptionDesc1().equals("网络不佳") ? -1 : 1;
        }

        return 0;
    }

    private static boolean locationNonNull(DeliveryCoordinateVO location) {
        return location != null && StringUtils.isNotBlank(location.getLatitude()) && StringUtils.isNotBlank(location.getLongitude());
    }

    /**
     * 判断账号是否为临时骑手账号
     * 临时账号的判断条件：账号类型为外部账号 && 角色包含临时骑手角色
     * @param tStaffRider 骑手信息
     * @return 是否为临时骑手账号
     */
    public static Boolean isTempRider(TStaffRider tStaffRider) {
        Objects.requireNonNull(tStaffRider);

        if (Objects.isNull(tStaffRider.getAccountType()) ||
                CollectionUtils.isEmpty(tStaffRider.getRoleIdList())) {
            return false;
        }

        return tStaffRider.getAccountType() == AccountTypeEnum.OUTSOURCING.getValue()
                && tStaffRider.getRoleIdList().stream()
                .anyMatch(roleId -> MccUtils.tempRiderRoleIds().contains(roleId.toString()));


    }

    private static String buildScene(String category) {
        try {
            return AddressSceneConvertUtils.convertCategory2Scene(category);
        } catch (Exception e) {
            log.error("buildScene error", e);
        }
        return null;
    }

    public static boolean isRestaurant(String scene) {
        return StringUtils.isNotBlank(scene) && MccUtils.getRestaurantSceneList().contains(scene);
    }
}
