package com.sankuai.shangou.logistics.delivery.config;


import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurityInterceptor;
import com.sankuai.shangou.commons.auth.login.app.AppLoginInterceptor;
import com.sankuai.shangou.commons.auth.login.web.WebLoginInterceptor;
import com.sankuai.shangou.logistics.delivery.config.interceptor.RegionSelectShippingBusinessInterceptor;
import com.sankuai.shangou.logistics.delivery.config.interceptor.RegionSelectWhiteListInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.config.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableWebMvc
@Profile("!local-test")
@ComponentScan("com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity")
public class WebMvcConfiguration extends WebMvcConfigurerAdapter {

    @Resource
    private WebLoginInterceptor webLoginInterceptor;

    @Resource
    private AppLoginInterceptor appLoginInterceptor;

    @Resource
    private RegionSelectShippingBusinessInterceptor regionSelectShippingBusinessInterceptor;

    @Resource
    private RegionSelectWhiteListInterceptor regionSelectWhiteListInterceptor;

    @Resource
    private DataSecurityInterceptor dataSecurityInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(appLoginInterceptor)
                        .addPathPatterns("/dmp/api/app/**");

        registry.addInterceptor(webLoginInterceptor)
                .addPathPatterns("/dmp/api/poi/**")
                .addPathPatterns("/dmp/api/board/**")
                .addPathPatterns("/dmp/api/shipping-area/**")
                .addPathPatterns("/dmp/api/web/**");

        registry.addInterceptor(regionSelectShippingBusinessInterceptor)
                .addPathPatterns("/dmp/api/shipping-area/**");

        registry.addInterceptor(regionSelectWhiteListInterceptor)
                .addPathPatterns("/dmp/api/shipping-area/user-indicator/get")
                .addPathPatterns("/dmp/api/shipping-area/people-portrait/get")
                .addPathPatterns("/dmp/api/shipping-area/map-aoi-classify/get");

        // 数据鉴权拦截器
        registry.addInterceptor(dataSecurityInterceptor)
                .addPathPatterns("/**");
        ;
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/**").addResourceLocations("classpath:/static/");
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }


    // 指定用Gson做序列化和反序列化
//    @Override
//    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
//        log.info("configureMessageConverters {}", converters);
//        converters.add(0, new GsonHttpMessageConverter());
//    }
//
//    @Override
//    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
//        log.info("extendMessageConverters {}", converters);
//    }
}

