package com.sankuai.shangou.logistics.delivery.questionnaire.convert;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDO;
import com.sankuai.shangou.logistics.delivery.questionnaire.vo.DeliveryOrderQuestionnaireVO;
import com.sankuai.shangou.logistics.delivery.questionnaire.vo.QuestionnaireVO;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/16 23:57
 **/
public class Converter {
    public static List<DeliveryOrderQuestionnaireVO> toDeliveryOrderQuestionnaireVO(List<DeliveryQuestionnaireDO> deliveryQuestionnaireDOList) {
        Map<Long, List<DeliveryQuestionnaireDO>> deliveryOrderQuestionnaireMap =
                deliveryQuestionnaireDOList.stream().collect(Collectors.groupingBy(DeliveryQuestionnaireDO::getDeliveryOrderId));


        return deliveryOrderQuestionnaireMap.entrySet().stream()
                .map(entry -> {
            DeliveryOrderQuestionnaireVO vo = new DeliveryOrderQuestionnaireVO();
            vo.setDeliveryOrderId(entry.getKey());
            vo.setQuestionnaireList(entry.getValue().stream().map(Converter::toQuestionnaireVO).collect(Collectors.toList()));

            return vo;
        }).collect(Collectors.toList());
    }

    public static QuestionnaireVO toQuestionnaireVO(DeliveryQuestionnaireDO deliveryQuestionnaireDO) {
        QuestionnaireVO vo = new QuestionnaireVO();
        vo.setAnswerListDesc(JsonUtil.fromJson(deliveryQuestionnaireDO.getAnswerOptionsSnapshot(), new TypeReference<List<String>>() {}));
        vo.setIsAnswered(!StringUtils.isBlank(deliveryQuestionnaireDO.getAnswer()));
        vo.setQuestionDesc(deliveryQuestionnaireDO.getQuestion());

        return vo;
    }
}
