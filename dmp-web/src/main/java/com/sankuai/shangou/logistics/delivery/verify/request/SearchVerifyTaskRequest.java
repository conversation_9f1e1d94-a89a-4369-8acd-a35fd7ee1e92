package com.sankuai.shangou.logistics.delivery.verify.request;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/22 19:18
 **/
@Data
public class SearchVerifyTaskRequest {
    private List<Long> storeIds;

    private String accountName;

    private Long taskPushTimeBegin;

    private Long taskPushTimeEnd;

    private List<Integer> statusList;

    private Integer page;

    private Integer pageSize;

    /**
     * 人脸验证结果 0-否 1-是
     */
    private Integer faceIdentifyResult;

    /**
     * 头盔验证结果 0-否 1-是
     */
    private Integer helmetIdentifyResult;

    /**
     * 着装验证结果 0-否 1-是
     */
    private Integer dressingIdentifyResult;

    /**
     * 任务id
     */
    private Long taskId;

    public String validate() {
        if (taskPushTimeBegin == null || taskPushTimeEnd == null) {
            return "任务下发时间范围不能为空";
        }

        if (page == null || pageSize == null) {
            return "分页参数不能为空";
        }

        if (CollectionUtils.isEmpty(storeIds)) {
            return "门店列表不能为空";
        }

        return null;
    }
}
