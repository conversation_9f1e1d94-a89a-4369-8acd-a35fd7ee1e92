package com.sankuai.shangou.logistics.delivery.config.interceptor;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meituan.shangou.sac.dto.model.SacPermissionDTO;
import com.meituan.shangou.sac.dto.request.search.QuerySacAccountPermissionRequest;
import com.meituan.shangou.sac.dto.response.SacStatus;
import com.meituan.shangou.sac.dto.response.search.QuerySacAccountPermissionResponse;
import com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService;
import com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder;
import com.sankuai.shangou.logistics.delivery.config.constant.CommonConstant;
import com.sankuai.shangou.logistics.delivery.config.constant.JsonMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.Optional;

/**
 * 业务权限拦截器
 */
@Slf4j
@Component
public class BusinessInterceptor extends HandlerInterceptorAdapter {

    @Resource
    private SacAccountSearchThriftService sacAccountSearchThriftService;
    private static final List<Integer> APP_ID_LIST = Lists.newArrayList(3/*百川PC*/, 6/*蜻蜓*/);

    protected boolean hasBusinessPermission(String permissionCode) {
        try {
            QuerySacAccountPermissionRequest authRequest = new QuerySacAccountPermissionRequest();
            authRequest.setAccountId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountId());
            QuerySacAccountPermissionResponse permissionResponse =
                    sacAccountSearchThriftService.querySacAccountPermission(authRequest);
            if (permissionResponse.getSacStatus().getCode() != SacStatus.CODE_SUCCESS ||
                    CollectionUtils.isEmpty(permissionResponse.getSacPermissionDTOS())) {
                return false;
            }
            Optional<SacPermissionDTO> permissionOpt =
                    permissionResponse.getSacPermissionDTOS().stream()
                            .filter(sacPermissionDTO -> APP_ID_LIST.contains(sacPermissionDTO.getAppId()))
                            .filter(sacPermissionDTO -> StringUtils.equals(sacPermissionDTO.getSacPermissionCode(),
                                    permissionCode)).findAny();
            if (!permissionOpt.isPresent()) {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("invoke LaborBusinessInterceptor error", e);
            return false;
        }
    }

    protected void noAuth(HttpServletResponse response) throws IOException {
        response.setContentType(CommonConstant.CONTENT_TYPE_APPLICATION_JSON);
        response.setCharacterEncoding(CommonConstant.CHARSET_UTF8);
        String data = JSON.toJSONString(JsonMessage.forbidden("您没有权限进行此操作"));
        PrintWriter writer = response.getWriter();
        writer.write(data);
        writer.flush();
    }

}
