package com.sankuai.shangou.logistics.delivery.verify;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.common.vo.PageVO;
import com.sankuai.shangou.logistics.delivery.verify.dto.TPageDTO;
import com.sankuai.shangou.logistics.delivery.verify.dto.VerifyTaskViewAndExportDTO;
import com.sankuai.shangou.logistics.delivery.verify.request.QueryVerifyImageRequest;
import com.sankuai.shangou.logistics.delivery.verify.request.SearchVerifyTaskRequest;
import com.sankuai.shangou.logistics.delivery.verify.request.VerifyTaskExportRequest;
import com.sankuai.shangou.logistics.delivery.verify.vo.PunishInfoVO;
import com.sankuai.shangou.logistics.delivery.verify.vo.VerifyImageVO;
import com.sankuai.shangou.logistics.delivery.verify.vo.VerifyTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/22 17:26
 **/
@InterfaceDoc(
        displayName = "验证任务页面",
        type = "restful",
        scenarios = "验证任务查询等接口",
        description = "验证任务查询等接口",
        host = "https://qingting.waimai.meituan.com/"
)
@Slf4j
@RestController
@RequestMapping("dmp/api/web/verify/")
public class VerifyTaskController {
    @Resource
    private VerifyTaskService verifyTaskService;

    @MethodDoc(
            description = "查询验证任务列表",
            displayName = "查询验证任务列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询验证任务列表",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/verify/list",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "数据卡片模块-实时数据"
    )
    @PostMapping(value = "/list")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({@SecurityParam(value = "storeIds")})
    @ResponseBody
    public PageVO<VerifyTaskVO> searchVerifyTaskList(@RequestBody SearchVerifyTaskRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        VerifyTaskExportRequest tRequest = new VerifyTaskExportRequest(
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                request.getStoreIds(),
                request.getAccountName(),
                request.getTaskPushTimeBegin(),
                request.getTaskPushTimeEnd(),
                request.getStatusList(),
                request.getPage(),
                request.getPageSize(),
                request.getFaceIdentifyResult(),
                request.getHelmetIdentifyResult(),
                request.getDressingIdentifyResult(),
                request.getTaskId(),
                WebLoginContextHolder.getWebLoginContext().getAppId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountId()
        );
        return trans2VO(verifyTaskService.searchVerifyTask(tRequest));
    }

    @MethodDoc(
            description = "查询验证任务采集图片",
            displayName = "查询验证任务采集图片",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询验证任务采集图片",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/verify/queryVerifyImage",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "数据卡片模块-实时数据"
    )
    @PostMapping(value = "/queryVerifyImage")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({@SecurityParam(value = "storeIds")})
    @ResponseBody
    public VerifyImageVO queryVerifyImage(@RequestBody QueryVerifyImageRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        String imageBase64 = verifyTaskService.queryVerifyImageBase64(request.getTaskId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                WebLoginContextHolder.getWebLoginContext().getAppId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountId());
        VerifyImageVO verifyImageVO = new VerifyImageVO();
        verifyImageVO.setImageUrl(imageBase64);
        return verifyImageVO;
    }


    private static PageVO<VerifyTaskVO> trans2VO(TPageDTO<VerifyTaskViewAndExportDTO> pageDTO) {
        if (pageDTO == null) {
            return null;
        }

        List<VerifyTaskVO> taskVOList = Optional.ofNullable(pageDTO.getList())
                .orElse(Collections.emptyList())
                .stream()
                .map(dto -> {
                    VerifyTaskVO verifyTaskVO = new VerifyTaskVO();
                    verifyTaskVO.setTaskId(dto.getTaskId());
                    verifyTaskVO.setCollectContentList(dto.getCollectContentList());
                    verifyTaskVO.setCollectMode(dto.getCollectMode());
                    verifyTaskVO.setStatus(dto.getStatus());
                    verifyTaskVO.setPushTime(dto.getPushTime());
                    verifyTaskVO.setCompleteTime(dto.getCompleteTime());
                    verifyTaskVO.setExpireTime(dto.getExpireTime());
                    verifyTaskVO.setRiderName(dto.getRiderName());
                    verifyTaskVO.setRiderAccountName(dto.getRiderAccountName());
                    verifyTaskVO.setStoreName(dto.getStoreName());
                    verifyTaskVO.setChannelOrderId(dto.getChannelOrderId());
                    verifyTaskVO.setCallbackCode(dto.getCallbackCode());
                    verifyTaskVO.setCallbackMsg(dto.getCallbackMsg());
                    verifyTaskVO.setFaceIdentifyResult(dto.getFaceIdentifyResult());
                    if (dto.getFaceIdentifyPunishInfo() != null) {
                        PunishInfoVO punishInfoVO = new PunishInfoVO();
                        punishInfoVO.setPunishId(dto.getFaceIdentifyPunishInfo().getPunishId());
                        punishInfoVO.setPunishType(dto.getFaceIdentifyPunishInfo().getPunishType());
                        punishInfoVO.setAppealStatus(dto.getFaceIdentifyPunishInfo().getAppealStatus());
                        verifyTaskVO.setFaceIdentifyPunishInfo(punishInfoVO);
                    }
                    verifyTaskVO.setHelmetIdentifyResult(dto.getHelmetIdentifyResult());
                    if (dto.getHelmetIdentifyPunishInfo() != null) {
                        PunishInfoVO punishInfoVO = new PunishInfoVO();
                        punishInfoVO.setPunishId(dto.getHelmetIdentifyPunishInfo().getPunishId());
                        punishInfoVO.setPunishType(dto.getHelmetIdentifyPunishInfo().getPunishType());
                        punishInfoVO.setAppealStatus(dto.getHelmetIdentifyPunishInfo().getAppealStatus());
                        verifyTaskVO.setHelmetIdentifyPunishInfo(punishInfoVO);
                    }
                    verifyTaskVO.setDressingIdentifyResult(dto.getDressingIdentifyResult());
                    if (dto.getDressingIdentifyPunishInfo() != null) {
                        PunishInfoVO punishInfoVO = new PunishInfoVO();
                        punishInfoVO.setPunishId(dto.getDressingIdentifyPunishInfo().getPunishId());
                        punishInfoVO.setPunishType(dto.getDressingIdentifyPunishInfo().getPunishType());
                        punishInfoVO.setAppealStatus(dto.getDressingIdentifyPunishInfo().getAppealStatus());
                        verifyTaskVO.setDressingIdentifyPunishInfo(punishInfoVO);
                    }
                    if (dto.getTaskExpirePunishInfo() != null) {
                        PunishInfoVO punishInfoVO = new PunishInfoVO();
                        punishInfoVO.setPunishId(dto.getTaskExpirePunishInfo().getPunishId());
                        punishInfoVO.setPunishType(dto.getTaskExpirePunishInfo().getPunishType());
                        punishInfoVO.setAppealStatus(dto.getTaskExpirePunishInfo().getAppealStatus());
                        verifyTaskVO.setTaskExpirePunishInfo(punishInfoVO);
                    }
                    verifyTaskVO.setRequestCode(dto.getRequestCode());
                    verifyTaskVO.setUuid(dto.getUuid());
                    verifyTaskVO.setImageIsExpire(dto.getImageIsExpire());

                    return verifyTaskVO;
                }).collect(Collectors.toList());

        return new PageVO<>(taskVOList, pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal());

    }
}
