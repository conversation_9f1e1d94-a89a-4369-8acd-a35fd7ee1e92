package com.sankuai.shangou.logistics.delivery.monitoring.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2022/4/25
 */

@TypeDoc(
        description = "骑手改派请求"
)
@ApiModel("骑手改派请求")
@Data
public class RiderChangeRequest {
    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id")
    private Long shopId;

    @FieldDoc(
            description = "赋能统一订单号",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "赋能统一订单号")
    private Long orderId;


    @FieldDoc(
            description = "转单后的骑手账号id",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "转单后的骑手账号id")
    private Long riderAccountId;

    public Optional<String> validate() {
        if (shopId == null || shopId <= 0) {
            return Optional.of("门店id无效");
        }
        if(orderId == null || orderId <= 0) {
            return Optional.of("赋能统一订单号无效");
        }
        if(riderAccountId == null || riderAccountId <= 0) {
            return Optional.of("转单后的骑手账号id无效");
        }

        return Optional.empty();
    }
}
