package com.sankuai.shangou.logistics.delivery.sealcontainer;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.common.vo.PageVO;
import com.sankuai.shangou.logistics.delivery.seal.request.QuerySealContainerListRequest;
import com.sankuai.shangou.logistics.delivery.seal.request.ReturnContainerRequest;
import com.sankuai.shangou.logistics.delivery.seal.request.QuerySealContainerLogByConditionRequest;
import com.sankuai.shangou.logistics.delivery.sealcontainer.service.SealContainerManagementService;
import com.sankuai.shangou.logistics.delivery.sealcontainer.vo.SealContainerOpLogVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/6/27 15:11
 **/
@InterfaceDoc(
        displayName = "封签容具管理相关接口",
        type = "restful",
        scenarios = "封签容具管理相关接口",
        description = "封签容具管理相关接口",
        version = "V1.0"
)
@Slf4j
@RestController
@RequestMapping("dmp/api/web/fulfill-data/picking/seal-container/")
public class SealContainerManagementController {

    @Resource
    private SealContainerManagementService sealContainerManagementService;
    @MethodDoc(
            description = "封签容具-使用中容具列表",
            displayName = "封签容具-使用中容具列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "封签容具-使用中容具列表",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/fulfill-data/picking/seal-container/list",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "数据卡片模块-实时数据"
    )
    @PostMapping(value = "/list")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({@SecurityParam(value = "warehouseIds")})
    public PageVO<SealContainerOpLogVO> sealContainerList(@RequestBody QuerySealContainerListRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        return sealContainerManagementService.queryStoreUsingSealContainerList(request);
    }


    @MethodDoc(
            description = "封签容具-归还容具",
            displayName = "封签容具-归还容具",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "封签容具-归还容具",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/fulfill-data/picking/seal-container/",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "数据卡片模块-实时数据"
    )
    @PostMapping(value = "/return")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    public void returnContainer(@RequestBody ReturnContainerRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        sealContainerManagementService.returnSealContainer(request.getWarehouseId(), request.getSealContainerCode(), request.getSkipDeliveryStatusCheck());
    }

    @MethodDoc(
            description = "封签容具-归还容具日志列表",
            displayName = "封签容具-归还容具日志列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "封签容具-可用容具列表",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/fulfill-data/picking/seal-container/log",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "数据卡片模块-实时数据"
    )
    @PostMapping(value = "/log")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({@SecurityParam(value = "warehouseIds")})
    public PageVO<SealContainerOpLogVO> querySealContainerLogList(@RequestBody QuerySealContainerLogByConditionRequest request) {
        return sealContainerManagementService.querySealContainerLogList(request);
    }

}
