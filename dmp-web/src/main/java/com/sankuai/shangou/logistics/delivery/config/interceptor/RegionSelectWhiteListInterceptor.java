package com.sankuai.shangou.logistics.delivery.config.interceptor;

import com.alibaba.fastjson.JSON;
import com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder;
import com.sankuai.shangou.logistics.delivery.config.constant.CommonConstant;
import com.sankuai.shangou.logistics.delivery.config.constant.JsonMessage;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.MccUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

/**
 * 区域规划工具白名单拦截器
 */
@Component
public class RegionSelectWhiteListInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (MccUtils.getRegionSelectAccountWhiteList().contains(WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountName())) {
            return true;
        } else {
            noRegionAuth(response);
            return false;
        }
    }

    public void noRegionAuth(HttpServletResponse response) throws Exception {
        response.setContentType(CommonConstant.CONTENT_TYPE_APPLICATION_JSON);
        response.setCharacterEncoding(CommonConstant.CHARSET_UTF8);
        String authApprovalLink = MccUtils.getRegionAuthApprovalLink();
        String data = JSON.toJSONString(JsonMessage.forbidden(String.format("暂无数据查询权限，请参考%s进行权限申请", authApprovalLink)));
        PrintWriter writer = response.getWriter();
        writer.write(data);
        writer.flush();
    }
}
