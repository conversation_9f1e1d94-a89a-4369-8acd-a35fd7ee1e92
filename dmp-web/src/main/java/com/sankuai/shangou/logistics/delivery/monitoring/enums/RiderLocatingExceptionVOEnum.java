package com.sankuai.shangou.logistics.delivery.monitoring.enums;

public enum  RiderLocatingExceptionVOEnum {
    QUERY_IN_PROGRESS_ORDER_FAIL(1, "网络不佳","调用后端查询进行中运单接口失败"),
    TURN_OFF_BAI_CHUAN_LOCATING_SERVICE(2,"操作不规范","百川隐私定位未开启"),
    TURN_OFF_SYSTEM_LOCATING_PERMISSION(3, "操作不规范","系统定位未开启"),
    SDK_GET_LOCATION_FAIL(4, "网络不佳","定位SDK获取定位失败"),
    POST_RIDER_LOCATION_FAIL(5, "网络不佳","前端向后端上报位置接口失败"),
    RIDER_KILL_APPLICATION(6,"网络不佳","骑手关闭应用"),
    RIDER_SWITCH_STORE(7, "操作不规范","骑手切换门店"),
    RIDER_LOGGED_OUT(8, "操作不规范","骑手退出登录"),
    UNKNOWN_EXCEPTION(9, "网络不佳","未知异常"),
    RIDER_FINISH_DELIVERY_MORE_THAN_45_MINUTES(10, "非履约时段","骑手完成配送后45分钟停止定位");


    private int value;
    private String desc1;
    private String desc2;

    RiderLocatingExceptionVOEnum(int value, String desc1,String desc2) {
        this.value = value;
        this.desc1 = desc1;
        this.desc2 = desc2;
    }

    public int getValue() {
        return value;
    }

    public String getDesc1() {
        return desc1;
    }

    public String getDesc2() {
        return desc2;
    }

    public static RiderLocatingExceptionVOEnum enumOf(int value) {
        for (RiderLocatingExceptionVOEnum each : values()) {
            if (each.getValue() == value) {
                return each;
            }
        }
        return null;
    }
}
