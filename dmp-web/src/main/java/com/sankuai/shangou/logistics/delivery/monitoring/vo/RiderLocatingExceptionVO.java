package com.sankuai.shangou.logistics.delivery.monitoring.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * 骑手定位异常VO 参考com.sankuai.meituan.shangou.saas.crm.eapi.web.controller.delivery.rider.constant.RiderLocatingExceptionVOEnum
 */
@TypeDoc(
        description = "骑手监控大屏-骑手定位异常信息, 示例 \n" +
                "    QUERY_IN_PROGRESS_ORDER_FAIL(1, \"网络不佳\",\"调用后端查询进行中运单接口失败\"),\n" +
                "    TURN_OFF_BAI_CHUAN_LOCATING_SERVICE(2,\"操作不规范\",\"百川隐私定位未开启\"),\n" +
                "    TURN_OFF_SYSTEM_LOCATING_PERMISSION(3, \"操作不规范\",\"系统定位未开启\"),\n" +
                "    SDK_GET_LOCATION_FAIL(4, \"网络不佳\",\"定位SDK获取定位失败\"),\n" +
                "    POST_RIDER_LOCATION_FAIL(5, \"网络不佳\",\"前端向后端上报位置接口失败\"),\n" +
                "    RIDER_KILL_APPLICATION(6,\"网络不佳\",\"骑手关闭应用\"),\n" +
                "    RIDER_SWITCH_STORE(7, \"操作不规范\",\"骑手切换门店\"),\n" +
                "    RIDER_LOGGED_OUT(8, \"操作不规范\",\"骑手退出登录\"),\n" +
                "    UNKNOWN_EXCEPTION(9, \"网络不佳\",\"未知异常\"),\n" +
                "    RIDER_FINISH_DELIVERY_MORE_THAN_45_MINUTES(10, \"非履约时段\",\"骑手完成配送后45分钟停止定位\");"
)
@ApiModel("骑手监控大屏-骑手定位异常信息")
@Data
@Builder
@AllArgsConstructor
public class RiderLocatingExceptionVO {
    @FieldDoc(
            description = "异常类型"
    )
    @ApiModelProperty(value = "异常类型", required = true)
    private int exceptionType;

    @FieldDoc(
            description = "异常一级描述"
    )
    @ApiModelProperty(value = "异常一级描述", required = true)
    private String exceptionDesc1;

    @FieldDoc(
            description = "异常二级描述"
    )
    @ApiModelProperty(value = "异常二级描述", required = true)
    private String exceptionDesc2;
}
