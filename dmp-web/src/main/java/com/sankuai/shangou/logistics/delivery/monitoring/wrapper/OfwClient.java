package com.sankuai.shangou.logistics.delivery.monitoring.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.sankuai.qnh.ofc.ofw.client.thrift.common.base.OfcStatus;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.BatchFulfillmentOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.ChannelOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.response.order.FulfillmentOrderDetailResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/5/7 17:08
 **/
@Rhino
@Slf4j
public class OfwClient {
    @Resource
    private FulfillmentOrderSearchThriftService fulfillmentOrderSearchThriftService;

    @Degrade(rhinoKey = "OfwClient.batchGetSealDeliveryTag", fallBackMethod = "batchGetSealDeliveryTagFallback", timeoutInMilliseconds = 2000)
    public Map<OrderKey, Boolean> batchGetSealDeliveryTag(Long tenantId, Long storeId, List<OrderKey> orderKeys) {
        List<ChannelOrderIdKeyReq> channelOrderIdKeyReqs = orderKeys.stream()
                .map(tradeOrderKey -> new ChannelOrderIdKeyReq(tradeOrderKey.getOrderBizType(), tradeOrderKey.getChannelOrderId()))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(channelOrderIdKeyReqs)) {
            return Collections.emptyMap();
        }

        List<FulfillmentOrderDetailDTO> fulfillmentOrderDetailDTOS = searchFulfillmentOrderListByBatchFulfillmentOrderId(tenantId, storeId, channelOrderIdKeyReqs);

        Map<OrderKey, Boolean> orderIsContainSealTagMap = new HashMap<>();
        for (FulfillmentOrderDetailDTO fulfillmentOrderDetailDTO : fulfillmentOrderDetailDTOS) {
            boolean containSealProduct = Objects.equals(fulfillmentOrderDetailDTO.getIsContainSealProduct(), true);
            orderIsContainSealTagMap.putIfAbsent(new OrderKey(fulfillmentOrderDetailDTO.getChannelOrderId(), fulfillmentOrderDetailDTO.getOrderSource()), containSealProduct);
        }

        return orderIsContainSealTagMap;
    }


    private List<FulfillmentOrderDetailDTO> searchFulfillmentOrderListByBatchFulfillmentOrderId(Long tenantId, Long storeId, List<ChannelOrderIdKeyReq> channelOrderIdKeyReqs) {
        List<FulfillmentOrderDetailDTO> fulfillmentOrderDetailDTOList = Lists.newArrayList();
        //OFC最大查询100
        List<List<ChannelOrderIdKeyReq>> partitionChannelOrderIdKey = Lists.partition(channelOrderIdKeyReqs, 100);
        for (List<ChannelOrderIdKeyReq> orderIdKeyReqs : partitionChannelOrderIdKey) {
            BatchFulfillmentOrderIdKeyReq req = new BatchFulfillmentOrderIdKeyReq();
            req.setTenantId(tenantId);
            req.setWarehouseId(storeId);
            req.setChannelOrderIdKeyList(orderIdKeyReqs);
            FulfillmentOrderDetailResponse response = fulfillmentOrderSearchThriftService.searchFulfillmentOrderListByBatchFulfillmentOrderId(req);
            if (!Objects.equals(response.getStatus().getCode(), OfcStatus.SUCCESS.getCode())) {
                log.info("fulfillmentOrderSearchThriftService.searchFulfillmentOrderListByBatchFulfillmentOrderId, req = {}, response = {}", req, response);
                throw new BizException("查询履约单失败");
            }
            fulfillmentOrderDetailDTOList.addAll(Optional.ofNullable(response.getFulfillmentOrderList()).orElse(Lists.newArrayList()));

        }

        return fulfillmentOrderDetailDTOList;
    }

    public Map<OrderKey, Boolean> batchGetSealDeliveryTagFallback(Long tenantId, Long storeId, List<OrderKey> orderKeys) {
        log.error("SealDeliveryTagComponent.batchGetSealDeliveryTag 发生降级");
        return Collections.emptyMap();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OrderKey {
        private String channelOrderId;

        private Integer orderBizType;
    }
}
