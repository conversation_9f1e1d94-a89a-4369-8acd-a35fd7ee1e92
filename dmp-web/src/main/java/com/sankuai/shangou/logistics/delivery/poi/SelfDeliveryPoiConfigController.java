package com.sankuai.shangou.logistics.delivery.poi;


import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.shangou.commons.auth.login.context.LoginUser;
import com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder;
import com.sankuai.shangou.commons.auth.login.web.context.CookieTokenTypeEnum;
import com.sankuai.shangou.commons.auth.login.web.context.LoginUtil;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.constants.Operator;
import com.sankuai.shangou.logistics.delivery.constants.PageReq;
import com.sankuai.shangou.logistics.delivery.constants.PageResp;
import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPO;
import com.sankuai.shangou.logistics.delivery.poi.request.BatchUpdateNeedTurnDeliveryReq;
import com.sankuai.shangou.logistics.delivery.poi.request.BatchUpdatePickDeliverySplitReq;
import com.sankuai.shangou.logistics.delivery.poi.request.QueryDapStoreConfigUrlReq;
import com.sankuai.shangou.logistics.delivery.poi.request.QueryDeliveryMethodManagementListReq;
import com.sankuai.shangou.logistics.delivery.poi.response.ConfigUrlResponse;
import com.sankuai.shangou.logistics.delivery.poi.response.Result;
import com.sankuai.shangou.logistics.delivery.poi.response.SelfDeliveryDeliveryPoiConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-09-01
 * @email <EMAIL>
 */
@InterfaceDoc(
        displayName = "自配送门店设置",
        type = "restful",
        scenarios = "包含自配送门店设置相关接口",
        description = "包含自配送门店设置相关接口",
        host = "https://fnsaas.waimai.meituan.com/"
)
@Slf4j
@RestController
@RequestMapping("dmp/api/poi/config")
public class SelfDeliveryPoiConfigController {

    @Resource
    private SelfDeliveryPoiConfigService selfDeliveryPoiConfigService;

    @MethodDoc(
            displayName = "查询配送方式管理列表",
            description = "查询配送方式管理列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询配送方式管理列表请求",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/poi/config/querySelfDeliveryConfigList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/querySelfDeliveryConfigList")
    @ResponseBody
    @MethodLog(logResponse = true, logRequest = true)
    public PageResp<SelfDeliveryDeliveryPoiConfigVO> querySelfDeliveryConfigList(@RequestBody QueryDeliveryMethodManagementListReq req) {
        PageResp<Pair<SelfDeliveryPoiConfigPO, String>> pageResp = selfDeliveryPoiConfigService.querySelfDeliveryConfigList(
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountId(),
                req.getEnableTurnDelivery(),
                req.getEnablePickDeliverySplit(),
                req.getDepartmentIds(),
                new PageReq(req.getPageNo(), req.getPageSize())
        );
        return new PageResp<>(
                IListUtils.mapTo(pageResp.getList(), pair -> SelfDeliveryDeliveryPoiConfigVO.fromPO(pair.getValue(), pair.getKey())),
                pageResp.getTotalCount(), pageResp.isHasMore());
    }


    @MethodDoc(
            displayName = "查询青云配送门店设置URL",
            description = "查询青云配送门店设置URL",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询青云配送门店设置URL请求",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/poi/config/queryDapStoreConfigUrl",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/queryDapStoreConfigUrl")
    @ResponseBody
    @MethodLog(logResponse = true, logRequest = true)
    @Deprecated
    public ConfigUrlResponse queryDapStoreConfigUrl(@RequestBody QueryDapStoreConfigUrlReq req) {

        HttpServletRequest httpRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Pair<CookieTokenTypeEnum, String> token = LoginUtil.getLoginTicket(httpRequest);

        String url = selfDeliveryPoiConfigService.queryDapStoreConfigUrl(
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                req.getPoiId(), token.getValue()
        );

        return new ConfigUrlResponse(url);
    }

    @PostMapping("/queryDapStoreSettingsLink")
    @ResponseBody
    @MethodLog(logResponse = true, logRequest = true)
    public ConfigUrlResponse queryDapStoreSettingsLink(@RequestBody QueryDapStoreConfigUrlReq req) {
        HttpServletRequest httpRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Pair<CookieTokenTypeEnum, String> token = LoginUtil.getLoginTicket(httpRequest);
        LoginUser loginUser = WebLoginContextHolder.getWebLoginContext().getLoginUser();
        String url = selfDeliveryPoiConfigService.queryDapStoreSettingsLink(
                loginUser.getTenantId(), req.getPoiId(), token.getValue(),loginUser.getEmployeeId(),loginUser.getEmployeeName()
        );
        return new ConfigUrlResponse(url);
    }

    @MethodDoc(
            displayName = "批量更新配送方式",
            description = "批量更新配送方式",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量更新配送方式请求",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/poi/config/batchUpdateNeedTurnDelivery",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/batchUpdateNeedTurnDelivery")
    @ResponseBody
    @MethodLog(logResponse = true, logRequest = true)
    public void batchUpdateNeedTurnDelivery(@RequestBody BatchUpdateNeedTurnDeliveryReq req) {
        selfDeliveryPoiConfigService.batchUpdateNeedTurnDelivery(
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                req.getPoiIds(), req.getEnableTurnDelivery(),
                new Operator(WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName(), WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeId(), LocalDateTime.now())
        );
    }

    @MethodDoc(
            displayName = "批量更新配送方式",
            description = "批量更新配送方式",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量更新配送方式请求",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/poi/config/batchUpdateNeedTurnDelivery",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/batchUpdatePickDeliverySplit")
    @ResponseBody
    @MethodLog(logResponse = true, logRequest = true)
    public void batchUpdatePickDeliverySplit(@RequestBody BatchUpdatePickDeliverySplitReq req) {
        selfDeliveryPoiConfigService.batchUpdatePickDeliverySplit(
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                req.getPoiIds(), req.getEnablePickDeliverySplit(),
                new Operator(WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName(), WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeId(), LocalDateTime.now())
        );
    }
}
