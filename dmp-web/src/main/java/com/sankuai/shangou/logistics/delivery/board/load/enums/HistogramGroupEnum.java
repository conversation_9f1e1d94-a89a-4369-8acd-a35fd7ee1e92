package com.sankuai.shangou.logistics.delivery.board.load.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/7/4 19:40
 **/
public enum HistogramGroupEnum {
    ORDER_DISTRIBUTION_GROUP(1, "订单分布", 1),
    SCHEDULED_EMPLOYEE_DISTRIBUTION_GROUP(2, "排班人员分布", 2),

    ATTENDANCE_EMPLOYEE_DISTRIBUTION_GROUP(3, "出勤员工分布", 3),
    ;
    private Integer code;

    private String groupDesc;

    private Integer order;

    public Integer getCode() {
        return code;
    }

    public String getGroupDesc() {
        return groupDesc;
    }

    public Integer getOrder() {
        return order;
    }

    HistogramGroupEnum(Integer code, String groupDesc, Integer order) {
        this.code = code;
        this.groupDesc = groupDesc;
        this.order = order;
    }

    public static HistogramGroupEnum enumOf(String desc) {
        for (HistogramGroupEnum val : values()) {
            if (Objects.equals(val.groupDesc, desc)) {
                return val;
            }
        }

        return null;
    }
}


