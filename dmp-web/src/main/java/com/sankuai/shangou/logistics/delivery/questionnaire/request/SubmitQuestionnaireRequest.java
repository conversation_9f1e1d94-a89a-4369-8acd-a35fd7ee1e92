package com.sankuai.shangou.logistics.delivery.questionnaire.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/11/17 00:07
 **/
@Data
public class SubmitQuestionnaireRequest {

    @FieldDoc(description = "运单id")
    private Long deliveryOrderId;

    @FieldDoc(description = "key-问题 value-答案")
    private Map<String, String> questionnaireResult;


    @FieldDoc(description = "填写场景 1-点击送达时 2-查看已送达列表时")
    private Integer scene;

    public void validate() {
        if (deliveryOrderId == null) {
            throw new IllegalArgumentException("运单id不合法");
        }

        if (MapUtils.isEmpty(questionnaireResult)) {
            throw new IllegalArgumentException("答案不能为空");
        }

        if (scene == null) {
            throw new IllegalArgumentException("填写场景不合法");
        }
    }
}
