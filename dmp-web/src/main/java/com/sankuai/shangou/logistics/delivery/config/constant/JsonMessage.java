package com.sankuai.shangou.logistics.delivery.config.constant;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class JsonMessage {
    public static final int OK = 0;
    public static final int ERROR = 1;
    public static final int FORBIDDEN = 403;
    public static final int PARAM_ERROR = 1;
    public static final String ERROR_TEXT = "系统异常";
    public static final String NOT_OPEN_BUSINESS = "未开放此业务";

    private int code;
    private String msg;
    private Object data;

    private JsonMessage() {

    }

    public static JsonMessage ok() {
        return ok("");
    }

    public static JsonMessage ok(String msg) {
        return getJsonMessage(OK, msg, "");
    }

    public static JsonMessage okData(Object data) {
        return getJsonMessage(OK, "ok", data);
    }

    public static JsonMessage ok(String msg, Object data) {
        return getJsonMessage(OK, msg, data);
    }

    public static JsonMessage ok(Object data) {
        return getJsonMessage(OK, "ok", data);
    }


    public static JsonMessage fail() {
        return fail(ERROR_TEXT);
    }

    public static JsonMessage paramError() {
        return fail("参数异常");
    }

    public static JsonMessage fail(String msg) {
        return fail(msg, "");
    }

    public static JsonMessage fail(String msg, Object data) {
        return getJsonMessage(ERROR, msg, data);
    }

    public static JsonMessage forbidden(String msg) {
        return forbidden(msg, "");
    }

    public static JsonMessage forbidden(String msg, Object data) {
        return getJsonMessage(FORBIDDEN, msg, data);
    }

    public static JsonMessage notOpenBusiness() {
        return getJsonMessage(1, NOT_OPEN_BUSINESS, "");
    }


    public static JsonMessage getJsonMessage(int code, String msg, Object data) {
        JsonMessage jsonMessage = new JsonMessage();
        jsonMessage.code = code;
        jsonMessage.msg = msg;
        jsonMessage.data = data;
        return jsonMessage;
    }

    @Override
    public String toString() {
        return "JsonMessage{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
}
