package com.sankuai.shangou.logistics.delivery.sealcontainer.service;

import com.alibaba.fastjson.JSON;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.thrift.publisher.response.PaginationList;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import com.sankuai.shangou.logistics.delivery.common.vo.PageVO;
import com.sankuai.shangou.logistics.delivery.common.wrapper.OswClient;
import com.sankuai.shangou.logistics.delivery.seal.SealContainerManagementQueryService;
import com.sankuai.shangou.logistics.delivery.seal.enums.SealContainerStatusEnum;
import com.sankuai.shangou.logistics.delivery.seal.request.QuerySealContainerListRequest;
import com.sankuai.shangou.logistics.delivery.seal.request.QuerySealContainerLogByConditionRequest;
import com.sankuai.shangou.logistics.delivery.seal.request.SealContainerOpLogWebDTO;
import com.sankuai.shangou.logistics.delivery.seal.wrapper.OCMSQueryServiceWrapper;
import com.sankuai.shangou.logistics.delivery.seal.wrapper.RiderDeliveryOrderWrapper;
import com.sankuai.shangou.logistics.delivery.sealcontainer.vo.SealContainerOpLogVO;
import com.sankuai.shangou.logistics.delivery.seal.wrapper.SealContainerOpLogServiceWrapper;
import com.sankuai.shangou.logistics.hu.api.constants.enums.SealContainerOperateType;
import com.sankuai.shangou.logistics.hu.api.dto.SealContainerLogDTO;
import com.sankuai.shangou.logistics.hu.api.dto.SealContainerLogExtInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/6/28 15:31
 **/
@Service
@Slf4j
public class SealContainerManagementService {
    @Resource
    private SealContainerOpLogServiceWrapper sealContainerOpLogServiceWrapper;

    @Resource
    private OswClient oswClient;

    @Resource
    private SealContainerManagementQueryService sealContainerManagementQueryService;

    @Resource
    private RiderDeliveryOrderWrapper riderDeliveryOrderWrapper;

    @Resource
    private OCMSQueryServiceWrapper ocmsQueryServiceWrapper;


    public PageVO<SealContainerOpLogVO> queryStoreUsingSealContainerList(QuerySealContainerListRequest request) {
        List<SealContainerLogDTO> usingSealContainerLatestLogs = sealContainerOpLogServiceWrapper.queryLatestSealContainerOperateLog(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(), request.getSealContainerCode(), request.getWarehouseIds());

        //只看现在是使用中的容具
        usingSealContainerLatestLogs = usingSealContainerLatestLogs.stream()
                .filter(opLog -> Objects.equals(opLog.getOpType(), SealContainerOperateType.STOCK_OUT.getCode())
                        || Objects.equals(opLog.getOpType(), SealContainerOperateType.TRANSFER.getCode()))
                .collect(Collectors.toList());


        //按照交易单号筛选
        if (StringUtils.isNotBlank(request.getTradeOrderNo())) {
            usingSealContainerLatestLogs = usingSealContainerLatestLogs.stream()
                    .filter(log -> Objects.equals(log.getTradeOrderNo(), request.getTradeOrderNo()))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(usingSealContainerLatestLogs) || usingSealContainerLatestLogs.size() < (request.getPage() - 1) * request.getPageSize()) {
            return new PageVO<>(Collections.emptyList(), request.getPage(), request.getPageSize(), usingSealContainerLatestLogs.size());
        }

        //拿到最近一次操作记录是转单的容具code 查询对应容具最后一次出库的记录
        Map<String, SealContainerLogDTO> transferSealContainerLogMap = usingSealContainerLatestLogs.stream()
                .filter(opLog -> Objects.equals(opLog.getOpType(), SealContainerOperateType.TRANSFER.getCode()))
                .collect(Collectors.toMap(SealContainerLogDTO::getSealContainerCode, Function.identity(), (k1, k2) -> k2));

        List<SealContainerLogDTO> stockOutSealContainerLogs = sealContainerOpLogServiceWrapper.queryLatestSealContainerOperateLogByOpTye(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(), request.getWarehouseIds(), new ArrayList<>(transferSealContainerLogMap.keySet()), Collections.singletonList(SealContainerOperateType.STOCK_OUT.getCode()));

        //合并最后一次出库和最后一次转单的出库记录
        Map<String, SealContainerLogDTO> stockOutSealContainerLogMap = new HashMap<>();
        stockOutSealContainerLogMap.putAll(usingSealContainerLatestLogs.stream()
                .filter(opLog -> Objects.equals(opLog.getOpType(), SealContainerOperateType.STOCK_OUT.getCode()))
                .collect(Collectors.toMap(SealContainerLogDTO::getSealContainerCode, Function.identity(), (k1, k2) -> k2)));
        stockOutSealContainerLogMap.putAll(
                stockOutSealContainerLogs.stream().collect(Collectors.toMap(SealContainerLogDTO::getSealContainerCode, Function.identity(), (k1, k2) -> k2))
        );

        //拼接同一个container的出库日志和转单日志
        List<SealContainerOpLogVO> sealContainerOpLogVOS = usingSealContainerLatestLogs.stream().map(opLog -> {
            String containerCode = opLog.getSealContainerCode();
            return buildOpLogVO(stockOutSealContainerLogMap.get(containerCode), transferSealContainerLogMap.get(containerCode), null, SealContainerStatusEnum.USING);
        }).sorted(new Comparator<SealContainerOpLogVO>() {
            @Override
            public int compare(SealContainerOpLogVO o1, SealContainerOpLogVO o2) {
                return -o1.getRiderStartDeliveryTime().compareTo(o2.getRiderStartDeliveryTime());
            }
        }).collect(Collectors.toList());

        //内存分页
        List<SealContainerOpLogVO> pageList = sealContainerOpLogVOS.subList((request.getPage() - 1) * request.getPageSize(), Math.min(request.getPage() * request.getPageSize(), sealContainerOpLogVOS.size()));

        //填充门店名
        Map<Long, WarehouseDTO> warehouseInfoMap = oswClient.queryWarehouseInfoList(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                pageList.stream().map(SealContainerOpLogVO::getWarehouseId).collect(Collectors.toList()));
        pageList.forEach(sealContainerOpLogVO -> {
            if (warehouseInfoMap.containsKey(sealContainerOpLogVO.getWarehouseId())) {
                sealContainerOpLogVO.setWarehouseName(warehouseInfoMap.get(sealContainerOpLogVO.getWarehouseId()).getName());
            }
        });

        return new PageVO(pageList, request.getPage(), request.getPageSize(), sealContainerOpLogVOS.size());
    }


    public void returnSealContainer(Long warehouseId, String containerCode, Boolean skipDeliveryStatusCheck) {
        //根据编码查到关联订单
        List<SealContainerLogDTO> sealContainerLogDTOS = sealContainerOpLogServiceWrapper.queryLatestSealContainerOperateLog(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(), containerCode, Collections.singletonList(warehouseId));

        if (CollectionUtils.isEmpty(sealContainerLogDTOS)) {
            throw new BizException(20000092, "当前编码不是该门店待归还容具编码，请核实后再扫码或手动添加");
        }

        SealContainerLogDTO sealContainerLogDTO = sealContainerLogDTOS.get(0);
        if (!Objects.equals(sealContainerLogDTO.getOpType(), SealContainerOperateType.STOCK_OUT.getCode()) &&
                !Objects.equals(sealContainerLogDTO.getOpType(), SealContainerOperateType.TRANSFER.getCode())) {
            throw new BizException(20000092, "当前编码不是该门店待归还容具编码，请核实后再扫码或手动添加");
        }

        //查询订单的配送状态 是否还在配送中
        if (!Objects.equals(skipDeliveryStatusCheck, true)) {
            OCMSOrderVO ocmsOrderVO = ocmsQueryServiceWrapper.queryOrderByViewIdCondition(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(), sealContainerLogDTO.getTradeOrderNo(), sealContainerLogDTO.getTradeOrderBizType());
            Long orderId = ocmsOrderVO.getOrderId();

            List<TRiderDeliveryOrder> tRiderDeliveryOrders = riderDeliveryOrderWrapper.queryDeliveryOrderByOrderIdList(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(), warehouseId, Collections.singletonList(orderId));
            if (CollectionUtils.isNotEmpty(tRiderDeliveryOrders) && Objects.equals(tRiderDeliveryOrders.get(0).getDeliveryStatus(), RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode())) {
                throw new BizException(20000091, "当前容具编码所属订单仍在配送中，请确认容具是否使用完成，再进行归还");
            }
        }

        //归还容具
        sealContainerOpLogServiceWrapper.appendLog(warehouseId,
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                Collections.singletonList(buildSealContainerLogDTO(sealContainerLogDTO.getWarehouseId(), sealContainerLogDTO.getTradeOrderNo(), sealContainerLogDTO.getTradeOrderBizType(), containerCode)));
    }


    private SealContainerLogDTO buildSealContainerLogDTO(Long warehouseId, String tradeOrderNo, Integer orderBizType, String sealContainerCode) {
        SealContainerLogDTO dto = new SealContainerLogDTO();
        dto.setTenantId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
        dto.setWarehouseId(warehouseId);
        dto.setTradeOrderBizType(orderBizType);
        dto.setTradeOrderNo(tradeOrderNo);
        dto.setOpType(SealContainerOperateType.STOCK_IN.getCode());
        dto.setOpTime(LocalDateTime.now());
        dto.setSealContainerCode(sealContainerCode);
        dto.setOperatorId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountId());
        dto.setOperatorName(WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName());
        Map<String, Object> map = new HashMap<>();
        map.put("source", "WEB");
        dto.setExtInfo(JSON.toJSONString(map));

        return dto;
    }


    public PageVO<SealContainerOpLogVO> querySealContainerLogList(QuerySealContainerLogByConditionRequest request) {
        request.setTenantId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
        PaginationList<SealContainerOpLogWebDTO> sealContainerOpLogWebDTOPaginationList = sealContainerManagementQueryService.querySealContainerLogList(request);
        List<SealContainerOpLogVO> sealContainerOpLogVOS = IListUtils.mapTo(
                sealContainerOpLogWebDTOPaginationList.getList(),
                dto -> {
                    SealContainerOpLogVO vo = new SealContainerOpLogVO();
                    vo.setWarehouseId(dto.getWarehouseId());
                    vo.setWarehouseName(dto.getWarehouseName());
                    vo.setSealContainerCode(dto.getSealContainerCode());
                    vo.setTradeOrderNo(dto.getTradeOrderNo());
                    vo.setChannelId(dto.getChannelId());
                    vo.setStatus(dto.getStatus());
                    vo.setStockOutOperatorName(dto.getStockOutOperatorName());
                    vo.setStockOutTime(dto.getStockOutTime());
                    vo.setRiderName(dto.getRiderName());
                    vo.setRiderStartDeliveryTime(dto.getRiderStartDeliveryTime());
                    vo.setGiveBackOperatorName(dto.getGiveBackOperatorName());
                    vo.setGiveBackTime(dto.getGiveBackTime());
                    vo.setGiveBackType(dto.getGiveBackType());
                    return vo;
                }
        );

        return new PageVO<>(
                sealContainerOpLogVOS,
                sealContainerOpLogWebDTOPaginationList.getPage(),
                sealContainerOpLogWebDTOPaginationList.getPageSize(),
                sealContainerOpLogWebDTOPaginationList.getTotal().intValue()
        );
    }


    //注意：门店名之后再填充
    private SealContainerOpLogVO buildOpLogVO(SealContainerLogDTO stockOutLogDto, @Nullable SealContainerLogDTO transferOutLogDto, @Nullable SealContainerLogDTO stockInLogDto,
                                              SealContainerStatusEnum sealContainerStatusEnum) {
        SealContainerOpLogVO sealContainerOpLogVO = new SealContainerOpLogVO();
        sealContainerOpLogVO.setWarehouseId(stockOutLogDto.getWarehouseId());
        sealContainerOpLogVO.setSealContainerCode(stockOutLogDto.getSealContainerCode());
        sealContainerOpLogVO.setTradeOrderNo(stockOutLogDto.getTradeOrderNo());
        sealContainerOpLogVO.setChannelId(ChannelOrderConvertUtils.convertChannelId(stockOutLogDto.getTradeOrderBizType()));
        sealContainerOpLogVO.setStatus(sealContainerStatusEnum.getCode());
        sealContainerOpLogVO.setStockOutOperatorName(stockOutLogDto.getOperatorName());
        sealContainerOpLogVO.setStockOutTime(TimeUtils.toMilliSeconds(stockOutLogDto.getOpTime()));

        // 使用三元运算符简化逻辑
        SealContainerLogDTO effectiveLogDto = Objects.nonNull(transferOutLogDto) ? transferOutLogDto : stockOutLogDto;
        sealContainerOpLogVO.setRiderName(effectiveLogDto.getOperatorName());
        sealContainerOpLogVO.setRiderStartDeliveryTime(TimeUtils.toMilliSeconds(effectiveLogDto.getOpTime()));

        if (Objects.nonNull(stockInLogDto)) {
            sealContainerOpLogVO.setGiveBackTime(TimeUtils.toMilliSeconds(stockInLogDto.getOpTime()));
            sealContainerOpLogVO.setGiveBackOperatorName(stockInLogDto.getOperatorName());
            setGiveBackType(stockInLogDto, sealContainerOpLogVO);
        }
        return sealContainerOpLogVO;
    }

    private static void setGiveBackType(SealContainerLogDTO stockInLogDto, SealContainerOpLogVO sealContainerOpLogVO) {
        try {
            if (StringUtils.isNotBlank(stockInLogDto.getExtInfo())) {
                String source = Optional.ofNullable(JSON.parseObject(stockInLogDto.getExtInfo(), SealContainerLogExtInfo.class)).map(SealContainerLogExtInfo::getSource).orElse(StringUtils.EMPTY);
                if (Objects.equals(source, "WEB")) {
                    sealContainerOpLogVO.setGiveBackType(2);
                } else if (Objects.equals(source, "APP")) {
                    sealContainerOpLogVO.setGiveBackType(1);
                }
            }
        } catch (Exception e) {
            log.error("setGiveBackType error", e);
        }
    }

}
