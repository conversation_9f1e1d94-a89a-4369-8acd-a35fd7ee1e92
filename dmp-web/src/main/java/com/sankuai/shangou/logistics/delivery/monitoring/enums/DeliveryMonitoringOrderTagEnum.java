package com.sankuai.shangou.logistics.delivery.monitoring.enums;

/**
 * <AUTHOR>
 * @since 2024/5/14 11:05
 **/
public enum DeliveryMonitoringOrderTagEnum {
    MEMBER(10, "会员订单"),
    SEAL(20, "封签交付"),

    RESTAURANT(30, "餐馆订单"),
    FACAI(40, "发财酒订单");
    private int code;

    private String desc;

    DeliveryMonitoringOrderTagEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
