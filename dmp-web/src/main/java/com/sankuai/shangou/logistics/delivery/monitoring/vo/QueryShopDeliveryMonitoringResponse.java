package com.sankuai.shangou.logistics.delivery.monitoring.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 门店配送情况监控返回体.
 *
 * <AUTHOR>
 * @since 2021/12/5 17:48
 */
@TypeDoc(
        description = "门店配送情况监控返回体"
)
@ApiModel("门店配送情况监控返回体")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryShopDeliveryMonitoringResponse {

    /**
     * 骑手配送现状
     * Map 的 key 参考
     * @see com.sankuai.shangou.logistics.delivery.monitoring.enums.RiderShowStatusEnum
     */
    @FieldDoc(
            description = "骑手配送现状"
    )
    @ApiModelProperty(value = "骑手配送现状")
    private Map<Integer, List<RiderDeliveryInfoVO>> riderStatus;

    /**
     * 门店运单现状
     * Map 的 key 参考
     * @see com.sankuai.shangou.logistics.delivery.monitoring.enums.OrderShowStatusEnum
     */
    @FieldDoc(
            description = "门店运单现状"
    )
    @ApiModelProperty(value = "门店运单现状")
    private Map<Integer, List<RiderDeliveryOrderMonitoringVO>> orderStatus;

    @FieldDoc(
            description = "配送监控大屏中订单下单信息"
    )
    @ApiModelProperty(value = "配送监控大屏中订单下单信息")
    private List<OrderInfoVO> orderInfo;
}
