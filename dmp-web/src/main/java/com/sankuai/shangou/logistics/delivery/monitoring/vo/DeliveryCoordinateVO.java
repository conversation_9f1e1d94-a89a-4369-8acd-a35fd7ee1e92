package com.sankuai.shangou.logistics.delivery.monitoring.vo;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 供配送使用的坐标点信息 VO.
 *
 * <AUTHOR>
 * @since 2021/12/3 18:48
 */
@TypeDoc(
        description = "配送使用的坐标点信息 VO"
)
@ApiModel("配送使用的坐标点信息 VO")
@Data
@NoArgsConstructor
public class DeliveryCoordinateVO {

    @FieldDoc(
            description = "定位经度，精确到小数点后六位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "定位经度", required = true)
    private String longitude;

    @FieldDoc(
            description = "定位纬度，精确到小数点后六位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "定位纬度", required = true)
    private String latitude;

    public DeliveryCoordinateVO(String longitude, String latitude) {
        Preconditions.checkNotNull(longitude, "定位经度不能为空");
        Preconditions.checkNotNull(latitude, "定位纬度不能为空");
        this.longitude = longitude;
        this.latitude = latitude;
    }

    public DeliveryCoordinateVO(Double longitude, Double latitude) {
        Preconditions.checkNotNull(longitude, "定位经度不能为空");
        Preconditions.checkNotNull(latitude, "定位纬度不能为空");
        this.longitude = longitude.toString();
        this.latitude = latitude.toString();
    }
}
