package com.sankuai.shangou.logistics.delivery.questionnaire;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.model.DeliveryQuestionnaireDO;
import com.sankuai.shangou.logistics.delivery.questionnaire.convert.Converter;
import com.sankuai.shangou.logistics.delivery.questionnaire.request.QueryDeliveryQuestionnaireRequest;
import com.sankuai.shangou.logistics.delivery.questionnaire.request.SubmitQuestionnaireRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/16 23:47
 **/
@Slf4j
@RestController
@RequestMapping("dmp/api/app/questionnaire")
public class DeliveryQuestionnaireController {
    @Resource
    private DeliveryQuestionnaireService deliveryQuestionnaireService;

    @MethodDoc(
            displayName = "查询运单对应的问卷",
            description = "查询运单对应的问卷",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询运单对应的问卷请求",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/app/questionnaire/get",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/get")
    @ResponseBody
    @MethodLog(logResponse = true, logRequest = true)
    public Object queryDeliveryQuestionnaire(@RequestBody QueryDeliveryQuestionnaireRequest req) {
        List<DeliveryQuestionnaireDO> deliveryQuestionnaireDOList = deliveryQuestionnaireService.queryQuestionnaireByDeliveryOrderIds(req.getDeliveryOrderIdList());

       return Converter.toDeliveryOrderQuestionnaireVO(deliveryQuestionnaireDOList);
    }

    @MethodDoc(
            displayName = "提交问卷答案",
            description = "提交问卷答案",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "提交问卷答案",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/app/questionnaire/submit",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/submit")
    @ResponseBody
    @MethodLog(logResponse = true, logRequest = true)
    public void submitDeliveryQuestionnaire(@RequestBody SubmitQuestionnaireRequest req) {
        req.validate();
        deliveryQuestionnaireService.submitAnswers(req.getQuestionnaireResult(), req.getDeliveryOrderId(),
               AppLoginContextHolder.getAppLoginContext().getLoginUser().getAccountId(), req.getScene());
    }

}
