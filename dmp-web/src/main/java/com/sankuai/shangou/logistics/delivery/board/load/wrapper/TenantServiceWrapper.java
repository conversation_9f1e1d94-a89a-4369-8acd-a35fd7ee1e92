package com.sankuai.shangou.logistics.delivery.board.load.wrapper;

import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/7/9 22:34
 **/
@Slf4j
@Service
public class TenantServiceWrapper {
    @Resource
    private PoiThriftService poiThriftService;

    @MethodLog(logResponse = true, logRequest = true)
    public Map<Long, PoiInfoDto> queryPoiByIds(Long tenantId, List<Long> poiIds) {
        if (CollectionUtils.isEmpty(poiIds)) {
            return Maps.newHashMap();
        }


        log.info("invoke poiThriftService.queryTenantPoiInfoMapByPoiIds start, poiIds:{}", poiIds);
        PoiMapResponse response = poiThriftService.queryTenantPoiInfoMapByPoiIds(poiIds, tenantId);
        if (response == null || response.getStatus() == null || response.getStatus().getCode() == null) {
            throw new ThirdPartyException("未能获取到接口返回");
        } else if (response.getStatus().getCode() != 0) {
            throw new ThirdPartyException(response.getStatus().getMessage());
        }
        log.info("invoke poiThriftService.queryTenantPoiInfoMapByPoiIds end, tenantId:{}, poiIds:{}, response: {}", tenantId, poiIds, response);
        return Optional.ofNullable(response.getPoiInfoMap()).orElse(Collections.emptyMap());
    }
}
