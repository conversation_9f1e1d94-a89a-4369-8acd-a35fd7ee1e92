package com.sankuai.shangou.logistics.delivery.board.load.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023/7/2 19:12
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DeliveryLoadDataPointVO {
    /** x轴坐标 */
    private String x;

    /** y轴坐标 */
    private BigDecimal y;

    /** 指标code */
    private String code;


    /** 指标描述 */
    private String desc;

    /** 分组名 */
    private String groupName;

    /** 颜色 */
    private String color;
}
