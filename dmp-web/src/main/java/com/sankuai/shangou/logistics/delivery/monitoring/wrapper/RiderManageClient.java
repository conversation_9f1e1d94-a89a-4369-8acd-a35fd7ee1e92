package com.sankuai.shangou.logistics.delivery.monitoring.wrapper;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.reco.pickselect.common.utils.ParamCheckUtils;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListViewIdConditionResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.enums.SortByEnum;
import com.meituan.shangou.saas.order.management.client.enums.SortFieldEnum;
import com.meituan.shangou.saas.order.management.client.enums.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaProducer;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.common.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.message.RiderChangeTransStockMsg;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.RiderOperateThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderChangeRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.response.RiderChangeResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrderMonitorInfo;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TStaffRider;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.StoreDeliveryMonitorRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.StoreRiderAccountListRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.BatchQueryDeliveryOrderResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.StoreDeliveryMonitorResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.StoreRiderAccountListResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.request.UserContext;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.retry.RetryTemplateUtil;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import com.sankuai.shangou.logistics.delivery.common.wrapper.OswClient;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.delivery.monitoring.enums.RiderChangeExceptionEnum;
import com.sankuai.shangou.logistics.delivery.monitoring.utils.MccUtils;
import com.sankuai.shangou.logistics.delivery.monitoring.utils.RiderMonitorBuilder;
import com.sankuai.shangou.logistics.delivery.monitoring.vo.LimitItemVO;
import com.sankuai.shangou.logistics.delivery.monitoring.vo.QueryShopDeliveryMonitoringResponse;
import com.sankuai.shangou.logistics.delivery.monitoring.vo.QueryShopRiderAccountListResponse;
import com.sankuai.shangou.logistics.delivery.monitoring.vo.RiderAccountInfoVO;
import com.sankuai.shangou.logistics.delivery.utils.LimitTakeOrderUtils;
import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.MaterialTransInfoDto;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.request.RiderChangeTransMaterialRequest;
import com.sankuai.shangou.logistics.warehouse.enums.PromotionMaterialType;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 骑手管理 Facade.
 *
 * <AUTHOR>
 * @since 2021/12/6 19:36
 */
@Service
@Slf4j
public class RiderManageClient {

    @Resource
    private RiderQueryThriftService riderQueryThriftService;

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    @Resource
    private OfwClient ofwClient;

    @Resource
    private RiderOperateThriftService riderOperateThriftService;

    @Resource
    private OswClient oswClient;

    @Resource
    private TradeShippingOrderService tradeShippingOrderService;

    @Resource
    private LaborManagementServiceWrapper laborManagementServiceWrapper;

    @Resource
    private RiderLimitAcceptOrderWrapper riderLimitAcceptOrderWrapper;

    @Resource
    private OutboundServiceWrapper outboundServiceWrapper;
    @Resource
    private DepotGoodsWrapper depotGoodsWrapper;

    @MafkaProducer(namespace = "com.sankuai.mafka.castle.daojiacommon",
            appkey = "com.sankuai.shangou.supplychain.ofapp",
            topic = "rider_change_trans_material_stock_topic")
    private IProducerProcessor<Object, String> riderChangeTransStockProducer;

    //支付时间默认查询间隔 单位:天
    private static final int DEFAULT_PAY_TIME_QUERY_DURATION = 60;

    /**
     * 门店配送信息监控接口.
     *
     * @param shopId    门店 ID
     * @param tenantId 租户 ID
     * @param appId    app ID
     * @return QueryShopDeliveryMonitoringResponse 骑手和订单相关信息
     */
    public QueryShopDeliveryMonitoringResponse shopDeliveryMonitor(Long tenantId, Long shopId, Integer appId, Boolean filterThirdDelivery) {
        StoreDeliveryMonitorRequest tRequest = new StoreDeliveryMonitorRequest(tenantId, shopId, appId);
        StoreDeliveryMonitorResponse response;
        try {
            response = riderQueryThriftService.storeDeliveryMonitor(tRequest);
            log.info("RiderQueryThriftService#shopDeliveryMonitor success. request:{}, response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("RiderQueryThriftService#shopDeliveryMonitor error. request:{}", tRequest, e);
            throw new BizException("查询运单失败");
        }
        if (response == null || response.getStatus() == null || response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            String errorMsg = Optional.ofNullable(response).map(StoreDeliveryMonitorResponse::getStatus).map(Status::getMsg).orElse(
                    "系统异常，查询骑手大屏信息失败，请稍后再试");
            throw new BizException(errorMsg);
        }

        //查封签标签
        Map<OfwClient.OrderKey, Boolean> sealTagMap = Collections.emptyMap();
        if(GrayConfigUtils.judgeIsGrayStore(tenantId, shopId, GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false)) {
            List<OfwClient.OrderKey> orderKeys = response.getDeliveryOrderMonitorInfoList().stream().map(tDeliveryOrder -> {
                return new OfwClient.OrderKey(tDeliveryOrder.getChannelOrderId(), tDeliveryOrder.getOrderBizTypeCode());
            }).collect(Collectors.toList());
            sealTagMap = ofwClient.batchGetSealDeliveryTag(tenantId, shopId, orderKeys);
        }

        return RiderMonitorBuilder.buildShopDeliveryMonitoringResp(response.getDeliveryOrderMonitorInfoList(),
                response.getRiderMonitorInfoList(),
                queryOrderInfo(convertToViewIdCondition(response.getDeliveryOrderMonitorInfoList())),
                !Objects.isNull(filterThirdDelivery) && filterThirdDelivery,
                sealTagMap);
    }

    /**
     * 将数据库的门店信息进行转换，适配queryOrderByViewIdCondition接口
     *
     */
    private List<ViewIdCondition> convertToViewIdCondition(List<TRiderDeliveryOrderMonitorInfo> deliverOrderList) {
        return Optional.ofNullable(deliverOrderList).orElse(Collections.emptyList())
                .stream()
                .map(deliverOrder -> new ViewIdCondition(deliverOrder.getOrderBizTypeCode(), deliverOrder.getChannelOrderId()))
                .collect(Collectors.toList());
    }


    /**
     * 分页查询订单信息
     * 该接口对应的是ES，需要分页查询，页大小目前要求20
     *
     */
    public List<OCMSOrderVO> queryOrderInfo(List<ViewIdCondition> deliverOrderList) {

        List<List<ViewIdCondition>> splitOrderVOs = Lists.partition(deliverOrderList, 20);;
        List<List<OCMSOrderVO>> ocmsOrders = splitOrderVOs.stream().map(
                orders -> {
                    OCMSListViewIdConditionRequest request = new OCMSListViewIdConditionRequest();
                    request.setSortField(SortFieldEnum.ORDER_CREATE_TIME);
                    request.setSort(SortByEnum.DESC);
                    request.setViewIdConditionList(orders);
                    request.setTenantId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
                    request.setNeedSortedTags(Boolean.TRUE);
                    OCMSListViewIdConditionResponse response = ocmsQueryThriftService.queryOrderByViewIdCondition(request);
                    if (response == null || response.getStatus() == null ||
                            response.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
                        throw new BizException("订单查询失败");
                    }
                    return response.getOcmsOrderList();
                }
        ).collect(Collectors.toList());
        return ocmsOrders.stream().collect(ArrayList::new, ArrayList::addAll, ArrayList::addAll);
    }


    /**
     * 改派骑手
     * @param req http请求
     * @param tenantId 租户id
     * @param operatorId 操作人id
     */
    public void riderChange(com.sankuai.shangou.logistics.delivery.monitoring.request.RiderChangeRequest req,
                            Long tenantId, Long operatorId) {

        //查骑手信息
        Map<Long, EmployeeDTO> employeeDTOMap = oswClient.batchQueryEmployeeInfo(tenantId, Collections.singletonList(req.getRiderAccountId()));
        if (!employeeDTOMap.containsKey(req.getRiderAccountId())) {
            throw new BizException("转单失败,未查询到骑手信息");
        }
        EmployeeDTO newRider = employeeDTOMap.get(req.getRiderAccountId());

        boolean isGrayStore = GrayConfigUtils.judgeIsGrayStore(tenantId, req.getShopId(), GrayKeyEnum.LIMIT_ACCEPT_ORDER.getGrayKey());
        if (isGrayStore) {
            // 查询骑手是否被限制接单
            List<LimitItemDTO> limitItemList = Collections.emptyList();
            try {
                limitItemList = riderLimitAcceptOrderWrapper.querySingleRiderLimitAcceptOrderInfo(tenantId, req.getRiderAccountId(), req.getShopId());
            } catch (Exception e) {
                log.error("查询限制接单信息失败", e);
                Cat.logEvent("DEAL_RIDER_LIMIT_RESULT", "ERROR");
            }

            if (CollectionUtils.isNotEmpty(limitItemList)) {
                List<String> msg = limitItemList.stream()
                        .filter(Objects::nonNull)
                        .filter(LimitTakeOrderUtils::checkCurrentIsLimit)
                        .map(LimitItemDTO::getReason)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(msg)) {
                    String errMsgFormat = "骑手已被限制接单，原因：%s。";
                    String errMsg = String.format(errMsgFormat, Joiner.on("、")
                            .join(msg));
                    throw new BizException(RiderChangeExceptionEnum.LIMIT_ACCEPT_ORDER.getCode(), errMsg);
                }
            }
        }

        //转配送
        RiderChangeRequest request = new RiderChangeRequest(tenantId,
                req.getShopId(),
                req.getOrderId(),
                operatorId,
                req.getRiderAccountId(),
                newRider.getEmpName(),
                newRider.getPhoneNumber(), null);
        RiderChangeResponse response = null;
        try {
            response = riderOperateThriftService.riderChange(request);
            log.info("RiderOperateThriftService.riderChange success, request: {}, response: {}", request, response);
        } catch (Exception e) {
            log.error("riderOperateThriftService.riderChange error, request: {}", request, e);
            throw new ThirdPartyException("系统异常,改派骑手操作失败,请稍后再试");
        }

        if (response == null || response.getStatus() == null) {
            throw new BizException("系统异常,改派骑手操作失败,请稍后再试");
        }
        if (response.getStatus().getCode() != Status.SUCCESS.getCode()) {
            String errorMsg = StringUtils.isNotBlank(response.getStatus().getMsg()) ? response.getStatus().getMsg() : "系统异常,改派骑手操作失败,请稍后再试";
            throw new BizException(errorMsg);
        }

        //转拣货单
        ShippingAndDeliveryOrderPair shippingAndDeliveryOrderPair = tryToTransPickTask(req, newRider);

        try {
            //NOTE: 这里幂等分两个部分，一个是前端重复点击/超时，导致重复请求；另一个是后端ofapp调oio超时重试，导致重复请求；
            //对于前者，判断每次转单是否真正成功了，注意这里仍有几率校验失败(select-update)，但概率较低，先不处理；
            //对于后者，只要保证重试时，传入的timestamp一致即可；
            long timestamp = System.currentTimeMillis();
            boolean isPickFinish = Objects.nonNull(shippingAndDeliveryOrderPair.getTradeShippingOrderDTO()) && Objects.equals(shippingAndDeliveryOrderPair.getTradeShippingOrderDTO().getStatus(), TradeShippingOrderStatus.FINISH.getCode());
            boolean isTakenGoods = Objects.nonNull(shippingAndDeliveryOrderPair.getTRiderDeliveryOrder()) && shippingAndDeliveryOrderPair.getTRiderDeliveryOrder().getDeliveryStatus() >= DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode();
            //如果不是重复点击 && 拣货任务已经完成
            if (Objects.nonNull(response.getIsSameRider()) && !response.getIsSameRider() && isPickFinish && isTakenGoods) {
                RiderChangeTransMaterialRequest riderChangeTransMaterialRequest = buildRiderChangeTransMaterialRequest(req, shippingAndDeliveryOrderPair.getTradeShippingOrderDTO(), timestamp, response.getBeforeRiderAccountId());
                Optional<MaterialTransInfoDto> materialTransInfoDto = outboundServiceWrapper.transMaterialStockWhenRiderChange(riderChangeTransMaterialRequest);
                if (materialTransInfoDto.isPresent() && materialTransInfoDto.get().getOperateCount() > 0) {
                    RiderChangeTransStockMsg riderChangeTransStockMessage = buildRiderChangeTransStockMsg(newRider, shippingAndDeliveryOrderPair.getTRiderDeliveryOrder(), materialTransInfoDto.get(), response);
                    riderChangeTransStockProducer.sendMessage(JSON.toJSONString(riderChangeTransStockMessage));
                }
            }
        } catch (Exception e) {
            log.error("骑手转单后转移库存失败", e);
            Cat.logEvent("FORTUNE_WINE", "RIDER_CHANGE_TRANS_STOCK_FAIL");
        }
    }

    private RiderChangeTransStockMsg buildRiderChangeTransStockMsg(EmployeeDTO newRider, TRiderDeliveryOrder tRiderDeliveryOrder, MaterialTransInfoDto materialTransInfoDto, RiderChangeResponse riderChangeResponse) {
        Map<String, DepotGoodsDetailDto> skuMap = depotGoodsWrapper.queryBySkuId(tRiderDeliveryOrder.getTenantId(), tRiderDeliveryOrder.getStoreId(), Lists.newArrayList(materialTransInfoDto.getMaterialSkuId()));
        if (MapUtils.isEmpty(skuMap) || !skuMap.containsKey(materialTransInfoDto.getMaterialSkuId())) {
            log.error("查询转移货品信息信息失败");
        }
        DepotGoodsDetailDto depotGoodsDetailDto = skuMap.get(materialTransInfoDto.getMaterialSkuId());
        RiderChangeTransStockMsg riderChangeTransStockMessage = new RiderChangeTransStockMsg();
        riderChangeTransStockMessage.setTenantId(tRiderDeliveryOrder.getTenantId());
        riderChangeTransStockMessage.setStoreId(tRiderDeliveryOrder.getStoreId());
        riderChangeTransStockMessage.setChannelOrderId(tRiderDeliveryOrder.getChannelOrderId());
        riderChangeTransStockMessage.setOrderBizType(tRiderDeliveryOrder.getOrderBizTypeCode());
        riderChangeTransStockMessage.setBeforeRiderAccountId(riderChangeResponse.getBeforeRiderAccountId());
        riderChangeTransStockMessage.setBeforeRiderName(riderChangeResponse.getBeforeRiderName());
        riderChangeTransStockMessage.setAfterRiderAccountId(newRider.getAccountId());
        riderChangeTransStockMessage.setAfterRiderName(newRider.getEmpName());
        riderChangeTransStockMessage.setTransStockMaterialInfo(
                new RiderChangeTransStockMsg.TransStockMaterialInfo(
                        materialTransInfoDto.getMaterialSkuId(), depotGoodsDetailDto.getGoodsName(),materialTransInfoDto.getOperateCount(),
                        Objects.nonNull(depotGoodsDetailDto.getGoodsPic()) && org.apache.commons.collections.CollectionUtils.isNotEmpty(depotGoodsDetailDto.getGoodsPic().getRealPicUrlList()) ? depotGoodsDetailDto.getGoodsPic().getRealPicUrlList().get(0) : StringUtils.EMPTY,
                        depotGoodsDetailDto.getSpecName()
                )
        );
        riderChangeTransStockMessage.setOperateTime(System.currentTimeMillis());
        return riderChangeTransStockMessage;
    }

    private RiderChangeTransMaterialRequest buildRiderChangeTransMaterialRequest(com.sankuai.shangou.logistics.delivery.monitoring.request.RiderChangeRequest req,
                                                                                 TradeShippingOrderDTO tradeShippingOrderDTO,
                                                                                 long timestamp, Long beforeRiderAccountId) {
        RiderChangeTransMaterialRequest riderChangeTransMaterialRequest = new RiderChangeTransMaterialRequest();
        riderChangeTransMaterialRequest.setWarehouseId(tradeShippingOrderDTO.getWarehouseId());
        riderChangeTransMaterialRequest.setTradeOrderNo(tradeShippingOrderDTO.getTradeOrderNo());
        riderChangeTransMaterialRequest.setTradeChannelType(tradeShippingOrderDTO.getTradeChannelType());
        riderChangeTransMaterialRequest.setOldRiderAccountId(beforeRiderAccountId);
        riderChangeTransMaterialRequest.setNewRiderAccountId(req.getRiderAccountId());
        riderChangeTransMaterialRequest.setMaterialType(Lists.newArrayList(PromotionMaterialType.FORTUNE_WINE.getCode()));
        riderChangeTransMaterialRequest.setOperateTimeStamp(timestamp);
        riderChangeTransMaterialRequest.setUserContext(new UserContext(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(), WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountId(), WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountName(), WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName(), WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeId()));
        return riderChangeTransMaterialRequest;
    }

    private ShippingAndDeliveryOrderPair tryToTransPickTask(com.sankuai.shangou.logistics.delivery.monitoring.request.RiderChangeRequest request,
                                    EmployeeDTO newRider) {
        try {
            ShippingAndDeliveryOrderPair pair = new ShippingAndDeliveryOrderPair();
            long storeId = request.getShopId();
            if (MccUtils.acceptPickOrderSwitch(storeId)) {
                BatchQueryDeliveryOrderResponse response = RetryTemplateUtil.simpleWithFixedRetry(3, 100).execute(new RetryCallback<BatchQueryDeliveryOrderResponse, Exception>() {
                    @Override
                    public BatchQueryDeliveryOrderResponse doWithRetry(RetryContext context) throws Exception {
                        BatchQueryDeliveryOrderResponse response = riderQueryThriftService.queryDeliveryOrderByOrderIdListV2(Collections.singletonList(request.getOrderId()));
                        log.info("end invoke riderQueryThriftService.queryDeliveryOrderByOrderIdListV2, orderId: {}, response: {}", request.getOrderId(), JSON.toJSONString(response));
                        return response;
                    }
                });

                if (CollectionUtils.isEmpty(response.getTRiderDeliveryOrders())) {
                    log.warn("无生效中的运单");
                    return pair;
                }
                TRiderDeliveryOrder tRiderDeliveryOrder = response.getTRiderDeliveryOrders().get(0);
                pair.setTRiderDeliveryOrder(tRiderDeliveryOrder);


                //拣货完成不转拣货单
                TResult<List<TradeShippingOrderDTO>> tradeOrderResult = RetryTemplateUtil.simpleWithFixedRetry(3, 100)
                        .execute(new RetryCallback<TResult<List<TradeShippingOrderDTO>>, Exception>() {
                            @Override
                            public TResult<List<TradeShippingOrderDTO>> doWithRetry(RetryContext context) throws Exception {
                                TResult<List<TradeShippingOrderDTO>> tradeOrderNos = tradeShippingOrderService.getByTradeOrderNos(tRiderDeliveryOrder.getStoreId(),
                                        tRiderDeliveryOrder.getOrderBizTypeCode(), Collections.singletonList(tRiderDeliveryOrder.getChannelOrderId()));
                                log.info("end invoke tradeShippingOrderService.getByTradeOrderNos, tradeOrderNo: {}, response: {}", tRiderDeliveryOrder.getChannelOrderId(), tradeOrderNos);
                                return tradeOrderNos;
                            }
                        });

                if (tradeOrderResult.isSuccess() && CollectionUtils.isNotEmpty(tradeOrderResult.getData())) {
                    TradeShippingOrderDTO shippingOrderDTO = tradeOrderResult.getData().get(0);
                    pair.setTradeShippingOrderDTO(shippingOrderDTO);
                    if (Objects.equals(shippingOrderDTO.getStatus(), TradeShippingOrderStatus.FINISH.getCode())) {
                        return pair;
                    }
                }

                //过滤拣配分离订单
                if (Objects.equals(tRiderDeliveryOrder.getPickDeliverySplitTag(), true)) {
                    return pair;
                }

                TResult<Void> tResult = RetryTemplateUtil.simpleWithFixedRetry(3, 100).execute(new RetryCallback<TResult<Void>, Exception>() {
                    @Override
                    public TResult<Void> doWithRetry(RetryContext context) throws Exception {
                        TResult<Void> tResult = tradeShippingOrderService.changeOperator(tRiderDeliveryOrder.getStoreId(),
                                tRiderDeliveryOrder.getChannelOrderId(),
                                tRiderDeliveryOrder.getOrderBizTypeCode(),
                                request.getRiderAccountId(),
                                newRider.getEmpName());
                        log.info("end invoke tradeShippingOrderService.changeOperator, tradeOrderNo: {}, response: {}", tRiderDeliveryOrder.getChannelOrderId(), tResult);
                        return tResult;
                    }
                });

                if (!tResult.isSuccess()) {
                    if (Objects.equals(tResult.getCode(), com.sankuai.shangou.logistics.warehouse.enums.ResponseCodeEnum.SHIP_TASK_ALREADY_FINISH.getCode())) {
                        log.warn("拣货任务已结束,不报错");
                        return pair;
                    }

                    throw new BizException("转拣货任务失败," + tResult.getMsg());
                }
            }
            return pair;
        } catch (Exception e) {
            log.error("转拣货任务失败", e);
            Cat.logEvent("PICK_DELIVERY_SPLIT", "TRANS_PICK_TASK_FAIL");
            return new ShippingAndDeliveryOrderPair();
        }
    }


    /**
     * 查询门店下骑手名单（具有门店权限的店员/店长/兼职骑手）
     * @param tenantId 租户id
     * @param shopId 门店id
     * @param appId appId
     */
    public QueryShopRiderAccountListResponse shopRiderAccountList(Long tenantId, Long shopId, Integer appId) {
        ParamCheckUtils.nullCheck(tenantId, "查询门店下骑手名单，租户 ID 无效");
        ParamCheckUtils.nullCheck(shopId, "查询门店下骑手名单，门店 ID 无效");
        ParamCheckUtils.nullCheck(appId, "查询门店下骑手名单，应用 APP ID 无效");
        StoreRiderAccountListRequest request = new StoreRiderAccountListRequest(tenantId, shopId, appId);
        StoreRiderAccountListResponse response = null;
        try {
            response = riderQueryThriftService.storeRiderAccountList(request);
            log.info("riderQueryThriftService.storeRiderAccountList success, request: {}, response: {}", request, response);
        } catch (Exception e) {
            log.error("riderQueryThriftService.storeRiderAccountList error, request: {}", request, e);
            throw new ThirdPartyException("不好意思出错了，请稍候再试");
        }

        if (response == null || response.getStatus() == null) {
            throw new BizException("系统异常,查询门店骑手名单失败,请稍后再试");
        }
        if (response.getStatus().getCode() != Status.SUCCESS.getCode()) {
            String errorMsg = StringUtils.isNotBlank(response.getStatus().getMsg()) ? response.getStatus().getMsg() : "系统异常,查询门店骑手名单失败,请稍后再试";
            throw new BizException(errorMsg);
        }

        if (CollectionUtils.isEmpty(response.getRiderList())) {
            return new QueryShopRiderAccountListResponse(Lists.newArrayList());
        }

        //因为培训不通过会阻止转单 所以这里默认为培训通过 防止影响配送效率
        List<RiderAccountInfoVO> accountInfos = response.getRiderList().stream()
                .map(staffRider -> new RiderAccountInfoVO(
                        staffRider.getAccountId(),
                        staffRider.getName(),
                        staffRider.getPhone(),
                        isTempRider(staffRider),
                        true,
                        Lists.newArrayList()))
                .collect(Collectors.toList());

        return new QueryShopRiderAccountListResponse(accountInfos);
    }


    /**
     * 判断账号是否为临时骑手账号
     * 临时账号的判断条件：账号类型为外部账号 && 角色包含临时骑手角色
     * @param tStaffRider 骑手信息
     * @return 是否为临时骑手账号
     */
    public static Boolean isTempRider(TStaffRider tStaffRider) {
        Objects.requireNonNull(tStaffRider);

        if (Objects.isNull(tStaffRider.getAccountType()) ||
                CollectionUtils.isEmpty(tStaffRider.getRoleIdList())) {
            return false;
        }

        return tStaffRider.getAccountType() == AccountTypeEnum.OUTSOURCING.getValue()
                && tStaffRider.getRoleIdList().stream()
                .anyMatch(roleId -> MccUtils.tempRiderRoleIds().contains(roleId.toString()));


    }


    public void appendTrainingResult(Long tenantId, Long storeId, QueryShopRiderAccountListResponse response) {
        try {
            if (!MccUtils.isDhTenant(tenantId) || !MccUtils.isTrainingGrayStore(storeId)) {
                log.info("非歪马租户或非培训灰度门店，不处理骑手培训信息");
                return;
            }

            if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getRiderAccountList())) {
                log.info("骑手信息为空，不处理骑手培训信息");
                return;
            }
            List<Long> riderAccountIds = response.getRiderAccountList()
                    .stream()
                    .map(RiderAccountInfoVO::getRiderAccountId)
                    .collect(Collectors.toList());
            Map<Long, Boolean> trainingResultMap = laborManagementServiceWrapper.batchQueryTrainingResult(tenantId, riderAccountIds);

            //因为培训不通过会阻止转单 所以这里默认为培训通过 防止影响配送效率
            for (RiderAccountInfoVO selfRiderInfo : response.getRiderAccountList()) {
                selfRiderInfo.setIsCompleteTrain(trainingResultMap.getOrDefault(selfRiderInfo.getRiderAccountId(), Boolean.TRUE));
            }

        } catch (Exception e) {
            log.error("处理骑手是否通过培训信息失败", e);
            Cat.logEvent("DEAL_RIDER_TRAINING_RESULT", "ERROR");
        }
    }

    public void appendLimitInfoResult(Long tenantId, QueryShopRiderAccountListResponse response) {
        appendLimitInfoResultNew(tenantId, response, null);
    }

    /**
     * 批量查询骑手限制接单信息
     *
     * @param tenantId 租户id
     * @param response 骑手信息
     */
    public void appendLimitInfoResultNew(Long tenantId, QueryShopRiderAccountListResponse response, Long storeId) {
        try {
            if (!MccUtils.isDhTenant(tenantId)) {
                log.info("非歪马租户，不处理骑手限制接单信息");
                return;
            }

            if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getRiderAccountList())) {
                log.info("骑手信息为空，不处理骑手限制接单信息");
                return;
            }
            List<Long> riderAccountIdList = response.getRiderAccountList()
                    .stream()
                    .map(RiderAccountInfoVO::getRiderAccountId)
                    .collect(Collectors.toList());

            // 批量查询骑手受限信息
            Map<Long, List<LimitItemDTO>> riderLimitMap =
                    riderLimitAcceptOrderWrapper.batchQueryRiderLimitAcceptOrderInfo(tenantId, riderAccountIdList, storeId);


            response.getRiderAccountList().forEach(vo -> {
                List<LimitItemVO> limitItemList = Optional.ofNullable(riderLimitMap.get(vo.getRiderAccountId()))
                        .orElseGet(Lists::newArrayList)
                        .stream()
                        .filter(Objects::nonNull)
                        .filter(LimitTakeOrderUtils::checkCurrentIsLimit)
                        .map(LimitItemVO::convertToLimitItemVO)
                        .collect(Collectors.toList());
                vo.setLimitItemList(limitItemList);
            });

        } catch (Exception e) {
            log.error("处理骑手限制接单信息失败", e);
            Cat.logEvent("DEAL_RIDER_LIMIT_RESULT", "ERROR");
        }
    }

    @Data
    public static class ShippingAndDeliveryOrderPair {
        @Nullable
        private TradeShippingOrderDTO tradeShippingOrderDTO;

        private TRiderDeliveryOrder tRiderDeliveryOrder;
    }

}
