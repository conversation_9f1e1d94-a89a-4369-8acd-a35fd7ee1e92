package com.sankuai.shangou.logistics.delivery.monitoring.enums;

/**
 * 骑手转单异常枚举
 * <AUTHOR>
 * @since 2024/11/07 12:00
 */
public enum RiderChangeExceptionEnum {
    LIMIT_ACCEPT_ORDER(20000102, "限制接单");


    private final int code;

    private final String message;

    RiderChangeExceptionEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
