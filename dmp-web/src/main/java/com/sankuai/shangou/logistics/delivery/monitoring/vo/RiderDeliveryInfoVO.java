package com.sankuai.shangou.logistics.delivery.monitoring.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-12-1
 * @Desc 骑手监控大屏-骑手信息
 */
@TypeDoc(
        description = "骑手监控大屏-骑手信息"
)
@ApiModel("骑手监控大屏-骑手信息")
@Data
@Builder
public class RiderDeliveryInfoVO {

    @FieldDoc(
            description = "骑手账号 ID"
    )
    @ApiModelProperty(value = "骑手账号 ID", required = true)
    private Long riderAccountId;

    @FieldDoc(
            description = "骑手名字"
    )
    @ApiModelProperty(value = "骑手名字", required = true)
    private String riderName;

    @FieldDoc(
            description = "骑手电话"
    )
    @ApiModelProperty(value = "骑手电话", required = true)
    private String riderPhone;

    @FieldDoc(
            description = "是否为临时骑手"
    )
    @ApiModelProperty(value = "是否为临时骑手", required = true)
    private Boolean tempRiderFlag;

    @FieldDoc(
            description = "骑手状态"
    )
    @ApiModelProperty(value = "骑手状态", required = true)
    private Integer riderStatus;

    @FieldDoc(
            description = "骑手状态描述"
    )
    @ApiModelProperty(value = "骑手状态描述", required = true)
    private String riderStatusDesc;

    @FieldDoc(
            description = "骑手定位经纬度"
    )
    @ApiModelProperty(value = "骑手定位经纬度", required = true)
    private DeliveryCoordinateVO riderLocation;

    @FieldDoc(
            description = "骑手定位异常原因"
    )
    @ApiModelProperty(value = "骑手定位异常原因")
    private RiderLocatingExceptionVO riderLocatingExceptionVO;


    @FieldDoc(
            description = "关联运单信息"
    )
    @ApiModelProperty(value = "关联运单信息", required = true)
    private List<RiderDeliveryOrderMonitoringVO> orderInfoList;

}
