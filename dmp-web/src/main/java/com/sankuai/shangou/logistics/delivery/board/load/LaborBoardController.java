package com.sankuai.shangou.logistics.delivery.board.load;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Maps;
import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.board.load.enums.CompareTypeEnum;
import com.sankuai.shangou.logistics.delivery.board.load.enums.HistogramGroupEnum;
import com.sankuai.shangou.logistics.delivery.board.load.vo.DeliveryLoadDataExcelVO;
import com.sankuai.shangou.logistics.delivery.board.load.vo.DeliveryLoadDataPointVO;
import com.sankuai.shangou.logistics.delivery.board.load.vo.DeliveryLoadGraphDataVO;
import com.sankuai.shangou.logistics.delivery.board.load.vo.DeliveryLoadGraphFrameVO;
import com.sankuai.shangou.logistics.delivery.board.load.wrapper.TenantServiceWrapper;
import com.sankuai.shangou.logistics.delivery.indicator.dto.BizIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorConstants;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorDTO;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorGroupType;
import com.sankuai.shangou.logistics.delivery.indicator.service.IndicatorReader;
import com.sankuai.shangou.logistics.delivery.indicator.service.OmTimetable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@InterfaceDoc(
        displayName = "自营骑手配送负载数据看板",
        type = "restful",
        scenarios = "包含骑手日维度全量、增量数据查询接口",
        description = "包含骑手日维度全量、增量数据查询接口",
        host = "https://fnsaas.waimai.meituan.com/"
)
@Slf4j
@RestController
@RequestMapping("dmp/api/board/delivery-load")
public class LaborBoardController {
    @Resource
    private IndicatorReader indicatorReader;

    @Resource
    private TenantServiceWrapper tenantServiceWrapper;

    //单位: 分钟
    private static final Integer BIZ_INTERVAL = 10;

    public static List<BizIndicatorEnum> histogramIndicatorEnums = new ArrayList<>();

    public static List<BizIndicatorEnum> lineIndicatorEnums = new ArrayList<>();

    private static Map<HistogramGroupEnum, List<BizIndicatorEnum>> bizIndicatorGroupMap = new HashMap<>();

    private static String DEFAULT_COLOR = "#FFD700";

    static {
        histogramIndicatorEnums.add(BizIndicatorEnum.WAIT_ACCEPT_ORDER_COUNT);
        histogramIndicatorEnums.add(BizIndicatorEnum.WAIT_PICK_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT);
        histogramIndicatorEnums.add(BizIndicatorEnum.WAIT_PICK_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT);
        histogramIndicatorEnums.add(BizIndicatorEnum.DELIVERED_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT);
        histogramIndicatorEnums.add(BizIndicatorEnum.DELIVERED_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT);
        histogramIndicatorEnums.add(BizIndicatorEnum.SCHEDULED_EMPLOYEE_COUNT);
        histogramIndicatorEnums.add(BizIndicatorEnum.SCHEDULED_ATTENDANCE_EMPLOYEE_COUNT);
        histogramIndicatorEnums.add(BizIndicatorEnum.UNSCHEDULED_ATTENDANCE_EMPLOYEE_COUNT);

        lineIndicatorEnums.add(BizIndicatorEnum.ATTENDANCE_DELIVERY_LOAD);
        lineIndicatorEnums.add(BizIndicatorEnum.SCHEDULED_EMPLOYEE_DELIVERY_LOAD_INCLUDE_UNSCHEDULED_ORDER);
        lineIndicatorEnums.add(BizIndicatorEnum.SCHEDULED_EMPLOYEE_DELIVERY_LOAD_EXCLUDE_UNSCHEDULED_ORDER);
        lineIndicatorEnums.add(BizIndicatorEnum.FULFILL_EMPLOYEE_DELIVERY_LOAD_INCLUDE_WAIT_ACCEPT_ORDER);
        lineIndicatorEnums.add(BizIndicatorEnum.FULFILL_EMPLOYEE_DELIVERY_LOAD_EXCLUDE_WAIT_ACCEPT_ORDER);

        //只有导出时才会用到
        lineIndicatorEnums.add(BizIndicatorEnum.FULFILLING_EMPLOYEE_COUNT);

        bizIndicatorGroupMap.put(HistogramGroupEnum.ORDER_DISTRIBUTION_GROUP, Arrays.asList(BizIndicatorEnum.WAIT_ACCEPT_ORDER_COUNT,
                BizIndicatorEnum.WAIT_PICK_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT,
                BizIndicatorEnum.WAIT_PICK_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT,
                BizIndicatorEnum.DELIVERED_BY_UNSCHEDULED_EMPLOYEE_ORDER_COUNT,
                BizIndicatorEnum.DELIVERED_BY_SCHEDULED_EMPLOYEE_ORDER_COUNT));
        bizIndicatorGroupMap.put(HistogramGroupEnum.SCHEDULED_EMPLOYEE_DISTRIBUTION_GROUP, Collections.singletonList(BizIndicatorEnum.SCHEDULED_EMPLOYEE_COUNT));
        bizIndicatorGroupMap.put(HistogramGroupEnum.ATTENDANCE_EMPLOYEE_DISTRIBUTION_GROUP, Arrays.asList(BizIndicatorEnum.UNSCHEDULED_ATTENDANCE_EMPLOYEE_COUNT,
                BizIndicatorEnum.SCHEDULED_ATTENDANCE_EMPLOYEE_COUNT));
    }

    @MethodDoc(
            displayName = "自营骑手日维度配送负载-全量数据",
            description = "自营骑手日维度配送负载-全量数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询天维度全量配送负载数据请求",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/board/delivery-load/full-data",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/full-data")
    @ResponseBody
    @MethodLog(logResponse = true, logRequest = true)
    public Map<String, Object> queryFullData(@RequestParam Long storeId, @RequestParam String date,
                                    @RequestParam Integer compareType, @RequestParam(required = false) List<String> indicatorCodes) {
        DeliveryLoadGraphFrameVO graphFrameVO = readerDeliveryLoadGraphFrameVO();
        DeliveryLoadGraphDataVO graphDataVO = readerAllIndicator(storeId, date,
                compareType, indicatorCodes);
        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put("graphFrameVO", graphFrameVO);
        map.put("graphDataVO", graphDataVO);
        return map;
    }

    @MethodDoc(
            displayName = "自营骑手日维度配送负载-增量数据",
            description = "自营骑手日维度配送负载-增量数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询天维度增量配送负载数据请求",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/board/delivery-load/incremental-data",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/incremental-data")
    @MethodLog(logResponse = true, logRequest = true)
    public Object queryIncrementalData(@RequestParam Long storeId, @RequestParam(required = false) String lastPointBizTime,
                                           @RequestParam(required = false) List<String> indicatorCodes) {
        DeliveryLoadGraphDataVO deliveryLoadGraphDataVO = readerIncrementIndicator(storeId,
                lastPointBizTime, indicatorCodes);
        return deliveryLoadGraphDataVO;
    }


    @MethodDoc(
            displayName = "自营骑手多日维度配送负载-导出",
            description = "自营骑手多日维度配送负载-导出",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自营骑手多日维度配送负载导出请求",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dpm/board/delivery-load/export",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/export")
    public void export(@RequestParam Long storeId, @RequestParam String beginDate, @RequestParam String endDate,
                               @RequestParam(required = false) List<String> indicatorCodes, HttpServletResponse response) throws IOException {
        log.info("LaborBoardController.export, storeId: {}, beginDate: {} , endDate: {}, indicatorCodes: {}", storeId, beginDate, endDate, indicatorCodes);
        if ((Long.parseLong(endDate) - Long.parseLong(beginDate)) / 1000 / 60 / 60 / 24 >= 93) {
            throw new IllegalArgumentException("时间间隔不能超过3个月");
        }
        SXSSFWorkbook workbook = null;
        try {
            workbook = exportDeliveryLoadData(storeId, beginDate, endDate, indicatorCodes);
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            String fileName = String.format("导出门店实时履约负载数据-%s.xlsx", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
            OutputStream os = response.getOutputStream();
            workbook.write(os);
        } finally {
            if (Objects.nonNull(workbook)) {
                workbook.dispose();
            }
        }

    }

    private SXSSFWorkbook exportDeliveryLoadData(Long storeId, String beginDate, String endDate, List<String> indicatorCodes) {
        if (indicatorCodes == null) {
            indicatorCodes = Collections.emptyList();
        }

        //如果需要导出履约中骑手负载 需要把负载人数也查出来
        if ((indicatorCodes.contains(BizIndicatorEnum.FULFILL_EMPLOYEE_DELIVERY_LOAD_INCLUDE_WAIT_ACCEPT_ORDER.getIndicatorCode())
                || indicatorCodes.contains(BizIndicatorEnum.FULFILL_EMPLOYEE_DELIVERY_LOAD_EXCLUDE_WAIT_ACCEPT_ORDER.getIndicatorCode()))) {
            indicatorCodes.add(BizIndicatorEnum.FULFILLING_EMPLOYEE_COUNT.getIndicatorCode());
        }

        DeliveryLoadGraphDataVO deliveryLoadGraphDataVO = readerMultiDaysIndicator(storeId,
                beginDate, endDate, indicatorCodes);

        Map<Long, PoiInfoDto> poiInfoDtoMap = tenantServiceWrapper.queryPoiByIds(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(), Collections.singletonList(storeId));
        String storeName = Optional.ofNullable(poiInfoDtoMap.get(storeId)).map(PoiInfoDto::getPoiName).orElse("");

        SXSSFWorkbook workbook = new SXSSFWorkbook();
        SXSSFSheet sheet = workbook.createSheet(String.format("导出门店实时履约负载数据-%s.xlsx", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))));
        List<String> header = generateHeaders(indicatorCodes);
        SXSSFRow headerRow = sheet.createRow(0);
        for (int i = 0; i < header.size(); i++) {
            headerRow.createCell(i).setCellValue(header.get(i));
        }

        List<DeliveryLoadDataExcelVO> excelVoList = DeliveryLoadDataExcelVO.build(deliveryLoadGraphDataVO, storeName);

        List<DeliveryLoadDataExcelVO> sortedExcelVo = excelVoList.stream().sorted((o1, o2) -> {
            if (Objects.equals(o1.getDate(), o2.getDate())) {
                return o1.getBizTime().compareTo(o2.getBizTime());
            }

            return o1.getDate().compareTo(o2.getDate());
        }).collect(Collectors.toList());

        int rowIndex = 1;
        for (DeliveryLoadDataExcelVO vo : sortedExcelVo) {
            int colIndex = 0;
            SXSSFRow dataRow = sheet.createRow(rowIndex++);

            dataRow.createCell(colIndex++).setCellValue(vo.getStoreName());
            dataRow.createCell(colIndex++).setCellValue(vo.getDate());
            dataRow.createCell(colIndex++).setCellValue(vo.getBizTime());

            //直方图
            for (BizIndicatorEnum histogramIndicatorEnum : histogramIndicatorEnums) {
                dataRow.createCell(colIndex++).setCellValue(vo.getIndicatorValues().getOrDefault(histogramIndicatorEnum, ""));
            }

            //折线图
            for (String indicatorCode : indicatorCodes) {
                dataRow.createCell(colIndex++).setCellValue(vo.getIndicatorValues().getOrDefault(BizIndicatorEnum.enumOf(indicatorCode), ""));
            }

        }
        ;

        return workbook;
    }

    private List<String> generateHeaders(List<String> bizIndicatorCodes) {
        List<String> headers = new ArrayList<>();
        headers.add("门店名称");
        headers.add("日期");
        headers.add("时段");

        //直方图
        for (BizIndicatorEnum histogramIndicatorEnum : histogramIndicatorEnums) {
            headers.add(histogramIndicatorEnum.getDesc());
        }

        //折线图
        for (String lineIndicator : bizIndicatorCodes) {
            headers.add(BizIndicatorEnum.enumOf(lineIndicator).getDesc());
        }

        return headers;
    }

    private DeliveryLoadGraphDataVO readerAllIndicator(Long storeId, String dateTime, Integer compareType, List<String> indicatorCodes) {
        LocalDate paraDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(dateTime)), ZoneId.systemDefault()).toLocalDate();
        //1.查直方图
        Map<LocalDateTime, List<IndicatorDTO>> histogramIndicator =
                indicatorReader.readBizIndicator(storeId, parseBizTimePoint(paraDate), histogramIndicatorEnums);


        //2.查当日折线图
        Map<LocalDateTime, List<IndicatorDTO>> lineIndicator = new HashMap<>();
        if (CollectionUtils.isNotEmpty(indicatorCodes)) {
            lineIndicator = indicatorReader.readBizIndicator(storeId, parseBizTimePoint(paraDate), indicatorCodes.stream().map(BizIndicatorEnum::enumOf).collect(Collectors.toList()));

        }

        //3.查同比折线图
        Map<LocalDateTime, List<IndicatorDTO>> refLineIndicator = new HashMap<>();
        if (CollectionUtils.isNotEmpty(indicatorCodes)) {
            refLineIndicator = indicatorReader.readBizIndicator(storeId, parseBizTimePoint(parseCompareDate(paraDate, compareType)), indicatorCodes.stream().map(BizIndicatorEnum::enumOf).collect(Collectors.toList()));

        }
        return buildDeliveryLoadGraphDataVO(histogramIndicator, lineIndicator, refLineIndicator, paraDate, compareType);

    }

    private DeliveryLoadGraphDataVO readerMultiDaysIndicator(Long storeId, String beginTime, String endTime, List<String> indicatorCodes) {
        LocalDate beginDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(beginTime)), ZoneId.systemDefault()).toLocalDate();
        LocalDate endDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(endTime)), ZoneId.systemDefault()).toLocalDate();

        //1.查直方图
        Map<LocalDateTime, List<IndicatorDTO>> histogramIndicator =
                indicatorReader.readBizIndicator(storeId, parseBizTimePoint(beginDate, endDate), histogramIndicatorEnums);


        //2.查当日折线图
        Map<LocalDateTime, List<IndicatorDTO>> lineIndicator = new HashMap<>();
        if (CollectionUtils.isNotEmpty(indicatorCodes)) {
            lineIndicator =
                    indicatorReader.readBizIndicator(storeId, parseBizTimePoint(beginDate, endDate), indicatorCodes.stream().map(BizIndicatorEnum::enumOf).collect(Collectors.toList()));

        }


        return buildDeliveryLoadGraphDataVO(histogramIndicator, lineIndicator, Collections.emptyMap(), null, null);

    }

    private DeliveryLoadGraphDataVO readerIncrementIndicator(Long storeId, String lastPointDateTime, List<String> indicatorCodes) {
        LocalTime lastPointLocalTime = Optional.ofNullable(lastPointDateTime)
                .map(item -> LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(item)), ZoneId.systemDefault()).toLocalTime())
                .orElse(null);

        OmTimetable omTimetable = OmTimetable.getInstance(IndicatorConstants.startTime, IndicatorConstants.openTime, IndicatorConstants.closeTime);
        List<LocalDateTime> incrementPointsBizTime = omTimetable.getCurrentPoints(lastPointLocalTime, BIZ_INTERVAL, IndicatorConstants.CAL_INTERVAL);

        //1.查直方图
        Map<LocalDateTime, List<IndicatorDTO>> histogramIndicator =
                indicatorReader.readBizIndicator(storeId, incrementPointsBizTime, histogramIndicatorEnums);


        //2.查当日折线图增量数据
        Map<LocalDateTime, List<IndicatorDTO>> lineIndicator = new HashMap<>();
        if (CollectionUtils.isNotEmpty(indicatorCodes)) {
            lineIndicator = indicatorReader.readBizIndicator(storeId, incrementPointsBizTime, indicatorCodes.stream().map(BizIndicatorEnum::enumOf).collect(Collectors.toList()));
        }


        return buildDeliveryLoadGraphDataVO(histogramIndicator, lineIndicator, Collections.emptyMap(), null, null);

    }

    private List<LocalDateTime> parseBizTimePoint(LocalDate paraDate) {
        List<LocalDateTime> points;
        OmTimetable omTimetable = OmTimetable.getInstance(IndicatorConstants.startTime, IndicatorConstants.openTime, IndicatorConstants.closeTime);
        LocalDate nowDay = omTimetable.getBizDay(null);

        //当日全量数据
        if (paraDate.isBefore(nowDay)) {
            points = omTimetable.getHistoryPoints(paraDate, BIZ_INTERVAL, IndicatorConstants.CAL_INTERVAL);
        } else {
            points = omTimetable.getCurrentPoints(null, BIZ_INTERVAL, IndicatorConstants.CAL_INTERVAL);
        }

        return points;
    }

    private List<LocalDateTime> parseBizTimePoint(LocalDate beginDate, LocalDate endDate) {
        List<LocalDateTime> points = new ArrayList<>();
        LocalDate paraDate = LocalDate.of(beginDate.getYear(), beginDate.getMonth(), beginDate.getDayOfMonth());
        while(paraDate.isBefore(endDate) || paraDate.isEqual(endDate)) {
            points.addAll(parseBizTimePoint(paraDate));
            paraDate = paraDate.plusDays(1);
        }

        return points;
    }

    private LocalDate parseCompareDate(LocalDate paraDate, Integer compareType) {
        CompareTypeEnum compareTypeEnum = CompareTypeEnum.enumOf(compareType);
        if (compareTypeEnum == null) {
            throw new IllegalArgumentException("同比类型不合法");
        }
        switch(compareTypeEnum) {
            case DAILY_COMPARE:
                return paraDate.minusDays(1);
            case WEEKLY_COMPARE:
                return paraDate.minusWeeks(1);
            case MONTHLY_COMPARE:
                return paraDate.minusMonths(1);
            default:
                throw new IllegalArgumentException("同比类型不合法");
        }
    }

    private DeliveryLoadGraphDataVO buildDeliveryLoadGraphDataVO(Map<LocalDateTime, List<IndicatorDTO>> histogramIndicator,
                                                                 Map<LocalDateTime, List<IndicatorDTO>> lineIndicator,
                                                                 Map<LocalDateTime, List<IndicatorDTO>> refLineIndicator,
                                                                 LocalDate paraDay, Integer compareType) {

        //对齐数据起点(如果指标的起点时间不一样 前端绘制时会出错 通过补0的方式对齐起点)
        alignByFillZero(histogramIndicator, lineIndicator, refLineIndicator, paraDay, compareType);


        HashMap<BizIndicatorEnum, HistogramGroupEnum> indicatorEnum2groupEnumMap = new HashMap<>();
        bizIndicatorGroupMap.forEach((key, value) -> value.forEach(indicatorEnum -> {
            indicatorEnum2groupEnumMap.put(indicatorEnum, key);
        }));

        DeliveryLoadGraphDataVO deliveryLoadGraphDataVO = new DeliveryLoadGraphDataVO(new ArrayList<>(), new ArrayList<>(), new ArrayList<>());

        //直方图
        histogramIndicator.forEach((bizTime, indicatorDTOS) -> {
            Map<String, List<IndicatorDTO>> bizCode2IndicatorMap = indicatorDTOS.stream().collect(Collectors.groupingBy(IndicatorDTO::getCode));
            bizCode2IndicatorMap.forEach((code, indicators) -> {
                if (indicatorEnum2groupEnumMap.containsKey(BizIndicatorEnum.enumOf(code))) {
                    deliveryLoadGraphDataVO.getHistogramDataPoints().add(toHistogramDataPointVO(code, indicators, indicatorEnum2groupEnumMap.get(BizIndicatorEnum.enumOf(code))));
                }
            });
        });

        //折线图
        lineIndicator.forEach((bizTime, indicatorDTOS) -> {
                    List<DeliveryLoadDataPointVO> linePointVOs = indicatorDTOS.stream().filter(indicatorDto -> lineIndicatorEnums.contains(BizIndicatorEnum.enumOf(indicatorDto.getCode())))
                            .map(indicatorDTO -> toLineDataPointVO(indicatorDTO, false))
                            .collect(Collectors.toList());
                    deliveryLoadGraphDataVO.getLineDataPoints().addAll(linePointVOs);
                }
        );

        //同比折线图
        refLineIndicator.forEach((bizTime, indicatorDTOS) -> {
            List<DeliveryLoadDataPointVO> refLinePointVOs = indicatorDTOS.stream().filter(indicatorDto -> lineIndicatorEnums.contains(BizIndicatorEnum.enumOf(indicatorDto.getCode())))
                    .map(indicatorDTO -> toLineDataPointVO(indicatorDTO, true))
                    .collect(Collectors.toList());
            deliveryLoadGraphDataVO.getRefLineDataPoints().addAll(refLinePointVOs);
        });

        //排序
        deliveryLoadGraphDataVO.setHistogramDataPoints(deliveryLoadGraphDataVO.getHistogramDataPoints().stream().sorted(this::compareIndicator).collect(Collectors.toList()));
        deliveryLoadGraphDataVO.setLineDataPoints(deliveryLoadGraphDataVO.getLineDataPoints().stream().sorted(this::compareIndicator).collect(Collectors.toList()));
        deliveryLoadGraphDataVO.setRefLineDataPoints(deliveryLoadGraphDataVO.getRefLineDataPoints().stream().sorted(this::compareIndicator).collect(Collectors.toList()));


        //处理x
        processX(deliveryLoadGraphDataVO);

        return deliveryLoadGraphDataVO;
    }


    /**
     * 先按照时间排 再按照分组排 最后按照组内顺序排 组内同一顺序 按照desc来排序
     */
    private int compareIndicator(DeliveryLoadDataPointVO o1, DeliveryLoadDataPointVO o2) {
        if (Objects.equals(o1.getX(), o2.getX())) {
            if (StringUtils.isNotBlank(o1.getGroupName()) && StringUtils.isNotBlank(o2.getGroupName())) {
                HistogramGroupEnum group1 = HistogramGroupEnum.enumOf(o1.getGroupName());
                HistogramGroupEnum group2 = HistogramGroupEnum.enumOf(o2.getGroupName());

                if (group1 == null || group2 == null) {
                    return 0;
                }

                if (Objects.equals(group1, group2)) {
                    int index1 = bizIndicatorGroupMap.get(group1).indexOf(BizIndicatorEnum.enumOf(o1.getCode()));
                    int index2 = bizIndicatorGroupMap.get(group2).indexOf(BizIndicatorEnum.enumOf(o2.getCode()));

                    if (Objects.equals(index1, index2)) {
                        return o1.getDesc().compareTo(o2.getDesc());
                    }

                    return index1 - index2;
                }
                return group1.getOrder() - group2.getOrder();
            }

            return 0;
        }

        return (int) (Long.parseLong(o1.getX()) - Long.parseLong(o2.getX()));
    }

    public DeliveryLoadGraphFrameVO readerDeliveryLoadGraphFrameVO() {
        return new DeliveryLoadGraphFrameVO((long) (BIZ_INTERVAL * 60 * 1000), null, Collections.emptyList(), null, null);
    }

    private DeliveryLoadDataPointVO toLineDataPointVO(IndicatorDTO indicatorDTO, Boolean isRef) {

        BizIndicatorEnum bizIndicatorEnum = BizIndicatorEnum.enumOf(indicatorDTO.getCode());
        DeliveryLoadDataPointVO dataPointVO = new DeliveryLoadDataPointVO();
        dataPointVO.setX(String.valueOf(indicatorDTO.getBizTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
        dataPointVO.setY(indicatorDTO.getValue());
        dataPointVO.setCode(indicatorDTO.getCode());
        if (isRef) {
            dataPointVO.setDesc("同比-" + bizIndicatorEnum.getDesc());
        } else {
            dataPointVO.setDesc(bizIndicatorEnum.getDesc());
        }

        if (Objects.equals(bizIndicatorEnum.getIndicatorGroupType(), IndicatorGroupType.BY_SHIFT)) {
            dataPointVO.setColor(StringUtils.isNotBlank(indicatorDTO.getProperty()) ? indicatorDTO.getProperty() : DEFAULT_COLOR);
        } else {
            dataPointVO.setColor(getColorConfigMap().getOrDefault(bizIndicatorEnum, DEFAULT_COLOR));
        }


        return dataPointVO;
    }

    private DeliveryLoadDataPointVO toHistogramDataPointVO(String bizIndicatorCode, List<IndicatorDTO> indicatorDTOS, HistogramGroupEnum histogramGroupEnum) {
        if (CollectionUtils.isEmpty(indicatorDTOS)) {
            log.warn(String.format("%s 指标数据为空", bizIndicatorCode));
            return null;
        }
        BizIndicatorEnum bizIndicatorEnum = BizIndicatorEnum.enumOf(bizIndicatorCode);

        DeliveryLoadDataPointVO dataPointVO = new DeliveryLoadDataPointVO();
        dataPointVO.setX(String.valueOf(indicatorDTOS.get(0).getBizTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
        if (Objects.equals(bizIndicatorEnum.getIndicatorGroupType(), IndicatorGroupType.BY_SHIFT)) {
            dataPointVO.setY(indicatorDTOS.stream().map(IndicatorDTO::getValue).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        } else {
            dataPointVO.setY(indicatorDTOS.get(0).getValue());
        }

        dataPointVO.setCode(bizIndicatorCode);
        dataPointVO.setGroupName(histogramGroupEnum.getGroupDesc());
        dataPointVO.setColor(getColorConfigMap().getOrDefault(bizIndicatorEnum, DEFAULT_COLOR));
        dataPointVO.setDesc(bizIndicatorEnum.getDesc());


        return dataPointVO;
    }

    private DeliveryLoadGraphDataVO processX(DeliveryLoadGraphDataVO originalDeliveryLoadGraphDataVO) {
        //如果不是bizInterval的整数倍 X向前偏移到bizInterval的整数倍上
        originalDeliveryLoadGraphDataVO.getHistogramDataPoints().forEach(point -> {
            point.setX(String.valueOf(Long.parseLong(point.getX()) / (BIZ_INTERVAL * 60 * 1000) * (BIZ_INTERVAL * 60 * 1000)));
        });

        originalDeliveryLoadGraphDataVO.getLineDataPoints().forEach(point -> {
            point.setX(String.valueOf(Long.parseLong(point.getX()) / (BIZ_INTERVAL * 60 * 1000) * (BIZ_INTERVAL * 60 * 1000)));
        });

        originalDeliveryLoadGraphDataVO.getRefLineDataPoints().forEach(point -> {
            point.setX(String.valueOf(Long.parseLong(point.getX()) / (BIZ_INTERVAL * 60 * 1000) * (BIZ_INTERVAL * 60 * 1000)));
        });

        return originalDeliveryLoadGraphDataVO;
    }

    private Map<BizIndicatorEnum, String> getColorConfigMap() {

        Map<BizIndicatorEnum, String> bizIndicatorColorMap = new HashMap<>();
        //从Lion读取颜色
        Map<String, String> colorMap = Lion.getConfigRepository().getMap("delivery.load.board.color.config", String.class, Collections.emptyMap());
        colorMap.forEach((indicatorCode, color) -> {
            bizIndicatorColorMap.put(BizIndicatorEnum.enumOf(indicatorCode), color);
        });

        return bizIndicatorColorMap;
    }

    private void alignByFillZero(Map<LocalDateTime, List<IndicatorDTO>> histogramIndicator,
                                 Map<LocalDateTime, List<IndicatorDTO>> lineIndicator,
                                 Map<LocalDateTime, List<IndicatorDTO>> refLineIndicator,
                                 LocalDate paraDay, Integer compareType) {
        if (Objects.isNull(paraDay) || Objects.isNull(compareType)) {
            return;
        }

        //找出当前数据与对比数据之间的天数间隔
        LocalDate refDate = parseCompareDate(paraDay, compareType);
        long durationDays = ChronoUnit.DAYS.between(refDate, paraDay);

        //找到最小的时间点
        Optional<LocalDateTime> minBizTimeOpt = findMinBizTime(histogramIndicator, lineIndicator, refLineIndicator, durationDays);

        if (!minBizTimeOpt.isPresent()) {
            return;
        }

        //补点
        fillPoint(histogramIndicator, minBizTimeOpt.get());
        fillPoint(lineIndicator, minBizTimeOpt.get());
        fillPoint(refLineIndicator, minBizTimeOpt.get().minusDays(durationDays));
    }

    private Optional<LocalDateTime> findMinBizTime(Map<LocalDateTime, List<IndicatorDTO>> histogramIndicator,
                                                   Map<LocalDateTime, List<IndicatorDTO>> lineIndicator,
                                                   Map<LocalDateTime, List<IndicatorDTO>> refLineIndicator,
                                                   long durationDays) {
        HashSet<LocalDateTime> bizTimeSet = new HashSet<>();

        bizTimeSet.addAll(histogramIndicator.keySet());
        bizTimeSet.addAll(lineIndicator.keySet());
        bizTimeSet.addAll(refLineIndicator.keySet().stream().map(refTime -> refTime.plusDays(durationDays)).collect(Collectors.toSet()));


        return bizTimeSet.stream().min(LocalDateTime::compareTo);
    }

    private void fillPoint(Map<LocalDateTime, List<IndicatorDTO>> indicator, LocalDateTime minBizTime) {
        //先把数据分解为指标维度
        Map<String/*bizCode*/, List<IndicatorDTO>> bizIndicatorMap = indicator.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(IndicatorDTO::getCode));

        //按照指标来填充
        bizIndicatorMap.forEach((bizCode, dtos) -> {
            Optional<IndicatorDTO> minDtoOpt = dtos.stream().min(Comparator.comparing(IndicatorDTO::getBizTime));
            if (!minDtoOpt.isPresent()) {
                return;
            }

            IndicatorDTO minDto = minDtoOpt.get();
            LocalDateTime curTime = LocalDateTime.of(minBizTime.toLocalDate(), minBizTime.toLocalTime());
            while (curTime.isBefore(minDto.getBizTime())) {
                IndicatorDTO filledIndicatorPoint = IndicatorDTO.builder().fragmentName(minDto.getFragmentName())
                        .bizTime(curTime)
                        .value(BigDecimal.ZERO)
                        .logisticsUnitId(minDto.getLogisticsUnitId())
                        .calculateTime(LocalDateTime.now())
                        .property(minDto.getProperty())
                        .code(minDto.getCode())
                        .build();
                indicator.putIfAbsent(curTime, new ArrayList<>());
                indicator.get(curTime).add(filledIndicatorPoint);
                curTime = curTime.plusMinutes(BIZ_INTERVAL);
            }
        });
    }
}
