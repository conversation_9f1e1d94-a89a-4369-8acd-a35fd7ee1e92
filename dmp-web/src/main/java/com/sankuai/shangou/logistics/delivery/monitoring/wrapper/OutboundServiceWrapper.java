package com.sankuai.shangou.logistics.delivery.monitoring.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.warehouse.PromotionMaterialOutboundService;
import com.sankuai.shangou.logistics.warehouse.dto.MaterialTransInfoDto;
import com.sankuai.shangou.logistics.warehouse.dto.TradeOrderKey;
import com.sankuai.shangou.logistics.warehouse.dto.request.QueryTransMaterialInfoRequest;
import com.sankuai.shangou.logistics.warehouse.dto.request.RiderChangeTransMaterialRequest;
import com.sankuai.shangou.logistics.warehouse.dto.request.RiderTakeAwayTransMaterialRequest;
import com.sankuai.shangou.logistics.warehouse.enums.PromotionMaterialProcessCodeEnum;
import com.sankuai.shangou.logistics.warehouse.enums.PromotionMaterialType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-02-10
 * @email <EMAIL>
 */
@Slf4j
@Rhino
public class OutboundServiceWrapper {

    @Resource
    private PromotionMaterialOutboundService promotionMaterialOutboundService;

    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "OutboundServiceWrapper.transMaterialStockWhenRiderChange", fallBackMethod = "transMaterialStockWhenRiderChangeFallback", timeoutInMilliseconds = 2000)
    public Optional<MaterialTransInfoDto> transMaterialStockWhenRiderChange(RiderChangeTransMaterialRequest request) {
        TResult<List<MaterialTransInfoDto>> result = promotionMaterialOutboundService.transMaterialStockWhenRiderChange(request);
        if (result == null || !result.isSuccess()) {
            throw new ThirdPartyException("调用物流服务异常");
        }
        return CollectionUtils.isEmpty(result.getData()) ? Optional.empty() : Optional.of(result.getData().get(0));
    }

    @Deprecated
    public Optional<MaterialTransInfoDto> transMaterialStockWhenRiderChangeFallback(RiderChangeTransMaterialRequest request) {
        log.warn("调用物流服务transMaterialStockWhenRiderChange异常降级");
        return Optional.empty();
    }

}
