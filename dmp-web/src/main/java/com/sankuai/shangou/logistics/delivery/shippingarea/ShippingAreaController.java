package com.sankuai.shangou.logistics.delivery.shippingarea;

import com.dianping.cat.Cat;
import com.meituan.linz.boot.util.Assert;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.enums.HeatmapTypeEnum;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.request.QueryCityHeatmapRequest;
import com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.shippingarea.enums.CatRegionSelectEventEnum;
import com.sankuai.shangou.logistics.delivery.shippingarea.service.ShippingAccountAuthService;
import com.sankuai.shangou.logistics.delivery.shippingarea.service.MapIndicatorService;
import com.sankuai.shangou.logistics.delivery.shippingarea.service.ShippingAreaService;
import com.sankuai.shangou.logistics.delivery.shippingarea.vo.request.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/8/14 19:28
 **/

//TODO linxiaorui 记得加鉴权！！！！
@InterfaceDoc(
        displayName = "配送范围管理页面",
        type = "restful",
        scenarios = "包含配送范围查询、新增、修改等接口",
        description = "包含配送范围查询、新增、修改等接口",
        host = "https://qingting.waimai.meituan.com/"
)
@Slf4j
@RestController
@RequestMapping("dmp/api/shipping-area")
public class ShippingAreaController {
    @Resource
    private ShippingAreaService shippingAreaService;

    @Resource
    private MapIndicatorService mapIndicatorService;


    @Resource
    private ShippingAccountAuthService accountService;

    @MethodDoc(
            description = "获取国内有门店权限的城市列表",
            displayName = "获取国内有门店权限的城市列表",
            restExampleResponseData = "",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/administrative-tree/get"
    )
    @ApiOperation(value = "获取国内有门店权限的城市列表")
    @MethodLog(logRequest = true, logResponse = true)
    @RequestMapping(value = "/administrative-tree/get", method = RequestMethod.GET)
    public Object getAdministrativeTree() {
        return shippingAreaService.getAdministrativeTree(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountId());
    }

    @MethodDoc(
            description = "查询城市配送信息",
            displayName = "查询城市配送信息",
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/city/get"
    )
    @ApiOperation(value = "查询城市配送信息")
    @MethodLog(logRequest = true, logResponse = true)
    @RequestMapping(value = "/city/get", method = RequestMethod.GET)
    public Object getCityDeliveryInfo(@RequestParam("cityId") int cityId) throws TException {
        Assert.throwIfTrue(cityId <= 0, "错误的城市id");
        return shippingAreaService.getCityDeliveryInfo(cityId,
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountId());
    }

    @MethodDoc(
            description = "查询门店多渠道配送信息",
            displayName = "查询门店多渠道配送信息",
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/store/get"
    )
    @ApiOperation(value = "查询门店多渠道配送信息")
    @MethodLog(logRequest = true, logResponse = true)
    @RequestMapping(value = "/store/get", method = RequestMethod.GET)
    public Object getStoreChannelDeliveryInfo(@RequestParam("storeId") Long storeId) throws TException {
        return shippingAreaService.getPoiChannelDeliveryInfo(storeId,
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
    }

    @MethodDoc(
            description = "配送范围删除",
            displayName = "配送范围删除",
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/store/delete"
    )
    @ApiOperation(value = "配送范围删除")
    @MethodLog(logRequest = true, logResponse = true)
    @RequestMapping(value = "/store/delete", method = RequestMethod.POST)
    public void deleteDeliveryRegion(@RequestBody DeleteChannelPoiShippingParam request) {
        Cat.logMetricForCount(CatRegionSelectEventEnum.REGION_SELECT_DELETE_SHIPPING.getType(),
                1, CatRegionSelectEventEnum.formatShippingEventTags(request.getStoreId(),
                        WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName()));

        request.validate();
        shippingAreaService.deleteChannelPoiShipping(request,
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountName(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName());
    }


    @MethodDoc(
            description = "更新正常时段配送范围",
            displayName = "更新正常时段配送范围",
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/store/regular-period/update"
    )
    @ApiOperation(value = "更新正常时段配送范围")
    @MethodLog(logRequest = true, logResponse = true)
    @RequestMapping(value = "/store/regular-period/update", method = RequestMethod.POST)
    public void updateRegularPeriodDeliveryRegion(@RequestBody BatchUpsertChannelPoiShippingParam request) throws TException {
        request.validate();
        Cat.logMetricForCount(CatRegionSelectEventEnum.REGION_SELECT_UPSERT_SHIPPING.getType(),
                1, CatRegionSelectEventEnum.formatShippingEventTags(request.getStoreId(),
                        WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName()));

        shippingAreaService.batchUpdateChannelPoiRegularPeriodShipping(request,
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountName(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName());
    }

    @MethodDoc(
            description = "更新特殊时段配送范围",
            displayName = "更新特殊时段配送范围",
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/store/special-period/update"
    )
    @ApiOperation(value = "更新特殊时段配送范围")
    @MethodLog(logRequest = true, logResponse = true)
    @RequestMapping(value = "/store/special-period/update", method = RequestMethod.POST)
    public void updateSpecialPeriodDeliveryRegion(@RequestBody BatchUpsertChannelPoiShippingParam request) throws TException {
        request.validate();
        Cat.logMetricForCount(CatRegionSelectEventEnum.REGION_SELECT_UPSERT_SHIPPING.getType(),
                1, CatRegionSelectEventEnum.formatShippingEventTags(request.getStoreId(),
                        WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName()));

        shippingAreaService.batchUpdateChannelPoiSpecialPeriodShipping(request,
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountName(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName());
    }

    @MethodDoc(
            description = "新增正常时段配送范围",
            displayName = "新增正常时段配送范围",
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/store/regular-period/add"
    )
    @ApiOperation(value = "新增正常时段配送范围")
    @MethodLog(logRequest = true, logResponse = true)
    @RequestMapping(value = "/store/regular-period/add", method = RequestMethod.POST)
    public void addRegularPeriodDeliveryRegion(@RequestBody BatchUpsertChannelPoiShippingParam request) throws TException {
        request.validate();
        Cat.logMetricForCount(CatRegionSelectEventEnum.REGION_SELECT_UPSERT_SHIPPING.getType(),
                1, CatRegionSelectEventEnum.formatShippingEventTags(request.getStoreId(),
                        WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName()));

        shippingAreaService.batchUpdateChannelPoiRegularPeriodShipping(request,
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountName(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName());
    }


    @MethodDoc(
            description = "新增特殊时段配送范围",
            displayName = "新增特殊时段配送范围",
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/store/special-period/add"
    )
    @ApiOperation(value = "新增特殊时段配送范围")
    @MethodLog(logRequest = true, logResponse = true)
    @RequestMapping(value = "/store/special-period/add", method = RequestMethod.POST)
    public void addSpecialPeriodDeliveryRegion(@RequestBody BatchUpsertChannelPoiShippingParam request) throws TException {
        request.validate();
        Cat.logMetricForCount(CatRegionSelectEventEnum.REGION_SELECT_UPSERT_SHIPPING.getType(),
                1, CatRegionSelectEventEnum.formatShippingEventTags(request.getStoreId(),
                        WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName()));

        shippingAreaService.batchUpdateChannelPoiSpecialPeriodShipping(request,
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountName(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName());
    }


    @MethodDoc(
            description = "查询城市超配信息",
            displayName = "查询城市超配信息",
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            paramType = ParamType.REQUEST_PARAM,
                            description = ""
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/city-outscope-points/get"
    )
    @ApiOperation(value = "查询城市超配信息")
    @MethodLog(logRequest = true)
    @RequestMapping(value = "/city-outscope-points/get", method = RequestMethod.POST)
    public Object queryCityOutOfScopePoints(@RequestBody CityOutOfScopePointsQueryParam req) {
        req.validate();
        Cat.logMetricForCount(CatRegionSelectEventEnum.REGION_SELECT_INDICATOR_ACCESS.getType(),
                1, CatRegionSelectEventEnum.formatIndicatorAccessEventTags("CityOutScopePoints",
                        WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName()));

        return mapIndicatorService.queryOutOfScopePoints(req);
    }


    @MethodDoc(
            description = "歪马订单量热力图",
            displayName = "",
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            paramType = ParamType.REQUEST_BODY,
                            description = "歪马订单量热力图"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/dh-order-heatmap/get"
    )
    @ApiOperation(value = "歪马订单量热力图")
    @MethodLog(logRequest = true)
    @RequestMapping(value = "/dh-order-heatmap/get", method = RequestMethod.POST)
    public Object queryDhOrderHeatmap(@RequestBody DhOrderHeatMapQueryParam queryParam) throws TException {
        queryParam.validate();
        Cat.logMetricForCount(CatRegionSelectEventEnum.REGION_SELECT_INDICATOR_ACCESS.getType(),
                1, CatRegionSelectEventEnum.formatIndicatorAccessEventTags("cityDhOrderHeatmap",
                        WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName()));
        QueryCityHeatmapRequest request = new QueryCityHeatmapRequest();
        request.setHeatmapType(HeatmapTypeEnum.DRUNK_HORSE_ORDER.name());
        request.setDateType(queryParam.getDateType());
        request.setDt(queryParam.getDt());
        request.setCityId(queryParam.getCityId());
        return mapIndicatorService.queryCityHeatmap(request);
    }


    @MethodDoc(
            description = "查询图层对象指标",
            displayName = "查询图层对象指标",
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            paramType = ParamType.REQUEST_BODY,
                            description = ""
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/user-indicator/get"
    )
    @ApiOperation(value = "查询图层对象指标")
    @MethodLog(logRequest = true, logResponse = true)
    @RequestMapping(value = "/user-indicator/get", method = RequestMethod.POST)
    public Object queryLoiOrderUserIndicator(@RequestBody LoiOrderUserIndicatorQueryParam req) {
        Cat.logMetricForCount(CatRegionSelectEventEnum.REGION_SELECT_INDICATOR_ACCESS.getType(),
                1, CatRegionSelectEventEnum.formatIndicatorAccessEventTags("LoiOrderUserIndicator",
                        WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName()));

        req.validate();
        return mapIndicatorService.queryLoiOrderUserIndicator(req, WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
    }

    @MethodDoc(
            description = "查询人口画像",
            displayName = "查询人口画像",
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            paramType = ParamType.REQUEST_BODY,
                            description = ""
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/shipping-area/people-portrait/get"
    )
    @ApiOperation(value = "查询人口画像")
    @MethodLog(logRequest = true)
    @RequestMapping(value = "/people-portrait/get", method = RequestMethod.POST)
    public Object queryPeoplePortrait(@RequestBody PeoplePortraitQueryParam req) {
        Cat.logMetricForCount(CatRegionSelectEventEnum.REGION_SELECT_INDICATOR_ACCESS.getType(),
                1, CatRegionSelectEventEnum.formatIndicatorAccessEventTags("PeoplePortrait",
                        WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName()));

        req.validate();
        return mapIndicatorService.queryPeoplePortrait(req, WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
    }


    @MethodDoc(
            description = "查询aoi分类指标",
            displayName = "查询aoi分类指标",
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            paramType = ParamType.REQUEST_PARAM,
                            description = ""
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/map-aoi-classify/get"
    )
    @ApiOperation(value = "查询aoi分类指标")
    @MethodLog(logRequest = true)
    @RequestMapping(value = "/map-aoi-classify/get", method = RequestMethod.POST)
    public Object queryMapAoiClassify(@RequestBody LoiMapAoiClassifyQueryParam req) {
        Cat.logMetricForCount(CatRegionSelectEventEnum.REGION_SELECT_INDICATOR_ACCESS.getType(),
                1, CatRegionSelectEventEnum.formatIndicatorAccessEventTags("MapAoiClassify",
                        WebLoginContextHolder.getWebLoginContext().getLoginUser().getEmployeeName()));

        req.validate();
        return mapIndicatorService.queryLoiMapAoiClassify(req, WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
    }

    @MethodDoc(
            description = "查询电动车骑行导航信息",
            displayName = "查询电动车骑行导航信息",
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/routeplan/electricbike"
    )
    @ApiOperation(value = "查询电动车骑行导航信息")
    @MethodLog(logRequest = true, logResponse = true)
    @RequestMapping(value = "/routeplan/electricbike", method = RequestMethod.GET)
    public Object getElectricBikeRoutePlan(ElectricBikeRouteParam request) {
        request.validate();
        return shippingAreaService.getElectricBikeRoutePlan(request, null);
    }


    @MethodDoc(
            description = "查询访问账号数据权限",
            displayName = "查询访问账号数据权限",
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "dmp/api/shipping-area/permission/get"
    )
    @ApiOperation(value = "查询访问账号数据权限")
    @MethodLog(logRequest = true, logResponse = true)
    @RequestMapping(value = "/permission/get", method = RequestMethod.GET)
    public Object getPermission() {
        return accountService.getDataPermission(WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountName());
    }
     
}