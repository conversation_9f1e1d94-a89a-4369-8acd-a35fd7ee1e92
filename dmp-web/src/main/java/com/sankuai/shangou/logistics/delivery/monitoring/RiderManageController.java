package com.sankuai.shangou.logistics.delivery.monitoring;

import com.dianping.cat.Cat;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.shangou.commons.auth.login.context.LoginUser;
import com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.delivery.monitoring.request.QueryShopDeliveryStatusRequest;
import com.sankuai.shangou.logistics.delivery.monitoring.request.QueryShopRiderAccountListRequest;
import com.sankuai.shangou.logistics.delivery.monitoring.request.RiderChangeRequest;
import com.sankuai.shangou.logistics.delivery.monitoring.vo.QueryShopDeliveryMonitoringResponse;
import com.sankuai.shangou.logistics.delivery.monitoring.vo.QueryShopRiderAccountListResponse;
import com.sankuai.shangou.logistics.delivery.monitoring.wrapper.RiderManageClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 自营骑手管理控制器.
 *
 * <AUTHOR>
 * @since 2021/12/1 15:18
 */
@InterfaceDoc(
        displayName = "自营骑手 WEB 端配送管理相关接口",
        type = "restful",
        scenarios = "包含查看骑手整体状况等接口",
        description = "包含查看骑手整体状况等接口"
)
@Slf4j
@Api(value = "自营骑手 WEB 端配送管理相关接口")
@RestController
@RequestMapping("dmp/api/web/delivery/manage")
public class RiderManageController {

    @Resource
    private RiderManageClient riderManageClient;


    @MethodDoc(
            displayName = "监控门店运单相关信息（全部进行中运单的状态，门店所有骑手的配送情况）",
            description = "监控门店运单相关信息（全部进行中运单的状态，门店所有骑手的配送情况）",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "监控门店运单相关信息的请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/api/v1/rider/manage/deliverymonitoring",
            restExamplePostData = "",
            restExampleResponseData = ""
    )
    @DataSecurity({
            @SecurityParam(value = "shopId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "监控门店运单相关信息")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/deliverymonitoring", method = RequestMethod.POST)
    public QueryShopDeliveryMonitoringResponse shopDeliveryMonitoring(@RequestBody QueryShopDeliveryStatusRequest req) {
        Optional<String> checkErrorMsg = req.validate();
        if (checkErrorMsg.isPresent()) {
            throw new IllegalArgumentException(checkErrorMsg.get());
        }

        QueryShopDeliveryMonitoringResponse monitoringInfo = riderManageClient.shopDeliveryMonitor(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                req.getShopId(), WebLoginContextHolder.getWebLoginContext().getAppId(), req.getFilterThirdDelivery());
        return monitoringInfo;

    }

    @MethodDoc(
            displayName = "改派骑手",
            description = "改派骑手",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "改派骑手的请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "dmp/api/web/delivery/manage/riderChange",
            restExamplePostData = "",
            restExampleResponseData = ""
    )
    @DataSecurity({
            @SecurityParam(value = "shopId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "改派骑手")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/riderChange", method = RequestMethod.POST)
    public void riderChange(@RequestBody RiderChangeRequest request) {
        Optional<String> errMsg = request.validate();
        if (errMsg.isPresent()) {
            throw new IllegalArgumentException(errMsg.get());
        }

        riderManageClient.riderChange(request, WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                WebLoginContextHolder.getWebLoginContext().getLoginUser().getAccountId());
        Cat.logEvent("DELIVERY_MONITOR", "RIDER_CHANGE");
    }


    @MethodDoc(
            displayName = "查询门店下骑手名单（具有门店权限的店员/店长/兼职骑手）",
            description = "查询门店下骑手名单（具有门店权限的店员/店长/兼职骑手）",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询门店下骑手名单的请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "dmp/api/web/delivery/manage/shopRiderAccountList",
            restExamplePostData = "",
            restExampleResponseData = ""
    )
    @DataSecurity({
            @SecurityParam(value = "shopId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "查询门店下骑手名单（具有门店权限的店员/店长/兼职骑手）")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/shopRiderAccountList", method = RequestMethod.POST)
    public QueryShopRiderAccountListResponse shopRiderAccountList(@RequestBody QueryShopRiderAccountListRequest request) {
        Optional<String> errMsg = request.validate();
        if (errMsg.isPresent()) {
            throw new IllegalArgumentException(errMsg.get());
        }

        LoginUser currLoginUser = WebLoginContextHolder.getWebLoginContext().getLoginUser();
        QueryShopRiderAccountListResponse accountList = riderManageClient.shopRiderAccountList(currLoginUser.getTenantId(),
                request.getShopId(),  WebLoginContextHolder.getWebLoginContext().getAppId());


        boolean isGrayStore = GrayConfigUtils.judgeIsGrayStore(currLoginUser.getTenantId(), request.getShopId(), GrayKeyEnum.LIMIT_ACCEPT_ORDER.getGrayKey());
        if (isGrayStore) {
            // 处理骑手限制接单详情
            riderManageClient.appendLimitInfoResultNew(currLoginUser.getTenantId(), accountList, request.getShopId());
        } else {
            // 处理骑手是否培训通过
            riderManageClient.appendTrainingResult(currLoginUser.getTenantId(), request.getShopId(), accountList);

        }

        return accountList;
    }


}
