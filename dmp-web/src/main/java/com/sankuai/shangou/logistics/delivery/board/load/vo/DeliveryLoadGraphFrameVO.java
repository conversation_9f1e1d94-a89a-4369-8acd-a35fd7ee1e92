package com.sankuai.shangou.logistics.delivery.board.load.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/6/27 10:35
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryLoadGraphFrameVO  {
    /** 刷新间隔时间，单位：毫秒 */
    private Long freshInterval;
    /** X坐标轴最大刻度 */
    private String xMaxSize;

    /** X坐标轴刻度名称，长度与xMaxSize保持一致 */
    private List<String> xScaleNames;

    /** 左Y坐标轴单位刻度值 */
    private BigDecimal lyScaleValue;

    /** 右Y坐标轴单位刻度值 */
    private BigDecimal ryScaleValue;
}

