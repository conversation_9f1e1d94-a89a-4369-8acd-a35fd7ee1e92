package com.sankuai.shangou.logistics.delivery.board.load.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/7/4 16:47
 **/
public enum CompareTypeEnum {
    DAILY_COMPARE(1, "日环比"),
    WEEKLY_COMPARE(2, "周同比"),
    MONTHLY_COMPARE(3, "月同比");

    private Integer code;

    private String desc;

    CompareTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CompareTypeEnum enumOf(Integer compareType) {
        for (CompareTypeEnum value : values()) {
            if (Objects.equals(value.getCode(), compareType)) {
                return value;
            }
        }

        return null;
    }
}
