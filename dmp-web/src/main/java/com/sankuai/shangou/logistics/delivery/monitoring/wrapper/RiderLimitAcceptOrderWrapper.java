package com.sankuai.shangou.logistics.delivery.monitoring.wrapper;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.sdms.sdk.limit.LimitAcceptOrderThriftService;
import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/11/1 14:10
 */
@Rhino
@Slf4j
public class RiderLimitAcceptOrderWrapper {
    @Resource
    private LimitAcceptOrderThriftService limitAcceptOrderThriftService;
    /**
     * 批量查询骑手接单限制信息
     * @param tenantId 租户id
     * @param riderAccountIdList 骑手id列表
     * @return 骑手接单限制信息
     */
    @Degrade(rhinoKey = "RiderLimitAcceptOrderWrapper.batchQueryRiderLimitAcceptOrderInfo", fallBackMethod = "batchQueryRiderLimitAcceptOrderInfoFallback", timeoutInMilliseconds = 5000)
    @MethodLog(logRequest = true, logResponse = true)
    public Map<Long, List<LimitItemDTO>> batchQueryRiderLimitAcceptOrderInfo(Long tenantId, List<Long> riderAccountIdList, Long storeId) {
        if (Objects.isNull(tenantId) || CollectionUtils.isEmpty(riderAccountIdList)) {
            throw new IllegalArgumentException("批量查询骑手接单限制信息失败, 参数不合法:租户id或骑手ID列表为空");
        }

        log.info("start query LimitAcceptOrderThriftService#queryLimitItemListByAccountIdList  tenantId: {}, accountIds: {}, storeId:{}", tenantId, riderAccountIdList, storeId);
        TResult<Map<Long, List<LimitItemDTO>>> tResponse = limitAcceptOrderThriftService.queryLimitItemListByAccountIdListNew(tenantId, riderAccountIdList, storeId);
        log.info("end   query LimitAcceptOrderThriftService#queryLimitItemListByAccountIdList  response: {}", tResponse);

        if (tResponse == null || !tResponse.isSuccess()){
            throw new BizException("批量查询骑手接单限制信息失败");
        }
        return Optional.ofNullable(tResponse.getData()).orElse(Maps.newHashMap());
    }

    public Map<Long, List<LimitItemDTO>> batchQueryRiderLimitAcceptOrderInfoFallback(Long tenantId, List<Long> riderAccountIdList, Long storeId) {
        log.error("批量查询骑手接单限制信息降级");
        return Maps.newHashMap();
    }


    /**
     * 查询单个骑手接单限制信息
     * @param tenantId 租户id
     * @param riderAccountId 骑手id
     * @return 骑手接单限制信息
     */
    @Degrade(rhinoKey = "RiderLimitAcceptOrderWrapper.querySingleRiderLimitAcceptOrderInfo", fallBackMethod = "querySingleRiderLimitAcceptOrderInfoFallback", timeoutInMilliseconds = 5000)
    @MethodLog(logRequest = true, logResponse = true)
    public List<LimitItemDTO> querySingleRiderLimitAcceptOrderInfo(Long tenantId, Long riderAccountId, Long storeId) {
        if (Objects.isNull(tenantId) || Objects.isNull(riderAccountId)) {
            throw new IllegalArgumentException("租户ID参数或骑手ID信息为空");
        }

        log.info("start query LimitAcceptOrderThriftService#queryLimitItemListByAccountId  tenantId: {}, accountId: {} ,storeId :{}", tenantId, riderAccountId, storeId);
        TResult<List<LimitItemDTO>> tResponse = limitAcceptOrderThriftService.queryLimitItemListByAccountIdNew(tenantId, riderAccountId, storeId);
        log.info("end   query LimitAcceptOrderThriftService#queryLimitItemListByAccountId  response: {}", tResponse);

        if (tResponse == null || !tResponse.isSuccess() ) {
            throw new BizException("单查询骑手接单限制信息失败");
        }
        return Optional.ofNullable(tResponse.getData()).orElse(Lists.newArrayList());
    }

    public List<LimitItemDTO> querySingleRiderLimitAcceptOrderInfoFallback(Long tenantId, Long riderAccountId, Long storeId) {
        log.error("查询单个骑手接单限制信息降级");
        return Lists.newArrayList();
    }

}
