package com.sankuai.shangou.logistics.delivery.monitoring.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/11/1 17:22
 */
@TypeDoc(
        description = "骑手限制接单原因 VO"
)
@ApiModel("骑手限制接单原因 VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LimitItemVO {

    @FieldDoc(
            description = "限制原因", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "限制原因", required = true)
    private String reason;



    public static LimitItemVO convertToLimitItemVO(LimitItemDTO dto) {
        return new LimitItemVO(dto.getReason());
    }
}
