package com.sankuai.shangou.logistics.delivery.monitoring.enums;

/**
 * 骑手状态展示枚举.
 *
 * <AUTHOR>
 * @since 2021/12/5 18:04
 */
public enum RiderShowStatusEnum {

    IN_BUSY(0, "履约中"),
    IN_IDLE(1, "空闲中"),
    THIRD_PARTY(2, "三方骑手")
    ;

    private final int code;
    private final String desc;


    RiderShowStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
