package com.sankuai.shangou.logistics.delivery.sealcontainer.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/6/27 17:30
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SealContainerOpLogVO {
    private Long warehouseId;

    private String warehouseName;

    private String sealContainerCode;

    private String tradeOrderNo;

    private Integer channelId;

    private Integer status;

    private String stockOutOperatorName;

    private Long stockOutTime;

    private String riderName;

    private Long riderStartDeliveryTime;

    private String giveBackOperatorName;

    private Long giveBackTime;

    private Integer giveBackType;
}
