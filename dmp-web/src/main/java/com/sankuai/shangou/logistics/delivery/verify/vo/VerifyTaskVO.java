package com.sankuai.shangou.logistics.delivery.verify.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/22 17:32
 **/
@Data
public class VerifyTaskVO {
    private String taskId;

    private List<Integer> collectContentList;

    private Integer collectMode;

    private Integer status;

    private Long pushTime;

    private Long completeTime;

    private Long expireTime;

    private String riderAccountName;

    private String riderName;

    private String storeName;

    private String channelOrderId;

    private String callbackCode;

    private String callbackMsg;

    private Boolean faceIdentifyResult;

    private Boolean helmetIdentifyResult;

    private Boolean dressingIdentifyResult;

    private String requestCode;

    private PunishInfoVO faceIdentifyPunishInfo;

    private PunishInfoVO helmetIdentifyPunishInfo;

    private PunishInfoVO dressingIdentifyPunishInfo;

    private PunishInfoVO taskExpirePunishInfo;

    private String uuid;

    private Boolean imageIsExpire;

}
