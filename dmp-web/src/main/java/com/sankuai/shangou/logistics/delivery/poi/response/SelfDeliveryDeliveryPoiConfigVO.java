package com.sankuai.shangou.logistics.delivery.poi.response;

import com.google.common.collect.Lists;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-09-01
 * @email <EMAIL>
 */
@Data
public class SelfDeliveryDeliveryPoiConfigVO {

    private Long id;

    private Long tenantId;

    private Long poiId;

    private String poiName;

    private List<String> supportThirdParty;

    private String defaultDeliveryMethod;

    private Integer enableTurnDelivery;

    private Integer enablePickDeliverySplit;

    private String lastOperatorName;

    private Long lastOperateTime;


    public static SelfDeliveryDeliveryPoiConfigVO fromPO(String poiName, SelfDeliveryPoiConfigPO po) {
        SelfDeliveryDeliveryPoiConfigVO vo = new SelfDeliveryDeliveryPoiConfigVO();
        vo.setId(po.getId());
        vo.setTenantId(po.getTenantId());
        vo.setPoiId(po.getPoiId());
        vo.setPoiName(poiName);
        vo.setSupportThirdParty(Lists.newArrayList("青云配送"));
        vo.setDefaultDeliveryMethod("自配送");
        vo.setEnableTurnDelivery(po.getEnableTurnDelivery());
        vo.setEnablePickDeliverySplit(po.getEnablePickDeliverySplit());
        vo.setLastOperatorName(po.getLastOperatorName());
        vo.setLastOperateTime(TimeUtils.toMilliSeconds(po.getLastOperateTime()));
        return vo;
    }

}
