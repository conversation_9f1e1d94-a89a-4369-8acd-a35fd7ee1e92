package com.sankuai.shangou.logistics.delivery.config.bean;

import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.meituan.shangou.saas.tenant.thrift.PoiApprovalManageThriftService;
import com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService;
import com.sankuai.map.open.platform.api.MapOpenApiService;
import com.sankuai.meituan.gis.remote.service.thrift.TAdminDivisionService;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurityPrepareFilter;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPoiShippingThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.RiderOperateThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.LoiIndicatorThriftService;
import com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.MccUtils;
import com.sankuai.shangou.logistics.hu.api.service.SealContainerLogService;
import com.sankuai.shangou.logistics.sdms.sdk.verify.VerifyTaskThriftService;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import com.sankuai.xm.pubapi.thrift.PusherInfo;
import com.sankuai.xm.udb.common.UdbServiceI;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/10/6 19:25
 **/
@Configuration
public class WebExternalServiceBeanConfig {


    @Bean("channelPoiShippingThriftService")
    public ChannelPoiShippingThriftService.Iface channelPoiShippingThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.ocmschannel");
        proxy.setServiceInterface(Class.forName("com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPoiShippingThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(2000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (ChannelPoiShippingThriftService.Iface) proxy.getObject();
    }

    @Bean("udbService")
    public UdbServiceI.Iface udbServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.xm.udb");
        proxy.setServiceInterface(Class.forName("com.sankuai.xm.udb.common.UdbServiceI"));
        proxy.setNettyIO(true);
        proxy.setTimeout(2000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (UdbServiceI.Iface) proxy.getObject();
    }


    @Bean("tAdminDivisionService")
    public TAdminDivisionService.Iface tAdminDivisionServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.cos.mtgis");
        proxy.setServiceInterface(Class.forName("com.sankuai.meituan.gis.remote.service.thrift.TAdminDivisionService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(2000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (TAdminDivisionService.Iface) proxy.getObject();
    }

    @Bean("poiShippingAreaPusher")
    public PusherInfo regionSelectShippingPusher() throws KmsResultNullException {
        PusherInfo pusherInfo = new PusherInfo();
        pusherInfo.setAppkey(MccUtils.getShippingAdjustPusherKey());    // 申请公众号时分配的appkey，对应公众号平台管理页面的：Key
        pusherInfo.setToken(Kms.getByName("com.sankuai.shangou.logistics.dmp",
                "poi.shipping.pusher.token")); // 申请公众号时分配的appSecret，对应公众号平台管理页面的：Token
        pusherInfo.setFromUid(MccUtils.getShippingAdjustPusherId());    // 公众号ID，pubId，对应公众号平台管理页面的：PubID
        pusherInfo.setToAppid((short) 1);  // 大象APP_ID=1，若不配置该成员，则自动配置公众所属的APP_ID
        pusherInfo.setChannelId((short) 0);    // IM通信组分配的业务线标识，大象APP的channelId=0
        return pusherInfo;
    }


    @Bean("poiApprovalManageThriftService")
    public PoiApprovalManageThriftService poiApprovalManageThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.tenant");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.saas.tenant.thrift.PoiApprovalManageThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(2000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (PoiApprovalManageThriftService) proxy.getObject();
    }

    @Bean("shippingPushMessageService")
    public PushMessageServiceI.Iface shippingPushMessageServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.xm.pubapi");
        proxy.setServiceInterface(Class.forName("com.sankuai.xm.pubapi.thrift.PushMessageServiceI"));
        proxy.setNettyIO(true);
        proxy.setTimeout(2000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (PushMessageServiceI.Iface) proxy.getObject();
    }

    @Bean("sacAccountSearchThriftService")
    public SacAccountSearchThriftService sacAccountSearchThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.saasauth");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(2000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (SacAccountSearchThriftService) proxy.getObject();
    }


    @Bean("loiIndicatorThriftService")
    public LoiIndicatorThriftService loiIndicatorThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.sgshopmgmt.regionselection");
        proxy.setServiceInterface(Class.forName("com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.LoiIndicatorThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (LoiIndicatorThriftService) proxy.getObject();
    }

    @Bean("mapOpenApiService")
    public MapOpenApiService.Iface mapOpenApiServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.apigw.map.facadecenter");
        proxy.setServiceInterface(Class.forName("com.sankuai.map.open.platform.api.MapOpenApiService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (MapOpenApiService.Iface) proxy.getObject();
    }

    @Bean("fulfillmentOrderSearchThriftService")
    public FulfillmentOrderSearchThriftService fulfillmentOrderSearchThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.qnh.ofc.ofw");
        proxy.setServiceInterface(Class.forName("com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (FulfillmentOrderSearchThriftService) proxy.getObject();
    }

    @Bean("sealContainerLogService")
    public SealContainerLogService sealContainerLogService() throws Exception {
        RichTypeThriftClientProxy proxy = new RichTypeThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.logistics.hucenter");
        proxy.setServiceInterface(Class.forName("com.sankuai.shangou.logistics.hu.api.service.SealContainerLogService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (SealContainerLogService) proxy.getObject();
    }

    @Bean("verifyTaskThriftService")
    public VerifyTaskThriftService verifyTaskThriftService() throws Exception {
        RichTypeThriftClientProxy proxy = new RichTypeThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.logistics.sdms");
        proxy.setServiceInterface(Class.forName("com.sankuai.shangou.logistics.sdms.sdk.verify.VerifyTaskThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (VerifyTaskThriftService) proxy.getObject();
    }


    @Bean("tradeShippingOrderService")
    public TradeShippingOrderService tradeShippingOrderService() throws Exception {
        RichTypeThriftClientProxy proxy = new RichTypeThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.logistics.oio");
        proxy.setServiceInterface(Class.forName("com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (TradeShippingOrderService) proxy.getObject();
    }

    @Bean("riderOperateThriftService")
    public RiderOperateThriftService riderOperateThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.sgfulfillment.tms");
        proxy.setServiceInterface(Class.forName("com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.RiderOperateThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(3000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (RiderOperateThriftService) proxy.getObject();
    }


}
