package com.sankuai.shangou.logistics.delivery.monitoring.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 查询门店经营范围的返回体.
 *
 * <AUTHOR>
 * @since 2021/12/3 18:42
 */
@TypeDoc(
        description = "查询门店经营范围的返回体"
)
@ApiModel("查询门店经营范围的返回体")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryShopSpAreaResponse {

    @FieldDoc(
            description = "租户 ID"
    )
    @ApiModelProperty(value = "租户 ID")
    private Long tenantId;

    @FieldDoc(
            description = "门店 ID"
    )
    @ApiModelProperty(value = "门店 ID")
    private Long shopId;

    @FieldDoc(
            description = "门店名称"
    )
    @ApiModelProperty(value = "门店名称")
    private String shopName;

    @FieldDoc(
            description = "门店的经纬度坐标"
    )
    @ApiModelProperty(value = "门店的经纬度坐标")
    private DeliveryCoordinateVO shopCoordinate;

    @FieldDoc(
            description = "不同渠道的门店经营范围"
    )
    @ApiModelProperty(value = "不同渠道的门店经营范围")
    @Deprecated
    private Map<Integer, List<DeliveryCoordinateVO>> channelShopSpAreaMap;


    @FieldDoc(
            description = "不同渠道的门店经营范围列表"
    )
    @ApiModelProperty(value = "不同渠道的门店经营范围列表")
    private Map<Integer, List<ShopShippingAreaVO>> channelShopSpAreaListMap;
}
