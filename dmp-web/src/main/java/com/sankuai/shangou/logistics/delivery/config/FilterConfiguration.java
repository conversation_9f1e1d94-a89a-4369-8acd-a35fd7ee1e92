package com.sankuai.shangou.logistics.delivery.config;

import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurityPrepareFilter;
import com.sankuai.meituan.shangou.empower.auth.sdk.filter.LoginFilterFactoryBean;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.filter.CharacterEncodingFilter;
import org.springframework.web.filter.DelegatingFilterProxy;

import javax.servlet.DispatcherType;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by liujia on 2018/8/15.
 */
@Configuration
public class FilterConfiguration {
    private static final int CHARACTER_ENCODING_FILTER_ORDER = 2;

    /**
     * 确保加载顺序
     */
    private static final int JMONITOR_LISTENER_ORDER = Ordered.HIGHEST_PRECEDENCE;
    private static final int MT_CONTEXT_LISTENER_ORDER = JMONITOR_LISTENER_ORDER + 1;
    private static final int HTTP_MONITOR_FILTER_ORDER = MT_CONTEXT_LISTENER_ORDER + 1;
    private static final int CROS_MONITOR_FILTER_ORDER = HTTP_MONITOR_FILTER_ORDER + 1;
    private static final int LOGIN_FILTER_ORDER = CROS_MONITOR_FILTER_ORDER + 1;
    private static final int CAT_MONITOR_FILTER_ORDER = LOGIN_FILTER_ORDER + 1;
    private static final int ENCODING_MONITOR_FILTER_ORDER = CAT_MONITOR_FILTER_ORDER + 1;
    private static final int AUTH_FILTER_ORDER = ENCODING_MONITOR_FILTER_ORDER + 1;


    // 数据鉴权准备 filter，这里放最后面
    private static final int DATA_SECURITY_PREPARE_FILTER_ORDER = Ordered.LOWEST_PRECEDENCE;


    //登录校验filter
    @Bean
    public FilterRegistrationBean loginFilter(){
        DelegatingFilterProxy filter = new DelegatingFilterProxy();
        FilterRegistrationBean registration = new FilterRegistrationBean();
        filter.setTargetBeanName("loginFilterBean");
        filter.setTargetFilterLifecycle(true);

        registration.setFilter(filter);
        registration.addUrlPatterns("/dmp/api/app/*");

        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setName("loginFilter");

        registration.setOrder(LOGIN_FILTER_ORDER);
        return registration;
    }


    //登录factoryBean 和相关配置
    @Bean
    public LoginFilterFactoryBean loginFilterBean(){
        LoginFilterFactoryBean loginFilterFactoryBean = new LoginFilterFactoryBean();
        loginFilterFactoryBean.setSecret("");//账号系统分配
        loginFilterFactoryBean.setLogEnable(true);//info日志打印
        loginFilterFactoryBean.setExcludedUriList("/api/account/**");//不校验配置urlList（ANT风格） 逗号分隔
        loginFilterFactoryBean.setIncludedUriList("");//校验配置urlList（ANT风格） 逗号分隔,优先级高于exludedUriList，同时配置则只有include生效
        return loginFilterFactoryBean;
    }


    //用于校验数据权限
    @Bean
    public FilterRegistrationBean dataSecurityPrepareFilter() {
        DataSecurityPrepareFilter filter = new DataSecurityPrepareFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/*");
        registration.setName("data-security-prepare-filter");
        registration.setOrder(DATA_SECURITY_PREPARE_FILTER_ORDER);
        return registration;
    }
}
