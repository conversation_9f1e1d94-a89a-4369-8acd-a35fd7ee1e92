package com.sankuai.shangou.logistics.delivery.poi.response;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023-09-06
 * @email <EMAIL>
 */
@Data
public class Result<T> {

    private int code;

    private String msg;

    private T data;

    private static final int SUCCESS_CODE = 0;

    public Result(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> Result<T> success(T t) {
        return new Result<>(SUCCESS_CODE, StringUtils.EMPTY, t);
    }


}
