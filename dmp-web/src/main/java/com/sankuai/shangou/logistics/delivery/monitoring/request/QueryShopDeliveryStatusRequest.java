package com.sankuai.shangou.logistics.delivery.monitoring.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2021-12-1
 * Desc 骑手监控大屏-查询门店配送情况请求
 */
@TypeDoc(
        description = "骑手监控大屏-查询门店配送情况请求"
)
@ApiModel("骑手监控大屏-查询门店配送情况请求")
@Data
public class QueryShopDeliveryStatusRequest {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private Long shopId;

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private Boolean filterThirdDelivery = false;

    public Optional<String> validate() {
        if (shopId == null || shopId <= 0) {
            return Optional.of("门店 ID 参数不合法");
        }
        return Optional.empty();
    }
}
