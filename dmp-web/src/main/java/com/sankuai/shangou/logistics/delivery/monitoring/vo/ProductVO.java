package com.sankuai.shangou.logistics.delivery.monitoring.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/09/06 14:32
 */
@TypeDoc(
        description = "配送监控大屏中订单商品信息"
)
@ApiModel("配送监控大屏中订单商品信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductVO {

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "赋能商品的sku"
    )
    @ApiModelProperty(value = "赋能商品的sku", required = true)
    public String sku;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "规格", required = true)
    private String specification;

    @FieldDoc(
            description = "数量或份数"
    )
    @ApiModelProperty(value = "数量或份数", required = true)
    public Integer quantity;

}
