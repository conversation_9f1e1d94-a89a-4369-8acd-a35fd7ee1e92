package com.sankuai.shangou.logistics.delivery.config.interceptor;

import com.dianping.cat.Cat;
import com.sankuai.shangou.logistics.delivery.shippingarea.utils.MccUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 配送范围管理业务权限拦截器
 */
@Slf4j
@Component
public class RegionSelectShippingBusinessInterceptor extends BusinessInterceptor {
    private static final String POI_SHIPPING_PERMISSION_CODE = "DELIVERY_REGION_WAIMA";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!MccUtils.enableShippingBusinessAuthCheck()) {
            return true;
        }

        if (hasBusinessPermission(POI_SHIPPING_PERMISSION_CODE)) {
            return true;
        } else {
            //打点
            Cat.logEvent("region.select.shipping", "auth.fail");
            noAuth(response);
            return false;
        }
    }
}
