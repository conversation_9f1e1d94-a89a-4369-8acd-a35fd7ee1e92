package com.sankuai.shangou.logistics.delivery.monitoring.utils;

import com.dianping.lion.client.Lion;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.shangou.logistics.delivery.verify.config.MccConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.dianping.lion.client.Lion.getConfigRepository;

/**
 * <AUTHOR>
 * @since 2024/5/11 12:04
 **/
@Slf4j
public class MccUtils {
    private static final String ORDER_API_APP_KEY = "com.sankuai.shangou.qnh.orderapi";

    /**
     * 歪马临时骑手的角色id
     */
    public static List<String> tempRiderRoleIds() {
        String roleIds = ConfigUtilAdapter.getString("temp.rider.role.ids","");
        if (StringUtils.isBlank(roleIds)) {
            return Collections.emptyList();
        }
        return Splitter.on(",").splitToList(roleIds);
    }

    public static Boolean isDhScenePoi(Long storeId) {
        try {
            List<Long> stores = Lion.getConfigRepository(ORDER_API_APP_KEY).getList("dh_scene_poi_switch", Long.class, Collections.emptyList());
            //全量逻辑
            if (stores.size() == 1 && stores.get(0).equals(-1L)) {
                return true;
            }
            return stores.contains(storeId);
        } catch (Exception e) {
            log.error("isDhScenePoi error", e);
            return false;
        }
    }

    public static List<String> getRestaurantSceneList() {
        return getConfigRepository().getList("restaurant.scene.list", String.class, Lists.newArrayList("餐馆"));
    }

    public static Boolean acceptPickOrderSwitch(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.sgshopmgmt.empower.pieapi").getList("accept.pick.order.gray.store.list", Long.class, Collections.emptyList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(-1L, grayStoreIds.get(0))) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }



    public static boolean isDhTenant(Long tenantId) {
        List<Long> tenantIds = MccUtils.getDrunkHorseTenantId();
        return tenantIds.contains(tenantId);
    }


    /**
     * 歪马租户id
     *
     * @return
     */
    public static List<Long> getDrunkHorseTenantId() {
        String dhTenantIds = Lion.getConfigRepository().get("drunk.horse.tenant.id.list", "1000395");

        return Splitter.on(",")
                .trimResults()
                .splitToList(dhTenantIds).stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());

    }


    public static Boolean isTrainingGrayStore(Long storeId) {
        if (storeId == null) {
            return false;
        }

        List<Long> grayStoreList = getConfigRepository("com.sankuai.shangou.bizmng.labor")
                .getList("training.gray.store.ids", Long.class, Collections.emptyList());

        if (grayStoreList.size() == 1 && Objects.equals(grayStoreList.get(0), -1L)) {
            return true;
        }

        return grayStoreList.contains(storeId);
    }
}
