package com.sankuai.shangou.logistics.delivery.config.advisor;

import com.sankuai.shangou.advisor.ResponseBodyWithCodeMsgAdvisor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 必须显式指定RestControllerAdvice的生效范围，否则@ExceptionHandler的处理不正确
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice(basePackages = {
        "com.sankuai.shangou.logistics.delivery.board",
        "com.sankuai.shangou.logistics.delivery.gray",
        "com.sankuai.shangou.logistics.delivery.monitoring",
        "com.sankuai.shangou.logistics.delivery.poi",
        "com.sankuai.shangou.logistics.delivery.questionnaire",
        "com.sankuai.shangou.logistics.delivery.realtimeboard",
        "com.sankuai.shangou.logistics.delivery.sealcontainer",
        "com.sankuai.shangou.logistics.delivery.shippingarea",
        "com.sankuai.shangou.logistics.delivery.verify"})
public class MvcResponseBodyWithCodeMsgAdvisor extends ResponseBodyWithCodeMsgAdvisor {

}
