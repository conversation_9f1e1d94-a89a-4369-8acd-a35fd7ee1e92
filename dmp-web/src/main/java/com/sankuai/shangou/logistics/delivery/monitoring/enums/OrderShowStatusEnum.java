package com.sankuai.shangou.logistics.delivery.monitoring.enums;

import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;

/**
 * 运单状态展示枚举.
 *
 * <AUTHOR>
 * @since 2021/12/5 18:05
 */
public enum OrderShowStatusEnum {

    WAIT_TAKE(30, "待领取"),
    PICKING(40, "拣货中"),
    IN_DELIVERY(50, "配送中"),
    OTHER_STATUS(-1,"其他状态")
    ;

    private final int code;
    private final String desc;


    OrderShowStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderShowStatusEnum mapRiderDeliveryStatusEnum(RiderDeliveryStatusEnum source) {
        switch (source) {
            case WAITING_TO_ASSIGN_RIDER:
                return WAIT_TAKE;
            case RIDER_ASSIGNED:
                return PICKING;
            case RIDER_TAKEN_GOODS:
                return IN_DELIVERY;
            default:
                return OTHER_STATUS;
        }
    }

    public static OrderShowStatusEnum mapThirdRiderDeliveryStatusEnum(DeliveryStatusEnum source) {
        switch (source) {
            case INIT:
            case DELIVERY_LAUNCHED:
            case WAITING_TO_ASSIGN_RIDER:
                return WAIT_TAKE;
            case RIDER_ASSIGNED:
            case RIDER_ARRIVED_SHOP:
                return PICKING;
            case RIDER_TAKEN_GOODS:
                return IN_DELIVERY;
            default:
                return OTHER_STATUS;
        }
    }

    /**
     * 将运单状态枚举值映射为运单展示状态枚举值
     */
    public static OrderShowStatusEnum mapRiderDeliveryStatusEnum(Integer source) {
        RiderDeliveryStatusEnum riderDeliveryStatusEnum = RiderDeliveryStatusEnum.enumOf(source);
        return mapRiderDeliveryStatusEnum(riderDeliveryStatusEnum);
    }

}
