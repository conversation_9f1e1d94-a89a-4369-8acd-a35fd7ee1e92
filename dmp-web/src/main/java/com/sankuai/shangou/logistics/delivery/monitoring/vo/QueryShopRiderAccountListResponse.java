package com.sankuai.shangou.logistics.delivery.monitoring.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/25
 *
 */
@TypeDoc(
        description = "查询门店骑手名单响应"
)
@ApiModel("查询门店骑手名单响应")
@Data
@Builder
@AllArgsConstructor
public class QueryShopRiderAccountListResponse {

    @FieldDoc(
            description = "骑手账号列表",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "骑手账号列表")
    private List<RiderAccountInfoVO> riderAccountList;

}
