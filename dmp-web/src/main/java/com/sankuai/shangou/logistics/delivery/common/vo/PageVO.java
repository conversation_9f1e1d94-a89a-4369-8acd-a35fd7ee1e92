package com.sankuai.shangou.logistics.delivery.common.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/27 19:21
 **/
@Data
public class PageVO<T> {
    private List<T> list;

    private int page;

    private int pageSize;

    private int total;

    private boolean hasMore;

    public PageVO(List<T> list, int page, int pageSize, int total) {
        this.pageSize = pageSize;
        this.page = page;
        this.total = total;
        this.list = list;
        this.hasMore = total > page * pageSize;
    }
}
