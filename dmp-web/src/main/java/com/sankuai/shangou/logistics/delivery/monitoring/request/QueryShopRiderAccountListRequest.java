package com.sankuai.shangou.logistics.delivery.monitoring.request;

import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.util.Optional;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/4/25
 *
 */
@TypeDoc(
        description = "查询门店骑手名单请求"
)
@ApiModel("查询门店骑手名单请求")
@Data
public class QueryShopRiderAccountListRequest {
    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id")
    private Long shopId;

    public Optional<String> validate() {
        if (shopId == null || shopId <= 0) {
            return Optional.of("门店id参数不合法");
        }

        return Optional.empty();
    }
}