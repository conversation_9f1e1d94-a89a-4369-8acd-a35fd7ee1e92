package com.sankuai.shangou.logistics.delivery.configure;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.shangou.logistics.delivery.config.LionConfigUtils;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigSaveRequest;
import com.sankuai.shangou.logistics.delivery.configure.service.DeliveryConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-07-04
 * @email <EMAIL>
 */
@Slf4j
@RestController
@RequestMapping("dmp/api/web/config")
public class DeliveryConfigController {

    @Resource
    private DeliveryConfigService deliveryConfigService;


    @MethodDoc(
            displayName = "查询门店配置明细",
            description = "查询门店配置明细",
            parameters = {
                    @ParamDoc(
                            name = "tenantId",
                            description = "租户ID",
                            type = Long.class,
                            paramType = ParamType.REQUEST_PARAM,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    ),
                    @ParamDoc(
                            name = "storeId",
                            description = "门店ID",
                            type = Long.class,
                            paramType = ParamType.REQUEST_PARAM,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/web/config/detail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/detail")
    @ResponseBody
    public DeliveryConfigDetailVO queryConfigDetail(@RequestParam("tenantId") Long tenantId,
                                                   @RequestParam("storeId") Long storeId) {
        log.info("查询门店配置明细请求, tenantId: {}, storeId: {}", tenantId, storeId);

        try {
            DeliveryConfigDetailVO result = deliveryConfigService.queryConfigDetail(tenantId, storeId);
            log.info("查询门店配置明细成功, tenantId: {}, storeId: {}", tenantId, storeId);
            return result;
        } catch (Exception e) {
            log.error("查询门店配置明细失败, tenantId: {}, storeId: {}", tenantId, storeId, e);
            throw e;
        }
    }

    @MethodDoc(
            displayName = "保存门店配置明细",
            description = "保存门店配置明细",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "保存请求",
                            type = DeliveryConfigSaveRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/web/config/save",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/save")
    @ResponseBody
    public void saveConfigDetail(@RequestBody DeliveryConfigSaveRequest request) {
        log.info("保存门店配置明细请求, tenantId: {}, storeId: {}", request.getDeliveryConfigDetailVO().getTenantId(), request.getDeliveryConfigDetailVO().getStoreId());

        try {
            deliveryConfigService.saveConfigDetail(request);
            log.info("保存门店配置明细成功, tenantId: {}, storeId: {}", request.getDeliveryConfigDetailVO().getTenantId(), request.getDeliveryConfigDetailVO().getStoreId());
        } catch (Exception e) {
            log.error("保存门店配置明细失败, tenantId: {}, storeId: {}", request.getDeliveryConfigDetailVO().getTenantId(), request.getDeliveryConfigDetailVO().getStoreId(), e);
            throw e;
        }
    }


    @MethodDoc(
            displayName = "查询配送支持的订单标签",
            description = "查询配送支持的订单标签映射关系",
            parameters = {},
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/web/config/deliverySupportOrderTags",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/deliverySupportOrderTags")
    @ResponseBody
    public DeliverySupportOrderTagResponse deliverySupportOrderTags() {
        log.info("查询配送支持的订单标签请求");

        try {
            DeliverySupportOrderTagResponse result = new DeliverySupportOrderTagResponse(LionConfigUtils.getSceneOrderTagMap());
            log.info("查询配送支持的订单标签成功");
            return result;
        } catch (Exception e) {
            log.error("查询配送支持的订单标签失败", e);
            throw e;
        }
    }

}
