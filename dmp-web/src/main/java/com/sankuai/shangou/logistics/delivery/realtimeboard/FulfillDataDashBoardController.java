package com.sankuai.shangou.logistics.delivery.realtimeboard;


import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder;
import com.sankuai.shangou.commons.thrift.publisher.response.PaginationList;
import com.sankuai.shangou.commons.thrift.publisher.response.TPageResult;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.fulfill.dto.*;
import com.sankuai.shangou.logistics.delivery.fulfill.request.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.*;


@InterfaceDoc(
        displayName = "歪马履约配送数据看板相关接口",
        type = "restful",
        scenarios = "歪马履约配送数据看板相关接口",
        description = "歪马履约配送数据看板相关接口",
        version = "V1.0"
)
@Slf4j
@RestController
@RequestMapping("dmp/api/web/fulfill-data/")
public class FulfillDataDashBoardController {

    @Resource
    private FulfillDataSearchService fulfillDataSearchService;


    @MethodDoc(
            description = "数据卡片模块-实时数据",
            displayName = "数据卡片模块-实时数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "数据卡片模块-实时数据",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/fulfill-data/summary/real-time",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "数据卡片模块-实时数据"
    )
    @PostMapping(value = "/summary/real-time")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({@SecurityParam(value = "storeIds")})
    public FulfillSummaryRealTimeResp summaryRealTime(@RequestBody FulfillDataQueryRequest request) {
        request.setTenantId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException(validate.get());
        }

        return fulfillDataSearchService.querySummaryRealTime(request);
    }


    @MethodDoc(
            description = "数据卡片模块-累计数据",
            displayName = "数据卡片模块-累计数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "歪马履约配送数据看板累计统计数据",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/fulfill-data/summary/cumulative",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "数据卡片模块-累计数据"
    )
    @PostMapping(value = "/summary/cumulative")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({@SecurityParam(value = "storeIds")})
    public FulfillSummaryCumulativeResp cumulative(@RequestBody FulfillDataQueryRequest request) {
        request.setTenantId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException(validate.get());
        }

        //从数仓拿数据
        return fulfillDataSearchService.queryCumulativeSummary(request);
    }

    @MethodDoc(
            description = "数据卡片模块-三方运力数据",
            displayName = "数据卡片模块-三方运力数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "歪马履约配送数据看板累计统计数据",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/fulfill-data/summary/carrier",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "数据卡片模块-累计数据"
    )
    @PostMapping(value = "/summary/carrier")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({@SecurityParam(value = "storeIds")})
    public FulfillSummaryCarrierResp summaryCarrier(@RequestBody FulfillDataQueryRequest request) {
        request.setTenantId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException(validate.get());
        }

        //从数仓拿数据
        return fulfillDataSearchService.querySummaryCarrier(request);
    }


    @MethodDoc(
            description = "分段数据模块-实时数据",
            displayName = "分段数据模块-实时数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分段数据模块-实时数据",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/fulfill-data/segmentation/real-time",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "分段数据模块-实时数据"
    )
    @PostMapping(value = "/segmentation/real-time")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({@SecurityParam(value = "storeIds")})
    public SegmentationFulfillDataResponse segmentationRealTime(@RequestBody SegmentationFulfillDataQueryRequest request) {
        request.setTenantId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException(validate.get());
        }

        //从数仓拿数据
        return fulfillDataSearchService.querySegmentationRealTime(request);
    }

    @MethodDoc(
            description = "分段数据模块-累计数据",
            displayName = "分段数据模块-累计数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分段数据模块-累计数据",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/fulfill-data/segmentation/cumulative",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "分段数据模块-累计数据"
    )
    @PostMapping(value = "/segmentation/cumulative")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({@SecurityParam(value = "storeIds")})
    public SegmentationFulfillDataResponse segmentationCumulative(@RequestBody SegmentationFulfillDataQueryRequest request) {
        request.setTenantId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException(validate.get());
        }

        //从数仓拿数据
        return fulfillDataSearchService.querySegmentationCumulative(request);
    }

    @MethodDoc(
            description = "门店+承运商维度数据",
            displayName = "门店+承运商维度数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "歪马履约配送数据看板门店+承运商维度数据",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/fulfill-data/carrier-statistic",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "门店+承运商维度数据"
    )
    @PostMapping(value = "/carrier-statistic")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({@SecurityParam(value = "storeIds")})
    public PaginationList<FulfillCarrierStatisticsDTO> storeCarrierStatistic(@RequestBody FulfillDataPageQueryDTO request) {
        request.setTenantId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException(validate.get());
        }

        //从数仓拿数据
        return fulfillDataSearchService.queryCarrierStatistic(request).getData();
    }

    @MethodDoc(
            description = "门店+1/+2维度数据",
            displayName = "门店+1/+2维度数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店+1/+2维度数据",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/fulfill-data/department-statistics",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "分段数据模块-累计数据"
    )
    @PostMapping(value = "/department-statistics")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({@SecurityParam(value = "storeIds")})
    public PaginationList<DepartmentFulfillDataDTO> departmentStatistics(@RequestBody DepartmentFulfillDataQueryRequest request) {
        request.setTenantId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException(validate.get());
        }

        //从数仓拿数据
        return fulfillDataSearchService.queryDepartmentLevelStatistic(request).getData();
    }

    @MethodDoc(
            description = "员工维度数据（单门店模式下）",
            displayName = "员工维度数据（单门店模式下）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "员工维度数据（单门店模式下）",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/fulfill-data/rider-statistics",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "员工维度数据（单门店模式下）"
    )
    @PostMapping(value = "/rider-statistics")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({@SecurityParam(value = "storeId")})
    public PaginationList<FulfillRiderStatisticsDTO> storeRiderStatistics(@RequestBody PageFulfillDataQueryRequest request) {
        request.setTenantId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
        request.setAppId(WebLoginContextHolder.getWebLoginContext().getAppId());
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException(validate.get());
        }

        return fulfillDataSearchService.queryRiderStatistics(request).getData();
    }

    @MethodDoc(
            description = "门店维度数据（多门店模式下）",
            displayName = "门店维度数据（多门店模式下）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店维度数据（多门店模式下）",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/fulfill-data/store-statistics",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "门店维度数据（多门店模式下）"
    )
    @PostMapping(value = "/store-statistics")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({@SecurityParam(value = "storeIds")})
    public PaginationList<FulfillStoreStatisticsDTO> storeStatistics(@RequestBody PageFulfillDataQueryRequest request) {
        request.setTenantId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
        request.setAppId(WebLoginContextHolder.getWebLoginContext().getAppId());
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException(validate.get());
        }

        return fulfillDataSearchService.queryStoreStatistics(request).getData();
    }


    @MethodDoc(
            description = "异常单数据",
            displayName = "异常单数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "异常单数据",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId，以及request.storeIds鉴权用户有这些门店列表权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_TYPE",
                            content = "数据鉴权"
                    ),
            },
            restExampleUrl = "POST dmp/api/web/fulfill-data/abnormal",
            restExamplePostData = "{}",
            restExampleResponseData = "{'code':0,'msg':'...'}",
            returnValueDescription = "异常单数据"
    )
    @PostMapping(value = "/abnormal")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({@SecurityParam(value = "storeIds")})
    public PaginationList<AbnormalOrderDTO> abnormal(@RequestBody PageFulfillDataQueryRequest request) {
        request.setTenantId(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId());
        request.setAppId(WebLoginContextHolder.getWebLoginContext().getAppId());
        Optional<String> validate = request.validate();
        if (validate.isPresent()) {
            throw new IllegalArgumentException(validate.get());
        }

        //从数仓拿数据
        return fulfillDataSearchService.queryAbnormalOrderList(request).getData();
    }

}
