package com.sankuai.shangou.logistics.delivery.gray.request;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/5/11 17:58
 **/
@Data
public class WebGrayConfigRequest {
    private String grayKey;

    private Long storeId;

    public void validate() {
        if (StringUtils.isBlank(grayKey)) {
            throw new IllegalArgumentException("grayKey不能为空");
        }

        if (storeId == null || storeId <= 0L) {
            throw new IllegalArgumentException("storeId不能为空");
        }
    }
}
