package com.sankuai.shangou.logistics.delivery.monitoring.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/4 11:48
 **/
@TypeDoc(
        description = "配送范围VO"
)
@ApiModel("配送范围VO")
@Data
@NoArgsConstructor
public class ShopShippingAreaVO {
    @FieldDoc(description = "是否是大范围")
    @ApiModelProperty(value = "是否是大范围")
    private Boolean isWiderArea;

    @FieldDoc(description = "是否是特殊时段范围")
    @ApiModelProperty(value = "是否是特殊时段范围")
    private Boolean isSpecialTimeArea;

    @FieldDoc(description = "边界坐标点")
    @ApiModelProperty(value = "边界坐标点")
    private List<DeliveryCoordinateVO> boundPoints;
}
