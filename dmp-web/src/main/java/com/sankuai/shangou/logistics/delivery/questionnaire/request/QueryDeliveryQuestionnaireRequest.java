package com.sankuai.shangou.logistics.delivery.questionnaire.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/16 23:52
 **/
@Data
public class QueryDeliveryQuestionnaireRequest {

    @FieldDoc(description = "运单id列表")
    private List<Long> deliveryOrderIdList;
}
