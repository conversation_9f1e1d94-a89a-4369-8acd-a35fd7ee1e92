package com.sankuai.shangou.logistics.delivery.monitoring.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/09/06 17:10
 */
@TypeDoc(
        description = "配送监控大屏中订单信息"
)
@ApiModel("配送监控大屏中订单信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderInfoVO {

    @FieldDoc(
            description = "订单号"
    )
    @ApiModelProperty(value = "订单号")
    private String orderId;

    @FieldDoc(
            description = "用户类型 10-普通用户, 15-会员用户"
    )
    @ApiModelProperty(value = "用户类型 10-普通用户, 15-会员用户")
    @Deprecated
    private Integer orderUserType;

    @FieldDoc(
            description = "配送监控大屏中订单下单商品信息"
    )
    @ApiModelProperty(value = "配送监控大屏中订单下单商品信息")
    private List<ProductVO> products;

    @FieldDoc(
            description = "配送监控大屏中订单赠品商品信息"
    )
    @ApiModelProperty(value = "配送监控大屏中订单赠品商品信息")
    private List<ProductVO> gifts;

    @FieldDoc(
            description = "订单标签 10-会员订单 20-封签交付 30-餐馆订单"
    )
    @ApiModelProperty(value = "订单标签 10-会员订单 20-封签交付 30-餐馆订单")
    private List<Integer> orderTagList;

    @FieldDoc(description = "排序好的标签")
    @ApiModelProperty(value = "排序好的标签")
    private List<SortedOrderTagVO> sortedTagList;
}
