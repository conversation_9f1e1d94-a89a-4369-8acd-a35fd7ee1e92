package com.sankuai.shangou.logistics.delivery.board.load.vo;

import com.sankuai.shangou.logistics.delivery.indicator.dto.BizIndicatorEnum;
import com.sankuai.shangou.logistics.delivery.indicator.dto.IndicatorGroupType;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/9 17:54
 **/

@Data
public class DeliveryLoadDataExcelVO {
    private String storeName;

    private String date;

    private String bizTime;

    Map<BizIndicatorEnum, String> indicatorValues;


    public static List<DeliveryLoadDataExcelVO> build(DeliveryLoadGraphDataVO deliveryLoadGraphDataVO, String storeName) {
        Map<String, List<DeliveryLoadDataPointVO>> histogramIndicatorMap = deliveryLoadGraphDataVO.getHistogramDataPoints()
                .stream().collect(Collectors.groupingBy(DeliveryLoadDataPointVO::getX));

        Map<String, List<DeliveryLoadDataPointVO>> lineIndicatorMap = deliveryLoadGraphDataVO.getLineDataPoints()
                .stream().collect(Collectors.groupingBy(DeliveryLoadDataPointVO::getX));

        HashMap<String, List<DeliveryLoadDataPointVO>> indicatorMap = new HashMap<>(histogramIndicatorMap);
        lineIndicatorMap.forEach((key,value) -> {
            if (indicatorMap.containsKey(key)) {
                indicatorMap.get(key).addAll(value);
            } else {
                indicatorMap.put(key, value);
            }
        });
        List<DeliveryLoadDataExcelVO> deliveryLoadDataExcelVOS = new ArrayList<>();
        indicatorMap.forEach((key, value) -> {
            Map<String, List<DeliveryLoadDataPointVO>> timeIndicatorMap = value.stream().collect(Collectors.groupingBy(DeliveryLoadDataPointVO::getCode));
            DeliveryLoadDataExcelVO dataExcelVO = new DeliveryLoadDataExcelVO();
            LocalDateTime bizTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(key)), ZoneId.systemDefault());

            dataExcelVO.setStoreName(storeName);
            dataExcelVO.setDate(bizTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            dataExcelVO.setBizTime(bizTime.format(DateTimeFormatter.ofPattern("HH:mm")));

            Map<BizIndicatorEnum, String> indicatorValues = new HashMap<>();
            timeIndicatorMap.forEach((code, point) -> {
                indicatorValues.put(BizIndicatorEnum.enumOf(code), getValue(code, point));
            });
            dataExcelVO.setIndicatorValues(indicatorValues);

            deliveryLoadDataExcelVOS.add(dataExcelVO);
        });

        return deliveryLoadDataExcelVOS;
    }


    private static String getValue(String indicatorCode, List<DeliveryLoadDataPointVO> pointVOS) {
        BizIndicatorEnum bizIndicatorEnum = BizIndicatorEnum.enumOf(indicatorCode);
        if (CollectionUtils.isEmpty(pointVOS)) {
            return "";
        }

        if (bizIndicatorEnum.getIndicatorGroupType() == IndicatorGroupType.BY_SHIFT) {
             return pointVOS.stream().map(DeliveryLoadDataPointVO::getY).reduce(BigDecimal::add).map(BigDecimal::toString).orElse("");
        }

        return Optional.ofNullable(pointVOS.get(0)).map(DeliveryLoadDataPointVO::getY).map(BigDecimal::toString).orElse("");


    }

}
