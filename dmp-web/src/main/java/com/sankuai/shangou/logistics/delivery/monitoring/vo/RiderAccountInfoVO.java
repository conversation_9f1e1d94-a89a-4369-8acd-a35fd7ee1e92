package com.sankuai.shangou.logistics.delivery.monitoring.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@TypeDoc(
        description = "骑手账号信息"
)
@ApiModel("骑手账号信息")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RiderAccountInfoVO {
    @FieldDoc(
            description = "骑手账号 ID"
    )
    @ApiModelProperty(value = "骑手账号 ID", required = true)
    private Long riderAccountId;

    @FieldDoc(
            description = "骑手名字"
    )
    @ApiModelProperty(value = "骑手名字", required = true)
    private String riderName;

    @FieldDoc(
            description = "骑手电话"
    )
    @ApiModelProperty(value = "骑手电话", required = true)
    private String riderPhone;

    @FieldDoc(
            description = "是否为临时骑手"
    )
    @ApiModelProperty(value = "是否为临时骑手", required = true)
    private Boolean tempRiderFlag;

    @FieldDoc(
            description = "是否培训通过", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否培训通过", required = false)
    private Boolean isCompleteTrain;


    @FieldDoc(
            description = "骑手限制接单详情", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "骑手限制接单详情", required = false)
    private List<LimitItemVO> limitItemList;
}
