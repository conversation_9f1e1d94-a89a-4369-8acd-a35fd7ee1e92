package com.sankuai.shangou.logistics.delivery.gray;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.gray.request.GrayConfigRequest;
import com.sankuai.shangou.logistics.delivery.gray.request.WebGrayConfigRequest;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/4/19 11:47
 **/
@InterfaceDoc(
        displayName = "功能灰度配置相关接口",
        type = "restful",
        scenarios = "功能灰度配置相关接口",
        description = "功能灰度配置相关接口",
        host = "https://fnsaas.waimai.meituan.com/"
)
@Slf4j
@RestController
@RequestMapping("dmp/api/web/gray-config")
public class WebGrayConfigController {
    @MethodDoc(
            displayName = "查询门店是否是灰度门店",
            description = "查询门店是否是灰度门店",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询门店是否是灰度门店请求",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/web/gray-config/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/query")
    @ResponseBody
    @MethodLog(logResponse = true, logRequest = true)
    public Boolean querySelfDeliveryConfigList(@RequestBody WebGrayConfigRequest request) {
        request.validate();
        return GrayConfigUtils.judgeIsGrayStore(WebLoginContextHolder.getWebLoginContext().getLoginUser().getTenantId(),
                request.getStoreId(), request.getGrayKey());

    }

}
