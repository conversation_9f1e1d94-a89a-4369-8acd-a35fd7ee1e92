package com.sankuai.shangou.logistics.delivery.configure;

import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * DeliveryConfigController测试类
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@RunWith(MockitoJUnitRunner.class)
public class DeliveryConfigControllerTest {

    @InjectMocks
    private DeliveryConfigController deliveryConfigController;

    @Mock
    private com.sankuai.shangou.logistics.delivery.configure.service.DeliveryConfigService deliveryConfigService;

    @Test
    public void testQueryConfigDetail() {
        // 测试参数
        Long tenantId = 1000666L;
        Long storeId = 49869573L;

        // 调用方法
        DeliveryConfigDetailVO result = deliveryConfigController.queryConfigDetail(tenantId, storeId);

        // 验证结果
        assertNotNull(result);
        assertEquals("1000666", result.getTenantId());
        assertEquals("49869573", result.getStoreId());
        
        // 验证配送平台配置
        assertNotNull(result.getDeliveryPlatformConfig());
        assertEquals(2, result.getDeliveryPlatformConfig().size());
        
        DeliveryConfigDetailVO.DeliveryPlatformConfigVO config1 = result.getDeliveryPlatformConfig().get(0);
        assertEquals(Integer.valueOf(100), config1.getChannelType());
        assertEquals(Integer.valueOf(2), config1.getPlatformCode());
        assertEquals(Integer.valueOf(1), config1.getStatus());
        assertEquals(Integer.valueOf(1), config1.getDeliveryLaunchPoint());
        assertEquals("", config1.getRedirectUrl());
        assertEquals(Integer.valueOf(0), config1.getDeliveryLaunchDelayMinutes());
        assertEquals(Integer.valueOf(60), config1.getBookingOrderDeliveryLaunchMinutes());
        
        // 验证二级配送配置
        assertNotNull(config1.getSecondDeliveryConfig());
        assertEquals(Integer.valueOf(5), config1.getSecondDeliveryConfig().getSecondPlatformCodeCode());
        assertEquals("", config1.getSecondDeliveryConfig().getRedirectUrl());
        
        // 验证禁用条件
        assertNotNull(config1.getSecondDeliveryConfig().getForbiddenCondition());
        assertEquals(2, config1.getSecondDeliveryConfig().getForbiddenCondition().getOrderTags().size());
        assertEquals("150.5", config1.getSecondDeliveryConfig().getForbiddenCondition().getOrderActualPayment());
        
        // 验证自送预约配送规则
        assertNotNull(config1.getSelfDeliverybookingDeliveryRule());
        assertTrue(config1.getSelfDeliverybookingDeliveryRule().getNeedBusinessHoursPushdown());
        assertEquals(2, config1.getSelfDeliverybookingDeliveryRule().getBookingPushDownTimeConfig().size());
        
        // 验证自送配置
        assertNotNull(result.getSelfDeliveryConfig());
        assertEquals(Integer.valueOf(1), result.getSelfDeliveryConfig().getSelfDeliveryMode());
        assertEquals(Integer.valueOf(1), result.getSelfDeliveryConfig().getInternalNavigationMode());
        assertEquals(Integer.valueOf(1), result.getSelfDeliveryConfig().getCompletedSortMode());
        assertEquals(3, result.getSelfDeliveryConfig().getRiderTransRoles().size());
        
        // 验证考核时间
        assertNotNull(result.getSelfDeliveryConfig().getAssertTime());
        assertEquals(1, result.getSelfDeliveryConfig().getAssertTime().size());
        
        DeliveryConfigDetailVO.AssertTimeVO assertTime = result.getSelfDeliveryConfig().getAssertTime().get(0);
        assertEquals(Integer.valueOf(1), assertTime.getType());
        assertEquals("挑战", assertTime.getHint());
        assertEquals(2, assertTime.getOrderTags().size());
        assertEquals("30", assertTime.getFormula());
        
        // 验证配送完成模式
        assertNotNull(result.getSelfDeliveryConfig().getDeliveryCompleteMode());
        assertEquals(Integer.valueOf(1), result.getSelfDeliveryConfig().getDeliveryCompleteMode().getDistanceReminder());
        
        // 验证配送完成配置
        assertNotNull(result.getSelfDeliveryConfig().getDeliveryCompleteMode().getDeliveryCompleteConfig());
        assertEquals(Integer.valueOf(1), result.getSelfDeliveryConfig().getDeliveryCompleteMode().getDeliveryCompleteConfig().getOperationMode());
        assertEquals(2, result.getSelfDeliveryConfig().getDeliveryCompleteMode().getDeliveryCompleteConfig().getAllExamplePicInfoList().size());
        assertEquals(1, result.getSelfDeliveryConfig().getDeliveryCompleteMode().getDeliveryCompleteConfig().getSpecialProductUploadPicConfig().size());
        assertEquals("已为您拍照留存", result.getSelfDeliveryConfig().getDeliveryCompleteMode().getDeliveryCompleteConfig().getSendPicToCustomerTips());
        assertEquals("请联系顾客确认", result.getSelfDeliveryConfig().getDeliveryCompleteMode().getDeliveryCompleteConfig().getNotContactCustomerTips());
        assertEquals(Integer.valueOf(300), result.getSelfDeliveryConfig().getDeliveryCompleteMode().getDeliveryCompleteConfig().getUploadImageDurationThreshold());
        assertTrue(result.getSelfDeliveryConfig().getDeliveryCompleteMode().getDeliveryCompleteConfig().getIsShowNotContactCustomerTips());
        
        // 验证配送提醒配置
        assertNotNull(result.getSelfDeliveryConfig().getDeliveryRemindConfig());
        assertEquals(Integer.valueOf(5), result.getSelfDeliveryConfig().getDeliveryRemindConfig().getReceiveTimeOutMins());
        assertEquals(Integer.valueOf(10), result.getSelfDeliveryConfig().getDeliveryRemindConfig().getTakenTimeOutMins());
        assertEquals(Integer.valueOf(15), result.getSelfDeliveryConfig().getDeliveryRemindConfig().getSoonDeliveryTimeoutMinsBeforeEta());
        
        // 验证配送超时配置
        assertNotNull(result.getSelfDeliveryConfig().getDeliveryRemindConfig().getDeliveryTimeOutsBeforeEtaConfig());
        assertEquals(Integer.valueOf(20), result.getSelfDeliveryConfig().getDeliveryRemindConfig().getDeliveryTimeOutsBeforeEtaConfig().getImmediateBeforeEtaMins());
        assertEquals(Integer.valueOf(30), result.getSelfDeliveryConfig().getDeliveryRemindConfig().getDeliveryTimeOutsBeforeEtaConfig().getBookingBeforeEtaMins());
        
        // 打印JSON格式以便验证
        System.out.println("Generated JSON:");
        System.out.println(JSON.toJSONString(result, true));
    }
}
