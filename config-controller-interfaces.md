# 门店配送配置接口文档

## 1. 查询门店配置明细

### 接口描述
查询指定租户和门店的配送配置明细信息。

### 请求方式
- **URL**: `/dmp/api/web/config/detail`
- **Method**: POST

### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| tenantId | Long | 是 | 租户ID |
| storeId | Long | 是 | 门店ID |

### 请求示例
```http
POST /dmp/api/web/config/detail?tenantId=10001&storeId=20001
```

### 响应参数

| 参数名 | 类型 | 描述 |
|-------|------|------|
| tenantId | Long | 租户ID |
| storeId | Long | 门店ID |
| deliveryPlatformConfig | Array | 配送平台配置列表 |
| selfDeliveryConfig | Object | 自送配置 |

#### deliveryPlatformConfig 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| channelType | Integer | 渠道类型 |
| platformCode | Integer | 平台编码 |
| status | Integer | 状态：0-禁用，1-启用 |
| deliveryLaunchPoint | Integer | 配送发起点 |
| redirectUrl | String | 跳转链接 |
| deliveryLaunchDelayMinutes | Integer | 配送发起延迟分钟数 |
| bookingOrderDeliveryLaunchMinutes | Integer | 预约单配送发起分钟数 |
| secondDeliveryConfig | Object | 二级配送配置 |
| selfDeliveryBookingDeliveryRule | Object | 自送预约配送规则 |

#### secondDeliveryConfig 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| secondPlatformCodeCode | List<Integer> | 二级平台编码列表 |
| redirectUrl | String | 跳转链接 |
| forbiddenCondition | Object | 禁用条件 |

#### forbiddenCondition 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| orderTags | List<Integer> | 订单标签列表 |
| orderActualPayment | String | 订单实际支付金额 |

#### selfDeliveryBookingDeliveryRule 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| needBusinessHoursPushdown | Boolean | 是否需要营业时间下推 |
| bookingPushDownTimeConfig | List<BookingPushDownTimeConfigVO> | 预约下推时间配置列表 |

#### bookingPushDownTimeConfig 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| orderTags | List<Integer> | 订单标签列表 |
| condition | List<ConditionVO> | 条件列表 |
| formula | String | 计算公式 |

#### condition 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| identifer | String | 标识符 |
| interval | IntervalVO | 区间配置 |

#### interval 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| values | List<String> | 区间值列表 |
| intervalType | Integer | 区间类型 |

#### selfDeliveryConfig 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| assertTime | List<AssertTimeVO> | 配送时长考核配置列表 |
| selfDeliveryMode | Integer | 自送拣配作业模式 |
| internalNavigationMode | Integer | 内部导航模式 |
| completedSortMode | Integer | 已完成排序模式 |
| riderTransRoles | List<Long> | 骑手转换角色列表 |
| deliveryCompleteMode | DeliveryCompleteModeVO | 确认送达操作配置 |
| deliveryRemindConfig | DeliveryRemindConfigVO | 配送提醒配置 |

#### assertTime 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| type | Integer | 考核类型 |
| hint | String | 提示信息 |
| timeConfigType | String | 时长设置类型：1-固定时长，2-eta+-分钟 |
| orderTags | List<Integer> | 订单标签列表 |
| distanceConfigVOS | List<DistanceConfigVO> | 距离配置列表 |

#### distanceConfig 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| condition | List<ConditionVO> | 条件列表 |
| formula | String | 计算公式 |

#### deliveryCompleteMode 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| distanceReminder | Integer | 距离提醒：0-不提示，1-提示 |
| deliveryCompleteConfig | DeliveryCompleteConfigVO | 配送完成配置 |

#### deliveryCompleteConfig 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| operationMode | Integer | 经营类型：1-直营，2-加盟，3-赋能 |
| allExamplePicInfoList | List<ExamplePicInfoVO> | 所有示例图片信息列表 |
| specialProductUploadPicConfig | List<SpecialProductUploadPicConfigVO> | 特殊商品上传图片配置 |
| sendPicToCustomerTips | String | 发送图片给顾客提示 |
| notContactCustomerTips | String | 未联系顾客提示 |
| uploadImageDurationThreshold | Integer | 上传图片最大时长阈值（秒） |
| isShowNotContactCustomerTips | Boolean | 是否显示未联系顾客提示 |

#### examplePicInfo 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| type | Integer | 图片类型 |
| name | String | 图片名称 |
| picUrl | String | 图片链接 |
| order | Integer | 显示顺序 |

#### specialProductUploadPicConfig 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| productType | String | 商品类型 |
| picTypeList | List<Integer> | 图片类型列表 |
| isForceUploadPic | Boolean | 是否强制上传图片 |
| needUploadPicCount | Integer | 需要上传的照片张数 |

#### deliveryRemindConfig 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| receiveTimeOutMins | Integer | 领取超时时间（分钟），0为未开启 |
| takenTimeOutMins | Integer | 取货超时时间（分钟），0为未开启 |
| soonDeliveryTimeoutMinsBeforeEta | Integer | 即将超时时间（分钟），0为未开启 |
| deliveryTimeOutsBeforeEtaConfig | DeliveryTimeOutsBeforeEtaConfigVO | 配送超时配置 |

#### deliveryTimeOutsBeforeEtaConfig 对象

| 参数名 | 类型 | 描述 |
|-------|------|------|
| immediateBeforeEtaMins | Integer | 立即单配送超时时间（分钟） |
| bookingBeforeEtaMins | Integer | 预约单配送超时时间（分钟） |

### 响应示例

```json
{
  "tenantId": 10001,
  "storeId": 20001,
  "deliveryPlatformConfig": [
    {
      "channelType": 1,
      "platformCode": 1001,
      "status": 1,
      "deliveryLaunchPoint": 2,
      "redirectUrl": "https://example.com/redirect",
      "deliveryLaunchDelayMinutes": 5,
      "bookingOrderDeliveryLaunchMinutes": 30,
      "secondDeliveryConfig": {
        "secondPlatformCodeCode": [2001, 2002],
        "redirectUrl": "https://example.com/second-redirect",
        "forbiddenCondition": {
          "orderTags": [1, 2, 3],
          "orderActualPayment": "100.00"
        }
      },
      "selfDeliveryBookingDeliveryRule": {
        "needBusinessHoursPushdown": true,
        "bookingPushDownTimeConfig": [
          {
            "orderTags": [1, 2],
            "condition": [
              {
                "identifer": "distance",
                "interval": {
                  "values": ["0", "5000"],
                  "intervalType": 1
                }
              }
            ],
            "formula": "max(10, distance/100)"
          }
        ]
      }
    }
  ],
  "selfDeliveryConfig": {
    "assertTime": [
      {
        "type": 1,
        "hint": "配送时长考核",
        "timeConfigType": "1",
        "orderTags": [1, 2],
        "distanceConfigVOS": [
          {
            "condition": [
              {
                "identifer": "distance",
                "interval": {
                  "values": ["0", "3000"],
                  "intervalType": 1
                }
              }
            ],
            "formula": "distance/100 + 10"
          }
        ]
      }
    ],
    "selfDeliveryMode": 1,
    "internalNavigationMode": 2,
    "completedSortMode": 1,
    "riderTransRoles": [1, 2, 3],
    "deliveryCompleteMode": {
      "distanceReminder": 1,
      "deliveryCompleteConfig": {
        "operationMode": 1,
        "allExamplePicInfoList": [
          {
            "type": 1,
            "name": "商品照片",
            "picUrl": "https://example.com/pic1.jpg",
            "order": 1
          }
        ],
        "specialProductUploadPicConfig": [
          {
            "productType": "生鲜",
            "picTypeList": [1, 2],
            "isForceUploadPic": true,
            "needUploadPicCount": 2
          }
        ],
        "sendPicToCustomerTips": "请上传商品照片给顾客",
        "notContactCustomerTips": "请联系顾客确认送达",
        "uploadImageDurationThreshold": 60,
        "isShowNotContactCustomerTips": true
      }
    },
    "deliveryRemindConfig": {
      "receiveTimeOutMins": 5,
      "takenTimeOutMins": 10,
      "soonDeliveryTimeoutMinsBeforeEta": 5,
      "deliveryTimeOutsBeforeEtaConfig": {
        "immediateBeforeEtaMins": 15,
        "bookingBeforeEtaMins": 20
      }
    }
  }
}
```

## 2. 保存门店配置明细

### 接口描述
保存指定租户和门店的配送配置明细信息。

### 请求方式
- **URL**: `/dmp/api/web/config/save`
- **Method**: POST

### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| deliveryConfigDetailVO | DeliveryConfigDetailVO | 是 | 配送配置详情对象 |

#### deliveryConfigDetailVO 对象

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| tenantId | Long | 是 | 租户ID |
| storeId | Long | 是 | 门店ID |
| deliveryPlatformConfig | List<DeliveryPlatformConfigVO> | 否 | 配送平台配置列表 |
| selfDeliveryConfig | SelfDeliveryConfigVO | 否 | 自送配置 |

请求参数中的各个对象结构与查询接口的响应参数结构完全一致，详细字段说明请参考查询接口的响应参数部分。

### 请求示例

```json
{
  "deliveryConfigDetailVO": {
    "tenantId": 10001,
    "storeId": 20001,
    "deliveryPlatformConfig": [
      {
        "channelType": 1,
        "platformCode": 1001,
        "status": 1,
        "deliveryLaunchPoint": 2,
        "redirectUrl": "https://example.com/redirect",
        "deliveryLaunchDelayMinutes": 5,
        "bookingOrderDeliveryLaunchMinutes": 30,
        "secondDeliveryConfig": {
          "secondPlatformCodeCode": [2001, 2002],
          "redirectUrl": "https://example.com/second-redirect",
          "forbiddenCondition": {
            "orderTags": [1, 2, 3],
            "orderActualPayment": "100.00"
          }
        },
        "selfDeliveryBookingDeliveryRule": {
          "needBusinessHoursPushdown": true,
          "bookingPushDownTimeConfig": [
            {
              "orderTags": [1, 2],
              "condition": [
                {
                  "identifer": "distance",
                  "interval": {
                    "values": ["0", "5000"],
                    "intervalType": 1
                  }
                }
              ],
              "formula": "max(10, distance/100)"
            }
          ]
        }
      }
    ],
    "selfDeliveryConfig": {
      "assertTime": [
        {
          "type": 1,
          "hint": "配送时长考核",
          "timeConfigType": "1",
          "orderTags": [1, 2],
          "distanceConfigVOS": [
            {
              "condition": [
                {
                  "identifer": "distance",
                  "interval": {
                    "values": ["0", "3000"],
                    "intervalType": 1
                  }
                }
              ],
              "formula": "distance/100 + 10"
            }
          ]
        }
      ],
      "selfDeliveryMode": 1,
      "internalNavigationMode": 2,
      "completedSortMode": 1,
      "riderTransRoles": [1, 2, 3],
      "deliveryCompleteMode": {
        "distanceReminder": 1,
        "deliveryCompleteConfig": {
          "operationMode": 1,
          "allExamplePicInfoList": [
            {
              "type": 1,
              "name": "商品照片",
              "picUrl": "https://example.com/pic1.jpg",
              "order": 1
            }
          ],
          "specialProductUploadPicConfig": [
            {
              "productType": "生鲜",
              "picTypeList": [1, 2],
              "isForceUploadPic": true,
              "needUploadPicCount": 2
            }
          ],
          "sendPicToCustomerTips": "请上传商品照片给顾客",
          "notContactCustomerTips": "请联系顾客确认送达",
          "uploadImageDurationThreshold": 60,
          "isShowNotContactCustomerTips": true
        }
      },
      "deliveryRemindConfig": {
        "receiveTimeOutMins": 5,
        "takenTimeOutMins": 10,
        "soonDeliveryTimeoutMinsBeforeEta": 5,
        "deliveryTimeOutsBeforeEtaConfig": {
          "immediateBeforeEtaMins": 15,
          "bookingBeforeEtaMins": 20
        }
      }
    }
  }
}
```

### 响应参数
无响应参数，接口调用成功时返回空内容，状态码为200。

## 3. 字段说明与UI对应关系

根据UI界面，各字段的含义和对应关系如下：

### 配送时长考核配置 (assertTime)

| 字段名 | UI显示名称 | 描述 |
|-------|-----------|------|
| type | 考核类型 | 配送时长考核的类型 |
| hint | 提示信息 | 在UI中显示的提示文字 |
| timeConfigType | 时长设置类型 | 1-固定时长，2-eta+-分钟 |
| orderTags | 订单标签 | 适用的订单标签列表 |
| distanceConfigVOS | 距离配置 | 基于距离的时长配置规则 |

### 自送拣配作业模式 (selfDeliveryMode)

| 值 | UI显示名称 | 描述 |
|---|-----------|------|
| 1 | 拣货模式 | 按拣货模式进行作业 |
| 2 | 配送模式 | 按配送模式进行作业 |

### 内部导航模式 (internalNavigationMode)

| 值 | UI显示名称 | 描述 |
|---|-----------|------|
| 1 | 开启 | 开启内部导航 |
| 2 | 关闭 | 关闭内部导航 |

### 已完成排序模式 (completedSortMode)

| 值 | UI显示名称 | 描述 |
|---|-----------|------|
| 1 | 按完成时间排序 | 按订单完成时间排序 |
| 2 | 按距离排序 | 按配送距离排序 |

### 确认送达操作配置 (deliveryCompleteMode)

| 字段名 | UI显示名称 | 描述 |
|-------|-----------|------|
| distanceReminder | 距离提醒 | 0-不提示，1-提示距离信息 |
| deliveryCompleteConfig | 配送完成配置 | 送达确认的详细配置 |

### 配送提醒配置 (deliveryRemindConfig)

| 字段名 | UI显示名称 | 描述 |
|-------|-----------|------|
| receiveTimeOutMins | 领取超时提醒 | 领取超时时间（分钟），0为未开启 |
| takenTimeOutMins | 取货超时提醒 | 取货超时时间（分钟），0为未开启 |
| soonDeliveryTimeoutMinsBeforeEta | 即将超时提醒 | 即将超时时间（分钟），0为未开启 |
| deliveryTimeOutsBeforeEtaConfig | 配送超时配置 | 配送超时的详细配置 |

### 配送平台配置 (deliveryPlatformConfig)

| 字段名 | UI显示名称 | 描述 |
|-------|-----------|------|
| channelType | 渠道类型 | 配送渠道的类型 |
| platformCode | 平台编码 | 配送平台的编码 |
| status | 状态 | 0-禁用，1-启用 |
| deliveryLaunchPoint | 配送发起点 | 配送任务的发起点 |
| redirectUrl | 跳转链接 | 平台跳转的URL地址 |
| deliveryLaunchDelayMinutes | 配送发起延迟 | 配送发起延迟分钟数 |
| bookingOrderDeliveryLaunchMinutes | 预约单配送发起 | 预约单配送发起分钟数 |

## 3. 查询配送支持的订单标签

### 接口描述
查询配送系统支持的订单标签映射关系，用于获取不同场景下支持的订单标签列表。

### 请求方式
- **URL**: `/dmp/api/web/config/deliverySupportOrderTags`
- **Method**: POST

### 请求参数
无请求参数

### 请求示例
```http
POST /dmp/api/web/config/deliverySupportOrderTags
```

### 响应参数

| 参数名 | 类型 | 描述 |
|-------|------|------|
| sceneOrderTagMap | Map<Integer, List<Integer>> | 场景订单标签映射关系 |

#### sceneOrderTagMap 对象说明

| Key值 | 场景名称   | 描述               |
|------|--------|------------------|
| 1    | 转辅配方式  | 转为辅助配送方式时支持的订单标签 |
| 2    | 自动呼叫时间 | 自动呼叫功能支持的订单标签    |
| 3    | 剩余时长   | 剩余时长计算支持的订单标签    |
| 4    | 确认送达   | 确认送达时的订单标签       |

Value为对应场景下支持的订单标签ID列表。
目前枚举:

| value  | 场景名称 |
|--------|------|
| 2010   | 餐馆   |
| 2008   | 高价值  |
| 900001 | 封签   |

### 响应示例

```json
{
  "sceneOrderTagMap": {
    "1": [101, 102, 103, 104],
    "2": [201, 202, 203],
    "3": [301, 302, 303, 304, 305]
  }
}
```

### 字段说明

- **sceneOrderTagMap**: 场景与订单标签的映射关系
  - Key: 场景ID（Integer类型）
    - 1: 转辅配方式场景
    - 2: 自动呼叫时间场景
    - 3: 剩余时长场景
  - Value: 该场景下支持的订单标签ID列表（List<Integer>类型）

### 使用说明

1. 该接口主要用于前端页面获取配送配置时的订单标签选项
2. 不同场景下支持的订单标签可能不同，前端需要根据场景ID获取对应的标签列表
3. 订单标签的具体含义和显示名称需要结合业务系统的标签字典进行解析
4. 配置数据来源于Lion配置中心，支持动态更新