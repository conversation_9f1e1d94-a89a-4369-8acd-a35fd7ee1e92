package com.sankuai.shangou.logistics.sdms.application.config.aviator;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorNil;
import com.googlecode.aviator.runtime.type.AviatorObject;

import java.util.Map;

/**
 * args:
 * condition 条件
 * formula 条件满足后执行的公式
 */
public class CaseThenFunction extends AbstractFunction {
    public static final String FUNCTION_NAME = "F_CASE_THEN";

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject condition, AviatorObject formula) {
        boolean conditionResult = FunctionUtils.getBooleanValue(condition, env);
        if (conditionResult) {
            Number actionResult = FunctionUtils.getNumberValue(formula, env);
            return new AviatorDecimal(actionResult);
        }

        return AviatorNil.NIL;
    }

    @Override
    public String getName() {
        return FUNCTION_NAME;
    }
}
