package com.sankuai.shangou.logistics.sdms.application.config;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.utils.AddressSceneConvertUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import com.sankuai.shangou.logistics.sdms.application.config.aviator.CaseThenFunction;
import com.sankuai.shangou.logistics.sdms.application.config.aviator.ExpressionCalcPeon;
import com.sankuai.shangou.logistics.sdms.application.config.aviator.SwitchGroupFunction;
import com.sankuai.shangou.logistics.sdms.application.config.data.ExpressionSourceDataEnum;
import com.sankuai.shangou.logistics.sdms.application.utils.JsonUtil;
import com.sankuai.shangou.logistics.sdms.application.utils.LionConfigUtils;
import com.sankuai.shangou.logistics.sdms.dao.mapper.AssessDeliveryTimeConfigMapper;
import com.sankuai.shangou.logistics.sdms.dao.mapper.SdmsStoreConfigMapper;
import com.sankuai.shangou.logistics.sdms.dao.model.AssessDeliveryTimeConfig;
import com.sankuai.shangou.logistics.sdms.dao.model.AssessDeliveryTimeConfigExample;
import com.sankuai.shangou.logistics.sdms.dao.model.SdmsStoreConfig;
import com.sankuai.shangou.logistics.sdms.dao.model.SdmsStoreConfigExample;
import com.sankuai.shangou.logistics.sdms.domain.constants.CalcUnitEnum;
import com.sankuai.shangou.logistics.sdms.domain.entity.config.expression.ExpressionCondition;
import com.sankuai.shangou.logistics.sdms.domain.entity.config.expression.ExpressionNode;
import com.sankuai.shangou.logistics.sdms.domain.entity.config.expression.excutable.ExecutableExpressionNode;
import com.sankuai.shangou.logistics.sdms.domain.entity.config.expression.interval.Interval;
import com.sankuai.shangou.logistics.sdms.domain.entity.config.expression.interval.IntervalNumber;
import com.sankuai.shangou.logistics.sdms.domain.entity.config.expression.interval.IntervalTypeEnum;
import com.sankuai.shangou.logistics.sdms.domain.utils.IListUtils;
import com.sankuai.shangou.logistics.sdms.domain.utils.TimeUtils;
import com.sankuai.shangou.logistics.sdms.infrastructure.external.OfcOrderSearchClient;
import com.sankuai.shangou.logistics.sdms.infrastructure.external.TradeOrderClientImpl;
import com.sankuai.shangou.logistics.sdms.infrastructure.external.WarehouseQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-07-09
 * @email <EMAIL>
 */
@Slf4j
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class SdmsStoreConfigApplicationService {

    @Resource
    private SdmsStoreConfigMapper sdmsStoreConfigMapper;
    @Resource
    private AssessDeliveryTimeConfigMapper assessDeliveryTimeConfigMapper;
    @Resource
    private WarehouseQueryWrapper warehouseQueryWrapper;
    @Resource
    private TradeOrderClientImpl tradeOrderClient;
    @Resource
    private OfcOrderSearchClient ofcOrderSearchClient;

    private static final int BOOKING_ORDER = 1;
    private static final String TRUE_CONDITION = "true";
    private static final Set<IntervalTypeEnum> ONE_VALUE_TYPE_SET = Sets.newHashSet(IntervalTypeEnum.EQUAL, IntervalTypeEnum.NOT_EQUAL);


    @Deprecated
    public Integer calcAssessDeliveryDuration(Long merchantId, long warehouseId, String tradeOrderNo, int orderBizType, long deliveryDistance,
                                              @Nullable BizOrderModel bizOrderModel, @Nullable List<Pair<SdmsStoreConfig, List<AssessDeliveryTimeConfig>>> configList) {
        if (Objects.isNull(configList)) {
            configList = queryValidConfigContent(merchantId, warehouseId, CalcUnitEnum.MINUS);
        }
        if (CollectionUtils.isEmpty(configList)) {
            return -1;
        }
        // FIXME: 13.7.24 这里其实还差一步提取formula需要用到哪些source data，排期问题先不实现
        if (Objects.isNull(bizOrderModel)) {
            bizOrderModel = tradeOrderClient.queryTradeOrder(merchantId, warehouseId, tradeOrderNo, orderBizType);
        }
        Map payload = new HashMap();
        payload.put(replaceSymbol(ExpressionSourceDataEnum.DELIVERY_DISTANCE.getDataIdentifier()), deliveryDistance);
        for (ExpressionSourceDataEnum value : ExpressionSourceDataEnum.values()) {
            switch (value) {
                case ETA: {
                    long etaDurationMill = bizOrderModel.getDeliveryModel().getArrivalTime() - bizOrderModel.getPayTime();
                    long etaDuration = etaDurationMill / 1000 / 60;
                    payload.put(replaceSymbol(value.getDataIdentifier()), etaDuration);
                    break;
                }
                case RESTAURANT_SCENE: {
                    payload.put(replaceSymbol(value.getDataIdentifier()), isRestaurantScene(bizOrderModel.getDeliveryModel().getCategory()) ? 1 : 0);
                    break;
                }
                case ORDER_RESERVE_TYPE: {
                    payload.put(replaceSymbol(value.getDataIdentifier()), bizOrderModel.getIsBooking());
                    break;
                }
                case CONTAIN_SEAL_PRODUCT: {
                    boolean containSealProduct = ofcOrderSearchClient.queryOrderIsContainSealProduct(merchantId, warehouseId, tradeOrderNo, orderBizType);
                    payload.put(replaceSymbol(value.getDataIdentifier()), containSealProduct ? 1 : 0);
                    break;
                }
                default:
                    continue;
            }
        }
        log.info("payload = {}", payload);
        for (Pair<SdmsStoreConfig, List<AssessDeliveryTimeConfig>> pair : configList) {
            for (AssessDeliveryTimeConfig assessDeliveryTimeConfig : pair.getValue()) {
                String configJson = assessDeliveryTimeConfig.getConfigJson();
                //表达式树，用作展示
                ExpressionNode rootExpressionTree = JsonUtil.fromJson(configJson, ExpressionNode.class);
                //生成执行树，用作后续编译为avitar语句。
                ExecutableExpressionNode executableExpressionTree = toExecutableExpressionTree(rootExpressionTree);
                log.info("executableExpressionTree = {}", executableExpressionTree);
                //生成表达式
                String formula = compile(executableExpressionTree);
                log.info("formula = {}", formula);
                //aviatar执行
                ExpressionCalcPeon expressionCalcPeon = new ExpressionCalcPeon();
                BigDecimal result = expressionCalcPeon.execute(replaceSymbol(formula), payload);
                if (result.compareTo(BigDecimal.ZERO) > 0) {
                    return result.intValue();
                }
            }
        }


        return -1;
    }

    public Integer calcAssessDeliveryDurationBySeconds(Long merchantId, long warehouseId, String tradeOrderNo, int orderBizType, long deliveryDistance,
                                              @Nullable BizOrderModel bizOrderModel, @Nullable List<Pair<SdmsStoreConfig, List<AssessDeliveryTimeConfig>>> configList) {

        if (Objects.isNull(configList)) {
            configList = queryValidConfigContent(merchantId, warehouseId, CalcUnitEnum.SECONDS);
        }
        if (CollectionUtils.isEmpty(configList)) {
            return -1;
        }
        // FIXME: 13.7.24 这里其实还差一步提取formula需要用到哪些source data，排期问题先不实现
        if (Objects.isNull(bizOrderModel)) {
            bizOrderModel = tradeOrderClient.queryTradeOrder(merchantId, warehouseId, tradeOrderNo, orderBizType);
        }
        Map payload = new HashMap();
        payload.put(replaceSymbol(ExpressionSourceDataEnum.DELIVERY_DISTANCE.getDataIdentifier()), deliveryDistance);
        for (ExpressionSourceDataEnum value : ExpressionSourceDataEnum.values()) {
            switch (value) {
                case ETA: {
                    long etaDurationMill = bizOrderModel.getDeliveryModel().getArrivalTime() - bizOrderModel.getPayTime();
                    long etaDurationSeconds = etaDurationMill / 1000;
                    payload.put(replaceSymbol(value.getDataIdentifier()), etaDurationSeconds);
                    break;
                }
                case RESTAURANT_SCENE: {
                    payload.put(replaceSymbol(value.getDataIdentifier()), isRestaurantScene(bizOrderModel.getDeliveryModel().getCategory()) ? 1 : 0);
                    break;
                }
                case ORDER_RESERVE_TYPE: {
                    payload.put(replaceSymbol(value.getDataIdentifier()), bizOrderModel.getIsBooking());
                    break;
                }
                case CONTAIN_SEAL_PRODUCT: {
                    boolean containSealProduct = ofcOrderSearchClient.queryOrderIsContainSealProduct(merchantId, warehouseId, tradeOrderNo, orderBizType);
                    payload.put(replaceSymbol(value.getDataIdentifier()), containSealProduct ? 1 : 0);
                    break;
                }
                default:
                    continue;
            }
        }
        log.info("payload = {}", payload);
        for (Pair<SdmsStoreConfig, List<AssessDeliveryTimeConfig>> pair : configList) {
            for (AssessDeliveryTimeConfig assessDeliveryTimeConfig : pair.getValue()) {
                String configJson = assessDeliveryTimeConfig.getConfigJson();
                //表达式树，用作展示
                ExpressionNode rootExpressionTree = JsonUtil.fromJson(configJson, ExpressionNode.class);
                //生成执行树，用作后续编译为avitar语句。
                ExecutableExpressionNode executableExpressionTree = toExecutableExpressionTree(rootExpressionTree);
                log.info("executableExpressionTree = {}", executableExpressionTree);
                //生成表达式
                String formula = compile(executableExpressionTree);
                log.info("formula = {}", formula);
                //aviatar执行
                ExpressionCalcPeon expressionCalcPeon = new ExpressionCalcPeon();
                BigDecimal result = expressionCalcPeon.execute(replaceSymbol(formula), payload);
                if (result.compareTo(BigDecimal.ZERO) > 0) {
                    return result.intValue();
                }
            }
        }


        return -1;
    }


    public Long calcPushDownTimestamp(Long merchantId, long warehouseId, String tradeOrderNo, int orderBizType, long deliveryDistance) {
        List<Pair<SdmsStoreConfig, List<AssessDeliveryTimeConfig>>> configList = queryValidConfigContent(merchantId, warehouseId, CalcUnitEnum.SECONDS);
        if (CollectionUtils.isEmpty(configList)) {
            return -1L;
        }
        BizOrderModel bizOrderModel = tradeOrderClient.queryTradeOrder(merchantId, warehouseId, tradeOrderNo, orderBizType);
        if (!Objects.equals(bizOrderModel.getIsBooking(), BOOKING_ORDER)) {
            return -1L;
        }
        Long eta = bizOrderModel.getDeliveryModel().getArrivalTime();
        LocalTime earliestOpeningTime = getEarliestOpeningHours(merchantId, warehouseId, bizOrderModel.getOrderSource());
        LocalTime etaTime = TimeUtils.fromMilliSeconds(eta).toLocalTime();
        Duration duration = Duration.between(earliestOpeningTime, etaTime);
        //eta和开业时间的差值小于0则肯定跨天了，不计入规则,即 duration>0&& eta <= 最早营业时间+40分钟,则提前40分钟下发
        if (duration.getSeconds() > 0 && etaTime.isBefore(earliestOpeningTime.plusMinutes(LionConfigUtils.getPushDownOpeningTimeMin()))) {
            return eta - LionConfigUtils.getPushDownOpeningTimeMin() * 60 * 1000L;
        } else {
            Integer calcDuration = calcAssessDeliveryDuration(merchantId, warehouseId, tradeOrderNo, orderBizType, deliveryDistance, bizOrderModel, configList);
            if (calcDuration < 0) {
                return -1L;
            }
            return eta - calcDuration * 60 * 1000L;
        }
    }

    private LocalTime getEarliestOpeningHours(Long merchantId, long warehouseId, int channelId) {
        ChannelPoiInfoDTO channelPoiInfoDTO = warehouseQueryWrapper.queryChannelPoiInfoDTO(merchantId, warehouseId, channelId);
        List<String> shippingTimes = channelPoiInfoDTO.getShippingTime();
        if (CollectionUtils.isEmpty(shippingTimes)) {
            throw new ThirdPartyException("没有营业时间！");
        }
        //这里逻辑是和产品业务定好了一个"参考时间"，从参考时间往后找最近的
        List<LocalTime> openTimes = Lists.newArrayList();
        for (String shippingTime : shippingTimes) {
            String[] split = shippingTime.split("-");
            openTimes.add(TimeUtils.fromFormat(split[0]));
        }
        String referenceOpenTimeStr = LionConfigUtils.getReferenceOpenTime();
        LocalTime referenceOpenTime = TimeUtils.fromFormat(referenceOpenTimeStr);

        LocalTime localTime = openTimes.get(0);
        long minDiffSeconds = Long.MAX_VALUE;
        for (LocalTime openTime : openTimes) {
            Duration duration = Duration.between(referenceOpenTime, openTime);
            long diffSeconds = duration.getSeconds();
            if (diffSeconds > 0 && diffSeconds < minDiffSeconds) {
                localTime = openTime;
            }
        }

        return localTime;
    }

    private String replaceSymbol(String source) {
        if (source.contains("{")) {
            source = source.replace("{", "__");
        }
        if (source.contains("}")) {
            source = source.replace("}", "__");
        }
        return source;
    }

    private Boolean isRestaurantScene(String category) {
        try {
            if (StringUtils.isBlank(category)) {
                return false;
            }
            String scene = AddressSceneConvertUtils.convertCategory2Scene(category);
            if (Objects.isNull(scene)) {
                return false;
            }
            return LionConfigUtils.getDhRestaurantSceneList().contains(scene);
        } catch (Exception e) {
            log.error("isRestaurantScene error", e);
            return false;
        }
    }

    private static String compile(ExecutableExpressionNode tree) {
        if (tree == null) {
            throw new IllegalArgumentException("空树");
        }

        if (!tree.hasSubs()) {
            // 没有子节点
            return packageCaseThenFunction(tree.getCondition(), tree.getFormula());
        }

        // 1. 深度遍历到叶子节点 => caseThen
        List<String> pathList = new ArrayList<>();
        dfs(pathList, tree, new ArrayList<>());
        // 2. switchCase(所有caseThen)
        if (pathList.size() == 0) {
            throw new IllegalArgumentException("合法条件路径为空");
        }
        String compositeCaseThen = String.join(",", pathList);
        String switchGroupFormula = String.format("%s(%s)", SwitchGroupFunction.FUNCTION_NAME, compositeCaseThen);
        // 3. 如果表达式中有集合算子，如SWITCH_ACCUMULATE，转换表达式
        return switchGroupFormula;
    }

    private static String packageCaseThenFunction(String condition, String formula) {
        if (StringUtils.isBlank(condition)) {
            condition = "true";
        }
        if (StringUtils.isBlank(formula)) {
            throw new IllegalArgumentException("公式不能为空");
        }

        return String.format("%s(%s,%s)", CaseThenFunction.FUNCTION_NAME, condition, formula);
    }


    private static void dfs(List<String> pathList, ExecutableExpressionNode tree, List<String> conditionList) {
        if (tree == null) {
            return;
        }
        if (StringUtils.isNotBlank(tree.getCondition())) {
            conditionList.add(tree.getCondition());
        }

        if (!tree.hasSubs()) {
            // 叶子节点
            if (conditionList.size() > 0) {
                String compositeCondition = String.join(" && ", conditionList);
                String formula = tree.getFormula();
                pathList.add(packageCaseThenFunction(compositeCondition, formula));
            }
            return;
        }

        for (ExecutableExpressionNode node : tree.getSubs()) {
            List<String> newConditionList = new ArrayList<>(conditionList);
            dfs(pathList, node, newConditionList);
        }
    }

    private ExecutableExpressionNode toExecutableExpressionTree(ExpressionNode source) {

        if (Objects.isNull(source)) {
            throw new IllegalArgumentException("不正确的表达式树");
        }

        ExecutableExpressionNode newNode = new ExecutableExpressionNode();
        newNode.setCondition(buildCondition(source.getCondition()));
        if (CollectionUtils.isEmpty(source.getSubs())) {
            // leaf node
            newNode.setFormula(source.getFormula());
        }

        if (CollectionUtils.isNotEmpty(source.getSubs())) {
            List<ExecutableExpressionNode> newNodes = new ArrayList<>();
            for (ExpressionNode node : source.getSubs()) {
                newNodes.add(toExecutableExpressionTree(node));
            }
            newNode.setSubs(newNodes);
        }
        return newNode;
    }

    public static String buildCondition(ExpressionCondition expressionCondition) {
        if (Objects.isNull(expressionCondition)) {
            return TRUE_CONDITION;
        }

        Interval interval = expressionCondition.getInterval();
        List<IntervalNumber> values = interval.getValues();
        String conditionFormula = expressionCondition.getFormula();

        if (ONE_VALUE_TYPE_SET.contains(interval.getIntervalType())) {
            if (CollectionUtils.isEmpty(values) || values.size() != 1) {
                // 错误条件
                throw new SystemException(String.format("error condition: %s", JsonUtil.toJson(expressionCondition)));
            }
            IntervalNumber firstValue = values.get(0);
            switch (interval.getIntervalType()) {
                case EQUAL:
                    return String.format("%s == %s", conditionFormula, firstValue.getValue());
                case NOT_EQUAL:
                    return String.format("%s != %s", conditionFormula, firstValue.getValue());
                default:
                    // 暂不支持
                    throw new SystemException("don't support this interval: " + interval.getIntervalType());
            }
        }

        if (CollectionUtils.isEmpty(values) || values.size() != 2) {
            // 错误条件
            throw new SystemException(String.format("error condition: %s", JsonUtil.toJson(expressionCondition)));
        }
        IntervalNumber firstValue = values.get(0);
        IntervalNumber secondValue = values.get(1);

        switch (interval.getIntervalType()) {
            case EQUAL:
                return String.format("%s == %s", conditionFormula, firstValue.getValue());
            case NOT_EQUAL:
                return String.format("%s != %s", conditionFormula, firstValue.getValue());
            case LEFT_OPEN: // (,]
                if (secondValue.isPositiveInfinity() || firstValue.isPositiveInfinity()) {
                    // 错误条件
                    throw new SystemException(String.format("error condition: %s", JsonUtil.toJson(expressionCondition)));
                }
                if (firstValue.isNegativeInfinity()) {
                    return String.format("%s <= %s", conditionFormula, secondValue.getValue());
                }
                return String.format("%s < %s && %s <= %s", firstValue.getValue(), conditionFormula, conditionFormula,
                        secondValue.getValue());
            case RIGHT_OPEN: // [.)
                if (firstValue.isNegativeInfinity() || secondValue.isNegativeInfinity()) {
                    // 错误条件
                    throw new SystemException(String.format("error condition: %s", JsonUtil.toJson(expressionCondition)));
                }
                if (secondValue.isPositiveInfinity()) {
                    return String.format("%s >= %s", conditionFormula, firstValue.getValue());
                }
                return String.format("%s <= %s && %s < %s", firstValue.getValue(), conditionFormula, conditionFormula,
                        secondValue.getValue());

            case ALL_OPEN: // (,)
                if (firstValue.isPositiveInfinity() || secondValue.isNegativeInfinity()) {
                    // 错误条件
                    throw new SystemException(String.format("error condition: %s", JsonUtil.toJson(expressionCondition)));
                }
                if (firstValue.isNegativeInfinity() && secondValue.isPositiveInfinity()) {
                    return "true";
                }
                if (firstValue.isNegativeInfinity()) {
                    return String.format("%s < %s", conditionFormula, secondValue.getValue());
                }
                if (secondValue.isPositiveInfinity()) {
                    return String.format("%s > %s", conditionFormula, firstValue.getValue());
                }

                return String.format("%s < %s && %s < %s", firstValue.getValue(), conditionFormula, conditionFormula,
                        secondValue.getValue());
            case ALL_CLOSE: // [,]
                if (firstValue.isNegativeInfinity() || firstValue.isPositiveInfinity() || secondValue.isNegativeInfinity()
                        || secondValue.isPositiveInfinity()) {
                    // 错误条件
                    throw new SystemException(String.format("error condition: %s", JsonUtil.toJson(expressionCondition)));
                }
                return String.format("%s <= %s && %s <= %s", firstValue.getValue(), conditionFormula, conditionFormula,
                        secondValue.getValue());

            default:
                // 暂不支持
                throw new SystemException("don't support this interval: " + interval.getIntervalType());
        }
    }


    private List<Pair<SdmsStoreConfig, List<AssessDeliveryTimeConfig>>> queryValidConfigContent(long merchantId, long warehouseId, CalcUnitEnum calcUnitEnum) {

        //1.查门店详情
        WarehouseDTO warehouseDTO = warehouseQueryWrapper.queryWarehouse(merchantId, warehouseId);

        //2.查配置
        SdmsStoreConfigExample storeConfigExample = new SdmsStoreConfigExample();
        //criteria1默认是加入的
        SdmsStoreConfigExample.Criteria criteria1 = storeConfigExample.createCriteria().andStoreOperationModeEqualTo(warehouseDTO.getOperationMode());
        SdmsStoreConfigExample.Criteria criteria2 = storeConfigExample.createCriteria().andCityIdEqualTo(Long.valueOf(warehouseDTO.getRegion().getCityId()));
        SdmsStoreConfigExample.Criteria criteria3 = storeConfigExample.createCriteria().andStoreIdEqualTo(warehouseId);
        storeConfigExample.or(criteria2);
        storeConfigExample.or(criteria3);
        List<SdmsStoreConfig> sdmsStoreConfigs = sdmsStoreConfigMapper.selectByExample(storeConfigExample);
        if (CollectionUtils.isEmpty(sdmsStoreConfigs)) {
            return Lists.newArrayList();
        }
        Map<Long, SdmsStoreConfig> storeConfigIdMap = sdmsStoreConfigs.stream()
                .collect(Collectors.toMap(
                        SdmsStoreConfig::getId,
                        Function.identity()
                ));


        AssessDeliveryTimeConfigExample timeConfigExample = new AssessDeliveryTimeConfigExample();
        timeConfigExample.createCriteria().andBelongStoreConfigIdIn(IListUtils.mapTo(sdmsStoreConfigs, SdmsStoreConfig::getId))
                .andIsDeletedEqualTo(0);
        List<AssessDeliveryTimeConfig> assessDeliveryTimeConfigs = assessDeliveryTimeConfigMapper.selectByExample(timeConfigExample);
        //按计算时间过滤
        assessDeliveryTimeConfigs = IListUtils.nullSafeFilterElement(assessDeliveryTimeConfigs, assessDeliveryTimeConfig -> Objects.equals(assessDeliveryTimeConfig.getCalcUnit(), calcUnitEnum.getCode()));

        Map<Long, List<AssessDeliveryTimeConfig>> configIdAndTimeConfigMap = IListUtils.nullSafeGroupBy(assessDeliveryTimeConfigs, AssessDeliveryTimeConfig::getBelongStoreConfigId);
        Map<SdmsStoreConfig, List<AssessDeliveryTimeConfig>> map = configIdAndTimeConfigMap.entrySet()
                .stream()
                .filter(entry -> storeConfigIdMap.containsKey(entry.getKey()))
                .collect(Collectors.toMap(
                        it -> storeConfigIdMap.get(it.getKey()),
                        Map.Entry::getValue,
                        (older, newer) -> newer
                ));
        //排序逻辑：门店>城市>直营
        List<Pair<SdmsStoreConfig, List<AssessDeliveryTimeConfig>>> resultList = Lists.newArrayList(null, null, null);
        for (Map.Entry<SdmsStoreConfig, List<AssessDeliveryTimeConfig>> entry : map.entrySet()) {
            if (Objects.equals(entry.getKey().getStoreId(), warehouseId)) {
                resultList.add(0, Pair.of(entry.getKey(), entry.getValue()));
            }
            if (Objects.equals(entry.getKey().getCityId(), Long.valueOf(warehouseDTO.getRegion().getCityId()))) {
                resultList.add(1, Pair.of(entry.getKey(), entry.getValue()));
            }
            if (Objects.equals(entry.getKey().getStoreOperationMode(), warehouseDTO.getOperationMode())) {
                resultList.add(2, Pair.of(entry.getKey(), entry.getValue()));
            }
        }
        resultList.removeIf(item -> Objects.equals(item, null));

        return resultList;
    }

}
