package com.sankuai.shangou.logistics.sdms.application.config.aviator;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class ExpressionCalcPeon {
    private static AviatorEvaluatorInstance instance;
    private static String DIVISION_BY_ZERO_ERROR = "Division by zero";

    public ExpressionCalcPeon() {
        if (Objects.isNull(instance)) {
            synchronized (ExpressionCalcPeon.class) {
                if (Objects.isNull(instance)) {
                    instance = AviatorEvaluator.newInstance();
                    instance.setCachedExpressionByDefault(true);
                    // 初始化所有自定义函数 addFunction
                    instance.addFunction(new SwitchGroupFunction());
                    instance.addFunction(new CaseThenFunction());
                }
            }
        }
    }

    public BigDecimal execute(String expression, Map payload) {
        Object result;
        try {
            log.info("ExpressionCalcPeon.execute, expression = {}, payload = {}", expression, payload);
            result = instance.execute(expression, payload, true);
        } catch (NumberFormatException e) {
            throw new SystemException("数据异常");
        } catch (NullPointerException e) {
            throw new IllegalArgumentException("有空输入");
        } catch (ArithmeticException e) {
            // 当公式中有分母为0等值错误时
            log.warn("division error");
            return new BigDecimal(0);
        }

        if (result == null) {
            log.warn("null result");
            return new BigDecimal(0);
        }
        //this.instance.clearExpressionCache();
        return new BigDecimal(result.toString());
    }
}
