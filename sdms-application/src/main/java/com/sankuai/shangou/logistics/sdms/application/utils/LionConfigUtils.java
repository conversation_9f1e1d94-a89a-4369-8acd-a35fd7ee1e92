package com.sankuai.shangou.logistics.sdms.application.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.shangou.logistics.sdms.domain.utils.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.time.LocalDateTime;
import java.util.*;

import static com.dianping.lion.client.Lion.getConfigRepository;

/**
 * <AUTHOR>
 * @date 2024-07-13
 * @email <EMAIL>
 */
@Slf4j
public class LionConfigUtils {

    private LionConfigUtils() {

    }

    public static List<String> getDhRestaurantSceneList() {
        try {
            return getConfigRepository("com.sankuai.sgfulfillment.tms").getList("dh.restaurant.scene", String.class, Lists.newArrayList("餐馆"));
        } catch (Exception e) {
            log.error("isDhScenePoi error", e);
            return Lists.newArrayList();
        }
    }

    public static int getPushDownOpeningTimeMin() {
        return getConfigRepository().getIntValue("push.down.opening.min", 40);
    }

    public static String getReferenceOpenTime() {
        return getConfigRepository().get("reference.open.time", "09:00");
    }

    public static List<Long> getVerifyTaskPushPositionIds() {
        return Lion.getConfigRepository().getList("verify.task.push.position.ids", Long.class, Collections.emptyList());
    }

    public static String getVerifyManagementPageUrl(Long storeId, String accountName, LocalDateTime pushTime) {
        String url = getConfigRepository().get("qingting.verify.page.url", "https://qingting.waimai.meituan.com/home.html#/waimaM/delivery/yoda-collection-task");
        return String.format(url + "?accountName=%s&current=1&pageSize=20&storeIds=%s&taskPushTime=%s&taskPushTime=%s",
                accountName,
                storeId.toString(),
                TimeUtils.toMilliSeconds(pushTime.minusMinutes(1)).toString(),
                TimeUtils.toMilliSeconds(pushTime.plusMinutes(1)).toString());
    }

    public static String getYodaPageUrl() {
        return  getConfigRepository().get("yoda.page.url", "https://rc-yoda.sankuai.com/?service=#/logQuery");
    }

    public static List<String> getVerifyTaskPushWhiteList(Integer operationMode) {
        String pushWhiteListMapStr = getConfigRepository().get("verify.task.push.white.list.map");
        Map<Integer, List<String>> pushWhiteListMap = JSON.parseObject(pushWhiteListMapStr, new TypeReference<Map<Integer, List<String>>>() {
        });

        if (MapUtils.isEmpty(pushWhiteListMap)) {
            return Collections.emptyList();
        }

        return pushWhiteListMap.getOrDefault(operationMode, Collections.emptyList());
    }

    /**
     * 自配送融合v2降级开关 false 未降级 true 降级
     * @return
     */
    public static Boolean fusionSelfDegreeSwitchV2(){
        return Lion.getConfigRepository("com.sankuai.sgfulfillment.tms").getBooleanValue("fusion.self.degree.switch.v2", false);
    }

}
